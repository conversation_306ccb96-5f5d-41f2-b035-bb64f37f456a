import { I as IGameProcessor } from "./services-Ckq1alRq.js";
import { r as reactExports, j as jsxDevRuntimeExports } from "./vendor-react-BH-kks1U.js";
import { S as SystemContext } from "./context-CJb-Kg-5.js";
import { G as GameStartScreen } from "./game-association-1fe4bzjE.js";
import { a as useUnifiedGameLogic, b as useMultisensoryIntegration } from "./hooks-DiB_syzW.js";
import { u as useStandardTTS, S as StandardTTSButton, T as TTS_MESSAGES } from "./game-memory-Q6N7kS_-.js";
class NumericalCognitionCollector {
  constructor() {
    this.cognitionThresholds = {
      excellent: 0.95,
      good: 0.85,
      average: 0.7,
      poor: 0.5,
      critical: 0.3
    };
    this.numericalSkills = {
      counting: "Habilidade de contagem sequencial",
      subitizing: "Reconhecimento imediato de quantidades pequenas",
      numberSense: "Senso numérico e magnitude",
      cardinality: "Compreensão do princípio da cardinalidade",
      oneToOne: "Correspondência um-para-um"
    };
    this.difficultyRanges = {
      easy: { min: 1, max: 5, expectedTime: 3e3 },
      medium: { min: 3, max: 10, expectedTime: 5e3 },
      hard: { min: 5, max: 15, expectedTime: 8e3 }
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de cognição numérica
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Método padronizado de análise para integração com testes e processadores
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de cognição numérica
   */
  async analyze(data) {
    if (!data || !data.numberCounting) {
      console.warn("NumericalCognitionCollector: Dados inválidos recebidos", data);
      return {
        countingAccuracy: 0.7,
        numberSequencing: 0.7,
        subitizing: 0.7,
        numericalComprehension: 0.7,
        quantityComparison: 0.7,
        overallCognition: 0.7,
        recommendations: []
      };
    }
    try {
      const countingAccuracy = this.assessCountingAccuracy(data);
      const numberSequencing = this.assessNumberSequencing(data);
      const subitizing = this.assessSubitizing(data);
      const numericalComprehension = this.assessNumericalComprehension(data);
      const quantityComparison = this.assessQuantityComparison(data);
      const overallCognition = countingAccuracy * 0.25 + numberSequencing * 0.2 + subitizing * 0.15 + numericalComprehension * 0.25 + quantityComparison * 0.15;
      const recommendations = this.generateRecommendations({
        countingAccuracy,
        numberSequencing,
        subitizing,
        numericalComprehension,
        quantityComparison,
        overallCognition
      });
      return {
        countingAccuracy,
        numberSequencing,
        subitizing,
        numericalComprehension,
        quantityComparison,
        overallCognition,
        recommendations
      };
    } catch (error2) {
      console.error("Erro na análise de cognição numérica:", error2);
      return {
        countingAccuracy: 0.7,
        numberSequencing: 0.7,
        subitizing: 0.7,
        numericalComprehension: 0.7,
        quantityComparison: 0.7,
        overallCognition: 0.7,
        recommendations: [],
        error: error2.message
      };
    }
  }
  assessCountingAccuracy(data) {
    const attempts = data.attempts.filter((a) => a.type === "counting");
    if (attempts.length === 0) return 0.7;
    const correctAttempts = attempts.filter((a) => a.isCorrect);
    const accuracy = correctAttempts.length / attempts.length;
    const accuracyByRange = this.analyzeAccuracyByNumberRange(attempts);
    return Math.min(1, accuracy * (1 + accuracyByRange.consistency * 0.2));
  }
  assessSubitizing(data) {
    const subitizingAttempts = data.attempts.filter(
      (a) => a.correctAnswer <= 4 && a.responseTime < 2e3
    );
    if (subitizingAttempts.length === 0) return 0.6;
    const accuracy = subitizingAttempts.filter((a) => a.isCorrect).length / subitizingAttempts.length;
    const avgResponseTime = subitizingAttempts.reduce((sum, a) => sum + a.responseTime, 0) / subitizingAttempts.length;
    const speedFactor = Math.max(0, 1 - (avgResponseTime - 1e3) / 2e3);
    return Math.min(1, accuracy * (0.7 + speedFactor * 0.3));
  }
  assessNumberSense(data) {
    const attempts = data.attempts;
    if (attempts.length === 0) return 0.7;
    const estimationErrors = attempts.map((a) => Math.abs(a.userAnswer - a.correctAnswer));
    const avgError = estimationErrors.reduce((sum, e) => sum + e, 0) / estimationErrors.length;
    const systematicBias = this.detectSystematicBias(attempts);
    const magnitudeDiscrimination = this.assessMagnitudeDiscrimination(attempts);
    const baseScore = Math.max(0, 1 - avgError / 10);
    const biaspenalty = systematicBias.hasBias ? 0.2 : 0;
    return Math.min(1, (baseScore + magnitudeDiscrimination) / 2 - biaspenalty);
  }
  assessCardinality(data) {
    const countingSequences = data.countingSequences || [];
    if (countingSequences.length === 0) return 0.7;
    const correctCardinalityUse = countingSequences.filter(
      (seq) => seq.finalAnswer === seq.lastCountedNumber
    ).length;
    const cardinalityAccuracy = correctCardinalityUse / countingSequences.length;
    const consistency = this.measureCardinalityConsistency(countingSequences);
    return Math.min(1, cardinalityAccuracy * (0.8 + consistency * 0.2));
  }
  assessOneToOneCorrespondence(data) {
    const countingBehavior = data.countingBehavior || [];
    if (countingBehavior.length === 0) return 0.7;
    const correctCorrespondence = countingBehavior.filter(
      (behavior) => behavior.objectsPointed === behavior.numbersSpoken && behavior.noDoublePointing && behavior.noSkippedObjects
    ).length;
    return Math.min(1, correctCorrespondence / countingBehavior.length);
  }
  assessProcessingSpeed(data) {
    const attempts = data.attempts;
    if (attempts.length === 0) return 0.7;
    const difficultyGroups = this.groupByDifficulty(attempts);
    let totalSpeedScore = 0;
    let groupCount = 0;
    Object.keys(difficultyGroups).forEach((difficulty) => {
      const group = difficultyGroups[difficulty];
      const expectedTime = this.difficultyRanges[difficulty]?.expectedTime || 5e3;
      const avgTime = group.reduce((sum, a) => sum + a.responseTime, 0) / group.length;
      const speedScore = Math.max(0, 1 - (avgTime - expectedTime) / expectedTime);
      totalSpeedScore += speedScore;
      groupCount++;
    });
    return groupCount > 0 ? totalSpeedScore / groupCount : 0.7;
  }
  analyzeErrorPatterns(data) {
    const attempts = data.attempts;
    const errors = attempts.filter((a) => !a.isCorrect);
    if (errors.length === 0) {
      return {
        countingErrors: 0,
        systematicOvercount: 0,
        systematicUndercount: 0,
        skipErrors: 0,
        doubleCountErrors: 0,
        patternSeverity: "none"
      };
    }
    const patterns = {
      countingErrors: errors.filter((e) => e.errorType === "counting").length / errors.length,
      systematicOvercount: errors.filter((e) => e.userAnswer > e.correctAnswer).length / errors.length,
      systematicUndercount: errors.filter((e) => e.userAnswer < e.correctAnswer).length / errors.length,
      skipErrors: errors.filter((e) => e.errorType === "skip").length / errors.length,
      doubleCountErrors: errors.filter((e) => e.errorType === "doubleCount").length / errors.length
    };
    const maxPattern = Math.max(...Object.values(patterns));
    patterns.patternSeverity = maxPattern > 0.7 ? "high" : maxPattern > 0.4 ? "medium" : "low";
    return patterns;
  }
  assessCognitiveLoad(data) {
    const attempts = data.attempts;
    if (attempts.length === 0) return 0.7;
    const performanceOverTime = this.analyzePerformanceDecline(attempts);
    const timeComplexityRelation = this.analyzeTimeComplexityRelation(attempts);
    const consistencyScore = this.measurePerformanceConsistency(attempts);
    const cognitiveLoadScore = (1 - performanceOverTime.decline) * 0.4 + timeComplexityRelation.efficiency * 0.3 + consistencyScore * 0.3;
    return Math.min(1, Math.max(0, cognitiveLoadScore));
  }
  assessNumericalFluency(data) {
    const attempts = data.attempts;
    if (attempts.length === 0) return 0.7;
    const accuracyScore = attempts.filter((a) => a.isCorrect).length / attempts.length;
    const avgResponseTime = attempts.reduce((sum, a) => sum + a.responseTime, 0) / attempts.length;
    const speedScore = Math.max(0, 1 - (avgResponseTime - 2e3) / 5e3);
    const automaticResponses = attempts.filter(
      (a) => a.isCorrect && a.responseTime < 1500 && a.correctAnswer <= 5
    ).length;
    const automaticityBonus = automaticResponses / attempts.length * 0.2;
    return Math.min(1, accuracyScore * 0.6 + speedScore * 0.4 + automaticityBonus);
  }
  assessAdaptivePerformance(data) {
    const attempts = data.attempts;
    if (attempts.length < 5) return 0.7;
    const improvementRate = this.calculateLearningCurve(attempts);
    const difficultyAdaptation = this.analyzeDifficultyAdaptation(attempts);
    const errorRecovery = this.analyzeErrorRecovery(attempts);
    return Math.min(1, improvementRate * 0.4 + difficultyAdaptation * 0.3 + errorRecovery * 0.3);
  }
  // Métodos auxiliares
  analyzeAccuracyByNumberRange(attempts) {
    const ranges = {
      small: attempts.filter((a) => a.correctAnswer <= 3),
      medium: attempts.filter((a) => a.correctAnswer > 3 && a.correctAnswer <= 8),
      large: attempts.filter((a) => a.correctAnswer > 8)
    };
    const accuracies = {};
    Object.keys(ranges).forEach((range) => {
      if (ranges[range].length > 0) {
        accuracies[range] = ranges[range].filter((a) => a.isCorrect).length / ranges[range].length;
      }
    });
    const consistencyScore = this.calculateConsistency(Object.values(accuracies));
    return { accuracies, consistency: consistencyScore };
  }
  detectSystematicBias(attempts) {
    const errors = attempts.filter((a) => !a.isCorrect);
    if (errors.length === 0) return { hasBias: false, type: "none", magnitude: 0 };
    const overcounts = errors.filter((e) => e.userAnswer > e.correctAnswer).length;
    const undercounts = errors.filter((e) => e.userAnswer < e.correctAnswer).length;
    const overPercent = overcounts / errors.length;
    const underPercent = undercounts / errors.length;
    if (overPercent > 0.7) {
      return { hasBias: true, type: "overcount", magnitude: overPercent };
    } else if (underPercent > 0.7) {
      return { hasBias: true, type: "undercount", magnitude: underPercent };
    }
    return { hasBias: false, type: "none", magnitude: 0 };
  }
  assessMagnitudeDiscrimination(attempts) {
    const closeNumberPairs = attempts.filter((a) => {
      const otherAttempts = attempts.filter(
        (b) => Math.abs(b.correctAnswer - a.correctAnswer) <= 2 && b !== a
      );
      return otherAttempts.length > 0;
    });
    if (closeNumberPairs.length === 0) return 0.7;
    const correctDiscrimination = closeNumberPairs.filter((a) => a.isCorrect).length;
    return correctDiscrimination / closeNumberPairs.length;
  }
  groupByDifficulty(attempts) {
    return attempts.reduce((groups, attempt) => {
      const difficulty = attempt.difficulty || this.inferDifficulty(attempt.correctAnswer);
      if (!groups[difficulty]) groups[difficulty] = [];
      groups[difficulty].push(attempt);
      return groups;
    }, {});
  }
  inferDifficulty(number) {
    if (number <= 5) return "easy";
    if (number <= 10) return "medium";
    return "hard";
  }
  calculateConsistency(values) {
    if (values.length < 2) return 1;
    const mean = values.reduce((sum, v) => sum + v, 0) / values.length;
    const variance = values.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / values.length;
    return Math.max(0, 1 - variance);
  }
  analyzePerformanceDecline(attempts) {
    if (attempts.length < 5) return { decline: 0 };
    const firstHalf = attempts.slice(0, Math.floor(attempts.length / 2));
    const secondHalf = attempts.slice(Math.floor(attempts.length / 2));
    const firstAccuracy = firstHalf.filter((a) => a.isCorrect).length / firstHalf.length;
    const secondAccuracy = secondHalf.filter((a) => a.isCorrect).length / secondHalf.length;
    const decline = Math.max(0, firstAccuracy - secondAccuracy);
    return { decline, firstAccuracy, secondAccuracy };
  }
  getDefaultMetrics() {
    return {
      countingAccuracy: 0.7,
      subitizingAbility: 0.7,
      numberSenseCapacity: 0.7,
      cardinalityUnderstanding: 0.7,
      oneToOneCorrespondence: 0.7,
      processingSpeed: 0.7,
      errorPatterns: {
        countingErrors: 0.3,
        systematicOvercount: 0.2,
        systematicUndercount: 0.2,
        skipErrors: 0.1,
        doubleCountErrors: 0.1,
        patternSeverity: "low"
      },
      cognitiveLoad: 0.7,
      numericalFluency: 0.7,
      adaptivePerformance: 0.7
    };
  }
  measureCardinalityConsistency(sequences) {
    if (sequences.length < 3) return 1;
    const correctUses = sequences.filter(
      (seq) => seq.finalAnswer === seq.lastCountedNumber
    ).length;
    return correctUses / sequences.length;
  }
  analyzeTimeComplexityRelation(attempts) {
    const complexityGroups = this.groupByDifficulty(attempts);
    let efficiency = 0;
    let groupCount = 0;
    Object.keys(complexityGroups).forEach((difficulty) => {
      const group = complexityGroups[difficulty];
      const expectedComplexity = this.difficultyRanges[difficulty]?.expectedTime || 5e3;
      const actualComplexity = group.reduce((sum, a) => sum + a.responseTime, 0) / group.length;
      const groupEfficiency = Math.max(0, 1 - Math.abs(actualComplexity - expectedComplexity) / expectedComplexity);
      efficiency += groupEfficiency;
      groupCount++;
    });
    return { efficiency: groupCount > 0 ? efficiency / groupCount : 0.7 };
  }
  measurePerformanceConsistency(attempts) {
    if (attempts.length < 3) return 1;
    const accuracies = [];
    const windowSize = 3;
    for (let i = 0; i <= attempts.length - windowSize; i++) {
      const window = attempts.slice(i, i + windowSize);
      const windowAccuracy = window.filter((a) => a.isCorrect).length / window.length;
      accuracies.push(windowAccuracy);
    }
    return this.calculateConsistency(accuracies);
  }
  calculateLearningCurve(attempts) {
    if (attempts.length < 5) return 0.5;
    const segments = 5;
    const segmentSize = Math.floor(attempts.length / segments);
    const segmentAccuracies = [];
    for (let i = 0; i < segments; i++) {
      const start = i * segmentSize;
      const end = i === segments - 1 ? attempts.length : (i + 1) * segmentSize;
      const segment = attempts.slice(start, end);
      if (segment.length > 0) {
        const accuracy = segment.filter((a) => a.isCorrect).length / segment.length;
        segmentAccuracies.push(accuracy);
      }
    }
    const firstAccuracy = segmentAccuracies[0] || 0;
    const lastAccuracy = segmentAccuracies[segmentAccuracies.length - 1] || 0;
    return Math.max(0, Math.min(1, 0.5 + (lastAccuracy - firstAccuracy)));
  }
  analyzeDifficultyAdaptation(attempts) {
    const difficultyGroups = this.groupByDifficulty(attempts);
    const difficultyLevels = Object.keys(difficultyGroups).length;
    if (difficultyLevels < 2) return 0.7;
    let adaptationScore = 0;
    Object.keys(difficultyGroups).forEach((difficulty) => {
      const group = difficultyGroups[difficulty];
      const accuracy = group.filter((a) => a.isCorrect).length / group.length;
      const expectedAccuracy = difficulty === "easy" ? 0.9 : difficulty === "medium" ? 0.75 : 0.6;
      const adaptationForLevel = 1 - Math.abs(accuracy - expectedAccuracy);
      adaptationScore += adaptationForLevel;
    });
    return adaptationScore / difficultyLevels;
  }
  analyzeErrorRecovery(attempts) {
    const errors = attempts.map((attempt, index) => ({ ...attempt, index })).filter((a) => !a.isCorrect);
    if (errors.length === 0) return 1;
    let recoveryCount = 0;
    errors.forEach((error2) => {
      const nextAttempts = attempts.slice(error2.index + 1, error2.index + 4);
      const correctNext = nextAttempts.filter((a) => a.isCorrect).length;
      if (correctNext >= Math.min(2, nextAttempts.length)) {
        recoveryCount++;
      }
    });
    return recoveryCount / errors.length;
  }
}
class AttentionFocusCollector {
  constructor() {
    this.attentionMetrics = {
      sustained: "Atenção sustentada ao longo do tempo",
      selective: "Atenção seletiva para objetos relevantes",
      divided: "Capacidade de dividir atenção entre tarefas",
      focused: "Intensidade do foco em objetos específicos"
    };
    this.focusThresholds = {
      excellent: 0.95,
      good: 0.85,
      average: 0.7,
      poor: 0.5,
      critical: 0.3
    };
    this.distractionSources = {
      visual: "Distractores visuais na tela",
      temporal: "Fadiga ao longo do tempo",
      cognitive: "Sobrecarga cognitiva",
      motor: "Interferência motora"
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  async analyze(data) {
    if (!data || !data.attempts) {
      console.warn("AttentionFocusCollector: Dados inválidos recebidos", data);
      return this.getDefaultMetrics();
    }
    return {
      sustainedAttention: this.assessSustainedAttention(data),
      selectiveAttention: this.assessSelectiveAttention(data),
      attentionalControl: this.assessAttentionalControl(data),
      focusStability: this.assessFocusStability(data),
      distractionResistance: this.assessDistractionResistance(data),
      vigilanceDecrement: this.assessVigilanceDecrement(data),
      attentionalSwitching: this.assessAttentionalSwitching(data),
      concentrationDepth: this.assessConcentrationDepth(data),
      cognitiveFlexibility: this.assessCognitiveFlexibility(data),
      attentionalEfficiency: this.assessAttentionalEfficiency(data)
    };
  }
  assessSustainedAttention(data) {
    const attempts = data.attempts;
    if (attempts.length < 5) return 0.7;
    const timeSegments = this.divideIntoTimeSegments(attempts, 5);
    const segmentAccuracies = timeSegments.map(
      (segment) => segment.filter((a) => a.isCorrect).length / segment.length
    );
    const meanAccuracy = segmentAccuracies.reduce((sum, acc) => sum + acc, 0) / segmentAccuracies.length;
    const variance = segmentAccuracies.reduce((sum, acc) => sum + Math.pow(acc - meanAccuracy, 2), 0) / segmentAccuracies.length;
    const stability = Math.max(0, 1 - variance * 2);
    const sustainedScore = meanAccuracy * 0.7 + stability * 0.3;
    return Math.min(1, sustainedScore);
  }
  assessSelectiveAttention(data) {
    const attempts = data.attempts;
    if (attempts.length === 0) return 0.7;
    attempts.map((a) => a.correctAnswer);
    const responseAccuracy = attempts.filter((a) => a.isCorrect).length / attempts.length;
    const timeByDensity = this.analyzeTimeByObjectDensity(attempts);
    const selectionErrors = this.analyzeSelectionErrors(attempts);
    const selectiveScore = responseAccuracy * 0.5 + timeByDensity.efficiency * 0.3 + (1 - selectionErrors.rate) * 0.2;
    return Math.min(1, selectiveScore);
  }
  assessAttentionalControl(data) {
    const attempts = data.attempts;
    if (attempts.length === 0) return 0.7;
    const difficultyGroups = this.groupByDifficulty(attempts);
    let controlScore = 0;
    let groupCount = 0;
    Object.keys(difficultyGroups).forEach((difficulty) => {
      const group = difficultyGroups[difficulty];
      if (group.length === 0) return;
      const avgTime = group.reduce((sum, a) => sum + a.responseTime, 0) / group.length;
      const expectedTime = this.getExpectedTimeForDifficulty(difficulty);
      const timeAdaptation = 1 - Math.abs(avgTime - expectedTime) / expectedTime;
      const accuracy = group.filter((a) => a.isCorrect).length / group.length;
      controlScore += timeAdaptation * 0.6 + accuracy * 0.4;
      groupCount++;
    });
    return groupCount > 0 ? controlScore / groupCount : 0.7;
  }
  assessFocusStability(data) {
    const attempts = data.attempts;
    if (attempts.length < 8) return 0.7;
    const responseTimes = attempts.map((a) => a.responseTime);
    const meanTime = responseTimes.reduce((sum, t) => sum + t, 0) / responseTimes.length;
    const timeVariability = responseTimes.reduce((sum, t) => sum + Math.pow(t - meanTime, 2), 0) / responseTimes.length;
    const stabilityScore = Math.max(0, 1 - Math.sqrt(timeVariability) / meanTime);
    const attentionalLapses = this.detectAttentionalLapses(attempts);
    const lapsesPenalty = attentionalLapses.frequency * 0.3;
    return Math.min(1, Math.max(0, stabilityScore - lapsesPenalty));
  }
  assessDistractionResistance(data) {
    const attempts = data.attempts;
    if (attempts.length === 0) return 0.7;
    const highDensityAttempts = attempts.filter((a) => a.correctAnswer > 8);
    const lowDensityAttempts = attempts.filter((a) => a.correctAnswer <= 5);
    if (highDensityAttempts.length === 0 || lowDensityAttempts.length === 0) {
      return 0.7;
    }
    const highDensityAccuracy = highDensityAttempts.filter((a) => a.isCorrect).length / highDensityAttempts.length;
    const lowDensityAccuracy = lowDensityAttempts.filter((a) => a.isCorrect).length / lowDensityAttempts.length;
    const distractionResistance = 1 - Math.abs(highDensityAccuracy - lowDensityAccuracy);
    const errorRecovery = this.analyzeErrorRecovery(attempts);
    return Math.min(1, distractionResistance * 0.7 + errorRecovery * 0.3);
  }
  assessVigilanceDecrement(data) {
    const attempts = data.attempts;
    if (attempts.length < 10) return 0.7;
    const timeQuarters = this.divideIntoTimeSegments(attempts, 4);
    if (timeQuarters.length < 2) return 0.7;
    const quarterAccuracies = timeQuarters.map(
      (quarter) => quarter.filter((a) => a.isCorrect).length / quarter.length
    );
    const firstQuarter = quarterAccuracies[0];
    const lastQuarter = quarterAccuracies[quarterAccuracies.length - 1];
    const decline = Math.max(0, firstQuarter - lastQuarter);
    const vigilanceScore = Math.max(0, 1 - decline * 2);
    return Math.min(1, vigilanceScore);
  }
  assessAttentionalSwitching(data) {
    const attempts = data.attempts;
    if (attempts.length === 0) return 0.7;
    const difficultyChanges = this.detectDifficultyChanges(attempts);
    if (difficultyChanges.length === 0) return 0.7;
    const switchingEfficiency = difficultyChanges.map((change) => {
      const postSwitchAttempts = attempts.slice(change.index, change.index + 3);
      return postSwitchAttempts.filter((a) => a.isCorrect).length / postSwitchAttempts.length;
    });
    const avgSwitchingEfficiency = switchingEfficiency.reduce((sum, eff) => sum + eff, 0) / switchingEfficiency.length;
    return Math.min(1, avgSwitchingEfficiency);
  }
  assessConcentrationDepth(data) {
    const attempts = data.attempts;
    if (attempts.length === 0) return 0.7;
    const complexAttempts = attempts.filter((a) => a.correctAnswer > 10);
    if (complexAttempts.length === 0) {
      const accuracy = attempts.filter((a) => a.isCorrect).length / attempts.length;
      const avgTime = attempts.reduce((sum, a) => sum + a.responseTime, 0) / attempts.length;
      const timeScore2 = this.assessOptimalResponseTime(avgTime);
      return Math.min(1, accuracy * 0.6 + timeScore2 * 0.4);
    }
    const complexAccuracy = complexAttempts.filter((a) => a.isCorrect).length / complexAttempts.length;
    const avgComplexTime = complexAttempts.reduce((sum, a) => sum + a.responseTime, 0) / complexAttempts.length;
    const timeScore = this.assessOptimalResponseTime(avgComplexTime);
    const concentrationScore = complexAccuracy * 0.7 + timeScore * 0.3;
    return Math.min(1, concentrationScore);
  }
  assessCognitiveFlexibility(data) {
    const attempts = data.attempts;
    if (attempts.length < 5) return 0.7;
    const numberRanges = this.categorizeByNumberRange(attempts);
    const rangeCount = Object.keys(numberRanges).length;
    if (rangeCount < 2) return 0.7;
    let flexibilityScore = 0;
    Object.keys(numberRanges).forEach((range) => {
      const rangeAttempts = numberRanges[range];
      const rangeAccuracy = rangeAttempts.filter((a) => a.isCorrect).length / rangeAttempts.length;
      flexibilityScore += rangeAccuracy;
    });
    flexibilityScore /= rangeCount;
    const adaptiveStrategies = this.detectAdaptiveStrategies(attempts);
    const strategyBonus = adaptiveStrategies.count * 0.1;
    return Math.min(1, flexibilityScore + strategyBonus);
  }
  assessAttentionalEfficiency(data) {
    const attempts = data.attempts;
    if (attempts.length === 0) return 0.7;
    const accuracy = attempts.filter((a) => a.isCorrect).length / attempts.length;
    const avgTime = attempts.reduce((sum, a) => sum + a.responseTime, 0) / attempts.length;
    const timeEfficiency = this.calculateTimeEfficiency(avgTime);
    const efficiencyScore = accuracy * 0.6 + timeEfficiency * 0.4;
    const consistencyBonus = this.assessEfficiencyConsistency(attempts) * 0.1;
    return Math.min(1, efficiencyScore + consistencyBonus);
  }
  // Métodos auxiliares
  divideIntoTimeSegments(attempts, segmentCount) {
    const segmentSize = Math.floor(attempts.length / segmentCount);
    const segments = [];
    for (let i = 0; i < segmentCount; i++) {
      const start = i * segmentSize;
      const end = i === segmentCount - 1 ? attempts.length : (i + 1) * segmentSize;
      segments.push(attempts.slice(start, end));
    }
    return segments.filter((segment) => segment.length > 0);
  }
  analyzeTimeByObjectDensity(attempts) {
    const densityGroups = {
      low: attempts.filter((a) => a.correctAnswer <= 3),
      medium: attempts.filter((a) => a.correctAnswer > 3 && a.correctAnswer <= 8),
      high: attempts.filter((a) => a.correctAnswer > 8)
    };
    let totalEfficiency = 0;
    let groupCount = 0;
    Object.keys(densityGroups).forEach((density) => {
      const group = densityGroups[density];
      if (group.length === 0) return;
      const avgTime = group.reduce((sum, a) => sum + a.responseTime, 0) / group.length;
      const expectedTime = density === "low" ? 2e3 : density === "medium" ? 4e3 : 6e3;
      const efficiency = Math.max(0, 1 - Math.abs(avgTime - expectedTime) / expectedTime);
      totalEfficiency += efficiency;
      groupCount++;
    });
    return { efficiency: groupCount > 0 ? totalEfficiency / groupCount : 0.7 };
  }
  analyzeSelectionErrors(attempts) {
    const errors = attempts.filter((a) => !a.isCorrect);
    if (errors.length === 0) return { rate: 0, patterns: [] };
    const offByOne = errors.filter((e) => Math.abs(e.userAnswer - e.correctAnswer) === 1).length;
    const offByMany = errors.filter((e) => Math.abs(e.userAnswer - e.correctAnswer) > 3).length;
    const selectionErrorRate = errors.length / attempts.length;
    const patterns = [];
    if (offByOne / errors.length > 0.5) patterns.push("off_by_one_tendency");
    if (offByMany / errors.length > 0.3) patterns.push("gross_miscounting");
    return { rate: selectionErrorRate, patterns };
  }
  groupByDifficulty(attempts) {
    return attempts.reduce((groups, attempt) => {
      const difficulty = this.inferDifficulty(attempt.correctAnswer);
      if (!groups[difficulty]) groups[difficulty] = [];
      groups[difficulty].push(attempt);
      return groups;
    }, {});
  }
  inferDifficulty(number) {
    if (number <= 5) return "easy";
    if (number <= 10) return "medium";
    return "hard";
  }
  getExpectedTimeForDifficulty(difficulty) {
    const times = { easy: 2500, medium: 4e3, hard: 6e3 };
    return times[difficulty] || 4e3;
  }
  detectAttentionalLapses(attempts) {
    const lapses = attempts.filter(
      (a) => !a.isCorrect && a.responseTime < 1e3 || // Resposta impulsiva incorreta
      a.responseTime > 15e3
      // Resposta muito lenta (possível distração)
    );
    return {
      frequency: lapses.length / attempts.length,
      count: lapses.length,
      types: {
        impulsive: lapses.filter((l) => l.responseTime < 1e3).length,
        distracted: lapses.filter((l) => l.responseTime > 15e3).length
      }
    };
  }
  analyzeErrorRecovery(attempts) {
    const errors = attempts.map((attempt, index) => ({ ...attempt, index })).filter((a) => !a.isCorrect);
    if (errors.length === 0) return 1;
    let recoveryCount = 0;
    errors.forEach((error2) => {
      const nextAttempt = attempts[error2.index + 1];
      if (nextAttempt && nextAttempt.isCorrect) {
        recoveryCount++;
      }
    });
    return recoveryCount / errors.length;
  }
  detectDifficultyChanges(attempts) {
    const changes = [];
    for (let i = 1; i < attempts.length; i++) {
      const prevDifficulty = this.inferDifficulty(attempts[i - 1].correctAnswer);
      const currDifficulty = this.inferDifficulty(attempts[i].correctAnswer);
      if (prevDifficulty !== currDifficulty) {
        changes.push({
          index: i,
          from: prevDifficulty,
          to: currDifficulty
        });
      }
    }
    return changes;
  }
  assessOptimalResponseTime(avgTime) {
    const optimalMin = 2e3;
    const optimalMax = 5e3;
    if (avgTime >= optimalMin && avgTime <= optimalMax) {
      return 1;
    } else if (avgTime < optimalMin) {
      return Math.max(0, avgTime / optimalMin);
    } else {
      return Math.max(0, 1 - (avgTime - optimalMax) / optimalMax);
    }
  }
  categorizeByNumberRange(attempts) {
    return attempts.reduce((categories, attempt) => {
      const range = attempt.correctAnswer <= 3 ? "small" : attempt.correctAnswer <= 8 ? "medium" : "large";
      if (!categories[range]) categories[range] = [];
      categories[range].push(attempt);
      return categories;
    }, {});
  }
  detectAdaptiveStrategies(attempts) {
    let strategyChanges = 0;
    for (let i = 1; i < attempts.length; i++) {
      const prev = attempts[i - 1];
      const curr = attempts[i];
      if (!prev.isCorrect && curr.responseTime > prev.responseTime * 1.3) {
        strategyChanges++;
      }
    }
    return { count: strategyChanges };
  }
  calculateTimeEfficiency(avgTime) {
    const idealTime = 3e3;
    return Math.max(0, 1 - Math.abs(avgTime - idealTime) / idealTime);
  }
  assessEfficiencyConsistency(attempts) {
    if (attempts.length < 5) return 1;
    const efficiencies = attempts.map((a) => {
      const accuracy = a.isCorrect ? 1 : 0;
      const timeEfficiency = this.calculateTimeEfficiency(a.responseTime);
      return accuracy * 0.6 + timeEfficiency * 0.4;
    });
    const meanEfficiency = efficiencies.reduce((sum, e) => sum + e, 0) / efficiencies.length;
    const variance = efficiencies.reduce((sum, e) => sum + Math.pow(e - meanEfficiency, 2), 0) / efficiencies.length;
    return Math.max(0, 1 - variance);
  }
  getDefaultMetrics() {
    return {
      sustainedAttention: 0.7,
      selectiveAttention: 0.7,
      attentionalControl: 0.7,
      focusStability: 0.7,
      distractionResistance: 0.7,
      vigilanceDecrement: 0.7,
      attentionalSwitching: 0.7,
      concentrationDepth: 0.7,
      cognitiveFlexibility: 0.7,
      attentionalEfficiency: 0.7
    };
  }
}
class VisualProcessingCollector {
  constructor() {
    this.visualComponents = {
      objectRecognition: "Reconhecimento de objetos visuais",
      spatialDistribution: "Processamento de distribuição espacial",
      visualScanning: "Varredura visual sistemática",
      figureGround: "Discriminação figura-fundo",
      visualMemory: "Memória visual de curto prazo"
    };
    this.processingThresholds = {
      excellent: 0.95,
      good: 0.85,
      average: 0.7,
      poor: 0.5,
      critical: 0.3
    };
    this.scanningPatterns = {
      systematic: "Varredura visual sistemática",
      random: "Varredura aleatória",
      focused: "Varredura concentrada",
      exhaustive: "Varredura exaustiva"
    };
    console.log("👁️ VisualProcessingCollector inicializado");
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Retorna métricas padrão quando dados são inválidos
   */
  getDefaultMetrics() {
    return {
      visualScanningEfficiency: { score: 0, efficiency: 0, scanningPattern: "unknown" },
      objectRecognition: { accuracy: 0, speed: 0, confidence: 0 },
      spatialDistribution: { uniformity: 0, clustering: 0, coverage: 0 },
      figureGroundPerception: { discrimination: 0, clarity: 0, focus: 0 },
      visualMemoryPerformance: { retention: 0, recall: 0, recognition: 0 },
      scanningStrategy: { strategy: "unknown", consistency: 0, effectiveness: 0 },
      processingSpeed: { averageTime: 0, variability: 0, efficiency: 0 },
      visualDiscrimination: { accuracy: 0, precision: 0, sensitivity: 0 },
      visualAttention: { focus: 0, sustain: 0, selective: 0 },
      perceptualOrganization: { grouping: 0, structure: 0, coherence: 0 },
      timestamp: Date.now(),
      dataQuality: "invalid"
    };
  }
  async analyze(data) {
    if (!data || !data.attempts) {
      console.warn("VisualProcessingCollector: Dados inválidos recebidos", data);
      return this.getDefaultMetrics();
    }
    return {
      visualScanningEfficiency: this.assessVisualScanningEfficiency(data),
      objectRecognition: this.assessObjectRecognition(data),
      spatialDistribution: this.assessSpatialDistribution(data),
      figureGroundPerception: this.assessFigureGroundPerception(data),
      visualMemoryPerformance: this.assessVisualMemoryPerformance(data),
      scanningStrategy: this.analyzeScanningStrategy(data),
      processingSpeed: this.assessProcessingSpeed(data),
      visualDiscrimination: this.assessVisualDiscrimination(data),
      visualAttention: this.assessVisualAttention(data),
      perceptualOrganization: this.assessPerceptualOrganization(data)
    };
  }
}
class MathematicalReasoningCollector {
  constructor() {
    this.reasoningComponents = {
      quantitativeReasoning: "Raciocínio quantitativo fundamental",
      mathematicalConcepts: "Compreensão de conceitos matemáticos",
      logicalSequencing: "Sequenciamento lógico e ordenação",
      abstractThinking: "Pensamento abstrato com números",
      problemSolving: "Resolução de problemas matemáticos"
    };
    this.mathematicalSkills = {
      counting: "Contagem sequencial",
      cardinality: "Compreensão de cardinalidade",
      ordinality: "Compreensão de ordinality",
      conservation: "Conservação numérica",
      comparison: "Comparação de quantidades"
    };
    console.log("🧮 MathematicalReasoningCollector inicializado");
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  async analyze(data) {
    if (!data || !data.attempts) {
      console.warn("MathematicalReasoningCollector: Dados inválidos recebidos", data);
      return this.getDefaultMetrics();
    }
    return {
      quantitativeAccuracy: this.assessQuantitativeAccuracy(data),
      countingProficiency: this.assessCountingProficiency(data),
      mathematicalStrategies: this.assessMathematicalStrategies(data),
      numericalConceptUnderstanding: this.assessNumericalConcepts(data),
      logicalSequencing: this.assessLogicalSequencing(data),
      numericalMemory: this.assessNumericalMemory(data),
      errorAnalysis: this.analyzeNumericalErrors(data),
      mathematicalFluency: this.assessMathematicalFluency(data),
      abstractReasoning: this.assessAbstractReasoning(data),
      patternRecognition: this.assessPatternRecognition(data)
    };
  }
  getDefaultMetrics() {
    return {
      quantitativeAccuracy: 0.7,
      countingProficiency: 0.7,
      mathematicalStrategies: { efficiency: 0.7, adaptability: 0.7 },
      numericalConceptUnderstanding: 0.7,
      logicalSequencing: 0.7,
      numericalMemory: 0.7,
      errorAnalysis: { patterns: [], frequency: 0 },
      mathematicalFluency: 0.7,
      abstractReasoning: 0.7,
      patternRecognition: 0.7
    };
  }
}
class ErrorPatternCollector {
  constructor() {
    this.name = "ContagemNumerosErrorPatternCollector";
    this.description = "Coleta padrões de erros no ContagemNumeros";
    this.version = "1.0.0";
    this.isActive = true;
    this.collectedData = [];
    this.errorData = {
      countingErrors: {},
      sequenceErrors: [],
      numberRecognitionErrors: [],
      quantityEstimationErrors: [],
      persistentErrors: {},
      errorClusters: [],
      learningIndicators: [],
      mathematicalConcepts: {}
    };
    this.sessionStartTime = Date.now();
    this.errorThresholds = {
      persistent: 3,
      cluster: 5,
      severity: {
        low: 0.3,
        medium: 0.6,
        high: 0.8
      }
    };
    console.log(`🔢 ${this.name} v${this.version} inicializado`);
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de erros
   */
  collect(gameData) {
    if (!gameData) {
      console.warn("ContagemNumerosErrorPatternCollector: Dados do jogo não fornecidos para análise");
      return { errors: [], patterns: [], metrics: {} };
    }
    console.log(`📊 ContagemNumerosErrorPatternCollector: Analisando dados da sessão ${gameData.sessionId || "sem ID"}`);
    try {
      const errorMetrics = this.analyzeErrorPatterns(gameData);
      const errors = [];
      if (gameData.attemptHistory && Array.isArray(gameData.attemptHistory)) {
        gameData.attemptHistory.forEach((attempt, index) => {
          if (!attempt.isCorrect && attempt.targetNumber !== void 0 && attempt.selectedNumber !== void 0) {
            const countingError = this.collectCountingError(
              attempt.targetNumber,
              attempt.selectedNumber,
              {
                difficulty: gameData.difficulty || "medium",
                responseTime: attempt.responseTime || 0,
                attemptNumber: index
              }
            );
            if (countingError) errors.push(countingError);
          }
        });
      }
      if (gameData.sequenceHistory && Array.isArray(gameData.sequenceHistory)) {
        gameData.sequenceHistory.forEach((sequence) => {
          if (sequence.expected && sequence.actual && sequence.expected !== sequence.actual) {
            const sequenceError = this.collectSequenceError(
              sequence.expected,
              sequence.actual,
              {
                sequenceType: sequence.type || "ascending",
                difficulty: gameData.difficulty || "medium"
              }
            );
            if (sequenceError) errors.push(sequenceError);
          }
        });
      }
      const collectedMetric = {
        timestamp: Date.now(),
        type: "error_pattern",
        gameType: "ContagemNumeros",
        data: errorMetrics,
        errors,
        sessionData: {
          sessionId: gameData.sessionId,
          level: gameData.level || 1,
          attempt: gameData.attempt || 1
        }
      };
      this.collectedData.push(collectedMetric);
      this.categorizeErrors(errorMetrics);
      return {
        errors,
        patterns: errorMetrics,
        metrics: this.generateErrorMetrics(gameData)
      };
    } catch (error2) {
      console.error("❌ Erro ao coletar padrões de erro (ContagemNumeros):", error2);
      return { errors: [], patterns: [], metrics: {}, error: error2.message };
    }
  }
  analyzeErrorPatterns(gameData) {
    const patterns = {
      countingErrors: this.detectCountingErrors(gameData),
      sequenceErrors: this.detectSequenceErrors(gameData),
      numberRecognitionErrors: this.detectRecognitionErrors(gameData),
      quantityEstimationErrors: this.detectEstimationErrors(gameData),
      severity: this.calculateOverallSeverity(gameData)
    };
    return patterns;
  }
  detectCountingErrors(gameData) {
    return [];
  }
  detectSequenceErrors(gameData) {
    return [];
  }
  detectRecognitionErrors(gameData) {
    return [];
  }
  detectEstimationErrors(gameData) {
    return [];
  }
  categorizeErrors(errorMetrics) {
  }
  /**
   * Coleta erros de contagem
   */
  collectCountingError(correctCount, providedCount, context) {
    const errorKey = `${correctCount}->${providedCount}`;
    const difference = Math.abs(correctCount - providedCount);
    const countingError = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      correctCount,
      providedCount,
      difference,
      errorType: this.identifyCountingErrorType(correctCount, providedCount, context),
      context: {
        difficulty: context.difficulty || "medium",
        objectType: context.objectType || "unknown",
        arrangement: context.arrangement || "random",
        responseTime: context.responseTime || 0,
        attempts: context.attempts || 1,
        visualComplexity: context.visualComplexity || "medium"
      },
      severity: this.calculateCountingErrorSeverity(correctCount, providedCount, context),
      relativeError: difference / correctCount,
      direction: providedCount > correctCount ? "overcount" : "undercount"
    };
    if (!this.errorData.countingErrors[errorKey]) {
      this.errorData.countingErrors[errorKey] = [];
    }
    this.errorData.countingErrors[errorKey].push(countingError);
    this.detectPersistentCountingError(errorKey, countingError);
    this.analyzeCountingPattern(countingError);
    return countingError;
  }
  /**
   * Coleta erros de sequência numérica
   */
  collectSequenceError(expectedSequence, actualSequence, context) {
    const sequenceError = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      expectedSequence,
      actualSequence,
      errorType: this.identifySequenceErrorType(expectedSequence, actualSequence),
      context: {
        sequenceType: context.sequenceType || "ascending",
        difficulty: context.difficulty || "medium"
      },
      severity: this.calculateSequenceErrorSeverity(expectedSequence, actualSequence, context)
    };
    this.errorData.sequenceErrors.push(sequenceError);
    return sequenceError;
  }
  /**
   * Identifica o tipo de erro de contagem
   */
  identifyCountingErrorType(correct, provided, context) {
    const difference = Math.abs(correct - provided);
    const relativeError = difference / correct;
    if (difference === 0) return "no_error";
    if (difference === 1) return "off_by_one";
    if (relativeError < 0.2) return "minor_miscount";
    if (relativeError < 0.5) return "moderate_miscount";
    if (relativeError >= 0.5) return "major_miscount";
    if (provided === correct * 2) return "double_counting";
    if (provided === Math.floor(correct / 2)) return "half_counting";
    if (provided % 5 === 0 && correct % 5 !== 0) return "rounding_to_five";
    if (provided % 10 === 0 && correct % 10 !== 0) return "rounding_to_ten";
    return "general_miscount";
  }
  /**
   * Identifica o tipo de erro de sequência
   */
  identifySequenceErrorType(expected, actual) {
    if (!actual) return "no_sequence";
    return "sequence_pattern_error";
  }
  /**
   * Calcula a severidade do erro de contagem
   */
  calculateCountingErrorSeverity(targetNumber2, selectedNumber, context) {
    let severity = 0.5;
    const distance = Math.abs(targetNumber2 - selectedNumber);
    severity += distance * 0.05;
    if (severity > 0.8) severity = 0.8;
    if (context.responseTime > 5e3) severity += 0.1;
    if (context.responseTime < 500) severity += 0.1;
    if (context.difficulty === "hard") severity -= 0.1;
    return Math.min(Math.max(severity, 0), 1);
  }
  /**
   * Calcula a severidade do erro de sequência
   */
  calculateSequenceErrorSeverity(expected, actual, context) {
    return 0.6;
  }
  /**
   * Salva dados coletados para análise futura
   */
  saveCollectedData(gameData, errorMetrics, errors) {
    const collectedMetric = {
      timestamp: Date.now(),
      type: "error_pattern",
      gameType: "ContagemNumeros",
      data: errorMetrics,
      errors,
      sessionData: {
        sessionId: gameData.sessionId,
        level: gameData.level || 1,
        attempt: gameData.attempt || 1
      }
    };
    this.collectedData.push(collectedMetric);
  }
  /**
   * Gera métricas de erro com base nos dados coletados
   */
  generateErrorMetrics(gameData) {
    const countingErrorCount = Object.values(this.errorData.countingErrors).reduce(
      (total, errors) => total + errors.length,
      0
    );
    return {
      totalErrors: countingErrorCount + this.errorData.sequenceErrors.length,
      uniqueCountingErrors: Object.keys(this.errorData.countingErrors).length,
      mostCommonError: this.findMostCommonError(),
      averageSeverity: this.calculateAverageSeverity(),
      mathematicalUnderstandingScore: this.calculateMathematicalUnderstandingScore(gameData),
      numericalProcessingScore: this.calculateNumericalProcessingScore(gameData),
      improvement: this.calculateImprovementMetric(gameData)
    };
  }
  /**
   * Encontra o erro mais comum
   */
  findMostCommonError() {
    let maxCount = 0;
    let mostCommonError = null;
    Object.entries(this.errorData.countingErrors).forEach(([errorKey, errors]) => {
      if (errors.length > maxCount) {
        maxCount = errors.length;
        mostCommonError = errorKey;
      }
    });
    return {
      error: mostCommonError,
      count: maxCount
    };
  }
  /**
   * Calcula a severidade média dos erros
   */
  calculateAverageSeverity() {
    let totalSeverity = 0;
    let errorCount = 0;
    Object.values(this.errorData.countingErrors).forEach((errors) => {
      errors.forEach((error2) => {
        totalSeverity += error2.severity;
        errorCount++;
      });
    });
    this.errorData.sequenceErrors.forEach((error2) => {
      totalSeverity += error2.severity;
      errorCount++;
    });
    return errorCount > 0 ? totalSeverity / errorCount : 0;
  }
  /**
   * Calcula pontuação de compreensão matemática
   */
  calculateMathematicalUnderstandingScore(gameData) {
    return 0.7;
  }
  /**
   * Calcula pontuação de processamento numérico
   */
  calculateNumericalProcessingScore(gameData) {
    return 0.6;
  }
  /**
   * Calcula métrica de melhoria ao longo do tempo
   */
  calculateImprovementMetric(gameData) {
    return 0.5;
  }
  /**
   * Método de análise para compatibilidade com outros coletores
   */
  analyze(gameData) {
    return this.collect(gameData);
  }
  /**
   * Detecta padrões persistentes de contagem
   */
  detectPersistentCountingError(errorKey, errorData) {
    if (!this.errorData.persistentErrors[errorKey]) {
      this.errorData.persistentErrors[errorKey] = [];
    }
    this.errorData.persistentErrors[errorKey].push(errorData);
    if (this.errorData.persistentErrors[errorKey].length >= this.errorThresholds.persistent) {
      errorData.isPersistent = true;
      this.flagForIntervention(errorKey, "counting_error");
    }
  }
  /**
   * Analisa padrões de contagem
   */
  analyzeCountingPattern(countingError) {
    const recentCountingErrors = Object.values(this.errorData.countingErrors).flat().slice(-5);
    const overCountTrend = recentCountingErrors.filter((e) => e.direction === "overcount").length;
    const underCountTrend = recentCountingErrors.filter((e) => e.direction === "undercount").length;
    if (overCountTrend >= 3) {
      countingError.patternDetected = "consistent_overcounting";
    } else if (underCountTrend >= 3) {
      countingError.patternDetected = "consistent_undercounting";
    }
  }
  /**
   * Detecta padrões de sequência
   */
  detectSequencePattern(sequenceError) {
    const recentSequenceErrors = this.errorData.sequenceErrors.slice(-5);
    const errorPositions = recentSequenceErrors.flatMap((e) => e.errorPositions.map((ep) => ep.position));
    const positionCounts = {};
    errorPositions.forEach((pos) => {
      positionCounts[pos] = (positionCounts[pos] || 0) + 1;
    });
    const frequentPosition = Object.entries(positionCounts).find(([pos, count]) => count >= 3);
    if (frequentPosition) {
      sequenceError.patternDetected = `consistent_error_at_position_${frequentPosition[0]}`;
    }
  }
  /**
   * Detecta padrões de reconhecimento
   */
  detectRecognitionPattern(recognitionError) {
    const recentRecognitionErrors = this.errorData.numberRecognitionErrors.slice(-5);
    const confusionTypes = recentRecognitionErrors.map((e) => e.confusionType);
    const visualSimilarityErrors = confusionTypes.filter((t) => t === "visual_similarity").length;
    if (visualSimilarityErrors >= 3) {
      recognitionError.patternDetected = "consistent_visual_confusion";
    }
  }
  /**
   * Analisa padrões de estimativa
   */
  analyzeEstimationPattern(estimationError) {
    const recentEstimationErrors = this.errorData.quantityEstimationErrors.slice(-5);
    const strategies = recentEstimationErrors.map((e) => e.estimationStrategy);
    const strategyCounts = {};
    strategies.forEach((strategy) => {
      strategyCounts[strategy] = (strategyCounts[strategy] || 0) + 1;
    });
    const dominantStrategy = Object.entries(strategyCounts).find(([strategy, count]) => count >= 3);
    if (dominantStrategy) {
      estimationError.patternDetected = `consistent_${dominantStrategy[0]}`;
    }
  }
  /**
   * Analisa padrões de erro coletados
   */
  /**
   * Avalia conceitos matemáticos
   */
  assessMathematicalConcepts() {
    return {
      counting: this.assessCountingSkills(),
      numberRecognition: this.assessNumberRecognitionSkills(),
      sequencing: this.assessSequencingSkills(),
      quantityEstimation: this.assessQuantityEstimationSkills(),
      placeValue: this.assessPlaceValueUnderstanding()
    };
  }
  /**
   * Avalia habilidades de contagem
   */
  assessCountingSkills() {
    const countingErrors = Object.values(this.errorData.countingErrors).flat();
    if (countingErrors.length === 0) return { level: "proficient", confidence: 1 };
    const averageSeverity = countingErrors.reduce((sum, e) => sum + e.severity, 0) / countingErrors.length;
    const offByOneErrors = countingErrors.filter((e) => e.errorType === "off_by_one").length;
    const majorErrors = countingErrors.filter((e) => e.errorType === "major_miscount").length;
    let level = "developing";
    let confidence = 0.5;
    if (averageSeverity < 0.3 && majorErrors === 0) {
      level = "proficient";
      confidence = 0.8;
    } else if (averageSeverity < 0.6 && majorErrors <= 1) {
      level = "emerging";
      confidence = 0.6;
    } else {
      level = "needs_support";
      confidence = Math.max(0.2, 1 - averageSeverity);
    }
    return { level, confidence, details: { offByOneErrors, majorErrors, averageSeverity } };
  }
  /**
   * Avalia habilidades de reconhecimento numérico
   */
  assessNumberRecognitionSkills() {
    const recognitionErrors = this.errorData.numberRecognitionErrors;
    if (recognitionErrors.length === 0) return { level: "proficient", confidence: 1 };
    const visualConfusions = recognitionErrors.filter((e) => e.confusionType === "visual_similarity").length;
    const averageSeverity = recognitionErrors.reduce((sum, e) => sum + e.severity, 0) / recognitionErrors.length;
    let level = "developing";
    let confidence = 0.5;
    if (averageSeverity < 0.3) {
      level = "proficient";
      confidence = 0.8;
    } else if (visualConfusions > recognitionErrors.length * 0.5) {
      level = "visual_processing_support";
      confidence = 0.4;
    } else {
      level = "needs_support";
      confidence = Math.max(0.2, 1 - averageSeverity);
    }
    return { level, confidence, details: { visualConfusions, averageSeverity } };
  }
  /**
   * Avalia habilidades de sequenciamento
   */
  assessSequencingSkills() {
    const sequenceErrors = this.errorData.sequenceErrors;
    if (sequenceErrors.length === 0) return { level: "proficient", confidence: 1 };
    const incompleteSequences = sequenceErrors.filter((e) => e.errorType === "incomplete_sequence").length;
    const averageSeverity = sequenceErrors.reduce((sum, e) => sum + e.severity, 0) / sequenceErrors.length;
    let level = "developing";
    let confidence = 0.5;
    if (averageSeverity < 0.4) {
      level = "proficient";
      confidence = 0.8;
    } else if (incompleteSequences > sequenceErrors.length * 0.6) {
      level = "sequence_completion_support";
      confidence = 0.4;
    } else {
      level = "needs_support";
      confidence = Math.max(0.2, 1 - averageSeverity);
    }
    return { level, confidence, details: { incompleteSequences, averageSeverity } };
  }
  /**
   * Avalia habilidades de estimativa de quantidade
   */
  assessQuantityEstimationSkills() {
    const estimationErrors = this.errorData.quantityEstimationErrors;
    if (estimationErrors.length === 0) return { level: "proficient", confidence: 1 };
    const accurateEstimations = estimationErrors.filter((e) => e.estimationStrategy === "accurate_subitizing").length;
    const averageRelativeError = estimationErrors.reduce((sum, e) => sum + e.relativeError, 0) / estimationErrors.length;
    let level = "developing";
    let confidence = 0.5;
    if (averageRelativeError < 0.2) {
      level = "proficient";
      confidence = 0.9;
    } else if (accurateEstimations > estimationErrors.length * 0.3) {
      level = "emerging";
      confidence = 0.7;
    } else {
      level = "needs_support";
      confidence = Math.max(0.2, 1 - averageRelativeError);
    }
    return { level, confidence, details: { accurateEstimations, averageRelativeError } };
  }
  /**
   * Avalia compreensão de valor posicional
   */
  assessPlaceValueUnderstanding() {
    const recognitionErrors = this.errorData.numberRecognitionErrors;
    const digitReversals = recognitionErrors.filter((e) => e.confusionType === "digit_reversal").length;
    const digitOmissions = recognitionErrors.filter((e) => e.confusionType === "digit_omission").length;
    let level = "developing";
    let confidence = 0.6;
    if (digitReversals === 0 && digitOmissions === 0) {
      level = "proficient";
      confidence = 0.8;
    } else if (digitReversals > 2 || digitOmissions > 2) {
      level = "needs_support";
      confidence = 0.3;
    }
    return { level, confidence, details: { digitReversals, digitOmissions } };
  }
  /**
   * Gera métricas de erro estruturadas
   */
  // Métodos auxiliares para métricas
  getMostCommonCountingError() {
    const allErrors = Object.values(this.errorData.countingErrors).flat();
    const errorTypes = {};
    allErrors.forEach((error2) => {
      errorTypes[error2.errorType] = (errorTypes[error2.errorType] || 0) + 1;
    });
    return Object.entries(errorTypes).reduce(
      (max, [type, count]) => count > max.count ? { type, count } : max,
      { type: null, count: 0 }
    );
  }
  getAverageCountingErrorSeverity() {
    const allErrors = Object.values(this.errorData.countingErrors).flat();
    if (allErrors.length === 0) return 0;
    return allErrors.reduce((sum, error2) => sum + error2.severity, 0) / allErrors.length;
  }
  getCountingErrorTypes() {
    const allErrors = Object.values(this.errorData.countingErrors).flat();
    const errorTypes = {};
    allErrors.forEach((error2) => {
      errorTypes[error2.errorType] = (errorTypes[error2.errorType] || 0) + 1;
    });
    return errorTypes;
  }
  getAverageSequenceErrorSeverity() {
    if (this.errorData.sequenceErrors.length === 0) return 0;
    return this.errorData.sequenceErrors.reduce((sum, error2) => sum + error2.severity, 0) / this.errorData.sequenceErrors.length;
  }
  getCommonSequenceErrorPositions() {
    const positions = {};
    if (!this.errorData.sequenceErrors || this.errorData.sequenceErrors.length === 0) {
      return [];
    }
    this.errorData.sequenceErrors.forEach((error2) => {
      if (error2.errorPositions && Array.isArray(error2.errorPositions)) {
        error2.errorPositions.forEach((ep) => {
          positions[ep.position] = (positions[ep.position] || 0) + 1;
        });
      } else if (error2.errorPosition) {
        const position = error2.errorPosition;
        positions[position] = (positions[position] || 0) + 1;
      }
    });
    return Object.entries(positions).sort(([, a], [, b]) => b - a).slice(0, 3).map(([position, count]) => ({ position: parseInt(position), count }));
  }
  getAverageRecognitionErrorSeverity() {
    if (this.errorData.numberRecognitionErrors.length === 0) return 0;
    return this.errorData.numberRecognitionErrors.reduce((sum, error2) => sum + error2.severity, 0) / this.errorData.numberRecognitionErrors.length;
  }
  getRecognitionConfusionTypes() {
    const confusionTypes = {};
    this.errorData.numberRecognitionErrors.forEach((error2) => {
      confusionTypes[error2.confusionType] = (confusionTypes[error2.confusionType] || 0) + 1;
    });
    return confusionTypes;
  }
  getAverageEstimationError() {
    if (this.errorData.quantityEstimationErrors.length === 0) return 0;
    return this.errorData.quantityEstimationErrors.reduce((sum, error2) => sum + error2.relativeError, 0) / this.errorData.quantityEstimationErrors.length;
  }
  getEstimationStrategies() {
    const strategies = {};
    this.errorData.quantityEstimationErrors.forEach((error2) => {
      strategies[error2.estimationStrategy] = (strategies[error2.estimationStrategy] || 0) + 1;
    });
    return strategies;
  }
  generateLearningIndicators() {
    return {
      adaptationRate: this.calculateAdaptationRate(),
      errorRecovery: this.calculateErrorRecoveryRate(),
      conceptualGrowth: this.calculateConceptualGrowth(),
      consistencyScore: this.calculateConsistencyScore()
    };
  }
  calculateAdaptationRate() {
    const errorsBySlice = this.getErrorsByTimeSlices(10);
    if (errorsBySlice.length < 3) return 0;
    const firstHalf = errorsBySlice.slice(0, 5).reduce((sum, count) => sum + count, 0);
    const secondHalf = errorsBySlice.slice(5).reduce((sum, count) => sum + count, 0);
    if (firstHalf === 0) return 1;
    return Math.max(0, (firstHalf - secondHalf) / firstHalf);
  }
  calculateErrorRecoveryRate() {
    const allErrors = this.getAllErrors();
    let recoveryCount = 0;
    let totalErrors = 0;
    for (let i = 0; i < allErrors.length - 1; i++) {
      totalErrors++;
      if (allErrors[i + 1].severity < allErrors[i].severity) {
        recoveryCount++;
      }
    }
    return totalErrors > 0 ? recoveryCount / totalErrors : 0;
  }
  calculateConceptualGrowth() {
    const conceptAssessments = this.assessMathematicalConcepts();
    const averageConfidence = Object.values(conceptAssessments).reduce((sum, assessment) => sum + assessment.confidence, 0) / Object.keys(conceptAssessments).length;
    return averageConfidence;
  }
  calculateConsistencyScore() {
    const allErrors = this.getAllErrors();
    if (allErrors.length < 2) return 1;
    const severities = allErrors.map((e) => e.severity || 0);
    const mean = severities.reduce((sum, s) => sum + s, 0) / severities.length;
    const variance = severities.reduce((sum, s) => sum + Math.pow(s - mean, 2), 0) / severities.length;
    const standardDeviation = Math.sqrt(variance);
    return Math.max(0, 1 - standardDeviation / (mean + 0.1));
  }
  // Métodos auxiliares gerais
  flagForIntervention(errorKey, errorType) {
    console.warn(`Padrão numérico persistente detectado: ${errorKey} (${errorType})`);
  }
  getTotalErrorCount() {
    const countingErrors = Object.values(this.errorData.countingErrors).flat().length;
    return countingErrors + this.errorData.sequenceErrors.length + this.errorData.numberRecognitionErrors.length + this.errorData.quantityEstimationErrors.length;
  }
  identifyPersistentPatterns() {
    return Object.entries(this.errorData.persistentErrors).filter(([key, errors]) => errors.length >= this.errorThresholds.persistent).map(([key, errors]) => ({
      pattern: key,
      frequency: errors.length,
      severity: errors.reduce((sum, e) => sum + e.severity, 0) / errors.length
    }));
  }
  assessLearningProgress() {
    const recentErrors = this.getTimeWindowErrors(3e5);
    const olderErrors = this.getTimeWindowErrors(6e5, 3e5);
    return {
      errorReduction: olderErrors.length > 0 ? (olderErrors.length - recentErrors.length) / olderErrors.length : 0,
      improvementTrend: this.calculateImprovementTrend(),
      learningRate: this.calculateLearningRate()
    };
  }
  generateInterventionRecommendations() {
    const recommendations = [];
    const conceptAssessments = this.assessMathematicalConcepts();
    Object.entries(conceptAssessments).forEach(([concept, assessment]) => {
      if (assessment.confidence < 0.5) {
        recommendations.push({
          type: `${concept}_support`,
          priority: assessment.confidence < 0.3 ? "high" : "medium",
          description: `Suporte adicional necessário em ${concept}`,
          details: assessment.details
        });
      }
    });
    const persistentPatterns = this.identifyPersistentPatterns();
    persistentPatterns.forEach((pattern) => {
      recommendations.push({
        type: "targeted_practice",
        priority: pattern.severity > 0.7 ? "high" : "medium",
        description: `Prática direcionada para: ${pattern.pattern}`,
        frequency: pattern.frequency
      });
    });
    return recommendations;
  }
  calculateOverallSeverity() {
    const allErrors = this.getAllErrors();
    if (allErrors.length === 0) return 0;
    const totalSeverity = allErrors.reduce((sum, error2) => sum + (error2.severity || 0), 0);
    return totalSeverity / allErrors.length;
  }
  calculateImprovementTrend() {
    const errorsByTime = this.getErrorsByTimeSlices(5);
    if (errorsByTime.length < 2) return 0;
    let improvements = 0;
    for (let i = 1; i < errorsByTime.length; i++) {
      if (errorsByTime[i] < errorsByTime[i - 1]) improvements++;
    }
    return improvements / (errorsByTime.length - 1);
  }
  calculateLearningRate() {
    const totalTime = Date.now() - this.sessionStartTime;
    const totalErrors = this.getTotalErrorCount();
    if (totalTime === 0) return 0;
    return Math.max(0, 1 - totalErrors / (totalTime / 6e4));
  }
  getAllErrors() {
    const countingErrors = Object.values(this.errorData.countingErrors).flat();
    return [
      ...countingErrors,
      ...this.errorData.sequenceErrors,
      ...this.errorData.numberRecognitionErrors,
      ...this.errorData.quantityEstimationErrors
    ];
  }
  getTimeWindowErrors(windowMs, offsetMs = 0) {
    const now = Date.now();
    const startTime = now - windowMs - offsetMs;
    const endTime = now - offsetMs;
    return this.getAllErrors().filter((error2) => {
      const errorTime = new Date(error2.timestamp).getTime();
      return errorTime >= startTime && errorTime <= endTime;
    });
  }
  getErrorsByTimeSlices(slices) {
    const sessionDuration = Date.now() - this.sessionStartTime;
    const sliceDuration = sessionDuration / slices;
    const sliceErrors = [];
    for (let i = 0; i < slices; i++) {
      const sliceStart = this.sessionStartTime + i * sliceDuration;
      const sliceEnd = sliceStart + sliceDuration;
      const errorsInSlice = this.getAllErrors().filter((error2) => {
        const errorTime = new Date(error2.timestamp).getTime();
        return errorTime >= sliceStart && errorTime < sliceEnd;
      });
      sliceErrors.push(errorsInSlice.length);
    }
    return sliceErrors;
  }
  /**
   * Reset dos dados de erro
   */
  reset() {
    this.errorData = {
      countingErrors: {},
      sequenceErrors: [],
      numberRecognitionErrors: [],
      quantityEstimationErrors: [],
      persistentErrors: {},
      errorClusters: [],
      learningIndicators: [],
      mathematicalConcepts: {}
    };
    this.sessionStartTime = Date.now();
  }
  /**
   * Exporta dados para análise externa
   */
  exportData() {
    return {
      ...this.errorData,
      sessionDuration: Date.now() - this.sessionStartTime,
      analysis: this.analyzeErrorPatterns(),
      metrics: this.generateErrorMetrics()
    };
  }
}
class EstimationSkillsCollector {
  constructor() {
    this.estimationThresholds = {
      excellent: 0.95,
      // Estimativas quase perfeitas
      good: 0.85,
      // Boa capacidade de estimativa
      average: 0.7,
      // Estimativas razoáveis
      poor: 0.5,
      // Dificuldade em estimar
      critical: 0.3
      // Estimativas muito distantes
    };
    this.estimationSkills = {
      visualEstimation: "Estimativa visual de quantidades",
      numericalApproximation: "Aproximação numérica sem contagem",
      magnitudeComparison: "Comparação de magnitudes",
      quantityPerception: "Percepção de quantidade",
      spatialNumerosity: "Numerosidade espacial"
    };
    this.toleranceByDifficulty = {
      easy: 1,
      // ±1 para números de 1-5
      medium: 1,
      // ±1 para números de 3-8
      hard: 2
      // ±2 para números de 6-12
    };
  }
  /**
   * Método padronizado de coleta de dados
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Análise principal das habilidades de estimativa
   */
  async analyze(data) {
    if (!data || !data.numberEstimation) {
      console.warn("EstimationSkillsCollector: Dados de estimativa não encontrados");
      return this.getDefaultAnalysis();
    }
    const estimationData = data.numberEstimation;
    const estimationAccuracy = this.calculateEstimationAccuracy(estimationData);
    const errorPatterns = this.analyzeErrorPatterns(estimationData);
    const temporalProgress = this.analyzeTemporalProgress(estimationData);
    const estimationStrategies = this.detectEstimationStrategies(estimationData);
    const confidenceLevel = this.assessConfidenceLevel(estimationData);
    const skillIndex = this.calculateSkillIndex(estimationData);
    const analysis = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      collector: "EstimationSkillsCollector",
      version: "3.0.0",
      // Métricas principais
      estimationAccuracy,
      skillIndex,
      confidenceLevel,
      // Análises detalhadas
      errorPatterns,
      temporalProgress,
      estimationStrategies,
      // Habilidades específicas
      skillAssessment: {
        visualEstimation: this.assessVisualEstimation(estimationData),
        numericalApproximation: this.assessNumericalApproximation(estimationData),
        magnitudeComparison: this.assessMagnitudeComparison(estimationData),
        quantityPerception: this.assessQuantityPerception(estimationData),
        spatialNumerosity: this.assessSpatialNumerosity(estimationData)
      },
      // Recomendações
      recommendations: this.generateRecommendations(estimationData, skillIndex),
      // Metadados
      metadata: {
        totalAttempts: estimationData.length || 0,
        validAttempts: estimationData.filter((attempt) => attempt.isValid).length || 0,
        averageError: this.calculateAverageError(estimationData),
        maxError: this.calculateMaxError(estimationData),
        minError: this.calculateMinError(estimationData)
      }
    };
    return analysis;
  }
  /**
   * Calcular precisão das estimativas
   */
  calculateEstimationAccuracy(data) {
    if (!data || data.length === 0) return 0.5;
    let correctEstimations = 0;
    data.forEach((attempt) => {
      if (attempt.userEstimate && attempt.actualCount) {
        const error2 = Math.abs(attempt.userEstimate - attempt.actualCount);
        const tolerance = this.toleranceByDifficulty[attempt.difficulty] || 1;
        if (error2 <= tolerance) {
          correctEstimations++;
        }
      }
    });
    return data.length > 0 ? correctEstimations / data.length : 0.5;
  }
  /**
   * Analisar padrões de erro
   */
  analyzeErrorPatterns(data) {
    const errors = [];
    const biases = {
      overestimation: 0,
      // Tendência a superestimar
      underestimation: 0,
      // Tendência a subestimar
      systematic: false,
      // Erro sistemático
      random: false
      // Erro aleatório
    };
    data.forEach((attempt) => {
      if (attempt.userEstimate && attempt.actualCount) {
        const error2 = attempt.userEstimate - attempt.actualCount;
        errors.push(error2);
        if (error2 > 0) biases.overestimation++;
        else if (error2 < 0) biases.underestimation++;
      }
    });
    const totalAttempts = data.length;
    if (totalAttempts > 0) {
      const overestimationRate = biases.overestimation / totalAttempts;
      const underestimationRate = biases.underestimation / totalAttempts;
      biases.systematic = Math.max(overestimationRate, underestimationRate) > 0.7;
      biases.random = Math.abs(overestimationRate - underestimationRate) < 0.2;
    }
    return {
      averageError: errors.length > 0 ? errors.reduce((a, b) => a + b, 0) / errors.length : 0,
      errorVariance: this.calculateVariance(errors),
      biases,
      errorDistribution: this.analyzeErrorDistribution(errors)
    };
  }
  /**
   * Avaliar progresso temporal
   */
  analyzeTemporalProgress(data) {
    if (data.length < 3) return { trend: "insufficient_data", improvement: 0 };
    const firstHalf = data.slice(0, Math.floor(data.length / 2));
    const secondHalf = data.slice(Math.floor(data.length / 2));
    const firstAccuracy = this.calculateEstimationAccuracy(firstHalf);
    const secondAccuracy = this.calculateEstimationAccuracy(secondHalf);
    const improvement = secondAccuracy - firstAccuracy;
    let trend = "stable";
    if (improvement > 0.1) trend = "improving";
    else if (improvement < -0.1) trend = "declining";
    return {
      trend,
      improvement,
      firstHalfAccuracy: firstAccuracy,
      secondHalfAccuracy: secondAccuracy,
      consistencyScore: this.calculateConsistency(data)
    };
  }
  /**
   * Detectar estratégias de estimativa
   */
  detectEstimationStrategies(data) {
    const strategies = {
      visualChunking: false,
      // Agrupamento visual
      roundNumbers: false,
      // Uso de números redondos
      anchoring: false,
      // Ancoragem em números conhecidos
      systematicCounting: false
      // Contagem parcial sistemática
    };
    const roundNumberUsage = data.filter(
      (attempt) => attempt.userEstimate && attempt.userEstimate % 5 === 0
    ).length / data.length;
    strategies.roundNumbers = roundNumberUsage > 0.6;
    const estimates = data.map((attempt) => attempt.userEstimate).filter(Boolean);
    const mostCommonEstimate = this.findMostCommon(estimates);
    const anchoringRate = estimates.filter((est) => Math.abs(est - mostCommonEstimate) <= 2).length / estimates.length;
    strategies.anchoring = anchoringRate > 0.5;
    return strategies;
  }
  /**
   * Avaliar nível de confiança
   */
  assessConfidenceLevel(data) {
    const responseTimes = data.map((attempt) => attempt.responseTime).filter(Boolean);
    const averageTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length || 0;
    const consistency = this.calculateConsistency(data);
    const timeConfidence = averageTime < 5e3 ? 0.8 : averageTime < 8e3 ? 0.6 : 0.4;
    return (consistency + timeConfidence) / 2;
  }
  /**
   * Calcular índice de habilidade geral
   */
  calculateSkillIndex(data) {
    const accuracy = this.calculateEstimationAccuracy(data);
    const consistency = this.calculateConsistency(data);
    const confidence = this.assessConfidenceLevel(data);
    return accuracy * 0.5 + consistency * 0.3 + confidence * 0.2;
  }
  /**
   * Avaliar estimativa visual
   */
  assessVisualEstimation(data) {
    const visualAttempts = data.filter((attempt) => attempt.showTime <= 4e3);
    return this.calculateEstimationAccuracy(visualAttempts);
  }
  /**
   * Avaliar aproximação numérica
   */
  assessNumericalApproximation(data) {
    const approximationScore = data.reduce((score, attempt) => {
      if (attempt.userEstimate && attempt.actualCount) {
        const error2 = Math.abs(attempt.userEstimate - attempt.actualCount);
        const relativeError = error2 / attempt.actualCount;
        return score + Math.max(0, 1 - relativeError);
      }
      return score;
    }, 0);
    return data.length > 0 ? approximationScore / data.length : 0.5;
  }
  /**
   * Avaliar comparação de magnitudes
   */
  assessMagnitudeComparison(data) {
    const magnitudes = data.map((attempt) => attempt.actualCount).filter(Boolean);
    const uniqueMagnitudes = [...new Set(magnitudes)];
    if (uniqueMagnitudes.length < 2) return 0.5;
    const performanceByMagnitude = {};
    uniqueMagnitudes.forEach((magnitude) => {
      const attemptsForMagnitude = data.filter((attempt) => attempt.actualCount === magnitude);
      performanceByMagnitude[magnitude] = this.calculateEstimationAccuracy(attemptsForMagnitude);
    });
    const performances = Object.values(performanceByMagnitude);
    const averagePerformance = performances.reduce((a, b) => a + b, 0) / performances.length;
    return averagePerformance;
  }
  /**
   * Avaliar percepção de quantidade
   */
  assessQuantityPerception(data) {
    const quickResponses = data.filter((attempt) => attempt.responseTime < 3e3);
    return this.calculateEstimationAccuracy(quickResponses);
  }
  /**
   * Avaliar numerosidade espacial
   */
  assessSpatialNumerosity(data) {
    return this.calculateEstimationAccuracy(data);
  }
  /**
   * Gerar recomendações
   */
  generateRecommendations(data, skillIndex) {
    const recommendations = [];
    if (skillIndex < 0.6) {
      recommendations.push({
        type: "improvement",
        priority: "high",
        message: "Pratique mais atividades de estimativa com números menores",
        activities: ["number_estimation_easy", "visual_quantity_games"]
      });
    }
    const errorPatterns = this.analyzeErrorPatterns(data);
    if (errorPatterns.biases.overestimation > errorPatterns.biases.underestimation) {
      recommendations.push({
        type: "strategy",
        priority: "medium",
        message: "Tente olhar mais atentamente antes de estimar - você tende a superestimar",
        activities: ["careful_observation", "comparison_games"]
      });
    }
    if (this.assessConfidenceLevel(data) < 0.5) {
      recommendations.push({
        type: "confidence",
        priority: "medium",
        message: "Confie mais nas suas primeiras impressões",
        activities: ["quick_estimation", "intuitive_counting"]
      });
    }
    return recommendations;
  }
  /**
   * Funções auxiliares
   */
  calculateVariance(numbers) {
    if (numbers.length === 0) return 0;
    const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
    const variance = numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;
    return variance;
  }
  calculateConsistency(data) {
    const errors = data.map((attempt) => {
      if (attempt.userEstimate && attempt.actualCount) {
        return Math.abs(attempt.userEstimate - attempt.actualCount);
      }
      return null;
    }).filter(Boolean);
    if (errors.length === 0) return 0.5;
    const variance = this.calculateVariance(errors);
    return Math.max(0, 1 - variance / 10);
  }
  analyzeErrorDistribution(errors) {
    const distribution = { negative: 0, zero: 0, positive: 0 };
    errors.forEach((error2) => {
      if (error2 < 0) distribution.negative++;
      else if (error2 === 0) distribution.zero++;
      else distribution.positive++;
    });
    return distribution;
  }
  findMostCommon(array) {
    const frequency = {};
    array.forEach((item) => frequency[item] = (frequency[item] || 0) + 1);
    return Object.keys(frequency).reduce((a, b) => frequency[a] > frequency[b] ? a : b, 0);
  }
  calculateAverageError(data) {
    const errors = data.map((attempt) => {
      if (attempt.userEstimate && attempt.actualCount) {
        return Math.abs(attempt.userEstimate - attempt.actualCount);
      }
      return null;
    }).filter(Boolean);
    return errors.length > 0 ? errors.reduce((a, b) => a + b, 0) / errors.length : 0;
  }
  calculateMaxError(data) {
    const errors = data.map((attempt) => {
      if (attempt.userEstimate && attempt.actualCount) {
        return Math.abs(attempt.userEstimate - attempt.actualCount);
      }
      return null;
    }).filter(Boolean);
    return errors.length > 0 ? Math.max(...errors) : 0;
  }
  calculateMinError(data) {
    const errors = data.map((attempt) => {
      if (attempt.userEstimate && attempt.actualCount) {
        return Math.abs(attempt.userEstimate - attempt.actualCount);
      }
      return null;
    }).filter(Boolean);
    return errors.length > 0 ? Math.min(...errors) : 0;
  }
  getDefaultAnalysis() {
    return {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      collector: "EstimationSkillsCollector",
      version: "3.0.0",
      estimationAccuracy: 0.5,
      skillIndex: 0.5,
      confidenceLevel: 0.5,
      errorPatterns: { averageError: 0, errorVariance: 0, biases: {} },
      temporalProgress: { trend: "insufficient_data", improvement: 0 },
      estimationStrategies: {},
      skillAssessment: {},
      recommendations: [],
      metadata: { totalAttempts: 0, validAttempts: 0 }
    };
  }
}
class SequenceAnalysisCollector {
  constructor() {
    this.sequenceThresholds = {
      excellent: 0.95,
      good: 0.85,
      average: 0.7,
      poor: 0.5,
      critical: 0.3
    };
    this.sequenceTypes = {
      ascending: "Sequências crescentes simples",
      descending: "Sequências decrescentes",
      evenNumbers: "Sequências de números pares",
      oddNumbers: "Sequências de números ímpares",
      arithmetic: "Progressões aritméticas",
      geometric: "Progressões geométricas",
      fibonacci: "Sequências tipo Fibonacci",
      complex: "Padrões complexos"
    };
    this.cognitiveSkills = {
      patternRecognition: "Reconhecimento de padrões",
      logicalReasoning: "Raciocínio lógico",
      sequentialMemory: "Memória sequencial",
      abstractThinking: "Pensamento abstrato",
      ruleInference: "Inferência de regras"
    };
  }
  /**
   * Método padronizado de coleta de dados
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Análise principal das habilidades de sequência
   */
  async analyze(data) {
    if (!data || !data.sequenceCompletion) {
      console.warn("SequenceAnalysisCollector: Dados de sequência não encontrados");
      return this.getDefaultAnalysis();
    }
    const sequenceData = data.sequenceCompletion;
    const accuracyByType = this.analyzeAccuracyByType(sequenceData);
    const solvingStrategies = this.detectSolvingStrategies(sequenceData);
    const cognitiveComplexity = this.assessCognitiveComplexity(sequenceData);
    const temporalProgress = this.analyzeTemporalProgress(sequenceData);
    const errorPatterns = this.analyzeErrorPatterns(sequenceData);
    const processingSpeed = this.assessProcessingSpeed(sequenceData);
    const sequentialSkillIndex = this.calculateSequentialSkillIndex(sequenceData);
    const analysis = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      collector: "SequenceAnalysisCollector",
      version: "3.0.0",
      // Métricas principais
      overallAccuracy: this.calculateOverallAccuracy(sequenceData),
      sequentialSkillIndex,
      processingSpeed,
      // Análises detalhadas
      accuracyByType,
      solvingStrategies,
      cognitiveComplexity,
      temporalProgress,
      errorPatterns,
      // Habilidades cognitivas específicas
      cognitiveSkills: {
        patternRecognition: this.assessPatternRecognition(sequenceData),
        logicalReasoning: this.assessLogicalReasoning(sequenceData),
        sequentialMemory: this.assessSequentialMemory(sequenceData),
        abstractThinking: this.assessAbstractThinking(sequenceData),
        ruleInference: this.assessRuleInference(sequenceData)
      },
      // Análise de dificuldade
      difficultyAnalysis: this.analyzeDifficultyProgression(sequenceData),
      // Recomendações
      recommendations: this.generateRecommendations(sequenceData, sequentialSkillIndex),
      // Metadados
      metadata: {
        totalSequences: sequenceData.length || 0,
        uniqueTypes: this.countUniqueTypes(sequenceData),
        averageLength: this.calculateAverageLength(sequenceData),
        complexityDistribution: this.analyzeComplexityDistribution(sequenceData)
      }
    };
    return analysis;
  }
  /**
   * Analisar precisão por tipo de sequência
   */
  analyzeAccuracyByType(data) {
    const accuracyByType = {};
    const groupedData = this.groupBySequenceType(data);
    Object.keys(groupedData).forEach((type) => {
      const typeData = groupedData[type];
      const correct = typeData.filter((attempt) => attempt.isCorrect).length;
      accuracyByType[type] = {
        accuracy: typeData.length > 0 ? correct / typeData.length : 0,
        attempts: typeData.length,
        averageTime: this.calculateAverageTime(typeData),
        difficulty: this.assessTypeDifficulty(typeData)
      };
    });
    return accuracyByType;
  }
  /**
   * Detectar estratégias de resolução
   */
  detectSolvingStrategies(data) {
    const strategies = {
      immediateRecognition: false,
      // Reconhecimento imediato do padrão
      systematicAnalysis: false,
      // Análise sistemática passo a passo
      trialAndError: false,
      // Tentativa e erro
      ruleApplication: false,
      // Aplicação consciente de regras
      visualPattern: false
      // Reconhecimento visual de padrão
    };
    const quickCorrect = data.filter(
      (attempt) => attempt.isCorrect && attempt.responseTime < 3e3
    ).length;
    strategies.immediateRecognition = quickCorrect / data.length > 0.6;
    const systematicAttempts = data.filter(
      (attempt) => attempt.responseTime >= 3e3 && attempt.responseTime <= 8e3 && attempt.isCorrect
    ).length;
    strategies.systematicAnalysis = systematicAttempts / data.length > 0.5;
    strategies.trialAndError = this.detectTrialAndErrorPattern(data);
    return strategies;
  }
  /**
   * Avaliar complexidade cognitiva
   */
  assessCognitiveComplexity(data) {
    const complexityScores = data.map((attempt) => {
      let complexity = 0;
      if (attempt.sequenceType) {
        switch (attempt.sequenceType) {
          case "crescente_simples":
          case "decrescente_simples":
            complexity += 1;
            break;
          case "pares":
          case "ímpares":
            complexity += 2;
            break;
          case "múltiplos_3":
          case "soma_3":
            complexity += 3;
            break;
          case "fibonacci":
          case "multiplicação":
            complexity += 4;
            break;
          default:
            complexity += 2;
        }
      }
      if (attempt.sequenceLength) {
        complexity += Math.max(0, attempt.sequenceLength - 3);
      }
      if (attempt.maxNumber) {
        complexity += attempt.maxNumber > 10 ? 1 : 0;
      }
      return {
        attempt,
        complexity,
        solved: attempt.isCorrect
      };
    });
    return {
      averageComplexity: complexityScores.reduce((sum, item) => sum + item.complexity, 0) / complexityScores.length,
      complexityVsPerformance: this.analyzeComplexityVsPerformance(complexityScores),
      adaptiveCapacity: this.assessAdaptiveCapacity(complexityScores)
    };
  }
  /**
   * Analisar progresso temporal
   */
  analyzeTemporalProgress(data) {
    if (data.length < 3) return { trend: "insufficient_data", improvement: 0 };
    const segments = this.divideIntoSegments(data, 3);
    const segmentAccuracies = segments.map((segment) => this.calculateAccuracy(segment));
    const trend = this.calculateTrend(segmentAccuracies);
    const improvement = segmentAccuracies[segmentAccuracies.length - 1] - segmentAccuracies[0];
    return {
      trend,
      improvement,
      segmentAccuracies,
      learningRate: this.calculateLearningRate(segmentAccuracies),
      stability: this.calculateStability(segmentAccuracies)
    };
  }
  /**
   * Analisar padrões de erro
   */
  analyzeErrorPatterns(data) {
    const errors = data.filter((attempt) => !attempt.isCorrect);
    const errorTypes = {
      offByOne: 0,
      // Erro de ±1
      patternMisunderstanding: 0,
      // Não entendeu o padrão
      calculationError: 0,
      // Erro de cálculo
      randomGuess: 0
      // Resposta aleatória
    };
    errors.forEach((error2) => {
      if (error2.userAnswer && error2.correctAnswer) {
        const difference = Math.abs(error2.userAnswer - error2.correctAnswer);
        if (difference === 1) {
          errorTypes.offByOne++;
        } else if (difference > 5) {
          errorTypes.randomGuess++;
        } else {
          errorTypes.calculationError++;
        }
      }
    });
    return {
      totalErrors: errors.length,
      errorTypes,
      errorRate: data.length > 0 ? errors.length / data.length : 0,
      criticalErrors: this.identifyCriticalErrors(errors),
      errorProgression: this.analyzeErrorProgression(data)
    };
  }
  /**
   * Avaliar velocidade de processamento
   */
  assessProcessingSpeed(data) {
    const responseTimes = data.map((attempt) => attempt.responseTime).filter(Boolean);
    if (responseTimes.length === 0) return { speed: "unknown", score: 0.5 };
    const averageTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    const medianTime = this.calculateMedian(responseTimes);
    let speedCategory = "average";
    let speedScore = 0.5;
    if (averageTime < 4e3) {
      speedCategory = "fast";
      speedScore = 0.8;
    } else if (averageTime < 7e3) {
      speedCategory = "average";
      speedScore = 0.6;
    } else {
      speedCategory = "slow";
      speedScore = 0.4;
    }
    return {
      averageTime,
      medianTime,
      speedCategory,
      speedScore,
      consistency: this.calculateTimeConsistency(responseTimes)
    };
  }
  /**
   * Calcular índice de habilidade sequencial
   */
  calculateSequentialSkillIndex(data) {
    const accuracy = this.calculateOverallAccuracy(data);
    const speed = this.assessProcessingSpeed(data).speedScore;
    const complexity = this.assessCognitiveComplexity(data).adaptiveCapacity || 0.5;
    const consistency = this.calculateConsistency(data);
    return accuracy * 0.4 + speed * 0.2 + complexity * 0.2 + consistency * 0.2;
  }
  /**
   * Avaliar habilidades cognitivas específicas
   */
  assessPatternRecognition(data) {
    const quickCorrect = data.filter(
      (attempt) => attempt.isCorrect && attempt.responseTime < 5e3
    ).length;
    return data.length > 0 ? quickCorrect / data.length : 0.5;
  }
  assessLogicalReasoning(data) {
    const complexSequences = data.filter(
      (attempt) => attempt.sequenceType && ["múltiplos_3", "soma_3", "fibonacci", "multiplicação"].includes(attempt.sequenceType)
    );
    return this.calculateAccuracy(complexSequences);
  }
  assessSequentialMemory(data) {
    const longSequences = data.filter(
      (attempt) => attempt.sequenceLength && attempt.sequenceLength >= 4
    );
    return this.calculateAccuracy(longSequences);
  }
  assessAbstractThinking(data) {
    const abstractPatterns = data.filter(
      (attempt) => attempt.sequenceType && ["fibonacci", "multiplicação", "progressão_ímpar"].includes(attempt.sequenceType)
    );
    return this.calculateAccuracy(abstractPatterns);
  }
  assessRuleInference(data) {
    const inferenceScore = data.reduce((score, attempt, index) => {
      if (index === 0) return score;
      const similarPrevious = data.slice(0, index).filter(
        (prev) => prev.sequenceType === attempt.sequenceType
      );
      if (similarPrevious.length > 0 && attempt.isCorrect) {
        return score + 1;
      }
      return score;
    }, 0);
    return data.length > 1 ? inferenceScore / (data.length - 1) : 0.5;
  }
  /**
   * Gerar recomendações
   */
  generateRecommendations(data, skillIndex) {
    const recommendations = [];
    if (skillIndex < 0.6) {
      recommendations.push({
        type: "improvement",
        priority: "high",
        message: "Pratique sequências mais simples para fortalecer a base",
        activities: ["simple_sequences", "counting_games"]
      });
    }
    const accuracyByType = this.analyzeAccuracyByType(data);
    Object.keys(accuracyByType).forEach((type) => {
      if (accuracyByType[type].accuracy < 0.5 && accuracyByType[type].attempts >= 2) {
        recommendations.push({
          type: "specific_skill",
          priority: "medium",
          message: `Pratique mais sequências do tipo: ${this.sequenceTypes[type] || type}`,
          activities: [`${type}_practice`, "pattern_games"]
        });
      }
    });
    const processingSpeed = this.assessProcessingSpeed(data);
    if (processingSpeed.speedCategory === "slow") {
      recommendations.push({
        type: "speed",
        priority: "low",
        message: "Tente resolver mais rapidamente - confie na sua primeira impressão",
        activities: ["speed_sequences", "quick_pattern_games"]
      });
    }
    return recommendations;
  }
  /**
   * Funções auxiliares
   */
  calculateOverallAccuracy(data) {
    if (!data || data.length === 0) return 0;
    const correct = data.filter((attempt) => attempt.isCorrect).length;
    return correct / data.length;
  }
  groupBySequenceType(data) {
    const grouped = {};
    data.forEach((attempt) => {
      const type = attempt.sequenceType || "unknown";
      if (!grouped[type]) grouped[type] = [];
      grouped[type].push(attempt);
    });
    return grouped;
  }
  calculateAverageTime(data) {
    const times = data.map((attempt) => attempt.responseTime).filter(Boolean);
    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;
  }
  calculateAccuracy(data) {
    if (!data || data.length === 0) return 0;
    const correct = data.filter((attempt) => attempt.isCorrect).length;
    return correct / data.length;
  }
  calculateMedian(numbers) {
    const sorted = numbers.slice().sort((a, b) => a - b);
    const middle = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 ? (sorted[middle - 1] + sorted[middle]) / 2 : sorted[middle];
  }
  calculateConsistency(data) {
    if (data.length < 2) return 0.5;
    const accuracies = this.divideIntoSegments(data, 3).map((segment) => this.calculateAccuracy(segment));
    const variance = this.calculateVariance(accuracies);
    return Math.max(0, 1 - variance);
  }
  calculateVariance(numbers) {
    if (numbers.length === 0) return 0;
    const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
    return numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;
  }
  divideIntoSegments(data, numSegments) {
    const segmentSize = Math.floor(data.length / numSegments);
    const segments = [];
    for (let i = 0; i < numSegments; i++) {
      const start = i * segmentSize;
      const end = i === numSegments - 1 ? data.length : (i + 1) * segmentSize;
      segments.push(data.slice(start, end));
    }
    return segments.filter((segment) => segment.length > 0);
  }
  getDefaultAnalysis() {
    return {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      collector: "SequenceAnalysisCollector",
      version: "3.0.0",
      overallAccuracy: 0.5,
      sequentialSkillIndex: 0.5,
      processingSpeed: { speed: "unknown", score: 0.5 },
      accuracyByType: {},
      solvingStrategies: {},
      cognitiveComplexity: {},
      temporalProgress: { trend: "insufficient_data", improvement: 0 },
      errorPatterns: { totalErrors: 0, errorRate: 0 },
      cognitiveSkills: {},
      recommendations: [],
      metadata: { totalSequences: 0, uniqueTypes: 0 }
    };
  }
}
class ComparisonSkillsCollector {
  constructor() {
    this.comparisonThresholds = {
      excellent: 0.95,
      good: 0.85,
      average: 0.7,
      poor: 0.5,
      critical: 0.3
    };
    this.comparisonTypes = {
      magnitude: "Comparação de magnitude (maior/menor)",
      quantity: "Comparação de quantidades",
      difference: "Cálculo de diferenças",
      ordering: "Ordenação de números",
      relative: "Posicionamento relativo",
      range: "Comparação por faixas"
    };
    this.cognitiveSkills = {
      numberSense: "Senso numérico",
      spatialReasoning: "Raciocínio espacial",
      relationalThinking: "Pensamento relacional",
      magnitudeEstimation: "Estimativa de magnitude",
      comparativeAnalysis: "Análise comparativa"
    };
  }
  /**
   * Método padronizado de coleta de dados
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Análise principal das habilidades de comparação
   */
  async analyze(data) {
    if (!data || !data.numberComparison) {
      console.warn("ComparisonSkillsCollector: Dados de comparação não encontrados");
      return this.getDefaultAnalysis();
    }
    const comparisonData = data.numberComparison;
    const accuracyByType = this.analyzeAccuracyByType(comparisonData);
    const magnitudeDiscrimination = this.assessMagnitudeDiscrimination(comparisonData);
    const comparisonStrategies = this.analyzeComparisonStrategies(comparisonData);
    const processingSpeed = this.assessProcessingSpeed(comparisonData);
    const temporalProgress = this.analyzeTemporalProgress(comparisonData);
    const errorPatterns = this.analyzeErrorPatterns(comparisonData);
    const comparativeSkillIndex = this.calculateComparativeSkillIndex(comparisonData);
    const analysis = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      collector: "ComparisonSkillsCollector",
      version: "3.0.0",
      // Métricas principais
      overallAccuracy: this.calculateOverallAccuracy(comparisonData),
      comparativeSkillIndex,
      processingSpeed,
      // Análises detalhadas
      accuracyByType,
      magnitudeDiscrimination,
      comparisonStrategies,
      temporalProgress,
      errorPatterns,
      // Habilidades cognitivas específicas
      cognitiveSkills: {
        numberSense: this.assessNumberSense(comparisonData),
        spatialReasoning: this.assessSpatialReasoning(comparisonData),
        relationalThinking: this.assessRelationalThinking(comparisonData),
        magnitudeEstimation: this.assessMagnitudeEstimation(comparisonData),
        comparativeAnalysis: this.assessComparativeAnalysis(comparisonData)
      },
      // Análise de dificuldade
      difficultyAnalysis: this.analyzeDifficultyProgression(comparisonData),
      // Análise de precisão por faixa numérica
      rangeAccuracy: this.analyzeRangeAccuracy(comparisonData),
      // Recomendações
      recommendations: this.generateRecommendations(comparisonData, comparativeSkillIndex),
      // Metadados
      metadata: {
        totalComparisons: comparisonData.length || 0,
        uniqueTypes: this.countUniqueTypes(comparisonData),
        averageDifference: this.calculateAverageDifference(comparisonData),
        rangeDistribution: this.analyzeRangeDistribution(comparisonData)
      }
    };
    return analysis;
  }
  /**
   * Analisar precisão por tipo de comparação
   */
  analyzeAccuracyByType(data) {
    const accuracyByType = {};
    const groupedData = this.groupByComparisonType(data);
    Object.keys(groupedData).forEach((type) => {
      const typeData = groupedData[type];
      const correct = typeData.filter((attempt) => attempt.isCorrect).length;
      accuracyByType[type] = {
        accuracy: typeData.length > 0 ? correct / typeData.length : 0,
        attempts: typeData.length,
        averageTime: this.calculateAverageTime(typeData),
        averageDifference: this.calculateAverageDifference(typeData),
        difficulty: this.assessTypeDifficulty(typeData)
      };
    });
    return accuracyByType;
  }
  /**
   * Avaliar discriminação de magnitude
   */
  assessMagnitudeDiscrimination(data) {
    const difficultyLevels = {
      easy: [],
      // diferença > 5
      medium: [],
      // diferença 3-5
      hard: [],
      // diferença 1-2
      veryHard: []
      // diferença < 1
    };
    data.forEach((attempt) => {
      const diff = Math.abs(attempt.number1 - attempt.number2);
      if (diff > 5) difficultyLevels.easy.push(attempt);
      else if (diff >= 3) difficultyLevels.medium.push(attempt);
      else if (diff >= 1) difficultyLevels.hard.push(attempt);
      else difficultyLevels.veryHard.push(attempt);
    });
    const discrimination = {};
    Object.keys(difficultyLevels).forEach((level) => {
      const levelData = difficultyLevels[level];
      discrimination[level] = {
        accuracy: this.calculateAccuracy(levelData),
        averageTime: this.calculateAverageTime(levelData),
        attempts: levelData.length
      };
    });
    return {
      byDifficulty: discrimination,
      discriminationThreshold: this.calculateDiscriminationThreshold(data),
      weberFraction: this.calculateWeberFraction(data)
    };
  }
  /**
   * Analisar estratégias de comparação
   */
  analyzeComparisonStrategies(data) {
    const strategies = {
      immediateComparison: false,
      // Comparação imediata visual
      countingStrategy: false,
      // Estratégia de contagem
      magnitudeEstimation: false,
      // Estimativa de magnitude
      digitalComparison: false,
      // Comparação dígito por dígito
      patternRecognition: false
      // Reconhecimento de padrões
    };
    const quickCorrect = data.filter(
      (attempt) => attempt.isCorrect && attempt.responseTime < 2e3
    ).length;
    strategies.immediateComparison = quickCorrect / data.length > 0.6;
    const timeVsDifference = this.analyzeTimeVsDifference(data);
    strategies.countingStrategy = timeVsDifference.correlation > 0.7;
    strategies.magnitudeEstimation = this.detectMagnitudeStrategy(data);
    strategies.digitalComparison = this.detectDigitalStrategy(data);
    return {
      strategies,
      dominantStrategy: this.identifyDominantStrategy(strategies),
      strategyEffectiveness: this.assessStrategyEffectiveness(data, strategies)
    };
  }
  /**
   * Avaliar velocidade de processamento
   */
  assessProcessingSpeed(data) {
    const responseTimes = data.map((attempt) => attempt.responseTime).filter(Boolean);
    if (responseTimes.length === 0) return { speed: "unknown", score: 0.5 };
    const averageTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    const medianTime = this.calculateMedian(responseTimes);
    let speedCategory = "average";
    let speedScore = 0.5;
    if (averageTime < 2e3) {
      speedCategory = "very_fast";
      speedScore = 0.9;
    } else if (averageTime < 3e3) {
      speedCategory = "fast";
      speedScore = 0.8;
    } else if (averageTime < 5e3) {
      speedCategory = "average";
      speedScore = 0.6;
    } else {
      speedCategory = "slow";
      speedScore = 0.4;
    }
    return {
      averageTime,
      medianTime,
      speedCategory,
      speedScore,
      consistency: this.calculateTimeConsistency(responseTimes),
      speedVsAccuracy: this.analyzeSpeedVsAccuracy(data)
    };
  }
  /**
   * Analisar progresso temporal
   */
  analyzeTemporalProgress(data) {
    if (data.length < 3) return { trend: "insufficient_data", improvement: 0 };
    const segments = this.divideIntoSegments(data, 3);
    const segmentAccuracies = segments.map((segment) => this.calculateAccuracy(segment));
    const segmentTimes = segments.map((segment) => this.calculateAverageTime(segment));
    const accuracyTrend = this.calculateTrend(segmentAccuracies);
    const speedTrend = this.calculateTrend(segmentTimes.map((time) => 1 / time));
    return {
      accuracyTrend,
      speedTrend,
      improvement: segmentAccuracies[segmentAccuracies.length - 1] - segmentAccuracies[0],
      segmentAccuracies,
      segmentTimes,
      learningRate: this.calculateLearningRate(segmentAccuracies),
      stability: this.calculateStability(segmentAccuracies)
    };
  }
  /**
   * Analisar padrões de erro
   */
  analyzeErrorPatterns(data) {
    const errors = data.filter((attempt) => !attempt.isCorrect);
    const errorTypes = {
      magnitudeConfusion: 0,
      // Confusão de magnitude
      digitalError: 0,
      // Erro na comparação de dígitos
      inverseComparison: 0,
      // Comparação invertida
      proximityError: 0,
      // Erro em números próximos
      randomError: 0
      // Erro aparentemente aleatório
    };
    errors.forEach((error2) => {
      if (error2.userAnswer && error2.correctAnswer) {
        const difference = Math.abs(error2.number1 - error2.number2);
        if (difference <= 2) {
          errorTypes.proximityError++;
        } else if (this.isInverseComparison(error2)) {
          errorTypes.inverseComparison++;
        } else if (this.isDigitalError(error2)) {
          errorTypes.digitalError++;
        } else if (difference > 10) {
          errorTypes.magnitudeConfusion++;
        } else {
          errorTypes.randomError++;
        }
      }
    });
    return {
      totalErrors: errors.length,
      errorTypes,
      errorRate: data.length > 0 ? errors.length / data.length : 0,
      criticalErrors: this.identifyCriticalErrors(errors),
      errorProgression: this.analyzeErrorProgression(data),
      errorsByDifficulty: this.analyzeErrorsByDifficulty(data)
    };
  }
  /**
   * Calcular índice de habilidade comparativa
   */
  calculateComparativeSkillIndex(data) {
    const accuracy = this.calculateOverallAccuracy(data);
    const speed = this.assessProcessingSpeed(data).speedScore;
    const discrimination = this.assessMagnitudeDiscrimination(data);
    const discriminationScore = this.calculateDiscriminationScore(discrimination);
    const consistency = this.calculateConsistency(data);
    return accuracy * 0.4 + speed * 0.2 + discriminationScore * 0.2 + consistency * 0.2;
  }
  /**
   * Avaliar habilidades cognitivas específicas
   */
  assessNumberSense(data) {
    const overallAccuracy = this.calculateOverallAccuracy(data);
    const speedScore = this.assessProcessingSpeed(data).speedScore;
    return overallAccuracy * 0.7 + speedScore * 0.3;
  }
  assessSpatialReasoning(data) {
    const quickComparisons = data.filter(
      (attempt) => attempt.isCorrect && attempt.responseTime < 3e3
    ).length;
    return data.length > 0 ? quickComparisons / data.length : 0.5;
  }
  assessRelationalThinking(data) {
    const complexComparisons = data.filter(
      (attempt) => attempt.comparisonType && ["relative_position", "range_comparison"].includes(attempt.comparisonType)
    );
    return this.calculateAccuracy(complexComparisons);
  }
  assessMagnitudeEstimation(data) {
    const discrimination = this.assessMagnitudeDiscrimination(data);
    return this.calculateDiscriminationScore(discrimination);
  }
  assessComparativeAnalysis(data) {
    const multiStepComparisons = data.filter(
      (attempt) => attempt.requiresMultipleSteps || attempt.number1 > 20 && attempt.number2 > 20
    );
    return this.calculateAccuracy(multiStepComparisons);
  }
  /**
   * Analisar precisão por faixa numérica
   */
  analyzeRangeAccuracy(data) {
    const ranges = {
      small: [],
      // 1-10
      medium: [],
      // 11-50
      large: [],
      // 51-100
      veryLarge: []
      // >100
    };
    data.forEach((attempt) => {
      const maxNum = Math.max(attempt.number1, attempt.number2);
      if (maxNum <= 10) ranges.small.push(attempt);
      else if (maxNum <= 50) ranges.medium.push(attempt);
      else if (maxNum <= 100) ranges.large.push(attempt);
      else ranges.veryLarge.push(attempt);
    });
    const rangeAccuracy = {};
    Object.keys(ranges).forEach((range) => {
      const rangeData = ranges[range];
      rangeAccuracy[range] = {
        accuracy: this.calculateAccuracy(rangeData),
        averageTime: this.calculateAverageTime(rangeData),
        attempts: rangeData.length
      };
    });
    return rangeAccuracy;
  }
  /**
   * Gerar recomendações
   */
  generateRecommendations(data, skillIndex) {
    const recommendations = [];
    if (skillIndex < 0.6) {
      recommendations.push({
        type: "improvement",
        priority: "high",
        message: "Pratique comparações simples com números pequenos",
        activities: ["simple_comparisons", "number_line_games"]
      });
    }
    const magnitudeDiscrimination = this.assessMagnitudeDiscrimination(data);
    if (magnitudeDiscrimination.byDifficulty.hard.accuracy < 0.5) {
      recommendations.push({
        type: "specific_skill",
        priority: "high",
        message: "Trabalhe discriminação de números próximos",
        activities: ["close_numbers_practice", "magnitude_games"]
      });
    }
    const processingSpeed = this.assessProcessingSpeed(data);
    if (processingSpeed.speedCategory === "slow") {
      recommendations.push({
        type: "speed",
        priority: "medium",
        message: "Pratique comparações rápidas para desenvolver automatismo",
        activities: ["speed_comparisons", "quick_decision_games"]
      });
    }
    const rangeAccuracy = this.analyzeRangeAccuracy(data);
    Object.keys(rangeAccuracy).forEach((range) => {
      if (rangeAccuracy[range].accuracy < 0.6 && rangeAccuracy[range].attempts >= 2) {
        recommendations.push({
          type: "range_specific",
          priority: "medium",
          message: `Pratique mais comparações na faixa ${range}`,
          activities: [`${range}_range_practice`, "graduated_difficulty"]
        });
      }
    });
    return recommendations;
  }
  /**
   * Funções auxiliares
   */
  calculateOverallAccuracy(data) {
    if (!data || data.length === 0) return 0;
    const correct = data.filter((attempt) => attempt.isCorrect).length;
    return correct / data.length;
  }
  groupByComparisonType(data) {
    const grouped = {};
    data.forEach((attempt) => {
      const type = attempt.comparisonType || "magnitude";
      if (!grouped[type]) grouped[type] = [];
      grouped[type].push(attempt);
    });
    return grouped;
  }
  calculateAverageTime(data) {
    const times = data.map((attempt) => attempt.responseTime).filter(Boolean);
    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;
  }
  calculateAverageDifference(data) {
    const differences = data.map(
      (attempt) => Math.abs(attempt.number1 - attempt.number2)
    ).filter(Boolean);
    return differences.length > 0 ? differences.reduce((a, b) => a + b, 0) / differences.length : 0;
  }
  calculateAccuracy(data) {
    if (!data || data.length === 0) return 0;
    const correct = data.filter((attempt) => attempt.isCorrect).length;
    return correct / data.length;
  }
  calculateMedian(numbers) {
    const sorted = numbers.slice().sort((a, b) => a - b);
    const middle = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 ? (sorted[middle - 1] + sorted[middle]) / 2 : sorted[middle];
  }
  calculateDiscriminationScore(discrimination) {
    const scores = Object.values(discrimination.byDifficulty).map((level) => level.accuracy);
    return scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0.5;
  }
  calculateConsistency(data) {
    if (data.length < 2) return 0.5;
    const accuracies = this.divideIntoSegments(data, 3).map((segment) => this.calculateAccuracy(segment));
    const variance = this.calculateVariance(accuracies);
    return Math.max(0, 1 - variance);
  }
  calculateVariance(numbers) {
    if (numbers.length === 0) return 0;
    const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
    return numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;
  }
  divideIntoSegments(data, numSegments) {
    const segmentSize = Math.floor(data.length / numSegments);
    const segments = [];
    for (let i = 0; i < numSegments; i++) {
      const start = i * segmentSize;
      const end = i === numSegments - 1 ? data.length : (i + 1) * segmentSize;
      segments.push(data.slice(start, end));
    }
    return segments.filter((segment) => segment.length > 0);
  }
  getDefaultAnalysis() {
    return {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      collector: "ComparisonSkillsCollector",
      version: "3.0.0",
      overallAccuracy: 0.5,
      comparativeSkillIndex: 0.5,
      processingSpeed: { speed: "unknown", score: 0.5 },
      accuracyByType: {},
      magnitudeDiscrimination: {},
      comparisonStrategies: {},
      temporalProgress: { trend: "insufficient_data", improvement: 0 },
      errorPatterns: { totalErrors: 0, errorRate: 0 },
      cognitiveSkills: {},
      recommendations: [],
      metadata: { totalComparisons: 0, uniqueTypes: 0 }
    };
  }
}
class SoundMatchingCollector {
  constructor() {
    this.soundThresholds = {
      excellent: 0.95,
      good: 0.85,
      average: 0.7,
      poor: 0.5,
      critical: 0.3
    };
    this.soundTypes = {
      quantity: "Correspondência quantidade-som",
      sequence: "Sequência sonora",
      pattern: "Padrão rítmico",
      recognition: "Reconhecimento auditivo",
      memory: "Memória auditiva"
    };
    this.cognitiveSkills = {
      auditoryProcessing: "Processamento auditivo",
      auditoryMemory: "Memória auditiva",
      audioVisualIntegration: "Integração audiovisual",
      sequentialProcessing: "Processamento sequencial",
      rhythmicPatterns: "Padrões rítmicos"
    };
  }
  /**
   * Método padronizado de coleta de dados
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Análise principal das habilidades de correspondência som-número
   */
  async analyze(data) {
    if (!data || !data.soundMatching) {
      console.warn("SoundMatchingCollector: Dados de correspondência sonora não encontrados");
      return this.getDefaultAnalysis();
    }
    const soundData = data.soundMatching;
    const accuracyBySoundType = this.analyzeAccuracyBySoundType(soundData);
    const auditoryMemory = this.assessAuditoryMemory(soundData);
    const audioVisualIntegration = this.analyzeAudioVisualIntegration(soundData);
    const auditoryProcessingSpeed = this.assessAuditoryProcessingSpeed(soundData);
    const temporalProgress = this.analyzeTemporalProgress(soundData);
    const auditoryErrorPatterns = this.analyzeAuditoryErrorPatterns(soundData);
    const auditorySkillIndex = this.calculateAuditorySkillIndex(soundData);
    const analysis = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      collector: "SoundMatchingCollector",
      version: "3.0.0",
      // Métricas principais
      overallAccuracy: this.calculateOverallAccuracy(soundData),
      auditorySkillIndex,
      auditoryProcessingSpeed,
      // Análises detalhadas
      accuracyBySoundType,
      auditoryMemory,
      audioVisualIntegration,
      temporalProgress,
      auditoryErrorPatterns,
      // Habilidades cognitivas específicas
      cognitiveSkills: {
        auditoryProcessing: this.assessAuditoryProcessing(soundData),
        auditoryMemory: this.assessAuditoryMemorySkill(soundData),
        audioVisualIntegration: this.assessAudioVisualIntegrationSkill(soundData),
        sequentialProcessing: this.assessSequentialProcessing(soundData),
        rhythmicPatterns: this.assessRhythmicPatterns(soundData)
      },
      // Análise de dificuldade auditiva
      auditoryDifficultyAnalysis: this.analyzeAuditoryDifficultyProgression(soundData),
      // Análise de latência de resposta
      responseLatency: this.analyzeResponseLatency(soundData),
      // Recomendações
      recommendations: this.generateRecommendations(soundData, auditorySkillIndex),
      // Metadados
      metadata: {
        totalSounds: soundData.length || 0,
        uniqueSoundTypes: this.countUniqueSoundTypes(soundData),
        averageSoundDuration: this.calculateAverageSoundDuration(soundData),
        soundComplexityDistribution: this.analyzeSoundComplexityDistribution(soundData)
      }
    };
    return analysis;
  }
  /**
   * Analisar precisão por tipo de som
   */
  analyzeAccuracyBySoundType(data) {
    const accuracyBySoundType = {};
    const groupedData = this.groupBySoundType(data);
    Object.keys(groupedData).forEach((type) => {
      const typeData = groupedData[type];
      const correct = typeData.filter((attempt) => attempt.isCorrect).length;
      accuracyBySoundType[type] = {
        accuracy: typeData.length > 0 ? correct / typeData.length : 0,
        attempts: typeData.length,
        averageResponseTime: this.calculateAverageTime(typeData),
        averageLatency: this.calculateAverageLatency(typeData),
        difficulty: this.assessSoundTypeDifficulty(typeData)
      };
    });
    return accuracyBySoundType;
  }
  /**
   * Avaliar capacidade de memória auditiva
   */
  assessAuditoryMemory(data) {
    const memoryLevels = {
      immediate: [],
      // resposta imediata (< 2s após som)
      shortTerm: [],
      // resposta rápida (2-5s)
      mediumTerm: [],
      // resposta normal (5-10s)
      longTerm: []
      // resposta demorada (> 10s)
    };
    data.forEach((attempt) => {
      const latency = attempt.responseTime - (attempt.soundDuration || 1e3);
      if (latency < 2e3) memoryLevels.immediate.push(attempt);
      else if (latency < 5e3) memoryLevels.shortTerm.push(attempt);
      else if (latency < 1e4) memoryLevels.mediumTerm.push(attempt);
      else memoryLevels.longTerm.push(attempt);
    });
    const memoryAnalysis = {};
    Object.keys(memoryLevels).forEach((level) => {
      const levelData = memoryLevels[level];
      memoryAnalysis[level] = {
        accuracy: this.calculateAccuracy(levelData),
        averageTime: this.calculateAverageTime(levelData),
        attempts: levelData.length
      };
    });
    return {
      byLatency: memoryAnalysis,
      memorySpan: this.calculateMemorySpan(data),
      retentionRate: this.calculateRetentionRate(data),
      decayPattern: this.analyzeDecayPattern(data)
    };
  }
  /**
   * Analisar integração audiovisual
   */
  analyzeAudioVisualIntegration(data) {
    const integration = {
      synchronization: 0,
      // Sincronização som-visual
      crossModalAccuracy: 0,
      // Precisão cross-modal
      interferenceResistance: 0,
      // Resistência a interferência
      modalityPreference: null
      // Preferência de modalidade
    };
    const audioOnlyTrials = data.filter((attempt) => !attempt.hasVisualStimulus);
    const audioVisualTrials = data.filter((attempt) => attempt.hasVisualStimulus);
    const audioOnlyAccuracy = this.calculateAccuracy(audioOnlyTrials);
    const audioVisualAccuracy = this.calculateAccuracy(audioVisualTrials);
    integration.crossModalAccuracy = audioVisualAccuracy;
    integration.interferenceResistance = audioVisualAccuracy / Math.max(audioOnlyAccuracy, 0.1);
    integration.modalityPreference = audioOnlyAccuracy > audioVisualAccuracy ? "auditory" : "visual";
    integration.synchronization = this.analyzeSynchronization(data);
    return integration;
  }
  /**
   * Avaliar velocidade de processamento auditivo
   */
  assessAuditoryProcessingSpeed(data) {
    const responseTimes = data.map((attempt) => attempt.responseTime).filter(Boolean);
    const soundDurations = data.map((attempt) => attempt.soundDuration || 1e3).filter(Boolean);
    if (responseTimes.length === 0) return { speed: "unknown", score: 0.5 };
    const latencies = data.map(
      (attempt, index) => attempt.responseTime - soundDurations[index]
    ).filter((latency) => latency > 0);
    const averageLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
    const medianLatency = this.calculateMedian(latencies);
    let speedCategory = "average";
    let speedScore = 0.5;
    if (averageLatency < 1e3) {
      speedCategory = "very_fast";
      speedScore = 0.9;
    } else if (averageLatency < 2e3) {
      speedCategory = "fast";
      speedScore = 0.8;
    } else if (averageLatency < 4e3) {
      speedCategory = "average";
      speedScore = 0.6;
    } else {
      speedCategory = "slow";
      speedScore = 0.4;
    }
    return {
      averageLatency,
      medianLatency,
      speedCategory,
      speedScore,
      consistency: this.calculateLatencyConsistency(latencies),
      processingEfficiency: this.calculateProcessingEfficiency(data)
    };
  }
  /**
   * Analisar progresso temporal
   */
  analyzeTemporalProgress(data) {
    if (data.length < 3) return { trend: "insufficient_data", improvement: 0 };
    const segments = this.divideIntoSegments(data, 3);
    const segmentAccuracies = segments.map((segment) => this.calculateAccuracy(segment));
    const segmentLatencies = segments.map((segment) => this.calculateAverageLatency(segment));
    const accuracyTrend = this.calculateTrend(segmentAccuracies);
    const speedTrend = this.calculateTrend(segmentLatencies.map((latency) => 1 / latency));
    return {
      accuracyTrend,
      speedTrend,
      improvement: segmentAccuracies[segmentAccuracies.length - 1] - segmentAccuracies[0],
      segmentAccuracies,
      segmentLatencies,
      auditoryLearningRate: this.calculateLearningRate(segmentAccuracies),
      auditoryStability: this.calculateStability(segmentAccuracies)
    };
  }
  /**
   * Analisar padrões de erro auditivo
   */
  analyzeAuditoryErrorPatterns(data) {
    const errors = data.filter((attempt) => !attempt.isCorrect);
    const errorTypes = {
      quantityMiscount: 0,
      // Erro na contagem de sons
      sequenceConfusion: 0,
      // Confusão na sequência
      memoryFailure: 0,
      // Falha de memória auditiva
      attentionLapse: 0,
      // Lapso de atenção
      processingDelay: 0
      // Atraso no processamento
    };
    errors.forEach((error2) => {
      const latency = error2.responseTime - (error2.soundDuration || 1e3);
      if (latency < 500) {
        errorTypes.attentionLapse++;
      } else if (latency > 1e4) {
        errorTypes.memoryFailure++;
      } else if (error2.soundType === "sequence") {
        errorTypes.sequenceConfusion++;
      } else if (error2.soundType === "quantity") {
        errorTypes.quantityMiscount++;
      } else {
        errorTypes.processingDelay++;
      }
    });
    return {
      totalErrors: errors.length,
      errorTypes,
      errorRate: data.length > 0 ? errors.length / data.length : 0,
      criticalErrors: this.identifyCriticalAuditoryErrors(errors),
      errorProgression: this.analyzeErrorProgression(data),
      auditoryErrorsByComplexity: this.analyzeErrorsByComplexity(data)
    };
  }
  /**
   * Calcular índice de habilidade auditiva
   */
  calculateAuditorySkillIndex(data) {
    const accuracy = this.calculateOverallAccuracy(data);
    const speed = this.assessAuditoryProcessingSpeed(data).speedScore;
    const memory = this.assessAuditoryMemory(data);
    const memoryScore = this.calculateMemoryScore(memory);
    const integration = this.analyzeAudioVisualIntegration(data);
    const integrationScore = integration.crossModalAccuracy;
    const consistency = this.calculateConsistency(data);
    return accuracy * 0.3 + speed * 0.2 + memoryScore * 0.2 + integrationScore * 0.15 + consistency * 0.15;
  }
  /**
   * Avaliar habilidades cognitivas específicas
   */
  assessAuditoryProcessing(data) {
    const overallAccuracy = this.calculateOverallAccuracy(data);
    const speedScore = this.assessAuditoryProcessingSpeed(data).speedScore;
    return overallAccuracy * 0.7 + speedScore * 0.3;
  }
  assessAuditoryMemorySkill(data) {
    const memory = this.assessAuditoryMemory(data);
    return this.calculateMemoryScore(memory);
  }
  assessAudioVisualIntegrationSkill(data) {
    const integration = this.analyzeAudioVisualIntegration(data);
    return integration.crossModalAccuracy;
  }
  assessSequentialProcessing(data) {
    const sequenceTrials = data.filter(
      (attempt) => attempt.soundType && attempt.soundType.includes("sequence")
    );
    return this.calculateAccuracy(sequenceTrials);
  }
  assessRhythmicPatterns(data) {
    const rhythmTrials = data.filter(
      (attempt) => attempt.soundType && attempt.soundType.includes("rhythm")
    );
    return this.calculateAccuracy(rhythmTrials);
  }
  /**
   * Analisar latência de resposta
   */
  analyzeResponseLatency(data) {
    const latencies = data.map(
      (attempt) => attempt.responseTime - (attempt.soundDuration || 1e3)
    ).filter((latency) => latency > 0);
    if (latencies.length === 0) return { status: "no_data" };
    return {
      averageLatency: latencies.reduce((a, b) => a + b, 0) / latencies.length,
      medianLatency: this.calculateMedian(latencies),
      latencyVariability: this.calculateVariance(latencies),
      optimalRange: this.calculateOptimalLatencyRange(data),
      latencyDistribution: this.analyzeLatencyDistribution(latencies)
    };
  }
  /**
   * Gerar recomendações
   */
  generateRecommendations(data, skillIndex) {
    const recommendations = [];
    if (skillIndex < 0.6) {
      recommendations.push({
        type: "improvement",
        priority: "high",
        message: "Pratique exercícios básicos de correspondência som-número",
        activities: ["simple_sound_counting", "audio_visual_matching"]
      });
    }
    const memory = this.assessAuditoryMemory(data);
    if (this.calculateMemoryScore(memory) < 0.5) {
      recommendations.push({
        type: "memory",
        priority: "high",
        message: "Trabalhe memória auditiva com sequências progressivamente mais longas",
        activities: ["memory_sequences", "auditory_span_games"]
      });
    }
    const processing = this.assessAuditoryProcessingSpeed(data);
    if (processing.speedCategory === "slow") {
      recommendations.push({
        type: "speed",
        priority: "medium",
        message: "Pratique respostas mais rápidas a estímulos auditivos",
        activities: ["speed_sound_games", "reaction_time_training"]
      });
    }
    const integration = this.analyzeAudioVisualIntegration(data);
    if (integration.crossModalAccuracy < 0.6) {
      recommendations.push({
        type: "integration",
        priority: "medium",
        message: "Desenvolva integração audiovisual com exercícios multimodais",
        activities: ["cross_modal_games", "synchronization_training"]
      });
    }
    return recommendations;
  }
  /**
   * Funções auxiliares
   */
  calculateOverallAccuracy(data) {
    if (!data || data.length === 0) return 0;
    const correct = data.filter((attempt) => attempt.isCorrect).length;
    return correct / data.length;
  }
  groupBySoundType(data) {
    const grouped = {};
    data.forEach((attempt) => {
      const type = attempt.soundType || "quantity";
      if (!grouped[type]) grouped[type] = [];
      grouped[type].push(attempt);
    });
    return grouped;
  }
  calculateAverageTime(data) {
    const times = data.map((attempt) => attempt.responseTime).filter(Boolean);
    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;
  }
  calculateAverageLatency(data) {
    const latencies = data.map(
      (attempt) => attempt.responseTime - (attempt.soundDuration || 1e3)
    ).filter((latency) => latency > 0);
    return latencies.length > 0 ? latencies.reduce((a, b) => a + b, 0) / latencies.length : 0;
  }
  calculateAccuracy(data) {
    if (!data || data.length === 0) return 0;
    const correct = data.filter((attempt) => attempt.isCorrect).length;
    return correct / data.length;
  }
  calculateMedian(numbers) {
    const sorted = numbers.slice().sort((a, b) => a - b);
    const middle = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 ? (sorted[middle - 1] + sorted[middle]) / 2 : sorted[middle];
  }
  calculateMemoryScore(memory) {
    const scores = Object.values(memory.byLatency).map((level) => level.accuracy);
    return scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0.5;
  }
  calculateConsistency(data) {
    if (data.length < 2) return 0.5;
    const accuracies = this.divideIntoSegments(data, 3).map((segment) => this.calculateAccuracy(segment));
    const variance = this.calculateVariance(accuracies);
    return Math.max(0, 1 - variance);
  }
  calculateVariance(numbers) {
    if (numbers.length === 0) return 0;
    const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
    return numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;
  }
  divideIntoSegments(data, numSegments) {
    const segmentSize = Math.floor(data.length / numSegments);
    const segments = [];
    for (let i = 0; i < numSegments; i++) {
      const start = i * segmentSize;
      const end = i === numSegments - 1 ? data.length : (i + 1) * segmentSize;
      segments.push(data.slice(start, end));
    }
    return segments.filter((segment) => segment.length > 0);
  }
  getDefaultAnalysis() {
    return {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      collector: "SoundMatchingCollector",
      version: "3.0.0",
      overallAccuracy: 0.5,
      auditorySkillIndex: 0.5,
      auditoryProcessingSpeed: { speed: "unknown", score: 0.5 },
      accuracyBySoundType: {},
      auditoryMemory: {},
      audioVisualIntegration: {},
      temporalProgress: { trend: "insufficient_data", improvement: 0 },
      auditoryErrorPatterns: { totalErrors: 0, errorRate: 0 },
      cognitiveSkills: {},
      recommendations: [],
      metadata: { totalSounds: 0, uniqueSoundTypes: 0 }
    };
  }
}
class PatternRecognitionCollector {
  constructor() {
    this.patternThresholds = {
      excellent: 0.95,
      good: 0.85,
      average: 0.7,
      poor: 0.5,
      critical: 0.3
    };
    this.patternTypes = {
      sequential: "Padrões sequenciais (1,2,3...)",
      arithmetic: "Progressões aritméticas (+2, +3...)",
      geometric: "Progressões geométricas (×2, ×3...)",
      visual: "Padrões visuais e espaciais",
      repetitive: "Padrões repetitivos (A,B,A,B...)",
      complex: "Padrões complexos e mistos"
    };
    this.cognitiveSkills = {
      patternRecognition: "Reconhecimento de padrões",
      sequentialThinking: "Pensamento sequencial",
      logicalReasoning: "Raciocínio lógico",
      predictionAbility: "Capacidade de predição",
      abstractThinking: "Pensamento abstrato"
    };
  }
  /**
   * Método padronizado de coleta de dados
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Análise principal do reconhecimento de padrões
   */
  async analyze(data) {
    if (!data || !data.patternRecognition) {
      console.warn("PatternRecognitionCollector: Dados de reconhecimento de padrões não encontrados");
      return this.getDefaultAnalysis();
    }
    const patternData = data.patternRecognition;
    const accuracyByType = this.analyzeAccuracyByType(patternData);
    const recognitionSpeed = this.assessRecognitionSpeed(patternData);
    const complexityAnalysis = this.analyzeComplexityHandling(patternData);
    const predictionAbility = this.assessPredictionAbility(patternData);
    const temporalProgress = this.analyzeTemporalProgress(patternData);
    const errorPatterns = this.analyzeErrorPatterns(patternData);
    const patternRecognitionIndex = this.calculatePatternRecognitionIndex(patternData);
    const analysis = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      collector: "PatternRecognitionCollector",
      version: "3.0.0",
      // Métricas principais
      overallAccuracy: this.calculateOverallAccuracy(patternData),
      patternRecognitionIndex,
      recognitionSpeed,
      // Análises detalhadas
      accuracyByType,
      complexityAnalysis,
      predictionAbility,
      temporalProgress,
      errorPatterns,
      // Habilidades cognitivas específicas
      cognitiveSkills: {
        patternRecognition: this.assessPatternRecognition(patternData),
        sequentialThinking: this.assessSequentialThinking(patternData),
        logicalReasoning: this.assessLogicalReasoning(patternData),
        predictionAbility: this.assessPredictionSkill(patternData),
        abstractThinking: this.assessAbstractThinking(patternData)
      },
      // Análise de dificuldade
      difficultyProgression: this.analyzeDifficultyProgression(patternData),
      // Recomendações adaptativas
      adaptiveRecommendations: this.generateAdaptiveRecommendations(patternData),
      // Métricas de engajamento
      engagementMetrics: this.calculateEngagementMetrics(patternData)
    };
    return analysis;
  }
  /**
   * Calcula precisão geral
   */
  calculateOverallAccuracy(data) {
    if (!data.attempts || data.attempts.length === 0) return 0.7;
    const correct = data.attempts.filter((attempt) => attempt.correct).length;
    return correct / data.attempts.length;
  }
  /**
   * Analisa precisão por tipo de padrão
   */
  analyzeAccuracyByType(data) {
    const accuracyByType = {};
    Object.keys(this.patternTypes).forEach((type) => {
      const typeAttempts = data.attempts?.filter((attempt) => attempt.patternType === type) || [];
      if (typeAttempts.length > 0) {
        const correct = typeAttempts.filter((attempt) => attempt.correct).length;
        accuracyByType[type] = {
          accuracy: correct / typeAttempts.length,
          attempts: typeAttempts.length,
          description: this.patternTypes[type]
        };
      }
    });
    return accuracyByType;
  }
  /**
   * Avalia velocidade de reconhecimento
   */
  assessRecognitionSpeed(data) {
    if (!data.attempts || data.attempts.length === 0) {
      return { average: 5e3, category: "average" };
    }
    const responseTimes = data.attempts.map((attempt) => attempt.responseTime || 5e3);
    const averageTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    let category = "average";
    if (averageTime < 2e3) category = "excellent";
    else if (averageTime < 3e3) category = "good";
    else if (averageTime < 5e3) category = "average";
    else if (averageTime < 8e3) category = "poor";
    else category = "critical";
    return {
      average: averageTime,
      category,
      distribution: this.calculateTimeDistribution(responseTimes)
    };
  }
  /**
   * Analisa capacidade de lidar com complexidade
   */
  analyzeComplexityHandling(data) {
    const complexityLevels = ["simple", "medium", "complex", "advanced"];
    const complexityAnalysis = {};
    complexityLevels.forEach((level) => {
      const levelAttempts = data.attempts?.filter((attempt) => attempt.complexity === level) || [];
      if (levelAttempts.length > 0) {
        const correct = levelAttempts.filter((attempt) => attempt.correct).length;
        complexityAnalysis[level] = {
          accuracy: correct / levelAttempts.length,
          attempts: levelAttempts.length,
          averageTime: levelAttempts.reduce((sum, attempt) => sum + (attempt.responseTime || 5e3), 0) / levelAttempts.length
        };
      }
    });
    return complexityAnalysis;
  }
  /**
   * Avalia capacidade de predição
   */
  assessPredictionAbility(data) {
    const predictionAttempts = data.attempts?.filter((attempt) => attempt.type === "prediction") || [];
    if (predictionAttempts.length === 0) {
      return { accuracy: 0.7, confidence: "medium" };
    }
    const correct = predictionAttempts.filter((attempt) => attempt.correct).length;
    const accuracy = correct / predictionAttempts.length;
    let confidence = "low";
    if (accuracy >= 0.9) confidence = "excellent";
    else if (accuracy >= 0.8) confidence = "high";
    else if (accuracy >= 0.7) confidence = "medium";
    else if (accuracy >= 0.5) confidence = "low";
    else confidence = "very-low";
    return {
      accuracy,
      confidence,
      totalPredictions: predictionAttempts.length
    };
  }
  /**
   * Analisa progresso temporal
   */
  analyzeTemporalProgress(data) {
    if (!data.attempts || data.attempts.length < 5) {
      return { trend: "insufficient-data", improvement: 0 };
    }
    const attempts = data.attempts.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
    const firstHalf = attempts.slice(0, Math.floor(attempts.length / 2));
    const secondHalf = attempts.slice(Math.floor(attempts.length / 2));
    const firstHalfAccuracy = firstHalf.filter((a) => a.correct).length / firstHalf.length;
    const secondHalfAccuracy = secondHalf.filter((a) => a.correct).length / secondHalf.length;
    const improvement = secondHalfAccuracy - firstHalfAccuracy;
    let trend = "stable";
    if (improvement > 0.1) trend = "improving";
    else if (improvement < -0.1) trend = "declining";
    return {
      trend,
      improvement,
      firstHalfAccuracy,
      secondHalfAccuracy
    };
  }
  /**
   * Analiza padrões de erro
   */
  analyzeErrorPatterns(data) {
    const errors = data.attempts?.filter((attempt) => !attempt.correct) || [];
    if (errors.length === 0) {
      return { errorRate: 0, commonPatterns: [] };
    }
    const errorTypes = {};
    errors.forEach((error2) => {
      const type = error2.errorType || "unknown";
      errorTypes[type] = (errorTypes[type] || 0) + 1;
    });
    const sortedErrors = Object.entries(errorTypes).sort(([, a], [, b]) => b - a).slice(0, 3).map(([type, count]) => ({ type, count, percentage: count / errors.length }));
    return {
      errorRate: errors.length / data.attempts.length,
      commonPatterns: sortedErrors,
      totalErrors: errors.length
    };
  }
  /**
   * Calcula índice de reconhecimento de padrões
   */
  calculatePatternRecognitionIndex(data) {
    const accuracy = this.calculateOverallAccuracy(data);
    const speedScore = this.calculateSpeedScore(data);
    const complexityScore = this.calculateComplexityScore(data);
    return accuracy * 0.5 + speedScore * 0.25 + complexityScore * 0.25;
  }
  /**
   * Calcula pontuação de velocidade
   */
  calculateSpeedScore(data) {
    const speed = this.assessRecognitionSpeed(data);
    const speedCategories = {
      "excellent": 1,
      "good": 0.8,
      "average": 0.6,
      "poor": 0.4,
      "critical": 0.2
    };
    return speedCategories[speed.category] || 0.6;
  }
  /**
   * Calcula pontuação de complexidade
   */
  calculateComplexityScore(data) {
    const complexityAnalysis = this.analyzeComplexityHandling(data);
    const weights = { simple: 0.1, medium: 0.3, complex: 0.4, advanced: 0.2 };
    let weightedScore = 0;
    let totalWeight = 0;
    Object.entries(complexityAnalysis).forEach(([level, analysis]) => {
      const weight = weights[level] || 0.25;
      weightedScore += analysis.accuracy * weight;
      totalWeight += weight;
    });
    return totalWeight > 0 ? weightedScore / totalWeight : 0.6;
  }
  /**
   * Avalia habilidades cognitivas específicas
   */
  assessPatternRecognition(data) {
    return this.calculateOverallAccuracy(data);
  }
  assessSequentialThinking(data) {
    const sequentialAttempts = data.attempts?.filter((a) => a.patternType === "sequential") || [];
    if (sequentialAttempts.length === 0) return 0.7;
    const correct = sequentialAttempts.filter((a) => a.correct).length;
    return correct / sequentialAttempts.length;
  }
  assessLogicalReasoning(data) {
    const logicalAttempts = data.attempts?.filter((a) => ["arithmetic", "geometric"].includes(a.patternType)) || [];
    if (logicalAttempts.length === 0) return 0.7;
    const correct = logicalAttempts.filter((a) => a.correct).length;
    return correct / logicalAttempts.length;
  }
  assessPredictionSkill(data) {
    return this.assessPredictionAbility(data).accuracy;
  }
  assessAbstractThinking(data) {
    const abstractAttempts = data.attempts?.filter((a) => ["complex", "visual"].includes(a.patternType)) || [];
    if (abstractAttempts.length === 0) return 0.7;
    const correct = abstractAttempts.filter((a) => a.correct).length;
    return correct / abstractAttempts.length;
  }
  /**
   * Analiza progressão de dificuldade
   */
  analyzeDifficultyProgression(data) {
    return {
      currentLevel: "medium",
      suggestedNext: "complex",
      readiness: 0.8
    };
  }
  /**
   * Gera recomendações adaptativas
   */
  generateAdaptiveRecommendations(data) {
    const accuracy = this.calculateOverallAccuracy(data);
    const speed = this.assessRecognitionSpeed(data);
    const recommendations = [];
    if (accuracy < 0.7) {
      recommendations.push({
        type: "difficulty",
        action: "decrease",
        reason: "Baixa precisão detectada"
      });
    }
    if (speed.average > 8e3) {
      recommendations.push({
        type: "time",
        action: "extend",
        reason: "Tempo de resposta elevado"
      });
    }
    return recommendations;
  }
  /**
   * Calcula métricas de engajamento
   */
  calculateEngagementMetrics(data) {
    return {
      attentionLevel: 0.8,
      motivationScore: 0.75,
      frustrationLevel: 0.3
    };
  }
  /**
   * Calcula distribuição de tempos
   */
  calculateTimeDistribution(times) {
    const sorted = times.sort((a, b) => a - b);
    return {
      min: Math.min(...times),
      max: Math.max(...times),
      median: sorted[Math.floor(sorted.length / 2)],
      q1: sorted[Math.floor(sorted.length * 0.25)],
      q3: sorted[Math.floor(sorted.length * 0.75)]
    };
  }
  /**
   * Retorna análise padrão quando dados são insuficientes
   */
  getDefaultAnalysis() {
    return {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      collector: "PatternRecognitionCollector",
      version: "3.0.0",
      overallAccuracy: 0.7,
      patternRecognitionIndex: 0.7,
      recognitionSpeed: { average: 5e3, category: "average" },
      accuracyByType: {},
      complexityAnalysis: {},
      predictionAbility: { accuracy: 0.7, confidence: "medium" },
      temporalProgress: { trend: "insufficient-data", improvement: 0 },
      errorPatterns: { errorRate: 0.3, commonPatterns: [] },
      cognitiveSkills: {
        patternRecognition: 0.7,
        sequentialThinking: 0.7,
        logicalReasoning: 0.7,
        predictionAbility: 0.7,
        abstractThinking: 0.7
      },
      difficultyProgression: {
        currentLevel: "medium",
        suggestedNext: "complex",
        readiness: 0.7
      },
      adaptiveRecommendations: [],
      engagementMetrics: {
        attentionLevel: 0.7,
        motivationScore: 0.7,
        frustrationLevel: 0.3
      }
    };
  }
}
class NumberCountingCollectorsHub {
  constructor() {
    this._collectors = {
      numericalCognition: new NumericalCognitionCollector(),
      attentionFocus: new AttentionFocusCollector(),
      visualProcessing: new VisualProcessingCollector(),
      mathematicalReasoning: new MathematicalReasoningCollector(),
      errorPattern: new ErrorPatternCollector(),
      estimationSkills: new EstimationSkillsCollector(),
      sequenceAnalysis: new SequenceAnalysisCollector(),
      comparisonSkills: new ComparisonSkillsCollector(),
      soundMatching: new SoundMatchingCollector(),
      patternRecognition: new PatternRecognitionCollector()
    };
    this.analysisHistory = [];
    this.currentSession = null;
    this.performanceBaseline = null;
    this.cognitiveProfile = null;
    this.gameSpecificConfig = {
      minAttempts: 3,
      maxAnalysisTime: 5e3,
      significantChangeThreshold: 0.15,
      adaptiveDifficultyEnabled: true,
      // 🎯 CONFIGURAÇÕES POR ATIVIDADE V3
      activityConfigs: {
        number_counting: {
          focusMetric: "counting_accuracy",
          timeWeight: 0.3,
          accuracyWeight: 0.7,
          patterns: ["sequential_errors", "magnitude_errors"]
        },
        sound_matching: {
          focusMetric: "auditory_processing",
          timeWeight: 0.4,
          accuracyWeight: 0.6,
          patterns: ["audio_confusion", "delay_patterns"]
        },
        number_estimation: {
          focusMetric: "estimation_accuracy",
          timeWeight: 0.2,
          accuracyWeight: 0.8,
          patterns: ["underestimation", "overestimation", "magnitude_bias"]
        },
        sequence_completion: {
          focusMetric: "pattern_recognition",
          timeWeight: 0.5,
          accuracyWeight: 0.5,
          patterns: ["sequence_logic", "arithmetic_progression"]
        },
        number_comparison: {
          focusMetric: "comparison_logic",
          timeWeight: 0.3,
          accuracyWeight: 0.7,
          patterns: ["magnitude_comparison", "relative_errors"]
        },
        pattern_recognition: {
          focusMetric: "abstract_reasoning",
          timeWeight: 0.6,
          accuracyWeight: 0.4,
          patterns: ["pattern_complexity", "abstraction_level"]
        }
      }
    };
    this.behavioralAnalysis = {
      activityPreferences: {},
      learningProgression: {},
      cognitivePatterns: {},
      adaptiveRecommendations: []
    };
  }
  /**
   * Getter para coletores - necessário para GameSpecificProcessors
   */
  get collectors() {
    return this._collectors;
  }
  /**
   * 🎯 EXECUTA ANÁLISE COMPLETA V3 - SUPORTE PARA 6 ATIVIDADES
   */
  async runCompleteAnalysis(gameData) {
    try {
      console.log("🔢 Iniciando análise completa do NumberCounting V3...");
      if (!this.validateGameData(gameData)) {
        throw new Error("Dados do jogo inválidos para análise V3");
      }
      const startTime = Date.now();
      const collectorData = this.prepareCollectorDataV3(gameData);
      const activityAnalysis = await this.analyzeByActivity(gameData);
      const analysisPromises = [
        this.collectors.numericalCognition.analyze(collectorData.numericalCognition),
        this.collectors.attentionFocus.analyze(collectorData.attentionFocus),
        this.collectors.visualProcessing.analyze(collectorData.visualProcessing),
        this.collectors.mathematicalReasoning.analyze(collectorData.mathematicalReasoning),
        this.collectors.errorPattern.analyze(collectorData.errorPattern),
        this.collectors.estimationSkills.analyze(collectorData.estimation),
        this.collectors.sequenceAnalysis.analyze(collectorData.sequence),
        this.collectors.comparisonSkills.analyze(collectorData.comparison),
        this.collectors.soundMatching.analyze(collectorData.sound),
        this.collectors.patternRecognition.analyze(collectorData.pattern)
      ];
      const [
        numericalCognitionResults,
        attentionFocusResults,
        visualProcessingResults,
        mathematicalReasoningResults,
        errorPatternResults,
        estimationSkillsResults,
        sequenceAnalysisResults,
        comparisonSkillsResults,
        soundMatchingResults,
        patternRecognitionResults
      ] = await Promise.all(analysisPromises);
      const integratedAnalysis = this.integrateAnalysisResultsV3({
        numericalCognition: numericalCognitionResults,
        attentionFocus: attentionFocusResults,
        visualProcessing: visualProcessingResults,
        mathematicalReasoning: mathematicalReasoningResults,
        errorPattern: errorPatternResults,
        estimationSkills: estimationSkillsResults,
        sequenceAnalysis: sequenceAnalysisResults,
        comparisonSkills: comparisonSkillsResults,
        soundMatching: soundMatchingResults,
        patternRecognition: patternRecognitionResults,
        activityAnalysis
      }, gameData);
      const synthesisMetrics = this.calculateSynthesisMetricsV3(integratedAnalysis, gameData);
      const cognitiveInsights = this.generateCognitiveInsightsV3(integratedAnalysis, gameData);
      const developmentProfile = this.createDevelopmentProfileV3(integratedAnalysis, activityAnalysis);
      const adaptiveRecommendations = this.generateAdaptiveRecommendations(integratedAnalysis, activityAnalysis);
      const analysisTime = Date.now() - startTime;
      const completeAnalysis = {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        gameId: "numbercounting_v3",
        sessionId: gameData.sessionId,
        analysisTime,
        version: "3.0.0",
        // 🎯 RESULTADOS INDIVIDUAIS DOS COLETORES V3
        detailedResults: {
          numericalCognition: numericalCognitionResults,
          attentionFocus: attentionFocusResults,
          visualProcessing: visualProcessingResults,
          mathematicalReasoning: mathematicalReasoningResults,
          errorPattern: errorPatternResults,
          estimationSkills: estimationSkillsResults,
          sequenceAnalysis: sequenceAnalysisResults,
          comparisonSkills: comparisonSkillsResults,
          soundMatching: soundMatchingResults,
          patternRecognition: patternRecognitionResults
        },
        // 🎯 ANÁLISE POR ATIVIDADE V3
        activityAnalysis,
        // Análise integrada
        integratedAnalysis,
        synthesisMetrics,
        cognitiveInsights,
        developmentProfile,
        adaptiveRecommendations,
        // 📊 METADADOS DA ANÁLISE V3
        metadata: {
          totalAttempts: gameData.attempts?.length || 0,
          activitiesPlayed: this.getActivitiesPlayed(gameData),
          difficulty: gameData.difficulty,
          accuracy: this.calculateOverallAccuracy(gameData),
          avgResponseTime: this.calculateAverageResponseTime(gameData),
          dataQuality: this.assessDataQuality(gameData),
          engagementLevel: this.calculateEngagementLevel(gameData),
          cognitiveLoad: this.estimateCognitiveLoad(gameData)
        }
      };
      this.analysisHistory.push(completeAnalysis);
      this.updateCognitiveProfileV3(completeAnalysis);
      console.log(`✅ Análise NumberCounting V3 concluída em ${analysisTime}ms`);
      return completeAnalysis;
    } catch (error2) {
      console.error("❌ Erro na análise completa NumberCounting V3:", error2);
      return this.getErrorAnalysisV3(error2, gameData);
    }
  }
  /**
   * Alias para runCompleteAnalysis para compatibilidade
   */
  async processGameData(gameData) {
    return await this.runCompleteAnalysis(gameData);
  }
  /**
   * Valida os dados do jogo antes da análise
   */
  validateGameData(gameData) {
    if (!gameData) {
      console.warn("NumberCountingCollectorsHub: Dados do jogo não fornecidos");
      return false;
    }
    if (!gameData.attempts || !Array.isArray(gameData.attempts)) {
      console.warn("NumberCountingCollectorsHub: Tentativas do jogo não encontradas");
      return false;
    }
    if (gameData.attempts.length < this.gameSpecificConfig.minAttempts) {
      console.warn(`NumberCountingCollectorsHub: Número insuficiente de tentativas (${gameData.attempts.length})`);
      return false;
    }
    const validAttempts = gameData.attempts.every(
      (attempt) => typeof attempt.correctAnswer === "number" && typeof attempt.userAnswer === "number" && typeof attempt.responseTime === "number" && typeof attempt.isCorrect === "boolean"
    );
    if (!validAttempts) {
      console.warn("NumberCountingCollectorsHub: Estrutura das tentativas inválida");
      return false;
    }
    return true;
  }
  /**
   * 🎯 ANÁLISE POR ATIVIDADE V3
   */
  async analyzeByActivity(gameData) {
    console.log("🎯 Analisando por atividade V3...");
    const activityData = {};
    const activitiesPlayed = this.getActivitiesPlayed(gameData);
    for (const activity of activitiesPlayed) {
      const activityAttempts = this.getActivityAttempts(gameData, activity);
      if (activityAttempts.length > 0) {
        activityData[activity] = {
          totalAttempts: activityAttempts.length,
          correctAnswers: activityAttempts.filter((a) => a.isCorrect).length,
          accuracy: this.calculateActivityAccuracy(activityAttempts),
          averageResponseTime: this.calculateActivityAverageTime(activityAttempts),
          learningCurve: this.calculateLearningCurve(activityAttempts),
          errorPatterns: this.identifyActivityErrorPatterns(activityAttempts, activity),
          cognitiveMetrics: this.calculateActivityCognitiveMetrics(activityAttempts, activity),
          difficultyProgression: this.analyzeDifficultyProgression(activityAttempts),
          engagementIndicators: this.calculateActivityEngagement(activityAttempts)
        };
      }
    }
    return activityData;
  }
  /**
   * 🎯 OBTER ATIVIDADES JOGADAS
   */
  getActivitiesPlayed(gameData) {
    if (!gameData || !gameData.attempts) return [];
    const activities = /* @__PURE__ */ new Set();
    gameData.attempts.forEach((attempt) => {
      if (attempt.activityType) {
        activities.add(attempt.activityType);
      }
    });
    return Array.from(activities);
  }
  /**
   * 🎯 OBTER TENTATIVAS DE UMA ATIVIDADE ESPECÍFICA
   */
  getActivityAttempts(gameData, activity) {
    if (!gameData || !gameData.attempts) return [];
    return gameData.attempts.filter((attempt) => attempt.activityType === activity);
  }
  /**
   * Identifica eventos de distração
   */
  identifyDistractionEvents(gameData) {
    if (!gameData || !gameData.attempts) return [];
    const distractionEvents = [];
    gameData.attempts.forEach((attempt, index) => {
      if (attempt.responseTime && attempt.responseTime > 1e4) {
        distractionEvents.push({
          attemptIndex: index,
          type: "slow_response",
          duration: attempt.responseTime,
          timestamp: attempt.timestamp
        });
      }
      if (index > 0 && !attempt.isCorrect && !gameData.attempts[index - 1].isCorrect) {
        distractionEvents.push({
          attemptIndex: index,
          type: "consecutive_errors",
          timestamp: attempt.timestamp
        });
      }
    });
    return distractionEvents;
  }
  /**
   * Calcula métricas de foco
   */
  calculateFocusMetrics(gameData) {
    if (!gameData || !gameData.attempts) {
      return { avgResponseTime: 0, consistency: 0, attentionSpan: 0 };
    }
    const responseTimes = gameData.attempts.filter((attempt) => attempt.responseTime).map((attempt) => attempt.responseTime);
    const avgResponseTime = responseTimes.length > 0 ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length : 0;
    const variance = responseTimes.length > 1 ? responseTimes.reduce((sum, time) => sum + Math.pow(time - avgResponseTime, 2), 0) / responseTimes.length : 0;
    const consistency = variance > 0 ? 1 / (1 + Math.sqrt(variance) / avgResponseTime) : 1;
    const attentionSpan = this.calculateAttentionSpan(gameData);
    return {
      avgResponseTime,
      consistency: Math.max(0, Math.min(1, consistency)),
      attentionSpan
    };
  }
  /**
   * Calcula o span de atenção
   */
  calculateAttentionSpan(gameData) {
    if (!gameData || !gameData.attempts) return 0;
    let maxSpan = 0;
    let currentSpan = 0;
    gameData.attempts.forEach((attempt) => {
      if (attempt.responseTime && attempt.responseTime < 8e3) {
        currentSpan++;
        maxSpan = Math.max(maxSpan, currentSpan);
      } else {
        currentSpan = 0;
      }
    });
    return maxSpan;
  }
  /**
   * Avalia a complexidade visual
   */
  assessVisualComplexity(gameData) {
    if (!gameData || !gameData.attempts) return "low";
    let complexityScore = 0;
    gameData.attempts.forEach((attempt) => {
      if (attempt.numbers && attempt.numbers.length > 5) complexityScore += 2;
      if (attempt.patterns && attempt.patterns.length > 3) complexityScore += 2;
      if (attempt.visualElements && attempt.visualElements > 10) complexityScore += 1;
      if (attempt.difficulty && attempt.difficulty > 3) complexityScore += 1;
    });
    const avgComplexity = gameData.attempts.length > 0 ? complexityScore / gameData.attempts.length : 0;
    if (avgComplexity > 3) return "high";
    if (avgComplexity > 1.5) return "medium";
    return "low";
  }
  /**
   * Extrai padrões espaciais
   */
  extractSpatialPatterns(gameData) {
    if (!gameData || !gameData.attempts) return [];
    const patterns = [];
    gameData.attempts.forEach((attempt) => {
      if (attempt.position) {
        patterns.push(attempt.position);
      }
      if (attempt.spatialArrangement) {
        patterns.push(attempt.spatialArrangement);
      }
    });
    return patterns;
  }
  /**
   * Extrai tipos de raciocínio
   */
  extractReasoningTypes(gameData) {
    if (!gameData || !gameData.attempts) return ["basic_counting"];
    const reasoningTypes = /* @__PURE__ */ new Set();
    gameData.attempts.forEach((attempt) => {
      if (attempt.activityType === "number_comparison") {
        reasoningTypes.add("comparative_reasoning");
      }
      if (attempt.activityType === "sequence_completion") {
        reasoningTypes.add("sequential_reasoning");
      }
      if (attempt.activityType === "number_estimation") {
        reasoningTypes.add("estimation_reasoning");
      }
      if (attempt.activityType === "pattern_recognition") {
        reasoningTypes.add("pattern_reasoning");
      }
      if (attempt.isCorrect && attempt.responseTime < 2e3) {
        reasoningTypes.add("intuitive_reasoning");
      }
    });
    if (reasoningTypes.size === 0) {
      reasoningTypes.add("basic_counting");
    }
    return Array.from(reasoningTypes);
  }
  /**
   * Avalia a complexidade do problema
   */
  assessProblemComplexity(gameData) {
    if (!gameData || !gameData.attempts) return "low";
    let complexityScore = 0;
    gameData.attempts.forEach((attempt) => {
      if (attempt.difficulty) complexityScore += attempt.difficulty;
      if (attempt.numbers && attempt.numbers.some((n) => n > 20)) complexityScore += 1;
      if (attempt.operationSteps && attempt.operationSteps > 1) complexityScore += 2;
    });
    const avgComplexity = gameData.attempts.length > 0 ? complexityScore / gameData.attempts.length : 0;
    if (avgComplexity > 4) return "high";
    if (avgComplexity > 2) return "medium";
    return "low";
  }
  /**
   * Categoriza erros
   */
  categorizeErrors(gameData) {
    if (!gameData || !gameData.attempts) return {};
    const errorTypes = {};
    gameData.attempts.forEach((attempt) => {
      if (!attempt.isCorrect) {
        const errorType = this.identifyErrorType(attempt);
        errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
      }
    });
    return errorTypes;
  }
  /**
   * Identifica tipo de erro
   */
  identifyErrorType(attempt) {
    if (!attempt.answer || !attempt.targetValue) return "unknown";
    const difference = Math.abs(attempt.answer - attempt.targetValue);
    if (difference === 1) return "off_by_one";
    if (difference <= 3) return "close_estimate";
    if (attempt.answer > attempt.targetValue * 2) return "significant_overestimate";
    if (attempt.answer < attempt.targetValue / 2) return "significant_underestimate";
    if (difference > 10) return "major_miscalculation";
    return "moderate_error";
  }
  /**
   * Analisa padrões de recuperação
   */
  analyzeRecoveryPatterns(gameData) {
    if (!gameData || !gameData.attempts) return {};
    const recoveryPatterns = {
      immediateRecovery: 0,
      gradualRecovery: 0,
      persistentErrors: 0
    };
    for (let i = 1; i < gameData.attempts.length; i++) {
      const prevAttempt = gameData.attempts[i - 1];
      const currentAttempt = gameData.attempts[i];
      if (!prevAttempt.isCorrect && currentAttempt.isCorrect) {
        recoveryPatterns.immediateRecovery++;
      } else if (!prevAttempt.isCorrect && !currentAttempt.isCorrect) {
        recoveryPatterns.persistentErrors++;
      }
    }
    return recoveryPatterns;
  }
  /**
   * 🎯 PREPARAR DADOS PARA COLETORES V3
   */
  prepareCollectorDataV3(gameData) {
    const baseData = {
      attempts: gameData.attempts,
      sessionId: gameData.sessionId,
      difficulty: gameData.difficulty,
      activitiesPlayed: this.getActivitiesPlayed(gameData),
      sessionDuration: gameData.sessionDuration || 0,
      timestamp: Date.now()
    };
    return {
      numericalCognition: {
        ...baseData,
        focus: "numerical_concepts",
        numericalRange: this.extractNumericalRange(gameData),
        operationTypes: this.extractOperationTypes(gameData)
      },
      attentionFocus: {
        ...baseData,
        focus: "sustained_attention",
        distractionEvents: this.identifyDistractionEvents(gameData),
        focusMetrics: this.calculateFocusMetrics(gameData)
      },
      visualProcessing: {
        ...baseData,
        focus: "visual_numerical_processing",
        visualComplexity: this.assessVisualComplexity(gameData),
        spatialPatterns: this.extractSpatialPatterns(gameData)
      },
      mathematicalReasoning: {
        ...baseData,
        focus: "mathematical_logic",
        reasoningTypes: this.extractReasoningTypes(gameData),
        problemComplexity: this.assessProblemComplexity(gameData)
      },
      errorPattern: {
        ...baseData,
        focus: "error_analysis",
        errorTypes: this.categorizeErrors(gameData),
        recoveryPatterns: this.analyzeRecoveryPatterns(gameData)
      },
      estimation: {
        ...baseData,
        numberEstimation: this.filterByActivity(gameData, "number_estimation"),
        focus: "estimation_skills"
      },
      sequence: {
        ...baseData,
        sequenceCompletion: this.filterByActivity(gameData, "sequence_completion"),
        focus: "sequence_analysis"
      },
      comparison: {
        ...baseData,
        numberComparison: this.filterByActivity(gameData, "number_comparison"),
        focus: "comparison_skills"
      },
      sound: {
        ...baseData,
        soundMatching: this.filterByActivity(gameData, "sound_matching"),
        focus: "auditory_processing"
      },
      pattern: {
        ...baseData,
        patternRecognition: this.filterByActivity(gameData, "pattern_recognition"),
        focus: "pattern_analysis"
      }
    };
  }
  /**
   * Filtra dados por atividade específica
   */
  filterByActivity(gameData, activityType) {
    if (!gameData.attempts) return [];
    return gameData.attempts.filter((attempt) => attempt.activityType === activityType);
  }
  /**
   * Extrai faixa numérica
   */
  extractNumericalRange(gameData) {
    if (!gameData || !gameData.attempts) {
      return { min: 0, max: 10 };
    }
    let minValue = Infinity;
    let maxValue = -Infinity;
    gameData.attempts.forEach((attempt) => {
      if (attempt.numbers && Array.isArray(attempt.numbers)) {
        attempt.numbers.forEach((num) => {
          if (typeof num === "number") {
            minValue = Math.min(minValue, num);
            maxValue = Math.max(maxValue, num);
          }
        });
      }
      if (typeof attempt.answer === "number") {
        minValue = Math.min(minValue, attempt.answer);
        maxValue = Math.max(maxValue, attempt.answer);
      }
      if (typeof attempt.targetValue === "number") {
        minValue = Math.min(minValue, attempt.targetValue);
        maxValue = Math.max(maxValue, attempt.targetValue);
      }
    });
    return minValue === Infinity || maxValue === -Infinity ? { min: 0, max: 10 } : { min: minValue, max: maxValue };
  }
  /**
   * Extrai tipos de operação
   */
  extractOperationTypes(gameData) {
    if (!gameData || !gameData.attempts) {
      return ["counting"];
    }
    const operationTypes = /* @__PURE__ */ new Set();
    gameData.attempts.forEach((attempt) => {
      if (attempt.activityType) operationTypes.add(attempt.activityType);
      if (attempt.operationType) operationTypes.add(attempt.operationType);
      if (attempt.numbers && attempt.numbers.length > 1) operationTypes.add("comparison");
      if (attempt.isEstimation) operationTypes.add("estimation");
      if (attempt.hasSound || attempt.soundFile) operationTypes.add("auditory_matching");
      if (attempt.patterns) operationTypes.add("pattern_recognition");
    });
    return Array.from(operationTypes);
  }
  /**
   * Extrai padrões de resposta
   */
  extractResponsePatterns(gameData) {
    if (!gameData || !gameData.attempts) {
      return { patterns: [], frequency: {} };
    }
    const patterns = [];
    const frequency = {};
    gameData.attempts.forEach((attempt) => {
      if (attempt.responsePattern) {
        patterns.push(attempt.responsePattern);
        frequency[attempt.responsePattern] = (frequency[attempt.responsePattern] || 0) + 1;
      }
      if (attempt.responseTime && attempt.responseTime < 1e3) {
        patterns.push("quick_response");
        frequency["quick_response"] = (frequency["quick_response"] || 0) + 1;
      }
      if (attempt.confidence && attempt.confidence < 0.5) {
        patterns.push("low_confidence");
        frequency["low_confidence"] = (frequency["low_confidence"] || 0) + 1;
      }
    });
    return { patterns, frequency };
  }
  /**
   * Extrai progressão de dificuldade
   */
  extractDifficultyProgression(gameData) {
    if (!gameData || !gameData.attempts) {
      return { levels: [], progression: "stable" };
    }
    const levels = [];
    let lastLevel = null;
    let progressionType = "stable";
    gameData.attempts.forEach((attempt) => {
      if (attempt.difficulty !== void 0) {
        levels.push(attempt.difficulty);
        if (lastLevel !== null) {
          if (attempt.difficulty > lastLevel) {
            progressionType = "increasing";
          } else if (attempt.difficulty < lastLevel) {
            progressionType = "decreasing";
          }
        }
        lastLevel = attempt.difficulty;
      }
    });
    return { levels, progression: progressionType };
  }
  /**
   * Extrai métricas de desempenho
   */
  extractPerformanceMetrics(gameData) {
    if (!gameData || !gameData.attempts) {
      return {
        accuracy: 0,
        averageResponseTime: 0,
        totalAttempts: 0,
        correctAttempts: 0
      };
    }
    const totalAttempts = gameData.attempts.length;
    let correctAttempts = 0;
    let totalResponseTime = 0;
    let responseTimeCount = 0;
    gameData.attempts.forEach((attempt) => {
      if (attempt.isCorrect) correctAttempts++;
      if (attempt.responseTime && typeof attempt.responseTime === "number") {
        totalResponseTime += attempt.responseTime;
        responseTimeCount++;
      }
    });
    const accuracy = totalAttempts > 0 ? correctAttempts / totalAttempts : 0;
    const averageResponseTime = responseTimeCount > 0 ? totalResponseTime / responseTimeCount : 0;
    return {
      accuracy,
      averageResponseTime,
      totalAttempts,
      correctAttempts
    };
  }
  /**
   * Calcula acurácia geral
   */
  calculateOverallAccuracy(gameData) {
    const metrics = this.extractPerformanceMetrics(gameData);
    return metrics.accuracy;
  }
  /**
   * Calcula tempo médio de resposta
   */
  calculateAverageResponseTime(gameData) {
    const metrics = this.extractPerformanceMetrics(gameData);
    return metrics.averageResponseTime;
  }
  /**
   * Avalia qualidade dos dados
   */
  assessDataQuality(gameData) {
    if (!gameData || !gameData.attempts) return "poor";
    const validAttempts = gameData.attempts.every(
      (attempt) => attempt.correctAnswer !== void 0 && attempt.userAnswer !== void 0 && attempt.responseTime !== void 0 && attempt.isCorrect !== void 0
    );
    const sufficientAttempts = gameData.attempts.length >= this.gameSpecificConfig.minAttempts;
    const variety = new Set(gameData.attempts.map((a) => a.activityType)).size > 1;
    if (validAttempts && sufficientAttempts && variety) return "high";
    if (validAttempts && sufficientAttempts) return "medium";
    return "poor";
  }
  /**
   * Calcula nível de engajamento
   */
  calculateEngagementLevel(gameData) {
    if (!gameData || !gameData.attempts) return 0;
    const totalAttempts = gameData.attempts.length;
    const responseTimes = gameData.attempts.filter((a) => a.responseTime).map((a) => a.responseTime);
    const avgResponseTime = responseTimes.length > 0 ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length : 0;
    const engagementScore = totalAttempts * 0.4 + (avgResponseTime < 5e3 ? 0.6 : 0.3);
    return Math.min(1, Math.max(0, engagementScore));
  }
  /**
   * Estima carga cognitiva
   */
  estimateCognitiveLoad(gameData) {
    if (!gameData || !gameData.attempts) return "low";
    const complexity = this.assessProblemComplexity(gameData);
    const responseTimes = gameData.attempts.filter((a) => a.responseTime).map((a) => a.responseTime);
    const avgResponseTime = responseTimes.length > 0 ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length : 0;
    if (complexity === "high" || avgResponseTime > 7e3) return "high";
    if (complexity === "medium" || avgResponseTime > 4e3) return "medium";
    return "low";
  }
  /**
   * Calcula acurácia por atividade
   */
  calculateActivityAccuracy(activityAttempts) {
    if (!activityAttempts || activityAttempts.length === 0) return 0;
    const correct = activityAttempts.filter((a) => a.isCorrect).length;
    return correct / activityAttempts.length;
  }
  /**
   * Calcula tempo médio por atividade
   */
  calculateActivityAverageTime(activityAttempts) {
    if (!activityAttempts || activityAttempts.length === 0) return 0;
    const times = activityAttempts.filter((a) => a.responseTime).map((a) => a.responseTime);
    return times.length > 0 ? times.reduce((sum, time) => sum + time, 0) / times.length : 0;
  }
  /**
   * Calcula curva de aprendizado
   */
  calculateLearningCurve(activityAttempts) {
    if (!activityAttempts || activityAttempts.length < 2) return [];
    const curve = [];
    let correctStreak = 0;
    activityAttempts.forEach((attempt) => {
      if (attempt.isCorrect) {
        correctStreak++;
      } else {
        correctStreak = 0;
      }
      curve.push({ attempt, streak: correctStreak });
    });
    return curve;
  }
  /**
   * Identifica padrões de erro por atividade
   */
  identifyActivityErrorPatterns(activityAttempts, activity) {
    if (!activityAttempts || activityAttempts.length === 0) return [];
    const patterns = this.gameSpecificConfig.activityConfigs[activity]?.patterns || [];
    const errors = activityAttempts.filter((a) => !a.isCorrect).map((a) => this.identifyErrorType(a));
    return [.../* @__PURE__ */ new Set([...patterns, ...errors])];
  }
  /**
   * Calcula métricas cognitivas por atividade
   */
  calculateActivityCognitiveMetrics(activityAttempts, activity) {
    if (!activityAttempts || activityAttempts.length === 0) return {};
    const config = this.gameSpecificConfig.activityConfigs[activity] || {};
    const accuracy = this.calculateActivityAccuracy(activityAttempts);
    const avgTime = this.calculateActivityAverageTime(activityAttempts);
    return {
      focusMetric: config.focusMetric || "unknown",
      weightedScore: accuracy * (config.accuracyWeight || 0.5) + Math.max(0, 1 - avgTime / 1e4) * (config.timeWeight || 0.5)
    };
  }
  /**
   * Analisa progressão de dificuldade
   */
  analyzeDifficultyProgression(activityAttempts) {
    return this.extractDifficultyProgression({ attempts: activityAttempts });
  }
  /**
   * Calcula indicadores de engajamento por atividade
   */
  calculateActivityEngagement(activityAttempts) {
    if (!activityAttempts || activityAttempts.length === 0) return 0;
    const totalAttempts = activityAttempts.length;
    const avgTime = this.calculateActivityAverageTime(activityAttempts);
    return Math.min(1, totalAttempts * 0.4 + (avgTime < 5e3 ? 0.6 : 0.3));
  }
  /**
   * Integra resultados da análise V3
   */
  integrateAnalysisResultsV3(results, gameData) {
    const integrated = {
      numericalSkills: results.numericalCognition.score || 0,
      attentionMetrics: results.attentionFocus.metrics || {},
      visualProcessing: results.visualProcessing.complexity || "low",
      reasoningAbility: results.mathematicalReasoning.level || "basic",
      errorPatterns: results.errorPattern.patterns || [],
      estimationAccuracy: results.estimationSkills.accuracy || 0,
      sequenceSkills: results.sequenceAnalysis.score || 0,
      comparisonSkills: results.comparisonSkills.score || 0,
      auditoryProcessing: results.soundMatching.score || 0,
      patternRecognition: results.patternRecognition.score || 0,
      activityPerformance: results.activityAnalysis
    };
    return integrated;
  }
  /**
   * Calcula métricas de síntese V3
   */
  calculateSynthesisMetricsV3(integratedAnalysis, gameData) {
    const weights = {
      numericalSkills: 0.2,
      attentionMetrics: 0.15,
      visualProcessing: 0.15,
      reasoningAbility: 0.2,
      estimationAccuracy: 0.15,
      sequenceSkills: 0.1,
      comparisonSkills: 0.1,
      auditoryProcessing: 0.05,
      patternRecognition: 0.05
    };
    let compositeScore = 0;
    Object.keys(weights).forEach((key) => {
      const value = typeof integratedAnalysis[key] === "number" ? integratedAnalysis[key] : 0;
      compositeScore += value * weights[key];
    });
    return {
      compositeScore,
      performanceTrend: this.analyzePerformanceTrend(gameData),
      cognitiveLoad: this.estimateCognitiveLoad(gameData)
    };
  }
  /**
   * Gera insights cognitivos V3
   */
  generateCognitiveInsightsV3(integratedAnalysis, gameData) {
    const insights = [];
    if (integratedAnalysis.numericalSkills > 0.8) {
      insights.push("Forte habilidade em conceitos numéricos");
    } else if (integratedAnalysis.numericalSkills < 0.4) {
      insights.push("Necessita reforço em conceitos numéricos básicos");
    }
    if (integratedAnalysis.attentionMetrics.consistency < 0.5) {
      insights.push("Inconsistência na atenção sustentada detectada");
    }
    if (integratedAnalysis.errorPatterns.includes("significant_overestimate")) {
      insights.push("Tendência a superestimar em tarefas de estimativa");
    }
    return insights;
  }
  /**
   * Cria perfil de desenvolvimento V3
   */
  createDevelopmentProfileV3(integratedAnalysis, activityAnalysis) {
    return {
      strengths: Object.keys(integratedAnalysis.activityPerformance).filter((activity) => integratedAnalysis.activityPerformance[activity].accuracy > 0.7),
      weaknesses: Object.keys(integratedAnalysis.activityPerformance).filter((activity) => integratedAnalysis.activityPerformance[activity].accuracy < 0.4),
      progress: this.calculateProgressMetrics(activityAnalysis)
    };
  }
  /**
   * Gera recomendações adaptativas
   */
  generateAdaptiveRecommendations(integratedAnalysis, activityAnalysis) {
    const recommendations = [];
    Object.keys(activityAnalysis).forEach((activity) => {
      if (activityAnalysis[activity].accuracy < 0.5) {
        recommendations.push(`Praticar mais ${activity} com dificuldade reduzida`);
      }
      if (activityAnalysis[activity].averageResponseTime > 7e3) {
        recommendations.push(`Focar em velocidade para ${activity}`);
      }
    });
    return recommendations;
  }
  /**
   * Atualiza perfil cognitivo V3
   */
  updateCognitiveProfileV3(completeAnalysis) {
    this.cognitiveProfile = {
      ...this.cognitiveProfile,
      lastAnalysis: completeAnalysis,
      performanceHistory: this.analysisHistory.map((a) => a.metadata.accuracy)
    };
  }
  /**
   * Analisa tendência de desempenho
   */
  analyzePerformanceTrend(gameData) {
    const metrics = this.extractPerformanceMetrics(gameData);
    return metrics.accuracy > 0.7 ? "positive" : metrics.accuracy < 0.4 ? "negative" : "stable";
  }
  /**
   * Calcula métricas de progresso
   */
  calculateProgressMetrics(activityAnalysis) {
    const progress = {};
    Object.keys(activityAnalysis).forEach((activity) => {
      progress[activity] = activityAnalysis[activity].learningCurve.slice(-1)[0]?.streak || 0;
    });
    return progress;
  }
  /**
   * Retorna análise de erro V3
   */
  getErrorAnalysisV3(error2, gameData) {
    return {
      error: error2.message,
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      gameId: "numbercounting_v3",
      sessionId: gameData?.sessionId || "unknown",
      metadata: {
        totalAttempts: gameData?.attempts?.length || 0,
        dataQuality: "poor"
      }
    };
  }
}
class ContagemNumerosProcessors extends IGameProcessor {
  constructor(logger) {
    const config = {
      category: "numerical_processing",
      therapeuticFocus: ["numerical_cognition", "counting_skills", "mathematical_reasoning"],
      cognitiveAreas: ["numerical_processing", "executive_function", "memory"],
      thresholds: {
        accuracy: 70,
        responseTime: 3500,
        engagement: 75
      }
    };
    super(config);
    this.logger = logger || this.logger;
    this.gameType = "ContagemNumeros";
  }
  /**
   * Processa dados do jogo Contagem de Números
   * @param {Object} gameData - Dados coletados do jogo
   * @param {Object} collectorsHub - Hub de coletores específico do jogo
   * @returns {Promise<Object>} Análise terapêutica específica
   */
  async processGameData(gameData, collectorsHub = null) {
    try {
      this.logger?.info("🎮 Processando dados ContagemNumeros", {
        sessionId: gameData.sessionId,
        userId: gameData.userId,
        collectorsActive: collectorsHub ? Object.keys(collectorsHub.collectors || {}).length : 0
      });
      const metrics = await this.processContagemNumerosMetrics(gameData, gameData);
      const therapeuticAnalysis = this.generateTherapeuticAnalysis(metrics, gameData);
      const processedMetrics = this.processMetricsForDatabase(metrics, gameData);
      return {
        success: true,
        gameType: this.gameType,
        metrics,
        therapeuticAnalysis,
        processedMetrics,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    } catch (error2) {
      this.logger?.error("❌ Erro ao processar dados ContagemNumeros:", error2);
      return {
        success: false,
        gameType: this.gameType,
        error: error2.message,
        fallbackAnalysis: this.generateFallbackTherapeuticAnalysis(gameData),
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    }
  }
  /**
   * Método principal de processamento de dados
   * @param {Object} sessionData - Dados da sessão de jogo
   * @returns {Promise<Object>} Métricas processadas
   */
  async processData(sessionData) {
    try {
      this.logger?.info("🔢 Processando dados Contagem de Números", {
        sessionId: sessionData.sessionId,
        userId: sessionData.userId
      });
      const result = await this.processGameData(sessionData);
      this.logger?.therapeutic("✅ Processamento Contagem de Números concluído com sucesso");
      return result;
    } catch (error2) {
      this.logger?.error("❌ Erro ao processar dados Contagem de Números:", error2);
      throw error2;
    }
  }
  /**
   * Processa métricas específicas do Contagem de Números
   * @param {Object} gameData - Dados do jogo
   * @param {Object} sessionData - Dados da sessão
   * @returns {Object} Métricas processadas
   */
  async processContagemNumerosMetrics(gameData, sessionData) {
    try {
      this.logger?.info("🔢 Processando métricas Contagem de Números...", {
        sessionId: sessionData.sessionId
      });
      const metrics = {
        // Análise de processamento numérico
        numericalProcessing: this.analyzeNumericalProcessing(gameData),
        // Habilidades de contagem
        countingAbilities: this.analyzeCountingAbilities(gameData),
        // Subitização (reconhecimento rápido de pequenas quantidades)
        subitization: this.analyzeSubitization(gameData),
        // Conceitos numéricos
        numericalConcepts: this.analyzeNumericalConcepts(gameData),
        // Estratégias de contagem
        countingStrategies: this.analyzeCountingStrategies(gameData),
        // Precisão numérica
        numericalAccuracy: this.analyzeNumericalAccuracy(gameData),
        // Velocidade de processamento numérico
        numericalSpeed: this.analyzeNumericalSpeed(gameData),
        // Indicadores de dificuldades matemáticas
        mathematicalConcerns: this.identifyMathematicalConcerns(gameData),
        // Recomendações específicas
        recommendations: this.generateNumericalRecommendations(gameData)
      };
      this.logger?.info("✅ Métricas Contagem de Números processadas", {
        accuracy: metrics.numericalAccuracy.overallAccuracy,
        countingAbility: metrics.countingAbilities.level,
        processingSpeed: metrics.numericalSpeed.category
      });
      return metrics;
    } catch (error2) {
      this.logger?.error("❌ Erro ao processar métricas Contagem de Números:", error2);
      throw error2;
    }
  }
  /**
   * Analisa processamento numérico geral
   */
  analyzeNumericalProcessing(gameData) {
    const { interactions = [], totalCorrect = 0, totalAttempts = 1 } = gameData;
    return {
      overallAccuracy: Math.round(totalCorrect / totalAttempts * 100),
      numberRecognition: this.assessNumberRecognition(interactions),
      quantityDiscrimination: this.assessQuantityDiscrimination(interactions),
      numericalComparison: this.assessNumericalComparison(interactions),
      processing_efficiency: this.calculateProcessingEfficiency(interactions)
    };
  }
  /**
   * Analisa habilidades de contagem
   */
  analyzeCountingAbilities(gameData) {
    const { interactions = [], difficulty = "easy" } = gameData;
    const countingAccuracy = this.calculateCountingAccuracy(interactions);
    const countingRange = this.assessCountingRange(interactions);
    const countingStrategy = this.identifyCountingStrategy(interactions);
    return {
      level: this.determineCoutingLevel(countingAccuracy, countingRange),
      accuracy: countingAccuracy,
      range: countingRange,
      strategy: countingStrategy,
      consistency: this.assessCountingConsistency(interactions),
      errorPatterns: this.identifyCountingErrorPatterns(interactions)
    };
  }
  /**
   * Analisa subitização (reconhecimento rápido de quantidades pequenas)
   */
  analyzeSubitization(gameData) {
    const { interactions = [] } = gameData;
    const smallQuantities = interactions.filter(
      (i) => i.targetQuantity && i.targetQuantity <= 4
    );
    const fastResponses = smallQuantities.filter(
      (i) => i.responseTime && i.responseTime < 1500 && i.correct
    );
    return {
      subitizationRange: this.determineSubitizationRange(smallQuantities),
      fastRecognition: fastResponses.length,
      accuracy: smallQuantities.length > 0 ? fastResponses.length / smallQuantities.length : 0,
      averageResponseTime: this.calculateAverageResponseTime(smallQuantities),
      abilityLevel: this.assessSubitizationAbility(fastResponses, smallQuantities)
    };
  }
  /**
   * Analisa conceitos numéricos
   */
  analyzeNumericalConcepts(gameData) {
    const { interactions = [] } = gameData;
    return {
      oneToOneCorrespondence: this.assessOneToOneCorrespondence(interactions),
      numberSequence: this.assessNumberSequenceUnderstanding(interactions),
      cardinality: this.assessCardinalityUnderstanding(interactions),
      magnitude: this.assessMagnitudeUnderstanding(interactions),
      numberLine: this.assessNumberLineUnderstanding(interactions),
      conceptualDevelopment: this.assessConceptualDevelopment(interactions)
    };
  }
  /**
   * Analisa estratégias de contagem utilizadas
   */
  analyzeCountingStrategies(gameData) {
    const { interactions = [] } = gameData;
    return {
      primaryStrategy: this.identifyPrimaryCountingStrategy(interactions),
      strategyFlexibility: this.assessStrategyFlexibility(interactions),
      adaptiveStrategy: this.assessAdaptiveStrategyUse(interactions),
      efficiency: this.assessStrategyEfficiency(interactions),
      development: this.assessStrategyDevelopment(interactions)
    };
  }
  /**
   * Analisa precisão numérica
   */
  analyzeNumericalAccuracy(gameData) {
    const { interactions = [], totalCorrect = 0, totalAttempts = 1 } = gameData;
    const accuracyByRange = this.calculateAccuracyByNumberRange(interactions);
    const errorAnalysis = this.analyzeNumericalErrors(interactions);
    return {
      overallAccuracy: Math.round(totalCorrect / totalAttempts * 100),
      accuracyByRange,
      errorPatterns: errorAnalysis.patterns,
      errorTypes: errorAnalysis.types,
      improvementTrend: this.calculateAccuracyTrend(interactions)
    };
  }
  /**
   * Analisa velocidade de processamento numérico
   */
  analyzeNumericalSpeed(gameData) {
    const { interactions = [] } = gameData;
    const responseTimes = interactions.map((i) => i.responseTime).filter((t) => t && t > 0);
    if (responseTimes.length === 0) {
      return { category: "insufficient_data", average: 0 };
    }
    const average = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const speedByQuantity = this.analyzeSpeedByQuantity(interactions);
    return {
      average: Math.round(average),
      category: this.categorizeProcessingSpeed(average),
      speedByQuantity,
      processingEfficiency: this.calculateNumericalProcessingEfficiency(interactions),
      speedTrend: this.calculateSpeedTrend(responseTimes)
    };
  }
  /**
   * Identifica preocupações matemáticas
   */
  identifyMathematicalConcerns(gameData) {
    const { interactions = [], accuracy = 0 } = gameData;
    return {
      dyscalculiaRisk: this.assessDyscalculiaRisk(interactions, accuracy),
      numberSenseDeficit: this.assessNumberSenseDeficit(interactions),
      countingDifficulties: this.identifyCountingDifficulties(interactions),
      conceptualGaps: this.identifyConceptualGaps(interactions),
      processingDelays: this.identifyProcessingDelays(interactions)
    };
  }
  /**
   * Gera recomendações específicas
   */
  generateNumericalRecommendations(gameData) {
    const recommendations = [];
    const { accuracy = 0, interactions = [] } = gameData;
    if (accuracy < 50) {
      recommendations.push({
        type: "foundational_support",
        priority: "high",
        action: "Reforçar conceitos básicos de contagem e correspondência um-para-um",
        rationale: "Baixa precisão indica necessidade de fundamentos sólidos"
      });
    }
    const avgSpeed = this.calculateAverageResponseTime(interactions);
    if (avgSpeed > 5e3) {
      recommendations.push({
        type: "fluency_development",
        priority: "medium",
        action: "Exercícios de fluência numérica e reconhecimento rápido",
        rationale: "Velocidade baixa indica necessidade de automatização"
      });
    }
    const errorPatterns = this.identifyCountingErrorPatterns(interactions);
    if (errorPatterns.sequenceErrors > 2) {
      recommendations.push({
        type: "sequence_training",
        priority: "medium",
        action: "Prática intensiva da sequência numérica",
        rationale: "Múltiplos erros de sequência detectados"
      });
    }
    return recommendations;
  }
  // Métodos auxiliares de análise
  assessNumberRecognition(interactions) {
    const recognitionTasks = interactions.filter((i) => i.taskType === "recognition");
    const correct = recognitionTasks.filter((i) => i.correct);
    return recognitionTasks.length > 0 ? Math.round(correct.length / recognitionTasks.length * 100) : 0;
  }
  assessQuantityDiscrimination(interactions) {
    const discriminationTasks = interactions.filter(
      (i) => i.taskType === "discrimination" || i.requiresComparison
    );
    const correct = discriminationTasks.filter((i) => i.correct);
    return discriminationTasks.length > 0 ? Math.round(correct.length / discriminationTasks.length * 100) : 0;
  }
  assessNumericalComparison(interactions) {
    const comparisonTasks = interactions.filter(
      (i) => i.taskType === "comparison" || i.involvesComparison
    );
    const correct = comparisonTasks.filter((i) => i.correct);
    return comparisonTasks.length > 0 ? Math.round(correct.length / comparisonTasks.length * 100) : 0;
  }
  calculateProcessingEfficiency(interactions) {
    const correctInteractions = interactions.filter((i) => i.correct && i.responseTime);
    if (correctInteractions.length === 0) return 0;
    const avgTime = correctInteractions.reduce((sum, i) => sum + i.responseTime, 0) / correctInteractions.length;
    return Math.min(1, 3e3 / avgTime);
  }
  calculateCountingAccuracy(interactions) {
    const countingTasks = interactions.filter(
      (i) => i.taskType === "counting" || i.requiresCounting
    );
    const correct = countingTasks.filter((i) => i.correct);
    return countingTasks.length > 0 ? Math.round(correct.length / countingTasks.length * 100) : 0;
  }
  assessCountingRange(interactions) {
    const quantities = interactions.map((i) => i.targetQuantity).filter((q) => q && q > 0);
    if (quantities.length === 0) return { min: 0, max: 0 };
    return {
      min: Math.min(...quantities),
      max: Math.max(...quantities),
      comfortable: this.findComfortableRange(interactions)
    };
  }
  findComfortableRange(interactions) {
    const rangeAccuracy = {};
    interactions.forEach((interaction) => {
      const quantity = interaction.targetQuantity;
      if (quantity) {
        const range = this.getNumberRange(quantity);
        if (!rangeAccuracy[range]) {
          rangeAccuracy[range] = { correct: 0, total: 0 };
        }
        rangeAccuracy[range].total++;
        if (interaction.correct) {
          rangeAccuracy[range].correct++;
        }
      }
    });
    const comfortableRanges = Object.entries(rangeAccuracy).filter(([_, data]) => data.total > 0 && data.correct / data.total > 0.8).map(([range, _]) => range);
    return comfortableRanges;
  }
  getNumberRange(quantity) {
    if (quantity <= 5) return "1-5";
    if (quantity <= 10) return "6-10";
    if (quantity <= 20) return "11-20";
    return "20+";
  }
  identifyCountingStrategy(interactions) {
    const avgResponseTime = this.calculateAverageResponseTime(interactions);
    const fastResponses = interactions.filter(
      (i) => i.responseTime && i.responseTime < 2e3
    ).length;
    if (fastResponses / interactions.length > 0.7) {
      return "subitization";
    } else if (avgResponseTime < 3e3) {
      return "efficient_counting";
    } else {
      return "systematic_counting";
    }
  }
  determineCoutingLevel(accuracy, range) {
    if (accuracy < 50) return "emergent";
    if (accuracy < 70 || range.max <= 5) return "developing";
    if (accuracy < 85 || range.max <= 10) return "proficient";
    return "advanced";
  }
  assessCountingConsistency(interactions) {
    const accuracies = this.calculateRollingAccuracy(interactions, 5);
    const variance = this.calculateVariance(accuracies);
    return variance < 0.1 ? "high" : variance < 0.2 ? "moderate" : "low";
  }
  calculateRollingAccuracy(interactions, windowSize) {
    const accuracies = [];
    for (let i = 0; i <= interactions.length - windowSize; i++) {
      const window = interactions.slice(i, i + windowSize);
      const correct = window.filter((item) => item.correct).length;
      accuracies.push(correct / windowSize);
    }
    return accuracies;
  }
  calculateVariance(values) {
    if (values.length === 0) return 0;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map((val) => Math.pow(val - mean, 2));
    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
  }
  identifyCountingErrorPatterns(interactions) {
    const errors = interactions.filter((i) => !i.correct);
    return {
      sequenceErrors: errors.filter((e) => e.errorType === "sequence").length,
      skippingErrors: errors.filter((e) => e.errorType === "skipping").length,
      double_counting: errors.filter((e) => e.errorType === "double_counting").length,
      magnitude_errors: errors.filter((e) => e.errorType === "magnitude").length
    };
  }
  // Métodos de subitização
  determineSubitizationRange(interactions) {
    const maxQuickRecognition = Math.max(
      ...interactions.filter((i) => i.responseTime < 1500 && i.correct).map((i) => i.targetQuantity || 0)
    );
    return Math.min(maxQuickRecognition, 4);
  }
  calculateAverageResponseTime(interactions) {
    const times = interactions.map((i) => i.responseTime).filter((t) => t && t > 0);
    return times.length > 0 ? times.reduce((sum, time) => sum + time, 0) / times.length : 0;
  }
  assessSubitizationAbility(fastResponses, smallQuantities) {
    if (smallQuantities.length === 0) return "insufficient_data";
    const ratio = fastResponses.length / smallQuantities.length;
    if (ratio > 0.8) return "excellent";
    if (ratio > 0.6) return "good";
    if (ratio > 0.4) return "developing";
    return "emerging";
  }
  // Métodos de conceitos numéricos
  assessOneToOneCorrespondence(interactions) {
    const correspondenceTasks = interactions.filter(
      (i) => i.taskType === "correspondence" || i.requiresCorrespondence
    );
    const correct = correspondenceTasks.filter((i) => i.correct);
    return correspondenceTasks.length > 0 ? Math.round(correct.length / correspondenceTasks.length * 100) : 0;
  }
  assessNumberSequenceUnderstanding(interactions) {
    const sequenceTasks = interactions.filter(
      (i) => i.taskType === "sequence" || i.requiresSequence
    );
    const correct = sequenceTasks.filter((i) => i.correct);
    return sequenceTasks.length > 0 ? Math.round(correct.length / sequenceTasks.length * 100) : 0;
  }
  assessCardinalityUnderstanding(interactions) {
    const cardinalityTasks = interactions.filter(
      (i) => i.taskType === "cardinality" || i.requiresCardinality
    );
    const correct = cardinalityTasks.filter((i) => i.correct);
    return cardinalityTasks.length > 0 ? Math.round(correct.length / cardinalityTasks.length * 100) : 0;
  }
  assessMagnitudeUnderstanding(interactions) {
    const magnitudeTasks = interactions.filter(
      (i) => i.taskType === "magnitude" || i.requiresMagnitude
    );
    const correct = magnitudeTasks.filter((i) => i.correct);
    return magnitudeTasks.length > 0 ? Math.round(correct.length / magnitudeTasks.length * 100) : 0;
  }
  assessNumberLineUnderstanding(interactions) {
    const numberLineTasks = interactions.filter(
      (i) => i.taskType === "numberline" || i.requiresNumberLine
    );
    const correct = numberLineTasks.filter((i) => i.correct);
    return numberLineTasks.length > 0 ? Math.round(correct.length / numberLineTasks.length * 100) : 0;
  }
  assessConceptualDevelopment(interactions) {
    const scores = {
      correspondence: this.assessOneToOneCorrespondence(interactions),
      sequence: this.assessNumberSequenceUnderstanding(interactions),
      cardinality: this.assessCardinalityUnderstanding(interactions),
      magnitude: this.assessMagnitudeUnderstanding(interactions)
    };
    const averageScore = Object.values(scores).reduce((sum, score) => sum + score, 0) / 4;
    if (averageScore > 80) return "advanced";
    if (averageScore > 60) return "proficient";
    if (averageScore > 40) return "developing";
    return "emerging";
  }
  // Métodos adicionais
  identifyPrimaryCountingStrategy(interactions) {
    const avgTime = this.calculateAverageResponseTime(interactions);
    if (avgTime < 2e3) return "subitization";
    if (avgTime < 4e3) return "counting_on";
    return "counting_all";
  }
  assessStrategyFlexibility(interactions) {
    const strategies = interactions.map((i) => this.identifyInteractionStrategy(i));
    const uniqueStrategies = new Set(strategies).size;
    return uniqueStrategies > 1 ? "flexible" : "rigid";
  }
  identifyInteractionStrategy(interaction) {
    if (!interaction.responseTime) return "unknown";
    if (interaction.responseTime < 1500) return "subitization";
    if (interaction.responseTime < 3e3) return "counting_on";
    return "counting_all";
  }
  assessAdaptiveStrategyUse(interactions) {
    const smallNumbers = interactions.filter((i) => i.targetQuantity <= 4);
    const largeNumbers = interactions.filter((i) => i.targetQuantity > 4);
    const avgTimeSmall = this.calculateAverageResponseTime(smallNumbers);
    const avgTimeLarge = this.calculateAverageResponseTime(largeNumbers);
    return avgTimeSmall < avgTimeLarge ? "adaptive" : "non_adaptive";
  }
  assessStrategyEfficiency(interactions) {
    const correctInteractions = interactions.filter((i) => i.correct);
    const avgTime = this.calculateAverageResponseTime(correctInteractions);
    if (avgTime < 2e3) return "high";
    if (avgTime < 4e3) return "moderate";
    return "low";
  }
  assessStrategyDevelopment(interactions) {
    const firstHalf = interactions.slice(0, Math.floor(interactions.length / 2));
    const secondHalf = interactions.slice(Math.floor(interactions.length / 2));
    const firstHalfTime = this.calculateAverageResponseTime(firstHalf);
    const secondHalfTime = this.calculateAverageResponseTime(secondHalf);
    if (secondHalfTime < firstHalfTime * 0.8) return "improving";
    if (secondHalfTime > firstHalfTime * 1.2) return "declining";
    return "stable";
  }
  // Métodos de análise de precisão
  calculateAccuracyByNumberRange(interactions) {
    const ranges = {
      "1-3": { correct: 0, total: 0 },
      "4-6": { correct: 0, total: 0 },
      "7-10": { correct: 0, total: 0 },
      "11+": { correct: 0, total: 0 }
    };
    interactions.forEach((interaction) => {
      const quantity = interaction.targetQuantity;
      if (!quantity) return;
      let range;
      if (quantity <= 3) range = "1-3";
      else if (quantity <= 6) range = "4-6";
      else if (quantity <= 10) range = "7-10";
      else range = "11+";
      ranges[range].total++;
      if (interaction.correct) {
        ranges[range].correct++;
      }
    });
    const result = {};
    Object.entries(ranges).forEach(([range, data]) => {
      result[range] = data.total > 0 ? Math.round(data.correct / data.total * 100) : 0;
    });
    return result;
  }
  analyzeNumericalErrors(interactions) {
    const errors = interactions.filter((i) => !i.correct);
    const patterns = {
      off_by_one: errors.filter((e) => Math.abs((e.response || 0) - (e.targetQuantity || 0)) === 1).length,
      sequence_errors: errors.filter((e) => e.errorType === "sequence").length,
      magnitude_errors: errors.filter((e) => e.errorType === "magnitude").length,
      random_errors: errors.filter((e) => !e.errorType || e.errorType === "random").length
    };
    const types = {
      systematic: patterns.off_by_one + patterns.sequence_errors,
      conceptual: patterns.magnitude_errors,
      careless: patterns.random_errors
    };
    return { patterns, types };
  }
  calculateAccuracyTrend(interactions) {
    if (interactions.length < 6) return "insufficient_data";
    const firstThird = interactions.slice(0, Math.floor(interactions.length / 3));
    const lastThird = interactions.slice(-Math.floor(interactions.length / 3));
    const firstAccuracy = firstThird.filter((i) => i.correct).length / firstThird.length;
    const lastAccuracy = lastThird.filter((i) => i.correct).length / lastThird.length;
    if (lastAccuracy > firstAccuracy + 0.1) return "improving";
    if (lastAccuracy < firstAccuracy - 0.1) return "declining";
    return "stable";
  }
  // Métodos de velocidade
  analyzeSpeedByQuantity(interactions) {
    const speedByQuantity = {};
    interactions.forEach((interaction) => {
      const quantity = interaction.targetQuantity;
      const time = interaction.responseTime;
      if (quantity && time) {
        if (!speedByQuantity[quantity]) {
          speedByQuantity[quantity] = [];
        }
        speedByQuantity[quantity].push(time);
      }
    });
    const result = {};
    Object.entries(speedByQuantity).forEach(([quantity, times]) => {
      result[quantity] = times.reduce((sum, time) => sum + time, 0) / times.length;
    });
    return result;
  }
  categorizeProcessingSpeed(averageTime) {
    if (averageTime < 2e3) return "fast";
    if (averageTime < 4e3) return "normal";
    return "slow";
  }
  calculateNumericalProcessingEfficiency(interactions) {
    const correctInteractions = interactions.filter((i) => i.correct && i.responseTime);
    if (correctInteractions.length === 0) return 0;
    const accuracy = correctInteractions.length / interactions.length;
    const avgTime = this.calculateAverageResponseTime(correctInteractions);
    const timeEfficiency = Math.max(0, (5e3 - avgTime) / 5e3);
    return (accuracy + timeEfficiency) / 2;
  }
  calculateSpeedTrend(responseTimes) {
    if (responseTimes.length < 6) return "insufficient_data";
    const firstHalf = responseTimes.slice(0, Math.floor(responseTimes.length / 2));
    const secondHalf = responseTimes.slice(Math.floor(responseTimes.length / 2));
    const firstAvg = firstHalf.reduce((sum, time) => sum + time, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, time) => sum + time, 0) / secondHalf.length;
    if (secondAvg < firstAvg * 0.8) return "improving";
    if (secondAvg > firstAvg * 1.2) return "declining";
    return "stable";
  }
  // Métodos de identificação de preocupações
  assessDyscalculiaRisk(interactions, accuracy) {
    const riskFactors = {
      lowAccuracy: accuracy < 50,
      sequenceErrors: this.identifyCountingErrorPatterns(interactions).sequenceErrors > 3,
      slowProcessing: this.calculateAverageResponseTime(interactions) > 5e3,
      inconsistentPerformance: this.assessCountingConsistency(interactions) === "low",
      conceptualGaps: this.assessConceptualDevelopment(interactions) === "emerging"
    };
    const riskCount = Object.values(riskFactors).filter(Boolean).length;
    if (riskCount >= 3) return "high";
    if (riskCount >= 2) return "moderate";
    return "low";
  }
  assessNumberSenseDeficit(interactions) {
    const indicators = {
      poorSubitization: this.assessSubitizationAbility([], interactions) === "emerging",
      weakMagnitude: this.assessMagnitudeUnderstanding(interactions) < 50,
      limitedRange: this.assessCountingRange(interactions).max < 10,
      strategicRigidity: this.assessStrategyFlexibility(interactions) === "rigid"
    };
    const indicatorCount = Object.values(indicators).filter(Boolean).length;
    return indicatorCount >= 2 ? "likely" : "unlikely";
  }
  identifyCountingDifficulties(interactions) {
    const difficulties = [];
    const errorPatterns = this.identifyCountingErrorPatterns(interactions);
    if (errorPatterns.sequenceErrors > 2) {
      difficulties.push("sequence_difficulties");
    }
    if (errorPatterns.skippingErrors > 2) {
      difficulties.push("attention_difficulties");
    }
    if (errorPatterns.double_counting > 2) {
      difficulties.push("working_memory_difficulties");
    }
    return difficulties;
  }
  identifyConceptualGaps(interactions) {
    const gaps = [];
    if (this.assessOneToOneCorrespondence(interactions) < 60) {
      gaps.push("one_to_one_correspondence");
    }
    if (this.assessCardinalityUnderstanding(interactions) < 60) {
      gaps.push("cardinality_concept");
    }
    if (this.assessNumberSequenceUnderstanding(interactions) < 60) {
      gaps.push("number_sequence");
    }
    return gaps;
  }
  identifyProcessingDelays(interactions) {
    const avgTime = this.calculateAverageResponseTime(interactions);
    const delays = [];
    if (avgTime > 5e3) {
      delays.push("general_processing_delay");
    }
    const speedByQuantity = this.analyzeSpeedByQuantity(interactions);
    Object.entries(speedByQuantity).forEach(([quantity, time]) => {
      if (parseInt(quantity) <= 4 && time > 3e3) {
        delays.push("subitization_delay");
      }
    });
    return delays;
  }
  /**
   * Processa coletores com Circuit Breaker para resiliência
   * @param {Object} collectorsHub - Hub de coletores
   * @param {Object} gameData - Dados do jogo
   * @returns {Promise<Object>} Resultados dos coletores
   */
  async processCollectorsWithCircuitBreaker(collectorsHub, gameData) {
    if (!collectorsHub || !collectorsHub.collectors) {
      this.logger?.warn("⚠️ ContagemNumeros: Hub de coletores não disponível");
      return { collectors: {}, warning: "No collectors available" };
    }
    const results = {};
    const collectors = collectorsHub.collectors;
    for (const [collectorName, collector] of Object.entries(collectors)) {
      try {
        if (collector && typeof collector.analyze === "function") {
          this.logger?.debug("🎮 Processando coletor: " + collectorName);
          results[collectorName] = await this.processWithTimeout(
            () => collector.analyze(gameData),
            5e3,
            // 5 segundos timeout
            collectorName + " timeout"
          );
        } else {
          this.logger?.warn("⚠️ Coletor " + collectorName + " não tem método analyze");
          results[collectorName] = { error: "No analyze method" };
        }
      } catch (error2) {
        this.logger?.error("❌ Erro no coletor " + collectorName + ":", error2);
        results[collectorName] = {
          error: error2.message,
          fallback: this.generateFallbackMetrics(collectorName, gameData)
        };
      }
    }
    return {
      collectors: results,
      processedAt: (/* @__PURE__ */ new Date()).toISOString(),
      gameType: "ContagemNumeros"
    };
  }
  /**
   * Processa com timeout para evitar travamentos
   */
  async processWithTimeout(fn, timeout, errorMsg) {
    return Promise.race([
      fn(),
      new Promise(
        (_, reject) => setTimeout(() => reject(new Error(errorMsg)), timeout)
      )
    ]);
  }
  /**
   * Gera métricas de fallback em caso de erro
   */
  generateFallbackMetrics(collectorName, gameData) {
    return {
      fallback: true,
      collector: collectorName,
      basicScore: 50,
      confidence: "low",
      note: "Generated due to collector error"
    };
  }
  /**
   * Gera análise integrada combinando processador e coletores
   * @param {Object} processorResults - Resultados do processador
   * @param {Object} collectorsResults - Resultados dos coletores
   * @returns {Object} Análise integrada
   */
  generateIntegratedAnalysis(processorResults, collectorsResults = {}) {
    try {
      const integratedAnalysis = {
        gameType: "ContagemNumeros",
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        sessionMetrics: processorResults,
        collectorInsights: collectorsResults.collectors || {},
        // Análise integrada específica para ContagemNumeros
        gamePerformance: {
          accuracy: processorResults.accuracy || 0,
          responseTime: processorResults.averageResponseTime || 0,
          engagement: this.calculateEngagement(processorResults),
          cognitiveLoad: this.calculateCognitiveLoad(processorResults)
        },
        // Insights terapêuticos
        therapeuticInsights: this.generateTherapeuticInsights(processorResults, collectorsResults),
        // Recomendações baseadas na análise integrada
        recommendations: this.generateRecommendations(processorResults, collectorsResults),
        // Métricas de qualidade da análise
        analysisQuality: {
          dataCompleteness: this.assessDataCompleteness(processorResults, collectorsResults),
          collectorsCoverage: Object.keys(collectorsResults.collectors || {}).length,
          confidence: this.calculateConfidenceScore(processorResults, collectorsResults)
        }
      };
      this.logger?.info("✅ ContagemNumeros: Análise integrada gerada", {
        accuracy: integratedAnalysis.gamePerformance.accuracy,
        collectorsUsed: Object.keys(collectorsResults.collectors || {}).length,
        confidence: integratedAnalysis.analysisQuality.confidence
      });
      return integratedAnalysis;
    } catch (error2) {
      this.logger?.error("❌ Erro ao gerar análise integrada ContagemNumeros:", error2);
      return {
        error: error2.message,
        fallback: true,
        basicMetrics: processorResults,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    }
  }
  /**
   * Métodos auxiliares para análise integrada
   */
  calculateEngagement(results) {
    return Math.min(100, (results.accuracy || 0) + 20);
  }
  calculateCognitiveLoad(results) {
    const time = results.averageResponseTime || 3e3;
    return Math.max(0, Math.min(100, time / 30));
  }
  generateTherapeuticInsights(processorResults, collectorsResults) {
    const insights = [];
    if (processorResults.accuracy < 60) {
      insights.push("Desempenho abaixo do esperado detectado");
    }
    if (processorResults.averageResponseTime > 3e3) {
      insights.push("Tempo de resposta acima da média");
    }
    return insights;
  }
  generateRecommendations(processorResults, collectorsResults) {
    const recommendations = [];
    if (processorResults.accuracy < 70) {
      recommendations.push("Exercícios de reforço recomendados");
    }
    return recommendations;
  }
  assessDataCompleteness(processorResults, collectorsResults) {
    let score = 0;
    if (processorResults.accuracy !== void 0) score += 25;
    if (processorResults.averageResponseTime !== void 0) score += 25;
    if (Object.keys(collectorsResults.collectors || {}).length > 0) score += 50;
    return score;
  }
  calculateConfidenceScore(processorResults, collectorsResults) {
    const dataQuality = this.assessDataCompleteness(processorResults, collectorsResults);
    const collectorCount = Object.keys(collectorsResults.collectors || {}).length;
    return Math.min(100, dataQuality + collectorCount * 5);
  }
  /**
   * Gera análise terapêutica completa
   * @param {Object} metrics - Métricas processadas
   * @param {Object} gameData - Dados do jogo
   * @returns {Object} Análise terapêutica
   */
  generateTherapeuticAnalysis(metrics, gameData) {
    try {
      const analysis = {
        // Análise comportamental
        behavioral: {
          engagement: this.calculateEngagementScore(gameData),
          persistence: this.calculatePersistenceScore(gameData),
          adaptability: this.calculateAdaptabilityScore(gameData),
          frustrationTolerance: this.calculateFrustrationTolerance(gameData),
          socialInteraction: this.calculateSocialInteractionScore(gameData)
        },
        // Análise cognitiva
        cognitive: {
          attention: this.calculateAttentionScore(gameData),
          memory: this.calculateMemoryScore(gameData),
          processingSpeed: this.calculateProcessingSpeedScore(gameData),
          executiveFunction: this.calculateExecutiveFunctionScore(gameData),
          visualProcessing: this.calculateVisualProcessingScore(gameData)
        },
        // Análise sensorial
        sensory: {
          visualPerception: this.calculateVisualPerceptionScore(gameData),
          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),
          tactileProcessing: this.calculateTactileProcessingScore(gameData),
          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)
        },
        // Análise motora
        motor: {
          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),
          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),
          coordination: this.calculateCoordinationScore(gameData),
          motorPlanning: this.calculateMotorPlanningScore(gameData)
        },
        // Recomendações terapêuticas
        recommendations: this.generateTherapeuticRecommendations(metrics, gameData),
        // Indicadores de progresso
        progressIndicators: this.generateProgressIndicators(metrics, gameData),
        // Insights específicos do jogo
        gameSpecificInsights: this.generateGameSpecificInsights(metrics, gameData),
        // Metadados
        metadata: {
          analysisTimestamp: (/* @__PURE__ */ new Date()).toISOString(),
          gameType: this.gameType,
          analysisVersion: "3.0.0",
          confidenceScore: this.calculateAnalysisConfidenceScore(metrics, gameData)
        }
      };
      return analysis;
    } catch (error2) {
      this.logger?.error("❌ Erro ao gerar análise terapêutica:", error2);
      return this.generateFallbackTherapeuticAnalysis(gameData);
    }
  }
  /**
   * Métodos de cálculo de scores terapêuticos
   */
  calculateEngagementScore(gameData) {
    const interactions = gameData.interactions || [];
    const totalTime = gameData.totalTime || 1e3;
    const completionRate = gameData.completionRate || 0;
    let score = 50;
    if (interactions.length > 0) {
      score += Math.min(30, interactions.length * 2);
    }
    if (totalTime > 3e4) {
      score += 10;
    }
    score += completionRate * 10;
    return Math.max(0, Math.min(100, score));
  }
  calculatePersistenceScore(gameData) {
    const attempts = gameData.attempts || 1;
    const errors = gameData.errors || 0;
    const completion = gameData.completion || 0;
    let score = 50;
    if (attempts > 1 && completion > 0.5) {
      score += 20;
    }
    if (errors > 0 && completion > 0.8) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateAdaptabilityScore(gameData) {
    const difficultyChanges = gameData.difficultyChanges || 0;
    const adaptationSuccess = gameData.adaptationSuccess || 0;
    let score = 50;
    if (difficultyChanges > 0) {
      score += adaptationSuccess * 20;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateFrustrationTolerance(gameData) {
    const errors = gameData.errors || 0;
    const quitEarly = gameData.quitEarly || false;
    const completion = gameData.completion || 0;
    let score = 70;
    if (errors > 3 && !quitEarly) {
      score += 15;
    }
    if (completion > 0.8) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateSocialInteractionScore(gameData) {
    const engagement = this.calculateEngagementScore(gameData);
    return Math.max(30, Math.min(80, engagement * 0.8));
  }
  calculateAttentionScore(gameData) {
    const focusTime = gameData.focusTime || 0;
    const distractions = gameData.distractions || 0;
    const responseTime = gameData.averageResponseTime || 3e3;
    let score = 50;
    if (focusTime > 6e4) {
      score += 20;
    }
    if (distractions < 2) {
      score += 15;
    }
    if (responseTime < 2e3) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateMemoryScore(gameData) {
    const accuracy = gameData.accuracy || 0;
    const patterns = gameData.patterns || [];
    let score = 50;
    if (accuracy > 70) {
      score += 25;
    }
    if (patterns.length > 3) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateProcessingSpeedScore(gameData) {
    const responseTime = gameData.averageResponseTime || 3e3;
    const accuracy = gameData.accuracy || 0;
    let score = 50;
    if (responseTime < 1500 && accuracy > 60) {
      score += 30;
    } else if (responseTime < 2500) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateExecutiveFunctionScore(gameData) {
    const planningEvidence = gameData.planningEvidence || 0;
    const inhibitionControl = gameData.inhibitionControl || 0;
    const workingMemory = gameData.workingMemory || 0;
    const score = (planningEvidence + inhibitionControl + workingMemory) / 3;
    return Math.max(0, Math.min(100, score));
  }
  calculateVisualProcessingScore(gameData) {
    const visualTasks = gameData.visualTasks || 0;
    const visualAccuracy = gameData.visualAccuracy || 0;
    let score = 50;
    if (visualTasks > 5 && visualAccuracy > 70) {
      score += 25;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateVisualPerceptionScore(gameData) {
    return this.calculateVisualProcessingScore(gameData);
  }
  calculateAuditoryProcessingScore(gameData) {
    const auditoryTasks = gameData.auditoryTasks || 0;
    const auditoryAccuracy = gameData.auditoryAccuracy || 50;
    let score = 50;
    if (auditoryTasks > 0) {
      score = auditoryAccuracy;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateTactileProcessingScore(gameData) {
    const touchInteractions = gameData.touchInteractions || 0;
    const touchAccuracy = gameData.touchAccuracy || 50;
    let score = 50;
    if (touchInteractions > 3) {
      score = touchAccuracy;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateSensoryIntegrationScore(gameData) {
    const visual = this.calculateVisualPerceptionScore(gameData);
    const auditory = this.calculateAuditoryProcessingScore(gameData);
    const tactile = this.calculateTactileProcessingScore(gameData);
    return (visual + auditory + tactile) / 3;
  }
  calculateFineMotorSkillsScore(gameData) {
    const precision = gameData.precision || 50;
    const motorControl = gameData.motorControl || 50;
    return (precision + motorControl) / 2;
  }
  calculateGrossMotorSkillsScore(gameData) {
    const movements = gameData.movements || 0;
    const coordination = gameData.coordination || 50;
    let score = 50;
    if (movements > 10) {
      score = coordination;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateCoordinationScore(gameData) {
    const eyeHandCoordination = gameData.eyeHandCoordination || 50;
    const bilateralCoordination = gameData.bilateralCoordination || 50;
    return (eyeHandCoordination + bilateralCoordination) / 2;
  }
  calculateMotorPlanningScore(gameData) {
    const planningSteps = gameData.planningSteps || 0;
    const executionSuccess = gameData.executionSuccess || 0;
    let score = 50;
    if (planningSteps > 0) {
      score = executionSuccess;
    }
    return Math.max(0, Math.min(100, score));
  }
  generateTherapeuticRecommendations(metrics, gameData) {
    const recommendations = [];
    const engagement = this.calculateEngagementScore(gameData);
    if (engagement < 50) {
      recommendations.push({
        category: "engagement",
        priority: "high",
        recommendation: "Implementar estratégias de motivação e gamificação",
        rationale: "Baixo engajamento detectado"
      });
    }
    const attention = this.calculateAttentionScore(gameData);
    if (attention < 50) {
      recommendations.push({
        category: "attention",
        priority: "medium",
        recommendation: "Exercícios de foco e concentração",
        rationale: "Dificuldades atencionais identificadas"
      });
    }
    const processing = this.calculateProcessingSpeedScore(gameData);
    if (processing < 50) {
      recommendations.push({
        category: "processing",
        priority: "medium",
        recommendation: "Atividades para melhorar velocidade de processamento",
        rationale: "Processamento lento identificado"
      });
    }
    return recommendations;
  }
  generateProgressIndicators(metrics, gameData) {
    return {
      overallProgress: this.calculateOverallProgress(gameData),
      strengthAreas: this.identifyStrengthAreas(gameData),
      challengeAreas: this.identifyChallengeAreas(gameData),
      developmentGoals: this.generateDevelopmentGoals(gameData),
      milestones: this.generateMilestones(gameData)
    };
  }
  generateGameSpecificInsights(metrics, gameData) {
    return {
      gameType: this.gameType,
      specificMetrics: metrics,
      gamePerformance: this.calculateGamePerformance(gameData),
      adaptationNeeds: this.identifyAdaptationNeeds(gameData)
    };
  }
  calculateAnalysisConfidenceScore(metrics, gameData) {
    let confidence = 50;
    const dataPoints = Object.keys(gameData).length;
    if (dataPoints > 10) confidence += 20;
    else if (dataPoints > 5) confidence += 10;
    const metricsCount = Object.keys(metrics).length;
    if (metricsCount > 5) confidence += 20;
    else if (metricsCount > 3) confidence += 10;
    const sessionTime = gameData.totalTime || 0;
    if (sessionTime > 6e4) confidence += 10;
    return Math.max(0, Math.min(100, confidence));
  }
  generateFallbackTherapeuticAnalysis(gameData) {
    return {
      behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50, socialInteraction: 50 },
      cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50, visualProcessing: 50 },
      sensory: { visualPerception: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },
      motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },
      recommendations: [],
      progressIndicators: { overallProgress: 50, strengthAreas: [], challengeAreas: [], developmentGoals: [], milestones: [] },
      gameSpecificInsights: { gameType: this.gameType, specificMetrics: {}, gamePerformance: 50, adaptationNeeds: [] },
      metadata: { analysisTimestamp: (/* @__PURE__ */ new Date()).toISOString(), gameType: this.gameType, analysisVersion: "3.0.0", confidenceScore: 30 }
    };
  }
  calculateOverallProgress(gameData) {
    const accuracy = gameData.accuracy || 0;
    const completion = gameData.completion || 0;
    const engagement = this.calculateEngagementScore(gameData);
    return (accuracy + completion + engagement) / 3;
  }
  identifyStrengthAreas(gameData) {
    const strengths = [];
    if (gameData.accuracy > 80) strengths.push("Precisão");
    if (gameData.averageResponseTime < 2e3) strengths.push("Velocidade de resposta");
    if (this.calculateEngagementScore(gameData) > 70) strengths.push("Engajamento");
    return strengths;
  }
  identifyChallengeAreas(gameData) {
    const challenges = [];
    if (gameData.accuracy < 50) challenges.push("Precisão");
    if (gameData.averageResponseTime > 4e3) challenges.push("Velocidade de resposta");
    if (this.calculateEngagementScore(gameData) < 40) challenges.push("Engajamento");
    return challenges;
  }
  generateDevelopmentGoals(gameData) {
    const goals = [];
    if (gameData.accuracy < 70) {
      goals.push("Melhorar precisão para 70%+");
    }
    if (gameData.averageResponseTime > 3e3) {
      goals.push("Reduzir tempo de resposta para menos de 3 segundos");
    }
    return goals;
  }
  generateMilestones(gameData) {
    return [
      { milestone: "Primeira sessão completa", achieved: gameData.completion > 0.8 },
      { milestone: "Precisão acima de 50%", achieved: gameData.accuracy > 50 },
      { milestone: "Engajamento sustentado", achieved: this.calculateEngagementScore(gameData) > 60 }
    ];
  }
  calculateGamePerformance(gameData) {
    const accuracy = gameData.accuracy || 0;
    const completion = gameData.completion || 0;
    const efficiency = gameData.efficiency || 0;
    return (accuracy + completion + efficiency) / 3;
  }
  identifyAdaptationNeeds(gameData) {
    const needs = [];
    if (gameData.accuracy < 40) {
      needs.push("Reduzir dificuldade");
    }
    if (gameData.averageResponseTime > 5e3) {
      needs.push("Aumentar tempo limite");
    }
    if (this.calculateEngagementScore(gameData) < 30) {
      needs.push("Aumentar elementos motivacionais");
    }
    return needs;
  }
  /**
   * Processa métricas para estrutura padronizada do banco de dados
   * @param {Object} metrics - Métricas processadas
   * @param {Object} gameData - Dados do jogo
   * @returns {Object} Métricas processadas para banco
   */
  processMetricsForDatabase(metrics, gameData) {
    try {
      return {
        // Métricas básicas
        basic: {
          accuracy: gameData.accuracy || 0,
          responseTime: gameData.averageResponseTime || 0,
          completion: gameData.completion || 0,
          score: gameData.score || 0,
          duration: gameData.totalTime || 0,
          attempts: gameData.attempts || 1,
          errors: gameData.errors || 0
        },
        // Métricas cognitivas
        cognitive: {
          attention: this.calculateAttentionScore(gameData),
          memory: this.calculateMemoryScore(gameData),
          processingSpeed: this.calculateProcessingSpeedScore(gameData),
          executiveFunction: this.calculateExecutiveFunctionScore(gameData)
        },
        // Métricas comportamentais
        behavioral: {
          engagement: this.calculateEngagementScore(gameData),
          persistence: this.calculatePersistenceScore(gameData),
          adaptability: this.calculateAdaptabilityScore(gameData),
          frustrationTolerance: this.calculateFrustrationTolerance(gameData)
        },
        // Métricas sensoriais
        sensory: {
          visualProcessing: this.calculateVisualProcessingScore(gameData),
          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),
          tactileProcessing: this.calculateTactileProcessingScore(gameData),
          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)
        },
        // Métricas motoras
        motor: {
          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),
          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),
          coordination: this.calculateCoordinationScore(gameData),
          motorPlanning: this.calculateMotorPlanningScore(gameData)
        },
        // Métricas específicas do jogo
        gameSpecific: metrics,
        // Metadados
        metadata: {
          gameType: this.gameType,
          processingTimestamp: (/* @__PURE__ */ new Date()).toISOString(),
          version: "3.0.0"
        }
      };
    } catch (error2) {
      this.logger?.error("❌ Erro ao processar métricas para banco:", error2);
      return {
        basic: { accuracy: 0, responseTime: 0, completion: 0, score: 0, duration: 0, attempts: 1, errors: 0 },
        cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50 },
        behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50 },
        sensory: { visualProcessing: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },
        motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },
        gameSpecific: metrics,
        metadata: { gameType: this.gameType, processingTimestamp: (/* @__PURE__ */ new Date()).toISOString(), version: "3.0.0" }
      };
    }
  }
}
const BaseGameConfig = {
  difficulties: [
    {
      id: "easy",
      name: "Fácil",
      description: "Ideal para iniciantes"
    },
    {
      id: "medium",
      name: "Médio",
      description: "Desafio equilibrado"
    },
    {
      id: "hard",
      name: "Avançado",
      description: "Para especialistas"
    }
  ],
  gameSettings: {
    basePoints: 10,
    streakBonus: 5,
    maxWrongOptions: 3,
    feedbackDuration: 2e3,
    celebrationDuration: 3e3,
    initialTtsDelay: 1e3,
    adaptiveDifficulty: true,
    showProgressIndicators: true,
    enableHints: true,
    enableEncouragement: true
  },
  accessibility: {
    tts: {
      enabled: true,
      speed: 0.8,
      pitch: 1,
      languages: ["pt-BR"],
      autoRepeat: false,
      contextualHelp: true
    },
    visual: {
      highContrast: false,
      largeText: false,
      reducedMotion: false,
      colorBlindSupport: true
    },
    motor: {
      largeButtons: false,
      keyboardNavigation: true,
      touchOptimized: true
    }
  },
  encouragingMessages: [
    "Muito bem! Você está indo ótimo! 🎉",
    "Excelente! Continue assim! ⭐",
    "Fantástico! Suas habilidades estão crescendo! 👏"
  ]
};
const createActivityType = (id, name, icon, description, difficultyConfig) => ({
  id,
  name,
  icon,
  description,
  difficulty: difficultyConfig
});
const ContagemNumerosConfig = {
  ...BaseGameConfig,
  gameSettings: {
    ...BaseGameConfig.gameSettings,
    activityBonus: 3,
    activityRotationInterval: 1,
    maxConsecutiveErrors: 3,
    // 🎯 Configuração padronizada de rodadas por atividade
    activitySettings: {
      roundsPerActivity: {
        easy: 4,
        // 4 rodadas para nível fácil
        medium: 5,
        // 5 rodadas para nível médio
        hard: 7
        // 7 rodadas para nível avançado
      }
    },
    // 🎯 Configurações específicas para atividades simples (visual counting, soma simples, etc)
    simpleDifficultyConfigs: {
      easy: {
        name: "Fácil",
        range: [1, 3],
        maxNumber: 5
      },
      medium: {
        name: "Médio",
        range: [1, 5],
        maxNumber: 8
      },
      hard: {
        name: "Avançado",
        range: [1, 8],
        maxNumber: 10
      }
    }
  },
  difficulties: [
    {
      id: "easy",
      name: "Fácil",
      range: [1, 4],
      description: "Contagem de 1 a 5",
      estimationRange: [3, 8],
      sequenceLength: 3,
      comparisonMax: 5,
      patternLength: 4
    },
    {
      id: "medium",
      name: "Médio",
      range: [3, 8],
      description: "Contagem de 3 a 8",
      estimationRange: [5, 12],
      sequenceLength: 4,
      comparisonMax: 8,
      patternLength: 5
    },
    {
      id: "hard",
      name: "Avançado",
      range: [6, 12],
      description: "Contagem de 6 a 12",
      estimationRange: [8, 20],
      sequenceLength: 5,
      comparisonMax: 12,
      patternLength: 6
    }
  ],
  activityTypes: {
    NUMBER_COUNTING: createActivityType(
      "number_counting",
      "Contagem Simples",
      "🔢",
      "Conte os objetos na tela e escolha o número correto",
      {
        easy: { range: [1, 4], objects: "small" },
        medium: { range: [3, 8], objects: "medium" },
        hard: { range: [6, 12], objects: "large" }
      }
    ),
    SOUND_MATCHING: createActivityType(
      "sound_matching",
      "Combinação de Sons",
      "🎵",
      "Associe o som do número com a quantidade correta",
      {
        easy: { range: [1, 4], playSpeed: "slow" },
        medium: { range: [3, 8], playSpeed: "normal" },
        hard: { range: [6, 12], playSpeed: "fast" }
      }
    ),
    NUMBER_ESTIMATION: createActivityType(
      "number_estimation",
      "Estimativa Numérica",
      "🎯",
      "Estime quantidades sem contar individualmente",
      {
        easy: { range: [3, 8], showTime: 4e3, tolerance: 1 },
        medium: { range: [5, 12], showTime: 3e3, tolerance: 1 },
        hard: { range: [8, 20], showTime: 2e3, tolerance: 2 }
      }
    ),
    SEQUENCE_COMPLETION: createActivityType(
      "sequence_completion",
      "Completar Sequência",
      "📝",
      "Complete sequências numéricas crescentes",
      {
        easy: { length: 3, step: 1, maxNumber: 10 },
        medium: { length: 4, step: [1, 2], maxNumber: 15 },
        hard: { length: 5, step: [1, 2, 3], maxNumber: 20 }
      }
    ),
    NUMBER_COMPARISON: createActivityType(
      "number_comparison",
      "Comparação Numérica",
      "🔢",
      "Compare quantidades e identifique maior/menor",
      {
        easy: { maxNumber: 5, groups: 2 },
        medium: { maxNumber: 8, groups: 2 },
        hard: { maxNumber: 12, groups: 3 }
      }
    ),
    PATTERN_RECOGNITION: createActivityType(
      "pattern_recognition",
      "Reconhecimento de Padrões",
      "🔍",
      "Identifique padrões em sequências numéricas",
      {
        easy: { patternLength: 4, complexity: "simple" },
        medium: { patternLength: 5, complexity: "medium" },
        hard: { patternLength: 6, complexity: "complex" }
      }
    )
  },
  sequences: {
    easy: [
      { sequence: [1, 2, 3], missing: 4, options: [4, 5, 6], type: "crescente_simples" },
      { sequence: [2, 3, 4], missing: 5, options: [5, 6, 7], type: "crescente_simples" },
      { sequence: [3, 4, 5], missing: 6, options: [6, 7, 8], type: "crescente_simples" },
      { sequence: [5, 4, 3], missing: 2, options: [2, 1, 6], type: "decrescente_simples" }
    ],
    medium: [
      { sequence: [2, 4, 6], missing: 8, options: [8, 9, 10], type: "pares" },
      { sequence: [1, 3, 5], missing: 7, options: [7, 8, 9], type: "ímpares" },
      { sequence: [5, 6, 7], missing: 8, options: [8, 9, 10], type: "crescente_simples" },
      { sequence: [10, 8, 6], missing: 4, options: [4, 3, 2], type: "decrescente_pares" }
    ],
    hard: [
      { sequence: [3, 6, 9], missing: 12, options: [12, 15, 18], type: "múltiplos_3" },
      { sequence: [2, 5, 8], missing: 11, options: [11, 14, 17], type: "soma_3" },
      { sequence: [10, 9, 8], missing: 7, options: [7, 6, 5], type: "decrescente_simples" },
      { sequence: [1, 4, 7], missing: 10, options: [10, 13, 16], type: "soma_3" }
    ]
  },
  patterns: {
    easy: [
      { pattern: [1, 1, 2, 2], next: [3, 3], description: "Números dobrados", type: "repetição" },
      { pattern: [1, 2, 1, 2], next: [1, 2], description: "Alternância simples", type: "alternância" },
      { pattern: [2, 2, 3, 3], next: [4, 4], description: "Pares crescentes", type: "repetição_crescente" }
    ],
    medium: [
      { pattern: [1, 2, 3, 1, 2], next: [3], description: "Sequência repetida", type: "ciclo" },
      { pattern: [2, 4, 2, 4], next: [2], description: "Par-par alternado", type: "alternância" },
      { pattern: [1, 3, 1, 3], next: [1], description: "Ímpar alternado", type: "alternância_ímpar" }
    ],
    hard: [
      { pattern: [1, 3, 5, 7], next: [9], description: "Números ímpares", type: "progressão_ímpar" },
      { pattern: [2, 4, 8, 16], next: [32], description: "Dobrar o anterior", type: "multiplicação" },
      { pattern: [1, 1, 2, 3], next: [5], description: "Fibonacci simples", type: "fibonacci" }
    ]
  },
  categories: [
    {
      id: "fruits",
      name: "Frutas",
      emoji: "🍎",
      objects: [
        { id: "apple", emoji: "🍎", name: "Maçã" },
        { id: "banana", emoji: "🍌", name: "Banana" },
        { id: "orange", emoji: "🍊", name: "Laranja" },
        { id: "grapes", emoji: "🍇", name: "Uvas" },
        { id: "strawberry", emoji: "🍓", name: "Morango" },
        { id: "pineapple", emoji: "🍍", name: "Abacaxi" }
      ]
    },
    {
      id: "animals",
      name: "Animais",
      emoji: "🐶",
      objects: [
        { id: "dog", emoji: "🐶", name: "Cachorro" },
        { id: "cat", emoji: "🐱", name: "Gato" },
        { id: "rabbit", emoji: "🐰", name: "Coelho" },
        { id: "bear", emoji: "🐻", name: "Urso" },
        { id: "pig", emoji: "🐷", name: "Porco" },
        { id: "cow", emoji: "🐮", name: "Vaca" }
      ]
    },
    {
      id: "toys",
      name: "Brinquedos",
      emoji: "🧸",
      objects: [
        { id: "teddy", emoji: "🧸", name: "Ursinho" },
        { id: "ball", emoji: "⚽", name: "Bola" },
        { id: "car", emoji: "🚗", name: "Carrinho" },
        { id: "doll", emoji: "🪆", name: "Boneca" },
        { id: "blocks", emoji: "🧱", name: "Blocos" },
        { id: "kite", emoji: "🪁", name: "Pipa" }
      ]
    },
    {
      id: "nature",
      name: "Natureza",
      emoji: "🌸",
      objects: [
        { id: "flower", emoji: "🌸", name: "Flor" },
        { id: "tree", emoji: "🌳", name: "Árvore" },
        { id: "sun", emoji: "☀️", name: "Sol" },
        { id: "star", emoji: "⭐", name: "Estrela" },
        { id: "moon", emoji: "🌙", name: "Lua" },
        { id: "cloud", emoji: "☁️", name: "Nuvem" }
      ]
    },
    {
      id: "shapes",
      name: "Formas",
      emoji: "🔴",
      objects: [
        { id: "circle", emoji: "🔴", name: "Círculo" },
        { id: "square", emoji: "🟦", name: "Quadrado" },
        { id: "triangle", emoji: "🔺", name: "Triângulo" },
        { id: "diamond", emoji: "🔶", name: "Losango" },
        { id: "heart", emoji: "❤️", name: "Coração" },
        { id: "star_shape", emoji: "⭐", name: "Estrela" }
      ]
    },
    {
      id: "transport",
      name: "Transporte",
      emoji: "🚗",
      objects: [
        { id: "car", emoji: "🚗", name: "Carro" },
        { id: "bus", emoji: "🚌", name: "Ônibus" },
        { id: "bike", emoji: "🚲", name: "Bicicleta" },
        { id: "plane", emoji: "✈️", name: "Avião" },
        { id: "train", emoji: "🚂", name: "Trem" },
        { id: "boat", emoji: "⛵", name: "Barco" }
      ]
    }
  ],
  // 🎯 CONFIGURAÇÕES ESPECÍFICAS PARA COMPARAÇÃO DE QUANTIDADE V2
  quantityComparisonConfig: {
    // Tipos de desafios de comparação
    challengeTypes: [
      {
        id: "more_than",
        question: "Qual grupo tem MAIS objetos?",
        instruction: "Escolha o grupo com mais objetos",
        tts: "Qual grupo tem mais objetos? Clique no grupo maior.",
        correctSide: "larger"
      },
      {
        id: "less_than",
        question: "Qual grupo tem MENOS objetos?",
        instruction: "Escolha o grupo com menos objetos",
        tts: "Qual grupo tem menos objetos? Clique no grupo menor.",
        correctSide: "smaller"
      },
      {
        id: "equal_check",
        question: "Os grupos têm a MESMA quantidade?",
        instruction: "Clique em SIM se forem iguais, NÃO se diferentes",
        tts: "Os dois grupos têm a mesma quantidade de objetos?",
        correctSide: "equality"
      },
      {
        id: "count_difference",
        question: "Quantos objetos A MAIS tem o grupo maior?",
        instruction: "Conte a diferença entre os grupos",
        tts: "Quantos objetos a mais tem o grupo com mais objetos?",
        correctSide: "difference"
      }
    ],
    // Configuração de dificuldade para comparação
    difficultySettings: {
      easy: {
        maxNumber: 5,
        minNumber: 1,
        maxDifference: 2,
        allowedTypes: ["more_than", "less_than"],
        equalityChance: 0.2
      },
      medium: {
        maxNumber: 8,
        minNumber: 2,
        maxDifference: 4,
        allowedTypes: ["more_than", "less_than", "equal_check"],
        equalityChance: 0.25
      },
      hard: {
        maxNumber: 12,
        minNumber: 3,
        maxDifference: 6,
        allowedTypes: ["more_than", "less_than", "equal_check", "count_difference"],
        equalityChance: 0.3
      }
    },
    // Mensagens contextuais expandidas
    encouragementMessages: {
      more_than: [
        "Perfeito! Você identificou o grupo maior! 📈",
        "Excelente! Você sabe qual tem mais! 🎯",
        "Ótima observação! Grupo maior identificado! 👀"
      ],
      less_than: [
        "Muito bem! Você encontrou o grupo menor! 📉",
        "Correto! Você identificou o menor grupo! 🔍",
        "Perfeito! Menor quantidade identificada! ✨"
      ],
      equal_check: [
        "Incrível! Você percebeu que são iguais! ⚖️",
        "Excelente! Mesma quantidade identificada! 🎯",
        "Perfeito! Você tem um ótimo olho para igualdade! 👁️"
      ],
      count_difference: [
        "Fantástico! Você calculou a diferença! 🧮",
        "Perfeito! Matemática de subtração excelente! ➖",
        "Incrível! Você domina as diferenças numéricas! 🔢"
      ]
    }
  },
  activityMessages: {
    number_counting: {
      instructions: [
        "Conte quantos {object}s você vê na tela!",
        "Quantos {object}s há aqui?",
        "Vamos contar os {object}s juntos!"
      ],
      encouragement: [
        "Ótima contagem!",
        "Você sabe contar muito bem!",
        "Perfeito! Continue contando!"
      ]
    },
    sound_matching: {
      instructions: [
        "Ouça o número e encontre a quantidade correspondente!",
        "Escute com atenção e escolha a resposta certa!",
        "O que você ouviu? Encontre a quantidade!"
      ],
      encouragement: [
        "Excelente audição!",
        "Você ouve muito bem!",
        "Perfeita associação de som!"
      ]
    },
    number_estimation: {
      instructions: [
        "Estime quantos há sem contar um por um!",
        "Olhe rapidamente e faça uma estimativa!",
        "Quanto você acha que tem aqui?"
      ],
      encouragement: [
        "Ótima estimativa!",
        "Você tem um bom olho para números!",
        "Estimativa perfeita!"
      ]
    },
    sequence_completion: {
      instructions: [
        "Complete a sequência numérica!",
        "Qual número vem a seguir?",
        "Continue o padrão da sequência!"
      ],
      encouragement: [
        "Excelente lógica sequencial!",
        "Você entende padrões muito bem!",
        "Sequência perfeita!"
      ]
    },
    number_comparison: {
      instructions: [
        "Compare as quantidades e escolha a resposta!",
        "Qual grupo tem mais? Qual tem menos?",
        "Compare e decida!",
        "Observe bem os dois grupos!",
        "Use seus olhos de matemático!"
      ],
      encouragement: [
        "Ótima comparação!",
        "Você sabe comparar números!",
        "Comparação perfeita!",
        "Excelente olho matemático!",
        "Você domina as comparações!"
      ]
    },
    pattern_recognition: {
      instructions: [
        "Identifique o padrão e continue!",
        "Que padrão você vê aqui?",
        "Complete o padrão numérico!"
      ],
      encouragement: [
        "Excelente reconhecimento de padrão!",
        "Você é ótimo com padrões!",
        "Padrão identificado perfeitamente!"
      ]
    }
  },
  // 🎯 GERADOR AVANÇADO DE DADOS DE COMPARAÇÃO V2
  generateQuantityComparisonData: (difficulty = "easy") => {
    const settings = ContagemNumerosConfig.quantityComparisonConfig.difficultySettings[difficulty];
    const challengeTypes = ContagemNumerosConfig.quantityComparisonConfig.challengeTypes;
    const allowedTypes = settings.allowedTypes;
    const categories = ContagemNumerosConfig.categories;
    const selectedType = allowedTypes[Math.floor(Math.random() * allowedTypes.length)];
    const challengeConfig = challengeTypes.find((type) => type.id === selectedType);
    let count1, count2;
    const shouldBeEqual = Math.random() < settings.equalityChance && selectedType === "equal_check";
    if (shouldBeEqual) {
      count1 = Math.floor(Math.random() * (settings.maxNumber - settings.minNumber + 1)) + settings.minNumber;
      count2 = count1;
    } else {
      count1 = Math.floor(Math.random() * (settings.maxNumber - settings.minNumber + 1)) + settings.minNumber;
      count2 = Math.floor(Math.random() * (settings.maxNumber - settings.minNumber + 1)) + settings.minNumber;
      while (count1 === count2 || Math.abs(count1 - count2) > settings.maxDifference) {
        count2 = Math.floor(Math.random() * (settings.maxNumber - settings.minNumber + 1)) + settings.minNumber;
      }
    }
    const category1 = categories[Math.floor(Math.random() * categories.length)];
    let category2 = categories[Math.floor(Math.random() * categories.length)];
    while (category2.id === category1.id) {
      category2 = categories[Math.floor(Math.random() * categories.length)];
    }
    const generateGroupObjects = (category, count) => {
      const objects = [];
      for (let i = 0; i < count; i++) {
        const randomObject = category.objects[Math.floor(Math.random() * category.objects.length)];
        objects.push({
          id: i,
          emoji: randomObject.emoji,
          name: randomObject.name,
          category: category.name
        });
      }
      return objects;
    };
    const group1 = generateGroupObjects(category1, count1);
    const group2 = generateGroupObjects(category2, count2);
    let correctAnswer, explanation;
    switch (selectedType) {
      case "more_than":
        correctAnswer = count1 > count2 ? "left" : "right";
        explanation = `Grupo ${correctAnswer === "left" ? "A" : "B"} tem mais objetos (${Math.max(count1, count2)} > ${Math.min(count1, count2)})`;
        break;
      case "less_than":
        correctAnswer = count1 < count2 ? "left" : "right";
        explanation = `Grupo ${correctAnswer === "left" ? "A" : "B"} tem menos objetos (${Math.min(count1, count2)} < ${Math.max(count1, count2)})`;
        break;
      case "equal_check":
        correctAnswer = count1 === count2 ? "equal" : "different";
        explanation = count1 === count2 ? `Ambos os grupos têm ${count1} objetos` : `Grupos diferentes: ${count1} ≠ ${count2}`;
        break;
      case "count_difference":
        const difference = Math.abs(count1 - count2);
        correctAnswer = difference.toString();
        explanation = `Diferença: |${count1} - ${count2}| = ${difference}`;
        break;
      default:
        correctAnswer = count1 > count2 ? "left" : "right";
        explanation = `Comparação padrão: ${count1} vs ${count2}`;
    }
    return {
      challengeType: selectedType,
      challengeConfig,
      group1: {
        objects: group1,
        count: count1,
        category: category1.name,
        categoryEmoji: category1.emoji
      },
      group2: {
        objects: group2,
        count: count2,
        category: category2.name,
        categoryEmoji: category2.emoji
      },
      correctAnswer,
      explanation,
      instruction: challengeConfig.question,
      ttsInstruction: challengeConfig.tts,
      difficulty,
      metadata: {
        difference: Math.abs(count1 - count2),
        isEqual: count1 === count2,
        larger: count1 > count2 ? "left" : "right",
        smaller: count1 < count2 ? "left" : "right"
      }
    };
  },
  encouragingMessages: [
    ...BaseGameConfig.encouragingMessages,
    "Perfeito! Você é ótimo com atividades numéricas! 🔢",
    "Incrível! Você domina diferentes tipos de atividades! 🌟"
  ],
  gameInfo: {
    title: "Contagem de Números V3",
    description: "Desenvolva habilidades matemáticas com 6 atividades diversificadas",
    icon: "🎯",
    category: "mathematics",
    ageRange: "3-8",
    skills: [
      "contagem básica",
      "estimativa numérica",
      "sequências",
      "comparação",
      "padrões",
      "associação sonora",
      "raciocínio lógico",
      "matemática básica"
    ],
    version: "3.1.0",
    activities: 6
  },
  categoryNames: {
    fruits: "frutas",
    animals: "animais",
    toys: "brinquedos",
    nature: "elementos da natureza",
    shapes: "formas geométricas",
    transport: "meios de transporte"
  }
};
const contagemNumerosGame = "_contagemNumerosGame_gekck_47";
const gameContent = "_gameContent_gekck_73";
const gameArea = "_gameArea_gekck_93";
const gameHeader = "_gameHeader_gekck_125";
const gameTitle = "_gameTitle_gekck_153";
const gameStats = "_gameStats_gekck_243";
const statCard = "_statCard_gekck_257";
const statValue = "_statValue_gekck_301";
const statLabel = "_statLabel_gekck_315";
const activityMenu = "_activityMenu_gekck_329";
const activityButton = "_activityButton_gekck_345";
const active = "_active_gekck_383";
const answerButton = "_answerButton_gekck_527";
const optionNumber = "_optionNumber_gekck_585";
const gameControls = "_gameControls_gekck_645";
const controlButton = "_controlButton_gekck_661";
const objectsDisplay = "_objectsDisplay_gekck_731";
const countingObject = "_countingObject_gekck_763";
const additionDisplay = "_additionDisplay_gekck_925";
const additionNumber = "_additionNumber_gekck_943";
const additionOperator = "_additionOperator_gekck_961";
const additionResult = "_additionResult_gekck_971";
const targetNumber = "_targetNumber_gekck_991";
const comparisonGroups = "_comparisonGroups_gekck_1017";
const comparisonGroup = "_comparisonGroup_gekck_1017";
const groupLabel = "_groupLabel_gekck_1067";
const groupObjects = "_groupObjects_gekck_1081";
const groupObject = "_groupObject_gekck_1081";
const feedbackOverlay = "_feedbackOverlay_gekck_1123";
const feedbackContent = "_feedbackContent_gekck_1149";
const feedbackMessage = "_feedbackMessage_gekck_1165";
const success = "_success_gekck_1177";
const error = "_error_gekck_1187";
const countingObjectsGrid = "_countingObjectsGrid_gekck_1219";
const writtenNumberDisplay = "_writtenNumberDisplay_gekck_1239";
const writtenNumberText = "_writtenNumberText_gekck_1249";
const sequenceDisplay = "_sequenceDisplay_gekck_1275";
const sequenceNumber = "_sequenceNumber_gekck_1293";
const sequenceArrow = "_sequenceArrow_gekck_1313";
const sequencePlaceholder = "_sequencePlaceholder_gekck_1323";
const comparisonDisplay = "_comparisonDisplay_gekck_1345";
const comparisonNumber = "_comparisonNumber_gekck_1363";
const comparisonVs = "_comparisonVs_gekck_1391";
const patternDisplay = "_patternDisplay_gekck_1405";
const dotsPattern = "_dotsPattern_gekck_1415";
const dot = "_dot_gekck_1415";
const dicePattern = "_dicePattern_gekck_1455";
const diceEmoji = "_diceEmoji_gekck_1463";
const fingersPattern = "_fingersPattern_gekck_1475";
const finger = "_finger_gekck_1475";
const questionArea = "_questionArea_gekck_1579";
const questionTitle = "_questionTitle_gekck_1599";
const answerOptions = "_answerOptions_gekck_1689";
const answerGrid = "_answerGrid_gekck_3445";
const equalityButtons = "_equalityButtons_gekck_3805";
const styles = {
  contagemNumerosGame,
  gameContent,
  gameArea,
  gameHeader,
  gameTitle,
  gameStats,
  statCard,
  statValue,
  statLabel,
  activityMenu,
  activityButton,
  active,
  answerButton,
  optionNumber,
  gameControls,
  controlButton,
  objectsDisplay,
  countingObject,
  additionDisplay,
  additionNumber,
  additionOperator,
  additionResult,
  targetNumber,
  comparisonGroups,
  comparisonGroup,
  groupLabel,
  groupObjects,
  groupObject,
  feedbackOverlay,
  feedbackContent,
  feedbackMessage,
  success,
  error,
  countingObjectsGrid,
  writtenNumberDisplay,
  writtenNumberText,
  sequenceDisplay,
  sequenceNumber,
  sequenceArrow,
  sequencePlaceholder,
  comparisonDisplay,
  comparisonNumber,
  comparisonVs,
  patternDisplay,
  dotsPattern,
  dot,
  dicePattern,
  diceEmoji,
  fingersPattern,
  finger,
  questionArea,
  questionTitle,
  answerOptions,
  answerGrid,
  equalityButtons
};
const ACTIVITY_TYPES = {
  VISUAL_COUNTING: {
    id: "visual_counting",
    name: "Contagem Visual",
    description: "Conte os objetos que você vê",
    icon: "👁️"
  },
  SIMPLE_ADDITION: {
    id: "simple_addition",
    name: "Soma Simples",
    description: "Some números pequenos",
    icon: "➕"
  },
  NUMBER_RECOGNITION: {
    id: "number_recognition",
    name: "Reconhecimento",
    description: "Encontre o número correto",
    icon: "🔍"
  },
  QUANTITY_COMPARISON: {
    id: "quantity_comparison",
    name: "Comparação",
    description: "Qual tem mais?",
    icon: "⚖️"
  }
};
const ContagemNumerosGame = ({ onBack }) => {
  const { user, sessionId } = reactExports.useContext(SystemContext);
  const {
    ttsActive,
    toggleTTS,
    speak,
    speakGameInstructions,
    speakFeedback
  } = useStandardTTS("contagem_numeros");
  const gameInstructions = "Conte os objetos na tela e escolha o número correto. Use os botões para selecionar sua resposta.";
  const [gameState, setGameState] = reactExports.useState({
    status: "start",
    // 'start', 'playing', 'finished'
    score: 0,
    round: 1,
    totalRounds: 4,
    // Usar sistema baseado na dificuldade (4-7 rodadas)
    difficulty: "easy",
    accuracy: 100,
    roundStartTime: null,
    // Sistema de atividades controlado pelo usuário
    currentActivity: ACTIVITY_TYPES.VISUAL_COUNTING.id,
    userControlledActivities: true,
    // Usuário escolhe as atividades
    // Dados específicos de atividades - PERSISTENTES
    activityData: {
      visual_counting: {
        objects: [],
        correctCount: 0,
        options: [],
        instruction: ""
      },
      simple_addition: {
        number1: 0,
        number2: 0,
        correctAnswer: 0,
        options: [],
        instruction: ""
      },
      number_recognition: {
        targetNumber: 0,
        options: [],
        instruction: ""
      },
      quantity_comparison: {
        group1: [],
        group2: [],
        correctAnswer: "",
        instruction: ""
      }
    }
  });
  const [showStartScreen, setShowStartScreen] = reactExports.useState(true);
  const [gameStarted, setGameStarted] = reactExports.useState(false);
  const [feedback, setFeedback] = reactExports.useState(null);
  const [collectorsHub] = reactExports.useState(() => new NumberCountingCollectorsHub());
  const {
    startUnifiedSession
  } = useUnifiedGameLogic("contagem-numeros");
  const {
    initMultisensory
  } = useMultisensoryIntegration(sessionId, {
    learningStyle: user?.profile?.learningStyle || "visual"
  });
  reactExports.useEffect(() => {
    if (gameState.status === "playing" && ttsActive) {
      setTimeout(() => {
        speakGameInstructions("Contagem de Números", gameInstructions);
      }, 1e3);
    }
  }, [gameState.status, ttsActive, speakGameInstructions, gameInstructions]);
  const generateActivityData = reactExports.useCallback((activityId, difficulty) => {
    const config = ContagemNumerosConfig.gameSettings.simpleDifficultyConfigs[difficulty];
    switch (activityId) {
      case ACTIVITY_TYPES.VISUAL_COUNTING.id:
        return generateVisualCountingData(config);
      case ACTIVITY_TYPES.SIMPLE_ADDITION.id:
        return generateSimpleAdditionData(config);
      case ACTIVITY_TYPES.NUMBER_RECOGNITION.id:
        return generateNumberRecognitionData(config);
      case ACTIVITY_TYPES.QUANTITY_COMPARISON.id:
        return generateQuantityComparisonData(config);
      default:
        return generateVisualCountingData(config);
    }
  }, []);
  const generateVisualCountingData = reactExports.useCallback((config) => {
    const correctCount = Math.floor(Math.random() * (config.range[1] - config.range[0] + 1)) + config.range[0];
    const categories = ContagemNumerosConfig.categories;
    const randomCategory = categories[Math.floor(Math.random() * categories.length)];
    const randomObject = randomCategory.objects[Math.floor(Math.random() * randomCategory.objects.length)];
    const objects = Array.from({ length: correctCount }, (_, index) => ({
      id: index,
      emoji: randomObject.emoji,
      name: randomObject.name
    }));
    const options = /* @__PURE__ */ new Set([correctCount]);
    while (options.size < 4) {
      const wrongOption = Math.floor(Math.random() * config.maxNumber) + 1;
      if (wrongOption !== correctCount && wrongOption > 0) {
        options.add(wrongOption);
      }
    }
    return {
      objects,
      correctCount,
      options: Array.from(options).sort(() => Math.random() - 0.5),
      instruction: `Conte quantos ${randomObject.name.toLowerCase()}s você vê na tela`
    };
  }, []);
  const generateSimpleAdditionData = reactExports.useCallback((config) => {
    const number1 = Math.floor(Math.random() * config.range[1]) + 1;
    const number2 = Math.floor(Math.random() * (config.range[1] - number1)) + 1;
    const correctAnswer = number1 + number2;
    const options = /* @__PURE__ */ new Set([correctAnswer]);
    while (options.size < 4) {
      const wrongOption = Math.floor(Math.random() * config.maxNumber) + 1;
      if (wrongOption !== correctAnswer && wrongOption > 0) {
        options.add(wrongOption);
      }
    }
    return {
      number1,
      number2,
      correctAnswer,
      options: Array.from(options).sort(() => Math.random() - 0.5),
      instruction: `Quanto é ${number1} + ${number2}?`
    };
  }, []);
  const generateNumberRecognitionData = reactExports.useCallback((config) => {
    const challengeTypes = ["visual_count", "written_number", "sequence", "comparison", "pattern"];
    let availableTypes = challengeTypes;
    if (gameState.difficulty === "easy") {
      availableTypes = ["visual_count", "written_number"];
    } else if (gameState.difficulty === "medium") {
      availableTypes = ["visual_count", "written_number", "sequence", "pattern"];
    }
    const challengeType = availableTypes[Math.floor(Math.random() * availableTypes.length)];
    switch (challengeType) {
      case "visual_count": {
        const targetNumber2 = Math.floor(Math.random() * config.maxNumber) + 1;
        const categories = ContagemNumerosConfig.categories;
        const randomCategory = categories[Math.floor(Math.random() * categories.length)];
        const randomObject = randomCategory.objects[Math.floor(Math.random() * randomCategory.objects.length)];
        const objects = Array.from({ length: targetNumber2 }, (_, i) => ({
          id: i,
          emoji: randomObject.emoji,
          name: randomObject.name
        }));
        const options = generateNumberOptions(targetNumber2, config.maxNumber);
        return {
          challengeType: "visual_count",
          targetNumber: targetNumber2,
          objects,
          options,
          instruction: `Conte os ${randomObject.name.toLowerCase()}s e encontre o número correto`
        };
      }
      case "written_number": {
        const targetNumber2 = Math.floor(Math.random() * config.maxNumber) + 1;
        const numberWords = ["", "um", "dois", "três", "quatro", "cinco", "seis", "sete", "oito", "nove", "dez"];
        const options = generateNumberOptions(targetNumber2, config.maxNumber);
        return {
          challengeType: "written_number",
          targetNumber: targetNumber2,
          writtenNumber: numberWords[targetNumber2] || targetNumber2.toString(),
          options,
          instruction: `Encontre o número que representa: "${numberWords[targetNumber2]}"`
        };
      }
      case "sequence": {
        const targetNumber2 = Math.floor(Math.random() * (config.maxNumber - 1)) + 2;
        const beforeNumber = targetNumber2 - 1;
        const options = generateNumberOptions(targetNumber2, config.maxNumber);
        return {
          challengeType: "sequence",
          targetNumber: targetNumber2,
          beforeNumber,
          options,
          instruction: ``
        };
      }
      case "comparison": {
        const number1 = Math.floor(Math.random() * config.maxNumber) + 1;
        let number2 = Math.floor(Math.random() * config.maxNumber) + 1;
        while (number2 === number1) {
          number2 = Math.floor(Math.random() * config.maxNumber) + 1;
        }
        const targetNumber2 = Math.max(number1, number2);
        const options = generateNumberOptions(targetNumber2, config.maxNumber);
        return {
          challengeType: "comparison",
          targetNumber: targetNumber2,
          number1,
          number2,
          options,
          instruction: `Qual é o maior: ${number1} ou ${number2}?`
        };
      }
      case "pattern": {
        const targetNumber2 = Math.floor(Math.random() * Math.min(6, config.maxNumber)) + 1;
        const patternTypes = ["dots", "dice", "fingers"];
        const patternType = patternTypes[Math.floor(Math.random() * patternTypes.length)];
        const options = generateNumberOptions(targetNumber2, config.maxNumber);
        return {
          challengeType: "pattern",
          targetNumber: targetNumber2,
          patternType,
          options,
          instruction: `Quantos pontos você vê?`
        };
      }
      default: {
        const targetNumber2 = Math.floor(Math.random() * config.maxNumber) + 1;
        const options = generateNumberOptions(targetNumber2, config.maxNumber);
        return {
          challengeType: "basic",
          targetNumber: targetNumber2,
          options,
          instruction: `Encontre o número ${targetNumber2}`
        };
      }
    }
  }, [gameState.difficulty]);
  const generateNumberOptions = reactExports.useCallback((correctNumber, maxNumber) => {
    const options = /* @__PURE__ */ new Set([correctNumber]);
    let attempts = 0;
    while (options.size < 4 && attempts < 20) {
      let wrongOption;
      if (Math.random() < 0.5) {
        const offset = (Math.random() < 0.5 ? -1 : 1) * (Math.floor(Math.random() * 2) + 1);
        wrongOption = correctNumber + offset;
      } else {
        wrongOption = Math.floor(Math.random() * maxNumber) + 1;
      }
      if (wrongOption > 0 && wrongOption <= maxNumber && wrongOption !== correctNumber) {
        options.add(wrongOption);
      }
      attempts++;
    }
    while (options.size < 4) {
      const wrongOption = Math.floor(Math.random() * maxNumber) + 1;
      if (wrongOption !== correctNumber) {
        options.add(wrongOption);
      }
    }
    return Array.from(options).sort(() => Math.random() - 0.5);
  }, []);
  const generateQuantityComparisonData = reactExports.useCallback((config) => {
    return ContagemNumerosConfig.generateQuantityComparisonData(config.difficulty || gameState.difficulty || "easy");
  }, [gameState.difficulty]);
  const startGame = reactExports.useCallback(async (selectedDifficulty) => {
    const initialData = generateActivityData(ACTIVITY_TYPES.VISUAL_COUNTING.id, selectedDifficulty);
    const roundsConfig = {
      easy: 4,
      medium: 5,
      hard: 7
    };
    const totalRounds = roundsConfig[selectedDifficulty] || 4;
    setGameState((prev) => ({
      ...prev,
      status: "playing",
      difficulty: selectedDifficulty,
      totalRounds,
      round: 1,
      // Resetar para rodada 1
      roundStartTime: Date.now(),
      activityData: {
        ...prev.activityData,
        [ACTIVITY_TYPES.VISUAL_COUNTING.id]: initialData
      }
    }));
    setShowStartScreen(false);
    setGameStarted(true);
    if (startUnifiedSession) {
      startUnifiedSession(selectedDifficulty);
    }
    try {
      if (initMultisensory && typeof initMultisensory === "function") {
        await initMultisensory(sessionId || `session_${Date.now()}`, {
          difficulty: selectedDifficulty,
          gameMode: "number_counting_v4",
          userId: user?.id || "anonymous"
        });
      }
    } catch (error2) {
      console.warn("⚠️ Erro ao inicializar sessão multissensorial:", error2);
    }
    setTimeout(() => {
      speakGameInstructions("Contagem de Números", `${initialData.instruction}`);
    }, ContagemNumerosConfig.gameSettings.initialTtsDelay);
  }, [generateActivityData, startUnifiedSession, initMultisensory, sessionId, user, speak]);
  const handleAnswer = reactExports.useCallback((answer) => {
    const currentActivityData = gameState.activityData[gameState.currentActivity];
    let isCorrect = false;
    let correctAnswer = null;
    switch (gameState.currentActivity) {
      case ACTIVITY_TYPES.VISUAL_COUNTING.id:
        correctAnswer = currentActivityData.correctCount;
        isCorrect = answer === correctAnswer;
        break;
      case ACTIVITY_TYPES.SIMPLE_ADDITION.id:
        correctAnswer = currentActivityData.correctAnswer;
        isCorrect = answer === correctAnswer;
        break;
      case ACTIVITY_TYPES.NUMBER_RECOGNITION.id:
        correctAnswer = currentActivityData.targetNumber;
        isCorrect = answer === correctAnswer;
        break;
      case ACTIVITY_TYPES.QUANTITY_COMPARISON.id:
        correctAnswer = currentActivityData.correctAnswer;
        isCorrect = answer === correctAnswer;
        break;
      default:
        isCorrect = false;
    }
    setGameState((prev) => {
      const newScore = isCorrect ? prev.score + ContagemNumerosConfig.gameSettings.basePoints : prev.score;
      const newRound = prev.round + 1;
      const totalAttempts = prev.round;
      const correctAnswers = Math.floor(prev.score / ContagemNumerosConfig.gameSettings.basePoints) + (isCorrect ? 1 : 0);
      const newAccuracy = totalAttempts > 0 ? Math.round(correctAnswers / totalAttempts * 100) : 100;
      return {
        ...prev,
        score: newScore,
        round: newRound,
        accuracy: newAccuracy
      };
    });
    setFeedback({
      isCorrect,
      message: isCorrect ? "Muito bem! 🎉" : `Não foi dessa vez. A resposta era ${correctAnswer}`,
      correctAnswer
    });
    const collectionData = {
      activity: gameState.currentActivity,
      isCorrect,
      responseTime: Date.now() - gameState.roundStartTime,
      difficulty: gameState.difficulty,
      round: gameState.round,
      answer,
      correctAnswer,
      questionData: currentActivityData,
      timestamp: Date.now()
    };
    try {
      collectorsHub.collectData(collectionData);
    } catch (error2) {
      console.warn("⚠️ Erro na coleta de dados dos collectors:", error2);
    }
    if (isCorrect) {
      speakFeedback(true);
    } else {
      speakFeedback(false, `Não foi dessa vez. A resposta correta era ${correctAnswer}`);
    }
    setTimeout(() => {
      setFeedback(null);
      generateNewRound();
    }, 2500);
  }, [gameState, speak]);
  const generateNewRound = reactExports.useCallback(() => {
    const currentActivity = gameState.currentActivity;
    const newData = generateActivityData(currentActivity, gameState.difficulty);
    setGameState((prev) => ({
      ...prev,
      round: prev.round + 1,
      roundStartTime: Date.now(),
      activityData: {
        ...prev.activityData,
        [currentActivity]: newData
      }
    }));
    setTimeout(() => {
      speak(newData.instruction, { rate: 0.8 });
    }, 500);
  }, [gameState, generateActivityData, speak]);
  const switchActivity = reactExports.useCallback((activityId) => {
    if (activityId === gameState.currentActivity) return;
    const newData = generateActivityData(activityId, gameState.difficulty);
    setGameState((prev) => ({
      ...prev,
      currentActivity: activityId,
      roundStartTime: Date.now(),
      activityData: {
        ...prev.activityData,
        [activityId]: newData
      }
    }));
    setTimeout(() => {
      speak(newData.instruction, { rate: 0.8 });
    }, 500);
  }, [gameState, generateActivityData, speak]);
  const renderVisualCounting = () => {
    const data = gameState.activityData.visual_counting;
    if (!data || !data.objects) {
      const initialData = generateActivityData(ACTIVITY_TYPES.VISUAL_COUNTING.id, gameState.difficulty);
      setGameState((prev) => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          [ACTIVITY_TYPES.VISUAL_COUNTING.id]: initialData
        }
      }));
      return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: "Gerando atividade..." }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 593,
        columnNumber: 14
      }, void 0);
    }
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionArea, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionHeader, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: styles.questionTitle, children: data.instruction }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 599,
        columnNumber: 11
      }, void 0) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 598,
        columnNumber: 9
      }, void 0),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.objectsDisplay, children: data.objects.map((obj, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "div",
        {
          className: styles.countingObject,
          style: { animationDelay: `${index * 0.1}s` },
          children: obj.emoji
        },
        obj.id,
        false,
        {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 605,
          columnNumber: 13
        },
        void 0
      )) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 603,
        columnNumber: 9
      }, void 0),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.answerOptions, children: data.options.map((option, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          className: styles.answerButton,
          onClick: () => handleAnswer(option),
          children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.optionNumber, children: option }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 623,
            columnNumber: 15
          }, void 0)
        },
        index,
        false,
        {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 618,
          columnNumber: 13
        },
        void 0
      )) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 616,
        columnNumber: 9
      }, void 0)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
      lineNumber: 597,
      columnNumber: 7
    }, void 0);
  };
  const renderSimpleAddition = () => {
    const data = gameState.activityData.simple_addition;
    if (!data || !data.options) {
      const initialData = generateActivityData(ACTIVITY_TYPES.SIMPLE_ADDITION.id, gameState.difficulty);
      setGameState((prev) => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          [ACTIVITY_TYPES.SIMPLE_ADDITION.id]: initialData
        }
      }));
      return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: "Gerando atividade..." }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 645,
        columnNumber: 14
      }, void 0);
    }
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionArea, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionHeader, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: styles.questionTitle, children: data.instruction }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 651,
        columnNumber: 11
      }, void 0) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 650,
        columnNumber: 9
      }, void 0),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.objectsDisplay, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.additionDisplay, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.additionNumber, children: data.number1 }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 657,
          columnNumber: 13
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.additionOperator, children: "+" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 658,
          columnNumber: 13
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.additionNumber, children: data.number2 }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 659,
          columnNumber: 13
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.additionOperator, children: "=" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 660,
          columnNumber: 13
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.additionResult, children: "?" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 661,
          columnNumber: 13
        }, void 0)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 656,
        columnNumber: 11
      }, void 0) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 655,
        columnNumber: 9
      }, void 0),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.answerOptions, children: data.options.map((option, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          className: styles.answerButton,
          onClick: () => handleAnswer(option),
          children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.optionNumber, children: option }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 673,
            columnNumber: 15
          }, void 0)
        },
        index,
        false,
        {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 668,
          columnNumber: 13
        },
        void 0
      )) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 666,
        columnNumber: 9
      }, void 0)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
      lineNumber: 649,
      columnNumber: 7
    }, void 0);
  };
  const renderNumberRecognition = () => {
    const data = gameState.activityData.number_recognition;
    if (!data || !data.options) {
      const initialData = generateActivityData(ACTIVITY_TYPES.NUMBER_RECOGNITION.id, gameState.difficulty);
      setGameState((prev) => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          [ACTIVITY_TYPES.NUMBER_RECOGNITION.id]: initialData
        }
      }));
      return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: "Gerando atividade..." }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 696,
        columnNumber: 14
      }, void 0);
    }
    const renderChallengeContent = () => {
      switch (data.challengeType) {
        case "visual_count":
          return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.objectsDisplay, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.countingObjectsGrid, children: data.objects?.map((obj, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "div",
            {
              className: styles.countingObject,
              style: { animationDelay: `${index * 0.1}s` },
              children: obj.emoji
            },
            obj.id,
            false,
            {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
              lineNumber: 706,
              columnNumber: 19
            },
            void 0
          )) }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 704,
            columnNumber: 15
          }, void 0) }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 703,
            columnNumber: 13
          }, void 0);
        case "written_number":
          return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.objectsDisplay, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.writtenNumberDisplay, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.writtenNumberText, children: [
            '"',
            data.writtenNumber,
            '"'
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 722,
            columnNumber: 17
          }, void 0) }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 721,
            columnNumber: 15
          }, void 0) }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 720,
            columnNumber: 13
          }, void 0);
        case "sequence":
          return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.objectsDisplay, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.sequenceDisplay, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.sequenceNumber, children: data.beforeNumber }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
              lineNumber: 731,
              columnNumber: 17
            }, void 0),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.sequenceArrow, children: "→" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
              lineNumber: 732,
              columnNumber: 17
            }, void 0),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.sequencePlaceholder, children: "?" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
              lineNumber: 733,
              columnNumber: 17
            }, void 0)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 730,
            columnNumber: 15
          }, void 0) }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 729,
            columnNumber: 13
          }, void 0);
        case "comparison":
          return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.objectsDisplay, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.comparisonDisplay, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.comparisonNumber, children: data.number1 }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
              lineNumber: 742,
              columnNumber: 17
            }, void 0),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.comparisonVs, children: "VS" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
              lineNumber: 743,
              columnNumber: 17
            }, void 0),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.comparisonNumber, children: data.number2 }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
              lineNumber: 744,
              columnNumber: 17
            }, void 0)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 741,
            columnNumber: 15
          }, void 0) }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 740,
            columnNumber: 13
          }, void 0);
        case "pattern":
          return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.objectsDisplay, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.patternDisplay, children: renderPattern(data.patternType, data.targetNumber) }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 752,
            columnNumber: 15
          }, void 0) }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 751,
            columnNumber: 13
          }, void 0);
        default:
          return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.objectsDisplay, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.targetNumber, children: data.targetNumber }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 761,
            columnNumber: 15
          }, void 0) }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 760,
            columnNumber: 13
          }, void 0);
      }
    };
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionArea, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionHeader, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: styles.questionTitle, children: data.instruction }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 772,
        columnNumber: 11
      }, void 0) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 771,
        columnNumber: 9
      }, void 0),
      renderChallengeContent(),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.answerOptions, children: data.options.map((option, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          className: styles.answerButton,
          onClick: () => handleAnswer(option),
          children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.optionNumber, children: option }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 785,
            columnNumber: 15
          }, void 0)
        },
        index,
        false,
        {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 780,
          columnNumber: 13
        },
        void 0
      )) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 778,
        columnNumber: 9
      }, void 0)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
      lineNumber: 770,
      columnNumber: 7
    }, void 0);
  };
  const renderPattern = reactExports.useCallback((patternType, number) => {
    switch (patternType) {
      case "dots":
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.dotsPattern, children: Array.from({ length: number }, (_, i) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.dot, children: "●" }, i, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 800,
          columnNumber: 15
        }, void 0)) }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 798,
          columnNumber: 11
        }, void 0);
      case "dice":
        const dicePatterns = {
          1: "⚀",
          2: "⚁",
          3: "⚂",
          4: "⚃",
          5: "⚄",
          6: "⚅"
        };
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.dicePattern, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.diceEmoji, children: dicePatterns[number] || "⚀" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 811,
          columnNumber: 13
        }, void 0) }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 810,
          columnNumber: 11
        }, void 0);
      case "fingers":
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.fingersPattern, children: Array.from({ length: number }, (_, i) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.finger, children: "👆" }, i, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 819,
          columnNumber: 15
        }, void 0)) }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 817,
          columnNumber: 11
        }, void 0);
      default:
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: number }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 825,
          columnNumber: 16
        }, void 0);
    }
  }, []);
  const renderQuantityComparison = () => {
    const data = gameState.activityData.quantity_comparison;
    if (!data || !data.group1 || !data.group2) {
      const initialData = generateActivityData(ACTIVITY_TYPES.QUANTITY_COMPARISON.id, gameState.difficulty);
      setGameState((prev) => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          [ACTIVITY_TYPES.QUANTITY_COMPARISON.id]: initialData
        }
      }));
      return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: "Gerando atividade..." }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 843,
        columnNumber: 14
      }, void 0);
    }
    const handleComparisonAnswer = (answer) => {
      let isCorrect = false;
      switch (data.challengeType) {
        case "more_than":
        case "less_than":
          isCorrect = data.correctAnswer === answer;
          break;
        case "equal_check":
          isCorrect = data.correctAnswer === answer;
          break;
        case "count_difference":
          isCorrect = data.correctAnswer === answer;
          break;
        default:
          isCorrect = data.correctAnswer === answer;
      }
      handleAnswer(isCorrect ? data.correctAnswer : "wrong");
    };
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionArea, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionHeader, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: styles.questionTitle, children: data.instruction }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 871,
        columnNumber: 11
      }, void 0) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 870,
        columnNumber: 9
      }, void 0),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.objectsDisplay, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.comparisonGroups, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              className: styles.comparisonGroup,
              onClick: () => handleComparisonAnswer("left"),
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.groupLabel, children: "Grupo A" }, void 0, false, {
                  fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
                  lineNumber: 881,
                  columnNumber: 15
                }, void 0),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.groupObjects, children: data.group1.objects.map((obj, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                  "span",
                  {
                    className: styles.groupObject,
                    children: obj.emoji
                  },
                  `group1-${obj.id}-${index}`,
                  false,
                  {
                    fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
                    lineNumber: 884,
                    columnNumber: 19
                  },
                  void 0
                )) }, void 0, false, {
                  fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
                  lineNumber: 882,
                  columnNumber: 15
                }, void 0)
              ]
            },
            void 0,
            true,
            {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
              lineNumber: 877,
              columnNumber: 13
            },
            void 0
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              className: styles.comparisonGroup,
              onClick: () => handleComparisonAnswer("right"),
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.groupLabel, children: "Grupo B" }, void 0, false, {
                  fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
                  lineNumber: 898,
                  columnNumber: 15
                }, void 0),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.groupObjects, children: data.group2.objects.map((obj, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
                  "span",
                  {
                    className: styles.groupObject,
                    children: obj.emoji
                  },
                  `group2-${obj.id}-${index}`,
                  false,
                  {
                    fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
                    lineNumber: 901,
                    columnNumber: 19
                  },
                  void 0
                )) }, void 0, false, {
                  fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
                  lineNumber: 899,
                  columnNumber: 15
                }, void 0)
              ]
            },
            void 0,
            true,
            {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
              lineNumber: 894,
              columnNumber: 13
            },
            void 0
          )
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 876,
          columnNumber: 11
        }, void 0),
        data.challengeType === "equal_check" && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.equalityButtons, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              className: styles.answerButton,
              onClick: () => handleComparisonAnswer("equal"),
              children: "✅ SIM, são iguais"
            },
            void 0,
            false,
            {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
              lineNumber: 915,
              columnNumber: 15
            },
            void 0
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              className: styles.answerButton,
              onClick: () => handleComparisonAnswer("different"),
              children: "❌ NÃO, são diferentes"
            },
            void 0,
            false,
            {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
              lineNumber: 921,
              columnNumber: 15
            },
            void 0
          )
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 914,
          columnNumber: 13
        }, void 0),
        data.challengeType === "count_difference" && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.answerGrid, children: [0, 1, 2, 3, 4, 5, 6].map((num) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            className: styles.answerButton,
            onClick: () => handleComparisonAnswer(num.toString()),
            children: num
          },
          num,
          false,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 934,
            columnNumber: 17
          },
          void 0
        )) }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 932,
          columnNumber: 13
        }, void 0)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 875,
        columnNumber: 9
      }, void 0)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
      lineNumber: 869,
      columnNumber: 7
    }, void 0);
  };
  const renderCurrentActivity = () => {
    switch (gameState.currentActivity) {
      case ACTIVITY_TYPES.VISUAL_COUNTING.id:
        return renderVisualCounting();
      case ACTIVITY_TYPES.SIMPLE_ADDITION.id:
        return renderSimpleAddition();
      case ACTIVITY_TYPES.NUMBER_RECOGNITION.id:
        return renderNumberRecognition();
      case ACTIVITY_TYPES.QUANTITY_COMPARISON.id:
        return renderQuantityComparison();
      default:
        return renderVisualCounting();
    }
  };
  if (showStartScreen) {
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      GameStartScreen,
      {
        gameTitle: "Contagem de Números",
        gameDescription: "Aprenda matemática de forma divertida e interativa",
        gameIcon: "🔢",
        onStart: startGame,
        onBack,
        difficulties: ContagemNumerosConfig.difficulties.map((diff) => ({
          id: diff.id,
          name: diff.name,
          description: diff.description,
          icon: diff.id === "easy" ? "😊" : diff.id === "medium" ? "🎯" : "🚀"
        }))
      },
      void 0,
      false,
      {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 968,
        columnNumber: 7
      },
      void 0
    );
  }
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.contagemNumerosGame, children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.gameContent, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.gameHeader, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h1", { className: styles.gameTitle, children: [
          "🔢 Contagem de Números V4",
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.7rem", opacity: 0.8, marginTop: "0.25rem" }, children: Object.values(ACTIVITY_TYPES).find((type) => type.id === gameState.currentActivity)?.name || "Atividade" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 992,
            columnNumber: 13
          }, void 0)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 990,
          columnNumber: 11
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          StandardTTSButton,
          {
            ttsActive,
            toggleTTS,
            size: "normal",
            position: "header"
          },
          void 0,
          false,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 996,
            columnNumber: 11
          },
          void 0
        )
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 989,
        columnNumber: 9
      }, void 0),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.gameStats, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statCard, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statValue, children: gameState.score }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 1007,
            columnNumber: 13
          }, void 0),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statLabel, children: "Pontos" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 1008,
            columnNumber: 13
          }, void 0)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 1006,
          columnNumber: 11
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statCard, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statValue, children: gameState.round }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 1011,
            columnNumber: 13
          }, void 0),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statLabel, children: "Rodada" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 1012,
            columnNumber: 13
          }, void 0)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 1010,
          columnNumber: 11
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statCard, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statValue, children: [
            gameState.accuracy || 100,
            "%"
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 1015,
            columnNumber: 13
          }, void 0),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statLabel, children: "Precisão" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
            lineNumber: 1016,
            columnNumber: 13
          }, void 0)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 1014,
          columnNumber: 11
        }, void 0)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 1005,
        columnNumber: 9
      }, void 0),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.activityMenu, children: Object.values(ACTIVITY_TYPES).map((activity) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          className: `${styles.activityButton} ${gameState.currentActivity === activity.id ? styles.active : ""}`,
          onClick: () => switchActivity(activity.id),
          title: activity.description,
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: activity.icon }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
              lineNumber: 1031,
              columnNumber: 15
            }, void 0),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: activity.name }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
              lineNumber: 1032,
              columnNumber: 15
            }, void 0)
          ]
        },
        activity.id,
        true,
        {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 1023,
          columnNumber: 13
        },
        void 0
      )) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 1021,
        columnNumber: 9
      }, void 0),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.gameArea, children: renderCurrentActivity() }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 1038,
        columnNumber: 9
      }, void 0),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.gameControls, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("button", { className: styles.controlButton, onClick: () => speakGameInstructions("Contagem de Números", "Aprenda matemática de forma divertida contando objetos e resolvendo operações."), children: "🔊 Explicar" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 1044,
          columnNumber: 11
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("button", { className: styles.controlButton, onClick: () => setShowStartScreen(true), children: "🔄 Reiniciar" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 1047,
          columnNumber: 11
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("button", { className: styles.controlButton, onClick: () => {
          speak(TTS_MESSAGES.NAVIGATION.LEAVING_GAME);
          setTimeout(onBack, 1e3);
        }, children: "⬅️ Voltar" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
          lineNumber: 1050,
          columnNumber: 11
        }, void 0)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
        lineNumber: 1043,
        columnNumber: 9
      }, void 0)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
      lineNumber: 987,
      columnNumber: 7
    }, void 0),
    feedback && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `${styles.feedbackOverlay} ${feedback.isCorrect ? styles.success : styles.error}`, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.feedbackContent, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.feedbackMessage, children: feedback.message }, void 0, false, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
      lineNumber: 1060,
      columnNumber: 13
    }, void 0) }, void 0, false, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
      lineNumber: 1059,
      columnNumber: 11
    }, void 0) }, void 0, false, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
      lineNumber: 1058,
      columnNumber: 9
    }, void 0)
  ] }, void 0, true, {
    fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/ContagemNumeros/ContagemNumerosGame.jsx",
    lineNumber: 986,
    columnNumber: 5
  }, void 0);
};
const ContagemNumerosGame$1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: ContagemNumerosGame
}, Symbol.toStringTag, { value: "Module" }));
export {
  ContagemNumerosProcessors as C,
  NumberCountingCollectorsHub as N,
  ContagemNumerosGame$1 as a
};
//# sourceMappingURL=game-numbers-CLPWZorL.js.map
