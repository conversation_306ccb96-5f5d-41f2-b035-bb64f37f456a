{"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,SAAS,OAAO,EAAE,eAAe;AAExB;AACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACFA,SAAS,wBAAwB,EAAE,YAAAA,aAAY,oBAAoB;AAC3D,8BAAsB,CAAC,YAAY,kBAAkB;AACzD,QAAI,kBAAkB;AACpB,uBAAiB,UAAU;AAAA;AAAA,EAE/B;AAEA,QAAM,oBAAoB;AAAA,IACxB;AAAA,MACE,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA;AAAA,EAEX;AAEM,4BAAoBA,aAAY,SAASA,cAAa;AAE5D,SACGC,4CAAA,aAAQ,WAAWC,SAAO,WACzB;AAAA,IAAAD,4CAAC,MAAG,aAAWC,SAAO,OAAO,UAA7B;AAAA;AAAA;AAAA;AAAA,IAEA;AAAA,IAEAD,4CAAC,SAAI,WAAWC,SAAO,MACpB,UAAkB,sBAAI,CAAC,UAAU,UAChCD,qCAAA;AAAA,MAAC;AAAA;AAAA,QAEC,WAAWC,SAAO;AAAA,QAClB,SAAS,MAAM,oBAAoB,SAAS,IAAI,SAAS,KAAK;AAAA,QAC9D,OAAO,EAAE,gBAAgB,GAAG,QAAQ,GAAG,IAAI;AAAA,QAE3C;AAAA,UAACD,qCAAA,gBAAI,WAAWC,SAAO,YACrB;AAAA,YAAAD,4CAAC,OAAI,aAAWC,SAAO,MAAO,mBAAS,QAAvC;AAAA;AAAA;AAAA;AAAA,YAA4C;AAAA,YAC3CD,4CAAA,UAAK,WAAW,GAAGC,SAAO,KAAK,IAAIA,SAAO,SAAS,KAAK,KAAKA,SAAO,IAAI,IACtE,mBAAS,MADZ;AAAA;AAAA;AAAA;AAAA,eAEA;AAAA,YAJF;AAAA;AAAA;AAAA;AAAA,UAKA;AAAA,sDAEC,MAAG,aAAWA,SAAO,WAAY,mBAAS,SAA3C;AAAA;AAAA;AAAA;AAAA,UAAiD;AAAA,sDAChD,KAAE,aAAWA,SAAO,iBAAkB,mBAAS,eAAhD;AAAA;AAAA;AAAA;AAAA,aAA4D;AAAA;AAAA;AAAA,MAbvD,SAAS;AAAA,MADhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAgBD,EAlBH;AAAA;AAAA;AAAA;AAAA,OAmBA;AAAA,IAxBF;AAAA;AAAA;AAAA;AAAA,EAyBA;AAEJ;;;;;;;;;;;;;;;;;;;;;;;;;AC/GA,SAAS,iBAAiB;AACxB,QAAM,SAAS;AAEf,QAAM,mBAAmB,CAAC,SAAS,OAAO,cAAc;AAChD,yBAAe,SAAS,cAAc,KAAK;AACpC,6BAAY,gBAAgB,IAAI;AAC7C,iBAAa,cAAc;AAEpB,kBAAO,aAAa,OAAO;AAAA,MAChC,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,SAAS;AAAA,MACT,cAAc;AAAA,MACd,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,KAAK;AAAA,KACN;AAED,UAAM,SAAS;AAAA,MACb,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AACA,iBAAa,MAAM,aAAa,OAAO,IAAI,KAAK,OAAO;AAEjD,UAAAC,QAAO,SAAS,cAAc,MAAM;AACrC,IAAAA,MAAA,cAAc,SAAS,YAAY,MAAM;AAC9C,iBAAa,QAAQA,KAAI;AAEhB,kBAAK,YAAY,YAAY;AAEtC,eAAW,MAAM;AACf,mBAAa,MAAM,YAAY;AAC/B,mBAAa,MAAM,UAAU;AAAA,OAC5B,GAAG;AAEN,eAAW,MAAM;AACf,mBAAa,MAAM,YAAY;AAC/B,mBAAa,MAAM,UAAU;AAC7B,iBAAW,MAAM,aAAa,OAAO,GAAG,GAAG;AAAA,OAC1C,GAAI;AAAA,EACT;AAEA,QAAM,UAAU,MAAM;AACpB,cAAU,UAAU,UAAU,MAAM,EAAE,KAAK,MAAM;AAC/C,uBAAiB,kCAAkC,SAAS;AAAA,KAC7D,EAAE,MAAM,MAAM;AACb,uBAAiB,4BAA4B,OAAO;AAAA,KACrD;AAAA,EACH;AAGE,SAAAF,qCAAA;AAAA,IAAC,OAAO;AAAA,IAAP;AAAA,MACC,WAAWC,SAAO;AAAA,MAClB,SAAS,EAAE,SAAS,GAAG,GAAG,GAAG;AAAA,MAC7B,SAAS,EAAE,SAAS,GAAG,GAAG,EAAE;AAAA,MAC5B,YAAY,EAAE,UAAU,IAAI;AAAA,MAC5B,MAAK;AAAA,MACL,cAAW;AAAA,MAEX;AAAA,QAACD,qCAAA,gBAAI,WAAWC,SAAO,eACrB,sDAAC,MAAG,aAAWA,SAAO,cAAc,UAApC;AAAA;AAAA;AAAA;AAAA,eAEA,EAHF;AAAA;AAAA;AAAA;AAAA,QAIA;AAAA,QAECD,qCAAA,eAAG,WAAWC,SAAO,OAAO;AAAA;AAAA,sDAE1B,QAAK,aAAWA,SAAO,OAAO,eAAY,QAAO,UAAlD;AAAA;AAAA;AAAA;AAAA,aAAoD;AAAA,UAFtD;AAAA;AAAA;AAAA;AAAA,QAGA;AAAA,QAECD,qCAAA,cAAE,WAAWC,SAAO,MAAM;AAAA;AAAA,UAGzBD,qCAAA,OAAC,YAAO,UAAR;AAAA;AAAA;AAAA;AAAA,aAAqC;AAAA,UAHvC;AAAA;AAAA;AAAA;AAAA,QAIA;AAAA,QAECA,qCAAA,gBAAI,WAAWC,SAAO,WACrB;AAAA,UAACD,qCAAA,gBAAI,WAAWC,SAAO,aACrB,UAAAD,qCAAA;AAAA,YAAC,OAAO;AAAA,YAAP;AAAA,cACC,KAAK,iEAAiE,mBAAmB,MAAM,CAAC;AAAA,cAChG,KAAI;AAAA,cACJ,WAAWC,SAAO;AAAA,cAClB,OAAM;AAAA,cACN,SAAS;AAAA,cACT,YAAY,EAAE,OAAO,KAAK;AAAA,cAC1B,UAAU,EAAE,OAAO,KAAK;AAAA,cACxB,MAAK;AAAA,cACL,UAAU;AAAA,cACV,WAAW,CAAC,MAAM,EAAE,QAAQ,WAAW,QAAQ;AAAA;AAAA,YAVjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YADF;AAAA;AAAA;AAAA;AAAA,UAaA;AAAA,UAEAD,4CAAC,SAAI,WAAWC,SAAO,cACrB,UAACD,qCAAA,eAAG,WAAWC,SAAO,cACpB;AAAA,YAAAD,qCAAA,OAAC,QAAG,UAAJ;AAAA;AAAA;AAAA;AAAA,YAAoC;AAAA,YACpCA,qCAAA,OAAC,QAAG,UAAJ;AAAA;AAAA;AAAA;AAAA,YAA8C;AAAA,YAC9CA,qCAAA,OAAC,QAAG,UAAJ;AAAA;AAAA;AAAA;AAAA,eAAkC;AAAA,YAHpC;AAAA;AAAA;AAAA;AAAA,iBAIA,EALF;AAAA;AAAA;AAAA;AAAA,aAMA;AAAA,UAtBF;AAAA;AAAA;AAAA;AAAA,WAuBA;AAAA;AAAA;AAAA,IAhDF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiDA;AAEJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/GA,SAAS,aAAa,EAAE,gBAAgB;AAAG,QAAM,kBAAkB,CAAC,QAAQ,WAAW,WAAW,WAAW;AACzG,QAAI,cAAc;AAChB,mBAAa,MAAM;AAAA;AAAA,EAGvB;AACA,QAAM,QAAQ;AAAA,IACZ;AAAA,MACE,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA;AAAA,EAEV;AAEA,SACGA,4CAAA,aAAQ,WAAWC,SAAO,WACzB;AAAA,IAAAD,4CAAC,MAAG,aAAWC,SAAO,OAAO,UAA7B;AAAA;AAAA;AAAA;AAAA,IAEA;AAAA,IAEAD,4CAAC,SAAI,WAAWC,SAAO,MACpB,UAAM,UAAI,CAAC,MAAM,UAChBD,qCAAA;AAAA,MAAC;AAAA;AAAA,QAEC,WAAW,GAAGC,SAAO,IAAI,IAAIA,SAAO,KAAK,KAAK,KAAKA,SAAO,IAAI;AAAA,QAC9D,SAAS,MAAM,gBAAgB,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI;AAAA,QAC7D,OAAO,EAAE,gBAAgB,GAAG,QAAQ,GAAG,IAAI;AAAA,QAE3C;AAAA,UAACD,qCAAA,gBAAI,WAAWC,SAAO,YACrB;AAAA,YAAAD,4CAAC,OAAI,aAAWC,SAAO,MAAO,eAAK,QAAnC;AAAA;AAAA;AAAA;AAAA,YAAwC;AAAA,YACxCD,qCAAA,OAAC,QAAK,aAAW,GAAGC,SAAO,KAAK,IAAIA,SAAO,QAAQ,KAAK,MAAM,OAAO,CAAC,EAAE,YAAgB,SAAK,MAAM,MAAM,CAAC,CAAC,EAAE,KAAKA,SAAO,SAAS,IAC/H,eAAK,MADR;AAAA;AAAA;AAAA;AAAA,eAEA;AAAA,YAJF;AAAA;AAAA;AAAA;AAAA,UAKA;AAAA,sDAEC,MAAG,aAAWA,SAAO,WAAY,eAAK,SAAvC;AAAA;AAAA;AAAA;AAAA,UAA6C;AAAA,sDAC5C,KAAE,aAAWA,SAAO,iBAAkB,eAAK,eAA5C;AAAA;AAAA;AAAA;AAAA,aAAwD;AAAA;AAAA;AAAA,MAbnD,KAAK;AAAA,MADZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAgBD,EAlBH;AAAA;AAAA;AAAA;AAAA,OAmBA;AAAA,IAxBF;AAAA;AAAA;AAAA;AAAA,EAyBA;AAEJ;;;;;;;;;;;;;;;;;AClFA,MAAM,aAAa;AAAA,EACjB,EAAE,IAAI,QAAQ,MAAM,UAAU,MAAM,KAAK;AAAA,EACzC,EAAE,IAAI,sBAAsB,MAAM,UAAU,MAAM,KAAK;AAAA,EACvD,EAAE,IAAI,mBAAmB,MAAM,WAAW,MAAM,KAAK;AAAA,EACrD,EAAE,IAAI,eAAe,MAAM,WAAW,MAAM,KAAK;AAAA,EACjD,EAAE,IAAI,oBAAoB,MAAM,UAAU,MAAM,KAAK;AAAA,EACrD,EAAE,IAAI,eAAe,MAAM,SAAS,MAAM,KAAK;AAAA,EAC/C,EAAE,IAAI,qBAAqB,MAAM,WAAW,MAAM,MAAM;AAAA,EACxD,EAAE,IAAI,mBAAmB,MAAM,WAAW,MAAM,KAAK;AAAA,EACrD,EAAE,IAAI,iBAAiB,MAAM,UAAU,MAAM,KAAK;AAAA,EAClD,EAAE,IAAI,qBAAqB,MAAM,QAAQ,MAAM,KAAK;AACtD;AAKA,SAAS,OAAO,EAAE,iBAAiB,oBAAoB;AAC/C,8BAAsB,CAAC,YAAY,iBAAiB;AACxD,QAAI,kBAAkB;AACpB,uBAAiB,UAAU;AAAA;AAAA,EAE/B;AAEA,qDACG,UAAO,aAAW,OAAO,QACxB,UAAAD,qCAAA,OAAC,SAAI,WAAW,OAAO,eACrB,UAAAA,qCAAA,OAAC,SAAI,WAAW,OAAO,gBACpB,UAAW,eAAI,CAAC,aAA0BA,qCAAA;AAAA,IAAC;AAAA;AAAA,MAExC,WAAW,GAAG,OAAO,SAAS,IAAI,oBAAoB,SAAS,KAAK,OAAO,SAAS,EAAE;AAAA,MACtF,SAAS,MAAM,oBAAoB,SAAS,IAAI,SAAS,IAAI;AAAA,MAC7D,MAAK;AAAA,MACL,cAAY,gBAAgB,SAAS,IAAI;AAAA,MAEzC;AAAA,QAAAA,4CAAC,OAAI,aAAW,OAAO,SAAU,mBAAS,QAA1C;AAAA;AAAA;AAAA;AAAA,QAA+C;AAAA,oDAC9C,OAAI,aAAW,OAAO,UAAW,mBAAS,QAA3C;AAAA;AAAA;AAAA;AAAA,WAAgD;AAAA;AAAA;AAAA,IAP3C,SAAS;AAAA,IADyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAU1C,EAXH;AAAA;AAAA;AAAA;AAAA,SAYA,EAbF;AAAA;AAAA;AAAA;AAAA,SAcA,EAfF;AAAA;AAAA;AAAA;AAAA,EAgBA;AAEJ;AChDA,MAAM,sBAAsBG,kBAAK,0BAAM,OAAO,4BAAqD,0IAAC;AACpG,MAAM,wBAAwBA,kBAAK,0BAAM,OAAO,4BAAyD,0IAAC;AAC1G,MAAM,sBAAsBA,kBAAK,0BAAM,OAAO,4BAAqD,0IAAC;AACpG,MAAM,aAAaA,kBAAK,0BAAM,OAAO,2BAAuC,0IAAC;AAC7E,MAAM,iBAAiBA,kBAAK,0BAAM,OAAO,2BAA2C,0IAAC;AACrF,MAAM,uBAAuBA,kBAAK,0BAAM,OAAO,gCAAuD,0IAAC;AACvG,MAAM,qBAAqBA,kBAAK,0BAAM,OAAO,6BAAmD,0IAAC;AACjG,MAAM,mBAAmBA,kBAAK,0BAAM,OAAO,2BAA+C,0IAAC;AAC3F,MAAM,uBAAuBA,kBAAK,0BAAM,OAAO,6BAAuD,0IAAC;AAGvG,MAAM,sBAAsB,MACzBH,4CAAA,SAAI,OAAO;AAAA,EACV,SAAS;AAAA,EACT,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,UAAU;AAAA,EACV,YAAY;AACd,GACE;AAAA,EAACA,4CAAA,SAAI,OAAO,EAAE,cAAc,QAAQ,UAAU,UAAU,UAAxD;AAAA;AAAA;AAAA;AAAA,EAA0D,GAAAI,MAAA;AAAA,EAC1DJ,qCAAA,OAAC,SAAI,UAAL;AAAA;AAAA;AAAA;AAAA,KAAuBI,MAAA;AAAA,EAZzB;AAAA;AAAA;AAAA;AAAA,GAaAA,MAAA;AAGF,MAAM,WAAW,CAAC,EAAE,QAAQ,aAAa;AAEvC,QAAM,iBAAiB;AAAA,IACrB,WAAW;AAAA,IACX,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,WAAW;AAAA,IACX,sBAAsB;AAAA,IACtB,SAAS;AAAA,IACT,oBAAoB;AAAA,IACpB,UAAU;AAAA,IACV,eAAe;AAAA,IACf,UAAU;AAAA,IACV,eAAe;AAAA,IACf,UAAU;AAAA,IACV,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA;AAAA,IACpB,qBAAqB;AAAA,IACrB,oBAAoB;AAAA,EACtB;AAGM,wBAAgB,eAAe,MAAM;AAE3C,MAAI,eAAe;AACjB,WACGJ,qCAAA,OAAAK,aAAA,YAAS,UAAUL,qCAAA,OAAC,qBAAD;AAAA;AAAA;AAAA;AAAA,OAAqBI,MAAA,GACvC,UAACJ,qCAAA,wBAAc,OAAf;AAAA;AAAA;AAAA;AAAA,OAAAI,MAA+B,EADjC;AAAA;AAAA;AAAA;AAAA,IAEA,GAAAA,MAAA;AAAA;AAKJ,QAAM,WAAW;AAAA,IACf,sBAAsB;AAAA,MACpB,OAAO;AAAA,MACP,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,MACP,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,mBAAmB;AAAA,MACjB,OAAO;AAAA,MACP,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,MACP,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,oBAAoB;AAAA,MAClB,OAAO;AAAA,MACP,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,OAAO;AAAA,MACP,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,MACP,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,OAAO;AAAA,MACP,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,MACP,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,OAAO;AAAA,MACP,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,qBAAqB;AAAA,MACnB,OAAO;AAAA,MACP,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,OAAO;AAAA,MACP,aAAa;AAAA,MACb,SAAS;AAAA;AAAA,EAEb;AAEM,eAAO,SAAS,MAAM,KAAK;AAAA,IAC/B,OAAO;AAAA,IACP,aAAa;AAAA,IACb,SAAS;AAAA,EACX;AAGE,SAAAJ,qCAAA,OAAC,SAAI,OAAO;AAAA,IACV,SAAS;AAAA,IACT,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,WAAW;AAAA,EAEX;AAAA,IAACA,4CAAA,QAAG,OAAO,EAAE,OAAO,WAAW,cAAc,OAC1C,kBAAK,MADR;AAAA;AAAA;AAAA;AAAA,IAEA,GAAAI,MAAA;AAAA,IACCJ,qCAAA,cAAE,OAAO,EAAE,OAAO,QAAQ,cAAc,QAAQ,UAAU,SACxD,kBAAK,YADR;AAAA;AAAA;AAAA;AAAA,IAEA,GAAAI,MAAA;AAAA,IACAJ,4CAAC,SAAI,OAAO;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,GACG,eAAK,QAPR;AAAA;AAAA;AAAA;AAAA,OAQAI,MAAA;AAAA,IAtBF;AAAA;AAAA;AAAA;AAAA,EAuBA,GAAAA,MAAA;AAEJ;ACvJA,MAAM,QAAQD,kBAAK,MAAM,2BAAO,qBAAe,+HAAC;AAChD,MAAM,aAAaA,kBAAK,MAAM,2BAAO,qBAAyB,0IAAC;AAC/D,MAAM,oBAAoBA,kBAAK,MAAM,2BAAO,iCAAuC,+HAAC;AACpF,MAAM,eAAeA,kBAAK,MAAM,2BAAO,4BAA6B,+HAAC;AAErE,MAAM,qBAAqBA,kBAAK,0BAAM,OAAO,yBAAiC,0IAAC;AAK/E,SAAS,MAAM;AACb,QAAM,CAAC,iBAAiB,kBAAkB,IAAIG,sBAAS,MAAM;AAC7D,QAAM,CAAC,aAAa,cAAc,IAAIA,sBAAS,MAAM;AAErD,QAAM,kBAAkB,MAAM;AAC5B,uBAAmB,MAAM;AACzB,mBAAe,MAAM;AAAA,EAAG;AAEpB,+BAAuB,CAAC,eAAe;AAE3C,UAAM,UAAU;AAAA,MACd;AAAA,MAAsB;AAAA,MAAmB;AAAA,MACzC;AAAA,MAAoB;AAAA,MAAe;AAAA,MACnC;AAAA,MAAqB;AAAA,MAAmB;AAAA,MACxC;AAAA,MAAW;AAAA,MAAW;AAAA,MAAU;AAAA,MAAS;AAAA,MAAU;AAAA,MAAU;AAAA,IAC/D;AAEI,gBAAQ,SAAS,UAAU,GAAG;AAEhC,yBAAmB,UAAU;AAC7B,qBAAe,MAAM;AAAA,WAChB;AAEL,yBAAmB,UAAU;AAC7B,qBAAe,MAAM;AAAA;AAAA,EAEzB;AAEM,2BAAmB,CAAC,WAAW;AACnC,QAAI,WAAW,cAAc;AAC3B,qBAAe,OAAO;AAAA,eACb,WAAW,eAAe;AACnC,qBAAe,OAAO;AAAA,eACb,WAAW,0BAA0B;AAC9C,qBAAe,eAAe;AAAA,eACrB,WAAW,iBAAiB;AACrC,qBAAe,UAAU;AAAA,eAChB,WAAW,yBAAyB;AAC7C,qBAAe,YAAY;AAAA,WACtB;AACL,yBAAmB,MAAM;AACzB,qBAAe,MAAM;AAAA;AAAA,EAEzB;AAEA,QAAM,mBAAmB,MAAM;AAC7B,mBAAe,MAAM;AACrB,uBAAmB,MAAM;AAAA,EAC3B;AAGA,QAAM,kBAAkB,MACtBN,4CAAC,OAAI,aAAU,qBAAoB,OAAO;AAAA,IACxC,SAAS;AAAA,IACT,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,OAAO;AAAA,KAEP,UAACA,qCAAA,gBAAI,UAAL;AAAA;AAAA;AAAA;AAAA,SAAkB,EARpB;AAAA;AAAA;AAAA;AAAA,EASA;AAIA,SAAAA,qCAAA,OAAC,OAAI,aAAU,OACb;AAAA,IAACA,qCAAA,iBAAO,aAAa,gBAArB;AAAA;AAAA;AAAA;AAAA,IAAsC;AAAA,IAErCA,4CAAAK,uBAAA,EAAS,UAAUL,4CAAC,iBAAD;AAAA;AAAA;AAAA;AAAA,WAAiB,GAClC,UAAgB,0BACdA,4CAAA,SAAM,cAAc,oBAArB;AAAA;AAAA;AAAA;AAAA,WAAuC,IACrC,gBAAgB,UACjBA,qCAAA,qBAAW,QAAQ,oBAApB;AAAA;AAAA;AAAA;AAAA,WAAsC,IACpC,gBAAgB,kBACjBA,qCAAA,4BAAkB,QAAQ,oBAA3B;AAAA;AAAA;AAAA;AAAA,WAA6C,IAC3C,gBAAgB,aACjBA,qCAAA,uBAAa,QAAQ,oBAAtB;AAAA;AAAA;AAAA;AAAA,WAAwC,IACtC,gBAAgB,eACjBA,qCAAA,6BAAmB,QAAQ,oBAA5B;AAAA;AAAA;AAAA;AAAA,IAA8C,WAC5C,gBAAgB,SAClBA,qCAAA,OAAC,YAAS,QAAQ,iBAAiB,QAAQ,iBAA3C;AAAA;AAAA;AAAA;AAAA,WAA6D,IAE7DA,4CAAC,QAAK,aAAU,gBACd;AAAA,MAAAA,4CAAC,gBAAD;AAAA;AAAA;AAAA;AAAA,MAAgB;AAAA,MAChBA,qCAAA,OAAC,yBAAwB,oBAAkB,qBAA3C;AAAA;AAAA;AAAA;AAAA,MAAiE;AAAA,MACjEA,qCAAA,OAAC,cAAa,gBAAc,iBAA5B;AAAA;AAAA;AAAA;AAAA,SAA8C;AAAA,MAHhD;AAAA;AAAA;AAAA;AAAA,WAIA,EAlBJ;AAAA;AAAA;AAAA;AAAA,IAoBA;AAAA,IAEAA,qCAAA;AAAA,MAAC;AAAA;AAAA,QACC;AAAA,QACA,kBAAkB;AAAA;AAAA,MAFpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAGA,EA5BF;AAAA;AAAA;AAAA;AAAA,EA6BA;AAEJ;AC/FA,MAAM,cAAc,IAAI,YAAY;AAAA,EAClC,gBAAgB;AAAA,IACd,SAAS;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA;AAAA,MACX,sBAAsB;AAAA;AAAA,EACxB;AAEJ,CAAC;AAGD,eAAe,gBAAgB;AACzB;AACF,YAAQ,KAAK,sCAAsC;AAG7C,mBAAS,MAAM,6BAA6B;AAGlD,aAAS,WAAW,SAAS,eAAe,MAAM,CAAC,EAAE;AAAA,kDAClD,MAAM,YAAN,EACC,sDAAC,gBACC,YAACA,4CAAA,qBAAoB,UAAQ,aAC3B,UAACA,qCAAA,yBAAe,QACd,UAACA,4CAAA,oBACC,UAACA,4CAAA,mBACC,sDAAC,eACC,YAACA,4CAAA,uBACC,YAAAA,qCAAA,OAAC,KAAD;AAAA;AAAA;AAAA;AAAA,aAAK,EADP;AAAA;AAAA;AAAA;AAAA,aAEA,EAHF;AAAA;AAAA;AAAA;AAAA,aAIA,EALF;AAAA;AAAA;AAAA;AAAA,aAMA,EAPF;AAAA;AAAA;AAAA;AAAA,aAQA,EATF;AAAA;AAAA;AAAA;AAAA,aAUA,EAXF;AAAA;AAAA;AAAA;AAAA,aAYA,EAbF;AAAA;AAAA;AAAA;AAAA,aAcA,EAfF;AAAA;AAAA;AAAA;AAAA,SAgBA;AAAA,IACF;AAEA,YAAQ,KAAK,8CAA8C;AAAA,WACpD,OAAO;AACN,kBAAM,sDAAsD,KAAK;AAGzE,aAAS,WAAW,SAAS,eAAe,MAAM,CAAC,EAAE;AAAA,MAClDA,4CAAA,OAAI,aAAU,kBACb;AAAA,oDAAC,QAAG,UAAJ;AAAA;AAAA;AAAA;AAAA,WAAyB;AAAA,oDACxB,OAAE,UAAH;AAAA;AAAA;AAAA;AAAA,WAAqE;AAAA,QACpEA,4CAAA,YAAO,SAAS,MAAM,OAAO,SAAS,UAAU,UAAjD;AAAA;AAAA;AAAA;AAAA,WAEA;AAAA,QAEGA,4CAAA,SAAI,WAAU,iBACb;AAAA,UAACA,qCAAA,cAAG,gBAAM,QAAV;AAAA;AAAA;AAAA;AAAA,aAAkB;AAAA,UAClBA,qCAAA,OAAC,OAAK,kBAAM,MAAZ;AAAA;AAAA;AAAA;AAAA,aAAkB;AAAA,UAFpB;AAAA;AAAA;AAAA;AAAA,WAGA;AAAA,QAVJ;AAAA;AAAA;AAAA;AAAA,SAYA;AAAA,IACF;AAAA;AAEJ;AAGA,cAAc", "names": ["activities", "jsxDEV", "styles", "icon", "lazy", "this", "Suspense", "useState"], "ignoreList": [], "sources": ["../../src/components/navigation/Header/Header.jsx", "../../src/components/navigation/ActivitiesGrid/ActivitiesGrid.jsx", "../../src/components/navigation/DonationBanner/DonationBanner.jsx", "../../src/components/navigation/ToolsSection/ToolsSection.jsx", "../../src/components/navigation/Footer/Footer.jsx", "../../src/components/pages/GamePage.jsx", "../../src/components/pages/App.jsx", "../../src/main.jsx"], "sourcesContent": ["/**\r\n * @file Header.jsx\r\n * @description Header do Portal Betina - Temporariamente desabilitado\r\n * @version 3.0.0\r\n */\r\n\r\nimport React from 'react'\r\n\r\nfunction Header({ onLogoClick }) {\r\n  // Header temporariamente desabilitado\r\n  return null\r\n}\r\n\r\nexport default Header\r\n", "/**\r\n * @file ActivitiesGrid.jsx\r\n * @description Grid de atividades do Portal Betina\r\n * @version 3.0.0\r\n */\r\n\r\nimport React, { useState, useEffect } from 'react'\r\nimport styles from './ActivitiesGrid.module.css'\r\n\r\nfunction ActivitiesGridComponent({ activities, onActivitySelect }) {\r\n  const handleActivityClick = (activityId, activityTitle) => {\r\n    if (onActivitySelect) {\r\n      onActivitySelect(activityId)\r\n    }\r\n  }\r\n\r\n  const defaultActivities = [\r\n    {\r\n      id: 'letter-recognition',\r\n      title: 'Reconhecimento de Letras',\r\n      description: 'Aprenda o alfabeto de forma divertida e interativa',\r\n      icon: '🔤',\r\n      badge: 'Letras',\r\n      color: 'blue'\r\n    },\r\n    {\r\n      id: 'number-counting',\r\n      title: 'Contagem de Números',\r\n      description: 'Pratique contagem e reconhecimento numérico',\r\n      icon: '🔢',\r\n      badge: 'Números',\r\n      color: 'orange'\r\n    },\r\n    {\r\n      id: 'memory-game',\r\n      title: '<PERSON><PERSON> da Memória',\r\n      description: 'Encontre os pares e exercite sua memória',\r\n      icon: '🧠',\r\n      badge: 'Memória',\r\n      color: 'green'\r\n    },\r\n    {\r\n      id: 'color-match',\r\n      title: 'Combinação de Cores',\r\n      description: 'Combine cores e desenvolva percepção visual',\r\n      icon: '🌈',\r\n      badge: 'Cores',\r\n      color: 'purple'\r\n    },\r\n    {\r\n      id: 'musical-sequence',\r\n      title: 'Sequência Musical',\r\n      description: 'Repita sequências sonoras e desenvolva a memória auditiva',\r\n      icon: '🎵',\r\n      badge: 'Música',\r\n      color: 'pink'\r\n    },\r\n    {\r\n      id: 'image-association',\r\n      title: 'Associação de Imagens',\r\n      description: 'Associe imagens e desenvolva conexões cognitivas',\r\n      icon: '🧩',\r\n      badge: 'Imagens',\r\n      color: 'cyan'\r\n    },\r\n    {\r\n      id: 'padroes-visuais',\r\n      title: 'Padrões Visuais',\r\n      description: 'Identifique e complete padrões visuais complexos',\r\n      icon: '🔷',\r\n      badge: 'Padrões',\r\n      color: 'indigo'\r\n    },\r\n    {\r\n      id: 'quebra-cabeca',\r\n      title: 'Quebra-Cabeça',\r\n      description: 'Monte quebra-cabeças e desenvolva raciocínio espacial',\r\n      icon: '🧩',\r\n      badge: 'Puzzle',\r\n      color: 'teal'\r\n    },\r\n    {\r\n      id: 'creative-painting',\r\n      title: 'Pintura Criativa',\r\n      description: 'Expresse sua criatividade através da arte digital',\r\n      icon: '🎨',\r\n      badge: 'Arte',\r\n      color: 'red'\r\n    }\r\n  ]\r\n\r\n  const displayActivities = activities?.length ? activities : defaultActivities\r\n\r\n  return (\r\n    <section className={styles.container}>\r\n      <h2 className={styles.title}>\r\n        🎯 Atividades Mais Populares\r\n      </h2>\r\n\r\n      <div className={styles.grid}>\r\n        {displayActivities.map((activity, index) => (\r\n          <button\r\n            key={activity.id}\r\n            className={styles.card}\r\n            onClick={() => handleActivityClick(activity.id, activity.title)}\r\n            style={{ animationDelay: `${index * 0.1}s` }}\r\n          >\r\n            <div className={styles.cardHeader}>\r\n              <div className={styles.icon}>{activity.icon}</div>\r\n              <span className={`${styles.badge} ${styles[activity.color] || styles.blue}`}>\r\n                {activity.badge}\r\n              </span>\r\n            </div>\r\n            \r\n            <h3 className={styles.cardTitle}>{activity.title}</h3>\r\n            <p className={styles.cardDescription}>{activity.description}</p>\r\n          </button>\r\n        ))}\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n\r\nexport default ActivitiesGridComponent\r\n", "/**\r\n * @file DonationBanner.jsx\r\n * @description Banner de doação do Portal Betina com UI/UX aprimorada\r\n * @version 3.1.0\r\n */\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport styles from './DonationBanner.module.css';\r\n\r\nfunction DonationBanner() {\r\n  const pixKey = \"<EMAIL>\";\r\n\r\n  const showNotification = (message, type = 'success') => {\r\n    const notification = document.createElement('div');\r\n    notification.className = `notification ${type}`;\r\n    notification.textContent = message;\r\n    \r\n    Object.assign(notification.style, {\r\n      position: 'fixed',\r\n      top: '20px',\r\n      right: '20px',\r\n      padding: '12px 24px',\r\n      borderRadius: '8px',\r\n      color: '#fff',\r\n      fontWeight: '600',\r\n      fontSize: '14px',\r\n      zIndex: '10000',\r\n      transform: 'translateX(100%)',\r\n      transition: 'transform 0.3s ease, opacity 0.3s ease',\r\n      boxShadow: '0 4px 12px rgba(0,0,0,0.2)',\r\n      display: 'flex',\r\n      alignItems: 'center',\r\n      gap: '8px',\r\n    });\r\n\r\n    const colors = {\r\n      success: 'linear-gradient(135deg, #10b981, #059669)',\r\n      error: 'linear-gradient(135deg, #ef4444, #dc2626)',\r\n    };\r\n    notification.style.background = colors[type] || colors.success;\r\n\r\n    const icon = document.createElement('span');\r\n    icon.textContent = type === 'success' ? '✅' : '❌';\r\n    notification.prepend(icon);\r\n\r\n    document.body.appendChild(notification);\r\n\r\n    setTimeout(() => {\r\n      notification.style.transform = 'translateX(0)';\r\n      notification.style.opacity = '1';\r\n    }, 100);\r\n\r\n    setTimeout(() => {\r\n      notification.style.transform = 'translateX(100%)';\r\n      notification.style.opacity = '0';\r\n      setTimeout(() => notification.remove(), 300);\r\n    }, 3000);\r\n  };\r\n\r\n  const copyPix = () => {\r\n    navigator.clipboard.writeText(pixKey).then(() => {\r\n      showNotification('Chave PIX copiada com sucesso!', 'success');\r\n    }).catch(() => {\r\n      showNotification('Erro ao copiar chave PIX', 'error');\r\n    });\r\n  };\r\n\r\n  return (\r\n    <motion.section\r\n      className={styles.banner}\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.5 }}\r\n      role=\"region\"\r\n      aria-label=\"Banner de doação\"\r\n    >\r\n      <div className={styles.welcomeHeader}>\r\n        <h1 className={styles.welcomeTitle}>\r\n          🌟 Bem-vindos ao Portal Bettina! 🌟\r\n        </h1>\r\n      </div>\r\n\r\n      <h2 className={styles.title}>\r\n        Ajude a manter este projeto vivo\r\n        <span className={styles.heart} aria-hidden=\"true\">💖</span>\r\n      </h2>\r\n\r\n      <p className={styles.text}>\r\n        Este portal nasceu da história da minha filha Bettina e do desejo de apoiar outras crianças no seu desenvolvimento. \r\n        Oferecemos gratuitamente atividades terapêuticas e educativas para crianças com autismo, TDAH e outras necessidades cognitivas.\r\n        <strong> Faça sua doação se possível!</strong>\r\n      </p>\r\n      \r\n      <div className={styles.qrSection}>\r\n        <div className={styles.qrContainer}>\r\n          <motion.img \r\n            src={`https://api.qrserver.com/v1/create-qr-code/?size=120x120&data=${encodeURIComponent(pixKey)}`}\r\n            alt=\"QR Code PIX para doação\"\r\n            className={styles.qrImage}\r\n            title=\"Clique para copiar a chave PIX ou escaneie com seu banco\"\r\n            onClick={copyPix}\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            role=\"button\"\r\n            tabIndex={0}\r\n            onKeyDown={(e) => e.key === 'Enter' && copyPix()}\r\n          />\r\n        </div>\r\n\r\n        <div className={styles.donationInfo}>\r\n          <ul className={styles.donationText}>\r\n            <li>Escaneie o QR Code com seu banco</li>\r\n            <li>Ou pressione o QR code para copiar a chave</li>\r\n            <li>Qualquer valor é bem-vindo! 🙏</li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </motion.section>\r\n  );\r\n}\r\n\r\nexport default DonationBanner;", "/**\r\n * @file ToolsSection.jsx\r\n * @description Seção de ferramentas e configurações do Portal Betina\r\n * @version 3.0.0\r\n */\r\n\r\nimport React from 'react'\r\nimport styles from './ToolsSection.module.css'\r\n\r\nfunction ToolsSection({ onToolSelect }) {  const handleToolClick = (toolId, toolTitle, toolType = 'info') => {\r\n    if (onToolSelect) {\r\n      onToolSelect(toolId)\r\n    }\r\n    // Removido: notificação desnecessária\r\n  }\r\n  const tools = [\r\n    {\r\n      id: 'dashboard-performance',\r\n      title: 'Dashboards do Sistema',\r\n      description: 'Performance, IA, Neuropedagógico, Multissensorial e Sistema Integrado',\r\n      icon: '📊',\r\n      badge: 'Premium',\r\n      color: 'premium',\r\n      type: 'premium'\r\n    },\r\n    {\r\n      id: 'user-profiles',\r\n      title: 'Perfis de Usuário',\r\n      description: 'Gerencie diferentes perfis para toda a família',\r\n      icon: '👤',\r\n      badge: 'Perfis',\r\n      color: 'purple',\r\n      type: 'info'\r\n    },\r\n    {\r\n      id: 'admin-panel',\r\n      title: 'Painel Administrativo',\r\n      description: 'Configurações avançadas e gerenciamento do sistema',\r\n      icon: '🔐',\r\n      badge: 'Admin',\r\n      color: 'red',\r\n      type: 'warning'\r\n    },\r\n    {\r\n      id: 'about-info',\r\n      title: 'Sobre o Portal',\r\n      description: 'Informações sobre o projeto, versão e créditos',\r\n      icon: 'ℹ️',\r\n      badge: 'Info',\r\n      color: 'cyan',\r\n      type: 'info'\r\n    },\r\n    {\r\n      id: 'accessibility-settings',\r\n      title: 'Configurações de Acessibilidade',\r\n      description: 'Alto contraste, tamanho da fonte e outras opções de acessibilidade',\r\n      icon: '♿',\r\n      badge: 'Acessibilidade',\r\n      color: 'special',\r\n      type: 'special'\r\n    }\r\n  ]\r\n\r\n  return (\r\n    <section className={styles.container}>\r\n      <h2 className={styles.title}>\r\n        ⚙️ Ferramentas e Configurações\r\n      </h2>\r\n\r\n      <div className={styles.grid}>\r\n        {tools.map((tool, index) => (\r\n          <button\r\n            key={tool.id}\r\n            className={`${styles.card} ${styles[tool.color] || styles.blue}`}\r\n            onClick={() => handleToolClick(tool.id, tool.title, tool.type)}\r\n            style={{ animationDelay: `${index * 0.1}s` }}\r\n          >\r\n            <div className={styles.cardHeader}>\r\n              <div className={styles.icon}>{tool.icon}</div>\r\n              <span className={`${styles.badge} ${styles[`badge${tool.color.charAt(0).toUpperCase() + tool.color.slice(1)}`] || styles.badgeBlue}`}>\r\n                {tool.badge}\r\n              </span>\r\n            </div>\r\n            \r\n            <h3 className={styles.cardTitle}>{tool.title}</h3>\r\n            <p className={styles.cardDescription}>{tool.description}</p>\r\n          </button>\r\n        ))}\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n\r\nexport default ToolsSection\r\n", "/**\r\n * @file Footer.jsx\r\n * @description Footer simples do Portal Betina\r\n * @version 3.0.0\r\n */\r\n\r\nimport React from 'react'\r\nimport styles from './Footer.module.css'\r\n\r\nconst activities = [\r\n  { id: 'home', name: '<PERSON><PERSON><PERSON>', icon: '🏠' },\r\n  { id: 'letter-recognition', name: '<PERSON><PERSON>', icon: '🔤' },\r\n  { id: 'number-counting', name: '<PERSON><PERSON><PERSON><PERSON>', icon: '🔢' },\r\n  { id: 'memory-game', name: 'Me<PERSON><PERSON><PERSON>', icon: '🧠' },\r\n  { id: 'musical-sequence', name: 'Música', icon: '🎵' },\r\n  { id: 'color-match', name: 'Cores', icon: '🌈' },\r\n  { id: 'image-association', name: 'Imagens', icon: '🖼️' },\r\n  { id: 'padroes-visuais', name: '<PERSON><PERSON><PERSON><PERSON>', icon: '🔷' },\r\n  { id: 'quebra-cabeca', name: 'Puzzle', icon: '🧩' },\r\n  { id: 'creative-painting', name: 'Arte', icon: '🎨' }\r\n]\r\n\r\n/**\r\n * Componente de rodapé com navegação para atividades\r\n */\r\nfunction Footer({ currentActivity, onActivityChange }) {\r\n  const handleActivityClick = (activityId, activityName) => {\r\n    if (onActivityChange) {\r\n      onActivityChange(activityId)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <footer className={styles.footer}>\r\n      <div className={styles.footerContent}>\r\n        <div className={styles.navigationGrid}>\r\n          {activities.map((activity) => (            <button\r\n              key={activity.id}\r\n              className={`${styles.navButton} ${currentActivity === activity.id ? styles.active : ''}`}\r\n              onClick={() => handleActivityClick(activity.id, activity.name)}\r\n              role=\"button\"\r\n              aria-label={`Navegar para ${activity.name}`}\r\n            >\r\n              <div className={styles.navIcon}>{activity.icon}</div>\r\n              <div className={styles.navLabel}>{activity.name}</div>\r\n            </button>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  )\r\n}\r\n\r\nexport default Footer\r\n", "import React, { Suspense, lazy } from 'react'\r\n\r\n// Lazy loading dos jogos para reduzir bundle size\r\nconst ContagemNumerosGame = lazy(() => import('../../games/ContagemNumeros/ContagemNumerosGame.jsx'))\r\nconst LetterRecognitionGame = lazy(() => import('../../games/LetterRecognition/LetterRecognitionGame.jsx'))\r\nconst MusicalSequenceGame = lazy(() => import('../../games/MusicalSequence/MusicalSequenceGame.jsx'))\r\nconst MemoryGame = lazy(() => import('../../games/MemoryGame/MemoryGame.jsx'))\r\nconst ColorMatchGame = lazy(() => import('../../games/ColorMatch/ColorMatchGame.jsx'))\r\nconst ImageAssociationGame = lazy(() => import('../../games/ImageAssociation/ImageAssociationGame.jsx'))\r\nconst PadroesVisuaisGame = lazy(() => import('../../games/PadroesVisuais/PadroesVisuaisGame.jsx'))\r\nconst QuebraCabecaGame = lazy(() => import('../../games/QuebraCabeca/QuebraCabecaGame.jsx'))\r\nconst CreativePaintingGame = lazy(() => import('../../games/CreativePainting/CreativePaintingGame.jsx'))\r\n\r\n// Componente de carregamento otimizado\r\nconst GameLoadingFallback = () => (\r\n  <div style={{\r\n    display: 'flex',\r\n    flexDirection: 'column',\r\n    justifyContent: 'center',\r\n    alignItems: 'center',\r\n    height: '100vh',\r\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n    color: 'white',\r\n    fontSize: '18px',\r\n    fontFamily: 'Arial, sans-serif'\r\n  }}>\r\n    <div style={{ marginBottom: '20px', fontSize: '48px' }}>🎮</div>\r\n    <div>Carregando jogo...</div>\r\n  </div>\r\n)\r\n\r\nconst GamePage = ({ gameId, onBack }) => {\r\n  // Mapear IDs para componentes lazy-loaded\r\n  const gameComponents = {\r\n    'numbers': ContagemNumerosGame,\r\n    'number-counting': ContagemNumerosGame,\r\n    'contagem-numeros': ContagemNumerosGame,\r\n    'letters': LetterRecognitionGame,\r\n    'letter-recognition': LetterRecognitionGame,\r\n    'music': MusicalSequenceGame,\r\n    'musical-sequence': MusicalSequenceGame,\r\n    'memory': MemoryGame,\r\n    'memory-game': MemoryGame,\r\n    'colors': ColorMatchGame,\r\n    'color-match': ColorMatchGame,\r\n    'images': ImageAssociationGame,\r\n    'image-association': ImageAssociationGame,\r\n    'patterns': PadroesVisuaisGame,\r\n    'padroes-visuais': PadroesVisuaisGame,\r\n    'quebra-cabeca': QuebraCabecaGame,\r\n    'emotional-puzzle': QuebraCabecaGame, // Quebra-cabeça emocional\r\n    'creative-painting': CreativePaintingGame,\r\n    'pintura-criativa': CreativePaintingGame\r\n  }\r\n\r\n  // Se existe componente real, renderizar o jogo com Suspense\r\n  const GameComponent = gameComponents[gameId]\r\n\r\n  if (GameComponent) {\r\n    return (\r\n      <Suspense fallback={<GameLoadingFallback />}>\r\n        <GameComponent onBack={onBack} />\r\n      </Suspense>\r\n    )\r\n  }\r\n\r\n  // Fallback para jogos não implementados\r\n  const gameInfo = {\r\n    'letter-recognition': {\r\n      title: 'Reconhecimento de Letras',\r\n      description: 'Jogo educativo para aprender o alfabeto',\r\n      content: '🔤 Este é o jogo de reconhecimento de letras! Em breve estará totalmente funcional.'\r\n    },\r\n    'letters': {\r\n      title: 'Reconhecimento de Letras',\r\n      description: 'Jogo educativo para aprender o alfabeto',\r\n      content: '🔤 Este é o jogo de reconhecimento de letras! Em breve estará totalmente funcional.'\r\n    },\r\n    'number-counting': {\r\n      title: 'Contagem de Números',\r\n      description: 'Jogo para aprender números e contagem',\r\n      content: '🔢 Este é o jogo de contagem de números! Em breve estará totalmente funcional.'\r\n    },\r\n    'numbers': {\r\n      title: 'Contagem de Números',\r\n      description: 'Jogo para aprender números e contagem',\r\n      content: '🔢 Este é o jogo de contagem de números! Em breve estará totalmente funcional.'\r\n    },\r\n    'musical-sequence': {\r\n      title: 'Sequência Musical',\r\n      description: 'Jogo de memória auditiva',\r\n      content: '🎵 Este é o jogo de sequência musical! Em breve estará totalmente funcional.'\r\n    },\r\n    'music': {\r\n      title: 'Sequência Musical',\r\n      description: 'Jogo de memória auditiva',\r\n      content: '🎵 Este é o jogo de sequência musical! Em breve estará totalmente funcional.'\r\n    },\r\n    'memory-game': {\r\n      title: 'Jogo da Memória',\r\n      description: 'Jogo clássico de memória',\r\n      content: '🧠 Este é o jogo da memória! Em breve estará totalmente funcional.'\r\n    },\r\n    'memory': {\r\n      title: 'Jogo da Memória',\r\n      description: 'Jogo clássico de memória',\r\n      content: '🧠 Este é o jogo da memória! Em breve estará totalmente funcional.'\r\n    },\r\n    'color-match': {\r\n      title: 'Combinação de Cores',\r\n      description: 'Jogo de combinação de cores',\r\n      content: '🌈 Este é o jogo de combinação de cores! Em breve estará totalmente funcional.'\r\n    },\r\n    'colors': {\r\n      title: 'Combinação de Cores',\r\n      description: 'Jogo de combinação de cores',\r\n      content: '🌈 Este é o jogo de combinação de cores! Em breve estará totalmente funcional.'\r\n    },\r\n    'image-association': {\r\n      title: 'Associação de Imagens',\r\n      description: 'Jogo de associação cognitiva',\r\n      content: '🧩 Este é o jogo de associação de imagens! Em breve estará totalmente funcional.'\r\n    },\r\n    'images': {\r\n      title: 'Associação de Imagens',\r\n      description: 'Jogo de associação cognitiva',\r\n      content: '🧩 Este é o jogo de associação de imagens! Em breve estará totalmente funcional.'\r\n    }\r\n  }\r\n\r\n  const game = gameInfo[gameId] || {\r\n    title: 'Jogo não encontrado',\r\n    description: 'Este jogo ainda não foi implementado',\r\n    content: '❌ Jogo não encontrado'\r\n  }\r\n\r\n  return (\r\n    <div style={{\r\n      padding: '2rem',\r\n      textAlign: 'center',\r\n      background: 'rgba(255, 255, 255, 0.95)',\r\n      borderRadius: '1rem',\r\n      margin: '2rem',\r\n      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'\r\n    }}>\r\n      <h1 style={{ color: '#0066cc', marginBottom: '1rem' }}>\r\n        {game.title}\r\n      </h1>\r\n      <p style={{ color: '#666', marginBottom: '2rem', fontSize: '1.1rem' }}>\r\n        {game.description}\r\n      </p>\r\n      <div style={{ \r\n        padding: '3rem', \r\n        background: '#f8f9fa', \r\n        borderRadius: '0.5rem',\r\n        marginBottom: '2rem',\r\n        fontSize: '1.2rem'\r\n      }}>\r\n        {game.content}\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default GamePage\r\n", "import React, { useState, Suspense, lazy } from 'react'\r\nimport './App.css'\r\n\r\n// Importando componentes essenciais (sempre carregados)\r\nimport Header from '../navigation/Header'\r\nimport ActivitiesGridComponent from '../navigation/ActivitiesGrid'\r\nimport DonationBanner from '../navigation/DonationBanner'\r\nimport ToolsSection from '../navigation/ToolsSection'\r\nimport Footer from '../navigation/Footer'\r\n\r\n// Lazy loading para componentes pesados\r\nconst About = lazy(() => import('./About/About'))\r\nconst AdminPanel = lazy(() => import('./AdminPanel/AdminPanel'))\r\nconst AccessibilityPage = lazy(() => import('./AccessibilityPage/AccessibilityPage'))\r\nconst UserProfiles = lazy(() => import('./UserProfiles/UserProfiles'))\r\n// BackupExport removido - funcionalidade disponível apenas no dashboard premium\r\nconst DashboardContainer = lazy(() => import('../dashboard/DashboardContainer'))\r\n\r\n// Importação estática do GamePage para evitar problemas de lazy loading\r\nimport GamePage from './GamePage.jsx'\r\n\r\nfunction App() {\r\n  const [currentActivity, setCurrentActivity] = useState('home')\r\n  const [currentPage, setCurrentPage] = useState('home')\r\n\r\n  const handleLogoClick = () => {\r\n    setCurrentActivity('home')\r\n    setCurrentPage('home')  }\r\n  \r\n  const handleActivityChange = (activityId) => {\r\n    // Lista de jogos válidos\r\n    const gameIds = [\r\n      'letter-recognition', 'number-counting', 'memory-game', \r\n      'musical-sequence', 'color-match', 'image-association',\r\n      'creative-painting', 'padroes-visuais', 'quebra-cabeca',\r\n      'letters', 'numbers', 'memory', 'music', 'colors', 'images', 'patterns'\r\n    ]\r\n    \r\n    if (gameIds.includes(activityId)) {\r\n      // Se é um jogo, navegar para a página do jogo\r\n      setCurrentActivity(activityId)\r\n      setCurrentPage('game')\r\n    } else {\r\n      // Se não é um jogo, manter na home\r\n      setCurrentActivity(activityId)\r\n      setCurrentPage('home')\r\n    }\r\n  }\r\n\r\n  const handleToolSelect = (toolId) => {\r\n    if (toolId === 'about-info') {\r\n      setCurrentPage('about')\r\n    } else if (toolId === 'admin-panel') {\r\n      setCurrentPage('admin')\r\n    } else if (toolId === 'accessibility-settings') {\r\n      setCurrentPage('accessibility')\r\n    } else if (toolId === 'user-profiles') {\r\n      setCurrentPage('profiles')\r\n    } else if (toolId === 'dashboard-performance') {\r\n      setCurrentPage('dashboards')\r\n    } else {\r\n      setCurrentActivity(toolId)\r\n      setCurrentPage('home')\r\n    }\r\n  }\r\n\r\n  const handleBackToHome = () => {\r\n    setCurrentPage('home')\r\n    setCurrentActivity('home')\r\n  }\r\n\r\n  // Componente de carregamento\r\n  const LoadingFallback = () => (\r\n    <div className=\"loading-container\" style={{\r\n      display: 'flex',\r\n      justifyContent: 'center',\r\n      alignItems: 'center',\r\n      height: '60vh',\r\n      fontSize: '18px',\r\n      color: '#666'\r\n    }}>\r\n      <div>Carregando...</div>\r\n    </div>\r\n  )\r\n\r\n  return (\r\n    <div className=\"app\">\r\n      <Header onLogoClick={handleLogoClick} />\r\n      \r\n      <Suspense fallback={<LoadingFallback />}>\r\n        {currentPage === 'about' ? (\r\n          <About onBackToHome={handleBackToHome} />\r\n        ) : currentPage === 'admin' ? (\r\n          <AdminPanel onBack={handleBackToHome} />\r\n        ) : currentPage === 'accessibility' ? (\r\n          <AccessibilityPage onBack={handleBackToHome} />\r\n        ) : currentPage === 'profiles' ? (\r\n          <UserProfiles onBack={handleBackToHome} />\r\n        ) : currentPage === 'dashboards' ? (\r\n          <DashboardContainer onBack={handleBackToHome} />\r\n        ) : currentPage === 'game' ? (\r\n          <GamePage gameId={currentActivity} onBack={handleBackToHome} />\r\n        ) : (\r\n          <main className=\"main-content\">\r\n            <DonationBanner />\r\n            <ActivitiesGridComponent onActivitySelect={handleActivityChange} />\r\n            <ToolsSection onToolSelect={handleToolSelect} />\r\n          </main>\r\n        )}\r\n      </Suspense>\r\n\r\n      <Footer \r\n        currentActivity={currentActivity}\r\n        onActivityChange={handleActivityChange}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default App\r\n", "/**\r\n * @file main.jsx\r\n * @description Entry point principal do Portal Betina V3 com sistema de resiliência integrado\r\n * @version 3.0.0\r\n */\r\n\r\nimport React from 'react'\r\nimport ReactDOM from 'react-dom/client'\r\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query'\r\nimport { HelmetProvider } from 'react-helmet-async'\r\nimport App from './components/pages/App.jsx'\r\nimport { initializePortalBetinaSystem } from './api/services/AppInitializer'\r\nimport { SystemProvider } from './components/context/SystemContext'\r\nimport { DatabaseProvider } from './components/context/DatabaseProvider'\r\nimport { AccessibilityProvider } from './components/context/AccessibilityContext'\r\nimport { PremiumProvider } from './context/PremiumContext'\r\nimport { AdminProvider } from './context/AdminContext'\r\n\r\n// Importar estilos de acessibilidade\r\nimport './styles/accessibility.css'\r\n\r\n// Configuração do React Query para gerenciamento de estado e cache\r\nconst queryClient = new QueryClient({\r\n  defaultOptions: {\r\n    queries: {\r\n      retry: 2,\r\n      staleTime: 60000, // 1 minuto\r\n      refetchOnWindowFocus: false,\r\n    },\r\n  },\r\n})\r\n\r\n// Função de inicialização principal\r\nasync function initializeApp() {\r\n  try {\r\n    console.info('🚀 Inicializando Portal Betina V3...')\r\n    \r\n    // Inicializar sistema integrado\r\n    const system = await initializePortalBetinaSystem()\r\n    \r\n    // Renderizar a aplicação com o sistema inicializado\r\n    ReactDOM.createRoot(document.getElementById('root')).render(\r\n      <React.StrictMode>\r\n        <HelmetProvider>\r\n          <QueryClientProvider client={queryClient}>\r\n            <SystemProvider system={system}>\r\n              <DatabaseProvider>\r\n                <PremiumProvider>\r\n                  <AdminProvider>\r\n                    <AccessibilityProvider>\r\n                      <App />\r\n                    </AccessibilityProvider>\r\n                  </AdminProvider>\r\n                </PremiumProvider>\r\n              </DatabaseProvider>\r\n            </SystemProvider>\r\n          </QueryClientProvider>\r\n        </HelmetProvider>\r\n      </React.StrictMode>\r\n    )\r\n    \r\n    console.info('✅ Portal Betina V3 inicializado com sucesso!')\r\n  } catch (error) {\r\n    console.error('❌ Erro fatal na inicialização do Portal Betina V3:', error)\r\n    \r\n    // Renderizar página de fallback em caso de erro crítico na inicialização\r\n    ReactDOM.createRoot(document.getElementById('root')).render(\r\n      <div className=\"critical-error\">\r\n        <h1>Erro na Inicialização</h1>\r\n        <p>Não foi possível inicializar o sistema. Tente recarregar a página.</p>\r\n        <button onClick={() => window.location.reload()}>\r\n          Recarregar\r\n        </button>\r\n        {import.meta.env.DEV && (\r\n          <div className=\"error-details\">\r\n            <p>{error.message}</p>\r\n            <pre>{error.stack}</pre>\r\n          </div>\r\n        )}\r\n      </div>\r\n    )\r\n  }\r\n}\r\n\r\n// Iniciar a aplicação\r\ninitializeApp()\r\n"], "file": "assets/index-C53L1-Ks.js"}