# Dockerfile.frontend - Frontend apenas
FROM node:20-alpine AS base

# Instalar dependências do sistema
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    && ln -sf python3 /usr/bin/python

WORKDIR /app

# Estágio para dependências
FROM base AS dependencies
COPY package.json ./
RUN npm install && \
    npm cache clean --force

# Estágio para build
FROM dependencies AS builder
COPY src/ ./src/
COPY public/ ./public/
COPY index.html ./
COPY vite.config.js ./

# Build do frontend
RUN npm run build

# Verificar se o build foi bem-sucedido
RUN ls -la dist/ || (echo "Build failed - dist directory not found" && exit 1)

# Estágio de produção
FROM node:20-alpine AS production

# Criar usuário não-root
RUN apk add --no-cache curl
RUN addgroup -g 1001 -S nodejs
RUN adduser -S frontend -u 1001

WORKDIR /app

# Instalar servidor estático simples
COPY static-package.json ./package.json
RUN npm install && npm cache clean --force

# Copiar servidor estático
COPY serve-static.js ./

# Copiar arquivos buildados
COPY --from=builder /app/dist ./public

# Definir permissões
RUN chown -R frontend:nodejs /app

USER frontend

EXPOSE 5173

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5173 || exit 1

CMD ["node", "serve-static.js"]
