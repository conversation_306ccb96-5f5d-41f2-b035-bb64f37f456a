import { R as React } from "./vendor-react-ByWh_-BW.js";
import "./vendor-misc-DneMUARX.js";
import "./services-M1ydzWhv.js";
import "./dashboard-DanqcTsU.js";
import "./context-Ch-5FaFa.js";
import "./hooks-NJkOkh4y.js";
import "./vendor-utils-CjlX8hrF.js";
import "./vendor-charts-Cii0KTpx.js";
import "./admin-D2mpdgvV.js";
import "./utils-CLTxz6zX.js";
import "./game-colors-B_gd3llZ.js";
import "./game-association-B9GAxBuN.js";
import "./game-letters-v8KNWHXS.js";
import "./game-memory-6_ujaMB2.js";
import "./vendor-motion-CJek6P2z.js";
import "./game-musical-Ci_rqtJn.js";
import "./game-patterns-GQY4qytf.js";
import "./game-puzzle-BLc_eXaF.js";
import "./game-numbers-tpTS4tK7.js";
import "./game-creative-iDOKdRXI.js";
const container = "_container_z1ypv_15";
const heroBanner = "_heroBanner_z1ypv_81";
const heroContent = "_heroContent_z1ypv_99";
const heroTitle = "_heroTitle_z1ypv_109";
const heroSubtitle = "_heroSubtitle_z1ypv_125";
const badgeContainer = "_badgeContainer_z1ypv_139";
const techBadge = "_techBadge_z1ypv_153";
const badgePrimary = "_badgePrimary_z1ypv_177";
const badgeGreen = "_badgeGreen_z1ypv_185";
const badgePurple = "_badgePurple_z1ypv_193";
const section = "_section_z1ypv_203";
const sectionTitle = "_sectionTitle_z1ypv_221";
const sectionContent = "_sectionContent_z1ypv_249";
const benefitsList = "_benefitsList_z1ypv_271";
const benefitItem = "_benefitItem_z1ypv_283";
const benefitEmoji = "_benefitEmoji_z1ypv_305";
const benefitText = "_benefitText_z1ypv_315";
const highlightBox = "_highlightBox_z1ypv_327";
const highlightTitle = "_highlightTitle_z1ypv_345";
const highlightText = "_highlightText_z1ypv_357";
const aiFeatureGrid = "_aiFeatureGrid_z1ypv_371";
const aiFeatureCard = "_aiFeatureCard_z1ypv_385";
const aiFeatureIcon = "_aiFeatureIcon_z1ypv_415";
const aiFeatureTitle = "_aiFeatureTitle_z1ypv_425";
const aiFeatureDescription = "_aiFeatureDescription_z1ypv_439";
const techCategory = "_techCategory_z1ypv_453";
const techCategoryTitle = "_techCategoryTitle_z1ypv_461";
const techBadges = "_techBadges_z1ypv_481";
const styles = {
  container,
  heroBanner,
  heroContent,
  heroTitle,
  heroSubtitle,
  badgeContainer,
  techBadge,
  badgePrimary,
  badgeGreen,
  badgePurple,
  section,
  sectionTitle,
  sectionContent,
  benefitsList,
  benefitItem,
  benefitEmoji,
  benefitText,
  highlightBox,
  highlightTitle,
  highlightText,
  aiFeatureGrid,
  aiFeatureCard,
  aiFeatureIcon,
  aiFeatureTitle,
  aiFeatureDescription,
  techCategory,
  techCategoryTitle,
  techBadges
};
var _jsxFileName = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\components\\pages\\About\\About.jsx";
function About({
  onBackToHome
}) {
  return /* @__PURE__ */ React.createElement("div", { className: styles.container, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 12,
    columnNumber: 5
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.heroBanner, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 14,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.heroContent, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 15,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("h1", { className: styles.heroTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 16,
    columnNumber: 11
  } }, "Portal Betina"), /* @__PURE__ */ React.createElement("p", { className: styles.heroSubtitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 17,
    columnNumber: 11
  } }, "Transformando vidas através de atividades neuropedagógicas potencializadas por inteligência artificial"), /* @__PURE__ */ React.createElement("div", { className: styles.badgeContainer, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 20,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("span", { className: `${styles.techBadge} ${styles.badgePrimary}`, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 21,
    columnNumber: 13
  } }, "🧠 Desenvolvimento Cognitivo"), /* @__PURE__ */ React.createElement("span", { className: `${styles.techBadge} ${styles.badgeGreen}`, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 24,
    columnNumber: 13
  } }, "🤖 Potencializado por IA"), /* @__PURE__ */ React.createElement("span", { className: `${styles.techBadge} ${styles.badgePurple}`, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 27,
    columnNumber: 13
  } }, "♿ 100% Acessível")))), /* @__PURE__ */ React.createElement("section", { className: styles.section, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 35,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("h2", { className: styles.sectionTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 36,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 37,
    columnNumber: 11
  } }, "🧠"), "O que são Atividades Neuropedagógicas?"), /* @__PURE__ */ React.createElement("div", { className: styles.sectionContent, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 40,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("p", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 41,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 42,
    columnNumber: 13
  } }, "Atividades neuropedagógicas"), " são intervenções estruturadas que estimulam o desenvolvimento cognitivo, emocional e social, especialmente para crianças com autismo, TDAH ou outras necessidades específicas."), /* @__PURE__ */ React.createElement("p", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 46,
    columnNumber: 11
  } }, "Elas combinam princípios da ", /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 47,
    columnNumber: 41
  } }, "neurociência"), ", ", /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 47,
    columnNumber: 72
  } }, "psicologia"), " e", /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 48,
    columnNumber: 13
  } }, "pedagogia"), " para promover habilidades essenciais como:"), /* @__PURE__ */ React.createElement("ul", { className: styles.benefitsList, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 51,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("li", { className: styles.benefitItem, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 52,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.benefitEmoji, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 53,
    columnNumber: 15
  } }, "🎯"), /* @__PURE__ */ React.createElement("span", { className: styles.benefitText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 54,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 55,
    columnNumber: 17
  } }, "Atenção e Concentração:"), " Melhorar o foco e a capacidade de manter a atenção")), /* @__PURE__ */ React.createElement("li", { className: styles.benefitItem, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 58,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.benefitEmoji, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 59,
    columnNumber: 15
  } }, "🧠"), /* @__PURE__ */ React.createElement("span", { className: styles.benefitText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 60,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 61,
    columnNumber: 17
  } }, "Memória:"), " Fortalecer a memória de trabalho e de longo prazo")), /* @__PURE__ */ React.createElement("li", { className: styles.benefitItem, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 64,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.benefitEmoji, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 65,
    columnNumber: 15
  } }, "🤔"), /* @__PURE__ */ React.createElement("span", { className: styles.benefitText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 66,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 67,
    columnNumber: 17
  } }, "Raciocínio Lógico:"), " Desenvolver habilidades de resolução de problemas")), /* @__PURE__ */ React.createElement("li", { className: styles.benefitItem, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 70,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.benefitEmoji, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 71,
    columnNumber: 15
  } }, "✋"), /* @__PURE__ */ React.createElement("span", { className: styles.benefitText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 72,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 73,
    columnNumber: 17
  } }, "Coordenação Motora:"), " Aprimorar habilidades motoras finas e grossas")), /* @__PURE__ */ React.createElement("li", { className: styles.benefitItem, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 76,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.benefitEmoji, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 77,
    columnNumber: 15
  } }, "😊"), /* @__PURE__ */ React.createElement("span", { className: styles.benefitText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 78,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 79,
    columnNumber: 17
  } }, "Regulação Emocional:"), " Aprender a identificar e gerenciar emoções"))))), /* @__PURE__ */ React.createElement("section", { className: styles.section, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 87,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("h2", { className: styles.sectionTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 88,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 89,
    columnNumber: 11
  } }, "🤖"), "Como a Inteligência Artificial Potencializa o Aprendizado"), /* @__PURE__ */ React.createElement("div", { className: styles.sectionContent, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 92,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.highlightBox, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 93,
    columnNumber: 11
  } }, "          ", /* @__PURE__ */ React.createElement("h3", { className: styles.highlightTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 93,
    columnNumber: 58
  } }, "🚀 Portal Betina V3 - Tecnologia Avançada para Desenvolvimento"), /* @__PURE__ */ React.createElement("p", { className: styles.highlightText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 96,
    columnNumber: 11
  } }, "Nossa plataforma integra inteligência artificial com metodologias terapêuticas comprovadas para oferecer uma experiência personalizada e eficaz para cada criança.")), /* @__PURE__ */ React.createElement("div", { className: styles.aiFeatureGrid, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 102,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.aiFeatureCard, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 103,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.aiFeatureIcon, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 104,
    columnNumber: 15
  } }, "🎯"), /* @__PURE__ */ React.createElement("h4", { className: styles.aiFeatureTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 105,
    columnNumber: 15
  } }, "Personalização Inteligente"), /* @__PURE__ */ React.createElement("p", { className: styles.aiFeatureDescription, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 106,
    columnNumber: 15
  } }, "A IA adapta a dificuldade e o ritmo das atividades baseado no desempenho individual da criança")), /* @__PURE__ */ React.createElement("div", { className: styles.aiFeatureCard, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 111,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.aiFeatureIcon, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 112,
    columnNumber: 15
  } }, "📊"), /* @__PURE__ */ React.createElement("h4", { className: styles.aiFeatureTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 113,
    columnNumber: 15
  } }, "Análise de Progresso"), /* @__PURE__ */ React.createElement("p", { className: styles.aiFeatureDescription, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 114,
    columnNumber: 15
  } }, "Algoritmos analisam padrões de aprendizado e fornecem insights sobre o desenvolvimento cognitivo")), /* @__PURE__ */ React.createElement("div", { className: styles.aiFeatureCard, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 119,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.aiFeatureIcon, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 120,
    columnNumber: 15
  } }, "🎮"), /* @__PURE__ */ React.createElement("h4", { className: styles.aiFeatureTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 121,
    columnNumber: 15
  } }, "Engajamento Otimizado"), /* @__PURE__ */ React.createElement("p", { className: styles.aiFeatureDescription, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 122,
    columnNumber: 15
  } }, "IA determina os melhores momentos e tipos de feedback para manter a motivação e interesse")), /* @__PURE__ */ React.createElement("div", { className: styles.aiFeatureCard, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 127,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.aiFeatureIcon, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 128,
    columnNumber: 15
  } }, "🔄"), /* @__PURE__ */ React.createElement("h4", { className: styles.aiFeatureTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 129,
    columnNumber: 15
  } }, "Adaptação Contínua"), /* @__PURE__ */ React.createElement("p", { className: styles.aiFeatureDescription, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 130,
    columnNumber: 15
  } }, "O sistema aprende continuamente com as interações, melhorando constantemente a experiência"))))), /* @__PURE__ */ React.createElement("section", { className: styles.section, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 139,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("h2", { className: styles.sectionTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 140,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 141,
    columnNumber: 11
  } }, "⚙️"), "Tecnologias e Metodologias Aplicadas"), /* @__PURE__ */ React.createElement("div", { className: styles.sectionContent, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 144,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("p", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 145,
    columnNumber: 11
  } }, "O Portal Betina utiliza tecnologias modernas e metodologias baseadas em evidências científicas:"), /* @__PURE__ */ React.createElement("div", { className: styles.techCategory, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 149,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("h4", { className: styles.techCategoryTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 150,
    columnNumber: 13
  } }, "🧬 Base Científica"), /* @__PURE__ */ React.createElement("div", { className: styles.techBadges, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 151,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.techBadge, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 152,
    columnNumber: 15
  } }, "Neurociência Cognitiva"), /* @__PURE__ */ React.createElement("span", { className: styles.techBadge, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 153,
    columnNumber: 15
  } }, "Psicologia do Desenvolvimento"), /* @__PURE__ */ React.createElement("span", { className: styles.techBadge, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 154,
    columnNumber: 15
  } }, "Pedagogia Inclusiva"), /* @__PURE__ */ React.createElement("span", { className: styles.techBadge, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 155,
    columnNumber: 15
  } }, "Terapia ABA"), /* @__PURE__ */ React.createElement("span", { className: styles.techBadge, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 156,
    columnNumber: 15
  } }, "Neuroplasticidade"))), /* @__PURE__ */ React.createElement("div", { className: styles.techCategory, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 160,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("h4", { className: styles.techCategoryTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 161,
    columnNumber: 13
  } }, "💻 Tecnologia"), /* @__PURE__ */ React.createElement("div", { className: styles.techBadges, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 162,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.techBadge, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 163,
    columnNumber: 15
  } }, "React + IA"), /* @__PURE__ */ React.createElement("span", { className: styles.techBadge, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 164,
    columnNumber: 15
  } }, "Machine Learning"), /* @__PURE__ */ React.createElement("span", { className: styles.techBadge, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 165,
    columnNumber: 15
  } }, "Design Responsivo"), /* @__PURE__ */ React.createElement("span", { className: styles.techBadge, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 166,
    columnNumber: 15
  } }, "Acessibilidade Web"), /* @__PURE__ */ React.createElement("span", { className: styles.techBadge, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 167,
    columnNumber: 15
  } }, "Progressive Web App"))), /* @__PURE__ */ React.createElement("div", { className: styles.techCategory, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 171,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("h4", { className: styles.techCategoryTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 172,
    columnNumber: 13
  } }, "🌈 Acessibilidade"), /* @__PURE__ */ React.createElement("div", { className: styles.techBadges, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 173,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.techBadge, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 174,
    columnNumber: 15
  } }, "Screen Reader"), /* @__PURE__ */ React.createElement("span", { className: styles.techBadge, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 175,
    columnNumber: 15
  } }, "Alto Contraste"), /* @__PURE__ */ React.createElement("span", { className: styles.techBadge, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 176,
    columnNumber: 15
  } }, "Navegação por Teclado"), /* @__PURE__ */ React.createElement("span", { className: styles.techBadge, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 177,
    columnNumber: 15
  } }, "Feedback Háptico"), /* @__PURE__ */ React.createElement("span", { className: styles.techBadge, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 178,
    columnNumber: 15
  } }, "WCAG 2.1 AA"))))), /* @__PURE__ */ React.createElement("section", { className: styles.section, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 185,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("h2", { className: styles.sectionTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 186,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 187,
    columnNumber: 11
  } }, "👨‍👩‍👧‍👦"), "Para Pais, Terapeutas e Educadores"), /* @__PURE__ */ React.createElement("div", { className: styles.sectionContent, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 190,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("p", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 191,
    columnNumber: 11
  } }, "O Portal Betina foi desenvolvido para ser uma ferramenta colaborativa entre famílias e profissionais:"), /* @__PURE__ */ React.createElement("ul", { className: styles.benefitsList, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 195,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("li", { className: styles.benefitItem, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 196,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.benefitEmoji, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 197,
    columnNumber: 15
  } }, "👩‍⚕️"), /* @__PURE__ */ React.createElement("span", { className: styles.benefitText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 198,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 199,
    columnNumber: 17
  } }, "Para Terapeutas:"), " Ferramentas complementares para sessões presenciais e atividades para casa")), /* @__PURE__ */ React.createElement("li", { className: styles.benefitItem, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 202,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.benefitEmoji, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 203,
    columnNumber: 15
  } }, "👨‍🏫"), /* @__PURE__ */ React.createElement("span", { className: styles.benefitText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 204,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 205,
    columnNumber: 17
  } }, "Para Educadores:"), " Recursos para inclusão escolar e desenvolvimento de habilidades específicas")), /* @__PURE__ */ React.createElement("li", { className: styles.benefitItem, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 208,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.benefitEmoji, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 209,
    columnNumber: 15
  } }, "👨‍👩‍👧"), /* @__PURE__ */ React.createElement("span", { className: styles.benefitText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 210,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 211,
    columnNumber: 17
  } }, "Para Famílias:"), " Atividades estruturadas para momentos de qualidade e desenvolvimento em casa")), /* @__PURE__ */ React.createElement("li", { className: styles.benefitItem, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 214,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.benefitEmoji, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 215,
    columnNumber: 15
  } }, "🤝"), /* @__PURE__ */ React.createElement("span", { className: styles.benefitText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 216,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 217,
    columnNumber: 17
  } }, "Colaboração:"), " Dados e progresso compartilhados entre todos os envolvidos no cuidado da criança"))), /* @__PURE__ */ React.createElement("div", { className: styles.highlightBox, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 222,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("p", { className: styles.highlightText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 223,
    columnNumber: 13
  } }, "💝 ", /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 224,
    columnNumber: 18
  } }, "100% Gratuito e Sempre Será"), /* @__PURE__ */ React.createElement("br", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 224,
    columnNumber: 62
  } }), "Acreditamos que toda criança merece acesso a ferramentas de qualidade para seu desenvolvimento, independentemente da condição socioeconômica da família.")))));
}
export {
  About as default
};
//# sourceMappingURL=About-3oLEsZnh.js.map
