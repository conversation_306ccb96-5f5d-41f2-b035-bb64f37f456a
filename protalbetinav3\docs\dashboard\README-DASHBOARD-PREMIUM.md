# 📊 Guia de Demonstração de Dashboards Premium

## Visão Geral

Este guia explica como acessar e utilizar as dashboards premium do Portal Betina V3, protegidas pelo sistema de autenticação JWT e middleware de permissões.

## Fluxo de Autenticação

### 1. Login

Para acessar as dashboards premium, é necessário autenticar-se usando credenciais válidas:

```
POST /api/auth/dashboard/login
```

**Corpo da requisição:**
```json
{
  "email": "<EMAIL>",
  "password": "admin123"
}
```

**Resposta:**
```json
{
  "success": true,
  "message": "Login realizado com sucesso",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "admin-001",
    "email": "<EMAIL>",
    "role": "admin",
    "name": "<PERSON>ministrador",
    "isPremium": true,
    "permissions": [...]
  }
}
```

### 2. Verificação de Token

Para verificar a validade de um token:

```
GET /api/auth/dashboard/verify
```

**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 3. Listagem de Dashboards Disponíveis

Para obter uma lista de dashboards disponíveis para o usuário autenticado:

```
GET /api/premium/dashboards
```

**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Dashboards Disponíveis

### 1. Dashboard de Métricas Gerais

Disponível para usuários com permissão `VIEW_PREMIUM_DASHBOARDS`:

```
GET /api/premium/dashboards/metrics
```

### 2. Dashboard de Análise Terapêutica

Disponível para usuários com permissão `READ_REPORTS`:

```
GET /api/premium/dashboards/therapeutic
```

### 3. Dashboard Administrativa

Disponível apenas para usuários com permissão `MANAGE_SYSTEM` (administradores):

```
GET /api/premium/dashboards/admin
```

## Usuários de Demonstração

Para facilitar os testes, foram criados os seguintes usuários de demonstração:

| Tipo | Email | Senha | Permissões |
|------|-------|-------|------------|
| Admin | <EMAIL> | admin123 | Todas as permissões |
| Terapeuta | <EMAIL> | terapeuta123 | Leitura/escrita de crianças, relatórios, dashboards premium |
| Pai | <EMAIL> | pai123 | Leitura de dados básicos, dashboards básicas |
| Demo | <EMAIL> | demo123 | Dashboards premium, relatórios |

## Script de Teste

O arquivo `test-dashboard-auth.ps1` contém um script PowerShell para testar automaticamente todas as rotas de autenticação e dashboards com diferentes tipos de usuários:

```powershell
# Executar o script de teste
./test-dashboard-auth.ps1
```

## Segurança

O sistema implementa várias camadas de segurança:

1. **Autenticação JWT**: Tokens JWT com rotação de secrets e expiração.
2. **Middleware de Permissões**: Verificação granular de permissões por rota.
3. **Rate Limiting**: Limitação de tentativas de login e requisições por IP.
4. **Audit Logging**: Registro detalhado de todas as tentativas de acesso.
5. **Cache de Permissões**: Otimização de performance sem comprometer segurança.

## Executando com Docker

Para iniciar o ambiente completo com Docker:

```bash
docker-compose up -d
```

Ou no Windows usando PowerShell:

```powershell
./setup-docker.ps1
```

Após iniciar os containers, as dashboards estarão disponíveis em http://localhost:3000.
