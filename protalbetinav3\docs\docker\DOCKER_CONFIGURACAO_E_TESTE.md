# Guia de Configuração e Teste Docker do Portal Betina V3

Este documento descreve como configurar e testar o sistema Portal Betina V3 usando Docker, levando em consideração tanto o ambiente de desenvolvimento quanto o de produção.

## Estrutura do Projeto

O Portal Betina V3 é uma aplicação com frontend e backend integrados:

- **Frontend**: Aplicação React com Vite que contém jogos terapêuticos e interface de usuário
- **Backend**: API Node.js/Express que gerencia dados, métricas e orquestração terapêutica
- **Banco de Dados**: PostgreSQL para armazenamento persistente

## Preparando o Ambiente

Antes de executar os containers Docker, é necessário configurar corretamente o ambiente:

### Usando os Scripts de Configuração Automática

Execute um dos scripts abaixo para configurar automaticamente o ambiente:

**Windows (PowerShell)**:
```powershell
.\setup-docker.ps1
```

**Linux/Mac/Git Bash**:
```bash
chmod +x setup-docker.sh
./setup-docker.sh
```

Estes scripts vão:
1. Criar o arquivo `frontend-package.json` com as dependências do frontend
2. Criar o arquivo `vite.config.js` para configuração do Vite
3. Criar `Dockerfile.dev` para ambiente de desenvolvimento
4. Criar `docker-compose.dev.yml` para orquestração de containers em desenvolvimento
5. Atualizar `Dockerfile` para usar o correto package.json do frontend

## Modos de Execução

### Ambiente de Desenvolvimento

O ambiente de desenvolvimento oferece hot reload e facilidades para depuração:

```bash
# Iniciar ambiente de desenvolvimento
docker-compose -f docker-compose.dev.yml up -d

# Verificar o ambiente
./test-docker.sh dev  # Linux/Mac
.\test-docker.ps1 -Mode dev  # Windows
```

**Características do ambiente de desenvolvimento**:
- Frontend acessível em: http://localhost:5173 com hot reload
- API acessível em: http://localhost:3000/api
- Volumes montados para desenvolvimento em tempo real
- Depuração habilitada

### Ambiente de Produção

O ambiente de produção é otimizado para performance e escalabilidade:

```bash
# Iniciar ambiente de produção
docker-compose up -d

# Verificar o ambiente
./test-docker.sh  # Linux/Mac
.\test-docker.ps1  # Windows
```

**Características do ambiente de produção**:
- Frontend servido pelo Nginx em: http://localhost:80
- API otimizada em: http://localhost:3000/api
- Monitoramento via Prometheus: http://localhost:9090
- Build otimizado com minificação e splitting

## Problemas Comuns e Soluções

### 1. Erro "Missing script: build"

Este erro ocorre quando o Dockerfile do frontend está tentando executar o comando de build, mas o script não está definido no package.json.

**Solução**: Execute o script de configuração automática (`setup-docker.sh` ou `setup-docker.ps1`) que vai criar o frontend-package.json adequado.

### 2. Problemas de conexão com a API

Se o frontend não conseguir conectar com a API, verifique:

```bash
# Verificar logs da API
docker-compose logs api

# Verificar se a API está respondendo
curl http://localhost:3000/api/health
```

### 3. Erro no Banco de Dados

Se o banco de dados não iniciar corretamente:

```bash
# Verificar logs do banco
docker-compose logs portal-betina-db

# Remover volume e recriar (CUIDADO: apaga todos os dados)
docker-compose down -v
docker volume prune -f
docker-compose up -d
```

## Arquivos de Configuração

Para personalizar o ambiente, você pode editar os seguintes arquivos:

- `.env` - Variáveis de ambiente para produção
- `.env.development` - Variáveis de ambiente para desenvolvimento
- `docker-compose.yml` - Configuração dos serviços em produção
- `docker-compose.dev.yml` - Configuração dos serviços em desenvolvimento

## Comandos Úteis

```bash
# Acompanhar logs
docker-compose logs -f

# Reiniciar apenas um serviço
docker-compose restart api

# Executar comandos dentro de um container
docker-compose exec api sh

# Parar todos os containers
docker-compose down
```
