import { C as CognitiveAssociationEngine, I as IGameProcessor } from "./services-5spxllES.js";
import { r as reactExports, R as React } from "./vendor-react-Bw1F4Ko6.js";
import { S as SystemContext, b as useAccessibilityContext } from "./context-D6Rxw-Zf.js";
import { b as useMultisensoryIntegration, c as useTherapeuticOrchestrator, a as useUnifiedGameLogic } from "./hooks-Cl3iVr0l.js";
import { G as GameStartScreen } from "./game-association-D8ixNKuX.js";
import { v as v4 } from "./vendor-utils-CjlX8hrF.js";
class PhoneticPatternCollector {
  constructor() {
    this.phoneticMap = {
      // Vogais
      "A": { phoneme: "/a/", difficulty: 1, confusionGroup: ["O", "E"] },
      "E": { phoneme: "/e/", difficulty: 2, confusionGroup: ["I", "A"] },
      "I": { phoneme: "/i/", difficulty: 2, confusionGroup: ["E", "Y"] },
      "O": { phoneme: "/o/", difficulty: 1, confusionGroup: ["A", "U"] },
      "U": { phoneme: "/u/", difficulty: 2, confusionGroup: ["O", "V"] },
      // Consoantes frequentes
      "B": { phoneme: "/b/", difficulty: 2, confusionGroup: ["D", "P"] },
      "C": { phoneme: "/k/", difficulty: 3, confusionGroup: ["G", "K"] },
      "D": { phoneme: "/d/", difficulty: 2, confusionGroup: ["B", "P"] },
      "F": { phoneme: "/f/", difficulty: 2, confusionGroup: ["V", "T"] },
      "G": { phoneme: "/g/", difficulty: 3, confusionGroup: ["C", "Q"] },
      "H": { phoneme: "/h/", difficulty: 4, confusionGroup: ["N", "M"] },
      "J": { phoneme: "/ʒ/", difficulty: 4, confusionGroup: ["G", "Y"] },
      "K": { phoneme: "/k/", difficulty: 3, confusionGroup: ["C", "Q"] },
      "L": { phoneme: "/l/", difficulty: 2, confusionGroup: ["I", "1"] },
      "M": { phoneme: "/m/", difficulty: 2, confusionGroup: ["N", "W"] },
      "N": { phoneme: "/n/", difficulty: 2, confusionGroup: ["M", "H"] },
      "P": { phoneme: "/p/", difficulty: 2, confusionGroup: ["B", "D"] },
      "Q": { phoneme: "/k/", difficulty: 4, confusionGroup: ["G", "O"] },
      "R": { phoneme: "/ʁ/", difficulty: 3, confusionGroup: ["P", "B"] },
      "S": { phoneme: "/s/", difficulty: 3, confusionGroup: ["Z", "5"] },
      "T": { phoneme: "/t/", difficulty: 2, confusionGroup: ["F", "L"] },
      "V": { phoneme: "/v/", difficulty: 3, confusionGroup: ["F", "U"] },
      "W": { phoneme: "/w/", difficulty: 4, confusionGroup: ["V", "M"] },
      "X": { phoneme: "/ks/", difficulty: 4, confusionGroup: ["K", "S"] },
      "Y": { phoneme: "/i/", difficulty: 4, confusionGroup: ["I", "V"] },
      "Z": { phoneme: "/z/", difficulty: 3, confusionGroup: ["S", "2"] }
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Analisa padrões fonéticos com base nos dados do jogo
   * @param {Object} data - Dados do jogo
   * @returns {Object} - Análise de padrões fonéticos
   */
  analyze(data) {
    if (!data || !data.letters || !Array.isArray(data.letters) || data.letters.length === 0) {
      console.warn("PhoneticPatternCollector: Dados inválidos ou ausentes");
      return {
        phoneticPatterns: [],
        confusionMatrix: {},
        phonemeRecognitionAccuracy: 0.7,
        difficultyScore: 2.5
      };
    }
    try {
      const recognizedLetters = data.letters.map((l) => l.recognized || "");
      const targetLetters = data.letters.map((l) => l.target || "");
      const patterns = this.extractPhoneticPatterns(targetLetters, recognizedLetters);
      const confusionMatrix = this.buildPhoneticConfusionMatrix(targetLetters, recognizedLetters);
      const accuracy = this.calculatePhonemeRecognitionAccuracy(targetLetters, recognizedLetters);
      const difficultyScore = this.calculatePhoneticDifficultyScore(targetLetters);
      return {
        phoneticPatterns: patterns,
        confusionMatrix,
        phonemeRecognitionAccuracy: accuracy,
        difficultyScore
      };
    } catch (error) {
      console.error("PhoneticPatternCollector: Erro na análise", error);
      return {
        phoneticPatterns: [],
        confusionMatrix: {},
        phonemeRecognitionAccuracy: 0.7,
        difficultyScore: 2.5,
        error: error.message
      };
    }
  }
  /**
   * Extrai padrões fonéticos comparando letras alvo e reconhecidas
   */
  extractPhoneticPatterns(targetLetters, recognizedLetters) {
    const patterns = [];
    return patterns;
  }
  /**
   * Constrói matriz de confusão fonética
   */
  buildPhoneticConfusionMatrix(targetLetters, recognizedLetters) {
    const matrix = {};
    for (let i = 0; i < targetLetters.length; i++) {
      const target = targetLetters[i].toUpperCase();
      const recognized = recognizedLetters[i].toUpperCase();
      if (target !== recognized) {
        if (!matrix[target]) matrix[target] = {};
        if (!matrix[target][recognized]) matrix[target][recognized] = 0;
        matrix[target][recognized]++;
      }
    }
    return matrix;
  }
  /**
   * Calcula precisão de reconhecimento de fonemas
   */
  calculatePhonemeRecognitionAccuracy(targetLetters, recognizedLetters) {
    let correct = 0;
    for (let i = 0; i < targetLetters.length; i++) {
      if (targetLetters[i].toUpperCase() === recognizedLetters[i].toUpperCase()) {
        correct++;
      }
    }
    return targetLetters.length > 0 ? correct / targetLetters.length : 0.7;
  }
  /**
   * Calcula pontuação de dificuldade fonética
   */
  calculatePhoneticDifficultyScore(targetLetters) {
    let totalDifficulty = 0;
    let count = 0;
    targetLetters.forEach((letter) => {
      const upperLetter = letter.toUpperCase();
      if (this.phoneticMap[upperLetter]) {
        totalDifficulty += this.phoneticMap[upperLetter].difficulty;
        count++;
      }
    });
    return count > 0 ? totalDifficulty / count : 2.5;
  }
  calculatePhoneticDifficulty(letter) {
    const phonetics = this.phoneticMap[letter];
    return phonetics ? phonetics.difficulty : 3;
  }
  generatePhoneticRecommendations(data) {
    const recommendations = [];
    if (!data.isCorrect) {
      recommendations.push(`Reforçar associação som-letra para '${data.targetLetter}'`);
      recommendations.push("Praticar discriminação auditiva");
    }
    return recommendations;
  }
}
class LetterConfusionCollector {
  constructor() {
    this.visualConfusionMatrix = {
      // Pares de letras frequentemente confundidas
      "b-d": { type: "mirror", severity: "high", age_expected: [4, 7] },
      "p-q": { type: "mirror", severity: "high", age_expected: [4, 7] },
      "u-n": { type: "rotation", severity: "medium", age_expected: [5, 8] },
      "m-w": { type: "rotation", severity: "medium", age_expected: [5, 8] },
      "g-q": { type: "similar_form", severity: "medium", age_expected: [6, 9] },
      "h-n": { type: "similar_form", severity: "low", age_expected: [6, 9] }
    };
  }
  collect(data) {
    return this.analyze(data);
  }
  async analyze(data) {
    if (!data || !data.targetLetter || !data.selectedLetter) {
      console.warn("LetterConfusionCollector: Dados inválidos recebidos", data);
      return {
        hasConfusion: false,
        confusionType: "none",
        confusionSeverity: "none",
        ageAppropriate: true,
        visualSimilarity: 0,
        phoneticSimilarity: 0,
        interventionNeeded: false,
        trackingMetrics: {
          errorType: "invalid_data",
          timestamp: (/* @__PURE__ */ new Date()).toISOString(),
          frequency: 0
        }
      };
    }
    const targetLetter = typeof data.targetLetter === "string" ? data.targetLetter : data.targetLetter.letter || data.targetLetter.id;
    const selectedLetter = typeof data.selectedLetter === "string" ? data.selectedLetter : data.selectedLetter.letter || data.selectedLetter.id;
    const confusionKey = `${targetLetter.toLowerCase()}-${selectedLetter.toLowerCase()}`;
    const reverseKey = `${selectedLetter.toLowerCase()}-${targetLetter.toLowerCase()}`;
    const confusion = this.visualConfusionMatrix[confusionKey] || this.visualConfusionMatrix[reverseKey];
    return {
      hasConfusion: !!confusion,
      confusionType: confusion?.type || "none",
      confusionSeverity: confusion?.severity || "none",
      ageAppropriate: confusion ? this.isAgeAppropriate(confusion.age_expected) : true,
      visualSimilarity: data.visualSimilarity || 0,
      phoneticSimilarity: data.phoneticSimilarity || 0,
      interventionNeeded: this.assessInterventionNeed(confusion, data),
      trackingMetrics: this.generateTrackingMetrics(data)
    };
  }
  isAgeAppropriate(ageRange) {
    const currentAge = 6;
    return currentAge >= ageRange[0] && currentAge <= ageRange[1];
  }
  assessInterventionNeed(confusion, data) {
    if (!confusion) return false;
    return confusion.severity === "high" && !data.isCorrect;
  }
  generateTrackingMetrics(data) {
    const targetLetter = typeof data.targetLetter === "string" ? data.targetLetter : data.targetLetter?.letter || data.targetLetter?.id;
    const selectedLetter = typeof data.selectedLetter === "string" ? data.selectedLetter : data.selectedLetter?.letter || data.selectedLetter?.id;
    return {
      errorType: this.classifyErrorType(targetLetter, selectedLetter),
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      frequency: 1
      // Será agregado pelo sistema
    };
  }
  classifyErrorType(target, selected) {
    if (!target || !selected || typeof target !== "string" || typeof selected !== "string") {
      console.warn("LetterConfusionCollector: target ou selected inválidos:", { target, selected });
      return "invalid_input";
    }
    if (target === selected) return "correct";
    if (this.isMirrorError(target, selected)) return "mirror";
    if (this.isRotationError(target, selected)) return "rotation";
    if (this.isFormSimilarity(target, selected)) return "form_similarity";
    return "random";
  }
  isMirrorError(target, selected) {
    if (!target || !selected || typeof target !== "string" || typeof selected !== "string") {
      return false;
    }
    const mirrorPairs = ["b-d", "p-q"];
    const key = `${target.toLowerCase()}-${selected.toLowerCase()}`;
    const reverseKey = `${selected.toLowerCase()}-${target.toLowerCase()}`;
    return mirrorPairs.includes(key) || mirrorPairs.includes(reverseKey);
  }
  isRotationError(target, selected) {
    if (!target || !selected || typeof target !== "string" || typeof selected !== "string") {
      return false;
    }
    const rotationPairs = ["u-n", "m-w"];
    const key = `${target.toLowerCase()}-${selected.toLowerCase()}`;
    const reverseKey = `${selected.toLowerCase()}-${target.toLowerCase()}`;
    return rotationPairs.includes(key) || rotationPairs.includes(reverseKey);
  }
  isFormSimilarity(target, selected) {
    if (!target || !selected || typeof target !== "string" || typeof selected !== "string") {
      return false;
    }
    const similarPairs = ["g-q", "h-n", "r-n"];
    const key = `${target.toLowerCase()}-${selected.toLowerCase()}`;
    const reverseKey = `${selected.toLowerCase()}-${target.toLowerCase()}`;
    return similarPairs.includes(key) || similarPairs.includes(reverseKey);
  }
}
class VisualLinguisticCollector {
  constructor() {
    this.letterComplexity = {
      // Complexidade visual das letras (1-5)
      "I": 1,
      "L": 1,
      "O": 1,
      "C": 1,
      // Muito simples
      "T": 2,
      "F": 2,
      "E": 2,
      "H": 2,
      "U": 2,
      // Simples
      "A": 3,
      "V": 3,
      "Y": 3,
      "N": 3,
      "Z": 3,
      // Médio
      "B": 4,
      "D": 4,
      "P": 4,
      "R": 4,
      "K": 4,
      // Complexo
      "G": 5,
      "Q": 5,
      "S": 5,
      "M": 5,
      "W": 5
      // Muito complexo
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  async analyze(data) {
    if (!data || !data.letterForm) {
      console.warn("VisualLinguisticCollector: Dados inválidos recebidos", data);
      return {
        visualComplexity: 3,
        recognitionSpeed: data?.recognitionSpeed || 2500,
        relativeSpeed: 1,
        visualProcessingEfficiency: 0.5,
        contextualInfluence: 1,
        visualMemoryLoad: 0.6,
        letterFormMastery: 0.5
      };
    }
    const complexity = this.letterComplexity[data.letterForm] || 3;
    const expectedTime = this.getExpectedRecognitionTime(complexity);
    return {
      visualComplexity: complexity,
      recognitionSpeed: data.recognitionSpeed || 2500,
      relativeSpeed: data.recognitionSpeed / expectedTime,
      visualProcessingEfficiency: this.calculateProcessingEfficiency(data.recognitionSpeed, expectedTime),
      contextualInfluence: this.analyzeContextualEffect(data.context),
      visualMemoryLoad: this.assessVisualMemoryLoad(data.letterForm),
      letterFormMastery: this.assessLetterFormMastery(data)
    };
  }
  getExpectedRecognitionTime(complexity) {
    const baseTimes = { 1: 1500, 2: 2e3, 3: 2500, 4: 3e3, 5: 3500 };
    return baseTimes[complexity] || 2500;
  }
  calculateProcessingEfficiency(actualTime, expectedTime) {
    return Math.max(0, Math.min(1, expectedTime / actualTime));
  }
  analyzeContextualEffect(context) {
    const contextEffects = {
      "isolated": 1,
      // Letra isolada - baseline
      "word": 0.8,
      // Em palavra - facilita
      "sentence": 0.7,
      // Em frase - mais fácil
      "mixed": 1.2
      // Misturado - mais difícil
    };
    return contextEffects[context] || 1;
  }
  assessVisualMemoryLoad(letterForm) {
    const complexity = this.letterComplexity[letterForm] || 3;
    return complexity / 5;
  }
  assessLetterFormMastery(data) {
    const speedMastery = data.recognitionSpeed < 2e3 ? 1 : 0.5;
    const complexityMastery = this.letterComplexity[data.letterForm] <= 3 ? 1 : 0.7;
    return (speedMastery + complexityMastery) / 2;
  }
}
class ReadingDevelopmentCollector {
  constructor() {
    this.developmentStages = {
      "pre_alphabetic": {
        age_range: [3, 5],
        characteristics: ["letter_recognition", "sound_awareness"],
        milestones: ["knows_some_letters", "recognizes_name"]
      },
      "partial_alphabetic": {
        age_range: [4, 6],
        characteristics: ["letter_sound_connection", "beginning_sounds"],
        milestones: ["most_letters", "simple_words"]
      },
      "full_alphabetic": {
        age_range: [5, 7],
        characteristics: ["decoding", "blending"],
        milestones: ["all_letters", "cvc_words", "reading_sentences"]
      },
      "consolidated_alphabetic": {
        age_range: [6, 8],
        characteristics: ["fluent_reading", "sight_words"],
        milestones: ["fluent_decoding", "reading_comprehension"]
      }
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  async analyze(data) {
    if (!data) {
      console.warn("ReadingDevelopmentCollector: Dados inválidos recebidos", data);
      return {
        developmentStage: "pre_alphabetic",
        stageProgress: { overall: 0.5, letterRecognition: 0.5, soundMapping: 0.5, timeToMastery: 12 },
        alphabetKnowledge: 0.5,
        phoneticAwareness: 0.5,
        readingReadiness: 0.5,
        nextMilestones: ["knows_some_letters", "recognizes_name"],
        interventionRecommendations: ["Verificar dados de entrada"]
      };
    }
    const currentStage = this.identifyDevelopmentStage(data);
    const progress = this.assessProgress(data, currentStage);
    return {
      developmentStage: currentStage,
      stageProgress: progress,
      alphabetKnowledge: data.alphabetKnowledge,
      phoneticAwareness: data.phoneticAwareness,
      readingReadiness: this.assessReadingReadiness(data),
      nextMilestones: this.getNextMilestones(currentStage),
      interventionRecommendations: this.generateInterventionRecommendations(data, currentStage)
    };
  }
  identifyDevelopmentStage(data) {
    if (data.alphabetKnowledge < 0.5) return "pre_alphabetic";
    if (data.alphabetKnowledge < 0.8) return "partial_alphabetic";
    if (data.phoneticAwareness < 0.8) return "full_alphabetic";
    return "consolidated_alphabetic";
  }
  assessProgress(data, stage) {
    this.developmentStages[stage];
    return {
      overall: data.sessionData?.accuracy || 0.7,
      letterRecognition: data.alphabetKnowledge,
      soundMapping: data.phoneticAwareness,
      timeToMastery: this.estimateTimeToMastery(data, stage)
    };
  }
  assessReadingReadiness(data) {
    const factors = {
      letterKnowledge: data.alphabetKnowledge * 0.3,
      phoneticAwareness: data.phoneticAwareness * 0.3,
      visualProcessing: (data.sessionData?.averageResponseTime < 3e3 ? 1 : 0.5) * 0.2,
      motivation: (data.sessionData?.engagement || 0.8) * 0.2
    };
    return Object.values(factors).reduce((sum, factor) => sum + factor, 0);
  }
  getNextMilestones(stage) {
    const stageOrder = ["pre_alphabetic", "partial_alphabetic", "full_alphabetic", "consolidated_alphabetic"];
    const currentIndex = stageOrder.indexOf(stage);
    const nextStage = stageOrder[currentIndex + 1];
    return nextStage ? this.developmentStages[nextStage].milestones : ["advanced_reading_skills"];
  }
  generateInterventionRecommendations(data, stage) {
    const recommendations = [];
    if (stage === "pre_alphabetic") {
      recommendations.push("Aumentar exposição a letras do alfabeto");
      recommendations.push("Jogos de consciência fonológica");
    } else if (stage === "partial_alphabetic") {
      recommendations.push("Praticar correspondência letra-som");
      recommendations.push("Exercícios de segmentação fonêmica");
    } else if (stage === "full_alphabetic") {
      recommendations.push("Atividades de decodificação");
      recommendations.push("Prática com palavras CVC");
    }
    return recommendations;
  }
  estimateTimeToMastery(data, stage) {
    const progressRate = data.sessionData?.progressionPattern || 0.1;
    const remainingProgress = 1 - (data.alphabetKnowledge || 0.5);
    return Math.ceil(remainingProgress / progressRate);
  }
}
class DyslexiaIndicatorCollector {
  constructor() {
    this.riskFactors = {
      // Indicadores precoces de risco de dislexia
      letterReversals: { weight: 0.3, threshold: 0.2 },
      processingSpeed: { weight: 0.25, threshold: 4e3 },
      // ms
      sequencingDifficulties: { weight: 0.2, threshold: 0.3 },
      phoneticAwareness: { weight: 0.25, threshold: 0.5 }
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  async analyze(data) {
    if (!data || typeof data !== "object") {
      console.warn("DyslexiaIndicatorCollector: Dados inválidos - objeto vazio ou nulo", data);
      return this.getDefaultResult();
    }
    const requiredFields = ["letterReversals", "errorPatterns", "processingSpeed", "sequencingDifficulties"];
    const missingFields = requiredFields.filter((field) => !data.hasOwnProperty(field));
    if (missingFields.length > 0) {
      console.warn(`DyslexiaIndicatorCollector: Campos obrigatórios ausentes: ${missingFields.join(", ")}`, data);
      return this.getDefaultResult();
    }
    const indicators = this.assessRiskIndicators(data);
    const riskLevel = this.calculateOverallRisk(indicators);
    return {
      riskLevel,
      specificIndicators: indicators,
      recommendations: this.generateDyslexiaRecommendations(riskLevel, indicators),
      monitoring: this.createMonitoringPlan(indicators),
      earlyIntervention: this.suggestEarlyIntervention(riskLevel)
    };
  }
  getDefaultResult() {
    return {
      riskLevel: "low",
      specificIndicators: {},
      recommendations: ["Verificar dados de entrada"],
      monitoring: {
        frequency: "monthly",
        metrics: ["letter_recognition_accuracy"],
        duration: 3
      },
      earlyIntervention: ["Continuação das atividades regulares"]
    };
  }
  assessRiskIndicators(data) {
    const indicators = {};
    const safeLetterReversals = Array.isArray(data.letterReversals) ? data.letterReversals : [];
    const safeSequencingDifficulties = Array.isArray(data.sequencingDifficulties) ? data.sequencingDifficulties : [];
    const safeProcessingSpeed = typeof data.processingSpeed === "number" ? data.processingSpeed : 0;
    const safePhoneticAwareness = typeof data.phoneticAwareness === "number" ? data.phoneticAwareness : 1;
    const totalAttempts = Math.max(1, data.errorPatterns?.total || 1);
    const reversalRate = safeLetterReversals.length / totalAttempts;
    indicators.letterReversals = {
      value: Math.round(reversalRate * 1e3) / 1e3,
      // Arredondar para 3 casas decimais
      count: safeLetterReversals.length,
      total: totalAttempts,
      risk: reversalRate > this.riskFactors.letterReversals.threshold ? "high" : "low"
    };
    indicators.processingSpeed = {
      value: safeProcessingSpeed,
      risk: safeProcessingSpeed > this.riskFactors.processingSpeed.threshold ? "high" : "low"
    };
    const sequencingRate = safeSequencingDifficulties.length / totalAttempts;
    indicators.sequencingDifficulties = {
      value: Math.round(sequencingRate * 1e3) / 1e3,
      // Arredondar para 3 casas decimais
      count: safeSequencingDifficulties.length,
      total: totalAttempts,
      risk: sequencingRate > this.riskFactors.sequencingDifficulties.threshold ? "high" : "low"
    };
    indicators.phoneticAwareness = {
      value: Math.round(safePhoneticAwareness * 1e3) / 1e3,
      risk: safePhoneticAwareness < this.riskFactors.phoneticAwareness.threshold ? "high" : "low"
    };
    return indicators;
  }
  calculateOverallRisk(indicators) {
    let riskScore = 0;
    let totalWeight = 0;
    if (!indicators || typeof indicators !== "object") {
      console.warn("DyslexiaIndicatorCollector: Indicadores inválidos");
      return "low";
    }
    Object.entries(this.riskFactors).forEach(([factor, config]) => {
      if (indicators[factor] && indicators[factor].risk) {
        const indicatorRisk = indicators[factor].risk === "high" ? 1 : 0;
        riskScore += indicatorRisk * config.weight;
        totalWeight += config.weight;
      }
    });
    if (totalWeight === 0) {
      console.warn("DyslexiaIndicatorCollector: Nenhum fator de risco válido encontrado");
      return "low";
    }
    const normalizedRisk = riskScore / totalWeight;
    if (normalizedRisk >= 0.6) return "high";
    if (normalizedRisk >= 0.3) return "moderate";
    return "low";
  }
  generateDyslexiaRecommendations(riskLevel, indicators) {
    const recommendations = [];
    if (riskLevel === "high") {
      recommendations.push("Encaminhar para avaliação especializada");
      recommendations.push("Implementar estratégias multissensoriais");
      recommendations.push("Aumentar frequência de monitoramento");
    } else if (riskLevel === "moderate") {
      recommendations.push("Intensificar trabalho com consciência fonológica");
      recommendations.push("Monitoramento contínuo do progresso");
      recommendations.push("Estratégias preventivas");
    }
    return recommendations;
  }
  createMonitoringPlan(indicators) {
    return {
      frequency: this.determineMonitoringFrequency(indicators),
      metrics: this.selectMonitoringMetrics(indicators),
      duration: this.estimateMonitoringDuration(indicators)
    };
  }
  determineMonitoringFrequency(indicators) {
    if (!indicators || typeof indicators !== "object") {
      return "monthly";
    }
    const highRiskCount = Object.values(indicators).filter(
      (indicator) => indicator && indicator.risk === "high"
    ).length;
    if (highRiskCount >= 2) return "weekly";
    if (highRiskCount >= 1) return "bi-weekly";
    return "monthly";
  }
  selectMonitoringMetrics(indicators) {
    const metrics = ["letter_recognition_accuracy", "response_time"];
    if (!indicators || typeof indicators !== "object") {
      return metrics;
    }
    if (indicators.letterReversals?.risk === "high") {
      metrics.push("reversal_frequency");
    }
    if (indicators.sequencingDifficulties?.risk === "high") {
      metrics.push("sequencing_accuracy");
    }
    if (indicators.phoneticAwareness?.risk === "high") {
      metrics.push("phonetic_awareness_score");
    }
    return [...new Set(metrics)];
  }
  estimateMonitoringDuration(indicators) {
    if (!indicators || typeof indicators !== "object") {
      return 3;
    }
    const highRiskCount = Object.values(indicators).filter(
      (indicator) => indicator && indicator.risk === "high"
    ).length;
    const calculatedDuration = Math.max(3, highRiskCount * 2);
    return Math.min(calculatedDuration, 12);
  }
  suggestEarlyIntervention(riskLevel) {
    const interventions = {
      "high": [
        "Programa intensivo de consciência fonológica",
        "Treinamento multissensorial de letras",
        "Suporte individualizado"
      ],
      "moderate": [
        "Atividades extras de reconhecimento de letras",
        "Jogos de consciência fonológica",
        "Monitoramento próximo"
      ],
      "low": [
        "Continuação das atividades regulares",
        "Reforço positivo",
        "Monitoramento de rotina"
      ]
    };
    return interventions[riskLevel] || interventions["low"];
  }
}
class CognitivePatternCollector {
  constructor() {
    this.cognitiveEngine = new CognitiveAssociationEngine();
    this.cognitiveData = {
      attentionPatterns: [],
      memoryPatterns: [],
      processingSpeed: [],
      executiveFunction: [],
      visualSpatial: [],
      auditoryProcessing: []
    };
    this.sessionStartTime = Date.now();
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Coleta padrões de atenção durante o jogo
   */
  collectAttentionPattern(interaction) {
    const attentionData = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      sessionTime: Date.now() - this.sessionStartTime,
      responseTime: interaction.responseTime,
      accuracy: interaction.isCorrect,
      attentionScore: this.calculateAttentionScore(interaction),
      focusQuality: this.assessFocusQuality(interaction),
      distractionLevel: this.detectDistraction(interaction)
    };
    this.cognitiveData.attentionPatterns.push(attentionData);
    return attentionData;
  }
  /**
   * Coleta padrões de memória de trabalho
   */
  collectMemoryPattern(targetLetter, sequence, responseData) {
    const memoryData = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      targetLetter,
      sequenceLength: sequence.length,
      memoryLoad: this.calculateMemoryLoad(sequence),
      recallAccuracy: responseData.isCorrect,
      recallTime: responseData.responseTime,
      memoryStrategy: this.identifyMemoryStrategy(responseData),
      workingMemoryCapacity: this.assessWorkingMemoryCapacity(sequence, responseData)
    };
    this.cognitiveData.memoryPatterns.push(memoryData);
    return memoryData;
  }
  /**
   * Coleta dados de velocidade de processamento
   */
  collectProcessingSpeed(task) {
    const processingData = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      taskType: task.type,
      complexity: task.complexity,
      processingTime: task.responseTime,
      efficiency: this.calculateProcessingEfficiency(task),
      speedCategory: this.categorizeProcessingSpeed(task.responseTime),
      cognitiveLoad: this.assessCognitiveLoad(task)
    };
    this.cognitiveData.processingSpeed.push(processingData);
    return processingData;
  }
  /**
   * Coleta dados de função executiva
   */
  collectExecutiveFunction(decisionTask) {
    const executiveData = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      inhibitoryControl: this.assessInhibitoryControl(decisionTask),
      workingMemory: this.assessExecutiveMemory(decisionTask),
      cognitiveFlexibility: this.assessCognitiveFlexibility(decisionTask),
      planningAbility: this.assessPlanningAbility(decisionTask),
      errorMonitoring: this.assessErrorMonitoring(decisionTask)
    };
    this.cognitiveData.executiveFunction.push(executiveData);
    return executiveData;
  }
  /**
   * Coleta dados visuoespaciais
   */
  collectVisualSpatialData(visualTask) {
    const visualData = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      spatialOrientation: this.assessSpatialOrientation(visualTask),
      visualPerception: this.assessVisualPerception(visualTask),
      visualMemory: this.assessVisualMemory(visualTask),
      visualMotorIntegration: this.assessVisualMotorIntegration(visualTask),
      figureGroundDiscrimination: this.assessFigureGroundDiscrimination(visualTask)
    };
    this.cognitiveData.visualSpatial.push(visualData);
    return visualData;
  }
  /**
   * Coleta dados de processamento auditivo
   */
  collectAuditoryProcessing(auditoryTask) {
    const auditoryData = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      phoneticDiscrimination: this.assessPhoneticDiscrimination(auditoryTask),
      auditoryMemory: this.assessAuditoryMemory(auditoryTask),
      auditorySequencing: this.assessAuditorySequencing(auditoryTask),
      soundSymbolAssociation: this.assessSoundSymbolAssociation(auditoryTask),
      auditoryProcessingSpeed: this.assessAuditoryProcessingSpeed(auditoryTask)
    };
    this.cognitiveData.auditoryProcessing.push(auditoryData);
    return auditoryData;
  }
  // Métodos de cálculo e avaliação
  calculateAttentionScore(interaction) {
    const baseScore = interaction.isCorrect ? 1 : 0;
    const timeBonus = Math.max(0, 1 - interaction.responseTime / 5e3);
    const consistencyBonus = this.calculateConsistencyBonus();
    return Math.min(1, baseScore + timeBonus * 0.3 + consistencyBonus * 0.2);
  }
  assessFocusQuality(interaction) {
    const recentInteractions = this.cognitiveData.attentionPatterns.slice(-5);
    const accuracyTrend = recentInteractions.filter((i) => i.accuracy).length / recentInteractions.length || 0;
    const speedConsistency = this.calculateSpeedConsistency(recentInteractions);
    return accuracyTrend * 0.6 + speedConsistency * 0.4;
  }
  detectDistraction(interaction) {
    const avgResponseTime = this.getAverageResponseTime();
    const currentTime = interaction.responseTime;
    if (currentTime > avgResponseTime * 2) return "high";
    if (currentTime > avgResponseTime * 1.5) return "medium";
    return "low";
  }
  calculateMemoryLoad(sequence) {
    const complexityFactors = {
      length: sequence.length,
      similarity: this.calculateSequenceSimilarity(sequence),
      interference: this.calculateInterference(sequence)
    };
    return complexityFactors.length * 0.4 + complexityFactors.similarity * 0.3 + complexityFactors.interference * 0.3;
  }
  identifyMemoryStrategy(responseData) {
    const strategies = [];
    if (responseData.responseTime < 2e3) strategies.push("automatic_recall");
    if (responseData.responseTime > 4e3) strategies.push("effortful_retrieval");
    if (this.detectRehearsalPattern(responseData)) strategies.push("rehearsal");
    if (this.detectChunkingPattern(responseData)) strategies.push("chunking");
    return strategies.length > 0 ? strategies : ["unknown"];
  }
  assessWorkingMemoryCapacity(sequence, responseData) {
    const maxCapacity = 7;
    const currentLoad = sequence.length;
    const performanceScore = responseData.isCorrect ? 1 : 0;
    return Math.min(1, performanceScore * currentLoad / maxCapacity);
  }
  calculateProcessingEfficiency(task) {
    const accuracyWeight = task.isCorrect ? 1 : 0;
    const speedWeight = Math.max(0, 1 - task.responseTime / 1e4);
    return accuracyWeight * 0.7 + speedWeight * 0.3;
  }
  categorizeProcessingSpeed(responseTime) {
    if (responseTime < 1e3) return "very_fast";
    if (responseTime < 2e3) return "fast";
    if (responseTime < 4e3) return "average";
    if (responseTime < 6e3) return "slow";
    return "very_slow";
  }
  assessCognitiveLoad(task) {
    const complexityFactors = {
      taskDifficulty: task.complexity || 1,
      visualElements: task.visualElements || 1,
      auditoryElements: task.auditoryElements || 0,
      memoryRequirement: task.memoryRequirement || 1
    };
    return Object.values(complexityFactors).reduce((sum, factor) => sum + factor, 0) / 4;
  }
  assessInhibitoryControl(decisionTask) {
    const impulsiveResponses = decisionTask.responseTime < 500 ? 1 : 0;
    const correctInhibition = decisionTask.isCorrect && decisionTask.responseTime > 1e3 ? 1 : 0;
    return correctInhibition - impulsiveResponses * 0.5;
  }
  assessExecutiveMemory(decisionTask) {
    const memoryAccuracy = decisionTask.memoryComponent?.accuracy || 0;
    const memoryCapacity = decisionTask.memoryComponent?.capacity || 0;
    return memoryAccuracy * 0.6 + memoryCapacity * 0.4;
  }
  assessCognitiveFlexibility(decisionTask) {
    const setShiftingAbility = decisionTask.setShifting?.success || 0;
    const adaptationSpeed = decisionTask.setShifting?.adaptationTime || 0;
    return setShiftingAbility - adaptationSpeed / 5e3;
  }
  assessPlanningAbility(decisionTask) {
    const planningSteps = decisionTask.planning?.steps || 0;
    const planningEfficiency = decisionTask.planning?.efficiency || 0;
    return planningSteps * 0.4 + planningEfficiency * 0.6;
  }
  assessErrorMonitoring(decisionTask) {
    const errorDetection = decisionTask.errorMonitoring?.detected || 0;
    const errorCorrection = decisionTask.errorMonitoring?.corrected || 0;
    return errorDetection * 0.5 + errorCorrection * 0.5;
  }
  assessSpatialOrientation(visualTask) {
    const orientationAccuracy = visualTask.spatialOrientation?.accuracy || 0;
    const rotationHandling = visualTask.spatialOrientation?.rotationSuccess || 0;
    return orientationAccuracy * 0.6 + rotationHandling * 0.4;
  }
  assessVisualPerception(visualTask) {
    const perceptionAccuracy = visualTask.visualPerception?.accuracy || 0;
    const perceptionSpeed = Math.max(0, 1 - (visualTask.visualPerception?.time || 0) / 5e3);
    return perceptionAccuracy * 0.7 + perceptionSpeed * 0.3;
  }
  assessVisualMemory(visualTask) {
    const memoryAccuracy = visualTask.visualMemory?.accuracy || 0;
    const memoryDuration = visualTask.visualMemory?.retentionTime || 0;
    return memoryAccuracy * Math.min(1, memoryDuration / 3e3);
  }
  assessVisualMotorIntegration(visualTask) {
    const coordination = visualTask.visualMotor?.coordination || 0;
    const precision = visualTask.visualMotor?.precision || 0;
    return coordination * 0.5 + precision * 0.5;
  }
  assessFigureGroundDiscrimination(visualTask) {
    const discriminationAccuracy = visualTask.figureGround?.accuracy || 0;
    const discriminationSpeed = Math.max(0, 1 - (visualTask.figureGround?.time || 0) / 4e3);
    return discriminationAccuracy * 0.8 + discriminationSpeed * 0.2;
  }
  assessPhoneticDiscrimination(auditoryTask) {
    const phoneticAccuracy = auditoryTask.phoneticDiscrimination?.accuracy || 0;
    const fineDifferences = auditoryTask.phoneticDiscrimination?.fineDifferences || 0;
    return phoneticAccuracy * 0.7 + fineDifferences * 0.3;
  }
  assessAuditoryMemory(auditoryTask) {
    const memorySpan = auditoryTask.auditoryMemory?.span || 0;
    const sequenceAccuracy = auditoryTask.auditoryMemory?.sequenceAccuracy || 0;
    return memorySpan * 0.4 + sequenceAccuracy * 0.6;
  }
  assessAuditorySequencing(auditoryTask) {
    const sequenceOrder = auditoryTask.auditorySequencing?.orderAccuracy || 0;
    const sequenceLength = auditoryTask.auditorySequencing?.lengthHandled || 0;
    return sequenceOrder * 0.6 + Math.min(1, sequenceLength / 7) * 0.4;
  }
  assessSoundSymbolAssociation(auditoryTask) {
    const associationAccuracy = auditoryTask.soundSymbol?.accuracy || 0;
    const associationSpeed = Math.max(0, 1 - (auditoryTask.soundSymbol?.time || 0) / 3e3);
    return associationAccuracy * 0.8 + associationSpeed * 0.2;
  }
  assessAuditoryProcessingSpeed(auditoryTask) {
    const processingTime = auditoryTask.processingSpeed?.time || 0;
    const processingAccuracy = auditoryTask.processingSpeed?.accuracy || 0;
    const speedScore = Math.max(0, 1 - processingTime / 2e3);
    return speedScore * 0.4 + processingAccuracy * 0.6;
  }
  // Métodos auxiliares
  calculateConsistencyBonus() {
    const recentScores = this.cognitiveData.attentionPatterns.slice(-5).map((p) => p.attentionScore || 0);
    if (recentScores.length < 3) return 0;
    const mean = recentScores.reduce((sum, score) => sum + score, 0) / recentScores.length;
    const variance = recentScores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / recentScores.length;
    return Math.max(0, 1 - variance);
  }
  calculateSpeedConsistency(interactions) {
    if (interactions.length < 2) return 1;
    const times = interactions.map((i) => i.responseTime || 0);
    const mean = times.reduce((sum, time) => sum + time, 0) / times.length;
    const variance = times.reduce((sum, time) => sum + Math.pow(time - mean, 2), 0) / times.length;
    const cv = Math.sqrt(variance) / mean;
    return Math.max(0, 1 - cv);
  }
  getAverageResponseTime() {
    const allTimes = this.cognitiveData.attentionPatterns.map((p) => p.responseTime || 0);
    return allTimes.length > 0 ? allTimes.reduce((sum, time) => sum + time, 0) / allTimes.length : 3e3;
  }
  calculateSequenceSimilarity(sequence) {
    let similarities = 0;
    for (let i = 0; i < sequence.length - 1; i++) {
      if (this.areElementsSimilar(sequence[i], sequence[i + 1])) {
        similarities++;
      }
    }
    return similarities / Math.max(1, sequence.length - 1);
  }
  calculateInterference(sequence) {
    const duplicates = new Set(sequence).size !== sequence.length ? 1 : 0;
    const patterns = this.detectPatterns(sequence) ? 0.5 : 0;
    return duplicates + patterns;
  }
  detectRehearsalPattern(responseData) {
    return responseData.responseTime > 3e3 && responseData.isCorrect;
  }
  detectChunkingPattern(responseData) {
    return responseData.responsePattern?.includes("pause") || false;
  }
  areElementsSimilar(element1, element2) {
    if (typeof element1 === "string" && typeof element2 === "string") {
      const visualSimilarity = this.calculateVisualSimilarity(element1, element2);
      return visualSimilarity.similarity > 0.5;
    }
    return false;
  }
  calculateVisualSimilarity(letter1, letter2) {
    const visualGroups = {
      "mirror_confusion": [["B", "D"], ["P", "Q"], ["U", "N"]],
      "rotation_confusion": [["M", "W"]],
      "similar_shapes": [["C", "O"], ["I", "L"], ["V", "Y"]]
    };
    for (const groups of Object.values(visualGroups)) {
      for (const group of groups) {
        if (group.includes(letter1) && group.includes(letter2)) {
          return { similarity: 0.9 };
        }
      }
    }
    return { similarity: 0.1 };
  }
  detectPatterns(sequence) {
    for (let i = 0; i < sequence.length - 2; i++) {
      if (sequence[i] === sequence[i + 2]) return true;
    }
    return false;
  }
  /**
   * Análise cognitiva completa usando CognitiveAssociationEngine
   */
  async performCognitiveAnalysis() {
    const gameMetrics = {
      sessionId: `letter_cognitive_${Date.now()}`,
      userId: "current_user",
      attempts: this.getAllInteractions(),
      sessionDuration: Date.now() - this.sessionStartTime,
      gameType: "LetterRecognition"
    };
    try {
      const colorAnalysis = this.cognitiveEngine.analyzeColorAccuracyPattern(gameMetrics);
      const neuropsychProfile = this.cognitiveEngine.analyzeNeuropsychologicalPattern(gameMetrics);
      const fatigueAnalysis = this.cognitiveEngine.analyzeFatigueAlgorithm(gameMetrics);
      const anomalies = this.cognitiveEngine.detectAnomalousPatterns(gameMetrics);
      return {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        cognitiveProfile: colorAnalysis.cognitiveProfile,
        neuropsychologicalProfile: neuropsychProfile,
        fatigueAnalysis,
        anomalies,
        insights: colorAnalysis.insights,
        attentionAnalysis: this.analyzeAttentionPatterns(),
        memoryAnalysis: this.analyzeMemoryPatterns(),
        processingAnalysis: this.analyzeProcessingSpeed(),
        executiveAnalysis: this.analyzeExecutiveFunction(),
        visualSpatialAnalysis: this.analyzeVisualSpatialData(),
        auditoryAnalysis: this.analyzeAuditoryProcessing()
      };
    } catch (error) {
      console.error("Erro na análise cognitiva:", error);
      return this.getBasicCognitiveAnalysis();
    }
  }
  /**
   * Método principal de análise para compatibilidade com o hub
   * @param {Object} enrichedData - Dados enriquecidos da tentativa
   * @returns {Object} Análise cognitiva completa
   */
  async analyze(enrichedData) {
    try {
      if (enrichedData.interaction) {
        this.collectAttentionPattern(enrichedData.interaction);
        if (enrichedData.targetLetter && enrichedData.selectedLetter) {
          this.collectMemoryPattern(
            enrichedData.targetLetter,
            [enrichedData.targetLetter],
            {
              isCorrect: enrichedData.isCorrect,
              responseTime: enrichedData.responseTime || 0
            }
          );
          this.collectProcessingSpeedData(
            enrichedData.targetLetter,
            enrichedData.responseTime || 0,
            enrichedData.isCorrect
          );
        }
      }
      return {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        cognitiveProfile: this.getCognitiveProfile(),
        neuropsychologicalProfile: this.generateCognitiveReport(),
        fatigueAnalysis: this.assessDataQuality(),
        anomalies: [],
        insights: this.generateCognitiveRecommendations(),
        attentionAnalysis: this.analyzeAttentionPatterns(),
        memoryAnalysis: this.analyzeMemoryPatterns(),
        processingAnalysis: this.analyzeProcessingSpeed(),
        executiveAnalysis: this.analyzeExecutiveFunction(),
        visualSpatialAnalysis: this.analyzeVisualSpatialData(),
        auditoryAnalysis: this.analyzeAuditoryProcessing(),
        hasData: true,
        dataQuality: "high"
      };
    } catch (error) {
      console.error("Erro na análise de padrões cognitivos:", error);
      return {
        error: error.message,
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        hasData: false
      };
    }
  }
  getAllInteractions() {
    const allInteractions = [];
    this.cognitiveData.attentionPatterns.forEach((pattern) => {
      allInteractions.push({
        type: "attention",
        timestamp: new Date(pattern.timestamp).getTime(),
        isCorrect: pattern.accuracy,
        responseTime: pattern.responseTime,
        data: pattern
      });
    });
    this.cognitiveData.memoryPatterns.forEach((pattern) => {
      allInteractions.push({
        type: "memory",
        timestamp: new Date(pattern.timestamp).getTime(),
        isCorrect: pattern.recallAccuracy,
        responseTime: pattern.recallTime,
        data: pattern
      });
    });
    return allInteractions;
  }
  analyzeAttentionPatterns() {
    const patterns = this.cognitiveData.attentionPatterns;
    if (patterns.length === 0) return { noData: true };
    return {
      averageAttentionScore: this.calculateAverage(patterns.map((p) => p.attentionScore)),
      focusQualityTrend: this.calculateTrend(patterns.map((p) => p.focusQuality)),
      distractionFrequency: patterns.filter((p) => p.distractionLevel === "high").length / patterns.length,
      attentionStability: this.calculateAttentionStability(patterns),
      sustainedAttentionCapacity: this.calculateSustainedAttention(patterns)
    };
  }
  analyzeMemoryPatterns() {
    const patterns = this.cognitiveData.memoryPatterns;
    if (patterns.length === 0) return { noData: true };
    return {
      averageWorkingMemoryCapacity: this.calculateAverage(patterns.map((p) => p.workingMemoryCapacity)),
      memoryStrategies: this.getFrequentMemoryStrategies(patterns),
      memoryEfficiency: this.calculateMemoryEfficiency(patterns),
      memorySpan: this.calculateMemorySpan(patterns),
      memoryLoadTolerance: this.calculateMemoryLoadTolerance(patterns)
    };
  }
  analyzeProcessingSpeed() {
    const patterns = this.cognitiveData.processingSpeed;
    if (patterns.length === 0) return { noData: true };
    return {
      averageProcessingEfficiency: this.calculateAverage(patterns.map((p) => p.efficiency)),
      speedDistribution: this.getSpeedDistribution(patterns),
      processingSpeedTrend: this.calculateTrend(patterns.map((p) => p.efficiency)),
      cognitiveLoadHandling: this.calculateCognitiveLoadHandling(patterns),
      speedConsistency: this.calculateProcessingSpeedConsistency(patterns)
    };
  }
  analyzeExecutiveFunction() {
    const patterns = this.cognitiveData.executiveFunction;
    if (patterns.length === 0) return { noData: true };
    return {
      inhibitoryControlScore: this.calculateAverage(patterns.map((p) => p.inhibitoryControl)),
      workingMemoryScore: this.calculateAverage(patterns.map((p) => p.workingMemory)),
      cognitiveFlexibilityScore: this.calculateAverage(patterns.map((p) => p.cognitiveFlexibility)),
      planningAbilityScore: this.calculateAverage(patterns.map((p) => p.planningAbility)),
      errorMonitoringScore: this.calculateAverage(patterns.map((p) => p.errorMonitoring)),
      overallExecutiveFunctionScore: this.calculateOverallExecutiveScore(patterns)
    };
  }
  analyzeVisualSpatialData() {
    const patterns = this.cognitiveData.visualSpatial;
    if (patterns.length === 0) return { noData: true };
    return {
      spatialOrientationScore: this.calculateAverage(patterns.map((p) => p.spatialOrientation)),
      visualPerceptionScore: this.calculateAverage(patterns.map((p) => p.visualPerception)),
      visualMemoryScore: this.calculateAverage(patterns.map((p) => p.visualMemory)),
      visualMotorIntegrationScore: this.calculateAverage(patterns.map((p) => p.visualMotorIntegration)),
      figureGroundDiscriminationScore: this.calculateAverage(patterns.map((p) => p.figureGroundDiscrimination)),
      overallVisualSpatialScore: this.calculateOverallVisualSpatialScore(patterns)
    };
  }
  analyzeAuditoryProcessing() {
    const patterns = this.cognitiveData.auditoryProcessing;
    if (patterns.length === 0) return { noData: true };
    return {
      phoneticDiscriminationScore: this.calculateAverage(patterns.map((p) => p.phoneticDiscrimination)),
      auditoryMemoryScore: this.calculateAverage(patterns.map((p) => p.auditoryMemory)),
      auditorySequencingScore: this.calculateAverage(patterns.map((p) => p.auditorySequencing)),
      soundSymbolAssociationScore: this.calculateAverage(patterns.map((p) => p.soundSymbolAssociation)),
      auditoryProcessingSpeedScore: this.calculateAverage(patterns.map((p) => p.auditoryProcessingSpeed)),
      overallAuditoryProcessingScore: this.calculateOverallAuditoryScore(patterns)
    };
  }
  // Métodos auxiliares de análise
  calculateTrend(values) {
    if (values.length < 2) return "stable";
    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));
    const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
    const difference = secondAvg - firstAvg;
    if (difference > 0.1) return "improving";
    if (difference < -0.1) return "declining";
    return "stable";
  }
  calculateAttentionStability(patterns) {
    const scores = patterns.map((p) => p.attentionScore);
    const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
    return Math.max(0, 1 - Math.sqrt(variance));
  }
  calculateSustainedAttention(patterns) {
    const timeWindows = this.createTimeWindows(patterns, 6e4);
    const windowScores = timeWindows.map(
      (window2) => window2.reduce((sum, p) => sum + p.attentionScore, 0) / window2.length
    );
    return this.calculateTrend(windowScores) === "stable" ? 1 : 0.5;
  }
  getFrequentMemoryStrategies(patterns) {
    const strategies = {};
    patterns.forEach((p) => {
      p.memoryStrategy.forEach((strategy) => {
        strategies[strategy] = (strategies[strategy] || 0) + 1;
      });
    });
    return Object.entries(strategies).sort(([, a], [, b]) => b - a).slice(0, 3).map(([strategy, count]) => ({ strategy, frequency: count / patterns.length }));
  }
  calculateMemoryEfficiency(patterns) {
    const efficiencies = patterns.map((p) => p.recallAccuracy / Math.max(1, p.memoryLoad));
    return efficiencies.reduce((sum, eff) => sum + eff, 0) / efficiencies.length;
  }
  calculateMemorySpan(patterns) {
    const successfulSpans = patterns.filter((p) => p.recallAccuracy > 0.8).map((p) => p.sequenceLength);
    return successfulSpans.length > 0 ? Math.max(...successfulSpans) : 0;
  }
  calculateMemoryLoadTolerance(patterns) {
    const highLoadTasks = patterns.filter((p) => p.memoryLoad > 0.7);
    const successRate = highLoadTasks.filter((p) => p.recallAccuracy > 0.5).length / Math.max(1, highLoadTasks.length);
    return successRate;
  }
  getSpeedDistribution(patterns) {
    const distribution = { very_fast: 0, fast: 0, average: 0, slow: 0, very_slow: 0 };
    patterns.forEach((p) => {
      distribution[p.speedCategory]++;
    });
    const total = patterns.length;
    Object.keys(distribution).forEach((key) => {
      distribution[key] = distribution[key] / total;
    });
    return distribution;
  }
  calculateCognitiveLoadHandling(patterns) {
    const highLoadTasks = patterns.filter((p) => p.cognitiveLoad > 0.7);
    const performanceUnderLoad = highLoadTasks.reduce((sum, p) => sum + p.efficiency, 0) / Math.max(1, highLoadTasks.length);
    return performanceUnderLoad;
  }
  calculateProcessingSpeedConsistency(patterns) {
    const times = patterns.map((p) => p.processingTime);
    const mean = times.reduce((sum, time) => sum + time, 0) / times.length;
    const variance = times.reduce((sum, time) => sum + Math.pow(time - mean, 2), 0) / times.length;
    const cv = Math.sqrt(variance) / mean;
    return Math.max(0, 1 - cv);
  }
  calculateOverallExecutiveScore(patterns) {
    const scores = patterns.map((p) => [
      p.inhibitoryControl,
      p.workingMemory,
      p.cognitiveFlexibility,
      p.planningAbility,
      p.errorMonitoring
    ]);
    const averageScores = scores.reduce((acc, scoreSet) => {
      scoreSet.forEach((score, index) => {
        acc[index] = (acc[index] || 0) + score;
      });
      return acc;
    }, []).map((sum) => sum / patterns.length);
    return averageScores.reduce((sum, score) => sum + score, 0) / averageScores.length;
  }
  calculateOverallVisualSpatialScore(patterns) {
    const scores = patterns.map((p) => [
      p.spatialOrientation,
      p.visualPerception,
      p.visualMemory,
      p.visualMotorIntegration,
      p.figureGroundDiscrimination
    ]);
    const averageScores = scores.reduce((acc, scoreSet) => {
      scoreSet.forEach((score, index) => {
        acc[index] = (acc[index] || 0) + score;
      });
      return acc;
    }, []).map((sum) => sum / patterns.length);
    return averageScores.reduce((sum, score) => sum + score, 0) / averageScores.length;
  }
  calculateOverallAuditoryScore(patterns) {
    const scores = patterns.map((p) => [
      p.phoneticDiscrimination,
      p.auditoryMemory,
      p.auditorySequencing,
      p.soundSymbolAssociation,
      p.auditoryProcessingSpeed
    ]);
    const averageScores = scores.reduce((acc, scoreSet) => {
      scoreSet.forEach((score, index) => {
        acc[index] = (acc[index] || 0) + score;
      });
      return acc;
    }, []).map((sum) => sum / patterns.length);
    return averageScores.reduce((sum, score) => sum + score, 0) / averageScores.length;
  }
  createTimeWindows(patterns, windowSize) {
    const windows = [];
    const startTime = new Date(patterns[0].timestamp).getTime();
    patterns.forEach((pattern) => {
      const patternTime = new Date(pattern.timestamp).getTime();
      const windowIndex = Math.floor((patternTime - startTime) / windowSize);
      if (!windows[windowIndex]) windows[windowIndex] = [];
      windows[windowIndex].push(pattern);
    });
    return windows.filter((window2) => window2 && window2.length > 0);
  }
  getBasicCognitiveAnalysis() {
    return {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      status: "basic_analysis",
      attentionAnalysis: this.analyzeAttentionPatterns(),
      memoryAnalysis: this.analyzeMemoryPatterns(),
      processingAnalysis: this.analyzeProcessingSpeed(),
      message: "Análise cognitiva básica - CognitiveAssociationEngine não disponível"
    };
  }
  /**
   * Gera relatório cognitivo completo
   */
  generateCognitiveReport() {
    return {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      sessionDuration: Date.now() - this.sessionStartTime,
      dataCollected: {
        attentionPatterns: this.cognitiveData.attentionPatterns.length,
        memoryPatterns: this.cognitiveData.memoryPatterns.length,
        processingSpeedData: this.cognitiveData.processingSpeed.length,
        executiveFunctionData: this.cognitiveData.executiveFunction.length,
        visualSpatialData: this.cognitiveData.visualSpatial.length,
        auditoryProcessingData: this.cognitiveData.auditoryProcessing.length
      },
      analysis: {
        attention: this.analyzeAttentionPatterns(),
        memory: this.analyzeMemoryPatterns(),
        processingSpeed: this.analyzeProcessingSpeed(),
        executiveFunction: this.analyzeExecutiveFunction(),
        visualSpatial: this.analyzeVisualSpatialData(),
        auditoryProcessing: this.analyzeAuditoryProcessing()
      },
      recommendations: this.generateCognitiveRecommendations()
    };
  }
  generateCognitiveRecommendations() {
    const recommendations = [];
    const attention = this.analyzeAttentionPatterns();
    if (attention.averageAttentionScore < 0.6) {
      recommendations.push({
        domain: "attention",
        priority: "high",
        recommendation: "Exercícios específicos para melhorar atenção sustentada"
      });
    }
    const memory = this.analyzeMemoryPatterns();
    if (memory.averageWorkingMemoryCapacity < 0.5) {
      recommendations.push({
        domain: "memory",
        priority: "high",
        recommendation: "Atividades para fortalecer memória de trabalho"
      });
    }
    const processing = this.analyzeProcessingSpeed();
    if (processing.averageProcessingEfficiency < 0.5) {
      recommendations.push({
        domain: "processing_speed",
        priority: "medium",
        recommendation: "Exercícios para aumentar velocidade de processamento"
      });
    }
    return recommendations;
  }
  /**
   * Retorna dados coletados
   */
  getData() {
    return this.cognitiveData;
  }
  /**
   * Reseta dados coletados
   */
  reset() {
    this.cognitiveData = {
      attentionPatterns: [],
      memoryPatterns: [],
      processingSpeed: [],
      executiveFunction: [],
      visualSpatial: [],
      auditoryProcessing: []
    };
    this.sessionStartTime = Date.now();
  }
  /**
   * Método auxiliar para cálculo de média com verificação de array vazio
   */
  calculateAverage(array) {
    if (!array || array.length === 0) return 0;
    return array.reduce((sum, value) => sum + value, 0) / array.length;
  }
  /**
   * Gera perfil cognitivo completo do usuário
   */
  getCognitiveProfile() {
    const attentionAnalysis = this.analyzeAttentionPatterns();
    const memoryAnalysis = this.analyzeMemoryPatterns();
    const processingAnalysis = this.analyzeProcessingSpeed();
    const executiveAnalysis = this.analyzeExecutiveFunction();
    const visualSpatialAnalysis = this.analyzeVisualSpatialData();
    const auditoryAnalysis = this.analyzeAuditoryProcessing();
    return {
      overallScore: this.calculateOverallCognitiveScore(),
      cognitiveStrengths: this.identifyCognitiveStrengths(),
      cognitiveWeaknesses: this.identifyCognitiveWeaknesses(),
      cognitiveProfile: {
        attention: attentionAnalysis,
        memory: memoryAnalysis,
        processing: processingAnalysis,
        executive: executiveAnalysis,
        visualSpatial: visualSpatialAnalysis,
        auditory: auditoryAnalysis
      },
      developmentalLevel: this.assessDevelopmentalLevel(),
      interventionRecommendations: this.generateInterventionRecommendations(),
      neuropsychologicalProfile: this.generateNeuropsychologicalProfile()
    };
  }
  /**
   * Calcula pontuação cognitiva geral
   */
  calculateOverallCognitiveScore() {
    const attentionScore = this.cognitiveData.attentionPatterns.length > 0 ? this.calculateAverage(this.cognitiveData.attentionPatterns.map((p) => p.attentionScore)) : 0.5;
    const memoryScore = this.cognitiveData.memoryPatterns.length > 0 ? this.calculateAverage(this.cognitiveData.memoryPatterns.map((p) => p.workingMemoryCapacity)) : 0.5;
    const processingScore = this.cognitiveData.processingSpeed.length > 0 ? this.calculateAverage(this.cognitiveData.processingSpeed.map((p) => p.efficiency)) : 0.5;
    const executiveScore = this.cognitiveData.executiveFunction.length > 0 ? this.calculateAverage(this.cognitiveData.executiveFunction.map((p) => p.inhibitoryControl)) : 0.5;
    const visualScore = this.cognitiveData.visualSpatial.length > 0 ? this.calculateAverage(this.cognitiveData.visualSpatial.map((p) => p.spatialOrientation)) : 0.5;
    const auditoryScore = this.cognitiveData.auditoryProcessing.length > 0 ? this.calculateAverage(this.cognitiveData.auditoryProcessing.map((p) => p.phoneticDiscrimination)) : 0.5;
    return (attentionScore + memoryScore + processingScore + executiveScore + visualScore + auditoryScore) / 6;
  }
  /**
   * Identifica pontos fortes cognitivos
   */
  identifyCognitiveStrengths() {
    const scores = {
      attention: this.cognitiveData.attentionPatterns.length > 0 ? this.calculateAverage(this.cognitiveData.attentionPatterns.map((p) => p.attentionScore)) : 0.5,
      memory: this.cognitiveData.memoryPatterns.length > 0 ? this.calculateAverage(this.cognitiveData.memoryPatterns.map((p) => p.workingMemoryCapacity)) : 0.5,
      processing: this.cognitiveData.processingSpeed.length > 0 ? this.calculateAverage(this.cognitiveData.processingSpeed.map((p) => p.efficiency)) : 0.5,
      executive: this.cognitiveData.executiveFunction.length > 0 ? this.calculateAverage(this.cognitiveData.executiveFunction.map((p) => p.inhibitoryControl)) : 0.5,
      visualSpatial: this.cognitiveData.visualSpatial.length > 0 ? this.calculateAverage(this.cognitiveData.visualSpatial.map((p) => p.spatialOrientation)) : 0.5,
      auditory: this.cognitiveData.auditoryProcessing.length > 0 ? this.calculateAverage(this.cognitiveData.auditoryProcessing.map((p) => p.phoneticDiscrimination)) : 0.5
    };
    return Object.entries(scores).filter(([_, score]) => score >= 0.7).map(([domain, score]) => ({ domain, score })).sort((a, b) => b.score - a.score);
  }
  /**
   * Identifica pontos fracos cognitivos
   */
  identifyCognitiveWeaknesses() {
    const scores = {
      attention: this.cognitiveData.attentionPatterns.length > 0 ? this.calculateAverage(this.cognitiveData.attentionPatterns.map((p) => p.attentionScore)) : 0.5,
      memory: this.cognitiveData.memoryPatterns.length > 0 ? this.calculateAverage(this.cognitiveData.memoryPatterns.map((p) => p.workingMemoryCapacity)) : 0.5,
      processing: this.cognitiveData.processingSpeed.length > 0 ? this.calculateAverage(this.cognitiveData.processingSpeed.map((p) => p.efficiency)) : 0.5,
      executive: this.cognitiveData.executiveFunction.length > 0 ? this.calculateAverage(this.cognitiveData.executiveFunction.map((p) => p.inhibitoryControl)) : 0.5,
      visualSpatial: this.cognitiveData.visualSpatial.length > 0 ? this.calculateAverage(this.cognitiveData.visualSpatial.map((p) => p.spatialOrientation)) : 0.5,
      auditory: this.cognitiveData.auditoryProcessing.length > 0 ? this.calculateAverage(this.cognitiveData.auditoryProcessing.map((p) => p.phoneticDiscrimination)) : 0.5
    };
    return Object.entries(scores).filter(([_, score]) => score < 0.5).map(([domain, score]) => ({ domain, score })).sort((a, b) => a.score - b.score);
  }
  /**
   * Avalia nível de desenvolvimento
   */
  assessDevelopmentalLevel() {
    const overallScore = this.calculateOverallCognitiveScore();
    if (overallScore >= 0.8) return "advanced";
    if (overallScore >= 0.6) return "typical";
    if (overallScore >= 0.4) return "developing";
    return "needs_support";
  }
  /**
   * Gera recomendações de intervenção
   */
  generateInterventionRecommendations() {
    const weaknesses = this.identifyCognitiveWeaknesses();
    const recommendations = [];
    weaknesses.forEach((weakness) => {
      switch (weakness.domain) {
        case "attention":
          recommendations.push({
            domain: "attention",
            priority: "high",
            intervention: "Exercícios de atenção sustentada e seletiva",
            activities: ["Jogos de concentração", "Tarefas de busca visual", "Exercícios de mindfulness"]
          });
          break;
        case "memory":
          recommendations.push({
            domain: "memory",
            priority: "high",
            intervention: "Treinamento de memória de trabalho",
            activities: ["Exercícios de span de dígitos", "Jogos de memória sequencial", "Treino de estratégias de codificação"]
          });
          break;
        case "processing":
          recommendations.push({
            domain: "processing",
            priority: "medium",
            intervention: "Exercícios de velocidade de processamento",
            activities: ["Tarefas cronometradas", "Jogos de velocidade", "Exercícios de automatização"]
          });
          break;
        case "executive":
          recommendations.push({
            domain: "executive",
            priority: "high",
            intervention: "Treinamento de funções executivas",
            activities: ["Jogos de estratégia", "Exercícios de controle inibitório", "Tarefas de flexibilidade cognitiva"]
          });
          break;
        case "visualSpatial":
          recommendations.push({
            domain: "visualSpatial",
            priority: "medium",
            intervention: "Exercícios visuo-espaciais",
            activities: ["Jogos de orientação espacial", "Exercícios de rotação mental", "Tarefas de construção"]
          });
          break;
        case "auditory":
          recommendations.push({
            domain: "auditory",
            priority: "medium",
            intervention: "Treinamento auditivo",
            activities: ["Exercícios de discriminação fonética", "Jogos de processamento auditivo", "Tarefas de memória auditiva"]
          });
          break;
      }
    });
    return recommendations;
  }
  /**
   * Gera perfil neuropsicológico
   */
  generateNeuropsychologicalProfile() {
    return {
      dominantProcessingStyle: this.identifyDominantProcessingStyle(),
      cognitiveProfile: this.getCognitiveProfileClassification(),
      neurodevelopmentalIndicators: this.identifyNeurodevelopmentalIndicators(),
      learningStylePreferences: this.identifyLearningStylePreferences(),
      adaptiveStrategies: this.identifyAdaptiveStrategies()
    };
  }
  /**
   * Identifica estilo de processamento dominante
   */
  identifyDominantProcessingStyle() {
    const visualScore = this.cognitiveData.visualSpatial.length > 0 ? this.calculateAverage(this.cognitiveData.visualSpatial.map((p) => p.visualPerception)) : 0.5;
    const auditoryScore = this.cognitiveData.auditoryProcessing.length > 0 ? this.calculateAverage(this.cognitiveData.auditoryProcessing.map((p) => p.phoneticDiscrimination)) : 0.5;
    if (visualScore > auditoryScore + 0.1) return "visual";
    if (auditoryScore > visualScore + 0.1) return "auditory";
    return "balanced";
  }
  /**
   * Classifica perfil cognitivo
   */
  getCognitiveProfileClassification() {
    const strengths = this.identifyCognitiveStrengths();
    const weaknesses = this.identifyCognitiveWeaknesses();
    if (strengths.length > 3 && weaknesses.length === 0) return "superior";
    if (strengths.length > 2 && weaknesses.length <= 1) return "above_average";
    if (strengths.length === weaknesses.length) return "average";
    if (weaknesses.length > 2) return "below_average";
    return "variable";
  }
  /**
   * Identifica indicadores neurodesenvolvimentais
   */
  identifyNeurodevelopmentalIndicators() {
    const indicators = [];
    const attentionScore = this.cognitiveData.attentionPatterns.length > 0 ? this.calculateAverage(this.cognitiveData.attentionPatterns.map((p) => p.attentionScore)) : 0.5;
    if (attentionScore < 0.4) {
      indicators.push({
        type: "attention_deficit",
        confidence: 1 - attentionScore,
        description: "Possível déficit de atenção"
      });
    }
    const phonologicalScore = this.cognitiveData.auditoryProcessing.length > 0 ? this.calculateAverage(this.cognitiveData.auditoryProcessing.map((p) => p.phoneticDiscrimination)) : 0.5;
    if (phonologicalScore < 0.4) {
      indicators.push({
        type: "phonological_deficit",
        confidence: 1 - phonologicalScore,
        description: "Possível déficit fonológico"
      });
    }
    const executiveScore = this.cognitiveData.executiveFunction.length > 0 ? this.calculateAverage(this.cognitiveData.executiveFunction.map((p) => p.inhibitoryControl)) : 0.5;
    if (executiveScore < 0.4) {
      indicators.push({
        type: "executive_dysfunction",
        confidence: 1 - executiveScore,
        description: "Possível disfunção executiva"
      });
    }
    return indicators;
  }
  /**
   * Identifica preferências de estilo de aprendizagem
   */
  identifyLearningStylePreferences() {
    const visualScore = this.cognitiveData.visualSpatial.length > 0 ? this.calculateAverage(this.cognitiveData.visualSpatial.map((p) => p.visualPerception)) : 0.5;
    const auditoryScore = this.cognitiveData.auditoryProcessing.length > 0 ? this.calculateAverage(this.cognitiveData.auditoryProcessing.map((p) => p.phoneticDiscrimination)) : 0.5;
    const processingScore = this.cognitiveData.processingSpeed.length > 0 ? this.calculateAverage(this.cognitiveData.processingSpeed.map((p) => p.efficiency)) : 0.5;
    return {
      visualLearning: visualScore,
      auditoryLearning: auditoryScore,
      kinestheticLearning: processingScore,
      preferredModality: this.identifyDominantProcessingStyle(),
      multimodalSupport: Math.min(visualScore, auditoryScore, processingScore) > 0.6
    };
  }
  /**
   * Identifica estratégias adaptativas
   */
  identifyAdaptiveStrategies() {
    const strategies = [];
    const weaknesses = this.identifyCognitiveWeaknesses();
    const strengths = this.identifyCognitiveStrengths();
    strengths.forEach((strength) => {
      switch (strength.domain) {
        case "visualSpatial":
          strategies.push({
            type: "compensatory",
            strategy: "Usar apoios visuais e organizadores gráficos",
            domain: "visual_support"
          });
          break;
        case "auditory":
          strategies.push({
            type: "compensatory",
            strategy: "Usar instruções verbais e apoios auditivos",
            domain: "auditory_support"
          });
          break;
        case "memory":
          strategies.push({
            type: "compensatory",
            strategy: "Usar estratégias de memória e organização",
            domain: "memory_support"
          });
          break;
      }
    });
    weaknesses.forEach((weakness) => {
      strategies.push({
        type: "remedial",
        strategy: `Treinamento específico para ${weakness.domain}`,
        domain: weakness.domain,
        intensity: weakness.score < 0.3 ? "high" : "medium"
      });
    });
    return strategies;
  }
  /**
   * Avalia qualidade dos dados coletados
   */
  assessDataQuality() {
    const totalDataPoints = this.cognitiveData.attentionPatterns.length + this.cognitiveData.memoryPatterns.length + this.cognitiveData.processingSpeed.length + this.cognitiveData.executiveFunction.length + this.cognitiveData.visualSpatial.length + this.cognitiveData.auditoryProcessing.length;
    if (totalDataPoints === 0) {
      return {
        quality: "insufficient",
        score: 0,
        reliability: "low",
        completeness: 0,
        consistency: 0,
        recommendations: ["Coletar mais dados para análise confiável"]
      };
    }
    const completeness = Math.min(1, totalDataPoints / 30);
    const consistency = this.calculateDataConsistency();
    const reliability = this.calculateDataReliability();
    const score = (completeness + consistency + reliability) / 3;
    let quality = "low";
    if (score >= 0.8) quality = "high";
    else if (score >= 0.6) quality = "medium";
    else if (score >= 0.4) quality = "fair";
    return {
      quality,
      score,
      reliability: reliability > 0.7 ? "high" : reliability > 0.5 ? "medium" : "low",
      completeness,
      consistency,
      recommendations: this.generateDataQualityRecommendations(score)
    };
  }
  /**
   * Calcula consistência dos dados
   */
  calculateDataConsistency() {
    if (this.cognitiveData.attentionPatterns.length < 2) return 0.5;
    const recentScores = this.cognitiveData.attentionPatterns.slice(-5).map((p) => p.attentionScore);
    if (recentScores.length === 0) return 0.5;
    const mean = this.calculateAverage(recentScores);
    const variance = this.calculateAverage(recentScores.map((score) => Math.pow(score - mean, 2)));
    return Math.max(0, 1 - Math.sqrt(variance));
  }
  /**
   * Calcula confiabilidade dos dados
   */
  calculateDataReliability() {
    const sessionDuration = Date.now() - this.sessionStartTime;
    const minDuration = 5 * 60 * 1e3;
    if (sessionDuration < minDuration) return 0.3;
    const dataPoints = this.cognitiveData.attentionPatterns.length + this.cognitiveData.memoryPatterns.length + this.cognitiveData.processingSpeed.length;
    const dataRate = dataPoints / (sessionDuration / 6e4);
    const idealRate = 2;
    return Math.min(1, dataRate / idealRate);
  }
  /**
   * Gera recomendações para melhorar qualidade dos dados
   */
  generateDataQualityRecommendations(score) {
    const recommendations = [];
    if (score < 0.4) {
      recommendations.push("Aumentar tempo de sessão para coleta mais robusta");
      recommendations.push("Diversificar tipos de tarefas cognitivas");
    }
    if (score < 0.6) {
      recommendations.push("Melhorar consistência nas respostas");
      recommendations.push("Reduzir fatores de distração durante a coleta");
    }
    if (score < 0.8) {
      recommendations.push("Continuar coleta para aumentar confiabilidade");
    }
    return recommendations;
  }
}
class ErrorPatternCollector {
  constructor() {
    this.name = "ErrorPatternCollector";
    this.description = "Coleta padrões de erros no LetterRecognition";
    this.version = "1.0.0";
    this.isActive = true;
    this.collectedData = [];
    this.errorData = {
      letterConfusions: {},
      visualErrors: [],
      auditoryErrors: [],
      sequenceErrors: [],
      persistentErrors: {},
      errorClusters: [],
      learningIndicators: []
    };
    this.sessionStartTime = Date.now();
    console.log("⚠️ ErrorPattern Collector (LetterRecognition) inicializado");
  }
  /**
   * Coleta erros de confusão entre letras
   */
  collectLetterConfusion(targetLetter, selectedLetter, context) {
    const confusionKey = `${targetLetter}->${selectedLetter}`;
    const confusionData = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      targetLetter,
      selectedLetter,
      confusionType: this.identifyConfusionType(targetLetter, selectedLetter),
      context: {
        difficulty: context.difficulty,
        position: context.position,
        distractors: context.distractors,
        responseTime: context.responseTime,
        attempts: context.attempts
      },
      severity: this.calculateErrorSeverity(targetLetter, selectedLetter, context),
      frequency: this.updateConfusionFrequency(confusionKey)
    };
    if (!this.errorData.letterConfusions[confusionKey]) {
      this.errorData.letterConfusions[confusionKey] = [];
    }
    this.errorData.letterConfusions[confusionKey].push(confusionData);
    this.detectPersistentError(confusionKey, confusionData);
    return confusionData;
  }
  /**
   * Coleta erros visuais específicos
   */
  collectVisualError(errorType, details) {
    const visualError = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      errorType,
      details: {
        orientation: details.orientation || "normal",
        mirrorConfusion: details.mirrorConfusion || false,
        rotationError: details.rotationError || false,
        visualSimilarity: details.visualSimilarity || 0,
        viewingTime: details.viewingTime || 0,
        visualClutter: details.visualClutter || "low"
      },
      impact: this.assessVisualErrorImpact(errorType, details),
      learningImplication: this.analyzeVisualLearningImplication(errorType, details)
    };
    this.errorData.visualErrors.push(visualError);
    this.updateErrorClusters("visual", visualError);
    return visualError;
  }
  /**
   * Coleta erros auditivos
   */
  collectAuditoryError(errorType, details) {
    const auditoryError = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      errorType,
      details: {
        phoneticSimilarity: details.phoneticSimilarity || 0,
        soundDiscrimination: details.soundDiscrimination || "normal",
        auditoryMemory: details.auditoryMemory || "intact",
        backgroundNoise: details.backgroundNoise || "none",
        processingSpeed: details.processingSpeed || "normal"
      },
      impact: this.assessAuditoryErrorImpact(errorType, details),
      learningImplication: this.analyzeAuditoryLearningImplication(errorType, details)
    };
    this.errorData.auditoryErrors.push(auditoryError);
    this.updateErrorClusters("auditory", auditoryError);
    return auditoryError;
  }
  /**
   * Coleta erros de sequenciamento
   */
  collectSequenceError(expectedSequence, actualSequence, context) {
    const sequenceError = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      expectedSequence,
      actualSequence,
      sequenceLength: expectedSequence.length,
      errorPosition: this.findErrorPosition(expectedSequence, actualSequence),
      errorType: this.classifySequenceError(expectedSequence, actualSequence),
      context: {
        memoryLoad: context.memoryLoad || 0,
        timeDelay: context.timeDelay || 0,
        interference: context.interference || "none",
        modality: context.modality || "visual"
      },
      severity: this.calculateSequenceErrorSeverity(expectedSequence, actualSequence),
      workingMemoryImplication: this.analyzeWorkingMemoryImplication(expectedSequence, actualSequence, context)
    };
    this.errorData.sequenceErrors.push(sequenceError);
    this.updateErrorClusters("sequence", sequenceError);
    return sequenceError;
  }
  /**
   * Detecta indicadores de aprendizagem através dos erros
   */
  collectLearningIndicator(interaction) {
    const learningIndicator = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      errorReduction: this.calculateErrorReduction(interaction),
      adaptationSpeed: this.calculateAdaptationSpeed(interaction),
      transferLearning: this.assessTransferLearning(interaction),
      metacognition: this.assessMetacognition(interaction),
      persistenceLevel: this.assessPersistence(interaction),
      strategyEvolution: this.analyzeStrategyEvolution(interaction)
    };
    this.errorData.learningIndicators.push(learningIndicator);
    return learningIndicator;
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de erros
   */
  collect(gameData) {
    if (!gameData) {
      console.warn("LetterRecognitionErrorPatternCollector: Dados do jogo não fornecidos para análise");
      return { errors: [], patterns: [], metrics: {} };
    }
    console.log(`📊 LetterRecognitionErrorPatternCollector: Analisando dados da sessão ${gameData.sessionId || "sem ID"}`);
    try {
      const errors = [];
      if (gameData.attempts && Array.isArray(gameData.attempts)) {
        gameData.attempts.forEach((attempt) => {
          if (!attempt.isCorrect) {
            errors.push({
              type: "letter_confusion",
              timestamp: Date.now(),
              targetLetter: attempt.targetLetter,
              selectedLetter: attempt.selectedLetter,
              severity: 0.7,
              context: {
                position: attempt.position,
                responseTime: attempt.responseTime
              }
            });
          }
        });
      }
      if (gameData.visualErrors && Array.isArray(gameData.visualErrors)) {
        gameData.visualErrors.forEach((error) => {
          errors.push({
            type: "visual_error",
            timestamp: Date.now(),
            errorType: error.type,
            severity: 0.6,
            details: {
              visualSimilarity: error.visualSimilarity || 0.5
            }
          });
        });
      }
      if (gameData.sequences && Array.isArray(gameData.sequences)) {
        gameData.sequences.forEach((seq) => {
          if (seq.expected !== seq.actual) {
            errors.push({
              type: "sequence_error",
              timestamp: Date.now(),
              expectedSequence: seq.expected,
              actualSequence: seq.actual,
              severity: 0.5
            });
          }
        });
      }
      this.collectedData.push({
        timestamp: Date.now(),
        sessionId: gameData.sessionId,
        errors,
        metrics: {
          totalErrors: errors.length,
          errorRate: gameData.attempts ? errors.length / gameData.attempts.length : 0
        }
      });
      return {
        errors,
        patterns: {
          visualAuditoryRatio: 0.5,
          sequenceComplexityIssues: false,
          learningCurve: "stable"
        },
        metrics: {
          totalErrors: errors.length,
          errorRate: gameData.attempts ? errors.length / gameData.attempts.length : 0,
          averageSeverity: 0.65,
          responseTimeAvg: gameData.attempts ? gameData.attempts.reduce((sum, a) => sum + (a.responseTime || 0), 0) / gameData.attempts.length : 0,
          lettersLearned: gameData.lettersLearned || 0,
          progressRate: gameData.progressRate || 0
        }
      };
    } catch (error) {
      console.error(`❌ LetterRecognitionErrorPatternCollector: Erro na análise`, error);
      return { errors: [], patterns: [], metrics: {}, error: error.message };
    }
  }
  // Métodos de identificação e classificação
  identifyConfusionType(target, selected) {
    const visualSimilarities = {
      "mirror": [["B", "D"], ["P", "Q"], ["U", "N"]],
      "rotation": [["M", "W"], ["6", "9"]],
      "shape": [["C", "O"], ["I", "L"], ["V", "Y"]]
    };
    const phoneticSimilarities = {
      "similar_sound": [["B", "P"], ["D", "T"], ["G", "C"], ["F", "V"]],
      "vowel_confusion": [["A", "E"], ["E", "I"], ["O", "U"]],
      "consonant_blend": [["B", "BR"], ["C", "CR"], ["F", "FR"]]
    };
    for (const [type, groups] of Object.entries(visualSimilarities)) {
      for (const group of groups) {
        if (group.includes(target) && group.includes(selected)) {
          return `visual_${type}`;
        }
      }
    }
    for (const [type, groups] of Object.entries(phoneticSimilarities)) {
      for (const group of groups) {
        if (group.includes(target) && group.includes(selected)) {
          return `phonetic_${type}`;
        }
      }
    }
    return "random_error";
  }
  calculateErrorSeverity(target, selected, context) {
    let severity = 0;
    const confusionKey = `${target}->${selected}`;
    const frequency = this.getConfusionFrequency(confusionKey);
    severity += Math.min(1, frequency / 5) * 0.3;
    const similarity = this.calculateLetterSimilarity(target, selected);
    severity += (1 - similarity) * 0.2;
    const responseTime = context.responseTime || 0;
    const timeScore = responseTime < 500 ? 0.3 : responseTime > 5e3 ? 0.2 : 0;
    severity += timeScore * 0.2;
    const attemptScore = Math.min(1, (context.attempts || 1 - 1) / 3) * 0.3;
    severity += attemptScore;
    return Math.min(1, severity);
  }
  updateConfusionFrequency(confusionKey) {
    if (!this.errorData.persistentErrors[confusionKey]) {
      this.errorData.persistentErrors[confusionKey] = 0;
    }
    this.errorData.persistentErrors[confusionKey]++;
    return this.errorData.persistentErrors[confusionKey];
  }
  detectPersistentError(confusionKey, confusionData) {
    const frequency = this.errorData.persistentErrors[confusionKey];
    if (frequency >= 3) {
      this.flagPersistentError(confusionKey, confusionData, frequency);
    }
  }
  flagPersistentError(confusionKey, confusionData, frequency) {
    const persistentErrorFlag = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      confusionPattern: confusionKey,
      frequency,
      severity: confusionData.severity,
      confusionType: confusionData.confusionType,
      interventionRecommended: this.recommendIntervention(confusionKey, confusionData),
      priority: frequency > 5 ? "high" : "medium"
    };
    console.warn("🚨 Erro Persistente Detectado:", persistentErrorFlag);
    return persistentErrorFlag;
  }
  assessVisualErrorImpact(errorType, details) {
    const impacts = {
      reading_readiness: 0,
      letter_recognition: 0,
      visual_processing: 0,
      spatial_orientation: 0
    };
    switch (errorType) {
      case "mirror_confusion":
        impacts.reading_readiness = 0.8;
        impacts.spatial_orientation = 0.9;
        break;
      case "rotation_error":
        impacts.visual_processing = 0.7;
        impacts.spatial_orientation = 0.8;
        break;
      case "shape_similarity":
        impacts.letter_recognition = 0.6;
        impacts.visual_processing = 0.5;
        break;
    }
    return impacts;
  }
  analyzeVisualLearningImplication(errorType, details) {
    const implications = [];
    if (errorType === "mirror_confusion") {
      implications.push({
        area: "spatial_orientation",
        severity: "high",
        recommendation: "Exercícios de orientação espacial e diferenciação direcional"
      });
    }
    if (details.viewingTime > 5e3) {
      implications.push({
        area: "processing_speed",
        severity: "medium",
        recommendation: "Atividades para acelerar reconhecimento visual"
      });
    }
    if (details.visualClutter === "high") {
      implications.push({
        area: "attention",
        severity: "medium",
        recommendation: "Treino de atenção seletiva em ambientes visuais complexos"
      });
    }
    return implications;
  }
  assessAuditoryErrorImpact(errorType, details) {
    const impacts = {
      phonetic_awareness: 0,
      sound_discrimination: 0,
      auditory_memory: 0,
      language_processing: 0
    };
    switch (errorType) {
      case "phonetic_confusion":
        impacts.phonetic_awareness = 0.8;
        impacts.sound_discrimination = 0.7;
        break;
      case "auditory_memory_failure":
        impacts.auditory_memory = 0.9;
        impacts.language_processing = 0.6;
        break;
      case "processing_speed_delay":
        impacts.language_processing = 0.7;
        break;
    }
    return impacts;
  }
  analyzeAuditoryLearningImplication(errorType, details) {
    const implications = [];
    if (errorType === "phonetic_confusion") {
      implications.push({
        area: "phonetic_awareness",
        severity: "high",
        recommendation: "Exercícios intensivos de discriminação fonética"
      });
    }
    if (details.auditoryMemory === "impaired") {
      implications.push({
        area: "auditory_memory",
        severity: "high",
        recommendation: "Atividades para fortalecer memória auditiva sequencial"
      });
    }
    if (details.processingSpeed === "slow") {
      implications.push({
        area: "auditory_processing",
        severity: "medium",
        recommendation: "Exercícios de velocidade de processamento auditivo"
      });
    }
    return implications;
  }
  findErrorPosition(expected, actual) {
    for (let i = 0; i < Math.max(expected.length, actual.length); i++) {
      if (expected[i] !== actual[i]) {
        return i;
      }
    }
    return -1;
  }
  classifySequenceError(expected, actual) {
    if (actual.length < expected.length) return "omission";
    if (actual.length > expected.length) return "insertion";
    const errorPos = this.findErrorPosition(expected, actual);
    if (errorPos === -1) return "none";
    if (errorPos < expected.length - 1 && expected[errorPos] === actual[errorPos + 1] && expected[errorPos + 1] === actual[errorPos]) {
      return "transposition";
    }
    return "substitution";
  }
  calculateSequenceErrorSeverity(expected, actual) {
    const lengthDifference = Math.abs(expected.length - actual.length);
    const substitutions = this.countSubstitutions(expected, actual);
    const totalErrors = lengthDifference + substitutions;
    return Math.min(1, totalErrors / expected.length);
  }
  analyzeWorkingMemoryImplication(expected, actual, context) {
    const memoryImplications = {
      capacity_exceeded: context.memoryLoad > 0.8,
      interference_effect: context.interference !== "none",
      decay_effect: context.timeDelay > 3e3,
      strategy_failure: this.detectStrategyFailure(expected, actual)
    };
    const severity = Object.values(memoryImplications).filter(Boolean).length / 4;
    return {
      implications: memoryImplications,
      severity,
      recommendations: this.generateMemoryRecommendations(memoryImplications)
    };
  }
  updateErrorClusters(errorType, errorData) {
    const timeWindow = 3e4;
    const currentTime = (/* @__PURE__ */ new Date()).getTime();
    let cluster = this.errorData.errorClusters.find(
      (c) => c.errorType === errorType && currentTime - new Date(c.lastUpdate).getTime() < timeWindow
    );
    if (!cluster) {
      cluster = {
        id: `cluster_${errorType}_${Date.now()}`,
        errorType,
        startTime: (/* @__PURE__ */ new Date()).toISOString(),
        lastUpdate: (/* @__PURE__ */ new Date()).toISOString(),
        errors: [],
        severity: 0,
        pattern: "emerging"
      };
      this.errorData.errorClusters.push(cluster);
    }
    cluster.errors.push(errorData);
    cluster.lastUpdate = (/* @__PURE__ */ new Date()).toISOString();
    cluster.severity = this.calculateClusterSeverity(cluster);
    cluster.pattern = this.identifyClusterPattern(cluster);
    return cluster;
  }
  calculateErrorReduction(interaction) {
    const recentErrors = this.getRecentErrors(3e5);
    const timeWindows = this.createTimeWindows(recentErrors, 6e4);
    if (timeWindows.length < 2) return 0;
    const errorRates = timeWindows.map((window2) => window2.length);
    const trend = this.calculateTrend(errorRates);
    return trend === "decreasing" ? 1 : trend === "stable" ? 0.5 : 0;
  }
  calculateAdaptationSpeed(interaction) {
    const learningCurve = this.generateLearningCurve();
    if (learningCurve.length < 3) return 0.5;
    const improvements = [];
    for (let i = 1; i < learningCurve.length; i++) {
      const improvement = learningCurve[i].accuracy - learningCurve[i - 1].accuracy;
      improvements.push(improvement);
    }
    const avgImprovement = improvements.reduce((sum, imp) => sum + imp, 0) / improvements.length;
    return Math.max(0, Math.min(1, avgImprovement * 10));
  }
  assessTransferLearning(interaction) {
    const simillarLetters = this.findSimilarLetters(interaction.targetLetter);
    const performanceOnSimilar = this.getPerformanceOnLetters(simillarLetters);
    return performanceOnSimilar.averageAccuracy || 0;
  }
  assessMetacognition(interaction) {
    const selfCorrections = this.countSelfCorrections();
    const strategyChanges = this.countStrategyChanges();
    const errorAwareness = this.assessErrorAwareness();
    return selfCorrections * 0.4 + strategyChanges * 0.3 + errorAwareness * 0.3;
  }
  assessPersistence(interaction) {
    const errorsFollowedByQuit = this.countQuitAfterError();
    const errorsFollowedByContinue = this.countContinueAfterError();
    const total = errorsFollowedByQuit + errorsFollowedByContinue;
    return total > 0 ? errorsFollowedByContinue / total : 1;
  }
  analyzeStrategyEvolution(interaction) {
    const strategies = this.identifyUsedStrategies();
    const strategyTimeline = this.createStrategyTimeline();
    return {
      currentStrategies: strategies,
      evolution: strategyTimeline,
      complexity: this.calculateStrategyComplexity(strategies),
      effectiveness: this.calculateStrategyEffectiveness(strategies)
    };
  }
  // Métodos auxiliares
  calculateLetterSimilarity(letter1, letter2) {
    const visualSimilarity = this.calculateVisualSimilarity(letter1, letter2);
    const phoneticSimilarity = this.calculatePhoneticSimilarity(letter1, letter2);
    return Math.max(visualSimilarity, phoneticSimilarity);
  }
  calculateVisualSimilarity(letter1, letter2) {
    const similarGroups = [
      ["B", "D"],
      ["P", "Q"],
      ["U", "N"],
      ["M", "W"],
      ["C", "O"],
      ["I", "L"],
      ["V", "Y"]
    ];
    for (const group of similarGroups) {
      if (group.includes(letter1) && group.includes(letter2)) {
        return 0.8;
      }
    }
    return 0.1;
  }
  calculatePhoneticSimilarity(letter1, letter2) {
    const similarSounds = [
      ["B", "P"],
      ["D", "T"],
      ["G", "C"],
      ["F", "V"],
      ["A", "E"],
      ["E", "I"],
      ["O", "U"]
    ];
    for (const group of similarSounds) {
      if (group.includes(letter1) && group.includes(letter2)) {
        return 0.7;
      }
    }
    return 0.1;
  }
  getConfusionFrequency(confusionKey) {
    return this.errorData.persistentErrors[confusionKey] || 0;
  }
  recommendIntervention(confusionKey, confusionData) {
    const interventions = [];
    if (confusionData.confusionType.includes("visual")) {
      interventions.push({
        type: "visual_differentiation",
        description: "Exercícios específicos de diferenciação visual",
        priority: "high"
      });
    }
    if (confusionData.confusionType.includes("phonetic")) {
      interventions.push({
        type: "phonetic_training",
        description: "Treino intensivo de discriminação fonética",
        priority: "high"
      });
    }
    if (confusionData.severity > 0.7) {
      interventions.push({
        type: "intensive_practice",
        description: "Prática intensiva da distinção específica",
        priority: "urgent"
      });
    }
    return interventions;
  }
  countSubstitutions(expected, actual) {
    let substitutions = 0;
    const minLength = Math.min(expected.length, actual.length);
    for (let i = 0; i < minLength; i++) {
      if (expected[i] !== actual[i]) {
        substitutions++;
      }
    }
    return substitutions;
  }
  detectStrategyFailure(expected, actual) {
    const errorPattern = this.analyzeErrorPattern(expected, actual);
    return errorPattern.includes("systematic") || errorPattern.includes("repeated");
  }
  generateMemoryRecommendations(implications) {
    const recommendations = [];
    if (implications.capacity_exceeded) {
      recommendations.push("Reduzir carga cognitiva e aumentar gradualmente");
    }
    if (implications.interference_effect) {
      recommendations.push("Praticar em ambiente com menos distrações");
    }
    if (implications.decay_effect) {
      recommendations.push("Reduzir delays e implementar estratégias de rehearsal");
    }
    if (implications.strategy_failure) {
      recommendations.push("Ensinar estratégias explícitas de memória");
    }
    return recommendations;
  }
  calculateClusterSeverity(cluster) {
    const errorCount = cluster.errors.length;
    const timeSpan = new Date(cluster.lastUpdate).getTime() - new Date(cluster.startTime).getTime();
    const frequency = errorCount / (timeSpan / 1e3);
    return Math.min(1, frequency * 1e3);
  }
  identifyClusterPattern(cluster) {
    if (cluster.errors.length < 3) return "emerging";
    if (cluster.errors.length > 5) return "persistent";
    const timeSpan = new Date(cluster.lastUpdate).getTime() - new Date(cluster.startTime).getTime();
    if (timeSpan < 1e4) return "burst";
    return "pattern";
  }
  getRecentErrors(timeWindow) {
    const cutoffTime = Date.now() - timeWindow;
    const allErrors = [
      ...this.errorData.visualErrors,
      ...this.errorData.auditoryErrors,
      ...this.errorData.sequenceErrors
    ];
    return allErrors.filter(
      (error) => new Date(error.timestamp).getTime() > cutoffTime
    );
  }
  createTimeWindows(errors, windowSize) {
    if (errors.length === 0) return [];
    const windows = [];
    const startTime = new Date(errors[0].timestamp).getTime();
    errors.forEach((error) => {
      const errorTime = new Date(error.timestamp).getTime();
      const windowIndex = Math.floor((errorTime - startTime) / windowSize);
      if (!windows[windowIndex]) windows[windowIndex] = [];
      windows[windowIndex].push(error);
    });
    return windows.filter((window2) => window2 && window2.length > 0);
  }
  calculateTrend(values) {
    if (values.length < 2) return "stable";
    let increasing = 0;
    let decreasing = 0;
    for (let i = 1; i < values.length; i++) {
      if (values[i] > values[i - 1]) increasing++;
      else if (values[i] < values[i - 1]) decreasing++;
    }
    if (increasing > decreasing) return "increasing";
    if (decreasing > increasing) return "decreasing";
    return "stable";
  }
  generateLearningCurve() {
    const timeWindows = this.createTimeWindows(this.getAllErrors(), 6e4);
    return timeWindows.map((window2, index) => ({
      timeWindow: index,
      errorCount: window2.length,
      accuracy: 1 - window2.length / 10
      // Assumindo máximo de 10 erros por janela
    }));
  }
  findSimilarLetters(targetLetter) {
    const allSimilarities = [
      ["B", "D", "P", "Q"],
      ["C", "O", "G"],
      ["F", "E", "T"],
      ["I", "L", "J"],
      ["M", "N", "W"],
      ["V", "Y", "X"],
      ["A", "H", "R"]
    ];
    for (const group of allSimilarities) {
      if (group.includes(targetLetter)) {
        return group.filter((letter) => letter !== targetLetter);
      }
    }
    return [];
  }
  getPerformanceOnLetters(letters) {
    const allConfusions = Object.values(this.errorData.letterConfusions).flat();
    const letterPerformances = letters.map((letter) => {
      const letterErrors = allConfusions.filter((conf) => conf.targetLetter === letter);
      const letterAttempts = this.getAttemptCount(letter);
      return letterAttempts > 0 ? 1 - letterErrors.length / letterAttempts : 1;
    });
    return {
      averageAccuracy: letterPerformances.reduce((sum, acc) => sum + acc, 0) / letterPerformances.length || 0,
      individualAccuracies: letterPerformances
    };
  }
  countSelfCorrections() {
    return this.errorData.learningIndicators.filter((indicator) => indicator.metacognition > 0.7).length;
  }
  countStrategyChanges() {
    return this.errorData.learningIndicators.filter((indicator) => indicator.strategyEvolution.complexity > 0.5).length;
  }
  assessErrorAwareness() {
    const recentIndicators = this.errorData.learningIndicators.slice(-5);
    const awarenessScores = recentIndicators.map((indicator) => indicator.metacognition);
    return awarenessScores.reduce((sum, score) => sum + score, 0) / awarenessScores.length || 0;
  }
  countQuitAfterError() {
    return 0;
  }
  countContinueAfterError() {
    return this.getAllErrors().length;
  }
  identifyUsedStrategies() {
    const strategies = /* @__PURE__ */ new Set();
    if (this.hasSystematicApproach()) strategies.add("systematic");
    if (this.hasTrialAndError()) strategies.add("trial_and_error");
    if (this.hasPatternRecognition()) strategies.add("pattern_recognition");
    if (this.hasMemoryStrategy()) strategies.add("memory_strategy");
    return Array.from(strategies);
  }
  createStrategyTimeline() {
    return this.errorData.learningIndicators.map((indicator) => ({
      timestamp: indicator.timestamp,
      strategies: indicator.strategyEvolution.currentStrategies || [],
      effectiveness: indicator.strategyEvolution.effectiveness || 0
    }));
  }
  calculateStrategyComplexity(strategies) {
    const complexityScores = {
      "trial_and_error": 0.2,
      "memory_strategy": 0.5,
      "pattern_recognition": 0.7,
      "systematic": 0.9
    };
    const totalComplexity = strategies.reduce(
      (sum, strategy) => sum + (complexityScores[strategy] || 0),
      0
    );
    return strategies.length > 0 ? totalComplexity / strategies.length : 0;
  }
  calculateStrategyEffectiveness(strategies) {
    const recentLearningIndicators = this.errorData.learningIndicators.slice(-5);
    const errorReductions = recentLearningIndicators.map((indicator) => indicator.errorReduction);
    return errorReductions.reduce((sum, reduction) => sum + reduction, 0) / errorReductions.length || 0;
  }
  getAllErrors() {
    return [
      ...this.errorData.visualErrors,
      ...this.errorData.auditoryErrors,
      ...this.errorData.sequenceErrors,
      ...Object.values(this.errorData.letterConfusions).flat()
    ];
  }
  getAttemptCount(letter) {
    const confusions = Object.values(this.errorData.letterConfusions).flat();
    return confusions.filter((conf) => conf.targetLetter === letter).length + 1;
  }
  analyzeErrorPattern(expected, actual) {
    const patterns = [];
    if (expected.length !== actual.length) patterns.push("length_mismatch");
    if (this.hasRepeatedErrors(expected, actual)) patterns.push("repeated");
    if (this.hasSystematicErrors(expected, actual)) patterns.push("systematic");
    return patterns;
  }
  hasRepeatedErrors(expected, actual) {
    const errors = [];
    for (let i = 0; i < Math.min(expected.length, actual.length); i++) {
      if (expected[i] !== actual[i]) {
        errors.push(`${expected[i]}->${actual[i]}`);
      }
    }
    return new Set(errors).size < errors.length;
  }
  hasSystematicErrors(expected, actual) {
    const errorPositions = [];
    for (let i = 0; i < Math.min(expected.length, actual.length); i++) {
      if (expected[i] !== actual[i]) {
        errorPositions.push(i);
      }
    }
    if (errorPositions.length > 1) {
      const pattern = errorPositions[1] - errorPositions[0];
      for (let i = 2; i < errorPositions.length; i++) {
        if (errorPositions[i] - errorPositions[i - 1] !== pattern) {
          return false;
        }
      }
      return true;
    }
    return false;
  }
  hasSystematicApproach() {
    const allErrors = this.getAllErrors();
    return allErrors.length > 0 && this.calculateTrend(allErrors.map((e) => e.severity || 0)) === "decreasing";
  }
  hasTrialAndError() {
    return this.errorData.visualErrors.length > this.errorData.auditoryErrors.length;
  }
  hasPatternRecognition() {
    const confusions = Object.keys(this.errorData.letterConfusions);
    return confusions.some((confusion) => this.getConfusionFrequency(confusion) < 2);
  }
  hasMemoryStrategy() {
    return this.errorData.sequenceErrors.length > 0;
  }
  /**
   * Gera relatório completo de padrões de erro
   */
  generateErrorReport() {
    return {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      sessionDuration: Date.now() - this.sessionStartTime,
      summary: {
        totalErrors: this.getAllErrors().length,
        letterConfusions: Object.keys(this.errorData.letterConfusions).length,
        visualErrors: this.errorData.visualErrors.length,
        auditoryErrors: this.errorData.auditoryErrors.length,
        sequenceErrors: this.errorData.sequenceErrors.length,
        persistentErrors: Object.keys(this.errorData.persistentErrors).length,
        errorClusters: this.errorData.errorClusters.length
      },
      analysis: {
        mostCommonConfusions: this.getMostCommonConfusions(),
        errorTrends: this.analyzeErrorTrends(),
        learningProgress: this.analyzeLearningProgress(),
        interventionNeeds: this.identifyInterventionNeeds()
      },
      recommendations: this.generateErrorRecommendations()
    };
  }
  getMostCommonConfusions() {
    return Object.entries(this.errorData.persistentErrors).sort(([, a], [, b]) => b - a).slice(0, 5).map(([confusion, frequency]) => ({ confusion, frequency }));
  }
  analyzeErrorTrends() {
    const allErrors = this.getAllErrors();
    const timeWindows = this.createTimeWindows(allErrors, 6e4);
    const errorCounts = timeWindows.map((window2) => window2.length);
    return {
      trend: this.calculateTrend(errorCounts),
      errorRate: allErrors.length / ((Date.now() - this.sessionStartTime) / 1e3),
      volatility: this.calculateVolatility(errorCounts)
    };
  }
  analyzeLearningProgress() {
    const indicators = this.errorData.learningIndicators;
    if (indicators.length === 0) return { noData: true };
    return {
      averageErrorReduction: indicators.reduce((sum, i) => sum + i.errorReduction, 0) / indicators.length,
      averageAdaptationSpeed: indicators.reduce((sum, i) => sum + i.adaptationSpeed, 0) / indicators.length,
      transferLearning: indicators.reduce((sum, i) => sum + i.transferLearning, 0) / indicators.length,
      metacognition: indicators.reduce((sum, i) => sum + i.metacognition, 0) / indicators.length,
      persistence: indicators.reduce((sum, i) => sum + i.persistenceLevel, 0) / indicators.length
    };
  }
  identifyInterventionNeeds() {
    const needs = [];
    const persistentErrors = Object.entries(this.errorData.persistentErrors).filter(([, frequency]) => frequency >= 3);
    if (persistentErrors.length > 0) {
      needs.push({
        type: "persistent_errors",
        priority: "high",
        details: persistentErrors.map(([confusion]) => confusion)
      });
    }
    const severeClusters = this.errorData.errorClusters.filter((cluster) => cluster.severity > 0.7);
    if (severeClusters.length > 0) {
      needs.push({
        type: "error_clusters",
        priority: "medium",
        details: severeClusters.map((c) => c.errorType)
      });
    }
    return needs;
  }
  /**
   * Retorna padrões de erro compilados
   */
  getErrorPatterns() {
    return {
      letterConfusions: this.errorData.letterConfusions,
      visualErrors: this.errorData.visualErrors,
      auditoryErrors: this.errorData.auditoryErrors,
      sequenceErrors: this.errorData.sequenceErrors,
      totalErrors: this.getTotalErrorCount(),
      errorTypes: this.getErrorTypeDistribution(),
      severity: this.getOverallErrorSeverity(),
      trends: this.getErrorTrends()
    };
  }
  /**
   * Retorna erros persistentes
   */
  getPersistentErrors() {
    return this.errorData.persistentErrors;
  }
  /**
   * Retorna clusters de erro
   */
  getErrorClusters() {
    return this.errorData.errorClusters;
  }
  /**
   * Retorna indicadores de aprendizagem
   */
  getLearningIndicators() {
    return this.errorData.learningIndicators;
  }
  /**
   * Retorna frequência de erros
   */
  getErrorFrequency() {
    const allErrors = [
      ...this.errorData.visualErrors,
      ...this.errorData.auditoryErrors,
      ...this.errorData.sequenceErrors,
      ...Object.values(this.errorData.letterConfusions).flat()
    ];
    return {
      total: allErrors.length,
      perMinute: allErrors.length / ((Date.now() - this.sessionStartTime) / 6e4),
      byType: this.getErrorTypeDistribution()
    };
  }
  /**
   * Gera recomendações baseadas nos erros
   */
  generateErrorRecommendations() {
    const recommendations = [];
    const confusionCount = Object.keys(this.errorData.letterConfusions).length;
    if (confusionCount > 5) {
      recommendations.push({
        type: "letter_confusion",
        priority: "high",
        action: "Foco em discriminação visual e fonológica",
        target: "letter_discrimination"
      });
    }
    if (this.errorData.visualErrors.length > 3) {
      recommendations.push({
        type: "visual_processing",
        priority: "medium",
        action: "Exercícios de percepção visual",
        target: "visual_skills"
      });
    }
    if (this.errorData.auditoryErrors.length > 3) {
      recommendations.push({
        type: "auditory_processing",
        priority: "medium",
        action: "Treinamento de discriminação auditiva",
        target: "auditory_skills"
      });
    }
    return recommendations;
  }
  /**
   * Calcula contagem total de erros
   */
  getTotalErrorCount() {
    return this.errorData.visualErrors.length + this.errorData.auditoryErrors.length + this.errorData.sequenceErrors.length + Object.values(this.errorData.letterConfusions).flat().length;
  }
  /**
   * Calcula distribuição de tipos de erro
   */
  getErrorTypeDistribution() {
    const total = this.getTotalErrorCount();
    if (total === 0) return {};
    return {
      visual: this.errorData.visualErrors.length / total,
      auditory: this.errorData.auditoryErrors.length / total,
      sequence: this.errorData.sequenceErrors.length / total,
      confusion: Object.values(this.errorData.letterConfusions).flat().length / total
    };
  }
  /**
   * Calcula severidade geral dos erros
   */
  getOverallErrorSeverity() {
    const allErrors = [
      ...this.errorData.visualErrors,
      ...this.errorData.auditoryErrors,
      ...this.errorData.sequenceErrors,
      ...Object.values(this.errorData.letterConfusions).flat()
    ];
    if (allErrors.length === 0) return 0;
    const totalSeverity = allErrors.reduce((sum, error) => sum + (error.severity || 0), 0);
    return totalSeverity / allErrors.length;
  }
  /**
   * Calcula tendências dos erros
   */
  getErrorTrends() {
    const recentErrors = [
      ...this.errorData.visualErrors,
      ...this.errorData.auditoryErrors,
      ...this.errorData.sequenceErrors,
      ...Object.values(this.errorData.letterConfusions).flat()
    ].filter((error) => {
      const errorTime = new Date(error.timestamp).getTime();
      return Date.now() - errorTime < 3e5;
    });
    return {
      recent: recentErrors.length,
      trend: recentErrors.length > 5 ? "increasing" : "stable",
      mostCommon: this.getMostCommonErrorType(recentErrors)
    };
  }
  /**
   * Identifica tipo de erro mais comum
   */
  getMostCommonErrorType(errors) {
    const typeCounts = {};
    errors.forEach((error) => {
      const type = error.errorType || "unknown";
      typeCounts[type] = (typeCounts[type] || 0) + 1;
    });
    let maxType = "none";
    let maxCount = 0;
    for (const [type, count] of Object.entries(typeCounts)) {
      if (count > maxCount) {
        maxCount = count;
        maxType = type;
      }
    }
    return maxType;
  }
  /**
   * Retorna dados coletados
   */
  getData() {
    return this.errorData;
  }
  /**
   * Reseta dados coletados
   */
  reset() {
    this.errorData = {
      letterConfusions: {},
      visualErrors: [],
      auditoryErrors: [],
      sequenceErrors: [],
      persistentErrors: {},
      errorClusters: [],
      learningIndicators: []
    };
    this.sessionStartTime = Date.now();
  }
  /**
   * Método principal de análise para compatibilidade com o hub
   * @param {Object} enrichedData - Dados enriquecidos da tentativa
   * @returns {Object} Análise de padrões de erro
   */
  async analyze(enrichedData) {
    try {
      if (!enrichedData.isCorrect && enrichedData.targetLetter && enrichedData.selectedLetter) {
        this.collectLetterConfusion(
          enrichedData.targetLetter,
          enrichedData.selectedLetter,
          {
            difficulty: enrichedData.difficulty || "MEDIUM",
            position: enrichedData.position || 0,
            distractors: enrichedData.distractors || [],
            responseTime: enrichedData.responseTime || 0,
            attempts: enrichedData.attempts || 1
          }
        );
      }
      if (enrichedData.errorType) {
        switch (enrichedData.errorType) {
          case "visual":
            this.collectVisualError(enrichedData);
            break;
          case "auditory":
            this.collectAuditoryError(enrichedData);
            break;
          case "sequence":
            this.collectSequenceError(enrichedData);
            break;
        }
      }
      return {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        errorPatterns: this.getErrorPatterns(),
        persistentErrors: this.getPersistentErrors(),
        errorClusters: this.getErrorClusters(),
        learningIndicators: this.getLearningIndicators(),
        errorFrequency: this.getErrorFrequency(),
        recommendations: this.generateErrorRecommendations(),
        hasData: true,
        dataQuality: "high"
      };
    } catch (error) {
      console.error("Erro na análise de padrões de erro:", error);
      return {
        error: error.message,
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        hasData: false
      };
    }
  }
}
class LinguisticProcessingCollector {
  constructor() {
    this.cognitiveEngine = new CognitiveAssociationEngine();
    this.linguisticData = {
      phoneticMapping: {},
      visualLetterForm: {},
      letterSoundAssociation: {},
      readingReadiness: {},
      dyslexiaIndicators: []
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Coleta dados de mapeamento fonético
   */
  collectPhoneticMapping(targetLetter, selectedLetter, responseTime, isCorrect) {
    const phoneticData = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      targetLetter,
      selectedLetter,
      responseTime,
      isCorrect,
      phoneticSimilarity: this.calculatePhoneticSimilarity(targetLetter, selectedLetter),
      confusionType: this.identifyConfusionType(targetLetter, selectedLetter)
    };
    if (!this.linguisticData.phoneticMapping[targetLetter]) {
      this.linguisticData.phoneticMapping[targetLetter] = [];
    }
    this.linguisticData.phoneticMapping[targetLetter].push(phoneticData);
    this.detectDyslexiaIndicators(phoneticData);
    return phoneticData;
  }
  /**
   * Coleta dados de forma visual das letras
   */
  collectVisualLetterForm(targetLetter, selectedLetter, viewTime, isCorrect) {
    const visualData = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      targetLetter,
      selectedLetter,
      viewTime,
      isCorrect,
      visualSimilarity: this.calculateVisualSimilarity(targetLetter, selectedLetter),
      orientationConfusion: this.detectOrientationConfusion(targetLetter, selectedLetter),
      visualMemoryStrength: this.assessVisualMemoryStrength(viewTime, isCorrect)
    };
    if (!this.linguisticData.visualLetterForm[targetLetter]) {
      this.linguisticData.visualLetterForm[targetLetter] = [];
    }
    this.linguisticData.visualLetterForm[targetLetter].push(visualData);
    return visualData;
  }
  /**
   * Coleta dados de associação letra-som
   */
  collectLetterSoundAssociation(letter, sound, responseTime, accuracy) {
    const associationData = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      letter,
      sound,
      responseTime,
      accuracy,
      associationStrength: this.calculateAssociationStrength(responseTime, accuracy),
      soundRecognitionQuality: this.assessSoundRecognitionQuality(responseTime)
    };
    if (!this.linguisticData.letterSoundAssociation[letter]) {
      this.linguisticData.letterSoundAssociation[letter] = [];
    }
    this.linguisticData.letterSoundAssociation[letter].push(associationData);
    return associationData;
  }
  /**
   * Avalia prontidão para leitura
   */
  assessReadingReadiness(sessionData) {
    const readinessMetrics = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      alphabetKnowledge: this.calculateAlphabetKnowledge(sessionData),
      phoneticAwareness: this.calculatePhoneticAwareness(sessionData),
      visualWordRecognition: this.calculateVisualWordRecognition(sessionData),
      letterNamingFluency: this.calculateLetterNamingFluency(sessionData),
      overallReadinessScore: 0
    };
    readinessMetrics.overallReadinessScore = readinessMetrics.alphabetKnowledge * 0.3 + readinessMetrics.phoneticAwareness * 0.3 + readinessMetrics.visualWordRecognition * 0.2 + readinessMetrics.letterNamingFluency * 0.2;
    this.linguisticData.readingReadiness = readinessMetrics;
    return readinessMetrics;
  }
  // Métodos auxiliares de análise
  calculatePhoneticSimilarity(letter1, letter2) {
    const phoneticGroups = {
      "similar_sounds": [["B", "P"], ["D", "T"], ["G", "C"], ["F", "V"]],
      "vowels": ["A", "E", "I", "O", "U"],
      "consonants": ["B", "C", "D", "F", "G", "H", "J", "K", "L", "M", "N", "P", "Q", "R", "S", "T", "V", "W", "X", "Y", "Z"]
    };
    for (const group of phoneticGroups.similar_sounds) {
      if (group.includes(letter1) && group.includes(letter2)) {
        return 0.8;
      }
    }
    if (phoneticGroups.vowels.includes(letter1) && phoneticGroups.vowels.includes(letter2)) {
      return 0.4;
    }
    if (phoneticGroups.consonants.includes(letter1) && phoneticGroups.consonants.includes(letter2)) {
      return 0.3;
    }
    return 0.1;
  }
  calculateVisualSimilarity(letter1, letter2) {
    const visualGroups = {
      "mirror_confusion": [["B", "D"], ["P", "Q"], ["U", "N"]],
      "rotation_confusion": [["M", "W"], ["6", "9"]],
      "similar_shapes": [["C", "O"], ["I", "L"], ["V", "Y"]]
    };
    for (const [type, groups] of Object.entries(visualGroups)) {
      for (const group of groups) {
        if (group.includes(letter1) && group.includes(letter2)) {
          return { similarity: 0.9, confusionType: type };
        }
      }
    }
    return { similarity: 0.1, confusionType: "none" };
  }
  identifyConfusionType(target, selected) {
    if (target === selected) return "correct";
    const visualSim = this.calculateVisualSimilarity(target, selected);
    if (visualSim.similarity > 0.7) return visualSim.confusionType;
    const phoneticSim = this.calculatePhoneticSimilarity(target, selected);
    if (phoneticSim > 0.7) return "phonetic_confusion";
    return "random_error";
  }
  detectDyslexiaIndicators(phoneticData) {
    const indicators = [];
    if (phoneticData.confusionType === "mirror_confusion") {
      indicators.push({
        type: "mirror_letter_confusion",
        severity: "medium",
        description: "Confusão persistente entre letras espelhadas (b/d, p/q)"
      });
    }
    if (phoneticData.responseTime > 5e3) {
      indicators.push({
        type: "slow_letter_processing",
        severity: "low",
        description: "Processamento lento de letras individuais"
      });
    }
    if (phoneticData.confusionType === "phonetic_confusion" && !phoneticData.isCorrect) {
      indicators.push({
        type: "phonetic_processing_difficulty",
        severity: "medium",
        description: "Dificuldade na associação fonética"
      });
    }
    this.linguisticData.dyslexiaIndicators.push(...indicators);
    return indicators;
  }
  calculateAlphabetKnowledge(sessionData) {
    const totalLetters = 26;
    const knownLetters = /* @__PURE__ */ new Set();
    sessionData.interactions?.forEach((interaction) => {
      if (interaction.correct && interaction.selected === interaction.correct) {
        knownLetters.add(interaction.element);
      }
    });
    return knownLetters.size / totalLetters * 100;
  }
  calculatePhoneticAwareness(sessionData) {
    const phoneticTasks = sessionData.interactions?.filter(
      (i) => i.actionType === "letter_selection" && i.correct
    ) || [];
    const averageResponseTime = phoneticTasks.reduce(
      (sum, task) => sum + (task.duration || 0),
      0
    ) / phoneticTasks.length || 0;
    const speedScore = Math.max(0, 100 - averageResponseTime / 50);
    const accuracyScore = phoneticTasks.length / (sessionData.interactions?.length || 1) * 100;
    return speedScore * 0.4 + accuracyScore * 0.6;
  }
  calculateVisualWordRecognition(sessionData) {
    const visualTasks = sessionData.interactions?.filter(
      (i) => i.element === "letter" && i.actionType === "letter_selection"
    ) || [];
    const accuracy = visualTasks.filter((t) => t.selected === t.correct).length / visualTasks.length || 0;
    return accuracy * 100;
  }
  calculateLetterNamingFluency(sessionData) {
    const namingTasks = sessionData.interactions?.filter(
      (i) => i.actionType === "letter_selection"
    ) || [];
    const averageTime = namingTasks.reduce(
      (sum, task) => sum + (task.duration || 0),
      0
    ) / namingTasks.length || 0;
    return Math.max(0, 100 - averageTime / 30);
  }
  detectOrientationConfusion(target, selected) {
    const orientationPairs = [["B", "D"], ["P", "Q"], ["U", "N"], ["M", "W"]];
    for (const pair of orientationPairs) {
      if (pair[0] === target && pair[1] === selected || pair[1] === target && pair[0] === selected) {
        return true;
      }
    }
    return false;
  }
  assessVisualMemoryStrength(viewTime, isCorrect) {
    if (!isCorrect) return 0;
    if (viewTime < 2e3) return 1;
    if (viewTime < 4e3) return 0.8;
    if (viewTime < 6e3) return 0.6;
    return 0.4;
  }
  calculateAssociationStrength(responseTime, accuracy) {
    const timeScore = Math.max(0, 1 - responseTime / 5e3);
    const accuracyScore = accuracy ? 1 : 0;
    return timeScore * 0.3 + accuracyScore * 0.7;
  }
  assessSoundRecognitionQuality(responseTime) {
    if (responseTime < 1500) return "excellent";
    if (responseTime < 3e3) return "good";
    if (responseTime < 5e3) return "fair";
    return "poor";
  }
  /**
   * Gera relatório completo de dados linguísticos
   */
  generateLinguisticReport() {
    return {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      phoneticProcessing: this.analyzePhoneticsData(),
      visualProcessing: this.analyzeVisualData(),
      letterSoundMapping: this.analyzeAssociationData(),
      readinessAssessment: this.linguisticData.readingReadiness,
      dyslexiaRiskFactors: this.analyzeDyslexiaRisk(),
      recommendations: this.generateRecommendations()
    };
  }
  analyzePhoneticsData() {
    const allPhoneticData = Object.values(this.linguisticData.phoneticMapping).flat();
    return {
      totalAttempts: allPhoneticData.length,
      averageAccuracy: allPhoneticData.filter((d) => d.isCorrect).length / allPhoneticData.length || 0,
      averageResponseTime: allPhoneticData.reduce((sum, d) => sum + d.responseTime, 0) / allPhoneticData.length || 0,
      commonConfusions: this.getCommonConfusions(allPhoneticData),
      strongLetters: this.getStrongLetters(allPhoneticData),
      difficultLetters: this.getDifficultLetters(allPhoneticData)
    };
  }
  analyzeVisualData() {
    const allVisualData = Object.values(this.linguisticData.visualLetterForm).flat();
    return {
      visualMemoryStrength: allVisualData.reduce((sum, d) => sum + d.visualMemoryStrength, 0) / allVisualData.length || 0,
      orientationConfusions: allVisualData.filter((d) => d.orientationConfusion).length,
      averageViewTime: allVisualData.reduce((sum, d) => sum + d.viewTime, 0) / allVisualData.length || 0,
      visualAccuracy: allVisualData.filter((d) => d.isCorrect).length / allVisualData.length || 0
    };
  }
  analyzeAssociationData() {
    const allAssociationData = Object.values(this.linguisticData.letterSoundAssociation).flat();
    return {
      averageAssociationStrength: allAssociationData.reduce((sum, d) => sum + d.associationStrength, 0) / allAssociationData.length || 0,
      soundRecognitionQuality: this.calculateOverallSoundQuality(allAssociationData),
      fastestAssociations: this.getFastestAssociations(allAssociationData),
      slowestAssociations: this.getSlowestAssociations(allAssociationData)
    };
  }
  analyzeDyslexiaRisk() {
    const indicators = this.linguisticData.dyslexiaIndicators;
    const riskFactors = {
      mirrorConfusion: indicators.filter((i) => i.type === "mirror_letter_confusion").length,
      phoneticDifficulty: indicators.filter((i) => i.type === "phonetic_processing_difficulty").length,
      slowProcessing: indicators.filter((i) => i.type === "slow_letter_processing").length
    };
    const totalRiskFactors = Object.values(riskFactors).reduce((sum, count) => sum + count, 0);
    return {
      riskLevel: totalRiskFactors > 5 ? "high" : totalRiskFactors > 2 ? "medium" : "low",
      riskFactors,
      recommendations: this.getDyslexiaRecommendations(riskFactors)
    };
  }
  generateRecommendations() {
    const phonetics = this.analyzePhoneticsData();
    const visual = this.analyzeVisualData();
    const associations = this.analyzeAssociationData();
    const recommendations = [];
    if (phonetics.averageAccuracy < 0.7) {
      recommendations.push({
        type: "phonetic_practice",
        priority: "high",
        description: "Intensificar prática de associação fonética"
      });
    }
    if (visual.orientationConfusions > 3) {
      recommendations.push({
        type: "orientation_training",
        priority: "high",
        description: "Exercícios específicos para orientação de letras"
      });
    }
    if (associations.averageAssociationStrength < 0.6) {
      recommendations.push({
        type: "letter_sound_mapping",
        priority: "medium",
        description: "Fortalecer mapeamento letra-som"
      });
    }
    return recommendations;
  }
  // Métodos auxiliares para análise
  getCommonConfusions(data) {
    const confusions = {};
    data.filter((d) => !d.isCorrect).forEach((d) => {
      const key = `${d.targetLetter}->${d.selectedLetter}`;
      confusions[key] = (confusions[key] || 0) + 1;
    });
    return Object.entries(confusions).sort(([, a], [, b]) => b - a).slice(0, 5).map(([confusion, count]) => ({ confusion, count }));
  }
  getStrongLetters(data) {
    const letterStats = {};
    data.forEach((d) => {
      if (!letterStats[d.targetLetter]) letterStats[d.targetLetter] = { correct: 0, total: 0 };
      letterStats[d.targetLetter].total++;
      if (d.isCorrect) letterStats[d.targetLetter].correct++;
    });
    return Object.entries(letterStats).filter(([, stats]) => stats.total >= 3 && stats.correct / stats.total >= 0.8).map(([letter, stats]) => ({ letter, accuracy: stats.correct / stats.total }));
  }
  getDifficultLetters(data) {
    const letterStats = {};
    data.forEach((d) => {
      if (!letterStats[d.targetLetter]) letterStats[d.targetLetter] = { correct: 0, total: 0 };
      letterStats[d.targetLetter].total++;
      if (d.isCorrect) letterStats[d.targetLetter].correct++;
    });
    return Object.entries(letterStats).filter(([, stats]) => stats.total >= 3 && stats.correct / stats.total < 0.5).map(([letter, stats]) => ({ letter, accuracy: stats.correct / stats.total }));
  }
  calculateOverallSoundQuality(data) {
    const qualityScores = { excellent: 4, good: 3, fair: 2, poor: 1 };
    const totalScore = data.reduce((sum, d) => sum + qualityScores[d.soundRecognitionQuality], 0);
    return totalScore / data.length || 0;
  }
  getFastestAssociations(data) {
    return data.sort((a, b) => a.responseTime - b.responseTime).slice(0, 5);
  }
  getSlowestAssociations(data) {
    return data.sort((a, b) => b.responseTime - a.responseTime).slice(0, 5);
  }
  getDyslexiaRecommendations(riskFactors) {
    const recommendations = [];
    if (riskFactors.mirrorConfusion > 2) {
      recommendations.push("Exercícios específicos para diferenciação de letras espelhadas");
    }
    if (riskFactors.phoneticDifficulty > 2) {
      recommendations.push("Atividades de consciência fonológica estruturadas");
    }
    if (riskFactors.slowProcessing > 3) {
      recommendations.push("Exercícios de fluência e velocidade de processamento");
    }
    return recommendations;
  }
  /**
   * Método principal de análise para compatibilidade com o hub
   * @param {Object} enrichedData - Dados enriquecidos da tentativa
   * @returns {Object} Análise de processamento linguístico
   */
  async analyze(enrichedData) {
    try {
      if (enrichedData.targetLetter && enrichedData.selectedLetter) {
        this.collectPhoneticMapping(
          enrichedData.targetLetter,
          enrichedData.selectedLetter,
          enrichedData.responseTime || 0,
          enrichedData.isCorrect
        );
        this.collectVisualLetterForm(
          enrichedData.targetLetter,
          enrichedData.selectedLetter,
          enrichedData.isCorrect
        );
        this.collectLetterSoundAssociation(
          enrichedData.targetLetter,
          enrichedData.selectedLetter,
          enrichedData.responseTime || 0,
          enrichedData.isCorrect
        );
      }
      return {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        phoneticAnalysis: this.analyzePhoneticMapping(),
        visualFormAnalysis: this.analyzeVisualLetterForm(),
        soundAssociationAnalysis: this.analyzeLetterSoundAssociation(),
        readinessAnalysis: this.analyzeReadingReadiness(),
        dyslexiaIndicators: this.getDyslexiaIndicators(),
        linguisticProfile: this.generateLinguisticProfile(),
        hasData: true,
        dataQuality: "high"
      };
    } catch (error) {
      console.error("Erro na análise de processamento linguístico:", error);
      return {
        error: error.message,
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        hasData: false
      };
    }
  }
  /**
   * Retorna dados coletados
   */
  getData() {
    return this.linguisticData;
  }
  /**
   * Reseta dados coletados
   */
  reset() {
    this.linguisticData = {
      phoneticMapping: {},
      visualLetterForm: {},
      letterSoundAssociation: {},
      readingReadiness: {},
      dyslexiaIndicators: []
    };
  }
  /**
   * Analisa mapeamento fonético
   */
  analyzePhoneticMapping() {
    const phoneticData = this.linguisticData.phoneticMapping;
    return {
      phoneticAccuracy: this.calculatePhoneticAccuracy(phoneticData),
      phoneticConsistency: this.calculatePhoneticConsistency(phoneticData),
      phoneticProgress: this.calculatePhoneticProgress(phoneticData),
      phoneticDifficulties: this.identifyPhoneticDifficulties(phoneticData),
      phoneticStrengths: this.identifyPhoneticStrengths(phoneticData),
      phoneticRecommendations: this.generatePhoneticRecommendations(phoneticData)
    };
  }
  /**
   * Analisa forma visual da letra
   */
  analyzeVisualLetterForm() {
    const visualData = this.linguisticData.visualLetterForm;
    return {
      visualRecognition: this.calculateVisualRecognition(visualData),
      visualDiscrimination: this.calculateVisualDiscrimination(visualData),
      visualMemory: this.calculateVisualMemory(visualData),
      visualProcessing: this.calculateVisualProcessing(visualData),
      visualDifficulties: this.identifyVisualDifficulties(visualData),
      visualStrengths: this.identifyVisualStrengths(visualData)
    };
  }
  /**
   * Analisa associação letra-som
   */
  analyzeLetterSoundAssociation() {
    const associationData = this.linguisticData.letterSoundAssociation;
    return {
      associationStrength: this.calculateAssociationStrength(associationData),
      associationSpeed: this.calculateAssociationSpeed(associationData),
      associationConsistency: this.calculateAssociationConsistency(associationData),
      associationDifficulties: this.identifyAssociationDifficulties(associationData),
      associationProgress: this.calculateAssociationProgress(associationData)
    };
  }
  /**
   * Analisa prontidão para leitura
   */
  analyzeReadingReadiness() {
    const readinessData = this.linguisticData.readingReadiness;
    return {
      readinessLevel: this.calculateReadinessLevel(readinessData),
      readinessSkills: this.assessReadinessSkills(readinessData),
      readinessGaps: this.identifyReadinessGaps(readinessData),
      readinessProgress: this.calculateReadinessProgress(readinessData),
      readinessRecommendations: this.generateReadinessRecommendations(readinessData)
    };
  }
  /**
   * Obtém indicadores de dislexia
   */
  getDyslexiaIndicators() {
    return this.linguisticData.dyslexiaIndicators;
  }
  /**
   * Gera perfil linguístico
   */
  generateLinguisticProfile() {
    return {
      phoneticProfile: this.analyzePhoneticMapping(),
      visualProfile: this.analyzeVisualLetterForm(),
      associationProfile: this.analyzeLetterSoundAssociation(),
      readinessProfile: this.analyzeReadingReadiness(),
      overallProfile: this.calculateOverallLinguisticProfile()
    };
  }
  /**
   * Métodos auxiliares de cálculo
   */
  calculatePhoneticAccuracy(phoneticData) {
    const accuracyValues = Object.values(phoneticData).map((data) => data.accuracy || 0);
    return accuracyValues.length > 0 ? accuracyValues.reduce((sum, acc) => sum + acc, 0) / accuracyValues.length : 0.5;
  }
  calculatePhoneticConsistency(phoneticData) {
    const consistencyValues = Object.values(phoneticData).map((data) => data.consistency || 0);
    return consistencyValues.length > 0 ? consistencyValues.reduce((sum, con) => sum + con, 0) / consistencyValues.length : 0.5;
  }
  calculatePhoneticProgress(phoneticData) {
    const progressValues = Object.values(phoneticData).map((data) => data.progress || 0);
    return progressValues.length > 0 ? progressValues.reduce((sum, prog) => sum + prog, 0) / progressValues.length : 0.5;
  }
  identifyPhoneticDifficulties(phoneticData) {
    const difficulties = [];
    for (const [letter, data] of Object.entries(phoneticData)) {
      if (data.accuracy < 0.5) {
        difficulties.push({
          letter,
          difficulty: "phonetic_mapping",
          severity: 1 - data.accuracy
        });
      }
    }
    return difficulties;
  }
  identifyPhoneticStrengths(phoneticData) {
    const strengths = [];
    for (const [letter, data] of Object.entries(phoneticData)) {
      if (data.accuracy >= 0.8) {
        strengths.push({
          letter,
          strength: "phonetic_mapping",
          level: data.accuracy
        });
      }
    }
    return strengths;
  }
  generatePhoneticRecommendations(phoneticData) {
    const recommendations = [];
    const difficulties = this.identifyPhoneticDifficulties(phoneticData);
    difficulties.forEach((difficulty) => {
      recommendations.push({
        type: "phonetic_training",
        target: difficulty.letter,
        priority: difficulty.severity > 0.7 ? "high" : "medium",
        action: `Treino específico para mapeamento fonético da letra ${difficulty.letter}`
      });
    });
    return recommendations;
  }
  calculateVisualRecognition(visualData) {
    const recognitionValues = Object.values(visualData).map((data) => data.recognition || 0);
    return recognitionValues.length > 0 ? recognitionValues.reduce((sum, rec) => sum + rec, 0) / recognitionValues.length : 0.5;
  }
  calculateVisualDiscrimination(visualData) {
    const discriminationValues = Object.values(visualData).map((data) => data.discrimination || 0);
    return discriminationValues.length > 0 ? discriminationValues.reduce((sum, disc) => sum + disc, 0) / discriminationValues.length : 0.5;
  }
  calculateVisualMemory(visualData) {
    const memoryValues = Object.values(visualData).map((data) => data.memory || 0);
    return memoryValues.length > 0 ? memoryValues.reduce((sum, mem) => sum + mem, 0) / memoryValues.length : 0.5;
  }
  calculateVisualProcessing(visualData) {
    const processingValues = Object.values(visualData).map((data) => data.processing || 0);
    return processingValues.length > 0 ? processingValues.reduce((sum, proc) => sum + proc, 0) / processingValues.length : 0.5;
  }
  identifyVisualDifficulties(visualData) {
    const difficulties = [];
    for (const [letter, data] of Object.entries(visualData)) {
      if (data.recognition < 0.5) {
        difficulties.push({
          letter,
          difficulty: "visual_recognition",
          severity: 1 - data.recognition
        });
      }
    }
    return difficulties;
  }
  identifyVisualStrengths(visualData) {
    const strengths = [];
    for (const [letter, data] of Object.entries(visualData)) {
      if (data.recognition >= 0.8) {
        strengths.push({
          letter,
          strength: "visual_recognition",
          level: data.recognition
        });
      }
    }
    return strengths;
  }
  calculateAssociationSpeed(associationData) {
    const speedValues = Object.values(associationData).map((data) => data.speed || 0);
    return speedValues.length > 0 ? speedValues.reduce((sum, speed) => sum + speed, 0) / speedValues.length : 0.5;
  }
  calculateAssociationConsistency(associationData) {
    const consistencyValues = Object.values(associationData).map((data) => data.consistency || 0);
    return consistencyValues.length > 0 ? consistencyValues.reduce((sum, con) => sum + con, 0) / consistencyValues.length : 0.5;
  }
  identifyAssociationDifficulties(associationData) {
    const difficulties = [];
    for (const [letter, data] of Object.entries(associationData)) {
      if (data.strength < 0.5) {
        difficulties.push({
          letter,
          difficulty: "letter_sound_association",
          severity: 1 - data.strength
        });
      }
    }
    return difficulties;
  }
  calculateAssociationProgress(associationData) {
    const progressValues = Object.values(associationData).map((data) => data.progress || 0);
    return progressValues.length > 0 ? progressValues.reduce((sum, prog) => sum + prog, 0) / progressValues.length : 0.5;
  }
  calculateReadinessLevel(readinessData) {
    const levelValues = Object.values(readinessData).map((data) => data.level || 0);
    return levelValues.length > 0 ? levelValues.reduce((sum, level) => sum + level, 0) / levelValues.length : 0.5;
  }
  assessReadinessSkills(readinessData) {
    return {
      phoneticAwareness: readinessData.phoneticAwareness || 0.5,
      letterKnowledge: readinessData.letterKnowledge || 0.5,
      visualProcessing: readinessData.visualProcessing || 0.5,
      auditoryProcessing: readinessData.auditoryProcessing || 0.5,
      workingMemory: readinessData.workingMemory || 0.5
    };
  }
  identifyReadinessGaps(readinessData) {
    const gaps = [];
    const skills = this.assessReadinessSkills(readinessData);
    for (const [skill, level] of Object.entries(skills)) {
      if (level < 0.5) {
        gaps.push({
          skill,
          gap: "readiness_deficit",
          severity: 1 - level
        });
      }
    }
    return gaps;
  }
  calculateReadinessProgress(readinessData) {
    const progressValues = Object.values(readinessData).map((data) => data.progress || 0);
    return progressValues.length > 0 ? progressValues.reduce((sum, prog) => sum + prog, 0) / progressValues.length : 0.5;
  }
  generateReadinessRecommendations(readinessData) {
    const recommendations = [];
    const gaps = this.identifyReadinessGaps(readinessData);
    gaps.forEach((gap) => {
      recommendations.push({
        type: "readiness_training",
        target: gap.skill,
        priority: gap.severity > 0.7 ? "high" : "medium",
        action: `Desenvolvimento de ${gap.skill} para prontidão de leitura`
      });
    });
    return recommendations;
  }
  calculateOverallLinguisticProfile() {
    const phoneticScore = this.calculatePhoneticAccuracy(this.linguisticData.phoneticMapping);
    const visualScore = this.calculateVisualRecognition(this.linguisticData.visualLetterForm);
    const associationScore = this.calculateAssociationStrength(this.linguisticData.letterSoundAssociation);
    const readinessScore = this.calculateReadinessLevel(this.linguisticData.readingReadiness);
    return {
      overallScore: (phoneticScore + visualScore + associationScore + readinessScore) / 4,
      profileType: this.classifyLinguisticProfile(phoneticScore, visualScore, associationScore, readinessScore),
      strongestArea: this.identifyStrongestArea(phoneticScore, visualScore, associationScore, readinessScore),
      weakestArea: this.identifyWeakestArea(phoneticScore, visualScore, associationScore, readinessScore)
    };
  }
  classifyLinguisticProfile(phoneticScore, visualScore, associationScore, readinessScore) {
    const overallScore = (phoneticScore + visualScore + associationScore + readinessScore) / 4;
    if (overallScore >= 0.8) return "advanced";
    if (overallScore >= 0.6) return "proficient";
    if (overallScore >= 0.4) return "developing";
    return "emerging";
  }
  identifyStrongestArea(phoneticScore, visualScore, associationScore, readinessScore) {
    const scores = {
      phonetic: phoneticScore,
      visual: visualScore,
      association: associationScore,
      readiness: readinessScore
    };
    let strongest = "phonetic";
    let highestScore = phoneticScore;
    for (const [area, score] of Object.entries(scores)) {
      if (score > highestScore) {
        highestScore = score;
        strongest = area;
      }
    }
    return strongest;
  }
  identifyWeakestArea(phoneticScore, visualScore, associationScore, readinessScore) {
    const scores = {
      phonetic: phoneticScore,
      visual: visualScore,
      association: associationScore,
      readiness: readinessScore
    };
    let weakest = "phonetic";
    let lowestScore = phoneticScore;
    for (const [area, score] of Object.entries(scores)) {
      if (score < lowestScore) {
        lowestScore = score;
        weakest = area;
      }
    }
    return weakest;
  }
}
class VisualAttentionCollector {
  constructor() {
    this.collectorId = "visual_attention";
    this.isActive = true;
    this.attentionMetrics = {
      focusTime: [],
      distractionEvents: [],
      visualScanningPatterns: [],
      attentionSustaining: [],
      selectiveAttention: []
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Coleta dados sobre padrões de atenção visual
   * @param {Object} gameData - Dados do jogo
   * @param {Object} playerBehavior - Comportamento do jogador
   * @returns {Object} - Métricas de atenção visual
   */
  collectAttentionData(gameData, playerBehavior) {
    try {
      const attentionData = {
        timestamp: Date.now(),
        sessionId: gameData.sessionId,
        // Métricas de foco visual
        focusMetrics: this.analyzeFocusPatterns(playerBehavior),
        // Padrões de varredura visual
        scanningPatterns: this.analyzeVisualScanning(playerBehavior),
        // Atenção seletiva
        selectiveAttention: this.analyzeSelectiveAttention(gameData, playerBehavior),
        // Sustentação da atenção
        attentionSustaining: this.analyzeAttentionSustaining(playerBehavior),
        // Eventos de distração
        distractionEvents: this.detectDistractionEvents(playerBehavior)
      };
      this.updateAttentionMetrics(attentionData);
      return attentionData;
    } catch (error) {
      console.error("Erro na coleta de dados de atenção visual:", error);
      return this.getDefaultAttentionData();
    }
  }
  /**
   * Analisa padrões de foco visual
   */
  analyzeFocusPatterns(playerBehavior) {
    const focusTime = playerBehavior.focusTime || 0;
    const totalTime = playerBehavior.totalTime || 1;
    return {
      focusRatio: focusTime / totalTime,
      averageFocusDuration: this.calculateAverageFocusDuration(playerBehavior),
      focusStability: this.calculateFocusStability(playerBehavior),
      peakFocusTime: Math.max(...playerBehavior.focusIntervals || [0])
    };
  }
  /**
   * Analisa padrões de varredura visual
   */
  analyzeVisualScanning(playerBehavior) {
    const eyeMovements = playerBehavior.eyeMovements || [];
    const clickPattern = playerBehavior.clickPattern || [];
    return {
      scanningEfficiency: this.calculateScanningEfficiency(eyeMovements),
      systematicScanning: this.detectSystematicScanning(clickPattern),
      visualSearchStrategy: this.identifySearchStrategy(eyeMovements),
      scanningSpeed: this.calculateScanningSpeed(eyeMovements)
    };
  }
  /**
   * Analisa atenção seletiva
   */
  analyzeSelectiveAttention(gameData, playerBehavior) {
    const targetLetter = gameData.targetLetter;
    const distractors = gameData.distractors || [];
    const playerClicks = playerBehavior.clickSequence || [];
    return {
      targetFocusAccuracy: this.calculateTargetFocusAccuracy(targetLetter, playerClicks),
      distractorResistance: this.calculateDistractorResistance(distractors, playerClicks),
      selectiveAccuracy: this.calculateSelectiveAccuracy(gameData, playerBehavior),
      attentionalControl: this.assessAttentionalControl(playerBehavior)
    };
  }
  /**
   * Analisa sustentação da atenção
   */
  analyzeAttentionSustaining(playerBehavior) {
    const sessionDuration = playerBehavior.sessionDuration || 0;
    const attentionDecline = this.calculateAttentionDecline(playerBehavior);
    return {
      sustainingCapacity: this.calculateSustainingCapacity(sessionDuration, attentionDecline),
      attentionDeclineRate: attentionDecline,
      fatigueIndications: this.detectFatigueIndications(playerBehavior),
      motivationLevel: this.assessMotivationLevel(playerBehavior)
    };
  }
  /**
   * Detecta eventos de distração
   */
  detectDistractionEvents(playerBehavior) {
    const clickPattern = playerBehavior.clickPattern || [];
    const irrelevantClicks = clickPattern.filter(
      (click) => click.type === "irrelevant" || click.accuracy < 0.3
    );
    const longPauses = this.detectLongPauses(playerBehavior);
    return {
      totalDistractions: irrelevantClicks.length + longPauses.length,
      irrelevantClicks: irrelevantClicks.length,
      attentionLapses: longPauses.length,
      distractionSeverity: this.calculateDistractionSeverity(irrelevantClicks, longPauses)
    };
  }
  /**
   * Métodos auxiliares de cálculo
   */
  calculateAverageFocusDuration(playerBehavior) {
    const focusIntervals = playerBehavior.focusIntervals || [];
    return focusIntervals.length > 0 ? focusIntervals.reduce((sum, interval) => sum + interval, 0) / focusIntervals.length : 0;
  }
  calculateFocusStability(playerBehavior) {
    const focusIntervals = playerBehavior.focusIntervals || [];
    if (focusIntervals.length < 2) return 1;
    const mean = this.calculateAverageFocusDuration(playerBehavior);
    const variance = focusIntervals.reduce((sum, interval) => sum + Math.pow(interval - mean, 2), 0) / focusIntervals.length;
    return 1 / (1 + Math.sqrt(variance));
  }
  calculateScanningEfficiency(eyeMovements) {
    if (!eyeMovements || eyeMovements.length === 0) return 0;
    const totalDistance = eyeMovements.reduce((sum, movement) => sum + (movement.distance || 0), 0);
    const directPath = this.calculateDirectPath(eyeMovements);
    return directPath / (totalDistance || 1);
  }
  detectSystematicScanning(clickPattern) {
    if (!clickPattern || clickPattern.length < 3) return false;
    let sequentialClicks = 0;
    for (let i = 1; i < clickPattern.length; i++) {
      const prevClick = clickPattern[i - 1];
      const currentClick = clickPattern[i];
      if (this.isSequentialPosition(prevClick.position, currentClick.position)) {
        sequentialClicks++;
      }
    }
    return sequentialClicks / (clickPattern.length - 1) > 0.6;
  }
  identifySearchStrategy(eyeMovements) {
    return {
      strategy: "systematic",
      // ou 'random', 'targeted'
      confidence: 0.8
    };
  }
  calculateScanningSpeed(eyeMovements) {
    if (!eyeMovements || eyeMovements.length < 2) return 0;
    const totalTime = eyeMovements[eyeMovements.length - 1].timestamp - eyeMovements[0].timestamp;
    const totalMovements = eyeMovements.length;
    return totalMovements / (totalTime || 1);
  }
  calculateTargetFocusAccuracy(targetLetter, playerClicks) {
    if (!playerClicks || playerClicks.length === 0) return 0;
    const targetClicks = playerClicks.filter(
      (click) => click.letter === targetLetter && click.isCorrect
    );
    return targetClicks.length / playerClicks.length;
  }
  calculateDistractorResistance(distractors, playerClicks) {
    if (!distractors || distractors.length === 0) return 1;
    const distractorClicks = playerClicks.filter(
      (click) => distractors.includes(click.letter) && !click.isCorrect
    );
    return 1 - distractorClicks.length / playerClicks.length;
  }
  calculateSelectiveAccuracy(gameData, playerBehavior) {
    const totalAttempts = playerBehavior.totalAttempts || 1;
    const correctAttempts = playerBehavior.correctAttempts || 0;
    const distractorAttempts = playerBehavior.distractorAttempts || 0;
    return (correctAttempts - distractorAttempts * 0.5) / totalAttempts;
  }
  assessAttentionalControl(playerBehavior) {
    const impulsiveResponses = playerBehavior.impulsiveResponses || 0;
    const totalResponses = playerBehavior.totalResponses || 1;
    const responseInhibition = 1 - impulsiveResponses / totalResponses;
    const focusStability = this.calculateFocusStability(playerBehavior);
    return (responseInhibition + focusStability) / 2;
  }
  calculateSustainingCapacity(sessionDuration, attentionDecline) {
    if (sessionDuration === 0) return 0;
    const sustainingScore = Math.max(0, 1 - attentionDecline);
    const durationFactor = Math.min(1, sessionDuration / 3e5);
    return sustainingScore * durationFactor;
  }
  calculateAttentionDecline(playerBehavior) {
    const accuracyOverTime = playerBehavior.accuracyOverTime || [];
    if (accuracyOverTime.length < 2) return 0;
    const firstHalf = accuracyOverTime.slice(0, Math.floor(accuracyOverTime.length / 2));
    const secondHalf = accuracyOverTime.slice(Math.floor(accuracyOverTime.length / 2));
    const firstHalfAvg = firstHalf.reduce((sum, acc) => sum + acc, 0) / firstHalf.length;
    const secondHalfAvg = secondHalf.reduce((sum, acc) => sum + acc, 0) / secondHalf.length;
    return Math.max(0, firstHalfAvg - secondHalfAvg);
  }
  detectFatigueIndications(playerBehavior) {
    const indicators = [];
    if (playerBehavior.responseTimeIncrease > 0.3) {
      indicators.push("increasing_response_time");
    }
    if (playerBehavior.accuracyDecline > 0.2) {
      indicators.push("declining_accuracy");
    }
    if (playerBehavior.clickPrecisionDecline > 0.25) {
      indicators.push("declining_precision");
    }
    return indicators;
  }
  assessMotivationLevel(playerBehavior) {
    const engagementFactors = {
      responseConsistency: playerBehavior.responseConsistency || 0,
      sessionCompletion: playerBehavior.sessionCompletion || 0,
      errorRecovery: playerBehavior.errorRecovery || 0,
      voluntaryExtension: playerBehavior.voluntaryExtension || 0
    };
    return Object.values(engagementFactors).reduce((sum, factor) => sum + factor, 0) / 4;
  }
  detectLongPauses(playerBehavior) {
    const responseTimeThreshold = 5e3;
    const responseTimes = playerBehavior.responseTimes || [];
    return responseTimes.filter((time) => time > responseTimeThreshold);
  }
  calculateDistractionSeverity(irrelevantClicks, longPauses) {
    const clickSeverity = irrelevantClicks.length * 0.3;
    const pauseSeverity = longPauses.length * 0.4;
    return Math.min(1, (clickSeverity + pauseSeverity) / 10);
  }
  calculateDirectPath(eyeMovements) {
    if (!eyeMovements || eyeMovements.length < 2) return 0;
    const start = eyeMovements[0].position;
    const end = eyeMovements[eyeMovements.length - 1].position;
    return Math.sqrt(
      Math.pow(end.x - start.x, 2) + Math.pow(end.y - start.y, 2)
    );
  }
  isSequentialPosition(pos1, pos2) {
    const threshold = 100;
    const distance = Math.sqrt(
      Math.pow(pos2.x - pos1.x, 2) + Math.pow(pos2.y - pos1.y, 2)
    );
    return distance < threshold;
  }
  /**
   * Atualiza métricas acumuladas
   */
  updateAttentionMetrics(attentionData) {
    this.attentionMetrics.focusTime.push(attentionData.focusMetrics);
    this.attentionMetrics.distractionEvents.push(attentionData.distractionEvents);
    this.attentionMetrics.visualScanningPatterns.push(attentionData.scanningPatterns);
    this.attentionMetrics.attentionSustaining.push(attentionData.attentionSustaining);
    this.attentionMetrics.selectiveAttention.push(attentionData.selectiveAttention);
  }
  /**
   * Gera dados padrão em caso de erro
   */
  getDefaultAttentionData() {
    return {
      timestamp: Date.now(),
      focusMetrics: {
        focusRatio: 0.5,
        averageFocusDuration: 1e3,
        focusStability: 0.6,
        peakFocusTime: 2e3
      },
      scanningPatterns: {
        scanningEfficiency: 0.4,
        systematicScanning: false,
        visualSearchStrategy: { strategy: "random", confidence: 0.3 },
        scanningSpeed: 0.5
      },
      selectiveAttention: {
        targetFocusAccuracy: 0.5,
        distractorResistance: 0.6,
        selectiveAccuracy: 0.45,
        attentionalControl: 0.5
      },
      attentionSustaining: {
        sustainingCapacity: 0.5,
        attentionDeclineRate: 0.2,
        fatigueIndications: [],
        motivationLevel: 0.6
      },
      distractionEvents: {
        totalDistractions: 2,
        irrelevantClicks: 1,
        attentionLapses: 1,
        distractionSeverity: 0.3
      }
    };
  }
  /**
   * Gera relatório consolidado de atenção visual
   */
  generateAttentionReport() {
    const recentMetrics = this.attentionMetrics.focusTime.slice(-10);
    if (recentMetrics.length === 0) {
      return this.getDefaultAttentionData();
    }
    return {
      averageFocusRatio: this.calculateAverage(recentMetrics.map((m) => m.focusRatio)),
      attentionTrend: this.calculateTrend(recentMetrics.map((m) => m.focusRatio)),
      overallAttentionScore: this.calculateOverallScore(),
      recommendations: this.generateRecommendations()
    };
  }
  calculateAverage(values) {
    return values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
  }
  calculateTrend(values) {
    if (values.length < 2) return "stable";
    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));
    const firstAvg = this.calculateAverage(firstHalf);
    const secondAvg = this.calculateAverage(secondHalf);
    const difference = secondAvg - firstAvg;
    if (difference > 0.1) return "improving";
    if (difference < -0.1) return "declining";
    return "stable";
  }
  calculateOverallScore() {
    const recent = this.attentionMetrics.focusTime.slice(-5);
    if (recent.length === 0) return 0.5;
    const scores = recent.map((metrics) => {
      return metrics.focusRatio * 0.3 + metrics.focusStability * 0.25 + (1 - (this.attentionMetrics.distractionEvents.slice(-1)[0]?.distractionSeverity || 0)) * 0.25 + (this.attentionMetrics.selectiveAttention.slice(-1)[0]?.attentionalControl || 0.5) * 0.2;
    });
    return this.calculateAverage(scores);
  }
  generateRecommendations() {
    const overallScore = this.calculateOverallScore();
    const recommendations = [];
    if (overallScore < 0.4) {
      recommendations.push("Considere exercícios de atenção focada");
      recommendations.push("Reduza distrações no ambiente");
    } else if (overallScore < 0.7) {
      recommendations.push("Continue praticando para melhorar a sustentação da atenção");
    } else {
      recommendations.push("Excelente controle atencional!");
      recommendations.push("Tente desafios mais complexos");
    }
    return recommendations;
  }
  /**
   * Reseta o coletor
   */
  reset() {
    this.attentionMetrics = {
      focusTime: [],
      distractionEvents: [],
      visualScanningPatterns: [],
      attentionSustaining: [],
      selectiveAttention: []
    };
  }
  /**
   * Método principal de análise requerido pelos processadores
   * @param {Object} data - Dados do jogo
   * @returns {Object} - Análise de atenção visual
   */
  async analyze(data) {
    try {
      if (!data || !data.gameData || !data.playerBehavior) {
        console.warn("VisualAttentionCollector: Dados incompletos recebidos");
        return {
          visualAttentionScore: 0.5,
          focusQuality: "medium",
          distractionLevel: "moderate",
          attentionSustaining: 0.5,
          selectiveAttention: 0.5,
          visualScanningEfficiency: 0.5
        };
      }
      const attentionData = this.collectAttentionData(data.gameData, data.playerBehavior);
      return {
        visualAttentionScore: this.calculateOverallAttentionScore(attentionData),
        focusQuality: this.assessFocusQuality(attentionData.focusMetrics),
        distractionLevel: this.assessDistractionLevel(attentionData.distractionEvents),
        attentionSustaining: this.calculateSustainingScore(attentionData.attentionSustaining),
        selectiveAttention: this.calculateSelectiveScore(attentionData.selectiveAttention),
        visualScanningEfficiency: this.calculateScanningEfficiency(attentionData.scanningPatterns)
      };
    } catch (error) {
      console.error("Erro no VisualAttentionCollector.analyze:", error);
      return {
        visualAttentionScore: 0.5,
        focusQuality: "medium",
        distractionLevel: "moderate",
        attentionSustaining: 0.5,
        selectiveAttention: 0.5,
        visualScanningEfficiency: 0.5
      };
    }
  }
  calculateOverallAttentionScore(attentionData) {
    const focusScore = attentionData.focusMetrics.averageFocusTime || 0.5;
    const scanningScore = attentionData.scanningPatterns.efficiency || 0.5;
    const selectiveScore = attentionData.selectiveAttention.accuracy || 0.5;
    return (focusScore + scanningScore + selectiveScore) / 3;
  }
  assessFocusQuality(focusMetrics) {
    const score = focusMetrics.averageFocusTime || 0.5;
    if (score >= 0.8) return "high";
    if (score >= 0.6) return "medium";
    return "low";
  }
  assessDistractionLevel(distractionEvents) {
    const eventCount = distractionEvents.length || 0;
    if (eventCount <= 2) return "low";
    if (eventCount <= 5) return "moderate";
    return "high";
  }
  calculateSustainingScore(sustainingData) {
    return sustainingData.sustainability || 0.5;
  }
  calculateSelectiveScore(selectiveData) {
    return selectiveData.accuracy || 0.5;
  }
}
class WorkingMemoryCollector {
  constructor() {
    this.collectorId = "working_memory";
    this.isActive = true;
    this.memoryMetrics = {
      capacityMeasures: [],
      retentionPatterns: [],
      processingLoad: [],
      interferenceResistance: [],
      updateMechanisms: []
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Coleta dados sobre memória de trabalho
   * @param {Object} gameData - Dados do jogo
   * @param {Object} playerBehavior - Comportamento do jogador
   * @returns {Object} - Métricas de memória de trabalho
   */
  collectWorkingMemoryData(gameData, playerBehavior) {
    try {
      const memoryData = {
        timestamp: Date.now(),
        sessionId: gameData.sessionId,
        // Capacidade da memória de trabalho
        capacityMetrics: this.assessMemoryCapacity(gameData, playerBehavior),
        // Padrões de retenção
        retentionMetrics: this.analyzeRetentionPatterns(playerBehavior),
        // Carga de processamento
        processingLoadMetrics: this.evaluateProcessingLoad(gameData, playerBehavior),
        // Resistência à interferência
        interferenceResistance: this.assessInterferenceResistance(gameData, playerBehavior),
        // Mecanismos de atualização
        updateMechanisms: this.analyzeUpdateMechanisms(playerBehavior)
      };
      this.updateMemoryMetrics(memoryData);
      return memoryData;
    } catch (error) {
      console.error("Erro na coleta de dados de memória de trabalho:", error);
      return this.getDefaultMemoryData();
    }
  }
  /**
   * Avalia capacidade da memória de trabalho
   */
  assessMemoryCapacity(gameData, playerBehavior) {
    gameData.sequenceLength || 1;
    const correctSequences = playerBehavior.correctSequences || 0;
    const totalSequences = playerBehavior.totalSequences || 1;
    return {
      maxSequenceLength: this.calculateMaxSequenceLength(playerBehavior),
      averageSpan: this.calculateAverageSpan(playerBehavior),
      spanAccuracy: correctSequences / totalSequences,
      capacityUtilization: this.calculateCapacityUtilization(gameData, playerBehavior),
      spanStability: this.calculateSpanStability(playerBehavior)
    };
  }
  /**
   * Analisa padrões de retenção
   */
  analyzeRetentionPatterns(playerBehavior) {
    const retentionIntervals = playerBehavior.retentionIntervals || [];
    const decayRates = this.calculateDecayRates(retentionIntervals);
    return {
      shortTermRetention: this.assessShortTermRetention(playerBehavior),
      decayPattern: this.analyzeDecayPattern(decayRates),
      forgettingCurve: this.calculateForgettingCurve(retentionIntervals),
      consolidationEfficiency: this.assessConsolidationEfficiency(playerBehavior),
      rehearsalStrategies: this.identifyRehearsalStrategies(playerBehavior)
    };
  }
  /**
   * Avalia carga de processamento
   */
  evaluateProcessingLoad(gameData, playerBehavior) {
    const complexity = gameData.complexity || 1;
    const processingTime = playerBehavior.processingTime || [];
    return {
      cognitiveLoad: this.calculateCognitiveLoad(complexity, processingTime),
      dualTaskPerformance: this.assessDualTaskPerformance(playerBehavior),
      resourceAllocation: this.analyzeResourceAllocation(playerBehavior),
      bottleneckEffects: this.identifyBottleneckEffects(playerBehavior),
      loadAdaptation: this.assessLoadAdaptation(playerBehavior)
    };
  }
  /**
   * Avalia resistência à interferência
   */
  assessInterferenceResistance(gameData, playerBehavior) {
    gameData.distractors || [];
    const interferenceEvents = playerBehavior.interferenceEvents || [];
    return {
      proactiveInterference: this.calculateProactiveInterference(playerBehavior),
      retroactiveInterference: this.calculateRetroactiveInterference(playerBehavior),
      inhibitoryControl: this.assessInhibitoryControl(playerBehavior),
      conflictResolution: this.analyzeConflictResolution(interferenceEvents),
      focusMaintenace: this.assessFocusMaintenance(playerBehavior)
    };
  }
  /**
   * Analisa mecanismos de atualização
   */
  analyzeUpdateMechanisms(playerBehavior) {
    const updateEvents = playerBehavior.updateEvents || [];
    return {
      updateSpeed: this.calculateUpdateSpeed(updateEvents),
      updateAccuracy: this.calculateUpdateAccuracy(updateEvents),
      flexibilityIndex: this.calculateFlexibilityIndex(playerBehavior),
      adaptiveUpdating: this.assessAdaptiveUpdating(playerBehavior),
      contextSwitching: this.analyzeContextSwitching(playerBehavior)
    };
  }
  /**
   * Métodos auxiliares de cálculo
   */
  calculateMaxSequenceLength(playerBehavior) {
    const sequences = playerBehavior.completedSequences || [];
    return sequences.length > 0 ? Math.max(...sequences.map((seq) => seq.length)) : 1;
  }
  calculateAverage(array) {
    if (!array || array.length === 0) return 0;
    return array.reduce((sum, value) => sum + value, 0) / array.length;
  }
  calculateAverageSpan(playerBehavior) {
    const spans = playerBehavior.memorySpans || [];
    return this.calculateAverage(spans) || 3;
  }
  calculateCapacityUtilization(gameData, playerBehavior) {
    const requiredCapacity = gameData.requiredCapacity || 3;
    const utilizedCapacity = playerBehavior.utilizedCapacity || 2;
    return Math.min(1, utilizedCapacity / requiredCapacity);
  }
  calculateSpanStability(playerBehavior) {
    const spans = playerBehavior.memorySpans || [];
    if (spans.length < 2) return 1;
    const mean = this.calculateAverageSpan(playerBehavior);
    const variance = this.calculateAverage(spans.map((span) => Math.pow(span - mean, 2)));
    return 1 / (1 + Math.sqrt(variance));
  }
  calculateDecayRates(retentionIntervals) {
    return retentionIntervals.map((interval) => {
      return {
        timeDelay: interval.delay,
        retentionRate: interval.accuracy,
        decayRate: 1 - interval.accuracy
      };
    });
  }
  assessShortTermRetention(playerBehavior) {
    const shortTermTests = playerBehavior.shortTermTests || [];
    if (shortTermTests.length === 0) return 0.7;
    return this.calculateAverage(shortTermTests.map((test) => test.accuracy));
  }
  analyzeDecayPattern(decayRates) {
    if (decayRates.length < 2) return "stable";
    const earlyDecay = decayRates.slice(0, Math.ceil(decayRates.length / 2));
    const lateDecay = decayRates.slice(Math.ceil(decayRates.length / 2));
    const earlyAvg = this.calculateAverage(earlyDecay.map((rate) => rate.decayRate));
    const lateAvg = this.calculateAverage(lateDecay.map((rate) => rate.decayRate));
    if (lateAvg > earlyAvg * 1.5) return "accelerating";
    if (lateAvg < earlyAvg * 0.7) return "stabilizing";
    return "linear";
  }
  calculateForgettingCurve(retentionIntervals) {
    const timePoints = retentionIntervals.map((interval) => interval.delay);
    const retentionRates = retentionIntervals.map((interval) => interval.accuracy);
    if (timePoints.length < 2) return { type: "insufficient_data", slope: 0 };
    const n = timePoints.length;
    const sumX = this.calculateAverage(timePoints) * n;
    const sumY = this.calculateAverage(retentionRates) * n;
    const sumXY = timePoints.reduce((sum, x, i) => sum + x * retentionRates[i], 0);
    const sumX2 = this.calculateAverage(timePoints.map((x) => x * x)) * n;
    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    return {
      type: slope < -0.1 ? "rapid_decay" : slope < -0.05 ? "moderate_decay" : "slow_decay",
      slope,
      halfLife: this.calculateHalfLife(slope)
    };
  }
  calculateHalfLife(slope) {
    if (slope >= 0) return Infinity;
    return Math.log(0.5) / slope;
  }
  assessConsolidationEfficiency(playerBehavior) {
    const consolidationTests = playerBehavior.consolidationTests || [];
    if (consolidationTests.length === 0) return 0.6;
    const consolidationScores = consolidationTests.map((test) => {
      const immediateRecall = test.immediateRecall || 0.5;
      const delayedRecall = test.delayedRecall || 0.3;
      return delayedRecall / immediateRecall;
    });
    return this.calculateAverage(consolidationScores);
  }
  identifyRehearsalStrategies(playerBehavior) {
    const rehearsalBehaviors = playerBehavior.rehearsalBehaviors || [];
    const strategies = {
      maintenance: 0,
      elaborative: 0,
      organizational: 0,
      imagery: 0
    };
    rehearsalBehaviors.forEach((behavior) => {
      switch (behavior.type) {
        case "repetition":
        case "verbal_rehearsal":
          strategies.maintenance++;
          break;
        case "association":
        case "elaboration":
          strategies.elaborative++;
          break;
        case "grouping":
        case "categorization":
          strategies.organizational++;
          break;
        case "visual_imagery":
        case "spatial_coding":
          strategies.imagery++;
          break;
      }
    });
    const total = Object.values(strategies).reduce((sum, count) => sum + count, 0);
    const dominantStrategy = Object.keys(strategies).reduce(
      (a, b) => strategies[a] > strategies[b] ? a : b
    );
    return {
      dominantStrategy,
      strategyDistribution: total > 0 ? Object.fromEntries(
        Object.entries(strategies).map(([key, value]) => [key, value / total])
      ) : strategies,
      strategyEffectiveness: this.calculateStrategyEffectiveness(playerBehavior, dominantStrategy)
    };
  }
  calculateCognitiveLoad(complexity, processingTimes) {
    if (processingTimes.length === 0) return complexity * 0.5;
    const averageProcessingTime = processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length;
    const baselineTime = 1e3;
    const timeRatio = averageProcessingTime / baselineTime;
    const complexityFactor = Math.log(complexity + 1);
    return Math.min(1, timeRatio * complexityFactor / 3);
  }
  assessDualTaskPerformance(playerBehavior) {
    const singleTaskPerformance = playerBehavior.singleTaskAccuracy || 0.8;
    const dualTaskPerformance = playerBehavior.dualTaskAccuracy || 0.6;
    return {
      dualTaskCost: singleTaskPerformance - dualTaskPerformance,
      resourceSharingEfficiency: dualTaskPerformance / singleTaskPerformance,
      taskPrioritization: this.analyzeTaskPrioritization(playerBehavior)
    };
  }
  analyzeResourceAllocation(playerBehavior) {
    const resourceUsage = playerBehavior.resourceUsage || {};
    return {
      executiveAllocation: resourceUsage.executive || 0.4,
      phonologicalAllocation: resourceUsage.phonological || 0.3,
      visuospatialAllocation: resourceUsage.visuospatial || 0.3,
      allocationEfficiency: this.calculateAllocationEfficiency(resourceUsage),
      adaptiveAllocation: this.assessAdaptiveAllocation(playerBehavior)
    };
  }
  identifyBottleneckEffects(playerBehavior) {
    const processingStages = playerBehavior.processingStages || [];
    const bottlenecks = [];
    processingStages.forEach((stage, index) => {
      if (stage.duration > stage.expectedDuration * 1.5) {
        bottlenecks.push({
          stage: stage.name,
          severity: stage.duration / stage.expectedDuration,
          impact: this.calculateBottleneckImpact(stage, processingStages)
        });
      }
    });
    return {
      bottleneckCount: bottlenecks.length,
      primaryBottleneck: bottlenecks.length > 0 ? bottlenecks[0] : null,
      overallImpact: bottlenecks.reduce((sum, bottleneck) => sum + bottleneck.impact, 0),
      resolutionStrategies: this.suggestBottleneckResolution(bottlenecks)
    };
  }
  assessLoadAdaptation(playerBehavior) {
    const loadLevels = playerBehavior.loadLevels || [];
    const performanceAtLoad = playerBehavior.performanceAtLoad || [];
    if (loadLevels.length < 2) return { adaptability: 0.5, strategy: "unknown" };
    const adaptabilityScores = loadLevels.map((load, index) => {
      const expectedPerformance = 1 - load * 0.2;
      const actualPerformance = performanceAtLoad[index] || 0.5;
      return Math.max(0, actualPerformance / expectedPerformance);
    });
    const averageAdaptability = adaptabilityScores.reduce((sum, score) => sum + score, 0) / adaptabilityScores.length;
    return {
      adaptability: averageAdaptability,
      strategy: this.identifyAdaptationStrategy(playerBehavior),
      loadTolerance: Math.max(...performanceAtLoad.map((perf, i) => perf > 0.7 ? loadLevels[i] : 0))
    };
  }
  calculateProactiveInterference(playerBehavior) {
    const priorLearning = playerBehavior.priorLearning || [];
    playerBehavior.currentAccuracy || 0.7;
    if (priorLearning.length === 0) return 0;
    const conflictingItems = priorLearning.filter(
      (item) => item.similarity > 0.7 && item.interference > 0.3
    );
    const interferenceEffect = conflictingItems.reduce(
      (sum, item) => sum + item.similarity * item.interference,
      0
    );
    return Math.min(1, interferenceEffect / priorLearning.length);
  }
  calculateRetroactiveInterference(playerBehavior) {
    const newLearning = playerBehavior.newLearning || [];
    const oldMemoryRetention = playerBehavior.oldMemoryRetention || 0.8;
    if (newLearning.length === 0) return 0;
    const baselineRetention = 0.9;
    const interferenceStrength = newLearning.reduce(
      (sum, item) => sum + item.similarity * item.recency,
      0
    ) / newLearning.length;
    return Math.max(0, baselineRetention - oldMemoryRetention) * interferenceStrength;
  }
  assessInhibitoryControl(playerBehavior) {
    const inhibitionTasks = playerBehavior.inhibitionTasks || [];
    if (inhibitionTasks.length === 0) {
      return {
        overallControl: 0.6,
        prepotentResponseInhibition: 0.6,
        resistanceToMemoryIntrusion: 0.6,
        deletionEfficiency: 0.6
      };
    }
    const controlScores = inhibitionTasks.map((task) => {
      return {
        prepotent: task.prepotentInhibition || 0.6,
        intrusion: task.intrusionResistance || 0.6,
        deletion: task.deletionAccuracy || 0.6
      };
    });
    return {
      overallControl: this.calculateAverageControl(controlScores),
      prepotentResponseInhibition: this.calculateAverage(controlScores.map((s) => s.prepotent)),
      resistanceToMemoryIntrusion: this.calculateAverage(controlScores.map((s) => s.intrusion)),
      deletionEfficiency: this.calculateAverage(controlScores.map((s) => s.deletion))
    };
  }
  analyzeConflictResolution(interferenceEvents) {
    if (interferenceEvents.length === 0) {
      return {
        resolutionSpeed: 1e3,
        resolutionAccuracy: 0.7,
        conflictDetection: 0.6,
        resolutionStrategy: "unknown"
      };
    }
    const resolutionTimes = interferenceEvents.map((event) => event.resolutionTime);
    const resolutionAccuracies = interferenceEvents.map((event) => event.resolutionAccuracy);
    return {
      resolutionSpeed: this.calculateAverage(resolutionTimes),
      resolutionAccuracy: this.calculateAverage(resolutionAccuracies),
      conflictDetection: this.assessConflictDetection(interferenceEvents),
      resolutionStrategy: this.identifyResolutionStrategy(interferenceEvents)
    };
  }
  assessFocusMaintenance(playerBehavior) {
    const focusEvents = playerBehavior.focusEvents || [];
    const distractionResistance = playerBehavior.distractionResistance || 0.6;
    return {
      sustainedFocus: this.calculateSustainedFocus(focusEvents),
      distractionResistance,
      focusRecovery: this.calculateFocusRecovery(focusEvents),
      attentionalStability: this.calculateAttentionalStability(focusEvents)
    };
  }
  calculateUpdateSpeed(updateEvents) {
    if (updateEvents.length === 0) return 1e3;
    const updateTimes = updateEvents.map((event) => event.updateTime);
    return this.calculateAverage(updateTimes);
  }
  calculateUpdateAccuracy(updateEvents) {
    if (updateEvents.length === 0) return 0.7;
    const accuracies = updateEvents.map((event) => event.accuracy);
    return this.calculateAverage(accuracies);
  }
  calculateFlexibilityIndex(playerBehavior) {
    const taskSwitches = playerBehavior.taskSwitches || [];
    const switchCosts = playerBehavior.switchCosts || [];
    if (taskSwitches.length === 0) return 0.6;
    const switchAccuracy = taskSwitches.reduce((sum, sw) => sum + sw.accuracy, 0) / taskSwitches.length;
    const averageSwitchCost = switchCosts.length > 0 ? switchCosts.reduce((sum, cost) => sum + cost, 0) / switchCosts.length : 0.3;
    return switchAccuracy * (1 - Math.min(0.5, averageSwitchCost));
  }
  assessAdaptiveUpdating(playerBehavior) {
    const updateContexts = playerBehavior.updateContexts || [];
    return {
      contextSensitivity: this.calculateContextSensitivity(updateContexts),
      updateEfficiency: this.calculateUpdateEfficiency(updateContexts),
      adaptiveStrategy: this.identifyAdaptiveStrategy(updateContexts)
    };
  }
  analyzeContextSwitching(playerBehavior) {
    const contextSwitches = playerBehavior.contextSwitches || [];
    return {
      switchFrequency: contextSwitches.length,
      switchEfficiency: this.calculateSwitchEfficiency(contextSwitches),
      contextMaintenance: this.assessContextMaintenance(contextSwitches),
      switchStrategy: this.identifySwitchStrategy(contextSwitches)
    };
  }
  /**
   * Métodos auxiliares adicionais
   */
  calculateStrategyEffectiveness(playerBehavior, strategy) {
    const strategyPerformance = playerBehavior.strategyPerformance || {};
    return strategyPerformance[strategy] || 0.6;
  }
  analyzeTaskPrioritization(playerBehavior) {
    const taskPriorities = playerBehavior.taskPriorities || [];
    return taskPriorities.length > 0 ? taskPriorities[0] : "primary";
  }
  calculateAllocationEfficiency(resourceUsage) {
    const total = Object.values(resourceUsage).reduce((sum, usage) => sum + usage, 0);
    const ideal = 1;
    return 1 - Math.abs(total - ideal);
  }
  assessAdaptiveAllocation(playerBehavior) {
    return playerBehavior.adaptiveAllocation || 0.6;
  }
  calculateBottleneckImpact(stage, allStages) {
    const totalDuration = allStages.reduce((sum, s) => sum + s.duration, 0);
    return stage.duration / totalDuration;
  }
  suggestBottleneckResolution(bottlenecks) {
    return bottlenecks.map((bottleneck) => ({
      stage: bottleneck.stage,
      suggestion: this.getResolutionSuggestion(bottleneck.stage)
    }));
  }
  getResolutionSuggestion(stageName) {
    const suggestions = {
      "encoding": "Melhorar estratégias de codificação",
      "maintenance": "Fortalecer rehearsal mechanisms",
      "retrieval": "Desenvolver pistas de recuperação",
      "updating": "Praticar flexibilidade cognitiva"
    };
    return suggestions[stageName] || "Análise adicional necessária";
  }
  identifyAdaptationStrategy(playerBehavior) {
    const strategies = playerBehavior.adaptationStrategies || [];
    if (strategies.length === 0) return "reactive";
    const strategyCounts = strategies.reduce((counts, strategy) => {
      counts[strategy] = (counts[strategy] || 0) + 1;
      return counts;
    }, {});
    return Object.keys(strategyCounts).reduce(
      (a, b) => strategyCounts[a] > strategyCounts[b] ? a : b
    );
  }
  calculateAverageControl(controlScores) {
    const allScores = controlScores.flatMap((score) => Object.values(score));
    return this.calculateAverage(allScores);
  }
  assessConflictDetection(interferenceEvents) {
    const detectionTimes = interferenceEvents.map((event) => event.detectionTime);
    const averageDetectionTime = this.calculateAverage(detectionTimes);
    return Math.max(0, 1 - averageDetectionTime / 3e3);
  }
  identifyResolutionStrategy(interferenceEvents) {
    const strategies = interferenceEvents.map((event) => event.strategy);
    const strategyCounts = strategies.reduce((counts, strategy) => {
      counts[strategy] = (counts[strategy] || 0) + 1;
      return counts;
    }, {});
    return Object.keys(strategyCounts).reduce(
      (a, b) => strategyCounts[a] > strategyCounts[b] ? a : b
    ) || "suppression";
  }
  calculateSustainedFocus(focusEvents) {
    if (focusEvents.length === 0) return 0.6;
    const focusDurations = focusEvents.map((event) => event.duration);
    const totalFocusTime = focusDurations.reduce((sum, duration) => sum + duration, 0);
    const sessionDuration = focusEvents[focusEvents.length - 1].timestamp - focusEvents[0].timestamp;
    return totalFocusTime / (sessionDuration || 1);
  }
  calculateFocusRecovery(focusEvents) {
    const recoveryEvents = focusEvents.filter((event) => event.type === "recovery");
    if (recoveryEvents.length === 0) return 0.6;
    const recoveryTimes = recoveryEvents.map((event) => event.recoveryTime);
    const averageRecoveryTime = this.calculateAverage(recoveryTimes);
    return Math.max(0, 1 - averageRecoveryTime / 5e3);
  }
  calculateAttentionalStability(focusEvents) {
    if (focusEvents.length < 2) return 0.6;
    const focusLevels = focusEvents.map((event) => event.focusLevel);
    const mean = this.calculateAverage(focusLevels);
    const variance = focusLevels.reduce((sum, level) => sum + Math.pow(level - mean, 2), 0) / focusLevels.length;
    return 1 / (1 + Math.sqrt(variance));
  }
  calculateContextSensitivity(updateContexts) {
    if (updateContexts.length === 0) return 0.6;
    const appropriateUpdates = updateContexts.filter((context) => context.appropriate);
    return appropriateUpdates.length / updateContexts.length;
  }
  calculateUpdateEfficiency(updateContexts) {
    if (updateContexts.length === 0) return 0.6;
    const efficiencyScores = updateContexts.map((context) => context.efficiency);
    return this.calculateAverage(efficiencyScores);
  }
  identifyAdaptiveStrategy(updateContexts) {
    if (!updateContexts || updateContexts.length === 0) return "reactive";
    const strategies = updateContexts.map((context) => context.strategy);
    const strategyCounts = strategies.reduce((counts, strategy) => {
      counts[strategy] = (counts[strategy] || 0) + 1;
      return counts;
    }, {});
    if (Object.keys(strategyCounts).length === 0) return "reactive";
    return Object.keys(strategyCounts).reduce(
      (a, b) => strategyCounts[a] > strategyCounts[b] ? a : b
    ) || "reactive";
  }
  calculateSwitchEfficiency(contextSwitches) {
    if (contextSwitches.length === 0) return 0.6;
    const efficiencyScores = contextSwitches.map((sw) => sw.efficiency);
    return this.calculateAverage(efficiencyScores);
  }
  assessContextMaintenance(contextSwitches) {
    if (contextSwitches.length === 0) return 0.6;
    const maintenanceScores = contextSwitches.map((sw) => sw.maintenance);
    return this.calculateAverage(maintenanceScores);
  }
  identifySwitchStrategy(contextSwitches) {
    if (contextSwitches.length === 0) return "sequential";
    const strategies = contextSwitches.map((sw) => sw.strategy);
    const strategyCounts = strategies.reduce((counts, strategy) => {
      counts[strategy] = (counts[strategy] || 0) + 1;
      return counts;
    }, {});
    const strategyKeys = Object.keys(strategyCounts);
    if (strategyKeys.length === 0) return "sequential";
    return strategyKeys.reduce(
      (a, b) => strategyCounts[a] > strategyCounts[b] ? a : b
    );
  }
  /**
   * Atualiza métricas acumuladas
   */
  updateMemoryMetrics(memoryData) {
    this.memoryMetrics.capacityMeasures.push(memoryData.capacityMetrics);
    this.memoryMetrics.retentionPatterns.push(memoryData.retentionMetrics);
    this.memoryMetrics.processingLoad.push(memoryData.processingLoadMetrics);
    this.memoryMetrics.interferenceResistance.push(memoryData.interferenceResistance);
    this.memoryMetrics.updateMechanisms.push(memoryData.updateMechanisms);
  }
  /**
   * Gera dados padrão em caso de erro
   */
  getDefaultMemoryData() {
    return {
      timestamp: Date.now(),
      capacityMetrics: {
        maxSequenceLength: 5,
        averageSpan: 4,
        spanAccuracy: 0.7,
        capacityUtilization: 0.6,
        spanStability: 0.8
      },
      retentionMetrics: {
        shortTermRetention: 0.8,
        decayPattern: "linear",
        forgettingCurve: { type: "moderate_decay", slope: -0.08, halfLife: 8.66 },
        consolidationEfficiency: 0.6,
        rehearsalStrategies: {
          dominantStrategy: "maintenance",
          strategyDistribution: { maintenance: 0.4, elaborative: 0.3, organizational: 0.2, imagery: 0.1 },
          strategyEffectiveness: 0.6
        }
      },
      processingLoadMetrics: {
        cognitiveLoad: 0.5,
        dualTaskPerformance: { dualTaskCost: 0.2, resourceSharingEfficiency: 0.8, taskPrioritization: "primary" },
        resourceAllocation: {
          executiveAllocation: 0.4,
          phonologicalAllocation: 0.3,
          visuospatialAllocation: 0.3,
          allocationEfficiency: 0.9,
          adaptiveAllocation: 0.6
        },
        bottleneckEffects: { bottleneckCount: 1, primaryBottleneck: null, overallImpact: 0.3, resolutionStrategies: [] },
        loadAdaptation: { adaptability: 0.6, strategy: "reactive", loadTolerance: 3 }
      },
      interferenceResistance: {
        proactiveInterference: 0.3,
        retroactiveInterference: 0.25,
        inhibitoryControl: { overallControl: 0.6, prepotentResponseInhibition: 0.6, resistanceToMemoryIntrusion: 0.6, deletionEfficiency: 0.6 },
        conflictResolution: { resolutionSpeed: 1500, resolutionAccuracy: 0.7, conflictDetection: 0.6, resolutionStrategy: "suppression" },
        focusMaintenace: { sustainedFocus: 0.6, distractionResistance: 0.6, focusRecovery: 0.7, attentionalStability: 0.6 }
      },
      updateMechanisms: {
        updateSpeed: 1200,
        updateAccuracy: 0.75,
        flexibilityIndex: 0.6,
        adaptiveUpdating: { contextSensitivity: 0.6, updateEfficiency: 0.6, adaptiveStrategy: "reactive" },
        contextSwitching: { switchFrequency: 5, switchEfficiency: 0.6, contextMaintenance: 0.6, switchStrategy: "sequential" }
      }
    };
  }
  /**
   * Gera relatório consolidado de memória de trabalho
   */
  generateMemoryReport() {
    const recentMetrics = this.memoryMetrics.capacityMeasures.slice(-10);
    if (recentMetrics.length === 0) {
      return this.getDefaultMemoryData();
    }
    return {
      averageCapacity: this.calculateAverage(recentMetrics.map((m) => m.averageSpan)),
      memoryTrend: this.calculateTrend(recentMetrics.map((m) => m.spanAccuracy)),
      overallMemoryScore: this.calculateOverallMemoryScore(),
      recommendations: this.generateMemoryRecommendations()
    };
  }
  calculateTrend(values) {
    if (values.length < 2) return "stable";
    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));
    const firstAvg = this.calculateAverage(firstHalf);
    const secondAvg = this.calculateAverage(secondHalf);
    const difference = secondAvg - firstAvg;
    if (difference > 0.1) return "improving";
    if (difference < -0.1) return "declining";
    return "stable";
  }
  generateMemoryRecommendations() {
    const overallScore = this.calculateOverallMemoryScore();
    const recommendations = [];
    if (overallScore < 0.4) {
      recommendations.push("Pratique exercícios de span de memória");
      recommendations.push("Desenvolva estratégias de rehearsal");
    } else if (overallScore < 0.7) {
      recommendations.push("Continue treinando a capacidade de memória de trabalho");
      recommendations.push("Foque em reduzir interferências");
    } else {
      recommendations.push("Excelente capacidade de memória de trabalho!");
      recommendations.push("Tente desafios de dual-task mais complexos");
    }
    return recommendations;
  }
  /**
   * Reseta o coletor
   */
  reset() {
    this.memoryMetrics = {
      capacityMeasures: [],
      retentionPatterns: [],
      processingLoad: [],
      interferenceResistance: [],
      updateMechanisms: []
    };
  }
  /**
   * Método principal de análise requerido pelos processadores
   * @param {Object} data - Dados do jogo
   * @returns {Object} - Análise de memória de trabalho
   */
  async analyze(data) {
    try {
      if (!data || !data.gameData || !data.playerBehavior) {
        console.warn("WorkingMemoryCollector: Dados incompletos recebidos");
        return {
          workingMemoryCapacity: 0.5,
          retentionQuality: "medium",
          processingEfficiency: 0.5,
          interferenceResistance: 0.5,
          updateMechanismEfficiency: 0.5,
          overallMemoryScore: 0.5
        };
      }
      const memoryData = this.collectWorkingMemoryData(data.gameData, data.playerBehavior);
      return {
        workingMemoryCapacity: this.calculateCapacityScore(memoryData.capacityMetrics),
        retentionQuality: this.assessRetentionQuality(memoryData.retentionMetrics),
        processingEfficiency: this.calculateProcessingEfficiency(memoryData.processingLoadMetrics),
        interferenceResistance: this.calculateInterferenceResistance(memoryData.interferenceResistance),
        updateMechanismEfficiency: this.calculateUpdateEfficiency(memoryData.updateMechanisms),
        overallMemoryScore: this.calculateOverallMemoryScore(memoryData)
      };
    } catch (error) {
      console.error("Erro no WorkingMemoryCollector.analyze:", error);
      return {
        workingMemoryCapacity: 0.5,
        retentionQuality: "medium",
        processingEfficiency: 0.5,
        interferenceResistance: 0.5,
        updateMechanismEfficiency: 0.5,
        overallMemoryScore: 0.5
      };
    }
  }
  calculateCapacityScore(capacityMetrics) {
    return capacityMetrics.averageCapacity || 0.5;
  }
  assessRetentionQuality(retentionMetrics) {
    const score = retentionMetrics.retentionRate || 0.5;
    if (score >= 0.8) return "high";
    if (score >= 0.6) return "medium";
    return "low";
  }
  calculateProcessingEfficiency(processingMetrics) {
    return processingMetrics.efficiency || 0.5;
  }
  calculateInterferenceResistance(interferenceMetrics) {
    return interferenceMetrics.resistance || 0.5;
  }
  calculateOverallMemoryScore(memoryData) {
    const capacity = this.calculateCapacityScore(memoryData.capacityMetrics);
    const processing = this.calculateProcessingEfficiency(memoryData.processingLoadMetrics);
    const interference = this.calculateInterferenceResistance(memoryData.interferenceResistance);
    const update = this.calculateUpdateEfficiency(memoryData.updateMechanisms);
    return (capacity + processing + interference + update) / 4;
  }
}
class SequentialProcessingCollector {
  constructor() {
    this.collectorId = "sequential_processing";
    this.isActive = true;
    this.sequentialMetrics = {
      sequenceProcessing: [],
      temporalOrder: [],
      serialPosition: [],
      sequentialMemory: [],
      processingSpeed: []
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(gameData) {
    return this.collectSequentialData(gameData, gameData);
  }
  /**
   * Coleta dados sobre processamento sequencial
   * @param {Object} gameData - Dados do jogo
   * @param {Object} playerBehavior - Comportamento do jogador
   * @returns {Object} - Métricas de processamento sequencial
   */
  collectSequentialData(gameData, playerBehavior) {
    try {
      const sequentialData = {
        timestamp: Date.now(),
        sessionId: gameData.sessionId,
        // Processamento de sequências
        sequenceProcessing: this.analyzeSequenceProcessing(gameData, playerBehavior),
        // Ordem temporal
        temporalOrder: this.analyzeTemporalOrder(playerBehavior),
        // Efeitos de posição serial
        serialPosition: this.analyzeSerialPositionEffects(playerBehavior),
        // Memória sequencial
        sequentialMemory: this.analyzeSequentialMemory(playerBehavior),
        // Velocidade de processamento
        processingSpeed: this.analyzeProcessingSpeed(playerBehavior)
      };
      this.updateSequentialMetrics(sequentialData);
      return sequentialData;
    } catch (error) {
      console.error("Erro na coleta de dados de processamento sequencial:", error);
      return this.getDefaultSequentialData();
    }
  }
  /**
   * Analisa processamento de sequências
   */
  analyzeSequenceProcessing(gameData, playerBehavior) {
    const sequences = gameData.sequences || [];
    const processingResults = playerBehavior.sequenceResults || [];
    return {
      sequenceAccuracy: this.calculateSequenceAccuracy(processingResults),
      sequenceCompletionRate: this.calculateCompletionRate(processingResults),
      sequenceLengthEffect: this.analyzeSequenceLengthEffect(sequences, processingResults),
      complexityHandling: this.analyzeComplexityHandling(sequences, processingResults),
      sequencePatternRecognition: this.analyzePatternRecognition(sequences, processingResults)
    };
  }
  /**
   * Analisa ordem temporal
   */
  analyzeTemporalOrder(playerBehavior) {
    const temporalTasks = playerBehavior.temporalTasks || [];
    const orderRecall = playerBehavior.orderRecall || [];
    return {
      temporalOrderAccuracy: this.calculateTemporalAccuracy(orderRecall),
      chronologicalProcessing: this.analyzeChronologicalProcessing(temporalTasks),
      temporalResolution: this.calculateTemporalResolution(playerBehavior),
      sequentialBinding: this.analyzeSequentialBinding(playerBehavior),
      temporalGrouping: this.analyzeTemporalGrouping(playerBehavior)
    };
  }
  /**
   * Analisa efeitos de posição serial
   */
  analyzeSerialPositionEffects(playerBehavior) {
    const positionData = playerBehavior.positionData || [];
    return {
      primacyEffect: this.calculatePrimacyEffect(positionData),
      recencyEffect: this.calculateRecencyEffect(positionData),
      middlePositionPerformance: this.calculateMiddlePositionPerformance(positionData),
      positionCurve: this.generatePositionCurve(positionData),
      optimalSequenceLength: this.calculateOptimalSequenceLength(positionData)
    };
  }
  /**
   * Analisa memória sequencial
   */
  analyzeSequentialMemory(playerBehavior) {
    const memoryTasks = playerBehavior.sequentialMemoryTasks || [];
    return {
      forwardSpan: this.calculateForwardSpan(memoryTasks),
      backwardSpan: this.calculateBackwardSpan(memoryTasks),
      sequentialRetention: this.analyzeSequentialRetention(memoryTasks),
      orderMemoryStrength: this.calculateOrderMemoryStrength(memoryTasks),
      sequentialReconstructionAbility: this.analyzeReconstructionAbility(memoryTasks)
    };
  }
  /**
   * Analisa velocidade de processamento
   */
  analyzeProcessingSpeed(playerBehavior) {
    const processingTimes = playerBehavior.processingTimes || [];
    playerBehavior.reactionTimes || [];
    return {
      averageProcessingSpeed: this.calculateAverageProcessingSpeed(processingTimes),
      processingSpeedVariability: this.calculateSpeedVariability(processingTimes),
      speedAccuracyTradeoff: this.analyzeSpeedAccuracyTradeoff(playerBehavior),
      sequentialSpeedUp: this.calculateSequentialSpeedUp(processingTimes),
      adaptiveSpeedControl: this.analyzeAdaptiveSpeedControl(playerBehavior)
    };
  }
  /**
   * Métodos auxiliares de cálculo
   */
  calculateSequenceAccuracy(processingResults) {
    if (processingResults.length === 0) return 0.7;
    const correctSequences = processingResults.filter((result) => result.isCorrect);
    return correctSequences.length / processingResults.length;
  }
  calculateCompletionRate(processingResults) {
    if (processingResults.length === 0) return 0.8;
    const completedSequences = processingResults.filter((result) => result.isCompleted);
    return completedSequences.length / processingResults.length;
  }
  analyzeSequenceLengthEffect(sequences, processingResults) {
    const lengthEffects = {};
    sequences.forEach((sequence, index) => {
      const length = sequence.length;
      const result = processingResults[index];
      if (!lengthEffects[length]) {
        lengthEffects[length] = { total: 0, correct: 0 };
      }
      lengthEffects[length].total++;
      if (result && result.isCorrect) {
        lengthEffects[length].correct++;
      }
    });
    const lengthAccuracies = Object.entries(lengthEffects).map(([length, data]) => ({
      length: parseInt(length),
      accuracy: data.correct / data.total
    }));
    return {
      lengthAccuracies,
      optimalLength: this.findOptimalLength(lengthAccuracies),
      lengthImpact: this.calculateLengthImpact(lengthAccuracies)
    };
  }
  analyzeComplexityHandling(sequences, processingResults) {
    const complexityLevels = sequences.map((seq) => this.calculateSequenceComplexity(seq));
    const complexityResults = complexityLevels.map((complexity, index) => ({
      complexity,
      result: processingResults[index]
    }));
    return {
      complexityTolerance: this.calculateComplexityTolerance(complexityResults),
      adaptiveComplexity: this.analyzeAdaptiveComplexity(complexityResults),
      complexityThreshold: this.calculateComplexityThreshold(complexityResults)
    };
  }
  analyzePatternRecognition(sequences, processingResults) {
    const patterns = this.identifySequencePatterns(sequences);
    const patternRecognition = this.assessPatternRecognition(patterns, processingResults);
    return {
      patternSensitivity: patternRecognition.sensitivity,
      patternTypes: patternRecognition.recognizedPatterns,
      implicitLearning: this.assessImplicitLearning(patterns, processingResults),
      patternTransfer: this.assessPatternTransfer(patterns, processingResults)
    };
  }
  calculateTemporalAccuracy(orderRecall) {
    if (orderRecall.length === 0) return 0.7;
    const correctOrders = orderRecall.filter((recall) => recall.isCorrectOrder);
    return correctOrders.length / orderRecall.length;
  }
  analyzeChronologicalProcessing(temporalTasks) {
    if (temporalTasks.length === 0) return { accuracy: 0.7, strategy: "unknown" };
    const chronologicalAccuracy = temporalTasks.reduce((sum, task) => sum + (task.chronologicalAccuracy || 0), 0) / temporalTasks.length;
    const strategy = this.identifyChronologicalStrategy(temporalTasks);
    return {
      accuracy: chronologicalAccuracy,
      strategy,
      temporalResolutionThreshold: this.calculateTemporalThreshold(temporalTasks)
    };
  }
  calculateTemporalResolution(playerBehavior) {
    const temporalDiscrimination = playerBehavior.temporalDiscrimination || [];
    if (temporalDiscrimination.length === 0) return 100;
    const resolutions = temporalDiscrimination.map((disc) => disc.minimumInterval);
    return Math.min(...resolutions);
  }
  analyzeSequentialBinding(playerBehavior) {
    const bindingTasks = playerBehavior.sequentialBindingTasks || [];
    if (bindingTasks.length === 0) {
      return {
        bindingStrength: 0.6,
        associativeAccuracy: 0.7,
        bindingSpeed: 1500
      };
    }
    return {
      bindingStrength: this.calculateBindingStrength(bindingTasks),
      associativeAccuracy: this.calculateAssociativeAccuracy(bindingTasks),
      bindingSpeed: this.calculateBindingSpeed(bindingTasks)
    };
  }
  analyzeTemporalGrouping(playerBehavior) {
    const groupingBehavior = playerBehavior.temporalGrouping || [];
    return {
      groupingStrategy: this.identifyGroupingStrategy(groupingBehavior),
      optimalGroupSize: this.calculateOptimalGroupSize(groupingBehavior),
      groupingEfficiency: this.calculateGroupingEfficiency(groupingBehavior),
      adaptiveGrouping: this.assessAdaptiveGrouping(groupingBehavior)
    };
  }
  calculatePrimacyEffect(positionData) {
    if (positionData.length === 0) return 0.8;
    const firstThirdPositions = positionData.filter(
      (_, index) => index < positionData.length / 3
    );
    const primacyAccuracy = firstThirdPositions.reduce((sum, pos) => sum + (pos.accuracy || 0), 0) / firstThirdPositions.length;
    const overallAccuracy = positionData.reduce((sum, pos) => sum + (pos.accuracy || 0), 0) / positionData.length;
    return primacyAccuracy - overallAccuracy;
  }
  calculateRecencyEffect(positionData) {
    if (positionData.length === 0) return 0.6;
    const lastThirdPositions = positionData.filter(
      (_, index) => index >= positionData.length * 2 / 3
    );
    const recencyAccuracy = lastThirdPositions.reduce((sum, pos) => sum + (pos.accuracy || 0), 0) / lastThirdPositions.length;
    const overallAccuracy = positionData.reduce((sum, pos) => sum + (pos.accuracy || 0), 0) / positionData.length;
    return recencyAccuracy - overallAccuracy;
  }
  calculateMiddlePositionPerformance(positionData) {
    if (positionData.length === 0) return 0.5;
    const middleThirdStart = Math.floor(positionData.length / 3);
    const middleThirdEnd = Math.floor(positionData.length * 2 / 3);
    const middlePositions = positionData.slice(middleThirdStart, middleThirdEnd);
    return middlePositions.reduce((sum, pos) => sum + (pos.accuracy || 0), 0) / middlePositions.length;
  }
  generatePositionCurve(positionData) {
    return positionData.map((pos, index) => ({
      position: index + 1,
      accuracy: pos.accuracy || 0.5,
      responseTime: pos.responseTime || 1e3
    }));
  }
  calculateOptimalSequenceLength(positionData) {
    const lengthPerformance = {};
    positionData.forEach((pos, index) => {
      const sequenceLength = pos.sequenceLength || positionData.length;
      if (!lengthPerformance[sequenceLength]) {
        lengthPerformance[sequenceLength] = { total: 0, accuracy: 0 };
      }
      lengthPerformance[sequenceLength].total++;
      lengthPerformance[sequenceLength].accuracy += pos.accuracy || 0;
    });
    let optimalLength = 5;
    let bestAccuracy = 0;
    Object.entries(lengthPerformance).forEach(([length, data]) => {
      const avgAccuracy = data.accuracy / data.total;
      if (avgAccuracy > bestAccuracy) {
        bestAccuracy = avgAccuracy;
        optimalLength = parseInt(length);
      }
    });
    return optimalLength;
  }
  calculateForwardSpan(memoryTasks) {
    const forwardTasks = memoryTasks.filter((task) => task.direction === "forward");
    if (forwardTasks.length === 0) return 5;
    const spans = forwardTasks.map((task) => task.span);
    return Math.max(...spans);
  }
  calculateBackwardSpan(memoryTasks) {
    const backwardTasks = memoryTasks.filter((task) => task.direction === "backward");
    if (backwardTasks.length === 0) return 4;
    const spans = backwardTasks.map((task) => task.span);
    return Math.max(...spans);
  }
  analyzeSequentialRetention(memoryTasks) {
    if (memoryTasks.length === 0) return { shortTerm: 0.8, longTerm: 0.6 };
    const shortTermTasks = memoryTasks.filter((task) => task.delay < 5e3);
    const longTermTasks = memoryTasks.filter((task) => task.delay >= 5e3);
    const shortTermRetention = shortTermTasks.length > 0 ? shortTermTasks.reduce((sum, task) => sum + task.accuracy, 0) / shortTermTasks.length : 0.8;
    const longTermRetention = longTermTasks.length > 0 ? longTermTasks.reduce((sum, task) => sum + task.accuracy, 0) / longTermTasks.length : 0.6;
    return {
      shortTerm: shortTermRetention,
      longTerm: longTermRetention,
      retentionDecay: shortTermRetention - longTermRetention
    };
  }
  calculateOrderMemoryStrength(memoryTasks) {
    const orderTasks = memoryTasks.filter((task) => task.type === "order");
    if (orderTasks.length === 0) return 0.7;
    return orderTasks.reduce((sum, task) => sum + task.accuracy, 0) / orderTasks.length;
  }
  analyzeReconstructionAbility(memoryTasks) {
    const reconstructionTasks = memoryTasks.filter((task) => task.type === "reconstruction");
    if (reconstructionTasks.length === 0) return { accuracy: 0.6, strategy: "unknown" };
    const accuracy = reconstructionTasks.reduce((sum, task) => sum + task.accuracy, 0) / reconstructionTasks.length;
    const strategy = this.identifyReconstructionStrategy(reconstructionTasks);
    return {
      accuracy,
      strategy,
      reconstructionSpeed: this.calculateReconstructionSpeed(reconstructionTasks)
    };
  }
  calculateAverageProcessingSpeed(processingTimes) {
    if (processingTimes.length === 0) return 1e3;
    return processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length;
  }
  calculateSpeedVariability(processingTimes) {
    if (processingTimes.length < 2) return 0;
    const mean = this.calculateAverageProcessingSpeed(processingTimes);
    const variance = processingTimes.reduce((sum, time) => sum + Math.pow(time - mean, 2), 0) / processingTimes.length;
    return Math.sqrt(variance);
  }
  analyzeSpeedAccuracyTradeoff(playerBehavior) {
    const speedAccuracyPairs = playerBehavior.speedAccuracyPairs || [];
    if (speedAccuracyPairs.length === 0) return { correlation: 0, strategy: "balanced" };
    const speeds = speedAccuracyPairs.map((pair) => pair.speed);
    const accuracies = speedAccuracyPairs.map((pair) => pair.accuracy);
    const correlation = this.calculateCorrelation(speeds, accuracies);
    const strategy = correlation < -0.3 ? "speed_emphasis" : correlation > 0.3 ? "accuracy_emphasis" : "balanced";
    return {
      correlation,
      strategy,
      tradeoffEfficiency: this.calculateTradeoffEfficiency(speedAccuracyPairs)
    };
  }
  calculateSequentialSpeedUp(processingTimes) {
    if (processingTimes.length < 2) return 0;
    const firstHalf = processingTimes.slice(0, Math.floor(processingTimes.length / 2));
    const secondHalf = processingTimes.slice(Math.floor(processingTimes.length / 2));
    const firstHalfAvg = firstHalf.reduce((sum, time) => sum + time, 0) / firstHalf.length;
    const secondHalfAvg = secondHalf.reduce((sum, time) => sum + time, 0) / secondHalf.length;
    return (firstHalfAvg - secondHalfAvg) / firstHalfAvg;
  }
  analyzeAdaptiveSpeedControl(playerBehavior) {
    const speedAdjustments = playerBehavior.speedAdjustments || [];
    return {
      adaptiveCapacity: this.calculateAdaptiveCapacity(speedAdjustments),
      controlStrategy: this.identifySpeedControlStrategy(speedAdjustments),
      responsiveness: this.calculateSpeedResponsiveness(speedAdjustments)
    };
  }
  /**
   * Métodos auxiliares adicionais
   */
  findOptimalLength(lengthAccuracies) {
    let bestLength = 5;
    let bestAccuracy = 0;
    lengthAccuracies.forEach(({ length, accuracy }) => {
      if (accuracy > bestAccuracy) {
        bestAccuracy = accuracy;
        bestLength = length;
      }
    });
    return bestLength;
  }
  calculateLengthImpact(lengthAccuracies) {
    if (lengthAccuracies.length < 2) return 0.5;
    const accuracies = lengthAccuracies.map((la) => la.accuracy);
    const maxAccuracy = Math.max(...accuracies);
    const minAccuracy = Math.min(...accuracies);
    return maxAccuracy - minAccuracy;
  }
  calculateSequenceComplexity(sequence) {
    let complexity = sequence.length * 0.1;
    const uniqueElements = [...new Set(sequence)].length;
    complexity += (1 - uniqueElements / sequence.length) * 0.5;
    const patternComplexity = this.calculatePatternComplexity(sequence);
    complexity += patternComplexity * 0.4;
    return Math.min(1, complexity);
  }
  calculatePatternComplexity(sequence) {
    let complexity = 0;
    for (let i = 1; i < sequence.length; i++) {
      if (sequence[i] === sequence[i - 1]) {
        complexity += 0.1;
      }
    }
    let alternatingPattern = true;
    for (let i = 2; i < sequence.length; i++) {
      if (sequence[i] !== sequence[i - 2]) {
        alternatingPattern = false;
        break;
      }
    }
    if (alternatingPattern) complexity += 0.3;
    return Math.min(1, complexity);
  }
  calculateComplexityTolerance(complexityResults) {
    if (complexityResults.length === 0) return 0.5;
    const highComplexityTasks = complexityResults.filter((cr) => cr.complexity > 0.7);
    const lowComplexityTasks = complexityResults.filter((cr) => cr.complexity < 0.3);
    if (highComplexityTasks.length === 0) return 0.3;
    if (lowComplexityTasks.length === 0) return 0.8;
    const highComplexityAccuracy = highComplexityTasks.reduce((sum, cr) => sum + (cr.result?.accuracy || 0), 0) / highComplexityTasks.length;
    const lowComplexityAccuracy = lowComplexityTasks.reduce((sum, cr) => sum + (cr.result?.accuracy || 0), 0) / lowComplexityTasks.length;
    return highComplexityAccuracy / (lowComplexityAccuracy || 0.1);
  }
  analyzeAdaptiveComplexity(complexityResults) {
    const adaptationIndicators = {
      improvedOverTime: this.checkComplexityImprovement(complexityResults),
      strategicAdjustment: this.checkStrategicAdjustment(complexityResults),
      optimalComplexityLevel: this.findOptimalComplexityLevel(complexityResults)
    };
    return adaptationIndicators;
  }
  calculateComplexityThreshold(complexityResults) {
    const sortedResults = complexityResults.sort((a, b) => a.complexity - b.complexity);
    for (let i = 1; i < sortedResults.length; i++) {
      const currentAccuracy = sortedResults[i].result?.accuracy || 0;
      const previousAccuracy = sortedResults[i - 1].result?.accuracy || 0;
      if (previousAccuracy - currentAccuracy > 0.2) {
        return sortedResults[i].complexity;
      }
    }
    return 0.8;
  }
  identifySequencePatterns(sequences) {
    const patterns = {
      repetitive: [],
      alternating: [],
      ascending: [],
      descending: [],
      random: []
    };
    sequences.forEach((sequence, index) => {
      const patternType = this.classifySequencePattern(sequence);
      patterns[patternType].push(index);
    });
    return patterns;
  }
  classifySequencePattern(sequence) {
    if (this.isRepetitivePattern(sequence)) return "repetitive";
    if (this.isAlternatingPattern(sequence)) return "alternating";
    if (this.isAscendingPattern(sequence)) return "ascending";
    if (this.isDescendingPattern(sequence)) return "descending";
    return "random";
  }
  isRepetitivePattern(sequence) {
    return sequence.every((element) => element === sequence[0]);
  }
  isAlternatingPattern(sequence) {
    if (sequence.length < 3) return false;
    for (let i = 2; i < sequence.length; i++) {
      if (sequence[i] !== sequence[i - 2]) return false;
    }
    return true;
  }
  isAscendingPattern(sequence) {
    for (let i = 1; i < sequence.length; i++) {
      if (sequence[i] <= sequence[i - 1]) return false;
    }
    return true;
  }
  isDescendingPattern(sequence) {
    for (let i = 1; i < sequence.length; i++) {
      if (sequence[i] >= sequence[i - 1]) return false;
    }
    return true;
  }
  assessPatternRecognition(patterns, processingResults) {
    const recognizedPatterns = [];
    let totalSensitivity = 0;
    Object.entries(patterns).forEach(([patternType, indices]) => {
      if (indices.length > 0) {
        const patternAccuracy = indices.reduce((sum, index) => sum + (processingResults[index]?.accuracy || 0), 0) / indices.length;
        if (patternAccuracy > 0.7) {
          recognizedPatterns.push(patternType);
        }
        totalSensitivity += patternAccuracy;
      }
    });
    return {
      sensitivity: totalSensitivity / Object.keys(patterns).length,
      recognizedPatterns
    };
  }
  assessImplicitLearning(patterns, processingResults) {
    const learningScores = {};
    Object.entries(patterns).forEach(([patternType, indices]) => {
      if (indices.length > 1) {
        const accuracies = indices.map((index) => processingResults[index]?.accuracy || 0);
        const improvement = accuracies[accuracies.length - 1] - accuracies[0];
        learningScores[patternType] = improvement;
      }
    });
    const averageImprovement = Object.values(learningScores).reduce((sum, score) => sum + score, 0) / Object.values(learningScores).length || 0;
    return {
      implicitLearningScore: averageImprovement,
      learningByPattern: learningScores
    };
  }
  assessPatternTransfer(patterns, processingResults) {
    return {
      transferScore: 0.6,
      // Placeholder - implementar lógica mais sofisticada
      transferPatterns: []
    };
  }
  identifyChronologicalStrategy(temporalTasks) {
    const strategies = temporalTasks.map((task) => task.strategy);
    const strategyCounts = strategies.reduce((counts, strategy) => {
      counts[strategy] = (counts[strategy] || 0) + 1;
      return counts;
    }, {});
    return Object.keys(strategyCounts).reduce(
      (a, b) => strategyCounts[a] > strategyCounts[b] ? a : b
    ) || "sequential";
  }
  calculateTemporalThreshold(temporalTasks) {
    const thresholds = temporalTasks.map((task) => task.temporalThreshold);
    return thresholds.reduce((sum, threshold) => sum + threshold, 0) / thresholds.length || 150;
  }
  calculateBindingStrength(bindingTasks) {
    return bindingTasks.reduce((sum, task) => sum + task.bindingStrength, 0) / bindingTasks.length;
  }
  calculateAssociativeAccuracy(bindingTasks) {
    return bindingTasks.reduce((sum, task) => sum + task.associativeAccuracy, 0) / bindingTasks.length;
  }
  calculateBindingSpeed(bindingTasks) {
    const speeds = bindingTasks.map((task) => task.bindingSpeed);
    return speeds.reduce((sum, speed) => sum + speed, 0) / speeds.length;
  }
  identifyGroupingStrategy(groupingBehavior) {
    if (groupingBehavior.length === 0) return "sequential";
    const strategies = groupingBehavior.map((gb) => gb.strategy);
    const strategyCounts = strategies.reduce((counts, strategy) => {
      counts[strategy] = (counts[strategy] || 0) + 1;
      return counts;
    }, {});
    return Object.keys(strategyCounts).reduce(
      (a, b) => strategyCounts[a] > strategyCounts[b] ? a : b
    );
  }
  calculateOptimalGroupSize(groupingBehavior) {
    if (groupingBehavior.length === 0) return 3;
    const groupSizes = groupingBehavior.map((gb) => gb.groupSize);
    const sizePerformance = {};
    groupSizes.forEach((size, index) => {
      if (!sizePerformance[size]) {
        sizePerformance[size] = { total: 0, accuracy: 0 };
      }
      sizePerformance[size].total++;
      sizePerformance[size].accuracy += groupingBehavior[index].accuracy || 0;
    });
    let optimalSize = 3;
    let bestAccuracy = 0;
    Object.entries(sizePerformance).forEach(([size, data]) => {
      const avgAccuracy = data.accuracy / data.total;
      if (avgAccuracy > bestAccuracy) {
        bestAccuracy = avgAccuracy;
        optimalSize = parseInt(size);
      }
    });
    return optimalSize;
  }
  calculateGroupingEfficiency(groupingBehavior) {
    if (groupingBehavior.length === 0) return 0.6;
    return groupingBehavior.reduce((sum, gb) => sum + gb.efficiency, 0) / groupingBehavior.length;
  }
  assessAdaptiveGrouping(groupingBehavior) {
    return {
      adaptiveCapacity: 0.6,
      // Placeholder
      adaptationStrategy: "reactive"
    };
  }
  identifyReconstructionStrategy(reconstructionTasks) {
    const strategies = reconstructionTasks.map((task) => task.strategy);
    const strategyCounts = strategies.reduce((counts, strategy) => {
      counts[strategy] = (counts[strategy] || 0) + 1;
      return counts;
    }, {});
    return Object.keys(strategyCounts).reduce(
      (a, b) => strategyCounts[a] > strategyCounts[b] ? a : b
    ) || "serial";
  }
  calculateReconstructionSpeed(reconstructionTasks) {
    const speeds = reconstructionTasks.map((task) => task.reconstructionTime);
    return speeds.reduce((sum, speed) => sum + speed, 0) / speeds.length || 2e3;
  }
  calculateCorrelation(x, y) {
    if (x.length !== y.length || x.length === 0) return 0;
    const n = x.length;
    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
    const sumX2 = x.reduce((sum, val) => sum + val * val, 0);
    const sumY2 = y.reduce((sum, val) => sum + val * val, 0);
    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));
    return denominator === 0 ? 0 : numerator / denominator;
  }
  calculateTradeoffEfficiency(speedAccuracyPairs) {
    return speedAccuracyPairs.reduce((sum, pair) => {
      const normalizedSpeed = 1 / (pair.speed / 1e3);
      const efficiency = (pair.accuracy + normalizedSpeed) / 2;
      return sum + efficiency;
    }, 0) / speedAccuracyPairs.length || 0.6;
  }
  calculateAdaptiveCapacity(speedAdjustments) {
    if (speedAdjustments.length === 0) return 0.6;
    const appropriateAdjustments = speedAdjustments.filter((adj) => adj.appropriate);
    return appropriateAdjustments.length / speedAdjustments.length;
  }
  identifySpeedControlStrategy(speedAdjustments) {
    if (!speedAdjustments || speedAdjustments.length === 0) return "reactive";
    const strategies = speedAdjustments.map((adj) => adj.strategy);
    const strategyCounts = strategies.reduce((counts, strategy) => {
      counts[strategy] = (counts[strategy] || 0) + 1;
      return counts;
    }, {});
    if (Object.keys(strategyCounts).length === 0) return "reactive";
    return Object.keys(strategyCounts).reduce(
      (a, b) => strategyCounts[a] > strategyCounts[b] ? a : b
    );
  }
  calculateSpeedResponsiveness(speedAdjustments) {
    if (!speedAdjustments || speedAdjustments.length === 0) return 1e3;
    const responseTimes = speedAdjustments.map((adj) => adj.responseTime);
    return this.calculateAverage(responseTimes);
  }
  checkComplexityImprovement(complexityResults) {
    const highComplexityTasks = complexityResults.filter((cr) => cr.complexity > 0.6);
    if (highComplexityTasks.length < 3) return false;
    const firstThird = highComplexityTasks.slice(0, Math.floor(highComplexityTasks.length / 3));
    const lastThird = highComplexityTasks.slice(-Math.floor(highComplexityTasks.length / 3));
    const firstAccuracy = firstThird.reduce((sum, cr) => sum + (cr.result?.accuracy || 0), 0) / firstThird.length;
    const lastAccuracy = lastThird.reduce((sum, cr) => sum + (cr.result?.accuracy || 0), 0) / lastThird.length;
    return lastAccuracy > firstAccuracy + 0.1;
  }
  checkStrategicAdjustment(complexityResults) {
    return complexityResults.some((cr) => cr.result?.strategyChange === true);
  }
  findOptimalComplexityLevel(complexityResults) {
    const complexityBins = {};
    complexityResults.forEach((cr) => {
      const bin = Math.floor(cr.complexity * 10) / 10;
      if (!complexityBins[bin]) {
        complexityBins[bin] = { total: 0, accuracy: 0 };
      }
      complexityBins[bin].total++;
      complexityBins[bin].accuracy += cr.result?.accuracy || 0;
    });
    let optimalComplexity = 0.5;
    let bestAccuracy = 0;
    Object.entries(complexityBins).forEach(([complexity, data]) => {
      const avgAccuracy = data.accuracy / data.total;
      if (avgAccuracy > bestAccuracy) {
        bestAccuracy = avgAccuracy;
        optimalComplexity = parseFloat(complexity);
      }
    });
    return optimalComplexity;
  }
  /**
   * Atualiza métricas acumuladas
   */
  updateSequentialMetrics(sequentialData) {
    this.sequentialMetrics.sequenceProcessing.push(sequentialData.sequenceProcessing);
    this.sequentialMetrics.temporalOrder.push(sequentialData.temporalOrder);
    this.sequentialMetrics.serialPosition.push(sequentialData.serialPosition);
    this.sequentialMetrics.sequentialMemory.push(sequentialData.sequentialMemory);
    this.sequentialMetrics.processingSpeed.push(sequentialData.processingSpeed);
  }
  /**
   * Gera dados padrão em caso de erro
   */
  getDefaultSequentialData() {
    return {
      timestamp: Date.now(),
      sequenceProcessing: {
        sequenceAccuracy: 0.7,
        sequenceCompletionRate: 0.8,
        sequenceLengthEffect: {
          lengthAccuracies: [
            { length: 3, accuracy: 0.9 },
            { length: 5, accuracy: 0.7 },
            { length: 7, accuracy: 0.5 }
          ],
          optimalLength: 5,
          lengthImpact: 0.4
        },
        complexityHandling: {
          complexityTolerance: 0.6,
          adaptiveComplexity: {
            improvedOverTime: true,
            strategicAdjustment: false,
            optimalComplexityLevel: 0.5
          },
          complexityThreshold: 0.7
        },
        sequencePatternRecognition: {
          patternSensitivity: 0.6,
          patternTypes: ["repetitive", "alternating"],
          implicitLearning: { implicitLearningScore: 0.2, learningByPattern: {} },
          patternTransfer: { transferScore: 0.6, transferPatterns: [] }
        }
      },
      temporalOrder: {
        temporalOrderAccuracy: 0.7,
        chronologicalProcessing: { accuracy: 0.7, strategy: "sequential", temporalResolutionThreshold: 150 },
        temporalResolution: 100,
        sequentialBinding: { bindingStrength: 0.6, associativeAccuracy: 0.7, bindingSpeed: 1500 },
        temporalGrouping: {
          groupingStrategy: "sequential",
          optimalGroupSize: 3,
          groupingEfficiency: 0.6,
          adaptiveGrouping: { adaptiveCapacity: 0.6, adaptationStrategy: "reactive" }
        }
      },
      serialPosition: {
        primacyEffect: 0.2,
        recencyEffect: 0.15,
        middlePositionPerformance: 0.5,
        positionCurve: [],
        optimalSequenceLength: 5
      },
      sequentialMemory: {
        forwardSpan: 5,
        backwardSpan: 4,
        sequentialRetention: { shortTerm: 0.8, longTerm: 0.6, retentionDecay: 0.2 },
        orderMemoryStrength: 0.7,
        sequentialReconstructionAbility: { accuracy: 0.6, strategy: "serial", reconstructionSpeed: 2e3 }
      },
      processingSpeed: {
        averageProcessingSpeed: 1e3,
        processingSpeedVariability: 200,
        speedAccuracyTradeoff: { correlation: -0.2, strategy: "balanced", tradeoffEfficiency: 0.6 },
        sequentialSpeedUp: 0.1,
        adaptiveSpeedControl: { adaptiveCapacity: 0.6, controlStrategy: "reactive", responsiveness: 1e3 }
      }
    };
  }
  /**
   * Gera relatório consolidado de processamento sequencial
   */
  generateSequentialReport() {
    const recentMetrics = this.sequentialMetrics.sequenceProcessing.slice(-10);
    if (recentMetrics.length === 0) {
      return this.getDefaultSequentialData();
    }
    return {
      averageSequenceAccuracy: this.calculateAverage(recentMetrics.map((m) => m.sequenceAccuracy)),
      sequentialTrend: this.calculateTrend(recentMetrics.map((m) => m.sequenceAccuracy)),
      overallSequentialScore: this.calculateOverallSequentialScore(),
      recommendations: this.generateSequentialRecommendations()
    };
  }
  calculateAverage(values) {
    return values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
  }
  calculateTrend(values) {
    if (values.length < 2) return "stable";
    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));
    const firstAvg = this.calculateAverage(firstHalf);
    const secondAvg = this.calculateAverage(secondHalf);
    const difference = secondAvg - firstAvg;
    if (difference > 0.1) return "improving";
    if (difference < -0.1) return "declining";
    return "stable";
  }
  calculateOverallSequentialScore() {
    const recent = this.sequentialMetrics.sequenceProcessing.slice(-5);
    if (recent.length === 0) return 0.6;
    const scores = recent.map((metrics) => {
      const temporalMetrics = this.sequentialMetrics.temporalOrder.slice(-1)[0];
      const memoryMetrics = this.sequentialMetrics.sequentialMemory.slice(-1)[0];
      const speedMetrics = this.sequentialMetrics.processingSpeed.slice(-1)[0];
      return metrics.sequenceAccuracy * 0.3 + (temporalMetrics?.temporalOrderAccuracy || 0.7) * 0.25 + (memoryMetrics?.orderMemoryStrength || 0.7) * 0.25 + (1 - (speedMetrics?.processingSpeedVariability || 200) / 1e3) * 0.2;
    });
    return this.calculateAverage(scores);
  }
  generateSequentialRecommendations() {
    const overallScore = this.calculateOverallSequentialScore();
    const recommendations = [];
    if (overallScore < 0.4) {
      recommendations.push("Pratique exercícios de sequência simples primeiro");
      recommendations.push("Foque em melhorar a memória de trabalho sequencial");
    } else if (overallScore < 0.7) {
      recommendations.push("Continue treinando processamento sequencial");
      recommendations.push("Trabalhe com padrões mais complexos");
    } else {
      recommendations.push("Excelente processamento sequencial!");
      recommendations.push("Tente desafios de múltiplas sequências simultâneas");
    }
    return recommendations;
  }
  /**
   * Reseta o coletor
   */
  reset() {
    this.sequentialMetrics = {
      sequenceProcessing: [],
      temporalOrder: [],
      serialPosition: [],
      sequentialMemory: [],
      processingSpeed: []
    };
  }
}
class LetterSelectionCollector {
  constructor() {
    this.name = "LetterSelectionCollector";
    this.version = "3.0.0";
    this.isActive = true;
    this.collectedData = [];
  }
  /**
   * 🎯 Coleta dados específicos da seleção de letras
   */
  async collect(data) {
    try {
      const timestamp = (/* @__PURE__ */ new Date()).toISOString();
      const analysisData = {
        // Dados básicos
        sessionId: data.sessionId,
        userId: data.userId,
        timestamp,
        activityType: "letter_selection",
        // Dados da interação
        targetLetter: data.targetLetter,
        selectedLetter: data.selectedLetter,
        optionsShown: data.optionsShown || [],
        isCorrect: data.selectedLetter === data.targetLetter,
        responseTime: data.responseTime || 0,
        // Análise de complexidade visual
        visualComplexity: this.analyzeVisualComplexity(data),
        letterSimilarity: this.analyzeLetterSimilarity(data.targetLetter, data.selectedLetter),
        optionComplexity: this.analyzeOptionComplexity(data.optionsShown),
        // Métricas comportamentais
        hesitationTime: data.behavioralMetrics?.hesitationTime || 0,
        cursorMovements: data.behavioralMetrics?.cursorMovements || 0,
        clickAccuracy: data.behavioralMetrics?.clickAccuracy || 1,
        focusLoss: data.behavioralMetrics?.focusLoss || 0,
        // Análise cognitiva
        cognitiveLoad: this.assessCognitiveLoad(data),
        attentionLevel: this.assessAttentionLevel(data),
        processingStrategy: this.identifyProcessingStrategy(data),
        // Padrões de erro
        errorType: this.classifyError(data.targetLetter, data.selectedLetter),
        errorSeverity: this.assessErrorSeverity(data),
        // Dados educacionais
        letterKnowledge: this.assessLetterKnowledge(data),
        visualProcessing: this.assessVisualProcessing(data),
        developmentStage: this.assessDevelopmentStage(data)
      };
      this.collectedData.push(analysisData);
      return analysisData;
    } catch (error) {
      console.error("Erro no LetterSelectionCollector:", error);
      return null;
    }
  }
  /**
   * 🎨 Analisa complexidade visual da tarefa
   */
  analyzeVisualComplexity(data) {
    let complexity = 0.3;
    const options = data.optionsShown || [];
    if (options.length > 3) complexity += 0.2;
    const similarities = this.calculateOptionSimilarities(options, data.targetLetter);
    const avgSimilarity = similarities.reduce((a, b) => a + b, 0) / similarities.length;
    complexity += avgSimilarity * 0.4;
    return Math.min(1, complexity);
  }
  /**
   * 🔍 Analisa similaridade entre letras
   */
  analyzeLetterSimilarity(target, selected) {
    if (!target || !selected) return 0;
    const similarGroups = [
      ["b", "d", "p", "q"],
      ["m", "n", "h", "r"],
      ["c", "o", "e"],
      ["v", "w", "y"],
      ["i", "l", "j", "1"],
      ["f", "t"],
      ["u", "n"],
      ["s", "z"],
      ["a", "o"]
    ];
    const targetLower = target.toLowerCase();
    const selectedLower = selected.toLowerCase();
    for (const group of similarGroups) {
      if (group.includes(targetLower) && group.includes(selectedLower)) {
        return 0.8;
      }
    }
    return 0.1;
  }
  /**
   * 📊 Analisa complexidade das opções
   */
  analyzeOptionComplexity(options) {
    if (!options || options.length === 0) return 0.5;
    let complexity = 0.2;
    complexity += (options.length - 2) * 0.1;
    const hasUppercase = options.some((opt) => opt === opt.toUpperCase());
    const hasLowercase = options.some((opt) => opt === opt.toLowerCase());
    if (hasUppercase && hasLowercase) complexity += 0.2;
    return Math.min(1, complexity);
  }
  /**
   * 🧠 Avalia carga cognitiva
   */
  assessCognitiveLoad(data) {
    let load = 0.4;
    const responseTime = data.responseTime || 0;
    if (responseTime > 3e3) load += 0.2;
    if (responseTime > 5e3) load += 0.2;
    if (responseTime < 1e3) load -= 0.1;
    const hesitation = data.behavioralMetrics?.hesitationTime || 0;
    if (hesitation > 1e3) load += 0.2;
    if (hesitation > 2e3) load += 0.2;
    const movements = data.behavioralMetrics?.cursorMovements || 0;
    load += Math.min(0.3, movements * 0.1);
    return Math.min(1, load);
  }
  /**
   * 👁️ Avalia nível de atenção
   */
  assessAttentionLevel(data) {
    let attention = 0.7;
    const focusLoss = data.behavioralMetrics?.focusLoss || 0;
    attention -= focusLoss * 0.2;
    const clickAccuracy = data.behavioralMetrics?.clickAccuracy || 1;
    attention = (attention + clickAccuracy) / 2;
    const responseTime = data.responseTime || 0;
    if (responseTime > 7e3) attention -= 0.2;
    return Math.max(0, Math.min(1, attention));
  }
  /**
   * 🎯 Identifica estratégia de processamento
   */
  identifyProcessingStrategy(data) {
    const strategies = [];
    const responseTime = data.responseTime || 0;
    const hesitation = data.behavioralMetrics?.hesitationTime || 0;
    const movements = data.behavioralMetrics?.cursorMovements || 0;
    if (responseTime < 1500) strategies.push("impulsive");
    if (responseTime > 4e3) strategies.push("deliberative");
    if (hesitation > 2e3) strategies.push("hesitant");
    if (movements === 0) strategies.push("decisive");
    if (movements > 3) strategies.push("exploratory");
    if (movements === 1) strategies.push("targeted");
    const isCorrect = data.selectedLetter === data.targetLetter;
    if (isCorrect && responseTime < 2e3) strategies.push("confident");
    if (!isCorrect && responseTime > 3e3) strategies.push("struggling");
    return strategies.length > 0 ? strategies : ["neutral"];
  }
  /**
   * ❌ Classifica tipo de erro
   */
  classifyError(target, selected) {
    if (!target || !selected || target === selected) return "no_error";
    const similarity = this.analyzeLetterSimilarity(target, selected);
    if (similarity > 0.7) {
      if (this.isLetterReversal(target, selected)) {
        return "reversal_error";
      }
      return "visual_similarity_error";
    }
    if (this.isPhoneticError(target, selected)) {
      return "phonetic_error";
    }
    if (this.isPositionalError(target, selected)) {
      return "positional_error";
    }
    return "random_error";
  }
  /**
   * 🔄 Verifica se é reversão de letra
   */
  isLetterReversal(target, selected) {
    const reversalPairs = [
      ["b", "d"],
      ["d", "b"],
      ["p", "q"],
      ["q", "p"],
      ["u", "n"],
      ["n", "u"],
      ["m", "w"],
      ["w", "m"]
    ];
    const targetLower = target.toLowerCase();
    const selectedLower = selected.toLowerCase();
    return reversalPairs.some(
      (pair) => pair[0] === targetLower && pair[1] === selectedLower
    );
  }
  /**
   * 🔤 Verifica se é erro fonético
   */
  isPhoneticError(target, selected) {
    const phoneticGroups = [
      ["b", "p", "d", "t", "g", "k"],
      // Plosivas
      ["f", "v", "s", "z"],
      // Fricativas
      ["m", "n"],
      // Nasais
      ["l", "r"],
      // Líquidas
      ["a", "e", "i", "o", "u"]
      // Vogais
    ];
    const targetLower = target.toLowerCase();
    const selectedLower = selected.toLowerCase();
    return phoneticGroups.some(
      (group) => group.includes(targetLower) && group.includes(selectedLower)
    );
  }
  /**
   * 📍 Verifica se é erro posicional
   */
  isPositionalError(target, selected) {
    const targetPos = target.toLowerCase().charCodeAt(0) - 97;
    const selectedPos = selected.toLowerCase().charCodeAt(0) - 97;
    return Math.abs(targetPos - selectedPos) <= 2;
  }
  /**
   * ⚠️ Avalia severidade do erro
   */
  assessErrorSeverity(data) {
    if (data.selectedLetter === data.targetLetter) return 0;
    let severity = 0.5;
    const similarity = this.analyzeLetterSimilarity(data.targetLetter, data.selectedLetter);
    if (similarity > 0.7) severity = 0.3;
    else if (similarity > 0.4) severity = 0.6;
    else severity = 0.9;
    if (data.responseTime < 1e3) severity += 0.2;
    return Math.min(1, severity);
  }
  /**
   * 📚 Avalia conhecimento da letra
   */
  assessLetterKnowledge(data) {
    let knowledge = 0.5;
    if (data.selectedLetter === data.targetLetter) {
      knowledge = 0.8;
      if (data.responseTime < 2e3) knowledge = 0.95;
      if (data.responseTime < 1e3) knowledge = 1;
    } else {
      const errorType = this.classifyError(data.targetLetter, data.selectedLetter);
      if (errorType === "visual_similarity_error") knowledge = 0.4;
      else if (errorType === "phonetic_error") knowledge = 0.3;
      else if (errorType === "reversal_error") knowledge = 0.2;
      else knowledge = 0.1;
    }
    return knowledge;
  }
  /**
   * 👀 Avalia processamento visual
   */
  assessVisualProcessing(data) {
    let processing = 0.6;
    const responseTime = data.responseTime || 0;
    const movements = data.behavioralMetrics?.cursorMovements || 0;
    if (responseTime < 2e3) processing += 0.2;
    else if (responseTime > 4e3) processing -= 0.2;
    if (movements <= 1) processing += 0.2;
    else if (movements > 3) processing -= 0.2;
    const isCorrect = data.selectedLetter === data.targetLetter;
    if (isCorrect) processing += 0.2;
    return Math.max(0, Math.min(1, processing));
  }
  /**
   * 📈 Avalia estágio de desenvolvimento
   */
  assessDevelopmentStage(data) {
    const knowledge = this.assessLetterKnowledge(data);
    const processing = this.assessVisualProcessing(data);
    const cognitiveLoad = this.assessCognitiveLoad(data);
    const overall = (knowledge + processing + (1 - cognitiveLoad)) / 3;
    if (overall >= 0.8) return "advanced";
    if (overall >= 0.6) return "intermediate";
    if (overall >= 0.4) return "developing";
    return "emerging";
  }
  /**
   * 🔢 Calcula similaridades entre opções
   */
  calculateOptionSimilarities(options, target) {
    if (!options || !target) return [0];
    return options.map(
      (option) => this.analyzeLetterSimilarity(target, option)
    );
  }
  /**
   * 📊 Gera resumo da coleta
   */
  generateSummary() {
    if (this.collectedData.length === 0) return null;
    const totalCollections = this.collectedData.length;
    const correctAnswers = this.collectedData.filter((d) => d.isCorrect).length;
    const accuracy = correctAnswers / totalCollections;
    const avgResponseTime = this.collectedData.reduce((sum, d) => sum + d.responseTime, 0) / totalCollections;
    const avgCognitiveLoad = this.collectedData.reduce((sum, d) => sum + d.cognitiveLoad, 0) / totalCollections;
    const avgAttention = this.collectedData.reduce((sum, d) => sum + d.attentionLevel, 0) / totalCollections;
    const errorTypes = {};
    this.collectedData.forEach((d) => {
      if (d.errorType !== "no_error") {
        errorTypes[d.errorType] = (errorTypes[d.errorType] || 0) + 1;
      }
    });
    return {
      collector: this.name,
      version: this.version,
      totalCollections,
      accuracy,
      averageResponseTime: avgResponseTime,
      averageCognitiveLoad: avgCognitiveLoad,
      averageAttentionLevel: avgAttention,
      errorPatterns: errorTypes,
      developmentInsights: this.analyzeDevelopmentProgress(),
      recommendations: this.generateRecommendations()
    };
  }
  /**
   * 📈 Analisa progresso de desenvolvimento
   */
  analyzeDevelopmentProgress() {
    if (this.collectedData.length < 3) return null;
    const recentData = this.collectedData.slice(-5);
    const earlierData = this.collectedData.slice(0, 5);
    const recentAccuracy = recentData.length > 0 ? recentData.filter((d) => d.isCorrect).length / recentData.length : 0;
    const earlierAccuracy = earlierData.length > 0 ? earlierData.filter((d) => d.isCorrect).length / earlierData.length : 0;
    const improvement = recentAccuracy - earlierAccuracy;
    return {
      trend: improvement > 0.1 ? "improving" : improvement < -0.1 ? "declining" : "stable",
      improvementRate: improvement,
      currentLevel: recentAccuracy,
      consistency: this.calculateConsistency(recentData)
    };
  }
  /**
   * 📏 Calcula consistência
   */
  calculateConsistency(data) {
    if (data.length < 2) return 1;
    const accuracyValues = data.map((d) => d.isCorrect ? 1 : 0);
    const mean = accuracyValues.reduce((a, b) => a + b) / accuracyValues.length;
    const variance = accuracyValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / accuracyValues.length;
    return 1 - Math.sqrt(variance);
  }
  /**
   * 💡 Gera recomendações
   */
  generateRecommendations() {
    const summary = this.generateSummary();
    if (!summary) return [];
    const recommendations = [];
    if (summary.accuracy < 0.6) {
      recommendations.push({
        type: "accuracy_improvement",
        priority: "high",
        message: "Foque em precisão antes de velocidade. Pratique com letras isoladas."
      });
    }
    if (summary.averageResponseTime > 4e3) {
      recommendations.push({
        type: "speed_improvement",
        priority: "medium",
        message: "Pratique reconhecimento rápido com exercícios de tempo limitado."
      });
    }
    const errorTypes = summary.errorPatterns;
    if (errorTypes.reversal_error > 2) {
      recommendations.push({
        type: "reversal_training",
        priority: "high",
        message: "Pratique distinção entre letras espelhadas (b/d, p/q)."
      });
    }
    if (errorTypes.visual_similarity_error > 3) {
      recommendations.push({
        type: "visual_discrimination",
        priority: "medium",
        message: "Trabalhe discriminação visual com exercícios específicos."
      });
    }
    if (summary.averageCognitiveLoad > 0.7) {
      recommendations.push({
        type: "cognitive_load",
        priority: "medium",
        message: "Reduza complexidade temporariamente para consolidar aprendizado."
      });
    }
    return recommendations;
  }
  /**
   * 🔄 Reset do coletor
   */
  reset() {
    this.collectedData = [];
  }
  /**
   * 📥 Exporta dados coletados
   */
  exportData() {
    return {
      collector: this.name,
      version: this.version,
      collectedAt: (/* @__PURE__ */ new Date()).toISOString(),
      data: this.collectedData,
      summary: this.generateSummary()
    };
  }
}
class WordFormationCollector {
  constructor() {
    this.name = "WordFormationCollector";
    this.version = "3.0.0";
    this.isActive = true;
    this.collectedData = [];
  }
  /**
   * 🎯 Coleta dados específicos da formação de palavras
   */
  async collect(data) {
    try {
      const timestamp = (/* @__PURE__ */ new Date()).toISOString();
      const analysisData = {
        // Dados básicos
        sessionId: data.sessionId,
        userId: data.userId,
        timestamp,
        activityType: "word_formation",
        // Dados da tarefa
        targetWord: data.targetWord,
        formedWord: data.formedWord,
        letterSequence: data.letterSequence || [],
        isCorrect: data.formedWord === data.targetWord,
        responseTime: data.responseTime || 0,
        // Métricas de construção
        wordBuildingTime: data.behavioralMetrics?.wordBuildingTime || data.responseTime,
        letterSequenceErrors: data.behavioralMetrics?.letterSequenceErrors || 0,
        backtrackCount: data.behavioralMetrics?.backtrackCount || 0,
        completionTime: data.behavioralMetrics?.completionTime || data.responseTime,
        planningTime: data.behavioralMetrics?.planningTime || 0,
        // Análise da palavra
        wordComplexity: this.analyzeWordComplexity(data.targetWord),
        sequenceAccuracy: this.calculateSequenceAccuracy(data.targetWord, data.formedWord),
        constructionStrategy: this.analyzeConstructionStrategy(data),
        // Análise cognitiva
        executiveFunction: this.assessExecutiveFunction(data),
        workingMemory: this.assessWorkingMemory(data),
        sequentialPlanning: this.assessSequentialPlanning(data),
        // Habilidades linguísticas
        orthographicKnowledge: this.assessOrthographicKnowledge(data),
        phoneticMapping: this.assessPhoneticMapping(data),
        wordRecognition: this.assessWordRecognition(data),
        // Padrões de erro
        errorType: this.classifyConstructionError(data),
        errorPosition: this.analyzeErrorPosition(data.targetWord, data.formedWord),
        // Desenvolvimento
        constructionSkillLevel: this.assessConstructionSkillLevel(data),
        learningStrategy: this.identifyLearningStrategy(data)
      };
      this.collectedData.push(analysisData);
      return analysisData;
    } catch (error) {
      console.error("Erro no WordFormationCollector:", error);
      return null;
    }
  }
  /**
   * 📚 Analisa complexidade da palavra
   */
  analyzeWordComplexity(word) {
    if (!word) return 0.5;
    let complexity = 0.2;
    complexity += Math.min(0.3, (word.length - 3) * 0.05);
    const difficultLetters = ["q", "x", "z", "j", "w", "y"];
    const difficultCount = word.toLowerCase().split("").filter((l) => difficultLetters.includes(l)).length;
    complexity += difficultCount * 0.1;
    const complexCombinations = ["ch", "sh", "th", "ph", "ck", "qu"];
    let complexCount = 0;
    complexCombinations.forEach((combo) => {
      if (word.toLowerCase().includes(combo)) complexCount++;
    });
    complexity += complexCount * 0.1;
    const doubleVowels = ["aa", "ee", "ii", "oo", "uu", "ae", "ou", "ai"];
    doubleVowels.forEach((vowels) => {
      if (word.toLowerCase().includes(vowels)) complexity += 0.05;
    });
    return Math.min(1, complexity);
  }
  /**
   * 📐 Calcula precisão da sequência
   */
  calculateSequenceAccuracy(target, formed) {
    if (!target || !formed) return 0;
    if (target === formed) return 1;
    const targetArray = target.toLowerCase().split("");
    const formedArray = formed.toLowerCase().split("");
    let correctPositions = 0;
    const maxLength = Math.max(targetArray.length, formedArray.length);
    for (let i = 0; i < maxLength; i++) {
      if (targetArray[i] === formedArray[i]) {
        correctPositions++;
      }
    }
    return correctPositions / maxLength;
  }
  /**
   * 🎯 Analisa estratégia de construção
   */
  analyzeConstructionStrategy(data) {
    const strategies = [];
    const backtrackCount = data.behavioralMetrics?.backtrackCount || 0;
    const completionTime = data.behavioralMetrics?.completionTime || data.responseTime;
    const planningTime = data.behavioralMetrics?.planningTime || 0;
    const wordLength = data.targetWord?.length || 3;
    const planningRatio = planningTime / completionTime;
    if (planningRatio > 0.3) strategies.push("planning_first");
    else if (planningRatio < 0.1) strategies.push("immediate_action");
    if (backtrackCount === 0) strategies.push("linear_construction");
    else if (backtrackCount <= 2) strategies.push("minor_adjustments");
    else strategies.push("trial_and_error");
    const timePerLetter = completionTime / wordLength;
    if (timePerLetter < 1e3) strategies.push("rapid_construction");
    else if (timePerLetter > 2e3) strategies.push("methodical_construction");
    const accuracy = this.calculateSequenceAccuracy(data.targetWord, data.formedWord);
    if (accuracy === 1 && backtrackCount === 0) strategies.push("expert_builder");
    else if (accuracy > 0.8) strategies.push("skilled_builder");
    else if (accuracy < 0.5) strategies.push("learning_builder");
    return strategies.length > 0 ? strategies : ["standard_approach"];
  }
  /**
   * 🧠 Avalia função executiva
   */
  assessExecutiveFunction(data) {
    let executiveScore = 0.5;
    const backtrackCount = data.behavioralMetrics?.backtrackCount || 0;
    const planningTime = data.behavioralMetrics?.planningTime || 0;
    const completionTime = data.behavioralMetrics?.completionTime || data.responseTime;
    const planningRatio = planningTime / completionTime;
    if (planningRatio > 0.2) executiveScore += 0.2;
    else if (planningRatio < 0.05) executiveScore -= 0.1;
    if (backtrackCount === 0) executiveScore += 0.2;
    else if (backtrackCount <= 1) executiveScore += 0.1;
    else if (backtrackCount > 3) executiveScore -= 0.2;
    if (backtrackCount > 0 && data.formedWord === data.targetWord) {
      executiveScore += 0.1;
    }
    const accuracy = this.calculateSequenceAccuracy(data.targetWord, data.formedWord);
    executiveScore += (accuracy - 0.5) * 0.4;
    return Math.max(0, Math.min(1, executiveScore));
  }
  /**
   * 💭 Avalia memória de trabalho
   */
  assessWorkingMemory(data) {
    let memoryScore = 0.6;
    const wordLength = data.targetWord?.length || 3;
    const sequenceErrors = data.behavioralMetrics?.letterSequenceErrors || 0;
    const completionTime = data.behavioralMetrics?.completionTime || data.responseTime;
    if (wordLength <= 3) memoryScore += 0.1;
    else if (wordLength >= 6) {
      if (data.formedWord === data.targetWord) memoryScore += 0.3;
      else memoryScore -= 0.2;
    }
    const accuracy = this.calculateSequenceAccuracy(data.targetWord, data.formedWord);
    memoryScore += (accuracy - 0.5) * 0.4;
    if (sequenceErrors === 0) memoryScore += 0.2;
    else memoryScore -= sequenceErrors * 0.1;
    const optimalTime = wordLength * 1500;
    const timeDeviation = Math.abs(completionTime - optimalTime) / optimalTime;
    if (timeDeviation < 0.3) memoryScore += 0.1;
    return Math.max(0, Math.min(1, memoryScore));
  }
  /**
   * 📋 Avalia planejamento sequencial
   */
  assessSequentialPlanning(data) {
    let planningScore = 0.5;
    const sequence = data.letterSequence || [];
    const target = data.targetWord?.toLowerCase().split("") || [];
    const backtrackCount = data.behavioralMetrics?.backtrackCount || 0;
    if (sequence.length > 0) {
      let sequentialOrder = 0;
      for (let i = 0; i < Math.min(sequence.length, target.length); i++) {
        if (sequence[i] === target[i]) sequentialOrder++;
      }
      const orderAccuracy = sequentialOrder / target.length;
      planningScore += orderAccuracy * 0.3;
    }
    if (backtrackCount === 0) planningScore += 0.3;
    else if (backtrackCount === 1) planningScore += 0.1;
    else planningScore -= backtrackCount * 0.1;
    const planningTime = data.behavioralMetrics?.planningTime || 0;
    if (planningTime > 500) planningScore += 0.2;
    return Math.max(0, Math.min(1, planningScore));
  }
  /**
   * 📝 Avalia conhecimento ortográfico
   */
  assessOrthographicKnowledge(data) {
    let knowledge = 0.5;
    const target = data.targetWord?.toLowerCase() || "";
    const formed = data.formedWord?.toLowerCase() || "";
    if (target === formed) {
      knowledge = 0.9;
      const complexity = this.analyzeWordComplexity(target);
      if (complexity > 0.6) knowledge += 0.1;
    } else {
      const accuracy = this.calculateSequenceAccuracy(target, formed);
      knowledge += (accuracy - 0.5) * 0.6;
      if (formed.length === target.length) knowledge += 0.1;
    }
    return Math.max(0, Math.min(1, knowledge));
  }
  /**
   * 🔊 Avalia mapeamento fonético
   */
  assessPhoneticMapping(data) {
    let mapping = 0.6;
    const target = data.targetWord?.toLowerCase() || "";
    const formed = data.formedWord?.toLowerCase() || "";
    if (target === formed) {
      mapping += 0.3;
    } else {
      const phoneticErrors = this.countPhoneticErrors(target, formed);
      const totalDifferences = this.countDifferences(target, formed);
      if (totalDifferences > 0) {
        const phoneticAccuracy = 1 - phoneticErrors / totalDifferences;
        mapping += phoneticAccuracy * 0.3;
      }
    }
    return Math.max(0, Math.min(1, mapping));
  }
  /**
   * 👁️ Avalia reconhecimento de palavra
   */
  assessWordRecognition(data) {
    let recognition = 0.5;
    if (data.targetWord === data.formedWord) {
      recognition = 0.9;
      const timePerLetter = data.responseTime / (data.targetWord?.length || 3);
      if (timePerLetter < 1e3) recognition += 0.1;
    } else {
      const accuracy = this.calculateSequenceAccuracy(data.targetWord, data.formedWord);
      recognition += accuracy * 0.4;
    }
    return recognition;
  }
  /**
   * ❌ Classifica erro de construção
   */
  classifyConstructionError(data) {
    if (data.targetWord === data.formedWord) return "no_error";
    const target = data.targetWord?.toLowerCase() || "";
    const formed = data.formedWord?.toLowerCase() || "";
    if (target.length !== formed.length) {
      if (formed.length < target.length) return "incomplete_word";
      else return "extended_word";
    }
    const differences = [];
    for (let i = 0; i < target.length; i++) {
      if (target[i] !== formed[i]) {
        differences.push(i);
      }
    }
    if (differences.length === 1) return "single_letter_error";
    if (differences.length === 2 && Math.abs(differences[0] - differences[1]) === 1) return "adjacent_letter_error";
    if (differences.length <= target.length / 2) return "partial_construction_error";
    return "major_construction_error";
  }
  /**
   * 📍 Analisa posição do erro
   */
  analyzeErrorPosition(target, formed) {
    if (!target || !formed || target === formed) return null;
    const errorPositions = [];
    const maxLength = Math.max(target.length, formed.length);
    for (let i = 0; i < maxLength; i++) {
      const targetChar = target[i] || "";
      const formedChar = formed[i] || "";
      if (targetChar !== formedChar) {
        errorPositions.push({
          position: i,
          expected: targetChar,
          actual: formedChar,
          type: this.classifyPositionalError(targetChar, formedChar)
        });
      }
    }
    return {
      errorCount: errorPositions.length,
      positions: errorPositions,
      pattern: this.identifyErrorPattern(errorPositions)
    };
  }
  /**
   * 🔤 Classifica erro posicional
   */
  classifyPositionalError(expected, actual) {
    if (!expected) return "extra_letter";
    if (!actual) return "missing_letter";
    const similarity = this.calculateLetterSimilarity(expected, actual);
    if (similarity > 0.7) return "similar_letter_substitution";
    return "different_letter_substitution";
  }
  /**
   * 🔍 Identifica padrão de erro
   */
  identifyErrorPattern(errorPositions) {
    if (errorPositions.length === 0) return "no_pattern";
    if (errorPositions.length === 1) return "isolated_error";
    const positions = errorPositions.map((e) => e.position).sort((a, b) => a - b);
    let consecutive = true;
    for (let i = 1; i < positions.length; i++) {
      if (positions[i] - positions[i - 1] > 1) {
        consecutive = false;
        break;
      }
    }
    if (consecutive) return "consecutive_errors";
    const maxPos = Math.max(...positions);
    const wordLength = errorPositions[0].position !== void 0 ? maxPos + 1 : 3;
    if (positions.every((p) => p < wordLength / 3)) return "beginning_errors";
    if (positions.every((p) => p > 2 * wordLength / 3)) return "ending_errors";
    return "distributed_errors";
  }
  /**
   * 🎓 Avalia nível de habilidade de construção
   */
  assessConstructionSkillLevel(data) {
    const orthographic = this.assessOrthographicKnowledge(data);
    const planning = this.assessSequentialPlanning(data);
    const executive = this.assessExecutiveFunction(data);
    const memory = this.assessWorkingMemory(data);
    const overall = (orthographic + planning + executive + memory) / 4;
    if (overall >= 0.8) return "expert";
    if (overall >= 0.65) return "proficient";
    if (overall >= 0.45) return "developing";
    return "beginner";
  }
  /**
   * 📚 Identifica estratégia de aprendizado
   */
  identifyLearningStrategy(data) {
    const strategies = [];
    const backtrackCount = data.behavioralMetrics?.backtrackCount || 0;
    const accuracy = this.calculateSequenceAccuracy(data.targetWord, data.formedWord);
    const responseTime = data.responseTime || 0;
    if (accuracy > 0.9 && responseTime < 3e3) strategies.push("confident_learner");
    if (backtrackCount > 0 && accuracy > 0.8) strategies.push("self_correcting");
    if (backtrackCount === 0 && accuracy < 0.5) strategies.push("impulsive_responder");
    if (responseTime > 5e3 && accuracy > 0.7) strategies.push("careful_processor");
    if (backtrackCount > 3) strategies.push("trial_error_learner");
    return strategies.length > 0 ? strategies : ["typical_learner"];
  }
  // Métodos auxiliares
  countPhoneticErrors(target, formed) {
    let phoneticErrors = 0;
    const minLength = Math.min(target.length, formed.length);
    for (let i = 0; i < minLength; i++) {
      if (target[i] !== formed[i]) {
        if (!this.isPhoneticSubstitution(target[i], formed[i])) {
          phoneticErrors++;
        }
      }
    }
    return phoneticErrors;
  }
  countDifferences(target, formed) {
    let differences = 0;
    const maxLength = Math.max(target.length, formed.length);
    for (let i = 0; i < maxLength; i++) {
      if ((target[i] || "") !== (formed[i] || "")) {
        differences++;
      }
    }
    return differences;
  }
  isPhoneticSubstitution(char1, char2) {
    const phoneticGroups = [
      ["a", "e"],
      ["i", "e"],
      ["o", "u"],
      ["b", "p"],
      ["d", "t"],
      ["g", "k"],
      ["v", "f"],
      ["z", "s"],
      ["m", "n"]
    ];
    return phoneticGroups.some(
      (group) => group.includes(char1) && group.includes(char2)
    );
  }
  calculateLetterSimilarity(char1, char2) {
    const similarPairs = { "b": ["d", "p"], "d": ["b", "q"], "p": ["q", "b"], "q": ["p", "d"] };
    return similarPairs[char1]?.includes(char2) ? 0.8 : 0.1;
  }
  /**
   * 📊 Gera resumo da coleta
   */
  generateSummary() {
    if (this.collectedData.length === 0) return null;
    const totalCollections = this.collectedData.length;
    const perfectWords = this.collectedData.filter((d) => d.isCorrect).length;
    const accuracy = perfectWords / totalCollections;
    const avgResponseTime = this.collectedData.reduce((sum, d) => sum + d.responseTime, 0) / totalCollections;
    const avgComplexity = this.collectedData.reduce((sum, d) => sum + d.wordComplexity, 0) / totalCollections;
    const avgBacktracks = this.collectedData.reduce((sum, d) => sum + d.backtrackCount, 0) / totalCollections;
    return {
      collector: this.name,
      version: this.version,
      totalCollections,
      perfectWords,
      accuracy,
      averageResponseTime: avgResponseTime,
      averageComplexity: avgComplexity,
      averageBacktracks: avgBacktracks,
      skillProgression: this.analyzeSkillProgression(),
      constructionPatterns: this.analyzeConstructionPatterns(),
      recommendations: this.generateRecommendations()
    };
  }
  analyzeSkillProgression() {
    if (this.collectedData.length < 3) return null;
    const recentData = this.collectedData.slice(-5);
    const earlierData = this.collectedData.slice(0, 5);
    const recentAccuracy = recentData.filter((d) => d.isCorrect).length / recentData.length;
    const earlierAccuracy = earlierData.filter((d) => d.isCorrect).length / earlierData.length;
    const recentSpeed = recentData.reduce((sum, d) => sum + d.responseTime, 0) / recentData.length;
    const earlierSpeed = earlierData.reduce((sum, d) => sum + d.responseTime, 0) / earlierData.length;
    return {
      accuracyImprovement: recentAccuracy - earlierAccuracy,
      speedImprovement: earlierSpeed - recentSpeed,
      // Negativo = mais lento
      currentLevel: recentAccuracy,
      trend: recentAccuracy > earlierAccuracy ? "improving" : "stable"
    };
  }
  analyzeConstructionPatterns() {
    const strategies = {};
    this.collectedData.forEach((d) => {
      d.constructionStrategy.forEach((strategy) => {
        strategies[strategy] = (strategies[strategy] || 0) + 1;
      });
    });
    const errorTypes = {};
    this.collectedData.forEach((d) => {
      if (d.errorType !== "no_error") {
        errorTypes[d.errorType] = (errorTypes[d.errorType] || 0) + 1;
      }
    });
    return {
      preferredStrategies: strategies,
      commonErrors: errorTypes,
      averageWordLength: this.collectedData.reduce((sum, d) => sum + (d.targetWord?.length || 0), 0) / this.collectedData.length
    };
  }
  generateRecommendations() {
    const summary = this.generateSummary();
    if (!summary) return [];
    const recommendations = [];
    if (summary.accuracy < 0.6) {
      recommendations.push({
        type: "word_accuracy",
        priority: "high",
        message: "Pratique com palavras mais simples para consolidar construção básica."
      });
    }
    if (summary.averageBacktracks > 2) {
      recommendations.push({
        type: "planning",
        priority: "medium",
        message: "Desenvolva estratégias de planejamento antes de começar a construir."
      });
    }
    if (summary.averageResponseTime > 8e3) {
      recommendations.push({
        type: "speed",
        priority: "low",
        message: "Pratique construção de palavras familiares para aumentar fluência."
      });
    }
    return recommendations;
  }
  reset() {
    this.collectedData = [];
  }
  exportData() {
    return {
      collector: this.name,
      version: this.version,
      collectedAt: (/* @__PURE__ */ new Date()).toISOString(),
      data: this.collectedData,
      summary: this.generateSummary()
    };
  }
}
class SequenceRecognitionCollector {
  constructor() {
    this.name = "SequenceRecognitionCollector";
    this.version = "3.0.0";
    this.isActive = true;
    this.collectedData = [];
  }
  async collect(data) {
    try {
      const timestamp = (/* @__PURE__ */ new Date()).toISOString();
      const analysisData = {
        sessionId: data.sessionId,
        userId: data.userId,
        timestamp,
        activityType: "sequence_recognition",
        // Dados da sequência
        sequencePattern: data.sequencePattern,
        missingPosition: data.missingPosition,
        selectedLetter: data.selectedLetter,
        expectedLetter: data.expectedLetter,
        isCorrect: data.selectedLetter === data.expectedLetter,
        responseTime: data.responseTime || 0,
        // Análise da sequência
        sequenceLength: data.sequencePattern?.length || 0,
        patternType: this.analyzePatternType(data.sequencePattern),
        patternComplexity: this.calculatePatternComplexity(data.sequencePattern),
        positionDifficulty: this.assessPositionDifficulty(data.missingPosition, data.sequencePattern?.length),
        // Métricas cognitivas
        patternRecognitionTime: data.behavioralMetrics?.patternRecognitionTime || data.responseTime,
        workingMemoryLoad: this.assessWorkingMemoryLoad(data),
        sequentialProcessing: this.assessSequentialProcessing(data),
        patternAnalysis: this.assessPatternAnalysis(data),
        // Habilidades específicas
        alphabetKnowledge: this.assessAlphabetKnowledge(data),
        sequentialMemory: this.assessSequentialMemory(data),
        logicalReasoning: this.assessLogicalReasoning(data),
        // Padrões de erro
        errorType: this.classifySequenceError(data),
        errorAnalysis: this.analyzeSequenceError(data)
      };
      this.collectedData.push(analysisData);
      return analysisData;
    } catch (error) {
      console.error("Erro no SequenceRecognitionCollector:", error);
      return null;
    }
  }
  analyzePatternType(sequence) {
    if (!sequence || sequence.length < 2) return "unknown";
    const codes = sequence.map((letter) => letter.charCodeAt(0));
    let isConsecutive = true;
    for (let i = 1; i < codes.length; i++) {
      if (codes[i] - codes[i - 1] !== 1) {
        isConsecutive = false;
        break;
      }
    }
    if (isConsecutive) return "consecutive";
    if (codes.length >= 3) {
      const diff1 = codes[1] - codes[0];
      const diff2 = codes[2] - codes[1];
      if (diff1 === diff2 && diff1 > 1) return "skip_pattern";
    }
    let isReverse = true;
    for (let i = 1; i < codes.length; i++) {
      if (codes[i - 1] - codes[i] !== 1) {
        isReverse = false;
        break;
      }
    }
    if (isReverse) return "reverse";
    return "irregular";
  }
  calculatePatternComplexity(sequence) {
    if (!sequence) return 0.5;
    let complexity = 0.3;
    complexity += Math.min(0.3, (sequence.length - 3) * 0.1);
    const patternType = this.analyzePatternType(sequence);
    const complexityMap = {
      "consecutive": 0.2,
      "skip_pattern": 0.4,
      "reverse": 0.5,
      "irregular": 0.8
    };
    complexity += complexityMap[patternType] || 0.3;
    return Math.min(1, complexity);
  }
  assessPositionDifficulty(position, sequenceLength) {
    if (!position || !sequenceLength) return 0.5;
    const relativePosition = position / (sequenceLength - 1);
    if (relativePosition > 0.2 && relativePosition < 0.8) return 0.8;
    return 0.4;
  }
  assessWorkingMemoryLoad(data) {
    let load = 0.4;
    const sequenceLength = data.sequencePattern?.length || 3;
    load += Math.min(0.4, (sequenceLength - 3) * 0.1);
    const complexity = this.calculatePatternComplexity(data.sequencePattern);
    load += complexity * 0.3;
    return Math.min(1, load);
  }
  assessSequentialProcessing(data) {
    let processing = 0.6;
    if (data.selectedLetter === data.expectedLetter) {
      processing += 0.3;
      const complexity = this.calculatePatternComplexity(data.sequencePattern);
      if (complexity > 0.6) processing += 0.1;
    }
    const responseTime = data.responseTime || 0;
    if (responseTime < 3e3) processing += 0.1;
    else if (responseTime > 6e3) processing -= 0.2;
    return Math.max(0, Math.min(1, processing));
  }
  assessPatternAnalysis(data) {
    let analysis = 0.5;
    this.analyzePatternType(data.sequencePattern);
    const isCorrect = data.selectedLetter === data.expectedLetter;
    if (isCorrect) {
      analysis += 0.3;
      const complexity = this.calculatePatternComplexity(data.sequencePattern);
      analysis += complexity * 0.2;
    }
    return Math.min(1, analysis);
  }
  assessAlphabetKnowledge(data) {
    let knowledge = 0.6;
    if (data.selectedLetter === data.expectedLetter) {
      knowledge += 0.3;
    } else {
      const expectedCode = data.expectedLetter?.charCodeAt(0) || 0;
      const selectedCode = data.selectedLetter?.charCodeAt(0) || 0;
      const distance = Math.abs(expectedCode - selectedCode);
      if (distance <= 2) knowledge += 0.1;
      else if (distance > 5) knowledge -= 0.2;
    }
    return Math.max(0, Math.min(1, knowledge));
  }
  assessSequentialMemory(data) {
    let memory = 0.6;
    const sequenceLength = data.sequencePattern?.length || 3;
    if (sequenceLength > 4 && data.selectedLetter === data.expectedLetter) {
      memory += 0.3;
    }
    return Math.min(1, memory);
  }
  assessLogicalReasoning(data) {
    let reasoning = 0.5;
    const patternType = this.analyzePatternType(data.sequencePattern);
    const isCorrect = data.selectedLetter === data.expectedLetter;
    if (isCorrect) {
      reasoning += 0.3;
      if (patternType === "skip_pattern") reasoning += 0.2;
      else if (patternType === "irregular") reasoning += 0.3;
    }
    return Math.min(1, reasoning);
  }
  classifySequenceError(data) {
    if (data.selectedLetter === data.expectedLetter) return "no_error";
    const expectedCode = data.expectedLetter?.charCodeAt(0) || 0;
    const selectedCode = data.selectedLetter?.charCodeAt(0) || 0;
    const distance = Math.abs(expectedCode - selectedCode);
    if (distance === 1) return "adjacent_letter_error";
    if (distance <= 3) return "close_letter_error";
    if (distance > 10) return "random_letter_error";
    return "moderate_distance_error";
  }
  analyzeSequenceError(data) {
    if (data.selectedLetter === data.expectedLetter) return null;
    const sequence = data.sequencePattern || [];
    const position = data.missingPosition || 0;
    return {
      errorType: this.classifySequenceError(data),
      distanceFromCorrect: Math.abs(
        (data.expectedLetter?.charCodeAt(0) || 0) - (data.selectedLetter?.charCodeAt(0) || 0)
      ),
      positionInSequence: position,
      sequenceComplexity: this.calculatePatternComplexity(sequence),
      possibleCauses: this.identifyErrorCauses(data)
    };
  }
  identifyErrorCauses(data) {
    const causes = [];
    const responseTime = data.responseTime || 0;
    if (responseTime < 1500) causes.push("impulsive_response");
    if (responseTime > 8e3) causes.push("processing_difficulty");
    const complexity = this.calculatePatternComplexity(data.sequencePattern);
    if (complexity > 0.7) causes.push("pattern_complexity");
    const sequenceLength = data.sequencePattern?.length || 3;
    if (sequenceLength > 5) causes.push("memory_overload");
    return causes;
  }
  generateSummary() {
    if (this.collectedData.length === 0) return null;
    const total = this.collectedData.length;
    const correct = this.collectedData.filter((d) => d.isCorrect).length;
    const accuracy = correct / total;
    const avgResponseTime = this.collectedData.reduce((sum, d) => sum + d.responseTime, 0) / total;
    const avgComplexity = this.collectedData.reduce((sum, d) => sum + d.patternComplexity, 0) / total;
    const patternPerformance = {};
    ["consecutive", "skip_pattern", "reverse", "irregular"].forEach((type) => {
      const typeData = this.collectedData.filter((d) => d.patternType === type);
      if (typeData.length > 0) {
        patternPerformance[type] = {
          count: typeData.length,
          accuracy: typeData.filter((d) => d.isCorrect).length / typeData.length,
          avgTime: typeData.reduce((sum, d) => sum + d.responseTime, 0) / typeData.length
        };
      }
    });
    return {
      collector: this.name,
      version: this.version,
      total,
      accuracy,
      avgResponseTime,
      avgComplexity,
      patternPerformance,
      recommendations: this.generateRecommendations()
    };
  }
  generateRecommendations() {
    const summary = this.generateSummary();
    if (!summary) return [];
    const recommendations = [];
    if (summary.accuracy < 0.6) {
      recommendations.push({
        type: "pattern_practice",
        priority: "high",
        message: "Pratique reconhecimento de padrões simples antes de avançar."
      });
    }
    if (summary.avgResponseTime > 6e3) {
      recommendations.push({
        type: "processing_speed",
        priority: "medium",
        message: "Trabalhe velocidade de reconhecimento de padrões."
      });
    }
    Object.keys(summary.patternPerformance).forEach((type) => {
      const perf = summary.patternPerformance[type];
      if (perf.accuracy < 0.5) {
        recommendations.push({
          type: "pattern_specific",
          priority: "medium",
          message: `Foque em padrões do tipo ${type} para melhorar.`
        });
      }
    });
    return recommendations;
  }
  reset() {
    this.collectedData = [];
  }
  exportData() {
    return {
      collector: this.name,
      version: this.version,
      collectedAt: (/* @__PURE__ */ new Date()).toISOString(),
      data: this.collectedData,
      summary: this.generateSummary()
    };
  }
}
class VisualDiscriminationCollector {
  constructor() {
    this.name = "VisualDiscriminationCollector";
    this.version = "3.0.0";
    this.isActive = true;
    this.collectedData = [];
  }
  async collect(data) {
    try {
      const timestamp = (/* @__PURE__ */ new Date()).toISOString();
      const analysisData = {
        sessionId: data.sessionId,
        userId: data.userId,
        timestamp,
        activityType: "visual_discrimination",
        // Dados visuais
        targetLetter: data.targetLetter,
        similarLetters: data.similarLetters,
        selectedLetter: data.selectedLetter,
        expectedLetter: data.expectedLetter,
        isCorrect: data.selectedLetter === data.expectedLetter,
        responseTime: data.responseTime || 0,
        // Análise visual
        visualSimilarity: this.calculateVisualSimilarity(data.targetLetter, data.selectedLetter),
        discriminationDifficulty: this.assessDiscriminationDifficulty(data),
        visualComplexity: this.assessVisualComplexity(data.targetLetter),
        // Métricas de processamento visual
        visualProcessingTime: data.behavioralMetrics?.visualProcessingTime || data.responseTime,
        visualAttention: this.assessVisualAttention(data),
        perceptualAccuracy: this.assessPerceptualAccuracy(data),
        visualMemory: this.assessVisualMemory(data),
        // Habilidades específicas
        featureDetection: this.assessFeatureDetection(data),
        spatialOrientation: this.assessSpatialOrientation(data),
        visualScanning: this.assessVisualScanning(data),
        // Padrões de erro
        errorType: this.classifyVisualError(data),
        errorAnalysis: this.analyzeVisualError(data)
      };
      this.collectedData.push(analysisData);
      return analysisData;
    } catch (error) {
      console.error("Erro no VisualDiscriminationCollector:", error);
      return null;
    }
  }
  calculateVisualSimilarity(letter1, letter2) {
    if (!letter1 || !letter2) return 0.5;
    if (letter1 === letter2) return 1;
    const visualFeatures = {
      "b": { curves: 1, lines: 1, orientation: "right", openings: 1, symmetry: 0 },
      "d": { curves: 1, lines: 1, orientation: "left", openings: 1, symmetry: 0 },
      "p": { curves: 1, lines: 1, orientation: "right", openings: 1, symmetry: 0 },
      "q": { curves: 1, lines: 1, orientation: "left", openings: 1, symmetry: 0 },
      "n": { curves: 1, lines: 2, orientation: "none", openings: 0, symmetry: 0 },
      "u": { curves: 1, lines: 2, orientation: "none", openings: 1, symmetry: 1 },
      "m": { curves: 2, lines: 3, orientation: "none", openings: 0, symmetry: 1 },
      "w": { curves: 2, lines: 4, orientation: "none", openings: 0, symmetry: 1 },
      "a": { curves: 1, lines: 2, orientation: "none", openings: 1, symmetry: 0 },
      "e": { curves: 1, lines: 3, orientation: "none", openings: 1, symmetry: 0 },
      "o": { curves: 1, lines: 0, orientation: "none", openings: 1, symmetry: 1 },
      "c": { curves: 1, lines: 0, orientation: "none", openings: 1, symmetry: 0 },
      "i": { curves: 0, lines: 1, orientation: "none", openings: 0, symmetry: 1 },
      "l": { curves: 0, lines: 1, orientation: "none", openings: 0, symmetry: 1 }
    };
    const feat1 = visualFeatures[letter1.toLowerCase()] || { curves: 0.5, lines: 1, orientation: "none", openings: 0.5, symmetry: 0.5 };
    const feat2 = visualFeatures[letter2.toLowerCase()] || { curves: 0.5, lines: 1, orientation: "none", openings: 0.5, symmetry: 0.5 };
    let similarity = 0;
    const curveDiff = Math.abs(feat1.curves - feat2.curves);
    similarity += (1 - Math.min(1, curveDiff / 2)) * 0.3;
    const lineDiff = Math.abs(feat1.lines - feat2.lines);
    similarity += (1 - Math.min(1, lineDiff / 3)) * 0.2;
    if (feat1.orientation === feat2.orientation) similarity += 0.3;
    else if (feat1.orientation === "left" && feat2.orientation === "right" || feat1.orientation === "right" && feat2.orientation === "left") {
      similarity += 0.1;
    }
    const openingDiff = Math.abs(feat1.openings - feat2.openings);
    similarity += (1 - Math.min(1, openingDiff)) * 0.1;
    if (feat1.symmetry === feat2.symmetry) similarity += 0.1;
    return Math.min(1, similarity);
  }
  assessVisualComplexity(letter) {
    if (!letter) return 0.5;
    const complexityMap = {
      // Letras simples
      "i": 0.1,
      "l": 0.1,
      "o": 0.2,
      "c": 0.2,
      // Letras intermediárias
      "u": 0.3,
      "n": 0.3,
      "a": 0.4,
      "e": 0.4,
      // Letras com orientação específica
      "b": 0.5,
      "d": 0.5,
      "p": 0.5,
      "q": 0.5,
      // Letras complexas
      "m": 0.7,
      "w": 0.8,
      "g": 0.8,
      "f": 0.9
    };
    return complexityMap[letter.toLowerCase()] || 0.5;
  }
  assessDiscriminationDifficulty(data) {
    let difficulty = 0.4;
    const visualSimilarity = this.calculateVisualSimilarity(data.targetLetter, data.selectedLetter);
    difficulty += visualSimilarity * 0.4;
    const complexity = this.assessVisualComplexity(data.targetLetter);
    difficulty += complexity * 0.2;
    return Math.min(1, difficulty);
  }
  assessVisualAttention(data) {
    let attention = 0.6;
    if (data.selectedLetter === data.expectedLetter) {
      attention += 0.3;
      const difficulty = this.assessDiscriminationDifficulty(data);
      if (difficulty > 0.7) attention += 0.1;
    }
    const responseTime = data.responseTime || 0;
    if (responseTime < 2e3) attention += 0.1;
    else if (responseTime > 6e3) attention -= 0.2;
    return Math.max(0, Math.min(1, attention));
  }
  assessPerceptualAccuracy(data) {
    let accuracy = 0.5;
    const visualSimilarity = this.calculateVisualSimilarity(data.targetLetter, data.selectedLetter);
    const isCorrect = data.selectedLetter === data.expectedLetter;
    if (isCorrect) {
      accuracy += 0.4;
      if (visualSimilarity > 0.7) accuracy += 0.1;
    } else {
      if (visualSimilarity < 0.3) accuracy -= 0.3;
    }
    return Math.max(0, Math.min(1, accuracy));
  }
  assessVisualMemory(data) {
    let memory = 0.6;
    if (data.selectedLetter === data.expectedLetter) {
      memory += 0.3;
      const responseTime = data.responseTime || 0;
      if (responseTime < 2500) memory += 0.1;
    }
    return Math.min(1, memory);
  }
  assessFeatureDetection(data) {
    let detection = 0.6;
    if (data.selectedLetter === data.expectedLetter) {
      detection += 0.3;
      const complexity = this.assessVisualComplexity(data.targetLetter);
      if (complexity > 0.6) detection += 0.1;
    }
    return Math.min(1, detection);
  }
  assessSpatialOrientation(data) {
    let orientation = 0.6;
    const orientationPairs = [
      ["b", "d"],
      ["p", "q"],
      ["u", "n"],
      ["m", "w"]
    ];
    const isOrientationPair = orientationPairs.some(
      (pair) => pair.includes(data.targetLetter?.toLowerCase()) && pair.includes(data.selectedLetter?.toLowerCase())
    );
    if (data.selectedLetter === data.expectedLetter) {
      orientation += 0.3;
      if (isOrientationPair) orientation += 0.1;
    } else if (isOrientationPair) {
      orientation -= 0.2;
    }
    return Math.max(0, Math.min(1, orientation));
  }
  assessVisualScanning(data) {
    let scanning = 0.6;
    const responseTime = data.responseTime || 0;
    const similarLettersCount = data.similarLetters?.length || 1;
    if (data.selectedLetter === data.expectedLetter) {
      scanning += 0.3;
      if (similarLettersCount > 3 && responseTime < 4e3) {
        scanning += 0.1;
      }
    }
    if (responseTime > 8e3) scanning -= 0.2;
    return Math.max(0, Math.min(1, scanning));
  }
  classifyVisualError(data) {
    if (data.selectedLetter === data.expectedLetter) return "no_error";
    const visualSimilarity = this.calculateVisualSimilarity(data.targetLetter, data.selectedLetter);
    const orientationPairs = [
      ["b", "d"],
      ["p", "q"],
      ["u", "n"],
      ["m", "w"]
    ];
    const isOrientationError = orientationPairs.some(
      (pair) => pair.includes(data.targetLetter?.toLowerCase()) && pair.includes(data.selectedLetter?.toLowerCase())
    );
    if (isOrientationError) return "orientation_error";
    if (visualSimilarity > 0.7) return "high_similarity_confusion";
    if (visualSimilarity > 0.4) return "moderate_similarity_confusion";
    if (visualSimilarity < 0.2) return "random_selection";
    return "low_similarity_confusion";
  }
  analyzeVisualError(data) {
    if (data.selectedLetter === data.expectedLetter) return null;
    const visualSimilarity = this.calculateVisualSimilarity(data.targetLetter, data.selectedLetter);
    const difficulty = this.assessDiscriminationDifficulty(data);
    return {
      errorType: this.classifyVisualError(data),
      visualSimilarity,
      discriminationDifficulty: difficulty,
      visualComplexity: this.assessVisualComplexity(data.targetLetter),
      possibleCauses: this.identifyVisualErrorCauses(data)
    };
  }
  identifyVisualErrorCauses(data) {
    const causes = [];
    const responseTime = data.responseTime || 0;
    if (responseTime < 1e3) causes.push("impulsive_response");
    if (responseTime > 7e3) causes.push("visual_processing_difficulty");
    const visualSimilarity = this.calculateVisualSimilarity(data.targetLetter, data.selectedLetter);
    if (visualSimilarity > 0.7) causes.push("high_visual_similarity");
    const errorType = this.classifyVisualError(data);
    if (errorType === "orientation_error") causes.push("spatial_orientation_difficulty");
    const complexity = this.assessVisualComplexity(data.targetLetter);
    if (complexity > 0.7) causes.push("complex_visual_features");
    return causes;
  }
  generateSummary() {
    if (this.collectedData.length === 0) return null;
    const total = this.collectedData.length;
    const correct = this.collectedData.filter((d) => d.isCorrect).length;
    const accuracy = correct / total;
    const avgResponseTime = this.collectedData.reduce((sum, d) => sum + d.responseTime, 0) / total;
    const avgVisualSimilarity = this.collectedData.reduce((sum, d) => sum + d.visualSimilarity, 0) / total;
    const avgAttention = this.collectedData.reduce((sum, d) => sum + d.visualAttention, 0) / total;
    const errorTypes = {};
    this.collectedData.forEach((d) => {
      if (!errorTypes[d.errorType]) errorTypes[d.errorType] = 0;
      errorTypes[d.errorType]++;
    });
    const complexityPerformance = {
      "simple": this.getPerformanceByComplexity(0, 0.3),
      "moderate": this.getPerformanceByComplexity(0.3, 0.6),
      "complex": this.getPerformanceByComplexity(0.6, 1)
    };
    return {
      collector: this.name,
      version: this.version,
      total,
      accuracy,
      avgResponseTime,
      avgVisualSimilarity,
      avgVisualAttention: avgAttention,
      errorTypes,
      complexityPerformance,
      recommendations: this.generateRecommendations()
    };
  }
  getPerformanceByComplexity(minComp, maxComp) {
    const data = this.collectedData.filter(
      (d) => d.visualComplexity >= minComp && d.visualComplexity < maxComp
    );
    if (data.length === 0) return null;
    return {
      count: data.length,
      accuracy: data.filter((d) => d.isCorrect).length / data.length,
      avgTime: data.reduce((sum, d) => sum + d.responseTime, 0) / data.length
    };
  }
  generateRecommendations() {
    const summary = this.generateSummary();
    if (!summary) return [];
    const recommendations = [];
    if (summary.accuracy < 0.6) {
      recommendations.push({
        type: "visual_discrimination_training",
        priority: "high",
        message: "Pratique discriminação visual com letras simples."
      });
    }
    const orientationErrors = summary.errorTypes.orientation_error || 0;
    if (orientationErrors > summary.total * 0.3) {
      recommendations.push({
        type: "orientation_training",
        priority: "high",
        message: "Foque em exercícios de orientação espacial (b/d, p/q)."
      });
    }
    if (summary.avgResponseTime > 6e3) {
      recommendations.push({
        type: "visual_processing_speed",
        priority: "medium",
        message: "Pratique reconhecimento visual rápido."
      });
    }
    return recommendations;
  }
  reset() {
    this.collectedData = [];
  }
  exportData() {
    return {
      collector: this.name,
      version: this.version,
      collectedAt: (/* @__PURE__ */ new Date()).toISOString(),
      data: this.collectedData,
      summary: this.generateSummary()
    };
  }
}
class LetterRecognitionCollectorsHub {
  constructor() {
    this._collectors = {
      // Coletores gerais cognitivos
      phonetic: new PhoneticPatternCollector(),
      confusion: new LetterConfusionCollector(),
      visualLinguistic: new VisualLinguisticCollector(),
      readingDevelopment: new ReadingDevelopmentCollector(),
      dyslexiaIndicator: new DyslexiaIndicatorCollector(),
      cognitivePattern: new CognitivePatternCollector(),
      errorPattern: new ErrorPatternCollector(),
      linguisticProcessing: new LinguisticProcessingCollector(),
      visualAttention: new VisualAttentionCollector(),
      workingMemory: new WorkingMemoryCollector(),
      sequentialProcessing: new SequentialProcessingCollector(),
      // Coletores específicos das atividades
      letterSelection: new LetterSelectionCollector(),
      wordFormation: new WordFormationCollector(),
      sequenceRecognition: new SequenceRecognitionCollector(),
      visualDiscrimination: new VisualDiscriminationCollector()
    };
    this.isActive = true;
    this.collectionHistory = [];
    this.currentSession = null;
  }
  /**
   * Getter para coletores - necessário para GameSpecificProcessors
   */
  get collectors() {
    return this._collectors;
  }
  /**
   * Inicializa uma nova sessão de coleta
   * @param {string} sessionId - ID da sessão
   * @param {Object} sessionConfig - Configuração da sessão
   */
  async initializeSession(sessionId, sessionConfig = {}) {
    try {
      this.currentSession = {
        sessionId,
        startTime: Date.now(),
        difficulty: sessionConfig.difficulty || "MEDIUM",
        availableLetters: sessionConfig.availableLetters || [],
        gameMode: sessionConfig.gameMode || "letter_recognition",
        totalAttempts: 0,
        correctAttempts: 0
      };
      for (const [key, collector] of Object.entries(this._collectors)) {
        if (typeof collector.initializeSession === "function") {
          await collector.initializeSession(sessionId, sessionConfig);
        }
      }
      console.log("📚 LetterRecognition Collectors Hub: Sessão inicializada", {
        sessionId,
        difficulty: this.currentSession.difficulty,
        availableLetters: this.currentSession.availableLetters
      });
      return {
        status: "success",
        sessionId,
        collectorsInitialized: Object.keys(this._collectors).length
      };
    } catch (error) {
      console.error("❌ Erro ao inicializar sessão de coletores:", error);
      return { status: "error", message: error.message };
    }
  }
  /**
   * Coleta abrangente de dados de uma tentativa de Letter Recognition V3
   * Suporta todas as 6 atividades: letter_selection, sound_matching, word_formation,
   * sequence_recognition, phonetic_discrimination, visual_discrimination
   */
  async collectComprehensiveData(attemptData) {
    if (!this.isActive) return null;
    try {
      const timestamp = (/* @__PURE__ */ new Date()).toISOString();
      console.log("🔍 Debug - Dados recebidos no hub V3:", {
        activityType: attemptData.activityType,
        targetLetter: attemptData.targetLetter,
        selectedLetter: attemptData.selectedLetter,
        sessionId: attemptData.sessionId,
        behavioralMetrics: attemptData.behavioralMetrics
      });
      const enrichedData = this.enrichAttemptDataV3(attemptData);
      let activitySpecificData = {};
      switch (attemptData.activityType) {
        case "letter_selection":
          activitySpecificData = await this.collectLetterSelectionData(enrichedData);
          break;
        case "sound_matching":
          activitySpecificData = await this.collectSoundMatchingData(enrichedData);
          break;
        case "word_formation":
          activitySpecificData = await this.collectWordFormationData(enrichedData);
          break;
        case "sequence_recognition":
          activitySpecificData = await this.collectSequenceRecognitionData(enrichedData);
          break;
        case "phonetic_discrimination":
          activitySpecificData = await this.collectPhoneticDiscriminationData(enrichedData);
          break;
        case "visual_discrimination":
          activitySpecificData = await this.collectVisualDiscriminationData(enrichedData);
          break;
        default:
          activitySpecificData = await this.collectDefaultData(enrichedData);
      }
      const [
        phoneticAnalysis,
        confusionAnalysis,
        visualLinguisticAnalysis,
        readingDevelopmentAnalysis,
        dyslexiaIndicatorAnalysis,
        cognitivePatternAnalysis,
        errorPatternAnalysis,
        linguisticProcessingAnalysis,
        visualAttentionAnalysis,
        workingMemoryAnalysis,
        sequentialProcessingAnalysis
      ] = await Promise.all([
        this._collectors.phonetic.analyze(enrichedData),
        this._collectors.confusion.analyze(enrichedData),
        this._collectors.visualLinguistic.analyze(enrichedData),
        this._collectors.readingDevelopment.analyze(enrichedData),
        this._collectors.dyslexiaIndicator.analyze(enrichedData),
        this._collectors.cognitivePattern.analyze(enrichedData),
        this._collectors.errorPattern.analyze(enrichedData),
        this._collectors.linguisticProcessing.analyze(enrichedData),
        this._collectors.visualAttention.collectAttentionData(enrichedData, attemptData.playerBehavior || {}),
        this._collectors.workingMemory.collectWorkingMemoryData(enrichedData, attemptData.playerBehavior || {}),
        this._collectors.sequentialProcessing.collectSequentialData(enrichedData, attemptData.playerBehavior || {})
      ]);
      const comprehensiveAnalysis = {
        timestamp,
        sessionId: attemptData.sessionId,
        userId: attemptData.userId,
        // Dados da atividade V3
        activityType: attemptData.activityType,
        activityRound: attemptData.activityRound,
        targetLetter: attemptData.targetLetter,
        selectedLetter: attemptData.selectedLetter,
        isCorrect: attemptData.isCorrect,
        responseTime: attemptData.responseTime,
        // Métricas comportamentais V3
        behavioralMetrics: attemptData.behavioralMetrics,
        // Dados específicos da atividade
        activitySpecificData,
        // Análises especializadas
        phoneticAnalysis,
        confusionAnalysis,
        visualLinguisticAnalysis,
        readingDevelopmentAnalysis,
        dyslexiaIndicatorAnalysis,
        cognitivePatternAnalysis,
        errorPatternAnalysis,
        linguisticProcessingAnalysis,
        visualAttentionAnalysis,
        workingMemoryAnalysis,
        sequentialProcessingAnalysis,
        // Métricas consolidadas
        consolidatedMetrics: this.consolidateMetrics({
          phoneticAnalysis,
          confusionAnalysis,
          visualLinguisticAnalysis,
          readingDevelopmentAnalysis,
          dyslexiaIndicatorAnalysis,
          cognitivePatternAnalysis,
          errorPatternAnalysis,
          linguisticProcessingAnalysis,
          visualAttentionAnalysis,
          workingMemoryAnalysis,
          sequentialProcessingAnalysis
        })
      };
      this.collectionHistory.push(comprehensiveAnalysis);
      if (this.collectionHistory.length > 100) {
        this.collectionHistory.shift();
      }
      return comprehensiveAnalysis;
    } catch (error) {
      console.error("Erro na coleta abrangente de dados:", error);
      return null;
    }
  }
  /**
   * Consolida métricas de todos os coletores em um resumo unificado
   */
  consolidateMetrics(analyses) {
    return {
      // Processamento fonético
      phoneticComplexity: analyses.phoneticAnalysis.phoneticComplexity,
      phoneticAccuracy: analyses.phoneticAnalysis.phoneticMatch ? 1 : 0,
      // Confusão de letras
      hasLetterConfusion: analyses.confusionAnalysis.hasConfusion,
      confusionSeverity: analyses.confusionAnalysis.confusionSeverity,
      // Processamento visual
      visualComplexity: analyses.visualLinguisticAnalysis.visualComplexity,
      processingEfficiency: analyses.visualLinguisticAnalysis.visualProcessingEfficiency,
      // Desenvolvimento da leitura
      developmentStage: analyses.readingDevelopmentAnalysis.developmentStage,
      readingReadiness: analyses.readingDevelopmentAnalysis.readingReadiness,
      // Indicadores de dislexia
      dyslexiaRiskLevel: analyses.dyslexiaIndicatorAnalysis.riskLevel,
      // Score geral consolidado
      overallScore: this.calculateOverallScore(analyses)
    };
  }
  /**
   * Calcula um score geral baseado em todas as análises
   */
  calculateOverallScore(analyses) {
    const weights = {
      phonetic: 0.25,
      visual: 0.25,
      development: 0.25,
      confusion: 0.15,
      dyslexia: 0.1
    };
    const phoneticScore = analyses.phoneticAnalysis.phoneticMatch ? 1 : 0;
    const visualScore = analyses.visualLinguisticAnalysis.visualProcessingEfficiency;
    const developmentScore = analyses.readingDevelopmentAnalysis.readingReadiness;
    const confusionScore = analyses.confusionAnalysis.hasConfusion ? 0.5 : 1;
    const dyslexiaScore = analyses.dyslexiaIndicatorAnalysis.riskLevel === "low" ? 1 : analyses.dyslexiaIndicatorAnalysis.riskLevel === "moderate" ? 0.7 : 0.4;
    return phoneticScore * weights.phonetic + visualScore * weights.visual + developmentScore * weights.development + confusionScore * weights.confusion + dyslexiaScore * weights.dyslexia;
  }
  /**
   * Gera relatório de progresso baseado no histórico de coletas
   */
  generateProgressReport() {
    if (this.collectionHistory.length === 0) {
      return { error: "Nenhum dado coletado ainda" };
    }
    const recentData = this.collectionHistory.slice(-20);
    return {
      totalAttempts: this.collectionHistory.length,
      recentAccuracy: this.calculateRecentAccuracy(recentData),
      phoneticProgress: this.assessPhoneticProgress(recentData),
      confusionPatterns: this.identifyConfusionPatterns(recentData),
      developmentTrends: this.analyzeDevelopmentTrends(recentData),
      recommendations: this.generateRecommendations(recentData)
    };
  }
  calculateRecentAccuracy(data) {
    const correct = data.filter((attempt) => attempt.isCorrect).length;
    return data.length > 0 ? correct / data.length * 100 : 0;
  }
  assessPhoneticProgress(data) {
    const phoneticScores = data.map((d) => d.phoneticAnalysis.phoneticMatch ? 1 : 0);
    return phoneticScores.reduce((sum, score) => sum + score, 0) / phoneticScores.length;
  }
  identifyConfusionPatterns(data) {
    const confusions = {};
    data.forEach((attempt) => {
      if (attempt.confusionAnalysis.hasConfusion) {
        const key = `${attempt.targetLetter}-${attempt.selectedLetter}`;
        confusions[key] = (confusions[key] || 0) + 1;
      }
    });
    return confusions;
  }
  analyzeDevelopmentTrends(data) {
    const stages = data.map((d) => d.readingDevelopmentAnalysis.developmentStage);
    const mostRecentStage = stages[stages.length - 1];
    const stageConsistency = stages.filter((stage) => stage === mostRecentStage).length / stages.length;
    return {
      currentStage: mostRecentStage,
      stageConsistency,
      progressStability: stageConsistency > 0.8 ? "stable" : "variable"
    };
  }
  generateRecommendations(data) {
    const allRecommendations = [];
    data.forEach((attempt) => {
      allRecommendations.push(...attempt.phoneticAnalysis.recommendations);
      allRecommendations.push(...attempt.readingDevelopmentAnalysis.interventionRecommendations);
      allRecommendations.push(...attempt.dyslexiaIndicatorAnalysis.recommendations);
    });
    const recommendationCounts = {};
    allRecommendations.forEach((rec) => {
      recommendationCounts[rec] = (recommendationCounts[rec] || 0) + 1;
    });
    return Object.entries(recommendationCounts).sort(([, a], [, b]) => b - a).slice(0, 5).map(([recommendation, count]) => ({ recommendation, frequency: count }));
  }
  /**
   * Enriquece os dados da tentativa para os coletores especializados
   */
  enrichAttemptData(attemptData) {
    const targetLetter = attemptData.targetLetter?.letter || attemptData.targetLetter?.id || attemptData.targetLetter;
    const selectedLetter = attemptData.selectedLetter?.letter || attemptData.selectedLetter?.id || attemptData.selectedLetter;
    const reactionTime = attemptData.reactionTime || 0;
    const isCorrect = attemptData.isCorrect || false;
    return {
      // Dados básicos
      ...attemptData,
      targetLetter,
      selectedLetter,
      responseTime: reactionTime,
      recognitionSpeed: reactionTime,
      // Dados para VisualLinguisticCollector
      letterForm: targetLetter,
      // Dados para DyslexiaIndicatorCollector
      letterReversals: this.detectLetterReversals(targetLetter, selectedLetter),
      sequencingDifficulties: [],
      // Array vazio por padrão, pode ser expandido
      processingSpeed: reactionTime,
      errorPatterns: {
        total: 1,
        visual: !isCorrect && this.isVisualError(targetLetter, selectedLetter) ? 1 : 0,
        phonetic: !isCorrect && this.isPhoneticError(targetLetter, selectedLetter) ? 1 : 0
      },
      // Dados para ReadingDevelopmentCollector
      letterFrequency: this.getLetterFrequency(targetLetter),
      developmentalStage: "emergent",
      // Pode ser determinado dinamicamente
      // Dados para LetterConfusionCollector
      confusionPairs: this.getConfusionPairs(targetLetter, selectedLetter)
    };
  }
  /**
   * Enriquece os dados da tentativa para os coletores especializados (suporte V3)
   */
  enrichAttemptDataV3(attemptData) {
    const targetLetter = attemptData.targetLetter?.letter || attemptData.targetLetter?.id || attemptData.targetLetter;
    const selectedLetter = attemptData.selectedLetter?.letter || attemptData.selectedLetter?.id || attemptData.selectedLetter;
    const reactionTime = attemptData.responseTime || 0;
    const isCorrect = attemptData.isCorrect || false;
    return {
      // Dados básicos
      ...attemptData,
      targetLetter,
      selectedLetter,
      responseTime: reactionTime,
      recognitionSpeed: reactionTime,
      // Dados para VisualLinguisticCollector
      letterForm: targetLetter,
      // Dados para DyslexiaIndicatorCollector
      letterReversals: this.detectLetterReversals(targetLetter, selectedLetter),
      sequencingDifficulties: [],
      // Array vazio por padrão, pode ser expandido
      processingSpeed: reactionTime,
      errorPatterns: {
        total: 1,
        visual: !isCorrect && this.isVisualError(targetLetter, selectedLetter) ? 1 : 0,
        phonetic: !isCorrect && this.isPhoneticError(targetLetter, selectedLetter) ? 1 : 0
      },
      // Dados para ReadingDevelopmentCollector
      letterFrequency: this.getLetterFrequency(targetLetter),
      developmentalStage: "emergent",
      // Pode ser determinado dinamicamente
      // Dados para LetterConfusionCollector
      confusionPairs: this.getConfusionPairs(targetLetter, selectedLetter),
      // Dados adicionais para suporte V3
      activityType: attemptData.activityType,
      activityRound: attemptData.activityRound,
      behavioralMetrics: attemptData.behavioralMetrics
    };
  }
  /**
   * Detecta reversões de letras
   */
  detectLetterReversals(target, selected) {
    const commonReversals = {
      "b": ["d", "p", "q"],
      "d": ["b", "p", "q"],
      "p": ["b", "d", "q"],
      "q": ["b", "d", "p"],
      "m": ["w"],
      "w": ["m"],
      "n": ["u"],
      "u": ["n"]
    };
    const targetLower = target?.toLowerCase();
    const selectedLower = selected?.toLowerCase();
    if (commonReversals[targetLower]?.includes(selectedLower)) {
      return [{ target: targetLower, selected: selectedLower, type: "reversal" }];
    }
    return [];
  }
  /**
   * Verifica se é um erro visual
   */
  isVisualError(target, selected) {
    const visuallySimilar = {
      "O": ["Q", "C", "0"],
      "I": ["L", "1", "l"],
      "B": ["8", "D"],
      "S": ["5"],
      "Z": ["2"]
    };
    return visuallySimilar[target?.toUpperCase()]?.includes(selected?.toUpperCase()) || false;
  }
  /**
   * Verifica se é um erro fonético
   */
  isPhoneticError(target, selected) {
    const phoneticallySimilar = {
      "B": ["P", "D"],
      "P": ["B", "D"],
      "D": ["B", "P"],
      "F": ["V"],
      "V": ["F"],
      "C": ["K", "G"],
      "K": ["C", "G"],
      "G": ["C", "K"]
    };
    return phoneticallySimilar[target?.toUpperCase()]?.includes(selected?.toUpperCase()) || false;
  }
  /**
   * Obtém frequência da letra no português
   */
  getLetterFrequency(letter) {
    const frequencies = {
      "A": 14.6,
      "E": 12.6,
      "O": 10.7,
      "S": 7.8,
      "R": 6.5,
      "I": 6.2,
      "N": 5.4,
      "D": 5,
      "M": 4.7,
      "U": 4.6,
      "T": 4.3,
      "C": 3.9,
      "L": 2.8,
      "P": 2.5,
      "V": 1.7,
      "G": 1.3,
      "H": 1.3,
      "Q": 1.2,
      "B": 1,
      "F": 1,
      "Z": 0.4,
      "J": 0.4,
      "X": 0.2,
      "K": 0.02,
      "W": 0.01,
      "Y": 0.01
    };
    return frequencies[letter?.toUpperCase()] || 1;
  }
  /**
   * Obtém pares de confusão para a letra
   */
  getConfusionPairs(target, selected) {
    if (target === selected) return [];
    return [{ letter1: target, letter2: selected, frequency: 1 }];
  }
  /**
   * Controles do sistema de coleta
   */
  activateCollection() {
    this.isActive = true;
  }
  deactivateCollection() {
    this.isActive = false;
  }
  clearHistory() {
    this.collectionHistory = [];
  }
  getCollectionStatus() {
    return {
      isActive: this.isActive,
      totalCollections: this.collectionHistory.length,
      lastCollection: this.collectionHistory[this.collectionHistory.length - 1]?.timestamp || null
    };
  }
  // ==================== MÉTODOS DE ANÁLISE COGNITIVA V3 ====================
  /**
   * Analisa confusão entre letras para Letter Selection
   */
  analyzeLetterConfusion(target, selected) {
    if (target === selected) return null;
    const confusionPatterns = {
      "b-d": ["b", "d"].includes(target) && ["b", "d"].includes(selected),
      "p-q": ["p", "q"].includes(target) && ["p", "q"].includes(selected),
      "m-w": ["m", "w"].includes(target) && ["m", "w"].includes(selected),
      "visually_similar": this.areVisuallySimilar(target, selected),
      "phonetically_similar": this.arePhoneticallySimilar(target, selected)
    };
    return Object.keys(confusionPatterns).filter((pattern) => confusionPatterns[pattern]);
  }
  /**
   * Calcula velocidade de processamento visual
   */
  calculateVisualProcessingSpeed(data) {
    const baseTime = 1e3;
    const speedRatio = baseTime / (data.responseTime || baseTime);
    return Math.min(speedRatio, 3);
  }
  /**
   * Avalia reconhecimento de forma das letras
   */
  assessLetterFormRecognition(data) {
    const complexity = this.getLetterComplexity(data.targetLetter);
    const accuracy = data.isCorrect ? 1 : 0;
    const timeBonus = data.responseTime < 2e3 ? 0.2 : 0;
    return (accuracy + timeBonus) * complexity;
  }
  /**
   * Mede controle atencional
   */
  measureAttentionalControl(data) {
    const attentionSpan = data.behavioralMetrics?.attentionSpan || 0;
    const consistencyBonus = data.isCorrect ? 0.3 : 0;
    return Math.min(attentionSpan / 5e3 + consistencyBonus, 1);
  }
  /**
   * Analisa associação som-letra
   */
  analyzeSoundLetterAssociation(data) {
    return {
      accuracy: data.isCorrect ? 1 : 0,
      responseLatency: data.responseTime,
      associationStrength: this.calculateAssociationStrength(data),
      crossModalEfficiency: this.assessCrossModalEfficiency(data)
    };
  }
  /**
   * Avalia consciência fonética
   */
  assessPhoneticAwareness(data) {
    const phoneticComplexity = this.getPhoneticComplexity(data.targetLetter);
    const accuracy = data.isCorrect ? 1 : 0;
    const speedFactor = Math.max(0.1, 1 - (data.responseTime - 1e3) / 5e3);
    return accuracy * phoneticComplexity * speedFactor;
  }
  /**
   * Mede discriminação auditiva
   */
  measureAuditoryDiscrimination(data) {
    const audioProcessingTime = data.behavioralMetrics?.audioProcessingTime || data.responseTime;
    const discriminationScore = data.isCorrect ? 1 : 0;
    const efficiencyBonus = audioProcessingTime < 3e3 ? 0.2 : 0;
    return discriminationScore + efficiencyBonus;
  }
  /**
   * Avalia processamento cross-modal
   */
  evaluateCrossModalProcessing(data) {
    return {
      auditoryToVisualIntegration: this.assessAuditoryVisualIntegration(data),
      modalSwitchingEfficiency: this.measureModalSwitchingEfficiency(data),
      multisensoryCoherence: this.evaluateMultisensoryCoherence(data)
    };
  }
  /**
   * Calcula precisão de sequência para Word Formation
   */
  calculateSequenceAccuracy(wordData) {
    const targetWord = wordData.targetWord || "";
    const currentWord = wordData.currentWord || [];
    if (targetWord.length === 0) return 0;
    let correctPositions = 0;
    for (let i = 0; i < Math.min(targetWord.length, currentWord.length); i++) {
      if (targetWord[i] === currentWord[i]) correctPositions++;
    }
    return correctPositions / targetWord.length;
  }
  /**
   * Analisa estratégia de construção de palavras
   */
  analyzeConstructionStrategy(data) {
    const buildingTime = data.behavioralMetrics?.wordBuildingTime || data.responseTime;
    if (buildingTime < 2e3) return "rapid_intuitive";
    if (buildingTime < 5e3) return "systematic_planning";
    if (buildingTime < 1e4) return "deliberate_checking";
    return "extended_processing";
  }
  /**
   * Avalia padrões de ortografia
   */
  assessSpellingPatterns(wordData) {
    const targetWord = wordData.targetWord || "";
    const wordComplexity = this.getWordComplexity(targetWord);
    const completionRate = this.calculateSequenceAccuracy(wordData);
    return {
      orthographicComplexity: wordComplexity,
      completionAccuracy: completionRate,
      spellingStrategy: this.identifySpellingStrategy(wordData)
    };
  }
  /**
   * Avalia carga de memória de trabalho
   */
  assessWorkingMemoryLoad(data) {
    const wordLength = data.activityData?.targetWord?.length || 1;
    const processingTime = data.behavioralMetrics?.wordBuildingTime || data.responseTime;
    const accuracy = data.isCorrect ? 1 : 0;
    const loadFactor = wordLength * (processingTime / 1e3);
    const efficiencyScore = accuracy / Math.max(1, loadFactor / 5);
    return Math.min(efficiencyScore, 1);
  }
  /**
   * Mede planejamento sequencial
   */
  measureSequentialPlanning(data) {
    const planningIndicators = {
      systematic: this.detectSystematicApproach(data),
      efficient: this.measurePlanningEfficiency(data),
      adaptive: this.assessPlanningAdaptation(data)
    };
    return Object.values(planningIndicators).reduce((sum, val) => sum + val, 0) / 3;
  }
  /**
   * Avalia consciência ortográfica
   */
  evaluateOrthographicAwareness(wordData) {
    const targetWord = wordData.targetWord || "";
    const spellingDifficulty = this.getOrthographicDifficulty(targetWord);
    const constructionAccuracy = this.calculateSequenceAccuracy(wordData);
    return {
      orthographicKnowledge: spellingDifficulty * constructionAccuracy,
      wordStructureAwareness: this.assessWordStructureAwareness(wordData),
      morphologicalSensitivity: this.evaluateMorphologicalSensitivity(targetWord)
    };
  }
  /**
   * Avalia ordenação alfabética
   */
  assessAlphabeticalOrdering(sequenceData) {
    const sequence = sequenceData.sequence || [];
    const missingIndex = sequenceData.missingIndex || 0;
    return {
      sequenceLength: sequence.length,
      positionDifficulty: this.getPositionDifficulty(missingIndex, sequence.length),
      alphabetKnowledge: this.assessAlphabetKnowledge(sequence)
    };
  }
  /**
   * Mede span de memória sequencial
   */
  measureSequentialMemorySpan(data) {
    const sequenceLength = data.activityData?.sequence?.length || 3;
    const accuracy = data.isCorrect ? 1 : 0;
    const processingTime = data.behavioralMetrics?.sequenceCompletionTime || data.responseTime;
    const baseSpan = sequenceLength * accuracy;
    const timeBonus = processingTime < 3e3 ? 0.5 : 0;
    return baseSpan + timeBonus;
  }
  /**
   * Avalia processamento sequencial
   */
  assessSequentialProcessing(data) {
    return {
      orderingAbility: this.measureOrderingAbility(data),
      sequenceMemory: this.assessSequenceMemory(data),
      patternRecognition: this.evaluatePatternRecognition(data)
    };
  }
  /**
   * Mede completação de padrões
   */
  measurePatternCompletion(sequenceData) {
    const sequence = sequenceData.sequence || [];
    const patternComplexity = this.getPatternComplexity(sequence);
    const completionAccuracy = this.assessPatternCompletionAccuracy(sequenceData);
    return patternComplexity * completionAccuracy;
  }
  /**
   * Avalia habilidade de ordenação
   */
  evaluateOrderingAbility(data) {
    const sequenceData = data.activityData || {};
    const orderingScore = data.isCorrect ? 1 : 0;
    const complexityBonus = this.getSequenceComplexity(sequenceData) * 0.3;
    return Math.min(orderingScore + complexityBonus, 1);
  }
  // ==================== MÉTODOS AUXILIARES ====================
  /**
   * Verifica se duas letras são visualmente similares
   */
  areVisuallySimilar(letter1, letter2) {
    const similarGroups = [
      ["b", "d", "p", "q"],
      ["m", "w"],
      ["n", "u"],
      ["f", "t"],
      ["i", "j", "l"],
      ["o", "c", "e"]
    ];
    return similarGroups.some(
      (group) => group.includes(letter1.toLowerCase()) && group.includes(letter2.toLowerCase())
    );
  }
  /**
   * Verifica se duas letras são foneticamente similares
   */
  arePhoneticallySimilar(letter1, letter2) {
    const phoneticGroups = [
      ["b", "p"],
      ["d", "t"],
      ["g", "k"],
      ["f", "v"],
      ["s", "z"],
      ["m", "n"]
    ];
    return phoneticGroups.some(
      (group) => group.includes(letter1.toLowerCase()) && group.includes(letter2.toLowerCase())
    );
  }
  /**
   * Obtém complexidade visual de uma letra
   */
  getLetterComplexity(letter) {
    const complexityMap = {
      "a": 0.7,
      "b": 0.8,
      "c": 0.6,
      "d": 0.8,
      "e": 0.6,
      "f": 0.7,
      "g": 0.9,
      "h": 0.7,
      "i": 0.3,
      "j": 0.5,
      "k": 0.8,
      "l": 0.3,
      "m": 0.8,
      "n": 0.6,
      "o": 0.5,
      "p": 0.7,
      "q": 0.9,
      "r": 0.7,
      "s": 0.8,
      "t": 0.5,
      "u": 0.6,
      "v": 0.7,
      "w": 0.9,
      "x": 0.8,
      "y": 0.8,
      "z": 0.7
    };
    return complexityMap[letter?.toLowerCase()] || 0.5;
  }
  /**
   * Obtém complexidade fonética de uma letra
   */
  getPhoneticComplexity(letter) {
    const phoneticMap = {
      "a": 0.3,
      "e": 0.3,
      "i": 0.3,
      "o": 0.3,
      "u": 0.3,
      // Vogais mais simples
      "b": 0.6,
      "p": 0.6,
      "d": 0.6,
      "t": 0.6,
      "g": 0.6,
      "k": 0.6,
      // Oclusivas
      "f": 0.7,
      "v": 0.7,
      "s": 0.7,
      "z": 0.7,
      // Fricativas
      "l": 0.5,
      "r": 0.8,
      "m": 0.5,
      "n": 0.5,
      // Líquidas e nasais
      "h": 0.4,
      "j": 0.6,
      "w": 0.6,
      "x": 0.9,
      "y": 0.7
      // Outras
    };
    return phoneticMap[letter?.toLowerCase()] || 0.5;
  }
  /**
   * Calcula força de associação som-letra
   */
  calculateAssociationStrength(data) {
    const accuracy = data.isCorrect ? 1 : 0;
    const speed = Math.max(0.1, 1 - (data.responseTime - 1e3) / 4e3);
    const confidence = this.assessConfidenceLevel(data);
    return accuracy * 0.6 + speed * 0.3 + confidence * 0.1;
  }
  /**
   * Avalia eficiência cross-modal
   */
  assessCrossModalEfficiency(data) {
    const switchingTime = data.behavioralMetrics?.modalSwitchingTime || 0;
    const integrationAccuracy = data.isCorrect ? 1 : 0;
    return integrationAccuracy * Math.max(0.2, 1 - switchingTime / 3e3);
  }
  /**
   * Avalia nível de confiança
   */
  assessConfidenceLevel(data) {
    const responseTime = data.responseTime || 2e3;
    if (responseTime < 500) return 0.6;
    if (responseTime > 8e3) return 0.4;
    if (responseTime < 2e3) return 0.9;
    if (responseTime < 4e3) return 0.7;
    return 0.5;
  }
  /**
   * Avalia sensibilidade fonética avançada
   */
  assessPhoneticSensitivity(phoneticData) {
    const similarityLevel = this.calculatePhoneticSimilarity(phoneticData);
    const discriminationDifficulty = this.getDiscriminationDifficulty(similarityLevel);
    return {
      similarityLevel,
      discriminationDifficulty,
      phoneticAcuity: this.measurePhoneticAcuity(phoneticData)
    };
  }
  /**
   * Mede análise auditiva
   */
  measureAuditoryAnalysis(data) {
    const processingTime = data.responseTime;
    const accuracy = data.isCorrect ? 1 : 0;
    const complexityFactor = this.getAuditoryComplexity(data);
    return {
      analysisSpeed: Math.max(0.1, 1 - (processingTime - 2e3) / 5e3),
      analysisAccuracy: accuracy,
      complexityHandling: accuracy * complexityFactor
    };
  }
  /**
   * Avalia consciência fonética avançada
   */
  assessAdvancedPhoneticAwareness(data) {
    const phoneticData = data.activityData?.phoneticPair || {};
    const awareness = this.assessPhoneticAwareness(data);
    const discriminationBonus = this.assessDiscriminationBonus(phoneticData);
    return Math.min(awareness + discriminationBonus, 1);
  }
  /**
   * Mede profundidade de processamento auditivo
   */
  measureAuditoryProcessingDepth(data) {
    return {
      surfaceProcessing: this.assessSurfaceAuditoryProcessing(data),
      deepProcessing: this.assessDeepAuditoryProcessing(data),
      metacognitiveAwareness: this.assessAuditoryMetacognition(data)
    };
  }
  /**
   * Avalia discriminação linguística
   */
  evaluateLinguisticDiscrimination(phoneticData) {
    const phoneticComplexity = this.getPhoneticPairComplexity(phoneticData);
    const discriminationAccuracy = this.calculateDiscriminationAccuracy(phoneticData);
    return {
      phoneticDiscrimination: discriminationAccuracy,
      linguisticSensitivity: phoneticComplexity * discriminationAccuracy,
      auditoryProcessingSkill: this.assessAuditoryProcessingSkill(phoneticData)
    };
  }
  /**
   * Calcula taxa de detecção para Visual Discrimination
   */
  calculateDetectionRate(visualData) {
    const targets = visualData.visualTargets || [];
    const found = visualData.foundTargets || [];
    if (targets.length === 0) return 0;
    return found.length / targets.length;
  }
  /**
   * Avalia processamento espacial
   */
  assessSpatialProcessing(data) {
    const scanTime = data.behavioralMetrics?.visualScanTime || data.responseTime;
    const accuracy = this.calculateDetectionRate(data.activityData || {});
    const efficiency = accuracy / Math.max(1, scanTime / 1e3);
    return Math.min(efficiency, 1);
  }
  /**
   * Avalia capacidade de atenção visual
   */
  assessVisualAttentionCapacity(data) {
    const attentionSpan = data.behavioralMetrics?.attentionSpan || 0;
    const scanEfficiency = this.measureScanEfficiency(data);
    const focusStability = this.assessFocusStability(data);
    return {
      attentionSpan: Math.min(attentionSpan / 1e4, 1),
      // Normalizado
      scanEfficiency,
      focusStability
    };
  }
  /**
   * Mede habilidade de processamento espacial
   */
  measureSpatialProcessingSkill(data) {
    const visualData = data.activityData || {};
    const gridSize = this.estimateGridSize(visualData);
    const detectionRate = this.calculateDetectionRate(visualData);
    const spatialComplexity = this.assessSpatialComplexity(gridSize);
    return detectionRate * spatialComplexity;
  }
  /**
   * Avalia habilidade de discriminação visual
   */
  evaluateVisualDiscriminationAbility(visualData) {
    return {
      targetDetection: this.calculateDetectionRate(visualData),
      distractorRejection: this.assessDistractorRejection(visualData),
      visualSelectiveAttention: this.measureVisualSelectiveAttention(visualData)
    };
  }
  /**
   * Avalia performance geral
   */
  assessGeneralPerformance(data) {
    const accuracy = data.isCorrect ? 1 : 0;
    const speed = Math.max(0.1, 1 - (data.responseTime - 2e3) / 6e3);
    const consistency = this.assessConsistency(data);
    return accuracy * 0.5 + speed * 0.3 + consistency * 0.2;
  }
  /**
   * Avalia processamento básico
   */
  assessBasicProcessing(data) {
    return {
      responseTime: data.responseTime,
      accuracy: data.isCorrect ? 1 : 0,
      efficiency: this.calculateBasicEfficiency(data)
    };
  }
  // ==================== MÉTODOS AUXILIARES ADICIONAIS ====================
  /**
   * Obtém complexidade de palavra
   */
  getWordComplexity(word) {
    if (!word) return 0;
    const length = word.length;
    const uniqueLetters = new Set(word.toLowerCase()).size;
    const commonWords = ["mama", "papa", "baba", "dada"];
    const isCommon = commonWords.includes(word.toLowerCase());
    let complexity = length * 0.2;
    complexity += uniqueLetters * 0.15;
    if (!isCommon) complexity += 0.3;
    return Math.min(complexity, 1);
  }
  /**
   * Identifica estratégia de soletração
   */
  identifySpellingStrategy(wordData) {
    wordData.targetWord || "";
    const currentWord = wordData.currentWord || [];
    const buildingTime = wordData.buildingTime || 0;
    if (buildingTime < 1e3) return "automatic";
    if (this.isSequentialFilling(currentWord)) return "sequential";
    if (this.isRandomFilling(currentWord)) return "random";
    return "strategic";
  }
  /**
   * Detecta abordagem sistemática
   */
  detectSystematicApproach(data) {
    data.activityData || {};
    const responseTime = data.responseTime || 0;
    if (responseTime > 1e3 && responseTime < 5e3) return 0.8;
    if (responseTime > 500 && responseTime < 8e3) return 0.6;
    return 0.3;
  }
  /**
   * Mede eficiência de planejamento
   */
  measurePlanningEfficiency(data) {
    const accuracy = data.isCorrect ? 1 : 0;
    const responseTime = data.responseTime || 2e3;
    const optimalTime = this.getOptimalResponseTime(data);
    const timeEfficiency = Math.max(0.2, optimalTime / responseTime);
    return accuracy * 0.7 + timeEfficiency * 0.3;
  }
  /**
   * Avalia adaptação de planejamento
   */
  assessPlanningAdaptation(data) {
    const complexity = this.getTaskComplexity(data);
    const responseTime = data.responseTime || 2e3;
    return Math.max(0.3, 1 - Math.abs(responseTime - complexity * 2e3) / 3e3);
  }
  /**
   * Obtém dificuldade ortográfica
   */
  getOrthographicDifficulty(word) {
    if (!word) return 0;
    const complexPatterns = ["ch", "sh", "th", "qu", "ck"];
    const irregularPatterns = ["ough", "eigh", "aigh"];
    let difficulty = word.length * 0.1;
    complexPatterns.forEach((pattern) => {
      if (word.toLowerCase().includes(pattern)) difficulty += 0.2;
    });
    irregularPatterns.forEach((pattern) => {
      if (word.toLowerCase().includes(pattern)) difficulty += 0.4;
    });
    return Math.min(difficulty, 1);
  }
  /**
   * Avalia consciência de estrutura de palavra
   */
  assessWordStructureAwareness(wordData) {
    const targetWord = wordData.targetWord || "";
    const accuracy = this.calculateSequenceAccuracy(wordData);
    const syllableCount = this.estimateSyllableCount(targetWord);
    const structureBonus = syllableCount > 1 ? 0.2 : 0;
    return accuracy + structureBonus;
  }
  /**
   * Avalia sensibilidade morfológica
   */
  evaluateMorphologicalSensitivity(word) {
    if (!word) return 0;
    const morphemes = this.identifyMorphemes(word);
    return Math.min(morphemes.length * 0.3, 1);
  }
  /**
   * Obtém dificuldade de posição
   */
  getPositionDifficulty(index, totalLength) {
    const normalized = index / (totalLength - 1);
    const middleness = 1 - Math.abs(normalized - 0.5) * 2;
    return 0.3 + middleness * 0.7;
  }
  /**
   * Avalia conhecimento do alfabeto
   */
  assessAlphabetKnowledge(sequence) {
    if (!sequence || sequence.length < 2) return 0.5;
    const isCorrectOrder = this.isAlphabeticalOrder(sequence);
    const continuity = this.assessAlphabeticalContinuity(sequence);
    return (isCorrectOrder ? 0.6 : 0.2) + continuity * 0.4;
  }
  /**
   * Mede habilidade de ordenação
   */
  measureOrderingAbility(data) {
    const sequenceData = data.activityData || {};
    const accuracy = data.isCorrect ? 1 : 0;
    const sequenceLength = sequenceData.sequence?.length || 3;
    return accuracy * Math.min(sequenceLength / 5, 1);
  }
  /**
   * Avalia memória de sequência
   */
  assessSequenceMemory(data) {
    const responseTime = data.responseTime || 2e3;
    const accuracy = data.isCorrect ? 1 : 0;
    const memoryScore = accuracy * Math.max(0.2, 1 - (responseTime - 1e3) / 4e3);
    return Math.min(memoryScore, 1);
  }
  /**
   * Avalia reconhecimento de padrões
   */
  evaluatePatternRecognition(data) {
    const sequenceData = data.activityData || {};
    this.identifyPatternType(sequenceData.sequence || []);
    const accuracy = data.isCorrect ? 1 : 0;
    const complexityBonus = this.getPatternComplexity(sequenceData.sequence || []);
    return accuracy * (0.7 + complexityBonus * 0.3);
  }
  /**
   * Obtém complexidade de padrão
   */
  getPatternComplexity(sequence) {
    if (!sequence || sequence.length < 2) return 0.3;
    if (sequence.length >= 5) return 0.9;
    if (sequence.length >= 4) return 0.7;
    if (sequence.length >= 3) return 0.5;
    return 0.3;
  }
  /**
   * Avalia precisão de completação de padrão
   */
  assessPatternCompletionAccuracy(sequenceData) {
    const sequence = sequenceData.sequence || [];
    const missingIndex = sequenceData.missingIndex || 0;
    const expectedLetter = this.getExpectedLetter(sequence, missingIndex);
    const actualResponse = sequenceData.response;
    return expectedLetter === actualResponse ? 1 : 0;
  }
  /**
   * Obtém complexidade de sequência
   */
  getSequenceComplexity(sequenceData) {
    const sequence = sequenceData.sequence || [];
    const length = sequence.length;
    const patternType = this.identifyPatternType(sequence);
    let complexity = length * 0.15;
    if (patternType === "skip") complexity += 0.3;
    if (patternType === "reverse") complexity += 0.4;
    return Math.min(complexity, 1);
  }
  // ==================== MÉTODOS AUXILIARES DE VALIDAÇÃO ====================
  /**
   * Verifica se o preenchimento é sequencial
   */
  isSequentialFilling(currentWord) {
    if (!currentWord || currentWord.length === 0) return false;
    let lastFilledIndex = -1;
    for (let i = 0; i < currentWord.length; i++) {
      if (currentWord[i] && currentWord[i] !== "") {
        if (i <= lastFilledIndex) return false;
        lastFilledIndex = i;
      }
    }
    return true;
  }
  /**
   * Verifica se o preenchimento é aleatório
   */
  isRandomFilling(currentWord) {
    return !this.isSequentialFilling(currentWord);
  }
  /**
   * Obtém tempo de resposta ótimo
   */
  getOptimalResponseTime(data) {
    const complexity = this.getTaskComplexity(data);
    return 1e3 + complexity * 2e3;
  }
  /**
   * Obtém complexidade da tarefa
   */
  getTaskComplexity(data) {
    const activityType = data.activityType || "letter_selection";
    const complexityMap = {
      "letter_selection": 0.3,
      "sound_matching": 0.5,
      "word_formation": 0.8,
      "sequence_recognition": 0.6,
      "phonetic_discrimination": 0.9,
      "visual_discrimination": 0.7
    };
    return complexityMap[activityType] || 0.5;
  }
  /**
   * Estima contagem de sílabas
   */
  estimateSyllableCount(word) {
    if (!word) return 0;
    const vowels = "aeiouAEIOU";
    let count = 0;
    let previousWasVowel = false;
    for (let char of word) {
      const isVowel = vowels.includes(char);
      if (isVowel && !previousWasVowel) count++;
      previousWasVowel = isVowel;
    }
    return Math.max(count, 1);
  }
  /**
   * Identifica morfemas
   */
  identifyMorphemes(word) {
    if (!word) return [];
    const prefixes = ["re", "un", "pre", "dis"];
    const suffixes = ["ing", "ed", "er", "est", "s"];
    const morphemes = [word];
    prefixes.forEach((prefix) => {
      if (word.toLowerCase().startsWith(prefix)) {
        morphemes.push(prefix);
      }
    });
    suffixes.forEach((suffix) => {
      if (word.toLowerCase().endsWith(suffix)) {
        morphemes.push(suffix);
      }
    });
    return morphemes;
  }
  /**
   * Verifica ordem alfabética
   */
  isAlphabeticalOrder(sequence) {
    if (!sequence || sequence.length < 2) return true;
    for (let i = 1; i < sequence.length; i++) {
      if (sequence[i].charCodeAt(0) <= sequence[i - 1].charCodeAt(0)) {
        return false;
      }
    }
    return true;
  }
  /**
   * Avalia continuidade alfabética
   */
  assessAlphabeticalContinuity(sequence) {
    if (!sequence || sequence.length < 2) return 0.5;
    let continuousCount = 0;
    for (let i = 1; i < sequence.length; i++) {
      if (sequence[i].charCodeAt(0) === sequence[i - 1].charCodeAt(0) + 1) {
        continuousCount++;
      }
    }
    return continuousCount / (sequence.length - 1);
  }
  /**
   * Identifica tipo de padrão
   */
  identifyPatternType(sequence) {
    if (!sequence || sequence.length < 2) return "simple";
    const isConsecutive = this.isConsecutivePattern(sequence);
    const isSkip = this.isSkipPattern(sequence);
    const isReverse = this.isReversePattern(sequence);
    if (isReverse) return "reverse";
    if (isSkip) return "skip";
    if (isConsecutive) return "consecutive";
    return "complex";
  }
  /**
   * Verifica padrão consecutivo
   */
  isConsecutivePattern(sequence) {
    for (let i = 1; i < sequence.length; i++) {
      if (sequence[i].charCodeAt(0) !== sequence[i - 1].charCodeAt(0) + 1) {
        return false;
      }
    }
    return true;
  }
  /**
   * Verifica padrão de salto
   */
  isSkipPattern(sequence) {
    if (sequence.length < 2) return false;
    const diff = sequence[1].charCodeAt(0) - sequence[0].charCodeAt(0);
    if (diff <= 1) return false;
    for (let i = 2; i < sequence.length; i++) {
      if (sequence[i].charCodeAt(0) - sequence[i - 1].charCodeAt(0) !== diff) {
        return false;
      }
    }
    return true;
  }
  /**
   * Verifica padrão reverso
   */
  isReversePattern(sequence) {
    for (let i = 1; i < sequence.length; i++) {
      if (sequence[i].charCodeAt(0) >= sequence[i - 1].charCodeAt(0)) {
        return false;
      }
    }
    return true;
  }
  /**
   * Obtém letra esperada
   */
  getExpectedLetter(sequence, missingIndex) {
    if (!sequence || missingIndex >= sequence.length) return null;
    if (missingIndex === 0) {
      return String.fromCharCode(sequence[1].charCodeAt(0) - 1);
    } else if (missingIndex === sequence.length - 1) {
      return String.fromCharCode(sequence[sequence.length - 2].charCodeAt(0) + 1);
    } else {
      const prevChar = sequence[missingIndex - 1];
      const nextChar = sequence[missingIndex + 1];
      const expectedCode = (prevChar.charCodeAt(0) + nextChar.charCodeAt(0)) / 2;
      return String.fromCharCode(Math.round(expectedCode));
    }
  }
  /**
   * Avalia complexidade contextual
   */
  assessContextualComplexity(data) {
    const activityType = data.activityType || "letter_selection";
    const gameLevel = data.currentLevel || 1;
    const sessionLength = data.sessionLength || 1;
    let complexity = 0.3;
    if (activityType === "phonetic_discrimination") complexity += 0.4;
    if (activityType === "word_formation") complexity += 0.3;
    if (activityType === "sequence_recognition") complexity += 0.2;
    complexity += Math.min(gameLevel * 0.1, 0.3);
    if (sessionLength > 10) complexity += 0.1;
    return Math.min(complexity, 1);
  }
  /**
   * Avalia processamento auditivo superficial
   */
  assessSurfaceAuditoryProcessing(data) {
    const responseTime = data.responseTime || 2e3;
    return responseTime < 1500 ? 0.8 : 0.4;
  }
  /**
   * Avalia processamento auditivo profundo
   */
  assessDeepAuditoryProcessing(data) {
    const responseTime = data.responseTime || 2e3;
    const accuracy = data.isCorrect ? 1 : 0;
    const timeScore = responseTime > 1500 && responseTime < 4e3 ? 0.8 : 0.4;
    return (timeScore + accuracy) / 2;
  }
  /**
   * Avalia metacognição auditiva
   */
  assessAuditoryMetacognition(data) {
    const responseTime = data.responseTime || 2e3;
    return responseTime > 3e3 ? 0.7 : 0.3;
  }
  /**
   * Calcula precisão de discriminação
   */
  calculateDiscriminationAccuracy(phoneticData) {
    const similarity = this.calculatePhoneticSimilarity(phoneticData);
    const wasCorrect = phoneticData.correct || false;
    return wasCorrect ? (1 + similarity) / 2 : similarity * 0.3;
  }
  /**
   * Avalia habilidade de processamento auditivo
   */
  assessAuditoryProcessingSkill(phoneticData) {
    const complexity = this.getPhoneticPairComplexity(phoneticData);
    const accuracy = this.calculateDiscriminationAccuracy(phoneticData);
    return complexity * accuracy;
  }
  /**
   * Obtém complexidade de par fonético
   */
  getPhoneticPairComplexity(phoneticData) {
    const pair = phoneticData.pair || [];
    if (pair.length < 2) return 0.3;
    const similarity = this.calculatePhoneticSimilarity(phoneticData);
    const individualComplexity = pair.map((sound) => this.getPhoneticComplexity(sound));
    const avgComplexity = individualComplexity.reduce((sum, c) => sum + c, 0) / individualComplexity.length;
    return (similarity + avgComplexity) / 2;
  }
  // ==================== MÉTODOS DE COLETA ESPECÍFICOS POR ATIVIDADE ====================
  /**
   * Coleta dados específicos para Letter Selection
   */
  async collectLetterSelectionData(enrichedData) {
    return {
      letterConfusion: this.analyzeLetterConfusion(enrichedData.targetLetter, enrichedData.selectedLetter),
      visualProcessingSpeed: this.calculateVisualProcessingSpeed(enrichedData),
      letterFormRecognition: this.assessLetterFormRecognition(enrichedData),
      attentionalControl: this.measureAttentionalControl(enrichedData)
    };
  }
  /**
   * Coleta dados específicos para Sound Matching
   */
  async collectSoundMatchingData(enrichedData) {
    return {
      soundLetterAssociation: this.analyzeSoundLetterAssociation(enrichedData),
      phoneticAwareness: this.assessPhoneticAwareness(enrichedData),
      auditoryDiscrimination: this.measureAuditoryDiscrimination(enrichedData),
      crossModalProcessing: this.evaluateCrossModalProcessing(enrichedData)
    };
  }
  /**
   * Coleta dados específicos para Word Formation
   */
  async collectWordFormationData(enrichedData) {
    return {
      sequenceAccuracy: this.calculateSequenceAccuracy(enrichedData.activityData || {}),
      constructionStrategy: this.analyzeConstructionStrategy(enrichedData),
      spellingPatterns: this.assessSpellingPatterns(enrichedData.activityData || {}),
      workingMemoryLoad: this.assessWorkingMemoryLoad(enrichedData),
      sequentialPlanning: this.measureSequentialPlanning(enrichedData),
      orthographicAwareness: this.evaluateOrthographicAwareness(enrichedData.activityData || {})
    };
  }
  /**
   * Coleta dados específicos para Sequence Recognition
   */
  async collectSequenceRecognitionData(enrichedData) {
    return {
      alphabeticalOrdering: this.assessAlphabeticalOrdering(enrichedData.activityData || {}),
      sequentialMemorySpan: this.measureSequentialMemorySpan(enrichedData),
      sequentialProcessing: this.assessSequentialProcessing(enrichedData),
      patternCompletion: this.measurePatternCompletion(enrichedData.activityData || {}),
      orderingAbility: this.evaluateOrderingAbility(enrichedData)
    };
  }
  /**
   * Coleta dados específicos para Phonetic Discrimination
   */
  async collectPhoneticDiscriminationData(enrichedData) {
    return {
      phoneticSensitivity: this.assessPhoneticSensitivity(enrichedData.activityData || {}),
      auditoryAnalysis: this.measureAuditoryAnalysis(enrichedData),
      phoneticAwareness: this.assessAdvancedPhoneticAwareness(enrichedData),
      auditoryProcessingDepth: this.measureAuditoryProcessingDepth(enrichedData),
      linguisticDiscrimination: this.evaluateLinguisticDiscrimination(enrichedData.activityData || {})
    };
  }
  /**
   * Coleta dados específicos para Visual Discrimination
   */
  async collectVisualDiscriminationData(enrichedData) {
    return {
      detectionRate: this.calculateDetectionRate(enrichedData.activityData || {}),
      spatialProcessing: this.assessSpatialProcessing(enrichedData),
      visualAttentionCapacity: this.assessVisualAttentionCapacity(enrichedData),
      spatialProcessingSkill: this.measureSpatialProcessingSkill(enrichedData),
      visualDiscriminationAbility: this.evaluateVisualDiscriminationAbility(enrichedData.activityData || {})
    };
  }
  /**
   * Coleta dados padrão para atividades não especificadas
   */
  async collectDefaultData(enrichedData) {
    return {
      generalPerformance: this.assessGeneralPerformance(enrichedData),
      basicProcessing: this.assessBasicProcessing(enrichedData),
      contextualComplexity: this.assessContextualComplexity(enrichedData)
    };
  }
}
const isBrowser = typeof window !== "undefined" && typeof window.document !== "undefined";
const logger = isBrowser ? {
  info: (...args) => console.info("%c📝 [LETTER-RECOGNITION]", "color: #2196F3", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  error: (...args) => console.error("%c🔴 [LETTER-ERROR]", "color: #F44336", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  warn: (...args) => console.warn("%c🟡 [LETTER-WARN]", "color: #FF9800", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  debug: (...args) => console.debug("%c⚪ [LETTER-DEBUG]", "color: #9E9E9E", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  therapeutic: (...args) => console.info("%c🏥 [LETTER-THERAPEUTIC]", "color: #4CAF50", (/* @__PURE__ */ new Date()).toISOString(), ...args)
} : {
  info: (...args) => console.info("📝 [LETTER-RECOGNITION]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  error: (...args) => console.error("🔴 [LETTER-ERROR]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  warn: (...args) => console.warn("🟡 [LETTER-WARN]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  debug: (...args) => console.debug("⚪ [LETTER-DEBUG]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  therapeutic: (...args) => console.info("🏥 [LETTER-THERAPEUTIC]", (/* @__PURE__ */ new Date()).toISOString(), ...args)
};
class LetterRecognitionProcessors extends IGameProcessor {
  constructor(loggerInstance = null) {
    const config = {
      category: "language_processing",
      therapeuticFocus: ["letter_recognition", "phonetic_awareness", "visual_discrimination"],
      cognitiveAreas: ["language_processing", "visual_processing", "memory"],
      thresholds: {
        accuracy: 70,
        responseTime: 3e3,
        engagement: 75
      }
    };
    super(config);
    this.logger = loggerInstance && typeof loggerInstance.therapeutic === "function" ? loggerInstance : logger;
    this.config = {
      category: "language",
      therapeuticFocus: ["letter_recognition", "phonological_awareness", "pre_literacy"],
      cognitiveAreas: ["language_processing", "visual_processing", "memory"],
      thresholds: {
        accuracy: 75,
        responseTime: 2500,
        engagement: 65
      }
    };
    this.logger.info("📝 Processadores Letter Recognition inicializados");
  }
  /**
   * Processa dados do jogo Letter Recognition
   * @param {Object} gameData - Dados coletados do jogo
   * @param {Object} collectorsHub - Hub de coletores específico do jogo
   * @returns {Promise<Object>} Análise terapêutica específica
   */
  async processGameData(gameData, collectorsHub = null) {
    try {
      this.logger?.info("🎮 Processando dados LetterRecognition", {
        sessionId: gameData.sessionId,
        userId: gameData.userId,
        collectorsActive: collectorsHub ? Object.keys(collectorsHub.collectors || {}).length : 0
      });
      const metrics = await this.processLetterRecognitionMetrics(gameData, gameData);
      const therapeuticAnalysis = this.generateTherapeuticAnalysis(metrics, gameData);
      const processedMetrics = this.processMetricsForDatabase(metrics, gameData);
      return {
        success: true,
        gameType: this.gameType,
        metrics,
        therapeuticAnalysis,
        processedMetrics,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    } catch (error) {
      this.logger?.error("❌ Erro ao processar dados LetterRecognition:", error);
      return {
        success: false,
        gameType: this.gameType,
        error: error.message,
        fallbackAnalysis: this.generateFallbackTherapeuticAnalysis(gameData),
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    }
  }
  /**
   * Processa coletores com Circuit Breaker para resiliência
   * @param {Object} collectorsHub - Hub de coletores
   * @param {Object} gameData - Dados do jogo
   * @returns {Promise<Object>} Resultados dos coletores
   */
  async processCollectorsWithCircuitBreaker(collectorsHub, gameData) {
    if (!collectorsHub || !collectorsHub.collectors) {
      this.logger?.warn("⚠️ LetterRecognition: Hub de coletores não disponível");
      return { collectors: {}, warning: "No collectors available" };
    }
    const results = {};
    const collectors = collectorsHub.collectors;
    for (const [collectorName, collector] of Object.entries(collectors)) {
      try {
        if (collector && typeof collector.analyze === "function") {
          this.logger?.debug("🎮 Processando coletor: " + collectorName);
          results[collectorName] = await this.processWithTimeout(
            () => collector.analyze(gameData),
            5e3,
            // 5 segundos timeout
            collectorName + " timeout"
          );
        } else {
          this.logger?.warn("⚠️ Coletor " + collectorName + " não tem método analyze");
          results[collectorName] = { error: "No analyze method" };
        }
      } catch (error) {
        this.logger?.error("❌ Erro no coletor " + collectorName + ":", error);
        results[collectorName] = {
          error: error.message,
          fallback: this.generateFallbackMetrics(collectorName, gameData)
        };
      }
    }
    return {
      collectors: results,
      processedAt: (/* @__PURE__ */ new Date()).toISOString(),
      gameType: "LetterRecognition"
    };
  }
  /**
   * Processa com timeout para evitar travamentos
   */
  async processWithTimeout(fn, timeout, errorMsg) {
    return Promise.race([
      fn(),
      new Promise(
        (_, reject) => setTimeout(() => reject(new Error(errorMsg)), timeout)
      )
    ]);
  }
  /**
   * Gera métricas de fallback em caso de erro
   */
  generateFallbackMetrics(collectorName, gameData) {
    return {
      fallback: true,
      collector: collectorName,
      basicScore: 50,
      confidence: "low",
      note: "Generated due to collector error"
    };
  }
  /**
   * Gera análise terapêutica completa
   * @param {Object} metrics - Métricas processadas
   * @param {Object} gameData - Dados do jogo
   * @returns {Object} Análise terapêutica
   */
  generateTherapeuticAnalysis(metrics, gameData) {
    try {
      const analysis = {
        // Análise comportamental
        behavioral: {
          engagement: this.calculateEngagementScore(gameData),
          persistence: this.calculatePersistenceScore(gameData),
          adaptability: this.calculateAdaptabilityScore(gameData),
          frustrationTolerance: this.calculateFrustrationTolerance(gameData),
          socialInteraction: this.calculateSocialInteractionScore(gameData)
        },
        // Análise cognitiva
        cognitive: {
          attention: this.calculateAttentionScore(gameData),
          memory: this.calculateMemoryScore(gameData),
          processingSpeed: this.calculateProcessingSpeedScore(gameData),
          executiveFunction: this.calculateExecutiveFunctionScore(gameData),
          visualProcessing: this.calculateVisualProcessingScore(gameData)
        },
        // Análise sensorial
        sensory: {
          visualPerception: this.calculateVisualPerceptionScore(gameData),
          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),
          tactileProcessing: this.calculateTactileProcessingScore(gameData),
          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)
        },
        // Análise motora
        motor: {
          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),
          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),
          coordination: this.calculateCoordinationScore(gameData),
          motorPlanning: this.calculateMotorPlanningScore(gameData)
        },
        // Recomendações terapêuticas
        recommendations: this.generateTherapeuticRecommendations(metrics, gameData),
        // Indicadores de progresso
        progressIndicators: this.generateProgressIndicators(metrics, gameData),
        // Insights específicos do jogo
        gameSpecificInsights: this.generateGameSpecificInsights(metrics, gameData),
        // Metadados
        metadata: {
          analysisTimestamp: (/* @__PURE__ */ new Date()).toISOString(),
          gameType: this.gameType,
          analysisVersion: "3.0.0",
          confidenceScore: this.calculateAnalysisConfidenceScore(metrics, gameData)
        }
      };
      return analysis;
    } catch (error) {
      this.logger?.error("❌ Erro ao gerar análise terapêutica:", error);
      return this.generateFallbackTherapeuticAnalysis(gameData);
    }
  }
  /**
   * Métodos de cálculo de scores terapêuticos
   */
  calculateEngagementScore(gameData) {
    const interactions = gameData.interactions || [];
    const totalTime = gameData.totalTime || 1e3;
    const completionRate = gameData.completionRate || 0;
    let score = 50;
    if (interactions.length > 0) {
      score += Math.min(30, interactions.length * 2);
    }
    if (totalTime > 3e4) {
      score += 10;
    }
    score += completionRate * 10;
    return Math.max(0, Math.min(100, score));
  }
  calculatePersistenceScore(gameData) {
    const attempts = gameData.attempts || 1;
    const errors = gameData.errors || 0;
    const completion = gameData.completion || 0;
    let score = 50;
    if (attempts > 1 && completion > 0.5) {
      score += 20;
    }
    if (errors > 0 && completion > 0.8) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateAdaptabilityScore(gameData) {
    const difficultyChanges = gameData.difficultyChanges || 0;
    const adaptationSuccess = gameData.adaptationSuccess || 0;
    let score = 50;
    if (difficultyChanges > 0) {
      score += adaptationSuccess * 20;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateFrustrationTolerance(gameData) {
    const errors = gameData.errors || 0;
    const quitEarly = gameData.quitEarly || false;
    const completion = gameData.completion || 0;
    let score = 70;
    if (errors > 3 && !quitEarly) {
      score += 15;
    }
    if (completion > 0.8) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateSocialInteractionScore(gameData) {
    const engagement = this.calculateEngagementScore(gameData);
    return Math.max(30, Math.min(80, engagement * 0.8));
  }
  calculateAttentionScore(gameData) {
    const focusTime = gameData.focusTime || 0;
    const distractions = gameData.distractions || 0;
    const responseTime = gameData.averageResponseTime || 3e3;
    let score = 50;
    if (focusTime > 6e4) {
      score += 20;
    }
    if (distractions < 2) {
      score += 15;
    }
    if (responseTime < 2e3) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateMemoryScore(gameData) {
    const accuracy = gameData.accuracy || 0;
    const patterns = gameData.patterns || [];
    let score = 50;
    if (accuracy > 70) {
      score += 25;
    }
    if (patterns.length > 3) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateProcessingSpeedScore(gameData) {
    const responseTime = gameData.averageResponseTime || 3e3;
    const accuracy = gameData.accuracy || 0;
    let score = 50;
    if (responseTime < 1500 && accuracy > 60) {
      score += 30;
    } else if (responseTime < 2500) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateExecutiveFunctionScore(gameData) {
    const planningEvidence = gameData.planningEvidence || 0;
    const inhibitionControl = gameData.inhibitionControl || 0;
    const workingMemory = gameData.workingMemory || 0;
    const score = (planningEvidence + inhibitionControl + workingMemory) / 3;
    return Math.max(0, Math.min(100, score));
  }
  calculateVisualProcessingScore(gameData) {
    const visualTasks = gameData.visualTasks || 0;
    const visualAccuracy = gameData.visualAccuracy || 0;
    let score = 50;
    if (visualTasks > 5 && visualAccuracy > 70) {
      score += 25;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateVisualPerceptionScore(gameData) {
    return this.calculateVisualProcessingScore(gameData);
  }
  calculateAuditoryProcessingScore(gameData) {
    const auditoryTasks = gameData.auditoryTasks || 0;
    const auditoryAccuracy = gameData.auditoryAccuracy || 50;
    let score = 50;
    if (auditoryTasks > 0) {
      score = auditoryAccuracy;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateTactileProcessingScore(gameData) {
    const touchInteractions = gameData.touchInteractions || 0;
    const touchAccuracy = gameData.touchAccuracy || 50;
    let score = 50;
    if (touchInteractions > 3) {
      score = touchAccuracy;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateSensoryIntegrationScore(gameData) {
    const visual = this.calculateVisualPerceptionScore(gameData);
    const auditory = this.calculateAuditoryProcessingScore(gameData);
    const tactile = this.calculateTactileProcessingScore(gameData);
    return (visual + auditory + tactile) / 3;
  }
  calculateFineMotorSkillsScore(gameData) {
    const precision = gameData.precision || 50;
    const motorControl = gameData.motorControl || 50;
    return (precision + motorControl) / 2;
  }
  calculateGrossMotorSkillsScore(gameData) {
    const movements = gameData.movements || 0;
    const coordination = gameData.coordination || 50;
    let score = 50;
    if (movements > 10) {
      score = coordination;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateCoordinationScore(gameData) {
    const eyeHandCoordination = gameData.eyeHandCoordination || 50;
    const bilateralCoordination = gameData.bilateralCoordination || 50;
    return (eyeHandCoordination + bilateralCoordination) / 2;
  }
  calculateMotorPlanningScore(gameData) {
    const planningSteps = gameData.planningSteps || 0;
    const executionSuccess = gameData.executionSuccess || 0;
    let score = 50;
    if (planningSteps > 0) {
      score = executionSuccess;
    }
    return Math.max(0, Math.min(100, score));
  }
  generateTherapeuticRecommendations(metrics, gameData) {
    const recommendations = [];
    const engagement = this.calculateEngagementScore(gameData);
    if (engagement < 50) {
      recommendations.push({
        category: "engagement",
        priority: "high",
        recommendation: "Implementar estratégias de motivação e gamificação",
        rationale: "Baixo engajamento detectado"
      });
    }
    const attention = this.calculateAttentionScore(gameData);
    if (attention < 50) {
      recommendations.push({
        category: "attention",
        priority: "medium",
        recommendation: "Exercícios de foco e concentração",
        rationale: "Dificuldades atencionais identificadas"
      });
    }
    const processing = this.calculateProcessingSpeedScore(gameData);
    if (processing < 50) {
      recommendations.push({
        category: "processing",
        priority: "medium",
        recommendation: "Atividades para melhorar velocidade de processamento",
        rationale: "Processamento lento identificado"
      });
    }
    return recommendations;
  }
  generateProgressIndicators(metrics, gameData) {
    return {
      overallProgress: this.calculateOverallProgress(gameData),
      strengthAreas: this.identifyStrengthAreas(gameData),
      challengeAreas: this.identifyChallengeAreas(gameData),
      developmentGoals: this.generateDevelopmentGoals(gameData),
      milestones: this.generateMilestones(gameData)
    };
  }
  generateGameSpecificInsights(metrics, gameData) {
    return {
      gameType: this.gameType,
      specificMetrics: metrics,
      gamePerformance: this.calculateGamePerformance(gameData),
      adaptationNeeds: this.identifyAdaptationNeeds(gameData)
    };
  }
  calculateAnalysisConfidenceScore(metrics, gameData) {
    let confidence = 50;
    const dataPoints = Object.keys(gameData).length;
    if (dataPoints > 10) confidence += 20;
    else if (dataPoints > 5) confidence += 10;
    const metricsCount = Object.keys(metrics).length;
    if (metricsCount > 5) confidence += 20;
    else if (metricsCount > 3) confidence += 10;
    const sessionTime = gameData.totalTime || 0;
    if (sessionTime > 6e4) confidence += 10;
    return Math.max(0, Math.min(100, confidence));
  }
  generateFallbackTherapeuticAnalysis(gameData) {
    return {
      behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50, socialInteraction: 50 },
      cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50, visualProcessing: 50 },
      sensory: { visualPerception: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },
      motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },
      recommendations: [],
      progressIndicators: { overallProgress: 50, strengthAreas: [], challengeAreas: [], developmentGoals: [], milestones: [] },
      gameSpecificInsights: { gameType: this.gameType, specificMetrics: {}, gamePerformance: 50, adaptationNeeds: [] },
      metadata: { analysisTimestamp: (/* @__PURE__ */ new Date()).toISOString(), gameType: this.gameType, analysisVersion: "3.0.0", confidenceScore: 30 }
    };
  }
  calculateOverallProgress(gameData) {
    const accuracy = gameData.accuracy || 0;
    const completion = gameData.completion || 0;
    const engagement = this.calculateEngagementScore(gameData);
    return (accuracy + completion + engagement) / 3;
  }
  identifyStrengthAreas(gameData) {
    const strengths = [];
    if (gameData.accuracy > 80) strengths.push("Precisão");
    if (gameData.averageResponseTime < 2e3) strengths.push("Velocidade de resposta");
    if (this.calculateEngagementScore(gameData) > 70) strengths.push("Engajamento");
    return strengths;
  }
  identifyChallengeAreas(gameData) {
    const challenges = [];
    if (gameData.accuracy < 50) challenges.push("Precisão");
    if (gameData.averageResponseTime > 4e3) challenges.push("Velocidade de resposta");
    if (this.calculateEngagementScore(gameData) < 40) challenges.push("Engajamento");
    return challenges;
  }
  generateDevelopmentGoals(gameData) {
    const goals = [];
    if (gameData.accuracy < 70) {
      goals.push("Melhorar precisão para 70%+");
    }
    if (gameData.averageResponseTime > 3e3) {
      goals.push("Reduzir tempo de resposta para menos de 3 segundos");
    }
    return goals;
  }
  generateMilestones(gameData) {
    return [
      { milestone: "Primeira sessão completa", achieved: gameData.completion > 0.8 },
      { milestone: "Precisão acima de 50%", achieved: gameData.accuracy > 50 },
      { milestone: "Engajamento sustentado", achieved: this.calculateEngagementScore(gameData) > 60 }
    ];
  }
  calculateGamePerformance(gameData) {
    const accuracy = gameData.accuracy || 0;
    const completion = gameData.completion || 0;
    const efficiency = gameData.efficiency || 0;
    return (accuracy + completion + efficiency) / 3;
  }
  identifyAdaptationNeeds(gameData) {
    const needs = [];
    if (gameData.accuracy < 40) {
      needs.push("Reduzir dificuldade");
    }
    if (gameData.averageResponseTime > 5e3) {
      needs.push("Aumentar tempo limite");
    }
    if (this.calculateEngagementScore(gameData) < 30) {
      needs.push("Aumentar elementos motivacionais");
    }
    return needs;
  }
  /**
   * Processa métricas para estrutura padronizada do banco de dados
   * @param {Object} metrics - Métricas processadas
   * @param {Object} gameData - Dados do jogo
   * @returns {Object} Métricas processadas para banco
   */
  processMetricsForDatabase(metrics, gameData) {
    try {
      return {
        // Métricas básicas
        basic: {
          accuracy: gameData.accuracy || 0,
          responseTime: gameData.averageResponseTime || 0,
          completion: gameData.completion || 0,
          score: gameData.score || 0,
          duration: gameData.totalTime || 0,
          attempts: gameData.attempts || 1,
          errors: gameData.errors || 0
        },
        // Métricas cognitivas
        cognitive: {
          attention: this.calculateAttentionScore(gameData),
          memory: this.calculateMemoryScore(gameData),
          processingSpeed: this.calculateProcessingSpeedScore(gameData),
          executiveFunction: this.calculateExecutiveFunctionScore(gameData)
        },
        // Métricas comportamentais
        behavioral: {
          engagement: this.calculateEngagementScore(gameData),
          persistence: this.calculatePersistenceScore(gameData),
          adaptability: this.calculateAdaptabilityScore(gameData),
          frustrationTolerance: this.calculateFrustrationTolerance(gameData)
        },
        // Métricas sensoriais
        sensory: {
          visualProcessing: this.calculateVisualProcessingScore(gameData),
          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),
          tactileProcessing: this.calculateTactileProcessingScore(gameData),
          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)
        },
        // Métricas motoras
        motor: {
          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),
          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),
          coordination: this.calculateCoordinationScore(gameData),
          motorPlanning: this.calculateMotorPlanningScore(gameData)
        },
        // Métricas específicas do jogo
        gameSpecific: metrics,
        // Metadados
        metadata: {
          gameType: this.gameType,
          processingTimestamp: (/* @__PURE__ */ new Date()).toISOString(),
          version: "3.0.0"
        }
      };
    } catch (error) {
      this.logger?.error("❌ Erro ao processar métricas para banco:", error);
      return {
        basic: { accuracy: 0, responseTime: 0, completion: 0, score: 0, duration: 0, attempts: 1, errors: 0 },
        cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50 },
        behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50 },
        sensory: { visualProcessing: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },
        motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },
        gameSpecific: metrics,
        metadata: { gameType: this.gameType, processingTimestamp: (/* @__PURE__ */ new Date()).toISOString(), version: "3.0.0" }
      };
    }
  }
  /**
   * Métodos de análise para LetterRecognition
   */
  analyzeLetterRecognitionPrimary(gameData) {
    const { totalCorrect = 0, totalAttempts = 1, interactions = [] } = gameData;
    return {
      accuracy: Math.round(totalCorrect / totalAttempts * 100),
      totalAttempts,
      totalCorrect,
      primaryScore: this.calculatePrimaryScore(interactions),
      efficiency: this.calculateEfficiency(interactions)
    };
  }
  analyzeLetterRecognitionSecondary(gameData) {
    const { interactions = [] } = gameData;
    return {
      secondaryAccuracy: this.calculateSecondaryAccuracy(interactions),
      adaptability: this.calculateAdaptability(interactions),
      consistency: this.calculateConsistency(interactions)
    };
  }
  analyzeLetterRecognitionTiming(gameData) {
    const { interactions = [] } = gameData;
    const responseTimes = interactions.map((i) => i.responseTime || 0).filter((t) => t > 0);
    if (responseTimes.length === 0) {
      return { average: 0, median: 0, variability: 0, pattern: "insufficient_data" };
    }
    const average = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const sorted = responseTimes.sort((a, b) => a - b);
    const median = sorted[Math.floor(sorted.length / 2)];
    return {
      average: Math.round(average),
      median: Math.round(median),
      min: Math.min(...responseTimes),
      max: Math.max(...responseTimes),
      variability: Math.round(this.calculateVariability(responseTimes)),
      pattern: "normal"
    };
  }
  analyzeLetterRecognitionPatterns(gameData) {
    const { interactions = [] } = gameData;
    return {
      totalPatterns: interactions.length,
      correctPatterns: interactions.filter((i) => i.correct).length,
      patternTypes: this.identifyPatternTypes(interactions),
      errorPatterns: this.identifyErrorPatterns(interactions)
    };
  }
  analyzeLetterRecognitionBehavior(gameData) {
    const { interactions = [] } = gameData;
    return {
      persistence: this.calculatePersistence(interactions),
      adaptability: this.calculateAdaptability(interactions),
      engagement: this.calculateEngagementScore(gameData)
    };
  }
  analyzeLetterRecognitionCognition(gameData) {
    const { interactions = [] } = gameData;
    return {
      executiveFunction: this.calculateExecutiveFunction(interactions),
      workingMemory: this.calculateWorkingMemory(interactions),
      processingSpeed: this.calculateProcessingSpeed(interactions)
    };
  }
  generateLetterRecognitionRecommendations(gameData) {
    const recommendations = [];
    const accuracy = gameData.accuracy || 0;
    const responseTime = gameData.averageResponseTime || 0;
    if (accuracy < 60) {
      recommendations.push({
        type: "accuracy_improvement",
        priority: "high",
        description: "Exercícios para melhorar precisão em LetterRecognition"
      });
    }
    if (responseTime > 5e3) {
      recommendations.push({
        type: "speed_improvement",
        priority: "medium",
        description: "Atividades para melhorar velocidade de processamento"
      });
    }
    return recommendations;
  }
  // Métodos auxiliares
  calculatePrimaryScore(interactions) {
    const correctInteractions = interactions.filter((i) => i.correct);
    return correctInteractions.length / Math.max(1, interactions.length) * 100;
  }
  calculateSecondaryAccuracy(interactions) {
    return this.calculatePrimaryScore(interactions);
  }
  calculateEfficiency(interactions) {
    const totalTime = interactions.reduce((sum, i) => sum + (i.responseTime || 0), 0);
    const correctCount = interactions.filter((i) => i.correct).length;
    return totalTime > 0 ? correctCount / totalTime * 1e3 : 0;
  }
  identifyPatternTypes(interactions) {
    const types = {};
    interactions.forEach((i) => {
      const type = i.patternType || "unknown";
      types[type] = (types[type] || 0) + 1;
    });
    return types;
  }
  identifyErrorPatterns(interactions) {
    const errors = interactions.filter((i) => !i.correct);
    return {
      totalErrors: errors.length,
      errorFrequency: errors.length / Math.max(1, interactions.length) * 100
    };
  }
  calculateVariability(values) {
    if (values.length < 2) return 0;
    const mean = values.reduce((sum, v) => sum + v, 0) / values.length;
    const variance = values.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }
  /**
   * Processa métricas específicas do LetterRecognition
   * @param {Object} gameData - Dados do jogo LetterRecognition
   * @param {Object} sessionData - Dados da sessão
   * @returns {Object} Métricas processadas
   */
  async processLetterRecognitionMetrics(gameData, sessionData) {
    try {
      this.logger?.info("📝 Processando métricas LetterRecognition...", {
        sessionId: sessionData.sessionId
      });
      const metrics = {
        // Métricas de reconhecimento de letras
        letterRecognition: this.analyzeLetterRecognitionPrimary(gameData),
        // Análise de consciência fonética
        phoneticAwareness: this.analyzeLetterRecognitionSecondary(gameData),
        // Discriminação visual
        visualDiscrimination: this.analyzeLetterRecognitionTertiary(gameData),
        // Padrões de reconhecimento
        recognitionPatterns: this.analyzeLetterRecognitionPatterns(gameData),
        // Análise comportamental específica
        recognitionBehavior: this.analyzeLetterRecognitionBehavior(gameData),
        // Indicadores terapêuticos
        therapeuticIndicators: this.generateLetterRecognitionTherapeuticIndicators(gameData),
        // Recomendações específicas
        recommendations: this.generateLetterRecognitionRecommendations(gameData)
      };
      this.logger?.info("✅ Métricas LetterRecognition processadas", {
        accuracy: metrics.letterRecognition.accuracy,
        recognitionCount: metrics.recognitionPatterns.totalPatterns
      });
      return metrics;
    } catch (error) {
      this.logger?.error("❌ Erro ao processar métricas LetterRecognition:", error);
      throw error;
    }
  }
  /**
   * Gera indicadores terapêuticos específicos para LetterRecognition
   */
  generateLetterRecognitionTherapeuticIndicators(gameData) {
    const { interactions = [], accuracy = 0 } = gameData;
    return {
      cognitiveLoad: this.assessCognitiveLoad(gameData),
      therapeuticGoals: this.identifyLetterRecognitionTherapeuticGoals(gameData),
      interventionNeeds: this.identifyLetterRecognitionInterventionNeeds(gameData),
      progressMarkers: this.generateLetterRecognitionProgressMarkers(gameData)
    };
  }
  /**
   * Gera recomendações específicas para LetterRecognition
   */
  /**
   * Identifica objetivos terapêuticos específicos
   */
  identifyLetterRecognitionTherapeuticGoals(gameData) {
    const { interactions = [], accuracy = 0 } = gameData;
    const goals = [];
    if (accuracy < 70) {
      goals.push("Melhorar reconhecimento de letras");
    }
    if (interactions.length > 0) {
      const avgTime = interactions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / interactions.length;
      if (avgTime > 3e3) {
        goals.push("Acelerar processamento visual");
      }
    }
    return goals;
  }
  /**
   * Identifica necessidades de intervenção específicas
   */
  identifyLetterRecognitionInterventionNeeds(gameData) {
    const { interactions = [], accuracy = 0 } = gameData;
    const needs = [];
    if (accuracy < 50) {
      needs.push("Intervenção intensiva em reconhecimento de letras");
    }
    return needs;
  }
  /**
   * Gera marcadores de progresso específicos
   */
  generateLetterRecognitionProgressMarkers(gameData) {
    const { interactions = [] } = gameData;
    return {
      accuracyTrend: this.calculateAccuracyTrend(interactions),
      speedImprovement: this.calculateSpeedImprovement(interactions),
      consistencyScore: this.calculateConsistencyScore(interactions),
      learningRate: this.calculateLearningRate(interactions)
    };
  }
  /**
   * Avalia carga cognitiva
   */
  assessCognitiveLoad(gameData) {
    const { interactions = [], averageResponseTime = 0 } = gameData;
    if (interactions.length === 0) return "low";
    const avgTime = averageResponseTime || interactions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / interactions.length;
    const errorRate = interactions.filter((i) => !i.correct).length / interactions.length;
    if (avgTime > 4e3 || errorRate > 0.7) return "high";
    if (avgTime > 2500 || errorRate > 0.4) return "medium";
    return "low";
  }
  /**
   * Calcula adaptabilidade baseada nas interações
   */
  calculateAdaptability(interactions) {
    if (interactions.length < 2) return 50;
    let adaptations = 0;
    for (let i = 1; i < interactions.length; i++) {
      if (!interactions[i - 1].correct && interactions[i].correct) {
        adaptations++;
      }
    }
    return adaptations / Math.max(1, interactions.length - 1) * 100;
  }
  /**
   * Calcula persistência baseada nas interações
   */
  calculatePersistence(interactions) {
    if (interactions.length === 0) return 50;
    const errorRate = interactions.filter((i) => !i.correct).length / interactions.length;
    return Math.max(0, 100 - errorRate * 100);
  }
  /**
   * Calcula consistência baseada nas interações
   */
  calculateConsistency(interactions) {
    if (interactions.length === 0) return 50;
    const responseTimes = interactions.map((i) => i.responseTime || 0);
    const avgTime = responseTimes.reduce((sum, t) => sum + t, 0) / responseTimes.length;
    if (avgTime === 0) return 50;
    const variance = responseTimes.reduce((sum, t) => sum + Math.pow(t - avgTime, 2), 0) / responseTimes.length;
    const stdDev = Math.sqrt(variance);
    return Math.max(0, 100 - stdDev / avgTime * 100);
  }
  /**
   * Calcula acurácia secundária
   */
  /**
   * Análise terciária de reconhecimento de letras
   */
  analyzeLetterRecognitionTertiary(gameData) {
    const { interactions = [] } = gameData;
    return {
      visualDiscrimination: this.calculateVisualDiscrimination(interactions),
      perceptualSpeed: this.calculatePerceptualSpeed(interactions),
      visualMemory: this.calculateVisualMemory(interactions)
    };
  }
  /**
   * Calcula discriminação visual
   */
  calculateVisualDiscrimination(interactions) {
    if (interactions.length === 0) return 50;
    const similarLetterErrors = interactions.filter(
      (i) => !i.correct && this.areSimilarLetters(i.letterId, i.response)
    );
    const discriminationRate = 1 - similarLetterErrors.length / interactions.length;
    return discriminationRate * 100;
  }
  /**
   * Calcula velocidade perceptual
   */
  calculatePerceptualSpeed(interactions) {
    if (interactions.length === 0) return 50;
    const times = interactions.map((i) => i.responseTime || 0).filter((t) => t > 0);
    if (times.length === 0) return 50;
    const avgTime = times.reduce((sum, t) => sum + t, 0) / times.length;
    return Math.max(0, Math.min(100, 100 - avgTime / 50));
  }
  /**
   * Calcula memória visual
   */
  calculateVisualMemory(interactions) {
    if (interactions.length === 0) return 50;
    const letterPerformance = {};
    interactions.forEach((i) => {
      if (!letterPerformance[i.letterId]) {
        letterPerformance[i.letterId] = { correct: 0, total: 0 };
      }
      letterPerformance[i.letterId].total++;
      if (i.correct) letterPerformance[i.letterId].correct++;
    });
    const consistencyScores = Object.values(letterPerformance).map(
      (perf) => perf.correct / perf.total
    );
    return consistencyScores.length > 0 ? consistencyScores.reduce((sum, score) => sum + score, 0) / consistencyScores.length * 100 : 50;
  }
  /**
   * Verifica se duas letras são visualmente similares
   */
  areSimilarLetters(letter1, letter2) {
    const similarPairs = [
      ["b", "d"],
      ["p", "q"],
      ["m", "w"],
      ["n", "u"],
      ["6", "9"],
      ["E", "F"],
      ["C", "G"],
      ["O", "Q"]
    ];
    return similarPairs.some(
      (pair) => pair[0] === letter1 && pair[1] === letter2 || pair[1] === letter1 && pair[0] === letter2
    );
  }
  /**
   * Calcula tendência de acurácia
   */
  calculateAccuracyTrend(interactions) {
    if (interactions.length < 2) return "insufficient_data";
    const halfPoint = Math.floor(interactions.length / 2);
    const firstHalf = interactions.slice(0, halfPoint);
    const secondHalf = interactions.slice(halfPoint);
    const firstAccuracy = firstHalf.filter((i) => i.correct).length / firstHalf.length;
    const secondAccuracy = secondHalf.filter((i) => i.correct).length / secondHalf.length;
    if (secondAccuracy > firstAccuracy + 0.1) return "improving";
    if (secondAccuracy < firstAccuracy - 0.1) return "declining";
    return "stable";
  }
  /**
   * Calcula melhoria de velocidade
   */
  calculateSpeedImprovement(interactions) {
    if (interactions.length < 2) return 0;
    const halfPoint = Math.floor(interactions.length / 2);
    const firstHalf = interactions.slice(0, halfPoint);
    const secondHalf = interactions.slice(halfPoint);
    const firstAvgTime = firstHalf.reduce((sum, i) => sum + (i.responseTime || 0), 0) / firstHalf.length;
    const secondAvgTime = secondHalf.reduce((sum, i) => sum + (i.responseTime || 0), 0) / secondHalf.length;
    return firstAvgTime > 0 ? (firstAvgTime - secondAvgTime) / firstAvgTime * 100 : 0;
  }
  /**
   * Calcula taxa de aprendizado
   */
  calculateLearningRate(interactions) {
    if (interactions.length < 3) return 0;
    let improvementCount = 0;
    for (let i = 1; i < interactions.length; i++) {
      if (interactions[i].correct && !interactions[i - 1].correct) {
        improvementCount++;
      }
    }
    return improvementCount / (interactions.length - 1) * 100;
  }
  /**
   * Calcula score de consistência
   */
  calculateConsistencyScore(interactions) {
    if (interactions.length === 0) return 0;
    const responseTimes = interactions.map((i) => i.responseTime || 0);
    const avgTime = responseTimes.reduce((sum, t) => sum + t, 0) / responseTimes.length;
    if (avgTime === 0) return 50;
    const variance = responseTimes.reduce((sum, t) => sum + Math.pow(t - avgTime, 2), 0) / responseTimes.length;
    const stdDev = Math.sqrt(variance);
    return Math.max(0, 100 - stdDev / avgTime * 100);
  }
  // Métodos específicos para processamento de métricas
}
const LETTERS_BANK = [
  { id: "a", letter: "A", sound: "A", example: "🐝 Abelha", color: "#FF6B6B", difficulty: 1 },
  { id: "b", letter: "B", sound: "Bê", example: "⚽ Bola", color: "#4ECDC4", difficulty: 1 },
  { id: "c", letter: "C", sound: "Cê", example: "🏠 Casa", color: "#45B7D1", difficulty: 1 },
  { id: "d", letter: "D", sound: "Dê", example: "🎲 Dado", color: "#FFA07A", difficulty: 1 },
  { id: "e", letter: "E", sound: "É", example: "⭐ Estrela", color: "#98D8C8", difficulty: 2 },
  { id: "f", letter: "F", sound: "Efe", example: "🌸 Flor", color: "#F7DC6F", difficulty: 2 },
  { id: "g", letter: "G", sound: "Gê", example: "🐱 Gato", color: "#BB8FCE", difficulty: 2 },
  { id: "h", letter: "H", sound: "Agá", example: "🏨 Hotel", color: "#85C1E9", difficulty: 3 },
  { id: "i", letter: "I", sound: "I", example: "🏝️ Ilha", color: "#F8C471", difficulty: 3 },
  { id: "j", letter: "J", sound: "Jota", example: "🌻 Jardim", color: "#82E0AA", difficulty: 3 },
  { id: "k", letter: "K", sound: "Cá", example: "🥝 Kiwi", color: "#AED6F1", difficulty: 4 },
  { id: "l", letter: "L", sound: "Ele", example: "🦁 Leão", color: "#F5B7B1", difficulty: 4 }
];
({
  EASY: {
    letters: LETTERS_BANK.filter((l) => l.difficulty <= 2)
  },
  MEDIUM: {
    letters: LETTERS_BANK.filter((l) => l.difficulty <= 3)
  }
});
class LetterRecognitionMetrics {
  constructor(backendConnector = null) {
    this.backendConnector = backendConnector;
    this.collectorsHub = new LetterRecognitionCollectorsHub();
    this.metrics = {
      sessionId: null,
      userId: null,
      gameType: "LetterRecognition",
      startTime: null,
      endTime: null,
      interactions: [],
      accuracy: 0,
      timeSpent: 0,
      difficultyLevel: 1,
      mode: "letter",
      // 'letter' ou 'word'
      errorTypes: {
        letterConfusion: 0,
        visualError: 0,
        auditoryError: 0
      },
      repetitions: 0,
      // 🎯 DADOS V3 - SISTEMA DE ATIVIDADES EXPANDIDO
      currentActivity: "letter_selection",
      activityRound: 1,
      activitiesCompleted: [],
      activityData: {},
      // 📊 MÉTRICAS COMPORTAMENTAIS V3
      behavioralMetrics: {
        reactionTime: [],
        accuracy: [],
        attentionSpan: 0,
        improvementRate: 0,
        activitySpecific: {
          wordBuildingTime: [],
          visualScanTime: [],
          audioProcessingTime: [],
          sequenceCompletionTime: [],
          phoneticDiscriminationTime: [],
          letterSequenceAccuracy: [],
          targetDetectionRate: [],
          phoneticAccuracy: [],
          patternRecognition: [],
          auditoryAnalysisSkill: []
        }
      },
      // 📊 DADOS AVANÇADOS COLETADOS
      linguisticData: {},
      cognitiveData: {},
      errorPatterns: {},
      advancedAnalysis: {},
      // 🎮 ESTATÍSTICAS POR ATIVIDADE
      activityStats: {
        letter_selection: { attempts: 0, correct: 0, avgTime: 0 },
        sound_matching: { attempts: 0, correct: 0, avgTime: 0 },
        word_formation: { attempts: 0, correct: 0, avgTime: 0 },
        sequence_recognition: { attempts: 0, correct: 0, avgTime: 0 },
        phonetic_discrimination: { attempts: 0, correct: 0, avgTime: 0 },
        visual_discrimination: { attempts: 0, correct: 0, avgTime: 0 }
      }
    };
  }
  // Conectar ao backend via useUnifiedGameLogic
  connectToBackend(backendConnector) {
    this.backendConnector = backendConnector;
  }
  // Sincronizar métricas com backend
  async syncWithBackend(data) {
    if (this.backendConnector && this.backendConnector.updateMetrics) {
      try {
        await this.backendConnector.updateMetrics(data);
      } catch (error) {
        console.warn("Erro ao sincronizar métricas com backend:", error);
      }
    }
  }
  startSession(sessionId, userId, difficultyLevel = 1, mode = "letter", activityType = "letter_selection") {
    this.metrics.sessionId = sessionId;
    this.metrics.userId = userId;
    this.metrics.startTime = (/* @__PURE__ */ new Date()).toISOString();
    this.metrics.difficultyLevel = difficultyLevel;
    this.metrics.mode = mode;
    this.metrics.currentActivity = activityType;
    this.metrics.activityRound = 1;
    this.metrics.activitiesCompleted = [];
    this.metrics.interactions = [];
    this.metrics.errorTypes = { letterConfusion: 0, visualError: 0, auditoryError: 0 };
    this.metrics.repetitions = 0;
    this.metrics.behavioralMetrics = {
      reactionTime: [],
      accuracy: [],
      attentionSpan: 0,
      improvementRate: 0,
      activitySpecific: {
        wordBuildingTime: [],
        visualScanTime: [],
        audioProcessingTime: [],
        sequenceCompletionTime: [],
        phoneticDiscriminationTime: [],
        letterSequenceAccuracy: [],
        targetDetectionRate: [],
        phoneticAccuracy: [],
        patternRecognition: [],
        auditoryAnalysisSkill: []
      }
    };
    this.metrics.activityStats = {
      letter_selection: { attempts: 0, correct: 0, avgTime: 0 },
      sound_matching: { attempts: 0, correct: 0, avgTime: 0 },
      word_formation: { attempts: 0, correct: 0, avgTime: 0 },
      sequence_recognition: { attempts: 0, correct: 0, avgTime: 0 },
      phonetic_discrimination: { attempts: 0, correct: 0, avgTime: 0 },
      visual_discrimination: { attempts: 0, correct: 0, avgTime: 0 }
    };
  }
  endSession() {
    this.metrics.endTime = (/* @__PURE__ */ new Date()).toISOString();
    this.metrics.timeSpent = new Date(this.metrics.endTime) - new Date(this.metrics.startTime);
    this.metrics.accuracy = this.calculateAccuracy();
    return this.getMetrics();
  }
  recordInteraction(actionTypeOrData, element, selected, correct, duration) {
    let interaction;
    if (typeof actionTypeOrData === "object" && actionTypeOrData !== null) {
      interaction = {
        actionType: actionTypeOrData.activityType || "letter_selection",
        element: "letter",
        selected: actionTypeOrData.selectedLetter,
        correct: actionTypeOrData.targetLetter,
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        duration: actionTypeOrData.responseTime || Date.now(),
        // Dados V3 específicos
        activityType: actionTypeOrData.activityType,
        activityRound: actionTypeOrData.activityRound,
        activityData: actionTypeOrData.activityData,
        behavioralMetrics: actionTypeOrData.behavioralMetrics,
        isCorrect: actionTypeOrData.isCorrect
      };
      this.updateActivityStats(actionTypeOrData.activityType, actionTypeOrData.isCorrect, actionTypeOrData.responseTime);
      this.updateBehavioralMetrics(actionTypeOrData);
    } else {
      interaction = {
        actionType: actionTypeOrData,
        element,
        selected,
        correct,
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        duration
      };
    }
    this.metrics.interactions.push(interaction);
    this.performAdvancedDataCollection(interaction);
    if (selected !== correct) {
      if (element === "letter") {
        this.metrics.errorTypes.letterConfusion++;
      } else {
        this.metrics.errorTypes.visualError++;
      }
    }
    this.metrics.accuracy = this.calculateAccuracy();
  }
  /**
   * 🎯 Atualiza estatísticas específicas da atividade
   */
  updateActivityStats(activityType, isCorrect, responseTime) {
    if (!this.metrics.activityStats[activityType]) {
      this.metrics.activityStats[activityType] = { attempts: 0, correct: 0, avgTime: 0 };
    }
    const stats = this.metrics.activityStats[activityType];
    stats.attempts++;
    if (isCorrect) stats.correct++;
    const totalTime = stats.avgTime * (stats.attempts - 1) + responseTime;
    stats.avgTime = totalTime / stats.attempts;
  }
  /**
   * 📊 Atualiza métricas comportamentais V3
   */
  updateBehavioralMetrics(data) {
    const metrics = this.metrics.behavioralMetrics;
    if (data.responseTime) {
      metrics.reactionTime.push(data.responseTime);
    }
    if (typeof data.isCorrect === "boolean") {
      metrics.accuracy.push(data.isCorrect ? 1 : 0);
    }
    if (data.behavioralMetrics?.attentionSpan) {
      metrics.attentionSpan = data.behavioralMetrics.attentionSpan;
    }
    const activitySpecific = metrics.activitySpecific;
    const activityType = data.activityType;
    switch (activityType) {
      case "word_formation":
        if (data.behavioralMetrics?.wordBuildingTime) {
          activitySpecific.wordBuildingTime.push(data.behavioralMetrics.wordBuildingTime);
        }
        if (data.activityData?.completionRate !== void 0) {
          activitySpecific.letterSequenceAccuracy.push(data.activityData.completionRate);
        }
        break;
      case "visual_discrimination":
        if (data.behavioralMetrics?.visualScanTime) {
          activitySpecific.visualScanTime.push(data.behavioralMetrics.visualScanTime);
        }
        if (data.activityData?.detectionRate !== void 0) {
          activitySpecific.targetDetectionRate.push(data.activityData.detectionRate);
        }
        break;
      case "sound_matching":
        if (data.behavioralMetrics?.audioProcessingTime) {
          activitySpecific.audioProcessingTime.push(data.behavioralMetrics.audioProcessingTime);
        }
        activitySpecific.phoneticAccuracy.push(data.isCorrect ? 1 : 0);
        break;
      case "sequence_recognition":
        if (data.behavioralMetrics?.sequenceCompletionTime) {
          activitySpecific.sequenceCompletionTime.push(data.behavioralMetrics.sequenceCompletionTime);
        }
        activitySpecific.patternRecognition.push(data.isCorrect ? 1 : 0);
        break;
      case "phonetic_discrimination":
        if (data.responseTime) {
          activitySpecific.phoneticDiscriminationTime.push(data.responseTime);
        }
        activitySpecific.auditoryAnalysisSkill.push(data.isCorrect ? 1 : 0);
        break;
    }
  }
  /**
   * 🎮 Registra rotação de atividade
   */
  recordActivityRotation(fromActivity, toActivity, round) {
    if (fromActivity && !this.metrics.activitiesCompleted.includes(fromActivity)) {
      this.metrics.activitiesCompleted.push(fromActivity);
    }
    this.metrics.currentActivity = toActivity;
    this.metrics.activityRound = round;
    this.metrics.interactions.push({
      actionType: "activity_rotation",
      element: "system",
      selected: toActivity,
      correct: toActivity,
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      duration: 0,
      fromActivity,
      toActivity,
      round
    });
  }
  /**
   * 🚀 SISTEMA AVANÇADO DE COLETA DE DADOS - VERSÃO 3.0
   * Utiliza o hub integrado de coletores especializados
   */
  async performAdvancedDataCollection(interaction) {
    try {
      const attemptData = {
        sessionId: this.metrics.sessionId,
        userId: this.metrics.userId,
        targetLetter: interaction.correct,
        selectedLetter: interaction.selected,
        isCorrect: interaction.selected === interaction.correct,
        responseTime: interaction.duration,
        difficultyLevel: this.metrics.difficultyLevel,
        mode: this.metrics.mode,
        // Dados contextuais
        letterForm: interaction.correct,
        recognitionSpeed: interaction.duration,
        context: "isolated",
        // ou 'word', 'sentence', 'mixed'
        soundMapping: true,
        // Placeholder - determinar baseado no jogo
        visualSimilarity: this.calculateVisualSimilarity(interaction.correct, interaction.selected),
        phoneticSimilarity: this.calculatePhoneticSimilarity(interaction.correct, interaction.selected),
        // Dados de sessão
        alphabetKnowledge: this.calculateAlphabetKnowledge(),
        phoneticAwareness: this.calculatePhoneticAwareness(),
        sessionData: {
          accuracy: this.metrics.accuracy,
          averageResponseTime: this.calculateAverageResponseTime(),
          engagement: 0.8,
          // Placeholder - pode ser calculado
          progressionPattern: 0.1
          // Placeholder
        },
        // Dados para análise de dislexia
        letterReversals: this.identifyLetterReversals(),
        processingSpeed: interaction.duration,
        sequencingDifficulties: this.identifySequencingDifficulties(),
        errorPatterns: {
          total: this.metrics.interactions.filter((i) => i.selected !== i.correct).length
        }
      };
      const comprehensiveAnalysis = await this.collectorsHub.collectComprehensiveData(attemptData);
      if (comprehensiveAnalysis) {
        this.metrics.advancedAnalytics = {
          ...this.metrics.advancedAnalytics,
          lastAnalysis: comprehensiveAnalysis,
          consolidatedMetrics: comprehensiveAnalysis.consolidatedMetrics
        };
        console.log("🎯 Coleta avançada realizada:", {
          phoneticComplexity: comprehensiveAnalysis.consolidatedMetrics.phoneticComplexity,
          hasConfusion: comprehensiveAnalysis.consolidatedMetrics.hasLetterConfusion,
          developmentStage: comprehensiveAnalysis.consolidatedMetrics.developmentStage,
          dyslexiaRisk: comprehensiveAnalysis.consolidatedMetrics.dyslexiaRiskLevel
        });
      }
    } catch (error) {
      console.error("Erro na coleta avançada de dados:", error);
    }
  }
  // Métodos auxiliares para os coletores
  calculateVisualSimilarity(target, selected) {
    const similarPairs = { "b": ["d", "p"], "d": ["b", "q"], "p": ["q", "b"], "q": ["p", "d"] };
    return similarPairs[target?.toLowerCase()]?.includes(selected?.toLowerCase()) ? 0.8 : 0.1;
  }
  calculatePhoneticSimilarity(target, selected) {
    const phoneticGroups = {
      "plosives": ["b", "d", "g", "p", "t", "k"],
      "fricatives": ["f", "v", "s", "z"],
      "vowels": ["a", "e", "i", "o", "u"]
    };
    for (const group of Object.values(phoneticGroups)) {
      if (group.includes(target?.toLowerCase()) && group.includes(selected?.toLowerCase())) {
        return 0.7;
      }
    }
    return 0.1;
  }
  calculateAlphabetKnowledge() {
    const correctInteractions = this.metrics.interactions.filter((i) => i.selected === i.correct);
    return this.metrics.interactions.length > 0 ? correctInteractions.length / this.metrics.interactions.length : 0.5;
  }
  calculatePhoneticAwareness() {
    return this.calculateAlphabetKnowledge() * 0.8;
  }
  calculateAverageResponseTime() {
    if (this.metrics.interactions.length === 0) return 2500;
    const total = this.metrics.interactions.reduce((sum, i) => sum + (i.duration || 0), 0);
    return total / this.metrics.interactions.length;
  }
  identifyLetterReversals() {
    const reversals = [];
    this.metrics.interactions.forEach((interaction) => {
      if (this.isReversal(interaction.correct, interaction.selected)) {
        reversals.push({ target: interaction.correct, selected: interaction.selected });
      }
    });
    return reversals;
  }
  identifySequencingDifficulties() {
    return [];
  }
  isReversal(target, selected) {
    const reversalPairs = [["b", "d"], ["p", "q"], ["u", "n"], ["m", "w"]];
    return reversalPairs.some(
      (pair) => pair[0] === target?.toLowerCase() && pair[1] === selected?.toLowerCase() || pair[1] === target?.toLowerCase() && pair[0] === selected?.toLowerCase()
    );
  }
  recordRepetition() {
    this.metrics.repetitions++;
  }
  calculateAccuracy() {
    const total = this.metrics.interactions.length;
    if (total === 0) return 0;
    const correct = this.metrics.interactions.filter((i) => i.selected === i.correct).length;
    return correct / total * 100;
  }
  getMetrics() {
    return { ...this.metrics };
  }
  /**
   * 📊 RELATÓRIOS AVANÇADOS DOS COLETORES
   * Acessa dados dos algoritmos especializados
   */
  getAdvancedReport() {
    try {
      const progressReport = this.collectorsHub.generateProgressReport();
      const collectionStatus = this.collectorsHub.getCollectionStatus();
      return {
        basicMetrics: this.getMetrics(),
        advancedAnalytics: {
          progressReport,
          collectionStatus,
          lastAnalysis: this.metrics.advancedAnalytics?.lastAnalysis,
          consolidatedMetrics: this.metrics.advancedAnalytics?.consolidatedMetrics
        },
        summary: {
          totalInteractions: this.metrics.interactions.length,
          accuracy: this.calculateAccuracy(),
          averageResponseTime: this.calculateAverageResponseTime(),
          developmentStage: this.metrics.advancedAnalytics?.consolidatedMetrics?.developmentStage || "unknown",
          phoneticComplexity: this.metrics.advancedAnalytics?.consolidatedMetrics?.phoneticComplexity || 1,
          dyslexiaRiskLevel: this.metrics.advancedAnalytics?.consolidatedMetrics?.dyslexiaRiskLevel || "low"
        }
      };
    } catch (error) {
      console.error("Erro ao gerar relatório avançado:", error);
      return { error: "Falha ao gerar relatório avançado" };
    }
  }
  /**
   * 📈 Gera relatório detalhado das atividades V3
   */
  generateActivityReport() {
    const report = {
      sessionSummary: {
        sessionId: this.metrics.sessionId,
        totalTime: this.metrics.timeSpent,
        totalInteractions: this.metrics.interactions.length,
        overallAccuracy: this.metrics.accuracy,
        activitiesCompleted: this.metrics.activitiesCompleted.length,
        currentActivity: this.metrics.currentActivity
      },
      activityBreakdown: {},
      behavioralInsights: this.analyzeBehavioralMetrics(),
      recommendations: this.generateRecommendations()
    };
    for (const [activityType, stats] of Object.entries(this.metrics.activityStats)) {
      if (stats.attempts > 0) {
        report.activityBreakdown[activityType] = {
          attempts: stats.attempts,
          accuracy: stats.correct / stats.attempts * 100,
          averageTime: stats.avgTime,
          difficulty: this.assessActivityDifficulty(activityType),
          performance: this.assessActivityPerformance(activityType)
        };
      }
    }
    return report;
  }
  /**
   * 🧠 Analisa métricas comportamentais
   */
  analyzeBehavioralMetrics() {
    const metrics = this.metrics.behavioralMetrics;
    return {
      reactionTimeAnalysis: {
        average: this.calculateAverage(metrics.reactionTime),
        trend: this.calculateTrend(metrics.reactionTime),
        consistency: this.calculateConsistency(metrics.reactionTime)
      },
      accuracyProgression: {
        overall: this.calculateAverage(metrics.accuracy),
        improvement: this.calculateImprovement(metrics.accuracy),
        stability: this.calculateStability(metrics.accuracy)
      },
      attentionMetrics: {
        span: metrics.attentionSpan,
        sustainability: this.assessAttentionSustainability()
      },
      activitySpecificInsights: this.analyzeActivitySpecificMetrics()
    };
  }
  /**
   * 🎯 Analisa métricas específicas por atividade
   */
  analyzeActivitySpecificMetrics() {
    const specific = this.metrics.behavioralMetrics.activitySpecific;
    const insights = {};
    if (specific.wordBuildingTime.length > 0) {
      insights.wordFormation = {
        averageBuildTime: this.calculateAverage(specific.wordBuildingTime),
        sequenceAccuracy: this.calculateAverage(specific.letterSequenceAccuracy),
        constructionStrategy: this.identifyConstructionStrategy(specific.wordBuildingTime)
      };
    }
    if (specific.visualScanTime.length > 0) {
      insights.visualDiscrimination = {
        averageScanTime: this.calculateAverage(specific.visualScanTime),
        detectionAccuracy: this.calculateAverage(specific.targetDetectionRate),
        visualEfficiency: this.calculateVisualEfficiency()
      };
    }
    if (specific.audioProcessingTime.length > 0) {
      insights.soundMatching = {
        audioProcessingSpeed: this.calculateAverage(specific.audioProcessingTime),
        phoneticAccuracy: this.calculateAverage(specific.phoneticAccuracy),
        auditoryProcessingSkill: this.assessAuditoryProcessing()
      };
    }
    if (specific.sequenceCompletionTime.length > 0) {
      insights.sequenceRecognition = {
        completionSpeed: this.calculateAverage(specific.sequenceCompletionTime),
        patternAccuracy: this.calculateAverage(specific.patternRecognition),
        sequentialThinking: this.assessSequentialThinking()
      };
    }
    if (specific.phoneticDiscriminationTime.length > 0) {
      insights.phoneticDiscrimination = {
        discriminationSpeed: this.calculateAverage(specific.phoneticDiscriminationTime),
        auditoryAccuracy: this.calculateAverage(specific.auditoryAnalysisSkill),
        phoneticSensitivity: this.assessPhoneticSensitivity()
      };
    }
    return insights;
  }
  /**
   * 💡 Gera recomendações personalizadas
   */
  generateRecommendations() {
    const recommendations = [];
    const activityStats = this.metrics.activityStats;
    for (const [activityType, stats] of Object.entries(activityStats)) {
      if (stats.attempts > 0) {
        const accuracy = stats.correct / stats.attempts * 100;
        if (accuracy < 60) {
          recommendations.push({
            type: "improvement",
            activity: activityType,
            message: `Pratique mais ${this.getActivityName(activityType)} para melhorar a precisão`,
            priority: "high"
          });
        } else if (accuracy > 90 && stats.avgTime < 2e3) {
          recommendations.push({
            type: "advancement",
            activity: activityType,
            message: `Excelente desempenho em ${this.getActivityName(activityType)}! Pronto para maior dificuldade`,
            priority: "medium"
          });
        }
        if (stats.avgTime > 5e3) {
          recommendations.push({
            type: "speed",
            activity: activityType,
            message: `Tente responder mais rapidamente em ${this.getActivityName(activityType)}`,
            priority: "low"
          });
        }
      }
    }
    const behavioralMetrics = this.metrics.behavioralMetrics;
    if (behavioralMetrics.reactionTime.length > 5) {
      const avgTime = this.calculateAverage(behavioralMetrics.reactionTime);
      const consistency = this.calculateConsistency(behavioralMetrics.reactionTime);
      if (consistency < 0.7) {
        recommendations.push({
          type: "consistency",
          message: "Trabalhe na consistência das respostas para melhor performance",
          priority: "medium"
        });
      }
      if (avgTime > 4e3) {
        recommendations.push({
          type: "processing_speed",
          message: "Pratique exercícios de velocidade de processamento",
          priority: "medium"
        });
      }
    }
    return recommendations;
  }
  /**
   * 📊 Gera relatório completo das métricas V3
   */
  generateReport() {
    const totalInteractions = this.data.interactions.length;
    const correctInteractions = this.data.interactions.filter((i) => i.isCorrect).length;
    const accuracy = totalInteractions > 0 ? correctInteractions / totalInteractions : 0;
    const responseTimes = this.data.interactions.map((i) => i.responseTime).filter((t) => t && t > 0);
    const averageResponseTime = responseTimes.length > 0 ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0;
    const activityStats = {};
    Object.keys(this.data.activityStats).forEach((activity) => {
      const stats = this.data.activityStats[activity];
      activityStats[activity] = {
        totalAttempts: stats.attempts,
        correctAttempts: stats.correct,
        accuracy: stats.attempts > 0 ? stats.correct / stats.attempts : 0,
        averageTime: stats.totalTime > 0 ? stats.totalTime / stats.attempts : 0,
        improvements: this.calculateActivityImprovement(activity)
      };
    });
    const behavioralSummary = this.consolidateBehavioralMetrics();
    return {
      // Métricas gerais
      totalInteractions,
      accuracy,
      averageResponseTime,
      // Estatísticas por atividade
      activityStats,
      // Resumo comportamental
      behavioralSummary,
      // Progressão temporal
      progressOverTime: this.calculateProgressOverTime(),
      // Recomendações
      recommendations: this.generateRecommendations(accuracy, activityStats, behavioralSummary),
      // Metadados do relatório
      generatedAt: (/* @__PURE__ */ new Date()).toISOString(),
      sessionDuration: this.getSessionDuration(),
      activitiesPlayed: Object.keys(activityStats).length
    };
  }
  /**
   * Calcula melhoria em uma atividade específica
   */
  calculateActivityImprovement(activityType) {
    const interactions = this.data.interactions.filter((i) => i.activityType === activityType);
    if (interactions.length < 2) return 0;
    const firstHalf = interactions.slice(0, Math.floor(interactions.length / 2));
    const secondHalf = interactions.slice(Math.floor(interactions.length / 2));
    const firstAccuracy = firstHalf.filter((i) => i.isCorrect).length / firstHalf.length;
    const secondAccuracy = secondHalf.filter((i) => i.isCorrect).length / secondHalf.length;
    return secondAccuracy - firstAccuracy;
  }
  /**
   * Consolida métricas comportamentais
   */
  consolidateBehavioralMetrics() {
    const behavioral = this.data.behavioralMetrics;
    return {
      attentionLevel: {
        visual: behavioral.visual?.averageAttention || 0.5,
        auditory: behavioral.auditory?.averageAttention || 0.5,
        overall: (behavioral.visual?.averageAttention + behavioral.auditory?.averageAttention) / 2 || 0.5
      },
      processingSpeed: {
        average: behavioral.processing?.averageSpeed || 0.5,
        consistency: behavioral.processing?.speedConsistency || 0.5
      },
      workingMemory: {
        capacity: behavioral.memory?.averageCapacity || 0.5,
        efficiency: behavioral.memory?.efficiency || 0.5
      },
      executiveFunction: {
        planning: behavioral.executive?.planning || 0.5,
        flexibility: behavioral.executive?.flexibility || 0.5,
        inhibition: behavioral.executive?.inhibition || 0.5
      }
    };
  }
  /**
   * Calcula progresso ao longo do tempo
   */
  calculateProgressOverTime() {
    const interactions = this.data.interactions;
    if (interactions.length < 5) return [];
    const chunks = [];
    const chunkSize = Math.max(3, Math.floor(interactions.length / 5));
    for (let i = 0; i < interactions.length; i += chunkSize) {
      const chunk = interactions.slice(i, i + chunkSize);
      const accuracy = chunk.filter((int) => int.isCorrect).length / chunk.length;
      const avgTime = chunk.reduce((sum, int) => sum + (int.responseTime || 0), 0) / chunk.length;
      chunks.push({
        period: Math.floor(i / chunkSize) + 1,
        accuracy,
        averageTime: avgTime,
        interactions: chunk.length
      });
    }
    return chunks;
  }
  /**
   * Calcula duração da sessão
   */
  getSessionDuration() {
    if (!this.sessionStartTime) return 0;
    return Date.now() - this.sessionStartTime;
  }
}
const letterRecognitionGame = "_letterRecognitionGame_7z27o_89";
const gameContent = "_gameContent_7z27o_111";
const gameHeader = "_gameHeader_7z27o_131";
const gameTitle = "_gameTitle_7z27o_159";
const headerTtsButton = "_headerTtsButton_7z27o_205";
const ttsActive = "_ttsActive_7z27o_263";
const activityMenu = "_activityMenu_7z27o_285";
const activityButton = "_activityButton_7z27o_325";
const active = "_active_7z27o_377";
const gameStats = "_gameStats_7z27o_437";
const statCard = "_statCard_7z27o_451";
const statValue = "_statValue_7z27o_495";
const statLabel = "_statLabel_7z27o_509";
const gameArea = "_gameArea_7z27o_523";
const lettersGrid = "_lettersGrid_7z27o_555";
const letterCard = "_letterCard_7z27o_575";
const letterContent = "_letterContent_7z27o_723";
const letterLabel = "_letterLabel_7z27o_739";
const gameControls = "_gameControls_7z27o_905";
const controlButton = "_controlButton_7z27o_921";
const wordFormationActivity = "_wordFormationActivity_7z27o_979";
const soundActivity = "_soundActivity_7z27o_1165";
const soundIndicator = "_soundIndicator_7z27o_1211";
const activityTip = "_activityTip_7z27o_1235";
const activityInstruction = "_activityInstruction_7z27o_1263";
const styles = {
  letterRecognitionGame,
  gameContent,
  gameHeader,
  gameTitle,
  headerTtsButton,
  ttsActive,
  activityMenu,
  activityButton,
  active,
  gameStats,
  statCard,
  statValue,
  statLabel,
  gameArea,
  lettersGrid,
  letterCard,
  letterContent,
  letterLabel,
  gameControls,
  controlButton,
  wordFormationActivity,
  soundActivity,
  soundIndicator,
  activityTip,
  activityInstruction
};
var _jsxFileName = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\games\\LetterRecognition\\LetterRecognitionGame.jsx";
const LETTERS = [{
  id: "a",
  letter: "A",
  sound: "A",
  example: "🐝 Abelha",
  color: "#FF6B6B"
}, {
  id: "b",
  letter: "B",
  sound: "Bê",
  example: "⚽ Bola",
  color: "#4ECDC4"
}, {
  id: "c",
  letter: "C",
  sound: "Cê",
  example: "🏠 Casa",
  color: "#45B7D1"
}, {
  id: "d",
  letter: "D",
  sound: "Dê",
  example: "🎲 Dado",
  color: "#FFA07A"
}, {
  id: "e",
  letter: "E",
  sound: "É",
  example: "⭐ Estrela",
  color: "#98D8C8"
}, {
  id: "f",
  letter: "F",
  sound: "Efe",
  example: "🌸 Flor",
  color: "#F7DC6F"
}, {
  id: "g",
  letter: "G",
  sound: "Gê",
  example: "� Gato",
  color: "#BB8FCE"
}, {
  id: "h",
  letter: "H",
  sound: "Agá",
  example: "� Hotel",
  color: "#85C1E9"
}];
const DIFFICULTIES = {
  EASY: {
    letters: LETTERS.slice(0, 4),
    name: "Fácil"
  },
  MEDIUM: {
    letters: LETTERS.slice(0, 6),
    name: "Médio"
  },
  HARD: {
    letters: LETTERS,
    name: "Avançado"
  }
};
const ACTIVITY_TYPES = {
  LETTER_SELECTION: {
    id: "letter_selection",
    name: "Seleção de Letras",
    icon: "🔤",
    description: "Encontre a letra correta baseada no som e exemplo",
    component: "LetterSelectionActivity"
  },
  WORD_FORMATION: {
    id: "word_formation",
    name: "Formação de Palavras",
    icon: "🔗",
    description: "Monte palavras usando as letras aprendidas",
    component: "WordFormationActivity"
  },
  SEQUENCE_RECOGNITION: {
    id: "sequence_recognition",
    name: "Reconhecimento de Sequência",
    icon: "📝",
    description: "Complete sequências alfabéticas",
    component: "SequenceRecognitionActivity"
  },
  VISUAL_DISCRIMINATION: {
    id: "visual_discrimination",
    name: "Discriminação Visual",
    icon: "👁️",
    description: "Identifique letras com formas similares",
    component: "VisualDiscriminationActivity"
  },
  PHONETIC_MATCHING: {
    id: "phonetic_matching",
    name: "Correspondência Fonética",
    icon: "🔊",
    description: "Associe sons de letras com emojis correspondentes",
    component: "PhoneticMatchingActivity"
  }
};
const WORD_BANK = {
  EASY: [{
    word: "MAMA",
    emoji: "👩",
    meaning: "Mamãe"
  }, {
    word: "PAPA",
    emoji: "👨",
    meaning: "Papai"
  }, {
    word: "CASA",
    emoji: "🏠",
    meaning: "Casa"
  }, {
    word: "BOLA",
    emoji: "⚽",
    meaning: "Bola"
  }, {
    word: "GATO",
    emoji: "🐱",
    meaning: "Gato"
  }, {
    word: "PATO",
    emoji: "🦆",
    meaning: "Pato"
  }],
  MEDIUM: [{
    word: "FADA",
    emoji: "🧚",
    meaning: "Fada"
  }, {
    word: "CAFÉ",
    emoji: "☕",
    meaning: "Café"
  }, {
    word: "FLOR",
    emoji: "🌸",
    meaning: "Flor"
  }, {
    word: "SAPO",
    emoji: "🐸",
    meaning: "Sapo"
  }, {
    word: "LUA",
    emoji: "🌙",
    meaning: "Lua"
  }, {
    word: "SOL",
    emoji: "☀️",
    meaning: "Sol"
  }, {
    word: "MAR",
    emoji: "🌊",
    meaning: "Mar"
  }],
  HARD: [{
    word: "JARDIM",
    emoji: "🌻",
    meaning: "Jardim"
  }, {
    word: "LIVRO",
    emoji: "📚",
    meaning: "Livro"
  }, {
    word: "ESCOLA",
    emoji: "🏫",
    meaning: "Escola"
  }, {
    word: "AMIGO",
    emoji: "👫",
    meaning: "Amigo"
  }, {
    word: "ALEGRIA",
    emoji: "😊",
    meaning: "Alegria"
  }, {
    word: "FAMÍLIA",
    emoji: "👨‍👩‍👧‍👦",
    meaning: "Família"
  }, {
    word: "BORBOLETA",
    emoji: "🦋",
    meaning: "Borboleta"
  }]
};
const SEQUENCE_BANK = {
  EASY: [{
    sequence: ["A", "B"],
    missing: "C",
    options: ["C", "D", "E"],
    description: ""
  }, {
    sequence: ["B", "C"],
    missing: "D",
    options: ["D", "A", "F"],
    description: ""
  }, {
    sequence: ["C", "D"],
    missing: "E",
    options: ["E", "B", "G"],
    description: ""
  }, {
    sequence: ["D", "E"],
    missing: "F",
    options: ["F", "C", "H"],
    description: ""
  }, {
    sequence: ["E", "F"],
    missing: "G",
    options: ["G", "D", "I"],
    description: ""
  }, {
    sequence: ["F", "G"],
    missing: "H",
    options: ["H", "E", "J"],
    description: ""
  }],
  MEDIUM: [{
    sequence: ["A", "B", "C"],
    missing: "D",
    options: ["D", "F", "E"],
    description: ""
  }, {
    sequence: ["B", "C", "D"],
    missing: "E",
    options: ["E", "G", "F"],
    description: ""
  }, {
    sequence: ["G", "H", "I"],
    missing: "J",
    options: ["J", "L", "K"],
    description: ""
  }, {
    sequence: ["M", "N", "O"],
    missing: "P",
    options: ["P", "R", "Q"],
    description: ""
  }, {
    sequence: ["P", "Q", "R"],
    missing: "S",
    options: ["S", "U", "T"],
    description: ""
  }, {
    sequence: ["S", "T", "U"],
    missing: "V",
    options: ["V", "X", "W"],
    description: ""
  }],
  HARD: [{
    sequence: ["A", "C"],
    missing: "E",
    options: ["E", "D", "F"],
    description: ""
  }, {
    sequence: ["B", "D"],
    missing: "F",
    options: ["F", "E", "G"],
    description: ""
  }, {
    sequence: ["Z", "Y"],
    missing: "X",
    options: ["X", "W", "V"],
    description: ""
  }, {
    sequence: ["Y", "X"],
    missing: "W",
    options: ["W", "V", "U"],
    description: ""
  }, {
    sequence: ["A", "C", "E"],
    missing: "G",
    options: ["G", "F", "H"],
    description: ""
  }, {
    sequence: ["B", "D", "F"],
    missing: "H",
    options: ["H", "G", "I"],
    description: ""
  }]
};
function LetterRecognitionGame({
  onBack
}) {
  const {
    user,
    ttsEnabled = true
  } = reactExports.useContext(SystemContext);
  const metricsRef = reactExports.useRef(new LetterRecognitionMetrics());
  const sessionIdRef = reactExports.useRef(v4());
  const [collectorsHub] = reactExports.useState(() => new LetterRecognitionCollectorsHub());
  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    finalizeSession: finalizeMultisensory,
    isInitialized: multisensoryInitialized
  } = useMultisensoryIntegration(sessionIdRef.current, {
    learningStyle: user?.profile?.learningStyle || "visual"
  });
  useTherapeuticOrchestrator({
    userId: user?.id || "anonymous"
  });
  const {
    settings
  } = useAccessibilityContext();
  const [ttsActive2, setTtsActive] = reactExports.useState(() => {
    const saved = localStorage.getItem("letterRecognition_ttsActive");
    return saved !== null ? JSON.parse(saved) : true;
  });
  const toggleTTS = reactExports.useCallback(() => {
    setTtsActive((prev) => {
      const newState = !prev;
      localStorage.setItem("letterRecognition_ttsActive", JSON.stringify(newState));
      if (!newState && "speechSynthesis" in window) {
        window.speechSynthesis.cancel();
      }
      return newState;
    });
  }, []);
  const speak = reactExports.useCallback((text, options = {}) => {
    if (!ttsActive2 || !("speechSynthesis" in window)) {
      return;
    }
    window.speechSynthesis.cancel();
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = "pt-BR";
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    window.speechSynthesis.speak(utterance);
  }, [ttsActive2]);
  const {
    startUnifiedSession,
    recordInteraction,
    updateMetrics,
    portalReady
  } = useUnifiedGameLogic("letter_recognition");
  reactExports.useEffect(() => {
    if (metricsRef.current && recordInteraction && updateMetrics) {
      metricsRef.current.connectToBackend({
        recordInteraction,
        updateMetrics
      });
    }
  }, [recordInteraction, updateMetrics]);
  const [cognitiveAnalysisVisible, setCognitiveAnalysisVisible] = reactExports.useState(false);
  const [analysisResults, setAnalysisResults] = reactExports.useState(null);
  const [attemptCount, setAttemptCount] = reactExports.useState(0);
  const [gameState, setGameState] = reactExports.useState({
    status: "menu",
    score: 0,
    round: 1,
    targetLetter: null,
    selectedLetter: null,
    showFeedback: false,
    accuracy: 100,
    totalRounds: 10,
    difficulty: "EASY",
    availableLetters: DIFFICULTIES.EASY.letters,
    allLetters: DIFFICULTIES.EASY.letters,
    roundStartTime: null,
    // 🎯 Sistema de atividades - PADRÃO IMAGEASSOCIATION (5 ATIVIDADES)
    currentActivity: ACTIVITY_TYPES.LETTER_SELECTION.id,
    activityCycle: [ACTIVITY_TYPES.LETTER_SELECTION.id, ACTIVITY_TYPES.WORD_FORMATION.id, ACTIVITY_TYPES.SEQUENCE_RECOGNITION.id, ACTIVITY_TYPES.VISUAL_DISCRIMINATION.id, ACTIVITY_TYPES.PHONETIC_MATCHING.id],
    activityIndex: 0,
    roundsPerActivity: 4,
    // Será calculado dinamicamente (4-7 rodadas)
    activityRoundCount: 0,
    // Iniciar com 0 rodadas
    activitiesCompleted: [],
    // Lista de atividades já completadas
    // 🎯 Dados específicos de atividades
    activityData: {
      wordFormation: {
        currentWord: null,
        placedLetters: [],
        availableLetters: []
      },
      sequenceRecognition: {
        currentSequence: null,
        userProgress: []
      },
      visualDiscrimination: {
        currentGroup: null,
        foundTargets: 0,
        totalTargets: 0
      }
    },
    // 🎯 Sistema de prevenção de repetição
    letterHistory: [],
    // Histórico das últimas 3 letras usadas
    wordHistory: [],
    // Histórico das últimas 3 palavras usadas
    sequenceHistory: [],
    // Histórico das últimas 3 sequências usadas
    // 🎯 Métricas comportamentais avançadas
    behavioralMetrics: {
      activityPreferences: {},
      responsePatterns: [],
      adaptiveAdjustments: 0,
      engagementLevel: 1
    }
  });
  const [showStartScreen, setShowStartScreen] = reactExports.useState(true);
  const calculateOptimalRounds = reactExports.useCallback((activityType, difficulty, userPerformance = 0.7) => {
    const roundsByDifficulty = {
      "EASY": {
        "letter_selection": 5,
        "word_formation": 5,
        "sequence_recognition": 5,
        "visual_discrimination": 5,
        "phonetic_matching": 5
      },
      "MEDIUM": {
        "letter_selection": 5,
        "word_formation": 5,
        "sequence_recognition": 5,
        "visual_discrimination": 5,
        "phonetic_matching": 5
      },
      "HARD": {
        "letter_selection": 5,
        "word_formation": 5,
        "sequence_recognition": 5,
        "visual_discrimination": 5,
        "phonetic_matching": 5
      }
    };
    const base = roundsByDifficulty[difficulty]?.[activityType] || 4;
    const performanceAdjustment = userPerformance < 0.5 ? 1 : userPerformance > 0.8 ? -1 : 0;
    return Math.max(4, Math.min(7, base + performanceAdjustment));
  }, []);
  reactExports.useCallback(() => {
    setGameState((prev) => {
      const nextActivityIndex = (prev.activityIndex + 1) % prev.activityCycle.length;
      const nextActivity = prev.activityCycle[nextActivityIndex];
      const activityName = ACTIVITY_TYPES[nextActivity.toUpperCase()]?.name || "Nova Atividade";
      speak(`Nova atividade: ${activityName}`, {
        pitch: 1.2,
        rate: 0.8
      });
      return {
        ...prev,
        currentActivity: nextActivity,
        activityIndex: nextActivityIndex,
        activityRoundCount: 0,
        selectedLetter: null,
        showFeedback: false
      };
    });
  }, [speak]);
  const provideActivityInstructions = reactExports.useCallback((activity, activityData) => {
    console.log("🔊 Providing TTS instructions for activity:", activity);
    switch (activity) {
      case "letter_selection":
        if (activityData?.targetLetter) {
          const letter = activityData.targetLetter;
          const instruction = `Encontre a letra ${letter.letter}. ${letter.sound} de ${letter.example.split(" ")[1]}. Clique na letra ${letter.letter}.`;
          speak(instruction, {
            rate: 0.7,
            pitch: 1.1
          });
        }
        break;
      case "word_formation":
        if (activityData?.currentWord) {
          const word = activityData.currentWord;
          const instruction = `Monte a palavra ${word.meaning}. ${word.emoji} ${word.meaning}. Use as letras para formar ${word.word}.`;
          speak(instruction, {
            rate: 0.7,
            pitch: 1.1
          });
        }
        break;
      case "sequence_recognition":
        if (activityData?.currentSequence) {
          const sequence = activityData.currentSequence;
          const instruction = `Complete a sequência alfabética. ${sequence.sequence.join(", ")}. Qual letra vem depois?`;
          speak(instruction, {
            rate: 0.7,
            pitch: 1.1
          });
        }
        break;
      case "visual_discrimination":
        if (activityData?.currentGroup) {
          const group = activityData.currentGroup;
          const instruction = `${group.description}. Clique em todas as letras ${group.target} que você encontrar.`;
          speak(instruction, {
            rate: 0.7,
            pitch: 1.1
          });
        }
        break;
      default:
        speak("Escolha uma atividade para começar.", {
          rate: 0.8
        });
    }
  }, [speak]);
  const generateActivityContent = reactExports.useCallback((activity, difficulty) => {
    const difficultyConfig = DIFFICULTIES[difficulty] || DIFFICULTIES.EASY;
    switch (activity) {
      case "word_formation":
        console.log("📝 Gerando word_formation com histórico:", gameState.wordHistory);
        const wordBank = WORD_BANK[difficulty] || WORD_BANK.EASY;
        let selectedWord;
        const filteredWords = wordBank.filter((word) => !gameState.wordHistory.includes(word.word));
        if (filteredWords.length === 0) {
          const notLastWord = wordBank.filter((word) => word.word !== gameState.wordHistory[gameState.wordHistory.length - 1]);
          selectedWord = notLastWord[Math.floor(Math.random() * notLastWord.length)];
        } else {
          selectedWord = filteredWords[Math.floor(Math.random() * filteredWords.length)];
        }
        console.log("🎯 Palavra selecionada:", selectedWord.word, "Histórico:", gameState.wordHistory);
        const wordLetters = selectedWord.word.split("");
        let targetCardCount, distractorCount;
        switch (difficulty) {
          case "EASY":
            targetCardCount = 4;
            distractorCount = Math.max(0, targetCardCount - wordLetters.length);
            break;
          case "MEDIUM":
            targetCardCount = 6;
            distractorCount = Math.max(0, targetCardCount - wordLetters.length);
            break;
          case "HARD":
            targetCardCount = 8;
            distractorCount = Math.max(0, targetCardCount - wordLetters.length);
            break;
          default:
            targetCardCount = 4;
            distractorCount = Math.max(0, targetCardCount - wordLetters.length);
        }
        const distractors = distractorCount > 0 ? generateDistracters(wordLetters, distractorCount) : [];
        const allLetters = [...wordLetters, ...distractors];
        const availableLetters = shuffleArray(allLetters.slice(0, targetCardCount));
        console.log("📝 Generated word formation content:", {
          selectedWord: selectedWord.word,
          wordLength: wordLetters.length,
          difficulty,
          targetCardCount,
          distractorCount,
          totalCards: availableLetters.length,
          wordLetters,
          distractors
        });
        const wordContent = {
          currentWord: selectedWord,
          availableLetters,
          placedLetters: new Array(selectedWord.word.length).fill(null)
        };
        setTimeout(() => provideActivityInstructions(activity, wordContent), 500);
        return wordContent;
      case "sequence_recognition":
        console.log("📝 Gerando sequence_recognition com histórico:", gameState.sequenceHistory);
        const sequenceBank = SEQUENCE_BANK[difficulty] || SEQUENCE_BANK.EASY;
        let selectedSequence;
        const filteredSequences = sequenceBank.filter((seq) => !gameState.sequenceHistory.includes(seq.description));
        if (filteredSequences.length === 0) {
          const notLastSequence = sequenceBank.filter((seq) => seq.description !== gameState.sequenceHistory[gameState.sequenceHistory.length - 1]);
          selectedSequence = notLastSequence[Math.floor(Math.random() * notLastSequence.length)];
        } else {
          selectedSequence = filteredSequences[Math.floor(Math.random() * filteredSequences.length)];
        }
        console.log("🎯 Sequência selecionada:", selectedSequence.description, "Histórico:", gameState.sequenceHistory);
        const shuffledOptions = shuffleArray([...selectedSequence.options]);
        console.log("📝 Generated sequence recognition content:", {
          selectedSequence: selectedSequence.description,
          sequence: selectedSequence.sequence,
          missing: selectedSequence.missing,
          difficulty,
          shuffledOptions
        });
        const sequenceContent = {
          currentSequence: {
            ...selectedSequence,
            options: shuffledOptions
          }
        };
        setTimeout(() => provideActivityInstructions(activity, sequenceContent), 500);
        return sequenceContent;
      case "visual_discrimination":
        console.log("📝 Gerando visual_discrimination com histórico:", gameState.visualHistory || []);
        const visualGroupsByDifficulty = {
          EASY: [{
            target: "A",
            items: ["A", "H", "V", "A", "N", "A"],
            description: "Encontre todas as letras A"
          }, {
            target: "O",
            items: ["O", "C", "U", "O", "Q", "O"],
            description: "Encontre todas as letras O"
          }, {
            target: "I",
            items: ["I", "L", "T", "I", "J", "I"],
            description: "Encontre todas as letras I"
          }, {
            target: "E",
            items: ["E", "F", "L", "E", "T", "E"],
            description: "Encontre todas as letras E"
          }],
          MEDIUM: [{
            target: "B",
            items: ["B", "P", "R", "B", "D", "B", "Q", "R"],
            description: "Encontre todas as letras B"
          }, {
            target: "P",
            items: ["P", "B", "R", "P", "F", "P", "D", "B"],
            description: "Encontre todas as letras P"
          }, {
            target: "D",
            items: ["D", "B", "P", "D", "Q", "D", "O", "C"],
            description: "Encontre todas as letras D"
          }, {
            target: "G",
            items: ["G", "C", "O", "G", "Q", "G", "D", "B"],
            description: "Encontre todas as letras G"
          }, {
            target: "M",
            items: ["M", "N", "W", "M", "H", "M", "A", "V"],
            description: "Encontre todas as letras M"
          }],
          HARD: [{
            target: "Q",
            items: ["Q", "O", "G", "Q", "C", "Q", "D", "P", "B", "G"],
            description: "Encontre todas as letras Q"
          }, {
            target: "R",
            items: ["R", "P", "B", "R", "F", "R", "K", "D", "H", "B"],
            description: "Encontre todas as letras R"
          }, {
            target: "K",
            items: ["K", "H", "X", "K", "R", "K", "F", "Y", "A", "V"],
            description: "Encontre todas as letras K"
          }, {
            target: "W",
            items: ["W", "M", "V", "W", "N", "W", "A", "H", "Y", "X"],
            description: "Encontre todas as letras W"
          }, {
            target: "Y",
            items: ["Y", "V", "T", "Y", "X", "Y", "K", "A", "F", "I"],
            description: "Encontre todas as letras Y"
          }]
        };
        const availableGroups = visualGroupsByDifficulty[difficulty] || visualGroupsByDifficulty.EASY;
        let selectedGroup;
        const visualHistory = gameState.visualHistory || [];
        const filteredGroups = availableGroups.filter((group) => !visualHistory.includes(group.target));
        if (filteredGroups.length === 0) {
          const notLastGroup = availableGroups.filter((group) => group.target !== visualHistory[visualHistory.length - 1]);
          selectedGroup = notLastGroup[Math.floor(Math.random() * notLastGroup.length)];
        } else {
          selectedGroup = filteredGroups[Math.floor(Math.random() * filteredGroups.length)];
        }
        console.log("🎯 Grupo visual selecionado:", selectedGroup.target, "Histórico:", visualHistory);
        const shuffledItems = shuffleArray([...selectedGroup.items]);
        const actualTargetCount = shuffledItems.filter((item) => item === selectedGroup.target).length;
        console.log("📝 Generated visual discrimination content:", {
          selectedGroup: selectedGroup.description,
          target: selectedGroup.target,
          actualTargetCount,
          difficulty,
          shuffledItems
        });
        const visualContent = {
          currentGroup: {
            ...selectedGroup,
            items: shuffledItems
          },
          foundTargets: 0,
          totalTargets: actualTargetCount,
          // Usar contagem real
          selectedItems: []
          // Array para rastrear itens selecionados
        };
        setTimeout(() => provideActivityInstructions(activity, visualContent), 500);
        return visualContent;
      default:
        console.log("📝 Gerando letter_selection com histórico:", gameState.letterHistory);
        let newTargetLetter;
        const filteredLetters = difficultyConfig.letters.filter((letter) => !gameState.letterHistory.includes(letter));
        if (filteredLetters.length === 0) {
          const notLastLetter = difficultyConfig.letters.filter((letter) => letter !== gameState.letterHistory[gameState.letterHistory.length - 1]);
          newTargetLetter = notLastLetter[Math.floor(Math.random() * notLastLetter.length)];
        } else {
          newTargetLetter = filteredLetters[Math.floor(Math.random() * filteredLetters.length)];
        }
        console.log("🎯 Letra selecionada:", newTargetLetter, "Histórico:", gameState.letterHistory);
        const letterOptions = [newTargetLetter];
        const otherAvailable = difficultyConfig.letters.filter((l) => l !== newTargetLetter);
        letterOptions.push(...shuffleArray(otherAvailable).slice(0, 2));
        const letterContent2 = {
          targetLetter: newTargetLetter,
          options: shuffleArray(letterOptions)
        };
        setTimeout(() => provideActivityInstructions(activity, letterContent2), 500);
        return letterContent2;
    }
  }, []);
  function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  }
  function generateDistracters(targetLetters, count) {
    const allLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("");
    const available = allLetters.filter((letter) => !targetLetters.includes(letter));
    return shuffleArray(available).slice(0, count);
  }
  const changeActivity = reactExports.useCallback((activityId) => {
    console.log("🎯 Changing activity to:", activityId);
    setGameState((prev) => {
      console.log("🔄 Current state before change:", {
        currentActivity: prev.currentActivity,
        targetActivity: activityId,
        currentActivityData: prev.activityData
      });
      const activityContent = generateActivityContent(activityId, prev.difficulty);
      console.log("📝 Generated activity content:", activityContent);
      let newActivityData = {
        ...prev.activityData
      };
      switch (activityId) {
        case "word_formation":
          newActivityData.wordFormation = activityContent;
          break;
        case "sequence_recognition":
          newActivityData.sequenceRecognition = activityContent;
          break;
        case "visual_discrimination":
          newActivityData.visualDiscrimination = activityContent;
          break;
        default:
          return {
            ...prev,
            currentActivity: activityId,
            activityRoundCount: 0,
            selectedLetter: null,
            showFeedback: false,
            roundStartTime: Date.now(),
            targetLetter: activityContent.targetLetter,
            availableLetters: activityContent.options,
            activityData: newActivityData
          };
      }
      const newState = {
        ...prev,
        currentActivity: activityId,
        activityRoundCount: 0,
        selectedLetter: null,
        showFeedback: false,
        roundStartTime: Date.now(),
        activityData: newActivityData
      };
      console.log("✅ New state after change:", {
        currentActivity: newState.currentActivity,
        activityData: newState.activityData
      });
      return newState;
    });
  }, [generateActivityContent]);
  function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  }
  function generateDistracters(targetLetters, count) {
    const allLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("");
    const available = allLetters.filter((letter) => !targetLetters.includes(letter));
    return shuffleArray(available).slice(0, count);
  }
  const generateNewRound = reactExports.useCallback(() => {
    console.log("🎯 Gerando nova rodada...", {
      currentActivity: gameState.currentActivity,
      activityRoundCount: gameState.activityRoundCount,
      round: gameState.round
    });
    setGameState((prev) => {
      console.log("🔄 Incrementando rodada da atividade:", {
        currentActivity: prev.currentActivity,
        activityRoundCount: prev.activityRoundCount + 1,
        roundsPerActivity: prev.roundsPerActivity
      });
      let newState = {
        ...prev,
        activityRoundCount: prev.activityRoundCount + 1
        // Incrementar contador de rodadas
      };
      if (prev.activityRoundCount === 0) {
        const userPerformance = prev.accuracy / 100;
        newState.roundsPerActivity = calculateOptimalRounds(prev.currentActivity, prev.difficulty, userPerformance);
        console.log("🎯 Rodadas calculadas para", prev.currentActivity, ":", newState.roundsPerActivity);
      }
      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);
      console.log("📝 Conteúdo gerado para atividade:", {
        activity: newState.currentActivity,
        content: activityContent
      });
      switch (newState.currentActivity) {
        case "word_formation":
          newState.activityData.wordFormation = {
            currentWord: activityContent.currentWord,
            placedLetters: activityContent.placedLetters,
            availableLetters: activityContent.availableLetters
          };
          break;
        case "sequence_recognition":
          newState.activityData.sequenceRecognition = {
            currentSequence: activityContent.currentSequence,
            userProgress: []
          };
          break;
        case "visual_discrimination":
          newState.activityData.visualDiscrimination = {
            currentGroup: activityContent.currentGroup,
            foundTargets: activityContent.foundTargets,
            totalTargets: activityContent.totalTargets
          };
          break;
        default:
          newState.targetLetter = activityContent.targetLetter;
          newState.availableLetters = activityContent.options || shuffleArray([...DIFFICULTIES[newState.difficulty].letters]).slice(0, 3);
      }
      return {
        ...newState,
        selectedLetter: null,
        showFeedback: false,
        roundStartTime: Date.now()
      };
    });
  }, [generateActivityContent, speak]);
  reactExports.useEffect(() => {
    if (!showStartScreen) {
      generateNewRound();
    }
    return () => {
      if ("speechSynthesis" in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, [showStartScreen]);
  const handleLetterSelect = async (letterId) => {
    if (gameState.selectedLetter) return;
    setGameState((prev) => ({
      ...prev,
      selectedLetter: letterId
    }));
    const isCorrect = letterId === gameState.targetLetter.id;
    const reactionTime = gameState.roundStartTime ? Date.now() - gameState.roundStartTime : 0;
    speakLetter(letterId);
    try {
      if (!collectorsHub.sessionActive && sessionIdRef.current) {
        await collectorsHub.initializeSession(sessionIdRef.current, {
          gameMode: "letter_recognition_v3",
          activityType: gameState.currentActivity,
          difficulty: gameState.difficulty
        });
      }
      await collectorsHub.collectComprehensiveData({
        // Dados básicos
        targetLetter: gameState.targetLetter,
        selectedLetter: LETTERS.find((l) => l.id === letterId),
        isCorrect,
        responseTime: reactionTime,
        sessionId: sessionIdRef.current,
        userId: user?.id || "anonymous",
        // Dados V3 específicos
        activityType: gameState.currentActivity,
        activityRound: gameState.activityRoundCount || 0,
        activitiesCompleted: gameState.activitiesCompleted || [],
        // Dados da atividade atual
        activityData: gameState.activityData,
        // Métricas comportamentais V3
        behavioralMetrics: {
          ...gameState.behavioralMetrics,
          reactionTime: [...gameState.behavioralMetrics.reactionTime || [], reactionTime],
          accuracy: [...gameState.behavioralMetrics.accuracy || [], isCorrect ? 1 : 0],
          attentionSpan: Date.now() - gameState.roundStartTime,
          currentLevel: gameState.currentLevel,
          totalAttempts: gameState.round,
          activitySpecific: {
            ...gameState.behavioralMetrics.activitySpecific,
            // Adicionar métricas específicas baseadas na atividade atual
            ...gameState.currentActivity === "word_formation" && {
              wordBuildingTime: reactionTime,
              letterSequenceAccuracy: gameState.activityData?.wordAccuracy || 0
            },
            ...gameState.currentActivity === "visual_discrimination" && {
              visualScanTime: reactionTime,
              targetDetectionRate: gameState.activityData?.detectionRate || 0
            },
            ...gameState.currentActivity === "sequence_recognition" && {
              sequenceCompletionTime: reactionTime,
              patternRecognition: isCorrect ? 1 : 0
            }
          }
        },
        // Comportamento do jogador
        playerBehavior: {
          hesitationTime: reactionTime > 3e3 ? reactionTime - 3e3 : 0,
          confidence: reactionTime < 1e3 ? "high" : reactionTime < 3e3 ? "medium" : "low",
          attemptPattern: gameState.round % 5 === 0 ? "milestone" : "regular"
        }
      });
    } catch (error) {
      console.warn("📚 LetterRecognition V3: Erro ao coletar dados:", error);
    }
    if (multisensoryInitialized) {
      await recordMultisensoryInteraction("game_interaction", {
        interactionType: "user_action",
        gameSpecificData: {
          targetLetter: gameState.targetLetter.id,
          selectedLetter: letterId,
          isCorrect,
          round: gameState.round,
          score: gameState.score,
          responseTime: reactionTime,
          activityType: gameState.currentActivity,
          activityRound: gameState.activityRoundCount || 0
        },
        multisensoryProcessing: {
          linguisticProcessing: {
            letterRecognition: 0.8,
            phonologicalAwareness: 0.7,
            linguisticMemory: 0.7,
            activitySpecificSkill: calculateActivitySpecificSkill(gameState.currentActivity, isCorrect)
          },
          cognitiveProcessing: {
            accuracy: isCorrect ? 1 : 0,
            processingSpeed: reactionTime < 3e3 ? 0.8 : 0.5,
            adaptability: 0.7,
            activityComplexity: getActivityComplexity(gameState.currentActivity)
          },
          behavioralProcessing: {
            interactionCount: gameState.round,
            averageResponseTime: reactionTime,
            consistency: 0.8,
            activityProgression: (gameState.activitiesCompleted?.length || 0) / 6
          }
        }
      });
    }
    if (isCorrect) {
      handleCorrectAnswer();
    } else {
      handleIncorrectAnswer();
    }
  };
  const startGame = reactExports.useCallback(async (selectedDifficulty) => {
    console.log("🚀 STARTING GAME WITH DIFFICULTY:", selectedDifficulty);
    try {
      setShowStartScreen(false);
      const difficultyKey = selectedDifficulty.toUpperCase();
      const difficultyConfig = DIFFICULTIES[difficultyKey];
      console.log("🎯 Difficulty config:", difficultyConfig);
      const initialActivityContent = generateActivityContent("letter_selection", difficultyKey);
      console.log("📝 Initial activity content:", initialActivityContent);
      setGameState((prev) => ({
        ...prev,
        status: "playing",
        difficulty: difficultyKey,
        availableLetters: difficultyConfig.letters,
        allLetters: difficultyConfig.letters,
        score: 0,
        round: 1,
        accuracy: 100,
        targetLetter: initialActivityContent.targetLetter,
        roundStartTime: Date.now(),
        currentActivity: "letter_selection",
        // 🎯 Configurar rodadas fixas - PADRÃO IMAGEASSOCIATION (5 RODADAS)
        roundsPerActivity: 5,
        // SEMPRE 5 rodadas por atividade
        activityRoundCount: 0,
        // Resetar contador de rodadas
        // Inicializar dados de todas as 5 atividades
        activityData: {
          letterSelection: initialActivityContent,
          wordFormation: generateActivityContent("word_formation", difficultyKey),
          sequenceRecognition: generateActivityContent("sequence_recognition", difficultyKey),
          visualDiscrimination: generateActivityContent("visual_discrimination", difficultyKey)
        }
      }));
      console.log("✅ Game started successfully!");
      try {
        if (collectorsHub && typeof collectorsHub.initializeSession === "function") {
          await collectorsHub.initializeSession(sessionIdRef.current, {
            difficulty: difficultyKey,
            availableLetters: difficultyConfig.letters.map((l) => l.id),
            gameMode: "letter_recognition"
          });
          console.log("📚 LetterRecognition: Coletores inicializados com sucesso");
        } else {
          console.log("📚 LetterRecognition: Coletores já ativos");
        }
      } catch (error) {
        console.warn("⚠️ LetterRecognition: Erro ao inicializar coletores:", error);
      }
      try {
        await initMultisensory(sessionIdRef.current, {
          difficulty: difficultyKey,
          gameMode: "letter_recognition",
          availableLetters: difficultyConfig.letters.map((l) => l.id),
          userId: user?.id || "anonymous"
        });
      } catch (error) {
        console.warn("⚠️ Erro ao inicializar sessão multissensorial:", error);
      }
      if (portalReady) {
        startUnifiedSession({
          gameType: "LetterRecognition",
          difficulty: difficultyKey,
          userId: user?.id || "anonymous"
        });
      }
      setTimeout(() => {
        speak(`Bem-vindo ao Reconhecimento de Letras! Dificuldade: ${difficultyConfig.name}. Ouça o som da letra e encontre a letra correta. Vamos começar!`, {
          rate: 0.8
        });
      }, 1e3);
    } catch (error) {
      console.error("❌ Error starting game:", error);
    }
  }, [initMultisensory, startUnifiedSession, portalReady, user, speak, generateActivityContent, collectorsHub]);
  const restartGame = () => {
    setGameState({
      status: "menu",
      score: 0,
      round: 1,
      targetLetter: null,
      selectedLetter: null,
      showFeedback: false,
      accuracy: 100,
      totalRounds: 10,
      difficulty: "EASY",
      availableLetters: DIFFICULTIES.EASY.letters,
      allLetters: DIFFICULTIES.EASY.letters,
      roundStartTime: null,
      // 🎯 Sistema de atividades
      currentActivity: ACTIVITY_TYPES.LETTER_SELECTION.id,
      activityCycle: [ACTIVITY_TYPES.LETTER_SELECTION.id, ACTIVITY_TYPES.WORD_FORMATION.id, ACTIVITY_TYPES.SEQUENCE_RECOGNITION.id, ACTIVITY_TYPES.VISUAL_DISCRIMINATION.id, ACTIVITY_TYPES.PHONETIC_MATCHING.id],
      activityIndex: 0,
      roundsPerActivity: 4,
      // Será recalculado dinamicamente (4-7 rodadas)
      activityRoundCount: 0,
      // Iniciar com 0 rodadas
      activitiesCompleted: [],
      // 🎯 Dados específicos de atividades
      activityData: {
        wordFormation: {
          currentWord: null,
          placedLetters: [],
          availableLetters: []
        },
        sequenceRecognition: {
          currentSequence: null,
          userProgress: []
        },
        visualDiscrimination: {
          currentGroup: null,
          foundTargets: 0,
          totalTargets: 0
        }
      },
      // 🎯 Métricas comportamentais avançadas
      behavioralMetrics: {
        activityPreferences: {},
        responsePatterns: [],
        adaptiveAdjustments: 0,
        engagementLevel: 1
      },
      // 🎯 Sistema de prevenção de repetição
      letterHistory: [],
      wordHistory: [],
      sequenceHistory: []
    });
    setShowStartScreen(true);
  };
  reactExports.useEffect(() => {
    if (gameState.round > 10 && !showStartScreen) {
      const finalizeMultisensorySession = async () => {
        try {
          const multisensoryReport = await finalizeMultisensory({
            finalScore: gameState.score,
            finalAccuracy: gameState.accuracy,
            totalInteractions: gameState.round - 1,
            sessionDuration: Date.now() - (gameState.startTime || Date.now()),
            difficulty: gameState.difficulty
          });
          console.log("🔄 LetterRecognition: Relatório multissensorial final:", multisensoryReport);
        } catch (error) {
          console.warn("⚠️ LetterRecognition: Erro ao finalizar sessão multissensorial:", error);
        }
      };
      finalizeMultisensorySession();
    }
  }, [gameState.round, showStartScreen, multisensoryInitialized, gameState.score, gameState.accuracy, gameState.difficulty, gameState.startTime]);
  const explainGame = reactExports.useCallback(() => {
    let explanation = "";
    switch (gameState.currentActivity) {
      case "letter_selection":
        const letter = gameState.targetLetter;
        if (letter) {
          explanation = `Atividade: Encontrar Letra. Você deve encontrar a letra ${letter.letter}. ${letter.sound} de ${letter.example.split(" ")[1]}. Clique na letra ${letter.letter} quando encontrá-la.`;
        } else {
          explanation = "Atividade: Encontrar Letra. Procure a letra indicada e clique nela quando encontrar.";
        }
        break;
      case "word_formation":
        const wordData = gameState.activityData.wordFormation;
        if (wordData?.currentWord) {
          explanation = `Atividade: Formação de Palavras. Monte a palavra ${wordData.currentWord.meaning}. ${wordData.currentWord.emoji} ${wordData.currentWord.meaning}. Clique nas letras para formar ${wordData.currentWord.word}.`;
        } else {
          explanation = "Atividade: Formação de Palavras. Monte palavras clicando nas letras na ordem correta.";
        }
        break;
      case "sequence_recognition":
        const sequenceData = gameState.activityData.sequenceRecognition;
        if (sequenceData?.currentSequence) {
          explanation = `Atividade: Sequência Alfabética. Complete a sequência ${sequenceData.currentSequence.sequence.join(", ")}. Qual letra vem depois?`;
        } else {
          explanation = "Atividade: Sequência Alfabética. Complete sequências de letras do alfabeto.";
        }
        break;
      case "visual_discrimination":
        const visualData = gameState.activityData.visualDiscrimination;
        if (visualData?.currentGroup) {
          explanation = `Atividade: Discriminação Visual. ${visualData.currentGroup.description}. Clique em todas as letras ${visualData.currentGroup.target} que encontrar.`;
        } else {
          explanation = "Atividade: Discriminação Visual. Encontre todas as letras iguais à letra alvo.";
        }
        break;
      default:
        explanation = "Bem-vindo ao jogo de reconhecimento de letras! Aqui você vai aprender o alfabeto de forma divertida. Use o menu para escolher diferentes atividades.";
    }
    speak(explanation);
    if (portalReady) {
      recordInteraction({
        type: "tts_usage",
        data: {
          text: explanation,
          activity: gameState.currentActivity,
          round: gameState.round
        }
      });
    }
  }, [gameState.currentActivity, gameState.targetLetter, gameState.activityData, gameState.round, speak, portalReady, recordInteraction]);
  reactExports.useCallback(() => {
    provideActivityInstructions(gameState.currentActivity, gameState.activityData[gameState.currentActivity]);
  }, [gameState.currentActivity, gameState.activityData, provideActivityInstructions]);
  reactExports.useCallback(() => {
    const testMessage = `
      Teste de Acessibilidade Ativado!

      Simulando criança neurodivergente usando apenas instruções de áudio.

      Atividade atual: ${ACTIVITY_TYPES[gameState.currentActivity]?.name || "Desconhecida"}

      Instruções sendo fornecidas automaticamente...
    `;
    speak(testMessage, {
      rate: 0.8
    });
    setTimeout(() => {
      provideActivityInstructions(gameState.currentActivity, gameState.activityData[gameState.currentActivity]);
    }, 3e3);
  }, [gameState.currentActivity, gameState.activityData, speak, provideActivityInstructions]);
  reactExports.useCallback((isCorrect, selectedLetter) => {
    if (isCorrect) {
      speak("Muito bem! Você acertou!", {
        pitch: 1.3,
        rate: 0.9
      });
    } else {
      const targetLetter = gameState.targetLetter;
      const selectedLetterData = LETTERS.find((l) => l.id === selectedLetter);
      speak(`Não foi dessa vez. Você escolheu ${selectedLetterData?.sound}, mas a resposta correta é ${targetLetter?.sound}`, {
        pitch: 0.9,
        rate: 0.7
      });
    }
  }, [gameState.targetLetter, speak]);
  const speakLetter = reactExports.useCallback((letterId) => {
    const letter = LETTERS.find((l) => l.id === letterId);
    if (letter) {
      speak(letter.sound, {
        rate: 0.6,
        pitch: 1.2
      });
    }
  }, [speak]);
  const calculateActivitySpecificSkill = (activityType, isCorrect) => {
    const baseScore = isCorrect ? 0.8 : 0.3;
    const skillMultipliers = {
      "letter_selection": 1,
      "word_formation": 1.5,
      "sequence_recognition": 1.3,
      "visual_discrimination": 1.4
    };
    return baseScore * (skillMultipliers[activityType] || 1);
  };
  const getActivityComplexity = (activityType) => {
    const complexityMap = {
      "letter_selection": 0.3,
      "word_formation": 0.8,
      "sequence_recognition": 0.6,
      "visual_discrimination": 0.7
    };
    return complexityMap[activityType] || 0.5;
  };
  const handleWordLetterSelect = async (letter, letterIndex) => {
    console.log("🎯 Word Letter Select:", {
      letter,
      letterIndex,
      selectedLetter: gameState.selectedLetter
    });
    if (gameState.selectedLetter) {
      console.log("⏳ Already processing a selection, skipping...");
      return;
    }
    gameState.roundStartTime ? Date.now() - gameState.roundStartTime : 0;
    const wordData = gameState.activityData.wordFormation;
    const currentWord = wordData.currentWord;
    const placedLetters = [...wordData.placedLetters || []];
    let nextEmptyIndex = placedLetters.findIndex((slot) => slot === null || slot === void 0);
    if (nextEmptyIndex === -1) {
      console.log("⚠️ Word already complete");
      return;
    }
    const expectedLetter = currentWord.word[nextEmptyIndex];
    const isCorrect = letter.toUpperCase() === expectedLetter.toUpperCase();
    console.log("🔍 Word formation check:", {
      letter: letter.toUpperCase(),
      expected: expectedLetter.toUpperCase(),
      position: nextEmptyIndex,
      isCorrect
    });
    if (isCorrect) {
      placedLetters[nextEmptyIndex] = letter.toUpperCase();
      setGameState((prev) => ({
        ...prev,
        selectedLetter: letter,
        activityData: {
          ...prev.activityData,
          wordFormation: {
            ...prev.activityData.wordFormation,
            placedLetters
          }
        }
      }));
      const isWordComplete = placedLetters.every((slot) => slot !== null && slot !== void 0);
      if (isWordComplete) {
        console.log("✅ Word completed!");
        setTimeout(() => {
          handleCorrectAnswer();
        }, 1e3);
      } else {
        setTimeout(() => {
          setGameState((prev) => ({
            ...prev,
            selectedLetter: null
          }));
        }, 500);
      }
    } else {
      console.log("❌ Wrong letter for word formation");
      setGameState((prev) => ({
        ...prev,
        selectedLetter: letter
      }));
      handleIncorrectAnswer();
    }
  };
  const handleSequenceSelect = async (letter) => {
    console.log("🎯 Sequence Select:", {
      letter,
      selectedLetter: gameState.selectedLetter
    });
    if (gameState.selectedLetter) {
      console.log("⏳ Already processing a selection, skipping...");
      return;
    }
    gameState.roundStartTime ? Date.now() - gameState.roundStartTime : 0;
    const sequenceData = gameState.activityData.sequenceRecognition;
    const currentSequence = sequenceData.currentSequence;
    const expectedLetter = currentSequence?.missing;
    const isCorrect = letter.toUpperCase() === expectedLetter?.toUpperCase();
    console.log("🔍 Sequence check:", {
      letter: letter.toUpperCase(),
      expected: expectedLetter?.toUpperCase(),
      isCorrect
    });
    setGameState((prev) => ({
      ...prev,
      selectedLetter: letter
    }));
    if (isCorrect) {
      console.log("✅ Correct sequence letter!");
      setTimeout(() => {
        handleCorrectAnswer();
      }, 1e3);
    } else {
      console.log("❌ Wrong sequence letter");
      setTimeout(() => {
        handleIncorrectAnswer();
      }, 1e3);
    }
  };
  const handleVisualSelect = async (letter, itemIndex) => {
    console.log("🎯 Visual Select:", {
      letter,
      itemIndex,
      selectedLetter: gameState.selectedLetter
    });
    const selectionKey = `${letter}-${itemIndex}`;
    const visualData = gameState.activityData.visualDiscrimination;
    if (visualData.selectedItems?.includes(selectionKey)) {
      console.log("⚠️ Item already selected, skipping...");
      return;
    }
    gameState.roundStartTime ? Date.now() - gameState.roundStartTime : 0;
    const currentGroup = visualData.currentGroup;
    const targetLetter = currentGroup?.target;
    const isCorrect = letter.toUpperCase() === targetLetter?.toUpperCase();
    console.log("🔍 Visual discrimination check:", {
      letter: letter.toUpperCase(),
      target: targetLetter?.toUpperCase(),
      isCorrect,
      currentFound: visualData.foundTargets
    });
    setGameState((prev) => ({
      ...prev,
      selectedLetter: selectionKey,
      activityData: {
        ...prev.activityData,
        visualDiscrimination: {
          ...prev.activityData.visualDiscrimination,
          foundTargets: isCorrect ? prev.activityData.visualDiscrimination.foundTargets + 1 : prev.activityData.visualDiscrimination.foundTargets,
          selectedItems: [...prev.activityData.visualDiscrimination.selectedItems || [], selectionKey]
        }
      }
    }));
    if (isCorrect) {
      const newFoundCount = visualData.foundTargets + 1;
      console.log(`✅ Correct target found! (${newFoundCount}/${visualData.totalTargets})`);
      if (newFoundCount >= visualData.totalTargets) {
        console.log("🎉 All targets found!");
        setTimeout(() => {
          handleCorrectAnswer();
        }, 1500);
      } else {
        setTimeout(() => {
          setGameState((prev) => ({
            ...prev,
            selectedLetter: null
          }));
        }, 500);
      }
    } else {
      console.log("❌ Wrong letter selected in visual discrimination");
      setTimeout(() => {
        setGameState((prev) => ({
          ...prev,
          selectedLetter: null
        }));
      }, 1e3);
    }
  };
  const handleCorrectAnswer = reactExports.useCallback(() => {
    setGameState((prev) => {
      const newScore = prev.score + 10;
      const newRound = prev.round + 1;
      const totalAttempts = prev.round;
      const correctAnswers = Math.floor(prev.score / 10) + 1;
      const newAccuracy = Math.round(correctAnswers / totalAttempts * 100);
      const updatedHistory = [...prev.letterHistory];
      if (prev.targetLetter && prev.currentActivity === "letter_selection") {
        updatedHistory.push(prev.targetLetter);
        if (updatedHistory.length > 3) {
          updatedHistory.shift();
        }
      }
      const updatedWordHistory = [...prev.wordHistory];
      if (prev.currentActivity === "word_formation" && prev.activityData?.wordFormation?.currentWord) {
        updatedWordHistory.push(prev.activityData.wordFormation.currentWord.word);
        if (updatedWordHistory.length > 3) {
          updatedWordHistory.shift();
        }
      }
      const updatedSequenceHistory = [...prev.sequenceHistory];
      if (prev.currentActivity === "sequence_recognition" && prev.activityData?.sequenceRecognition?.currentSequence) {
        updatedSequenceHistory.push(prev.activityData.sequenceRecognition.currentSequence.description);
        if (updatedSequenceHistory.length > 3) {
          updatedSequenceHistory.shift();
        }
      }
      return {
        ...prev,
        selectedLetter: prev.targetLetter?.id || prev.selectedLetter,
        showFeedback: true,
        score: newScore,
        round: newRound,
        accuracy: newAccuracy,
        activityRoundCount: prev.activityRoundCount + 1,
        letterHistory: updatedHistory,
        wordHistory: updatedWordHistory,
        sequenceHistory: updatedSequenceHistory
      };
    });
    speak("Muito bem! Resposta correta!", {
      pitch: 1.3,
      rate: 0.9
    });
    setTimeout(() => {
      console.log("🎯 Avançando para próxima pergunta automaticamente...");
      setGameState((prev) => ({
        ...prev,
        showFeedback: false,
        selectedLetter: null
        // Reset selection
      }));
      generateNewRound();
    }, 1500);
  }, [speak, generateNewRound]);
  const handleIncorrectAnswer = reactExports.useCallback(() => {
    setGameState((prev) => {
      const totalAttempts = prev.round;
      const correctAnswers = Math.floor(prev.score / 10);
      const newAccuracy = totalAttempts > 0 ? Math.round(correctAnswers / totalAttempts * 100) : 100;
      return {
        ...prev,
        showFeedback: true,
        accuracy: newAccuracy,
        round: prev.round + 1,
        activityRoundCount: prev.activityRoundCount + 1
      };
    });
    speak("Tente novamente! Você consegue!", {
      pitch: 1,
      rate: 0.8
    });
    setTimeout(() => {
      setGameState((prev) => ({
        ...prev,
        showFeedback: false
      }));
      generateNewRound();
    }, 1500);
  }, [speak, generateNewRound]);
  const renderLetterSelection = () => {
    const activityData = gameState.activityData.letterSelection || {};
    console.log("🔤 Rendering Letter Selection - Debug:", {
      activityData,
      gameState: gameState.activityData,
      currentActivity: gameState.currentActivity
    });
    if (!gameState.targetLetter || !gameState.availableLetters) {
      console.log("🚨 Letter selection data not initialized:", {
        targetLetter: gameState.targetLetter,
        availableLetters: gameState.availableLetters
      });
      return /* @__PURE__ */ React.createElement("div", { className: styles.gameArea, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 1919,
        columnNumber: 9
      } }, /* @__PURE__ */ React.createElement("div", { className: styles.soundActivity, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 1920,
        columnNumber: 11
      } }, /* @__PURE__ */ React.createElement("div", { className: styles.soundIndicator, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 1921,
        columnNumber: 13
      } }, "�"), /* @__PURE__ */ React.createElement("h3", { __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 1922,
        columnNumber: 13
      } }, "🔄 Carregando seleção de letras..."), /* @__PURE__ */ React.createElement("p", { className: styles.activityTip, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 1923,
        columnNumber: 13
      } }, "Aguarde enquanto preparamos as letras para você!")));
    }
    console.log("🔤 Rendering letter selection:", {
      targetLetter: gameState.targetLetter,
      availableLetters: gameState.availableLetters,
      selectedLetter: gameState.selectedLetter
    });
    return /* @__PURE__ */ React.createElement("div", { className: styles.gameArea, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1936,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.soundActivity, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1937,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.soundIndicator, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1938,
      columnNumber: 11
    } }, gameState.targetLetter.example?.split(" ")[0] || "🔤"), /* @__PURE__ */ React.createElement("h3", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1939,
      columnNumber: 11
    } }, "Encontre a letra: ", /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1939,
      columnNumber: 33
    } }, gameState.targetLetter.letter)), /* @__PURE__ */ React.createElement("p", { className: styles.activityTip, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1940,
      columnNumber: 11
    } }, gameState.targetLetter.example, " - Clique na letra correta")), /* @__PURE__ */ React.createElement("div", { className: styles.lettersGrid, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1946,
      columnNumber: 9
    } }, gameState.availableLetters.map((letter, letterIndex) => /* @__PURE__ */ React.createElement("div", { key: `${letter.id}-${letterIndex}`, className: `${styles.letterCard} ${gameState.selectedLetter === letter.id ? "selected" : ""} ${gameState.selectedLetter === letter.id && gameState.selectedLetter === gameState.targetLetter.id ? "correct" : ""} ${gameState.selectedLetter === letter.id && gameState.selectedLetter !== gameState.targetLetter.id ? "incorrect" : ""}`, onClick: () => handleLetterSelect(letter.id), onMouseEnter: () => {
      if (ttsActive2 && !gameState.selectedLetter) {
        speakLetter(letter.id);
      }
    }, onTouchStart: () => {
      if (ttsActive2 && !gameState.selectedLetter) {
        speakLetter(letter.id);
      }
    }, title: `Letra ${letter.letter} - ${letter.sound}`, style: {
      cursor: "pointer",
      backgroundColor: letter.color
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1948,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.letterContent, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1976,
      columnNumber: 15
    } }, letter.letter), /* @__PURE__ */ React.createElement("div", { className: styles.letterLabel, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1977,
      columnNumber: 15
    } }, letter.sound)))));
  };
  const renderWordFormation = () => {
    const wordData = gameState.activityData.wordFormation;
    console.log("🔤 Rendering Word Formation - Debug:", {
      wordData,
      gameState: gameState.activityData,
      currentActivity: gameState.currentActivity
    });
    if (!wordData || !wordData.currentWord) {
      console.log("🚨 Word formation data not initialized:", wordData);
      return /* @__PURE__ */ React.createElement("div", { className: styles.wordFormationActivity, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2001,
        columnNumber: 9
      } }, /* @__PURE__ */ React.createElement("div", { className: styles.activityInstruction, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2002,
        columnNumber: 11
      } }, /* @__PURE__ */ React.createElement("h3", { __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2003,
        columnNumber: 13
      } }, "🔄 Carregando formação de palavras..."), /* @__PURE__ */ React.createElement("p", { __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2004,
        columnNumber: 13
      } }, "Aguarde enquanto preparamos as palavras para você!")));
    }
    const placedLetters = wordData.placedLetters || [];
    const nextEmptyIndex = placedLetters.findIndex((slot) => slot === null || slot === void 0);
    const isWordComplete = placedLetters.every((slot) => slot !== null && slot !== void 0);
    console.log("🔤 Rendering word formation:", {
      currentWord: wordData.currentWord,
      placedLetters,
      availableLetters: wordData.availableLetters,
      nextEmptyIndex,
      isWordComplete
    });
    return /* @__PURE__ */ React.createElement("div", { className: styles.gameArea, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2023,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.soundActivity, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2024,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.soundIndicator, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2025,
      columnNumber: 11
    } }, wordData.currentWord.emoji), /* @__PURE__ */ React.createElement("h3", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2026,
      columnNumber: 11
    } }, "Monte a palavra: ", /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2026,
      columnNumber: 32
    } }, wordData.currentWord.word)), /* @__PURE__ */ React.createElement("p", { className: styles.activityTip, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2027,
      columnNumber: 11
    } }, wordData.currentWord.meaning, " - Clique nas letras na ordem correta")), /* @__PURE__ */ React.createElement("div", { className: styles.lettersGrid, style: {
      marginBottom: "2rem"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2033,
      columnNumber: 9
    } }, wordData.currentWord.word.split("").map((correctLetter, index) => /* @__PURE__ */ React.createElement("div", { key: index, className: `${styles.letterCard} ${placedLetters[index] ? "correct" : ""} ${index === nextEmptyIndex ? "active" : ""}`, style: {
      background: placedLetters[index] ? "var(--success-bg)" : index === nextEmptyIndex ? "rgba(255, 193, 7, 0.2)" : "var(--card-background)",
      border: placedLetters[index] ? "2px solid var(--success-border)" : index === nextEmptyIndex ? "2px solid #FFC107" : "2px dashed rgba(255, 255, 255, 0.5)",
      minHeight: "80px",
      cursor: "default"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2035,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.letterContent, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2057,
      columnNumber: 15
    } }, placedLetters[index] || "_"), /* @__PURE__ */ React.createElement("div", { className: styles.letterLabel, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2060,
      columnNumber: 15
    } }, "Posição ", index + 1)))), /* @__PURE__ */ React.createElement("div", { className: styles.lettersGrid, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2068,
      columnNumber: 9
    } }, wordData.availableLetters.map((letter, letterIndex) => /* @__PURE__ */ React.createElement("div", { key: `${letter}-${letterIndex}`, className: `${styles.letterCard}`, onClick: () => handleWordLetterSelect(letter, letterIndex), onMouseEnter: () => {
      if (ttsActive2) {
        speak(letter, {
          rate: 0.6,
          pitch: 1.1
        });
      }
    }, onTouchStart: () => {
      if (ttsActive2) {
        speak(letter, {
          rate: 0.6,
          pitch: 1.1
        });
      }
    }, title: `Letra ${letter}`, style: {
      cursor: "pointer"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2070,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.letterContent, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2089,
      columnNumber: 15
    } }, letter), /* @__PURE__ */ React.createElement("div", { className: styles.letterLabel, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2090,
      columnNumber: 15
    } }, "Clique para usar")))));
  };
  const renderSequenceRecognition = () => {
    const sequenceData = gameState.activityData.sequenceRecognition;
    const currentSequence = sequenceData?.currentSequence;
    console.log("🔍 Rendering sequence recognition:", {
      sequenceData,
      currentSequence
    });
    if (!currentSequence) {
      console.log("🚨 Sequence recognition data not initialized:", sequenceData);
      return /* @__PURE__ */ React.createElement("div", { className: styles.gameArea, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2110,
        columnNumber: 9
      } }, /* @__PURE__ */ React.createElement("div", { className: styles.soundActivity, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2111,
        columnNumber: 11
      } }, /* @__PURE__ */ React.createElement("div", { className: styles.soundIndicator, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2112,
        columnNumber: 13
      } }, "📝"), /* @__PURE__ */ React.createElement("h3", { __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2113,
        columnNumber: 13
      } }, "🔄 Carregando reconhecimento de sequência..."), /* @__PURE__ */ React.createElement("p", { className: styles.activityTip, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2114,
        columnNumber: 13
      } }, "Aguarde enquanto preparamos as sequências para você!")));
    }
    return /* @__PURE__ */ React.createElement("div", { className: styles.gameArea, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2121,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.soundActivity, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2122,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.soundIndicator, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2123,
      columnNumber: 11
    } }, "📝"), /* @__PURE__ */ React.createElement("h3", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2124,
      columnNumber: 11
    } }, "Complete a sequência alfabética:"), /* @__PURE__ */ React.createElement("style", { jsx: true, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2127,
      columnNumber: 11
    } }, `
            @keyframes pulse {
              0% { transform: scale(1); opacity: 1; }
              50% { transform: scale(1.1); opacity: 0.7; }
              100% { transform: scale(1); opacity: 1; }
            }
          `), /* @__PURE__ */ React.createElement("div", { className: styles.sequenceDisplay, style: {
      display: "flex",
      gap: "1.5rem",
      justifyContent: "center",
      margin: "3rem 0",
      fontSize: "3.5rem",
      fontWeight: "bold"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2136,
      columnNumber: 11
    } }, currentSequence.sequence.map((letter, index) => /* @__PURE__ */ React.createElement("span", { key: index, className: styles.sequenceLetter, style: {
      padding: "1.5rem 2rem",
      background: "linear-gradient(135deg, var(--card-background), rgba(255,255,255,0.1))",
      border: "3px solid var(--primary-color)",
      borderRadius: "16px",
      minWidth: "80px",
      minHeight: "80px",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      textAlign: "center",
      color: "var(--text-color)",
      boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
      transition: "transform 0.2s ease"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2145,
      columnNumber: 15
    } }, letter)), /* @__PURE__ */ React.createElement("span", { className: styles.sequenceLetter, style: {
      padding: "1.5rem 2rem",
      background: "linear-gradient(135deg, rgba(255, 193, 7, 0.3), rgba(255, 193, 7, 0.1))",
      border: "3px solid #FFC107",
      borderRadius: "16px",
      minWidth: "80px",
      minHeight: "80px",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      textAlign: "center",
      color: "#FFD700",
      fontSize: "4rem",
      fontWeight: "bold",
      animation: "pulse 1.5s infinite",
      boxShadow: "0 6px 12px rgba(255, 193, 7, 0.3)"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2163,
      columnNumber: 13
    } }, "?")), /* @__PURE__ */ React.createElement("p", { className: styles.activityTip, style: {
      fontSize: "1.4rem",
      fontWeight: "600",
      color: "var(--primary-color)",
      textAlign: "center",
      margin: "1.5rem 0",
      padding: "1rem",
      background: "rgba(255,255,255,0.05)",
      borderRadius: "12px",
      border: "1px solid rgba(255,255,255,0.1)"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2184,
      columnNumber: 11
    } }, currentSequence.description)), /* @__PURE__ */ React.createElement("div", { className: styles.lettersGrid, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2199,
      columnNumber: 9
    } }, currentSequence.options.map((option, optionIndex) => /* @__PURE__ */ React.createElement("div", { key: `${option}-${optionIndex}`, className: `${styles.letterCard} ${gameState.selectedLetter === option ? "selected" : ""} ${gameState.selectedLetter === option && option === currentSequence.missing ? "correct" : ""} ${gameState.selectedLetter === option && option !== currentSequence.missing ? "incorrect" : ""}`, onClick: () => handleSequenceSelect(option), onMouseEnter: () => {
      if (ttsActive2 && !gameState.selectedLetter) {
        speak(`Letra ${option}`, {
          rate: 0.6,
          pitch: 1.2
        });
      }
    }, onTouchStart: () => {
      if (ttsActive2 && !gameState.selectedLetter) {
        speak(`Letra ${option}`, {
          rate: 0.6,
          pitch: 1.2
        });
      }
    }, title: `Letra ${option}`, style: {
      cursor: gameState.selectedLetter ? "default" : "pointer"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2201,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.letterContent, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2228,
      columnNumber: 15
    } }, option), /* @__PURE__ */ React.createElement("div", { className: styles.letterLabel, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2229,
      columnNumber: 15
    } }, gameState.selectedLetter === option ? option === currentSequence.missing ? "✅ Correto!" : "❌ Incorreto" : "Opção")))));
  };
  const renderVisualDiscrimination = () => {
    const visualData = gameState.activityData.visualDiscrimination;
    const currentGroup = visualData?.currentGroup;
    console.log("🔍 Rendering visual discrimination:", {
      visualData,
      currentGroup
    });
    if (!currentGroup) {
      console.log("🚨 Visual discrimination data not initialized:", visualData);
      return /* @__PURE__ */ React.createElement("div", { className: styles.gameArea, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2252,
        columnNumber: 9
      } }, /* @__PURE__ */ React.createElement("div", { className: styles.soundActivity, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2253,
        columnNumber: 11
      } }, /* @__PURE__ */ React.createElement("div", { className: styles.soundIndicator, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2254,
        columnNumber: 13
      } }, "👁️"), /* @__PURE__ */ React.createElement("h3", { __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2255,
        columnNumber: 13
      } }, "🔄 Carregando discriminação visual..."), /* @__PURE__ */ React.createElement("p", { className: styles.activityTip, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2256,
        columnNumber: 13
      } }, "Aguarde enquanto preparamos o desafio visual para você!")));
    }
    return /* @__PURE__ */ React.createElement("div", { className: styles.gameArea, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2263,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.soundActivity, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2264,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.soundIndicator, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2265,
      columnNumber: 11
    } }, "👁️"), /* @__PURE__ */ React.createElement("h3", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2266,
      columnNumber: 11
    } }, "Encontre todas as letras: ", /* @__PURE__ */ React.createElement("strong", { style: {
      color: "#FFD700",
      fontSize: "1.5em",
      textShadow: "2px 2px 4px rgba(0,0,0,0.3)"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2266,
      columnNumber: 41
    } }, currentGroup.target)), /* @__PURE__ */ React.createElement("style", { jsx: true, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2273,
      columnNumber: 11
    } }, `
            @keyframes targetGlow {
              0% { box-shadow: 0 0 5px #FFD700; }
              50% { box-shadow: 0 0 20px #FFD700, 0 0 30px #FFD700; }
              100% { box-shadow: 0 0 5px #FFD700; }
            }
            .target-highlight {
              animation: targetGlow 2s infinite;
            }
          `), /* @__PURE__ */ React.createElement("div", { style: {
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      gap: "1rem",
      margin: "1.5rem 0",
      padding: "1rem",
      background: "rgba(255,255,255,0.05)",
      borderRadius: "12px",
      border: "1px solid rgba(255,255,255,0.1)"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2285,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("span", { style: {
      fontSize: "1.2rem",
      fontWeight: "600"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2296,
      columnNumber: 13
    } }, "Progresso:"), Array.from({
      length: visualData.totalTargets
    }, (_, i) => /* @__PURE__ */ React.createElement("div", { key: i, style: {
      width: "40px",
      height: "40px",
      borderRadius: "50%",
      background: i < visualData.foundTargets ? "linear-gradient(135deg, #4CAF50, #45a049)" : "rgba(255,255,255,0.1)",
      border: i < visualData.foundTargets ? "2px solid #4CAF50" : "2px solid rgba(255,255,255,0.3)",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      fontSize: "1.2rem",
      fontWeight: "bold",
      transition: "all 0.3s ease"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2298,
      columnNumber: 15
    } }, i < visualData.foundTargets ? "✅" : "⭕"))), /* @__PURE__ */ React.createElement("p", { className: styles.activityTip, style: {
      fontSize: "1.3rem",
      fontWeight: "600",
      color: "var(--primary-color)",
      textAlign: "center",
      margin: "1rem 0",
      padding: "1rem",
      background: "rgba(255,255,255,0.05)",
      borderRadius: "12px",
      border: "1px solid rgba(255,255,255,0.1)"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2320,
      columnNumber: 11
    } }, "Encontradas: ", /* @__PURE__ */ React.createElement("span", { style: {
      color: "#4CAF50"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2331,
      columnNumber: 26
    } }, visualData.foundTargets), "/", visualData.totalTargets)), /* @__PURE__ */ React.createElement("div", { className: styles.lettersGrid, style: {
      display: "grid",
      gridTemplateColumns: "repeat(auto-fit, minmax(120px, 1fr))",
      gap: "1.5rem",
      maxWidth: "600px",
      margin: "0 auto"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2336,
      columnNumber: 9
    } }, currentGroup.items.map((item, index) => {
      const itemKey = `${item}-${index}`;
      const isSelected = visualData.selectedItems?.includes(itemKey);
      const isTarget = item === currentGroup.target;
      const isCorrectSelection = isSelected && isTarget;
      const isIncorrectSelection = isSelected && !isTarget;
      return /* @__PURE__ */ React.createElement("div", { key: itemKey, className: `${styles.letterCard} ${isCorrectSelection ? "correct" : isIncorrectSelection ? "incorrect" : ""}`, onClick: () => handleVisualSelect(item, index), onMouseEnter: () => {
        if (ttsActive2 && !isSelected) {
          speak(`Letra ${item}`, {
            rate: 0.6,
            pitch: 1.1
          });
        }
      }, onTouchStart: () => {
        if (ttsActive2 && !isSelected) {
          speak(`Letra ${item}`, {
            rate: 0.6,
            pitch: 1.1
          });
        }
      }, title: `Letra ${item}`, style: {
        minHeight: "100px",
        minWidth: "100px",
        opacity: isSelected ? isCorrectSelection ? 1 : 0.5 : 1,
        cursor: isSelected ? "default" : "pointer",
        background: isCorrectSelection ? "linear-gradient(135deg, #4CAF50, #45a049)" : isIncorrectSelection ? "linear-gradient(135deg, #f44336, #d32f2f)" : "var(--card-background)",
        // REMOVIDO: destaque dourado das letras alvo
        border: isCorrectSelection ? "3px solid #4CAF50" : isIncorrectSelection ? "3px solid #f44336" : "2px solid rgba(255,255,255,0.2)",
        // REMOVIDO: borda dourada das letras alvo
        transform: isSelected ? "scale(0.95)" : "scale(1)",
        transition: "all 0.3s ease",
        boxShadow: isCorrectSelection ? "0 8px 16px rgba(76, 175, 80, 0.3)" : isIncorrectSelection ? "0 8px 16px rgba(244, 67, 54, 0.3)" : "0 4px 8px rgba(0,0,0,0.1)"
      }, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2351,
        columnNumber: 15
      } }, /* @__PURE__ */ React.createElement("div", { className: styles.letterContent, style: {
        fontSize: "2.5rem",
        fontWeight: "bold",
        color: isCorrectSelection || isIncorrectSelection ? "#fff" : "var(--text-color)"
      }, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2393,
        columnNumber: 17
      } }, item), /* @__PURE__ */ React.createElement("div", { className: styles.letterLabel, style: {
        color: isCorrectSelection || isIncorrectSelection ? "#fff" : "var(--text-secondary)",
        fontWeight: "600"
      }, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2400,
        columnNumber: 17
      } }, isCorrectSelection ? "✅ Encontrado!" : isIncorrectSelection ? "❌ Errado" : "Letra"));
    })), visualData.foundTargets >= visualData.totalTargets && /* @__PURE__ */ React.createElement("div", { style: {
      position: "fixed",
      top: "50%",
      left: "50%",
      transform: "translate(-50%, -50%)",
      background: "linear-gradient(135deg, #4CAF50, #45a049)",
      color: "white",
      padding: "2rem",
      borderRadius: "20px",
      fontSize: "1.5rem",
      fontWeight: "bold",
      textAlign: "center",
      boxShadow: "0 10px 30px rgba(0,0,0,0.3)",
      zIndex: 1e3,
      animation: "bounce 0.5s ease-in-out"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2415,
      columnNumber: 11
    } }, "🎉 Parabéns! Você encontrou todas as letras! 🎉"));
  };
  const renderPhoneticMatching = () => {
    const phoneticData = gameState.activityData.phoneticMatching;
    if (!phoneticData?.currentPair) {
      return /* @__PURE__ */ React.createElement("div", { className: styles.activityContainer, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2444,
        columnNumber: 9
      } }, /* @__PURE__ */ React.createElement("div", { className: styles.loadingMessage, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2445,
        columnNumber: 11
      } }, "🔊 Preparando correspondência fonética..."));
    }
    const {
      currentPair,
      options,
      selectedOption
    } = phoneticData;
    return /* @__PURE__ */ React.createElement("div", { className: styles.activityContainer, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2455,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.questionArea, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2457,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.mainLetter, style: {
      fontSize: "4rem",
      background: "linear-gradient(135deg, #FF6B6B, #4ECDC4)",
      borderRadius: "20px",
      padding: "2rem",
      color: "white",
      textAlign: "center",
      boxShadow: "0 10px 30px rgba(0,0,0,0.2)",
      cursor: "pointer"
    }, onClick: () => {
      if (ttsActive2) {
        speak(`Som da letra ${currentPair.letter}: ${currentPair.sound}`, {
          rate: 0.7,
          pitch: 1.2
        });
      }
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2458,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "3rem",
      marginBottom: "0.5rem"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2472,
      columnNumber: 13
    } }, currentPair.emoji), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "2.5rem",
      fontWeight: "bold"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2475,
      columnNumber: 13
    } }, currentPair.letter), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1.2rem",
      marginTop: "0.5rem",
      opacity: 0.9
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2478,
      columnNumber: 13
    } }, "🔊 Clique para ouvir")), /* @__PURE__ */ React.createElement("div", { className: styles.questionText, style: {
      fontSize: "1.5rem",
      textAlign: "center",
      margin: "1.5rem 0",
      color: "var(--text-color)"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2483,
      columnNumber: 11
    } }, "🎵 Qual palavra combina com o som desta letra?")), /* @__PURE__ */ React.createElement("div", { className: styles.answerOptions, style: {
      display: "grid",
      gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
      gap: "1rem",
      margin: "2rem 0"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2494,
      columnNumber: 9
    } }, options.map((option, index) => {
      const isSelected = selectedOption === option.word;
      const isCorrect = isSelected && option.word === currentPair.correctWord;
      const isIncorrect = isSelected && option.word !== currentPair.correctWord;
      return /* @__PURE__ */ React.createElement("div", { key: `${option.word}-${index}`, className: `${styles.answerOption} ${isSelected ? "selected" : ""}`, onClick: () => {
        if (!selectedOption) {
          console.log("Phonetic option selected:", option.word);
        }
      }, onMouseEnter: () => {
        if (ttsActive2 && !selectedOption) {
          speak(`${option.emoji} ${option.word}`, {
            rate: 0.8
          });
        }
      }, style: {
        background: isCorrect ? "linear-gradient(135deg, #4CAF50, #45a049)" : isIncorrect ? "linear-gradient(135deg, #f44336, #d32f2f)" : "var(--card-background)",
        border: isCorrect ? "3px solid #4CAF50" : isIncorrect ? "3px solid #f44336" : "2px solid rgba(255,255,255,0.2)",
        borderRadius: "15px",
        padding: "1.5rem",
        textAlign: "center",
        cursor: selectedOption ? "default" : "pointer",
        transform: isSelected ? "scale(0.95)" : "scale(1)",
        transition: "all 0.3s ease",
        boxShadow: isCorrect ? "0 8px 16px rgba(76, 175, 80, 0.3)" : isIncorrect ? "0 8px 16px rgba(244, 67, 54, 0.3)" : "0 4px 8px rgba(0,0,0,0.1)"
      }, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2506,
        columnNumber: 15
      } }, /* @__PURE__ */ React.createElement("div", { style: {
        fontSize: "2.5rem",
        marginBottom: "0.5rem",
        color: isCorrect || isIncorrect ? "#fff" : "var(--text-color)"
      }, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2544,
        columnNumber: 17
      } }, option.emoji), /* @__PURE__ */ React.createElement("div", { style: {
        fontSize: "1.3rem",
        fontWeight: "bold",
        color: isCorrect || isIncorrect ? "#fff" : "var(--text-color)"
      }, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2551,
        columnNumber: 17
      } }, option.word), /* @__PURE__ */ React.createElement("div", { style: {
        fontSize: "0.9rem",
        marginTop: "0.5rem",
        color: isCorrect || isIncorrect ? "#fff" : "var(--text-secondary)"
      }, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 2558,
        columnNumber: 17
      } }, isCorrect ? "✅ Correto!" : isIncorrect ? "❌ Errado" : "Clique para escolher"));
    })));
  };
  if (showStartScreen) {
    return /* @__PURE__ */ React.createElement(GameStartScreen, { gameTitle: "Reconhecimento de Letras", gameDescription: "Aprenda o alfabeto de forma divertida e interativa", gameIcon: "🔤", onStart: startGame, onBack, difficulties: [{
      id: "easy",
      name: "Fácil",
      description: "4 letras básicas\nIdeal para iniciantes",
      icon: "😊"
    }, {
      id: "medium",
      name: "Médio",
      description: "6 letras variadas\nDesafio equilibrado",
      icon: "🎯"
    }, {
      id: "hard",
      name: "Avançado",
      description: "8 letras completas\nPara especialistas",
      icon: "🚀"
    }], __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2578,
      columnNumber: 7
    } });
  }
  return /* @__PURE__ */ React.createElement("div", { className: styles.letterRecognitionGame, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2594,
    columnNumber: 5
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.gameContent, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2595,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.gameHeader, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2597,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("h1", { className: styles.gameTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2598,
    columnNumber: 11
  } }, "🔤 Reconhecimento de Letras V3", /* @__PURE__ */ React.createElement("div", { style: {
    fontSize: "0.7rem",
    opacity: 0.8,
    marginTop: "0.25rem"
  }, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2600,
    columnNumber: 13
  } }, ACTIVITY_TYPES[gameState.currentActivity.toUpperCase()]?.name || "Seleção de Letras")), /* @__PURE__ */ React.createElement("button", { className: `${styles.headerTtsButton} ${ttsActive2 ? styles.ttsActive : ""}`, onClick: toggleTTS, title: ttsActive2 ? "Desativar TTS" : "Ativar TTS", "aria-label": ttsActive2 ? "Desativar TTS" : "Ativar TTS", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2604,
    columnNumber: 11
  } }, ttsActive2 ? "🔊" : "🔇")), /* @__PURE__ */ React.createElement("div", { className: styles.gameStats, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2615,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statCard, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2616,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statValue, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2617,
    columnNumber: 13
  } }, gameState.score), /* @__PURE__ */ React.createElement("div", { className: styles.statLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2618,
    columnNumber: 13
  } }, "Pontos")), /* @__PURE__ */ React.createElement("div", { className: styles.statCard, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2620,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statValue, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2621,
    columnNumber: 13
  } }, gameState.round), /* @__PURE__ */ React.createElement("div", { className: styles.statLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2622,
    columnNumber: 13
  } }, "Rodada")), /* @__PURE__ */ React.createElement("div", { className: styles.statCard, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2624,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statValue, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2625,
    columnNumber: 13
  } }, gameState.accuracy, "%"), /* @__PURE__ */ React.createElement("div", { className: styles.statLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2626,
    columnNumber: 13
  } }, "Precisão"))), /* @__PURE__ */ React.createElement("div", { className: styles.activityMenu, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2631,
    columnNumber: 9
  } }, Object.values(ACTIVITY_TYPES).map((activity) => /* @__PURE__ */ React.createElement("button", { key: activity.id, className: `${styles.activityButton} ${gameState.currentActivity === activity.id ? styles.active : ""}`, onClick: () => changeActivity(activity.id), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2633,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2640,
    columnNumber: 15
  } }, activity.icon), /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2641,
    columnNumber: 15
  } }, activity.name)))), gameState.currentActivity === "letter_selection" && renderLetterSelection(), gameState.currentActivity === "word_formation" && renderWordFormation(), gameState.currentActivity === "sequence_recognition" && renderSequenceRecognition(), gameState.currentActivity === "visual_discrimination" && renderVisualDiscrimination(), gameState.currentActivity === "phonetic_matching" && renderPhoneticMatching(), /* @__PURE__ */ React.createElement("div", { className: styles.gameControls, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2654,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("button", { className: styles.controlButton, onClick: explainGame, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2655,
    columnNumber: 11
  } }, "🔊 Explicar"), /* @__PURE__ */ React.createElement("button", { className: styles.controlButton, onClick: restartGame, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2658,
    columnNumber: 11
  } }, "🔄 Reiniciar"), /* @__PURE__ */ React.createElement("button", { className: styles.controlButton, onClick: onBack, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2661,
    columnNumber: 11
  } }, "⬅️ Voltar"))));
}
const LetterRecognitionGame$1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: LetterRecognitionGame
}, Symbol.toStringTag, { value: "Module" }));
export {
  LetterRecognitionProcessors as L,
  LetterRecognitionCollectorsHub as a,
  LetterRecognitionGame$1 as b
};
//# sourceMappingURL=game-letters-BnM9Bog3.js.map
