{"version": 3, "names": ["_core", "require", "_traverse", "findBareSupers", "visitors", "environmentVisitor", "Super", "path", "node", "parentPath", "isCallExpression", "callee", "push", "referenceVisitor", "TSTypeAnnotation|TypeAnnotation", "skip", "ReferencedIdentifier", "scope", "hasOwnBinding", "name", "rename", "handleClassTDZ", "state", "classBinding", "getBinding", "classNameTDZError", "file", "addHelper", "throwNode", "t", "callExpression", "stringLiteral", "replaceWith", "sequenceExpression", "classFieldDefinitionEvaluationTDZVisitor", "injectInitialization", "constructor", "nodes", "renamer", "lastReturnsThis", "length", "isDerived", "superClass", "newConstructor", "classMethod", "identifier", "blockStatement", "params", "restElement", "body", "template", "statement", "ast", "get", "unshiftContainer", "bareSupers", "traverse", "<PERSON><PERSON><PERSON><PERSON>", "bareSuper", "map", "n", "cloneNode", "isExpressionStatement", "allNodes", "toExpression", "thisExpression", "insertAfter", "memoiseComputedKey", "keyNode", "hint", "isUidReference", "isIdentifier", "hasUid", "isMemoiseAssignment", "isAssignmentExpression", "operator", "left", "ident", "id", "kind", "assignmentExpression", "extractComputedKeys", "computedPaths", "declarations", "computedPath", "computedKey", "isReferencedIdentifier", "computedNode", "isConstantExpression", "assignment", "generateUidBasedOnNode", "expressionStatement", "key"], "sources": ["../src/misc.ts"], "sourcesContent": ["import { template, types as t } from \"@babel/core\";\nimport type { File, NodePath, Scope, Visitor } from \"@babel/core\";\nimport { visitors } from \"@babel/traverse\";\n\nconst findBareSupers = visitors.environmentVisitor<\n  NodePath<t.CallExpression>[]\n>({\n  Super(path) {\n    const { node, parentPath } = path;\n    if (parentPath.isCallExpression({ callee: node })) {\n      this.push(parentPath);\n    }\n  },\n});\n\nconst referenceVisitor: Visitor<{ scope: Scope }> = {\n  \"TSTypeAnnotation|TypeAnnotation\"(\n    path: NodePath<t.TSTypeAnnotation | t.TypeAnnotation>,\n  ) {\n    path.skip();\n  },\n\n  ReferencedIdentifier(path: NodePath<t.Identifier>, { scope }) {\n    if (scope.hasOwnBinding(path.node.name)) {\n      scope.rename(path.node.name);\n      path.skip();\n    }\n  },\n};\n\ntype HandleClassTDZState = {\n  classBinding: Scope.Binding;\n  file: File;\n};\n\nfunction handleClassTDZ(\n  path: NodePath<t.Identifier>,\n  state: HandleClassTDZState,\n) {\n  if (\n    state.classBinding &&\n    state.classBinding === path.scope.getBinding(path.node.name)\n  ) {\n    const classNameTDZError = state.file.addHelper(\"classNameTDZError\");\n    const throwNode = t.callExpression(classNameTDZError, [\n      t.stringLiteral(path.node.name),\n    ]);\n\n    path.replaceWith(t.sequenceExpression([throwNode, path.node]));\n    path.skip();\n  }\n}\n\nconst classFieldDefinitionEvaluationTDZVisitor: Visitor<HandleClassTDZState> = {\n  ReferencedIdentifier: handleClassTDZ,\n  \"TSTypeAnnotation|TypeAnnotation\"(path) {\n    path.skip();\n  },\n};\n\ninterface RenamerState {\n  scope: Scope;\n}\n\nexport function injectInitialization(\n  path: NodePath<t.Class>,\n  constructor: NodePath<t.ClassMethod> | undefined,\n  nodes: t.ExpressionStatement[],\n  renamer?: (visitor: Visitor<RenamerState>, state: RenamerState) => void,\n  lastReturnsThis?: boolean,\n) {\n  if (!nodes.length) return;\n\n  const isDerived = !!path.node.superClass;\n\n  if (!constructor) {\n    const newConstructor = t.classMethod(\n      \"constructor\",\n      t.identifier(\"constructor\"),\n      [],\n      t.blockStatement([]),\n    );\n\n    if (isDerived) {\n      newConstructor.params = [t.restElement(t.identifier(\"args\"))];\n      newConstructor.body.body.push(template.statement.ast`super(...args)`);\n    }\n\n    [constructor] = path\n      .get(\"body\")\n      .unshiftContainer(\"body\", newConstructor) as NodePath<t.ClassMethod>[];\n  }\n\n  if (renamer) {\n    renamer(referenceVisitor, { scope: constructor.scope });\n  }\n\n  if (isDerived) {\n    const bareSupers: NodePath<t.CallExpression>[] = [];\n    constructor.traverse(findBareSupers, bareSupers);\n    let isFirst = true;\n    for (const bareSuper of bareSupers) {\n      if (isFirst) {\n        isFirst = false;\n      } else {\n        nodes = nodes.map(n => t.cloneNode(n));\n      }\n      if (!bareSuper.parentPath.isExpressionStatement()) {\n        const allNodes: t.Expression[] = [\n          bareSuper.node,\n          ...nodes.map(n => t.toExpression(n)),\n        ];\n        if (!lastReturnsThis) allNodes.push(t.thisExpression());\n        bareSuper.replaceWith(t.sequenceExpression(allNodes));\n      } else {\n        bareSuper.insertAfter(nodes);\n      }\n    }\n  } else {\n    constructor.get(\"body\").unshiftContainer(\"body\", nodes);\n  }\n}\n\ntype ComputedKeyAssignmentExpression = t.AssignmentExpression & {\n  left: t.Identifier;\n};\n\n/**\n * Try to memoise a computed key.\n * It returns undefined when the computed key is an uid reference, otherwise\n * an assignment expression `memoiserId = computed key`\n * @export\n * @param {t.Expression} keyNode Computed key\n * @param {Scope} scope The scope where memoiser id should be registered\n * @param {string} hint The memoiser id hint\n * @returns {(ComputedKeyAssignmentExpression | undefined)}\n */\nexport function memoiseComputedKey(\n  keyNode: t.Expression,\n  scope: Scope,\n  hint: string,\n): ComputedKeyAssignmentExpression | undefined {\n  const isUidReference = t.isIdentifier(keyNode) && scope.hasUid(keyNode.name);\n  if (isUidReference) {\n    return;\n  }\n  const isMemoiseAssignment =\n    t.isAssignmentExpression(keyNode, { operator: \"=\" }) &&\n    t.isIdentifier(keyNode.left) &&\n    scope.hasUid(keyNode.left.name);\n  if (isMemoiseAssignment) {\n    return t.cloneNode(keyNode as ComputedKeyAssignmentExpression);\n  } else {\n    const ident = t.identifier(hint);\n    // Declaring in the same block scope\n    // Ref: https://github.com/babel/babel/pull/10029/files#diff-fbbdd83e7a9c998721c1484529c2ce92\n    scope.push({\n      id: ident,\n      kind: \"let\",\n    });\n    return t.assignmentExpression(\n      \"=\",\n      t.cloneNode(ident),\n      keyNode,\n    ) as ComputedKeyAssignmentExpression;\n  }\n}\n\nexport function extractComputedKeys(\n  path: NodePath<t.Class>,\n  computedPaths: NodePath<t.ClassProperty | t.ClassMethod>[],\n  file: File,\n) {\n  const { scope } = path;\n  const declarations: t.ExpressionStatement[] = [];\n  const state = {\n    classBinding: path.node.id && scope.getBinding(path.node.id.name),\n    file,\n  };\n  for (const computedPath of computedPaths) {\n    const computedKey = computedPath.get(\"key\");\n    if (computedKey.isReferencedIdentifier()) {\n      handleClassTDZ(computedKey, state);\n    } else {\n      computedKey.traverse(classFieldDefinitionEvaluationTDZVisitor, state);\n    }\n\n    const computedNode = computedPath.node;\n    // Make sure computed property names are only evaluated once (upon class definition)\n    // and in the right order in combination with static properties\n    if (!computedKey.isConstantExpression()) {\n      const assignment = memoiseComputedKey(\n        computedKey.node,\n        scope,\n        scope.generateUidBasedOnNode(computedKey.node),\n      );\n      if (assignment) {\n        declarations.push(t.expressionStatement(assignment));\n        computedNode.key = t.cloneNode(assignment.left);\n      }\n    }\n  }\n\n  return declarations;\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAEA,IAAAC,SAAA,GAAAD,OAAA;AAEA,MAAME,cAAc,GAAGC,kBAAQ,CAACC,kBAAkB,CAEhD;EACAC,KAAKA,CAACC,IAAI,EAAE;IACV,MAAM;MAAEC,IAAI;MAAEC;IAAW,CAAC,GAAGF,IAAI;IACjC,IAAIE,UAAU,CAACC,gBAAgB,CAAC;MAAEC,MAAM,EAAEH;IAAK,CAAC,CAAC,EAAE;MACjD,IAAI,CAACI,IAAI,CAACH,UAAU,CAAC;IACvB;EACF;AACF,CAAC,CAAC;AAEF,MAAMI,gBAA2C,GAAG;EAClD,iCAAiCC,CAC/BP,IAAqD,EACrD;IACAA,IAAI,CAACQ,IAAI,CAAC,CAAC;EACb,CAAC;EAEDC,oBAAoBA,CAACT,IAA4B,EAAE;IAAEU;EAAM,CAAC,EAAE;IAC5D,IAAIA,KAAK,CAACC,aAAa,CAACX,IAAI,CAACC,IAAI,CAACW,IAAI,CAAC,EAAE;MACvCF,KAAK,CAACG,MAAM,CAACb,IAAI,CAACC,IAAI,CAACW,IAAI,CAAC;MAC5BZ,IAAI,CAACQ,IAAI,CAAC,CAAC;IACb;EACF;AACF,CAAC;AAOD,SAASM,cAAcA,CACrBd,IAA4B,EAC5Be,KAA0B,EAC1B;EACA,IACEA,KAAK,CAACC,YAAY,IAClBD,KAAK,CAACC,YAAY,KAAKhB,IAAI,CAACU,KAAK,CAACO,UAAU,CAACjB,IAAI,CAACC,IAAI,CAACW,IAAI,CAAC,EAC5D;IACA,MAAMM,iBAAiB,GAAGH,KAAK,CAACI,IAAI,CAACC,SAAS,CAAC,mBAAmB,CAAC;IACnE,MAAMC,SAAS,GAAGC,WAAC,CAACC,cAAc,CAACL,iBAAiB,EAAE,CACpDI,WAAC,CAACE,aAAa,CAACxB,IAAI,CAACC,IAAI,CAACW,IAAI,CAAC,CAChC,CAAC;IAEFZ,IAAI,CAACyB,WAAW,CAACH,WAAC,CAACI,kBAAkB,CAAC,CAACL,SAAS,EAAErB,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;IAC9DD,IAAI,CAACQ,IAAI,CAAC,CAAC;EACb;AACF;AAEA,MAAMmB,wCAAsE,GAAG;EAC7ElB,oBAAoB,EAAEK,cAAc;EACpC,iCAAiCP,CAACP,IAAI,EAAE;IACtCA,IAAI,CAACQ,IAAI,CAAC,CAAC;EACb;AACF,CAAC;AAMM,SAASoB,oBAAoBA,CAClC5B,IAAuB,EACvB6B,WAAgD,EAChDC,KAA8B,EAC9BC,OAAuE,EACvEC,eAAyB,EACzB;EACA,IAAI,CAACF,KAAK,CAACG,MAAM,EAAE;EAEnB,MAAMC,SAAS,GAAG,CAAC,CAAClC,IAAI,CAACC,IAAI,CAACkC,UAAU;EAExC,IAAI,CAACN,WAAW,EAAE;IAChB,MAAMO,cAAc,GAAGd,WAAC,CAACe,WAAW,CAClC,aAAa,EACbf,WAAC,CAACgB,UAAU,CAAC,aAAa,CAAC,EAC3B,EAAE,EACFhB,WAAC,CAACiB,cAAc,CAAC,EAAE,CACrB,CAAC;IAED,IAAIL,SAAS,EAAE;MACbE,cAAc,CAACI,MAAM,GAAG,CAAClB,WAAC,CAACmB,WAAW,CAACnB,WAAC,CAACgB,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;MAC7DF,cAAc,CAACM,IAAI,CAACA,IAAI,CAACrC,IAAI,CAACsC,cAAQ,CAACC,SAAS,CAACC,GAAG,gBAAgB,CAAC;IACvE;IAEA,CAAChB,WAAW,CAAC,GAAG7B,IAAI,CACjB8C,GAAG,CAAC,MAAM,CAAC,CACXC,gBAAgB,CAAC,MAAM,EAAEX,cAAc,CAA8B;EAC1E;EAEA,IAAIL,OAAO,EAAE;IACXA,OAAO,CAACzB,gBAAgB,EAAE;MAAEI,KAAK,EAAEmB,WAAW,CAACnB;IAAM,CAAC,CAAC;EACzD;EAEA,IAAIwB,SAAS,EAAE;IACb,MAAMc,UAAwC,GAAG,EAAE;IACnDnB,WAAW,CAACoB,QAAQ,CAACrD,cAAc,EAAEoD,UAAU,CAAC;IAChD,IAAIE,OAAO,GAAG,IAAI;IAClB,KAAK,MAAMC,SAAS,IAAIH,UAAU,EAAE;MAClC,IAAIE,OAAO,EAAE;QACXA,OAAO,GAAG,KAAK;MACjB,CAAC,MAAM;QACLpB,KAAK,GAAGA,KAAK,CAACsB,GAAG,CAACC,CAAC,IAAI/B,WAAC,CAACgC,SAAS,CAACD,CAAC,CAAC,CAAC;MACxC;MACA,IAAI,CAACF,SAAS,CAACjD,UAAU,CAACqD,qBAAqB,CAAC,CAAC,EAAE;QACjD,MAAMC,QAAwB,GAAG,CAC/BL,SAAS,CAAClD,IAAI,EACd,GAAG6B,KAAK,CAACsB,GAAG,CAACC,CAAC,IAAI/B,WAAC,CAACmC,YAAY,CAACJ,CAAC,CAAC,CAAC,CACrC;QACD,IAAI,CAACrB,eAAe,EAAEwB,QAAQ,CAACnD,IAAI,CAACiB,WAAC,CAACoC,cAAc,CAAC,CAAC,CAAC;QACvDP,SAAS,CAAC1B,WAAW,CAACH,WAAC,CAACI,kBAAkB,CAAC8B,QAAQ,CAAC,CAAC;MACvD,CAAC,MAAM;QACLL,SAAS,CAACQ,WAAW,CAAC7B,KAAK,CAAC;MAC9B;IACF;EACF,CAAC,MAAM;IACLD,WAAW,CAACiB,GAAG,CAAC,MAAM,CAAC,CAACC,gBAAgB,CAAC,MAAM,EAAEjB,KAAK,CAAC;EACzD;AACF;AAgBO,SAAS8B,kBAAkBA,CAChCC,OAAqB,EACrBnD,KAAY,EACZoD,IAAY,EACiC;EAC7C,MAAMC,cAAc,GAAGzC,WAAC,CAAC0C,YAAY,CAACH,OAAO,CAAC,IAAInD,KAAK,CAACuD,MAAM,CAACJ,OAAO,CAACjD,IAAI,CAAC;EAC5E,IAAImD,cAAc,EAAE;IAClB;EACF;EACA,MAAMG,mBAAmB,GACvB5C,WAAC,CAAC6C,sBAAsB,CAACN,OAAO,EAAE;IAAEO,QAAQ,EAAE;EAAI,CAAC,CAAC,IACpD9C,WAAC,CAAC0C,YAAY,CAACH,OAAO,CAACQ,IAAI,CAAC,IAC5B3D,KAAK,CAACuD,MAAM,CAACJ,OAAO,CAACQ,IAAI,CAACzD,IAAI,CAAC;EACjC,IAAIsD,mBAAmB,EAAE;IACvB,OAAO5C,WAAC,CAACgC,SAAS,CAACO,OAA0C,CAAC;EAChE,CAAC,MAAM;IACL,MAAMS,KAAK,GAAGhD,WAAC,CAACgB,UAAU,CAACwB,IAAI,CAAC;IAGhCpD,KAAK,CAACL,IAAI,CAAC;MACTkE,EAAE,EAAED,KAAK;MACTE,IAAI,EAAE;IACR,CAAC,CAAC;IACF,OAAOlD,WAAC,CAACmD,oBAAoB,CAC3B,GAAG,EACHnD,WAAC,CAACgC,SAAS,CAACgB,KAAK,CAAC,EAClBT,OACF,CAAC;EACH;AACF;AAEO,SAASa,mBAAmBA,CACjC1E,IAAuB,EACvB2E,aAA0D,EAC1DxD,IAAU,EACV;EACA,MAAM;IAAET;EAAM,CAAC,GAAGV,IAAI;EACtB,MAAM4E,YAAqC,GAAG,EAAE;EAChD,MAAM7D,KAAK,GAAG;IACZC,YAAY,EAAEhB,IAAI,CAACC,IAAI,CAACsE,EAAE,IAAI7D,KAAK,CAACO,UAAU,CAACjB,IAAI,CAACC,IAAI,CAACsE,EAAE,CAAC3D,IAAI,CAAC;IACjEO;EACF,CAAC;EACD,KAAK,MAAM0D,YAAY,IAAIF,aAAa,EAAE;IACxC,MAAMG,WAAW,GAAGD,YAAY,CAAC/B,GAAG,CAAC,KAAK,CAAC;IAC3C,IAAIgC,WAAW,CAACC,sBAAsB,CAAC,CAAC,EAAE;MACxCjE,cAAc,CAACgE,WAAW,EAAE/D,KAAK,CAAC;IACpC,CAAC,MAAM;MACL+D,WAAW,CAAC7B,QAAQ,CAACtB,wCAAwC,EAAEZ,KAAK,CAAC;IACvE;IAEA,MAAMiE,YAAY,GAAGH,YAAY,CAAC5E,IAAI;IAGtC,IAAI,CAAC6E,WAAW,CAACG,oBAAoB,CAAC,CAAC,EAAE;MACvC,MAAMC,UAAU,GAAGtB,kBAAkB,CACnCkB,WAAW,CAAC7E,IAAI,EAChBS,KAAK,EACLA,KAAK,CAACyE,sBAAsB,CAACL,WAAW,CAAC7E,IAAI,CAC/C,CAAC;MACD,IAAIiF,UAAU,EAAE;QACdN,YAAY,CAACvE,IAAI,CAACiB,WAAC,CAAC8D,mBAAmB,CAACF,UAAU,CAAC,CAAC;QACpDF,YAAY,CAACK,GAAG,GAAG/D,WAAC,CAACgC,SAAS,CAAC4B,UAAU,CAACb,IAAI,CAAC;MACjD;IACF;EACF;EAEA,OAAOO,YAAY;AACrB", "ignoreList": []}