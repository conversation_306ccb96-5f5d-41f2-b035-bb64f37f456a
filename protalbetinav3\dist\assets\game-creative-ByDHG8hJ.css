/**
 * @file CreativePainting.module.css
 * @description Estilos modulares para Pintura Criativa - Padrão Unificado
 * @version 3.1.0
 */

/* Variáveis CSS para consistência e reutilização */
:root {
  --card-background: rgba(255, 255, 255, 0.1);
  --card-border: 1px solid rgba(255, 255, 255, 0.2);
  --card-blur: blur(10px);
  --success-bg: rgba(76, 175, 80, 0.3);
  --success-border: rgba(76, 175, 80, 0.5);
  --error-bg: rgba(244, 67, 54, 0.3);
  --error-border: rgba(244, 67, 54, 0.5);
  --primary-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Container principal do CreativePainting */
._creativePaintingGame_1fy6o_41 {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* CONTAINER PRINCIPAL DO JOGO */
._gameContainer_1fy6o_63 {
  max-width: 1200px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

/* HEADER PADRÃO */
._gameHeader_1fy6o_85 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  text-align: center;
  position: relative;
}

._gameTitle_1fy6o_101 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

._gameSubtitle_1fy6o_121 {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 20px;
}

._gameStats_1fy6o_133 {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

._statItem_1fy6o_147 {
  background: rgba(255, 255, 255, 0.15);
  padding: 12px 20px;
  border-radius: 15px;
  text-align: center;
  backdrop-filter: blur(10px);
}

._statValue_1fy6o_163 {
  font-size: 1.5rem;
  font-weight: 700;
  display: block;
}

._statLabel_1fy6o_175 {
  font-size: 0.9rem;
  opacity: 0.8;
}

._backButton_1fy6o_185 {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 600;
}

._backButton_1fy6o_185:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

._gameTitle_1fy6o_101 {
  font-size: 2rem;
  font-weight: 800;
  text-align: center;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Área de pintura */
._paintingArea_1fy6o_237 {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
}

._canvasContainer_1fy6o_249 {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
}

._canvas_1fy6o_249 {
  width: 100%;
  height: 400px;
  background: white;
  border-radius: 12px;
  cursor: crosshair;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

/* Paleta de cores */
._colorPalette_1fy6o_287 {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 1.5rem;
  width: 200px;
}

._paletteTitle_1fy6o_305 {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-align: center;
}

._colorGrid_1fy6o_319 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

._colorButton_1fy6o_333 {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

._colorButton_1fy6o_333:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

._colorButton_1fy6o_333._active_1fy6o_363 {
  border-width: 4px;
  border-color: #FECA57;
  transform: scale(1.15);
}

/* Ferramentas */
._toolsSection_1fy6o_377 {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 1rem;
}

._toolsTitle_1fy6o_387 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

._brushSizes_1fy6o_399 {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  justify-content: center;
}

._brushSize_1fy6o_399 {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

._brushSize_1fy6o_399:hover {
  background: rgba(255, 255, 255, 0.3);
}

._brushSize_1fy6o_399._active_1fy6o_363 {
  background: rgba(255, 206, 84, 0.3);
  border-color: #FECA57;
}

._brushDot_1fy6o_457 {
  border-radius: 50%;
  background: white;
}

._toolButtons_1fy6o_467 {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

._toolButton_1fy6o_467 {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
  text-align: center;
}

._toolButton_1fy6o_467:hover {
  background: rgba(255, 255, 255, 0.25);
}

/* Controles do jogo */
._gameControls_1fy6o_513 {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

._controlButton_1fy6o_529 {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 600;
}

._controlButton_1fy6o_529:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

._saveButton_1fy6o_565 {
  background: rgba(76, 175, 80, 0.2);
  border-color: rgba(76, 175, 80, 0.4);
}

._saveButton_1fy6o_565:hover {
  background: rgba(76, 175, 80, 0.3);
}

._clearButton_1fy6o_583 {
  background: rgba(244, 67, 54, 0.2);
  border-color: rgba(244, 67, 54, 0.4);
}

._clearButton_1fy6o_583:hover {
  background: rgba(244, 67, 54, 0.3);
}

/* Galeria de desenhos */
._gallery_1fy6o_603 {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  margin-top: 2rem;
}

._galleryTitle_1fy6o_621 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-align: center;
}

._galleryGrid_1fy6o_635 {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
}

._galleryItem_1fy6o_647 {
  background: white;
  border-radius: 12px;
  padding: 0.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
}

._galleryItem_1fy6o_647:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

._galleryThumbnail_1fy6o_675 {
  width: 100%;
  height: 100px;
  object-fit: cover;
  border-radius: 8px;
}

/* Mensagens de feedback */
._successMessage_1fy6o_691 {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(76, 175, 80, 0.95);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  z-index: 1000;
  animation: _slideIn_1fy6o_1 0.5s ease-in-out;
}

@keyframes _slideIn_1fy6o_1 {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsividade */
@media (max-width: 768px) {
  ._creativePaintingGame_1fy6o_41 {
    padding: 0.5rem;
  }
  
  ._paintingArea_1fy6o_237 {
    flex-direction: column;
    gap: 1rem;
  }
  
  ._colorPalette_1fy6o_287 {
    width: 100%;
  }
  
  ._colorGrid_1fy6o_319 {
    grid-template-columns: repeat(6, 1fr);
  }
  
  ._canvas_1fy6o_249 {
    height: 300px;
  }
  
  ._gameTitle_1fy6o_101 {
    font-size: 1.5rem;
  }
  
  ._gameControls_1fy6o_513 {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  ._gameHeader_1fy6o_85 {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  ._colorGrid_1fy6o_319 {
    grid-template-columns: repeat(4, 1fr);
  }
  
  ._canvas_1fy6o_249 {    height: 250px;
  }
  
  ._galleryGrid_1fy6o_635 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Estilos para o botão de voltar e badge de dificuldade */
._backButton_1fy6o_185 {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

._backButton_1fy6o_185:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-50%) translateX(-2px);
}

._difficultyBadge_1fy6o_891 {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-left: 0.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Ajuste para o gameTitle para acomodar o botão de voltar */
._gameTitle_1fy6o_101 {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  flex: 1;
}

._gameHeader_1fy6o_85 {
  position: relative;
  display: flex;
  align-items: center;
  padding: 2rem;
}

/* =====================================================
   🎯 ESTILOS PARA ATIVIDADES TERAPÊUTICAS V3
   ===================================================== */

/* Seletor de atividades terapêuticas */
._activitySelector_1fy6o_959 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  backdrop-filter: var(--card-blur);
}

._activityTitle_1fy6o_977 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.3rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

._activityGrid_1fy6o_999 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

._activityBtn_1fy6o_1011 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: var(--card-blur);
}

._activityBtn_1fy6o_1011:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

._activityBtn_1fy6o_1011._active_1fy6o_363 {
  background: var(--success-bg);
  border: 2px solid var(--success-border);
  transform: scale(1.02);
}

._activityIcon_1fy6o_1057 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

._activityName_1fy6o_1067 {
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.3rem;
}

._activityFocus_1fy6o_1081 {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
}

/* Área de atividade específica */
._activityArea_1fy6o_1095 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: var(--card-blur);
}

._therapeuticActivity_1fy6o_1111 {
  width: 100%;
}

._activityHeader_1fy6o_1119 {
  text-align: center;
  margin-bottom: 2rem;
}

._activityHeader_1fy6o_1119 h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

._activityHeader_1fy6o_1119 p {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

/* Canvas terapêutico */
._therapeuticCanvas_1fy6o_1159 {
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: white;
  cursor: crosshair;
  display: block;
  margin: 0 auto;
}

/* Análise de preferência cromática */
._colorAnalysisArea_1fy6o_1179 {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

._expandedColorPalette_1fy6o_1191 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
}

._analysisColorBtn_1fy6o_1209 {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

._analysisColorBtn_1fy6o_1209:hover {
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.6);
}

._colorUsageCount_1fy6o_1245 {
  position: absolute;
  top: -8px;
  right: -8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
}

._emotionalCanvas_1fy6o_1277 {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

/* Métricas em tempo real */
._realTimeMetrics_1fy6o_1291,
._motorMetrics_1fy6o_1293,
._spatialMetrics_1fy6o_1295,
._creativityMetrics_1fy6o_1297,
._attentionMetrics_1fy6o_1299 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

._metricCard_1fy6o_1313 {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  backdrop-filter: blur(5px);
}

._metricCard_1fy6o_1313 span:first-child {
  display: block;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.5rem;
}

._metricCard_1fy6o_1313 span:last-child {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #4CAF50;
}

/* Rastreamento de precisão motora */
._motorTrackingArea_1fy6o_1361 {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

._guidedCanvas_1fy6o_1373 {
  position: relative;
  display: flex;
  justify-content: center;
}

._guidedPaths_1fy6o_1385 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

._pathOverlay_1fy6o_1403 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Mapeamento de organização espacial */
._spatialMappingArea_1fy6o_1421 {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

._spatialCanvas_1fy6o_1433 {
  position: relative;
  display: flex;
  justify-content: center;
}

._spatialGrid_1fy6o_1445 {
  position: absolute;
  top: 0;
  left: 0;
  width: 600px;
  height: 400px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(4, 1fr);
  pointer-events: none;
  opacity: 0.3;
}

._gridCell_1fy6o_1471 {
  border: 1px dashed rgba(255, 255, 255, 0.3);
}

/* Perfil de expressão criativa */
._creativeProfilingArea_1fy6o_1481 {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

._creativeCanvas_1fy6o_1493 {
  display: flex;
  justify-content: center;
}

/* Medição de foco atencional */
._attentionMeasurementArea_1fy6o_1505 {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

._focusTask_1fy6o_1517 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

._taskInstruction_1fy6o_1531 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #FFD700;
  text-align: center;
  padding: 1rem;
  background: rgba(255, 215, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.4);
  border-radius: 8px;
}

/* =====================================================
   🎨 ESTILOS PARA ATIVIDADES FUNCIONAIS V3
   ===================================================== */

/* Atividade de pintura geral */
._paintingActivity_1fy6o_1563 {
  width: 100%;
}

/* Pintura Livre */
._freePaintingArea_1fy6o_1573 {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

._colorPalette_1fy6o_287 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: var(--card-blur);
}

._colorPalette_1fy6o_287 h4 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

._colorGrid_1fy6o_319 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
  gap: 0.5rem;
}

._colorBtn_1fy6o_1625 {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

._colorBtn_1fy6o_1625:hover {
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.6);
}

._colorBtn_1fy6o_1625._active_1fy6o_363 {
  border-color: #FFD700;
  border-width: 4px;
  transform: scale(1.1);
}

._brushControls_1fy6o_1665 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: var(--card-blur);
}

._brushControls_1fy6o_1665 h4 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

._brushSizeControl_1fy6o_1693 {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

._brushSizeControl_1fy6o_1693 label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

._brushSlider_1fy6o_1715 {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.2);
  outline: none;
  cursor: pointer;
}

._canvasContainer_1fy6o_249 {
  display: flex;
  justify-content: center;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: var(--card-blur);
}

._paintingCanvas_1fy6o_1753 {
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: white;
  cursor: crosshair;
}

._advancedCanvas_1fy6o_1767 {
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: white;
  cursor: crosshair;
}

._actionControls_1fy6o_1781 {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

._clearBtn_1fy6o_1793, ._saveBtn_1fy6o_1793, ._undoBtn_1fy6o_1793, ._redoBtn_1fy6o_1793 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  padding: 0.8rem 1.5rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: var(--card-blur);
}

._clearBtn_1fy6o_1793:hover {
  background: var(--error-bg);
  border-color: var(--error-border);
}

._saveBtn_1fy6o_1793:hover {
  background: var(--success-bg);
  border-color: var(--success-border);
}

._undoBtn_1fy6o_1793:hover, ._redoBtn_1fy6o_1793:hover {
  background: rgba(33, 150, 243, 0.3);
  border-color: rgba(33, 150, 243, 0.5);
}

._undoBtn_1fy6o_1793:disabled, ._redoBtn_1fy6o_1793:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Pintura Assistida */
._assistedPaintingArea_1fy6o_1859 {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

._templateSelector_1fy6o_1871 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: var(--card-blur);
}

._templateSelector_1fy6o_1871 h4 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

._templateGrid_1fy6o_1899 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

._templateBtn_1fy6o_1911 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: var(--card-blur);
}

._templateBtn_1fy6o_1911:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.2);
}

._templateBtn_1fy6o_1911._active_1fy6o_363 {
  background: var(--success-bg);
  border-color: var(--success-border);
}

._templateIcon_1fy6o_1953 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

._templateName_1fy6o_1963 {
  color: white;
  font-size: 0.9rem;
  font-weight: 600;
}

._assistedCanvas_1fy6o_1975 {
  position: relative;
  display: inline-block;
}

._clickableAreas_1fy6o_1985 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

._clickableArea_1fy6o_1985 {
  pointer-events: all;
  transition: all 0.3s ease;
}

._clickableArea_1fy6o_1985:hover {
  background: rgba(255, 255, 255, 0.1);
}

._progressSection_1fy6o_2021 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: var(--card-blur);
}

._progressSection_1fy6o_2021 h4 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

._progressBar_1fy6o_2049 {
  width: 100%;
  height: 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
}

._progressFill_1fy6o_2065 {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  transition: width 0.3s ease;
}

/* Canvas de Pintura */
._canvasPaintingArea_1fy6o_2079 {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

._toolBar_1fy6o_2091 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: var(--card-blur);
}

._toolBar_1fy6o_2091 h4 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

._toolGrid_1fy6o_2119 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
  gap: 0.5rem;
}

._toolBtn_1fy6o_2131 {
  width: 60px;
  height: 60px;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: var(--card-blur);
}

._toolBtn_1fy6o_2131:hover {
  transform: scale(1.05);
  background: rgba(255, 255, 255, 0.2);
}

._toolBtn_1fy6o_2131._active_1fy6o_363 {
  background: var(--success-bg);
  border-color: var(--success-border);
}

._advancedControls_1fy6o_2181 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

._controlGroup_1fy6o_2193 {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

._controlGroup_1fy6o_2193 label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

._brushTypeSelect_1fy6o_2215 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 6px;
  padding: 0.5rem;
  color: white;
  backdrop-filter: var(--card-blur);
}

/* Responsividade para atividades funcionais */
@media (max-width: 768px) {
  ._activityGrid_1fy6o_999 {
    grid-template-columns: 1fr;
  }

  ._colorGrid_1fy6o_319 {
    grid-template-columns: repeat(5, 1fr);
  }

  ._colorBtn_1fy6o_1625 {
    width: 40px;
    height: 40px;
  }

  ._paintingCanvas_1fy6o_1753, ._advancedCanvas_1fy6o_1767 {
    width: 100%;
    max-width: 400px;
    height: 300px;
  }

  ._templateGrid_1fy6o_1899 {
    grid-template-columns: repeat(2, 1fr);
  }

  ._toolGrid_1fy6o_2119 {
    grid-template-columns: repeat(3, 1fr);
  }

  ._advancedControls_1fy6o_2181 {
    grid-template-columns: 1fr;
  }

  ._actionControls_1fy6o_1781 {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}

/* =====================================================
   🎯 ESTILOS PADRÃO LETTERRECOGNITION - OBRIGATÓRIOS
   ===================================================== */

._creativePaintingGame_1fy6o_41 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
}

._gameContent_1fy6o_2331 {
  max-width: 1200px;
  margin: 0 auto;
}

/* Header do jogo - PADRÃO LETTERRECOGNITION */
._gameHeader_1fy6o_85 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 3rem 1rem 1rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  position: relative;
}

._gameTitle_1fy6o_101 {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

._headerTtsButton_1fy6o_2385 {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

._headerTtsButton_1fy6o_2385:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

._headerTtsButton_1fy6o_2385._ttsActive_1fy6o_2421 {
  background: var(--success-bg);
  border-color: var(--success-border);
}

/* Estatísticas do jogo - PADRÃO LETTERRECOGNITION */
._gameStats_1fy6o_133 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 0 0.5rem;
}

._statCard_1fy6o_2449 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
}

._statCard_1fy6o_2449:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.25);
}

._statValue_1fy6o_163 {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  margin-bottom: 0.25rem;
}

._statLabel_1fy6o_175 {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.9);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

/* Menu de atividades - PADRÃO LETTERRECOGNITION */
._activityMenu_1fy6o_2513 {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 2rem;
  padding: 0 1rem;
  flex-wrap: wrap;
}

._activityButton_1fy6o_2531 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  min-width: 80px;
  backdrop-filter: var(--card-blur);
  color: white;
}

._activityButton_1fy6o_2531:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

._activityButton_1fy6o_2531._active_1fy6o_363 {
  background: var(--success-bg);
  border-color: var(--success-border);
  color: white;
}

/* Controles do jogo - CENTRALIZADOS E SEPARADOS */
._gameControls_1fy6o_513 {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  margin-top: 2rem;
  padding: 0 1rem;
  flex-wrap: wrap;
}

._controlsGroup_1fy6o_2607 {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
}

._controlButton_1fy6o_529 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: var(--card-blur);
}

._controlButton_1fy6o_529:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}
