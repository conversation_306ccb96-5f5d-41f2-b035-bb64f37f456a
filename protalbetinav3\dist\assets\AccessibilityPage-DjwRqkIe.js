import { r as reactExports, j as jsxDevRuntimeExports } from "./vendor-react-BH-kks1U.js";
import { b as useAccessibilityContext } from "./context-CJb-Kg-5.js";
import "./vendor-misc-BhjEiCpb.js";
import "./services-Ckq1alRq.js";
import "./dashboard-D6oq-mHv.js";
import "./vendor-charts-JJkNskvH.js";
import "./admin-AVDlea_I.js";
import "./utils-C8SspVp8.js";
import "./vendor-utils-CjlX8hrF.js";
import "./game-colors-DknHlpST.js";
import "./game-association-1fe4bzjE.js";
import "./hooks-DiB_syzW.js";
import "./game-letters-C11f_WoE.js";
import "./game-memory-Q6N7kS_-.js";
import "./vendor-motion-CThs1zaH.js";
import "./game-musical-4K52sZ4i.js";
import "./game-patterns-C1u1YIjS.js";
import "./game-puzzle-y0iWrjae.js";
import "./game-numbers-CLPWZorL.js";
import "./game-creative-danUmc-P.js";
const container = "_container_xqmmk_15";
const pageHeader = "_pageHeader_xqmmk_33";
const backButton = "_backButton_xqmmk_53";
const pageTitle = "_pageTitle_xqmmk_89";
const pageSubtitle = "_pageSubtitle_xqmmk_103";
const pageContent = "_pageContent_xqmmk_117";
const accessibilityPanel = "_accessibilityPanel_xqmmk_127";
const panelContent = "_panelContent_xqmmk_139";
const panelInfo = "_panelInfo_xqmmk_147";
const accessibilityGroup = "_accessibilityGroup_xqmmk_171";
const groupTitle = "_groupTitle_xqmmk_191";
const presetsGrid = "_presetsGrid_xqmmk_207";
const presetButton = "_presetButton_xqmmk_219";
const optionRow = "_optionRow_xqmmk_257";
const optionLabel = "_optionLabel_xqmmk_267";
const optionText = "_optionText_xqmmk_281";
const switchContainer = "_switchContainer_xqmmk_295";
const switchInput = "_switchInput_xqmmk_307";
const switchSlider = "_switchSlider_xqmmk_319";
const selectInput = "_selectInput_xqmmk_385";
const successMessage = "_successMessage_xqmmk_419";
const styles = {
  container,
  pageHeader,
  backButton,
  pageTitle,
  pageSubtitle,
  pageContent,
  accessibilityPanel,
  panelContent,
  panelInfo,
  accessibilityGroup,
  groupTitle,
  presetsGrid,
  presetButton,
  optionRow,
  optionLabel,
  optionText,
  switchContainer,
  switchInput,
  switchSlider,
  selectInput,
  successMessage
};
function AccessibilityPage({ onBack }) {
  const { settings, updateSettings, applyPreset } = useAccessibilityContext();
  const [savedSuccessfully, setSavedSuccessfully] = reactExports.useState(false);
  reactExports.useEffect(() => {
    const applyAccessibilitySettings = () => {
      const root = document.documentElement;
      if (settings.highContrast) {
        root.classList.add("high-contrast");
      } else {
        root.classList.remove("high-contrast");
      }
      root.classList.remove("font-small", "font-medium", "font-large", "font-extra-large");
      root.classList.add(`font-${settings.fontSize}`);
      if (settings.dyslexiaFriendly) {
        root.classList.add("dyslexia-friendly");
      } else {
        root.classList.remove("dyslexia-friendly");
      }
      if (settings.reducedMotion) {
        root.classList.add("reduced-motion");
      } else {
        root.classList.remove("reduced-motion");
      }
      root.classList.remove("scheme-default", "scheme-dark", "scheme-soft", "scheme-high-contrast");
      root.classList.add(`scheme-${settings.colorScheme}`);
      const customStyles = `
        .high-contrast * {
          filter: contrast(1.5) !important;
          border: 1px solid #000 !important;
        }
        
        .font-small { font-size: 12px !important; }
        .font-medium { font-size: 16px !important; }
        .font-large { font-size: 20px !important; }
        .font-extra-large { font-size: 24px !important; }
        
        .dyslexia-friendly * {
          font-family: 'OpenDyslexic', 'Comic Sans MS', cursive !important;
        }
        
        .reduced-motion * {
          animation: none !important;
          transition: none !important;
        }
        
        .scheme-dark {
          background: #1a1a1a !important;
          color: #ffffff !important;
        }
        
        .scheme-soft {
          filter: brightness(0.9) saturate(0.8) !important;
        }
        
        .scheme-high-contrast {
          filter: contrast(2) brightness(1.2) !important;
        }
      `;
      const existingStyle = document.getElementById("accessibility-styles");
      if (existingStyle) {
        existingStyle.remove();
      }
      const styleElement = document.createElement("style");
      styleElement.id = "accessibility-styles";
      styleElement.textContent = customStyles;
      document.head.appendChild(styleElement);
    };
    applyAccessibilitySettings();
  }, [settings]);
  const [textToSpeechEnabled, setTextToSpeechEnabled] = reactExports.useState(settings.textToSpeech);
  const showSuccessMessage = () => {
    setSavedSuccessfully(true);
    setTimeout(() => setSavedSuccessfully(false), 2e3);
  };
  const speakText = (text) => {
    if (settings.textToSpeech && "speechSynthesis" in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.8;
      utterance.pitch = 1;
      speechSynthesis.speak(utterance);
    }
  };
  const handleSettingChange = reactExports.useCallback((key, value) => {
    console.log(`🔧 Alterando configuração: ${key} = ${value}`);
    const newSettings = { [key]: value };
    updateSettings(newSettings);
    showSuccessMessage();
    speakText(`${key} ${value ? "ativado" : "desativado"}`);
  }, [updateSettings, settings.textToSpeech]);
  const handleApplyPreset = reactExports.useCallback((preset) => {
    console.log(`🎨 Aplicando preset: ${preset}`);
    applyPreset(preset);
    showSuccessMessage();
    speakText(`Preset ${preset} aplicado`);
  }, [applyPreset, settings.textToSpeech]);
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.container, children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.pageHeader, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("button", { className: styles.backButton, onClick: onBack, children: "← Voltar" }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
        lineNumber: 141,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h1", { className: styles.pageTitle, children: "♿ Configurações de Acessibilidade" }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
        lineNumber: 144,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: styles.pageSubtitle, children: "Personalize sua experiência para ter o melhor acesso ao Portal Betina" }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
        lineNumber: 145,
        columnNumber: 9
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
      lineNumber: 140,
      columnNumber: 7
    }, this),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.pageContent, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.accessibilityPanel, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.panelContent, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: styles.panelInfo, children: "Configure suas preferências de acessibilidade para uma melhor experiência no Portal Betina." }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
        lineNumber: 153,
        columnNumber: 13
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.accessibilityGroup, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: styles.groupTitle, children: "⚡ Configurações Rápidas" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 159,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.presetsGrid, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              className: styles.presetButton,
              onClick: () => handleApplyPreset("default"),
              children: "Padrão"
            },
            void 0,
            false,
            {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
              lineNumber: 161,
              columnNumber: 17
            },
            this
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              className: styles.presetButton,
              onClick: () => handleApplyPreset("high-contrast"),
              children: "Alto Contraste"
            },
            void 0,
            false,
            {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
              lineNumber: 167,
              columnNumber: 17
            },
            this
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              className: styles.presetButton,
              onClick: () => handleApplyPreset("autism-friendly"),
              children: "Autismo"
            },
            void 0,
            false,
            {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
              lineNumber: 173,
              columnNumber: 17
            },
            this
          ),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              className: styles.presetButton,
              onClick: () => handleApplyPreset("dyslexia"),
              children: "Dislexia"
            },
            void 0,
            false,
            {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
              lineNumber: 179,
              columnNumber: 17
            },
            this
          )
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 160,
          columnNumber: 15
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
        lineNumber: 158,
        columnNumber: 13
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.accessibilityGroup, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: styles.groupTitle, children: "👁️ Visual" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 190,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.optionRow, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: styles.optionLabel, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.optionText, children: "Alto Contraste" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
            lineNumber: 194,
            columnNumber: 19
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.switchContainer, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "input",
              {
                type: "checkbox",
                className: styles.switchInput,
                checked: settings.highContrast,
                onChange: (e) => handleSettingChange("highContrast", e.target.checked)
              },
              void 0,
              false,
              {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
                lineNumber: 196,
                columnNumber: 21
              },
              this
            ),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.switchSlider }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
              lineNumber: 202,
              columnNumber: 21
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
            lineNumber: 195,
            columnNumber: 19
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 193,
          columnNumber: 17
        }, this) }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 192,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.optionRow, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: styles.optionLabel, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.optionText, children: "Tamanho da Fonte" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
            lineNumber: 209,
            columnNumber: 19
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "select",
            {
              className: styles.selectInput,
              value: settings.fontSize,
              onChange: (e) => handleSettingChange("fontSize", e.target.value),
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "small", children: "Pequena" }, void 0, false, {
                  fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
                  lineNumber: 215,
                  columnNumber: 21
                }, this),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "medium", children: "Média" }, void 0, false, {
                  fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
                  lineNumber: 216,
                  columnNumber: 21
                }, this),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "large", children: "Grande" }, void 0, false, {
                  fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
                  lineNumber: 217,
                  columnNumber: 21
                }, this),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "extra-large", children: "Extra Grande" }, void 0, false, {
                  fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
                  lineNumber: 218,
                  columnNumber: 21
                }, this)
              ]
            },
            void 0,
            true,
            {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
              lineNumber: 210,
              columnNumber: 19
            },
            this
          )
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 208,
          columnNumber: 17
        }, this) }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 207,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.optionRow, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: styles.optionLabel, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.optionText, children: "Fonte para Dislexia" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
            lineNumber: 225,
            columnNumber: 19
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.switchContainer, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "input",
              {
                type: "checkbox",
                className: styles.switchInput,
                checked: settings.dyslexiaFriendly,
                onChange: (e) => handleSettingChange("dyslexiaFriendly", e.target.checked)
              },
              void 0,
              false,
              {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
                lineNumber: 227,
                columnNumber: 21
              },
              this
            ),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.switchSlider }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
              lineNumber: 233,
              columnNumber: 21
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
            lineNumber: 226,
            columnNumber: 19
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 224,
          columnNumber: 17
        }, this) }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 223,
          columnNumber: 15
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
        lineNumber: 189,
        columnNumber: 13
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.accessibilityGroup, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: styles.groupTitle, children: "🎭 Movimento" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 241,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.optionRow, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: styles.optionLabel, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.optionText, children: "Reduzir Animações" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
            lineNumber: 245,
            columnNumber: 19
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.switchContainer, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "input",
              {
                type: "checkbox",
                className: styles.switchInput,
                checked: settings.reducedMotion,
                onChange: (e) => handleSettingChange("reducedMotion", e.target.checked)
              },
              void 0,
              false,
              {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
                lineNumber: 247,
                columnNumber: 21
              },
              this
            ),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.switchSlider }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
              lineNumber: 253,
              columnNumber: 21
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
            lineNumber: 246,
            columnNumber: 19
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 244,
          columnNumber: 17
        }, this) }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 243,
          columnNumber: 15
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
        lineNumber: 240,
        columnNumber: 13
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.accessibilityGroup, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: styles.groupTitle, children: "🔊 Áudio" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 261,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.optionRow, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: styles.optionLabel, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.optionText, children: "Leitura de Texto" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
            lineNumber: 265,
            columnNumber: 19
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.switchContainer, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "input",
              {
                type: "checkbox",
                className: styles.switchInput,
                checked: settings.textToSpeech,
                onChange: (e) => handleSettingChange("textToSpeech", e.target.checked)
              },
              void 0,
              false,
              {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
                lineNumber: 267,
                columnNumber: 21
              },
              this
            ),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.switchSlider }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
              lineNumber: 273,
              columnNumber: 21
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
            lineNumber: 266,
            columnNumber: 19
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 264,
          columnNumber: 17
        }, this) }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 263,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.optionRow, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: styles.optionLabel, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.optionText, children: "Sons Ativados" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
            lineNumber: 280,
            columnNumber: 19
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.switchContainer, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "input",
              {
                type: "checkbox",
                className: styles.switchInput,
                checked: settings.soundEnabled,
                onChange: (e) => handleSettingChange("soundEnabled", e.target.checked)
              },
              void 0,
              false,
              {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
                lineNumber: 282,
                columnNumber: 21
              },
              this
            ),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.switchSlider }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
              lineNumber: 288,
              columnNumber: 21
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
            lineNumber: 281,
            columnNumber: 19
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 279,
          columnNumber: 17
        }, this) }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 278,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.optionRow, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: styles.optionLabel, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.optionText, children: "Leitura Automática" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
            lineNumber: 295,
            columnNumber: 19
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.switchContainer, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "input",
              {
                type: "checkbox",
                className: styles.switchInput,
                checked: settings.autoRead,
                onChange: (e) => handleSettingChange("autoRead", e.target.checked)
              },
              void 0,
              false,
              {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
                lineNumber: 297,
                columnNumber: 21
              },
              this
            ),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.switchSlider }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
              lineNumber: 303,
              columnNumber: 21
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
            lineNumber: 296,
            columnNumber: 19
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 294,
          columnNumber: 17
        }, this) }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 293,
          columnNumber: 15
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
        lineNumber: 260,
        columnNumber: 13
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.accessibilityGroup, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: styles.groupTitle, children: "🎨 Tema" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 311,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.optionRow, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { className: styles.optionLabel, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.optionText, children: "Esquema de Cores" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
            lineNumber: 315,
            columnNumber: 19
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "select",
            {
              className: styles.selectInput,
              value: settings.colorScheme,
              onChange: (e) => handleSettingChange("colorScheme", e.target.value),
              children: [
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "default", children: "Padrão" }, void 0, false, {
                  fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
                  lineNumber: 321,
                  columnNumber: 21
                }, this),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "dark", children: "Escuro" }, void 0, false, {
                  fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
                  lineNumber: 322,
                  columnNumber: 21
                }, this),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "soft", children: "Suave" }, void 0, false, {
                  fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
                  lineNumber: 323,
                  columnNumber: 21
                }, this),
                /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("option", { value: "high-contrast", children: "Alto Contraste" }, void 0, false, {
                  fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
                  lineNumber: 324,
                  columnNumber: 21
                }, this)
              ]
            },
            void 0,
            true,
            {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
              lineNumber: 316,
              columnNumber: 19
            },
            this
          )
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 314,
          columnNumber: 17
        }, this) }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
          lineNumber: 313,
          columnNumber: 15
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
        lineNumber: 310,
        columnNumber: 13
      }, this),
      savedSuccessfully && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.successMessage, children: "✅ Configurações salvas com sucesso!" }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
        lineNumber: 331,
        columnNumber: 15
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
      lineNumber: 152,
      columnNumber: 11
    }, this) }, void 0, false, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
      lineNumber: 151,
      columnNumber: 9
    }, this) }, void 0, false, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
      lineNumber: 150,
      columnNumber: 7
    }, this)
  ] }, void 0, true, {
    fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",
    lineNumber: 139,
    columnNumber: 5
  }, this);
}
export {
  AccessibilityPage as default
};
//# sourceMappingURL=AccessibilityPage-DjwRqkIe.js.map
