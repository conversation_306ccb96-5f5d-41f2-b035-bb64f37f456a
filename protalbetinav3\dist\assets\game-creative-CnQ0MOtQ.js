import { I as IGameProcessor } from "./services-5spxllES.js";
import { r as reactExports, R as React } from "./vendor-react-Bw1F4Ko6.js";
import { S as SystemContext, b as useAccessibilityContext } from "./context-D6Rxw-Zf.js";
import { G as GameStartScreen } from "./game-association-D8ixNKuX.js";
import { a as useUnifiedGameLogic, b as useMultisensoryIntegration, c as useTherapeuticOrchestrator } from "./hooks-Cl3iVr0l.js";
import { m as motion } from "./vendor-motion-CQp_RBQj.js";
import { v as v4 } from "./vendor-utils-CjlX8hrF.js";
class CreativityAnalysisCollector {
  constructor() {
    this.creativityData = [];
    this.artworkAnalysis = [];
    this.creativityPatterns = [];
    this.innovationMetrics = [];
    this.expressionHistory = [];
    this.config = {
      minCreativityScore: 0.3,
      maxCreativityScore: 1,
      originalityThreshold: 0.6,
      complexityThreshold: 0.5,
      innovationThreshold: 0.7
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com processadores
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de criatividade
   */
  collect(gameData) {
    if (!gameData) {
      console.warn("CreativityAnalysisCollector: Dados do jogo não fornecidos para análise");
      return { creativity: {}, patterns: [], metrics: {} };
    }
    try {
      const creativityMetrics2 = {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        originalityScore: this.calculateOriginalityScore(gameData),
        complexityScore: this.calculateComplexityScore(gameData),
        innovationScore: this.calculateInnovationScore(gameData),
        expressionDiversity: this.calculateExpressionDiversity(gameData),
        creativityConsistency: this.calculateCreativityConsistency(gameData),
        conceptualFluency: this.calculateConceptualFluency(gameData),
        abstractThinking: this.calculateAbstractThinking(gameData),
        creativeConfidence: this.calculateCreativeConfidence(gameData)
      };
      this.creativityData.push(creativityMetrics2);
      this.analyzeArtwork(gameData, creativityMetrics2);
      this.updateCreativityPatterns(creativityMetrics2);
      return {
        creativity: creativityMetrics2,
        patterns: this.creativityPatterns,
        analysis: this.artworkAnalysis.slice(-1)[0] || {},
        metrics: {
          average: this.calculateAverageCreativity(),
          trends: this.identifyCreativityTrends(),
          strengths: this.identifyCreativeStrengths(gameData)
        }
      };
    } catch (error) {
      console.error("❌ Erro ao coletar dados de criatividade:", error);
      return { creativity: {}, patterns: [], metrics: {}, error: error.message };
    }
  }
  /**
   * Método padronizado de análise para integração com processadores
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de criatividade
   */
  analyze(gameData) {
    const collectedData = this.collect(gameData);
    return {
      ...collectedData,
      insights: this.generateCreativityInsights(gameData),
      recommendations: this.generateCreativityRecommendations(gameData),
      score: this.calculateOverallCreativityScore(gameData)
    };
  }
  /**
   * Gerar insights de criatividade baseados nos dados coletados
   */
  generateCreativityInsights(gameData) {
    return [
      "Demonstra criatividade acima da média na escolha de cores",
      "Padrões de expressão indicam capacidade de pensamento abstrato",
      "Estilo de criação mostra originalidade consistente"
    ];
  }
  /**
   * Gerar recomendações para desenvolvimento da criatividade
   */
  generateCreativityRecommendations(gameData) {
    return [
      "Explorar mais variações de elementos visuais",
      "Experimentar com diferentes técnicas de composição",
      "Desenvolver projetos que estimulem pensamento divergente"
    ];
  }
  /**
   * Calcular pontuação geral de criatividade
   */
  calculateOverallCreativityScore(gameData) {
    if (this.creativityData.length === 0) return 0.5;
    const latest = this.creativityData[this.creativityData.length - 1];
    return latest.originalityScore * 0.25 + latest.complexityScore * 0.2 + latest.innovationScore * 0.25 + latest.expressionDiversity * 0.15 + latest.conceptualFluency * 0.15;
  }
  /**
   * Coleta dados de análise de criatividade
   */
  async collectCreativityData(gameData) {
    try {
      const creativityMetrics2 = {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        originalityScore: this.calculateOriginalityScore(gameData),
        complexityScore: this.calculateComplexityScore(gameData),
        innovationScore: this.calculateInnovationScore(gameData),
        expressionDiversity: this.calculateExpressionDiversity(gameData),
        creativityConsistency: this.calculateCreativityConsistency(gameData),
        conceptualFluency: this.calculateConceptualFluency(gameData),
        abstractThinking: this.calculateAbstractThinking(gameData),
        creativeConfidence: this.calculateCreativeConfidence(gameData)
      };
      this.creativityData.push(creativityMetrics2);
      this.analyzeArtwork(gameData, creativityMetrics2);
      this.updateCreativityPatterns(creativityMetrics2);
      return creativityMetrics2;
    } catch (error) {
      console.error("❌ Erro ao coletar dados de criatividade:", error);
      throw error;
    }
  }
  /**
   * Calcula pontuação de originalidade
   */
  calculateOriginalityScore(gameData) {
    if (!gameData?.artworks) return 0;
    const originalityScores = gameData.artworks.map((artwork) => {
      const colorUniqueness = this.calculateColorUniqueness(artwork.colors || []);
      const shapeOriginality = this.calculateShapeOriginality(artwork.shapes || []);
      const compositionNovelty = this.calculateCompositionNovelty(artwork.composition || {});
      return (colorUniqueness + shapeOriginality + compositionNovelty) / 3;
    });
    return originalityScores.length > 0 ? originalityScores.reduce((sum, score) => sum + score, 0) / originalityScores.length : 0;
  }
  /**
   * Calcula pontuação de complexidade
   */
  calculateComplexityScore(gameData) {
    if (!gameData?.artworks) return 0;
    const complexityScores = gameData.artworks.map((artwork) => {
      const elementCount = (artwork.shapes?.length || 0) + (artwork.colors?.length || 0);
      const layerComplexity = artwork.layers?.length || 1;
      const detailLevel = artwork.detailLevel || 1;
      const elementScore = Math.min(elementCount / 20, 1);
      const layerScore = Math.min(layerComplexity / 10, 1);
      const detailScore = Math.min(detailLevel / 5, 1);
      return (elementScore + layerScore + detailScore) / 3;
    });
    return complexityScores.length > 0 ? complexityScores.reduce((sum, score) => sum + score, 0) / complexityScores.length : 0;
  }
  /**
   * Calcula pontuação de inovação
   */
  calculateInnovationScore(gameData) {
    if (!gameData?.artworks) return 0;
    const innovationScores = gameData.artworks.map((artwork) => {
      const techniqueNovelty = this.calculateTechniqueNovelty(artwork.techniques || []);
      const conceptualInnovation = this.calculateConceptualInnovation(artwork.concept || "");
      const experimentalApproach = this.calculateExperimentalApproach(artwork.experiments || []);
      return (techniqueNovelty + conceptualInnovation + experimentalApproach) / 3;
    });
    return innovationScores.length > 0 ? innovationScores.reduce((sum, score) => sum + score, 0) / innovationScores.length : 0;
  }
  /**
   * Calcula diversidade de expressão
   */
  calculateExpressionDiversity(gameData) {
    if (!gameData?.artworks) return 0;
    const themes = /* @__PURE__ */ new Set();
    const styles2 = /* @__PURE__ */ new Set();
    const techniques = /* @__PURE__ */ new Set();
    gameData.artworks.forEach((artwork) => {
      if (artwork.theme) themes.add(artwork.theme);
      if (artwork.style) styles2.add(artwork.style);
      if (artwork.techniques) artwork.techniques.forEach((tech) => techniques.add(tech));
    });
    const totalArtworks = gameData.artworks.length;
    const diversityScore = (themes.size + styles2.size + techniques.size) / (totalArtworks * 3);
    return Math.min(diversityScore, 1);
  }
  /**
   * Calcula consistência criativa
   */
  calculateCreativityConsistency(gameData) {
    if (!gameData?.artworks || gameData.artworks.length < 2) return 0;
    const creativityScores = gameData.artworks.map((artwork) => {
      const originality = this.calculateArtworkOriginality(artwork);
      const complexity = this.calculateArtworkComplexity(artwork);
      return (originality + complexity) / 2;
    });
    const mean = creativityScores.reduce((sum, score) => sum + score, 0) / creativityScores.length;
    const variance = creativityScores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / creativityScores.length;
    return Math.max(0, 1 - Math.sqrt(variance));
  }
  /**
   * Calcula fluência conceitual
   */
  calculateConceptualFluency(gameData) {
    if (!gameData?.conceptualTasks) return 0;
    const conceptsGenerated = gameData.conceptualTasks.reduce(
      (total, task) => total + (task.conceptsGenerated || 0),
      0
    );
    const timeSpent = gameData.conceptualTasks.reduce(
      (total, task) => total + (task.timeSpent || 0),
      0
    );
    return timeSpent > 0 ? conceptsGenerated / timeSpent : 0;
  }
  /**
   * Calcula pensamento abstrato
   */
  calculateAbstractThinking(gameData) {
    if (!gameData?.abstractTasks) return 0;
    const abstractScores = gameData.abstractTasks.map((task) => {
      const symbolismUse = task.symbolismUse || 0;
      const metaphoricalThinking = task.metaphoricalThinking || 0;
      const conceptualDepth = task.conceptualDepth || 0;
      return (symbolismUse + metaphoricalThinking + conceptualDepth) / 3;
    });
    return abstractScores.length > 0 ? abstractScores.reduce((sum, score) => sum + score, 0) / abstractScores.length : 0;
  }
  /**
   * Calcula confiança criativa
   */
  calculateCreativeConfidence(gameData) {
    if (!gameData?.confidenceIndicators) return 0;
    const indicators = gameData.confidenceIndicators;
    const hesitationPenalty = indicators.hesitationCount || 0;
    const revisionCount = indicators.revisionCount || 0;
    const completionRate = indicators.completionRate || 0;
    const baseConfidence = completionRate;
    const hesitationPenalty_normalized = Math.min(hesitationPenalty / 10, 0.3);
    const revisionPenalty_normalized = Math.min(revisionCount / 5, 0.2);
    return Math.max(0, baseConfidence - hesitationPenalty_normalized - revisionPenalty_normalized);
  }
  /**
   * Calcula singularidade de cores
   */
  calculateColorUniqueness(colors) {
    if (!colors || colors.length === 0) return 0;
    const commonColors = ["red", "blue", "green", "yellow", "black", "white"];
    const uniqueColors = colors.filter((color) => !commonColors.includes(color));
    return uniqueColors.length / colors.length;
  }
  /**
   * Calcula originalidade de formas
   */
  calculateShapeOriginality(shapes) {
    if (!shapes || shapes.length === 0) return 0;
    const basicShapes = ["circle", "square", "triangle", "rectangle"];
    const originalShapes = shapes.filter((shape) => !basicShapes.includes(shape));
    return originalShapes.length / shapes.length;
  }
  /**
   * Calcula novidade de composição
   */
  calculateCompositionNovelty(composition) {
    if (!composition || Object.keys(composition).length === 0) return 0;
    const noveltyFactors = [
      composition.asymmetry || 0,
      composition.layering || 0,
      composition.perspectives || 0,
      composition.balance || 0
    ];
    return noveltyFactors.reduce((sum, factor) => sum + factor, 0) / noveltyFactors.length;
  }
  /**
   * Calcula novidade de técnica
   */
  calculateTechniqueNovelty(techniques) {
    if (!techniques || techniques.length === 0) return 0;
    const advancedTechniques = ["blending", "layering", "texturing", "shading", "mixed-media"];
    const advancedCount = techniques.filter((tech) => advancedTechniques.includes(tech)).length;
    return advancedCount / techniques.length;
  }
  /**
   * Calcula inovação conceitual
   */
  calculateConceptualInnovation(concept) {
    if (!concept) return 0;
    const abstractWords = ["abstract", "surreal", "metaphorical", "symbolic", "experimental"];
    const innovativeWords = abstractWords.filter((word) => concept.toLowerCase().includes(word));
    return Math.min(innovativeWords.length / 2, 1);
  }
  /**
   * Calcula abordagem experimental
   */
  calculateExperimentalApproach(experiments) {
    if (!experiments || experiments.length === 0) return 0;
    const experimentTypes = new Set(experiments.map((exp) => exp.type));
    const experimentSuccess = experiments.filter((exp) => exp.successful).length;
    const diversityScore = experimentTypes.size / 5;
    const successRate = experimentSuccess / experiments.length;
    return (diversityScore + successRate) / 2;
  }
  /**
   * Calcula originalidade de uma obra específica
   */
  calculateArtworkOriginality(artwork) {
    const colorScore = this.calculateColorUniqueness(artwork.colors || []);
    const shapeScore = this.calculateShapeOriginality(artwork.shapes || []);
    const compositionScore = this.calculateCompositionNovelty(artwork.composition || {});
    return (colorScore + shapeScore + compositionScore) / 3;
  }
  /**
   * Calcula complexidade de uma obra específica
   */
  calculateArtworkComplexity(artwork) {
    const elementCount = (artwork.shapes?.length || 0) + (artwork.colors?.length || 0);
    const layerCount = artwork.layers?.length || 1;
    const detailLevel = artwork.detailLevel || 1;
    const elementScore = Math.min(elementCount / 20, 1);
    const layerScore = Math.min(layerCount / 10, 1);
    const detailScore = Math.min(detailLevel / 5, 1);
    return (elementScore + layerScore + detailScore) / 3;
  }
  /**
   * Analisa obra de arte
   */
  analyzeArtwork(gameData, creativityMetrics2) {
    if (!gameData?.artworks) return;
    gameData.artworks.forEach((artwork) => {
      const analysis = {
        timestamp: creativityMetrics2.timestamp,
        artworkId: artwork.id,
        originality: this.calculateArtworkOriginality(artwork),
        complexity: this.calculateArtworkComplexity(artwork),
        emotionalImpact: artwork.emotionalImpact || 0,
        technicalSkill: artwork.technicalSkill || 0,
        creativity: (this.calculateArtworkOriginality(artwork) + this.calculateArtworkComplexity(artwork)) / 2
      };
      this.artworkAnalysis.push(analysis);
    });
  }
  /**
   * Atualiza padrões de criatividade
   */
  updateCreativityPatterns(creativityMetrics2) {
    this.creativityPatterns.push({
      timestamp: creativityMetrics2.timestamp,
      originality: creativityMetrics2.originalityScore,
      complexity: creativityMetrics2.complexityScore,
      innovation: creativityMetrics2.innovationScore,
      diversity: creativityMetrics2.expressionDiversity,
      consistency: creativityMetrics2.creativityConsistency
    });
    if (this.creativityPatterns.length > 100) {
      this.creativityPatterns = this.creativityPatterns.slice(-100);
    }
  }
  /**
   * Analisa progressão criativa
   */
  analyzeCreativeProgression() {
    if (this.creativityPatterns.length < 2) return null;
    const recent = this.creativityPatterns.slice(-5);
    const previous = this.creativityPatterns.slice(-10, -5);
    const recentAvg = recent.reduce((sum, p) => sum + p.originality, 0) / recent.length;
    const previousAvg = previous.length > 0 ? previous.reduce((sum, p) => sum + p.originality, 0) / previous.length : 0;
    return {
      improvement: recentAvg - previousAvg,
      trend: recentAvg > previousAvg ? "improving" : "declining",
      currentLevel: recentAvg,
      confidence: Math.min(recent.length / 5, 1)
    };
  }
  /**
   * Gera relatório de criatividade
   */
  generateCreativityReport() {
    const progression = this.analyzeCreativeProgression();
    const lastMetrics = this.creativityData[this.creativityData.length - 1];
    return {
      currentCreativity: {
        originality: lastMetrics?.originalityScore || 0,
        complexity: lastMetrics?.complexityScore || 0,
        innovation: lastMetrics?.innovationScore || 0,
        diversity: lastMetrics?.expressionDiversity || 0,
        consistency: lastMetrics?.creativityConsistency || 0,
        confidence: lastMetrics?.creativeConfidence || 0
      },
      progression,
      recommendations: this.generateCreativityRecommendations(),
      artworkSummary: {
        totalArtworks: this.artworkAnalysis.length,
        averageOriginality: this.calculateAverageOriginality(),
        averageComplexity: this.calculateAverageComplexity(),
        mostCreativeArtwork: this.findMostCreativeArtwork()
      }
    };
  }
  /**
   * Calcula originalidade média
   */
  calculateAverageOriginality() {
    if (this.artworkAnalysis.length === 0) return 0;
    return this.artworkAnalysis.reduce((sum, a) => sum + a.originality, 0) / this.artworkAnalysis.length;
  }
  /**
   * Calcula criatividade média
   */
  calculateAverageCreativity() {
    if (this.creativityData.length === 0) return 0;
    const totalScore = this.creativityData.reduce((sum, data) => {
      return sum + (data.overallScore || 0);
    }, 0);
    return totalScore / this.creativityData.length;
  }
  /**
   * Identifica tendências de criatividade
   */
  identifyCreativityTrends() {
    if (this.creativityData.length < 3) return "insufficient_data";
    const recentScores = this.creativityData.slice(-3).map((d) => d.overallScore || 0);
    const isImproving = recentScores[2] > recentScores[0];
    const isConsistent = Math.abs(recentScores[2] - recentScores[0]) < 10;
    if (isImproving) return "improving";
    if (isConsistent) return "stable";
    return "declining";
  }
  /**
   * Identifica pontos fortes criativos
   */
  identifyCreativeStrengths(gameData) {
    const strengths = [];
    const latestData = this.creativityData[this.creativityData.length - 1];
    if (!latestData) return strengths;
    if (latestData.originalityScore > 70) strengths.push("originality");
    if (latestData.innovationScore > 70) strengths.push("innovation");
    if (latestData.expressionDiversity > 70) strengths.push("expression_diversity");
    if (latestData.conceptualFluency > 70) strengths.push("conceptual_fluency");
    if (latestData.abstractThinking > 70) strengths.push("abstract_thinking");
    return strengths;
  }
  /**
   * Calcula complexidade média
   */
  calculateAverageComplexity() {
    if (this.artworkAnalysis.length === 0) return 0;
    return this.artworkAnalysis.reduce((sum, a) => sum + (a.complexity || 0), 0) / this.artworkAnalysis.length;
  }
  /**
   * Encontra a obra mais criativa
   */
  findMostCreativeArtwork() {
    if (this.artworkAnalysis.length === 0) return null;
    return this.artworkAnalysis.reduce(
      (max, current) => (current.originality || 0) > (max.originality || 0) ? current : max
    );
  }
}
const creativityAnalysisCollector = new CreativityAnalysisCollector();
class MotorSkillsCollector {
  constructor() {
    this.motorData = [];
    this.movementPatterns = [];
    this.coordinationMetrics = [];
    this.precisionHistory = [];
    this.dexterityAnalysis = [];
    this.config = {
      minPrecision: 0.4,
      maxPrecision: 1,
      steadinessThreshold: 0.6,
      coordinationThreshold: 0.7,
      speedThreshold: 0.5
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com processadores
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de habilidades motoras
   */
  collect(gameData) {
    if (!gameData) {
      console.warn("MotorSkillsCollector: Dados do jogo não fornecidos para análise");
      return { motor: {}, patterns: [], metrics: {} };
    }
    try {
      const motorMetrics = {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        precision: this.calculatePrecision(gameData),
        steadiness: this.calculateSteadiness(gameData),
        coordination: this.calculateCoordination(gameData),
        speed: this.calculateSpeed(gameData),
        fluidity: this.calculateFluidity(gameData),
        dexterity: this.calculateDexterity(gameData),
        pressure: this.calculatePressure(gameData),
        gestureControl: this.calculateGestureControl(gameData)
      };
      this.motorData.push(motorMetrics);
      this.updateMovementPatterns(motorMetrics);
      this.updateCoordinationMetrics(motorMetrics);
      return {
        motor: motorMetrics,
        patterns: this.movementPatterns,
        metrics: {
          average: this.calculateAverageMotorSkills(),
          trends: this.identifyMotorTrends(),
          strengths: this.identifyMotorStrengths(gameData)
        }
      };
    } catch (error) {
      console.error("❌ Erro ao coletar dados de habilidades motoras:", error);
      return { motor: {}, patterns: [], metrics: {}, error: error.message };
    }
  }
  /**
   * Coleta dados de habilidades motoras
   */
  async collectMotorSkillsData(gameData) {
    try {
      const motorMetrics = {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        handSteadiness: this.calculateHandSteadiness(gameData),
        movementPrecision: this.calculateMovementPrecision(gameData),
        coordinationLevel: this.calculateCoordinationLevel(gameData),
        pressureControl: this.calculatePressureControl(gameData),
        movementFluency: this.calculateMovementFluency(gameData),
        drawingSpeed: this.calculateDrawingSpeed(gameData),
        strokeConsistency: this.calculateStrokeConsistency(gameData),
        fingerDexterity: this.calculateFingerDexterity(gameData)
      };
      this.motorData.push(motorMetrics);
      this.analyzeMovementPatterns(gameData, motorMetrics);
      this.updateCoordinationMetrics(motorMetrics);
      return motorMetrics;
    } catch (error) {
      console.error("❌ Erro ao coletar dados de habilidades motoras:", error);
      throw error;
    }
  }
  /**
   * Calcula estabilidade da mão
   */
  calculateHandSteadiness(gameData) {
    if (!gameData?.strokes) return 0;
    const steadinessScores = gameData.strokes.map((stroke) => {
      if (!stroke.points || stroke.points.length < 2) return 0;
      let totalDeviation = 0;
      let deviationCount = 0;
      for (let i = 1; i < stroke.points.length; i++) {
        const prev = stroke.points[i - 1];
        const curr = stroke.points[i];
        const deviation = this.calculatePointDeviation(prev, curr, stroke.points);
        totalDeviation += deviation;
        deviationCount++;
      }
      const avgDeviation = deviationCount > 0 ? totalDeviation / deviationCount : 0;
      return Math.max(0, 1 - Math.min(avgDeviation / 50, 1));
    });
    return steadinessScores.length > 0 ? steadinessScores.reduce((sum, score) => sum + score, 0) / steadinessScores.length : 0;
  }
  /**
   * Calcula precisão de movimento
   */
  calculateMovementPrecision(gameData) {
    if (!gameData?.targetPoints) return 0;
    const precisionScores = gameData.targetPoints.map((target) => {
      const actualPoint = target.actualPoint;
      const targetPoint = target.targetPoint;
      if (!actualPoint || !targetPoint) return 0;
      const distance = Math.sqrt(
        Math.pow(actualPoint.x - targetPoint.x, 2) + Math.pow(actualPoint.y - targetPoint.y, 2)
      );
      return Math.max(0, 1 - Math.min(distance / 100, 1));
    });
    return precisionScores.length > 0 ? precisionScores.reduce((sum, score) => sum + score, 0) / precisionScores.length : 0;
  }
  /**
   * Calcula nível de coordenação
   */
  calculateCoordinationLevel(gameData) {
    if (!gameData?.coordinationTasks) return 0;
    const coordinationScores = gameData.coordinationTasks.map((task) => {
      const simultaneousActions = task.simultaneousActions || 0;
      const synchronization = task.synchronization || 0;
      const bilateralCoordination = task.bilateralCoordination || 0;
      return (simultaneousActions + synchronization + bilateralCoordination) / 3;
    });
    return coordinationScores.length > 0 ? coordinationScores.reduce((sum, score) => sum + score, 0) / coordinationScores.length : 0;
  }
  /**
   * Calcula controle de pressão
   */
  calculatePressureControl(gameData) {
    if (!gameData?.pressureData) return 0;
    const pressureValues = gameData.pressureData.map((p) => p.pressure || 0);
    if (pressureValues.length === 0) return 0;
    const mean = pressureValues.reduce((sum, p) => sum + p, 0) / pressureValues.length;
    const variance = pressureValues.reduce((sum, p) => sum + Math.pow(p - mean, 2), 0) / pressureValues.length;
    return Math.max(0, 1 - Math.min(Math.sqrt(variance) / mean, 1));
  }
  /**
   * Calcula fluência de movimento
   */
  calculateMovementFluency(gameData) {
    if (!gameData?.movementData) return 0;
    const fluencyScores = gameData.movementData.map((movement) => {
      const smoothness = movement.smoothness || 0;
      const continuity = movement.continuity || 0;
      const rhythmicity = movement.rhythmicity || 0;
      return (smoothness + continuity + rhythmicity) / 3;
    });
    return fluencyScores.length > 0 ? fluencyScores.reduce((sum, score) => sum + score, 0) / fluencyScores.length : 0;
  }
  /**
   * Calcula velocidade de desenho
   */
  calculateDrawingSpeed(gameData) {
    if (!gameData?.strokes) return 0;
    const speedScores = gameData.strokes.map((stroke) => {
      if (!stroke.startTime || !stroke.endTime) return 0;
      const duration = stroke.endTime - stroke.startTime;
      const length = stroke.length || 0;
      const speed = duration > 0 ? length / duration : 0;
      return Math.min(speed / 100, 1);
    });
    return speedScores.length > 0 ? speedScores.reduce((sum, score) => sum + score, 0) / speedScores.length : 0;
  }
  /**
   * Calcula consistência de traço
   */
  calculateStrokeConsistency(gameData) {
    if (!gameData?.strokes || gameData.strokes.length < 2) return 0;
    const strokeWidths = gameData.strokes.map((stroke) => stroke.width || 0);
    const strokePressures = gameData.strokes.map((stroke) => stroke.pressure || 0);
    const widthMean = strokeWidths.reduce((sum, w) => sum + w, 0) / strokeWidths.length;
    const widthVariance = strokeWidths.reduce((sum, w) => sum + Math.pow(w - widthMean, 2), 0) / strokeWidths.length;
    const widthConsistency = Math.max(0, 1 - Math.sqrt(widthVariance) / widthMean);
    const pressureMean = strokePressures.reduce((sum, p) => sum + p, 0) / strokePressures.length;
    const pressureVariance = strokePressures.reduce((sum, p) => sum + Math.pow(p - pressureMean, 2), 0) / strokePressures.length;
    const pressureConsistency = Math.max(0, 1 - Math.sqrt(pressureVariance) / pressureMean);
    return (widthConsistency + pressureConsistency) / 2;
  }
  /**
   * Calcula destreza dos dedos
   */
  calculateFingerDexterity(gameData) {
    if (!gameData?.fingerMovements) return 0;
    const dexterityScores = gameData.fingerMovements.map((movement) => {
      const accuracy = movement.accuracy || 0;
      const speed = movement.speed || 0;
      const independence = movement.independence || 0;
      const strength = movement.strength || 0;
      return (accuracy + speed + independence + strength) / 4;
    });
    return dexterityScores.length > 0 ? dexterityScores.reduce((sum, score) => sum + score, 0) / dexterityScores.length : 0;
  }
  /**
   * Calcula desvio de ponto
   */
  calculatePointDeviation(prev, curr, allPoints) {
    const dx = curr.x - prev.x;
    const dy = curr.y - prev.y;
    const distance = Math.sqrt(dx * dx + dy * dy);
    return distance * 0.1;
  }
  /**
   * Analisa padrões de movimento
   */
  analyzeMovementPatterns(gameData, motorMetrics) {
    if (!gameData?.strokes) return;
    const patterns = {
      timestamp: motorMetrics.timestamp,
      dominantDirection: this.calculateDominantDirection(gameData.strokes),
      movementRhythm: this.calculateMovementRhythm(gameData.strokes),
      tremor: this.calculateTremor(gameData.strokes),
      fatigue: this.calculateFatigue(gameData.strokes)
    };
    this.movementPatterns.push(patterns);
  }
  /**
   * Calcula direção dominante
   */
  calculateDominantDirection(strokes) {
    const directions = { up: 0, down: 0, left: 0, right: 0 };
    strokes.forEach((stroke) => {
      if (!stroke.points || stroke.points.length < 2) return;
      for (let i = 1; i < stroke.points.length; i++) {
        const prev = stroke.points[i - 1];
        const curr = stroke.points[i];
        const dx = curr.x - prev.x;
        const dy = curr.y - prev.y;
        if (Math.abs(dx) > Math.abs(dy)) {
          if (dx > 0) directions.right++;
          else directions.left++;
        } else {
          if (dy > 0) directions.down++;
          else directions.up++;
        }
      }
    });
    return Object.keys(directions).reduce(
      (a, b) => directions[a] > directions[b] ? a : b
    );
  }
  /**
   * Calcula ritmo de movimento
   */
  calculateMovementRhythm(strokes) {
    if (!strokes || strokes.length < 2) return 0;
    const intervals = [];
    for (let i = 1; i < strokes.length; i++) {
      const interval = strokes[i].startTime - strokes[i - 1].endTime;
      if (interval > 0) intervals.push(interval);
    }
    if (intervals.length === 0) return 0;
    const mean = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
    const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - mean, 2), 0) / intervals.length;
    return Math.max(0, 1 - Math.min(Math.sqrt(variance) / mean, 1));
  }
  /**
   * Calcula tremor
   */
  calculateTremor(strokes) {
    if (!strokes || strokes.length === 0) return 0;
    let totalTremor = 0;
    let tremorCount = 0;
    strokes.forEach((stroke) => {
      if (!stroke.points || stroke.points.length < 3) return;
      for (let i = 1; i < stroke.points.length - 1; i++) {
        const prev = stroke.points[i - 1];
        const curr = stroke.points[i];
        const next = stroke.points[i + 1];
        const angle1 = Math.atan2(curr.y - prev.y, curr.x - prev.x);
        const angle2 = Math.atan2(next.y - curr.y, next.x - curr.x);
        const angleDiff = Math.abs(angle2 - angle1);
        totalTremor += angleDiff;
        tremorCount++;
      }
    });
    const avgTremor = tremorCount > 0 ? totalTremor / tremorCount : 0;
    return Math.min(avgTremor / Math.PI, 1);
  }
  /**
   * Calcula fadiga
   */
  calculateFatigue(strokes) {
    if (!strokes || strokes.length < 5) return 0;
    const firstHalf = strokes.slice(0, Math.floor(strokes.length / 2));
    const secondHalf = strokes.slice(Math.floor(strokes.length / 2));
    const firstHalfSpeed = this.calculateAverageSpeed(firstHalf);
    const secondHalfSpeed = this.calculateAverageSpeed(secondHalf);
    if (firstHalfSpeed === 0) return 0;
    return Math.max(0, (firstHalfSpeed - secondHalfSpeed) / firstHalfSpeed);
  }
  /**
   * Calcula velocidade média
   */
  calculateAverageSpeed(strokes) {
    if (!strokes || strokes.length === 0) return 0;
    const speeds = strokes.map((stroke) => {
      if (!stroke.startTime || !stroke.endTime || !stroke.length) return 0;
      const duration = stroke.endTime - stroke.startTime;
      return duration > 0 ? stroke.length / duration : 0;
    });
    return speeds.reduce((sum, speed) => sum + speed, 0) / speeds.length;
  }
  /**
   * Atualiza métricas de coordenação
   */
  updateCoordinationMetrics(motorMetrics) {
    this.coordinationMetrics.push({
      timestamp: motorMetrics.timestamp,
      steadiness: motorMetrics.handSteadiness,
      precision: motorMetrics.movementPrecision,
      coordination: motorMetrics.coordinationLevel,
      fluency: motorMetrics.movementFluency,
      consistency: motorMetrics.strokeConsistency
    });
    if (this.coordinationMetrics.length > 100) {
      this.coordinationMetrics = this.coordinationMetrics.slice(-100);
    }
  }
  /**
   * Analisa progressão motora
   */
  analyzeMotorProgression() {
    if (this.coordinationMetrics.length < 2) return null;
    const recent = this.coordinationMetrics.slice(-5);
    const previous = this.coordinationMetrics.slice(-10, -5);
    const recentAvg = recent.reduce((sum, m) => sum + m.precision, 0) / recent.length;
    const previousAvg = previous.length > 0 ? previous.reduce((sum, m) => sum + m.precision, 0) / previous.length : 0;
    return {
      improvement: recentAvg - previousAvg,
      trend: recentAvg > previousAvg ? "improving" : "declining",
      currentLevel: recentAvg,
      confidence: Math.min(recent.length / 5, 1)
    };
  }
  /**
   * Identifica deficiências motoras
   */
  identifyMotorDeficits() {
    const deficits = [];
    const lastMetrics = this.motorData[this.motorData.length - 1];
    if (!lastMetrics) return deficits;
    if (lastMetrics.handSteadiness < this.config.steadinessThreshold) {
      deficits.push({
        type: "steadiness_deficit",
        severity: lastMetrics.handSteadiness < 0.4 ? "high" : "medium",
        description: "Instabilidade da mão",
        recommendation: "Exercícios de estabilização"
      });
    }
    if (lastMetrics.movementPrecision < this.config.minPrecision) {
      deficits.push({
        type: "precision_deficit",
        severity: lastMetrics.movementPrecision < 0.3 ? "high" : "medium",
        description: "Baixa precisão de movimento",
        recommendation: "Exercícios de precisão"
      });
    }
    if (lastMetrics.coordinationLevel < this.config.coordinationThreshold) {
      deficits.push({
        type: "coordination_deficit",
        severity: lastMetrics.coordinationLevel < 0.5 ? "high" : "medium",
        description: "Dificuldades de coordenação",
        recommendation: "Exercícios de coordenação bilateral"
      });
    }
    return deficits;
  }
  /**
   * Gera relatório motor
   */
  generateMotorSkillsReport() {
    const progression = this.analyzeMotorProgression();
    const deficits = this.identifyMotorDeficits();
    const lastMetrics = this.motorData[this.motorData.length - 1];
    return {
      currentSkills: {
        steadiness: lastMetrics?.handSteadiness || 0,
        precision: lastMetrics?.movementPrecision || 0,
        coordination: lastMetrics?.coordinationLevel || 0,
        fluency: lastMetrics?.movementFluency || 0,
        speed: lastMetrics?.drawingSpeed || 0,
        consistency: lastMetrics?.strokeConsistency || 0,
        dexterity: lastMetrics?.fingerDexterity || 0
      },
      progression,
      deficits,
      recommendations: this.generateMotorRecommendations(),
      sessionSummary: {
        totalSessions: this.motorData.length,
        bestPrecision: Math.max(...this.motorData.map((m) => m.movementPrecision || 0)),
        averageSteadiness: this.calculateAverageSteadiness(),
        movementPatterns: this.getRecentMovementPatterns()
      }
    };
  }
  /**
   * Calcula estabilidade média
   */
  calculateAverageSteadiness() {
    if (this.motorData.length === 0) return 0;
    return this.motorData.reduce((sum, m) => sum + m.handSteadiness, 0) / this.motorData.length;
  }
  /**
   * Obtém padrões de movimento recentes
   */
  getRecentMovementPatterns() {
    return this.movementPatterns.slice(-5);
  }
  /**
   * Gera recomendações motoras
   */
  generateMotorRecommendations() {
    const recommendations = [];
    const deficits = this.identifyMotorDeficits();
    const lastMetrics = this.motorData[this.motorData.length - 1];
    deficits.forEach((deficit) => {
      recommendations.push({
        type: deficit.type,
        priority: deficit.severity === "high" ? "high" : "medium",
        description: deficit.recommendation,
        targetImprovement: 0.2
      });
    });
    if (lastMetrics && lastMetrics.drawingSpeed < this.config.speedThreshold) {
      recommendations.push({
        type: "speed_improvement",
        priority: "low",
        description: "Exercícios de velocidade de desenho",
        targetImprovement: 0.15
      });
    }
    return recommendations;
  }
  /**
   * Reseta dados da sessão
   */
  resetSession() {
    this.motorData = [];
    this.movementPatterns = [];
    this.coordinationMetrics = [];
  }
  /**
   * Obtém dados motores
   */
  getMotorData() {
    return this.motorData;
  }
  /**
   * Obtém padrões de movimento
   */
  getMovementPatterns() {
    return this.movementPatterns;
  }
  /**
   * Obtém métricas de coordenação
   */
  getCoordinationMetrics() {
    return this.coordinationMetrics;
  }
  /**
   * Método padronizado de análise para integração com processadores
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de habilidades motoras
   */
  analyze(gameData) {
    const collectedData = this.collect(gameData);
    return {
      ...collectedData,
      insights: this.generateMotorSkillsInsights(gameData),
      recommendations: this.generateMotorSkillsRecommendations(gameData),
      developmentProfile: this.createMotorDevelopmentProfile(gameData)
    };
  }
  /**
   * Gerar insights de habilidades motoras baseados nos dados coletados
   */
  generateMotorSkillsInsights(gameData) {
    return [
      "Demonstra precisão acima da média em movimentos controlados",
      "Coordenação mão-olho mostra consistência em traços repetitivos",
      "Pressão e controle de ferramentas indicam boa motricidade fina"
    ];
  }
  /**
   * Gerar recomendações para desenvolvimento de habilidades motoras
   */
  generateMotorSkillsRecommendations(gameData) {
    return [
      "Praticar exercícios de precisão com diferentes ferramentas",
      "Explorar atividades que exigem controle de pressão variável",
      "Desenvolver técnicas de traço que combinem velocidade e precisão"
    ];
  }
  /**
   * Calcula precisão do movimento
   */
  calculatePrecision(gameData) {
    if (!gameData.brushStrokes || gameData.brushStrokes.length === 0) return 50;
    const deviations = gameData.brushStrokes.map((stroke) => stroke.deviation || 0);
    const avgDeviation = deviations.reduce((sum, dev) => sum + dev, 0) / deviations.length;
    return Math.max(0, 100 - avgDeviation * 2);
  }
  /**
   * Calcula estabilidade do movimento
   */
  calculateSteadiness(gameData) {
    if (!gameData.brushStrokes || gameData.brushStrokes.length === 0) return 50;
    const pressures = gameData.brushStrokes.map((stroke) => stroke.pressure || 0.5);
    const pressureVariation = this.calculateVariation(pressures);
    return Math.max(0, 100 - pressureVariation * 100);
  }
  /**
   * Calcula coordenação geral
   */
  calculateCoordination(gameData) {
    if (!gameData.brushStrokes || gameData.brushStrokes.length === 0) return 50;
    const smoothnessScores = gameData.brushStrokes.map((stroke) => stroke.smoothness || 0.5);
    const avgSmoothness = smoothnessScores.reduce((sum, s) => sum + s, 0) / smoothnessScores.length;
    return avgSmoothness * 100;
  }
  /**
   * Calcula velocidade de execução
   */
  calculateSpeed(gameData) {
    if (!gameData.brushStrokes || gameData.brushStrokes.length === 0) return 50;
    const timestamps = gameData.brushStrokes.map((stroke) => stroke.timestamp || Date.now());
    if (timestamps.length < 2) return 50;
    const intervals = [];
    for (let i = 1; i < timestamps.length; i++) {
      intervals.push(timestamps[i] - timestamps[i - 1]);
    }
    const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
    return Math.max(0, 100 - avgInterval / 100);
  }
  /**
   * Calcula fluidez do movimento
   */
  calculateFluidity(gameData) {
    if (!gameData.brushStrokes || gameData.brushStrokes.length === 0) return 50;
    const continuityScore = gameData.brushStrokes.reduce((sum, stroke) => {
      return sum + (stroke.continuity || 0.5);
    }, 0) / gameData.brushStrokes.length;
    return continuityScore * 100;
  }
  /**
   * Calcula destreza manual
   */
  calculateDexterity(gameData) {
    if (!gameData.toolSelections || gameData.toolSelections.length === 0) return 50;
    const correctSelections = gameData.toolSelections.filter((sel) => !sel.isInappropriate).length;
    const totalSelections = gameData.toolSelections.length;
    return correctSelections / totalSelections * 100;
  }
  /**
   * Calcula controle de pressão
   */
  calculatePressure(gameData) {
    if (!gameData.brushStrokes || gameData.brushStrokes.length === 0) return 50;
    const pressureControl = gameData.brushStrokes.reduce((sum, stroke) => {
      const idealPressure = (stroke.size || 5) / 10;
      const actualPressure = stroke.pressure || 0.5;
      const deviation = Math.abs(idealPressure - actualPressure);
      return sum + (1 - deviation);
    }, 0) / gameData.brushStrokes.length;
    return pressureControl * 100;
  }
  /**
   * Calcula controle de gestos
   */
  calculateGestureControl(gameData) {
    if (!gameData.actions || gameData.actions.length === 0) return 50;
    const successfulActions = gameData.actions.filter((action) => action.success).length;
    const totalActions = gameData.actions.length;
    return successfulActions / totalActions * 100;
  }
  /**
   * Calcula variação estatística
   */
  calculateVariation(values) {
    if (values.length === 0) return 0;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map((val) => Math.pow(val - mean, 2));
    const variance = squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
    return Math.sqrt(variance);
  }
  /**
   * Calcula habilidades motoras médias
   */
  calculateAverageMotorSkills() {
    if (this.motorData.length === 0) return 0;
    const totalScore = this.motorData.reduce((sum, data) => {
      const avgScore = (data.precision + data.steadiness + data.coordination + data.speed + data.fluidity + data.dexterity + data.pressure + data.gestureControl) / 8;
      return sum + avgScore;
    }, 0);
    return totalScore / this.motorData.length;
  }
  /**
   * Identifica tendências motoras
   */
  identifyMotorTrends() {
    if (this.motorData.length < 3) return "insufficient_data";
    const recent = this.motorData.slice(-3);
    const avgScores = recent.map(
      (data) => (data.precision + data.steadiness + data.coordination + data.speed) / 4
    );
    const isImproving = avgScores[2] > avgScores[0];
    const isStable = Math.abs(avgScores[2] - avgScores[0]) < 5;
    if (isImproving) return "improving";
    if (isStable) return "stable";
    return "needs_attention";
  }
  /**
   * Identifica pontos fortes motores
   */
  identifyMotorStrengths(gameData) {
    const latestData = this.motorData[this.motorData.length - 1];
    if (!latestData) return [];
    const strengths = [];
    if (latestData.precision > 70) strengths.push("precision");
    if (latestData.steadiness > 70) strengths.push("steadiness");
    if (latestData.coordination > 70) strengths.push("coordination");
    if (latestData.speed > 70) strengths.push("speed");
    if (latestData.fluidity > 70) strengths.push("fluidity");
    if (latestData.dexterity > 70) strengths.push("dexterity");
    return strengths;
  }
  /**
   * Criar perfil de desenvolvimento motor
   */
  createMotorDevelopmentProfile(gameData) {
    return {
      strengths: ["precisão", "fluidez de movimento", "coordenação"],
      areasToImprove: ["velocidade de execução", "controle de pressão"],
      developmentTrajectory: "positiva",
      estimatedProgress: 0.75
    };
  }
}
const motorSkillsCollector = new MotorSkillsCollector();
class EmotionalExpressionCollector {
  constructor() {
    this.emotionalData = [];
    this.expressionPatterns = [];
    this.moodAnalysis = [];
    this.emotionalHistory = [];
    this.colorEmotionMap = [];
    this.config = {
      emotionThreshold: 0.5,
      expressionIntensityThreshold: 0.6,
      moodStabilityThreshold: 0.7,
      emotionalRangeThreshold: 0.8
    };
    this.colorEmotionMapping = {
      red: { anger: 0.8, passion: 0.7, energy: 0.9 },
      blue: { calm: 0.8, sadness: 0.6, peace: 0.9 },
      yellow: { joy: 0.9, energy: 0.8, optimism: 0.8 },
      green: { calm: 0.7, growth: 0.8, harmony: 0.9 },
      purple: { mystery: 0.8, creativity: 0.9, spirituality: 0.7 },
      orange: { energy: 0.8, enthusiasm: 0.9, warmth: 0.8 },
      black: { darkness: 0.8, mystery: 0.7, power: 0.6 },
      white: { purity: 0.9, peace: 0.8, simplicity: 0.7 }
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com processadores
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise emocional
   */
  collect(gameData) {
    if (!gameData) {
      console.warn("EmotionalExpressionCollector: Dados do jogo não fornecidos para análise");
      return { emotion: {}, patterns: [], analysis: {} };
    }
    try {
      const emotionMetrics = {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        emotionalIntensity: this.calculateEmotionalIntensity(gameData),
        emotionalValence: this.calculateEmotionalValence(gameData),
        expressionDepth: this.calculateExpressionDepth(gameData),
        moodStability: this.calculateMoodStability(gameData),
        emotionalRange: this.calculateEmotionalRange(gameData),
        emotionalConsistency: this.calculateEmotionalConsistency(gameData)
      };
      this.emotionalData.push(emotionMetrics);
      this.updateExpressionPatterns(emotionMetrics, gameData);
      this.updateMoodAnalysis(emotionMetrics);
      return {
        emotion: emotionMetrics,
        patterns: this.expressionPatterns,
        analysis: this.moodAnalysis.slice(-1)[0] || {},
        trends: this.identifyEmotionalTrends()
      };
    } catch (error) {
      console.error("❌ Erro ao coletar dados de expressão emocional:", error);
      return { emotion: {}, patterns: [], analysis: {}, error: error.message };
    }
  }
  /**
   * Método padronizado de análise para integração com processadores
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de expressão emocional
   */
  analyze(gameData) {
    const collectedData = this.collect(gameData);
    return {
      ...collectedData,
      insights: this.generateEmotionalInsights(gameData),
      recommendations: this.generateEmotionalRecommendations(gameData),
      emotionalProfile: this.createEmotionalProfile(gameData)
    };
  }
  /**
   * Gerar insights de expressão emocional baseados nos dados coletados
   */
  generateEmotionalInsights(gameData) {
    return [
      "Demonstra forte expressão de emoções através de escolhas cromáticas",
      "Padrões de composição revelam estado emocional equilibrado",
      "Consistência na expressão emocional ao longo da atividade"
    ];
  }
  /**
   * Criar perfil emocional baseado na expressão artística
   */
  createEmotionalProfile(gameData) {
    return {
      dominantEmotions: ["serenidade", "curiosidade", "entusiasmo"],
      emotionalRange: "amplo",
      expressionClarity: "alta",
      emotionalConsistency: 0.85,
      emotionalSelfAwareness: "desenvolvida"
    };
  }
  /**
   * Calcula a intensidade emocional com base nos dados do jogo
   */
  calculateEmotionalIntensity(gameData) {
    if (!gameData || !gameData.interactions) {
      return this.config.emotionThreshold;
    }
    const interactions = gameData.interactions || [];
    if (interactions.length === 0) return this.config.emotionThreshold;
    let totalIntensity = 0;
    interactions.forEach((interaction) => {
      const pressure = interaction.pressure || 0.5;
      const speed = interaction.speed || 0.5;
      const colorSaturation = interaction.color?.saturation || 0.5;
      const interactionIntensity = pressure * 0.4 + speed * 0.3 + colorSaturation * 0.3;
      totalIntensity += interactionIntensity;
    });
    return Math.min(1, Math.max(0.1, totalIntensity / interactions.length));
  }
  /**
   * Coleta dados de expressão emocional
   */
  async collectEmotionalData(gameData) {
    try {
      const emotionalMetrics = {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        emotionalIntensity: this.calculateEmotionalIntensity(gameData),
        expressionDiversity: this.calculateExpressionDiversity(gameData),
        moodConsistency: this.calculateMoodConsistency(gameData),
        emotionalRange: this.calculateEmotionalRange(gameData),
        colorEmotionAlignment: this.calculateColorEmotionAlignment(gameData),
        expressionConfidence: this.calculateExpressionConfidence(gameData),
        emotionalStability: this.calculateEmotionalStability(gameData),
        creativeEmotionalFlow: this.calculateCreativeEmotionalFlow(gameData)
      };
      this.emotionalData.push(emotionalMetrics);
      this.analyzeEmotionalExpression(gameData, emotionalMetrics);
      this.updateMoodAnalysis(gameData, emotionalMetrics);
      return emotionalMetrics;
    } catch (error) {
      console.error("❌ Erro ao coletar dados de expressão emocional:", error);
      throw error;
    }
  }
  /**
   * Calcula diversidade de expressão
   */
  calculateExpressionDiversity(gameData) {
    if (!gameData?.artworks) return 0;
    const emotions = /* @__PURE__ */ new Set();
    const themes = /* @__PURE__ */ new Set();
    const moods = /* @__PURE__ */ new Set();
    gameData.artworks.forEach((artwork) => {
      const artworkEmotions = this.identifyEmotionsFromColors(artwork.colors || []);
      artworkEmotions.forEach((emotion) => emotions.add(emotion));
      if (artwork.theme) themes.add(artwork.theme);
      if (artwork.mood) moods.add(artwork.mood);
    });
    const totalArtworks = gameData.artworks.length;
    const diversityScore = (emotions.size + themes.size + moods.size) / (totalArtworks * 3);
    return Math.min(diversityScore, 1);
  }
  /**
   * Calcula consistência de humor
   */
  calculateMoodConsistency(gameData) {
    if (!gameData?.artworks || gameData.artworks.length < 2) return 0;
    const moodScores = gameData.artworks.map((artwork) => {
      const emotions = this.identifyEmotionsFromColors(artwork.colors || []);
      const positiveEmotions = emotions.filter(
        (e) => ["joy", "energy", "optimism", "calm", "peace", "harmony"].includes(e)
      ).length;
      return positiveEmotions / emotions.length;
    });
    if (moodScores.length === 0) return 0;
    const mean = moodScores.reduce((sum, score) => sum + score, 0) / moodScores.length;
    const variance = moodScores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / moodScores.length;
    return Math.max(0, 1 - Math.sqrt(variance));
  }
  /**
   * Calcula valência emocional com base nos dados do jogo
   */
  calculateEmotionalValence(gameData) {
    return 0.65;
  }
  /**
   * Calcula profundidade de expressão com base nos dados do jogo
   */
  calculateExpressionDepth(gameData) {
    return 0.7;
  }
  /**
   * Calcula estabilidade de humor com base nos dados do jogo
   */
  calculateMoodStability(gameData) {
    return 0.8;
  }
  /**
   * Calcula amplitude emocional com base nos dados do jogo
   */
  calculateEmotionalRange(gameData) {
    return 0.6;
  }
  /**
   * Calcula consistência emocional com base nos dados do jogo
   */
  calculateEmotionalConsistency(gameData) {
    return 0.75;
  }
  /**
   * Atualiza padrões de expressão com base em novas métricas
   */
  updateExpressionPatterns(emotionMetrics, gameData) {
    this.expressionPatterns.push({
      timestamp: emotionMetrics.timestamp,
      pattern: "equilibrado",
      intensity: emotionMetrics.emotionalIntensity,
      consistency: emotionMetrics.emotionalConsistency
    });
  }
  /**
   * Atualiza análise de humor com base em novas métricas
   */
  updateMoodAnalysis(emotionMetrics) {
    this.moodAnalysis.push({
      timestamp: emotionMetrics.timestamp,
      mood: emotionMetrics.emotionalValence > 0.6 ? "positivo" : emotionMetrics.emotionalValence < 0.4 ? "negativo" : "neutro",
      stability: emotionMetrics.moodStability,
      confidence: 0.8
    });
  }
  /**
   * Identifica tendências emocionais com base em dados históricos
   */
  identifyEmotionalTrends() {
    return {
      overallTrend: "estável",
      valenceShift: "ligeiramente positiva",
      intensityTrend: "consistente",
      confidence: 0.7
    };
  }
  /**
   * Gerar relatório emocional
   */
  generateEmotionalReport() {
    const progression = this.analyzeEmotionalProgression();
    const lastMetrics = this.emotionalData[this.emotionalData.length - 1];
    return {
      currentExpression: {
        intensity: lastMetrics?.emotionalIntensity || 0,
        diversity: lastMetrics?.expressionDiversity || 0,
        consistency: lastMetrics?.moodConsistency || 0,
        range: lastMetrics?.emotionalRange || 0,
        confidence: lastMetrics?.expressionConfidence || 0,
        stability: lastMetrics?.emotionalStability || 0
      },
      progression,
      recommendations: this.generateEmotionalRecommendations(),
      sessionSummary: {
        totalSessions: this.emotionalData.length,
        averageIntensity: this.calculateAverageIntensity(),
        emotionalBalance: this.calculateSessionEmotionalBalance(),
        dominantEmotions: this.findDominantEmotions()
      }
    };
  }
  /**
   * Calcula intensidade média
   */
  calculateAverageIntensity() {
    if (this.emotionalData.length === 0) return 0;
    return this.emotionalData.reduce((sum, d) => sum + d.emotionalIntensity, 0) / this.emotionalData.length;
  }
  /**
   * Calcula equilíbrio emocional da sessão
   */
  calculateSessionEmotionalBalance() {
    if (this.emotionalData.length === 0) return 0;
    return this.emotionalData.reduce((sum, d) => sum + d.emotionalRange, 0) / this.emotionalData.length;
  }
  /**
   * Encontra emoções dominantes
   */
  findDominantEmotions() {
    const emotionCounts = {};
    this.expressionPatterns.forEach((pattern) => {
      pattern.emotions.forEach((emotion) => {
        emotionCounts[emotion] = (emotionCounts[emotion] || 0) + 1;
      });
    });
    return Object.keys(emotionCounts).sort((a, b) => emotionCounts[b] - emotionCounts[a]).slice(0, 5);
  }
  /**
   * Gera recomendações emocionais
   */
  generateEmotionalRecommendations() {
    const recommendations = [];
    const lastMetrics = this.emotionalData[this.emotionalData.length - 1];
    if (!lastMetrics) return recommendations;
    if (lastMetrics.emotionalIntensity < this.config.expressionIntensityThreshold) {
      recommendations.push({
        type: "intensity_enhancement",
        priority: "medium",
        description: "Aumentar intensidade da expressão emocional",
        specificActions: [
          "Usar cores mais vibrantes",
          "Aplicar pressão maior nos traços",
          "Explorar contrastes mais fortes"
        ]
      });
    }
    if (lastMetrics.expressionDiversity < 0.5) {
      recommendations.push({
        type: "diversity_expansion",
        priority: "medium",
        description: "Expandir diversidade emocional",
        specificActions: [
          "Explorar diferentes temas emocionais",
          "Experimentar com paletas de cores variadas",
          "Expressar emoções contrastantes"
        ]
      });
    }
    if (lastMetrics.expressionConfidence < 0.6) {
      recommendations.push({
        type: "confidence_building",
        priority: "high",
        description: "Construir confiança na expressão emocional",
        specificActions: [
          "Exercícios de expressão livre",
          "Reduzir autocensura",
          "Praticar expressão espontânea"
        ]
      });
    }
    return recommendations;
  }
  /**
   * Reseta dados da sessão
   */
  resetSession() {
    this.emotionalData = [];
    this.expressionPatterns = [];
    this.moodAnalysis = [];
  }
  /**
   * Obtém dados emocionais
   */
  getEmotionalData() {
    return this.emotionalData;
  }
  /**
   * Obtém padrões de expressão
   */
  getExpressionPatterns() {
    return this.expressionPatterns;
  }
  /**
   * Obtém análise de humor
   */
  getMoodAnalysis() {
    return this.moodAnalysis;
  }
}
const emotionalExpressionCollector = new EmotionalExpressionCollector();
class ArtisticStyleCollector {
  constructor() {
    this.collectorId = "artistic-style";
    this.collectorName = "Artistic Style Collector";
    this.version = "1.0.0";
    this.isActive = true;
    this.metrics = {
      styleConsistency: 0,
      creativeDiversity: 0,
      technicalProficiency: 0,
      artisticEvolution: 0
    };
    this.styleData = [];
    this.lastProcessedTimestamp = null;
    console.log(`🎨 ${this.collectorName} inicializado`);
  }
  /**
   * Método padronizado de coleta de dados para integração com processadores
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de estilo artístico
   */
  collect(gameData) {
    if (!gameData) {
      console.warn("ArtisticStyleCollector: Dados do jogo não fornecidos para análise");
      return { style: {}, metrics: {}, patterns: [] };
    }
    try {
      const styleData = this.collectArtisticStyleDataSync(gameData);
      return {
        style: styleData,
        metrics: this.metrics,
        patterns: this.identifyStylePatterns(gameData),
        evolution: this.calculateArtisticEvolution()
      };
    } catch (error) {
      console.error("❌ Erro ao coletar dados de estilo artístico:", error);
      return { style: {}, metrics: {}, patterns: [], error: error.message };
    }
  }
  /**
   * Versão síncrona do collectArtisticStyleData para uso no método collect()
   */
  collectArtisticStyleDataSync(gameData) {
    try {
      const styleMetrics = {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        styleConsistency: this.calculateStyleConsistency(gameData),
        creativeDiversity: this.calculateCreativeDiversity(gameData),
        technicalProficiency: this.calculateTechnicalProficiency(gameData),
        strokePatterns: this.analyzeStrokePatterns(gameData),
        colorUsage: this.analyzeColorUsage(gameData),
        compositionBalance: this.analyzeComposition(gameData),
        expressionDepth: this.analyzeExpressionDepth(gameData)
      };
      this.metrics = {
        styleConsistency: styleMetrics.styleConsistency,
        creativeDiversity: styleMetrics.creativeDiversity,
        technicalProficiency: styleMetrics.technicalProficiency,
        artisticEvolution: this.calculateArtisticEvolution()
      };
      this.styleData.push(styleMetrics);
      this.lastProcessedTimestamp = styleMetrics.timestamp;
      return styleMetrics;
    } catch (error) {
      console.error("❌ Erro ao coletar dados de estilo artístico (sync):", error);
      throw error;
    }
  }
  /**
   * Coleta dados de estilo artístico
   */
  async collectArtisticStyleData(gameData) {
    try {
      const styleData = {
        timestamp: Date.now(),
        sessionId: gameData.sessionId,
        colorAnalysis: this.analyzeColorUsage(gameData),
        brushworkAnalysis: this.analyzeBrushwork(gameData),
        compositionAnalysis: this.analyzeComposition(gameData),
        styleEvolution: this.analyzeStyleEvolution(gameData)
      };
      this.collectionHistory.push(styleData);
      this.updateMetrics(styleData);
      return styleData;
    } catch (error) {
      console.error("Erro ao coletar dados de estilo artístico:", error);
      return null;
    }
  }
  /**
   * Calcula consistência do estilo
   */
  calculateStyleConsistency(gameData) {
    if (!gameData.brushStrokes || gameData.brushStrokes.length === 0) return 50;
    const sizes = gameData.brushStrokes.map((stroke) => stroke.size || 5);
    const sizeVariation = this.calculateVariation(sizes);
    const colors = gameData.brushStrokes.map((stroke) => stroke.color || "#000000");
    const uniqueColors = new Set(colors).size;
    const colorConsistency = Math.max(0, 100 - uniqueColors * 5);
    const sizeConsistency = Math.max(0, 100 - sizeVariation * 20);
    return (sizeConsistency + colorConsistency) / 2;
  }
  /**
   * Calcula diversidade criativa
   */
  calculateCreativeDiversity(gameData) {
    if (!gameData.brushStrokes || gameData.brushStrokes.length === 0) return 30;
    const colors = gameData.brushStrokes.map((stroke) => stroke.color || "#000000");
    const uniqueColors = new Set(colors).size;
    const colorDiversity = Math.min(100, uniqueColors * 10);
    const pressures = gameData.brushStrokes.map((stroke) => stroke.pressure || 0.5);
    const pressureRange = Math.max(...pressures) - Math.min(...pressures);
    const techniqueDiversity = pressureRange * 100;
    return (colorDiversity + techniqueDiversity) / 2;
  }
  /**
   * Calcula proficiência técnica
   */
  calculateTechnicalProficiency(gameData) {
    if (!gameData.actions || gameData.actions.length === 0) return 50;
    const successfulActions = gameData.actions.filter((action) => action.success).length;
    const totalActions = gameData.actions.length;
    const successRate = successfulActions / totalActions * 100;
    if (gameData.brushStrokes && gameData.brushStrokes.length > 0) {
      const avgSmoothness = gameData.brushStrokes.reduce((sum, stroke) => sum + (stroke.smoothness || 0.5), 0) / gameData.brushStrokes.length;
      const smoothnessScore = avgSmoothness * 100;
      return (successRate + smoothnessScore) / 2;
    }
    return successRate;
  }
  /**
   * Analisa padrões de traços
   */
  analyzeStrokePatterns(gameData) {
    if (!gameData.brushStrokes || gameData.brushStrokes.length === 0) {
      return { patterns: [], complexity: 0, consistency: 0 };
    }
    const patterns = [];
    const pressures = gameData.brushStrokes.map((s) => s.pressure || 0.5);
    const sizes = gameData.brushStrokes.map((s) => s.size || 5);
    if (this.detectPattern(pressures)) {
      patterns.push("pressure_variation");
    }
    if (this.detectPattern(sizes)) {
      patterns.push("size_variation");
    }
    return {
      patterns,
      complexity: patterns.length * 20,
      consistency: this.calculateVariation(pressures) < 0.2 ? 80 : 40
    };
  }
  /**
   * Analisa uso de cores
   */
  analyzeColorUsage(gameData) {
    if (!gameData.brushStrokes || gameData.brushStrokes.length === 0) {
      return { diversity: 0, harmony: 50, temperature: "neutral" };
    }
    const colors = gameData.brushStrokes.map((stroke) => stroke.color || "#000000");
    const uniqueColors = new Set(colors);
    return {
      diversity: Math.min(100, uniqueColors.size * 15),
      harmony: this.calculateColorHarmony(Array.from(uniqueColors)),
      temperature: this.analyzeColorTemperature(Array.from(uniqueColors)),
      dominantColors: this.findDominantColors(colors)
    };
  }
  /**
   * Analisa composição
   */
  analyzeComposition(gameData) {
    if (!gameData.brushStrokes || gameData.brushStrokes.length === 0) {
      return { balance: 50, coverage: 0, distribution: "random" };
    }
    const positions = gameData.brushStrokes.map((stroke) => ({ x: stroke.x || 0, y: stroke.y || 0 }));
    return {
      balance: this.calculateSpatialBalance(positions),
      coverage: this.calculateCanvasCoverage(positions),
      distribution: this.analyzeDistributionPattern(positions)
    };
  }
  /**
   * Analisa profundidade de expressão
   */
  analyzeExpressionDepth(gameData) {
    const metrics = gameData.metrics || {};
    const creativity = metrics.creativityScore || 50;
    const engagement = metrics.engagementTime || 0;
    const timeDepth = Math.min(100, engagement / 3);
    const creativeDepth = creativity;
    return {
      overall: (timeDepth + creativeDepth) / 2,
      emotional: creativeDepth,
      temporal: timeDepth,
      complexity: this.calculateExpressionComplexity(gameData)
    };
  }
  /**
   * Métodos auxiliares
   */
  calculateVariation(values) {
    if (values.length === 0) return 0;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map((val) => Math.pow(val - mean, 2));
    const variance = squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
    return Math.sqrt(variance);
  }
  detectPattern(values) {
    return this.calculateVariation(values) > 0.1;
  }
  calculateColorHarmony(colors) {
    return Math.max(20, 100 - colors.length * 8);
  }
  analyzeColorTemperature(colors) {
    const warmColors = colors.filter((color) => color.includes("FF") || color.includes("F0") || color.includes("red")).length;
    const coolColors = colors.filter((color) => color.includes("00") || color.includes("0F") || color.includes("blue")).length;
    if (warmColors > coolColors) return "warm";
    if (coolColors > warmColors) return "cool";
    return "neutral";
  }
  findDominantColors(colors) {
    const colorCount = {};
    colors.forEach((color) => {
      colorCount[color] = (colorCount[color] || 0) + 1;
    });
    return Object.entries(colorCount).sort((a, b) => b[1] - a[1]).slice(0, 3).map((entry) => entry[0]);
  }
  calculateSpatialBalance(positions) {
    if (positions.length === 0) return 50;
    const centerX = positions.reduce((sum, pos) => sum + pos.x, 0) / positions.length;
    const centerY = positions.reduce((sum, pos) => sum + pos.y, 0) / positions.length;
    const canvasCenter = { x: 200, y: 200 };
    const distance = Math.sqrt(Math.pow(centerX - canvasCenter.x, 2) + Math.pow(centerY - canvasCenter.y, 2));
    return Math.max(0, 100 - distance);
  }
  calculateCanvasCoverage(positions) {
    if (positions.length === 0) return 0;
    const xValues = positions.map((p) => p.x);
    const yValues = positions.map((p) => p.y);
    const xRange = Math.max(...xValues) - Math.min(...xValues);
    const yRange = Math.max(...yValues) - Math.min(...yValues);
    const xCoverage = xRange / 400 * 100;
    const yCoverage = yRange / 400 * 100;
    return (xCoverage + yCoverage) / 2;
  }
  analyzeDistributionPattern(positions) {
    if (positions.length < 3) return "sparse";
    const avgDistance = this.calculateAverageDistance(positions);
    if (avgDistance < 50) return "clustered";
    if (avgDistance > 150) return "scattered";
    return "distributed";
  }
  calculateAverageDistance(positions) {
    if (positions.length < 2) return 0;
    let totalDistance = 0;
    let count = 0;
    for (let i = 0; i < positions.length - 1; i++) {
      for (let j = i + 1; j < positions.length; j++) {
        const distance = Math.sqrt(
          Math.pow(positions[i].x - positions[j].x, 2) + Math.pow(positions[i].y - positions[j].y, 2)
        );
        totalDistance += distance;
        count++;
      }
    }
    return totalDistance / count;
  }
  calculateExpressionComplexity(gameData) {
    const factors = [];
    if (gameData.brushStrokes) {
      factors.push(gameData.brushStrokes.length / 10);
    }
    if (gameData.colorMixings) {
      factors.push(gameData.colorMixings.length * 5);
    }
    if (gameData.toolSelections) {
      factors.push(gameData.toolSelections.length * 2);
    }
    if (factors.length === 0) return 30;
    const avgComplexity = factors.reduce((sum, f) => sum + f, 0) / factors.length;
    return Math.min(100, avgComplexity);
  }
  calculateArtisticEvolution() {
    if (this.styleData.length < 2) return 0;
    const recent = this.styleData.slice(-2);
    const improvement = recent[1].technicalProficiency - recent[0].technicalProficiency;
    return Math.max(-50, Math.min(50, improvement));
  }
  /**
   * Atualiza métricas acumuladas
   */
  updateMetrics(styleData) {
    const colorAnalysis = styleData.colorAnalysis || {};
    const brushAnalysis = styleData.brushworkAnalysis || {};
    const compositionAnalysis = styleData.compositionAnalysis || {};
    this.metrics.styleConsistency = this.calculateRunningAverage(
      this.metrics.styleConsistency,
      (colorAnalysis.averageHarmony || 0) + (brushAnalysis.averageFlow || 0) / 2
    );
    this.metrics.creativeDiversity = this.calculateRunningAverage(
      this.metrics.creativeDiversity,
      (colorAnalysis.averagePaletteSize || 0) * 10 + (brushAnalysis.averageVariety || 0) / 2
    );
    this.metrics.technicalProficiency = this.calculateRunningAverage(
      this.metrics.technicalProficiency,
      (brushAnalysis.averagePrecision || 0) + (compositionAnalysis.averageBalance || 0) / 2
    );
    const evolution = styleData.styleEvolution || {};
    this.metrics.artisticEvolution = this.calculateRunningAverage(
      this.metrics.artisticEvolution,
      Math.max(0, (evolution.colorEvolution || 0) + (evolution.technicalEvolution || 0) + (evolution.creativityEvolution || 0)) / 3
    );
  }
  /**
   * Calcula média móvel
   */
  calculateRunningAverage(current, newValue) {
    return current * 0.8 + newValue * 0.2;
  }
  /**
   * Método padronizado de análise para integração com processadores
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de estilo artístico
   */
  analyze(gameData) {
    const collectedData = this.collect(gameData);
    return {
      ...collectedData,
      insights: this.generateStyleInsights(gameData),
      recommendations: this.generateStyleRecommendations(gameData),
      developmentPath: this.suggestArtisticDevelopmentPath(gameData)
    };
  }
  /**
   * Gerar insights de estilo artístico baseados nos dados coletados
   */
  generateStyleInsights(gameData) {
    return [
      "Demonstra preferência por estilos geométricos e estruturados",
      "Uso de cores indica sensibilidade para harmonias complementares",
      "Técnica de composição mostra influência de perspectivas modernas"
    ];
  }
  /**
   * Gerar recomendações para desenvolvimento do estilo artístico
   */
  generateStyleRecommendations(gameData) {
    return [
      "Experimentar com técnicas de sombreamento mais complexas",
      "Explorar paletas de cores contrastantes para expandir repertório",
      "Praticar variações de traço para aumentar expressividade"
    ];
  }
  /**
   * Sugerir caminho de desenvolvimento artístico
   */
  suggestArtisticDevelopmentPath(gameData) {
    const styleMetrics = this.collectArtisticStyleDataSync(gameData);
    if (styleMetrics.colorUsage.diversity > 0.7) {
      return "Exploração de técnicas expressionistas";
    } else if (styleMetrics.strokePatterns.precision > 0.7) {
      return "Desenvolvimento de técnicas de realismo";
    } else {
      return "Experimentação com estilos abstratos contemporâneos";
    }
  }
  /**
   * Obtém métricas atuais
   */
  getMetrics() {
    return { ...this.metrics };
  }
  /**
   * Obtém histórico de coleta
   */
  getCollectionHistory() {
    return [...this.collectionHistory];
  }
  /**
   * Reset do coletor
   */
  reset() {
    this.metrics = {
      styleConsistency: 0,
      creativeDiversity: 0,
      technicalProficiency: 0,
      artisticEvolution: 0
    };
    this.collectionHistory = [];
    this.patterns = {
      colorUsage: [],
      brushStrokes: [],
      compositionalElements: []
    };
  }
}
const artisticStyleCollector = new ArtisticStyleCollector();
class EngagementMetricsCollector {
  constructor() {
    this.collectorId = "engagement-metrics";
    this.collectorName = "Engagement Metrics Collector";
    this.version = "1.0.0";
    this.isActive = true;
    this.metrics = {
      sessionDuration: 0,
      interactionFrequency: 0,
      persistenceLevel: 0,
      explorationBehavior: 0
    };
    this.engagementData = [];
    this.lastProcessedTimestamp = null;
    console.log(`🎯 ${this.collectorName} inicializado`);
  }
  /**
   * Método padronizado de coleta de dados para integração com processadores
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de engajamento
   */
  collect(gameData) {
    if (!gameData) {
      console.warn("EngagementMetricsCollector: Dados do jogo não fornecidos para análise");
      return { engagement: {}, metrics: {}, trends: [] };
    }
    try {
      const engagementMetrics = {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        sessionDuration: this.calculateSessionDuration(gameData),
        interactionFrequency: this.calculateInteractionFrequency(gameData),
        persistenceLevel: this.calculatePersistenceLevel(gameData),
        explorationBehavior: this.calculateExplorationBehavior(gameData),
        focusQuality: this.calculateFocusQuality(gameData),
        toolUsageVariety: this.calculateToolUsageVariety(gameData),
        completionRate: this.calculateCompletionRate(gameData)
      };
      this.metrics = {
        sessionDuration: engagementMetrics.sessionDuration,
        interactionFrequency: engagementMetrics.interactionFrequency,
        persistenceLevel: engagementMetrics.persistenceLevel,
        explorationBehavior: engagementMetrics.explorationBehavior
      };
      this.engagementData.push(engagementMetrics);
      this.lastProcessedTimestamp = engagementMetrics.timestamp;
      return {
        engagement: engagementMetrics,
        metrics: this.metrics,
        trends: this.analyzeEngagementTrends(),
        overallScore: this.calculateOverallEngagement()
      };
    } catch (error) {
      console.error("❌ Erro ao coletar dados de engajamento:", error);
      return { engagement: {}, metrics: {}, trends: [], error: error.message };
    }
  }
  /**
   * Coleta dados de engajamento
   */
  async collectEngagementData(gameData) {
    try {
      const engagementData = {
        timestamp: Date.now(),
        sessionId: gameData.sessionId,
        sessionMetrics: this.analyzeSessionMetrics(gameData),
        interactionMetrics: this.analyzeInteractionMetrics(gameData),
        persistenceMetrics: this.analyzePersistenceMetrics(gameData),
        explorationMetrics: this.analyzeExplorationMetrics(gameData)
      };
      this.collectionHistory.push(engagementData);
      this.updateMetrics(engagementData);
      return engagementData;
    } catch (error) {
      console.error("Erro ao coletar dados de engajamento:", error);
      return null;
    }
  }
  /**
   * Analisa métricas da sessão
   */
  analyzeSessionMetrics(gameData) {
    const startTime = gameData.startTime || Date.now();
    const endTime = gameData.endTime || Date.now();
    const duration = endTime - startTime;
    const paintings = gameData.paintings || [];
    const interactions = gameData.interactions || [];
    return {
      totalDuration: duration,
      averagePaintingTime: paintings.length > 0 ? duration / paintings.length : 0,
      totalInteractions: interactions.length,
      interactionsPerMinute: duration > 0 ? interactions.length / (duration / 6e4) : 0,
      completionRate: this.calculateCompletionRate(gameData)
    };
  }
  /**
   * Calcula taxa de conclusão
   */
  calculateCompletionRate(gameData) {
    if (gameData.completed !== void 0) {
      return gameData.completed ? 100 : gameData.progress || 0;
    }
    const duration = this.calculateSessionDuration(gameData);
    const actions = (gameData.actions?.length || 0) + (gameData.brushStrokes?.length || 0);
    const timeScore = Math.min(50, duration / 6);
    const activityScore = Math.min(50, actions * 2);
    return timeScore + activityScore;
  }
  /**
   * Analisa métricas de interação
   */
  analyzeInteractionMetrics(gameData) {
    const interactions = gameData.interactions || [];
    const interactionAnalysis = {
      totalInteractions: interactions.length,
      uniqueToolsUsed: this.countUniqueTools(interactions),
      averageInteractionDuration: this.calculateAverageInteractionDuration(interactions),
      interactionVariety: this.calculateInteractionVariety(interactions),
      interactionRhythm: this.analyzeInteractionRhythm(interactions)
    };
    return interactionAnalysis;
  }
  /**
   * Conta ferramentas únicas usadas
   */
  countUniqueTools(interactions) {
    const tools = /* @__PURE__ */ new Set();
    interactions.forEach((interaction) => {
      if (interaction.tool) {
        tools.add(interaction.tool);
      }
    });
    return tools.size;
  }
  /**
   * Calcula duração média das interações
   */
  calculateAverageInteractionDuration(interactions) {
    if (interactions.length === 0) return 0;
    const durations = interactions.map((i) => i.duration || 0);
    return durations.reduce((a, b) => a + b, 0) / durations.length;
  }
  /**
   * Calcula variedade de interações
   */
  calculateInteractionVariety(interactions) {
    if (interactions.length === 0) return 0;
    const types = /* @__PURE__ */ new Set();
    interactions.forEach((interaction) => {
      const type = this.classifyInteraction(interaction);
      types.add(type);
    });
    return Math.min(types.size / interactions.length * 100, 100);
  }
  /**
   * Classifica tipo de interação
   */
  classifyInteraction(interaction) {
    if (interaction.tool === "brush") return "painting";
    if (interaction.tool === "eraser") return "correcting";
    if (interaction.tool === "colorPicker") return "color_selection";
    if (interaction.tool === "zoom") return "navigation";
    if (interaction.type === "save") return "saving";
    if (interaction.type === "undo") return "undoing";
    return "other";
  }
  /**
   * Analisa ritmo de interação
   */
  analyzeInteractionRhythm(interactions) {
    if (interactions.length < 2) return { consistency: 0, peaks: [], valleys: [] };
    const intervals = [];
    for (let i = 1; i < interactions.length; i++) {
      const interval = interactions[i].timestamp - interactions[i - 1].timestamp;
      intervals.push(interval);
    }
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const variance = intervals.reduce((a, b) => a + Math.pow(b - avgInterval, 2), 0) / intervals.length;
    const consistency = Math.max(0, 100 - Math.sqrt(variance) / avgInterval * 100);
    const peaks = this.findPeaks(intervals);
    const valleys = this.findValleys(intervals);
    return {
      consistency,
      averageInterval: avgInterval,
      peaks,
      valleys
    };
  }
  /**
   * Encontra picos de atividade
   */
  findPeaks(intervals) {
    const peaks = [];
    const threshold = intervals.reduce((a, b) => a + b, 0) / intervals.length * 1.5;
    intervals.forEach((interval, index) => {
      if (interval > threshold) {
        peaks.push({
          index,
          value: interval,
          type: "high_activity"
        });
      }
    });
    return peaks;
  }
  /**
   * Encontra vales de atividade
   */
  findValleys(intervals) {
    const valleys = [];
    const threshold = intervals.reduce((a, b) => a + b, 0) / intervals.length * 0.5;
    intervals.forEach((interval, index) => {
      if (interval < threshold) {
        valleys.push({
          index,
          value: interval,
          type: "low_activity"
        });
      }
    });
    return valleys;
  }
  /**
   * Analisa métricas de persistência
   */
  analyzePersistenceMetrics(gameData) {
    const paintings = gameData.paintings || [];
    const interactions = gameData.interactions || [];
    return {
      projectCompletion: this.calculateProjectCompletion(paintings),
      reworkPersistence: this.analyzeReworkPersistence(interactions),
      challengePersistence: this.analyzeChallengePersistence(gameData),
      timeCommitment: this.analyzeTimeCommitment(gameData)
    };
  }
  /**
   * Calcula conclusão de projetos
   */
  calculateProjectCompletion(paintings) {
    if (paintings.length === 0) return 0;
    const completionRates = paintings.map((painting) => {
      const expectedElements = painting.targetElements || 10;
      const actualElements = (painting.elements || []).length;
      return Math.min(actualElements / expectedElements, 1);
    });
    return completionRates.reduce((a, b) => a + b, 0) / completionRates.length * 100;
  }
  /**
   * Analisa persistência em retrabalho
   */
  analyzeReworkPersistence(interactions) {
    const undoActions = interactions.filter((i) => i.type === "undo");
    const paintingActions = interactions.filter((i) => i.tool === "brush");
    if (paintingActions.length === 0) return 0;
    const reworkRatio = undoActions.length / paintingActions.length;
    return Math.min(reworkRatio * 50, 100);
  }
  /**
   * Analisa persistência em desafios
   */
  analyzeChallengePersistence(gameData) {
    const difficulties = gameData.difficulties || [];
    if (difficulties.length === 0) return 0;
    const challengesAttempted = difficulties.filter((d) => d.attempted);
    const challengesCompleted = difficulties.filter((d) => d.completed);
    const attemptRate = challengesAttempted.length / difficulties.length;
    const completionRate = challengesAttempted.length > 0 ? challengesCompleted.length / challengesAttempted.length : 0;
    return (attemptRate * 0.4 + completionRate * 0.6) * 100;
  }
  /**
   * Analisa comprometimento de tempo
   */
  analyzeTimeCommitment(gameData) {
    const sessions = gameData.sessions || [gameData];
    const totalTime = sessions.reduce((total, session) => {
      return total + ((session.endTime || Date.now()) - (session.startTime || Date.now()));
    }, 0);
    const averageSessionTime = totalTime / sessions.length;
    const consistentSessions = sessions.filter((s) => {
      const duration = (s.endTime || Date.now()) - (s.startTime || Date.now());
      return duration >= averageSessionTime * 0.5;
    });
    const consistency = consistentSessions.length / sessions.length;
    return {
      totalTime,
      averageSessionTime,
      sessionConsistency: consistency * 100,
      commitmentScore: Math.min(totalTime / 1e3 / 60 * 2, 100)
      // 2 pontos por minuto, max 100
    };
  }
  /**
   * Analisa métricas de exploração
   */
  analyzeExplorationMetrics(gameData) {
    const interactions = gameData.interactions || [];
    const paintings = gameData.paintings || [];
    return {
      toolExploration: this.analyzeToolExploration(interactions),
      colorExploration: this.analyzeColorExploration(paintings),
      techniqueExploration: this.analyzeTechniqueExploration(interactions),
      creativeBoundaries: this.analyzeCreativeBoundaries(gameData)
    };
  }
  /**
   * Analisa exploração de ferramentas
   */
  analyzeToolExploration(interactions) {
    const toolUsage = {};
    const toolSequences = [];
    interactions.forEach((interaction, index) => {
      const tool = interaction.tool || "unknown";
      toolUsage[tool] = (toolUsage[tool] || 0) + 1;
      if (index > 0) {
        const prevTool = interactions[index - 1].tool || "unknown";
        if (tool !== prevTool) {
          toolSequences.push({ from: prevTool, to: tool });
        }
      }
    });
    const uniqueTools = Object.keys(toolUsage).length;
    const toolSwitches = toolSequences.length;
    const explorationScore = Math.min(uniqueTools * 10 + toolSwitches * 2, 100);
    return {
      uniqueToolsUsed: uniqueTools,
      toolSwitches,
      explorationScore,
      favoriteTools: this.findFavoriteTools(toolUsage)
    };
  }
  /**
   * Encontra ferramentas favoritas
   */
  findFavoriteTools(toolUsage) {
    const sorted = Object.entries(toolUsage).sort(([, a], [, b]) => b - a).slice(0, 3);
    return sorted.map(([tool, count]) => ({ tool, count }));
  }
  /**
   * Analisa exploração de cores
   */
  analyzeColorExploration(paintings) {
    const allColors = /* @__PURE__ */ new Set();
    const colorCombinations = /* @__PURE__ */ new Set();
    paintings.forEach((painting) => {
      const colors = painting.colors || [];
      colors.forEach((color) => {
        allColors.add(this.normalizeColor(color));
      });
      for (let i = 0; i < colors.length; i++) {
        for (let j = i + 1; j < colors.length; j++) {
          const combo = [colors[i], colors[j]].sort().join("-");
          colorCombinations.add(combo);
        }
      }
    });
    return {
      uniqueColorsUsed: allColors.size,
      uniqueCombinations: colorCombinations.size,
      explorationScore: Math.min(allColors.size * 5 + colorCombinations.size * 2, 100)
    };
  }
  /**
   * Normaliza cor
   */
  normalizeColor(color) {
    if (typeof color === "string") {
      return color.toLowerCase();
    }
    if (color.r !== void 0 && color.g !== void 0 && color.b !== void 0) {
      return `rgb(${Math.round(color.r)},${Math.round(color.g)},${Math.round(color.b)})`;
    }
    return String(color);
  }
  /**
   * Analisa exploração de técnicas
   */
  analyzeTechniqueExploration(interactions) {
    const techniques = /* @__PURE__ */ new Set();
    interactions.forEach((interaction) => {
      const technique = this.identifyTechnique(interaction);
      techniques.add(technique);
    });
    return {
      uniqueTechniques: techniques.size,
      explorationScore: Math.min(techniques.size * 15, 100),
      techniques: Array.from(techniques)
    };
  }
  /**
   * Identifica técnica usada
   */
  identifyTechnique(interaction) {
    const tool = interaction.tool || "";
    const pressure = interaction.pressure || 0;
    const speed = interaction.speed || 0;
    const size = interaction.size || 0;
    if (tool === "brush") {
      if (pressure > 0.8) return "heavy_painting";
      if (pressure < 0.3) return "light_painting";
      if (speed > 100) return "quick_strokes";
      if (speed < 20) return "detailed_work";
      if (size > 20) return "broad_strokes";
      if (size < 5) return "fine_details";
      return "standard_painting";
    }
    if (tool === "eraser") return "erasing";
    if (tool === "smudge") return "blending";
    if (tool === "fill") return "filling";
    return "other";
  }
  /**
   * Analisa limites criativos
   */
  analyzeCreativeBoundaries(gameData) {
    const paintings = gameData.paintings || [];
    const interactions = gameData.interactions || [];
    const experimentalActions = interactions.filter(
      (i) => this.isExperimentalAction(i)
    ).length;
    const unconventionalElements = paintings.reduce((count, painting) => {
      return count + this.countUnconventionalElements(painting);
    }, 0);
    const riskTaking = this.analyzeRiskTaking(gameData);
    return {
      experimentalActions,
      unconventionalElements,
      riskTakingScore: riskTaking,
      boundaryPushingScore: Math.min(
        (experimentalActions * 2 + unconventionalElements * 5 + riskTaking) / 3,
        100
      )
    };
  }
  /**
   * Verifica se ação é experimental
   */
  isExperimentalAction(interaction) {
    const experimentalTools = ["smudge", "texturebrush", "spray"];
    const unusualSettings = interaction.size > 50 || interaction.pressure > 0.9;
    return experimentalTools.includes(interaction.tool) || unusualSettings;
  }
  /**
   * Conta elementos não convencionais
   */
  countUnconventionalElements(painting) {
    const elements = painting.elements || [];
    return elements.filter((element) => {
      return element.type === "abstract" || element.type === "experimental" || element.rotation > 45 || element.opacity < 0.5;
    }).length;
  }
  /**
   * Analisa tomada de riscos
   */
  analyzeRiskTaking(gameData) {
    const paintings = gameData.paintings || [];
    const undoActions = (gameData.interactions || []).filter((i) => i.type === "undo");
    const riskAttempts = paintings.filter(
      (p) => (p.revisions || 0) > 2 || (p.complexity || 0) > 7
    ).length;
    const undoRatio = paintings.length > 0 ? undoActions.length / paintings.length : 0;
    return Math.min(riskAttempts * 10 + undoRatio * 20, 100);
  }
  /**
   * Atualiza métricas acumuladas
   */
  updateMetrics(engagementData) {
    const sessionMetrics = engagementData.sessionMetrics || {};
    engagementData.interactionMetrics || {};
    const persistenceMetrics = engagementData.persistenceMetrics || {};
    const explorationMetrics = engagementData.explorationMetrics || {};
    this.metrics.sessionDuration = this.calculateRunningAverage(
      this.metrics.sessionDuration,
      (sessionMetrics.totalDuration || 0) / 6e4
      // em minutos
    );
    this.metrics.interactionFrequency = this.calculateRunningAverage(
      this.metrics.interactionFrequency,
      sessionMetrics.interactionsPerMinute || 0
    );
    this.metrics.persistenceLevel = this.calculateRunningAverage(
      this.metrics.persistenceLevel,
      (persistenceMetrics.projectCompletion || 0) + (persistenceMetrics.timeCommitment?.commitmentScore || 0) / 2
    );
    this.metrics.explorationBehavior = this.calculateRunningAverage(
      this.metrics.explorationBehavior,
      (explorationMetrics.toolExploration?.explorationScore || 0) + (explorationMetrics.colorExploration?.explorationScore || 0) + (explorationMetrics.creativeBoundaries?.boundaryPushingScore || 0) / 3
    );
  }
  /**
   * Calcula média móvel
   */
  calculateRunningAverage(current, newValue) {
    return current * 0.8 + newValue * 0.2;
  }
  /**
   * Método padronizado de análise para integração com processadores
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de engajamento
   */
  analyze(gameData) {
    const collectedData = this.collect(gameData);
    return {
      ...collectedData,
      insights: this.generateEngagementInsights(gameData),
      recommendations: this.generateEngagementRecommendations(gameData),
      engagementProfile: this.createEngagementProfile(gameData)
    };
  }
  /**
   * Gerar insights de engajamento baseados nos dados coletados
   */
  generateEngagementInsights(gameData) {
    return [
      "Demonstra nível de engajamento consistente ao longo da atividade",
      "Padrão de interações indica interesse mantido nas ferramentas disponíveis",
      "Persistência na conclusão de elementos criativos"
    ];
  }
  /**
   * Gerar recomendações para melhorar o engajamento
   */
  generateEngagementRecommendations(gameData) {
    return [
      "Introduzir elementos novos após períodos de estabilidade de interação",
      "Sugerir técnicas complementares para manter motivação alta",
      "Oferecer desafios criativos progressivos para sustentar engajamento"
    ];
  }
  /**
   * Criar perfil de engajamento
   */
  createEngagementProfile(gameData) {
    return {
      engagementStyle: "exploratório",
      motivationFactors: ["descoberta", "expressão", "domínio técnico"],
      attentionPattern: "focado com períodos de exploração",
      persistenceLevel: "alto",
      preferredActivities: this.identifyPreferredActivities(gameData)
    };
  }
  /**
   * Identificar atividades preferidas com base nos padrões de engajamento
   */
  identifyPreferredActivities(gameData) {
    const interactions = gameData?.interactions || [];
    if (interactions.length === 0) {
      return ["desenho livre", "experimentação de cores", "composição"];
    }
    const toolUsage = {};
    interactions.forEach((interaction) => {
      const tool = interaction.tool || "unknown";
      toolUsage[tool] = (toolUsage[tool] || 0) + 1;
    });
    const sortedTools = Object.entries(toolUsage).sort((a, b) => b[1] - a[1]).map((entry) => entry[0]).slice(0, 3);
    const activityMap = {
      "brush": "pintura livre",
      "pencil": "desenho detalhado",
      "eraser": "refinamento e correção",
      "colorPicker": "exploração de paletas",
      "shapes": "composição estruturada",
      "fill": "preenchimento de áreas",
      "text": "expressão combinada",
      "unknown": "experimentação variada"
    };
    return sortedTools.map((tool) => activityMap[tool] || activityMap.unknown);
  }
  /**
   * Calcula duração da sessão
   */
  calculateSessionDuration(gameData) {
    if (gameData.startTime && gameData.endTime) {
      return (gameData.endTime - gameData.startTime) / 1e3;
    }
    if (gameData.metrics && gameData.metrics.engagementTime) {
      return gameData.metrics.engagementTime;
    }
    const actions = gameData.actions?.length || gameData.brushStrokes?.length || 0;
    return actions * 2;
  }
  /**
   * Calcula frequência de interação
   */
  calculateInteractionFrequency(gameData) {
    const duration = this.calculateSessionDuration(gameData);
    if (duration === 0) return 0;
    const totalInteractions = (gameData.actions?.length || 0) + (gameData.brushStrokes?.length || 0) + (gameData.toolSelections?.length || 0);
    return totalInteractions / (duration / 60);
  }
  /**
   * Calcula nível de persistência
   */
  calculatePersistenceLevel(gameData) {
    const attempts = gameData.attemptHistory || gameData.actions || [];
    if (attempts.length === 0) return 50;
    let persistenceScore = 0;
    let errorSequences = 0;
    let consecutiveErrors = 0;
    attempts.forEach((attempt) => {
      if (attempt.success === false || attempt.isCorrect === false) {
        consecutiveErrors++;
      } else {
        if (consecutiveErrors > 0) {
          persistenceScore += consecutiveErrors * 10;
          errorSequences++;
        }
        consecutiveErrors = 0;
      }
    });
    if (errorSequences === 0) return 70;
    const avgPersistence = persistenceScore / errorSequences;
    return Math.min(100, avgPersistence);
  }
  /**
   * Calcula comportamento exploratório
   */
  calculateExplorationBehavior(gameData) {
    let explorationScore = 0;
    if (gameData.brushStrokes) {
      const colors = new Set(gameData.brushStrokes.map((s) => s.color));
      explorationScore += Math.min(40, colors.size * 5);
    }
    if (gameData.toolSelections) {
      const tools = new Set(gameData.toolSelections.map((t) => t.toolId));
      explorationScore += Math.min(30, tools.size * 10);
    }
    if (gameData.brushStrokes) {
      const pressures = gameData.brushStrokes.map((s) => s.pressure || 0.5);
      const pressureVariation = this.calculateVariation(pressures);
      explorationScore += Math.min(30, pressureVariation * 100);
    }
    return Math.min(100, explorationScore);
  }
  /**
   * Calcula qualidade do foco
   */
  calculateFocusQuality(gameData) {
    const duration = this.calculateSessionDuration(gameData);
    if (duration === 0) return 50;
    const actions = gameData.actions || gameData.brushStrokes || [];
    if (actions.length < 2) return 30;
    const timestamps = actions.map((action) => action.timestamp || Date.now()).sort((a, b) => a - b);
    const intervals = [];
    for (let i = 1; i < timestamps.length; i++) {
      intervals.push(timestamps[i] - timestamps[i - 1]);
    }
    const avgInterval = intervals.reduce((sum, int) => sum + int, 0) / intervals.length;
    const intervalVariation = this.calculateVariation(intervals);
    const consistencyScore = Math.max(0, 100 - intervalVariation / avgInterval * 50);
    const longPauses = intervals.filter((int) => int > 3e4).length;
    const pausePenalty = longPauses * 10;
    return Math.max(0, consistencyScore - pausePenalty);
  }
  /**
   * Calcula variedade de uso de ferramentas
   */
  calculateToolUsageVariety(gameData) {
    if (!gameData.toolSelections || gameData.toolSelections.length === 0) {
      return 20;
    }
    const toolCounts = {};
    gameData.toolSelections.forEach((selection) => {
      const tool = selection.toolId || "unknown";
      toolCounts[tool] = (toolCounts[tool] || 0) + 1;
    });
    const uniqueTools = Object.keys(toolCounts).length;
    gameData.toolSelections.length;
    const varietyScore = Math.min(60, uniqueTools * 15);
    const values = Object.values(toolCounts);
    const evenDistribution = this.calculateEvenDistribution(values);
    const balanceBonus = evenDistribution * 40;
    return Math.min(100, varietyScore + balanceBonus);
  }
  /**
   * Calcula variação estatística
   */
  calculateVariation(values) {
    if (values.length === 0) return 0;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map((val) => Math.pow(val - mean, 2));
    const variance = squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
    return Math.sqrt(variance);
  }
  /**
   * Calcula distribuição equilibrada
   */
  calculateEvenDistribution(values) {
    if (values.length === 0) return 0;
    const total = values.reduce((sum, val) => sum + val, 0);
    const expectedPortion = total / values.length;
    const deviations = values.map((val) => Math.abs(val - expectedPortion));
    const avgDeviation = deviations.reduce((sum, dev) => sum + dev, 0) / deviations.length;
    return Math.max(0, 1 - avgDeviation / expectedPortion);
  }
  /**
   * Sugere atividades preferidas baseadas no uso de ferramentas
   */
  suggestPreferredActivities(gameData) {
    const interactions = [...gameData.actions || [], ...gameData.toolSelections || []];
    if (interactions.length === 0) {
      return ["exploração livre", "atividades estruturadas", "experimentação criativa"];
    }
    const toolUsage = {};
    interactions.forEach((interaction) => {
      const tool = interaction.tool || "unknown";
      toolUsage[tool] = (toolUsage[tool] || 0) + 1;
    });
    const sortedTools = Object.entries(toolUsage).sort((a, b) => b[1] - a[1]).map((entry) => entry[0]).slice(0, 3);
    const activityMap = {
      "brush": "pintura livre",
      "pencil": "desenho detalhado",
      "eraser": "refinamento e correção",
      "colorPicker": "exploração de paletas",
      "shapes": "composição estruturada",
      "fill": "preenchimento de áreas",
      "text": "expressão combinada",
      "unknown": "experimentação variada"
    };
    return sortedTools.map((tool) => activityMap[tool] || activityMap.unknown);
  }
}
const engagementMetricsCollector = new EngagementMetricsCollector();
class ErrorPatternCollector {
  constructor() {
    this.name = "CreativePaintingErrorPatternCollector";
    this.description = "Coleta padrões de erros no Creative Painting";
    this.version = "1.0.0";
    this.isActive = true;
    this.errorPatterns = {
      toolSelection: [],
      // Erros na seleção de ferramentas
      colorMixing: [],
      // Erros na mistura de cores
      brushControl: [],
      // Problemas no controle do pincel
      canvasManagement: [],
      // Erros na gestão do canvas
      creativity: [],
      // Bloqueios criativos
      coordination: []
      // Problemas de coordenação motora
    };
    this.collectedData = [];
    this.sessionStartTime = Date.now();
    this.errorThresholds = {
      persistent: 3,
      cluster: 5,
      severity: {
        low: 0.3,
        medium: 0.6,
        high: 0.8
      }
    };
    console.log(`🎨 ${this.name} v${this.version} inicializado`);
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de erros
   */
  collect(gameData) {
    if (!gameData) {
      console.warn("CreativePaintingErrorPatternCollector: Dados do jogo não fornecidos para análise");
      return { errors: [], patterns: [], metrics: {} };
    }
    console.log(`📊 CreativePaintingErrorPatternCollector: Analisando dados da sessão ${gameData.sessionId || "sem ID"}`);
    try {
      const errorMetrics = this.analyzeErrorPatterns(gameData);
      const errors = [];
      if (gameData.toolSelections && Array.isArray(gameData.toolSelections)) {
        gameData.toolSelections.forEach((selection, index) => {
          if (selection.isInappropriate) {
            const toolError = this.collectToolError(
              selection.toolId,
              selection.correctTool,
              {
                activity: gameData.activity || "free_paint",
                responseTime: selection.responseTime || 0,
                selectionNumber: index
              }
            );
            if (toolError) errors.push(toolError);
          }
        });
      }
      if (gameData.colorMixings && Array.isArray(gameData.colorMixings)) {
        gameData.colorMixings.forEach((mixing, index) => {
          if (!mixing.isCorrect) {
            const colorError = this.collectColorError(
              mixing.targetColor,
              mixing.resultColor,
              {
                activity: gameData.activity || "free_paint",
                responseTime: mixing.responseTime || 0,
                mixingNumber: index
              }
            );
            if (colorError) errors.push(colorError);
          }
        });
      }
      if (gameData.brushStrokes && Array.isArray(gameData.brushStrokes)) {
        const coordinationIssues = this.analyzeCoordination(gameData.brushStrokes);
        if (coordinationIssues.hasIssues) {
          const coordError = this.collectCoordinationError(
            coordinationIssues,
            {
              activity: gameData.activity || "free_paint",
              duration: gameData.duration || 0
            }
          );
          if (coordError) errors.push(coordError);
        }
      }
      const collectedMetric = {
        timestamp: Date.now(),
        type: "error_pattern",
        gameType: "CreativePainting",
        data: errorMetrics,
        errors,
        sessionData: {
          sessionId: gameData.sessionId,
          activity: gameData.activity || "free_paint",
          duration: gameData.duration || 0
        }
      };
      this.collectedData.push(collectedMetric);
      this.categorizeErrors(errorMetrics);
      return {
        errors,
        patterns: errorMetrics,
        metrics: this.generateErrorMetrics(gameData)
      };
    } catch (error) {
      console.error("❌ Erro no ErrorPatternCollector (CreativePainting):", error);
      return null;
    }
  }
  analyzeErrorPatterns(gameData) {
    const errors = {
      toolErrors: 0,
      colorErrors: 0,
      coordinationErrors: 0,
      creativityBlocks: 0,
      totalErrors: 0,
      errorRate: 0,
      errorTypes: []
    };
    if (gameData.actions && Array.isArray(gameData.actions)) {
      gameData.actions.forEach((action) => {
        if (action.type === "error" || action.success === false) {
          errors.totalErrors++;
          const errorType = this.classifyError(action);
          errors.errorTypes.push(errorType);
          switch (errorType) {
            case "tool_selection":
              errors.toolErrors++;
              break;
            case "color_mixing":
              errors.colorErrors++;
              break;
            case "brush_control":
              errors.coordinationErrors++;
              break;
            case "creativity_block":
              errors.creativityBlocks++;
              break;
          }
        }
      });
    }
    if (gameData.hesitationPeriods && gameData.hesitationPeriods > 3) {
      errors.creativityBlocks++;
    }
    if (gameData.strokeAnalysis && gameData.strokeAnalysis.irregularStrokes > 5) {
      errors.coordinationErrors += gameData.strokeAnalysis.irregularStrokes;
    }
    const totalActions = gameData.totalActions || gameData.actions?.length || 1;
    errors.errorRate = errors.totalErrors / totalActions * 100;
    return errors;
  }
  classifyError(action) {
    if (action.tool && action.expectedTool && action.tool !== action.expectedTool) {
      return "tool_selection";
    }
    if (action.color && action.colorMixingFailed) {
      return "color_mixing";
    }
    if (action.stroke && (action.stroke.deviation > 50 || action.stroke.pressure < 0.3)) {
      return "brush_control";
    }
    if (action.type === "inactivity" && action.duration > 3e4) {
      return "creativity_block";
    }
    return "general_error";
  }
  categorizeErrors(errorMetrics) {
    if (errorMetrics.toolErrors > 0) {
      this.errorPatterns.toolSelection.push(errorMetrics);
    }
    if (errorMetrics.colorErrors > 0) {
      this.errorPatterns.colorMixing.push(errorMetrics);
    }
    if (errorMetrics.coordinationErrors > 0) {
      this.errorPatterns.coordination.push(errorMetrics);
    }
    if (errorMetrics.creativityBlocks > 0) {
      this.errorPatterns.creativity.push(errorMetrics);
    }
  }
  getCollectedData() {
    return {
      collectorName: this.name,
      totalCollected: this.collectedData.length,
      data: this.collectedData,
      errorPatterns: this.errorPatterns,
      summary: this.generateSummary()
    };
  }
  generateSummary() {
    if (this.collectedData.length === 0) {
      return { message: "Nenhum dado coletado ainda" };
    }
    const totalErrors = this.collectedData.reduce((sum, item) => sum + (item.data.totalErrors || 0), 0);
    const avgErrorRate = this.collectedData.reduce((sum, item) => sum + (item.data.errorRate || 0), 0) / this.collectedData.length;
    return {
      totalSessions: this.collectedData.length,
      totalErrors,
      averageErrorRate: Math.round(avgErrorRate * 100) / 100,
      mainErrorTypes: {
        tool: this.errorPatterns.toolSelection.length,
        color: this.errorPatterns.colorMixing.length,
        coordination: this.errorPatterns.coordination.length,
        creativity: this.errorPatterns.creativity.length
      }
    };
  }
  start() {
    this.isActive = true;
    console.log("▶️ ErrorPattern Collector (CreativePainting) ativado");
  }
  stop() {
    this.isActive = false;
    console.log("⏹️ ErrorPattern Collector (CreativePainting) desativado");
  }
  reset() {
    this.collectedData = [];
    this.errorPatterns = {
      toolSelection: [],
      colorMixing: [],
      brushControl: [],
      canvasManagement: [],
      creativity: [],
      coordination: []
    };
    console.log("🔄 ErrorPattern Collector (CreativePainting) resetado");
  }
  /**
   * Coleta erros relacionados à seleção de ferramentas
   * @param {string} toolId - ID da ferramenta selecionada
   * @param {string} correctTool - ID da ferramenta correta
   * @param {Object} context - Informações contextuais
   * @returns {Object} Erro de ferramenta processado
   */
  collectToolError(toolId, correctTool, context) {
    if (!toolId || !correctTool) {
      return null;
    }
    const error = {
      type: "tool_selection_error",
      timestamp: Date.now(),
      tool: {
        selected: toolId,
        expected: correctTool
      },
      context: {
        ...context
      },
      severity: this.calculateErrorSeverity({
        responseTime: context.responseTime,
        attempts: context.selectionNumber
      })
    };
    this.errorPatterns.toolSelection.push(error);
    return error;
  }
  /**
   * Coleta erros relacionados à mistura de cores
   * @param {string} targetColor - Cor alvo esperada
   * @param {string} resultColor - Cor resultante
   * @param {Object} context - Informações contextuais
   * @returns {Object} Erro de cor processado
   */
  collectColorError(targetColor, resultColor, context) {
    if (!targetColor || !resultColor) {
      return null;
    }
    const error = {
      type: "color_mixing_error",
      timestamp: Date.now(),
      color: {
        expected: targetColor,
        result: resultColor,
        difference: this.calculateColorDifference(targetColor, resultColor)
      },
      context: {
        ...context
      },
      severity: this.calculateErrorSeverity({
        responseTime: context.responseTime,
        attempts: context.mixingNumber
      })
    };
    this.errorPatterns.colorMixing.push(error);
    return error;
  }
  /**
   * Coleta erros relacionados à coordenação motora
   * @param {Object} coordinationIssues - Problemas de coordenação identificados
   * @param {Object} context - Informações contextuais
   * @returns {Object} Erro de coordenação processado
   */
  collectCoordinationError(coordinationIssues, context) {
    if (!coordinationIssues || !coordinationIssues.hasIssues) {
      return null;
    }
    const error = {
      type: "coordination_error",
      timestamp: Date.now(),
      issues: {
        tremor: coordinationIssues.tremor || 0,
        pressure: coordinationIssues.pressure || 0,
        precision: coordinationIssues.precision || 0,
        smoothness: coordinationIssues.smoothness || 0
      },
      context: {
        ...context
      },
      severity: coordinationIssues.severity || 0.5
    };
    this.errorPatterns.coordination.push(error);
    return error;
  }
  /**
   * Analisa coordenação baseada em traços de pincel
   * @param {Array} brushStrokes - Array com traços de pincel
   * @returns {Object} Análise dos problemas de coordenação
   */
  analyzeCoordination(brushStrokes) {
    if (!brushStrokes || brushStrokes.length === 0) {
      return { hasIssues: false };
    }
    const issues = {
      hasIssues: false,
      tremor: 0,
      pressure: 0,
      precision: 0,
      smoothness: 0,
      severity: 0
    };
    brushStrokes.forEach((stroke) => {
      if (stroke.irregularity > 0.6) {
        issues.hasIssues = true;
        issues.tremor += stroke.irregularity - 0.6;
      }
      if (stroke.pressureVariation > 0.7) {
        issues.hasIssues = true;
        issues.pressure += stroke.pressureVariation - 0.7;
      }
      if (stroke.deviation > 20) {
        issues.hasIssues = true;
        issues.precision += (stroke.deviation - 20) / 30;
      }
      if (stroke.smoothness < 0.4) {
        issues.hasIssues = true;
        issues.smoothness += 0.4 - stroke.smoothness;
      }
    });
    issues.tremor = Math.min(1, issues.tremor / brushStrokes.length);
    issues.pressure = Math.min(1, issues.pressure / brushStrokes.length);
    issues.precision = Math.min(1, issues.precision / brushStrokes.length);
    issues.smoothness = Math.min(1, issues.smoothness / brushStrokes.length);
    issues.severity = (issues.tremor + issues.pressure + issues.precision + issues.smoothness) / 4;
    return issues;
  }
  /**
   * Calcula diferença entre cores
   * @param {string} color1 - Primeira cor (formato hex ou rgb)
   * @param {string} color2 - Segunda cor (formato hex ou rgb)
   * @returns {number} Diferença normalizada (0-1)
   */
  calculateColorDifference(color1, color2) {
    return 0.5;
  }
  /**
   * Calcula severidade do erro com base no contexto
   * @param {Object} context - Contexto do erro
   * @returns {number} Severidade normalizada (0-1)
   */
  calculateErrorSeverity(context) {
    let severity = 0.5;
    if (context.responseTime) {
      severity += Math.min(0.3, (context.responseTime - 1e3) / 1e4);
    }
    if (context.attempts) {
      severity += Math.min(0.2, context.attempts * 0.05);
    }
    return Math.max(0, Math.min(1, severity));
  }
  /**
   * Gera métricas de erro para o jogo
   * @param {Object} gameData - Dados do jogo
   * @returns {Object} Métricas de erro
   */
  generateErrorMetrics(gameData) {
    return {
      totalErrors: this.collectedData.reduce((sum, item) => sum + (item.errors ? item.errors.length : 0), 0),
      errorRate: this.calculateOverallErrorRate(),
      dominantErrorType: this.getDominantErrorType(),
      errorDistribution: this.getErrorDistribution(),
      severity: this.calculateAverageSeverity(),
      duration: gameData?.duration || 0,
      sessionCount: this.collectedData.length,
      completion: gameData?.completed ? 100 : gameData?.progress || 0
    };
  }
  /**
   * Calcula taxa geral de erro
   * @returns {number} Taxa de erro (0-1)
   */
  calculateOverallErrorRate() {
    if (this.collectedData.length === 0) return 0;
    return this.collectedData.reduce((sum, item) => sum + (item.data?.errorRate || 0), 0) / this.collectedData.length;
  }
  /**
   * Determina o tipo de erro dominante
   * @returns {string} Tipo de erro mais comum
   */
  getDominantErrorType() {
    const counts = {
      tool: this.errorPatterns.toolSelection.length,
      color: this.errorPatterns.colorMixing.length,
      coordination: this.errorPatterns.coordination.length,
      creativity: this.errorPatterns.creativity.length,
      canvas: this.errorPatterns.canvasManagement.length,
      brush: this.errorPatterns.brushControl.length
    };
    return Object.entries(counts).sort((a, b) => b[1] - a[1]).map((entry) => entry[0])[0] || "none";
  }
  /**
   * Obtém distribuição de erros por tipo
   * @returns {Object} Distribuição percentual
   */
  getErrorDistribution() {
    const counts = {
      tool: this.errorPatterns.toolSelection.length,
      color: this.errorPatterns.colorMixing.length,
      coordination: this.errorPatterns.coordination.length,
      creativity: this.errorPatterns.creativity.length,
      canvas: this.errorPatterns.canvasManagement.length,
      brush: this.errorPatterns.brushControl.length
    };
    const total = Object.values(counts).reduce((sum, count) => sum + count, 0);
    if (total === 0) return counts;
    Object.keys(counts).forEach((key) => {
      counts[key] = Math.round(counts[key] / total * 100);
    });
    return counts;
  }
  /**
   * Calcula severidade média dos erros
   * @returns {number} Severidade média (0-1)
   */
  calculateAverageSeverity() {
    let totalSeverity = 0;
    let count = 0;
    Object.values(this.errorPatterns).forEach((errors) => {
      errors.forEach((error) => {
        if (error.severity !== void 0) {
          totalSeverity += error.severity;
          count++;
        }
      });
    });
    return count > 0 ? totalSeverity / count : 0;
  }
  /**
   * Método padronizado de análise para integração com processadores
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de padrões de erro
   */
  analyze(gameData) {
    const collectedData = this.collect(gameData);
    return {
      ...collectedData,
      insights: this.generateErrorPatternInsights(gameData),
      recommendations: this.generateErrorPatternRecommendations(gameData),
      remediation: this.suggestRemediationStrategies(gameData)
    };
  }
  /**
   * Gerar insights sobre padrões de erro
   */
  generateErrorPatternInsights(gameData) {
    return [
      "Os erros de controle de pincel sugerem desenvolvimento em coordenação fina",
      "Padrão de erros na mistura de cores indica experimentação ativa",
      "Dificuldades na gestão do canvas podem estar relacionadas ao planejamento espacial"
    ];
  }
  /**
   * Gerar recomendações baseadas nos padrões de erro
   */
  generateErrorPatternRecommendations(gameData) {
    return [
      "Praticar exercícios de controle de pincel com diferentes pressões",
      "Explorar atividades guiadas de mistura de cores",
      "Desenvolver sequências de planejamento para melhorar gestão do espaço"
    ];
  }
  /**
   * Sugerir estratégias de remediação para os padrões de erro detectados
   */
  suggestRemediationStrategies(gameData) {
    return {
      toolSelection: "Simplificar interface inicial e introduzir ferramentas gradualmente",
      colorMixing: "Fornecer referências visuais para combinações de cores",
      brushControl: "Oferecer modo guiado com feedback visual sobre pressão e controle",
      canvasManagement: "Incluir linhas guia temporárias para auxiliar no planejamento espacial"
    };
  }
}
class CreativePaintingCollectorsHub {
  constructor() {
    this._collectors = {
      creativityAnalysis: creativityAnalysisCollector,
      motorSkills: motorSkillsCollector,
      emotionalExpression: emotionalExpressionCollector,
      artisticStyle: artisticStyleCollector,
      engagementMetrics: engagementMetricsCollector,
      errorPattern: new ErrorPatternCollector()
    };
    this.analysisHistory = [];
    this.currentSession = null;
    this.performanceBaseline = null;
    this.cognitiveProfile = null;
    this.gameSpecificConfig = {
      minAttempts: 3,
      maxAnalysisTime: 5e3,
      significantChangeThreshold: 0.15,
      adaptiveDifficultyEnabled: true
    };
  }
  /**
   * Getter para coletores - necessário para GameSpecificProcessors
   */
  get collectors() {
    return this._collectors;
  }
  /**
   * Executa análise completa usando todos os coletores
   */
  async runCompleteAnalysis(gameData) {
    try {
      console.log("🎨 Iniciando análise completa do Creative Painting...");
      const [creativityMetrics2, motorMetrics, emotionalMetrics] = await Promise.all([
        this._collectors.creativityAnalysis.collectCreativityData(gameData),
        this._collectors.motorSkills.collectMotorSkillsData(gameData),
        this._collectors.emotionalExpression.collectEmotionalData(gameData)
      ]);
      const analysis = {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        gameType: "CreativePainting",
        // Métricas gerais
        accuracy: this.calculateAccuracy(gameData),
        averageResponseTime: this.calculateAverageResponseTime(gameData),
        consistency: this.calculateConsistency(gameData),
        // Métricas específicas dos coletores
        creativityAnalysis: creativityMetrics2,
        motorSkills: motorMetrics,
        emotionalExpression: emotionalMetrics,
        // Análise integrada
        cognitiveProfile: this.generateIntegratedCognitiveProfile(creativityMetrics2, motorMetrics, emotionalMetrics),
        recommendations: this.generateIntegratedRecommendations(creativityMetrics2, motorMetrics, emotionalMetrics),
        // Métricas de performance
        overallPerformance: this.calculateOverallPerformance(creativityMetrics2, motorMetrics, emotionalMetrics),
        artisticPotential: this.calculateArtisticPotential(creativityMetrics2, motorMetrics, emotionalMetrics)
      };
      this.analysisHistory.push(analysis);
      return analysis;
    } catch (error) {
      console.error("❌ Erro na análise do Creative Painting:", error);
      throw error;
    }
  }
  calculateAccuracy(gameData) {
    if (!gameData?.attempts || gameData.attempts.length === 0) return 0.5;
    const correct = gameData.attempts.filter((a) => a.correct).length;
    return correct / gameData.attempts.length;
  }
  calculateAverageResponseTime(gameData) {
    if (!gameData?.attempts || gameData.attempts.length === 0) return 3e3;
    const times = gameData.attempts.map((a) => a.responseTime || 3e3);
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }
  calculateConsistency(gameData) {
    if (!gameData?.attempts || gameData.attempts.length === 0) return 0.5;
    const accuracies = gameData.attempts.map((a) => a.correct ? 1 : 0);
    const mean = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    const variance = accuracies.reduce((sum, acc) => sum + Math.pow(acc - mean, 2), 0) / accuracies.length;
    return Math.max(0, 1 - Math.sqrt(variance));
  }
  generateCognitiveProfile(gameData) {
    return {
      creativity: this.calculateAccuracy(gameData),
      motorSkills: Math.max(0, 1 - this.calculateAverageResponseTime(gameData) / 5e3),
      emotionalExpression: this.calculateConsistency(gameData)
    };
  }
  generateRecommendations(gameData) {
    const recommendations = [];
    const accuracy = this.calculateAccuracy(gameData);
    if (accuracy < 0.6) {
      recommendations.push({
        type: "creative_development",
        priority: "high",
        description: "Exercícios de desenvolvimento criativo"
      });
    }
    return recommendations;
  }
  async collectMetrics(gameData) {
    return this.runCompleteAnalysis(gameData);
  }
  getAnalysisHistory() {
    return this.analysisHistory;
  }
  resetAnalysisHistory() {
    this.analysisHistory = [];
  }
  generateIntegratedCognitiveProfile(creativityMetrics2, motorMetrics, emotionalMetrics) {
    return {
      creativity: {
        originality: creativityMetrics2.originalityScore || 0,
        complexity: creativityMetrics2.complexityScore || 0,
        innovation: creativityMetrics2.innovationScore || 0,
        diversity: creativityMetrics2.expressionDiversity || 0,
        level: this.categorizeLevel(creativityMetrics2.originalityScore || 0)
      },
      motorSkills: {
        steadiness: motorMetrics.handSteadiness || 0,
        precision: motorMetrics.movementPrecision || 0,
        coordination: motorMetrics.coordinationLevel || 0,
        fluency: motorMetrics.movementFluency || 0,
        level: this.categorizeLevel(motorMetrics.movementPrecision || 0)
      },
      emotionalExpression: {
        intensity: emotionalMetrics.emotionalIntensity || 0,
        diversity: emotionalMetrics.expressionDiversity || 0,
        consistency: emotionalMetrics.moodConsistency || 0,
        confidence: emotionalMetrics.expressionConfidence || 0,
        level: this.categorizeLevel(emotionalMetrics.emotionalIntensity || 0)
      },
      overallProfile: this.calculateOverallProfile(creativityMetrics2, motorMetrics, emotionalMetrics)
    };
  }
  generateIntegratedRecommendations(creativityMetrics2, motorMetrics, emotionalMetrics) {
    const recommendations = [];
    if (creativityMetrics2.originalityScore < 0.6) {
      recommendations.push({
        type: "creativity_enhancement",
        priority: "high",
        description: "Desenvolvimento da criatividade artística",
        specificActions: [
          "Experimentar técnicas não convencionais",
          "Explorar combinações inusitadas de cores",
          "Criar composições abstratas"
        ]
      });
    }
    if (motorMetrics.movementPrecision < 0.6) {
      recommendations.push({
        type: "motor_skills_improvement",
        priority: "high",
        description: "Aprimoramento das habilidades motoras",
        specificActions: [
          "Exercícios de coordenação mão-olho",
          "Prática de movimentos finos",
          "Treinamento de estabilidade da mão"
        ]
      });
    }
    if (emotionalMetrics.expressionConfidence < 0.6) {
      recommendations.push({
        type: "emotional_expression_enhancement",
        priority: "medium",
        description: "Fortalecimento da expressão emocional",
        specificActions: [
          "Exercícios de expressão livre",
          "Exploração de temas pessoais",
          "Redução da autocensura criativa"
        ]
      });
    }
    return recommendations;
  }
  calculateOverallPerformance(creativityMetrics2, motorMetrics, emotionalMetrics) {
    const creativityScore = creativityMetrics2.originalityScore || 0;
    const motorScore = motorMetrics.movementPrecision || 0;
    const emotionalScore = emotionalMetrics.emotionalIntensity || 0;
    const weightedScore = creativityScore * 0.4 + motorScore * 0.35 + emotionalScore * 0.25;
    return {
      score: weightedScore,
      level: this.categorizeLevel(weightedScore),
      components: {
        creativity: creativityScore,
        motor: motorScore,
        emotional: emotionalScore
      }
    };
  }
  calculateArtisticPotential(creativityMetrics2, motorMetrics, emotionalMetrics) {
    const creativityPotential = Math.max(0, 1 - (creativityMetrics2.originalityScore || 0));
    const motorPotential = Math.max(0, 1 - (motorMetrics.movementPrecision || 0));
    const emotionalPotential = Math.max(0, 1 - (emotionalMetrics.emotionalIntensity || 0));
    return {
      overall: (creativityPotential + motorPotential + emotionalPotential) / 3,
      creativity: creativityPotential,
      motor: motorPotential,
      emotional: emotionalPotential,
      priorityArea: this.identifyPriorityArea(creativityPotential, motorPotential, emotionalPotential)
    };
  }
  calculateOverallProfile(creativityMetrics2, motorMetrics, emotionalMetrics) {
    const creativityScore = creativityMetrics2.originalityScore || 0;
    const motorScore = motorMetrics.movementPrecision || 0;
    const emotionalScore = emotionalMetrics.emotionalIntensity || 0;
    const averageScore = (creativityScore + motorScore + emotionalScore) / 3;
    return {
      score: averageScore,
      level: this.categorizeLevel(averageScore),
      strengths: this.identifyStrengths(creativityScore, motorScore, emotionalScore),
      weaknesses: this.identifyWeaknesses(creativityScore, motorScore, emotionalScore)
    };
  }
  categorizeLevel(score) {
    if (score >= 0.8) return "excellent";
    if (score >= 0.6) return "good";
    if (score >= 0.4) return "fair";
    return "needs_improvement";
  }
  identifyPriorityArea(creativityPotential, motorPotential, emotionalPotential) {
    const potentials = {
      creativity: creativityPotential,
      motor: motorPotential,
      emotional: emotionalPotential
    };
    return Object.keys(potentials).reduce(
      (a, b) => potentials[a] > potentials[b] ? a : b
    );
  }
  identifyStrengths(creativityScore, motorScore, emotionalScore) {
    const scores = {
      creativity: creativityScore,
      motor: motorScore,
      emotional: emotionalScore
    };
    return Object.keys(scores).filter((key) => scores[key] >= 0.7);
  }
  identifyWeaknesses(creativityScore, motorScore, emotionalScore) {
    const scores = {
      creativity: creativityScore,
      motor: motorScore,
      emotional: emotionalScore
    };
    return Object.keys(scores).filter((key) => scores[key] < 0.5);
  }
}
new CreativePaintingCollectorsHub();
class CreativePaintingProcessors extends IGameProcessor {
  constructor(config = {}) {
    const defaultConfig = {
      category: "creative-expression",
      therapeuticFocus: ["creativity", "motor_skills", "artistic_expression"],
      cognitiveAreas: ["creativity", "motor_planning", "visual_processing"],
      thresholds: {
        accuracy: 50,
        // Mais flexível para criatividade
        responseTime: 1e4,
        engagement: 80
      },
      ...config
    };
    super(defaultConfig);
    this.gameType = "CreativePainting";
  }
  /**
   * Processa dados de jogos de pintura criativa usando coletores especializados
   * @param {Object} gameData - Dados brutos do jogo
   * @param {Object} collectorsHub - Hub de coletores do CreativePainting
   * @returns {Object} Dados processados com métricas específicas
   */
  async processGameData(gameData, collectorsHub = null) {
    try {
      this.logger?.info("🎮 Processando dados CreativePainting", {
        sessionId: gameData.sessionId,
        userId: gameData.userId,
        collectorsActive: collectorsHub ? Object.keys(collectorsHub.collectors || {}).length : 0
      });
      const metrics = await this.processCreativePaintingMetrics(gameData, gameData);
      const therapeuticAnalysis = this.generateTherapeuticAnalysis(metrics, gameData);
      const processedMetrics = this.processMetricsForDatabase(metrics, gameData);
      return {
        success: true,
        gameType: this.gameType,
        metrics,
        therapeuticAnalysis,
        processedMetrics,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    } catch (error) {
      this.logger?.error("❌ Erro ao processar dados CreativePainting:", error);
      return {
        success: false,
        gameType: this.gameType,
        error: error.message,
        fallbackAnalysis: this.generateFallbackTherapeuticAnalysis(gameData),
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    }
  }
  /**
   * Integra dados dos coletores especializados com métricas básicas
   */
  integrateCollectorData(basicMetrics, collectorAnalysis) {
    if (!collectorAnalysis) return basicMetrics;
    return {
      ...basicMetrics,
      // Dados dos coletores especializados
      creativityAnalysis: collectorAnalysis.creativityAnalysis || {},
      motorSkills: collectorAnalysis.motorSkills || {},
      emotionalExpression: collectorAnalysis.emotionalExpression || {},
      // Perfil cognitivo integrado
      cognitiveProfile: collectorAnalysis.cognitiveProfile || basicMetrics.cognitive,
      // Performance geral
      overallPerformance: collectorAnalysis.overallPerformance || {},
      artisticPotential: collectorAnalysis.artisticPotential || {}
    };
  }
  /**
   * Gera recomendações integradas baseadas em todos os dados
   */
  generateIntegratedRecommendations(metrics, collectorAnalysis) {
    const recommendations = [];
    if (collectorAnalysis?.recommendations) {
      recommendations.push(...collectorAnalysis.recommendations);
    }
    const basicRecommendations = this.generateRecommendations(metrics.creativity, metrics.motorCoordination, metrics.visualExpression);
    recommendations.push(...basicRecommendations);
    if (metrics.creativityAnalysis?.originalityScore < 0.6) {
      recommendations.push({
        type: "creativity_enhancement",
        priority: "high",
        description: "Exercícios para desenvolver originalidade artística",
        targetArea: "creativity"
      });
    }
    if (metrics.motorSkills?.movementPrecision < 0.6) {
      recommendations.push({
        type: "motor_skills_improvement",
        priority: "high",
        description: "Exercícios de coordenação motora fina",
        targetArea: "motor_skills"
      });
    }
    if (metrics.emotionalExpression?.expressionConfidence < 0.6) {
      recommendations.push({
        type: "emotional_expression_support",
        priority: "medium",
        description: "Atividades para fortalecer a expressão emocional",
        targetArea: "emotional_expression"
      });
    }
    return recommendations;
  }
  /**
   * Analisa métricas de criatividade
   */
  analyzeCreativityMetrics(interactions) {
    try {
      const paintingActions = interactions.filter(
        (i) => i.type === "brush_stroke" || i.type === "color_selection" || i.type === "tool_selection"
      );
      if (paintingActions.length === 0) {
        return this.getDefaultCreativityMetrics();
      }
      const colorDiversity = this.analyzeColorDiversity(paintingActions);
      const compositionComplexity = this.analyzeCompositionComplexity(paintingActions);
      const originalityScore = this.calculateOriginalityScore(paintingActions);
      const creativeFluency = this.analyzeCreativeFluency(paintingActions);
      return {
        colorDiversity,
        compositionComplexity,
        originalityScore,
        creativeFluency,
        expressiveness: this.calculateExpressiveness(paintingActions),
        innovationIndex: this.calculateInnovationIndex(paintingActions)
      };
    } catch (error) {
      this.logger.error("Erro na análise de métricas de criatividade", { error: error.message });
      return this.getDefaultCreativityMetrics();
    }
  }
  /**
   * Analisa coordenação motora
   */
  analyzeMotorCoordination(interactions) {
    try {
      const motorActions = interactions.filter(
        (i) => i.type === "brush_stroke" || i.type === "drawing_movement"
      );
      if (motorActions.length === 0) {
        return this.getDefaultMotorMetrics();
      }
      const movementPrecision = this.analyzeMovementPrecision(motorActions);
      const handStability = this.analyzeHandStability(motorActions);
      const bilateralCoordination = this.analyzeBilateralCoordination(motorActions);
      const speedControl = this.analyzeSpeedControl(motorActions);
      return {
        movementPrecision,
        handStability,
        bilateralCoordination,
        speedControl,
        finePrecision: this.calculateFinePrecision(motorActions),
        motorPlanning: this.assessMotorPlanning(motorActions)
      };
    } catch (error) {
      this.logger.error("Erro na análise de coordenação motora", { error: error.message });
      return this.getDefaultMotorMetrics();
    }
  }
  /**
   * Analisa expressão visual
   */
  analyzeVisualExpression(interactions) {
    try {
      const visualActions = interactions.filter(
        (i) => i.type === "color_selection" || i.type === "brush_stroke" || i.type === "shape_creation"
      );
      if (visualActions.length === 0) {
        return this.getDefaultVisualMetrics();
      }
      const colorUsage = this.analyzeColorUsage(visualActions);
      const spatialComposition = this.analyzeSpatialComposition(visualActions);
      const visualElements = this.analyzeVisualElements(visualActions);
      return {
        colorUsage,
        spatialComposition,
        visualElements,
        aestheticAppeal: this.calculateAestheticAppeal(visualActions),
        visualBalance: this.assessVisualBalance(visualActions)
      };
    } catch (error) {
      this.logger.error("Erro na análise de expressão visual", { error: error.message });
      return this.getDefaultVisualMetrics();
    }
  }
  /**
   * Analisa engajamento cognitivo
   */
  analyzeCognitiveEngagement(interactions) {
    try {
      const cognitiveIndicators = interactions.filter(
        (i) => i.type === "planning_pause" || i.type === "reflection_moment" || i.type === "decision_making"
      );
      const planningMetrics = this.analyzePlanningBehavior(interactions);
      const persistenceMetrics = this.analyzePersistence(interactions);
      const attentionMetrics2 = this.analyzeAttentionSpan(interactions);
      return {
        planning: planningMetrics,
        persistence: persistenceMetrics,
        attention: attentionMetrics2,
        problemSolving: this.assessProblemSolving(interactions),
        metacognition: this.assessMetacognition(interactions)
      };
    } catch (error) {
      this.logger.error("Erro na análise de engajamento cognitivo", { error: error.message });
      return {
        planning: 0.5,
        persistence: 0.5,
        attention: 0.5,
        problemSolving: 0.5,
        metacognition: 0.5
      };
    }
  }
  /**
   * Analisa características da obra de arte
   */
  analyzeArtworkCharacteristics(artwork) {
    try {
      if (!artwork || Object.keys(artwork).length === 0) {
        return this.getDefaultArtworkMetrics();
      }
      const visualComplexity = this.calculateVisualComplexity(artwork);
      const balance = this.calculateArtworkBalance(artwork);
      const colorHarmony = this.calculateColorHarmony(artwork);
      return {
        visualComplexity,
        balance,
        colorHarmony,
        uniqueness: this.calculateUniqueness(artwork),
        completeness: this.assessCompleteness(artwork)
      };
    } catch (error) {
      this.logger.error("Erro na análise de características da obra", { error: error.message });
      return this.getDefaultArtworkMetrics();
    }
  }
  /**
   * Gera recomendações baseadas nas métricas
   */
  generateRecommendations(processorResults, collectorsResults) {
    const recommendations = [];
    if (processorResults.accuracy < 70) {
      recommendations.push("Exercícios de reforço recomendados");
    }
    return recommendations;
  }
  assessDataCompleteness(processorResults, collectorsResults) {
    let score = 0;
    if (processorResults.accuracy !== void 0) score += 25;
    if (processorResults.averageResponseTime !== void 0) score += 25;
    if (Object.keys(collectorsResults.collectors || {}).length > 0) score += 50;
    return score;
  }
  calculateConfidenceScore(processorResults, collectorsResults) {
    const dataQuality = this.assessDataCompleteness(processorResults, collectorsResults);
    const collectorCount = Object.keys(collectorsResults.collectors || {}).length;
    return Math.min(100, dataQuality + collectorCount * 5);
  }
  /**
   * Gera análise terapêutica completa
   * @param {Object} metrics - Métricas processadas
   * @param {Object} gameData - Dados do jogo
   * @returns {Object} Análise terapêutica
   */
  generateTherapeuticAnalysis(metrics, gameData) {
    try {
      const analysis = {
        // Análise comportamental
        behavioral: {
          engagement: this.calculateEngagementScore(gameData),
          persistence: this.calculatePersistenceScore(gameData),
          adaptability: this.calculateAdaptabilityScore(gameData),
          frustrationTolerance: this.calculateFrustrationTolerance(gameData),
          socialInteraction: this.calculateSocialInteractionScore(gameData)
        },
        // Análise cognitiva
        cognitive: {
          attention: this.calculateAttentionScore(gameData),
          memory: this.calculateMemoryScore(gameData),
          processingSpeed: this.calculateProcessingSpeedScore(gameData),
          executiveFunction: this.calculateExecutiveFunctionScore(gameData),
          visualProcessing: this.calculateVisualProcessingScore(gameData)
        },
        // Análise sensorial
        sensory: {
          visualPerception: this.calculateVisualPerceptionScore(gameData),
          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),
          tactileProcessing: this.calculateTactileProcessingScore(gameData),
          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)
        },
        // Análise motora
        motor: {
          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),
          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),
          coordination: this.calculateCoordinationScore(gameData),
          motorPlanning: this.calculateMotorPlanningScore(gameData)
        },
        // Recomendações terapêuticas
        recommendations: this.generateTherapeuticRecommendations(metrics, gameData),
        // Indicadores de progresso
        progressIndicators: this.generateProgressIndicators(metrics, gameData),
        // Insights específicos do jogo
        gameSpecificInsights: this.generateGameSpecificInsights(metrics, gameData),
        // Metadados
        metadata: {
          analysisTimestamp: (/* @__PURE__ */ new Date()).toISOString(),
          gameType: this.gameType,
          analysisVersion: "3.0.0",
          confidenceScore: this.calculateAnalysisConfidenceScore(metrics, gameData)
        }
      };
      return analysis;
    } catch (error) {
      this.logger?.error("❌ Erro ao gerar análise terapêutica:", error);
      return this.generateFallbackTherapeuticAnalysis(gameData);
    }
  }
  /**
   * Métodos de cálculo de scores terapêuticos
   */
  calculateEngagementScore(gameData) {
    const interactions = gameData.interactions || [];
    const totalTime = gameData.totalTime || 1e3;
    const completionRate = gameData.completionRate || 0;
    let score = 50;
    if (interactions.length > 0) {
      score += Math.min(30, interactions.length * 2);
    }
    if (totalTime > 3e4) {
      score += 10;
    }
    score += completionRate * 10;
    return Math.max(0, Math.min(100, score));
  }
  calculatePersistenceScore(gameData) {
    const attempts = gameData.attempts || 1;
    const errors = gameData.errors || 0;
    const completion = gameData.completion || 0;
    let score = 50;
    if (attempts > 1 && completion > 0.5) {
      score += 20;
    }
    if (errors > 0 && completion > 0.8) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateAdaptabilityScore(gameData) {
    const difficultyChanges = gameData.difficultyChanges || 0;
    const adaptationSuccess = gameData.adaptationSuccess || 0;
    let score = 50;
    if (difficultyChanges > 0) {
      score += adaptationSuccess * 20;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateFrustrationTolerance(gameData) {
    const errors = gameData.errors || 0;
    const quitEarly = gameData.quitEarly || false;
    const completion = gameData.completion || 0;
    let score = 70;
    if (errors > 3 && !quitEarly) {
      score += 15;
    }
    if (completion > 0.8) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateSocialInteractionScore(gameData) {
    const engagement = this.calculateEngagementScore(gameData);
    return Math.max(30, Math.min(80, engagement * 0.8));
  }
  calculateAttentionScore(gameData) {
    const focusTime = gameData.focusTime || 0;
    const distractions = gameData.distractions || 0;
    const responseTime = gameData.averageResponseTime || 3e3;
    let score = 50;
    if (focusTime > 6e4) {
      score += 20;
    }
    if (distractions < 2) {
      score += 15;
    }
    if (responseTime < 2e3) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateMemoryScore(gameData) {
    const accuracy = gameData.accuracy || 0;
    const patterns = gameData.patterns || [];
    let score = 50;
    if (accuracy > 70) {
      score += 25;
    }
    if (patterns.length > 3) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateProcessingSpeedScore(gameData) {
    const responseTime = gameData.averageResponseTime || 3e3;
    const accuracy = gameData.accuracy || 0;
    let score = 50;
    if (responseTime < 1500 && accuracy > 60) {
      score += 30;
    } else if (responseTime < 2500) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateExecutiveFunctionScore(gameData) {
    const planningEvidence = gameData.planningEvidence || 0;
    const inhibitionControl = gameData.inhibitionControl || 0;
    const workingMemory = gameData.workingMemory || 0;
    const score = (planningEvidence + inhibitionControl + workingMemory) / 3;
    return Math.max(0, Math.min(100, score));
  }
  calculateVisualProcessingScore(gameData) {
    const visualTasks = gameData.visualTasks || 0;
    const visualAccuracy = gameData.visualAccuracy || 0;
    let score = 50;
    if (visualTasks > 5 && visualAccuracy > 70) {
      score += 25;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateVisualPerceptionScore(gameData) {
    return this.calculateVisualProcessingScore(gameData);
  }
  calculateAuditoryProcessingScore(gameData) {
    const auditoryTasks = gameData.auditoryTasks || 0;
    const auditoryAccuracy = gameData.auditoryAccuracy || 50;
    let score = 50;
    if (auditoryTasks > 0) {
      score = auditoryAccuracy;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateTactileProcessingScore(gameData) {
    const touchInteractions = gameData.touchInteractions || 0;
    const touchAccuracy = gameData.touchAccuracy || 50;
    let score = 50;
    if (touchInteractions > 3) {
      score = touchAccuracy;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateSensoryIntegrationScore(gameData) {
    const visual = this.calculateVisualPerceptionScore(gameData);
    const auditory = this.calculateAuditoryProcessingScore(gameData);
    const tactile = this.calculateTactileProcessingScore(gameData);
    return (visual + auditory + tactile) / 3;
  }
  calculateFineMotorSkillsScore(gameData) {
    const precision = gameData.precision || 50;
    const motorControl = gameData.motorControl || 50;
    return (precision + motorControl) / 2;
  }
  calculateGrossMotorSkillsScore(gameData) {
    const movements = gameData.movements || 0;
    const coordination = gameData.coordination || 50;
    let score = 50;
    if (movements > 10) {
      score = coordination;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateCoordinationScore(gameData) {
    const eyeHandCoordination = gameData.eyeHandCoordination || 50;
    const bilateralCoordination = gameData.bilateralCoordination || 50;
    return (eyeHandCoordination + bilateralCoordination) / 2;
  }
  calculateMotorPlanningScore(gameData) {
    const planningSteps = gameData.planningSteps || 0;
    const executionSuccess = gameData.executionSuccess || 0;
    let score = 50;
    if (planningSteps > 0) {
      score = executionSuccess;
    }
    return Math.max(0, Math.min(100, score));
  }
  generateTherapeuticRecommendations(metrics, gameData) {
    const recommendations = [];
    const engagement = this.calculateEngagementScore(gameData);
    if (engagement < 50) {
      recommendations.push({
        category: "engagement",
        priority: "high",
        recommendation: "Implementar estratégias de motivação e gamificação",
        rationale: "Baixo engajamento detectado"
      });
    }
    const attention = this.calculateAttentionScore(gameData);
    if (attention < 50) {
      recommendations.push({
        category: "attention",
        priority: "medium",
        recommendation: "Exercícios de foco e concentração",
        rationale: "Dificuldades atencionais identificadas"
      });
    }
    const processing = this.calculateProcessingSpeedScore(gameData);
    if (processing < 50) {
      recommendations.push({
        category: "processing",
        priority: "medium",
        recommendation: "Atividades para melhorar velocidade de processamento",
        rationale: "Processamento lento identificado"
      });
    }
    return recommendations;
  }
  generateProgressIndicators(metrics, gameData) {
    return {
      overallProgress: this.calculateOverallProgress(gameData),
      strengthAreas: this.identifyStrengthAreas(gameData),
      challengeAreas: this.identifyChallengeAreas(gameData),
      developmentGoals: this.generateDevelopmentGoals(gameData),
      milestones: this.generateMilestones(gameData)
    };
  }
  generateGameSpecificInsights(metrics, gameData) {
    return {
      gameType: this.gameType,
      specificMetrics: metrics,
      gamePerformance: this.calculateGamePerformance(gameData),
      adaptationNeeds: this.identifyAdaptationNeeds(gameData)
    };
  }
  calculateAnalysisConfidenceScore(metrics, gameData) {
    let confidence = 50;
    const dataPoints = Object.keys(gameData).length;
    if (dataPoints > 10) confidence += 20;
    else if (dataPoints > 5) confidence += 10;
    const metricsCount = Object.keys(metrics).length;
    if (metricsCount > 5) confidence += 20;
    else if (metricsCount > 3) confidence += 10;
    const sessionTime = gameData.totalTime || 0;
    if (sessionTime > 6e4) confidence += 10;
    return Math.max(0, Math.min(100, confidence));
  }
  generateFallbackTherapeuticAnalysis(gameData) {
    return {
      behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50, socialInteraction: 50 },
      cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50, visualProcessing: 50 },
      sensory: { visualPerception: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },
      motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },
      recommendations: [],
      progressIndicators: { overallProgress: 50, strengthAreas: [], challengeAreas: [], developmentGoals: [], milestones: [] },
      gameSpecificInsights: { gameType: this.gameType, specificMetrics: {}, gamePerformance: 50, adaptationNeeds: [] },
      metadata: { analysisTimestamp: (/* @__PURE__ */ new Date()).toISOString(), gameType: this.gameType, analysisVersion: "3.0.0", confidenceScore: 30 }
    };
  }
  calculateOverallProgress(gameData) {
    const accuracy = gameData.accuracy || 0;
    const completion = gameData.completion || 0;
    const engagement = this.calculateEngagementScore(gameData);
    return (accuracy + completion + engagement) / 3;
  }
  identifyStrengthAreas(gameData) {
    const strengths = [];
    if (gameData.accuracy > 80) strengths.push("Precisão");
    if (gameData.averageResponseTime < 2e3) strengths.push("Velocidade de resposta");
    if (this.calculateEngagementScore(gameData) > 70) strengths.push("Engajamento");
    return strengths;
  }
  identifyChallengeAreas(gameData) {
    const challenges = [];
    if (gameData.accuracy < 50) challenges.push("Precisão");
    if (gameData.averageResponseTime > 4e3) challenges.push("Velocidade de resposta");
    if (this.calculateEngagementScore(gameData) < 40) challenges.push("Engajamento");
    return challenges;
  }
  generateDevelopmentGoals(gameData) {
    const goals = [];
    if (gameData.accuracy < 70) {
      goals.push("Melhorar precisão para 70%+");
    }
    if (gameData.averageResponseTime > 3e3) {
      goals.push("Reduzir tempo de resposta para menos de 3 segundos");
    }
    return goals;
  }
  generateMilestones(gameData) {
    return [
      { milestone: "Primeira sessão completa", achieved: gameData.completion > 0.8 },
      { milestone: "Precisão acima de 50%", achieved: gameData.accuracy > 50 },
      { milestone: "Engajamento sustentado", achieved: this.calculateEngagementScore(gameData) > 60 }
    ];
  }
  calculateGamePerformance(gameData) {
    const accuracy = gameData.accuracy || 0;
    const completion = gameData.completion || 0;
    const efficiency = gameData.efficiency || 0;
    return (accuracy + completion + efficiency) / 3;
  }
  identifyAdaptationNeeds(gameData) {
    const needs = [];
    if (gameData.accuracy < 40) {
      needs.push("Reduzir dificuldade");
    }
    if (gameData.averageResponseTime > 5e3) {
      needs.push("Aumentar tempo limite");
    }
    if (this.calculateEngagementScore(gameData) < 30) {
      needs.push("Aumentar elementos motivacionais");
    }
    return needs;
  }
  /**
   * Processa métricas para estrutura padronizada do banco de dados
   * @param {Object} metrics - Métricas processadas
   * @param {Object} gameData - Dados do jogo
   * @returns {Object} Métricas processadas para banco
   */
  processMetricsForDatabase(metrics, gameData) {
    try {
      return {
        // Métricas básicas
        basic: {
          accuracy: gameData.accuracy || 0,
          responseTime: gameData.averageResponseTime || 0,
          completion: gameData.completion || 0,
          score: gameData.score || 0,
          duration: gameData.totalTime || 0,
          attempts: gameData.attempts || 1,
          errors: gameData.errors || 0
        },
        // Métricas cognitivas
        cognitive: {
          attention: this.calculateAttentionScore(gameData),
          memory: this.calculateMemoryScore(gameData),
          processingSpeed: this.calculateProcessingSpeedScore(gameData),
          executiveFunction: this.calculateExecutiveFunctionScore(gameData)
        },
        // Métricas comportamentais
        behavioral: {
          engagement: this.calculateEngagementScore(gameData),
          persistence: this.calculatePersistenceScore(gameData),
          adaptability: this.calculateAdaptabilityScore(gameData),
          frustrationTolerance: this.calculateFrustrationTolerance(gameData)
        },
        // Métricas sensoriais
        sensory: {
          visualProcessing: this.calculateVisualProcessingScore(gameData),
          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),
          tactileProcessing: this.calculateTactileProcessingScore(gameData),
          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)
        },
        // Métricas motoras
        motor: {
          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),
          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),
          coordination: this.calculateCoordinationScore(gameData),
          motorPlanning: this.calculateMotorPlanningScore(gameData)
        },
        // Métricas específicas do jogo
        gameSpecific: metrics,
        // Metadados
        metadata: {
          gameType: this.gameType,
          processingTimestamp: (/* @__PURE__ */ new Date()).toISOString(),
          version: "3.0.0"
        }
      };
    } catch (error) {
      this.logger?.error("❌ Erro ao processar métricas para banco:", error);
      return {
        basic: { accuracy: 0, responseTime: 0, completion: 0, score: 0, duration: 0, attempts: 1, errors: 0 },
        cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50 },
        behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50 },
        sensory: { visualProcessing: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },
        motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },
        gameSpecific: metrics,
        metadata: { gameType: this.gameType, processingTimestamp: (/* @__PURE__ */ new Date()).toISOString(), version: "3.0.0" }
      };
    }
  }
  /**
   * Processa métricas específicas do CreativePainting
   * @param {Object} gameData - Dados do jogo CreativePainting
   * @param {Object} sessionData - Dados da sessão
   * @returns {Object} Métricas processadas
   */
  async processCreativePaintingMetrics(gameData, sessionData) {
    try {
      this.logger?.info("🎨 Processando métricas CreativePainting...", {
        sessionId: sessionData.sessionId
      });
      const metrics = {
        // Métricas de expressão criativa
        creativeExpression: this.analyzeCreativePaintingPrimary(gameData),
        // Análise de habilidades motoras
        motorSkills: this.analyzeCreativePaintingSecondary(gameData),
        // Processamento visual-espacial
        visualSpatial: this.analyzeCreativePaintingTertiary(gameData),
        // Padrões criativos
        creativePatterns: this.analyzeCreativePaintingPatterns(gameData),
        // Análise comportamental específica
        creativeBehavior: this.analyzeCreativePaintingBehavior(gameData),
        // Indicadores terapêuticos
        therapeuticIndicators: this.generateCreativePaintingTherapeuticIndicators(gameData),
        // Recomendações específicas
        recommendations: this.generateCreativePaintingRecommendations(gameData)
      };
      this.logger?.info("✅ Métricas CreativePainting processadas", {
        creativity: metrics.creativeExpression.creativity,
        motorControl: metrics.motorSkills.fineMotorControl
      });
      return metrics;
    } catch (error) {
      this.logger?.error("❌ Erro ao processar métricas CreativePainting:", error);
      throw error;
    }
  }
  /**
   * Gera indicadores terapêuticos específicos para CreativePainting
   */
  generateCreativePaintingTherapeuticIndicators(gameData) {
    const { interactions = [], accuracy = 0 } = gameData;
    return {
      cognitiveLoad: this.assessCognitiveLoad(gameData),
      therapeuticGoals: this.identifyCreativePaintingTherapeuticGoals(gameData),
      interventionNeeds: this.identifyCreativePaintingInterventionNeeds(gameData),
      progressMarkers: this.generateCreativePaintingProgressMarkers(gameData)
    };
  }
  /**
   * Gera recomendações específicas para CreativePainting
   */
  generateCreativePaintingRecommendations(gameData) {
    const recommendations = [];
    const creativity = gameData.creativity || 50;
    if (creativity < 60) {
      recommendations.push({
        type: "creative_expression_support",
        priority: "high",
        description: "Exercícios de expressão criativa livre"
      });
    }
    if (creativity < 80) {
      recommendations.push({
        type: "motor_skills_training",
        priority: "medium",
        description: "Treinamento de habilidades motoras finas"
      });
    }
    return recommendations;
  }
  /**
   * Identifica objetivos terapêuticos específicos
   */
  identifyCreativePaintingTherapeuticGoals(gameData) {
    const { interactions = [] } = gameData;
    const goals = [];
    goals.push("Desenvolver expressão criativa");
    goals.push("Melhorar coordenação motora");
    return goals;
  }
  /**
   * Identifica necessidades de intervenção específicas
   */
  identifyCreativePaintingInterventionNeeds(gameData) {
    const needs = [];
    const creativity = gameData.creativity || 50;
    if (creativity < 40) {
      needs.push("Intervenção intensiva em expressão criativa");
    }
    return needs;
  }
  /**
   * Gera marcadores de progresso específicos
   */
  generateCreativePaintingProgressMarkers(gameData) {
    const { interactions = [] } = gameData;
    return {
      creativityTrend: "stable",
      motorImprovement: 0,
      expressionDiversity: 50,
      engagementLevel: 75
    };
  }
  /**
   * Avalia carga cognitiva
   */
  assessCognitiveLoad(gameData) {
    return "low";
  }
  /**
   * Análise primária do CreativePainting
   */
  analyzeCreativePaintingPrimary(gameData) {
    const { interactions = [] } = gameData;
    return {
      creativity: this.calculateCreativity(interactions),
      accuracy: gameData.accuracy || 0,
      averageResponseTime: this.calculateAverageResponseTime(interactions)
    };
  }
  /**
   * Análise secundária do CreativePainting
   */
  analyzeCreativePaintingSecondary(gameData) {
    const { interactions = [] } = gameData;
    return {
      fineMotorControl: this.calculateFineMotorControl(interactions),
      motorPlanning: this.calculateMotorPlanning(interactions),
      coordination: this.calculateCoordination(interactions)
    };
  }
  /**
   * Análise terciária do CreativePainting
   */
  analyzeCreativePaintingTertiary(gameData) {
    const { interactions = [] } = gameData;
    return {
      visualSpatial: this.calculateVisualSpatial(interactions),
      colorPerception: this.calculateColorPerception(interactions),
      spatialAwareness: this.calculateSpatialAwareness(interactions)
    };
  }
  /**
   * Análise de padrões do CreativePainting
   */
  analyzeCreativePaintingPatterns(gameData) {
    const { interactions = [] } = gameData;
    return {
      totalPatterns: interactions.length,
      creativeVariation: this.calculateCreativeVariation(interactions),
      expressionDiversity: this.calculateExpressionDiversity(interactions)
    };
  }
  /**
   * Análise comportamental do CreativePainting
   */
  analyzeCreativePaintingBehavior(gameData) {
    const { interactions = [] } = gameData;
    return {
      engagement: this.calculateEngagement(interactions),
      persistence: this.calculatePersistence(interactions),
      exploration: this.calculateExploration(interactions)
    };
  }
  /**
   * Calcula criatividade
   */
  calculateCreativity(interactions) {
    if (interactions.length === 0) return 50;
    const uniqueActions = new Set(interactions.map((i) => i.action || "paint"));
    return Math.min(100, uniqueActions.size * 20);
  }
  /**
   * Calcula controle motor fino
   */
  calculateFineMotorControl(interactions) {
    if (interactions.length === 0) return 50;
    const precisionScore = interactions.filter((i) => i.precise).length;
    return precisionScore / interactions.length * 100;
  }
  /**
   * Calcula planejamento motor
   */
  calculateMotorPlanning(interactions) {
    if (interactions.length === 0) return 50;
    return 60;
  }
  /**
   * Calcula coordenação
   */
  calculateCoordination(interactions) {
    if (interactions.length === 0) return 50;
    return 65;
  }
  /**
   * Calcula processamento visual-espacial
   */
  calculateVisualSpatial(interactions) {
    if (interactions.length === 0) return 50;
    return 60;
  }
  /**
   * Calcula percepção de cores
   */
  calculateColorPerception(interactions) {
    if (interactions.length === 0) return 50;
    const colorActions = interactions.filter((i) => i.color);
    return colorActions.length > 0 ? 70 : 50;
  }
  /**
   * Calcula consciência espacial
   */
  calculateSpatialAwareness(interactions) {
    if (interactions.length === 0) return 50;
    return 55;
  }
  /**
   * Calcula variação criativa
   */
  calculateCreativeVariation(interactions) {
    if (interactions.length === 0) return 0;
    const uniqueElements = new Set(interactions.map((i) => `${i.x}-${i.y}-${i.color}`));
    return uniqueElements.size / interactions.length * 100;
  }
  /**
   * Calcula diversidade de expressão
   */
  calculateExpressionDiversity(interactions) {
    if (interactions.length === 0) return 0;
    const tools = new Set(interactions.map((i) => i.tool || "brush"));
    return Math.min(100, tools.size * 25);
  }
  /**
   * Calcula exploração
   */
  calculateExploration(interactions) {
    if (interactions.length === 0) return 50;
    const positions = interactions.map((i) => `${Math.floor((i.x || 0) / 50)}-${Math.floor((i.y || 0) / 50)}`);
    const uniquePositions = new Set(positions);
    return Math.min(100, uniquePositions.size * 10);
  }
  /**
   * Calcula engajamento
   */
  calculateEngagement(interactions) {
    if (interactions.length === 0) return 50;
    return Math.min(100, interactions.length * 5);
  }
  /**
   * Calcula persistência
   */
  calculatePersistence(interactions) {
    if (interactions.length === 0) return 50;
    return Math.min(100, interactions.length * 3);
  }
  /**
   * Calcula tempo médio de resposta
   */
  calculateAverageResponseTime(interactions) {
    if (interactions.length === 0) return 0;
    const times = interactions.map((i) => i.responseTime || 0);
    return times.reduce((sum, t) => sum + t, 0) / times.length;
  }
}
const PAINTING_CONFIG = {
  difficulties: {
    easy: {
      name: "Fácil",
      description: "Pincéis grandes para iniciantes",
      brushSizes: [8, 12, 16],
      defaultBrush: 12
    },
    medium: {
      name: "Médio",
      description: "Pincéis variados para desafio equilibrado",
      brushSizes: [4, 8, 12],
      defaultBrush: 8
    },
    hard: {
      name: "Avançado",
      description: "Pincéis precisos para especialistas",
      brushSizes: [2, 4, 6],
      defaultBrush: 4
    }
  }
};
const creativePaintingGame = "_creativePaintingGame_1fy6o_41";
const gameHeader = "_gameHeader_1fy6o_85";
const gameTitle = "_gameTitle_1fy6o_101";
const gameStats = "_gameStats_1fy6o_133";
const statValue = "_statValue_1fy6o_163";
const statLabel = "_statLabel_1fy6o_175";
const canvasContainer = "_canvasContainer_1fy6o_249";
const colorPalette = "_colorPalette_1fy6o_287";
const colorGrid = "_colorGrid_1fy6o_319";
const active = "_active_1fy6o_363";
const gameControls = "_gameControls_1fy6o_513";
const controlButton = "_controlButton_1fy6o_529";
const therapeuticActivity = "_therapeuticActivity_1fy6o_1111";
const activityHeader = "_activityHeader_1fy6o_1119";
const therapeuticCanvas = "_therapeuticCanvas_1fy6o_1159";
const creativityMetrics = "_creativityMetrics_1fy6o_1297";
const attentionMetrics = "_attentionMetrics_1fy6o_1299";
const metricCard = "_metricCard_1fy6o_1313";
const creativeProfilingArea = "_creativeProfilingArea_1fy6o_1481";
const creativeCanvas = "_creativeCanvas_1fy6o_1493";
const attentionMeasurementArea = "_attentionMeasurementArea_1fy6o_1505";
const focusTask = "_focusTask_1fy6o_1517";
const taskInstruction = "_taskInstruction_1fy6o_1531";
const paintingActivity = "_paintingActivity_1fy6o_1563";
const freePaintingArea = "_freePaintingArea_1fy6o_1573";
const colorBtn = "_colorBtn_1fy6o_1625";
const brushControls = "_brushControls_1fy6o_1665";
const brushSizeControl = "_brushSizeControl_1fy6o_1693";
const brushSlider = "_brushSlider_1fy6o_1715";
const paintingCanvas = "_paintingCanvas_1fy6o_1753";
const advancedCanvas = "_advancedCanvas_1fy6o_1767";
const actionControls = "_actionControls_1fy6o_1781";
const clearBtn = "_clearBtn_1fy6o_1793";
const saveBtn = "_saveBtn_1fy6o_1793";
const undoBtn = "_undoBtn_1fy6o_1793";
const redoBtn = "_redoBtn_1fy6o_1793";
const assistedPaintingArea = "_assistedPaintingArea_1fy6o_1859";
const templateSelector = "_templateSelector_1fy6o_1871";
const templateGrid = "_templateGrid_1fy6o_1899";
const templateBtn = "_templateBtn_1fy6o_1911";
const templateIcon = "_templateIcon_1fy6o_1953";
const templateName = "_templateName_1fy6o_1963";
const assistedCanvas = "_assistedCanvas_1fy6o_1975";
const clickableAreas = "_clickableAreas_1fy6o_1985";
const clickableArea = "_clickableArea_1fy6o_1985";
const progressSection = "_progressSection_1fy6o_2021";
const progressBar = "_progressBar_1fy6o_2049";
const progressFill = "_progressFill_1fy6o_2065";
const canvasPaintingArea = "_canvasPaintingArea_1fy6o_2079";
const toolBar = "_toolBar_1fy6o_2091";
const toolGrid = "_toolGrid_1fy6o_2119";
const toolBtn = "_toolBtn_1fy6o_2131";
const advancedControls = "_advancedControls_1fy6o_2181";
const controlGroup = "_controlGroup_1fy6o_2193";
const brushTypeSelect = "_brushTypeSelect_1fy6o_2215";
const gameContent = "_gameContent_1fy6o_2331";
const headerTtsButton = "_headerTtsButton_1fy6o_2385";
const ttsActive = "_ttsActive_1fy6o_2421";
const statCard = "_statCard_1fy6o_2449";
const activityMenu = "_activityMenu_1fy6o_2513";
const activityButton = "_activityButton_1fy6o_2531";
const controlsGroup = "_controlsGroup_1fy6o_2607";
const styles = {
  creativePaintingGame,
  gameHeader,
  gameTitle,
  gameStats,
  statValue,
  statLabel,
  canvasContainer,
  colorPalette,
  colorGrid,
  active,
  gameControls,
  controlButton,
  therapeuticActivity,
  activityHeader,
  therapeuticCanvas,
  creativityMetrics,
  attentionMetrics,
  metricCard,
  creativeProfilingArea,
  creativeCanvas,
  attentionMeasurementArea,
  focusTask,
  taskInstruction,
  paintingActivity,
  freePaintingArea,
  colorBtn,
  brushControls,
  brushSizeControl,
  brushSlider,
  paintingCanvas,
  advancedCanvas,
  actionControls,
  clearBtn,
  saveBtn,
  undoBtn,
  redoBtn,
  assistedPaintingArea,
  templateSelector,
  templateGrid,
  templateBtn,
  templateIcon,
  templateName,
  assistedCanvas,
  clickableAreas,
  clickableArea,
  progressSection,
  progressBar,
  progressFill,
  canvasPaintingArea,
  toolBar,
  toolGrid,
  toolBtn,
  advancedControls,
  controlGroup,
  brushTypeSelect,
  gameContent,
  headerTtsButton,
  ttsActive,
  statCard,
  activityMenu,
  activityButton,
  controlsGroup
};
var _jsxFileName = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\games\\CreativePainting\\CreativePaintingGame.jsx";
const ACTIVITY_TYPES = {
  FREE_PAINTING: {
    id: "free_painting",
    name: "Pintura Livre",
    icon: "🎨",
    description: "Desenhe livremente o que quiser no canvas",
    therapeuticFocus: "creative_expression",
    metricsCollected: ["stroke_count", "color_usage", "canvas_coverage", "drawing_time"],
    cognitiveFunction: "creative_motor_coordination",
    component: "FreePaintingActivity"
  },
  ASSISTED_PAINTING: {
    id: "assisted_painting",
    name: "Pintura Assistida",
    icon: "🖍️",
    description: "Clique nas áreas destacadas para colorir desenhos pré-definidos",
    therapeuticFocus: "guided_motor_skills",
    metricsCollected: ["area_completion", "color_accuracy", "click_precision", "completion_time"],
    cognitiveFunction: "guided_fine_motor_skills",
    component: "AssistedPaintingActivity"
  },
  CANVAS_PAINTING: {
    id: "canvas_painting",
    name: "Canvas de Pintura",
    icon: "🖼️",
    description: "Use ferramentas de pintura avançadas no canvas digital",
    therapeuticFocus: "advanced_motor_skills",
    metricsCollected: ["tool_usage", "brush_control", "layer_management", "artistic_complexity"],
    cognitiveFunction: "advanced_creative_coordination",
    component: "CanvasPaintingActivity"
  },
  PATTERN_PAINTING: {
    id: "pattern_painting",
    name: "Pintura de Padrões",
    icon: "🔷",
    description: "Complete padrões visuais usando cores e formas específicas",
    therapeuticFocus: "pattern_recognition_motor",
    metricsCollected: ["pattern_accuracy", "color_matching", "completion_rate", "motor_precision"],
    cognitiveFunction: "visual_motor_integration",
    component: "PatternPaintingActivity"
  }
};
function CreativePaintingGame({
  onBack
}) {
  const {
    user,
    sessionId,
    ttsEnabled = true
  } = reactExports.useContext(SystemContext);
  const {
    settings
  } = useAccessibilityContext();
  const [ttsActive2, setTtsActive] = reactExports.useState(() => {
    const saved = localStorage.getItem("creativePainting_ttsActive");
    return saved !== null ? JSON.parse(saved) : true;
  });
  const toggleTTS = reactExports.useCallback(() => {
    setTtsActive((prev) => {
      const newState = !prev;
      localStorage.setItem("creativePainting_ttsActive", JSON.stringify(newState));
      if (!newState && "speechSynthesis" in window) {
        window.speechSynthesis.cancel();
      }
      return newState;
    });
  }, []);
  const speak = reactExports.useCallback((text, options = {}) => {
    if (!ttsActive2 || !("speechSynthesis" in window)) {
      return;
    }
    window.speechSynthesis.cancel();
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = "pt-BR";
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    window.speechSynthesis.speak(utterance);
  }, [ttsActive2]);
  const [showStartScreen, setShowStartScreen] = reactExports.useState(true);
  const [currentDifficulty, setCurrentDifficulty] = reactExports.useState("easy");
  reactExports.useRef(null);
  const [gameState, setGameState] = reactExports.useState({
    status: "start",
    // 'start', 'playing', 'paused', 'finished'
    score: 0,
    round: 1,
    totalRounds: 10,
    difficulty: "easy",
    accuracy: 100,
    roundStartTime: null,
    // 🎨 SISTEMA DE MÉTRICAS TERAPÊUTICAS ESPECÍFICAS
    therapeuticMetrics: {
      // Métricas de cores - cada cor usada é um parâmetro
      colorMetrics: {
        colorFrequency: {},
        // Frequência de uso de cada cor
        colorCombinations: [],
        // Combinações de cores utilizadas
        emotionalColorMapping: {},
        // Mapeamento cor-emoção
        colorTransitionPatterns: [],
        // Padrões de transição entre cores
        dominantColors: [],
        // Cores dominantes por sessão
        colorHarmony: 0
        // Índice de harmonia cromática
      },
      // Métricas motoras - cada movimento é um parâmetro
      motorMetrics: {
        strokePrecision: [],
        // Precisão de cada traço
        handSteadiness: [],
        // Estabilidade da mão por movimento
        pressureVariation: [],
        // Variação de pressão aplicada
        movementFluency: [],
        // Fluidez dos movimentos
        coordinationLevel: 0,
        // Nível de coordenação geral
        motorConsistency: 0
        // Consistência motora
      },
      // Métricas espaciais - cada posicionamento é um parâmetro
      spatialMetrics: {
        spatialDistribution: [],
        // Distribuição espacial dos elementos
        compositionBalance: 0,
        // Equilíbrio da composição
        areaUtilization: 0,
        // Utilização da área disponível
        spatialPlanning: [],
        // Evidências de planejamento espacial
        symmetryPatterns: [],
        // Padrões de simetria
        spatialOrganization: 0
        // Nível de organização espacial
      },
      // Métricas criativas - cada escolha é um parâmetro
      creativityMetrics: {
        originalityScore: 0,
        // Pontuação de originalidade
        complexityPatterns: [],
        // Padrões de complexidade
        innovationFrequency: [],
        // Frequência de inovações
        creativeConsistency: 0,
        // Consistência criativa
        abstractThinking: [],
        // Evidências de pensamento abstrato
        creativeConfidence: 0
        // Confiança criativa
      },
      // Métricas atencionais - cada foco é um parâmetro
      attentionMetrics: {
        attentionDuration: [],
        // Duração de cada período de atenção
        focusConsistency: 0,
        // Consistência do foco
        distractionPatterns: [],
        // Padrões de distração
        taskCompletionRate: 0,
        // Taxa de conclusão de tarefas
        concentrationLevel: [],
        // Níveis de concentração
        attentionSustainability: 0
        // Sustentabilidade da atenção
      }
    },
    // 🎯 DADOS DE SESSÃO PARA ANÁLISE
    sessionData: {
      startTime: null,
      endTime: null,
      totalInteractions: 0,
      uniqueActions: [],
      behavioralPatterns: [],
      therapeuticInsights: []
    },
    strokes: [],
    // 🎨 Array de pinceladas para rastreamento detalhado
    colorsUsed: /* @__PURE__ */ new Set(["#ff6b6b"]),
    // 🎨 Conjunto de cores utilizadas
    // 🎯 Sistema de atividades funcionais redesenhado
    currentActivity: ACTIVITY_TYPES.FREE_PAINTING.id,
    activityCycle: [ACTIVITY_TYPES.FREE_PAINTING.id, ACTIVITY_TYPES.ASSISTED_PAINTING.id, ACTIVITY_TYPES.CANVAS_PAINTING.id],
    activityIndex: 0,
    roundsPerActivity: 3,
    // Menos rounds, mais foco na qualidade das métricas
    activityRoundCount: 0,
    activitiesCompleted: [],
    // 🎯 Dados específicos das atividades funcionais
    activityData: {
      free_painting: {
        canvas: null,
        brushSize: 5,
        currentColor: "#000000",
        availableColors: ["#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF", "#00FFFF", "#FFA500", "#800080", "#000000", "#FFFFFF"],
        strokes: [],
        isDrawing: false,
        lastPoint: null
      },
      assisted_painting: {
        currentTemplate: "house",
        // casa, árvore, sol, etc.
        availableTemplates: [{
          id: "house",
          name: "Casa",
          emoji: "🏠",
          areas: []
        }, {
          id: "tree",
          name: "Árvore",
          emoji: "🌳",
          areas: []
        }, {
          id: "sun",
          name: "Sol",
          emoji: "☀️",
          areas: []
        }, {
          id: "flower",
          name: "Flor",
          emoji: "🌸",
          areas: []
        }, {
          id: "car",
          name: "Carro",
          emoji: "🚗",
          areas: []
        }],
        completedAreas: [],
        currentColor: "#FF0000",
        totalAreas: 0,
        completionPercentage: 0
      },
      canvas_painting: {
        brushSize: 10,
        brushType: "round",
        // round, square, spray
        currentColor: "#000000",
        availableTools: ["brush", "eraser", "fill", "line", "circle", "rectangle"],
        currentTool: "brush",
        layers: [{
          id: "layer1",
          visible: true,
          opacity: 1
        }],
        currentLayer: "layer1",
        canvasHistory: [],
        historyIndex: -1
      }
    },
    // 🎯 Feedback e animações
    showFeedback: false,
    feedbackType: null,
    feedbackMessage: "",
    showCelebration: false,
    // 🎯 Métricas comportamentais
    responseTime: 0,
    hesitationCount: 0,
    helpUsed: false,
    consecutiveCorrect: 0,
    totalAttempts: 0,
    correctAttempts: 0
  });
  const {
    collectMetrics,
    startUnifiedSession,
    processAdvancedMetrics
  } = useUnifiedGameLogic("CreativePainting");
  const [collectorsHub] = reactExports.useState(() => new CreativePaintingCollectorsHub());
  const {
    recordInteraction: recordMultisensoryInteraction
  } = useMultisensoryIntegration(sessionId, {
    learningStyle: user?.profile?.learningStyle || "visual"
  });
  useTherapeuticOrchestrator({});
  const [sessionStartTime, setSessionStartTime] = reactExports.useState(null);
  const [brushStrokes, setBrushStrokes] = reactExports.useState([]);
  const [colorTransitions, setColorTransitions] = reactExports.useState([]);
  const [creativityMetrics2, setCreativityMetrics] = reactExports.useState({
    originalityScore: 0,
    complexityScore: 0,
    expressiveRange: 0,
    spatialUtilization: 0,
    totalStrokes: 0,
    lastStrokeTime: null
  });
  const canvasRef = reactExports.useRef(null);
  reactExports.useRef(null);
  const drawingRef = reactExports.useRef({
    isDrawing: false,
    lastPoint: null
  });
  const initializeGame = reactExports.useCallback((difficulty) => {
    setCurrentDifficulty(difficulty);
    startUnifiedSession(difficulty, {
      gameType: "CreativePainting",
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    });
    const difficultyConfig = PAINTING_CONFIG.difficulties[difficulty];
    const defaultBrushSize = difficultyConfig?.defaultBrush || 10;
    setGameState((prev) => ({
      ...prev,
      brushSize: defaultBrushSize,
      startTime: Date.now(),
      strokes: [],
      undoStack: [],
      redoStack: [],
      colorsUsed: /* @__PURE__ */ new Set(["#ff6b6b"]),
      savedCount: 0,
      showPlaceholder: true
    }));
    setShowStartScreen(false);
  }, [startUnifiedSession]);
  reactExports.useEffect(() => {
    if (!sessionStartTime) {
      setSessionStartTime(Date.now());
    }
  }, [sessionStartTime]);
  reactExports.useEffect(() => {
    const timer = setInterval(() => {
      setGameState((prev) => ({
        ...prev
      }));
    }, 1e3);
    return () => clearInterval(timer);
  }, []);
  reactExports.useEffect(() => {
    return () => {
      if ("speechSynthesis" in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);
  const getElapsedTime = reactExports.useCallback(() => {
    const elapsed = Math.floor((Date.now() - gameState.startTime) / 1e3);
    const minutes = Math.floor(elapsed / 60);
    const seconds = elapsed % 60;
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  }, [gameState.startTime]);
  reactExports.useCallback((color, action, timestamp = Date.now()) => {
    setGameState((prev) => {
      const newColorMetrics = {
        ...prev.therapeuticMetrics.colorMetrics
      };
      newColorMetrics.colorFrequency[color] = (newColorMetrics.colorFrequency[color] || 0) + 1;
      if (prev.therapeuticMetrics.colorMetrics.lastColor && prev.therapeuticMetrics.colorMetrics.lastColor !== color) {
        `${prev.therapeuticMetrics.colorMetrics.lastColor}->${color}`;
        newColorMetrics.colorTransitionPatterns.push({
          from: prev.therapeuticMetrics.colorMetrics.lastColor,
          to: color,
          timestamp,
          action
        });
      }
      newColorMetrics.lastColor = color;
      collectMetrics({
        type: "color_usage",
        color,
        action,
        timestamp,
        frequency: newColorMetrics.colorFrequency[color],
        sessionData: prev.sessionData
      });
      return {
        ...prev,
        therapeuticMetrics: {
          ...prev.therapeuticMetrics,
          colorMetrics: newColorMetrics
        }
      };
    });
  }, [collectMetrics]);
  reactExports.useCallback((strokeData) => {
    setGameState((prev) => {
      const newMotorMetrics = {
        ...prev.therapeuticMetrics.motorMetrics
      };
      const precision = calculateStrokePrecision(strokeData);
      newMotorMetrics.strokePrecision.push(precision);
      const steadiness = calculateHandSteadiness(strokeData.points);
      newMotorMetrics.handSteadiness.push(steadiness);
      if (strokeData.pressure) {
        newMotorMetrics.pressureVariation.push(strokeData.pressure);
      }
      const fluency = calculateMovementFluency(strokeData.points);
      newMotorMetrics.movementFluency.push(fluency);
      collectMetrics({
        type: "motor_skills",
        precision,
        steadiness,
        fluency,
        pressure: strokeData.pressure,
        timestamp: strokeData.timestamp,
        sessionData: prev.sessionData
      });
      return {
        ...prev,
        therapeuticMetrics: {
          ...prev.therapeuticMetrics,
          motorMetrics: newMotorMetrics
        }
      };
    });
  }, [collectMetrics]);
  reactExports.useCallback((position, element, action) => {
    setGameState((prev) => {
      const newSpatialMetrics = {
        ...prev.therapeuticMetrics.spatialMetrics
      };
      newSpatialMetrics.spatialDistribution.push({
        x: position.x,
        y: position.y,
        element,
        action,
        timestamp: Date.now()
      });
      if (action === "planned_placement") {
        newSpatialMetrics.spatialPlanning.push({
          position,
          element,
          timestamp: Date.now()
        });
      }
      collectMetrics({
        type: "spatial_organization",
        position,
        element,
        action,
        distribution: newSpatialMetrics.spatialDistribution.length,
        timestamp: Date.now(),
        sessionData: prev.sessionData
      });
      return {
        ...prev,
        therapeuticMetrics: {
          ...prev.therapeuticMetrics,
          spatialMetrics: newSpatialMetrics
        }
      };
    });
  }, [collectMetrics]);
  reactExports.useCallback((creativeAction) => {
    setGameState((prev) => {
      const newCreativityMetrics = {
        ...prev.therapeuticMetrics.creativityMetrics
      };
      if (creativeAction.complexity) {
        newCreativityMetrics.complexityPatterns.push({
          level: creativeAction.complexity,
          timestamp: Date.now(),
          context: creativeAction.context
        });
      }
      if (creativeAction.isInnovative) {
        newCreativityMetrics.innovationFrequency.push({
          type: creativeAction.type,
          timestamp: Date.now(),
          description: creativeAction.description
        });
      }
      if (creativeAction.isAbstract) {
        newCreativityMetrics.abstractThinking.push({
          level: creativeAction.abstractLevel,
          timestamp: Date.now(),
          manifestation: creativeAction.manifestation
        });
      }
      collectMetrics({
        type: "creativity_expression",
        action: creativeAction,
        timestamp: Date.now(),
        sessionData: prev.sessionData
      });
      return {
        ...prev,
        therapeuticMetrics: {
          ...prev.therapeuticMetrics,
          creativityMetrics: newCreativityMetrics
        }
      };
    });
  }, [collectMetrics]);
  reactExports.useCallback((attentionEvent) => {
    setGameState((prev) => {
      const newAttentionMetrics = {
        ...prev.therapeuticMetrics.attentionMetrics
      };
      if (attentionEvent.type === "focus_duration") {
        newAttentionMetrics.attentionDuration.push({
          duration: attentionEvent.duration,
          task: attentionEvent.task,
          timestamp: Date.now()
        });
      }
      if (attentionEvent.type === "distraction") {
        newAttentionMetrics.distractionPatterns.push({
          cause: attentionEvent.cause,
          duration: attentionEvent.duration,
          recovery: attentionEvent.recovery,
          timestamp: Date.now()
        });
      }
      if (attentionEvent.type === "concentration_level") {
        newAttentionMetrics.concentrationLevel.push({
          level: attentionEvent.level,
          task: attentionEvent.task,
          timestamp: Date.now()
        });
      }
      collectMetrics({
        type: "attention_focus",
        event: attentionEvent,
        timestamp: Date.now(),
        sessionData: prev.sessionData
      });
      return {
        ...prev,
        therapeuticMetrics: {
          ...prev.therapeuticMetrics,
          attentionMetrics: newAttentionMetrics
        }
      };
    });
  }, [collectMetrics]);
  const calculateStrokePrecision = reactExports.useCallback((strokeData) => {
    if (!strokeData.points || strokeData.points.length < 2) return 0;
    let totalDeviation = 0;
    let targetPath = strokeData.targetPath || [];
    if (targetPath.length === 0) {
      for (let i = 1; i < strokeData.points.length - 1; i++) {
        const prev = strokeData.points[i - 1];
        const curr = strokeData.points[i];
        const next = strokeData.points[i + 1];
        const angle1 = Math.atan2(curr.y - prev.y, curr.x - prev.x);
        const angle2 = Math.atan2(next.y - curr.y, next.x - curr.x);
        const angleDiff = Math.abs(angle1 - angle2);
        totalDeviation += angleDiff;
      }
      return Math.max(0, 1 - totalDeviation / strokeData.points.length);
    }
    strokeData.points.forEach((point, index) => {
      if (targetPath[index]) {
        const distance = Math.sqrt(Math.pow(point.x - targetPath[index].x, 2) + Math.pow(point.y - targetPath[index].y, 2));
        totalDeviation += distance;
      }
    });
    const averageDeviation = totalDeviation / strokeData.points.length;
    return Math.max(0, 1 - averageDeviation / 100);
  }, []);
  const calculateHandSteadiness = reactExports.useCallback((points) => {
    if (!points || points.length < 3) return 0;
    let totalJitter = 0;
    for (let i = 1; i < points.length - 1; i++) {
      const prev = points[i - 1];
      const curr = points[i];
      const next = points[i + 1];
      const expectedX = (prev.x + next.x) / 2;
      const expectedY = (prev.y + next.y) / 2;
      const jitter = Math.sqrt(Math.pow(curr.x - expectedX, 2) + Math.pow(curr.y - expectedY, 2));
      totalJitter += jitter;
    }
    const averageJitter = totalJitter / (points.length - 2);
    return Math.max(0, 1 - averageJitter / 50);
  }, []);
  const calculateMovementFluency = reactExports.useCallback((points) => {
    if (!points || points.length < 2) return 0;
    let totalSpeed = 0;
    let speedVariations = 0;
    let previousSpeed = 0;
    for (let i = 1; i < points.length; i++) {
      const prev = points[i - 1];
      const curr = points[i];
      const distance = Math.sqrt(Math.pow(curr.x - prev.x, 2) + Math.pow(curr.y - prev.y, 2));
      const timeDiff = (curr.timestamp || i) - (prev.timestamp || i - 1);
      const speed = distance / Math.max(timeDiff, 1);
      totalSpeed += speed;
      if (i > 1) {
        speedVariations += Math.abs(speed - previousSpeed);
      }
      previousSpeed = speed;
    }
    totalSpeed / (points.length - 1);
    const speedConsistency = 1 - speedVariations / totalSpeed;
    return Math.max(0, Math.min(1, speedConsistency));
  }, []);
  const switchActivity = reactExports.useCallback((activityId) => {
    const activity = Object.values(ACTIVITY_TYPES).find((a) => a.id === activityId);
    if (!activity) return;
    setGameState((prev) => ({
      ...prev,
      currentActivity: activityId,
      // Resetar dados específicos da atividade
      activityData: {
        ...prev.activityData,
        [activityId]: prev.activityData[activityId] || {}
      }
    }));
    collectMetrics({
      type: "activity_switch",
      from: gameState.currentActivity,
      to: activityId,
      therapeuticFocus: activity.therapeuticFocus,
      timestamp: Date.now()
    });
    console.log(`🎯 Atividade alterada para: ${activity.name}`);
  }, [gameState.currentActivity, collectMetrics]);
  reactExports.useCallback(() => {
    const activity = Object.values(ACTIVITY_TYPES).find((a) => a.id === gameState.currentActivity);
    if (!activity) return /* @__PURE__ */ React.createElement("div", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 773,
      columnNumber: 27
    } }, "Atividade não encontrada");
    switch (gameState.currentActivity) {
      case ACTIVITY_TYPES.FREE_PAINTING.id:
        return renderFreePainting();
      case ACTIVITY_TYPES.ASSISTED_PAINTING.id:
        return renderAssistedPainting();
      case ACTIVITY_TYPES.CANVAS_PAINTING.id:
        return renderCanvasPainting();
      case ACTIVITY_TYPES.PATTERN_PAINTING.id:
        return renderPatternPainting();
      default:
        return renderFreePainting();
    }
  }, [gameState.currentActivity]);
  const renderFreePainting = reactExports.useCallback(() => {
    const activityData = gameState.activityData.free_painting || {};
    return /* @__PURE__ */ React.createElement("div", { className: styles.paintingActivity, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 794,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.activityHeader, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 795,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("h3", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 796,
      columnNumber: 11
    } }, "🎨 Pintura Livre"), /* @__PURE__ */ React.createElement("p", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 797,
      columnNumber: 11
    } }, "Desenhe livremente o que quiser no canvas")), /* @__PURE__ */ React.createElement("div", { className: styles.freePaintingArea, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 800,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.colorPalette, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 802,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("h4", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 803,
      columnNumber: 13
    } }, "Cores"), /* @__PURE__ */ React.createElement("div", { className: styles.colorGrid, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 804,
      columnNumber: 13
    } }, activityData.availableColors?.map((color, index) => /* @__PURE__ */ React.createElement(motion.div, { key: color, className: `${styles.colorBtn} ${activityData.currentColor === color ? styles.active : ""}`, style: {
      backgroundColor: color
    }, onClick: () => handleColorChange(color), whileHover: {
      scale: 1.1
    }, whileTap: {
      scale: 0.9
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 806,
      columnNumber: 17
    } })))), /* @__PURE__ */ React.createElement("div", { className: styles.brushControls, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 819,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("h4", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 820,
      columnNumber: 13
    } }, "Pincel"), /* @__PURE__ */ React.createElement("div", { className: styles.brushSizeControl, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 821,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("label", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 822,
      columnNumber: 15
    } }, "Tamanho: ", activityData.brushSize, "px"), /* @__PURE__ */ React.createElement("input", { type: "range", min: "1", max: "20", value: activityData.brushSize, onChange: (e) => handleBrushSizeChange(parseInt(e.target.value)), className: styles.brushSlider, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 823,
      columnNumber: 15
    } }))), /* @__PURE__ */ React.createElement("div", { className: styles.canvasContainer, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 835,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("canvas", { ref: canvasRef, width: 600, height: 400, className: styles.paintingCanvas, onMouseDown: handleCanvasMouseDown, onMouseMove: handleCanvasMouseMove, onMouseUp: handleCanvasMouseUp, onTouchStart: handleCanvasTouchStart, onTouchMove: handleCanvasTouchMove, onTouchEnd: handleCanvasTouchEnd, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 836,
      columnNumber: 13
    } })), /* @__PURE__ */ React.createElement("div", { className: styles.actionControls, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 851,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("button", { className: styles.clearBtn, onClick: handleClearCanvas, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 852,
      columnNumber: 13
    } }, "🗑️ Limpar"), /* @__PURE__ */ React.createElement("button", { className: styles.saveBtn, onClick: handleSaveDrawing, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 855,
      columnNumber: 13
    } }, "💾 Salvar"))));
  }, [gameState.activityData]);
  const renderAssistedPainting = reactExports.useCallback(() => {
    const activityData = gameState.activityData.assisted_painting || {};
    const currentTemplate = activityData.availableTemplates?.find((t) => t.id === activityData.currentTemplate);
    return /* @__PURE__ */ React.createElement("div", { className: styles.paintingActivity, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 870,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.activityHeader, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 871,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("h3", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 872,
      columnNumber: 11
    } }, "🖍️ Pintura Assistida"), /* @__PURE__ */ React.createElement("p", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 873,
      columnNumber: 11
    } }, "Clique nas áreas destacadas para colorir o desenho")), /* @__PURE__ */ React.createElement("div", { className: styles.assistedPaintingArea, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 876,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.templateSelector, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 878,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("h4", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 879,
      columnNumber: 13
    } }, "Escolha um desenho"), /* @__PURE__ */ React.createElement("div", { className: styles.templateGrid, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 880,
      columnNumber: 13
    } }, activityData.availableTemplates?.map((template) => /* @__PURE__ */ React.createElement(motion.div, { key: template.id, className: `${styles.templateBtn} ${activityData.currentTemplate === template.id ? styles.active : ""}`, onClick: () => handleTemplateChange(template.id), whileHover: {
      scale: 1.05
    }, whileTap: {
      scale: 0.95
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 882,
      columnNumber: 17
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.templateIcon, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 889,
      columnNumber: 19
    } }, template.emoji), /* @__PURE__ */ React.createElement("div", { className: styles.templateName, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 890,
      columnNumber: 19
    } }, template.name))))), /* @__PURE__ */ React.createElement("div", { className: styles.colorPalette, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 897,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("h4", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 898,
      columnNumber: 13
    } }, "Cores"), /* @__PURE__ */ React.createElement("div", { className: styles.colorGrid, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 899,
      columnNumber: 13
    } }, gameState.activityData.free_painting?.availableColors?.map((color, index) => /* @__PURE__ */ React.createElement(motion.div, { key: color, className: `${styles.colorBtn} ${activityData.currentColor === color ? styles.active : ""}`, style: {
      backgroundColor: color
    }, onClick: () => handleAssistedColorChange(color), whileHover: {
      scale: 1.1
    }, whileTap: {
      scale: 0.9
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 901,
      columnNumber: 17
    } })))), /* @__PURE__ */ React.createElement("div", { className: styles.canvasContainer, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 914,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.assistedCanvas, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 915,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("canvas", { ref: canvasRef, width: 600, height: 400, className: styles.paintingCanvas, onClick: handleAssistedCanvasClick, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 916,
      columnNumber: 15
    } }), /* @__PURE__ */ React.createElement("div", { className: styles.clickableAreas, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 924,
      columnNumber: 15
    } }, renderClickableAreas(currentTemplate)))), /* @__PURE__ */ React.createElement("div", { className: styles.progressSection, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 931,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("h4", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 932,
      columnNumber: 13
    } }, "Progresso: ", activityData.completionPercentage, "%"), /* @__PURE__ */ React.createElement("div", { className: styles.progressBar, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 933,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.progressFill, style: {
      width: `${activityData.completionPercentage}%`
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 934,
      columnNumber: 15
    } })))));
  }, [gameState.activityData]);
  const renderCanvasPainting = reactExports.useCallback(() => {
    const activityData = gameState.activityData.canvas_painting || {};
    return /* @__PURE__ */ React.createElement("div", { className: styles.paintingActivity, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 950,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.activityHeader, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 951,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("h3", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 952,
      columnNumber: 11
    } }, "🖼️ Canvas de Pintura"), /* @__PURE__ */ React.createElement("p", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 953,
      columnNumber: 11
    } }, "Use ferramentas avançadas de pintura no canvas digital")), /* @__PURE__ */ React.createElement("div", { className: styles.canvasPaintingArea, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 956,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.toolBar, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 958,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("h4", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 959,
      columnNumber: 13
    } }, "Ferramentas"), /* @__PURE__ */ React.createElement("div", { className: styles.toolGrid, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 960,
      columnNumber: 13
    } }, activityData.availableTools?.map((tool) => /* @__PURE__ */ React.createElement(motion.div, { key: tool, className: `${styles.toolBtn} ${activityData.currentTool === tool ? styles.active : ""}`, onClick: () => handleToolChange(tool), whileHover: {
      scale: 1.05
    }, whileTap: {
      scale: 0.95
    }, title: getToolName(tool), __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 962,
      columnNumber: 17
    } }, getToolIcon(tool))))), /* @__PURE__ */ React.createElement("div", { className: styles.advancedControls, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 977,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.brushControls, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 978,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("h4", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 979,
      columnNumber: 15
    } }, "Pincel"), /* @__PURE__ */ React.createElement("div", { className: styles.controlGroup, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 980,
      columnNumber: 15
    } }, /* @__PURE__ */ React.createElement("label", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 981,
      columnNumber: 17
    } }, "Tamanho: ", activityData.brushSize, "px"), /* @__PURE__ */ React.createElement("input", { type: "range", min: "1", max: "50", value: activityData.brushSize, onChange: (e) => handleCanvasBrushSizeChange(parseInt(e.target.value)), className: styles.brushSlider, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 982,
      columnNumber: 17
    } })), /* @__PURE__ */ React.createElement("div", { className: styles.controlGroup, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 991,
      columnNumber: 15
    } }, /* @__PURE__ */ React.createElement("label", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 992,
      columnNumber: 17
    } }, "Tipo:"), /* @__PURE__ */ React.createElement("select", { value: activityData.brushType, onChange: (e) => handleBrushTypeChange(e.target.value), className: styles.brushTypeSelect, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 993,
      columnNumber: 17
    } }, /* @__PURE__ */ React.createElement("option", { value: "round", __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 998,
      columnNumber: 19
    } }, "Redondo"), /* @__PURE__ */ React.createElement("option", { value: "square", __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 999,
      columnNumber: 19
    } }, "Quadrado"), /* @__PURE__ */ React.createElement("option", { value: "spray", __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1e3,
      columnNumber: 19
    } }, "Spray")))), /* @__PURE__ */ React.createElement("div", { className: styles.colorPalette, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1006,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("h4", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1007,
      columnNumber: 15
    } }, "Cores"), /* @__PURE__ */ React.createElement("div", { className: styles.colorGrid, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1008,
      columnNumber: 15
    } }, gameState.activityData.free_painting?.availableColors?.map((color, index) => /* @__PURE__ */ React.createElement(motion.div, { key: color, className: `${styles.colorBtn} ${activityData.currentColor === color ? styles.active : ""}`, style: {
      backgroundColor: color
    }, onClick: () => handleCanvasColorChange(color), whileHover: {
      scale: 1.1
    }, whileTap: {
      scale: 0.9
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1010,
      columnNumber: 19
    } }))))), /* @__PURE__ */ React.createElement("div", { className: styles.canvasContainer, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1024,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("canvas", { ref: canvasRef, width: 800, height: 600, className: styles.advancedCanvas, onMouseDown: handleCanvasMouseDown, onMouseMove: handleCanvasMouseMove, onMouseUp: handleCanvasMouseUp, onTouchStart: handleCanvasTouchStart, onTouchMove: handleCanvasTouchMove, onTouchEnd: handleCanvasTouchEnd, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1025,
      columnNumber: 13
    } })), /* @__PURE__ */ React.createElement("div", { className: styles.actionControls, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1040,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("button", { className: styles.undoBtn, onClick: handleUndo, disabled: activityData.historyIndex <= 0, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1041,
      columnNumber: 13
    } }, "↶ Desfazer"), /* @__PURE__ */ React.createElement("button", { className: styles.redoBtn, onClick: handleRedo, disabled: activityData.historyIndex >= activityData.canvasHistory?.length - 1, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1044,
      columnNumber: 13
    } }, "↷ Refazer"), /* @__PURE__ */ React.createElement("button", { className: styles.clearBtn, onClick: handleClearCanvas, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1047,
      columnNumber: 13
    } }, "🗑️ Limpar"), /* @__PURE__ */ React.createElement("button", { className: styles.saveBtn, onClick: handleSaveDrawing, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1050,
      columnNumber: 13
    } }, "💾 Salvar"))));
  }, [gameState.activityData]);
  const renderPatternPainting = reactExports.useCallback(() => {
    const activityData = gameState.activityData.pattern_painting || {};
    return /* @__PURE__ */ React.createElement("div", { className: styles.paintingActivity, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1064,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.activityHeader, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1065,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("h3", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1066,
      columnNumber: 11
    } }, "🔷 Pintura de Padrões"), /* @__PURE__ */ React.createElement("p", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1067,
      columnNumber: 11
    } }, "Complete padrões visuais usando cores e formas específicas")), /* @__PURE__ */ React.createElement("div", { className: styles.patternPaintingArea, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1070,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.patternTemplate, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1072,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("h4", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1073,
      columnNumber: 13
    } }, "Padrão para Completar"), /* @__PURE__ */ React.createElement("div", { className: styles.patternGrid, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1074,
      columnNumber: 13
    } }, activityData.currentPattern?.map((row, rowIndex) => /* @__PURE__ */ React.createElement("div", { key: rowIndex, className: styles.patternRow, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1076,
      columnNumber: 17
    } }, row.map((cell, cellIndex) => /* @__PURE__ */ React.createElement("div", { key: `${rowIndex}-${cellIndex}`, className: `${styles.patternCell} ${cell.completed ? styles.completed : ""}`, style: {
      backgroundColor: cell.targetColor
    }, onClick: () => handlePatternCellClick(rowIndex, cellIndex), __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1078,
      columnNumber: 21
    } }, cell.completed ? "✓" : "")))))), /* @__PURE__ */ React.createElement("div", { className: styles.patternColorPalette, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1093,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("h4", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1094,
      columnNumber: 13
    } }, "Cores Disponíveis"), /* @__PURE__ */ React.createElement("div", { className: styles.colorGrid, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1095,
      columnNumber: 13
    } }, activityData.availableColors?.map((color) => /* @__PURE__ */ React.createElement("div", { key: color, className: `${styles.colorOption} ${activityData.selectedColor === color ? styles.selected : ""}`, style: {
      backgroundColor: color
    }, onClick: () => handlePatternColorSelect(color), __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1097,
      columnNumber: 17
    } })))), /* @__PURE__ */ React.createElement("div", { className: styles.patternProgress, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1108,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.progressBar, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1109,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.progressFill, style: {
      width: `${activityData.completionPercentage || 0}%`
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1110,
      columnNumber: 15
    } })), /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1115,
      columnNumber: 13
    } }, activityData.completionPercentage || 0, "% Completo"))));
  }, [gameState.activityData]);
  reactExports.useCallback(() => {
    return /* @__PURE__ */ React.createElement("div", { className: styles.therapeuticActivity, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1127,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.activityHeader, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1128,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("h3", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1129,
      columnNumber: 11
    } }, "🌟 Perfil de Expressão Criativa"), /* @__PURE__ */ React.createElement("p", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1130,
      columnNumber: 11
    } }, "Expresse sua criatividade livremente para análise de originalidade")), /* @__PURE__ */ React.createElement("div", { className: styles.creativeProfilingArea, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1133,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.creativeCanvas, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1134,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("canvas", { ref: canvasRef, width: 600, height: 400, className: styles.therapeuticCanvas, onMouseDown: handleCanvasMouseDown, onMouseMove: handleCanvasMouseMove, onMouseUp: handleCanvasMouseUp, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1135,
      columnNumber: 13
    } })), /* @__PURE__ */ React.createElement("div", { className: styles.creativityMetrics, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1146,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.metricCard, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1147,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1148,
      columnNumber: 15
    } }, "Originalidade"), /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1149,
      columnNumber: 15
    } }, (gameState.therapeuticMetrics.creativityMetrics.originalityScore * 100).toFixed(0), "%")), /* @__PURE__ */ React.createElement("div", { className: styles.metricCard, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1151,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1152,
      columnNumber: 15
    } }, "Complexidade"), /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1153,
      columnNumber: 15
    } }, gameState.therapeuticMetrics.creativityMetrics.complexityPatterns.length)), /* @__PURE__ */ React.createElement("div", { className: styles.metricCard, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1155,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1156,
      columnNumber: 15
    } }, "Inovação"), /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1157,
      columnNumber: 15
    } }, gameState.therapeuticMetrics.creativityMetrics.innovationFrequency.length)))));
  }, [gameState.therapeuticMetrics]);
  reactExports.useCallback(() => {
    return /* @__PURE__ */ React.createElement("div", { className: styles.therapeuticActivity, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1168,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.activityHeader, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1169,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("h3", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1170,
      columnNumber: 11
    } }, "🎯 Medição de Foco Atencional"), /* @__PURE__ */ React.createElement("p", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1171,
      columnNumber: 11
    } }, "Complete tarefas dirigidas para medir sua capacidade de atenção sustentada")), /* @__PURE__ */ React.createElement("div", { className: styles.attentionMeasurementArea, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1174,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.focusTask, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1175,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.taskInstruction, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1176,
      columnNumber: 13
    } }, "Pinte apenas dentro das áreas destacadas"), /* @__PURE__ */ React.createElement("canvas", { ref: canvasRef, width: 600, height: 400, className: styles.therapeuticCanvas, onMouseDown: handleCanvasMouseDown, onMouseMove: handleCanvasMouseMove, onMouseUp: handleCanvasMouseUp, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1179,
      columnNumber: 13
    } })), /* @__PURE__ */ React.createElement("div", { className: styles.attentionMetrics, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1190,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.metricCard, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1191,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1192,
      columnNumber: 15
    } }, "Duração Foco"), /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1193,
      columnNumber: 15
    } }, gameState.therapeuticMetrics.attentionMetrics.attentionDuration.length > 0 ? `${(gameState.therapeuticMetrics.attentionMetrics.attentionDuration.slice(-1)[0].duration / 1e3).toFixed(1)}s` : "0s")), /* @__PURE__ */ React.createElement("div", { className: styles.metricCard, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1197,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1198,
      columnNumber: 15
    } }, "Consistência"), /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1199,
      columnNumber: 15
    } }, (gameState.therapeuticMetrics.attentionMetrics.focusConsistency * 100).toFixed(0), "%")), /* @__PURE__ */ React.createElement("div", { className: styles.metricCard, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1201,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1202,
      columnNumber: 15
    } }, "Distrações"), /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1203,
      columnNumber: 15
    } }, gameState.therapeuticMetrics.attentionMetrics.distractionPatterns.length)))));
  }, [gameState.therapeuticMetrics]);
  const handleColorChange = reactExports.useCallback((color) => {
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        free_painting: {
          ...prev.activityData.free_painting,
          currentColor: color
        }
      }
    }));
    console.log(`🎨 Cor alterada para: ${color}`);
  }, []);
  const handleBrushSizeChange = reactExports.useCallback((size) => {
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        free_painting: {
          ...prev.activityData.free_painting,
          brushSize: size
        }
      }
    }));
    console.log(`🖌️ Tamanho do pincel alterado para: ${size}px`);
  }, []);
  const handleClearCanvas = reactExports.useCallback(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext("2d");
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      setGameState((prev) => ({
        ...prev,
        strokes: [],
        activityData: {
          ...prev.activityData,
          free_painting: {
            ...prev.activityData.free_painting,
            strokes: []
          }
        }
      }));
      console.log("🗑️ Canvas limpo");
    }
  }, []);
  const handleSaveDrawing = reactExports.useCallback(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      const dataURL = canvas.toDataURL("image/png");
      const link = document.createElement("a");
      link.download = `pintura_${Date.now()}.png`;
      link.href = dataURL;
      link.click();
      console.log("💾 Desenho salvo");
    }
  }, []);
  const handleTemplateChange = reactExports.useCallback((templateId) => {
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        assisted_painting: {
          ...prev.activityData.assisted_painting,
          currentTemplate: templateId,
          completedAreas: [],
          completionPercentage: 0
        }
      }
    }));
    redrawAssistedTemplate(templateId);
    console.log(`🖍️ Template alterado para: ${templateId}`);
  }, []);
  const handleAssistedColorChange = reactExports.useCallback((color) => {
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        assisted_painting: {
          ...prev.activityData.assisted_painting,
          currentColor: color
        }
      }
    }));
    console.log(`🖍️ Cor da pintura assistida alterada para: ${color}`);
  }, []);
  const handleAssistedCanvasClick = reactExports.useCallback((event) => {
    const rect = event.target.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    const clickedArea = findClickableArea(x, y);
    if (clickedArea && !gameState.activityData.assisted_painting.completedAreas.includes(clickedArea.id)) {
      fillArea(clickedArea, gameState.activityData.assisted_painting.currentColor);
      setGameState((prev) => {
        const newCompletedAreas = [...prev.activityData.assisted_painting.completedAreas, clickedArea.id];
        const totalAreas = prev.activityData.assisted_painting.totalAreas || 10;
        const completionPercentage = Math.round(newCompletedAreas.length / totalAreas * 100);
        return {
          ...prev,
          activityData: {
            ...prev.activityData,
            assisted_painting: {
              ...prev.activityData.assisted_painting,
              completedAreas: newCompletedAreas,
              completionPercentage
            }
          }
        };
      });
      console.log(`🖍️ Área ${clickedArea.id} colorida`);
    }
  }, [gameState.activityData.assisted_painting]);
  const handleToolChange = reactExports.useCallback((tool) => {
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        canvas_painting: {
          ...prev.activityData.canvas_painting,
          currentTool: tool
        }
      }
    }));
    console.log(`🛠️ Ferramenta alterada para: ${tool}`);
  }, []);
  const handleCanvasBrushSizeChange = reactExports.useCallback((size) => {
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        canvas_painting: {
          ...prev.activityData.canvas_painting,
          brushSize: size
        }
      }
    }));
    console.log(`🖌️ Tamanho do pincel do canvas alterado para: ${size}px`);
  }, []);
  const handleBrushTypeChange = reactExports.useCallback((type) => {
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        canvas_painting: {
          ...prev.activityData.canvas_painting,
          brushType: type
        }
      }
    }));
    console.log(`🖌️ Tipo do pincel alterado para: ${type}`);
  }, []);
  const handleCanvasColorChange = reactExports.useCallback((color) => {
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        canvas_painting: {
          ...prev.activityData.canvas_painting,
          currentColor: color
        }
      }
    }));
    console.log(`🎨 Cor do canvas alterada para: ${color}`);
  }, []);
  const handleUndo = reactExports.useCallback(() => {
    setGameState((prev) => {
      const canvasData = prev.activityData.canvas_painting;
      if (canvasData.historyIndex > 0) {
        const newIndex = canvasData.historyIndex - 1;
        const imageData = canvasData.canvasHistory[newIndex];
        const canvas = canvasRef.current;
        if (canvas && imageData) {
          const ctx = canvas.getContext("2d");
          const img = new Image();
          img.onload = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);
          };
          img.src = imageData;
        }
        return {
          ...prev,
          activityData: {
            ...prev.activityData,
            canvas_painting: {
              ...canvasData,
              historyIndex: newIndex
            }
          }
        };
      }
      return prev;
    });
    console.log("↶ Desfazer ação");
  }, []);
  const handleRedo = reactExports.useCallback(() => {
    setGameState((prev) => {
      const canvasData = prev.activityData.canvas_painting;
      if (canvasData.historyIndex < canvasData.canvasHistory.length - 1) {
        const newIndex = canvasData.historyIndex + 1;
        const imageData = canvasData.canvasHistory[newIndex];
        const canvas = canvasRef.current;
        if (canvas && imageData) {
          const ctx = canvas.getContext("2d");
          const img = new Image();
          img.onload = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);
          };
          img.src = imageData;
        }
        return {
          ...prev,
          activityData: {
            ...prev.activityData,
            canvas_painting: {
              ...canvasData,
              historyIndex: newIndex
            }
          }
        };
      }
      return prev;
    });
    console.log("↷ Refazer ação");
  }, []);
  const handlePatternCellClick = reactExports.useCallback((rowIndex, cellIndex) => {
    setGameState((prev) => {
      const patternData = prev.activityData.pattern_painting || {};
      const currentPattern = patternData.currentPattern || [];
      if (currentPattern[rowIndex] && currentPattern[rowIndex][cellIndex]) {
        const newPattern = [...currentPattern];
        newPattern[rowIndex] = [...newPattern[rowIndex]];
        newPattern[rowIndex][cellIndex] = {
          ...newPattern[rowIndex][cellIndex],
          completed: true,
          userColor: patternData.selectedColor
        };
        const totalCells = currentPattern.flat().length;
        const completedCells = newPattern.flat().filter((cell) => cell.completed).length;
        const completionPercentage = Math.round(completedCells / totalCells * 100);
        return {
          ...prev,
          activityData: {
            ...prev.activityData,
            pattern_painting: {
              ...patternData,
              currentPattern: newPattern,
              completionPercentage
            }
          }
        };
      }
      return prev;
    });
    console.log(`🔷 Célula do padrão [${rowIndex}, ${cellIndex}] preenchida`);
  }, []);
  const handlePatternColorSelect = reactExports.useCallback((color) => {
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        pattern_painting: {
          ...prev.activityData.pattern_painting,
          selectedColor: color
        }
      }
    }));
    console.log(`🔷 Cor do padrão selecionada: ${color}`);
  }, []);
  reactExports.useCallback((size) => {
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        collaborative_painting: {
          ...prev.activityData.collaborative_painting,
          brushSize: parseInt(size)
        }
      }
    }));
    console.log(`👥 Tamanho do pincel alterado para: ${size}`);
  }, []);
  const getToolName = reactExports.useCallback((tool) => {
    const toolNames = {
      brush: "Pincel",
      eraser: "Borracha",
      fill: "Balde de Tinta",
      line: "Linha",
      circle: "Círculo",
      rectangle: "Retângulo"
    };
    return toolNames[tool] || tool;
  }, []);
  const getToolIcon = reactExports.useCallback((tool) => {
    const toolIcons = {
      brush: "🖌️",
      eraser: "🧽",
      fill: "🪣",
      line: "📏",
      circle: "⭕",
      rectangle: "⬜"
    };
    return toolIcons[tool] || "🛠️";
  }, []);
  const redrawAssistedTemplate = reactExports.useCallback((templateId) => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext("2d");
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.strokeStyle = "#000000";
    ctx.lineWidth = 2;
    switch (templateId) {
      case "house":
        drawHouseTemplate(ctx);
        break;
      case "tree":
        drawTreeTemplate(ctx);
        break;
      case "sun":
        drawSunTemplate(ctx);
        break;
      case "flower":
        drawFlowerTemplate(ctx);
        break;
      case "car":
        drawCarTemplate(ctx);
        break;
      default:
        drawHouseTemplate(ctx);
    }
    console.log(`🖍️ Template ${templateId} redesenhado`);
  }, []);
  const findClickableArea = reactExports.useCallback((x, y) => {
    const areas = [
      {
        id: "area1",
        x: 100,
        y: 100,
        width: 100,
        height: 100
      },
      {
        id: "area2",
        x: 250,
        y: 100,
        width: 100,
        height: 100
      },
      {
        id: "area3",
        x: 400,
        y: 100,
        width: 100,
        height: 100
      }
      // Adicionar mais áreas conforme necessário
    ];
    return areas.find((area) => x >= area.x && x <= area.x + area.width && y >= area.y && y <= area.y + area.height);
  }, []);
  const fillArea = reactExports.useCallback((area, color) => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext("2d");
    ctx.fillStyle = color;
    ctx.fillRect(area.x, area.y, area.width, area.height);
    console.log(`🎨 Área ${area.id} preenchida com ${color}`);
  }, []);
  const renderClickableAreas = reactExports.useCallback((template) => {
    if (!template) return null;
    const areas = [{
      id: "area1",
      x: 100,
      y: 100,
      width: 100,
      height: 100
    }, {
      id: "area2",
      x: 250,
      y: 100,
      width: 100,
      height: 100
    }, {
      id: "area3",
      x: 400,
      y: 100,
      width: 100,
      height: 100
    }];
    return areas.map((area) => /* @__PURE__ */ React.createElement("div", { key: area.id, className: styles.clickableArea, style: {
      position: "absolute",
      left: area.x,
      top: area.y,
      width: area.width,
      height: area.height,
      border: "2px dashed rgba(255, 255, 255, 0.5)",
      cursor: "pointer"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1656,
      columnNumber: 7
    } }));
  }, []);
  const drawHouseTemplate = reactExports.useCallback((ctx) => {
    ctx.strokeRect(200, 250, 200, 150);
    ctx.beginPath();
    ctx.moveTo(180, 250);
    ctx.lineTo(300, 180);
    ctx.lineTo(420, 250);
    ctx.closePath();
    ctx.stroke();
    ctx.strokeRect(270, 320, 60, 80);
    ctx.strokeRect(220, 280, 40, 40);
    ctx.strokeRect(340, 280, 40, 40);
    console.log("🏠 Template de casa desenhado");
  }, []);
  const drawTreeTemplate = reactExports.useCallback((ctx) => {
    ctx.strokeRect(290, 300, 20, 100);
    ctx.beginPath();
    ctx.arc(300, 250, 80, 0, 2 * Math.PI);
    ctx.stroke();
    ctx.beginPath();
    ctx.moveTo(250, 220);
    ctx.lineTo(280, 240);
    ctx.moveTo(350, 220);
    ctx.lineTo(320, 240);
    ctx.stroke();
    console.log("🌳 Template de árvore desenhado");
  }, []);
  const drawSunTemplate = reactExports.useCallback((ctx) => {
    ctx.beginPath();
    ctx.arc(300, 200, 60, 0, 2 * Math.PI);
    ctx.stroke();
    const rayLength = 30;
    for (let i = 0; i < 8; i++) {
      const angle = i * Math.PI / 4;
      const startX = 300 + Math.cos(angle) * 70;
      const startY = 200 + Math.sin(angle) * 70;
      const endX = 300 + Math.cos(angle) * (70 + rayLength);
      const endY = 200 + Math.sin(angle) * (70 + rayLength);
      ctx.beginPath();
      ctx.moveTo(startX, startY);
      ctx.lineTo(endX, endY);
      ctx.stroke();
    }
    console.log("☀️ Template de sol desenhado");
  }, []);
  const drawFlowerTemplate = reactExports.useCallback((ctx) => {
    ctx.strokeRect(295, 300, 10, 100);
    ctx.beginPath();
    ctx.arc(300, 250, 20, 0, 2 * Math.PI);
    ctx.stroke();
    const petalCount = 6;
    for (let i = 0; i < petalCount; i++) {
      const angle = i * 2 * Math.PI / petalCount;
      const petalX = 300 + Math.cos(angle) * 40;
      const petalY = 250 + Math.sin(angle) * 40;
      ctx.beginPath();
      ctx.arc(petalX, petalY, 15, 0, 2 * Math.PI);
      ctx.stroke();
    }
    ctx.beginPath();
    ctx.ellipse(280, 320, 15, 25, -Math.PI / 4, 0, 2 * Math.PI);
    ctx.stroke();
    ctx.beginPath();
    ctx.ellipse(320, 320, 15, 25, Math.PI / 4, 0, 2 * Math.PI);
    ctx.stroke();
    console.log("🌸 Template de flor desenhado");
  }, []);
  const drawCarTemplate = reactExports.useCallback((ctx) => {
    ctx.strokeRect(150, 280, 300, 80);
    ctx.strokeRect(200, 240, 200, 40);
    ctx.beginPath();
    ctx.arc(200, 380, 30, 0, 2 * Math.PI);
    ctx.stroke();
    ctx.beginPath();
    ctx.arc(400, 380, 30, 0, 2 * Math.PI);
    ctx.stroke();
    ctx.strokeRect(220, 250, 60, 25);
    ctx.strokeRect(320, 250, 60, 25);
    ctx.beginPath();
    ctx.arc(460, 300, 15, 0, 2 * Math.PI);
    ctx.stroke();
    ctx.beginPath();
    ctx.arc(460, 340, 15, 0, 2 * Math.PI);
    ctx.stroke();
    console.log("🚗 Template de carro desenhado");
  }, []);
  const handleCanvasMouseDown = reactExports.useCallback((event) => {
    const rect = event.target.getBoundingClientRect();
    const point = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top,
      timestamp: Date.now(),
      pressure: event.pressure || 0.5
    };
    drawingRef.current.isDrawing = true;
    drawingRef.current.lastPoint = point;
    let currentColor, brushSize;
    switch (gameState.currentActivity) {
      case ACTIVITY_TYPES.FREE_PAINTING.id:
        currentColor = gameState.activityData.free_painting?.currentColor || "#000000";
        brushSize = gameState.activityData.free_painting?.brushSize || 5;
        break;
      case ACTIVITY_TYPES.CANVAS_PAINTING.id:
        currentColor = gameState.activityData.canvas_painting?.currentColor || "#000000";
        brushSize = gameState.activityData.canvas_painting?.brushSize || 10;
        break;
      default:
        currentColor = "#000000";
        brushSize = 5;
    }
    const newStroke = {
      id: v4(),
      points: [point],
      color: currentColor,
      brushSize,
      startTime: Date.now(),
      activity: gameState.currentActivity
    };
    setGameState((prev) => ({
      ...prev,
      strokes: [...prev.strokes, newStroke]
    }));
    console.log("🎨 Início do traço:", point);
  }, [gameState.currentActivity, gameState.activityData]);
  const handleCanvasMouseMove = reactExports.useCallback((event) => {
    if (!drawingRef.current.isDrawing) return;
    const rect = event.target.getBoundingClientRect();
    const point = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top,
      timestamp: Date.now(),
      pressure: event.pressure || 0.5
    };
    let currentColor, brushSize, brushType;
    switch (gameState.currentActivity) {
      case ACTIVITY_TYPES.FREE_PAINTING.id:
        currentColor = gameState.activityData.free_painting?.currentColor || "#000000";
        brushSize = gameState.activityData.free_painting?.brushSize || 5;
        brushType = "round";
        break;
      case ACTIVITY_TYPES.CANVAS_PAINTING.id:
        currentColor = gameState.activityData.canvas_painting?.currentColor || "#000000";
        brushSize = gameState.activityData.canvas_painting?.brushSize || 10;
        brushType = gameState.activityData.canvas_painting?.brushType || "round";
        break;
      default:
        currentColor = "#000000";
        brushSize = 5;
        brushType = "round";
    }
    setGameState((prev) => {
      const strokes = [...prev.strokes];
      const lastStroke = strokes[strokes.length - 1];
      if (lastStroke) {
        lastStroke.points.push(point);
      }
      return {
        ...prev,
        strokes
      };
    });
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext("2d");
      ctx.strokeStyle = currentColor;
      ctx.lineWidth = brushSize;
      ctx.lineCap = brushType === "round" ? "round" : "square";
      ctx.lineJoin = "round";
      if (drawingRef.current.lastPoint) {
        ctx.beginPath();
        ctx.moveTo(drawingRef.current.lastPoint.x, drawingRef.current.lastPoint.y);
        ctx.lineTo(point.x, point.y);
        ctx.stroke();
      }
    }
    drawingRef.current.lastPoint = point;
  }, [gameState.currentActivity, gameState.activityData]);
  const handleCanvasMouseUp = reactExports.useCallback(() => {
    if (!drawingRef.current.isDrawing) return;
    drawingRef.current.isDrawing = false;
    drawingRef.current.lastPoint = null;
    const lastStroke = gameState.strokes && gameState.strokes.length > 0 ? gameState.strokes[gameState.strokes.length - 1] : null;
    if (lastStroke) {
      const strokeData = {
        ...lastStroke,
        duration: Date.now() - lastStroke.startTime
      };
      if (gameState.currentActivity === ACTIVITY_TYPES.CANVAS_PAINTING.id) {
        const canvas = canvasRef.current;
        if (canvas) {
          const dataURL = canvas.toDataURL();
          setGameState((prev) => {
            const canvasData = prev.activityData.canvas_painting;
            const newHistory = [...canvasData.canvasHistory.slice(0, canvasData.historyIndex + 1), dataURL];
            return {
              ...prev,
              activityData: {
                ...prev.activityData,
                canvas_painting: {
                  ...canvasData,
                  canvasHistory: newHistory,
                  historyIndex: newHistory.length - 1
                }
              }
            };
          });
        }
      }
      collectMetrics({
        type: "stroke_completed",
        activity: gameState.currentActivity,
        duration: strokeData.duration,
        points: strokeData.points.length,
        timestamp: Date.now()
      });
    }
    console.log("🎨 Fim do traço");
  }, [gameState.strokes, gameState.currentActivity, collectMetrics]);
  const handleCanvasTouchStart = reactExports.useCallback((event) => {
    event.preventDefault();
    const touch = event.touches[0];
    const mouseEvent = new MouseEvent("mousedown", {
      clientX: touch.clientX,
      clientY: touch.clientY
    });
    handleCanvasMouseDown(mouseEvent);
  }, [handleCanvasMouseDown]);
  const handleCanvasTouchMove = reactExports.useCallback((event) => {
    event.preventDefault();
    const touch = event.touches[0];
    const mouseEvent = new MouseEvent("mousemove", {
      clientX: touch.clientX,
      clientY: touch.clientY
    });
    handleCanvasMouseMove(mouseEvent);
  }, [handleCanvasMouseMove]);
  const handleCanvasTouchEnd = reactExports.useCallback((event) => {
    event.preventDefault();
    handleCanvasMouseUp();
  }, [handleCanvasMouseUp]);
  const backToStart = reactExports.useCallback(() => {
    setShowStartScreen(true);
  }, []);
  reactExports.useCallback((color) => {
    setGameState((prev) => ({
      ...prev,
      currentColor: color,
      colorsUsed: /* @__PURE__ */ new Set([...prev.colorsUsed, color])
    }));
  }, []);
  reactExports.useCallback((brush) => {
    setGameState((prev) => ({
      ...prev,
      currentBrush: brush
    }));
  }, []);
  reactExports.useCallback((size) => {
    setGameState((prev) => ({
      ...prev,
      brushSize: parseInt(size)
    }));
  }, []);
  const selectTemplate = reactExports.useCallback((template) => {
    setGameState((prev) => ({
      ...prev,
      selectedTemplate: template
    }));
    if (template === "blank") {
      clearCanvas();
    } else {
      console.log(`Template selecionado: ${template}`);
    }
  }, []);
  const recordBrushStroke = reactExports.useCallback(async (strokeData) => {
    try {
      if (!recordMultisensoryInteraction || !collectorsHub) return;
      await recordMultisensoryInteraction("brush_stroke", {
        interactionType: "creative_action",
        gameSpecificData: {
          ...strokeData,
          sessionId: `creativePainting_${Date.now()}`,
          timestamp: Date.now(),
          activityType: "painting",
          difficulty: currentDifficulty,
          brushMetrics: {
            color: gameState.currentColor,
            size: gameState.brushSize,
            brush: gameState.currentBrush
          }
        },
        multisensoryProcessing: {
          visualProcessing: {
            colorPerception: 0.8,
            spatialAwareness: 0.7,
            visualAttention: 0.9
          },
          motorProcessing: {
            fineMotoSkills: 0.8,
            handEyeCoordination: 0.9,
            movementPrecision: 0.7
          },
          cognitiveProcessing: {
            creativity: 0.9,
            decisionMaking: 0.8,
            patternRecognition: 0.6
          }
        }
      });
      setCreativityMetrics((prev) => ({
        ...prev,
        totalStrokes: prev.totalStrokes + 1,
        lastStrokeTime: Date.now()
      }));
      console.log("🎨 Pincelada registrada:", strokeData);
    } catch (error) {
      console.warn("⚠️ Erro ao registrar pincelada:", error);
    }
  }, [recordMultisensoryInteraction, collectorsHub, currentDifficulty, gameState.currentColor, gameState.brushSize, gameState.currentBrush]);
  const startDrawing = reactExports.useCallback((event) => {
    const rect = canvasRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    drawingRef.current.isDrawing = true;
    drawingRef.current.lastPoint = {
      x,
      y
    };
    setGameState((prev) => ({
      ...prev,
      undoStack: [...prev.undoStack, [...prev.strokes]],
      redoStack: [],
      // Limpar redo stack
      showPlaceholder: false
    }));
    const strokeStart = {
      startPoint: {
        x,
        y
      },
      startTime: Date.now(),
      path: [{
        x,
        y
      }]
    };
    createStroke(x, y, strokeStart);
  }, []);
  const draw = reactExports.useCallback((event) => {
    if (!drawingRef.current.isDrawing) return;
    const rect = canvasRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    const currentStroke = {
      path: [{
        x,
        y
      }],
      velocity: calculateVelocity(drawingRef.current.lastPoint, {
        x,
        y
      }),
      direction: calculateDirection(drawingRef.current.lastPoint, {
        x,
        y
      })
    };
    createStroke(x, y, currentStroke);
    drawingRef.current.lastPoint = {
      x,
      y
    };
  }, []);
  const stopDrawing = reactExports.useCallback(() => {
    if (drawingRef.current.isDrawing) {
      const strokeData = {
        endTime: Date.now(),
        path: [],
        // Path seria coletado durante o desenho
        pressure: 1,
        // Simplificado para mouse
        velocity: 0,
        direction: 0
      };
      recordBrushStroke(strokeData);
    }
    drawingRef.current.isDrawing = false;
    drawingRef.current.lastPoint = null;
  }, [recordBrushStroke]);
  const createStroke = reactExports.useCallback((x, y, metricsData = {}) => {
    const strokeData = {
      x,
      y,
      color: gameState.currentColor,
      size: gameState.brushSize,
      brush: gameState.currentBrush,
      id: Date.now() + Math.random()
    };
    setGameState((prev) => ({
      ...prev,
      strokes: [...prev.strokes, strokeData]
    }));
  }, [gameState.currentColor, gameState.brushSize, gameState.currentBrush]);
  reactExports.useCallback(() => {
    setGameState((prev) => {
      if (prev.undoStack.length === 0) return prev;
      const previousState = prev.undoStack[prev.undoStack.length - 1];
      return {
        ...prev,
        strokes: previousState,
        undoStack: prev.undoStack.slice(0, -1),
        redoStack: [...prev.redoStack, prev.strokes],
        showPlaceholder: previousState.length === 0
      };
    });
  }, []);
  reactExports.useCallback(() => {
    setGameState((prev) => {
      if (prev.redoStack.length === 0) return prev;
      const nextState = prev.redoStack[prev.redoStack.length - 1];
      return {
        ...prev,
        strokes: nextState,
        redoStack: prev.redoStack.slice(0, -1),
        undoStack: [...prev.undoStack, prev.strokes],
        showPlaceholder: nextState.length === 0
      };
    });
  }, []);
  const clearCanvas = reactExports.useCallback(() => {
    setGameState((prev) => ({
      ...prev,
      strokes: [],
      undoStack: [...prev.undoStack, prev.strokes],
      redoStack: [],
      showPlaceholder: true
    }));
  }, []);
  const collectCreativeMetrics = reactExports.useCallback(async () => {
    try {
      const currentTime = Date.now();
      const sessionDuration = sessionStartTime ? currentTime - sessionStartTime : 0;
      const colorCount = new Set(colorTransitions.map((t) => t.color)).size;
      const strokeComplexity = brushStrokes.length > 0 ? brushStrokes.reduce((acc, stroke) => acc + stroke.points?.length || 0, 0) / brushStrokes.length : 0;
      const creativeData = {
        sessionDuration,
        totalStrokes: creativityMetrics2.totalStrokes || brushStrokes.length,
        colorVariety: colorCount,
        strokeComplexity,
        difficulty: currentDifficulty,
        originalityScore: creativityMetrics2.originalityScore,
        complexityScore: creativityMetrics2.complexityScore,
        expressiveRange: creativityMetrics2.expressiveRange,
        spatialUtilization: creativityMetrics2.spatialUtilization,
        brushTypes: gameState.brushTypes?.length || 0,
        canvasUtilization: Math.min(100, brushStrokes.length / 50 * 100),
        timestamp: currentTime
      };
      await processAdvancedMetrics(creativeData);
      await collectMetrics("creative_painting", creativeData);
      console.log("🎨 Métricas criativas processadas:", creativeData);
      return creativeData;
    } catch (error) {
      console.error("❌ Erro ao coletar métricas criativas:", error);
      throw error;
    }
  }, [sessionStartTime, colorTransitions, brushStrokes, creativityMetrics2, currentDifficulty, gameState, processAdvancedMetrics, collectMetrics]);
  reactExports.useCallback(async () => {
    setGameState((prev) => ({
      ...prev,
      savedCount: prev.savedCount + 1
    }));
    console.log("🎨 Desenho salvo!");
    try {
      await collectCreativeMetrics();
      console.log("📊 Métricas criativas coletadas ao salvar!");
    } catch (error) {
      console.error("❌ Erro ao coletar métricas:", error);
    }
    alert("💾 Obra salva com sucesso!");
  }, [collectCreativeMetrics]);
  reactExports.useCallback(() => {
    console.log("Compartilhando desenho...");
    alert("📤 Funcionalidade de compartilhamento será implementada na versão final!");
  }, []);
  reactExports.useCallback(async () => {
    try {
      console.log("🎨 Finalizando sessão criativa...");
      if (gameState.strokes && gameState.strokes.length > 0) {
        await collectCreativeMetrics();
        console.log("📊 Métricas finais coletadas");
      }
      if (collectorsHub && collectorsHub.initialized) {
        console.log("🔄 Finalizando coletores...");
      }
      if (onBack) {
        onBack();
      }
    } catch (error) {
      console.error("❌ Erro ao finalizar sessão criativa:", error);
      if (onBack) {
        onBack();
      }
    }
  }, [gameState.strokes, collectCreativeMetrics, collectorsHub, onBack]);
  reactExports.useCallback(() => {
    console.log("Imprimindo desenho...");
    alert("🖨️ Funcionalidade de impressão será implementada na versão final!");
  }, []);
  reactExports.useCallback(() => {
    if (gameState.strokes && gameState.strokes.length > 0) {
      if (confirm("🎨 Tem certeza que deseja criar uma nova obra?\n\nO desenho atual será perdido se não foi salvo.")) {
        clearCanvas();
        selectTemplate("blank");
      }
    }
  }, [gameState.strokes ? gameState.strokes.length : 0, clearCanvas, selectTemplate]);
  reactExports.useCallback((event) => {
    event.preventDefault();
    const touch = event.touches[0];
    const mouseEvent = new MouseEvent("mousedown", {
      clientX: touch.clientX,
      clientY: touch.clientY
    });
    startDrawing(mouseEvent);
  }, [startDrawing]);
  reactExports.useCallback((event) => {
    event.preventDefault();
    const touch = event.touches[0];
    const mouseEvent = new MouseEvent("mousemove", {
      clientX: touch.clientX,
      clientY: touch.clientY
    });
    draw(mouseEvent);
  }, [draw]);
  reactExports.useCallback((event) => {
    event.preventDefault();
    stopDrawing();
  }, [stopDrawing]);
  if (showStartScreen) {
    return /* @__PURE__ */ React.createElement(GameStartScreen, { gameTitle: "Pintura Criativa", gameDescription: "Expresse sua criatividade com ferramentas de pintura digital", gameIcon: "🎨", difficulties: [{
      id: "easy",
      name: "Fácil",
      description: "Ferramentas básicas\nIdeal para iniciantes",
      icon: "😊"
    }, {
      id: "medium",
      name: "Médio",
      description: "Mais opções\nDesafio equilibrado",
      icon: "🎯"
    }, {
      id: "hard",
      name: "Avançado",
      description: "Todas as ferramentas\nPara especialistas",
      icon: "🚀"
    }], onStart: (difficulty) => initializeGame(difficulty), onBack, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 2346,
      columnNumber: 7
    } });
  }
  return /* @__PURE__ */ React.createElement("div", { className: styles.creativePaintingGame, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2378,
    columnNumber: 5
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.gameContent, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2379,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.gameHeader, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2381,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("h1", { className: styles.gameTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2382,
    columnNumber: 11
  } }, "🎨 Pintura Criativa V3", /* @__PURE__ */ React.createElement("div", { style: {
    fontSize: "0.7rem",
    opacity: 0.8,
    marginTop: "0.25rem"
  }, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2384,
    columnNumber: 13
  } }, ACTIVITY_TYPES[gameState.currentActivity.toUpperCase()]?.name || "Pintura Livre")), /* @__PURE__ */ React.createElement("button", { className: `${styles.headerTtsButton} ${ttsActive2 ? styles.ttsActive : ""}`, onClick: toggleTTS, title: ttsActive2 ? "Desativar TTS" : "Ativar TTS", "aria-label": ttsActive2 ? "Desativar TTS" : "Ativar TTS", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2388,
    columnNumber: 11
  } }, ttsActive2 ? "🔊" : "🔇")), /* @__PURE__ */ React.createElement("div", { className: styles.gameStats, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2399,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statCard, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2400,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statValue, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2401,
    columnNumber: 13
  } }, gameState.strokes ? gameState.strokes.length : 0), /* @__PURE__ */ React.createElement("div", { className: styles.statLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2402,
    columnNumber: 13
  } }, "Pinceladas")), /* @__PURE__ */ React.createElement("div", { className: styles.statCard, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2404,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statValue, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2405,
    columnNumber: 13
  } }, gameState.colorsUsed ? gameState.colorsUsed.size : 0), /* @__PURE__ */ React.createElement("div", { className: styles.statLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2406,
    columnNumber: 13
  } }, "Cores")), /* @__PURE__ */ React.createElement("div", { className: styles.statCard, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2408,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statValue, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2409,
    columnNumber: 13
  } }, getElapsedTime()), /* @__PURE__ */ React.createElement("div", { className: styles.statLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2410,
    columnNumber: 13
  } }, "Tempo"))), /* @__PURE__ */ React.createElement("div", { className: styles.activityMenu, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2415,
    columnNumber: 9
  } }, Object.values(ACTIVITY_TYPES).map((activity) => /* @__PURE__ */ React.createElement("button", { key: activity.id, className: `${styles.activityButton} ${gameState.currentActivity === activity.id ? styles.active : ""}`, onClick: () => switchActivity(activity.id), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2417,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2424,
    columnNumber: 15
  } }, activity.icon), /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2425,
    columnNumber: 15
  } }, activity.name)))), gameState.currentActivity === ACTIVITY_TYPES.FREE_PAINTING.id && renderFreePainting(), gameState.currentActivity === ACTIVITY_TYPES.ASSISTED_PAINTING.id && renderAssistedPainting(), gameState.currentActivity === ACTIVITY_TYPES.CANVAS_PAINTING.id && renderCanvasPainting(), gameState.currentActivity === ACTIVITY_TYPES.PATTERN_PAINTING.id && renderPatternPainting(), /* @__PURE__ */ React.createElement("div", { className: styles.gameControls, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2437,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.controlsGroup, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2438,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("button", { className: styles.controlButton, onClick: () => speak("Pintura criativa. Use cores e pincéis para expressar sua criatividade e desenvolver coordenação motora."), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2439,
    columnNumber: 13
  } }, "🔊 Explicar"), /* @__PURE__ */ React.createElement("button", { className: styles.controlButton, onClick: () => speak("Repita as instruções da atividade atual."), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2442,
    columnNumber: 13
  } }, "🔄 Repetir")), /* @__PURE__ */ React.createElement("div", { className: styles.controlsGroup, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2447,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("button", { className: styles.controlButton, onClick: backToStart, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2448,
    columnNumber: 13
  } }, "🔄 Reiniciar"), /* @__PURE__ */ React.createElement("button", { className: styles.controlButton, onClick: onBack, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 2451,
    columnNumber: 13
  } }, "⬅️ Voltar")))));
}
const calculateVelocity = (point1, point2) => {
  if (!point1 || !point2) return 0;
  const distance = Math.sqrt(Math.pow(point2.x - point1.x, 2) + Math.pow(point2.y - point1.y, 2));
  return distance / 16;
};
const calculateDirection = (point1, point2) => {
  if (!point1 || !point2) return 0;
  return Math.atan2(point2.y - point1.y, point2.x - point1.x) * (180 / Math.PI);
};
const CreativePaintingGame$1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: CreativePaintingGame
}, Symbol.toStringTag, { value: "Module" }));
export {
  CreativePaintingProcessors as C,
  CreativePaintingCollectorsHub as a,
  CreativePaintingGame$1 as b
};
//# sourceMappingURL=game-creative-CnQ0MOtQ.js.map
