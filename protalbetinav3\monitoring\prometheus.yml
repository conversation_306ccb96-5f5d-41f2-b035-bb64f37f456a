# Configuração do Prometheus para Portal Betina V3
# Monitora frontend, API e métricas personalizadas

global:
  scrape_interval: 15s
  evaluation_interval: 15s

# Regras de alertas (podem ser expandidas conforme necessário)
rule_files:
  # - "alert_rules.yml"

# Configuração de scraping
scrape_configs:
  - job_name: 'api'
    metrics_path: '/api/metrics'
    static_configs:
      - targets: ['api:3000']

  - job_name: 'frontend'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['frontend:80']

  - job_name: 'docker'
    static_configs:
      - targets: ['host.docker.internal:9323']

  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
