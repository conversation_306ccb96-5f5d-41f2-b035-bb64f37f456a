// vite.config.js
import { defineConfig } from "file:///C:/Projetos/protalbetinav3/node_modules/vite/dist/node/index.js";
import react from "file:///C:/Projetos/protalbetinav3/node_modules/@vitejs/plugin-react/dist/index.mjs";
import { resolve } from "path";
import { visualizer } from "file:///C:/Projetos/protalbetinav3/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";
var __vite_injected_original_dirname = "C:\\Projetos\\protalbetinav3";
var vite_config_default = defineConfig({
  plugins: [
    react(),
    visualizer({
      filename: "dist/stats.html",
      open: false,
      gzipSize: true,
      brotliSize: true
    })
  ],
  resolve: {
    alias: {
      "@": resolve(__vite_injected_original_dirname, "src"),
      "@components": resolve(__vite_injected_original_dirname, "src/components"),
      "@hooks": resolve(__vite_injected_original_dirname, "src/hooks"),
      "@utils": resolve(__vite_injected_original_dirname, "src/utils"),
      "@styles": resolve(__vite_injected_original_dirname, "src/styles"),
      "@games": resolve(__vite_injected_original_dirname, "src/games"),
      "@api": resolve(__vite_injected_original_dirname, "src/api")
    }
  },
  // Otimizações de dependências (unificado)
  optimizeDeps: {
    include: [
      "react",
      "react-dom",
      "react-router-dom",
      "chart.js",
      "react-chartjs-2",
      "framer-motion",
      "styled-components",
      "lodash",
      "moment",
      "date-fns",
      "uuid",
      "axios"
    ],
    exclude: [
      "@api",
      "src/api/services/core/SystemOrchestrator.js",
      "src/api/services/analysis/*",
      "src/api/services/processors/*",
      "pg",
      "redis",
      "winston",
      "nodemon",
      "express"
    ]
  },
  server: {
    port: 5173,
    host: true,
    proxy: {
      "/api": {
        target: "http://localhost:8080",
        changeOrigin: true
      }
    }
  },
  build: {
    outDir: "dist",
    assetsDir: "assets",
    minify: "terser",
    sourcemap: false,
    emptyOutDir: true,
    target: "es2020",
    // Target unificado
    cssCodeSplit: true,
    chunkSizeWarningLimit: 800,
    // Aumentar limite de warning para 800kb
    rollupOptions: {
      external: [
        "pg",
        "redis",
        "winston",
        "express",
        "nodemon",
        "bcrypt",
        "bcryptjs",
        "jsonwebtoken",
        "helmet",
        "compression",
        "cors",
        "rate-limiter-flexible"
      ],
      output: {
        format: "es",
        entryFileNames: "assets/[name]-[hash].js",
        chunkFileNames: "assets/[name]-[hash].js",
        assetFileNames: "assets/[name]-[hash].[ext]",
        manualChunks: {
          // React ecosystem
          react: ["react", "react-dom"],
          "react-router": ["react-router-dom"],
          "react-query": ["@tanstack/react-query"],
          // Chart libraries (já grande, pode separar mais)
          "chart-core": ["chart.js"],
          "chart-react": ["react-chartjs-2"],
          // UI libraries (grande, separar)
          "framer-motion": ["framer-motion"],
          "styled-components": ["styled-components"],
          // Utility libraries
          "lodash": ["lodash"],
          "date-utils": ["moment", "date-fns"],
          "uuid": ["uuid"],
          // HTTP and data fetching
          "axios": ["axios"],
          // Validation (estava vazio, remover)
          // validation: ['joi', 'zod'],
          // Context providers
          "helmet": ["react-helmet-async"],
          // Prop validation
          "prop-types": ["prop-types"]
        }
      }
    },
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        dead_code: true,
        unused: true
      },
      mangle: {
        safari10: true
      },
      format: {
        comments: false
      }
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
