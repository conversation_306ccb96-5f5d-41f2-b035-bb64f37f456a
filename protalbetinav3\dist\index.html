<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Portal Betina</title>
    <style>
      /* Inline critical CSS for initial loading state */
      .initial-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        width: 100%;
        background-color: #f8f9fa;
        font-family: Arial, sans-serif;
      }
      .initial-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        border-top-color: #4A90E2;
        animation: initial-spin 1s ease-in-out infinite;
        margin-bottom: 20px;
      }
      @keyframes initial-spin {
        to { transform: rotate(360deg); }
      }
    </style>
    <script type="module" crossorigin src="/assets/index-C53L1-Ks.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-utils-CjlX8hrF.js">
    <link rel="modulepreload" crossorigin href="/assets/hooks-DiB_syzW.js">
    <link rel="modulepreload" crossorigin href="/assets/context-CJb-Kg-5.js">
    <link rel="modulepreload" crossorigin href="/assets/vendor-charts-JJkNskvH.js">
    <link rel="modulepreload" crossorigin href="/assets/admin-AVDlea_I.js">
    <link rel="modulepreload" crossorigin href="/assets/utils-C8SspVp8.js">
    <link rel="modulepreload" crossorigin href="/assets/dashboard-D6oq-mHv.js">
    <link rel="modulepreload" crossorigin href="/assets/game-association-1fe4bzjE.js">
    <link rel="modulepreload" crossorigin href="/assets/game-colors-DknHlpST.js">
    <link rel="modulepreload" crossorigin href="/assets/game-letters-C11f_WoE.js">
    <link rel="modulepreload" crossorigin href="/assets/vendor-motion-CThs1zaH.js">
    <link rel="modulepreload" crossorigin href="/assets/game-memory-Q6N7kS_-.js">
    <link rel="modulepreload" crossorigin href="/assets/game-musical-4K52sZ4i.js">
    <link rel="modulepreload" crossorigin href="/assets/game-patterns-C1u1YIjS.js">
    <link rel="modulepreload" crossorigin href="/assets/game-puzzle-y0iWrjae.js">
    <link rel="modulepreload" crossorigin href="/assets/game-numbers-CLPWZorL.js">
    <link rel="modulepreload" crossorigin href="/assets/game-creative-danUmc-P.js">
    <link rel="modulepreload" crossorigin href="/assets/services-Ckq1alRq.js">
    <link rel="modulepreload" crossorigin href="/assets/vendor-misc-BhjEiCpb.js">
    <link rel="modulepreload" crossorigin href="/assets/vendor-react-BH-kks1U.js">
    <link rel="modulepreload" crossorigin href="/assets/vendor-query-WfQzIUQA.js">
    <link rel="stylesheet" crossorigin href="/assets/admin-BQPZfmpR.css">
    <link rel="stylesheet" crossorigin href="/assets/dashboard-C4HFo8oW.css">
    <link rel="stylesheet" crossorigin href="/assets/game-association-aRlfWsTT.css">
    <link rel="stylesheet" crossorigin href="/assets/game-colors-WFKpsHgQ.css">
    <link rel="stylesheet" crossorigin href="/assets/game-letters-CgoS80mu.css">
    <link rel="stylesheet" crossorigin href="/assets/game-memory-BTGvJ0Wb.css">
    <link rel="stylesheet" crossorigin href="/assets/game-musical-Srt0Rn1x.css">
    <link rel="stylesheet" crossorigin href="/assets/game-patterns-C7lF44VH.css">
    <link rel="stylesheet" crossorigin href="/assets/game-puzzle-jC3b1JUV.css">
    <link rel="stylesheet" crossorigin href="/assets/game-numbers-CWskqaet.css">
    <link rel="stylesheet" crossorigin href="/assets/game-creative-ByDHG8hJ.css">
    <link rel="stylesheet" crossorigin href="/assets/index-DmdqTW8r.css">
  </head>
  <body>
    <div id="root">
      <!-- Initial loading state before React hydrates -->
      <div class="initial-loading">
        <div class="initial-spinner"></div>
        <p>Carregando Portal Betina...</p>
      </div>
    </div>
    <!-- Use module preload to speed up loading -->
    <link rel="modulepreload" href="data:text/jsx;base64,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">

  </body>
</html>
