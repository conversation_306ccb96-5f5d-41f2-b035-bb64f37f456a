# 🔌 AUDITORIA DOS ENDPOINTS DA API - Portal Betina V3

**Data:** 2025-07-05T04:41:25.008Z
**Total de arquivos:** 42
**Total de endpoints:** 137

## 📂 Resumo por Categoria

- **root:** 1 arquivos, 6 endpoints
- **analytics:** 4 arquivos, 8 endpoints
- **auth:** 4 arquivos, 4 endpoints
- **dashboard:** 7 arquivos, 17 endpoints
- **debug:** 1 arquivos, 2 endpoints
- **metrics:** 7 arquivos, 23 endpoints
- **premium:** 5 arquivos, 29 endpoints
- **profiles:** 4 arquivos, 23 endpoints
- **public:** 4 arquivos, 11 endpoints
- **reports:** 5 arquivos, 14 endpoints

## 🔒 Issues de Segurança

- **ai-reports.js:** Endpoints POST sem rate limiting detectado (medium)
- **insights.js:** Endpoints POST sem rate limiting detectado (medium)
- **insights.js:** Endpoints de escrita sem validação de entrada detectado (medium)
- **predictions.js:** Endpoints POST sem rate limiting detectado (medium)
- **predictions.js:** Endpoints de escrita sem validação de entrada detectado (medium)

## ⚡ Issues de Confiabilidade

- **dashboard.js:** Ausência de tratamento de erros (high)
- **engagement.js:** Ausência de tratamento de erros (high)
- **performance.js:** Ausência de tratamento de erros (high)
- **health.js:** Ausência de tratamento de erros (high)

## 💡 Recomendações

### Implementar validação e rate limiting (high)
Vários endpoints carecem de validação adequada e rate limiting para prevenir ataques

### Implementar tratamento de erros consistente (high)
Alguns endpoints não possuem tratamento adequado de erros


## 🔌 Endpoints Detalhados

### ROOT

#### src\api\routes\ai-reports.js

- `GET /api/root/insights/:childId`
- `GET /api/root/health`
- `GET /api/root/config`
- `POST /api/root/generate`
- `POST /api/root/analyze-metrics`
- `POST /api/root/dashboard`

### ANALYTICS

#### src\api\routes\analytics\comparisons.js

- `GET /api/analytics`

#### src\api\routes\analytics\insights.js

- `GET /api/analytics`
- `GET /api/analytics/:childId/history`
- `GET /api/analytics`
- `POST /api/analytics/custom`

#### src\api\routes\analytics\predictions.js

- `GET /api/analytics`
- `POST /api/analytics/scenario`

#### src\api\routes\analytics\trends.js

- `GET /api/analytics`

### AUTH

#### src\api\routes\auth\login.js

- `POST /api/auth`

#### src\api\routes\auth\logout.js

- `POST /api/auth`

#### src\api\routes\auth\refresh.js

- `POST /api/auth`

#### src\api\routes\auth\verify.js

- `GET /api/auth`

### DASHBOARD

#### src\api\routes\dashboard\accessibility.js

- `GET /api/dashboard`
- `POST /api/dashboard/audit`

#### src\api\routes\dashboard\behavior.js

- `GET /api/dashboard`
- `GET /api/dashboard/patterns`

#### src\api\routes\dashboard\cache.js

- `GET /api/dashboard`
- `POST /api/dashboard`
- `DELETE /api/dashboard`

#### src\api\routes\dashboard\overview-fixed.js

- `GET /api/dashboard`
- `GET /api/dashboard/summary`
- `GET /api/dashboard/health`
- `POST /api/dashboard/refresh`

#### src\api\routes\dashboard\overview.js

- `GET /api/dashboard`
- `GET /api/dashboard/summary`

#### src\api\routes\dashboard\progress.js

- `GET /api/dashboard`
- `GET /api/dashboard/detailed/:skillArea`

#### src\api\routes\dashboard\therapeutic.js

- `GET /api/dashboard`
- `GET /api/dashboard/goals`

### DEBUG

#### src\api\routes\debug\metrics-monitor.js

- `GET /api/debug`
- `POST /api/debug`

### METRICS

#### src\api\routes\metrics\dashboard.js

- `GET /api/metrics`

#### src\api\routes\metrics\engagement.js

- `GET /api/metrics`

#### src\api\routes\metrics\game-sessions.js

- `GET /api/metrics/:sessionId`
- `GET /api/metrics`
- `POST /api/metrics`
- `POST /api/metrics/:sessionId/complete`
- `PUT /api/metrics/:sessionId`

#### src\api\routes\metrics\interactions.js

- `GET /api/metrics/session/:sessionId`
- `GET /api/metrics/analysis/:sessionId`
- `GET /api/metrics`
- `POST /api/metrics`
- `POST /api/metrics/batch`
- `DELETE /api/metrics/session/:sessionId`

#### src\api\routes\metrics\multisensory.js

- `GET /api/metrics/session/:sessionId`
- `GET /api/metrics/analysis/:sessionId`
- `GET /api/metrics/patterns/:sessionId`
- `GET /api/metrics`
- `POST /api/metrics`
- `POST /api/metrics/stream`
- `POST /api/metrics/calibrate`

#### src\api\routes\metrics\performance.js

- `GET /api/metrics`

#### src\api\routes\metrics\progress.js

- `GET /api/metrics`
- `POST /api/metrics`

### PREMIUM

#### src\api\routes\premium\auth.js

- `GET /api/premium/status`
- `POST /api/premium/upgrade`
- `POST /api/premium/cancel`

#### src\api\routes\premium\dashboard.js

- `GET /api/premium`
- `GET /api/premium/analytics`
- `GET /api/premium/insights`
- `POST /api/premium/custom-report`

#### src\api\routes\premium\insights.js

- `GET /api/premium`
- `GET /api/premium/cognitive`
- `GET /api/premium/behavioral`
- `GET /api/premium/therapeutic`
- `GET /api/premium/predictions`
- `POST /api/premium/generate`

#### src\api\routes\premium\profiles.js

- `GET /api/premium`
- `GET /api/premium/cognitive`
- `GET /api/premium/therapeutic`
- `GET /api/premium/achievements`
- `POST /api/premium/cognitive/assessment`
- `POST /api/premium/export`
- `PUT /api/premium`
- `PUT /api/premium/therapeutic`

#### src\api\routes\premium\reports.js

- `GET /api/premium`
- `GET /api/premium/:reportId`
- `GET /api/premium/:reportId/download`
- `POST /api/premium/generate`
- `POST /api/premium/comprehensive`
- `POST /api/premium/comparative`
- `POST /api/premium/therapeutic`
- `DELETE /api/premium/:reportId`

### PROFILES

#### src\api\routes\profiles\children.js

- `GET /api/profiles`
- `GET /api/profiles/:childId`
- `POST /api/profiles`
- `PUT /api/profiles/:childId`
- `DELETE /api/profiles/:childId`

#### src\api\routes\profiles\preferences.js

- `GET /api/profiles`
- `GET /api/profiles/defaults`
- `GET /api/profiles/export`
- `POST /api/profiles/reset`
- `POST /api/profiles/import`
- `PUT /api/profiles`

#### src\api\routes\profiles\therapists.js

- `GET /api/profiles`
- `GET /api/profiles/:therapistId`
- `GET /api/profiles/:therapistId/statistics`
- `GET /api/profiles/:therapistId/schedule`
- `PUT /api/profiles`

#### src\api\routes\profiles\users.js

- `GET /api/profiles`
- `GET /api/profiles/activity`
- `GET /api/profiles/active`
- `POST /api/profiles`
- `PUT /api/profiles`
- `PUT /api/profiles/password`
- `DELETE /api/profiles`

### PUBLIC

#### src\api\routes\public\activities.js

- `GET /api/public`
- `GET /api/public/:id`
- `GET /api/public/category/:category`
- `GET /api/public/neurodivergence/:type`

#### src\api\routes\public\games.js

- `GET /api/public`
- `GET /api/public/:id`
- `GET /api/public/category/:category`

#### src\api\routes\public\health.js

- `GET /api/public`
- `GET /api/public/detailed`

#### src\api\routes\public\metrics.js

- `GET /api/public`
- `POST /api/public`

### REPORTS

#### src\api\routes\reports\behavior.js

- `GET /api/reports`
- `GET /api/reports/:childId/patterns`

#### src\api\routes\reports\export.js

- `GET /api/reports/:exportId/download`
- `GET /api/reports/templates`
- `GET /api/reports/history/:childId`
- `GET /api/reports`
- `POST /api/reports`

#### src\api\routes\reports\goals.js

- `GET /api/reports`
- `POST /api/reports`
- `PUT /api/reports/:goalId`

#### src\api\routes\reports\progress.js

- `GET /api/reports`
- `GET /api/reports/:childId/chart-data`

#### src\api\routes\reports\therapeutic.js

- `GET /api/reports`
- `GET /api/reports/:childId/summary`


## 📊 Dados da Auditoria (JSON)

```json
{
  "summary": {
    "totalFiles": 42,
    "totalEndpoints": 137,
    "categoriesFound": [
      "root",
      "analytics",
      "auth",
      "dashboard",
      "debug",
      "metrics",
      "premium",
      "profiles",
      "public",
      "reports"
    ],
    "securityIssues": [
      {
        "file": "ai-reports.js",
        "type": "security",
        "severity": "medium",
        "message": "Endpoints POST sem rate limiting detectado"
      },
      {
        "file": "insights.js",
        "type": "security",
        "severity": "medium",
        "message": "Endpoints POST sem rate limiting detectado"
      },
      {
        "file": "insights.js",
        "type": "security",
        "severity": "medium",
        "message": "Endpoints de escrita sem validação de entrada detectado"
      },
      {
        "file": "predictions.js",
        "type": "security",
        "severity": "medium",
        "message": "Endpoints POST sem rate limiting detectado"
      },
      {
        "file": "predictions.js",
        "type": "security",
        "severity": "medium",
        "message": "Endpoints de escrita sem validação de entrada detectado"
      }
    ],
    "performanceIssues": [
      {
        "file": "dashboard.js",
        "type": "reliability",
        "severity": "high",
        "message": "Ausência de tratamento de erros"
      },
      {
        "file": "engagement.js",
        "type": "reliability",
        "severity": "high",
        "message": "Ausência de tratamento de erros"
      },
      {
        "file": "performance.js",
        "type": "reliability",
        "severity": "high",
        "message": "Ausência de tratamento de erros"
      },
      {
        "file": "health.js",
        "type": "reliability",
        "severity": "high",
        "message": "Ausência de tratamento de erros"
      }
    ],
    "architectureIssues": [],
    "recommendations": [
      {
        "category": "security",
        "priority": "high",
        "title": "Implementar validação e rate limiting",
        "description": "Vários endpoints carecem de validação adequada e rate limiting para prevenir ataques"
      },
      {
        "category": "reliability",
        "priority": "high",
        "title": "Implementar tratamento de erros consistente",
        "description": "Alguns endpoints não possuem tratamento adequado de erros"
      }
    ]
  },
  "categories": {
    "root": [
      {
        "file": "src\\api\\routes\\ai-reports.js",
        "category": "root",
        "endpoints": [
          {
            "method": "GET",
            "path": "/insights/:childId",
            "fullPath": "/api/root/insights/:childId"
          },
          {
            "method": "GET",
            "path": "/health",
            "fullPath": "/api/root/health"
          },
          {
            "method": "GET",
            "path": "/config",
            "fullPath": "/api/root/config"
          },
          {
            "method": "POST",
            "path": "/generate",
            "fullPath": "/api/root/generate"
          },
          {
            "method": "POST",
            "path": "/analyze-metrics",
            "fullPath": "/api/root/analyze-metrics"
          },
          {
            "method": "POST",
            "path": "/dashboard",
            "fullPath": "/api/root/dashboard"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": false,
          "authentication": false,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": false,
          "documentation": true
        },
        "imports": [
          "express",
          "../services/createIntegratedSystem.js"
        ],
        "exports": true,
        "issues": [
          {
            "type": "security",
            "severity": "medium",
            "message": "Endpoints POST sem rate limiting detectado"
          }
        ]
      }
    ],
    "analytics": [
      {
        "file": "src\\api\\routes\\analytics\\comparisons.js",
        "category": "analytics",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/analytics"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": false,
          "authentication": true,
          "validation": false,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express",
          "../../utils/logger.js",
          "../../services/MetricsService.js",
          "../../algorithms/CognitiveAssociationEngine.js"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\analytics\\insights.js",
        "category": "analytics",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/analytics"
          },
          {
            "method": "GET",
            "path": "/:childId/history",
            "fullPath": "/api/analytics/:childId/history"
          },
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/analytics"
          },
          {
            "method": "POST",
            "path": "/custom",
            "fullPath": "/api/analytics/custom"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": false,
          "authentication": false,
          "validation": false,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express",
          "../../utils/logger.js",
          "../../services/MetricsService.js",
          "../../algorithms/CognitiveAssociationEngine.js"
        ],
        "exports": false,
        "issues": [
          {
            "type": "security",
            "severity": "medium",
            "message": "Endpoints POST sem rate limiting detectado"
          },
          {
            "type": "security",
            "severity": "medium",
            "message": "Endpoints de escrita sem validação de entrada detectado"
          }
        ]
      },
      {
        "file": "src\\api\\routes\\analytics\\predictions.js",
        "category": "analytics",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/analytics"
          },
          {
            "method": "POST",
            "path": "/scenario",
            "fullPath": "/api/analytics/scenario"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": false,
          "authentication": false,
          "validation": false,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express",
          "../../utils/logger.js",
          "../../services/MetricsService.js",
          "../../algorithms/CognitiveAssociationEngine.js"
        ],
        "exports": true,
        "issues": [
          {
            "type": "security",
            "severity": "medium",
            "message": "Endpoints POST sem rate limiting detectado"
          },
          {
            "type": "security",
            "severity": "medium",
            "message": "Endpoints de escrita sem validação de entrada detectado"
          }
        ]
      },
      {
        "file": "src\\api\\routes\\analytics\\trends.js",
        "category": "analytics",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/analytics"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": false,
          "authentication": false,
          "validation": false,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": false,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express",
          "../../utils/logger.js"
        ],
        "exports": true,
        "issues": []
      }
    ],
    "auth": [
      {
        "file": "src\\api\\routes\\auth\\login.js",
        "category": "auth",
        "endpoints": [
          {
            "method": "POST",
            "path": "/",
            "fullPath": "/api/auth"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": true,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express",
          "bcrypt",
          "jsonwebtoken",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\auth\\logout.js",
        "category": "auth",
        "endpoints": [
          {
            "method": "POST",
            "path": "/",
            "fullPath": "/api/auth"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": true,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js",
          "jsonwebtoken"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\auth\\refresh.js",
        "category": "auth",
        "endpoints": [
          {
            "method": "POST",
            "path": "/",
            "fullPath": "/api/auth"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": true,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": false,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js",
          "jsonwebtoken"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\auth\\verify.js",
        "category": "auth",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/auth"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": true,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js",
          "jsonwebtoken"
        ],
        "exports": true,
        "issues": []
      }
    ],
    "dashboard": [
      {
        "file": "src\\api\\routes\\dashboard\\accessibility.js",
        "category": "dashboard",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/dashboard"
          },
          {
            "method": "POST",
            "path": "/audit",
            "fullPath": "/api/dashboard/audit"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": false,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js",
          "express",
          "../../services/logger.js",
          "../../services/createIntegratedSystem.js"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\dashboard\\behavior.js",
        "category": "dashboard",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/dashboard"
          },
          {
            "method": "GET",
            "path": "/patterns",
            "fullPath": "/api/dashboard/patterns"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": false,
          "authentication": false,
          "validation": false,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\dashboard\\cache.js",
        "category": "dashboard",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/dashboard"
          },
          {
            "method": "POST",
            "path": "/",
            "fullPath": "/api/dashboard"
          },
          {
            "method": "DELETE",
            "path": "/",
            "fullPath": "/api/dashboard"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": false,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": false,
          "errorHandling": true,
          "logging": false,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\dashboard\\overview-fixed.js",
        "category": "dashboard",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/dashboard"
          },
          {
            "method": "GET",
            "path": "/summary",
            "fullPath": "/api/dashboard/summary"
          },
          {
            "method": "GET",
            "path": "/health",
            "fullPath": "/api/dashboard/health"
          },
          {
            "method": "POST",
            "path": "/refresh",
            "fullPath": "/api/dashboard/refresh"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": true,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": false,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js",
          "../../middleware/auth/jwt.js"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\dashboard\\overview.js",
        "category": "dashboard",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/dashboard"
          },
          {
            "method": "GET",
            "path": "/summary",
            "fullPath": "/api/dashboard/summary"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": false,
          "authentication": false,
          "validation": false,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\dashboard\\progress.js",
        "category": "dashboard",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/dashboard"
          },
          {
            "method": "GET",
            "path": "/detailed/:skillArea",
            "fullPath": "/api/dashboard/detailed/:skillArea"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": false,
          "authentication": false,
          "validation": false,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\dashboard\\therapeutic.js",
        "category": "dashboard",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/dashboard"
          },
          {
            "method": "GET",
            "path": "/goals",
            "fullPath": "/api/dashboard/goals"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": false,
          "authentication": true,
          "validation": false,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express"
        ],
        "exports": true,
        "issues": []
      }
    ],
    "debug": [
      {
        "file": "src\\api\\routes\\debug\\metrics-monitor.js",
        "category": "debug",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/debug"
          },
          {
            "method": "POST",
            "path": "/",
            "fullPath": "/api/debug"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": false,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": false,
          "errorHandling": true,
          "logging": false,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js"
        ],
        "exports": true,
        "issues": []
      }
    ],
    "metrics": [
      {
        "file": "src\\api\\routes\\metrics\\dashboard.js",
        "category": "metrics",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/metrics"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": false,
          "authentication": false,
          "validation": false,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": false,
          "errorHandling": false,
          "logging": false,
          "documentation": true
        },
        "imports": [],
        "exports": true,
        "issues": [
          {
            "type": "reliability",
            "severity": "high",
            "message": "Ausência de tratamento de erros"
          }
        ]
      },
      {
        "file": "src\\api\\routes\\metrics\\engagement.js",
        "category": "metrics",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/metrics"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": false,
          "authentication": false,
          "validation": false,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": false,
          "errorHandling": false,
          "logging": false,
          "documentation": true
        },
        "imports": [],
        "exports": true,
        "issues": [
          {
            "type": "reliability",
            "severity": "high",
            "message": "Ausência de tratamento de erros"
          }
        ]
      },
      {
        "file": "src\\api\\routes\\metrics\\game-sessions.js",
        "category": "metrics",
        "endpoints": [
          {
            "method": "GET",
            "path": "/:sessionId",
            "fullPath": "/api/metrics/:sessionId"
          },
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/metrics"
          },
          {
            "method": "POST",
            "path": "/",
            "fullPath": "/api/metrics"
          },
          {
            "method": "POST",
            "path": "/:sessionId/complete",
            "fullPath": "/api/metrics/:sessionId/complete"
          },
          {
            "method": "PUT",
            "path": "/:sessionId",
            "fullPath": "/api/metrics/:sessionId"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": false,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js",
          "../../middleware/logging/requestLogger.js"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\metrics\\interactions.js",
        "category": "metrics",
        "endpoints": [
          {
            "method": "GET",
            "path": "/session/:sessionId",
            "fullPath": "/api/metrics/session/:sessionId"
          },
          {
            "method": "GET",
            "path": "/analysis/:sessionId",
            "fullPath": "/api/metrics/analysis/:sessionId"
          },
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/metrics"
          },
          {
            "method": "POST",
            "path": "/",
            "fullPath": "/api/metrics"
          },
          {
            "method": "POST",
            "path": "/batch",
            "fullPath": "/api/metrics/batch"
          },
          {
            "method": "DELETE",
            "path": "/session/:sessionId",
            "fullPath": "/api/metrics/session/:sessionId"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": true,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": false,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\metrics\\multisensory.js",
        "category": "metrics",
        "endpoints": [
          {
            "method": "GET",
            "path": "/session/:sessionId",
            "fullPath": "/api/metrics/session/:sessionId"
          },
          {
            "method": "GET",
            "path": "/analysis/:sessionId",
            "fullPath": "/api/metrics/analysis/:sessionId"
          },
          {
            "method": "GET",
            "path": "/patterns/:sessionId",
            "fullPath": "/api/metrics/patterns/:sessionId"
          },
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/metrics"
          },
          {
            "method": "POST",
            "path": "/",
            "fullPath": "/api/metrics"
          },
          {
            "method": "POST",
            "path": "/stream",
            "fullPath": "/api/metrics/stream"
          },
          {
            "method": "POST",
            "path": "/calibrate",
            "fullPath": "/api/metrics/calibrate"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": true,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": false,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js",
          "../../middleware/auth/jwt.js",
          "../../services/metrics/MetricsCollector.js"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\metrics\\performance.js",
        "category": "metrics",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/metrics"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": false,
          "authentication": false,
          "validation": false,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": false,
          "errorHandling": false,
          "logging": false,
          "documentation": true
        },
        "imports": [],
        "exports": true,
        "issues": [
          {
            "type": "reliability",
            "severity": "high",
            "message": "Ausência de tratamento de erros"
          }
        ]
      },
      {
        "file": "src\\api\\routes\\metrics\\progress.js",
        "category": "metrics",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/metrics"
          },
          {
            "method": "POST",
            "path": "/",
            "fullPath": "/api/metrics"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": false,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": false,
          "errorHandling": true,
          "logging": false,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js"
        ],
        "exports": true,
        "issues": []
      }
    ],
    "premium": [
      {
        "file": "src\\api\\routes\\premium\\auth.js",
        "category": "premium",
        "endpoints": [
          {
            "method": "GET",
            "path": "/status",
            "fullPath": "/api/premium/status"
          },
          {
            "method": "POST",
            "path": "/upgrade",
            "fullPath": "/api/premium/upgrade"
          },
          {
            "method": "POST",
            "path": "/cancel",
            "fullPath": "/api/premium/cancel"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": true,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": false,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js",
          "../../middleware/auth/jwt.js"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\premium\\dashboard.js",
        "category": "premium",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/premium"
          },
          {
            "method": "GET",
            "path": "/analytics",
            "fullPath": "/api/premium/analytics"
          },
          {
            "method": "GET",
            "path": "/insights",
            "fullPath": "/api/premium/insights"
          },
          {
            "method": "POST",
            "path": "/custom-report",
            "fullPath": "/api/premium/custom-report"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": true,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": false,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js",
          "../../middleware/auth/jwt.js"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\premium\\insights.js",
        "category": "premium",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/premium"
          },
          {
            "method": "GET",
            "path": "/cognitive",
            "fullPath": "/api/premium/cognitive"
          },
          {
            "method": "GET",
            "path": "/behavioral",
            "fullPath": "/api/premium/behavioral"
          },
          {
            "method": "GET",
            "path": "/therapeutic",
            "fullPath": "/api/premium/therapeutic"
          },
          {
            "method": "GET",
            "path": "/predictions",
            "fullPath": "/api/premium/predictions"
          },
          {
            "method": "POST",
            "path": "/generate",
            "fullPath": "/api/premium/generate"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": true,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": false,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js",
          "../../middleware/auth/jwt.js"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\premium\\profiles.js",
        "category": "premium",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/premium"
          },
          {
            "method": "GET",
            "path": "/cognitive",
            "fullPath": "/api/premium/cognitive"
          },
          {
            "method": "GET",
            "path": "/therapeutic",
            "fullPath": "/api/premium/therapeutic"
          },
          {
            "method": "GET",
            "path": "/achievements",
            "fullPath": "/api/premium/achievements"
          },
          {
            "method": "POST",
            "path": "/cognitive/assessment",
            "fullPath": "/api/premium/cognitive/assessment"
          },
          {
            "method": "POST",
            "path": "/export",
            "fullPath": "/api/premium/export"
          },
          {
            "method": "PUT",
            "path": "/",
            "fullPath": "/api/premium"
          },
          {
            "method": "PUT",
            "path": "/therapeutic",
            "fullPath": "/api/premium/therapeutic"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": true,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": false,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js",
          "../../middleware/auth/jwt.js"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\premium\\reports.js",
        "category": "premium",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/premium"
          },
          {
            "method": "GET",
            "path": "/:reportId",
            "fullPath": "/api/premium/:reportId"
          },
          {
            "method": "GET",
            "path": "/:reportId/download",
            "fullPath": "/api/premium/:reportId/download"
          },
          {
            "method": "POST",
            "path": "/generate",
            "fullPath": "/api/premium/generate"
          },
          {
            "method": "POST",
            "path": "/comprehensive",
            "fullPath": "/api/premium/comprehensive"
          },
          {
            "method": "POST",
            "path": "/comparative",
            "fullPath": "/api/premium/comparative"
          },
          {
            "method": "POST",
            "path": "/therapeutic",
            "fullPath": "/api/premium/therapeutic"
          },
          {
            "method": "DELETE",
            "path": "/:reportId",
            "fullPath": "/api/premium/:reportId"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": true,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": false,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js",
          "../../middleware/auth/jwt.js"
        ],
        "exports": true,
        "issues": []
      }
    ],
    "profiles": [
      {
        "file": "src\\api\\routes\\profiles\\children.js",
        "category": "profiles",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/profiles"
          },
          {
            "method": "GET",
            "path": "/:childId",
            "fullPath": "/api/profiles/:childId"
          },
          {
            "method": "POST",
            "path": "/",
            "fullPath": "/api/profiles"
          },
          {
            "method": "PUT",
            "path": "/:childId",
            "fullPath": "/api/profiles/:childId"
          },
          {
            "method": "DELETE",
            "path": "/:childId",
            "fullPath": "/api/profiles/:childId"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": false,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": false,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\profiles\\preferences.js",
        "category": "profiles",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/profiles"
          },
          {
            "method": "GET",
            "path": "/defaults",
            "fullPath": "/api/profiles/defaults"
          },
          {
            "method": "GET",
            "path": "/export",
            "fullPath": "/api/profiles/export"
          },
          {
            "method": "POST",
            "path": "/reset",
            "fullPath": "/api/profiles/reset"
          },
          {
            "method": "POST",
            "path": "/import",
            "fullPath": "/api/profiles/import"
          },
          {
            "method": "PUT",
            "path": "/",
            "fullPath": "/api/profiles"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": false,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": false,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\profiles\\therapists.js",
        "category": "profiles",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/profiles"
          },
          {
            "method": "GET",
            "path": "/:therapistId",
            "fullPath": "/api/profiles/:therapistId"
          },
          {
            "method": "GET",
            "path": "/:therapistId/statistics",
            "fullPath": "/api/profiles/:therapistId/statistics"
          },
          {
            "method": "GET",
            "path": "/:therapistId/schedule",
            "fullPath": "/api/profiles/:therapistId/schedule"
          },
          {
            "method": "PUT",
            "path": "/",
            "fullPath": "/api/profiles"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": false,
          "authentication": false,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": false,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\profiles\\users.js",
        "category": "profiles",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/profiles"
          },
          {
            "method": "GET",
            "path": "/activity",
            "fullPath": "/api/profiles/activity"
          },
          {
            "method": "GET",
            "path": "/active",
            "fullPath": "/api/profiles/active"
          },
          {
            "method": "POST",
            "path": "/",
            "fullPath": "/api/profiles"
          },
          {
            "method": "PUT",
            "path": "/",
            "fullPath": "/api/profiles"
          },
          {
            "method": "PUT",
            "path": "/password",
            "fullPath": "/api/profiles/password"
          },
          {
            "method": "DELETE",
            "path": "/",
            "fullPath": "/api/profiles"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": false,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js",
          "bcryptjs"
        ],
        "exports": true,
        "issues": []
      }
    ],
    "public": [
      {
        "file": "src\\api\\routes\\public\\activities.js",
        "category": "public",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/public"
          },
          {
            "method": "GET",
            "path": "/:id",
            "fullPath": "/api/public/:id"
          },
          {
            "method": "GET",
            "path": "/category/:category",
            "fullPath": "/api/public/category/:category"
          },
          {
            "method": "GET",
            "path": "/neurodivergence/:type",
            "fullPath": "/api/public/neurodivergence/:type"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": false,
          "authentication": false,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": false,
          "errorHandling": true,
          "logging": false,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/logging/requestLogger.js"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\public\\games.js",
        "category": "public",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/public"
          },
          {
            "method": "GET",
            "path": "/:id",
            "fullPath": "/api/public/:id"
          },
          {
            "method": "GET",
            "path": "/category/:category",
            "fullPath": "/api/public/category/:category"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": false,
          "authentication": false,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": false,
          "errorHandling": true,
          "logging": false,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/logging/requestLogger.js"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\public\\health.js",
        "category": "public",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/public"
          },
          {
            "method": "GET",
            "path": "/detailed",
            "fullPath": "/api/public/detailed"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": true,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": false,
          "errorHandling": false,
          "logging": false,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js"
        ],
        "exports": true,
        "issues": [
          {
            "type": "reliability",
            "severity": "high",
            "message": "Ausência de tratamento de erros"
          }
        ]
      },
      {
        "file": "src\\api\\routes\\public\\metrics.js",
        "category": "public",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/public"
          },
          {
            "method": "POST",
            "path": "/",
            "fullPath": "/api/public"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": false,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": false,
          "errorHandling": true,
          "logging": false,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js"
        ],
        "exports": true,
        "issues": []
      }
    ],
    "reports": [
      {
        "file": "src\\api\\routes\\reports\\behavior.js",
        "category": "reports",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/reports"
          },
          {
            "method": "GET",
            "path": "/:childId/patterns",
            "fullPath": "/api/reports/:childId/patterns"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": false,
          "authentication": false,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": false,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\reports\\export.js",
        "category": "reports",
        "endpoints": [
          {
            "method": "GET",
            "path": "/:exportId/download",
            "fullPath": "/api/reports/:exportId/download"
          },
          {
            "method": "GET",
            "path": "/templates",
            "fullPath": "/api/reports/templates"
          },
          {
            "method": "GET",
            "path": "/history/:childId",
            "fullPath": "/api/reports/history/:childId"
          },
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/reports"
          },
          {
            "method": "POST",
            "path": "/",
            "fullPath": "/api/reports"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": false,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": true,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\reports\\goals.js",
        "category": "reports",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/reports"
          },
          {
            "method": "POST",
            "path": "/",
            "fullPath": "/api/reports"
          },
          {
            "method": "PUT",
            "path": "/:goalId",
            "fullPath": "/api/reports/:goalId"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": true,
          "authentication": false,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": false,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express",
          "../../middleware/security/rateLimiter.js",
          "../../middleware/validation/inputValidator.js",
          "../../middleware/error/errorHandler.js",
          "module"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\reports\\progress.js",
        "category": "reports",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/reports"
          },
          {
            "method": "GET",
            "path": "/:childId/chart-data",
            "fullPath": "/api/reports/:childId/chart-data"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": false,
          "authentication": false,
          "validation": false,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": false,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express"
        ],
        "exports": true,
        "issues": []
      },
      {
        "file": "src\\api\\routes\\reports\\therapeutic.js",
        "category": "reports",
        "endpoints": [
          {
            "method": "GET",
            "path": "/",
            "fullPath": "/api/reports"
          },
          {
            "method": "GET",
            "path": "/:childId/summary",
            "fullPath": "/api/reports/:childId/summary"
          }
        ],
        "middlewares": [],
        "security": {
          "rateLimit": false,
          "authentication": false,
          "validation": true,
          "cors": false,
          "helmet": false
        },
        "patterns": {
          "asyncAwait": false,
          "errorHandling": true,
          "logging": true,
          "documentation": true
        },
        "imports": [
          "express"
        ],
        "exports": true,
        "issues": []
      }
    ]
  },
  "detailedAnalysis": {
    "ai-reports.js": {
      "file": "src\\api\\routes\\ai-reports.js",
      "category": "root",
      "endpoints": [
        {
          "method": "GET",
          "path": "/insights/:childId",
          "fullPath": "/api/root/insights/:childId"
        },
        {
          "method": "GET",
          "path": "/health",
          "fullPath": "/api/root/health"
        },
        {
          "method": "GET",
          "path": "/config",
          "fullPath": "/api/root/config"
        },
        {
          "method": "POST",
          "path": "/generate",
          "fullPath": "/api/root/generate"
        },
        {
          "method": "POST",
          "path": "/analyze-metrics",
          "fullPath": "/api/root/analyze-metrics"
        },
        {
          "method": "POST",
          "path": "/dashboard",
          "fullPath": "/api/root/dashboard"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": false,
        "authentication": false,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": true,
        "errorHandling": true,
        "logging": false,
        "documentation": true
      },
      "imports": [
        "express",
        "../services/createIntegratedSystem.js"
      ],
      "exports": true,
      "issues": [
        {
          "type": "security",
          "severity": "medium",
          "message": "Endpoints POST sem rate limiting detectado"
        }
      ]
    },
    "comparisons.js": {
      "file": "src\\api\\routes\\analytics\\comparisons.js",
      "category": "analytics",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/analytics"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": false,
        "authentication": true,
        "validation": false,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": true,
        "errorHandling": true,
        "logging": true,
        "documentation": true
      },
      "imports": [
        "express",
        "../../utils/logger.js",
        "../../services/MetricsService.js",
        "../../algorithms/CognitiveAssociationEngine.js"
      ],
      "exports": true,
      "issues": []
    },
    "insights.js": {
      "file": "src\\api\\routes\\premium\\insights.js",
      "category": "premium",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/premium"
        },
        {
          "method": "GET",
          "path": "/cognitive",
          "fullPath": "/api/premium/cognitive"
        },
        {
          "method": "GET",
          "path": "/behavioral",
          "fullPath": "/api/premium/behavioral"
        },
        {
          "method": "GET",
          "path": "/therapeutic",
          "fullPath": "/api/premium/therapeutic"
        },
        {
          "method": "GET",
          "path": "/predictions",
          "fullPath": "/api/premium/predictions"
        },
        {
          "method": "POST",
          "path": "/generate",
          "fullPath": "/api/premium/generate"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": true,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": true,
        "errorHandling": true,
        "logging": false,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js",
        "../../middleware/auth/jwt.js"
      ],
      "exports": true,
      "issues": []
    },
    "predictions.js": {
      "file": "src\\api\\routes\\analytics\\predictions.js",
      "category": "analytics",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/analytics"
        },
        {
          "method": "POST",
          "path": "/scenario",
          "fullPath": "/api/analytics/scenario"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": false,
        "authentication": false,
        "validation": false,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": true,
        "errorHandling": true,
        "logging": true,
        "documentation": true
      },
      "imports": [
        "express",
        "../../utils/logger.js",
        "../../services/MetricsService.js",
        "../../algorithms/CognitiveAssociationEngine.js"
      ],
      "exports": true,
      "issues": [
        {
          "type": "security",
          "severity": "medium",
          "message": "Endpoints POST sem rate limiting detectado"
        },
        {
          "type": "security",
          "severity": "medium",
          "message": "Endpoints de escrita sem validação de entrada detectado"
        }
      ]
    },
    "trends.js": {
      "file": "src\\api\\routes\\analytics\\trends.js",
      "category": "analytics",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/analytics"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": false,
        "authentication": false,
        "validation": false,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": false,
        "errorHandling": true,
        "logging": true,
        "documentation": true
      },
      "imports": [
        "express",
        "../../utils/logger.js"
      ],
      "exports": true,
      "issues": []
    },
    "login.js": {
      "file": "src\\api\\routes\\auth\\login.js",
      "category": "auth",
      "endpoints": [
        {
          "method": "POST",
          "path": "/",
          "fullPath": "/api/auth"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": true,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": true,
        "errorHandling": true,
        "logging": true,
        "documentation": true
      },
      "imports": [
        "express",
        "bcrypt",
        "jsonwebtoken",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js"
      ],
      "exports": true,
      "issues": []
    },
    "logout.js": {
      "file": "src\\api\\routes\\auth\\logout.js",
      "category": "auth",
      "endpoints": [
        {
          "method": "POST",
          "path": "/",
          "fullPath": "/api/auth"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": true,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": true,
        "errorHandling": true,
        "logging": true,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js",
        "jsonwebtoken"
      ],
      "exports": true,
      "issues": []
    },
    "refresh.js": {
      "file": "src\\api\\routes\\auth\\refresh.js",
      "category": "auth",
      "endpoints": [
        {
          "method": "POST",
          "path": "/",
          "fullPath": "/api/auth"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": true,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": false,
        "errorHandling": true,
        "logging": true,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js",
        "jsonwebtoken"
      ],
      "exports": true,
      "issues": []
    },
    "verify.js": {
      "file": "src\\api\\routes\\auth\\verify.js",
      "category": "auth",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/auth"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": true,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": true,
        "errorHandling": true,
        "logging": true,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js",
        "jsonwebtoken"
      ],
      "exports": true,
      "issues": []
    },
    "accessibility.js": {
      "file": "src\\api\\routes\\dashboard\\accessibility.js",
      "category": "dashboard",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/dashboard"
        },
        {
          "method": "POST",
          "path": "/audit",
          "fullPath": "/api/dashboard/audit"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": false,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": true,
        "errorHandling": true,
        "logging": true,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js",
        "express",
        "../../services/logger.js",
        "../../services/createIntegratedSystem.js"
      ],
      "exports": true,
      "issues": []
    },
    "behavior.js": {
      "file": "src\\api\\routes\\reports\\behavior.js",
      "category": "reports",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/reports"
        },
        {
          "method": "GET",
          "path": "/:childId/patterns",
          "fullPath": "/api/reports/:childId/patterns"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": false,
        "authentication": false,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": false,
        "errorHandling": true,
        "logging": true,
        "documentation": true
      },
      "imports": [
        "express"
      ],
      "exports": true,
      "issues": []
    },
    "cache.js": {
      "file": "src\\api\\routes\\dashboard\\cache.js",
      "category": "dashboard",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/dashboard"
        },
        {
          "method": "POST",
          "path": "/",
          "fullPath": "/api/dashboard"
        },
        {
          "method": "DELETE",
          "path": "/",
          "fullPath": "/api/dashboard"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": false,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": false,
        "errorHandling": true,
        "logging": false,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js"
      ],
      "exports": true,
      "issues": []
    },
    "overview-fixed.js": {
      "file": "src\\api\\routes\\dashboard\\overview-fixed.js",
      "category": "dashboard",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/dashboard"
        },
        {
          "method": "GET",
          "path": "/summary",
          "fullPath": "/api/dashboard/summary"
        },
        {
          "method": "GET",
          "path": "/health",
          "fullPath": "/api/dashboard/health"
        },
        {
          "method": "POST",
          "path": "/refresh",
          "fullPath": "/api/dashboard/refresh"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": true,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": true,
        "errorHandling": true,
        "logging": false,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js",
        "../../middleware/auth/jwt.js"
      ],
      "exports": true,
      "issues": []
    },
    "overview.js": {
      "file": "src\\api\\routes\\dashboard\\overview.js",
      "category": "dashboard",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/dashboard"
        },
        {
          "method": "GET",
          "path": "/summary",
          "fullPath": "/api/dashboard/summary"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": false,
        "authentication": false,
        "validation": false,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": true,
        "errorHandling": true,
        "logging": true,
        "documentation": true
      },
      "imports": [
        "express"
      ],
      "exports": true,
      "issues": []
    },
    "progress.js": {
      "file": "src\\api\\routes\\reports\\progress.js",
      "category": "reports",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/reports"
        },
        {
          "method": "GET",
          "path": "/:childId/chart-data",
          "fullPath": "/api/reports/:childId/chart-data"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": false,
        "authentication": false,
        "validation": false,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": false,
        "errorHandling": true,
        "logging": true,
        "documentation": true
      },
      "imports": [
        "express"
      ],
      "exports": true,
      "issues": []
    },
    "therapeutic.js": {
      "file": "src\\api\\routes\\reports\\therapeutic.js",
      "category": "reports",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/reports"
        },
        {
          "method": "GET",
          "path": "/:childId/summary",
          "fullPath": "/api/reports/:childId/summary"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": false,
        "authentication": false,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": false,
        "errorHandling": true,
        "logging": true,
        "documentation": true
      },
      "imports": [
        "express"
      ],
      "exports": true,
      "issues": []
    },
    "metrics-monitor.js": {
      "file": "src\\api\\routes\\debug\\metrics-monitor.js",
      "category": "debug",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/debug"
        },
        {
          "method": "POST",
          "path": "/",
          "fullPath": "/api/debug"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": false,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": false,
        "errorHandling": true,
        "logging": false,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js"
      ],
      "exports": true,
      "issues": []
    },
    "dashboard.js": {
      "file": "src\\api\\routes\\premium\\dashboard.js",
      "category": "premium",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/premium"
        },
        {
          "method": "GET",
          "path": "/analytics",
          "fullPath": "/api/premium/analytics"
        },
        {
          "method": "GET",
          "path": "/insights",
          "fullPath": "/api/premium/insights"
        },
        {
          "method": "POST",
          "path": "/custom-report",
          "fullPath": "/api/premium/custom-report"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": true,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": true,
        "errorHandling": true,
        "logging": false,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js",
        "../../middleware/auth/jwt.js"
      ],
      "exports": true,
      "issues": []
    },
    "engagement.js": {
      "file": "src\\api\\routes\\metrics\\engagement.js",
      "category": "metrics",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/metrics"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": false,
        "authentication": false,
        "validation": false,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": false,
        "errorHandling": false,
        "logging": false,
        "documentation": true
      },
      "imports": [],
      "exports": true,
      "issues": [
        {
          "type": "reliability",
          "severity": "high",
          "message": "Ausência de tratamento de erros"
        }
      ]
    },
    "game-sessions.js": {
      "file": "src\\api\\routes\\metrics\\game-sessions.js",
      "category": "metrics",
      "endpoints": [
        {
          "method": "GET",
          "path": "/:sessionId",
          "fullPath": "/api/metrics/:sessionId"
        },
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/metrics"
        },
        {
          "method": "POST",
          "path": "/",
          "fullPath": "/api/metrics"
        },
        {
          "method": "POST",
          "path": "/:sessionId/complete",
          "fullPath": "/api/metrics/:sessionId/complete"
        },
        {
          "method": "PUT",
          "path": "/:sessionId",
          "fullPath": "/api/metrics/:sessionId"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": false,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": true,
        "errorHandling": true,
        "logging": true,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js",
        "../../middleware/logging/requestLogger.js"
      ],
      "exports": true,
      "issues": []
    },
    "interactions.js": {
      "file": "src\\api\\routes\\metrics\\interactions.js",
      "category": "metrics",
      "endpoints": [
        {
          "method": "GET",
          "path": "/session/:sessionId",
          "fullPath": "/api/metrics/session/:sessionId"
        },
        {
          "method": "GET",
          "path": "/analysis/:sessionId",
          "fullPath": "/api/metrics/analysis/:sessionId"
        },
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/metrics"
        },
        {
          "method": "POST",
          "path": "/",
          "fullPath": "/api/metrics"
        },
        {
          "method": "POST",
          "path": "/batch",
          "fullPath": "/api/metrics/batch"
        },
        {
          "method": "DELETE",
          "path": "/session/:sessionId",
          "fullPath": "/api/metrics/session/:sessionId"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": true,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": true,
        "errorHandling": true,
        "logging": false,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js"
      ],
      "exports": true,
      "issues": []
    },
    "multisensory.js": {
      "file": "src\\api\\routes\\metrics\\multisensory.js",
      "category": "metrics",
      "endpoints": [
        {
          "method": "GET",
          "path": "/session/:sessionId",
          "fullPath": "/api/metrics/session/:sessionId"
        },
        {
          "method": "GET",
          "path": "/analysis/:sessionId",
          "fullPath": "/api/metrics/analysis/:sessionId"
        },
        {
          "method": "GET",
          "path": "/patterns/:sessionId",
          "fullPath": "/api/metrics/patterns/:sessionId"
        },
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/metrics"
        },
        {
          "method": "POST",
          "path": "/",
          "fullPath": "/api/metrics"
        },
        {
          "method": "POST",
          "path": "/stream",
          "fullPath": "/api/metrics/stream"
        },
        {
          "method": "POST",
          "path": "/calibrate",
          "fullPath": "/api/metrics/calibrate"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": true,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": true,
        "errorHandling": true,
        "logging": false,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js",
        "../../middleware/auth/jwt.js",
        "../../services/metrics/MetricsCollector.js"
      ],
      "exports": true,
      "issues": []
    },
    "performance.js": {
      "file": "src\\api\\routes\\metrics\\performance.js",
      "category": "metrics",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/metrics"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": false,
        "authentication": false,
        "validation": false,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": false,
        "errorHandling": false,
        "logging": false,
        "documentation": true
      },
      "imports": [],
      "exports": true,
      "issues": [
        {
          "type": "reliability",
          "severity": "high",
          "message": "Ausência de tratamento de erros"
        }
      ]
    },
    "auth.js": {
      "file": "src\\api\\routes\\premium\\auth.js",
      "category": "premium",
      "endpoints": [
        {
          "method": "GET",
          "path": "/status",
          "fullPath": "/api/premium/status"
        },
        {
          "method": "POST",
          "path": "/upgrade",
          "fullPath": "/api/premium/upgrade"
        },
        {
          "method": "POST",
          "path": "/cancel",
          "fullPath": "/api/premium/cancel"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": true,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": true,
        "errorHandling": true,
        "logging": false,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js",
        "../../middleware/auth/jwt.js"
      ],
      "exports": true,
      "issues": []
    },
    "profiles.js": {
      "file": "src\\api\\routes\\premium\\profiles.js",
      "category": "premium",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/premium"
        },
        {
          "method": "GET",
          "path": "/cognitive",
          "fullPath": "/api/premium/cognitive"
        },
        {
          "method": "GET",
          "path": "/therapeutic",
          "fullPath": "/api/premium/therapeutic"
        },
        {
          "method": "GET",
          "path": "/achievements",
          "fullPath": "/api/premium/achievements"
        },
        {
          "method": "POST",
          "path": "/cognitive/assessment",
          "fullPath": "/api/premium/cognitive/assessment"
        },
        {
          "method": "POST",
          "path": "/export",
          "fullPath": "/api/premium/export"
        },
        {
          "method": "PUT",
          "path": "/",
          "fullPath": "/api/premium"
        },
        {
          "method": "PUT",
          "path": "/therapeutic",
          "fullPath": "/api/premium/therapeutic"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": true,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": true,
        "errorHandling": true,
        "logging": false,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js",
        "../../middleware/auth/jwt.js"
      ],
      "exports": true,
      "issues": []
    },
    "reports.js": {
      "file": "src\\api\\routes\\premium\\reports.js",
      "category": "premium",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/premium"
        },
        {
          "method": "GET",
          "path": "/:reportId",
          "fullPath": "/api/premium/:reportId"
        },
        {
          "method": "GET",
          "path": "/:reportId/download",
          "fullPath": "/api/premium/:reportId/download"
        },
        {
          "method": "POST",
          "path": "/generate",
          "fullPath": "/api/premium/generate"
        },
        {
          "method": "POST",
          "path": "/comprehensive",
          "fullPath": "/api/premium/comprehensive"
        },
        {
          "method": "POST",
          "path": "/comparative",
          "fullPath": "/api/premium/comparative"
        },
        {
          "method": "POST",
          "path": "/therapeutic",
          "fullPath": "/api/premium/therapeutic"
        },
        {
          "method": "DELETE",
          "path": "/:reportId",
          "fullPath": "/api/premium/:reportId"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": true,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": true,
        "errorHandling": true,
        "logging": false,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js",
        "../../middleware/auth/jwt.js"
      ],
      "exports": true,
      "issues": []
    },
    "children.js": {
      "file": "src\\api\\routes\\profiles\\children.js",
      "category": "profiles",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/profiles"
        },
        {
          "method": "GET",
          "path": "/:childId",
          "fullPath": "/api/profiles/:childId"
        },
        {
          "method": "POST",
          "path": "/",
          "fullPath": "/api/profiles"
        },
        {
          "method": "PUT",
          "path": "/:childId",
          "fullPath": "/api/profiles/:childId"
        },
        {
          "method": "DELETE",
          "path": "/:childId",
          "fullPath": "/api/profiles/:childId"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": false,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": false,
        "errorHandling": true,
        "logging": true,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js"
      ],
      "exports": true,
      "issues": []
    },
    "preferences.js": {
      "file": "src\\api\\routes\\profiles\\preferences.js",
      "category": "profiles",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/profiles"
        },
        {
          "method": "GET",
          "path": "/defaults",
          "fullPath": "/api/profiles/defaults"
        },
        {
          "method": "GET",
          "path": "/export",
          "fullPath": "/api/profiles/export"
        },
        {
          "method": "POST",
          "path": "/reset",
          "fullPath": "/api/profiles/reset"
        },
        {
          "method": "POST",
          "path": "/import",
          "fullPath": "/api/profiles/import"
        },
        {
          "method": "PUT",
          "path": "/",
          "fullPath": "/api/profiles"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": false,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": false,
        "errorHandling": true,
        "logging": true,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js"
      ],
      "exports": true,
      "issues": []
    },
    "therapists.js": {
      "file": "src\\api\\routes\\profiles\\therapists.js",
      "category": "profiles",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/profiles"
        },
        {
          "method": "GET",
          "path": "/:therapistId",
          "fullPath": "/api/profiles/:therapistId"
        },
        {
          "method": "GET",
          "path": "/:therapistId/statistics",
          "fullPath": "/api/profiles/:therapistId/statistics"
        },
        {
          "method": "GET",
          "path": "/:therapistId/schedule",
          "fullPath": "/api/profiles/:therapistId/schedule"
        },
        {
          "method": "PUT",
          "path": "/",
          "fullPath": "/api/profiles"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": false,
        "authentication": false,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": false,
        "errorHandling": true,
        "logging": true,
        "documentation": true
      },
      "imports": [
        "express"
      ],
      "exports": true,
      "issues": []
    },
    "users.js": {
      "file": "src\\api\\routes\\profiles\\users.js",
      "category": "profiles",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/profiles"
        },
        {
          "method": "GET",
          "path": "/activity",
          "fullPath": "/api/profiles/activity"
        },
        {
          "method": "GET",
          "path": "/active",
          "fullPath": "/api/profiles/active"
        },
        {
          "method": "POST",
          "path": "/",
          "fullPath": "/api/profiles"
        },
        {
          "method": "PUT",
          "path": "/",
          "fullPath": "/api/profiles"
        },
        {
          "method": "PUT",
          "path": "/password",
          "fullPath": "/api/profiles/password"
        },
        {
          "method": "DELETE",
          "path": "/",
          "fullPath": "/api/profiles"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": false,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": true,
        "errorHandling": true,
        "logging": true,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js",
        "bcryptjs"
      ],
      "exports": true,
      "issues": []
    },
    "activities.js": {
      "file": "src\\api\\routes\\public\\activities.js",
      "category": "public",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/public"
        },
        {
          "method": "GET",
          "path": "/:id",
          "fullPath": "/api/public/:id"
        },
        {
          "method": "GET",
          "path": "/category/:category",
          "fullPath": "/api/public/category/:category"
        },
        {
          "method": "GET",
          "path": "/neurodivergence/:type",
          "fullPath": "/api/public/neurodivergence/:type"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": false,
        "authentication": false,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": false,
        "errorHandling": true,
        "logging": false,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/logging/requestLogger.js"
      ],
      "exports": true,
      "issues": []
    },
    "games.js": {
      "file": "src\\api\\routes\\public\\games.js",
      "category": "public",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/public"
        },
        {
          "method": "GET",
          "path": "/:id",
          "fullPath": "/api/public/:id"
        },
        {
          "method": "GET",
          "path": "/category/:category",
          "fullPath": "/api/public/category/:category"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": false,
        "authentication": false,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": false,
        "errorHandling": true,
        "logging": false,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/logging/requestLogger.js"
      ],
      "exports": true,
      "issues": []
    },
    "health.js": {
      "file": "src\\api\\routes\\public\\health.js",
      "category": "public",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/public"
        },
        {
          "method": "GET",
          "path": "/detailed",
          "fullPath": "/api/public/detailed"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": true,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": false,
        "errorHandling": false,
        "logging": false,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js"
      ],
      "exports": true,
      "issues": [
        {
          "type": "reliability",
          "severity": "high",
          "message": "Ausência de tratamento de erros"
        }
      ]
    },
    "metrics.js": {
      "file": "src\\api\\routes\\public\\metrics.js",
      "category": "public",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/public"
        },
        {
          "method": "POST",
          "path": "/",
          "fullPath": "/api/public"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": false,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": false,
        "errorHandling": true,
        "logging": false,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js"
      ],
      "exports": true,
      "issues": []
    },
    "export.js": {
      "file": "src\\api\\routes\\reports\\export.js",
      "category": "reports",
      "endpoints": [
        {
          "method": "GET",
          "path": "/:exportId/download",
          "fullPath": "/api/reports/:exportId/download"
        },
        {
          "method": "GET",
          "path": "/templates",
          "fullPath": "/api/reports/templates"
        },
        {
          "method": "GET",
          "path": "/history/:childId",
          "fullPath": "/api/reports/history/:childId"
        },
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/reports"
        },
        {
          "method": "POST",
          "path": "/",
          "fullPath": "/api/reports"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": false,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": true,
        "errorHandling": true,
        "logging": true,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js"
      ],
      "exports": true,
      "issues": []
    },
    "goals.js": {
      "file": "src\\api\\routes\\reports\\goals.js",
      "category": "reports",
      "endpoints": [
        {
          "method": "GET",
          "path": "/",
          "fullPath": "/api/reports"
        },
        {
          "method": "POST",
          "path": "/",
          "fullPath": "/api/reports"
        },
        {
          "method": "PUT",
          "path": "/:goalId",
          "fullPath": "/api/reports/:goalId"
        }
      ],
      "middlewares": [],
      "security": {
        "rateLimit": true,
        "authentication": false,
        "validation": true,
        "cors": false,
        "helmet": false
      },
      "patterns": {
        "asyncAwait": false,
        "errorHandling": true,
        "logging": true,
        "documentation": true
      },
      "imports": [
        "express",
        "../../middleware/security/rateLimiter.js",
        "../../middleware/validation/inputValidator.js",
        "../../middleware/error/errorHandler.js",
        "module"
      ],
      "exports": true,
      "issues": []
    }
  }
}
```
