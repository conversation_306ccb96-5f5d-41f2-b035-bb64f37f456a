#!/bin/bash
# ======================================================
# SCRIPT DE CONFIGURAÇÃO NGINX - PORTAL BETINA V3
# ======================================================
# Este script automatiza a configuração do Nginx para o Portal Betina V3
# Execute com privilégios de root: sudo ./setup-nginx.sh

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configurações
DOMAIN="betina.uniqsolutions.com.br"
NGINX_SITES_AVAILABLE="/etc/nginx/sites-available"
NGINX_SITES_ENABLED="/etc/nginx/sites-enabled"
NGINX_CONF_DIR="/etc/nginx"
SSL_CERT_DIR="/etc/ssl/certs"
SSL_KEY_DIR="/etc/ssl/private"

echo -e "${BLUE}🚀 Configurando Nginx para Portal Betina V3${NC}"
echo -e "${BLUE}Domínio: ${DOMAIN}${NC}"
echo ""

# Verificar se é root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}❌ Este script deve ser executado como root (sudo)${NC}"
   exit 1
fi

# Verificar se o Nginx está instalado
if ! command -v nginx &> /dev/null; then
    echo -e "${YELLOW}⚠️  Nginx não encontrado. Instalando...${NC}"
    
    # Detectar distribuição
    if [[ -f /etc/debian_version ]]; then
        apt update
        apt install -y nginx
    elif [[ -f /etc/redhat-release ]]; then
        yum install -y nginx
    else
        echo -e "${RED}❌ Distribuição não suportada. Instale o Nginx manualmente.${NC}"
        exit 1
    fi
fi

# Criar diretórios necessários
echo -e "${BLUE}📁 Criando diretórios necessários...${NC}"
mkdir -p $NGINX_SITES_AVAILABLE
mkdir -p $NGINX_SITES_ENABLED
mkdir -p $SSL_CERT_DIR
mkdir -p $SSL_KEY_DIR
mkdir -p /var/log/nginx
mkdir -p /var/www/html

# Copiar configuração do site
echo -e "${BLUE}📄 Copiando configuração do site...${NC}"
cp "./nginx/betina.uniqsolutions.com.br.conf" "$NGINX_SITES_AVAILABLE/"

# Criar link simbólico
echo -e "${BLUE}🔗 Criando link simbólico...${NC}"
if [[ -f "$NGINX_SITES_ENABLED/betina.uniqsolutions.com.br.conf" ]]; then
    rm "$NGINX_SITES_ENABLED/betina.uniqsolutions.com.br.conf"
fi
ln -s "$NGINX_SITES_AVAILABLE/betina.uniqsolutions.com.br.conf" "$NGINX_SITES_ENABLED/"

# Remover configuração padrão do Nginx
if [[ -f "$NGINX_SITES_ENABLED/default" ]]; then
    echo -e "${YELLOW}⚠️  Removendo configuração padrão do Nginx...${NC}"
    rm "$NGINX_SITES_ENABLED/default"
fi

# Backup da configuração principal do Nginx
if [[ -f "$NGINX_CONF_DIR/nginx.conf" ]]; then
    echo -e "${BLUE}💾 Fazendo backup da configuração atual...${NC}"
    cp "$NGINX_CONF_DIR/nginx.conf" "$NGINX_CONF_DIR/nginx.conf.backup.$(date +%Y%m%d_%H%M%S)"
fi

# Copiar configuração principal (opcional)
read -p "Deseja substituir a configuração principal do Nginx? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}📄 Copiando configuração principal...${NC}"
    cp "./nginx/nginx.conf" "$NGINX_CONF_DIR/"
fi

# Criar páginas de erro customizadas
echo -e "${BLUE}📄 Criando páginas de erro...${NC}"
cat > /var/www/html/404.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>404 - Página não encontrada</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        h1 { color: #333; }
        p { color: #666; }
    </style>
</head>
<body>
    <h1>404 - Página não encontrada</h1>
    <p>A página que você está procurando não foi encontrada.</p>
    <p><a href="/">Voltar para o início</a></p>
</body>
</html>
EOF

cat > /var/www/html/50x.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Erro do servidor</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        h1 { color: #333; }
        p { color: #666; }
    </style>
</head>
<body>
    <h1>Erro do servidor</h1>
    <p>Ocorreu um erro interno do servidor.</p>
    <p>Tente novamente mais tarde.</p>
</body>
</html>
EOF

# Testar configuração do Nginx
echo -e "${BLUE}🔍 Testando configuração do Nginx...${NC}"
if nginx -t; then
    echo -e "${GREEN}✅ Configuração do Nginx válida!${NC}"
else
    echo -e "${RED}❌ Erro na configuração do Nginx!${NC}"
    exit 1
fi

# Recarregar Nginx
echo -e "${BLUE}🔄 Recarregando Nginx...${NC}"
systemctl reload nginx

# Verificar status do Nginx
if systemctl is-active --quiet nginx; then
    echo -e "${GREEN}✅ Nginx está rodando!${NC}"
else
    echo -e "${YELLOW}⚠️  Iniciando Nginx...${NC}"
    systemctl start nginx
    systemctl enable nginx
fi

echo ""
echo -e "${GREEN}🎉 Configuração concluída com sucesso!${NC}"
echo ""
echo -e "${BLUE}📋 Próximos passos:${NC}"
echo -e "1. ${YELLOW}Configurar certificado SSL:${NC}"
echo -e "   - Instalar certificado em: $SSL_CERT_DIR/betina.uniqsolutions.com.br.pem"
echo -e "   - Instalar chave privada em: $SSL_KEY_DIR/betina.uniqsolutions.com.br.key"
echo -e "   - Ou usar Let's Encrypt: certbot --nginx -d $DOMAIN"
echo ""
echo -e "2. ${YELLOW}Verificar se os serviços estão rodando:${NC}"
echo -e "   - Frontend (Vite): http://localhost:5173"
echo -e "   - Backend (API): http://localhost:3000"
echo -e "   - Monitoring: http://localhost:9090"
echo ""
echo -e "3. ${YELLOW}Testar o site:${NC}"
echo -e "   - HTTP: http://$DOMAIN (deve redirecionar para HTTPS)"
echo -e "   - HTTPS: https://$DOMAIN"
echo ""
echo -e "4. ${YELLOW}Monitorar logs:${NC}"
echo -e "   - tail -f /var/log/nginx/betina.uniqsolutions.com.br.access.log"
echo -e "   - tail -f /var/log/nginx/betina.uniqsolutions.com.br.error.log"
echo ""
echo -e "${GREEN}✅ Nginx configurado para Portal Betina V3!${NC}"
