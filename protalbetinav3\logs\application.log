{
  "timestamp": "2025-06-24T13:11:27.225Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:236:26)"
  }
}
{
  "timestamp": "2025-06-24T13:12:33.669Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:13:39.965Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:14:55.719Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:16:30.810Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:16:58.451Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:17:23.185Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:36:42.228Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:37:48.053Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:41:01.931Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:42:12.570Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:43:46.472Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:44:25.797Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:46:47.742Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:51:15.281Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T14:07:14.431Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T14:53:39.986Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T15:22:48.631Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T15:25:15.681Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T15:27:40.941Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T19:51:14.776Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T20:17:49.471Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T20:26:01.041Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T20:29:28.124Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T20:37:44.678Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T20:39:42.588Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T20:45:14.998Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T22:11:01.973Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3049:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseIntegrator._initializeComponents (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseIntegrator.js:51:28)\n    at new DatabaseIntegrator (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseIntegrator.js:40:10)\n    at new DatabaseSingleton (file:///C:/Projetos/protalbetinav3/src/api/services/databaseInstance.js:32:36)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/databaseInstance.js:59:27\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)"
  }
}
{
  "timestamp": "2025-06-24T22:11:02.017Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3049:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at DatabaseIntegrator._initializeComponents (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseIntegrator.js:61:28)\n    at new DatabaseIntegrator (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseIntegrator.js:40:10)\n    at new DatabaseSingleton (file:///C:/Projetos/protalbetinav3/src/api/services/databaseInstance.js:32:36)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/databaseInstance.js:59:27\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)\n    at async importModuleDynamicallyWrapper (node:internal/vm/module:436:15)"
  }
}
{
  "timestamp": "2025-06-24T22:50:00.484Z",
  "level": "INFO",
  "message": "🎮 Processando entrada de dados do jogo...",
  "meta": {
    "gameId": "memoria-cognitiva"
  }
}
{
  "timestamp": "2025-06-24T22:50:00.533Z",
  "level": "INFO",
  "message": "🔍 Refinando dados de métricas...",
  "meta": {
    "sessionId": "session_1750805400480",
    "type": "memoria-cognitiva"
  }
}
{
  "timestamp": "2025-06-24T22:50:00.534Z",
  "level": "INFO",
  "message": "✅ Dados de métricas refinados:",
  "meta": {
    "sessionId": "session_1750805400480",
    "accuracy": 85,
    "engagement": 76,
    "processingId": "refined_1750805400534_c0yxfb"
  }
}
{
  "timestamp": "2025-06-24T22:50:00.538Z",
  "level": "INFO",
  "message": "🔄 Processando dados terapêuticos...",
  "meta": {}
}
{
  "timestamp": "2025-06-24T22:50:00.540Z",
  "level": "ERROR",
  "message": "Erro ao analisar padrões terapêuticos:",
  "meta": {},
  "error": {
    "name": "TypeError",
    "message": "Cannot read properties of undefined (reading 'timeEngaged')",
    "stack": "TypeError: Cannot read properties of undefined (reading 'timeEngaged')\n    at SystemOrchestrator.identifyEngagementPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:985:25)\n    at SystemOrchestrator.analyzeTherapeuticPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:923:34)\n    at SystemOrchestrator.processTherapeuticData (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:755:35)\n    at testIntegrationFlow (file:///C:/Projetos/protalbetinav3/test-final-integration.js:105:46)"
  }
}
{
  "timestamp": "2025-06-24T22:50:00.544Z",
  "level": "INFO",
  "message": "📈 Preparando dados para dashboard...",
  "meta": {}
}
{
  "timestamp": "2025-06-24T22:51:01.897Z",
  "level": "INFO",
  "message": "🎮 Processando entrada de dados do jogo...",
  "meta": {
    "gameId": "memoria-cognitiva"
  }
}
{
  "timestamp": "2025-06-24T22:51:01.942Z",
  "level": "INFO",
  "message": "🔍 Refinando dados de métricas...",
  "meta": {
    "sessionId": "session_1750805461894",
    "type": "memoria-cognitiva"
  }
}
{
  "timestamp": "2025-06-24T22:51:01.944Z",
  "level": "INFO",
  "message": "✅ Dados de métricas refinados:",
  "meta": {
    "sessionId": "session_1750805461894",
    "accuracy": 85,
    "engagement": 76,
    "processingId": "refined_1750805461944_6lo252"
  }
}
{
  "timestamp": "2025-06-24T22:51:01.947Z",
  "level": "INFO",
  "message": "🔄 Processando dados terapêuticos...",
  "meta": {}
}
{
  "timestamp": "2025-06-24T22:51:01.948Z",
  "level": "ERROR",
  "message": "Erro ao analisar padrões terapêuticos:",
  "meta": {},
  "error": {
    "name": "TypeError",
    "message": "Cannot read properties of undefined (reading 'timeEngaged')",
    "stack": "TypeError: Cannot read properties of undefined (reading 'timeEngaged')\n    at SystemOrchestrator.identifyEngagementPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:985:25)\n    at SystemOrchestrator.analyzeTherapeuticPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:923:34)\n    at SystemOrchestrator.processTherapeuticData (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:755:35)\n    at testIntegrationFlow (file:///C:/Projetos/protalbetinav3/test-final-integration.js:105:46)"
  }
}
{
  "timestamp": "2025-06-24T22:51:01.952Z",
  "level": "INFO",
  "message": "📈 Preparando dados para dashboard...",
  "meta": {}
}
{
  "timestamp": "2025-06-24T22:52:52.477Z",
  "level": "INFO",
  "message": "🎮 Processando entrada de dados do jogo...",
  "meta": {
    "gameId": "memoria-cognitiva"
  }
}
{
  "timestamp": "2025-06-24T22:52:52.526Z",
  "level": "INFO",
  "message": "🔍 Refinando dados de métricas...",
  "meta": {
    "sessionId": "session_1750805572474",
    "type": "memoria-cognitiva"
  }
}
{
  "timestamp": "2025-06-24T22:52:52.528Z",
  "level": "INFO",
  "message": "✅ Dados de métricas refinados:",
  "meta": {
    "sessionId": "session_1750805572474",
    "accuracy": 85,
    "engagement": 76,
    "processingId": "refined_1750805572528_6nq4fs"
  }
}
{
  "timestamp": "2025-06-24T22:52:52.531Z",
  "level": "INFO",
  "message": "🔄 Processando dados terapêuticos...",
  "meta": {}
}
{
  "timestamp": "2025-06-24T22:52:52.533Z",
  "level": "ERROR",
  "message": "Erro ao analisar padrões terapêuticos:",
  "meta": {},
  "error": {
    "name": "TypeError",
    "message": "Cannot read properties of undefined (reading 'timeEngaged')",
    "stack": "TypeError: Cannot read properties of undefined (reading 'timeEngaged')\n    at SystemOrchestrator.identifyEngagementPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:985:25)\n    at SystemOrchestrator.analyzeTherapeuticPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:923:34)\n    at SystemOrchestrator.processTherapeuticData (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:755:35)\n    at testIntegrationFlow (file:///C:/Projetos/protalbetinav3/test-final-integration.js:105:46)"
  }
}
{
  "timestamp": "2025-06-24T22:52:52.536Z",
  "level": "INFO",
  "message": "📈 Preparando dados para dashboard...",
  "meta": {}
}
{
  "timestamp": "2025-06-24T22:53:12.391Z",
  "level": "INFO",
  "message": "🎮 Processando entrada de dados do jogo...",
  "meta": {
    "gameId": "memoria-cognitiva"
  }
}
{
  "timestamp": "2025-06-24T22:53:12.437Z",
  "level": "INFO",
  "message": "🔍 Refinando dados de métricas...",
  "meta": {
    "sessionId": "session_1750805592388",
    "type": "memoria-cognitiva"
  }
}
{
  "timestamp": "2025-06-24T22:53:12.439Z",
  "level": "INFO",
  "message": "✅ Dados de métricas refinados:",
  "meta": {
    "sessionId": "session_1750805592388",
    "accuracy": 85,
    "engagement": 76,
    "processingId": "refined_1750805592438_nfgwui"
  }
}
{
  "timestamp": "2025-06-24T22:53:12.442Z",
  "level": "INFO",
  "message": "🔄 Processando dados terapêuticos...",
  "meta": {}
}
{
  "timestamp": "2025-06-24T22:53:12.443Z",
  "level": "ERROR",
  "message": "Erro ao analisar padrões terapêuticos:",
  "meta": {},
  "error": {
    "name": "TypeError",
    "message": "Cannot read properties of undefined (reading 'timeEngaged')",
    "stack": "TypeError: Cannot read properties of undefined (reading 'timeEngaged')\n    at SystemOrchestrator.identifyEngagementPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:985:25)\n    at SystemOrchestrator.analyzeTherapeuticPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:923:34)\n    at SystemOrchestrator.processTherapeuticData (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:755:35)\n    at testIntegrationFlow (file:///C:/Projetos/protalbetinav3/test-final-integration.js:105:46)"
  }
}
{
  "timestamp": "2025-06-24T22:53:12.447Z",
  "level": "INFO",
  "message": "📈 Preparando dados para dashboard...",
  "meta": {}
}
{
  "timestamp": "2025-06-24T22:54:42.279Z",
  "level": "INFO",
  "message": "🎮 Processando entrada de dados do jogo...",
  "meta": {
    "gameId": "memoria-cognitiva"
  }
}
{
  "timestamp": "2025-06-24T22:54:42.319Z",
  "level": "INFO",
  "message": "🔍 Refinando dados de métricas...",
  "meta": {
    "sessionId": "session_1750805682275",
    "type": "memoria-cognitiva"
  }
}
{
  "timestamp": "2025-06-24T22:54:42.322Z",
  "level": "INFO",
  "message": "✅ Dados de métricas refinados:",
  "meta": {
    "sessionId": "session_1750805682275",
    "accuracy": 85,
    "engagement": 76,
    "processingId": "refined_1750805682322_bprm32"
  }
}
{
  "timestamp": "2025-06-24T22:54:42.326Z",
  "level": "INFO",
  "message": "🔄 Processando dados terapêuticos...",
  "meta": {}
}
{
  "timestamp": "2025-06-24T22:54:42.328Z",
  "level": "ERROR",
  "message": "Erro ao analisar padrões terapêuticos:",
  "meta": {},
  "error": {
    "name": "TypeError",
    "message": "Cannot read properties of undefined (reading 'timeEngaged')",
    "stack": "TypeError: Cannot read properties of undefined (reading 'timeEngaged')\n    at SystemOrchestrator.identifyEngagementPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:985:25)\n    at SystemOrchestrator.analyzeTherapeuticPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:923:34)\n    at SystemOrchestrator.processTherapeuticData (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:755:35)\n    at testIntegrationFlow (file:///C:/Projetos/protalbetinav3/test-final-integration.js:105:46)"
  }
}
{
  "timestamp": "2025-06-24T22:54:42.331Z",
  "level": "INFO",
  "message": "📈 Preparando dados para dashboard...",
  "meta": {}
}
{
  "timestamp": "2025-06-25T01:21:06.711Z",
  "level": "INFO",
  "message": "CognitiveAnalyzer conectado ao SystemOrchestrator",
  "meta": {}
}
{
  "timestamp": "2025-06-25T01:31:53.126Z",
  "level": "INFO",
  "message": "CognitiveAnalyzer conectado ao SystemOrchestrator",
  "meta": {}
}
{
  "timestamp": "2025-06-25T01:50:58.708Z",
  "level": "INFO",
  "message": "CognitiveAnalyzer conectado ao SystemOrchestrator",
  "meta": {}
}
{
  "timestamp": "2025-06-25T01:51:21.755Z",
  "level": "INFO",
  "message": "CognitiveAnalyzer conectado ao SystemOrchestrator",
  "meta": {}
}
{"timestamp":"2025-07-07 23:05:28.269","level":"info","message":"Configuration validation passed","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"config_validation_success","environment":"development","requestId":"req_1751940328261_n3d7dfl86s","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-07 23:05:28.295","level":"info","message":"🚀 SystemIntegration inicializada","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_integration_init","components":["config","logger","cache","health"],"requestId":"req_1751940328294_rxd6yf1jtt","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-07 23:05:28.310","level":"info","message":"🚀 SystemIntegration inicializada","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_integration_init","components":["config","logger","cache","health"],"requestId":"req_1751940328309_w7pe7ilbjwc","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-07 23:05:28.273","level":"info","message":"⚙️ GameConfig inicializado","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"game_config_init","environment":"development","hotReloadEnabled":true,"requestId":"req_1751940328271_8qjvz7qyys","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-07 23:05:28.281","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1751940328280_4781yci30s7","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-07 23:05:28.283","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1751940328282_4sxcrtv19mn","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-07 23:05:28.284","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1751940328284_w7jt49pe9wq","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-07 23:05:28.286","level":"info","message":"Health component registered: multisensory_analyzer","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"multisensory_analyzer","requestId":"req_1751940328285_2dz58qetl7p","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-07 23:05:28.287","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1751940328286_g50nlgzb6in","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-07 23:05:28.288","level":"info","message":"Health component registered: filesystem","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"filesystem","requestId":"req_1751940328287_7pa2691etab","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-07 23:05:28.289","level":"info","message":"Health monitoring started","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_monitoring_started","interval":30000,"requestId":"req_1751940328288_prefljcdh","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-07 23:05:28.291","level":"info","message":"🏥 HealthCheckService inicializado","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_service_init","checkInterval":30000,"alertThresholds":{"cpu":80,"memory":85,"disk":90,"responseTime":5000},"requestId":"req_1751940328290_ige4rhp256","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-07 23:05:28.292","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1751940328291_lyrhev04izg","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-07 23:05:28.293","level":"info","message":"Health component registered: structured_logger","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"structured_logger","requestId":"req_1751940328292_pelw1jar0v9","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-07 23:05:28.294","level":"info","message":"Health component registered: game_config","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"game_config","requestId":"req_1751940328293_lgje6gd0kh","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-07 23:05:28.304","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1751940328303_vbkrpmgyl1","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-07 23:05:28.307","level":"info","message":"Health component registered: structured_logger","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"structured_logger","requestId":"req_1751940328306_alfutshsxr6","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-07 23:05:28.309","level":"info","message":"Health component registered: game_config","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"game_config","requestId":"req_1751940328308_ibu8bp8nutq","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-07 23:05:28.476","level":"info","message":"Test log message","service":"betina-v3","version":"3.0.0","environment":"development","context":{"userId":"test_user","requestId":"req_1751940328475_fdw2g1sb28d"},"data":{}}
{"timestamp":"2025-07-07 23:05:28.478","level":"info","message":"Game Action: ColorMatch - card_select","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"game_action","gameType":"ColorMatch","action":"card_select","userId":"user123","sessionId":"session456","requestId":"req_1751940328477_pdeadv4mpw"},"data":{"score":100}}
{"timestamp":"2025-07-07 23:05:28.480","level":"error","message":"Test error message","service":"betina-v3","version":"3.0.0","environment":"development","context":{"component":"test","requestId":"req_1751940328479_8412udurhgp","userId":"anonymous"},"data":{"stack":"test stack"}}
{"timestamp":"2025-07-07 23:05:28.481","level":"info","message":"Multisensory Event: sensor_update","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"multisensory_event","event":"sensor_update","userId":"user123","sessionId":"session456","requestId":"req_1751940328481_183tz4t8dof"},"data":{"sensorData":{"visual":0.8,"auditory":0.6}}}
{"timestamp":"2025-07-07 23:05:28.600","level":"warn","message":"System Health: overall - warning","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"warning"},"data":{"metrics":{"uptime":0.9324768,"memory":{"rss":46592000,"heapTotal":12353536,"heapUsed":8439880,"external":1927481,"arrayBuffers":35091},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":2476,"timestamp":1751940328597,"memoryUsage":{"total":17098924032,"free":**********,"used":13859737600,"percentage":81.0561973026023}}}}
{"timestamp":"2025-07-07 23:05:28.709","level":"warn","message":"System Health: overall - warning","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"warning"},"data":{"metrics":{"uptime":1.0499182,"memory":{"rss":46649344,"heapTotal":12353536,"heapUsed":8604224,"external":1927761,"arrayBuffers":35331},"cpu":{"percentage":15.384615384615385,"user":16000,"system":0,"total":16000},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":2476,"timestamp":1751940328706,"memoryUsage":{"total":17098924032,"free":**********,"used":13891342336,"percentage":81.24103195033132}}}}
{"timestamp":"2025-07-08 17:00:18.635","level":"info","message":"🧠 AIBrainOrchestrator inicializado - Modo Supremo","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"ai_brain_init","version":"3.2.3","mode":"supremo","apiConfigs":[{"endpoint":"https://api.x.ai/v1","model":"grok-3"}],"supportedGames":["ColorMatch","ImageAssociation","MemoryGame","MusicalSequence","PadroesVisuais","ContagemNumeros","PatternMatching","SequenceLearning","CreativePainting","LetterRecognition","QuebraCabeca"],"cacheEnabled":true,"cacheMaxSize":1000,"cacheTTL":3600000,"timestamp":"2025-07-08T20:00:18.625Z","requestId":"req_1752004818625_oc2razn9r8","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:18.642","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004818642_u6gfie38nsg","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:18.644","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004818644_rw51q880sim","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:18.646","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004818646_8fooxudfk4n","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:18.647","level":"info","message":"Health component registered: multisensory_analyzer","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"multisensory_analyzer","requestId":"req_1752004818646_h0evvwr3lrr","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:18.648","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004818647_jzvyu9zzo7g","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:18.649","level":"info","message":"Health component registered: filesystem","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"filesystem","requestId":"req_1752004818648_jrc9okmp8zk","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:18.650","level":"info","message":"Health monitoring started","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_monitoring_started","interval":30000,"requestId":"req_1752004818650_mx7aqy6gse","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:18.651","level":"info","message":"🏥 HealthCheckService inicializado","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_service_init","checkInterval":30000,"alertThresholds":{"cpu":80,"memory":85,"disk":90,"responseTime":5000},"requestId":"req_1752004818650_pj60ajd30zd","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:18.657","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004818656_qv2ibq4z7dl","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:18.658","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004818657_m3mopp24p2c","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:18.658","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004818658_qltrt26ws7","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:18.659","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004818659_n4dheq18xd8","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:18.660","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752004818659_fit7x9jjwj","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:18.676","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004818676_9m1slbnhmte","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:18.677","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004818677_e7s8uen15ps","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:18.678","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004818677_m0yo1m7j9g","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:18.678","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004818678_5yil4ong6vs","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:18.679","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752004818679_fnb3rv7d5vd","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:38.113","level":"info","message":"🧠 AIBrainOrchestrator inicializado - Modo Supremo","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"ai_brain_init","version":"3.2.3","mode":"supremo","apiConfigs":[{"endpoint":"https://api.x.ai/v1","model":"grok-3"}],"supportedGames":["ColorMatch","ImageAssociation","MemoryGame","MusicalSequence","PadroesVisuais","ContagemNumeros","PatternMatching","SequenceLearning","CreativePainting","LetterRecognition","QuebraCabeca"],"cacheEnabled":true,"cacheMaxSize":1000,"cacheTTL":3600000,"timestamp":"2025-07-08T20:00:38.106Z","requestId":"req_1752004838106_u1kngi87dl8","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:38.118","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004838118_wrmovr46utg","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:38.120","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004838120_sq6mon8k6dd","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:38.122","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004838122_fcjn4m61rzm","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:38.123","level":"info","message":"Health component registered: multisensory_analyzer","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"multisensory_analyzer","requestId":"req_1752004838123_p105fpj5zo","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:38.124","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004838124_gzgsixs5qb","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:38.125","level":"info","message":"Health component registered: filesystem","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"filesystem","requestId":"req_1752004838124_v8ox0g66z2t","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:38.126","level":"info","message":"Health monitoring started","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_monitoring_started","interval":30000,"requestId":"req_1752004838125_iep4418qfjm","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:38.127","level":"info","message":"🏥 HealthCheckService inicializado","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_service_init","checkInterval":30000,"alertThresholds":{"cpu":80,"memory":85,"disk":90,"responseTime":5000},"requestId":"req_1752004838126_14uzskwol43","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:38.132","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004838131_3lodgu9p6f4","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:38.133","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004838132_jy1tbekusv","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:38.133","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004838133_85px2p6xfni","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:38.134","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004838134_9ejduc69twu","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:38.135","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752004838134_6kt0m2ximn","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:38.152","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004838151_27taq7kfza3","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:38.153","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004838152_r151e4wyccr","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:38.154","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004838153_ak3wpw9h4e","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:38.154","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004838154_3rpxasqsm2x","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:00:38.155","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752004838155_o3tpbjavwb9","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:06.695","level":"info","message":"🧠 AIBrainOrchestrator inicializado - Modo Supremo","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"ai_brain_init","version":"3.2.3","mode":"supremo","apiConfigs":[{"endpoint":"https://api.x.ai/v1","model":"grok-3"}],"supportedGames":["ColorMatch","ImageAssociation","MemoryGame","MusicalSequence","PadroesVisuais","ContagemNumeros","PatternMatching","SequenceLearning","CreativePainting","LetterRecognition","QuebraCabeca"],"cacheEnabled":true,"cacheMaxSize":1000,"cacheTTL":3600000,"timestamp":"2025-07-08T20:01:06.684Z","requestId":"req_1752004866684_qzrzcdwtx9o","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:06.705","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004866703_jrmoazjoh6b","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:06.707","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004866706_orhht9qzu6f","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:06.710","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004866709_ew45x78wo88","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:06.711","level":"info","message":"Health component registered: multisensory_analyzer","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"multisensory_analyzer","requestId":"req_1752004866711_935f4i499l7","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:06.713","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004866712_kzil3u9zw5","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:06.714","level":"info","message":"Health component registered: filesystem","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"filesystem","requestId":"req_1752004866713_pvnz4masrin","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:06.715","level":"info","message":"Health monitoring started","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_monitoring_started","interval":30000,"requestId":"req_1752004866715_21lker34nzf","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:06.717","level":"info","message":"🏥 HealthCheckService inicializado","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_service_init","checkInterval":30000,"alertThresholds":{"cpu":80,"memory":85,"disk":90,"responseTime":5000},"requestId":"req_1752004866716_5waatuofjqn","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:06.724","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004866723_9zh44pf7uf","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:06.725","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004866725_ybw7v3cnzcn","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:06.726","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004866726_ifgetvlycp","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:06.727","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004866727_llrhnyjyqml","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:06.728","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752004866728_n1rgtzexha","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:06.755","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004866754_r9qwe6dlsko","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:06.756","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004866756_pnbte480exl","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:06.757","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004866757_ef2kcrfn2yg","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:06.758","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004866758_iv8n4rfhryh","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:06.759","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752004866759_mz4ramzorbi","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:31.715","level":"info","message":"🧠 AIBrainOrchestrator inicializado - Modo Supremo","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"ai_brain_init","version":"3.2.3","mode":"supremo","apiConfigs":[{"endpoint":"https://api.x.ai/v1","model":"grok-3"}],"supportedGames":["ColorMatch","ImageAssociation","MemoryGame","MusicalSequence","PadroesVisuais","ContagemNumeros","PatternMatching","SequenceLearning","CreativePainting","LetterRecognition","QuebraCabeca"],"cacheEnabled":true,"cacheMaxSize":1000,"cacheTTL":3600000,"timestamp":"2025-07-08T20:01:31.709Z","requestId":"req_1752004891709_4yh89q0x9al","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:31.720","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004891719_glvwpg29plu","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:31.721","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004891720_cxa0bkrkfq6","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:31.723","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004891722_xzwzel0l0u","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:31.723","level":"info","message":"Health component registered: multisensory_analyzer","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"multisensory_analyzer","requestId":"req_1752004891723_jf4zrxsbnqj","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:31.724","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004891724_5zy4ir14pix","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:31.725","level":"info","message":"Health component registered: filesystem","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"filesystem","requestId":"req_1752004891725_96l3sxu73f","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:31.726","level":"info","message":"Health monitoring started","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_monitoring_started","interval":30000,"requestId":"req_1752004891726_hyw675efd4j","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:31.727","level":"info","message":"🏥 HealthCheckService inicializado","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_service_init","checkInterval":30000,"alertThresholds":{"cpu":80,"memory":85,"disk":90,"responseTime":5000},"requestId":"req_1752004891726_oux1u6glz1d","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:31.731","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004891730_wr9v0c8276","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:31.732","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004891732_iw9ij9vtai","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:31.733","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004891732_0enk3sjsfzhv","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:31.733","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004891733_t7mfg1t16y","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:31.734","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752004891734_wmrnf5mhufm","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:31.747","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004891747_shr086z2eo","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:31.748","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004891747_uqfzmb5rmt","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:31.748","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004891748_9t8t7qlboms","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:31.749","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004891748_j13nng1syz","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:01:31.749","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752004891749_nmxci89uf0c","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:01.852","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":30.8103628,"memory":{"rss":62177280,"heapTotal":26480640,"heapUsed":23384736,"external":2407720,"arrayBuffers":33435},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":25712,"timestamp":1752004921848,"memoryUsage":{"total":17098924032,"free":**********,"used":13448519680,"percentage":78.65126282116697}}}}
{"timestamp":"2025-07-08 17:02:06.016","level":"info","message":"🧠 AIBrainOrchestrator inicializado - Modo Supremo","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"ai_brain_init","version":"3.2.3","mode":"supremo","apiConfigs":[{"endpoint":"https://api.x.ai/v1","model":"grok-3"}],"supportedGames":["ColorMatch","ImageAssociation","MemoryGame","MusicalSequence","PadroesVisuais","ContagemNumeros","PatternMatching","SequenceLearning","CreativePainting","LetterRecognition","QuebraCabeca"],"cacheEnabled":true,"cacheMaxSize":1000,"cacheTTL":3600000,"timestamp":"2025-07-08T20:02:06.010Z","requestId":"req_1752004926010_241ccq9kmad","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:06.021","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004926020_4uakqulgice","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:06.022","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004926022_rekjvw1ekad","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:06.024","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004926023_p0hjxjnhwx9","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:06.024","level":"info","message":"Health component registered: multisensory_analyzer","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"multisensory_analyzer","requestId":"req_1752004926024_nta7opsm2ik","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:06.025","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004926025_xp3w8780xw","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:06.026","level":"info","message":"Health component registered: filesystem","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"filesystem","requestId":"req_1752004926026_858sxydr6sa","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:06.027","level":"info","message":"Health monitoring started","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_monitoring_started","interval":30000,"requestId":"req_1752004926027_bbq2oqylscj","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:06.028","level":"info","message":"🏥 HealthCheckService inicializado","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_service_init","checkInterval":30000,"alertThresholds":{"cpu":80,"memory":85,"disk":90,"responseTime":5000},"requestId":"req_1752004926027_8apcw082cha","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:06.031","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004926031_xuchm32rzy","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:06.032","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004926032_3v8fa9a7cih","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:06.033","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004926033_y1wmd0d5os","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:06.033","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004926033_jdxth1aruxa","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:06.034","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752004926034_clsx5wochx7","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:06.048","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004926048_h479yy6whsv","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:06.049","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004926048_eclxubkmvkm","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:06.049","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004926049_tdv22ikvetg","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:06.050","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004926050_chgfrpeaber","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:06.051","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752004926051_89vurlexx9a","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:23.357","level":"info","message":"🧠 AIBrainOrchestrator inicializado - Modo Supremo","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"ai_brain_init","version":"3.2.3","mode":"supremo","apiConfigs":[{"endpoint":"https://api.x.ai/v1","model":"grok-3"}],"supportedGames":["ColorMatch","ImageAssociation","MemoryGame","MusicalSequence","PadroesVisuais","ContagemNumeros","PatternMatching","SequenceLearning","CreativePainting","LetterRecognition","QuebraCabeca"],"cacheEnabled":true,"cacheMaxSize":1000,"cacheTTL":3600000,"timestamp":"2025-07-08T20:02:23.350Z","requestId":"req_1752004943350_qqb9rox1o3m","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:23.362","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004943361_in502tet9r","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:23.363","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004943363_vhvdsi7i2m","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:23.365","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004943364_ikiz6gt9keo","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:23.365","level":"info","message":"Health component registered: multisensory_analyzer","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"multisensory_analyzer","requestId":"req_1752004943365_dlwaqqr37o","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:23.366","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004943366_xd8fvw9m9cb","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:23.367","level":"info","message":"Health component registered: filesystem","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"filesystem","requestId":"req_1752004943367_kjyt1zog8ng","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:23.368","level":"info","message":"Health monitoring started","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_monitoring_started","interval":30000,"requestId":"req_1752004943368_srdl6it7lj","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:23.369","level":"info","message":"🏥 HealthCheckService inicializado","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_service_init","checkInterval":30000,"alertThresholds":{"cpu":80,"memory":85,"disk":90,"responseTime":5000},"requestId":"req_1752004943368_7w0imr81pca","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:23.373","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004943373_9mdmulghuy8","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:23.374","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004943374_hy8rmvnrk46","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:23.375","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004943375_x9fqp2cb5q","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:23.376","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004943375_q6k4eauhgoh","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:23.376","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752004943376_t5h9z3dk6wf","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:23.392","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004943391_1rlgsk1ddqg","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:23.392","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004943392_ebsquyevwfq","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:23.393","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004943393_ndut4jsbne7","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:23.394","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004943393_ep0dej6g13e","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:23.394","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752004943394_68sws03949m","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:44.476","level":"info","message":"🧠 AIBrainOrchestrator inicializado - Modo Supremo","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"ai_brain_init","version":"3.2.3","mode":"supremo","apiConfigs":[{"endpoint":"https://api.x.ai/v1","model":"grok-3"}],"supportedGames":["ColorMatch","ImageAssociation","MemoryGame","MusicalSequence","PadroesVisuais","ContagemNumeros","PatternMatching","SequenceLearning","CreativePainting","LetterRecognition","QuebraCabeca"],"cacheEnabled":true,"cacheMaxSize":1000,"cacheTTL":3600000,"timestamp":"2025-07-08T20:02:44.469Z","requestId":"req_1752004964469_81iy3x0qex","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:44.482","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004964481_943w7phwwik","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:44.483","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004964482_rgjp4stu9j","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:44.484","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004964484_c44d9a7ygc","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:44.485","level":"info","message":"Health component registered: multisensory_analyzer","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"multisensory_analyzer","requestId":"req_1752004964485_73fjaviqq4","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:44.486","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004964485_3sqyzmmvzg7","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:44.486","level":"info","message":"Health component registered: filesystem","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"filesystem","requestId":"req_1752004964486_gn1yq9bb8aq","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:44.487","level":"info","message":"Health monitoring started","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_monitoring_started","interval":30000,"requestId":"req_1752004964487_wtr5b1ms8o","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:44.488","level":"info","message":"🏥 HealthCheckService inicializado","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_service_init","checkInterval":30000,"alertThresholds":{"cpu":80,"memory":85,"disk":90,"responseTime":5000},"requestId":"req_1752004964488_o34pr18dr5","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:44.493","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004964493_e14hzcocj36","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:44.494","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004964494_isc3h9t3dyr","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:44.495","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004964494_ucvz57fdl","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:44.495","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004964495_1dech8m8i5c","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:44.496","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752004964495_5nlhlfyfj5q","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:44.511","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004964511_2abl6rlcaml","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:44.512","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004964512_jfki5r6bigi","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:44.512","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004964512_k545qla6mzq","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:44.513","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004964512_mu62inctvvs","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:02:44.513","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752004964513_r6df3l8c039","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:00.416","level":"info","message":"🧠 AIBrainOrchestrator inicializado - Modo Supremo","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"ai_brain_init","version":"3.2.3","mode":"supremo","apiConfigs":[{"endpoint":"https://api.x.ai/v1","model":"grok-3"}],"supportedGames":["ColorMatch","ImageAssociation","MemoryGame","MusicalSequence","PadroesVisuais","ContagemNumeros","PatternMatching","SequenceLearning","CreativePainting","LetterRecognition","QuebraCabeca"],"cacheEnabled":true,"cacheMaxSize":1000,"cacheTTL":3600000,"timestamp":"2025-07-08T20:03:00.407Z","requestId":"req_1752004980407_tfujznsh0gn","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:00.422","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004980421_h6d2s2kdup9","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:00.423","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004980423_tkuk0ileuh","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:00.425","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004980425_r7kqyft8yv","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:00.426","level":"info","message":"Health component registered: multisensory_analyzer","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"multisensory_analyzer","requestId":"req_1752004980425_wmb0woweiqf","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:00.427","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004980426_9fof736pzk9","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:00.427","level":"info","message":"Health component registered: filesystem","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"filesystem","requestId":"req_1752004980427_zanr8e5o4i","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:00.428","level":"info","message":"Health monitoring started","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_monitoring_started","interval":30000,"requestId":"req_1752004980428_n1cqb623xp","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:00.430","level":"info","message":"🏥 HealthCheckService inicializado","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_service_init","checkInterval":30000,"alertThresholds":{"cpu":80,"memory":85,"disk":90,"responseTime":5000},"requestId":"req_1752004980429_xb5t6usx4","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:00.435","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004980434_oq7b7l7efp9","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:00.436","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004980436_hrf33rwvv6h","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:00.436","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004980436_0tyudcni9fh","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:00.437","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004980437_15v12xedjp1","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:00.438","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752004980437_dj59czr1dmw","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:00.457","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004980456_2ncsc47ysja","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:00.458","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004980457_ubap2j8zszb","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:00.458","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004980458_4og0zz2iuls","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:00.459","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004980459_mpfv57mqaa","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:00.460","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752004980459_c0igzeex9k","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:19.367","level":"info","message":"🧠 AIBrainOrchestrator inicializado - Modo Supremo","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"ai_brain_init","version":"3.2.3","mode":"supremo","apiConfigs":[{"endpoint":"https://api.x.ai/v1","model":"grok-3"}],"supportedGames":["ColorMatch","ImageAssociation","MemoryGame","MusicalSequence","PadroesVisuais","ContagemNumeros","PatternMatching","SequenceLearning","CreativePainting","LetterRecognition","QuebraCabeca"],"cacheEnabled":true,"cacheMaxSize":1000,"cacheTTL":3600000,"timestamp":"2025-07-08T20:03:19.356Z","requestId":"req_1752004999356_8y1g0yj3c19","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:19.375","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004999374_e3zlt761o8m","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:19.377","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004999376_9v9sc1agvl","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:19.380","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004999379_q9atw4l7yke","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:19.381","level":"info","message":"Health component registered: multisensory_analyzer","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"multisensory_analyzer","requestId":"req_1752004999380_1yk6q93bi2g","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:19.382","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004999382_4u9wnqqy6wf","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:19.383","level":"info","message":"Health component registered: filesystem","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"filesystem","requestId":"req_1752004999383_qazcy34q3n","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:19.385","level":"info","message":"Health monitoring started","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_monitoring_started","interval":30000,"requestId":"req_1752004999385_1yfko3jrd1f","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:19.387","level":"info","message":"🏥 HealthCheckService inicializado","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_service_init","checkInterval":30000,"alertThresholds":{"cpu":80,"memory":85,"disk":90,"responseTime":5000},"requestId":"req_1752004999386_swx98so0p5","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:19.393","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004999392_n4kyvyant6j","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:19.394","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004999394_1ritxktb3pg","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:19.395","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004999395_0t45aktgj2nl","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:19.396","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004999396_d7xbjilrnyu","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:19.397","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752004999397_o8r54f1eh9i","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:19.414","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752004999413_pwq23kfb2ug","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:19.414","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752004999414_wy3kpp207j","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:19.415","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752004999415_t3nw4e54fc","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:19.416","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752004999415_3h0kl3ofo88","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:19.416","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752004999416_loktgbayq7m","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:46.059","level":"info","message":"🧠 AIBrainOrchestrator inicializado - Modo Supremo","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"ai_brain_init","version":"3.2.3","mode":"supremo","apiConfigs":[{"endpoint":"https://api.x.ai/v1","model":"grok-3"}],"supportedGames":["ColorMatch","ImageAssociation","MemoryGame","MusicalSequence","PadroesVisuais","ContagemNumeros","PatternMatching","SequenceLearning","CreativePainting","LetterRecognition","QuebraCabeca"],"cacheEnabled":true,"cacheMaxSize":1000,"cacheTTL":3600000,"timestamp":"2025-07-08T20:03:46.054Z","requestId":"req_1752005026054_naff5agodyo","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:46.064","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752005026064_x7dnprzd3q","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:46.066","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752005026065_bc96dl6s53h","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:46.067","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752005026067_hbuzvd3fnhe","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:46.068","level":"info","message":"Health component registered: multisensory_analyzer","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"multisensory_analyzer","requestId":"req_1752005026067_i2u09x2lwil","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:46.068","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752005026068_0mx5wjjyd9id","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:46.069","level":"info","message":"Health component registered: filesystem","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"filesystem","requestId":"req_1752005026069_p9aaz0xqkyh","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:46.070","level":"info","message":"Health monitoring started","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_monitoring_started","interval":30000,"requestId":"req_1752005026070_jto2mx0ulkm","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:46.071","level":"info","message":"🏥 HealthCheckService inicializado","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_service_init","checkInterval":30000,"alertThresholds":{"cpu":80,"memory":85,"disk":90,"responseTime":5000},"requestId":"req_1752005026070_593p8jq9m37","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:46.075","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752005026074_vrdudh2dkoc","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:46.075","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752005026075_08gbb64cqvwm","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:46.076","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752005026076_ruzzcccll0h","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:46.077","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752005026076_4k1tz8rl1t4","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:46.077","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752005026077_b0v51g2jput","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:46.089","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752005026089_cw0takvhq9p","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:46.090","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752005026089_c8157gwh42o","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:46.090","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752005026090_yv2crdoz0zd","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:46.091","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752005026091_6ot5wrtvmr8","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:03:46.092","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752005026091_tx840qwr0po","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:03.211","level":"info","message":"🧠 AIBrainOrchestrator inicializado - Modo Supremo","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"ai_brain_init","version":"3.2.3","mode":"supremo","apiConfigs":[{"endpoint":"https://api.x.ai/v1","model":"grok-3"}],"supportedGames":["ColorMatch","ImageAssociation","MemoryGame","MusicalSequence","PadroesVisuais","ContagemNumeros","PatternMatching","SequenceLearning","CreativePainting","LetterRecognition","QuebraCabeca"],"cacheEnabled":true,"cacheMaxSize":1000,"cacheTTL":3600000,"timestamp":"2025-07-08T20:04:03.205Z","requestId":"req_1752005043205_cl4jkvvgcxm","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:03.216","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752005043215_x2jn4tjt33","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:03.218","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752005043217_1e7o56uf9lj","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:03.220","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752005043219_knqp8htuokf","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:03.220","level":"info","message":"Health component registered: multisensory_analyzer","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"multisensory_analyzer","requestId":"req_1752005043220_zznx6s7lx5h","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:03.221","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752005043221_dxxcovsqfdd","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:03.222","level":"info","message":"Health component registered: filesystem","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"filesystem","requestId":"req_1752005043222_3wwmo7gojb","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:03.223","level":"info","message":"Health monitoring started","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_monitoring_started","interval":30000,"requestId":"req_1752005043223_qav8tell5w8","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:03.224","level":"info","message":"🏥 HealthCheckService inicializado","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_service_init","checkInterval":30000,"alertThresholds":{"cpu":80,"memory":85,"disk":90,"responseTime":5000},"requestId":"req_1752005043223_qang617cfi","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:03.228","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752005043228_nr4q3nvb8z9","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:03.229","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752005043229_jat2jh9knnn","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:03.229","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752005043229_26nn7ll0ccw","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:03.230","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752005043230_5po2yuqklvl","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:03.231","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752005043230_mfkhl9xvfn","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:03.243","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752005043243_o23snqc9pps","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:03.244","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752005043244_ewiuesaf48","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:03.245","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752005043244_6zfrq2kid0i","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:03.245","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752005043245_n62ll1bgsa","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:03.246","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752005043246_ndcgnr0ixv","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:33.340","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":30.7564095,"memory":{"rss":62730240,"heapTotal":25956352,"heapUsed":23405168,"external":2407720,"arrayBuffers":33435},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":19012,"timestamp":1752005073337,"memoryUsage":{"total":17098924032,"free":**********,"used":13834158080,"percentage":80.90660005337112}}}}
{"timestamp":"2025-07-08 17:04:46.957","level":"info","message":"🧠 AIBrainOrchestrator inicializado - Modo Supremo","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"ai_brain_init","version":"3.2.3","mode":"supremo","apiConfigs":[{"endpoint":"https://api.x.ai/v1","model":"grok-3"}],"supportedGames":["ColorMatch","ImageAssociation","MemoryGame","MusicalSequence","PadroesVisuais","ContagemNumeros","PatternMatching","SequenceLearning","CreativePainting","LetterRecognition","QuebraCabeca"],"cacheEnabled":true,"cacheMaxSize":1000,"cacheTTL":3600000,"timestamp":"2025-07-08T20:04:46.946Z","requestId":"req_1752005086946_0y0ule762cj","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:46.970","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752005086968_cr0ox017il","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:46.973","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752005086972_1841jo1yhrg","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:46.977","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752005086975_a3xv76t3hz","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:46.978","level":"info","message":"Health component registered: multisensory_analyzer","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"multisensory_analyzer","requestId":"req_1752005086978_q3zjzzgu5h","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:46.979","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752005086979_6mwgdf5lstp","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:46.980","level":"info","message":"Health component registered: filesystem","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"filesystem","requestId":"req_1752005086980_fwumxm0gsuc","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:46.982","level":"info","message":"Health monitoring started","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_monitoring_started","interval":30000,"requestId":"req_1752005086982_j14f8ebx7f","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:46.983","level":"info","message":"🏥 HealthCheckService inicializado","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_service_init","checkInterval":30000,"alertThresholds":{"cpu":80,"memory":85,"disk":90,"responseTime":5000},"requestId":"req_1752005086982_gcv8gs88mop","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:46.987","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752005086987_tgh65mi2ucm","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:46.988","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752005086988_g6cymavvg6r","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:46.989","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752005086988_fh9dq1e8oro","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:46.989","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752005086989_e7id6kuaqzl","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:46.990","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752005086990_ipesusdv66l","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:47.003","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752005087002_rywz2s9tha","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:47.004","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752005087003_j1ig2k0eq6q","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:47.005","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752005087004_tcypwpb8tpm","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:47.005","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752005087005_zdaec4o1z1e","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:04:47.006","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752005087006_ai6i9v8lvou","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:05:17.095","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":30.9095314,"memory":{"rss":77283328,"heapTotal":57151488,"heapUsed":29876712,"external":2603081,"arrayBuffers":228796},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":1888,"timestamp":1752005117093,"memoryUsage":{"total":17098924032,"free":**********,"used":14490464256,"percentage":84.74488937948162}}}}
{"timestamp":"2025-07-08 17:05:41.564","level":"info","message":"🧠 AIBrainOrchestrator inicializado - Modo Supremo","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"ai_brain_init","version":"3.2.3","mode":"supremo","apiConfigs":[{"endpoint":"https://api.x.ai/v1","model":"grok-3"}],"supportedGames":["ColorMatch","ImageAssociation","MemoryGame","MusicalSequence","PadroesVisuais","ContagemNumeros","PatternMatching","SequenceLearning","CreativePainting","LetterRecognition","QuebraCabeca"],"cacheEnabled":true,"cacheMaxSize":1000,"cacheTTL":3600000,"timestamp":"2025-07-08T20:05:41.553Z","requestId":"req_1752005141553_errl30kcc2e","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:05:41.569","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752005141569_tq1c1zm237","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:05:41.571","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752005141570_eu27tg2p4cg","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:05:41.572","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752005141572_kxvosyx36g","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:05:41.573","level":"info","message":"Health component registered: multisensory_analyzer","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"multisensory_analyzer","requestId":"req_1752005141573_e41qcjo0z2","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:05:41.574","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752005141573_yizb3rabukp","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:05:41.574","level":"info","message":"Health component registered: filesystem","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"filesystem","requestId":"req_1752005141574_brd3702ru5s","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:05:41.575","level":"info","message":"Health monitoring started","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_monitoring_started","interval":30000,"requestId":"req_1752005141575_tmbd5kuxpnl","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:05:41.577","level":"info","message":"🏥 HealthCheckService inicializado","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_service_init","checkInterval":30000,"alertThresholds":{"cpu":80,"memory":85,"disk":90,"responseTime":5000},"requestId":"req_1752005141576_ubds684wh9","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:05:41.583","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752005141582_klwvzt3h1us","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:05:41.584","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752005141584_yy2tilyv4z","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:05:41.585","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752005141585_px8pwsmf9qc","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:05:41.586","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752005141586_arpfqbtdymd","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:05:41.587","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752005141587_64gkmlcskny","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:05:41.606","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752005141605_f5a6p187ibp","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:05:41.606","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752005141606_5qba0gma4gv","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:05:41.607","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752005141607_z8o6lk1co9k","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:05:41.607","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752005141607_j5pb893lkdb","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:05:41.608","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752005141608_ptxs2kbmu1g","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:06:11.696","level":"warn","message":"Health Alert: Memory usage is 90.73%","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"memory_high","severity":"warning","threshold":85,"requestId":"req_1752005171695_ac2w0ixiage","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:06:11.698","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":30.9043363,"memory":{"rss":62492672,"heapTotal":25956352,"heapUsed":23415576,"external":2407720,"arrayBuffers":33435},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":15460,"timestamp":1752005171691,"memoryUsage":{"total":17098924032,"free":**********,"used":15513927680,"percentage":90.73043222466082}}}}
{"timestamp":"2025-07-08 17:06:40.082","level":"warn","message":"Health Alert: Memory usage is 91.59%","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"memory_high","severity":"warning","threshold":85,"requestId":"req_1752005200081_hb52559xild","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:06:40.084","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":60.90797,"memory":{"rss":62734336,"heapTotal":25956352,"heapUsed":23662160,"external":2408000,"arrayBuffers":33675},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":15460,"timestamp":1752005200079,"memoryUsage":{"total":17098924032,"free":**********,"used":15660392448,"percentage":91.58700523314893}}}}
{"timestamp":"2025-07-08 17:07:10.091","level":"warn","message":"Health Alert: Memory usage is 91.89%","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"memory_high","severity":"warning","threshold":85,"requestId":"req_1752005230090_m8x2259h72","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:07:10.092","level":"warn","message":"Health Alert: Component ai_brain has 3 consecutive failures","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"component_failure","severity":"critical","component":"ai_brain","requestId":"req_1752005230092_8ju0xuafy7","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:07:10.093","level":"warn","message":"Health Alert: Component system_orchestrator has 3 consecutive failures","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"component_failure","severity":"critical","component":"system_orchestrator","requestId":"req_1752005230093_muavzecwrv","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:07:10.094","level":"warn","message":"Health Alert: Component intelligent_cache has 3 consecutive failures","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"component_failure","severity":"critical","component":"intelligent_cache","requestId":"req_1752005230093_zqa89umnusr","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:07:10.094","level":"warn","message":"Health Alert: Component database has 3 consecutive failures","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"component_failure","severity":"critical","component":"database","requestId":"req_1752005230094_nnhg8ag6y8b","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:07:10.095","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":90.9161086,"memory":{"rss":62910464,"heapTotal":25956352,"heapUsed":23848680,"external":2416392,"arrayBuffers":42067},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":15460,"timestamp":1752005230088,"memoryUsage":{"total":17098924032,"free":**********,"used":15712202752,"percentage":91.89000853267257}}}}
{"timestamp":"2025-07-08 17:07:40.095","level":"warn","message":"Health Alert: Memory usage is 87.21%","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"memory_high","severity":"warning","threshold":85,"requestId":"req_1752005260095_cxlhl4t722q","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:07:40.096","level":"warn","message":"Health Alert: Component ai_brain has 4 consecutive failures","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"component_failure","severity":"critical","component":"ai_brain","requestId":"req_1752005260096_elg6s3bu1r7","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:07:40.097","level":"warn","message":"Health Alert: Component system_orchestrator has 4 consecutive failures","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"component_failure","severity":"critical","component":"system_orchestrator","requestId":"req_1752005260096_e75v1tlmoyc","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:07:40.097","level":"warn","message":"Health Alert: Component intelligent_cache has 4 consecutive failures","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"component_failure","severity":"critical","component":"intelligent_cache","requestId":"req_1752005260097_jr4mdzhxicp","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:07:40.098","level":"warn","message":"Health Alert: Component database has 4 consecutive failures","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"component_failure","severity":"critical","component":"database","requestId":"req_1752005260097_lw98pgkkf8","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:07:40.098","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":120.9203167,"memory":{"rss":63098880,"heapTotal":25956352,"heapUsed":24217784,"external":2432976,"arrayBuffers":58651},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":15460,"timestamp":1752005260093,"memoryUsage":{"total":17098924032,"free":**********,"used":14912675840,"percentage":87.21411833920942}}}}
{"timestamp":"2025-07-08 17:08:10.109","level":"warn","message":"Health Alert: Memory usage is 89.29%","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"memory_high","severity":"warning","threshold":85,"requestId":"req_1752005290109_tj9qd7lwi0b","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:10.110","level":"warn","message":"Health Alert: Component ai_brain has 5 consecutive failures","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"component_failure","severity":"critical","component":"ai_brain","requestId":"req_1752005290110_9c54cvv03o","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:10.111","level":"warn","message":"Health Alert: Component system_orchestrator has 5 consecutive failures","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"component_failure","severity":"critical","component":"system_orchestrator","requestId":"req_1752005290110_z0fi379ynh","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:10.111","level":"warn","message":"Health Alert: Component intelligent_cache has 5 consecutive failures","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"component_failure","severity":"critical","component":"intelligent_cache","requestId":"req_1752005290111_pgsppyqr5no","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:10.112","level":"warn","message":"Health Alert: Component database has 5 consecutive failures","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"component_failure","severity":"critical","component":"database","requestId":"req_1752005290111_uctwxxkp4ee","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:10.112","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":150.9355764,"memory":{"rss":63557632,"heapTotal":27004928,"heapUsed":23632856,"external":2441368,"arrayBuffers":50059},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":15460,"timestamp":1752005290108,"memoryUsage":{"total":17098924032,"free":**********,"used":15267700736,"percentage":89.29041796680929}}}}
{"timestamp":"2025-07-08 17:08:40.116","level":"warn","message":"Health Alert: Memory usage is 93.87%","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"memory_high","severity":"warning","threshold":85,"requestId":"req_1752005320115_2fmdywoo3bm","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:40.116","level":"warn","message":"Health Alert: Component ai_brain has 6 consecutive failures","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"component_failure","severity":"critical","component":"ai_brain","requestId":"req_1752005320116_3ao689c7fi3","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:40.117","level":"warn","message":"Health Alert: Component system_orchestrator has 6 consecutive failures","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"component_failure","severity":"critical","component":"system_orchestrator","requestId":"req_1752005320117_9gu1ntsh6pb","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:40.117","level":"warn","message":"Health Alert: Component intelligent_cache has 6 consecutive failures","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"component_failure","severity":"critical","component":"intelligent_cache","requestId":"req_1752005320117_lycomxf9cvn","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:40.118","level":"warn","message":"Health Alert: Component database has 6 consecutive failures","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"component_failure","severity":"critical","component":"database","requestId":"req_1752005320118_lz7hzfat7o8","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:40.118","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":180.9426205,"memory":{"rss":63893504,"heapTotal":27004928,"heapUsed":23972912,"external":2440968,"arrayBuffers":66643},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":15460,"timestamp":1752005320114,"memoryUsage":{"total":17098924032,"free":**********,"used":16051576832,"percentage":93.87477716118319}}}}
{"timestamp":"2025-07-08 17:08:54.983","level":"info","message":"🧠 AIBrainOrchestrator inicializado - Modo Supremo","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"ai_brain_init","version":"3.2.3","mode":"supremo","apiConfigs":[{"endpoint":"https://api.x.ai/v1","model":"grok-3"}],"supportedGames":["ColorMatch","ImageAssociation","MemoryGame","MusicalSequence","PadroesVisuais","ContagemNumeros","PatternMatching","SequenceLearning","CreativePainting","LetterRecognition","QuebraCabeca"],"cacheEnabled":true,"cacheMaxSize":1000,"cacheTTL":3600000,"timestamp":"2025-07-08T20:08:54.972Z","requestId":"req_1752005334972_u4ste0bmjn","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:54.994","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752005334992_yqa9exnih5n","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:54.996","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752005334995_y10eli1l7f","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:54.998","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752005334997_z5w0s8ha4vh","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:54.998","level":"info","message":"Health component registered: multisensory_analyzer","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"multisensory_analyzer","requestId":"req_1752005334998_3v9yspxh8lp","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:55.000","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752005334999_1v237edpdif","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:55.000","level":"info","message":"Health component registered: filesystem","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"filesystem","requestId":"req_1752005335000_vac5k190bua","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:55.002","level":"info","message":"Health monitoring started","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_monitoring_started","interval":30000,"requestId":"req_1752005335002_fsw1afr15c9","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:55.003","level":"info","message":"🏥 HealthCheckService inicializado","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_service_init","checkInterval":30000,"alertThresholds":{"cpu":80,"memory":85,"disk":90,"responseTime":5000},"requestId":"req_1752005335002_r1y54tw8jqj","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:55.009","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752005335008_a6kj7xkv0s9","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:55.011","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752005335010_sjak9xzvs3c","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:55.012","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752005335012_vl38e5gi4mo","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:55.013","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752005335013_vqd6zlhr3f","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:55.014","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752005335013_0n4rtg388ay","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:55.033","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752005335033_q1uxvbnbvbh","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:55.034","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752005335034_bs11x70efok","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:55.035","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752005335035_tvege6somc","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:55.035","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752005335035_13cim6awu109","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:08:55.036","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752005335036_8nmf3doj2v","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:20.162","level":"info","message":"🧠 AIBrainOrchestrator inicializado - Modo Supremo","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"ai_brain_init","version":"3.2.3","mode":"supremo","apiConfigs":[{"endpoint":"https://api.x.ai/v1","model":"grok-3"}],"supportedGames":["ColorMatch","ImageAssociation","MemoryGame","MusicalSequence","PadroesVisuais","ContagemNumeros","PatternMatching","SequenceLearning","CreativePainting","LetterRecognition","QuebraCabeca"],"cacheEnabled":true,"cacheMaxSize":1000,"cacheTTL":3600000,"timestamp":"2025-07-08T20:09:20.149Z","requestId":"req_1752005360149_ywgrh63oa0o","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:20.173","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752005360172_j2yblz66r8","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:20.176","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752005360175_dny0wwr3zhq","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:20.179","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752005360178_hnojijn72yu","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:20.180","level":"info","message":"Health component registered: multisensory_analyzer","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"multisensory_analyzer","requestId":"req_1752005360179_0xap3b0b3gxc","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:20.182","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752005360181_vp7i99sz4h","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:20.184","level":"info","message":"Health component registered: filesystem","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"filesystem","requestId":"req_1752005360183_cthfb85tkou","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:20.188","level":"info","message":"Health monitoring started","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_monitoring_started","interval":30000,"requestId":"req_1752005360188_amasenpwv9","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:20.190","level":"info","message":"🏥 HealthCheckService inicializado","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_service_init","checkInterval":30000,"alertThresholds":{"cpu":80,"memory":85,"disk":90,"responseTime":5000},"requestId":"req_1752005360189_b8vrwi7cl4p","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:20.202","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752005360201_xsqjqamtja","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:20.203","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752005360203_nlin6ls64ch","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:20.204","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752005360204_l5i124bjrba","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:20.205","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752005360205_gpo1j3y6jr","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:20.206","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752005360206_0aaz1ft0hxoq","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:20.234","level":"info","message":"Health component registered: system_orchestrator","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"system_orchestrator","requestId":"req_1752005360234_7izrem0kfx7","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:20.236","level":"info","message":"Health component registered: ai_brain","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"ai_brain","requestId":"req_1752005360235_fqcg8uavi29","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:20.237","level":"info","message":"Health component registered: intelligent_cache","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"intelligent_cache","requestId":"req_1752005360236_zipo9f3q15","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:20.238","level":"info","message":"Health component registered: database","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_component_registered","component":"database","requestId":"req_1752005360237_k0j5g0fvx6e","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:20.239","level":"info","message":"Health Check components registered","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_check_init","components":["system_orchestrator","ai_brain","intelligent_cache","database"],"requestId":"req_1752005360238_1l7cb7t8ll7","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:50.305","level":"warn","message":"Health Alert: Memory usage is 90.28%","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"memory_high","severity":"warning","threshold":85,"requestId":"req_1752005390304_2i5zk01cgek","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:09:50.307","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":31.3567183,"memory":{"rss":61665280,"heapTotal":25956352,"heapUsed":23383464,"external":2407720,"arrayBuffers":33435},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":4588,"timestamp":1752005390301,"memoryUsage":{"total":17098924032,"free":**********,"used":15437418496,"percentage":90.28298194149203}}}}
{"timestamp":"2025-07-08 17:10:20.308","level":"warn","message":"Health Alert: Memory usage is 86.09%","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"health_alert","alertType":"memory_high","severity":"warning","threshold":85,"requestId":"req_1752005420308_xvhum11bsgc","userId":"anonymous"},"data":{}}
{"timestamp":"2025-07-08 17:10:20.310","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":61.3614329,"memory":{"rss":62054400,"heapTotal":25956352,"heapUsed":23632008,"external":2408000,"arrayBuffers":33675},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":4588,"timestamp":1752005420307,"memoryUsage":{"total":17098924032,"free":**********,"used":14720450560,"percentage":86.08992315803745}}}}
