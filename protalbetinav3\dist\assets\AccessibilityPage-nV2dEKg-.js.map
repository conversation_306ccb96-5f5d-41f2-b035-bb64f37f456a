{"version": 3, "file": "AccessibilityPage-nV2dEKg-.js", "sources": ["../../src/components/pages/AccessibilityPage/AccessibilityPage.jsx"], "sourcesContent": ["/**\r\n * @file AccessibilityPage.jsx\r\n * @description Página dedicada às configurações de acessibilidade\r\n * @version 3.0.0\r\n */\r\n\r\nimport React, { useState, useCallback, useEffect } from 'react'\r\nimport { useAccessibilityContext } from '../../context/AccessibilityContext'\r\nimport styles from './AccessibilityPage.module.css'\r\n\r\nfunction AccessibilityPage({ onBack }) {\r\n  const { settings, updateSettings, applyPreset } = useAccessibilityContext()\r\n  const [savedSuccessfully, setSavedSuccessfully] = useState(false)\r\n\r\n  // Aplicar configurações ao documento\r\n  useEffect(() => {\r\n    const applyAccessibilitySettings = () => {\r\n      const root = document.documentElement\r\n      const body = document.body\r\n\r\n      // Alto Contraste\r\n      if (settings.highContrast) {\r\n        root.classList.add('high-contrast')\r\n      } else {\r\n        root.classList.remove('high-contrast')\r\n      }\r\n\r\n      // Tam<PERSON><PERSON> da Fonte\r\n      root.classList.remove('font-small', 'font-medium', 'font-large', 'font-extra-large')\r\n      root.classList.add(`font-${settings.fontSize}`)\r\n\r\n      // Fonte para Dislexia\r\n      if (settings.dyslexiaFriendly) {\r\n        root.classList.add('dyslexia-friendly')\r\n      } else {\r\n        root.classList.remove('dyslexia-friendly')\r\n      }\r\n\r\n      // Reduzir Animações\r\n      if (settings.reducedMotion) {\r\n        root.classList.add('reduced-motion')\r\n      } else {\r\n        root.classList.remove('reduced-motion')\r\n      }\r\n\r\n      // Esquema de Cores\r\n      root.classList.remove('scheme-default', 'scheme-dark', 'scheme-soft', 'scheme-high-contrast')\r\n      root.classList.add(`scheme-${settings.colorScheme}`)\r\n\r\n      // Aplicar estilos CSS personalizados\r\n      const customStyles = `\r\n        .high-contrast * {\r\n          filter: contrast(1.5) !important;\r\n          border: 1px solid #000 !important;\r\n        }\r\n        \r\n        .font-small { font-size: 12px !important; }\r\n        .font-medium { font-size: 16px !important; }\r\n        .font-large { font-size: 20px !important; }\r\n        .font-extra-large { font-size: 24px !important; }\r\n        \r\n        .dyslexia-friendly * {\r\n          font-family: 'OpenDyslexic', 'Comic Sans MS', cursive !important;\r\n        }\r\n        \r\n        .reduced-motion * {\r\n          animation: none !important;\r\n          transition: none !important;\r\n        }\r\n        \r\n        .scheme-dark {\r\n          background: #1a1a1a !important;\r\n          color: #ffffff !important;\r\n        }\r\n        \r\n        .scheme-soft {\r\n          filter: brightness(0.9) saturate(0.8) !important;\r\n        }\r\n        \r\n        .scheme-high-contrast {\r\n          filter: contrast(2) brightness(1.2) !important;\r\n        }\r\n      `\r\n\r\n      // Remover estilo anterior se existir\r\n      const existingStyle = document.getElementById('accessibility-styles')\r\n      if (existingStyle) {\r\n        existingStyle.remove()\r\n      }\r\n\r\n      // Adicionar novo estilo\r\n      const styleElement = document.createElement('style')\r\n      styleElement.id = 'accessibility-styles'\r\n      styleElement.textContent = customStyles\r\n      document.head.appendChild(styleElement)\r\n    }\r\n\r\n    applyAccessibilitySettings()\r\n  }, [settings])\r\n\r\n  const [textToSpeechEnabled, setTextToSpeechEnabled] = useState(settings.textToSpeech)\r\n\r\n  // Exibir mensagem de sucesso temporária após salvar\r\n  const showSuccessMessage = () => {\r\n    setSavedSuccessfully(true)\r\n    setTimeout(() => setSavedSuccessfully(false), 2000)\r\n  }\r\n\r\n  // Text-to-Speech\r\n  const speakText = (text) => {\r\n    if (settings.textToSpeech && 'speechSynthesis' in window) {\r\n      const utterance = new SpeechSynthesisUtterance(text)\r\n      utterance.rate = 0.8\r\n      utterance.pitch = 1\r\n      speechSynthesis.speak(utterance)\r\n    }\r\n  }\r\n\r\n  // Alterar configuração individual\r\n  const handleSettingChange = useCallback((key, value) => {\r\n    console.log(`🔧 Alterando configuração: ${key} = ${value}`)\r\n    const newSettings = { [key]: value }\r\n    updateSettings(newSettings)\r\n    showSuccessMessage()\r\n    \r\n    // Feedback por voz\r\n    speakText(`${key} ${value ? 'ativado' : 'desativado'}`)\r\n  }, [updateSettings, settings.textToSpeech])\r\n\r\n  // Aplicar tema preset\r\n  const handleApplyPreset = useCallback((preset) => {\r\n    console.log(`🎨 Aplicando preset: ${preset}`)\r\n    applyPreset(preset)\r\n    showSuccessMessage()\r\n    speakText(`Preset ${preset} aplicado`)\r\n  }, [applyPreset, settings.textToSpeech])\r\n\r\n  return (\r\n    <div className={styles.container}>\r\n      <div className={styles.pageHeader}>\r\n        <button className={styles.backButton} onClick={onBack}>\r\n          ← Voltar\r\n        </button>\r\n        <h1 className={styles.pageTitle}>♿ Configurações de Acessibilidade</h1>\r\n        <p className={styles.pageSubtitle}>\r\n          Personalize sua experiência para ter o melhor acesso ao Portal Betina\r\n        </p>\r\n      </div>\r\n      \r\n      <div className={styles.pageContent}>\r\n        <div className={styles.accessibilityPanel}>\r\n          <div className={styles.panelContent}>\r\n            <p className={styles.panelInfo}>\r\n              Configure suas preferências de acessibilidade para uma melhor experiência no Portal Betina.\r\n            </p>\r\n            \r\n            {/* Presets Rápidos */}\r\n            <div className={styles.accessibilityGroup}>\r\n              <h3 className={styles.groupTitle}>⚡ Configurações Rápidas</h3>\r\n              <div className={styles.presetsGrid}>\r\n                <button \r\n                  className={styles.presetButton}\r\n                  onClick={() => handleApplyPreset('default')}\r\n                >\r\n                  Padrão\r\n                </button>\r\n                <button \r\n                  className={styles.presetButton}\r\n                  onClick={() => handleApplyPreset('high-contrast')}\r\n                >\r\n                  Alto Contraste\r\n                </button>\r\n                <button \r\n                  className={styles.presetButton}\r\n                  onClick={() => handleApplyPreset('autism-friendly')}\r\n                >\r\n                  Autismo\r\n                </button>\r\n                <button \r\n                  className={styles.presetButton}\r\n                  onClick={() => handleApplyPreset('dyslexia')}\r\n                >\r\n                  Dislexia\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Configurações Visuais */}\r\n            <div className={styles.accessibilityGroup}>\r\n              <h3 className={styles.groupTitle}>👁️ Visual</h3>\r\n              \r\n              <div className={styles.optionRow}>\r\n                <label className={styles.optionLabel}>\r\n                  <span className={styles.optionText}>Alto Contraste</span>\r\n                  <div className={styles.switchContainer}>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      className={styles.switchInput}\r\n                      checked={settings.highContrast}\r\n                      onChange={(e) => handleSettingChange('highContrast', e.target.checked)}\r\n                    />\r\n                    <span className={styles.switchSlider}></span>\r\n                  </div>\r\n                </label>\r\n              </div>\r\n\r\n              <div className={styles.optionRow}>\r\n                <label className={styles.optionLabel}>\r\n                  <span className={styles.optionText}>Tamanho da Fonte</span>\r\n                  <select\r\n                    className={styles.selectInput}\r\n                    value={settings.fontSize}\r\n                    onChange={(e) => handleSettingChange('fontSize', e.target.value)}\r\n                  >\r\n                    <option value=\"small\">Pequena</option>\r\n                    <option value=\"medium\">Média</option>\r\n                    <option value=\"large\">Grande</option>\r\n                    <option value=\"extra-large\">Extra Grande</option>\r\n                  </select>\r\n                </label>\r\n              </div>\r\n\r\n              <div className={styles.optionRow}>\r\n                <label className={styles.optionLabel}>\r\n                  <span className={styles.optionText}>Fonte para Dislexia</span>\r\n                  <div className={styles.switchContainer}>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      className={styles.switchInput}\r\n                      checked={settings.dyslexiaFriendly}\r\n                      onChange={(e) => handleSettingChange('dyslexiaFriendly', e.target.checked)}\r\n                    />\r\n                    <span className={styles.switchSlider}></span>\r\n                  </div>\r\n                </label>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Configurações de Movimento */}\r\n            <div className={styles.accessibilityGroup}>\r\n              <h3 className={styles.groupTitle}>🎭 Movimento</h3>\r\n              \r\n              <div className={styles.optionRow}>\r\n                <label className={styles.optionLabel}>\r\n                  <span className={styles.optionText}>Reduzir Animações</span>\r\n                  <div className={styles.switchContainer}>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      className={styles.switchInput}\r\n                      checked={settings.reducedMotion}\r\n                      onChange={(e) => handleSettingChange('reducedMotion', e.target.checked)}\r\n                    />\r\n                    <span className={styles.switchSlider}></span>\r\n                  </div>\r\n                </label>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Configurações de Áudio */}\r\n            <div className={styles.accessibilityGroup}>\r\n              <h3 className={styles.groupTitle}>🔊 Áudio</h3>\r\n              \r\n              <div className={styles.optionRow}>\r\n                <label className={styles.optionLabel}>\r\n                  <span className={styles.optionText}>Leitura de Texto</span>\r\n                  <div className={styles.switchContainer}>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      className={styles.switchInput}\r\n                      checked={settings.textToSpeech}\r\n                      onChange={(e) => handleSettingChange('textToSpeech', e.target.checked)}\r\n                    />\r\n                    <span className={styles.switchSlider}></span>\r\n                  </div>\r\n                </label>\r\n              </div>\r\n\r\n              <div className={styles.optionRow}>\r\n                <label className={styles.optionLabel}>\r\n                  <span className={styles.optionText}>Sons Ativados</span>\r\n                  <div className={styles.switchContainer}>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      className={styles.switchInput}\r\n                      checked={settings.soundEnabled}\r\n                      onChange={(e) => handleSettingChange('soundEnabled', e.target.checked)}\r\n                    />\r\n                    <span className={styles.switchSlider}></span>\r\n                  </div>\r\n                </label>\r\n              </div>\r\n\r\n              <div className={styles.optionRow}>\r\n                <label className={styles.optionLabel}>\r\n                  <span className={styles.optionText}>Leitura Automática</span>\r\n                  <div className={styles.switchContainer}>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      className={styles.switchInput}\r\n                      checked={settings.autoRead}\r\n                      onChange={(e) => handleSettingChange('autoRead', e.target.checked)}\r\n                    />\r\n                    <span className={styles.switchSlider}></span>\r\n                  </div>\r\n                </label>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Temas */}\r\n            <div className={styles.accessibilityGroup}>\r\n              <h3 className={styles.groupTitle}>🎨 Tema</h3>\r\n              \r\n              <div className={styles.optionRow}>\r\n                <label className={styles.optionLabel}>\r\n                  <span className={styles.optionText}>Esquema de Cores</span>\r\n                  <select\r\n                    className={styles.selectInput}\r\n                    value={settings.colorScheme}\r\n                    onChange={(e) => handleSettingChange('colorScheme', e.target.value)}\r\n                  >\r\n                    <option value=\"default\">Padrão</option>\r\n                    <option value=\"dark\">Escuro</option>\r\n                    <option value=\"soft\">Suave</option>\r\n                    <option value=\"high-contrast\">Alto Contraste</option>\r\n                  </select>\r\n                </label>\r\n              </div>\r\n            </div>\r\n\r\n            {savedSuccessfully && (\r\n              <div className={styles.successMessage}>\r\n                ✅ Configurações salvas com sucesso!\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default AccessibilityPage\r\n"], "names": ["AccessibilityPage", "onBack", "settings", "updateSettings", "applyPreset", "useAccessibilityContext", "savedSuccessfully", "setSavedSuccessfully", "useState", "useEffect", "applyAccessibilitySettings", "root", "document", "documentElement", "highContrast", "classList", "add", "remove", "fontSize", "dyslexiaFriendly", "reducedMotion", "colorScheme", "customStyles", "existingStyle", "getElementById", "styleElement", "createElement", "id", "textContent", "head", "append<PERSON><PERSON><PERSON>", "textToSpeechEnabled", "setTextToSpeechEnabled", "textToSpeech", "showSuccessMessage", "setTimeout", "speakText", "text", "window", "utterance", "SpeechSynthesisUtterance", "rate", "pitch", "speechSynthesis", "speak", "handleSettingChange", "useCallback", "key", "value", "console", "log", "newSettings", "handleApplyPreset", "preset", "styles", "container", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "pageHeader", "backButton", "pageTitle", "pageSubtitle", "pageContent", "accessibilityPanel", "panelContent", "panelInfo", "accessibilityGroup", "groupTitle", "presetsGrid", "presetButton", "optionRow", "optionLabel", "optionText", "switchContainer", "switchInput", "e", "target", "checked", "switchSlider", "selectInput", "soundEnabled", "autoRead", "successMessage"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,SAASA,kBAAkB;AAAA,EAAEC;AAAO,GAAG;AAC/B,QAAA;AAAA,IAAEC;AAAAA,IAAUC;AAAAA,IAAgBC;AAAAA,MAAgBC,wBAAwB;AAC1E,QAAM,CAACC,mBAAmBC,oBAAoB,IAAIC,aAAAA,SAAS,KAAK;AAGhEC,eAAAA,UAAU,MAAM;AACd,UAAMC,6BAA6BA,MAAM;AACvC,YAAMC,OAAOC,SAASC;AAItB,UAAIX,SAASY,cAAc;AACpBC,aAAAA,UAAUC,IAAI,eAAe;AAAA,MAAA,OAC7B;AACAD,aAAAA,UAAUE,OAAO,eAAe;AAAA,MAAA;AAIvCN,WAAKI,UAAUE,OAAO,cAAc,eAAe,cAAc,kBAAkB;AACnFN,WAAKI,UAAUC,IAAI,QAAQd,SAASgB,QAAQ,EAAE;AAG9C,UAAIhB,SAASiB,kBAAkB;AACxBJ,aAAAA,UAAUC,IAAI,mBAAmB;AAAA,MAAA,OACjC;AACAD,aAAAA,UAAUE,OAAO,mBAAmB;AAAA,MAAA;AAI3C,UAAIf,SAASkB,eAAe;AACrBL,aAAAA,UAAUC,IAAI,gBAAgB;AAAA,MAAA,OAC9B;AACAD,aAAAA,UAAUE,OAAO,gBAAgB;AAAA,MAAA;AAIxCN,WAAKI,UAAUE,OAAO,kBAAkB,eAAe,eAAe,sBAAsB;AAC5FN,WAAKI,UAAUC,IAAI,UAAUd,SAASmB,WAAW,EAAE;AAGnD,YAAMC,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmCfC,YAAAA,gBAAgBX,SAASY,eAAe,sBAAsB;AACpE,UAAID,eAAe;AACjBA,sBAAcN,OAAO;AAAA,MAAA;AAIjBQ,YAAAA,eAAeb,SAASc,cAAc,OAAO;AACnDD,mBAAaE,KAAK;AAClBF,mBAAaG,cAAcN;AAClBO,eAAAA,KAAKC,YAAYL,YAAY;AAAA,IACxC;AAE2B,+BAAA;AAAA,EAAA,GAC1B,CAACvB,QAAQ,CAAC;AAEb,QAAM,CAAC6B,qBAAqBC,sBAAsB,IAAIxB,aAAAA,SAASN,SAAS+B,YAAY;AAGpF,QAAMC,qBAAqBA,MAAM;AAC/B3B,yBAAqB,IAAI;AACzB4B,eAAW,MAAM5B,qBAAqB,KAAK,GAAG,GAAI;AAAA,EACpD;AAGA,QAAM6B,YAAaC,CAAS,SAAA;AACtBnC,QAAAA,SAAS+B,gBAAgB,qBAAqBK,QAAQ;AAClDC,YAAAA,YAAY,IAAIC,yBAAyBH,IAAI;AACnDE,gBAAUE,OAAO;AACjBF,gBAAUG,QAAQ;AAClBC,sBAAgBC,MAAML,SAAS;AAAA,IAAA;AAAA,EAEnC;AAGA,QAAMM,sBAAsBC,aAAAA,YAAY,CAACC,KAAKC,UAAU;AACtDC,YAAQC,IAAI,8BAA8BH,GAAG,MAAMC,KAAK,EAAE;AAC1D,UAAMG,cAAc;AAAA,MAAE,CAACJ,GAAG,GAAGC;AAAAA,IAAM;AACnC7C,mBAAegD,WAAW;AACP,uBAAA;AAGnBf,cAAU,GAAGW,GAAG,IAAIC,QAAQ,YAAY,YAAY,EAAE;AAAA,EACrD,GAAA,CAAC7C,gBAAgBD,SAAS+B,YAAY,CAAC;AAGpCmB,QAAAA,oBAAoBN,yBAAaO,CAAW,WAAA;AACxCH,YAAAA,IAAI,wBAAwBG,MAAM,EAAE;AAC5CjD,gBAAYiD,MAAM;AACC,uBAAA;AACT,cAAA,UAAUA,MAAM,WAAW;AAAA,EACpC,GAAA,CAACjD,aAAaF,SAAS+B,YAAY,CAAC;AAEvC,6CACG,OAAI,EAAA,WAAWqB,OAAOC,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC9B,OAAI,EAAA,WAAWL,OAAOM,YAAW,QAAA,MAAA,UAAA;AAAA,IAAAJ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAC/B,sBAAA,cAAA,UAAA,EAAO,WAAWL,OAAOO,YAAY,SAAS5D,QAAO,QAAA,MAAA,UAAA;AAAA,IAAAuD,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,UAEvD,GACC,sBAAA,cAAA,MAAA,EAAG,WAAWL,OAAOQ,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAN,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,mCAAiC,GACjE,sBAAA,cAAA,KAAA,EAAE,WAAWL,OAAOS,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAP,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,uEAEnC,CACF,GAEA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAOU,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAR,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAChC,OAAI,EAAA,WAAWL,OAAOW,oBAAmB,QAAA,MAAA,UAAA;AAAA,IAAAT,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACvC,OAAI,EAAA,WAAWL,OAAOY,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAV,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACjC,KAAE,EAAA,WAAWL,OAAOa,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAX,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,6FAEhC,GAGC,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOc,oBAAmB,QAAA,MAAA,UAAA;AAAA,IAAAZ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACvC,MAAG,EAAA,WAAWL,OAAOe,YAAW,QAAA,MAAA,UAAA;AAAA,IAAAb,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,yBAAuB,GACxD,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOgB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAd,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EACjC,GAAA,sBAAA,cAAC,UACC,EAAA,WAAWL,OAAOiB,cAClB,SAAS,MAAMnB,kBAAkB,SAAS,GAAE,QAAA,MAAA,UAAA;AAAA,IAAAI,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAC7C,GAAA,QAED,GACA,sBAAA,cAAC,YACC,WAAWL,OAAOiB,cAClB,SAAS,MAAMnB,kBAAkB,eAAe,GAAE,QAAA,MAAA,UAAA;AAAA,IAAAI,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EACnD,GAAA,gBAED,GACA,sBAAA,cAAC,YACC,WAAWL,OAAOiB,cAClB,SAAS,MAAMnB,kBAAkB,iBAAiB,GAAE,QAAA,MAAA,UAAA;AAAA,IAAAI,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EACrD,GAAA,SAED,GACA,sBAAA,cAAC,YACC,WAAWL,OAAOiB,cAClB,SAAS,MAAMnB,kBAAkB,UAAU,GAAE,QAAA,MAAA,UAAA;AAAA,IAAAI,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAC9C,EAAA,GAAA,UAED,CACF,CACF,GAGA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAOc,oBAAmB,QAAA,MAAA,UAAA;AAAA,IAAAZ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACvC,MAAG,EAAA,WAAWL,OAAOe,YAAW,QAAA,MAAA,UAAA;AAAA,IAAAb,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,YAAU,GAE3C,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOkB,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAhB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC9B,SAAM,EAAA,WAAWL,OAAOmB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAClC,QAAK,EAAA,WAAWL,OAAOoB,YAAW,QAAA,MAAA,UAAA;AAAA,IAAAlB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,gBAAc,GACjD,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOqB,iBAAgB,QAAA,MAAA,UAAA;AAAA,IAAAnB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACpC,SACC,EAAA,MAAK,YACL,WAAWL,OAAOsB,aAClB,SAAS1E,SAASY,cAClB,UAAW+D,CAAAA,MAAMhC,oBAAoB,gBAAgBgC,EAAEC,OAAOC,OAAO,GAAE,QAAA,MAAA,UAAA;AAAA,IAAAvB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,CACvE,GACD,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAO0B,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAxB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,CAAA,CACxC,CACF,CACF,GAEA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAOkB,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAhB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC9B,SAAM,EAAA,WAAWL,OAAOmB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAClC,QAAK,EAAA,WAAWL,OAAOoB,YAAW,QAAA,MAAA,UAAA;AAAA,IAAAlB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,kBAAgB,GACpD,sBAAA,cAAC,YACC,WAAWL,OAAO2B,aAClB,OAAO/E,SAASgB,UAChB,UAAW2D,CAAAA,MAAMhC,oBAAoB,YAAYgC,EAAEC,OAAO9B,KAAK,GAAE,QAAA,MAAA,UAAA;AAAA,IAAAQ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAEhE,sBAAA,cAAA,UAAA,EAAO,OAAM,SAAO,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,SAAO,GAC7B,sBAAA,cAAC,YAAO,OAAM,UAAQ,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,OAAK,GAC5B,sBAAA,cAAC,YAAO,OAAM,SAAO,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,QAAM,GAC5B,sBAAA,cAAC,YAAO,OAAM,eAAa,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,cAAY,CAC1C,CACF,CACF,GAEC,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOkB,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAhB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC9B,SAAM,EAAA,WAAWL,OAAOmB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAClC,QAAK,EAAA,WAAWL,OAAOoB,YAAW,QAAA,MAAA,UAAA;AAAA,IAAAlB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,qBAAmB,GACtD,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOqB,iBAAgB,QAAA,MAAA,UAAA;AAAA,IAAAnB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACpC,SACC,EAAA,MAAK,YACL,WAAWL,OAAOsB,aAClB,SAAS1E,SAASiB,kBAClB,UAAW0D,CAAAA,MAAMhC,oBAAoB,oBAAoBgC,EAAEC,OAAOC,OAAO,GAAE,QAAA,MAAA,UAAA;AAAA,IAAAvB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,CAC3E,GACD,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAO0B,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAxB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,CAAA,CACxC,CACF,CACF,CACF,GAGC,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOc,oBAAmB,QAAA,MAAA,UAAA;AAAA,IAAAZ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACvC,MAAG,EAAA,WAAWL,OAAOe,YAAW,QAAA,MAAA,UAAA;AAAA,IAAAb,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,cAAY,GAE7C,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOkB,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAhB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC9B,SAAM,EAAA,WAAWL,OAAOmB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAClC,QAAK,EAAA,WAAWL,OAAOoB,YAAW,QAAA,MAAA,UAAA;AAAA,IAAAlB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,mBAAiB,GACpD,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOqB,iBAAgB,QAAA,MAAA,UAAA;AAAA,IAAAnB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACpC,SACC,EAAA,MAAK,YACL,WAAWL,OAAOsB,aAClB,SAAS1E,SAASkB,eAClB,UAAWyD,CAAAA,MAAMhC,oBAAoB,iBAAiBgC,EAAEC,OAAOC,OAAO,GAAE,QAAA,MAAA,UAAA;AAAA,IAAAvB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,CACxE,GACD,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAO0B,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAxB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,CAAA,CACxC,CACF,CACF,CACF,GAGC,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOc,oBAAmB,QAAA,MAAA,UAAA;AAAA,IAAAZ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACvC,MAAG,EAAA,WAAWL,OAAOe,YAAW,QAAA,MAAA,UAAA;AAAA,IAAAb,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,UAAQ,GAEzC,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOkB,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAhB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC9B,SAAM,EAAA,WAAWL,OAAOmB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAClC,QAAK,EAAA,WAAWL,OAAOoB,YAAW,QAAA,MAAA,UAAA;AAAA,IAAAlB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,kBAAgB,GACnD,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOqB,iBAAgB,QAAA,MAAA,UAAA;AAAA,IAAAnB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACpC,SACC,EAAA,MAAK,YACL,WAAWL,OAAOsB,aAClB,SAAS1E,SAAS+B,cAClB,UAAW4C,CAAAA,MAAMhC,oBAAoB,gBAAgBgC,EAAEC,OAAOC,OAAO,GAAE,QAAA,MAAA,UAAA;AAAA,IAAAvB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,CACvE,GACD,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAO0B,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAxB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,CAAA,CACxC,CACF,CACF,GAEA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAOkB,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAhB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC9B,SAAM,EAAA,WAAWL,OAAOmB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAClC,QAAK,EAAA,WAAWL,OAAOoB,YAAW,QAAA,MAAA,UAAA;AAAA,IAAAlB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,eAAa,GAChD,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOqB,iBAAgB,QAAA,MAAA,UAAA;AAAA,IAAAnB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACpC,SACC,EAAA,MAAK,YACL,WAAWL,OAAOsB,aAClB,SAAS1E,SAASgF,cAClB,UAAWL,CAAAA,MAAMhC,oBAAoB,gBAAgBgC,EAAEC,OAAOC,OAAO,GAAE,QAAA,MAAA,UAAA;AAAA,IAAAvB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,CACvE,GACD,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAO0B,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAxB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,CAAA,CACxC,CACF,CACF,GAEA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAOkB,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAhB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC9B,SAAM,EAAA,WAAWL,OAAOmB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAClC,QAAK,EAAA,WAAWL,OAAOoB,YAAW,QAAA,MAAA,UAAA;AAAA,IAAAlB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,oBAAkB,GACrD,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOqB,iBAAgB,QAAA,MAAA,UAAA;AAAA,IAAAnB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACpC,SACC,EAAA,MAAK,YACL,WAAWL,OAAOsB,aAClB,SAAS1E,SAASiF,UAClB,UAAWN,CAAAA,MAAMhC,oBAAoB,YAAYgC,EAAEC,OAAOC,OAAO,GAAE,QAAA,MAAA,UAAA;AAAA,IAAAvB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,CACnE,GACD,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAO0B,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAxB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,CAAA,CACxC,CACF,CACF,CACF,GAGC,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOc,oBAAmB,QAAA,MAAA,UAAA;AAAA,IAAAZ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACvC,MAAG,EAAA,WAAWL,OAAOe,YAAW,QAAA,MAAA,UAAA;AAAA,IAAAb,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,SAAO,GAExC,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOkB,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAhB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC9B,SAAM,EAAA,WAAWL,OAAOmB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAClC,QAAK,EAAA,WAAWL,OAAOoB,YAAW,QAAA,MAAA,UAAA;AAAA,IAAAlB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,kBAAgB,GACpD,sBAAA,cAAC,YACC,WAAWL,OAAO2B,aAClB,OAAO/E,SAASmB,aAChB,UAAWwD,CAAAA,MAAMhC,oBAAoB,eAAegC,EAAEC,OAAO9B,KAAK,GAAE,QAAA,MAAA,UAAA;AAAA,IAAAQ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAEnE,sBAAA,cAAA,UAAA,EAAO,OAAM,WAAS,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,QAAM,GAC9B,sBAAA,cAAC,YAAO,OAAM,QAAM,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,QAAM,GAC3B,sBAAA,cAAC,YAAO,OAAM,QAAM,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,OAAK,GAC1B,sBAAA,cAAC,YAAO,OAAM,iBAAe,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAC,GAAA,gBAAc,CAC9C,CACF,CACF,CACF,GAECrD,qBACC,sBAAA,cAAC,SAAI,WAAWgD,OAAO8B,gBAAe,QAAA,MAAA,UAAA;AAAA,IAAA5B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAC,qCAEvC,CAEJ,CACF,CACF,CACF;AAEJ;"}