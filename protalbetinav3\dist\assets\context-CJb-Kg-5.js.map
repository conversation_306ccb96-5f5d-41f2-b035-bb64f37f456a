{"version": 3, "file": "context-CJb-Kg-5.js", "sources": ["../../src/components/context/SystemContext.jsx", "../../src/components/context/DatabaseProvider.jsx", "../../src/components/context/AccessibilityContext.jsx", "../../src/context/PremiumContext.jsx", "../../src/context/AdminContext.jsx"], "sourcesContent": ["/**\r\n * @file SystemContext.jsx\r\n * @description Provedor de contexto para acesso ao sistema integrado em toda a aplicação\r\n * @version 3.1.0\r\n */\r\n\r\nimport React, { createContext, useContext, useMemo } from 'react';\r\nimport PropTypes from 'prop-types';\r\n\r\n// Criar o contexto com valor inicial null\r\nexport const SystemContext = createContext(null);\r\n\r\n/**\r\n * Hook para acessar o sistema integrado\r\n * @returns {Object} Sistema integrado completo com métodos utilitários\r\n * @throws {Error} Se usado fora de um SystemProvider\r\n */\r\nexport function useSystem() {\r\n  const context = useContext(SystemContext);\r\n  if (!context) {\r\n    throw new Error('useSystem deve ser usado dentro de um SystemProvider');\r\n  }\r\n  return context;\r\n}\r\n\r\n// Alias para compatibilidade\r\nexport const useSystemContext = useSystem;\r\n\r\n/**\r\n * Provedor que disponibiliza o sistema integrado para a árvore de componentes\r\n * @param {Object} props - Propriedades do componente\r\n * @param {Object} props.system - Sistema integrado inicializado\r\n * @param {React.ReactNode} props.children - Componentes filhos\r\n * @returns {JSX.Element}\r\n */\r\nexport function SystemProvider({ system, children }) {\r\n  // Validar sistema na inicialização\r\n  if (!system || typeof system !== 'object') {\r\n    throw new Error('SystemProvider requer um objeto system válido');\r\n  }\r\n\r\n  // Memoizar o valor do contexto para evitar renderizações desnecessárias\r\n  const systemValue = useMemo(() => ({\r\n    system,\r\n    // Métodos utilitários com binding seguro\r\n    healthCheck: (...args) => system?.healthCheck?.(...args),\r\n    getStatistics: (...args) => system?.getStatistics?.(...args),\r\n    dispatchEvent: (...args) => system?.dispatchEvent?.(...args),\r\n    trackUserInteraction: (...args) => system?.trackUserInteraction?.(...args),\r\n    \r\n    // Informações de status do sistema com fallback seguro\r\n    getStatus: () => ({\r\n      database: system?.databaseInstance?.getStatus?.() ?? { status: 'unavailable' },\r\n      resilience: system?.resilience?.getCircuitBreakersStatus?.() ?? { status: 'unavailable' },\r\n      timestamp: new Date().toISOString(),\r\n      version: '3.1.0'\r\n    })\r\n  }), [system]);\r\n\r\n  return (\r\n    <SystemContext.Provider value={systemValue}>\r\n      {children}\r\n    </SystemContext.Provider>\r\n  );\r\n}\r\n\r\nSystemProvider.propTypes = {\r\n  system: PropTypes.shape({\r\n    healthCheck: PropTypes.func,\r\n    getStatistics: PropTypes.func,\r\n    dispatchEvent: PropTypes.func,\r\n    trackUserInteraction: PropTypes.func,\r\n    databaseInstance: PropTypes.shape({\r\n      getStatus: PropTypes.func\r\n    }),\r\n    resilience: PropTypes.shape({\r\n      getCircuitBreakersStatus: PropTypes.func\r\n    })\r\n  }).isRequired,\r\n  children: PropTypes.node.isRequired\r\n};\r\n\r\n// Exportação padrão\r\nexport default SystemProvider;", "/**\r\n * @file DatabaseProvider.jsx\r\n * @description Provedor de contexto para acesso ao DatabaseIntegrator em toda a aplicação\r\n * @version 3.0.0\r\n */\r\n\r\nimport React from 'react';\r\nimport PropTypes from 'prop-types';\r\nimport { useSystem } from './SystemContext';\r\n\r\n// Criar o contexto\r\nexport const DatabaseContext = React.createContext(null);\r\n\r\n/**\r\n * Hook para acessar o DatabaseIntegrator em qualquer componente\r\n * @returns {Object} DatabaseIntegrator instance\r\n */\r\nexport function useDatabase() {\r\n  const context = React.useContext(DatabaseContext);\r\n  if (!context) {\r\n    throw new Error('useDatabase deve ser usado dentro de um DatabaseProvider');\r\n  }\r\n  return context;\r\n}\r\n\r\n/**\r\n * Alias para useDatabase - mantém compatibilidade\r\n * @returns {Object} DatabaseIntegrator instance \r\n */\r\nexport const useDatabaseContext = useDatabase;\r\n\r\n/**\r\n * Provedor que disponibiliza o DatabaseIntegrator para a árvore de componentes\r\n * @param {Object} props - Propriedades do componente\r\n * @returns {JSX.Element}\r\n */\r\nexport function DatabaseProvider({ children }) {\r\n  // Obter o database diretamente do sistema integrado\r\n  const { system } = useSystem();\r\n  const databaseInstance = system.databaseInstance;\r\n  // O valor memoizado evita renderizações desnecessárias\r\n  const databaseValue = React.useMemo(() => {\r\n    // Verificar se os métodos existem antes de fazer bind\r\n    const safeBind = (method) => method ? method.bind(databaseInstance) : () => console.warn('Method not available');\r\n    \r\n    return {\r\n      // Expõe apenas o que componentes precisam acessar\r\n      db: databaseInstance,\r\n      databaseService: databaseInstance?.manager || databaseInstance,\r\n      saveGameMetrics: safeBind(databaseInstance.saveGameMetrics),\r\n      getUserData: safeBind(databaseInstance.getUserData),\r\n      saveUserData: safeBind(databaseInstance.saveUserData),\r\n      getStatus: safeBind(databaseInstance.getStatus),\r\n      \r\n      // Acesso ao sistema de resiliência para casos especiais\r\n      resilience: system.resilience\r\n    };\r\n  }, [databaseInstance, system.resilience]);\r\n\r\n  return (\r\n    <DatabaseContext.Provider value={databaseValue}>\r\n      {children}\r\n    </DatabaseContext.Provider>\r\n  );\r\n}\r\n\r\nDatabaseProvider.propTypes = {\r\n  children: PropTypes.node.isRequired\r\n};\r\n\r\nexport default DatabaseProvider;\r\n", "/**\r\n * @file AccessibilityContext.jsx\r\n * @description Contexto para gerenciar configurações de acessibilidade em toda a aplicação\r\n * @version 3.0.0\r\n */\r\n\r\nimport React from 'react'\r\nimport PropTypes from 'prop-types'\r\nimport useAccessibility from '../../hooks/useAccessibility'\r\n\r\n// Criando o contexto\r\nconst AccessibilityContext = React.createContext({\r\n  settings: {\r\n    textToSpeech: true,\r\n    highContrast: false,\r\n    reducedMotion: false,\r\n    colorScheme: 'default',\r\n    dyslexiaFriendly: false,\r\n    fontSize: 'medium',\r\n    soundEnabled: true,\r\n    autoRead: false\r\n  },\r\n  updateSettings: () => {},\r\n  applyPreset: () => {}\r\n})\r\n\r\n/**\r\n * Provider para o contexto de acessibilidade\r\n */\r\nexport function AccessibilityProvider({ children }) {\r\n  const accessibilityManager = useAccessibility()\r\n\r\n  return (\r\n    <AccessibilityContext.Provider value={accessibilityManager}>\r\n      {children}\r\n    </AccessibilityContext.Provider>\r\n  )\r\n}\r\n\r\n/**\r\n * Hook para usar o contexto de acessibilidade\r\n */\r\nexport function useAccessibilityContext() {\r\n  return React.useContext(AccessibilityContext)\r\n}\r\n\r\nAccessibilityProvider.propTypes = {\r\n  children: PropTypes.node.isRequired\r\n}\r\n\r\nexport default AccessibilityContext\r\n", "/**\r\n * @file PremiumContext.jsx\r\n * @description Contexto para controle de acesso premium do Portal Betina V3\r\n * @version 3.0.0\r\n */\r\n\r\nimport React from 'react';\r\n\r\nconst PremiumContext = React.createContext();\r\n\r\n// Hook para usar o contexto premium\r\nexport const usePremium = () => {\r\n  const context = React.useContext(PremiumContext);\r\n  if (!context) {\r\n    throw new Error('usePremium deve ser usado dentro de PremiumProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\nexport const PremiumProvider = ({ children }) => {  const [user, setUser] = React.useState({\r\n    id: 'demo_user',\r\n    name: '<PERSON>u<PERSON><PERSON> De<PERSON>',\r\n    email: '<EMAIL>',\r\n    isPremium: false, // Por padrão, usuário não é premium\r\n    isLoggedIn: false, // Por padrão, usuário não está logado\r\n    subscription: 'free',\r\n    permissions: []\r\n  });\r\n\r\n  const [premiumFeatures] = React.useState({\r\n    dashboard: true,           // Dashboard do sistema\r\n    analytics: true,          // Métricas e análises\r\n    backupFull: true,         // Backup completo\r\n    performance: true,        // Análise de performance\r\n    exportData: true,         // Exportação de dados\r\n    advancedReports: true,    // Relatórios avançados\r\n    multiUser: true,          // Múltiplos usuários\r\n    cloudSync: true           // Sincronização na nuvem\r\n  });\r\n\r\n  // Simular login (em produção, viria de uma API)\r\n  const login = async (email, password, isPremium = false) => {\r\n    try {\r\n      // Simulação de autenticação\r\n      const userData = {\r\n        id: Date.now(),\r\n        name: email.split('@')[0],\r\n        email,\r\n        isPremium,\r\n        isLoggedIn: true,\r\n        subscription: isPremium ? 'premium' : 'free',\r\n        permissions: isPremium ? Object.keys(premiumFeatures) : []\r\n      };\r\n\r\n      setUser(userData);\r\n      localStorage.setItem('betina_user', JSON.stringify(userData));\r\n      return { success: true, user: userData };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  };\r\n\r\n  // Logout\r\n  const logout = () => {\r\n    setUser({\r\n      id: null,\r\n      name: '',\r\n      email: '',\r\n      isPremium: false,\r\n      isLoggedIn: false,\r\n      subscription: null,\r\n      permissions: []\r\n    });\r\n    localStorage.removeItem('betina_user');\r\n  };\r\n\r\n  // Verificar se o usuário tem acesso a uma funcionalidade premium\r\n  const hasAccess = (feature) => {\r\n    if (!premiumFeatures[feature]) return true; // Feature não é premium\r\n    return user.isPremium && user.permissions.includes(feature);\r\n  };\r\n\r\n  // Verificar se o usuário está logado\r\n  const isLoggedIn = () => user.isLoggedIn;\r\n  // Verificar se o usuário é premium\r\n  const isPremium = () => user.isPremium;\r\n\r\n  // Verificar se pode acessar dashboard\r\n  const canAccessDashboard = () => {\r\n    return user.isPremium && user.permissions.includes('dashboard');\r\n  };\r\n\r\n  // Verificar se pode acessar relatórios IA\r\n  const canAccessAIReports = () => {\r\n    return user.isPremium && user.permissions.includes('ai_reports');\r\n  };\r\n\r\n  // Verificar se pode acessar métricas avançadas\r\n  const canAccessAdvancedMetrics = () => {\r\n    return user.isPremium && user.permissions.includes('advanced_metrics');\r\n  };\r\n\r\n  // Carregar dados do usuário do localStorage na inicialização\r\n  React.useEffect(() => {\r\n    const savedUser = localStorage.getItem('betina_user');\r\n    if (savedUser) {\r\n      try {\r\n        const userData = JSON.parse(savedUser);\r\n        setUser(userData);\r\n      } catch (error) {\r\n        console.error('Erro ao carregar dados do usuário:', error);\r\n        localStorage.removeItem('betina_user');\r\n      }\r\n    }\r\n  }, []);\r\n  const value = {\r\n    user,\r\n    login,\r\n    logout,\r\n    hasAccess,\r\n    isLoggedIn,\r\n    isPremium,\r\n    canAccessDashboard,\r\n    canAccessAIReports,\r\n    canAccessAdvancedMetrics,\r\n    premiumFeatures\r\n  };\r\n\r\n  return (\r\n    <PremiumContext.Provider value={value}>\r\n      {children}\r\n    </PremiumContext.Provider>\r\n  );\r\n};\r\n\r\nexport default PremiumContext;\r\n", "/**\r\n * @file AdminContext.jsx\r\n * @description Contexto para gerenciar permissões administrativas\r\n * @version 3.0.0\r\n */\r\n\r\nimport React from 'react'\r\nimport PropTypes from 'prop-types'\r\n\r\nconst AdminContext = React.createContext()\r\n\r\nexport const useAdmin = () => {\r\n  const context = React.useContext(AdminContext)\r\n  if (!context) {\r\n    throw new Error('useAdmin deve ser usado dentro de um AdminProvider')\r\n  }\r\n  return context\r\n}\r\n\r\nexport const AdminProvider = ({ children }) => {\r\n  const [isAdmin, setIsAdmin] = React.useState(false)\r\n  const [adminSession, setAdminSession] = React.useState(null)\r\n\r\n  // Verificar se há uma sessão admin ativa ao carregar\r\n  React.useEffect(() => {\r\n    const savedSession = localStorage.getItem('betina_admin_session')\r\n    if (savedSession) {\r\n      try {\r\n        const session = JSON.parse(savedSession)\r\n        // Verificar se a sessão não expirou (24 horas)\r\n        const now = new Date().getTime()\r\n        const sessionTime = new Date(session.timestamp).getTime()\r\n        const hoursDiff = (now - sessionTime) / (1000 * 60 * 60)\r\n        \r\n        if (hoursDiff < 24) {\r\n          setIsAdmin(true)\r\n          setAdminSession(session)\r\n        } else {\r\n          // Sessão expirada, remover\r\n          localStorage.removeItem('betina_admin_session')\r\n        }\r\n      } catch (error) {\r\n        console.error('Erro ao verificar sessão admin:', error)\r\n        localStorage.removeItem('betina_admin_session')\r\n      }\r\n    }\r\n  }, [])\r\n\r\n  const loginAdmin = (credentials) => {\r\n    // Verificar credenciais (em produção, isso seria uma API)\r\n    if (credentials.username === 'admin' && credentials.password === 'betina2024admin') {\r\n      const session = {\r\n        user: 'admin',\r\n        timestamp: new Date().toISOString(),\r\n        permissions: ['dashboard_integrated', 'system_admin', 'user_management']\r\n      }\r\n      \r\n      setIsAdmin(true)\r\n      setAdminSession(session)\r\n      localStorage.setItem('betina_admin_session', JSON.stringify(session))\r\n      \r\n      return { success: true, message: 'Login administrativo realizado com sucesso!' }\r\n    }\r\n    \r\n    return { success: false, message: 'Credenciais administrativas inválidas' }\r\n  }\r\n\r\n  const logoutAdmin = () => {\r\n    setIsAdmin(false)\r\n    setAdminSession(null)\r\n    localStorage.removeItem('betina_admin_session')\r\n  }\r\n\r\n  const hasPermission = (permission) => {\r\n    if (!isAdmin || !adminSession) return false\r\n    return adminSession.permissions && adminSession.permissions.includes(permission)\r\n  }\r\n\r\n  const canAccessIntegratedDashboard = () => {\r\n    return hasPermission('dashboard_integrated')\r\n  }\r\n\r\n  const value = {\r\n    isAdmin,\r\n    adminSession,\r\n    loginAdmin,\r\n    logoutAdmin,\r\n    hasPermission,\r\n    canAccessIntegratedDashboard\r\n  }\r\n\r\n  return (\r\n    <AdminContext.Provider value={value}>\r\n      {children}\r\n    </AdminContext.Provider>\r\n  )\r\n}\r\n\r\nAdminProvider.propTypes = {\r\n  children: PropTypes.node.isRequired\r\n}\r\n"], "names": ["createContext", "useContext", "useMemo", "isPremium", "jsxDEV", "this"], "mappings": ";;;AAUa,MAAA,gBAAgBA,2BAAc,IAAI;AAOxC,SAAS,YAAY;AACpB,QAAA,UAAUC,wBAAW,aAAa;AACxC,MAAI,CAAC,SAAS;AACN,UAAA,IAAI,MAAM,sDAAsD;AAAA,EAAA;AAEjE,SAAA;AACT;AAYO,SAAS,eAAe,EAAE,QAAQ,YAAY;AAEnD,MAAI,CAAC,UAAU,OAAO,WAAW,UAAU;AACnC,UAAA,IAAI,MAAM,+CAA+C;AAAA,EAAA;AAI3D,QAAA,cAAcC,aAAAA,QAAQ,OAAO;AAAA,IACjC;AAAA;AAAA,IAEA,aAAa,IAAI,SAAS,QAAQ,cAAc,GAAG,IAAI;AAAA,IACvD,eAAe,IAAI,SAAS,QAAQ,gBAAgB,GAAG,IAAI;AAAA,IAC3D,eAAe,IAAI,SAAS,QAAQ,gBAAgB,GAAG,IAAI;AAAA,IAC3D,sBAAsB,IAAI,SAAS,QAAQ,uBAAuB,GAAG,IAAI;AAAA;AAAA,IAGzE,WAAW,OAAO;AAAA,MAChB,UAAU,QAAQ,kBAAkB,iBAAiB,EAAE,QAAQ,cAAc;AAAA,MAC7E,YAAY,QAAQ,YAAY,gCAAgC,EAAE,QAAQ,cAAc;AAAA,MACxF,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,SAAS;AAAA,IACX;AAAA,EAAA,IACE,CAAC,MAAM,CAAC;AAEZ,qDACG,cAAc,UAAd,EAAuB,OAAO,aAC5B,SADH,GAAA,QAAA,OAAA;AAAA,IAAA,UAAA;AAAA,IAAA,YAAA;AAAA,IAAA,cAAA;AAAA,EAEA,GAAA,IAAA;AAEJ;AAEA,eAAe,YAAY;AAAA,EACzB,QAAQ,UAAU,MAAM;AAAA,IACtB,aAAa,UAAU;AAAA,IACvB,eAAe,UAAU;AAAA,IACzB,eAAe,UAAU;AAAA,IACzB,sBAAsB,UAAU;AAAA,IAChC,kBAAkB,UAAU,MAAM;AAAA,MAChC,WAAW,UAAU;AAAA,IAAA,CACtB;AAAA,IACD,YAAY,UAAU,MAAM;AAAA,MAC1B,0BAA0B,UAAU;AAAA,IACrC,CAAA;AAAA,EACF,CAAA,EAAE;AAAA,EACH,UAAU,UAAU,KAAK;AAC3B;ACrEa,MAAA,kBAAkB,MAAM,cAAc,IAAI;AAyBvC,SAAA,iBAAiB,EAAE,YAAY;AAEvC,QAAA,EAAE,OAAO,IAAI,UAAU;AAC7B,QAAM,mBAAmB,OAAO;AAE1B,QAAA,gBAAgB,MAAM,QAAQ,MAAM;AAElC,UAAA,WAAW,CAAC,WAAW,SAAS,OAAO,KAAK,gBAAgB,IAAI,MAAM,QAAQ,KAAK,sBAAsB;AAExG,WAAA;AAAA;AAAA,MAEL,IAAI;AAAA,MACJ,iBAAiB,kBAAkB,WAAW;AAAA,MAC9C,iBAAiB,SAAS,iBAAiB,eAAe;AAAA,MAC1D,aAAa,SAAS,iBAAiB,WAAW;AAAA,MAClD,cAAc,SAAS,iBAAiB,YAAY;AAAA,MACpD,WAAW,SAAS,iBAAiB,SAAS;AAAA;AAAA,MAG9C,YAAY,OAAO;AAAA,IACrB;AAAA,EACC,GAAA,CAAC,kBAAkB,OAAO,UAAU,CAAC;AAExC,qDACG,gBAAgB,UAAhB,EAAyB,OAAO,eAC9B,SADH,GAAA,QAAA,OAAA;AAAA,IAAA,UAAA;AAAA,IAAA,YAAA;AAAA,IAAA,cAAA;AAAA,EAEA,GAAA,IAAA;AAEJ;AAEA,iBAAiB,YAAY;AAAA,EAC3B,UAAU,UAAU,KAAK;AAC3B;ACzDA,MAAM,uBAAuB,MAAM,cAAc;AAAA,EAC/C,UAAU;AAAA,IACR,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe;AAAA,IACf,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,cAAc;AAAA,IACd,UAAU;AAAA,EACZ;AAAA,EACA,gBAAgB,MAAM;AAAA,EAAC;AAAA,EACvB,aAAa,MAAM;AAAA,EAAA;AACrB,CAAC;AAKe,SAAA,sBAAsB,EAAE,YAAY;AAClD,QAAM,uBAAuB,iBAAiB;AAE9C,qDACG,qBAAqB,UAArB,EAA8B,OAAO,sBACnC,SADH,GAAA,QAAA,OAAA;AAAA,IAAA,UAAA;AAAA,IAAA,YAAA;AAAA,IAAA,cAAA;AAAA,EAEA,GAAA,IAAA;AAEJ;AAKO,SAAS,0BAA0B;AACjC,SAAA,MAAM,WAAW,oBAAoB;AAC9C;AAEA,sBAAsB,YAAY;AAAA,EAChC,UAAU,UAAU,KAAK;AAC3B;ACxCA,MAAM,iBAAiB,MAAM,cAAc;AAGpC,MAAM,aAAa,MAAM;AACxB,QAAA,UAAU,MAAM,WAAW,cAAc;AAC/C,MAAI,CAAC,SAAS;AACN,UAAA,IAAI,MAAM,qDAAqD;AAAA,EAAA;AAEhE,SAAA;AACT;AAEO,MAAM,kBAAkB,CAAC,EAAE,eAAe;AAAG,QAAM,CAAC,MAAM,OAAO,IAAI,MAAM,SAAS;AAAA,IACvF,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,OAAO;AAAA,IACP,WAAW;AAAA;AAAA,IACX,YAAY;AAAA;AAAA,IACZ,cAAc;AAAA,IACd,aAAa,CAAA;AAAA,EAAC,CACf;AAED,QAAM,CAAC,eAAe,IAAI,MAAM,SAAS;AAAA,IACvC,WAAW;AAAA;AAAA,IACX,WAAW;AAAA;AAAA,IACX,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,YAAY;AAAA;AAAA,IACZ,iBAAiB;AAAA;AAAA,IACjB,WAAW;AAAA;AAAA,IACX,WAAW;AAAA;AAAA,EAAA,CACZ;AAGD,QAAM,QAAQ,OAAO,OAAO,UAAUC,aAAY,UAAU;AACtD,QAAA;AAEF,YAAM,WAAW;AAAA,QACf,IAAI,KAAK,IAAI;AAAA,QACb,MAAM,MAAM,MAAM,GAAG,EAAE,CAAC;AAAA,QACxB;AAAA,QACA,WAAAA;AAAAA,QACA,YAAY;AAAA,QACZ,cAAcA,aAAY,YAAY;AAAA,QACtC,aAAaA,aAAY,OAAO,KAAK,eAAe,IAAI,CAAA;AAAA,MAC1D;AAEA,cAAQ,QAAQ;AAChB,mBAAa,QAAQ,eAAe,KAAK,UAAU,QAAQ,CAAC;AAC5D,aAAO,EAAE,SAAS,MAAM,MAAM,SAAS;AAAA,aAChC,OAAO;AACd,aAAO,EAAE,SAAS,OAAO,OAAO,MAAM,QAAQ;AAAA,IAAA;AAAA,EAElD;AAGA,QAAM,SAAS,MAAM;AACX,YAAA;AAAA,MACN,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,aAAa,CAAA;AAAA,IAAC,CACf;AACD,iBAAa,WAAW,aAAa;AAAA,EACvC;AAGM,QAAA,YAAY,CAAC,YAAY;AAC7B,QAAI,CAAC,gBAAgB,OAAO,EAAU,QAAA;AACtC,WAAO,KAAK,aAAa,KAAK,YAAY,SAAS,OAAO;AAAA,EAC5D;AAGM,QAAA,aAAa,MAAM,KAAK;AAExB,QAAA,YAAY,MAAM,KAAK;AAG7B,QAAM,qBAAqB,MAAM;AAC/B,WAAO,KAAK,aAAa,KAAK,YAAY,SAAS,WAAW;AAAA,EAChE;AAGA,QAAM,qBAAqB,MAAM;AAC/B,WAAO,KAAK,aAAa,KAAK,YAAY,SAAS,YAAY;AAAA,EACjE;AAGA,QAAM,2BAA2B,MAAM;AACrC,WAAO,KAAK,aAAa,KAAK,YAAY,SAAS,kBAAkB;AAAA,EACvE;AAGA,QAAM,UAAU,MAAM;AACd,UAAA,YAAY,aAAa,QAAQ,aAAa;AACpD,QAAI,WAAW;AACT,UAAA;AACI,cAAA,WAAW,KAAK,MAAM,SAAS;AACrC,gBAAQ,QAAQ;AAAA,eACT,OAAO;AACN,gBAAA,MAAM,sCAAsC,KAAK;AACzD,qBAAa,WAAW,aAAa;AAAA,MAAA;AAAA,IACvC;AAAA,EAEJ,GAAG,EAAE;AACL,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,SACGC,qCAAA,OAAA,eAAe,UAAf,EAAwB,OACtB,SADH,GAAA,QAAA,OAAA;AAAA,IAAA,UAAA;AAAA,IAAA,YAAA;AAAA,IAAA,cAAA;AAAA,EAEA,GAAAC,MAAA;AAEJ;AC5HA,MAAM,eAAe,MAAM,cAAc;AAElC,MAAM,WAAW,MAAM;AACtB,QAAA,UAAU,MAAM,WAAW,YAAY;AAC7C,MAAI,CAAC,SAAS;AACN,UAAA,IAAI,MAAM,oDAAoD;AAAA,EAAA;AAE/D,SAAA;AACT;AAEO,MAAM,gBAAgB,CAAC,EAAE,eAAe;AAC7C,QAAM,CAAC,SAAS,UAAU,IAAI,MAAM,SAAS,KAAK;AAClD,QAAM,CAAC,cAAc,eAAe,IAAI,MAAM,SAAS,IAAI;AAG3D,QAAM,UAAU,MAAM;AACd,UAAA,eAAe,aAAa,QAAQ,sBAAsB;AAChE,QAAI,cAAc;AACZ,UAAA;AACI,cAAA,UAAU,KAAK,MAAM,YAAY;AAEvC,cAAM,OAAM,oBAAI,KAAK,GAAE,QAAQ;AAC/B,cAAM,cAAc,IAAI,KAAK,QAAQ,SAAS,EAAE,QAAQ;AACxD,cAAM,aAAa,MAAM,gBAAgB,MAAO,KAAK;AAErD,YAAI,YAAY,IAAI;AAClB,qBAAW,IAAI;AACf,0BAAgB,OAAO;AAAA,QAAA,OAClB;AAEL,uBAAa,WAAW,sBAAsB;AAAA,QAAA;AAAA,eAEzC,OAAO;AACN,gBAAA,MAAM,mCAAmC,KAAK;AACtD,qBAAa,WAAW,sBAAsB;AAAA,MAAA;AAAA,IAChD;AAAA,EAEJ,GAAG,EAAE;AAEC,QAAA,aAAa,CAAC,gBAAgB;AAElC,QAAI,YAAY,aAAa,WAAW,YAAY,aAAa,mBAAmB;AAClF,YAAM,UAAU;AAAA,QACd,MAAM;AAAA,QACN,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAClC,aAAa,CAAC,wBAAwB,gBAAgB,iBAAiB;AAAA,MACzE;AAEA,iBAAW,IAAI;AACf,sBAAgB,OAAO;AACvB,mBAAa,QAAQ,wBAAwB,KAAK,UAAU,OAAO,CAAC;AAEpE,aAAO,EAAE,SAAS,MAAM,SAAS,8CAA8C;AAAA,IAAA;AAGjF,WAAO,EAAE,SAAS,OAAO,SAAS,wCAAwC;AAAA,EAC5E;AAEA,QAAM,cAAc,MAAM;AACxB,eAAW,KAAK;AAChB,oBAAgB,IAAI;AACpB,iBAAa,WAAW,sBAAsB;AAAA,EAChD;AAEM,QAAA,gBAAgB,CAAC,eAAe;AACpC,QAAI,CAAC,WAAW,CAAC,aAAqB,QAAA;AACtC,WAAO,aAAa,eAAe,aAAa,YAAY,SAAS,UAAU;AAAA,EACjF;AAEA,QAAM,+BAA+B,MAAM;AACzC,WAAO,cAAc,sBAAsB;AAAA,EAC7C;AAEA,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,SACGD,qCAAA,OAAA,aAAa,UAAb,EAAsB,OACpB,SADH,GAAA,QAAA,OAAA;AAAA,IAAA,UAAA;AAAA,IAAA,YAAA;AAAA,IAAA,cAAA;AAAA,EAEA,GAAAC,MAAA;AAEJ;AAEA,cAAc,YAAY;AAAA,EACxB,UAAU,UAAU,KAAK;AAC3B;"}