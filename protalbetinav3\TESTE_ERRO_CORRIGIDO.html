<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Teste de Correção de Erros - Portal Betina V3</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            font-size: 2.5em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #4CAF50;
        }
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: rgba(76, 175, 80, 0.3); border-left: 4px solid #4CAF50; }
        .error { background: rgba(244, 67, 54, 0.3); border-left: 4px solid #f44336; }
        .warning { background: rgba(255, 193, 7, 0.3); border-left: 4px solid #FFC107; }
        .info { background: rgba(33, 150, 243, 0.3); border-left: 4px solid #2196F3; }
        .results {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .emoji { font-size: 1.2em; margin-right: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Teste de Correção de Erros</h1>
            <p>Portal Betina V3 - Verificação de Funcionamento</p>
        </div>

        <div class="test-section">
            <h2><span class="emoji">🌐</span>Teste 1: Conectividade dos Serviços</h2>
            <p>Verifica se todos os serviços estão respondendo corretamente.</p>
            <button class="test-button" onclick="testarConectividade()">🔍 Testar Conectividade</button>
            <div id="conectividade-result" class="results"></div>
        </div>

        <div class="test-section">
            <h2><span class="emoji">🎮</span>Teste 2: Frontend (Erro JavaScript)</h2>
            <p>Verifica se o erro "Cannot access 'e' before initialization" foi corrigido.</p>
            <button class="test-button" onclick="testarFrontend()">🎯 Testar Frontend</button>
            <div id="frontend-result" class="results"></div>
        </div>

        <div class="test-section">
            <h2><span class="emoji">🎨</span>Teste 3: ColorMatch Game</h2>
            <p>Testa especificamente o jogo ColorMatch que estava com erro.</p>
            <button class="test-button" onclick="testarColorMatch()">🎨 Testar ColorMatch</button>
            <div id="colormatch-result" class="results"></div>
        </div>

        <div class="test-section">
            <h2><span class="emoji">🗄️</span>Teste 4: PostgreSQL + DatabaseIntegrator</h2>
            <p>Verifica se o PostgreSQL real está funcionando.</p>
            <button class="test-button" onclick="testarDatabase()">💾 Testar Database</button>
            <div id="database-result" class="results"></div>
        </div>

        <div class="test-section">
            <h2><span class="emoji">🚀</span>Teste Completo</h2>
            <p>Executa todos os testes em sequência.</p>
            <button class="test-button" onclick="executarTodosOsTestes()">🎯 Executar Todos os Testes</button>
            <div id="completo-result" class="results"></div>
        </div>
    </div>

    <script>
        function log(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            const typeClass = type;
            const emoji = {
                'success': '✅',
                'error': '❌',
                'warning': '⚠️',
                'info': 'ℹ️'
            }[type] || 'ℹ️';
            
            container.innerHTML += `<div class="status ${typeClass}">${emoji} [${timestamp}] ${message}</div>`;
            container.scrollTop = container.scrollHeight;
        }

        function clearLog(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        async function testarConectividade() {
            clearLog('conectividade-result');
            log('conectividade-result', 'Iniciando teste de conectividade...', 'info');
            
            // Teste Frontend
            try {
                const frontendResponse = await fetch('http://localhost:5173');
                if (frontendResponse.ok) {
                    log('conectividade-result', 'Frontend (5173): ✅ FUNCIONANDO', 'success');
                } else {
                    log('conectividade-result', `Frontend (5173): ❌ Status ${frontendResponse.status}`, 'error');
                }
            } catch (error) {
                log('conectividade-result', `Frontend (5173): ❌ ${error.message}`, 'error');
            }

            // Teste API
            try {
                const apiResponse = await fetch('http://localhost:3000/api/public/debug/system-status', {
                    headers: { 'Authorization': 'Bearer test-token' }
                });
                if (apiResponse.ok) {
                    const data = await apiResponse.json();
                    log('conectividade-result', 'API (3000): ✅ FUNCIONANDO', 'success');
                    log('conectividade-result', `SystemOrchestrator: ${data.systemOrchestratorAvailable ? '✅' : '❌'}`, data.systemOrchestratorAvailable ? 'success' : 'error');
                } else {
                    log('conectividade-result', `API (3000): ❌ Status ${apiResponse.status}`, 'error');
                }
            } catch (error) {
                log('conectividade-result', `API (3000): ❌ ${error.message}`, 'error');
            }
        }

        async function testarFrontend() {
            clearLog('frontend-result');
            log('frontend-result', 'Testando se o erro JavaScript foi corrigido...', 'info');
            
            try {
                // Simular carregamento do frontend
                const iframe = document.createElement('iframe');
                iframe.src = 'http://localhost:5173';
                iframe.style.display = 'none';
                document.body.appendChild(iframe);
                
                // Aguardar carregamento
                await new Promise((resolve, reject) => {
                    iframe.onload = () => {
                        log('frontend-result', '✅ Frontend carregou sem erros JavaScript críticos', 'success');
                        log('frontend-result', '✅ Erro "Cannot access \'e\' before initialization" parece corrigido', 'success');
                        document.body.removeChild(iframe);
                        resolve();
                    };
                    iframe.onerror = () => {
                        log('frontend-result', '❌ Erro ao carregar frontend', 'error');
                        document.body.removeChild(iframe);
                        reject();
                    };
                    
                    // Timeout
                    setTimeout(() => {
                        log('frontend-result', '⚠️ Timeout no carregamento do frontend', 'warning');
                        document.body.removeChild(iframe);
                        resolve();
                    }, 10000);
                });
                
            } catch (error) {
                log('frontend-result', `❌ Erro no teste: ${error.message}`, 'error');
            }
        }

        async function testarColorMatch() {
            clearLog('colormatch-result');
            log('colormatch-result', 'Testando jogo ColorMatch especificamente...', 'info');
            
            try {
                // Abrir ColorMatch em nova janela para teste
                const colorMatchWindow = window.open('http://localhost:5173', '_blank', 'width=800,height=600');
                
                if (colorMatchWindow) {
                    log('colormatch-result', '✅ ColorMatch aberto em nova janela', 'success');
                    log('colormatch-result', '🎯 Verifique se o jogo carrega sem erros no console', 'info');
                    log('colormatch-result', '🎨 Teste clicando em algumas cores para verificar coletores', 'info');
                    
                    // Fechar após 30 segundos
                    setTimeout(() => {
                        if (!colorMatchWindow.closed) {
                            colorMatchWindow.close();
                            log('colormatch-result', '🔄 Janela de teste fechada automaticamente', 'info');
                        }
                    }, 30000);
                } else {
                    log('colormatch-result', '❌ Não foi possível abrir janela de teste', 'error');
                }
                
            } catch (error) {
                log('colormatch-result', `❌ Erro no teste ColorMatch: ${error.message}`, 'error');
            }
        }

        async function testarDatabase() {
            clearLog('database-result');
            log('database-result', 'Testando PostgreSQL e DatabaseIntegrator...', 'info');
            
            try {
                const response = await fetch('http://localhost:3000/api/public/debug/database', {
                    headers: { 'Authorization': 'Bearer test-token' }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log('database-result', `API Database: ${data.success ? '✅' : '❌'}`, data.success ? 'success' : 'error');
                    log('database-result', `PostgreSQL Conectado: ${data.connected ? '✅' : '❌'}`, data.connected ? 'success' : 'warning');
                    log('database-result', `Tabelas Existem: ${data.tablesExist ? '✅' : '❌'}`, data.tablesExist ? 'success' : 'warning');
                    log('database-result', `Sessões no DB: ${data.sessionCount || 0}`, 'info');
                    
                    if (data.error) {
                        log('database-result', `⚠️ Aviso: ${data.error}`, 'warning');
                    }
                } else {
                    log('database-result', `❌ Erro na API: Status ${response.status}`, 'error');
                }
                
            } catch (error) {
                log('database-result', `❌ Erro no teste database: ${error.message}`, 'error');
            }
        }

        async function executarTodosOsTestes() {
            clearLog('completo-result');
            log('completo-result', '🚀 Iniciando bateria completa de testes...', 'info');
            
            log('completo-result', '1️⃣ Testando conectividade...', 'info');
            await testarConectividade();
            
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            log('completo-result', '2️⃣ Testando frontend...', 'info');
            await testarFrontend();
            
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            log('completo-result', '3️⃣ Testando ColorMatch...', 'info');
            await testarColorMatch();
            
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            log('completo-result', '4️⃣ Testando database...', 'info');
            await testarDatabase();
            
            log('completo-result', '🎉 Todos os testes concluídos!', 'success');
            log('completo-result', '📊 Verifique os resultados individuais acima', 'info');
        }

        // Auto-executar teste básico ao carregar
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('conectividade-result', '🔄 Executando teste automático de conectividade...', 'info');
                testarConectividade();
            }, 1000);
        });
    </script>
</body>
</html>
