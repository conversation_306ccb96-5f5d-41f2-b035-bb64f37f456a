/**
 * @file LetterRecognition.module.css
 * @description Estilos modulares para o Jogo de Reconhecimento de Letras - BASEADO NO CONTAGEM NÚMEROS
 * @version 7.0.0 - Layout copiado do ContagemNumeros que funciona perfeitamente
 */

/* Variáveis CSS para consistência e reutilização - EXATO DO CONTAGEM NÚMEROS */
:root {
  --card-background: rgba(255, 255, 255, 0.1);
  --card-border: 1px solid rgba(255, 255, 255, 0.2);
  --card-blur: blur(10px);
  --success-bg: rgba(76, 175, 80, 0.3);
  --success-border: rgba(76, 175, 80, 0.5);
  --error-bg: rgba(244, 67, 54, 0.3);
  --error-border: rgba(244, 67, 54, 0.5);
  --primary-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --game-background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-color: #667eea;
  --primary-hover: #5a6fd8;
}

/* Animações - EXATO DO MEMORYGAME */
@keyframes _buttonPressEffect_7z27o_1 {
  0% { transform: scale(1); }
  50% { transform: scale(1.15); box-shadow: 0 10px 30px rgba(255, 255, 255, 0.4); }
  100% { transform: scale(1); }
}

@keyframes _correctFeedback_7z27o_1 {
  0% { box-shadow: 0 0 0 rgba(76, 175, 80, 0.4); }
  50% { box-shadow: 0 0 30px rgba(76, 175, 80, 0.8); }
  100% { box-shadow: 0 0 0 rgba(76, 175, 80, 0.4); }
}

@keyframes _incorrectFeedback_7z27o_1 {
  0% { box-shadow: 0 0 0 rgba(244, 67, 54, 0.4); }
  25% { transform: translateX(-5px); }
  50% { transform: translateX(5px); box-shadow: 0 0 20px rgba(244, 67, 54, 0.8); }
  75% { transform: translateX(-5px); }
  100% { transform: translateX(0); box-shadow: 0 0 0 rgba(244, 67, 54, 0.4); }
}

/* Container principal - EXATO DO MEMORYGAME */
._letterRecognitionGame_7z27o_89 {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Conteúdo do jogo - EXATO DO MEMORYGAME */
._gameContent_7z27o_111 {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Header do jogo - EXATO DO MEMORYGAME */
._gameHeader_7z27o_131 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 3rem 1rem 1rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  position: relative;
  min-height: 70px;
}

._gameTitle_7z27o_159 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

._activitySubtitle_7z27o_183 {
  font-size: 0.7rem;
  opacity: 0.8;
  margin-top: 0.25rem;
  background: var(--card-background);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: var(--card-border);
}

/* Botão TTS no header - EXATO DO MEMORYGAME */
._headerTtsButton_7z27o_205 {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: white;
  z-index: 10;
}

._headerTtsButton_7z27o_205:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

._headerTtsButton_7z27o_205:active {
  transform: scale(0.95);
}

/* Estados do toggle TTS - EXATO DO MEMORYGAME */
._ttsActive_7z27o_263 {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
}

._ttsInactive_7z27o_273 {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
}

/* Menu de atividades - EXATO DO MEMORYGAME */
._activityMenu_7z27o_285 {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

._activitySelector_7z27o_301 {
  margin-bottom: 2rem;
}

._selectorTitle_7z27o_309 {
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-align: left;
}

._activityButtons_7z27o_325 {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  justify-content: center;
}

._activityButton_7z27o_325 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

._activityButton_7z27o_325:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

._activityButton_7z27o_325._active_7z27o_377 {
  background: var(--success-bg) !important;
  border: 2px solid var(--success-border) !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

/* Elementos de atividade - EXATO DO MEMORYGAME */
._activityIcon_7z27o_391 {
  font-size: 1.2rem;
  margin-right: 0.5rem;
}

._activityName_7z27o_401 {
  flex: 1;
  text-align: left;
}

._activeIndicator_7z27o_411 {
  color: #00ff00;
  font-size: 0.8rem;
  margin-left: auto;
  animation: _pulse_7z27o_1 1.5s ease-in-out infinite;
}

@keyframes _pulse_7z27o_1 {
  0%, 100% { opacity: 0.7; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

/* Estatísticas do jogo - EXATO DO MEMORYGAME */
._gameStats_7z27o_437 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

._statCard_7z27o_451 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

._statCard_7z27o_451::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #E91E63);
  opacity: 0.7;
}

._statValue_7z27o_495 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: white;
}

._statLabel_7z27o_509 {
  font-size: 0.8rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.8);
}

/* Área do jogo principal - EXATO DO MEMORYGAME */
._gameArea_7z27o_523 {
  flex: 1;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

/* Grid de letras - seguindo padrão de cards do MemoryGame */
._lettersGrid_7z27o_555 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  width: 100%;
  max-width: 600px;
  margin: 2rem 0;
}

/* Card de letra - baseado no card do MemoryGame */
._letterCard_7z27o_575 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  position: relative;
  overflow: hidden;
}

._letterCard_7z27o_575:hover {
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

._letterCard_7z27o_575:active {
  transform: translateY(-2px);
}

/* Estados do card de letra - EXATO DO MEMORYGAME */
._letterCard_7z27o_575._correct_7z27o_631 {
  background: var(--success-bg) !important;
  border: 2px solid var(--success-border) !important;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.4);
  animation: _correctFeedback_7z27o_1 0.6s ease-out;
}

._letterCard_7z27o_575._incorrect_7z27o_645 {
  background: var(--error-bg) !important;
  border: 2px solid var(--error-border) !important;
  box-shadow: 0 4px 20px rgba(244, 67, 54, 0.4);
  animation: _incorrectFeedback_7z27o_1 0.6s ease-out;
}

._letterCard_7z27o_575._selected_7z27o_659 {
  background: rgba(255, 193, 7, 0.3) !important;
  border: 2px solid #FFC107 !important;
  box-shadow: 0 4px 20px rgba(255, 193, 7, 0.4);
  transform: scale(1.05);
}

._letterCard_7z27o_575._active_7z27o_377 {
  background: rgba(33, 150, 243, 0.2) !important;
  border: 2px solid #2196F3 !important;
  box-shadow: 0 0 15px rgba(33, 150, 243, 0.5);
  animation: _activePulse_7z27o_1 2s ease-in-out infinite;
}

._letterCard_7z27o_575._disabled_7z27o_687 {
  opacity: 0.5;
  cursor: not-allowed !important;
  background: rgba(128, 128, 128, 0.2) !important;
}

@keyframes _activePulse_7z27o_1 {
  0%, 100% {
    box-shadow: 0 0 15px rgba(33, 150, 243, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 25px rgba(33, 150, 243, 0.8);
    transform: scale(1.02);
  }
}

/* Conteúdo da letra - seguindo padrão MemoryGame */
._letterContent_7z27o_723 {
  font-size: 3rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

._letterLabel_7z27o_739 {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

/* Botão de som - seguindo padrão MemoryGame */
._soundButton_7z27o_753 {
  background: rgba(74, 144, 226, 0.3);
  border: 2px solid rgba(74, 144, 226, 0.5);
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  margin: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

._soundButton_7z27o_753:hover {
  background: rgba(74, 144, 226, 0.5);
  transform: scale(1.05);
}

/* Botão para repetir instrução - seguindo padrão MemoryGame */
._repeatButton_7z27o_797 {
  background: rgba(74, 144, 226, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 600;
  margin-left: 1rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

._repeatButton_7z27o_797:hover {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.4);
}

._repeatButton_7z27o_797:active {
  transform: scale(0.95);
}

/* Feedback e controles - seguindo padrão MemoryGame */
._feedback_7z27o_851 {
  text-align: center;
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 12px;
  font-weight: 600;
  animation: _fadeIn_7z27o_1 0.3s ease-out;
}

._feedback_7z27o_851._success_7z27o_869 {
  background: var(--success-bg);
  border: 2px solid var(--success-border);
  color: white;
}

._feedback_7z27o_851._error_7z27o_881 {
  background: var(--error-bg);
  border: 2px solid var(--error-border);
  color: white;
}

@keyframes _fadeIn_7z27o_1 {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Controles do jogo - PADRÃO CONTAGEMNUMEROS */
._gameControls_7z27o_905 {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

._controlButton_7z27o_921 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 120px;
  justify-content: center;
}

._controlButton_7z27o_921:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

._controlButton_7z27o_921:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* ===== ATIVIDADE DE FORMAÇÃO DE PALAVRAS ===== */
._wordFormationActivity_7z27o_979 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  padding: 2rem;
}

._wordTarget_7z27o_995 {
  text-align: center;
  padding: 2rem;
  background: rgba(156, 39, 176, 0.2);
  border: 2px solid rgba(156, 39, 176, 0.5);
  border-radius: 16px;
  margin-bottom: 2rem;
}

._wordEmoji_7z27o_1013 {
  font-size: 4rem;
  margin-bottom: 1rem;
  display: block;
}

._wordMeaning_7z27o_1025 {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
}

._wordSlots_7z27o_1039 {
  display: flex;
  gap: 0.5rem;
  margin: 2rem 0;
  justify-content: center;
  flex-wrap: wrap;
}

._wordSlot_7z27o_1039 {
  width: 60px;
  height: 60px;
  border: 2px dashed rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 700;
  color: white;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

._wordSlot_7z27o_1039._filled_7z27o_1085 {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.6);
  border-style: solid;
}

._availableLetters_7z27o_1097 {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 2rem;
}

._availableLetter_7z27o_1097 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  min-width: 50px;
  text-align: center;
}

._availableLetter_7z27o_1097:hover {
  transform: scale(1.05);
  background: rgba(255, 255, 255, 0.2);
}

._availableLetter_7z27o_1097._used_7z27o_1151 {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(128, 128, 128, 0.2);
}

/* ===== MELHORIAS PARA FORMAÇÃO DE PALAVRAS ===== */
._soundActivity_7z27o_1165 {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 2rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 16px;
  backdrop-filter: var(--card-blur);
  margin-bottom: 2rem;
}

._soundActivity_7z27o_1165 h3 {
  color: white;
  font-size: 2rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
}

._soundIndicator_7z27o_1211 {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: _soundPulse_7z27o_1 2s ease-in-out infinite;
  text-align: center;
}

@keyframes _soundPulse_7z27o_1 {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.1); opacity: 1; }
}

._activityTip_7z27o_1235 {
  color: rgba(255, 255, 255, 0.95);
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  line-height: 1.5;
  font-weight: 500;
  max-width: 600px;
  text-align: center;
  background: rgba(255, 193, 7, 0.2);
  padding: 1rem 1.5rem;
  border-radius: 12px;
  border-left: 4px solid #FFC107;
}

._activityInstruction_7z27o_1263 {
  text-align: center;
  padding: 2rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 16px;
  backdrop-filter: var(--card-blur);
}

._activityInstruction_7z27o_1263 h3 {
  color: white;
  font-size: 1.8rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

._activityInstruction_7z27o_1263 p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  line-height: 1.5;
}

/* Responsividade - seguindo padrão MemoryGame + Formação de Palavras */
@media (max-width: 768px) {
  ._letterRecognitionGame_7z27o_89 {
    padding: 0.5rem;
  }

  ._gameArea_7z27o_523 {
    padding: 1rem;
    min-height: 300px;
  }

  ._lettersGrid_7z27o_555 {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
  }

  ._letterCard_7z27o_575 {
    padding: 1.5rem 0.5rem;
    min-height: 100px;
  }

  ._letterContent_7z27o_723 {
    font-size: 2.5rem;
  }

  ._gameStats_7z27o_437 {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Responsividade para Formação de Palavras */
  ._wordFormationActivity_7z27o_979 {
    padding: 1rem;
    gap: 1.5rem;
  }

  ._wordTarget_7z27o_995 {
    padding: 1.5rem;
  }

  ._wordEmoji_7z27o_1013 {
    font-size: 3rem;
  }

  ._wordMeaning_7z27o_1025 {
    font-size: 1.3rem;
  }

  ._wordSlots_7z27o_1039 {
    gap: 0.3rem;
    margin: 1.5rem 0;
  }

  ._wordSlot_7z27o_1039 {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }

  ._availableLetters_7z27o_1097 {
    gap: 0.5rem;
    margin-top: 1.5rem;
  }

  ._availableLetter_7z27o_1097 {
    padding: 0.8rem;
    font-size: 1.3rem;
    min-width: 45px;
  }

  ._soundActivity_7z27o_1165 {
    padding: 1.5rem;
    min-height: 150px;
  }

  ._soundActivity_7z27o_1165 h3 {
    font-size: 1.6rem;
    margin-bottom: 1rem;
  }

  ._soundIndicator_7z27o_1211 {
    font-size: 3rem;
  }

  ._activityTip_7z27o_1235 {
    font-size: 1rem;
    padding: 0.8rem 1rem;
    margin-bottom: 1rem;
  }
}


