/**
 * @file GameStartScreen.module.css
 * @description Estilos modulares para o componente GameStartScreen
 * @version 3.0.0
 * Layout baseado no padrão aprovado das telas de dificuldade
 */

/* Container principal */
._gameStartContainer_spz1j_9 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  overflow-y: auto;
  padding: 2rem 1rem;
}

/* Conteúdo principal */
._gameStartMain_spz1j_22 {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

._gameStartContent_spz1j_29 {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
}

/* Seção do título */
._gameStartTitleSection_spz1j_36 {
  text-align: center;
  margin-bottom: 3rem;
}

._gameIconLarge_spz1j_41 {
  font-size: 5rem;
  margin-bottom: 1.5rem;
  display: block;
}

._gameStartTitle_spz1j_36 {
  font-size: 3rem;
  font-weight: 800;
  margin: 0 0 0.5rem 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  color: white;
}

._gameStartSubtitle_spz1j_55 {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0 0 1rem 0;
  font-weight: 300;
  color: white;
}

/* Card de instruções */
._gameStartInstructionCard_spz1j_64 {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: center;
}

._gameStartInstructionCard_spz1j_64 p {
  margin: 0;
  font-size: 1.1rem;
  line-height: 1.6;
}

/* Conteúdo customizado - Benefícios */
._gameStartCustomContent_spz1j_81 {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
}

/* Seção de dificuldade */
._difficultySection_spz1j_91 {
  margin-bottom: 2rem;
}

._difficultySectionTitle_spz1j_95 {
  font-size: 1.5rem;
  font-weight: 700;
  text-align: center;
  margin: 0 0 1.5rem 0;
  color: #ffffff;
}

._difficultyOptions_spz1j_103 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

._difficultyCard_spz1j_110 {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

._difficultyCard_spz1j_110:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.15);
}

._difficultyCard_spz1j_110._active_spz1j_130 {
  transform: translateY(-8px) scale(1.05);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4);
  border-color: #96CEB4;
  background: rgba(150, 206, 180, 0.2);
}

._difficultyCard_spz1j_110::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #96CEB4, #FECA57, #FF6B6B);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

._difficultyCard_spz1j_110:hover::before,
._difficultyCard_spz1j_110._active_spz1j_130::before {
  transform: scaleX(1);
}

._difficultyIcon_spz1j_154 {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

._difficultyTitle_spz1j_160 {
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #ffffff;
}

._difficultyDescription_spz1j_167 {
  font-size: 0.95rem;
  opacity: 0.9;
  line-height: 1.5;
  margin-bottom: 1rem;
}

._difficultyPreview_spz1j_174 {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

._difficultyPreview_spz1j_174 span {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

/* Botão de iniciar */
._gameStartActions_spz1j_193 {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

._gameStartPlayButton_spz1j_199 {
  background: linear-gradient(135deg, #96CEB4 0%, #FECA57 100%);
  border: none;
  border-radius: 25px;
  padding: 1rem 3rem;
  font-size: 1.2rem;
  font-weight: 700;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

._gameStartPlayButton_spz1j_199:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #FECA57 0%, #FF6B6B 100%);
}

._gameStartPlayIcon_spz1j_222 {
  font-size: 1.3rem;
}

/* Footer */
._gameStartFooter_spz1j_227 {
  text-align: center;
  opacity: 0.8;
}

._gameStartFooterText_spz1j_232 {
  margin: 0;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Alto contraste */
._highContrast_spz1j_242 {
  background: #000000;
  color: #ffffff;
}

._highContrast_spz1j_242 ._difficultyCard_spz1j_110 {
  background: #333333;
  border-color: #ffffff;
  color: #ffffff;
}

._highContrast_spz1j_242 ._gameStartPlayButton_spz1j_199 {
  background: #ffffff;
  color: #000000;
}

/* Responsividade */
@media (max-width: 768px) {
  ._gameStartContainer_spz1j_9 {
    padding: 1rem;
  }

  ._gameStartHeader_spz1j_264 {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  ._gameStartTitle_spz1j_36 {
    font-size: 2.2rem;
  }

  ._gameIconLarge_spz1j_41 {
    font-size: 3rem;
  }

  ._difficultyOptions_spz1j_103 {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  ._difficultyCard_spz1j_110 {
    padding: 1.5rem;
  }

  ._gameStartPlayButton_spz1j_199 {
    padding: 0.75rem 2rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  ._gameStartTitle_spz1j_36 {
    font-size: 1.8rem;
  }

  ._gameIconLarge_spz1j_41 {
    font-size: 2.5rem;
  }

  ._difficultyCard_spz1j_110 {
    padding: 1rem;
  }

  ._difficultyIcon_spz1j_154 {
    font-size: 2.5rem;
  }

  ._difficultyTitle_spz1j_160 {
    font-size: 1.2rem;
  }
}

/* Animações */
@keyframes _fadeInUp_spz1j_1 {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

._gameStartContent_spz1j_29 > * {
  animation: _fadeInUp_spz1j_1 0.6s ease-out forwards;
}

._gameStartContent_spz1j_29 > *:nth-child(1) { animation-delay: 0.1s; }
._gameStartContent_spz1j_29 > *:nth-child(2) { animation-delay: 0.2s; }
._gameStartContent_spz1j_29 > *:nth-child(3) { animation-delay: 0.3s; }
._gameStartContent_spz1j_29 > *:nth-child(4) { animation-delay: 0.4s; }
._gameStartContent_spz1j_29 > *:nth-child(5) { animation-delay: 0.5s; }

/* Redução de movimento */
._reducedMotion_spz1j_338 ._difficultyCard_spz1j_110,
._reducedMotion_spz1j_338 ._gameStartPlayButton_spz1j_199,
._reducedMotion_spz1j_338 ._backButton_spz1j_340 {
  transition: none;
  animation: none;
}

._reducedMotion_spz1j_338 ._difficultyCard_spz1j_110:hover,
._reducedMotion_spz1j_338 ._difficultyCard_spz1j_110._active_spz1j_130 {
  transform: none;
}

._reducedMotion_spz1j_338 ._gameStartContent_spz1j_29 > * {
  animation: none;
}/**
 * @file ImageAssociation.module.css
 * @description Estilos modulares para o Jogo de Associação de Imagens - Padrão Unificado
 * @version 3.1.0
 */

/* Variáveis CSS para consistência e reutilização */
:root {
  --card-background: rgba(255, 255, 255, 0.1);
  --card-border: 1px solid rgba(255, 255, 255, 0.2);
  --card-blur: blur(10px);
  --success-bg: rgba(76, 175, 80, 0.3);
  --success-border: rgba(76, 175, 80, 0.5);
  --error-bg: rgba(244, 67, 54, 0.3);
  --error-border: rgba(244, 67, 54, 0.5);
  --primary-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Container principal do ImageAssociation */
._imageAssociationGame_9wjam_41 {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Container principal - genérico para outros jogos */
._gameContainer_9wjam_63 {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Conteúdo do jogo */
._gameContent_9wjam_85 {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Header do jogo */
._gameHeader_9wjam_105 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 3rem 1rem 1rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  position: relative;
  min-height: 70px;
}

._gameTitle_9wjam_133 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

._activitySubtitle_9wjam_157 {
  font-size: 0.7rem;
  opacity: 0.8;
  margin-top: 0.25rem;
  background: var(--card-background);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: var(--card-border);
}

/* Estatísticas */
._gameStats_9wjam_179 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

._statCard_9wjam_193 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

._statCard_9wjam_193::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #E91E63);
  opacity: 0.7;
}

._statValue_9wjam_237 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: white;
}

._statLabel_9wjam_251 {
  font-size: 0.8rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.8);
}

/* Área da pergunta */
._questionArea_9wjam_265 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  text-align: center;
}

._questionTitle_9wjam_285 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: white;
}

._objectsDisplay_9wjam_299 {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
  margin: 2rem 0;
  min-height: 150px;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 2px dashed rgba(255, 255, 255, 0.3);
}

._countingObject_9wjam_327 {
  font-size: 3rem;
  transition: all 0.3s ease;
  animation: _objectAppear_9wjam_1 0.5s ease-out;
  cursor: default;
  user-select: none;
}

._countingObject_9wjam_327:hover {
  transform: scale(1.1);
}

@keyframes _objectAppear_9wjam_1 {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-10deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* Opções de resposta */
._answerOptions_9wjam_375 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

._answerButton_9wjam_391 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 1.5rem;
  color: white;
  font-size: 2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

._answerButton_9wjam_391:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.25);
}

._answerButton_9wjam_391._correct_9wjam_441 {
  border-color: var(--success-border);
  background: var(--success-bg);
  animation: _correctPulse_9wjam_1 0.6s ease-in-out;
}

._answerButton_9wjam_391._incorrect_9wjam_453 {
  border-color: var(--error-border);
  background: var(--error-bg);
  animation: _incorrectShake_9wjam_1 0.6s ease-in-out;
}

._answerButton_9wjam_391:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@keyframes _correctPulse_9wjam_1 {
  0%, 100% { transform: translateY(-5px) scale(1.05); }
  50% { transform: translateY(-5px) scale(1.2); }
}

@keyframes _incorrectShake_9wjam_1 {
  0%, 100% { transform: translateY(-5px) translateX(0); }
  25% { transform: translateY(-5px) translateX(-10px); }
  75% { transform: translateY(-5px) translateX(10px); }
}

/* Controles do jogo */
._gameControls_9wjam_501 {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

._controlButton_9wjam_517 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 600;
}

._controlButton_9wjam_517:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

._nextButton_9wjam_553 {
  background: var(--success-bg);
  border-color: var(--success-border);
}

._nextButton_9wjam_553:hover {
  background: rgba(76, 175, 80, 0.4);
}

/* Feedback */
._feedbackMessage_9wjam_573 {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 1.5rem 2.5rem;
  border-radius: 16px;
  font-size: 1.3rem;
  font-weight: 700;
  z-index: 1000;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: _messageSlide_9wjam_1 3s ease-in-out;
}

._feedbackMessage_9wjam_573._success_9wjam_603 {
  background: var(--success-bg);
  color: white;
}

._feedbackMessage_9wjam_573._error_9wjam_613 {
  background: var(--error-bg);
  color: white;
}

@keyframes _messageSlide_9wjam_1 {
  0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
  15% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  85% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}

/* Botões TTS */
._headerTtsButton_9wjam_639 {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: white;
  z-index: 10;
}

._headerTtsButton_9wjam_639:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

._headerTtsButton_9wjam_639:active {
  transform: scale(0.95);
}

._ttsActive_9wjam_695 {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
}

._ttsInactive_9wjam_705 {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
}

._repeatButton_9wjam_715 {
  background: rgba(74, 144, 226, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 600;
  margin-left: 1rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

._repeatButton_9wjam_715:hover {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.4);
}

._repeatButton_9wjam_715:active {
  transform: scale(0.95);
}

._ttsIndicator_9wjam_767 {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(74, 144, 226, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 6px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  z-index: 5;
  pointer-events: none;
  transition: all 0.2s ease;
}

._answerButton_9wjam_391:hover ._ttsIndicator_9wjam_767 {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.1);
}

/* Menu de atividades */
._activityMenu_9wjam_815 {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

._activityButton_9wjam_831 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

._activityButton_9wjam_831:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

._activityButton_9wjam_831._active_9wjam_869 {
  background: var(--success-bg) !important;
  border: 2px solid var(--success-border) !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

._activityIcon_9wjam_881 {
  font-size: 1.2rem;
}

._activityName_9wjam_889 {
  font-weight: 500;
}

/* Conteúdo das atividades */
._activityContent_9wjam_899 {
  width: 100%;
  min-height: 400px;
}

._activityContainer_9wjam_909 {
  width: 100%;
  padding: 1rem;
}

._activityHeader_9wjam_919 {
  text-align: center;
  margin-bottom: 2rem;
}

._activityTitle_9wjam_929 {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  margin-bottom: 0.5rem;
}

._activitySubtitle_9wjam_157 {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
}

/* Classes específicas do ColorMatch */
._questionHeader_9wjam_955 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  position: relative;
}

._optionNumber_9wjam_971 {
  font-size: 1.2rem;
  font-weight: bold;
}

._colorDisplay_9wjam_981 {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
  border: 3px solid rgba(255,255,255,0.3);
  box-shadow: 0 4px 20px rgba(0,0,0,0.2);
  transition: all 0.3s ease;
}

._colorDisplay_9wjam_981:hover {
  transform: scale(1.05);
}

/* Atividade de Som */
._soundActivity_9wjam_1025 {
  text-align: center;
}

._soundIndicator_9wjam_1033 {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: _soundPulse_9wjam_1 2s ease-in-out infinite;
}

@keyframes _soundPulse_9wjam_1 {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.1); opacity: 1; }
}

._soundButton_9wjam_1055 {
  background: rgba(74, 144, 226, 0.3);
  border: 2px solid rgba(74, 144, 226, 0.5);
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  margin: 1rem;
}

._soundButton_9wjam_1055:hover {
  background: rgba(74, 144, 226, 0.5);
  transform: scale(1.05);
}

/* Atividade de Estimativa */
._estimationDisplay_9wjam_1093 {
  position: relative;
}

._estimationObjects_9wjam_1101 {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  opacity: 0.9;
}

._estimationObject_9wjam_1101 {
  font-size: 1.5rem;
  transform: rotate(var(--rotation));
  transition: opacity 0.3s ease;
}

._estimationTip_9wjam_1129 {
  text-align: center;
  margin-top: 1rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  background: rgba(255, 193, 7, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border-left: 4px solid #FFC107;
}

/* Atividade de Sequência */
._sequenceDisplay_9wjam_1153 {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-wrap: wrap;
}

._sequenceNumber_9wjam_1171 {
  background: var(--card-background);
  padding: 1rem;
  border-radius: 12px;
  min-width: 60px;
  text-align: center;
  color: white;
  font-weight: bold;
  transition: all 0.3s ease;
}

._sequenceNumber_9wjam_1171:hover {
  transform: scale(1.05);
}

._sequenceArrow_9wjam_1201 {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.5rem;
}

._sequenceMissing_9wjam_1211 {
  background: rgba(255, 255, 80, 0.3) !important;
  border: 2px dashed rgba(255, 255, 255, 0.5);
  animation: _missingPulse_9wjam_1 2s ease-in-out infinite;
}

@keyframes _missingPulse_9wjam_1 {
  0%, 100% { box-shadow: 0 0 0 rgba(255, 255, 80, 0.3); }
  50% { box-shadow: 0 0 20px rgba(255, 255, 80, 0.6); }
}

/* Atividade de Comparação */
._comparisonDisplay_9wjam_1235 {
  display: flex;
  gap: 3rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

._comparisonGroup_9wjam_1251 {
  text-align: center;
  background: var(--card-background);
  padding: 1.5rem;
  border-radius: 16px;
  transition: all 0.3s ease;
}

._comparisonGroup_9wjam_1251:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-5px);
}

._comparisonObjects_9wjam_1277 {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  max-width: 150px;
  margin-bottom: 1rem;
}

._comparisonNumber_9wjam_1295 {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  background: var(--card-background);
  padding: 0.5rem 1rem;
  border-radius: 8px;
}

/* Atividade de Padrões */
._patternDisplay_9wjam_1315 {
  text-align: center;
}

._patternDescription_9wjam_1323 {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1rem;
  background: rgba(156, 39, 176, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border-left: 4px solid #9C27B0;
}

._patternSequence_9wjam_1343 {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-wrap: wrap;
}

._patternNumber_9wjam_1361 {
  background: linear-gradient(135deg, rgba(156, 39, 176, 0.3), rgba(156, 39, 176, 0.1));
  padding: 1rem;
  border-radius: 12px;
  min-width: 60px;
  text-align: center;
  color: white;
  font-weight: bold;
  border: 1px solid rgba(156, 39, 176, 0.4);
  transition: all 0.3s ease;
}

._patternNumber_9wjam_1361:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(156, 39, 176, 0.3);
}

/* Responsividade */
@media (max-width: 768px) {
  ._activityMenu_9wjam_815 {
    gap: 0.25rem;
  }
  
  ._activityButton_9wjam_831 {
    padding: 0.4rem 0.8rem;
    font-size: 0.7rem;
  }
  
  ._comparisonDisplay_9wjam_1235 {
    gap: 1.5rem;
  }
  
  ._sequenceDisplay_9wjam_1153,
  ._patternSequence_9wjam_1343 {
    gap: 0.5rem;
    font-size: 1.5rem;
  }
  
  ._sequenceNumber_9wjam_1171,
  ._patternNumber_9wjam_1361 {
    padding: 0.75rem;
    min-width: 50px;
  }
  
  ._gameStats_9wjam_179 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  ._activityMenu_9wjam_815 {
    flex-direction: column;
    align-items: center;
  }
  
  ._activityButton_9wjam_831 {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }
  
  ._comparisonDisplay_9wjam_1235 {
    flex-direction: column;
    gap: 1rem;
  }
  
  ._sequenceDisplay_9wjam_1153,
  ._patternSequence_9wjam_1343 {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  ._sequenceArrow_9wjam_1201 {
    transform: rotate(90deg);
  }
}

/* Suporte a alto contraste */
[data-theme="high-contrast"] {
  --card-background: #000;
  --card-border: 1px solid #fff;
  --success-bg: #28a745;
  --success-border: #1e7e34;
  --error-bg: #dc3545;
  --error-border: #bd2130;
}

/* Suporte a movimento reduzido */
._reduced-motion_9wjam_1537 {
  ._answerButton_9wjam_391, ._controlButton_9wjam_517, ._countingObject_9wjam_327, ._feedbackMessage_9wjam_573, ._soundIndicator_9wjam_1033, ._sequenceMissing_9wjam_1211 {
    animation: none !important;
    transition: none !important;
  }
}