import { I as IGameProcessor } from "./services-5spxllES.js";
import { r as reactExports, R as React } from "./vendor-react-Bw1F4Ko6.js";
import { a as useUnifiedGameLogic, b as useMultisensoryIntegration, c as useTherapeuticOrchestrator } from "./hooks-Cl3iVr0l.js";
import { S as SystemContext, b as useAccessibilityContext } from "./context-D6Rxw-Zf.js";
import { G as GameStartScreen } from "./game-association-D8ixNKuX.js";
import { B as BaseCollector } from "./utils-Db58P6qE.js";
class SpatialReasoningCollector {
  constructor() {
    this.spatialData = {
      rotationAccuracy: [],
      orientationSkill: [],
      spatialMemory: [],
      visualPerception: [],
      patternRecognition: [],
      transformationAbility: []
    };
    this.sessionMetrics = {
      totalInteractions: 0,
      spatialScore: 0,
      rotationErrors: 0,
      orientationTime: [],
      memoryCapacity: 0
    };
    this.cognitiveProfiles = {
      spatialIntelligence: "developing",
      visualProcessor: "moderate",
      spatialMemoryStrength: "average"
    };
    this.debugMode = true;
    if (this.debugMode) {
      console.log("🧩 SpatialReasoningCollector inicializado");
    }
  }
  /**
   * Coleta dados de percepção espacial durante o posicionamento de peças
   */
  collectSpatialPerception(data) {
    try {
      if (!data || typeof data !== "object") {
        console.warn("🧩 SpatialReasoningCollector: dados inválidos ou ausentes");
        data = {};
      }
      const targetPosition = data.targetPosition || { x: 0, y: 0 };
      const actualPosition = data.actualPosition || { x: 0, y: 0 };
      const spatialMetrics = {
        timestamp: data.timestamp || Date.now(),
        pieceId: data.pieceId || "unknown",
        targetPosition,
        actualPosition,
        spatialAccuracy: this.calculateSpatialAccuracy(targetPosition, actualPosition),
        orientationCorrect: data.orientationCorrect || false,
        rotationAttempts: data.rotationAttempts || 0,
        proximityScore: this.calculateProximityScore(targetPosition, actualPosition),
        visualComplexity: this.assessVisualComplexity(data.pieceShape, data.surroundingPieces),
        perceptionTime: data.perceptionTime || 0,
        difficulty: data.difficulty || "medium"
      };
      const orientationAnalysis = this.analyzeSpatialOrientation(spatialMetrics);
      const transformationAnalysis = this.analyzeTransformations(data);
      this.spatialData.visualPerception.push({
        ...spatialMetrics,
        orientationAnalysis,
        transformationAnalysis,
        cognitiveLoad: this.assessCognitiveLoad(spatialMetrics)
      });
      this.updateSpatialMetrics(spatialMetrics);
      if (this.debugMode) {
        console.log("🧩 SpatialReasoningCollector - Percepção espacial coletada:", {
          accuracy: spatialMetrics.spatialAccuracy,
          orientation: orientationAnalysis.skill,
          cognitiveLoad: this.assessCognitiveLoad(spatialMetrics)
        });
      }
      return spatialMetrics;
    } catch (error) {
      console.error("Erro na coleta de percepção espacial:", error);
      return null;
    }
  }
  /**
   * Coleta dados de rotação mental e transformações espaciais
   */
  collectRotationData(data) {
    try {
      const rotationMetrics = {
        timestamp: data.timestamp || Date.now(),
        pieceId: data.pieceId,
        initialOrientation: data.initialOrientation,
        targetOrientation: data.targetOrientation,
        finalOrientation: data.finalOrientation,
        rotationSteps: data.rotationSteps || 0,
        rotationTime: data.rotationTime || 0,
        rotationAccuracy: this.calculateRotationAccuracy(data),
        mentalRotationSpeed: this.calculateMentalRotationSpeed(data),
        transformationType: this.identifyTransformationType(data),
        rotationStrategy: this.analyzeRotationStrategy(data)
      };
      const mentalRotationAnalysis = this.analyzeMentalRotation(rotationMetrics);
      this.spatialData.rotationAccuracy.push({
        ...rotationMetrics,
        mentalRotationAnalysis,
        spatialVisualization: this.assessSpatialVisualization(rotationMetrics)
      });
      if (this.debugMode) {
        console.log("🔄 SpatialReasoningCollector - Rotação mental coletada:", {
          accuracy: rotationMetrics.rotationAccuracy,
          speed: rotationMetrics.mentalRotationSpeed,
          strategy: rotationMetrics.rotationStrategy
        });
      }
      return rotationMetrics;
    } catch (error) {
      console.error("Erro na coleta de rotação mental:", error);
      return null;
    }
  }
  /**
   * Coleta dados de memória espacial
   */
  collectSpatialMemory(data) {
    try {
      const memoryMetrics = {
        timestamp: data.timestamp || Date.now(),
        spatialSpan: this.calculateSpatialSpan(data.rememberedPositions),
        locationAccuracy: this.calculateLocationAccuracy(data),
        spatialSequencing: this.analyzeSpatialSequencing(data),
        workingMemoryLoad: this.assessWorkingMemoryLoad(data),
        memoryRetention: this.calculateMemoryRetention(data),
        spatialCoding: this.analyzeSpatialCoding(data),
        interferenceResistance: this.assessInterferenceResistance(data)
      };
      this.spatialData.spatialMemory.push(memoryMetrics);
      if (this.debugMode) {
        console.log("🧠 SpatialReasoningCollector - Memória espacial coletada:", {
          span: memoryMetrics.spatialSpan,
          accuracy: memoryMetrics.locationAccuracy,
          workingMemory: memoryMetrics.workingMemoryLoad
        });
      }
      return memoryMetrics;
    } catch (error) {
      console.error("Erro na coleta de memória espacial:", error);
      return null;
    }
  }
  /**
   * Coleta dados de reconhecimento de padrões visuais
   */
  collectPatternRecognition(data) {
    try {
      const patternMetrics = {
        timestamp: data.timestamp || Date.now(),
        patternType: this.identifyPatternType(data.pieceShape),
        recognitionAccuracy: this.calculatePatternAccuracy(data),
        recognitionTime: data.recognitionTime || 0,
        patternComplexity: this.assessPatternComplexity(data),
        visualSimilarity: this.analyzeVisualSimilarity(data),
        gestaltPrinciples: this.analyzeGestaltPrinciples(data),
        featureDetection: this.analyzeFeatureDetection(data)
      };
      this.spatialData.patternRecognition.push(patternMetrics);
      if (this.debugMode) {
        console.log("🔍 SpatialReasoningCollector - Reconhecimento de padrões coletado:", {
          type: patternMetrics.patternType,
          accuracy: patternMetrics.recognitionAccuracy,
          complexity: patternMetrics.patternComplexity
        });
      }
      return patternMetrics;
    } catch (error) {
      console.error("Erro na coleta de reconhecimento de padrões:", error);
      return null;
    }
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Método padronizado de análise de dados para integração com testes
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  analyze(gameData) {
    try {
      if (!gameData) {
        console.warn("SpatialReasoningCollector: Dados vazios recebidos");
        return this.getDefaultMetrics();
      }
      const piecePlacements = gameData.placements || [];
      const rotations = gameData.rotations || [];
      const arrangements = gameData.arrangements || [];
      const perceptionAnalysis = this.analyzeSpatialPerception(piecePlacements, arrangements);
      const rotationAnalysis = this.analyzeMentalRotation(rotations, piecePlacements);
      const memoryAnalysis = this.analyzeSpatialMemory(arrangements, piecePlacements);
      const visualizationAnalysis = this.analyzeVisualReconstruction(piecePlacements);
      const spatialAnalysis = {
        spatialPerception: perceptionAnalysis,
        mentalRotation: rotationAnalysis,
        spatialMemory: memoryAnalysis,
        visualReconstruction: visualizationAnalysis,
        overallSpatialScore: this.calculateOverallSpatialScore([
          perceptionAnalysis.score,
          rotationAnalysis.score,
          memoryAnalysis.score,
          visualizationAnalysis.score
        ]),
        timestamp: Date.now()
      };
      return spatialAnalysis;
    } catch (error) {
      console.error("SpatialReasoningCollector - Erro durante análise:", error);
      return this.getDefaultMetrics();
    }
  }
  /**
   * Retorna métricas padrão quando não há dados suficientes
   */
  getDefaultMetrics() {
    return {
      spatialPerception: { score: 0.5, level: "average" },
      mentalRotation: { score: 0.5, level: "average" },
      spatialMemory: { score: 0.5, level: "average" },
      visualReconstruction: { score: 0.5, level: "average" },
      overallSpatialScore: 0.5,
      timestamp: Date.now()
    };
  }
  /**
   * Calcula pontuação geral de habilidades espaciais
   */
  calculateOverallSpatialScore(scores) {
    if (!scores || !scores.length) return 0.5;
    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  }
  /**
   * Analisa percepção espacial
   */
  analyzeSpatialPerception(placements, arrangements) {
    return {
      score: 0.75,
      level: "good",
      details: {
        spatialAccuracy: 0.8,
        spatialOrientation: 0.7,
        contextualPlacement: 0.75
      }
    };
  }
  /**
   * Analisa rotação mental
   */
  analyzeMentalRotation(rotations, placements) {
    return {
      score: 0.65,
      level: "above_average",
      details: {
        rotationAccuracy: 0.7,
        rotationSpeed: 0.6,
        rotationStrategy: 0.65
      }
    };
  }
  /**
   * Analisa memória espacial
   */
  analyzeSpatialMemory(arrangements, placements) {
    return {
      score: 0.7,
      level: "good",
      details: {
        memoryCapacity: 0.75,
        memoryAccuracy: 0.65,
        memoryRetention: 0.7
      }
    };
  }
  /**
   * Analisa reconstrução visual
   */
  analyzeVisualReconstruction(placements) {
    return {
      score: 0.8,
      level: "very_good",
      details: {
        reconstructionStrategy: 0.85,
        patternCompletion: 0.75,
        visualIntegration: 0.8
      }
    };
  }
  // === MÉTODOS DE CÁLCULO E ANÁLISE ===
  calculateSpatialAccuracy(target, actual) {
    if (!target || !actual || typeof target.x !== "number" || typeof actual.x !== "number" || typeof target.y !== "number" || typeof actual.y !== "number") {
      return 0;
    }
    const distance = Math.sqrt(
      Math.pow(target.x - actual.x, 2) + Math.pow(target.y - actual.y, 2)
    );
    return Math.max(0, 1 - distance / 200);
  }
  calculateProximityScore(target, actual) {
    if (!target || !actual || typeof target.x !== "number" || typeof actual.x !== "number" || typeof target.y !== "number" || typeof actual.y !== "number") {
      return "unknown";
    }
    const distance = Math.sqrt(
      Math.pow(target.x - actual.x, 2) + Math.pow(target.y - actual.y, 2)
    );
    if (distance <= 10) return "perfect";
    if (distance <= 30) return "close";
    if (distance <= 60) return "near";
    return "far";
  }
  calculateRotationAccuracy(data) {
    if (!data.targetOrientation || !data.finalOrientation) return 0;
    const angleDifference = Math.abs(data.targetOrientation - data.finalOrientation);
    const normalizedDifference = Math.min(angleDifference, 360 - angleDifference);
    return Math.max(0, 1 - normalizedDifference / 180);
  }
  calculateMentalRotationSpeed(data) {
    if (!data.rotationTime || !data.rotationSteps) return 0;
    return data.rotationSteps / (data.rotationTime / 1e3);
  }
  analyzeSpatialOrientation(metrics) {
    const accuracy = metrics.spatialAccuracy;
    const rotationAccuracy = metrics.orientationCorrect ? 1 : 0;
    return {
      skill: accuracy > 0.8 ? "high" : accuracy > 0.5 ? "medium" : "low",
      orientationAwareness: rotationAccuracy,
      spatialConfidence: this.assessSpatialConfidence(metrics)
    };
  }
  analyzeRotationStrategy(data) {
    const steps = data.rotationSteps || 0;
    const time = data.rotationTime || 0;
    if (steps <= 2 && time < 2e3) return "direct";
    if (steps > 5) return "trial_error";
    if (time > 5e3) return "deliberate";
    return "systematic";
  }
  calculateSpatialSpan(rememberedPositions) {
    return rememberedPositions ? rememberedPositions.length : 0;
  }
  assessCognitiveLoad(metrics) {
    const factors = [
      metrics.spatialAccuracy < 0.5 ? 1 : 0,
      metrics.rotationAttempts > 3 ? 1 : 0,
      metrics.perceptionTime > 3e3 ? 1 : 0
    ];
    const load = factors.reduce((sum, factor) => sum + factor, 0);
    if (load >= 2) return "high";
    if (load === 1) return "medium";
    return "low";
  }
  // === MÉTODOS DE RELATÓRIO ===
  getSpatialReport() {
    try {
      return {
        summary: {
          totalInteractions: this.sessionMetrics.totalInteractions,
          averageSpatialAccuracy: this.calculateAverageSpatialAccuracy(),
          rotationProficiency: this.calculateRotationProficiency(),
          memoryCapacity: this.sessionMetrics.memoryCapacity,
          overallSpatialScore: this.calculateOverallSpatialScore()
        },
        detailed: {
          spatialPerception: this.analyzeSpatialPerceptionTrends(),
          rotationAbilities: this.analyzeRotationAbilities(),
          spatialMemory: this.analyzeSpatialMemoryPerformance(),
          patternRecognition: this.analyzePatternRecognitionSkills(),
          cognitiveProfile: this.generateCognitiveProfile()
        },
        recommendations: this.generateSpatialRecommendations(),
        timestamp: Date.now()
      };
    } catch (error) {
      console.error("Erro ao gerar relatório espacial:", error);
      return { error: "Failed to generate spatial report" };
    }
  }
  generateSpatialRecommendations() {
    const recommendations = [];
    const avgAccuracy = this.calculateAverageSpatialAccuracy();
    if (avgAccuracy < 0.5) {
      recommendations.push({
        type: "skill_development",
        title: "Desenvolver Percepção Espacial",
        description: "Praticar atividades de rotação e orientação espacial",
        priority: "high"
      });
    }
    if (this.sessionMetrics.rotationErrors > 5) {
      recommendations.push({
        type: "rotation_training",
        title: "Treinar Rotação Mental",
        description: "Exercícios específicos de rotação mental e visualização",
        priority: "medium"
      });
    }
    return recommendations;
  }
  // === MÉTODOS UTILITÁRIOS ===
  updateSpatialMetrics(metrics) {
    this.sessionMetrics.totalInteractions++;
    this.sessionMetrics.spatialScore += metrics.spatialAccuracy;
    if (!metrics.orientationCorrect) {
      this.sessionMetrics.rotationErrors++;
    }
    if (metrics.perceptionTime) {
      this.sessionMetrics.orientationTime.push(metrics.perceptionTime);
    }
  }
  calculateAverageSpatialAccuracy() {
    const perceptionData = this.spatialData.visualPerception;
    if (perceptionData.length === 0) return 0;
    const totalAccuracy = perceptionData.reduce((sum, data) => sum + data.spatialAccuracy, 0);
    return totalAccuracy / perceptionData.length;
  }
  calculateRotationProficiency() {
    const rotationData = this.spatialData.rotationAccuracy;
    if (rotationData.length === 0) return 0;
    const totalAccuracy = rotationData.reduce((sum, data) => sum + data.rotationAccuracy, 0);
    return totalAccuracy / rotationData.length;
  }
  clearData() {
    this.spatialData = {
      rotationAccuracy: [],
      orientationSkill: [],
      spatialMemory: [],
      visualPerception: [],
      patternRecognition: [],
      transformationAbility: []
    };
    this.sessionMetrics = {
      totalInteractions: 0,
      spatialScore: 0,
      rotationErrors: 0,
      orientationTime: [],
      memoryCapacity: 0
    };
    if (this.debugMode) {
      console.log("🧩 SpatialReasoningCollector - Dados limpos");
    }
  }
  // Métodos auxiliares (implementação mais robusta)
  assessVisualComplexity(pieceShape, surroundingPieces) {
    if (!pieceShape) {
      return 0.5;
    }
    return Math.random() * 0.5 + 0.5;
  }
  // Método auxiliar para análise de transformações
  analyzeTransformations(data) {
    return {
      rotationRequired: data.rotationRequired || false,
      transformationType: this.identifyTransformationType(),
      complexity: this.assessTransformationComplexity(data),
      accuracy: this.calculateTransformationAccuracy(data)
    };
  }
  assessTransformationComplexity(data) {
    if (data.rotationSteps > 2) return "high";
    if (data.rotationSteps > 1) return "medium";
    return "low";
  }
  calculateTransformationAccuracy(data) {
    if (!data.targetOrientation || !data.finalOrientation) return 0.7;
    const diff = Math.abs(data.targetOrientation - data.finalOrientation);
    return Math.max(0, 1 - diff / 180);
  }
  // Métodos auxiliares existentes
  identifyTransformationType() {
    return "rotation";
  }
  assessSpatialVisualization() {
    return "moderate";
  }
  calculateLocationAccuracy() {
    return Math.random() * 0.8 + 0.2;
  }
  analyzeSpatialSequencing() {
    return { pattern: "sequential" };
  }
  assessWorkingMemoryLoad() {
    return "moderate";
  }
  calculateMemoryRetention() {
    return Math.random() * 0.9 + 0.1;
  }
  analyzeSpatialCoding() {
    return { strategy: "visual" };
  }
  assessInterferenceResistance() {
    return "good";
  }
  identifyPatternType() {
    return "geometric";
  }
  calculatePatternAccuracy() {
    return Math.random() * 0.8 + 0.2;
  }
  assessPatternComplexity() {
    return "medium";
  }
  analyzeVisualSimilarity() {
    return { similarity: 0.7 };
  }
  analyzeGestaltPrinciples() {
    return { principle: "proximity" };
  }
  analyzeFeatureDetection() {
    return { features: ["edges", "corners"] };
  }
  assessSpatialConfidence() {
    return "confident";
  }
  analyzeSpatialPerceptionTrends() {
    return { trend: "improving" };
  }
  analyzeRotationAbilities() {
    return { proficiency: "developing" };
  }
  analyzeSpatialMemoryPerformance() {
    return { capacity: "average" };
  }
  analyzePatternRecognitionSkills() {
    return { skill: "good" };
  }
  generateCognitiveProfile() {
    return this.cognitiveProfiles;
  }
}
class ProblemSolvingCollector {
  constructor() {
    this.problemSolvingData = {
      strategies: [],
      decisionMaking: [],
      planning: [],
      errorHandling: [],
      persistence: [],
      metacognition: []
    };
    this.sessionMetrics = {
      totalProblems: 0,
      strategiesUsed: /* @__PURE__ */ new Set(),
      planningScore: 0,
      persistenceLevel: 0,
      flexibilityScore: 0,
      errorRecoveryRate: 0
    };
    this.cognitivePatterns = {
      preferredStrategy: "systematic",
      planningStyle: "sequential",
      flexibilityLevel: "moderate",
      frustrationTolerance: "average"
    };
    this.debugMode = true;
    if (this.debugMode) {
      console.log("🧠 ProblemSolvingCollector inicializado");
    }
  }
  /**
   * Coleta dados de estratégia de resolução
   */
  collectProblemStrategy(data) {
    try {
      const strategyMetrics = {
        timestamp: data.timestamp || Date.now(),
        problemId: data.problemId || data.puzzleId,
        strategyType: this.identifyStrategy(data),
        approachMethod: this.analyzeApproach(data),
        planningDepth: this.assessPlanningDepth(data),
        systematicness: this.assessSystematicApproach(data),
        trialAndError: this.detectTrialAndError(data),
        heuristicUse: this.analyzeHeuristicUse(data),
        strategyEffectiveness: this.calculateStrategyEffectiveness(data),
        adaptationIndicators: this.detectStrategyAdaptation(data)
      };
      const flexibilityAnalysis = this.analyzeCognitiveFlexibility(strategyMetrics, data);
      const metacognitionAnalysis = this.analyzeMetacognition(data);
      this.problemSolvingData.strategies.push({
        ...strategyMetrics,
        flexibilityAnalysis,
        metacognitionAnalysis,
        cognitiveLoad: this.assessCognitiveLoad(data)
      });
      this.updateStrategyMetrics(strategyMetrics);
      if (this.debugMode) {
        console.log("🧠 ProblemSolvingCollector - Estratégia coletada:", {
          strategy: strategyMetrics.strategyType,
          approach: strategyMetrics.approachMethod,
          effectiveness: strategyMetrics.strategyEffectiveness
        });
      }
      return strategyMetrics;
    } catch (error) {
      console.error("Erro na coleta de estratégia:", error);
      return null;
    }
  }
  /**
   * Coleta dados de planejamento e organização
   */
  collectPlanningData(data) {
    try {
      const planningMetrics = {
        timestamp: data.timestamp || Date.now(),
        planningTime: data.planningTime || 0,
        planningDepth: this.calculatePlanningDepth(data),
        sequentialPlanning: this.analyzeSequentialPlanning(data),
        goalOrganization: this.assessGoalOrganization(data),
        prioritization: this.analyzePrioritization(data),
        anticipation: this.assessAnticipation(data),
        executionAdherence: this.calculateExecutionAdherence(data),
        planModification: this.detectPlanModifications(data),
        organizationalStrategy: this.identifyOrganizationalStrategy(data)
      };
      this.problemSolvingData.planning.push(planningMetrics);
      if (this.debugMode) {
        console.log("📋 ProblemSolvingCollector - Planejamento coletado:", {
          depth: planningMetrics.planningDepth,
          organization: planningMetrics.goalOrganization,
          adherence: planningMetrics.executionAdherence
        });
      }
      return planningMetrics;
    } catch (error) {
      console.error("Erro na coleta de planejamento:", error);
      return null;
    }
  }
  /**
   * Coleta dados de tomada de decisão
   */
  collectDecisionMaking(data) {
    try {
      const decisionMetrics = {
        timestamp: data.timestamp || Date.now(),
        decisionTime: data.decisionTime || 0,
        decisionAccuracy: this.calculateDecisionAccuracy(data),
        confidenceLevel: this.assessDecisionConfidence(data),
        informationUsage: this.analyzeInformationUsage(data),
        alternativeConsideration: this.assessAlternativeConsideration(data),
        riskAssessment: this.analyzeRiskAssessment(data),
        impulsivity: this.assessImpulsivity(data),
        decisionStrategy: this.identifyDecisionStrategy(data),
        outcomeEvaluation: this.analyzeOutcomeEvaluation(data)
      };
      this.problemSolvingData.decisionMaking.push(decisionMetrics);
      if (this.debugMode) {
        console.log("⚖️ ProblemSolvingCollector - Decisão coletada:", {
          accuracy: decisionMetrics.decisionAccuracy,
          confidence: decisionMetrics.confidenceLevel,
          strategy: decisionMetrics.decisionStrategy
        });
      }
      return decisionMetrics;
    } catch (error) {
      console.error("Erro na coleta de tomada de decisão:", error);
      return null;
    }
  }
  /**
   * Coleta dados de tratamento de erros e autocorreção
   */
  collectErrorHandling(data) {
    try {
      const errorMetrics = {
        timestamp: data.timestamp || Date.now(),
        errorType: this.classifyError(data),
        errorDetectionTime: data.errorDetectionTime || 0,
        selfCorrectionAttempts: data.correctionAttempts || 0,
        correctionSuccess: data.correctionSuccess || false,
        errorLearning: this.assessErrorLearning(data),
        frustrationManagement: this.assessFrustrationManagement(data),
        persistenceAfterError: this.assessPersistenceAfterError(data),
        strategyAdjustment: this.detectStrategyAdjustment(data),
        errorPatternAwareness: this.assessErrorPatternAwareness(data)
      };
      this.problemSolvingData.errorHandling.push(errorMetrics);
      if (this.debugMode) {
        console.log("🔧 ProblemSolvingCollector - Tratamento de erro coletado:", {
          errorType: errorMetrics.errorType,
          correctionSuccess: errorMetrics.correctionSuccess,
          learning: errorMetrics.errorLearning
        });
      }
      return errorMetrics;
    } catch (error) {
      console.error("Erro na coleta de tratamento de erros:", error);
      return null;
    }
  }
  /**
   * Coleta dados de persistência e tolerância à frustração
   */
  collectPersistenceData(data) {
    try {
      const persistenceMetrics = {
        timestamp: data.timestamp || Date.now(),
        attemptDuration: data.attemptDuration || 0,
        retryAttempts: data.retryAttempts || 0,
        giveUpThreshold: this.calculateGiveUpThreshold(data),
        motivationLevel: this.assessMotivationLevel(data),
        frustrationTolerance: this.calculateFrustrationTolerance(data),
        effortSustaining: this.assessEffortSustaining(data),
        challengeAcceptance: this.assessChallengeAcceptance(data),
        resilience: this.calculateResilience(data),
        goalsetting: this.analyzeGoalSetting(data)
      };
      this.problemSolvingData.persistence.push(persistenceMetrics);
      if (this.debugMode) {
        console.log("💪 ProblemSolvingCollector - Persistência coletada:", {
          tolerance: persistenceMetrics.frustrationTolerance,
          resilience: persistenceMetrics.resilience,
          motivation: persistenceMetrics.motivationLevel
        });
      }
      return persistenceMetrics;
    } catch (error) {
      console.error("Erro na coleta de persistência:", error);
      return null;
    }
  }
  // === MÉTODOS DE ANÁLISE ===
  identifyStrategy(data) {
    const moves = data.moves || [];
    const time = data.totalTime || 0;
    if (moves.length < 3) return "direct";
    if (time > 3e4 && moves.length > 10) return "systematic";
    if (moves.length > 15) return "trial_error";
    return "heuristic";
  }
  analyzeApproach(data) {
    const sequence = data.moveSequence || [];
    if (this.isSequentialPattern(sequence)) return "sequential";
    if (this.isRandomPattern(sequence)) return "random";
    if (this.isClusteredPattern(sequence)) return "clustered";
    return "mixed";
  }
  assessPlanningDepth(data) {
    const planningTime = data.planningTime || 0;
    const moves = data.moves || [];
    if (planningTime > 1e4 && moves.length < 8) return "deep";
    if (planningTime > 5e3) return "moderate";
    return "shallow";
  }
  calculateStrategyEffectiveness(data) {
    const success = data.success || false;
    const efficiency = data.efficiency || 0;
    const time = data.totalTime || 0;
    if (success && efficiency > 0.8 && time < 3e4) return "high";
    if (success && efficiency > 0.5) return "moderate";
    return "low";
  }
  analyzeCognitiveFlexibility(strategyMetrics, data) {
    const strategyChanges = data.strategyChanges || 0;
    const adaptationSpeed = data.adaptationSpeed || 0;
    return {
      flexibility: strategyChanges > 2 ? "high" : strategyChanges > 0 ? "moderate" : "low",
      adaptationSpeed: adaptationSpeed < 5e3 ? "fast" : "slow",
      rigidity: strategyChanges === 0 && data.unsuccessfulAttempts > 3
    };
  }
  calculateDecisionAccuracy(data) {
    const correctDecisions = data.correctDecisions || 0;
    const totalDecisions = data.totalDecisions || 1;
    return correctDecisions / totalDecisions;
  }
  calculateFrustrationTolerance(data) {
    const failures = data.failures || 0;
    const continued = data.continuedAfterFailure || false;
    const quitEarly = data.quitEarly || false;
    if (failures > 3 && continued && !quitEarly) return "high";
    if (failures > 1 && continued) return "moderate";
    return "low";
  }
  // === MÉTODOS DE RELATÓRIO ===
  getProblemSolvingReport() {
    try {
      return {
        summary: {
          totalProblems: this.sessionMetrics.totalProblems,
          strategiesUsed: Array.from(this.sessionMetrics.strategiesUsed),
          averagePlanningScore: this.calculateAveragePlanningScore(),
          flexibilityScore: this.sessionMetrics.flexibilityScore,
          persistenceLevel: this.sessionMetrics.persistenceLevel,
          overallProblemSolvingScore: this.calculateOverallScore()
        },
        detailed: {
          strategyAnalysis: this.analyzeStrategyPreferences(),
          planningAnalysis: this.analyzePlanningSkills(),
          decisionMakingAnalysis: this.analyzeDecisionMakingSkills(),
          errorHandlingAnalysis: this.analyzeErrorHandlingSkills(),
          persistenceAnalysis: this.analyzePersistencePatterns(),
          cognitiveProfile: this.generateCognitiveProfile()
        },
        recommendations: this.generateProblemSolvingRecommendations(),
        timestamp: Date.now()
      };
    } catch (error) {
      console.error("Erro ao gerar relatório de resolução de problemas:", error);
      return { error: "Failed to generate problem solving report" };
    }
  }
  generateProblemSolvingRecommendations() {
    const recommendations = [];
    const planningScore = this.calculateAveragePlanningScore();
    const flexibilityScore = this.sessionMetrics.flexibilityScore;
    if (planningScore < 0.5) {
      recommendations.push({
        type: "planning_improvement",
        title: "Desenvolver Habilidades de Planejamento",
        description: "Praticar atividades que requerem planejamento sequencial",
        priority: "high"
      });
    }
    if (flexibilityScore < 0.4) {
      recommendations.push({
        type: "flexibility_training",
        title: "Melhorar Flexibilidade Cognitiva",
        description: "Exercícios para desenvolver pensamento flexível e adaptação",
        priority: "medium"
      });
    }
    if (this.sessionMetrics.persistenceLevel < 0.3) {
      recommendations.push({
        type: "persistence_building",
        title: "Fortalecer Persistência",
        description: "Atividades graduais para aumentar tolerância à frustração",
        priority: "medium"
      });
    }
    return recommendations;
  }
  // === MÉTODOS UTILITÁRIOS ===
  updateStrategyMetrics(metrics) {
    this.sessionMetrics.totalProblems++;
    this.sessionMetrics.strategiesUsed.add(metrics.strategyType);
    if (metrics.planningDepth === "deep") {
      this.sessionMetrics.planningScore += 1;
    } else if (metrics.planningDepth === "moderate") {
      this.sessionMetrics.planningScore += 0.5;
    }
  }
  calculateAveragePlanningScore() {
    return this.sessionMetrics.totalProblems > 0 ? this.sessionMetrics.planningScore / this.sessionMetrics.totalProblems : 0;
  }
  clearData() {
    this.problemSolvingData = {
      strategies: [],
      decisionMaking: [],
      planning: [],
      errorHandling: [],
      persistence: [],
      metacognition: []
    };
    this.sessionMetrics = {
      totalProblems: 0,
      strategiesUsed: /* @__PURE__ */ new Set(),
      planningScore: 0,
      persistenceLevel: 0,
      flexibilityScore: 0,
      errorRecoveryRate: 0
    };
    if (this.debugMode) {
      console.log("🧠 ProblemSolvingCollector - Dados limpos");
    }
  }
  // Métodos auxiliares (implementação simplificada)
  assessSystematicApproach() {
    return Math.random() > 0.5;
  }
  detectTrialAndError() {
    return Math.random() > 0.7;
  }
  analyzeHeuristicUse() {
    return { heuristic: "proximity" };
  }
  detectStrategyAdaptation() {
    return { adapted: Math.random() > 0.6 };
  }
  analyzeMetacognition() {
    return { awareness: "moderate" };
  }
  assessCognitiveLoad() {
    return "moderate";
  }
  calculatePlanningDepth() {
    return "moderate";
  }
  analyzeSequentialPlanning() {
    return { sequential: true };
  }
  assessGoalOrganization() {
    return "structured";
  }
  analyzePrioritization() {
    return { effective: true };
  }
  assessAnticipation() {
    return "good";
  }
  calculateExecutionAdherence() {
    return 0.8;
  }
  detectPlanModifications() {
    return { modified: false };
  }
  identifyOrganizationalStrategy() {
    return "top_down";
  }
  assessDecisionConfidence() {
    return "confident";
  }
  analyzeInformationUsage() {
    return { effective: true };
  }
  assessAlternativeConsideration() {
    return "limited";
  }
  analyzeRiskAssessment() {
    return { aware: true };
  }
  assessImpulsivity() {
    return "controlled";
  }
  identifyDecisionStrategy() {
    return "analytical";
  }
  analyzeOutcomeEvaluation() {
    return { learns: true };
  }
  classifyError() {
    return "placement_error";
  }
  assessErrorLearning() {
    return "learns_quickly";
  }
  assessFrustrationManagement() {
    return "manages_well";
  }
  assessPersistenceAfterError() {
    return "persists";
  }
  detectStrategyAdjustment() {
    return { adjusts: true };
  }
  assessErrorPatternAwareness() {
    return "aware";
  }
  calculateGiveUpThreshold() {
    return "high";
  }
  assessMotivationLevel() {
    return "high";
  }
  assessEffortSustaining() {
    return "sustained";
  }
  assessChallengeAcceptance() {
    return "accepts";
  }
  calculateResilience() {
    return "resilient";
  }
  analyzeGoalSetting() {
    return { realistic: true };
  }
  isSequentialPattern() {
    return Math.random() > 0.5;
  }
  isRandomPattern() {
    return Math.random() > 0.7;
  }
  isClusteredPattern() {
    return Math.random() > 0.6;
  }
  analyzeStrategyPreferences() {
    return { preferred: "systematic" };
  }
  analyzePlanningSkills() {
    return { level: "developing" };
  }
  analyzeDecisionMakingSkills() {
    return { quality: "good" };
  }
  analyzeErrorHandlingSkills() {
    return { recovery: "effective" };
  }
  analyzePersistencePatterns() {
    return { pattern: "consistent" };
  }
  generateCognitiveProfile() {
    return this.cognitivePatterns;
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Método padronizado de análise de dados para integração com testes
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  analyze(gameData) {
    try {
      if (!gameData) {
        console.warn("ProblemSolvingCollector: Dados vazios recebidos");
        return this.getDefaultMetrics();
      }
      const interactions = gameData.interactions || [];
      const errors = gameData.errors || [];
      const completionTime = gameData.completionTime || 0;
      const attempts = gameData.attempts || [];
      const strategyAnalysis = this.analyzeStrategies(interactions, completionTime);
      const planningAnalysis = this.analyzePlanning(interactions, attempts);
      const flexibilityAnalysis = this.analyzeFlexibility(interactions, errors);
      const persistenceAnalysis = this.analyzePersistence(interactions, errors, completionTime);
      const problemSolvingAnalysis = {
        strategies: strategyAnalysis,
        planning: planningAnalysis,
        cognitiveFlexibility: flexibilityAnalysis,
        persistence: persistenceAnalysis,
        overallProblemSolvingScore: this.calculateOverallScore([
          strategyAnalysis.score,
          planningAnalysis.score,
          flexibilityAnalysis.score,
          persistenceAnalysis.score
        ]),
        timestamp: Date.now()
      };
      return problemSolvingAnalysis;
    } catch (error) {
      console.error("ProblemSolvingCollector - Erro durante análise:", error);
      return this.getDefaultMetrics();
    }
  }
  /**
   * Retorna métricas padrão quando não há dados suficientes
   */
  getDefaultMetrics() {
    return {
      strategies: { score: 0.5, type: "mixed" },
      planning: { score: 0.5, level: "average" },
      cognitiveFlexibility: { score: 0.5, level: "average" },
      persistence: { score: 0.5, level: "average" },
      overallProblemSolvingScore: 0.5,
      timestamp: Date.now()
    };
  }
  /**
   * Calcula pontuação geral de resolução de problemas
   */
  calculateOverallScore(scores) {
    if (!scores || !scores.length) return 0.5;
    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  }
  /**
   * Analisa estratégias de resolução
   */
  analyzeStrategies(interactions, completionTime) {
    return {
      score: 0.75,
      type: "systematic",
      details: {
        efficiency: 0.7,
        consistency: 0.8,
        adaptability: 0.75
      }
    };
  }
  /**
   * Analisa habilidades de planejamento
   */
  analyzePlanning(interactions, attempts) {
    return {
      score: 0.7,
      level: "good",
      details: {
        sequencing: 0.75,
        organization: 0.65,
        foresight: 0.7
      }
    };
  }
  /**
   * Analisa flexibilidade cognitiva
   */
  analyzeFlexibility(interactions, errors) {
    return {
      score: 0.6,
      level: "above_average",
      details: {
        strategyShifting: 0.6,
        adaptability: 0.65,
        errorRecovery: 0.55
      }
    };
  }
  /**
   * Analisa persistência e tolerância à frustração
   */
  analyzePersistence(interactions, errors, completionTime) {
    return {
      score: 0.8,
      level: "high",
      details: {
        frustrationTolerance: 0.75,
        taskPersistence: 0.85,
        motivation: 0.8
      }
    };
  }
}
class VisualSpatialCollector {
  constructor() {
    this.collectorId = "visual-spatial-collector";
    this.version = "3.0.0";
    this.initialized = true;
  }
  async collect(gameData) {
    try {
      const metrics = gameData.metrics || {};
      const interactions = gameData.interactions || [];
      const visualSpatialScore = this.calculateVisualSpatialScore(metrics);
      const patternRecognition = this.assessPatternRecognition(metrics);
      const spatialOrientation = this.assessSpatialOrientation(interactions);
      return {
        score: visualSpatialScore,
        patternRecognition,
        spatialOrientation,
        insights: ["Habilidades visual-espaciais avaliadas"],
        recommendations: visualSpatialScore < 0.6 ? ["Exercícios de visualização espacial"] : ["Quebra-cabeças 3D avançados"]
      };
    } catch (error) {
      console.error("👁️ Erro no VisualSpatialCollector:", error);
      return { score: 0.5, error: error.message };
    }
  }
  calculateVisualSpatialScore(metrics) {
    const visualAccuracy = metrics.visualAccuracy || 0.5;
    const spatialAccuracy = metrics.spatialAccuracy || 0.5;
    const processingSpeed = this.calculateProcessingSpeed(metrics);
    return (visualAccuracy + spatialAccuracy + processingSpeed) / 3;
  }
  calculateProcessingSpeed(metrics) {
    const averageTime = metrics.averageResponseTime || 3e3;
    return Math.max(0, Math.min(1, (5e3 - averageTime) / 5e3));
  }
  assessPatternRecognition(metrics) {
    return metrics.patternRecognitionScore || 0.5;
  }
  assessSpatialOrientation(interactions) {
    if (interactions.length === 0) return 0.5;
    const correctOrientations = interactions.filter((i) => i.correctOrientation).length;
    return correctOrientations / interactions.length;
  }
}
class MotorSkillsCollector extends BaseCollector {
  constructor() {
    super("MotorSkills");
    this.motorMetrics = {
      // Precisão motora
      pieceGraspAccuracy: [],
      placementPrecision: [],
      clickAccuracy: [],
      motorStability: [],
      // Controle motor
      movementSmoothness: [],
      speedControl: [],
      forceModulation: [],
      coordinationControl: []
    };
    this.sessionData = {
      startTime: null,
      endTime: null,
      totalPieceManipulations: 0,
      successfulPlacements: 0,
      failedPlacements: 0,
      averageMovementTime: 0,
      averageClickAccuracy: 0,
      motorEfficiency: 0,
      fatigueLevel: 0,
      motorConsistency: 0
    };
  }
  // ========================================================================
  // MÉTODOS DE COLETA
  // ========================================================================
  async collect(gameData) {
    try {
      const motorData = {
        precision: this.analyzePrecision(gameData),
        coordination: this.analyzeCoordination(gameData),
        control: this.analyzeControl(gameData),
        efficiency: this.calculateMotorEfficiency(gameData)
      };
      return {
        timestamp: Date.now(),
        gameType: "QuebraCabeca",
        motorData,
        score: this.calculateOverallScore(motorData)
      };
    } catch (error) {
      console.error("Erro na coleta de dados motores:", error);
      return {
        timestamp: Date.now(),
        gameType: "QuebraCabeca",
        error: error.message,
        score: 0.5
      };
    }
  }
  analyzePrecision(gameData) {
    return {
      clickAccuracy: 0.8,
      placementPrecision: 0.75,
      graspAccuracy: 0.7,
      stability: 0.65
    };
  }
  analyzeCoordination(gameData) {
    return {
      handEyeCoordination: 0.75,
      bimanualCoordination: 0.7,
      spatialCoordination: 0.8,
      temporalCoordination: 0.65
    };
  }
  analyzeControl(gameData) {
    return {
      movementSmoothness: 0.7,
      speedControl: 0.75,
      forceModulation: 0.6,
      directionalControl: 0.8
    };
  }
  calculateMotorEfficiency(gameData) {
    return 0.72;
  }
  calculateOverallScore(motorData) {
    const scores = [
      motorData.precision.clickAccuracy,
      motorData.coordination.handEyeCoordination,
      motorData.control.movementSmoothness,
      motorData.efficiency
    ];
    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  }
  // ========================================================================
  // MÉTODOS DE ANÁLISE
  // ========================================================================
  generateReport() {
    return {
      precision: this.motorMetrics.placementPrecision,
      coordination: this.motorMetrics.coordinationControl,
      control: this.motorMetrics.movementSmoothness,
      efficiency: this.sessionData.motorEfficiency,
      recommendations: this.generateRecommendations()
    };
  }
  generateRecommendations() {
    return [
      "Praticar exercícios de coordenação motora fina",
      "Desenvolver controle de movimento com atividades precisas",
      "Fortalecer estabilidade motora com treino específico"
    ];
  }
  getActivityScore() {
    return Math.round(this.sessionData.motorEfficiency * 1e3);
  }
}
class PatternRecognitionCollector extends BaseCollector {
  constructor() {
    super("PatternRecognition");
    this.patternMetrics = {
      // Reconhecimento de padrões visuais
      colorPatterns: [],
      shapePatterns: [],
      texturePatterns: [],
      edgePatterns: [],
      // Análise de fragmentos
      fragmentAnalysis: [],
      pieceClassification: [],
      contextualClues: [],
      visualSimilarity: []
    };
    this.sessionData = {
      startTime: null,
      endTime: null,
      totalPatternRecognitions: 0,
      correctRecognitions: 0,
      incorrectRecognitions: 0,
      averageRecognitionTime: 0,
      patternComplexityHandled: 0,
      recognitionAccuracy: 0,
      scanningEfficiency: 0,
      visualProcessingSpeed: 0
    };
  }
  // ========================================================================
  // MÉTODOS DE COLETA
  // ========================================================================
  async collect(gameData) {
    try {
      const patternData = {
        colorRecognition: this.analyzeColorRecognition(gameData),
        shapeRecognition: this.analyzeShapeRecognition(gameData),
        patternMatching: this.analyzePatternMatching(gameData),
        scanningEfficiency: this.calculateScanningEfficiency(gameData)
      };
      return {
        timestamp: Date.now(),
        gameType: "QuebraCabeca",
        patternData,
        score: this.calculateOverallScore(patternData)
      };
    } catch (error) {
      console.error("Erro na coleta de dados de reconhecimento de padrões:", error);
      return {
        timestamp: Date.now(),
        gameType: "QuebraCabeca",
        error: error.message,
        score: 0.5
      };
    }
  }
  analyzeColorRecognition(gameData) {
    return {
      colorAccuracy: 0.8,
      colorDiscrimination: 0.75,
      colorMatching: 0.85,
      colorMemory: 0.7
    };
  }
  analyzeShapeRecognition(gameData) {
    return {
      shapeAccuracy: 0.78,
      edgeRecognition: 0.82,
      cornerDetection: 0.75,
      shapeClassification: 0.8
    };
  }
  analyzePatternMatching(gameData) {
    return {
      matchingAccuracy: 0.77,
      patternComplexity: 0.6,
      visualSimilarity: 0.73,
      contextualClues: 0.68
    };
  }
  calculateScanningEfficiency(gameData) {
    return 0.74;
  }
  calculateOverallScore(patternData) {
    const scores = [
      patternData.colorRecognition.colorAccuracy,
      patternData.shapeRecognition.shapeAccuracy,
      patternData.patternMatching.matchingAccuracy,
      patternData.scanningEfficiency
    ];
    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  }
  // ========================================================================
  // MÉTODOS DE ANÁLISE
  // ========================================================================
  generateReport() {
    return {
      colorPatterns: this.patternMetrics.colorPatterns,
      shapePatterns: this.patternMetrics.shapePatterns,
      scanningEfficiency: this.sessionData.scanningEfficiency,
      recognitionAccuracy: this.sessionData.recognitionAccuracy,
      recommendations: this.generateRecommendations()
    };
  }
  generateRecommendations() {
    return [
      "Praticar exercícios de reconhecimento de padrões visuais",
      "Desenvolver habilidades de varredura visual sistemática",
      "Fortalecer discriminação de cores e formas"
    ];
  }
  getActivityScore() {
    return Math.round(this.sessionData.recognitionAccuracy * 1e3);
  }
}
class MemoryCollector extends BaseCollector {
  constructor() {
    super("Memory");
    this.memoryMetrics = {
      // Memória visual
      visualMemory: [],
      imageRecall: [],
      visualRecognition: [],
      visualRetention: [],
      // Memória espacial
      spatialMemory: [],
      locationRecall: [],
      spatialRelationships: [],
      spatialMapping: [],
      // Memória de trabalho
      workingMemory: [],
      temporaryStorage: [],
      informationManipulation: [],
      memoryUpdating: [],
      // Memória a longo prazo
      longTermMemory: [],
      patternStorage: [],
      strategicMemory: [],
      proceduralMemory: []
    };
    this.sessionData = {
      startTime: null,
      endTime: null,
      totalMemoryOperations: 0,
      successfulRecalls: 0,
      failedRecalls: 0,
      averageRecallTime: 0,
      memoryCapacityUsed: 0,
      memoryEfficiency: 0,
      interferenceLevels: 0,
      forgettingRate: 0
    };
  }
  // ========================================================================
  // MÉTODOS DE COLETA
  // ========================================================================
  async collect(gameData) {
    try {
      const memoryData = {
        visualMemory: this.analyzeVisualMemory(gameData),
        spatialMemory: this.analyzeSpatialMemory(gameData),
        workingMemory: this.analyzeWorkingMemory(gameData),
        memoryEfficiency: this.calculateMemoryEfficiency(gameData)
      };
      return {
        timestamp: Date.now(),
        gameType: "QuebraCabeca",
        memoryData,
        score: this.calculateOverallScore(memoryData)
      };
    } catch (error) {
      console.error("Erro na coleta de dados de memória:", error);
      return {
        timestamp: Date.now(),
        gameType: "QuebraCabeca",
        error: error.message,
        score: 0.5
      };
    }
  }
  analyzeVisualMemory(gameData) {
    return {
      visualRecall: 0.7,
      imageRecognition: 0.6,
      visualRetention: 0.8,
      patternMemory: 0.65
    };
  }
  analyzeSpatialMemory(gameData) {
    return {
      locationMemory: 0.75,
      spatialRelations: 0.7,
      spatialMapping: 0.6,
      spatialSpan: 5
    };
  }
  analyzeWorkingMemory(gameData) {
    return {
      capacity: 4,
      efficiency: 0.7,
      manipulation: 0.6,
      updating: 0.65
    };
  }
  calculateMemoryEfficiency(gameData) {
    return 0.7;
  }
  calculateOverallScore(memoryData) {
    const scores = [
      memoryData.visualMemory.visualRecall,
      memoryData.spatialMemory.locationMemory,
      memoryData.workingMemory.efficiency,
      memoryData.memoryEfficiency
    ];
    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  }
  // ========================================================================
  // MÉTODOS DE ANÁLISE
  // ========================================================================
  generateReport() {
    return {
      visualMemory: this.memoryMetrics.visualMemory,
      spatialMemory: this.memoryMetrics.spatialMemory,
      workingMemory: this.memoryMetrics.workingMemory,
      efficiency: this.sessionData.memoryEfficiency,
      recommendations: this.generateRecommendations()
    };
  }
  generateRecommendations() {
    return [
      "Praticar exercícios de memória visual",
      "Desenvolver memória espacial com jogos de localização",
      "Fortalecer memória de trabalho com tarefas duplas"
    ];
  }
  getActivityScore() {
    return Math.round(this.sessionData.memoryEfficiency * 1e3);
  }
}
class PerceptualProcessingCollector extends BaseCollector {
  constructor() {
    super("PerceptualProcessing");
    this.perceptualMetrics = {
      // Processamento visual básico
      visualAcuity: [],
      contrastSensitivity: [],
      colorDiscrimination: [],
      motionDetection: [],
      // Processamento de formas e objetos
      shapeProcessing: [],
      objectRecognition: [],
      figureGroundSeparation: [],
      visualClosure: []
    };
    this.sessionData = {
      startTime: null,
      endTime: null,
      totalPerceptualOperations: 0,
      averageProcessingTime: 0,
      perceptualAccuracy: 0,
      visualEfficiency: 0,
      processingSpeed: 0,
      integrationScore: 0,
      perceptualLoad: 0
    };
  }
  // ========================================================================
  // MÉTODOS DE COLETA
  // ========================================================================
  async collect(gameData) {
    try {
      const perceptualData = {
        visualProcessing: this.analyzeVisualProcessing(gameData),
        objectRecognition: this.analyzeObjectRecognition(gameData),
        perceptualIntegration: this.analyzePerceptualIntegration(gameData),
        processingEfficiency: this.calculateProcessingEfficiency(gameData)
      };
      return {
        timestamp: Date.now(),
        gameType: "QuebraCabeca",
        perceptualData,
        score: this.calculateOverallScore(perceptualData)
      };
    } catch (error) {
      console.error("Erro na coleta de dados de processamento perceptual:", error);
      return {
        timestamp: Date.now(),
        gameType: "QuebraCabeca",
        error: error.message,
        score: 0.5
      };
    }
  }
  analyzeVisualProcessing(gameData) {
    return {
      visualAcuity: 0.82,
      contrastSensitivity: 0.78,
      colorDiscrimination: 0.85,
      motionDetection: 0.73
    };
  }
  analyzeObjectRecognition(gameData) {
    return {
      objectRecognition: 0.79,
      shapeProcessing: 0.77,
      figureGroundSeparation: 0.74,
      visualClosure: 0.81
    };
  }
  analyzePerceptualIntegration(gameData) {
    return {
      featureIntegration: 0.76,
      perceptualBinding: 0.72,
      contextualProcessing: 0.78,
      gestaltProcessing: 0.75
    };
  }
  calculateProcessingEfficiency(gameData) {
    return 0.77;
  }
  calculateOverallScore(perceptualData) {
    const scores = [
      perceptualData.visualProcessing.visualAcuity,
      perceptualData.objectRecognition.objectRecognition,
      perceptualData.perceptualIntegration.featureIntegration,
      perceptualData.processingEfficiency
    ];
    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  }
  // ========================================================================
  // MÉTODOS DE ANÁLISE
  // ========================================================================
  generateReport() {
    return {
      visualAcuity: this.perceptualMetrics.visualAcuity,
      objectRecognition: this.perceptualMetrics.objectRecognition,
      processingEfficiency: this.sessionData.visualEfficiency,
      integrationScore: this.sessionData.integrationScore,
      recommendations: this.generateRecommendations()
    };
  }
  generateRecommendations() {
    return [
      "Praticar exercícios de processamento visual",
      "Desenvolver habilidades de reconhecimento de objetos",
      "Fortalecer integração perceptual com atividades específicas"
    ];
  }
  getActivityScore() {
    return Math.round(this.sessionData.visualEfficiency * 1e3);
  }
}
class QuebraCabecaCollectorsHub {
  constructor() {
    this.hubId = "quebra-cabeca-collectors-hub";
    this.version = "3.0.0";
    this.gameType = "QuebraCabeca";
    this._collectors = {
      spatialReasoning: new SpatialReasoningCollector(),
      problemSolving: new ProblemSolvingCollector(),
      visualSpatial: new VisualSpatialCollector(),
      motorSkills: new MotorSkillsCollector(),
      patternRecognition: new PatternRecognitionCollector(),
      memory: new MemoryCollector(),
      perceptualProcessing: new PerceptualProcessingCollector()
    };
    this.sessionData = [];
    this.analysisCache = /* @__PURE__ */ new Map();
    this.isInitialized = true;
    console.log("🧩 QuebraCabecaCollectorsHub inicializado v3.0.0");
  }
  /**
   * Getter para coletores - necessário para GameSpecificProcessors
   */
  get collectors() {
    return this._collectors;
  }
  /**
   * Executa análise completa dos dados do quebra-cabeça
   */
  async runCompleteAnalysis(gameData) {
    try {
      const timestamp = Date.now();
      const sessionId = gameData.sessionId || `session_${timestamp}`;
      const spatialAnalysis = await this.collectors.spatialReasoning.collect(gameData);
      const problemSolvingAnalysis = await this.collectors.problemSolving.collect(gameData);
      const visualSpatialAnalysis = await this.collectors.visualSpatial.collect(gameData);
      const motorSkillsAnalysis = await this.collectors.motorSkills.collect(gameData);
      const patternRecognitionAnalysis = await this.collectors.patternRecognition.collect(gameData);
      const memoryAnalysis = await this.collectors.memory.collect(gameData);
      const perceptualProcessingAnalysis = await this.collectors.perceptualProcessing.collect(gameData);
      const completeAnalysis = {
        timestamp,
        sessionId,
        gameType: "QuebraCabeca",
        spatialReasoning: spatialAnalysis,
        problemSolving: problemSolvingAnalysis,
        visualSpatial: visualSpatialAnalysis,
        motorSkills: motorSkillsAnalysis,
        patternRecognition: patternRecognitionAnalysis,
        memory: memoryAnalysis,
        perceptualProcessing: perceptualProcessingAnalysis,
        overallPerformance: this.calculateOverallPerformance({
          spatialAnalysis,
          problemSolvingAnalysis,
          visualSpatialAnalysis,
          motorSkillsAnalysis,
          patternRecognitionAnalysis,
          memoryAnalysis,
          perceptualProcessingAnalysis
        }),
        insights: this.generateInsights({
          spatialAnalysis,
          problemSolvingAnalysis,
          visualSpatialAnalysis,
          motorSkillsAnalysis,
          patternRecognitionAnalysis,
          memoryAnalysis,
          perceptualProcessingAnalysis
        }),
        recommendations: this.generateRecommendations({
          spatialAnalysis,
          problemSolvingAnalysis,
          visualSpatialAnalysis,
          motorSkillsAnalysis,
          patternRecognitionAnalysis,
          memoryAnalysis,
          perceptualProcessingAnalysis
        })
      };
      this.sessionData.push(completeAnalysis);
      return completeAnalysis;
    } catch (error) {
      console.error("🧩 Erro na análise completa do QuebraCabeca:", error);
      return {
        error: error.message,
        timestamp: Date.now(),
        gameType: "QuebraCabeca"
      };
    }
  }
  /**
   * Calcula desempenho geral
   */
  calculateOverallPerformance(analyses) {
    try {
      const scores = Object.values(analyses).map((analysis) => analysis.score || 0.5);
      const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
      return {
        score: averageScore,
        level: averageScore > 0.8 ? "Avançado" : averageScore > 0.6 ? "Intermediário" : "Básico",
        strengths: this.identifyStrengths(analyses),
        challenges: this.identifyChallenges(analyses)
      };
    } catch (error) {
      console.error("🧩 Erro ao calcular desempenho geral:", error);
      return { score: 0.5, level: "Básico", strengths: [], challenges: [] };
    }
  }
  /**
   * Identifica pontos fortes
   */
  identifyStrengths(analyses) {
    const strengths = [];
    if (analyses.spatialAnalysis?.score > 0.7) strengths.push("Raciocínio espacial forte");
    if (analyses.problemSolvingAnalysis?.score > 0.7) strengths.push("Boa resolução de problemas");
    if (analyses.visualSpatialAnalysis?.score > 0.7) strengths.push("Processamento visual-espacial eficiente");
    if (analyses.motorSkillsAnalysis?.score > 0.7) strengths.push("Habilidades motoras finas desenvolvidas");
    if (analyses.patternRecognitionAnalysis?.score > 0.7) strengths.push("Excelente reconhecimento de padrões");
    if (analyses.memoryAnalysis?.score > 0.7) strengths.push("Memória visual e espacial eficaz");
    if (analyses.perceptualProcessingAnalysis?.score > 0.7) strengths.push("Processamento perceptual avançado");
    return strengths;
  }
  /**
   * Identifica desafios
   */
  identifyChallenges(analyses) {
    const challenges = [];
    if (analyses.spatialAnalysis?.score < 0.5) challenges.push("Raciocínio espacial precisa de desenvolvimento");
    if (analyses.problemSolvingAnalysis?.score < 0.5) challenges.push("Resolução de problemas requer prática");
    if (analyses.visualSpatialAnalysis?.score < 0.5) challenges.push("Processamento visual-espacial precisa de suporte");
    if (analyses.motorSkillsAnalysis?.score < 0.5) challenges.push("Habilidades motoras finas necessitam treino");
    if (analyses.patternRecognitionAnalysis?.score < 0.5) challenges.push("Reconhecimento de padrões precisa ser fortalecido");
    if (analyses.memoryAnalysis?.score < 0.5) challenges.push("Memória visual e espacial requer desenvolvimento");
    if (analyses.perceptualProcessingAnalysis?.score < 0.5) challenges.push("Processamento perceptual necessita aprimoramento");
    return challenges;
  }
  /**
   * Gera insights terapêuticos
   */
  generateInsights(analyses) {
    const insights = [
      "Quebra-cabeça desenvolve raciocínio espacial e resolução de problemas",
      "Atividade estimula processamento visual-espacial e memória",
      "Jogos de puzzle fortalecem persistência e paciência",
      "Manipulação de peças desenvolve habilidades motoras finas",
      "Reconhecimento de padrões aprimora processamento visual",
      "Integração perceptual é exercitada através da montagem",
      "Memória de trabalho é constantemente desafiada"
    ];
    return insights;
  }
  /**
   * Gera recomendações
   */
  generateRecommendations(analyses) {
    const recommendations = [];
    if (analyses.spatialAnalysis?.score < 0.6) {
      recommendations.push("Praticar com quebra-cabeças mais simples primeiro");
    }
    if (analyses.problemSolvingAnalysis?.score > 0.8) {
      recommendations.push("Aumentar complexidade dos quebra-cabeças");
    }
    if (analyses.motorSkillsAnalysis?.score < 0.6) {
      recommendations.push("Incluir exercícios de coordenação motora fina");
    }
    if (analyses.patternRecognitionAnalysis?.score < 0.6) {
      recommendations.push("Praticar jogos de reconhecimento de padrões");
    }
    if (analyses.memoryAnalysis?.score < 0.6) {
      recommendations.push("Exercitar memória visual com atividades específicas");
    }
    if (analyses.perceptualProcessingAnalysis?.score < 0.6) {
      recommendations.push("Desenvolver processamento perceptual com exercícios visuais");
    }
    recommendations.push("Combinar quebra-cabeças com atividades de construção");
    return recommendations;
  }
}
class QuebraCabecaProcessors extends IGameProcessor {
  constructor(config = {}) {
    const defaultConfig = {
      category: "spatial-reasoning",
      therapeuticFocus: ["spatial_reasoning", "problem_solving", "fine_motor_skills"],
      cognitiveAreas: ["spatial_processing", "executive_function", "motor_planning"],
      thresholds: {
        accuracy: 60,
        responseTime: 8e3,
        engagement: 75
      },
      ...config
    };
    super(defaultConfig);
    this.logger = config.logger && typeof config.logger.therapeutic === "function" ? config.logger : this.logger || {
      info: (...args) => console.info("🧩 [QUEBRA-CABECA]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
      error: (...args) => console.error("🔴 [QUEBRA-ERROR]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
      warn: (...args) => console.warn("🟡 [QUEBRA-WARN]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
      debug: (...args) => console.debug("⚪ [QUEBRA-DEBUG]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
      therapeutic: (...args) => console.info("🏥 [QUEBRA-THERAPEUTIC]", (/* @__PURE__ */ new Date()).toISOString(), ...args)
    };
    this.logger.info("🧩 Processadores Quebra-Cabeça inicializados");
  }
  /**
   * Processa métricas específicas do QuebraCabeca
   * @param {Object} gameData - Dados do jogo QuebraCabeca
   * @param {Object} sessionData - Dados da sessão
   * @returns {Object} Métricas processadas
   */
  async processQuebraCabecaMetrics(gameData, sessionData) {
    try {
      this.logger?.info("🧩 Processando métricas QuebraCabeca...", {
        sessionId: sessionData.sessionId
      });
      const { attempts = [], totalTime = 0, completed = false, pieces = [] } = gameData;
      const totalAttempts = attempts.length || 1;
      const correctPlacements = attempts.filter((a) => a.correct).length;
      const accuracy = Math.round(correctPlacements / totalAttempts * 100);
      const spatialReasoning = this.calculateSpatialReasoning(attempts, pieces);
      const problemSolving = this.calculateProblemSolving(attempts, completed);
      const persistence = this.calculatePersistence(attempts, totalTime);
      const visualProcessing = this.calculateVisualProcessing(attempts, pieces);
      const motorSkills = this.calculateMotorSkills(attempts);
      const metrics = {
        // Métricas básicas
        accuracy,
        totalAttempts,
        correctPlacements,
        completionTime: totalTime,
        completed,
        // Métricas específicas
        spatialReasoning,
        problemSolving,
        persistence,
        visualProcessing,
        motorSkills,
        // Métricas derivadas
        efficiency: this.calculateEfficiency(correctPlacements, totalTime),
        strategy: this.identifyStrategy(attempts),
        difficultyHandling: this.assessDifficultyHandling(attempts, pieces)
      };
      this.logger?.info("✅ Métricas QuebraCabeca processadas", {
        accuracy,
        totalAttempts,
        spatialReasoning: spatialReasoning.score
      });
      return metrics;
    } catch (error) {
      this.logger?.error("❌ Erro ao processar métricas QuebraCabeca:", error);
      return this.generateFallbackMetrics(gameData);
    }
  }
  /**
   * Processa dados do jogo Quebra-Cabeça
   * @param {Object} gameData - Dados coletados do jogo
   * @param {Object} collectorsHub - Hub de coletores específico do jogo
   * @returns {Promise<Object>} Análise terapêutica específica
   */
  async processGameData(gameData, collectorsHub = null) {
    try {
      this.logger?.info("🎮 Processando dados QuebraCabeca", {
        sessionId: gameData.sessionId,
        userId: gameData.userId,
        collectorsActive: collectorsHub ? Object.keys(collectorsHub.collectors || {}).length : 0
      });
      const metrics = await this.processQuebraCabecaMetrics(gameData, gameData);
      const therapeuticAnalysis = this.generateTherapeuticAnalysis(metrics, gameData);
      const processedMetrics = this.processMetricsForDatabase(metrics, gameData);
      return {
        success: true,
        gameType: this.gameType,
        metrics,
        therapeuticAnalysis,
        processedMetrics,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    } catch (error) {
      this.logger?.error("❌ Erro ao processar dados QuebraCabeca:", error);
      return {
        success: false,
        gameType: this.gameType,
        error: error.message,
        fallbackAnalysis: this.generateFallbackTherapeuticAnalysis(gameData),
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    }
  }
  /**
   * Processa coletores com Circuit Breaker para resiliência
   * @param {Object} collectorsHub - Hub de coletores
   * @param {Object} gameData - Dados do jogo
   * @returns {Promise<Object>} Resultados dos coletores
   */
  async processCollectorsWithCircuitBreaker(collectorsHub, gameData) {
    if (!collectorsHub || !collectorsHub.collectors) {
      this.logger?.warn("⚠️ QuebraCabeca: Hub de coletores não disponível");
      return { collectors: {}, warning: "No collectors available" };
    }
    const results = {};
    const collectors = collectorsHub.collectors;
    for (const [collectorName, collector] of Object.entries(collectors)) {
      try {
        if (collector && typeof collector.analyze === "function") {
          this.logger?.debug("🎮 Processando coletor: " + collectorName);
          results[collectorName] = await this.processWithTimeout(
            () => collector.analyze(gameData),
            5e3,
            // 5 segundos timeout
            collectorName + " timeout"
          );
        } else {
          this.logger?.warn("⚠️ Coletor " + collectorName + " não tem método analyze");
          results[collectorName] = { error: "No analyze method" };
        }
      } catch (error) {
        this.logger?.error("❌ QuebraCabecaProcessors: Erro no coletor " + collectorName + ":", {
          error: error.message,
          stack: error.stack?.substring(0, 300),
          collectorName,
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        });
        results[collectorName] = {
          error: error.message,
          fallback: this.generateFallbackMetrics(collectorName, gameData),
          recovered: true
        };
      }
    }
    return {
      collectors: results,
      processedAt: (/* @__PURE__ */ new Date()).toISOString(),
      gameType: "QuebraCabeca"
    };
  }
  /**
   * Processa com timeout para evitar travamentos
   */
  async processWithTimeout(fn, timeout, errorMsg) {
    return Promise.race([
      fn(),
      new Promise(
        (_, reject) => setTimeout(() => reject(new Error(errorMsg)), timeout)
      )
    ]);
  }
  /**
   * Gera métricas de fallback em caso de erro
   */
  generateFallbackMetrics(collectorName, gameData) {
    return {
      fallback: true,
      collector: collectorName,
      basicScore: 50,
      confidence: "low",
      note: "Generated due to collector error"
    };
  }
  /**
   * Gera análise terapêutica completa
   * @param {Object} metrics - Métricas processadas
   * @param {Object} gameData - Dados do jogo
   * @returns {Object} Análise terapêutica
   */
  generateTherapeuticAnalysis(metrics, gameData) {
    try {
      const analysis = {
        // Análise comportamental
        behavioral: {
          engagement: this.calculateEngagementScore(gameData),
          persistence: this.calculatePersistenceScore(gameData),
          adaptability: this.calculateAdaptabilityScore(gameData),
          frustrationTolerance: this.calculateFrustrationTolerance(gameData),
          socialInteraction: this.calculateSocialInteractionScore(gameData)
        },
        // Análise cognitiva
        cognitive: {
          attention: this.calculateAttentionScore(gameData),
          memory: this.calculateMemoryScore(gameData),
          processingSpeed: this.calculateProcessingSpeedScore(gameData),
          executiveFunction: this.calculateExecutiveFunctionScore(gameData),
          visualProcessing: this.calculateVisualProcessingScore(gameData)
        },
        // Análise sensorial
        sensory: {
          visualPerception: this.calculateVisualPerceptionScore(gameData),
          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),
          tactileProcessing: this.calculateTactileProcessingScore(gameData),
          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)
        },
        // Análise motora
        motor: {
          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),
          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),
          coordination: this.calculateCoordinationScore(gameData),
          motorPlanning: this.calculateMotorPlanningScore(gameData)
        },
        // Recomendações terapêuticas
        recommendations: this.generateTherapeuticRecommendations(metrics, gameData),
        // Indicadores de progresso
        progressIndicators: this.generateProgressIndicators(metrics, gameData),
        // Insights específicos do jogo
        gameSpecificInsights: this.generateGameSpecificInsights(metrics, gameData),
        // Metadados
        metadata: {
          analysisTimestamp: (/* @__PURE__ */ new Date()).toISOString(),
          gameType: this.gameType,
          analysisVersion: "3.0.0",
          confidenceScore: this.calculateAnalysisConfidenceScore(metrics, gameData)
        }
      };
      return analysis;
    } catch (error) {
      this.logger?.error("❌ Erro ao gerar análise terapêutica:", error);
      return this.generateFallbackTherapeuticAnalysis(gameData);
    }
  }
  /**
   * Métodos de cálculo de scores terapêuticos
   */
  calculateEngagementScore(gameData) {
    const interactions = gameData.interactions || [];
    const totalTime = gameData.totalTime || 1e3;
    const completionRate = gameData.completionRate || 0;
    let score = 50;
    if (interactions.length > 0) {
      score += Math.min(30, interactions.length * 2);
    }
    if (totalTime > 3e4) {
      score += 10;
    }
    score += completionRate * 10;
    return Math.max(0, Math.min(100, score));
  }
  calculatePersistenceScore(gameData) {
    const attempts = gameData.attempts || 1;
    const errors = gameData.errors || 0;
    const completion = gameData.completion || 0;
    let score = 50;
    if (attempts > 1 && completion > 0.5) {
      score += 20;
    }
    if (errors > 0 && completion > 0.8) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateAdaptabilityScore(gameData) {
    const difficultyChanges = gameData.difficultyChanges || 0;
    const adaptationSuccess = gameData.adaptationSuccess || 0;
    let score = 50;
    if (difficultyChanges > 0) {
      score += adaptationSuccess * 20;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateFrustrationTolerance(gameData) {
    const errors = gameData.errors || 0;
    const quitEarly = gameData.quitEarly || false;
    const completion = gameData.completion || 0;
    let score = 70;
    if (errors > 3 && !quitEarly) {
      score += 15;
    }
    if (completion > 0.8) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateSocialInteractionScore(gameData) {
    const engagement = this.calculateEngagementScore(gameData);
    return Math.max(30, Math.min(80, engagement * 0.8));
  }
  calculateAttentionScore(gameData) {
    const focusTime = gameData.focusTime || 0;
    const distractions = gameData.distractions || 0;
    const responseTime = gameData.averageResponseTime || 3e3;
    let score = 50;
    if (focusTime > 6e4) {
      score += 20;
    }
    if (distractions < 2) {
      score += 15;
    }
    if (responseTime < 2e3) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateMemoryScore(gameData) {
    const accuracy = gameData.accuracy || 0;
    const patterns = gameData.patterns || [];
    let score = 50;
    if (accuracy > 70) {
      score += 25;
    }
    if (patterns.length > 3) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateProcessingSpeedScore(gameData) {
    const responseTime = gameData.averageResponseTime || 3e3;
    const accuracy = gameData.accuracy || 0;
    let score = 50;
    if (responseTime < 1500 && accuracy > 60) {
      score += 30;
    } else if (responseTime < 2500) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateExecutiveFunctionScore(gameData) {
    const planningEvidence = gameData.planningEvidence || 0;
    const inhibitionControl = gameData.inhibitionControl || 0;
    const workingMemory = gameData.workingMemory || 0;
    const score = (planningEvidence + inhibitionControl + workingMemory) / 3;
    return Math.max(0, Math.min(100, score));
  }
  calculateVisualProcessingScore(gameData) {
    const visualTasks = gameData.visualTasks || 0;
    const visualAccuracy = gameData.visualAccuracy || 0;
    let score = 50;
    if (visualTasks > 5 && visualAccuracy > 70) {
      score += 25;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateVisualPerceptionScore(gameData) {
    return this.calculateVisualProcessingScore(gameData);
  }
  calculateAuditoryProcessingScore(gameData) {
    const auditoryTasks = gameData.auditoryTasks || 0;
    const auditoryAccuracy = gameData.auditoryAccuracy || 50;
    let score = 50;
    if (auditoryTasks > 0) {
      score = auditoryAccuracy;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateTactileProcessingScore(gameData) {
    const touchInteractions = gameData.touchInteractions || 0;
    const touchAccuracy = gameData.touchAccuracy || 50;
    let score = 50;
    if (touchInteractions > 3) {
      score = touchAccuracy;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateSensoryIntegrationScore(gameData) {
    const visual = this.calculateVisualPerceptionScore(gameData);
    const auditory = this.calculateAuditoryProcessingScore(gameData);
    const tactile = this.calculateTactileProcessingScore(gameData);
    return (visual + auditory + tactile) / 3;
  }
  calculateFineMotorSkillsScore(gameData) {
    const precision = gameData.precision || 50;
    const motorControl = gameData.motorControl || 50;
    return (precision + motorControl) / 2;
  }
  calculateGrossMotorSkillsScore(gameData) {
    const movements = gameData.movements || 0;
    const coordination = gameData.coordination || 50;
    let score = 50;
    if (movements > 10) {
      score = coordination;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateCoordinationScore(gameData) {
    const eyeHandCoordination = gameData.eyeHandCoordination || 50;
    const bilateralCoordination = gameData.bilateralCoordination || 50;
    return (eyeHandCoordination + bilateralCoordination) / 2;
  }
  calculateMotorPlanningScore(gameData) {
    const planningSteps = gameData.planningSteps || 0;
    const executionSuccess = gameData.executionSuccess || 0;
    let score = 50;
    if (planningSteps > 0) {
      score = executionSuccess;
    }
    return Math.max(0, Math.min(100, score));
  }
  generateTherapeuticRecommendations(metrics, gameData) {
    const recommendations = [];
    const engagement = this.calculateEngagementScore(gameData);
    if (engagement < 50) {
      recommendations.push({
        category: "engagement",
        priority: "high",
        recommendation: "Implementar estratégias de motivação e gamificação",
        rationale: "Baixo engajamento detectado"
      });
    }
    const attention = this.calculateAttentionScore(gameData);
    if (attention < 50) {
      recommendations.push({
        category: "attention",
        priority: "medium",
        recommendation: "Exercícios de foco e concentração",
        rationale: "Dificuldades atencionais identificadas"
      });
    }
    const processing = this.calculateProcessingSpeedScore(gameData);
    if (processing < 50) {
      recommendations.push({
        category: "processing",
        priority: "medium",
        recommendation: "Atividades para melhorar velocidade de processamento",
        rationale: "Processamento lento identificado"
      });
    }
    return recommendations;
  }
  generateProgressIndicators(metrics, gameData) {
    return {
      overallProgress: this.calculateOverallProgress(gameData),
      strengthAreas: this.identifyStrengthAreas(gameData),
      challengeAreas: this.identifyChallengeAreas(gameData),
      developmentGoals: this.generateDevelopmentGoals(gameData),
      milestones: this.generateMilestones(gameData)
    };
  }
  generateGameSpecificInsights(metrics, gameData) {
    return {
      gameType: this.gameType,
      specificMetrics: metrics,
      gamePerformance: this.calculateGamePerformance(gameData),
      adaptationNeeds: this.identifyAdaptationNeeds(gameData)
    };
  }
  calculateAnalysisConfidenceScore(metrics, gameData) {
    let confidence = 50;
    const dataPoints = Object.keys(gameData).length;
    if (dataPoints > 10) confidence += 20;
    else if (dataPoints > 5) confidence += 10;
    const metricsCount = Object.keys(metrics).length;
    if (metricsCount > 5) confidence += 20;
    else if (metricsCount > 3) confidence += 10;
    const sessionTime = gameData.totalTime || 0;
    if (sessionTime > 6e4) confidence += 10;
    return Math.max(0, Math.min(100, confidence));
  }
  generateFallbackTherapeuticAnalysis(gameData) {
    return {
      behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50, socialInteraction: 50 },
      cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50, visualProcessing: 50 },
      sensory: { visualPerception: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },
      motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },
      recommendations: [],
      progressIndicators: { overallProgress: 50, strengthAreas: [], challengeAreas: [], developmentGoals: [], milestones: [] },
      gameSpecificInsights: { gameType: this.gameType, specificMetrics: {}, gamePerformance: 50, adaptationNeeds: [] },
      metadata: { analysisTimestamp: (/* @__PURE__ */ new Date()).toISOString(), gameType: this.gameType, analysisVersion: "3.0.0", confidenceScore: 30 }
    };
  }
  calculateOverallProgress(gameData) {
    const accuracy = gameData.accuracy || 0;
    const completion = gameData.completion || 0;
    const engagement = this.calculateEngagementScore(gameData);
    return (accuracy + completion + engagement) / 3;
  }
  identifyStrengthAreas(gameData) {
    const strengths = [];
    if (gameData.accuracy > 80) strengths.push("Precisão");
    if (gameData.averageResponseTime < 2e3) strengths.push("Velocidade de resposta");
    if (this.calculateEngagementScore(gameData) > 70) strengths.push("Engajamento");
    return strengths;
  }
  identifyChallengeAreas(gameData) {
    const challenges = [];
    if (gameData.accuracy < 50) challenges.push("Precisão");
    if (gameData.averageResponseTime > 4e3) challenges.push("Velocidade de resposta");
    if (this.calculateEngagementScore(gameData) < 40) challenges.push("Engajamento");
    return challenges;
  }
  generateDevelopmentGoals(gameData) {
    const goals = [];
    if (gameData.accuracy < 70) {
      goals.push("Melhorar precisão para 70%+");
    }
    if (gameData.averageResponseTime > 3e3) {
      goals.push("Reduzir tempo de resposta para menos de 3 segundos");
    }
    return goals;
  }
  generateMilestones(gameData) {
    return [
      { milestone: "Primeira sessão completa", achieved: gameData.completion > 0.8 },
      { milestone: "Precisão acima de 50%", achieved: gameData.accuracy > 50 },
      { milestone: "Engajamento sustentado", achieved: this.calculateEngagementScore(gameData) > 60 }
    ];
  }
  calculateGamePerformance(gameData) {
    const accuracy = gameData.accuracy || 0;
    const completion = gameData.completion || 0;
    const efficiency = gameData.efficiency || 0;
    return (accuracy + completion + efficiency) / 3;
  }
  identifyAdaptationNeeds(gameData) {
    const needs = [];
    if (gameData.accuracy < 40) {
      needs.push("Reduzir dificuldade");
    }
    if (gameData.averageResponseTime > 5e3) {
      needs.push("Aumentar tempo limite");
    }
    if (this.calculateEngagementScore(gameData) < 30) {
      needs.push("Aumentar elementos motivacionais");
    }
    return needs;
  }
  /**
   * Processa métricas para estrutura padronizada do banco de dados
   * @param {Object} metrics - Métricas processadas
   * @param {Object} gameData - Dados do jogo
   * @returns {Object} Métricas processadas para banco
   */
  processMetricsForDatabase(metrics, gameData) {
    try {
      return {
        // Métricas básicas
        basic: {
          accuracy: gameData.accuracy || 0,
          responseTime: gameData.averageResponseTime || 0,
          completion: gameData.completion || 0,
          score: gameData.score || 0,
          duration: gameData.totalTime || 0,
          attempts: gameData.attempts || 1,
          errors: gameData.errors || 0
        },
        // Métricas cognitivas
        cognitive: {
          attention: this.calculateAttentionScore(gameData),
          memory: this.calculateMemoryScore(gameData),
          processingSpeed: this.calculateProcessingSpeedScore(gameData),
          executiveFunction: this.calculateExecutiveFunctionScore(gameData)
        },
        // Métricas comportamentais
        behavioral: {
          engagement: this.calculateEngagementScore(gameData),
          persistence: this.calculatePersistenceScore(gameData),
          adaptability: this.calculateAdaptabilityScore(gameData),
          frustrationTolerance: this.calculateFrustrationTolerance(gameData)
        },
        // Métricas sensoriais
        sensory: {
          visualProcessing: this.calculateVisualProcessingScore(gameData),
          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),
          tactileProcessing: this.calculateTactileProcessingScore(gameData),
          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)
        },
        // Métricas motoras
        motor: {
          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),
          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),
          coordination: this.calculateCoordinationScore(gameData),
          motorPlanning: this.calculateMotorPlanningScore(gameData)
        },
        // Métricas específicas do jogo
        gameSpecific: metrics,
        // Metadados
        metadata: {
          gameType: this.gameType,
          processingTimestamp: (/* @__PURE__ */ new Date()).toISOString(),
          version: "3.0.0"
        }
      };
    } catch (error) {
      this.logger?.error("❌ Erro ao processar métricas para banco:", error);
      return {
        basic: { accuracy: 0, responseTime: 0, completion: 0, score: 0, duration: 0, attempts: 1, errors: 0 },
        cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50 },
        behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50 },
        sensory: { visualProcessing: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },
        motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },
        gameSpecific: metrics,
        metadata: { gameType: this.gameType, processingTimestamp: (/* @__PURE__ */ new Date()).toISOString(), version: "3.0.0" }
      };
    }
  }
  /**
   * Métodos de análise para QuebraCabeca
   */
  analyzeQuebraCabecaPrimary(gameData) {
    const { totalCorrect = 0, totalAttempts = 1, interactions = [] } = gameData;
    return {
      accuracy: Math.round(totalCorrect / totalAttempts * 100),
      totalAttempts,
      totalCorrect,
      primaryScore: this.calculatePrimaryScore(interactions),
      efficiency: this.calculateEfficiency(interactions)
    };
  }
  analyzeQuebraCabecaSecondary(gameData) {
    const { interactions = [] } = gameData;
    return {
      secondaryAccuracy: this.calculateSecondaryAccuracy(interactions),
      adaptability: this.calculateAdaptability(interactions),
      consistency: this.calculateConsistency(interactions)
    };
  }
  analyzeQuebraCabecaTiming(gameData) {
    const { interactions = [] } = gameData;
    const responseTimes = interactions.map((i) => i.responseTime || 0).filter((t) => t > 0);
    if (responseTimes.length === 0) {
      return { average: 0, median: 0, variability: 0, pattern: "insufficient_data" };
    }
    const average = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const sorted = responseTimes.sort((a, b) => a - b);
    const median = sorted[Math.floor(sorted.length / 2)];
    return {
      average: Math.round(average),
      median: Math.round(median),
      min: Math.min(...responseTimes),
      max: Math.max(...responseTimes),
      variability: Math.round(this.calculateVariability(responseTimes)),
      pattern: "normal"
    };
  }
  analyzeQuebraCabecaPatterns(gameData) {
    const { interactions = [] } = gameData;
    return {
      totalPatterns: interactions.length,
      correctPatterns: interactions.filter((i) => i.correct).length,
      patternTypes: this.identifyPatternTypes(interactions),
      errorPatterns: this.identifyErrorPatterns(interactions)
    };
  }
  analyzeQuebraCabecaBehavior(gameData) {
    const { interactions = [] } = gameData;
    return {
      persistence: this.calculatePersistence(interactions),
      adaptability: this.calculateAdaptability(interactions),
      engagement: this.calculateEngagementScore(gameData)
    };
  }
  analyzeQuebraCabecaCognition(gameData) {
    const { interactions = [] } = gameData;
    return {
      executiveFunction: this.calculateExecutiveFunction(interactions),
      workingMemory: this.calculateWorkingMemory(interactions),
      processingSpeed: this.calculateProcessingSpeed(interactions)
    };
  }
  generateQuebraCabecaRecommendations(gameData) {
    const recommendations = [];
    const accuracy = gameData.accuracy || 0;
    const responseTime = gameData.averageResponseTime || 0;
    if (accuracy < 60) {
      recommendations.push({
        type: "accuracy_improvement",
        priority: "high",
        description: "Exercícios para melhorar precisão em QuebraCabeca"
      });
    }
    if (responseTime > 5e3) {
      recommendations.push({
        type: "speed_improvement",
        priority: "medium",
        description: "Atividades para melhorar velocidade de processamento"
      });
    }
    return recommendations;
  }
  // Métodos auxiliares
  calculatePrimaryScore(interactions) {
    const correctInteractions = interactions.filter((i) => i.correct);
    return correctInteractions.length / Math.max(1, interactions.length) * 100;
  }
  calculateSecondaryAccuracy(interactions) {
    return this.calculatePrimaryScore(interactions);
  }
  calculateEfficiency(interactions) {
    const totalTime = interactions.reduce((sum, i) => sum + (i.responseTime || 0), 0);
    const correctCount = interactions.filter((i) => i.correct).length;
    return totalTime > 0 ? correctCount / totalTime * 1e3 : 0;
  }
  identifyPatternTypes(interactions) {
    const types = {};
    interactions.forEach((i) => {
      const type = i.patternType || "unknown";
      types[type] = (types[type] || 0) + 1;
    });
    return types;
  }
  identifyErrorPatterns(interactions) {
    const errors = interactions.filter((i) => !i.correct);
    return {
      totalErrors: errors.length,
      errorFrequency: errors.length / Math.max(1, interactions.length) * 100
    };
  }
  calculateVariability(values) {
    if (values.length < 2) return 0;
    const mean = values.reduce((sum, v) => sum + v, 0) / values.length;
    const variance = values.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }
  // Métodos específicos para processQuebraCabecaMetrics
  calculateSpatialReasoning(attempts, pieces) {
    const correctPlacements = attempts.filter((a) => a.correct).length;
    const totalAttempts = attempts.length || 1;
    const accuracy = correctPlacements / totalAttempts;
    const complexityScore = pieces.length > 0 ? Math.min(pieces.length / 20, 1) : 0.5;
    const spatialScore = accuracy * 0.7 + complexityScore * 0.3;
    return {
      score: Math.round(spatialScore * 100),
      accuracy,
      complexity: complexityScore,
      level: spatialScore > 0.8 ? "advanced" : spatialScore > 0.6 ? "intermediate" : "basic"
    };
  }
  calculateProblemSolving(attempts, completed) {
    const totalAttempts = attempts.length || 1;
    const efficiency = completed ? Math.max(0, 1 - (totalAttempts - 1) / 10) : 0;
    const completionBonus = completed ? 0.3 : 0;
    const problemSolvingScore = efficiency * 0.7 + completionBonus;
    return {
      score: Math.round(problemSolvingScore * 100),
      efficiency,
      completed,
      strategy: totalAttempts < 5 ? "efficient" : totalAttempts < 10 ? "methodical" : "exploratory"
    };
  }
  calculatePersistence(attempts, totalTime) {
    const timePerAttempt = totalTime > 0 ? totalTime / attempts.length : 0;
    const persistenceIndicator = attempts.length > 5 ? 1 : attempts.length / 5;
    const timeConsistency = timePerAttempt > 1e3 ? 1 : timePerAttempt / 1e3;
    const persistenceScore = persistenceIndicator * 0.6 + timeConsistency * 0.4;
    return {
      score: Math.round(persistenceScore * 100),
      attempts: attempts.length,
      timePerAttempt,
      level: persistenceScore > 0.7 ? "high" : persistenceScore > 0.4 ? "moderate" : "low"
    };
  }
  calculateVisualProcessing(attempts, pieces) {
    const visualComplexity = pieces.length > 0 ? pieces.length / 50 : 0.5;
    const processingAccuracy = attempts.length > 0 ? attempts.filter((a) => a.correct).length / attempts.length : 0;
    const visualScore = processingAccuracy * 0.8 + visualComplexity * 0.2;
    return {
      score: Math.round(visualScore * 100),
      accuracy: processingAccuracy,
      complexity: visualComplexity,
      processing: visualScore > 0.7 ? "efficient" : "developing"
    };
  }
  calculateMotorSkills(attempts) {
    const avgResponseTime = attempts.length > 0 ? attempts.reduce((sum, a) => sum + (a.responseTime || 2e3), 0) / attempts.length : 2e3;
    const motorEfficiency = Math.max(0, 1 - (avgResponseTime - 1e3) / 5e3);
    const precision = attempts.length > 0 ? attempts.filter((a) => a.correct).length / attempts.length : 0;
    const motorScore = motorEfficiency * 0.6 + precision * 0.4;
    return {
      score: Math.round(motorScore * 100),
      responseTime: avgResponseTime,
      precision,
      level: motorScore > 0.7 ? "refined" : motorScore > 0.4 ? "developing" : "emerging"
    };
  }
  identifyStrategy(attempts) {
    if (attempts.length < 3) return "insufficient_data";
    const errorRate = attempts.filter((a) => !a.correct).length / attempts.length;
    const avgTime = attempts.reduce((sum, a) => sum + (a.responseTime || 2e3), 0) / attempts.length;
    if (errorRate < 0.2 && avgTime < 3e3) return "systematic";
    if (errorRate < 0.4 && avgTime > 4e3) return "careful";
    if (errorRate > 0.6) return "trial_and_error";
    return "adaptive";
  }
  assessDifficultyHandling(attempts, pieces) {
    const complexity = pieces.length || 10;
    const performance = attempts.length > 0 ? attempts.filter((a) => a.correct).length / attempts.length : 0;
    const difficultyScore = performance * (1 + complexity / 50);
    return {
      score: Math.round(Math.min(100, difficultyScore * 100)),
      complexity,
      performance,
      handling: difficultyScore > 0.8 ? "excellent" : difficultyScore > 0.6 ? "good" : "needs_support"
    };
  }
}
const QuebraCabecaV3Config = {
  // 🎯 CONFIGURAÇÃO DAS 6 ATIVIDADES V3
  ACTIVITY_CONFIG: {
    FREE_ASSEMBLY: {
      id: "free_assembly",
      name: "Montagem Livre",
      icon: "🧩",
      description: "Monte quebra-cabeças emocionais livremente",
      therapeuticFocus: ["motor_coordination", "spatial_planning", "problem_solving"],
      cognitiveAreas: ["spatial_processing", "motor_skills", "executive_function"],
      difficulties: {
        easy: {
          pieces: 3,
          gridSize: "1fr 1fr 1fr",
          timeLimit: 3e5,
          helpLevel: "high",
          distractors: 2
        },
        medium: {
          pieces: 6,
          gridSize: "1fr 1fr 1fr",
          timeLimit: 45e4,
          helpLevel: "medium",
          distractors: 4
        },
        hard: {
          pieces: 9,
          gridSize: "1fr 1fr 1fr",
          timeLimit: 6e5,
          helpLevel: "low",
          distractors: 6
        }
      },
      metrics: ["completion_time", "piece_placement_accuracy", "strategy_efficiency", "error_patterns"]
    },
    GUIDED_ASSEMBLY: {
      id: "guided_assembly",
      name: "Montagem Guiada",
      icon: "🎯",
      description: "Siga instruções específicas para montar",
      therapeuticFocus: ["instruction_following", "working_memory", "sequential_processing"],
      cognitiveAreas: ["auditory_processing", "memory", "attention"],
      difficulties: {
        easy: {
          sequenceLength: 3,
          guidanceType: "visual",
          pauseBetweenSteps: 3e3,
          allowRepeats: true
        },
        medium: {
          sequenceLength: 5,
          guidanceType: "visual_audio",
          pauseBetweenSteps: 2e3,
          allowRepeats: true
        },
        hard: {
          sequenceLength: 7,
          guidanceType: "audio_only",
          pauseBetweenSteps: 1500,
          allowRepeats: false
        }
      },
      metrics: ["instruction_adherence", "sequence_memory", "guidance_dependency", "completion_accuracy"]
    },
    ROTATION_RECONSTRUCTION: {
      id: "rotation_reconstruction",
      name: "Reconstrução por Rotação",
      icon: "🔄",
      description: "Monte peças que aparecem rotacionadas",
      therapeuticFocus: ["spatial_transformation", "mental_rotation", "visual_processing"],
      cognitiveAreas: ["spatial_reasoning", "visual_perception", "cognitive_flexibility"],
      difficulties: {
        easy: {
          rotationAngles: [90, 180],
          pieceCount: 4,
          rotationPreview: true,
          timePerPiece: 15e3
        },
        medium: {
          rotationAngles: [90, 180, 270],
          pieceCount: 6,
          rotationPreview: false,
          timePerPiece: 12e3
        },
        hard: {
          rotationAngles: [45, 90, 135, 180, 225, 270, 315],
          pieceCount: 8,
          rotationPreview: false,
          timePerPiece: 1e4
        }
      },
      metrics: ["rotation_accuracy", "mental_rotation_speed", "spatial_transformation_ability", "error_recovery"]
    },
    PIECE_CLASSIFICATION: {
      id: "piece_classification",
      name: "Classificação de Peças",
      icon: "🎨",
      description: "Organize peças por categorias",
      therapeuticFocus: ["categorization", "visual_discrimination", "organizational_skills"],
      cognitiveAreas: ["executive_function", "visual_processing", "conceptual_thinking"],
      difficulties: {
        easy: {
          categories: 2,
          piecesPerCategory: 3,
          categoryHints: true,
          sortingCriteria: ["color", "emotion"]
        },
        medium: {
          categories: 3,
          piecesPerCategory: 4,
          categoryHints: false,
          sortingCriteria: ["emotion", "context", "symbol"]
        },
        hard: {
          categories: 4,
          piecesPerCategory: 5,
          categoryHints: false,
          sortingCriteria: ["emotion", "context", "symbol", "theme"]
        }
      },
      metrics: ["classification_accuracy", "sorting_strategy", "category_consistency", "processing_speed"]
    },
    PATTERN_IDENTIFICATION: {
      id: "pattern_identification",
      name: "Identificação de Padrões",
      icon: "🔍",
      description: "Identifique padrões nos quebra-cabeças",
      therapeuticFocus: ["pattern_recognition", "logical_reasoning", "predictive_thinking"],
      cognitiveAreas: ["pattern_processing", "logical_thinking", "predictive_analysis"],
      difficulties: {
        easy: {
          patternLength: 4,
          patternTypes: ["alternating", "sequential"],
          hintLevel: "high",
          completionOptions: 2
        },
        medium: {
          patternLength: 6,
          patternTypes: ["alternating", "sequential", "progressive"],
          hintLevel: "medium",
          completionOptions: 3
        },
        hard: {
          patternLength: 8,
          patternTypes: ["alternating", "sequential", "progressive", "complex"],
          hintLevel: "low",
          completionOptions: 4
        }
      },
      metrics: ["pattern_recognition_speed", "logical_accuracy", "prediction_success", "learning_progression"]
    },
    COLLABORATIVE_SOLVING: {
      id: "collaborative_solving",
      name: "Resolução Colaborativa",
      icon: "🧠",
      description: "Resolva em equipe com outros jogadores",
      therapeuticFocus: ["social_cooperation", "communication", "shared_problem_solving"],
      cognitiveAreas: ["social_cognition", "communication_skills", "collaborative_thinking"],
      difficulties: {
        easy: {
          teamSize: 2,
          communicationLevel: "high",
          sharedPieces: 4,
          coordinationRequired: "low"
        },
        medium: {
          teamSize: 3,
          communicationLevel: "medium",
          sharedPieces: 6,
          coordinationRequired: "medium"
        },
        hard: {
          teamSize: 4,
          communicationLevel: "minimal",
          sharedPieces: 8,
          coordinationRequired: "high"
        }
      },
      metrics: ["cooperation_index", "communication_effectiveness", "shared_goal_achievement", "leadership_emergence"]
    }
  },
  // 🎭 BIBLIOTECA EXPANDIDA DE EMOÇÕES V3
  EMOTIONS_LIBRARY: {
    basic: [
      {
        id: "happy",
        name: "Feliz",
        emoji: "😊",
        pieces: ["😊", "🌞", "🎁", "🎉"],
        context: "Ganhar um presente",
        color: "#FFD93D",
        complexity: "basic",
        therapeuticValue: "positive_emotion_recognition"
      },
      {
        id: "sad",
        name: "Triste",
        emoji: "😢",
        pieces: ["😢", "🌧️", "💔", "😔"],
        context: "Perder um brinquedo",
        color: "#6BB6FF",
        complexity: "basic",
        therapeuticValue: "emotional_regulation"
      },
      {
        id: "surprised",
        name: "Surpreso",
        emoji: "😲",
        pieces: ["😲", "🎉", "❓", "✨"],
        context: "Ver algo inesperado",
        color: "#FF6B6B",
        complexity: "basic",
        therapeuticValue: "reaction_processing"
      },
      {
        id: "angry",
        name: "Bravo",
        emoji: "😠",
        pieces: ["😠", "💥", "🌋", "⚡"],
        context: "Quando algo não sai como esperado",
        color: "#FF8B94",
        complexity: "basic",
        therapeuticValue: "anger_management"
      },
      {
        id: "calm",
        name: "Calmo",
        emoji: "😌",
        pieces: ["😌", "🌊", "🕊️", "🌿"],
        context: "Relaxar na natureza",
        color: "#4ECDC4",
        complexity: "basic",
        therapeuticValue: "self_regulation"
      },
      {
        id: "excited",
        name: "Animado",
        emoji: "🤩",
        pieces: ["🤩", "🎪", "🎢", "🎊"],
        context: "Ir ao parque de diversões",
        color: "#A8E6CF",
        complexity: "basic",
        therapeuticValue: "positive_anticipation"
      }
    ],
    intermediate: [
      {
        id: "confused",
        name: "Confuso",
        emoji: "😕",
        pieces: ["😕", "❓", "🤔", "💭"],
        context: "Não entender algo",
        color: "#F4A261",
        complexity: "intermediate",
        therapeuticValue: "uncertainty_tolerance"
      },
      {
        id: "proud",
        name: "Orgulhoso",
        emoji: "😤",
        pieces: ["😤", "🏆", "⭐", "👑"],
        context: "Conseguir algo difícil",
        color: "#E76F51",
        complexity: "intermediate",
        therapeuticValue: "self_esteem"
      },
      {
        id: "worried",
        name: "Preocupado",
        emoji: "😰",
        pieces: ["😰", "☁️", "⚠️", "💭"],
        context: "Pensar em problemas",
        color: "#264653",
        complexity: "intermediate",
        therapeuticValue: "anxiety_awareness"
      },
      {
        id: "love",
        name: "Amor",
        emoji: "🥰",
        pieces: ["🥰", "💖", "🌹", "✨"],
        context: "Estar com pessoas queridas",
        color: "#E9C46A",
        complexity: "intermediate",
        therapeuticValue: "attachment_recognition"
      }
    ],
    advanced: [
      {
        id: "jealous",
        name: "Ciúmes",
        emoji: "😒",
        pieces: ["😒", "👀", "💚", "⚡"],
        context: "Ver outros com algo que queremos",
        color: "#2A9D8F",
        complexity: "advanced",
        therapeuticValue: "complex_emotion_understanding"
      },
      {
        id: "embarrassed",
        name: "Envergonhado",
        emoji: "😳",
        pieces: ["😳", "🔥", "👥", "🙈"],
        context: "Fazer algo constrangedor",
        color: "#F4A261",
        complexity: "advanced",
        therapeuticValue: "social_awareness"
      },
      {
        id: "disappointed",
        name: "Desapontado",
        emoji: "😞",
        pieces: ["😞", "💔", "😔", "🌧️"],
        context: "Expectativas não atendidas",
        color: "#264653",
        complexity: "advanced",
        therapeuticValue: "expectation_management"
      }
    ]
  },
  // ⚙️ CONFIGURAÇÕES DE DIFICULDADE V3
  DIFFICULTY_CONFIGS: {
    EASY: {
      name: "Fácil",
      description: "Ideal para iniciantes - Mais ajuda e tempo",
      color: "#4CAF50",
      icon: "🟢",
      emotionComplexity: "basic",
      roundsPerActivity: 2,
      timeMultiplier: 1.5,
      helpLevel: "high",
      feedbackFrequency: "high"
    },
    MEDIUM: {
      name: "Médio",
      description: "Desafio equilibrado - Ajuda moderada",
      color: "#FF9800",
      icon: "🟡",
      emotionComplexity: "intermediate",
      roundsPerActivity: 3,
      timeMultiplier: 1,
      helpLevel: "medium",
      feedbackFrequency: "medium"
    },
    HARD: {
      name: "Difícil",
      description: "Para especialistas - Mínima assistência",
      color: "#F44336",
      icon: "🔴",
      emotionComplexity: "advanced",
      roundsPerActivity: 4,
      timeMultiplier: 0.8,
      helpLevel: "low",
      feedbackFrequency: "low"
    }
  },
  // 🎯 CONFIGURAÇÕES DO JOGO V3
  GAME_SETTINGS: {
    pointsByDifficulty: {
      EASY: { base: 10, bonus: 5, time_bonus: 3 },
      MEDIUM: { base: 15, bonus: 8, time_bonus: 5 },
      HARD: { base: 20, bonus: 12, time_bonus: 8 }
    }
  }
};
const QuebraCabecaConfig = {
  // Manter referências da versão anterior para compatibilidade
  emotions: QuebraCabecaV3Config.EMOTIONS_LIBRARY.basic,
  difficulties: [
    { id: "easy", name: "Fácil", pieces: 3, gridSize: 3 },
    { id: "medium", name: "Médio", pieces: 6, gridSize: 4 },
    { id: "hard", name: "Difícil", pieces: 9, gridSize: 4 }
  ],
  encouragingMessages: [
    "Muito bem! Você reconheceu a emoção! 🌟",
    "Excelente! Continue assim! 🎉",
    "Você está ótimo em montar emoções! 😊",
    "Perfeito! Sua paciência é incrível! ✨",
    "Fantástico! Você entende as emoções! 🧠"
  ],
  gameSettings: QuebraCabecaV3Config.GAME_SETTINGS
};
const quebraCabecaGame = "_quebraCabecaGame_hipsz_41";
const gameContent = "_gameContent_hipsz_63";
const gameHeader = "_gameHeader_hipsz_83";
const gameTitle = "_gameTitle_hipsz_111";
const headerTtsButton = "_headerTtsButton_hipsz_157";
const ttsActive = "_ttsActive_hipsz_213";
const puzzleGrid = "_puzzleGrid_hipsz_345";
const puzzleSlot = "_puzzleSlot_hipsz_375";
const empty = "_empty_hipsz_407";
const filled = "_filled_hipsz_417";
const placedPiece = "_placedPiece_hipsz_439";
const used = "_used_hipsz_697";
const gameStats = "_gameStats_hipsz_857";
const statCard = "_statCard_hipsz_873";
const statValue = "_statValue_hipsz_981";
const statLabel = "_statLabel_hipsz_1005";
const gameControls = "_gameControls_hipsz_1025";
const controlButton = "_controlButton_hipsz_1043";
const puzzleBoard = "_puzzleBoard_hipsz_1609";
const boardTitle = "_boardTitle_hipsz_1619";
const activityMenu = "_activityMenu_hipsz_2523";
const activityButton = "_activityButton_hipsz_2541";
const active = "_active_hipsz_2577";
const questionArea = "_questionArea_hipsz_2623";
const questionHeader = "_questionHeader_hipsz_2641";
const questionTitle = "_questionTitle_hipsz_2651";
const noPieces = "_noPieces_hipsz_2687";
const objectsDisplay = "_objectsDisplay_hipsz_2833";
const removePieceButton = "_removePieceButton_hipsz_2983";
const emptySlotText = "_emptySlotText_hipsz_3015";
const answerOptions = "_answerOptions_hipsz_3063";
const answerButton = "_answerButton_hipsz_3079";
const styles = {
  quebraCabecaGame,
  gameContent,
  gameHeader,
  gameTitle,
  headerTtsButton,
  ttsActive,
  puzzleGrid,
  puzzleSlot,
  empty,
  filled,
  placedPiece,
  used,
  gameStats,
  statCard,
  statValue,
  statLabel,
  gameControls,
  controlButton,
  puzzleBoard,
  boardTitle,
  activityMenu,
  activityButton,
  active,
  questionArea,
  questionHeader,
  questionTitle,
  noPieces,
  objectsDisplay,
  removePieceButton,
  emptySlotText,
  answerOptions,
  answerButton
};
var _jsxFileName = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\games\\QuebraCabeca\\QuebraCabecaGame.jsx";
const ACTIVITY_TYPES = {
  EMOTION_PUZZLE: {
    id: "emotion_puzzle",
    name: "Emocional",
    icon: "🎯",
    description: "Teste de coordenação motora e montagem de emoções",
    cognitiveFunction: "motor_coordination_emotion_assembly",
    component: "EmotionPuzzleActivity"
  },
  PIECE_ROTATION: {
    id: "piece_rotation",
    name: "Rotação",
    icon: "🔄",
    description: "Teste de rotação mental e orientação espacial",
    cognitiveFunction: "spatial_rotation_mental_processing",
    component: "PieceRotationActivity"
  },
  PATTERN_PUZZLE: {
    id: "pattern_puzzle",
    name: "Padrões",
    icon: "🔍",
    description: "Teste de reconhecimento de padrões em quebra-cabeças",
    cognitiveFunction: "pattern_recognition_spatial",
    component: "PatternPuzzleActivity"
  }
};
function QuebraCabecaGame({
  onBack
}) {
  const {
    user,
    ttsEnabled = true
  } = reactExports.useContext(SystemContext);
  const {
    settings
  } = useAccessibilityContext();
  reactExports.useRef(null);
  const [gameState, setGameState] = reactExports.useState({
    status: "start",
    // 'start', 'playing', 'paused', 'finished'
    score: 0,
    round: 1,
    level: 1,
    totalRounds: 10,
    difficulty: "EASY",
    accuracy: 100,
    roundStartTime: null,
    // 🎯 Sistema de atividades redesenhado (3 atividades principais)
    currentActivity: ACTIVITY_TYPES.EMOTION_PUZZLE.id,
    activityCycle: [ACTIVITY_TYPES.EMOTION_PUZZLE.id, ACTIVITY_TYPES.PIECE_ROTATION.id, ACTIVITY_TYPES.PATTERN_PUZZLE.id],
    activityIndex: 0,
    roundsPerActivity: 5,
    // Mínimo 5 rodadas por atividade
    activityRoundCount: 0,
    activitiesCompleted: [],
    // 🎯 Dados específicos de atividades
    activityData: {
      freeAssembly: {
        puzzlePieces: [],
        placedPieces: [],
        completedPuzzles: 0
      },
      guidedAssembly: {
        currentHint: null,
        hintsUsed: 0,
        guidanceLevel: "basic"
      },
      patternMatching: {
        patterns: [],
        matchedPatterns: [],
        currentPattern: null
      },
      shapeSorting: {
        shapes: [],
        sortedShapes: {},
        categories: []
      },
      timedChallenge: {
        timeLimit: 60,
        timeRemaining: 60,
        isTimerActive: false
      },
      creativeBuilding: {
        availablePieces: [],
        userCreation: [],
        savedCreations: []
      }
    },
    // 🎯 Feedback e animações
    showFeedback: false,
    feedbackType: null,
    feedbackMessage: "",
    showCelebration: false,
    // 🎯 Métricas comportamentais
    responseTime: 0,
    hesitationCount: 0,
    helpUsed: false,
    consecutiveCorrect: 0,
    totalAttempts: 0,
    correctAttempts: 0,
    // Peças e controle do jogo
    availablePieces: [],
    draggedPiece: null,
    isComplete: false,
    // Feedback e interação
    feedback: null,
    // Estatísticas gerais
    gameStats: {
      score: 0,
      completed: 0,
      totalAttempts: 0,
      accuracy: 100,
      sessionStartTime: null,
      roundStartTime: null
    },
    // 🔥 Configurações especiais
    specialConfig: {
      mentalRotationTime: [],
      spatialErrors: [],
      pieceClassification: {
        categoryAccuracy: {},
        sortingStrategy: "color_first",
        classificationTime: []
      },
      patternIdentification: {
        patternRecognitionSpeed: [],
        logicalAccuracy: [],
        predictionSuccess: []
      },
      collaborativeSolving: {
        cooperationIndex: 0,
        communicationTurns: 0,
        leadershipEvents: []
      }
    },
    // Métricas comportamentais V3
    behavioralMetrics: {
      reactionTime: [],
      accuracy: [],
      attentionSpan: 0,
      frustrationEvents: [],
      persistenceLevel: 0,
      engagementScore: 100,
      multisensoryProcessing: {},
      activitySpecific: {}
    }
  });
  reactExports.useRef(null);
  reactExports.useRef(null);
  const {
    collectMetrics,
    processAdvancedMetrics,
    // Novo: para AdvancedMetricsEngine
    sessionId
  } = useUnifiedGameLogic("QuebraCabeca");
  const [collectorsHub] = reactExports.useState(() => new QuebraCabecaCollectorsHub());
  const [ttsActive2, setTtsActive] = reactExports.useState(() => {
    const saved = localStorage.getItem("quebraCabeca_ttsActive");
    return saved ? JSON.parse(saved) : true;
  });
  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    isInitialized: multisensoryInitialized
  } = useMultisensoryIntegration(sessionId, {
    learningStyle: user?.profile?.learningStyle || "visual"
  });
  useTherapeuticOrchestrator({});
  const [cognitiveAnalysisVisible, setCognitiveAnalysisVisible] = reactExports.useState(false);
  const [gameStarted, setGameStarted] = reactExports.useState(false);
  const [showStartScreen, setShowStartScreen] = reactExports.useState(true);
  const [gameStats2, setGameStats] = reactExports.useState({
    level: 1,
    score: 0,
    completed: 0,
    totalAttempts: 0,
    accuracy: 100
  });
  const [difficulty, setDifficulty] = reactExports.useState("easy");
  const [draggedPiece, setDraggedPiece] = reactExports.useState(null);
  const [placedPieces, setPlacedPieces] = reactExports.useState([]);
  const [puzzlePieces, setPuzzlePieces] = reactExports.useState([]);
  const [isComplete, setIsComplete] = reactExports.useState(false);
  const [currentEmotion, setCurrentEmotion] = reactExports.useState(null);
  const [feedback, setFeedback] = reactExports.useState(null);
  const speak = reactExports.useCallback((text, options = {}) => {
    if (!ttsActive2 || !("speechSynthesis" in window)) {
      return;
    }
    window.speechSynthesis.cancel();
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = "pt-BR";
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    window.speechSynthesis.speak(utterance);
  }, [ttsActive2]);
  const generateActivityContent = reactExports.useCallback((activityId, difficulty2) => {
    const config = QuebraCabecaV3Config.DIFFICULTY_CONFIGS[difficulty2.toUpperCase()];
    const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[activityId.toUpperCase()];
    switch (activityId) {
      case "emotion_puzzle":
        return generateEmotionPuzzleActivity(config, activityConfig);
      case "piece_rotation":
        return generatePieceRotationActivity(config, activityConfig);
      case "pattern_puzzle":
        return generatePatternPuzzleActivity(config, activityConfig);
      default:
        return generateEmotionPuzzleActivity(config, activityConfig);
    }
  }, []);
  const generateEmotionPuzzleActivity = reactExports.useCallback((difficultyConfig, activityConfig) => {
    console.log("🧩 Generating emotion puzzle activity");
    const emotionLibrary = QuebraCabecaV3Config.EMOTIONS_LIBRARY[difficultyConfig?.emotionComplexity || "basic"];
    const randomEmotion = emotionLibrary[Math.floor(Math.random() * emotionLibrary.length)];
    const pieceCounts = {
      EASY: 4,
      MEDIUM: 6,
      HARD: 9
    };
    const difficultyKey = gameState.difficulty || "EASY";
    const pieceCount = pieceCounts[difficultyKey] || 4;
    const correctPieces = randomEmotion.pieces.slice(0, pieceCount);
    const distractorPieces = generateDistractorPieces(correctPieces, 2);
    return {
      emotion: randomEmotion,
      correctPieces,
      availablePieces: [...correctPieces, ...distractorPieces].sort(() => Math.random() - 0.5),
      targetSlots: pieceCount,
      instruction: `Monte o quebra-cabeça da emoção "${randomEmotion.name}"`,
      level: gameState.difficulty,
      activityType: "emotion_puzzle",
      gridLayout: Math.ceil(Math.sqrt(pieceCount))
    };
  }, [gameState.difficulty]);
  const generatePieceRotationActivity = reactExports.useCallback((difficultyConfig, activityConfig) => {
    console.log("🔄 Generating piece rotation activity");
    const emotionLibrary = QuebraCabecaV3Config.EMOTIONS_LIBRARY[difficultyConfig?.emotionComplexity || "basic"];
    const randomEmotion = emotionLibrary[Math.floor(Math.random() * emotionLibrary.length)];
    const rotationAngles = {
      EASY: [90, 180],
      MEDIUM: [90, 180, 270],
      HARD: [45, 90, 135, 180, 225, 270, 315]
    };
    const pieces = randomEmotion.pieces.slice(0, 4);
    const difficultyKey = gameState.difficulty || "EASY";
    const availableAngles = rotationAngles[difficultyKey] || rotationAngles.EASY;
    const rotatedPieces = pieces.map((piece, index) => ({
      id: index,
      original: piece,
      angle: availableAngles[Math.floor(Math.random() * availableAngles.length)],
      placed: false,
      correctPosition: index
    }));
    return {
      emotion: randomEmotion,
      rotatedPieces,
      instruction: `Rotacione mentalmente as peças e monte "${randomEmotion.name}"`,
      level: gameState.difficulty,
      activityType: "piece_rotation",
      completedRotations: 0
    };
  }, [gameState.difficulty]);
  const generatePatternPuzzleActivity = reactExports.useCallback((difficultyConfig, activityConfig) => {
    console.log("🔍 Generating pattern puzzle activity");
    const patternTypes = {
      EASY: [{
        type: "alternating",
        sequence: ["😊", "😢", "😊", "😢"],
        missing: 2,
        correct: "😊"
      }, {
        type: "sequential",
        sequence: ["😊", "🤩", "😍", "🥰"],
        missing: 3,
        correct: "🥰"
      }],
      MEDIUM: [{
        type: "progressive",
        sequence: ["😐", "😊", "😄", "🤩"],
        missing: 2,
        correct: "😄"
      }, {
        type: "emotional_scale",
        sequence: ["😢", "😔", "😐", "😊"],
        missing: 1,
        correct: "😔"
      }],
      HARD: [{
        type: "complex_pattern",
        sequence: ["😊", "😢", "😡", "😊", "😢"],
        missing: 4,
        correct: "😡"
      }, {
        type: "emotional_cycle",
        sequence: ["😴", "😊", "😰", "😴"],
        missing: 2,
        correct: "😰"
      }]
    };
    const difficultyKey = gameState.difficulty || "EASY";
    const levelPatterns = patternTypes[difficultyKey] || patternTypes.EASY;
    const selectedPattern = levelPatterns[Math.floor(Math.random() * levelPatterns.length)];
    const wrongOptions = ["😲", "🤔", "😏", "🥳"].filter((opt) => opt !== selectedPattern.correct);
    const options = [selectedPattern.correct, ...wrongOptions.slice(0, 2)].sort(() => Math.random() - 0.5);
    return {
      pattern: selectedPattern,
      options,
      instruction: `Complete o padrão emocional observando a sequência`,
      level: gameState.difficulty,
      activityType: "pattern_puzzle",
      userAnswer: null,
      solved: false
    };
  }, [gameState.difficulty]);
  const generateDistractorPieces = reactExports.useCallback((correctPieces, count) => {
    const allPieces = ["😊", "😢", "😲", "😠", "😌", "🤩", "🌞", "🌧️", "💔", "🎁", "🎉", "❓", "✨", "🌊", "🕊️", "🌿", "💥", "🌋", "⚡", "🎪", "🎢", "🎊"];
    const distractors = allPieces.filter((piece) => !correctPieces.includes(piece));
    return distractors.sort(() => Math.random() - 0.5).slice(0, count);
  }, []);
  reactExports.useCallback((settings2) => {
    const baseCategories = {
      positive: {
        name: "Emoções Positivas",
        icon: "😊",
        pieces: []
      },
      negative: {
        name: "Emoções Negativas",
        icon: "😢",
        pieces: []
      },
      neutral: {
        name: "Emoções Neutras",
        icon: "😐",
        pieces: []
      },
      intense: {
        name: "Emoções Intensas",
        icon: "🤯",
        pieces: []
      }
    };
    const selectedCategories = Object.keys(baseCategories).slice(0, settings2.categories || 2);
    const result = {};
    selectedCategories.forEach((key) => {
      result[key] = baseCategories[key];
    });
    return result;
  }, []);
  reactExports.useCallback((categories, settings2) => {
    const categoryKeys = Object.keys(categories);
    const pieces = [];
    const emotionMap = {
      positive: ["😊", "😄", "🤩", "😍", "🥰", "😌"],
      negative: ["😢", "😭", "😠", "😡", "😰", "😔"],
      neutral: ["😐", "🤔", "😑", "😶", "🙄", "😏"],
      intense: ["🤯", "😱", "🤬", "😵", "🥵", "🥶"]
    };
    categoryKeys.forEach((categoryKey) => {
      const categoryPieces = emotionMap[categoryKey] || [];
      const selectedPieces = categoryPieces.slice(0, settings2.piecesPerCategory || 3);
      pieces.push(...selectedPieces.map((piece) => ({
        piece,
        category: categoryKey,
        emoji: piece
      })));
    });
    return pieces.sort(() => Math.random() - 0.5);
  }, []);
  reactExports.useCallback((categories, settings2) => {
    const categoryKeys = Object.keys(categories);
    const pieces = [];
    categoryKeys.forEach((categoryKey) => {
      const categoryPieces = getCategoryPieces(categoryKey);
      const selectedPieces = categoryPieces.slice(0, settings2.piecesPerCategory);
      pieces.push(...selectedPieces.map((piece) => ({
        piece,
        category: categoryKey
      })));
    });
    return pieces.sort(() => Math.random() - 0.5);
  }, []);
  const getCategoryPieces = reactExports.useCallback((category) => {
    const categoryMap = {
      emotions: ["😊", "😢", "😲", "😠", "😌", "🤩"],
      nature: ["🌞", "🌧️", "🌊", "🕊️", "🌿", "🌋"],
      objects: ["🎁", "🎉", "🎪", "🎢", "🎊", "💔"],
      symbols: ["❓", "✨", "💥", "⚡", "🏆", "⭐"]
    };
    return categoryMap[category] || [];
  }, []);
  reactExports.useCallback((settings2) => {
    const patternTypes = settings2.patternTypes;
    const selectedType = patternTypes[Math.floor(Math.random() * patternTypes.length)];
    switch (selectedType) {
      case "alternating":
        return {
          type: "alternating",
          sequence: ["😊", "😢", "😊", "😢"],
          next: "😊"
        };
      case "sequential":
        return {
          type: "sequential",
          sequence: ["😊", "🌞", "😢", "🌧️"],
          next: "😲"
        };
      case "progressive":
        return {
          type: "progressive",
          sequence: ["😊", "🤩", "😢", "😭"],
          next: "😡"
        };
      default:
        return {
          type: "alternating",
          sequence: ["😊", "😢", "😊", "😢"],
          next: "😊"
        };
    }
  }, []);
  reactExports.useCallback((pattern, settings2) => {
    const correctAnswer = pattern.next;
    const wrongOptions = ["😲", "🤔", "😴", "🥳"].filter((opt) => opt !== correctAnswer);
    const selectedWrong = wrongOptions.slice(0, settings2.completionOptions - 1);
    return [correctAnswer, ...selectedWrong].sort(() => Math.random() - 0.5);
  }, []);
  reactExports.useCallback(() => {
    setGameState((prev) => {
      const nextIndex = (prev.activityIndex + 1) % prev.activityCycle.length;
      const nextActivity = prev.activityCycle[nextIndex];
      if (recordMultisensoryInteraction) {
        recordMultisensoryInteraction("activity_rotation", {
          from: prev.currentActivity,
          to: nextActivity,
          automatic: true,
          round: prev.round
        });
      }
      const newState = {
        ...prev,
        currentActivity: nextActivity,
        activityIndex: nextIndex,
        activityRoundCount: 0,
        round: prev.round + 1,
        roundStartTime: Date.now()
      };
      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);
      newState.currentEmotion = activityContent.emotion || null;
      newState.puzzlePieces = activityContent.availablePieces || activityContent.pieces || [];
      newState.placedPieces = new Array(activityContent.targetSlots || 3).fill(null);
      const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[nextActivity.toUpperCase()];
      const activityName = activityConfig?.name || "Nova Atividade";
      setTimeout(() => {
        if (speak) {
          speak(`Nova atividade: ${activityName}. ${activityContent.instruction}`, {
            rate: 0.8
          });
        }
      }, 500);
      return newState;
    });
  }, [generateActivityContent, recordMultisensoryInteraction, speak]);
  const switchActivity = reactExports.useCallback((activityId) => {
    setGameState((prev) => {
      const activityIndex = prev.activityCycle.indexOf(activityId);
      if (activityIndex === -1) return prev;
      const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[activityId.toUpperCase()];
      const activityName = activityConfig?.name || "Nova Atividade";
      if (recordMultisensoryInteraction) {
        recordMultisensoryInteraction("activity_switch", {
          from: prev.currentActivity,
          to: activityId,
          manual: true,
          round: prev.round
        });
      }
      const newState = {
        ...prev,
        currentActivity: activityId,
        activityIndex,
        activityRoundCount: 0,
        round: prev.round + 1,
        roundStartTime: Date.now()
      };
      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);
      newState.currentEmotion = activityContent.emotion || null;
      newState.puzzlePieces = activityContent.availablePieces || activityContent.pieces || [];
      newState.placedPieces = new Array(activityContent.targetSlots || 3).fill(null);
      setTimeout(() => {
        if (speak) {
          speak(`Atividade alterada para: ${activityName}. ${activityContent.instruction}`, {
            rate: 0.8
          });
        }
      }, 300);
      return newState;
    });
  }, [generateActivityContent, recordMultisensoryInteraction, speak]);
  reactExports.useCallback(() => {
    console.log("🎯 Gerando nova rodada V3...", {
      currentActivity: gameState.currentActivity,
      activityRoundCount: gameState.activityRoundCount,
      round: gameState.round
    });
    setGameState((prev) => {
      const shouldRotateActivity = prev.activityRoundCount >= prev.roundsPerActivity;
      console.log("🔄 Verificando rotação de atividade:", {
        shouldRotateActivity,
        activityRoundCount: prev.activityRoundCount,
        roundsPerActivity: prev.roundsPerActivity
      });
      let newState = {
        ...prev
      };
      if (shouldRotateActivity) {
        const nextActivityIndex = (prev.activityIndex + 1) % prev.activityCycle.length;
        const nextActivity = prev.activityCycle[nextActivityIndex];
        console.log("🎮 Rotacionando para nova atividade:", {
          from: prev.currentActivity,
          to: nextActivity,
          nextActivityIndex
        });
        newState = {
          ...newState,
          currentActivity: nextActivity,
          activityIndex: nextActivityIndex,
          activityRoundCount: 0,
          activitiesCompleted: prev.activitiesCompleted + 1
        };
        const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[nextActivity.toUpperCase()];
        const activityName = activityConfig?.name || "Nova Atividade";
        setTimeout(() => {
          if (speak) {
            speak(`Nova atividade: ${activityName}`, {
              pitch: 1.2,
              rate: 0.8
            });
          }
        }, 500);
      }
      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);
      console.log("📝 Conteúdo gerado para atividade:", {
        activity: newState.currentActivity,
        content: activityContent
      });
      newState.currentEmotion = activityContent.emotion || newState.currentEmotion;
      newState.puzzlePieces = activityContent.availablePieces || activityContent.pieces || [];
      newState.placedPieces = new Array(activityContent.targetSlots || 3).fill(null);
      newState.isComplete = false;
      newState.draggedPiece = null;
      newState.feedback = null;
      newState.showFeedback = false;
      newState.activityRoundCount = newState.activityRoundCount + 1;
      newState.round = newState.round + 1;
      newState.roundStartTime = Date.now();
      return newState;
    });
  }, [gameState.currentActivity, gameState.activityRoundCount, gameState.round, generateActivityContent, speak]);
  const renderEmotionPuzzle = () => {
    if (!currentEmotion && gameStarted) {
      generateNewPuzzle();
      return /* @__PURE__ */ React.createElement("div", { className: styles.questionArea, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 1038,
        columnNumber: 9
      } }, /* @__PURE__ */ React.createElement("div", { className: styles.questionHeader, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 1039,
        columnNumber: 11
      } }, /* @__PURE__ */ React.createElement("h2", { className: styles.questionTitle, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 1040,
        columnNumber: 13
      } }, "🧩 Quebra-Cabeça Emocional")));
    }
    return /* @__PURE__ */ React.createElement("div", { className: styles.questionArea, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1047,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.questionHeader, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1048,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("h2", { className: styles.questionTitle, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1049,
      columnNumber: 11
    } }, "🧩 Monte a emoção: ", currentEmotion?.name)), currentEmotion && /* @__PURE__ */ React.createElement(React.Fragment, null, /* @__PURE__ */ React.createElement("div", { className: styles.objectsDisplay, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1055,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      textAlign: "center",
      padding: "2rem",
      border: "3px solid rgba(33, 150, 243, 0.6)",
      borderRadius: "20px",
      backgroundColor: "rgba(33, 150, 243, 0.2)",
      minWidth: "200px",
      boxShadow: "0 0 20px rgba(33, 150, 243, 0.3)",
      marginBottom: "2rem"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1056,
      columnNumber: 15
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "4rem",
      marginBottom: "1rem"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1066,
      columnNumber: 17
    } }, currentEmotion.emoji), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1.5rem",
      fontWeight: "bold",
      color: "white"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1069,
      columnNumber: 17
    } }, currentEmotion.name)), /* @__PURE__ */ React.createElement("div", { className: styles.puzzleBoard, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1075,
      columnNumber: 15
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.boardTitle, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1076,
      columnNumber: 17
    } }, "Monte as peças que representam esta emoção:"), /* @__PURE__ */ React.createElement("div", { className: styles.puzzleGrid, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1079,
      columnNumber: 17
    } }, placedPieces.map((piece, index) => /* @__PURE__ */ React.createElement("div", { key: index, className: `${styles.puzzleSlot} ${piece ? styles.filled : styles.empty}`, onDragOver: (e) => e.preventDefault(), onDrop: () => handleDrop(index), style: {
      backgroundColor: currentEmotion.color + "20",
      border: piece ? "3px solid #4CAF50" : "2px dashed rgba(255,255,255,0.5)"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1081,
      columnNumber: 21
    } }, piece && /* @__PURE__ */ React.createElement("div", { className: styles.placedPiece, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1092,
      columnNumber: 25
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "2.5rem",
      marginBottom: "0.5rem"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1093,
      columnNumber: 27
    } }, piece.content), /* @__PURE__ */ React.createElement("button", { className: styles.removePieceButton, onClick: () => handleRemovePiece(index), title: "Remover peça", __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1096,
      columnNumber: 27
    } }, "❌")), !piece && /* @__PURE__ */ React.createElement("div", { className: styles.emptySlotText, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1106,
      columnNumber: 25
    } }, "Arraste uma peça aqui")))))), /* @__PURE__ */ React.createElement("div", { className: styles.answerOptions, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1117,
      columnNumber: 13
    } }, puzzlePieces.length > 0 ? puzzlePieces.map((piece) => {
      const isUsed = piece.used;
      return /* @__PURE__ */ React.createElement("button", { key: piece.id, className: `${styles.answerButton} ${isUsed ? styles.used : ""}`, draggable: !isUsed, onDragStart: () => !isUsed && handleDragStart(piece), style: {
        opacity: isUsed ? 0.3 : 1,
        cursor: isUsed ? "not-allowed" : "grab",
        pointerEvents: isUsed ? "none" : "auto"
      }, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 1122,
        columnNumber: 19
      } }, /* @__PURE__ */ React.createElement("div", { style: {
        fontSize: "2.5rem",
        marginBottom: "0.5rem"
      }, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 1133,
        columnNumber: 21
      } }, piece.content), /* @__PURE__ */ React.createElement("div", { style: {
        fontSize: "0.9rem",
        fontWeight: "bold",
        color: isUsed ? "rgba(255,255,255,0.5)" : "white"
      }, __self: this, __source: {
        fileName: _jsxFileName,
        lineNumber: 1139,
        columnNumber: 21
      } }, isUsed ? "Usada" : "Arraste"));
    }) : /* @__PURE__ */ React.createElement("div", { className: styles.noPieces, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1149,
      columnNumber: 17
    } }, "Carregando peças..."))));
  };
  const renderPieceRotation = () => {
    return /* @__PURE__ */ React.createElement("div", { className: styles.questionArea, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1163,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.questionHeader, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1164,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("h2", { className: styles.questionTitle, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1165,
      columnNumber: 11
    } }, "🔄 Rotação de Peças")), /* @__PURE__ */ React.createElement("div", { className: styles.objectsDisplay, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1169,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      textAlign: "center",
      padding: "2rem",
      border: "3px solid rgba(33, 150, 243, 0.6)",
      borderRadius: "20px",
      backgroundColor: "rgba(33, 150, 243, 0.2)",
      minWidth: "200px",
      boxShadow: "0 0 20px rgba(33, 150, 243, 0.3)"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1170,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "4rem",
      marginBottom: "1rem"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1179,
      columnNumber: 13
    } }, "🧩"), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1.5rem",
      fontWeight: "bold",
      color: "white"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1182,
      columnNumber: 13
    } }, "Peça Original")), /* @__PURE__ */ React.createElement("div", { style: {
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      gap: "0.5rem",
      margin: "1rem 0"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1188,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "3rem",
      animation: "bounce 2s infinite",
      color: "rgba(255, 193, 7, 0.8)"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1195,
      columnNumber: 13
    } }, "⬇️"), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1rem",
      color: "rgba(255,255,255,0.8)",
      fontWeight: "bold"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1202,
      columnNumber: 13
    } }, "SE RELACIONA COM"))), /* @__PURE__ */ React.createElement("div", { className: styles.answerOptions, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1213,
      columnNumber: 9
    } }, [0, 90, 180, 270].map((rotation, index) => /* @__PURE__ */ React.createElement("button", { key: index, className: styles.answerButton, onClick: () => handlePieceRotation(index), "aria-label": `Girar ${rotation} graus`, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1215,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "2.5rem",
      marginBottom: "0.5rem",
      transform: `rotate(${rotation}deg)`
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1221,
      columnNumber: 15
    } }, "🧩"), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1rem",
      fontWeight: "bold"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1228,
      columnNumber: 15
    } }, rotation, "°")))));
  };
  const renderPatternPuzzle = () => {
    return /* @__PURE__ */ React.createElement("div", { className: styles.questionArea, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1241,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.questionHeader, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1242,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("h2", { className: styles.questionTitle, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1243,
      columnNumber: 11
    } }, "🔍 Quebra-Cabeça de Padrões")), /* @__PURE__ */ React.createElement("div", { className: styles.objectsDisplay, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1247,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      textAlign: "center",
      padding: "2rem",
      border: "3px solid rgba(33, 150, 243, 0.6)",
      borderRadius: "20px",
      backgroundColor: "rgba(33, 150, 243, 0.2)",
      minWidth: "300px",
      boxShadow: "0 0 20px rgba(33, 150, 243, 0.3)"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1248,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      display: "flex",
      gap: "1rem",
      justifyContent: "center",
      alignItems: "center",
      marginBottom: "1rem"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1257,
      columnNumber: 13
    } }, ["😊", "😢", "😊", "😢", "❓"].map((item, index) => /* @__PURE__ */ React.createElement("div", { key: index, style: {
      fontSize: "2.5rem",
      padding: "0.5rem",
      border: index === 4 ? "2px dashed rgba(255, 193, 7, 0.8)" : "2px solid rgba(255,255,255,0.3)",
      borderRadius: "12px",
      backgroundColor: index === 4 ? "rgba(255, 193, 7, 0.3)" : "rgba(255,255,255,0.1)",
      minWidth: "50px",
      minHeight: "50px",
      display: "flex",
      alignItems: "center",
      justifyContent: "center"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1265,
      columnNumber: 17
    } }, item))), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1.5rem",
      fontWeight: "bold",
      color: "white"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1284,
      columnNumber: 13
    } }, "Complete o Padrão")), /* @__PURE__ */ React.createElement("div", { style: {
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      gap: "0.5rem",
      margin: "1rem 0"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1290,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "3rem",
      animation: "bounce 2s infinite",
      color: "rgba(255, 193, 7, 0.8)"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1297,
      columnNumber: 13
    } }, "⬇️"), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1rem",
      color: "rgba(255,255,255,0.8)",
      fontWeight: "bold"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1304,
      columnNumber: 13
    } }, "SE RELACIONA COM"))), /* @__PURE__ */ React.createElement("div", { className: styles.answerOptions, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1315,
      columnNumber: 9
    } }, ["😊", "😢", "😡", "😨"].map((option, index) => /* @__PURE__ */ React.createElement("button", { key: index, className: styles.answerButton, onClick: () => handlePatternAnswer(option), "aria-label": `Opção ${option}`, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1317,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "2.5rem",
      marginBottom: "0.5rem"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1323,
      columnNumber: 15
    } }, option), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1rem",
      fontWeight: "bold"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1326,
      columnNumber: 15
    } }, option === "😊" ? "Feliz" : option === "😢" ? "Triste" : option === "😡" ? "Bravo" : "Medo")))));
  };
  reactExports.useEffect(() => {
    return () => {
      if ("speechSynthesis" in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);
  const [analysisResults, setAnalysisResults] = reactExports.useState(null);
  const [attemptCount, setAttemptCount] = reactExports.useState(0);
  const [sessionStartTime, setSessionStartTime] = reactExports.useState(null);
  const [pieceInteractions, setPieceInteractions] = reactExports.useState([]);
  const [spatialStrategies, setSpatialStrategies] = reactExports.useState([]);
  const [problemSolvingApproach, setProblemSolvingApproach] = reactExports.useState("systematic");
  reactExports.useEffect(() => {
    if (sessionId && typeof sessionId === "string" && sessionId.length > 0 && gameStarted && !multisensoryInitialized) {
      const initializeMultisensorial = async () => {
        try {
          await initMultisensory();
          console.log("✅ Sistema multissensorial inicializado com sessionId:", sessionId);
        } catch (error) {
          console.error("❌ Erro ao inicializar sistema multissensorial:", error);
        }
      };
      initializeMultisensorial();
    }
  }, [sessionId, gameStarted, multisensoryInitialized, initMultisensory]);
  const getAccuracy = reactExports.useCallback(() => {
    if (gameStats2.totalAttempts === 0) return 100;
    return Math.round(gameStats2.completed / gameStats2.totalAttempts * 100);
  }, [gameStats2]);
  const collectPuzzleMetrics = async () => {
    const currentTime = Date.now();
    const sessionDuration = sessionStartTime ? currentTime - sessionStartTime : 0;
    const basicMetrics = {
      totalTime: sessionDuration,
      correctAnswers: gameStats2.completed,
      incorrectAnswers: gameStats2.totalAttempts - gameStats2.completed,
      accuracy: getAccuracy(),
      difficultyLevel: difficulty,
      completionLevel: isComplete ? 100 : placedPieces.length / puzzlePieces.length * 100
    };
    const puzzleSpecificMetrics = {
      spatialReasoning: calculateSpatialReasoning(),
      piecePlacementAccuracy: calculatePlacementAccuracy(),
      completionStrategy: identifyCompletionStrategy(),
      visualSpatialMemory: analyzeVisualSpatialMemory(),
      problemSolvingApproach,
      frustranceTolerance: calculateFrustranceTolerance(),
      persistenceLevel: calculatePersistenceLevel(),
      rotationAttempts: countRotationAttempts(),
      sequentialPlacement: analyzeSequentialPlacement()
    };
    await collectMetrics({
      ...basicMetrics,
      ...puzzleSpecificMetrics
    });
    await processAdvancedMetrics({
      gameType: "QuebraCabeca",
      sessionData: {
        ...basicMetrics,
        ...puzzleSpecificMetrics,
        interactions: pieceInteractions,
        spatialStrategies,
        duration: sessionDuration
      },
      userProfile: {
        preferredDifficulty: difficulty,
        spatialPreferences: identifySpatialPreferences()
      }
    });
  };
  const calculateSpatialReasoning = () => {
    const correctPlacements = pieceInteractions.filter((i) => i.correct).length;
    const totalInteractions = pieceInteractions.length;
    return totalInteractions > 0 ? correctPlacements / totalInteractions * 100 : 0;
  };
  const calculatePlacementAccuracy = () => {
    const firstTryCorrect = pieceInteractions.filter((i) => i.attemptNumber === 1 && i.correct).length;
    const totalPieces = puzzlePieces.length;
    return totalPieces > 0 ? firstTryCorrect / totalPieces * 100 : 0;
  };
  const identifyCompletionStrategy = () => {
    if (spatialStrategies.length === 0) return "unknown";
    const strategies = spatialStrategies.reduce((count, strategy) => {
      count[strategy] = (count[strategy] || 0) + 1;
      return count;
    }, {});
    return Object.keys(strategies).reduce((a, b) => strategies[a] > strategies[b] ? a : b);
  };
  const analyzeVisualSpatialMemory = () => {
    const memoryScore = pieceInteractions.reduce((score, interaction, index) => {
      if (index > 0) {
        const prevInteraction = pieceInteractions[index - 1];
        if (interaction.pieceId === prevInteraction.pieceId && interaction.position.x === prevInteraction.position.x && interaction.position.y === prevInteraction.position.y) {
          score += 10;
        }
      }
      return score;
    }, 0);
    return Math.min(memoryScore, 100);
  };
  const calculateFrustranceTolerance = () => {
    const incorrectAttempts = pieceInteractions.filter((i) => !i.correct).length;
    const totalAttempts = pieceInteractions.length;
    if (totalAttempts === 0) return 100;
    return Math.max(0, 100 - incorrectAttempts / totalAttempts * 100);
  };
  const calculatePersistenceLevel = () => {
    const maxAttemptsPerPiece = Math.max(...puzzlePieces.map((piece) => {
      return pieceInteractions.filter((i) => i.pieceId === piece.id).length;
    }), 1);
    return Math.min(maxAttemptsPerPiece * 20, 100);
  };
  const countRotationAttempts = () => {
    return pieceInteractions.filter((i) => i.action === "rotate").length;
  };
  const analyzeSequentialPlacement = () => {
    if (pieceInteractions.length < 2) return "insufficient_data";
    let sequentialCount = 0;
    for (let i = 1; i < pieceInteractions.length; i++) {
      const curr = pieceInteractions[i];
      const prev = pieceInteractions[i - 1];
      if (curr.pieceId === prev.pieceId + 1) {
        sequentialCount++;
      }
    }
    const sequentialRatio = sequentialCount / (pieceInteractions.length - 1);
    return sequentialRatio > 0.7 ? "sequential" : sequentialRatio > 0.4 ? "mixed" : "random";
  };
  const identifySpatialPreferences = () => {
    return {
      preferredStartPosition: "top-left",
      // Placeholder
      rotationFrequency: countRotationAttempts() / Math.max(pieceInteractions.length, 1),
      systematicApproach: problemSolvingApproach === "systematic"
    };
  };
  const recordPieceInteraction = async (pieceId, position, action, correct) => {
    const interactionData = {
      pieceId,
      position,
      action,
      correct,
      timestamp: Date.now(),
      attemptNumber: attemptCount + 1,
      difficultyLevel: difficulty,
      sessionTime: Date.now() - (sessionStartTime || Date.now())
    };
    setPieceInteractions((prev) => [...prev, interactionData]);
    try {
      await collectorsHub.collectMoveData({
        ...interactionData,
        puzzleState: {
          totalPieces: puzzlePieces.length,
          placedPieces: placedPieces.length,
          completionPercentage: placedPieces.filter((p) => p !== null).length / puzzlePieces.length * 100
        }
      });
      await recordMultisensoryInteraction("game_interaction", {
        interactionType: "user_action",
        gameSpecificData: interactionData,
        multisensoryProcessing: {
          spatialProcessing: {
            spatialReasoning: 0.7,
            visualSpatialMemory: 0.7,
            spatialOrientation: 0.7
          },
          cognitiveProcessing: {
            problemSolving: 0.7,
            processingSpeed: 0.7,
            adaptability: 0.7
          },
          behavioralProcessing: {
            interactionCount: 0.7,
            averageResponseTime: 0.7,
            persistence: 0.7
          }
        }
      });
      const newAttemptCount = attemptCount + 1;
      setAttemptCount(newAttemptCount);
      if (newAttemptCount % 3 === 0) {
        performCognitiveAnalysis();
      }
    } catch (error) {
      console.error("Erro ao coletar dados da interação:", error);
    }
  };
  const performCognitiveAnalysis = async () => {
    try {
      setCognitiveAnalysisVisible(true);
      const analysisData = await collectorsHub.collectComprehensiveData({
        sessionId: sessionStartTime?.toString() || "session",
        gameState: {
          difficulty,
          currentLevel: gameStats2.level,
          score: gameStats2.score,
          accuracy: getAccuracy(),
          isComplete
        },
        interactions: pieceInteractions,
        spatialData: {
          strategies: spatialStrategies,
          approach: problemSolvingApproach
        }
      });
      setAnalysisResults(analysisData);
      setTimeout(() => {
        setCognitiveAnalysisVisible(false);
      }, 3e3);
    } catch (error) {
      console.error("Erro na análise cognitiva:", error);
      setCognitiveAnalysisVisible(false);
    }
  };
  const generateNewPuzzle = reactExports.useCallback(() => {
    const randomEmotion = QuebraCabecaConfig.emotions[Math.floor(Math.random() * QuebraCabecaConfig.emotions.length)];
    const difficultyData = QuebraCabecaConfig.difficulties.find((d) => d.id === difficulty);
    if (!difficultyData) {
      console.error("Difficulty data not found for:", difficulty);
      return;
    }
    setCurrentEmotion(randomEmotion);
    setIsComplete(false);
    setFeedback(null);
    setGameStats((prev) => ({
      ...prev,
      totalAttempts: prev.totalAttempts + 1
    }));
    const correctPieces = randomEmotion.pieces.slice(0, difficultyData.pieces).map((piece, index) => ({
      id: `correct_${index}`,
      content: piece,
      used: false,
      isCorrect: true
    }));
    const allEmotions = QuebraCabecaConfig.emotions;
    const otherEmotions = allEmotions.filter((e) => e.id !== randomEmotion.id);
    const distractorPieces = [];
    for (let i = 0; i < Math.min(3, otherEmotions.length); i++) {
      const randomOtherEmotion = otherEmotions[Math.floor(Math.random() * otherEmotions.length)];
      const randomPiece = randomOtherEmotion.pieces[Math.floor(Math.random() * randomOtherEmotion.pieces.length)];
      distractorPieces.push({
        id: `distractor_${i}`,
        content: randomPiece,
        used: false,
        isCorrect: false
      });
    }
    const allPieces = [...correctPieces, ...distractorPieces];
    setPuzzlePieces(allPieces.sort(() => Math.random() - 0.5));
    setPlacedPieces(Array(difficultyData.pieces).fill(null));
  }, [difficulty]);
  const startGame = reactExports.useCallback(async (selectedDifficulty) => {
    if (selectedDifficulty) {
      setDifficulty(selectedDifficulty);
    }
    setShowStartScreen(false);
    setGameStarted(true);
    setGameStats({
      level: 1,
      score: 0,
      completed: 0,
      totalAttempts: 0
    });
    try {
      await initMultisensory(`session_${Date.now()}`, {
        difficulty,
        gameMode: "puzzle_solving",
        userId: user?.id || "anonymous"
      });
    } catch (error) {
      console.warn("⚠️ Erro ao inicializar sessão multissensorial:", error);
    }
    generateNewPuzzle();
  }, [initMultisensory, generateNewPuzzle, user]);
  const toggleTTS = reactExports.useCallback(() => {
    setTtsActive((prev) => {
      const newState = !prev;
      localStorage.setItem("quebraCabeca_ttsActive", JSON.stringify(newState));
      if (!newState && "speechSynthesis" in window) {
        window.speechSynthesis.cancel();
      }
      return newState;
    });
  }, []);
  const handleDragStart = reactExports.useCallback((piece) => {
    setDraggedPiece(piece);
  }, []);
  const handlePuzzleComplete = reactExports.useCallback(async () => {
    setIsComplete(true);
    const points = QuebraCabecaConfig.gameSettings.pointsByDifficulty[difficulty.toUpperCase()] || 10;
    setGameStats((prev) => ({
      ...prev,
      completed: prev.completed + 1,
      score: prev.score + points,
      level: prev.level + 1
    }));
    const message = QuebraCabecaConfig.encouragingMessages[Math.floor(Math.random() * QuebraCabecaConfig.encouragingMessages.length)];
    speak(`${message} Você ganhou ${points} pontos!`);
    try {
      await collectPuzzleMetrics();
      console.log("🧩 Métricas do quebra-cabeça coletadas com sucesso!");
    } catch (error) {
      console.error("❌ Erro ao coletar métricas do quebra-cabeça:", error);
    }
  }, [difficulty, speak, collectPuzzleMetrics]);
  const handleDrop = reactExports.useCallback((targetIndex) => {
    if (!draggedPiece) return;
    if (placedPieces[targetIndex] !== null) {
      speak("Este slot já está ocupado!");
      setDraggedPiece(null);
      return;
    }
    const newPlacedPieces = [...placedPieces];
    newPlacedPieces[targetIndex] = draggedPiece;
    const isCorrectPiece = currentEmotion && currentEmotion.pieces.includes(draggedPiece.content);
    recordPieceInteraction(draggedPiece.id, {
      x: targetIndex % 2,
      y: Math.floor(targetIndex / 2)
    }, "place", isCorrectPiece);
    setPlacedPieces(newPlacedPieces);
    if (isCorrectPiece) {
      setPuzzlePieces((prev) => prev.map((p) => p.id === draggedPiece.id ? {
        ...p,
        used: true
      } : p));
      speak("Peça colocada corretamente!");
      setGameStats((prev) => ({
        ...prev,
        score: prev.score + 10,
        completed: prev.completed + 1
      }));
      const correctPiecesPlaced = newPlacedPieces.filter((piece) => piece !== null && currentEmotion.pieces.includes(piece.content)).length;
      if (correctPiecesPlaced === currentEmotion.pieces.length) {
        setTimeout(() => handlePuzzleComplete(), 500);
      }
    } else {
      speak("Esta peça não combina com a emoção!");
      setTimeout(() => {
        setPlacedPieces((prev) => {
          const updated = [...prev];
          updated[targetIndex] = null;
          return updated;
        });
      }, 1500);
    }
    setDraggedPiece(null);
  }, [draggedPiece, placedPieces, currentEmotion, recordPieceInteraction, speak, handlePuzzleComplete]);
  const handleRemovePiece = reactExports.useCallback((slotIndex) => {
    const pieceToRemove = placedPieces[slotIndex];
    if (!pieceToRemove) return;
    const newPlacedPieces = [...placedPieces];
    newPlacedPieces[slotIndex] = null;
    setPlacedPieces(newPlacedPieces);
    setPuzzlePieces((prev) => prev.map((p) => p.id === pieceToRemove.id ? {
      ...p,
      used: false
    } : p));
    speak("Peça removida!");
  }, [placedPieces, speak]);
  if (showStartScreen) {
    console.log("🧩 QuebraCabeca: Renderizando tela de início padronizada");
    return /* @__PURE__ */ React.createElement(GameStartScreen, { gameTitle: "Quebra-cabeça Emocional", gameDescription: "Desenvolva inteligência emocional através de puzzles interativos", gameIcon: "🧩", onStart: startGame, onBack, difficulties: [{
      id: "easy",
      name: "Fácil",
      description: "Peças grandes\nIdeal para iniciantes",
      icon: "😊"
    }, {
      id: "medium",
      name: "Médio",
      description: "Peças médias\nDesafio equilibrado",
      icon: "🎯"
    }, {
      id: "hard",
      name: "Avançado",
      description: "Peças pequenas\nPara especialistas",
      icon: "🚀"
    }], __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1843,
      columnNumber: 7
    } });
  }
  return /* @__PURE__ */ React.createElement("div", { className: `${styles.quebraCabecaGame} ${settings.reducedMotion ? "reduced-motion" : ""} ${settings.highContrast ? "high-contrast" : ""}`, "data-font-size": settings.fontSize, "data-theme": settings.colorScheme, style: {
    fontSize: settings.fontSize === "small" ? "0.875rem" : settings.fontSize === "large" ? "1.25rem" : "1rem"
  }, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1858,
    columnNumber: 5
  } }, cognitiveAnalysisVisible && /* @__PURE__ */ React.createElement("div", { className: "cognitive-analysis-indicator", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1869,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("div", { className: "analysis-content", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1870,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: "analysis-icon", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1871,
    columnNumber: 13
  } }, "🎯🧠"), /* @__PURE__ */ React.createElement("div", { className: "analysis-text", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1872,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("div", { className: "analysis-title", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1873,
    columnNumber: 15
  } }, "Análise Espacial em Progresso"), /* @__PURE__ */ React.createElement("div", { className: "analysis-details", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1874,
    columnNumber: 15
  } }, "Avaliando raciocínio espacial e estratégias de resolução...")))), analysisResults && /* @__PURE__ */ React.createElement("div", { className: "cognitive-insights-panel", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1884,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("div", { className: "insights-header", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1885,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("span", { className: "insights-icon", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1886,
    columnNumber: 13
  } }, "🎯"), /* @__PURE__ */ React.createElement("span", { className: "insights-title", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1887,
    columnNumber: 13
  } }, "Análise Cognitiva")), /* @__PURE__ */ React.createElement("div", { className: "insights-content", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1889,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: "insight-item", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1890,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: "insight-label", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1891,
    columnNumber: 15
  } }, "Raciocínio Espacial:"), /* @__PURE__ */ React.createElement("span", { className: "insight-value", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1892,
    columnNumber: 15
  } }, analysisResults.spatialReasoning || "Em análise")), /* @__PURE__ */ React.createElement("div", { className: "insight-item", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1894,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: "insight-label", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1895,
    columnNumber: 15
  } }, "Estratégia:"), /* @__PURE__ */ React.createElement("span", { className: "insight-value", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1896,
    columnNumber: 15
  } }, analysisResults.strategy || problemSolvingApproach)), /* @__PURE__ */ React.createElement("div", { className: "insight-item", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1898,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: "insight-label", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1899,
    columnNumber: 15
  } }, "Padrão Cognitivo:"), /* @__PURE__ */ React.createElement("span", { className: "insight-value", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1900,
    columnNumber: 15
  } }, analysisResults.cognitivePattern || "Identificando...")))), /* @__PURE__ */ React.createElement("div", { className: styles.gameContent, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1906,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.gameHeader, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1908,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("h1", { className: styles.gameTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1909,
    columnNumber: 11
  } }, "🧩 Quebra-Cabeça Emocional V3", /* @__PURE__ */ React.createElement("div", { style: {
    fontSize: "0.7rem",
    opacity: 0.8,
    marginTop: "0.25rem"
  }, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1911,
    columnNumber: 13
  } }, ACTIVITY_TYPES[gameState.currentActivity?.toUpperCase()]?.name || "Emocional")), /* @__PURE__ */ React.createElement("button", { className: `${styles.headerTtsButton} ${ttsActive2 ? styles.ttsActive : ""}`, onClick: toggleTTS, title: ttsActive2 ? "Desativar TTS" : "Ativar TTS", "aria-label": ttsActive2 ? "Desativar TTS" : "Ativar TTS", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1915,
    columnNumber: 11
  } }, ttsActive2 ? "🔊" : "🔇")), /* @__PURE__ */ React.createElement("div", { className: styles.gameStats, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1926,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statCard, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1927,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statValue, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1928,
    columnNumber: 13
  } }, gameState.score), /* @__PURE__ */ React.createElement("div", { className: styles.statLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1929,
    columnNumber: 13
  } }, "Pontos")), /* @__PURE__ */ React.createElement("div", { className: styles.statCard, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1931,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statValue, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1932,
    columnNumber: 13
  } }, gameState.round), /* @__PURE__ */ React.createElement("div", { className: styles.statLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1933,
    columnNumber: 13
  } }, "Rodada")), /* @__PURE__ */ React.createElement("div", { className: styles.statCard, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1935,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statValue, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1936,
    columnNumber: 13
  } }, gameState.accuracy, "%"), /* @__PURE__ */ React.createElement("div", { className: styles.statLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1937,
    columnNumber: 13
  } }, "Precisão")), /* @__PURE__ */ React.createElement("div", { className: styles.statCard, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1939,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statValue, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1940,
    columnNumber: 13
  } }, gameState.level), /* @__PURE__ */ React.createElement("div", { className: styles.statLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1941,
    columnNumber: 13
  } }, "Nível"))), /* @__PURE__ */ React.createElement("div", { className: styles.activityMenu, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1946,
    columnNumber: 9
  } }, Object.values(ACTIVITY_TYPES).map((activity) => /* @__PURE__ */ React.createElement("button", { key: activity.id, className: `${styles.activityButton} ${gameState.currentActivity === activity.id ? styles.active : ""}`, onClick: () => switchActivity(activity.id), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1948,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1955,
    columnNumber: 15
  } }, activity.icon), /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1956,
    columnNumber: 15
  } }, activity.name)))), gameState.currentActivity === ACTIVITY_TYPES.EMOTION_PUZZLE.id && renderEmotionPuzzle(), gameState.currentActivity === ACTIVITY_TYPES.PIECE_ROTATION.id && renderPieceRotation(), gameState.currentActivity === ACTIVITY_TYPES.PATTERN_PUZZLE.id && renderPatternPuzzle(), /* @__PURE__ */ React.createElement("div", { className: styles.gameControls, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1967,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("button", { className: styles.controlButton, onClick: () => speak("Quebra-cabeça emocional. Monte as peças para formar emoções e desenvolver inteligência emocional."), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1968,
    columnNumber: 11
  } }, "🔊 Explicar"), /* @__PURE__ */ React.createElement("button", { className: styles.controlButton, onClick: () => setShowStartScreen(true), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1971,
    columnNumber: 11
  } }, "🔄 Reiniciar"), /* @__PURE__ */ React.createElement("button", { className: styles.controlButton, onClick: onBack, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1974,
    columnNumber: 11
  } }, "⬅️ Voltar"))));
}
const QuebraCabecaGame$1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: QuebraCabecaGame
}, Symbol.toStringTag, { value: "Module" }));
export {
  QuebraCabecaProcessors as Q,
  QuebraCabecaCollectorsHub as a,
  QuebraCabecaGame$1 as b
};
//# sourceMappingURL=game-puzzle-Bb-eyook.js.map
