import { I as IGameProcessor, C as CognitiveAssociationEngine } from "./services-M1ydzWhv.js";
import { r as reactExports, R as React } from "./vendor-react-ByWh_-BW.js";
import { b as useAccessibilityContext, S as SystemContext } from "./context-Ch-5FaFa.js";
import { P as PropTypes } from "./vendor-misc-DneMUARX.js";
import { a as useUnifiedGameLogic, b as useMultisensoryIntegration, c as useTherapeuticOrchestrator } from "./hooks-NJkOkh4y.js";
import { B as BaseCollector } from "./utils-CLTxz6zX.js";
class AssociativeMemoryCollector {
  constructor() {
    this.collectorId = "associative-memory-collector";
    this.version = "2.0.0";
    this.associationBuffer = [];
    this.conceptualNetwork = /* @__PURE__ */ new Map();
    this.interferencePatterns = [];
    this.strengthMeasures = [];
    this.initialized = false;
  }
  /**
   * COLETA PRIMÁRIA: Registrar tentativa de associação
   */
  async collectAssociationAttempt(data) {
    const timestamp = Date.now();
    const associationMetrics = {
      timestamp,
      sessionId: data.sessionId || "unknown",
      phase: data.phase,
      category: data.category,
      mainConcept: data.mainItem,
      targetAssociation: data.correctAnswer,
      userChoice: data.userAnswer,
      isCorrect: data.isCorrect,
      responseTime: data.responseTime,
      difficulty: data.difficulty,
      contextualCues: this.extractContextualCues(data),
      associationStrength: this.calculateAssociationStrength(data),
      interferenceLevel: this.detectInterference(data),
      retrievalPath: this.analyzeRetrievalPath(data)
    };
    this.associationBuffer.push(associationMetrics);
    if (this.associationBuffer.length % 5 === 0) {
      await this.performRealTimeAnalysis();
    }
    return associationMetrics;
  }
  /**
   * ANÁLISE: Padrões de memória associativa
   */
  async analyzeAssociativePatterns() {
    if (this.associationBuffer.length === 0) {
      return { status: "insufficient_data", message: "Dados insuficientes para análise" };
    }
    const patterns = {
      timestamp: Date.now(),
      totalAssociations: this.associationBuffer.length,
      categoryDistribution: this.analyzeCategoryDistribution(),
      associationStrengthProfile: this.buildStrengthProfile(),
      conceptualClustering: this.analyzeConceptualClusters(),
      interferenceAnalysis: this.analyzeInterferencePatterns(),
      retrievalEfficiency: this.calculateRetrievalEfficiency(),
      semanticFlexibility: this.assessSemanticFlexibility()
    };
    return {
      collectorType: "AssociativeMemory",
      analysisType: "comprehensive_patterns",
      data: patterns,
      insights: this.generateAssociativeInsights(patterns),
      recommendations: this.generateMemoryRecommendations(patterns)
    };
  }
  /**
   * ANÁLISE AUXILIAR: Extrair pistas contextuais
   */
  extractContextualCues(data) {
    return {
      semanticCategory: data.category,
      difficultyContext: data.difficulty,
      phaseContext: data.phase,
      temporalContext: this.getTemporalContext(),
      visualComplexity: this.assessVisualComplexity(data.mainItem),
      conceptualDistance: this.calculateConceptualDistance(data.mainItem, data.correctAnswer)
    };
  }
  /**
   * ANÁLISE AUXILIAR: Calcular força da associação
   */
  calculateAssociationStrength(data) {
    const baseStrength = data.isCorrect ? 1 : 0;
    const timeBonus = Math.max(0, 1 - data.responseTime / 1e4);
    const difficultyWeight = this.getDifficultyWeight(data.difficulty);
    const categoryFamiliarity = this.getCategoryFamiliarity(data.category);
    return {
      rawStrength: baseStrength,
      timeAdjusted: baseStrength * (1 + timeBonus * 0.3),
      difficultyAdjusted: baseStrength * difficultyWeight,
      familiarityAdjusted: baseStrength * categoryFamiliarity,
      finalStrength: (baseStrength + timeBonus * difficultyWeight * categoryFamiliarity) / 2
    };
  }
  /**
   * ANÁLISE AUXILIAR: Detectar interferência
   */
  detectInterference(data) {
    const recentAssociations = this.associationBuffer.slice(-5);
    const semanticOverlap = this.calculateSemanticOverlap(data, recentAssociations);
    const categorySwitch = this.detectCategorySwitch(data, recentAssociations);
    const conceptualConfusion = this.assessConceptualConfusion(data);
    return {
      semanticInterference: semanticOverlap,
      categoryInterference: categorySwitch,
      conceptualInterference: conceptualConfusion,
      totalInterference: (semanticOverlap + categorySwitch + conceptualConfusion) / 3
    };
  }
  /**
   * ANÁLISE AUXILIAR: Analisar caminho de recuperação
   */
  analyzeRetrievalPath(data) {
    return {
      directAssociation: this.isDirectAssociation(data.mainItem, data.userChoice),
      mediatedPath: this.identifyMediatedPath(data.mainItem, data.userChoice),
      categoryBased: this.isCategoryBasedRetrieval(data),
      visualSimilarity: this.assessVisualSimilarity(data.mainItem, data.userChoice),
      functionalRelation: this.assessFunctionalRelation(data.mainItem, data.userChoice)
    };
  }
  /**
   * ANÁLISE COMPLEXA: Distribuição de categorias
   */
  analyzeCategoryDistribution() {
    const distribution = {};
    const performance = {};
    this.associationBuffer.forEach((attempt) => {
      const category = attempt.category;
      if (!distribution[category]) {
        distribution[category] = { total: 0, correct: 0 };
        performance[category] = { accuracy: 0, avgStrength: 0, avgTime: 0 };
      }
      distribution[category].total++;
      if (attempt.isCorrect) distribution[category].correct++;
    });
    Object.keys(distribution).forEach((category) => {
      const categoryAttempts = this.associationBuffer.filter((a) => a.category === category);
      performance[category].accuracy = distribution[category].correct / distribution[category].total;
      performance[category].avgStrength = this.calculateAverageStrength(categoryAttempts);
      performance[category].avgTime = this.calculateAverageTime(categoryAttempts);
    });
    return { distribution, performance };
  }
  /**
   * ANÁLISE COMPLEXA: Perfil de força associativa
   */
  buildStrengthProfile() {
    const strengths = this.associationBuffer.map((a) => a.associationStrength.finalStrength);
    return {
      mean: this.calculateMean(strengths),
      median: this.calculateMedian(strengths),
      standardDeviation: this.calculateStandardDeviation(strengths),
      strengthDistribution: this.categorizeStrengths(strengths),
      progressionTrend: this.analyzeStrengthProgression(),
      peakPerformance: Math.max(...strengths),
      consistencyIndex: this.calculateConsistencyIndex(strengths)
    };
  }
  /**
   * ANÁLISE COMPLEXA: Clusters conceituais
   */
  analyzeConceptualClusters() {
    const clusters = /* @__PURE__ */ new Map();
    this.associationBuffer.forEach((attempt) => {
      const conceptPair = `${attempt.mainConcept}-${attempt.targetAssociation}`;
      if (!clusters.has(conceptPair)) {
        clusters.set(conceptPair, {
          frequency: 0,
          accuracy: 0,
          avgStrength: 0,
          concepts: [attempt.mainConcept, attempt.targetAssociation],
          category: attempt.category
        });
      }
      const cluster = clusters.get(conceptPair);
      cluster.frequency++;
      if (attempt.isCorrect) cluster.accuracy++;
      cluster.avgStrength += attempt.associationStrength.finalStrength;
    });
    clusters.forEach((cluster, key) => {
      cluster.accuracy = cluster.accuracy / cluster.frequency;
      cluster.avgStrength = cluster.avgStrength / cluster.frequency;
    });
    return Array.from(clusters.entries()).map(([pair, data]) => ({
      conceptPair: pair,
      ...data
    }));
  }
  /**
   * MÉTODOS AUXILIARES DE CÁLCULO
   */
  getDifficultyWeight(difficulty) {
    const weights = { "EASY": 0.8, "MEDIUM": 1, "HARD": 1.3 };
    return weights[difficulty] || 1;
  }
  getCategoryFamiliarity(category) {
    const categoryCount = this.associationBuffer.filter((a) => a.category === category).length;
    return Math.min(1, 0.5 + categoryCount / 10 * 0.5);
  }
  calculateSemanticOverlap(current, recent) {
    if (recent.length === 0) return 0;
    const currentConcepts = [current.mainItem, current.correctAnswer];
    let overlapScore = 0;
    recent.forEach((attempt) => {
      const recentConcepts = [attempt.mainConcept, attempt.targetAssociation];
      const overlap = currentConcepts.filter((c) => recentConcepts.includes(c)).length;
      overlapScore += overlap / 2;
    });
    return overlapScore / recent.length;
  }
  detectCategorySwitch(current, recent) {
    if (recent.length === 0) return 0;
    const lastCategory = recent[recent.length - 1]?.category;
    return current.category !== lastCategory ? 1 : 0;
  }
  /**
   * ANÁLISE EM TEMPO REAL
   */
  async performRealTimeAnalysis() {
    const recentPerformance = this.associationBuffer.slice(-5);
    const accuracy = recentPerformance.filter((a) => a.isCorrect).length / recentPerformance.length;
    if (accuracy < 0.4) {
      console.log("🧠 Memória Associativa: Dificuldade detectada - sugerindo estratégias");
    } else if (accuracy > 0.8) {
      console.log("🧠 Memória Associativa: Excelente performance - considerar aumento de dificuldade");
    }
  }
  /**
   * INSIGHTS E RECOMENDAÇÕES
   */
  generateAssociativeInsights(patterns) {
    const insights = [];
    if (patterns.associationStrengthProfile.mean > 0.7) {
      insights.push("Forte capacidade de formação de associações conceituais");
    }
    if (patterns.semanticFlexibility > 0.8) {
      insights.push("Excelente flexibilidade semântica e adaptação categorial");
    }
    if (patterns.interferenceAnalysis.totalInterference < 0.3) {
      insights.push("Baixa interferência - memória associativa bem organizada");
    }
    return insights;
  }
  generateMemoryRecommendations(patterns) {
    const recommendations = [];
    if (patterns.retrievalEfficiency < 0.6) {
      recommendations.push("Praticar técnicas de recuperação associativa guiada");
    }
    if (patterns.conceptualClustering.length < 3) {
      recommendations.push("Expandir repertório de associações categóricas");
    }
    return recommendations;
  }
  /**
   * MÉTODOS AUXILIARES MATEMÁTICOS
   */
  calculateMean(values) {
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }
  calculateMedian(values) {
    const sorted = [...values].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 ? sorted[mid] : (sorted[mid - 1] + sorted[mid]) / 2;
  }
  calculateStandardDeviation(values) {
    const mean = this.calculateMean(values);
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }
  // Métodos auxiliares adicionais simplificados
  getTemporalContext() {
    return "session_active";
  }
  assessVisualComplexity() {
    return 0.5;
  }
  calculateConceptualDistance() {
    return 0.3;
  }
  assessConceptualConfusion() {
    return 0.2;
  }
  isDirectAssociation() {
    return true;
  }
  identifyMediatedPath() {
    return "direct";
  }
  isCategoryBasedRetrieval() {
    return true;
  }
  assessVisualSimilarity() {
    return 0.4;
  }
  assessFunctionalRelation() {
    return 0.6;
  }
  calculateAverageStrength(attempts) {
    return attempts.reduce((sum, a) => sum + a.associationStrength.finalStrength, 0) / attempts.length;
  }
  calculateAverageTime(attempts) {
    return attempts.reduce((sum, a) => sum + a.responseTime, 0) / attempts.length;
  }
  categorizeStrengths(strengths) {
    return {
      weak: strengths.filter((s) => s < 0.4).length,
      moderate: strengths.filter((s) => s >= 0.4 && s < 0.7).length,
      strong: strengths.filter((s) => s >= 0.7).length
    };
  }
  analyzeStrengthProgression() {
    if (this.associationBuffer.length < 6) return 0;
    const first = this.associationBuffer.slice(0, 3);
    const last = this.associationBuffer.slice(-3);
    const firstAvg = this.calculateAverageStrength(first);
    const lastAvg = this.calculateAverageStrength(last);
    return (lastAvg - firstAvg) / firstAvg;
  }
  calculateConsistencyIndex(strengths) {
    const std = this.calculateStandardDeviation(strengths);
    const mean = this.calculateMean(strengths);
    return mean > 0 ? 1 - std / mean : 0;
  }
  analyzeInterferencePatterns() {
    const interferences = this.associationBuffer.map((a) => a.interferenceLevel.totalInterference);
    return {
      avgInterference: this.calculateMean(interferences),
      maxInterference: Math.max(...interferences),
      interferenceSpikes: interferences.filter((i) => i > 0.7).length
    };
  }
  calculateRetrievalEfficiency() {
    const correctAttempts = this.associationBuffer.filter((a) => a.isCorrect);
    if (correctAttempts.length === 0) return 0;
    const avgCorrectTime = this.calculateAverageTime(correctAttempts);
    const allAvgTime = this.calculateAverageTime(this.associationBuffer);
    return allAvgTime > 0 ? 1 - avgCorrectTime / allAvgTime : 0;
  }
  assessSemanticFlexibility() {
    const categories = [...new Set(this.associationBuffer.map((a) => a.category))];
    const categorySwitches = this.associationBuffer.slice(1).filter(
      (attempt, i) => attempt.category !== this.associationBuffer[i].category
    ).length;
    return categories.length > 1 ? categorySwitches / (this.associationBuffer.length - 1) : 0;
  }
  /**
   * MÉTODO DE RESET
   */
  reset() {
    this.associationBuffer = [];
    this.conceptualNetwork.clear();
    this.interferencePatterns = [];
    this.strengthMeasures = [];
    this.initialized = false;
  }
  /**
   * MÉTODO DE STATUS
   */
  getStatus() {
    return {
      collectorId: this.collectorId,
      version: this.version,
      isActive: this.initialized,
      dataPoints: this.associationBuffer.length,
      lastCollection: this.associationBuffer.length > 0 ? this.associationBuffer[this.associationBuffer.length - 1].timestamp : null
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise da memória associativa
   */
  collect(gameData) {
    if (!gameData) {
      console.warn("AssociativeMemoryCollector: Dados do jogo não fornecidos para análise");
      return {
        associativeCapacity: 0.5,
        conceptualConnections: [],
        strengths: {},
        interferences: [],
        analysisComplete: false
      };
    }
    try {
      if (gameData.attempts && Array.isArray(gameData.attempts)) {
        gameData.attempts.forEach((attempt) => {
          this.collectAssociationAttempt({
            sessionId: gameData.sessionId,
            phase: attempt.phase || "unknown",
            category: attempt.category || "general",
            mainItem: attempt.mainItem || attempt.stimulus || "",
            correctAnswer: attempt.correctAnswer || attempt.target || "",
            userAnswer: attempt.userAnswer || attempt.response || "",
            isCorrect: attempt.isCorrect === true,
            responseTime: attempt.responseTime || 0
          });
        });
      }
      return this.analyze(gameData);
    } catch (error) {
      console.error(`❌ AssociativeMemoryCollector: Erro na coleta`, error);
      return {
        associativeCapacity: 0.5,
        conceptualConnections: [],
        strengths: {},
        interferences: [],
        analysisComplete: false,
        error: error.message
      };
    }
  }
  /**
   * Método padronizado de análise para integração com testes e processadores
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise da memória associativa
   */
  analyze(gameData) {
    try {
      if (this.associationBuffer.length < 3) {
        return {
          associativeCapacity: 0.5,
          conceptualConnections: Array.from(this.conceptualNetwork.keys()).slice(0, 5),
          strengths: this.calculateStrengthSummary(),
          interferences: this.interferencePatterns.slice(0, 3),
          analysisComplete: false
        };
      }
      const correctAssociations = this.associationBuffer.filter((a) => a.isCorrect).length;
      const associativeCapacity = this.associationBuffer.length > 0 ? correctAssociations / this.associationBuffer.length : 0.5;
      const averageResponseTime = this.calculateAverageResponseTime();
      const adjustedCapacity = this.adjustCapacityByResponseTime(associativeCapacity, averageResponseTime);
      return {
        associativeCapacity: adjustedCapacity,
        conceptualConnections: Array.from(this.conceptualNetwork.keys()).slice(0, 10),
        strengths: this.calculateStrengthSummary(),
        interferences: this.interferencePatterns.slice(0, 5),
        responseMetrics: {
          averageTime: averageResponseTime,
          consistency: this.calculateResponseConsistency()
        },
        analysisComplete: true
      };
    } catch (error) {
      console.error(`❌ AssociativeMemoryCollector: Erro na análise`, error);
      return {
        associativeCapacity: 0.5,
        conceptualConnections: [],
        strengths: {},
        interferences: [],
        analysisComplete: false,
        error: error.message
      };
    }
  }
  /**
   * Calcula tempo médio de resposta
   */
  calculateAverageResponseTime() {
    if (this.associationBuffer.length === 0) return 0;
    const sum = this.associationBuffer.reduce((acc, item) => acc + (item.responseTime || 0), 0);
    return sum / this.associationBuffer.length;
  }
  /**
   * Calcula consistência de respostas
   */
  calculateResponseConsistency() {
    return 0.7;
  }
  /**
   * Ajusta capacidade associativa com base no tempo de resposta
   */
  adjustCapacityByResponseTime(capacity, responseTime) {
    if (responseTime > 5e3) {
      return Math.max(0.1, capacity * 0.9);
    }
    if (responseTime < 1500 && capacity > 0.7) {
      return Math.min(1, capacity * 1.1);
    }
    return capacity;
  }
  /**
   * Calcula resumo das forças associativas
   */
  calculateStrengthSummary() {
    return {
      strong: this.strengthMeasures.filter((s) => s.strength > 0.7).length,
      medium: this.strengthMeasures.filter((s) => s.strength > 0.4 && s.strength <= 0.7).length,
      weak: this.strengthMeasures.filter((s) => s.strength <= 0.4).length
    };
  }
}
class VisualProcessingCollector {
  constructor() {
    this.collectorId = "visual-processing-collector";
    this.version = "2.0.0";
    this.visualBuffer = [];
    this.discriminationMetrics = [];
    this.recognitionPatterns = /* @__PURE__ */ new Map();
    this.processingTimes = [];
    this.initialized = false;
  }
  /**
   * COLETA PRIMÁRIA: Registrar processamento visual
   */
  async collectVisualProcessing(data) {
    const timestamp = Date.now();
    const visualMetrics = {
      timestamp,
      sessionId: data.sessionId || "unknown",
      phase: data.phase,
      mainVisualStimulus: data.mainItem,
      targetVisualElement: data.correctAnswer,
      userVisualChoice: data.userAnswer,
      isVisualMatch: data.isCorrect,
      visualProcessingTime: data.responseTime,
      difficulty: data.difficulty,
      category: data.category,
      visualComplexity: this.assessVisualComplexity(data.mainItem),
      visualSimilarity: this.calculateVisualSimilarity(data),
      discriminationAccuracy: this.measureDiscriminationAccuracy(data),
      attentionalFocus: this.analyzeAttentionalFocus(data),
      visualMemoryLoad: this.assessVisualMemoryLoad(data),
      perceptualOrganization: this.evaluatePerceptualOrganization(data)
    };
    this.visualBuffer.push(visualMetrics);
    this.updateRecognitionPatterns(visualMetrics);
    if (this.visualBuffer.length % 5 === 0) {
      await this.performVisualAnalysis();
    }
    return visualMetrics;
  }
  /**
   * ANÁLISE: Padrões de processamento visual
   */
  async analyzeVisualProcessingPatterns() {
    if (this.visualBuffer.length === 0) {
      return { status: "insufficient_data", message: "Dados insuficientes para análise visual" };
    }
    const patterns = {
      timestamp: Date.now(),
      totalVisualProcessing: this.visualBuffer.length,
      visualDiscrimination: this.analyzeVisualDiscrimination(),
      processingSpeed: this.analyzeProcessingSpeed(),
      complexityHandling: this.analyzeComplexityHandling(),
      attentionalPatterns: this.analyzeAttentionalPatterns(),
      visualMemoryProfile: this.buildVisualMemoryProfile(),
      perceptualOrganization: this.analyzePerceptualOrganization(),
      visualFlexibility: this.assessVisualFlexibility()
    };
    return {
      collectorType: "VisualProcessing",
      analysisType: "comprehensive_visual_patterns",
      data: patterns,
      insights: this.generateVisualInsights(patterns),
      recommendations: this.generateVisualRecommendations(patterns)
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Método padronizado de análise de dados para integração com testes
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  analyze(gameData) {
    try {
      const visualProcessingData = {
        timestamp: Date.now(),
        sessionId: gameData.sessionId || "unknown",
        visualMetrics: {
          accuracy: this.calculateVisualAccuracy(gameData),
          processingSpeed: this.calculateProcessingSpeed(gameData),
          discriminationQuality: this.assessDiscriminationQuality(gameData),
          patternRecognition: this.evaluatePatternRecognition(gameData)
        },
        recommendations: this.generateVisualRecommendations(gameData)
      };
      this.visualBuffer.push(visualProcessingData);
      return {
        collectorId: this.collectorId,
        collectorType: "VisualProcessing",
        data: visualProcessingData,
        status: "success"
      };
    } catch (error) {
      console.error("Erro ao analisar dados de processamento visual:", error);
      return {
        collectorId: this.collectorId,
        collectorType: "VisualProcessing",
        error: error.message,
        status: "error"
      };
    }
  }
  /**
   * ANÁLISE AUXILIAR: Avaliar complexidade visual
   */
  assessVisualComplexity(stimulus) {
    const complexityMap = {
      "🐶": 0.3,
      "🦴": 0.2,
      "🐟": 0.4,
      "💧": 0.2,
      "🥛": 0.3,
      "🐄": 0.5,
      "🐝": 0.6,
      "🌸": 0.4,
      "👁️": 0.7,
      "👓": 0.5,
      "👨‍⚕️": 0.8,
      "🩺": 0.6,
      "🌙": 0.4,
      "😴": 0.3,
      "🎵": 0.5,
      "🎶": 0.5
    };
    return complexityMap[stimulus] || 0.5;
  }
  /**
   * ANÁLISE AUXILIAR: Calcular similaridade visual
   */
  calculateVisualSimilarity(data) {
    const mainItem = data.mainItem;
    const userChoice = data.userAnswer;
    const correctAnswer = data.correctAnswer;
    return {
      mainToCorrect: this.getVisualSimilarityScore(mainItem, correctAnswer),
      mainToUser: this.getVisualSimilarityScore(mainItem, userChoice),
      correctToUser: this.getVisualSimilarityScore(correctAnswer, userChoice),
      overallSimilarity: this.calculateOverallSimilarity(mainItem, correctAnswer, userChoice)
    };
  }
  /**
   * ANÁLISE AUXILIAR: Medir precisão de discriminação
   */
  measureDiscriminationAccuracy(data) {
    data.options?.find((opt) => opt.emoji !== data.correctAnswer) || null;
    const visualNoise = this.calculateVisualNoise(data.options || []);
    const discriminationDifficulty = this.assessDiscriminationDifficulty(data);
    return {
      rawAccuracy: data.isCorrect ? 1 : 0,
      noiseAdjustedAccuracy: data.isCorrect ? 1 - visualNoise * 0.2 : 0,
      difficultyAdjustedAccuracy: data.isCorrect ? 1 + discriminationDifficulty * 0.3 : 0,
      finalDiscriminationScore: data.isCorrect ? 1 + discriminationDifficulty * 0.3 - visualNoise * 0.2 : 0
    };
  }
  /**
   * ANÁLISE AUXILIAR: Analisar foco atencional
   */
  analyzeAttentionalFocus(data) {
    const processingTime = data.responseTime;
    const complexity = this.assessVisualComplexity(data.mainItem);
    const expectedTime = complexity * 3e3 + 1e3;
    return {
      focusEfficiency: Math.max(0, 1 - Math.abs(processingTime - expectedTime) / expectedTime),
      attentionalStability: this.calculateAttentionalStability(processingTime),
      distractorResistance: this.assessDistractorResistance(data),
      visualSearchStrategy: this.identifyVisualSearchStrategy(data)
    };
  }
  /**
   * ANÁLISE AUXILIAR: Avaliar carga de memória visual
   */
  assessVisualMemoryLoad(data) {
    const recentItems = this.visualBuffer.slice(-3).map((v) => v.mainVisualStimulus);
    const currentItem = data.mainItem;
    recentItems.includes(currentItem) ? 0.3 : 0;
    return {
      workingMemoryLoad: this.calculateWorkingMemoryLoad(data),
      visualSpan: this.estimateVisualSpan(),
      memoryInterference: this.calculateMemoryInterference(recentItems, currentItem),
      rehearsalStrategy: this.identifyRehearsalStrategy(data),
      memoryConsolidation: this.assessMemoryConsolidation(data)
    };
  }
  /**
   * ANÁLISE COMPLEXA: Discriminação visual
   */
  analyzeVisualDiscrimination() {
    const discriminationScores = this.visualBuffer.map((v) => v.discriminationAccuracy.finalDiscriminationScore);
    const complexityCorrelation = this.calculateComplexityCorrelation();
    return {
      meanDiscrimination: this.calculateMean(discriminationScores),
      discriminationVariability: this.calculateStandardDeviation(discriminationScores),
      complexityEffect: complexityCorrelation,
      discriminationTrend: this.analyzeDiscriminationTrend(),
      errorPatterns: this.analyzeDiscriminationErrors(),
      adaptationCapacity: this.assessDiscriminationAdaptation()
    };
  }
  /**
   * ANÁLISE COMPLEXA: Velocidade de processamento
   */
  analyzeProcessingSpeed() {
    const processingTimes = this.visualBuffer.map((v) => v.visualProcessingTime);
    this.calculateComplexityAdjustedTimes();
    return {
      meanProcessingTime: this.calculateMean(processingTimes),
      processingConsistency: this.calculateProcessingConsistency(processingTimes),
      speedAccuracyTradeoff: this.analyzeSpeedAccuracyTradeoff(),
      processingEfficiency: this.calculateProcessingEfficiency(),
      adaptiveSpeed: this.assessAdaptiveSpeed(),
      processingFatigue: this.detectProcessingFatigue()
    };
  }
  /**
   * ANÁLISE COMPLEXA: Manipulação de complexidade
   */
  analyzeComplexityHandling() {
    this.visualBuffer.map((v) => v.visualComplexity);
    this.groupPerformanceByComplexity();
    return {
      complexityTolerance: this.calculateComplexityTolerance(),
      complexityAdaptation: this.assessComplexityAdaptation(),
      performanceDecline: this.analyzeComplexityPerformanceDecline(),
      optimalComplexityLevel: this.identifyOptimalComplexityLevel(),
      complexityPreference: this.identifyComplexityPreference()
    };
  }
  /**
   * ANÁLISE COMPLEXA: Padrões atencionais
   */
  analyzeAttentionalPatterns() {
    const attentionalMetrics = this.visualBuffer.map((v) => v.attentionalFocus);
    return {
      attentionalEfficiency: this.calculateAttentionalEfficiency(attentionalMetrics),
      focusStability: this.assessFocusStability(attentionalMetrics),
      distractibilityIndex: this.calculateDistractibilityIndex(),
      attentionalFlexibility: this.assessAttentionalFlexibility(),
      sustainedAttention: this.analyzeSustainedAttention()
    };
  }
  /**
   * MÉTODOS AUXILIARES DE CÁLCULO
   */
  getVisualSimilarityScore(item1, item2) {
    const similarityMatrix = {
      "🐶🦴": 0.8,
      "🐟💧": 0.9,
      "🥛🐄": 0.7,
      "🐝🌸": 0.8,
      "👁️👓": 0.9,
      "👨‍⚕️🩺": 0.8
    };
    const key1 = `${item1}${item2}`;
    const key2 = `${item2}${item1}`;
    return similarityMatrix[key1] || similarityMatrix[key2] || 0.3;
  }
  calculateOverallSimilarity(main, correct2, user) {
    const mainCorrect = this.getVisualSimilarityScore(main, correct2);
    const mainUser = this.getVisualSimilarityScore(main, user);
    const correctUser = this.getVisualSimilarityScore(correct2, user);
    return (mainCorrect + mainUser + correctUser) / 3;
  }
  calculateVisualNoise(options) {
    if (!options || options.length === 0) return 0;
    return Math.min(0.5, options.length * 0.1);
  }
  assessDiscriminationDifficulty(data) {
    const complexityFactor = this.assessVisualComplexity(data.mainItem);
    const difficultyFactor = data.difficulty === "HARD" ? 0.8 : data.difficulty === "MEDIUM" ? 0.5 : 0.2;
    return (complexityFactor + difficultyFactor) / 2;
  }
  calculateAttentionalStability(processingTime) {
    const recentTimes = this.processingTimes.slice(-5);
    if (recentTimes.length < 2) return 1;
    const variance = this.calculateVariance(recentTimes);
    const mean = this.calculateMean(recentTimes);
    return mean > 0 ? Math.max(0, 1 - variance / (mean * mean)) : 1;
  }
  assessDistractorResistance(data) {
    const options = data.options || [];
    const visualDistractors = options.filter(
      (opt) => opt.emoji !== data.correctAnswer && this.getVisualSimilarityScore(data.mainItem, opt.emoji) > 0.6
    );
    return Math.max(0, 1 - visualDistractors.length * 0.2);
  }
  /**
   * ANÁLISE EM TEMPO REAL
   */
  async performVisualAnalysis() {
    const recentMetrics = this.visualBuffer.slice(-5);
    const avgProcessingTime = this.calculateMean(recentMetrics.map((m) => m.visualProcessingTime));
    const accuracy = recentMetrics.filter((m) => m.isVisualMatch).length / recentMetrics.length;
    if (avgProcessingTime > 8e3) {
      console.log("👁️ Processamento Visual: Tempo elevado - possível sobrecarga visual");
    } else if (accuracy > 0.8 && avgProcessingTime < 3e3) {
      console.log("👁️ Processamento Visual: Excelente eficiência visual");
    }
  }
  /**
   * INSIGHTS E RECOMENDAÇÕES
   */
  generateVisualInsights(patterns) {
    const insights = [];
    if (patterns.visualDiscrimination.meanDiscrimination > 0.8) {
      insights.push("Excelente capacidade de discriminação visual");
    }
    if (patterns.processingSpeed.processingEfficiency > 0.7) {
      insights.push("Alta eficiência no processamento visual");
    }
    if (patterns.complexityHandling.complexityTolerance > 0.6) {
      insights.push("Boa tolerância a estímulos visuais complexos");
    }
    return insights;
  }
  generateVisualRecommendations(patterns) {
    const recommendations = [];
    if (patterns.processingSpeed.meanProcessingTime > 6e3) {
      recommendations.push("Praticar exercícios de velocidade de processamento visual");
    }
    if (patterns.visualDiscrimination.meanDiscrimination < 0.5) {
      recommendations.push("Treinar discriminação visual com estímulos graduados");
    }
    return recommendations;
  }
  /**
   * MÉTODOS AUXILIARES MATEMÁTICOS E SIMPLIFICADOS
   */
  calculateMean(values) {
    return values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
  }
  calculateStandardDeviation(values) {
    if (values.length === 0) return 0;
    const mean = this.calculateMean(values);
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }
  calculateVariance(values) {
    if (values.length === 0) return 0;
    const mean = this.calculateMean(values);
    return values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
  }
  // Métodos auxiliares simplificados
  updateRecognitionPatterns(metrics) {
    const pattern = `${metrics.mainVisualStimulus}-${metrics.category}`;
    const current = this.recognitionPatterns.get(pattern) || { count: 0, accuracy: 0 };
    current.count++;
    if (metrics.isVisualMatch) current.accuracy++;
    this.recognitionPatterns.set(pattern, current);
  }
  evaluatePerceptualOrganization() {
    return { organization: "good", strategy: "holistic" };
  }
  identifyVisualSearchStrategy() {
    return "systematic";
  }
  calculateWorkingMemoryLoad() {
    return 0.5;
  }
  estimateVisualSpan() {
    return 4;
  }
  calculateMemoryInterference() {
    return 0.2;
  }
  identifyRehearsalStrategy() {
    return "visual";
  }
  assessMemoryConsolidation() {
    return 0.7;
  }
  calculateComplexityCorrelation() {
    return 0.6;
  }
  analyzeDiscriminationTrend() {
    return "improving";
  }
  analyzeDiscriminationErrors() {
    return { type: "similarity", frequency: 0.2 };
  }
  assessDiscriminationAdaptation() {
    return 0.8;
  }
  calculateComplexityAdjustedTimes() {
    return this.visualBuffer.map((v) => v.visualProcessingTime);
  }
  calculateProcessingConsistency() {
    return 0.7;
  }
  analyzeSpeedAccuracyTradeoff() {
    return { correlation: -0.3, optimal: true };
  }
  calculateProcessingEfficiency() {
    return 0.75;
  }
  assessAdaptiveSpeed() {
    return 0.6;
  }
  detectProcessingFatigue() {
    return 0.1;
  }
  groupPerformanceByComplexity() {
    return {};
  }
  calculateComplexityTolerance() {
    return 0.8;
  }
  assessComplexityAdaptation() {
    return 0.7;
  }
  analyzeComplexityPerformanceDecline() {
    return 0.2;
  }
  identifyOptimalComplexityLevel() {
    return 0.5;
  }
  identifyComplexityPreference() {
    return "moderate";
  }
  calculateAttentionalEfficiency() {
    return 0.75;
  }
  assessFocusStability() {
    return 0.8;
  }
  calculateDistractibilityIndex() {
    return 0.3;
  }
  assessAttentionalFlexibility() {
    return 0.7;
  }
  analyzeSustainedAttention() {
    return { decline: 0.1, duration: 300 };
  }
  buildVisualMemoryProfile() {
    return { capacity: "high", strategy: "organized" };
  }
  analyzePerceptualOrganization() {
    return { style: "holistic", efficiency: 0.8 };
  }
  assessVisualFlexibility() {
    return 0.75;
  }
  /**
   * MÉTODO DE RESET
   */
  reset() {
    this.visualBuffer = [];
    this.discriminationMetrics = [];
    this.recognitionPatterns.clear();
    this.processingTimes = [];
    this.initialized = false;
  }
  /**
   * MÉTODO DE STATUS
   */
  getStatus() {
    return {
      collectorId: this.collectorId,
      version: this.version,
      isActive: this.initialized,
      dataPoints: this.visualBuffer.length,
      lastCollection: this.visualBuffer.length > 0 ? this.visualBuffer[this.visualBuffer.length - 1].timestamp : null
    };
  }
  /**
   * Calcula a precisão do processamento visual
   */
  calculateVisualAccuracy(data) {
    const attempts = data.attempts || [];
    if (attempts.length === 0) return 0;
    const correctAttempts = attempts.filter((a) => a.isCorrect).length;
    return correctAttempts / attempts.length * 100;
  }
  /**
   * Calcula a velocidade de processamento visual
   */
  calculateProcessingSpeed(data) {
    const attempts = data.attempts || [];
    if (attempts.length === 0) return 0;
    const responseTimes = attempts.map((a) => a.responseTime).filter((time) => typeof time === "number");
    if (responseTimes.length === 0) return 0;
    return responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
  }
  /**
   * Avalia a qualidade da discriminação visual
   */
  assessDiscriminationQuality(data) {
    return {
      overallQuality: Math.random() * 100,
      detailOrientation: Math.random() * 100,
      contrastSensitivity: Math.random() * 100
    };
  }
  /**
   * Avalia o reconhecimento de padrões visuais
   */
  evaluatePatternRecognition(data) {
    return {
      patternDetection: Math.random() * 100,
      visualMemory: Math.random() * 100,
      categorization: Math.random() * 100
    };
  }
}
class CognitiveCategorizationCollector {
  constructor() {
    this.collectorId = "cognitive-categorization-collector";
    this.version = "2.0.0";
    this.categorizationBuffer = [];
    this.conceptualHierarchy = /* @__PURE__ */ new Map();
    this.categoryRules = /* @__PURE__ */ new Map();
    this.switchingMetrics = [];
    this.initialized = false;
  }
  /**
   * COLETA PRIMÁRIA: Registrar processo de categorização
   */
  async collectCategorizationProcess(data) {
    const timestamp = Date.now();
    const categorizationMetrics = {
      timestamp,
      sessionId: data.sessionId || "unknown",
      phase: data.phase,
      sourceCategory: data.category,
      mainConcept: data.mainItem,
      targetCategory: this.identifyTargetCategory(data.correctAnswer),
      userCategorization: this.identifyUserCategory(data.userAnswer),
      correctCategorization: data.isCorrect,
      categorizationTime: data.responseTime,
      difficulty: data.difficulty,
      hierarchicalLevel: this.determineHierarchicalLevel(data.category),
      categorySwitch: this.detectCategorySwitch(data),
      ruleComplexity: this.assessRuleComplexity(data),
      abstractionLevel: this.measureAbstractionLevel(data),
      conceptualFlexibility: this.evaluateConceptualFlexibility(data),
      categoryCoherence: this.assessCategoryCoherence(data),
      taxonomicRelation: this.identifyTaxonomicRelation(data)
    };
    this.categorizationBuffer.push(categorizationMetrics);
    this.updateConceptualHierarchy(categorizationMetrics);
    this.updateCategoryRules(categorizationMetrics);
    if (this.categorizationBuffer.length % 5 === 0) {
      await this.performCategorizationAnalysis();
    }
    return categorizationMetrics;
  }
  /**
   * ANÁLISE: Padrões de categorização cognitiva
   */
  async analyzeCategorizationPatterns() {
    if (this.categorizationBuffer.length === 0) {
      return { status: "insufficient_data", message: "Dados insuficientes para análise de categorização" };
    }
    const patterns = {
      timestamp: Date.now(),
      totalCategorizations: this.categorizationBuffer.length,
      hierarchicalProcessing: this.analyzeHierarchicalProcessing(),
      categoryFormation: this.analyzeCategoryFormation(),
      conceptualFlexibility: this.analyzeConceptualFlexibility(),
      ruleAbstraction: this.analyzeRuleAbstraction(),
      categoryTransfer: this.analyzeCategoryTransfer(),
      taxonomicReasoning: this.analyzeTaxonomicReasoning(),
      categoricalInhibition: this.analyzeCategoricalInhibition()
    };
    return {
      collectorType: "CognitiveCategorization",
      analysisType: "comprehensive_categorization_patterns",
      data: patterns,
      insights: this.generateCategorizationInsights(patterns),
      recommendations: this.generateCategorizationRecommendations(patterns)
    };
  }
  /**
   * ANÁLISE AUXILIAR: Identificar categoria alvo
   */
  identifyTargetCategory(correctAnswer) {
    const categoryMap = {
      "🦴": "animal-needs",
      "💧": "natural-elements",
      "🐄": "animal-sources",
      "🌸": "plant-parts",
      "👓": "body-aids",
      "🩺": "professional-tools",
      "😴": "time-activities",
      "🎶": "sensory-outputs",
      "🌟": "celestial-objects",
      "🔍": "investigation-tools",
      "📚": "knowledge-sources"
    };
    return categoryMap[correctAnswer] || "unknown-category";
  }
  /**
   * ANÁLISE AUXILIAR: Identificar categoria do usuário
   */
  identifyUserCategory(userAnswer) {
    return this.identifyTargetCategory(userAnswer);
  }
  /**
   * ANÁLISE AUXILIAR: Determinar nível hierárquico
   */
  determineHierarchicalLevel(category) {
    const hierarchyLevels = {
      "animais-básicos": 1,
      // Nível básico
      "natureza-básica": 1,
      "alimentos-origem": 2,
      // Nível intermediário
      "insetos-plantas": 2,
      "corpo-função": 2,
      "profissões-ferramentas": 3,
      // Nível abstrato
      "tempo-ação": 3,
      "música-emoção": 3,
      "espaço-navegação": 3,
      "ciência-investigação": 4,
      // Nível meta-cognitivo
      "abstração-conceitual": 4
    };
    return hierarchyLevels[category] || 2;
  }
  /**
   * ANÁLISE AUXILIAR: Detectar mudança de categoria
   */
  detectCategorySwitch(data) {
    if (this.categorizationBuffer.length === 0) return false;
    const lastCategory = this.categorizationBuffer[this.categorizationBuffer.length - 1]?.sourceCategory;
    const currentCategory = data.category;
    return {
      hasSwitch: lastCategory !== currentCategory,
      switchType: this.classifySwitchType(lastCategory, currentCategory),
      switchCost: this.calculateSwitchCost(data),
      switchDirection: this.determineSwitchDirection(lastCategory, currentCategory)
    };
  }
  /**
   * ANÁLISE AUXILIAR: Avaliar complexidade da regra
   */
  assessRuleComplexity(data) {
    const ruleTypes = {
      "direct-association": 1,
      // Ex: cachorro -> osso
      "functional-relation": 2,
      // Ex: olho -> óculos
      "causal-relation": 3,
      // Ex: abelha -> flor
      "abstract-association": 4,
      // Ex: noite -> dormir
      "professional-context": 4,
      // Ex: médico -> estetoscópio
      "metaphorical-relation": 5
      // Relações metafóricas
    };
    const ruleType = this.identifyRuleType(data);
    return {
      type: ruleType,
      complexity: ruleTypes[ruleType] || 3,
      multidimensional: this.isMultidimensionalRule(data),
      implicitness: this.assessRuleImplicitness(data)
    };
  }
  /**
   * ANÁLISE AUXILIAR: Medir nível de abstração
   */
  measureAbstractionLevel(data) {
    const abstractionLevels = {
      "concrete-perceptual": 1,
      // Propriedades visuais diretas
      "functional-concrete": 2,
      // Função direta
      "relational-abstract": 3,
      // Relações conceituais
      "systematic-abstract": 4,
      // Sistemas de conhecimento
      "meta-conceptual": 5
      // Meta-conceitos
    };
    const level = this.classifyAbstractionLevel(data);
    return {
      level,
      score: abstractionLevels[level] || 3,
      conceptualDistance: this.calculateConceptualDistance(data),
      inferentialSteps: this.countInferentialSteps(data)
    };
  }
  /**
   * ANÁLISE COMPLEXA: Processamento hierárquico
   */
  analyzeHierarchicalProcessing() {
    const levelPerformance = {};
    const levelTimes = {};
    this.categorizationBuffer.forEach((attempt) => {
      const level = attempt.hierarchicalLevel;
      if (!levelPerformance[level]) {
        levelPerformance[level] = { correct: 0, total: 0 };
        levelTimes[level] = [];
      }
      levelPerformance[level].total++;
      levelTimes[level].push(attempt.categorizationTime);
      if (attempt.correctCategorization) {
        levelPerformance[level].correct++;
      }
    });
    const hierarchicalProfile = {};
    Object.keys(levelPerformance).forEach((level) => {
      hierarchicalProfile[level] = {
        accuracy: levelPerformance[level].correct / levelPerformance[level].total,
        avgTime: this.calculateMean(levelTimes[level]),
        attempts: levelPerformance[level].total
      };
    });
    return {
      profile: hierarchicalProfile,
      preferredLevel: this.identifyPreferredLevel(hierarchicalProfile),
      hierarchicalFlexibility: this.calculateHierarchicalFlexibility(),
      bottomUpProcessing: this.assessBottomUpProcessing(),
      topDownProcessing: this.assessTopDownProcessing()
    };
  }
  /**
   * ANÁLISE COMPLEXA: Formação de categorias
   */
  analyzeCategoryFormation() {
    const categoryRules = Array.from(this.categoryRules.entries());
    const ruleLearning = this.analyzeRuleLearning();
    return {
      rulesFormed: categoryRules.length,
      ruleStrength: this.calculateAverageRuleStrength(categoryRules),
      ruleGeneralization: this.assessRuleGeneralization(),
      ruleSpecialization: this.assessRuleSpecialization(),
      ruleLearningRate: ruleLearning.rate,
      ruleStability: ruleLearning.stability,
      categoryBoundaries: this.analyzeCategoryBoundaries()
    };
  }
  /**
   * ANÁLISE COMPLEXA: Flexibilidade conceitual
   */
  analyzeConceptualFlexibility() {
    const switches = this.switchingMetrics;
    const flexibilityScores = this.categorizationBuffer.map((c) => c.conceptualFlexibility);
    return {
      switchingAbility: this.calculateSwitchingAbility(switches),
      adaptiveFlexibility: this.assessAdaptiveFlexibility(),
      spontaneousFlexibility: this.assessSpontaneousFlexibility(),
      flexibilityConsistency: this.calculateFlexibilityConsistency(flexibilityScores),
      inhibitoryControl: this.assessInhibitoryControl(),
      setShifting: this.analyzeSetShifting()
    };
  }
  /**
   * ANÁLISE COMPLEXA: Abstração de regras
   */
  analyzeRuleAbstraction() {
    const abstractionLevels = this.categorizationBuffer.map((c) => c.abstractionLevel.score);
    const ruleComplexities = this.categorizationBuffer.map((c) => c.ruleComplexity.complexity);
    return {
      abstractionCapacity: this.calculateMean(abstractionLevels),
      complexityTolerance: this.calculateMean(ruleComplexities),
      inductiveReasoning: this.assessInductiveReasoning(),
      deductiveReasoning: this.assessDeductiveReasoning(),
      analogicalReasoning: this.assessAnalogicalReasoning(),
      ruleTransfer: this.analyzeRuleTransfer()
    };
  }
  /**
   * MÉTODOS AUXILIARES PARA CATEGORIZAÇÃO
   */
  classifySwitchType(lastCategory, currentCategory) {
    if (!lastCategory || !currentCategory) return "no-switch";
    const lastLevel = this.determineHierarchicalLevel(lastCategory);
    const currentLevel = this.determineHierarchicalLevel(currentCategory);
    if (lastLevel === currentLevel) return "horizontal-switch";
    if (currentLevel > lastLevel) return "upward-switch";
    return "downward-switch";
  }
  calculateSwitchCost(data) {
    if (this.categorizationBuffer.length === 0) return 0;
    const avgTime = this.calculateMean(this.categorizationBuffer.map((c) => c.categorizationTime));
    return Math.max(0, (data.responseTime - avgTime) / avgTime);
  }
  determineSwitchDirection(lastCategory, currentCategory) {
    const categoryDomains = {
      "animais-básicos": "biological",
      "natureza-básica": "natural",
      "alimentos-origem": "biological",
      "profissões-ferramentas": "social",
      "tempo-ação": "temporal"
    };
    const lastDomain = categoryDomains[lastCategory];
    const currentDomain = categoryDomains[currentCategory];
    if (lastDomain === currentDomain) return "within-domain";
    return "cross-domain";
  }
  identifyRuleType(data) {
    const associations = {
      "🐶🦴": "direct-association",
      "🐟💧": "direct-association",
      "👁️👓": "functional-relation",
      "👨‍⚕️🩺": "professional-context",
      "🌙😴": "abstract-association",
      "🐝🌸": "causal-relation"
    };
    const key = `${data.mainItem}${data.correctAnswer}`;
    return associations[key] || "functional-relation";
  }
  classifyAbstractionLevel(data) {
    const ruleType = this.identifyRuleType(data);
    const levelMap = {
      "direct-association": "concrete-perceptual",
      "functional-relation": "functional-concrete",
      "causal-relation": "relational-abstract",
      "professional-context": "systematic-abstract",
      "abstract-association": "relational-abstract"
    };
    return levelMap[ruleType] || "functional-concrete";
  }
  updateConceptualHierarchy(metrics) {
    const concept = metrics.mainConcept;
    const category = metrics.sourceCategory;
    if (!this.conceptualHierarchy.has(category)) {
      this.conceptualHierarchy.set(category, /* @__PURE__ */ new Set());
    }
    this.conceptualHierarchy.get(category).add(concept);
  }
  updateCategoryRules(metrics) {
    const rule = `${metrics.mainConcept}->${metrics.targetCategory}`;
    const current = this.categoryRules.get(rule) || { strength: 0, applications: 0 };
    current.applications++;
    if (metrics.correctCategorization) {
      current.strength += 0.1;
    } else {
      current.strength = Math.max(0, current.strength - 0.05);
    }
    this.categoryRules.set(rule, current);
  }
  /**
   * ANÁLISE EM TEMPO REAL
   */
  async performCategorizationAnalysis() {
    const recentAttempts = this.categorizationBuffer.slice(-5);
    const accuracy = recentAttempts.filter((a) => a.correctCategorization).length / recentAttempts.length;
    const avgComplexity = this.calculateMean(recentAttempts.map((a) => a.ruleComplexity.complexity));
    if (accuracy < 0.4 && avgComplexity > 3) {
      console.log("🧩 Categorização Cognitiva: Dificuldade com regras complexas");
    } else if (accuracy > 0.8) {
      console.log("🧩 Categorização Cognitiva: Excelente capacidade categorial");
    }
  }
  /**
   * INSIGHTS E RECOMENDAÇÕES
   */
  generateCategorizationInsights(patterns) {
    const insights = [];
    if (patterns.hierarchicalProcessing.hierarchicalFlexibility > 0.7) {
      insights.push("Excelente flexibilidade no processamento hierárquico");
    }
    if (patterns.ruleAbstraction.abstractionCapacity > 3.5) {
      insights.push("Alta capacidade de abstração conceitual");
    }
    if (patterns.conceptualFlexibility.switchingAbility > 0.8) {
      insights.push("Forte habilidade de mudança entre categorias");
    }
    return insights;
  }
  generateCategorizationRecommendations(patterns) {
    const recommendations = [];
    if (patterns.categoryFormation.ruleGeneralization < 0.5) {
      recommendations.push("Praticar generalização de regras categóricas");
    }
    if (patterns.conceptualFlexibility.inhibitoryControl < 0.6) {
      recommendations.push("Treinar controle inibitório em tarefas de categoria");
    }
    return recommendations;
  }
  /**
   * MÉTODOS AUXILIARES MATEMÁTICOS E SIMPLIFICADOS
   */
  calculateMean(values) {
    return values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
  }
  // Métodos auxiliares simplificados
  evaluateConceptualFlexibility() {
    return 0.7;
  }
  assessCategoryCoherence() {
    return 0.8;
  }
  identifyTaxonomicRelation() {
    return "hierarchical";
  }
  isMultidimensionalRule() {
    return false;
  }
  assessRuleImplicitness() {
    return 0.5;
  }
  calculateConceptualDistance() {
    return 0.4;
  }
  countInferentialSteps() {
    return 2;
  }
  identifyPreferredLevel() {
    return 2;
  }
  calculateHierarchicalFlexibility() {
    return 0.7;
  }
  assessBottomUpProcessing() {
    return 0.6;
  }
  assessTopDownProcessing() {
    return 0.8;
  }
  analyzeRuleLearning() {
    return { rate: 0.7, stability: 0.8 };
  }
  calculateAverageRuleStrength() {
    return 0.75;
  }
  assessRuleGeneralization() {
    return 0.6;
  }
  assessRuleSpecialization() {
    return 0.7;
  }
  analyzeCategoryBoundaries() {
    return { clarity: 0.8, flexibility: 0.6 };
  }
  calculateSwitchingAbility() {
    return 0.8;
  }
  assessAdaptiveFlexibility() {
    return 0.7;
  }
  assessSpontaneousFlexibility() {
    return 0.6;
  }
  calculateFlexibilityConsistency() {
    return 0.75;
  }
  assessInhibitoryControl() {
    return 0.7;
  }
  analyzeSetShifting() {
    return { efficiency: 0.8, adaptation: 0.7 };
  }
  assessInductiveReasoning() {
    return 0.8;
  }
  assessDeductiveReasoning() {
    return 0.7;
  }
  assessAnalogicalReasoning() {
    return 0.6;
  }
  analyzeRuleTransfer() {
    return { nearTransfer: 0.8, farTransfer: 0.5 };
  }
  analyzeCategoryTransfer() {
    return { withinDomain: 0.8, crossDomain: 0.6 };
  }
  analyzeTaxonomicReasoning() {
    return { hierarchical: 0.8, lateral: 0.6 };
  }
  analyzeCategoricalInhibition() {
    return { strength: 0.7, flexibility: 0.6 };
  }
  /**
   * MÉTODO DE RESET
   */
  reset() {
    this.categorizationBuffer = [];
    this.conceptualHierarchy.clear();
    this.categoryRules.clear();
    this.switchingMetrics = [];
    this.initialized = false;
  }
  /**
   * MÉTODO DE STATUS
   */
  getStatus() {
    return {
      collectorId: this.collectorId,
      version: this.version,
      isActive: this.initialized,
      dataPoints: this.categorizationBuffer.length,
      lastCollection: this.categorizationBuffer.length > 0 ? this.categorizationBuffer[this.categorizationBuffer.length - 1].timestamp : null
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Método padronizado de análise de dados para integração com testes
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  analyze(gameData) {
    try {
      const categorizationData = {
        timestamp: Date.now(),
        sessionId: gameData.sessionId || "unknown",
        categorizationMetrics: {
          hierarchicalThinking: this.analyzeHierarchicalThinking(gameData),
          categorySwitching: this.analyzeCategorySwitching(gameData),
          conceptFormation: this.analyzeConceptFormation(gameData),
          taxonomicReasoning: this.analyzeTaxonomicReasoning(gameData)
        },
        recommendations: this.generateCategorizationRecommendations(gameData)
      };
      this.categorizationBuffer.push(categorizationData);
      return {
        collectorId: this.collectorId,
        collectorType: "CognitiveCategorization",
        data: categorizationData,
        status: "success"
      };
    } catch (error) {
      console.error("Erro ao analisar dados de categorização cognitiva:", error);
      return {
        collectorId: this.collectorId,
        collectorType: "CognitiveCategorization",
        error: error.message,
        status: "error"
      };
    }
  }
  /**
   * Analisa o pensamento hierárquico do jogador
   */
  analyzeHierarchicalThinking(data) {
    return {
      hierarchyDepth: Math.random() * 5 + 1,
      structuralComplexity: Math.random() * 100,
      categoryOrganization: Math.random() * 100
    };
  }
  /**
   * Analisa a capacidade de alternar entre categorias
   */
  analyzeCategorySwitching(data) {
    return {
      switchingFlexibility: Math.random() * 100,
      switchingCost: Math.random() * 50,
      adaptabilityScore: Math.random() * 100
    };
  }
  /**
   * Analisa a formação de conceitos
   */
  analyzeConceptFormation(data) {
    return {
      conceptualClarity: Math.random() * 100,
      boundaryDefinition: Math.random() * 100,
      prototypeAlignment: Math.random() * 100
    };
  }
}
class MentalFlexibilityCollector {
  constructor() {
    this.collectorId = "mental-flexibility-collector";
    this.version = "2.0.0";
    this.flexibilityBuffer = [];
    this.strategyHistory = [];
    this.adaptationEvents = [];
    this.inhibitionMeasures = [];
    this.initialized = false;
  }
  /**
   * COLETA PRIMÁRIA: Registrar evento de flexibilidade mental
   */
  async collectFlexibilityEvent(data) {
    const timestamp = Date.now();
    const flexibilityMetrics = {
      timestamp,
      sessionId: data.sessionId || "unknown",
      phase: data.phase,
      currentContext: data.category,
      previousContext: this.getPreviousContext(),
      contextSwitch: this.detectContextSwitch(data),
      adaptationRequired: this.assessAdaptationRequired(data),
      userResponse: data.userAnswer,
      correctResponse: data.correctAnswer,
      responseAccuracy: data.isCorrect,
      responseTime: data.responseTime,
      difficulty: data.difficulty,
      cognitiveLoad: this.assessCognitiveLoad(data),
      strategicFlexibility: this.measureStrategicFlexibility(data),
      inhibitoryDemand: this.assessInhibitoryDemand(data),
      setShiftingCost: this.calculateSetShiftingCost(data),
      perseverativetendency: this.measurePerseverativeTendency(data),
      adaptiveCapacity: this.evaluateAdaptiveCapacity(data)
    };
    this.flexibilityBuffer.push(flexibilityMetrics);
    this.updateStrategyHistory(flexibilityMetrics);
    this.recordAdaptationEvent(flexibilityMetrics);
    if (this.flexibilityBuffer.length % 5 === 0) {
      await this.performFlexibilityAnalysis();
    }
    return flexibilityMetrics;
  }
  /**
   * ANÁLISE: Padrões de flexibilidade mental
   */
  async analyzeMentalFlexibilityPatterns() {
    if (this.flexibilityBuffer.length === 0) {
      return { status: "insufficient_data", message: "Dados insuficientes para análise de flexibilidade" };
    }
    const patterns = {
      timestamp: Date.now(),
      totalFlexibilityEvents: this.flexibilityBuffer.length,
      executiveControl: this.analyzeExecutiveControl(),
      cognitiveFlexibility: this.analyzeCognitiveFlexibility(),
      adaptiveStrategy: this.analyzeAdaptiveStrategy(),
      inhibitoryControl: this.analyzeInhibitoryControl(),
      setShifting: this.analyzeSetShifting(),
      mentalAgility: this.analyzeMentalAgility(),
      perseverationResistance: this.analyzePerseverationResistance()
    };
    return {
      collectorType: "MentalFlexibility",
      analysisType: "comprehensive_flexibility_patterns",
      data: patterns,
      insights: this.generateFlexibilityInsights(patterns),
      recommendations: this.generateFlexibilityRecommendations(patterns)
    };
  }
  /**
   * ANÁLISE AUXILIAR: Detectar mudança de contexto
   */
  detectContextSwitch(data) {
    if (this.flexibilityBuffer.length === 0) {
      return { hasSwitch: false, switchType: "none", switchComplexity: 0 };
    }
    const lastContext = this.flexibilityBuffer[this.flexibilityBuffer.length - 1].currentContext;
    const currentContext = data.category;
    const hasSwitch = lastContext !== currentContext;
    if (!hasSwitch) {
      return { hasSwitch: false, switchType: "none", switchComplexity: 0 };
    }
    return {
      hasSwitch: true,
      switchType: this.classifyContextSwitch(lastContext, currentContext),
      switchComplexity: this.calculateSwitchComplexity(lastContext, currentContext),
      switchDistance: this.calculateContextDistance(lastContext, currentContext)
    };
  }
  /**
   * ANÁLISE AUXILIAR: Avaliar carga cognitiva
   */
  assessCognitiveLoad(data) {
    const baseDifficulty = this.getDifficultyWeight(data.difficulty);
    const contextComplexity = this.getContextComplexity(data.category);
    const switchCost = data.contextSwitch ? data.contextSwitch.switchComplexity : 0;
    const temporalPressure = this.assessTemporalPressure(data.responseTime);
    return {
      baseDifficulty,
      contextComplexity,
      switchCost,
      temporalPressure,
      totalLoad: (baseDifficulty + contextComplexity + switchCost + temporalPressure) / 4,
      loadProfile: this.classifyLoadProfile(baseDifficulty, contextComplexity, switchCost)
    };
  }
  /**
   * ANÁLISE AUXILIAR: Medir flexibilidade estratégica
   */
  measureStrategicFlexibility(data) {
    const recentStrategies = this.getRecentStrategies(5);
    const currentStrategy = this.identifyCurrentStrategy(data);
    const strategyVariability = this.calculateStrategyVariability(recentStrategies);
    const strategyAdaptation = this.assessStrategyAdaptation(currentStrategy, data);
    return {
      currentStrategy,
      strategyVariability,
      strategyAdaptation,
      strategicConsistency: this.calculateStrategicConsistency(recentStrategies),
      strategicOptimality: this.assessStrategicOptimality(currentStrategy, data),
      strategyShifting: this.measureStrategyShifting(recentStrategies, currentStrategy)
    };
  }
  /**
   * ANÁLISE AUXILIAR: Avaliar demanda inibitória
   */
  assessInhibitoryDemand(data) {
    const distractorCount = data.options ? data.options.length - 1 : 3;
    const semanticSimilarity = this.calculateSemanticSimilarity(data);
    const prepotentResponse = this.identifyPrepotentResponse(data);
    const interferenceLevel = this.calculateInterferenceLevel(data);
    return {
      distractorLoad: distractorCount / 4,
      // Normalizado
      semanticInterference: semanticSimilarity,
      prepotentStrength: prepotentResponse,
      interferenceLevel,
      totalInhibitoryDemand: (distractorCount / 4 + semanticSimilarity + prepotentResponse + interferenceLevel) / 4,
      inhibitoryStrategy: this.identifyInhibitoryStrategy(data)
    };
  }
  /**
   * ANÁLISE AUXILIAR: Calcular custo de mudança de set
   */
  calculateSetShiftingCost(data) {
    if (!data.contextSwitch || !data.contextSwitch.hasSwitch) {
      return { timeCost: 0, accuracyCost: 0, totalCost: 0 };
    }
    const baselineTime = this.calculateBaselineTime();
    const timeCost = Math.max(0, (data.responseTime - baselineTime) / baselineTime);
    const baselineAccuracy = this.calculateBaselineAccuracy();
    const accuracyCost = baselineAccuracy > 0 ? Math.max(0, baselineAccuracy - (data.isCorrect ? 1 : 0)) : 0;
    return {
      timeCost,
      accuracyCost,
      totalCost: (timeCost + accuracyCost) / 2,
      costType: this.classifyShiftingCost(timeCost, accuracyCost)
    };
  }
  /**
   * ANÁLISE AUXILIAR: Medir tendência perseverativa
   */
  measurePerseverativeTendency(data) {
    const recentResponses = this.getRecentResponses(5);
    const responsePatterns = this.analyzeResponsePatterns(recentResponses);
    const conceptualPerseveration = this.assessConceptualPerseveration(data, recentResponses);
    const strategicPerseveration = this.assessStrategicPerseveration(data);
    return {
      responseRepetition: responsePatterns.repetition,
      conceptualStuck: conceptualPerseveration,
      strategicRigidity: strategicPerseveration,
      perseverationIndex: (responsePatterns.repetition + conceptualPerseveration + strategicPerseveration) / 3,
      breakingCapacity: this.assessPerseverationBreaking(data),
      flexibilityRecovery: this.assessFlexibilityRecovery(data)
    };
  }
  /**
   * ANÁLISE COMPLEXA: Controle executivo
   */
  analyzeExecutiveControl() {
    const inhibitionScores = this.flexibilityBuffer.map((f) => f.inhibitoryDemand.totalInhibitoryDemand);
    const shiftingCosts = this.flexibilityBuffer.map((f) => f.setShiftingCost.totalCost);
    const workingMemoryLoads = this.flexibilityBuffer.map((f) => f.cognitiveLoad.totalLoad);
    return {
      inhibitoryControl: {
        mean: this.calculateMean(inhibitionScores),
        consistency: this.calculateConsistency(inhibitionScores),
        efficiency: this.calculateInhibitoryEfficiency()
      },
      cognitiveFlexibility: {
        mean: this.calculateMean(shiftingCosts),
        adaptability: this.calculateFlexibilityAdaptability(),
        resilience: this.calculateFlexibilityResilience()
      },
      workingMemory: {
        mean: this.calculateMean(workingMemoryLoads),
        capacity: this.estimateWorkingMemoryCapacity(),
        efficiency: this.calculateWMEfficiency()
      },
      executiveProfile: this.buildExecutiveProfile()
    };
  }
  /**
   * ANÁLISE COMPLEXA: Flexibilidade cognitiva
   */
  analyzeCognitiveFlexibility() {
    const switches = this.flexibilityBuffer.filter((f) => f.contextSwitch.hasSwitch);
    const adaptations = this.adaptationEvents;
    return {
      switchingPerformance: this.analyzeSwitchingPerformance(switches),
      adaptiveFlexibility: this.analyzeAdaptiveFlexibility(adaptations),
      alternatingFlexibility: this.analyzeAlternatingFlexibility(),
      fluencyFlexibility: this.analyzeFluencyFlexibility(),
      flexibilityRange: this.calculateFlexibilityRange(),
      flexibilityStability: this.assessFlexibilityStability()
    };
  }
  /**
   * ANÁLISE COMPLEXA: Estratégia adaptativa
   */
  analyzeAdaptiveStrategy() {
    const strategies = this.strategyHistory;
    const adaptations = this.adaptationEvents;
    return {
      strategyRepertoire: this.buildStrategyRepertoire(strategies),
      adaptationTriggers: this.identifyAdaptationTriggers(adaptations),
      strategicLearning: this.analyzeStrategicLearning(),
      contextSensitivity: this.assessContextSensitivity(),
      strategicOptimization: this.analyzeStrategicOptimization(),
      emergentStrategies: this.identifyEmergentStrategies()
    };
  }
  /**
   * MÉTODOS AUXILIARES DE CLASSIFICAÇÃO
   */
  classifyContextSwitch(lastContext, currentContext) {
    const contextDomains = {
      "animais-básicos": "biological",
      "natureza-básica": "natural",
      "alimentos-origem": "biological",
      "profissões-ferramentas": "social",
      "tempo-ação": "temporal",
      "música-emoção": "sensory"
    };
    const lastDomain = contextDomains[lastContext] || "unknown";
    const currentDomain = contextDomains[currentContext] || "unknown";
    if (lastDomain === currentDomain) return "intra-domain";
    return "inter-domain";
  }
  calculateSwitchComplexity(lastContext, currentContext) {
    const complexityMatrix = {
      "intra-domain": 0.3,
      "inter-domain": 0.7,
      "unknown": 0.5
    };
    const switchType = this.classifyContextSwitch(lastContext, currentContext);
    return complexityMatrix[switchType] || 0.5;
  }
  getDifficultyWeight(difficulty) {
    const weights = { "EASY": 0.3, "MEDIUM": 0.6, "HARD": 1 };
    return weights[difficulty] || 0.6;
  }
  getContextComplexity(category) {
    const complexities = {
      "animais-básicos": 0.2,
      "natureza-básica": 0.3,
      "alimentos-origem": 0.4,
      "profissões-ferramentas": 0.8,
      "tempo-ação": 0.7,
      "música-emoção": 0.9
    };
    return complexities[category] || 0.5;
  }
  identifyCurrentStrategy(data) {
    if (data.responseTime < 2e3) return "fast-intuitive";
    if (data.responseTime > 8e3) return "deliberate-analytical";
    if (data.isCorrect) return "accurate-systematic";
    return "exploratory-flexible";
  }
  /**
   * ANÁLISE EM TEMPO REAL
   */
  async performFlexibilityAnalysis() {
    const recentEvents = this.flexibilityBuffer.slice(-5);
    const switches = recentEvents.filter((e) => e.contextSwitch.hasSwitch);
    const adaptations = recentEvents.filter((e) => e.adaptationRequired);
    if (switches.length > 3) {
      console.log("🧠 Flexibilidade Mental: Alta demanda de switching - monitorando fadiga");
    } else if (adaptations.every((a) => a.responseAccuracy)) {
      console.log("🧠 Flexibilidade Mental: Excelente adaptabilidade demonstrada");
    }
  }
  /**
   * INSIGHTS E RECOMENDAÇÕES
   */
  generateFlexibilityInsights(patterns) {
    const insights = [];
    if (patterns.executiveControl.inhibitoryControl.efficiency > 0.8) {
      insights.push("Excelente controle inibitório e regulação executiva");
    }
    if (patterns.cognitiveFlexibility.adaptability > 0.7) {
      insights.push("Alta capacidade de adaptação cognitiva");
    }
    if (patterns.perseverationResistance.breakingCapacity > 0.8) {
      insights.push("Forte resistência a padrões perseverativos");
    }
    return insights;
  }
  generateFlexibilityRecommendations(patterns) {
    const recommendations = [];
    if (patterns.setShifting.efficiency < 0.5) {
      recommendations.push("Praticar exercícios de alternância entre tarefas");
    }
    if (patterns.adaptiveStrategy.contextSensitivity < 0.6) {
      recommendations.push("Desenvolver sensibilidade a mudanças contextuais");
    }
    return recommendations;
  }
  /**
   * MÉTODOS AUXILIARES MATEMÁTICOS E SIMPLIFICADOS
   */
  calculateMean(values) {
    return values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
  }
  calculateConsistency(values) {
    if (values.length < 2) return 1;
    const mean = this.calculateMean(values);
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return mean > 0 ? Math.max(0, 1 - Math.sqrt(variance) / mean) : 1;
  }
  // Métodos auxiliares simplificados
  getPreviousContext() {
    return this.flexibilityBuffer.length > 0 ? this.flexibilityBuffer[this.flexibilityBuffer.length - 1].currentContext : null;
  }
  assessAdaptationRequired() {
    return Math.random() > 0.7;
  }
  evaluateAdaptiveCapacity() {
    return 0.75;
  }
  updateStrategyHistory(metrics) {
    this.strategyHistory.push({
      timestamp: metrics.timestamp,
      strategy: metrics.strategicFlexibility.currentStrategy,
      context: metrics.currentContext,
      success: metrics.responseAccuracy
    });
  }
  recordAdaptationEvent(metrics) {
    if (metrics.adaptationRequired) {
      this.adaptationEvents.push({
        timestamp: metrics.timestamp,
        trigger: metrics.contextSwitch.switchType,
        response: metrics.strategicFlexibility.strategyAdaptation,
        outcome: metrics.responseAccuracy
      });
    }
  }
  calculateContextDistance() {
    return 0.5;
  }
  assessTemporalPressure() {
    return 0.3;
  }
  classifyLoadProfile() {
    return "moderate";
  }
  getRecentStrategies() {
    return ["systematic", "intuitive", "analytical"];
  }
  calculateStrategyVariability() {
    return 0.6;
  }
  assessStrategyAdaptation() {
    return 0.7;
  }
  calculateStrategicConsistency() {
    return 0.8;
  }
  assessStrategicOptimality() {
    return 0.75;
  }
  measureStrategyShifting() {
    return 0.6;
  }
  calculateSemanticSimilarity() {
    return 0.4;
  }
  identifyPrepotentResponse() {
    return 0.3;
  }
  calculateInterferenceLevel() {
    return 0.4;
  }
  identifyInhibitoryStrategy() {
    return "selective";
  }
  calculateBaselineTime() {
    return 4e3;
  }
  calculateBaselineAccuracy() {
    return 0.7;
  }
  classifyShiftingCost() {
    return "moderate";
  }
  getRecentResponses() {
    return [];
  }
  analyzeResponsePatterns() {
    return { repetition: 0.2 };
  }
  assessConceptualPerseveration() {
    return 0.1;
  }
  assessStrategicPerseveration() {
    return 0.15;
  }
  assessPerseverationBreaking() {
    return 0.8;
  }
  assessFlexibilityRecovery() {
    return 0.85;
  }
  calculateInhibitoryEfficiency() {
    return 0.8;
  }
  calculateFlexibilityAdaptability() {
    return 0.75;
  }
  calculateFlexibilityResilience() {
    return 0.7;
  }
  estimateWorkingMemoryCapacity() {
    return 4;
  }
  calculateWMEfficiency() {
    return 0.8;
  }
  buildExecutiveProfile() {
    return { type: "balanced", strength: "inhibition" };
  }
  analyzeSwitchingPerformance() {
    return { accuracy: 0.8, speed: 0.7 };
  }
  analyzeAdaptiveFlexibility() {
    return { trigger: "context", response: "appropriate" };
  }
  analyzeAlternatingFlexibility() {
    return { efficiency: 0.75 };
  }
  analyzeFluencyFlexibility() {
    return { diversity: 0.8 };
  }
  calculateFlexibilityRange() {
    return 0.8;
  }
  assessFlexibilityStability() {
    return 0.7;
  }
  buildStrategyRepertoire() {
    return { size: 4, diversity: 0.8 };
  }
  identifyAdaptationTriggers() {
    return ["context_switch", "difficulty_change"];
  }
  analyzeStrategicLearning() {
    return { rate: 0.7, retention: 0.8 };
  }
  assessContextSensitivity() {
    return 0.75;
  }
  analyzeStrategicOptimization() {
    return { efficiency: 0.8 };
  }
  identifyEmergentStrategies() {
    return ["hybrid-approach"];
  }
  analyzeInhibitoryControl() {
    return { strength: 0.8, flexibility: 0.7 };
  }
  analyzeSetShifting() {
    return { efficiency: 0.75, cost: 0.3 };
  }
  analyzeMentalAgility() {
    return { speed: 0.8, accuracy: 0.75 };
  }
  analyzePerseverationResistance() {
    return { breakingCapacity: 0.85, recovery: 0.8 };
  }
  /**
   * MÉTODO DE RESET
   */
  reset() {
    this.flexibilityBuffer = [];
    this.strategyHistory = [];
    this.adaptationEvents = [];
    this.inhibitionMeasures = [];
    this.initialized = false;
  }
  /**
   * MÉTODO DE STATUS
   */
  getStatus() {
    return {
      collectorId: this.collectorId,
      version: this.version,
      isActive: this.initialized,
      dataPoints: this.flexibilityBuffer.length,
      lastCollection: this.flexibilityBuffer.length > 0 ? this.flexibilityBuffer[this.flexibilityBuffer.length - 1].timestamp : null
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Método padronizado de análise de dados para integração com testes
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  analyze(gameData) {
    try {
      const flexibilityData = {
        timestamp: Date.now(),
        sessionId: gameData.sessionId || "unknown",
        flexibilityMetrics: {
          cognitiveFlexibility: this.analyzeCognitiveFlexibility(gameData),
          switchingAbility: this.analyzeSwitchingAbility(gameData),
          adaptationMetrics: this.analyzeAdaptationMetrics(gameData),
          perseverationResistance: this.analyzePerseverationResistance(gameData)
        },
        recommendations: this.generateFlexibilityRecommendations(gameData)
      };
      this.flexibilityBuffer.push(flexibilityData);
      return {
        collectorId: this.collectorId,
        collectorType: "MentalFlexibility",
        data: flexibilityData,
        status: "success"
      };
    } catch (error) {
      console.error("Erro ao analisar dados de flexibilidade mental:", error);
      return {
        collectorId: this.collectorId,
        collectorType: "MentalFlexibility",
        error: error.message,
        status: "error"
      };
    }
  }
  /**
   * Analisa a habilidade de alternância cognitiva
   */
  analyzeSwitchingAbility(data) {
    return {
      switchingCost: Math.random() * 50,
      switchingAccuracy: Math.random() * 100,
      switchingSpeed: Math.random() * 100
    };
  }
  /**
   * Analisa métricas de adaptação
   */
  analyzeAdaptationMetrics(data) {
    return {
      adaptationRate: Math.random() * 100,
      strategicShifting: Math.random() * 100,
      contextualAdaptation: Math.random() * 100
    };
  }
}
class SemanticMemoryCollector {
  constructor() {
    this.collectorId = "semantic-memory";
    this.collectorName = "Semantic Memory Collector";
    this.version = "1.0.0";
    this.isActive = true;
    this.metrics = {
      semanticAccuracy: 0,
      conceptualFluency: 0,
      associativeStrength: 0,
      categoryCoherence: 0
    };
    this.collectionHistory = [];
    this.patterns = {
      semanticNetworks: [],
      conceptualMaps: [],
      associationStrengths: []
    };
    console.log(`🧠 ${this.collectorName} inicializado`);
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Analisa dados do jogo para métricas de memória semântica
   */
  analyze(gameData) {
    try {
      return this.collectSemanticMemoryData(gameData);
    } catch (error) {
      console.error("Erro na análise de memória semântica:", error);
      return {
        collectorId: this.collectorId,
        status: "error",
        error: error.message
      };
    }
  }
  /**
   * Coleta dados de memória semântica
   */
  async collectSemanticMemoryData(gameData) {
    try {
      const semanticData = {
        timestamp: Date.now(),
        sessionId: gameData.sessionId,
        semanticAccuracy: this.analyzeSemanticAccuracy(gameData),
        conceptualAnalysis: this.analyzeConceptualFluency(gameData),
        associativeAnalysis: this.analyzeAssociativeStrength(gameData),
        categoryAnalysis: this.analyzeCategoryCoherence(gameData)
      };
      this.collectionHistory.push(semanticData);
      this.updateMetrics(semanticData);
      return semanticData;
    } catch (error) {
      console.error("Erro ao coletar dados de memória semântica:", error);
      return null;
    }
  }
}
class ErrorPatternCollector {
  constructor() {
    this.name = "ImageAssociationErrorPatternCollector";
    this.description = "Coleta padrões de erros no ImageAssociation";
    this.version = "1.0.0";
    this.isActive = true;
    this.collectedData = [];
    this.errorData = {
      associationErrors: {},
      semanticErrors: [],
      visualProcessingErrors: [],
      categoricalErrors: [],
      persistentErrors: {},
      errorClusters: [],
      learningIndicators: [],
      conceptualMappings: {}
    };
    this.sessionStartTime = Date.now();
    this.errorThresholds = {
      persistent: 3,
      cluster: 5,
      severity: {
        low: 0.3,
        medium: 0.6,
        high: 0.8
      }
    };
    console.log(`🖼️ ${this.name} v${this.version} inicializado`);
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de erros
   */
  collect(gameData) {
    if (!gameData) {
      console.warn("ImageAssociationErrorPatternCollector: Dados do jogo não fornecidos para análise");
      return { errors: [], patterns: [], metrics: {} };
    }
    console.log(`📊 ImageAssociationErrorPatternCollector: Analisando dados da sessão ${gameData.sessionId || "sem ID"}`);
    try {
      const errorMetrics = this.analyzeErrorPatterns(gameData);
      const errors = [];
      if (gameData.attemptHistory && Array.isArray(gameData.attemptHistory)) {
        gameData.attemptHistory.forEach((attempt, index) => {
          if (!attempt.isCorrect && attempt.targetImage && attempt.selectedImage) {
            const associationError = this.collectAssociationError(
              attempt.targetImage,
              attempt.selectedImage,
              {
                difficulty: gameData.difficulty || "medium",
                responseTime: attempt.responseTime || 0,
                attemptNumber: index,
                category: attempt.category || "general"
              }
            );
            if (associationError) errors.push(associationError);
          }
        });
      }
      const collectedMetric = {
        timestamp: Date.now(),
        type: "error_pattern",
        gameType: "ImageAssociation",
        data: errorMetrics,
        errors,
        sessionData: {
          sessionId: gameData.sessionId,
          level: gameData.level || 1,
          attempt: gameData.attempt || 1
        }
      };
      this.collectedData.push(collectedMetric);
      this.categorizeErrors(errorMetrics);
      return {
        errors,
        patterns: errorMetrics,
        metrics: this.generateErrorMetrics(gameData)
      };
    } catch (error) {
      console.error("❌ Erro ao coletar padrões de erro (ImageAssociation):", error);
      return null;
    }
  }
  analyzeErrorPatterns(gameData) {
    const patterns = {
      associationErrors: this.detectAssociationErrors(gameData),
      semanticErrors: this.detectSemanticErrors(gameData),
      visualProcessingErrors: this.detectVisualErrors(gameData),
      categoricalErrors: this.detectCategoricalErrors(gameData),
      severity: this.calculateOverallSeverity(gameData)
    };
    return patterns;
  }
  detectAssociationErrors(gameData) {
    return [];
  }
  detectSemanticErrors(gameData) {
    return [];
  }
  detectVisualErrors(gameData) {
    return [];
  }
  detectCategoricalErrors(gameData) {
    return [];
  }
  calculateOverallSeverity(gameData) {
    return "low";
  }
  categorizeErrors(errorMetrics) {
  }
  /**
   * Coleta erros de associação de imagens
   */
  collectAssociationError(targetImage, selectedImage, context) {
    const errorKey = `${targetImage.id || targetImage.name}->${selectedImage.id || selectedImage.name}`;
    const associationError = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      targetImage,
      selectedImage,
      errorType: this.identifyAssociationErrorType(targetImage, selectedImage, context),
      context: {
        category: context.category || "general",
        difficulty: context.difficulty || "medium",
        responseTime: context.responseTime || 0,
        attemptNumber: context.attemptNumber || 0
      },
      severity: this.calculateAssociationErrorSeverity(targetImage, selectedImage, context),
      semanticDistance: this.calculateSemanticDistance(targetImage, selectedImage)
    };
    if (!this.errorData.associationErrors[errorKey]) {
      this.errorData.associationErrors[errorKey] = [];
    }
    this.errorData.associationErrors[errorKey].push(associationError);
    return associationError;
  }
  /**
   * Identifica o tipo de erro de associação
   */
  identifyAssociationErrorType(targetImage, selectedImage, context) {
    if (!selectedImage) return "no_selection";
    if (targetImage.category && selectedImage.category && targetImage.category !== selectedImage.category) {
      return "category_error";
    }
    if (this.areSimilarVisually(targetImage, selectedImage)) {
      return "visual_similarity_error";
    }
    if (this.areSimilarFunctionally(targetImage, selectedImage)) {
      return "functional_similarity_error";
    }
    if (this.shareContext(targetImage, selectedImage)) {
      return "contextual_error";
    }
    return "general_association_error";
  }
  /**
   * Verifica se duas imagens são visualmente similares
   */
  areSimilarVisually(image1, image2) {
    return false;
  }
  /**
   * Verifica se duas imagens têm funções similares
   */
  areSimilarFunctionally(image1, image2) {
    return false;
  }
  /**
   * Verifica se duas imagens aparecem em contextos similares
   */
  shareContext(image1, image2) {
    return false;
  }
  /**
   * Calcula a distância semântica entre duas imagens
   */
  calculateSemanticDistance(image1, image2) {
    return 0.5;
  }
  /**
   * Calcula a severidade do erro de associação
   */
  calculateAssociationErrorSeverity(targetImage, selectedImage, context) {
    let severity = 0.5;
    const distance = this.calculateSemanticDistance(targetImage, selectedImage);
    severity += distance * 0.3;
    if (context.responseTime > 5e3) severity += 0.1;
    if (context.responseTime < 500) severity += 0.1;
    if (context.difficulty === "hard") severity -= 0.1;
    return Math.min(Math.max(severity, 0), 1);
  }
  /**
   * Gera métricas de erro com base nos dados coletados
   */
  generateErrorMetrics(gameData) {
    const errorCount = Object.values(this.errorData.associationErrors).reduce(
      (total, errors) => total + errors.length,
      0
    );
    return {
      totalErrors: errorCount,
      uniqueAssociationErrors: Object.keys(this.errorData.associationErrors).length,
      mostCommonError: this.findMostCommonError(),
      averageSeverity: this.calculateAverageSeverity(),
      visualProcessingScore: this.calculateVisualProcessingScore(gameData),
      conceptualAssociationScore: this.calculateConceptualAssociationScore(gameData),
      improvement: this.calculateImprovementMetric(gameData)
    };
  }
  /**
   * Encontra o erro mais comum
   */
  findMostCommonError() {
    let maxCount = 0;
    let mostCommonError = null;
    Object.entries(this.errorData.associationErrors).forEach(([errorKey, errors]) => {
      if (errors.length > maxCount) {
        maxCount = errors.length;
        mostCommonError = errorKey;
      }
    });
    return {
      error: mostCommonError,
      count: maxCount
    };
  }
  /**
   * Calcula a severidade média dos erros
   */
  calculateAverageSeverity() {
    let totalSeverity = 0;
    let errorCount = 0;
    Object.values(this.errorData.associationErrors).forEach((errors) => {
      errors.forEach((error) => {
        totalSeverity += error.severity;
        errorCount++;
      });
    });
    return errorCount > 0 ? totalSeverity / errorCount : 0;
  }
  /**
   * Calcula pontuação de processamento visual
   */
  calculateVisualProcessingScore(gameData) {
    return 0.7;
  }
  /**
   * Calcula pontuação de associação conceitual
   */
  calculateConceptualAssociationScore(gameData) {
    return 0.6;
  }
  /**
   * Calcula métrica de melhoria ao longo do tempo
   */
  calculateImprovementMetric(gameData) {
    return 0.5;
  }
  /**
   * Método de análise para compatibilidade com outros coletores
   */
  analyze(gameData) {
    return this.collect(gameData);
  }
}
class SimpleAssociationCollector extends BaseCollector {
  constructor() {
    super("SimpleAssociation");
    this.cognitiveMetrics = {
      // Métricas específicas de associação simples
      associativeSpeed: [],
      relationRecognition: [],
      visualPatternMatching: [],
      semanticProcessing: [],
      errorPatterns: [],
      // Padrões de resposta
      responseConsistency: [],
      hintDependency: [],
      difficultyProgression: [],
      // Análise temporal
      reactionTimes: [],
      learningCurve: [],
      fatigueIndicators: []
    };
    this.associationTypes = {
      "animal-food": { weight: 1, complexity: "low" },
      "tool-object": { weight: 1, complexity: "low" },
      "cause-effect": { weight: 2, complexity: "medium" },
      "problem-solution": { weight: 2, complexity: "medium" },
      "device-accessory": { weight: 3, complexity: "high" },
      "organ-aid": { weight: 3, complexity: "high" },
      "location-access": { weight: 2, complexity: "medium" },
      "vehicle-fuel": { weight: 2, complexity: "medium" }
    };
    this.sessionData = {
      startTime: null,
      endTime: null,
      totalAssociations: 0,
      correctAssociations: 0,
      incorrectAssociations: 0,
      averageResponseTime: 0,
      associationsByType: {},
      errorsByType: {},
      hintUsage: 0,
      difficultyLevel: "beginner"
    };
  }
  // ========================================================================
  // COLETA DE DADOS DE INTERAÇÃO
  // ========================================================================
  collectInteraction(interactionData) {
    const {
      selectedItems,
      validation,
      responseTime,
      timestamp,
      hintUsed = false,
      visualCuesEnabled = true
    } = interactionData;
    const associationType = validation.relationship;
    const isCorrect = validation.isCorrect;
    const interaction = {
      id: this.generateInteractionId(),
      timestamp,
      responseTime,
      selectedItems: selectedItems.map((item) => ({
        id: item.id,
        image: item.image,
        pairId: item.pairId,
        type: item.type
      })),
      validation: {
        isCorrect,
        relationship: associationType,
        reason: validation.reason
      },
      context: {
        hintUsed,
        visualCuesEnabled,
        difficultyLevel: this.sessionData.difficultyLevel
      }
    };
    this.interactions.push(interaction);
    this.analyzeAssociativeProcessing(interaction);
    this.analyzeResponsePatterns(interaction);
    this.analyzeErrorPatterns(interaction);
    this.updateSessionMetrics(interaction);
    return interaction;
  }
  // ========================================================================
  // ANÁLISE COGNITIVA ESPECIALIZADA
  // ========================================================================
  analyzeAssociativeProcessing(interaction) {
    const { responseTime, validation, context } = interaction;
    const associationType = validation.relationship;
    const complexity = this.associationTypes[associationType]?.complexity || "medium";
    const speedMetric = {
      timestamp: interaction.timestamp,
      responseTime,
      complexity,
      associationType,
      isCorrect: validation.isCorrect,
      adjustedSpeed: this.calculateAdjustedSpeed(responseTime, complexity),
      efficiency: this.calculateEfficiency(responseTime, validation.isCorrect, complexity)
    };
    this.cognitiveMetrics.associativeSpeed.push(speedMetric);
    const relationMetric = {
      timestamp: interaction.timestamp,
      relationType: associationType,
      recognized: validation.isCorrect,
      responseTime,
      complexity,
      confidence: this.calculateConfidence(responseTime, validation.isCorrect)
    };
    this.cognitiveMetrics.relationRecognition.push(relationMetric);
    const semanticMetric = {
      timestamp: interaction.timestamp,
      semanticDistance: this.calculateSemanticDistance(interaction.selectedItems),
      processingTime: responseTime,
      accurateProcessing: validation.isCorrect,
      semanticCategory: this.categorizeSemanticRelation(associationType)
    };
    this.cognitiveMetrics.semanticProcessing.push(semanticMetric);
  }
  analyzeResponsePatterns(interaction) {
    const { responseTime, validation, context } = interaction;
    const recentInteractions = this.interactions.slice(-5);
    const consistencyScore = this.calculateResponseConsistency(recentInteractions);
    this.cognitiveMetrics.responseConsistency.push({
      timestamp: interaction.timestamp,
      consistencyScore,
      windowSize: recentInteractions.length
    });
    if (context.hintUsed) {
      this.cognitiveMetrics.hintDependency.push({
        timestamp: interaction.timestamp,
        responseTime,
        wasCorrect: validation.isCorrect,
        associationType: validation.relationship
      });
    }
    const difficultyMetric = {
      timestamp: interaction.timestamp,
      currentDifficulty: context.difficultyLevel,
      performance: validation.isCorrect ? 1 : 0,
      responseTime,
      shouldIncreaseComplexity: this.shouldIncreaseComplexity(),
      shouldDecreaseComplexity: this.shouldDecreaseComplexity()
    };
    this.cognitiveMetrics.difficultyProgression.push(difficultyMetric);
  }
  analyzeErrorPatterns(interaction) {
    if (!interaction.validation.isCorrect) {
      const errorType = this.classifyError(interaction);
      const errorAnalysis = {
        timestamp: interaction.timestamp,
        errorType,
        selectedItems: interaction.selectedItems,
        correctRelationship: interaction.validation.relationship,
        responseTime: interaction.responseTime,
        context: interaction.context,
        cognitiveLoad: this.estimateCognitiveLoad(interaction),
        interferenceType: this.detectInterference(interaction)
      };
      this.cognitiveMetrics.errorPatterns.push(errorAnalysis);
    }
  }
  // ========================================================================
  // CÁLCULOS E MÉTRICAS ESPECIALIZADAS
  // ========================================================================
  calculateAdjustedSpeed(responseTime, complexity) {
    const baselinesByComplexity = {
      "low": 3e3,
      // 3 segundos para associações simples
      "medium": 5e3,
      // 5 segundos para associações médias
      "high": 8e3
      // 8 segundos para associações complexas
    };
    const baseline = baselinesByComplexity[complexity] || 5e3;
    return Math.max(0, (baseline - responseTime) / baseline);
  }
  calculateEfficiency(responseTime, isCorrect, complexity) {
    if (!isCorrect) return 0;
    const maxTimeByComplexity = {
      "low": 5e3,
      "medium": 8e3,
      "high": 12e3
    };
    const maxTime = maxTimeByComplexity[complexity] || 8e3;
    return Math.max(0, (maxTime - responseTime) / maxTime);
  }
  calculateConfidence(responseTime, isCorrect) {
    if (!isCorrect) return 0;
    const idealTime = 3e3;
    const timeFactor = Math.exp(-Math.abs(responseTime - idealTime) / 2e3);
    return timeFactor;
  }
  calculateSemanticDistance(selectedItems) {
    if (selectedItems.length !== 2) return 0;
    const semanticCategories = {
      "animals": ["🐕", "🦴", "🐱", "🐸", "🦋"],
      "objects": ["🔑", "🔒", "📱", "🔌", "👓"],
      "nature": ["☀️", "🌡️", "🌧️", "☂️", "🌱"],
      "body": ["👁️", "👂", "👶", "💪"],
      "vehicles": ["🚗", "⛽", "✈️", "🚲"],
      "places": ["🏠", "🏥", "🏛️", "🎪"]
    };
    const [item1, item2] = selectedItems;
    let category1 = null, category2 = null;
    for (const [category, items] of Object.entries(semanticCategories)) {
      if (items.includes(item1.image)) category1 = category;
      if (items.includes(item2.image)) category2 = category;
    }
    if (category1 === category2) return 0.1;
    if (category1 && category2) return 0.5;
    return 1;
  }
  categorizeSemanticRelation(associationType) {
    const categories = {
      "animal-food": "biological",
      "tool-object": "functional",
      "cause-effect": "causal",
      "problem-solution": "logical",
      "device-accessory": "technological",
      "organ-aid": "medical",
      "location-access": "spatial",
      "vehicle-fuel": "mechanical"
    };
    return categories[associationType] || "unknown";
  }
  calculateResponseConsistency(recentInteractions) {
    if (recentInteractions.length < 2) return 1;
    const responseTimes = recentInteractions.map((i) => i.responseTime);
    const mean = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    const variance = responseTimes.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / responseTimes.length;
    const standardDeviation = Math.sqrt(variance);
    const consistencyScore = Math.max(0, 1 - standardDeviation / mean);
    return consistencyScore;
  }
  shouldIncreaseComplexity() {
    const recentInteractions = this.interactions.slice(-10);
    if (recentInteractions.length < 5) return false;
    const accuracy = recentInteractions.filter((i) => i.validation.isCorrect).length / recentInteractions.length;
    const averageTime = recentInteractions.reduce((sum, i) => sum + i.responseTime, 0) / recentInteractions.length;
    return accuracy >= 0.8 && averageTime < 4e3;
  }
  shouldDecreaseComplexity() {
    const recentInteractions = this.interactions.slice(-10);
    if (recentInteractions.length < 5) return false;
    const accuracy = recentInteractions.filter((i) => i.validation.isCorrect).length / recentInteractions.length;
    return accuracy < 0.5;
  }
  classifyError(interaction) {
    const { selectedItems, validation } = interaction;
    if (selectedItems.length !== 2) return "incomplete_selection";
    const [item1, item2] = selectedItems;
    if (item1.pairId === item2.pairId) return "impossible_error";
    if (this.hasVisualSimilarity(item1.image, item2.image)) {
      return "visual_similarity_confusion";
    }
    if (this.hasSimilarSemanticCategory(item1.image, item2.image)) {
      return "semantic_category_confusion";
    }
    if (this.hasTemporalProximity(item1, item2)) {
      return "temporal_proximity_error";
    }
    return "random_association";
  }
  hasVisualSimilarity(image1, image2) {
    const visualGroups = [
      ["🔑", "🔒", "🔓"],
      // Objetos de fechadura
      ["☀️", "🌡️", "🔥"],
      // Objetos relacionados ao calor
      ["👁️", "👓", "👀"],
      // Objetos relacionados à visão
      ["🚗", "🚲", "✈️"]
      // Veículos
    ];
    return visualGroups.some(
      (group) => group.includes(image1) && group.includes(image2)
    );
  }
  hasSimilarSemanticCategory(image1, image2) {
    const semanticDistance = this.calculateSemanticDistance([
      { image: image1 },
      { image: image2 }
    ]);
    return semanticDistance < 0.3;
  }
  hasTemporalProximity(item1, item2) {
    return Math.abs(item1.id.split("_")[0] - item2.id.split("_")[0]) <= 1;
  }
  estimateCognitiveLoad(interaction) {
    const { responseTime, context } = interaction;
    let loadFactors = 0;
    if (responseTime > 8e3) loadFactors += 0.3;
    if (context.hintUsed) loadFactors += 0.2;
    if (!context.visualCuesEnabled) loadFactors += 0.2;
    const difficultyMultiplier = {
      "beginner": 1,
      "intermediate": 1.3,
      "advanced": 1.6
    }[context.difficultyLevel] || 1;
    return Math.min(1, loadFactors * difficultyMultiplier);
  }
  detectInterference(interaction) {
    const recentErrors = this.cognitiveMetrics.errorPatterns.slice(-3);
    if (recentErrors.length < 2) return "none";
    const errorTypes = recentErrors.map((e) => e.errorType);
    if (errorTypes.every((type) => type === "visual_similarity_confusion")) {
      return "visual_interference";
    }
    if (errorTypes.every((type) => type === "semantic_category_confusion")) {
      return "semantic_interference";
    }
    if (errorTypes.some((type) => type === "temporal_proximity_error")) {
      return "temporal_interference";
    }
    return "mixed_interference";
  }
  // ========================================================================
  // ATUALIZAÇÃO DE MÉTRICAS DE SESSÃO
  // ========================================================================
  updateSessionMetrics(interaction) {
    const { validation, responseTime, context } = interaction;
    const associationType = validation.relationship;
    this.sessionData.totalAssociations++;
    if (validation.isCorrect) {
      this.sessionData.correctAssociations++;
    } else {
      this.sessionData.incorrectAssociations++;
    }
    const totalTime = this.interactions.reduce((sum, i) => sum + i.responseTime, 0);
    this.sessionData.averageResponseTime = totalTime / this.interactions.length;
    if (!this.sessionData.associationsByType[associationType]) {
      this.sessionData.associationsByType[associationType] = { total: 0, correct: 0 };
    }
    this.sessionData.associationsByType[associationType].total++;
    if (validation.isCorrect) {
      this.sessionData.associationsByType[associationType].correct++;
    }
    if (!validation.isCorrect) {
      const errorType = this.classifyError(interaction);
      if (!this.sessionData.errorsByType[errorType]) {
        this.sessionData.errorsByType[errorType] = 0;
      }
      this.sessionData.errorsByType[errorType]++;
    }
    if (context.hintUsed) {
      this.sessionData.hintUsage++;
    }
  }
  // ========================================================================
  // RELATÓRIOS E ANÁLISES
  // ========================================================================
  generateCognitiveReport() {
    return {
      associativeProcessing: this.analyzeAssociativeProcessing(),
      responsePatterns: this.analyzeResponsePatterns(),
      errorAnalysis: this.analyzeErrorPatterns(),
      learningProgression: this.analyzeLearningProgression(),
      adaptiveRecommendations: this.generateAdaptiveRecommendations()
    };
  }
  generateAdaptiveRecommendations() {
    const recommendations = [];
    const averageSpeed = this.calculateAverageMetric(this.cognitiveMetrics.associativeSpeed, "adjustedSpeed");
    if (averageSpeed < 0.3) {
      recommendations.push({
        type: "difficulty_adjustment",
        recommendation: "Reduzir complexidade das associações",
        confidence: 0.8
      });
    } else if (averageSpeed > 0.8) {
      recommendations.push({
        type: "difficulty_adjustment",
        recommendation: "Aumentar complexidade das associações",
        confidence: 0.9
      });
    }
    const hintDependency = this.cognitiveMetrics.hintDependency.length / this.interactions.length;
    if (hintDependency > 0.5) {
      recommendations.push({
        type: "support_adjustment",
        recommendation: "Manter suporte visual e reduzir gradualmente",
        confidence: 0.7
      });
    }
    const errorPatterns = this.getErrorPatternDistribution();
    const dominantError = Object.keys(errorPatterns).reduce(
      (a, b) => errorPatterns[a] > errorPatterns[b] ? a : b
    );
    if (errorPatterns[dominantError] > 0.4) {
      recommendations.push({
        type: "intervention",
        recommendation: `Foco em correção de ${dominantError}`,
        confidence: 0.8
      });
    }
    return recommendations;
  }
  getPerformanceByComplexity(complexity) {
    const speedMetrics = this.cognitiveMetrics.associativeSpeed.filter((m) => m.complexity === complexity);
    return {
      count: speedMetrics.length,
      averageSpeed: this.calculateAverageMetric(speedMetrics, "adjustedSpeed"),
      averageEfficiency: this.calculateAverageMetric(speedMetrics, "efficiency"),
      successRate: speedMetrics.filter((m) => m.isCorrect).length / speedMetrics.length || 0
    };
  }
  getErrorPatternDistribution() {
    const errorCounts = {};
    this.cognitiveMetrics.errorPatterns.forEach((error) => {
      errorCounts[error.errorType] = (errorCounts[error.errorType] || 0) + 1;
    });
    const total = Object.values(errorCounts).reduce((sum, count) => sum + count, 0);
    Object.keys(errorCounts).forEach((errorType) => {
      errorCounts[errorType] = errorCounts[errorType] / total;
    });
    return errorCounts;
  }
  getActivityScore() {
    if (this.sessionData.totalAssociations === 0) return 0;
    const accuracy = this.sessionData.correctAssociations / this.sessionData.totalAssociations;
    const speedFactor = Math.max(0, 1 - (this.sessionData.averageResponseTime - 3e3) / 1e4);
    const hintPenalty = Math.max(0, 1 - this.sessionData.hintUsage / this.sessionData.totalAssociations);
    return Math.round(accuracy * speedFactor * hintPenalty * 1e3);
  }
}
class CategoricalAssociationCollector extends BaseCollector {
  constructor() {
    super("CategoricalAssociation");
    this.cognitiveMetrics = {
      // Métricas específicas de categorização
      categorizationSpeed: [],
      hierarchicalThinking: [],
      semanticOrganization: [],
      conceptualFlexibility: [],
      categoryTransfers: [],
      // Padrões de categorização
      categoryPreferences: {},
      crossCategoryErrors: [],
      subcategoryDiscrimination: [],
      // Análise de agrupamento
      clusteringBehavior: [],
      categoryCoherence: [],
      conceptualBoundaries: []
    };
    this.categoryStructure = {
      animals: {
        superCategory: "living",
        level: "basic",
        features: ["movement", "biological", "organic"],
        subcategories: ["mammals", "amphibians", "insects", "birds"]
      },
      fruits: {
        superCategory: "food",
        level: "basic",
        features: ["edible", "organic", "sweet"],
        subcategories: ["citrus", "tropical", "berries", "stone_fruits"]
      },
      vehicles: {
        superCategory: "transportation",
        level: "basic",
        features: ["movement", "mechanical", "transport"],
        subcategories: ["land", "air", "water", "personal"]
      },
      tools: {
        superCategory: "artifacts",
        level: "basic",
        features: ["functional", "mechanical", "purposeful"],
        subcategories: ["cutting", "measuring", "building", "writing"]
      },
      emotions: {
        superCategory: "psychological",
        level: "abstract",
        features: ["mental", "experiential", "subjective"],
        subcategories: ["positive", "negative", "complex", "basic"]
      }
    };
    this.sessionData = {
      startTime: null,
      endTime: null,
      totalCategorizations: 0,
      correctCategorizations: 0,
      incorrectCategorizations: 0,
      averageCategorizationTime: 0,
      categoriesByAccuracy: {},
      crossCategoryConfusions: {},
      categoryCompletionTimes: {},
      difficultyLevel: "beginner"
    };
  }
  // ========================================================================
  // COLETA DE DADOS DE CATEGORIZAÇÃO
  // ========================================================================
  collectCategorization(categorizationData) {
    const {
      selectedItem,
      targetCategory,
      actualCategory,
      responseTime,
      timestamp,
      categorySequence = [],
      visualCuesEnabled = true,
      categoryLabelsVisible = true
    } = categorizationData;
    const isCorrect = targetCategory === actualCategory;
    const categorization = {
      id: this.generateInteractionId(),
      timestamp,
      responseTime,
      selectedItem: {
        id: selectedItem.id,
        image: selectedItem.image,
        actualCategory,
        features: this.extractItemFeatures(selectedItem)
      },
      targetCategory,
      isCorrect,
      categorySequence,
      context: {
        visualCuesEnabled,
        categoryLabelsVisible,
        difficultyLevel: this.sessionData.difficultyLevel,
        availableCategories: this.getAvailableCategories()
      }
    };
    this.interactions.push(categorization);
    this.analyzeCategorizationProcessing(categorization);
    this.analyzeHierarchicalThinking(categorization);
    this.analyzeConceptualFlexibility(categorization);
    this.analyzeCategoryBoundaries(categorization);
    this.updateSessionMetrics(categorization);
    return categorization;
  }
  // ========================================================================
  // ANÁLISES COGNITIVAS ESPECIALIZADAS
  // ========================================================================
  analyzeCategorizationProcessing(categorization) {
    const { responseTime, isCorrect, targetCategory, selectedItem, context } = categorization;
    const speedMetric = {
      timestamp: categorization.timestamp,
      responseTime,
      targetCategory,
      itemComplexity: this.calculateItemComplexity(selectedItem),
      categoryComplexity: this.calculateCategoryComplexity(targetCategory),
      isCorrect,
      adjustedSpeed: this.calculateAdjustedCategorizationSpeed(responseTime, targetCategory),
      efficiency: this.calculateCategorizationEfficiency(responseTime, isCorrect, targetCategory)
    };
    this.cognitiveMetrics.categorizationSpeed.push(speedMetric);
    const semanticMetric = {
      timestamp: categorization.timestamp,
      category: targetCategory,
      item: selectedItem.image,
      semanticDistance: this.calculateSemanticDistance(selectedItem, targetCategory),
      prototypicality: this.calculatePrototypicality(selectedItem, targetCategory),
      categorized: isCorrect,
      responseTime
    };
    this.cognitiveMetrics.semanticOrganization.push(semanticMetric);
  }
  analyzeHierarchicalThinking(categorization) {
    const { targetCategory, selectedItem, isCorrect } = categorization;
    const hierarchicalMetric = {
      timestamp: categorization.timestamp,
      basicLevel: targetCategory,
      superCategory: this.categoryStructure[targetCategory]?.superCategory,
      subcategory: this.identifySubcategory(selectedItem, targetCategory),
      levelAccuracy: isCorrect,
      hierarchicalDepth: this.calculateHierarchicalDepth(targetCategory),
      crossLevelConfusion: this.detectCrossLevelConfusion(categorization)
    };
    this.cognitiveMetrics.hierarchicalThinking.push(hierarchicalMetric);
  }
  analyzeConceptualFlexibility(categorization) {
    const recentCategorizations = this.interactions.slice(-5);
    const flexibilityMetric = {
      timestamp: categorization.timestamp,
      categorySwitch: this.detectCategorySwitch(recentCategorizations),
      adaptationSpeed: this.calculateAdaptationSpeed(recentCategorizations),
      setShiftingAbility: this.assessSetShifting(recentCategorizations),
      cognitiveRigidity: this.detectCognitiveRigidity(recentCategorizations)
    };
    this.cognitiveMetrics.conceptualFlexibility.push(flexibilityMetric);
  }
  analyzeCategoryBoundaries(categorization) {
    const { selectedItem, targetCategory, isCorrect } = categorization;
    if (!isCorrect) {
      const actualCategory = selectedItem.actualCategory;
      const boundaryAnalysis = {
        timestamp: categorization.timestamp,
        confusedCategories: [targetCategory, actualCategory],
        boundaryType: this.classifyBoundaryConfusion(targetCategory, actualCategory),
        featureOverlap: this.calculateFeatureOverlap(targetCategory, actualCategory),
        prototypicalDistance: this.calculatePrototypicalDistance(selectedItem, targetCategory, actualCategory)
      };
      this.cognitiveMetrics.conceptualBoundaries.push(boundaryAnalysis);
    }
  }
  // ========================================================================
  // CÁLCULOS ESPECIALIZADOS
  // ========================================================================
  calculateItemComplexity(item) {
    const visualComplexity = {
      "🐱": 0.3,
      "🐸": 0.4,
      "🦋": 0.6,
      "🐧": 0.5,
      "🍎": 0.2,
      "🍌": 0.3,
      "🍇": 0.5,
      "🍊": 0.3,
      "🚗": 0.4,
      "✈️": 0.6,
      "🚲": 0.3,
      "🚁": 0.7,
      "🔨": 0.3,
      "🪚": 0.5,
      "🔧": 0.4,
      "✂️": 0.4,
      "😊": 0.3,
      "😢": 0.4,
      "😡": 0.5,
      "😴": 0.4
    };
    return visualComplexity[item.image] || 0.5;
  }
  calculateCategoryComplexity(category) {
    const complexityMap = {
      animals: 0.4,
      fruits: 0.3,
      vehicles: 0.5,
      tools: 0.6,
      emotions: 0.8
    };
    return complexityMap[category] || 0.5;
  }
  calculateAdjustedCategorizationSpeed(responseTime, category) {
    const baselinesByCategory = {
      animals: 3500,
      fruits: 3e3,
      vehicles: 4e3,
      tools: 4500,
      emotions: 6e3
    };
    const baseline = baselinesByCategory[category] || 4e3;
    return Math.max(0, (baseline - responseTime) / baseline);
  }
  calculateCategorizationEfficiency(responseTime, isCorrect, category) {
    if (!isCorrect) return 0;
    const maxTimeByCategory = {
      animals: 6e3,
      fruits: 5e3,
      vehicles: 7e3,
      tools: 8e3,
      emotions: 1e4
    };
    const maxTime = maxTimeByCategory[category] || 7e3;
    return Math.max(0, (maxTime - responseTime) / maxTime);
  }
  calculateSemanticDistance(item, category) {
    const itemFeatures = this.extractItemFeatures(item);
    const categoryFeatures = this.categoryStructure[category]?.features || [];
    const overlap = itemFeatures.filter((feature) => categoryFeatures.includes(feature)).length;
    const totalFeatures = (/* @__PURE__ */ new Set([...itemFeatures, ...categoryFeatures])).size;
    return 1 - overlap / totalFeatures;
  }
  calculatePrototypicality(item, category) {
    const prototypes = {
      animals: { "🐱": 0.9, "🐸": 0.7, "🦋": 0.6, "🐧": 0.8 },
      fruits: { "🍎": 0.9, "🍌": 0.8, "🍇": 0.7, "🍊": 0.8 },
      vehicles: { "🚗": 0.9, "✈️": 0.8, "🚲": 0.7, "🚁": 0.6 },
      tools: { "🔨": 0.9, "🪚": 0.7, "🔧": 0.8, "✂️": 0.6 },
      emotions: { "😊": 0.9, "😢": 0.8, "😡": 0.7, "😴": 0.6 }
    };
    return prototypes[category]?.[item.image] || 0.5;
  }
  extractItemFeatures(item) {
    const features = {
      "🐱": ["biological", "movement", "mammal", "domestic"],
      "🐸": ["biological", "movement", "amphibian", "water"],
      "🦋": ["biological", "movement", "insect", "colorful"],
      "🐧": ["biological", "movement", "bird", "arctic"],
      "🍎": ["edible", "organic", "round", "sweet"],
      "🍌": ["edible", "organic", "curved", "tropical"],
      "🍇": ["edible", "organic", "clustered", "sweet"],
      "🍊": ["edible", "organic", "round", "citrus"],
      "🚗": ["mechanical", "transport", "land", "personal"],
      "✈️": ["mechanical", "transport", "air", "fast"],
      "🚲": ["mechanical", "transport", "land", "manual"],
      "🚁": ["mechanical", "transport", "air", "vertical"],
      "🔨": ["tool", "construction", "impact", "metal"],
      "🪚": ["tool", "cutting", "construction", "teeth"],
      "🔧": ["tool", "mechanical", "turning", "metal"],
      "✂️": ["tool", "cutting", "sharp", "precise"],
      "😊": ["emotion", "positive", "basic", "social"],
      "😢": ["emotion", "negative", "basic", "sadness"],
      "😡": ["emotion", "negative", "intense", "anger"],
      "😴": ["emotion", "neutral", "state", "sleep"]
    };
    return features[item.image] || [];
  }
  identifySubcategory(item, category) {
    const subcategorization = {
      animals: {
        "🐱": "mammals",
        "🐸": "amphibians",
        "🦋": "insects",
        "🐧": "birds"
      },
      fruits: {
        "🍎": "stone_fruits",
        "🍌": "tropical",
        "🍇": "berries",
        "🍊": "citrus"
      },
      vehicles: {
        "🚗": "land",
        "✈️": "air",
        "🚲": "personal",
        "🚁": "air"
      },
      tools: {
        "🔨": "building",
        "🪚": "cutting",
        "🔧": "mechanical",
        "✂️": "cutting"
      },
      emotions: {
        "😊": "positive",
        "😢": "negative",
        "😡": "negative",
        "😴": "neutral"
      }
    };
    return subcategorization[category]?.[item.image] || "unknown";
  }
  calculateHierarchicalDepth(category) {
    const depths = {
      animals: 3,
      // living -> animals -> [mammals/birds/etc]
      fruits: 3,
      // food -> fruits -> [citrus/tropical/etc]
      vehicles: 3,
      // transportation -> vehicles -> [land/air/etc]
      tools: 3,
      // artifacts -> tools -> [cutting/building/etc]
      emotions: 3
      // psychological -> emotions -> [positive/negative/etc]
    };
    return depths[category] || 2;
  }
  detectCrossLevelConfusion(categorization) {
    const { targetCategory, selectedItem } = categorization;
    const actualCategory = selectedItem.actualCategory;
    if (targetCategory === actualCategory) return "none";
    const targetSuper = this.categoryStructure[targetCategory]?.superCategory;
    const actualSuper = this.categoryStructure[actualCategory]?.superCategory;
    if (targetSuper === actualSuper) return "sibling_category";
    if (this.isSubcategoryOf(targetCategory, actualCategory)) return "sub_to_super";
    if (this.isSubcategoryOf(actualCategory, targetCategory)) return "super_to_sub";
    return "distant_category";
  }
  detectCategorySwitch(recentCategorizations) {
    if (recentCategorizations.length < 2) return false;
    const current = recentCategorizations[recentCategorizations.length - 1];
    const previous = recentCategorizations[recentCategorizations.length - 2];
    return current.targetCategory !== previous.targetCategory;
  }
  calculateAdaptationSpeed(recentCategorizations) {
    const switches = [];
    for (let i = 1; i < recentCategorizations.length; i++) {
      const current = recentCategorizations[i];
      const previous = recentCategorizations[i - 1];
      if (current.targetCategory !== previous.targetCategory) {
        switches.push({
          switchTime: current.timestamp - previous.timestamp,
          responseTime: current.responseTime,
          successful: current.isCorrect
        });
      }
    }
    if (switches.length === 0) return 1;
    const averageSwitchTime = switches.reduce((sum, s) => sum + s.responseTime, 0) / switches.length;
    const successRate = switches.filter((s) => s.successful).length / switches.length;
    const speedScore = Math.max(0, 1 - (averageSwitchTime - 3e3) / 7e3);
    return (speedScore + successRate) / 2;
  }
  assessSetShifting(recentCategorizations) {
    const categories = recentCategorizations.map((c) => c.targetCategory);
    const uniqueCategories = new Set(categories);
    if (uniqueCategories.size <= 1) return 0.2;
    if (uniqueCategories.size === 2) return 0.6;
    if (uniqueCategories.size >= 3) return 1;
    return 0.5;
  }
  detectCognitiveRigidity(recentCategorizations) {
    if (recentCategorizations.length < 4) return 0;
    const errors = recentCategorizations.filter((c) => !c.isCorrect);
    if (errors.length < 2) return 0;
    const repeatedErrors = errors.filter((error, index, array) => {
      if (index === 0) return false;
      return error.targetCategory === array[index - 1].targetCategory;
    });
    return repeatedErrors.length / errors.length;
  }
  classifyBoundaryConfusion(targetCategory, actualCategory) {
    const superCategories = {
      target: this.categoryStructure[targetCategory]?.superCategory,
      actual: this.categoryStructure[actualCategory]?.superCategory
    };
    if (superCategories.target === superCategories.actual) {
      return "within_supercategory";
    }
    const featureOverlap = this.calculateFeatureOverlap(targetCategory, actualCategory);
    if (featureOverlap > 0.5) return "feature_similarity";
    if (featureOverlap > 0.3) return "partial_similarity";
    return "distant_confusion";
  }
  calculateFeatureOverlap(category1, category2) {
    const features1 = this.categoryStructure[category1]?.features || [];
    const features2 = this.categoryStructure[category2]?.features || [];
    const overlap = features1.filter((f) => features2.includes(f)).length;
    const total = (/* @__PURE__ */ new Set([...features1, ...features2])).size;
    return total === 0 ? 0 : overlap / total;
  }
  calculatePrototypicalDistance(item, targetCategory, actualCategory) {
    const targetPrototypicality = this.calculatePrototypicality(item, targetCategory);
    const actualPrototypicality = this.calculatePrototypicality(item, actualCategory);
    return Math.abs(targetPrototypicality - actualPrototypicality);
  }
  // ========================================================================
  // HELPERS E UTILITÁRIOS
  // ========================================================================
  isSubcategoryOf(category, superCategory) {
    return false;
  }
  getAvailableCategories() {
    return Object.keys(this.categoryStructure);
  }
  // ========================================================================
  // ATUALIZAÇÃO DE MÉTRICAS DE SESSÃO
  // ========================================================================
  updateSessionMetrics(categorization) {
    const { isCorrect, responseTime, targetCategory } = categorization;
    this.sessionData.totalCategorizations++;
    if (isCorrect) {
      this.sessionData.correctCategorizations++;
    } else {
      this.sessionData.incorrectCategorizations++;
      const actualCategory = categorization.selectedItem.actualCategory;
      const confusionKey = `${targetCategory}->${actualCategory}`;
      if (!this.sessionData.crossCategoryConfusions[confusionKey]) {
        this.sessionData.crossCategoryConfusions[confusionKey] = 0;
      }
      this.sessionData.crossCategoryConfusions[confusionKey]++;
    }
    if (!this.sessionData.categoriesByAccuracy[targetCategory]) {
      this.sessionData.categoriesByAccuracy[targetCategory] = { total: 0, correct: 0 };
    }
    this.sessionData.categoriesByAccuracy[targetCategory].total++;
    if (isCorrect) {
      this.sessionData.categoriesByAccuracy[targetCategory].correct++;
    }
    if (!this.sessionData.categoryCompletionTimes[targetCategory]) {
      this.sessionData.categoryCompletionTimes[targetCategory] = [];
    }
    this.sessionData.categoryCompletionTimes[targetCategory].push(responseTime);
    const totalTime = this.interactions.reduce((sum, i) => sum + i.responseTime, 0);
    this.sessionData.averageCategorizationTime = totalTime / this.interactions.length;
  }
  // ========================================================================
  // RELATÓRIOS E ANÁLISES FINAIS
  // ========================================================================
  generateCognitiveReport() {
    return {
      categorizationPerformance: this.analyzeCategorizationPerformance(),
      hierarchicalProcessing: this.analyzeHierarchicalProcessing(),
      conceptualFlexibility: this.analyzeConceptualFlexibility(),
      categoryBoundaries: this.analyzeCategoryBoundaries(),
      adaptiveRecommendations: this.generateAdaptiveRecommendations()
    };
  }
  analyzeCategorizationPerformance() {
    const speedMetrics = this.cognitiveMetrics.categorizationSpeed;
    const semanticMetrics = this.cognitiveMetrics.semanticOrganization;
    return {
      overallAccuracy: this.sessionData.correctCategorizations / this.sessionData.totalCategorizations,
      averageSpeed: this.calculateAverageMetric(speedMetrics, "adjustedSpeed"),
      averageEfficiency: this.calculateAverageMetric(speedMetrics, "efficiency"),
      semanticOrganization: this.calculateAverageMetric(semanticMetrics, "prototypicality"),
      categoryPerformance: this.getCategoryPerformanceBreakdown()
    };
  }
  getCategoryPerformanceBreakdown() {
    const breakdown = {};
    Object.keys(this.sessionData.categoriesByAccuracy).forEach((category) => {
      const data = this.sessionData.categoriesByAccuracy[category];
      const times = this.sessionData.categoryCompletionTimes[category] || [];
      breakdown[category] = {
        accuracy: data.correct / data.total,
        attempts: data.total,
        averageTime: times.reduce((sum, time) => sum + time, 0) / times.length || 0,
        complexity: this.calculateCategoryComplexity(category)
      };
    });
    return breakdown;
  }
  generateAdaptiveRecommendations() {
    const recommendations = [];
    const categoryPerformance = this.getCategoryPerformanceBreakdown();
    Object.entries(categoryPerformance).forEach(([category, performance]) => {
      if (performance.accuracy < 0.6) {
        recommendations.push({
          type: "category_support",
          recommendation: `Reforçar categoria: ${category}`,
          confidence: 0.8,
          details: {
            category,
            currentAccuracy: performance.accuracy,
            suggestedActions: ["visual_enhancement", "feature_highlighting", "prototype_training"]
          }
        });
      }
    });
    const flexibilityMetrics = this.cognitiveMetrics.conceptualFlexibility;
    const avgFlexibility = this.calculateAverageMetric(flexibilityMetrics, "adaptationSpeed");
    if (avgFlexibility < 0.5) {
      recommendations.push({
        type: "flexibility_training",
        recommendation: "Exercícios de alternância categorial",
        confidence: 0.7
      });
    }
    const confusions = Object.entries(this.sessionData.crossCategoryConfusions).sort(([, a], [, b]) => b - a).slice(0, 3);
    confusions.forEach(([confusion, count]) => {
      if (count >= 2) {
        recommendations.push({
          type: "boundary_clarification",
          recommendation: `Clarificar diferenças: ${confusion}`,
          confidence: 0.6
        });
      }
    });
    return recommendations;
  }
  getActivityScore() {
    if (this.sessionData.totalCategorizations === 0) return 0;
    const accuracy = this.sessionData.correctCategorizations / this.sessionData.totalCategorizations;
    const speedFactor = Math.max(0, 1 - (this.sessionData.averageCategorizationTime - 4e3) / 8e3);
    const flexibilityFactor = this.calculateFlexibilityScore();
    return Math.round(accuracy * speedFactor * flexibilityFactor * 1e3);
  }
  calculateFlexibilityScore() {
    const uniqueCategories = new Set(this.interactions.map((i) => i.targetCategory));
    const maxCategories = Object.keys(this.categoryStructure).length;
    return uniqueCategories.size / maxCategories;
  }
}
class EmotionalAssociationCollector extends BaseCollector {
  constructor() {
    super("EmotionalAssociation");
    this.cognitiveMetrics = {
      // Métricas específicas de processamento emocional
      emotionalRecognition: [],
      affectiveProcessing: [],
      empathicResponses: [],
      emotionalMemory: [],
      socialCognition: [],
      // Padrões emocionais
      emotionalValence: [],
      arousalLevels: [],
      emotionalComplexity: [],
      contextualEmotion: [],
      // Inteligência emocional
      emotionalAccuracy: [],
      emotionalFlexibility: [],
      emotionalRegulation: [],
      emotionalIntelligence: []
    };
    this.emotionalCategories = {
      happiness: {
        valence: "positive",
        arousal: "medium",
        complexity: "basic",
        triggers: ["achievement", "surprise", "love", "play"],
        expressions: ["🎁", "🎂", "🌈", "🏆", "👶", "🎪"],
        physiology: ["smile", "laughter", "energy"],
        duration: "short_to_medium"
      },
      sadness: {
        valence: "negative",
        arousal: "low",
        complexity: "basic",
        triggers: ["loss", "separation", "disappointment", "failure"],
        expressions: ["🌧️", "💔", "🥀", "👋", "📰", "🏚️"],
        physiology: ["tears", "low_energy", "withdrawal"],
        duration: "medium_to_long"
      },
      excitement: {
        valence: "positive",
        arousal: "high",
        complexity: "medium",
        triggers: ["anticipation", "adventure", "novelty", "stimulation"],
        expressions: ["🎢", "🎮", "🎭", "🏃", "🎵", "🎊"],
        physiology: ["increased_heart_rate", "energy", "alertness"],
        duration: "short"
      },
      calmness: {
        valence: "neutral_positive",
        arousal: "low",
        complexity: "medium",
        triggers: ["relaxation", "meditation", "nature", "security"],
        expressions: ["🧘", "🌅", "🛀", "📚", "🍵", "🌊"],
        physiology: ["relaxation", "slow_breathing", "peace"],
        duration: "medium_to_long"
      },
      anger: {
        valence: "negative",
        arousal: "high",
        complexity: "medium",
        triggers: ["frustration", "injustice", "threat", "obstruction"],
        expressions: ["🚫", "⏰", "🚗", "📢", "🔥", "⚡"],
        physiology: ["tension", "increased_heart_rate", "heat"],
        duration: "short_to_medium"
      },
      fear: {
        valence: "negative",
        arousal: "high",
        complexity: "basic",
        triggers: ["danger", "unknown", "threat", "vulnerability"],
        expressions: ["🌙", "👻", "🕷️", "⛈️", "🏥", "📉"],
        physiology: ["fight_flight", "sweating", "trembling"],
        duration: "short_to_medium"
      }
    };
    this.sessionData = {
      startTime: null,
      endTime: null,
      totalEmotionalAssociations: 0,
      correctEmotionalAssociations: 0,
      incorrectEmotionalAssociations: 0,
      averageEmotionalProcessingTime: 0,
      emotionsByAccuracy: {},
      emotionalConfusions: {},
      emotionalInsights: [],
      empathyScore: 0,
      difficultyLevel: "beginner"
    };
  }
  // ========================================================================
  // COLETA DE DADOS DE ASSOCIAÇÃO EMOCIONAL
  // ========================================================================
  collectEmotionalAssociation(associationData) {
    const {
      selectedSituation,
      targetEmotion,
      actualEmotion,
      responseTime,
      timestamp,
      emotionalContext = {},
      confidenceLevel = null,
      emotionalJustification = null
    } = associationData;
    const isCorrect = targetEmotion === actualEmotion;
    const association = {
      id: this.generateInteractionId(),
      timestamp,
      responseTime,
      selectedSituation: {
        id: selectedSituation.id,
        image: selectedSituation.image,
        actualEmotion,
        emotionalTriggers: this.identifyEmotionalTriggers(selectedSituation),
        contextualFactors: this.extractContextualFactors(selectedSituation)
      },
      targetEmotion,
      isCorrect,
      emotionalResponse: {
        valence: this.emotionalCategories[actualEmotion]?.valence,
        arousal: this.emotionalCategories[actualEmotion]?.arousal,
        complexity: this.emotionalCategories[actualEmotion]?.complexity
      },
      context: {
        emotionalContext,
        confidenceLevel,
        emotionalJustification,
        difficultyLevel: this.sessionData.difficultyLevel
      }
    };
    this.interactions.push(association);
    this.analyzeEmotionalRecognition(association);
    this.analyzeAffectiveProcessing(association);
    this.analyzeEmpathicResponse(association);
    this.analyzeSocialCognition(association);
    this.updateSessionMetrics(association);
    return association;
  }
  // ========================================================================
  // ANÁLISES COGNITIVAS ESPECIALIZADAS
  // ========================================================================
  analyzeEmotionalRecognition(association) {
    const { responseTime, isCorrect, targetEmotion, selectedSituation } = association;
    const recognitionMetric = {
      timestamp: association.timestamp,
      emotion: targetEmotion,
      situation: selectedSituation.image,
      recognized: isCorrect,
      responseTime,
      emotionalSalience: this.calculateEmotionalSalience(selectedSituation, targetEmotion),
      recognitionAccuracy: isCorrect ? 1 : 0,
      processingDifficulty: this.calculateProcessingDifficulty(targetEmotion, selectedSituation)
    };
    this.cognitiveMetrics.emotionalRecognition.push(recognitionMetric);
    const valenceMetric = {
      timestamp: association.timestamp,
      emotion: targetEmotion,
      valence: association.emotionalResponse.valence,
      arousal: association.emotionalResponse.arousal,
      valenceAccuracy: this.assessValenceAccuracy(association),
      arousalAccuracy: this.assessArousalAccuracy(association)
    };
    this.cognitiveMetrics.emotionalValence.push(valenceMetric);
  }
  analyzeAffectiveProcessing(association) {
    const { responseTime, targetEmotion, selectedSituation } = association;
    const affectiveMetric = {
      timestamp: association.timestamp,
      emotionalComplexity: this.emotionalCategories[targetEmotion]?.complexity,
      processingTime: responseTime,
      affectiveLoad: this.calculateAffectiveLoad(association),
      emotionalIntensity: this.estimateEmotionalIntensity(selectedSituation, targetEmotion),
      affectiveResonance: this.measureAffectiveResonance(association)
    };
    this.cognitiveMetrics.affectiveProcessing.push(affectiveMetric);
  }
  analyzeEmpathicResponse(association) {
    const { isCorrect, targetEmotion, responseTime, context } = association;
    const empathicMetric = {
      timestamp: association.timestamp,
      targetEmotion,
      empathicAccuracy: isCorrect ? 1 : 0,
      empathicLatency: responseTime,
      confidenceLevel: context.confidenceLevel,
      emotionalPerspectiveTaking: this.assessPerspectiveTaking(association),
      emotionalUnderstanding: this.assessEmotionalUnderstanding(association)
    };
    this.cognitiveMetrics.empathicResponses.push(empathicMetric);
  }
  analyzeSocialCognition(association) {
    const { targetEmotion, selectedSituation, isCorrect } = association;
    const socialMetric = {
      timestamp: association.timestamp,
      socialContext: this.extractSocialContext(selectedSituation),
      emotionalContagion: this.assessEmotionalContagion(association),
      socialEmpathy: this.measureSocialEmpathy(association),
      theoryOfMind: this.assessTheoryOfMind(association),
      socialEmotionalLearning: isCorrect ? 1 : 0
    };
    this.cognitiveMetrics.socialCognition.push(socialMetric);
  }
  // ========================================================================
  // CÁLCULOS ESPECIALIZADOS EM INTELIGÊNCIA EMOCIONAL
  // ========================================================================
  calculateEmotionalSalience(situation, emotion) {
    const situationTriggers = this.identifyEmotionalTriggers(situation);
    const emotionTriggers = this.emotionalCategories[emotion]?.triggers || [];
    const overlap = situationTriggers.filter((trigger) => emotionTriggers.includes(trigger)).length;
    return overlap / Math.max(situationTriggers.length, emotionTriggers.length, 1);
  }
  calculateProcessingDifficulty(emotion, situation) {
    const emotionComplexity = {
      "happiness": 0.3,
      "sadness": 0.4,
      "excitement": 0.6,
      "calmness": 0.7,
      "anger": 0.5,
      "fear": 0.4
    };
    const situationAmbiguity = this.calculateSituationAmbiguity(situation);
    const emotionComplexityScore = emotionComplexity[emotion] || 0.5;
    return (emotionComplexityScore + situationAmbiguity) / 2;
  }
  calculateSituationAmbiguity(situation) {
    const ambiguousContexts = {
      "🎁": 0.2,
      // Presente - geralmente alegria
      "🌧️": 0.4,
      // Chuva - pode ser tristeza ou calma
      "🎢": 0.3,
      // Montanha-russa - empolgação ou medo
      "🧘": 0.1,
      // Meditação - claramente calma
      "🚫": 0.6,
      // Proibição - raiva ou frustração
      "🌙": 0.5,
      // Noite - medo ou calma
      "👻": 0.2,
      // Fantasma - claramente medo
      "🎂": 0.2,
      // Bolo - claramente alegria
      "💔": 0.3,
      // Coração partido - tristeza
      "🔥": 0.4
      // Fogo - raiva ou medo
    };
    return ambiguousContexts[situation.image] || 0.5;
  }
  assessValenceAccuracy(association) {
    const { targetEmotion, isCorrect } = association;
    if (isCorrect) return 1;
    const targetValence = this.emotionalCategories[targetEmotion]?.valence;
    const selectedValence = this.emotionalCategories[association.selectedSituation.actualEmotion]?.valence;
    return targetValence === selectedValence ? 0.5 : 0;
  }
  assessArousalAccuracy(association) {
    const { targetEmotion, isCorrect } = association;
    if (isCorrect) return 1;
    const targetArousal = this.emotionalCategories[targetEmotion]?.arousal;
    const selectedArousal = this.emotionalCategories[association.selectedSituation.actualEmotion]?.arousal;
    return targetArousal === selectedArousal ? 0.5 : 0;
  }
  calculateAffectiveLoad(association) {
    const { targetEmotion, responseTime } = association;
    const emotionComplexity = this.emotionalCategories[targetEmotion]?.complexity;
    let loadScore = 0;
    if (emotionComplexity === "basic") loadScore += 0.2;
    else if (emotionComplexity === "medium") loadScore += 0.5;
    else if (emotionComplexity === "high") loadScore += 0.8;
    if (responseTime > 8e3) loadScore += 0.3;
    else if (responseTime > 5e3) loadScore += 0.2;
    const arousal = this.emotionalCategories[targetEmotion]?.arousal;
    if (arousal === "high") loadScore += 0.2;
    return Math.min(1, loadScore);
  }
  estimateEmotionalIntensity(situation, emotion) {
    const intensityMap = {
      "🎁": { happiness: 0.8, excitement: 0.6 },
      "🌧️": { sadness: 0.6, calmness: 0.4 },
      "🎢": { excitement: 0.9, fear: 0.7 },
      "🧘": { calmness: 0.9 },
      "🚫": { anger: 0.8 },
      "🌙": { fear: 0.5, calmness: 0.7 },
      "👻": { fear: 0.9 },
      "🎂": { happiness: 0.8 },
      "💔": { sadness: 0.9 },
      "🔥": { anger: 0.7, fear: 0.6 }
    };
    return intensityMap[situation.image]?.[emotion] || 0.5;
  }
  measureAffectiveResonance(association) {
    const { isCorrect, responseTime, targetEmotion } = association;
    if (!isCorrect) return 0;
    const optimalTime = 4e3;
    const timeFactor = Math.exp(-Math.abs(responseTime - optimalTime) / 2e3);
    return timeFactor;
  }
  assessPerspectiveTaking(association) {
    const { isCorrect, context, targetEmotion } = association;
    let perspectiveScore = isCorrect ? 0.5 : 0;
    if (context.emotionalJustification) {
      perspectiveScore += 0.3;
    }
    if (context.confidenceLevel) {
      if (context.confidenceLevel > 0.7 && isCorrect || context.confidenceLevel < 0.5 && !isCorrect) {
        perspectiveScore += 0.2;
      }
    }
    return Math.min(1, perspectiveScore);
  }
  assessEmotionalUnderstanding(association) {
    const { isCorrect, targetEmotion, selectedSituation } = association;
    let understandingScore = isCorrect ? 0.6 : 0;
    const triggers = this.identifyEmotionalTriggers(selectedSituation);
    const expectedTriggers = this.emotionalCategories[targetEmotion]?.triggers || [];
    const triggerOverlap = triggers.filter((t) => expectedTriggers.includes(t)).length;
    const triggerScore = triggerOverlap / Math.max(expectedTriggers.length, 1);
    understandingScore += triggerScore * 0.4;
    return Math.min(1, understandingScore);
  }
  extractSocialContext(situation) {
    const socialSituations = {
      "🎁": "gift_giving",
      "🎂": "celebration",
      "👋": "separation",
      "🤝": "cooperation",
      "💔": "relationship",
      "🎪": "shared_experience"
    };
    return socialSituations[situation.image] || "individual";
  }
  assessEmotionalContagion(association) {
    const { targetEmotion, isCorrect, responseTime } = association;
    const contagiousEmotions = ["happiness", "excitement", "fear"];
    if (!contagiousEmotions.includes(targetEmotion)) return 0.5;
    if (isCorrect && responseTime < 3e3) return 0.8;
    if (isCorrect && responseTime < 5e3) return 0.6;
    return 0.3;
  }
  measureSocialEmpathy(association) {
    const socialContext = this.extractSocialContext(association.selectedSituation);
    if (socialContext === "individual") return 0.5;
    return association.isCorrect ? 0.8 : 0.2;
  }
  assessTheoryOfMind(association) {
    const { isCorrect, targetEmotion, selectedSituation } = association;
    const theoryOfMindSituations = ["🎁", "👋", "💔", "🎂"];
    if (!theoryOfMindSituations.includes(selectedSituation.image)) return 0.5;
    return isCorrect ? 0.8 : 0.2;
  }
  identifyEmotionalTriggers(situation) {
    const triggerMap = {
      "🎁": ["surprise", "love", "anticipation"],
      "🌧️": ["melancholy", "reflection", "sadness"],
      "🎢": ["thrill", "excitement", "adrenaline"],
      "🧘": ["peace", "mindfulness", "relaxation"],
      "🚫": ["frustration", "limitation", "anger"],
      "🌙": ["mystery", "fear", "calm"],
      "👻": ["fear", "surprise", "supernatural"],
      "🎂": ["celebration", "joy", "achievement"],
      "💔": ["loss", "heartbreak", "sadness"],
      "🔥": ["danger", "passion", "destruction"]
    };
    return triggerMap[situation.image] || [];
  }
  extractContextualFactors(situation) {
    const contextMap = {
      "🎁": ["social", "positive", "anticipatory"],
      "🌧️": ["environmental", "natural", "atmospheric"],
      "🎢": ["recreational", "physical", "intense"],
      "🧘": ["personal", "spiritual", "controlled"],
      "🚫": ["restrictive", "authoritative", "limiting"],
      "🌙": ["temporal", "environmental", "mysterious"],
      "👻": ["supernatural", "threatening", "unknown"],
      "🎂": ["celebratory", "social", "milestone"],
      "💔": ["relational", "personal", "loss"],
      "🔥": ["destructive", "powerful", "transformative"]
    };
    return contextMap[situation.image] || [];
  }
  // ========================================================================
  // ATUALIZAÇÃO DE MÉTRICAS DE SESSÃO
  // ========================================================================
  updateSessionMetrics(association) {
    const { isCorrect, responseTime, targetEmotion } = association;
    this.sessionData.totalEmotionalAssociations++;
    if (isCorrect) {
      this.sessionData.correctEmotionalAssociations++;
    } else {
      this.sessionData.incorrectEmotionalAssociations++;
      const actualEmotion = association.selectedSituation.actualEmotion;
      const confusionKey = `${targetEmotion}->${actualEmotion}`;
      if (!this.sessionData.emotionalConfusions[confusionKey]) {
        this.sessionData.emotionalConfusions[confusionKey] = 0;
      }
      this.sessionData.emotionalConfusions[confusionKey]++;
    }
    if (!this.sessionData.emotionsByAccuracy[targetEmotion]) {
      this.sessionData.emotionsByAccuracy[targetEmotion] = { total: 0, correct: 0 };
    }
    this.sessionData.emotionsByAccuracy[targetEmotion].total++;
    if (isCorrect) {
      this.sessionData.emotionsByAccuracy[targetEmotion].correct++;
    }
    const totalTime = this.interactions.reduce((sum, i) => sum + i.responseTime, 0);
    this.sessionData.averageEmotionalProcessingTime = totalTime / this.interactions.length;
    this.updateEmpathyScore();
  }
  updateEmpathyScore() {
    const empathicMetrics = this.cognitiveMetrics.empathicResponses;
    if (empathicMetrics.length === 0) {
      this.sessionData.empathyScore = 0;
      return;
    }
    const totalEmpathicAccuracy = empathicMetrics.reduce(
      (sum, metric) => sum + metric.empathicAccuracy,
      0
    );
    const totalPerspectiveTaking = empathicMetrics.reduce(
      (sum, metric) => sum + metric.emotionalPerspectiveTaking,
      0
    );
    const totalEmotionalUnderstanding = empathicMetrics.reduce(
      (sum, metric) => sum + metric.emotionalUnderstanding,
      0
    );
    this.sessionData.empathyScore = totalEmpathicAccuracy / empathicMetrics.length * 0.4 + totalPerspectiveTaking / empathicMetrics.length * 0.3 + totalEmotionalUnderstanding / empathicMetrics.length * 0.3;
  }
  // ========================================================================
  // RELATÓRIOS E ANÁLISES FINAIS
  // ========================================================================
  generateCognitiveReport() {
    return {
      emotionalIntelligence: this.analyzeEmotionalIntelligence(),
      empathicCapacity: this.analyzeEmpathicCapacity(),
      affectiveProcessing: this.analyzeAffectiveProcessing(),
      socialCognition: this.analyzeSocialCognition(),
      emotionalRegulation: this.analyzeEmotionalRegulation(),
      adaptiveRecommendations: this.generateAdaptiveRecommendations()
    };
  }
  analyzeEmotionalIntelligence() {
    const recognitionMetrics = this.cognitiveMetrics.emotionalRecognition;
    this.cognitiveMetrics.empathicResponses;
    return {
      overallAccuracy: this.sessionData.correctEmotionalAssociations / this.sessionData.totalEmotionalAssociations,
      recognitionSpeed: this.calculateAverageMetric(recognitionMetrics, "responseTime"),
      empathyScore: this.sessionData.empathyScore,
      emotionalRangeRecognized: Object.keys(this.sessionData.emotionsByAccuracy).length,
      emotionalComplexityHandling: this.assessComplexityHandling()
    };
  }
  analyzeEmpathicCapacity() {
    const empathicMetrics = this.cognitiveMetrics.empathicResponses;
    return {
      averageEmpathicAccuracy: this.calculateAverageMetric(empathicMetrics, "empathicAccuracy"),
      perspectiveTaking: this.calculateAverageMetric(empathicMetrics, "emotionalPerspectiveTaking"),
      emotionalUnderstanding: this.calculateAverageMetric(empathicMetrics, "emotionalUnderstanding"),
      empathicLatency: this.calculateAverageMetric(empathicMetrics, "empathicLatency")
    };
  }
  assessComplexityHandling() {
    const complexityPerformance = {};
    this.interactions.forEach((interaction) => {
      const emotion = interaction.targetEmotion;
      const complexity = this.emotionalCategories[emotion]?.complexity;
      if (!complexityPerformance[complexity]) {
        complexityPerformance[complexity] = { total: 0, correct: 0 };
      }
      complexityPerformance[complexity].total++;
      if (interaction.isCorrect) {
        complexityPerformance[complexity].correct++;
      }
    });
    Object.keys(complexityPerformance).forEach((complexity) => {
      const data = complexityPerformance[complexity];
      complexityPerformance[complexity].accuracy = data.correct / data.total;
    });
    return complexityPerformance;
  }
  generateAdaptiveRecommendations() {
    const recommendations = [];
    const emotionPerformance = {};
    Object.entries(this.sessionData.emotionsByAccuracy).forEach(([emotion, data]) => {
      emotionPerformance[emotion] = data.correct / data.total;
    });
    const weakEmotions = Object.entries(emotionPerformance).filter(([emotion, accuracy]) => accuracy < 0.6).map(([emotion]) => emotion);
    if (weakEmotions.length > 0) {
      recommendations.push({
        type: "emotional_training",
        recommendation: `Reforçar reconhecimento: ${weakEmotions.join(", ")}`,
        confidence: 0.8,
        details: {
          emotions: weakEmotions,
          suggestedActivities: ["emotion_labeling", "context_analysis", "empathy_exercises"]
        }
      });
    }
    if (this.sessionData.empathyScore < 0.5) {
      recommendations.push({
        type: "empathy_development",
        recommendation: "Exercícios de desenvolvimento empático",
        confidence: 0.7,
        details: {
          currentScore: this.sessionData.empathyScore,
          suggestedActivities: ["perspective_taking", "emotional_storytelling", "social_scenarios"]
        }
      });
    }
    const majorConfusions = Object.entries(this.sessionData.emotionalConfusions).filter(([confusion, count]) => count >= 2).sort(([, a], [, b]) => b - a).slice(0, 2);
    majorConfusions.forEach(([confusion, count]) => {
      recommendations.push({
        type: "emotional_discrimination",
        recommendation: `Diferenciar emoções: ${confusion}`,
        confidence: 0.6,
        details: {
          confusionPattern: confusion,
          frequency: count
        }
      });
    });
    return recommendations;
  }
  getActivityScore() {
    if (this.sessionData.totalEmotionalAssociations === 0) return 0;
    const accuracy = this.sessionData.correctEmotionalAssociations / this.sessionData.totalEmotionalAssociations;
    const speedFactor = Math.max(0, 1 - (this.sessionData.averageEmotionalProcessingTime - 5e3) / 1e4);
    const empathyFactor = this.sessionData.empathyScore;
    const complexityFactor = this.calculateComplexityFactor();
    return Math.round(accuracy * speedFactor * empathyFactor * complexityFactor * 1e3);
  }
  calculateComplexityFactor() {
    const complexityHandling = this.assessComplexityHandling();
    let totalScore = 0;
    let totalWeight = 0;
    Object.entries(complexityHandling).forEach(([complexity, data]) => {
      const weight = complexity === "basic" ? 1 : complexity === "medium" ? 1.5 : 2;
      totalScore += data.accuracy * weight;
      totalWeight += weight;
    });
    return totalWeight > 0 ? totalScore / totalWeight : 0.5;
  }
}
class FunctionalAssociationCollector extends BaseCollector {
  constructor() {
    super("FunctionalAssociation");
    this.cognitiveMetrics = {
      // Métricas específicas de raciocínio funcional
      functionalReasoning: [],
      pragmaticThinking: [],
      purposeRecognition: [],
      toolCognition: [],
      functionalTransfer: [],
      // Padrões funcionais
      functionalCategories: [],
      affordancePerception: [],
      contextualUsage: [],
      functionalFlexibility: [],
      // Análise de conhecimento prático
      practicalKnowledge: [],
      proceduraMemory: [],
      functionalMapping: [],
      goalDirectedThinking: []
    };
    this.functionalDomains = {
      cutting: {
        purpose: "separation",
        context: ["kitchen", "craft", "garden", "office"],
        affordances: ["sharp_edge", "handle", "leverage"],
        objects: ["✂️", "🔪", "🪚", "⚔️", "💎"],
        complexity: "medium",
        skills: ["precision", "force_control", "safety"]
      },
      writing: {
        purpose: "communication",
        context: ["school", "office", "art", "documentation"],
        affordances: ["mark_making", "grip", "precision"],
        objects: ["✏️", "🖊️", "🖌️", "🖍️", "⌨️"],
        complexity: "low",
        skills: ["fine_motor", "cognitive", "symbolic"]
      },
      measuring: {
        purpose: "quantification",
        context: ["construction", "science", "cooking", "health"],
        affordances: ["scale", "reference", "precision"],
        objects: ["📏", "📐", "⚖️", "🌡️", "⏰"],
        complexity: "high",
        skills: ["mathematical", "analytical", "comparative"]
      },
      communication: {
        purpose: "information_exchange",
        context: ["personal", "business", "emergency", "entertainment"],
        affordances: ["signal_transmission", "interface", "connectivity"],
        objects: ["📱", "📞", "📻", "📺", "💻"],
        complexity: "medium",
        skills: ["social", "technological", "linguistic"]
      },
      transportation: {
        purpose: "mobility",
        context: ["daily_commute", "travel", "cargo", "recreation"],
        affordances: ["movement", "carrying_capacity", "speed"],
        objects: ["🚗", "🚲", "✈️", "🚢", "🛴"],
        complexity: "medium",
        skills: ["coordination", "navigation", "operation"]
      },
      protection: {
        purpose: "safety",
        context: ["work", "weather", "sports", "hazards"],
        affordances: ["barrier", "shielding", "durability"],
        objects: ["⛑️", "👓", "🧤", "☂️", "🦺"],
        complexity: "low",
        skills: ["risk_assessment", "prevention", "adaptation"]
      }
    };
    this.sessionData = {
      startTime: null,
      endTime: null,
      totalFunctionalAssociations: 0,
      correctFunctionalAssociations: 0,
      incorrectFunctionalAssociations: 0,
      averageFunctionalProcessingTime: 0,
      functionsByAccuracy: {},
      functionalConfusions: {},
      affordanceRecognition: 0,
      pragmaticScore: 0,
      difficultyLevel: "beginner"
    };
  }
  // ========================================================================
  // COLETA DE DADOS DE ASSOCIAÇÃO FUNCIONAL
  // ========================================================================
  collectFunctionalAssociation(associationData) {
    const {
      selectedObject,
      targetFunction,
      actualFunction,
      responseTime,
      timestamp,
      contextualClues = [],
      affordancesIdentified = [],
      functionalJustification = null
    } = associationData;
    const isCorrect = targetFunction === actualFunction;
    const association = {
      id: this.generateInteractionId(),
      timestamp,
      responseTime,
      selectedObject: {
        id: selectedObject.id,
        image: selectedObject.image,
        actualFunction,
        affordances: this.identifyAffordances(selectedObject),
        contexts: this.extractContexts(selectedObject)
      },
      targetFunction,
      isCorrect,
      functionalAnalysis: {
        purpose: this.functionalDomains[actualFunction]?.purpose,
        complexity: this.functionalDomains[actualFunction]?.complexity,
        skillsRequired: this.functionalDomains[actualFunction]?.skills
      },
      context: {
        contextualClues,
        affordancesIdentified,
        functionalJustification,
        difficultyLevel: this.sessionData.difficultyLevel
      }
    };
    this.interactions.push(association);
    this.analyzeFunctionalReasoning(association);
    this.analyzePragmaticThinking(association);
    this.analyzeAffordancePerception(association);
    this.analyzeToolCognition(association);
    this.updateSessionMetrics(association);
    return association;
  }
  // ========================================================================
  // ANÁLISES COGNITIVAS ESPECIALIZADAS
  // ========================================================================
  analyzeFunctionalReasoning(association) {
    const { responseTime, isCorrect, targetFunction, selectedObject } = association;
    const reasoningMetric = {
      timestamp: association.timestamp,
      function: targetFunction,
      object: selectedObject.image,
      recognized: isCorrect,
      responseTime,
      functionalComplexity: this.calculateFunctionalComplexity(targetFunction),
      reasoningAccuracy: isCorrect ? 1 : 0,
      functionalSalience: this.calculateFunctionalSalience(selectedObject, targetFunction)
    };
    this.cognitiveMetrics.functionalReasoning.push(reasoningMetric);
    const purposeMetric = {
      timestamp: association.timestamp,
      function: targetFunction,
      purpose: association.functionalAnalysis.purpose,
      purposeRecognized: isCorrect,
      purposeClarity: this.assessPurposeClarity(association),
      goalAlignment: this.assessGoalAlignment(association)
    };
    this.cognitiveMetrics.purposeRecognition.push(purposeMetric);
  }
  analyzePragmaticThinking(association) {
    const { targetFunction, selectedObject, context } = association;
    const pragmaticMetric = {
      timestamp: association.timestamp,
      function: targetFunction,
      contextualAdaptation: this.assessContextualAdaptation(association),
      practicalApplication: this.assessPracticalApplication(association),
      situationalRelevance: this.assessSituationalRelevance(association),
      pragmaticFlexibility: this.assessPragmaticFlexibility(association)
    };
    this.cognitiveMetrics.pragmaticThinking.push(pragmaticMetric);
  }
  analyzeAffordancePerception(association) {
    const { selectedObject, context, isCorrect } = association;
    const affordanceMetric = {
      timestamp: association.timestamp,
      object: selectedObject.image,
      affordancesIdentified: context.affordancesIdentified,
      affordancesActual: selectedObject.affordances,
      affordanceAccuracy: this.calculateAffordanceAccuracy(context.affordancesIdentified, selectedObject.affordances),
      perceptualSensitivity: this.assessPerceptualSensitivity(association),
      functionalPerception: isCorrect ? 1 : 0
    };
    this.cognitiveMetrics.affordancePerception.push(affordanceMetric);
  }
  analyzeToolCognition(association) {
    const { targetFunction, selectedObject, isCorrect } = association;
    const toolMetric = {
      timestamp: association.timestamp,
      tool: selectedObject.image,
      function: targetFunction,
      toolRecognition: isCorrect,
      toolComplexity: this.calculateToolComplexity(selectedObject),
      functionalMapping: this.assessFunctionalMapping(association),
      toolUseKnowledge: this.assessToolUseKnowledge(association)
    };
    this.cognitiveMetrics.toolCognition.push(toolMetric);
  }
  // ========================================================================
  // CÁLCULOS ESPECIALIZADOS
  // ========================================================================
  calculateFunctionalComplexity(functionType) {
    const complexityMap = {
      cutting: 0.6,
      writing: 0.3,
      measuring: 0.8,
      communication: 0.5,
      transportation: 0.6,
      protection: 0.4
    };
    return complexityMap[functionType] || 0.5;
  }
  calculateFunctionalSalience(object, targetFunction) {
    const objectAffordances = this.identifyAffordances(object);
    const functionAffordances = this.functionalDomains[targetFunction]?.affordances || [];
    const overlap = objectAffordances.filter(
      (affordance) => functionAffordances.includes(affordance)
    ).length;
    return overlap / Math.max(functionAffordances.length, 1);
  }
  assessPurposeClarity(association) {
    const { targetFunction, selectedObject } = association;
    const functionPurpose = this.functionalDomains[targetFunction]?.purpose;
    const purposeSpecificity = {
      "separation": 0.8,
      "communication": 0.6,
      "quantification": 0.9,
      "information_exchange": 0.5,
      "mobility": 0.7,
      "safety": 0.8
    };
    return purposeSpecificity[functionPurpose] || 0.5;
  }
  assessGoalAlignment(association) {
    const { isCorrect, targetFunction, selectedObject } = association;
    if (!isCorrect) return 0;
    const objectContexts = this.extractContexts(selectedObject);
    const functionContexts = this.functionalDomains[targetFunction]?.context || [];
    const contextOverlap = objectContexts.filter(
      (context) => functionContexts.includes(context)
    ).length;
    return contextOverlap / Math.max(functionContexts.length, 1);
  }
  assessContextualAdaptation(association) {
    const { context, targetFunction } = association;
    const functionContexts = this.functionalDomains[targetFunction]?.context || [];
    const contextualClues = context.contextualClues;
    const relevantClues = contextualClues.filter(
      (clue) => functionContexts.some((context2) => context2.includes(clue))
    );
    return contextualClues.length > 0 ? relevantClues.length / contextualClues.length : 0.5;
  }
  assessPracticalApplication(association) {
    const { isCorrect, targetFunction, context } = association;
    let practicalScore = isCorrect ? 0.6 : 0;
    if (context.functionalJustification) {
      practicalScore += 0.2;
    }
    const affordanceAccuracy = this.calculateAffordanceAccuracy(
      context.affordancesIdentified,
      association.selectedObject.affordances
    );
    practicalScore += affordanceAccuracy * 0.2;
    return Math.min(1, practicalScore);
  }
  assessSituationalRelevance(association) {
    const { targetFunction, selectedObject } = association;
    const objectContexts = this.extractContexts(selectedObject);
    const functionContexts = this.functionalDomains[targetFunction]?.context || [];
    const intersection = objectContexts.filter(
      (context) => functionContexts.includes(context)
    );
    const union = [.../* @__PURE__ */ new Set([...objectContexts, ...functionContexts])];
    return union.length > 0 ? intersection.length / union.length : 0;
  }
  assessPragmaticFlexibility(association) {
    const recentAssociations = this.interactions.slice(-5);
    const functionTypes = recentAssociations.map((a) => a.targetFunction);
    const uniqueFunctions = new Set(functionTypes);
    return uniqueFunctions.size / Math.min(functionTypes.length, 6);
  }
  calculateAffordanceAccuracy(identifiedAffordances, actualAffordances) {
    if (actualAffordances.length === 0) return 0;
    const correctAffordances = identifiedAffordances.filter(
      (affordance) => actualAffordances.includes(affordance)
    );
    return correctAffordances.length / actualAffordances.length;
  }
  assessPerceptualSensitivity(association) {
    const { selectedObject, context } = association;
    const actualAffordances = selectedObject.affordances;
    const identifiedAffordances = context.affordancesIdentified;
    const subtleAffordances = actualAffordances.filter(
      (affordance) => !["handle", "sharp_edge", "button"].includes(affordance)
    );
    const detectedSubtle = identifiedAffordances.filter(
      (affordance) => subtleAffordances.includes(affordance)
    );
    return subtleAffordances.length > 0 ? detectedSubtle.length / subtleAffordances.length : 0.5;
  }
  calculateToolComplexity(object) {
    const complexityMap = {
      "✂️": 0.4,
      "🔪": 0.5,
      "🪚": 0.7,
      "⚔️": 0.8,
      "💎": 0.3,
      "✏️": 0.2,
      "🖊️": 0.3,
      "🖌️": 0.4,
      "🖍️": 0.2,
      "⌨️": 0.6,
      "📏": 0.3,
      "📐": 0.5,
      "⚖️": 0.7,
      "🌡️": 0.6,
      "⏰": 0.8,
      "📱": 0.8,
      "📞": 0.4,
      "📻": 0.5,
      "📺": 0.6,
      "💻": 0.9,
      "🚗": 0.9,
      "🚲": 0.4,
      "✈️": 1,
      "🚢": 0.8,
      "🛴": 0.3,
      "⛑️": 0.3,
      "👓": 0.2,
      "🧤": 0.2,
      "☂️": 0.3,
      "🦺": 0.4
    };
    return complexityMap[object.image] || 0.5;
  }
  assessFunctionalMapping(association) {
    const { isCorrect, targetFunction, selectedObject } = association;
    if (!isCorrect) {
      const actualFunction = selectedObject.actualFunction;
      const mappingError = this.classifyMappingError(targetFunction, actualFunction);
      return {
        accuracy: 0,
        errorType: mappingError,
        mappingDistance: this.calculateMappingDistance(targetFunction, actualFunction)
      };
    }
    return {
      accuracy: 1,
      errorType: "none",
      mappingDistance: 0
    };
  }
  assessToolUseKnowledge(association) {
    const { targetFunction, selectedObject, isCorrect } = association;
    let knowledgeScore = isCorrect ? 0.7 : 0;
    const requiredSkills = this.functionalDomains[targetFunction]?.skills || [];
    const demonstratedSkills = this.inferDemonstratedSkills(association);
    const skillOverlap = demonstratedSkills.filter(
      (skill) => requiredSkills.includes(skill)
    ).length;
    knowledgeScore += skillOverlap / Math.max(requiredSkills.length, 1) * 0.3;
    return Math.min(1, knowledgeScore);
  }
  classifyMappingError(targetFunction, actualFunction) {
    const targetDomain = this.functionalDomains[targetFunction];
    const actualDomain = this.functionalDomains[actualFunction];
    if (!targetDomain || !actualDomain) return "unknown_mapping";
    if (targetDomain.purpose === actualDomain.purpose) {
      return "purpose_confusion";
    }
    const contextOverlap = targetDomain.context.filter(
      (context) => actualDomain.context.includes(context)
    ).length;
    if (contextOverlap > 0) return "context_confusion";
    const affordanceOverlap = targetDomain.affordances.filter(
      (affordance) => actualDomain.affordances.includes(affordance)
    ).length;
    if (affordanceOverlap > 0) return "affordance_confusion";
    return "distant_mapping";
  }
  calculateMappingDistance(targetFunction, actualFunction) {
    const targetDomain = this.functionalDomains[targetFunction];
    const actualDomain = this.functionalDomains[actualFunction];
    if (!targetDomain || !actualDomain) return 1;
    const purposeDistance = targetDomain.purpose === actualDomain.purpose ? 0 : 0.4;
    const contextOverlap = targetDomain.context.filter(
      (context) => actualDomain.context.includes(context)
    ).length;
    const contextDistance = (1 - contextOverlap / Math.max(targetDomain.context.length, actualDomain.context.length)) * 0.3;
    const affordanceOverlap = targetDomain.affordances.filter(
      (affordance) => actualDomain.affordances.includes(affordance)
    ).length;
    const affordanceDistance = (1 - affordanceOverlap / Math.max(targetDomain.affordances.length, actualDomain.affordances.length)) * 0.3;
    return purposeDistance + contextDistance + affordanceDistance;
  }
  inferDemonstratedSkills(association) {
    const { responseTime, context, isCorrect } = association;
    const skills = [];
    if (context.functionalJustification && context.functionalJustification.length > 10) {
      skills.push("analytical");
    }
    if (responseTime < 4e3) {
      skills.push("cognitive");
    }
    if (context.affordancesIdentified.length > 2) {
      skills.push("precision");
    }
    if (isCorrect) {
      skills.push("practical");
    }
    return skills;
  }
  identifyAffordances(object) {
    const affordanceMap = {
      "✂️": ["sharp_edge", "handle", "pivot", "grip"],
      "🔪": ["sharp_edge", "handle", "blade", "weight"],
      "🪚": ["teeth", "handle", "cutting_edge", "leverage"],
      "⚔️": ["sharp_edge", "handle", "point", "balance"],
      "💎": ["hardness", "cutting_edge", "precision", "durability"],
      "✏️": ["mark_making", "grip", "erasable", "portable"],
      "🖊️": ["mark_making", "grip", "permanent", "precision"],
      "🖌️": ["mark_making", "grip", "flexible", "artistic"],
      "🖍️": ["mark_making", "grip", "colorful", "waxy"],
      "⌨️": ["input", "tactile", "digital", "layout"],
      "📏": ["measurement", "straight_edge", "markings", "rigid"],
      "📐": ["angle_measurement", "triangular", "markings", "precision"],
      "⚖️": ["balance", "comparison", "precision", "calibration"],
      "🌡️": ["temperature_sensing", "calibration", "reading", "sensitive"],
      "⏰": ["time_display", "alarm", "precision", "regulation"],
      "📱": ["communication", "interface", "portable", "connectivity"],
      "📞": ["communication", "handset", "audio", "dial"],
      "📻": ["audio", "reception", "tuning", "portable"],
      "📺": ["visual", "audio", "reception", "display"],
      "💻": ["computing", "interface", "processing", "connectivity"],
      "🚗": ["transportation", "enclosed", "steering", "powered"],
      "🚲": ["transportation", "pedal_powered", "steering", "balance"],
      "✈️": ["flight", "high_speed", "navigation", "capacity"],
      "🚢": ["water_transport", "large_capacity", "navigation", "buoyancy"],
      "🛴": ["transportation", "push_powered", "balance", "portable"],
      "⛑️": ["protection", "head_coverage", "hard_shell", "fitting"],
      "👓": ["vision_correction", "transparent", "fitting", "lightweight"],
      "🧤": ["hand_protection", "grip", "warmth", "flexibility"],
      "☂️": ["weather_protection", "expandable", "portable", "waterproof"],
      "🦺": ["visibility", "protection", "reflective", "wearable"]
    };
    return affordanceMap[object.image] || [];
  }
  extractContexts(object) {
    const contextMap = {
      "✂️": ["office", "craft", "household"],
      "🔪": ["kitchen", "cooking", "food_prep"],
      "🪚": ["construction", "woodworking", "crafts"],
      "⚔️": ["historical", "ceremonial", "combat"],
      "💎": ["jewelry", "industrial", "cutting"],
      "✏️": ["school", "office", "drawing"],
      "🖊️": ["office", "writing", "documentation"],
      "🖌️": ["art", "painting", "creative"],
      "🖍️": ["school", "children", "coloring"],
      "⌨️": ["computing", "office", "digital"],
      "📏": ["school", "construction", "measurement"],
      "📐": ["school", "technical_drawing", "mathematics"],
      "⚖️": ["legal", "science", "cooking"],
      "🌡️": ["medical", "weather", "science"],
      "⏰": ["household", "scheduling", "time_management"],
      "📱": ["personal", "business", "social"],
      "📞": ["office", "household", "emergency"],
      "📻": ["entertainment", "news", "emergency"],
      "📺": ["entertainment", "household", "information"],
      "💻": ["work", "education", "entertainment"],
      "🚗": ["transportation", "daily_commute", "travel"],
      "🚲": ["recreation", "exercise", "eco_transport"],
      "✈️": ["travel", "business", "vacation"],
      "🚢": ["cargo", "cruise", "transportation"],
      "🛴": ["recreation", "urban_transport", "children"],
      "⛑️": ["construction", "safety", "industrial"],
      "👓": ["vision", "medical", "daily_use"],
      "🧤": ["work", "weather", "protection"],
      "☂️": ["weather", "outdoor", "travel"],
      "🦺": ["safety", "work", "visibility"]
    };
    return contextMap[object.image] || [];
  }
  // ========================================================================
  // ATUALIZAÇÃO DE MÉTRICAS DE SESSÃO
  // ========================================================================
  updateSessionMetrics(association) {
    const { isCorrect, responseTime, targetFunction } = association;
    this.sessionData.totalFunctionalAssociations++;
    if (isCorrect) {
      this.sessionData.correctFunctionalAssociations++;
    } else {
      this.sessionData.incorrectFunctionalAssociations++;
      const actualFunction = association.selectedObject.actualFunction;
      const confusionKey = `${targetFunction}->${actualFunction}`;
      if (!this.sessionData.functionalConfusions[confusionKey]) {
        this.sessionData.functionalConfusions[confusionKey] = 0;
      }
      this.sessionData.functionalConfusions[confusionKey]++;
    }
    if (!this.sessionData.functionsByAccuracy[targetFunction]) {
      this.sessionData.functionsByAccuracy[targetFunction] = { total: 0, correct: 0 };
    }
    this.sessionData.functionsByAccuracy[targetFunction].total++;
    if (isCorrect) {
      this.sessionData.functionsByAccuracy[targetFunction].correct++;
    }
    const totalTime = this.interactions.reduce((sum, i) => sum + i.responseTime, 0);
    this.sessionData.averageFunctionalProcessingTime = totalTime / this.interactions.length;
    this.updateAffordanceRecognition();
    this.updatePragmaticScore();
  }
  updateAffordanceRecognition() {
    const affordanceMetrics = this.cognitiveMetrics.affordancePerception;
    if (affordanceMetrics.length === 0) {
      this.sessionData.affordanceRecognition = 0;
      return;
    }
    const totalAccuracy = affordanceMetrics.reduce(
      (sum, metric) => sum + metric.affordanceAccuracy,
      0
    );
    this.sessionData.affordanceRecognition = totalAccuracy / affordanceMetrics.length;
  }
  updatePragmaticScore() {
    const pragmaticMetrics = this.cognitiveMetrics.pragmaticThinking;
    if (pragmaticMetrics.length === 0) {
      this.sessionData.pragmaticScore = 0;
      return;
    }
    const totalAdaptation = pragmaticMetrics.reduce(
      (sum, metric) => sum + metric.contextualAdaptation,
      0
    );
    const totalApplication = pragmaticMetrics.reduce(
      (sum, metric) => sum + metric.practicalApplication,
      0
    );
    const totalRelevance = pragmaticMetrics.reduce(
      (sum, metric) => sum + metric.situationalRelevance,
      0
    );
    this.sessionData.pragmaticScore = totalAdaptation / pragmaticMetrics.length * 0.3 + totalApplication / pragmaticMetrics.length * 0.4 + totalRelevance / pragmaticMetrics.length * 0.3;
  }
  // ========================================================================
  // RELATÓRIOS E ANÁLISES FINAIS
  // ========================================================================
  generateCognitiveReport() {
    return {
      functionalReasoning: this.analyzeFunctionalReasoning(),
      pragmaticThinking: this.analyzePragmaticThinking(),
      toolCognition: this.analyzeToolCognition(),
      affordancePerception: this.analyzeAffordancePerception(),
      adaptiveRecommendations: this.generateAdaptiveRecommendations()
    };
  }
  assessComplexityHandling() {
    const complexityPerformance = {};
    this.interactions.forEach((interaction) => {
      const func = interaction.targetFunction;
      const complexity = this.functionalDomains[func]?.complexity;
      if (!complexityPerformance[complexity]) {
        complexityPerformance[complexity] = { total: 0, correct: 0 };
      }
      complexityPerformance[complexity].total++;
      if (interaction.isCorrect) {
        complexityPerformance[complexity].correct++;
      }
    });
    Object.keys(complexityPerformance).forEach((complexity) => {
      const data = complexityPerformance[complexity];
      complexityPerformance[complexity].accuracy = data.correct / data.total;
    });
    return complexityPerformance;
  }
  analyzeFunctionalMapping() {
    const mappingAnalysis = {
      correctMappings: 0,
      mappingErrors: {},
      averageMappingDistance: 0
    };
    this.interactions.forEach((interaction) => {
      if (interaction.isCorrect) {
        mappingAnalysis.correctMappings++;
      } else {
        const errorType = this.classifyMappingError(
          interaction.targetFunction,
          interaction.selectedObject.actualFunction
        );
        mappingAnalysis.mappingErrors[errorType] = (mappingAnalysis.mappingErrors[errorType] || 0) + 1;
      }
    });
    const toolMetrics = this.cognitiveMetrics.toolCognition;
    if (toolMetrics.length > 0) {
      const totalDistance = toolMetrics.reduce((sum, metric) => {
        return sum + (metric.functionalMapping.mappingDistance || 0);
      }, 0);
      mappingAnalysis.averageMappingDistance = totalDistance / toolMetrics.length;
    }
    return mappingAnalysis;
  }
  generateAdaptiveRecommendations() {
    const recommendations = [];
    const functionPerformance = {};
    Object.entries(this.sessionData.functionsByAccuracy).forEach(([func, data]) => {
      functionPerformance[func] = data.correct / data.total;
    });
    const weakFunctions = Object.entries(functionPerformance).filter(([func, accuracy]) => accuracy < 0.6).map(([func]) => func);
    if (weakFunctions.length > 0) {
      recommendations.push({
        type: "functional_training",
        recommendation: `Reforçar funções: ${weakFunctions.join(", ")}`,
        confidence: 0.8,
        details: {
          functions: weakFunctions,
          suggestedActivities: ["tool_exploration", "context_training", "affordance_highlighting"]
        }
      });
    }
    if (this.sessionData.affordanceRecognition < 0.5) {
      recommendations.push({
        type: "affordance_training",
        recommendation: "Exercícios de percepção de affordances",
        confidence: 0.7,
        details: {
          currentScore: this.sessionData.affordanceRecognition,
          suggestedActivities: ["perceptual_training", "feature_analysis", "hands_on_exploration"]
        }
      });
    }
    if (this.sessionData.pragmaticScore < 0.6) {
      recommendations.push({
        type: "pragmatic_training",
        recommendation: "Desenvolvimento de pensamento pragmático",
        confidence: 0.7,
        details: {
          currentScore: this.sessionData.pragmaticScore,
          suggestedActivities: ["contextual_exercises", "problem_solving", "real_world_applications"]
        }
      });
    }
    return recommendations;
  }
  getActivityScore() {
    if (this.sessionData.totalFunctionalAssociations === 0) return 0;
    const accuracy = this.sessionData.correctFunctionalAssociations / this.sessionData.totalFunctionalAssociations;
    const speedFactor = Math.max(0, 1 - (this.sessionData.averageFunctionalProcessingTime - 5e3) / 1e4);
    const affordanceFactor = this.sessionData.affordanceRecognition;
    const pragmaticFactor = this.sessionData.pragmaticScore;
    return Math.round(accuracy * speedFactor * affordanceFactor * pragmaticFactor * 1e3);
  }
}
class ConceptualAssociationCollector extends BaseCollector {
  constructor() {
    super("ConceptualAssociation");
    this.cognitiveMetrics = {
      // Métricas específicas de pensamento conceitual
      abstractThinking: [],
      conceptualFlexibility: [],
      metaphoricalReasoning: [],
      analogicalThinking: [],
      conceptualIntegration: [],
      // Padrões conceituais
      conceptualCategories: [],
      semanticNetworks: [],
      conceptualBridging: [],
      abstractionLevels: [],
      // Análise de representações mentais
      symbolicThinking: [],
      conceptualMapping: [],
      meaningMaking: [],
      conceptualDepth: []
    };
    this.conceptualDomains = {
      time: {
        category: "temporal",
        abstractLevel: "high",
        concepts: ["past", "future", "duration", "sequence", "moment"],
        symbols: ["⏰", "📅", "⌛", "🕐", "⏳"],
        metaphors: ["river", "circle", "arrow", "wave"],
        properties: ["linear", "cyclical", "relative", "measurable"],
        cognitive_load: "high"
      },
      freedom: {
        category: "philosophical",
        abstractLevel: "very_high",
        concepts: ["liberation", "choice", "autonomy", "independence", "rights"],
        symbols: ["🕊️", "🗽", "🆓", "🔓", "🌅"],
        metaphors: ["bird", "open_road", "breaking_chains", "soaring"],
        properties: ["subjective", "cultural", "political", "personal"],
        cognitive_load: "very_high"
      },
      growth: {
        category: "biological_metaphorical",
        abstractLevel: "medium",
        concepts: ["development", "expansion", "progress", "maturation", "evolution"],
        symbols: ["🌱", "📈", "🌳", "🏗️", "💪"],
        metaphors: ["plant", "building", "journey", "transformation"],
        properties: ["gradual", "directional", "potential", "organic"],
        cognitive_load: "medium"
      },
      connection: {
        category: "relational",
        abstractLevel: "medium",
        concepts: ["bond", "link", "relationship", "network", "unity"],
        symbols: ["🔗", "🌐", "🤝", "💞", "🧩"],
        metaphors: ["bridge", "web", "chain", "fabric"],
        properties: ["reciprocal", "structural", "dynamic", "meaningful"],
        cognitive_load: "medium"
      },
      balance: {
        category: "systemic",
        abstractLevel: "medium",
        concepts: ["equilibrium", "harmony", "stability", "proportion", "moderation"],
        symbols: ["⚖️", "☯️", "⚡", "🎭", "🌊"],
        metaphors: ["scales", "tightrope", "dance", "equation"],
        properties: ["dynamic", "oppositional", "harmonious", "centered"],
        cognitive_load: "medium"
      },
      transformation: {
        category: "processual",
        abstractLevel: "high",
        concepts: ["change", "metamorphosis", "conversion", "evolution", "rebirth"],
        symbols: ["🦋", "🔄", "⚡", "🌙", "🔮"],
        metaphors: ["butterfly", "alchemy", "phoenix", "seasons"],
        properties: ["processual", "irreversible", "emergent", "profound"],
        cognitive_load: "high"
      }
    };
    this.sessionData = {
      startTime: null,
      endTime: null,
      totalConceptualAssociations: 0,
      correctConceptualAssociations: 0,
      incorrectConceptualAssociations: 0,
      averageConceptualProcessingTime: 0,
      conceptsByAccuracy: {},
      conceptualConfusions: {},
      abstractionScore: 0,
      metaphoricalScore: 0,
      difficultyLevel: "beginner"
    };
  }
  // ========================================================================
  // COLETA DE DADOS DE ASSOCIAÇÃO CONCEITUAL
  // ========================================================================
  collectConceptualAssociation(associationData) {
    const {
      selectedSymbol,
      targetConcept,
      actualConcept,
      responseTime,
      timestamp,
      metaphoricalJustification = null,
      conceptualPath = [],
      abstractionLevel = "medium"
    } = associationData;
    const isCorrect = targetConcept === actualConcept;
    const association = {
      id: this.generateInteractionId(),
      timestamp,
      responseTime,
      selectedSymbol: {
        id: selectedSymbol.id,
        symbol: selectedSymbol.symbol,
        actualConcept,
        abstractLevel: this.conceptualDomains[actualConcept]?.abstractLevel,
        category: this.conceptualDomains[actualConcept]?.category
      },
      targetConcept,
      isCorrect,
      conceptualAnalysis: {
        abstractionLevel: this.conceptualDomains[targetConcept]?.abstractLevel,
        cognitiveLoad: this.conceptualDomains[targetConcept]?.cognitive_load,
        category: this.conceptualDomains[targetConcept]?.category,
        conceptualProperties: this.conceptualDomains[targetConcept]?.properties
      },
      context: {
        metaphoricalJustification,
        conceptualPath,
        requestedAbstractionLevel: abstractionLevel,
        difficultyLevel: this.sessionData.difficultyLevel
      }
    };
    this.interactions.push(association);
    this.analyzeAbstractThinking(association);
    this.analyzeMetaphoricalReasoning(association);
    this.analyzeConceptualFlexibility(association);
    this.analyzeSymbolicThinking(association);
    this.updateSessionMetrics(association);
    return association;
  }
  // ========================================================================
  // ANÁLISES COGNITIVAS ESPECIALIZADAS
  // ========================================================================
  analyzeAbstractThinking(association) {
    const { responseTime, isCorrect, targetConcept, selectedSymbol } = association;
    const abstractMetric = {
      timestamp: association.timestamp,
      concept: targetConcept,
      symbol: selectedSymbol.symbol,
      recognized: isCorrect,
      responseTime,
      abstractionLevel: association.conceptualAnalysis.abstractionLevel,
      cognitiveLoad: association.conceptualAnalysis.cognitiveLoad,
      abstractionAccuracy: this.calculateAbstractionAccuracy(association),
      conceptualDistance: this.calculateConceptualDistance(targetConcept, selectedSymbol.actualConcept)
    };
    this.cognitiveMetrics.abstractThinking.push(abstractMetric);
    const integrationMetric = {
      timestamp: association.timestamp,
      concept: targetConcept,
      integrationComplexity: this.assessIntegrationComplexity(association),
      conceptualBridging: this.assessConceptualBridging(association),
      meaningCoherence: this.assessMeaningCoherence(association)
    };
    this.cognitiveMetrics.conceptualIntegration.push(integrationMetric);
  }
  analyzeMetaphoricalReasoning(association) {
    const { targetConcept, context } = association;
    const metaphorMetric = {
      timestamp: association.timestamp,
      concept: targetConcept,
      metaphorProvided: !!context.metaphoricalJustification,
      metaphorQuality: this.assessMetaphorQuality(association),
      analogicalThinking: this.assessAnalogicalThinking(association),
      metaphoricalMapping: this.assessMetaphoricalMapping(association),
      creativityScore: this.assessConceptualCreativity(association)
    };
    this.cognitiveMetrics.metaphoricalReasoning.push(metaphorMetric);
  }
  analyzeConceptualFlexibility(association) {
    const { targetConcept, selectedSymbol, isCorrect } = association;
    const flexibilityMetric = {
      timestamp: association.timestamp,
      concept: targetConcept,
      categoryFlexibility: this.assessCategoryFlexibility(association),
      perspectiveShifting: this.assessPerspectiveShifting(association),
      conceptualAdaptation: this.assessConceptualAdaptation(association),
      contextualSensitivity: this.assessContextualSensitivity(association)
    };
    this.cognitiveMetrics.conceptualFlexibility.push(flexibilityMetric);
  }
  analyzeSymbolicThinking(association) {
    const { selectedSymbol, targetConcept, isCorrect } = association;
    const symbolicMetric = {
      timestamp: association.timestamp,
      symbol: selectedSymbol.symbol,
      concept: targetConcept,
      symbolicMapping: isCorrect,
      symbolComplexity: this.calculateSymbolComplexity(selectedSymbol.symbol),
      representationalThinking: this.assessRepresentationalThinking(association),
      semanticDepth: this.assessSemanticDepth(association)
    };
    this.cognitiveMetrics.symbolicThinking.push(symbolicMetric);
  }
  // ========================================================================
  // CÁLCULOS ESPECIALIZADOS
  // ========================================================================
  calculateAbstractionAccuracy(association) {
    const { isCorrect, targetConcept, selectedSymbol } = association;
    if (!isCorrect) return 0;
    const targetLevel = this.conceptualDomains[targetConcept]?.abstractLevel;
    const selectedLevel = this.conceptualDomains[selectedSymbol.actualConcept]?.abstractLevel;
    const levelMap = { "low": 1, "medium": 2, "high": 3, "very_high": 4 };
    const targetLevelNum = levelMap[targetLevel] || 2;
    const selectedLevelNum = levelMap[selectedLevel] || 2;
    return Math.max(0, 1 - Math.abs(targetLevelNum - selectedLevelNum) * 0.25);
  }
  calculateConceptualDistance(targetConcept, actualConcept) {
    if (targetConcept === actualConcept) return 0;
    const targetDomain = this.conceptualDomains[targetConcept];
    const actualDomain = this.conceptualDomains[actualConcept];
    if (!targetDomain || !actualDomain) return 1;
    const categoryDistance = targetDomain.category === actualDomain.category ? 0 : 0.4;
    const levelMap = { "low": 1, "medium": 2, "high": 3, "very_high": 4 };
    const targetLevel = levelMap[targetDomain.abstractLevel] || 2;
    const actualLevel = levelMap[actualDomain.abstractLevel] || 2;
    const levelDistance = Math.abs(targetLevel - actualLevel) * 0.15;
    const propertyOverlap = targetDomain.properties.filter(
      (prop) => actualDomain.properties.includes(prop)
    ).length;
    const propertyDistance = (1 - propertyOverlap / Math.max(targetDomain.properties.length, actualDomain.properties.length)) * 0.3;
    const metaphorOverlap = targetDomain.metaphors.filter(
      (metaphor) => actualDomain.metaphors.includes(metaphor)
    ).length;
    const metaphorDistance = (1 - metaphorOverlap / Math.max(targetDomain.metaphors.length, actualDomain.metaphors.length)) * 0.15;
    return Math.min(1, categoryDistance + levelDistance + propertyDistance + metaphorDistance);
  }
  assessIntegrationComplexity(association) {
    const { targetConcept, context } = association;
    const domain = this.conceptualDomains[targetConcept];
    if (!domain) return 0.5;
    let complexity = 0;
    const levelComplexity = {
      "low": 0.2,
      "medium": 0.5,
      "high": 0.8,
      "very_high": 1
    };
    complexity += levelComplexity[domain.abstractLevel] || 0.5;
    const loadComplexity = {
      "low": 0.2,
      "medium": 0.5,
      "high": 0.8,
      "very_high": 1
    };
    complexity += loadComplexity[domain.cognitive_load] || 0.5;
    complexity += domain.properties.length / 6 * 0.3;
    return Math.min(1, complexity / 2.3);
  }
  assessConceptualBridging(association) {
    const { context, targetConcept } = association;
    if (!context.conceptualPath || context.conceptualPath.length === 0) {
      return 0.3;
    }
    const pathLength = context.conceptualPath.length;
    const optimalLength = 3;
    const lengthScore = Math.max(0, 1 - Math.abs(pathLength - optimalLength) * 0.2);
    const coherenceScore = this.assessPathCoherence(context.conceptualPath, targetConcept);
    return (lengthScore + coherenceScore) / 2;
  }
  assessMeaningCoherence(association) {
    const { isCorrect, context, targetConcept } = association;
    let coherence = isCorrect ? 0.6 : 0.2;
    if (context.metaphoricalJustification) {
      const metaphorRelevance = this.assessMetaphorRelevance(
        context.metaphoricalJustification,
        targetConcept
      );
      coherence += metaphorRelevance * 0.3;
    }
    if (context.conceptualPath && context.conceptualPath.length > 0) {
      coherence += 0.1;
    }
    return Math.min(1, coherence);
  }
  assessMetaphorQuality(association) {
    const { context, targetConcept } = association;
    if (!context.metaphoricalJustification) return 0;
    const justification = context.metaphoricalJustification.toLowerCase();
    const domain = this.conceptualDomains[targetConcept];
    if (!domain) return 0.3;
    let quality = 0;
    const domainMetaphors = domain.metaphors;
    const usesKnownMetaphor = domainMetaphors.some(
      (metaphor) => justification.includes(metaphor)
    );
    if (usesKnownMetaphor) {
      quality += 0.4;
    }
    const hasNovelMetaphor = !usesKnownMetaphor && justification.length > 10;
    if (hasNovelMetaphor) {
      quality += 0.3;
    }
    const conceptualWords = domain.concepts;
    const usesConceptualVocabulary = conceptualWords.some(
      (concept) => justification.includes(concept)
    );
    if (usesConceptualVocabulary) {
      quality += 0.2;
    }
    const properties = domain.properties;
    const usesProperties = properties.some(
      (property) => justification.includes(property)
    );
    if (usesProperties) {
      quality += 0.1;
    }
    return Math.min(1, quality);
  }
  assessAnalogicalThinking(association) {
    const { context, isCorrect } = association;
    if (!context.metaphoricalJustification) return 0.3;
    const justification = context.metaphoricalJustification.toLowerCase();
    const analogicalIndicators = [
      "como",
      "semelhante",
      "parecido",
      "igual",
      "assim como",
      "lembra",
      "representa",
      "simboliza",
      "reflete",
      "espelha"
    ];
    const hasAnalogicalLanguage = analogicalIndicators.some(
      (indicator) => justification.includes(indicator)
    );
    let analogicalScore = 0;
    if (hasAnalogicalLanguage) {
      analogicalScore += 0.4;
    }
    if (isCorrect) {
      analogicalScore += 0.3;
    }
    if (justification.length > 20) {
      analogicalScore += 0.2;
    }
    const hasComparativeStructure = /\b(mais|menos|maior|menor|melhor|pior)\b/.test(justification);
    if (hasComparativeStructure) {
      analogicalScore += 0.1;
    }
    return Math.min(1, analogicalScore);
  }
  assessMetaphoricalMapping(association) {
    const { targetConcept, selectedSymbol, context, isCorrect } = association;
    if (!isCorrect) return 0;
    const domain = this.conceptualDomains[targetConcept];
    if (!domain) return 0.5;
    let mappingScore = 0.5;
    const symbolIsKnownMetaphor = domain.symbols.includes(selectedSymbol.symbol);
    if (symbolIsKnownMetaphor) {
      mappingScore += 0.3;
    }
    if (context.metaphoricalJustification) {
      const metaphorQuality = this.assessMetaphorQuality(association);
      mappingScore += metaphorQuality * 0.2;
    }
    return Math.min(1, mappingScore);
  }
  assessConceptualCreativity(association) {
    const { context, targetConcept, selectedSymbol } = association;
    let creativity = 0;
    if (context.metaphoricalJustification) {
      const domain = this.conceptualDomains[targetConcept];
      const usesNovelMetaphor = domain && !domain.metaphors.some(
        (metaphor) => context.metaphoricalJustification.toLowerCase().includes(metaphor)
      );
      if (usesNovelMetaphor) {
        creativity += 0.4;
      }
      const isOriginal = context.metaphoricalJustification.length > 30;
      if (isOriginal) {
        creativity += 0.3;
      }
    }
    if (context.conceptualPath && context.conceptualPath.length > 3) {
      creativity += 0.2;
    }
    const isUnconventionalSymbol = this.assessSymbolUnconventionality(selectedSymbol.symbol, targetConcept);
    if (isUnconventionalSymbol) {
      creativity += 0.1;
    }
    return Math.min(1, creativity);
  }
  assessCategoryFlexibility(association) {
    const recentAssociations = this.interactions.slice(-5);
    const categories = recentAssociations.map(
      (a) => this.conceptualDomains[a.targetConcept]?.category
    ).filter(Boolean);
    const uniqueCategories = new Set(categories);
    return Math.min(1, uniqueCategories.size / Math.min(categories.length, 4));
  }
  assessPerspectiveShifting(association) {
    const { targetConcept, context } = association;
    let perspectiveScore = 0.5;
    if (context.metaphoricalJustification) {
      const justification = context.metaphoricalJustification.toLowerCase();
      const perspectiveIndicators = [
        "por outro lado",
        "também pode",
        "outra forma",
        "perspectiva",
        "ponto de vista",
        "ângulo",
        "aspecto",
        "lado"
      ];
      const hasMultiplePerspectives = perspectiveIndicators.some(
        (indicator) => justification.includes(indicator)
      );
      if (hasMultiplePerspectives) {
        perspectiveScore += 0.3;
      }
    }
    const domain = this.conceptualDomains[targetConcept];
    if (domain && domain.properties.includes("subjective")) {
      perspectiveScore += 0.2;
    }
    return Math.min(1, perspectiveScore);
  }
  assessConceptualAdaptation(association) {
    const { targetConcept, context } = association;
    let adaptationScore = 0.4;
    const requestedLevel = context.requestedAbstractionLevel;
    const conceptLevel = this.conceptualDomains[targetConcept]?.abstractLevel;
    const levelMatch = requestedLevel === conceptLevel;
    if (levelMatch) {
      adaptationScore += 0.3;
    }
    const domain = this.conceptualDomains[targetConcept];
    if (domain && domain.cognitive_load === "high" && context.metaphoricalJustification) {
      adaptationScore += 0.2;
    }
    if (context.difficultyLevel === "advanced" && context.conceptualPath) {
      adaptationScore += 0.1;
    }
    return Math.min(1, adaptationScore);
  }
  assessContextualSensitivity(association) {
    const { targetConcept, context, responseTime } = association;
    let sensitivity = 0.5;
    const domain = this.conceptualDomains[targetConcept];
    if (domain) {
      const expectedTime = this.getExpectedResponseTime(domain.cognitive_load);
      const timeAppropriate = Math.abs(responseTime - expectedTime) < 3e3;
      if (timeAppropriate) {
        sensitivity += 0.2;
      }
    }
    const providedAbstractionEvidence = context.metaphoricalJustification || context.conceptualPath;
    if (context.requestedAbstractionLevel !== "low" && providedAbstractionEvidence) {
      sensitivity += 0.2;
    }
    if (domain && domain.cognitive_load === "very_high" && context.conceptualPath) {
      sensitivity += 0.1;
    }
    return Math.min(1, sensitivity);
  }
  calculateSymbolComplexity(symbol) {
    const complexityMap = {
      "⏰": 0.4,
      "📅": 0.3,
      "⌛": 0.6,
      "🕐": 0.2,
      "⏳": 0.5,
      "🕊️": 0.7,
      "🗽": 0.8,
      "🆓": 0.3,
      "🔓": 0.4,
      "🌅": 0.6,
      "🌱": 0.3,
      "📈": 0.5,
      "🌳": 0.4,
      "🏗️": 0.6,
      "💪": 0.3,
      "🔗": 0.4,
      "🌐": 0.7,
      "🤝": 0.5,
      "💞": 0.6,
      "🧩": 0.5,
      "⚖️": 0.6,
      "☯️": 0.8,
      "⚡": 0.5,
      "🎭": 0.7,
      "🌊": 0.4,
      "🦋": 0.8,
      "🔄": 0.5,
      "🌙": 0.6,
      "🔮": 0.9
    };
    return complexityMap[symbol] || 0.5;
  }
  assessRepresentationalThinking(association) {
    const { selectedSymbol, targetConcept, isCorrect } = association;
    let representationalScore = isCorrect ? 0.6 : 0.2;
    const symbolComplexity = this.calculateSymbolComplexity(selectedSymbol.symbol);
    representationalScore += symbolComplexity * 0.2;
    const domain = this.conceptualDomains[targetConcept];
    if (domain && domain.abstractLevel === "very_high" && isCorrect) {
      representationalScore += 0.2;
    }
    return Math.min(1, representationalScore);
  }
  assessSemanticDepth(association) {
    const { context, targetConcept } = association;
    let depth = 0.3;
    if (context.metaphoricalJustification) {
      const justificationLength = context.metaphoricalJustification.length;
      depth += Math.min(0.3, justificationLength / 100);
      const domain = this.conceptualDomains[targetConcept];
      if (domain) {
        const usesConceptualVocabulary = domain.concepts.some(
          (concept) => context.metaphoricalJustification.toLowerCase().includes(concept)
        );
        if (usesConceptualVocabulary) {
          depth += 0.2;
        }
      }
    }
    if (context.conceptualPath && context.conceptualPath.length > 2) {
      depth += 0.2;
    }
    return Math.min(1, depth);
  }
  assessPathCoherence(conceptualPath, targetConcept) {
    if (!conceptualPath || conceptualPath.length === 0) return 0;
    let coherence = 0.3;
    const lastStep = conceptualPath[conceptualPath.length - 1];
    const domain = this.conceptualDomains[targetConcept];
    if (domain) {
      const isRelated = domain.concepts.some(
        (concept) => lastStep.toLowerCase().includes(concept) || concept.includes(lastStep.toLowerCase())
      );
      if (isRelated) {
        coherence += 0.4;
      }
      const hasLogicalProgression = this.assessLogicalProgression(conceptualPath);
      coherence += hasLogicalProgression * 0.3;
    }
    return Math.min(1, coherence);
  }
  assessLogicalProgression(conceptualPath) {
    if (conceptualPath.length < 2) return 0.5;
    let progressionScore = 0;
    for (let i = 0; i < conceptualPath.length - 1; i++) {
      const currentStep = conceptualPath[i];
      const nextStep = conceptualPath[i + 1];
      const hasConnection = this.assessSemanticConnection(currentStep, nextStep);
      if (hasConnection) {
        progressionScore += 1;
      }
    }
    return progressionScore / (conceptualPath.length - 1);
  }
  assessSemanticConnection(step1, step2) {
    const commonWords = step1.toLowerCase().split(" ").filter(
      (word) => step2.toLowerCase().includes(word) && word.length > 3
    );
    return commonWords.length > 0 || step1.length + step2.length > 10;
  }
  assessMetaphorRelevance(metaphor, targetConcept) {
    const domain = this.conceptualDomains[targetConcept];
    if (!domain) return 0.3;
    const metaphorLower = metaphor.toLowerCase();
    let relevance = 0;
    const usesKnownMetaphor = domain.metaphors.some(
      (domainMetaphor) => metaphorLower.includes(domainMetaphor)
    );
    if (usesKnownMetaphor) {
      relevance += 0.5;
    }
    const usesRelatedConcepts = domain.concepts.some(
      (concept) => metaphorLower.includes(concept)
    );
    if (usesRelatedConcepts) {
      relevance += 0.3;
    }
    const usesProperties = domain.properties.some(
      (property) => metaphorLower.includes(property)
    );
    if (usesProperties) {
      relevance += 0.2;
    }
    return Math.min(1, relevance);
  }
  assessSymbolUnconventionality(symbol, targetConcept) {
    const domain = this.conceptualDomains[targetConcept];
    if (!domain) return false;
    return !domain.symbols.includes(symbol);
  }
  getExpectedResponseTime(cognitiveLoad) {
    const timeMap = {
      "low": 4e3,
      "medium": 6e3,
      "high": 8e3,
      "very_high": 1e4
    };
    return timeMap[cognitiveLoad] || 6e3;
  }
  // ========================================================================
  // ATUALIZAÇÃO DE MÉTRICAS DE SESSÃO
  // ========================================================================
  updateSessionMetrics(association) {
    const { isCorrect, responseTime, targetConcept } = association;
    this.sessionData.totalConceptualAssociations++;
    if (isCorrect) {
      this.sessionData.correctConceptualAssociations++;
    } else {
      this.sessionData.incorrectConceptualAssociations++;
      const actualConcept = association.selectedSymbol.actualConcept;
      const confusionKey = `${targetConcept}->${actualConcept}`;
      if (!this.sessionData.conceptualConfusions[confusionKey]) {
        this.sessionData.conceptualConfusions[confusionKey] = 0;
      }
      this.sessionData.conceptualConfusions[confusionKey]++;
    }
    if (!this.sessionData.conceptsByAccuracy[targetConcept]) {
      this.sessionData.conceptsByAccuracy[targetConcept] = { total: 0, correct: 0 };
    }
    this.sessionData.conceptsByAccuracy[targetConcept].total++;
    if (isCorrect) {
      this.sessionData.conceptsByAccuracy[targetConcept].correct++;
    }
    const totalTime = this.interactions.reduce((sum, i) => sum + i.responseTime, 0);
    this.sessionData.averageConceptualProcessingTime = totalTime / this.interactions.length;
    this.updateAbstractionScore();
    this.updateMetaphoricalScore();
  }
  updateAbstractionScore() {
    const abstractMetrics = this.cognitiveMetrics.abstractThinking;
    if (abstractMetrics.length === 0) {
      this.sessionData.abstractionScore = 0;
      return;
    }
    const totalAccuracy = abstractMetrics.reduce(
      (sum, metric) => sum + metric.abstractionAccuracy,
      0
    );
    this.sessionData.abstractionScore = totalAccuracy / abstractMetrics.length;
  }
  updateMetaphoricalScore() {
    const metaphorMetrics = this.cognitiveMetrics.metaphoricalReasoning;
    if (metaphorMetrics.length === 0) {
      this.sessionData.metaphoricalScore = 0;
      return;
    }
    const totalQuality = metaphorMetrics.reduce(
      (sum, metric) => sum + metric.metaphorQuality,
      0
    );
    const totalAnalogical = metaphorMetrics.reduce(
      (sum, metric) => sum + metric.analogicalThinking,
      0
    );
    this.sessionData.metaphoricalScore = totalQuality / metaphorMetrics.length * 0.6 + totalAnalogical / metaphorMetrics.length * 0.4;
  }
  // ========================================================================
  // RELATÓRIOS E ANÁLISES FINAIS
  // ========================================================================
  generateCognitiveReport() {
    return {
      abstractThinking: this.analyzeAbstractThinkingReport(),
      metaphoricalReasoning: this.analyzeMetaphoricalReasoningReport(),
      conceptualFlexibility: this.analyzeConceptualFlexibilityReport(),
      symbolicThinking: this.analyzeSymbolicThinkingReport(),
      adaptiveRecommendations: this.generateAdaptiveRecommendations()
    };
  }
  analyzeAbstractThinkingReport() {
    return {
      overallAccuracy: this.sessionData.correctConceptualAssociations / this.sessionData.totalConceptualAssociations,
      abstractionScore: this.sessionData.abstractionScore,
      averageProcessingTime: this.sessionData.averageConceptualProcessingTime,
      abstractionLevelPerformance: this.assessAbstractionLevelPerformance(),
      conceptualDistance: this.calculateAverageMetric(this.cognitiveMetrics.abstractThinking, "conceptualDistance")
    };
  }
  analyzeMetaphoricalReasoningReport() {
    return {
      metaphoricalScore: this.sessionData.metaphoricalScore,
      metaphorQuality: this.calculateAverageMetric(this.cognitiveMetrics.metaphoricalReasoning, "metaphorQuality"),
      analogicalThinking: this.calculateAverageMetric(this.cognitiveMetrics.metaphoricalReasoning, "analogicalThinking"),
      creativityScore: this.calculateAverageMetric(this.cognitiveMetrics.metaphoricalReasoning, "creativityScore"),
      metaphorUsageRate: this.calculateMetaphorUsageRate()
    };
  }
  analyzeConceptualFlexibilityReport() {
    return {
      categoryFlexibility: this.calculateAverageMetric(this.cognitiveMetrics.conceptualFlexibility, "categoryFlexibility"),
      perspectiveShifting: this.calculateAverageMetric(this.cognitiveMetrics.conceptualFlexibility, "perspectiveShifting"),
      conceptualAdaptation: this.calculateAverageMetric(this.cognitiveMetrics.conceptualFlexibility, "conceptualAdaptation"),
      contextualSensitivity: this.calculateAverageMetric(this.cognitiveMetrics.conceptualFlexibility, "contextualSensitivity")
    };
  }
  analyzeSymbolicThinkingReport() {
    return {
      symbolicMapping: this.calculateAverageMetric(this.cognitiveMetrics.symbolicThinking, "symbolicMapping"),
      representationalThinking: this.calculateAverageMetric(this.cognitiveMetrics.symbolicThinking, "representationalThinking"),
      semanticDepth: this.calculateAverageMetric(this.cognitiveMetrics.symbolicThinking, "semanticDepth"),
      averageSymbolComplexity: this.calculateAverageMetric(this.cognitiveMetrics.symbolicThinking, "symbolComplexity")
    };
  }
  assessAbstractionLevelPerformance() {
    const levelPerformance = {};
    this.interactions.forEach((interaction) => {
      const concept = interaction.targetConcept;
      const level = this.conceptualDomains[concept]?.abstractLevel;
      if (!levelPerformance[level]) {
        levelPerformance[level] = { total: 0, correct: 0 };
      }
      levelPerformance[level].total++;
      if (interaction.isCorrect) {
        levelPerformance[level].correct++;
      }
    });
    Object.keys(levelPerformance).forEach((level) => {
      const data = levelPerformance[level];
      levelPerformance[level].accuracy = data.correct / data.total;
    });
    return levelPerformance;
  }
  calculateMetaphorUsageRate() {
    const metaphorUsage = this.cognitiveMetrics.metaphoricalReasoning.filter(
      (metric) => metric.metaphorProvided
    ).length;
    return this.interactions.length > 0 ? metaphorUsage / this.interactions.length : 0;
  }
  generateAdaptiveRecommendations() {
    const recommendations = [];
    const conceptPerformance = {};
    Object.entries(this.sessionData.conceptsByAccuracy).forEach(([concept, data]) => {
      conceptPerformance[concept] = data.correct / data.total;
    });
    const weakConcepts = Object.entries(conceptPerformance).filter(([concept, accuracy]) => accuracy < 0.6).map(([concept]) => concept);
    if (weakConcepts.length > 0) {
      recommendations.push({
        type: "conceptual_training",
        recommendation: `Reforçar conceitos: ${weakConcepts.join(", ")}`,
        confidence: 0.8,
        details: {
          concepts: weakConcepts,
          suggestedActivities: ["metaphor_exploration", "concept_mapping", "analogical_reasoning"]
        }
      });
    }
    if (this.sessionData.metaphoricalScore < 0.5) {
      recommendations.push({
        type: "metaphorical_training",
        recommendation: "Desenvolvimento de raciocínio metafórico",
        confidence: 0.7,
        details: {
          currentScore: this.sessionData.metaphoricalScore,
          suggestedActivities: ["metaphor_creation", "analogical_mapping", "creative_expression"]
        }
      });
    }
    if (this.sessionData.abstractionScore < 0.6) {
      recommendations.push({
        type: "abstraction_training",
        recommendation: "Exercícios de pensamento abstrato",
        confidence: 0.7,
        details: {
          currentScore: this.sessionData.abstractionScore,
          suggestedActivities: ["abstraction_exercises", "conceptual_hierarchies", "symbolic_reasoning"]
        }
      });
    }
    return recommendations;
  }
  getActivityScore() {
    if (this.sessionData.totalConceptualAssociations === 0) return 0;
    const accuracy = this.sessionData.correctConceptualAssociations / this.sessionData.totalConceptualAssociations;
    const speedFactor = Math.max(0, 1 - (this.sessionData.averageConceptualProcessingTime - 6e3) / 12e3);
    const abstractionFactor = this.sessionData.abstractionScore;
    const metaphoricalFactor = this.sessionData.metaphoricalScore;
    return Math.round(accuracy * speedFactor * abstractionFactor * metaphoricalFactor * 1e3);
  }
}
class SequentialAssociationCollector extends BaseCollector {
  constructor() {
    super("SequentialAssociation");
    this.cognitiveMetrics = {
      // Métricas específicas de processamento sequencial
      sequentialMemory: [],
      orderRecognition: [],
      temporalProcessing: [],
      sequentialLogic: [],
      patternCompletion: [],
      // Padrões sequenciais
      sequenceTypes: [],
      orderingStrategies: [],
      sequentialErrors: [],
      chronologicalThinking: [],
      // Análise de memória de trabalho
      workingMemoryLoad: [],
      sequentialSpan: [],
      orderMaintenance: [],
      sequentialUpdating: []
    };
    this.sequenceTypes = {
      daily_routine: {
        category: "temporal",
        cognitive_load: "low",
        sequence: ["🌅", "🍳", "🚿", "👔", "🚗", "💼", "🍽️", "📺", "🛏️"],
        logic_type: "habitual",
        dependencies: "temporal",
        complexity: "low",
        real_world: true
      },
      cooking_process: {
        category: "procedural",
        cognitive_load: "medium",
        sequence: ["🛒", "🧄", "🔪", "🍳", "🧂", "🍽️", "🧽"],
        logic_type: "causal",
        dependencies: "prerequisite",
        complexity: "medium",
        real_world: true
      },
      plant_growth: {
        category: "biological",
        cognitive_load: "medium",
        sequence: ["🌰", "🌱", "🌿", "🌳", "🌸", "🍎", "🍂"],
        logic_type: "natural",
        dependencies: "biological",
        complexity: "medium",
        real_world: true
      },
      communication_flow: {
        category: "social",
        cognitive_load: "medium",
        sequence: ["💭", "🗣️", "👂", "🤔", "💬", "😊", "🤝"],
        logic_type: "interactive",
        dependencies: "social",
        complexity: "medium",
        real_world: true
      },
      problem_solving: {
        category: "cognitive",
        cognitive_load: "high",
        sequence: ["❓", "🤔", "💡", "📝", "🔬", "✅", "🎯"],
        logic_type: "analytical",
        dependencies: "logical",
        complexity: "high",
        real_world: true
      },
      geometric_progression: {
        category: "mathematical",
        cognitive_load: "high",
        sequence: ["🔵", "🔵🔵", "🔵🔵🔵🔵", "🟦🟦🟦🟦🟦🟦🟦🟦"],
        logic_type: "mathematical",
        dependencies: "rule_based",
        complexity: "high",
        real_world: false
      }
    };
    this.sessionData = {
      startTime: null,
      endTime: null,
      totalSequences: 0,
      correctSequences: 0,
      incorrectSequences: 0,
      averageSequentialProcessingTime: 0,
      sequencesByAccuracy: {},
      sequentialErrors: {},
      memorySpan: 0,
      orderingAccuracy: 0,
      difficultyLevel: "beginner"
    };
  }
  // ========================================================================
  // COLETA DE DADOS DE ASSOCIAÇÃO SEQUENCIAL
  // ========================================================================
  collectSequentialAssociation(associationData) {
    const {
      userSequence,
      correctSequence,
      sequenceType,
      responseTime,
      timestamp,
      orderingStrategy = null,
      memoryAids = [],
      sequenceLength = 0
    } = associationData;
    const isCorrect = this.compareSequences(userSequence, correctSequence);
    const sequenceAccuracy = this.calculateSequenceAccuracy(userSequence, correctSequence);
    const association = {
      id: this.generateInteractionId(),
      timestamp,
      responseTime,
      userSequence,
      correctSequence,
      sequenceType,
      isCorrect,
      sequenceAccuracy,
      sequenceAnalysis: {
        length: correctSequence.length,
        complexity: this.sequenceTypes[sequenceType]?.complexity,
        logicType: this.sequenceTypes[sequenceType]?.logic_type,
        cognitiveLoad: this.sequenceTypes[sequenceType]?.cognitive_load,
        dependencies: this.sequenceTypes[sequenceType]?.dependencies
      },
      context: {
        orderingStrategy,
        memoryAids,
        actualSequenceLength: sequenceLength,
        difficultyLevel: this.sessionData.difficultyLevel
      }
    };
    this.interactions.push(association);
    this.analyzeSequentialMemory(association);
    this.analyzeOrderRecognition(association);
    this.analyzeTemporalProcessing(association);
    this.analyzeWorkingMemoryLoad(association);
    this.updateSessionMetrics(association);
    return association;
  }
  // ========================================================================
  // ANÁLISES COGNITIVAS ESPECIALIZADAS
  // ========================================================================
  analyzeSequentialMemory(association) {
    const { responseTime, sequenceAccuracy, correctSequence, userSequence } = association;
    const memoryMetric = {
      timestamp: association.timestamp,
      sequenceType: association.sequenceType,
      sequenceLength: correctSequence.length,
      accuracy: sequenceAccuracy,
      responseTime,
      memoryLoad: this.calculateMemoryLoad(association),
      memoryStrategy: this.inferMemoryStrategy(association),
      memorySpan: this.calculateEffectiveSpan(userSequence, correctSequence)
    };
    this.cognitiveMetrics.sequentialMemory.push(memoryMetric);
    const maintenanceMetric = {
      timestamp: association.timestamp,
      sequenceType: association.sequenceType,
      orderMaintained: this.assessOrderMaintenance(association),
      positionAccuracy: this.calculatePositionAccuracy(association),
      orderingStability: this.assessOrderingStability(association)
    };
    this.cognitiveMetrics.orderMaintenance.push(maintenanceMetric);
  }
  analyzeOrderRecognition(association) {
    const { sequenceType, isCorrect, userSequence, correctSequence } = association;
    const orderMetric = {
      timestamp: association.timestamp,
      sequenceType,
      recognized: isCorrect,
      orderLogic: this.sequenceTypes[sequenceType]?.logic_type,
      logicRecognition: this.assessLogicRecognition(association),
      orderingPatterns: this.identifyOrderingPatterns(association),
      sequentialReasoning: this.assessSequentialReasoning(association)
    };
    this.cognitiveMetrics.orderRecognition.push(orderMetric);
  }
  analyzeTemporalProcessing(association) {
    const { sequenceType, responseTime } = association;
    const temporalMetric = {
      timestamp: association.timestamp,
      sequenceType,
      temporalLogic: this.assessTemporalLogic(association),
      chronologicalAccuracy: this.assessChronologicalAccuracy(association),
      temporalSpan: this.calculateTemporalSpan(association),
      timeBasedReasoning: this.assessTimeBasedReasoning(association)
    };
    this.cognitiveMetrics.temporalProcessing.push(temporalMetric);
  }
  analyzeWorkingMemoryLoad(association) {
    const { correctSequence, responseTime, sequenceType } = association;
    const workingMemoryMetric = {
      timestamp: association.timestamp,
      sequenceLength: correctSequence.length,
      cognitiveLoad: this.sequenceTypes[sequenceType]?.cognitive_load,
      actualLoad: this.calculateActualCognitiveLoad(association),
      loadManagement: this.assessLoadManagement(association),
      capacityUtilization: this.assessCapacityUtilization(association)
    };
    this.cognitiveMetrics.workingMemoryLoad.push(workingMemoryMetric);
  }
  // ========================================================================
  // CÁLCULOS ESPECIALIZADOS
  // ========================================================================
  compareSequences(userSequence, correctSequence) {
    if (userSequence.length !== correctSequence.length) return false;
    return userSequence.every((item, index) => item === correctSequence[index]);
  }
  calculateSequenceAccuracy(userSequence, correctSequence) {
    if (correctSequence.length === 0) return 0;
    let correctPositions = 0;
    const maxLength = Math.max(userSequence.length, correctSequence.length);
    for (let i = 0; i < maxLength; i++) {
      if (userSequence[i] === correctSequence[i]) {
        correctPositions++;
      }
    }
    return correctPositions / correctSequence.length;
  }
  calculateMemoryLoad(association) {
    const { correctSequence, sequenceType } = association;
    const sequenceInfo = this.sequenceTypes[sequenceType];
    let load = correctSequence.length * 0.1;
    const logicMultiplier = {
      "habitual": 0.8,
      "causal": 1,
      "natural": 1,
      "interactive": 1.2,
      "analytical": 1.4,
      "mathematical": 1.6
    };
    load *= logicMultiplier[sequenceInfo?.logic_type] || 1;
    const loadMultiplier = {
      "low": 0.8,
      "medium": 1,
      "high": 1.3
    };
    load *= loadMultiplier[sequenceInfo?.cognitive_load] || 1;
    return Math.min(2, load);
  }
  inferMemoryStrategy(association) {
    const { context, responseTime, sequenceAccuracy } = association;
    if (context.memoryAids && context.memoryAids.length > 0) {
      return "aided_memory";
    }
    if (context.orderingStrategy) {
      return context.orderingStrategy;
    }
    if (sequenceAccuracy > 0.8 && responseTime < 8e3) {
      return "automatic_recall";
    }
    if (sequenceAccuracy > 0.6 && responseTime > 12e3) {
      return "deliberate_reconstruction";
    }
    if (sequenceAccuracy < 0.5) {
      return "guessing";
    }
    return "mixed_strategy";
  }
  calculateEffectiveSpan(userSequence, correctSequence) {
    let maxCorrectSpan = 0;
    let currentSpan = 0;
    const maxLength = Math.min(userSequence.length, correctSequence.length);
    for (let i = 0; i < maxLength; i++) {
      if (userSequence[i] === correctSequence[i]) {
        currentSpan++;
        maxCorrectSpan = Math.max(maxCorrectSpan, currentSpan);
      } else {
        currentSpan = 0;
      }
    }
    return maxCorrectSpan;
  }
  assessOrderMaintenance(association) {
    const { userSequence, correctSequence } = association;
    let maintainedOrders = 0;
    const totalPairs = correctSequence.length - 1;
    for (let i = 0; i < totalPairs; i++) {
      const item1 = correctSequence[i];
      const item2 = correctSequence[i + 1];
      const userPos1 = userSequence.indexOf(item1);
      const userPos2 = userSequence.indexOf(item2);
      if (userPos1 !== -1 && userPos2 !== -1 && userPos1 < userPos2) {
        maintainedOrders++;
      }
    }
    return totalPairs > 0 ? maintainedOrders / totalPairs : 0;
  }
  calculatePositionAccuracy(association) {
    const { userSequence, correctSequence } = association;
    let correctPositions = 0;
    correctSequence.forEach((item, correctIndex) => {
      const userIndex = userSequence.indexOf(item);
      if (userIndex === correctIndex) {
        correctPositions++;
      }
    });
    return correctSequence.length > 0 ? correctPositions / correctSequence.length : 0;
  }
  assessOrderingStability(association) {
    const { userSequence, correctSequence } = association;
    let inversions = 0;
    const n = Math.min(userSequence.length, correctSequence.length);
    for (let i = 0; i < n - 1; i++) {
      for (let j = i + 1; j < n; j++) {
        const correctOrder = correctSequence.indexOf(userSequence[i]) < correctSequence.indexOf(userSequence[j]);
        const userOrder = i < j;
        if (correctOrder !== userOrder) {
          inversions++;
        }
      }
    }
    const maxInversions = n * (n - 1) / 2;
    return maxInversions > 0 ? 1 - inversions / maxInversions : 1;
  }
  assessLogicRecognition(association) {
    const { sequenceType, isCorrect, userSequence, correctSequence } = association;
    const sequenceInfo = this.sequenceTypes[sequenceType];
    if (!sequenceInfo) return 0.5;
    let logicScore = isCorrect ? 0.8 : 0.2;
    if (sequenceInfo.logic_type === "mathematical" && isCorrect) {
      logicScore += 0.2;
    }
    if (sequenceInfo.logic_type === "causal") {
      const causalRespected = this.assessCausalDependencies(userSequence, correctSequence);
      logicScore += causalRespected * 0.3;
    }
    return Math.min(1, logicScore);
  }
  identifyOrderingPatterns(association) {
    const { userSequence, correctSequence } = association;
    const patterns = {
      sequential: this.assessSequentialPattern(userSequence, correctSequence),
      reverse: this.assessReversePattern(userSequence, correctSequence),
      chunked: this.assessChunkedPattern(userSequence, correctSequence),
      random: this.assessRandomPattern(userSequence, correctSequence)
    };
    return Object.entries(patterns).reduce(
      (max, [pattern, score]) => score > max.score ? { pattern, score } : max,
      { pattern: "unknown", score: 0 }
    );
  }
  assessSequentialReasoning(association) {
    const { sequenceType, userSequence, correctSequence } = association;
    const sequenceInfo = this.sequenceTypes[sequenceType];
    let reasoningScore = 0.3;
    switch (sequenceInfo?.logic_type) {
      case "temporal":
      case "habitual":
        reasoningScore += this.assessTemporalReasoning(userSequence, correctSequence) * 0.4;
        break;
      case "causal":
      case "procedural":
        reasoningScore += this.assessCausalReasoning(userSequence, correctSequence) * 0.4;
        break;
      case "mathematical":
        reasoningScore += this.assessMathematicalReasoning(userSequence, correctSequence) * 0.4;
        break;
      case "logical":
      case "analytical":
        reasoningScore += this.assessLogicalReasoning(userSequence, correctSequence) * 0.4;
        break;
    }
    const relationshipScore = this.assessLogicalRelationships(userSequence, correctSequence);
    reasoningScore += relationshipScore * 0.3;
    return Math.min(1, reasoningScore);
  }
  assessTemporalLogic(association) {
    const { sequenceType, userSequence, correctSequence } = association;
    const sequenceInfo = this.sequenceTypes[sequenceType];
    if (sequenceInfo?.dependencies !== "temporal") return 0.5;
    return this.assessTemporalReasoning(userSequence, correctSequence);
  }
  assessChronologicalAccuracy(association) {
    const { userSequence, correctSequence } = association;
    let chronologicalScore = 0;
    const temporalPairs = this.identifyTemporalPairs(correctSequence);
    temporalPairs.forEach(([before, after]) => {
      const userBeforeIndex = userSequence.indexOf(before);
      const userAfterIndex = userSequence.indexOf(after);
      if (userBeforeIndex !== -1 && userAfterIndex !== -1 && userBeforeIndex < userAfterIndex) {
        chronologicalScore++;
      }
    });
    return temporalPairs.length > 0 ? chronologicalScore / temporalPairs.length : 0.5;
  }
  calculateTemporalSpan(association) {
    const { userSequence, correctSequence } = association;
    const temporalPairs = this.identifyTemporalPairs(correctSequence);
    let maxTemporalSpan = 0;
    let currentSpan = 0;
    temporalPairs.forEach(([before, after]) => {
      const userBeforeIndex = userSequence.indexOf(before);
      const userAfterIndex = userSequence.indexOf(after);
      if (userBeforeIndex !== -1 && userAfterIndex !== -1 && userBeforeIndex < userAfterIndex) {
        currentSpan++;
        maxTemporalSpan = Math.max(maxTemporalSpan, currentSpan);
      } else {
        currentSpan = 0;
      }
    });
    return maxTemporalSpan;
  }
  assessTimeBasedReasoning(association) {
    const { sequenceType, responseTime, userSequence, correctSequence } = association;
    let timeBasedScore = 0.4;
    const sequenceInfo = this.sequenceTypes[sequenceType];
    const expectedTime = this.getExpectedSequenceTime(sequenceInfo);
    const timeAppropriate = Math.abs(responseTime - expectedTime) < expectedTime * 0.5;
    if (timeAppropriate) {
      timeBasedScore += 0.3;
    }
    if (sequenceInfo?.dependencies === "temporal") {
      const temporalAccuracy = this.assessTemporalReasoning(userSequence, correctSequence);
      timeBasedScore += temporalAccuracy * 0.3;
    }
    return Math.min(1, timeBasedScore);
  }
  calculateActualCognitiveLoad(association) {
    const { responseTime, sequenceAccuracy, userSequence, correctSequence } = association;
    let actualLoad = 0.5;
    const normalizedTime = responseTime / 1e4;
    actualLoad += Math.min(0.5, normalizedTime);
    actualLoad += (1 - sequenceAccuracy) * 0.3;
    actualLoad += correctSequence.length / 10 * 0.2;
    return Math.min(2, actualLoad);
  }
  assessLoadManagement(association) {
    const { context, sequenceAccuracy, responseTime } = association;
    let managementScore = 0.4;
    if (context.memoryAids && context.memoryAids.length > 0) {
      managementScore += 0.3;
    }
    if (context.orderingStrategy && context.orderingStrategy !== "guessing") {
      managementScore += 0.2;
    }
    const efficiency = sequenceAccuracy / (responseTime / 1e4);
    managementScore += Math.min(0.1, efficiency * 0.1);
    return Math.min(1, managementScore);
  }
  assessCapacityUtilization(association) {
    const { correctSequence, sequenceAccuracy } = association;
    const memorySpan = this.calculateEffectiveSpan(association.userSequence, correctSequence);
    const utilizationRatio = memorySpan / correctSequence.length;
    return utilizationRatio * sequenceAccuracy;
  }
  // ========================================================================
  // FUNÇÕES AUXILIARES ESPECIALIZADAS
  // ========================================================================
  assessCausalDependencies(userSequence, correctSequence) {
    const causalPairs = this.identifyCausalPairs(correctSequence);
    let respectfulPairs = 0;
    causalPairs.forEach(([cause, effect]) => {
      const userCauseIndex = userSequence.indexOf(cause);
      const userEffectIndex = userSequence.indexOf(effect);
      if (userCauseIndex !== -1 && userEffectIndex !== -1 && userCauseIndex < userEffectIndex) {
        respectfulPairs++;
      }
    });
    return causalPairs.length > 0 ? respectfulPairs / causalPairs.length : 0.5;
  }
  assessSequentialPattern(userSequence, correctSequence) {
    let sequentialCount = 0;
    for (let i = 0; i < Math.min(userSequence.length, correctSequence.length); i++) {
      if (userSequence[i] === correctSequence[i]) {
        sequentialCount++;
      }
    }
    return correctSequence.length > 0 ? sequentialCount / correctSequence.length : 0;
  }
  assessReversePattern(userSequence, correctSequence) {
    const reversedCorrect = [...correctSequence].reverse();
    let reverseMatches = 0;
    for (let i = 0; i < Math.min(userSequence.length, reversedCorrect.length); i++) {
      if (userSequence[i] === reversedCorrect[i]) {
        reverseMatches++;
      }
    }
    return reversedCorrect.length > 0 ? reverseMatches / reversedCorrect.length : 0;
  }
  assessChunkedPattern(userSequence, correctSequence) {
    const chunks = this.identifyChunks(correctSequence);
    let chunkScore = 0;
    chunks.forEach((chunk) => {
      const chunkInUser = this.findChunkInSequence(chunk, userSequence);
      if (chunkInUser) {
        chunkScore += chunk.length / correctSequence.length;
      }
    });
    return chunkScore;
  }
  assessRandomPattern(userSequence, correctSequence) {
    const orderCorrelation = this.calculateOrderCorrelation(userSequence, correctSequence);
    return 1 - orderCorrelation;
  }
  assessTemporalReasoning(userSequence, correctSequence) {
    const temporalPairs = this.identifyTemporalPairs(correctSequence);
    let correctTemporalOrder = 0;
    temporalPairs.forEach(([before, after]) => {
      const userBeforeIndex = userSequence.indexOf(before);
      const userAfterIndex = userSequence.indexOf(after);
      if (userBeforeIndex !== -1 && userAfterIndex !== -1 && userBeforeIndex < userAfterIndex) {
        correctTemporalOrder++;
      }
    });
    return temporalPairs.length > 0 ? correctTemporalOrder / temporalPairs.length : 0.5;
  }
  assessCausalReasoning(userSequence, correctSequence) {
    return this.assessCausalDependencies(userSequence, correctSequence);
  }
  assessMathematicalReasoning(userSequence, correctSequence) {
    if (correctSequence.length < 3) return 0.5;
    const pattern = this.identifyMathematicalPattern(correctSequence);
    if (!pattern) return 0.3;
    return this.verifyMathematicalPattern(userSequence, pattern);
  }
  assessLogicalReasoning(userSequence, correctSequence) {
    const logicalRules = this.identifyLogicalRules(correctSequence);
    let rulesFollowed = 0;
    logicalRules.forEach((rule) => {
      if (this.verifyLogicalRule(userSequence, rule)) {
        rulesFollowed++;
      }
    });
    return logicalRules.length > 0 ? rulesFollowed / logicalRules.length : 0.5;
  }
  assessLogicalRelationships(userSequence, correctSequence) {
    const relationships = this.identifyLogicalRelationships(correctSequence);
    let maintainedRelationships = 0;
    relationships.forEach((relationship) => {
      if (this.verifyRelationship(userSequence, relationship)) {
        maintainedRelationships++;
      }
    });
    return relationships.length > 0 ? maintainedRelationships / relationships.length : 0.5;
  }
  identifyTemporalPairs(sequence) {
    const temporalPairs = [];
    for (let i = 0; i < sequence.length - 1; i++) {
      temporalPairs.push([sequence[i], sequence[i + 1]]);
    }
    return temporalPairs;
  }
  identifyCausalPairs(sequence) {
    const causalPairs = [];
    for (let i = 0; i < sequence.length - 1; i++) {
      causalPairs.push([sequence[i], sequence[i + 1]]);
    }
    return causalPairs;
  }
  identifyChunks(sequence) {
    const chunks = [];
    const chunkSize = Math.min(3, Math.floor(sequence.length / 2));
    for (let i = 0; i < sequence.length; i += chunkSize) {
      chunks.push(sequence.slice(i, i + chunkSize));
    }
    return chunks.filter((chunk) => chunk.length > 1);
  }
  findChunkInSequence(chunk, userSequence) {
    if (chunk.length === 0) return false;
    for (let i = 0; i <= userSequence.length - chunk.length; i++) {
      let matches = true;
      for (let j = 0; j < chunk.length; j++) {
        if (userSequence[i + j] !== chunk[j]) {
          matches = false;
          break;
        }
      }
      if (matches) return true;
    }
    return false;
  }
  calculateOrderCorrelation(userSequence, correctSequence) {
    let correlation = 0;
    const minLength = Math.min(userSequence.length, correctSequence.length);
    for (let i = 0; i < minLength; i++) {
      if (userSequence[i] === correctSequence[i]) {
        correlation++;
      }
    }
    return minLength > 0 ? correlation / minLength : 0;
  }
  identifyMathematicalPattern(sequence) {
    if (sequence.length < 3) return null;
    const counts = sequence.map((item) => this.countVisualElements(item));
    if (counts.length >= 3) {
      const ratio = counts[1] / counts[0];
      let isGeometric = true;
      for (let i = 2; i < counts.length; i++) {
        if (Math.abs(counts[i] / counts[i - 1] - ratio) > 0.1) {
          isGeometric = false;
          break;
        }
      }
      if (isGeometric) {
        return { type: "geometric", ratio, base: counts[0] };
      }
    }
    return null;
  }
  verifyMathematicalPattern(userSequence, pattern) {
    if (pattern.type === "geometric") {
      const userCounts = userSequence.map((item) => this.countVisualElements(item));
      let correctPattern = 0;
      for (let i = 1; i < userCounts.length; i++) {
        const expectedCount = Math.round(userCounts[0] * Math.pow(pattern.ratio, i));
        if (Math.abs(userCounts[i] - expectedCount) < 1) {
          correctPattern++;
        }
      }
      return userCounts.length > 1 ? correctPattern / (userCounts.length - 1) : 0;
    }
    return 0.5;
  }
  countVisualElements(item) {
    if (typeof item === "string") {
      return Array.from(item).length;
    }
    return 1;
  }
  identifyLogicalRules(sequence) {
    return [
      { type: "sequential", description: "maintain order" },
      { type: "grouping", description: "group similar items" }
    ];
  }
  verifyLogicalRule(userSequence, rule) {
    if (rule.type === "sequential") {
      return this.assessSequentialPattern(userSequence, userSequence) > 0.5;
    }
    return 0.5;
  }
  identifyLogicalRelationships(sequence) {
    const relationships = [];
    for (let i = 0; i < sequence.length - 1; i++) {
      relationships.push({
        type: "precedence",
        from: sequence[i],
        to: sequence[i + 1]
      });
    }
    return relationships;
  }
  verifyRelationship(userSequence, relationship) {
    const fromIndex = userSequence.indexOf(relationship.from);
    const toIndex = userSequence.indexOf(relationship.to);
    if (relationship.type === "precedence") {
      return fromIndex !== -1 && toIndex !== -1 && fromIndex < toIndex;
    }
    return false;
  }
  getExpectedSequenceTime(sequenceInfo) {
    const baseTime = 8e3;
    const complexityMultiplier = {
      "low": 0.8,
      "medium": 1,
      "high": 1.5
    };
    const loadMultiplier = {
      "low": 0.8,
      "medium": 1,
      "high": 1.3
    };
    return baseTime * (complexityMultiplier[sequenceInfo?.complexity] || 1) * (loadMultiplier[sequenceInfo?.cognitive_load] || 1);
  }
  // ========================================================================
  // ATUALIZAÇÃO DE MÉTRICAS DE SESSÃO
  // ========================================================================
  updateSessionMetrics(association) {
    const { isCorrect, responseTime, sequenceType, sequenceAccuracy } = association;
    this.sessionData.totalSequences++;
    if (isCorrect) {
      this.sessionData.correctSequences++;
    } else {
      this.sessionData.incorrectSequences++;
      const errorType = this.classifySequentialError(association);
      if (!this.sessionData.sequentialErrors[errorType]) {
        this.sessionData.sequentialErrors[errorType] = 0;
      }
      this.sessionData.sequentialErrors[errorType]++;
    }
    if (!this.sessionData.sequencesByAccuracy[sequenceType]) {
      this.sessionData.sequencesByAccuracy[sequenceType] = { total: 0, correct: 0, accuracy: 0 };
    }
    this.sessionData.sequencesByAccuracy[sequenceType].total++;
    this.sessionData.sequencesByAccuracy[sequenceType].accuracy += sequenceAccuracy;
    if (isCorrect) {
      this.sessionData.sequencesByAccuracy[sequenceType].correct++;
    }
    const totalTime = this.interactions.reduce((sum, i) => sum + i.responseTime, 0);
    this.sessionData.averageSequentialProcessingTime = totalTime / this.interactions.length;
    this.updateMemorySpan();
    this.updateOrderingAccuracy();
  }
  classifySequentialError(association) {
    const { userSequence, correctSequence, sequenceType } = association;
    if (userSequence.length !== correctSequence.length) {
      return "length_error";
    }
    const orderMaintenance = this.assessOrderMaintenance(association);
    if (orderMaintenance < 0.3) {
      return "order_error";
    }
    const sequenceAccuracy = this.calculateSequenceAccuracy(userSequence, correctSequence);
    if (sequenceAccuracy < 0.5) {
      return "content_error";
    }
    const sequenceInfo = this.sequenceTypes[sequenceType];
    if (sequenceInfo?.logic_type === "causal") {
      const causalRespected = this.assessCausalDependencies(userSequence, correctSequence);
      if (causalRespected < 0.5) {
        return "causal_error";
      }
    }
    return "partial_error";
  }
  updateMemorySpan() {
    const memoryMetrics = this.cognitiveMetrics.sequentialMemory;
    if (memoryMetrics.length === 0) {
      this.sessionData.memorySpan = 0;
      return;
    }
    const totalSpan = memoryMetrics.reduce((sum, metric) => sum + metric.memorySpan, 0);
    this.sessionData.memorySpan = totalSpan / memoryMetrics.length;
  }
  updateOrderingAccuracy() {
    const orderMetrics = this.cognitiveMetrics.orderMaintenance;
    if (orderMetrics.length === 0) {
      this.sessionData.orderingAccuracy = 0;
      return;
    }
    const totalOrderMaintenance = orderMetrics.reduce(
      (sum, metric) => sum + metric.orderMaintained,
      0
    );
    this.sessionData.orderingAccuracy = totalOrderMaintenance / orderMetrics.length;
  }
  // ========================================================================
  // RELATÓRIOS E ANÁLISES FINAIS
  // ========================================================================
  generateCognitiveReport() {
    return {
      sequentialMemory: this.analyzeSequentialMemoryReport(),
      orderRecognition: this.analyzeOrderRecognitionReport(),
      temporalProcessing: this.analyzeTemporalProcessingReport(),
      workingMemory: this.analyzeWorkingMemoryReport(),
      adaptiveRecommendations: this.generateAdaptiveRecommendations()
    };
  }
  analyzeSequentialMemoryReport() {
    return {
      memorySpan: this.sessionData.memorySpan,
      overallAccuracy: this.sessionData.correctSequences / this.sessionData.totalSequences,
      averageProcessingTime: this.sessionData.averageSequentialProcessingTime,
      memoryStrategies: this.analyzeMemoryStrategies(),
      sequenceAccuracy: this.calculateAverageSequenceAccuracy()
    };
  }
  analyzeOrderRecognitionReport() {
    return {
      orderingAccuracy: this.sessionData.orderingAccuracy,
      logicRecognition: this.calculateAverageMetric(this.cognitiveMetrics.orderRecognition, "logicRecognition"),
      sequentialReasoning: this.calculateAverageMetric(this.cognitiveMetrics.orderRecognition, "sequentialReasoning"),
      orderingPatterns: this.analyzeOrderingPatterns()
    };
  }
  analyzeTemporalProcessingReport() {
    return {
      temporalLogic: this.calculateAverageMetric(this.cognitiveMetrics.temporalProcessing, "temporalLogic"),
      chronologicalAccuracy: this.calculateAverageMetric(this.cognitiveMetrics.temporalProcessing, "chronologicalAccuracy"),
      temporalSpan: this.calculateAverageMetric(this.cognitiveMetrics.temporalProcessing, "temporalSpan"),
      timeBasedReasoning: this.calculateAverageMetric(this.cognitiveMetrics.temporalProcessing, "timeBasedReasoning")
    };
  }
  analyzeWorkingMemoryReport() {
    return {
      actualCognitiveLoad: this.calculateAverageMetric(this.cognitiveMetrics.workingMemoryLoad, "actualLoad"),
      loadManagement: this.calculateAverageMetric(this.cognitiveMetrics.workingMemoryLoad, "loadManagement"),
      capacityUtilization: this.calculateAverageMetric(this.cognitiveMetrics.workingMemoryLoad, "capacityUtilization"),
      cognitiveEfficiency: this.calculateCognitiveEfficiency()
    };
  }
  analyzeMemoryStrategies() {
    const strategies = {};
    this.cognitiveMetrics.sequentialMemory.forEach((metric) => {
      const strategy = metric.memoryStrategy;
      strategies[strategy] = (strategies[strategy] || 0) + 1;
    });
    const total = this.cognitiveMetrics.sequentialMemory.length;
    Object.keys(strategies).forEach((strategy) => {
      strategies[strategy] = strategies[strategy] / total;
    });
    return strategies;
  }
  calculateAverageSequenceAccuracy() {
    const totalAccuracy = this.interactions.reduce(
      (sum, interaction) => sum + interaction.sequenceAccuracy,
      0
    );
    return this.interactions.length > 0 ? totalAccuracy / this.interactions.length : 0;
  }
  analyzeOrderingPatterns() {
    const patterns = {};
    this.cognitiveMetrics.orderRecognition.forEach((metric) => {
      const pattern = metric.orderingPatterns.pattern;
      patterns[pattern] = (patterns[pattern] || 0) + 1;
    });
    return patterns;
  }
  calculateCognitiveEfficiency() {
    const accuracySum = this.interactions.reduce(
      (sum, interaction) => sum + interaction.sequenceAccuracy,
      0
    );
    const timeSum = this.interactions.reduce(
      (sum, interaction) => sum + interaction.responseTime,
      0
    );
    if (this.interactions.length === 0 || timeSum === 0) return 0;
    const avgAccuracy = accuracySum / this.interactions.length;
    const avgTime = timeSum / this.interactions.length;
    return avgAccuracy / (avgTime / 1e4);
  }
  generateAdaptiveRecommendations() {
    const recommendations = [];
    const sequencePerformance = {};
    Object.entries(this.sessionData.sequencesByAccuracy).forEach(([type, data]) => {
      sequencePerformance[type] = data.correct / data.total;
    });
    const weakSequenceTypes = Object.entries(sequencePerformance).filter(([type, accuracy]) => accuracy < 0.6).map(([type]) => type);
    if (weakSequenceTypes.length > 0) {
      recommendations.push({
        type: "sequence_training",
        recommendation: `Reforçar tipos de sequência: ${weakSequenceTypes.join(", ")}`,
        confidence: 0.8,
        details: {
          sequenceTypes: weakSequenceTypes,
          suggestedActivities: ["sequential_practice", "pattern_recognition", "ordering_exercises"]
        }
      });
    }
    if (this.sessionData.memorySpan < 4) {
      recommendations.push({
        type: "memory_span_training",
        recommendation: "Exercícios para ampliar span de memória sequencial",
        confidence: 0.7,
        details: {
          currentSpan: this.sessionData.memorySpan,
          suggestedActivities: ["span_building", "chunking_practice", "memory_strategies"]
        }
      });
    }
    if (this.sessionData.orderingAccuracy < 0.6) {
      recommendations.push({
        type: "ordering_training",
        recommendation: "Desenvolvimento de habilidades de ordenação",
        confidence: 0.7,
        details: {
          currentAccuracy: this.sessionData.orderingAccuracy,
          suggestedActivities: ["ordering_practice", "logical_sequences", "temporal_reasoning"]
        }
      });
    }
    return recommendations;
  }
  getActivityScore() {
    if (this.sessionData.totalSequences === 0) return 0;
    const accuracy = this.sessionData.correctSequences / this.sessionData.totalSequences;
    const speedFactor = Math.max(0, 1 - (this.sessionData.averageSequentialProcessingTime - 8e3) / 16e3);
    const memoryFactor = Math.min(1, this.sessionData.memorySpan / 7);
    const orderingFactor = this.sessionData.orderingAccuracy;
    return Math.round(accuracy * speedFactor * memoryFactor * orderingFactor * 1e3);
  }
}
class ImageAssociationCollectorsHub {
  constructor() {
    this.hubId = "image-association-collectors-hub";
    this.version = "3.0.0";
    this.gameType = "ImageAssociation";
    this.associativeMemoryCollector = new AssociativeMemoryCollector();
    this.visualProcessingCollector = new VisualProcessingCollector();
    this.categorizationCollector = new CognitiveCategorizationCollector();
    this.flexibilityCollector = new MentalFlexibilityCollector();
    this.semanticMemoryCollector = new SemanticMemoryCollector();
    this.errorPatternCollector = new ErrorPatternCollector();
    this.simpleAssociationCollector = new SimpleAssociationCollector();
    this.categoricalAssociationCollector = new CategoricalAssociationCollector();
    this.emotionalAssociationCollector = new EmotionalAssociationCollector();
    this.functionalAssociationCollector = new FunctionalAssociationCollector();
    this.conceptualAssociationCollector = new ConceptualAssociationCollector();
    this.sequentialAssociationCollector = new SequentialAssociationCollector();
    this._collectors = {
      associativeMemory: this.associativeMemoryCollector,
      visualProcessing: this.visualProcessingCollector,
      categorization: this.categorizationCollector,
      flexibility: this.flexibilityCollector,
      semanticMemory: this.semanticMemoryCollector,
      errorPattern: this.errorPatternCollector,
      simpleAssociation: this.simpleAssociationCollector,
      categoricalAssociation: this.categoricalAssociationCollector,
      emotionalAssociation: this.emotionalAssociationCollector,
      functionalAssociation: this.functionalAssociationCollector,
      conceptualAssociation: this.conceptualAssociationCollector,
      sequentialAssociation: this.sequentialAssociationCollector
    };
    this.integratedData = [];
    this.sessionMetrics = /* @__PURE__ */ new Map();
    this.correlationMatrix = /* @__PURE__ */ new Map();
    this.initialized = false;
  }
  /**
   * Getter para acessar os coletores do hub
   * @returns {Object} Objeto com todos os coletores
   */
  get collectors() {
    return {
      associativeMemory: this.associativeMemoryCollector,
      visualProcessing: this.visualProcessingCollector,
      categorization: this.categorizationCollector,
      flexibility: this.flexibilityCollector,
      errorPattern: this.errorPatternCollector,
      simpleAssociation: this.simpleAssociationCollector,
      categoricalAssociation: this.categoricalAssociationCollector,
      emotionalAssociation: this.emotionalAssociationCollector,
      functionalAssociation: this.functionalAssociationCollector,
      conceptualAssociation: this.conceptualAssociationCollector,
      sequentialAssociation: this.sequentialAssociationCollector
    };
  }
  /**
   * INICIALIZAÇÃO DO HUB
   */
  async initialize() {
    try {
      this.associativeMemoryCollector.initialized = true;
      this.visualProcessingCollector.initialized = true;
      this.categorizationCollector.initialized = true;
      this.flexibilityCollector.initialized = true;
      this.initialized = true;
      console.log("🎯 ImageAssociation Collectors Hub: Inicializado com sucesso");
      return {
        status: "success",
        hubId: this.hubId,
        collectors: this.getCollectorStatus(),
        message: "Hub de coletores inicializado"
      };
    } catch (error) {
      console.error("❌ Erro na inicialização do hub:", error);
      return { status: "error", message: error.message };
    }
  }
  /**
   * COLETA INTEGRADA: Processar evento do jogo
   */
  async collectGameEvent(gameData) {
    if (!this.initialized) {
      await this.initialize();
    }
    const timestamp = Date.now();
    try {
      const eventData = {
        timestamp,
        sessionId: gameData.sessionId || `session_${timestamp}`,
        phase: gameData.phase,
        category: gameData.category,
        mainItem: gameData.mainItem,
        correctAnswer: gameData.correctAnswer,
        userAnswer: gameData.userAnswer,
        isCorrect: gameData.isCorrect,
        responseTime: gameData.responseTime,
        difficulty: gameData.difficulty,
        options: gameData.options,
        explanation: gameData.explanation
      };
      const [
        associativeData,
        visualData,
        categorizationData,
        flexibilityData
      ] = await Promise.all([
        this.associativeMemoryCollector.collectAssociationAttempt(eventData),
        this.visualProcessingCollector.collectVisualProcessing(eventData),
        this.categorizationCollector.collectCategorizationProcess(eventData),
        this.flexibilityCollector.collectFlexibilityEvent(eventData)
      ]);
      const integratedEvent = {
        timestamp,
        eventId: `event_${timestamp}`,
        gameType: this.gameType,
        rawData: eventData,
        collectors: {
          associativeMemory: associativeData,
          visualProcessing: visualData,
          categorization: categorizationData,
          mentalFlexibility: flexibilityData
        },
        integrationMetrics: this.calculateIntegrationMetrics(
          associativeData,
          visualData,
          categorizationData,
          flexibilityData
        )
      };
      this.integratedData.push(integratedEvent);
      this.updateSessionMetrics(integratedEvent);
      if (this.integratedData.length % 5 === 0) {
        await this.performRealTimeIntegratedAnalysis();
      }
      return integratedEvent;
    } catch (error) {
      console.error("❌ Erro na coleta integrada:", error);
      return { status: "error", message: error.message };
    }
  }
  /**
   * ANÁLISE INTEGRADA: Padrões cognitivos unificados
   */
  async performIntegratedAnalysis() {
    if (this.integratedData.length === 0) {
      return {
        status: "insufficient_data",
        message: "Dados insuficientes para análise integrada"
      };
    }
    try {
      const [
        associativeAnalysis,
        visualAnalysis,
        categorizationAnalysis,
        flexibilityAnalysis
      ] = await Promise.all([
        this.associativeMemoryCollector.analyzeAssociativePatterns(),
        this.visualProcessingCollector.analyzeVisualProcessingPatterns(),
        this.categorizationCollector.analyzeCategorizationPatterns(),
        this.flexibilityCollector.analyzeMentalFlexibilityPatterns()
      ]);
      const integratedAnalysis = {
        timestamp: Date.now(),
        gameType: this.gameType,
        totalEvents: this.integratedData.length,
        // Análises individuais
        individualAnalyses: {
          associativeMemory: associativeAnalysis,
          visualProcessing: visualAnalysis,
          categorization: categorizationAnalysis,
          mentalFlexibility: flexibilityAnalysis
        },
        // Análises integradas
        cognitiveProfile: this.buildIntegratedCognitiveProfile(),
        crossDomainCorrelations: this.analyzeCorrelations(),
        emergentPatterns: this.identifyEmergentPatterns(),
        cognitiveStrengths: this.identifyCognitiveStrengths(),
        cognitiveWeaknesses: this.identifyCognitiveWeaknesses(),
        developmentalTrajectory: this.analyzeDevelopmentalTrajectory(),
        // Insights e recomendações integradas
        unifiedInsights: this.generateUnifiedInsights(),
        personalizedRecommendations: this.generatePersonalizedRecommendations(),
        // Métricas de qualidade
        dataQuality: this.assessDataQuality(),
        analysisConfidence: this.calculateAnalysisConfidence()
      };
      return {
        status: "success",
        type: "integrated_analysis",
        data: integratedAnalysis
      };
    } catch (error) {
      console.error("❌ Erro na análise integrada:", error);
      return { status: "error", message: error.message };
    }
  }
  /**
   * MÉTRICAS DE INTEGRAÇÃO
   */
  calculateIntegrationMetrics(associative, visual, categorization, flexibility) {
    return {
      // Consistência entre coletores
      responseConsistency: this.calculateResponseConsistency(associative, visual, categorization, flexibility),
      // Correlações cognitivas
      associativeVisualCorrelation: this.calculateCorrelation(
        associative.associationStrength?.finalStrength || 0,
        visual.discriminationAccuracy?.finalDiscriminationScore || 0
      ),
      categorizationFlexibilityCorrelation: this.calculateCorrelation(
        categorization.ruleComplexity?.complexity || 0,
        flexibility.cognitiveLoad?.totalLoad || 0
      ),
      // Eficiência cognitiva integrada
      integratedEfficiency: this.calculateIntegratedEfficiency(associative, visual, categorization, flexibility),
      // Carga cognitiva total
      totalCognitiveLoad: this.calculateTotalCognitiveLoad(associative, visual, categorization, flexibility),
      // Sincronia cognitiva
      cognitiveSync: this.assessCognitiveSync(associative, visual, categorization, flexibility)
    };
  }
  /**
   * PERFIL COGNITIVO INTEGRADO
   */
  buildIntegratedCognitiveProfile() {
    const recentEvents = this.integratedData.slice(-10);
    if (recentEvents.length === 0) {
      return { status: "insufficient_data" };
    }
    const associativeMetrics = this.extractDomainMetrics(recentEvents, "associativeMemory");
    const visualMetrics = this.extractDomainMetrics(recentEvents, "visualProcessing");
    const categorizationMetrics = this.extractDomainMetrics(recentEvents, "categorization");
    const flexibilityMetrics = this.extractDomainMetrics(recentEvents, "mentalFlexibility");
    return {
      // Domínios cognitivos
      domains: {
        associativeMemory: {
          strength: this.calculateDomainStrength(associativeMetrics),
          efficiency: this.calculateDomainEfficiency(associativeMetrics),
          consistency: this.calculateDomainConsistency(associativeMetrics)
        },
        visualProcessing: {
          strength: this.calculateDomainStrength(visualMetrics),
          efficiency: this.calculateDomainEfficiency(visualMetrics),
          consistency: this.calculateDomainConsistency(visualMetrics)
        },
        categorization: {
          strength: this.calculateDomainStrength(categorizationMetrics),
          efficiency: this.calculateDomainEfficiency(categorizationMetrics),
          consistency: this.calculateDomainConsistency(categorizationMetrics)
        },
        mentalFlexibility: {
          strength: this.calculateDomainStrength(flexibilityMetrics),
          efficiency: this.calculateDomainEfficiency(flexibilityMetrics),
          consistency: this.calculateDomainConsistency(flexibilityMetrics)
        }
      },
      // Perfil geral
      overallProfile: {
        dominantDomain: this.identifyDominantDomain(),
        cognitiveBalance: this.assessCognitiveBalance(),
        processingStyle: this.identifyProcessingStyle(),
        learningPreference: this.identifyLearningPreference()
      },
      // Métricas integradas
      integratedMetrics: {
        globalEfficiency: this.calculateGlobalEfficiency(),
        cognitiveFlexibility: this.calculateGlobalFlexibility(),
        adaptiveCapacity: this.calculateAdaptiveCapacity(),
        learningRate: this.calculateLearningRate()
      }
    };
  }
  /**
   * ANÁLISE DE CORRELAÇÕES CRUZADAS
   */
  analyzeCorrelations() {
    const correlations = {
      // Correlações entre domínios
      associativeVisual: this.calculateDomainCorrelation("associativeMemory", "visualProcessing"),
      visualCategorization: this.calculateDomainCorrelation("visualProcessing", "categorization"),
      categorizationFlexibility: this.calculateDomainCorrelation("categorization", "mentalFlexibility"),
      flexibilityAssociative: this.calculateDomainCorrelation("mentalFlexibility", "associativeMemory"),
      // Correlações temporais
      performanceStability: this.analyzePerformanceStability(),
      learningProgression: this.analyzeLearningProgression(),
      // Correlações contextuais
      difficultyResponse: this.analyzeDifficultyResponse(),
      categorySpecificPerformance: this.analyzeCategorySpecificPerformance()
    };
    return {
      correlationMatrix: correlations,
      strongestCorrelations: this.identifyStrongestCorrelations(correlations),
      emergentPatterns: this.identifyCorrelationPatterns(correlations),
      predictiveFactors: this.identifyPredictiveFactors(correlations)
    };
  }
  /**
   * ANÁLISE EM TEMPO REAL
   */
  async performRealTimeIntegratedAnalysis() {
    const recentEvents = this.integratedData.slice(-5);
    const patterns = this.detectEmergentPatterns(recentEvents);
    const performance = this.assessIntegratedPerformance(recentEvents);
    if (performance.overallEfficiency < 0.4) {
      console.log("🎯 ImageAssociation Hub: Performance baixa detectada - análise aprofundada recomendada");
    } else if (performance.overallEfficiency > 0.8) {
      console.log("🎯 ImageAssociation Hub: Excelente performance integrada");
    }
    if (patterns.length > 0) {
      console.log("🎯 ImageAssociation Hub: Padrões emergentes detectados:", patterns);
    }
  }
  /**
   * INSIGHTS UNIFICADOS
   */
  generateUnifiedInsights() {
    const insights = [];
    const profile = this.buildIntegratedCognitiveProfile();
    if (profile.domains?.associativeMemory?.strength > 0.8) {
      insights.push("Excelente capacidade de formação de associações conceituais");
    }
    if (profile.domains?.visualProcessing?.efficiency > 0.7) {
      insights.push("Processamento visual eficiente e discriminação precisa");
    }
    if (profile.domains?.categorization?.strength > 0.8) {
      insights.push("Forte raciocínio categorial e flexibilidade taxonômica");
    }
    if (profile.domains?.mentalFlexibility?.consistency > 0.7) {
      insights.push("Excelente controle executivo e adaptabilidade mental");
    }
    if (profile.overallProfile?.cognitiveBalance > 0.8) {
      insights.push("Perfil cognitivo bem balanceado entre todos os domínios");
    }
    return insights;
  }
  /**
   * RECOMENDAÇÕES PERSONALIZADAS
   */
  generatePersonalizedRecommendations() {
    const recommendations = [];
    const profile = this.buildIntegratedCognitiveProfile();
    const correlations = this.analyzeCorrelations();
    if (profile.domains?.associativeMemory?.strength < 0.5) {
      recommendations.push("Praticar exercícios de associação semântica e formação de conceitos");
    }
    if (profile.domains?.visualProcessing?.efficiency < 0.6) {
      recommendations.push("Treinar discriminação visual com complexidade gradual");
    }
    if (profile.domains?.categorization?.consistency < 0.6) {
      recommendations.push("Exercitar raciocínio categorial com diferentes taxonomias");
    }
    if (profile.domains?.mentalFlexibility?.strength < 0.6) {
      recommendations.push("Desenvolver flexibilidade mental com exercícios de switching");
    }
    if (correlations.strongestCorrelations?.length > 0) {
      recommendations.push("Explorar pontos fortes para apoiar áreas em desenvolvimento");
    }
    return recommendations;
  }
  /**
   * MÉTODOS AUXILIARES MATEMÁTICOS
   */
  calculateCorrelation(x, y) {
    return Math.max(-1, Math.min(1, (x + y) / 2 - Math.abs(x - y) / 2));
  }
  calculateResponseConsistency(associative, visual, categorization, flexibility) {
    const accuracies = [
      associative.isCorrect ? 1 : 0,
      visual.isVisualMatch ? 1 : 0,
      categorization.correctCategorization ? 1 : 0,
      flexibility.responseAccuracy ? 1 : 0
    ];
    const mean = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    const variance = accuracies.reduce((sum, acc) => sum + Math.pow(acc - mean, 2), 0) / accuracies.length;
    return Math.max(0, 1 - Math.sqrt(variance));
  }
  calculateIntegratedEfficiency(associative, visual, categorization, flexibility) {
    const efficiencies = [
      associative.associationStrength?.finalStrength || 0,
      visual.discriminationAccuracy?.finalDiscriminationScore || 0,
      categorization.abstractionLevel?.score / 5 || 0,
      flexibility.adaptiveCapacity || 0
    ];
    return efficiencies.reduce((sum, eff) => sum + eff, 0) / efficiencies.length;
  }
  // Métodos auxiliares simplificados
  calculateTotalCognitiveLoad() {
    return 0.5;
  }
  assessCognitiveSync() {
    return 0.7;
  }
  extractDomainMetrics() {
    return [];
  }
  calculateDomainStrength() {
    return 0.7;
  }
  calculateDomainEfficiency() {
    return 0.75;
  }
  calculateDomainConsistency() {
    return 0.8;
  }
  identifyDominantDomain() {
    return "associativeMemory";
  }
  assessCognitiveBalance() {
    return 0.8;
  }
  identifyProcessingStyle() {
    return "systematic";
  }
  identifyLearningPreference() {
    return "visual-conceptual";
  }
  calculateGlobalEfficiency() {
    return 0.75;
  }
  calculateGlobalFlexibility() {
    return 0.8;
  }
  calculateAdaptiveCapacity() {
    return 0.7;
  }
  calculateLearningRate() {
    return 0.6;
  }
  calculateDomainCorrelation() {
    return 0.6;
  }
  analyzePerformanceStability() {
    return 0.7;
  }
  analyzeLearningProgression() {
    return 0.8;
  }
  analyzeDifficultyResponse() {
    return 0.6;
  }
  analyzeCategorySpecificPerformance() {
    return {};
  }
  identifyStrongestCorrelations() {
    return [];
  }
  identifyCorrelationPatterns() {
    return [];
  }
  identifyPredictiveFactors() {
    return [];
  }
  identifyEmergentPatterns() {
    return [];
  }
  detectEmergentPatterns() {
    return [];
  }
  assessIntegratedPerformance() {
    return { overallEfficiency: 0.7 };
  }
  identifyCognitiveStrengths() {
    return ["visual_processing", "categorization"];
  }
  identifyCognitiveWeaknesses() {
    return ["mental_flexibility"];
  }
  analyzeDevelopmentalTrajectory() {
    return { trend: "improving", rate: 0.1 };
  }
  assessDataQuality() {
    return { completeness: 0.9, consistency: 0.8 };
  }
  calculateAnalysisConfidence() {
    return 0.85;
  }
  updateSessionMetrics() {
  }
  /**
   * STATUS DOS COLETORES
   */
  getCollectorStatus() {
    return {
      associativeMemory: this.associativeMemoryCollector.getStatus(),
      visualProcessing: this.visualProcessingCollector.getStatus(),
      categorization: this.categorizationCollector.getStatus(),
      mentalFlexibility: this.flexibilityCollector.getStatus()
    };
  }
  /**
   * RESET COMPLETO
   */
  reset() {
    this.associativeMemoryCollector.reset();
    this.visualProcessingCollector.reset();
    this.categorizationCollector.reset();
    this.flexibilityCollector.reset();
    this.integratedData = [];
    this.sessionMetrics.clear();
    this.correlationMatrix.clear();
    this.initialized = false;
    console.log("🎯 ImageAssociation Hub: Reset completo realizado");
  }
  /**
   * STATUS DO HUB
   */
  getStatus() {
    return {
      hubId: this.hubId,
      version: this.version,
      gameType: this.gameType,
      isInitialized: this.initialized,
      totalEvents: this.integratedData.length,
      collectors: this.getCollectorStatus(),
      lastEvent: this.integratedData.length > 0 ? this.integratedData[this.integratedData.length - 1].timestamp : null
    };
  }
}
class ImageAssociationProcessors extends IGameProcessor {
  constructor(logger2 = null) {
    const config = {
      category: "conceptual-association",
      therapeuticFocus: ["conceptual_understanding", "categorical_thinking", "semantic_memory"],
      cognitiveAreas: ["semantic_processing", "executive_function", "memory"],
      thresholds: {
        accuracy: 65,
        responseTime: 4e3,
        engagement: 65
      }
    };
    super(config);
    this.logger = logger2 && typeof logger2.therapeutic === "function" ? logger2 : {
      info: (...args) => console.info("🔗 [IMAGE-ASSOCIATION]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
      error: (...args) => console.error("🔴 [IMAGE-ERROR]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
      warn: (...args) => console.warn("🟡 [IMAGE-WARN]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
      debug: (...args) => console.debug("⚪ [IMAGE-DEBUG]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
      therapeutic: (...args) => console.info("🏥 [IMAGE-THERAPEUTIC]", (/* @__PURE__ */ new Date()).toISOString(), ...args)
    };
    this.logger.info("🔗 Processadores Image Association inicializados com interface padrão");
  }
  /**
   * Valida os dados de entrada do jogo
   * @param {Object} gameData - Dados do jogo para validação
   * @throws {Error} Se os dados são inválidos
   */
  validateGameData(gameData) {
    const required = ["sessionId", "userId", "gameId", "timestamp"];
    const missing = required.filter((field) => !gameData || !gameData[field]);
    if (missing.length > 0) {
      const error = new Error(`Missing required fields: ${missing.join(", ")}`);
      error.code = "VALIDATION_ERROR";
      this.logger.error("❌ Validação falhou", { missing, gameData: gameData || "null" });
      throw error;
    }
    if (gameData.gameId !== "ImageAssociation") {
      const error = new Error(`Invalid gameId: expected 'ImageAssociation', got '${gameData.gameId}'`);
      error.code = "INVALID_GAME_ID";
      this.logger.error("❌ Game ID inválido", { expected: "ImageAssociation", received: gameData.gameId });
      throw error;
    }
    if (!gameData.data || typeof gameData.data !== "object") {
      const error = new Error("Invalid game data structure: data field must be an object");
      error.code = "INVALID_DATA_STRUCTURE";
      this.logger.error("❌ Estrutura de dados inválida", { dataType: typeof gameData.data });
      throw error;
    }
    this.logger.debug("✅ Validação de dados aprovada", {
      sessionId: gameData.sessionId,
      userId: gameData.userId,
      gameId: gameData.gameId
    });
  }
  /**
   * Retorna informações sobre o processador
   * @returns {Object} Informações do processador
   */
  getProcessorInfo() {
    return {
      name: "ImageAssociationProcessors",
      version: "3.0.0",
      gameId: "ImageAssociation",
      category: this.config.category,
      therapeuticFocus: this.config.therapeuticFocus,
      cognitiveAreas: this.config.cognitiveAreas,
      thresholds: this.config.thresholds,
      capabilities: [
        "associative_memory_analysis",
        "visual_processing_evaluation",
        "categorization_assessment",
        "mental_flexibility_measurement"
      ]
    };
  }
  /**
   * Processa coletores com circuit breaker
   * @param {Object} collectorsHub - Hub de coletores
   * @param {Object} gameData - Dados do jogo
   * @returns {Promise<Object>} Dados processados com fallback
   */
  async processCollectorsWithCircuitBreaker(collectorsHub, gameData) {
    const results = {};
    const errors = [];
    if (!collectorsHub || !collectorsHub.collectors) {
      this.logger.warn("⚠️ Nenhum hub de coletores fornecido");
      return { results: {}, errors: ["No collectors hub provided"] };
    }
    const collectors = collectorsHub.collectors;
    const collectorNames = Object.keys(collectors);
    this.logger.info("🔄 Processando coletores", {
      count: collectorNames.length,
      collectors: collectorNames
    });
    for (const collectorName of collectorNames) {
      try {
        const collector = collectors[collectorName];
        if (this.isCollectorHealthy(collectorName)) {
          const startTime = Date.now();
          const result = await this.processCollector(collector, gameData);
          const processingTime = Date.now() - startTime;
          results[collectorName] = {
            data: result,
            processingTime,
            status: "success"
          };
          this.logger.debug(`✅ Coletor ${collectorName} processado`, {
            processingTime: `${processingTime}ms`
          });
        } else {
          results[collectorName] = this.getFallbackResult(collectorName);
          this.logger.warn(`⚠️ Usando fallback para coletor ${collectorName}`);
        }
      } catch (error) {
        errors.push({
          collector: collectorName,
          error: error.message,
          code: error.code || "COLLECTOR_ERROR"
        });
        this.logger.error(`❌ Erro no coletor ${collectorName}`, {
          error: error.message,
          stack: error.stack
        });
        this.recordCollectorFailure(collectorName);
      }
    }
    return { results, errors };
  }
  /**
   * Verifica se um coletor está saudável
   * @param {string} collectorName - Nome do coletor
   * @returns {boolean} True se o coletor está saudável
   */
  isCollectorHealthy(collectorName) {
    return !this.failedCollectors?.has(collectorName);
  }
  /**
   * Processa um coletor individual
   * @param {Object} collector - Instância do coletor
   * @param {Object} gameData - Dados do jogo
   * @returns {Promise<Object>} Resultado do processamento
   */
  async processCollector(collector, gameData) {
    if (!collector || typeof collector.collect !== "function") {
      throw new Error("Invalid collector: must have a collect method");
    }
    return await collector.collect(gameData);
  }
  /**
   * Retorna resultado fallback para coletor com problemas
   * @param {string} collectorName - Nome do coletor
   * @returns {Object} Resultado padrão
   */
  getFallbackResult(collectorName) {
    return {
      data: null,
      processingTime: 0,
      status: "fallback",
      message: `Fallback result for ${collectorName}`,
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    };
  }
  /**
   * Registra falha de um coletor
   * @param {string} collectorName - Nome do coletor
   */
  recordCollectorFailure(collectorName) {
    if (!this.failedCollectors) {
      this.failedCollectors = /* @__PURE__ */ new Set();
    }
    this.failedCollectors.add(collectorName);
    setTimeout(() => {
      this.failedCollectors.delete(collectorName);
      this.logger.info(`🔄 Coletor ${collectorName} reabilitado após recuperação`);
    }, 5 * 60 * 1e3);
  }
  /**
   * Processa dados do jogo Image Association
   * @param {Object} gameData - Dados coletados do jogo
   * @param {Object} collectorsHub - Hub de coletores específico do jogo
   * @returns {Promise<Object>} Análise terapêutica específica
   */
  async processGameData(gameData, collectorsHub = null) {
    try {
      this.logger?.info("🎮 Processando dados ImageAssociation", {
        sessionId: gameData.sessionId,
        userId: gameData.userId,
        collectorsActive: collectorsHub ? Object.keys(collectorsHub.collectors || {}).length : 0
      });
      const metrics = await this.processImageAssociationMetrics(gameData, gameData);
      const therapeuticAnalysis = this.generateTherapeuticAnalysis(metrics, gameData);
      const processedMetrics = this.processMetricsForDatabase(metrics, gameData);
      return {
        success: true,
        gameType: this.gameType,
        metrics,
        therapeuticAnalysis,
        processedMetrics,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    } catch (error) {
      this.logger?.error("❌ Erro ao processar dados ImageAssociation:", error);
      return {
        success: false,
        gameType: this.gameType,
        error: error.message,
        fallbackAnalysis: this.generateFallbackAnalysis(gameData),
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    }
  }
  /**
   * Atualiza método prepareImageAssociationData para suportar resultado de coletores
   * @param {Object} gameData - Dados originais do jogo
   * @param {Object} collectorResults - Resultados dos coletores processados
   * @returns {Object} Dados preparados para análise
   */
  prepareImageAssociationData(gameData, collectorResults = null) {
    const metrics = gameData.metrics || {};
    const attempts = gameData.attempts || [];
    let collectorData = {};
    if (collectorResults && collectorResults.results) {
      collectorData = Object.keys(collectorResults.results).reduce((acc, collectorName) => {
        const result = collectorResults.results[collectorName];
        if (result && result.status === "success" && result.data) {
          acc[collectorName] = result.data;
        }
        return acc;
      }, {});
      this.logger.debug("📊 Dados dos coletores integrados", {
        totalCollectors: Object.keys(collectorResults.results).length,
        successfulCollectors: Object.keys(collectorData).length,
        errors: collectorResults.errors?.length || 0
      });
    }
    return {
      sessionId: gameData.sessionId,
      userId: gameData.userId,
      gameName: "ImageAssociation",
      accuracy: metrics.accuracy || 0,
      responseTime: metrics.responseTime || 0,
      attempts,
      associations: attempts.map((attempt) => ({
        imageId: attempt.imageId,
        associatedImageId: attempt.associatedImageId,
        responseTime: attempt.responseTime,
        correct: attempt.correct,
        category: attempt.category,
        difficulty: attempt.difficulty
      })),
      categories: this.extractCategories(attempts),
      sessionDuration: gameData.sessionDuration || 0,
      completionRate: metrics.completionRate || 0,
      errorPatterns: this.analyzeErrorPatterns(attempts),
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      // Dados adicionais dos coletores
      collectorData,
      // Metadados de qualidade
      dataQuality: {
        hasCollectorData: Object.keys(collectorData).length > 0,
        collectorCount: Object.keys(collectorData).length,
        hasAttempts: attempts.length > 0,
        attemptCount: attempts.length,
        hasMetrics: Object.keys(metrics).length > 0,
        completeness: this.calculateDataCompleteness(gameData, collectorResults)
      }
    };
  }
  /**
   * Calcula a completude dos dados recebidos
   * @param {Object} gameData - Dados originais do jogo
   * @param {Object} collectorResults - Resultados dos coletores
   * @returns {number} Pontuação de completude (0-1)
   */
  calculateDataCompleteness(gameData, collectorResults = null) {
    let completeness = 0;
    let totalChecks = 0;
    const basicFields = ["sessionId", "userId", "gameId", "timestamp"];
    basicFields.forEach((field) => {
      totalChecks++;
      if (gameData && gameData[field]) {
        completeness++;
      }
    });
    totalChecks++;
    if (gameData && gameData.attempts && gameData.attempts.length > 0) {
      completeness++;
    }
    totalChecks++;
    if (gameData && gameData.metrics && Object.keys(gameData.metrics).length > 0) {
      completeness++;
    }
    if (collectorResults && collectorResults.results) {
      const collectorsWithData = Object.values(collectorResults.results).filter(
        (result) => result && result.status === "success" && result.data
      );
      totalChecks++;
      if (collectorsWithData.length > 0) {
        completeness += collectorsWithData.length / Object.keys(collectorResults.results).length;
      }
    }
    const score = totalChecks > 0 ? completeness / totalChecks : 0;
    this.logger.debug("📊 Completude dos dados calculada", {
      score: score.toFixed(2),
      completeness,
      totalChecks,
      hasCollectors: collectorResults ? Object.keys(collectorResults.results || {}).length : 0
    });
    return Math.min(1, Math.max(0, score));
  }
  /**
   * Avalia a qualidade geral dos dados
   * @param {Object} gameData - Dados do jogo
   * @returns {Object} Avaliação da qualidade
   */
  assessDataQuality(gameData) {
    const quality = {
      completeness: this.calculateDataCompleteness(gameData),
      validity: this.validateDataStructure(gameData),
      consistency: this.checkDataConsistency(gameData),
      reliability: "medium"
      // Padrão
    };
    const scores = [quality.completeness, quality.validity ? 1 : 0, quality.consistency];
    quality.overallScore = scores.reduce((a, b) => a + b, 0) / scores.length;
    if (quality.overallScore >= 0.8) {
      quality.reliability = "high";
    } else if (quality.overallScore >= 0.6) {
      quality.reliability = "medium";
    } else {
      quality.reliability = "low";
    }
    return quality;
  }
  /**
   * Valida a estrutura dos dados
   * @param {Object} gameData - Dados do jogo
   * @returns {boolean} True se a estrutura é válida
   */
  validateDataStructure(gameData) {
    try {
      this.validateGameData(gameData);
      return true;
    } catch (error) {
      return false;
    }
  }
  /**
   * Verifica a consistência dos dados
   * @param {Object} gameData - Dados do jogo
   * @returns {number} Pontuação de consistência (0-1)
   */
  checkDataConsistency(gameData) {
    let consistency = 0;
    let checks = 0;
    if (gameData.attempts && gameData.attempts.length > 0) {
      checks++;
      const times = gameData.attempts.map((a) => a.responseTime).filter((t) => t > 0);
      if (times.length > 0) {
        times.reduce((a, b) => a + b, 0) / times.length;
        const validTimes = times.filter((t) => t > 0 && t < 6e4);
        consistency += validTimes.length / times.length;
      }
    }
    if (gameData.metrics && gameData.metrics.accuracy !== void 0) {
      checks++;
      if (gameData.metrics.accuracy >= 0 && gameData.metrics.accuracy <= 1) {
        consistency++;
      }
    }
    return checks > 0 ? consistency / checks : 0;
  }
  /**
   * Analisa memória associativa
   */
  async analyzeAssociativeMemory(data) {
    try {
      const associations = data.associations || [];
      const correctAssociations = associations.filter((a) => a.correct);
      const associationStrength = correctAssociations.length / (associations.length || 1);
      const categoryConsistency = this.calculateCategoryConsistency(associations);
      const semanticDistance = this.calculateSemanticDistance(associations);
      const memoryStability = this.calculateMemoryStability(associations);
      return {
        associationStrength,
        categoryConsistency,
        semanticDistance,
        memoryStability,
        // Métricas específicas
        totalAssociations: associations.length,
        correctAssociations: correctAssociations.length,
        accuracyRate: associationStrength,
        averageResponseTime: this.calculateAverageResponseTime(associations),
        // Padrões identificados
        dominantPatterns: this.identifyAssociativePatterns(associations),
        strengthAreas: this.identifyAssociativeStrengths(associations),
        challengeAreas: this.identifyAssociativeChallenges(associations),
        // Recomendações
        recommendations: this.generateAssociativeRecommendations(associationStrength, categoryConsistency, semanticDistance)
      };
    } catch (error) {
      this.logger.error("❌ Erro na análise de memória associativa:", error);
      return this.getFallbackAssociativeData();
    }
  }
  /**
   * Analisa processamento visual
   */
  async analyzeVisualProcessing(data) {
    try {
      const associations = data.associations || [];
      const visualDiscrimination = this.calculateVisualDiscrimination(associations);
      const imageRecognition = this.calculatePerceptualSpeed(associations) * 100;
      const visualMemory = this.calculateVisualMemory(associations);
      const perceptualSpeed = this.calculatePerceptualSpeed(associations) * 100;
      return {
        visualDiscrimination,
        imageRecognition,
        visualMemory,
        perceptualSpeed,
        // Métricas específicas
        processingAccuracy: data.accuracy || 0,
        averageProcessingTime: this.calculateAverageResponseTime(associations),
        visualComplexityHandling: this.assessVisualComplexityHandling(associations),
        // Padrões visuais
        visualPatterns: this.identifyVisualPatterns(associations),
        visualStrengths: this.identifyVisualStrengths(associations),
        visualChallenges: this.identifyVisualChallenges(associations),
        // Recomendações
        recommendations: this.generateVisualProcessingRecommendations(visualDiscrimination, imageRecognition, visualMemory)
      };
    } catch (error) {
      this.logger.error("❌ Erro na análise de processamento visual:", error);
      return this.getFallbackVisualData();
    }
  }
  /**
   * Analisa categorização cognitiva
   */
  async analyzeCategorization(data) {
    try {
      const associations = data.associations || [];
      const categories = data.categories || [];
      const categoricalThinking = this.calculateCategoricalThinking(associations, categories);
      const abstractReasoning = this.calculateAbstractReasoning(associations);
      const conceptualFlexibility = this.calculateConceptualFlexibility(associations);
      const taxonomicOrganization = this.calculateTaxonomicOrganization(associations);
      return {
        categoricalThinking,
        abstractReasoning,
        conceptualFlexibility,
        taxonomicOrganization,
        // Métricas específicas
        categoryAccuracy: this.calculateCategoryAccuracy(associations, categories),
        categoryConsistency: this.calculateCategoryConsistency(associations),
        conceptualBreadth: categories.length,
        // Padrões categorizacionais
        categorizationPatterns: this.identifyCategorizationPatterns(associations, categories),
        categorizationStrengths: this.identifyCategorizationStrengths(associations),
        categorizationChallenges: this.identifyCategorizationChallenges(associations),
        // Recomendações
        recommendations: this.generateCategorizationRecommendations(categoricalThinking, abstractReasoning, conceptualFlexibility)
      };
    } catch (error) {
      this.logger.error("❌ Erro na análise de categorização:", error);
      return this.getFallbackCategorizationData();
    }
  }
  /**
   * Analisa flexibilidade mental
   */
  async analyzeMentalFlexibility(data) {
    try {
      const associations = data.associations || [];
      const cognitiveFlexibility = this.calculateCognitiveFlexibility(associations);
      const setShifting = this.calculateSetShifting(associations);
      const adaptiveThinking = this.calculateAdaptiveThinking(associations);
      const inhibitoryControl = this.calculateInhibitoryControl(associations);
      return {
        cognitiveFlexibility,
        setShifting,
        adaptiveThinking,
        inhibitoryControl,
        // Métricas específicas
        flexibilityIndex: (cognitiveFlexibility + setShifting + adaptiveThinking) / 3,
        adaptationSpeed: this.calculateAdaptationSpeed(associations),
        errorRecovery: this.calculateErrorRecovery(associations),
        // Padrões de flexibilidade
        flexibilityPatterns: this.identifyFlexibilityPatterns(associations),
        flexibilityStrengths: this.identifyFlexibilityStrengths(associations),
        flexibilityChallenges: this.identifyFlexibilityChallenges(associations),
        // Recomendações
        recommendations: this.generateMentalFlexibilityRecommendations(cognitiveFlexibility, setShifting, adaptiveThinking)
      };
    } catch (error) {
      this.logger.error("❌ Erro na análise de flexibilidade mental:", error);
      return this.getFallbackFlexibilityData();
    }
  }
  /**
   * Gera análise integrada dos resultados dos processadores
   */
  async generateIntegratedAnalysis(results) {
    try {
      const {
        associativeResults,
        visualResults,
        categorizationResults,
        flexibilityResults
      } = results;
      const overallScore = (associativeResults.accuracyRate + visualResults.processingAccuracy + categorizationResults.categoryAccuracy + flexibilityResults.flexibilityIndex) / 4;
      return {
        overallScore,
        cognitiveProfile: "balanced",
        // Placeholder
        strengths: this.identifyIntegratedStrengths(results),
        challenges: this.identifyIntegratedChallenges(results),
        recommendations: this.generateIntegratedRecommendations(results),
        status: "success"
      };
    } catch (error) {
      this.logger.error("❌ Erro na geração da análise integrada:", error);
      return {
        overallScore: 0,
        cognitiveProfile: "incomplete_data",
        strengths: [],
        challenges: [],
        recommendations: [],
        status: "fallback"
      };
    }
  }
  /**
   * Identifica forças em análise integrada
   */
  identifyIntegratedStrengths(results) {
    const strengths = [];
    if (results.associativeResults.accuracyRate > 0.7) {
      strengths.push("associative_memory");
    }
    return strengths;
  }
  /**
   * Identifica desafios em análise integrada
   */
  identifyIntegratedChallenges(results) {
    const challenges = [];
    if (results.flexibilityResults.flexibilityIndex < 0.5) {
      challenges.push("cognitive_flexibility");
    }
    return challenges;
  }
  /**
   * Gera recomendações em análise integrada
   */
  generateIntegratedRecommendations(results) {
    const recommendations = [];
    if (results.associativeResults.accuracyRate < 0.6) {
      recommendations.push("exercicios_memoria");
    }
    return recommendations;
  }
  /**
   * Avalia a qualidade dos dados de entrada
   */
  /**
   * Extrai categorias únicas de tentativas
   */
  extractCategories(attempts) {
    const categories = /* @__PURE__ */ new Set();
    attempts.forEach((attempt) => {
      if (attempt.category) {
        categories.add(attempt.category);
      }
    });
    return Array.from(categories);
  }
  /**
   * Analisa padrões de erro em tentativas
   */
  /**
   * Identifica tipos comuns de erro
   */
  identifyCommonErrorTypes(attempts) {
    const errorTypes = {
      visual: 0,
      conceptual: 0,
      associative: 0,
      categorical: 0,
      temporal: 0
    };
    attempts.forEach((attempt) => {
      if (!attempt.correct) {
        if (attempt.responseTime > 5e3) errorTypes.temporal++;
        if (attempt.category !== attempt.expectedCategory) errorTypes.categorical++;
        if (attempt.visualComplexity > 0.7) errorTypes.visual++;
        errorTypes.conceptual++;
      }
    });
    return errorTypes;
  }
  analyzeErrorPatterns(attempts) {
    const errorPatterns = attempts.map((attempt) => {
      return {
        attemptId: attempt.attemptId,
        errorType: attempt.correct ? "none" : "associative_error",
        severity: attempt.correct ? "low" : "high",
        timestamp: attempt.timestamp
      };
    });
    return errorPatterns;
  }
  /**
   * Método terapêutico exemplo: Síntese de pensamento conceptual
   */
  synthesizeConceptualThinking(associativeResults, categorizationResults) {
    return {
      strength: "alta",
      areas: ["associação", "categorização"],
      recomendações: ["exercícios de associação", "atividades de categorização"]
    };
  }
  /**
   * Método terapêutico exemplo: Síntese de flexibilidade cognitiva
   */
  synthesizeCognitiveFlexibility(flexibilityResults, visualResults) {
    return {
      strength: "média",
      areas: ["flexibilidade cognitiva", "processamento visual"],
      recomendações: ["jogos de raciocínio", "atividades visuais dinâmicas"]
    };
  }
  /**
   * Método terapêutico exemplo: Síntese de capacidade de aprendizagem
   */
  synthesizeLearningCapacity(associativeResults, flexibilityResults) {
    return {
      strength: "alta",
      areas: ["memória associativa", "flexibilidade mental"],
      recomendações: ["técnicas de memorização", "exercícios de flexibilidade"]
    };
  }
  /**
   * Método terapêutico exemplo: Síntese de estratégias adaptativas
   */
  synthesizeAdaptiveStrategies(flexibilityResults, categorizationResults) {
    return {
      strength: "média",
      areas: ["flexibilidade cognitiva", "categorização"],
      recomendações: ["atividades de categorização", "exercícios de adaptação"]
    };
  }
  /**
   * Cálculo de desempenho geral baseado em múltiplas métricas
   */
  calculateOverallPerformance(associativeResults, visualResults, categorizationResults, flexibilityResults) {
    return (associativeResults.accuracyRate * 0.4 + visualResults.processingAccuracy * 0.3 + categorizationResults.categoryAccuracy * 0.2 + flexibilityResults.flexibilityIndex * 0.1) * 100;
  }
  /**
   * Identifica áreas de força em múltiplas métricas
   */
  identifyStrengthAreas(associativeResults, visualResults, categorizationResults, flexibilityResults) {
    const strengths = [];
    if (associativeResults.accuracyRate > 0.7) {
      strengths.push("memória associativa");
    }
    if (visualResults.processingAccuracy > 0.7) {
      strengths.push("processamento visual");
    }
    if (categorizationResults.categoryAccuracy > 0.7) {
      strengths.push("categorização");
    }
    if (flexibilityResults.flexibilityIndex > 0.7) {
      strengths.push("flexibilidade mental");
    }
    return strengths;
  }
  /**
   * Identifica áreas de melhoria em múltiplas métricas
   */
  identifyImprovementAreas(associativeResults, visualResults, categorizationResults, flexibilityResults) {
    const improvements = [];
    if (associativeResults.accuracyRate < 0.6) {
      improvements.push("memória associativa");
    }
    if (visualResults.processingAccuracy < 0.6) {
      improvements.push("processamento visual");
    }
    if (categorizationResults.categoryAccuracy < 0.6) {
      improvements.push("categorização");
    }
    if (flexibilityResults.flexibilityIndex < 0.6) {
      improvements.push("flexibilidade mental");
    }
    return improvements;
  }
  /**
   * Gera recomendações adaptativas com base em múltiplas métricas
   */
  generateAdaptiveRecommendations(associativeResults, visualResults, categorizationResults, flexibilityResults) {
    const recommendations = [];
    if (associativeResults.accuracyRate < 0.6) {
      recommendations.push("exercícios de memória associativa");
    }
    if (visualResults.processingAccuracy < 0.6) {
      recommendations.push("atividades de processamento visual");
    }
    if (categorizationResults.categoryAccuracy < 0.6) {
      recommendations.push("exercícios de categorização");
    }
    if (flexibilityResults.flexibilityIndex < 0.6) {
      recommendations.push("atividades de flexibilidade mental");
    }
    return recommendations;
  }
  /**
   * Processa métricas específicas do ImageAssociation
   * @param {Object} gameData - Dados do jogo ImageAssociation
   * @param {Object} sessionData - Dados da sessão
   * @returns {Object} Métricas processadas
   */
  async processImageAssociationMetrics(gameData, sessionData) {
    try {
      this.logger?.info("🔗 Processando métricas ImageAssociation...", {
        sessionId: sessionData.sessionId
      });
      const metrics = {
        // Métricas de associação conceitual
        conceptualAssociation: this.analyzeConceptualAssociation(gameData),
        // Análise de pensamento categórico
        categoricalThinking: this.analyzeCategoricalThinking(gameData),
        // Tempo de resposta semântico
        semanticResponseTime: this.analyzeSemanticResponseTime(gameData),
        // Padrões de associação
        associationPatterns: this.analyzeAssociationPatterns(gameData),
        // Análise comportamental específica
        associationBehavior: this.analyzeAssociationBehavior(gameData),
        // Métricas cognitivas relacionadas à associação
        associationCognition: this.analyzeAssociationCognition(gameData),
        // Indicadores terapêuticos
        therapeuticIndicators: this.generateTherapeuticIndicators(gameData),
        // Recomendações específicas
        recommendations: this.generateImageAssociationRecommendations(gameData)
      };
      this.logger?.info("✅ Métricas ImageAssociation processadas", {
        accuracy: metrics.conceptualAssociation.accuracy,
        responseTime: metrics.semanticResponseTime.average,
        associationCount: metrics.associationPatterns.totalAssociations
      });
      return metrics;
    } catch (error) {
      this.logger?.error("❌ Erro ao processar métricas ImageAssociation:", error);
      throw error;
    }
  }
  /**
   * Análise de associação conceitual
   */
  analyzeConceptualAssociation(gameData) {
    const { totalCorrect = 0, totalAttempts = 1, interactions = [] } = gameData;
    return {
      accuracy: Math.round(totalCorrect / totalAttempts * 100),
      totalAttempts,
      totalCorrect,
      conceptualStrength: this.calculateConceptualStrength(interactions),
      associationSpeed: this.calculateAssociationSpeed(interactions),
      categoryRecognition: this.calculateCategoryRecognition(interactions)
    };
  }
  /**
   * Análise de pensamento categórico
   */
  analyzeCategoricalThinking(gameData) {
    const { interactions = [], categories = [] } = gameData;
    return {
      categoryAccuracy: this.calculateCategoryAccuracy(interactions, categories),
      categoryConsistency: this.calculateCategoryConsistency(interactions),
      categoricalFlexibility: this.calculateCategoricalFlexibility(interactions),
      semanticOrganization: this.calculateSemanticOrganization(interactions)
    };
  }
  /**
   * Análise de tempo de resposta semântico
   */
  analyzeSemanticResponseTime(gameData) {
    const { interactions = [] } = gameData;
    const responseTimes = interactions.map((i) => i.responseTime || 0).filter((t) => t > 0);
    if (responseTimes.length === 0) {
      return { average: 0, median: 0, variability: 0, pattern: "insufficient_data" };
    }
    const average = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const sorted = responseTimes.sort((a, b) => a - b);
    const median = sorted[Math.floor(sorted.length / 2)];
    const variability = this.calculateVariability(responseTimes);
    return {
      average: Math.round(average),
      median: Math.round(median),
      min: Math.min(...responseTimes),
      max: Math.max(...responseTimes),
      variability: Math.round(variability),
      pattern: this.identifyResponsePattern(responseTimes)
    };
  }
  /**
   * Identifica padrões de tempo de resposta
   */
  identifyResponsePattern(responseTimes) {
    if (responseTimes.length === 0) return "insufficient_data";
    const average = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const variance = responseTimes.reduce((sum, time) => sum + Math.pow(time - average, 2), 0) / responseTimes.length;
    const stdDev = Math.sqrt(variance);
    if (stdDev < average * 0.2) return "consistent";
    if (stdDev < average * 0.5) return "moderate_variation";
    return "high_variation";
  }
  /**
   * Análise de padrões de associação
   */
  analyzeAssociationPatterns(gameData) {
    const { interactions = [] } = gameData;
    return {
      totalAssociations: interactions.length,
      correctAssociations: interactions.filter((i) => i.correct).length,
      associationTypes: this.identifyAssociationTypes(interactions),
      difficultyProgression: this.analyzeDifficultyProgression(interactions),
      errorPatterns: this.identifyErrorPatterns(interactions)
    };
  }
  /**
   * Análise comportamental específica
   */
  analyzeAssociationBehavior(gameData) {
    const { interactions = [] } = gameData;
    return {
      persistence: this.calculatePersistence(interactions),
      adaptability: this.calculateAdaptability(interactions),
      strategicThinking: this.calculateStrategicThinking(interactions),
      frustrationTolerance: this.calculateFrustrationTolerance(interactions)
    };
  }
  /**
   * Análise cognitiva relacionada à associação
   */
  analyzeAssociationCognition(gameData) {
    const { interactions = [] } = gameData;
    return {
      executiveFunction: this.calculateExecutiveFunction(interactions),
      workingMemory: this.calculateWorkingMemory(interactions),
      processingSpeed: this.calculateProcessingSpeed(interactions),
      semanticMemory: this.calculateSemanticMemory(interactions)
    };
  }
  /**
   * Gera indicadores terapêuticos
   */
  generateTherapeuticIndicators(gameData) {
    const { interactions = [], accuracy = 0, averageResponseTime = 0 } = gameData;
    return {
      cognitiveLoad: this.assessCognitiveLoad(gameData),
      therapeuticGoals: this.identifyTherapeuticGoals(gameData),
      interventionNeeds: this.identifyInterventionNeeds(gameData),
      progressMarkers: this.generateProgressMarkers(gameData)
    };
  }
  /**
   * Gera recomendações específicas para ImageAssociation
   */
  generateImageAssociationRecommendations(gameData) {
    const recommendations = [];
    const accuracy = gameData.accuracy || 0;
    const responseTime = gameData.averageResponseTime || 0;
    if (accuracy < 60) {
      recommendations.push({
        type: "conceptual_support",
        priority: "high",
        description: "Exercícios de associação conceitual básica"
      });
    }
    if (responseTime > 5e3) {
      recommendations.push({
        type: "processing_speed",
        priority: "medium",
        description: "Atividades para melhorar velocidade de processamento semântico"
      });
    }
    return recommendations;
  }
  // Métodos auxiliares de cálculo
  calculateConceptualStrength(interactions) {
    const correctInteractions = interactions.filter((i) => i.correct);
    return correctInteractions.length / Math.max(1, interactions.length) * 100;
  }
  calculateAssociationSpeed(interactions) {
    const times = interactions.map((i) => i.responseTime || 0).filter((t) => t > 0);
    if (times.length === 0) return 0;
    return times.reduce((sum, t) => sum + t, 0) / times.length;
  }
  calculateCategoryRecognition(interactions) {
    const categoryMatches = interactions.filter((i) => i.category && i.expectedCategory === i.category);
    return categoryMatches.length / Math.max(1, interactions.length) * 100;
  }
  calculateCategoryAccuracy(interactions, categories) {
    if (categories.length === 0) return 0;
    const correctByCategory = {};
    const totalByCategory = {};
    interactions.forEach((i) => {
      const cat = i.category || "unknown";
      totalByCategory[cat] = (totalByCategory[cat] || 0) + 1;
      if (i.correct) {
        correctByCategory[cat] = (correctByCategory[cat] || 0) + 1;
      }
    });
    return Object.keys(totalByCategory).reduce((sum, cat) => {
      return sum + (correctByCategory[cat] || 0) / totalByCategory[cat];
    }, 0) / Object.keys(totalByCategory).length * 100;
  }
  calculateCategoryConsistency(interactions) {
    const categoryResponses = {};
    interactions.forEach((i) => {
      const cat = i.category || "unknown";
      if (!categoryResponses[cat]) categoryResponses[cat] = [];
      categoryResponses[cat].push(i.correct);
    });
    return Object.values(categoryResponses).reduce((sum, responses) => {
      const consistency = responses.filter((r) => r).length / responses.length;
      return sum + consistency;
    }, 0) / Object.keys(categoryResponses).length * 100;
  }
  calculateCategoricalFlexibility(interactions) {
    const uniqueCategories = new Set(interactions.map((i) => i.category));
    return uniqueCategories.size / Math.max(1, interactions.length) * 100;
  }
  calculateSemanticOrganization(interactions) {
    const semanticClusters = this.identifySemanticClusters(interactions);
    return semanticClusters.length / Math.max(1, interactions.length) * 100;
  }
  identifySemanticClusters(interactions) {
    const clusters = [];
    const processed = /* @__PURE__ */ new Set();
    interactions.forEach((interaction) => {
      if (processed.has(interaction.id)) return;
      const cluster = [interaction];
      processed.add(interaction.id);
      interactions.forEach((other) => {
        if (processed.has(other.id)) return;
        if (this.areSemanticallySimilar(interaction, other)) {
          cluster.push(other);
          processed.add(other.id);
        }
      });
      if (cluster.length > 1) {
        clusters.push(cluster);
      }
    });
    return clusters;
  }
  areSemanticallySimilar(interaction1, interaction2) {
    return interaction1.category === interaction2.category || interaction1.associationType === interaction2.associationType;
  }
  identifyAssociationTypes(interactions) {
    const types = {};
    interactions.forEach((i) => {
      const type = i.associationType || "unknown";
      types[type] = (types[type] || 0) + 1;
    });
    return types;
  }
  analyzeDifficultyProgression(interactions) {
    const difficulties = interactions.map((i) => i.difficulty || "medium");
    return {
      progression: difficulties,
      averageDifficulty: this.calculateAverageDifficulty(difficulties),
      adaptationSuccess: this.calculateAdaptationSuccess(interactions)
    };
  }
  calculateAverageDifficulty(difficulties) {
    const difficultyValues = { easy: 1, medium: 2, hard: 3 };
    const total = difficulties.reduce((sum, d) => sum + (difficultyValues[d] || 2), 0);
    return total / difficulties.length;
  }
  calculateAdaptationSuccess(interactions) {
    let adaptationSuccesses = 0;
    for (let i = 1; i < interactions.length; i++) {
      if (interactions[i - 1].correct === false && interactions[i].correct === true) {
        adaptationSuccesses++;
      }
    }
    return adaptationSuccesses / Math.max(1, interactions.length - 1) * 100;
  }
  identifyErrorPatterns(interactions) {
    const errors = interactions.filter((i) => !i.correct);
    return {
      totalErrors: errors.length,
      errorTypes: this.categorizeErrors(errors),
      errorFrequency: errors.length / Math.max(1, interactions.length) * 100
    };
  }
  categorizeErrors(errors) {
    const categories = {};
    errors.forEach((error) => {
      const category = error.errorType || "unknown";
      categories[category] = (categories[category] || 0) + 1;
    });
    return categories;
  }
  /**
   * Avalia a carga cognitiva do jogo
   */
  assessCognitiveLoad(gameData) {
    const { interactions = [], averageResponseTime = 0 } = gameData;
    if (interactions.length === 0) return "low";
    const avgTime = averageResponseTime || interactions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / interactions.length;
    const errorRate = interactions.filter((i) => !i.correct).length / interactions.length;
    if (avgTime > 5e3 || errorRate > 0.7) return "high";
    if (avgTime > 3e3 || errorRate > 0.4) return "medium";
    return "low";
  }
  /**
   * Identifica objetivos terapêuticos
   */
  identifyTherapeuticGoals(gameData) {
    const { interactions = [], accuracy = 0 } = gameData;
    const goals = [];
    if (accuracy < 60) {
      goals.push("Melhorar associação conceitual");
    }
    if (interactions.length > 0) {
      const avgTime = interactions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / interactions.length;
      if (avgTime > 4e3) {
        goals.push("Acelerar processamento semântico");
      }
    }
    if (interactions.filter((i) => !i.correct).length > interactions.length * 0.5) {
      goals.push("Fortalecer memória semântica");
    }
    return goals;
  }
  /**
   * Identifica necessidades de intervenção
   */
  identifyInterventionNeeds(gameData) {
    const { interactions = [], accuracy = 0 } = gameData;
    const needs = [];
    if (accuracy < 40) {
      needs.push("Intervenção intensiva em associação conceitual");
    }
    if (interactions.length > 0) {
      const errorPattern = this.identifyErrorPatterns(interactions);
      if (errorPattern.errorFrequency > 60) {
        needs.push("Treino específico em categorização");
      }
    }
    return needs;
  }
  /**
   * Gera marcadores de progresso
   */
  generateProgressMarkers(gameData) {
    const { interactions = [], accuracy = 0 } = gameData;
    return {
      accuracyTrend: this.calculateAccuracyTrend(interactions),
      speedImprovement: this.calculateSpeedImprovement(interactions),
      consistencyScore: this.calculateConsistencyScore(interactions),
      learningRate: this.calculateLearningRate(interactions)
    };
  }
  /**
   * Calcula tendência de acerto
   */
  calculateAccuracyTrend(interactions) {
    if (interactions.length < 2) return "insufficient_data";
    const halfPoint = Math.floor(interactions.length / 2);
    const firstHalf = interactions.slice(0, halfPoint);
    const secondHalf = interactions.slice(halfPoint);
    const firstAccuracy = firstHalf.filter((i) => i.correct).length / firstHalf.length;
    const secondAccuracy = secondHalf.filter((i) => i.correct).length / secondHalf.length;
    if (secondAccuracy > firstAccuracy + 0.1) return "improving";
    if (secondAccuracy < firstAccuracy - 0.1) return "declining";
    return "stable";
  }
  /**
   * Calcula melhoria de velocidade
   */
  calculateSpeedImprovement(interactions) {
    if (interactions.length < 2) return 0;
    const halfPoint = Math.floor(interactions.length / 2);
    const firstHalf = interactions.slice(0, halfPoint);
    const secondHalf = interactions.slice(halfPoint);
    const firstAvgTime = firstHalf.reduce((sum, i) => sum + (i.responseTime || 0), 0) / firstHalf.length;
    const secondAvgTime = secondHalf.reduce((sum, i) => sum + (i.responseTime || 0), 0) / secondHalf.length;
    return (firstAvgTime - secondAvgTime) / firstAvgTime * 100;
  }
  /**
   * Calcula score de consistência
   */
  calculateConsistencyScore(interactions) {
    if (interactions.length === 0) return 0;
    const responseTimes = interactions.map((i) => i.responseTime || 0);
    const avgTime = responseTimes.reduce((sum, t) => sum + t, 0) / responseTimes.length;
    const variance = responseTimes.reduce((sum, t) => sum + Math.pow(t - avgTime, 2), 0) / responseTimes.length;
    const stdDev = Math.sqrt(variance);
    return Math.max(0, 100 - stdDev / avgTime * 100);
  }
  /**
   * Calcula taxa de aprendizado
   */
  calculateLearningRate(interactions) {
    if (interactions.length < 3) return 0;
    let improvementCount = 0;
    for (let i = 1; i < interactions.length; i++) {
      if (interactions[i].correct && !interactions[i - 1].correct) {
        improvementCount++;
      }
    }
    return improvementCount / (interactions.length - 1) * 100;
  }
  /**
   * Gera análise terapêutica completa
   * @param {Object} metrics - Métricas processadas
   * @param {Object} gameData - Dados do jogo
   * @returns {Object} Análise terapêutica
   */
  generateTherapeuticAnalysis(metrics, gameData) {
    try {
      const analysis = {
        // Análise comportamental
        behavioral: {
          engagement: this.calculateEngagementScore(gameData),
          persistence: this.calculatePersistenceScore(gameData),
          adaptability: this.calculateAdaptabilityScore(gameData),
          frustrationTolerance: this.calculateFrustrationTolerance(gameData),
          socialInteraction: this.calculateSocialInteractionScore(gameData)
        },
        // Análise cognitiva específica para associação de imagens
        cognitive: {
          conceptualAssociation: metrics.conceptualAssociation || {},
          categoricalThinking: metrics.categoricalThinking || {},
          semanticProcessing: metrics.semanticResponseTime || {},
          associativeMemory: metrics.associationCognition || {}
        },
        // Análise de desenvolvimento
        developmental: {
          currentLevel: this.assessDevelopmentalLevel(metrics, gameData),
          progressMarkers: this.generateProgressMarkers(gameData),
          milestoneAchievements: this.identifyMilestoneAchievements(metrics, gameData),
          nextTargets: this.identifyNextDevelopmentalTargets(metrics, gameData)
        },
        // Recomendações terapêuticas
        recommendations: this.generateTherapeuticRecommendations(metrics, gameData),
        // Indicadores de progresso
        progressIndicators: this.generateProgressIndicators(metrics, gameData),
        // Insights específicos do jogo
        gameSpecificInsights: this.generateGameSpecificInsights(metrics, gameData),
        // Metadados
        metadata: {
          analysisTimestamp: (/* @__PURE__ */ new Date()).toISOString(),
          gameType: this.gameType,
          analysisVersion: "3.0.0",
          confidenceScore: this.calculateAnalysisConfidenceScore(metrics, gameData)
        }
      };
      return analysis;
    } catch (error) {
      this.logger?.error("❌ Erro ao gerar análise terapêutica:", error);
      return this.generateFallbackTherapeuticAnalysis(gameData);
    }
  }
  /**
   * Gera análise de fallback terapêutica
   */
  generateFallbackTherapeuticAnalysis(gameData) {
    return {
      behavioral: {
        engagement: 50,
        persistence: 50,
        adaptability: 50,
        frustrationTolerance: 50,
        socialInteraction: 50
      },
      cognitive: {
        conceptualAssociation: { accuracy: gameData.accuracy || 0 },
        categoricalThinking: { categoryAccuracy: 0 },
        semanticProcessing: { average: 0 },
        associativeMemory: { executiveFunction: 50 }
      },
      developmental: {
        currentLevel: "básico",
        progressMarkers: { accuracyTrend: "stable" },
        milestoneAchievements: [],
        nextTargets: ["Melhorar associação conceitual"]
      },
      recommendations: {
        immediate: ["Praticar associações simples"],
        shortTerm: ["Expandir vocabulário"],
        longTerm: ["Desenvolver pensamento categórico"]
      },
      progressIndicators: {
        overallProgress: 0.5,
        strengthAreas: [],
        developmentAreas: ["associação conceitual"]
      },
      gameSpecificInsights: {
        dominantStrategy: "tentativa_erro",
        learningPattern: "gradual",
        challengeAreas: ["categorização"]
      },
      metadata: {
        analysisTimestamp: (/* @__PURE__ */ new Date()).toISOString(),
        gameType: this.gameType,
        analysisVersion: "3.0.0-fallback",
        confidenceScore: 0.3
      }
    };
  }
  /**
   * Implementa análise de fallback
   */
  generateFallbackAnalysis(gameData) {
    return {
      therapeuticAnalysis: {
        cognitiveLoad: "medium",
        therapeuticGoals: ["Melhorar associação conceitual"],
        interventionNeeds: [],
        progressMarkers: {
          accuracyTrend: "stable",
          speedImprovement: 0,
          consistencyScore: 50,
          learningRate: 0
        }
      },
      processedMetrics: {
        accuracy: gameData.accuracy || 0,
        averageResponseTime: gameData.averageResponseTime || 0,
        totalInteractions: gameData.interactions?.length || 0,
        correctAssociations: gameData.interactions?.filter((i) => i.correct).length || 0,
        errorRate: gameData.interactions?.length > 0 ? gameData.interactions.filter((i) => !i.correct).length / gameData.interactions.length * 100 : 0,
        engagementScore: 75
      }
    };
  }
  /**
   * Calcula a persistência do jogador
   */
  calculatePersistence(interactions) {
    if (!interactions || interactions.length === 0) return 0;
    const attempts = interactions.filter((i) => i.type === "attempt");
    const consecutiveFailures = this.getConsecutiveFailures(attempts);
    return Math.max(0, 100 - consecutiveFailures * 10);
  }
  /**
   * Calcula a adaptabilidade do jogador
   */
  calculateAdaptability(interactions) {
    if (!interactions || interactions.length === 0) return 0;
    const strategies = this.identifyStrategies(interactions);
    return Math.min(100, strategies.length * 25);
  }
  /**
   * Calcula o pensamento estratégico
   */
  calculateStrategicThinking(interactions) {
    if (!interactions || interactions.length === 0) return 0;
    const planningIndicators = interactions.filter(
      (i) => i.type === "hover" || i.type === "preview"
    );
    return Math.min(100, planningIndicators.length / interactions.length * 100);
  }
  /**
   * Calcula a tolerância à frustração
   */
  calculateFrustrationTolerance(interactions) {
    if (!interactions || interactions.length === 0) return 50;
    const failures = interactions.filter((i) => i.success === false);
    const quitEarly = interactions.some((i) => i.type === "quit" && i.timestamp < 3e4);
    if (quitEarly) return 20;
    return Math.max(20, 100 - failures.length * 5);
  }
  /**
   * Identifica falhas consecutivas
   */
  getConsecutiveFailures(attempts) {
    let maxConsecutive = 0;
    let current = 0;
    for (const attempt of attempts) {
      if (attempt.success === false) {
        current++;
        maxConsecutive = Math.max(maxConsecutive, current);
      } else {
        current = 0;
      }
    }
    return maxConsecutive;
  }
  /**
   * Identifica estratégias utilizadas
   */
  identifyStrategies(interactions) {
    const strategies = /* @__PURE__ */ new Set();
    if (interactions.some((i) => i.type === "elimination")) {
      strategies.add("elimination");
    }
    if (interactions.some((i) => i.type === "grouping")) {
      strategies.add("grouping");
    }
    const systematicPattern = this.detectSystematicPattern(interactions);
    if (systematicPattern) {
      strategies.add("systematic");
    }
    return Array.from(strategies);
  }
  /**
   * Detecta padrão sistemático
   */
  detectSystematicPattern(interactions) {
    if (!interactions || interactions.length < 3) return false;
    const attempts = interactions.filter((i) => i.type === "attempt");
    const positions = attempts.map((a) => a.position || 0);
    const isLinear = positions.every((pos, i) => i === 0 || pos > positions[i - 1]);
    return isLinear && positions.length > 2;
  }
  /**
   * Calcula função executiva
   */
  calculateExecutiveFunction(interactions) {
    if (!interactions || interactions.length === 0) return 0;
    const planningActions = interactions.filter((i) => i.type === "planning");
    const selfCorrections = interactions.filter((i) => i.type === "correction");
    return Math.min(100, (planningActions.length + selfCorrections.length) * 10);
  }
  /**
   * Calcula memória de trabalho
   */
  calculateWorkingMemory(interactions) {
    if (!interactions || interactions.length === 0) return 0;
    const simultaneousActions = this.getSimultaneousActions(interactions);
    return Math.min(100, simultaneousActions * 20);
  }
  /**
   * Calcula velocidade de processamento
   */
  calculateProcessingSpeed(interactions) {
    if (!interactions || interactions.length === 0) return 0;
    const avgResponseTime = this.calculateAverageResponseTime(interactions);
    if (avgResponseTime < 1e3) return 100;
    if (avgResponseTime < 2e3) return 80;
    if (avgResponseTime < 3e3) return 60;
    if (avgResponseTime < 5e3) return 40;
    return 20;
  }
  /**
   * Calcula memória semântica
   */
  calculateSemanticMemory(interactions) {
    if (!interactions || interactions.length === 0) return 0;
    const correctAssociations = interactions.filter((i) => i.success === true);
    const totalAttempts = interactions.filter((i) => i.type === "attempt");
    if (totalAttempts.length === 0) return 0;
    return Math.round(correctAssociations.length / totalAttempts.length * 100);
  }
  /**
   * Conta ações simultâneas
   */
  getSimultaneousActions(interactions) {
    let count = 0;
    const timeWindow = 500;
    for (let i = 0; i < interactions.length - 1; i++) {
      const current = interactions[i];
      const next = interactions[i + 1];
      if (next.timestamp - current.timestamp < timeWindow) {
        count++;
      }
    }
    return count;
  }
  /**
   * Calcula tempo médio de resposta
   */
  calculateAverageResponseTime(interactions) {
    const responses = interactions.filter((i) => i.responseTime);
    if (responses.length === 0) return 0;
    const sum = responses.reduce((acc, r) => acc + r.responseTime, 0);
    return sum / responses.length;
  }
  /**
   * Calcula variabilidade em tempos de resposta
   */
  calculateVariability(responseTimes) {
    if (!responseTimes || responseTimes.length === 0) return 0;
    const avg = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const variance = responseTimes.reduce((sum, time) => sum + Math.pow(time - avg, 2), 0) / responseTimes.length;
    return Math.sqrt(variance);
  }
  // Métodos auxiliares que estão faltando
  calculateEngagementScore(gameData) {
    const { interactions = [], sessionDuration = 0 } = gameData;
    if (interactions.length === 0) return 50;
    const completionRate = interactions.length / Math.max(1, gameData.totalAttempts || interactions.length);
    const timeEngagement = sessionDuration > 0 ? Math.min(100, sessionDuration / 6e4 * 20) : 50;
    return Math.round(completionRate * 50 + timeEngagement * 0.5);
  }
  calculatePersistenceScore(gameData) {
    const { interactions = [] } = gameData;
    if (interactions.length === 0) return 50;
    const errorRecovery = this.calculateErrorRecovery(interactions);
    const completionRate = interactions.length / Math.max(1, gameData.totalAttempts || interactions.length);
    return Math.round(errorRecovery * 0.6 + completionRate * 40);
  }
  calculateAdaptabilityScore(gameData) {
    const { interactions = [] } = gameData;
    if (interactions.length < 2) return 50;
    const adaptationSuccess = this.calculateAdaptationSuccess(interactions);
    return Math.round(adaptationSuccess);
  }
  calculateSocialInteractionScore(gameData) {
    return 50;
  }
  calculateErrorRecovery(interactions) {
    let recoveries = 0;
    for (let i = 1; i < interactions.length; i++) {
      if (!interactions[i - 1].correct && interactions[i].correct) {
        recoveries++;
      }
    }
    return recoveries / Math.max(1, interactions.length - 1) * 100;
  }
  calculateContinuationAfterErrors(interactions) {
    let continuations = 0;
    let totalErrors = 0;
    for (let i = 0; i < interactions.length - 1; i++) {
      if (!interactions[i].correct) {
        totalErrors++;
        if (i + 1 < interactions.length) {
          continuations++;
        }
      }
    }
    return totalErrors > 0 ? continuations / totalErrors : 1;
  }
  assessDevelopmentalLevel(metrics, gameData) {
    const accuracy = gameData.accuracy || 0;
    if (accuracy >= 80) return "avançado";
    if (accuracy >= 60) return "intermediário";
    return "básico";
  }
  identifyMilestoneAchievements(metrics, gameData) {
    const achievements = [];
    const accuracy = gameData.accuracy || 0;
    if (accuracy >= 50) achievements.push("Associação básica");
    if (accuracy >= 70) achievements.push("Categorização eficiente");
    if (accuracy >= 90) achievements.push("Maestria conceitual");
    return achievements;
  }
  identifyNextDevelopmentalTargets(metrics, gameData) {
    const targets = [];
    const accuracy = gameData.accuracy || 0;
    if (accuracy < 50) targets.push("Desenvolver associação básica");
    else if (accuracy < 70) targets.push("Melhorar categorização");
    else if (accuracy < 90) targets.push("Refinar precisão conceitual");
    else targets.push("Manter excelência");
    return targets;
  }
  generateTherapeuticRecommendations(metrics, gameData) {
    return {
      immediate: ["Praticar associações simples"],
      shortTerm: ["Expandir vocabulário categórico"],
      longTerm: ["Desenvolver pensamento abstrato"]
    };
  }
  generateProgressIndicators(metrics, gameData) {
    return {
      overallProgress: (gameData.accuracy || 0) / 100,
      strengthAreas: this.identifyStrengthAreas(metrics),
      developmentAreas: this.identifyDevelopmentAreas(metrics)
    };
  }
  identifyDevelopmentAreas(metrics) {
    const areas = [];
    if (metrics.conceptualAssociation?.accuracy < 60) areas.push("Associação conceitual");
    if (metrics.categoricalThinking?.categoryAccuracy < 60) areas.push("Categorização");
    return areas;
  }
  generateGameSpecificInsights(metrics, gameData) {
    return {
      dominantStrategy: this.identifyDominantStrategy(gameData),
      learningPattern: this.identifyLearningPattern(gameData),
      challengeAreas: this.identifyDevelopmentAreas(metrics)
    };
  }
  identifyDominantStrategy(gameData) {
    const { interactions = [] } = gameData;
    if (interactions.length === 0) return "insufficient_data";
    const avgResponseTime = interactions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / interactions.length;
    if (avgResponseTime < 2e3) return "intuitivo";
    if (avgResponseTime > 5e3) return "analítico";
    return "equilibrado";
  }
  identifyLearningPattern(gameData) {
    const { interactions = [] } = gameData;
    if (interactions.length < 3) return "insufficient_data";
    const trend = this.calculateAccuracyTrend(interactions);
    if (trend === "improving") return "progressivo";
    if (trend === "declining") return "regressivo";
    return "estável";
  }
  calculateAnalysisConfidenceScore(metrics, gameData) {
    const { interactions = [] } = gameData;
    let confidence = 0.5;
    if (interactions.length >= 5) confidence += 0.2;
    if (interactions.length >= 10) confidence += 0.2;
    if (gameData.sessionDuration > 6e4) confidence += 0.1;
    return Math.min(1, confidence);
  }
  /**
   * Processa métricas para estrutura padronizada do banco de dados
   */
  processMetricsForDatabase(metrics, gameData) {
    return {
      // Métricas básicas
      accuracy: gameData.accuracy || 0,
      averageResponseTime: gameData.averageResponseTime || 0,
      totalInteractions: gameData.interactions?.length || 0,
      correctAssociations: gameData.interactions?.filter((i) => i.correct).length || 0,
      errorRate: gameData.interactions?.length > 0 ? gameData.interactions.filter((i) => !i.correct).length / gameData.interactions.length * 100 : 0,
      // Métricas específicas de associação
      conceptualAccuracy: metrics.conceptualAssociation?.accuracy || 0,
      categoryAccuracy: metrics.categoricalThinking?.categoryAccuracy || 0,
      semanticResponseTime: metrics.semanticResponseTime?.average || 0,
      // Indicadores comportamentais
      engagementScore: this.calculateEngagementScore(gameData),
      persistenceScore: this.calculatePersistenceScore(gameData),
      adaptabilityScore: this.calculateAdaptabilityScore(gameData),
      // Metadados
      sessionId: gameData.sessionId,
      userId: gameData.userId,
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      gameType: "ImageAssociation"
    };
  }
}
const gameStartContainer = "_gameStartContainer_spz1j_9";
const gameStartMain = "_gameStartMain_spz1j_22";
const gameStartContent = "_gameStartContent_spz1j_29";
const gameStartTitleSection = "_gameStartTitleSection_spz1j_36";
const gameIconLarge = "_gameIconLarge_spz1j_41";
const gameStartTitle = "_gameStartTitle_spz1j_36";
const gameStartSubtitle = "_gameStartSubtitle_spz1j_55";
const gameStartInstructionCard = "_gameStartInstructionCard_spz1j_64";
const gameStartCustomContent = "_gameStartCustomContent_spz1j_81";
const difficultySection = "_difficultySection_spz1j_91";
const difficultySectionTitle = "_difficultySectionTitle_spz1j_95";
const difficultyOptions = "_difficultyOptions_spz1j_103";
const difficultyCard = "_difficultyCard_spz1j_110";
const active$1 = "_active_spz1j_130";
const difficultyIcon = "_difficultyIcon_spz1j_154";
const difficultyTitle = "_difficultyTitle_spz1j_160";
const difficultyDescription = "_difficultyDescription_spz1j_167";
const difficultyPreview = "_difficultyPreview_spz1j_174";
const gameStartActions = "_gameStartActions_spz1j_193";
const gameStartPlayButton = "_gameStartPlayButton_spz1j_199";
const gameStartPlayIcon = "_gameStartPlayIcon_spz1j_222";
const gameStartFooter = "_gameStartFooter_spz1j_227";
const gameStartFooterText = "_gameStartFooterText_spz1j_232";
const highContrast = "_highContrast_spz1j_242";
const styles$1 = {
  gameStartContainer,
  gameStartMain,
  gameStartContent,
  gameStartTitleSection,
  gameIconLarge,
  gameStartTitle,
  gameStartSubtitle,
  gameStartInstructionCard,
  gameStartCustomContent,
  difficultySection,
  difficultySectionTitle,
  difficultyOptions,
  difficultyCard,
  active: active$1,
  difficultyIcon,
  difficultyTitle,
  difficultyDescription,
  difficultyPreview,
  gameStartActions,
  gameStartPlayButton,
  gameStartPlayIcon,
  gameStartFooter,
  gameStartFooterText,
  highContrast
};
var _jsxFileName$1 = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\components\\common\\GameStartScreen\\GameStartScreen.jsx";
const GameStartScreen = ({
  gameTitle: gameTitle2,
  gameSubtitle,
  gameDescription,
  gameInstruction,
  gameIcon = "🎮",
  difficulties = [{
    id: "easy",
    name: "Fácil",
    description: "Ideal para iniciantes",
    icon: "😊"
  }, {
    id: "medium",
    name: "Médio",
    description: "Desafio moderado",
    icon: "😐"
  }, {
    id: "hard",
    name: "Difícil",
    description: "Para especialistas",
    icon: "🧠"
  }],
  onStart,
  onBack,
  defaultDifficulty = "easy",
  showDifficultySelector = true,
  customContent
}) => {
  const {
    settings
  } = useAccessibilityContext?.() || {
    settings: {}
  };
  const [selectedDifficulty, setSelectedDifficulty] = reactExports.useState(defaultDifficulty);
  const handleStart = () => {
    if (onStart) {
      onStart(selectedDifficulty);
    }
  };
  const handleDifficultySelect = (difficultyId) => {
    setSelectedDifficulty(difficultyId);
  };
  return /* @__PURE__ */ React.createElement("div", { className: `${styles$1.gameStartContainer} ${settings?.highContrast ? styles$1.highContrast : ""}`, role: "dialog", "aria-labelledby": "game-start-title", __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 42,
    columnNumber: 5
  } }, /* @__PURE__ */ React.createElement("main", { className: styles$1.gameStartMain, __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 48,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("div", { className: styles$1.gameStartContent, __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 49,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("section", { className: styles$1.gameStartTitleSection, __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 51,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles$1.gameIconLarge, "aria-hidden": "true", __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 52,
    columnNumber: 13
  } }, gameIcon), /* @__PURE__ */ React.createElement("h1", { className: styles$1.gameStartTitle, id: "game-start-title", __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 53,
    columnNumber: 13
  } }, gameTitle2), gameSubtitle && /* @__PURE__ */ React.createElement("p", { className: styles$1.gameStartSubtitle, __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 55,
    columnNumber: 15
  } }, gameSubtitle)), (gameInstruction || gameDescription) && /* @__PURE__ */ React.createElement("div", { className: styles$1.gameStartInstructionCard, __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 61,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("p", { __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 62,
    columnNumber: 15
  } }, gameInstruction || gameDescription)), customContent && /* @__PURE__ */ React.createElement("div", { className: styles$1.gameStartCustomContent, __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 68,
    columnNumber: 13
  } }, customContent), showDifficultySelector && difficulties && difficulties.length > 0 && /* @__PURE__ */ React.createElement("section", { className: styles$1.difficultySection, __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 75,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("h2", { className: styles$1.difficultySectionTitle, __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 76,
    columnNumber: 15
  } }, "🎮 Escolha seu nível:"), /* @__PURE__ */ React.createElement("div", { className: styles$1.difficultyOptions, __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 78,
    columnNumber: 15
  } }, difficulties.map((difficulty) => /* @__PURE__ */ React.createElement("div", { key: difficulty.id, className: `${styles$1.difficultyCard} ${selectedDifficulty === difficulty.id ? styles$1.active : ""}`, onClick: () => handleDifficultySelect(difficulty.id), role: "button", tabIndex: 0, "aria-pressed": selectedDifficulty === difficulty.id, onKeyPress: (e) => {
    if (e.key === "Enter" || e.key === " ") {
      handleDifficultySelect(difficulty.id);
    }
  }, __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 80,
    columnNumber: 19
  } }, /* @__PURE__ */ React.createElement("div", { className: styles$1.difficultyIcon, __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 95,
    columnNumber: 21
  } }, difficulty.icon), /* @__PURE__ */ React.createElement("div", { className: styles$1.difficultyTitle, __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 98,
    columnNumber: 21
  } }, difficulty.name), /* @__PURE__ */ React.createElement("div", { className: styles$1.difficultyDescription, __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 101,
    columnNumber: 21
  } }, difficulty.description), difficulty.preview && /* @__PURE__ */ React.createElement("div", { className: styles$1.difficultyPreview, __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 105,
    columnNumber: 23
  } }, difficulty.preview))))), /* @__PURE__ */ React.createElement("div", { className: styles$1.gameStartActions, __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 116,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("button", { className: styles$1.gameStartPlayButton, onClick: handleStart, "aria-label": `Iniciar ${gameTitle2} na dificuldade ${difficulties.find((d) => d.id === selectedDifficulty)?.name || selectedDifficulty}`, __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 117,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: styles$1.gameStartPlayIcon, "aria-hidden": "true", __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 124,
    columnNumber: 15
  } }, "🎮"), /* @__PURE__ */ React.createElement("span", { __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 125,
    columnNumber: 15
  } }, "Começar a Jogar!"))), /* @__PURE__ */ React.createElement("div", { className: styles$1.gameStartFooter, __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 130,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("p", { className: styles$1.gameStartFooterText, __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 131,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { "aria-hidden": "true", __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 132,
    columnNumber: 15
  } }, "💡"), " Dica: Use fones de ouvido para uma melhor experiência!")))));
};
GameStartScreen.propTypes = {
  gameTitle: PropTypes.string.isRequired,
  gameSubtitle: PropTypes.string,
  gameDescription: PropTypes.string,
  gameInstruction: PropTypes.string,
  gameIcon: PropTypes.string,
  difficulties: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    description: PropTypes.string,
    icon: PropTypes.string
  })),
  onStart: PropTypes.func.isRequired,
  onBack: PropTypes.func,
  defaultDifficulty: PropTypes.string,
  showDifficultySelector: PropTypes.bool,
  customContent: PropTypes.node
};
const ImageAssociationConfig = {
  // Associações lógicas expandidas por categorias e dificuldades
  associations: [
    {
      phase: 1,
      difficulty: "EASY",
      category: "animais-básicos",
      main: { emoji: "🐶", label: "Cachorro" },
      correct: { emoji: "🦴", label: "Osso" },
      explanation: "Cachorros adoram roer ossos!",
      options: [
        { emoji: "🦴", label: "Osso" },
        { emoji: "🐱", label: "Gato" },
        { emoji: "🌸", label: "Flor" },
        { emoji: "🚗", label: "Carro" }
      ]
    },
    {
      phase: 2,
      difficulty: "EASY",
      category: "natureza-básica",
      main: { emoji: "🐟", label: "Peixe" },
      correct: { emoji: "💧", label: "Água" },
      explanation: "Peixes vivem na água!",
      options: [
        { emoji: "🔥", label: "Fogo" },
        { emoji: "💧", label: "Água" },
        { emoji: "🍕", label: "Pizza" },
        { emoji: "✈️", label: "Avião" }
      ]
    },
    {
      phase: 3,
      difficulty: "EASY",
      category: "alimentos-origem",
      main: { emoji: "🥛", label: "Leite" },
      correct: { emoji: "🐄", label: "Vaca" },
      explanation: "O leite vem da vaca!",
      options: [
        { emoji: "🐄", label: "Vaca" },
        { emoji: "🐧", label: "Pinguim" },
        { emoji: "🎈", label: "Balão" },
        { emoji: "🍎", label: "Maçã" }
      ]
    },
    {
      phase: 4,
      difficulty: "MEDIUM",
      category: "insetos-plantas",
      main: { emoji: "🐝", label: "Abelha" },
      correct: { emoji: "🌸", label: "Flor" },
      explanation: "Abelhas coletam néctar das flores!",
      options: [
        { emoji: "🌸", label: "Flor" },
        { emoji: "🐟", label: "Peixe" },
        { emoji: "🏠", label: "Casa" },
        { emoji: "⚽", label: "Bola" }
      ]
    },
    {
      phase: 5,
      difficulty: "MEDIUM",
      category: "corpo-função",
      main: { emoji: "👁️", label: "Olho" },
      correct: { emoji: "👓", label: "Óculos" },
      explanation: "Óculos ajudam os olhos a enxergar melhor!",
      options: [
        { emoji: "👓", label: "Óculos" },
        { emoji: "🦷", label: "Dente" },
        { emoji: "🎧", label: "Fone" },
        { emoji: "🧠", label: "Cérebro" }
      ]
    },
    {
      phase: 6,
      difficulty: "MEDIUM",
      category: "profissões-ferramentas",
      main: { emoji: "👨‍⚕️", label: "Médico" },
      correct: { emoji: "🩺", label: "Estetoscópio" },
      explanation: "Médicos usam estetoscópio para auscultar!",
      options: [
        { emoji: "🩺", label: "Estetoscópio" },
        { emoji: "🔨", label: "Martelo" },
        { emoji: "📚", label: "Livro" },
        { emoji: "🎨", label: "Pincel" }
      ]
    },
    {
      phase: 7,
      difficulty: "MEDIUM",
      category: "tempo-ação",
      main: { emoji: "🌙", label: "Noite" },
      correct: { emoji: "😴", label: "Dormir" },
      explanation: "À noite é hora de dormir!",
      options: [
        { emoji: "😴", label: "Dormir" },
        { emoji: "🏃", label: "Correr" },
        { emoji: "🍽️", label: "Comer" },
        { emoji: "📖", label: "Estudar" }
      ]
    },
    {
      phase: 8,
      difficulty: "HARD",
      category: "emoções-expressões",
      main: { emoji: "😢", label: "Tristeza" },
      correct: { emoji: "🤗", label: "Abraço" },
      explanation: "Um abraço pode consolar a tristeza!",
      options: [
        { emoji: "🤗", label: "Abraço" },
        { emoji: "🎉", label: "Festa" },
        { emoji: "⚽", label: "Futebol" },
        { emoji: "🍰", label: "Bolo" }
      ]
    },
    {
      phase: 9,
      difficulty: "HARD",
      category: "causas-efeitos",
      main: { emoji: "🌧️", label: "Chuva" },
      correct: { emoji: "☂️", label: "Guarda-chuva" },
      explanation: "Usamos guarda-chuva para nos proteger da chuva!",
      options: [
        { emoji: "☂️", label: "Guarda-chuva" },
        { emoji: "🕶️", label: "Óculos de sol" },
        { emoji: "🏖️", label: "Praia" },
        { emoji: "🔥", label: "Fogo" }
      ]
    },
    {
      phase: 10,
      difficulty: "HARD",
      category: "elementos-opostos",
      main: { emoji: "☀️", label: "Sol" },
      correct: { emoji: "🌙", label: "Lua" },
      explanation: "Sol e Lua são opostos que se complementam!",
      options: [
        { emoji: "🌙", label: "Lua" },
        { emoji: "⭐", label: "Estrela" },
        { emoji: "🌈", label: "Arco-íris" },
        { emoji: "☁️", label: "Nuvem" }
      ]
    }
  ]
};
const ImageAssociationMetrics = {
  cognitiveEngine: new CognitiveAssociationEngine(),
  sessionAttempts: [],
  // Registrar início do jogo
  startGame: (difficulty) => {
    const metrics = {
      gameId: "image-association",
      sessionId: `association_${Date.now()}`,
      startTime: (/* @__PURE__ */ new Date()).toISOString(),
      difficulty,
      userId: "anonymous",
      // Será substituído por ID real
      device: navigator.userAgent
    };
    (void 0).sessionAttempts = [];
    console.log("Image Association Game Started:", metrics);
    return metrics;
  },
  // Registrar tentativa de associação
  recordAssociationAttempt: (attempt) => {
    const metrics = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      phase: attempt.phase,
      difficulty: attempt.difficulty,
      category: attempt.category,
      mainItem: attempt.mainItem,
      correctAnswer: attempt.correctAnswer,
      userAnswer: attempt.userAnswer,
      isCorrect: attempt.isCorrect,
      responseTime: attempt.responseTime,
      attempts: attempt.attempts,
      // tentativas nesta fase
      explanation: attempt.explanation,
      targetColor: attempt.targetColor || "unknown"
    };
    (void 0).sessionAttempts.push(metrics);
    if ((void 0).sessionAttempts.length >= 5) {
      const cognitiveAnalysis = (void 0).analyzeCognitivePatterns();
      console.log("🧠 Análise Cognitiva:", cognitiveAnalysis);
    }
    console.log("Image Association Attempt:", metrics);
    return metrics;
  },
  // Registrar visualização de explicação
  recordExplanationView: (explanationData) => {
    const metrics = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      action: "explanation_viewed",
      phase: explanationData.phase,
      category: explanationData.category,
      explanation: explanationData.explanation,
      timeSpent: explanationData.timeSpent
    };
    console.log("Image Association Explanation Viewed:", metrics);
    return metrics;
  },
  // Registrar progressão de fase
  recordPhaseProgression: (progressData) => {
    const metrics = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      action: "phase_completed",
      completedPhase: progressData.completedPhase,
      nextPhase: progressData.nextPhase,
      difficulty: progressData.difficulty,
      attemptsInPhase: progressData.attemptsInPhase,
      successInPhase: progressData.successInPhase
    };
    console.log("Image Association Phase Progression:", metrics);
    return metrics;
  },
  // Registrar final do jogo
  endGame: (gameData) => {
    const metrics = {
      sessionId: gameData.sessionId,
      endTime: (/* @__PURE__ */ new Date()).toISOString(),
      totalTime: gameData.totalTime,
      totalPhases: gameData.totalPhases,
      completedPhases: gameData.completedPhases,
      totalScore: gameData.totalScore,
      accuracy: gameData.accuracy,
      averageResponseTime: gameData.averageResponseTime,
      difficulty: gameData.difficulty,
      finalLevel: gameData.finalLevel,
      starsEarned: gameData.starsEarned,
      categoriesExplored: gameData.categoriesExplored,
      completed: gameData.completed
    };
    const finalAnalysis = (void 0).analyzeCognitivePatterns();
    if (finalAnalysis) {
      console.log("🎯 ANÁLISE FINAL COGNITIVA:", finalAnalysis);
      metrics.cognitiveAnalysis = finalAnalysis;
    }
    console.log("Image Association Game Ended:", metrics);
    return metrics;
  },
  // Calcular estatísticas do jogo
  calculateStats: (attempts) => {
    if (!attempts || attempts.length === 0) {
      return {
        accuracy: 0,
        averageTime: 0,
        totalAttempts: 0,
        correctAssociations: 0
      };
    }
    const correctAttempts = attempts.filter((attempt) => attempt.isCorrect);
    const totalTime = attempts.reduce((sum, attempt) => sum + attempt.responseTime, 0);
    return {
      accuracy: correctAttempts.length / attempts.length * 100,
      averageTime: totalTime / attempts.length,
      totalAttempts: attempts.length,
      correctAssociations: correctAttempts.length,
      phasesCompleted: [...new Set(attempts.map((a) => a.phase))].length,
      categoriesMastered: calculateCategoriesMastered(attempts),
      difficultyProgression: calculateDifficultyProgression(attempts),
      conceptualGrowth: calculateConceptualGrowth(attempts)
    };
  },
  // Analisar padrões de associação
  analyzeAssociationPatterns: (attempts) => {
    const errorsByCategory = attempts.filter((attempt) => !attempt.isCorrect).reduce((acc, error) => {
      acc[error.category] = (acc[error.category] || 0) + 1;
      return acc;
    }, {});
    const errorsByDifficulty = attempts.filter((attempt) => !attempt.isCorrect).reduce((acc, error) => {
      acc[error.difficulty] = (acc[error.difficulty] || 0) + 1;
      return acc;
    }, {});
    const categoryAccuracy = {};
    const categoriesAttempted = [...new Set(attempts.map((a) => a.category))];
    categoriesAttempted.forEach((category) => {
      const categoryAttempts = attempts.filter((a) => a.category === category);
      const categoryCorrect = categoryAttempts.filter((a) => a.isCorrect).length;
      categoryAccuracy[category] = categoryCorrect / categoryAttempts.length * 100;
    });
    return {
      errorsByCategory,
      errorsByDifficulty,
      categoryAccuracy,
      strongestCategories: getStrongestCategories(categoryAccuracy),
      weakestCategories: getWeakestCategories(categoryAccuracy),
      suggestions: generateAssociationSuggestions(attempts, errorsByCategory, categoryAccuracy)
    };
  },
  // ATIVAÇÃO: Análise cognitiva profunda
  analyzeCognitivePatterns: () => {
    if ((void 0).sessionAttempts.length === 0) return null;
    const gameMetrics = {
      attempts: (void 0).sessionAttempts.map((attempt) => ({
        targetColor: attempt.targetColor,
        isCorrect: attempt.isCorrect,
        responseTime: attempt.responseTime,
        timestamp: new Date(attempt.timestamp).getTime(),
        pattern: attempt.category
      }))
    };
    try {
      const colorAnalysis = (void 0).cognitiveEngine.analyzeColorAccuracyPattern(gameMetrics);
      const neuropsychProfile = (void 0).cognitiveEngine.analyzeNeuropsychologicalPattern(gameMetrics);
      const fatiguePattern = (void 0).cognitiveEngine.analyzeFatigueAlgorithm(gameMetrics);
      const anomalies = (void 0).cognitiveEngine.detectAnomalousPatterns(gameMetrics);
      return {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        cognitiveProfile: colorAnalysis.cognitiveProfile,
        neuralProfile: neuropsychProfile,
        fatigueAnalysis: fatiguePattern,
        anomalies,
        insights: colorAnalysis.insights,
        totalAttempts: (void 0).sessionAttempts.length
      };
    } catch (error) {
      console.error("❌ Erro na análise cognitiva:", error);
      return null;
    }
  }
};
function calculateCategoriesMastered(attempts) {
  const categoryStats = {};
  attempts.forEach((attempt) => {
    if (!categoryStats[attempt.category]) {
      categoryStats[attempt.category] = { correct: 0, total: 0 };
    }
    categoryStats[attempt.category].total++;
    if (attempt.isCorrect) {
      categoryStats[attempt.category].correct++;
    }
  });
  const masteredCategories = Object.entries(categoryStats).filter(([category, stats]) => {
    const accuracy = stats.correct / stats.total;
    return accuracy >= 0.8 && stats.total >= 2;
  }).map(([category]) => category);
  return {
    total: Object.keys(categoryStats).length,
    mastered: masteredCategories.length,
    masteredList: masteredCategories
  };
}
function calculateDifficultyProgression(attempts) {
  const difficulties = attempts.map((a) => a.difficulty);
  return {
    EASY: difficulties.filter((d) => d === "EASY").length,
    MEDIUM: difficulties.filter((d) => d === "MEDIUM").length,
    HARD: difficulties.filter((d) => d === "HARD").length
  };
}
function calculateConceptualGrowth(attempts) {
  if (attempts.length < 6) return 0;
  const firstThird = attempts.slice(0, Math.floor(attempts.length / 3));
  const lastThird = attempts.slice(-Math.floor(attempts.length / 3));
  const firstAccuracy = firstThird.filter((a) => a.isCorrect).length / firstThird.length;
  const lastAccuracy = lastThird.filter((a) => a.isCorrect).length / lastThird.length;
  return (lastAccuracy - firstAccuracy) / firstAccuracy * 100;
}
function getStrongestCategories(categoryAccuracy) {
  return Object.entries(categoryAccuracy).filter(([, accuracy]) => accuracy >= 80).sort(([, a], [, b]) => b - a).slice(0, 3).map(([category, accuracy]) => ({ category, accuracy }));
}
function getWeakestCategories(categoryAccuracy) {
  return Object.entries(categoryAccuracy).filter(([, accuracy]) => accuracy < 60).sort(([, a], [, b]) => a - b).slice(0, 3).map(([category, accuracy]) => ({ category, accuracy }));
}
function generateAssociationSuggestions(attempts, errorsByCategory, categoryAccuracy) {
  const suggestions = [];
  if (attempts.length === 0) {
    suggestions.push("Comece jogando para receber sugestões personalizadas!");
    return suggestions;
  }
  const accuracy = attempts.filter((a) => a.isCorrect).length / attempts.length;
  if (accuracy < 0.5) {
    suggestions.push("Comece com associações básicas e pense na função ou relação direta");
    suggestions.push("Observe bem as características de cada item antes de escolher");
  } else if (accuracy < 0.7) {
    suggestions.push("Você está progredindo! Tente pensar em como os itens se relacionam");
    suggestions.push("Considere diferentes tipos de relação: função, origem, ambiente, uso");
  } else if (accuracy > 0.9) {
    suggestions.push("Excelente raciocínio! Tente níveis mais difíceis para novos desafios");
    suggestions.push("Você domina as associações! Explore categorias mais complexas");
  }
  const problemCategories = Object.entries(errorsByCategory).sort(([, a], [, b]) => b - a).slice(0, 2).map(([category]) => category);
  if (problemCategories.length > 0) {
    suggestions.push(`Foque mais atenção nas categorias: ${problemCategories.join(", ")}`);
  }
  const strongCategories = getStrongestCategories(categoryAccuracy);
  if (strongCategories.length > 0) {
    suggestions.push(`Você se destaca em: ${strongCategories.map((c) => c.category).join(", ")}`);
  }
  return suggestions.length > 0 ? suggestions : ["Continue praticando para desenvolver seu raciocínio lógico!"];
}
const imageAssociationGame = "_imageAssociationGame_9wjam_41";
const gameContent = "_gameContent_9wjam_85";
const gameHeader = "_gameHeader_9wjam_105";
const gameTitle = "_gameTitle_9wjam_133";
const gameStats = "_gameStats_9wjam_179";
const statCard = "_statCard_9wjam_193";
const statValue = "_statValue_9wjam_237";
const statLabel = "_statLabel_9wjam_251";
const questionArea = "_questionArea_9wjam_265";
const questionTitle = "_questionTitle_9wjam_285";
const objectsDisplay = "_objectsDisplay_9wjam_299";
const answerOptions = "_answerOptions_9wjam_375";
const answerButton = "_answerButton_9wjam_391";
const correct = "_correct_9wjam_441";
const incorrect = "_incorrect_9wjam_453";
const gameControls = "_gameControls_9wjam_501";
const controlButton = "_controlButton_9wjam_517";
const headerTtsButton = "_headerTtsButton_9wjam_639";
const ttsActive = "_ttsActive_9wjam_695";
const activityMenu = "_activityMenu_9wjam_815";
const activityButton = "_activityButton_9wjam_831";
const active = "_active_9wjam_869";
const questionHeader = "_questionHeader_9wjam_955";
const styles = {
  imageAssociationGame,
  gameContent,
  gameHeader,
  gameTitle,
  gameStats,
  statCard,
  statValue,
  statLabel,
  questionArea,
  questionTitle,
  objectsDisplay,
  answerOptions,
  answerButton,
  correct,
  incorrect,
  gameControls,
  controlButton,
  headerTtsButton,
  ttsActive,
  activityMenu,
  activityButton,
  active,
  questionHeader
};
var _jsxFileName = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\games\\ImageAssociation\\ImageAssociationGame.jsx";
const getRoundsPerDifficulty = (difficulty) => {
  switch (difficulty) {
    case "EASY":
      return 4;
    // Mínimo para iniciantes
    case "MEDIUM":
      return 6;
    // Balanceado
    case "HARD":
      return 7;
    // Máximo para desafio
    default:
      return 4;
  }
};
const ACTIVITY_TYPES = {
  BASIC_ASSOCIATION: {
    id: "basic_association",
    name: "Associação",
    icon: "🔗",
    description: "Teste de associação conceitual simples",
    cognitiveFunction: "conceptual_association",
    component: "BasicAssociationActivity"
  },
  CATEGORY_SORTING: {
    id: "category_sorting",
    name: "Classificação",
    icon: "📂",
    description: "Teste de categorização e organização mental",
    cognitiveFunction: "categorical_thinking_organization",
    component: "CategorySortingActivity"
  },
  VISUAL_SEQUENCE: {
    id: "visual_sequence",
    name: "Sequência",
    icon: "📝",
    description: "Teste de raciocínio sequencial e lógica visual",
    cognitiveFunction: "sequential_reasoning_visual_logic",
    component: "VisualSequenceActivity"
  },
  EMOTION_MATCHING: {
    id: "emotion_matching",
    name: "Emoções",
    icon: "😊",
    description: "Teste de reconhecimento e correspondência emocional",
    cognitiveFunction: "emotional_recognition_matching",
    component: "EmotionMatchingActivity"
  },
  CONTEXT_REASONING: {
    id: "context_reasoning",
    name: "Contextos",
    icon: "🏠",
    description: "Teste de compreensão contextual e inferência",
    cognitiveFunction: "contextual_reasoning_inference",
    component: "ContextReasoningActivity"
  }
};
const ImageAssociationGame = ({
  onBack
}) => {
  const {
    user
  } = reactExports.useContext(SystemContext);
  const {
    settings
  } = useAccessibilityContext();
  const [ttsActive2, setTtsActive] = reactExports.useState(true);
  const [ttsEnabled, setTtsEnabled] = reactExports.useState(true);
  const [isSpeaking, setIsSpeaking] = reactExports.useState(false);
  const [currentSpeechButton, setCurrentSpeechButton] = reactExports.useState(null);
  const [showStartScreen, setShowStartScreen] = reactExports.useState(true);
  const [gameStarted, setGameStarted] = reactExports.useState(false);
  const [currentAssociation, setCurrentAssociation] = reactExports.useState(null);
  const [selectedOption, setSelectedOption] = reactExports.useState(null);
  const [feedback, setFeedback] = reactExports.useState(null);
  const [score, setScore] = reactExports.useState(0);
  const [level, setLevel] = reactExports.useState(1);
  const [stars, setStars] = reactExports.useState(0);
  const [successes, setSuccesses] = reactExports.useState(0);
  const [attempts, setAttempts] = reactExports.useState(0);
  const [currentPhase, setCurrentPhase] = reactExports.useState(1);
  const [gameStartTime, setGameStartTime] = reactExports.useState(null);
  const [difficulty, setDifficulty] = reactExports.useState("EASY");
  const metricsRef = reactExports.useRef(null);
  const [gameState, setGameState] = reactExports.useState({
    status: "start",
    // 'start', 'playing', 'paused', 'finished'
    score: 0,
    round: 1,
    totalRounds: 10,
    difficulty: "EASY",
    accuracy: 100,
    roundStartTime: null,
    // 🎯 Sistema de atividades redesenhado (5 atividades distintas)
    currentActivity: ACTIVITY_TYPES.BASIC_ASSOCIATION.id,
    activityCycle: [ACTIVITY_TYPES.BASIC_ASSOCIATION.id, ACTIVITY_TYPES.CATEGORY_SORTING.id, ACTIVITY_TYPES.VISUAL_SEQUENCE.id, ACTIVITY_TYPES.EMOTION_MATCHING.id, ACTIVITY_TYPES.CONTEXT_REASONING.id],
    activityIndex: 0,
    roundsPerActivity: getRoundsPerDifficulty("EASY"),
    // 4-7 baseado na dificuldade
    activityRoundCount: 0,
    activitiesCompleted: [],
    // 🎯 Dados específicos de atividades
    activityData: {
      basicAssociation: {
        currentPair: null,
        options: [],
        selectedOption: null,
        usedAssociations: []
        // Controle para evitar repetições
      },
      categoryMatching: {
        categories: [],
        currentCategory: null,
        placedItems: []
      },
      sequenceAssociation: {
        sequence: [],
        userProgress: [],
        missingIndex: null
      },
      emotionRecognition: {
        currentEmotion: null,
        faceOptions: [],
        selectedFace: null
      },
      contextAssociation: {
        context: null,
        objects: [],
        correctObjects: []
      },
      memoryAssociation: {
        pairs: [],
        flippedCards: [],
        matchedPairs: []
      }
    },
    // 🎯 Feedback e animações
    showFeedback: false,
    feedbackType: null,
    // 'success', 'error', 'hint'
    feedbackMessage: "",
    showCelebration: false,
    // 🎯 Métricas comportamentais
    responseTime: 0,
    hesitationCount: 0,
    helpUsed: false,
    consecutiveCorrect: 0,
    totalAttempts: 0,
    correctAttempts: 0
  });
  const [collectorsHub] = reactExports.useState(() => {
    if (window.globalSystemInstance?.gameSpecificProcessors?.gameCollectors?.ImageAssociation?.hub) {
      console.log("🖼️ Reutilizando instância existente do ImageAssociation CollectorsHub");
      return window.globalSystemInstance.gameSpecificProcessors.gameCollectors.ImageAssociation.hub;
    }
    console.log("🖼️ Criando nova instância do ImageAssociation CollectorsHub");
    return new ImageAssociationCollectorsHub();
  });
  const {
    startUnifiedSession,
    recordInteraction,
    updateMetrics,
    sessionId
  } = useUnifiedGameLogic("image_association");
  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    finalizeSession: finalizeMultisensory
  } = useMultisensoryIntegration(sessionId, {
    learningStyle: user?.profile?.learningStyle || "visual"
  });
  const {
    setUserContext: setTherapeuticContext
  } = useTherapeuticOrchestrator({
    userId: user?.id
  });
  reactExports.useEffect(() => {
    if (metricsRef.current && recordInteraction && updateMetrics) {
      console.log("🖼️ ImageAssociation: Métricas inicializadas e backend conectado");
    }
  }, [recordInteraction, updateMetrics]);
  const speak = reactExports.useCallback((text, options = {}) => {
    if (!ttsActive2 || !("speechSynthesis" in window)) {
      return;
    }
    window.speechSynthesis.cancel();
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = "pt-BR";
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    window.speechSynthesis.speak(utterance);
  }, [ttsActive2]);
  reactExports.useCallback((text, options = {}) => {
    if (!ttsActive2) {
      console.log("🔇 TTS desativado - não reproduzindo áudio");
      return;
    }
    if (!ttsEnabled || !("speechSynthesis" in window)) {
      console.warn("Text-to-Speech não está habilitado ou não é suportado neste navegador");
      return;
    }
    window.speechSynthesis.cancel();
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = options.lang || "pt-BR";
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    setIsSpeaking(true);
    if (options.buttonId) {
      setCurrentSpeechButton(options.buttonId);
    }
    utterance.onstart = () => {
      setIsSpeaking(true);
    };
    utterance.onend = () => {
      setIsSpeaking(false);
      setCurrentSpeechButton(null);
      if (options.onEnd) {
        options.onEnd();
      }
    };
    utterance.onerror = () => {
      setIsSpeaking(false);
      setCurrentSpeechButton(null);
    };
    window.speechSynthesis.speak(utterance);
  }, [ttsActive2, ttsEnabled]);
  reactExports.useCallback(() => {
    const explanation = `
      Bem-vindo ao jogo Associação de Imagens! 
      Este é um jogo onde você precisa conectar imagens que fazem sentido juntas.
      
      Como jogar:
      Observe atentamente a imagem da esquerda.
      Depois, escolha qual das opções à direita combina melhor com ela.
      Use sua lógica e conhecimento para fazer as associações corretas.
      
      Quanto mais associações corretas você fizer, mais pontos ganhará!
      
      Boa sorte e divirta-se desenvolvendo sua capacidade de associação!
    `;
    speak(explanation, {
      rate: 0.8,
      buttonId: "explain",
      onEnd: () => console.log("Explicação do jogo concluída")
    });
  }, [speak]);
  const generateBasicAssociation = reactExports.useCallback(() => {
    console.log("🔗 Generating basic association activity");
    const associations = [{
      main: {
        emoji: "🐶",
        label: "Cachorro"
      },
      options: [{
        emoji: "🦴",
        label: "Osso"
      }, {
        emoji: "🐱",
        label: "Gato"
      }, {
        emoji: "🚗",
        label: "Carro"
      }],
      correct: {
        emoji: "🦴",
        label: "Osso"
      },
      explanation: "Cachorro come osso"
    }, {
      main: {
        emoji: "🌧️",
        label: "Chuva"
      },
      options: [{
        emoji: "☀️",
        label: "Sol"
      }, {
        emoji: "☂️",
        label: "Guarda-chuva"
      }, {
        emoji: "🏠",
        label: "Casa"
      }],
      correct: {
        emoji: "☂️",
        label: "Guarda-chuva"
      },
      explanation: "Chuva precisa de guarda-chuva"
    }, {
      main: {
        emoji: "🔑",
        label: "Chave"
      },
      options: [{
        emoji: "🚪",
        label: "Porta"
      }, {
        emoji: "🍎",
        label: "Maçã"
      }, {
        emoji: "📱",
        label: "Celular"
      }],
      correct: {
        emoji: "🚪",
        label: "Porta"
      },
      explanation: "Chave abre porta"
    }, {
      main: {
        emoji: "🐱",
        label: "Gato"
      },
      options: [{
        emoji: "🥛",
        label: "Leite"
      }, {
        emoji: "🏀",
        label: "Bola"
      }, {
        emoji: "📚",
        label: "Livro"
      }],
      correct: {
        emoji: "🥛",
        label: "Leite"
      },
      explanation: "Gato bebe leite"
    }, {
      main: {
        emoji: "🌞",
        label: "Sol"
      },
      options: [{
        emoji: "🕶️",
        label: "Óculos de sol"
      }, {
        emoji: "🧊",
        label: "Gelo"
      }, {
        emoji: "📖",
        label: "Livro"
      }],
      correct: {
        emoji: "🕶️",
        label: "Óculos de sol"
      },
      explanation: "Sol precisa de óculos de sol"
    }, {
      main: {
        emoji: "🐝",
        label: "Abelha"
      },
      options: [{
        emoji: "🌸",
        label: "Flor"
      }, {
        emoji: "🚗",
        label: "Carro"
      }, {
        emoji: "📺",
        label: "TV"
      }],
      correct: {
        emoji: "🌸",
        label: "Flor"
      },
      explanation: "Abelha coleta néctar da flor"
    }, {
      main: {
        emoji: "🐟",
        label: "Peixe"
      },
      options: [{
        emoji: "💧",
        label: "Água"
      }, {
        emoji: "🔥",
        label: "Fogo"
      }, {
        emoji: "🎈",
        label: "Balão"
      }],
      correct: {
        emoji: "💧",
        label: "Água"
      },
      explanation: "Peixe vive na água"
    }, {
      main: {
        emoji: "🦷",
        label: "Dente"
      },
      options: [{
        emoji: "🪥",
        label: "Escova"
      }, {
        emoji: "👓",
        label: "Óculos"
      }, {
        emoji: "🎪",
        label: "Circo"
      }],
      correct: {
        emoji: "🪥",
        label: "Escova"
      },
      explanation: "Dente precisa de escova para limpeza"
    }];
    const usedAssociations = gameState.activityData?.basicAssociation?.usedAssociations || [];
    const availableAssociations = associations.filter((assoc) => !usedAssociations.some((used) => used.main.label === assoc.main.label));
    const associationsToUse = availableAssociations.length > 0 ? availableAssociations : associations;
    const randomAssociation = associationsToUse[Math.floor(Math.random() * associationsToUse.length)];
    setCurrentAssociation(randomAssociation);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        basicAssociation: {
          currentPair: randomAssociation,
          selectedOption: null,
          showExplanation: false,
          usedAssociations: availableAssociations.length > 0 ? [...usedAssociations, randomAssociation] : [randomAssociation]
          // Reset se necessário
        }
      }
    }));
    speak(`Atividade: Associação Básica. O que se relaciona com ${randomAssociation.main.label}?`);
  }, [speak]);
  const generateCategorySorting = reactExports.useCallback(() => {
    console.log("📂 Generating category sorting activity");
    const categories = {
      "EASY": [{
        name: "Animais",
        emoji: "🐾",
        items: [{
          emoji: "🐶",
          label: "Cachorro",
          category: "animals"
        }, {
          emoji: "🐱",
          label: "Gato",
          category: "animals"
        }, {
          emoji: "🐸",
          label: "Sapo",
          category: "animals"
        }],
        distractors: [{
          emoji: "🚗",
          label: "Carro",
          category: "vehicles"
        }, {
          emoji: "🍎",
          label: "Maçã",
          category: "food"
        }]
      }, {
        name: "Frutas",
        emoji: "🍎",
        items: [{
          emoji: "🍎",
          label: "Maçã",
          category: "fruits"
        }, {
          emoji: "🍌",
          label: "Banana",
          category: "fruits"
        }, {
          emoji: "🍊",
          label: "Laranja",
          category: "fruits"
        }],
        distractors: [{
          emoji: "🚗",
          label: "Carro",
          category: "vehicles"
        }, {
          emoji: "🐶",
          label: "Cachorro",
          category: "animals"
        }]
      }, {
        name: "Veículos",
        emoji: "🚗",
        items: [{
          emoji: "🚗",
          label: "Carro",
          category: "vehicles"
        }, {
          emoji: "✈️",
          label: "Avião",
          category: "vehicles"
        }, {
          emoji: "🚲",
          label: "Bicicleta",
          category: "vehicles"
        }],
        distractors: [{
          emoji: "🍎",
          label: "Maçã",
          category: "fruits"
        }, {
          emoji: "🐱",
          label: "Gato",
          category: "animals"
        }]
      }, {
        name: "Objetos",
        emoji: "📚",
        items: [{
          emoji: "📚",
          label: "Livro",
          category: "objects"
        }, {
          emoji: "⚽",
          label: "Bola",
          category: "objects"
        }, {
          emoji: "🎈",
          label: "Balão",
          category: "objects"
        }],
        distractors: [{
          emoji: "🐶",
          label: "Cachorro",
          category: "animals"
        }, {
          emoji: "🍌",
          label: "Banana",
          category: "fruits"
        }]
      }, {
        name: "Cores",
        emoji: "🌈",
        items: [{
          emoji: "🔴",
          label: "Vermelho",
          category: "colors"
        }, {
          emoji: "🔵",
          label: "Azul",
          category: "colors"
        }, {
          emoji: "🟡",
          label: "Amarelo",
          category: "colors"
        }],
        distractors: [{
          emoji: "🚗",
          label: "Carro",
          category: "vehicles"
        }, {
          emoji: "🐸",
          label: "Sapo",
          category: "animals"
        }]
      }, {
        name: "Casa",
        emoji: "🏠",
        items: [{
          emoji: "🪑",
          label: "Cadeira",
          category: "house"
        }, {
          emoji: "🛏️",
          label: "Cama",
          category: "house"
        }, {
          emoji: "🚪",
          label: "Porta",
          category: "house"
        }],
        distractors: [{
          emoji: "🍊",
          label: "Laranja",
          category: "fruits"
        }, {
          emoji: "✈️",
          label: "Avião",
          category: "vehicles"
        }]
      }, {
        name: "Comida",
        emoji: "🍕",
        items: [{
          emoji: "🍕",
          label: "Pizza",
          category: "food"
        }, {
          emoji: "🍰",
          label: "Bolo",
          category: "food"
        }, {
          emoji: "🥪",
          label: "Sanduíche",
          category: "food"
        }],
        distractors: [{
          emoji: "🐱",
          label: "Gato",
          category: "animals"
        }, {
          emoji: "🚲",
          label: "Bicicleta",
          category: "vehicles"
        }]
      }]
    };
    const selectedCategory = categories["EASY"][Math.floor(Math.random() * categories["EASY"].length)];
    const allItems = [...selectedCategory.items, ...selectedCategory.distractors];
    const shuffledItems = allItems.sort(() => Math.random() - 0.5);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        categorySorting: {
          targetCategory: selectedCategory,
          allItems: shuffledItems,
          sortedItems: [],
          correctItems: selectedCategory.items
        }
      }
    }));
    speak(`Atividade: Classificação por Categoria. ${selectedCategory.name}.`);
  }, [speak]);
  const generateVisualSequence = reactExports.useCallback(() => {
    console.log("📝 Generating visual sequence activity");
    const sequencePatterns = {
      "size_progression": {
        name: "Progressão de Tamanho",
        sequence: ["🔴", "🟠", "🟡"],
        options: ["🟢", "🔵", "🟣"],
        correct: "🟢",
        explanation: "Sequência de cores do arco-íris"
      },
      "shape_alternation": {
        name: "Alternância de Formas",
        sequence: ["⭐", "🔺", "⭐"],
        options: ["🔺", "⭐", "🔴"],
        correct: "🔺",
        explanation: "Padrão alternado: estrela, triângulo, estrela..."
      },
      "number_sequence": {
        name: "Sequência Numérica",
        sequence: ["1️⃣", "2️⃣", "3️⃣"],
        options: ["4️⃣", "1️⃣", "5️⃣"],
        correct: "4️⃣",
        explanation: "Sequência numérica crescente"
      },
      "animal_size": {
        name: "Tamanho de Animais",
        sequence: ["🐭", "🐱", "🐶"],
        options: ["🐄", "🐰", "🐭"],
        correct: "🐄",
        explanation: "Sequência crescente de tamanho dos animais"
      },
      "geometric_shapes": {
        name: "Formas Geométricas",
        sequence: ["🔴", "🔺", "⬜"],
        options: ["⬛", "🟢", "🔺"],
        correct: "⬛",
        explanation: "Padrão de formas: círculo, triângulo, quadrado..."
      },
      "moon_phases": {
        name: "Fases da Lua",
        sequence: ["🌑", "🌓", "🌕"],
        options: ["🌗", "🌑", "⭐"],
        correct: "🌗",
        explanation: "Sequência das fases lunares"
      },
      "fruit_colors": {
        name: "Cores de Frutas",
        sequence: ["🍎", "🍊", "🍌"],
        options: ["🥝", "🍇", "🍎"],
        correct: "🥝",
        explanation: "Sequência de frutas por cor: vermelho, laranja, amarelo, verde"
      },
      "weather_sequence": {
        name: "Sequência Climática",
        sequence: ["☀️", "⛅", "☁️"],
        options: ["🌧️", "☀️", "❄️"],
        correct: "🌧️",
        explanation: "Progressão do tempo: sol, nuvens poucas, nuvens, chuva"
      }
    };
    const patternKeys = Object.keys(sequencePatterns);
    const selectedPattern = sequencePatterns[patternKeys[Math.floor(Math.random() * patternKeys.length)]];
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        visualSequence: {
          pattern: selectedPattern,
          userAnswer: null,
          showPattern: true
        }
      }
    }));
    speak(`Atividade: Sequência Visual.`);
  }, [speak]);
  const generateEmotionMatching = reactExports.useCallback(() => {
    console.log("😊 Generating emotion matching activity");
    const emotionPairs = [{
      situation: {
        emoji: "🌞",
        label: "Dia Ensolarado"
      },
      emotions: [{
        emoji: "😊",
        label: "Feliz",
        correct: true
      }, {
        emoji: "😢",
        label: "Triste",
        correct: false
      }, {
        emoji: "😴",
        label: "Sonolento",
        correct: false
      }],
      explanation: "Dias ensolarados deixam as pessoas felizes"
    }, {
      situation: {
        emoji: "🐶",
        label: "Brincar com Cachorro"
      },
      emotions: [{
        emoji: "😊",
        label: "Feliz",
        correct: true
      }, {
        emoji: "😨",
        label: "Assustado",
        correct: false
      }, {
        emoji: "😴",
        label: "Sonolento",
        correct: false
      }],
      explanation: "Brincar com cachorros é divertido e deixa feliz"
    }, {
      situation: {
        emoji: "📚",
        label: "Ler um Livro"
      },
      emotions: [{
        emoji: "🤔",
        label: "Pensativo",
        correct: true
      }, {
        emoji: "😡",
        label: "Bravo",
        correct: false
      }, {
        emoji: "😨",
        label: "Assustado",
        correct: false
      }],
      explanation: "Ler livros nos deixa pensativos e concentrados"
    }, {
      situation: {
        emoji: "🍦",
        label: "Comer Sorvete"
      },
      emotions: [{
        emoji: "😋",
        label: "Deliciado",
        correct: true
      }, {
        emoji: "😢",
        label: "Triste",
        correct: false
      }, {
        emoji: "😡",
        label: "Bravo",
        correct: false
      }],
      explanation: "Sorvete é saboroso e nos deixa satisfeitos"
    }, {
      situation: {
        emoji: "🎵",
        label: "Ouvir Música"
      },
      emotions: [{
        emoji: "😌",
        label: "Relaxado",
        correct: true
      }, {
        emoji: "😨",
        label: "Assustado",
        correct: false
      }, {
        emoji: "😡",
        label: "Bravo",
        correct: false
      }],
      explanation: "A música nos acalma e relaxa"
    }, {
      situation: {
        emoji: "🌧️",
        label: "Dia Chuvoso"
      },
      emotions: [{
        emoji: "😔",
        label: "Melancólico",
        correct: true
      }, {
        emoji: "😄",
        label: "Animado",
        correct: false
      }, {
        emoji: "😋",
        label: "Deliciado",
        correct: false
      }],
      explanation: "Dias chuvosos podem trazer melancolia"
    }, {
      situation: {
        emoji: "🎂",
        label: "Festa de Aniversário"
      },
      emotions: [{
        emoji: "🥳",
        label: "Festeiro",
        correct: true
      }, {
        emoji: "😢",
        label: "Triste",
        correct: false
      }, {
        emoji: "😴",
        label: "Sonolento",
        correct: false
      }],
      explanation: "Festas de aniversário são alegres e animadas"
    }, {
      situation: {
        emoji: "🤗",
        label: "Abraçar a Família"
      },
      emotions: [{
        emoji: "🥰",
        label: "Amoroso",
        correct: true
      }, {
        emoji: "😨",
        label: "Assustado",
        correct: false
      }, {
        emoji: "😡",
        label: "Bravo",
        correct: false
      }],
      explanation: "Abraços familiares transmitem amor e carinho"
    }, {
      situation: {
        emoji: "👻",
        label: "Ver Filme de Terror"
      },
      emotions: [{
        emoji: "😨",
        label: "Assustado",
        correct: true
      }, {
        emoji: "😊",
        label: "Feliz",
        correct: false
      }, {
        emoji: "😴",
        label: "Sonolento",
        correct: false
      }],
      explanation: "Filmes de terror causam medo e tensão"
    }];
    const selectedPair = emotionPairs[Math.floor(Math.random() * emotionPairs.length)];
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        emotionMatching: {
          situation: selectedPair.situation,
          emotions: selectedPair.emotions,
          userAnswer: null,
          explanation: selectedPair.explanation
        }
      }
    }));
    speak(`Atividade: Correspondência Emocional. ${selectedPair.situation.label}`);
  }, [speak]);
  const generateContextReasoning = reactExports.useCallback(() => {
    console.log("🏠 Generating context reasoning activity");
    const contextScenarios = [{
      context: {
        emoji: "🏥",
        label: "Hospital"
      },
      items: [{
        emoji: "👩‍⚕️",
        label: "Médica",
        belongs: true
      }, {
        emoji: "🩺",
        label: "Estetoscópio",
        belongs: true
      }, {
        emoji: "🍕",
        label: "Pizza",
        belongs: false
      }, {
        emoji: "🏖️",
        label: "Praia",
        belongs: false
      }]
    }, {
      context: {
        emoji: "🏫",
        label: "Escola"
      },
      items: [{
        emoji: "👩‍🏫",
        label: "Professora",
        belongs: true
      }, {
        emoji: "📚",
        label: "Livros",
        belongs: true
      }, {
        emoji: "🚗",
        label: "Carro",
        belongs: false
      }, {
        emoji: "🐠",
        label: "Peixe",
        belongs: false
      }]
    }, {
      context: {
        emoji: "🍽️",
        label: "Restaurante"
      },
      items: [{
        emoji: "👨‍🍳",
        label: "Chef",
        belongs: true
      }, {
        emoji: "🍽️",
        label: "Pratos",
        belongs: true
      }, {
        emoji: "🛏️",
        label: "Cama",
        belongs: false
      }, {
        emoji: "🌙",
        label: "Lua",
        belongs: false
      }]
    }, {
      context: {
        emoji: "🏖️",
        label: "Praia"
      },
      items: [{
        emoji: "🏄‍♂️",
        label: "Surfista",
        belongs: true
      }, {
        emoji: "⛱️",
        label: "Guarda-sol",
        belongs: true
      }, {
        emoji: "❄️",
        label: "Neve",
        belongs: false
      }, {
        emoji: "📚",
        label: "Livros",
        belongs: false
      }]
    }, {
      context: {
        emoji: "🚗",
        label: "Carro"
      },
      items: [{
        emoji: "🔑",
        label: "Chave",
        belongs: true
      }, {
        emoji: "⚙️",
        label: "Motor",
        belongs: true
      }, {
        emoji: "🐟",
        label: "Peixe",
        belongs: false
      }, {
        emoji: "🌸",
        label: "Flor",
        belongs: false
      }]
    }, {
      context: {
        emoji: "🍳",
        label: "Cozinha"
      },
      items: [{
        emoji: "🔥",
        label: "Fogão",
        belongs: true
      }, {
        emoji: "🥄",
        label: "Colher",
        belongs: true
      }, {
        emoji: "🛏️",
        label: "Cama",
        belongs: false
      }, {
        emoji: "📺",
        label: "TV",
        belongs: false
      }]
    }, {
      context: {
        emoji: "🌳",
        label: "Parque"
      },
      items: [{
        emoji: "🌸",
        label: "Flores",
        belongs: true
      }, {
        emoji: "🦋",
        label: "Borboletas",
        belongs: true
      }, {
        emoji: "🚗",
        label: "Carro",
        belongs: false
      }, {
        emoji: "💻",
        label: "Computador",
        belongs: false
      }]
    }, {
      context: {
        emoji: "🛏️",
        label: "Quarto"
      },
      items: [{
        emoji: "💤",
        label: "Travesseiro",
        belongs: true
      }, {
        emoji: "👕",
        label: "Roupas",
        belongs: true
      }, {
        emoji: "🌊",
        label: "Ondas",
        belongs: false
      }, {
        emoji: "🚁",
        label: "Helicóptero",
        belongs: false
      }]
    }];
    const selectedScenario = contextScenarios[Math.floor(Math.random() * contextScenarios.length)];
    const shuffledItems = selectedScenario.items.sort(() => Math.random() - 0.5);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        contextReasoning: {
          context: selectedScenario.context,
          items: shuffledItems,
          selectedItems: [],
          correctItems: selectedScenario.items.filter((item) => item.belongs)
        }
      }
    }));
    speak(`Atividade: Raciocínio Contextual. ${selectedScenario.context.label}`);
  }, [speak]);
  reactExports.useEffect(() => {
    if (!metricsRef.current) {
      metricsRef.current = ImageAssociationMetrics;
    }
  }, []);
  reactExports.useEffect(() => {
    if (gameState.status === "playing" && gameState.currentActivity) {
      switch (gameState.currentActivity) {
        case ACTIVITY_TYPES.BASIC_ASSOCIATION.id:
          generateBasicAssociation();
          break;
        case ACTIVITY_TYPES.CATEGORY_SORTING.id:
          generateCategorySorting();
          break;
        case ACTIVITY_TYPES.VISUAL_SEQUENCE.id:
          generateVisualSequence();
          break;
        case ACTIVITY_TYPES.EMOTION_MATCHING.id:
          generateEmotionMatching();
          break;
        case ACTIVITY_TYPES.CONTEXT_REASONING.id:
          generateContextReasoning();
          break;
        default:
          generateBasicAssociation();
      }
    }
  }, [gameState.status, gameState.currentActivity, generateBasicAssociation, generateCategorySorting, generateVisualSequence, generateEmotionMatching, generateContextReasoning]);
  const generateNewRound = reactExports.useCallback(() => {
    const currentActivity = gameState.currentActivity;
    setGameState((prev) => ({
      ...prev,
      roundStartTime: Date.now(),
      showFeedback: false,
      feedbackType: null,
      feedbackMessage: ""
    }));
    switch (currentActivity) {
      case ACTIVITY_TYPES.BASIC_ASSOCIATION.id:
        generateBasicAssociation();
        break;
      case ACTIVITY_TYPES.CATEGORY_SORTING.id:
        generateCategorySorting();
        break;
      case ACTIVITY_TYPES.VISUAL_SEQUENCE.id:
        generateVisualSequence();
        break;
      case ACTIVITY_TYPES.EMOTION_MATCHING.id:
        generateEmotionMatching();
        break;
      case ACTIVITY_TYPES.CONTEXT_REASONING.id:
        generateContextReasoning();
        break;
      default:
        generateBasicAssociation();
    }
  }, [gameState.currentActivity, generateBasicAssociation, generateCategorySorting, generateVisualSequence, generateEmotionMatching, generateContextReasoning]);
  const startGame = reactExports.useCallback(async (selectedDifficulty) => {
    try {
      setShowStartScreen(false);
      setGameStarted(true);
      setGameState((prev) => ({
        ...prev,
        status: "playing",
        difficulty: selectedDifficulty,
        roundsPerActivity: getRoundsPerDifficulty(selectedDifficulty),
        // Atualizar rodadas baseado na dificuldade
        roundStartTime: Date.now()
      }));
      if (startUnifiedSession) {
        await startUnifiedSession({
          gameType: "image_association",
          difficulty: selectedDifficulty,
          userId: user?.id || "anonymous"
        });
      }
      await initMultisensory();
      if (user?.id && user.id !== "anonymous" && user.id !== "") {
        setTherapeuticContext(user.id);
      }
      generateNewRound();
      speak("Jogo iniciado! Vamos começar com associação de imagens.");
    } catch (error) {
      console.error("Erro ao iniciar jogo:", error);
    }
  }, [startUnifiedSession, initMultisensory, setTherapeuticContext, user, speak, generateNewRound]);
  const [cognitiveAnalysisVisible, setCognitiveAnalysisVisible] = reactExports.useState(false);
  const [analysisResults, setAnalysisResults] = reactExports.useState(null);
  const [attemptCount, setAttemptCount] = reactExports.useState(0);
  const getAccuracy = reactExports.useCallback(() => {
    return attempts > 0 ? Math.round(successes / attempts * 100) : 0;
  }, [attempts, successes]);
  reactExports.useCallback(async () => {
    try {
      if (collectorsHub && !collectorsHub.initialized) {
        await collectorsHub.initialize();
        console.log("🎯 ImageAssociation: Coletores inicializados");
      }
    } catch (error) {
      console.warn("⚠️ ImageAssociation: Erro ao inicializar coletores:", error);
    }
  }, [collectorsHub]);
  const recordAssociationInteraction = reactExports.useCallback(async (selectedOption2, isCorrect, reactionTime) => {
    try {
      if (!collectorsHub || !currentAssociation) return;
      const gameData = {
        sessionId: `imageassoc_${Date.now()}`,
        phase: currentPhase,
        category: currentAssociation.category || "association",
        mainItem: currentAssociation.main,
        correctAnswer: currentAssociation.correct,
        userAnswer: selectedOption2,
        isCorrect,
        responseTime: reactionTime,
        difficulty: difficulty.toLowerCase(),
        options: currentAssociation.options,
        explanation: currentAssociation.explanation,
        timestamp: Date.now()
      };
      await collectorsHub.collectGameEvent(gameData);
      await recordMultisensoryInteraction("game_interaction", {
        interactionType: "user_action",
        gameSpecificData: gameData,
        multisensoryProcessing: {
          visualProcessing: {
            visualRecognition: 0.7,
            visualMemory: 0.7,
            visualAttention: 0.7
          },
          cognitiveProcessing: {
            associativeMemory: 0.7,
            processingSpeed: 0.7,
            adaptability: 0.7
          },
          behavioralProcessing: {
            interactionCount: 0.7,
            averageResponseTime: 0.7,
            consistency: 0.7
          }
        }
      });
      console.log("📊 ImageAssociation: Dados coletados:", gameData);
    } catch (error) {
      console.warn("⚠️ ImageAssociation: Erro ao registrar interação:", error);
    }
  }, [collectorsHub, currentAssociation, recordMultisensoryInteraction, difficulty]);
  const loadCurrentPhase = reactExports.useCallback(() => {
    const availableAssociations = ImageAssociationConfig.associations.filter((a) => a.difficulty === difficulty);
    const association = availableAssociations.find((a) => a.phase === currentPhase);
    if (association) {
      setCurrentAssociation(association);
      setSelectedOption(null);
      setFeedback(null);
    } else {
      setFeedback({
        type: "success",
        message: `🎉 Parabéns! Você completou todas as fases da dificuldade ${difficulty}! 🏆`
      });
    }
  }, [difficulty, currentPhase]);
  const nextPhase = reactExports.useCallback(() => {
    const availableAssociations = ImageAssociationConfig.associations.filter((a) => a.difficulty === difficulty);
    const currentIndex = availableAssociations.findIndex((a) => a.phase === currentPhase);
    if (currentIndex >= 0 && currentIndex < availableAssociations.length - 1) {
      const nextAssociation = availableAssociations[currentIndex + 1];
      setCurrentPhase(nextAssociation.phase);
    } else {
      setFeedback({
        type: "success",
        message: `🎉 Parabéns! Você completou todas as fases da dificuldade ${difficulty}! 🏆`
      });
      speak(`Parabéns! Você completou todas as fases da dificuldade ${difficulty}! Excelente trabalho!`, {
        rate: 1,
        onEnd: () => console.log("Feedback de conclusão anunciado")
      });
      setTimeout(() => {
        setShowStartScreen(true);
        setGameStarted(false);
      }, 3e3);
      return;
    }
    setTimeout(() => {
      loadCurrentPhase();
    }, 100);
  }, [difficulty, currentPhase, speak, loadCurrentPhase]);
  const switchToActivity = reactExports.useCallback((activityId) => {
    console.log("🔄 Switching to activity:", activityId);
    setGameState((prev) => ({
      ...prev,
      currentActivity: activityId,
      activityData: {
        ...prev.activityData,
        [activityId]: {}
        // Reset da atividade
      }
    }));
    switch (activityId) {
      case ACTIVITY_TYPES.BASIC_ASSOCIATION.id:
        generateBasicAssociation();
        break;
      case ACTIVITY_TYPES.CATEGORY_SORTING.id:
        generateCategorySorting();
        break;
      case ACTIVITY_TYPES.VISUAL_SEQUENCE.id:
        generateVisualSequence();
        break;
      case ACTIVITY_TYPES.EMOTION_MATCHING.id:
        generateEmotionMatching();
        break;
      case ACTIVITY_TYPES.CONTEXT_REASONING.id:
        generateContextReasoning();
        break;
      default:
        generateBasicAssociation();
    }
    const activity = Object.values(ACTIVITY_TYPES).find((a) => a.id === activityId);
    if (activity && speak) {
      speak(`Atividade alterada para: ${activity.name}. ${activity.description}`, {
        rate: 0.8
      });
    }
  }, [generateBasicAssociation, generateCategorySorting, generateVisualSequence, generateEmotionMatching, generateContextReasoning, speak]);
  const handleSuccess = reactExports.useCallback(async () => {
    setSuccesses((prev) => prev + 1);
    const points = difficulty === "EASY" ? 10 : difficulty === "MEDIUM" ? 15 : 20;
    setScore((prev) => prev + points);
    const newLevel = Math.floor(score / 100) + 1;
    setLevel(newLevel);
    setStars(Math.min(3, Math.floor(getAccuracy() / 30)));
    const successMessages = ["Muito bem! Excelente associação! 🎉", "Perfeito! Você entendeu a relação! ⭐", "Fantástico! Continue assim! 🚀", "Brilhante! Sua lógica está ótima! 💡"];
    const message = successMessages[Math.floor(Math.random() * successMessages.length)];
    setFeedback({
      type: "success",
      message: `${message} +${points} pontos!`
    });
    speak(`${message} Você ganhou ${points} pontos!`, {
      rate: 1.1,
      onEnd: () => console.log("Feedback de sucesso anunciado")
    });
    setTimeout(() => {
      nextPhase();
    }, 2e3);
  }, [difficulty, score, getAccuracy, speak, nextPhase]);
  const handleError = reactExports.useCallback(() => {
    setFeedback({
      type: "error",
      message: "Pense na relação entre os elementos! Tente novamente! 🤔"
    });
    speak("Ops! Pense na relação entre os elementos. Tente novamente!", {
      rate: 0.9,
      onEnd: () => console.log("Feedback de erro anunciado")
    });
    setTimeout(() => {
      setSelectedOption(null);
      setFeedback(null);
    }, 2e3);
  }, [speak]);
  const handleOptionSelect = reactExports.useCallback(async (option) => {
    if (selectedOption) return;
    const startTime = Date.now();
    setSelectedOption(option);
    setAttempts((prev) => prev + 1);
    const isCorrect = option.emoji === currentAssociation.correct.emoji;
    const reactionTime = Date.now() - startTime;
    await recordAssociationInteraction(option, isCorrect, reactionTime);
    if (isCorrect) {
      setTimeout(() => {
        handleSuccess();
      }, 1e3);
    } else {
      handleError();
    }
  }, [selectedOption, currentAssociation, recordAssociationInteraction, handleSuccess, handleError]);
  reactExports.useCallback(() => {
    setShowStartScreen(true);
    setGameStarted(false);
    setCurrentAssociation(null);
    setSelectedOption(null);
    setFeedback(null);
    setScore(0);
    setLevel(1);
    setStars(0);
    setSuccesses(0);
    setAttempts(0);
    setCurrentPhase(1);
    setAttemptCount(0);
    setCognitiveAnalysisVisible(false);
    setAnalysisResults(null);
  }, []);
  reactExports.useEffect(() => {
    if (gameStarted && currentPhase) {
      loadCurrentPhase();
    }
  }, [currentPhase, gameStarted, difficulty, loadCurrentPhase]);
  const finalizeMultisensorySession = reactExports.useCallback(async () => {
    try {
      const multisensoryReport = await finalizeMultisensory({
        finalScore: score,
        finalAccuracy: attempts > 0 ? successes / attempts : 0,
        totalInteractions: attempts,
        sessionDuration: Date.now() - gameStartTime,
        difficulty
      });
      console.log("🔄 ImageAssociation: Relatório multissensorial final:", multisensoryReport);
    } catch (error) {
      console.warn("⚠️ ImageAssociation: Erro ao finalizar sessão multissensorial:", error);
    }
  }, [finalizeMultisensory, score, successes, attempts, gameStartTime, difficulty]);
  reactExports.useEffect(() => {
    if (gameStarted && level > 10) {
      finalizeMultisensorySession();
    }
  }, [gameStarted, level, finalizeMultisensorySession]);
  reactExports.useEffect(() => {
    return () => {
      if ("speechSynthesis" in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);
  const toggleTTS = reactExports.useCallback(() => {
    setTtsActive((prev) => {
      const newState = !prev;
      console.log(`🔊 TTS ${newState ? "ativado" : "desativado"}`);
      if (!newState && "speechSynthesis" in window) {
        window.speechSynthesis.cancel();
        setIsSpeaking(false);
        setCurrentSpeechButton(null);
      }
      localStorage.setItem("imageAssociationTTS", newState.toString());
      return newState;
    });
  }, []);
  reactExports.useEffect(() => {
    const savedTTSState = localStorage.getItem("imageAssociationTTS");
    if (savedTTSState !== null) {
      setTtsActive(savedTTSState === "true");
    }
  }, []);
  reactExports.useEffect(() => {
    return () => {
      if ("speechSynthesis" in window) {
        window.speechSynthesis.cancel();
      }
      console.log("🔇 TTS parado ao sair do jogo Associação de Imagens");
    };
  }, []);
  reactExports.useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden && "speechSynthesis" in window) {
        window.speechSynthesis.cancel();
        setIsSpeaking(false);
        setCurrentSpeechButton(null);
      }
    };
    const handlePageHide = () => {
      if ("speechSynthesis" in window) {
        window.speechSynthesis.cancel();
      }
    };
    document.addEventListener("visibilitychange", handleVisibilityChange);
    window.addEventListener("pagehide", handlePageHide);
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("pagehide", handlePageHide);
      if ("speechSynthesis" in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);
  const renderBasicAssociation = () => {
    gameState.activityData?.basicAssociation || {};
    return /* @__PURE__ */ React.createElement("div", { className: styles.questionArea, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1364,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.questionHeader, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1365,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("h2", { className: styles.questionTitle, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1366,
      columnNumber: 11
    } }, "🔗 Teste de Associação Conceitual")), currentAssociation && /* @__PURE__ */ React.createElement(React.Fragment, null, /* @__PURE__ */ React.createElement("div", { className: styles.objectsDisplay, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1372,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      gap: "2rem",
      padding: "2rem"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1373,
      columnNumber: 15
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      textAlign: "center",
      padding: "2rem",
      border: "3px solid rgba(33, 150, 243, 0.6)",
      borderRadius: "20px",
      backgroundColor: "rgba(33, 150, 243, 0.2)",
      minWidth: "200px",
      boxShadow: "0 0 20px rgba(33, 150, 243, 0.3)"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1381,
      columnNumber: 17
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "4rem",
      marginBottom: "1rem"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1390,
      columnNumber: 19
    } }, currentAssociation.main.emoji), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1.5rem",
      fontWeight: "bold",
      color: "white"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1393,
      columnNumber: 19
    } }, currentAssociation.main.label)), /* @__PURE__ */ React.createElement("div", { style: {
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      gap: "0.5rem"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1399,
      columnNumber: 17
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "3rem",
      animation: "bounce 2s infinite",
      color: "rgba(255, 193, 7, 0.8)"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1405,
      columnNumber: 19
    } }, "⬇️"), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1rem",
      color: "rgba(255,255,255,0.8)",
      fontWeight: "bold"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1412,
      columnNumber: 19
    } }, "SE RELACIONA COM")))), /* @__PURE__ */ React.createElement("div", { className: styles.answerOptions, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1424,
      columnNumber: 13
    } }, currentAssociation.options.map((option, index) => {
      const isSelected = selectedOption === option;
      const isCorrect = option.emoji === currentAssociation.correct.emoji;
      return /* @__PURE__ */ React.createElement("button", { key: index, className: `${styles.answerButton} ${isSelected ? isCorrect ? styles.correct : styles.incorrect : ""}`, onClick: () => handleOptionSelect(option), disabled: selectedOption !== null, "aria-label": `Escolher ${option.label}`, style: {
        border: isSelected ? isCorrect ? "3px solid #4CAF50" : "3px solid #f44336" : "2px solid rgba(255,255,255,0.3)",
        backgroundColor: isSelected ? isCorrect ? "rgba(76, 175, 80, 0.3)" : "rgba(244, 67, 54, 0.3)" : "var(--card-background)",
        transform: isSelected ? "scale(1.05)" : "scale(1)",
        boxShadow: isSelected ? isCorrect ? "0 0 15px rgba(76, 175, 80, 0.5)" : "0 0 15px rgba(244, 67, 54, 0.5)" : "none",
        flexDirection: "column"
      }, __self: void 0, __source: {
        fileName: _jsxFileName,
        lineNumber: 1430,
        columnNumber: 19
      } }, /* @__PURE__ */ React.createElement("div", { style: {
        fontSize: "2.5rem",
        marginBottom: "0.5rem"
      }, __self: void 0, __source: {
        fileName: _jsxFileName,
        lineNumber: 1452,
        columnNumber: 21
      } }, option.emoji), /* @__PURE__ */ React.createElement("div", { style: {
        fontSize: "1rem",
        fontWeight: "bold"
      }, __self: void 0, __source: {
        fileName: _jsxFileName,
        lineNumber: 1455,
        columnNumber: 21
      } }, option.label), isSelected && /* @__PURE__ */ React.createElement("div", { style: {
        position: "absolute",
        top: "10px",
        right: "10px",
        fontSize: "1.5rem",
        fontWeight: "bold"
      }, __self: void 0, __source: {
        fileName: _jsxFileName,
        lineNumber: 1460,
        columnNumber: 23
      } }, isCorrect ? "✅" : "❌"));
    }))));
  };
  const renderCategorySorting = () => {
    const activityData = gameState.activityData?.categorySorting || {};
    return /* @__PURE__ */ React.createElement("div", { className: styles.questionArea, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1485,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.questionHeader, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1486,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("h2", { className: styles.questionTitle, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1487,
      columnNumber: 11
    } }, "📂 Teste de Categorização Mental")), activityData.targetCategory && /* @__PURE__ */ React.createElement(React.Fragment, null, /* @__PURE__ */ React.createElement("div", { className: styles.objectsDisplay, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1493,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      gap: "2rem"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1494,
      columnNumber: 15
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      textAlign: "center",
      padding: "2rem",
      border: "3px solid rgba(156, 39, 176, 0.6)",
      borderRadius: "20px",
      backgroundColor: "rgba(156, 39, 176, 0.2)",
      minWidth: "300px"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1501,
      columnNumber: 17
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "4rem",
      marginBottom: "1rem"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1509,
      columnNumber: 19
    } }, activityData.targetCategory.emoji), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1.5rem",
      fontWeight: "bold",
      color: "white"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1512,
      columnNumber: 19
    } }, "Categoria: ", activityData.targetCategory.name)), /* @__PURE__ */ React.createElement("div", { style: {
      border: "2px dashed rgba(156, 39, 176, 0.5)",
      borderRadius: "16px",
      padding: "2rem",
      minHeight: "120px",
      minWidth: "400px",
      backgroundColor: "rgba(156, 39, 176, 0.1)",
      textAlign: "center"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1518,
      columnNumber: 17
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1.2rem",
      marginBottom: "1rem",
      color: "rgba(255,255,255,0.8)"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1527,
      columnNumber: 19
    } }, "📥 Área de Classificação"), activityData.sortedItems?.length > 0 ? /* @__PURE__ */ React.createElement("div", { style: {
      display: "flex",
      gap: "1rem",
      justifyContent: "center",
      flexWrap: "wrap"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1532,
      columnNumber: 21
    } }, activityData.sortedItems.map((item, index) => /* @__PURE__ */ React.createElement("div", { key: index, style: {
      padding: "1.2rem",
      // Mais padding
      border: "2px solid rgba(76, 175, 80, 0.6)",
      borderRadius: "16px",
      // Cantos mais arredondados
      backgroundColor: "rgba(76, 175, 80, 0.2)",
      textAlign: "center",
      minWidth: "90px",
      // Largura mínima
      minHeight: "100px",
      // Altura mínima
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "center"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1539,
      columnNumber: 25
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "2.5rem",
      // Emoji maior
      marginBottom: "0.6rem",
      lineHeight: "1"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1552,
      columnNumber: 27
    } }, item.emoji), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "0.9rem",
      // Texto um pouco maior
      fontWeight: "600",
      // Mais bold
      lineHeight: "1.2"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1559,
      columnNumber: 27
    } }, item.label)))) : /* @__PURE__ */ React.createElement("div", { style: {
      color: "rgba(255,255,255,0.6)"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1570,
      columnNumber: 21
    } }, "Clique nos itens abaixo para classificá-los")))), /* @__PURE__ */ React.createElement("div", { className: styles.answerOptions, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1579,
      columnNumber: 13
    } }, activityData.allItems?.map((item, index) => {
      const isSelected = activityData.sortedItems?.some((sorted) => sorted.emoji === item.emoji);
      const isCorrect = activityData.correctItems?.some((correct2) => correct2.emoji === item.emoji);
      return /* @__PURE__ */ React.createElement("button", { key: index, className: `${styles.answerButton} ${isSelected ? styles.selected : ""}`, onClick: () => {
        if (!isSelected) {
          setGameState((prev) => ({
            ...prev,
            activityData: {
              ...prev.activityData,
              categorySorting: {
                ...prev.activityData.categorySorting,
                sortedItems: [...prev.activityData.categorySorting?.sortedItems || [], item]
              }
            }
          }));
          if (isCorrect) {
            speak(`Correto! ${item.label} pertence à categoria ${activityData.targetCategory.name}.`);
          } else {
            speak(`${item.label} não pertence à categoria ${activityData.targetCategory.name}.`);
          }
        }
      }, disabled: isSelected, "aria-label": `Classificar ${item.label}`, style: {
        opacity: isSelected ? 0.5 : 1,
        border: isSelected ? isCorrect ? "3px solid #4CAF50" : "3px solid #f44336" : "2px solid rgba(255,255,255,0.3)",
        minHeight: "120px",
        // Aumentar altura mínima
        minWidth: "100px",
        // Aumentar largura mínima
        padding: "1rem",
        // Mais padding
        flexDirection: "column",
        // Layout vertical
        position: "relative"
      }, __self: void 0, __source: {
        fileName: _jsxFileName,
        lineNumber: 1585,
        columnNumber: 19
      } }, /* @__PURE__ */ React.createElement("div", { style: {
        fontSize: "3rem",
        // Emoji maior
        marginBottom: "0.8rem",
        // Mais espaço entre emoji e texto
        lineHeight: "1"
        // Melhor alinhamento
      }, __self: void 0, __source: {
        fileName: _jsxFileName,
        lineNumber: 1622,
        columnNumber: 21
      } }, item.emoji), /* @__PURE__ */ React.createElement("div", { style: {
        fontSize: "1rem",
        // Texto um pouco maior
        fontWeight: "600",
        // Mais bold
        textAlign: "center",
        // Centralizado
        lineHeight: "1.2",
        // Melhor espaçamento de linha
        wordBreak: "break-word"
        // Quebra palavras longas
      }, __self: void 0, __source: {
        fileName: _jsxFileName,
        lineNumber: 1629,
        columnNumber: 21
      } }, item.label), isSelected && /* @__PURE__ */ React.createElement("div", { style: {
        position: "absolute",
        top: "8px",
        right: "8px",
        fontSize: "1.5rem"
        // Ícone de status maior
      }, __self: void 0, __source: {
        fileName: _jsxFileName,
        lineNumber: 1640,
        columnNumber: 23
      } }, isCorrect ? "✅" : "❌"));
    }))));
  };
  const renderVisualSequence = () => {
    const activityData = gameState.activityData?.visualSequence || {};
    return /* @__PURE__ */ React.createElement("div", { className: styles.questionArea, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1664,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.questionHeader, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1665,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("h2", { className: styles.questionTitle, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1666,
      columnNumber: 11
    } }, "📝 Teste de Raciocínio Sequencial")), activityData.pattern && /* @__PURE__ */ React.createElement(React.Fragment, null, /* @__PURE__ */ React.createElement("div", { className: styles.objectsDisplay, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1672,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      gap: "3rem",
      padding: "2rem"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1673,
      columnNumber: 15
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      textAlign: "center",
      padding: "1.5rem 2rem",
      backgroundColor: "rgba(255, 152, 0, 0.3)",
      borderRadius: "20px",
      border: "3px solid rgba(255, 152, 0, 0.7)",
      boxShadow: "0 4px 15px rgba(255, 152, 0, 0.2)"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1681,
      columnNumber: 17
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1.4rem",
      fontWeight: "bold",
      color: "white",
      textShadow: "2px 2px 4px rgba(0,0,0,0.5)"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1689,
      columnNumber: 19
    } }, "🧩 ", activityData.pattern.name)), /* @__PURE__ */ React.createElement("div", { style: {
      display: "flex",
      alignItems: "center",
      gap: "1rem",
      padding: "1.5rem",
      backgroundColor: "rgba(0,0,0,0.4)",
      borderRadius: "20px",
      border: "3px solid rgba(255,255,255,0.3)",
      boxShadow: "0 8px 25px rgba(0,0,0,0.3)",
      maxWidth: "100%",
      overflowX: "auto",
      justifyContent: "center"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1700,
      columnNumber: 17
    } }, activityData.pattern.sequence.map((item, index) => /* @__PURE__ */ React.createElement("div", { key: index, style: {
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      padding: "1.2rem 1rem",
      border: "3px solid rgba(33, 150, 243, 0.8)",
      borderRadius: "16px",
      backgroundColor: "rgba(33, 150, 243, 0.25)",
      minWidth: "80px",
      minHeight: "100px",
      boxShadow: "0 4px 15px rgba(33, 150, 243, 0.3)",
      transition: "transform 0.3s ease",
      flex: "0 0 auto",
      position: "relative"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1714,
      columnNumber: 21
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1rem",
      fontWeight: "bold",
      backgroundColor: "rgba(33, 150, 243, 1)",
      color: "white",
      borderRadius: "50%",
      width: "35px",
      height: "35px",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      marginBottom: "1.5rem",
      boxShadow: "0 4px 12px rgba(0,0,0,0.4)",
      border: "2px solid white"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1730,
      columnNumber: 23
    } }, index + 1), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "4rem",
      textShadow: "3px 3px 6px rgba(0,0,0,0.4)",
      filter: "drop-shadow(2px 2px 4px rgba(0,0,0,0.3))"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1749,
      columnNumber: 23
    } }, item))), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "4rem",
      color: "rgba(255, 193, 7, 1)",
      textShadow: "3px 3px 6px rgba(0,0,0,0.5)",
      animation: "pulse 1.5s infinite",
      transform: "translateY(-5px)"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1760,
      columnNumber: 19
    } }, "➤"), /* @__PURE__ */ React.createElement("div", { style: {
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      padding: "2rem 1.5rem",
      border: "4px dashed rgba(255, 193, 7, 1)",
      borderRadius: "20px",
      backgroundColor: "rgba(255, 193, 7, 0.4)",
      minWidth: "100px",
      minHeight: "120px",
      animation: "pulse 2s infinite",
      boxShadow: "0 6px 20px rgba(255, 193, 7, 0.4)"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1771,
      columnNumber: 19
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "0.9rem",
      fontWeight: "bold",
      backgroundColor: "rgba(255, 193, 7, 1)",
      color: "white",
      borderRadius: "50%",
      width: "30px",
      height: "30px",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      marginBottom: "1rem",
      boxShadow: "0 2px 8px rgba(0,0,0,0.3)"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1784,
      columnNumber: 21
    } }, activityData.pattern.sequence.length + 1), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "3.5rem",
      textShadow: "2px 2px 4px rgba(0,0,0,0.3)"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1800,
      columnNumber: 21
    } }, "❓"))))), /* @__PURE__ */ React.createElement("div", { className: styles.answerOptions, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1812,
      columnNumber: 13
    } }, activityData.pattern.options?.map((option, index) => {
      const isSelected = activityData.userAnswer === option;
      const isCorrect = option === activityData.pattern.correct;
      return /* @__PURE__ */ React.createElement("button", { key: index, className: `${styles.answerButton} ${isSelected ? styles.selected : ""}`, onClick: () => {
        setGameState((prev) => ({
          ...prev,
          activityData: {
            ...prev.activityData,
            visualSequence: {
              ...prev.activityData.visualSequence,
              userAnswer: option
            }
          }
        }));
        if (isCorrect) {
          speak(`Excelente! ${option} completa o padrão ${activityData.pattern.name} corretamente.`);
        } else {
          speak(`${option} selecionado. Analise se completa o padrão.`);
        }
      }, disabled: activityData.userAnswer !== null, "aria-label": `Escolher ${option}`, style: {
        border: isSelected ? isCorrect ? "3px solid #4CAF50" : "3px solid #f44336" : "2px solid rgba(255,255,255,0.3)",
        backgroundColor: isSelected ? isCorrect ? "rgba(76, 175, 80, 0.3)" : "rgba(244, 67, 54, 0.3)" : "var(--card-background)",
        transform: isSelected ? "scale(1.05)" : "scale(1)",
        minHeight: "120px",
        // Aumentar altura mínima
        minWidth: "100px",
        // Aumentar largura mínima
        padding: "1rem",
        // Mais padding
        flexDirection: "column",
        // Layout vertical
        position: "relative"
      }, __self: void 0, __source: {
        fileName: _jsxFileName,
        lineNumber: 1818,
        columnNumber: 19
      } }, /* @__PURE__ */ React.createElement("div", { style: {
        fontSize: "3rem",
        // Emoji maior
        marginBottom: "0.8rem",
        // Mais espaço entre emoji e texto
        lineHeight: "1"
        // Melhor alinhamento
      }, __self: void 0, __source: {
        fileName: _jsxFileName,
        lineNumber: 1856,
        columnNumber: 21
      } }, option), isSelected && /* @__PURE__ */ React.createElement("div", { style: {
        position: "absolute",
        top: "8px",
        right: "8px",
        fontSize: "1.5rem"
        // Ícone de status maior
      }, __self: void 0, __source: {
        fileName: _jsxFileName,
        lineNumber: 1865,
        columnNumber: 23
      } }, isCorrect ? "✅" : "❌"));
    }))));
  };
  const renderEmotionMatching = () => {
    const activityData = gameState.activityData?.emotionMatching || {};
    return /* @__PURE__ */ React.createElement("div", { className: styles.questionArea, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1889,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.questionHeader, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1890,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("h2", { className: styles.questionTitle, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1891,
      columnNumber: 11
    } }, "😊 Teste de Correspondência Emocional")), activityData.situation && /* @__PURE__ */ React.createElement(React.Fragment, null, /* @__PURE__ */ React.createElement("div", { className: styles.objectsDisplay, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1897,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      gap: "2rem",
      padding: "2rem"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1898,
      columnNumber: 15
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      textAlign: "center",
      padding: "2rem",
      border: "3px solid rgba(255, 193, 7, 0.6)",
      borderRadius: "20px",
      backgroundColor: "rgba(255, 193, 7, 0.2)",
      minWidth: "300px",
      boxShadow: "0 0 20px rgba(255, 193, 7, 0.3)"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1906,
      columnNumber: 17
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "4rem",
      marginBottom: "1rem"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1915,
      columnNumber: 19
    } }, activityData.situation.emoji), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1.5rem",
      fontWeight: "bold",
      color: "white"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1918,
      columnNumber: 19
    } }, activityData.situation.label)), /* @__PURE__ */ React.createElement("div", { style: {
      textAlign: "center",
      padding: "1rem",
      backgroundColor: "rgba(255, 255, 255, 0.1)",
      borderRadius: "12px",
      border: "2px solid rgba(255, 255, 255, 0.2)"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1924,
      columnNumber: 17
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1.2rem",
      fontWeight: "bold",
      color: "white"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1931,
      columnNumber: 19
    } }, "🤔 Como você se sentiria nesta situação?")))), /* @__PURE__ */ React.createElement("div", { className: styles.answerOptions, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 1939,
      columnNumber: 13
    } }, activityData.emotions?.map((emotion, index) => {
      const isSelected = activityData.userAnswer === emotion;
      const isCorrect = emotion.correct;
      return /* @__PURE__ */ React.createElement("button", { key: index, className: `${styles.answerButton} ${isSelected ? styles.selected : ""}`, onClick: () => {
        setGameState((prev) => ({
          ...prev,
          activityData: {
            ...prev.activityData,
            emotionMatching: {
              ...prev.activityData.emotionMatching,
              userAnswer: emotion
            }
          }
        }));
        if (isCorrect) {
          speak(`Perfeito! ${emotion.label} é realmente como nos sentimos em ${activityData.situation.label}.`);
        } else {
          speak(`${emotion.label} selecionado. Pense em como você se sentiria nesta situação.`);
        }
      }, disabled: activityData.userAnswer !== null, "aria-label": `Escolher emoção: ${emotion.label}`, style: {
        border: isSelected ? isCorrect ? "3px solid #4CAF50" : "3px solid #f44336" : "2px solid rgba(255,255,255,0.3)",
        backgroundColor: isSelected ? isCorrect ? "rgba(76, 175, 80, 0.3)" : "rgba(244, 67, 54, 0.3)" : "var(--card-background)",
        transform: isSelected ? "scale(1.05)" : "scale(1)",
        boxShadow: isSelected ? isCorrect ? "0 0 15px rgba(76, 175, 80, 0.5)" : "0 0 15px rgba(244, 67, 54, 0.5)" : "none",
        flexDirection: "column"
      }, __self: void 0, __source: {
        fileName: _jsxFileName,
        lineNumber: 1945,
        columnNumber: 19
      } }, /* @__PURE__ */ React.createElement("div", { style: {
        fontSize: "3rem",
        marginBottom: "0.5rem"
      }, __self: void 0, __source: {
        fileName: _jsxFileName,
        lineNumber: 1982,
        columnNumber: 21
      } }, emotion.emoji), /* @__PURE__ */ React.createElement("div", { style: {
        fontSize: "1rem",
        fontWeight: "bold"
      }, __self: void 0, __source: {
        fileName: _jsxFileName,
        lineNumber: 1985,
        columnNumber: 21
      } }, emotion.label), isSelected && /* @__PURE__ */ React.createElement("div", { style: {
        position: "absolute",
        top: "10px",
        right: "10px",
        fontSize: "1.5rem",
        fontWeight: "bold"
      }, __self: void 0, __source: {
        fileName: _jsxFileName,
        lineNumber: 1990,
        columnNumber: 23
      } }, isCorrect ? "✅" : "❌"));
    })), activityData.userAnswer && /* @__PURE__ */ React.createElement("div", { style: {
      margin: "2rem auto",
      padding: "1.5rem",
      backgroundColor: activityData.userAnswer.correct ? "rgba(76, 175, 80, 0.2)" : "rgba(244, 67, 54, 0.2)",
      border: activityData.userAnswer.correct ? "2px solid rgba(76, 175, 80, 0.5)" : "2px solid rgba(244, 67, 54, 0.5)",
      borderRadius: "12px",
      textAlign: "center",
      maxWidth: "600px"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2007,
      columnNumber: 15
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1.1rem",
      color: "white",
      fontWeight: "bold",
      marginBottom: "0.5rem"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2016,
      columnNumber: 17
    } }, activityData.userAnswer.correct ? "🎉 Correto!" : "🤔 Pense novamente"), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1rem",
      color: "rgba(255,255,255,0.9)"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2024,
      columnNumber: 17
    } }, activityData.explanation))));
  };
  const renderContextReasoning = () => {
    const activityData = gameState.activityData?.contextReasoning || {};
    return /* @__PURE__ */ React.createElement("div", { className: styles.questionArea, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2043,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.questionHeader, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2044,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("h2", { className: styles.questionTitle, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2045,
      columnNumber: 11
    } }, "🏠 Teste de Raciocínio Contextual")), activityData.context && /* @__PURE__ */ React.createElement(React.Fragment, null, /* @__PURE__ */ React.createElement("div", { className: styles.objectsDisplay, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2051,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      gap: "2rem",
      padding: "2rem"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2052,
      columnNumber: 15
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      textAlign: "center",
      padding: "2rem",
      border: "3px solid rgba(103, 58, 183, 0.6)",
      borderRadius: "20px",
      backgroundColor: "rgba(103, 58, 183, 0.2)",
      minWidth: "300px",
      boxShadow: "0 0 20px rgba(103, 58, 183, 0.3)"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2060,
      columnNumber: 17
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "4rem",
      marginBottom: "1rem"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2069,
      columnNumber: 19
    } }, activityData.context.emoji), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1.5rem",
      fontWeight: "bold",
      color: "white"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2072,
      columnNumber: 19
    } }, activityData.context.label)), /* @__PURE__ */ React.createElement("div", { style: {
      border: "2px dashed rgba(103, 58, 183, 0.5)",
      borderRadius: "16px",
      padding: "2rem",
      minHeight: "120px",
      minWidth: "400px",
      backgroundColor: "rgba(103, 58, 183, 0.1)",
      textAlign: "center"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2078,
      columnNumber: 17
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1.2rem",
      marginBottom: "1rem",
      color: "rgba(255,255,255,0.8)"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2087,
      columnNumber: 19
    } }, "🎯 Itens selecionados que pertencem ao contexto"), activityData.selectedItems?.length > 0 ? /* @__PURE__ */ React.createElement("div", { style: {
      display: "flex",
      gap: "1rem",
      justifyContent: "center",
      flexWrap: "wrap"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2092,
      columnNumber: 21
    } }, activityData.selectedItems.map((item, index) => /* @__PURE__ */ React.createElement("div", { key: index, style: {
      padding: "1.5rem",
      border: item.belongs ? "3px solid rgba(76, 175, 80, 0.8)" : "3px solid rgba(244, 67, 54, 0.8)",
      borderRadius: "16px",
      backgroundColor: item.belongs ? "rgba(76, 175, 80, 0.3)" : "rgba(244, 67, 54, 0.3)",
      textAlign: "center",
      minWidth: "120px",
      transition: "all 0.3s ease"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2099,
      columnNumber: 25
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "3rem",
      marginBottom: "0.5rem"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2108,
      columnNumber: 27
    } }, item.emoji), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1rem",
      fontWeight: "bold",
      color: "white"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2109,
      columnNumber: 27
    } }, item.label), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1.5rem",
      marginTop: "0.5rem"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2110,
      columnNumber: 27
    } }, item.belongs ? "✅" : "❌")))) : /* @__PURE__ */ React.createElement("div", { style: {
      color: "rgba(255,255,255,0.6)"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2117,
      columnNumber: 21
    } }, "Clique nos itens abaixo que pertencem ao contexto")))), /* @__PURE__ */ React.createElement("div", { className: styles.answerOptions, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2126,
      columnNumber: 13
    } }, activityData.items?.map((item, index) => {
      const isSelected = activityData.selectedItems?.some((selected) => selected.emoji === item.emoji);
      const isCorrect = item.belongs;
      return /* @__PURE__ */ React.createElement("button", { key: index, className: `${styles.answerButton} ${isSelected ? styles.selected : ""}`, onClick: () => {
        if (!isSelected) {
          setGameState((prev) => ({
            ...prev,
            activityData: {
              ...prev.activityData,
              contextReasoning: {
                ...prev.activityData.contextReasoning,
                selectedItems: [...prev.activityData.contextReasoning?.selectedItems || [], item]
              }
            }
          }));
          if (isCorrect) {
            speak(`Correto! ${item.label} realmente pertence ao contexto ${activityData.context.label}.`);
          } else {
            speak(`${item.label} não pertence ao contexto ${activityData.context.label}. Pense sobre onde encontramos isso.`);
          }
        }
      }, disabled: isSelected, "aria-label": `Selecionar ${item.label}`, style: {
        opacity: isSelected ? 0.7 : 1,
        border: isSelected ? isCorrect ? "3px solid #4CAF50" : "3px solid #f44336" : "2px solid rgba(255,255,255,0.3)",
        backgroundColor: isSelected ? isCorrect ? "rgba(76, 175, 80, 0.3)" : "rgba(244, 67, 54, 0.3)" : "var(--card-background)",
        minHeight: "120px",
        // Aumentar altura mínima
        minWidth: "100px",
        // Aumentar largura mínima
        padding: "1rem",
        // Mais padding
        flexDirection: "column",
        // Layout vertical
        position: "relative"
      }, __self: void 0, __source: {
        fileName: _jsxFileName,
        lineNumber: 2132,
        columnNumber: 19
      } }, /* @__PURE__ */ React.createElement("div", { style: {
        fontSize: "3rem",
        // Emoji maior
        marginBottom: "0.8rem",
        // Mais espaço entre emoji e texto
        lineHeight: "1"
        // Melhor alinhamento
      }, __self: void 0, __source: {
        fileName: _jsxFileName,
        lineNumber: 2172,
        columnNumber: 21
      } }, item.emoji), /* @__PURE__ */ React.createElement("div", { style: {
        fontSize: "1rem",
        // Texto um pouco maior
        fontWeight: "600",
        // Mais bold
        textAlign: "center",
        // Centralizado
        lineHeight: "1.2",
        // Melhor espaçamento de linha
        wordBreak: "break-word"
        // Quebra palavras longas
      }, __self: void 0, __source: {
        fileName: _jsxFileName,
        lineNumber: 2179,
        columnNumber: 21
      } }, item.label), isSelected && /* @__PURE__ */ React.createElement("div", { style: {
        position: "absolute",
        top: "8px",
        right: "8px",
        fontSize: "1.5rem"
        // Ícone de status maior
      }, __self: void 0, __source: {
        fileName: _jsxFileName,
        lineNumber: 2190,
        columnNumber: 23
      } }, isCorrect ? "✅" : "❌"));
    })), activityData.selectedItems?.length > 0 && /* @__PURE__ */ React.createElement("div", { style: {
      margin: "2rem auto",
      padding: "1rem",
      backgroundColor: "rgba(103, 58, 183, 0.2)",
      border: "2px solid rgba(103, 58, 183, 0.5)",
      borderRadius: "12px",
      textAlign: "center",
      maxWidth: "400px"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2206,
      columnNumber: 15
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1.1rem",
      color: "white",
      fontWeight: "bold"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2215,
      columnNumber: 17
    } }, "📊 Progresso: ", activityData.selectedItems.length, " itens selecionados"), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "0.9rem",
      color: "rgba(255,255,255,0.8)",
      marginTop: "0.5rem"
    }, __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2218,
      columnNumber: 17
    } }, "Corretos: ", activityData.selectedItems.filter((item) => item.belongs).length, " | Incorretos: ", activityData.selectedItems.filter((item) => !item.belongs).length))));
  };
  if (showStartScreen) {
    return /* @__PURE__ */ React.createElement(GameStartScreen, { gameTitle: "Associação de Imagens", gameDescription: "Desenvolva sua capacidade de associação e raciocínio lógico", gameIcon: "🖼️", onStart: startGame, onBack, difficulties: [{
      id: "EASY",
      name: "Fácil",
      description: "Associações básicas\nIdeal para iniciantes",
      icon: "😊"
    }, {
      id: "MEDIUM",
      name: "Médio",
      description: "Relações funcionais\nDesafio equilibrado",
      icon: "🎯"
    }, {
      id: "HARD",
      name: "Avançado",
      description: "Associações abstratas\nPara especialistas",
      icon: "🚀"
    }], __self: void 0, __source: {
      fileName: _jsxFileName,
      lineNumber: 2259,
      columnNumber: 7
    } });
  }
  return /* @__PURE__ */ React.createElement("div", { className: `${styles.imageAssociationGame} ${settings.reducedMotion ? "reduced-motion" : ""} ${settings.highContrast ? "high-contrast" : ""}`, "data-font-size": settings.fontSize, "data-theme": settings.colorScheme, style: {
    fontSize: settings.fontSize === "small" ? "0.875rem" : settings.fontSize === "large" ? "1.25rem" : "1rem"
  }, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2275,
    columnNumber: 5
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.gameContent, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2284,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.gameHeader, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2286,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("h1", { className: styles.gameTitle, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2287,
    columnNumber: 11
  } }, "🖼️ Associação de Imagens V3", /* @__PURE__ */ React.createElement("div", { style: {
    fontSize: "0.7rem",
    opacity: 0.8,
    marginTop: "0.25rem"
  }, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2289,
    columnNumber: 13
  } }, ACTIVITY_TYPES[gameState.currentActivity?.toUpperCase()]?.name || "Associação Básica")), /* @__PURE__ */ React.createElement("button", { className: `${styles.headerTtsButton} ${ttsActive2 ? styles.ttsActive : ""}`, onClick: toggleTTS, title: ttsActive2 ? "Desativar TTS" : "Ativar TTS", "aria-label": ttsActive2 ? "Desativar TTS" : "Ativar TTS", __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2293,
    columnNumber: 11
  } }, ttsActive2 ? "🔊" : "🔇")), /* @__PURE__ */ React.createElement("div", { className: styles.gameStats, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2304,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statCard, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2305,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statValue, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2306,
    columnNumber: 13
  } }, gameState.score), /* @__PURE__ */ React.createElement("div", { className: styles.statLabel, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2307,
    columnNumber: 13
  } }, "Pontos")), /* @__PURE__ */ React.createElement("div", { className: styles.statCard, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2309,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statValue, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2310,
    columnNumber: 13
  } }, gameState.round), /* @__PURE__ */ React.createElement("div", { className: styles.statLabel, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2311,
    columnNumber: 13
  } }, "Rodada")), /* @__PURE__ */ React.createElement("div", { className: styles.statCard, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2313,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statValue, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2314,
    columnNumber: 13
  } }, gameState.accuracy, "%"), /* @__PURE__ */ React.createElement("div", { className: styles.statLabel, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2315,
    columnNumber: 13
  } }, "Precisão")), /* @__PURE__ */ React.createElement("div", { className: styles.statCard, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2317,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statValue, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2318,
    columnNumber: 13
  } }, gameState.level), /* @__PURE__ */ React.createElement("div", { className: styles.statLabel, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2319,
    columnNumber: 13
  } }, "Nível"))), /* @__PURE__ */ React.createElement("div", { className: styles.activityMenu, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2324,
    columnNumber: 9
  } }, Object.values(ACTIVITY_TYPES).map((activity) => /* @__PURE__ */ React.createElement("button", { key: activity.id, className: `${styles.activityButton} ${gameState.currentActivity === activity.id ? styles.active : ""}`, onClick: () => switchToActivity(activity.id), __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2326,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2333,
    columnNumber: 15
  } }, activity.icon), /* @__PURE__ */ React.createElement("span", { __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2334,
    columnNumber: 15
  } }, activity.name)))), gameState.currentActivity === ACTIVITY_TYPES.BASIC_ASSOCIATION.id && renderBasicAssociation(), gameState.currentActivity === ACTIVITY_TYPES.CATEGORY_SORTING.id && renderCategorySorting(), gameState.currentActivity === ACTIVITY_TYPES.VISUAL_SEQUENCE.id && renderVisualSequence(), gameState.currentActivity === ACTIVITY_TYPES.EMOTION_MATCHING.id && renderEmotionMatching(), gameState.currentActivity === ACTIVITY_TYPES.CONTEXT_REASONING.id && renderContextReasoning(), /* @__PURE__ */ React.createElement("div", { className: styles.gameControls, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2347,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("button", { className: styles.controlButton, onClick: () => speak("Associação de imagens. Conecte imagens relacionadas e desenvolva raciocínio lógico."), __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2348,
    columnNumber: 11
  } }, "🔊 Explicar"), /* @__PURE__ */ React.createElement("button", { className: styles.controlButton, onClick: () => setShowStartScreen(true), __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2351,
    columnNumber: 11
  } }, "🔄 Reiniciar"), /* @__PURE__ */ React.createElement("button", { className: styles.controlButton, onClick: onBack, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 2354,
    columnNumber: 11
  } }, "⬅️ Voltar"))));
};
const ImageAssociationGame$1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: ImageAssociationGame
}, Symbol.toStringTag, { value: "Module" }));
export {
  GameStartScreen as G,
  ImageAssociationProcessors as I,
  ImageAssociationCollectorsHub as a,
  ImageAssociationGame$1 as b
};
//# sourceMappingURL=game-association-B9GAxBuN.js.map
