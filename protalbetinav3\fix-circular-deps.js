#!/usr/bin/env node

/**
 * Script para detectar e resolver dependências circulares
 * que podem estar causando o erro "Cannot access 'e' before initialization"
 */

import fs from 'fs';
import path from 'path';

// Mapa de dependências
const dependencyMap = new Map();
const circularDeps = [];

// Função para extrair imports de um arquivo
function extractImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const imports = [];
    
    // Regex para capturar imports
    const importRegex = /import\s+(?:(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)\s+from\s+)?['"]([^'"]+)['"]/g;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      const importPath = match[1];
      
      // Resolver caminho relativo
      if (importPath.startsWith('./') || importPath.startsWith('../')) {
        const resolvedPath = path.resolve(path.dirname(filePath), importPath);
        
        // Tentar diferentes extensões
        const extensions = ['.js', '.jsx', '.ts', '.tsx'];
        for (const ext of extensions) {
          const fullPath = resolvedPath + ext;
          if (fs.existsSync(fullPath)) {
            imports.push(fullPath);
            break;
          }
        }
        
        // Verificar se é um diretório com index
        const indexPath = path.join(resolvedPath, 'index.js');
        const indexJsxPath = path.join(resolvedPath, 'index.jsx');
        if (fs.existsSync(indexPath)) {
          imports.push(indexPath);
        } else if (fs.existsSync(indexJsxPath)) {
          imports.push(indexJsxPath);
        }
      }
    }
    
    return imports;
  } catch (error) {
    console.warn(`Erro ao processar ${filePath}: ${error.message}`);
    return [];
  }
}

// Função para encontrar arquivos JS/JSX
function findJsFiles(dir) {
  let results = [];
  
  if (!fs.existsSync(dir)) {
    return results;
  }
  
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      results = results.concat(findJsFiles(fullPath));
    } else if (file.endsWith('.js') || file.endsWith('.jsx')) {
      results.push(fullPath);
    }
  }
  
  return results;
}

// Função para detectar dependências circulares
function detectCircularDependencies(file, visited = new Set(), path = []) {
  if (path.includes(file)) {
    // Encontrou ciclo
    const cycleStart = path.indexOf(file);
    const cycle = path.slice(cycleStart).concat([file]);
    circularDeps.push(cycle);
    return;
  }
  
  if (visited.has(file)) {
    return;
  }
  
  visited.add(file);
  path.push(file);
  
  const dependencies = dependencyMap.get(file) || [];
  for (const dep of dependencies) {
    detectCircularDependencies(dep, visited, [...path]);
  }
}

// Função principal
async function main() {
  console.log('🔍 Detectando dependências circulares...\n');
  
  // Encontrar todos os arquivos
  const dirs = ['./src/games', './src/utils', './src/components'];
  let allFiles = [];
  
  for (const dir of dirs) {
    allFiles = allFiles.concat(findJsFiles(dir));
  }
  
  console.log(`📄 Analisando ${allFiles.length} arquivos...\n`);
  
  // Construir mapa de dependências
  for (const file of allFiles) {
    const imports = extractImports(file);
    dependencyMap.set(file, imports);
  }
  
  // Detectar ciclos
  for (const file of allFiles) {
    detectCircularDependencies(file);
  }
  
  // Relatório
  if (circularDeps.length === 0) {
    console.log('✅ Nenhuma dependência circular encontrada!');
  } else {
    console.log(`❌ Encontradas ${circularDeps.length} dependências circulares:\n`);
    
    circularDeps.forEach((cycle, index) => {
      console.log(`🔄 Ciclo ${index + 1}:`);
      cycle.forEach((file, i) => {
        const relativePath = path.relative(process.cwd(), file);
        console.log(`   ${i + 1}. ${relativePath}`);
      });
      console.log('');
    });
    
    console.log('💡 SOLUÇÕES RECOMENDADAS:');
    console.log('1. Mover código compartilhado para um módulo separado');
    console.log('2. Usar importação dinâmica (import()) onde possível');
    console.log('3. Refatorar para remover dependências desnecessárias');
    console.log('4. Usar injeção de dependência');
  }
  
  // Verificar arquivos problemáticos específicos
  console.log('\n🎯 VERIFICANDO ARQUIVOS ESPECÍFICOS:');
  
  const problematicFiles = [
    './src/games/ColorMatch/ColorMatchGame.jsx',
    './src/games/ColorMatch/collectors/index.js',
    './src/utils/realMetrics.js',
    './src/database/services/DatabaseIntegrator.js'
  ];
  
  for (const file of problematicFiles) {
    if (fs.existsSync(file)) {
      const deps = dependencyMap.get(path.resolve(file)) || [];
      console.log(`📄 ${file}:`);
      console.log(`   Importa ${deps.length} arquivos`);
      
      // Verificar se está em algum ciclo
      const inCycle = circularDeps.some(cycle => 
        cycle.some(cycleFile => path.resolve(cycleFile) === path.resolve(file))
      );
      
      if (inCycle) {
        console.log('   ⚠️ ESTÁ EM DEPENDÊNCIA CIRCULAR');
      } else {
        console.log('   ✅ Sem dependências circulares');
      }
    } else {
      console.log(`📄 ${file}: ❌ Arquivo não encontrado`);
    }
  }
}

main().catch(console.error);
