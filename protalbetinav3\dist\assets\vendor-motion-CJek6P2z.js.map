{"version": 3, "file": "vendor-motion-CJek6P2z.js", "sources": ["../../node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs", "../../node_modules/framer-motion/dist/es/utils/use-constant.mjs", "../../node_modules/framer-motion/dist/es/utils/is-browser.mjs", "../../node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs", "../../node_modules/framer-motion/dist/es/context/PresenceContext.mjs", "../../node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs", "../../node_modules/framer-motion/dist/es/components/AnimatePresence/use-presence.mjs", "../../node_modules/framer-motion/dist/es/context/LazyContext.mjs", "../../node_modules/framer-motion/dist/es/motion/features/definitions.mjs", "../../node_modules/framer-motion/dist/es/motion/features/load-features.mjs", "../../node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs", "../../node_modules/framer-motion/dist/es/render/dom/utils/filter-props.mjs", "../../node_modules/framer-motion/dist/es/render/components/create-proxy.mjs", "../../node_modules/framer-motion/dist/es/context/MotionContext/index.mjs", "../../node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs", "../../node_modules/framer-motion/dist/es/render/utils/is-variant-label.mjs", "../../node_modules/framer-motion/dist/es/render/utils/variant-props.mjs", "../../node_modules/framer-motion/dist/es/render/utils/is-controlling-variants.mjs", "../../node_modules/framer-motion/dist/es/context/MotionContext/utils.mjs", "../../node_modules/framer-motion/dist/es/context/MotionContext/create.mjs", "../../node_modules/framer-motion/dist/es/motion/utils/symbol.mjs", "../../node_modules/framer-motion/dist/es/utils/is-ref-object.mjs", "../../node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs", "../../node_modules/framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs", "../../node_modules/framer-motion/dist/es/animation/optimized-appear/data-id.mjs", "../../node_modules/framer-motion/dist/es/context/SwitchLayoutGroupContext.mjs", "../../node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs", "../../node_modules/framer-motion/dist/es/motion/index.mjs", "../../node_modules/framer-motion/dist/es/projection/styles/scale-correction.mjs", "../../node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs", "../../node_modules/framer-motion/dist/es/render/html/utils/build-transform.mjs", "../../node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs", "../../node_modules/framer-motion/dist/es/render/html/utils/create-render-state.mjs", "../../node_modules/framer-motion/dist/es/render/html/use-props.mjs", "../../node_modules/framer-motion/dist/es/render/svg/utils/path.mjs", "../../node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs", "../../node_modules/framer-motion/dist/es/render/svg/utils/create-render-state.mjs", "../../node_modules/framer-motion/dist/es/render/svg/utils/is-svg-tag.mjs", "../../node_modules/framer-motion/dist/es/render/svg/use-props.mjs", "../../node_modules/framer-motion/dist/es/render/svg/lowercase-elements.mjs", "../../node_modules/framer-motion/dist/es/render/dom/utils/is-svg-component.mjs", "../../node_modules/framer-motion/dist/es/render/dom/use-render.mjs", "../../node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs", "../../node_modules/framer-motion/dist/es/value/utils/resolve-motion-value.mjs", "../../node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs", "../../node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs", "../../node_modules/framer-motion/dist/es/render/html/config-motion.mjs", "../../node_modules/framer-motion/dist/es/render/svg/utils/scrape-motion-values.mjs", "../../node_modules/framer-motion/dist/es/render/svg/config-motion.mjs", "../../node_modules/framer-motion/dist/es/render/components/create-factory.mjs", "../../node_modules/framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs", "../../node_modules/framer-motion/dist/es/animation/utils/is-keyframes-target.mjs", "../../node_modules/framer-motion/dist/es/render/utils/setters.mjs", "../../node_modules/framer-motion/dist/es/value/use-will-change/is.mjs", "../../node_modules/framer-motion/dist/es/value/use-will-change/add-will-change.mjs", "../../node_modules/framer-motion/dist/es/animation/optimized-appear/get-appear-id.mjs", "../../node_modules/framer-motion/dist/es/animation/animators/waapi/utils/get-final-keyframe.mjs", "../../node_modules/framer-motion/dist/es/animation/utils/default-transitions.mjs", "../../node_modules/framer-motion/dist/es/animation/utils/is-transition-defined.mjs", "../../node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs", "../../node_modules/framer-motion/dist/es/animation/interfaces/visual-element-target.mjs", "../../node_modules/framer-motion/dist/es/animation/interfaces/visual-element-variant.mjs", "../../node_modules/framer-motion/dist/es/animation/interfaces/visual-element.mjs", "../../node_modules/framer-motion/dist/es/utils/shallow-compare.mjs", "../../node_modules/framer-motion/dist/es/render/utils/get-variant-context.mjs", "../../node_modules/framer-motion/dist/es/render/utils/animation-state.mjs", "../../node_modules/framer-motion/dist/es/motion/features/Feature.mjs", "../../node_modules/framer-motion/dist/es/motion/features/animation/index.mjs", "../../node_modules/framer-motion/dist/es/motion/features/animation/exit.mjs", "../../node_modules/framer-motion/dist/es/motion/features/animations.mjs", "../../node_modules/framer-motion/dist/es/events/add-dom-event.mjs", "../../node_modules/framer-motion/dist/es/events/event-info.mjs", "../../node_modules/framer-motion/dist/es/events/add-pointer-event.mjs", "../../node_modules/framer-motion/dist/es/projection/geometry/conversion.mjs", "../../node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs", "../../node_modules/framer-motion/dist/es/projection/geometry/models.mjs", "../../node_modules/framer-motion/dist/es/projection/utils/each-axis.mjs", "../../node_modules/framer-motion/dist/es/projection/utils/has-transform.mjs", "../../node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs", "../../node_modules/framer-motion/dist/es/projection/utils/measure.mjs", "../../node_modules/framer-motion/dist/es/utils/get-context-window.mjs", "../../node_modules/framer-motion/dist/es/utils/distance.mjs", "../../node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs", "../../node_modules/framer-motion/dist/es/gestures/drag/utils/constraints.mjs", "../../node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs", "../../node_modules/framer-motion/dist/es/gestures/drag/index.mjs", "../../node_modules/framer-motion/dist/es/gestures/pan/index.mjs", "../../node_modules/framer-motion/dist/es/projection/node/state.mjs", "../../node_modules/framer-motion/dist/es/projection/styles/scale-border-radius.mjs", "../../node_modules/framer-motion/dist/es/projection/styles/scale-box-shadow.mjs", "../../node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs", "../../node_modules/framer-motion/dist/es/animation/animate/single-value.mjs", "../../node_modules/framer-motion/dist/es/render/utils/compare-by-depth.mjs", "../../node_modules/framer-motion/dist/es/render/utils/flat-tree.mjs", "../../node_modules/framer-motion/dist/es/utils/delay.mjs", "../../node_modules/framer-motion/dist/es/projection/animation/mix-values.mjs", "../../node_modules/framer-motion/dist/es/projection/geometry/copy.mjs", "../../node_modules/framer-motion/dist/es/projection/geometry/delta-remove.mjs", "../../node_modules/framer-motion/dist/es/projection/geometry/utils.mjs", "../../node_modules/framer-motion/dist/es/projection/shared/stack.mjs", "../../node_modules/framer-motion/dist/es/projection/styles/transform.mjs", "../../node_modules/framer-motion/dist/es/projection/node/create-projection-node.mjs", "../../node_modules/framer-motion/dist/es/projection/node/DocumentProjectionNode.mjs", "../../node_modules/framer-motion/dist/es/projection/node/HTMLProjectionNode.mjs", "../../node_modules/framer-motion/dist/es/motion/features/drag.mjs", "../../node_modules/framer-motion/dist/es/gestures/hover.mjs", "../../node_modules/framer-motion/dist/es/gestures/focus.mjs", "../../node_modules/framer-motion/dist/es/gestures/press.mjs", "../../node_modules/framer-motion/dist/es/motion/features/viewport/observers.mjs", "../../node_modules/framer-motion/dist/es/motion/features/viewport/index.mjs", "../../node_modules/framer-motion/dist/es/motion/features/gestures.mjs", "../../node_modules/framer-motion/dist/es/motion/features/layout.mjs", "../../node_modules/framer-motion/dist/es/utils/reduced-motion/state.mjs", "../../node_modules/framer-motion/dist/es/utils/reduced-motion/index.mjs", "../../node_modules/framer-motion/dist/es/render/store.mjs", "../../node_modules/framer-motion/dist/es/render/utils/motion-values.mjs", "../../node_modules/framer-motion/dist/es/render/VisualElement.mjs", "../../node_modules/framer-motion/dist/es/render/dom/DOMVisualElement.mjs", "../../node_modules/framer-motion/dist/es/render/html/utils/render.mjs", "../../node_modules/framer-motion/dist/es/render/html/HTMLVisualElement.mjs", "../../node_modules/framer-motion/dist/es/render/svg/utils/camel-case-attrs.mjs", "../../node_modules/framer-motion/dist/es/render/svg/utils/render.mjs", "../../node_modules/framer-motion/dist/es/render/svg/SVGVisualElement.mjs", "../../node_modules/framer-motion/dist/es/render/dom/create-visual-element.mjs", "../../node_modules/framer-motion/dist/es/render/components/motion/create.mjs", "../../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs"], "sourcesContent": ["\"use client\";\nimport { createContext } from 'react';\n\nconst LayoutGroupContext = createContext({});\n\nexport { LayoutGroupContext };\n", "import { useRef } from 'react';\n\n/**\n * Creates a constant value over the lifecycle of a component.\n *\n * Even if `useMemo` is provided an empty array as its final argument, it doesn't offer\n * a guarantee that it won't re-run for performance reasons later on. By using `useConstant`\n * you can ensure that initialisers don't execute twice or more.\n */\nfunction useConstant(init) {\n    const ref = useRef(null);\n    if (ref.current === null) {\n        ref.current = init();\n    }\n    return ref.current;\n}\n\nexport { useConstant };\n", "const isBrowser = typeof window !== \"undefined\";\n\nexport { isBrowser };\n", "import { useLayoutEffect, useEffect } from 'react';\nimport { isBrowser } from './is-browser.mjs';\n\nconst useIsomorphicLayoutEffect = isBrowser ? useLayoutEffect : useEffect;\n\nexport { useIsomorphicLayoutEffect };\n", "\"use client\";\nimport { createContext } from 'react';\n\n/**\n * @public\n */\nconst PresenceContext = \n/* @__PURE__ */ createContext(null);\n\nexport { PresenceContext };\n", "\"use client\";\nimport { createContext } from 'react';\n\n/**\n * @public\n */\nconst MotionConfigContext = createContext({\n    transformPagePoint: (p) => p,\n    isStatic: false,\n    reducedMotion: \"never\",\n});\n\nexport { MotionConfigContext };\n", "import { useContext, useId, useEffect, useCallback } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\n\n/**\n * When a component is the child of `AnimatePresence`, it can use `usePresence`\n * to access information about whether it's still present in the React tree.\n *\n * ```jsx\n * import { usePresence } from \"framer-motion\"\n *\n * export const Component = () => {\n *   const [isPresent, safeToRemove] = usePresence()\n *\n *   useEffect(() => {\n *     !isPresent && setTimeout(safeToRemove, 1000)\n *   }, [isPresent])\n *\n *   return <div />\n * }\n * ```\n *\n * If `isPresent` is `false`, it means that a component has been removed the tree, but\n * `AnimatePresence` won't really remove it until `safeToRemove` has been called.\n *\n * @public\n */\nfunction usePresence(subscribe = true) {\n    const context = useContext(PresenceContext);\n    if (context === null)\n        return [true, null];\n    const { isPresent, onExitComplete, register } = context;\n    // It's safe to call the following hooks conditionally (after an early return) because the context will always\n    // either be null or non-null for the lifespan of the component.\n    const id = useId();\n    useEffect(() => {\n        if (subscribe) {\n            return register(id);\n        }\n    }, [subscribe]);\n    const safeToRemove = useCallback(() => subscribe && onExitComplete && onExitComplete(id), [id, onExitComplete, subscribe]);\n    return !isPresent && onExitComplete ? [false, safeToRemove] : [true];\n}\n/**\n * Similar to `usePresence`, except `useIsPresent` simply returns whether or not the component is present.\n * There is no `safeToRemove` function.\n *\n * ```jsx\n * import { useIsPresent } from \"framer-motion\"\n *\n * export const Component = () => {\n *   const isPresent = useIsPresent()\n *\n *   useEffect(() => {\n *     !isPresent && console.log(\"I've been removed!\")\n *   }, [isPresent])\n *\n *   return <div />\n * }\n * ```\n *\n * @public\n */\nfunction useIsPresent() {\n    return isPresent(useContext(PresenceContext));\n}\nfunction isPresent(context) {\n    return context === null ? true : context.isPresent;\n}\n\nexport { isPresent, useIsPresent, usePresence };\n", "\"use client\";\nimport { createContext } from 'react';\n\nconst LazyContext = createContext({ strict: false });\n\nexport { LazyContext };\n", "const featureProps = {\n    animation: [\n        \"animate\",\n        \"variants\",\n        \"whileHover\",\n        \"whileTap\",\n        \"exit\",\n        \"whileInView\",\n        \"whileFocus\",\n        \"whileDrag\",\n    ],\n    exit: [\"exit\"],\n    drag: [\"drag\", \"dragControls\"],\n    focus: [\"whileFocus\"],\n    hover: [\"whileHover\", \"onHoverStart\", \"onHoverEnd\"],\n    tap: [\"whileTap\", \"onTap\", \"onTapStart\", \"onTapCancel\"],\n    pan: [\"onPan\", \"onPanStart\", \"onPanSessionStart\", \"onPanEnd\"],\n    inView: [\"whileInView\", \"onViewportEnter\", \"onViewportLeave\"],\n    layout: [\"layout\", \"layoutId\"],\n};\nconst featureDefinitions = {};\nfor (const key in featureProps) {\n    featureDefinitions[key] = {\n        isEnabled: (props) => featureProps[key].some((name) => !!props[name]),\n    };\n}\n\nexport { featureDefinitions };\n", "import { featureDefinitions } from './definitions.mjs';\n\nfunction loadFeatures(features) {\n    for (const key in features) {\n        featureDefinitions[key] = {\n            ...featureDefinitions[key],\n            ...features[key],\n        };\n    }\n}\n\nexport { loadFeatures };\n", "/**\n * A list of all valid MotionProps.\n *\n * @privateRemarks\n * This doesn't throw if a `MotionProp` name is missing - it should.\n */\nconst validMotionProps = new Set([\n    \"animate\",\n    \"exit\",\n    \"variants\",\n    \"initial\",\n    \"style\",\n    \"values\",\n    \"variants\",\n    \"transition\",\n    \"transformTemplate\",\n    \"custom\",\n    \"inherit\",\n    \"onBeforeLayoutMeasure\",\n    \"onAnimationStart\",\n    \"onAnimationComplete\",\n    \"onUpdate\",\n    \"onDragStart\",\n    \"onDrag\",\n    \"onDragEnd\",\n    \"onMeasureDragConstraints\",\n    \"onDirectionLock\",\n    \"onDragTransitionEnd\",\n    \"_dragX\",\n    \"_dragY\",\n    \"onHoverStart\",\n    \"onHoverEnd\",\n    \"onViewportEnter\",\n    \"onViewportLeave\",\n    \"globalTapTarget\",\n    \"ignoreStrict\",\n    \"viewport\",\n]);\n/**\n * Check whether a prop name is a valid `MotionProp` key.\n *\n * @param key - Name of the property to check\n * @returns `true` is key is a valid `MotionProp`.\n *\n * @public\n */\nfunction isValidMotionProp(key) {\n    return (key.startsWith(\"while\") ||\n        (key.startsWith(\"drag\") && key !== \"draggable\") ||\n        key.startsWith(\"layout\") ||\n        key.startsWith(\"onTap\") ||\n        key.startsWith(\"onPan\") ||\n        key.startsWith(\"onLayout\") ||\n        validMotionProps.has(key));\n}\n\nexport { isValidMotionProp };\n", "import { isValidMotionProp } from '../../../motion/utils/valid-prop.mjs';\n\nlet shouldForward = (key) => !isValidMotionProp(key);\nfunction loadExternalIsValidProp(isValidProp) {\n    if (typeof isValidProp !== \"function\")\n        return;\n    // Explicitly filter our events\n    shouldForward = (key) => key.startsWith(\"on\") ? !isValidMotionProp(key) : isValidProp(key);\n}\n/**\n * Emotion and Styled Components both allow users to pass through arbitrary props to their components\n * to dynamically generate CSS. They both use the `@emotion/is-prop-valid` package to determine which\n * of these should be passed to the underlying DOM node.\n *\n * However, when styling a Motion component `styled(motion.div)`, both packages pass through *all* props\n * as it's seen as an arbitrary component rather than a DOM node. Motion only allows arbitrary props\n * passed through the `custom` prop so it doesn't *need* the payload or computational overhead of\n * `@emotion/is-prop-valid`, however to fix this problem we need to use it.\n *\n * By making it an optionalDependency we can offer this functionality only in the situations where it's\n * actually required.\n */\ntry {\n    /**\n     * We attempt to import this package but require won't be defined in esm environments, in that case\n     * isPropValid will have to be provided via `MotionContext`. In a 6.0.0 this should probably be removed\n     * in favour of explicit injection.\n     */\n    loadExternalIsValidProp(require(\"@emotion/is-prop-valid\").default);\n}\ncatch {\n    // We don't need to actually do anything here - the fallback is the existing `isPropValid`.\n}\nfunction filterProps(props, isDom, forwardMotionProps) {\n    const filteredProps = {};\n    for (const key in props) {\n        /**\n         * values is considered a valid prop by Emotion, so if it's present\n         * this will be rendered out to the DOM unless explicitly filtered.\n         *\n         * We check the type as it could be used with the `feColorMatrix`\n         * element, which we support.\n         */\n        if (key === \"values\" && typeof props.values === \"object\")\n            continue;\n        if (shouldForward(key) ||\n            (forwardMotionProps === true && isValidMotionProp(key)) ||\n            (!isDom && !isValidMotionProp(key)) ||\n            // If trying to use native HTML drag events, forward drag listeners\n            (props[\"draggable\"] &&\n                key.startsWith(\"onDrag\"))) {\n            filteredProps[key] =\n                props[key];\n        }\n    }\n    return filteredProps;\n}\n\nexport { filterProps, loadExternalIsValidProp };\n", "import { warnOnce } from 'motion-utils';\n\nfunction createDOMMotionComponentProxy(componentFactory) {\n    if (typeof Proxy === \"undefined\") {\n        return componentFactory;\n    }\n    /**\n     * A cache of generated `motion` components, e.g `motion.div`, `motion.input` etc.\n     * Rather than generating them anew every render.\n     */\n    const componentCache = new Map();\n    const deprecatedFactoryFunction = (...args) => {\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(false, \"motion() is deprecated. Use motion.create() instead.\");\n        }\n        return componentFactory(...args);\n    };\n    return new Proxy(deprecatedFactoryFunction, {\n        /**\n         * Called when `motion` is referenced with a prop: `motion.div`, `motion.input` etc.\n         * The prop name is passed through as `key` and we can use that to generate a `motion`\n         * DOM component with that name.\n         */\n        get: (_target, key) => {\n            if (key === \"create\")\n                return componentFactory;\n            /**\n             * If this element doesn't exist in the component cache, create it and cache.\n             */\n            if (!componentCache.has(key)) {\n                componentCache.set(key, componentFactory(key));\n            }\n            return componentCache.get(key);\n        },\n    });\n}\n\nexport { createDOMMotionComponentProxy };\n", "\"use client\";\nimport { createContext } from 'react';\n\nconst MotionContext = /* @__PURE__ */ createContext({});\n\nexport { MotionContext };\n", "function isAnimationControls(v) {\n    return (v !== null &&\n        typeof v === \"object\" &&\n        typeof v.start === \"function\");\n}\n\nexport { isAnimationControls };\n", "/**\n * Decides if the supplied variable is variant label\n */\nfunction isVariantLabel(v) {\n    return typeof v === \"string\" || Array.isArray(v);\n}\n\nexport { isVariantLabel };\n", "const variantPriorityOrder = [\n    \"animate\",\n    \"whileInView\",\n    \"whileFocus\",\n    \"whileHover\",\n    \"whileTap\",\n    \"whileDrag\",\n    \"exit\",\n];\nconst variantProps = [\"initial\", ...variantPriorityOrder];\n\nexport { variantPriorityOrder, variantProps };\n", "import { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { isVariantLabel } from './is-variant-label.mjs';\nimport { variantProps } from './variant-props.mjs';\n\nfunction isControllingVariants(props) {\n    return (isAnimationControls(props.animate) ||\n        variantProps.some((name) => isVariantLabel(props[name])));\n}\nfunction isVariantNode(props) {\n    return Boolean(isControllingVariants(props) || props.variants);\n}\n\nexport { isControllingVariants, isVariantNode };\n", "import { isControllingVariants } from '../../render/utils/is-controlling-variants.mjs';\nimport { isVariantLabel } from '../../render/utils/is-variant-label.mjs';\n\nfunction getCurrentTreeVariants(props, context) {\n    if (isControllingVariants(props)) {\n        const { initial, animate } = props;\n        return {\n            initial: initial === false || isVariantLabel(initial)\n                ? initial\n                : undefined,\n            animate: isVariantLabel(animate) ? animate : undefined,\n        };\n    }\n    return props.inherit !== false ? context : {};\n}\n\nexport { getCurrentTreeVariants };\n", "import { useContext, useMemo } from 'react';\nimport { MotionContext } from './index.mjs';\nimport { getCurrentTreeVariants } from './utils.mjs';\n\nfunction useCreateMotionContext(props) {\n    const { initial, animate } = getCurrentTreeVariants(props, useContext(MotionContext));\n    return useMemo(() => ({ initial, animate }), [variantLabelsAsDependency(initial), variantLabelsAsDependency(animate)]);\n}\nfunction variantLabelsAsDependency(prop) {\n    return Array.isArray(prop) ? prop.join(\" \") : prop;\n}\n\nexport { useCreateMotionContext };\n", "const motionComponentSymbol = Symbol.for(\"motionComponentSymbol\");\n\nexport { motionComponentSymbol };\n", "function isRefObject(ref) {\n    return (ref &&\n        typeof ref === \"object\" &&\n        Object.prototype.hasOwnProperty.call(ref, \"current\"));\n}\n\nexport { isRefObject };\n", "import { useCallback } from 'react';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\n\n/**\n * Creates a ref function that, when called, hydrates the provided\n * external ref and VisualElement.\n */\nfunction useMotionRef(visualState, visualElement, externalRef) {\n    return useCallback((instance) => {\n        if (instance) {\n            visualState.onMount && visualState.onMount(instance);\n        }\n        if (visualElement) {\n            if (instance) {\n                visualElement.mount(instance);\n            }\n            else {\n                visualElement.unmount();\n            }\n        }\n        if (externalRef) {\n            if (typeof externalRef === \"function\") {\n                externalRef(instance);\n            }\n            else if (isRefObject(externalRef)) {\n                externalRef.current = instance;\n            }\n        }\n    }, \n    /**\n     * Only pass a new ref callback to <PERSON>act if we've received a visual element\n     * factory. Otherwise we'll be mounting/remounting every time externalRef\n     * or other dependencies change.\n     */\n    [visualElement]);\n}\n\nexport { useMotionRef };\n", "/**\n * Convert camelCase to dash-case properties.\n */\nconst camelToDash = (str) => str.replace(/([a-z])([A-Z])/gu, \"$1-$2\").toLowerCase();\n\nexport { camelToDash };\n", "import { camelToDash } from '../../render/dom/utils/camel-to-dash.mjs';\n\nconst optimizedAppearDataId = \"framerAppearId\";\nconst optimizedAppearDataAttribute = \"data-\" + camelToDash(optimizedAppearDataId);\n\nexport { optimizedAppearDataAttribute, optimizedAppearDataId };\n", "\"use client\";\nimport { createContext } from 'react';\n\n/**\n * Internal, exported only for usage in Framer\n */\nconst SwitchLayoutGroupContext = createContext({});\n\nexport { SwitchLayoutGroupContext };\n", "import { microtask } from 'motion-dom';\nimport { useContext, useRef, useInsertionEffect, useEffect } from 'react';\nimport { optimizedAppearDataAttribute } from '../../animation/optimized-appear/data-id.mjs';\nimport { LazyContext } from '../../context/LazyContext.mjs';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nimport { MotionContext } from '../../context/MotionContext/index.mjs';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { SwitchLayoutGroupContext } from '../../context/SwitchLayoutGroupContext.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\n\nfunction useVisualElement(Component, visualState, props, createVisualElement, ProjectionNodeConstructor) {\n    const { visualElement: parent } = useContext(MotionContext);\n    const lazyContext = useContext(LazyContext);\n    const presenceContext = useContext(PresenceContext);\n    const reducedMotionConfig = useContext(MotionConfigContext).reducedMotion;\n    const visualElementRef = useRef(null);\n    /**\n     * If we haven't preloaded a renderer, check to see if we have one lazy-loaded\n     */\n    createVisualElement = createVisualElement || lazyContext.renderer;\n    if (!visualElementRef.current && createVisualElement) {\n        visualElementRef.current = createVisualElement(Component, {\n            visualState,\n            parent,\n            props,\n            presenceContext,\n            blockInitialAnimation: presenceContext\n                ? presenceContext.initial === false\n                : false,\n            reducedMotionConfig,\n        });\n    }\n    const visualElement = visualElementRef.current;\n    /**\n     * Load Motion gesture and animation features. These are rendered as renderless\n     * components so each feature can optionally make use of React lifecycle methods.\n     */\n    const initialLayoutGroupConfig = useContext(SwitchLayoutGroupContext);\n    if (visualElement &&\n        !visualElement.projection &&\n        ProjectionNodeConstructor &&\n        (visualElement.type === \"html\" || visualElement.type === \"svg\")) {\n        createProjectionNode(visualElementRef.current, props, ProjectionNodeConstructor, initialLayoutGroupConfig);\n    }\n    const isMounted = useRef(false);\n    useInsertionEffect(() => {\n        /**\n         * Check the component has already mounted before calling\n         * `update` unnecessarily. This ensures we skip the initial update.\n         */\n        if (visualElement && isMounted.current) {\n            visualElement.update(props, presenceContext);\n        }\n    });\n    /**\n     * Cache this value as we want to know whether HandoffAppearAnimations\n     * was present on initial render - it will be deleted after this.\n     */\n    const optimisedAppearId = props[optimizedAppearDataAttribute];\n    const wantsHandoff = useRef(Boolean(optimisedAppearId) &&\n        !window.MotionHandoffIsComplete?.(optimisedAppearId) &&\n        window.MotionHasOptimisedAnimation?.(optimisedAppearId));\n    useIsomorphicLayoutEffect(() => {\n        if (!visualElement)\n            return;\n        isMounted.current = true;\n        window.MotionIsMounted = true;\n        visualElement.updateFeatures();\n        microtask.render(visualElement.render);\n        /**\n         * Ideally this function would always run in a useEffect.\n         *\n         * However, if we have optimised appear animations to handoff from,\n         * it needs to happen synchronously to ensure there's no flash of\n         * incorrect styles in the event of a hydration error.\n         *\n         * So if we detect a situtation where optimised appear animations\n         * are running, we use useLayoutEffect to trigger animations.\n         */\n        if (wantsHandoff.current && visualElement.animationState) {\n            visualElement.animationState.animateChanges();\n        }\n    });\n    useEffect(() => {\n        if (!visualElement)\n            return;\n        if (!wantsHandoff.current && visualElement.animationState) {\n            visualElement.animationState.animateChanges();\n        }\n        if (wantsHandoff.current) {\n            // This ensures all future calls to animateChanges() in this component will run in useEffect\n            queueMicrotask(() => {\n                window.MotionHandoffMarkAsComplete?.(optimisedAppearId);\n            });\n            wantsHandoff.current = false;\n        }\n    });\n    return visualElement;\n}\nfunction createProjectionNode(visualElement, props, ProjectionNodeConstructor, initialPromotionConfig) {\n    const { layoutId, layout, drag, dragConstraints, layoutScroll, layoutRoot, layoutCrossfade, } = props;\n    visualElement.projection = new ProjectionNodeConstructor(visualElement.latestValues, props[\"data-framer-portal-id\"]\n        ? undefined\n        : getClosestProjectingNode(visualElement.parent));\n    visualElement.projection.setOptions({\n        layoutId,\n        layout,\n        alwaysMeasureLayout: Boolean(drag) || (dragConstraints && isRefObject(dragConstraints)),\n        visualElement,\n        /**\n         * TODO: Update options in an effect. This could be tricky as it'll be too late\n         * to update by the time layout animations run.\n         * We also need to fix this safeToRemove by linking it up to the one returned by usePresence,\n         * ensuring it gets called if there's no potential layout animations.\n         *\n         */\n        animationType: typeof layout === \"string\" ? layout : \"both\",\n        initialPromotionConfig,\n        crossfade: layoutCrossfade,\n        layoutScroll,\n        layoutRoot,\n    });\n}\nfunction getClosestProjectingNode(visualElement) {\n    if (!visualElement)\n        return undefined;\n    return visualElement.options.allowProjection !== false\n        ? visualElement.projection\n        : getClosestProjectingNode(visualElement.parent);\n}\n\nexport { useVisualElement };\n", "\"use client\";\nimport { jsxs, jsx } from 'react/jsx-runtime';\nimport { warning, invariant } from 'motion-utils';\nimport { forwardRef, useContext } from 'react';\nimport { LayoutGroupContext } from '../context/LayoutGroupContext.mjs';\nimport { LazyContext } from '../context/LazyContext.mjs';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { MotionContext } from '../context/MotionContext/index.mjs';\nimport { useCreateMotionContext } from '../context/MotionContext/create.mjs';\nimport { isBrowser } from '../utils/is-browser.mjs';\nimport { featureDefinitions } from './features/definitions.mjs';\nimport { loadFeatures } from './features/load-features.mjs';\nimport { motionComponentSymbol } from './utils/symbol.mjs';\nimport { useMotionRef } from './utils/use-motion-ref.mjs';\nimport { useVisualElement } from './utils/use-visual-element.mjs';\n\n/**\n * Create a `motion` component.\n *\n * This function accepts a Component argument, which can be either a string (ie \"div\"\n * for `motion.div`), or an actual React component.\n *\n * Alongside this is a config option which provides a way of rendering the provided\n * component \"offline\", or outside the React render cycle.\n */\nfunction createRendererMotionComponent({ preloadedFeatures, createVisualElement, useRender, useVisualState, Component, }) {\n    preloadedFeatures && loadFeatures(preloadedFeatures);\n    function MotionComponent(props, externalRef) {\n        /**\n         * If we need to measure the element we load this functionality in a\n         * separate class component in order to gain access to getSnapshotBeforeUpdate.\n         */\n        let MeasureLayout;\n        const configAndProps = {\n            ...useContext(MotionConfigContext),\n            ...props,\n            layoutId: useLayoutId(props),\n        };\n        const { isStatic } = configAndProps;\n        const context = useCreateMotionContext(props);\n        const visualState = useVisualState(props, isStatic);\n        if (!isStatic && isBrowser) {\n            useStrictMode(configAndProps, preloadedFeatures);\n            const layoutProjection = getProjectionFunctionality(configAndProps);\n            MeasureLayout = layoutProjection.MeasureLayout;\n            /**\n             * Create a VisualElement for this component. A VisualElement provides a common\n             * interface to renderer-specific APIs (ie DOM/Three.js etc) as well as\n             * providing a way of rendering to these APIs outside of the React render loop\n             * for more performant animations and interactions\n             */\n            context.visualElement = useVisualElement(Component, visualState, configAndProps, createVisualElement, layoutProjection.ProjectionNode);\n        }\n        /**\n         * The mount order and hierarchy is specific to ensure our element ref\n         * is hydrated by the time features fire their effects.\n         */\n        return (jsxs(MotionContext.Provider, { value: context, children: [MeasureLayout && context.visualElement ? (jsx(MeasureLayout, { visualElement: context.visualElement, ...configAndProps })) : null, useRender(Component, props, useMotionRef(visualState, context.visualElement, externalRef), visualState, isStatic, context.visualElement)] }));\n    }\n    MotionComponent.displayName = `motion.${typeof Component === \"string\"\n        ? Component\n        : `create(${Component.displayName ?? Component.name ?? \"\"})`}`;\n    const ForwardRefMotionComponent = forwardRef(MotionComponent);\n    ForwardRefMotionComponent[motionComponentSymbol] = Component;\n    return ForwardRefMotionComponent;\n}\nfunction useLayoutId({ layoutId }) {\n    const layoutGroupId = useContext(LayoutGroupContext).id;\n    return layoutGroupId && layoutId !== undefined\n        ? layoutGroupId + \"-\" + layoutId\n        : layoutId;\n}\nfunction useStrictMode(configAndProps, preloadedFeatures) {\n    const isStrict = useContext(LazyContext).strict;\n    /**\n     * If we're in development mode, check to make sure we're not rendering a motion component\n     * as a child of LazyMotion, as this will break the file-size benefits of using it.\n     */\n    if (process.env.NODE_ENV !== \"production\" &&\n        preloadedFeatures &&\n        isStrict) {\n        const strictMessage = \"You have rendered a `motion` component within a `LazyMotion` component. This will break tree shaking. Import and render a `m` component instead.\";\n        configAndProps.ignoreStrict\n            ? warning(false, strictMessage)\n            : invariant(false, strictMessage);\n    }\n}\nfunction getProjectionFunctionality(props) {\n    const { drag, layout } = featureDefinitions;\n    if (!drag && !layout)\n        return {};\n    const combined = { ...drag, ...layout };\n    return {\n        MeasureLayout: drag?.isEnabled(props) || layout?.isEnabled(props)\n            ? combined.MeasureLayout\n            : undefined,\n        ProjectionNode: combined.ProjectionNode,\n    };\n}\n\nexport { createRendererMotionComponent };\n", "import { isCSSVariableName } from 'motion-dom';\n\nconst scaleCorrectors = {};\nfunction addScaleCorrector(correctors) {\n    for (const key in correctors) {\n        scaleCorrectors[key] = correctors[key];\n        if (isCSSVariableName(key)) {\n            scaleCorrectors[key].isCSSVariable = true;\n        }\n    }\n}\n\nexport { addScaleCorrector, scaleCorrectors };\n", "import { transformProps } from 'motion-dom';\nimport { scaleCorrectors } from '../../projection/styles/scale-correction.mjs';\n\nfunction isForcedMotionValue(key, { layout, layoutId }) {\n    return (transformProps.has(key) ||\n        key.startsWith(\"origin\") ||\n        ((layout || layoutId !== undefined) &&\n            (!!scaleCorrectors[key] || key === \"opacity\")));\n}\n\nexport { isForcedMotionValue };\n", "import { transformPropOrder, getValueAsType, numberValueTypes } from 'motion-dom';\n\nconst translateAlias = {\n    x: \"translateX\",\n    y: \"translateY\",\n    z: \"translateZ\",\n    transformPerspective: \"perspective\",\n};\nconst numTransforms = transformPropOrder.length;\n/**\n * Build a CSS transform style from individual x/y/scale etc properties.\n *\n * This outputs with a default order of transforms/scales/rotations, this can be customised by\n * providing a transformTemplate function.\n */\nfunction buildTransform(latestValues, transform, transformTemplate) {\n    // The transform string we're going to build into.\n    let transformString = \"\";\n    let transformIsDefault = true;\n    /**\n     * Loop over all possible transforms in order, adding the ones that\n     * are present to the transform string.\n     */\n    for (let i = 0; i < numTransforms; i++) {\n        const key = transformPropOrder[i];\n        const value = latestValues[key];\n        if (value === undefined)\n            continue;\n        let valueIsDefault = true;\n        if (typeof value === \"number\") {\n            valueIsDefault = value === (key.startsWith(\"scale\") ? 1 : 0);\n        }\n        else {\n            valueIsDefault = parseFloat(value) === 0;\n        }\n        if (!valueIsDefault || transformTemplate) {\n            const valueAsType = getValueAsType(value, numberValueTypes[key]);\n            if (!valueIsDefault) {\n                transformIsDefault = false;\n                const transformName = translateAlias[key] || key;\n                transformString += `${transformName}(${valueAsType}) `;\n            }\n            if (transformTemplate) {\n                transform[key] = valueAsType;\n            }\n        }\n    }\n    transformString = transformString.trim();\n    // If we have a custom `transform` template, pass our transform values and\n    // generated transformString to that before returning\n    if (transformTemplate) {\n        transformString = transformTemplate(transform, transformIsDefault ? \"\" : transformString);\n    }\n    else if (transformIsDefault) {\n        transformString = \"none\";\n    }\n    return transformString;\n}\n\nexport { buildTransform };\n", "import { transformProps, isCSSVariableName, getValueAsType, numberValueTypes } from 'motion-dom';\nimport { buildTransform } from './build-transform.mjs';\n\nfunction buildHTMLStyles(state, latestValues, transformTemplate) {\n    const { style, vars, transformOrigin } = state;\n    // Track whether we encounter any transform or transformOrigin values.\n    let hasTransform = false;\n    let hasTransformOrigin = false;\n    /**\n     * Loop over all our latest animated values and decide whether to handle them\n     * as a style or CSS variable.\n     *\n     * Transforms and transform origins are kept separately for further processing.\n     */\n    for (const key in latestValues) {\n        const value = latestValues[key];\n        if (transformProps.has(key)) {\n            // If this is a transform, flag to enable further transform processing\n            hasTransform = true;\n            continue;\n        }\n        else if (isCSSVariableName(key)) {\n            vars[key] = value;\n            continue;\n        }\n        else {\n            // Convert the value to its default value type, ie 0 -> \"0px\"\n            const valueAsType = getValueAsType(value, numberValueTypes[key]);\n            if (key.startsWith(\"origin\")) {\n                // If this is a transform origin, flag and enable further transform-origin processing\n                hasTransformOrigin = true;\n                transformOrigin[key] =\n                    valueAsType;\n            }\n            else {\n                style[key] = valueAsType;\n            }\n        }\n    }\n    if (!latestValues.transform) {\n        if (hasTransform || transformTemplate) {\n            style.transform = buildTransform(latestValues, state.transform, transformTemplate);\n        }\n        else if (style.transform) {\n            /**\n             * If we have previously created a transform but currently don't have any,\n             * reset transform style to none.\n             */\n            style.transform = \"none\";\n        }\n    }\n    /**\n     * Build a transformOrigin style. Uses the same defaults as the browser for\n     * undefined origins.\n     */\n    if (hasTransformOrigin) {\n        const { originX = \"50%\", originY = \"50%\", originZ = 0, } = transformOrigin;\n        style.transformOrigin = `${originX} ${originY} ${originZ}`;\n    }\n}\n\nexport { buildHTMLStyles };\n", "const createHtmlRenderState = () => ({\n    style: {},\n    transform: {},\n    transformOrigin: {},\n    vars: {},\n});\n\nexport { createHtmlRenderState };\n", "import { isMotionValue } from 'motion-dom';\nimport { useMemo } from 'react';\nimport { isForcedMotionValue } from '../../motion/utils/is-forced-motion-value.mjs';\nimport { buildHTMLStyles } from './utils/build-styles.mjs';\nimport { createHtmlRenderState } from './utils/create-render-state.mjs';\n\nfunction copyRawValuesOnly(target, source, props) {\n    for (const key in source) {\n        if (!isMotionValue(source[key]) && !isForcedMotionValue(key, props)) {\n            target[key] = source[key];\n        }\n    }\n}\nfunction useInitialMotionValues({ transformTemplate }, visualState) {\n    return useMemo(() => {\n        const state = createHtmlRenderState();\n        buildHTMLStyles(state, visualState, transformTemplate);\n        return Object.assign({}, state.vars, state.style);\n    }, [visualState]);\n}\nfunction useStyle(props, visualState) {\n    const styleProp = props.style || {};\n    const style = {};\n    /**\n     * Copy non-Motion Values straight into style\n     */\n    copyRawValuesOnly(style, styleProp, props);\n    Object.assign(style, useInitialMotionValues(props, visualState));\n    return style;\n}\nfunction useHTMLProps(props, visualState) {\n    // The `any` isn't ideal but it is the type of createElement props argument\n    const htmlProps = {};\n    const style = useStyle(props, visualState);\n    if (props.drag && props.dragListener !== false) {\n        // Disable the ghost element when a user drags\n        htmlProps.draggable = false;\n        // Disable text selection\n        style.userSelect =\n            style.WebkitUserSelect =\n                style.WebkitTouchCallout =\n                    \"none\";\n        // Disable scrolling on the draggable direction\n        style.touchAction =\n            props.drag === true\n                ? \"none\"\n                : `pan-${props.drag === \"x\" ? \"y\" : \"x\"}`;\n    }\n    if (props.tabIndex === undefined &&\n        (props.onTap || props.onTapStart || props.whileTap)) {\n        htmlProps.tabIndex = 0;\n    }\n    htmlProps.style = style;\n    return htmlProps;\n}\n\nexport { copyRawValuesOnly, useHTMLProps };\n", "import { px } from 'motion-dom';\n\nconst dashKeys = {\n    offset: \"stroke-dashoffset\",\n    array: \"stroke-dasharray\",\n};\nconst camelKeys = {\n    offset: \"strokeDashoffset\",\n    array: \"strokeDasharray\",\n};\n/**\n * Build SVG path properties. Uses the path's measured length to convert\n * our custom pathLength, pathSpacing and pathOffset into stroke-dashoffset\n * and stroke-dasharray attributes.\n *\n * This function is mutative to reduce per-frame GC.\n */\nfunction buildSVGPath(attrs, length, spacing = 1, offset = 0, useDashCase = true) {\n    // Normalise path length by setting SVG attribute pathLength to 1\n    attrs.pathLength = 1;\n    // We use dash case when setting attributes directly to the DOM node and camel case\n    // when defining props on a React component.\n    const keys = useDashCase ? dashKeys : camelKeys;\n    // Build the dash offset\n    attrs[keys.offset] = px.transform(-offset);\n    // Build the dash array\n    const pathLength = px.transform(length);\n    const pathSpacing = px.transform(spacing);\n    attrs[keys.array] = `${pathLength} ${pathSpacing}`;\n}\n\nexport { buildSVGPath };\n", "import { buildHTMLStyles } from '../../html/utils/build-styles.mjs';\nimport { buildSVGPath } from './path.mjs';\n\n/**\n * Build SVG visual attrbutes, like cx and style.transform\n */\nfunction buildSVGAttrs(state, { attrX, attrY, attrScale, pathLength, pathSpacing = 1, pathOffset = 0, \n// This is object creation, which we try to avoid per-frame.\n...latest }, isSVGTag, transformTemplate, styleProp) {\n    buildHTMLStyles(state, latest, transformTemplate);\n    /**\n     * For svg tags we just want to make sure viewBox is animatable and treat all the styles\n     * as normal HTML tags.\n     */\n    if (isSVGTag) {\n        if (state.style.viewBox) {\n            state.attrs.viewBox = state.style.viewBox;\n        }\n        return;\n    }\n    state.attrs = state.style;\n    state.style = {};\n    const { attrs, style } = state;\n    /**\n     * However, we apply transforms as CSS transforms.\n     * So if we detect a transform, transformOrigin we take it from attrs and copy it into style.\n     */\n    if (attrs.transform) {\n        style.transform = attrs.transform;\n        delete attrs.transform;\n    }\n    if (style.transform || attrs.transformOrigin) {\n        style.transformOrigin = attrs.transformOrigin ?? \"50% 50%\";\n        delete attrs.transformOrigin;\n    }\n    if (style.transform) {\n        /**\n         * SVG's element transform-origin uses its own median as a reference.\n         * Therefore, transformBox becomes a fill-box\n         */\n        style.transformBox = styleProp?.transformBox ?? \"fill-box\";\n        delete attrs.transformBox;\n    }\n    // Render attrX/attrY/attrScale as attributes\n    if (attrX !== undefined)\n        attrs.x = attrX;\n    if (attrY !== undefined)\n        attrs.y = attrY;\n    if (attrScale !== undefined)\n        attrs.scale = attrScale;\n    // Build SVG path if one has been defined\n    if (pathLength !== undefined) {\n        buildSVGPath(attrs, pathLength, pathSpacing, pathOffset, false);\n    }\n}\n\nexport { buildSVGAttrs };\n", "import { createHtmlRenderState } from '../../html/utils/create-render-state.mjs';\n\nconst createSvgRenderState = () => ({\n    ...createHtmlRenderState(),\n    attrs: {},\n});\n\nexport { createSvgRenderState };\n", "const isSVGTag = (tag) => typeof tag === \"string\" && tag.toLowerCase() === \"svg\";\n\nexport { isSVGTag };\n", "import { useMemo } from 'react';\nimport { copyRawValuesOnly } from '../html/use-props.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { createSvgRenderState } from './utils/create-render-state.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\n\nfunction useSVGProps(props, visualState, _isStatic, Component) {\n    const visualProps = useMemo(() => {\n        const state = createSvgRenderState();\n        buildSVGAttrs(state, visualState, isSVGTag(Component), props.transformTemplate, props.style);\n        return {\n            ...state.attrs,\n            style: { ...state.style },\n        };\n    }, [visualState]);\n    if (props.style) {\n        const rawStyles = {};\n        copyRawValuesOnly(rawStyles, props.style, props);\n        visualProps.style = { ...rawStyles, ...visualProps.style };\n    }\n    return visualProps;\n}\n\nexport { useSVGProps };\n", "/**\n * We keep these listed separately as we use the lowercase tag names as part\n * of the runtime bundle to detect SVG components\n */\nconst lowercaseSVGElements = [\n    \"animate\",\n    \"circle\",\n    \"defs\",\n    \"desc\",\n    \"ellipse\",\n    \"g\",\n    \"image\",\n    \"line\",\n    \"filter\",\n    \"marker\",\n    \"mask\",\n    \"metadata\",\n    \"path\",\n    \"pattern\",\n    \"polygon\",\n    \"polyline\",\n    \"rect\",\n    \"stop\",\n    \"switch\",\n    \"symbol\",\n    \"svg\",\n    \"text\",\n    \"tspan\",\n    \"use\",\n    \"view\",\n];\n\nexport { lowercaseSVGElements };\n", "import { lowercaseSVGElements } from '../../svg/lowercase-elements.mjs';\n\nfunction isSVGComponent(Component) {\n    if (\n    /**\n     * If it's not a string, it's a custom React component. Currently we only support\n     * HTML custom React components.\n     */\n    typeof Component !== \"string\" ||\n        /**\n         * If it contains a dash, the element is a custom HTML webcomponent.\n         */\n        Component.includes(\"-\")) {\n        return false;\n    }\n    else if (\n    /**\n     * If it's in our list of lowercase SVG tags, it's an SVG component\n     */\n    lowercaseSVGElements.indexOf(Component) > -1 ||\n        /**\n         * If it contains a capital letter, it's an SVG component\n         */\n        /[A-Z]/u.test(Component)) {\n        return true;\n    }\n    return false;\n}\n\nexport { isSVGComponent };\n", "import { isMotionValue } from 'motion-dom';\nimport { Fragment, useMemo, createElement } from 'react';\nimport { useHTMLProps } from '../html/use-props.mjs';\nimport { useSVGProps } from '../svg/use-props.mjs';\nimport { filterProps } from './utils/filter-props.mjs';\nimport { isSVGComponent } from './utils/is-svg-component.mjs';\n\nfunction createUseRender(forwardMotionProps = false) {\n    const useRender = (Component, props, ref, { latestValues }, isStatic) => {\n        const useVisualProps = isSVGComponent(Component)\n            ? useSVGProps\n            : useHTMLProps;\n        const visualProps = useVisualProps(props, latestValues, isStatic, Component);\n        const filteredProps = filterProps(props, typeof Component === \"string\", forwardMotionProps);\n        const elementProps = Component !== Fragment\n            ? { ...filteredProps, ...visualProps, ref }\n            : {};\n        /**\n         * If component has been handed a motion value as its child,\n         * memoise its initial value and render that. Subsequent updates\n         * will be handled by the onChange handler\n         */\n        const { children } = props;\n        const renderedChildren = useMemo(() => (isMotionValue(children) ? children.get() : children), [children]);\n        return createElement(Component, {\n            ...elementProps,\n            children: renderedChildren,\n        });\n    };\n    return useRender;\n}\n\nexport { createUseRender };\n", "function getValueState(visualElement) {\n    const state = [{}, {}];\n    visualElement?.values.forEach((value, key) => {\n        state[0][key] = value.get();\n        state[1][key] = value.getVelocity();\n    });\n    return state;\n}\nfunction resolveVariantFromProps(props, definition, custom, visualElement) {\n    /**\n     * If the variant definition is a function, resolve.\n     */\n    if (typeof definition === \"function\") {\n        const [current, velocity] = getValueState(visualElement);\n        definition = definition(custom !== undefined ? custom : props.custom, current, velocity);\n    }\n    /**\n     * If the variant definition is a variant label, or\n     * the function returned a variant label, resolve.\n     */\n    if (typeof definition === \"string\") {\n        definition = props.variants && props.variants[definition];\n    }\n    /**\n     * At this point we've resolved both functions and variant labels,\n     * but the resolved variant label might itself have been a function.\n     * If so, resolve. This can only have returned a valid target object.\n     */\n    if (typeof definition === \"function\") {\n        const [current, velocity] = getValueState(visualElement);\n        definition = definition(custom !== undefined ? custom : props.custom, current, velocity);\n    }\n    return definition;\n}\n\nexport { resolveVariantFromProps };\n", "import { isMotionValue } from 'motion-dom';\n\n/**\n * If the provided value is a MotionValue, this returns the actual value, otherwise just the value itself\n *\n * TODO: Remove and move to library\n */\nfunction resolveMotionValue(value) {\n    return isMotionValue(value) ? value.get() : value;\n}\n\nexport { resolveMotionValue };\n", "import { useContext } from 'react';\nimport { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { MotionContext } from '../../context/MotionContext/index.mjs';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { isControllingVariants, isVariantNode } from '../../render/utils/is-controlling-variants.mjs';\nimport { resolveVariantFromProps } from '../../render/utils/resolve-variants.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\n\nfunction makeState({ scrapeMotionValuesFromProps, createRenderState, }, props, context, presenceContext) {\n    const state = {\n        latestValues: makeLatestValues(props, context, presenceContext, scrapeMotionValuesFromProps),\n        renderState: createRenderState(),\n    };\n    return state;\n}\nconst makeUseVisualState = (config) => (props, isStatic) => {\n    const context = useContext(MotionContext);\n    const presenceContext = useContext(PresenceContext);\n    const make = () => makeState(config, props, context, presenceContext);\n    return isStatic ? make() : useConstant(make);\n};\nfunction makeLatestValues(props, context, presenceContext, scrapeMotionValues) {\n    const values = {};\n    const motionValues = scrapeMotionValues(props, {});\n    for (const key in motionValues) {\n        values[key] = resolveMotionValue(motionValues[key]);\n    }\n    let { initial, animate } = props;\n    const isControllingVariants$1 = isControllingVariants(props);\n    const isVariantNode$1 = isVariantNode(props);\n    if (context &&\n        isVariantNode$1 &&\n        !isControllingVariants$1 &&\n        props.inherit !== false) {\n        if (initial === undefined)\n            initial = context.initial;\n        if (animate === undefined)\n            animate = context.animate;\n    }\n    let isInitialAnimationBlocked = presenceContext\n        ? presenceContext.initial === false\n        : false;\n    isInitialAnimationBlocked = isInitialAnimationBlocked || initial === false;\n    const variantToSet = isInitialAnimationBlocked ? animate : initial;\n    if (variantToSet &&\n        typeof variantToSet !== \"boolean\" &&\n        !isAnimationControls(variantToSet)) {\n        const list = Array.isArray(variantToSet) ? variantToSet : [variantToSet];\n        for (let i = 0; i < list.length; i++) {\n            const resolved = resolveVariantFromProps(props, list[i]);\n            if (resolved) {\n                const { transitionEnd, transition, ...target } = resolved;\n                for (const key in target) {\n                    let valueTarget = target[key];\n                    if (Array.isArray(valueTarget)) {\n                        /**\n                         * Take final keyframe if the initial animation is blocked because\n                         * we want to initialise at the end of that blocked animation.\n                         */\n                        const index = isInitialAnimationBlocked\n                            ? valueTarget.length - 1\n                            : 0;\n                        valueTarget = valueTarget[index];\n                    }\n                    if (valueTarget !== null) {\n                        values[key] = valueTarget;\n                    }\n                }\n                for (const key in transitionEnd) {\n                    values[key] = transitionEnd[key];\n                }\n            }\n        }\n    }\n    return values;\n}\n\nexport { makeUseVisualState };\n", "import { isMotionValue } from 'motion-dom';\nimport { isForcedMotionValue } from '../../../motion/utils/is-forced-motion-value.mjs';\n\nfunction scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n    const { style } = props;\n    const newValues = {};\n    for (const key in style) {\n        if (isMotionValue(style[key]) ||\n            (prevProps.style &&\n                isMotionValue(prevProps.style[key])) ||\n            isForcedMotionValue(key, props) ||\n            visualElement?.getValue(key)?.liveStyle !== undefined) {\n            newValues[key] = style[key];\n        }\n    }\n    return newValues;\n}\n\nexport { scrapeMotionValuesFromProps };\n", "import { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nimport { createHtmlRenderState } from './utils/create-render-state.mjs';\n\nconst htmlMotionConfig = {\n    useVisualState: makeUseVisualState({\n        scrapeMotionValuesFromProps,\n        createRenderState: createHtmlRenderState,\n    }),\n};\n\nexport { htmlMotionConfig };\n", "import { isMotionValue, transformPropOrder } from 'motion-dom';\nimport { scrapeMotionValuesFromProps as scrapeMotionValuesFromProps$1 } from '../../html/utils/scrape-motion-values.mjs';\n\nfunction scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n    const newValues = scrapeMotionValuesFromProps$1(props, prevProps, visualElement);\n    for (const key in props) {\n        if (isMotionValue(props[key]) ||\n            isMotionValue(prevProps[key])) {\n            const targetKey = transformPropOrder.indexOf(key) !== -1\n                ? \"attr\" + key.charAt(0).toUpperCase() + key.substring(1)\n                : key;\n            newValues[targetKey] = props[key];\n        }\n    }\n    return newValues;\n}\n\nexport { scrapeMotionValuesFromProps };\n", "import { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { createSvgRenderState } from './utils/create-render-state.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\n\nconst svgMotionConfig = {\n    useVisualState: makeUseVisualState({\n        scrapeMotionValuesFromProps: scrapeMotionValuesFromProps,\n        createRenderState: createSvgRenderState,\n    }),\n};\n\nexport { svgMotionConfig };\n", "import { createRendererMotionComponent } from '../../motion/index.mjs';\nimport { createUseRender } from '../dom/use-render.mjs';\nimport { isSVGComponent } from '../dom/utils/is-svg-component.mjs';\nimport { htmlMotionConfig } from '../html/config-motion.mjs';\nimport { svgMotionConfig } from '../svg/config-motion.mjs';\n\nfunction createMotionComponentFactory(preloadedFeatures, createVisualElement) {\n    return function createMotionComponent(Component, { forwardMotionProps } = { forwardMotionProps: false }) {\n        const baseConfig = isSVGComponent(Component)\n            ? svgMotionConfig\n            : htmlMotionConfig;\n        const config = {\n            ...baseConfig,\n            preloadedFeatures,\n            useRender: createUseRender(forwardMotionProps),\n            createVisualElement,\n            Component,\n        };\n        return createRendererMotionComponent(config);\n    };\n}\n\nexport { createMotionComponentFactory };\n", "import { resolveVariantFromProps } from './resolve-variants.mjs';\n\nfunction resolveVariant(visualElement, definition, custom) {\n    const props = visualElement.getProps();\n    return resolveVariantFromProps(props, definition, custom !== undefined ? custom : props.custom, visualElement);\n}\n\nexport { resolveVariant };\n", "const isKeyframesTarget = (v) => {\n    return Array.isArray(v);\n};\n\nexport { isKeyframesTarget };\n", "import { motionValue } from 'motion-dom';\nimport { isKeyframesTarget } from '../../animation/utils/is-keyframes-target.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\n\n/**\n * Set VisualElement's MotionValue, creating a new MotionValue for it if\n * it doesn't exist.\n */\nfunction setMotionValue(visualElement, key, value) {\n    if (visualElement.hasValue(key)) {\n        visualElement.getValue(key).set(value);\n    }\n    else {\n        visualElement.addValue(key, motionValue(value));\n    }\n}\nfunction resolveFinalValueInKeyframes(v) {\n    // TODO maybe throw if v.length - 1 is placeholder token?\n    return isKeyframesTarget(v) ? v[v.length - 1] || 0 : v;\n}\nfunction setTarget(visualElement, definition) {\n    const resolved = resolveVariant(visualElement, definition);\n    let { transitionEnd = {}, transition = {}, ...target } = resolved || {};\n    target = { ...target, ...transitionEnd };\n    for (const key in target) {\n        const value = resolveFinalValueInKeyframes(target[key]);\n        setMotionValue(visualElement, key, value);\n    }\n}\n\nexport { setTarget };\n", "import { isMotionValue } from 'motion-dom';\n\nfunction isWillChangeMotionValue(value) {\n    return Boolean(isMotionValue(value) && value.add);\n}\n\nexport { isWillChangeMotionValue };\n", "import { MotionGlobalConfig } from 'motion-utils';\nimport { isWillChangeMotionValue } from './is.mjs';\n\nfunction addValueToWillChange(visualElement, key) {\n    const willChange = visualElement.getValue(\"willChange\");\n    /**\n     * It could be that a user has set will<PERSON>hange to a regular MotionValue,\n     * in which case we can't add the value to it.\n     */\n    if (isWillChangeMotionValue(willChange)) {\n        return willChange.add(key);\n    }\n    else if (!willChange && MotionGlobalConfig.WillChange) {\n        const newWillChange = new MotionGlobalConfig.WillChange(\"auto\");\n        visualElement.addValue(\"willChange\", newWillChange);\n        newWillChange.add(key);\n    }\n}\n\nexport { addValueToWillChange };\n", "import { optimizedAppearDataAttribute } from './data-id.mjs';\n\nfunction getOptimisedAppearId(visualElement) {\n    return visualElement.props[optimizedAppearDataAttribute];\n}\n\nexport { getOptimisedAppearId };\n", "const isNotNull = (value) => value !== null;\nfunction getFinalKeyframe(keyframes, { repeat, repeatType = \"loop\" }, finalKeyframe) {\n    const resolvedKeyframes = keyframes.filter(isNotNull);\n    const index = repeat && repeatType !== \"loop\" && repeat % 2 === 1\n        ? 0\n        : resolvedKeyframes.length - 1;\n    return !index || finalKeyframe === undefined\n        ? resolvedKeyframes[index]\n        : finalKeyframe;\n}\n\nexport { getFinalKeyframe };\n", "import { transformProps } from 'motion-dom';\n\nconst underDampedSpring = {\n    type: \"spring\",\n    stiffness: 500,\n    damping: 25,\n    restSpeed: 10,\n};\nconst criticallyDampedSpring = (target) => ({\n    type: \"spring\",\n    stiffness: 550,\n    damping: target === 0 ? 2 * Math.sqrt(550) : 30,\n    restSpeed: 10,\n});\nconst keyframesTransition = {\n    type: \"keyframes\",\n    duration: 0.8,\n};\n/**\n * Default easing curve is a slightly shallower version of\n * the default browser easing curve.\n */\nconst ease = {\n    type: \"keyframes\",\n    ease: [0.25, 0.1, 0.35, 1],\n    duration: 0.3,\n};\nconst getDefaultTransition = (valueKey, { keyframes }) => {\n    if (keyframes.length > 2) {\n        return keyframesTransition;\n    }\n    else if (transformProps.has(valueKey)) {\n        return valueKey.startsWith(\"scale\")\n            ? criticallyDampedSpring(keyframes[1])\n            : underDampedSpring;\n    }\n    return ease;\n};\n\nexport { getDefaultTransition };\n", "/**\n * Decide whether a transition is defined on a given Transition.\n * This filters out orchestration options and returns true\n * if any options are left.\n */\nfunction isTransitionDefined({ when, delay: _delay, delayChildren, staggerChildren, staggerDirection, repeat, repeatType, repeatDelay, from, elapsed, ...transition }) {\n    return !!Object.keys(transition).length;\n}\n\nexport { isTransitionDefined };\n", "import { getValueTransition, frame, JSAnimation, AsyncMotionValueAnimation } from 'motion-dom';\nimport { secondsToMilliseconds, MotionGlobalConfig } from 'motion-utils';\nimport { getFinalKeyframe } from '../animators/waapi/utils/get-final-keyframe.mjs';\nimport { getDefaultTransition } from '../utils/default-transitions.mjs';\nimport { isTransitionDefined } from '../utils/is-transition-defined.mjs';\n\nconst animateMotionValue = (name, value, target, transition = {}, element, isHandoff) => (onComplete) => {\n    const valueTransition = getValueTransition(transition, name) || {};\n    /**\n     * Most transition values are currently completely overwritten by value-specific\n     * transitions. In the future it'd be nicer to blend these transitions. But for now\n     * delay actually does inherit from the root transition if not value-specific.\n     */\n    const delay = valueTransition.delay || transition.delay || 0;\n    /**\n     * Elapsed isn't a public transition option but can be passed through from\n     * optimized appear effects in milliseconds.\n     */\n    let { elapsed = 0 } = transition;\n    elapsed = elapsed - secondsToMilliseconds(delay);\n    const options = {\n        keyframes: Array.isArray(target) ? target : [null, target],\n        ease: \"easeOut\",\n        velocity: value.getVelocity(),\n        ...valueTransition,\n        delay: -elapsed,\n        onUpdate: (v) => {\n            value.set(v);\n            valueTransition.onUpdate && valueTransition.onUpdate(v);\n        },\n        onComplete: () => {\n            onComplete();\n            valueTransition.onComplete && valueTransition.onComplete();\n        },\n        name,\n        motionValue: value,\n        element: isHandoff ? undefined : element,\n    };\n    /**\n     * If there's no transition defined for this value, we can generate\n     * unique transition settings for this value.\n     */\n    if (!isTransitionDefined(valueTransition)) {\n        Object.assign(options, getDefaultTransition(name, options));\n    }\n    /**\n     * Both WAAPI and our internal animation functions use durations\n     * as defined by milliseconds, while our external API defines them\n     * as seconds.\n     */\n    options.duration && (options.duration = secondsToMilliseconds(options.duration));\n    options.repeatDelay && (options.repeatDelay = secondsToMilliseconds(options.repeatDelay));\n    /**\n     * Support deprecated way to set initial value. Prefer keyframe syntax.\n     */\n    if (options.from !== undefined) {\n        options.keyframes[0] = options.from;\n    }\n    let shouldSkip = false;\n    if (options.type === false ||\n        (options.duration === 0 && !options.repeatDelay)) {\n        options.duration = 0;\n        if (options.delay === 0) {\n            shouldSkip = true;\n        }\n    }\n    if (MotionGlobalConfig.instantAnimations ||\n        MotionGlobalConfig.skipAnimations) {\n        shouldSkip = true;\n        options.duration = 0;\n        options.delay = 0;\n    }\n    /**\n     * If the transition type or easing has been explicitly set by the user\n     * then we don't want to allow flattening the animation.\n     */\n    options.allowFlatten = !valueTransition.type && !valueTransition.ease;\n    /**\n     * If we can or must skip creating the animation, and apply only\n     * the final keyframe, do so. We also check once keyframes are resolved but\n     * this early check prevents the need to create an animation at all.\n     */\n    if (shouldSkip && !isHandoff && value.get() !== undefined) {\n        const finalKeyframe = getFinalKeyframe(options.keyframes, valueTransition);\n        if (finalKeyframe !== undefined) {\n            frame.update(() => {\n                options.onUpdate(finalKeyframe);\n                options.onComplete();\n            });\n            return;\n        }\n    }\n    return valueTransition.isSync\n        ? new JSAnimation(options)\n        : new AsyncMotionValueAnimation(options);\n};\n\nexport { animateMotionValue };\n", "import { getValueTransition, frame, positionalKeys } from 'motion-dom';\nimport { setTarget } from '../../render/utils/setters.mjs';\nimport { addValueToWillChange } from '../../value/use-will-change/add-will-change.mjs';\nimport { getOptimisedAppearId } from '../optimized-appear/get-appear-id.mjs';\nimport { animateMotionValue } from './motion-value.mjs';\n\n/**\n * Decide whether we should block this animation. Previously, we achieved this\n * just by checking whether the key was listed in protectedKeys, but this\n * posed problems if an animation was triggered by afterChildren and protectedKeys\n * had been set to true in the meantime.\n */\nfunction shouldBlockAnimation({ protectedKeys, needsAnimating }, key) {\n    const shouldBlock = protectedKeys.hasOwnProperty(key) && needsAnimating[key] !== true;\n    needsAnimating[key] = false;\n    return shouldBlock;\n}\nfunction animateTarget(visualElement, targetAndTransition, { delay = 0, transitionOverride, type } = {}) {\n    let { transition = visualElement.getDefaultTransition(), transitionEnd, ...target } = targetAndTransition;\n    if (transitionOverride)\n        transition = transitionOverride;\n    const animations = [];\n    const animationTypeState = type &&\n        visualElement.animationState &&\n        visualElement.animationState.getState()[type];\n    for (const key in target) {\n        const value = visualElement.getValue(key, visualElement.latestValues[key] ?? null);\n        const valueTarget = target[key];\n        if (valueTarget === undefined ||\n            (animationTypeState &&\n                shouldBlockAnimation(animationTypeState, key))) {\n            continue;\n        }\n        const valueTransition = {\n            delay,\n            ...getValueTransition(transition || {}, key),\n        };\n        /**\n         * If the value is already at the defined target, skip the animation.\n         */\n        const currentValue = value.get();\n        if (currentValue !== undefined &&\n            !value.isAnimating &&\n            !Array.isArray(valueTarget) &&\n            valueTarget === currentValue &&\n            !valueTransition.velocity) {\n            continue;\n        }\n        /**\n         * If this is the first time a value is being animated, check\n         * to see if we're handling off from an existing animation.\n         */\n        let isHandoff = false;\n        if (window.MotionHandoffAnimation) {\n            const appearId = getOptimisedAppearId(visualElement);\n            if (appearId) {\n                const startTime = window.MotionHandoffAnimation(appearId, key, frame);\n                if (startTime !== null) {\n                    valueTransition.startTime = startTime;\n                    isHandoff = true;\n                }\n            }\n        }\n        addValueToWillChange(visualElement, key);\n        value.start(animateMotionValue(key, value, valueTarget, visualElement.shouldReduceMotion && positionalKeys.has(key)\n            ? { type: false }\n            : valueTransition, visualElement, isHandoff));\n        const animation = value.animation;\n        if (animation) {\n            animations.push(animation);\n        }\n    }\n    if (transitionEnd) {\n        Promise.all(animations).then(() => {\n            frame.update(() => {\n                transitionEnd && setTarget(visualElement, transitionEnd);\n            });\n        });\n    }\n    return animations;\n}\n\nexport { animateTarget };\n", "import { resolveVariant } from '../../render/utils/resolve-dynamic-variants.mjs';\nimport { animateTarget } from './visual-element-target.mjs';\n\nfunction animateVariant(visualElement, variant, options = {}) {\n    const resolved = resolveVariant(visualElement, variant, options.type === \"exit\"\n        ? visualElement.presenceContext?.custom\n        : undefined);\n    let { transition = visualElement.getDefaultTransition() || {} } = resolved || {};\n    if (options.transitionOverride) {\n        transition = options.transitionOverride;\n    }\n    /**\n     * If we have a variant, create a callback that runs it as an animation.\n     * Otherwise, we resolve a Promise immediately for a composable no-op.\n     */\n    const getAnimation = resolved\n        ? () => Promise.all(animateTarget(visualElement, resolved, options))\n        : () => Promise.resolve();\n    /**\n     * If we have children, create a callback that runs all their animations.\n     * Otherwise, we resolve a Promise immediately for a composable no-op.\n     */\n    const getChildAnimations = visualElement.variantChildren && visualElement.variantChildren.size\n        ? (forwardDelay = 0) => {\n            const { delayChildren = 0, staggerChildren, staggerDirection, } = transition;\n            return animateChildren(visualElement, variant, delayChildren + forwardDelay, staggerChildren, staggerDirection, options);\n        }\n        : () => Promise.resolve();\n    /**\n     * If the transition explicitly defines a \"when\" option, we need to resolve either\n     * this animation or all children animations before playing the other.\n     */\n    const { when } = transition;\n    if (when) {\n        const [first, last] = when === \"beforeChildren\"\n            ? [getAnimation, getChildAnimations]\n            : [getChildAnimations, getAnimation];\n        return first().then(() => last());\n    }\n    else {\n        return Promise.all([getAnimation(), getChildAnimations(options.delay)]);\n    }\n}\nfunction animateChildren(visualElement, variant, delayChildren = 0, staggerChildren = 0, staggerDirection = 1, options) {\n    const animations = [];\n    const maxStaggerDuration = (visualElement.variantChildren.size - 1) * staggerChildren;\n    const generateStaggerDuration = staggerDirection === 1\n        ? (i = 0) => i * staggerChildren\n        : (i = 0) => maxStaggerDuration - i * staggerChildren;\n    Array.from(visualElement.variantChildren)\n        .sort(sortByTreeOrder)\n        .forEach((child, i) => {\n        child.notify(\"AnimationStart\", variant);\n        animations.push(animateVariant(child, variant, {\n            ...options,\n            delay: delayChildren + generateStaggerDuration(i),\n        }).then(() => child.notify(\"AnimationComplete\", variant)));\n    });\n    return Promise.all(animations);\n}\nfunction sortByTreeOrder(a, b) {\n    return a.sortNodePosition(b);\n}\n\nexport { animateVariant, sortByTreeOrder };\n", "import { resolveVariant } from '../../render/utils/resolve-dynamic-variants.mjs';\nimport { animateTarget } from './visual-element-target.mjs';\nimport { animateVariant } from './visual-element-variant.mjs';\n\nfunction animateVisualElement(visualElement, definition, options = {}) {\n    visualElement.notify(\"AnimationStart\", definition);\n    let animation;\n    if (Array.isArray(definition)) {\n        const animations = definition.map((variant) => animateVariant(visualElement, variant, options));\n        animation = Promise.all(animations);\n    }\n    else if (typeof definition === \"string\") {\n        animation = animateVariant(visualElement, definition, options);\n    }\n    else {\n        const resolvedDefinition = typeof definition === \"function\"\n            ? resolveVariant(visualElement, definition, options.custom)\n            : definition;\n        animation = Promise.all(animateTarget(visualElement, resolvedDefinition, options));\n    }\n    return animation.then(() => {\n        visualElement.notify(\"AnimationComplete\", definition);\n    });\n}\n\nexport { animateVisualElement };\n", "function shallowCompare(next, prev) {\n    if (!Array.isArray(prev))\n        return false;\n    const prevLength = prev.length;\n    if (prevLength !== next.length)\n        return false;\n    for (let i = 0; i < prevLength; i++) {\n        if (prev[i] !== next[i])\n            return false;\n    }\n    return true;\n}\n\nexport { shallowCompare };\n", "import { isVariantLabel } from './is-variant-label.mjs';\nimport { variantProps } from './variant-props.mjs';\n\nconst numVariantProps = variantProps.length;\nfunction getVariantContext(visualElement) {\n    if (!visualElement)\n        return undefined;\n    if (!visualElement.isControllingVariants) {\n        const context = visualElement.parent\n            ? getVariantContext(visualElement.parent) || {}\n            : {};\n        if (visualElement.props.initial !== undefined) {\n            context.initial = visualElement.props.initial;\n        }\n        return context;\n    }\n    const context = {};\n    for (let i = 0; i < numVariantProps; i++) {\n        const name = variantProps[i];\n        const prop = visualElement.props[name];\n        if (isVariantLabel(prop) || prop === false) {\n            context[name] = prop;\n        }\n    }\n    return context;\n}\n\nexport { getVariantContext };\n", "import { animateVisualElement } from '../../animation/interfaces/visual-element.mjs';\nimport { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { isKeyframesTarget } from '../../animation/utils/is-keyframes-target.mjs';\nimport { shallowCompare } from '../../utils/shallow-compare.mjs';\nimport { getVariantContext } from './get-variant-context.mjs';\nimport { isVariantLabel } from './is-variant-label.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\nimport { variantPriorityOrder } from './variant-props.mjs';\n\nconst reversePriorityOrder = [...variantPriorityOrder].reverse();\nconst numAnimationTypes = variantPriorityOrder.length;\nfunction animateList(visualElement) {\n    return (animations) => Promise.all(animations.map(({ animation, options }) => animateVisualElement(visualElement, animation, options)));\n}\nfunction createAnimationState(visualElement) {\n    let animate = animateList(visualElement);\n    let state = createState();\n    let isInitialRender = true;\n    /**\n     * This function will be used to reduce the animation definitions for\n     * each active animation type into an object of resolved values for it.\n     */\n    const buildResolvedTypeValues = (type) => (acc, definition) => {\n        const resolved = resolveVariant(visualElement, definition, type === \"exit\"\n            ? visualElement.presenceContext?.custom\n            : undefined);\n        if (resolved) {\n            const { transition, transitionEnd, ...target } = resolved;\n            acc = { ...acc, ...target, ...transitionEnd };\n        }\n        return acc;\n    };\n    /**\n     * This just allows us to inject mocked animation functions\n     * @internal\n     */\n    function setAnimateFunction(makeAnimator) {\n        animate = makeAnimator(visualElement);\n    }\n    /**\n     * When we receive new props, we need to:\n     * 1. Create a list of protected keys for each type. This is a directory of\n     *    value keys that are currently being \"handled\" by types of a higher priority\n     *    so that whenever an animation is played of a given type, these values are\n     *    protected from being animated.\n     * 2. Determine if an animation type needs animating.\n     * 3. Determine if any values have been removed from a type and figure out\n     *    what to animate those to.\n     */\n    function animateChanges(changedActiveType) {\n        const { props } = visualElement;\n        const context = getVariantContext(visualElement.parent) || {};\n        /**\n         * A list of animations that we'll build into as we iterate through the animation\n         * types. This will get executed at the end of the function.\n         */\n        const animations = [];\n        /**\n         * Keep track of which values have been removed. Then, as we hit lower priority\n         * animation types, we can check if they contain removed values and animate to that.\n         */\n        const removedKeys = new Set();\n        /**\n         * A dictionary of all encountered keys. This is an object to let us build into and\n         * copy it without iteration. Each time we hit an animation type we set its protected\n         * keys - the keys its not allowed to animate - to the latest version of this object.\n         */\n        let encounteredKeys = {};\n        /**\n         * If a variant has been removed at a given index, and this component is controlling\n         * variant animations, we want to ensure lower-priority variants are forced to animate.\n         */\n        let removedVariantIndex = Infinity;\n        /**\n         * Iterate through all animation types in reverse priority order. For each, we want to\n         * detect which values it's handling and whether or not they've changed (and therefore\n         * need to be animated). If any values have been removed, we want to detect those in\n         * lower priority props and flag for animation.\n         */\n        for (let i = 0; i < numAnimationTypes; i++) {\n            const type = reversePriorityOrder[i];\n            const typeState = state[type];\n            const prop = props[type] !== undefined\n                ? props[type]\n                : context[type];\n            const propIsVariant = isVariantLabel(prop);\n            /**\n             * If this type has *just* changed isActive status, set activeDelta\n             * to that status. Otherwise set to null.\n             */\n            const activeDelta = type === changedActiveType ? typeState.isActive : null;\n            if (activeDelta === false)\n                removedVariantIndex = i;\n            /**\n             * If this prop is an inherited variant, rather than been set directly on the\n             * component itself, we want to make sure we allow the parent to trigger animations.\n             *\n             * TODO: Can probably change this to a !isControllingVariants check\n             */\n            let isInherited = prop === context[type] &&\n                prop !== props[type] &&\n                propIsVariant;\n            /**\n             *\n             */\n            if (isInherited &&\n                isInitialRender &&\n                visualElement.manuallyAnimateOnMount) {\n                isInherited = false;\n            }\n            /**\n             * Set all encountered keys so far as the protected keys for this type. This will\n             * be any key that has been animated or otherwise handled by active, higher-priortiy types.\n             */\n            typeState.protectedKeys = { ...encounteredKeys };\n            // Check if we can skip analysing this prop early\n            if (\n            // If it isn't active and hasn't *just* been set as inactive\n            (!typeState.isActive && activeDelta === null) ||\n                // If we didn't and don't have any defined prop for this animation type\n                (!prop && !typeState.prevProp) ||\n                // Or if the prop doesn't define an animation\n                isAnimationControls(prop) ||\n                typeof prop === \"boolean\") {\n                continue;\n            }\n            /**\n             * As we go look through the values defined on this type, if we detect\n             * a changed value or a value that was removed in a higher priority, we set\n             * this to true and add this prop to the animation list.\n             */\n            const variantDidChange = checkVariantsDidChange(typeState.prevProp, prop);\n            let shouldAnimateType = variantDidChange ||\n                // If we're making this variant active, we want to always make it active\n                (type === changedActiveType &&\n                    typeState.isActive &&\n                    !isInherited &&\n                    propIsVariant) ||\n                // If we removed a higher-priority variant (i is in reverse order)\n                (i > removedVariantIndex && propIsVariant);\n            let handledRemovedValues = false;\n            /**\n             * As animations can be set as variant lists, variants or target objects, we\n             * coerce everything to an array if it isn't one already\n             */\n            const definitionList = Array.isArray(prop) ? prop : [prop];\n            /**\n             * Build an object of all the resolved values. We'll use this in the subsequent\n             * animateChanges calls to determine whether a value has changed.\n             */\n            let resolvedValues = definitionList.reduce(buildResolvedTypeValues(type), {});\n            if (activeDelta === false)\n                resolvedValues = {};\n            /**\n             * Now we need to loop through all the keys in the prev prop and this prop,\n             * and decide:\n             * 1. If the value has changed, and needs animating\n             * 2. If it has been removed, and needs adding to the removedKeys set\n             * 3. If it has been removed in a higher priority type and needs animating\n             * 4. If it hasn't been removed in a higher priority but hasn't changed, and\n             *    needs adding to the type's protectedKeys list.\n             */\n            const { prevResolvedValues = {} } = typeState;\n            const allKeys = {\n                ...prevResolvedValues,\n                ...resolvedValues,\n            };\n            const markToAnimate = (key) => {\n                shouldAnimateType = true;\n                if (removedKeys.has(key)) {\n                    handledRemovedValues = true;\n                    removedKeys.delete(key);\n                }\n                typeState.needsAnimating[key] = true;\n                const motionValue = visualElement.getValue(key);\n                if (motionValue)\n                    motionValue.liveStyle = false;\n            };\n            for (const key in allKeys) {\n                const next = resolvedValues[key];\n                const prev = prevResolvedValues[key];\n                // If we've already handled this we can just skip ahead\n                if (encounteredKeys.hasOwnProperty(key))\n                    continue;\n                /**\n                 * If the value has changed, we probably want to animate it.\n                 */\n                let valueHasChanged = false;\n                if (isKeyframesTarget(next) && isKeyframesTarget(prev)) {\n                    valueHasChanged = !shallowCompare(next, prev);\n                }\n                else {\n                    valueHasChanged = next !== prev;\n                }\n                if (valueHasChanged) {\n                    if (next !== undefined && next !== null) {\n                        // If next is defined and doesn't equal prev, it needs animating\n                        markToAnimate(key);\n                    }\n                    else {\n                        // If it's undefined, it's been removed.\n                        removedKeys.add(key);\n                    }\n                }\n                else if (next !== undefined && removedKeys.has(key)) {\n                    /**\n                     * If next hasn't changed and it isn't undefined, we want to check if it's\n                     * been removed by a higher priority\n                     */\n                    markToAnimate(key);\n                }\n                else {\n                    /**\n                     * If it hasn't changed, we add it to the list of protected values\n                     * to ensure it doesn't get animated.\n                     */\n                    typeState.protectedKeys[key] = true;\n                }\n            }\n            /**\n             * Update the typeState so next time animateChanges is called we can compare the\n             * latest prop and resolvedValues to these.\n             */\n            typeState.prevProp = prop;\n            typeState.prevResolvedValues = resolvedValues;\n            /**\n             *\n             */\n            if (typeState.isActive) {\n                encounteredKeys = { ...encounteredKeys, ...resolvedValues };\n            }\n            if (isInitialRender && visualElement.blockInitialAnimation) {\n                shouldAnimateType = false;\n            }\n            /**\n             * If this is an inherited prop we want to skip this animation\n             * unless the inherited variants haven't changed on this render.\n             */\n            const willAnimateViaParent = isInherited && variantDidChange;\n            const needsAnimating = !willAnimateViaParent || handledRemovedValues;\n            if (shouldAnimateType && needsAnimating) {\n                animations.push(...definitionList.map((animation) => ({\n                    animation: animation,\n                    options: { type },\n                })));\n            }\n        }\n        /**\n         * If there are some removed value that haven't been dealt with,\n         * we need to create a new animation that falls back either to the value\n         * defined in the style prop, or the last read value.\n         */\n        if (removedKeys.size) {\n            const fallbackAnimation = {};\n            /**\n             * If the initial prop contains a transition we can use that, otherwise\n             * allow the animation function to use the visual element's default.\n             */\n            if (typeof props.initial !== \"boolean\") {\n                const initialTransition = resolveVariant(visualElement, Array.isArray(props.initial)\n                    ? props.initial[0]\n                    : props.initial);\n                if (initialTransition && initialTransition.transition) {\n                    fallbackAnimation.transition = initialTransition.transition;\n                }\n            }\n            removedKeys.forEach((key) => {\n                const fallbackTarget = visualElement.getBaseTarget(key);\n                const motionValue = visualElement.getValue(key);\n                if (motionValue)\n                    motionValue.liveStyle = true;\n                // @ts-expect-error - @mattgperry to figure if we should do something here\n                fallbackAnimation[key] = fallbackTarget ?? null;\n            });\n            animations.push({ animation: fallbackAnimation });\n        }\n        let shouldAnimate = Boolean(animations.length);\n        if (isInitialRender &&\n            (props.initial === false || props.initial === props.animate) &&\n            !visualElement.manuallyAnimateOnMount) {\n            shouldAnimate = false;\n        }\n        isInitialRender = false;\n        return shouldAnimate ? animate(animations) : Promise.resolve();\n    }\n    /**\n     * Change whether a certain animation type is active.\n     */\n    function setActive(type, isActive) {\n        // If the active state hasn't changed, we can safely do nothing here\n        if (state[type].isActive === isActive)\n            return Promise.resolve();\n        // Propagate active change to children\n        visualElement.variantChildren?.forEach((child) => child.animationState?.setActive(type, isActive));\n        state[type].isActive = isActive;\n        const animations = animateChanges(type);\n        for (const key in state) {\n            state[key].protectedKeys = {};\n        }\n        return animations;\n    }\n    return {\n        animateChanges,\n        setActive,\n        setAnimateFunction,\n        getState: () => state,\n        reset: () => {\n            state = createState();\n            isInitialRender = true;\n        },\n    };\n}\nfunction checkVariantsDidChange(prev, next) {\n    if (typeof next === \"string\") {\n        return next !== prev;\n    }\n    else if (Array.isArray(next)) {\n        return !shallowCompare(next, prev);\n    }\n    return false;\n}\nfunction createTypeState(isActive = false) {\n    return {\n        isActive,\n        protectedKeys: {},\n        needsAnimating: {},\n        prevResolvedValues: {},\n    };\n}\nfunction createState() {\n    return {\n        animate: createTypeState(true),\n        whileInView: createTypeState(),\n        whileHover: createTypeState(),\n        whileTap: createTypeState(),\n        whileDrag: createTypeState(),\n        whileFocus: createTypeState(),\n        exit: createTypeState(),\n    };\n}\n\nexport { checkVariantsDidChange, createAnimationState };\n", "class Feature {\n    constructor(node) {\n        this.isMounted = false;\n        this.node = node;\n    }\n    update() { }\n}\n\nexport { Feature };\n", "import { isAnimationControls } from '../../../animation/utils/is-animation-controls.mjs';\nimport { createAnimationState } from '../../../render/utils/animation-state.mjs';\nimport { Feature } from '../Feature.mjs';\n\nclass AnimationFeature extends Feature {\n    /**\n     * We dynamically generate the AnimationState manager as it contains a reference\n     * to the underlying animation library. We only want to load that if we load this,\n     * so people can optionally code split it out using the `m` component.\n     */\n    constructor(node) {\n        super(node);\n        node.animationState || (node.animationState = createAnimationState(node));\n    }\n    updateAnimationControlsSubscription() {\n        const { animate } = this.node.getProps();\n        if (isAnimationControls(animate)) {\n            this.unmountControls = animate.subscribe(this.node);\n        }\n    }\n    /**\n     * Subscribe any provided AnimationControls to the component's VisualElement\n     */\n    mount() {\n        this.updateAnimationControlsSubscription();\n    }\n    update() {\n        const { animate } = this.node.getProps();\n        const { animate: prevAnimate } = this.node.prevProps || {};\n        if (animate !== prevAnimate) {\n            this.updateAnimationControlsSubscription();\n        }\n    }\n    unmount() {\n        this.node.animationState.reset();\n        this.unmountControls?.();\n    }\n}\n\nexport { AnimationFeature };\n", "import { Feature } from '../Feature.mjs';\n\nlet id = 0;\nclass ExitAnimationFeature extends Feature {\n    constructor() {\n        super(...arguments);\n        this.id = id++;\n    }\n    update() {\n        if (!this.node.presenceContext)\n            return;\n        const { isPresent, onExitComplete } = this.node.presenceContext;\n        const { isPresent: prevIsPresent } = this.node.prevPresenceContext || {};\n        if (!this.node.animationState || isPresent === prevIsPresent) {\n            return;\n        }\n        const exitAnimation = this.node.animationState.setActive(\"exit\", !isPresent);\n        if (onExitComplete && !isPresent) {\n            exitAnimation.then(() => {\n                onExitComplete(this.id);\n            });\n        }\n    }\n    mount() {\n        const { register, onExitComplete } = this.node.presenceContext || {};\n        if (onExitComplete) {\n            onExitComplete(this.id);\n        }\n        if (register) {\n            this.unmount = register(this.id);\n        }\n    }\n    unmount() { }\n}\n\nexport { ExitAnimationFeature };\n", "import { AnimationFeature } from './animation/index.mjs';\nimport { ExitAnimationFeature } from './animation/exit.mjs';\n\nconst animations = {\n    animation: {\n        Feature: AnimationFeature,\n    },\n    exit: {\n        Feature: ExitAnimationFeature,\n    },\n};\n\nexport { animations };\n", "function addDomEvent(target, eventName, handler, options = { passive: true }) {\n    target.addEventListener(eventName, handler, options);\n    return () => target.removeEventListener(eventName, handler);\n}\n\nexport { addDomEvent };\n", "import { isPrimaryPointer } from 'motion-dom';\n\nfunction extractEventInfo(event) {\n    return {\n        point: {\n            x: event.pageX,\n            y: event.pageY,\n        },\n    };\n}\nconst addPointerInfo = (handler) => {\n    return (event) => isPrimaryPointer(event) && handler(event, extractEventInfo(event));\n};\n\nexport { addPointerInfo, extractEventInfo };\n", "import { addDomEvent } from './add-dom-event.mjs';\nimport { addPointerInfo } from './event-info.mjs';\n\nfunction addPointerEvent(target, eventName, handler, options) {\n    return addDomEvent(target, eventName, addPointerInfo(handler), options);\n}\n\nexport { addPointerEvent };\n", "/**\n * Bounding boxes tend to be defined as top, left, right, bottom. For various operations\n * it's easier to consider each axis individually. This function returns a bounding box\n * as a map of single-axis min/max values.\n */\nfunction convertBoundingBoxToBox({ top, left, right, bottom, }) {\n    return {\n        x: { min: left, max: right },\n        y: { min: top, max: bottom },\n    };\n}\nfunction convertBoxToBoundingBox({ x, y }) {\n    return { top: y.min, right: x.max, bottom: y.max, left: x.min };\n}\n/**\n * Applies a TransformPoint function to a bounding box. TransformPoint is usually a function\n * provided by Framer to allow measured points to be corrected for device scaling. This is used\n * when measuring DOM elements and DOM event points.\n */\nfunction transformBoxPoints(point, transformPoint) {\n    if (!transformPoint)\n        return point;\n    const topLeft = transformPoint({ x: point.left, y: point.top });\n    const bottomRight = transformPoint({ x: point.right, y: point.bottom });\n    return {\n        top: topLeft.y,\n        left: topLeft.x,\n        bottom: bottomRight.y,\n        right: bottomRight.x,\n    };\n}\n\nexport { convertBoundingBoxToBox, convertBoxToBoundingBox, transformBoxPoints };\n", "import { mixNumber } from 'motion-dom';\n\nconst SCALE_PRECISION = 0.0001;\nconst SCALE_MIN = 1 - SCALE_PRECISION;\nconst SCALE_MAX = 1 + SCALE_PRECISION;\nconst TRANSLATE_PRECISION = 0.01;\nconst TRANSLATE_MIN = 0 - TRANSLATE_PRECISION;\nconst TRANSLATE_MAX = 0 + TRANSLATE_PRECISION;\nfunction calcLength(axis) {\n    return axis.max - axis.min;\n}\nfunction isNear(value, target, maxDistance) {\n    return Math.abs(value - target) <= maxDistance;\n}\nfunction calcAxisDelta(delta, source, target, origin = 0.5) {\n    delta.origin = origin;\n    delta.originPoint = mixNumber(source.min, source.max, delta.origin);\n    delta.scale = calcLength(target) / calcLength(source);\n    delta.translate =\n        mixNumber(target.min, target.max, delta.origin) - delta.originPoint;\n    if ((delta.scale >= SCALE_MIN && delta.scale <= SCALE_MAX) ||\n        isNaN(delta.scale)) {\n        delta.scale = 1.0;\n    }\n    if ((delta.translate >= TRANSLATE_MIN &&\n        delta.translate <= TRANSLATE_MAX) ||\n        isNaN(delta.translate)) {\n        delta.translate = 0.0;\n    }\n}\nfunction calcBoxDelta(delta, source, target, origin) {\n    calcAxisDelta(delta.x, source.x, target.x, origin ? origin.originX : undefined);\n    calcAxisDelta(delta.y, source.y, target.y, origin ? origin.originY : undefined);\n}\nfunction calcRelativeAxis(target, relative, parent) {\n    target.min = parent.min + relative.min;\n    target.max = target.min + calcLength(relative);\n}\nfunction calcRelativeBox(target, relative, parent) {\n    calcRelativeAxis(target.x, relative.x, parent.x);\n    calcRelativeAxis(target.y, relative.y, parent.y);\n}\nfunction calcRelativeAxisPosition(target, layout, parent) {\n    target.min = layout.min - parent.min;\n    target.max = target.min + calcLength(layout);\n}\nfunction calcRelativePosition(target, layout, parent) {\n    calcRelativeAxisPosition(target.x, layout.x, parent.x);\n    calcRelativeAxisPosition(target.y, layout.y, parent.y);\n}\n\nexport { calcAxisDelta, calcBoxDelta, calcLength, calcRelativeAxis, calcRelativeAxisPosition, calcRelativeBox, calcRelativePosition, isNear };\n", "const createAxisDelta = () => ({\n    translate: 0,\n    scale: 1,\n    origin: 0,\n    originPoint: 0,\n});\nconst createDelta = () => ({\n    x: createAxisDelta(),\n    y: createAxisDelta(),\n});\nconst createAxis = () => ({ min: 0, max: 0 });\nconst createBox = () => ({\n    x: createAxis(),\n    y: createAxis(),\n});\n\nexport { createAxis, createAxisDelta, createBox, createDelta };\n", "function eachAxis(callback) {\n    return [callback(\"x\"), callback(\"y\")];\n}\n\nexport { eachAxis };\n", "function isIdentityScale(scale) {\n    return scale === undefined || scale === 1;\n}\nfunction hasScale({ scale, scaleX, scaleY }) {\n    return (!isIdentityScale(scale) ||\n        !isIdentityScale(scaleX) ||\n        !isIdentityScale(scaleY));\n}\nfunction hasTransform(values) {\n    return (hasScale(values) ||\n        has2DTranslate(values) ||\n        values.z ||\n        values.rotate ||\n        values.rotateX ||\n        values.rotateY ||\n        values.skewX ||\n        values.skewY);\n}\nfunction has2DTranslate(values) {\n    return is2DTranslate(values.x) || is2DTranslate(values.y);\n}\nfunction is2DTranslate(value) {\n    return value && value !== \"0%\";\n}\n\nexport { has2DTranslate, hasScale, hasTransform };\n", "import { mixNumber } from 'motion-dom';\nimport { hasTransform } from '../utils/has-transform.mjs';\n\n/**\n * Scales a point based on a factor and an originPoint\n */\nfunction scalePoint(point, scale, originPoint) {\n    const distanceFromOrigin = point - originPoint;\n    const scaled = scale * distanceFromOrigin;\n    return originPoint + scaled;\n}\n/**\n * Applies a translate/scale delta to a point\n */\nfunction applyPointDelta(point, translate, scale, originPoint, boxScale) {\n    if (boxScale !== undefined) {\n        point = scalePoint(point, boxScale, originPoint);\n    }\n    return scalePoint(point, scale, originPoint) + translate;\n}\n/**\n * Applies a translate/scale delta to an axis\n */\nfunction applyAxisDelta(axis, translate = 0, scale = 1, originPoint, boxScale) {\n    axis.min = applyPointDelta(axis.min, translate, scale, originPoint, boxScale);\n    axis.max = applyPointDelta(axis.max, translate, scale, originPoint, boxScale);\n}\n/**\n * Applies a translate/scale delta to a box\n */\nfunction applyBoxDelta(box, { x, y }) {\n    applyAxisDelta(box.x, x.translate, x.scale, x.originPoint);\n    applyAxisDelta(box.y, y.translate, y.scale, y.originPoint);\n}\nconst TREE_SCALE_SNAP_MIN = 0.999999999999;\nconst TREE_SCALE_SNAP_MAX = 1.0000000000001;\n/**\n * Apply a tree of deltas to a box. We do this to calculate the effect of all the transforms\n * in a tree upon our box before then calculating how to project it into our desired viewport-relative box\n *\n * This is the final nested loop within updateLayoutDelta for future refactoring\n */\nfunction applyTreeDeltas(box, treeScale, treePath, isSharedTransition = false) {\n    const treeLength = treePath.length;\n    if (!treeLength)\n        return;\n    // Reset the treeScale\n    treeScale.x = treeScale.y = 1;\n    let node;\n    let delta;\n    for (let i = 0; i < treeLength; i++) {\n        node = treePath[i];\n        delta = node.projectionDelta;\n        /**\n         * TODO: Prefer to remove this, but currently we have motion components with\n         * display: contents in Framer.\n         */\n        const { visualElement } = node.options;\n        if (visualElement &&\n            visualElement.props.style &&\n            visualElement.props.style.display === \"contents\") {\n            continue;\n        }\n        if (isSharedTransition &&\n            node.options.layoutScroll &&\n            node.scroll &&\n            node !== node.root) {\n            transformBox(box, {\n                x: -node.scroll.offset.x,\n                y: -node.scroll.offset.y,\n            });\n        }\n        if (delta) {\n            // Incoporate each ancestor's scale into a culmulative treeScale for this component\n            treeScale.x *= delta.x.scale;\n            treeScale.y *= delta.y.scale;\n            // Apply each ancestor's calculated delta into this component's recorded layout box\n            applyBoxDelta(box, delta);\n        }\n        if (isSharedTransition && hasTransform(node.latestValues)) {\n            transformBox(box, node.latestValues);\n        }\n    }\n    /**\n     * Snap tree scale back to 1 if it's within a non-perceivable threshold.\n     * This will help reduce useless scales getting rendered.\n     */\n    if (treeScale.x < TREE_SCALE_SNAP_MAX &&\n        treeScale.x > TREE_SCALE_SNAP_MIN) {\n        treeScale.x = 1.0;\n    }\n    if (treeScale.y < TREE_SCALE_SNAP_MAX &&\n        treeScale.y > TREE_SCALE_SNAP_MIN) {\n        treeScale.y = 1.0;\n    }\n}\nfunction translateAxis(axis, distance) {\n    axis.min = axis.min + distance;\n    axis.max = axis.max + distance;\n}\n/**\n * Apply a transform to an axis from the latest resolved motion values.\n * This function basically acts as a bridge between a flat motion value map\n * and applyAxisDelta\n */\nfunction transformAxis(axis, axisTranslate, axisScale, boxScale, axisOrigin = 0.5) {\n    const originPoint = mixNumber(axis.min, axis.max, axisOrigin);\n    // Apply the axis delta to the final axis\n    applyAxisDelta(axis, axisTranslate, axisScale, originPoint, boxScale);\n}\n/**\n * Apply a transform to a box from the latest resolved motion values.\n */\nfunction transformBox(box, transform) {\n    transformAxis(box.x, transform.x, transform.scaleX, transform.scale, transform.originX);\n    transformAxis(box.y, transform.y, transform.scaleY, transform.scale, transform.originY);\n}\n\nexport { applyAxisDelta, applyBoxDelta, applyPointDelta, applyTreeDeltas, scalePoint, transformAxis, transformBox, translateAxis };\n", "import { convertBoundingBoxToBox, transformBoxPoints } from '../geometry/conversion.mjs';\nimport { translateAxis } from '../geometry/delta-apply.mjs';\n\nfunction measureViewportBox(instance, transformPoint) {\n    return convertBoundingBoxToBox(transformBoxPoints(instance.getBoundingClientRect(), transformPoint));\n}\nfunction measurePageBox(element, rootProjectionNode, transformPagePoint) {\n    const viewportBox = measureViewportBox(element, transformPagePoint);\n    const { scroll } = rootProjectionNode;\n    if (scroll) {\n        translateAxis(viewportBox.x, scroll.offset.x);\n        translateAxis(viewportBox.y, scroll.offset.y);\n    }\n    return viewportBox;\n}\n\nexport { measurePageBox, measureViewportBox };\n", "// Fixes https://github.com/motiondivision/motion/issues/2270\nconst getContextWindow = ({ current }) => {\n    return current ? current.ownerDocument.defaultView : null;\n};\n\nexport { getContextWindow };\n", "const distance = (a, b) => Math.abs(a - b);\nfunction distance2D(a, b) {\n    // Multi-dimensional\n    const xDelta = distance(a.x, b.x);\n    const yDelta = distance(a.y, b.y);\n    return Math.sqrt(xDelta ** 2 + yDelta ** 2);\n}\n\nexport { distance, distance2D };\n", "import { frame, isPrimaryPointer, cancel<PERSON>rame, frameData } from 'motion-dom';\nimport { pipe, secondsToMilliseconds, millisecondsToSeconds } from 'motion-utils';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { extractEventInfo } from '../../events/event-info.mjs';\nimport { distance2D } from '../../utils/distance.mjs';\n\n/**\n * @internal\n */\nclass PanSession {\n    constructor(event, handlers, { transformPagePoint, contextWindow, dragSnapToOrigin = false, } = {}) {\n        /**\n         * @internal\n         */\n        this.startEvent = null;\n        /**\n         * @internal\n         */\n        this.lastMoveEvent = null;\n        /**\n         * @internal\n         */\n        this.lastMoveEventInfo = null;\n        /**\n         * @internal\n         */\n        this.handlers = {};\n        /**\n         * @internal\n         */\n        this.contextWindow = window;\n        this.updatePoint = () => {\n            if (!(this.lastMoveEvent && this.lastMoveEventInfo))\n                return;\n            const info = getPanInfo(this.lastMoveEventInfo, this.history);\n            const isPanStarted = this.startEvent !== null;\n            // Only start panning if the offset is larger than 3 pixels. If we make it\n            // any larger than this we'll want to reset the pointer history\n            // on the first update to avoid visual snapping to the cursoe.\n            const isDistancePastThreshold = distance2D(info.offset, { x: 0, y: 0 }) >= 3;\n            if (!isPanStarted && !isDistancePastThreshold)\n                return;\n            const { point } = info;\n            const { timestamp } = frameData;\n            this.history.push({ ...point, timestamp });\n            const { onStart, onMove } = this.handlers;\n            if (!isPanStarted) {\n                onStart && onStart(this.lastMoveEvent, info);\n                this.startEvent = this.lastMoveEvent;\n            }\n            onMove && onMove(this.lastMoveEvent, info);\n        };\n        this.handlePointerMove = (event, info) => {\n            this.lastMoveEvent = event;\n            this.lastMoveEventInfo = transformPoint(info, this.transformPagePoint);\n            // Throttle mouse move event to once per frame\n            frame.update(this.updatePoint, true);\n        };\n        this.handlePointerUp = (event, info) => {\n            this.end();\n            const { onEnd, onSessionEnd, resumeAnimation } = this.handlers;\n            if (this.dragSnapToOrigin)\n                resumeAnimation && resumeAnimation();\n            if (!(this.lastMoveEvent && this.lastMoveEventInfo))\n                return;\n            const panInfo = getPanInfo(event.type === \"pointercancel\"\n                ? this.lastMoveEventInfo\n                : transformPoint(info, this.transformPagePoint), this.history);\n            if (this.startEvent && onEnd) {\n                onEnd(event, panInfo);\n            }\n            onSessionEnd && onSessionEnd(event, panInfo);\n        };\n        // If we have more than one touch, don't start detecting this gesture\n        if (!isPrimaryPointer(event))\n            return;\n        this.dragSnapToOrigin = dragSnapToOrigin;\n        this.handlers = handlers;\n        this.transformPagePoint = transformPagePoint;\n        this.contextWindow = contextWindow || window;\n        const info = extractEventInfo(event);\n        const initialInfo = transformPoint(info, this.transformPagePoint);\n        const { point } = initialInfo;\n        const { timestamp } = frameData;\n        this.history = [{ ...point, timestamp }];\n        const { onSessionStart } = handlers;\n        onSessionStart &&\n            onSessionStart(event, getPanInfo(initialInfo, this.history));\n        this.removeListeners = pipe(addPointerEvent(this.contextWindow, \"pointermove\", this.handlePointerMove), addPointerEvent(this.contextWindow, \"pointerup\", this.handlePointerUp), addPointerEvent(this.contextWindow, \"pointercancel\", this.handlePointerUp));\n    }\n    updateHandlers(handlers) {\n        this.handlers = handlers;\n    }\n    end() {\n        this.removeListeners && this.removeListeners();\n        cancelFrame(this.updatePoint);\n    }\n}\nfunction transformPoint(info, transformPagePoint) {\n    return transformPagePoint ? { point: transformPagePoint(info.point) } : info;\n}\nfunction subtractPoint(a, b) {\n    return { x: a.x - b.x, y: a.y - b.y };\n}\nfunction getPanInfo({ point }, history) {\n    return {\n        point,\n        delta: subtractPoint(point, lastDevicePoint(history)),\n        offset: subtractPoint(point, startDevicePoint(history)),\n        velocity: getVelocity(history, 0.1),\n    };\n}\nfunction startDevicePoint(history) {\n    return history[0];\n}\nfunction lastDevicePoint(history) {\n    return history[history.length - 1];\n}\nfunction getVelocity(history, timeDelta) {\n    if (history.length < 2) {\n        return { x: 0, y: 0 };\n    }\n    let i = history.length - 1;\n    let timestampedPoint = null;\n    const lastPoint = lastDevicePoint(history);\n    while (i >= 0) {\n        timestampedPoint = history[i];\n        if (lastPoint.timestamp - timestampedPoint.timestamp >\n            secondsToMilliseconds(timeDelta)) {\n            break;\n        }\n        i--;\n    }\n    if (!timestampedPoint) {\n        return { x: 0, y: 0 };\n    }\n    const time = millisecondsToSeconds(lastPoint.timestamp - timestampedPoint.timestamp);\n    if (time === 0) {\n        return { x: 0, y: 0 };\n    }\n    const currentVelocity = {\n        x: (lastPoint.x - timestampedPoint.x) / time,\n        y: (lastPoint.y - timestampedPoint.y) / time,\n    };\n    if (currentVelocity.x === Infinity) {\n        currentVelocity.x = 0;\n    }\n    if (currentVelocity.y === Infinity) {\n        currentVelocity.y = 0;\n    }\n    return currentVelocity;\n}\n\nexport { PanSession };\n", "import { mixNumber } from 'motion-dom';\nimport { progress, clamp } from 'motion-utils';\nimport { calcLength } from '../../../projection/geometry/delta-calc.mjs';\n\n/**\n * Apply constraints to a point. These constraints are both physical along an\n * axis, and an elastic factor that determines how much to constrain the point\n * by if it does lie outside the defined parameters.\n */\nfunction applyConstraints(point, { min, max }, elastic) {\n    if (min !== undefined && point < min) {\n        // If we have a min point defined, and this is outside of that, constrain\n        point = elastic\n            ? mixNumber(min, point, elastic.min)\n            : Math.max(point, min);\n    }\n    else if (max !== undefined && point > max) {\n        // If we have a max point defined, and this is outside of that, constrain\n        point = elastic\n            ? mixNumber(max, point, elastic.max)\n            : Math.min(point, max);\n    }\n    return point;\n}\n/**\n * Calculate constraints in terms of the viewport when defined relatively to the\n * measured axis. This is measured from the nearest edge, so a max constraint of 200\n * on an axis with a max value of 300 would return a constraint of 500 - axis length\n */\nfunction calcRelativeAxisConstraints(axis, min, max) {\n    return {\n        min: min !== undefined ? axis.min + min : undefined,\n        max: max !== undefined\n            ? axis.max + max - (axis.max - axis.min)\n            : undefined,\n    };\n}\n/**\n * Calculate constraints in terms of the viewport when\n * defined relatively to the measured bounding box.\n */\nfunction calcRelativeConstraints(layoutBox, { top, left, bottom, right }) {\n    return {\n        x: calcRelativeAxisConstraints(layoutBox.x, left, right),\n        y: calcRelativeAxisConstraints(layoutBox.y, top, bottom),\n    };\n}\n/**\n * Calculate viewport constraints when defined as another viewport-relative axis\n */\nfunction calcViewportAxisConstraints(layoutAxis, constraintsAxis) {\n    let min = constraintsAxis.min - layoutAxis.min;\n    let max = constraintsAxis.max - layoutAxis.max;\n    // If the constraints axis is actually smaller than the layout axis then we can\n    // flip the constraints\n    if (constraintsAxis.max - constraintsAxis.min <\n        layoutAxis.max - layoutAxis.min) {\n        [min, max] = [max, min];\n    }\n    return { min, max };\n}\n/**\n * Calculate viewport constraints when defined as another viewport-relative box\n */\nfunction calcViewportConstraints(layoutBox, constraintsBox) {\n    return {\n        x: calcViewportAxisConstraints(layoutBox.x, constraintsBox.x),\n        y: calcViewportAxisConstraints(layoutBox.y, constraintsBox.y),\n    };\n}\n/**\n * Calculate a transform origin relative to the source axis, between 0-1, that results\n * in an asthetically pleasing scale/transform needed to project from source to target.\n */\nfunction calcOrigin(source, target) {\n    let origin = 0.5;\n    const sourceLength = calcLength(source);\n    const targetLength = calcLength(target);\n    if (targetLength > sourceLength) {\n        origin = progress(target.min, target.max - sourceLength, source.min);\n    }\n    else if (sourceLength > targetLength) {\n        origin = progress(source.min, source.max - targetLength, target.min);\n    }\n    return clamp(0, 1, origin);\n}\n/**\n * Rebase the calculated viewport constraints relative to the layout.min point.\n */\nfunction rebaseAxisConstraints(layout, constraints) {\n    const relativeConstraints = {};\n    if (constraints.min !== undefined) {\n        relativeConstraints.min = constraints.min - layout.min;\n    }\n    if (constraints.max !== undefined) {\n        relativeConstraints.max = constraints.max - layout.min;\n    }\n    return relativeConstraints;\n}\nconst defaultElastic = 0.35;\n/**\n * Accepts a dragElastic prop and returns resolved elastic values for each axis.\n */\nfunction resolveDragElastic(dragElastic = defaultElastic) {\n    if (dragElastic === false) {\n        dragElastic = 0;\n    }\n    else if (dragElastic === true) {\n        dragElastic = defaultElastic;\n    }\n    return {\n        x: resolveAxisElastic(dragElastic, \"left\", \"right\"),\n        y: resolveAxisElastic(dragElastic, \"top\", \"bottom\"),\n    };\n}\nfunction resolveAxisElastic(dragElastic, minLabel, maxLabel) {\n    return {\n        min: resolvePointElastic(dragElastic, minLabel),\n        max: resolvePointElastic(dragElastic, maxLabel),\n    };\n}\nfunction resolvePointElastic(dragElastic, label) {\n    return typeof dragElastic === \"number\"\n        ? dragElastic\n        : dragElastic[label] || 0;\n}\n\nexport { applyConstraints, calcOrigin, calcRelativeAxisConstraints, calcRelativeConstraints, calcViewportAxisConstraints, calcViewportConstraints, defaultElastic, rebaseAxisConstraints, resolveAxisElastic, resolveDragElastic, resolvePointElastic };\n", "import { frame, mixNumber, setDragLock, percent } from 'motion-dom';\nimport { invariant } from 'motion-utils';\nimport { animateMotionValue } from '../../animation/interfaces/motion-value.mjs';\nimport { addDomEvent } from '../../events/add-dom-event.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { extractEventInfo } from '../../events/event-info.mjs';\nimport { convertBoxToBoundingBox, convertBoundingBoxToBox } from '../../projection/geometry/conversion.mjs';\nimport { calcLength } from '../../projection/geometry/delta-calc.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { eachAxis } from '../../projection/utils/each-axis.mjs';\nimport { measurePageBox } from '../../projection/utils/measure.mjs';\nimport { getContextWindow } from '../../utils/get-context-window.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { addValueToWillChange } from '../../value/use-will-change/add-will-change.mjs';\nimport { PanSession } from '../pan/PanSession.mjs';\nimport { applyConstraints, calcRelativeConstraints, resolveDragElastic, rebaseAxisConstraints, calcViewportConstraints, calcOrigin, defaultElastic } from './utils/constraints.mjs';\n\nconst elementDragControls = new WeakMap();\n/**\n *\n */\n// let latestPointerEvent: PointerEvent\nclass VisualElementDragControls {\n    constructor(visualElement) {\n        this.openDragLock = null;\n        this.isDragging = false;\n        this.currentDirection = null;\n        this.originPoint = { x: 0, y: 0 };\n        /**\n         * The permitted boundaries of travel, in pixels.\n         */\n        this.constraints = false;\n        this.hasMutatedConstraints = false;\n        /**\n         * The per-axis resolved elastic values.\n         */\n        this.elastic = createBox();\n        this.visualElement = visualElement;\n    }\n    start(originEvent, { snapToCursor = false } = {}) {\n        /**\n         * Don't start dragging if this component is exiting\n         */\n        const { presenceContext } = this.visualElement;\n        if (presenceContext && presenceContext.isPresent === false)\n            return;\n        const onSessionStart = (event) => {\n            const { dragSnapToOrigin } = this.getProps();\n            // Stop or pause any animations on both axis values immediately. This allows the user to throw and catch\n            // the component.\n            dragSnapToOrigin ? this.pauseAnimation() : this.stopAnimation();\n            if (snapToCursor) {\n                this.snapToCursor(extractEventInfo(event).point);\n            }\n        };\n        const onStart = (event, info) => {\n            // Attempt to grab the global drag gesture lock - maybe make this part of PanSession\n            const { drag, dragPropagation, onDragStart } = this.getProps();\n            if (drag && !dragPropagation) {\n                if (this.openDragLock)\n                    this.openDragLock();\n                this.openDragLock = setDragLock(drag);\n                // If we don 't have the lock, don't start dragging\n                if (!this.openDragLock)\n                    return;\n            }\n            this.isDragging = true;\n            this.currentDirection = null;\n            this.resolveConstraints();\n            if (this.visualElement.projection) {\n                this.visualElement.projection.isAnimationBlocked = true;\n                this.visualElement.projection.target = undefined;\n            }\n            /**\n             * Record gesture origin\n             */\n            eachAxis((axis) => {\n                let current = this.getAxisMotionValue(axis).get() || 0;\n                /**\n                 * If the MotionValue is a percentage value convert to px\n                 */\n                if (percent.test(current)) {\n                    const { projection } = this.visualElement;\n                    if (projection && projection.layout) {\n                        const measuredAxis = projection.layout.layoutBox[axis];\n                        if (measuredAxis) {\n                            const length = calcLength(measuredAxis);\n                            current = length * (parseFloat(current) / 100);\n                        }\n                    }\n                }\n                this.originPoint[axis] = current;\n            });\n            // Fire onDragStart event\n            if (onDragStart) {\n                frame.postRender(() => onDragStart(event, info));\n            }\n            addValueToWillChange(this.visualElement, \"transform\");\n            const { animationState } = this.visualElement;\n            animationState && animationState.setActive(\"whileDrag\", true);\n        };\n        const onMove = (event, info) => {\n            // latestPointerEvent = event\n            const { dragPropagation, dragDirectionLock, onDirectionLock, onDrag, } = this.getProps();\n            // If we didn't successfully receive the gesture lock, early return.\n            if (!dragPropagation && !this.openDragLock)\n                return;\n            const { offset } = info;\n            // Attempt to detect drag direction if directionLock is true\n            if (dragDirectionLock && this.currentDirection === null) {\n                this.currentDirection = getCurrentDirection(offset);\n                // If we've successfully set a direction, notify listener\n                if (this.currentDirection !== null) {\n                    onDirectionLock && onDirectionLock(this.currentDirection);\n                }\n                return;\n            }\n            // Update each point with the latest position\n            this.updateAxis(\"x\", info.point, offset);\n            this.updateAxis(\"y\", info.point, offset);\n            /**\n             * Ideally we would leave the renderer to fire naturally at the end of\n             * this frame but if the element is about to change layout as the result\n             * of a re-render we want to ensure the browser can read the latest\n             * bounding box to ensure the pointer and element don't fall out of sync.\n             */\n            this.visualElement.render();\n            /**\n             * This must fire after the render call as it might trigger a state\n             * change which itself might trigger a layout update.\n             */\n            onDrag && onDrag(event, info);\n        };\n        const onSessionEnd = (event, info) => this.stop(event, info);\n        const resumeAnimation = () => eachAxis((axis) => this.getAnimationState(axis) === \"paused\" &&\n            this.getAxisMotionValue(axis).animation?.play());\n        const { dragSnapToOrigin } = this.getProps();\n        this.panSession = new PanSession(originEvent, {\n            onSessionStart,\n            onStart,\n            onMove,\n            onSessionEnd,\n            resumeAnimation,\n        }, {\n            transformPagePoint: this.visualElement.getTransformPagePoint(),\n            dragSnapToOrigin,\n            contextWindow: getContextWindow(this.visualElement),\n        });\n    }\n    stop(event, info) {\n        const isDragging = this.isDragging;\n        this.cancel();\n        if (!isDragging)\n            return;\n        const { velocity } = info;\n        this.startAnimation(velocity);\n        const { onDragEnd } = this.getProps();\n        if (onDragEnd) {\n            frame.postRender(() => onDragEnd(event, info));\n        }\n    }\n    cancel() {\n        this.isDragging = false;\n        const { projection, animationState } = this.visualElement;\n        if (projection) {\n            projection.isAnimationBlocked = false;\n        }\n        this.panSession && this.panSession.end();\n        this.panSession = undefined;\n        const { dragPropagation } = this.getProps();\n        if (!dragPropagation && this.openDragLock) {\n            this.openDragLock();\n            this.openDragLock = null;\n        }\n        animationState && animationState.setActive(\"whileDrag\", false);\n    }\n    updateAxis(axis, _point, offset) {\n        const { drag } = this.getProps();\n        // If we're not dragging this axis, do an early return.\n        if (!offset || !shouldDrag(axis, drag, this.currentDirection))\n            return;\n        const axisValue = this.getAxisMotionValue(axis);\n        let next = this.originPoint[axis] + offset[axis];\n        // Apply constraints\n        if (this.constraints && this.constraints[axis]) {\n            next = applyConstraints(next, this.constraints[axis], this.elastic[axis]);\n        }\n        axisValue.set(next);\n    }\n    resolveConstraints() {\n        const { dragConstraints, dragElastic } = this.getProps();\n        const layout = this.visualElement.projection &&\n            !this.visualElement.projection.layout\n            ? this.visualElement.projection.measure(false)\n            : this.visualElement.projection?.layout;\n        const prevConstraints = this.constraints;\n        if (dragConstraints && isRefObject(dragConstraints)) {\n            if (!this.constraints) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        }\n        else {\n            if (dragConstraints && layout) {\n                this.constraints = calcRelativeConstraints(layout.layoutBox, dragConstraints);\n            }\n            else {\n                this.constraints = false;\n            }\n        }\n        this.elastic = resolveDragElastic(dragElastic);\n        /**\n         * If we're outputting to external MotionValues, we want to rebase the measured constraints\n         * from viewport-relative to component-relative.\n         */\n        if (prevConstraints !== this.constraints &&\n            layout &&\n            this.constraints &&\n            !this.hasMutatedConstraints) {\n            eachAxis((axis) => {\n                if (this.constraints !== false &&\n                    this.getAxisMotionValue(axis)) {\n                    this.constraints[axis] = rebaseAxisConstraints(layout.layoutBox[axis], this.constraints[axis]);\n                }\n            });\n        }\n    }\n    resolveRefConstraints() {\n        const { dragConstraints: constraints, onMeasureDragConstraints } = this.getProps();\n        if (!constraints || !isRefObject(constraints))\n            return false;\n        const constraintsElement = constraints.current;\n        invariant(constraintsElement !== null, \"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.\");\n        const { projection } = this.visualElement;\n        // TODO\n        if (!projection || !projection.layout)\n            return false;\n        const constraintsBox = measurePageBox(constraintsElement, projection.root, this.visualElement.getTransformPagePoint());\n        let measuredConstraints = calcViewportConstraints(projection.layout.layoutBox, constraintsBox);\n        /**\n         * If there's an onMeasureDragConstraints listener we call it and\n         * if different constraints are returned, set constraints to that\n         */\n        if (onMeasureDragConstraints) {\n            const userConstraints = onMeasureDragConstraints(convertBoxToBoundingBox(measuredConstraints));\n            this.hasMutatedConstraints = !!userConstraints;\n            if (userConstraints) {\n                measuredConstraints = convertBoundingBoxToBox(userConstraints);\n            }\n        }\n        return measuredConstraints;\n    }\n    startAnimation(velocity) {\n        const { drag, dragMomentum, dragElastic, dragTransition, dragSnapToOrigin, onDragTransitionEnd, } = this.getProps();\n        const constraints = this.constraints || {};\n        const momentumAnimations = eachAxis((axis) => {\n            if (!shouldDrag(axis, drag, this.currentDirection)) {\n                return;\n            }\n            let transition = (constraints && constraints[axis]) || {};\n            if (dragSnapToOrigin)\n                transition = { min: 0, max: 0 };\n            /**\n             * Overdamp the boundary spring if `dragElastic` is disabled. There's still a frame\n             * of spring animations so we should look into adding a disable spring option to `inertia`.\n             * We could do something here where we affect the `bounceStiffness` and `bounceDamping`\n             * using the value of `dragElastic`.\n             */\n            const bounceStiffness = dragElastic ? 200 : 1000000;\n            const bounceDamping = dragElastic ? 40 : 10000000;\n            const inertia = {\n                type: \"inertia\",\n                velocity: dragMomentum ? velocity[axis] : 0,\n                bounceStiffness,\n                bounceDamping,\n                timeConstant: 750,\n                restDelta: 1,\n                restSpeed: 10,\n                ...dragTransition,\n                ...transition,\n            };\n            // If we're not animating on an externally-provided `MotionValue` we can use the\n            // component's animation controls which will handle interactions with whileHover (etc),\n            // otherwise we just have to animate the `MotionValue` itself.\n            return this.startAxisValueAnimation(axis, inertia);\n        });\n        // Run all animations and then resolve the new drag constraints.\n        return Promise.all(momentumAnimations).then(onDragTransitionEnd);\n    }\n    startAxisValueAnimation(axis, transition) {\n        const axisValue = this.getAxisMotionValue(axis);\n        addValueToWillChange(this.visualElement, axis);\n        return axisValue.start(animateMotionValue(axis, axisValue, 0, transition, this.visualElement, false));\n    }\n    stopAnimation() {\n        eachAxis((axis) => this.getAxisMotionValue(axis).stop());\n    }\n    pauseAnimation() {\n        eachAxis((axis) => this.getAxisMotionValue(axis).animation?.pause());\n    }\n    getAnimationState(axis) {\n        return this.getAxisMotionValue(axis).animation?.state;\n    }\n    /**\n     * Drag works differently depending on which props are provided.\n     *\n     * - If _dragX and _dragY are provided, we output the gesture delta directly to those motion values.\n     * - Otherwise, we apply the delta to the x/y motion values.\n     */\n    getAxisMotionValue(axis) {\n        const dragKey = `_drag${axis.toUpperCase()}`;\n        const props = this.visualElement.getProps();\n        const externalMotionValue = props[dragKey];\n        return externalMotionValue\n            ? externalMotionValue\n            : this.visualElement.getValue(axis, (props.initial\n                ? props.initial[axis]\n                : undefined) || 0);\n    }\n    snapToCursor(point) {\n        eachAxis((axis) => {\n            const { drag } = this.getProps();\n            // If we're not dragging this axis, do an early return.\n            if (!shouldDrag(axis, drag, this.currentDirection))\n                return;\n            const { projection } = this.visualElement;\n            const axisValue = this.getAxisMotionValue(axis);\n            if (projection && projection.layout) {\n                const { min, max } = projection.layout.layoutBox[axis];\n                axisValue.set(point[axis] - mixNumber(min, max, 0.5));\n            }\n        });\n    }\n    /**\n     * When the viewport resizes we want to check if the measured constraints\n     * have changed and, if so, reposition the element within those new constraints\n     * relative to where it was before the resize.\n     */\n    scalePositionWithinConstraints() {\n        if (!this.visualElement.current)\n            return;\n        const { drag, dragConstraints } = this.getProps();\n        const { projection } = this.visualElement;\n        if (!isRefObject(dragConstraints) || !projection || !this.constraints)\n            return;\n        /**\n         * Stop current animations as there can be visual glitching if we try to do\n         * this mid-animation\n         */\n        this.stopAnimation();\n        /**\n         * Record the relative position of the dragged element relative to the\n         * constraints box and save as a progress value.\n         */\n        const boxProgress = { x: 0, y: 0 };\n        eachAxis((axis) => {\n            const axisValue = this.getAxisMotionValue(axis);\n            if (axisValue && this.constraints !== false) {\n                const latest = axisValue.get();\n                boxProgress[axis] = calcOrigin({ min: latest, max: latest }, this.constraints[axis]);\n            }\n        });\n        /**\n         * Update the layout of this element and resolve the latest drag constraints\n         */\n        const { transformTemplate } = this.visualElement.getProps();\n        this.visualElement.current.style.transform = transformTemplate\n            ? transformTemplate({}, \"\")\n            : \"none\";\n        projection.root && projection.root.updateScroll();\n        projection.updateLayout();\n        this.resolveConstraints();\n        /**\n         * For each axis, calculate the current progress of the layout axis\n         * within the new constraints.\n         */\n        eachAxis((axis) => {\n            if (!shouldDrag(axis, drag, null))\n                return;\n            /**\n             * Calculate a new transform based on the previous box progress\n             */\n            const axisValue = this.getAxisMotionValue(axis);\n            const { min, max } = this.constraints[axis];\n            axisValue.set(mixNumber(min, max, boxProgress[axis]));\n        });\n    }\n    addListeners() {\n        if (!this.visualElement.current)\n            return;\n        elementDragControls.set(this.visualElement, this);\n        const element = this.visualElement.current;\n        /**\n         * Attach a pointerdown event listener on this DOM element to initiate drag tracking.\n         */\n        const stopPointerListener = addPointerEvent(element, \"pointerdown\", (event) => {\n            const { drag, dragListener = true } = this.getProps();\n            drag && dragListener && this.start(event);\n        });\n        const measureDragConstraints = () => {\n            const { dragConstraints } = this.getProps();\n            if (isRefObject(dragConstraints) && dragConstraints.current) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        };\n        const { projection } = this.visualElement;\n        const stopMeasureLayoutListener = projection.addEventListener(\"measure\", measureDragConstraints);\n        if (projection && !projection.layout) {\n            projection.root && projection.root.updateScroll();\n            projection.updateLayout();\n        }\n        frame.read(measureDragConstraints);\n        /**\n         * Attach a window resize listener to scale the draggable target within its defined\n         * constraints as the window resizes.\n         */\n        const stopResizeListener = addDomEvent(window, \"resize\", () => this.scalePositionWithinConstraints());\n        /**\n         * If the element's layout changes, calculate the delta and apply that to\n         * the drag gesture's origin point.\n         */\n        const stopLayoutUpdateListener = projection.addEventListener(\"didUpdate\", (({ delta, hasLayoutChanged }) => {\n            if (this.isDragging && hasLayoutChanged) {\n                eachAxis((axis) => {\n                    const motionValue = this.getAxisMotionValue(axis);\n                    if (!motionValue)\n                        return;\n                    this.originPoint[axis] += delta[axis].translate;\n                    motionValue.set(motionValue.get() + delta[axis].translate);\n                });\n                this.visualElement.render();\n            }\n        }));\n        return () => {\n            stopResizeListener();\n            stopPointerListener();\n            stopMeasureLayoutListener();\n            stopLayoutUpdateListener && stopLayoutUpdateListener();\n        };\n    }\n    getProps() {\n        const props = this.visualElement.getProps();\n        const { drag = false, dragDirectionLock = false, dragPropagation = false, dragConstraints = false, dragElastic = defaultElastic, dragMomentum = true, } = props;\n        return {\n            ...props,\n            drag,\n            dragDirectionLock,\n            dragPropagation,\n            dragConstraints,\n            dragElastic,\n            dragMomentum,\n        };\n    }\n}\nfunction shouldDrag(direction, drag, currentDirection) {\n    return ((drag === true || drag === direction) &&\n        (currentDirection === null || currentDirection === direction));\n}\n/**\n * Based on an x/y offset determine the current drag direction. If both axis' offsets are lower\n * than the provided threshold, return `null`.\n *\n * @param offset - The x/y offset from origin.\n * @param lockThreshold - (Optional) - the minimum absolute offset before we can determine a drag direction.\n */\nfunction getCurrentDirection(offset, lockThreshold = 10) {\n    let direction = null;\n    if (Math.abs(offset.y) > lockThreshold) {\n        direction = \"y\";\n    }\n    else if (Math.abs(offset.x) > lockThreshold) {\n        direction = \"x\";\n    }\n    return direction;\n}\n\nexport { VisualElementDragControls, elementDragControls };\n", "import { Feature } from '../../motion/features/Feature.mjs';\nimport { noop } from 'motion-utils';\nimport { VisualElementDragControls } from './VisualElementDragControls.mjs';\n\nclass DragGesture extends Feature {\n    constructor(node) {\n        super(node);\n        this.removeGroupControls = noop;\n        this.removeListeners = noop;\n        this.controls = new VisualElementDragControls(node);\n    }\n    mount() {\n        // If we've been provided a DragControls for manual control over the drag gesture,\n        // subscribe this component to it on mount.\n        const { dragControls } = this.node.getProps();\n        if (dragControls) {\n            this.removeGroupControls = dragControls.subscribe(this.controls);\n        }\n        this.removeListeners = this.controls.addListeners() || noop;\n    }\n    unmount() {\n        this.removeGroupControls();\n        this.removeListeners();\n    }\n}\n\nexport { DragGesture };\n", "import { frame } from 'motion-dom';\nimport { noop } from 'motion-utils';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { Feature } from '../../motion/features/Feature.mjs';\nimport { getContextWindow } from '../../utils/get-context-window.mjs';\nimport { PanSession } from './PanSession.mjs';\n\nconst asyncHandler = (handler) => (event, info) => {\n    if (handler) {\n        frame.postRender(() => handler(event, info));\n    }\n};\nclass PanGesture extends Feature {\n    constructor() {\n        super(...arguments);\n        this.removePointerDownListener = noop;\n    }\n    onPointerDown(pointerDownEvent) {\n        this.session = new PanSession(pointerDownEvent, this.createPanHandlers(), {\n            transformPagePoint: this.node.getTransformPagePoint(),\n            contextWindow: getContextWindow(this.node),\n        });\n    }\n    createPanHandlers() {\n        const { onPanSessionStart, onPanStart, onPan, onPanEnd } = this.node.getProps();\n        return {\n            onSessionStart: asyncHandler(onPanSessionStart),\n            onStart: asyncHandler(onPanStart),\n            onMove: onPan,\n            onEnd: (event, info) => {\n                delete this.session;\n                if (onPanEnd) {\n                    frame.postRender(() => onPanEnd(event, info));\n                }\n            },\n        };\n    }\n    mount() {\n        this.removePointerDownListener = addPointerEvent(this.node.current, \"pointerdown\", (event) => this.onPointerDown(event));\n    }\n    update() {\n        this.session && this.session.updateHandlers(this.createPanHandlers());\n    }\n    unmount() {\n        this.removePointerDownListener();\n        this.session && this.session.end();\n    }\n}\n\nexport { PanGesture };\n", "/**\n * This should only ever be modified on the client otherwise it'll\n * persist through server requests. If we need instanced states we\n * could lazy-init via root.\n */\nconst globalProjectionState = {\n    /**\n     * Global flag as to whether the tree has animated since the last time\n     * we resized the window\n     */\n    hasAnimatedSinceResize: true,\n    /**\n     * We set this to true once, on the first update. Any nodes added to the tree beyond that\n     * update will be given a `data-projection-id` attribute.\n     */\n    hasEverUpdated: false,\n};\n\nexport { globalProjectionState };\n", "import { px } from 'motion-dom';\n\nfunction pixelsToPercent(pixels, axis) {\n    if (axis.max === axis.min)\n        return 0;\n    return (pixels / (axis.max - axis.min)) * 100;\n}\n/**\n * We always correct borderRadius as a percentage rather than pixels to reduce paints.\n * For example, if you are projecting a box that is 100px wide with a 10px borderRadius\n * into a box that is 200px wide with a 20px borderRadius, that is actually a 10%\n * borderRadius in both states. If we animate between the two in pixels that will trigger\n * a paint each time. If we animate between the two in percentage we'll avoid a paint.\n */\nconst correctBorderRadius = {\n    correct: (latest, node) => {\n        if (!node.target)\n            return latest;\n        /**\n         * If latest is a string, if it's a percentage we can return immediately as it's\n         * going to be stretched appropriately. Otherwise, if it's a pixel, convert it to a number.\n         */\n        if (typeof latest === \"string\") {\n            if (px.test(latest)) {\n                latest = parseFloat(latest);\n            }\n            else {\n                return latest;\n            }\n        }\n        /**\n         * If latest is a number, it's a pixel value. We use the current viewportBox to calculate that\n         * pixel value as a percentage of each axis\n         */\n        const x = pixelsToPercent(latest, node.target.x);\n        const y = pixelsToPercent(latest, node.target.y);\n        return `${x}% ${y}%`;\n    },\n};\n\nexport { correctBorderRadius, pixelsToPercent };\n", "import { complex, mixNumber } from 'motion-dom';\n\nconst correctBoxShadow = {\n    correct: (latest, { treeScale, projectionDelta }) => {\n        const original = latest;\n        const shadow = complex.parse(latest);\n        // TODO: Doesn't support multiple shadows\n        if (shadow.length > 5)\n            return original;\n        const template = complex.createTransformer(latest);\n        const offset = typeof shadow[0] !== \"number\" ? 1 : 0;\n        // Calculate the overall context scale\n        const xScale = projectionDelta.x.scale * treeScale.x;\n        const yScale = projectionDelta.y.scale * treeScale.y;\n        shadow[0 + offset] /= xScale;\n        shadow[1 + offset] /= yScale;\n        /**\n         * Ideally we'd correct x and y scales individually, but because blur and\n         * spread apply to both we have to take a scale average and apply that instead.\n         * We could potentially improve the outcome of this by incorporating the ratio between\n         * the two scales.\n         */\n        const averageScale = mixNumber(xScale, yScale, 0.5);\n        // Blur\n        if (typeof shadow[2 + offset] === \"number\")\n            shadow[2 + offset] /= averageScale;\n        // Spread\n        if (typeof shadow[3 + offset] === \"number\")\n            shadow[3 + offset] /= averageScale;\n        return template(shadow);\n    },\n};\n\nexport { correctBoxShadow };\n", "\"use client\";\nimport { jsx } from 'react/jsx-runtime';\nimport { frame, microtask } from 'motion-dom';\nimport { useContext, Component } from 'react';\nimport { usePresence } from '../../../components/AnimatePresence/use-presence.mjs';\nimport { LayoutGroupContext } from '../../../context/LayoutGroupContext.mjs';\nimport { SwitchLayoutGroupContext } from '../../../context/SwitchLayoutGroupContext.mjs';\nimport { globalProjectionState } from '../../../projection/node/state.mjs';\nimport { correctBorderRadius } from '../../../projection/styles/scale-border-radius.mjs';\nimport { correctBoxShadow } from '../../../projection/styles/scale-box-shadow.mjs';\nimport { addScaleCorrector } from '../../../projection/styles/scale-correction.mjs';\n\nclass MeasureLayoutWithContext extends Component {\n    /**\n     * This only mounts projection nodes for components that\n     * need measuring, we might want to do it for all components\n     * in order to incorporate transforms\n     */\n    componentDidMount() {\n        const { visualElement, layoutGroup, switchLayoutGroup, layoutId } = this.props;\n        const { projection } = visualElement;\n        addScaleCorrector(defaultScaleCorrectors);\n        if (projection) {\n            if (layoutGroup.group)\n                layoutGroup.group.add(projection);\n            if (switchLayoutGroup && switchLayoutGroup.register && layoutId) {\n                switchLayoutGroup.register(projection);\n            }\n            projection.root.didUpdate();\n            projection.addEventListener(\"animationComplete\", () => {\n                this.safeToRemove();\n            });\n            projection.setOptions({\n                ...projection.options,\n                onExitComplete: () => this.safeToRemove(),\n            });\n        }\n        globalProjectionState.hasEverUpdated = true;\n    }\n    getSnapshotBeforeUpdate(prevProps) {\n        const { layoutDependency, visualElement, drag, isPresent } = this.props;\n        const { projection } = visualElement;\n        if (!projection)\n            return null;\n        /**\n         * TODO: We use this data in relegate to determine whether to\n         * promote a previous element. There's no guarantee its presence data\n         * will have updated by this point - if a bug like this arises it will\n         * have to be that we markForRelegation and then find a new lead some other way,\n         * perhaps in didUpdate\n         */\n        projection.isPresent = isPresent;\n        if (drag ||\n            prevProps.layoutDependency !== layoutDependency ||\n            layoutDependency === undefined ||\n            prevProps.isPresent !== isPresent) {\n            projection.willUpdate();\n        }\n        else {\n            this.safeToRemove();\n        }\n        if (prevProps.isPresent !== isPresent) {\n            if (isPresent) {\n                projection.promote();\n            }\n            else if (!projection.relegate()) {\n                /**\n                 * If there's another stack member taking over from this one,\n                 * it's in charge of the exit animation and therefore should\n                 * be in charge of the safe to remove. Otherwise we call it here.\n                 */\n                frame.postRender(() => {\n                    const stack = projection.getStack();\n                    if (!stack || !stack.members.length) {\n                        this.safeToRemove();\n                    }\n                });\n            }\n        }\n        return null;\n    }\n    componentDidUpdate() {\n        const { projection } = this.props.visualElement;\n        if (projection) {\n            projection.root.didUpdate();\n            microtask.postRender(() => {\n                if (!projection.currentAnimation && projection.isLead()) {\n                    this.safeToRemove();\n                }\n            });\n        }\n    }\n    componentWillUnmount() {\n        const { visualElement, layoutGroup, switchLayoutGroup: promoteContext, } = this.props;\n        const { projection } = visualElement;\n        if (projection) {\n            projection.scheduleCheckAfterUnmount();\n            if (layoutGroup && layoutGroup.group)\n                layoutGroup.group.remove(projection);\n            if (promoteContext && promoteContext.deregister)\n                promoteContext.deregister(projection);\n        }\n    }\n    safeToRemove() {\n        const { safeToRemove } = this.props;\n        safeToRemove && safeToRemove();\n    }\n    render() {\n        return null;\n    }\n}\nfunction MeasureLayout(props) {\n    const [isPresent, safeToRemove] = usePresence();\n    const layoutGroup = useContext(LayoutGroupContext);\n    return (jsx(MeasureLayoutWithContext, { ...props, layoutGroup: layoutGroup, switchLayoutGroup: useContext(SwitchLayoutGroupContext), isPresent: isPresent, safeToRemove: safeToRemove }));\n}\nconst defaultScaleCorrectors = {\n    borderRadius: {\n        ...correctBorderRadius,\n        applyTo: [\n            \"borderTopLeftRadius\",\n            \"borderTopRightRadius\",\n            \"borderBottomLeftRadius\",\n            \"borderBottomRightRadius\",\n        ],\n    },\n    borderTopLeftRadius: correctBorderRadius,\n    borderTopRightRadius: correctBorderRadius,\n    borderBottomLeftRadius: correctBorderRadius,\n    borderBottomRightRadius: correctBorderRadius,\n    boxShadow: correctBoxShadow,\n};\n\nexport { MeasureLayout };\n", "import { isMotionValue, motionValue } from 'motion-dom';\nimport { animateMotionValue } from '../interfaces/motion-value.mjs';\n\nfunction animateSingleValue(value, keyframes, options) {\n    const motionValue$1 = isMotionValue(value) ? value : motionValue(value);\n    motionValue$1.start(animateMotionValue(\"\", motionValue$1, keyframes, options));\n    return motionValue$1.animation;\n}\n\nexport { animateSingleValue };\n", "const compareByDepth = (a, b) => a.depth - b.depth;\n\nexport { compareByDepth };\n", "import { addUniqueItem, removeItem } from 'motion-utils';\nimport { compareByDepth } from './compare-by-depth.mjs';\n\nclass FlatTree {\n    constructor() {\n        this.children = [];\n        this.isDirty = false;\n    }\n    add(child) {\n        addUniqueItem(this.children, child);\n        this.isDirty = true;\n    }\n    remove(child) {\n        removeItem(this.children, child);\n        this.isDirty = true;\n    }\n    forEach(callback) {\n        this.isDirty && this.children.sort(compareByDepth);\n        this.isDirty = false;\n        this.children.forEach(callback);\n    }\n}\n\nexport { FlatTree };\n", "import { time, frame, cancelFrame } from 'motion-dom';\nimport { secondsToMilliseconds } from 'motion-utils';\n\n/**\n * Timeout defined in ms\n */\nfunction delay(callback, timeout) {\n    const start = time.now();\n    const checkElapsed = ({ timestamp }) => {\n        const elapsed = timestamp - start;\n        if (elapsed >= timeout) {\n            cancelFrame(checkElapsed);\n            callback(elapsed - timeout);\n        }\n    };\n    frame.setup(checkElapsed, true);\n    return () => cancelFrame(checkElapsed);\n}\nfunction delayInSeconds(callback, timeout) {\n    return delay(callback, secondsToMilliseconds(timeout));\n}\n\nexport { delay, delayInSeconds };\n", "import { mixNumber, percent, px } from 'motion-dom';\nimport { progress, circOut, noop } from 'motion-utils';\n\nconst borders = [\"TopLeft\", \"TopRight\", \"BottomLeft\", \"BottomRight\"];\nconst numBorders = borders.length;\nconst asNumber = (value) => typeof value === \"string\" ? parseFloat(value) : value;\nconst isPx = (value) => typeof value === \"number\" || px.test(value);\nfunction mixValues(target, follow, lead, progress, shouldCrossfadeOpacity, isOnlyMember) {\n    if (shouldCrossfadeOpacity) {\n        target.opacity = mixNumber(0, lead.opacity ?? 1, easeCrossfadeIn(progress));\n        target.opacityExit = mixNumber(follow.opacity ?? 1, 0, easeCrossfadeOut(progress));\n    }\n    else if (isOnlyMember) {\n        target.opacity = mixNumber(follow.opacity ?? 1, lead.opacity ?? 1, progress);\n    }\n    /**\n     * Mix border radius\n     */\n    for (let i = 0; i < numBorders; i++) {\n        const borderLabel = `border${borders[i]}Radius`;\n        let followRadius = getRadius(follow, borderLabel);\n        let leadRadius = getRadius(lead, borderLabel);\n        if (followRadius === undefined && leadRadius === undefined)\n            continue;\n        followRadius || (followRadius = 0);\n        leadRadius || (leadRadius = 0);\n        const canMix = followRadius === 0 ||\n            leadRadius === 0 ||\n            isPx(followRadius) === isPx(leadRadius);\n        if (canMix) {\n            target[borderLabel] = Math.max(mixNumber(asNumber(followRadius), asNumber(leadRadius), progress), 0);\n            if (percent.test(leadRadius) || percent.test(followRadius)) {\n                target[borderLabel] += \"%\";\n            }\n        }\n        else {\n            target[borderLabel] = leadRadius;\n        }\n    }\n    /**\n     * Mix rotation\n     */\n    if (follow.rotate || lead.rotate) {\n        target.rotate = mixNumber(follow.rotate || 0, lead.rotate || 0, progress);\n    }\n}\nfunction getRadius(values, radiusName) {\n    return values[radiusName] !== undefined\n        ? values[radiusName]\n        : values.borderRadius;\n}\n// /**\n//  * We only want to mix the background color if there's a follow element\n//  * that we're not crossfading opacity between. For instance with switch\n//  * AnimateSharedLayout animations, this helps the illusion of a continuous\n//  * element being animated but also cuts down on the number of paints triggered\n//  * for elements where opacity is doing that work for us.\n//  */\n// if (\n//     !hasFollowElement &&\n//     latestLeadValues.backgroundColor &&\n//     latestFollowValues.backgroundColor\n// ) {\n//     /**\n//      * This isn't ideal performance-wise as mixColor is creating a new function every frame.\n//      * We could probably create a mixer that runs at the start of the animation but\n//      * the idea behind the crossfader is that it runs dynamically between two potentially\n//      * changing targets (ie opacity or borderRadius may be animating independently via variants)\n//      */\n//     leadState.backgroundColor = followState.backgroundColor = mixColor(\n//         latestFollowValues.backgroundColor as string,\n//         latestLeadValues.backgroundColor as string\n//     )(p)\n// }\nconst easeCrossfadeIn = /*@__PURE__*/ compress(0, 0.5, circOut);\nconst easeCrossfadeOut = /*@__PURE__*/ compress(0.5, 0.95, noop);\nfunction compress(min, max, easing) {\n    return (p) => {\n        // Could replace ifs with clamp\n        if (p < min)\n            return 0;\n        if (p > max)\n            return 1;\n        return easing(progress(min, max, p));\n    };\n}\n\nexport { mixValues };\n", "/**\n * Reset an axis to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyAxisInto(axis, originAxis) {\n    axis.min = originAxis.min;\n    axis.max = originAxis.max;\n}\n/**\n * Reset a box to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyBoxInto(box, originBox) {\n    copyAxisInto(box.x, originBox.x);\n    copyAxisInto(box.y, originBox.y);\n}\n/**\n * Reset a delta to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyAxisDeltaInto(delta, originDelta) {\n    delta.translate = originDelta.translate;\n    delta.scale = originDelta.scale;\n    delta.originPoint = originDelta.originPoint;\n    delta.origin = originDelta.origin;\n}\n\nexport { copyAxisDeltaInto, copyAxisInto, copyBoxInto };\n", "import { percent, mixNumber } from 'motion-dom';\nimport { scalePoint } from './delta-apply.mjs';\n\n/**\n * Remove a delta from a point. This is essentially the steps of applyPointDelta in reverse\n */\nfunction removePointDelta(point, translate, scale, originPoint, boxScale) {\n    point -= translate;\n    point = scalePoint(point, 1 / scale, originPoint);\n    if (boxScale !== undefined) {\n        point = scalePoint(point, 1 / boxScale, originPoint);\n    }\n    return point;\n}\n/**\n * Remove a delta from an axis. This is essentially the steps of applyAxisDelta in reverse\n */\nfunction removeAxisDelta(axis, translate = 0, scale = 1, origin = 0.5, boxScale, originAxis = axis, sourceAxis = axis) {\n    if (percent.test(translate)) {\n        translate = parseFloat(translate);\n        const relativeProgress = mixNumber(sourceAxis.min, sourceAxis.max, translate / 100);\n        translate = relativeProgress - sourceAxis.min;\n    }\n    if (typeof translate !== \"number\")\n        return;\n    let originPoint = mixNumber(originAxis.min, originAxis.max, origin);\n    if (axis === originAxis)\n        originPoint -= translate;\n    axis.min = removePointDelta(axis.min, translate, scale, originPoint, boxScale);\n    axis.max = removePointDelta(axis.max, translate, scale, originPoint, boxScale);\n}\n/**\n * Remove a transforms from an axis. This is essentially the steps of applyAxisTransforms in reverse\n * and acts as a bridge between motion values and removeAxisDelta\n */\nfunction removeAxisTransforms(axis, transforms, [key, scaleKey, originKey], origin, sourceAxis) {\n    removeAxisDelta(axis, transforms[key], transforms[scaleKey], transforms[originKey], transforms.scale, origin, sourceAxis);\n}\n/**\n * The names of the motion values we want to apply as translation, scale and origin.\n */\nconst xKeys = [\"x\", \"scaleX\", \"originX\"];\nconst yKeys = [\"y\", \"scaleY\", \"originY\"];\n/**\n * Remove a transforms from an box. This is essentially the steps of applyAxisBox in reverse\n * and acts as a bridge between motion values and removeAxisDelta\n */\nfunction removeBoxTransforms(box, transforms, originBox, sourceBox) {\n    removeAxisTransforms(box.x, transforms, xKeys, originBox ? originBox.x : undefined, sourceBox ? sourceBox.x : undefined);\n    removeAxisTransforms(box.y, transforms, yKeys, originBox ? originBox.y : undefined, sourceBox ? sourceBox.y : undefined);\n}\n\nexport { removeAxisDelta, removeAxisTransforms, removeBoxTransforms, removePointDelta };\n", "import { calcLength } from './delta-calc.mjs';\n\nfunction isAxisDeltaZero(delta) {\n    return delta.translate === 0 && delta.scale === 1;\n}\nfunction isDeltaZero(delta) {\n    return isAxisDeltaZero(delta.x) && isAxisDeltaZero(delta.y);\n}\nfunction axisEquals(a, b) {\n    return a.min === b.min && a.max === b.max;\n}\nfunction boxEquals(a, b) {\n    return axisEquals(a.x, b.x) && axisEquals(a.y, b.y);\n}\nfunction axisEqualsRounded(a, b) {\n    return (Math.round(a.min) === Math.round(b.min) &&\n        Math.round(a.max) === Math.round(b.max));\n}\nfunction boxEqualsRounded(a, b) {\n    return axisEqualsRounded(a.x, b.x) && axisEqualsRounded(a.y, b.y);\n}\nfunction aspectRatio(box) {\n    return calcLength(box.x) / calcLength(box.y);\n}\nfunction axisDeltaEquals(a, b) {\n    return (a.translate === b.translate &&\n        a.scale === b.scale &&\n        a.originPoint === b.originPoint);\n}\n\nexport { aspectRatio, axisDeltaEquals, axisEquals, axisEqualsRounded, boxEquals, boxEqualsRounded, isDeltaZero };\n", "import { addUniqueItem, removeItem } from 'motion-utils';\n\nclass NodeStack {\n    constructor() {\n        this.members = [];\n    }\n    add(node) {\n        addUniqueItem(this.members, node);\n        node.scheduleRender();\n    }\n    remove(node) {\n        removeItem(this.members, node);\n        if (node === this.prevLead) {\n            this.prevLead = undefined;\n        }\n        if (node === this.lead) {\n            const prevLead = this.members[this.members.length - 1];\n            if (prevLead) {\n                this.promote(prevLead);\n            }\n        }\n    }\n    relegate(node) {\n        const indexOfNode = this.members.findIndex((member) => node === member);\n        if (indexOfNode === 0)\n            return false;\n        /**\n         * Find the next projection node that is present\n         */\n        let prevLead;\n        for (let i = indexOfNode; i >= 0; i--) {\n            const member = this.members[i];\n            if (member.isPresent !== false) {\n                prevLead = member;\n                break;\n            }\n        }\n        if (prevLead) {\n            this.promote(prevLead);\n            return true;\n        }\n        else {\n            return false;\n        }\n    }\n    promote(node, preserveFollowOpacity) {\n        const prevLead = this.lead;\n        if (node === prevLead)\n            return;\n        this.prevLead = prevLead;\n        this.lead = node;\n        node.show();\n        if (prevLead) {\n            prevLead.instance && prevLead.scheduleRender();\n            node.scheduleRender();\n            node.resumeFrom = prevLead;\n            if (preserveFollowOpacity) {\n                node.resumeFrom.preserveOpacity = true;\n            }\n            if (prevLead.snapshot) {\n                node.snapshot = prevLead.snapshot;\n                node.snapshot.latestValues =\n                    prevLead.animationValues || prevLead.latestValues;\n            }\n            if (node.root && node.root.isUpdating) {\n                node.isLayoutDirty = true;\n            }\n            const { crossfade } = node.options;\n            if (crossfade === false) {\n                prevLead.hide();\n            }\n            /**\n             * TODO:\n             *   - Test border radius when previous node was deleted\n             *   - boxShadow mixing\n             *   - Shared between element A in scrolled container and element B (scroll stays the same or changes)\n             *   - Shared between element A in transformed container and element B (transform stays the same or changes)\n             *   - Shared between element A in scrolled page and element B (scroll stays the same or changes)\n             * ---\n             *   - Crossfade opacity of root nodes\n             *   - layoutId changes after animation\n             *   - layoutId changes mid animation\n             */\n        }\n    }\n    exitAnimationComplete() {\n        this.members.forEach((node) => {\n            const { options, resumingFrom } = node;\n            options.onExitComplete && options.onExitComplete();\n            if (resumingFrom) {\n                resumingFrom.options.onExitComplete &&\n                    resumingFrom.options.onExitComplete();\n            }\n        });\n    }\n    scheduleRender() {\n        this.members.forEach((node) => {\n            node.instance && node.scheduleRender(false);\n        });\n    }\n    /**\n     * Clear any leads that have been removed this render to prevent them from being\n     * used in future animations and to prevent memory leaks\n     */\n    removeLeadSnapshot() {\n        if (this.lead && this.lead.snapshot) {\n            this.lead.snapshot = undefined;\n        }\n    }\n}\n\nexport { NodeStack };\n", "function buildProjectionTransform(delta, treeScale, latestTransform) {\n    let transform = \"\";\n    /**\n     * The translations we use to calculate are always relative to the viewport coordinate space.\n     * But when we apply scales, we also scale the coordinate space of an element and its children.\n     * For instance if we have a treeScale (the culmination of all parent scales) of 0.5 and we need\n     * to move an element 100 pixels, we actually need to move it 200 in within that scaled space.\n     */\n    const xTranslate = delta.x.translate / treeScale.x;\n    const yTranslate = delta.y.translate / treeScale.y;\n    const zTranslate = latestTransform?.z || 0;\n    if (xTranslate || yTranslate || zTranslate) {\n        transform = `translate3d(${xTranslate}px, ${yTranslate}px, ${zTranslate}px) `;\n    }\n    /**\n     * Apply scale correction for the tree transform.\n     * This will apply scale to the screen-orientated axes.\n     */\n    if (treeScale.x !== 1 || treeScale.y !== 1) {\n        transform += `scale(${1 / treeScale.x}, ${1 / treeScale.y}) `;\n    }\n    if (latestTransform) {\n        const { transformPerspective, rotate, rotateX, rotateY, skewX, skewY } = latestTransform;\n        if (transformPerspective)\n            transform = `perspective(${transformPerspective}px) ${transform}`;\n        if (rotate)\n            transform += `rotate(${rotate}deg) `;\n        if (rotateX)\n            transform += `rotateX(${rotateX}deg) `;\n        if (rotateY)\n            transform += `rotateY(${rotateY}deg) `;\n        if (skewX)\n            transform += `skewX(${skewX}deg) `;\n        if (skewY)\n            transform += `skewY(${skewY}deg) `;\n    }\n    /**\n     * Apply scale to match the size of the element to the size we want it.\n     * This will apply scale to the element-orientated axes.\n     */\n    const elementScaleX = delta.x.scale * treeScale.x;\n    const elementScaleY = delta.y.scale * treeScale.y;\n    if (elementScaleX !== 1 || elementScaleY !== 1) {\n        transform += `scale(${elementScaleX}, ${elementScaleY})`;\n    }\n    return transform || \"none\";\n}\n\nexport { buildProjectionTransform };\n", "import { statsBuffer, isSVGElement, isSVGSVGElement, getValueTransition, cancelFrame, time, frameData, frameSteps, microtask, frame, activeAnimations, motionValue, mixNumber } from 'motion-dom';\nimport { SubscriptionManager, clamp, noop } from 'motion-utils';\nimport { animateSingleValue } from '../../animation/animate/single-value.mjs';\nimport { getOptimisedAppearId } from '../../animation/optimized-appear/get-appear-id.mjs';\nimport { FlatTree } from '../../render/utils/flat-tree.mjs';\nimport { delay } from '../../utils/delay.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { mixValues } from '../animation/mix-values.mjs';\nimport { copyBoxInto, copyAxisDeltaInto } from '../geometry/copy.mjs';\nimport { translateAxis, transformBox, applyBoxDelta, applyTreeDeltas } from '../geometry/delta-apply.mjs';\nimport { calcLength, calcRelativePosition, calcRelativeBox, calcBoxDelta, isNear } from '../geometry/delta-calc.mjs';\nimport { removeBoxTransforms } from '../geometry/delta-remove.mjs';\nimport { createBox, createDelta } from '../geometry/models.mjs';\nimport { boxEqualsRounded, isDeltaZero, axisDeltaEquals, aspectRatio, boxEquals } from '../geometry/utils.mjs';\nimport { NodeStack } from '../shared/stack.mjs';\nimport { scaleCorrectors } from '../styles/scale-correction.mjs';\nimport { buildProjectionTransform } from '../styles/transform.mjs';\nimport { eachAxis } from '../utils/each-axis.mjs';\nimport { hasTransform, hasScale, has2DTranslate } from '../utils/has-transform.mjs';\nimport { globalProjectionState } from './state.mjs';\n\nconst metrics = {\n    nodes: 0,\n    calculatedTargetDeltas: 0,\n    calculatedProjections: 0,\n};\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\nconst hiddenVisibility = { visibility: \"hidden\" };\n/**\n * We use 1000 as the animation target as 0-1000 maps better to pixels than 0-1\n * which has a noticeable difference in spring animations\n */\nconst animationTarget = 1000;\nlet id = 0;\nfunction resetDistortingTransform(key, visualElement, values, sharedAnimationValues) {\n    const { latestValues } = visualElement;\n    // Record the distorting transform and then temporarily set it to 0\n    if (latestValues[key]) {\n        values[key] = latestValues[key];\n        visualElement.setStaticValue(key, 0);\n        if (sharedAnimationValues) {\n            sharedAnimationValues[key] = 0;\n        }\n    }\n}\nfunction cancelTreeOptimisedTransformAnimations(projectionNode) {\n    projectionNode.hasCheckedOptimisedAppear = true;\n    if (projectionNode.root === projectionNode)\n        return;\n    const { visualElement } = projectionNode.options;\n    if (!visualElement)\n        return;\n    const appearId = getOptimisedAppearId(visualElement);\n    if (window.MotionHasOptimisedAnimation(appearId, \"transform\")) {\n        const { layout, layoutId } = projectionNode.options;\n        window.MotionCancelOptimisedAnimation(appearId, \"transform\", frame, !(layout || layoutId));\n    }\n    const { parent } = projectionNode;\n    if (parent && !parent.hasCheckedOptimisedAppear) {\n        cancelTreeOptimisedTransformAnimations(parent);\n    }\n}\nfunction createProjectionNode({ attachResizeListener, defaultParent, measureScroll, checkIsScrollRoot, resetTransform, }) {\n    return class ProjectionNode {\n        constructor(latestValues = {}, parent = defaultParent?.()) {\n            /**\n             * A unique ID generated for every projection node.\n             */\n            this.id = id++;\n            /**\n             * An id that represents a unique session instigated by startUpdate.\n             */\n            this.animationId = 0;\n            /**\n             * A Set containing all this component's children. This is used to iterate\n             * through the children.\n             *\n             * TODO: This could be faster to iterate as a flat array stored on the root node.\n             */\n            this.children = new Set();\n            /**\n             * Options for the node. We use this to configure what kind of layout animations\n             * we should perform (if any).\n             */\n            this.options = {};\n            /**\n             * We use this to detect when its safe to shut down part of a projection tree.\n             * We have to keep projecting children for scale correction and relative projection\n             * until all their parents stop performing layout animations.\n             */\n            this.isTreeAnimating = false;\n            this.isAnimationBlocked = false;\n            /**\n             * Flag to true if we think this layout has been changed. We can't always know this,\n             * currently we set it to true every time a component renders, or if it has a layoutDependency\n             * if that has changed between renders. Additionally, components can be grouped by LayoutGroup\n             * and if one node is dirtied, they all are.\n             */\n            this.isLayoutDirty = false;\n            /**\n             * Flag to true if we think the projection calculations for this node needs\n             * recalculating as a result of an updated transform or layout animation.\n             */\n            this.isProjectionDirty = false;\n            /**\n             * Flag to true if the layout *or* transform has changed. This then gets propagated\n             * throughout the projection tree, forcing any element below to recalculate on the next frame.\n             */\n            this.isSharedProjectionDirty = false;\n            /**\n             * Flag transform dirty. This gets propagated throughout the whole tree but is only\n             * respected by shared nodes.\n             */\n            this.isTransformDirty = false;\n            /**\n             * Block layout updates for instant layout transitions throughout the tree.\n             */\n            this.updateManuallyBlocked = false;\n            this.updateBlockedByResize = false;\n            /**\n             * Set to true between the start of the first `willUpdate` call and the end of the `didUpdate`\n             * call.\n             */\n            this.isUpdating = false;\n            /**\n             * If this is an SVG element we currently disable projection transforms\n             */\n            this.isSVG = false;\n            /**\n             * Flag to true (during promotion) if a node doing an instant layout transition needs to reset\n             * its projection styles.\n             */\n            this.needsReset = false;\n            /**\n             * Flags whether this node should have its transform reset prior to measuring.\n             */\n            this.shouldResetTransform = false;\n            /**\n             * Store whether this node has been checked for optimised appear animations. As\n             * effects fire bottom-up, and we want to look up the tree for appear animations,\n             * this makes sure we only check each path once, stopping at nodes that\n             * have already been checked.\n             */\n            this.hasCheckedOptimisedAppear = false;\n            /**\n             * An object representing the calculated contextual/accumulated/tree scale.\n             * This will be used to scale calculcated projection transforms, as these are\n             * calculated in screen-space but need to be scaled for elements to layoutly\n             * make it to their calculated destinations.\n             *\n             * TODO: Lazy-init\n             */\n            this.treeScale = { x: 1, y: 1 };\n            /**\n             *\n             */\n            this.eventHandlers = new Map();\n            this.hasTreeAnimated = false;\n            // Note: Currently only running on root node\n            this.updateScheduled = false;\n            this.scheduleUpdate = () => this.update();\n            this.projectionUpdateScheduled = false;\n            this.checkUpdateFailed = () => {\n                if (this.isUpdating) {\n                    this.isUpdating = false;\n                    this.clearAllSnapshots();\n                }\n            };\n            /**\n             * This is a multi-step process as shared nodes might be of different depths. Nodes\n             * are sorted by depth order, so we need to resolve the entire tree before moving to\n             * the next step.\n             */\n            this.updateProjection = () => {\n                this.projectionUpdateScheduled = false;\n                /**\n                 * Reset debug counts. Manually resetting rather than creating a new\n                 * object each frame.\n                 */\n                if (statsBuffer.value) {\n                    metrics.nodes =\n                        metrics.calculatedTargetDeltas =\n                            metrics.calculatedProjections =\n                                0;\n                }\n                this.nodes.forEach(propagateDirtyNodes);\n                this.nodes.forEach(resolveTargetDelta);\n                this.nodes.forEach(calcProjection);\n                this.nodes.forEach(cleanDirtyNodes);\n                if (statsBuffer.addProjectionMetrics) {\n                    statsBuffer.addProjectionMetrics(metrics);\n                }\n            };\n            /**\n             * Frame calculations\n             */\n            this.resolvedRelativeTargetAt = 0.0;\n            this.hasProjected = false;\n            this.isVisible = true;\n            this.animationProgress = 0;\n            /**\n             * Shared layout\n             */\n            // TODO Only running on root node\n            this.sharedNodes = new Map();\n            this.latestValues = latestValues;\n            this.root = parent ? parent.root || parent : this;\n            this.path = parent ? [...parent.path, parent] : [];\n            this.parent = parent;\n            this.depth = parent ? parent.depth + 1 : 0;\n            for (let i = 0; i < this.path.length; i++) {\n                this.path[i].shouldResetTransform = true;\n            }\n            if (this.root === this)\n                this.nodes = new FlatTree();\n        }\n        addEventListener(name, handler) {\n            if (!this.eventHandlers.has(name)) {\n                this.eventHandlers.set(name, new SubscriptionManager());\n            }\n            return this.eventHandlers.get(name).add(handler);\n        }\n        notifyListeners(name, ...args) {\n            const subscriptionManager = this.eventHandlers.get(name);\n            subscriptionManager && subscriptionManager.notify(...args);\n        }\n        hasListeners(name) {\n            return this.eventHandlers.has(name);\n        }\n        /**\n         * Lifecycles\n         */\n        mount(instance) {\n            if (this.instance)\n                return;\n            this.isSVG = isSVGElement(instance) && !isSVGSVGElement(instance);\n            this.instance = instance;\n            const { layoutId, layout, visualElement } = this.options;\n            if (visualElement && !visualElement.current) {\n                visualElement.mount(instance);\n            }\n            this.root.nodes.add(this);\n            this.parent && this.parent.children.add(this);\n            if (this.root.hasTreeAnimated && (layout || layoutId)) {\n                this.isLayoutDirty = true;\n            }\n            if (attachResizeListener) {\n                let cancelDelay;\n                const resizeUnblockUpdate = () => (this.root.updateBlockedByResize = false);\n                attachResizeListener(instance, () => {\n                    this.root.updateBlockedByResize = true;\n                    cancelDelay && cancelDelay();\n                    cancelDelay = delay(resizeUnblockUpdate, 250);\n                    if (globalProjectionState.hasAnimatedSinceResize) {\n                        globalProjectionState.hasAnimatedSinceResize = false;\n                        this.nodes.forEach(finishAnimation);\n                    }\n                });\n            }\n            if (layoutId) {\n                this.root.registerSharedNode(layoutId, this);\n            }\n            // Only register the handler if it requires layout animation\n            if (this.options.animate !== false &&\n                visualElement &&\n                (layoutId || layout)) {\n                this.addEventListener(\"didUpdate\", ({ delta, hasLayoutChanged, hasRelativeLayoutChanged, layout: newLayout, }) => {\n                    if (this.isTreeAnimationBlocked()) {\n                        this.target = undefined;\n                        this.relativeTarget = undefined;\n                        return;\n                    }\n                    // TODO: Check here if an animation exists\n                    const layoutTransition = this.options.transition ||\n                        visualElement.getDefaultTransition() ||\n                        defaultLayoutTransition;\n                    const { onLayoutAnimationStart, onLayoutAnimationComplete, } = visualElement.getProps();\n                    /**\n                     * The target layout of the element might stay the same,\n                     * but its position relative to its parent has changed.\n                     */\n                    const hasTargetChanged = !this.targetLayout ||\n                        !boxEqualsRounded(this.targetLayout, newLayout);\n                    /*\n                     * Note: Disabled to fix relative animations always triggering new\n                     * layout animations. If this causes further issues, we can try\n                     * a different approach to detecting relative target changes.\n                     */\n                    // || hasRelativeLayoutChanged\n                    /**\n                     * If the layout hasn't seemed to have changed, it might be that the\n                     * element is visually in the same place in the document but its position\n                     * relative to its parent has indeed changed. So here we check for that.\n                     */\n                    const hasOnlyRelativeTargetChanged = !hasLayoutChanged && hasRelativeLayoutChanged;\n                    if (this.options.layoutRoot ||\n                        this.resumeFrom ||\n                        hasOnlyRelativeTargetChanged ||\n                        (hasLayoutChanged &&\n                            (hasTargetChanged || !this.currentAnimation))) {\n                        if (this.resumeFrom) {\n                            this.resumingFrom = this.resumeFrom;\n                            this.resumingFrom.resumingFrom = undefined;\n                        }\n                        const animationOptions = {\n                            ...getValueTransition(layoutTransition, \"layout\"),\n                            onPlay: onLayoutAnimationStart,\n                            onComplete: onLayoutAnimationComplete,\n                        };\n                        if (visualElement.shouldReduceMotion ||\n                            this.options.layoutRoot) {\n                            animationOptions.delay = 0;\n                            animationOptions.type = false;\n                        }\n                        this.startAnimation(animationOptions);\n                        /**\n                         * Set animation origin after starting animation to avoid layout jump\n                         * caused by stopping previous layout animation\n                         */\n                        this.setAnimationOrigin(delta, hasOnlyRelativeTargetChanged);\n                    }\n                    else {\n                        /**\n                         * If the layout hasn't changed and we have an animation that hasn't started yet,\n                         * finish it immediately. Otherwise it will be animating from a location\n                         * that was probably never commited to screen and look like a jumpy box.\n                         */\n                        if (!hasLayoutChanged) {\n                            finishAnimation(this);\n                        }\n                        if (this.isLead() && this.options.onExitComplete) {\n                            this.options.onExitComplete();\n                        }\n                    }\n                    this.targetLayout = newLayout;\n                });\n            }\n        }\n        unmount() {\n            this.options.layoutId && this.willUpdate();\n            this.root.nodes.remove(this);\n            const stack = this.getStack();\n            stack && stack.remove(this);\n            this.parent && this.parent.children.delete(this);\n            this.instance = undefined;\n            this.eventHandlers.clear();\n            cancelFrame(this.updateProjection);\n        }\n        // only on the root\n        blockUpdate() {\n            this.updateManuallyBlocked = true;\n        }\n        unblockUpdate() {\n            this.updateManuallyBlocked = false;\n        }\n        isUpdateBlocked() {\n            return this.updateManuallyBlocked || this.updateBlockedByResize;\n        }\n        isTreeAnimationBlocked() {\n            return (this.isAnimationBlocked ||\n                (this.parent && this.parent.isTreeAnimationBlocked()) ||\n                false);\n        }\n        // Note: currently only running on root node\n        startUpdate() {\n            if (this.isUpdateBlocked())\n                return;\n            this.isUpdating = true;\n            this.nodes && this.nodes.forEach(resetSkewAndRotation);\n            this.animationId++;\n        }\n        getTransformTemplate() {\n            const { visualElement } = this.options;\n            return visualElement && visualElement.getProps().transformTemplate;\n        }\n        willUpdate(shouldNotifyListeners = true) {\n            this.root.hasTreeAnimated = true;\n            if (this.root.isUpdateBlocked()) {\n                this.options.onExitComplete && this.options.onExitComplete();\n                return;\n            }\n            /**\n             * If we're running optimised appear animations then these must be\n             * cancelled before measuring the DOM. This is so we can measure\n             * the true layout of the element rather than the WAAPI animation\n             * which will be unaffected by the resetSkewAndRotate step.\n             *\n             * Note: This is a DOM write. Worst case scenario is this is sandwiched\n             * between other snapshot reads which will cause unnecessary style recalculations.\n             * This has to happen here though, as we don't yet know which nodes will need\n             * snapshots in startUpdate(), but we only want to cancel optimised animations\n             * if a layout animation measurement is actually going to be affected by them.\n             */\n            if (window.MotionCancelOptimisedAnimation &&\n                !this.hasCheckedOptimisedAppear) {\n                cancelTreeOptimisedTransformAnimations(this);\n            }\n            !this.root.isUpdating && this.root.startUpdate();\n            if (this.isLayoutDirty)\n                return;\n            this.isLayoutDirty = true;\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                node.shouldResetTransform = true;\n                node.updateScroll(\"snapshot\");\n                if (node.options.layoutRoot) {\n                    node.willUpdate(false);\n                }\n            }\n            const { layoutId, layout } = this.options;\n            if (layoutId === undefined && !layout)\n                return;\n            const transformTemplate = this.getTransformTemplate();\n            this.prevTransformTemplateValue = transformTemplate\n                ? transformTemplate(this.latestValues, \"\")\n                : undefined;\n            this.updateSnapshot();\n            shouldNotifyListeners && this.notifyListeners(\"willUpdate\");\n        }\n        update() {\n            this.updateScheduled = false;\n            const updateWasBlocked = this.isUpdateBlocked();\n            // When doing an instant transition, we skip the layout update,\n            // but should still clean up the measurements so that the next\n            // snapshot could be taken correctly.\n            if (updateWasBlocked) {\n                this.unblockUpdate();\n                this.clearAllSnapshots();\n                this.nodes.forEach(clearMeasurements);\n                return;\n            }\n            if (!this.isUpdating) {\n                this.nodes.forEach(clearIsLayoutDirty);\n            }\n            this.isUpdating = false;\n            /**\n             * Write\n             */\n            this.nodes.forEach(resetTransformStyle);\n            /**\n             * Read ==================\n             */\n            // Update layout measurements of updated children\n            this.nodes.forEach(updateLayout);\n            /**\n             * Write\n             */\n            // Notify listeners that the layout is updated\n            this.nodes.forEach(notifyLayoutUpdate);\n            this.clearAllSnapshots();\n            /**\n             * Manually flush any pending updates. Ideally\n             * we could leave this to the following requestAnimationFrame but this seems\n             * to leave a flash of incorrectly styled content.\n             */\n            const now = time.now();\n            frameData.delta = clamp(0, 1000 / 60, now - frameData.timestamp);\n            frameData.timestamp = now;\n            frameData.isProcessing = true;\n            frameSteps.update.process(frameData);\n            frameSteps.preRender.process(frameData);\n            frameSteps.render.process(frameData);\n            frameData.isProcessing = false;\n        }\n        didUpdate() {\n            if (!this.updateScheduled) {\n                this.updateScheduled = true;\n                microtask.read(this.scheduleUpdate);\n            }\n        }\n        clearAllSnapshots() {\n            this.nodes.forEach(clearSnapshot);\n            this.sharedNodes.forEach(removeLeadSnapshots);\n        }\n        scheduleUpdateProjection() {\n            if (!this.projectionUpdateScheduled) {\n                this.projectionUpdateScheduled = true;\n                frame.preRender(this.updateProjection, false, true);\n            }\n        }\n        scheduleCheckAfterUnmount() {\n            /**\n             * If the unmounting node is in a layoutGroup and did trigger a willUpdate,\n             * we manually call didUpdate to give a chance to the siblings to animate.\n             * Otherwise, cleanup all snapshots to prevents future nodes from reusing them.\n             */\n            frame.postRender(() => {\n                if (this.isLayoutDirty) {\n                    this.root.didUpdate();\n                }\n                else {\n                    this.root.checkUpdateFailed();\n                }\n            });\n        }\n        /**\n         * Update measurements\n         */\n        updateSnapshot() {\n            if (this.snapshot || !this.instance)\n                return;\n            this.snapshot = this.measure();\n            if (this.snapshot &&\n                !calcLength(this.snapshot.measuredBox.x) &&\n                !calcLength(this.snapshot.measuredBox.y)) {\n                this.snapshot = undefined;\n            }\n        }\n        updateLayout() {\n            if (!this.instance)\n                return;\n            // TODO: Incorporate into a forwarded scroll offset\n            this.updateScroll();\n            if (!(this.options.alwaysMeasureLayout && this.isLead()) &&\n                !this.isLayoutDirty) {\n                return;\n            }\n            /**\n             * When a node is mounted, it simply resumes from the prevLead's\n             * snapshot instead of taking a new one, but the ancestors scroll\n             * might have updated while the prevLead is unmounted. We need to\n             * update the scroll again to make sure the layout we measure is\n             * up to date.\n             */\n            if (this.resumeFrom && !this.resumeFrom.instance) {\n                for (let i = 0; i < this.path.length; i++) {\n                    const node = this.path[i];\n                    node.updateScroll();\n                }\n            }\n            const prevLayout = this.layout;\n            this.layout = this.measure(false);\n            this.layoutCorrected = createBox();\n            this.isLayoutDirty = false;\n            this.projectionDelta = undefined;\n            this.notifyListeners(\"measure\", this.layout.layoutBox);\n            const { visualElement } = this.options;\n            visualElement &&\n                visualElement.notify(\"LayoutMeasure\", this.layout.layoutBox, prevLayout ? prevLayout.layoutBox : undefined);\n        }\n        updateScroll(phase = \"measure\") {\n            let needsMeasurement = Boolean(this.options.layoutScroll && this.instance);\n            if (this.scroll &&\n                this.scroll.animationId === this.root.animationId &&\n                this.scroll.phase === phase) {\n                needsMeasurement = false;\n            }\n            if (needsMeasurement && this.instance) {\n                const isRoot = checkIsScrollRoot(this.instance);\n                this.scroll = {\n                    animationId: this.root.animationId,\n                    phase,\n                    isRoot,\n                    offset: measureScroll(this.instance),\n                    wasRoot: this.scroll ? this.scroll.isRoot : isRoot,\n                };\n            }\n        }\n        resetTransform() {\n            if (!resetTransform)\n                return;\n            const isResetRequested = this.isLayoutDirty ||\n                this.shouldResetTransform ||\n                this.options.alwaysMeasureLayout;\n            const hasProjection = this.projectionDelta && !isDeltaZero(this.projectionDelta);\n            const transformTemplate = this.getTransformTemplate();\n            const transformTemplateValue = transformTemplate\n                ? transformTemplate(this.latestValues, \"\")\n                : undefined;\n            const transformTemplateHasChanged = transformTemplateValue !== this.prevTransformTemplateValue;\n            if (isResetRequested &&\n                this.instance &&\n                (hasProjection ||\n                    hasTransform(this.latestValues) ||\n                    transformTemplateHasChanged)) {\n                resetTransform(this.instance, transformTemplateValue);\n                this.shouldResetTransform = false;\n                this.scheduleRender();\n            }\n        }\n        measure(removeTransform = true) {\n            const pageBox = this.measurePageBox();\n            let layoutBox = this.removeElementScroll(pageBox);\n            /**\n             * Measurements taken during the pre-render stage\n             * still have transforms applied so we remove them\n             * via calculation.\n             */\n            if (removeTransform) {\n                layoutBox = this.removeTransform(layoutBox);\n            }\n            roundBox(layoutBox);\n            return {\n                animationId: this.root.animationId,\n                measuredBox: pageBox,\n                layoutBox,\n                latestValues: {},\n                source: this.id,\n            };\n        }\n        measurePageBox() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return createBox();\n            const box = visualElement.measureViewportBox();\n            const wasInScrollRoot = this.scroll?.wasRoot || this.path.some(checkNodeWasScrollRoot);\n            if (!wasInScrollRoot) {\n                // Remove viewport scroll to give page-relative coordinates\n                const { scroll } = this.root;\n                if (scroll) {\n                    translateAxis(box.x, scroll.offset.x);\n                    translateAxis(box.y, scroll.offset.y);\n                }\n            }\n            return box;\n        }\n        removeElementScroll(box) {\n            const boxWithoutScroll = createBox();\n            copyBoxInto(boxWithoutScroll, box);\n            if (this.scroll?.wasRoot) {\n                return boxWithoutScroll;\n            }\n            /**\n             * Performance TODO: Keep a cumulative scroll offset down the tree\n             * rather than loop back up the path.\n             */\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                const { scroll, options } = node;\n                if (node !== this.root && scroll && options.layoutScroll) {\n                    /**\n                     * If this is a new scroll root, we want to remove all previous scrolls\n                     * from the viewport box.\n                     */\n                    if (scroll.wasRoot) {\n                        copyBoxInto(boxWithoutScroll, box);\n                    }\n                    translateAxis(boxWithoutScroll.x, scroll.offset.x);\n                    translateAxis(boxWithoutScroll.y, scroll.offset.y);\n                }\n            }\n            return boxWithoutScroll;\n        }\n        applyTransform(box, transformOnly = false) {\n            const withTransforms = createBox();\n            copyBoxInto(withTransforms, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!transformOnly &&\n                    node.options.layoutScroll &&\n                    node.scroll &&\n                    node !== node.root) {\n                    transformBox(withTransforms, {\n                        x: -node.scroll.offset.x,\n                        y: -node.scroll.offset.y,\n                    });\n                }\n                if (!hasTransform(node.latestValues))\n                    continue;\n                transformBox(withTransforms, node.latestValues);\n            }\n            if (hasTransform(this.latestValues)) {\n                transformBox(withTransforms, this.latestValues);\n            }\n            return withTransforms;\n        }\n        removeTransform(box) {\n            const boxWithoutTransform = createBox();\n            copyBoxInto(boxWithoutTransform, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!node.instance)\n                    continue;\n                if (!hasTransform(node.latestValues))\n                    continue;\n                hasScale(node.latestValues) && node.updateSnapshot();\n                const sourceBox = createBox();\n                const nodeBox = node.measurePageBox();\n                copyBoxInto(sourceBox, nodeBox);\n                removeBoxTransforms(boxWithoutTransform, node.latestValues, node.snapshot ? node.snapshot.layoutBox : undefined, sourceBox);\n            }\n            if (hasTransform(this.latestValues)) {\n                removeBoxTransforms(boxWithoutTransform, this.latestValues);\n            }\n            return boxWithoutTransform;\n        }\n        setTargetDelta(delta) {\n            this.targetDelta = delta;\n            this.root.scheduleUpdateProjection();\n            this.isProjectionDirty = true;\n        }\n        setOptions(options) {\n            this.options = {\n                ...this.options,\n                ...options,\n                crossfade: options.crossfade !== undefined ? options.crossfade : true,\n            };\n        }\n        clearMeasurements() {\n            this.scroll = undefined;\n            this.layout = undefined;\n            this.snapshot = undefined;\n            this.prevTransformTemplateValue = undefined;\n            this.targetDelta = undefined;\n            this.target = undefined;\n            this.isLayoutDirty = false;\n        }\n        forceRelativeParentToResolveTarget() {\n            if (!this.relativeParent)\n                return;\n            /**\n             * If the parent target isn't up-to-date, force it to update.\n             * This is an unfortunate de-optimisation as it means any updating relative\n             * projection will cause all the relative parents to recalculate back\n             * up the tree.\n             */\n            if (this.relativeParent.resolvedRelativeTargetAt !==\n                frameData.timestamp) {\n                this.relativeParent.resolveTargetDelta(true);\n            }\n        }\n        resolveTargetDelta(forceRecalculation = false) {\n            /**\n             * Once the dirty status of nodes has been spread through the tree, we also\n             * need to check if we have a shared node of a different depth that has itself\n             * been dirtied.\n             */\n            const lead = this.getLead();\n            this.isProjectionDirty || (this.isProjectionDirty = lead.isProjectionDirty);\n            this.isTransformDirty || (this.isTransformDirty = lead.isTransformDirty);\n            this.isSharedProjectionDirty || (this.isSharedProjectionDirty = lead.isSharedProjectionDirty);\n            const isShared = Boolean(this.resumingFrom) || this !== lead;\n            /**\n             * We don't use transform for this step of processing so we don't\n             * need to check whether any nodes have changed transform.\n             */\n            const canSkip = !(forceRecalculation ||\n                (isShared && this.isSharedProjectionDirty) ||\n                this.isProjectionDirty ||\n                this.parent?.isProjectionDirty ||\n                this.attemptToResolveRelativeTarget ||\n                this.root.updateBlockedByResize);\n            if (canSkip)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If we have no layout, we can't perform projection, so early return\n             */\n            if (!this.layout || !(layout || layoutId))\n                return;\n            this.resolvedRelativeTargetAt = frameData.timestamp;\n            /**\n             * If we don't have a targetDelta but do have a layout, we can attempt to resolve\n             * a relativeParent. This will allow a component to perform scale correction\n             * even if no animation has started.\n             */\n            if (!this.targetDelta && !this.relativeTarget) {\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent &&\n                    relativeParent.layout &&\n                    this.animationProgress !== 1) {\n                    this.relativeParent = relativeParent;\n                    this.forceRelativeParentToResolveTarget();\n                    this.relativeTarget = createBox();\n                    this.relativeTargetOrigin = createBox();\n                    calcRelativePosition(this.relativeTargetOrigin, this.layout.layoutBox, relativeParent.layout.layoutBox);\n                    copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n            /**\n             * If we have no relative target or no target delta our target isn't valid\n             * for this frame.\n             */\n            if (!this.relativeTarget && !this.targetDelta)\n                return;\n            /**\n             * Lazy-init target data structure\n             */\n            if (!this.target) {\n                this.target = createBox();\n                this.targetWithTransforms = createBox();\n            }\n            /**\n             * If we've got a relative box for this component, resolve it into a target relative to the parent.\n             */\n            if (this.relativeTarget &&\n                this.relativeTargetOrigin &&\n                this.relativeParent &&\n                this.relativeParent.target) {\n                this.forceRelativeParentToResolveTarget();\n                calcRelativeBox(this.target, this.relativeTarget, this.relativeParent.target);\n                /**\n                 * If we've only got a targetDelta, resolve it into a target\n                 */\n            }\n            else if (this.targetDelta) {\n                if (Boolean(this.resumingFrom)) {\n                    // TODO: This is creating a new object every frame\n                    this.target = this.applyTransform(this.layout.layoutBox);\n                }\n                else {\n                    copyBoxInto(this.target, this.layout.layoutBox);\n                }\n                applyBoxDelta(this.target, this.targetDelta);\n            }\n            else {\n                /**\n                 * If no target, use own layout as target\n                 */\n                copyBoxInto(this.target, this.layout.layoutBox);\n            }\n            /**\n             * If we've been told to attempt to resolve a relative target, do so.\n             */\n            if (this.attemptToResolveRelativeTarget) {\n                this.attemptToResolveRelativeTarget = false;\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent &&\n                    Boolean(relativeParent.resumingFrom) ===\n                        Boolean(this.resumingFrom) &&\n                    !relativeParent.options.layoutScroll &&\n                    relativeParent.target &&\n                    this.animationProgress !== 1) {\n                    this.relativeParent = relativeParent;\n                    this.forceRelativeParentToResolveTarget();\n                    this.relativeTarget = createBox();\n                    this.relativeTargetOrigin = createBox();\n                    calcRelativePosition(this.relativeTargetOrigin, this.target, relativeParent.target);\n                    copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n            /**\n             * Increase debug counter for resolved target deltas\n             */\n            if (statsBuffer.value) {\n                metrics.calculatedTargetDeltas++;\n            }\n        }\n        getClosestProjectingParent() {\n            if (!this.parent ||\n                hasScale(this.parent.latestValues) ||\n                has2DTranslate(this.parent.latestValues)) {\n                return undefined;\n            }\n            if (this.parent.isProjecting()) {\n                return this.parent;\n            }\n            else {\n                return this.parent.getClosestProjectingParent();\n            }\n        }\n        isProjecting() {\n            return Boolean((this.relativeTarget ||\n                this.targetDelta ||\n                this.options.layoutRoot) &&\n                this.layout);\n        }\n        calcProjection() {\n            const lead = this.getLead();\n            const isShared = Boolean(this.resumingFrom) || this !== lead;\n            let canSkip = true;\n            /**\n             * If this is a normal layout animation and neither this node nor its nearest projecting\n             * is dirty then we can't skip.\n             */\n            if (this.isProjectionDirty || this.parent?.isProjectionDirty) {\n                canSkip = false;\n            }\n            /**\n             * If this is a shared layout animation and this node's shared projection is dirty then\n             * we can't skip.\n             */\n            if (isShared &&\n                (this.isSharedProjectionDirty || this.isTransformDirty)) {\n                canSkip = false;\n            }\n            /**\n             * If we have resolved the target this frame we must recalculate the\n             * projection to ensure it visually represents the internal calculations.\n             */\n            if (this.resolvedRelativeTargetAt === frameData.timestamp) {\n                canSkip = false;\n            }\n            if (canSkip)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If this section of the tree isn't animating we can\n             * delete our target sources for the following frame.\n             */\n            this.isTreeAnimating = Boolean((this.parent && this.parent.isTreeAnimating) ||\n                this.currentAnimation ||\n                this.pendingAnimation);\n            if (!this.isTreeAnimating) {\n                this.targetDelta = this.relativeTarget = undefined;\n            }\n            if (!this.layout || !(layout || layoutId))\n                return;\n            /**\n             * Reset the corrected box with the latest values from box, as we're then going\n             * to perform mutative operations on it.\n             */\n            copyBoxInto(this.layoutCorrected, this.layout.layoutBox);\n            /**\n             * Record previous tree scales before updating.\n             */\n            const prevTreeScaleX = this.treeScale.x;\n            const prevTreeScaleY = this.treeScale.y;\n            /**\n             * Apply all the parent deltas to this box to produce the corrected box. This\n             * is the layout box, as it will appear on screen as a result of the transforms of its parents.\n             */\n            applyTreeDeltas(this.layoutCorrected, this.treeScale, this.path, isShared);\n            /**\n             * If this layer needs to perform scale correction but doesn't have a target,\n             * use the layout as the target.\n             */\n            if (lead.layout &&\n                !lead.target &&\n                (this.treeScale.x !== 1 || this.treeScale.y !== 1)) {\n                lead.target = lead.layout.layoutBox;\n                lead.targetWithTransforms = createBox();\n            }\n            const { target } = lead;\n            if (!target) {\n                /**\n                 * If we don't have a target to project into, but we were previously\n                 * projecting, we want to remove the stored transform and schedule\n                 * a render to ensure the elements reflect the removed transform.\n                 */\n                if (this.prevProjectionDelta) {\n                    this.createProjectionDeltas();\n                    this.scheduleRender();\n                }\n                return;\n            }\n            if (!this.projectionDelta || !this.prevProjectionDelta) {\n                this.createProjectionDeltas();\n            }\n            else {\n                copyAxisDeltaInto(this.prevProjectionDelta.x, this.projectionDelta.x);\n                copyAxisDeltaInto(this.prevProjectionDelta.y, this.projectionDelta.y);\n            }\n            /**\n             * Update the delta between the corrected box and the target box before user-set transforms were applied.\n             * This will allow us to calculate the corrected borderRadius and boxShadow to compensate\n             * for our layout reprojection, but still allow them to be scaled correctly by the user.\n             * It might be that to simplify this we may want to accept that user-set scale is also corrected\n             * and we wouldn't have to keep and calc both deltas, OR we could support a user setting\n             * to allow people to choose whether these styles are corrected based on just the\n             * layout reprojection or the final bounding box.\n             */\n            calcBoxDelta(this.projectionDelta, this.layoutCorrected, target, this.latestValues);\n            if (this.treeScale.x !== prevTreeScaleX ||\n                this.treeScale.y !== prevTreeScaleY ||\n                !axisDeltaEquals(this.projectionDelta.x, this.prevProjectionDelta.x) ||\n                !axisDeltaEquals(this.projectionDelta.y, this.prevProjectionDelta.y)) {\n                this.hasProjected = true;\n                this.scheduleRender();\n                this.notifyListeners(\"projectionUpdate\", target);\n            }\n            /**\n             * Increase debug counter for recalculated projections\n             */\n            if (statsBuffer.value) {\n                metrics.calculatedProjections++;\n            }\n        }\n        hide() {\n            this.isVisible = false;\n            // TODO: Schedule render\n        }\n        show() {\n            this.isVisible = true;\n            // TODO: Schedule render\n        }\n        scheduleRender(notifyAll = true) {\n            this.options.visualElement?.scheduleRender();\n            if (notifyAll) {\n                const stack = this.getStack();\n                stack && stack.scheduleRender();\n            }\n            if (this.resumingFrom && !this.resumingFrom.instance) {\n                this.resumingFrom = undefined;\n            }\n        }\n        createProjectionDeltas() {\n            this.prevProjectionDelta = createDelta();\n            this.projectionDelta = createDelta();\n            this.projectionDeltaWithTransform = createDelta();\n        }\n        setAnimationOrigin(delta, hasOnlyRelativeTargetChanged = false) {\n            const snapshot = this.snapshot;\n            const snapshotLatestValues = snapshot ? snapshot.latestValues : {};\n            const mixedValues = { ...this.latestValues };\n            const targetDelta = createDelta();\n            if (!this.relativeParent ||\n                !this.relativeParent.options.layoutRoot) {\n                this.relativeTarget = this.relativeTargetOrigin = undefined;\n            }\n            this.attemptToResolveRelativeTarget = !hasOnlyRelativeTargetChanged;\n            const relativeLayout = createBox();\n            const snapshotSource = snapshot ? snapshot.source : undefined;\n            const layoutSource = this.layout ? this.layout.source : undefined;\n            const isSharedLayoutAnimation = snapshotSource !== layoutSource;\n            const stack = this.getStack();\n            const isOnlyMember = !stack || stack.members.length <= 1;\n            const shouldCrossfadeOpacity = Boolean(isSharedLayoutAnimation &&\n                !isOnlyMember &&\n                this.options.crossfade === true &&\n                !this.path.some(hasOpacityCrossfade));\n            this.animationProgress = 0;\n            let prevRelativeTarget;\n            this.mixTargetDelta = (latest) => {\n                const progress = latest / 1000;\n                mixAxisDelta(targetDelta.x, delta.x, progress);\n                mixAxisDelta(targetDelta.y, delta.y, progress);\n                this.setTargetDelta(targetDelta);\n                if (this.relativeTarget &&\n                    this.relativeTargetOrigin &&\n                    this.layout &&\n                    this.relativeParent &&\n                    this.relativeParent.layout) {\n                    calcRelativePosition(relativeLayout, this.layout.layoutBox, this.relativeParent.layout.layoutBox);\n                    mixBox(this.relativeTarget, this.relativeTargetOrigin, relativeLayout, progress);\n                    /**\n                     * If this is an unchanged relative target we can consider the\n                     * projection not dirty.\n                     */\n                    if (prevRelativeTarget &&\n                        boxEquals(this.relativeTarget, prevRelativeTarget)) {\n                        this.isProjectionDirty = false;\n                    }\n                    if (!prevRelativeTarget)\n                        prevRelativeTarget = createBox();\n                    copyBoxInto(prevRelativeTarget, this.relativeTarget);\n                }\n                if (isSharedLayoutAnimation) {\n                    this.animationValues = mixedValues;\n                    mixValues(mixedValues, snapshotLatestValues, this.latestValues, progress, shouldCrossfadeOpacity, isOnlyMember);\n                }\n                this.root.scheduleUpdateProjection();\n                this.scheduleRender();\n                this.animationProgress = progress;\n            };\n            this.mixTargetDelta(this.options.layoutRoot ? 1000 : 0);\n        }\n        startAnimation(options) {\n            this.notifyListeners(\"animationStart\");\n            this.currentAnimation?.stop();\n            this.resumingFrom?.currentAnimation?.stop();\n            if (this.pendingAnimation) {\n                cancelFrame(this.pendingAnimation);\n                this.pendingAnimation = undefined;\n            }\n            /**\n             * Start the animation in the next frame to have a frame with progress 0,\n             * where the target is the same as when the animation started, so we can\n             * calculate the relative positions correctly for instant transitions.\n             */\n            this.pendingAnimation = frame.update(() => {\n                globalProjectionState.hasAnimatedSinceResize = true;\n                activeAnimations.layout++;\n                this.motionValue || (this.motionValue = motionValue(0));\n                this.currentAnimation = animateSingleValue(this.motionValue, [0, 1000], {\n                    ...options,\n                    velocity: 0,\n                    isSync: true,\n                    onUpdate: (latest) => {\n                        this.mixTargetDelta(latest);\n                        options.onUpdate && options.onUpdate(latest);\n                    },\n                    onStop: () => {\n                        activeAnimations.layout--;\n                    },\n                    onComplete: () => {\n                        activeAnimations.layout--;\n                        options.onComplete && options.onComplete();\n                        this.completeAnimation();\n                    },\n                });\n                if (this.resumingFrom) {\n                    this.resumingFrom.currentAnimation = this.currentAnimation;\n                }\n                this.pendingAnimation = undefined;\n            });\n        }\n        completeAnimation() {\n            if (this.resumingFrom) {\n                this.resumingFrom.currentAnimation = undefined;\n                this.resumingFrom.preserveOpacity = undefined;\n            }\n            const stack = this.getStack();\n            stack && stack.exitAnimationComplete();\n            this.resumingFrom =\n                this.currentAnimation =\n                    this.animationValues =\n                        undefined;\n            this.notifyListeners(\"animationComplete\");\n        }\n        finishAnimation() {\n            if (this.currentAnimation) {\n                this.mixTargetDelta && this.mixTargetDelta(animationTarget);\n                this.currentAnimation.stop();\n            }\n            this.completeAnimation();\n        }\n        applyTransformsToTarget() {\n            const lead = this.getLead();\n            let { targetWithTransforms, target, layout, latestValues } = lead;\n            if (!targetWithTransforms || !target || !layout)\n                return;\n            /**\n             * If we're only animating position, and this element isn't the lead element,\n             * then instead of projecting into the lead box we instead want to calculate\n             * a new target that aligns the two boxes but maintains the layout shape.\n             */\n            if (this !== lead &&\n                this.layout &&\n                layout &&\n                shouldAnimatePositionOnly(this.options.animationType, this.layout.layoutBox, layout.layoutBox)) {\n                target = this.target || createBox();\n                const xLength = calcLength(this.layout.layoutBox.x);\n                target.x.min = lead.target.x.min;\n                target.x.max = target.x.min + xLength;\n                const yLength = calcLength(this.layout.layoutBox.y);\n                target.y.min = lead.target.y.min;\n                target.y.max = target.y.min + yLength;\n            }\n            copyBoxInto(targetWithTransforms, target);\n            /**\n             * Apply the latest user-set transforms to the targetBox to produce the targetBoxFinal.\n             * This is the final box that we will then project into by calculating a transform delta and\n             * applying it to the corrected box.\n             */\n            transformBox(targetWithTransforms, latestValues);\n            /**\n             * Update the delta between the corrected box and the final target box, after\n             * user-set transforms are applied to it. This will be used by the renderer to\n             * create a transform style that will reproject the element from its layout layout\n             * into the desired bounding box.\n             */\n            calcBoxDelta(this.projectionDeltaWithTransform, this.layoutCorrected, targetWithTransforms, latestValues);\n        }\n        registerSharedNode(layoutId, node) {\n            if (!this.sharedNodes.has(layoutId)) {\n                this.sharedNodes.set(layoutId, new NodeStack());\n            }\n            const stack = this.sharedNodes.get(layoutId);\n            stack.add(node);\n            const config = node.options.initialPromotionConfig;\n            node.promote({\n                transition: config ? config.transition : undefined,\n                preserveFollowOpacity: config && config.shouldPreserveFollowOpacity\n                    ? config.shouldPreserveFollowOpacity(node)\n                    : undefined,\n            });\n        }\n        isLead() {\n            const stack = this.getStack();\n            return stack ? stack.lead === this : true;\n        }\n        getLead() {\n            const { layoutId } = this.options;\n            return layoutId ? this.getStack()?.lead || this : this;\n        }\n        getPrevLead() {\n            const { layoutId } = this.options;\n            return layoutId ? this.getStack()?.prevLead : undefined;\n        }\n        getStack() {\n            const { layoutId } = this.options;\n            if (layoutId)\n                return this.root.sharedNodes.get(layoutId);\n        }\n        promote({ needsReset, transition, preserveFollowOpacity, } = {}) {\n            const stack = this.getStack();\n            if (stack)\n                stack.promote(this, preserveFollowOpacity);\n            if (needsReset) {\n                this.projectionDelta = undefined;\n                this.needsReset = true;\n            }\n            if (transition)\n                this.setOptions({ transition });\n        }\n        relegate() {\n            const stack = this.getStack();\n            if (stack) {\n                return stack.relegate(this);\n            }\n            else {\n                return false;\n            }\n        }\n        resetSkewAndRotation() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return;\n            // If there's no detected skew or rotation values, we can early return without a forced render.\n            let hasDistortingTransform = false;\n            /**\n             * An unrolled check for rotation values. Most elements don't have any rotation and\n             * skipping the nested loop and new object creation is 50% faster.\n             */\n            const { latestValues } = visualElement;\n            if (latestValues.z ||\n                latestValues.rotate ||\n                latestValues.rotateX ||\n                latestValues.rotateY ||\n                latestValues.rotateZ ||\n                latestValues.skewX ||\n                latestValues.skewY) {\n                hasDistortingTransform = true;\n            }\n            // If there's no distorting values, we don't need to do any more.\n            if (!hasDistortingTransform)\n                return;\n            const resetValues = {};\n            if (latestValues.z) {\n                resetDistortingTransform(\"z\", visualElement, resetValues, this.animationValues);\n            }\n            // Check the skew and rotate value of all axes and reset to 0\n            for (let i = 0; i < transformAxes.length; i++) {\n                resetDistortingTransform(`rotate${transformAxes[i]}`, visualElement, resetValues, this.animationValues);\n                resetDistortingTransform(`skew${transformAxes[i]}`, visualElement, resetValues, this.animationValues);\n            }\n            // Force a render of this element to apply the transform with all skews and rotations\n            // set to 0.\n            visualElement.render();\n            // Put back all the values we reset\n            for (const key in resetValues) {\n                visualElement.setStaticValue(key, resetValues[key]);\n                if (this.animationValues) {\n                    this.animationValues[key] = resetValues[key];\n                }\n            }\n            // Schedule a render for the next frame. This ensures we won't visually\n            // see the element with the reset rotate value applied.\n            visualElement.scheduleRender();\n        }\n        getProjectionStyles(styleProp) {\n            if (!this.instance || this.isSVG)\n                return undefined;\n            if (!this.isVisible) {\n                return hiddenVisibility;\n            }\n            const styles = {\n                visibility: \"\",\n            };\n            const transformTemplate = this.getTransformTemplate();\n            if (this.needsReset) {\n                this.needsReset = false;\n                styles.opacity = \"\";\n                styles.pointerEvents =\n                    resolveMotionValue(styleProp?.pointerEvents) || \"\";\n                styles.transform = transformTemplate\n                    ? transformTemplate(this.latestValues, \"\")\n                    : \"none\";\n                return styles;\n            }\n            const lead = this.getLead();\n            if (!this.projectionDelta || !this.layout || !lead.target) {\n                const emptyStyles = {};\n                if (this.options.layoutId) {\n                    emptyStyles.opacity =\n                        this.latestValues.opacity !== undefined\n                            ? this.latestValues.opacity\n                            : 1;\n                    emptyStyles.pointerEvents =\n                        resolveMotionValue(styleProp?.pointerEvents) || \"\";\n                }\n                if (this.hasProjected && !hasTransform(this.latestValues)) {\n                    emptyStyles.transform = transformTemplate\n                        ? transformTemplate({}, \"\")\n                        : \"none\";\n                    this.hasProjected = false;\n                }\n                return emptyStyles;\n            }\n            const valuesToRender = lead.animationValues || lead.latestValues;\n            this.applyTransformsToTarget();\n            styles.transform = buildProjectionTransform(this.projectionDeltaWithTransform, this.treeScale, valuesToRender);\n            if (transformTemplate) {\n                styles.transform = transformTemplate(valuesToRender, styles.transform);\n            }\n            const { x, y } = this.projectionDelta;\n            styles.transformOrigin = `${x.origin * 100}% ${y.origin * 100}% 0`;\n            if (lead.animationValues) {\n                /**\n                 * If the lead component is animating, assign this either the entering/leaving\n                 * opacity\n                 */\n                styles.opacity =\n                    lead === this\n                        ? valuesToRender.opacity ??\n                            this.latestValues.opacity ??\n                            1\n                        : this.preserveOpacity\n                            ? this.latestValues.opacity\n                            : valuesToRender.opacityExit;\n            }\n            else {\n                /**\n                 * Or we're not animating at all, set the lead component to its layout\n                 * opacity and other components to hidden.\n                 */\n                styles.opacity =\n                    lead === this\n                        ? valuesToRender.opacity !== undefined\n                            ? valuesToRender.opacity\n                            : \"\"\n                        : valuesToRender.opacityExit !== undefined\n                            ? valuesToRender.opacityExit\n                            : 0;\n            }\n            /**\n             * Apply scale correction\n             */\n            for (const key in scaleCorrectors) {\n                if (valuesToRender[key] === undefined)\n                    continue;\n                const { correct, applyTo, isCSSVariable } = scaleCorrectors[key];\n                /**\n                 * Only apply scale correction to the value if we have an\n                 * active projection transform. Otherwise these values become\n                 * vulnerable to distortion if the element changes size without\n                 * a corresponding layout animation.\n                 */\n                const corrected = styles.transform === \"none\"\n                    ? valuesToRender[key]\n                    : correct(valuesToRender[key], lead);\n                if (applyTo) {\n                    const num = applyTo.length;\n                    for (let i = 0; i < num; i++) {\n                        styles[applyTo[i]] = corrected;\n                    }\n                }\n                else {\n                    // If this is a CSS variable, set it directly on the instance.\n                    // Replacing this function from creating styles to setting them\n                    // would be a good place to remove per frame object creation\n                    if (isCSSVariable) {\n                        this.options.visualElement.renderState.vars[key] = corrected;\n                    }\n                    else {\n                        styles[key] = corrected;\n                    }\n                }\n            }\n            /**\n             * Disable pointer events on follow components. This is to ensure\n             * that if a follow component covers a lead component it doesn't block\n             * pointer events on the lead.\n             */\n            if (this.options.layoutId) {\n                styles.pointerEvents =\n                    lead === this\n                        ? resolveMotionValue(styleProp?.pointerEvents) || \"\"\n                        : \"none\";\n            }\n            return styles;\n        }\n        clearSnapshot() {\n            this.resumeFrom = this.snapshot = undefined;\n        }\n        // Only run on root\n        resetTree() {\n            this.root.nodes.forEach((node) => node.currentAnimation?.stop());\n            this.root.nodes.forEach(clearMeasurements);\n            this.root.sharedNodes.clear();\n        }\n    };\n}\nfunction updateLayout(node) {\n    node.updateLayout();\n}\nfunction notifyLayoutUpdate(node) {\n    const snapshot = node.resumeFrom?.snapshot || node.snapshot;\n    if (node.isLead() &&\n        node.layout &&\n        snapshot &&\n        node.hasListeners(\"didUpdate\")) {\n        const { layoutBox: layout, measuredBox: measuredLayout } = node.layout;\n        const { animationType } = node.options;\n        const isShared = snapshot.source !== node.layout.source;\n        // TODO Maybe we want to also resize the layout snapshot so we don't trigger\n        // animations for instance if layout=\"size\" and an element has only changed position\n        if (animationType === \"size\") {\n            eachAxis((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = calcLength(axisSnapshot);\n                axisSnapshot.min = layout[axis].min;\n                axisSnapshot.max = axisSnapshot.min + length;\n            });\n        }\n        else if (shouldAnimatePositionOnly(animationType, snapshot.layoutBox, layout)) {\n            eachAxis((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = calcLength(layout[axis]);\n                axisSnapshot.max = axisSnapshot.min + length;\n                /**\n                 * Ensure relative target gets resized and rerendererd\n                 */\n                if (node.relativeTarget && !node.currentAnimation) {\n                    node.isProjectionDirty = true;\n                    node.relativeTarget[axis].max =\n                        node.relativeTarget[axis].min + length;\n                }\n            });\n        }\n        const layoutDelta = createDelta();\n        calcBoxDelta(layoutDelta, layout, snapshot.layoutBox);\n        const visualDelta = createDelta();\n        if (isShared) {\n            calcBoxDelta(visualDelta, node.applyTransform(measuredLayout, true), snapshot.measuredBox);\n        }\n        else {\n            calcBoxDelta(visualDelta, layout, snapshot.layoutBox);\n        }\n        const hasLayoutChanged = !isDeltaZero(layoutDelta);\n        let hasRelativeLayoutChanged = false;\n        if (!node.resumeFrom) {\n            const relativeParent = node.getClosestProjectingParent();\n            /**\n             * If the relativeParent is itself resuming from a different element then\n             * the relative snapshot is not relavent\n             */\n            if (relativeParent && !relativeParent.resumeFrom) {\n                const { snapshot: parentSnapshot, layout: parentLayout } = relativeParent;\n                if (parentSnapshot && parentLayout) {\n                    const relativeSnapshot = createBox();\n                    calcRelativePosition(relativeSnapshot, snapshot.layoutBox, parentSnapshot.layoutBox);\n                    const relativeLayout = createBox();\n                    calcRelativePosition(relativeLayout, layout, parentLayout.layoutBox);\n                    if (!boxEqualsRounded(relativeSnapshot, relativeLayout)) {\n                        hasRelativeLayoutChanged = true;\n                    }\n                    if (relativeParent.options.layoutRoot) {\n                        node.relativeTarget = relativeLayout;\n                        node.relativeTargetOrigin = relativeSnapshot;\n                        node.relativeParent = relativeParent;\n                    }\n                }\n            }\n        }\n        node.notifyListeners(\"didUpdate\", {\n            layout,\n            snapshot,\n            delta: visualDelta,\n            layoutDelta,\n            hasLayoutChanged,\n            hasRelativeLayoutChanged,\n        });\n    }\n    else if (node.isLead()) {\n        const { onExitComplete } = node.options;\n        onExitComplete && onExitComplete();\n    }\n    /**\n     * Clearing transition\n     * TODO: Investigate why this transition is being passed in as {type: false } from Framer\n     * and why we need it at all\n     */\n    node.options.transition = undefined;\n}\nfunction propagateDirtyNodes(node) {\n    /**\n     * Increase debug counter for nodes encountered this frame\n     */\n    if (statsBuffer.value) {\n        metrics.nodes++;\n    }\n    if (!node.parent)\n        return;\n    /**\n     * If this node isn't projecting, propagate isProjectionDirty. It will have\n     * no performance impact but it will allow the next child that *is* projecting\n     * but *isn't* dirty to just check its parent to see if *any* ancestor needs\n     * correcting.\n     */\n    if (!node.isProjecting()) {\n        node.isProjectionDirty = node.parent.isProjectionDirty;\n    }\n    /**\n     * Propagate isSharedProjectionDirty and isTransformDirty\n     * throughout the whole tree. A future revision can take another look at\n     * this but for safety we still recalcualte shared nodes.\n     */\n    node.isSharedProjectionDirty || (node.isSharedProjectionDirty = Boolean(node.isProjectionDirty ||\n        node.parent.isProjectionDirty ||\n        node.parent.isSharedProjectionDirty));\n    node.isTransformDirty || (node.isTransformDirty = node.parent.isTransformDirty);\n}\nfunction cleanDirtyNodes(node) {\n    node.isProjectionDirty =\n        node.isSharedProjectionDirty =\n            node.isTransformDirty =\n                false;\n}\nfunction clearSnapshot(node) {\n    node.clearSnapshot();\n}\nfunction clearMeasurements(node) {\n    node.clearMeasurements();\n}\nfunction clearIsLayoutDirty(node) {\n    node.isLayoutDirty = false;\n}\nfunction resetTransformStyle(node) {\n    const { visualElement } = node.options;\n    if (visualElement && visualElement.getProps().onBeforeLayoutMeasure) {\n        visualElement.notify(\"BeforeLayoutMeasure\");\n    }\n    node.resetTransform();\n}\nfunction finishAnimation(node) {\n    node.finishAnimation();\n    node.targetDelta = node.relativeTarget = node.target = undefined;\n    node.isProjectionDirty = true;\n}\nfunction resolveTargetDelta(node) {\n    node.resolveTargetDelta();\n}\nfunction calcProjection(node) {\n    node.calcProjection();\n}\nfunction resetSkewAndRotation(node) {\n    node.resetSkewAndRotation();\n}\nfunction removeLeadSnapshots(stack) {\n    stack.removeLeadSnapshot();\n}\nfunction mixAxisDelta(output, delta, p) {\n    output.translate = mixNumber(delta.translate, 0, p);\n    output.scale = mixNumber(delta.scale, 1, p);\n    output.origin = delta.origin;\n    output.originPoint = delta.originPoint;\n}\nfunction mixAxis(output, from, to, p) {\n    output.min = mixNumber(from.min, to.min, p);\n    output.max = mixNumber(from.max, to.max, p);\n}\nfunction mixBox(output, from, to, p) {\n    mixAxis(output.x, from.x, to.x, p);\n    mixAxis(output.y, from.y, to.y, p);\n}\nfunction hasOpacityCrossfade(node) {\n    return (node.animationValues && node.animationValues.opacityExit !== undefined);\n}\nconst defaultLayoutTransition = {\n    duration: 0.45,\n    ease: [0.4, 0, 0.1, 1],\n};\nconst userAgentContains = (string) => typeof navigator !== \"undefined\" &&\n    navigator.userAgent &&\n    navigator.userAgent.toLowerCase().includes(string);\n/**\n * Measured bounding boxes must be rounded in Safari and\n * left untouched in Chrome, otherwise non-integer layouts within scaled-up elements\n * can appear to jump.\n */\nconst roundPoint = userAgentContains(\"applewebkit/\") && !userAgentContains(\"chrome/\")\n    ? Math.round\n    : noop;\nfunction roundAxis(axis) {\n    // Round to the nearest .5 pixels to support subpixel layouts\n    axis.min = roundPoint(axis.min);\n    axis.max = roundPoint(axis.max);\n}\nfunction roundBox(box) {\n    roundAxis(box.x);\n    roundAxis(box.y);\n}\nfunction shouldAnimatePositionOnly(animationType, snapshot, layout) {\n    return (animationType === \"position\" ||\n        (animationType === \"preserve-aspect\" &&\n            !isNear(aspectRatio(snapshot), aspectRatio(layout), 0.2)));\n}\nfunction checkNodeWasScrollRoot(node) {\n    return node !== node.root && node.scroll?.wasRoot;\n}\n\nexport { cleanDirtyNodes, createProjectionNode, mixAxis, mixAxisDelta, mixBox, propagateDirtyNodes };\n", "import { createProjectionNode } from './create-projection-node.mjs';\nimport { addDomEvent } from '../../events/add-dom-event.mjs';\n\nconst DocumentProjectionNode = createProjectionNode({\n    attachResizeListener: (ref, notify) => addDomEvent(ref, \"resize\", notify),\n    measureScroll: () => ({\n        x: document.documentElement.scrollLeft || document.body.scrollLeft,\n        y: document.documentElement.scrollTop || document.body.scrollTop,\n    }),\n    checkIsScrollRoot: () => true,\n});\n\nexport { DocumentProjectionNode };\n", "import { createProjectionNode } from './create-projection-node.mjs';\nimport { DocumentProjectionNode } from './DocumentProjectionNode.mjs';\n\nconst rootProjectionNode = {\n    current: undefined,\n};\nconst HTMLProjectionNode = createProjectionNode({\n    measureScroll: (instance) => ({\n        x: instance.scrollLeft,\n        y: instance.scrollTop,\n    }),\n    defaultParent: () => {\n        if (!rootProjectionNode.current) {\n            const documentNode = new DocumentProjectionNode({});\n            documentNode.mount(window);\n            documentNode.setOptions({ layoutScroll: true });\n            rootProjectionNode.current = documentNode;\n        }\n        return rootProjectionNode.current;\n    },\n    resetTransform: (instance, value) => {\n        instance.style.transform = value !== undefined ? value : \"none\";\n    },\n    checkIsScrollRoot: (instance) => Boolean(window.getComputedStyle(instance).position === \"fixed\"),\n});\n\nexport { HTMLProjectionNode, rootProjectionNode };\n", "import { DragGesture } from '../../gestures/drag/index.mjs';\nimport { PanGesture } from '../../gestures/pan/index.mjs';\nimport { MeasureLayout } from './layout/MeasureLayout.mjs';\nimport { HTMLProjectionNode } from '../../projection/node/HTMLProjectionNode.mjs';\n\nconst drag = {\n    pan: {\n        Feature: PanGesture,\n    },\n    drag: {\n        Feature: DragGesture,\n        ProjectionNode: HTMLProjectionNode,\n        MeasureLayout,\n    },\n};\n\nexport { drag };\n", "import { hover, frame } from 'motion-dom';\nimport { extractEventInfo } from '../events/event-info.mjs';\nimport { Feature } from '../motion/features/Feature.mjs';\n\nfunction handleHoverEvent(node, event, lifecycle) {\n    const { props } = node;\n    if (node.animationState && props.whileHover) {\n        node.animationState.setActive(\"whileHover\", lifecycle === \"Start\");\n    }\n    const eventName = (\"onHover\" + lifecycle);\n    const callback = props[eventName];\n    if (callback) {\n        frame.postRender(() => callback(event, extractEventInfo(event)));\n    }\n}\nclass HoverGesture extends Feature {\n    mount() {\n        const { current } = this.node;\n        if (!current)\n            return;\n        this.unmount = hover(current, (_element, startEvent) => {\n            handleHoverEvent(this.node, startEvent, \"Start\");\n            return (endEvent) => handleHoverEvent(this.node, endEvent, \"End\");\n        });\n    }\n    unmount() { }\n}\n\nexport { HoverGesture };\n", "import { pipe } from 'motion-utils';\nimport { addDomEvent } from '../events/add-dom-event.mjs';\nimport { Feature } from '../motion/features/Feature.mjs';\n\nclass FocusGesture extends Feature {\n    constructor() {\n        super(...arguments);\n        this.isActive = false;\n    }\n    onFocus() {\n        let isFocusVisible = false;\n        /**\n         * If this element doesn't match focus-visible then don't\n         * apply whileHover. But, if matches throws that focus-visible\n         * is not a valid selector then in that browser outline styles will be applied\n         * to the element by default and we want to match that behaviour with whileFocus.\n         */\n        try {\n            isFocusVisible = this.node.current.matches(\":focus-visible\");\n        }\n        catch (e) {\n            isFocusVisible = true;\n        }\n        if (!isFocusVisible || !this.node.animationState)\n            return;\n        this.node.animationState.setActive(\"whileFocus\", true);\n        this.isActive = true;\n    }\n    onBlur() {\n        if (!this.isActive || !this.node.animationState)\n            return;\n        this.node.animationState.setActive(\"whileFocus\", false);\n        this.isActive = false;\n    }\n    mount() {\n        this.unmount = pipe(addDomEvent(this.node.current, \"focus\", () => this.onFocus()), addDomEvent(this.node.current, \"blur\", () => this.onBlur()));\n    }\n    unmount() { }\n}\n\nexport { FocusGesture };\n", "import { press, frame } from 'motion-dom';\nimport { extractEventInfo } from '../events/event-info.mjs';\nimport { Feature } from '../motion/features/Feature.mjs';\n\nfunction handlePressEvent(node, event, lifecycle) {\n    const { props } = node;\n    if (node.current instanceof HTMLButtonElement && node.current.disabled) {\n        return;\n    }\n    if (node.animationState && props.whileTap) {\n        node.animationState.setActive(\"whileTap\", lifecycle === \"Start\");\n    }\n    const eventName = (\"onTap\" + (lifecycle === \"End\" ? \"\" : lifecycle));\n    const callback = props[eventName];\n    if (callback) {\n        frame.postRender(() => callback(event, extractEventInfo(event)));\n    }\n}\nclass PressGesture extends Feature {\n    mount() {\n        const { current } = this.node;\n        if (!current)\n            return;\n        this.unmount = press(current, (_element, startEvent) => {\n            handlePressEvent(this.node, startEvent, \"Start\");\n            return (endEvent, { success }) => handlePressEvent(this.node, endEvent, success ? \"End\" : \"Cancel\");\n        }, { useGlobalTarget: this.node.props.globalTapTarget });\n    }\n    unmount() { }\n}\n\nexport { PressGesture };\n", "/**\n * Map an IntersectionHandler callback to an element. We only ever make one handler for one\n * element, so even though these handlers might all be triggered by different\n * observers, we can keep them in the same map.\n */\nconst observerCallbacks = new WeakMap();\n/**\n * Multiple observers can be created for multiple element/document roots. Each with\n * different settings. So here we store dictionaries of observers to each root,\n * using serialised settings (threshold/margin) as lookup keys.\n */\nconst observers = new WeakMap();\nconst fireObserverCallback = (entry) => {\n    const callback = observerCallbacks.get(entry.target);\n    callback && callback(entry);\n};\nconst fireAllObserverCallbacks = (entries) => {\n    entries.forEach(fireObserverCallback);\n};\nfunction initIntersectionObserver({ root, ...options }) {\n    const lookupRoot = root || document;\n    /**\n     * If we don't have an observer lookup map for this root, create one.\n     */\n    if (!observers.has(lookupRoot)) {\n        observers.set(lookupRoot, {});\n    }\n    const rootObservers = observers.get(lookupRoot);\n    const key = JSON.stringify(options);\n    /**\n     * If we don't have an observer for this combination of root and settings,\n     * create one.\n     */\n    if (!rootObservers[key]) {\n        rootObservers[key] = new IntersectionObserver(fireAllObserverCallbacks, { root, ...options });\n    }\n    return rootObservers[key];\n}\nfunction observeIntersection(element, options, callback) {\n    const rootInteresectionObserver = initIntersectionObserver(options);\n    observerCallbacks.set(element, callback);\n    rootInteresectionObserver.observe(element);\n    return () => {\n        observerCallbacks.delete(element);\n        rootInteresectionObserver.unobserve(element);\n    };\n}\n\nexport { observeIntersection };\n", "import { Feature } from '../Feature.mjs';\nimport { observeIntersection } from './observers.mjs';\n\nconst thresholdNames = {\n    some: 0,\n    all: 1,\n};\nclass InViewFeature extends Feature {\n    constructor() {\n        super(...arguments);\n        this.hasEnteredView = false;\n        this.isInView = false;\n    }\n    startObserver() {\n        this.unmount();\n        const { viewport = {} } = this.node.getProps();\n        const { root, margin: rootMargin, amount = \"some\", once } = viewport;\n        const options = {\n            root: root ? root.current : undefined,\n            rootMargin,\n            threshold: typeof amount === \"number\" ? amount : thresholdNames[amount],\n        };\n        const onIntersectionUpdate = (entry) => {\n            const { isIntersecting } = entry;\n            /**\n             * If there's been no change in the viewport state, early return.\n             */\n            if (this.isInView === isIntersecting)\n                return;\n            this.isInView = isIntersecting;\n            /**\n             * Handle hasEnteredView. If this is only meant to run once, and\n             * element isn't visible, early return. Otherwise set hasEnteredView to true.\n             */\n            if (once && !isIntersecting && this.hasEnteredView) {\n                return;\n            }\n            else if (isIntersecting) {\n                this.hasEnteredView = true;\n            }\n            if (this.node.animationState) {\n                this.node.animationState.setActive(\"whileInView\", isIntersecting);\n            }\n            /**\n             * Use the latest committed props rather than the ones in scope\n             * when this observer is created\n             */\n            const { onViewportEnter, onViewportLeave } = this.node.getProps();\n            const callback = isIntersecting ? onViewportEnter : onViewportLeave;\n            callback && callback(entry);\n        };\n        return observeIntersection(this.node.current, options, onIntersectionUpdate);\n    }\n    mount() {\n        this.startObserver();\n    }\n    update() {\n        if (typeof IntersectionObserver === \"undefined\")\n            return;\n        const { props, prevProps } = this.node;\n        const hasOptionsChanged = [\"amount\", \"margin\", \"root\"].some(hasViewportOptionChanged(props, prevProps));\n        if (hasOptionsChanged) {\n            this.startObserver();\n        }\n    }\n    unmount() { }\n}\nfunction hasViewportOptionChanged({ viewport = {} }, { viewport: prevViewport = {} } = {}) {\n    return (name) => viewport[name] !== prevViewport[name];\n}\n\nexport { InViewFeature };\n", "import { HoverGesture } from '../../gestures/hover.mjs';\nimport { FocusGesture } from '../../gestures/focus.mjs';\nimport { PressGesture } from '../../gestures/press.mjs';\nimport { InViewFeature } from './viewport/index.mjs';\n\nconst gestureAnimations = {\n    inView: {\n        Feature: InViewFeature,\n    },\n    tap: {\n        Feature: PressGesture,\n    },\n    focus: {\n        Feature: FocusGesture,\n    },\n    hover: {\n        Feature: HoverGesture,\n    },\n};\n\nexport { gestureAnimations };\n", "import { HTMLProjectionNode } from '../../projection/node/HTMLProjectionNode.mjs';\nimport { MeasureLayout } from './layout/MeasureLayout.mjs';\n\nconst layout = {\n    layout: {\n        ProjectionNode: HTMLProjectionNode,\n        MeasureLayout,\n    },\n};\n\nexport { layout };\n", "// Does this device prefer reduced motion? Returns `null` server-side.\nconst prefersReducedMotion = { current: null };\nconst hasReducedMotionListener = { current: false };\n\nexport { hasReducedMotionListener, prefersReducedMotion };\n", "import { isBrowser } from '../is-browser.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from './state.mjs';\n\nfunction initPrefersReducedMotion() {\n    hasReducedMotionListener.current = true;\n    if (!isBrowser)\n        return;\n    if (window.matchMedia) {\n        const motionMediaQuery = window.matchMedia(\"(prefers-reduced-motion)\");\n        const setReducedMotionPreferences = () => (prefersReducedMotion.current = motionMediaQuery.matches);\n        motionMediaQuery.addListener(setReducedMotionPreferences);\n        setReducedMotionPreferences();\n    }\n    else {\n        prefersReducedMotion.current = false;\n    }\n}\n\nexport { initPrefersReducedMotion };\n", "const visualElementStore = new WeakMap();\n\nexport { visualElementStore };\n", "import { isMotionValue, motionValue } from 'motion-dom';\n\nfunction updateMotionValuesFromProps(element, next, prev) {\n    for (const key in next) {\n        const nextValue = next[key];\n        const prevValue = prev[key];\n        if (isMotionValue(nextValue)) {\n            /**\n             * If this is a motion value found in props or style, we want to add it\n             * to our visual element's motion value map.\n             */\n            element.addValue(key, nextValue);\n        }\n        else if (isMotionValue(prevValue)) {\n            /**\n             * If we're swapping from a motion value to a static value,\n             * create a new motion value from that\n             */\n            element.addValue(key, motionValue(nextValue, { owner: element }));\n        }\n        else if (prevValue !== nextValue) {\n            /**\n             * If this is a flat value that has changed, update the motion value\n             * or create one if it doesn't exist. We only want to do this if we're\n             * not handling the value with our animation state.\n             */\n            if (element.hasValue(key)) {\n                const existingValue = element.getValue(key);\n                if (existingValue.liveStyle === true) {\n                    existingValue.jump(nextValue);\n                }\n                else if (!existingValue.hasAnimated) {\n                    existingValue.set(nextValue);\n                }\n            }\n            else {\n                const latestValue = element.getStaticValue(key);\n                element.addValue(key, motionValue(latestValue !== undefined ? latestValue : nextValue, { owner: element }));\n            }\n        }\n    }\n    // Handle removed values\n    for (const key in prev) {\n        if (next[key] === undefined)\n            element.removeValue(key);\n    }\n    return next;\n}\n\nexport { updateMotionValuesFromProps };\n", "import { KeyframeResolver, time, frame, isMotionValue, cancelFrame, transformProps, motionValue, findValueType, complex, getAnimatableNone } from 'motion-dom';\nimport { warnOnce, isNumericalString, isZeroValueString, SubscriptionManager } from 'motion-utils';\nimport { featureDefinitions } from '../motion/features/definitions.mjs';\nimport { createBox } from '../projection/geometry/models.mjs';\nimport { initPrefersReducedMotion } from '../utils/reduced-motion/index.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from '../utils/reduced-motion/state.mjs';\nimport { visualElementStore } from './store.mjs';\nimport { isControllingVariants, isVariantNode } from './utils/is-controlling-variants.mjs';\nimport { updateMotionValuesFromProps } from './utils/motion-values.mjs';\nimport { resolveVariantFromProps } from './utils/resolve-variants.mjs';\n\nconst propEventHandlers = [\n    \"AnimationStart\",\n    \"AnimationComplete\",\n    \"Update\",\n    \"BeforeLayoutMeasure\",\n    \"LayoutMeasure\",\n    \"LayoutAnimationStart\",\n    \"LayoutAnimationComplete\",\n];\n/**\n * A VisualElement is an imperative abstraction around UI elements such as\n * HTMLElement, SVGElement, Three.Object3D etc.\n */\nclass VisualElement {\n    /**\n     * This method takes React props and returns found MotionValues. For example, HTML\n     * MotionValues will be found within the style prop, whereas for Three.js within attribute arrays.\n     *\n     * This isn't an abstract method as it needs calling in the constructor, but it is\n     * intended to be one.\n     */\n    scrapeMotionValuesFromProps(_props, _prevProps, _visualElement) {\n        return {};\n    }\n    constructor({ parent, props, presenceContext, reducedMotionConfig, blockInitialAnimation, visualState, }, options = {}) {\n        /**\n         * A reference to the current underlying Instance, e.g. a HTMLElement\n         * or Three.Mesh etc.\n         */\n        this.current = null;\n        /**\n         * A set containing references to this VisualElement's children.\n         */\n        this.children = new Set();\n        /**\n         * Determine what role this visual element should take in the variant tree.\n         */\n        this.isVariantNode = false;\n        this.isControllingVariants = false;\n        /**\n         * Decides whether this VisualElement should animate in reduced motion\n         * mode.\n         *\n         * TODO: This is currently set on every individual VisualElement but feels\n         * like it could be set globally.\n         */\n        this.shouldReduceMotion = null;\n        /**\n         * A map of all motion values attached to this visual element. Motion\n         * values are source of truth for any given animated value. A motion\n         * value might be provided externally by the component via props.\n         */\n        this.values = new Map();\n        this.KeyframeResolver = KeyframeResolver;\n        /**\n         * Cleanup functions for active features (hover/tap/exit etc)\n         */\n        this.features = {};\n        /**\n         * A map of every subscription that binds the provided or generated\n         * motion values onChange listeners to this visual element.\n         */\n        this.valueSubscriptions = new Map();\n        /**\n         * A reference to the previously-provided motion values as returned\n         * from scrapeMotionValuesFromProps. We use the keys in here to determine\n         * if any motion values need to be removed after props are updated.\n         */\n        this.prevMotionValues = {};\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        /**\n         * An object containing an unsubscribe function for each prop event subscription.\n         * For example, every \"Update\" event can have multiple subscribers via\n         * VisualElement.on(), but only one of those can be defined via the onUpdate prop.\n         */\n        this.propEventSubscriptions = {};\n        this.notifyUpdate = () => this.notify(\"Update\", this.latestValues);\n        this.render = () => {\n            if (!this.current)\n                return;\n            this.triggerBuild();\n            this.renderInstance(this.current, this.renderState, this.props.style, this.projection);\n        };\n        this.renderScheduledAt = 0.0;\n        this.scheduleRender = () => {\n            const now = time.now();\n            if (this.renderScheduledAt < now) {\n                this.renderScheduledAt = now;\n                frame.render(this.render, false, true);\n            }\n        };\n        const { latestValues, renderState } = visualState;\n        this.latestValues = latestValues;\n        this.baseTarget = { ...latestValues };\n        this.initialValues = props.initial ? { ...latestValues } : {};\n        this.renderState = renderState;\n        this.parent = parent;\n        this.props = props;\n        this.presenceContext = presenceContext;\n        this.depth = parent ? parent.depth + 1 : 0;\n        this.reducedMotionConfig = reducedMotionConfig;\n        this.options = options;\n        this.blockInitialAnimation = Boolean(blockInitialAnimation);\n        this.isControllingVariants = isControllingVariants(props);\n        this.isVariantNode = isVariantNode(props);\n        if (this.isVariantNode) {\n            this.variantChildren = new Set();\n        }\n        this.manuallyAnimateOnMount = Boolean(parent && parent.current);\n        /**\n         * Any motion values that are provided to the element when created\n         * aren't yet bound to the element, as this would technically be impure.\n         * However, we iterate through the motion values and set them to the\n         * initial values for this component.\n         *\n         * TODO: This is impure and we should look at changing this to run on mount.\n         * Doing so will break some tests but this isn't necessarily a breaking change,\n         * more a reflection of the test.\n         */\n        const { willChange, ...initialMotionValues } = this.scrapeMotionValuesFromProps(props, {}, this);\n        for (const key in initialMotionValues) {\n            const value = initialMotionValues[key];\n            if (latestValues[key] !== undefined && isMotionValue(value)) {\n                value.set(latestValues[key], false);\n            }\n        }\n    }\n    mount(instance) {\n        this.current = instance;\n        visualElementStore.set(instance, this);\n        if (this.projection && !this.projection.instance) {\n            this.projection.mount(instance);\n        }\n        if (this.parent && this.isVariantNode && !this.isControllingVariants) {\n            this.removeFromVariantTree = this.parent.addVariantChild(this);\n        }\n        this.values.forEach((value, key) => this.bindToMotionValue(key, value));\n        if (!hasReducedMotionListener.current) {\n            initPrefersReducedMotion();\n        }\n        this.shouldReduceMotion =\n            this.reducedMotionConfig === \"never\"\n                ? false\n                : this.reducedMotionConfig === \"always\"\n                    ? true\n                    : prefersReducedMotion.current;\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(this.shouldReduceMotion !== true, \"You have Reduced Motion enabled on your device. Animations may not appear as expected.\");\n        }\n        if (this.parent)\n            this.parent.children.add(this);\n        this.update(this.props, this.presenceContext);\n    }\n    unmount() {\n        this.projection && this.projection.unmount();\n        cancelFrame(this.notifyUpdate);\n        cancelFrame(this.render);\n        this.valueSubscriptions.forEach((remove) => remove());\n        this.valueSubscriptions.clear();\n        this.removeFromVariantTree && this.removeFromVariantTree();\n        this.parent && this.parent.children.delete(this);\n        for (const key in this.events) {\n            this.events[key].clear();\n        }\n        for (const key in this.features) {\n            const feature = this.features[key];\n            if (feature) {\n                feature.unmount();\n                feature.isMounted = false;\n            }\n        }\n        this.current = null;\n    }\n    bindToMotionValue(key, value) {\n        if (this.valueSubscriptions.has(key)) {\n            this.valueSubscriptions.get(key)();\n        }\n        const valueIsTransform = transformProps.has(key);\n        if (valueIsTransform && this.onBindTransform) {\n            this.onBindTransform();\n        }\n        const removeOnChange = value.on(\"change\", (latestValue) => {\n            this.latestValues[key] = latestValue;\n            this.props.onUpdate && frame.preRender(this.notifyUpdate);\n            if (valueIsTransform && this.projection) {\n                this.projection.isTransformDirty = true;\n            }\n        });\n        const removeOnRenderRequest = value.on(\"renderRequest\", this.scheduleRender);\n        let removeSyncCheck;\n        if (window.MotionCheckAppearSync) {\n            removeSyncCheck = window.MotionCheckAppearSync(this, key, value);\n        }\n        this.valueSubscriptions.set(key, () => {\n            removeOnChange();\n            removeOnRenderRequest();\n            if (removeSyncCheck)\n                removeSyncCheck();\n            if (value.owner)\n                value.stop();\n        });\n    }\n    sortNodePosition(other) {\n        /**\n         * If these nodes aren't even of the same type we can't compare their depth.\n         */\n        if (!this.current ||\n            !this.sortInstanceNodePosition ||\n            this.type !== other.type) {\n            return 0;\n        }\n        return this.sortInstanceNodePosition(this.current, other.current);\n    }\n    updateFeatures() {\n        let key = \"animation\";\n        for (key in featureDefinitions) {\n            const featureDefinition = featureDefinitions[key];\n            if (!featureDefinition)\n                continue;\n            const { isEnabled, Feature: FeatureConstructor } = featureDefinition;\n            /**\n             * If this feature is enabled but not active, make a new instance.\n             */\n            if (!this.features[key] &&\n                FeatureConstructor &&\n                isEnabled(this.props)) {\n                this.features[key] = new FeatureConstructor(this);\n            }\n            /**\n             * If we have a feature, mount or update it.\n             */\n            if (this.features[key]) {\n                const feature = this.features[key];\n                if (feature.isMounted) {\n                    feature.update();\n                }\n                else {\n                    feature.mount();\n                    feature.isMounted = true;\n                }\n            }\n        }\n    }\n    triggerBuild() {\n        this.build(this.renderState, this.latestValues, this.props);\n    }\n    /**\n     * Measure the current viewport box with or without transforms.\n     * Only measures axis-aligned boxes, rotate and skew must be manually\n     * removed with a re-render to work.\n     */\n    measureViewportBox() {\n        return this.current\n            ? this.measureInstanceViewportBox(this.current, this.props)\n            : createBox();\n    }\n    getStaticValue(key) {\n        return this.latestValues[key];\n    }\n    setStaticValue(key, value) {\n        this.latestValues[key] = value;\n    }\n    /**\n     * Update the provided props. Ensure any newly-added motion values are\n     * added to our map, old ones removed, and listeners updated.\n     */\n    update(props, presenceContext) {\n        if (props.transformTemplate || this.props.transformTemplate) {\n            this.scheduleRender();\n        }\n        this.prevProps = this.props;\n        this.props = props;\n        this.prevPresenceContext = this.presenceContext;\n        this.presenceContext = presenceContext;\n        /**\n         * Update prop event handlers ie onAnimationStart, onAnimationComplete\n         */\n        for (let i = 0; i < propEventHandlers.length; i++) {\n            const key = propEventHandlers[i];\n            if (this.propEventSubscriptions[key]) {\n                this.propEventSubscriptions[key]();\n                delete this.propEventSubscriptions[key];\n            }\n            const listenerName = (\"on\" + key);\n            const listener = props[listenerName];\n            if (listener) {\n                this.propEventSubscriptions[key] = this.on(key, listener);\n            }\n        }\n        this.prevMotionValues = updateMotionValuesFromProps(this, this.scrapeMotionValuesFromProps(props, this.prevProps, this), this.prevMotionValues);\n        if (this.handleChildMotionValue) {\n            this.handleChildMotionValue();\n        }\n    }\n    getProps() {\n        return this.props;\n    }\n    /**\n     * Returns the variant definition with a given name.\n     */\n    getVariant(name) {\n        return this.props.variants ? this.props.variants[name] : undefined;\n    }\n    /**\n     * Returns the defined default transition on this component.\n     */\n    getDefaultTransition() {\n        return this.props.transition;\n    }\n    getTransformPagePoint() {\n        return this.props.transformPagePoint;\n    }\n    getClosestVariantNode() {\n        return this.isVariantNode\n            ? this\n            : this.parent\n                ? this.parent.getClosestVariantNode()\n                : undefined;\n    }\n    /**\n     * Add a child visual element to our set of children.\n     */\n    addVariantChild(child) {\n        const closestVariantNode = this.getClosestVariantNode();\n        if (closestVariantNode) {\n            closestVariantNode.variantChildren &&\n                closestVariantNode.variantChildren.add(child);\n            return () => closestVariantNode.variantChildren.delete(child);\n        }\n    }\n    /**\n     * Add a motion value and bind it to this visual element.\n     */\n    addValue(key, value) {\n        // Remove existing value if it exists\n        const existingValue = this.values.get(key);\n        if (value !== existingValue) {\n            if (existingValue)\n                this.removeValue(key);\n            this.bindToMotionValue(key, value);\n            this.values.set(key, value);\n            this.latestValues[key] = value.get();\n        }\n    }\n    /**\n     * Remove a motion value and unbind any active subscriptions.\n     */\n    removeValue(key) {\n        this.values.delete(key);\n        const unsubscribe = this.valueSubscriptions.get(key);\n        if (unsubscribe) {\n            unsubscribe();\n            this.valueSubscriptions.delete(key);\n        }\n        delete this.latestValues[key];\n        this.removeValueFromRenderState(key, this.renderState);\n    }\n    /**\n     * Check whether we have a motion value for this key\n     */\n    hasValue(key) {\n        return this.values.has(key);\n    }\n    getValue(key, defaultValue) {\n        if (this.props.values && this.props.values[key]) {\n            return this.props.values[key];\n        }\n        let value = this.values.get(key);\n        if (value === undefined && defaultValue !== undefined) {\n            value = motionValue(defaultValue === null ? undefined : defaultValue, { owner: this });\n            this.addValue(key, value);\n        }\n        return value;\n    }\n    /**\n     * If we're trying to animate to a previously unencountered value,\n     * we need to check for it in our state and as a last resort read it\n     * directly from the instance (which might have performance implications).\n     */\n    readValue(key, target) {\n        let value = this.latestValues[key] !== undefined || !this.current\n            ? this.latestValues[key]\n            : this.getBaseTargetFromProps(this.props, key) ??\n                this.readValueFromInstance(this.current, key, this.options);\n        if (value !== undefined && value !== null) {\n            if (typeof value === \"string\" &&\n                (isNumericalString(value) || isZeroValueString(value))) {\n                // If this is a number read as a string, ie \"0\" or \"200\", convert it to a number\n                value = parseFloat(value);\n            }\n            else if (!findValueType(value) && complex.test(target)) {\n                value = getAnimatableNone(key, target);\n            }\n            this.setBaseTarget(key, isMotionValue(value) ? value.get() : value);\n        }\n        return isMotionValue(value) ? value.get() : value;\n    }\n    /**\n     * Set the base target to later animate back to. This is currently\n     * only hydrated on creation and when we first read a value.\n     */\n    setBaseTarget(key, value) {\n        this.baseTarget[key] = value;\n    }\n    /**\n     * Find the base target for a value thats been removed from all animation\n     * props.\n     */\n    getBaseTarget(key) {\n        const { initial } = this.props;\n        let valueFromInitial;\n        if (typeof initial === \"string\" || typeof initial === \"object\") {\n            const variant = resolveVariantFromProps(this.props, initial, this.presenceContext?.custom);\n            if (variant) {\n                valueFromInitial = variant[key];\n            }\n        }\n        /**\n         * If this value still exists in the current initial variant, read that.\n         */\n        if (initial && valueFromInitial !== undefined) {\n            return valueFromInitial;\n        }\n        /**\n         * Alternatively, if this VisualElement config has defined a getBaseTarget\n         * so we can read the value from an alternative source, try that.\n         */\n        const target = this.getBaseTargetFromProps(this.props, key);\n        if (target !== undefined && !isMotionValue(target))\n            return target;\n        /**\n         * If the value was initially defined on initial, but it doesn't any more,\n         * return undefined. Otherwise return the value as initially read from the DOM.\n         */\n        return this.initialValues[key] !== undefined &&\n            valueFromInitial === undefined\n            ? undefined\n            : this.baseTarget[key];\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new SubscriptionManager();\n        }\n        return this.events[eventName].add(callback);\n    }\n    notify(eventName, ...args) {\n        if (this.events[eventName]) {\n            this.events[eventName].notify(...args);\n        }\n    }\n}\n\nexport { VisualElement };\n", "import { DOMKeyframesResolver, isMotionValue } from 'motion-dom';\nimport { VisualElement } from '../VisualElement.mjs';\n\nclass DOMVisualElement extends VisualElement {\n    constructor() {\n        super(...arguments);\n        this.KeyframeResolver = DOMKeyframesResolver;\n    }\n    sortInstanceNodePosition(a, b) {\n        /**\n         * compareDocumentPosition returns a bitmask, by using the bitwise &\n         * we're returning true if 2 in that bitmask is set to true. 2 is set\n         * to true if b preceeds a.\n         */\n        return a.compareDocumentPosition(b) & 2 ? 1 : -1;\n    }\n    getBaseTargetFromProps(props, key) {\n        return props.style\n            ? props.style[key]\n            : undefined;\n    }\n    removeValueFromRenderState(key, { vars, style }) {\n        delete vars[key];\n        delete style[key];\n    }\n    handleChildMotionValue() {\n        if (this.childSubscription) {\n            this.childSubscription();\n            delete this.childSubscription;\n        }\n        const { children } = this.props;\n        if (isMotionValue(children)) {\n            this.childSubscription = children.on(\"change\", (latest) => {\n                if (this.current) {\n                    this.current.textContent = `${latest}`;\n                }\n            });\n        }\n    }\n}\n\nexport { DOMVisualElement };\n", "function renderHTML(element, { style, vars }, styleProp, projection) {\n    Object.assign(element.style, style, projection && projection.getProjectionStyles(styleProp));\n    // Loop over any CSS variables and assign those.\n    for (const key in vars) {\n        element.style.setProperty(key, vars[key]);\n    }\n}\n\nexport { renderHTML };\n", "import { transformProps, defaultTransformValue, readTransformValue, isCSSVariableName } from 'motion-dom';\nimport { measureViewportBox } from '../../projection/utils/measure.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { buildHTMLStyles } from './utils/build-styles.mjs';\nimport { renderHTML } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\n\nfunction getComputedStyle(element) {\n    return window.getComputedStyle(element);\n}\nclass HTMLVisualElement extends DOMVisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"html\";\n        this.renderInstance = renderHTML;\n    }\n    readValueFromInstance(instance, key) {\n        if (transformProps.has(key)) {\n            return this.projection?.isProjecting\n                ? defaultTransformValue(key)\n                : readTransformValue(instance, key);\n        }\n        else {\n            const computedStyle = getComputedStyle(instance);\n            const value = (isCSSVariableName(key)\n                ? computedStyle.getPropertyValue(key)\n                : computedStyle[key]) || 0;\n            return typeof value === \"string\" ? value.trim() : value;\n        }\n    }\n    measureInstanceViewportBox(instance, { transformPagePoint }) {\n        return measureViewportBox(instance, transformPagePoint);\n    }\n    build(renderState, latestValues, props) {\n        buildHTMLStyles(renderState, latestValues, props.transformTemplate);\n    }\n    scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n        return scrapeMotionValuesFromProps(props, prevProps, visualElement);\n    }\n}\n\nexport { HTMLVisualElement, getComputedStyle };\n", "/**\n * A set of attribute names that are always read/written as camel case.\n */\nconst camelCaseAttributes = new Set([\n    \"baseFrequency\",\n    \"diffuseConstant\",\n    \"kernelMatrix\",\n    \"kernelUnitLength\",\n    \"keySplines\",\n    \"keyTimes\",\n    \"limitingConeAngle\",\n    \"markerHeight\",\n    \"markerWidth\",\n    \"numOctaves\",\n    \"targetX\",\n    \"targetY\",\n    \"surfaceScale\",\n    \"specularConstant\",\n    \"specularExponent\",\n    \"stdDeviation\",\n    \"tableValues\",\n    \"viewBox\",\n    \"gradientTransform\",\n    \"pathLength\",\n    \"startOffset\",\n    \"textLength\",\n    \"lengthAdjust\",\n]);\n\nexport { camelCaseAttributes };\n", "import { camelToDash } from '../../dom/utils/camel-to-dash.mjs';\nimport { renderHTML } from '../../html/utils/render.mjs';\nimport { camelCaseAttributes } from './camel-case-attrs.mjs';\n\nfunction renderSVG(element, renderState, _styleProp, projection) {\n    renderHTML(element, renderState, undefined, projection);\n    for (const key in renderState.attrs) {\n        element.setAttribute(!camelCaseAttributes.has(key) ? camelToDash(key) : key, renderState.attrs[key]);\n    }\n}\n\nexport { renderSVG };\n", "import { transformProps, getDefaultValueType } from 'motion-dom';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { camelToDash } from '../dom/utils/camel-to-dash.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { camelCaseAttributes } from './utils/camel-case-attrs.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\nimport { renderSVG } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\n\nclass SVGVisualElement extends DOMVisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"svg\";\n        this.isSVGTag = false;\n        this.measureInstanceViewportBox = createBox;\n    }\n    getBaseTargetFromProps(props, key) {\n        return props[key];\n    }\n    readValueFromInstance(instance, key) {\n        if (transformProps.has(key)) {\n            const defaultType = getDefaultValueType(key);\n            return defaultType ? defaultType.default || 0 : 0;\n        }\n        key = !camelCaseAttributes.has(key) ? camelToDash(key) : key;\n        return instance.getAttribute(key);\n    }\n    scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n        return scrapeMotionValuesFromProps(props, prevProps, visualElement);\n    }\n    build(renderState, latestValues, props) {\n        buildSVGAttrs(renderState, latestValues, this.isSVGTag, props.transformTemplate, props.style);\n    }\n    renderInstance(instance, renderState, styleProp, projection) {\n        renderSVG(instance, renderState, styleProp, projection);\n    }\n    mount(instance) {\n        this.isSVGTag = isSVGTag(instance.tagName);\n        super.mount(instance);\n    }\n}\n\nexport { SVGVisualElement };\n", "import { Fragment } from 'react';\nimport { HTMLVisualElement } from '../html/HTMLVisualElement.mjs';\nimport { SVGVisualElement } from '../svg/SVGVisualElement.mjs';\nimport { isSVGComponent } from './utils/is-svg-component.mjs';\n\nconst createDomVisualElement = (Component, options) => {\n    return isSVGComponent(Component)\n        ? new SVGVisualElement(options)\n        : new HTMLVisualElement(options, {\n            allowProjection: Component !== Fragment,\n        });\n};\n\nexport { createDomVisualElement };\n", "import { animations } from '../../../motion/features/animations.mjs';\nimport { drag } from '../../../motion/features/drag.mjs';\nimport { gestureAnimations } from '../../../motion/features/gestures.mjs';\nimport { layout } from '../../../motion/features/layout.mjs';\nimport { createMotionComponentFactory } from '../create-factory.mjs';\nimport { createDomVisualElement } from '../../dom/create-visual-element.mjs';\n\nconst createMotionComponent = /*@__PURE__*/ createMotionComponentFactory({\n    ...animations,\n    ...gestureAnimations,\n    ...drag,\n    ...layout,\n}, createDomVisualElement);\n\nexport { createMotionComponent };\n", "import { createDOMMotionComponentProxy } from '../create-proxy.mjs';\nimport { createMotionComponent } from './create.mjs';\n\nconst motion = /*@__PURE__*/ createDOMMotionComponentProxy(createMotionComponent);\n\nexport { motion };\n"], "names": ["createContext", "useRef", "useLayoutEffect", "useEffect", "useContext", "id", "useId", "useCallback", "useMemo", "createProjectionNode", "useInsertionEffect", "layout", "drag", "MeasureLayout", "jsxs", "jsx", "forwardRef", "hasTransform", "isSVGTag", "Fragment", "createElement", "scrapeMotionValuesFromProps", "createMotionComponent", "delay", "animations", "context", "motionValue", "transformPoint", "distance", "rootProjectionNode", "info", "point", "timestamp", "event", "time", "dragSnapToO<PERSON>in", "Component", "progress", "getComputedStyle"], "mappings": ";;AAGA,MAAM,qBAAqBA,aAAa,cAAC,EAAE;ACM3C,SAAS,YAAY,MAAM;AACvB,QAAM,MAAMC,aAAM,OAAC,IAAI;AACvB,MAAI,IAAI,YAAY,MAAM;AACtB,QAAI,UAAU,KAAM;AAAA,EAC5B;AACI,SAAO,IAAI;AACf;ACfA,MAAM,YAAY,OAAO,WAAW;ACGpC,MAAM,4BAA4B,YAAYC,aAAAA,kBAAkBC,aAAS;ACGzE,MAAM,kBACUH,6BAAAA,cAAc,IAAI;ACDlC,MAAM,sBAAsBA,aAAAA,cAAc;AAAA,EACtC,oBAAoB,CAAC,MAAM;AAAA,EAC3B,UAAU;AAAA,EACV,eAAe;AACnB,CAAC;ACgBD,SAAS,YAAY,YAAY,MAAM;AACnC,QAAM,UAAUI,aAAU,WAAC,eAAe;AAC1C,MAAI,YAAY;AACZ,WAAO,CAAC,MAAM,IAAI;AACtB,QAAM,EAAE,WAAW,gBAAgB,SAAU,IAAG;AAGhD,QAAMC,MAAKC,aAAAA,MAAO;AAClBH,eAAAA,UAAU,MAAM;AACZ,QAAI,WAAW;AACX,aAAO,SAASE,GAAE;AAAA,IAC9B;AAAA,EACA,GAAO,CAAC,SAAS,CAAC;AACd,QAAM,eAAeE,aAAAA,YAAY,MAAM,aAAa,kBAAkB,eAAeF,GAAE,GAAG,CAACA,KAAI,gBAAgB,SAAS,CAAC;AACzH,SAAO,CAAC,aAAa,iBAAiB,CAAC,OAAO,YAAY,IAAI,CAAC,IAAI;AACvE;ACtCA,MAAM,cAAcL,aAAa,cAAC,EAAE,QAAQ,MAAK,CAAE;ACHnD,MAAM,eAAe;AAAA,EACjB,WAAW;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACH;AAAA,EACD,MAAM,CAAC,MAAM;AAAA,EACb,MAAM,CAAC,QAAQ,cAAc;AAAA,EAC7B,OAAO,CAAC,YAAY;AAAA,EACpB,OAAO,CAAC,cAAc,gBAAgB,YAAY;AAAA,EAClD,KAAK,CAAC,YAAY,SAAS,cAAc,aAAa;AAAA,EACtD,KAAK,CAAC,SAAS,cAAc,qBAAqB,UAAU;AAAA,EAC5D,QAAQ,CAAC,eAAe,mBAAmB,iBAAiB;AAAA,EAC5D,QAAQ,CAAC,UAAU,UAAU;AACjC;AACA,MAAM,qBAAqB,CAAE;AAC7B,WAAW,OAAO,cAAc;AAC5B,qBAAmB,GAAG,IAAI;AAAA,IACtB,WAAW,CAAC,UAAU,aAAa,GAAG,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,IAAI,CAAC;AAAA,EACvE;AACL;ACvBA,SAAS,aAAa,UAAU;AAC5B,aAAW,OAAO,UAAU;AACxB,uBAAmB,GAAG,IAAI;AAAA,MACtB,GAAG,mBAAmB,GAAG;AAAA,MACzB,GAAG,SAAS,GAAG;AAAA,IAClB;AAAA,EACT;AACA;ACHA,MAAM,mBAAmB,oBAAI,IAAI;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,CAAC;AASD,SAAS,kBAAkB,KAAK;AAC5B,SAAQ,IAAI,WAAW,OAAO,KACzB,IAAI,WAAW,MAAM,KAAK,QAAQ,eACnC,IAAI,WAAW,QAAQ,KACvB,IAAI,WAAW,OAAO,KACtB,IAAI,WAAW,OAAO,KACtB,IAAI,WAAW,UAAU,KACzB,iBAAiB,IAAI,GAAG;AAChC;ACpDA,IAAI,gBAAgB,CAAC,QAAQ,CAAC,kBAAkB,GAAG;AACnD,SAAS,wBAAwB,aAAa;AAC1C,MAAI,OAAO,gBAAgB;AACvB;AAEJ,kBAAgB,CAAC,QAAQ,IAAI,WAAW,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,YAAY,GAAG;AAC7F;AAcA,IAAI;AAMA,0BAAwB,QAAQ,wBAAwB,EAAE,OAAO;AACrE,QACM;AAEN;AACA,SAAS,YAAY,OAAO,OAAO,oBAAoB;AACnD,QAAM,gBAAgB,CAAE;AACxB,aAAW,OAAO,OAAO;AAQrB,QAAI,QAAQ,YAAY,OAAO,MAAM,WAAW;AAC5C;AACJ,QAAI,cAAc,GAAG,KAChB,uBAAuB,QAAQ,kBAAkB,GAAG,KACpD,CAAC,SAAS,CAAC,kBAAkB,GAAG;AAAA,IAEhC,MAAM,WAAW,KACd,IAAI,WAAW,QAAQ,GAAI;AAC/B,oBAAc,GAAG,IACb,MAAM,GAAG;AAAA,IACzB;AAAA,EACA;AACI,SAAO;AACX;ACtDA,SAAS,8BAA8B,kBAAkB;AACjD,MAAA,OAAO,UAAU,aAAa;AACvB,WAAA;AAAA,EAAA;AAML,QAAA,qCAAqB,IAAI;AACzB,QAAA,4BAA4B,IAAI,SAAS;AACA;AACvC,eAAS,OAAO,sDAAsD;AAAA,IAAA;AAEnE,WAAA,iBAAiB,GAAG,IAAI;AAAA,EACnC;AACO,SAAA,IAAI,MAAM,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMxC,KAAK,CAAC,SAAS,QAAQ;AACnB,UAAI,QAAQ;AACD,eAAA;AAIX,UAAI,CAAC,eAAe,IAAI,GAAG,GAAG;AAC1B,uBAAe,IAAI,KAAK,iBAAiB,GAAG,CAAC;AAAA,MAAA;AAE1C,aAAA,eAAe,IAAI,GAAG;AAAA,IAAA;AAAA,EACjC,CACH;AACL;AChCA,MAAM,gBAAgCA,6BAAa,cAAC,EAAE;ACHtD,SAAS,oBAAoB,GAAG;AAC5B,SAAQ,MAAM,QACV,OAAO,MAAM,YACb,OAAO,EAAE,UAAU;AAC3B;ACDA,SAAS,eAAe,GAAG;AACvB,SAAO,OAAO,MAAM,YAAY,MAAM,QAAQ,CAAC;AACnD;ACLA,MAAM,uBAAuB;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,MAAM,eAAe,CAAC,WAAW,GAAG,oBAAoB;ACLxD,SAAS,sBAAsB,OAAO;AAClC,SAAQ,oBAAoB,MAAM,OAAO,KACrC,aAAa,KAAK,CAAC,SAAS,eAAe,MAAM,IAAI,CAAC,CAAC;AAC/D;AACA,SAAS,cAAc,OAAO;AAC1B,SAAO,QAAQ,sBAAsB,KAAK,KAAK,MAAM,QAAQ;AACjE;ACPA,SAAS,uBAAuB,OAAO,SAAS;AAC5C,MAAI,sBAAsB,KAAK,GAAG;AAC9B,UAAM,EAAE,SAAS,QAAO,IAAK;AAC7B,WAAO;AAAA,MACH,SAAS,YAAY,SAAS,eAAe,OAAO,IAC9C,UACA;AAAA,MACN,SAAS,eAAe,OAAO,IAAI,UAAU;AAAA,IAChD;AAAA,EACT;AACI,SAAO,MAAM,YAAY,QAAQ,UAAU,CAAE;AACjD;ACVA,SAAS,uBAAuB,OAAO;AACnC,QAAM,EAAE,SAAS,QAAS,IAAG,uBAAuB,OAAOI,aAAAA,WAAW,aAAa,CAAC;AACpF,SAAOI,qBAAQ,OAAO,EAAE,SAAS,QAAO,IAAK,CAAC,0BAA0B,OAAO,GAAG,0BAA0B,OAAO,CAAC,CAAC;AACzH;AACA,SAAS,0BAA0B,MAAM;AACrC,SAAO,MAAM,QAAQ,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI;AAClD;ACVA,MAAM,wBAAwB,OAAO,IAAI,uBAAuB;ACAhE,SAAS,YAAY,KAAK;AACtB,SAAQ,OACJ,OAAO,QAAQ,YACf,OAAO,UAAU,eAAe,KAAK,KAAK,SAAS;AAC3D;ACGA,SAAS,aAAa,aAAa,eAAe,aAAa;AAC3D,SAAOD,aAAAA;AAAAA,IAAY,CAAC,aAAa;AAC7B,UAAI,UAAU;AACV,oBAAY,WAAW,YAAY,QAAQ,QAAQ;AAAA,MAC/D;AACQ,UAAI,eAAe;AACf,YAAI,UAAU;AACV,wBAAc,MAAM,QAAQ;AAAA,QAC5C,OACiB;AACD,wBAAc,QAAS;AAAA,QACvC;AAAA,MACA;AACQ,UAAI,aAAa;AACb,YAAI,OAAO,gBAAgB,YAAY;AACnC,sBAAY,QAAQ;AAAA,QACpC,WACqB,YAAY,WAAW,GAAG;AAC/B,sBAAY,UAAU;AAAA,QACtC;AAAA,MACA;AAAA,IACK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,CAAC,aAAa;AAAA,EAAC;AACnB;AChCA,MAAM,cAAc,CAAC,QAAQ,IAAI,QAAQ,oBAAoB,OAAO,EAAE,YAAa;ACDnF,MAAM,wBAAwB;AAC9B,MAAM,+BAA+B,UAAU,YAAY,qBAAqB;ACGhF,MAAM,2BAA2BP,aAAa,cAAC,EAAE;ACKjD,SAAS,iBAAiB,WAAW,aAAa,OAAO,qBAAqB,2BAA2B;AACrG,QAAM,EAAE,eAAe,WAAWI,aAAAA,WAAW,aAAa;AAC1D,QAAM,cAAcA,aAAU,WAAC,WAAW;AAC1C,QAAM,kBAAkBA,aAAU,WAAC,eAAe;AAClD,QAAM,sBAAsBA,aAAAA,WAAW,mBAAmB,EAAE;AAC5D,QAAM,mBAAmBH,aAAM,OAAC,IAAI;AAIpC,wBAAsB,uBAAuB,YAAY;AACzD,MAAI,CAAC,iBAAiB,WAAW,qBAAqB;AAClD,qBAAiB,UAAU,oBAAoB,WAAW;AAAA,MACtD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,uBAAuB,kBACjB,gBAAgB,YAAY,QAC5B;AAAA,MACN;AAAA,IACZ,CAAS;AAAA,EACT;AACI,QAAM,gBAAgB,iBAAiB;AAKvC,QAAM,2BAA2BG,aAAU,WAAC,wBAAwB;AACpE,MAAI,iBACA,CAAC,cAAc,cACf,8BACC,cAAc,SAAS,UAAU,cAAc,SAAS,QAAQ;AACjEK,2BAAqB,iBAAiB,SAAS,OAAO,2BAA2B,wBAAwB;AAAA,EACjH;AACI,QAAM,YAAYR,aAAM,OAAC,KAAK;AAC9BS,eAAAA,mBAAmB,MAAM;AAKrB,QAAI,iBAAiB,UAAU,SAAS;AACpC,oBAAc,OAAO,OAAO,eAAe;AAAA,IACvD;AAAA,EACA,CAAK;AAKD,QAAM,oBAAoB,MAAM,4BAA4B;AAC5D,QAAM,eAAeT,aAAAA,OAAO,QAAQ,iBAAiB,KACjD,CAAC,OAAO,0BAA0B,iBAAiB,KACnD,OAAO,8BAA8B,iBAAiB,CAAC;AAC3D,4BAA0B,MAAM;AAC5B,QAAI,CAAC;AACD;AACJ,cAAU,UAAU;AACpB,WAAO,kBAAkB;AACzB,kBAAc,eAAgB;AAC9B,cAAU,OAAO,cAAc,MAAM;AAWrC,QAAI,aAAa,WAAW,cAAc,gBAAgB;AACtD,oBAAc,eAAe,eAAgB;AAAA,IACzD;AAAA,EACA,CAAK;AACDE,eAAAA,UAAU,MAAM;AACZ,QAAI,CAAC;AACD;AACJ,QAAI,CAAC,aAAa,WAAW,cAAc,gBAAgB;AACvD,oBAAc,eAAe,eAAgB;AAAA,IACzD;AACQ,QAAI,aAAa,SAAS;AAEtB,qBAAe,MAAM;AACjB,eAAO,8BAA8B,iBAAiB;AAAA,MACtE,CAAa;AACD,mBAAa,UAAU;AAAA,IACnC;AAAA,EACA,CAAK;AACD,SAAO;AACX;AACA,SAASM,uBAAqB,eAAe,OAAO,2BAA2B,wBAAwB;AACnG,QAAM,EAAE,UAAU,QAAAE,SAAQ,MAAAC,OAAM,iBAAiB,cAAc,YAAY,gBAAe,IAAM;AAChG,gBAAc,aAAa,IAAI,0BAA0B,cAAc,cAAc,MAAM,uBAAuB,IAC5G,SACA,yBAAyB,cAAc,MAAM,CAAC;AACpD,gBAAc,WAAW,WAAW;AAAA,IAChC;AAAA,IACA,QAAAD;AAAA,IACA,qBAAqB,QAAQC,KAAI,KAAM,mBAAmB,YAAY,eAAe;AAAA,IACrF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,eAAe,OAAOD,YAAW,WAAWA,UAAS;AAAA,IACrD;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACR,CAAK;AACL;AACA,SAAS,yBAAyB,eAAe;AAC7C,MAAI,CAAC;AACD,WAAO;AACX,SAAO,cAAc,QAAQ,oBAAoB,QAC3C,cAAc,aACd,yBAAyB,cAAc,MAAM;AACvD;ACzGA,SAAS,8BAA8B,EAAE,mBAAmB,qBAAqB,WAAW,gBAAgB,aAAc;AACtH,uBAAqB,aAAa,iBAAiB;AAC1C,WAAA,gBAAgB,OAAO,aAAa;AAKrC,QAAAE;AACJ,UAAM,iBAAiB;AAAA,MACnB,GAAGT,aAAAA,WAAW,mBAAmB;AAAA,MACjC,GAAG;AAAA,MACH,UAAU,YAAY,KAAK;AAAA,IAC/B;AACM,UAAA,EAAE,aAAa;AACf,UAAA,UAAU,uBAAuB,KAAK;AACtC,UAAA,cAAc,eAAe,OAAO,QAAQ;AAC9C,QAAA,CAAC,YAAY,WAAW;AACxB,oBAAc,gBAAgB,iBAAiB;AACzC,YAAA,mBAAmB,2BAA2B,cAAc;AAClE,MAAAS,iBAAgB,iBAAiB;AAOjC,cAAQ,gBAAgB,iBAAiB,WAAW,aAAa,gBAAgB,qBAAqB,iBAAiB,cAAc;AAAA,IAAA;AAMzI,WAAQC,kBAAAA,KAAK,cAAc,UAAU,EAAE,OAAO,SAAS,UAAU,CAACD,kBAAiB,QAAQ,gBAAiBE,sBAAIF,gBAAe,EAAE,eAAe,QAAQ,eAAe,GAAG,eAAe,CAAC,IAAK,MAAM,UAAU,WAAW,OAAO,aAAa,aAAa,QAAQ,eAAe,WAAW,GAAG,aAAa,UAAU,QAAQ,aAAa,CAAC,GAAG;AAAA,EAAA;AAEpV,kBAAgB,cAAc,UAAU,OAAO,cAAc,WACvD,YACA,UAAU,UAAU,eAAe,UAAU,QAAQ,EAAE,GAAG;AAC1D,QAAA,4BAA4BG,wBAAW,eAAe;AAC5D,4BAA0B,qBAAqB,IAAI;AAC5C,SAAA;AACX;AACA,SAAS,YAAY,EAAE,YAAY;AACzB,QAAA,gBAAgBZ,aAAAA,WAAW,kBAAkB,EAAE;AACrD,SAAO,iBAAiB,aAAa,SAC/B,gBAAgB,MAAM,WACtB;AACV;AACA,SAAS,cAAc,gBAAgB,mBAAmB;AAChD,QAAA,WAAWA,aAAAA,WAAW,WAAW,EAAE;AAKzC,MACI,qBACA,UAAU;AACV,UAAM,gBAAgB;AACtB,mBAAe,eACT,QAAQ,OAAO,aAAa,IAC5B,UAAU,OAAO,aAAa;AAAA,EAAA;AAE5C;AACA,SAAS,2BAA2B,OAAO;AACjC,QAAA,EAAE,MAAAQ,OAAM,QAAAD,QAAA,IAAW;AACrB,MAAA,CAACC,SAAQ,CAACD;AACV,WAAO,CAAC;AACZ,QAAM,WAAW,EAAE,GAAGC,OAAM,GAAGD,QAAO;AAC/B,SAAA;AAAA,IACH,eAAeC,OAAM,UAAU,KAAK,KAAKD,SAAQ,UAAU,KAAK,IAC1D,SAAS,gBACT;AAAA,IACN,gBAAgB,SAAS;AAAA,EAC7B;AACJ;AChGA,MAAM,kBAAkB,CAAE;AAC1B,SAAS,kBAAkB,YAAY;AACnC,aAAW,OAAO,YAAY;AAC1B,oBAAgB,GAAG,IAAI,WAAW,GAAG;AACrC,QAAI,kBAAkB,GAAG,GAAG;AACxB,sBAAgB,GAAG,EAAE,gBAAgB;AAAA,IACjD;AAAA,EACA;AACA;ACPA,SAAS,oBAAoB,KAAK,EAAE,QAAAA,SAAQ,SAAQ,GAAI;AACpD,SAAQ,eAAe,IAAI,GAAG,KAC1B,IAAI,WAAW,QAAQ,MACrBA,WAAU,aAAa,YACpB,CAAC,CAAC,gBAAgB,GAAG,KAAK,QAAQ;AAC/C;ACNA,MAAM,iBAAiB;AAAA,EACnB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,sBAAsB;AAC1B;AACA,MAAM,gBAAgB,mBAAmB;AAOzC,SAAS,eAAe,cAAc,WAAW,mBAAmB;AAEhE,MAAI,kBAAkB;AACtB,MAAI,qBAAqB;AAKzB,WAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACpC,UAAM,MAAM,mBAAmB,CAAC;AAChC,UAAM,QAAQ,aAAa,GAAG;AAC9B,QAAI,UAAU;AACV;AACJ,QAAI,iBAAiB;AACrB,QAAI,OAAO,UAAU,UAAU;AAC3B,uBAAiB,WAAW,IAAI,WAAW,OAAO,IAAI,IAAI;AAAA,IACtE,OACa;AACD,uBAAiB,WAAW,KAAK,MAAM;AAAA,IACnD;AACQ,QAAI,CAAC,kBAAkB,mBAAmB;AACtC,YAAM,cAAc,eAAe,OAAO,iBAAiB,GAAG,CAAC;AAC/D,UAAI,CAAC,gBAAgB;AACjB,6BAAqB;AACrB,cAAM,gBAAgB,eAAe,GAAG,KAAK;AAC7C,2BAAmB,GAAG,aAAa,IAAI,WAAW;AAAA,MAClE;AACY,UAAI,mBAAmB;AACnB,kBAAU,GAAG,IAAI;AAAA,MACjC;AAAA,IACA;AAAA,EACA;AACI,oBAAkB,gBAAgB,KAAM;AAGxC,MAAI,mBAAmB;AACnB,sBAAkB,kBAAkB,WAAW,qBAAqB,KAAK,eAAe;AAAA,EAChG,WACa,oBAAoB;AACzB,sBAAkB;AAAA,EAC1B;AACI,SAAO;AACX;ACtDA,SAAS,gBAAgB,OAAO,cAAc,mBAAmB;AAC7D,QAAM,EAAE,OAAO,MAAM,gBAAiB,IAAG;AAEzC,MAAIM,gBAAe;AACnB,MAAI,qBAAqB;AAOzB,aAAW,OAAO,cAAc;AAC5B,UAAM,QAAQ,aAAa,GAAG;AAC9B,QAAI,eAAe,IAAI,GAAG,GAAG;AAEzB,MAAAA,gBAAe;AACf;AAAA,IACZ,WACiB,kBAAkB,GAAG,GAAG;AAC7B,WAAK,GAAG,IAAI;AACZ;AAAA,IACZ,OACa;AAED,YAAM,cAAc,eAAe,OAAO,iBAAiB,GAAG,CAAC;AAC/D,UAAI,IAAI,WAAW,QAAQ,GAAG;AAE1B,6BAAqB;AACrB,wBAAgB,GAAG,IACf;AAAA,MACpB,OACiB;AACD,cAAM,GAAG,IAAI;AAAA,MAC7B;AAAA,IACA;AAAA,EACA;AACI,MAAI,CAAC,aAAa,WAAW;AACzB,QAAIA,iBAAgB,mBAAmB;AACnC,YAAM,YAAY,eAAe,cAAc,MAAM,WAAW,iBAAiB;AAAA,IAC7F,WACiB,MAAM,WAAW;AAKtB,YAAM,YAAY;AAAA,IAC9B;AAAA,EACA;AAKI,MAAI,oBAAoB;AACpB,UAAM,EAAE,UAAU,OAAO,UAAU,OAAO,UAAU,EAAC,IAAM;AAC3D,UAAM,kBAAkB,GAAG,OAAO,IAAI,OAAO,IAAI,OAAO;AAAA,EAChE;AACA;AC3DA,MAAM,wBAAwB,OAAO;AAAA,EACjC,OAAO,CAAE;AAAA,EACT,WAAW,CAAE;AAAA,EACb,iBAAiB,CAAE;AAAA,EACnB,MAAM,CAAE;AACZ;ACCA,SAAS,kBAAkB,QAAQ,QAAQ,OAAO;AAC9C,aAAW,OAAO,QAAQ;AACtB,QAAI,CAAC,cAAc,OAAO,GAAG,CAAC,KAAK,CAAC,oBAAoB,KAAK,KAAK,GAAG;AACjE,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IACpC;AAAA,EACA;AACA;AACA,SAAS,uBAAuB,EAAE,kBAAmB,GAAE,aAAa;AAChE,SAAOT,aAAO,QAAC,MAAM;AACjB,UAAM,QAAQ,sBAAuB;AACrC,oBAAgB,OAAO,aAAa,iBAAiB;AACrD,WAAO,OAAO,OAAO,CAAE,GAAE,MAAM,MAAM,MAAM,KAAK;AAAA,EACxD,GAAO,CAAC,WAAW,CAAC;AACpB;AACA,SAAS,SAAS,OAAO,aAAa;AAClC,QAAM,YAAY,MAAM,SAAS,CAAE;AACnC,QAAM,QAAQ,CAAE;AAIhB,oBAAkB,OAAO,WAAW,KAAK;AACzC,SAAO,OAAO,OAAO,uBAAuB,OAAO,WAAW,CAAC;AAC/D,SAAO;AACX;AACA,SAAS,aAAa,OAAO,aAAa;AAEtC,QAAM,YAAY,CAAE;AACpB,QAAM,QAAQ,SAAS,OAAO,WAAW;AACzC,MAAI,MAAM,QAAQ,MAAM,iBAAiB,OAAO;AAE5C,cAAU,YAAY;AAEtB,UAAM,aACF,MAAM,mBACF,MAAM,qBACF;AAEZ,UAAM,cACF,MAAM,SAAS,OACT,SACA,OAAO,MAAM,SAAS,MAAM,MAAM,GAAG;AAAA,EACvD;AACI,MAAI,MAAM,aAAa,WAClB,MAAM,SAAS,MAAM,cAAc,MAAM,WAAW;AACrD,cAAU,WAAW;AAAA,EAC7B;AACI,YAAU,QAAQ;AAClB,SAAO;AACX;ACpDA,MAAM,WAAW;AAAA,EACb,QAAQ;AAAA,EACR,OAAO;AACX;AACA,MAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,OAAO;AACX;AAQA,SAAS,aAAa,OAAO,QAAQ,UAAU,GAAG,SAAS,GAAG,cAAc,MAAM;AAE9E,QAAM,aAAa;AAGnB,QAAM,OAAO,cAAc,WAAW;AAEtC,QAAM,KAAK,MAAM,IAAI,GAAG,UAAU,CAAC,MAAM;AAEzC,QAAM,aAAa,GAAG,UAAU,MAAM;AACtC,QAAM,cAAc,GAAG,UAAU,OAAO;AACxC,QAAM,KAAK,KAAK,IAAI,GAAG,UAAU,IAAI,WAAW;AACpD;ACvBA,SAAS,cAAc,OAAO;AAAA,EAAE;AAAA,EAAO;AAAA,EAAO;AAAA,EAAW;AAAA,EAAY,cAAc;AAAA,EAAG,aAAa;AAAA;AAAA,EAEnG,GAAG;GAAUU,WAAU,mBAAmB,WAAW;AACjD,kBAAgB,OAAO,QAAQ,iBAAiB;AAKhD,MAAIA,WAAU;AACV,QAAI,MAAM,MAAM,SAAS;AACrB,YAAM,MAAM,UAAU,MAAM,MAAM;AAAA,IAC9C;AACQ;AAAA,EACR;AACI,QAAM,QAAQ,MAAM;AACpB,QAAM,QAAQ,CAAE;AAChB,QAAM,EAAE,OAAO,MAAK,IAAK;AAKzB,MAAI,MAAM,WAAW;AACjB,UAAM,YAAY,MAAM;AACxB,WAAO,MAAM;AAAA,EACrB;AACI,MAAI,MAAM,aAAa,MAAM,iBAAiB;AAC1C,UAAM,kBAAkB,MAAM,mBAAmB;AACjD,WAAO,MAAM;AAAA,EACrB;AACI,MAAI,MAAM,WAAW;AAKjB,UAAM,eAAe,WAAW,gBAAgB;AAChD,WAAO,MAAM;AAAA,EACrB;AAEI,MAAI,UAAU;AACV,UAAM,IAAI;AACd,MAAI,UAAU;AACV,UAAM,IAAI;AACd,MAAI,cAAc;AACd,UAAM,QAAQ;AAElB,MAAI,eAAe,QAAW;AAC1B,iBAAa,OAAO,YAAY,aAAa,YAAY,KAAK;AAAA,EACtE;AACA;ACpDA,MAAM,uBAAuB,OAAO;AAAA,EAChC,GAAG,sBAAuB;AAAA,EAC1B,OAAO,CAAE;AACb;ACLA,MAAM,WAAW,CAAC,QAAQ,OAAO,QAAQ,YAAY,IAAI,YAAW,MAAO;ACM3E,SAAS,YAAY,OAAO,aAAa,WAAW,WAAW;AAC3D,QAAM,cAAcV,aAAAA,QAAQ,MAAM;AAC9B,UAAM,QAAQ,qBAAsB;AACpC,kBAAc,OAAO,aAAa,SAAS,SAAS,GAAG,MAAM,mBAAmB,MAAM,KAAK;AAC3F,WAAO;AAAA,MACH,GAAG,MAAM;AAAA,MACT,OAAO,EAAE,GAAG,MAAM,MAAO;AAAA,IAC5B;AAAA,EACT,GAAO,CAAC,WAAW,CAAC;AAChB,MAAI,MAAM,OAAO;AACb,UAAM,YAAY,CAAE;AACpB,sBAAkB,WAAW,MAAM,OAAO,KAAK;AAC/C,gBAAY,QAAQ,EAAE,GAAG,WAAW,GAAG,YAAY,MAAO;AAAA,EAClE;AACI,SAAO;AACX;ACjBA,MAAM,uBAAuB;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AC5BA,SAAS,eAAe,WAAW;AAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,OAAO,cAAc;AAAA;AAAA;AAAA,IAIjB,UAAU,SAAS,GAAG;AAAA,IAAG;AACzB,WAAO;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,IAKI,qBAAqB,QAAQ,SAAS,IAAI;AAAA;AAAA;AAAA,IAItC,SAAS,KAAK,SAAS;AAAA,IAAG;AAC1B,WAAO;AAAA,EACf;AACI,SAAO;AACX;ACpBA,SAAS,gBAAgB,qBAAqB,OAAO;AACjD,QAAM,YAAY,CAAC,WAAW,OAAO,KAAK,EAAE,aAAc,GAAE,aAAa;AACrE,UAAM,iBAAiB,eAAe,SAAS,IACzC,cACA;AACN,UAAM,cAAc,eAAe,OAAO,cAAc,UAAU,SAAS;AAC3E,UAAM,gBAAgB,YAAY,OAAO,OAAO,cAAc,UAAU,kBAAkB;AAC1F,UAAM,eAAe,cAAcW,aAAAA,WAC7B,EAAE,GAAG,eAAe,GAAG,aAAa,IAAG,IACvC,CAAE;AAMR,UAAM,EAAE,SAAQ,IAAK;AACrB,UAAM,mBAAmBX,aAAAA,QAAQ,MAAO,cAAc,QAAQ,IAAI,SAAS,QAAQ,UAAW,CAAC,QAAQ,CAAC;AACxG,WAAOY,aAAAA,cAAc,WAAW;AAAA,MAC5B,GAAG;AAAA,MACH,UAAU;AAAA,IACtB,CAAS;AAAA,EACJ;AACD,SAAO;AACX;AC9BA,SAAS,cAAc,eAAe;AAClC,QAAM,QAAQ,CAAC,CAAE,GAAE,EAAE;AACrB,iBAAe,OAAO,QAAQ,CAAC,OAAO,QAAQ;AAC1C,UAAM,CAAC,EAAE,GAAG,IAAI,MAAM,IAAK;AAC3B,UAAM,CAAC,EAAE,GAAG,IAAI,MAAM,YAAa;AAAA,EAC3C,CAAK;AACD,SAAO;AACX;AACA,SAAS,wBAAwB,OAAO,YAAY,QAAQ,eAAe;AAIvE,MAAI,OAAO,eAAe,YAAY;AAClC,UAAM,CAAC,SAAS,QAAQ,IAAI,cAAc,aAAa;AACvD,iBAAa,WAAW,WAAW,SAAY,SAAS,MAAM,QAAQ,SAAS,QAAQ;AAAA,EAC/F;AAKI,MAAI,OAAO,eAAe,UAAU;AAChC,iBAAa,MAAM,YAAY,MAAM,SAAS,UAAU;AAAA,EAChE;AAMI,MAAI,OAAO,eAAe,YAAY;AAClC,UAAM,CAAC,SAAS,QAAQ,IAAI,cAAc,aAAa;AACvD,iBAAa,WAAW,WAAW,SAAY,SAAS,MAAM,QAAQ,SAAS,QAAQ;AAAA,EAC/F;AACI,SAAO;AACX;AC1BA,SAAS,mBAAmB,OAAO;AAC/B,SAAO,cAAc,KAAK,IAAI,MAAM,IAAK,IAAG;AAChD;ACAA,SAAS,UAAU,EAAE,6BAAAC,8BAA6B,kBAAoB,GAAE,OAAO,SAAS,iBAAiB;AACrG,QAAM,QAAQ;AAAA,IACV,cAAc,iBAAiB,OAAO,SAAS,iBAAiBA,4BAA2B;AAAA,IAC3F,aAAa,kBAAmB;AAAA,EACnC;AACD,SAAO;AACX;AACA,MAAM,qBAAqB,CAAC,WAAW,CAAC,OAAO,aAAa;AACxD,QAAM,UAAUjB,aAAU,WAAC,aAAa;AACxC,QAAM,kBAAkBA,aAAU,WAAC,eAAe;AAClD,QAAM,OAAO,MAAM,UAAU,QAAQ,OAAO,SAAS,eAAe;AACpE,SAAO,WAAW,SAAS,YAAY,IAAI;AAC/C;AACA,SAAS,iBAAiB,OAAO,SAAS,iBAAiB,oBAAoB;AAC3E,QAAM,SAAS,CAAE;AACjB,QAAM,eAAe,mBAAmB,OAAO,EAAE;AACjD,aAAW,OAAO,cAAc;AAC5B,WAAO,GAAG,IAAI,mBAAmB,aAAa,GAAG,CAAC;AAAA,EAC1D;AACI,MAAI,EAAE,SAAS,QAAO,IAAK;AAC3B,QAAM,0BAA0B,sBAAsB,KAAK;AAC3D,QAAM,kBAAkB,cAAc,KAAK;AAC3C,MAAI,WACA,mBACA,CAAC,2BACD,MAAM,YAAY,OAAO;AACzB,QAAI,YAAY;AACZ,gBAAU,QAAQ;AACtB,QAAI,YAAY;AACZ,gBAAU,QAAQ;AAAA,EAC9B;AACI,MAAI,4BAA4B,kBAC1B,gBAAgB,YAAY,QAC5B;AACN,8BAA4B,6BAA6B,YAAY;AACrE,QAAM,eAAe,4BAA4B,UAAU;AAC3D,MAAI,gBACA,OAAO,iBAAiB,aACxB,CAAC,oBAAoB,YAAY,GAAG;AACpC,UAAM,OAAO,MAAM,QAAQ,YAAY,IAAI,eAAe,CAAC,YAAY;AACvE,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,YAAM,WAAW,wBAAwB,OAAO,KAAK,CAAC,CAAC;AACvD,UAAI,UAAU;AACV,cAAM,EAAE,eAAe,YAAY,GAAG,OAAQ,IAAG;AACjD,mBAAW,OAAO,QAAQ;AACtB,cAAI,cAAc,OAAO,GAAG;AAC5B,cAAI,MAAM,QAAQ,WAAW,GAAG;AAK5B,kBAAM,QAAQ,4BACR,YAAY,SAAS,IACrB;AACN,0BAAc,YAAY,KAAK;AAAA,UACvD;AACoB,cAAI,gBAAgB,MAAM;AACtB,mBAAO,GAAG,IAAI;AAAA,UACtC;AAAA,QACA;AACgB,mBAAW,OAAO,eAAe;AAC7B,iBAAO,GAAG,IAAI,cAAc,GAAG;AAAA,QACnD;AAAA,MACA;AAAA,IACA;AAAA,EACA;AACI,SAAO;AACX;ACzEA,SAASiB,8BAA4B,OAAO,WAAW,eAAe;AAClE,QAAM,EAAE,MAAK,IAAK;AAClB,QAAM,YAAY,CAAE;AACpB,aAAW,OAAO,OAAO;AACrB,QAAI,cAAc,MAAM,GAAG,CAAC,KACvB,UAAU,SACP,cAAc,UAAU,MAAM,GAAG,CAAC,KACtC,oBAAoB,KAAK,KAAK,KAC9B,eAAe,SAAS,GAAG,GAAG,cAAc,QAAW;AACvD,gBAAU,GAAG,IAAI,MAAM,GAAG;AAAA,IACtC;AAAA,EACA;AACI,SAAO;AACX;ACZA,MAAM,mBAAmB;AAAA,EACrB,gBAAgB,mBAAmB;AAAA,IACvC,6BAAQA;AAAAA,IACA,mBAAmB;AAAA,EAC3B,CAAK;AACL;ACNA,SAAS,4BAA4B,OAAO,WAAW,eAAe;AAClE,QAAM,YAAY,8BAA8B,OAAO,WAAW,aAAa;AAC/E,aAAW,OAAO,OAAO;AACrB,QAAI,cAAc,MAAM,GAAG,CAAC,KACxB,cAAc,UAAU,GAAG,CAAC,GAAG;AAC/B,YAAM,YAAY,mBAAmB,QAAQ,GAAG,MAAM,KAChD,SAAS,IAAI,OAAO,CAAC,EAAE,gBAAgB,IAAI,UAAU,CAAC,IACtD;AACN,gBAAU,SAAS,IAAI,MAAM,GAAG;AAAA,IAC5C;AAAA,EACA;AACI,SAAO;AACX;ACXA,MAAM,kBAAkB;AAAA,EACpB,gBAAgB,mBAAmB;AAAA,IAC/B;AAAA,IACA,mBAAmB;AAAA,EAC3B,CAAK;AACL;ACHA,SAAS,6BAA6B,mBAAmB,qBAAqB;AAC1E,SAAO,SAASC,uBAAsB,WAAW,EAAE,mBAAkB,IAAK,EAAE,oBAAoB,SAAS;AACrG,UAAM,aAAa,eAAe,SAAS,IACrC,kBACA;AACN,UAAM,SAAS;AAAA,MACX,GAAG;AAAA,MACH;AAAA,MACA,WAAW,gBAAgB,kBAAkB;AAAA,MAC7C;AAAA,MACA;AAAA,IACH;AACD,WAAO,8BAA8B,MAAM;AAAA,EAC9C;AACL;AClBA,SAAS,eAAe,eAAe,YAAY,QAAQ;AACvD,QAAM,QAAQ,cAAc,SAAU;AACtC,SAAO,wBAAwB,OAAO,YAAY,WAAW,SAAY,SAAS,MAAM,QAAQ,aAAa;AACjH;ACLA,MAAM,oBAAoB,CAAC,MAAM;AAC7B,SAAO,MAAM,QAAQ,CAAC;AAC1B;ACMA,SAAS,eAAe,eAAe,KAAK,OAAO;AAC/C,MAAI,cAAc,SAAS,GAAG,GAAG;AAC7B,kBAAc,SAAS,GAAG,EAAE,IAAI,KAAK;AAAA,EAC7C,OACS;AACD,kBAAc,SAAS,KAAK,YAAY,KAAK,CAAC;AAAA,EACtD;AACA;AACA,SAAS,6BAA6B,GAAG;AAErC,SAAO,kBAAkB,CAAC,IAAI,EAAE,EAAE,SAAS,CAAC,KAAK,IAAI;AACzD;AACA,SAAS,UAAU,eAAe,YAAY;AAC1C,QAAM,WAAW,eAAe,eAAe,UAAU;AACzD,MAAI,EAAE,gBAAgB,CAAA,GAAI,aAAa,CAAE,GAAE,GAAG,OAAM,IAAK,YAAY,CAAE;AACvE,WAAS,EAAE,GAAG,QAAQ,GAAG,cAAe;AACxC,aAAW,OAAO,QAAQ;AACtB,UAAM,QAAQ,6BAA6B,OAAO,GAAG,CAAC;AACtD,mBAAe,eAAe,KAAK,KAAK;AAAA,EAChD;AACA;AC1BA,SAAS,wBAAwB,OAAO;AACpC,SAAO,QAAQ,cAAc,KAAK,KAAK,MAAM,GAAG;AACpD;ACDA,SAAS,qBAAqB,eAAe,KAAK;AAC9C,QAAM,aAAa,cAAc,SAAS,YAAY;AAKtD,MAAI,wBAAwB,UAAU,GAAG;AACrC,WAAO,WAAW,IAAI,GAAG;AAAA,EACjC,WACa,CAAC,cAAc,mBAAmB,YAAY;AACnD,UAAM,gBAAgB,IAAI,mBAAmB,WAAW,MAAM;AAC9D,kBAAc,SAAS,cAAc,aAAa;AAClD,kBAAc,IAAI,GAAG;AAAA,EAC7B;AACA;ACfA,SAAS,qBAAqB,eAAe;AACzC,SAAO,cAAc,MAAM,4BAA4B;AAC3D;ACJA,MAAM,YAAY,CAAC,UAAU,UAAU;AACvC,SAAS,iBAAiB,WAAW,EAAE,QAAQ,aAAa,OAAQ,GAAE,eAAe;AACjF,QAAM,oBAAoB,UAAU,OAAO,SAAS;AACpD,QAAM,QAAQ,UAAU,eAAe,UAAU,SAAS,MAAM,IAC1D,IACA,kBAAkB,SAAS;AACjC,SACM,kBAAkB,KAAK;AAEjC;ACPA,MAAM,oBAAoB;AAAA,EACtB,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AACf;AACA,MAAM,yBAAyB,CAAC,YAAY;AAAA,EACxC,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS,WAAW,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI;AAAA,EAC7C,WAAW;AACf;AACA,MAAM,sBAAsB;AAAA,EACxB,MAAM;AAAA,EACN,UAAU;AACd;AAKA,MAAM,OAAO;AAAA,EACT,MAAM;AAAA,EACN,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC;AAAA,EACzB,UAAU;AACd;AACA,MAAM,uBAAuB,CAAC,UAAU,EAAE,gBAAgB;AACtD,MAAI,UAAU,SAAS,GAAG;AACtB,WAAO;AAAA,EACf,WACa,eAAe,IAAI,QAAQ,GAAG;AACnC,WAAO,SAAS,WAAW,OAAO,IAC5B,uBAAuB,UAAU,CAAC,CAAC,IACnC;AAAA,EACd;AACI,SAAO;AACX;AChCA,SAAS,oBAAoB,EAAE,MAAM,OAAO,QAAQ,eAAe,iBAAiB,kBAAkB,QAAQ,YAAY,aAAa,MAAM,SAAS,GAAG,WAAU,GAAI;AACnK,SAAO,CAAC,CAAC,OAAO,KAAK,UAAU,EAAE;AACrC;ACDA,MAAM,qBAAqB,CAAC,MAAM,OAAO,QAAQ,aAAa,CAAA,GAAI,SAAS,cAAc,CAAC,eAAe;AACrG,QAAM,kBAAkB,mBAAmB,YAAY,IAAI,KAAK,CAAE;AAMlE,QAAMC,SAAQ,gBAAgB,SAAS,WAAW,SAAS;AAK3D,MAAI,EAAE,UAAU,EAAC,IAAK;AACtB,YAAU,UAAU,sBAAsBA,MAAK;AAC/C,QAAM,UAAU;AAAA,IACZ,WAAW,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM,MAAM;AAAA,IACzD,MAAM;AAAA,IACN,UAAU,MAAM,YAAa;AAAA,IAC7B,GAAG;AAAA,IACH,OAAO,CAAC;AAAA,IACR,UAAU,CAAC,MAAM;AACb,YAAM,IAAI,CAAC;AACX,sBAAgB,YAAY,gBAAgB,SAAS,CAAC;AAAA,IACzD;AAAA,IACD,YAAY,MAAM;AACd,iBAAY;AACZ,sBAAgB,cAAc,gBAAgB,WAAY;AAAA,IAC7D;AAAA,IACD;AAAA,IACA,aAAa;AAAA,IACb,SAAS,YAAY,SAAY;AAAA,EACpC;AAKD,MAAI,CAAC,oBAAoB,eAAe,GAAG;AACvC,WAAO,OAAO,SAAS,qBAAqB,MAAM,OAAO,CAAC;AAAA,EAClE;AAMI,UAAQ,aAAa,QAAQ,WAAW,sBAAsB,QAAQ,QAAQ;AAC9E,UAAQ,gBAAgB,QAAQ,cAAc,sBAAsB,QAAQ,WAAW;AAIvF,MAAI,QAAQ,SAAS,QAAW;AAC5B,YAAQ,UAAU,CAAC,IAAI,QAAQ;AAAA,EACvC;AACI,MAAI,aAAa;AACjB,MAAI,QAAQ,SAAS,SAChB,QAAQ,aAAa,KAAK,CAAC,QAAQ,aAAc;AAClD,YAAQ,WAAW;AACnB,QAAI,QAAQ,UAAU,GAAG;AACrB,mBAAa;AAAA,IACzB;AAAA,EACA;AACI,MAAI,mBAAmB,qBACnB,mBAAmB,gBAAgB;AACnC,iBAAa;AACb,YAAQ,WAAW;AACnB,YAAQ,QAAQ;AAAA,EACxB;AAKI,UAAQ,eAAe,CAAC,gBAAgB,QAAQ,CAAC,gBAAgB;AAMjE,MAAI,cAAc,CAAC,aAAa,MAAM,IAAG,MAAO,QAAW;AACvD,UAAM,gBAAgB,iBAAiB,QAAQ,WAAW,eAAe;AACzE,QAAI,kBAAkB,QAAW;AAC7B,YAAM,OAAO,MAAM;AACf,gBAAQ,SAAS,aAAa;AAC9B,gBAAQ,WAAY;AAAA,MACpC,CAAa;AACD;AAAA,IACZ;AAAA,EACA;AACI,SAAO,gBAAgB,SACjB,IAAI,YAAY,OAAO,IACvB,IAAI,0BAA0B,OAAO;AAC/C;ACnFA,SAAS,qBAAqB,EAAE,eAAe,eAAc,GAAI,KAAK;AAClE,QAAM,cAAc,cAAc,eAAe,GAAG,KAAK,eAAe,GAAG,MAAM;AACjF,iBAAe,GAAG,IAAI;AACtB,SAAO;AACX;AACA,SAAS,cAAc,eAAe,qBAAqB,EAAE,OAAAA,SAAQ,GAAG,oBAAoB,KAAM,IAAG,IAAI;AACrG,MAAI,EAAE,aAAa,cAAc,qBAAsB,GAAE,eAAe,GAAG,OAAM,IAAK;AACtF,MAAI;AACA,iBAAa;AACjB,QAAMC,cAAa,CAAE;AACrB,QAAM,qBAAqB,QACvB,cAAc,kBACd,cAAc,eAAe,SAAU,EAAC,IAAI;AAChD,aAAW,OAAO,QAAQ;AACtB,UAAM,QAAQ,cAAc,SAAS,KAAK,cAAc,aAAa,GAAG,KAAK,IAAI;AACjF,UAAM,cAAc,OAAO,GAAG;AAC9B,QAAI,gBAAgB,UACf,sBACG,qBAAqB,oBAAoB,GAAG,GAAI;AACpD;AAAA,IACZ;AACQ,UAAM,kBAAkB;AAAA,MACpB,OAAAD;AAAA,MACA,GAAG,mBAAmB,cAAc,CAAE,GAAE,GAAG;AAAA,IAC9C;AAID,UAAM,eAAe,MAAM,IAAK;AAChC,QAAI,iBAAiB,UACjB,CAAC,MAAM,eACP,CAAC,MAAM,QAAQ,WAAW,KAC1B,gBAAgB,gBAChB,CAAC,gBAAgB,UAAU;AAC3B;AAAA,IACZ;AAKQ,QAAI,YAAY;AAChB,QAAI,OAAO,wBAAwB;AAC/B,YAAM,WAAW,qBAAqB,aAAa;AACnD,UAAI,UAAU;AACV,cAAM,YAAY,OAAO,uBAAuB,UAAU,KAAK,KAAK;AACpE,YAAI,cAAc,MAAM;AACpB,0BAAgB,YAAY;AAC5B,sBAAY;AAAA,QAChC;AAAA,MACA;AAAA,IACA;AACQ,yBAAqB,eAAe,GAAG;AACvC,UAAM,MAAM,mBAAmB,KAAK,OAAO,aAAa,cAAc,sBAAsB,eAAe,IAAI,GAAG,IAC5G,EAAE,MAAM,MAAK,IACb,iBAAiB,eAAe,SAAS,CAAC;AAChD,UAAM,YAAY,MAAM;AACxB,QAAI,WAAW;AACX,MAAAC,YAAW,KAAK,SAAS;AAAA,IACrC;AAAA,EACA;AACI,MAAI,eAAe;AACf,YAAQ,IAAIA,WAAU,EAAE,KAAK,MAAM;AAC/B,YAAM,OAAO,MAAM;AACf,yBAAiB,UAAU,eAAe,aAAa;AAAA,MACvE,CAAa;AAAA,IACb,CAAS;AAAA,EACT;AACI,SAAOA;AACX;AC7EA,SAAS,eAAe,eAAe,SAAS,UAAU,CAAA,GAAI;AAC1D,QAAM,WAAW,eAAe,eAAe,SAAS,QAAQ,SAAS,SACnE,cAAc,iBAAiB,SAC/B,MAAS;AACf,MAAI,EAAE,aAAa,cAAc,qBAAsB,KAAI,CAAE,EAAA,IAAK,YAAY,CAAE;AAChF,MAAI,QAAQ,oBAAoB;AAC5B,iBAAa,QAAQ;AAAA,EAC7B;AAKI,QAAM,eAAe,WACf,MAAM,QAAQ,IAAI,cAAc,eAAe,UAAU,OAAO,CAAC,IACjE,MAAM,QAAQ,QAAS;AAK7B,QAAM,qBAAqB,cAAc,mBAAmB,cAAc,gBAAgB,OACpF,CAAC,eAAe,MAAM;AACpB,UAAM,EAAE,gBAAgB,GAAG,iBAAiB,iBAAmB,IAAG;AAClE,WAAO,gBAAgB,eAAe,SAAS,gBAAgB,cAAc,iBAAiB,kBAAkB,OAAO;AAAA,EACnI,IACU,MAAM,QAAQ,QAAS;AAK7B,QAAM,EAAE,KAAI,IAAK;AACjB,MAAI,MAAM;AACN,UAAM,CAAC,OAAO,IAAI,IAAI,SAAS,mBACzB,CAAC,cAAc,kBAAkB,IACjC,CAAC,oBAAoB,YAAY;AACvC,WAAO,MAAO,EAAC,KAAK,MAAM,KAAI,CAAE;AAAA,EACxC,OACS;AACD,WAAO,QAAQ,IAAI,CAAC,aAAY,GAAI,mBAAmB,QAAQ,KAAK,CAAC,CAAC;AAAA,EAC9E;AACA;AACA,SAAS,gBAAgB,eAAe,SAAS,gBAAgB,GAAG,kBAAkB,GAAG,mBAAmB,GAAG,SAAS;AACpH,QAAMA,cAAa,CAAE;AACrB,QAAM,sBAAsB,cAAc,gBAAgB,OAAO,KAAK;AACtE,QAAM,0BAA0B,qBAAqB,IAC/C,CAAC,IAAI,MAAM,IAAI,kBACf,CAAC,IAAI,MAAM,qBAAqB,IAAI;AAC1C,QAAM,KAAK,cAAc,eAAe,EACnC,KAAK,eAAe,EACpB,QAAQ,CAAC,OAAO,MAAM;AACvB,UAAM,OAAO,kBAAkB,OAAO;AACtC,IAAAA,YAAW,KAAK,eAAe,OAAO,SAAS;AAAA,MAC3C,GAAG;AAAA,MACH,OAAO,gBAAgB,wBAAwB,CAAC;AAAA,IAC5D,CAAS,EAAE,KAAK,MAAM,MAAM,OAAO,qBAAqB,OAAO,CAAC,CAAC;AAAA,EACjE,CAAK;AACD,SAAO,QAAQ,IAAIA,WAAU;AACjC;AACA,SAAS,gBAAgB,GAAG,GAAG;AAC3B,SAAO,EAAE,iBAAiB,CAAC;AAC/B;AC1DA,SAAS,qBAAqB,eAAe,YAAY,UAAU,CAAA,GAAI;AACnE,gBAAc,OAAO,kBAAkB,UAAU;AACjD,MAAI;AACJ,MAAI,MAAM,QAAQ,UAAU,GAAG;AAC3B,UAAMA,cAAa,WAAW,IAAI,CAAC,YAAY,eAAe,eAAe,SAAS,OAAO,CAAC;AAC9F,gBAAY,QAAQ,IAAIA,WAAU;AAAA,EAC1C,WACa,OAAO,eAAe,UAAU;AACrC,gBAAY,eAAe,eAAe,YAAY,OAAO;AAAA,EACrE,OACS;AACD,UAAM,qBAAqB,OAAO,eAAe,aAC3C,eAAe,eAAe,YAAY,QAAQ,MAAM,IACxD;AACN,gBAAY,QAAQ,IAAI,cAAc,eAAe,oBAAoB,OAAO,CAAC;AAAA,EACzF;AACI,SAAO,UAAU,KAAK,MAAM;AACxB,kBAAc,OAAO,qBAAqB,UAAU;AAAA,EAC5D,CAAK;AACL;ACvBA,SAAS,eAAe,MAAM,MAAM;AAChC,MAAI,CAAC,MAAM,QAAQ,IAAI;AACnB,WAAO;AACX,QAAM,aAAa,KAAK;AACxB,MAAI,eAAe,KAAK;AACpB,WAAO;AACX,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACjC,QAAI,KAAK,CAAC,MAAM,KAAK,CAAC;AAClB,aAAO;AAAA,EACnB;AACI,SAAO;AACX;ACRA,MAAM,kBAAkB,aAAa;AACrC,SAAS,kBAAkB,eAAe;AACtC,MAAI,CAAC;AACD,WAAO;AACX,MAAI,CAAC,cAAc,uBAAuB;AACtC,UAAMC,WAAU,cAAc,SACxB,kBAAkB,cAAc,MAAM,KAAK,CAAA,IAC3C,CAAE;AACR,QAAI,cAAc,MAAM,YAAY,QAAW;AAC3C,MAAAA,SAAQ,UAAU,cAAc,MAAM;AAAA,IAClD;AACQ,WAAOA;AAAA,EACf;AACI,QAAM,UAAU,CAAE;AAClB,WAAS,IAAI,GAAG,IAAI,iBAAiB,KAAK;AACtC,UAAM,OAAO,aAAa,CAAC;AAC3B,UAAM,OAAO,cAAc,MAAM,IAAI;AACrC,QAAI,eAAe,IAAI,KAAK,SAAS,OAAO;AACxC,cAAQ,IAAI,IAAI;AAAA,IAC5B;AAAA,EACA;AACI,SAAO;AACX;AChBA,MAAM,uBAAuB,CAAC,GAAG,oBAAoB,EAAE,QAAS;AAChE,MAAM,oBAAoB,qBAAqB;AAC/C,SAAS,YAAY,eAAe;AAChC,SAAO,CAACD,gBAAe,QAAQ,IAAIA,YAAW,IAAI,CAAC,EAAE,WAAW,QAAO,MAAO,qBAAqB,eAAe,WAAW,OAAO,CAAC,CAAC;AAC1I;AACA,SAAS,qBAAqB,eAAe;AACzC,MAAI,UAAU,YAAY,aAAa;AACvC,MAAI,QAAQ,YAAa;AACzB,MAAI,kBAAkB;AAKtB,QAAM,0BAA0B,CAAC,SAAS,CAAC,KAAK,eAAe;AAC3D,UAAM,WAAW,eAAe,eAAe,YAAY,SAAS,SAC9D,cAAc,iBAAiB,SAC/B,MAAS;AACf,QAAI,UAAU;AACV,YAAM,EAAE,YAAY,eAAe,GAAG,OAAQ,IAAG;AACjD,YAAM,EAAE,GAAG,KAAK,GAAG,QAAQ,GAAG,cAAe;AAAA,IACzD;AACQ,WAAO;AAAA,EACV;AAKD,WAAS,mBAAmB,cAAc;AACtC,cAAU,aAAa,aAAa;AAAA,EAC5C;AAWI,WAAS,eAAe,mBAAmB;AACvC,UAAM,EAAE,MAAK,IAAK;AAClB,UAAM,UAAU,kBAAkB,cAAc,MAAM,KAAK,CAAE;AAK7D,UAAMA,cAAa,CAAE;AAKrB,UAAM,cAAc,oBAAI,IAAK;AAM7B,QAAI,kBAAkB,CAAE;AAKxB,QAAI,sBAAsB;AAO1B,aAAS,IAAI,GAAG,IAAI,mBAAmB,KAAK;AACxC,YAAM,OAAO,qBAAqB,CAAC;AACnC,YAAM,YAAY,MAAM,IAAI;AAC5B,YAAM,OAAO,MAAM,IAAI,MAAM,SACvB,MAAM,IAAI,IACV,QAAQ,IAAI;AAClB,YAAM,gBAAgB,eAAe,IAAI;AAKzC,YAAM,cAAc,SAAS,oBAAoB,UAAU,WAAW;AACtE,UAAI,gBAAgB;AAChB,8BAAsB;AAO1B,UAAI,cAAc,SAAS,QAAQ,IAAI,KACnC,SAAS,MAAM,IAAI,KACnB;AAIJ,UAAI,eACA,mBACA,cAAc,wBAAwB;AACtC,sBAAc;AAAA,MAC9B;AAKY,gBAAU,gBAAgB,EAAE,GAAG,gBAAiB;AAEhD;AAAA;AAAA,QAEC,CAAC,UAAU,YAAY,gBAAgB;AAAA,QAEnC,CAAC,QAAQ,CAAC,UAAU;AAAA,QAErB,oBAAoB,IAAI,KACxB,OAAO,SAAS;AAAA,QAAW;AAC3B;AAAA,MAChB;AAMY,YAAM,mBAAmB,uBAAuB,UAAU,UAAU,IAAI;AACxE,UAAI,oBAAoB;AAAA,MAEnB,SAAS,qBACN,UAAU,YACV,CAAC,eACD;AAAA,MAEH,IAAI,uBAAuB;AAChC,UAAI,uBAAuB;AAK3B,YAAM,iBAAiB,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AAKzD,UAAI,iBAAiB,eAAe,OAAO,wBAAwB,IAAI,GAAG,EAAE;AAC5E,UAAI,gBAAgB;AAChB,yBAAiB,CAAE;AAUvB,YAAM,EAAE,qBAAqB,CAAE,EAAA,IAAK;AACpC,YAAM,UAAU;AAAA,QACZ,GAAG;AAAA,QACH,GAAG;AAAA,MACN;AACD,YAAM,gBAAgB,CAAC,QAAQ;AAC3B,4BAAoB;AACpB,YAAI,YAAY,IAAI,GAAG,GAAG;AACtB,iCAAuB;AACvB,sBAAY,OAAO,GAAG;AAAA,QAC1C;AACgB,kBAAU,eAAe,GAAG,IAAI;AAChC,cAAME,eAAc,cAAc,SAAS,GAAG;AAC9C,YAAIA;AACA,UAAAA,aAAY,YAAY;AAAA,MAC/B;AACD,iBAAW,OAAO,SAAS;AACvB,cAAM,OAAO,eAAe,GAAG;AAC/B,cAAM,OAAO,mBAAmB,GAAG;AAEnC,YAAI,gBAAgB,eAAe,GAAG;AAClC;AAIJ,YAAI,kBAAkB;AACtB,YAAI,kBAAkB,IAAI,KAAK,kBAAkB,IAAI,GAAG;AACpD,4BAAkB,CAAC,eAAe,MAAM,IAAI;AAAA,QAChE,OACqB;AACD,4BAAkB,SAAS;AAAA,QAC/C;AACgB,YAAI,iBAAiB;AACjB,cAAI,SAAS,UAAa,SAAS,MAAM;AAErC,0BAAc,GAAG;AAAA,UACzC,OACyB;AAED,wBAAY,IAAI,GAAG;AAAA,UAC3C;AAAA,QACA,WACyB,SAAS,UAAa,YAAY,IAAI,GAAG,GAAG;AAKjD,wBAAc,GAAG;AAAA,QACrC,OACqB;AAKD,oBAAU,cAAc,GAAG,IAAI;AAAA,QACnD;AAAA,MACA;AAKY,gBAAU,WAAW;AACrB,gBAAU,qBAAqB;AAI/B,UAAI,UAAU,UAAU;AACpB,0BAAkB,EAAE,GAAG,iBAAiB,GAAG,eAAgB;AAAA,MAC3E;AACY,UAAI,mBAAmB,cAAc,uBAAuB;AACxD,4BAAoB;AAAA,MACpC;AAKY,YAAM,uBAAuB,eAAe;AAC5C,YAAM,iBAAiB,CAAC,wBAAwB;AAChD,UAAI,qBAAqB,gBAAgB;AACrC,QAAAF,YAAW,KAAK,GAAG,eAAe,IAAI,CAAC,eAAe;AAAA,UAClD;AAAA,UACA,SAAS,EAAE,KAAM;AAAA,QACpB,EAAC,CAAC;AAAA,MACnB;AAAA,IACA;AAMQ,QAAI,YAAY,MAAM;AAClB,YAAM,oBAAoB,CAAE;AAK5B,UAAI,OAAO,MAAM,YAAY,WAAW;AACpC,cAAM,oBAAoB,eAAe,eAAe,MAAM,QAAQ,MAAM,OAAO,IAC7E,MAAM,QAAQ,CAAC,IACf,MAAM,OAAO;AACnB,YAAI,qBAAqB,kBAAkB,YAAY;AACnD,4BAAkB,aAAa,kBAAkB;AAAA,QACrE;AAAA,MACA;AACY,kBAAY,QAAQ,CAAC,QAAQ;AACzB,cAAM,iBAAiB,cAAc,cAAc,GAAG;AACtD,cAAME,eAAc,cAAc,SAAS,GAAG;AAC9C,YAAIA;AACA,UAAAA,aAAY,YAAY;AAE5B,0BAAkB,GAAG,IAAI,kBAAkB;AAAA,MAC3D,CAAa;AACD,MAAAF,YAAW,KAAK,EAAE,WAAW,kBAAiB,CAAE;AAAA,IAC5D;AACQ,QAAI,gBAAgB,QAAQA,YAAW,MAAM;AAC7C,QAAI,oBACC,MAAM,YAAY,SAAS,MAAM,YAAY,MAAM,YACpD,CAAC,cAAc,wBAAwB;AACvC,sBAAgB;AAAA,IAC5B;AACQ,sBAAkB;AAClB,WAAO,gBAAgB,QAAQA,WAAU,IAAI,QAAQ,QAAS;AAAA,EACtE;AAII,WAAS,UAAU,MAAM,UAAU;AAE/B,QAAI,MAAM,IAAI,EAAE,aAAa;AACzB,aAAO,QAAQ,QAAS;AAE5B,kBAAc,iBAAiB,QAAQ,CAAC,UAAU,MAAM,gBAAgB,UAAU,MAAM,QAAQ,CAAC;AACjG,UAAM,IAAI,EAAE,WAAW;AACvB,UAAMA,cAAa,eAAe,IAAI;AACtC,eAAW,OAAO,OAAO;AACrB,YAAM,GAAG,EAAE,gBAAgB,CAAE;AAAA,IACzC;AACQ,WAAOA;AAAA,EACf;AACI,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,MAAM;AAAA,IAChB,OAAO,MAAM;AACT,cAAQ,YAAa;AACrB,wBAAkB;AAAA,IACrB;AAAA,EACJ;AACL;AACA,SAAS,uBAAuB,MAAM,MAAM;AACxC,MAAI,OAAO,SAAS,UAAU;AAC1B,WAAO,SAAS;AAAA,EACxB,WACa,MAAM,QAAQ,IAAI,GAAG;AAC1B,WAAO,CAAC,eAAe,MAAM,IAAI;AAAA,EACzC;AACI,SAAO;AACX;AACA,SAAS,gBAAgB,WAAW,OAAO;AACvC,SAAO;AAAA,IACH;AAAA,IACA,eAAe,CAAE;AAAA,IACjB,gBAAgB,CAAE;AAAA,IAClB,oBAAoB,CAAE;AAAA,EACzB;AACL;AACA,SAAS,cAAc;AACnB,SAAO;AAAA,IACH,SAAS,gBAAgB,IAAI;AAAA,IAC7B,aAAa,gBAAiB;AAAA,IAC9B,YAAY,gBAAiB;AAAA,IAC7B,UAAU,gBAAiB;AAAA,IAC3B,WAAW,gBAAiB;AAAA,IAC5B,YAAY,gBAAiB;AAAA,IAC7B,MAAM,gBAAiB;AAAA,EAC1B;AACL;ACnVA,MAAM,QAAQ;AAAA,EACV,YAAY,MAAM;AACd,SAAK,YAAY;AACjB,SAAK,OAAO;AAAA,EACpB;AAAA,EACI,SAAS;AAAA,EAAA;AACb;ACFA,MAAM,yBAAyB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnC,YAAY,MAAM;AACd,UAAM,IAAI;AACV,SAAK,mBAAmB,KAAK,iBAAiB,qBAAqB,IAAI;AAAA,EAC/E;AAAA,EACI,sCAAsC;AAClC,UAAM,EAAE,QAAS,IAAG,KAAK,KAAK,SAAU;AACxC,QAAI,oBAAoB,OAAO,GAAG;AAC9B,WAAK,kBAAkB,QAAQ,UAAU,KAAK,IAAI;AAAA,IAC9D;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAII,QAAQ;AACJ,SAAK,oCAAqC;AAAA,EAClD;AAAA,EACI,SAAS;AACL,UAAM,EAAE,QAAS,IAAG,KAAK,KAAK,SAAU;AACxC,UAAM,EAAE,SAAS,YAAW,IAAK,KAAK,KAAK,aAAa,CAAE;AAC1D,QAAI,YAAY,aAAa;AACzB,WAAK,oCAAqC;AAAA,IACtD;AAAA,EACA;AAAA,EACI,UAAU;AACN,SAAK,KAAK,eAAe,MAAO;AAChC,SAAK,kBAAmB;AAAA,EAChC;AACA;ACnCA,IAAInB,OAAK;AACT,MAAM,6BAA6B,QAAQ;AAAA,EACvC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,KAAKA;AAAAA,EAClB;AAAA,EACI,SAAS;AACL,QAAI,CAAC,KAAK,KAAK;AACX;AACJ,UAAM,EAAE,WAAW,eAAgB,IAAG,KAAK,KAAK;AAChD,UAAM,EAAE,WAAW,cAAa,IAAK,KAAK,KAAK,uBAAuB,CAAE;AACxE,QAAI,CAAC,KAAK,KAAK,kBAAkB,cAAc,eAAe;AAC1D;AAAA,IACZ;AACQ,UAAM,gBAAgB,KAAK,KAAK,eAAe,UAAU,QAAQ,CAAC,SAAS;AAC3E,QAAI,kBAAkB,CAAC,WAAW;AAC9B,oBAAc,KAAK,MAAM;AACrB,uBAAe,KAAK,EAAE;AAAA,MACtC,CAAa;AAAA,IACb;AAAA,EACA;AAAA,EACI,QAAQ;AACJ,UAAM,EAAE,UAAU,eAAc,IAAK,KAAK,KAAK,mBAAmB,CAAE;AACpE,QAAI,gBAAgB;AAChB,qBAAe,KAAK,EAAE;AAAA,IAClC;AACQ,QAAI,UAAU;AACV,WAAK,UAAU,SAAS,KAAK,EAAE;AAAA,IAC3C;AAAA,EACA;AAAA,EACI,UAAU;AAAA,EAAA;AACd;AC9BA,MAAM,aAAa;AAAA,EACf,WAAW;AAAA,IACP,SAAS;AAAA,EACZ;AAAA,EACD,MAAM;AAAA,IACF,SAAS;AAAA,EACZ;AACL;ACVA,SAAS,YAAY,QAAQ,WAAW,SAAS,UAAU,EAAE,SAAS,QAAQ;AAC1E,SAAO,iBAAiB,WAAW,SAAS,OAAO;AACnD,SAAO,MAAM,OAAO,oBAAoB,WAAW,OAAO;AAC9D;ACDA,SAAS,iBAAiB,OAAO;AAC7B,SAAO;AAAA,IACH,OAAO;AAAA,MACH,GAAG,MAAM;AAAA,MACT,GAAG,MAAM;AAAA,IACZ;AAAA,EACJ;AACL;AACA,MAAM,iBAAiB,CAAC,YAAY;AAChC,SAAO,CAAC,UAAU,iBAAiB,KAAK,KAAK,QAAQ,OAAO,iBAAiB,KAAK,CAAC;AACvF;ACTA,SAAS,gBAAgB,QAAQ,WAAW,SAAS,SAAS;AAC1D,SAAO,YAAY,QAAQ,WAAW,eAAe,OAAO,GAAG,OAAO;AAC1E;ACAA,SAAS,wBAAwB,EAAE,KAAK,MAAM,OAAO,OAAM,GAAK;AAC5D,SAAO;AAAA,IACH,GAAG,EAAE,KAAK,MAAM,KAAK,MAAO;AAAA,IAC5B,GAAG,EAAE,KAAK,KAAK,KAAK,OAAQ;AAAA,EAC/B;AACL;AACA,SAAS,wBAAwB,EAAE,GAAG,KAAK;AACvC,SAAO,EAAE,KAAK,EAAE,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,KAAK,MAAM,EAAE,IAAK;AACnE;AAMA,SAAS,mBAAmB,OAAOsB,iBAAgB;AAC/C,MAAI,CAACA;AACD,WAAO;AACX,QAAM,UAAUA,gBAAe,EAAE,GAAG,MAAM,MAAM,GAAG,MAAM,KAAK;AAC9D,QAAM,cAAcA,gBAAe,EAAE,GAAG,MAAM,OAAO,GAAG,MAAM,QAAQ;AACtE,SAAO;AAAA,IACH,KAAK,QAAQ;AAAA,IACb,MAAM,QAAQ;AAAA,IACd,QAAQ,YAAY;AAAA,IACpB,OAAO,YAAY;AAAA,EACtB;AACL;AC5BA,MAAM,kBAAkB;AACxB,MAAM,YAAY,IAAI;AACtB,MAAM,YAAY,IAAI;AACtB,MAAM,sBAAsB;AAC5B,MAAM,gBAAgB,IAAI;AAC1B,MAAM,gBAAgB,IAAI;AAC1B,SAAS,WAAW,MAAM;AACtB,SAAO,KAAK,MAAM,KAAK;AAC3B;AACA,SAAS,OAAO,OAAO,QAAQ,aAAa;AACxC,SAAO,KAAK,IAAI,QAAQ,MAAM,KAAK;AACvC;AACA,SAAS,cAAc,OAAO,QAAQ,QAAQ,SAAS,KAAK;AACxD,QAAM,SAAS;AACf,QAAM,cAAc,UAAU,OAAO,KAAK,OAAO,KAAK,MAAM,MAAM;AAClE,QAAM,QAAQ,WAAW,MAAM,IAAI,WAAW,MAAM;AACpD,QAAM,YACF,UAAU,OAAO,KAAK,OAAO,KAAK,MAAM,MAAM,IAAI,MAAM;AAC5D,MAAK,MAAM,SAAS,aAAa,MAAM,SAAS,aAC5C,MAAM,MAAM,KAAK,GAAG;AACpB,UAAM,QAAQ;AAAA,EACtB;AACI,MAAK,MAAM,aAAa,iBACpB,MAAM,aAAa,iBACnB,MAAM,MAAM,SAAS,GAAG;AACxB,UAAM,YAAY;AAAA,EAC1B;AACA;AACA,SAAS,aAAa,OAAO,QAAQ,QAAQ,QAAQ;AACjD,gBAAc,MAAM,GAAG,OAAO,GAAG,OAAO,GAAG,SAAS,OAAO,UAAU,MAAS;AAC9E,gBAAc,MAAM,GAAG,OAAO,GAAG,OAAO,GAAG,SAAS,OAAO,UAAU,MAAS;AAClF;AACA,SAAS,iBAAiB,QAAQ,UAAU,QAAQ;AAChD,SAAO,MAAM,OAAO,MAAM,SAAS;AACnC,SAAO,MAAM,OAAO,MAAM,WAAW,QAAQ;AACjD;AACA,SAAS,gBAAgB,QAAQ,UAAU,QAAQ;AAC/C,mBAAiB,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;AAC/C,mBAAiB,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;AACnD;AACA,SAAS,yBAAyB,QAAQhB,SAAQ,QAAQ;AACtD,SAAO,MAAMA,QAAO,MAAM,OAAO;AACjC,SAAO,MAAM,OAAO,MAAM,WAAWA,OAAM;AAC/C;AACA,SAAS,qBAAqB,QAAQA,SAAQ,QAAQ;AAClD,2BAAyB,OAAO,GAAGA,QAAO,GAAG,OAAO,CAAC;AACrD,2BAAyB,OAAO,GAAGA,QAAO,GAAG,OAAO,CAAC;AACzD;ACjDA,MAAM,kBAAkB,OAAO;AAAA,EAC3B,WAAW;AAAA,EACX,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,aAAa;AACjB;AACA,MAAM,cAAc,OAAO;AAAA,EACvB,GAAG,gBAAiB;AAAA,EACpB,GAAG,gBAAiB;AACxB;AACA,MAAM,aAAa,OAAO,EAAE,KAAK,GAAG,KAAK,EAAC;AAC1C,MAAM,YAAY,OAAO;AAAA,EACrB,GAAG,WAAY;AAAA,EACf,GAAG,WAAY;AACnB;ACdA,SAAS,SAAS,UAAU;AACxB,SAAO,CAAC,SAAS,GAAG,GAAG,SAAS,GAAG,CAAC;AACxC;ACFA,SAAS,gBAAgB,OAAO;AAC5B,SAAO,UAAU,UAAa,UAAU;AAC5C;AACA,SAAS,SAAS,EAAE,OAAO,QAAQ,OAAM,GAAI;AACzC,SAAQ,CAAC,gBAAgB,KAAK,KAC1B,CAAC,gBAAgB,MAAM,KACvB,CAAC,gBAAgB,MAAM;AAC/B;AACA,SAAS,aAAa,QAAQ;AAC1B,SAAQ,SAAS,MAAM,KACnB,eAAe,MAAM,KACrB,OAAO,KACP,OAAO,UACP,OAAO,WACP,OAAO,WACP,OAAO,SACP,OAAO;AACf;AACA,SAAS,eAAe,QAAQ;AAC5B,SAAO,cAAc,OAAO,CAAC,KAAK,cAAc,OAAO,CAAC;AAC5D;AACA,SAAS,cAAc,OAAO;AAC1B,SAAO,SAAS,UAAU;AAC9B;ACjBA,SAAS,WAAW,OAAO,OAAO,aAAa;AAC3C,QAAM,qBAAqB,QAAQ;AACnC,QAAM,SAAS,QAAQ;AACvB,SAAO,cAAc;AACzB;AAIA,SAAS,gBAAgB,OAAO,WAAW,OAAO,aAAa,UAAU;AACrE,MAAI,aAAa,QAAW;AACxB,YAAQ,WAAW,OAAO,UAAU,WAAW;AAAA,EACvD;AACI,SAAO,WAAW,OAAO,OAAO,WAAW,IAAI;AACnD;AAIA,SAAS,eAAe,MAAM,YAAY,GAAG,QAAQ,GAAG,aAAa,UAAU;AAC3E,OAAK,MAAM,gBAAgB,KAAK,KAAK,WAAW,OAAO,aAAa,QAAQ;AAC5E,OAAK,MAAM,gBAAgB,KAAK,KAAK,WAAW,OAAO,aAAa,QAAQ;AAChF;AAIA,SAAS,cAAc,KAAK,EAAE,GAAG,EAAC,GAAI;AAClC,iBAAe,IAAI,GAAG,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW;AACzD,iBAAe,IAAI,GAAG,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW;AAC7D;AACA,MAAM,sBAAsB;AAC5B,MAAM,sBAAsB;AAO5B,SAAS,gBAAgB,KAAK,WAAW,UAAU,qBAAqB,OAAO;AAC3E,QAAM,aAAa,SAAS;AAC5B,MAAI,CAAC;AACD;AAEJ,YAAU,IAAI,UAAU,IAAI;AAC5B,MAAI;AACJ,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACjC,WAAO,SAAS,CAAC;AACjB,YAAQ,KAAK;AAKb,UAAM,EAAE,kBAAkB,KAAK;AAC/B,QAAI,iBACA,cAAc,MAAM,SACpB,cAAc,MAAM,MAAM,YAAY,YAAY;AAClD;AAAA,IACZ;AACQ,QAAI,sBACA,KAAK,QAAQ,gBACb,KAAK,UACL,SAAS,KAAK,MAAM;AACpB,mBAAa,KAAK;AAAA,QACd,GAAG,CAAC,KAAK,OAAO,OAAO;AAAA,QACvB,GAAG,CAAC,KAAK,OAAO,OAAO;AAAA,MACvC,CAAa;AAAA,IACb;AACQ,QAAI,OAAO;AAEP,gBAAU,KAAK,MAAM,EAAE;AACvB,gBAAU,KAAK,MAAM,EAAE;AAEvB,oBAAc,KAAK,KAAK;AAAA,IACpC;AACQ,QAAI,sBAAsB,aAAa,KAAK,YAAY,GAAG;AACvD,mBAAa,KAAK,KAAK,YAAY;AAAA,IAC/C;AAAA,EACA;AAKI,MAAI,UAAU,IAAI,uBACd,UAAU,IAAI,qBAAqB;AACnC,cAAU,IAAI;AAAA,EACtB;AACI,MAAI,UAAU,IAAI,uBACd,UAAU,IAAI,qBAAqB;AACnC,cAAU,IAAI;AAAA,EACtB;AACA;AACA,SAAS,cAAc,MAAMiB,WAAU;AACnC,OAAK,MAAM,KAAK,MAAMA;AACtB,OAAK,MAAM,KAAK,MAAMA;AAC1B;AAMA,SAAS,cAAc,MAAM,eAAe,WAAW,UAAU,aAAa,KAAK;AAC/E,QAAM,cAAc,UAAU,KAAK,KAAK,KAAK,KAAK,UAAU;AAE5D,iBAAe,MAAM,eAAe,WAAW,aAAa,QAAQ;AACxE;AAIA,SAAS,aAAa,KAAK,WAAW;AAClC,gBAAc,IAAI,GAAG,UAAU,GAAG,UAAU,QAAQ,UAAU,OAAO,UAAU,OAAO;AACtF,gBAAc,IAAI,GAAG,UAAU,GAAG,UAAU,QAAQ,UAAU,OAAO,UAAU,OAAO;AAC1F;ACjHA,SAAS,mBAAmB,UAAUD,iBAAgB;AAClD,SAAO,wBAAwB,mBAAmB,SAAS,sBAAuB,GAAEA,eAAc,CAAC;AACvG;AACA,SAAS,eAAe,SAASE,qBAAoB,oBAAoB;AACrE,QAAM,cAAc,mBAAmB,SAAS,kBAAkB;AAClE,QAAM,EAAE,OAAM,IAAKA;AACnB,MAAI,QAAQ;AACR,kBAAc,YAAY,GAAG,OAAO,OAAO,CAAC;AAC5C,kBAAc,YAAY,GAAG,OAAO,OAAO,CAAC;AAAA,EACpD;AACI,SAAO;AACX;ACbA,MAAM,mBAAmB,CAAC,EAAE,cAAc;AACtC,SAAO,UAAU,QAAQ,cAAc,cAAc;AACzD;ACHA,MAAM,WAAW,CAAC,GAAG,MAAM,KAAK,IAAI,IAAI,CAAC;AACzC,SAAS,WAAW,GAAG,GAAG;AAEtB,QAAM,SAAS,SAAS,EAAE,GAAG,EAAE,CAAC;AAChC,QAAM,SAAS,SAAS,EAAE,GAAG,EAAE,CAAC;AAChC,SAAO,KAAK,KAAK,UAAU,IAAI,UAAU,CAAC;AAC9C;ACGA,MAAM,WAAW;AAAA,EACb,YAAY,OAAO,UAAU,EAAE,oBAAoB,eAAe,mBAAmB,MAAQ,IAAG,IAAI;AAIhG,SAAK,aAAa;AAIlB,SAAK,gBAAgB;AAIrB,SAAK,oBAAoB;AAIzB,SAAK,WAAW,CAAE;AAIlB,SAAK,gBAAgB;AACrB,SAAK,cAAc,MAAM;AACrB,UAAI,EAAE,KAAK,iBAAiB,KAAK;AAC7B;AACJ,YAAMC,QAAO,WAAW,KAAK,mBAAmB,KAAK,OAAO;AAC5D,YAAM,eAAe,KAAK,eAAe;AAIzC,YAAM,0BAA0B,WAAWA,MAAK,QAAQ,EAAE,GAAG,GAAG,GAAG,EAAG,CAAA,KAAK;AAC3E,UAAI,CAAC,gBAAgB,CAAC;AAClB;AACJ,YAAM,EAAE,OAAAC,OAAK,IAAKD;AAClB,YAAM,EAAE,WAAAE,WAAS,IAAK;AACtB,WAAK,QAAQ,KAAK,EAAE,GAAGD,QAAO,WAAAC,WAAS,CAAE;AACzC,YAAM,EAAE,SAAS,OAAQ,IAAG,KAAK;AACjC,UAAI,CAAC,cAAc;AACf,mBAAW,QAAQ,KAAK,eAAeF,KAAI;AAC3C,aAAK,aAAa,KAAK;AAAA,MACvC;AACY,gBAAU,OAAO,KAAK,eAAeA,KAAI;AAAA,IAC5C;AACD,SAAK,oBAAoB,CAACG,QAAOH,UAAS;AACtC,WAAK,gBAAgBG;AACrB,WAAK,oBAAoB,eAAeH,OAAM,KAAK,kBAAkB;AAErE,YAAM,OAAO,KAAK,aAAa,IAAI;AAAA,IACtC;AACD,SAAK,kBAAkB,CAACG,QAAOH,UAAS;AACpC,WAAK,IAAK;AACV,YAAM,EAAE,OAAO,cAAc,gBAAiB,IAAG,KAAK;AACtD,UAAI,KAAK;AACL,2BAAmB,gBAAiB;AACxC,UAAI,EAAE,KAAK,iBAAiB,KAAK;AAC7B;AACJ,YAAM,UAAU,WAAWG,OAAM,SAAS,kBACpC,KAAK,oBACL,eAAeH,OAAM,KAAK,kBAAkB,GAAG,KAAK,OAAO;AACjE,UAAI,KAAK,cAAc,OAAO;AAC1B,cAAMG,QAAO,OAAO;AAAA,MACpC;AACY,sBAAgB,aAAaA,QAAO,OAAO;AAAA,IAC9C;AAED,QAAI,CAAC,iBAAiB,KAAK;AACvB;AACJ,SAAK,mBAAmB;AACxB,SAAK,WAAW;AAChB,SAAK,qBAAqB;AAC1B,SAAK,gBAAgB,iBAAiB;AACtC,UAAM,OAAO,iBAAiB,KAAK;AACnC,UAAM,cAAc,eAAe,MAAM,KAAK,kBAAkB;AAChE,UAAM,EAAE,MAAK,IAAK;AAClB,UAAM,EAAE,UAAS,IAAK;AACtB,SAAK,UAAU,CAAC,EAAE,GAAG,OAAO,UAAS,CAAE;AACvC,UAAM,EAAE,eAAc,IAAK;AAC3B,sBACI,eAAe,OAAO,WAAW,aAAa,KAAK,OAAO,CAAC;AAC/D,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,eAAe,eAAe,KAAK,iBAAiB,GAAG,gBAAgB,KAAK,eAAe,aAAa,KAAK,eAAe,GAAG,gBAAgB,KAAK,eAAe,iBAAiB,KAAK,eAAe,CAAC;AAAA,EAClQ;AAAA,EACI,eAAe,UAAU;AACrB,SAAK,WAAW;AAAA,EACxB;AAAA,EACI,MAAM;AACF,SAAK,mBAAmB,KAAK,gBAAiB;AAC9C,gBAAY,KAAK,WAAW;AAAA,EACpC;AACA;AACA,SAAS,eAAe,MAAM,oBAAoB;AAC9C,SAAO,qBAAqB,EAAE,OAAO,mBAAmB,KAAK,KAAK,EAAC,IAAK;AAC5E;AACA,SAAS,cAAc,GAAG,GAAG;AACzB,SAAO,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,EAAE,IAAI,EAAE,EAAG;AACzC;AACA,SAAS,WAAW,EAAE,MAAO,GAAE,SAAS;AACpC,SAAO;AAAA,IACH;AAAA,IACA,OAAO,cAAc,OAAO,gBAAgB,OAAO,CAAC;AAAA,IACpD,QAAQ,cAAc,OAAO,iBAAiB,OAAO,CAAC;AAAA,IACtD,UAAU,YAAY,SAAS,GAAG;AAAA,EACrC;AACL;AACA,SAAS,iBAAiB,SAAS;AAC/B,SAAO,QAAQ,CAAC;AACpB;AACA,SAAS,gBAAgB,SAAS;AAC9B,SAAO,QAAQ,QAAQ,SAAS,CAAC;AACrC;AACA,SAAS,YAAY,SAAS,WAAW;AACrC,MAAI,QAAQ,SAAS,GAAG;AACpB,WAAO,EAAE,GAAG,GAAG,GAAG,EAAG;AAAA,EAC7B;AACI,MAAI,IAAI,QAAQ,SAAS;AACzB,MAAI,mBAAmB;AACvB,QAAM,YAAY,gBAAgB,OAAO;AACzC,SAAO,KAAK,GAAG;AACX,uBAAmB,QAAQ,CAAC;AAC5B,QAAI,UAAU,YAAY,iBAAiB,YACvC,sBAAsB,SAAS,GAAG;AAClC;AAAA,IACZ;AACQ;AAAA,EACR;AACI,MAAI,CAAC,kBAAkB;AACnB,WAAO,EAAE,GAAG,GAAG,GAAG,EAAG;AAAA,EAC7B;AACI,QAAMC,QAAO,sBAAsB,UAAU,YAAY,iBAAiB,SAAS;AACnF,MAAIA,UAAS,GAAG;AACZ,WAAO,EAAE,GAAG,GAAG,GAAG,EAAG;AAAA,EAC7B;AACI,QAAM,kBAAkB;AAAA,IACpB,IAAI,UAAU,IAAI,iBAAiB,KAAKA;AAAA,IACxC,IAAI,UAAU,IAAI,iBAAiB,KAAKA;AAAA,EAC3C;AACD,MAAI,gBAAgB,MAAM,UAAU;AAChC,oBAAgB,IAAI;AAAA,EAC5B;AACI,MAAI,gBAAgB,MAAM,UAAU;AAChC,oBAAgB,IAAI;AAAA,EAC5B;AACI,SAAO;AACX;AC9IA,SAAS,iBAAiB,OAAO,EAAE,KAAK,IAAG,GAAI,SAAS;AACpD,MAAI,QAAQ,UAAa,QAAQ,KAAK;AAElC,YAAQ,UACF,UAAU,KAAK,OAAO,QAAQ,GAAG,IACjC,KAAK,IAAI,OAAO,GAAG;AAAA,EACjC,WACa,QAAQ,UAAa,QAAQ,KAAK;AAEvC,YAAQ,UACF,UAAU,KAAK,OAAO,QAAQ,GAAG,IACjC,KAAK,IAAI,OAAO,GAAG;AAAA,EACjC;AACI,SAAO;AACX;AAMA,SAAS,4BAA4B,MAAM,KAAK,KAAK;AACjD,SAAO;AAAA,IACH,KAAK,QAAQ,SAAY,KAAK,MAAM,MAAM;AAAA,IAC1C,KAAK,QAAQ,SACP,KAAK,MAAM,OAAO,KAAK,MAAM,KAAK,OAClC;AAAA,EACT;AACL;AAKA,SAAS,wBAAwB,WAAW,EAAE,KAAK,MAAM,QAAQ,SAAS;AACtE,SAAO;AAAA,IACH,GAAG,4BAA4B,UAAU,GAAG,MAAM,KAAK;AAAA,IACvD,GAAG,4BAA4B,UAAU,GAAG,KAAK,MAAM;AAAA,EAC1D;AACL;AAIA,SAAS,4BAA4B,YAAY,iBAAiB;AAC9D,MAAI,MAAM,gBAAgB,MAAM,WAAW;AAC3C,MAAI,MAAM,gBAAgB,MAAM,WAAW;AAG3C,MAAI,gBAAgB,MAAM,gBAAgB,MACtC,WAAW,MAAM,WAAW,KAAK;AACjC,KAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG;AAAA,EAC9B;AACI,SAAO,EAAE,KAAK,IAAK;AACvB;AAIA,SAAS,wBAAwB,WAAW,gBAAgB;AACxD,SAAO;AAAA,IACH,GAAG,4BAA4B,UAAU,GAAG,eAAe,CAAC;AAAA,IAC5D,GAAG,4BAA4B,UAAU,GAAG,eAAe,CAAC;AAAA,EAC/D;AACL;AAKA,SAAS,WAAW,QAAQ,QAAQ;AAChC,MAAI,SAAS;AACb,QAAM,eAAe,WAAW,MAAM;AACtC,QAAM,eAAe,WAAW,MAAM;AACtC,MAAI,eAAe,cAAc;AAC7B,aAAS,SAAS,OAAO,KAAK,OAAO,MAAM,cAAc,OAAO,GAAG;AAAA,EAC3E,WACa,eAAe,cAAc;AAClC,aAAS,SAAS,OAAO,KAAK,OAAO,MAAM,cAAc,OAAO,GAAG;AAAA,EAC3E;AACI,SAAO,MAAM,GAAG,GAAG,MAAM;AAC7B;AAIA,SAAS,sBAAsBvB,SAAQ,aAAa;AAChD,QAAM,sBAAsB,CAAE;AAC9B,MAAI,YAAY,QAAQ,QAAW;AAC/B,wBAAoB,MAAM,YAAY,MAAMA,QAAO;AAAA,EAC3D;AACI,MAAI,YAAY,QAAQ,QAAW;AAC/B,wBAAoB,MAAM,YAAY,MAAMA,QAAO;AAAA,EAC3D;AACI,SAAO;AACX;AACA,MAAM,iBAAiB;AAIvB,SAAS,mBAAmB,cAAc,gBAAgB;AACtD,MAAI,gBAAgB,OAAO;AACvB,kBAAc;AAAA,EACtB,WACa,gBAAgB,MAAM;AAC3B,kBAAc;AAAA,EACtB;AACI,SAAO;AAAA,IACH,GAAG,mBAAmB,aAAa,QAAQ,OAAO;AAAA,IAClD,GAAG,mBAAmB,aAAa,OAAO,QAAQ;AAAA,EACrD;AACL;AACA,SAAS,mBAAmB,aAAa,UAAU,UAAU;AACzD,SAAO;AAAA,IACH,KAAK,oBAAoB,aAAa,QAAQ;AAAA,IAC9C,KAAK,oBAAoB,aAAa,QAAQ;AAAA,EACjD;AACL;AACA,SAAS,oBAAoB,aAAa,OAAO;AAC7C,SAAO,OAAO,gBAAgB,WACxB,cACA,YAAY,KAAK,KAAK;AAChC;AC5GA,MAAM,sBAAsB,oBAAI,QAAS;AAKzC,MAAM,0BAA0B;AAAA,EAC5B,YAAY,eAAe;AACvB,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,mBAAmB;AACxB,SAAK,cAAc,EAAE,GAAG,GAAG,GAAG,EAAG;AAIjC,SAAK,cAAc;AACnB,SAAK,wBAAwB;AAI7B,SAAK,UAAU,UAAW;AAC1B,SAAK,gBAAgB;AAAA,EAC7B;AAAA,EACI,MAAM,aAAa,EAAE,eAAe,MAAK,IAAK,CAAA,GAAI;AAI9C,UAAM,EAAE,oBAAoB,KAAK;AACjC,QAAI,mBAAmB,gBAAgB,cAAc;AACjD;AACJ,UAAM,iBAAiB,CAAC,UAAU;AAC9B,YAAM,EAAE,kBAAAwB,kBAAgB,IAAK,KAAK,SAAU;AAG5C,MAAAA,oBAAmB,KAAK,mBAAmB,KAAK,cAAe;AAC/D,UAAI,cAAc;AACd,aAAK,aAAa,iBAAiB,KAAK,EAAE,KAAK;AAAA,MAC/D;AAAA,IACS;AACD,UAAM,UAAU,CAAC,OAAO,SAAS;AAE7B,YAAM,EAAE,MAAAvB,OAAM,iBAAiB,YAAW,IAAK,KAAK,SAAU;AAC9D,UAAIA,SAAQ,CAAC,iBAAiB;AAC1B,YAAI,KAAK;AACL,eAAK,aAAc;AACvB,aAAK,eAAe,YAAYA,KAAI;AAEpC,YAAI,CAAC,KAAK;AACN;AAAA,MACpB;AACY,WAAK,aAAa;AAClB,WAAK,mBAAmB;AACxB,WAAK,mBAAoB;AACzB,UAAI,KAAK,cAAc,YAAY;AAC/B,aAAK,cAAc,WAAW,qBAAqB;AACnD,aAAK,cAAc,WAAW,SAAS;AAAA,MACvD;AAIY,eAAS,CAAC,SAAS;AACf,YAAI,UAAU,KAAK,mBAAmB,IAAI,EAAE,IAAG,KAAM;AAIrD,YAAI,QAAQ,KAAK,OAAO,GAAG;AACvB,gBAAM,EAAE,eAAe,KAAK;AAC5B,cAAI,cAAc,WAAW,QAAQ;AACjC,kBAAM,eAAe,WAAW,OAAO,UAAU,IAAI;AACrD,gBAAI,cAAc;AACd,oBAAM,SAAS,WAAW,YAAY;AACtC,wBAAU,UAAU,WAAW,OAAO,IAAI;AAAA,YACtE;AAAA,UACA;AAAA,QACA;AACgB,aAAK,YAAY,IAAI,IAAI;AAAA,MACzC,CAAa;AAED,UAAI,aAAa;AACb,cAAM,WAAW,MAAM,YAAY,OAAO,IAAI,CAAC;AAAA,MAC/D;AACY,2BAAqB,KAAK,eAAe,WAAW;AACpD,YAAM,EAAE,mBAAmB,KAAK;AAChC,wBAAkB,eAAe,UAAU,aAAa,IAAI;AAAA,IAC/D;AACD,UAAM,SAAS,CAAC,OAAO,SAAS;AAE5B,YAAM,EAAE,iBAAiB,mBAAmB,iBAAiB,OAAS,IAAG,KAAK,SAAU;AAExF,UAAI,CAAC,mBAAmB,CAAC,KAAK;AAC1B;AACJ,YAAM,EAAE,OAAM,IAAK;AAEnB,UAAI,qBAAqB,KAAK,qBAAqB,MAAM;AACrD,aAAK,mBAAmB,oBAAoB,MAAM;AAElD,YAAI,KAAK,qBAAqB,MAAM;AAChC,6BAAmB,gBAAgB,KAAK,gBAAgB;AAAA,QAC5E;AACgB;AAAA,MAChB;AAEY,WAAK,WAAW,KAAK,KAAK,OAAO,MAAM;AACvC,WAAK,WAAW,KAAK,KAAK,OAAO,MAAM;AAOvC,WAAK,cAAc,OAAQ;AAK3B,gBAAU,OAAO,OAAO,IAAI;AAAA,IAC/B;AACD,UAAM,eAAe,CAAC,OAAO,SAAS,KAAK,KAAK,OAAO,IAAI;AAC3D,UAAM,kBAAkB,MAAM,SAAS,CAAC,SAAS,KAAK,kBAAkB,IAAI,MAAM,YAC9E,KAAK,mBAAmB,IAAI,EAAE,WAAW,KAAI,CAAE;AACnD,UAAM,EAAE,iBAAgB,IAAK,KAAK,SAAU;AAC5C,SAAK,aAAa,IAAI,WAAW,aAAa;AAAA,MAC1C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACZ,GAAW;AAAA,MACC,oBAAoB,KAAK,cAAc,sBAAuB;AAAA,MAC9D;AAAA,MACA,eAAe,iBAAiB,KAAK,aAAa;AAAA,IAC9D,CAAS;AAAA,EACT;AAAA,EACI,KAAK,OAAO,MAAM;AACd,UAAM,aAAa,KAAK;AACxB,SAAK,OAAQ;AACb,QAAI,CAAC;AACD;AACJ,UAAM,EAAE,SAAQ,IAAK;AACrB,SAAK,eAAe,QAAQ;AAC5B,UAAM,EAAE,UAAS,IAAK,KAAK,SAAU;AACrC,QAAI,WAAW;AACX,YAAM,WAAW,MAAM,UAAU,OAAO,IAAI,CAAC;AAAA,IACzD;AAAA,EACA;AAAA,EACI,SAAS;AACL,SAAK,aAAa;AAClB,UAAM,EAAE,YAAY,eAAgB,IAAG,KAAK;AAC5C,QAAI,YAAY;AACZ,iBAAW,qBAAqB;AAAA,IAC5C;AACQ,SAAK,cAAc,KAAK,WAAW,IAAK;AACxC,SAAK,aAAa;AAClB,UAAM,EAAE,gBAAe,IAAK,KAAK,SAAU;AAC3C,QAAI,CAAC,mBAAmB,KAAK,cAAc;AACvC,WAAK,aAAc;AACnB,WAAK,eAAe;AAAA,IAChC;AACQ,sBAAkB,eAAe,UAAU,aAAa,KAAK;AAAA,EACrE;AAAA,EACI,WAAW,MAAM,QAAQ,QAAQ;AAC7B,UAAM,EAAE,MAAAA,MAAI,IAAK,KAAK,SAAU;AAEhC,QAAI,CAAC,UAAU,CAAC,WAAW,MAAMA,OAAM,KAAK,gBAAgB;AACxD;AACJ,UAAM,YAAY,KAAK,mBAAmB,IAAI;AAC9C,QAAI,OAAO,KAAK,YAAY,IAAI,IAAI,OAAO,IAAI;AAE/C,QAAI,KAAK,eAAe,KAAK,YAAY,IAAI,GAAG;AAC5C,aAAO,iBAAiB,MAAM,KAAK,YAAY,IAAI,GAAG,KAAK,QAAQ,IAAI,CAAC;AAAA,IACpF;AACQ,cAAU,IAAI,IAAI;AAAA,EAC1B;AAAA,EACI,qBAAqB;AACjB,UAAM,EAAE,iBAAiB,gBAAgB,KAAK,SAAU;AACxD,UAAMD,UAAS,KAAK,cAAc,cAC9B,CAAC,KAAK,cAAc,WAAW,SAC7B,KAAK,cAAc,WAAW,QAAQ,KAAK,IAC3C,KAAK,cAAc,YAAY;AACrC,UAAM,kBAAkB,KAAK;AAC7B,QAAI,mBAAmB,YAAY,eAAe,GAAG;AACjD,UAAI,CAAC,KAAK,aAAa;AACnB,aAAK,cAAc,KAAK,sBAAuB;AAAA,MAC/D;AAAA,IACA,OACa;AACD,UAAI,mBAAmBA,SAAQ;AAC3B,aAAK,cAAc,wBAAwBA,QAAO,WAAW,eAAe;AAAA,MAC5F,OACiB;AACD,aAAK,cAAc;AAAA,MACnC;AAAA,IACA;AACQ,SAAK,UAAU,mBAAmB,WAAW;AAK7C,QAAI,oBAAoB,KAAK,eACzBA,WACA,KAAK,eACL,CAAC,KAAK,uBAAuB;AAC7B,eAAS,CAAC,SAAS;AACf,YAAI,KAAK,gBAAgB,SACrB,KAAK,mBAAmB,IAAI,GAAG;AAC/B,eAAK,YAAY,IAAI,IAAI,sBAAsBA,QAAO,UAAU,IAAI,GAAG,KAAK,YAAY,IAAI,CAAC;AAAA,QACjH;AAAA,MACA,CAAa;AAAA,IACb;AAAA,EACA;AAAA,EACI,wBAAwB;AACpB,UAAM,EAAE,iBAAiB,aAAa,yBAAwB,IAAK,KAAK,SAAU;AAClF,QAAI,CAAC,eAAe,CAAC,YAAY,WAAW;AACxC,aAAO;AACX,UAAM,qBAAqB,YAAY;AACvC,cAAU,uBAAuB,MAAM,wGAAwG;AAC/I,UAAM,EAAE,eAAe,KAAK;AAE5B,QAAI,CAAC,cAAc,CAAC,WAAW;AAC3B,aAAO;AACX,UAAM,iBAAiB,eAAe,oBAAoB,WAAW,MAAM,KAAK,cAAc,uBAAuB;AACrH,QAAI,sBAAsB,wBAAwB,WAAW,OAAO,WAAW,cAAc;AAK7F,QAAI,0BAA0B;AAC1B,YAAM,kBAAkB,yBAAyB,wBAAwB,mBAAmB,CAAC;AAC7F,WAAK,wBAAwB,CAAC,CAAC;AAC/B,UAAI,iBAAiB;AACjB,8BAAsB,wBAAwB,eAAe;AAAA,MAC7E;AAAA,IACA;AACQ,WAAO;AAAA,EACf;AAAA,EACI,eAAe,UAAU;AACrB,UAAM,EAAE,MAAAC,OAAM,cAAc,aAAa,gBAAgB,kBAAkB,oBAAmB,IAAM,KAAK,SAAU;AACnH,UAAM,cAAc,KAAK,eAAe,CAAE;AAC1C,UAAM,qBAAqB,SAAS,CAAC,SAAS;AAC1C,UAAI,CAAC,WAAW,MAAMA,OAAM,KAAK,gBAAgB,GAAG;AAChD;AAAA,MAChB;AACY,UAAI,aAAc,eAAe,YAAY,IAAI,KAAM,CAAE;AACzD,UAAI;AACA,qBAAa,EAAE,KAAK,GAAG,KAAK,EAAG;AAOnC,YAAM,kBAAkB,cAAc,MAAM;AAC5C,YAAM,gBAAgB,cAAc,KAAK;AACzC,YAAM,UAAU;AAAA,QACZ,MAAM;AAAA,QACN,UAAU,eAAe,SAAS,IAAI,IAAI;AAAA,QAC1C;AAAA,QACA;AAAA,QACA,cAAc;AAAA,QACd,WAAW;AAAA,QACX,WAAW;AAAA,QACX,GAAG;AAAA,QACH,GAAG;AAAA,MACN;AAID,aAAO,KAAK,wBAAwB,MAAM,OAAO;AAAA,IAC7D,CAAS;AAED,WAAO,QAAQ,IAAI,kBAAkB,EAAE,KAAK,mBAAmB;AAAA,EACvE;AAAA,EACI,wBAAwB,MAAM,YAAY;AACtC,UAAM,YAAY,KAAK,mBAAmB,IAAI;AAC9C,yBAAqB,KAAK,eAAe,IAAI;AAC7C,WAAO,UAAU,MAAM,mBAAmB,MAAM,WAAW,GAAG,YAAY,KAAK,eAAe,KAAK,CAAC;AAAA,EAC5G;AAAA,EACI,gBAAgB;AACZ,aAAS,CAAC,SAAS,KAAK,mBAAmB,IAAI,EAAE,MAAM;AAAA,EAC/D;AAAA,EACI,iBAAiB;AACb,aAAS,CAAC,SAAS,KAAK,mBAAmB,IAAI,EAAE,WAAW,OAAO;AAAA,EAC3E;AAAA,EACI,kBAAkB,MAAM;AACpB,WAAO,KAAK,mBAAmB,IAAI,EAAE,WAAW;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOI,mBAAmB,MAAM;AACrB,UAAM,UAAU,QAAQ,KAAK,YAAa,CAAA;AAC1C,UAAM,QAAQ,KAAK,cAAc,SAAU;AAC3C,UAAM,sBAAsB,MAAM,OAAO;AACzC,WAAO,sBACD,sBACA,KAAK,cAAc,SAAS,OAAO,MAAM,UACrC,MAAM,QAAQ,IAAI,IAClB,WAAc,CAAC;AAAA,EACjC;AAAA,EACI,aAAa,OAAO;AAChB,aAAS,CAAC,SAAS;AACf,YAAM,EAAE,MAAAA,MAAI,IAAK,KAAK,SAAU;AAEhC,UAAI,CAAC,WAAW,MAAMA,OAAM,KAAK,gBAAgB;AAC7C;AACJ,YAAM,EAAE,eAAe,KAAK;AAC5B,YAAM,YAAY,KAAK,mBAAmB,IAAI;AAC9C,UAAI,cAAc,WAAW,QAAQ;AACjC,cAAM,EAAE,KAAK,IAAK,IAAG,WAAW,OAAO,UAAU,IAAI;AACrD,kBAAU,IAAI,MAAM,IAAI,IAAI,UAAU,KAAK,KAAK,GAAG,CAAC;AAAA,MACpE;AAAA,IACA,CAAS;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMI,iCAAiC;AAC7B,QAAI,CAAC,KAAK,cAAc;AACpB;AACJ,UAAM,EAAE,MAAAA,OAAM,oBAAoB,KAAK,SAAU;AACjD,UAAM,EAAE,eAAe,KAAK;AAC5B,QAAI,CAAC,YAAY,eAAe,KAAK,CAAC,cAAc,CAAC,KAAK;AACtD;AAKJ,SAAK,cAAe;AAKpB,UAAM,cAAc,EAAE,GAAG,GAAG,GAAG,EAAG;AAClC,aAAS,CAAC,SAAS;AACf,YAAM,YAAY,KAAK,mBAAmB,IAAI;AAC9C,UAAI,aAAa,KAAK,gBAAgB,OAAO;AACzC,cAAM,SAAS,UAAU,IAAK;AAC9B,oBAAY,IAAI,IAAI,WAAW,EAAE,KAAK,QAAQ,KAAK,OAAM,GAAI,KAAK,YAAY,IAAI,CAAC;AAAA,MACnG;AAAA,IACA,CAAS;AAID,UAAM,EAAE,kBAAmB,IAAG,KAAK,cAAc,SAAU;AAC3D,SAAK,cAAc,QAAQ,MAAM,YAAY,oBACvC,kBAAkB,CAAE,GAAE,EAAE,IACxB;AACN,eAAW,QAAQ,WAAW,KAAK,aAAc;AACjD,eAAW,aAAc;AACzB,SAAK,mBAAoB;AAKzB,aAAS,CAAC,SAAS;AACf,UAAI,CAAC,WAAW,MAAMA,OAAM,IAAI;AAC5B;AAIJ,YAAM,YAAY,KAAK,mBAAmB,IAAI;AAC9C,YAAM,EAAE,KAAK,IAAG,IAAK,KAAK,YAAY,IAAI;AAC1C,gBAAU,IAAI,UAAU,KAAK,KAAK,YAAY,IAAI,CAAC,CAAC;AAAA,IAChE,CAAS;AAAA,EACT;AAAA,EACI,eAAe;AACX,QAAI,CAAC,KAAK,cAAc;AACpB;AACJ,wBAAoB,IAAI,KAAK,eAAe,IAAI;AAChD,UAAM,UAAU,KAAK,cAAc;AAInC,UAAM,sBAAsB,gBAAgB,SAAS,eAAe,CAAC,UAAU;AAC3E,YAAM,EAAE,MAAAA,OAAM,eAAe,KAAI,IAAK,KAAK,SAAU;AACrD,MAAAA,SAAQ,gBAAgB,KAAK,MAAM,KAAK;AAAA,IACpD,CAAS;AACD,UAAM,yBAAyB,MAAM;AACjC,YAAM,EAAE,gBAAe,IAAK,KAAK,SAAU;AAC3C,UAAI,YAAY,eAAe,KAAK,gBAAgB,SAAS;AACzD,aAAK,cAAc,KAAK,sBAAuB;AAAA,MAC/D;AAAA,IACS;AACD,UAAM,EAAE,eAAe,KAAK;AAC5B,UAAM,4BAA4B,WAAW,iBAAiB,WAAW,sBAAsB;AAC/F,QAAI,cAAc,CAAC,WAAW,QAAQ;AAClC,iBAAW,QAAQ,WAAW,KAAK,aAAc;AACjD,iBAAW,aAAc;AAAA,IACrC;AACQ,UAAM,KAAK,sBAAsB;AAKjC,UAAM,qBAAqB,YAAY,QAAQ,UAAU,MAAM,KAAK,gCAAgC;AAKpG,UAAM,2BAA2B,WAAW,iBAAiB,aAAc,CAAC,EAAE,OAAO,uBAAuB;AACxG,UAAI,KAAK,cAAc,kBAAkB;AACrC,iBAAS,CAAC,SAAS;AACf,gBAAMc,eAAc,KAAK,mBAAmB,IAAI;AAChD,cAAI,CAACA;AACD;AACJ,eAAK,YAAY,IAAI,KAAK,MAAM,IAAI,EAAE;AACtC,UAAAA,aAAY,IAAIA,aAAY,IAAK,IAAG,MAAM,IAAI,EAAE,SAAS;AAAA,QAC7E,CAAiB;AACD,aAAK,cAAc,OAAQ;AAAA,MAC3C;AAAA,IACA,CAAW;AACH,WAAO,MAAM;AACT,yBAAoB;AACpB,0BAAqB;AACrB,gCAA2B;AAC3B,kCAA4B,yBAA0B;AAAA,IACzD;AAAA,EACT;AAAA,EACI,WAAW;AACP,UAAM,QAAQ,KAAK,cAAc,SAAU;AAC3C,UAAM,EAAE,MAAAd,QAAO,OAAO,oBAAoB,OAAO,kBAAkB,OAAO,kBAAkB,OAAO,cAAc,gBAAgB,eAAe,KAAO,IAAG;AAC1J,WAAO;AAAA,MACH,GAAG;AAAA,MACH,MAAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACH;AAAA,EACT;AACA;AACA,SAAS,WAAW,WAAWA,OAAM,kBAAkB;AACnD,UAASA,UAAS,QAAQA,UAAS,eAC9B,qBAAqB,QAAQ,qBAAqB;AAC3D;AAQA,SAAS,oBAAoB,QAAQ,gBAAgB,IAAI;AACrD,MAAI,YAAY;AAChB,MAAI,KAAK,IAAI,OAAO,CAAC,IAAI,eAAe;AACpC,gBAAY;AAAA,EACpB,WACa,KAAK,IAAI,OAAO,CAAC,IAAI,eAAe;AACzC,gBAAY;AAAA,EACpB;AACI,SAAO;AACX;ACrdA,MAAM,oBAAoB,QAAQ;AAAA,EAC9B,YAAY,MAAM;AACd,UAAM,IAAI;AACV,SAAK,sBAAsB;AAC3B,SAAK,kBAAkB;AACvB,SAAK,WAAW,IAAI,0BAA0B,IAAI;AAAA,EAC1D;AAAA,EACI,QAAQ;AAGJ,UAAM,EAAE,aAAc,IAAG,KAAK,KAAK,SAAU;AAC7C,QAAI,cAAc;AACd,WAAK,sBAAsB,aAAa,UAAU,KAAK,QAAQ;AAAA,IAC3E;AACQ,SAAK,kBAAkB,KAAK,SAAS,aAAc,KAAI;AAAA,EAC/D;AAAA,EACI,UAAU;AACN,SAAK,oBAAqB;AAC1B,SAAK,gBAAiB;AAAA,EAC9B;AACA;ACjBA,MAAM,eAAe,CAAC,YAAY,CAAC,OAAO,SAAS;AAC/C,MAAI,SAAS;AACT,UAAM,WAAW,MAAM,QAAQ,OAAO,IAAI,CAAC;AAAA,EACnD;AACA;AACA,MAAM,mBAAmB,QAAQ;AAAA,EAC7B,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,4BAA4B;AAAA,EACzC;AAAA,EACI,cAAc,kBAAkB;AAC5B,SAAK,UAAU,IAAI,WAAW,kBAAkB,KAAK,qBAAqB;AAAA,MACtE,oBAAoB,KAAK,KAAK,sBAAuB;AAAA,MACrD,eAAe,iBAAiB,KAAK,IAAI;AAAA,IACrD,CAAS;AAAA,EACT;AAAA,EACI,oBAAoB;AAChB,UAAM,EAAE,mBAAmB,YAAY,OAAO,SAAQ,IAAK,KAAK,KAAK,SAAU;AAC/E,WAAO;AAAA,MACH,gBAAgB,aAAa,iBAAiB;AAAA,MAC9C,SAAS,aAAa,UAAU;AAAA,MAChC,QAAQ;AAAA,MACR,OAAO,CAAC,OAAO,SAAS;AACpB,eAAO,KAAK;AACZ,YAAI,UAAU;AACV,gBAAM,WAAW,MAAM,SAAS,OAAO,IAAI,CAAC;AAAA,QAChE;AAAA,MACa;AAAA,IACJ;AAAA,EACT;AAAA,EACI,QAAQ;AACJ,SAAK,4BAA4B,gBAAgB,KAAK,KAAK,SAAS,eAAe,CAAC,UAAU,KAAK,cAAc,KAAK,CAAC;AAAA,EAC/H;AAAA,EACI,SAAS;AACL,SAAK,WAAW,KAAK,QAAQ,eAAe,KAAK,mBAAmB;AAAA,EAC5E;AAAA,EACI,UAAU;AACN,SAAK,0BAA2B;AAChC,SAAK,WAAW,KAAK,QAAQ,IAAK;AAAA,EAC1C;AACA;AC1CA,MAAM,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1B,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,gBAAgB;AACpB;ACdA,SAAS,gBAAgB,QAAQ,MAAM;AACnC,MAAI,KAAK,QAAQ,KAAK;AAClB,WAAO;AACX,SAAQ,UAAU,KAAK,MAAM,KAAK,OAAQ;AAC9C;AAQA,MAAM,sBAAsB;AAAA,EACxB,SAAS,CAAC,QAAQ,SAAS;AACvB,QAAI,CAAC,KAAK;AACN,aAAO;AAKX,QAAI,OAAO,WAAW,UAAU;AAC5B,UAAI,GAAG,KAAK,MAAM,GAAG;AACjB,iBAAS,WAAW,MAAM;AAAA,MAC1C,OACiB;AACD,eAAO;AAAA,MACvB;AAAA,IACA;AAKQ,UAAM,IAAI,gBAAgB,QAAQ,KAAK,OAAO,CAAC;AAC/C,UAAM,IAAI,gBAAgB,QAAQ,KAAK,OAAO,CAAC;AAC/C,WAAO,GAAG,CAAC,KAAK,CAAC;AAAA,EACpB;AACL;ACpCA,MAAM,mBAAmB;AAAA,EACrB,SAAS,CAAC,QAAQ,EAAE,WAAW,gBAAe,MAAO;AACjD,UAAM,WAAW;AACjB,UAAM,SAAS,QAAQ,MAAM,MAAM;AAEnC,QAAI,OAAO,SAAS;AAChB,aAAO;AACX,UAAM,WAAW,QAAQ,kBAAkB,MAAM;AACjD,UAAM,SAAS,OAAO,OAAO,CAAC,MAAM,WAAW,IAAI;AAEnD,UAAM,SAAS,gBAAgB,EAAE,QAAQ,UAAU;AACnD,UAAM,SAAS,gBAAgB,EAAE,QAAQ,UAAU;AACnD,WAAO,IAAI,MAAM,KAAK;AACtB,WAAO,IAAI,MAAM,KAAK;AAOtB,UAAM,eAAe,UAAU,QAAQ,QAAQ,GAAG;AAElD,QAAI,OAAO,OAAO,IAAI,MAAM,MAAM;AAC9B,aAAO,IAAI,MAAM,KAAK;AAE1B,QAAI,OAAO,OAAO,IAAI,MAAM,MAAM;AAC9B,aAAO,IAAI,MAAM,KAAK;AAC1B,WAAO,SAAS,MAAM;AAAA,EACzB;AACL;ACnBA,MAAM,iCAAiCwB,aAAAA,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7C,oBAAoB;AAChB,UAAM,EAAE,eAAe,aAAa,mBAAmB,SAAQ,IAAK,KAAK;AACzE,UAAM,EAAE,WAAU,IAAK;AACvB,sBAAkB,sBAAsB;AACxC,QAAI,YAAY;AACZ,UAAI,YAAY;AACZ,oBAAY,MAAM,IAAI,UAAU;AACpC,UAAI,qBAAqB,kBAAkB,YAAY,UAAU;AAC7D,0BAAkB,SAAS,UAAU;AAAA,MACrD;AACY,iBAAW,KAAK,UAAW;AAC3B,iBAAW,iBAAiB,qBAAqB,MAAM;AACnD,aAAK,aAAc;AAAA,MACnC,CAAa;AACD,iBAAW,WAAW;AAAA,QAClB,GAAG,WAAW;AAAA,QACd,gBAAgB,MAAM,KAAK,aAAc;AAAA,MACzD,CAAa;AAAA,IACb;AACQ,0BAAsB,iBAAiB;AAAA,EAC/C;AAAA,EACI,wBAAwB,WAAW;AAC/B,UAAM,EAAE,kBAAkB,eAAe,MAAAxB,OAAM,UAAS,IAAK,KAAK;AAClE,UAAM,EAAE,WAAU,IAAK;AACvB,QAAI,CAAC;AACD,aAAO;AAQX,eAAW,YAAY;AACvB,QAAIA,SACA,UAAU,qBAAqB,oBAC/B,qBAAqB,UACrB,UAAU,cAAc,WAAW;AACnC,iBAAW,WAAY;AAAA,IACnC,OACa;AACD,WAAK,aAAc;AAAA,IAC/B;AACQ,QAAI,UAAU,cAAc,WAAW;AACnC,UAAI,WAAW;AACX,mBAAW,QAAS;AAAA,MACpC,WACqB,CAAC,WAAW,YAAY;AAM7B,cAAM,WAAW,MAAM;AACnB,gBAAM,QAAQ,WAAW,SAAU;AACnC,cAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,QAAQ;AACjC,iBAAK,aAAc;AAAA,UAC3C;AAAA,QACA,CAAiB;AAAA,MACjB;AAAA,IACA;AACQ,WAAO;AAAA,EACf;AAAA,EACI,qBAAqB;AACjB,UAAM,EAAE,WAAU,IAAK,KAAK,MAAM;AAClC,QAAI,YAAY;AACZ,iBAAW,KAAK,UAAW;AAC3B,gBAAU,WAAW,MAAM;AACvB,YAAI,CAAC,WAAW,oBAAoB,WAAW,OAAM,GAAI;AACrD,eAAK,aAAc;AAAA,QACvC;AAAA,MACA,CAAa;AAAA,IACb;AAAA,EACA;AAAA,EACI,uBAAuB;AACnB,UAAM,EAAE,eAAe,aAAa,mBAAmB,eAAc,IAAM,KAAK;AAChF,UAAM,EAAE,WAAU,IAAK;AACvB,QAAI,YAAY;AACZ,iBAAW,0BAA2B;AACtC,UAAI,eAAe,YAAY;AAC3B,oBAAY,MAAM,OAAO,UAAU;AACvC,UAAI,kBAAkB,eAAe;AACjC,uBAAe,WAAW,UAAU;AAAA,IACpD;AAAA,EACA;AAAA,EACI,eAAe;AACX,UAAM,EAAE,iBAAiB,KAAK;AAC9B,oBAAgB,aAAc;AAAA,EACtC;AAAA,EACI,SAAS;AACL,WAAO;AAAA,EACf;AACA;AACA,SAAS,cAAc,OAAO;AAC1B,QAAM,CAAC,WAAW,YAAY,IAAI,YAAa;AAC/C,QAAM,cAAcR,aAAU,WAAC,kBAAkB;AACjD,SAAQW,kBAAAA,IAAI,0BAA0B,EAAE,GAAG,OAAO,aAA0B,mBAAmBX,aAAAA,WAAW,wBAAwB,GAAG,WAAsB,aAA0B,CAAE;AAC3L;AACA,MAAM,yBAAyB;AAAA,EAC3B,cAAc;AAAA,IACV,GAAG;AAAA,IACH,SAAS;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACH;AAAA,EACJ;AAAA,EACD,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,WAAW;AACf;AChIA,SAAS,mBAAmB,OAAO,WAAW,SAAS;AACnD,QAAM,gBAAgB,cAAc,KAAK,IAAI,QAAQ,YAAY,KAAK;AACtE,gBAAc,MAAM,mBAAmB,IAAI,eAAe,WAAW,OAAO,CAAC;AAC7E,SAAO,cAAc;AACzB;ACPA,MAAM,iBAAiB,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE;ACG7C,MAAM,SAAS;AAAA,EACX,cAAc;AACV,SAAK,WAAW,CAAE;AAClB,SAAK,UAAU;AAAA,EACvB;AAAA,EACI,IAAI,OAAO;AACP,kBAAc,KAAK,UAAU,KAAK;AAClC,SAAK,UAAU;AAAA,EACvB;AAAA,EACI,OAAO,OAAO;AACV,eAAW,KAAK,UAAU,KAAK;AAC/B,SAAK,UAAU;AAAA,EACvB;AAAA,EACI,QAAQ,UAAU;AACd,SAAK,WAAW,KAAK,SAAS,KAAK,cAAc;AACjD,SAAK,UAAU;AACf,SAAK,SAAS,QAAQ,QAAQ;AAAA,EACtC;AACA;ACfA,SAAS,MAAM,UAAU,SAAS;AAC9B,QAAM,QAAQ,KAAK,IAAK;AACxB,QAAM,eAAe,CAAC,EAAE,gBAAgB;AACpC,UAAM,UAAU,YAAY;AAC5B,QAAI,WAAW,SAAS;AACpB,kBAAY,YAAY;AACxB,eAAS,UAAU,OAAO;AAAA,IACtC;AAAA,EACK;AACD,QAAM,MAAM,cAAc,IAAI;AAC9B,SAAO,MAAM,YAAY,YAAY;AACzC;ACdA,MAAM,UAAU,CAAC,WAAW,YAAY,cAAc,aAAa;AACnE,MAAM,aAAa,QAAQ;AAC3B,MAAM,WAAW,CAAC,UAAU,OAAO,UAAU,WAAW,WAAW,KAAK,IAAI;AAC5E,MAAM,OAAO,CAAC,UAAU,OAAO,UAAU,YAAY,GAAG,KAAK,KAAK;AAClE,SAAS,UAAU,QAAQ,QAAQ,MAAMiC,WAAU,wBAAwB,cAAc;AACrF,MAAI,wBAAwB;AACxB,WAAO,UAAU,UAAU,GAAG,KAAK,WAAW,GAAG,gBAAgBA,SAAQ,CAAC;AAC1E,WAAO,cAAc,UAAU,OAAO,WAAW,GAAG,GAAG,iBAAiBA,SAAQ,CAAC;AAAA,EACzF,WACa,cAAc;AACnB,WAAO,UAAU,UAAU,OAAO,WAAW,GAAG,KAAK,WAAW,GAAGA,SAAQ;AAAA,EACnF;AAII,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACjC,UAAM,cAAc,SAAS,QAAQ,CAAC,CAAC;AACvC,QAAI,eAAe,UAAU,QAAQ,WAAW;AAChD,QAAI,aAAa,UAAU,MAAM,WAAW;AAC5C,QAAI,iBAAiB,UAAa,eAAe;AAC7C;AACJ,qBAAiB,eAAe;AAChC,mBAAe,aAAa;AAC5B,UAAM,SAAS,iBAAiB,KAC5B,eAAe,KACf,KAAK,YAAY,MAAM,KAAK,UAAU;AAC1C,QAAI,QAAQ;AACR,aAAO,WAAW,IAAI,KAAK,IAAI,UAAU,SAAS,YAAY,GAAG,SAAS,UAAU,GAAGA,SAAQ,GAAG,CAAC;AACnG,UAAI,QAAQ,KAAK,UAAU,KAAK,QAAQ,KAAK,YAAY,GAAG;AACxD,eAAO,WAAW,KAAK;AAAA,MACvC;AAAA,IACA,OACa;AACD,aAAO,WAAW,IAAI;AAAA,IAClC;AAAA,EACA;AAII,MAAI,OAAO,UAAU,KAAK,QAAQ;AAC9B,WAAO,SAAS,UAAU,OAAO,UAAU,GAAG,KAAK,UAAU,GAAGA,SAAQ;AAAA,EAChF;AACA;AACA,SAAS,UAAU,QAAQ,YAAY;AACnC,SAAO,OAAO,UAAU,MAAM,SACxB,OAAO,UAAU,IACjB,OAAO;AACjB;AAwBA,MAAM,kBAAgC,yBAAS,GAAG,KAAK,OAAO;AAC9D,MAAM,mBAAiC,yBAAS,KAAK,MAAM,IAAI;AAC/D,SAAS,SAAS,KAAK,KAAK,QAAQ;AAChC,SAAO,CAAC,MAAM;AAEV,QAAI,IAAI;AACJ,aAAO;AACX,QAAI,IAAI;AACJ,aAAO;AACX,WAAO,OAAO,SAAS,KAAK,KAAK,CAAC,CAAC;AAAA,EACtC;AACL;AChFA,SAAS,aAAa,MAAM,YAAY;AACpC,OAAK,MAAM,WAAW;AACtB,OAAK,MAAM,WAAW;AAC1B;AAMA,SAAS,YAAY,KAAK,WAAW;AACjC,eAAa,IAAI,GAAG,UAAU,CAAC;AAC/B,eAAa,IAAI,GAAG,UAAU,CAAC;AACnC;AAMA,SAAS,kBAAkB,OAAO,aAAa;AAC3C,QAAM,YAAY,YAAY;AAC9B,QAAM,QAAQ,YAAY;AAC1B,QAAM,cAAc,YAAY;AAChC,QAAM,SAAS,YAAY;AAC/B;ACtBA,SAAS,iBAAiB,OAAO,WAAW,OAAO,aAAa,UAAU;AACtE,WAAS;AACT,UAAQ,WAAW,OAAO,IAAI,OAAO,WAAW;AAChD,MAAI,aAAa,QAAW;AACxB,YAAQ,WAAW,OAAO,IAAI,UAAU,WAAW;AAAA,EAC3D;AACI,SAAO;AACX;AAIA,SAAS,gBAAgB,MAAM,YAAY,GAAG,QAAQ,GAAG,SAAS,KAAK,UAAU,aAAa,MAAM,aAAa,MAAM;AACnH,MAAI,QAAQ,KAAK,SAAS,GAAG;AACzB,gBAAY,WAAW,SAAS;AAChC,UAAM,mBAAmB,UAAU,WAAW,KAAK,WAAW,KAAK,YAAY,GAAG;AAClF,gBAAY,mBAAmB,WAAW;AAAA,EAClD;AACI,MAAI,OAAO,cAAc;AACrB;AACJ,MAAI,cAAc,UAAU,WAAW,KAAK,WAAW,KAAK,MAAM;AAClE,MAAI,SAAS;AACT,mBAAe;AACnB,OAAK,MAAM,iBAAiB,KAAK,KAAK,WAAW,OAAO,aAAa,QAAQ;AAC7E,OAAK,MAAM,iBAAiB,KAAK,KAAK,WAAW,OAAO,aAAa,QAAQ;AACjF;AAKA,SAAS,qBAAqB,MAAM,YAAY,CAAC,KAAK,UAAU,SAAS,GAAG,QAAQ,YAAY;AAC5F,kBAAgB,MAAM,WAAW,GAAG,GAAG,WAAW,QAAQ,GAAG,WAAW,SAAS,GAAG,WAAW,OAAO,QAAQ,UAAU;AAC5H;AAIA,MAAM,QAAQ,CAAC,KAAK,UAAU,SAAS;AACvC,MAAM,QAAQ,CAAC,KAAK,UAAU,SAAS;AAKvC,SAAS,oBAAoB,KAAK,YAAY,WAAW,WAAW;AAChE,uBAAqB,IAAI,GAAG,YAAY,OAAO,YAAY,UAAU,IAAI,QAAW,YAAY,UAAU,IAAI,MAAS;AACvH,uBAAqB,IAAI,GAAG,YAAY,OAAO,YAAY,UAAU,IAAI,QAAW,YAAY,UAAU,IAAI,MAAS;AAC3H;AChDA,SAAS,gBAAgB,OAAO;AAC5B,SAAO,MAAM,cAAc,KAAK,MAAM,UAAU;AACpD;AACA,SAAS,YAAY,OAAO;AACxB,SAAO,gBAAgB,MAAM,CAAC,KAAK,gBAAgB,MAAM,CAAC;AAC9D;AACA,SAAS,WAAW,GAAG,GAAG;AACtB,SAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE;AAC1C;AACA,SAAS,UAAU,GAAG,GAAG;AACrB,SAAO,WAAW,EAAE,GAAG,EAAE,CAAC,KAAK,WAAW,EAAE,GAAG,EAAE,CAAC;AACtD;AACA,SAAS,kBAAkB,GAAG,GAAG;AAC7B,SAAQ,KAAK,MAAM,EAAE,GAAG,MAAM,KAAK,MAAM,EAAE,GAAG,KAC1C,KAAK,MAAM,EAAE,GAAG,MAAM,KAAK,MAAM,EAAE,GAAG;AAC9C;AACA,SAAS,iBAAiB,GAAG,GAAG;AAC5B,SAAO,kBAAkB,EAAE,GAAG,EAAE,CAAC,KAAK,kBAAkB,EAAE,GAAG,EAAE,CAAC;AACpE;AACA,SAAS,YAAY,KAAK;AACtB,SAAO,WAAW,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC;AAC/C;AACA,SAAS,gBAAgB,GAAG,GAAG;AAC3B,SAAQ,EAAE,cAAc,EAAE,aACtB,EAAE,UAAU,EAAE,SACd,EAAE,gBAAgB,EAAE;AAC5B;AC1BA,MAAM,UAAU;AAAA,EACZ,cAAc;AACV,SAAK,UAAU,CAAE;AAAA,EACzB;AAAA,EACI,IAAI,MAAM;AACN,kBAAc,KAAK,SAAS,IAAI;AAChC,SAAK,eAAgB;AAAA,EAC7B;AAAA,EACI,OAAO,MAAM;AACT,eAAW,KAAK,SAAS,IAAI;AAC7B,QAAI,SAAS,KAAK,UAAU;AACxB,WAAK,WAAW;AAAA,IAC5B;AACQ,QAAI,SAAS,KAAK,MAAM;AACpB,YAAM,WAAW,KAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC;AACrD,UAAI,UAAU;AACV,aAAK,QAAQ,QAAQ;AAAA,MACrC;AAAA,IACA;AAAA,EACA;AAAA,EACI,SAAS,MAAM;AACX,UAAM,cAAc,KAAK,QAAQ,UAAU,CAAC,WAAW,SAAS,MAAM;AACtE,QAAI,gBAAgB;AAChB,aAAO;AAIX,QAAI;AACJ,aAAS,IAAI,aAAa,KAAK,GAAG,KAAK;AACnC,YAAM,SAAS,KAAK,QAAQ,CAAC;AAC7B,UAAI,OAAO,cAAc,OAAO;AAC5B,mBAAW;AACX;AAAA,MAChB;AAAA,IACA;AACQ,QAAI,UAAU;AACV,WAAK,QAAQ,QAAQ;AACrB,aAAO;AAAA,IACnB,OACa;AACD,aAAO;AAAA,IACnB;AAAA,EACA;AAAA,EACI,QAAQ,MAAM,uBAAuB;AACjC,UAAM,WAAW,KAAK;AACtB,QAAI,SAAS;AACT;AACJ,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,KAAM;AACX,QAAI,UAAU;AACV,eAAS,YAAY,SAAS,eAAgB;AAC9C,WAAK,eAAgB;AACrB,WAAK,aAAa;AAClB,UAAI,uBAAuB;AACvB,aAAK,WAAW,kBAAkB;AAAA,MAClD;AACY,UAAI,SAAS,UAAU;AACnB,aAAK,WAAW,SAAS;AACzB,aAAK,SAAS,eACV,SAAS,mBAAmB,SAAS;AAAA,MACzD;AACY,UAAI,KAAK,QAAQ,KAAK,KAAK,YAAY;AACnC,aAAK,gBAAgB;AAAA,MACrC;AACY,YAAM,EAAE,cAAc,KAAK;AAC3B,UAAI,cAAc,OAAO;AACrB,iBAAS,KAAM;AAAA,MAC/B;AAAA,IAaA;AAAA,EACA;AAAA,EACI,wBAAwB;AACpB,SAAK,QAAQ,QAAQ,CAAC,SAAS;AAC3B,YAAM,EAAE,SAAS,aAAY,IAAK;AAClC,cAAQ,kBAAkB,QAAQ,eAAgB;AAClD,UAAI,cAAc;AACd,qBAAa,QAAQ,kBACjB,aAAa,QAAQ,eAAgB;AAAA,MACzD;AAAA,IACA,CAAS;AAAA,EACT;AAAA,EACI,iBAAiB;AACb,SAAK,QAAQ,QAAQ,CAAC,SAAS;AAC3B,WAAK,YAAY,KAAK,eAAe,KAAK;AAAA,IACtD,CAAS;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKI,qBAAqB;AACjB,QAAI,KAAK,QAAQ,KAAK,KAAK,UAAU;AACjC,WAAK,KAAK,WAAW;AAAA,IACjC;AAAA,EACA;AACA;AC7GA,SAAS,yBAAyB,OAAO,WAAW,iBAAiB;AACjE,MAAI,YAAY;AAOhB,QAAM,aAAa,MAAM,EAAE,YAAY,UAAU;AACjD,QAAM,aAAa,MAAM,EAAE,YAAY,UAAU;AACjD,QAAM,aAAa,iBAAiB,KAAK;AACzC,MAAI,cAAc,cAAc,YAAY;AACxC,gBAAY,eAAe,UAAU,OAAO,UAAU,OAAO,UAAU;AAAA,EAC/E;AAKI,MAAI,UAAU,MAAM,KAAK,UAAU,MAAM,GAAG;AACxC,iBAAa,SAAS,IAAI,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC;AAAA,EACjE;AACI,MAAI,iBAAiB;AACjB,UAAM,EAAE,sBAAsB,QAAQ,SAAS,SAAS,OAAO,MAAK,IAAK;AACzE,QAAI;AACA,kBAAY,eAAe,oBAAoB,OAAO,SAAS;AACnE,QAAI;AACA,mBAAa,UAAU,MAAM;AACjC,QAAI;AACA,mBAAa,WAAW,OAAO;AACnC,QAAI;AACA,mBAAa,WAAW,OAAO;AACnC,QAAI;AACA,mBAAa,SAAS,KAAK;AAC/B,QAAI;AACA,mBAAa,SAAS,KAAK;AAAA,EACvC;AAKI,QAAM,gBAAgB,MAAM,EAAE,QAAQ,UAAU;AAChD,QAAM,gBAAgB,MAAM,EAAE,QAAQ,UAAU;AAChD,MAAI,kBAAkB,KAAK,kBAAkB,GAAG;AAC5C,iBAAa,SAAS,aAAa,KAAK,aAAa;AAAA,EAC7D;AACI,SAAO,aAAa;AACxB;ACpBA,MAAM,gBAAgB,CAAC,IAAI,KAAK,KAAK,GAAG;AACxC,MAAM,mBAAmB,EAAE,YAAY,SAAU;AAKjD,MAAM,kBAAkB;AACxB,IAAI,KAAK;AACT,SAAS,yBAAyB,KAAK,eAAe,QAAQ,uBAAuB;AACjF,QAAM,EAAE,aAAY,IAAK;AAEzB,MAAI,aAAa,GAAG,GAAG;AACnB,WAAO,GAAG,IAAI,aAAa,GAAG;AAC9B,kBAAc,eAAe,KAAK,CAAC;AACnC,QAAI,uBAAuB;AACvB,4BAAsB,GAAG,IAAI;AAAA,IACzC;AAAA,EACA;AACA;AACA,SAAS,uCAAuC,gBAAgB;AAC5D,iBAAe,4BAA4B;AAC3C,MAAI,eAAe,SAAS;AACxB;AACJ,QAAM,EAAE,kBAAkB,eAAe;AACzC,MAAI,CAAC;AACD;AACJ,QAAM,WAAW,qBAAqB,aAAa;AACnD,MAAI,OAAO,4BAA4B,UAAU,WAAW,GAAG;AAC3D,UAAM,EAAE,QAAA1B,SAAQ,SAAU,IAAG,eAAe;AAC5C,WAAO,+BAA+B,UAAU,aAAa,OAAO,EAAEA,WAAU,SAAS;AAAA,EACjG;AACI,QAAM,EAAE,OAAM,IAAK;AACnB,MAAI,UAAU,CAAC,OAAO,2BAA2B;AAC7C,2CAAuC,MAAM;AAAA,EACrD;AACA;AACA,SAAS,qBAAqB,EAAE,sBAAsB,eAAe,eAAe,mBAAmB,kBAAmB;AACtH,SAAO,MAAM,eAAe;AAAA,IACxB,YAAY,eAAe,CAAA,GAAI,SAAS,gBAAa,GAAM;AAIvD,WAAK,KAAK;AAIV,WAAK,cAAc;AAOnB,WAAK,WAAW,oBAAI,IAAK;AAKzB,WAAK,UAAU,CAAE;AAMjB,WAAK,kBAAkB;AACvB,WAAK,qBAAqB;AAO1B,WAAK,gBAAgB;AAKrB,WAAK,oBAAoB;AAKzB,WAAK,0BAA0B;AAK/B,WAAK,mBAAmB;AAIxB,WAAK,wBAAwB;AAC7B,WAAK,wBAAwB;AAK7B,WAAK,aAAa;AAIlB,WAAK,QAAQ;AAKb,WAAK,aAAa;AAIlB,WAAK,uBAAuB;AAO5B,WAAK,4BAA4B;AASjC,WAAK,YAAY,EAAE,GAAG,GAAG,GAAG,EAAG;AAI/B,WAAK,gBAAgB,oBAAI,IAAK;AAC9B,WAAK,kBAAkB;AAEvB,WAAK,kBAAkB;AACvB,WAAK,iBAAiB,MAAM,KAAK,OAAQ;AACzC,WAAK,4BAA4B;AACjC,WAAK,oBAAoB,MAAM;AAC3B,YAAI,KAAK,YAAY;AACjB,eAAK,aAAa;AAClB,eAAK,kBAAmB;AAAA,QAC5C;AAAA,MACa;AAMD,WAAK,mBAAmB,MAAM;AAC1B,aAAK,4BAA4B;AAWjC,aAAK,MAAM,QAAQ,mBAAmB;AACtC,aAAK,MAAM,QAAQ,kBAAkB;AACrC,aAAK,MAAM,QAAQ,cAAc;AACjC,aAAK,MAAM,QAAQ,eAAe;AAAA,MAIrC;AAID,WAAK,2BAA2B;AAChC,WAAK,eAAe;AACpB,WAAK,YAAY;AACjB,WAAK,oBAAoB;AAKzB,WAAK,cAAc,oBAAI,IAAK;AAC5B,WAAK,eAAe;AACpB,WAAK,OAAO,SAAS,OAAO,QAAQ,SAAS;AAC7C,WAAK,OAAO,SAAS,CAAC,GAAG,OAAO,MAAM,MAAM,IAAI,CAAE;AAClD,WAAK,SAAS;AACd,WAAK,QAAQ,SAAS,OAAO,QAAQ,IAAI;AACzC,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACvC,aAAK,KAAK,CAAC,EAAE,uBAAuB;AAAA,MACpD;AACY,UAAI,KAAK,SAAS;AACd,aAAK,QAAQ,IAAI,SAAU;AAAA,IAC3C;AAAA,IACQ,iBAAiB,MAAM,SAAS;AAC5B,UAAI,CAAC,KAAK,cAAc,IAAI,IAAI,GAAG;AAC/B,aAAK,cAAc,IAAI,MAAM,IAAI,oBAAmB,CAAE;AAAA,MACtE;AACY,aAAO,KAAK,cAAc,IAAI,IAAI,EAAE,IAAI,OAAO;AAAA,IAC3D;AAAA,IACQ,gBAAgB,SAAS,MAAM;AAC3B,YAAM,sBAAsB,KAAK,cAAc,IAAI,IAAI;AACvD,6BAAuB,oBAAoB,OAAO,GAAG,IAAI;AAAA,IACrE;AAAA,IACQ,aAAa,MAAM;AACf,aAAO,KAAK,cAAc,IAAI,IAAI;AAAA,IAC9C;AAAA;AAAA;AAAA;AAAA,IAIQ,MAAM,UAAU;AACZ,UAAI,KAAK;AACL;AACJ,WAAK,QAAQ,aAAa,QAAQ,KAAK,CAAC,gBAAgB,QAAQ;AAChE,WAAK,WAAW;AAChB,YAAM,EAAE,UAAU,QAAAA,SAAQ,cAAe,IAAG,KAAK;AACjD,UAAI,iBAAiB,CAAC,cAAc,SAAS;AACzC,sBAAc,MAAM,QAAQ;AAAA,MAC5C;AACY,WAAK,KAAK,MAAM,IAAI,IAAI;AACxB,WAAK,UAAU,KAAK,OAAO,SAAS,IAAI,IAAI;AAC5C,UAAI,KAAK,KAAK,oBAAoBA,WAAU,WAAW;AACnD,aAAK,gBAAgB;AAAA,MACrC;AACY,UAAI,sBAAsB;AACtB,YAAI;AACJ,cAAM,sBAAsB,MAAO,KAAK,KAAK,wBAAwB;AACrE,6BAAqB,UAAU,MAAM;AACjC,eAAK,KAAK,wBAAwB;AAClC,yBAAe,YAAa;AAC5B,wBAAc,MAAM,qBAAqB,GAAG;AAC5C,cAAI,sBAAsB,wBAAwB;AAC9C,kCAAsB,yBAAyB;AAC/C,iBAAK,MAAM,QAAQ,eAAe;AAAA,UAC1D;AAAA,QACA,CAAiB;AAAA,MACjB;AACY,UAAI,UAAU;AACV,aAAK,KAAK,mBAAmB,UAAU,IAAI;AAAA,MAC3D;AAEY,UAAI,KAAK,QAAQ,YAAY,SACzB,kBACC,YAAYA,UAAS;AACtB,aAAK,iBAAiB,aAAa,CAAC,EAAE,OAAO,kBAAkB,0BAA0B,QAAQ,gBAAiB;AAC9G,cAAI,KAAK,0BAA0B;AAC/B,iBAAK,SAAS;AACd,iBAAK,iBAAiB;AACtB;AAAA,UACxB;AAEoB,gBAAM,mBAAmB,KAAK,QAAQ,cAClC,cAAc,qBAAsB,KACpC;AACJ,gBAAM,EAAE,wBAAwB,8BAA+B,cAAc,SAAU;AAKvF,gBAAM,mBAAmB,CAAC,KAAK,gBAC3B,CAAC,iBAAiB,KAAK,cAAc,SAAS;AAYlD,gBAAM,+BAA+B,CAAC,oBAAoB;AAC1D,cAAI,KAAK,QAAQ,cACb,KAAK,cACL,gCACC,qBACI,oBAAoB,CAAC,KAAK,mBAAoB;AACnD,gBAAI,KAAK,YAAY;AACjB,mBAAK,eAAe,KAAK;AACzB,mBAAK,aAAa,eAAe;AAAA,YAC7D;AACwB,kBAAM,mBAAmB;AAAA,cACrB,GAAG,mBAAmB,kBAAkB,QAAQ;AAAA,cAChD,QAAQ;AAAA,cACR,YAAY;AAAA,YACf;AACD,gBAAI,cAAc,sBACd,KAAK,QAAQ,YAAY;AACzB,+BAAiB,QAAQ;AACzB,+BAAiB,OAAO;AAAA,YACpD;AACwB,iBAAK,eAAe,gBAAgB;AAKpC,iBAAK,mBAAmB,OAAO,4BAA4B;AAAA,UACnF,OACyB;AAMD,gBAAI,CAAC,kBAAkB;AACnB,8BAAgB,IAAI;AAAA,YAChD;AACwB,gBAAI,KAAK,OAAM,KAAM,KAAK,QAAQ,gBAAgB;AAC9C,mBAAK,QAAQ,eAAgB;AAAA,YACzD;AAAA,UACA;AACoB,eAAK,eAAe;AAAA,QACxC,CAAiB;AAAA,MACjB;AAAA,IACA;AAAA,IACQ,UAAU;AACN,WAAK,QAAQ,YAAY,KAAK,WAAY;AAC1C,WAAK,KAAK,MAAM,OAAO,IAAI;AAC3B,YAAM,QAAQ,KAAK,SAAU;AAC7B,eAAS,MAAM,OAAO,IAAI;AAC1B,WAAK,UAAU,KAAK,OAAO,SAAS,OAAO,IAAI;AAC/C,WAAK,WAAW;AAChB,WAAK,cAAc,MAAO;AAC1B,kBAAY,KAAK,gBAAgB;AAAA,IAC7C;AAAA;AAAA,IAEQ,cAAc;AACV,WAAK,wBAAwB;AAAA,IACzC;AAAA,IACQ,gBAAgB;AACZ,WAAK,wBAAwB;AAAA,IACzC;AAAA,IACQ,kBAAkB;AACd,aAAO,KAAK,yBAAyB,KAAK;AAAA,IACtD;AAAA,IACQ,yBAAyB;AACrB,aAAQ,KAAK,sBACR,KAAK,UAAU,KAAK,OAAO,uBAAsB,KAClD;AAAA,IAChB;AAAA;AAAA,IAEQ,cAAc;AACV,UAAI,KAAK,gBAAiB;AACtB;AACJ,WAAK,aAAa;AAClB,WAAK,SAAS,KAAK,MAAM,QAAQ,oBAAoB;AACrD,WAAK;AAAA,IACjB;AAAA,IACQ,uBAAuB;AACnB,YAAM,EAAE,kBAAkB,KAAK;AAC/B,aAAO,iBAAiB,cAAc,SAAQ,EAAG;AAAA,IAC7D;AAAA,IACQ,WAAW,wBAAwB,MAAM;AACrC,WAAK,KAAK,kBAAkB;AAC5B,UAAI,KAAK,KAAK,mBAAmB;AAC7B,aAAK,QAAQ,kBAAkB,KAAK,QAAQ,eAAgB;AAC5D;AAAA,MAChB;AAaY,UAAI,OAAO,kCACP,CAAC,KAAK,2BAA2B;AACjC,+CAAuC,IAAI;AAAA,MAC3D;AACY,OAAC,KAAK,KAAK,cAAc,KAAK,KAAK,YAAa;AAChD,UAAI,KAAK;AACL;AACJ,WAAK,gBAAgB;AACrB,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACvC,cAAM,OAAO,KAAK,KAAK,CAAC;AACxB,aAAK,uBAAuB;AAC5B,aAAK,aAAa,UAAU;AAC5B,YAAI,KAAK,QAAQ,YAAY;AACzB,eAAK,WAAW,KAAK;AAAA,QACzC;AAAA,MACA;AACY,YAAM,EAAE,UAAU,QAAAA,QAAQ,IAAG,KAAK;AAClC,UAAI,aAAa,UAAa,CAACA;AAC3B;AACJ,YAAM,oBAAoB,KAAK,qBAAsB;AACrD,WAAK,6BAA6B,oBAC5B,kBAAkB,KAAK,cAAc,EAAE,IACvC;AACN,WAAK,eAAgB;AACrB,+BAAyB,KAAK,gBAAgB,YAAY;AAAA,IACtE;AAAA,IACQ,SAAS;AACL,WAAK,kBAAkB;AACvB,YAAM,mBAAmB,KAAK,gBAAiB;AAI/C,UAAI,kBAAkB;AAClB,aAAK,cAAe;AACpB,aAAK,kBAAmB;AACxB,aAAK,MAAM,QAAQ,iBAAiB;AACpC;AAAA,MAChB;AACY,UAAI,CAAC,KAAK,YAAY;AAClB,aAAK,MAAM,QAAQ,kBAAkB;AAAA,MACrD;AACY,WAAK,aAAa;AAIlB,WAAK,MAAM,QAAQ,mBAAmB;AAKtC,WAAK,MAAM,QAAQ,YAAY;AAK/B,WAAK,MAAM,QAAQ,kBAAkB;AACrC,WAAK,kBAAmB;AAMxB,YAAM,MAAM,KAAK,IAAK;AACtB,gBAAU,QAAQ,MAAM,GAAG,MAAO,IAAI,MAAM,UAAU,SAAS;AAC/D,gBAAU,YAAY;AACtB,gBAAU,eAAe;AACzB,iBAAW,OAAO,QAAQ,SAAS;AACnC,iBAAW,UAAU,QAAQ,SAAS;AACtC,iBAAW,OAAO,QAAQ,SAAS;AACnC,gBAAU,eAAe;AAAA,IACrC;AAAA,IACQ,YAAY;AACR,UAAI,CAAC,KAAK,iBAAiB;AACvB,aAAK,kBAAkB;AACvB,kBAAU,KAAK,KAAK,cAAc;AAAA,MAClD;AAAA,IACA;AAAA,IACQ,oBAAoB;AAChB,WAAK,MAAM,QAAQ,aAAa;AAChC,WAAK,YAAY,QAAQ,mBAAmB;AAAA,IACxD;AAAA,IACQ,2BAA2B;AACvB,UAAI,CAAC,KAAK,2BAA2B;AACjC,aAAK,4BAA4B;AACjC,cAAM,UAAU,KAAK,kBAAkB,OAAO,IAAI;AAAA,MAClE;AAAA,IACA;AAAA,IACQ,4BAA4B;AAMxB,YAAM,WAAW,MAAM;AACnB,YAAI,KAAK,eAAe;AACpB,eAAK,KAAK,UAAW;AAAA,QACzC,OACqB;AACD,eAAK,KAAK,kBAAmB;AAAA,QACjD;AAAA,MACA,CAAa;AAAA,IACb;AAAA;AAAA;AAAA;AAAA,IAIQ,iBAAiB;AACb,UAAI,KAAK,YAAY,CAAC,KAAK;AACvB;AACJ,WAAK,WAAW,KAAK,QAAS;AAC9B,UAAI,KAAK,YACL,CAAC,WAAW,KAAK,SAAS,YAAY,CAAC,KACvC,CAAC,WAAW,KAAK,SAAS,YAAY,CAAC,GAAG;AAC1C,aAAK,WAAW;AAAA,MAChC;AAAA,IACA;AAAA,IACQ,eAAe;AACX,UAAI,CAAC,KAAK;AACN;AAEJ,WAAK,aAAc;AACnB,UAAI,EAAE,KAAK,QAAQ,uBAAuB,KAAK,OAAM,MACjD,CAAC,KAAK,eAAe;AACrB;AAAA,MAChB;AAQY,UAAI,KAAK,cAAc,CAAC,KAAK,WAAW,UAAU;AAC9C,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACvC,gBAAM,OAAO,KAAK,KAAK,CAAC;AACxB,eAAK,aAAc;AAAA,QACvC;AAAA,MACA;AACY,YAAM,aAAa,KAAK;AACxB,WAAK,SAAS,KAAK,QAAQ,KAAK;AAChC,WAAK,kBAAkB,UAAW;AAClC,WAAK,gBAAgB;AACrB,WAAK,kBAAkB;AACvB,WAAK,gBAAgB,WAAW,KAAK,OAAO,SAAS;AACrD,YAAM,EAAE,kBAAkB,KAAK;AAC/B,uBACI,cAAc,OAAO,iBAAiB,KAAK,OAAO,WAAW,aAAa,WAAW,YAAY,MAAS;AAAA,IAC1H;AAAA,IACQ,aAAa,QAAQ,WAAW;AAC5B,UAAI,mBAAmB,QAAQ,KAAK,QAAQ,gBAAgB,KAAK,QAAQ;AACzE,UAAI,KAAK,UACL,KAAK,OAAO,gBAAgB,KAAK,KAAK,eACtC,KAAK,OAAO,UAAU,OAAO;AAC7B,2BAAmB;AAAA,MACnC;AACY,UAAI,oBAAoB,KAAK,UAAU;AACnC,cAAM,SAAS,kBAAkB,KAAK,QAAQ;AAC9C,aAAK,SAAS;AAAA,UACV,aAAa,KAAK,KAAK;AAAA,UACvB;AAAA,UACA;AAAA,UACA,QAAQ,cAAc,KAAK,QAAQ;AAAA,UACnC,SAAS,KAAK,SAAS,KAAK,OAAO,SAAS;AAAA,QAC/C;AAAA,MACjB;AAAA,IACA;AAAA,IACQ,iBAAiB;AACb,UAAI,CAAC;AACD;AACJ,YAAM,mBAAmB,KAAK,iBAC1B,KAAK,wBACL,KAAK,QAAQ;AACjB,YAAM,gBAAgB,KAAK,mBAAmB,CAAC,YAAY,KAAK,eAAe;AAC/E,YAAM,oBAAoB,KAAK,qBAAsB;AACrD,YAAM,yBAAyB,oBACzB,kBAAkB,KAAK,cAAc,EAAE,IACvC;AACN,YAAM,8BAA8B,2BAA2B,KAAK;AACpE,UAAI,oBACA,KAAK,aACJ,iBACG,aAAa,KAAK,YAAY,KAC9B,8BAA8B;AAClC,uBAAe,KAAK,UAAU,sBAAsB;AACpD,aAAK,uBAAuB;AAC5B,aAAK,eAAgB;AAAA,MACrC;AAAA,IACA;AAAA,IACQ,QAAQ,kBAAkB,MAAM;AAC5B,YAAM,UAAU,KAAK,eAAgB;AACrC,UAAI,YAAY,KAAK,oBAAoB,OAAO;AAMhD,UAAI,iBAAiB;AACjB,oBAAY,KAAK,gBAAgB,SAAS;AAAA,MAC1D;AACY,eAAS,SAAS;AAClB,aAAO;AAAA,QACH,aAAa,KAAK,KAAK;AAAA,QACvB,aAAa;AAAA,QACb;AAAA,QACA,cAAc,CAAE;AAAA,QAChB,QAAQ,KAAK;AAAA,MAChB;AAAA,IACb;AAAA,IACQ,iBAAiB;AACb,YAAM,EAAE,kBAAkB,KAAK;AAC/B,UAAI,CAAC;AACD,eAAO,UAAW;AACtB,YAAM,MAAM,cAAc,mBAAoB;AAC9C,YAAM,kBAAkB,KAAK,QAAQ,WAAW,KAAK,KAAK,KAAK,sBAAsB;AACrF,UAAI,CAAC,iBAAiB;AAElB,cAAM,EAAE,WAAW,KAAK;AACxB,YAAI,QAAQ;AACR,wBAAc,IAAI,GAAG,OAAO,OAAO,CAAC;AACpC,wBAAc,IAAI,GAAG,OAAO,OAAO,CAAC;AAAA,QACxD;AAAA,MACA;AACY,aAAO;AAAA,IACnB;AAAA,IACQ,oBAAoB,KAAK;AACrB,YAAM,mBAAmB,UAAW;AACpC,kBAAY,kBAAkB,GAAG;AACjC,UAAI,KAAK,QAAQ,SAAS;AACtB,eAAO;AAAA,MACvB;AAKY,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACvC,cAAM,OAAO,KAAK,KAAK,CAAC;AACxB,cAAM,EAAE,QAAQ,QAAO,IAAK;AAC5B,YAAI,SAAS,KAAK,QAAQ,UAAU,QAAQ,cAAc;AAKtD,cAAI,OAAO,SAAS;AAChB,wBAAY,kBAAkB,GAAG;AAAA,UACzD;AACoB,wBAAc,iBAAiB,GAAG,OAAO,OAAO,CAAC;AACjD,wBAAc,iBAAiB,GAAG,OAAO,OAAO,CAAC;AAAA,QACrE;AAAA,MACA;AACY,aAAO;AAAA,IACnB;AAAA,IACQ,eAAe,KAAK,gBAAgB,OAAO;AACvC,YAAM,iBAAiB,UAAW;AAClC,kBAAY,gBAAgB,GAAG;AAC/B,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACvC,cAAM,OAAO,KAAK,KAAK,CAAC;AACxB,YAAI,CAAC,iBACD,KAAK,QAAQ,gBACb,KAAK,UACL,SAAS,KAAK,MAAM;AACpB,uBAAa,gBAAgB;AAAA,YACzB,GAAG,CAAC,KAAK,OAAO,OAAO;AAAA,YACvB,GAAG,CAAC,KAAK,OAAO,OAAO;AAAA,UAC/C,CAAqB;AAAA,QACrB;AACgB,YAAI,CAAC,aAAa,KAAK,YAAY;AAC/B;AACJ,qBAAa,gBAAgB,KAAK,YAAY;AAAA,MAC9D;AACY,UAAI,aAAa,KAAK,YAAY,GAAG;AACjC,qBAAa,gBAAgB,KAAK,YAAY;AAAA,MAC9D;AACY,aAAO;AAAA,IACnB;AAAA,IACQ,gBAAgB,KAAK;AACjB,YAAM,sBAAsB,UAAW;AACvC,kBAAY,qBAAqB,GAAG;AACpC,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACvC,cAAM,OAAO,KAAK,KAAK,CAAC;AACxB,YAAI,CAAC,KAAK;AACN;AACJ,YAAI,CAAC,aAAa,KAAK,YAAY;AAC/B;AACJ,iBAAS,KAAK,YAAY,KAAK,KAAK,eAAgB;AACpD,cAAM,YAAY,UAAW;AAC7B,cAAM,UAAU,KAAK,eAAgB;AACrC,oBAAY,WAAW,OAAO;AAC9B,4BAAoB,qBAAqB,KAAK,cAAc,KAAK,WAAW,KAAK,SAAS,YAAY,QAAW,SAAS;AAAA,MAC1I;AACY,UAAI,aAAa,KAAK,YAAY,GAAG;AACjC,4BAAoB,qBAAqB,KAAK,YAAY;AAAA,MAC1E;AACY,aAAO;AAAA,IACnB;AAAA,IACQ,eAAe,OAAO;AAClB,WAAK,cAAc;AACnB,WAAK,KAAK,yBAA0B;AACpC,WAAK,oBAAoB;AAAA,IACrC;AAAA,IACQ,WAAW,SAAS;AAChB,WAAK,UAAU;AAAA,QACX,GAAG,KAAK;AAAA,QACR,GAAG;AAAA,QACH,WAAW,QAAQ,cAAc,SAAY,QAAQ,YAAY;AAAA,MACpE;AAAA,IACb;AAAA,IACQ,oBAAoB;AAChB,WAAK,SAAS;AACd,WAAK,SAAS;AACd,WAAK,WAAW;AAChB,WAAK,6BAA6B;AAClC,WAAK,cAAc;AACnB,WAAK,SAAS;AACd,WAAK,gBAAgB;AAAA,IACjC;AAAA,IACQ,qCAAqC;AACjC,UAAI,CAAC,KAAK;AACN;AAOJ,UAAI,KAAK,eAAe,6BACpB,UAAU,WAAW;AACrB,aAAK,eAAe,mBAAmB,IAAI;AAAA,MAC3D;AAAA,IACA;AAAA,IACQ,mBAAmB,qBAAqB,OAAO;AAM3C,YAAM,OAAO,KAAK,QAAS;AAC3B,WAAK,sBAAsB,KAAK,oBAAoB,KAAK;AACzD,WAAK,qBAAqB,KAAK,mBAAmB,KAAK;AACvD,WAAK,4BAA4B,KAAK,0BAA0B,KAAK;AACrE,YAAM,WAAW,QAAQ,KAAK,YAAY,KAAK,SAAS;AAKxD,YAAM,UAAU,EAAE,sBACb,YAAY,KAAK,2BAClB,KAAK,qBACL,KAAK,QAAQ,qBACb,KAAK,kCACL,KAAK,KAAK;AACd,UAAI;AACA;AACJ,YAAM,EAAE,QAAAA,SAAQ,SAAU,IAAG,KAAK;AAIlC,UAAI,CAAC,KAAK,UAAU,EAAEA,WAAU;AAC5B;AACJ,WAAK,2BAA2B,UAAU;AAM1C,UAAI,CAAC,KAAK,eAAe,CAAC,KAAK,gBAAgB;AAC3C,cAAM,iBAAiB,KAAK,2BAA4B;AACxD,YAAI,kBACA,eAAe,UACf,KAAK,sBAAsB,GAAG;AAC9B,eAAK,iBAAiB;AACtB,eAAK,mCAAoC;AACzC,eAAK,iBAAiB,UAAW;AACjC,eAAK,uBAAuB,UAAW;AACvC,+BAAqB,KAAK,sBAAsB,KAAK,OAAO,WAAW,eAAe,OAAO,SAAS;AACtG,sBAAY,KAAK,gBAAgB,KAAK,oBAAoB;AAAA,QAC9E,OACqB;AACD,eAAK,iBAAiB,KAAK,iBAAiB;AAAA,QAChE;AAAA,MACA;AAKY,UAAI,CAAC,KAAK,kBAAkB,CAAC,KAAK;AAC9B;AAIJ,UAAI,CAAC,KAAK,QAAQ;AACd,aAAK,SAAS,UAAW;AACzB,aAAK,uBAAuB,UAAW;AAAA,MACvD;AAIY,UAAI,KAAK,kBACL,KAAK,wBACL,KAAK,kBACL,KAAK,eAAe,QAAQ;AAC5B,aAAK,mCAAoC;AACzC,wBAAgB,KAAK,QAAQ,KAAK,gBAAgB,KAAK,eAAe,MAAM;AAAA,MAI5F,WACqB,KAAK,aAAa;AACvB,YAAI,QAAQ,KAAK,YAAY,GAAG;AAE5B,eAAK,SAAS,KAAK,eAAe,KAAK,OAAO,SAAS;AAAA,QAC3E,OACqB;AACD,sBAAY,KAAK,QAAQ,KAAK,OAAO,SAAS;AAAA,QAClE;AACgB,sBAAc,KAAK,QAAQ,KAAK,WAAW;AAAA,MAC3D,OACiB;AAID,oBAAY,KAAK,QAAQ,KAAK,OAAO,SAAS;AAAA,MAC9D;AAIY,UAAI,KAAK,gCAAgC;AACrC,aAAK,iCAAiC;AACtC,cAAM,iBAAiB,KAAK,2BAA4B;AACxD,YAAI,kBACA,QAAQ,eAAe,YAAY,MAC/B,QAAQ,KAAK,YAAY,KAC7B,CAAC,eAAe,QAAQ,gBACxB,eAAe,UACf,KAAK,sBAAsB,GAAG;AAC9B,eAAK,iBAAiB;AACtB,eAAK,mCAAoC;AACzC,eAAK,iBAAiB,UAAW;AACjC,eAAK,uBAAuB,UAAW;AACvC,+BAAqB,KAAK,sBAAsB,KAAK,QAAQ,eAAe,MAAM;AAClF,sBAAY,KAAK,gBAAgB,KAAK,oBAAoB;AAAA,QAC9E,OACqB;AACD,eAAK,iBAAiB,KAAK,iBAAiB;AAAA,QAChE;AAAA,MACA;AAAA,IAOA;AAAA,IACQ,6BAA6B;AACzB,UAAI,CAAC,KAAK,UACN,SAAS,KAAK,OAAO,YAAY,KACjC,eAAe,KAAK,OAAO,YAAY,GAAG;AAC1C,eAAO;AAAA,MACvB;AACY,UAAI,KAAK,OAAO,gBAAgB;AAC5B,eAAO,KAAK;AAAA,MAC5B,OACiB;AACD,eAAO,KAAK,OAAO,2BAA4B;AAAA,MAC/D;AAAA,IACA;AAAA,IACQ,eAAe;AACX,aAAO,SAAS,KAAK,kBACjB,KAAK,eACL,KAAK,QAAQ,eACb,KAAK,MAAM;AAAA,IAC3B;AAAA,IACQ,iBAAiB;AACb,YAAM,OAAO,KAAK,QAAS;AAC3B,YAAM,WAAW,QAAQ,KAAK,YAAY,KAAK,SAAS;AACxD,UAAI,UAAU;AAKd,UAAI,KAAK,qBAAqB,KAAK,QAAQ,mBAAmB;AAC1D,kBAAU;AAAA,MAC1B;AAKY,UAAI,aACC,KAAK,2BAA2B,KAAK,mBAAmB;AACzD,kBAAU;AAAA,MAC1B;AAKY,UAAI,KAAK,6BAA6B,UAAU,WAAW;AACvD,kBAAU;AAAA,MAC1B;AACY,UAAI;AACA;AACJ,YAAM,EAAE,QAAAA,SAAQ,SAAU,IAAG,KAAK;AAKlC,WAAK,kBAAkB,QAAS,KAAK,UAAU,KAAK,OAAO,mBACvD,KAAK,oBACL,KAAK,gBAAgB;AACzB,UAAI,CAAC,KAAK,iBAAiB;AACvB,aAAK,cAAc,KAAK,iBAAiB;AAAA,MACzD;AACY,UAAI,CAAC,KAAK,UAAU,EAAEA,WAAU;AAC5B;AAKJ,kBAAY,KAAK,iBAAiB,KAAK,OAAO,SAAS;AAIvD,YAAM,iBAAiB,KAAK,UAAU;AACtC,YAAM,iBAAiB,KAAK,UAAU;AAKtC,sBAAgB,KAAK,iBAAiB,KAAK,WAAW,KAAK,MAAM,QAAQ;AAKzE,UAAI,KAAK,UACL,CAAC,KAAK,WACL,KAAK,UAAU,MAAM,KAAK,KAAK,UAAU,MAAM,IAAI;AACpD,aAAK,SAAS,KAAK,OAAO;AAC1B,aAAK,uBAAuB,UAAW;AAAA,MACvD;AACY,YAAM,EAAE,OAAM,IAAK;AACnB,UAAI,CAAC,QAAQ;AAMT,YAAI,KAAK,qBAAqB;AAC1B,eAAK,uBAAwB;AAC7B,eAAK,eAAgB;AAAA,QACzC;AACgB;AAAA,MAChB;AACY,UAAI,CAAC,KAAK,mBAAmB,CAAC,KAAK,qBAAqB;AACpD,aAAK,uBAAwB;AAAA,MAC7C,OACiB;AACD,0BAAkB,KAAK,oBAAoB,GAAG,KAAK,gBAAgB,CAAC;AACpE,0BAAkB,KAAK,oBAAoB,GAAG,KAAK,gBAAgB,CAAC;AAAA,MACpF;AAUY,mBAAa,KAAK,iBAAiB,KAAK,iBAAiB,QAAQ,KAAK,YAAY;AAClF,UAAI,KAAK,UAAU,MAAM,kBACrB,KAAK,UAAU,MAAM,kBACrB,CAAC,gBAAgB,KAAK,gBAAgB,GAAG,KAAK,oBAAoB,CAAC,KACnE,CAAC,gBAAgB,KAAK,gBAAgB,GAAG,KAAK,oBAAoB,CAAC,GAAG;AACtE,aAAK,eAAe;AACpB,aAAK,eAAgB;AACrB,aAAK,gBAAgB,oBAAoB,MAAM;AAAA,MAC/D;AAAA,IAOA;AAAA,IACQ,OAAO;AACH,WAAK,YAAY;AAAA,IAE7B;AAAA,IACQ,OAAO;AACH,WAAK,YAAY;AAAA,IAE7B;AAAA,IACQ,eAAe,YAAY,MAAM;AAC7B,WAAK,QAAQ,eAAe,eAAgB;AAC5C,UAAI,WAAW;AACX,cAAM,QAAQ,KAAK,SAAU;AAC7B,iBAAS,MAAM,eAAgB;AAAA,MAC/C;AACY,UAAI,KAAK,gBAAgB,CAAC,KAAK,aAAa,UAAU;AAClD,aAAK,eAAe;AAAA,MACpC;AAAA,IACA;AAAA,IACQ,yBAAyB;AACrB,WAAK,sBAAsB,YAAa;AACxC,WAAK,kBAAkB,YAAa;AACpC,WAAK,+BAA+B,YAAa;AAAA,IAC7D;AAAA,IACQ,mBAAmB,OAAO,+BAA+B,OAAO;AAC5D,YAAM,WAAW,KAAK;AACtB,YAAM,uBAAuB,WAAW,SAAS,eAAe,CAAE;AAClE,YAAM,cAAc,EAAE,GAAG,KAAK,aAAc;AAC5C,YAAM,cAAc,YAAa;AACjC,UAAI,CAAC,KAAK,kBACN,CAAC,KAAK,eAAe,QAAQ,YAAY;AACzC,aAAK,iBAAiB,KAAK,uBAAuB;AAAA,MAClE;AACY,WAAK,iCAAiC,CAAC;AACvC,YAAM,iBAAiB,UAAW;AAClC,YAAM,iBAAiB,WAAW,SAAS,SAAS;AACpD,YAAM,eAAe,KAAK,SAAS,KAAK,OAAO,SAAS;AACxD,YAAM,0BAA0B,mBAAmB;AACnD,YAAM,QAAQ,KAAK,SAAU;AAC7B,YAAM,eAAe,CAAC,SAAS,MAAM,QAAQ,UAAU;AACvD,YAAM,yBAAyB,QAAQ,2BACnC,CAAC,gBACD,KAAK,QAAQ,cAAc,QAC3B,CAAC,KAAK,KAAK,KAAK,mBAAmB,CAAC;AACxC,WAAK,oBAAoB;AACzB,UAAI;AACJ,WAAK,iBAAiB,CAAC,WAAW;AAC9B,cAAM0B,YAAW,SAAS;AAC1B,qBAAa,YAAY,GAAG,MAAM,GAAGA,SAAQ;AAC7C,qBAAa,YAAY,GAAG,MAAM,GAAGA,SAAQ;AAC7C,aAAK,eAAe,WAAW;AAC/B,YAAI,KAAK,kBACL,KAAK,wBACL,KAAK,UACL,KAAK,kBACL,KAAK,eAAe,QAAQ;AAC5B,+BAAqB,gBAAgB,KAAK,OAAO,WAAW,KAAK,eAAe,OAAO,SAAS;AAChG,iBAAO,KAAK,gBAAgB,KAAK,sBAAsB,gBAAgBA,SAAQ;AAK/E,cAAI,sBACA,UAAU,KAAK,gBAAgB,kBAAkB,GAAG;AACpD,iBAAK,oBAAoB;AAAA,UACjD;AACoB,cAAI,CAAC;AACD,iCAAqB,UAAW;AACpC,sBAAY,oBAAoB,KAAK,cAAc;AAAA,QACvE;AACgB,YAAI,yBAAyB;AACzB,eAAK,kBAAkB;AACvB,oBAAU,aAAa,sBAAsB,KAAK,cAAcA,WAAU,wBAAwB,YAAY;AAAA,QAClI;AACgB,aAAK,KAAK,yBAA0B;AACpC,aAAK,eAAgB;AACrB,aAAK,oBAAoBA;AAAA,MAC5B;AACD,WAAK,eAAe,KAAK,QAAQ,aAAa,MAAO,CAAC;AAAA,IAClE;AAAA,IACQ,eAAe,SAAS;AACpB,WAAK,gBAAgB,gBAAgB;AACrC,WAAK,kBAAkB,KAAM;AAC7B,WAAK,cAAc,kBAAkB,KAAM;AAC3C,UAAI,KAAK,kBAAkB;AACvB,oBAAY,KAAK,gBAAgB;AACjC,aAAK,mBAAmB;AAAA,MACxC;AAMY,WAAK,mBAAmB,MAAM,OAAO,MAAM;AACvC,8BAAsB,yBAAyB;AAE/C,aAAK,gBAAgB,KAAK,cAAc,YAAY,CAAC;AACrD,aAAK,mBAAmB,mBAAmB,KAAK,aAAa,CAAC,GAAG,GAAI,GAAG;AAAA,UACpE,GAAG;AAAA,UACH,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,UAAU,CAAC,WAAW;AAClB,iBAAK,eAAe,MAAM;AAC1B,oBAAQ,YAAY,QAAQ,SAAS,MAAM;AAAA,UAC9C;AAAA,UACD,QAAQ,MAAM;AAAA,UAEb;AAAA,UACD,YAAY,MAAM;AAEd,oBAAQ,cAAc,QAAQ,WAAY;AAC1C,iBAAK,kBAAmB;AAAA,UAC3B;AAAA,QACrB,CAAiB;AACD,YAAI,KAAK,cAAc;AACnB,eAAK,aAAa,mBAAmB,KAAK;AAAA,QAC9D;AACgB,aAAK,mBAAmB;AAAA,MACxC,CAAa;AAAA,IACb;AAAA,IACQ,oBAAoB;AAChB,UAAI,KAAK,cAAc;AACnB,aAAK,aAAa,mBAAmB;AACrC,aAAK,aAAa,kBAAkB;AAAA,MACpD;AACY,YAAM,QAAQ,KAAK,SAAU;AAC7B,eAAS,MAAM,sBAAuB;AACtC,WAAK,eACD,KAAK,mBACD,KAAK,kBACD;AACZ,WAAK,gBAAgB,mBAAmB;AAAA,IACpD;AAAA,IACQ,kBAAkB;AACd,UAAI,KAAK,kBAAkB;AACvB,aAAK,kBAAkB,KAAK,eAAe,eAAe;AAC1D,aAAK,iBAAiB,KAAM;AAAA,MAC5C;AACY,WAAK,kBAAmB;AAAA,IACpC;AAAA,IACQ,0BAA0B;AACtB,YAAM,OAAO,KAAK,QAAS;AAC3B,UAAI,EAAE,sBAAsB,QAAQ,QAAA1B,SAAQ,aAAc,IAAG;AAC7D,UAAI,CAAC,wBAAwB,CAAC,UAAU,CAACA;AACrC;AAMJ,UAAI,SAAS,QACT,KAAK,UACLA,WACA,0BAA0B,KAAK,QAAQ,eAAe,KAAK,OAAO,WAAWA,QAAO,SAAS,GAAG;AAChG,iBAAS,KAAK,UAAU,UAAW;AACnC,cAAM,UAAU,WAAW,KAAK,OAAO,UAAU,CAAC;AAClD,eAAO,EAAE,MAAM,KAAK,OAAO,EAAE;AAC7B,eAAO,EAAE,MAAM,OAAO,EAAE,MAAM;AAC9B,cAAM,UAAU,WAAW,KAAK,OAAO,UAAU,CAAC;AAClD,eAAO,EAAE,MAAM,KAAK,OAAO,EAAE;AAC7B,eAAO,EAAE,MAAM,OAAO,EAAE,MAAM;AAAA,MAC9C;AACY,kBAAY,sBAAsB,MAAM;AAMxC,mBAAa,sBAAsB,YAAY;AAO/C,mBAAa,KAAK,8BAA8B,KAAK,iBAAiB,sBAAsB,YAAY;AAAA,IACpH;AAAA,IACQ,mBAAmB,UAAU,MAAM;AAC/B,UAAI,CAAC,KAAK,YAAY,IAAI,QAAQ,GAAG;AACjC,aAAK,YAAY,IAAI,UAAU,IAAI,UAAS,CAAE;AAAA,MAC9D;AACY,YAAM,QAAQ,KAAK,YAAY,IAAI,QAAQ;AAC3C,YAAM,IAAI,IAAI;AACd,YAAM,SAAS,KAAK,QAAQ;AAC5B,WAAK,QAAQ;AAAA,QACT,YAAY,SAAS,OAAO,aAAa;AAAA,QACzC,uBAAuB,UAAU,OAAO,8BAClC,OAAO,4BAA4B,IAAI,IACvC;AAAA,MACtB,CAAa;AAAA,IACb;AAAA,IACQ,SAAS;AACL,YAAM,QAAQ,KAAK,SAAU;AAC7B,aAAO,QAAQ,MAAM,SAAS,OAAO;AAAA,IACjD;AAAA,IACQ,UAAU;AACN,YAAM,EAAE,aAAa,KAAK;AAC1B,aAAO,WAAW,KAAK,SAAU,GAAE,QAAQ,OAAO;AAAA,IAC9D;AAAA,IACQ,cAAc;AACV,YAAM,EAAE,aAAa,KAAK;AAC1B,aAAO,WAAW,KAAK,SAAU,GAAE,WAAW;AAAA,IAC1D;AAAA,IACQ,WAAW;AACP,YAAM,EAAE,aAAa,KAAK;AAC1B,UAAI;AACA,eAAO,KAAK,KAAK,YAAY,IAAI,QAAQ;AAAA,IACzD;AAAA,IACQ,QAAQ,EAAE,YAAY,YAAY,sBAAqB,IAAM,CAAA,GAAI;AAC7D,YAAM,QAAQ,KAAK,SAAU;AAC7B,UAAI;AACA,cAAM,QAAQ,MAAM,qBAAqB;AAC7C,UAAI,YAAY;AACZ,aAAK,kBAAkB;AACvB,aAAK,aAAa;AAAA,MAClC;AACY,UAAI;AACA,aAAK,WAAW,EAAE,YAAY;AAAA,IAC9C;AAAA,IACQ,WAAW;AACP,YAAM,QAAQ,KAAK,SAAU;AAC7B,UAAI,OAAO;AACP,eAAO,MAAM,SAAS,IAAI;AAAA,MAC1C,OACiB;AACD,eAAO;AAAA,MACvB;AAAA,IACA;AAAA,IACQ,uBAAuB;AACnB,YAAM,EAAE,kBAAkB,KAAK;AAC/B,UAAI,CAAC;AACD;AAEJ,UAAI,yBAAyB;AAK7B,YAAM,EAAE,aAAY,IAAK;AACzB,UAAI,aAAa,KACb,aAAa,UACb,aAAa,WACb,aAAa,WACb,aAAa,WACb,aAAa,SACb,aAAa,OAAO;AACpB,iCAAyB;AAAA,MACzC;AAEY,UAAI,CAAC;AACD;AACJ,YAAM,cAAc,CAAE;AACtB,UAAI,aAAa,GAAG;AAChB,iCAAyB,KAAK,eAAe,aAAa,KAAK,eAAe;AAAA,MAC9F;AAEY,eAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC3C,iCAAyB,SAAS,cAAc,CAAC,CAAC,IAAI,eAAe,aAAa,KAAK,eAAe;AACtG,iCAAyB,OAAO,cAAc,CAAC,CAAC,IAAI,eAAe,aAAa,KAAK,eAAe;AAAA,MACpH;AAGY,oBAAc,OAAQ;AAEtB,iBAAW,OAAO,aAAa;AAC3B,sBAAc,eAAe,KAAK,YAAY,GAAG,CAAC;AAClD,YAAI,KAAK,iBAAiB;AACtB,eAAK,gBAAgB,GAAG,IAAI,YAAY,GAAG;AAAA,QAC/D;AAAA,MACA;AAGY,oBAAc,eAAgB;AAAA,IAC1C;AAAA,IACQ,oBAAoB,WAAW;AAC3B,UAAI,CAAC,KAAK,YAAY,KAAK;AACvB,eAAO;AACX,UAAI,CAAC,KAAK,WAAW;AACjB,eAAO;AAAA,MACvB;AACY,YAAM,SAAS;AAAA,QACX,YAAY;AAAA,MACf;AACD,YAAM,oBAAoB,KAAK,qBAAsB;AACrD,UAAI,KAAK,YAAY;AACjB,aAAK,aAAa;AAClB,eAAO,UAAU;AACjB,eAAO,gBACH,mBAAmB,WAAW,aAAa,KAAK;AACpD,eAAO,YAAY,oBACb,kBAAkB,KAAK,cAAc,EAAE,IACvC;AACN,eAAO;AAAA,MACvB;AACY,YAAM,OAAO,KAAK,QAAS;AAC3B,UAAI,CAAC,KAAK,mBAAmB,CAAC,KAAK,UAAU,CAAC,KAAK,QAAQ;AACvD,cAAM,cAAc,CAAE;AACtB,YAAI,KAAK,QAAQ,UAAU;AACvB,sBAAY,UACR,KAAK,aAAa,YAAY,SACxB,KAAK,aAAa,UAClB;AACV,sBAAY,gBACR,mBAAmB,WAAW,aAAa,KAAK;AAAA,QACxE;AACgB,YAAI,KAAK,gBAAgB,CAAC,aAAa,KAAK,YAAY,GAAG;AACvD,sBAAY,YAAY,oBAClB,kBAAkB,CAAE,GAAE,EAAE,IACxB;AACN,eAAK,eAAe;AAAA,QACxC;AACgB,eAAO;AAAA,MACvB;AACY,YAAM,iBAAiB,KAAK,mBAAmB,KAAK;AACpD,WAAK,wBAAyB;AAC9B,aAAO,YAAY,yBAAyB,KAAK,8BAA8B,KAAK,WAAW,cAAc;AAC7G,UAAI,mBAAmB;AACnB,eAAO,YAAY,kBAAkB,gBAAgB,OAAO,SAAS;AAAA,MACrF;AACY,YAAM,EAAE,GAAG,EAAG,IAAG,KAAK;AACtB,aAAO,kBAAkB,GAAG,EAAE,SAAS,GAAG,KAAK,EAAE,SAAS,GAAG;AAC7D,UAAI,KAAK,iBAAiB;AAKtB,eAAO,UACH,SAAS,OACH,eAAe,WACb,KAAK,aAAa,WAClB,IACF,KAAK,kBACD,KAAK,aAAa,UAClB,eAAe;AAAA,MAC7C,OACiB;AAKD,eAAO,UACH,SAAS,OACH,eAAe,YAAY,SACvB,eAAe,UACf,KACJ,eAAe,gBAAgB,SAC3B,eAAe,cACf;AAAA,MAC9B;AAIY,iBAAW,OAAO,iBAAiB;AAC/B,YAAI,eAAe,GAAG,MAAM;AACxB;AACJ,cAAM,EAAE,SAAS,SAAS,cAAa,IAAK,gBAAgB,GAAG;AAO/D,cAAM,YAAY,OAAO,cAAc,SACjC,eAAe,GAAG,IAClB,QAAQ,eAAe,GAAG,GAAG,IAAI;AACvC,YAAI,SAAS;AACT,gBAAM,MAAM,QAAQ;AACpB,mBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,mBAAO,QAAQ,CAAC,CAAC,IAAI;AAAA,UAC7C;AAAA,QACA,OACqB;AAID,cAAI,eAAe;AACf,iBAAK,QAAQ,cAAc,YAAY,KAAK,GAAG,IAAI;AAAA,UAC3E,OACyB;AACD,mBAAO,GAAG,IAAI;AAAA,UACtC;AAAA,QACA;AAAA,MACA;AAMY,UAAI,KAAK,QAAQ,UAAU;AACvB,eAAO,gBACH,SAAS,OACH,mBAAmB,WAAW,aAAa,KAAK,KAChD;AAAA,MAC1B;AACY,aAAO;AAAA,IACnB;AAAA,IACQ,gBAAgB;AACZ,WAAK,aAAa,KAAK,WAAW;AAAA,IAC9C;AAAA;AAAA,IAEQ,YAAY;AACR,WAAK,KAAK,MAAM,QAAQ,CAAC,SAAS,KAAK,kBAAkB,MAAM;AAC/D,WAAK,KAAK,MAAM,QAAQ,iBAAiB;AACzC,WAAK,KAAK,YAAY,MAAO;AAAA,IACzC;AAAA,EACK;AACL;AACA,SAAS,aAAa,MAAM;AACxB,OAAK,aAAc;AACvB;AACA,SAAS,mBAAmB,MAAM;AAC9B,QAAM,WAAW,KAAK,YAAY,YAAY,KAAK;AACnD,MAAI,KAAK,OAAQ,KACb,KAAK,UACL,YACA,KAAK,aAAa,WAAW,GAAG;AAChC,UAAM,EAAE,WAAWA,SAAQ,aAAa,eAAc,IAAK,KAAK;AAChE,UAAM,EAAE,kBAAkB,KAAK;AAC/B,UAAM,WAAW,SAAS,WAAW,KAAK,OAAO;AAGjD,QAAI,kBAAkB,QAAQ;AAC1B,eAAS,CAAC,SAAS;AACf,cAAM,eAAe,WACf,SAAS,YAAY,IAAI,IACzB,SAAS,UAAU,IAAI;AAC7B,cAAM,SAAS,WAAW,YAAY;AACtC,qBAAa,MAAMA,QAAO,IAAI,EAAE;AAChC,qBAAa,MAAM,aAAa,MAAM;AAAA,MACtD,CAAa;AAAA,IACb,WACiB,0BAA0B,eAAe,SAAS,WAAWA,OAAM,GAAG;AAC3E,eAAS,CAAC,SAAS;AACf,cAAM,eAAe,WACf,SAAS,YAAY,IAAI,IACzB,SAAS,UAAU,IAAI;AAC7B,cAAM,SAAS,WAAWA,QAAO,IAAI,CAAC;AACtC,qBAAa,MAAM,aAAa,MAAM;AAItC,YAAI,KAAK,kBAAkB,CAAC,KAAK,kBAAkB;AAC/C,eAAK,oBAAoB;AACzB,eAAK,eAAe,IAAI,EAAE,MACtB,KAAK,eAAe,IAAI,EAAE,MAAM;AAAA,QACxD;AAAA,MACA,CAAa;AAAA,IACb;AACQ,UAAM,cAAc,YAAa;AACjC,iBAAa,aAAaA,SAAQ,SAAS,SAAS;AACpD,UAAM,cAAc,YAAa;AACjC,QAAI,UAAU;AACV,mBAAa,aAAa,KAAK,eAAe,gBAAgB,IAAI,GAAG,SAAS,WAAW;AAAA,IACrG,OACa;AACD,mBAAa,aAAaA,SAAQ,SAAS,SAAS;AAAA,IAChE;AACQ,UAAM,mBAAmB,CAAC,YAAY,WAAW;AACjD,QAAI,2BAA2B;AAC/B,QAAI,CAAC,KAAK,YAAY;AAClB,YAAM,iBAAiB,KAAK,2BAA4B;AAKxD,UAAI,kBAAkB,CAAC,eAAe,YAAY;AAC9C,cAAM,EAAE,UAAU,gBAAgB,QAAQ,aAAc,IAAG;AAC3D,YAAI,kBAAkB,cAAc;AAChC,gBAAM,mBAAmB,UAAW;AACpC,+BAAqB,kBAAkB,SAAS,WAAW,eAAe,SAAS;AACnF,gBAAM,iBAAiB,UAAW;AAClC,+BAAqB,gBAAgBA,SAAQ,aAAa,SAAS;AACnE,cAAI,CAAC,iBAAiB,kBAAkB,cAAc,GAAG;AACrD,uCAA2B;AAAA,UACnD;AACoB,cAAI,eAAe,QAAQ,YAAY;AACnC,iBAAK,iBAAiB;AACtB,iBAAK,uBAAuB;AAC5B,iBAAK,iBAAiB;AAAA,UAC9C;AAAA,QACA;AAAA,MACA;AAAA,IACA;AACQ,SAAK,gBAAgB,aAAa;AAAA,MAC9B,QAAAA;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,IACZ,CAAS;AAAA,EACT,WACa,KAAK,UAAU;AACpB,UAAM,EAAE,mBAAmB,KAAK;AAChC,sBAAkB,eAAgB;AAAA,EAC1C;AAMI,OAAK,QAAQ,aAAa;AAC9B;AACA,SAAS,oBAAoB,MAAM;AAO/B,MAAI,CAAC,KAAK;AACN;AAOJ,MAAI,CAAC,KAAK,gBAAgB;AACtB,SAAK,oBAAoB,KAAK,OAAO;AAAA,EAC7C;AAMI,OAAK,4BAA4B,KAAK,0BAA0B,QAAQ,KAAK,qBACzE,KAAK,OAAO,qBACZ,KAAK,OAAO,uBAAuB;AACvC,OAAK,qBAAqB,KAAK,mBAAmB,KAAK,OAAO;AAClE;AACA,SAAS,gBAAgB,MAAM;AAC3B,OAAK,oBACD,KAAK,0BACD,KAAK,mBACD;AAChB;AACA,SAAS,cAAc,MAAM;AACzB,OAAK,cAAe;AACxB;AACA,SAAS,kBAAkB,MAAM;AAC7B,OAAK,kBAAmB;AAC5B;AACA,SAAS,mBAAmB,MAAM;AAC9B,OAAK,gBAAgB;AACzB;AACA,SAAS,oBAAoB,MAAM;AAC/B,QAAM,EAAE,kBAAkB,KAAK;AAC/B,MAAI,iBAAiB,cAAc,SAAQ,EAAG,uBAAuB;AACjE,kBAAc,OAAO,qBAAqB;AAAA,EAClD;AACI,OAAK,eAAgB;AACzB;AACA,SAAS,gBAAgB,MAAM;AAC3B,OAAK,gBAAiB;AACtB,OAAK,cAAc,KAAK,iBAAiB,KAAK,SAAS;AACvD,OAAK,oBAAoB;AAC7B;AACA,SAAS,mBAAmB,MAAM;AAC9B,OAAK,mBAAoB;AAC7B;AACA,SAAS,eAAe,MAAM;AAC1B,OAAK,eAAgB;AACzB;AACA,SAAS,qBAAqB,MAAM;AAChC,OAAK,qBAAsB;AAC/B;AACA,SAAS,oBAAoB,OAAO;AAChC,QAAM,mBAAoB;AAC9B;AACA,SAAS,aAAa,QAAQ,OAAO,GAAG;AACpC,SAAO,YAAY,UAAU,MAAM,WAAW,GAAG,CAAC;AAClD,SAAO,QAAQ,UAAU,MAAM,OAAO,GAAG,CAAC;AAC1C,SAAO,SAAS,MAAM;AACtB,SAAO,cAAc,MAAM;AAC/B;AACA,SAAS,QAAQ,QAAQ,MAAM,IAAI,GAAG;AAClC,SAAO,MAAM,UAAU,KAAK,KAAK,GAAG,KAAK,CAAC;AAC1C,SAAO,MAAM,UAAU,KAAK,KAAK,GAAG,KAAK,CAAC;AAC9C;AACA,SAAS,OAAO,QAAQ,MAAM,IAAI,GAAG;AACjC,UAAQ,OAAO,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC;AACjC,UAAQ,OAAO,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC;AACrC;AACA,SAAS,oBAAoB,MAAM;AAC/B,SAAQ,KAAK,mBAAmB,KAAK,gBAAgB,gBAAgB;AACzE;AACA,MAAM,0BAA0B;AAAA,EAC5B,UAAU;AAAA,EACV,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;AACzB;AACA,MAAM,oBAAoB,CAAC,WAAW,OAAO,cAAc,eACvD,UAAU,aACV,UAAU,UAAU,cAAc,SAAS,MAAM;AAMrD,MAAM,aAAa,kBAAkB,cAAc,KAAK,CAAC,kBAAkB,SAAS,IAC9E,KAAK,QACL;AACN,SAAS,UAAU,MAAM;AAErB,OAAK,MAAM,WAAW,KAAK,GAAG;AAC9B,OAAK,MAAM,WAAW,KAAK,GAAG;AAClC;AACA,SAAS,SAAS,KAAK;AACnB,YAAU,IAAI,CAAC;AACf,YAAU,IAAI,CAAC;AACnB;AACA,SAAS,0BAA0B,eAAe,UAAUA,SAAQ;AAChE,SAAQ,kBAAkB,cACrB,kBAAkB,qBACf,CAAC,OAAO,YAAY,QAAQ,GAAG,YAAYA,OAAM,GAAG,GAAG;AACnE;AACA,SAAS,uBAAuB,MAAM;AAClC,SAAO,SAAS,KAAK,QAAQ,KAAK,QAAQ;AAC9C;ACpjDA,MAAM,yBAAyB,qBAAqB;AAAA,EAChD,sBAAsB,CAAC,KAAK,WAAW,YAAY,KAAK,UAAU,MAAM;AAAA,EACxE,eAAe,OAAO;AAAA,IAClB,GAAG,SAAS,gBAAgB,cAAc,SAAS,KAAK;AAAA,IACxD,GAAG,SAAS,gBAAgB,aAAa,SAAS,KAAK;AAAA,EAC/D;AAAA,EACI,mBAAmB,MAAM;AAC7B,CAAC;ACPD,MAAM,qBAAqB;AAAA,EACvB,SAAS;AACb;AACA,MAAM,qBAAqB,qBAAqB;AAAA,EAC5C,eAAe,CAAC,cAAc;AAAA,IAC1B,GAAG,SAAS;AAAA,IACZ,GAAG,SAAS;AAAA,EACpB;AAAA,EACI,eAAe,MAAM;AACjB,QAAI,CAAC,mBAAmB,SAAS;AAC7B,YAAM,eAAe,IAAI,uBAAuB,EAAE;AAClD,mBAAa,MAAM,MAAM;AACzB,mBAAa,WAAW,EAAE,cAAc,KAAI,CAAE;AAC9C,yBAAmB,UAAU;AAAA,IACzC;AACQ,WAAO,mBAAmB;AAAA,EAC7B;AAAA,EACD,gBAAgB,CAAC,UAAU,UAAU;AACjC,aAAS,MAAM,YAAY,UAAU,SAAY,QAAQ;AAAA,EAC5D;AAAA,EACD,mBAAmB,CAAC,aAAa,QAAQ,OAAO,iBAAiB,QAAQ,EAAE,aAAa,OAAO;AACnG,CAAC;ACnBD,MAAM,OAAO;AAAA,EACT,KAAK;AAAA,IACD,SAAS;AAAA,EACZ;AAAA,EACD,MAAM;AAAA,IACF,SAAS;AAAA,IACT,gBAAgB;AAAA,IAChB;AAAA,EACH;AACL;ACVA,SAAS,iBAAiB,MAAM,OAAO,WAAW;AAC9C,QAAM,EAAE,MAAK,IAAK;AAClB,MAAI,KAAK,kBAAkB,MAAM,YAAY;AACzC,SAAK,eAAe,UAAU,cAAc,cAAc,OAAO;AAAA,EACzE;AACI,QAAM,YAAa,YAAY;AAC/B,QAAM,WAAW,MAAM,SAAS;AAChC,MAAI,UAAU;AACV,UAAM,WAAW,MAAM,SAAS,OAAO,iBAAiB,KAAK,CAAC,CAAC;AAAA,EACvE;AACA;AACA,MAAM,qBAAqB,QAAQ;AAAA,EAC/B,QAAQ;AACJ,UAAM,EAAE,YAAY,KAAK;AACzB,QAAI,CAAC;AACD;AACJ,SAAK,UAAU,MAAM,SAAS,CAAC,UAAU,eAAe;AACpD,uBAAiB,KAAK,MAAM,YAAY,OAAO;AAC/C,aAAO,CAAC,aAAa,iBAAiB,KAAK,MAAM,UAAU,KAAK;AAAA,IAC5E,CAAS;AAAA,EACT;AAAA,EACI,UAAU;AAAA,EAAA;AACd;ACtBA,MAAM,qBAAqB,QAAQ;AAAA,EAC/B,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,WAAW;AAAA,EACxB;AAAA,EACI,UAAU;AACN,QAAI,iBAAiB;AAOrB,QAAI;AACA,uBAAiB,KAAK,KAAK,QAAQ,QAAQ,gBAAgB;AAAA,IACvE,SACe,GAAG;AACN,uBAAiB;AAAA,IAC7B;AACQ,QAAI,CAAC,kBAAkB,CAAC,KAAK,KAAK;AAC9B;AACJ,SAAK,KAAK,eAAe,UAAU,cAAc,IAAI;AACrD,SAAK,WAAW;AAAA,EACxB;AAAA,EACI,SAAS;AACL,QAAI,CAAC,KAAK,YAAY,CAAC,KAAK,KAAK;AAC7B;AACJ,SAAK,KAAK,eAAe,UAAU,cAAc,KAAK;AACtD,SAAK,WAAW;AAAA,EACxB;AAAA,EACI,QAAQ;AACJ,SAAK,UAAU,KAAK,YAAY,KAAK,KAAK,SAAS,SAAS,MAAM,KAAK,QAAO,CAAE,GAAG,YAAY,KAAK,KAAK,SAAS,QAAQ,MAAM,KAAK,OAAM,CAAE,CAAC;AAAA,EACtJ;AAAA,EACI,UAAU;AAAA,EAAA;AACd;AClCA,SAAS,iBAAiB,MAAM,OAAO,WAAW;AAC9C,QAAM,EAAE,MAAK,IAAK;AAClB,MAAI,KAAK,mBAAmB,qBAAqB,KAAK,QAAQ,UAAU;AACpE;AAAA,EACR;AACI,MAAI,KAAK,kBAAkB,MAAM,UAAU;AACvC,SAAK,eAAe,UAAU,YAAY,cAAc,OAAO;AAAA,EACvE;AACI,QAAM,YAAa,WAAW,cAAc,QAAQ,KAAK;AACzD,QAAM,WAAW,MAAM,SAAS;AAChC,MAAI,UAAU;AACV,UAAM,WAAW,MAAM,SAAS,OAAO,iBAAiB,KAAK,CAAC,CAAC;AAAA,EACvE;AACA;AACA,MAAM,qBAAqB,QAAQ;AAAA,EAC/B,QAAQ;AACJ,UAAM,EAAE,YAAY,KAAK;AACzB,QAAI,CAAC;AACD;AACJ,SAAK,UAAU,MAAM,SAAS,CAAC,UAAU,eAAe;AACpD,uBAAiB,KAAK,MAAM,YAAY,OAAO;AAC/C,aAAO,CAAC,UAAU,EAAE,QAAO,MAAO,iBAAiB,KAAK,MAAM,UAAU,UAAU,QAAQ,QAAQ;AAAA,IAC9G,GAAW,EAAE,iBAAiB,KAAK,KAAK,MAAM,gBAAe,CAAE;AAAA,EAC/D;AAAA,EACI,UAAU;AAAA,EAAA;AACd;ACxBA,MAAM,oBAAoB,oBAAI,QAAS;AAMvC,MAAM,YAAY,oBAAI,QAAS;AAC/B,MAAM,uBAAuB,CAAC,UAAU;AACpC,QAAM,WAAW,kBAAkB,IAAI,MAAM,MAAM;AACnD,cAAY,SAAS,KAAK;AAC9B;AACA,MAAM,2BAA2B,CAAC,YAAY;AAC1C,UAAQ,QAAQ,oBAAoB;AACxC;AACA,SAAS,yBAAyB,EAAE,MAAM,GAAG,WAAW;AACpD,QAAM,aAAa,QAAQ;AAI3B,MAAI,CAAC,UAAU,IAAI,UAAU,GAAG;AAC5B,cAAU,IAAI,YAAY,EAAE;AAAA,EACpC;AACI,QAAM,gBAAgB,UAAU,IAAI,UAAU;AAC9C,QAAM,MAAM,KAAK,UAAU,OAAO;AAKlC,MAAI,CAAC,cAAc,GAAG,GAAG;AACrB,kBAAc,GAAG,IAAI,IAAI,qBAAqB,0BAA0B,EAAE,MAAM,GAAG,SAAS;AAAA,EACpG;AACI,SAAO,cAAc,GAAG;AAC5B;AACA,SAAS,oBAAoB,SAAS,SAAS,UAAU;AACrD,QAAM,4BAA4B,yBAAyB,OAAO;AAClE,oBAAkB,IAAI,SAAS,QAAQ;AACvC,4BAA0B,QAAQ,OAAO;AACzC,SAAO,MAAM;AACT,sBAAkB,OAAO,OAAO;AAChC,8BAA0B,UAAU,OAAO;AAAA,EAC9C;AACL;AC3CA,MAAM,iBAAiB;AAAA,EACnB,MAAM;AAAA,EACN,KAAK;AACT;AACA,MAAM,sBAAsB,QAAQ;AAAA,EAChC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,iBAAiB;AACtB,SAAK,WAAW;AAAA,EACxB;AAAA,EACI,gBAAgB;AACZ,SAAK,QAAS;AACd,UAAM,EAAE,WAAW,CAAE,EAAA,IAAK,KAAK,KAAK,SAAU;AAC9C,UAAM,EAAE,MAAM,QAAQ,YAAY,SAAS,QAAQ,KAAI,IAAK;AAC5D,UAAM,UAAU;AAAA,MACZ,MAAM,OAAO,KAAK,UAAU;AAAA,MAC5B;AAAA,MACA,WAAW,OAAO,WAAW,WAAW,SAAS,eAAe,MAAM;AAAA,IACzE;AACD,UAAM,uBAAuB,CAAC,UAAU;AACpC,YAAM,EAAE,eAAc,IAAK;AAI3B,UAAI,KAAK,aAAa;AAClB;AACJ,WAAK,WAAW;AAKhB,UAAI,QAAQ,CAAC,kBAAkB,KAAK,gBAAgB;AAChD;AAAA,MAChB,WACqB,gBAAgB;AACrB,aAAK,iBAAiB;AAAA,MACtC;AACY,UAAI,KAAK,KAAK,gBAAgB;AAC1B,aAAK,KAAK,eAAe,UAAU,eAAe,cAAc;AAAA,MAChF;AAKY,YAAM,EAAE,iBAAiB,gBAAe,IAAK,KAAK,KAAK,SAAU;AACjE,YAAM,WAAW,iBAAiB,kBAAkB;AACpD,kBAAY,SAAS,KAAK;AAAA,IAC7B;AACD,WAAO,oBAAoB,KAAK,KAAK,SAAS,SAAS,oBAAoB;AAAA,EACnF;AAAA,EACI,QAAQ;AACJ,SAAK,cAAe;AAAA,EAC5B;AAAA,EACI,SAAS;AACL,QAAI,OAAO,yBAAyB;AAChC;AACJ,UAAM,EAAE,OAAO,UAAW,IAAG,KAAK;AAClC,UAAM,oBAAoB,CAAC,UAAU,UAAU,MAAM,EAAE,KAAK,yBAAyB,OAAO,SAAS,CAAC;AACtG,QAAI,mBAAmB;AACnB,WAAK,cAAe;AAAA,IAChC;AAAA,EACA;AAAA,EACI,UAAU;AAAA,EAAA;AACd;AACA,SAAS,yBAAyB,EAAE,WAAW,GAAI,GAAE,EAAE,UAAU,eAAe,GAAI,IAAG,IAAI;AACvF,SAAO,CAAC,SAAS,SAAS,IAAI,MAAM,aAAa,IAAI;AACzD;AChEA,MAAM,oBAAoB;AAAA,EACtB,QAAQ;AAAA,IACJ,SAAS;AAAA,EACZ;AAAA,EACD,KAAK;AAAA,IACD,SAAS;AAAA,EACZ;AAAA,EACD,OAAO;AAAA,IACH,SAAS;AAAA,EACZ;AAAA,EACD,OAAO;AAAA,IACH,SAAS;AAAA,EACZ;AACL;ACfA,MAAM,SAAS;AAAA,EACX,QAAQ;AAAA,IACJ,gBAAgB;AAAA,IAChB;AAAA,EACH;AACL;ACPA,MAAM,uBAAuB,EAAE,SAAS,KAAM;AAC9C,MAAM,2BAA2B,EAAE,SAAS,MAAO;ACCnD,SAAS,2BAA2B;AAChC,2BAAyB,UAAU;AACnC,MAAI,CAAC;AACD;AACJ,MAAI,OAAO,YAAY;AACnB,UAAM,mBAAmB,OAAO,WAAW,0BAA0B;AACrE,UAAM,8BAA8B,MAAO,qBAAqB,UAAU,iBAAiB;AAC3F,qBAAiB,YAAY,2BAA2B;AACxD,gCAA6B;AAAA,EACrC,OACS;AACD,yBAAqB,UAAU;AAAA,EACvC;AACA;AChBA,MAAM,qBAAqB,oBAAI,QAAS;ACExC,SAAS,4BAA4B,SAAS,MAAM,MAAM;AACtD,aAAW,OAAO,MAAM;AACpB,UAAM,YAAY,KAAK,GAAG;AAC1B,UAAM,YAAY,KAAK,GAAG;AAC1B,QAAI,cAAc,SAAS,GAAG;AAK1B,cAAQ,SAAS,KAAK,SAAS;AAAA,IAC3C,WACiB,cAAc,SAAS,GAAG;AAK/B,cAAQ,SAAS,KAAK,YAAY,WAAW,EAAE,OAAO,QAAO,CAAE,CAAC;AAAA,IAC5E,WACiB,cAAc,WAAW;AAM9B,UAAI,QAAQ,SAAS,GAAG,GAAG;AACvB,cAAM,gBAAgB,QAAQ,SAAS,GAAG;AAC1C,YAAI,cAAc,cAAc,MAAM;AAClC,wBAAc,KAAK,SAAS;AAAA,QAChD,WACyB,CAAC,cAAc,aAAa;AACjC,wBAAc,IAAI,SAAS;AAAA,QAC/C;AAAA,MACA,OACiB;AACD,cAAM,cAAc,QAAQ,eAAe,GAAG;AAC9C,gBAAQ,SAAS,KAAK,YAAY,gBAAgB,SAAY,cAAc,WAAW,EAAE,OAAO,QAAS,CAAA,CAAC;AAAA,MAC1H;AAAA,IACA;AAAA,EACA;AAEI,aAAW,OAAO,MAAM;AACpB,QAAI,KAAK,GAAG,MAAM;AACd,cAAQ,YAAY,GAAG;AAAA,EACnC;AACI,SAAO;AACX;ACpCA,MAAM,oBAAoB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAKA,MAAM,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQhB,4BAA4B,QAAQ,YAAY,gBAAgB;AAC5D,WAAO,CAAC;AAAA,EAAA;AAAA,EAEZ,YAAY,EAAE,QAAQ,OAAO,iBAAiB,qBAAqB,uBAAuB,YAAa,GAAG,UAAU,IAAI;AAKpH,SAAK,UAAU;AAIV,SAAA,+BAAe,IAAI;AAIxB,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAQ7B,SAAK,qBAAqB;AAMrB,SAAA,6BAAa,IAAI;AACtB,SAAK,mBAAmB;AAIxB,SAAK,WAAW,CAAC;AAKZ,SAAA,yCAAyB,IAAI;AAMlC,SAAK,mBAAmB,CAAC;AAIzB,SAAK,SAAS,CAAC;AAMf,SAAK,yBAAyB,CAAC;AAC/B,SAAK,eAAe,MAAM,KAAK,OAAO,UAAU,KAAK,YAAY;AACjE,SAAK,SAAS,MAAM;AAChB,UAAI,CAAC,KAAK;AACN;AACJ,WAAK,aAAa;AACb,WAAA,eAAe,KAAK,SAAS,KAAK,aAAa,KAAK,MAAM,OAAO,KAAK,UAAU;AAAA,IACzF;AACA,SAAK,oBAAoB;AACzB,SAAK,iBAAiB,MAAM;AAClB,YAAA,MAAM,KAAK,IAAI;AACjB,UAAA,KAAK,oBAAoB,KAAK;AAC9B,aAAK,oBAAoB;AACzB,cAAM,OAAO,KAAK,QAAQ,OAAO,IAAI;AAAA,MAAA;AAAA,IAE7C;AACM,UAAA,EAAE,cAAc,YAAA,IAAgB;AACtC,SAAK,eAAe;AACf,SAAA,aAAa,EAAE,GAAG,aAAa;AACpC,SAAK,gBAAgB,MAAM,UAAU,EAAE,GAAG,aAAA,IAAiB,CAAC;AAC5D,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,kBAAkB;AACvB,SAAK,QAAQ,SAAS,OAAO,QAAQ,IAAI;AACzC,SAAK,sBAAsB;AAC3B,SAAK,UAAU;AACV,SAAA,wBAAwB,QAAQ,qBAAqB;AACrD,SAAA,wBAAwB,sBAAsB,KAAK;AACnD,SAAA,gBAAgB,cAAc,KAAK;AACxC,QAAI,KAAK,eAAe;AACf,WAAA,sCAAsB,IAAI;AAAA,IAAA;AAEnC,SAAK,yBAAyB,QAAQ,UAAU,OAAO,OAAO;AAWxD,UAAA,EAAE,YAAY,GAAG,oBAAoB,IAAI,KAAK,4BAA4B,OAAO,CAAC,GAAG,IAAI;AAC/F,eAAW,OAAO,qBAAqB;AAC7B,YAAA,QAAQ,oBAAoB,GAAG;AACrC,UAAI,aAAa,GAAG,MAAM,UAAa,cAAc,KAAK,GAAG;AACzD,cAAM,IAAI,aAAa,GAAG,GAAG,KAAK;AAAA,MAAA;AAAA,IACtC;AAAA,EACJ;AAAA,EAEJ,MAAM,UAAU;AACZ,SAAK,UAAU;AACI,uBAAA,IAAI,UAAU,IAAI;AACrC,QAAI,KAAK,cAAc,CAAC,KAAK,WAAW,UAAU;AACzC,WAAA,WAAW,MAAM,QAAQ;AAAA,IAAA;AAElC,QAAI,KAAK,UAAU,KAAK,iBAAiB,CAAC,KAAK,uBAAuB;AAClE,WAAK,wBAAwB,KAAK,OAAO,gBAAgB,IAAI;AAAA,IAAA;AAE5D,SAAA,OAAO,QAAQ,CAAC,OAAO,QAAQ,KAAK,kBAAkB,KAAK,KAAK,CAAC;AAClE,QAAA,CAAC,yBAAyB,SAAS;AACV,+BAAA;AAAA,IAAA;AAExB,SAAA,qBACD,KAAK,wBAAwB,UACvB,QACA,KAAK,wBAAwB,WACzB,OACA,qBAAqB;AACQ;AAC9B,eAAA,KAAK,uBAAuB,MAAM,wFAAwF;AAAA,IAAA;AAEvI,QAAI,KAAK;AACA,WAAA,OAAO,SAAS,IAAI,IAAI;AACjC,SAAK,OAAO,KAAK,OAAO,KAAK,eAAe;AAAA,EAAA;AAAA,EAEhD,UAAU;AACD,SAAA,cAAc,KAAK,WAAW,QAAQ;AAC3C,gBAAY,KAAK,YAAY;AAC7B,gBAAY,KAAK,MAAM;AACvB,SAAK,mBAAmB,QAAQ,CAAC,WAAW,QAAQ;AACpD,SAAK,mBAAmB,MAAM;AACzB,SAAA,yBAAyB,KAAK,sBAAsB;AACzD,SAAK,UAAU,KAAK,OAAO,SAAS,OAAO,IAAI;AACpC,eAAA,OAAO,KAAK,QAAQ;AACtB,WAAA,OAAO,GAAG,EAAE,MAAM;AAAA,IAAA;AAEhB,eAAA,OAAO,KAAK,UAAU;AACvB,YAAA,UAAU,KAAK,SAAS,GAAG;AACjC,UAAI,SAAS;AACT,gBAAQ,QAAQ;AAChB,gBAAQ,YAAY;AAAA,MAAA;AAAA,IACxB;AAEJ,SAAK,UAAU;AAAA,EAAA;AAAA,EAEnB,kBAAkB,KAAK,OAAO;AAC1B,QAAI,KAAK,mBAAmB,IAAI,GAAG,GAAG;AAC7B,WAAA,mBAAmB,IAAI,GAAG,EAAE;AAAA,IAAA;AAE/B,UAAA,mBAAmB,eAAe,IAAI,GAAG;AAC3C,QAAA,oBAAoB,KAAK,iBAAiB;AAC1C,WAAK,gBAAgB;AAAA,IAAA;AAEzB,UAAM,iBAAiB,MAAM,GAAG,UAAU,CAAC,gBAAgB;AAClD,WAAA,aAAa,GAAG,IAAI;AACzB,WAAK,MAAM,YAAY,MAAM,UAAU,KAAK,YAAY;AACpD,UAAA,oBAAoB,KAAK,YAAY;AACrC,aAAK,WAAW,mBAAmB;AAAA,MAAA;AAAA,IACvC,CACH;AACD,UAAM,wBAAwB,MAAM,GAAG,iBAAiB,KAAK,cAAc;AACvE,QAAA;AACJ,QAAI,OAAO,uBAAuB;AAC9B,wBAAkB,OAAO,sBAAsB,MAAM,KAAK,KAAK;AAAA,IAAA;AAE9D,SAAA,mBAAmB,IAAI,KAAK,MAAM;AACpB,qBAAA;AACO,4BAAA;AAClB,UAAA;AACgB,wBAAA;AACpB,UAAI,MAAM;AACN,cAAM,KAAK;AAAA,IAAA,CAClB;AAAA,EAAA;AAAA,EAEL,iBAAiB,OAAO;AAIhB,QAAA,CAAC,KAAK,WACN,CAAC,KAAK,4BACN,KAAK,SAAS,MAAM,MAAM;AACnB,aAAA;AAAA,IAAA;AAEX,WAAO,KAAK,yBAAyB,KAAK,SAAS,MAAM,OAAO;AAAA,EAAA;AAAA,EAEpE,iBAAiB;AACb,QAAI,MAAM;AACV,SAAK,OAAO,oBAAoB;AACtB,YAAA,oBAAoB,mBAAmB,GAAG;AAChD,UAAI,CAAC;AACD;AACJ,YAAM,EAAE,WAAW,SAAS,mBAAuB,IAAA;AAI/C,UAAA,CAAC,KAAK,SAAS,GAAG,KAClB,sBACA,UAAU,KAAK,KAAK,GAAG;AACvB,aAAK,SAAS,GAAG,IAAI,IAAI,mBAAmB,IAAI;AAAA,MAAA;AAKhD,UAAA,KAAK,SAAS,GAAG,GAAG;AACd,cAAA,UAAU,KAAK,SAAS,GAAG;AACjC,YAAI,QAAQ,WAAW;AACnB,kBAAQ,OAAO;AAAA,QAAA,OAEd;AACD,kBAAQ,MAAM;AACd,kBAAQ,YAAY;AAAA,QAAA;AAAA,MACxB;AAAA,IACJ;AAAA,EACJ;AAAA,EAEJ,eAAe;AACX,SAAK,MAAM,KAAK,aAAa,KAAK,cAAc,KAAK,KAAK;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO9D,qBAAqB;AACV,WAAA,KAAK,UACN,KAAK,2BAA2B,KAAK,SAAS,KAAK,KAAK,IACxD,UAAU;AAAA,EAAA;AAAA,EAEpB,eAAe,KAAK;AACT,WAAA,KAAK,aAAa,GAAG;AAAA,EAAA;AAAA,EAEhC,eAAe,KAAK,OAAO;AAClB,SAAA,aAAa,GAAG,IAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7B,OAAO,OAAO,iBAAiB;AAC3B,QAAI,MAAM,qBAAqB,KAAK,MAAM,mBAAmB;AACzD,WAAK,eAAe;AAAA,IAAA;AAExB,SAAK,YAAY,KAAK;AACtB,SAAK,QAAQ;AACb,SAAK,sBAAsB,KAAK;AAChC,SAAK,kBAAkB;AAIvB,aAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACzC,YAAA,MAAM,kBAAkB,CAAC;AAC3B,UAAA,KAAK,uBAAuB,GAAG,GAAG;AAC7B,aAAA,uBAAuB,GAAG,EAAE;AAC1B,eAAA,KAAK,uBAAuB,GAAG;AAAA,MAAA;AAE1C,YAAM,eAAgB,OAAO;AACvB,YAAA,WAAW,MAAM,YAAY;AACnC,UAAI,UAAU;AACV,aAAK,uBAAuB,GAAG,IAAI,KAAK,GAAG,KAAK,QAAQ;AAAA,MAAA;AAAA,IAC5D;AAEC,SAAA,mBAAmB,4BAA4B,MAAM,KAAK,4BAA4B,OAAO,KAAK,WAAW,IAAI,GAAG,KAAK,gBAAgB;AAC9I,QAAI,KAAK,wBAAwB;AAC7B,WAAK,uBAAuB;AAAA,IAAA;AAAA,EAChC;AAAA,EAEJ,WAAW;AACP,WAAO,KAAK;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAKhB,WAAW,MAAM;AACb,WAAO,KAAK,MAAM,WAAW,KAAK,MAAM,SAAS,IAAI,IAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAK7D,uBAAuB;AACnB,WAAO,KAAK,MAAM;AAAA,EAAA;AAAA,EAEtB,wBAAwB;AACpB,WAAO,KAAK,MAAM;AAAA,EAAA;AAAA,EAEtB,wBAAwB;AACb,WAAA,KAAK,gBACN,OACA,KAAK,SACD,KAAK,OAAO,0BACZ;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAKd,gBAAgB,OAAO;AACb,UAAA,qBAAqB,KAAK,sBAAsB;AACtD,QAAI,oBAAoB;AACpB,yBAAmB,mBACf,mBAAmB,gBAAgB,IAAI,KAAK;AAChD,aAAO,MAAM,mBAAmB,gBAAgB,OAAO,KAAK;AAAA,IAAA;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA,EAKJ,SAAS,KAAK,OAAO;AAEjB,UAAM,gBAAgB,KAAK,OAAO,IAAI,GAAG;AACzC,QAAI,UAAU,eAAe;AACrB,UAAA;AACA,aAAK,YAAY,GAAG;AACnB,WAAA,kBAAkB,KAAK,KAAK;AAC5B,WAAA,OAAO,IAAI,KAAK,KAAK;AAC1B,WAAK,aAAa,GAAG,IAAI,MAAM,IAAI;AAAA,IAAA;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAKJ,YAAY,KAAK;AACR,SAAA,OAAO,OAAO,GAAG;AACtB,UAAM,cAAc,KAAK,mBAAmB,IAAI,GAAG;AACnD,QAAI,aAAa;AACD,kBAAA;AACP,WAAA,mBAAmB,OAAO,GAAG;AAAA,IAAA;AAE/B,WAAA,KAAK,aAAa,GAAG;AACvB,SAAA,2BAA2B,KAAK,KAAK,WAAW;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAKzD,SAAS,KAAK;AACH,WAAA,KAAK,OAAO,IAAI,GAAG;AAAA,EAAA;AAAA,EAE9B,SAAS,KAAK,cAAc;AACxB,QAAI,KAAK,MAAM,UAAU,KAAK,MAAM,OAAO,GAAG,GAAG;AACtC,aAAA,KAAK,MAAM,OAAO,GAAG;AAAA,IAAA;AAEhC,QAAI,QAAQ,KAAK,OAAO,IAAI,GAAG;AAC3B,QAAA,UAAU,UAAa,iBAAiB,QAAW;AAC3C,cAAA,YAAY,iBAAiB,OAAO,SAAY,cAAc,EAAE,OAAO,MAAM;AAChF,WAAA,SAAS,KAAK,KAAK;AAAA,IAAA;AAErB,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOX,UAAU,KAAK,QAAQ;AACf,QAAA,QAAQ,KAAK,aAAa,GAAG,MAAM,UAAa,CAAC,KAAK,UACpD,KAAK,aAAa,GAAG,IACrB,KAAK,uBAAuB,KAAK,OAAO,GAAG,KACzC,KAAK,sBAAsB,KAAK,SAAS,KAAK,KAAK,OAAO;AAC9D,QAAA,UAAU,UAAa,UAAU,MAAM;AACnC,UAAA,OAAO,UAAU,aAChB,kBAAkB,KAAK,KAAK,kBAAkB,KAAK,IAAI;AAExD,gBAAQ,WAAW,KAAK;AAAA,MAAA,WAEnB,CAAC,cAAc,KAAK,KAAK,QAAQ,KAAK,MAAM,GAAG;AAC5C,gBAAA,kBAAkB,KAAK,MAAM;AAAA,MAAA;AAEpC,WAAA,cAAc,KAAK,cAAc,KAAK,IAAI,MAAM,QAAQ,KAAK;AAAA,IAAA;AAEtE,WAAO,cAAc,KAAK,IAAI,MAAM,IAAQ,IAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhD,cAAc,KAAK,OAAO;AACjB,SAAA,WAAW,GAAG,IAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3B,cAAc,KAAK;AACT,UAAA,EAAE,YAAY,KAAK;AACrB,QAAA;AACJ,QAAI,OAAO,YAAY,YAAY,OAAO,YAAY,UAAU;AAC5D,YAAM,UAAU,wBAAwB,KAAK,OAAO,SAAS,KAAK,iBAAiB,MAAM;AACzF,UAAI,SAAS;AACT,2BAAmB,QAAQ,GAAG;AAAA,MAAA;AAAA,IAClC;AAKA,QAAA,WAAW,qBAAqB,QAAW;AACpC,aAAA;AAAA,IAAA;AAMX,UAAM,SAAS,KAAK,uBAAuB,KAAK,OAAO,GAAG;AAC1D,QAAI,WAAW,UAAa,CAAC,cAAc,MAAM;AACtC,aAAA;AAKJ,WAAA,KAAK,cAAc,GAAG,MAAM,UAC/B,qBAAqB,SACnB,SACA,KAAK,WAAW,GAAG;AAAA,EAAA;AAAA,EAE7B,GAAG,WAAW,UAAU;AACpB,QAAI,CAAC,KAAK,OAAO,SAAS,GAAG;AACzB,WAAK,OAAO,SAAS,IAAI,IAAI,oBAAoB;AAAA,IAAA;AAErD,WAAO,KAAK,OAAO,SAAS,EAAE,IAAI,QAAQ;AAAA,EAAA;AAAA,EAE9C,OAAO,cAAc,MAAM;AACnB,QAAA,KAAK,OAAO,SAAS,GAAG;AACxB,WAAK,OAAO,SAAS,EAAE,OAAO,GAAG,IAAI;AAAA,IAAA;AAAA,EACzC;AAER;AC7cA,MAAM,yBAAyB,cAAc;AAAA,EACzC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,mBAAmB;AAAA,EAChC;AAAA,EACI,yBAAyB,GAAG,GAAG;AAM3B,WAAO,EAAE,wBAAwB,CAAC,IAAI,IAAI,IAAI;AAAA,EACtD;AAAA,EACI,uBAAuB,OAAO,KAAK;AAC/B,WAAO,MAAM,QACP,MAAM,MAAM,GAAG,IACf;AAAA,EACd;AAAA,EACI,2BAA2B,KAAK,EAAE,MAAM,MAAK,GAAI;AAC7C,WAAO,KAAK,GAAG;AACf,WAAO,MAAM,GAAG;AAAA,EACxB;AAAA,EACI,yBAAyB;AACrB,QAAI,KAAK,mBAAmB;AACxB,WAAK,kBAAmB;AACxB,aAAO,KAAK;AAAA,IACxB;AACQ,UAAM,EAAE,aAAa,KAAK;AAC1B,QAAI,cAAc,QAAQ,GAAG;AACzB,WAAK,oBAAoB,SAAS,GAAG,UAAU,CAAC,WAAW;AACvD,YAAI,KAAK,SAAS;AACd,eAAK,QAAQ,cAAc,GAAG,MAAM;AAAA,QACxD;AAAA,MACA,CAAa;AAAA,IACb;AAAA,EACA;AACA;ACvCA,SAAS,WAAW,SAAS,EAAE,OAAO,KAAM,GAAE,WAAW,YAAY;AACjE,SAAO,OAAO,QAAQ,OAAO,OAAO,cAAc,WAAW,oBAAoB,SAAS,CAAC;AAE3F,aAAW,OAAO,MAAM;AACpB,YAAQ,MAAM,YAAY,KAAK,KAAK,GAAG,CAAC;AAAA,EAChD;AACA;ACCA,SAAS2B,mBAAiB,SAAS;AAC/B,SAAO,OAAO,iBAAiB,OAAO;AAC1C;AACA,MAAM,0BAA0B,iBAAiB;AAAA,EAC7C,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO;AACZ,SAAK,iBAAiB;AAAA,EAC9B;AAAA,EACI,sBAAsB,UAAU,KAAK;AACjC,QAAI,eAAe,IAAI,GAAG,GAAG;AACzB,aAAO,KAAK,YAAY,eAClB,sBAAsB,GAAG,IACzB,mBAAmB,UAAU,GAAG;AAAA,IAClD,OACa;AACD,YAAM,gBAAgBA,mBAAiB,QAAQ;AAC/C,YAAM,SAAS,kBAAkB,GAAG,IAC9B,cAAc,iBAAiB,GAAG,IAClC,cAAc,GAAG,MAAM;AAC7B,aAAO,OAAO,UAAU,WAAW,MAAM,KAAM,IAAG;AAAA,IAC9D;AAAA,EACA;AAAA,EACI,2BAA2B,UAAU,EAAE,sBAAsB;AACzD,WAAO,mBAAmB,UAAU,kBAAkB;AAAA,EAC9D;AAAA,EACI,MAAM,aAAa,cAAc,OAAO;AACpC,oBAAgB,aAAa,cAAc,MAAM,iBAAiB;AAAA,EAC1E;AAAA,EACI,4BAA4B,OAAO,WAAW,eAAe;AACzD,WAAOjB,8BAA4B,OAAO,WAAW,aAAa;AAAA,EAC1E;AACA;ACpCA,MAAM,sBAAsB,oBAAI,IAAI;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,CAAC;ACvBD,SAAS,UAAU,SAAS,aAAa,YAAY,YAAY;AAC7D,aAAW,SAAS,aAAa,QAAW,UAAU;AACtD,aAAW,OAAO,YAAY,OAAO;AACjC,YAAQ,aAAa,CAAC,oBAAoB,IAAI,GAAG,IAAI,YAAY,GAAG,IAAI,KAAK,YAAY,MAAM,GAAG,CAAC;AAAA,EAC3G;AACA;ACCA,MAAM,yBAAyB,iBAAiB;AAAA,EAC5C,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,6BAA6B;AAAA,EAC1C;AAAA,EACI,uBAAuB,OAAO,KAAK;AAC/B,WAAO,MAAM,GAAG;AAAA,EACxB;AAAA,EACI,sBAAsB,UAAU,KAAK;AACjC,QAAI,eAAe,IAAI,GAAG,GAAG;AACzB,YAAM,cAAc,oBAAoB,GAAG;AAC3C,aAAO,cAAc,YAAY,WAAW,IAAI;AAAA,IAC5D;AACQ,UAAM,CAAC,oBAAoB,IAAI,GAAG,IAAI,YAAY,GAAG,IAAI;AACzD,WAAO,SAAS,aAAa,GAAG;AAAA,EACxC;AAAA,EACI,4BAA4B,OAAO,WAAW,eAAe;AACzD,WAAO,4BAA4B,OAAO,WAAW,aAAa;AAAA,EAC1E;AAAA,EACI,MAAM,aAAa,cAAc,OAAO;AACpC,kBAAc,aAAa,cAAc,KAAK,UAAU,MAAM,mBAAmB,MAAM,KAAK;AAAA,EACpG;AAAA,EACI,eAAe,UAAU,aAAa,WAAW,YAAY;AACzD,cAAU,UAAU,aAAa,WAAW,UAAU;AAAA,EAC9D;AAAA,EACI,MAAM,UAAU;AACZ,SAAK,WAAW,SAAS,SAAS,OAAO;AACzC,UAAM,MAAM,QAAQ;AAAA,EAC5B;AACA;ACpCA,MAAM,yBAAyB,CAAC,WAAW,YAAY;AACnD,SAAO,eAAe,SAAS,IACzB,IAAI,iBAAiB,OAAO,IAC5B,IAAI,kBAAkB,SAAS;AAAA,IAC7B,iBAAiB,cAAcF,aAAQ;AAAA,EACnD,CAAS;AACT;ACJA,MAAM,wBAAsC,6CAA6B;AAAA,EACrE,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACP,GAAG,sBAAsB;ACTpB,MAAC,SAAuB,8CAA8B,qBAAqB;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125]}