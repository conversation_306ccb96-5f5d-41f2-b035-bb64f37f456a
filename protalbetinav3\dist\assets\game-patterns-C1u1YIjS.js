import { I as IGameProcessor, b as AdvancedMetricsEngine } from "./services-Ckq1alRq.js";
import { r as reactExports, j as jsxDevRuntimeExports } from "./vendor-react-BH-kks1U.js";
import { S as SystemContext, b as useAccessibilityContext } from "./context-CJb-Kg-5.js";
import { G as GameStartScreen } from "./game-association-1fe4bzjE.js";
import { a as useUnifiedGameLogic, b as useMultisensoryIntegration, c as useTherapeuticOrchestrator } from "./hooks-DiB_syzW.js";
class PatternRecognitionCollector {
  constructor() {
    this.patternTypes = {
      sequential: "sequencial",
      spatial: "espacial",
      temporal: "temporal",
      geometric: "geométrico",
      color: "cromático",
      repetitive: "repetitivo"
    };
    this.recognitionComplexity = {
      simple: { level: 1, description: "padrões básicos" },
      moderate: { level: 2, description: "padrões intermediários" },
      complex: { level: 3, description: "padrões avançados" },
      expert: { level: 4, description: "padrões especializados" }
    };
    this.shapeCategories = {
      geometric: ["triangle", "square", "diamond"],
      symbolic: ["star", "heart"],
      basic: ["circle"]
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  async analyze(data) {
    if (!data || !data.patternData) {
      console.warn("PatternRecognitionCollector: Dados inválidos recebidos", data);
      return {
        sequenceRecognition: 0.7,
        patternIdentification: 0.7,
        visualAnalysis: 0.7,
        complexityHandling: 0.7,
        adaptiveRecognition: 0.7,
        patternMemory: 0.7,
        recognitionSpeed: 0.7,
        errorRecovery: 0.7
      };
    }
    return {
      sequenceRecognition: this.assessSequenceRecognition(data),
      patternIdentification: this.assessPatternIdentification(data),
      visualAnalysis: this.assessVisualAnalysis(data),
      complexityHandling: this.assessComplexityHandling(data),
      adaptiveRecognition: this.assessAdaptiveRecognition(data),
      patternMemory: this.assessPatternMemory(data),
      recognitionSpeed: this.assessRecognitionSpeed(data),
      errorRecovery: this.assessErrorRecovery(data),
      patternInsights: this.generatePatternInsights(data)
    };
  }
  assessSequenceRecognition(data) {
    const sequences = data.patternData.sequences || [];
    if (sequences.length === 0) return 0.7;
    let totalScore = 0;
    let validSequences = 0;
    sequences.forEach((sequence) => {
      if (sequence.targetSequence && sequence.playerSequence) {
        const accuracy = this.calculateSequenceAccuracy(sequence.targetSequence, sequence.playerSequence);
        const lengthFactor = Math.min(1, sequence.targetSequence.length / 3);
        const difficultyBonus = this.getDifficultyMultiplier(sequence.difficulty);
        const sequenceScore = accuracy * lengthFactor * difficultyBonus;
        totalScore += sequenceScore;
        validSequences++;
      }
    });
    const avgScore = validSequences > 0 ? totalScore / validSequences : 0.7;
    const consistencyBonus = this.calculateConsistency(sequences);
    return Math.max(0, Math.min(1, avgScore + consistencyBonus));
  }
  assessPatternIdentification(data) {
    const interactions = data.patternData.interactions || [];
    if (interactions.length === 0) return 0.7;
    const patternTypes = this.identifyPatternTypes(interactions);
    let identificationScore = 0;
    Object.keys(this.patternTypes).forEach((type) => {
      if (patternTypes[type]) {
        const typeAccuracy = patternTypes[type].correct / patternTypes[type].total;
        const complexityWeight = patternTypes[type].avgComplexity;
        identificationScore += typeAccuracy * complexityWeight;
      }
    });
    const testedTypes = Object.keys(patternTypes).length;
    const normalizedScore = testedTypes > 0 ? identificationScore / testedTypes : 0.7;
    return Math.max(0, Math.min(1, normalizedScore));
  }
  assessVisualAnalysis(data) {
    const interactions = data.patternData.interactions || [];
    if (interactions.length === 0) return 0.7;
    let visualScore = 0;
    let totalAnalyses = 0;
    interactions.forEach((interaction) => {
      if (interaction.visualProcessing) {
        const scanTime = interaction.responseTime || 2e3;
        const accuracy = interaction.isCorrect ? 1 : 0;
        const efficiency = this.calculateVisualEfficiency(scanTime, accuracy);
        const shapeComplexity = this.getShapeComplexity(interaction.shapeId);
        visualScore += efficiency * shapeComplexity;
        totalAnalyses++;
      }
    });
    const avgVisualScore = totalAnalyses > 0 ? visualScore / totalAnalyses : 0.7;
    const improvementFactor = this.calculateVisualImprovement(interactions);
    return Math.max(0, Math.min(1, avgVisualScore + improvementFactor));
  }
  assessComplexityHandling(data) {
    const sequences = data.patternData.sequences || [];
    if (sequences.length === 0) return 0.7;
    const complexityGroups = {
      simple: [],
      moderate: [],
      complex: [],
      expert: []
    };
    sequences.forEach((sequence) => {
      const complexity = this.determineSequenceComplexity(sequence);
      if (complexityGroups[complexity]) {
        complexityGroups[complexity].push(sequence);
      }
    });
    let complexityScore = 0;
    let totalLevels = 0;
    Object.keys(complexityGroups).forEach((level) => {
      const group = complexityGroups[level];
      if (group.length > 0) {
        const levelAccuracy = group.filter((s) => s.isCorrect).length / group.length;
        const levelWeight = this.recognitionComplexity[level].level;
        complexityScore += levelAccuracy * levelWeight;
        totalLevels++;
      }
    });
    const avgComplexityHandling = totalLevels > 0 ? complexityScore / (totalLevels * 2.5) : 0.7;
    return Math.max(0, Math.min(1, avgComplexityHandling));
  }
  assessAdaptiveRecognition(data) {
    const sequences = data.patternData.sequences || [];
    if (sequences.length < 3) return 0.7;
    const adaptationScores = [];
    for (let i = 1; i < sequences.length; i++) {
      const current = sequences[i];
      const previous = sequences[i - 1];
      const patternChange = this.detectPatternChange(previous, current);
      if (patternChange) {
        const adaptationTime = current.responseTime || 2e3;
        const adaptationAccuracy = current.isCorrect ? 1 : 0;
        const adaptationQuality = this.calculateAdaptationQuality(adaptationTime, adaptationAccuracy, patternChange.difficulty);
        adaptationScores.push(adaptationQuality);
      }
    }
    const avgAdaptation = adaptationScores.length > 0 ? adaptationScores.reduce((sum, score) => sum + score, 0) / adaptationScores.length : 0.7;
    return Math.max(0, Math.min(1, avgAdaptation));
  }
  assessPatternMemory(data) {
    const sequences = data.patternData.sequences || [];
    if (sequences.length === 0) return 0.7;
    let memoryScore = 0;
    let totalSequences = 0;
    sequences.forEach((sequence) => {
      const memoryLoad = sequence.targetSequence ? sequence.targetSequence.length : 3;
      const retention = sequence.isCorrect ? 1 : this.calculatePartialRetention(sequence);
      const memoryFactor = Math.min(1, memoryLoad / 5);
      const retentionScore = retention * memoryFactor;
      memoryScore += retentionScore;
      totalSequences++;
    });
    const avgMemoryScore = totalSequences > 0 ? memoryScore / totalSequences : 0.7;
    const longSequences = sequences.filter((s) => s.targetSequence && s.targetSequence.length >= 4);
    const longSequenceBonus = longSequences.length > 0 ? 0.1 : 0;
    return Math.max(0, Math.min(1, avgMemoryScore + longSequenceBonus));
  }
  assessRecognitionSpeed(data) {
    const interactions = data.patternData.interactions || [];
    if (interactions.length === 0) return 0.7;
    const responseTimes = interactions.filter((i) => i.responseTime && i.responseTime > 0).map((i) => i.responseTime);
    if (responseTimes.length === 0) return 0.7;
    const avgResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const speedScore = Math.max(0, Math.min(1, (3e3 - avgResponseTime) / 2e3));
    const timeVariability = this.calculateTimeVariability(responseTimes);
    const consistencyBonus = Math.max(0, (1 - timeVariability) * 0.2);
    return Math.max(0, Math.min(1, speedScore + consistencyBonus));
  }
  assessErrorRecovery(data) {
    const sequences = data.patternData.sequences || [];
    if (sequences.length === 0) return 0.7;
    const errorRecoveries = [];
    for (let i = 1; i < sequences.length; i++) {
      const previous = sequences[i - 1];
      const current = sequences[i];
      if (!previous.isCorrect && current.isCorrect) {
        const recoveryQuality = this.calculateRecoveryQuality(previous, current);
        errorRecoveries.push(recoveryQuality);
      }
    }
    if (errorRecoveries.length === 0) {
      const errorRate = sequences.filter((s) => !s.isCorrect).length / sequences.length;
      return errorRate < 0.2 ? 0.9 : 0.7;
    }
    const avgRecovery = errorRecoveries.reduce((sum, recovery) => sum + recovery, 0) / errorRecoveries.length;
    return Math.max(0, Math.min(1, avgRecovery));
  }
  // Métodos auxiliares
  calculateSequenceAccuracy(target, player) {
    if (!target || !player) return 0;
    const minLength = Math.min(target.length, player.length);
    let matches = 0;
    for (let i = 0; i < minLength; i++) {
      if (target[i] === player[i]) {
        matches++;
      }
    }
    return matches / target.length;
  }
  getDifficultyMultiplier(difficulty) {
    const multipliers = { easy: 1, medium: 1.2, hard: 1.4 };
    return multipliers[difficulty] || 1;
  }
  calculateConsistency(sequences) {
    if (sequences.length < 3) return 0;
    const accuracies = sequences.map((s) => s.isCorrect ? 1 : 0);
    const variance = this.calculateVariance(accuracies);
    return Math.max(0, (1 - variance) * 0.15);
  }
  identifyPatternTypes(interactions) {
    const types = {};
    interactions.forEach((interaction) => {
      const patternType = this.inferPatternType(interaction);
      if (!types[patternType]) {
        types[patternType] = { correct: 0, total: 0, complexities: [] };
      }
      types[patternType].total++;
      if (interaction.isCorrect) types[patternType].correct++;
      types[patternType].complexities.push(this.getShapeComplexity(interaction.shapeId));
    });
    Object.keys(types).forEach((type) => {
      const complexities = types[type].complexities;
      types[type].avgComplexity = complexities.reduce((sum, c) => sum + c, 0) / complexities.length;
    });
    return types;
  }
  calculateVisualEfficiency(scanTime, accuracy) {
    const speedScore = Math.max(0, Math.min(1, (3e3 - scanTime) / 2e3));
    const combinedScore = speedScore * 0.6 + accuracy * 0.4;
    return combinedScore;
  }
  getShapeComplexity(shapeId) {
    const complexities = {
      circle: 1,
      square: 1.1,
      triangle: 1.2,
      star: 1.3,
      diamond: 1.4,
      heart: 1.5
    };
    return complexities[shapeId] || 1;
  }
  calculateVisualImprovement(interactions) {
    if (interactions.length < 5) return 0;
    const firstHalf = interactions.slice(0, Math.floor(interactions.length / 2));
    const secondHalf = interactions.slice(Math.floor(interactions.length / 2));
    const firstAccuracy = firstHalf.filter((i) => i.isCorrect).length / firstHalf.length;
    const secondAccuracy = secondHalf.filter((i) => i.isCorrect).length / secondHalf.length;
    const improvement = secondAccuracy - firstAccuracy;
    return Math.max(0, Math.min(0.2, improvement));
  }
  determineSequenceComplexity(sequence) {
    const length = sequence.targetSequence ? sequence.targetSequence.length : 3;
    const difficulty = sequence.difficulty || "easy";
    if (length <= 3 && difficulty === "easy") return "simple";
    if (length <= 4 && difficulty !== "hard") return "moderate";
    if (length <= 5) return "complex";
    return "expert";
  }
  detectPatternChange(previous, current) {
    const prevLength = previous.targetSequence ? previous.targetSequence.length : 3;
    const currLength = current.targetSequence ? current.targetSequence.length : 3;
    if (prevLength !== currLength) {
      return { type: "length", difficulty: Math.abs(currLength - prevLength) };
    }
    if (previous.difficulty !== current.difficulty) {
      return { type: "difficulty", difficulty: 1 };
    }
    return null;
  }
  calculateAdaptationQuality(time, accuracy, changeDifficulty) {
    const timeScore = Math.max(0, Math.min(1, (4e3 - time) / 3e3));
    const difficultyBonus = changeDifficulty * 0.1;
    return accuracy * 0.7 + timeScore * 0.3 + difficultyBonus;
  }
  calculatePartialRetention(sequence) {
    if (!sequence.targetSequence || !sequence.playerSequence) return 0;
    const matches = this.calculateSequenceAccuracy(sequence.targetSequence, sequence.playerSequence);
    return matches;
  }
  calculateTimeVariability(times) {
    if (times.length < 2) return 0;
    const mean = times.reduce((sum, time) => sum + time, 0) / times.length;
    const variance = times.reduce((sum, time) => sum + Math.pow(time - mean, 2), 0) / times.length;
    const standardDeviation = Math.sqrt(variance);
    return Math.min(1, standardDeviation / mean);
  }
  calculateRecoveryQuality(errorSequence, recoverySequence) {
    this.determineSequenceComplexity(errorSequence);
    const recoveryTime = recoverySequence.responseTime || 2e3;
    const recoveryAccuracy = recoverySequence.isCorrect ? 1 : 0;
    const timeBonus = Math.max(0, (3e3 - recoveryTime) / 3e3) * 0.3;
    return recoveryAccuracy * 0.7 + timeBonus;
  }
  calculateVariance(values) {
    if (values.length === 0) return 0;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return variance;
  }
  inferPatternType(interaction) {
    if (interaction.sequencePosition !== void 0) return "sequential";
    if (interaction.shapeId) return "geometric";
    return "temporal";
  }
  generatePatternInsights(data) {
    const insights = [];
    const results = data.patternData;
    if (!results) return insights;
    if (results.sequenceRecognition < 0.6) {
      insights.push("Dificuldades no reconhecimento de sequências");
    }
    if (results.patternIdentification < 0.6) {
      insights.push("Limitações na identificação de padrões específicos");
    }
    if (results.visualAnalysis < 0.5) {
      insights.push("Necessita melhorar análise visual");
    }
    if (results.complexityHandling < 0.5) {
      insights.push("Dificuldades com padrões complexos");
    }
    if (results.adaptiveRecognition < 0.6) {
      insights.push("Baixa adaptabilidade a novos padrões");
    }
    if (results.patternMemory < 0.5) {
      insights.push("Limitações na memória de padrões");
    }
    if (results.recognitionSpeed < 0.5) {
      insights.push("Velocidade de reconhecimento reduzida");
    }
    if (results.errorRecovery < 0.5) {
      insights.push("Dificuldades na recuperação após erros");
    }
    return insights;
  }
}
class VisualMemoryCollector {
  constructor() {
    this.memoryTypes = {
      shortTerm: "memória de curto prazo",
      workingMemory: "memória de trabalho",
      visualBuffer: "buffer visual",
      spatialMemory: "memória espacial",
      sequentialMemory: "memória sequencial"
    };
    this.memoryCapacity = {
      low: { threshold: 3, description: "capacidade limitada" },
      medium: { threshold: 5, description: "capacidade moderada" },
      high: { threshold: 7, description: "capacidade alta" },
      exceptional: { threshold: 10, description: "capacidade excepcional" }
    };
    this.retentionPeriods = {
      immediate: { max: 1e3, weight: 1 },
      short: { max: 5e3, weight: 1.2 },
      medium: { max: 15e3, weight: 1.4 },
      long: { max: 3e4, weight: 1.6 }
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  async analyze(data) {
    if (!data || !data.memoryData) {
      console.warn("VisualMemoryCollector: Dados inválidos recebidos", data);
      return {
        shortTermMemory: 0.7,
        workingMemory: 0.7,
        visualBuffer: 0.7,
        spatialMemory: 0.7,
        sequentialMemory: 0.7,
        memoryCapacity: 0.7,
        retentionQuality: 0.7,
        interferenceResistance: 0.7
      };
    }
    return {
      shortTermMemory: this.assessShortTermMemory(data),
      workingMemory: this.assessWorkingMemory(data),
      visualBuffer: this.assessVisualBuffer(data),
      spatialMemory: this.assessSpatialMemory(data),
      sequentialMemory: this.assessSequentialMemory(data),
      memoryCapacity: this.assessMemoryCapacity(data),
      retentionQuality: this.assessRetentionQuality(data),
      interferenceResistance: this.assessInterferenceResistance(data),
      memoryStrategies: this.identifyMemoryStrategies(data),
      memoryInsights: this.generateMemoryInsights(data)
    };
  }
  assessShortTermMemory(data) {
    const sequences = data.memoryData.sequences || [];
    if (sequences.length === 0) return 0.7;
    const shortSequences = sequences.filter((s) => {
      const length = s.targetSequence ? s.targetSequence.length : 0;
      const retentionTime = s.showTime || 5e3;
      return length <= 4 && retentionTime <= 1e4;
    });
    if (shortSequences.length === 0) return 0.7;
    let memoryScore = 0;
    shortSequences.forEach((sequence) => {
      const accuracy = this.calculateSequenceAccuracy(sequence);
      const retentionTime = sequence.showTime || 5e3;
      const memoryLoad = sequence.targetSequence ? sequence.targetSequence.length : 3;
      const retentionFactor = this.getRetentionFactor(retentionTime);
      const capacityFactor = Math.min(1, memoryLoad / 4);
      const shortTermScore = accuracy * retentionFactor * capacityFactor;
      memoryScore += shortTermScore;
    });
    const avgMemoryScore = memoryScore / shortSequences.length;
    const consistencyBonus = this.calculateMemoryConsistency(shortSequences);
    return Math.max(0, Math.min(1, avgMemoryScore + consistencyBonus));
  }
  assessWorkingMemory(data) {
    const sequences = data.memoryData.sequences || [];
    if (sequences.length === 0) return 0.7;
    const workingMemoryTasks = sequences.filter((s) => {
      const length = s.targetSequence ? s.targetSequence.length : 0;
      const complexity = this.determineSequenceComplexity(s);
      return length >= 4 || complexity > 1;
    });
    if (workingMemoryTasks.length === 0) return 0.7;
    let workingScore = 0;
    workingMemoryTasks.forEach((sequence) => {
      const accuracy = this.calculateSequenceAccuracy(sequence);
      const processingTime = sequence.responseTime || 2e3;
      const memoryLoad = sequence.targetSequence ? sequence.targetSequence.length : 3;
      const processingEfficiency = this.calculateProcessingEfficiency(processingTime, memoryLoad);
      const workingCapacity = this.assessWorkingCapacity(memoryLoad, accuracy);
      const workingMemoryScore = accuracy * 0.5 + processingEfficiency * 0.3 + workingCapacity * 0.2;
      workingScore += workingMemoryScore;
    });
    const avgWorkingScore = workingScore / workingMemoryTasks.length;
    return Math.max(0, Math.min(1, avgWorkingScore));
  }
  assessVisualBuffer(data) {
    const interactions = data.memoryData.interactions || [];
    if (interactions.length === 0) return 0.7;
    let bufferScore = 0;
    let validInteractions = 0;
    interactions.forEach((interaction) => {
      if (interaction.visualProcessing) {
        const visualComplexity = this.getVisualComplexity(interaction.shapeId);
        const processingTime = interaction.responseTime || 2e3;
        const accuracy = interaction.isCorrect ? 1 : 0;
        const bufferEfficiency = this.calculateBufferEfficiency(processingTime, visualComplexity, accuracy);
        bufferScore += bufferEfficiency;
        validInteractions++;
      }
    });
    if (validInteractions === 0) return 0.7;
    const avgBufferScore = bufferScore / validInteractions;
    const degradationFactor = this.assessBufferDegradation(interactions);
    return Math.max(0, Math.min(1, avgBufferScore * degradationFactor));
  }
  assessSpatialMemory(data) {
    const sequences = data.memoryData.sequences || [];
    if (sequences.length === 0) return 0.7;
    let spatialScore = 0;
    let spatialTasks = 0;
    sequences.forEach((sequence) => {
      if (sequence.spatialData) {
        const positionAccuracy = this.calculatePositionAccuracy(sequence.spatialData);
        const spatialComplexity = this.getSpatialComplexity(sequence.spatialData);
        const spatialMemoryScore = positionAccuracy * spatialComplexity;
        spatialScore += spatialMemoryScore;
        spatialTasks++;
      } else if (sequence.targetSequence) {
        const inferredSpatial = this.inferSpatialComponent(sequence);
        spatialScore += inferredSpatial;
        spatialTasks++;
      }
    });
    if (spatialTasks === 0) return 0.7;
    const avgSpatialScore = spatialScore / spatialTasks;
    return Math.max(0, Math.min(1, avgSpatialScore));
  }
  assessSequentialMemory(data) {
    const sequences = data.memoryData.sequences || [];
    if (sequences.length === 0) return 0.7;
    let sequentialScore = 0;
    sequences.forEach((sequence) => {
      const orderAccuracy = this.calculateOrderAccuracy(sequence);
      const sequenceLength = sequence.targetSequence ? sequence.targetSequence.length : 3;
      const temporalComplexity = this.getTemporalComplexity(sequence);
      const lengthFactor = Math.min(1, sequenceLength / 5);
      const sequentialMemoryScore = orderAccuracy * lengthFactor * temporalComplexity;
      sequentialScore += sequentialMemoryScore;
    });
    const avgSequentialScore = sequentialScore / sequences.length;
    const longSequences = sequences.filter((s) => s.targetSequence && s.targetSequence.length >= 5);
    const longSequenceBonus = longSequences.length > 0 ? 0.1 : 0;
    return Math.max(0, Math.min(1, avgSequentialScore + longSequenceBonus));
  }
  assessMemoryCapacity(data) {
    const sequences = data.memoryData.sequences || [];
    if (sequences.length === 0) return 0.7;
    const successfulLengths = sequences.filter((s) => s.isCorrect && s.targetSequence).map((s) => s.targetSequence.length);
    if (successfulLengths.length === 0) return 0.7;
    const maxSuccessfulLength = Math.max(...successfulLengths);
    const avgSuccessfulLength = successfulLengths.reduce((sum, len) => sum + len, 0) / successfulLengths.length;
    const capacityScore = Math.min(1, avgSuccessfulLength / 7);
    const maxCapacityBonus = Math.min(0.2, maxSuccessfulLength / 10);
    return Math.max(0, Math.min(1, capacityScore + maxCapacityBonus));
  }
  assessRetentionQuality(data) {
    const sequences = data.memoryData.sequences || [];
    if (sequences.length === 0) return 0.7;
    let retentionScore = 0;
    sequences.forEach((sequence) => {
      const showTime = sequence.showTime || 5e3;
      const accuracy = this.calculateSequenceAccuracy(sequence);
      const retentionPeriod = this.determineRetentionPeriod(showTime);
      const retentionWeight = this.retentionPeriods[retentionPeriod].weight;
      const qualityScore = accuracy * retentionWeight;
      retentionScore += qualityScore;
    });
    const avgRetentionScore = retentionScore / sequences.length;
    const maxWeight = Math.max(...Object.values(this.retentionPeriods).map((p) => p.weight));
    const normalizedScore = avgRetentionScore / maxWeight;
    return Math.max(0, Math.min(1, normalizedScore));
  }
  assessInterferenceResistance(data) {
    const sequences = data.memoryData.sequences || [];
    if (sequences.length < 3) return 0.7;
    let interferenceScores = [];
    for (let i = 1; i < sequences.length; i++) {
      const current = sequences[i];
      const previous = sequences[i - 1];
      const interference = this.calculateInterference(previous, current);
      if (interference > 0) {
        const resistanceScore = current.isCorrect ? 1 : 0;
        const weightedResistance = resistanceScore * (1 + interference);
        interferenceScores.push(weightedResistance);
      }
    }
    if (interferenceScores.length === 0) return 0.8;
    const avgResistance = interferenceScores.reduce((sum, score) => sum + score, 0) / interferenceScores.length;
    return Math.max(0, Math.min(1, avgResistance));
  }
  // Métodos auxiliares
  calculateSequenceAccuracy(sequence) {
    if (!sequence.targetSequence || !sequence.playerSequence) return 0;
    const target = sequence.targetSequence;
    const player = sequence.playerSequence;
    const minLength = Math.min(target.length, player.length);
    let matches = 0;
    for (let i = 0; i < minLength; i++) {
      if (target[i] === player[i]) {
        matches++;
      }
    }
    return matches / target.length;
  }
  getRetentionFactor(retentionTime) {
    if (retentionTime <= 3e3) return 1;
    if (retentionTime <= 8e3) return 0.9;
    if (retentionTime <= 15e3) return 0.8;
    return 0.7;
  }
  calculateMemoryConsistency(sequences) {
    if (sequences.length < 3) return 0;
    const accuracies = sequences.map((s) => this.calculateSequenceAccuracy(s));
    const variance = this.calculateVariance(accuracies);
    return Math.max(0, (1 - variance) * 0.15);
  }
  determineSequenceComplexity(sequence) {
    const length = sequence.targetSequence ? sequence.targetSequence.length : 3;
    const uniqueShapes = sequence.targetSequence ? new Set(sequence.targetSequence).size : 1;
    const lengthComplexity = Math.min(2, length / 3);
    const varietyComplexity = Math.min(1.5, uniqueShapes / 2);
    return lengthComplexity + varietyComplexity;
  }
  calculateProcessingEfficiency(processingTime, memoryLoad) {
    const expectedTime = 1e3 + memoryLoad * 500;
    const efficiency = Math.max(0, Math.min(1, expectedTime / processingTime));
    return efficiency;
  }
  assessWorkingCapacity(memoryLoad, accuracy) {
    const loadFactor = Math.min(1, memoryLoad / 6);
    const capacityScore = accuracy * loadFactor;
    return capacityScore;
  }
  getVisualComplexity(shapeId) {
    const complexities = {
      circle: 1,
      square: 1.1,
      triangle: 1.2,
      star: 1.4,
      diamond: 1.3,
      heart: 1.5
    };
    return complexities[shapeId] || 1;
  }
  calculateBufferEfficiency(processingTime, visualComplexity, accuracy) {
    const timeScore = Math.max(0, Math.min(1, (3e3 - processingTime) / 2e3));
    const complexityBonus = visualComplexity * 0.1;
    return accuracy * 0.6 + timeScore * 0.4 + complexityBonus;
  }
  assessBufferDegradation(interactions) {
    if (interactions.length < 5) return 1;
    const firstQuarter = interactions.slice(0, Math.floor(interactions.length / 4));
    const lastQuarter = interactions.slice(-Math.floor(interactions.length / 4));
    const firstAccuracy = firstQuarter.filter((i) => i.isCorrect).length / firstQuarter.length;
    const lastAccuracy = lastQuarter.filter((i) => i.isCorrect).length / lastQuarter.length;
    const degradation = firstAccuracy - lastAccuracy;
    const degradationFactor = Math.max(0.7, 1 - Math.max(0, degradation));
    return degradationFactor;
  }
  calculatePositionAccuracy(spatialData) {
    if (!spatialData.targetPositions || !spatialData.playerPositions) return 0.7;
    let matches = 0;
    const minLength = Math.min(spatialData.targetPositions.length, spatialData.playerPositions.length);
    for (let i = 0; i < minLength; i++) {
      const targetPos = spatialData.targetPositions[i];
      const playerPos = spatialData.playerPositions[i];
      const distance = Math.sqrt(
        Math.pow(targetPos.x - playerPos.x, 2) + Math.pow(targetPos.y - playerPos.y, 2)
      );
      if (distance <= 50) {
        matches++;
      }
    }
    return matches / spatialData.targetPositions.length;
  }
  getSpatialComplexity(spatialData) {
    if (!spatialData.targetPositions) return 1;
    const positions = spatialData.targetPositions;
    const spread = this.calculateSpatialSpread(positions);
    const density = this.calculateSpatialDensity(positions);
    return Math.min(2, spread + density);
  }
  inferSpatialComponent(sequence) {
    if (!sequence.targetSequence) return 0.7;
    const uniqueShapes = new Set(sequence.targetSequence).size;
    const totalShapes = sequence.targetSequence.length;
    const spatialDemand = uniqueShapes / totalShapes;
    const accuracy = this.calculateSequenceAccuracy(sequence);
    return accuracy * spatialDemand;
  }
  calculateOrderAccuracy(sequence) {
    if (!sequence.targetSequence || !sequence.playerSequence) return 0;
    const target = sequence.targetSequence;
    const player = sequence.playerSequence;
    let correctOrder = 0;
    const minLength = Math.min(target.length, player.length);
    for (let i = 0; i < minLength; i++) {
      if (target[i] === player[i]) {
        correctOrder++;
      } else {
        break;
      }
    }
    return correctOrder / target.length;
  }
  getTemporalComplexity(sequence) {
    const length = sequence.targetSequence ? sequence.targetSequence.length : 3;
    const showTime = sequence.showTime || 5e3;
    const timePerItem = showTime / length;
    const timeComplexity = Math.min(1.5, 5e3 / timePerItem);
    return timeComplexity;
  }
  determineRetentionPeriod(showTime) {
    if (showTime <= this.retentionPeriods.immediate.max) return "immediate";
    if (showTime <= this.retentionPeriods.short.max) return "short";
    if (showTime <= this.retentionPeriods.medium.max) return "medium";
    return "long";
  }
  calculateInterference(previous, current) {
    if (!previous.targetSequence || !current.targetSequence) return 0;
    const prevSet = new Set(previous.targetSequence);
    const currSet = new Set(current.targetSequence);
    let commonElements = 0;
    prevSet.forEach((element) => {
      if (currSet.has(element)) {
        commonElements++;
      }
    });
    const totalElements = (/* @__PURE__ */ new Set([...previous.targetSequence, ...current.targetSequence])).size;
    const similarity = commonElements / totalElements;
    return similarity;
  }
  calculateVariance(values) {
    if (values.length === 0) return 0;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return variance;
  }
  calculateSpatialSpread(positions) {
    if (positions.length < 2) return 1;
    const xCoords = positions.map((p) => p.x);
    const yCoords = positions.map((p) => p.y);
    const xRange = Math.max(...xCoords) - Math.min(...xCoords);
    const yRange = Math.max(...yCoords) - Math.min(...yCoords);
    return Math.min(1.5, (xRange + yRange) / 1e3);
  }
  calculateSpatialDensity(positions) {
    if (positions.length < 2) return 1;
    let totalDistance = 0;
    let pairs = 0;
    for (let i = 0; i < positions.length; i++) {
      for (let j = i + 1; j < positions.length; j++) {
        const distance = Math.sqrt(
          Math.pow(positions[i].x - positions[j].x, 2) + Math.pow(positions[i].y - positions[j].y, 2)
        );
        totalDistance += distance;
        pairs++;
      }
    }
    const avgDistance = pairs > 0 ? totalDistance / pairs : 100;
    return Math.min(1.5, 200 / avgDistance);
  }
  identifyMemoryStrategies(data) {
    const sequences = data.memoryData.sequences || [];
    const strategies = [];
    sequences.map((s) => this.calculateSequenceAccuracy(s));
    if (this.detectChunkingStrategy(sequences)) {
      strategies.push("chunking");
    }
    if (this.detectRehearsalStrategy(sequences)) {
      strategies.push("rehearsal");
    }
    if (this.detectVisualStrategy(sequences)) {
      strategies.push("visual_encoding");
    }
    if (this.detectSequentialStrategy(sequences)) {
      strategies.push("sequential_organization");
    }
    return strategies;
  }
  detectChunkingStrategy(sequences) {
    const longSequences = sequences.filter((s) => s.targetSequence && s.targetSequence.length >= 4);
    if (longSequences.length < 2) return false;
    const firstHalf = longSequences.slice(0, Math.floor(longSequences.length / 2));
    const secondHalf = longSequences.slice(Math.floor(longSequences.length / 2));
    const firstAccuracy = firstHalf.reduce((sum, s) => sum + this.calculateSequenceAccuracy(s), 0) / firstHalf.length;
    const secondAccuracy = secondHalf.reduce((sum, s) => sum + this.calculateSequenceAccuracy(s), 0) / secondHalf.length;
    return secondAccuracy > firstAccuracy + 0.1;
  }
  detectRehearsalStrategy(sequences) {
    const responseTimes = sequences.filter((s) => s.responseTime).map((s) => s.responseTime);
    if (responseTimes.length < 3) return false;
    const avgTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const longTimes = responseTimes.filter((time) => time > avgTime * 1.2);
    return longTimes.length > responseTimes.length * 0.3;
  }
  detectVisualStrategy(sequences) {
    let visuallyDistinctCorrect = 0;
    let visuallySimilarCorrect = 0;
    let distinctTotal = 0;
    let similarTotal = 0;
    sequences.forEach((sequence) => {
      if (sequence.targetSequence) {
        const uniqueShapes = new Set(sequence.targetSequence).size;
        const isVisuallyDistinct = uniqueShapes / sequence.targetSequence.length > 0.7;
        if (isVisuallyDistinct) {
          distinctTotal++;
          if (sequence.isCorrect) visuallyDistinctCorrect++;
        } else {
          similarTotal++;
          if (sequence.isCorrect) visuallySimilarCorrect++;
        }
      }
    });
    if (distinctTotal === 0 || similarTotal === 0) return false;
    const distinctAccuracy = visuallyDistinctCorrect / distinctTotal;
    const similarAccuracy = visuallySimilarCorrect / similarTotal;
    return distinctAccuracy > similarAccuracy + 0.15;
  }
  detectSequentialStrategy(sequences) {
    let sequentialErrors = 0;
    let totalSequences = 0;
    sequences.forEach((sequence) => {
      if (sequence.targetSequence && sequence.playerSequence) {
        totalSequences++;
        const orderAccuracy = this.calculateOrderAccuracy(sequence);
        const totalAccuracy = this.calculateSequenceAccuracy(sequence);
        if (orderAccuracy < totalAccuracy - 0.2) {
          sequentialErrors++;
        }
      }
    });
    if (totalSequences === 0) return false;
    return sequentialErrors / totalSequences < 0.3;
  }
  generateMemoryInsights(data) {
    const insights = [];
    const sequences = data.memoryData.sequences || [];
    if (sequences.length === 0) return insights;
    const maxLength = Math.max(...sequences.filter((s) => s.isCorrect && s.targetSequence).map((s) => s.targetSequence.length));
    if (maxLength <= 3) {
      insights.push("Capacidade de memória visual limitada a sequências curtas");
    } else if (maxLength >= 6) {
      insights.push("Excelente capacidade de memória visual para sequências longas");
    }
    const accuracies = sequences.map((s) => this.calculateSequenceAccuracy(s));
    const variance = this.calculateVariance(accuracies);
    if (variance > 0.3) {
      insights.push("Desempenho inconsistente na memória visual");
    } else if (variance < 0.1) {
      insights.push("Desempenho muito consistente na memória visual");
    }
    const strategies = this.identifyMemoryStrategies(data);
    if (strategies.length > 0) {
      insights.push(`Estratégias de memória identificadas: ${strategies.join(", ")}`);
    }
    return insights;
  }
}
class SpatialProcessingCollector {
  constructor() {
    this.spatialDomains = {
      visualization: "visualização espacial",
      orientation: "orientação espacial",
      rotation: "rotação mental",
      positioning: "posicionamento",
      navigation: "navegação espacial",
      transformation: "transformação espacial"
    };
    this.spatialComplexity = {
      basic: { level: 1, description: "relações espaciais básicas" },
      intermediate: { level: 2, description: "transformações moderadas" },
      advanced: { level: 3, description: "manipulações complexas" },
      expert: { level: 4, description: "processamento espacial avançado" }
    };
    this.spatialDimensions = {
      position: "posição absoluta",
      relative: "posição relativa",
      distance: "distância espacial",
      direction: "direção",
      scale: "escala",
      symmetry: "simetria"
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  async analyze(data) {
    if (!data || !data.spatialData) {
      console.warn("SpatialProcessingCollector: Dados inválidos recebidos", data);
      return {
        spatialVisualization: 0.7,
        spatialOrientation: 0.7,
        spatialTransformation: 0.7,
        spatialMemory: 0.7,
        spatialReasoning: 0.7,
        spatialNavigation: 0.7,
        spatialAccuracy: 0.7,
        spatialEfficiency: 0.7
      };
    }
    return {
      spatialVisualization: this.assessSpatialVisualization(data),
      spatialOrientation: this.assessSpatialOrientation(data),
      spatialTransformation: this.assessSpatialTransformation(data),
      spatialMemory: this.assessSpatialMemory(data),
      spatialReasoning: this.assessSpatialReasoning(data),
      spatialNavigation: this.assessSpatialNavigation(data),
      spatialAccuracy: this.assessSpatialAccuracy(data),
      spatialEfficiency: this.assessSpatialEfficiency(data),
      spatialStrategies: this.identifySpatialStrategies(data),
      spatialInsights: this.generateSpatialInsights(data)
    };
  }
  assessSpatialVisualization(data) {
    const interactions = data.spatialData.interactions || [];
    if (interactions.length === 0) return 0.7;
    let visualizationScore = 0;
    let validInteractions = 0;
    interactions.forEach((interaction) => {
      if (interaction.spatialVisualization) {
        const complexity = this.getSpatialComplexity(interaction);
        const accuracy = interaction.isCorrect ? 1 : 0;
        const processingTime = interaction.responseTime || 2e3;
        const visualizationQuality = this.calculateVisualizationQuality(accuracy, processingTime, complexity);
        visualizationScore += visualizationQuality;
        validInteractions++;
      }
    });
    if (validInteractions === 0) {
      return this.inferVisualizationFromSequences(data);
    }
    const avgVisualizationScore = visualizationScore / validInteractions;
    const consistencyBonus = this.calculateVisualizationConsistency(interactions);
    return Math.max(0, Math.min(1, avgVisualizationScore + consistencyBonus));
  }
  assessSpatialOrientation(data) {
    const sequences = data.spatialData.sequences || [];
    if (sequences.length === 0) return 0.7;
    let orientationScore = 0;
    sequences.forEach((sequence) => {
      if (sequence.spatialLayout) {
        const orientationAccuracy = this.calculateOrientationAccuracy(sequence.spatialLayout);
        const layoutComplexity = this.getLayoutComplexity(sequence.spatialLayout);
        const orientationQuality = orientationAccuracy * layoutComplexity;
        orientationScore += orientationQuality;
      } else {
        const inferredOrientation = this.inferOrientationFromSequence(sequence);
        orientationScore += inferredOrientation;
      }
    });
    const avgOrientationScore = orientationScore / sequences.length;
    return Math.max(0, Math.min(1, avgOrientationScore));
  }
  assessSpatialTransformation(data) {
    const interactions = data.spatialData.interactions || [];
    if (interactions.length === 0) return 0.7;
    const transformations = this.identifyTransformations(interactions);
    if (transformations.length === 0) return 0.7;
    let transformationScore = 0;
    transformations.forEach((transform) => {
      const accuracy = transform.isCorrect ? 1 : 0;
      const difficulty = this.getTransformationDifficulty(transform.type);
      const processingTime = transform.responseTime || 2e3;
      const transformationQuality = this.calculateTransformationQuality(accuracy, processingTime, difficulty);
      transformationScore += transformationQuality;
    });
    const avgTransformationScore = transformationScore / transformations.length;
    return Math.max(0, Math.min(1, avgTransformationScore));
  }
  assessSpatialMemory(data) {
    const sequences = data.spatialData.sequences || [];
    if (sequences.length === 0) return 0.7;
    let spatialMemoryScore = 0;
    sequences.forEach((sequence) => {
      const memoryLoad = this.calculateSpatialMemoryLoad(sequence);
      const retentionQuality = this.calculateSpatialRetention(sequence);
      const interferenceResistance = this.calculateSpatialInterferenceResistance(sequence);
      const memoryQuality = retentionQuality * 0.5 + interferenceResistance * 0.3 + memoryLoad * 0.2;
      spatialMemoryScore += memoryQuality;
    });
    const avgSpatialMemoryScore = spatialMemoryScore / sequences.length;
    return Math.max(0, Math.min(1, avgSpatialMemoryScore));
  }
  assessSpatialReasoning(data) {
    const sequences = data.spatialData.sequences || [];
    if (sequences.length === 0) return 0.7;
    let reasoningScore = 0;
    sequences.forEach((sequence) => {
      const logicalComplexity = this.calculateLogicalComplexity(sequence);
      const reasoningAccuracy = this.calculateReasoningAccuracy(sequence);
      const inferenceQuality = this.assessInferenceQuality(sequence);
      const reasoningQuality = reasoningAccuracy * 0.4 + logicalComplexity * 0.3 + inferenceQuality * 0.3;
      reasoningScore += reasoningQuality;
    });
    const avgReasoningScore = reasoningScore / sequences.length;
    return Math.max(0, Math.min(1, avgReasoningScore));
  }
  assessSpatialNavigation(data) {
    const interactions = data.spatialData.interactions || [];
    if (interactions.length === 0) return 0.7;
    const navigationPatterns = this.identifyNavigationPatterns(interactions);
    let navigationScore = 0;
    navigationPatterns.forEach((pattern) => {
      const efficiency = this.calculateNavigationEfficiency(pattern);
      const accuracy = pattern.accuracy || 0.7;
      const pathOptimization = this.assessPathOptimization(pattern);
      const navigationQuality = efficiency * 0.4 + accuracy * 0.4 + pathOptimization * 0.2;
      navigationScore += navigationQuality;
    });
    if (navigationPatterns.length === 0) return 0.7;
    const avgNavigationScore = navigationScore / navigationPatterns.length;
    return Math.max(0, Math.min(1, avgNavigationScore));
  }
  assessSpatialAccuracy(data) {
    const interactions = data.spatialData.interactions || [];
    if (interactions.length === 0) return 0.7;
    let accuracyScores = [];
    interactions.forEach((interaction) => {
      if (interaction.spatialPosition) {
        const positionAccuracy = this.calculatePositionAccuracy(interaction.spatialPosition);
        const distanceAccuracy = this.calculateDistanceAccuracy(interaction.spatialPosition);
        const directionAccuracy = this.calculateDirectionAccuracy(interaction.spatialPosition);
        const overallAccuracy = positionAccuracy * 0.4 + distanceAccuracy * 0.3 + directionAccuracy * 0.3;
        accuracyScores.push(overallAccuracy);
      }
    });
    if (accuracyScores.length === 0) {
      return this.inferAccuracyFromSequences(data);
    }
    const avgAccuracy = accuracyScores.reduce((sum, acc) => sum + acc, 0) / accuracyScores.length;
    return Math.max(0, Math.min(1, avgAccuracy));
  }
  assessSpatialEfficiency(data) {
    const interactions = data.spatialData.interactions || [];
    if (interactions.length === 0) return 0.7;
    let efficiencyScores = [];
    interactions.forEach((interaction) => {
      const processingTime = interaction.responseTime || 2e3;
      const spatialComplexity = this.getSpatialComplexity(interaction);
      const accuracy = interaction.isCorrect ? 1 : 0;
      const efficiency = this.calculateSpatialEfficiency(processingTime, accuracy, spatialComplexity);
      efficiencyScores.push(efficiency);
    });
    const avgEfficiency = efficiencyScores.reduce((sum, eff) => sum + eff, 0) / efficiencyScores.length;
    const improvementFactor = this.calculateEfficiencyImprovement(efficiencyScores);
    return Math.max(0, Math.min(1, avgEfficiency + improvementFactor));
  }
  // Métodos auxiliares
  getSpatialComplexity(interaction) {
    let complexity = 1;
    if (interaction.shapeId) {
      const shapeComplexities = {
        circle: 1,
        square: 1.1,
        triangle: 1.2,
        diamond: 1.3,
        star: 1.4,
        heart: 1.5
      };
      complexity *= shapeComplexities[interaction.shapeId] || 1;
    }
    if (interaction.position) {
      const positionComplexity = this.calculatePositionComplexity(interaction.position);
      complexity *= positionComplexity;
    }
    return Math.min(2, complexity);
  }
  calculateVisualizationQuality(accuracy, processingTime, complexity) {
    const timeScore = Math.max(0, Math.min(1, (4e3 - processingTime) / 3e3));
    const complexityBonus = (complexity - 1) * 0.1;
    return accuracy * 0.6 + timeScore * 0.4 + complexityBonus;
  }
  calculateVisualizationConsistency(interactions) {
    if (interactions.length < 3) return 0;
    const visualizationQualities = interactions.filter((i) => i.spatialVisualization).map((i) => {
      const accuracy = i.isCorrect ? 1 : 0;
      const complexity = this.getSpatialComplexity(i);
      return this.calculateVisualizationQuality(accuracy, i.responseTime || 2e3, complexity);
    });
    if (visualizationQualities.length === 0) return 0;
    const variance = this.calculateVariance(visualizationQualities);
    return Math.max(0, (1 - variance) * 0.15);
  }
  inferVisualizationFromSequences(data) {
    const sequences = data.spatialData.sequences || [];
    if (sequences.length === 0) return 0.7;
    let inferredVisualization = 0;
    sequences.forEach((sequence) => {
      const sequenceComplexity = this.getSequenceVisualComplexity(sequence);
      const accuracy = sequence.isCorrect ? 1 : 0;
      inferredVisualization += accuracy * sequenceComplexity;
    });
    return Math.max(0, Math.min(1, inferredVisualization / sequences.length));
  }
  calculateOrientationAccuracy(spatialLayout) {
    if (!spatialLayout.targetOrientation || !spatialLayout.playerOrientation) return 0.7;
    const orientationDifference = Math.abs(spatialLayout.targetOrientation - spatialLayout.playerOrientation);
    const accuracy = Math.max(0, 1 - orientationDifference / 180);
    return accuracy;
  }
  getLayoutComplexity(spatialLayout) {
    const elementCount = spatialLayout.elements ? spatialLayout.elements.length : 1;
    const distribution = spatialLayout.distribution || "simple";
    const distributionComplexities = {
      simple: 1,
      moderate: 1.2,
      complex: 1.4,
      scattered: 1.6
    };
    const complexity = elementCount / 5 * distributionComplexities[distribution];
    return Math.min(2, complexity);
  }
  inferOrientationFromSequence(sequence) {
    if (!sequence.targetSequence) return 0.7;
    const sequenceLength = sequence.targetSequence.length;
    const uniqueShapes = new Set(sequence.targetSequence).size;
    const orientationDemand = uniqueShapes / sequenceLength;
    const accuracy = sequence.isCorrect ? 1 : 0;
    return accuracy * orientationDemand;
  }
  identifyTransformations(interactions) {
    const transformations = [];
    for (let i = 1; i < interactions.length; i++) {
      const current = interactions[i];
      const previous = interactions[i - 1];
      const transformation = this.detectTransformation(previous, current);
      if (transformation) {
        transformations.push({
          type: transformation.type,
          isCorrect: current.isCorrect,
          responseTime: current.responseTime,
          difficulty: transformation.difficulty
        });
      }
    }
    return transformations;
  }
  detectTransformation(previous, current) {
    if (previous.shapeId !== current.shapeId) {
      return { type: "shape_change", difficulty: 1.2 };
    }
    if (previous.position && current.position) {
      const distance = this.calculateDistance(previous.position, current.position);
      if (distance > 50) {
        return { type: "position_change", difficulty: 1.1 };
      }
    }
    return null;
  }
  getTransformationDifficulty(transformationType) {
    const difficulties = {
      shape_change: 1.2,
      position_change: 1.1,
      rotation: 1.4,
      scaling: 1.3,
      reflection: 1.5
    };
    return difficulties[transformationType] || 1;
  }
  calculateTransformationQuality(accuracy, processingTime, difficulty) {
    const timeScore = Math.max(0, Math.min(1, (3e3 - processingTime) / 2e3));
    const difficultyBonus = (difficulty - 1) * 0.2;
    return accuracy * 0.7 + timeScore * 0.3 + difficultyBonus;
  }
  calculateSpatialMemoryLoad(sequence) {
    if (!sequence.targetSequence) return 0.5;
    const sequenceLength = sequence.targetSequence.length;
    const uniqueElements = new Set(sequence.targetSequence).size;
    const lengthLoad = Math.min(1, sequenceLength / 6);
    const varietyLoad = Math.min(1, uniqueElements / 4);
    return (lengthLoad + varietyLoad) / 2;
  }
  calculateSpatialRetention(sequence) {
    const showTime = sequence.showTime || 5e3;
    const accuracy = sequence.isCorrect ? 1 : 0;
    const retentionChallenge = Math.min(1, showTime / 1e4);
    const retentionQuality = accuracy * (1 + retentionChallenge);
    return Math.min(1, retentionQuality);
  }
  calculateSpatialInterferenceResistance(sequence) {
    const complexity = this.getSequenceVisualComplexity(sequence);
    const accuracy = sequence.isCorrect ? 1 : 0;
    return accuracy * complexity;
  }
  calculateLogicalComplexity(sequence) {
    if (!sequence.targetSequence) return 0.5;
    const hasPattern = this.detectLogicalPattern(sequence.targetSequence);
    const sequenceLength = sequence.targetSequence.length;
    const patternComplexity = hasPattern ? 1.3 : 1;
    const lengthComplexity = Math.min(1.5, sequenceLength / 4);
    return Math.min(2, patternComplexity * lengthComplexity);
  }
  calculateReasoningAccuracy(sequence) {
    return sequence.isCorrect ? 1 : 0;
  }
  assessInferenceQuality(sequence) {
    if (!sequence.playerSequence || !sequence.targetSequence) return 0.7;
    const partialCorrectness = this.calculatePartialCorrectness(sequence);
    const logicalConsistency = this.assessLogicalConsistency(sequence.playerSequence);
    return partialCorrectness * 0.6 + logicalConsistency * 0.4;
  }
  identifyNavigationPatterns(interactions) {
    const patterns = [];
    if (interactions.length < 3) return patterns;
    for (let i = 0; i < interactions.length - 2; i++) {
      const segment = interactions.slice(i, i + 3);
      const pattern = this.analyzeNavigationSegment(segment);
      if (pattern) {
        patterns.push(pattern);
      }
    }
    return patterns;
  }
  analyzeNavigationSegment(segment) {
    const positions = segment.filter((i) => i.position).map((i) => i.position);
    if (positions.length < 2) return null;
    const totalDistance = this.calculateTotalDistance(positions);
    const efficiency = this.calculatePathEfficiency(positions);
    const accuracy = segment.filter((i) => i.isCorrect).length / segment.length;
    return {
      totalDistance,
      efficiency,
      accuracy,
      segmentLength: segment.length
    };
  }
  calculateNavigationEfficiency(pattern) {
    return Math.min(1, pattern.efficiency || 0.7);
  }
  assessPathOptimization(pattern) {
    const efficiency = pattern.efficiency || 0.7;
    const accuracy = pattern.accuracy || 0.7;
    return efficiency * 0.6 + accuracy * 0.4;
  }
  calculatePositionAccuracy(spatialPosition) {
    if (!spatialPosition.target || !spatialPosition.actual) return 0.7;
    const distance = this.calculateDistance(spatialPosition.target, spatialPosition.actual);
    const tolerance = spatialPosition.tolerance || 50;
    const accuracy = Math.max(0, 1 - distance / tolerance);
    return accuracy;
  }
  calculateDistanceAccuracy(spatialPosition) {
    if (!spatialPosition.expectedDistance || !spatialPosition.perceivedDistance) return 0.7;
    const distanceError = Math.abs(spatialPosition.expectedDistance - spatialPosition.perceivedDistance);
    const relativeError = distanceError / spatialPosition.expectedDistance;
    const accuracy = Math.max(0, 1 - relativeError);
    return accuracy;
  }
  calculateDirectionAccuracy(spatialPosition) {
    if (!spatialPosition.expectedDirection || !spatialPosition.perceivedDirection) return 0.7;
    const directionError = Math.abs(spatialPosition.expectedDirection - spatialPosition.perceivedDirection);
    const normalizedError = Math.min(directionError, 360 - directionError) / 180;
    const accuracy = Math.max(0, 1 - normalizedError);
    return accuracy;
  }
  inferAccuracyFromSequences(data) {
    const sequences = data.spatialData.sequences || [];
    if (sequences.length === 0) return 0.7;
    const accuracies = sequences.map((s) => s.isCorrect ? 1 : 0);
    const avgAccuracy = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    return avgAccuracy;
  }
  calculateSpatialEfficiency(processingTime, accuracy, spatialComplexity) {
    const timeScore = Math.max(0, Math.min(1, (3e3 - processingTime) / 2e3));
    const complexityBonus = (spatialComplexity - 1) * 0.1;
    return accuracy * 0.5 + timeScore * 0.4 + complexityBonus;
  }
  calculateEfficiencyImprovement(efficiencyScores) {
    if (efficiencyScores.length < 4) return 0;
    const firstHalf = efficiencyScores.slice(0, Math.floor(efficiencyScores.length / 2));
    const secondHalf = efficiencyScores.slice(Math.floor(efficiencyScores.length / 2));
    const firstAvg = firstHalf.reduce((sum, eff) => sum + eff, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, eff) => sum + eff, 0) / secondHalf.length;
    const improvement = secondAvg - firstAvg;
    return Math.max(0, Math.min(0.2, improvement));
  }
  // Métodos auxiliares gerais
  calculatePositionComplexity(position) {
    const centerDistance = this.calculateDistanceFromCenter(position);
    const edgeProximity = this.calculateEdgeProximity(position);
    return 1 + centerDistance * 0.3 + edgeProximity * 0.2;
  }
  getSequenceVisualComplexity(sequence) {
    if (!sequence.targetSequence) return 1;
    const uniqueShapes = new Set(sequence.targetSequence).size;
    const sequenceLength = sequence.targetSequence.length;
    const varietyComplexity = uniqueShapes / 3;
    const lengthComplexity = sequenceLength / 5;
    return Math.min(2, (varietyComplexity + lengthComplexity) / 2);
  }
  detectLogicalPattern(sequence) {
    if (sequence.length < 3) return false;
    const hasRepetition = this.detectRepetitionPattern(sequence);
    const hasAlternation = this.detectAlternationPattern(sequence);
    const hasProgression = this.detectProgressionPattern(sequence);
    return hasRepetition || hasAlternation || hasProgression;
  }
  detectRepetitionPattern(sequence) {
    for (let patternLength = 1; patternLength <= Math.floor(sequence.length / 2); patternLength++) {
      const pattern = sequence.slice(0, patternLength);
      let isRepeating = true;
      for (let i = patternLength; i < sequence.length; i++) {
        if (sequence[i] !== pattern[i % patternLength]) {
          isRepeating = false;
          break;
        }
      }
      if (isRepeating) return true;
    }
    return false;
  }
  detectAlternationPattern(sequence) {
    if (sequence.length < 4) return false;
    for (let i = 2; i < sequence.length; i++) {
      if (sequence[i] !== sequence[i - 2]) {
        return false;
      }
    }
    return true;
  }
  detectProgressionPattern(sequence) {
    if (sequence.length < 3) return false;
    const shapeOrder = ["circle", "square", "triangle", "diamond", "star", "heart"];
    const indices = sequence.map((shape) => shapeOrder.indexOf(shape));
    if (indices.includes(-1)) return false;
    let increasing = true;
    let decreasing = true;
    for (let i = 1; i < indices.length; i++) {
      if (indices[i] <= indices[i - 1]) increasing = false;
      if (indices[i] >= indices[i - 1]) decreasing = false;
    }
    return increasing || decreasing;
  }
  calculatePartialCorrectness(sequence) {
    if (!sequence.targetSequence || !sequence.playerSequence) return 0;
    const target = sequence.targetSequence;
    const player = sequence.playerSequence;
    const minLength = Math.min(target.length, player.length);
    let correct2 = 0;
    for (let i = 0; i < minLength; i++) {
      if (target[i] === player[i]) {
        correct2++;
      }
    }
    return correct2 / target.length;
  }
  assessLogicalConsistency(playerSequence) {
    if (playerSequence.length < 3) return 0.7;
    const hasInternalLogic = this.detectLogicalPattern(playerSequence);
    return hasInternalLogic ? 0.9 : 0.6;
  }
  calculateDistance(pos1, pos2) {
    if (!pos1 || !pos2) return 0;
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    return Math.sqrt(dx * dx + dy * dy);
  }
  calculateTotalDistance(positions) {
    if (positions.length < 2) return 0;
    let totalDistance = 0;
    for (let i = 1; i < positions.length; i++) {
      totalDistance += this.calculateDistance(positions[i - 1], positions[i]);
    }
    return totalDistance;
  }
  calculatePathEfficiency(positions) {
    if (positions.length < 2) return 1;
    const totalDistance = this.calculateTotalDistance(positions);
    const directDistance = this.calculateDistance(positions[0], positions[positions.length - 1]);
    if (totalDistance === 0) return 1;
    const efficiency = directDistance / totalDistance;
    return Math.min(1, efficiency);
  }
  calculateDistanceFromCenter(position) {
    const center = { x: 400, y: 300 };
    const distance = this.calculateDistance(position, center);
    const maxDistance = Math.sqrt(400 * 400 + 300 * 300);
    return distance / maxDistance;
  }
  calculateEdgeProximity(position) {
    const edges = [
      position.x,
      // distância da borda esquerda
      800 - position.x,
      // distância da borda direita
      position.y,
      // distância da borda superior
      600 - position.y
      // distância da borda inferior
    ];
    const minDistanceToEdge = Math.min(...edges);
    return 1 - minDistanceToEdge / 100;
  }
  calculateVariance(values) {
    if (values.length === 0) return 0;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return variance;
  }
  identifySpatialStrategies(data) {
    const strategies = [];
    const interactions = data.spatialData.interactions || [];
    if (interactions.length === 0) return strategies;
    if (this.detectVisualizationStrategy(interactions)) {
      strategies.push("visualization");
    }
    if (this.detectSpatialChunkingStrategy(interactions)) {
      strategies.push("spatial_chunking");
    }
    if (this.detectLandmarkStrategy(interactions)) {
      strategies.push("landmark_navigation");
    }
    if (this.detectSystematicScanningStrategy(interactions)) {
      strategies.push("systematic_scanning");
    }
    return strategies;
  }
  detectVisualizationStrategy(interactions) {
    const visualizationTasks = interactions.filter((i) => i.spatialVisualization);
    if (visualizationTasks.length === 0) return false;
    const avgAccuracy = visualizationTasks.filter((i) => i.isCorrect).length / visualizationTasks.length;
    return avgAccuracy > 0.7;
  }
  detectSpatialChunkingStrategy(interactions) {
    const positions = interactions.filter((i) => i.position).map((i) => i.position);
    if (positions.length < 4) return false;
    const clusters = this.identifyPositionClusters(positions);
    return clusters.length > 1 && clusters.length < positions.length / 2;
  }
  detectLandmarkStrategy(interactions) {
    const positions = interactions.filter((i) => i.position).map((i) => i.position);
    if (positions.length < 3) return false;
    let landmarkReturns = 0;
    for (let i = 0; i < positions.length; i++) {
      for (let j = i + 2; j < positions.length; j++) {
        const distance = this.calculateDistance(positions[i], positions[j]);
        if (distance < 50) {
          landmarkReturns++;
        }
      }
    }
    return landmarkReturns > positions.length / 4;
  }
  detectSystematicScanningStrategy(interactions) {
    const positions = interactions.filter((i) => i.position).map((i) => i.position);
    if (positions.length < 4) return false;
    const systematicPattern = this.detectSystematicPattern(positions);
    return systematicPattern;
  }
  identifyPositionClusters(positions) {
    const clusters = [];
    const threshold = 100;
    positions.forEach((pos) => {
      let addedToCluster = false;
      for (let cluster of clusters) {
        const centerDistance = this.calculateDistance(pos, cluster.center);
        if (centerDistance <= threshold) {
          cluster.positions.push(pos);
          cluster.center = this.calculateClusterCenter(cluster.positions);
          addedToCluster = true;
          break;
        }
      }
      if (!addedToCluster) {
        clusters.push({
          center: pos,
          positions: [pos]
        });
      }
    });
    return clusters;
  }
  calculateClusterCenter(positions) {
    const sumX = positions.reduce((sum, pos) => sum + pos.x, 0);
    const sumY = positions.reduce((sum, pos) => sum + pos.y, 0);
    return {
      x: sumX / positions.length,
      y: sumY / positions.length
    };
  }
  detectSystematicPattern(positions) {
    if (positions.length < 3) return false;
    const horizontalMovement = this.detectHorizontalPattern(positions);
    const verticalMovement = this.detectVerticalPattern(positions);
    const diagonalMovement = this.detectDiagonalPattern(positions);
    return horizontalMovement || verticalMovement || diagonalMovement;
  }
  detectHorizontalPattern(positions) {
    let horizontalMovements = 0;
    for (let i = 1; i < positions.length; i++) {
      const dx = Math.abs(positions[i].x - positions[i - 1].x);
      const dy = Math.abs(positions[i].y - positions[i - 1].y);
      if (dx > dy * 2) {
        horizontalMovements++;
      }
    }
    return horizontalMovements > positions.length / 2;
  }
  detectVerticalPattern(positions) {
    let verticalMovements = 0;
    for (let i = 1; i < positions.length; i++) {
      const dx = Math.abs(positions[i].x - positions[i - 1].x);
      const dy = Math.abs(positions[i].y - positions[i - 1].y);
      if (dy > dx * 2) {
        verticalMovements++;
      }
    }
    return verticalMovements > positions.length / 2;
  }
  detectDiagonalPattern(positions) {
    let diagonalMovements = 0;
    for (let i = 1; i < positions.length; i++) {
      const dx = Math.abs(positions[i].x - positions[i - 1].x);
      const dy = Math.abs(positions[i].y - positions[i - 1].y);
      const ratio = Math.min(dx, dy) / Math.max(dx, dy);
      if (ratio > 0.5) {
        diagonalMovements++;
      }
    }
    return diagonalMovements > positions.length / 2;
  }
  generateSpatialInsights(data) {
    const insights = [];
    const interactions = data.spatialData.interactions || [];
    const sequences = data.spatialData.sequences || [];
    if (interactions.length > 0) {
      const avgAccuracy = interactions.filter((i) => i.isCorrect).length / interactions.length;
      if (avgAccuracy < 0.6) {
        insights.push("Dificuldades no processamento espacial geral");
      } else if (avgAccuracy > 0.8) {
        insights.push("Excelente capacidade de processamento espacial");
      }
    }
    if (sequences.length > 0) {
      const complexSequences = sequences.filter((s) => this.getSequenceVisualComplexity(s) > 1.5);
      const complexAccuracy = complexSequences.filter((s) => s.isCorrect).length / Math.max(1, complexSequences.length);
      if (complexAccuracy < 0.5) {
        insights.push("Limitações com tarefas espaciais complexas");
      } else if (complexAccuracy > 0.7) {
        insights.push("Boa adaptação a complexidade espacial");
      }
    }
    const strategies = this.identifySpatialStrategies(data);
    if (strategies.length > 0) {
      insights.push(`Estratégias espaciais identificadas: ${strategies.join(", ")}`);
    }
    return insights;
  }
}
class SequentialReasoningCollector {
  constructor() {
    this.reasoningTypes = {
      temporal: "raciocínio temporal",
      logical: "raciocínio lógico",
      causal: "raciocínio causal",
      predictive: "raciocínio preditivo",
      analytical: "raciocínio analítico",
      inductive: "raciocínio indutivo"
    };
    this.sequenceComplexity = {
      simple: { level: 1, description: "sequências lineares" },
      moderate: { level: 2, description: "padrões básicos" },
      complex: { level: 3, description: "múltiplas regras" },
      advanced: { level: 4, description: "padrões aninhados" }
    };
    this.logicalOperations = {
      repetition: "repetição",
      alternation: "alternância",
      progression: "progressão",
      symmetry: "simetria",
      transformation: "transformação",
      combination: "combinação"
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  async analyze(data) {
    if (!data || !data.sequentialData) {
      console.warn("SequentialReasoningCollector: Dados inválidos recebidos", data);
      return {
        temporalReasoning: 0.7,
        logicalSequencing: 0.7,
        patternPrediction: 0.7,
        ruleExtraction: 0.7,
        sequentialMemory: 0.7,
        orderProcessing: 0.7,
        inductiveReasoning: 0.7,
        sequentialPlanning: 0.7
      };
    }
    return {
      temporalReasoning: this.assessTemporalReasoning(data),
      logicalSequencing: this.assessLogicalSequencing(data),
      patternPrediction: this.assessPatternPrediction(data),
      ruleExtraction: this.assessRuleExtraction(data),
      sequentialMemory: this.assessSequentialMemory(data),
      orderProcessing: this.assessOrderProcessing(data),
      inductiveReasoning: this.assessInductiveReasoning(data),
      sequentialPlanning: this.assessSequentialPlanning(data),
      reasoningStrategies: this.identifyReasoningStrategies(data),
      reasoningInsights: this.generateReasoningInsights(data)
    };
  }
  assessTemporalReasoning(data) {
    const sequences = data.sequentialData.sequences || [];
    if (sequences.length === 0) return 0.7;
    let temporalScore = 0;
    sequences.forEach((sequence) => {
      const temporalAccuracy = this.calculateTemporalAccuracy(sequence);
      const timingConsistency = this.assessTimingConsistency(sequence);
      const temporalComplexity = this.getTemporalComplexity(sequence);
      const temporalQuality = temporalAccuracy * 0.5 + timingConsistency * 0.3 + temporalComplexity * 0.2;
      temporalScore += temporalQuality;
    });
    const avgTemporalScore = temporalScore / sequences.length;
    const longSequences = sequences.filter((s) => this.getSequenceLength(s) >= 5);
    const longSequenceBonus = longSequences.length > 0 ? 0.1 : 0;
    return Math.max(0, Math.min(1, avgTemporalScore + longSequenceBonus));
  }
  assessLogicalSequencing(data) {
    const sequences = data.sequentialData.sequences || [];
    if (sequences.length === 0) return 0.7;
    let logicalScore = 0;
    sequences.forEach((sequence) => {
      const logicalPattern = this.identifyLogicalPattern(sequence);
      const patternComplexity = this.getPatternComplexity(logicalPattern);
      const logicalAccuracy = this.calculateLogicalAccuracy(sequence, logicalPattern);
      const logicalQuality = logicalAccuracy * patternComplexity;
      logicalScore += logicalQuality;
    });
    const avgLogicalScore = logicalScore / sequences.length;
    return Math.max(0, Math.min(1, avgLogicalScore));
  }
  assessPatternPrediction(data) {
    const sequences = data.sequentialData.sequences || [];
    if (sequences.length === 0) return 0.7;
    let predictionScore = 0;
    let predictiveTasks = 0;
    sequences.forEach((sequence) => {
      if (this.isPredictiveTask(sequence)) {
        const predictionAccuracy = this.calculatePredictionAccuracy(sequence);
        const predictionComplexity = this.getPredictionComplexity(sequence);
        const confidenceLevel = this.assessPredictionConfidence(sequence);
        const predictionQuality = predictionAccuracy * 0.6 + predictionComplexity * 0.2 + confidenceLevel * 0.2;
        predictionScore += predictionQuality;
        predictiveTasks++;
      }
    });
    if (predictiveTasks === 0) {
      return this.inferPredictionFromSequences(sequences);
    }
    const avgPredictionScore = predictionScore / predictiveTasks;
    return Math.max(0, Math.min(1, avgPredictionScore));
  }
  assessRuleExtraction(data) {
    const sequences = data.sequentialData.sequences || [];
    if (sequences.length < 3) return 0.7;
    const extractedRules = this.extractConsistentRules(sequences);
    let ruleExtractionScore = 0;
    extractedRules.forEach((rule) => {
      const ruleAccuracy = this.calculateRuleAccuracy(rule, sequences);
      const ruleComplexity = this.getRuleComplexity(rule);
      const ruleGeneralizability = this.assessRuleGeneralizability(rule, sequences);
      const ruleQuality = ruleAccuracy * 0.4 + ruleComplexity * 0.3 + ruleGeneralizability * 0.3;
      ruleExtractionScore += ruleQuality;
    });
    if (extractedRules.length === 0) return 0.7;
    const avgRuleScore = ruleExtractionScore / extractedRules.length;
    return Math.max(0, Math.min(1, avgRuleScore));
  }
  assessSequentialMemory(data) {
    const sequences = data.sequentialData.sequences || [];
    if (sequences.length === 0) return 0.7;
    let sequentialMemoryScore = 0;
    sequences.forEach((sequence) => {
      const orderRetention = this.calculateOrderRetention(sequence);
      const sequenceSpan = this.calculateSequenceSpan(sequence);
      const memoryDecay = this.assessMemoryDecay(sequence);
      const memoryQuality = orderRetention * 0.5 + sequenceSpan * 0.3 + memoryDecay * 0.2;
      sequentialMemoryScore += memoryQuality;
    });
    const avgMemoryScore = sequentialMemoryScore / sequences.length;
    return Math.max(0, Math.min(1, avgMemoryScore));
  }
  assessOrderProcessing(data) {
    const sequences = data.sequentialData.sequences || [];
    if (sequences.length === 0) return 0.7;
    let orderProcessingScore = 0;
    sequences.forEach((sequence) => {
      const orderAccuracy = this.calculateOrderAccuracy(sequence);
      const processingSpeed = this.calculateOrderProcessingSpeed(sequence);
      const orderComplexity = this.getOrderComplexity(sequence);
      const processingQuality = orderAccuracy * 0.5 + processingSpeed * 0.3 + orderComplexity * 0.2;
      orderProcessingScore += processingQuality;
    });
    const avgProcessingScore = orderProcessingScore / sequences.length;
    return Math.max(0, Math.min(1, avgProcessingScore));
  }
  assessInductiveReasoning(data) {
    const sequences = data.sequentialData.sequences || [];
    if (sequences.length < 2) return 0.7;
    let inductiveScore = 0;
    let inductiveTasks = 0;
    for (let i = 1; i < sequences.length; i++) {
      const current = sequences[i];
      const previous = sequences.slice(0, i);
      const generalization = this.assessGeneralization(current, previous);
      if (generalization) {
        const inductiveAccuracy = generalization.accuracy;
        const generalizationLevel = generalization.level;
        const transferability = generalization.transferability;
        const inductiveQuality = inductiveAccuracy * 0.5 + generalizationLevel * 0.3 + transferability * 0.2;
        inductiveScore += inductiveQuality;
        inductiveTasks++;
      }
    }
    if (inductiveTasks === 0) return 0.7;
    const avgInductiveScore = inductiveScore / inductiveTasks;
    return Math.max(0, Math.min(1, avgInductiveScore));
  }
  assessSequentialPlanning(data) {
    const sequences = data.sequentialData.sequences || [];
    if (sequences.length === 0) return 0.7;
    let planningScore = 0;
    sequences.forEach((sequence) => {
      const planningEfficiency = this.calculatePlanningEfficiency(sequence);
      const stepOptimization = this.assessStepOptimization(sequence);
      const goalDirectedness = this.assessGoalDirectedness(sequence);
      const planningQuality = planningEfficiency * 0.4 + stepOptimization * 0.3 + goalDirectedness * 0.3;
      planningScore += planningQuality;
    });
    const avgPlanningScore = planningScore / sequences.length;
    return Math.max(0, Math.min(1, avgPlanningScore));
  }
  // Métodos auxiliares
  calculateTemporalAccuracy(sequence) {
    if (!sequence.targetSequence || !sequence.playerSequence) return 0;
    const target = sequence.targetSequence;
    const player = sequence.playerSequence;
    const minLength = Math.min(target.length, player.length);
    let temporalMatches = 0;
    for (let i = 0; i < minLength; i++) {
      if (target[i] === player[i]) {
        temporalMatches++;
      }
    }
    return temporalMatches / target.length;
  }
  assessTimingConsistency(sequence) {
    if (!sequence.timingData) return 0.7;
    const intervals = sequence.timingData.intervals || [];
    if (intervals.length < 2) return 0.7;
    const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
    const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
    const standardDeviation = Math.sqrt(variance);
    const consistency = Math.max(0, 1 - standardDeviation / avgInterval);
    return consistency;
  }
  getTemporalComplexity(sequence) {
    const length = this.getSequenceLength(sequence);
    const timingVariability = sequence.timingData ? this.calculateTimingVariability(sequence.timingData) : 1;
    const lengthComplexity = Math.min(1.5, length / 5);
    const timingComplexity = Math.min(1.5, timingVariability);
    return (lengthComplexity + timingComplexity) / 2;
  }
  getSequenceLength(sequence) {
    return sequence.targetSequence ? sequence.targetSequence.length : 3;
  }
  calculateTimingVariability(timingData) {
    const intervals = timingData.intervals || [];
    if (intervals.length < 2) return 1;
    const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
    const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
    return Math.min(2, Math.sqrt(variance) / avgInterval);
  }
  identifyLogicalPattern(sequence) {
    if (!sequence.targetSequence) return { type: "none", complexity: 1 };
    const seq = sequence.targetSequence;
    if (this.isRepetitionPattern(seq)) {
      return { type: "repetition", complexity: 1.2 };
    }
    if (this.isAlternationPattern(seq)) {
      return { type: "alternation", complexity: 1.4 };
    }
    if (this.isProgressionPattern(seq)) {
      return { type: "progression", complexity: 1.6 };
    }
    if (this.isSymmetryPattern(seq)) {
      return { type: "symmetry", complexity: 1.8 };
    }
    return { type: "complex", complexity: 2 };
  }
  isRepetitionPattern(sequence) {
    if (sequence.length < 2) return false;
    for (let patternLength = 1; patternLength <= Math.floor(sequence.length / 2); patternLength++) {
      const pattern = sequence.slice(0, patternLength);
      let isRepeating = true;
      for (let i = patternLength; i < sequence.length; i++) {
        if (sequence[i] !== pattern[i % patternLength]) {
          isRepeating = false;
          break;
        }
      }
      if (isRepeating) return true;
    }
    return false;
  }
  isAlternationPattern(sequence) {
    if (sequence.length < 4) return false;
    for (let i = 2; i < sequence.length; i++) {
      if (sequence[i] !== sequence[i - 2]) {
        return false;
      }
    }
    return true;
  }
  isProgressionPattern(sequence) {
    if (sequence.length < 3) return false;
    const shapeOrder = ["circle", "square", "triangle", "diamond", "star", "heart"];
    const indices = sequence.map((shape) => shapeOrder.indexOf(shape));
    if (indices.includes(-1)) return false;
    if (indices.length < 3) return false;
    const diff = indices[1] - indices[0];
    for (let i = 2; i < indices.length; i++) {
      if (indices[i] - indices[i - 1] !== diff) {
        return false;
      }
    }
    return true;
  }
  isSymmetryPattern(sequence) {
    if (sequence.length < 3) return false;
    for (let i = 0; i < Math.floor(sequence.length / 2); i++) {
      if (sequence[i] !== sequence[sequence.length - 1 - i]) {
        return false;
      }
    }
    return true;
  }
  getPatternComplexity(logicalPattern) {
    return logicalPattern.complexity || 1;
  }
  calculateLogicalAccuracy(sequence, logicalPattern) {
    if (!sequence.playerSequence && !sequence.targetSequence) return 0;
    const sequenceToAnalyze = sequence.playerSequence || sequence.targetSequence;
    const patternAdherence = this.checkPatternAdherence(sequenceToAnalyze, logicalPattern);
    const baseAccuracy = sequence.isCorrect ? 1 : 0.7;
    return patternAdherence * 0.7 + baseAccuracy * 0.3;
  }
  checkPatternAdherence(playerSequence, logicalPattern) {
    switch (logicalPattern.type) {
      case "repetition":
        return this.isRepetitionPattern(playerSequence) ? 1 : 0;
      case "alternation":
        return this.isAlternationPattern(playerSequence) ? 1 : 0;
      case "progression":
        return this.isProgressionPattern(playerSequence) ? 1 : 0;
      case "symmetry":
        return this.isSymmetryPattern(playerSequence) ? 1 : 0;
      default:
        return 0.5;
    }
  }
  isPredictiveTask(sequence) {
    return sequence.taskType === "prediction" || sequence.incomplete === true;
  }
  calculatePredictionAccuracy(sequence) {
    if (!sequence.predictedElements || !sequence.actualElements) return 0;
    const predicted = sequence.predictedElements;
    const actual = sequence.actualElements;
    const minLength = Math.min(predicted.length, actual.length);
    let matches = 0;
    for (let i = 0; i < minLength; i++) {
      if (predicted[i] === actual[i]) {
        matches++;
      }
    }
    return matches / actual.length;
  }
  getPredictionComplexity(sequence) {
    const predictionLength = sequence.predictedElements ? sequence.predictedElements.length : 1;
    const contextLength = sequence.contextElements ? sequence.contextElements.length : 3;
    const predictionFactor = Math.min(1.5, predictionLength / 2);
    const contextFactor = Math.min(1.5, contextLength / 5);
    return (predictionFactor + contextFactor) / 2;
  }
  assessPredictionConfidence(sequence) {
    const responseTime = sequence.responseTime || 2e3;
    const optimalTime = 3e3;
    const timeDifference = Math.abs(responseTime - optimalTime);
    const confidence = Math.max(0, 1 - timeDifference / optimalTime);
    return confidence;
  }
  inferPredictionFromSequences(sequences) {
    if (sequences.length === 0) return 0.7;
    const accuracies = sequences.map((s) => {
      const accuracy = this.calculateTemporalAccuracy(s);
      return isNaN(accuracy) ? 0.7 : accuracy;
    });
    const avgAccuracy = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    if (sequences.length < 2) {
      return Math.max(0, Math.min(1, avgAccuracy));
    }
    const firstHalf = accuracies.slice(0, Math.floor(accuracies.length / 2));
    const secondHalf = accuracies.slice(Math.floor(accuracies.length / 2));
    const firstAvg = firstHalf.length > 0 ? firstHalf.reduce((sum, acc) => sum + acc, 0) / firstHalf.length : 0;
    const secondAvg = secondHalf.length > 0 ? secondHalf.reduce((sum, acc) => sum + acc, 0) / secondHalf.length : 0;
    const improvementBonus = Math.max(0, Math.min(0.2, secondAvg - firstAvg));
    return Math.max(0, Math.min(1, avgAccuracy + improvementBonus));
  }
  extractConsistentRules(sequences) {
    const rules = [];
    const patternTypes = {};
    sequences.forEach((sequence) => {
      const pattern = this.identifyLogicalPattern(sequence);
      if (!patternTypes[pattern.type]) {
        patternTypes[pattern.type] = [];
      }
      patternTypes[pattern.type].push(sequence);
    });
    Object.keys(patternTypes).forEach((patternType) => {
      const sequencesOfType = patternTypes[patternType];
      if (sequencesOfType.length >= 2) {
        rules.push({
          type: patternType,
          sequences: sequencesOfType,
          frequency: sequencesOfType.length / sequences.length
        });
      }
    });
    return rules;
  }
  calculateRuleAccuracy(rule, sequences) {
    const ruleSequences = rule.sequences;
    const correctApplications = ruleSequences.filter((s) => s.isCorrect).length;
    return correctApplications / ruleSequences.length;
  }
  getRuleComplexity(rule) {
    const complexities = {
      repetition: 1.2,
      alternation: 1.4,
      progression: 1.6,
      symmetry: 1.8,
      complex: 2
    };
    return complexities[rule.type] || 1;
  }
  assessRuleGeneralizability(rule, allSequences) {
    const ruleSequences = rule.sequences;
    const otherSequences = allSequences.filter((s) => !ruleSequences.includes(s));
    if (otherSequences.length === 0) return 0.7;
    let applicableCount = 0;
    otherSequences.forEach((sequence) => {
      const pattern = this.identifyLogicalPattern(sequence);
      if (pattern.type === rule.type) {
        applicableCount++;
      }
    });
    return applicableCount / otherSequences.length;
  }
  calculateOrderRetention(sequence) {
    if (!sequence.targetSequence || !sequence.playerSequence) return 0;
    const target = sequence.targetSequence;
    const player = sequence.playerSequence;
    let correctOrder = 0;
    const minLength = Math.min(target.length, player.length);
    for (let i = 0; i < minLength; i++) {
      if (target[i] === player[i]) {
        correctOrder++;
      } else {
        break;
      }
    }
    return correctOrder / target.length;
  }
  calculateSequenceSpan(sequence) {
    const length = this.getSequenceLength(sequence);
    const maxSpan = 8;
    return Math.min(1, length / maxSpan);
  }
  assessMemoryDecay(sequence) {
    const showTime = sequence.showTime || 5e3;
    const accuracy = this.calculateTemporalAccuracy(sequence);
    const timeChallenge = Math.min(1, showTime / 1e4);
    const decayResistance = accuracy * (1 + timeChallenge);
    return Math.min(1, decayResistance);
  }
  calculateOrderAccuracy(sequence) {
    return this.calculateOrderRetention(sequence);
  }
  calculateOrderProcessingSpeed(sequence) {
    const responseTime = sequence.responseTime || 2e3;
    const sequenceLength = this.getSequenceLength(sequence);
    const expectedTime = 1e3 + sequenceLength * 500;
    const speedScore = Math.max(0, Math.min(1, expectedTime / responseTime));
    return speedScore;
  }
  getOrderComplexity(sequence) {
    const length = this.getSequenceLength(sequence);
    const uniqueElements = sequence.targetSequence ? new Set(sequence.targetSequence).size : 1;
    const lengthComplexity = Math.min(1.5, length / 5);
    const varietyComplexity = Math.min(1.5, uniqueElements / 3);
    return (lengthComplexity + varietyComplexity) / 2;
  }
  assessGeneralization(currentSequence, previousSequences) {
    if (previousSequences.length === 0) return null;
    const previousPatterns = previousSequences.map((s) => this.identifyLogicalPattern(s));
    const currentPattern = this.identifyLogicalPattern(currentSequence);
    const similarPatterns = previousPatterns.filter((p) => p.type === currentPattern.type);
    if (similarPatterns.length === 0) return null;
    const generalizationLevel = similarPatterns.length / previousSequences.length;
    const accuracy = currentSequence.isCorrect ? 1 : 0;
    const transferability = this.calculateTransferability(currentPattern, similarPatterns);
    return {
      accuracy,
      level: generalizationLevel,
      transferability
    };
  }
  calculateTransferability(currentPattern, similarPatterns) {
    const avgComplexity = similarPatterns.reduce((sum, p) => sum + p.complexity, 0) / similarPatterns.length;
    const complexityDifference = Math.abs(currentPattern.complexity - avgComplexity);
    const transferability = Math.max(0, 1 - complexityDifference / 2);
    return transferability;
  }
  calculatePlanningEfficiency(sequence) {
    const responseTime = sequence.responseTime || 2e3;
    const sequenceLength = this.getSequenceLength(sequence);
    const accuracy = sequence.isCorrect ? 1 : 0;
    const timeEfficiency = Math.max(0, Math.min(1, (4e3 - responseTime) / 3e3));
    const lengthFactor = Math.min(1, sequenceLength / 5);
    return accuracy * 0.5 + timeEfficiency * 0.3 + lengthFactor * 0.2;
  }
  assessStepOptimization(sequence) {
    if (!sequence.stepData) return 0.7;
    const steps = sequence.stepData.steps || [];
    const optimalSteps = sequence.stepData.optimalSteps || steps.length;
    const stepEfficiency = Math.max(0, 1 - Math.abs(steps.length - optimalSteps) / optimalSteps);
    return stepEfficiency;
  }
  assessGoalDirectedness(sequence) {
    const accuracy = sequence.isCorrect ? 1 : 0;
    const responseTime = sequence.responseTime || 2e3;
    const timeScore = Math.max(0, Math.min(1, (4e3 - responseTime) / 3e3));
    const goalDirectedness = accuracy * 0.7 + timeScore * 0.3;
    return goalDirectedness;
  }
  identifyReasoningStrategies(data) {
    const strategies = [];
    const sequences = data.sequentialData.sequences || [];
    if (sequences.length === 0) return strategies;
    if (this.detectAnalyticalStrategy(sequences)) {
      strategies.push("analytical");
    }
    if (this.detectInductiveStrategy(sequences)) {
      strategies.push("inductive");
    }
    if (this.detectPatternMatchingStrategy(sequences)) {
      strategies.push("pattern_matching");
    }
    if (this.detectTrialAndErrorStrategy(sequences)) {
      strategies.push("trial_and_error");
    }
    return strategies;
  }
  detectAnalyticalStrategy(sequences) {
    const responseTimes = sequences.filter((s) => s.responseTime).map((s) => s.responseTime);
    if (responseTimes.length < 3) return false;
    const avgTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const variance = responseTimes.reduce((sum, time) => sum + Math.pow(time - avgTime, 2), 0) / responseTimes.length;
    const consistency = 1 - Math.sqrt(variance) / avgTime;
    const accuracy = sequences.filter((s) => s.isCorrect).length / sequences.length;
    return consistency > 0.7 && accuracy > 0.7 && avgTime > 2e3;
  }
  detectInductiveStrategy(sequences) {
    if (sequences.length < 4) return false;
    const firstHalf = sequences.slice(0, Math.floor(sequences.length / 2));
    const secondHalf = sequences.slice(Math.floor(sequences.length / 2));
    const firstAccuracy = firstHalf.filter((s) => s.isCorrect).length / firstHalf.length;
    const secondAccuracy = secondHalf.filter((s) => s.isCorrect).length / secondHalf.length;
    return secondAccuracy > firstAccuracy + 0.15;
  }
  detectPatternMatchingStrategy(sequences) {
    const patterns = sequences.map((s) => this.identifyLogicalPattern(s));
    const patternTypes = {};
    patterns.forEach((pattern) => {
      patternTypes[pattern.type] = (patternTypes[pattern.type] || 0) + 1;
    });
    const maxFrequency = Math.max(...Object.values(patternTypes));
    return maxFrequency > sequences.length * 0.6;
  }
  detectTrialAndErrorStrategy(sequences) {
    const responseTimes = sequences.filter((s) => s.responseTime).map((s) => s.responseTime);
    if (responseTimes.length < 3) return false;
    const avgTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const variance = responseTimes.reduce((sum, time) => sum + Math.pow(time - avgTime, 2), 0) / responseTimes.length;
    const variability = Math.sqrt(variance) / avgTime;
    const accuracy = sequences.filter((s) => s.isCorrect).length / sequences.length;
    return variability > 0.5 && avgTime < 2e3 && accuracy < 0.7;
  }
  generateReasoningInsights(data) {
    const insights = [];
    const sequences = data.sequentialData.sequences || [];
    if (sequences.length === 0) return insights;
    const avgAccuracy = sequences.filter((s) => s.isCorrect).length / sequences.length;
    if (avgAccuracy < 0.6) {
      insights.push("Dificuldades no raciocínio sequencial");
    } else if (avgAccuracy > 0.8) {
      insights.push("Excelente capacidade de raciocínio sequencial");
    }
    const patterns = sequences.map((s) => this.identifyLogicalPattern(s));
    const patternTypes = {};
    patterns.forEach((pattern) => {
      patternTypes[pattern.type] = (patternTypes[pattern.type] || 0) + 1;
    });
    const dominantPattern = Object.keys(patternTypes).reduce(
      (a, b) => patternTypes[a] > patternTypes[b] ? a : b
    );
    if (patternTypes[dominantPattern] > sequences.length * 0.6) {
      insights.push(`Facilidade com padrões de ${dominantPattern}`);
    }
    const maxComplexity = Math.max(...patterns.map((p) => p.complexity));
    if (maxComplexity >= 1.8) {
      insights.push("Capaz de lidar com padrões complexos");
    } else if (maxComplexity <= 1.2) {
      insights.push("Limitado a padrões simples");
    }
    const strategies = this.identifyReasoningStrategies(data);
    if (strategies.length > 0) {
      insights.push(`Estratégias de raciocínio identificadas: ${strategies.join(", ")}`);
    }
    return insights;
  }
}
class VisualSequenceCollector {
  constructor() {
    this.sequences = [];
    this.patterns = [];
    this.temporalData = [];
  }
  async collect(sessionData) {
    try {
      const sequenceMetrics = {
        sequenceLength: sessionData.sequence?.length || 0,
        patternType: sessionData.patternType || "unknown",
        completionTime: sessionData.completionTime || 0,
        accuracy: sessionData.accuracy || 0,
        sequenceComplexity: this.calculateSequenceComplexity(sessionData),
        visualProcessingSpeed: this.calculateVisualProcessingSpeed(sessionData),
        sequenceMemory: this.assessSequenceMemory(sessionData),
        patternRecognition: this.evaluatePatternRecognition(sessionData)
      };
      this.sequences.push(sequenceMetrics);
      return sequenceMetrics;
    } catch (error) {
      console.error("VisualSequenceCollector error:", error);
      return {
        sequenceLength: 0,
        patternType: "error",
        completionTime: 0,
        accuracy: 0,
        sequenceComplexity: 0,
        visualProcessingSpeed: 0,
        sequenceMemory: 0,
        patternRecognition: 0
      };
    }
  }
  calculateSequenceComplexity(data) {
    const length = data.sequence?.length || 1;
    const uniqueElements = new Set(data.sequence || []).size;
    return Math.min(10, length * uniqueElements / 2);
  }
  calculateVisualProcessingSpeed(data) {
    const time = data.completionTime || 1e3;
    const length = data.sequence?.length || 1;
    return Math.max(1, Math.min(10, length * 1e3 / time));
  }
  assessSequenceMemory(data) {
    const accuracy = data.accuracy || 0;
    const length = data.sequence?.length || 1;
    return Math.min(10, accuracy * (length / 3));
  }
  evaluatePatternRecognition(data) {
    const accuracy = data.accuracy || 0;
    const complexity = this.calculateSequenceComplexity(data);
    return Math.min(10, accuracy * (complexity / 5));
  }
  /**
   * Método padronizado de análise de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  analyze(data) {
    return this.collect(data);
  }
  getMetrics() {
    return {
      totalSequences: this.sequences.length,
      averageAccuracy: this.sequences.reduce((acc, seq) => acc + seq.accuracy, 0) / (this.sequences.length || 1),
      averageComplexity: this.sequences.reduce((acc, seq) => acc + seq.sequenceComplexity, 0) / (this.sequences.length || 1),
      averageProcessingSpeed: this.sequences.reduce((acc, seq) => acc + seq.visualProcessingSpeed, 0) / (this.sequences.length || 1)
    };
  }
}
class SpatialPatternCollector {
  constructor() {
    this.collectorId = "spatial_pattern";
    this.isActive = true;
    this.spatialMetrics = {
      spatialRelationships: [],
      positionPatterns: [],
      distancePerceptions: [],
      orientationPatterns: [],
      symmetryRecognition: []
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Método principal de análise
   * @param {Object} data - Dados do jogo
   * @returns {Object} - Análise de padrões espaciais
   */
  async analyze(data) {
    try {
      if (!data || !data.gameData) {
        console.warn("SpatialPatternCollector: Dados incompletos recebidos");
        return {
          spatialAccuracy: 0.5,
          orientationSkills: "medium",
          symmetryRecognition: 0.5,
          spatialRelationships: 0.5,
          distancePerception: 0.5,
          overallSpatialScore: 0.5
        };
      }
      const spatialData = this.collectSpatialData(data.gameData, data.playerBehavior);
      return {
        spatialAccuracy: this.calculateSpatialAccuracy(spatialData),
        orientationSkills: this.assessOrientationSkills(spatialData),
        symmetryRecognition: this.assessSymmetryRecognition(spatialData),
        spatialRelationships: this.assessSpatialRelationships(spatialData),
        distancePerception: this.assessDistancePerception(spatialData),
        overallSpatialScore: this.calculateOverallSpatialScore(spatialData)
      };
    } catch (error) {
      console.error("Erro no SpatialPatternCollector.analyze:", error);
      return {
        spatialAccuracy: 0.5,
        orientationSkills: "medium",
        symmetryRecognition: 0.5,
        spatialRelationships: 0.5,
        distancePerception: 0.5,
        overallSpatialScore: 0.5
      };
    }
  }
  collectSpatialData(gameData, playerBehavior) {
    return {
      timestamp: Date.now(),
      sessionId: gameData.sessionId,
      spatialInteractions: this.analyzeSpatialInteractions(gameData, playerBehavior),
      orientationData: this.analyzeOrientation(gameData, playerBehavior),
      symmetryData: this.analyzeSymmetry(gameData, playerBehavior),
      relationshipData: this.analyzeRelationships(gameData, playerBehavior),
      distanceData: this.analyzeDistance(gameData, playerBehavior)
    };
  }
  analyzeSpatialInteractions(gameData, playerBehavior) {
    return {
      accuracy: playerBehavior?.accuracy || 0.5,
      responseTime: playerBehavior?.responseTime || 1e3,
      patterns: gameData?.patterns || []
    };
  }
  analyzeOrientation(gameData, playerBehavior) {
    return {
      rotationAccuracy: 0.5,
      orientationSpeed: 0.5,
      orientationConsistency: 0.5
    };
  }
  analyzeSymmetry(gameData, playerBehavior) {
    return {
      symmetryDetection: 0.5,
      symmetryCompletion: 0.5,
      symmetrySpeed: 0.5
    };
  }
  analyzeRelationships(gameData, playerBehavior) {
    return {
      relativePosition: 0.5,
      spatialHierarchy: 0.5,
      spatialMapping: 0.5
    };
  }
  analyzeDistance(gameData, playerBehavior) {
    return {
      distanceEstimation: 0.5,
      proximityJudgment: 0.5,
      scalePerception: 0.5
    };
  }
  calculateSpatialAccuracy(spatialData) {
    return spatialData.spatialInteractions?.accuracy || 0.5;
  }
  assessOrientationSkills(spatialData) {
    const score = spatialData.orientationData?.rotationAccuracy || 0.5;
    if (score >= 0.8) return "high";
    if (score >= 0.6) return "medium";
    return "low";
  }
  assessSymmetryRecognition(spatialData) {
    return spatialData.symmetryData?.symmetryDetection || 0.5;
  }
  assessSpatialRelationships(spatialData) {
    return spatialData.relationshipData?.relativePosition || 0.5;
  }
  assessDistancePerception(spatialData) {
    return spatialData.distanceData?.distanceEstimation || 0.5;
  }
  calculateOverallSpatialScore(spatialData) {
    const accuracy = this.calculateSpatialAccuracy(spatialData);
    const symmetry = this.assessSymmetryRecognition(spatialData);
    const relationships = this.assessSpatialRelationships(spatialData);
    const distance = this.assessDistancePerception(spatialData);
    return (accuracy + symmetry + relationships + distance) / 4;
  }
  updateSpatialMetrics(spatialData) {
    this.spatialMetrics.spatialRelationships.push(spatialData.relationshipData);
    this.spatialMetrics.positionPatterns.push(spatialData.spatialInteractions);
    this.spatialMetrics.distancePerceptions.push(spatialData.distanceData);
    this.spatialMetrics.orientationPatterns.push(spatialData.orientationData);
    this.spatialMetrics.symmetryRecognition.push(spatialData.symmetryData);
  }
  getSpatialMetrics() {
    return this.spatialMetrics;
  }
  reset() {
    this.spatialMetrics = {
      spatialRelationships: [],
      positionPatterns: [],
      distancePerceptions: [],
      orientationPatterns: [],
      symmetryRecognition: []
    };
  }
}
class ColorPatternCollector {
  constructor() {
    this.collectorId = "color_pattern";
    this.isActive = true;
    this.colorMetrics = {
      colorRecognition: [],
      colorSequences: [],
      colorDiscrimination: [],
      colorMemory: [],
      colorAssociation: []
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Método principal de análise
   * @param {Object} data - Dados do jogo
   * @returns {Object} - Análise de padrões de cores
   */
  async analyze(data) {
    try {
      if (!data || !data.gameData) {
        console.warn("ColorPatternCollector: Dados incompletos recebidos");
        return {
          colorRecognitionAccuracy: 0.5,
          colorSequenceSkills: "medium",
          colorDiscrimination: 0.5,
          colorMemoryCapacity: 0.5,
          colorAssociationSkills: 0.5,
          overallColorScore: 0.5
        };
      }
      const colorData = this.collectColorData(data.gameData, data.playerBehavior);
      return {
        colorRecognitionAccuracy: this.calculateColorRecognition(colorData),
        colorSequenceSkills: this.assessColorSequenceSkills(colorData),
        colorDiscrimination: this.assessColorDiscrimination(colorData),
        colorMemoryCapacity: this.assessColorMemory(colorData),
        colorAssociationSkills: this.assessColorAssociation(colorData),
        overallColorScore: this.calculateOverallColorScore(colorData)
      };
    } catch (error) {
      console.error("Erro no ColorPatternCollector.analyze:", error);
      return {
        colorRecognitionAccuracy: 0.5,
        colorSequenceSkills: "medium",
        colorDiscrimination: 0.5,
        colorMemoryCapacity: 0.5,
        colorAssociationSkills: 0.5,
        overallColorScore: 0.5
      };
    }
  }
  collectColorData(gameData, playerBehavior) {
    return {
      timestamp: Date.now(),
      sessionId: gameData.sessionId,
      colorInteractions: this.analyzeColorInteractions(gameData, playerBehavior),
      sequenceData: this.analyzeColorSequences(gameData, playerBehavior),
      discriminationData: this.analyzeColorDiscrimination(gameData, playerBehavior),
      memoryData: this.analyzeColorMemory(gameData, playerBehavior),
      associationData: this.analyzeColorAssociation(gameData, playerBehavior)
    };
  }
  analyzeColorInteractions(gameData, playerBehavior) {
    return {
      accuracy: playerBehavior?.accuracy || 0.5,
      responseTime: playerBehavior?.responseTime || 1e3,
      colorChoices: gameData?.colorChoices || [],
      correctColors: gameData?.correctColors || []
    };
  }
  analyzeColorSequences(gameData, playerBehavior) {
    return {
      sequenceAccuracy: 0.5,
      sequenceLength: gameData?.sequence?.length || 3,
      sequenceComplexity: 0.5,
      sequenceSpeed: 0.5
    };
  }
  analyzeColorDiscrimination(gameData, playerBehavior) {
    return {
      discriminationAccuracy: 0.5,
      discriminationSensitivity: 0.5,
      discriminationSpeed: 0.5
    };
  }
  analyzeColorMemory(gameData, playerBehavior) {
    return {
      shortTermColorMemory: 0.5,
      colorMemoryCapacity: 0.5,
      colorMemoryRetention: 0.5
    };
  }
  analyzeColorAssociation(gameData, playerBehavior) {
    return {
      colorMeaning: 0.5,
      colorEmotion: 0.5,
      colorCategorization: 0.5
    };
  }
  calculateColorRecognition(colorData) {
    return colorData.colorInteractions?.accuracy || 0.5;
  }
  assessColorSequenceSkills(colorData) {
    const score = colorData.sequenceData?.sequenceAccuracy || 0.5;
    if (score >= 0.8) return "high";
    if (score >= 0.6) return "medium";
    return "low";
  }
  assessColorDiscrimination(colorData) {
    return colorData.discriminationData?.discriminationAccuracy || 0.5;
  }
  assessColorMemory(colorData) {
    return colorData.memoryData?.colorMemoryCapacity || 0.5;
  }
  assessColorAssociation(colorData) {
    return colorData.associationData?.colorCategorization || 0.5;
  }
  calculateOverallColorScore(colorData) {
    const recognition = this.calculateColorRecognition(colorData);
    const discrimination = this.assessColorDiscrimination(colorData);
    const memory = this.assessColorMemory(colorData);
    const association = this.assessColorAssociation(colorData);
    return (recognition + discrimination + memory + association) / 4;
  }
  updateColorMetrics(colorData) {
    this.colorMetrics.colorRecognition.push(colorData.colorInteractions);
    this.colorMetrics.colorSequences.push(colorData.sequenceData);
    this.colorMetrics.colorDiscrimination.push(colorData.discriminationData);
    this.colorMetrics.colorMemory.push(colorData.memoryData);
    this.colorMetrics.colorAssociation.push(colorData.associationData);
  }
  getColorMetrics() {
    return this.colorMetrics;
  }
  reset() {
    this.colorMetrics = {
      colorRecognition: [],
      colorSequences: [],
      colorDiscrimination: [],
      colorMemory: [],
      colorAssociation: []
    };
  }
}
class GeometricPatternCollector {
  constructor() {
    this.collectorId = "geometric_pattern";
    this.isActive = true;
    this.geometricMetrics = {
      shapeRecognition: [],
      geometricSequences: [],
      spatialTransformations: [],
      symmetryPatterns: [],
      geometricRelationships: []
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Método principal de análise
   * @param {Object} data - Dados do jogo
   * @returns {Object} - Análise de padrões geométricos
   */
  async analyze(data) {
    try {
      if (!data || !data.gameData) {
        console.warn("GeometricPatternCollector: Dados incompletos recebidos");
        return {
          shapeRecognitionAccuracy: 0.5,
          geometricSequenceSkills: "medium",
          spatialTransformation: 0.5,
          symmetryRecognition: 0.5,
          geometricReasoning: 0.5,
          overallGeometricScore: 0.5
        };
      }
      const geometricData = this.collectGeometricData(data.gameData, data.playerBehavior);
      return {
        shapeRecognitionAccuracy: this.calculateShapeRecognition(geometricData),
        geometricSequenceSkills: this.assessGeometricSequenceSkills(geometricData),
        spatialTransformation: this.assessSpatialTransformation(geometricData),
        symmetryRecognition: this.assessSymmetryRecognition(geometricData),
        geometricReasoning: this.assessGeometricReasoning(geometricData),
        overallGeometricScore: this.calculateOverallGeometricScore(geometricData)
      };
    } catch (error) {
      console.error("Erro no GeometricPatternCollector.analyze:", error);
      return {
        shapeRecognitionAccuracy: 0.5,
        geometricSequenceSkills: "medium",
        spatialTransformation: 0.5,
        symmetryRecognition: 0.5,
        geometricReasoning: 0.5,
        overallGeometricScore: 0.5
      };
    }
  }
  collectGeometricData(gameData, playerBehavior) {
    return {
      timestamp: Date.now(),
      sessionId: gameData.sessionId,
      shapeInteractions: this.analyzeShapeInteractions(gameData, playerBehavior),
      sequenceData: this.analyzeGeometricSequences(gameData, playerBehavior),
      transformationData: this.analyzeSpatialTransformations(gameData, playerBehavior),
      symmetryData: this.analyzeSymmetryPatterns(gameData, playerBehavior),
      relationshipData: this.analyzeGeometricRelationships(gameData, playerBehavior)
    };
  }
  analyzeShapeInteractions(gameData, playerBehavior) {
    return {
      accuracy: playerBehavior?.accuracy || 0.5,
      responseTime: playerBehavior?.responseTime || 1e3,
      shapesIdentified: gameData?.shapesIdentified || [],
      correctShapes: gameData?.correctShapes || []
    };
  }
  analyzeGeometricSequences(gameData, playerBehavior) {
    return {
      sequenceAccuracy: 0.5,
      sequenceComplexity: gameData?.sequence?.complexity || "medium",
      sequenceLength: gameData?.sequence?.length || 3,
      sequenceSpeed: 0.5
    };
  }
  analyzeSpatialTransformations(gameData, playerBehavior) {
    return {
      rotationAccuracy: 0.5,
      scalingAccuracy: 0.5,
      translationAccuracy: 0.5,
      transformationSpeed: 0.5
    };
  }
  analyzeSymmetryPatterns(gameData, playerBehavior) {
    return {
      symmetryDetection: 0.5,
      symmetryCompletion: 0.5,
      symmetryTypes: ["horizontal", "vertical", "radial"],
      symmetryComplexity: 0.5
    };
  }
  analyzeGeometricRelationships(gameData, playerBehavior) {
    return {
      spatialRelations: 0.5,
      proportionalReasoning: 0.5,
      geometricComparisons: 0.5,
      patternExtension: 0.5
    };
  }
  calculateShapeRecognition(geometricData) {
    return geometricData.shapeInteractions?.accuracy || 0.5;
  }
  assessGeometricSequenceSkills(geometricData) {
    const score = geometricData.sequenceData?.sequenceAccuracy || 0.5;
    if (score >= 0.8) return "high";
    if (score >= 0.6) return "medium";
    return "low";
  }
  assessSpatialTransformation(geometricData) {
    const transformationData = geometricData.transformationData;
    const rotation = transformationData?.rotationAccuracy || 0.5;
    const scaling = transformationData?.scalingAccuracy || 0.5;
    const translation = transformationData?.translationAccuracy || 0.5;
    return (rotation + scaling + translation) / 3;
  }
  assessSymmetryRecognition(geometricData) {
    return geometricData.symmetryData?.symmetryDetection || 0.5;
  }
  assessGeometricReasoning(geometricData) {
    const relationshipData = geometricData.relationshipData;
    const spatial = relationshipData?.spatialRelations || 0.5;
    const proportional = relationshipData?.proportionalReasoning || 0.5;
    const comparison = relationshipData?.geometricComparisons || 0.5;
    return (spatial + proportional + comparison) / 3;
  }
  calculateOverallGeometricScore(geometricData) {
    const shape = this.calculateShapeRecognition(geometricData);
    const transformation = this.assessSpatialTransformation(geometricData);
    const symmetry = this.assessSymmetryRecognition(geometricData);
    const reasoning = this.assessGeometricReasoning(geometricData);
    return (shape + transformation + symmetry + reasoning) / 4;
  }
  updateGeometricMetrics(geometricData) {
    this.geometricMetrics.shapeRecognition.push(geometricData.shapeInteractions);
    this.geometricMetrics.geometricSequences.push(geometricData.sequenceData);
    this.geometricMetrics.spatialTransformations.push(geometricData.transformationData);
    this.geometricMetrics.symmetryPatterns.push(geometricData.symmetryData);
    this.geometricMetrics.geometricRelationships.push(geometricData.relationshipData);
  }
  getGeometricMetrics() {
    return this.geometricMetrics;
  }
  reset() {
    this.geometricMetrics = {
      shapeRecognition: [],
      geometricSequences: [],
      spatialTransformations: [],
      symmetryPatterns: [],
      geometricRelationships: []
    };
  }
}
class TemporalPatternCollector {
  constructor() {
    this.collectorId = "temporal_pattern";
    this.isActive = true;
    this.temporalMetrics = {
      sequenceTiming: [],
      rhythmPatterns: [],
      temporalOrdering: [],
      timingAccuracy: [],
      sequentialProcessing: []
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Método principal de análise
   * @param {Object} data - Dados do jogo
   * @returns {Object} - Análise de padrões temporais
   */
  async analyze(data) {
    try {
      if (!data || !data.gameData) {
        console.warn("TemporalPatternCollector: Dados incompletos recebidos");
        return {
          sequenceTimingAccuracy: 0.5,
          rhythmRecognition: "medium",
          temporalOrderingSkills: 0.5,
          timingPrecision: 0.5,
          sequentialProcessing: 0.5,
          overallTemporalScore: 0.5
        };
      }
      const temporalData = this.collectTemporalData(data.gameData, data.playerBehavior);
      return {
        sequenceTimingAccuracy: this.calculateSequenceTiming(temporalData),
        rhythmRecognition: this.assessRhythmRecognition(temporalData),
        temporalOrderingSkills: this.assessTemporalOrdering(temporalData),
        timingPrecision: this.assessTimingPrecision(temporalData),
        sequentialProcessing: this.assessSequentialProcessing(temporalData),
        overallTemporalScore: this.calculateOverallTemporalScore(temporalData)
      };
    } catch (error) {
      console.error("Erro no TemporalPatternCollector.analyze:", error);
      return {
        sequenceTimingAccuracy: 0.5,
        rhythmRecognition: "medium",
        temporalOrderingSkills: 0.5,
        timingPrecision: 0.5,
        sequentialProcessing: 0.5,
        overallTemporalScore: 0.5
      };
    }
  }
  collectTemporalData(gameData, playerBehavior) {
    return {
      timestamp: Date.now(),
      sessionId: gameData.sessionId,
      timingInteractions: this.analyzeTimingInteractions(gameData, playerBehavior),
      rhythmData: this.analyzeRhythmPatterns(gameData, playerBehavior),
      orderingData: this.analyzeTemporalOrdering(gameData, playerBehavior),
      precisionData: this.analyzeTimingPrecision(gameData, playerBehavior),
      processingData: this.analyzeSequentialProcessing(gameData, playerBehavior)
    };
  }
  analyzeTimingInteractions(gameData, playerBehavior) {
    return {
      responseTime: playerBehavior?.responseTime || 1e3,
      timingAccuracy: playerBehavior?.timingAccuracy || 0.5,
      expectedTiming: gameData?.expectedTiming || 1e3,
      timingDeviation: Math.abs((playerBehavior?.responseTime || 1e3) - (gameData?.expectedTiming || 1e3))
    };
  }
  analyzeRhythmPatterns(gameData, playerBehavior) {
    return {
      rhythmAccuracy: 0.5,
      rhythmConsistency: 0.5,
      rhythmComplexity: gameData?.rhythmComplexity || "medium",
      rhythmSpeed: 0.5
    };
  }
  analyzeTemporalOrdering(gameData, playerBehavior) {
    return {
      orderingAccuracy: 0.5,
      sequenceLength: gameData?.sequence?.length || 3,
      orderingSpeed: 0.5,
      orderingConsistency: 0.5
    };
  }
  analyzeTimingPrecision(gameData, playerBehavior) {
    return {
      precisionScore: 0.5,
      consistencyScore: 0.5,
      adaptabilityScore: 0.5,
      stabilityScore: 0.5
    };
  }
  analyzeSequentialProcessing(gameData, playerBehavior) {
    return {
      processingSpeed: 0.5,
      processingAccuracy: 0.5,
      processingConsistency: 0.5,
      processingEfficiency: 0.5
    };
  }
  calculateSequenceTiming(temporalData) {
    return temporalData.timingInteractions?.timingAccuracy || 0.5;
  }
  assessRhythmRecognition(temporalData) {
    const score = temporalData.rhythmData?.rhythmAccuracy || 0.5;
    if (score >= 0.8) return "high";
    if (score >= 0.6) return "medium";
    return "low";
  }
  assessTemporalOrdering(temporalData) {
    return temporalData.orderingData?.orderingAccuracy || 0.5;
  }
  assessTimingPrecision(temporalData) {
    return temporalData.precisionData?.precisionScore || 0.5;
  }
  assessSequentialProcessing(temporalData) {
    return temporalData.processingData?.processingEfficiency || 0.5;
  }
  calculateOverallTemporalScore(temporalData) {
    const timing = this.calculateSequenceTiming(temporalData);
    const ordering = this.assessTemporalOrdering(temporalData);
    const precision = this.assessTimingPrecision(temporalData);
    const processing = this.assessSequentialProcessing(temporalData);
    return (timing + ordering + precision + processing) / 4;
  }
  updateTemporalMetrics(temporalData) {
    this.temporalMetrics.sequenceTiming.push(temporalData.timingInteractions);
    this.temporalMetrics.rhythmPatterns.push(temporalData.rhythmData);
    this.temporalMetrics.temporalOrdering.push(temporalData.orderingData);
    this.temporalMetrics.timingAccuracy.push(temporalData.precisionData);
    this.temporalMetrics.sequentialProcessing.push(temporalData.processingData);
  }
  getTemporalMetrics() {
    return this.temporalMetrics;
  }
  reset() {
    this.temporalMetrics = {
      sequenceTiming: [],
      rhythmPatterns: [],
      temporalOrdering: [],
      timingAccuracy: [],
      sequentialProcessing: []
    };
  }
}
class ErrorPatternCollector {
  constructor() {
    this.collectorName = "ErrorPatternCollector";
    this.gameType = "PadroesVisuais";
    this.version = "1.0.0";
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }
  /**
   * Analisa padrões de erro no jogo
   * @param {Object} gameData - Dados do jogo
   * @returns {Object} Análise dos padrões de erro
   */
  analyze(gameData) {
    try {
      if (!gameData || !gameData.interactions) {
        return this.createEmptyAnalysis();
      }
      const errorAnalysis = {
        // Análise de tipos de erro
        errorTypes: this.analyzeErrorTypes(gameData.interactions),
        // Análise de frequência de erros
        errorFrequency: this.analyzeErrorFrequency(gameData.interactions),
        // Análise de padrões temporais de erro
        temporalPatterns: this.analyzeTemporalErrorPatterns(gameData.interactions),
        // Análise de persistência de erros
        errorPersistence: this.analyzeErrorPersistence(gameData.interactions),
        // Métricas de qualidade
        qualityMetrics: this.calculateQualityMetrics(gameData.interactions),
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        collector: this.collectorName,
        confidence: this.calculateConfidence(gameData.interactions)
      };
      return errorAnalysis;
    } catch (error) {
      console.error("Error in ErrorPatternCollector:", error);
      return this.createErrorAnalysis(error);
    }
  }
  /**
   * Analisa tipos de erro
   */
  analyzeErrorTypes(interactions) {
    const errorInteractions = interactions.filter((i) => i.success === false || i.error === true);
    const errorTypes = {
      patternMismatch: 0,
      colorConfusion: 0,
      spatialError: 0,
      sequenceError: 0,
      timingError: 0,
      recognitionError: 0
    };
    errorInteractions.forEach((interaction) => {
      if (interaction.errorType) {
        errorTypes[interaction.errorType] = (errorTypes[interaction.errorType] || 0) + 1;
      } else {
        if (interaction.patternType === "color") {
          errorTypes.colorConfusion++;
        } else if (interaction.patternType === "spatial") {
          errorTypes.spatialError++;
        } else if (interaction.patternType === "sequence") {
          errorTypes.sequenceError++;
        } else {
          errorTypes.recognitionError++;
        }
      }
    });
    return {
      distribution: errorTypes,
      totalErrors: errorInteractions.length,
      mostCommonError: this.findMostCommonError(errorTypes),
      errorRate: errorInteractions.length / Math.max(interactions.length, 1)
    };
  }
  /**
   * Analisa frequência de erros
   */
  analyzeErrorFrequency(interactions) {
    const errorInteractions = interactions.filter((i) => i.success === false || i.error === true);
    const totalInteractions = interactions.length;
    if (totalInteractions === 0) {
      return { frequency: 0, pattern: "none", stability: "stable" };
    }
    const timeBlocks = this.divideIntoTimeBlocks(interactions, 5);
    const errorFrequencyByBlock = timeBlocks.map((block) => {
      const blockErrors = block.filter((i) => i.success === false || i.error === true);
      return blockErrors.length / Math.max(block.length, 1);
    });
    return {
      frequency: errorInteractions.length / totalInteractions,
      pattern: this.identifyFrequencyPattern(errorFrequencyByBlock),
      stability: this.calculateErrorStability(errorFrequencyByBlock),
      distribution: errorFrequencyByBlock
    };
  }
  /**
   * Analisa padrões temporais de erro
   */
  analyzeTemporalErrorPatterns(interactions) {
    const errorInteractions = interactions.filter((i) => i.success === false || i.error === true);
    if (errorInteractions.length === 0) {
      return { pattern: "none", concentration: "dispersed", timing: "random" };
    }
    const timestamps = errorInteractions.map((i) => i.timestamp || Date.now());
    const timeGaps = this.calculateTimeGaps(timestamps);
    return {
      pattern: this.identifyTemporalPattern(timeGaps),
      concentration: this.analyzeErrorConcentration(timestamps),
      timing: this.analyzeErrorTiming(timestamps),
      averageGap: timeGaps.reduce((a, b) => a + b, 0) / Math.max(timeGaps.length, 1)
    };
  }
  /**
   * Analisa persistência de erros
   */
  analyzeErrorPersistence(interactions) {
    const errorSequences = this.findErrorSequences(interactions);
    return {
      maxSequenceLength: Math.max(...errorSequences.map((s) => s.length), 0),
      averageSequenceLength: errorSequences.reduce((sum, seq) => sum + seq.length, 0) / Math.max(errorSequences.length, 1),
      sequenceCount: errorSequences.length,
      recoveryRate: this.calculateRecoveryRate(interactions),
      persistence: this.calculatePersistenceScore(errorSequences)
    };
  }
  /**
   * Calcula métricas de qualidade
   */
  calculateQualityMetrics(interactions) {
    const successfulInteractions = interactions.filter((i) => i.success === true);
    const errorInteractions = interactions.filter((i) => i.success === false || i.error === true);
    return {
      accuracy: successfulInteractions.length / Math.max(interactions.length, 1),
      errorRate: errorInteractions.length / Math.max(interactions.length, 1),
      consistency: this.calculateConsistency(interactions),
      improvement: this.calculateImprovementTrend(interactions),
      qualityScore: this.calculateOverallQualityScore(interactions)
    };
  }
  /**
   * Métodos auxiliares
   */
  findMostCommonError(errorTypes) {
    const entries = Object.entries(errorTypes);
    if (entries.length === 0) return "none";
    const maxEntry = entries.reduce((max, curr) => curr[1] > max[1] ? curr : max);
    return maxEntry[0];
  }
  divideIntoTimeBlocks(interactions, blockCount) {
    const blockSize = Math.ceil(interactions.length / blockCount);
    const blocks = [];
    for (let i = 0; i < interactions.length; i += blockSize) {
      blocks.push(interactions.slice(i, i + blockSize));
    }
    return blocks;
  }
  identifyFrequencyPattern(frequencies) {
    if (frequencies.length < 2) return "insufficient_data";
    const trend = this.calculateTrend(frequencies);
    if (trend > 0.1) return "increasing";
    if (trend < -0.1) return "decreasing";
    return "stable";
  }
  calculateErrorStability(frequencies) {
    if (frequencies.length < 2) return "stable";
    const variance = this.calculateVariance(frequencies);
    if (variance < 0.01) return "very_stable";
    if (variance < 0.05) return "stable";
    if (variance < 0.1) return "moderate";
    return "unstable";
  }
  calculateTimeGaps(timestamps) {
    const gaps = [];
    for (let i = 1; i < timestamps.length; i++) {
      gaps.push(timestamps[i] - timestamps[i - 1]);
    }
    return gaps;
  }
  identifyTemporalPattern(timeGaps) {
    if (timeGaps.length === 0) return "none";
    const avgGap = timeGaps.reduce((a, b) => a + b, 0) / timeGaps.length;
    const variance = this.calculateVariance(timeGaps);
    if (variance < avgGap * 0.1) return "regular";
    if (variance < avgGap * 0.5) return "semi_regular";
    return "irregular";
  }
  analyzeErrorConcentration(timestamps) {
    if (timestamps.length < 2) return "dispersed";
    const gaps = this.calculateTimeGaps(timestamps);
    const avgGap = gaps.reduce((a, b) => a + b, 0) / gaps.length;
    const shortGaps = gaps.filter((gap) => gap < avgGap * 0.5);
    if (shortGaps.length > gaps.length * 0.7) return "concentrated";
    if (shortGaps.length > gaps.length * 0.3) return "moderate";
    return "dispersed";
  }
  analyzeErrorTiming(timestamps) {
    return "random";
  }
  findErrorSequences(interactions) {
    const sequences = [];
    let currentSequence = [];
    interactions.forEach((interaction) => {
      if (interaction.success === false || interaction.error === true) {
        currentSequence.push(interaction);
      } else {
        if (currentSequence.length > 0) {
          sequences.push(currentSequence);
          currentSequence = [];
        }
      }
    });
    if (currentSequence.length > 0) {
      sequences.push(currentSequence);
    }
    return sequences;
  }
  calculateRecoveryRate(interactions) {
    let recoveries = 0;
    let errorSequences = 0;
    let inErrorSequence = false;
    interactions.forEach((interaction) => {
      if (interaction.success === false || interaction.error === true) {
        if (!inErrorSequence) {
          errorSequences++;
          inErrorSequence = true;
        }
      } else {
        if (inErrorSequence) {
          recoveries++;
          inErrorSequence = false;
        }
      }
    });
    return errorSequences > 0 ? recoveries / errorSequences : 1;
  }
  calculatePersistenceScore(errorSequences) {
    if (errorSequences.length === 0) return 0;
    const totalErrors = errorSequences.reduce((sum, seq) => sum + seq.length, 0);
    const averageSequenceLength = totalErrors / errorSequences.length;
    return Math.min(1, averageSequenceLength / 5);
  }
  calculateConsistency(interactions) {
    const successRates = this.divideIntoTimeBlocks(interactions, 5).map((block) => {
      const successes = block.filter((i) => i.success === true);
      return successes.length / Math.max(block.length, 1);
    });
    const variance = this.calculateVariance(successRates);
    return Math.max(0, 1 - variance);
  }
  calculateImprovementTrend(interactions) {
    Math.ceil(interactions.length / 5);
    const blocks = this.divideIntoTimeBlocks(interactions, 5);
    const successRates = blocks.map((block) => {
      const successes = block.filter((i) => i.success === true);
      return successes.length / Math.max(block.length, 1);
    });
    return this.calculateTrend(successRates);
  }
  calculateOverallQualityScore(interactions) {
    const accuracy = interactions.filter((i) => i.success === true).length / Math.max(interactions.length, 1);
    const consistency = this.calculateConsistency(interactions);
    const improvement = Math.max(0, this.calculateImprovementTrend(interactions));
    return accuracy * 0.5 + consistency * 0.3 + improvement * 0.2;
  }
  calculateTrend(values) {
    if (values.length < 2) return 0;
    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));
    const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;
    return (secondAvg - firstAvg) / Math.max(firstAvg, 1e-3);
  }
  calculateVariance(values) {
    if (values.length < 2) return 0;
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return variance;
  }
  calculateConfidence(interactions) {
    if (interactions.length < 5) return 0.3;
    if (interactions.length < 10) return 0.6;
    if (interactions.length < 20) return 0.8;
    return 0.95;
  }
  createEmptyAnalysis() {
    return {
      errorTypes: {
        distribution: {},
        totalErrors: 0,
        mostCommonError: "none",
        errorRate: 0
      },
      errorFrequency: {
        frequency: 0,
        pattern: "none",
        stability: "stable"
      },
      temporalPatterns: {
        pattern: "none",
        concentration: "dispersed",
        timing: "random"
      },
      errorPersistence: {
        maxSequenceLength: 0,
        averageSequenceLength: 0,
        sequenceCount: 0,
        recoveryRate: 1,
        persistence: 0
      },
      qualityMetrics: {
        accuracy: 0,
        errorRate: 0,
        consistency: 0,
        improvement: 0,
        qualityScore: 0
      },
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      collector: this.collectorName,
      confidence: 0.1
    };
  }
  createErrorAnalysis(error) {
    return {
      error: error.message,
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      collector: this.collectorName,
      confidence: 0
    };
  }
}
class SequenceReproductionCollector {
  constructor() {
    this.name = "SequenceReproductionCollector";
    this.version = "3.0.0";
    this.description = "Analisa habilidades de reprodução de sequências visuais";
  }
  async collect(gameState) {
    try {
      const sequenceData = gameState.sequenceToReproduce || [];
      const playerData = gameState.playerSequence || [];
      const timeData = gameState.responseTime || 0;
      return {
        // Métricas de capacidade sequencial
        sequenceLength: sequenceData.length,
        reproductionAccuracy: this.calculateReproductionAccuracy(sequenceData, playerData),
        visualMemorySpan: this.assessVisualMemorySpan(sequenceData.length, gameState.difficulty),
        sequentialProcessingSpeed: this.calculateProcessingSpeed(timeData, sequenceData.length),
        // Análise de erros
        errorPositions: this.analyzeErrorPositions(sequenceData, playerData),
        errorTypes: this.categorizeErrors(sequenceData, playerData),
        // Padrões de performance
        consistencyScore: this.assessConsistency(gameState.sessionAttempts || []),
        improvementTrend: this.calculateImprovementTrend(gameState.sessionAttempts || []),
        // Métricas terapêuticas
        memoryLoadCapacity: this.assessMemoryLoad(sequenceData.length),
        attentionSustainedScore: this.evaluateAttentionSustained(timeData),
        cognitiveFlexibilityIndex: this.assessCognitiveFlexibility(gameState),
        timestamp: Date.now(),
        sessionId: gameState.sessionId || "unknown"
      };
    } catch (error) {
      console.error("Erro no SequenceReproductionCollector:", error);
      return null;
    }
  }
  calculateReproductionAccuracy(original, reproduction) {
    if (!original.length || !reproduction.length) return 0;
    let correct2 = 0;
    const maxLength = Math.max(original.length, reproduction.length);
    for (let i = 0; i < maxLength; i++) {
      const origElement = original[i];
      const reprElement = reproduction[i];
      if (origElement && reprElement) {
        if (origElement.shape === reprElement.shape && origElement.color === reprElement.color) {
          correct2++;
        }
      }
    }
    return correct2 / original.length * 100;
  }
  assessVisualMemorySpan(sequenceLength, difficulty) {
    const baseScore = sequenceLength * 10;
    const difficultyMultiplier = {
      easy: 1,
      medium: 1.2,
      hard: 1.5
    };
    return Math.min(100, baseScore * (difficultyMultiplier[difficulty] || 1));
  }
  calculateProcessingSpeed(responseTime, sequenceLength) {
    const optimalTime = sequenceLength * 1e3;
    const speedRatio = optimalTime / Math.max(responseTime, 1e3);
    return Math.min(100, speedRatio * 100);
  }
  analyzeErrorPositions(original, reproduction) {
    const errors = [];
    for (let i = 0; i < original.length; i++) {
      const origElement = original[i];
      const reprElement = reproduction[i];
      if (!reprElement || origElement.shape !== reprElement.shape || origElement.color !== reprElement.color) {
        errors.push({
          position: i,
          expected: origElement,
          actual: reprElement || null,
          errorType: this.categorizeError(origElement, reprElement)
        });
      }
    }
    return errors;
  }
  categorizeError(expected, actual) {
    if (!actual) return "missing";
    if (expected.shape !== actual.shape && expected.color !== actual.color) return "complete_mismatch";
    if (expected.shape !== actual.shape) return "shape_error";
    if (expected.color !== actual.color) return "color_error";
    return "unknown";
  }
  categorizeErrors(original, reproduction) {
    const errors = this.analyzeErrorPositions(original, reproduction);
    const categories = {
      shape_errors: 0,
      color_errors: 0,
      position_errors: 0,
      missing_elements: 0,
      complete_mismatches: 0
    };
    errors.forEach((error) => {
      switch (error.errorType) {
        case "shape_error":
          categories.shape_errors++;
          break;
        case "color_error":
          categories.color_errors++;
          break;
        case "missing":
          categories.missing_elements++;
          break;
        case "complete_mismatch":
          categories.complete_mismatches++;
          break;
      }
    });
    return categories;
  }
  assessConsistency(attempts) {
    if (attempts.length < 2) return 100;
    const accuracies = attempts.map((attempt) => attempt.accuracy || 0);
    const mean = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    const variance = accuracies.reduce((sum, acc) => sum + Math.pow(acc - mean, 2), 0) / accuracies.length;
    const standardDeviation = Math.sqrt(variance);
    return Math.max(0, 100 - standardDeviation * 2);
  }
  calculateImprovementTrend(attempts) {
    if (attempts.length < 3) return 0;
    const recentAttempts = attempts.slice(-5);
    const firstHalf = recentAttempts.slice(0, Math.floor(recentAttempts.length / 2));
    const secondHalf = recentAttempts.slice(Math.floor(recentAttempts.length / 2));
    const firstAvg = firstHalf.reduce((sum, att) => sum + (att.accuracy || 0), 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, att) => sum + (att.accuracy || 0), 0) / secondHalf.length;
    return secondAvg - firstAvg;
  }
  assessMemoryLoad(sequenceLength) {
    const memoryCapacity = 7;
    const loadPercentage = sequenceLength / memoryCapacity * 100;
    return Math.min(100, loadPercentage);
  }
  evaluateAttentionSustained(responseTime) {
    const optimalRange = { min: 3e3, max: 15e3 };
    if (responseTime < optimalRange.min) {
      return 70;
    } else if (responseTime > optimalRange.max) {
      return Math.max(30, 100 - (responseTime - optimalRange.max) / 1e3 * 5);
    } else {
      return 100;
    }
  }
  assessCognitiveFlexibility(gameState) {
    const attempts = gameState.sessionAttempts || [];
    if (attempts.length < 3) return 50;
    const patternTypes = /* @__PURE__ */ new Set();
    attempts.forEach((attempt) => {
      if (attempt.patternType) {
        patternTypes.add(attempt.patternType);
      }
    });
    return Math.min(100, patternTypes.size / 5 * 100);
  }
}
class PatternCompletionCollector {
  constructor() {
    this.name = "PatternCompletionCollector";
    this.version = "3.0.0";
    this.description = "Analisa habilidades de identificação e completamento de padrões visuais";
  }
  async collect(gameState) {
    try {
      const pattern = gameState.incompletePattern || [];
      const playerCompletion = gameState.playerCompletion || [];
      const correctCompletion = gameState.correctCompletion || [];
      return {
        // Métricas de reconhecimento de padrões
        patternRecognitionAccuracy: this.calculatePatternAccuracy(correctCompletion, playerCompletion),
        logicalReasoningScore: this.assessLogicalReasoning(pattern, correctCompletion, playerCompletion),
        patternComplexityHandled: this.evaluateComplexityHandling(pattern),
        completionEfficiency: this.calculateCompletionEfficiency(gameState.responseTime, pattern.length),
        // Análise de estratégias
        completionStrategy: this.analyzeCompletionStrategy(pattern, playerCompletion),
        strategicConsistency: this.assessStrategicConsistency(gameState.sessionAttempts || []),
        // Habilidades cognitivas
        spatialReasoningIndex: this.assessSpatialReasoning(pattern, playerCompletion),
        abstractThinkingScore: this.evaluateAbstractThinking(pattern, gameState.difficulty),
        problemSolvingApproach: this.analyzeProblemSolvingApproach(gameState),
        // Métricas de aprendizagem
        adaptationRate: this.calculateAdaptationRate(gameState.sessionAttempts || []),
        learningCurveSlope: this.assessLearningCurve(gameState.sessionAttempts || []),
        timestamp: Date.now(),
        sessionId: gameState.sessionId || "unknown"
      };
    } catch (error) {
      console.error("Erro no PatternCompletionCollector:", error);
      return null;
    }
  }
  calculatePatternAccuracy(correct2, player) {
    if (!correct2.length || !player.length) return 0;
    let matches = 0;
    const maxLength = Math.max(correct2.length, player.length);
    for (let i = 0; i < maxLength; i++) {
      const correctElement = correct2[i];
      const playerElement = player[i];
      if (correctElement && playerElement) {
        if (this.elementsMatch(correctElement, playerElement)) {
          matches++;
        }
      }
    }
    return matches / correct2.length * 100;
  }
  elementsMatch(element1, element2) {
    return element1.shape === element2.shape && element1.color === element2.color && element1.size === element2.size;
  }
  assessLogicalReasoning(pattern, correct2, player) {
    const patternRule = this.identifyPatternRule(pattern);
    const playerFollowsRule = this.checkRuleCompliance(player, patternRule);
    const correctFollowsRule = this.checkRuleCompliance(correct2, patternRule);
    if (correctFollowsRule === 0) return 0;
    return playerFollowsRule / correctFollowsRule * 100;
  }
  identifyPatternRule(pattern) {
    if (pattern.length < 3) return { type: "simple", confidence: 0.5 };
    const repetitionPattern = this.checkRepetitionPattern(pattern);
    if (repetitionPattern.confidence > 0.8) {
      return { type: "repetition", ...repetitionPattern };
    }
    const progressionPattern = this.checkProgressionPattern(pattern);
    if (progressionPattern.confidence > 0.8) {
      return { type: "progression", ...progressionPattern };
    }
    const alternationPattern = this.checkAlternationPattern(pattern);
    if (alternationPattern.confidence > 0.8) {
      return { type: "alternation", ...alternationPattern };
    }
    return { type: "complex", confidence: 0.3 };
  }
  checkRepetitionPattern(pattern) {
    for (let size = 1; size <= pattern.length / 2; size++) {
      let isRepeating = true;
      const segment = pattern.slice(0, size);
      for (let i = size; i < pattern.length; i++) {
        if (!this.elementsMatch(pattern[i], segment[i % size])) {
          isRepeating = false;
          break;
        }
      }
      if (isRepeating) {
        return {
          segmentSize: size,
          confidence: 0.9,
          rule: "repeat_segment"
        };
      }
    }
    return { confidence: 0 };
  }
  checkProgressionPattern(pattern) {
    const properties = ["shape", "color", "size"];
    for (const prop of properties) {
      const progression = this.analyzePropertyProgression(pattern, prop);
      if (progression.confidence > 0.7) {
        return {
          property: prop,
          direction: progression.direction,
          confidence: progression.confidence,
          rule: "progression"
        };
      }
    }
    return { confidence: 0 };
  }
  analyzePropertyProgression(pattern, property) {
    const values = pattern.map((element) => element[property]);
    const uniqueValues = [...new Set(values)];
    if (uniqueValues.length < 2) return { confidence: 0 };
    let isAscending = true;
    let isDescending = true;
    for (let i = 1; i < values.length; i++) {
      if (values[i] <= values[i - 1]) isAscending = false;
      if (values[i] >= values[i - 1]) isDescending = false;
    }
    if (isAscending) {
      return { direction: "ascending", confidence: 0.8 };
    } else if (isDescending) {
      return { direction: "descending", confidence: 0.8 };
    }
    return { confidence: 0 };
  }
  checkAlternationPattern(pattern) {
    if (pattern.length < 4) return { confidence: 0 };
    const properties = ["shape", "color", "size"];
    for (const prop of properties) {
      if (this.isAlternatingProperty(pattern, prop)) {
        return {
          property: prop,
          confidence: 0.9,
          rule: "alternation"
        };
      }
    }
    return { confidence: 0 };
  }
  isAlternatingProperty(pattern, property) {
    const values = pattern.map((element) => element[property]);
    for (let i = 2; i < values.length; i++) {
      if (values[i] !== values[i - 2]) {
        return false;
      }
    }
    return values[0] !== values[1];
  }
  checkRuleCompliance(completion, rule) {
    if (!rule || rule.confidence < 0.5) return 0;
    let compliance = 0;
    switch (rule.type) {
      case "repetition":
        compliance = this.checkRepetitionCompliance(completion, rule);
        break;
      case "progression":
        compliance = this.checkProgressionCompliance(completion, rule);
        break;
      case "alternation":
        compliance = this.checkAlternationCompliance(completion, rule);
        break;
      default:
        compliance = 0.5;
    }
    return compliance;
  }
  checkRepetitionCompliance(completion, rule) {
    rule.segmentSize;
    let compliance = 0;
    for (let i = 0; i < completion.length; i++) {
      compliance += 0.5;
    }
    return completion.length > 0 ? compliance / completion.length : 0;
  }
  checkProgressionCompliance(completion, rule) {
    if (completion.length < 2) return 0.5;
    const values = completion.map((element) => element[rule.property]);
    let isCorrectDirection = true;
    for (let i = 1; i < values.length; i++) {
      if (rule.direction === "ascending" && values[i] <= values[i - 1]) {
        isCorrectDirection = false;
      } else if (rule.direction === "descending" && values[i] >= values[i - 1]) {
        isCorrectDirection = false;
      }
    }
    return isCorrectDirection ? 1 : 0.2;
  }
  checkAlternationCompliance(completion, rule) {
    if (completion.length < 2) return 0.5;
    const values = completion.map((element) => element[rule.property]);
    for (let i = 2; i < values.length; i++) {
      if (values[i] !== values[i - 2]) {
        return 0.2;
      }
    }
    return 1;
  }
  evaluateComplexityHandling(pattern) {
    const factors = {
      length: Math.min(pattern.length / 10, 1),
      // Normalizado para 10 elementos
      uniqueShapes: new Set(pattern.map((e) => e.shape)).size,
      uniqueColors: new Set(pattern.map((e) => e.color)).size,
      uniqueSizes: new Set(pattern.map((e) => e.size)).size
    };
    const complexityScore = factors.length * 30 + factors.uniqueShapes * 20 + factors.uniqueColors * 20 + factors.uniqueSizes * 30;
    return Math.min(100, complexityScore);
  }
  calculateCompletionEfficiency(responseTime, patternLength) {
    const expectedTime = patternLength * 2e3;
    const efficiency = expectedTime / Math.max(responseTime, 1e3);
    return Math.min(100, efficiency * 100);
  }
  analyzeCompletionStrategy(pattern, completion) {
    if (completion.length === 0) return "no_attempt";
    const timeToFirstElement = completion[0]?.timestamp || 0;
    const averageTimeBetweenElements = this.calculateAverageTimeBetween(completion);
    if (averageTimeBetweenElements < 1e3) {
      return "rapid_fire";
    } else if (averageTimeBetweenElements > 5e3) {
      return "deliberate";
    } else if (timeToFirstElement > 1e4) {
      return "analytical";
    } else {
      return "balanced";
    }
  }
  calculateAverageTimeBetween(completion) {
    if (completion.length < 2) return 0;
    let totalTime = 0;
    for (let i = 1; i < completion.length; i++) {
      const timeDiff = (completion[i].timestamp || 0) - (completion[i - 1].timestamp || 0);
      totalTime += timeDiff;
    }
    return totalTime / (completion.length - 1);
  }
  assessStrategicConsistency(attempts) {
    if (attempts.length < 2) return 100;
    const strategies = attempts.map((attempt) => attempt.strategy || "unknown");
    const uniqueStrategies = new Set(strategies);
    return Math.max(0, 100 - (uniqueStrategies.size - 1) * 20);
  }
  assessSpatialReasoning(pattern, completion) {
    const spatialRelationships = this.analyzeSpatialRelationships(pattern);
    const completionSpatialConsistency = this.checkSpatialConsistency(completion, spatialRelationships);
    return completionSpatialConsistency * 100;
  }
  analyzeSpatialRelationships(pattern) {
    const relationships = {
      adjacency: [],
      symmetry: false,
      progression: false
    };
    for (let i = 0; i < pattern.length - 1; i++) {
      const current = pattern[i];
      const next = pattern[i + 1];
      relationships.adjacency.push({
        similarity: this.calculateSimilarity(current, next),
        position: i
      });
    }
    return relationships;
  }
  calculateSimilarity(element1, element2) {
    let similarity = 0;
    if (element1.shape === element2.shape) similarity += 0.33;
    if (element1.color === element2.color) similarity += 0.33;
    if (element1.size === element2.size) similarity += 0.34;
    return similarity;
  }
  checkSpatialConsistency(completion, relationships) {
    if (completion.length === 0 || relationships.adjacency.length === 0) return 0.5;
    let consistencyScore = 0;
    let validComparisons = 0;
    for (let i = 0; i < completion.length - 1; i++) {
      if (i < relationships.adjacency.length) {
        const expectedSimilarity = relationships.adjacency[i].similarity;
        const actualSimilarity = this.calculateSimilarity(completion[i], completion[i + 1]);
        const difference = Math.abs(expectedSimilarity - actualSimilarity);
        consistencyScore += Math.max(0, 1 - difference);
        validComparisons++;
      }
    }
    return validComparisons > 0 ? consistencyScore / validComparisons : 0.5;
  }
  evaluateAbstractThinking(pattern, difficulty) {
    const difficultyMultipliers = {
      easy: 0.5,
      medium: 0.8,
      hard: 1
    };
    const patternComplexity = this.evaluateComplexityHandling(pattern);
    const abstractionLevel = patternComplexity * (difficultyMultipliers[difficulty] || 0.8);
    return Math.min(100, abstractionLevel);
  }
  analyzeProblemSolvingApproach(gameState) {
    const responseTime = gameState.responseTime || 0;
    const attempts = gameState.sessionAttempts || [];
    if (responseTime < 5e3) {
      return "intuitive";
    } else if (responseTime > 3e4) {
      return "systematic";
    } else if (attempts.length > 3 && this.hasImprovementTrend(attempts)) {
      return "adaptive";
    } else {
      return "trial_error";
    }
  }
  hasImprovementTrend(attempts) {
    if (attempts.length < 3) return false;
    const recentScores = attempts.slice(-3).map((attempt) => attempt.accuracy || 0);
    return recentScores[2] > recentScores[0];
  }
  calculateAdaptationRate(attempts) {
    if (attempts.length < 3) return 50;
    const performanceChanges = [];
    for (let i = 1; i < attempts.length; i++) {
      const change = (attempts[i].accuracy || 0) - (attempts[i - 1].accuracy || 0);
      performanceChanges.push(change);
    }
    const positiveChanges = performanceChanges.filter((change) => change > 0).length;
    return positiveChanges / performanceChanges.length * 100;
  }
  assessLearningCurve(attempts) {
    if (attempts.length < 4) return 0;
    const scores = attempts.map((attempt) => attempt.accuracy || 0);
    const firstHalf = scores.slice(0, Math.floor(scores.length / 2));
    const secondHalf = scores.slice(Math.floor(scores.length / 2));
    const firstAvg = firstHalf.reduce((sum, score) => sum + score, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, score) => sum + score, 0) / secondHalf.length;
    return secondAvg - firstAvg;
  }
}
class PatternConstructionCollector {
  constructor() {
    this.name = "PatternConstructionCollector";
    this.version = "3.0.0";
    this.description = "Analisa habilidades de criação e construção de padrões visuais";
  }
  async collect(gameState) {
    try {
      const targetPattern = gameState.targetPattern || [];
      const constructedPattern = gameState.constructedPattern || [];
      const constructionSteps = gameState.constructionSteps || [];
      return {
        // Métricas de construção
        constructionAccuracy: this.calculateConstructionAccuracy(targetPattern, constructedPattern),
        spatialOrganizationScore: this.assessSpatialOrganization(constructedPattern),
        constructionEfficiency: this.calculateConstructionEfficiency(constructionSteps, targetPattern.length),
        planningQuality: this.assessPlanningQuality(constructionSteps),
        // Análise do processo
        constructionStrategy: this.analyzeConstructionStrategy(constructionSteps),
        stepSequenceOptimality: this.assessStepOptimality(constructionSteps, targetPattern),
        errorCorrectionAbility: this.evaluateErrorCorrection(constructionSteps),
        // Habilidades visuoespaciais
        spatialVisualizationIndex: this.assessSpatialVisualization(constructedPattern, targetPattern),
        constructiveApraxiaIndicators: this.evaluateConstructiveApraxia(constructionSteps),
        visualMotorIntegration: this.assessVisualMotorIntegration(constructionSteps),
        // Funções executivas
        executivePlanningScore: this.assessExecutivePlanning(constructionSteps),
        workingMemoryLoad: this.evaluateWorkingMemoryLoad(targetPattern, constructionSteps),
        inhibitoryControlIndex: this.assessInhibitoryControl(constructionSteps),
        timestamp: Date.now(),
        sessionId: gameState.sessionId || "unknown"
      };
    } catch (error) {
      console.error("Erro no PatternConstructionCollector:", error);
      return null;
    }
  }
  calculateConstructionAccuracy(target, constructed) {
    if (!target.length || !constructed.length) return 0;
    let correctPlacements = 0;
    Math.max(target.length, constructed.length);
    for (let i = 0; i < Math.min(target.length, constructed.length); i++) {
      if (this.elementsMatch(target[i], constructed[i])) {
        correctPlacements++;
      }
    }
    const lengthPenalty = Math.abs(target.length - constructed.length);
    const accuracyScore = correctPlacements / target.length * 100;
    const lengthPenaltyScore = lengthPenalty / target.length * 20;
    return Math.max(0, accuracyScore - lengthPenaltyScore);
  }
  elementsMatch(element1, element2) {
    if (!element1 || !element2) return false;
    return element1.shape === element2.shape && element1.color === element2.color && element1.size === element2.size && element1.position?.x === element2.position?.x && element1.position?.y === element2.position?.y;
  }
  assessSpatialOrganization(constructedPattern) {
    if (constructedPattern.length < 2) return 50;
    const spatialMetrics = {
      alignment: this.checkAlignment(constructedPattern),
      symmetry: this.checkSymmetry(constructedPattern),
      distribution: this.checkDistribution(constructedPattern),
      coherence: this.checkSpatialCoherence(constructedPattern)
    };
    return spatialMetrics.alignment * 0.3 + spatialMetrics.symmetry * 0.2 + spatialMetrics.distribution * 0.3 + spatialMetrics.coherence * 0.2;
  }
  checkAlignment(pattern) {
    const positions = pattern.map((element) => element.position).filter((pos) => pos);
    if (positions.length < 2) return 50;
    const yValues = positions.map((pos) => pos.y);
    const horizontalAlignment = this.calculateAlignment(yValues);
    const xValues = positions.map((pos) => pos.x);
    const verticalAlignment = this.calculateAlignment(xValues);
    return Math.max(horizontalAlignment, verticalAlignment);
  }
  calculateAlignment(values) {
    const uniqueValues = [...new Set(values)];
    const alignmentRatio = uniqueValues.length / values.length;
    return Math.max(0, 100 - alignmentRatio * 100);
  }
  checkSymmetry(pattern) {
    const positions = pattern.map((element) => element.position).filter((pos) => pos);
    if (positions.length < 4) return 50;
    const centerX = positions.reduce((sum, pos) => sum + pos.x, 0) / positions.length;
    const centerY = positions.reduce((sum, pos) => sum + pos.y, 0) / positions.length;
    const horizontalSymmetry = this.checkSymmetryAxis(pattern, centerX, "vertical");
    const verticalSymmetry = this.checkSymmetryAxis(pattern, centerY, "horizontal");
    return Math.max(horizontalSymmetry, verticalSymmetry);
  }
  checkSymmetryAxis(pattern, center, axis) {
    let symmetricPairs = 0;
    let totalElements = pattern.length;
    for (let element of pattern) {
      if (!element.position) continue;
      const mirrored = this.getMirroredPosition(element.position, center, axis);
      const hasSymmetricPair = pattern.some(
        (other) => other.position && Math.abs(other.position.x - mirrored.x) < 10 && Math.abs(other.position.y - mirrored.y) < 10 && this.elementsVisuallyMatch(element, other)
      );
      if (hasSymmetricPair) symmetricPairs++;
    }
    return symmetricPairs / totalElements * 100;
  }
  getMirroredPosition(position, center, axis) {
    if (axis === "vertical") {
      return { x: 2 * center - position.x, y: position.y };
    } else {
      return { x: position.x, y: 2 * center - position.y };
    }
  }
  elementsVisuallyMatch(element1, element2) {
    return element1.shape === element2.shape && element1.color === element2.color && element1.size === element2.size;
  }
  checkDistribution(pattern) {
    const positions = pattern.map((element) => element.position).filter((pos) => pos);
    if (positions.length < 3) return 50;
    const distances = [];
    for (let i = 0; i < positions.length - 1; i++) {
      for (let j = i + 1; j < positions.length; j++) {
        const distance = Math.sqrt(
          Math.pow(positions[j].x - positions[i].x, 2) + Math.pow(positions[j].y - positions[i].y, 2)
        );
        distances.push(distance);
      }
    }
    const meanDistance = distances.reduce((sum, d) => sum + d, 0) / distances.length;
    const variance = distances.reduce((sum, d) => sum + Math.pow(d - meanDistance, 2), 0) / distances.length;
    const standardDeviation = Math.sqrt(variance);
    const uniformityScore = Math.max(0, 100 - standardDeviation / meanDistance * 100);
    return uniformityScore;
  }
  checkSpatialCoherence(pattern) {
    const positions = pattern.map((element) => element.position).filter((pos) => pos);
    if (positions.length < 2) return 50;
    const bounds = this.calculateBounds(positions);
    const area = (bounds.maxX - bounds.minX) * (bounds.maxY - bounds.minY);
    const density = positions.length / Math.max(area, 1);
    const idealDensity = 5e-3;
    const densityScore = Math.max(0, 100 - Math.abs(density - idealDensity) * 1e4);
    return densityScore;
  }
  calculateBounds(positions) {
    return {
      minX: Math.min(...positions.map((pos) => pos.x)),
      maxX: Math.max(...positions.map((pos) => pos.x)),
      minY: Math.min(...positions.map((pos) => pos.y)),
      maxY: Math.max(...positions.map((pos) => pos.y))
    };
  }
  calculateConstructionEfficiency(steps, targetLength) {
    if (!steps.length || !targetLength) return 0;
    const totalTime = steps[steps.length - 1]?.timestamp - steps[0]?.timestamp || 0;
    const stepsCount = steps.length;
    const timeEfficiency = Math.max(0, 100 - totalTime / (targetLength * 3e3) * 100);
    const stepEfficiency = Math.max(0, 100 - (stepsCount - targetLength) / targetLength * 50);
    return (timeEfficiency + stepEfficiency) / 2;
  }
  assessPlanningQuality(steps) {
    if (steps.length < 2) return 50;
    const planningMetrics = {
      sequentialLogic: this.assessSequentialLogic(steps),
      backtracking: this.assessBacktracking(steps),
      hesitation: this.assessHesitation(steps),
      momentum: this.assessMomentum(steps)
    };
    return planningMetrics.sequentialLogic * 0.4 + planningMetrics.backtracking * 0.2 + planningMetrics.hesitation * 0.2 + planningMetrics.momentum * 0.2;
  }
  assessSequentialLogic(steps) {
    let logicalSequences = 0;
    for (let i = 1; i < steps.length; i++) {
      const currentStep = steps[i];
      const previousStep = steps[i - 1];
      if (this.isLogicalSequence(previousStep, currentStep)) {
        logicalSequences++;
      }
    }
    return logicalSequences / (steps.length - 1) * 100;
  }
  isLogicalSequence(step1, step2) {
    if (!step1.position || !step2.position) return false;
    const distance = Math.sqrt(
      Math.pow(step2.position.x - step1.position.x, 2) + Math.pow(step2.position.y - step1.position.y, 2)
    );
    return distance < 100;
  }
  assessBacktracking(steps) {
    const undoActions = steps.filter((step) => step.action === "undo" || step.action === "remove").length;
    const backtrackingRatio = undoActions / steps.length;
    return Math.max(0, 100 - backtrackingRatio * 200);
  }
  assessHesitation(steps) {
    if (steps.length < 2) return 100;
    const timeDifferences = [];
    for (let i = 1; i < steps.length; i++) {
      const timeDiff = steps[i].timestamp - steps[i - 1].timestamp;
      timeDifferences.push(timeDiff);
    }
    const meanTime = timeDifferences.reduce((sum, time) => sum + time, 0) / timeDifferences.length;
    const longPauses = timeDifferences.filter((time) => time > meanTime * 3).length;
    const hesitationRatio = longPauses / timeDifferences.length;
    return Math.max(0, 100 - hesitationRatio * 150);
  }
  assessMomentum(steps) {
    if (steps.length < 3) return 100;
    const timeDifferences = [];
    for (let i = 1; i < steps.length; i++) {
      const timeDiff = steps[i].timestamp - steps[i - 1].timestamp;
      timeDifferences.push(timeDiff);
    }
    const meanTime = timeDifferences.reduce((sum, time) => sum + time, 0) / timeDifferences.length;
    const variance = timeDifferences.reduce((sum, time) => sum + Math.pow(time - meanTime, 2), 0) / timeDifferences.length;
    const standardDeviation = Math.sqrt(variance);
    const momentumScore = Math.max(0, 100 - standardDeviation / meanTime * 100);
    return momentumScore;
  }
  analyzeConstructionStrategy(steps) {
    if (!steps.length) return "no_strategy";
    const constructionPattern = this.identifyConstructionPattern(steps);
    if (constructionPattern.linear > 0.8) return "linear";
    if (constructionPattern.centerOut > 0.8) return "center_out";
    if (constructionPattern.outsideIn > 0.8) return "outside_in";
    if (constructionPattern.clustered > 0.8) return "clustered";
    return "mixed";
  }
  identifyConstructionPattern(steps) {
    const positions = steps.map((step) => step.position).filter((pos) => pos);
    if (positions.length < 3) return { linear: 0, centerOut: 0, outsideIn: 0, clustered: 0 };
    return {
      linear: this.calculateLinearPattern(positions),
      centerOut: this.calculateCenterOutPattern(positions),
      outsideIn: this.calculateOutsideInPattern(positions),
      clustered: this.calculateClusteredPattern(positions)
    };
  }
  calculateLinearPattern(positions) {
    let linearConnections = 0;
    for (let i = 1; i < positions.length; i++) {
      const distance = Math.sqrt(
        Math.pow(positions[i].x - positions[i - 1].x, 2) + Math.pow(positions[i].y - positions[i - 1].y, 2)
      );
      if (distance < 150) {
        linearConnections++;
      }
    }
    return linearConnections / (positions.length - 1);
  }
  calculateCenterOutPattern(positions) {
    if (positions.length < 3) return 0;
    const centerX = positions.reduce((sum, pos) => sum + pos.x, 0) / positions.length;
    const centerY = positions.reduce((sum, pos) => sum + pos.y, 0) / positions.length;
    const distances = positions.map((pos) => Math.sqrt(
      Math.pow(pos.x - centerX, 2) + Math.pow(pos.y - centerY, 2)
    ));
    let increasingDistances = 0;
    for (let i = 1; i < distances.length; i++) {
      if (distances[i] >= distances[i - 1] * 0.8) {
        increasingDistances++;
      }
    }
    return increasingDistances / (distances.length - 1);
  }
  calculateOutsideInPattern(positions) {
    if (positions.length < 3) return 0;
    const centerX = positions.reduce((sum, pos) => sum + pos.x, 0) / positions.length;
    const centerY = positions.reduce((sum, pos) => sum + pos.y, 0) / positions.length;
    const distances = positions.map((pos) => Math.sqrt(
      Math.pow(pos.x - centerX, 2) + Math.pow(pos.y - centerY, 2)
    ));
    let decreasingDistances = 0;
    for (let i = 1; i < distances.length; i++) {
      if (distances[i] <= distances[i - 1] * 1.2) {
        decreasingDistances++;
      }
    }
    return decreasingDistances / (distances.length - 1);
  }
  calculateClusteredPattern(positions) {
    const clusters = this.identifyClusters(positions);
    const clusterSwitches = this.countClusterSwitches(positions, clusters);
    return Math.min(1, clusterSwitches / (positions.length * 0.3));
  }
  identifyClusters(positions) {
    const clusters = [];
    const visited = /* @__PURE__ */ new Set();
    for (let i = 0; i < positions.length; i++) {
      if (visited.has(i)) continue;
      const cluster = [i];
      visited.add(i);
      for (let j = i + 1; j < positions.length; j++) {
        if (visited.has(j)) continue;
        const distance = Math.sqrt(
          Math.pow(positions[j].x - positions[i].x, 2) + Math.pow(positions[j].y - positions[i].y, 2)
        );
        if (distance < 100) {
          cluster.push(j);
          visited.add(j);
        }
      }
      clusters.push(cluster);
    }
    return clusters;
  }
  countClusterSwitches(positions, clusters) {
    let switches = 0;
    let currentCluster = -1;
    for (let i = 0; i < positions.length; i++) {
      const positionCluster = this.findPositionCluster(i, clusters);
      if (positionCluster !== currentCluster) {
        switches++;
        currentCluster = positionCluster;
      }
    }
    return switches;
  }
  findPositionCluster(positionIndex, clusters) {
    for (let i = 0; i < clusters.length; i++) {
      if (clusters[i].includes(positionIndex)) {
        return i;
      }
    }
    return -1;
  }
  assessStepOptimality(steps, targetPattern) {
    const necessarySteps = targetPattern.length;
    const actualSteps = steps.filter((step) => step.action === "place" || step.action === "add").length;
    const stepEfficiency = necessarySteps / Math.max(actualSteps, 1);
    return Math.min(100, stepEfficiency * 100);
  }
  evaluateErrorCorrection(steps) {
    const errorActions = steps.filter(
      (step) => step.action === "undo" || step.action === "remove" || step.action === "correct"
    );
    const successfulCorrections = errorActions.filter(
      (action) => this.wasSuccessfulCorrection(action, steps)
    ).length;
    if (errorActions.length === 0) return 100;
    return successfulCorrections / errorActions.length * 100;
  }
  wasSuccessfulCorrection(errorAction, allSteps) {
    const errorIndex = allSteps.indexOf(errorAction);
    const subsequentSteps = allSteps.slice(errorIndex + 1, errorIndex + 3);
    return subsequentSteps.some(
      (step) => step.action === "place" || step.action === "add"
    );
  }
  assessSpatialVisualization(constructed, target) {
    if (!target.length || !constructed.length) return 50;
    const spatialMatchScore = this.calculateSpatialMatch(constructed, target);
    const orientationScore = this.assessOrientation(constructed, target);
    const proportionScore = this.assessProportions(constructed, target);
    return (spatialMatchScore + orientationScore + proportionScore) / 3;
  }
  calculateSpatialMatch(constructed, target) {
    let matches = 0;
    for (let targetElement of target) {
      const bestMatch = this.findBestSpatialMatch(targetElement, constructed);
      if (bestMatch.score > 0.8) {
        matches++;
      }
    }
    return matches / target.length * 100;
  }
  findBestSpatialMatch(targetElement, constructedElements) {
    let bestScore = 0;
    let bestElement = null;
    for (let constructedElement of constructedElements) {
      const score = this.calculateElementSimilarity(targetElement, constructedElement);
      if (score > bestScore) {
        bestScore = score;
        bestElement = constructedElement;
      }
    }
    return { score: bestScore, element: bestElement };
  }
  calculateElementSimilarity(element1, element2) {
    let similarity = 0;
    if (element1.shape === element2.shape) similarity += 0.4;
    if (element1.color === element2.color) similarity += 0.3;
    if (element1.size === element2.size) similarity += 0.3;
    return similarity;
  }
  assessOrientation(constructed, target) {
    const targetBounds = this.calculateBounds(target.map((e) => e.position).filter((p) => p));
    const constructedBounds = this.calculateBounds(constructed.map((e) => e.position).filter((p) => p));
    if (!targetBounds || !constructedBounds) return 50;
    const targetAspectRatio = (targetBounds.maxX - targetBounds.minX) / (targetBounds.maxY - targetBounds.minY);
    const constructedAspectRatio = (constructedBounds.maxX - constructedBounds.minX) / (constructedBounds.maxY - constructedBounds.minY);
    const aspectRatioSimilarity = 1 - Math.abs(targetAspectRatio - constructedAspectRatio) / Math.max(targetAspectRatio, constructedAspectRatio);
    return aspectRatioSimilarity * 100;
  }
  assessProportions(constructed, target) {
    const targetSizes = target.map((e) => e.size).filter((s) => s);
    const constructedSizes = constructed.map((e) => e.size).filter((s) => s);
    if (!targetSizes.length || !constructedSizes.length) return 50;
    const targetSizeDistribution = this.calculateSizeDistribution(targetSizes);
    const constructedSizeDistribution = this.calculateSizeDistribution(constructedSizes);
    return this.compareSizeDistributions(targetSizeDistribution, constructedSizeDistribution);
  }
  calculateSizeDistribution(sizes) {
    const distribution = {};
    sizes.forEach((size) => {
      distribution[size] = (distribution[size] || 0) + 1;
    });
    const total = sizes.length;
    Object.keys(distribution).forEach((size) => {
      distribution[size] = distribution[size] / total;
    });
    return distribution;
  }
  compareSizeDistributions(dist1, dist2) {
    const allSizes = /* @__PURE__ */ new Set([...Object.keys(dist1), ...Object.keys(dist2)]);
    let similarity = 0;
    for (let size of allSizes) {
      const freq1 = dist1[size] || 0;
      const freq2 = dist2[size] || 0;
      similarity += 1 - Math.abs(freq1 - freq2);
    }
    return similarity / allSizes.size * 100;
  }
  evaluateConstructiveApraxia(steps) {
    const apraxiaIndicators = {
      fragmentation: this.assessFragmentation(steps),
      perseveration: this.assessPerseveration(steps),
      spatialDisorganization: this.assessSpatialDisorganization(steps),
      closingDifficulty: this.assessClosingDifficulty(steps)
    };
    const totalIndicators = Object.values(apraxiaIndicators).reduce((sum, val) => sum + val, 0);
    return Math.max(0, 100 - totalIndicators);
  }
  assessFragmentation(steps) {
    if (steps.length < 5) return 0;
    const positionJumps = [];
    for (let i = 1; i < steps.length; i++) {
      if (steps[i].position && steps[i - 1].position) {
        const distance = Math.sqrt(
          Math.pow(steps[i].position.x - steps[i - 1].position.x, 2) + Math.pow(steps[i].position.y - steps[i - 1].position.y, 2)
        );
        positionJumps.push(distance);
      }
    }
    const meanJump = positionJumps.reduce((sum, jump) => sum + jump, 0) / positionJumps.length;
    const largeJumps = positionJumps.filter((jump) => jump > meanJump * 2).length;
    return largeJumps / positionJumps.length * 25;
  }
  assessPerseveration(steps) {
    const actionCounts = {};
    steps.forEach((step) => {
      const key = `${step.action}_${step.position?.x}_${step.position?.y}`;
      actionCounts[key] = (actionCounts[key] || 0) + 1;
    });
    const repetitions = Object.values(actionCounts).filter((count) => count > 2).length;
    return repetitions / Object.keys(actionCounts).length * 25;
  }
  assessSpatialDisorganization(steps) {
    const positions = steps.map((step) => step.position).filter((pos) => pos);
    if (positions.length < 3) return 0;
    const organizationScore = this.checkSpatialCoherence(steps.map((step) => ({ position: step.position })));
    return Math.max(0, (100 - organizationScore) / 4);
  }
  assessClosingDifficulty(steps) {
    const lastQuarterSteps = steps.slice(-Math.ceil(steps.length / 4));
    const undoActionsInEnd = lastQuarterSteps.filter(
      (step) => step.action === "undo" || step.action === "remove"
    ).length;
    return undoActionsInEnd / lastQuarterSteps.length * 25;
  }
  assessVisualMotorIntegration(steps) {
    if (steps.length < 2) return 50;
    const motorMetrics = {
      precision: this.assessMotorPrecision(steps),
      consistency: this.assessMotorConsistency(steps),
      coordination: this.assessCoordination(steps)
    };
    return (motorMetrics.precision + motorMetrics.consistency + motorMetrics.coordination) / 3;
  }
  assessMotorPrecision(steps) {
    const placements = steps.filter((step) => step.action === "place" && step.position);
    if (placements.length < 2) return 100;
    const positionVariations = [];
    for (let i = 1; i < placements.length; i++) {
      const expectedDistance = 50;
      const actualDistance = Math.sqrt(
        Math.pow(placements[i].position.x - placements[i - 1].position.x, 2) + Math.pow(placements[i].position.y - placements[i - 1].position.y, 2)
      );
      positionVariations.push(Math.abs(actualDistance - expectedDistance));
    }
    const meanVariation = positionVariations.reduce((sum, var_) => sum + var_, 0) / positionVariations.length;
    return Math.max(0, 100 - meanVariation);
  }
  assessMotorConsistency(steps) {
    const timeBetweenActions = [];
    for (let i = 1; i < steps.length; i++) {
      timeBetweenActions.push(steps[i].timestamp - steps[i - 1].timestamp);
    }
    if (timeBetweenActions.length < 2) return 100;
    const meanTime = timeBetweenActions.reduce((sum, time) => sum + time, 0) / timeBetweenActions.length;
    const variance = timeBetweenActions.reduce((sum, time) => sum + Math.pow(time - meanTime, 2), 0) / timeBetweenActions.length;
    const standardDeviation = Math.sqrt(variance);
    const consistencyScore = Math.max(0, 100 - standardDeviation / meanTime * 100);
    return consistencyScore;
  }
  assessCoordination(steps) {
    const positions = steps.map((step) => step.position).filter((pos) => pos);
    if (positions.length < 3) return 100;
    let smoothness = 0;
    for (let i = 2; i < positions.length; i++) {
      const vector1 = {
        x: positions[i - 1].x - positions[i - 2].x,
        y: positions[i - 1].y - positions[i - 2].y
      };
      const vector2 = {
        x: positions[i].x - positions[i - 1].x,
        y: positions[i].y - positions[i - 1].y
      };
      const angle = this.calculateAngleBetweenVectors(vector1, vector2);
      smoothness += Math.max(0, 180 - Math.abs(angle));
    }
    return smoothness / (positions.length - 2);
  }
  calculateAngleBetweenVectors(v1, v2) {
    const dotProduct = v1.x * v2.x + v1.y * v2.y;
    const magnitude1 = Math.sqrt(v1.x * v1.x + v1.y * v1.y);
    const magnitude2 = Math.sqrt(v2.x * v2.x + v2.y * v2.y);
    if (magnitude1 === 0 || magnitude2 === 0) return 0;
    const cosTheta = dotProduct / (magnitude1 * magnitude2);
    return Math.acos(Math.max(-1, Math.min(1, cosTheta))) * (180 / Math.PI);
  }
  assessExecutivePlanning(steps) {
    const planningMetrics = {
      sequencing: this.assessSequencing(steps),
      goalDirection: this.assessGoalDirection(steps),
      flexibility: this.assessPlanningFlexibility(steps)
    };
    return (planningMetrics.sequencing + planningMetrics.goalDirection + planningMetrics.flexibility) / 3;
  }
  assessSequencing(steps) {
    const logicalSequences = this.assessSequentialLogic(steps);
    return logicalSequences;
  }
  assessGoalDirection(steps) {
    const constructiveActions = steps.filter(
      (step) => step.action === "place" || step.action === "add"
    ).length;
    const totalActions = steps.length;
    return constructiveActions / Math.max(totalActions, 1) * 100;
  }
  assessPlanningFlexibility(steps) {
    const strategyChanges = this.countStrategyChanges(steps);
    const adaptationScore = Math.min(100, strategyChanges * 20);
    return adaptationScore;
  }
  countStrategyChanges(steps) {
    if (steps.length < 6) return 0;
    const segments = this.divideIntoSegments(steps, 3);
    const strategies = segments.map((segment) => this.identifySegmentStrategy(segment));
    let changes = 0;
    for (let i = 1; i < strategies.length; i++) {
      if (strategies[i] !== strategies[i - 1]) {
        changes++;
      }
    }
    return changes;
  }
  divideIntoSegments(steps, segmentCount) {
    const segmentSize = Math.floor(steps.length / segmentCount);
    const segments = [];
    for (let i = 0; i < segmentCount; i++) {
      const start = i * segmentSize;
      const end = i === segmentCount - 1 ? steps.length : (i + 1) * segmentSize;
      segments.push(steps.slice(start, end));
    }
    return segments;
  }
  identifySegmentStrategy(segment) {
    const positions = segment.map((step) => step.position).filter((pos) => pos);
    if (positions.length < 2) return "undefined";
    const pattern = this.calculateLinearPattern(positions);
    if (pattern > 0.7) return "linear";
    const clustering = this.calculateClusteredPattern(positions);
    if (clustering > 0.7) return "clustered";
    return "mixed";
  }
  evaluateWorkingMemoryLoad(targetPattern, steps) {
    const patternComplexity = targetPattern.length;
    const simultaneousElements = this.calculateSimultaneousElements(steps);
    const memoryDemand = this.calculateMemoryDemand(targetPattern);
    const loadScore = (patternComplexity + simultaneousElements + memoryDemand) / 3;
    return Math.min(100, loadScore * 10);
  }
  calculateSimultaneousElements(steps) {
    const timeWindows = this.createTimeWindows(steps, 5e3);
    const maxSimultaneous = Math.max(...timeWindows.map((window2) => window2.length));
    return Math.min(10, maxSimultaneous);
  }
  createTimeWindows(steps, windowSize) {
    const windows = [];
    for (let i = 0; i < steps.length; i++) {
      const windowEnd = steps[i].timestamp;
      const windowStart = windowEnd - windowSize;
      const window2 = steps.filter(
        (step) => step.timestamp >= windowStart && step.timestamp <= windowEnd
      );
      windows.push(window2);
    }
    return windows;
  }
  calculateMemoryDemand(targetPattern) {
    const uniqueElements = new Set(targetPattern.map(
      (element) => `${element.shape}_${element.color}_${element.size}`
    )).size;
    return Math.min(10, uniqueElements);
  }
  assessInhibitoryControl(steps) {
    const impulsiveActions = this.countImpulsiveActions(steps);
    const correctionEfficiency = this.evaluateErrorCorrection(steps);
    const inhibitionScore = (100 - impulsiveActions) * 0.6 + correctionEfficiency * 0.4;
    return Math.max(0, inhibitionScore);
  }
  countImpulsiveActions(steps) {
    if (steps.length < 2) return 0;
    const fastActions = [];
    for (let i = 1; i < steps.length; i++) {
      const timeDiff = steps[i].timestamp - steps[i - 1].timestamp;
      if (timeDiff < 500) {
        fastActions.push(timeDiff);
      }
    }
    return fastActions.length / (steps.length - 1) * 100;
  }
}
class VisualClassificationCollector {
  constructor() {
    this.name = "VisualClassificationCollector";
    this.version = "3.0.0";
    this.description = "Analisa habilidades de classificação e categorização visual";
  }
  async collect(gameState) {
    try {
      const elements = gameState.elementsToClassify || [];
      const playerClassification = gameState.playerClassification || {};
      const correctClassification = gameState.correctClassification || {};
      const classificationCriteria = gameState.classificationCriteria || [];
      return {
        // Métricas de classificação
        classificationAccuracy: this.calculateClassificationAccuracy(correctClassification, playerClassification),
        categoryConsistency: this.assessCategoryConsistency(playerClassification, classificationCriteria),
        criteriaIdentificationScore: this.assessCriteriaIdentification(playerClassification, correctClassification),
        abstractionLevel: this.evaluateAbstractionLevel(classificationCriteria, gameState.difficulty),
        // Análise de estratégias cognitivas
        classificationStrategy: this.analyzeClassificationStrategy(gameState.classificationHistory || []),
        categoryFormationAbility: this.assessCategoryFormation(playerClassification),
        conceptualFlexibility: this.evaluateConceptualFlexibility(gameState.sessionAttempts || []),
        // Processamento visual
        visualDiscriminationIndex: this.assessVisualDiscrimination(elements, playerClassification),
        perceptualOrganizationScore: this.evaluatePerceptualOrganization(playerClassification),
        featureDetectionAccuracy: this.assessFeatureDetection(elements, playerClassification, correctClassification),
        // Funções executivas
        categoricalThinkingIndex: this.assessCategoricalThinking(playerClassification),
        ruleApplicationConsistency: this.evaluateRuleApplication(gameState.classificationHistory || []),
        inhibitionOfIncorrectResponses: this.assessResponseInhibition(gameState),
        timestamp: Date.now(),
        sessionId: gameState.sessionId || "unknown"
      };
    } catch (error) {
      console.error("Erro no VisualClassificationCollector:", error);
      return null;
    }
  }
  calculateClassificationAccuracy(correct2, player) {
    if (!Object.keys(correct2).length || !Object.keys(player).length) return 0;
    let correctClassifications = 0;
    let totalElements = 0;
    for (const elementId in correct2) {
      totalElements++;
      const correctCategory = correct2[elementId];
      const playerCategory = player[elementId];
      if (correctCategory === playerCategory) {
        correctClassifications++;
      }
    }
    const unclassifiedElements = Object.keys(correct2).length - Object.keys(player).length;
    const penaltyScore = Math.max(0, unclassifiedElements) * 0.1;
    const accuracy = totalElements > 0 ? correctClassifications / totalElements * 100 : 0;
    return Math.max(0, accuracy - penaltyScore);
  }
  assessCategoryConsistency(playerClassification, criteria) {
    if (!Object.keys(playerClassification).length || !criteria.length) return 50;
    const categories = this.extractCategories(playerClassification);
    let consistencyScore = 0;
    for (const category of categories) {
      const elementsInCategory = this.getElementsInCategory(playerClassification, category);
      const consistency = this.calculateCategoryConsistency(elementsInCategory, criteria);
      consistencyScore += consistency;
    }
    return categories.length > 0 ? consistencyScore / categories.length : 0;
  }
  extractCategories(classification) {
    const categories = new Set(Object.values(classification));
    return Array.from(categories);
  }
  getElementsInCategory(classification, category) {
    const elements = [];
    for (const [elementId, elementCategory] of Object.entries(classification)) {
      if (elementCategory === category) {
        elements.push(elementId);
      }
    }
    return elements;
  }
  calculateCategoryConsistency(elementsInCategory, criteria) {
    if (elementsInCategory.length < 2) return 100;
    let sharedFeatures = 0;
    let totalComparisons = 0;
    for (let i = 0; i < elementsInCategory.length - 1; i++) {
      for (let j = i + 1; j < elementsInCategory.length; j++) {
        const element1 = elementsInCategory[i];
        const element2 = elementsInCategory[j];
        const similarity = this.calculateElementSimilarity(element1, element2, criteria);
        sharedFeatures += similarity;
        totalComparisons++;
      }
    }
    return totalComparisons > 0 ? sharedFeatures / totalComparisons * 100 : 100;
  }
  calculateElementSimilarity(element1, element2, criteria) {
    let similarityScore = 0;
    let criteriaCount = 0;
    for (const criterion of criteria) {
      if (this.elementsShareCriterion(element1, element2, criterion)) {
        similarityScore++;
      }
      criteriaCount++;
    }
    return criteriaCount > 0 ? similarityScore / criteriaCount : 0;
  }
  elementsShareCriterion(element1, element2, criterion) {
    switch (criterion.type) {
      case "shape":
        return element1.shape === element2.shape;
      case "color":
        return element1.color === element2.color;
      case "size":
        return element1.size === element2.size;
      case "pattern":
        return element1.pattern === element2.pattern;
      case "orientation":
        return element1.orientation === element2.orientation;
      default:
        return false;
    }
  }
  assessCriteriaIdentification(playerClassification, correctClassification) {
    const playerCriteria = this.identifyImpliedCriteria(playerClassification);
    const correctCriteria = this.identifyImpliedCriteria(correctClassification);
    const criteriaOverlap = this.calculateCriteriaOverlap(playerCriteria, correctCriteria);
    return criteriaOverlap * 100;
  }
  identifyImpliedCriteria(classification) {
    const categories = this.extractCategories(classification);
    const impliedCriteria = /* @__PURE__ */ new Set();
    for (const category of categories) {
      const elementsInCategory = this.getElementsInCategory(classification, category);
      const categoryFeatures = this.extractCommonFeatures(elementsInCategory);
      categoryFeatures.forEach((feature) => impliedCriteria.add(feature));
    }
    return Array.from(impliedCriteria);
  }
  extractCommonFeatures(elements) {
    if (elements.length < 2) return [];
    const commonFeatures = [];
    const featureTypes = ["shape", "color", "size", "pattern", "orientation"];
    for (const featureType of featureTypes) {
      const values = elements.map((element) => element[featureType]).filter((val) => val);
      const uniqueValues = new Set(values);
      if (uniqueValues.size === 1) {
        commonFeatures.push(featureType);
      }
    }
    return commonFeatures;
  }
  calculateCriteriaOverlap(criteria1, criteria2) {
    if (criteria1.length === 0 && criteria2.length === 0) return 1;
    if (criteria1.length === 0 || criteria2.length === 0) return 0;
    const intersection = criteria1.filter((criterion) => criteria2.includes(criterion));
    const union = [.../* @__PURE__ */ new Set([...criteria1, ...criteria2])];
    return intersection.length / union.length;
  }
  evaluateAbstractionLevel(criteria, difficulty) {
    const complexityScores = {
      shape: 1,
      color: 1,
      size: 2,
      pattern: 3,
      orientation: 3,
      function: 4,
      conceptual: 5
    };
    const totalComplexity = criteria.reduce((sum, criterion) => {
      return sum + (complexityScores[criterion.type] || 1);
    }, 0);
    const difficultyMultipliers = {
      easy: 0.5,
      medium: 0.8,
      hard: 1.2
    };
    const abstractionScore = totalComplexity * (difficultyMultipliers[difficulty] || 1);
    return Math.min(100, abstractionScore * 10);
  }
  analyzeClassificationStrategy(classificationHistory) {
    if (!classificationHistory.length) return "no_strategy";
    const strategyMetrics = {
      systematic: this.assessSystematicApproach(classificationHistory),
      trialError: this.assessTrialErrorApproach(classificationHistory),
      hypothesis: this.assessHypothesisTestingApproach(classificationHistory),
      random: this.assessRandomApproach(classificationHistory)
    };
    const maxStrategy = Object.entries(strategyMetrics).reduce((max, [strategy, score]) => score > max.score ? { strategy, score } : max, { strategy: "unknown", score: 0 });
    return maxStrategy.strategy;
  }
  assessSystematicApproach(history) {
    if (history.length < 3) return 0;
    let sequentialProcessing = 0;
    for (let i = 1; i < history.length; i++) {
      const currentElement = history[i].elementId;
      const previousElement = history[i - 1].elementId;
      if (this.isSequentialOrder(currentElement, previousElement)) {
        sequentialProcessing++;
      }
    }
    return sequentialProcessing / (history.length - 1);
  }
  isSequentialOrder(current, previous) {
    const currentNum = parseInt(current.replace(/\D/g, ""));
    const previousNum = parseInt(previous.replace(/\D/g, ""));
    return !isNaN(currentNum) && !isNaN(previousNum) && currentNum === previousNum + 1;
  }
  assessTrialErrorApproach(history) {
    const corrections = history.filter((entry) => entry.action === "reclassify").length;
    const totalActions = history.length;
    return totalActions > 0 ? corrections / totalActions : 0;
  }
  assessHypothesisTestingApproach(history) {
    if (history.length < 3) return 0;
    const analysisTime = history.map((entry) => entry.thinkingTime || 0);
    const meanThinkingTime = analysisTime.reduce((sum, time) => sum + time, 0) / analysisTime.length;
    return Math.min(1, meanThinkingTime / 5e3);
  }
  assessRandomApproach(history) {
    if (history.length < 4) return 0;
    const categoryChanges = this.countCategoryChanges(history);
    const maxPossibleChanges = history.length - 1;
    return maxPossibleChanges > 0 ? categoryChanges / maxPossibleChanges : 0;
  }
  countCategoryChanges(history) {
    let changes = 0;
    for (let i = 1; i < history.length; i++) {
      if (history[i].category !== history[i - 1].category) {
        changes++;
      }
    }
    return changes;
  }
  assessCategoryFormation(playerClassification) {
    const categories = this.extractCategories(playerClassification);
    if (categories.length === 0) return 0;
    let categoryQualityScore = 0;
    for (const category of categories) {
      const elementsInCategory = this.getElementsInCategory(playerClassification, category);
      const categorySize = elementsInCategory.length;
      const categoryCoherence = this.assessCategoryCoherence(elementsInCategory);
      const sizeScore = this.evaluateCategorySize(categorySize, Object.keys(playerClassification).length);
      const categoryScore = (categoryCoherence + sizeScore) / 2;
      categoryQualityScore += categoryScore;
    }
    return categoryQualityScore / categories.length;
  }
  assessCategoryCoherence(elements) {
    if (elements.length < 2) return 50;
    const featureConsistency = this.calculateFeatureConsistency(elements);
    return featureConsistency * 100;
  }
  calculateFeatureConsistency(elements) {
    const features = ["shape", "color", "size", "pattern"];
    let consistencyScore = 0;
    for (const feature of features) {
      const values = elements.map((element) => element[feature]).filter((val) => val);
      const uniqueValues = new Set(values);
      const featureConsistency = values.length > 0 ? 1 - (uniqueValues.size - 1) / values.length : 0;
      consistencyScore += Math.max(0, featureConsistency);
    }
    return consistencyScore / features.length;
  }
  evaluateCategorySize(categorySize, totalElements) {
    const ratio = categorySize / totalElements;
    if (ratio >= 0.2 && ratio <= 0.5) {
      return 100;
    } else if (ratio < 0.1 || ratio > 0.8) {
      return 30;
    } else {
      return 70;
    }
  }
  evaluateConceptualFlexibility(sessionAttempts) {
    if (sessionAttempts.length < 2) return 50;
    const strategies = sessionAttempts.map((attempt) => attempt.strategy || "unknown");
    const uniqueStrategies = new Set(strategies);
    const flexibilityScore = uniqueStrategies.size / sessionAttempts.length * 100;
    return Math.min(100, flexibilityScore);
  }
  assessVisualDiscrimination(elements, playerClassification) {
    if (!elements.length || !Object.keys(playerClassification).length) return 50;
    const discriminationTasks = this.identifyDiscriminationTasks(elements);
    let discriminationScore = 0;
    for (const task of discriminationTasks) {
      const taskScore = this.evaluateDiscriminationTask(task, playerClassification);
      discriminationScore += taskScore;
    }
    return discriminationTasks.length > 0 ? discriminationScore / discriminationTasks.length : 50;
  }
  identifyDiscriminationTasks(elements) {
    const tasks = [];
    for (let i = 0; i < elements.length - 1; i++) {
      for (let j = i + 1; j < elements.length; j++) {
        const similarity = this.calculateVisualSimilarity(elements[i], elements[j]);
        if (similarity > 0.7) {
          tasks.push({
            element1: elements[i],
            element2: elements[j],
            difficulty: similarity
          });
        }
      }
    }
    return tasks;
  }
  calculateVisualSimilarity(element1, element2) {
    let similarity = 0;
    const features = ["shape", "color", "size", "pattern"];
    for (const feature of features) {
      if (element1[feature] === element2[feature]) {
        similarity += 0.25;
      }
    }
    return similarity;
  }
  evaluateDiscriminationTask(task, playerClassification) {
    const category1 = playerClassification[task.element1.id];
    const category2 = playerClassification[task.element2.id];
    if (category1 && category2) {
      return category1 !== category2 ? 100 : 30;
    }
    return 50;
  }
  evaluatePerceptualOrganization(playerClassification) {
    const categories = this.extractCategories(playerClassification);
    if (categories.length === 0) return 0;
    let organizationScore = 0;
    for (const category of categories) {
      const elements = this.getElementsInCategory(playerClassification, category);
      const categoryOrganization = this.assessCategoryOrganization(elements);
      organizationScore += categoryOrganization;
    }
    return organizationScore / categories.length;
  }
  assessCategoryOrganization(elements) {
    if (elements.length < 2) return 50;
    const organizationMetrics = {
      featureGrouping: this.assessFeatureGrouping(elements),
      hierarchicalStructure: this.assessHierarchicalStructure(elements),
      spatialOrganization: this.assessSpatialOrganization(elements)
    };
    return organizationMetrics.featureGrouping * 0.4 + organizationMetrics.hierarchicalStructure * 0.3 + organizationMetrics.spatialOrganization * 0.3;
  }
  assessFeatureGrouping(elements) {
    const featureGroups = this.groupByFeatures(elements);
    const groupSizes = Object.values(featureGroups).map((group) => group.length);
    const meanSize = groupSizes.reduce((sum, size) => sum + size, 0) / groupSizes.length;
    const variance = groupSizes.reduce((sum, size) => sum + Math.pow(size - meanSize, 2), 0) / groupSizes.length;
    return Math.max(0, 100 - variance * 10);
  }
  groupByFeatures(elements) {
    const groups = {};
    for (const element of elements) {
      const key = `${element.shape}_${element.color}`;
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(element);
    }
    return groups;
  }
  assessHierarchicalStructure(elements) {
    const subCategories = this.identifySubCategories(elements);
    if (subCategories.length <= 1) return 30;
    if (subCategories.length > elements.length / 2) return 50;
    return 100;
  }
  identifySubCategories(elements) {
    const subCategories = /* @__PURE__ */ new Set();
    for (const element of elements) {
      const subCategoryKey = `${element.size}_${element.pattern}`;
      subCategories.add(subCategoryKey);
    }
    return Array.from(subCategories);
  }
  assessSpatialOrganization(elements) {
    if (!elements[0]?.position) return 50;
    const positions = elements.map((element) => element.position).filter((pos) => pos);
    if (positions.length < 2) return 50;
    const spatialClusters = this.identifySpatialClusters(positions);
    const clusteringQuality = this.evaluateClusteringQuality(spatialClusters, positions.length);
    return clusteringQuality;
  }
  identifySpatialClusters(positions) {
    const clusters = [];
    const visited = /* @__PURE__ */ new Set();
    for (let i = 0; i < positions.length; i++) {
      if (visited.has(i)) continue;
      const cluster = [i];
      visited.add(i);
      for (let j = i + 1; j < positions.length; j++) {
        if (visited.has(j)) continue;
        const distance = Math.sqrt(
          Math.pow(positions[j].x - positions[i].x, 2) + Math.pow(positions[j].y - positions[i].y, 2)
        );
        if (distance < 100) {
          cluster.push(j);
          visited.add(j);
        }
      }
      clusters.push(cluster);
    }
    return clusters;
  }
  evaluateClusteringQuality(clusters, totalPositions) {
    if (clusters.length === 0) return 0;
    if (clusters.length === totalPositions) return 30;
    const clusterSizes = clusters.map((cluster) => cluster.length);
    const meanSize = clusterSizes.reduce((sum, size) => sum + size, 0) / clusterSizes.length;
    if (meanSize >= 2 && meanSize <= 5) {
      return 100;
    } else {
      return 60;
    }
  }
  assessFeatureDetection(elements, playerClassification, correctClassification) {
    if (!elements.length) return 50;
    const relevantFeatures = this.identifyRelevantFeatures(correctClassification);
    const detectedFeatures = this.identifyDetectedFeatures(playerClassification);
    const featureDetectionScore = this.calculateFeatureDetectionAccuracy(relevantFeatures, detectedFeatures);
    return featureDetectionScore * 100;
  }
  identifyRelevantFeatures(correctClassification) {
    const categories = this.extractCategories(correctClassification);
    const relevantFeatures = /* @__PURE__ */ new Set();
    for (const category of categories) {
      const elementsInCategory = this.getElementsInCategory(correctClassification, category);
      const commonFeatures = this.extractCommonFeatures(elementsInCategory);
      commonFeatures.forEach((feature) => relevantFeatures.add(feature));
    }
    return Array.from(relevantFeatures);
  }
  identifyDetectedFeatures(playerClassification) {
    return this.identifyImpliedCriteria(playerClassification);
  }
  calculateFeatureDetectionAccuracy(relevant, detected) {
    if (relevant.length === 0 && detected.length === 0) return 1;
    if (relevant.length === 0) return 0.5;
    const intersection = detected.filter((feature) => relevant.includes(feature));
    return intersection.length / relevant.length;
  }
  assessCategoricalThinking(playerClassification) {
    const categories = this.extractCategories(playerClassification);
    const totalElements = Object.keys(playerClassification).length;
    if (totalElements === 0) return 0;
    const categoricalMetrics = {
      categoryUtilization: this.assessCategoryUtilization(categories, totalElements),
      categoryDistinctiveness: this.assessCategoryDistinctiveness(categories, playerClassification),
      conceptualClarity: this.assessConceptualClarity(playerClassification)
    };
    return categoricalMetrics.categoryUtilization * 0.4 + categoricalMetrics.categoryDistinctiveness * 0.3 + categoricalMetrics.conceptualClarity * 0.3;
  }
  assessCategoryUtilization(categories, totalElements) {
    const idealCategoryCount = Math.max(2, Math.min(totalElements / 3, 6));
    const actualCategoryCount = categories.length;
    const utilizationScore = 1 - Math.abs(actualCategoryCount - idealCategoryCount) / idealCategoryCount;
    return Math.max(0, utilizationScore * 100);
  }
  assessCategoryDistinctiveness(categories, classification) {
    if (categories.length < 2) return 100;
    let distinctivenessScore = 0;
    for (let i = 0; i < categories.length - 1; i++) {
      for (let j = i + 1; j < categories.length; j++) {
        const category1Elements = this.getElementsInCategory(classification, categories[i]);
        const category2Elements = this.getElementsInCategory(classification, categories[j]);
        const distinctiveness = this.calculateCategoryDistinctiveness(category1Elements, category2Elements);
        distinctivenessScore += distinctiveness;
      }
    }
    const comparisons = categories.length * (categories.length - 1) / 2;
    return comparisons > 0 ? distinctivenessScore / comparisons * 100 : 100;
  }
  calculateCategoryDistinctiveness(elements1, elements2) {
    if (elements1.length === 0 || elements2.length === 0) return 1;
    let distinctivenessSum = 0;
    let comparisons = 0;
    for (const element1 of elements1) {
      for (const element2 of elements2) {
        const similarity = this.calculateVisualSimilarity(element1, element2);
        distinctivenessSum += 1 - similarity;
        comparisons++;
      }
    }
    return comparisons > 0 ? distinctivenessSum / comparisons : 1;
  }
  assessConceptualClarity(classification) {
    const categories = this.extractCategories(classification);
    let clarityScore = 0;
    for (const category of categories) {
      const elements = this.getElementsInCategory(classification, category);
      const categoryClarity = this.calculateCategoryClarity(elements);
      clarityScore += categoryClarity;
    }
    return categories.length > 0 ? clarityScore / categories.length * 100 : 0;
  }
  calculateCategoryClarity(elements) {
    if (elements.length === 0) return 0;
    if (elements.length === 1) return 100;
    const featureConsistency = this.calculateFeatureConsistency(elements);
    return featureConsistency;
  }
  evaluateRuleApplication(classificationHistory) {
    if (classificationHistory.length < 3) return 100;
    const ruleApplications = this.extractRuleApplications(classificationHistory);
    const consistency = this.calculateRuleConsistency(ruleApplications);
    return consistency * 100;
  }
  extractRuleApplications(history) {
    const applications = [];
    for (const entry of history) {
      if (entry.rule && entry.elementId && entry.category) {
        applications.push({
          rule: entry.rule,
          element: entry.elementId,
          category: entry.category,
          timestamp: entry.timestamp
        });
      }
    }
    return applications;
  }
  calculateRuleConsistency(applications) {
    if (applications.length < 2) return 1;
    const ruleGroups = {};
    for (const application of applications) {
      const rule = application.rule;
      if (!ruleGroups[rule]) {
        ruleGroups[rule] = [];
      }
      ruleGroups[rule].push(application);
    }
    let totalConsistency = 0;
    let ruleCount = 0;
    for (const [rule, ruleApplications] of Object.entries(ruleGroups)) {
      const ruleConsistency = this.calculateSingleRuleConsistency(ruleApplications);
      totalConsistency += ruleConsistency;
      ruleCount++;
    }
    return ruleCount > 0 ? totalConsistency / ruleCount : 1;
  }
  calculateSingleRuleConsistency(ruleApplications) {
    if (ruleApplications.length < 2) return 1;
    const categories = ruleApplications.map((app) => app.category);
    const uniqueCategories = new Set(categories);
    return uniqueCategories.size === 1 ? 1 : 0.3;
  }
  assessResponseInhibition(gameState) {
    const responseTime = gameState.responseTime || 0;
    const corrections = gameState.corrections || 0;
    const totalResponses = gameState.totalResponses || 1;
    const timeScore = this.evaluateResponseTime(responseTime);
    const correctionScore = Math.max(0, 100 - corrections / totalResponses * 50);
    return (timeScore + correctionScore) / 2;
  }
  evaluateResponseTime(responseTime) {
    const optimalRange = { min: 2e3, max: 1e4 };
    if (responseTime < optimalRange.min) {
      return 60;
    } else if (responseTime > optimalRange.max) {
      return Math.max(40, 100 - (responseTime - optimalRange.max) / 1e3 * 3);
    } else {
      return 100;
    }
  }
}
class PatternTransformationCollector {
  constructor() {
    this.name = "PatternTransformationCollector";
    this.version = "3.0.0";
    this.description = "Analisa habilidades de transformação e manipulação de padrões visuais";
  }
  async collect(gameState) {
    try {
      const originalPattern = gameState.originalPattern || [];
      const transformationRules = gameState.transformationRules || [];
      const playerTransformation = gameState.playerTransformation || [];
      const correctTransformation = gameState.correctTransformation || [];
      return {
        // Métricas de transformação
        transformationAccuracy: this.calculateTransformationAccuracy(correctTransformation, playerTransformation),
        ruleApplicationAccuracy: this.assessRuleApplication(transformationRules, originalPattern, playerTransformation),
        transformationComplexity: this.evaluateTransformationComplexity(transformationRules),
        spatialTransformationScore: this.assessSpatialTransformation(originalPattern, playerTransformation),
        // Análise cognitiva
        mentalRotationAbility: this.assessMentalRotation(gameState),
        visualSpatialProcessing: this.evaluateVisualSpatialProcessing(originalPattern, playerTransformation),
        abstractReasoningIndex: this.assessAbstractReasoning(transformationRules, gameState.difficulty),
        cognitiveFlexibilityScore: this.evaluateCognitiveFlexibility(gameState.sessionAttempts || []),
        // Processamento executivo
        ruleRetentionScore: this.assessRuleRetention(transformationRules, gameState.transformationHistory || []),
        workingMemoryCapacity: this.evaluateWorkingMemoryCapacity(originalPattern, transformationRules),
        inhibitionOfIncorrectTransforms: this.assessTransformationInhibition(gameState),
        // Habilidades visuoespaciais
        spatialVisualizationIndex: this.assessSpatialVisualization(originalPattern, playerTransformation),
        geometricTransformationAccuracy: this.evaluateGeometricTransformations(gameState),
        patternManipulationSkill: this.assessPatternManipulation(originalPattern, playerTransformation, transformationRules),
        timestamp: Date.now(),
        sessionId: gameState.sessionId || "unknown"
      };
    } catch (error) {
      console.error("Erro no PatternTransformationCollector:", error);
      return null;
    }
  }
  calculateTransformationAccuracy(correct2, player) {
    if (!correct2.length || !player.length) return 0;
    let accurateTransformations = 0;
    Math.max(correct2.length, player.length);
    for (let i = 0; i < Math.min(correct2.length, player.length); i++) {
      if (this.transformationsMatch(correct2[i], player[i])) {
        accurateTransformations++;
      }
    }
    const lengthPenalty = Math.abs(correct2.length - player.length) * 5;
    const baseAccuracy = accurateTransformations / correct2.length * 100;
    return Math.max(0, baseAccuracy - lengthPenalty);
  }
  transformationsMatch(transform1, transform2) {
    if (!transform1 || !transform2) return false;
    const properties = ["shape", "color", "size", "orientation", "position"];
    for (const prop of properties) {
      if (transform1[prop] !== void 0 && transform2[prop] !== void 0) {
        if (!this.propertyValuesMatch(transform1[prop], transform2[prop], prop)) {
          return false;
        }
      }
    }
    return true;
  }
  propertyValuesMatch(value1, value2, property) {
    if (property === "position") {
      return Math.abs(value1.x - value2.x) < 10 && Math.abs(value1.y - value2.y) < 10;
    } else if (property === "orientation") {
      const normalizedValue1 = (value1 % 360 + 360) % 360;
      const normalizedValue2 = (value2 % 360 + 360) % 360;
      return Math.abs(normalizedValue1 - normalizedValue2) < 15;
    } else {
      return value1 === value2;
    }
  }
  assessRuleApplication(rules, originalPattern, playerTransformation) {
    if (!rules.length || !originalPattern.length || !playerTransformation.length) return 0;
    let correctApplications = 0;
    for (let i = 0; i < Math.min(originalPattern.length, playerTransformation.length); i++) {
      const originalElement = originalPattern[i];
      const transformedElement = playerTransformation[i];
      const expectedTransformation = this.applyTransformationRules(originalElement, rules);
      if (this.transformationsMatch(expectedTransformation, transformedElement)) {
        correctApplications++;
      }
    }
    return correctApplications / originalPattern.length * 100;
  }
  applyTransformationRules(element, rules) {
    let transformedElement = { ...element };
    for (const rule of rules) {
      transformedElement = this.applySingleRule(transformedElement, rule);
    }
    return transformedElement;
  }
  applySingleRule(element, rule) {
    const transformed = { ...element };
    switch (rule.type) {
      case "rotate":
        transformed.orientation = (transformed.orientation || 0) + rule.degrees;
        break;
      case "scale":
        transformed.size = this.scaleSize(transformed.size, rule.factor);
        break;
      case "translate":
        transformed.position = {
          x: (transformed.position?.x || 0) + rule.deltaX,
          y: (transformed.position?.y || 0) + rule.deltaY
        };
        break;
      case "changeColor":
        transformed.color = rule.newColor;
        break;
      case "changeShape":
        transformed.shape = rule.newShape;
        break;
      case "flip":
        transformed.flipped = !transformed.flipped;
        if (rule.axis === "horizontal") {
          transformed.position.y = -transformed.position.y;
        } else if (rule.axis === "vertical") {
          transformed.position.x = -transformed.position.x;
        }
        break;
    }
    return transformed;
  }
  scaleSize(currentSize, factor) {
    const sizeMap = { small: 1, medium: 2, large: 3 };
    const reverseSizeMap = { 1: "small", 2: "medium", 3: "large" };
    const numericSize = sizeMap[currentSize] || 2;
    const scaledSize = Math.max(1, Math.min(3, Math.round(numericSize * factor)));
    return reverseSizeMap[scaledSize] || currentSize;
  }
  evaluateTransformationComplexity(rules) {
    if (!rules.length) return 0;
    const complexityScores = {
      translate: 1,
      scale: 2,
      rotate: 3,
      changeColor: 2,
      changeShape: 3,
      flip: 4,
      composite: 5
    };
    const totalComplexity = rules.reduce((sum, rule) => {
      return sum + (complexityScores[rule.type] || 1);
    }, 0);
    return Math.min(100, totalComplexity / rules.length * 20);
  }
  assessSpatialTransformation(originalPattern, transformedPattern) {
    if (!originalPattern.length || !transformedPattern.length) return 50;
    const spatialMetrics = {
      positionAccuracy: this.assessPositionTransformation(originalPattern, transformedPattern),
      orientationAccuracy: this.assessOrientationTransformation(originalPattern, transformedPattern),
      scaleAccuracy: this.assessScaleTransformation(originalPattern, transformedPattern),
      spatialRelationshipPreservation: this.assessSpatialRelationships(originalPattern, transformedPattern)
    };
    return spatialMetrics.positionAccuracy * 0.3 + spatialMetrics.orientationAccuracy * 0.3 + spatialMetrics.scaleAccuracy * 0.2 + spatialMetrics.spatialRelationshipPreservation * 0.2;
  }
  assessPositionTransformation(original, transformed) {
    let positionAccuracy = 0;
    for (let i = 0; i < Math.min(original.length, transformed.length); i++) {
      const originalPos = original[i].position;
      const transformedPos = transformed[i].position;
      if (originalPos && transformedPos) {
        const distance = Math.sqrt(
          Math.pow(transformedPos.x - originalPos.x, 2) + Math.pow(transformedPos.y - originalPos.y, 2)
        );
        const accuracy = Math.max(0, 100 - distance / 5);
        positionAccuracy += accuracy;
      } else {
        positionAccuracy += 50;
      }
    }
    return original.length > 0 ? positionAccuracy / original.length : 0;
  }
  assessOrientationTransformation(original, transformed) {
    let orientationAccuracy = 0;
    for (let i = 0; i < Math.min(original.length, transformed.length); i++) {
      const originalOrientation = original[i].orientation || 0;
      const transformedOrientation = transformed[i].orientation || 0;
      const angleDifference = Math.abs(transformedOrientation - originalOrientation);
      const normalizedDifference = Math.min(angleDifference, 360 - angleDifference);
      const accuracy = Math.max(0, 100 - normalizedDifference / 1.8);
      orientationAccuracy += accuracy;
    }
    return original.length > 0 ? orientationAccuracy / original.length : 100;
  }
  assessScaleTransformation(original, transformed) {
    let scaleAccuracy = 0;
    const sizeValues = { small: 1, medium: 2, large: 3 };
    for (let i = 0; i < Math.min(original.length, transformed.length); i++) {
      const originalSize = sizeValues[original[i].size] || 2;
      const transformedSize = sizeValues[transformed[i].size] || 2;
      const scaleDifference = Math.abs(transformedSize - originalSize);
      const accuracy = Math.max(0, 100 - scaleDifference * 33.33);
      scaleAccuracy += accuracy;
    }
    return original.length > 0 ? scaleAccuracy / original.length : 100;
  }
  assessSpatialRelationships(original, transformed) {
    if (original.length < 2 || transformed.length < 2) return 100;
    let relationshipPreservation = 0;
    let totalRelationships = 0;
    for (let i = 0; i < original.length - 1; i++) {
      for (let j = i + 1; j < original.length; j++) {
        if (i < transformed.length && j < transformed.length) {
          const originalRelation = this.calculateSpatialRelationship(original[i], original[j]);
          const transformedRelation = this.calculateSpatialRelationship(transformed[i], transformed[j]);
          const relationshipMatch = this.compareSpatialRelationships(originalRelation, transformedRelation);
          relationshipPreservation += relationshipMatch;
          totalRelationships++;
        }
      }
    }
    return totalRelationships > 0 ? relationshipPreservation / totalRelationships * 100 : 100;
  }
  calculateSpatialRelationship(element1, element2) {
    if (!element1.position || !element2.position) {
      return { distance: 0, angle: 0 };
    }
    const deltaX = element2.position.x - element1.position.x;
    const deltaY = element2.position.y - element1.position.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    const angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI);
    return { distance, angle };
  }
  compareSpatialRelationships(relation1, relation2) {
    const distanceMatch = 1 - Math.abs(relation1.distance - relation2.distance) / Math.max(relation1.distance, relation2.distance, 1);
    const angleMatch = 1 - Math.abs(relation1.angle - relation2.angle) / 180;
    return Math.max(0, (distanceMatch + angleMatch) / 2);
  }
  assessMentalRotation(gameState) {
    const rotationTasks = this.extractRotationTasks(gameState);
    if (!rotationTasks.length) return 50;
    let rotationScore = 0;
    for (const task of rotationTasks) {
      const taskScore = this.evaluateRotationTask(task);
      rotationScore += taskScore;
    }
    return rotationScore / rotationTasks.length;
  }
  extractRotationTasks(gameState) {
    const transformationRules = gameState.transformationRules || [];
    const rotationTasks = [];
    for (const rule of transformationRules) {
      if (rule.type === "rotate") {
        rotationTasks.push({
          degrees: rule.degrees,
          accuracy: this.calculateRotationAccuracy(rule, gameState),
          responseTime: gameState.responseTime || 0
        });
      }
    }
    return rotationTasks;
  }
  calculateRotationAccuracy(rotationRule, gameState) {
    const expectedDegrees = rotationRule.degrees;
    const playerTransformation = gameState.playerTransformation || [];
    const originalPattern = gameState.originalPattern || [];
    if (!playerTransformation.length || !originalPattern.length) return 0;
    let rotationAccuracy = 0;
    for (let i = 0; i < Math.min(playerTransformation.length, originalPattern.length); i++) {
      const originalOrientation = originalPattern[i].orientation || 0;
      const playerOrientation = playerTransformation[i].orientation || 0;
      const expectedOrientation = originalOrientation + expectedDegrees;
      const error = Math.abs(playerOrientation - expectedOrientation);
      const normalizedError = Math.min(error, 360 - error);
      rotationAccuracy += Math.max(0, 100 - normalizedError / 1.8);
    }
    return originalPattern.length > 0 ? rotationAccuracy / originalPattern.length : 0;
  }
  evaluateRotationTask(task) {
    const accuracyScore = task.accuracy;
    const speedScore = this.evaluateRotationSpeed(task.degrees, task.responseTime);
    return accuracyScore * 0.7 + speedScore * 0.3;
  }
  evaluateRotationSpeed(degrees, responseTime) {
    const expectedTime = Math.abs(degrees) * 20 + 2e3;
    const speedRatio = expectedTime / Math.max(responseTime, 1e3);
    return Math.min(100, speedRatio * 100);
  }
  evaluateVisualSpatialProcessing(originalPattern, transformedPattern) {
    if (!originalPattern.length || !transformedPattern.length) return 50;
    const processingMetrics = {
      spatialMemoryAccuracy: this.assessSpatialMemory(originalPattern, transformedPattern),
      visualAttentionScore: this.assessVisualAttention(originalPattern, transformedPattern),
      spatialWorkingMemory: this.assessSpatialWorkingMemory(originalPattern),
      visualProcessingSpeed: this.assessVisualProcessingSpeed(originalPattern, transformedPattern)
    };
    return processingMetrics.spatialMemoryAccuracy * 0.3 + processingMetrics.visualAttentionScore * 0.2 + processingMetrics.spatialWorkingMemory * 0.3 + processingMetrics.visualProcessingSpeed * 0.2;
  }
  assessSpatialMemory(original, transformed) {
    let memoryScore = 0;
    for (let i = 0; i < Math.min(original.length, transformed.length); i++) {
      const originalElement = original[i];
      const transformedElement = transformed[i];
      if (originalElement.position && transformedElement.position) {
        const spatialConsistency = this.checkSpatialConsistency(originalElement, transformedElement, original, transformed, i);
        memoryScore += spatialConsistency;
      } else {
        memoryScore += 50;
      }
    }
    return original.length > 0 ? memoryScore / original.length : 50;
  }
  checkSpatialConsistency(element1, element2, pattern1, pattern2, index) {
    let consistencyScore = 0;
    let validComparisons = 0;
    for (let i = 0; i < pattern1.length; i++) {
      if (i !== index && i < pattern2.length) {
        const originalRelation = this.calculateSpatialRelationship(element1, pattern1[i]);
        const transformedRelation = this.calculateSpatialRelationship(element2, pattern2[i]);
        const relationMatch = this.compareSpatialRelationships(originalRelation, transformedRelation);
        consistencyScore += relationMatch;
        validComparisons++;
      }
    }
    return validComparisons > 0 ? consistencyScore / validComparisons * 100 : 100;
  }
  assessVisualAttention(original, transformed) {
    let attentionScore = 0;
    for (let i = 0; i < Math.min(original.length, transformed.length); i++) {
      const originalElement = original[i];
      const transformedElement = transformed[i];
      const detailPreservation = this.calculateDetailPreservation(originalElement, transformedElement);
      attentionScore += detailPreservation;
    }
    return original.length > 0 ? attentionScore / original.length : 100;
  }
  calculateDetailPreservation(original, transformed) {
    const preservableFeatures = ["shape", "color"];
    let preservedFeatures = 0;
    for (const feature of preservableFeatures) {
      if (original[feature] === transformed[feature]) {
        preservedFeatures++;
      }
    }
    return preservedFeatures / preservableFeatures.length * 100;
  }
  assessSpatialWorkingMemory(pattern) {
    const patternComplexity = this.calculatePatternComplexity(pattern);
    const memoryLoad = Math.min(100, patternComplexity * 10);
    return memoryLoad;
  }
  calculatePatternComplexity(pattern) {
    if (!pattern.length) return 0;
    const complexityFactors = {
      elementCount: Math.min(pattern.length / 5, 1),
      // Normalizado para 5 elementos
      uniqueShapes: new Set(pattern.map((e) => e.shape)).size,
      uniqueColors: new Set(pattern.map((e) => e.color)).size,
      spatialDistribution: this.calculateSpatialDistribution(pattern)
    };
    return (complexityFactors.elementCount * 3 + complexityFactors.uniqueShapes * 2 + complexityFactors.uniqueColors * 2 + complexityFactors.spatialDistribution * 3) / 10;
  }
  calculateSpatialDistribution(pattern) {
    const positions = pattern.map((e) => e.position).filter((pos) => pos);
    if (positions.length < 2) return 0.5;
    let totalDistance = 0;
    let distanceCount = 0;
    for (let i = 0; i < positions.length - 1; i++) {
      for (let j = i + 1; j < positions.length; j++) {
        const distance = Math.sqrt(
          Math.pow(positions[j].x - positions[i].x, 2) + Math.pow(positions[j].y - positions[i].y, 2)
        );
        totalDistance += distance;
        distanceCount++;
      }
    }
    const averageDistance = totalDistance / distanceCount;
    return Math.min(1, averageDistance / 200);
  }
  assessVisualProcessingSpeed(original, transformed) {
    const complexity = this.calculatePatternComplexity(original);
    const expectedProcessingTime = complexity * 3e3;
    const actualTime = transformed.processingTime || expectedProcessingTime;
    const speedRatio = expectedProcessingTime / Math.max(actualTime, 1e3);
    return Math.min(100, speedRatio * 100);
  }
  assessAbstractReasoning(transformationRules, difficulty) {
    if (!transformationRules.length) return 50;
    const reasoningMetrics = {
      ruleComplexity: this.evaluateRuleComplexity(transformationRules),
      abstractionLevel: this.evaluateAbstractionLevel(transformationRules),
      logicalConsistency: this.assessLogicalConsistency(transformationRules)
    };
    const difficultyMultipliers = {
      easy: 0.7,
      medium: 1,
      hard: 1.3
    };
    const baseScore = reasoningMetrics.ruleComplexity * 0.4 + reasoningMetrics.abstractionLevel * 0.4 + reasoningMetrics.logicalConsistency * 0.2;
    return Math.min(100, baseScore * (difficultyMultipliers[difficulty] || 1));
  }
  evaluateRuleComplexity(rules) {
    const complexityScores = {
      translate: 1,
      scale: 2,
      rotate: 3,
      changeColor: 2,
      changeShape: 3,
      flip: 4,
      conditional: 5,
      composite: 6
    };
    const totalComplexity = rules.reduce((sum, rule) => {
      return sum + (complexityScores[rule.type] || 1);
    }, 0);
    return Math.min(100, totalComplexity / rules.length * 15);
  }
  evaluateAbstractionLevel(rules) {
    const abstractionLevels = {
      translate: 1,
      // Concreto
      scale: 2,
      rotate: 2,
      changeColor: 1,
      changeShape: 3,
      // Mais abstrato
      flip: 3,
      conditional: 4,
      // Muito abstrato
      composite: 5
      // Altamente abstrato
    };
    const totalAbstraction = rules.reduce((sum, rule) => {
      return sum + (abstractionLevels[rule.type] || 1);
    }, 0);
    return Math.min(100, totalAbstraction / rules.length * 20);
  }
  assessLogicalConsistency(rules) {
    if (rules.length < 2) return 100;
    const ruleTypes = rules.map((rule) => rule.type);
    new Set(ruleTypes);
    const contradictions = this.findRuleContradictions(rules);
    const consistencyScore = Math.max(0, 100 - contradictions.length * 20);
    return consistencyScore;
  }
  findRuleContradictions(rules) {
    const contradictions = [];
    for (let i = 0; i < rules.length - 1; i++) {
      for (let j = i + 1; j < rules.length; j++) {
        if (this.rulesContradict(rules[i], rules[j])) {
          contradictions.push({ rule1: rules[i], rule2: rules[j] });
        }
      }
    }
    return contradictions;
  }
  rulesContradict(rule1, rule2) {
    if (rule1.type === "rotate" && rule2.type === "rotate") {
      return false;
    }
    if (rule1.type === "changeColor" && rule2.type === "changeColor") {
      return rule1.newColor !== rule2.newColor;
    }
    if (rule1.type === "changeShape" && rule2.type === "changeShape") {
      return rule1.newShape !== rule2.newShape;
    }
    return false;
  }
  evaluateCognitiveFlexibility(sessionAttempts) {
    if (sessionAttempts.length < 3) return 50;
    const flexibilityMetrics = {
      strategyVariation: this.assessStrategyVariation(sessionAttempts),
      adaptationSpeed: this.assessAdaptationSpeed(sessionAttempts),
      errorRecovery: this.assessErrorRecovery(sessionAttempts)
    };
    return flexibilityMetrics.strategyVariation * 0.4 + flexibilityMetrics.adaptationSpeed * 0.3 + flexibilityMetrics.errorRecovery * 0.3;
  }
  assessStrategyVariation(attempts) {
    const strategies = attempts.map((attempt) => attempt.strategy || "unknown");
    const uniqueStrategies = new Set(strategies);
    const variationScore = uniqueStrategies.size / attempts.length * 100;
    return Math.min(100, variationScore);
  }
  assessAdaptationSpeed(attempts) {
    let adaptationScore = 0;
    for (let i = 1; i < attempts.length; i++) {
      const currentAccuracy = attempts[i].accuracy || 0;
      const previousAccuracy = attempts[i - 1].accuracy || 0;
      if (currentAccuracy > previousAccuracy) {
        adaptationScore += 20;
      }
    }
    return Math.min(100, adaptationScore);
  }
  assessErrorRecovery(attempts) {
    let recoveryScore = 0;
    let recoveryOpportunities = 0;
    for (let i = 2; i < attempts.length; i++) {
      const twoAgo = attempts[i - 2].accuracy || 0;
      const oneAgo = attempts[i - 1].accuracy || 0;
      const current = attempts[i].accuracy || 0;
      if (oneAgo < twoAgo) {
        recoveryOpportunities++;
        if (current > oneAgo) {
          recoveryScore++;
        }
      }
    }
    return recoveryOpportunities > 0 ? recoveryScore / recoveryOpportunities * 100 : 100;
  }
  assessRuleRetention(transformationRules, transformationHistory) {
    if (!transformationRules.length || !transformationHistory.length) return 50;
    let retentionScore = 0;
    for (const rule of transformationRules) {
      const ruleApplications = transformationHistory.filter((entry) => entry.rule === rule.type);
      const consistentApplications = ruleApplications.filter((app) => app.correct).length;
      const ruleRetention = ruleApplications.length > 0 ? consistentApplications / ruleApplications.length * 100 : 50;
      retentionScore += ruleRetention;
    }
    return retentionScore / transformationRules.length;
  }
  evaluateWorkingMemoryCapacity(originalPattern, transformationRules) {
    const patternComplexity = this.calculatePatternComplexity(originalPattern);
    const ruleComplexity = this.evaluateRuleComplexity(transformationRules);
    const totalComplexity = patternComplexity + ruleComplexity;
    const memoryLoad = Math.min(100, totalComplexity * 2);
    return memoryLoad;
  }
  assessTransformationInhibition(gameState) {
    const responseTime = gameState.responseTime || 0;
    const corrections = gameState.corrections || 0;
    const attempts = gameState.attempts || 1;
    const timeScore = this.evaluateDeliberationTime(responseTime);
    const correctionScore = Math.max(0, 100 - corrections / attempts * 50);
    return (timeScore + correctionScore) / 2;
  }
  evaluateDeliberationTime(responseTime) {
    const optimalRange = { min: 3e3, max: 15e3 };
    if (responseTime < optimalRange.min) {
      return 60;
    } else if (responseTime > optimalRange.max) {
      return Math.max(40, 100 - (responseTime - optimalRange.max) / 1e3 * 2);
    } else {
      return 100;
    }
  }
  assessSpatialVisualization(originalPattern, transformedPattern) {
    if (!originalPattern.length || !transformedPattern.length) return 50;
    const visualizationMetrics = {
      spatialAccuracy: this.assessSpatialTransformation(originalPattern, transformedPattern),
      visualMemoryPreservation: this.assessVisualMemoryPreservation(originalPattern, transformedPattern),
      spatialRelationshipUnderstanding: this.assessSpatialRelationships(originalPattern, transformedPattern)
    };
    return visualizationMetrics.spatialAccuracy * 0.4 + visualizationMetrics.visualMemoryPreservation * 0.3 + visualizationMetrics.spatialRelationshipUnderstanding * 0.3;
  }
  assessVisualMemoryPreservation(original, transformed) {
    let preservationScore = 0;
    for (let i = 0; i < Math.min(original.length, transformed.length); i++) {
      const originalElement = original[i];
      const transformedElement = transformed[i];
      const preservableFeatures = ["shape", "color", "pattern"];
      let preservedCount = 0;
      for (const feature of preservableFeatures) {
        if (originalElement[feature] === transformedElement[feature]) {
          preservedCount++;
        }
      }
      preservationScore += preservedCount / preservableFeatures.length * 100;
    }
    return original.length > 0 ? preservationScore / original.length : 100;
  }
  evaluateGeometricTransformations(gameState) {
    const transformationRules = gameState.transformationRules || [];
    const geometricRules = transformationRules.filter(
      (rule) => ["rotate", "scale", "flip", "translate"].includes(rule.type)
    );
    if (!geometricRules.length) return 50;
    let geometricAccuracy = 0;
    for (const rule of geometricRules) {
      const ruleAccuracy = this.evaluateGeometricRule(rule, gameState);
      geometricAccuracy += ruleAccuracy;
    }
    return geometricAccuracy / geometricRules.length;
  }
  evaluateGeometricRule(rule, gameState) {
    switch (rule.type) {
      case "rotate":
        return this.calculateRotationAccuracy(rule, gameState);
      case "scale":
        return this.calculateScaleAccuracy(rule, gameState);
      case "flip":
        return this.calculateFlipAccuracy(rule, gameState);
      case "translate":
        return this.calculateTranslationAccuracy(rule, gameState);
      default:
        return 50;
    }
  }
  calculateScaleAccuracy(scaleRule, gameState) {
    const originalPattern = gameState.originalPattern || [];
    const playerTransformation = gameState.playerTransformation || [];
    if (!originalPattern.length || !playerTransformation.length) return 0;
    let scaleAccuracy = 0;
    const expectedFactor = scaleRule.factor;
    for (let i = 0; i < Math.min(originalPattern.length, playerTransformation.length); i++) {
      const originalSize = this.getSizeValue(originalPattern[i].size);
      const transformedSize = this.getSizeValue(playerTransformation[i].size);
      const actualFactor = transformedSize / originalSize;
      const factorError = Math.abs(actualFactor - expectedFactor);
      const accuracy = Math.max(0, 100 - factorError * 50);
      scaleAccuracy += accuracy;
    }
    return originalPattern.length > 0 ? scaleAccuracy / originalPattern.length : 0;
  }
  getSizeValue(size) {
    const sizeValues = { small: 1, medium: 2, large: 3 };
    return sizeValues[size] || 2;
  }
  calculateFlipAccuracy(flipRule, gameState) {
    const originalPattern = gameState.originalPattern || [];
    const playerTransformation = gameState.playerTransformation || [];
    if (!originalPattern.length || !playerTransformation.length) return 0;
    let flipAccuracy = 0;
    for (let i = 0; i < Math.min(originalPattern.length, playerTransformation.length); i++) {
      const originalPos = originalPattern[i].position;
      const transformedPos = playerTransformation[i].position;
      if (originalPos && transformedPos) {
        const flipCorrect = this.checkFlipCorrectness(originalPos, transformedPos, flipRule.axis);
        flipAccuracy += flipCorrect ? 100 : 0;
      } else {
        flipAccuracy += 50;
      }
    }
    return originalPattern.length > 0 ? flipAccuracy / originalPattern.length : 0;
  }
  checkFlipCorrectness(originalPos, transformedPos, axis) {
    if (axis === "horizontal") {
      return Math.abs(transformedPos.y + originalPos.y) < 20;
    } else if (axis === "vertical") {
      return Math.abs(transformedPos.x + originalPos.x) < 20;
    }
    return false;
  }
  calculateTranslationAccuracy(translateRule, gameState) {
    const originalPattern = gameState.originalPattern || [];
    const playerTransformation = gameState.playerTransformation || [];
    if (!originalPattern.length || !playerTransformation.length) return 0;
    let translationAccuracy = 0;
    const expectedDeltaX = translateRule.deltaX || 0;
    const expectedDeltaY = translateRule.deltaY || 0;
    for (let i = 0; i < Math.min(originalPattern.length, playerTransformation.length); i++) {
      const originalPos = originalPattern[i].position;
      const transformedPos = playerTransformation[i].position;
      if (originalPos && transformedPos) {
        const actualDeltaX = transformedPos.x - originalPos.x;
        const actualDeltaY = transformedPos.y - originalPos.y;
        const errorX = Math.abs(actualDeltaX - expectedDeltaX);
        const errorY = Math.abs(actualDeltaY - expectedDeltaY);
        const totalError = Math.sqrt(errorX * errorX + errorY * errorY);
        const accuracy = Math.max(0, 100 - totalError / 5);
        translationAccuracy += accuracy;
      } else {
        translationAccuracy += 50;
      }
    }
    return originalPattern.length > 0 ? translationAccuracy / originalPattern.length : 0;
  }
  assessPatternManipulation(originalPattern, transformedPattern, transformationRules) {
    if (!originalPattern.length || !transformedPattern.length || !transformationRules.length) return 50;
    const manipulationMetrics = {
      transformationAccuracy: this.calculateTransformationAccuracy(
        this.applyAllRules(originalPattern, transformationRules),
        transformedPattern
      ),
      manipulationEfficiency: this.assessManipulationEfficiency(originalPattern, transformedPattern, transformationRules),
      patternIntegrity: this.assessPatternIntegrity(originalPattern, transformedPattern),
      manipulationStrategy: this.assessManipulationStrategy(transformationRules)
    };
    return manipulationMetrics.transformationAccuracy * 0.4 + manipulationMetrics.manipulationEfficiency * 0.2 + manipulationMetrics.patternIntegrity * 0.2 + manipulationMetrics.manipulationStrategy * 0.2;
  }
  applyAllRules(pattern, rules) {
    let transformedPattern = pattern.map((element) => ({ ...element }));
    for (const rule of rules) {
      transformedPattern = transformedPattern.map((element) => this.applySingleRule(element, rule));
    }
    return transformedPattern;
  }
  assessManipulationEfficiency(original, transformed, rules) {
    const necessarySteps = rules.length;
    const actualComplexity = this.calculateManipulationComplexity(original, transformed);
    const efficiencyRatio = necessarySteps / Math.max(actualComplexity, 1);
    return Math.min(100, efficiencyRatio * 100);
  }
  calculateManipulationComplexity(original, transformed) {
    let complexity = 0;
    for (let i = 0; i < Math.min(original.length, transformed.length); i++) {
      const changes = this.countElementChanges(original[i], transformed[i]);
      complexity += changes;
    }
    return complexity;
  }
  countElementChanges(originalElement, transformedElement) {
    const properties = ["shape", "color", "size", "orientation", "position"];
    let changes = 0;
    for (const prop of properties) {
      if (!this.propertyValuesMatch(originalElement[prop], transformedElement[prop], prop)) {
        changes++;
      }
    }
    return changes;
  }
  assessPatternIntegrity(original, transformed) {
    const structuralIntegrity = this.assessStructuralIntegrity(original, transformed);
    const visualCoherence = this.assessVisualCoherence(transformed);
    return (structuralIntegrity + visualCoherence) / 2;
  }
  assessStructuralIntegrity(original, transformed) {
    if (original.length !== transformed.length) {
      return 50;
    }
    const relationshipPreservation = this.assessSpatialRelationships(original, transformed);
    return relationshipPreservation;
  }
  assessVisualCoherence(pattern) {
    if (pattern.length < 2) return 100;
    const coherenceMetrics = {
      colorHarmony: this.assessColorHarmony(pattern),
      sizeConsistency: this.assessSizeConsistency(pattern),
      spatialBalance: this.assessSpatialBalance(pattern)
    };
    return coherenceMetrics.colorHarmony * 0.4 + coherenceMetrics.sizeConsistency * 0.3 + coherenceMetrics.spatialBalance * 0.3;
  }
  assessColorHarmony(pattern) {
    const colors = pattern.map((element) => element.color).filter((color) => color);
    const uniqueColors = new Set(colors);
    if (uniqueColors.size >= 2 && uniqueColors.size <= 4) {
      return 100;
    } else if (uniqueColors.size === 1) {
      return 70;
    } else {
      return Math.max(30, 100 - (uniqueColors.size - 4) * 15);
    }
  }
  assessSizeConsistency(pattern) {
    const sizes = pattern.map((element) => element.size).filter((size) => size);
    const uniqueSizes = new Set(sizes);
    if (uniqueSizes.size >= 2 && uniqueSizes.size <= 3) {
      return 100;
    } else if (uniqueSizes.size === 1) {
      return 80;
    } else {
      return 60;
    }
  }
  assessSpatialBalance(pattern) {
    const positions = pattern.map((element) => element.position).filter((pos) => pos);
    if (positions.length < 2) return 100;
    const centerX = positions.reduce((sum, pos) => sum + pos.x, 0) / positions.length;
    const centerY = positions.reduce((sum, pos) => sum + pos.y, 0) / positions.length;
    let totalDeviation = 0;
    for (const pos of positions) {
      const deviation = Math.sqrt(
        Math.pow(pos.x - centerX, 2) + Math.pow(pos.y - centerY, 2)
      );
      totalDeviation += deviation;
    }
    const averageDeviation = totalDeviation / positions.length;
    const balanceScore = Math.max(0, 100 - averageDeviation / 5);
    return balanceScore;
  }
  assessManipulationStrategy(rules) {
    if (!rules.length) return 50;
    const strategyMetrics = {
      logicalOrder: this.assessLogicalOrder(rules),
      complexity: this.evaluateRuleComplexity(rules),
      efficiency: this.assessRuleEfficiency(rules)
    };
    return strategyMetrics.logicalOrder * 0.4 + strategyMetrics.complexity * 0.3 + strategyMetrics.efficiency * 0.3;
  }
  assessLogicalOrder(rules) {
    const optimalOrder = ["translate", "scale", "rotate", "changeColor", "changeShape", "flip"];
    let orderScore = 0;
    let lastOptimalIndex = -1;
    for (const rule of rules) {
      const currentOptimalIndex = optimalOrder.indexOf(rule.type);
      if (currentOptimalIndex >= lastOptimalIndex) {
        orderScore++;
        lastOptimalIndex = currentOptimalIndex;
      }
    }
    return orderScore / rules.length * 100;
  }
  assessRuleEfficiency(rules) {
    const ruleTypes = rules.map((rule) => rule.type);
    const uniqueTypes = new Set(ruleTypes);
    const efficiency = uniqueTypes.size / rules.length;
    return efficiency * 100;
  }
}
class AnomalyDetectionCollector {
  constructor() {
    this.name = "AnomalyDetectionCollector";
    this.version = "3.0.0";
    this.description = "Analisa habilidades de detecção e identificação de anomalias em padrões visuais";
  }
  async collect(gameState) {
    try {
      const patternElements = gameState.patternElements || [];
      const detectedAnomalies = gameState.detectedAnomalies || [];
      const actualAnomalies = gameState.actualAnomalies || [];
      const detectionTime = gameState.detectionTime || 0;
      return {
        // Métricas de detecção
        detectionAccuracy: this.calculateDetectionAccuracy(actualAnomalies, detectedAnomalies),
        anomalyIdentificationScore: this.assessAnomalyIdentification(actualAnomalies, detectedAnomalies),
        falsePositiveRate: this.calculateFalsePositiveRate(patternElements, detectedAnomalies, actualAnomalies),
        detectionSensitivity: this.calculateDetectionSensitivity(actualAnomalies, detectedAnomalies),
        // Análise visual
        visualDiscriminationAccuracy: this.assessVisualDiscrimination(patternElements, actualAnomalies, detectedAnomalies),
        patternDeviationRecognition: this.evaluatePatternDeviationRecognition(gameState),
        visualAttentionScope: this.assessVisualAttentionScope(patternElements, detectedAnomalies),
        perceptualSensitivity: this.evaluatePerceptualSensitivity(actualAnomalies, detectedAnomalies),
        // Processamento cognitivo
        anomalyCategorizationAbility: this.assessAnomalyCategorization(detectedAnomalies, actualAnomalies),
        patternComprehensionIndex: this.evaluatePatternComprehension(gameState),
        cognitiveFlexibilityInDetection: this.assessCognitiveFlexibility(gameState.sessionAttempts || []),
        // Eficiência e estratégia
        detectionEfficiency: this.calculateDetectionEfficiency(detectionTime, actualAnomalies.length),
        systematicSearchStrategy: this.assessSearchStrategy(gameState.searchSequence || []),
        attentionalFocusControl: this.evaluateAttentionalFocus(gameState),
        timestamp: Date.now(),
        sessionId: gameState.sessionId || "unknown"
      };
    } catch (error) {
      console.error("Erro no AnomalyDetectionCollector:", error);
      return null;
    }
  }
  calculateDetectionAccuracy(actual, detected) {
    if (!actual.length && !detected.length) return 100;
    if (!actual.length) return detected.length === 0 ? 100 : 0;
    let correctDetections = 0;
    for (const actualAnomaly of actual) {
      const wasDetected = detected.some(
        (detectedAnomaly) => this.anomaliesMatch(actualAnomaly, detectedAnomaly)
      );
      if (wasDetected) correctDetections++;
    }
    const falseDetections = this.countFalseDetections(actual, detected);
    const accuracy = correctDetections / actual.length * 100;
    const penalty = falseDetections / Math.max(detected.length, 1) * 20;
    return Math.max(0, accuracy - penalty);
  }
  anomaliesMatch(anomaly1, anomaly2) {
    if (anomaly1.elementId && anomaly2.elementId) {
      return anomaly1.elementId === anomaly2.elementId;
    }
    if (anomaly1.position && anomaly2.position) {
      const distance = Math.sqrt(
        Math.pow(anomaly1.position.x - anomaly2.position.x, 2) + Math.pow(anomaly1.position.y - anomaly2.position.y, 2)
      );
      return distance < 50;
    }
    return false;
  }
  countFalseDetections(actual, detected) {
    let falseDetections = 0;
    for (const detectedAnomaly of detected) {
      const isActualAnomaly = actual.some(
        (actualAnomaly) => this.anomaliesMatch(actualAnomaly, detectedAnomaly)
      );
      if (!isActualAnomaly) falseDetections++;
    }
    return falseDetections;
  }
  assessAnomalyIdentification(actual, detected) {
    if (!actual.length) return detected.length === 0 ? 100 : 0;
    let identificationScore = 0;
    for (const actualAnomaly of actual) {
      const matchingDetection = detected.find(
        (detectedAnomaly) => this.anomaliesMatch(actualAnomaly, detectedAnomaly)
      );
      if (matchingDetection) {
        const identificationAccuracy = this.assessAnomalyTypeIdentification(actualAnomaly, matchingDetection);
        identificationScore += identificationAccuracy;
      }
    }
    return actual.length > 0 ? identificationScore / actual.length : 0;
  }
  assessAnomalyTypeIdentification(actual, detected) {
    const typeScore = actual.type === detected.type ? 50 : 0;
    const severityScore = this.assessSeverityIdentification(actual.severity, detected.severity);
    const descriptionScore = this.assessDescriptionAccuracy(actual.description, detected.description);
    return typeScore + severityScore + descriptionScore;
  }
  assessSeverityIdentification(actualSeverity, detectedSeverity) {
    if (!actualSeverity || !detectedSeverity) return 25;
    const severityLevels = { low: 1, medium: 2, high: 3 };
    const actualLevel = severityLevels[actualSeverity] || 2;
    const detectedLevel = severityLevels[detectedSeverity] || 2;
    const difference = Math.abs(actualLevel - detectedLevel);
    return Math.max(0, 25 - difference * 8);
  }
  assessDescriptionAccuracy(actualDescription, detectedDescription) {
    if (!actualDescription || !detectedDescription) return 25;
    const actualWords = actualDescription.toLowerCase().split(" ");
    const detectedWords = detectedDescription.toLowerCase().split(" ");
    const commonWords = actualWords.filter((word) => detectedWords.includes(word));
    const similarity = commonWords.length / Math.max(actualWords.length, detectedWords.length);
    return similarity * 25;
  }
  calculateFalsePositiveRate(patternElements, detected, actual) {
    const totalElements = patternElements.length;
    const falsePositives = this.countFalseDetections(actual, detected);
    if (totalElements === 0) return 0;
    const falsePositiveRate = falsePositives / totalElements * 100;
    return Math.min(100, falsePositiveRate);
  }
  calculateDetectionSensitivity(actual, detected) {
    if (!actual.length) return 100;
    let detectedCount = 0;
    for (const actualAnomaly of actual) {
      const wasDetected = detected.some(
        (detectedAnomaly) => this.anomaliesMatch(actualAnomaly, detectedAnomaly)
      );
      if (wasDetected) detectedCount++;
    }
    return detectedCount / actual.length * 100;
  }
  assessVisualDiscrimination(patternElements, actualAnomalies, detectedAnomalies) {
    if (!patternElements.length) return 50;
    const discriminationTasks = this.identifyDiscriminationTasks(patternElements, actualAnomalies);
    let discriminationScore = 0;
    for (const task of discriminationTasks) {
      const taskScore = this.evaluateDiscriminationTask(task, detectedAnomalies);
      discriminationScore += taskScore;
    }
    return discriminationTasks.length > 0 ? discriminationScore / discriminationTasks.length : 50;
  }
  identifyDiscriminationTasks(patternElements, actualAnomalies) {
    const tasks = [];
    for (const anomaly of actualAnomalies) {
      const anomalyElement = this.findElementById(patternElements, anomaly.elementId);
      if (!anomalyElement) continue;
      const similarElements = patternElements.filter(
        (element) => element.id !== anomaly.elementId && this.calculateVisualSimilarity(element, anomalyElement) > 0.6
      );
      tasks.push({
        anomaly,
        element: anomalyElement,
        similarElements,
        difficulty: this.calculateDiscriminationDifficulty(anomalyElement, similarElements, anomaly)
      });
    }
    return tasks;
  }
  findElementById(elements, elementId) {
    return elements.find((element) => element.id === elementId);
  }
  calculateVisualSimilarity(element1, element2) {
    let similarity = 0;
    const features = ["shape", "color", "size", "pattern"];
    for (const feature of features) {
      if (element1[feature] === element2[feature]) {
        similarity += 0.25;
      }
    }
    return similarity;
  }
  calculateDiscriminationDifficulty(anomalyElement, similarElements, anomaly) {
    const similarityDifficulty = Math.min(similarElements.length / 5, 1) * 30;
    const anomalyTypeDifficulty = this.getAnomalyTypeDifficulty(anomaly.type);
    const severityDifficulty = this.getSeverityDifficulty(anomaly.severity);
    return similarityDifficulty + anomalyTypeDifficulty + severityDifficulty;
  }
  getAnomalyTypeDifficulty(anomalyType) {
    const difficultyMap = {
      color: 20,
      shape: 30,
      size: 25,
      position: 40,
      orientation: 35,
      pattern: 45,
      texture: 50
    };
    return difficultyMap[anomalyType] || 30;
  }
  getSeverityDifficulty(severity) {
    const difficultyMap = {
      high: 10,
      // Fácil de detectar
      medium: 25,
      // Moderadamente difícil
      low: 40
      // Difícil de detectar
    };
    return difficultyMap[severity] || 25;
  }
  evaluateDiscriminationTask(task, detectedAnomalies) {
    const wasDetected = detectedAnomalies.some(
      (detected) => this.anomaliesMatch(task.anomaly, detected)
    );
    if (wasDetected) {
      return Math.min(100, 100 - task.difficulty);
    } else {
      return Math.max(0, task.difficulty - 50);
    }
  }
  evaluatePatternDeviationRecognition(gameState) {
    const patternRules = gameState.patternRules || [];
    const detectedAnomalies = gameState.detectedAnomalies || [];
    if (!patternRules.length) return 50;
    let recognitionScore = 0;
    for (const rule of patternRules) {
      const ruleViolations = this.findRuleViolations(rule, gameState.patternElements || []);
      const detectedViolations = detectedAnomalies.filter(
        (anomaly) => anomaly.violatedRule === rule.id
      );
      const ruleRecognitionScore = this.calculateRuleRecognitionScore(ruleViolations, detectedViolations);
      recognitionScore += ruleRecognitionScore;
    }
    return recognitionScore / patternRules.length;
  }
  findRuleViolations(rule, patternElements) {
    const violations = [];
    switch (rule.type) {
      case "color_sequence":
        violations.push(...this.findColorSequenceViolations(rule, patternElements));
        break;
      case "size_progression":
        violations.push(...this.findSizeProgressionViolations(rule, patternElements));
        break;
      case "shape_pattern":
        violations.push(...this.findShapePatternViolations(rule, patternElements));
        break;
      case "spatial_arrangement":
        violations.push(...this.findSpatialArrangementViolations(rule, patternElements));
        break;
    }
    return violations;
  }
  findColorSequenceViolations(rule, elements) {
    const violations = [];
    const expectedSequence = rule.sequence || [];
    for (let i = 0; i < elements.length; i++) {
      const expectedColor = expectedSequence[i % expectedSequence.length];
      if (elements[i].color !== expectedColor) {
        violations.push({
          elementId: elements[i].id,
          type: "color_violation",
          expected: expectedColor,
          actual: elements[i].color
        });
      }
    }
    return violations;
  }
  findSizeProgressionViolations(rule, elements) {
    const violations = [];
    const sizeValues = { small: 1, medium: 2, large: 3 };
    for (let i = 1; i < elements.length; i++) {
      const currentSize = sizeValues[elements[i].size] || 2;
      const previousSize = sizeValues[elements[i - 1].size] || 2;
      const expectedProgression = rule.direction === "ascending" ? 1 : -1;
      const actualProgression = Math.sign(currentSize - previousSize);
      if (actualProgression !== expectedProgression && actualProgression !== 0) {
        violations.push({
          elementId: elements[i].id,
          type: "size_violation",
          expected: rule.direction,
          actual: actualProgression > 0 ? "ascending" : "descending"
        });
      }
    }
    return violations;
  }
  findShapePatternViolations(rule, elements) {
    const violations = [];
    const expectedPattern = rule.pattern || [];
    for (let i = 0; i < elements.length; i++) {
      const expectedShape = expectedPattern[i % expectedPattern.length];
      if (elements[i].shape !== expectedShape) {
        violations.push({
          elementId: elements[i].id,
          type: "shape_violation",
          expected: expectedShape,
          actual: elements[i].shape
        });
      }
    }
    return violations;
  }
  findSpatialArrangementViolations(rule, elements) {
    const violations = [];
    if (rule.alignment === "horizontal") {
      violations.push(...this.findHorizontalAlignmentViolations(elements));
    } else if (rule.alignment === "vertical") {
      violations.push(...this.findVerticalAlignmentViolations(elements));
    }
    return violations;
  }
  findHorizontalAlignmentViolations(elements) {
    const violations = [];
    const positions = elements.map((e) => e.position).filter((pos) => pos);
    if (positions.length < 2) return violations;
    const averageY = positions.reduce((sum, pos) => sum + pos.y, 0) / positions.length;
    const tolerance = 20;
    for (const element of elements) {
      if (element.position && Math.abs(element.position.y - averageY) > tolerance) {
        violations.push({
          elementId: element.id,
          type: "alignment_violation",
          expected: "horizontal",
          deviation: Math.abs(element.position.y - averageY)
        });
      }
    }
    return violations;
  }
  findVerticalAlignmentViolations(elements) {
    const violations = [];
    const positions = elements.map((e) => e.position).filter((pos) => pos);
    if (positions.length < 2) return violations;
    const averageX = positions.reduce((sum, pos) => sum + pos.x, 0) / positions.length;
    const tolerance = 20;
    for (const element of elements) {
      if (element.position && Math.abs(element.position.x - averageX) > tolerance) {
        violations.push({
          elementId: element.id,
          type: "alignment_violation",
          expected: "vertical",
          deviation: Math.abs(element.position.x - averageX)
        });
      }
    }
    return violations;
  }
  calculateRuleRecognitionScore(actualViolations, detectedViolations) {
    if (!actualViolations.length && !detectedViolations.length) return 100;
    if (!actualViolations.length) return detectedViolations.length === 0 ? 100 : 0;
    let recognizedViolations = 0;
    for (const actualViolation of actualViolations) {
      const wasRecognized = detectedViolations.some(
        (detected) => detected.elementId === actualViolation.elementId && detected.type === actualViolation.type
      );
      if (wasRecognized) recognizedViolations++;
    }
    return recognizedViolations / actualViolations.length * 100;
  }
  assessVisualAttentionScope(patternElements, detectedAnomalies) {
    if (!patternElements.length) return 50;
    const attentionMetrics = {
      spatialCoverage: this.assessSpatialCoverage(patternElements, detectedAnomalies),
      focusDistribution: this.assessFocusDistribution(detectedAnomalies),
      attentionPersistence: this.assessAttentionPersistence(detectedAnomalies)
    };
    return attentionMetrics.spatialCoverage * 0.5 + attentionMetrics.focusDistribution * 0.3 + attentionMetrics.attentionPersistence * 0.2;
  }
  assessSpatialCoverage(patternElements, detectedAnomalies) {
    const patternBounds = this.calculatePatternBounds(patternElements);
    const detectionBounds = this.calculateDetectionBounds(detectedAnomalies);
    if (!patternBounds || !detectionBounds) return 50;
    const coverageRatio = this.calculateBoundsCoverage(patternBounds, detectionBounds);
    return coverageRatio * 100;
  }
  calculatePatternBounds(elements) {
    const positions = elements.map((e) => e.position).filter((pos) => pos);
    if (positions.length === 0) return null;
    return {
      minX: Math.min(...positions.map((pos) => pos.x)),
      maxX: Math.max(...positions.map((pos) => pos.x)),
      minY: Math.min(...positions.map((pos) => pos.y)),
      maxY: Math.max(...positions.map((pos) => pos.y))
    };
  }
  calculateDetectionBounds(detections) {
    const positions = detections.map((d) => d.position).filter((pos) => pos);
    if (positions.length === 0) return null;
    return {
      minX: Math.min(...positions.map((pos) => pos.x)),
      maxX: Math.max(...positions.map((pos) => pos.x)),
      minY: Math.min(...positions.map((pos) => pos.y)),
      maxY: Math.max(...positions.map((pos) => pos.y))
    };
  }
  calculateBoundsCoverage(patternBounds, detectionBounds) {
    const overlapX = Math.max(0, Math.min(patternBounds.maxX, detectionBounds.maxX) - Math.max(patternBounds.minX, detectionBounds.minX));
    const overlapY = Math.max(0, Math.min(patternBounds.maxY, detectionBounds.maxY) - Math.max(patternBounds.minY, detectionBounds.minY));
    const overlapArea = overlapX * overlapY;
    const patternArea = (patternBounds.maxX - patternBounds.minX) * (patternBounds.maxY - patternBounds.minY);
    return patternArea > 0 ? overlapArea / patternArea : 0;
  }
  assessFocusDistribution(detectedAnomalies) {
    if (detectedAnomalies.length < 2) return 100;
    const positions = detectedAnomalies.map((d) => d.position).filter((pos) => pos);
    if (positions.length < 2) return 50;
    const centerX = positions.reduce((sum, pos) => sum + pos.x, 0) / positions.length;
    const centerY = positions.reduce((sum, pos) => sum + pos.y, 0) / positions.length;
    let totalDispersion = 0;
    for (const pos of positions) {
      const distance = Math.sqrt(
        Math.pow(pos.x - centerX, 2) + Math.pow(pos.y - centerY, 2)
      );
      totalDispersion += distance;
    }
    const averageDispersion = totalDispersion / positions.length;
    const optimalDispersion = 150;
    const distributionScore = Math.max(0, 100 - Math.abs(averageDispersion - optimalDispersion) / 2);
    return distributionScore;
  }
  assessAttentionPersistence(detectedAnomalies) {
    if (detectedAnomalies.length < 2) return 100;
    const clusters = this.clusterDetections(detectedAnomalies);
    let persistenceScore = 0;
    for (const cluster of clusters) {
      if (cluster.length > 1) {
        persistenceScore += Math.min(cluster.length * 20, 100);
      } else {
        persistenceScore += 50;
      }
    }
    return clusters.length > 0 ? persistenceScore / clusters.length : 100;
  }
  clusterDetections(detections) {
    const clusters = [];
    const visited = /* @__PURE__ */ new Set();
    const clusterRadius = 50;
    for (let i = 0; i < detections.length; i++) {
      if (visited.has(i) || !detections[i].position) continue;
      const cluster = [i];
      visited.add(i);
      for (let j = i + 1; j < detections.length; j++) {
        if (visited.has(j) || !detections[j].position) continue;
        const distance = Math.sqrt(
          Math.pow(detections[j].position.x - detections[i].position.x, 2) + Math.pow(detections[j].position.y - detections[i].position.y, 2)
        );
        if (distance <= clusterRadius) {
          cluster.push(j);
          visited.add(j);
        }
      }
      clusters.push(cluster);
    }
    return clusters;
  }
  evaluatePerceptualSensitivity(actualAnomalies, detectedAnomalies) {
    if (!actualAnomalies.length) return 50;
    let sensitivityScore = 0;
    for (const actualAnomaly of actualAnomalies) {
      const sensitivityLevel = this.calculateAnomalySensitivity(actualAnomaly);
      const wasDetected = detectedAnomalies.some(
        (detected) => this.anomaliesMatch(actualAnomaly, detected)
      );
      if (wasDetected) {
        sensitivityScore += Math.min(100, 50 + sensitivityLevel);
      } else {
        sensitivityScore += Math.max(0, 50 - sensitivityLevel);
      }
    }
    return sensitivityScore / actualAnomalies.length;
  }
  calculateAnomalySensitivity(anomaly) {
    const severityScore = this.getSeverityDifficulty(anomaly.severity);
    const typeScore = this.getAnomalyTypeDifficulty(anomaly.type);
    const contrastScore = this.calculateContrastScore(anomaly);
    return (severityScore + typeScore + contrastScore) / 3;
  }
  calculateContrastScore(anomaly) {
    const contrastLevel = anomaly.contrast || "medium";
    const contrastScores = {
      high: 10,
      medium: 30,
      low: 50
    };
    return contrastScores[contrastLevel] || 30;
  }
  assessAnomalyCategorization(detectedAnomalies, actualAnomalies) {
    if (!detectedAnomalies.length) return actualAnomalies.length === 0 ? 100 : 0;
    const categorizationMetrics = {
      typeAccuracy: this.assessTypeCategorizationAccuracy(detectedAnomalies, actualAnomalies),
      severityAccuracy: this.assessSeverityCategorization(detectedAnomalies, actualAnomalies),
      categoryConsistency: this.assessCategoryConsistency(detectedAnomalies)
    };
    return categorizationMetrics.typeAccuracy * 0.5 + categorizationMetrics.severityAccuracy * 0.3 + categorizationMetrics.categoryConsistency * 0.2;
  }
  assessTypeCategorizationAccuracy(detected, actual) {
    if (!actual.length) return 50;
    let accurateTyping = 0;
    for (const actualAnomaly of actual) {
      const matchingDetection = detected.find(
        (detectedAnomaly) => this.anomaliesMatch(actualAnomaly, detectedAnomaly)
      );
      if (matchingDetection && matchingDetection.type === actualAnomaly.type) {
        accurateTyping++;
      }
    }
    return accurateTyping / actual.length * 100;
  }
  assessSeverityCategorization(detected, actual) {
    if (!actual.length) return 50;
    let accurateSeverity = 0;
    for (const actualAnomaly of actual) {
      const matchingDetection = detected.find(
        (detectedAnomaly) => this.anomaliesMatch(actualAnomaly, detectedAnomaly)
      );
      if (matchingDetection) {
        const severityAccuracy = this.assessSeverityIdentification(
          actualAnomaly.severity,
          matchingDetection.severity
        );
        accurateSeverity += severityAccuracy;
      }
    }
    return actual.length > 0 ? accurateSeverity / actual.length : 50;
  }
  assessCategoryConsistency(detectedAnomalies) {
    if (detectedAnomalies.length < 2) return 100;
    const typeGroups = this.groupAnomaliesByType(detectedAnomalies);
    let consistencyScore = 0;
    for (const [type, anomalies] of Object.entries(typeGroups)) {
      const groupConsistency = this.calculateGroupConsistency(anomalies);
      consistencyScore += groupConsistency;
    }
    const typeCount = Object.keys(typeGroups).length;
    return typeCount > 0 ? consistencyScore / typeCount : 100;
  }
  groupAnomaliesByType(anomalies) {
    const groups = {};
    for (const anomaly of anomalies) {
      const type = anomaly.type || "unknown";
      if (!groups[type]) {
        groups[type] = [];
      }
      groups[type].push(anomaly);
    }
    return groups;
  }
  calculateGroupConsistency(anomalies) {
    if (anomalies.length < 2) return 100;
    const severities = anomalies.map((a) => a.severity).filter((s) => s);
    const uniqueSeverities = new Set(severities);
    const severityConsistency = severities.length > 0 ? 100 - (uniqueSeverities.size - 1) / severities.length * 50 : 100;
    return Math.max(0, severityConsistency);
  }
  evaluatePatternComprehension(gameState) {
    const patternRules = gameState.patternRules || [];
    const detectedAnomalies = gameState.detectedAnomalies || [];
    const patternElements = gameState.patternElements || [];
    if (!patternRules.length) return 50;
    const comprehensionMetrics = {
      ruleUnderstanding: this.assessRuleUnderstanding(patternRules, detectedAnomalies),
      patternRecognition: this.assessPatternRecognition(patternElements, gameState),
      contextualUnderstanding: this.assessContextualUnderstanding(gameState)
    };
    return comprehensionMetrics.ruleUnderstanding * 0.5 + comprehensionMetrics.patternRecognition * 0.3 + comprehensionMetrics.contextualUnderstanding * 0.2;
  }
  assessRuleUnderstanding(patternRules, detectedAnomalies) {
    let understandingScore = 0;
    for (const rule of patternRules) {
      const ruleViolations = detectedAnomalies.filter(
        (anomaly) => anomaly.violatedRule === rule.id
      );
      const ruleComplexity = this.calculateRuleComplexity(rule);
      const detectionQuality = this.assessRuleDetectionQuality(rule, ruleViolations);
      const complexityBonus = ruleComplexity / 100 * detectionQuality;
      understandingScore += detectionQuality + complexityBonus;
    }
    return patternRules.length > 0 ? understandingScore / patternRules.length : 50;
  }
  calculateRuleComplexity(rule) {
    const complexityScores = {
      color_sequence: 20,
      size_progression: 30,
      shape_pattern: 40,
      spatial_arrangement: 50,
      composite: 60
    };
    return complexityScores[rule.type] || 30;
  }
  assessRuleDetectionQuality(rule, detectedViolations) {
    if (!detectedViolations.length) return 30;
    let qualityScore = 0;
    for (const violation of detectedViolations) {
      const accuracy = this.assessViolationAccuracy(rule, violation);
      qualityScore += accuracy;
    }
    return detectedViolations.length > 0 ? qualityScore / detectedViolations.length : 30;
  }
  assessViolationAccuracy(rule, violation) {
    const baseScore = 50;
    if (violation.expected && violation.actual) {
      return baseScore + 30;
    } else if (violation.type) {
      return baseScore + 20;
    } else {
      return baseScore;
    }
  }
  assessPatternRecognition(patternElements, gameState) {
    const patternType = gameState.patternType || "unknown";
    const identifiedPattern = gameState.identifiedPattern || "unknown";
    if (patternType === identifiedPattern) {
      return 100;
    } else if (identifiedPattern !== "unknown") {
      return 60;
    } else {
      return 30;
    }
  }
  assessContextualUnderstanding(gameState) {
    const difficulty = gameState.difficulty || "medium";
    const detectionStrategy = gameState.detectionStrategy || "unknown";
    const detectedAnomalies = gameState.detectedAnomalies || [];
    let contextScore = 50;
    if (this.isStrategyAppropriate(detectionStrategy, difficulty)) {
      contextScore += 25;
    }
    if (this.isDetectionCountAppropriate(detectedAnomalies.length, difficulty)) {
      contextScore += 25;
    }
    return Math.min(100, contextScore);
  }
  isStrategyAppropriate(strategy, difficulty) {
    const appropriateStrategies = {
      easy: ["systematic", "quick"],
      medium: ["systematic", "focused"],
      hard: ["systematic", "analytical"]
    };
    return appropriateStrategies[difficulty]?.includes(strategy) || false;
  }
  isDetectionCountAppropriate(detectionCount, difficulty) {
    const expectedRanges = {
      easy: { min: 1, max: 3 },
      medium: { min: 2, max: 5 },
      hard: { min: 3, max: 8 }
    };
    const range = expectedRanges[difficulty] || { min: 1, max: 5 };
    return detectionCount >= range.min && detectionCount <= range.max;
  }
  assessCognitiveFlexibility(sessionAttempts) {
    if (sessionAttempts.length < 3) return 50;
    const flexibilityMetrics = {
      strategyAdaptation: this.assessStrategyAdaptation(sessionAttempts),
      errorLearning: this.assessErrorLearning(sessionAttempts),
      difficultyAdjustment: this.assessDifficultyAdjustment(sessionAttempts)
    };
    return flexibilityMetrics.strategyAdaptation * 0.4 + flexibilityMetrics.errorLearning * 0.3 + flexibilityMetrics.difficultyAdjustment * 0.3;
  }
  assessStrategyAdaptation(attempts) {
    const strategies = attempts.map((attempt) => attempt.detectionStrategy || "unknown");
    const uniqueStrategies = new Set(strategies);
    const adaptationScore = uniqueStrategies.size / attempts.length * 100;
    return Math.min(100, adaptationScore);
  }
  assessErrorLearning(attempts) {
    let learningScore = 0;
    for (let i = 1; i < attempts.length; i++) {
      const currentAccuracy = attempts[i].detectionAccuracy || 0;
      const previousAccuracy = attempts[i - 1].detectionAccuracy || 0;
      if (currentAccuracy > previousAccuracy) {
        learningScore += 20;
      } else if (currentAccuracy === previousAccuracy) {
        learningScore += 10;
      }
    }
    return Math.min(100, learningScore);
  }
  assessDifficultyAdjustment(attempts) {
    const difficulties = attempts.map((attempt) => attempt.difficulty).filter((d) => d);
    const uniqueDifficulties = new Set(difficulties);
    if (uniqueDifficulties.size < 2) return 50;
    let adjustmentScore = 0;
    for (let i = 1; i < attempts.length; i++) {
      const currentDiff = attempts[i].difficulty;
      const previousDiff = attempts[i - 1].difficulty;
      if (currentDiff !== previousDiff) {
        const currentAccuracy = attempts[i].detectionAccuracy || 0;
        if (currentAccuracy > 60) {
          adjustmentScore += 25;
        }
      }
    }
    return Math.min(100, adjustmentScore);
  }
  calculateDetectionEfficiency(detectionTime, anomalyCount) {
    if (anomalyCount === 0) return 100;
    const expectedTimePerAnomaly = 3e3;
    const totalExpectedTime = anomalyCount * expectedTimePerAnomaly;
    const efficiency = totalExpectedTime / Math.max(detectionTime, 1e3);
    return Math.min(100, efficiency * 100);
  }
  assessSearchStrategy(searchSequence) {
    if (!searchSequence.length) return 50;
    const strategyMetrics = {
      systematicness: this.assessSystematicSearch(searchSequence),
      coverage: this.assessSearchCoverage(searchSequence),
      efficiency: this.assessSearchEfficiency(searchSequence)
    };
    return strategyMetrics.systematicness * 0.4 + strategyMetrics.coverage * 0.3 + strategyMetrics.efficiency * 0.3;
  }
  assessSystematicSearch(searchSequence) {
    if (searchSequence.length < 3) return 50;
    const positions = searchSequence.map((step) => step.position).filter((pos) => pos);
    if (positions.length < 3) return 50;
    const searchPattern = this.identifySearchPattern(positions);
    const systematicScores = {
      linear: 90,
      grid: 95,
      spiral: 85,
      random: 30,
      clustered: 60
    };
    return systematicScores[searchPattern] || 50;
  }
  identifySearchPattern(positions) {
    if (this.isLinearSearch(positions)) return "linear";
    if (this.isGridSearch(positions)) return "grid";
    if (this.isSpiralSearch(positions)) return "spiral";
    if (this.isClusteredSearch(positions)) return "clustered";
    return "random";
  }
  isLinearSearch(positions) {
    if (positions.length < 3) return false;
    let linearX = true;
    let linearY = true;
    for (let i = 2; i < positions.length; i++) {
      const deltaX1 = positions[i - 1].x - positions[i - 2].x;
      const deltaX2 = positions[i].x - positions[i - 1].x;
      const deltaY1 = positions[i - 1].y - positions[i - 2].y;
      const deltaY2 = positions[i].y - positions[i - 1].y;
      if (Math.abs(deltaX1 - deltaX2) > 50) linearX = false;
      if (Math.abs(deltaY1 - deltaY2) > 50) linearY = false;
    }
    return linearX || linearY;
  }
  isGridSearch(positions) {
    const tolerance = 50;
    const xPositions = [...new Set(positions.map((p) => Math.round(p.x / tolerance) * tolerance))];
    const yPositions = [...new Set(positions.map((p) => Math.round(p.y / tolerance) * tolerance))];
    return xPositions.length >= 2 && yPositions.length >= 2;
  }
  isSpiralSearch(positions) {
    if (positions.length < 5) return false;
    const centerX = positions.reduce((sum, p) => sum + p.x, 0) / positions.length;
    const centerY = positions.reduce((sum, p) => sum + p.y, 0) / positions.length;
    const distances = positions.map(
      (p) => Math.sqrt(Math.pow(p.x - centerX, 2) + Math.pow(p.y - centerY, 2))
    );
    let increasing = 0;
    for (let i = 1; i < distances.length; i++) {
      if (distances[i] >= distances[i - 1] * 0.8) increasing++;
    }
    return increasing / (distances.length - 1) > 0.7;
  }
  isClusteredSearch(positions) {
    const clusters = this.identifySearchClusters(positions);
    return clusters.length > 1 && clusters.length < positions.length * 0.7;
  }
  identifySearchClusters(positions) {
    const clusters = [];
    const visited = /* @__PURE__ */ new Set();
    const clusterRadius = 100;
    for (let i = 0; i < positions.length; i++) {
      if (visited.has(i)) continue;
      const cluster = [i];
      visited.add(i);
      for (let j = i + 1; j < positions.length; j++) {
        if (visited.has(j)) continue;
        const distance = Math.sqrt(
          Math.pow(positions[j].x - positions[i].x, 2) + Math.pow(positions[j].y - positions[i].y, 2)
        );
        if (distance <= clusterRadius) {
          cluster.push(j);
          visited.add(j);
        }
      }
      clusters.push(cluster);
    }
    return clusters;
  }
  assessSearchCoverage(searchSequence) {
    const positions = searchSequence.map((step) => step.position).filter((pos) => pos);
    if (positions.length < 2) return 50;
    const searchBounds = this.calculateDetectionBounds(positions.map((pos) => ({ position: pos })));
    const searchArea = (searchBounds.maxX - searchBounds.minX) * (searchBounds.maxY - searchBounds.minY);
    const totalArea = 800 * 600;
    const coverageRatio = Math.min(1, searchArea / totalArea);
    return coverageRatio * 100;
  }
  assessSearchEfficiency(searchSequence) {
    const positions = searchSequence.map((step) => step.position).filter((pos) => pos);
    if (positions.length < 2) return 100;
    let redundantSearches = 0;
    const exploredAreas = [];
    for (const position of positions) {
      const isRedundant = exploredAreas.some(
        (area) => Math.sqrt(Math.pow(position.x - area.x, 2) + Math.pow(position.y - area.y, 2)) < 75
      );
      if (isRedundant) {
        redundantSearches++;
      } else {
        exploredAreas.push(position);
      }
    }
    const efficiency = Math.max(0, 100 - redundantSearches / positions.length * 100);
    return efficiency;
  }
  evaluateAttentionalFocus(gameState) {
    const detectedAnomalies = gameState.detectedAnomalies || [];
    const searchSequence = gameState.searchSequence || [];
    const responseTime = gameState.responseTime || 0;
    const focusMetrics = {
      selectionAccuracy: this.assessFocusSelectionAccuracy(detectedAnomalies, gameState.actualAnomalies || []),
      distractorResistance: this.assessDistractorResistance(gameState),
      focusMaintenance: this.assessFocusMaintenance(searchSequence, responseTime)
    };
    return focusMetrics.selectionAccuracy * 0.4 + focusMetrics.distractorResistance * 0.3 + focusMetrics.focusMaintenance * 0.3;
  }
  assessFocusSelectionAccuracy(detected, actual) {
    if (!actual.length) return detected.length === 0 ? 100 : 50;
    const correctSelections = detected.filter(
      (detectedAnomaly) => actual.some((actualAnomaly) => this.anomaliesMatch(actualAnomaly, detectedAnomaly))
    );
    return correctSelections.length / actual.length * 100;
  }
  assessDistractorResistance(gameState) {
    const distractors = gameState.distractors || [];
    const detectedAnomalies = gameState.detectedAnomalies || [];
    if (!distractors.length) return 100;
    const distractorDetections = detectedAnomalies.filter(
      (detected) => distractors.some((distractor) => this.anomaliesMatch(distractor, detected))
    );
    const resistanceScore = Math.max(0, 100 - distractorDetections.length / distractors.length * 50);
    return resistanceScore;
  }
  assessFocusMaintenance(searchSequence, responseTime) {
    if (!searchSequence.length) return 50;
    const focusStability = this.calculateFocusStability(searchSequence);
    const timeEfficiency = this.calculateTimeEfficiency(responseTime, searchSequence.length);
    return (focusStability + timeEfficiency) / 2;
  }
  calculateFocusStability(searchSequence) {
    if (searchSequence.length < 3) return 100;
    const dwellTimes = [];
    for (let i = 1; i < searchSequence.length; i++) {
      const dwellTime = searchSequence[i].timestamp - searchSequence[i - 1].timestamp;
      dwellTimes.push(dwellTime);
    }
    if (dwellTimes.length === 0) return 100;
    const meanDwellTime = dwellTimes.reduce((sum, time) => sum + time, 0) / dwellTimes.length;
    const variance = dwellTimes.reduce((sum, time) => sum + Math.pow(time - meanDwellTime, 2), 0) / dwellTimes.length;
    const standardDeviation = Math.sqrt(variance);
    const stability = Math.max(0, 100 - standardDeviation / meanDwellTime * 100);
    return stability;
  }
  calculateTimeEfficiency(responseTime, searchSteps) {
    const expectedTimePerStep = 1500;
    const expectedTotalTime = searchSteps * expectedTimePerStep;
    const efficiency = expectedTotalTime / Math.max(responseTime, 1e3);
    return Math.min(100, efficiency * 100);
  }
}
class PadroesVisuaisCollectorsHub {
  constructor() {
    this.gameType = "PadroesVisuais";
    this.version = "3.0.0";
    this._collectors = {
      patternRecognition: new PatternRecognitionCollector(),
      visualMemory: new VisualMemoryCollector(),
      spatialProcessing: new SpatialProcessingCollector(),
      sequentialReasoning: new SequentialReasoningCollector(),
      visualSequence: new VisualSequenceCollector(),
      spatialPattern: new SpatialPatternCollector(),
      colorPattern: new ColorPatternCollector(),
      geometricPattern: new GeometricPatternCollector(),
      temporalPattern: new TemporalPatternCollector(),
      errorPattern: new ErrorPatternCollector()
    };
    this.v3Collectors = {
      "reproduction-sequences": new SequenceReproductionCollector(),
      "pattern-completion": new PatternCompletionCollector(),
      "pattern-construction": new PatternConstructionCollector(),
      "visual-classification": new VisualClassificationCollector(),
      "pattern-transformation": new PatternTransformationCollector(),
      "anomaly-detection": new AnomalyDetectionCollector()
    };
    this.activeSession = null;
    this.sessionData = {
      sequences: [],
      interactions: [],
      metrics: {},
      startTime: null,
      currentActivity: null,
      activityData: {}
    };
    this.analysisConfig = {
      minDataPoints: 3,
      analysisThreshold: 5,
      realTimeAnalysis: true,
      deepAnalysisInterval: 10,
      useV3Collectors: true
    };
  }
  /**
   * Getter para coletores - necessário para GameSpecificProcessors
   */
  get collectors() {
    return this._collectors;
  }
  /**
   * Inicializar sessão de coleta
   */
  initializeSession(sessionId, gameConfig = {}) {
    console.log(`🎨 Sessão ${this.gameType} ${sessionId} iniciada`);
    this.activeSession = sessionId;
    this.sessionData = {
      sessionId,
      gameType: this.gameType,
      sequences: [],
      interactions: [],
      metrics: {},
      startTime: Date.now(),
      gameConfig,
      analysis: {
        patternRecognition: {},
        visualMemory: {},
        spatialProcessing: {},
        sequentialReasoning: {}
      }
    };
    return {
      success: true,
      sessionId,
      collectorsActive: Object.keys(this.collectors).length,
      gameType: this.gameType
    };
  }
  /**
   * Coletar dados de sequência de padrões visuais
   */
  collectSequenceData(sequenceData) {
    if (!this.activeSession) {
      console.warn("Tentativa de coleta sem sessão ativa");
      return false;
    }
    const processedSequence = this.processSequenceData(sequenceData);
    this.sessionData.sequences.push(processedSequence);
    if (this.analysisConfig.realTimeAnalysis && this.sessionData.sequences.length % this.analysisConfig.analysisThreshold === 0) {
      this.performRealtimeAnalysis();
    }
    return true;
  }
  /**
   * Coletar dados de interação específica
   */
  collectInteractionData(interactionData) {
    if (!this.activeSession) {
      console.warn("Tentativa de coleta sem sessão ativa");
      return false;
    }
    const processedInteraction = this.processInteractionData(interactionData);
    this.sessionData.interactions.push(processedInteraction);
    return true;
  }
  /**
   * Processar dados de sequência
   */
  processSequenceData(sequenceData) {
    const processed = {
      ...sequenceData,
      timestamp: Date.now(),
      sessionId: this.activeSession,
      processingTime: sequenceData.responseTime || 0,
      // Dados para análise de padrões
      patternData: {
        targetSequence: sequenceData.targetSequence,
        playerSequence: sequenceData.playerSequence,
        isCorrect: sequenceData.isCorrect,
        difficulty: sequenceData.difficulty,
        sequenceLength: sequenceData.targetSequence?.length || 0,
        showTime: sequenceData.showTime || 5e3,
        responseTime: sequenceData.responseTime || 2e3
      },
      // Dados para análise de memória visual
      memoryData: {
        sequences: [sequenceData],
        interactions: this.sessionData.interactions,
        retentionPeriod: sequenceData.showTime || 5e3,
        memoryLoad: sequenceData.targetSequence?.length || 0
      },
      // Dados para análise espacial
      spatialData: {
        sequences: [sequenceData],
        interactions: this.sessionData.interactions,
        spatialLayout: sequenceData.spatialLayout,
        visualComplexity: this.calculateVisualComplexity(sequenceData)
      },
      // Dados para análise de raciocínio sequencial
      sequentialData: {
        sequences: [sequenceData],
        temporalPattern: this.identifyTemporalPattern(sequenceData),
        logicalStructure: this.analyzeLogicalStructure(sequenceData)
      }
    };
    return processed;
  }
  /**
   * Processar dados de interação
   */
  processInteractionData(interactionData) {
    const processed = {
      ...interactionData,
      timestamp: Date.now(),
      sessionId: this.activeSession,
      // Enriquecer com dados contextuais
      context: {
        sequencePosition: this.sessionData.sequences.length,
        interactionPosition: this.sessionData.interactions.length,
        sessionProgress: this.calculateSessionProgress()
      },
      // Análise visual
      visualProcessing: {
        shapeComplexity: this.getShapeComplexity(interactionData.shapeId),
        processingTime: interactionData.responseTime || 2e3,
        visualAccuracy: interactionData.isCorrect
      },
      // Análise espacial
      spatialVisualization: {
        position: interactionData.position,
        spatialAccuracy: this.calculateSpatialAccuracy(interactionData),
        spatialComplexity: this.calculateSpatialComplexity(interactionData)
      }
    };
    return processed;
  }
  /**
   * Análise em tempo real
   */
  async performRealtimeAnalysis() {
    console.log("🎨 Iniciando análise em tempo real do PadroesVisuais...");
    const currentData = this.prepareAnalysisData();
    const quickAnalysis = await this.runQuickAnalysis(currentData);
    this.sessionData.metrics.realtime = {
      timestamp: Date.now(),
      analysis: quickAnalysis,
      dataPoints: this.sessionData.sequences.length + this.sessionData.interactions.length
    };
    console.log("✅ Análise em tempo real finalizada");
    return quickAnalysis;
  }
  /**
   * Análise completa da sessão
   */
  async performCompleteAnalysis() {
    if (!this.activeSession || this.sessionData.sequences.length === 0) {
      console.warn("Dados insuficientes para análise completa");
      return null;
    }
    console.log("🎨 Iniciando análise completa do PadroesVisuais...");
    const startTime = Date.now();
    const analysisData = this.prepareAnalysisData();
    const analysisPromises = [
      this.collectors.patternRecognition.analyze(analysisData),
      this.collectors.visualMemory.analyze(analysisData),
      this.collectors.spatialProcessing.analyze(analysisData),
      this.collectors.sequentialReasoning.analyze(analysisData)
    ];
    const [
      patternAnalysis,
      memoryAnalysis,
      spatialAnalysis,
      reasoningAnalysis
    ] = await Promise.all(analysisPromises);
    const integratedAnalysis = this.integrateAnalysisResults({
      patternRecognition: patternAnalysis,
      visualMemory: memoryAnalysis,
      spatialProcessing: spatialAnalysis,
      sequentialReasoning: reasoningAnalysis
    });
    const analysisTime = Date.now() - startTime;
    console.log(`✅ Análise completa finalizada em ${analysisTime}ms`);
    return integratedAnalysis;
  }
  /**
   * Preparar dados para análise
   */
  prepareAnalysisData() {
    return {
      sessionId: this.activeSession,
      gameType: this.gameType,
      // Dados específicos para cada coletor
      patternData: {
        sequences: this.sessionData.sequences,
        interactions: this.sessionData.interactions,
        patterns: this.extractPatterns(),
        complexity: this.calculateOverallComplexity()
      },
      memoryData: {
        sequences: this.sessionData.sequences,
        interactions: this.sessionData.interactions,
        memoryMetrics: this.calculateMemoryMetrics(),
        retentionData: this.extractRetentionData()
      },
      spatialData: {
        sequences: this.sessionData.sequences,
        interactions: this.sessionData.interactions,
        spatialMetrics: this.calculateSpatialMetrics(),
        visualComplexity: this.calculateVisualComplexityMetrics()
      },
      sequentialData: {
        sequences: this.sessionData.sequences,
        temporalPatterns: this.extractTemporalPatterns(),
        logicalStructures: this.extractLogicalStructures(),
        reasoningMetrics: this.calculateReasoningMetrics()
      }
    };
  }
  /**
   * Análise rápida para tempo real
   */
  async runQuickAnalysis(data) {
    const quickMetrics = {
      patternRecognition: this.calculateQuickPatternMetrics(data),
      visualMemory: this.calculateQuickMemoryMetrics(data),
      spatialProcessing: this.calculateQuickSpatialMetrics(data),
      sequentialReasoning: this.calculateQuickReasoningMetrics(data)
    };
    return quickMetrics;
  }
  /**
   * Integrar resultados de todos os coletores
   */
  integrateAnalysisResults(analyses) {
    const integrated = {
      sessionId: this.activeSession,
      gameType: this.gameType,
      timestamp: Date.now(),
      // Análises individuais
      individualAnalyses: analyses,
      // Métricas integradas
      integratedMetrics: this.calculateIntegratedMetrics(analyses),
      // Insights cruzados
      crossAnalysisInsights: this.generateCrossAnalysisInsights(analyses),
      // Recomendações
      recommendations: this.generateRecommendations(analyses),
      // Resumo da sessão
      sessionSummary: this.generateSessionSummary(analyses)
    };
    return integrated;
  }
  /**
   * Calcular métricas integradas
   */
  calculateIntegratedMetrics(analyses) {
    const integrated = {
      // Capacidade geral de padrões visuais
      overallPatternCapacity: this.calculateOverallPatternCapacity(analyses),
      // Eficiência de processamento
      processingEfficiency: this.calculateProcessingEfficiency(analyses),
      // Consistência entre domínios
      crossDomainConsistency: this.calculateCrossDomainConsistency(analyses),
      // Adaptabilidade
      adaptability: this.calculateAdaptability(analyses),
      // Potencial de desenvolvimento
      developmentPotential: this.calculateDevelopmentPotential(analyses)
    };
    return integrated;
  }
  /**
   * Gerar insights cruzados
   */
  generateCrossAnalysisInsights(analyses) {
    const insights = [];
    const patternMemoryCorrelation = this.analyzePatternMemoryCorrelation(
      analyses.patternRecognition,
      analyses.visualMemory
    );
    if (patternMemoryCorrelation.strength > 0.7) {
      insights.push("Forte correlação entre reconhecimento de padrões e memória visual");
    }
    const spatialReasoningCorrelation = this.analyzeSpatialReasoningCorrelation(
      analyses.spatialProcessing,
      analyses.sequentialReasoning
    );
    if (spatialReasoningCorrelation.strength > 0.7) {
      insights.push("Excelente integração entre processamento espacial e raciocínio sequencial");
    }
    const strengths = this.identifyStrengths(analyses);
    const weaknesses = this.identifyWeaknesses(analyses);
    if (strengths.length > 0) {
      insights.push(`Pontos fortes: ${strengths.join(", ")}`);
    }
    if (weaknesses.length > 0) {
      insights.push(`Áreas para desenvolvimento: ${weaknesses.join(", ")}`);
    }
    return insights;
  }
  /**
   * Gerar recomendações
   */
  generateRecommendations(analyses) {
    const recommendations = [];
    if (analyses.patternRecognition.sequenceRecognition < 0.6) {
      recommendations.push({
        domain: "pattern_recognition",
        priority: "high",
        suggestion: "Exercícios de reconhecimento de sequências simples",
        activities: ["Padrões de repetição", "Sequências de cores", "Jogos de memória visual"]
      });
    }
    if (analyses.visualMemory.shortTermMemory < 0.6) {
      recommendations.push({
        domain: "visual_memory",
        priority: "medium",
        suggestion: "Fortalecimento da memória visual de curto prazo",
        activities: ["Jogos de memória", "Sequências curtas", "Exercícios de retenção"]
      });
    }
    if (analyses.spatialProcessing.spatialVisualization < 0.6) {
      recommendations.push({
        domain: "spatial_processing",
        priority: "medium",
        suggestion: "Desenvolvimento de habilidades de visualização espacial",
        activities: ["Quebra-cabeças", "Jogos de rotação", "Exercícios de orientação"]
      });
    }
    if (analyses.sequentialReasoning.logicalSequencing < 0.6) {
      recommendations.push({
        domain: "sequential_reasoning",
        priority: "high",
        suggestion: "Fortalecimento do raciocínio lógico sequencial",
        activities: ["Sequências lógicas", "Padrões numéricos", "Jogos de estratégia"]
      });
    }
    return recommendations;
  }
  /**
   * Gerar relatório final
   */
  generateSessionReport() {
    const report = {
      sessionInfo: {
        sessionId: this.activeSession,
        gameType: this.gameType,
        startTime: this.sessionData.startTime,
        endTime: Date.now(),
        duration: Date.now() - this.sessionData.startTime,
        totalSequences: this.sessionData.sequences.length,
        totalInteractions: this.sessionData.interactions.length
      },
      dataCollected: {
        sequences: this.sessionData.sequences,
        interactions: this.sessionData.interactions,
        metrics: this.sessionData.metrics
      },
      collectorsUsed: Object.keys(this.collectors),
      analysisReadiness: {
        sufficientData: this.sessionData.sequences.length >= this.analysisConfig.minDataPoints,
        dataQuality: this.assessDataQuality(),
        recommendAnalysis: this.sessionData.sequences.length >= this.analysisConfig.analysisThreshold
      }
    };
    return report;
  }
  // Métodos auxiliares
  calculateVisualComplexity(sequenceData) {
    if (!sequenceData.targetSequence) return 1;
    const uniqueShapes = new Set(sequenceData.targetSequence).size;
    const sequenceLength = sequenceData.targetSequence.length;
    return Math.min(2, (uniqueShapes + sequenceLength) / 5);
  }
  identifyTemporalPattern(sequenceData) {
    return {
      type: "sequential",
      complexity: sequenceData.targetSequence?.length || 1,
      rhythm: "regular"
    };
  }
  analyzeLogicalStructure(sequenceData) {
    return {
      hasPattern: true,
      patternType: "sequence",
      logicalComplexity: 1.2
    };
  }
  getShapeComplexity(shapeId) {
    const complexities = {
      circle: 1,
      square: 1.1,
      triangle: 1.2,
      diamond: 1.3,
      star: 1.4,
      heart: 1.5
    };
    return complexities[shapeId] || 1;
  }
  calculateSpatialAccuracy(interactionData) {
    return interactionData.isCorrect ? 1 : 0;
  }
  calculateSpatialComplexity(interactionData) {
    return this.getShapeComplexity(interactionData.shapeId || "circle");
  }
  calculateSessionProgress() {
    const maxSequences = 10;
    return Math.min(1, this.sessionData.sequences.length / maxSequences);
  }
  extractPatterns() {
    return this.sessionData.sequences.map((s) => ({
      sequence: s.patternData.targetSequence,
      difficulty: s.patternData.difficulty,
      success: s.patternData.isCorrect
    }));
  }
  calculateOverallComplexity() {
    const complexities = this.sessionData.sequences.map((s) => s.spatialData.visualComplexity);
    return complexities.length > 0 ? complexities.reduce((sum, c) => sum + c, 0) / complexities.length : 1;
  }
  calculateMemoryMetrics() {
    const sequences = this.sessionData.sequences;
    return {
      avgRetentionTime: sequences.reduce((sum, s) => sum + (s.memoryData.retentionPeriod || 5e3), 0) / Math.max(1, sequences.length),
      avgMemoryLoad: sequences.reduce((sum, s) => sum + (s.memoryData.memoryLoad || 3), 0) / Math.max(1, sequences.length),
      successRate: sequences.filter((s) => s.patternData.isCorrect).length / Math.max(1, sequences.length)
    };
  }
  extractRetentionData() {
    return this.sessionData.sequences.map((s) => ({
      retentionPeriod: s.memoryData.retentionPeriod,
      memoryLoad: s.memoryData.memoryLoad,
      success: s.patternData.isCorrect
    }));
  }
  calculateSpatialMetrics() {
    const interactions = this.sessionData.interactions;
    return {
      avgProcessingTime: interactions.reduce((sum, i) => sum + (i.visualProcessing?.processingTime || 2e3), 0) / Math.max(1, interactions.length),
      spatialAccuracy: interactions.filter((i) => i.visualProcessing?.visualAccuracy).length / Math.max(1, interactions.length),
      avgComplexity: interactions.reduce((sum, i) => sum + (i.spatialVisualization?.spatialComplexity || 1), 0) / Math.max(1, interactions.length)
    };
  }
  calculateVisualComplexityMetrics() {
    const sequences = this.sessionData.sequences;
    return {
      avgVisualComplexity: sequences.reduce((sum, s) => sum + s.spatialData.visualComplexity, 0) / Math.max(1, sequences.length),
      maxComplexity: Math.max(...sequences.map((s) => s.spatialData.visualComplexity), 1),
      complexityRange: this.calculateComplexityRange(sequences)
    };
  }
  calculateComplexityRange(sequences) {
    const complexities = sequences.map((s) => s.spatialData.visualComplexity);
    return {
      min: Math.min(...complexities, 1),
      max: Math.max(...complexities, 1),
      range: Math.max(...complexities, 1) - Math.min(...complexities, 1)
    };
  }
  extractTemporalPatterns() {
    return this.sessionData.sequences.map((s) => s.sequentialData.temporalPattern);
  }
  extractLogicalStructures() {
    return this.sessionData.sequences.map((s) => s.sequentialData.logicalStructure);
  }
  calculateReasoningMetrics() {
    const sequences = this.sessionData.sequences;
    return {
      logicalAccuracy: sequences.filter((s) => s.patternData.isCorrect).length / Math.max(1, sequences.length),
      avgResponseTime: sequences.reduce((sum, s) => sum + s.patternData.responseTime, 0) / Math.max(1, sequences.length),
      complexityHandling: this.calculateComplexityHandling(sequences)
    };
  }
  calculateComplexityHandling(sequences) {
    const complexSequences = sequences.filter((s) => s.spatialData.visualComplexity > 1.5);
    return complexSequences.length > 0 ? complexSequences.filter((s) => s.patternData.isCorrect).length / complexSequences.length : 0.7;
  }
  // Métodos rápidos para análise em tempo real
  calculateQuickPatternMetrics(data) {
    const sequences = data.patternData.sequences;
    return {
      recognitionRate: sequences.filter((s) => s.patternData.isCorrect).length / Math.max(1, sequences.length),
      avgComplexity: data.patternData.complexity,
      totalPatterns: sequences.length
    };
  }
  calculateQuickMemoryMetrics(data) {
    const sequences = data.memoryData.sequences;
    return {
      retentionRate: sequences.filter((s) => s.patternData.isCorrect).length / Math.max(1, sequences.length),
      avgMemoryLoad: data.memoryData.memoryMetrics.avgMemoryLoad,
      memorySpan: Math.max(...sequences.map((s) => s.memoryData.memoryLoad), 3)
    };
  }
  calculateQuickSpatialMetrics(data) {
    const interactions = data.spatialData.interactions;
    return {
      spatialAccuracy: interactions.filter((i) => i.visualProcessing?.visualAccuracy).length / Math.max(1, interactions.length),
      processingSpeed: data.spatialData.spatialMetrics.avgProcessingTime,
      visualComplexity: data.spatialData.visualComplexity.avgVisualComplexity
    };
  }
  calculateQuickReasoningMetrics(data) {
    const sequences = data.sequentialData.sequences;
    return {
      logicalAccuracy: data.sequentialData.reasoningMetrics.logicalAccuracy,
      reasoningSpeed: data.sequentialData.reasoningMetrics.avgResponseTime,
      patternComplexity: sequences.length > 0 ? sequences[sequences.length - 1].sequentialData.logicalStructure.logicalComplexity : 1
    };
  }
  // Métricas integradas
  calculateOverallPatternCapacity(analyses) {
    const weights = {
      patternRecognition: 0.3,
      visualMemory: 0.25,
      spatialProcessing: 0.25,
      sequentialReasoning: 0.2
    };
    const scores = {
      patternRecognition: analyses.patternRecognition.sequenceRecognition || 0.7,
      visualMemory: analyses.visualMemory.shortTermMemory || 0.7,
      spatialProcessing: analyses.spatialProcessing.spatialVisualization || 0.7,
      sequentialReasoning: analyses.sequentialReasoning.logicalSequencing || 0.7
    };
    return Object.keys(weights).reduce(
      (sum, domain) => sum + scores[domain] * weights[domain],
      0
    );
  }
  calculateProcessingEfficiency(analyses) {
    const speeds = [
      analyses.patternRecognition.recognitionSpeed || 0.7,
      analyses.visualMemory.retentionQuality || 0.7,
      analyses.spatialProcessing.spatialEfficiency || 0.7,
      analyses.sequentialReasoning.temporalReasoning || 0.7
    ];
    return speeds.reduce((sum, speed) => sum + speed, 0) / speeds.length;
  }
  calculateCrossDomainConsistency(analyses) {
    const scores = [
      analyses.patternRecognition.sequenceRecognition || 0.7,
      analyses.visualMemory.shortTermMemory || 0.7,
      analyses.spatialProcessing.spatialVisualization || 0.7,
      analyses.sequentialReasoning.logicalSequencing || 0.7
    ];
    const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
    return Math.max(0, 1 - Math.sqrt(variance));
  }
  calculateAdaptability(analyses) {
    const adaptabilityScores = [
      analyses.patternRecognition.adaptiveRecognition || 0.7,
      analyses.visualMemory.interferenceResistance || 0.7,
      analyses.spatialProcessing.spatialEfficiency || 0.7,
      analyses.sequentialReasoning.inductiveReasoning || 0.7
    ];
    return adaptabilityScores.reduce((sum, score) => sum + score, 0) / adaptabilityScores.length;
  }
  calculateDevelopmentPotential(analyses) {
    const strengths = this.identifyStrengths(analyses);
    const consistency = this.calculateCrossDomainConsistency(analyses);
    const strengthBonus = strengths.length * 0.1;
    const consistencyBonus = consistency * 0.2;
    return Math.min(1, 0.6 + strengthBonus + consistencyBonus);
  }
  // Análises de correlação
  analyzePatternMemoryCorrelation(patternAnalysis, memoryAnalysis) {
    const patternScore = patternAnalysis.sequenceRecognition || 0.7;
    const memoryScore = memoryAnalysis.shortTermMemory || 0.7;
    const correlation = 1 - Math.abs(patternScore - memoryScore);
    return {
      strength: correlation,
      relationship: correlation > 0.7 ? "strong" : correlation > 0.5 ? "moderate" : "weak"
    };
  }
  analyzeSpatialReasoningCorrelation(spatialAnalysis, reasoningAnalysis) {
    const spatialScore = spatialAnalysis.spatialVisualization || 0.7;
    const reasoningScore = reasoningAnalysis.logicalSequencing || 0.7;
    const correlation = 1 - Math.abs(spatialScore - reasoningScore);
    return {
      strength: correlation,
      relationship: correlation > 0.7 ? "strong" : correlation > 0.5 ? "moderate" : "weak"
    };
  }
  identifyStrengths(analyses) {
    const strengths = [];
    const threshold = 0.8;
    if (analyses.patternRecognition.sequenceRecognition >= threshold) {
      strengths.push("reconhecimento_padroes");
    }
    if (analyses.visualMemory.shortTermMemory >= threshold) {
      strengths.push("memoria_visual");
    }
    if (analyses.spatialProcessing.spatialVisualization >= threshold) {
      strengths.push("processamento_espacial");
    }
    if (analyses.sequentialReasoning.logicalSequencing >= threshold) {
      strengths.push("raciocinio_sequencial");
    }
    return strengths;
  }
  identifyWeaknesses(analyses) {
    const weaknesses = [];
    const threshold = 0.6;
    if (analyses.patternRecognition.sequenceRecognition < threshold) {
      weaknesses.push("reconhecimento_padroes");
    }
    if (analyses.visualMemory.shortTermMemory < threshold) {
      weaknesses.push("memoria_visual");
    }
    if (analyses.spatialProcessing.spatialVisualization < threshold) {
      weaknesses.push("processamento_espacial");
    }
    if (analyses.sequentialReasoning.logicalSequencing < threshold) {
      weaknesses.push("raciocinio_sequencial");
    }
    return weaknesses;
  }
  generateSessionSummary(analyses) {
    return {
      overallPerformance: this.calculateOverallPatternCapacity(analyses),
      keyStrengths: this.identifyStrengths(analyses),
      developmentAreas: this.identifyWeaknesses(analyses),
      consistency: this.calculateCrossDomainConsistency(analyses),
      adaptability: this.calculateAdaptability(analyses),
      sessionQuality: this.assessDataQuality()
    };
  }
  assessDataQuality() {
    const totalDataPoints = this.sessionData.sequences.length + this.sessionData.interactions.length;
    const completenessScore = Math.min(1, totalDataPoints / 20);
    const varietyScore = Math.min(1, this.sessionData.sequences.length / 10);
    return (completenessScore + varietyScore) / 2;
  }
  /**
   * Coleta dados usando coletores V3 especializados
   */
  async collectV3Data(gameState) {
    if (!gameState.currentActivity) {
      console.warn("Atividade atual não especificada para coleta V3");
      return null;
    }
    const collector = this.v3Collectors[gameState.currentActivity];
    if (!collector) {
      console.warn(`Coletor V3 não encontrado para atividade: ${gameState.currentActivity}`);
      return null;
    }
    try {
      const data = await collector.collect(gameState);
      if (!this.sessionData.activityData[gameState.currentActivity]) {
        this.sessionData.activityData[gameState.currentActivity] = [];
      }
      this.sessionData.activityData[gameState.currentActivity].push({
        timestamp: Date.now(),
        data
      });
      return data;
    } catch (error) {
      console.error(`Erro na coleta V3 para ${gameState.currentActivity}:`, error);
      return null;
    }
  }
  /**
   * Coleta dados complementares usando coletores base relevantes
   */
  async collectComplementaryData(gameState) {
    const relevantCollectors = this.getRelevantBaseCollectors(gameState.currentActivity);
    const complementaryData = {};
    for (const [name, collector] of Object.entries(relevantCollectors)) {
      try {
        const data = await collector.collect(gameState);
        if (data) {
          complementaryData[name] = data;
        }
      } catch (error) {
        console.error(`Erro no coletor complementar ${name}:`, error);
      }
    }
    return complementaryData;
  }
  /**
   * Obtém coletores base relevantes para uma atividade específica
   */
  getRelevantBaseCollectors(activity) {
    const relevantCollectors = {};
    switch (activity) {
      case "reproduction-sequences":
        relevantCollectors.visualMemory = this.collectors.visualMemory;
        relevantCollectors.visualSequence = this.collectors.visualSequence;
        relevantCollectors.temporalPattern = this.collectors.temporalPattern;
        break;
      case "pattern-completion":
        relevantCollectors.patternRecognition = this.collectors.patternRecognition;
        relevantCollectors.sequentialReasoning = this.collectors.sequentialReasoning;
        relevantCollectors.geometricPattern = this.collectors.geometricPattern;
        break;
      case "pattern-construction":
        relevantCollectors.spatialPattern = this.collectors.spatialPattern;
        relevantCollectors.spatialProcessing = this.collectors.spatialProcessing;
        relevantCollectors.geometricPattern = this.collectors.geometricPattern;
        break;
      case "visual-classification":
        relevantCollectors.colorPattern = this.collectors.colorPattern;
        relevantCollectors.patternRecognition = this.collectors.patternRecognition;
        break;
      case "pattern-transformation":
        relevantCollectors.spatialPattern = this.collectors.spatialPattern;
        relevantCollectors.spatialProcessing = this.collectors.spatialProcessing;
        break;
      case "anomaly-detection":
        relevantCollectors.errorPattern = this.collectors.errorPattern;
        relevantCollectors.patternRecognition = this.collectors.patternRecognition;
        break;
      default:
        return this.collectors;
    }
    return relevantCollectors;
  }
  /**
   * Coleta dados completa (V3 + complementares)
   */
  async collectCompleteData(gameState) {
    const collectedData = {};
    const useV3Collectors = gameState.currentActivity && this.analysisConfig.useV3Collectors;
    if (useV3Collectors) {
      const v3Data = await this.collectV3Data(gameState);
      if (v3Data) {
        collectedData.v3 = v3Data;
        collectedData.activity = gameState.currentActivity;
      }
      const complementaryData = await this.collectComplementaryData(gameState);
      if (Object.keys(complementaryData).length > 0) {
        collectedData.complementary = complementaryData;
      }
    } else {
      for (const [name, collector] of Object.entries(this.collectors)) {
        try {
          const data = await collector.collect(gameState);
          if (data) {
            collectedData[name] = data;
          }
        } catch (error) {
          console.error(`Erro no coletor ${name}:`, error);
        }
      }
    }
    return collectedData;
  }
  /**
   * Análise integrada V3
   */
  async performV3Analysis(sessionData) {
    const analysis = {
      overview: {},
      activitySpecific: {},
      crossActivity: {},
      therapeuticInsights: {}
    };
    for (const [activity, data] of Object.entries(sessionData.activityData)) {
      if (data.length > 0) {
        analysis.activitySpecific[activity] = this.analyzeActivityData(activity, data);
      }
    }
    if (Object.keys(analysis.activitySpecific).length > 1) {
      analysis.crossActivity = this.performCrossActivityAnalysis(analysis.activitySpecific);
    }
    analysis.therapeuticInsights = this.generateTherapeuticInsights(analysis);
    analysis.overview = this.generateOverview(analysis);
    return analysis;
  }
  analyzeActivityData(activity, dataPoints) {
    const latestData = dataPoints[dataPoints.length - 1].data;
    const trend = this.calculateTrend(dataPoints);
    return {
      activity,
      latestMetrics: latestData,
      trend,
      dataPoints: dataPoints.length,
      avgPerformance: this.calculateAveragePerformance(dataPoints),
      strongPoints: this.identifyActivityStrengths(activity, dataPoints),
      improvementAreas: this.identifyImprovementAreas(activity, dataPoints)
    };
  }
  calculateTrend(dataPoints) {
    if (dataPoints.length < 2) return "stable";
    const recent = dataPoints.slice(-3);
    const older = dataPoints.slice(0, -3);
    if (recent.length === 0 || older.length === 0) return "stable";
    const recentAvg = recent.reduce((sum, dp) => sum + (dp.data.accuracy || 0), 0) / recent.length;
    const olderAvg = older.reduce((sum, dp) => sum + (dp.data.accuracy || 0), 0) / older.length;
    const difference = recentAvg - olderAvg;
    if (difference > 10) return "improving";
    if (difference < -10) return "declining";
    return "stable";
  }
  calculateAveragePerformance(dataPoints) {
    if (dataPoints.length === 0) return 0;
    const totalAccuracy = dataPoints.reduce((sum, dp) => sum + (dp.data.accuracy || 0), 0);
    return totalAccuracy / dataPoints.length;
  }
  identifyActivityStrengths(activity, dataPoints) {
    const strengths = [];
    const latestData = dataPoints[dataPoints.length - 1]?.data;
    if (!latestData) return strengths;
    switch (activity) {
      case "reproduction-sequences":
        if (latestData.sequentialProcessingSpeed > 80) strengths.push("Velocidade de processamento sequencial");
        if (latestData.visualMemorySpan > 75) strengths.push("Capacidade de memória visual");
        break;
      case "pattern-completion":
        if (latestData.logicalReasoningScore > 80) strengths.push("Raciocínio lógico");
        if (latestData.abstractThinkingScore > 75) strengths.push("Pensamento abstrato");
        break;
      case "pattern-construction":
        if (latestData.spatialOrganizationScore > 80) strengths.push("Organização espacial");
        if (latestData.planningQuality > 75) strengths.push("Qualidade de planejamento");
        break;
      case "visual-classification":
        if (latestData.categoricalThinkingIndex > 80) strengths.push("Pensamento categorial");
        if (latestData.visualDiscriminationIndex > 75) strengths.push("Discriminação visual");
        break;
      case "pattern-transformation":
        if (latestData.mentalRotationAbility > 80) strengths.push("Rotação mental");
        if (latestData.spatialVisualizationIndex > 75) strengths.push("Visualização espacial");
        break;
      case "anomaly-detection":
        if (latestData.detectionSensitivity > 80) strengths.push("Sensibilidade de detecção");
        if (latestData.visualAttentionScope > 75) strengths.push("Escopo de atenção visual");
        break;
    }
    return strengths;
  }
  identifyImprovementAreas(activity, dataPoints) {
    const improvements = [];
    const latestData = dataPoints[dataPoints.length - 1]?.data;
    if (!latestData) return improvements;
    switch (activity) {
      case "reproduction-sequences":
        if (latestData.reproductionAccuracy < 60) improvements.push("Precisão de reprodução");
        if (latestData.consistencyScore < 50) improvements.push("Consistência de desempenho");
        break;
      case "pattern-completion":
        if (latestData.patternRecognitionAccuracy < 60) improvements.push("Reconhecimento de padrões");
        if (latestData.strategicConsistency < 50) improvements.push("Consistência estratégica");
        break;
      case "pattern-construction":
        if (latestData.constructionAccuracy < 60) improvements.push("Precisão de construção");
        if (latestData.constructionEfficiency < 50) improvements.push("Eficiência de construção");
        break;
      case "visual-classification":
        if (latestData.classificationAccuracy < 60) improvements.push("Precisão de classificação");
        if (latestData.categoryConsistency < 50) improvements.push("Consistência categorial");
        break;
      case "pattern-transformation":
        if (latestData.transformationAccuracy < 60) improvements.push("Precisão de transformação");
        if (latestData.ruleApplicationAccuracy < 50) improvements.push("Aplicação de regras");
        break;
      case "anomaly-detection":
        if (latestData.detectionAccuracy < 60) improvements.push("Precisão de detecção");
        if (latestData.falsePositiveRate > 30) improvements.push("Redução de falsos positivos");
        break;
    }
    return improvements;
  }
  performCrossActivityAnalysis(activityAnalyses) {
    const crossAnalysis = {
      cognitiveProfile: this.buildCognitiveProfile(activityAnalyses),
      transferSkills: this.identifyTransferSkills(activityAnalyses),
      consistencyMetrics: this.calculateConsistencyAcrossActivities(activityAnalyses)
    };
    return crossAnalysis;
  }
  buildCognitiveProfile(activityAnalyses) {
    const profile = {
      visualProcessing: 0,
      spatialAbilities: 0,
      executiveFunctions: 0,
      memoryCapacity: 0,
      attentionControl: 0
    };
    let count = 0;
    for (const [activity, analysis] of Object.entries(activityAnalyses)) {
      const metrics = analysis.latestMetrics;
      switch (activity) {
        case "reproduction-sequences":
          profile.memoryCapacity += metrics.visualMemorySpan || 0;
          profile.attentionControl += metrics.attentionSustainedScore || 0;
          break;
        case "pattern-completion":
          profile.visualProcessing += metrics.logicalReasoningScore || 0;
          profile.executiveFunctions += metrics.abstractThinkingScore || 0;
          break;
        case "pattern-construction":
          profile.spatialAbilities += metrics.spatialOrganizationScore || 0;
          profile.executiveFunctions += metrics.executivePlanningScore || 0;
          break;
        case "visual-classification":
          profile.visualProcessing += metrics.visualDiscriminationIndex || 0;
          profile.executiveFunctions += metrics.categoricalThinkingIndex || 0;
          break;
        case "pattern-transformation":
          profile.spatialAbilities += metrics.spatialVisualizationIndex || 0;
          profile.visualProcessing += metrics.mentalRotationAbility || 0;
          break;
        case "anomaly-detection":
          profile.attentionControl += metrics.visualAttentionScope || 0;
          profile.visualProcessing += metrics.perceptualSensitivity || 0;
          break;
      }
      count++;
    }
    if (count > 0) {
      Object.keys(profile).forEach((key) => {
        profile[key] = profile[key] / count;
      });
    }
    return profile;
  }
  identifyTransferSkills(activityAnalyses) {
    const transferSkills = [];
    const activities = Object.keys(activityAnalyses);
    if (activities.includes("pattern-construction") && activities.includes("pattern-transformation")) {
      const constructionSpatial = activityAnalyses["pattern-construction"].latestMetrics.spatialOrganizationScore || 0;
      const transformationSpatial = activityAnalyses["pattern-transformation"].latestMetrics.spatialVisualizationIndex || 0;
      if (Math.abs(constructionSpatial - transformationSpatial) < 20) {
        transferSkills.push("Habilidades espaciais consistentes");
      }
    }
    if (activities.includes("visual-classification") && activities.includes("anomaly-detection")) {
      const classificationVisual = activityAnalyses["visual-classification"].latestMetrics.visualDiscriminationIndex || 0;
      const detectionVisual = activityAnalyses["anomaly-detection"].latestMetrics.perceptualSensitivity || 0;
      if (Math.abs(classificationVisual - detectionVisual) < 20) {
        transferSkills.push("Discriminação visual consistente");
      }
    }
    return transferSkills;
  }
  calculateConsistencyAcrossActivities(activityAnalyses) {
    const accuracies = Object.values(activityAnalyses).map((analysis) => analysis.avgPerformance);
    if (accuracies.length < 2) return { consistency: 100, variance: 0 };
    const mean = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    const variance = accuracies.reduce((sum, acc) => sum + Math.pow(acc - mean, 2), 0) / accuracies.length;
    const consistency = Math.max(0, 100 - Math.sqrt(variance));
    return { consistency, variance: Math.sqrt(variance) };
  }
  generateTherapeuticInsights(analysis) {
    const insights = {
      primaryStrengths: [],
      primaryChallenges: [],
      recommendations: [],
      cognitiveProfile: analysis.crossActivity?.cognitiveProfile || {}
    };
    if (analysis.crossActivity?.cognitiveProfile) {
      const profile = analysis.crossActivity.cognitiveProfile;
      const sortedAbilities = Object.entries(profile).sort((a, b) => b[1] - a[1]);
      insights.primaryStrengths = sortedAbilities.slice(0, 2).map(([ability, score]) => ({
        ability,
        score: Math.round(score)
      }));
      insights.primaryChallenges = sortedAbilities.slice(-2).map(([ability, score]) => ({
        ability,
        score: Math.round(score)
      }));
    }
    insights.recommendations = this.generateRecommendations(analysis);
    return insights;
  }
  generateOverview(analysis) {
    return {
      totalActivities: Object.keys(analysis.activitySpecific).length,
      overallPerformance: this.calculateOverallPerformance(analysis),
      keyInsights: this.extractKeyInsights(analysis),
      progressionStatus: this.assessProgressionStatus(analysis)
    };
  }
  calculateOverallPerformance(analysis) {
    const activities = Object.values(analysis.activitySpecific);
    if (activities.length === 0) return 0;
    const totalPerformance = activities.reduce((sum, activity) => sum + activity.avgPerformance, 0);
    return Math.round(totalPerformance / activities.length);
  }
  extractKeyInsights(analysis) {
    const insights = [];
    if (analysis.therapeuticInsights?.primaryStrengths?.length > 0) {
      const topStrength = analysis.therapeuticInsights.primaryStrengths[0];
      insights.push(`Força principal: ${topStrength.ability} (${topStrength.score}%)`);
    }
    if (analysis.therapeuticInsights?.primaryChallenges?.length > 0) {
      const topChallenge = analysis.therapeuticInsights.primaryChallenges[0];
      insights.push(`Área de foco: ${topChallenge.ability} (${topChallenge.score}%)`);
    }
    if (analysis.crossActivity?.transferSkills?.length > 0) {
      insights.push(`Habilidades transferíveis identificadas: ${analysis.crossActivity.transferSkills.length}`);
    }
    return insights;
  }
  assessProgressionStatus(analysis) {
    const trends = Object.values(analysis.activitySpecific).map((activity) => activity.trend);
    const improvingCount = trends.filter((trend) => trend === "improving").length;
    const decliningCount = trends.filter((trend) => trend === "declining").length;
    if (improvingCount > decliningCount) return "progressing";
    if (decliningCount > improvingCount) return "needs_attention";
    return "stable";
  }
  // Método principal para coleta de dados atualizado
  async collectDataPoint(gameState) {
    if (!this.activeSession) {
      console.warn("⚠️ Tentativa de coleta sem sessão ativa");
      return null;
    }
    try {
      const collectedData = await this.collectCompleteData(gameState);
      if (collectedData && Object.keys(collectedData).length > 0) {
        this.sessionData.interactions.push({
          timestamp: Date.now(),
          gameState: this.sanitizeGameState(gameState),
          collectedData,
          activity: gameState.currentActivity
        });
        if (this.analysisConfig.realTimeAnalysis) {
          await this.performRealtimeAnalysis(collectedData, gameState);
        }
        return collectedData;
      }
      return null;
    } catch (error) {
      console.error("❌ Erro na coleta de dados:", error);
      return null;
    }
  }
}
const isBrowser = typeof window !== "undefined" && typeof window.document !== "undefined";
const logger = isBrowser ? {
  info: (...args) => console.info("%c🔍 [PADROES-VISUAIS]", "color: #2196F3", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  error: (...args) => console.error("%c🔴 [PADROES-ERROR]", "color: #F44336", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  warn: (...args) => console.warn("%c🟡 [PADROES-WARN]", "color: #FF9800", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  debug: (...args) => console.debug("%c⚪ [PADROES-DEBUG]", "color: #9E9E9E", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  therapeutic: (...args) => console.info("%c🏥 [PADROES-THERAPEUTIC]", "color: #4CAF50", (/* @__PURE__ */ new Date()).toISOString(), ...args)
} : {
  info: (...args) => console.info("🔍 [PADROES-VISUAIS]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  error: (...args) => console.error("🔴 [PADROES-ERROR]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  warn: (...args) => console.warn("🟡 [PADROES-WARN]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  debug: (...args) => console.debug("⚪ [PADROES-DEBUG]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  therapeutic: (...args) => console.info("🏥 [PADROES-THERAPEUTIC]", (/* @__PURE__ */ new Date()).toISOString(), ...args)
};
class PadroesVisuaisProcessors extends IGameProcessor {
  constructor(loggerInstance = null) {
    const config = {
      category: "pattern_recognition",
      therapeuticFocus: ["pattern_recognition", "visual_processing", "spatial_reasoning"],
      cognitiveAreas: ["visual_processing", "executive_function", "reasoning"],
      thresholds: {
        accuracy: 65,
        responseTime: 4e3,
        engagement: 70
      }
    };
    super(config);
    this.logger = loggerInstance && typeof loggerInstance.therapeutic === "function" ? loggerInstance : logger;
    this.config = {
      category: "visual-perception",
      therapeuticFocus: ["pattern_recognition", "visual_sequencing", "spatial_awareness"],
      cognitiveAreas: ["visual_processing", "executive_function", "pattern_recognition"],
      thresholds: {
        accuracy: 65,
        responseTime: 4e3,
        engagement: 55
      }
    };
    this.logger.info("🔍 Processadores Padrões Visuais inicializados");
  }
  /**
   * Processa dados do jogo Padrões Visuais
   * @param {Object} gameData - Dados coletados do jogo
   * @param {Object} collectorsHub - Hub de coletores específico do jogo
   * @returns {Promise<Object>} Análise terapêutica específica
   */
  async processGameData(gameData, collectorsHub = null) {
    try {
      this.logger?.info("🎮 Processando dados PadroesVisuais", {
        sessionId: gameData.sessionId,
        userId: gameData.userId,
        collectorsActive: collectorsHub ? Object.keys(collectorsHub.collectors || {}).length : 0
      });
      const metrics = await this.processPadroesVisuaisMetrics(gameData, gameData);
      const therapeuticAnalysis = this.generateTherapeuticAnalysis(metrics, gameData);
      const processedMetrics = this.processMetricsForDatabase(metrics, gameData);
      return {
        success: true,
        gameType: this.gameType,
        metrics,
        therapeuticAnalysis,
        processedMetrics,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    } catch (error) {
      this.logger?.error("❌ Erro ao processar dados PadroesVisuais:", error);
      return {
        success: false,
        gameType: this.gameType,
        error: error.message,
        fallbackAnalysis: this.generateFallbackTherapeuticAnalysis(gameData),
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    }
  }
  assessVisualProcessingCapacity(patternRecognitionResults, visualMemoryResults) {
    const score = (patternRecognitionResults.patternIdentification + visualMemoryResults.visualRecall) / 2;
    return score > 0.8 ? "strong" : score > 0.5 ? "developing" : "needs_support";
  }
  assessSpatialReasoningAbility(spatialProcessingResults, sequentialReasoningResults) {
    const score = (spatialProcessingResults.spatialOrientation + sequentialReasoningResults.logicalProgression) / 2;
    return score > 0.8 ? "strong" : score > 0.5 ? "developing" : "needs_support";
  }
  assessPatternProcessingEfficiency(patternRecognitionResults, sequentialReasoningResults) {
    const score = (patternRecognitionResults.patternCompletion + sequentialReasoningResults.sequenceCompletion) / 2;
    return score > 0.8 ? "high" : score > 0.5 ? "moderate" : "low";
  }
  assessVisualMemoryIntegration(visualMemoryResults, spatialProcessingResults) {
    const score = (visualMemoryResults.visualRecall + spatialProcessingResults.spatialVisualization) / 2;
    return score > 0.8 ? "strong" : score > 0.5 ? "developing" : "needs_support";
  }
  assessCognitiveFlexibility(patternRecognitionResults, spatialProcessingResults) {
    const score = (patternRecognitionResults.patternDiversity + spatialProcessingResults.spatialFlexibility) / 2;
    return score > 0.8 ? "strong" : score > 0.5 ? "developing" : "needs_support";
  }
  assessExecutiveFunctioning(sequentialReasoningResults, patternRecognitionResults) {
    const score = (sequentialReasoningResults.ruleExtraction + patternRecognitionResults.patternIdentification) / 2;
    return score > 0.8 ? "strong" : score > 0.5 ? "developing" : "needs_support";
  }
  assessLearningPotential(patternRecognitionResults, visualMemoryResults) {
    const score = (patternRecognitionResults.patternCompletion + visualMemoryResults.workingMemoryCapacity) / 2;
    return score > 0.8 ? "high" : score > 0.5 ? "moderate" : "low";
  }
  determineInterventionPriorities(patternRecognitionResults, visualMemoryResults, spatialProcessingResults, sequentialReasoningResults) {
    const priorities = [];
    if (patternRecognitionResults.patternIdentification < 0.5) {
      priorities.push("pattern_identification");
    }
    if (visualMemoryResults.visualRecall < 0.5) {
      priorities.push("visual_recall");
    }
    if (spatialProcessingResults.spatialOrientation < 0.5) {
      priorities.push("spatial_orientation");
    }
    if (sequentialReasoningResults.sequenceCompletion < 0.5) {
      priorities.push("sequence_completion");
    }
    return priorities;
  }
  generateIntegratedAdaptiveRecommendations(patternRecognitionResults, visualMemoryResults, spatialProcessingResults, sequentialReasoningResults) {
    const recommendations = [];
    if (patternRecognitionResults.patternIdentification < 0.5) {
      recommendations.push("Simplificar padrões para melhorar identificação");
    }
    if (visualMemoryResults.visualRecall < 0.5) {
      recommendations.push("Usar sequências curtas para treinar memória visual");
    }
    if (spatialProcessingResults.spatialOrientation < 0.5) {
      recommendations.push("Introduzir exercícios de orientação espacial simples");
    }
    if (sequentialReasoningResults.sequenceCompletion < 0.5) {
      recommendations.push("Focar em sequências lógicas de baixa complexidade");
    }
    return recommendations;
  }
  identifyProgressMarkers(patternRecognitionResults, visualMemoryResults, spatialProcessingResults, sequentialReasoningResults) {
    const markers = [];
    if (patternRecognitionResults.patternIdentification > 0.7) {
      markers.push("pattern_identification_achieved");
    }
    if (visualMemoryResults.visualRecall > 0.7) {
      markers.push("visual_recall_achieved");
    }
    if (spatialProcessingResults.spatialOrientation > 0.7) {
      markers.push("spatial_orientation_achieved");
    }
    if (sequentialReasoningResults.sequenceCompletion > 0.7) {
      markers.push("sequence_completion_achieved");
    }
    return markers;
  }
  createStrengthsProfile(patternRecognitionResults, visualMemoryResults, spatialProcessingResults, sequentialReasoningResults) {
    return {
      patternRecognition: patternRecognitionResults.recognitionStrengths,
      visualMemory: visualMemoryResults.memoryStrengths,
      spatialProcessing: spatialProcessingResults.spatialStrengths,
      sequentialReasoning: sequentialReasoningResults.reasoningStrengths
    };
  }
  createChallengesProfile(patternRecognitionResults, visualMemoryResults, spatialProcessingResults, sequentialReasoningResults) {
    return {
      patternRecognition: patternRecognitionResults.recognitionChallenges,
      visualMemory: visualMemoryResults.memoryChallenges,
      spatialProcessing: spatialProcessingResults.spatialChallenges,
      sequentialReasoning: sequentialReasoningResults.reasoningChallenges
    };
  }
  generateVisualProfile(results) {
    return {
      patternIdentification: results.patternRecognitionResults.patternIdentification,
      visualRecall: results.visualMemoryResults.visualRecall,
      spatialOrientation: results.spatialProcessingResults.spatialOrientation
    };
  }
  generateCognitiveProfile(results) {
    return {
      patternRecognition: results.patternRecognitionResults.patternIdentification,
      workingMemory: results.visualMemoryResults.workingMemoryCapacity,
      ruleExtraction: results.sequentialReasoningResults.ruleExtraction
    };
  }
  generatePatternProfile(results) {
    return {
      completion: results.patternRecognitionResults.patternCompletion,
      geometric: results.patternRecognitionResults.geometricPatterns,
      sequential: results.patternRecognitionResults.sequentialPatterns
    };
  }
  generateSpatialProfile(results) {
    return {
      orientation: results.spatialProcessingResults.spatialOrientation,
      visualization: results.spatialProcessingResults.spatialVisualization,
      attention: results.spatialProcessingResults.spatialAttention
    };
  }
  assessPatternDevelopmentLevel(results) {
    const overallScore = this.calculateOverallPatternScore(results);
    if (overallScore > 0.8) return "advanced";
    if (overallScore > 0.5) return "developing";
    return "needs_support";
  }
  identifyTherapeuticTargets(results) {
    const targets = [];
    if (results.patternRecognitionResults.patternIdentification < 0.5) {
      targets.push("pattern_identification");
    }
    if (results.visualMemoryResults.visualRecall < 0.5) {
      targets.push("visual_recall");
    }
    if (results.spatialProcessingResults.spatialOrientation < 0.5) {
      targets.push("spatial_orientation");
    }
    if (results.sequentialReasoningResults.sequenceCompletion < 0.5) {
      targets.push("sequence_completion");
    }
    return targets;
  }
  generateInterventionPlan(results) {
    const plan = [];
    if (results.patternRecognitionResults.patternIdentification < 0.5) {
      plan.push("Implementar exercícios de identificação de padrões");
    }
    if (results.visualMemoryResults.visualRecall < 0.5) {
      plan.push("Focar em treinamento de memória visual");
    }
    if (results.spatialProcessingResults.spatialOrientation < 0.5) {
      plan.push("Praticar orientação espacial");
    }
    if (results.sequentialReasoningResults.sequenceCompletion < 0.5) {
      plan.push("Desenvolver habilidades de raciocínio sequencial");
    }
    return plan;
  }
  generateProgressTracking(results) {
    return [
      { metric: "patternIdentification", value: results.patternRecognitionResults.patternIdentification },
      { metric: "visualRecall", value: results.visualMemoryResults.visualRecall },
      { metric: "spatialOrientation", value: results.spatialProcessingResults.spatialOrientation },
      { metric: "sequenceCompletion", value: results.sequentialReasoningResults.sequenceCompletion }
    ];
  }
  generateAdaptiveStrategies(results) {
    const strategies = [];
    if (results.patternRecognitionResults.patternIdentification < 0.5) {
      strategies.push("Simplificar padrões para melhorar identificação");
    }
    if (results.visualMemoryResults.visualRecall < 0.5) {
      strategies.push("Usar sequências visuais curtas");
    }
    if (results.spatialProcessingResults.spatialOrientation < 0.5) {
      strategies.push("Introduzir exercícios espaciais simples");
    }
    if (results.sequentialReasoningResults.sequenceCompletion < 0.5) {
      strategies.push("Focar em sequências lógicas de baixa complexidade");
    }
    return strategies;
  }
  integrateStrengths(results) {
    return [
      ...results.patternRecognitionResults.recognitionStrengths,
      ...results.visualMemoryResults.memoryStrengths,
      ...results.spatialProcessingResults.spatialStrengths,
      ...results.sequentialReasoningResults.reasoningStrengths
    ];
  }
  integrateChallenges(results) {
    return [
      ...results.patternRecognitionResults.recognitionChallenges,
      ...results.visualMemoryResults.memoryChallenges,
      ...results.spatialProcessingResults.spatialChallenges,
      ...results.sequentialReasoningResults.reasoningChallenges
    ];
  }
  integrateRecommendations(results) {
    return [
      ...results.patternRecognitionResults.recommendations,
      ...results.visualMemoryResults.recommendations,
      ...results.spatialProcessingResults.recommendations,
      ...results.sequentialReasoningResults.recommendations
    ];
  }
  assessDataQuality(gameData) {
    let quality = 1;
    if (!gameData.sessionId) quality -= 0.2;
    if (!gameData.userId) quality -= 0.2;
    if (!gameData.attempts || gameData.attempts.length === 0) quality -= 0.3;
    if (!gameData.metrics) quality -= 0.3;
    return Math.max(0, quality);
  }
  async storeProcessedData(processedData) {
    try {
      if (this.databaseService) {
        await this.databaseService.store("padroes_visuais_analysis", processedData);
        this.logger.info("📊 Dados processados armazenados com sucesso");
      }
    } catch (error) {
      this.logger.error("❌ Erro ao armazenar dados processados:", error);
    }
  }
  /**
   * Método principal para processar dados (compatibilidade com GameSpecificProcessors)
   * @param {Object} gameData - Dados do jogo
   * @returns {Promise<Object>} - Dados processados
   */
  async processData(gameData) {
    try {
      const result = await this.processGameData(gameData);
      return {
        success: true,
        gameType: "PadroesVisuais",
        metrics: {
          patternRecognition: result.patternRecognition || {},
          visualMemory: result.visualMemory || {},
          spatialProcessing: result.spatialProcessing || {},
          sequentialReasoning: result.sequentialReasoning || {},
          therapeuticOutcomes: result.therapeuticOutcomes || {}
        },
        therapeuticAnalysis: result.therapeuticOutcomes || {},
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    } catch (error) {
      this.logger?.error("Erro no processData PadroesVisuais:", error);
      return {
        success: false,
        gameType: "PadroesVisuais",
        error: error.message,
        metrics: {},
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    }
  }
  /**
   * Processa coletores com Circuit Breaker para resiliência
   * @param {Object} collectorsHub - Hub de coletores
   * @param {Object} gameData - Dados do jogo
   * @returns {Promise<Object>} Resultados dos coletores
   */
  async processCollectorsWithCircuitBreaker(collectorsHub, gameData) {
    if (!collectorsHub || !collectorsHub.collectors) {
      this.logger?.warn("⚠️ PadroesVisuais: Hub de coletores não disponível");
      return { collectors: {}, warning: "No collectors available" };
    }
    const results = {};
    const collectors = collectorsHub.collectors;
    for (const [collectorName, collector] of Object.entries(collectors)) {
      try {
        if (collector && typeof collector.analyze === "function") {
          this.logger?.debug("🎮 Processando coletor: " + collectorName);
          results[collectorName] = await this.processWithTimeout(
            () => collector.analyze(gameData),
            5e3,
            // 5 segundos timeout
            collectorName + " timeout"
          );
        } else {
          this.logger?.warn("⚠️ Coletor " + collectorName + " não tem método analyze");
          results[collectorName] = { error: "No analyze method" };
        }
      } catch (error) {
        this.logger?.error("❌ Erro no coletor " + collectorName + ":", error);
        results[collectorName] = {
          error: error.message,
          fallback: this.generateFallbackMetrics(collectorName, gameData)
        };
      }
    }
    return {
      collectors: results,
      processedAt: (/* @__PURE__ */ new Date()).toISOString(),
      gameType: "PadroesVisuais"
    };
  }
  /**
   * Processa com timeout para evitar travamentos
   */
  async processWithTimeout(fn, timeout, errorMsg) {
    return Promise.race([
      fn(),
      new Promise(
        (_, reject) => setTimeout(() => reject(new Error(errorMsg)), timeout)
      )
    ]);
  }
  /**
   * Gera métricas de fallback em caso de erro
   */
  generateFallbackMetrics(collectorName, gameData) {
    return {
      fallback: true,
      collector: collectorName,
      basicScore: 50,
      confidence: "low",
      note: "Generated due to collector error"
    };
  }
  /**
   * Calcula score geral de padrões
   */
  calculateOverallPatternScore(patternData) {
    if (!patternData || Object.keys(patternData).length === 0) {
      return { score: 0, level: "insufficient_data" };
    }
    const scores = [];
    if (patternData.recognition) scores.push(patternData.recognition.score || 0);
    if (patternData.completion) scores.push(patternData.completion.score || 0);
    if (patternData.complexity) scores.push(patternData.complexity.score || 0);
    if (patternData.spatial) scores.push(patternData.spatial.score || 0);
    if (scores.length === 0) {
      return { score: 0, level: "no_data" };
    }
    const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length;
    return {
      score: avgScore,
      level: avgScore > 0.8 ? "excellent" : avgScore > 0.6 ? "good" : avgScore > 0.4 ? "average" : "needs_improvement",
      components: {
        recognition: patternData.recognition?.score || 0,
        completion: patternData.completion?.score || 0,
        complexity: patternData.complexity?.score || 0,
        spatial: patternData.spatial?.score || 0
      },
      consistency: this.calculatePatternConsistency(scores)
    };
  }
  /**
   * Calcula consistência de padrões
   */
  calculatePatternConsistency(scores) {
    if (scores.length < 2) return 1;
    const mean = scores.reduce((a, b) => a + b, 0) / scores.length;
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
    const stdDev = Math.sqrt(variance);
    return Math.max(0, 1 - stdDev / mean);
  }
  /**
   * Gera recomendações específicas para Padrões Visuais
   */
  generatePadroesVisuaisRecommendations(patternRecognition, visualSequencing, spatialAwareness) {
    const recommendations = [];
    if (patternRecognition && patternRecognition.score < 0.6) {
      recommendations.push({
        area: "pattern_recognition",
        type: "improvement",
        activity: "Exercícios de identificação de padrões simples"
      });
    }
    if (visualSequencing && visualSequencing.score < 0.6) {
      recommendations.push({
        area: "visual_sequencing",
        type: "improvement",
        activity: "Jogos de sequência visual com blocos coloridos"
      });
    }
    if (spatialAwareness && spatialAwareness.score < 0.6) {
      recommendations.push({
        area: "spatial_awareness",
        type: "improvement",
        activity: "Atividades de orientação espacial e mapas"
      });
    }
    return recommendations.length > 0 ? recommendations : [{
      area: "general",
      type: "maintenance",
      activity: "Continue praticando com padrões visuais variados"
    }];
  }
  /**
   * Gera análise terapêutica completa
   * @param {Object} metrics - Métricas processadas
   * @param {Object} gameData - Dados do jogo
   * @returns {Object} Análise terapêutica
   */
  generateTherapeuticAnalysis(metrics, gameData) {
    try {
      const analysis = {
        // Análise comportamental
        behavioral: {
          engagement: this.calculateEngagementScore(gameData),
          persistence: this.calculatePersistenceScore(gameData),
          adaptability: this.calculateAdaptabilityScore(gameData),
          frustrationTolerance: this.calculateFrustrationTolerance(gameData),
          socialInteraction: this.calculateSocialInteractionScore(gameData)
        },
        // Análise cognitiva
        cognitive: {
          attention: this.calculateAttentionScore(gameData),
          memory: this.calculateMemoryScore(gameData),
          processingSpeed: this.calculateProcessingSpeedScore(gameData),
          executiveFunction: this.calculateExecutiveFunctionScore(gameData),
          visualProcessing: this.calculateVisualProcessingScore(gameData)
        },
        // Análise sensorial
        sensory: {
          visualPerception: this.calculateVisualPerceptionScore(gameData),
          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),
          tactileProcessing: this.calculateTactileProcessingScore(gameData),
          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)
        },
        // Análise motora
        motor: {
          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),
          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),
          coordination: this.calculateCoordinationScore(gameData),
          motorPlanning: this.calculateMotorPlanningScore(gameData)
        },
        // Recomendações terapêuticas
        recommendations: this.generateTherapeuticRecommendations(metrics, gameData),
        // Indicadores de progresso
        progressIndicators: this.generateProgressIndicators(metrics, gameData),
        // Insights específicos do jogo
        gameSpecificInsights: this.generateGameSpecificInsights(metrics, gameData),
        // Metadados
        metadata: {
          analysisTimestamp: (/* @__PURE__ */ new Date()).toISOString(),
          gameType: this.gameType,
          analysisVersion: "3.0.0",
          confidenceScore: this.calculateAnalysisConfidenceScore(metrics, gameData)
        }
      };
      return analysis;
    } catch (error) {
      this.logger?.error("❌ Erro ao gerar análise terapêutica:", error);
      return this.generateFallbackTherapeuticAnalysis(gameData);
    }
  }
  /**
   * Métodos de cálculo de scores terapêuticos
   */
  calculateEngagementScore(gameData) {
    const interactions = gameData.interactions || [];
    const totalTime = gameData.totalTime || 1e3;
    const completionRate = gameData.completionRate || 0;
    let score = 50;
    if (interactions.length > 0) {
      score += Math.min(30, interactions.length * 2);
    }
    if (totalTime > 3e4) {
      score += 10;
    }
    score += completionRate * 10;
    return Math.max(0, Math.min(100, score));
  }
  calculatePersistenceScore(gameData) {
    const attempts = gameData.attempts || 1;
    const errors = gameData.errors || 0;
    const completion = gameData.completion || 0;
    let score = 50;
    if (attempts > 1 && completion > 0.5) {
      score += 20;
    }
    if (errors > 0 && completion > 0.8) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateAdaptabilityScore(gameData) {
    const difficultyChanges = gameData.difficultyChanges || 0;
    const adaptationSuccess = gameData.adaptationSuccess || 0;
    let score = 50;
    if (difficultyChanges > 0) {
      score += adaptationSuccess * 20;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateFrustrationTolerance(gameData) {
    const errors = gameData.errors || 0;
    const quitEarly = gameData.quitEarly || false;
    const completion = gameData.completion || 0;
    let score = 70;
    if (errors > 3 && !quitEarly) {
      score += 15;
    }
    if (completion > 0.8) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateSocialInteractionScore(gameData) {
    const engagement = this.calculateEngagementScore(gameData);
    return Math.max(30, Math.min(80, engagement * 0.8));
  }
  calculateAttentionScore(gameData) {
    const focusTime = gameData.focusTime || 0;
    const distractions = gameData.distractions || 0;
    const responseTime = gameData.averageResponseTime || 3e3;
    let score = 50;
    if (focusTime > 6e4) {
      score += 20;
    }
    if (distractions < 2) {
      score += 15;
    }
    if (responseTime < 2e3) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateMemoryScore(gameData) {
    const accuracy = gameData.accuracy || 0;
    const patterns = gameData.patterns || [];
    let score = 50;
    if (accuracy > 70) {
      score += 25;
    }
    if (patterns.length > 3) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateProcessingSpeedScore(gameData) {
    const responseTime = gameData.averageResponseTime || 3e3;
    const accuracy = gameData.accuracy || 0;
    let score = 50;
    if (responseTime < 1500 && accuracy > 60) {
      score += 30;
    } else if (responseTime < 2500) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateExecutiveFunctionScore(gameData) {
    const planningEvidence = gameData.planningEvidence || 0;
    const inhibitionControl = gameData.inhibitionControl || 0;
    const workingMemory = gameData.workingMemory || 0;
    const score = (planningEvidence + inhibitionControl + workingMemory) / 3;
    return Math.max(0, Math.min(100, score));
  }
  calculateVisualProcessingScore(gameData) {
    const visualTasks = gameData.visualTasks || 0;
    const visualAccuracy = gameData.visualAccuracy || 0;
    let score = 50;
    if (visualTasks > 5 && visualAccuracy > 70) {
      score += 25;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateVisualPerceptionScore(gameData) {
    return this.calculateVisualProcessingScore(gameData);
  }
  calculateAuditoryProcessingScore(gameData) {
    const auditoryTasks = gameData.auditoryTasks || 0;
    const auditoryAccuracy = gameData.auditoryAccuracy || 50;
    let score = 50;
    if (auditoryTasks > 0) {
      score = auditoryAccuracy;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateTactileProcessingScore(gameData) {
    const touchInteractions = gameData.touchInteractions || 0;
    const touchAccuracy = gameData.touchAccuracy || 50;
    let score = 50;
    if (touchInteractions > 3) {
      score = touchAccuracy;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateSensoryIntegrationScore(gameData) {
    const visual = this.calculateVisualPerceptionScore(gameData);
    const auditory = this.calculateAuditoryProcessingScore(gameData);
    const tactile = this.calculateTactileProcessingScore(gameData);
    return (visual + auditory + tactile) / 3;
  }
  calculateFineMotorSkillsScore(gameData) {
    const precision = gameData.precision || 50;
    const motorControl = gameData.motorControl || 50;
    return (precision + motorControl) / 2;
  }
  calculateGrossMotorSkillsScore(gameData) {
    const movements = gameData.movements || 0;
    const coordination = gameData.coordination || 50;
    let score = 50;
    if (movements > 10) {
      score = coordination;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateCoordinationScore(gameData) {
    const eyeHandCoordination = gameData.eyeHandCoordination || 50;
    const bilateralCoordination = gameData.bilateralCoordination || 50;
    return (eyeHandCoordination + bilateralCoordination) / 2;
  }
  calculateMotorPlanningScore(gameData) {
    const planningSteps = gameData.planningSteps || 0;
    const executionSuccess = gameData.executionSuccess || 0;
    let score = 50;
    if (planningSteps > 0) {
      score = executionSuccess;
    }
    return Math.max(0, Math.min(100, score));
  }
  generateTherapeuticRecommendations(metrics, gameData) {
    const recommendations = [];
    const engagement = this.calculateEngagementScore(gameData);
    if (engagement < 50) {
      recommendations.push({
        category: "engagement",
        priority: "high",
        recommendation: "Implementar estratégias de motivação e gamificação",
        rationale: "Baixo engajamento detectado"
      });
    }
    const attention = this.calculateAttentionScore(gameData);
    if (attention < 50) {
      recommendations.push({
        category: "attention",
        priority: "medium",
        recommendation: "Exercícios de foco e concentração",
        rationale: "Dificuldades atencionais identificadas"
      });
    }
    const processing = this.calculateProcessingSpeedScore(gameData);
    if (processing < 50) {
      recommendations.push({
        category: "processing",
        priority: "medium",
        recommendation: "Atividades para melhorar velocidade de processamento",
        rationale: "Processamento lento identificado"
      });
    }
    return recommendations;
  }
  generateProgressIndicators(metrics, gameData) {
    return {
      overallProgress: this.calculateOverallProgress(gameData),
      strengthAreas: this.identifyStrengthAreas(gameData),
      challengeAreas: this.identifyChallengeAreas(gameData),
      developmentGoals: this.generateDevelopmentGoals(gameData),
      milestones: this.generateMilestones(gameData)
    };
  }
  generateGameSpecificInsights(metrics, gameData) {
    return {
      gameType: this.gameType,
      specificMetrics: metrics,
      gamePerformance: this.calculateGamePerformance(gameData),
      adaptationNeeds: this.identifyAdaptationNeeds(gameData)
    };
  }
  calculateAnalysisConfidenceScore(metrics, gameData) {
    let confidence = 50;
    const dataPoints = Object.keys(gameData).length;
    if (dataPoints > 10) confidence += 20;
    else if (dataPoints > 5) confidence += 10;
    const metricsCount = Object.keys(metrics).length;
    if (metricsCount > 5) confidence += 20;
    else if (metricsCount > 3) confidence += 10;
    const sessionTime = gameData.totalTime || 0;
    if (sessionTime > 6e4) confidence += 10;
    return Math.max(0, Math.min(100, confidence));
  }
  generateFallbackTherapeuticAnalysis(gameData) {
    return {
      behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50, socialInteraction: 50 },
      cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50, visualProcessing: 50 },
      sensory: { visualPerception: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },
      motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },
      recommendations: [],
      progressIndicators: { overallProgress: 50, strengthAreas: [], challengeAreas: [], developmentGoals: [], milestones: [] },
      gameSpecificInsights: { gameType: this.gameType, specificMetrics: {}, gamePerformance: 50, adaptationNeeds: [] },
      metadata: { analysisTimestamp: (/* @__PURE__ */ new Date()).toISOString(), gameType: this.gameType, analysisVersion: "3.0.0", confidenceScore: 30 }
    };
  }
  calculateOverallProgress(gameData) {
    const accuracy = gameData.accuracy || 0;
    const completion = gameData.completion || 0;
    const engagement = this.calculateEngagementScore(gameData);
    return (accuracy + completion + engagement) / 3;
  }
  identifyStrengthAreas(gameData) {
    const strengths = [];
    if (gameData.accuracy > 80) strengths.push("Precisão");
    if (gameData.averageResponseTime < 2e3) strengths.push("Velocidade de resposta");
    if (this.calculateEngagementScore(gameData) > 70) strengths.push("Engajamento");
    return strengths;
  }
  identifyChallengeAreas(gameData) {
    const challenges = [];
    if (gameData.accuracy < 50) challenges.push("Precisão");
    if (gameData.averageResponseTime > 4e3) challenges.push("Velocidade de resposta");
    if (this.calculateEngagementScore(gameData) < 40) challenges.push("Engajamento");
    return challenges;
  }
  generateDevelopmentGoals(gameData) {
    const goals = [];
    if (gameData.accuracy < 70) {
      goals.push("Melhorar precisão para 70%+");
    }
    if (gameData.averageResponseTime > 3e3) {
      goals.push("Reduzir tempo de resposta para menos de 3 segundos");
    }
    return goals;
  }
  generateMilestones(gameData) {
    return [
      { milestone: "Primeira sessão completa", achieved: gameData.completion > 0.8 },
      { milestone: "Precisão acima de 50%", achieved: gameData.accuracy > 50 },
      { milestone: "Engajamento sustentado", achieved: this.calculateEngagementScore(gameData) > 60 }
    ];
  }
  calculateGamePerformance(gameData) {
    const accuracy = gameData.accuracy || 0;
    const completion = gameData.completion || 0;
    const efficiency = gameData.efficiency || 0;
    return (accuracy + completion + efficiency) / 3;
  }
  identifyAdaptationNeeds(gameData) {
    const needs = [];
    if (gameData.accuracy < 40) {
      needs.push("Reduzir dificuldade");
    }
    if (gameData.averageResponseTime > 5e3) {
      needs.push("Aumentar tempo limite");
    }
    if (this.calculateEngagementScore(gameData) < 30) {
      needs.push("Aumentar elementos motivacionais");
    }
    return needs;
  }
  /**
   * Processa métricas para estrutura padronizada do banco de dados
   * @param {Object} metrics - Métricas processadas
   * @param {Object} gameData - Dados do jogo
   * @returns {Object} Métricas processadas para banco
   */
  processMetricsForDatabase(metrics, gameData) {
    try {
      return {
        // Métricas básicas
        basic: {
          accuracy: gameData.accuracy || 0,
          responseTime: gameData.averageResponseTime || 0,
          completion: gameData.completion || 0,
          score: gameData.score || 0,
          duration: gameData.totalTime || 0,
          attempts: gameData.attempts || 1,
          errors: gameData.errors || 0
        },
        // Métricas cognitivas
        cognitive: {
          attention: this.calculateAttentionScore(gameData),
          memory: this.calculateMemoryScore(gameData),
          processingSpeed: this.calculateProcessingSpeedScore(gameData),
          executiveFunction: this.calculateExecutiveFunctionScore(gameData)
        },
        // Métricas comportamentais
        behavioral: {
          engagement: this.calculateEngagementScore(gameData),
          persistence: this.calculatePersistenceScore(gameData),
          adaptability: this.calculateAdaptabilityScore(gameData),
          frustrationTolerance: this.calculateFrustrationTolerance(gameData)
        },
        // Métricas sensoriais
        sensory: {
          visualProcessing: this.calculateVisualProcessingScore(gameData),
          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),
          tactileProcessing: this.calculateTactileProcessingScore(gameData),
          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)
        },
        // Métricas motoras
        motor: {
          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),
          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),
          coordination: this.calculateCoordinationScore(gameData),
          motorPlanning: this.calculateMotorPlanningScore(gameData)
        },
        // Métricas específicas do jogo
        gameSpecific: metrics,
        // Metadados
        metadata: {
          gameType: this.gameType,
          processingTimestamp: (/* @__PURE__ */ new Date()).toISOString(),
          version: "3.0.0"
        }
      };
    } catch (error) {
      this.logger?.error("❌ Erro ao processar métricas para banco:", error);
      return {
        basic: { accuracy: 0, responseTime: 0, completion: 0, score: 0, duration: 0, attempts: 1, errors: 0 },
        cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50 },
        behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50 },
        sensory: { visualProcessing: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },
        motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },
        gameSpecific: metrics,
        metadata: { gameType: this.gameType, processingTimestamp: (/* @__PURE__ */ new Date()).toISOString(), version: "3.0.0" }
      };
    }
  }
  /**
   * Métodos de análise para PadroesVisuais
   */
  analyzePadroesVisuaisPrimary(gameData) {
    const { totalCorrect = 0, totalAttempts = 1, interactions = [] } = gameData;
    return {
      accuracy: Math.round(totalCorrect / totalAttempts * 100),
      totalAttempts,
      totalCorrect,
      primaryScore: this.calculatePrimaryScore(interactions),
      efficiency: this.calculateEfficiency(interactions)
    };
  }
  analyzePadroesVisuaisSecondary(gameData) {
    const { interactions = [] } = gameData;
    return {
      secondaryAccuracy: this.calculateSecondaryAccuracy(interactions),
      adaptability: this.calculateAdaptability(interactions),
      consistency: this.calculateConsistency(interactions)
    };
  }
  analyzePadroesVisuaisTiming(gameData) {
    const { interactions = [] } = gameData;
    const responseTimes = interactions.map((i) => i.responseTime || 0).filter((t) => t > 0);
    if (responseTimes.length === 0) {
      return { average: 0, median: 0, variability: 0, pattern: "insufficient_data" };
    }
    const average = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const sorted = responseTimes.sort((a, b) => a - b);
    const median = sorted[Math.floor(sorted.length / 2)];
    return {
      average: Math.round(average),
      median: Math.round(median),
      min: Math.min(...responseTimes),
      max: Math.max(...responseTimes),
      variability: Math.round(this.calculateVariability(responseTimes)),
      pattern: "normal"
    };
  }
  analyzePadroesVisuaisPatterns(gameData) {
    const { interactions = [] } = gameData;
    return {
      totalPatterns: interactions.length,
      correctPatterns: interactions.filter((i) => i.correct).length,
      patternTypes: this.identifyPatternTypes(interactions),
      errorPatterns: this.identifyErrorPatterns(interactions)
    };
  }
  analyzePadroesVisuaisBehavior(gameData) {
    const { interactions = [] } = gameData;
    return {
      persistence: this.calculatePersistence(interactions),
      adaptability: this.calculateAdaptability(interactions),
      engagement: this.calculateEngagementScore(gameData)
    };
  }
  analyzePadroesVisuaisCognition(gameData) {
    const { interactions = [] } = gameData;
    return {
      executiveFunction: this.calculateExecutiveFunction(interactions),
      workingMemory: this.calculateWorkingMemory(interactions),
      processingSpeed: this.calculateProcessingSpeed(interactions)
    };
  }
  // Métodos auxiliares
  calculatePrimaryScore(interactions) {
    const correctInteractions = interactions.filter((i) => i.correct);
    return correctInteractions.length / Math.max(1, interactions.length) * 100;
  }
  calculateSecondaryAccuracy(interactions) {
    return this.calculatePrimaryScore(interactions);
  }
  calculateEfficiency(interactions) {
    const totalTime = interactions.reduce((sum, i) => sum + (i.responseTime || 0), 0);
    const correctCount = interactions.filter((i) => i.correct).length;
    return totalTime > 0 ? correctCount / totalTime * 1e3 : 0;
  }
  identifyPatternTypes(interactions) {
    const types = {};
    interactions.forEach((i) => {
      const type = i.patternType || "unknown";
      types[type] = (types[type] || 0) + 1;
    });
    return types;
  }
  identifyErrorPatterns(interactions) {
    const errors = interactions.filter((i) => !i.correct);
    return {
      totalErrors: errors.length,
      errorFrequency: errors.length / Math.max(1, interactions.length) * 100
    };
  }
  calculateVariability(values) {
    if (values.length < 2) return 0;
    const mean = values.reduce((sum, v) => sum + v, 0) / values.length;
    const variance = values.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }
  // Métodos auxiliares completos para todos os processadores
  calculateAdaptability(interactions) {
    if (interactions.length < 2) return 50;
    let adaptations = 0;
    for (let i = 1; i < interactions.length; i++) {
      if (!interactions[i - 1].correct && interactions[i].correct) {
        adaptations++;
      }
    }
    return adaptations / Math.max(1, interactions.length - 1) * 100;
  }
  calculatePersistence(interactions) {
    if (interactions.length === 0) return 50;
    const errorRate = interactions.filter((i) => !i.correct).length / interactions.length;
    return Math.max(0, 100 - errorRate * 100);
  }
  calculateConsistency(interactions) {
    if (interactions.length === 0) return 50;
    const responseTimes = interactions.map((i) => i.responseTime || 0);
    const avgTime = responseTimes.reduce((sum, t) => sum + t, 0) / responseTimes.length;
    if (avgTime === 0) return 50;
    const variance = responseTimes.reduce((sum, t) => sum + Math.pow(t - avgTime, 2), 0) / responseTimes.length;
    const stdDev = Math.sqrt(variance);
    return Math.max(0, 100 - stdDev / avgTime * 100);
  }
  calculateConsistencyScore(interactions) {
    return this.calculateConsistency(interactions);
  }
  calculateAccuracyTrend(interactions) {
    if (interactions.length < 2) return "insufficient_data";
    const halfPoint = Math.floor(interactions.length / 2);
    const firstHalf = interactions.slice(0, halfPoint);
    const secondHalf = interactions.slice(halfPoint);
    const firstAccuracy = firstHalf.filter((i) => i.correct).length / firstHalf.length;
    const secondAccuracy = secondHalf.filter((i) => i.correct).length / secondHalf.length;
    if (secondAccuracy > firstAccuracy + 0.1) return "improving";
    if (secondAccuracy < firstAccuracy - 0.1) return "declining";
    return "stable";
  }
  calculateSpeedImprovement(interactions) {
    if (interactions.length < 2) return 0;
    const halfPoint = Math.floor(interactions.length / 2);
    const firstHalf = interactions.slice(0, halfPoint);
    const secondHalf = interactions.slice(halfPoint);
    const firstAvgTime = firstHalf.reduce((sum, i) => sum + (i.responseTime || 0), 0) / firstHalf.length;
    const secondAvgTime = secondHalf.reduce((sum, i) => sum + (i.responseTime || 0), 0) / secondHalf.length;
    return firstAvgTime > 0 ? (firstAvgTime - secondAvgTime) / firstAvgTime * 100 : 0;
  }
  calculateLearningRate(interactions) {
    if (interactions.length < 3) return 0;
    let improvementCount = 0;
    for (let i = 1; i < interactions.length; i++) {
      if (interactions[i].correct && !interactions[i - 1].correct) {
        improvementCount++;
      }
    }
    return improvementCount / (interactions.length - 1) * 100;
  }
  identifyResponsePattern(responseTimes) {
    if (responseTimes.length === 0) return "insufficient_data";
    const average = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const variance = responseTimes.reduce((sum, time) => sum + Math.pow(time - average, 2), 0) / responseTimes.length;
    const stdDev = Math.sqrt(variance);
    if (stdDev < average * 0.2) return "consistent";
    if (stdDev < average * 0.5) return "moderate_variation";
    return "high_variation";
  }
  generateProgressMarkers(gameData) {
    const { interactions = [] } = gameData;
    return {
      accuracyTrend: this.calculateAccuracyTrend(interactions),
      speedImprovement: this.calculateSpeedImprovement(interactions),
      consistencyScore: this.calculateConsistencyScore(interactions),
      learningRate: this.calculateLearningRate(interactions)
    };
  }
  // Métodos específicos para processamento de métricas
  /**
   * Processa métricas específicas do PadroesVisuais
   * @param {Object} gameData - Dados do jogo PadroesVisuais
   * @param {Object} sessionData - Dados da sessão
   * @returns {Object} Métricas processadas
   */
  async processPadroesVisuaisMetrics(gameData, sessionData) {
    try {
      this.logger?.info("👁️ Processando métricas PadroesVisuais...", {
        sessionId: sessionData.sessionId
      });
      const metrics = {
        // Métricas de reconhecimento de padrões
        patternRecognition: this.analyzePadroesVisuaisPrimary(gameData),
        // Análise de processamento visual
        visualProcessing: this.analyzePadroesVisuaisSecondary(gameData),
        // Raciocínio espacial
        spatialReasoning: this.analyzePadroesVisuaisTertiary(gameData),
        // Padrões visuais
        visualPatterns: this.analyzePadroesVisuaisPatterns(gameData),
        // Análise comportamental específica
        visualBehavior: this.analyzePadroesVisuaisBehavior(gameData),
        // Indicadores terapêuticos
        therapeuticIndicators: this.generatePadroesVisuaisTherapeuticIndicators(gameData),
        // Recomendações específicas
        recommendations: this.generatePadroesVisuaisRecommendations(gameData)
      };
      this.logger?.info("✅ Métricas PadroesVisuais processadas", {
        accuracy: metrics.patternRecognition.accuracy,
        complexity: metrics.visualPatterns.complexity
      });
      return metrics;
    } catch (error) {
      this.logger?.error("❌ Erro ao processar métricas PadroesVisuais:", error);
      throw error;
    }
  }
  /**
   * Gera indicadores terapêuticos específicos para PadroesVisuais
   */
  generatePadroesVisuaisTherapeuticIndicators(gameData) {
    const { interactions = [], accuracy = 0 } = gameData;
    return {
      cognitiveLoad: this.assessCognitiveLoad(gameData),
      therapeuticGoals: this.identifyPadroesVisuaisTherapeuticGoals(gameData),
      interventionNeeds: this.identifyPadroesVisuaisInterventionNeeds(gameData),
      progressMarkers: this.generatePadroesVisuaisProgressMarkers(gameData)
    };
  }
  /**
   * Identifica objetivos terapêuticos específicos
   */
  identifyPadroesVis;
  /**
   * Identifica necessidades de intervenção específicas
   */
  identifyPadroesVisuaisInterventionNeeds(gameData) {
    const { interactions = [], accuracy = 0 } = gameData;
    const needs = [];
    if (accuracy < 50) {
      needs.push("Intervenção intensiva em processamento visual");
    }
    return needs;
  }
  /**
   * Gera marcadores de progresso específicos
   */
  generatePadroesVisuaisProgressMarkers(gameData) {
    const { interactions = [] } = gameData;
    return {
      accuracyTrend: this.calculateAccuracyTrend(interactions),
      speedImprovement: this.calculateSpeedImprovement(interactions),
      consistencyScore: this.calculateConsistencyScore(interactions),
      learningRate: this.calculateLearningRate(interactions)
    };
  }
  /**
   * Avalia carga cognitiva
   */
  assessCognitiveLoad(gameData) {
    const { interactions = [], averageResponseTime = 0 } = gameData;
    if (interactions.length === 0) return "low";
    const avgTime = averageResponseTime || interactions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / interactions.length;
    const errorRate = interactions.filter((i) => !i.correct).length / interactions.length;
    if (avgTime > 6e3 || errorRate > 0.7) return "high";
    if (avgTime > 4e3 || errorRate > 0.4) return "medium";
    return "low";
  }
  /**
   * Análise terciária do PadroesVisuais
   */
  analyzePadroesVisuaisTertiary(gameData) {
    const { interactions = [] } = gameData;
    return {
      spatialReasoning: this.calculateSpatialReasoning(interactions),
      visualAnalysis: this.calculateVisualAnalysis(interactions),
      patternComplexity: this.calculatePatternComplexity(interactions)
    };
  }
  calculateSpatialReasoning(interactions) {
    if (interactions.length === 0) return 50;
    const correctInteractions = interactions.filter((i) => i.correct);
    return correctInteractions.length / interactions.length * 100;
  }
  calculateVisualAnalysis(interactions) {
    if (interactions.length === 0) return 50;
    const times = interactions.map((i) => i.responseTime || 0).filter((t) => t > 0);
    if (times.length === 0) return 50;
    const avgTime = times.reduce((sum, t) => sum + t, 0) / times.length;
    return Math.max(0, Math.min(100, 100 - avgTime / 60));
  }
  calculatePatternComplexity(interactions) {
    if (interactions.length === 0) return 50;
    const complexPatterns = interactions.filter((i) => i.correct && i.difficulty === "hard");
    return complexPatterns.length > 0 ? complexPatterns.length / interactions.length * 100 : 50;
  }
}
const ACTIVITY_CONFIG = {
  reproducao_sequencias: {
    name: "Reprodução de Sequências",
    icon: "🔄",
    description: "Reproduza a sequência de padrões apresentada",
    minTime: 5e3,
    maxTime: 3e4,
    successCriteria: { accuracy: 0.8 },
    therapeuticFocus: ["memória_sequencial", "atenção_sustentada"]
  },
  completar_padroes: {
    name: "Completar Padrões",
    icon: "🧩",
    description: "Complete o padrão visual seguindo a lógica",
    minTime: 3e3,
    maxTime: 25e3,
    successCriteria: { accuracy: 0.75 },
    therapeuticFocus: ["raciocínio_lógico", "reconhecimento_padrões"]
  },
  construcao_padroes: {
    name: "Construção de Padrões",
    icon: "🔨",
    description: "Construa padrões seguindo as regras",
    minTime: 8e3,
    maxTime: 45e3,
    successCriteria: { accuracy: 0.7 },
    therapeuticFocus: ["criatividade_espacial", "planejamento"]
  },
  classificacao_visual: {
    name: "Classificação Visual",
    icon: "📊",
    description: "Agrupe elementos por características",
    minTime: 4e3,
    maxTime: 2e4,
    successCriteria: { accuracy: 0.85 },
    therapeuticFocus: ["flexibilidade_cognitiva", "categorização"]
  },
  transformacao_padroes: {
    name: "Transformação de Padrões",
    icon: "🔄",
    description: "Aplique transformações nos padrões",
    minTime: 6e3,
    maxTime: 35e3,
    successCriteria: { accuracy: 0.7 },
    therapeuticFocus: ["visualização_espacial", "raciocínio_abstrato"]
  },
  deteccao_anomalias: {
    name: "Detecção de Anomalias",
    icon: "🔍",
    description: "Encontre elementos que não seguem o padrão",
    minTime: 3e3,
    maxTime: 15e3,
    successCriteria: { accuracy: 0.9 },
    therapeuticFocus: ["atenção_detalhes", "controle_inibitório"]
  }
};
const PadroesVisuaisConfig = {
  // Formas disponíveis no jogo (mantido para compatibilidade)
  shapes: [
    { id: "star", name: "Estrela", emoji: "⭐", color: "#FFD93D" },
    { id: "circle", name: "Círculo", emoji: "🟢", color: "#4ECDC4" },
    { id: "triangle", name: "Triângulo", emoji: "🔺", color: "#45B7D1" },
    { id: "square", name: "Quadrado", emoji: "🟥", color: "#FF6B6B" },
    { id: "diamond", name: "Diamante", emoji: "🔷", color: "#A8E6CF" },
    { id: "heart", name: "Coração", emoji: "❤️", color: "#FF8B94" }
  ],
  // Níveis de dificuldade
  difficulties: [
    { id: "easy", name: "Fácil", sequenceLength: 3, maxLevel: 5, showTime: 9e3 },
    { id: "medium", name: "Médio", sequenceLength: 4, maxLevel: 7, showTime: 8e3 },
    { id: "hard", name: "Difícil", sequenceLength: 5, maxLevel: 10, showTime: 7e3 }
  ],
  // Mensagens de encorajamento
  encouragingMessages: [
    "Muito bem! Você tem um ótimo olho para padrões! 👀",
    "Excelente! Continue assim! 🎉",
    "Perfeito! Você está indo muito bem! ✨",
    "Fantástico! Sua memória está ótima! 🧠",
    "Incrível! Você domina os padrões visuais! 🌟"
  ]
};
const PadroesVisuaisMetrics = {
  // Registrar início do jogo
  startGame: (difficulty) => {
    const metrics = {
      gameId: "padroes-visuais",
      sessionId: `patterns_${Date.now()}`,
      startTime: (/* @__PURE__ */ new Date()).toISOString(),
      difficulty,
      userId: "anonymous",
      device: navigator.userAgent
    };
    console.log("Padrões Visuais Game Started:", metrics);
    return metrics;
  },
  // Registrar tentativa de sequência
  recordSequenceAttempt: (attempt) => {
    const metrics = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      level: attempt.level,
      sequenceLength: attempt.sequenceLength,
      targetSequence: attempt.targetSequence,
      playerSequence: attempt.playerSequence,
      isCorrect: attempt.isCorrect,
      responseTime: attempt.responseTime,
      difficulty: attempt.difficulty,
      errors: attempt.errors || 0,
      partialCorrect: attempt.partialCorrect,
      // quantas formas estavam corretas antes do erro
      shapesMatched: attempt.shapesMatched
    };
    console.log("Padrões Visuais Attempt:", metrics);
    return metrics;
  },
  // Registrar clique em forma individual
  recordShapeClick: (shapeData) => {
    const metrics = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      action: "shape_clicked",
      shapeId: shapeData.shapeId,
      shapeName: shapeData.shapeName,
      sequencePosition: shapeData.sequencePosition,
      isCorrectPosition: shapeData.isCorrectPosition,
      isCorrectShape: shapeData.isCorrectShape,
      level: shapeData.level
    };
    console.log("Padrões Visuais Shape Click:", metrics);
    return metrics;
  },
  // Registrar repetição de sequência
  recordSequenceRepeat: (repeatData) => {
    const metrics = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      action: "sequence_repeated",
      level: repeatData.level,
      sequenceLength: repeatData.sequenceLength,
      attempt: repeatData.attempt
    };
    console.log("Padrões Visuais Sequence Repeated:", metrics);
    return metrics;
  },
  // Registrar progressão de nível
  recordLevelProgression: (progressData) => {
    const metrics = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      action: "level_completed",
      completedLevel: progressData.completedLevel,
      nextLevel: progressData.nextLevel,
      difficulty: progressData.difficulty,
      attemptsInLevel: progressData.attemptsInLevel,
      timeInLevel: progressData.timeInLevel
    };
    console.log("Padrões Visuais Level Progression:", metrics);
    return metrics;
  },
  // Registrar final do jogo
  endGame: (gameData) => {
    const metrics = {
      sessionId: gameData.sessionId,
      endTime: (/* @__PURE__ */ new Date()).toISOString(),
      totalTime: gameData.totalTime,
      totalLevels: gameData.totalLevels,
      correctSequences: gameData.correctSequences,
      totalScore: gameData.totalScore,
      finalLevel: gameData.finalLevel,
      accuracy: gameData.accuracy,
      averageSequenceLength: gameData.averageSequenceLength,
      difficulty: gameData.difficulty,
      sequenceRepeats: gameData.sequenceRepeats || 0,
      completed: gameData.completed
    };
    console.log("Padrões Visuais Game Ended:", metrics);
    return metrics;
  },
  // Calcular estatísticas do jogo
  calculateStats: (attempts) => {
    if (!attempts || attempts.length === 0) {
      return {
        accuracy: 0,
        averageSequenceLength: 0,
        totalAttempts: 0,
        correctSequences: 0
      };
    }
    const correctAttempts = attempts.filter((attempt) => attempt.isCorrect);
    const totalSequenceLength = attempts.reduce((sum, attempt) => sum + attempt.sequenceLength, 0);
    return {
      accuracy: correctAttempts.length / attempts.length * 100,
      averageSequenceLength: totalSequenceLength / attempts.length,
      totalAttempts: attempts.length,
      correctSequences: correctAttempts.length,
      longestSequence: Math.max(...attempts.map((a) => a.sequenceLength)),
      memorySpan: calculateMemorySpan(attempts),
      patternRecognition: calculatePatternRecognition(attempts),
      visualMemoryGrowth: calculateVisualMemoryGrowth(attempts)
    };
  },
  // Analisar padrões de memória visual
  analyzeVisualPatterns: (attempts, shapeClicks) => {
    const errorsByPosition = attempts.filter((attempt) => !attempt.isCorrect).reduce((acc, error) => {
      const errorPosition = error.partialCorrect || 0;
      acc[errorPosition] = (acc[errorPosition] || 0) + 1;
      return acc;
    }, {});
    const shapePreferences = shapeClicks.reduce((acc, click) => {
      acc[click.shapeId] = (acc[click.shapeId] || 0) + 1;
      return acc;
    }, {});
    const shapeAccuracy = {};
    const shapesUsed = [...new Set(shapeClicks.map((c) => c.shapeId))];
    shapesUsed.forEach((shapeId) => {
      const shapeClicks2 = shapeClicks2.filter((c) => c.shapeId === shapeId);
      const shapeCorrect = shapeClicks2.filter((c) => c.isCorrectShape).length;
      shapeAccuracy[shapeId] = shapeCorrect / shapeClicks2.length * 100;
    });
    return {
      errorsByPosition,
      shapePreferences,
      shapeAccuracy,
      difficultShapes: getDifficultShapes(shapeAccuracy),
      suggestions: generateVisualSuggestions(attempts, errorsByPosition, shapeAccuracy)
    };
  }
};
function calculateMemorySpan(attempts) {
  const correctSequences = attempts.filter((a) => a.isCorrect);
  if (correctSequences.length === 0) return 0;
  let maxSpan = 0;
  let currentSpan = 0;
  attempts.forEach((attempt) => {
    if (attempt.isCorrect) {
      currentSpan = Math.max(currentSpan, attempt.sequenceLength);
    } else {
      maxSpan = Math.max(maxSpan, currentSpan);
      currentSpan = 0;
    }
  });
  return Math.max(maxSpan, currentSpan);
}
function calculatePatternRecognition(attempts) {
  if (attempts.length < 5) return 0;
  const firstAttempts = attempts.slice(0, Math.floor(attempts.length / 3));
  const lastAttempts = attempts.slice(-Math.floor(attempts.length / 3));
  const firstAvgTime = firstAttempts.reduce((sum, a) => sum + a.responseTime, 0) / firstAttempts.length;
  const lastAvgTime = lastAttempts.reduce((sum, a) => sum + a.responseTime, 0) / lastAttempts.length;
  return (firstAvgTime - lastAvgTime) / firstAvgTime * 100;
}
function calculateVisualMemoryGrowth(attempts) {
  if (attempts.length < 6) return 0;
  const firstHalf = attempts.slice(0, Math.floor(attempts.length / 2));
  const secondHalf = attempts.slice(Math.floor(attempts.length / 2));
  const firstAccuracy = firstHalf.filter((a) => a.isCorrect).length / firstHalf.length;
  const secondAccuracy = secondHalf.filter((a) => a.isCorrect).length / secondHalf.length;
  return (secondAccuracy - firstAccuracy) / firstAccuracy * 100;
}
function getDifficultShapes(shapeAccuracy) {
  return Object.entries(shapeAccuracy).filter(([, accuracy]) => accuracy < 60).sort(([, a], [, b]) => a - b).slice(0, 3).map(([shape, accuracy]) => ({ shape, accuracy }));
}
function generateVisualSuggestions(attempts, errorsByPosition, shapeAccuracy) {
  const suggestions = [];
  if (attempts.length === 0) {
    suggestions.push("Comece jogando para desenvolver sua memória visual!");
    return suggestions;
  }
  const accuracy = attempts.filter((a) => a.isCorrect).length / attempts.length;
  if (accuracy < 0.4) {
    suggestions.push("Foque bem na primeira visualização da sequência");
    suggestions.push('Use a função "Repetir Sequência" sempre que precisar');
  } else if (accuracy < 0.7) {
    suggestions.push("Tente criar uma história visual com as formas para lembrar melhor");
    suggestions.push("Observe bem as cores e formas de cada elemento");
  } else if (accuracy > 0.9) {
    suggestions.push("Excelente memória visual! Tente sequências mais longas");
    suggestions.push("Você está pronto para níveis mais desafiadores");
  }
  const mostErrorPosition = Object.entries(errorsByPosition).sort(([, a], [, b]) => b - a)[0];
  if (mostErrorPosition) {
    const position = parseInt(mostErrorPosition[0]);
    if (position === 0) {
      suggestions.push("Preste mais atenção à primeira forma da sequência");
    } else if (position === 1) {
      suggestions.push("A segunda forma é crucial - foque nela após ver a primeira");
    } else if (position >= 3) {
      suggestions.push("Sua memória de curto prazo está boa, continue praticando sequências longas");
    }
  }
  const difficultShapes = getDifficultShapes(shapeAccuracy);
  if (difficultShapes.length > 0) {
    suggestions.push(`Pratique mais com as formas: ${difficultShapes.map((s) => s.shape).join(", ")}`);
  }
  return suggestions.length > 0 ? suggestions : ["Continue praticando para fortalecer sua memória visual!"];
}
const padroesVisuaisGame = "_padroesVisuaisGame_19evq_51";
const gameContent = "_gameContent_19evq_73";
const gameHeader = "_gameHeader_19evq_93";
const gameTitle = "_gameTitle_19evq_135";
const headerTtsButton = "_headerTtsButton_19evq_189";
const ttsActive = "_ttsActive_19evq_243";
const speaking = "_speaking_19evq_255";
const active = "_active_19evq_299";
const gameStats = "_gameStats_19evq_311";
const statCard = "_statCard_19evq_325";
const statValue = "_statValue_19evq_383";
const statLabel = "_statLabel_19evq_401";
const questionArea = "_questionArea_19evq_451";
const questionHeader = "_questionHeader_19evq_491";
const questionTitle = "_questionTitle_19evq_499";
const objectsDisplay = "_objectsDisplay_19evq_555";
const answerOptions = "_answerOptions_19evq_691";
const selected = "_selected_19evq_807";
const correct = "_correct_19evq_819";
const incorrect = "_incorrect_19evq_831";
const answerButton = "_answerButton_19evq_845";
const gameControls = "_gameControls_19evq_1009";
const controlButton = "_controlButton_19evq_1027";
const activityMenu = "_activityMenu_19evq_3199";
const activityButton = "_activityButton_19evq_3217";
const styles = {
  padroesVisuaisGame,
  gameContent,
  gameHeader,
  gameTitle,
  headerTtsButton,
  ttsActive,
  speaking,
  active,
  gameStats,
  statCard,
  statValue,
  statLabel,
  questionArea,
  questionHeader,
  questionTitle,
  objectsDisplay,
  answerOptions,
  selected,
  correct,
  incorrect,
  answerButton,
  gameControls,
  controlButton,
  activityMenu,
  activityButton
};
const ACTIVITY_TYPES = {
  SEQUENCE_PATTERNS: {
    id: "sequence_patterns",
    name: "Sequenciais",
    icon: "🔄",
    description: "Teste de reconhecimento de sequências visuais",
    cognitiveFunction: "sequential_pattern_recognition",
    component: "SequencePatternsActivity"
  },
  GEOMETRIC_PATTERNS: {
    id: "geometric_patterns",
    name: "Geométricos",
    icon: "🔺",
    description: "Teste de reconhecimento de formas e geometria",
    cognitiveFunction: "geometric_spatial_recognition",
    component: "GeometricPatternsActivity"
  },
  COLOR_PATTERNS: {
    id: "color_patterns",
    name: "Cores",
    icon: "🌈",
    description: "Teste de reconhecimento de padrões cromáticos",
    cognitiveFunction: "chromatic_pattern_recognition",
    component: "ColorPatternsActivity"
  },
  SYMMETRY_PATTERNS: {
    id: "symmetry_patterns",
    name: "Simetria",
    icon: "⚖️",
    description: "Teste de reconhecimento de simetria e reflexão",
    cognitiveFunction: "symmetry_spatial_processing",
    component: "SymmetryPatternsActivity"
  },
  COMPLETION_PATTERNS: {
    id: "completion_patterns",
    name: "Completar",
    icon: "🧩",
    description: "Teste de completar padrões visuais complexos",
    cognitiveFunction: "pattern_completion_inference",
    component: "CompletionPatternsActivity"
  }
};
function PadroesVisuaisGame({ onBack }) {
  const { user, ttsEnabled = true } = reactExports.useContext(SystemContext);
  const { settings } = useAccessibilityContext();
  const [ttsActive2, setTtsActive] = reactExports.useState(true);
  const metricsRef = reactExports.useRef(null);
  const collectorsHub = reactExports.useMemo(() => new PadroesVisuaisCollectorsHub(), []);
  const [gameState, setGameState] = reactExports.useState({
    status: "start",
    // 'start', 'playing', 'paused', 'finished'
    score: 0,
    round: 1,
    totalRounds: 10,
    difficulty: "easy",
    accuracy: 100,
    roundStartTime: null,
    // 🎯 Sistema de atividades redesenhado (5 atividades distintas)
    currentActivity: ACTIVITY_TYPES.SEQUENCE_PATTERNS.id,
    activityCycle: [
      ACTIVITY_TYPES.SEQUENCE_PATTERNS.id,
      ACTIVITY_TYPES.GEOMETRIC_PATTERNS.id,
      ACTIVITY_TYPES.COLOR_PATTERNS.id,
      ACTIVITY_TYPES.SYMMETRY_PATTERNS.id,
      ACTIVITY_TYPES.COMPLETION_PATTERNS.id
    ],
    activityIndex: 0,
    roundsPerActivity: 5,
    // Mínimo 5 rodadas por nível
    activityRoundCount: 0,
    activitiesCompleted: [],
    // 🎯 Dados específicos de atividades
    activityData: {
      sequenceReproduction: {
        sequence: [],
        userSequence: [],
        showingSequence: false
      },
      patternCompletion: {
        pattern: [],
        missingElements: [],
        userAnswers: []
      },
      patternConstruction: {
        rules: null,
        availableElements: [],
        constructedPattern: []
      },
      visualClassification: {
        items: [],
        categories: [],
        userClassification: {}
      },
      patternTransformation: {
        originalPattern: [],
        transformationRule: null,
        userResult: []
      },
      anomalyDetection: {
        pattern: [],
        anomalies: [],
        foundAnomalies: []
      }
    },
    // 🎯 Feedback e animações
    showFeedback: false,
    feedbackType: null,
    feedbackMessage: "",
    showCelebration: false,
    // 🎯 Métricas comportamentais
    responseTime: 0,
    hesitationCount: 0,
    helpUsed: false,
    consecutiveCorrect: 0,
    totalAttempts: 0,
    correctAttempts: 0
  });
  const [sequenceToReproduce, setSequenceToReproduce] = reactExports.useState([]);
  const [patternToComplete, setPatternToComplete] = reactExports.useState(null);
  const [constructionRules, setConstructionRules] = reactExports.useState(null);
  const [classificationCriteria, setClassificationCriteria] = reactExports.useState(null);
  const [transformationTarget, setTransformationTarget] = reactExports.useState(null);
  const [anomalyPattern, setAnomalyPattern] = reactExports.useState(null);
  const [playerConstruction, setPlayerConstruction] = reactExports.useState([]);
  const [selectedTransformation, setSelectedTransformation] = reactExports.useState(null);
  const [classificationGroups, setClassificationGroups] = reactExports.useState([]);
  const [transformationResult, setTransformationResult] = reactExports.useState([]);
  const [classificationResults, setClassificationResults] = reactExports.useState({});
  const [selectedForClassification, setSelectedForClassification] = reactExports.useState(null);
  const [detectedAnomalies, setDetectedAnomalies] = reactExports.useState([]);
  const [showStartScreen, setShowStartScreen] = reactExports.useState(true);
  const [gameStarted, setGameStarted] = reactExports.useState(false);
  const [gameSequence, setGameSequence] = reactExports.useState([]);
  const [playerSequence, setPlayerSequence] = reactExports.useState([]);
  const [isShowingSequence, setIsShowingSequence] = reactExports.useState(false);
  const [isPlayerTurn, setIsPlayerTurn] = reactExports.useState(false);
  const [currentLevel, setCurrentLevel] = reactExports.useState(1);
  const [difficulty, setDifficulty] = reactExports.useState("easy");
  const [feedback, setFeedback] = reactExports.useState(null);
  const [playingShape, setPlayingShape] = reactExports.useState(null);
  const [consecutiveSuccesses, setConsecutiveSuccesses] = reactExports.useState(0);
  const [startTime, setStartTime] = reactExports.useState(null);
  const [draggedShape, setDraggedShape] = reactExports.useState(null);
  const [countdown, setCountdown] = reactExports.useState(null);
  const [gameStats2, setGameStats] = reactExports.useState({
    score: 0,
    correctSequences: 0,
    totalAttempts: 0,
    level: 1,
    streak: 0,
    stars: 0
  });
  const [analysisResults, setAnalysisResults] = reactExports.useState(null);
  const [attemptCount, setAttemptCount] = reactExports.useState(0);
  const [advancedMetricsEngine] = reactExports.useState(() => new AdvancedMetricsEngine());
  const [sessionInteractions, setSessionInteractions] = reactExports.useState([]);
  const [sessionSequences, setSessionSequences] = reactExports.useState([]);
  const {
    startUnifiedSession,
    recordInteraction,
    updateMetrics,
    sessionId
  } = useUnifiedGameLogic("padroes_visuais");
  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    finalizeSession: finalizeMultisensory,
    isInitialized: multisensoryInitialized
  } = useMultisensoryIntegration(sessionId, {
    learningStyle: user?.profile?.learningStyle || "visual"
  });
  const {
    setUserContext: setTherapeuticContext
  } = useTherapeuticOrchestrator({ userId: user?.id });
  reactExports.useEffect(() => {
    if (metricsRef.current && recordInteraction && updateMetrics) {
      metricsRef.current.connectToBackend({
        recordInteraction,
        updateMetrics
      });
    }
  }, [recordInteraction, updateMetrics]);
  reactExports.useEffect(() => {
    if (sessionId && typeof sessionId === "string" && sessionId.length > 0 && gameStarted && !multisensoryInitialized) {
      const initializeMultisensorial = async () => {
        try {
          await initMultisensory();
          console.log("✅ Sistema multissensorial inicializado com sessionId:", sessionId);
        } catch (error) {
          console.error("❌ Erro ao inicializar sistema multissensorial:", error);
        }
      };
      initializeMultisensorial();
    }
  }, [sessionId, gameStarted, multisensoryInitialized, initMultisensory]);
  const [isSpeaking, setIsSpeaking] = reactExports.useState(false);
  const [currentSpeechButton, setCurrentSpeechButton] = reactExports.useState(null);
  const speechTimeoutRef = reactExports.useRef(null);
  const speak = reactExports.useCallback((text, options = {}) => {
    if (!ttsActive2 || !("speechSynthesis" in window) || isSpeaking) {
      return;
    }
    if (speechTimeoutRef.current) {
      clearTimeout(speechTimeoutRef.current);
    }
    window.speechSynthesis.cancel();
    setIsSpeaking(true);
    setCurrentSpeechButton(options.buttonId || null);
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = "pt-BR";
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    utterance.onend = () => {
      setIsSpeaking(false);
      setCurrentSpeechButton(null);
      if (options.onEnd) options.onEnd();
    };
    utterance.onerror = () => {
      setIsSpeaking(false);
      setCurrentSpeechButton(null);
    };
    speechTimeoutRef.current = setTimeout(() => {
      window.speechSynthesis.cancel();
      setIsSpeaking(false);
      setCurrentSpeechButton(null);
    }, 15e3);
    window.speechSynthesis.speak(utterance);
  }, [ttsActive2, isSpeaking]);
  const toggleTTS = reactExports.useCallback(() => {
    if ("speechSynthesis" in window) {
      window.speechSynthesis.cancel();
    }
    setIsSpeaking(false);
    setCurrentSpeechButton(null);
    if (speechTimeoutRef.current) {
      clearTimeout(speechTimeoutRef.current);
    }
    setTtsActive((prev) => {
      const newState = !prev;
      localStorage.setItem("padroesVisuaisTTS", newState.toString());
      if (newState) {
        setTimeout(() => {
          speak("TTS ativado", { rate: 1 });
        }, 100);
      }
      return newState;
    });
  }, [speak]);
  reactExports.useEffect(() => {
    if (!metricsRef.current) {
      metricsRef.current = PadroesVisuaisMetrics;
    }
  }, []);
  const generateSequencePatterns = reactExports.useCallback(() => {
    console.log("🔄 Generating sequence patterns activity");
    const sequenceTypes = {
      easy: [
        { sequence: ["🔴", "🔵", "🔴", "🔵", "🔴"], correct: "🔵", explanation: "Padrão alternado simples: vermelho e azul se alternam" },
        { sequence: ["⭐", "⭐", "🌙", "⭐", "⭐"], correct: "🌙", explanation: "Padrão de repetição: duas estrelas, uma lua" },
        { sequence: ["🔺", "🔶", "🔺", "🔶", "🔺"], correct: "🔶", explanation: "Alternância entre triângulo e losango" },
        { sequence: ["🟢", "🟢", "🟢", "🔴", "🔴"], correct: "🔴", explanation: "Mudança de cor: três verdes, depois vermelhos" }
      ],
      medium: [
        { sequence: ["🔴", "🔵", "🟡", "🔴", "🔵"], correct: "🟡", explanation: "Ciclo de três cores: vermelho, azul, amarelo" },
        { sequence: ["🔺", "🔺", "🔵", "🔵", "🔺"], correct: "🔺", explanation: "Padrão em pares: dois triângulos, dois círculos" },
        { sequence: ["🌟", "🔶", "🔶", "🌟", "🔶"], correct: "🔶", explanation: "Sequência: uma estrela, dois losangos" },
        { sequence: ["🟦", "🟨", "🟦", "🟨", "🟦"], correct: "🟨", explanation: "Alternância de cores quadradas" }
      ],
      hard: [
        { sequence: ["🔴", "🔵", "🔵", "🟡", "🟡", "🟡"], correct: "🟢", explanation: "Progressão crescente: 1 vermelho, 2 azuis, 3 amarelos, próximo são 4 verdes" },
        { sequence: ["🔺", "🔶", "🔵", "🔺", "🔶"], correct: "🔵", explanation: "Ciclo de três formas: triângulo, losango, círculo" },
        { sequence: ["🌟", "🌟", "🔶", "🔶", "🔶", "🔵"], correct: "🔵", explanation: "Padrão complexo: 2 estrelas, 3 losangos, 2 círculos" },
        { sequence: ["🟢", "🔴", "🟢", "🔴", "🔴", "🟢"], correct: "🔴", explanation: "Padrão irregular: verde-vermelho, verde-vermelho-vermelho" }
      ]
    };
    const currentLevel2 = gameState.difficulty.toLowerCase() || "easy";
    const levelPatterns = sequenceTypes[currentLevel2] || sequenceTypes.easy;
    const selectedPattern = levelPatterns[Math.floor(Math.random() * levelPatterns.length)];
    const allEmojis = ["🟣", "🟢", "🟠", "⚫", "⚪", "🟤", "🔸", "🔹", "🔷", "🔶"];
    const wrongOptions = allEmojis.filter((opt) => opt !== selectedPattern.correct && !selectedPattern.sequence.includes(opt)).sort(() => Math.random() - 0.5).slice(0, 3);
    const options = [selectedPattern.correct, ...wrongOptions].sort(() => Math.random() - 0.5);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        sequence_patterns: {
          pattern: selectedPattern.sequence,
          sequence: selectedPattern.sequence,
          correctAnswer: selectedPattern.correct,
          options,
          explanation: selectedPattern.explanation,
          userAnswer: null,
          level: currentLevel2,
          round: prev.activityRoundCount + 1,
          totalRounds: prev.roundsPerActivity,
          showResult: false,
          isCorrect: null
        }
      }
    }));
    speak(`Padrões Sequenciais - Nível ${currentLevel2}. Observe a sequência e identifique qual elemento vem a seguir.`);
  }, [gameState.difficulty, speak]);
  const generateGeometricPatterns = reactExports.useCallback(() => {
    console.log("🔺 Generating geometric patterns activity");
    const geometricTypes = {
      easy: [
        {
          pattern: ["🔴", "🔺", "🟦", "🔴", "🔺"],
          correct: "🟦",
          explanation: "Padrão geométrico simples: círculo, triângulo, quadrado se repetem"
        },
        {
          pattern: ["⭐", "⭐", "🔶", "⭐", "⭐"],
          correct: "🔶",
          explanation: "Padrão de repetição: duas estrelas, um losango"
        },
        {
          pattern: ["🔺", "🔴", "🔺", "🔴", "🔺"],
          correct: "🔴",
          explanation: "Alternância entre triângulo e círculo"
        },
        {
          pattern: ["🟦", "🟦", "🔶", "🟦", "🟦"],
          correct: "🔶",
          explanation: "Padrão: dois quadrados, um losango"
        }
      ],
      medium: [
        {
          pattern: ["🔴", "🔺", "🔴🔺", "🟦", "🔺🟦"],
          correct: "🔴🟦",
          explanation: "Combinação progressiva: forma individual, depois combinada"
        },
        {
          pattern: ["⬜", "⬛", "⬜⬛", "🔶", "⬜🔶"],
          correct: "⬛🔶",
          explanation: "Padrão de combinação: branco, preto, depois combinações"
        },
        {
          pattern: ["🔺", "🔺🔴", "🔴", "🔴🟦", "🟦"],
          correct: "🟦🔺",
          explanation: "Sequência com transformações geométricas"
        }
      ],
      hard: [
        {
          pattern: ["🔴🔺🟦", "🔺🟦🔴", "🟦🔴🔺", "🔴🔺🟦"],
          correct: "🔺🟦🔴",
          explanation: "Rotação cíclica complexa de três formas geométricas"
        },
        {
          pattern: ["🔴", "🔴🔺", "🔴🔺🟦", "🔴🔺🟦🔶"],
          correct: "🔴🔺🟦🔶⭐",
          explanation: "Crescimento progressivo: cada elemento adiciona uma nova forma"
        }
      ]
    };
    const currentLevel2 = gameState.difficulty.toLowerCase() || "easy";
    const levelPatterns = geometricTypes[currentLevel2] || geometricTypes.easy;
    const selectedPattern = levelPatterns[Math.floor(Math.random() * levelPatterns.length)];
    const geometricShapes = ["🟣", "🔸", "🔹", "🔷", "🔶", "⬜", "⬛", "🟤", "🟨", "🟧"];
    const wrongOptions = geometricShapes.filter((opt) => opt !== selectedPattern.correct && !selectedPattern.pattern.includes(opt)).sort(() => Math.random() - 0.5).slice(0, 3);
    const options = [selectedPattern.correct, ...wrongOptions].sort(() => Math.random() - 0.5);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        geometric_patterns: {
          pattern: selectedPattern.pattern,
          sequence: selectedPattern.pattern,
          correctAnswer: selectedPattern.correct,
          options,
          explanation: selectedPattern.explanation,
          userAnswer: null,
          level: currentLevel2,
          round: prev.activityRoundCount + 1,
          totalRounds: prev.roundsPerActivity,
          showResult: false,
          isCorrect: null
        }
      }
    }));
    speak(`Padrões Geométricos - Nível ${currentLevel2}. Identifique a forma que completa o padrão geométrico.`);
  }, [gameState.difficulty, speak]);
  const generateColorPatterns = reactExports.useCallback(() => {
    console.log("🌈 Generating color patterns activity");
    const colorTypes = {
      easy: [
        {
          colors: ["🔴", "🟡", "🔵", "🔴", "🟡"],
          missing: 4,
          correct: "🔵",
          explanation: "Sequência de cores primárias: vermelho, amarelo, azul"
        },
        {
          colors: ["🟢", "🟢", "🟣", "🟢", "🟢"],
          missing: 2,
          correct: "🟣",
          explanation: "Padrão: 2 verdes, 1 roxo"
        }
      ],
      medium: [
        {
          colors: ["🔴🟡", "🔵🟢", "🟣🟠", "🔴🟡", "🔵🟢"],
          missing: 4,
          correct: "🟣🟠",
          explanation: "Sequência de pares de cores complementares"
        },
        {
          colors: ["🌈", "🔴", "🌈", "🟡", "🌈"],
          missing: 4,
          correct: "🔵",
          explanation: "Arco-íris alternado com cores primárias"
        }
      ],
      hard: [
        {
          colors: ["🔴🟡🔵", "🟡🔵🟢", "🔵🟢🟣", "🟢🟣🟠"],
          missing: 3,
          correct: "🟣🟠🔴",
          explanation: "Progressão cromática com sobreposição"
        }
      ]
    };
    const currentLevel2 = gameState.difficulty.toLowerCase() || "easy";
    const levelPatterns = colorTypes[currentLevel2] || colorTypes.easy;
    const selectedPattern = levelPatterns[Math.floor(Math.random() * levelPatterns.length)];
    const wrongOptions = ["⚫", "⚪", "🟤", "🔴🟢"].filter((opt) => opt !== selectedPattern.correct);
    const options = [selectedPattern.correct, ...wrongOptions.slice(0, 2)].sort(() => Math.random() - 0.5);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        color_patterns: {
          pattern: selectedPattern.colors,
          sequence: selectedPattern.colors,
          missingIndex: selectedPattern.missing,
          correctAnswer: selectedPattern.correct,
          options,
          explanation: selectedPattern.explanation,
          userAnswer: null,
          level: currentLevel2,
          round: prev.activityRoundCount + 1,
          totalRounds: prev.roundsPerActivity
        }
      }
    }));
    speak(`Atividade: Padrões de Cores - Nível ${currentLevel2}. Identifique a cor que completa o padrão cromático.`);
  }, [gameState.difficulty, speak]);
  const generateSymmetryPatterns = reactExports.useCallback(() => {
    console.log("⚖️ Generating symmetry patterns activity");
    const symmetryTypes = {
      easy: [
        {
          pattern: ["🔴", "🔵", "🟡", "🔵", "🔴"],
          type: "horizontal",
          isSymmetric: true,
          explanation: "Simetria horizontal perfeita"
        },
        {
          pattern: ["🔺", "🟦", "🔺", "🟦", "🔺"],
          type: "horizontal",
          isSymmetric: true,
          explanation: "Padrão simétrico horizontal"
        },
        {
          pattern: ["🔴", "🔵", "🟡", "🟢", "🟣"],
          type: "none",
          isSymmetric: false,
          explanation: "Sem simetria - padrão aleatório"
        }
      ],
      medium: [
        {
          pattern: [["🔴", "🔵"], ["🟡", "🟢"], ["🔴", "🔵"]],
          type: "vertical",
          isSymmetric: true,
          explanation: "Simetria vertical em matriz"
        },
        {
          pattern: [["🔺", "🟦"], ["🟦", "🔺"]],
          type: "diagonal",
          isSymmetric: true,
          explanation: "Simetria diagonal"
        }
      ],
      hard: [
        {
          pattern: [["🔴", "🔵", "🔴"], ["🟡", "🟢", "🟡"], ["🔴", "🔵", "🔴"]],
          type: "radial",
          isSymmetric: true,
          explanation: "Simetria radial complexa"
        }
      ]
    };
    const currentLevel2 = gameState.difficulty.toLowerCase() || "easy";
    const levelPatterns = symmetryTypes[currentLevel2] || symmetryTypes.easy;
    const selectedPattern = levelPatterns[Math.floor(Math.random() * levelPatterns.length)];
    const options = ["Simétrico", "Não Simétrico"].sort(() => Math.random() - 0.5);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        symmetry_patterns: {
          pattern: selectedPattern.pattern,
          sequence: selectedPattern.pattern,
          symmetryType: selectedPattern.type,
          isSymmetric: selectedPattern.isSymmetric,
          correctAnswer: selectedPattern.isSymmetric ? "Simétrico" : "Não Simétrico",
          options,
          explanation: selectedPattern.explanation,
          userAnswer: null,
          level: currentLevel2,
          round: prev.activityRoundCount + 1,
          totalRounds: prev.roundsPerActivity
        }
      }
    }));
    speak(`Atividade: Padrões de Simetria - Nível ${currentLevel2}. Analise se o padrão apresenta simetria.`);
  }, [gameState.difficulty, speak]);
  const generateCompletionPatterns = reactExports.useCallback(() => {
    console.log("🧩 Generating completion patterns activity");
    const completionTypes = {
      easy: [
        {
          pattern: ["🔴", "🔵", "🔴", "🔵", null],
          missing: 4,
          correct: "🔴",
          explanation: "Complete o padrão alternado simples"
        },
        {
          pattern: ["🟡", "🟡", "🟢", "🟡", "🟡", null],
          missing: 5,
          correct: "🟢",
          explanation: "Padrão: 2 amarelos, 1 verde"
        }
      ],
      medium: [
        {
          pattern: ["🔴", "🔵🟡", "🔴🔵", "🟡🔴", null],
          missing: 4,
          correct: "🔵🟡",
          explanation: "Padrão de rotação com múltiplos elementos"
        },
        {
          pattern: [["🔴", "🔵"], ["🟡", null], ["🔴", "🔵"]],
          missing: [1, 1],
          correct: "🟢",
          explanation: "Complete a matriz simétrica"
        }
      ],
      hard: [
        {
          pattern: ["🔴🔵🟡", "🔵🟡🟢", "🟡🟢🟣", null],
          missing: 3,
          correct: "🟢🟣🟠",
          explanation: "Sequência progressiva complexa com sobreposição"
        }
      ]
    };
    const currentLevel2 = gameState.difficulty.toLowerCase() || "easy";
    const levelPatterns = completionTypes[currentLevel2] || completionTypes.easy;
    const selectedPattern = levelPatterns[Math.floor(Math.random() * levelPatterns.length)];
    const wrongOptions = ["🟤", "⚫", "⚪", "🔶🔸"].filter((opt) => opt !== selectedPattern.correct);
    const options = [selectedPattern.correct, ...wrongOptions.slice(0, 3)].sort(() => Math.random() - 0.5);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        completion_patterns: {
          pattern: selectedPattern.pattern,
          sequence: selectedPattern.pattern,
          missingIndex: selectedPattern.missing,
          correctAnswer: selectedPattern.correct,
          options,
          explanation: selectedPattern.explanation,
          userAnswer: null,
          level: currentLevel2,
          round: prev.activityRoundCount + 1,
          totalRounds: prev.roundsPerActivity
        }
      }
    }));
    speak(`Atividade: Completar Padrões - Nível ${currentLevel2}. Complete o padrão visual identificando o elemento faltante.`);
  }, [gameState.difficulty, speak]);
  const generateNewRound = reactExports.useCallback((activityId = null) => {
    const currentActivity = activityId || gameState.currentActivity || ACTIVITY_TYPES.SEQUENCE_PATTERNS.id;
    console.log("🎯 generateNewRound - currentActivity:", currentActivity);
    setGameState((prev) => ({
      ...prev,
      currentActivity,
      // Garantir que está definido
      roundStartTime: Date.now(),
      showFeedback: false,
      feedbackType: null,
      feedbackMessage: ""
    }));
    switch (currentActivity) {
      case ACTIVITY_TYPES.SEQUENCE_PATTERNS.id:
        console.log("🔄 Gerando padrões sequenciais");
        generateSequencePatterns();
        break;
      case ACTIVITY_TYPES.GEOMETRIC_PATTERNS.id:
        console.log("🔺 Gerando padrões geométricos");
        generateGeometricPatterns();
        break;
      case ACTIVITY_TYPES.COLOR_PATTERNS.id:
        console.log("🌈 Gerando padrões de cores");
        generateColorPatterns();
        break;
      case ACTIVITY_TYPES.SYMMETRY_PATTERNS.id:
        console.log("⚖️ Gerando padrões de simetria");
        generateSymmetryPatterns();
        break;
      case ACTIVITY_TYPES.COMPLETION_PATTERNS.id:
        console.log("🧩 Gerando padrões de completar");
        generateCompletionPatterns();
        break;
      default:
        console.log("🔄 Fallback para padrões sequenciais");
        generateSequencePatterns();
    }
  }, [
    gameState.currentActivity,
    generateSequencePatterns,
    generateGeometricPatterns,
    generateColorPatterns,
    generateSymmetryPatterns,
    generateCompletionPatterns
  ]);
  reactExports.useCallback((activityId) => {
    console.log("🔄 Switching to activity:", activityId);
    setGameState((prev) => ({
      ...prev,
      currentActivity: activityId,
      activityData: {
        ...prev.activityData,
        [activityId]: {}
        // Reset da atividade
      }
    }));
    switch (activityId) {
      case ACTIVITY_TYPES.SEQUENCE_PATTERNS.id:
        generateSequencePatterns();
        break;
      case ACTIVITY_TYPES.GEOMETRIC_PATTERNS.id:
        generateGeometricPatterns();
        break;
      case ACTIVITY_TYPES.COLOR_PATTERNS.id:
        generateColorPatterns();
        break;
      case ACTIVITY_TYPES.SYMMETRY_PATTERNS.id:
        generateSymmetryPatterns();
        break;
      case ACTIVITY_TYPES.COMPLETION_PATTERNS.id:
        generateCompletionPatterns();
        break;
      default:
        generateSequencePatterns();
    }
    const activity = Object.values(ACTIVITY_TYPES).find((a) => a.id === activityId);
    if (activity && speak) {
      speak(`Atividade alterada para: ${activity.name}. ${activity.description}`, { rate: 0.8 });
    }
  }, [generateSequencePatterns, generateGeometricPatterns, generateColorPatterns, generateSymmetryPatterns, generateCompletionPatterns, speak]);
  const generateSequence = reactExports.useCallback((length) => {
    const sequence = [];
    let lastShapeIndex = -1;
    for (let i = 0; i < length; i++) {
      let randomIndex;
      do {
        randomIndex = Math.floor(Math.random() * PadroesVisuaisConfig.shapes.length);
      } while (randomIndex === lastShapeIndex && PadroesVisuaisConfig.shapes.length > 1);
      lastShapeIndex = randomIndex;
      sequence.push(PadroesVisuaisConfig.shapes[randomIndex].id);
    }
    return sequence;
  }, []);
  const showSequence = reactExports.useCallback(async () => {
    setIsShowingSequence(true);
    setIsPlayerTurn(false);
    setPlayerSequence([]);
    setFeedback(null);
    const difficultyData = PadroesVisuaisConfig.difficulties.find((d) => d.id === difficulty);
    const baseTime = difficultyData.showTime || 8e3;
    const sequenceBonus = gameSequence.length * 1500;
    const showTime = baseTime + sequenceBonus;
    console.log(`📝 Mostrando sequência de ${gameSequence.length} formas por ${showTime}ms`);
    const formDescription = gameSequence.length === 1 ? "forma" : "formas";
    const memorizeMessage = `Atenção! Memorize esta sequência de ${gameSequence.length} ${formDescription} coloridas. Você tem ${Math.ceil(showTime / 1e3)} segundos para memorizar.`;
    speak(memorizeMessage, {
      rate: 1,
      onEnd: () => console.log("Instrução de memorização anunciada")
    });
    setCountdown(Math.ceil(showTime / 1e3));
    const countdownInterval = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(countdownInterval);
          return 0;
        }
        return prev - 1;
      });
    }, 1e3);
    setTimeout(() => {
      clearInterval(countdownInterval);
      setCountdown(null);
      setIsShowingSequence(false);
      setIsPlayerTurn(true);
      const turnMessage = `Agora é sua vez! Recrie a sequência arrastando as formas para os slots corretos.`;
      speak(turnMessage, {
        rate: 0.9,
        onEnd: () => console.log("Vez do jogador anunciada")
      });
    }, showTime);
  }, [gameSequence, difficulty, speak]);
  reactExports.useCallback(() => {
    const difficultyData = PadroesVisuaisConfig.difficulties.find((d) => d.id === difficulty);
    const sequenceLength = Math.min(difficultyData.sequenceLength + Math.floor(currentLevel / 3), 8);
    console.log(`🎯 Gerando novo nível - Dificuldade: ${difficulty}, Nível: ${currentLevel}, Tamanho calculado: ${sequenceLength}`);
    const newSequence = generateSequence(sequenceLength);
    console.log(`✨ Nova sequência gerada: ${JSON.stringify(newSequence)}, Comprimento: ${newSequence.length}`);
    setGameSequence(newSequence);
    setPlayerSequence([]);
    setFeedback(null);
    setTimeout(() => showSequence(), 1e3);
  }, [difficulty, currentLevel, generateSequence, showSequence]);
  const startGame = reactExports.useCallback(async (selectedDifficulty) => {
    try {
      setShowStartScreen(false);
      setGameStarted(true);
      setGameState((prev) => ({
        ...prev,
        status: "playing",
        difficulty: selectedDifficulty,
        roundStartTime: Date.now()
      }));
      if (startUnifiedSession) {
        await startUnifiedSession({
          gameType: "padroes_visuais",
          difficulty: selectedDifficulty,
          userId: user?.id || "anonymous"
        });
      }
      if (user?.id && user.id !== "anonymous" && user.id !== "") {
        setTherapeuticContext(user.id);
      }
      generateNewRound();
      speak("Jogo iniciado! Vamos começar com padrões visuais.");
    } catch (error) {
      console.error("Erro ao iniciar jogo:", error);
    }
  }, [startUnifiedSession, setTherapeuticContext, user, speak, generateNewRound]);
  const changeActivity = reactExports.useCallback((activityId) => {
    console.log("🔄 changeActivity chamada com:", activityId);
    console.log("🔄 Atividade atual:", gameState.currentActivity);
    if (gameState.currentActivity === activityId) {
      console.log("⚠️ Já está na atividade selecionada");
      return;
    }
    setGameState((prev) => ({
      ...prev,
      currentActivity: activityId,
      activityRoundCount: 0,
      activityIndex: prev.activityCycle.indexOf(activityId)
    }));
    const activity = Object.values(ACTIVITY_TYPES).find((a) => a.id === activityId);
    console.log("🎯 Atividade encontrada:", activity);
    speak(`Mudando para: ${activity?.name}`);
    setTimeout(() => {
      generateNewRound(activityId);
    }, 100);
  }, [gameState.currentActivity, speak, generateNewRound]);
  reactExports.useCallback((shapeId) => {
    const shape = PadroesVisuaisConfig.shapes.find((s) => s.id === shapeId);
    return shape ? shape.emoji : shapeId;
  }, []);
  const explainGame = reactExports.useCallback(() => {
    const explanation = `
      Bem-vindo ao Padrões Visuais! Este jogo ajuda a desenvolver sua memória visual, sequenciação, concentração e reconhecimento de padrões lógicos.
      Você observará sequências de formas coloridas e deverá reproduzi-las na ordem correta.
      Use as formas disponíveis para arrastar e soltar nos slots corretos.
      Você pode escolher entre três níveis de dificuldade: fácil, médio e avançado.
      Boa sorte e divirta-se!
    `;
    speak(explanation, {
      rate: 0.9,
      buttonId: "explain",
      onEnd: () => console.log("Explicação do jogo anunciada")
    });
  }, [speak]);
  reactExports.useCallback(() => {
    let instruction = "";
    if (isShowingSequence) {
      instruction = `Observe e memorize esta sequência de ${gameSequence.length} formas coloridas. ${countdown ? `Você tem ${countdown} segundos restantes para memorizar.` : ""}`;
    } else if (isPlayerTurn) {
      instruction = `Agora é sua vez! Recrie a sequência arrastando as formas para os slots corretos na ordem que você memorizou. São ${gameSequence.length} formas no total.`;
    } else {
      instruction = "Preparando a próxima sequência. Aguarde um momento.";
    }
    speak(instruction, {
      rate: 0.9,
      buttonId: "repeat",
      onEnd: () => console.log("Instrução repetida")
    });
  }, [isShowingSequence, isPlayerTurn, gameSequence, countdown, speak]);
  const playFeedback = reactExports.useCallback((message, type = "neutral") => {
    const prefix = type === "success" ? "Parabéns! " : type === "error" ? "Ops! " : "";
    speak(prefix + message, {
      rate: type === "success" ? 1.1 : 0.9,
      pitch: type === "success" ? 1.2 : type === "error" ? 0.8 : 1,
      onEnd: () => console.log(`Feedback ${type} anunciado`)
    });
  }, [speak]);
  reactExports.useCallback(() => {
    const statsText = `
      Suas estatísticas atuais:
      Nível ${currentLevel}.
      Pontuação: ${gameStats2.score} pontos.
      Sequência atual de acertos: ${gameStats2.streak}.
      Precisão: ${getAccuracy()} por cento.
      Total de sequências corretas: ${gameStats2.correctSequences} de ${gameStats2.totalAttempts} tentativas.
    `;
    speak(statsText, {
      rate: 0.8,
      buttonId: "stats",
      onEnd: () => console.log("Estatísticas anunciadas")
    });
  }, [currentLevel, gameStats2, speak]);
  reactExports.useCallback(() => {
    if (gameSequence.length === 0) {
      speak("Nenhuma sequência disponível no momento.");
      return;
    }
    const shapes = gameSequence.map((shapeId) => {
      const shape = PadroesVisuaisConfig.shapes.find((s) => s.id === shapeId);
      return shape ? shape.name : "forma desconhecida";
    });
    const sequenceText = `
      A sequência atual tem ${gameSequence.length} formas:
      ${shapes.join(", ")}.
      ${isShowingSequence ? "Continue observando para memorizar." : "Agora recrie esta sequência na ordem correta."}
    `;
    speak(sequenceText, {
      rate: 0.8,
      buttonId: "sequence",
      onEnd: () => console.log("Sequência anunciada")
    });
  }, [gameSequence, isShowingSequence, speak]);
  reactExports.useCallback(() => {
    let hint = "";
    if (isShowingSequence) {
      hint = `Dica: Tente criar uma história ou padrão mental com as ${gameSequence.length} formas para facilitar a memorização. Observe as cores e posições com atenção.`;
    } else if (isPlayerTurn) {
      const filledSlots = playerSequence.filter((shape) => shape !== void 0).length;
      const remainingSlots = gameSequence.length - filledSlots;
      hint = remainingSlots > 0 ? `Dica: Você já colocou ${filledSlots} formas. Ainda faltam ${remainingSlots} formas para completar a sequência. Lembre-se da ordem que você memorizou.` : "Dica: Você preencheu todos os slots! Verifique se a ordem está correta antes de confirmar.";
    } else {
      hint = "Dica: Use o tempo de preparação para se concentrar e se preparar para a próxima sequência.";
    }
    speak(hint, {
      rate: 0.8,
      pitch: 1.1,
      buttonId: "hint",
      onEnd: () => console.log("Dica fornecida")
    });
  }, [isShowingSequence, isPlayerTurn, gameSequence, playerSequence, speak]);
  reactExports.useCallback(async () => {
    try {
      console.log("🏁 Processando métricas finais da sessão de Padrões Visuais...");
      const finalSessionData = {
        sessionId,
        userId: user?.id || "anonymous",
        gameType: "PadroesVisuais",
        duration: Date.now() - startTime,
        interactions: sessionInteractions,
        sequences: sessionSequences,
        finalStats: gameStats2,
        patternData: {
          finalLevel: currentLevel,
          difficulty,
          consecutiveSuccesses,
          totalSequences: sessionSequences.length
        },
        accuracy: gameStats2.correctSequences / Math.max(1, gameStats2.totalAttempts),
        averageResponseTime: sessionInteractions.length > 0 ? sessionInteractions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / sessionInteractions.length : 0,
        completeness: 1,
        attentionScore: calculateAttentionScore(),
        sessionComplete: true
      };
      const finalMetrics = await advancedMetricsEngine.processAdvancedMetrics(
        finalSessionData,
        { id: user?.id || "anonymous", preferences: {} },
        []
      );
      console.log("📊 Métricas Finais de Padrões Visuais:", finalMetrics);
      if (finalMetrics.metrics.patterns) {
        console.log("🎨 Resumo Final - Análise de Padrões Visuais:", {
          sessionId,
          totalInteractions: sessionInteractions.length,
          totalSequences: sessionSequences.length,
          finalAccuracy: gameStats2.correctSequences / Math.max(1, gameStats2.totalAttempts),
          patternRecognition: finalMetrics.metrics.patterns.patternRecognition,
          visualMemory: finalMetrics.metrics.patterns.visualMemory,
          spatialProcessing: finalMetrics.metrics.patterns.spatialProcessing,
          insights: finalMetrics.insights,
          recommendations: finalMetrics.recommendations
        });
      }
    } catch (error) {
      console.warn("Erro ao processar métricas finais:", error);
    }
  }, [sessionId, user, startTime, sessionInteractions, sessionSequences, gameStats2, currentLevel, difficulty, consecutiveSuccesses, advancedMetricsEngine]);
  const processAdvancedMetrics = reactExports.useCallback(async (currentInteraction) => {
    try {
      console.log("🔬 Processando métricas avançadas de padrões visuais...");
      const sessionData = {
        sessionId,
        userId: user?.id || "anonymous",
        gameType: "PadroesVisuais",
        duration: Date.now() - startTime,
        interactions: sessionInteractions,
        sequences: sessionSequences,
        patternData: {
          currentLevel,
          difficulty,
          consecutiveSuccesses,
          currentSequence: gameSequence,
          playerResponse: playerSequence
        },
        accuracy: gameStats2.correctSequences / Math.max(1, gameStats2.totalAttempts),
        averageResponseTime: sessionInteractions.length > 0 ? sessionInteractions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / sessionInteractions.length : 0,
        completeness: currentInteraction.correct ? 1 : 0,
        attentionScore: calculateAttentionScore()
      };
      const advancedMetrics = await advancedMetricsEngine.processAdvancedMetrics(
        sessionData,
        { id: user?.id || "anonymous", preferences: {} },
        []
      );
      console.log("📊 Métricas Avançadas de Padrões Visuais:", advancedMetrics);
      if (advancedMetrics.metrics.patterns) {
        console.log("🎨 Análise de Padrões Visuais:", {
          recognitionAccuracy: advancedMetrics.metrics.patterns.patternRecognition?.overallAccuracy,
          visualMemoryCapacity: advancedMetrics.metrics.patterns.visualMemory?.memoryCapacity,
          spatialProcessing: advancedMetrics.metrics.patterns.spatialProcessing?.spatialOrientation,
          errorPatterns: advancedMetrics.metrics.patterns.errorPatterns
        });
      }
      if (advancedMetrics.insights?.length > 0) {
        console.log("💡 Insights de Padrões:", advancedMetrics.insights);
      }
      if (advancedMetrics.recommendations?.length > 0) {
        console.log("🎯 Recomendações para Padrões:", advancedMetrics.recommendations);
      }
    } catch (error) {
      console.warn("Erro ao processar métricas avançadas:", error);
    }
  }, [sessionId, user, startTime, sessionInteractions, sessionSequences, gameStats2, currentLevel, difficulty, consecutiveSuccesses, gameSequence, playerSequence, advancedMetricsEngine]);
  const calculateAttentionScore = reactExports.useCallback(() => {
    if (sessionInteractions.length === 0) return 0.5;
    const recentInteractions = sessionInteractions.slice(-5);
    const avgResponseTime = recentInteractions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / recentInteractions.length;
    const accuracy = recentInteractions.filter((i) => i.correct).length / recentInteractions.length;
    const timeScore = Math.max(0, Math.min(1, (1e4 - avgResponseTime) / 1e4));
    return (accuracy + timeScore) / 2;
  }, [sessionInteractions]);
  const recordPatternInteraction = reactExports.useCallback(async (playerSequence2, gameSequence2, isCorrect, completionTime) => {
    const interactionData = {
      playerSequence: playerSequence2,
      correctSequence: gameSequence2,
      isCorrect,
      completionTime,
      timestamp: Date.now(),
      attemptNumber: attemptCount + 1,
      difficulty,
      level: currentLevel,
      sequenceLength: gameSequence2.length
    };
    try {
      await recordMultisensoryInteraction("pattern_interaction", {
        selectedPattern: playerSequence2,
        targetPattern: gameSequence2,
        isCorrect,
        responseTime: completionTime,
        difficulty,
        level: currentLevel,
        patternType: "sequence_reproduction"
      });
      await collectorsHub.collectSequenceData({
        ...interactionData,
        gameState: {
          score: gameStats2.score,
          totalAttempts: gameStats2.totalAttempts,
          correctSequences: gameStats2.correctSequences,
          streak: gameStats2.streak
        }
      });
      const newAttemptCount = attemptCount + 1;
      setAttemptCount(newAttemptCount);
      if (newAttemptCount % 3 === 0) {
        await performCognitiveAnalysis();
      }
    } catch (error) {
      console.error("Erro ao coletar dados do padrão:", error);
    }
  }, [recordMultisensoryInteraction, collectorsHub, attemptCount, difficulty, currentLevel, gameStats2]);
  const performCognitiveAnalysis = reactExports.useCallback(async () => {
    try {
      setCognitiveAnalysisVisible(true);
      const analysisData = await collectorsHub.performCompleteAnalysis();
      setAnalysisResults(analysisData);
      setTimeout(() => setCognitiveAnalysisVisible(false), 3e3);
    } catch (error) {
      console.error("Erro na análise cognitiva de padrões:", error);
      setCognitiveAnalysisVisible(false);
    }
  }, [collectorsHub]);
  const getAccuracy = reactExports.useCallback(() => {
    if (gameStats2.totalAttempts === 0) return 100;
    return Math.round(gameStats2.correctSequences / gameStats2.totalAttempts * 100);
  }, [gameStats2]);
  reactExports.useCallback(async (shapeId) => {
    setPlayingShape(shapeId);
    const shape = PadroesVisuaisConfig.shapes.find((s) => s.id === shapeId);
    if (!shape) {
      console.warn(`Forma não encontrada: ${shapeId}`);
      setPlayingShape(null);
      return;
    }
    console.log(`🎵 Reproduzindo forma: ${shape.name}`);
    setTimeout(() => setPlayingShape(null), 600);
  }, []);
  reactExports.useCallback((currentSequence) => {
    const filledSlots = currentSequence.filter((shape) => shape !== void 0);
    if (filledSlots.length === gameSequence.length) {
      const completionTime = startTime ? Date.now() - startTime : 0;
      const isCorrect = gameSequence.every((shape, index) => currentSequence[index] === shape);
      recordPatternInteraction(currentSequence, gameSequence, isCorrect, completionTime);
      if (isCorrect) {
        handleCorrectSequence();
      } else {
        handleIncorrectSequence();
      }
    }
  }, [gameSequence, startTime, recordPatternInteraction]);
  const handleCorrectSequence = reactExports.useCallback(() => {
    const basePoints = 20;
    const levelBonus = currentLevel * 10;
    const lengthBonus = gameSequence.length * 5;
    const points = basePoints + levelBonus + lengthBonus;
    const responseTime = Date.now() - startTime;
    const interaction = {
      timestamp: Date.now(),
      correct: true,
      sequenceCorrect: true,
      responseTime,
      sequenceLength: gameSequence.length,
      level: currentLevel,
      difficulty,
      gameSequence: [...gameSequence],
      playerSequence: [...playerSequence],
      points,
      consecutiveSuccesses: consecutiveSuccesses + 1
    };
    setSessionInteractions((prev) => [...prev, interaction]);
    setSessionSequences((prev) => [...prev, gameSequence]);
    setGameStats((prev) => ({
      ...prev,
      score: prev.score + points,
      correctSequences: prev.correctSequences + 1,
      totalAttempts: prev.totalAttempts + 1,
      level: prev.level + 1,
      streak: prev.streak + 1
    }));
    setCurrentLevel((prev) => prev + 1);
    setConsecutiveSuccesses((prev) => prev + 1);
    if ((consecutiveSuccesses + 1) % 3 === 0) {
      processAdvancedMetrics(interaction);
    }
    const message = PadroesVisuaisConfig.encouragingMessages[Math.floor(Math.random() * PadroesVisuaisConfig.encouragingMessages.length)];
    playFeedback(`${message} Você ganhou ${points} pontos!`, "success");
    setTimeout(() => {
      setFeedback(null);
      generateNewRound();
    }, 3e3);
  }, [gameSequence, playerSequence, currentLevel, difficulty, consecutiveSuccesses, startTime, generateNewRound, playFeedback, processAdvancedMetrics]);
  const handleIncorrectSequence = reactExports.useCallback(() => {
    const responseTime = Date.now() - startTime;
    const interaction = {
      timestamp: Date.now(),
      correct: false,
      sequenceCorrect: false,
      responseTime,
      sequenceLength: gameSequence.length,
      level: currentLevel,
      difficulty,
      gameSequence: [...gameSequence],
      playerSequence: [...playerSequence],
      points: 0,
      consecutiveSuccesses: 0,
      errorDetails: analyzeSequenceError(gameSequence, playerSequence)
    };
    setSessionInteractions((prev) => [...prev, interaction]);
    setSessionSequences((prev) => [...prev, gameSequence]);
    setGameStats((prev) => ({
      ...prev,
      totalAttempts: prev.totalAttempts + 1,
      streak: 0
    }));
    setConsecutiveSuccesses(0);
    processAdvancedMetrics(interaction);
    playFeedback("Sequência incorreta. Observe novamente a sequência correta!", "error");
    setTimeout(() => {
      setIsShowingSequence(true);
      setIsPlayerTurn(false);
      setTimeout(() => {
        setPlayerSequence([]);
        setFeedback(null);
        setIsShowingSequence(false);
        setIsPlayerTurn(true);
      }, 5e3);
    }, 1e3);
  }, [gameSequence, playerSequence, currentLevel, difficulty, startTime, playFeedback, processAdvancedMetrics]);
  const analyzeSequenceError = (correctSequence, playerSequence2) => {
    const errors = {
      positionErrors: [],
      shapeErrors: [],
      orderErrors: [],
      totalErrors: 0
    };
    correctSequence.forEach((correctShape, index) => {
      const playerShape = playerSequence2[index];
      if (playerShape !== correctShape) {
        errors.totalErrors++;
        errors.positionErrors.push(index);
        if (playerShape && correctSequence.includes(playerShape)) {
          errors.orderErrors.push({
            position: index,
            expected: correctShape,
            actual: playerShape,
            type: "order"
          });
        } else if (playerShape) {
          errors.shapeErrors.push({
            position: index,
            expected: correctShape,
            actual: playerShape,
            type: "shape"
          });
        } else {
          errors.shapeErrors.push({
            position: index,
            expected: correctShape,
            actual: null,
            type: "missing"
          });
        }
      }
    });
    return errors;
  };
  const initializeActivity = reactExports.useCallback((activityIndex) => {
    const activityKeys = Object.keys(ACTIVITY_CONFIG);
    const activityKey = activityKeys[activityIndex];
    setGameState((prev) => ({
      ...prev,
      activityIndex
    }));
    setSequenceToReproduce([]);
    setPatternToComplete(null);
    setConstructionRules(null);
    setClassificationCriteria(null);
    setTransformationTarget(null);
    setAnomalyPattern(null);
    setPlayerConstruction([]);
    setSelectedTransformation(null);
    setClassificationGroups([]);
    setFeedback(null);
    if (activityKey) {
      console.log(`🎯 Inicializando atividade: ${activityKey}`);
    }
  }, []);
  reactExports.useEffect(() => {
    if (gameStarted && !showStartScreen) {
      initializeActivity(0);
    }
  }, [gameStarted, showStartScreen, initializeActivity]);
  reactExports.useEffect(() => {
    if (currentLevel > 10 && gameStarted) {
      const finalizeMultisensorySession = async () => {
        try {
          const multisensoryReport = await finalizeMultisensory({
            finalScore: gameStats2.score,
            finalAccuracy: gameStats2.correctSequences / gameStats2.totalAttempts,
            totalInteractions: gameStats2.totalAttempts,
            sessionDuration: Date.now() - startTime,
            difficulty
          });
          console.log("🔄 PadroesVisuais: Relatório multissensorial final:", multisensoryReport);
        } catch (error) {
          console.warn("⚠️ PadroesVisuais: Erro ao finalizar sessão multissensorial:", error);
        }
      };
      finalizeMultisensorySession();
    }
  }, [currentLevel, gameStarted, finalizeMultisensory, gameStats2, startTime, difficulty]);
  reactExports.useEffect(() => {
    const savedTTSState = localStorage.getItem("padroesVisuaisTTS");
    if (savedTTSState !== null) {
      setTtsActive(savedTTSState === "true");
    }
  }, []);
  reactExports.useEffect(() => {
    return () => {
      if ("speechSynthesis" in window) {
        window.speechSynthesis.cancel();
      }
      console.log("🔇 TTS parado ao sair do jogo Padrões Visuais");
    };
  }, []);
  reactExports.useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden && "speechSynthesis" in window) {
        window.speechSynthesis.cancel();
        setIsSpeaking(false);
        setCurrentSpeechButton(null);
      }
    };
    const handlePageHide = () => {
      if ("speechSynthesis" in window) {
        window.speechSynthesis.cancel();
      }
    };
    document.addEventListener("visibilitychange", handleVisibilityChange);
    window.addEventListener("pagehide", handlePageHide);
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("pagehide", handlePageHide);
      if ("speechSynthesis" in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);
  const renderActivityInterface = () => {
    switch (gameState.currentActivity) {
      case ACTIVITY_TYPES.SEQUENCE_PATTERNS.id:
        return renderSequencePatterns();
      case ACTIVITY_TYPES.GEOMETRIC_PATTERNS.id:
        return renderGeometricPatterns();
      case ACTIVITY_TYPES.COLOR_PATTERNS.id:
        return renderColorPatterns();
      case ACTIVITY_TYPES.SYMMETRY_PATTERNS.id:
        return renderSymmetryPatterns();
      case ACTIVITY_TYPES.COMPLETION_PATTERNS.id:
        return renderCompletionPatterns();
      default:
        return renderSequencePatterns();
    }
  };
  const renderSequencePatterns = () => {
    const activityData = gameState.activityData.sequence_patterns || {};
    const pattern = activityData.pattern || activityData.sequence || [];
    const options = activityData.options || [];
    if (!pattern.length) {
      return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionArea, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "Carregando padrão sequencial..." }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2098,
        columnNumber: 11
      }, this) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2097,
        columnNumber: 9
      }, this);
    }
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionArea, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionHeader, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: styles.questionTitle, children: "🔄 Padrões Sequenciais" }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2106,
        columnNumber: 11
      }, this) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2105,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.objectsDisplay, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        gap: "1rem",
        padding: "2rem",
        flexWrap: "wrap"
      }, children: [
        pattern.map((item, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
          textAlign: "center",
          padding: "1.5rem",
          border: "2px solid rgba(33, 150, 243, 0.6)",
          borderRadius: "16px",
          backgroundColor: "rgba(33, 150, 243, 0.2)",
          minWidth: "80px",
          minHeight: "80px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center"
        }, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "3rem", marginBottom: "0.5rem" }, children: item }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2133,
            columnNumber: 17
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.8rem", color: "rgba(255,255,255,0.7)" }, children: index + 1 }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2136,
            columnNumber: 17
          }, this)
        ] }, index, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2120,
          columnNumber: 15
        }, this)),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
          textAlign: "center",
          padding: "1.5rem",
          border: "3px dashed rgba(255, 193, 7, 0.8)",
          borderRadius: "16px",
          backgroundColor: "rgba(255, 193, 7, 0.2)",
          minWidth: "80px",
          minHeight: "80px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          animation: "pulse 2s infinite"
        }, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "3rem", marginBottom: "0.5rem" }, children: "❓" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2157,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.8rem", color: "rgba(255,255,255,0.7)" }, children: "?" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2160,
            columnNumber: 15
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2143,
          columnNumber: 13
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2111,
        columnNumber: 11
      }, this) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2110,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.answerOptions, children: options.map((option, index) => {
        const isSelected = activityData.userAnswer === option;
        const isCorrect = option === activityData.correctAnswer;
        const showResult = activityData.showResult;
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            className: `${styles.answerButton} ${isSelected ? styles.selected : ""} ${showResult && isCorrect ? styles.correct : ""} ${showResult && isSelected && !isCorrect ? styles.incorrect : ""}`,
            onClick: () => selectPattern(option),
            disabled: showResult,
            style: {
              fontSize: "2.5rem",
              minHeight: "100px",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center"
            },
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: option }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
                lineNumber: 2191,
                columnNumber: 17
              }, this),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.7rem", marginTop: "0.5rem" }, children: [
                "Opção ",
                String.fromCharCode(65 + index)
              ] }, void 0, true, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
                lineNumber: 2192,
                columnNumber: 17
              }, this)
            ]
          },
          index,
          true,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2175,
            columnNumber: 15
          },
          this
        );
      }) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2168,
        columnNumber: 9
      }, this),
      activityData.showResult && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
        textAlign: "center",
        marginTop: "2rem",
        padding: "1.5rem",
        backgroundColor: activityData.isCorrect ? "rgba(76, 175, 80, 0.2)" : "rgba(244, 67, 54, 0.2)",
        borderRadius: "12px",
        border: `2px solid ${activityData.isCorrect ? "rgba(76, 175, 80, 0.5)" : "rgba(244, 67, 54, 0.5)"}`
      }, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "1.5rem", marginBottom: "1rem" }, children: activityData.isCorrect ? "🎉 Correto!" : "💡 Tente novamente" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2210,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "1rem", marginBottom: "1rem", color: "white" }, children: activityData.explanation }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2213,
          columnNumber: 13
        }, this),
        !activityData.isCorrect && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "1rem", fontWeight: "bold", color: "white" }, children: [
          "Resposta correta: ",
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { style: { fontSize: "1.5rem" }, children: activityData.correctAnswer }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2218,
            columnNumber: 35
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2217,
          columnNumber: 15
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2202,
        columnNumber: 11
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
      lineNumber: 2104,
      columnNumber: 7
    }, this);
  };
  const renderGeometricPatterns = () => {
    const activityData = gameState.activityData.geometric_patterns || {};
    const pattern = activityData.pattern || activityData.sequence || [];
    const options = activityData.options || [];
    if (!pattern.length) {
      return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionArea, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "Carregando padrão geométrico..." }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2236,
        columnNumber: 11
      }, this) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2235,
        columnNumber: 9
      }, this);
    }
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionArea, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionHeader, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: styles.questionTitle, children: "🔺 Padrões Geométricos" }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2244,
        columnNumber: 11
      }, this) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2243,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.objectsDisplay, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        gap: "1rem",
        padding: "2rem",
        flexWrap: "wrap"
      }, children: [
        pattern.map((item, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
          textAlign: "center",
          padding: "1.5rem",
          border: "2px solid rgba(156, 39, 176, 0.6)",
          borderRadius: "16px",
          backgroundColor: "rgba(156, 39, 176, 0.2)",
          minWidth: "80px",
          minHeight: "80px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center"
        }, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "3rem", marginBottom: "0.5rem" }, children: item }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2271,
            columnNumber: 17
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.8rem", color: "rgba(255,255,255,0.7)" }, children: index + 1 }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2274,
            columnNumber: 17
          }, this)
        ] }, index, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2258,
          columnNumber: 15
        }, this)),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
          textAlign: "center",
          padding: "1.5rem",
          border: "3px dashed rgba(255, 193, 7, 0.8)",
          borderRadius: "16px",
          backgroundColor: "rgba(255, 193, 7, 0.2)",
          minWidth: "80px",
          minHeight: "80px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          animation: "pulse 2s infinite"
        }, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "3rem", marginBottom: "0.5rem" }, children: "❓" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2295,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.8rem", color: "rgba(255,255,255,0.7)" }, children: "?" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2298,
            columnNumber: 15
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2281,
          columnNumber: 13
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2249,
        columnNumber: 11
      }, this) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2248,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.answerOptions, children: options.map((option, index) => {
        const isSelected = activityData.userAnswer === option;
        const isCorrect = option === activityData.correctAnswer;
        const showResult = activityData.showResult;
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            className: `${styles.answerButton} ${isSelected ? styles.selected : ""} ${showResult && isCorrect ? styles.correct : ""} ${showResult && isSelected && !isCorrect ? styles.incorrect : ""}`,
            onClick: () => selectPattern(option),
            disabled: showResult,
            style: {
              fontSize: "2.5rem",
              minHeight: "100px",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center"
            },
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: option }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
                lineNumber: 2329,
                columnNumber: 17
              }, this),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.7rem", marginTop: "0.5rem" }, children: [
                "Opção ",
                String.fromCharCode(65 + index)
              ] }, void 0, true, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
                lineNumber: 2330,
                columnNumber: 17
              }, this)
            ]
          },
          index,
          true,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2313,
            columnNumber: 15
          },
          this
        );
      }) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2306,
        columnNumber: 9
      }, this),
      activityData.showResult && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
        textAlign: "center",
        marginTop: "2rem",
        padding: "1.5rem",
        backgroundColor: activityData.isCorrect ? "rgba(76, 175, 80, 0.2)" : "rgba(244, 67, 54, 0.2)",
        borderRadius: "12px",
        border: `2px solid ${activityData.isCorrect ? "rgba(76, 175, 80, 0.5)" : "rgba(244, 67, 54, 0.5)"}`
      }, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "1.5rem", marginBottom: "1rem" }, children: activityData.isCorrect ? "🎉 Correto!" : "💡 Tente novamente" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2348,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "1rem", marginBottom: "1rem", color: "white" }, children: activityData.explanation }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2351,
          columnNumber: 13
        }, this),
        !activityData.isCorrect && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "1rem", fontWeight: "bold", color: "white" }, children: [
          "Resposta correta: ",
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { style: { fontSize: "1.5rem" }, children: activityData.correctAnswer }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2356,
            columnNumber: 35
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2355,
          columnNumber: 15
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2340,
        columnNumber: 11
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
      lineNumber: 2242,
      columnNumber: 7
    }, this);
  };
  const selectPattern = (selectedOption) => {
    console.log("🎯 Padrão selecionado:", selectedOption);
    const currentActivity = gameState.currentActivity;
    const activityData = gameState.activityData[currentActivity] || {};
    if (activityData.showResult) {
      console.log("⚠️ Pergunta já foi respondida");
      return;
    }
    const correctAnswer = activityData.correctAnswer;
    const isCorrect = selectedOption === correctAnswer;
    console.log("✅ Resposta correta:", correctAnswer);
    console.log("🎯 Resposta do usuário:", selectedOption);
    console.log("📊 Está correto:", isCorrect);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        [currentActivity]: {
          ...prev.activityData[currentActivity],
          userAnswer: selectedOption,
          showResult: true,
          isCorrect
        }
      },
      score: isCorrect ? prev.score + 20 : prev.score,
      totalCorrect: isCorrect ? prev.totalCorrect + 1 : prev.totalCorrect,
      totalAttempts: prev.totalAttempts + 1
    }));
    if (isCorrect) {
      playFeedback("Excelente! Resposta correta!", "success");
    } else {
      playFeedback(`Não foi dessa vez. A resposta correta era ${correctAnswer}`, "error");
    }
    setTimeout(() => {
      const newRoundCount = gameState.activityRoundCount + 1;
      if (newRoundCount >= gameState.roundsPerActivity) {
        setGameState((prev) => ({
          ...prev,
          activityRoundCount: 0,
          // Reset para próxima atividade
          activityIndex: (prev.activityIndex + 1) % prev.activityCycle.length
        }));
        const nextActivityId = gameState.activityCycle[(gameState.activityIndex + 1) % gameState.activityCycle.length];
        changeActivity(nextActivityId);
      } else {
        setGameState((prev) => ({
          ...prev,
          activityRoundCount: newRoundCount
        }));
        generateNewRound(currentActivity);
      }
    }, 3e3);
  };
  const renderColorPatterns = () => {
    const activityData = gameState.activityData.color_patterns || {};
    const pattern = activityData.pattern || activityData.sequence || [];
    const options = activityData.options || [];
    if (!pattern.length) {
      return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionArea, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "Carregando padrão de cores..." }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2500,
        columnNumber: 11
      }, this) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2499,
        columnNumber: 9
      }, this);
    }
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionArea, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionHeader, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: styles.questionTitle, children: "🎨 Padrões de Cores" }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2508,
        columnNumber: 11
      }, this) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2507,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.objectsDisplay, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        gap: "1rem",
        padding: "2rem",
        flexWrap: "wrap"
      }, children: [
        pattern.map((item, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
          textAlign: "center",
          padding: "1.5rem",
          border: "2px solid rgba(255, 87, 34, 0.6)",
          borderRadius: "16px",
          backgroundColor: "rgba(255, 87, 34, 0.2)",
          minWidth: "80px",
          minHeight: "80px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center"
        }, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "3rem", marginBottom: "0.5rem" }, children: item }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2534,
            columnNumber: 17
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.8rem", color: "rgba(255,255,255,0.7)" }, children: index + 1 }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2537,
            columnNumber: 17
          }, this)
        ] }, index, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2521,
          columnNumber: 15
        }, this)),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
          textAlign: "center",
          padding: "1.5rem",
          border: "3px dashed rgba(255, 193, 7, 0.8)",
          borderRadius: "16px",
          backgroundColor: "rgba(255, 193, 7, 0.2)",
          minWidth: "80px",
          minHeight: "80px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          animation: "pulse 2s infinite"
        }, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "3rem", marginBottom: "0.5rem" }, children: "❓" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2557,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.8rem", color: "rgba(255,255,255,0.7)" }, children: "?" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2558,
            columnNumber: 15
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2543,
          columnNumber: 13
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2512,
        columnNumber: 11
      }, this) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2511,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.answerOptions, children: options.map((option, index) => {
        const isSelected = activityData.userAnswer === option;
        const isCorrect = option === activityData.correctAnswer;
        const showResult = activityData.showResult;
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            className: `${styles.answerButton} ${isSelected ? styles.selected : ""} ${showResult && isCorrect ? styles.correct : ""} ${showResult && isSelected && !isCorrect ? styles.incorrect : ""}`,
            onClick: () => selectPattern(option),
            disabled: showResult,
            style: {
              fontSize: "2.5rem",
              minHeight: "100px",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center"
            },
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: option }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
                lineNumber: 2586,
                columnNumber: 17
              }, this),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.7rem", marginTop: "0.5rem" }, children: [
                "Opção ",
                String.fromCharCode(65 + index)
              ] }, void 0, true, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
                lineNumber: 2587,
                columnNumber: 17
              }, this)
            ]
          },
          index,
          true,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2570,
            columnNumber: 15
          },
          this
        );
      }) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2563,
        columnNumber: 9
      }, this),
      activityData.showResult && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
        textAlign: "center",
        marginTop: "2rem",
        padding: "1.5rem",
        backgroundColor: activityData.isCorrect ? "rgba(76, 175, 80, 0.2)" : "rgba(244, 67, 54, 0.2)",
        borderRadius: "12px",
        border: `2px solid ${activityData.isCorrect ? "rgba(76, 175, 80, 0.5)" : "rgba(244, 67, 54, 0.5)"}`
      }, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "1.5rem", marginBottom: "1rem" }, children: activityData.isCorrect ? "🎉 Correto!" : "💡 Tente novamente" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2604,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "1rem", marginBottom: "1rem", color: "white" }, children: activityData.explanation }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2607,
          columnNumber: 13
        }, this),
        !activityData.isCorrect && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "1rem", fontWeight: "bold", color: "white" }, children: [
          "Resposta correta: ",
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { style: { fontSize: "1.5rem" }, children: activityData.correctAnswer }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2612,
            columnNumber: 35
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2611,
          columnNumber: 15
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2596,
        columnNumber: 11
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
      lineNumber: 2506,
      columnNumber: 7
    }, this);
  };
  const renderSymmetryPatterns = () => {
    const activityData = gameState.activityData.symmetry_patterns || {};
    const pattern = activityData.pattern || activityData.sequence || [];
    const options = activityData.options || [];
    if (!pattern.length) {
      return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionArea, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "Carregando padrão de simetria..." }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2630,
        columnNumber: 11
      }, this) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2629,
        columnNumber: 9
      }, this);
    }
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionArea, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionHeader, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: styles.questionTitle, children: "🔄 Padrões de Simetria" }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2638,
        columnNumber: 11
      }, this) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2637,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.objectsDisplay, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        gap: "1rem",
        padding: "2rem",
        flexWrap: "wrap"
      }, children: [
        pattern.map((item, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
          textAlign: "center",
          padding: "1.5rem",
          border: "2px solid rgba(103, 58, 183, 0.6)",
          borderRadius: "16px",
          backgroundColor: "rgba(103, 58, 183, 0.2)",
          minWidth: "80px",
          minHeight: "80px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center"
        }, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "3rem", marginBottom: "0.5rem" }, children: item }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2664,
            columnNumber: 17
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.8rem", color: "rgba(255,255,255,0.7)" }, children: index + 1 }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2667,
            columnNumber: 17
          }, this)
        ] }, index, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2651,
          columnNumber: 15
        }, this)),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
          textAlign: "center",
          padding: "1.5rem",
          border: "3px dashed rgba(255, 193, 7, 0.8)",
          borderRadius: "16px",
          backgroundColor: "rgba(255, 193, 7, 0.2)",
          minWidth: "80px",
          minHeight: "80px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          animation: "pulse 2s infinite"
        }, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "3rem", marginBottom: "0.5rem" }, children: "❓" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2687,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.8rem", color: "rgba(255,255,255,0.7)" }, children: "?" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2688,
            columnNumber: 15
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2673,
          columnNumber: 13
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2642,
        columnNumber: 11
      }, this) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2641,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.answerOptions, children: options.map((option, index) => {
        const isSelected = activityData.userAnswer === option;
        const isCorrect = option === activityData.correctAnswer;
        const showResult = activityData.showResult;
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            className: `${styles.answerButton} ${isSelected ? styles.selected : ""} ${showResult && isCorrect ? styles.correct : ""} ${showResult && isSelected && !isCorrect ? styles.incorrect : ""}`,
            onClick: () => selectPattern(option),
            disabled: showResult,
            style: {
              fontSize: "2.5rem",
              minHeight: "100px",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center"
            },
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: option }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
                lineNumber: 2716,
                columnNumber: 17
              }, this),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.7rem", marginTop: "0.5rem" }, children: [
                "Opção ",
                String.fromCharCode(65 + index)
              ] }, void 0, true, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
                lineNumber: 2717,
                columnNumber: 17
              }, this)
            ]
          },
          index,
          true,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2700,
            columnNumber: 15
          },
          this
        );
      }) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2693,
        columnNumber: 9
      }, this),
      activityData.showResult && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
        textAlign: "center",
        marginTop: "2rem",
        padding: "1.5rem",
        backgroundColor: activityData.isCorrect ? "rgba(76, 175, 80, 0.2)" : "rgba(244, 67, 54, 0.2)",
        borderRadius: "12px",
        border: `2px solid ${activityData.isCorrect ? "rgba(76, 175, 80, 0.5)" : "rgba(244, 67, 54, 0.5)"}`
      }, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "1.5rem", marginBottom: "1rem" }, children: activityData.isCorrect ? "🎉 Correto!" : "💡 Tente novamente" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2734,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "1rem", marginBottom: "1rem", color: "white" }, children: activityData.explanation }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2737,
          columnNumber: 13
        }, this),
        !activityData.isCorrect && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "1rem", fontWeight: "bold", color: "white" }, children: [
          "Resposta correta: ",
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { style: { fontSize: "1.5rem" }, children: activityData.correctAnswer }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2742,
            columnNumber: 35
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2741,
          columnNumber: 15
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2726,
        columnNumber: 11
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
      lineNumber: 2636,
      columnNumber: 7
    }, this);
  };
  const renderCompletionPatterns = () => {
    const activityData = gameState.activityData.completion_patterns || {};
    const pattern = activityData.pattern || activityData.sequence || [];
    const options = activityData.options || [];
    if (!pattern.length) {
      return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionArea, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "Carregando padrão de completar..." }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2760,
        columnNumber: 11
      }, this) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2759,
        columnNumber: 9
      }, this);
    }
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionArea, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionHeader, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: styles.questionTitle, children: "🧩 Padrões de Completar" }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2768,
        columnNumber: 11
      }, this) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2767,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.objectsDisplay, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        gap: "1rem",
        padding: "2rem",
        flexWrap: "wrap"
      }, children: [
        pattern.map((item, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
          textAlign: "center",
          padding: "1.5rem",
          border: "2px solid rgba(0, 150, 136, 0.6)",
          borderRadius: "16px",
          backgroundColor: "rgba(0, 150, 136, 0.2)",
          minWidth: "80px",
          minHeight: "80px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center"
        }, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "3rem", marginBottom: "0.5rem" }, children: item }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2794,
            columnNumber: 17
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.8rem", color: "rgba(255,255,255,0.7)" }, children: index + 1 }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2797,
            columnNumber: 17
          }, this)
        ] }, index, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2781,
          columnNumber: 15
        }, this)),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
          textAlign: "center",
          padding: "1.5rem",
          border: "3px dashed rgba(255, 193, 7, 0.8)",
          borderRadius: "16px",
          backgroundColor: "rgba(255, 193, 7, 0.2)",
          minWidth: "80px",
          minHeight: "80px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          animation: "pulse 2s infinite"
        }, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "3rem", marginBottom: "0.5rem" }, children: "❓" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2817,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.8rem", color: "rgba(255,255,255,0.7)" }, children: "?" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2818,
            columnNumber: 15
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2803,
          columnNumber: 13
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2772,
        columnNumber: 11
      }, this) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2771,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.answerOptions, children: options.map((option, index) => {
        const isSelected = activityData.userAnswer === option;
        const isCorrect = option === activityData.correctAnswer;
        const showResult = activityData.showResult;
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            className: `${styles.answerButton} ${isSelected ? styles.selected : ""} ${showResult && isCorrect ? styles.correct : ""} ${showResult && isSelected && !isCorrect ? styles.incorrect : ""}`,
            onClick: () => selectPattern(option),
            disabled: showResult,
            style: {
              fontSize: "2.5rem",
              minHeight: "100px",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center"
            },
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: option }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
                lineNumber: 2846,
                columnNumber: 17
              }, this),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.7rem", marginTop: "0.5rem" }, children: [
                "Opção ",
                String.fromCharCode(65 + index)
              ] }, void 0, true, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
                lineNumber: 2847,
                columnNumber: 17
              }, this)
            ]
          },
          index,
          true,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2830,
            columnNumber: 15
          },
          this
        );
      }) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2823,
        columnNumber: 9
      }, this),
      activityData.showResult && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
        textAlign: "center",
        marginTop: "2rem",
        padding: "1.5rem",
        backgroundColor: activityData.isCorrect ? "rgba(76, 175, 80, 0.2)" : "rgba(244, 67, 54, 0.2)",
        borderRadius: "12px",
        border: `2px solid ${activityData.isCorrect ? "rgba(76, 175, 80, 0.5)" : "rgba(244, 67, 54, 0.5)"}`
      }, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "1.5rem", marginBottom: "1rem" }, children: activityData.isCorrect ? "🎉 Correto!" : "💡 Tente novamente" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2864,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "1rem", marginBottom: "1rem", color: "white" }, children: activityData.explanation }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2867,
          columnNumber: 13
        }, this),
        !activityData.isCorrect && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "1rem", fontWeight: "bold", color: "white" }, children: [
          "Resposta correta: ",
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { style: { fontSize: "1.5rem" }, children: activityData.correctAnswer }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 2872,
            columnNumber: 35
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 2871,
          columnNumber: 15
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 2856,
        columnNumber: 11
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
      lineNumber: 2766,
      columnNumber: 7
    }, this);
  };
  if (showStartScreen) {
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      GameStartScreen,
      {
        gameTitle: "Padrões Visuais",
        gameDescription: "Desenvolva sua memória visual e sequencial",
        gameIcon: "🎯",
        difficulties: [
          {
            id: "easy",
            name: "Fácil",
            description: "Sequências de 3 formas\nIdeal para iniciantes",
            icon: "😊"
          },
          {
            id: "medium",
            name: "Médio",
            description: "Sequências de 4 formas\nDesafio equilibrado",
            icon: "🎯"
          },
          {
            id: "hard",
            name: "Avançado",
            description: "Sequências de 5 formas\nPara especialistas",
            icon: "🚀"
          }
        ],
        onStart: (difficulty2) => startGame(difficulty2),
        onBack
      },
      void 0,
      false,
      {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 3001,
        columnNumber: 7
      },
      this
    );
  }
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.padroesVisuaisGame, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.gameContent, children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.gameHeader, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h1", { className: styles.gameTitle, children: [
        "🎯 Padrões Visuais V3",
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.7rem", opacity: 0.8, marginTop: "0.25rem" }, children: Object.values(ACTIVITY_TYPES).find((type) => type.id === gameState.currentActivity)?.name || "Atividade" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 3039,
          columnNumber: 13
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 3037,
        columnNumber: 11
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          className: `${styles.headerTtsButton} ${ttsActive2 ? styles.ttsActive : ""} ${isSpeaking ? styles.speaking : ""}`,
          onClick: toggleTTS,
          title: ttsActive2 ? isSpeaking ? "TTS Falando - Clique para desativar" : "Desativar TTS" : "Ativar TTS",
          "aria-label": ttsActive2 ? isSpeaking ? "TTS Falando - Clique para desativar" : "Desativar TTS" : "Ativar TTS",
          children: isSpeaking ? "🎤" : ttsActive2 ? "🔊" : "🔇"
        },
        void 0,
        false,
        {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 3043,
          columnNumber: 11
        },
        this
      )
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
      lineNumber: 3036,
      columnNumber: 9
    }, this),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.gameStats, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statCard, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statValue, children: gameStats2.score }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 3056,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statLabel, children: "Pontos" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 3057,
          columnNumber: 13
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 3055,
        columnNumber: 11
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statCard, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statValue, children: currentLevel }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 3060,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statLabel, children: "Nível" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 3061,
          columnNumber: 13
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 3059,
        columnNumber: 11
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statCard, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statValue, children: [
          getAccuracy(),
          "%"
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 3064,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statLabel, children: "Precisão" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
          lineNumber: 3065,
          columnNumber: 13
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 3063,
        columnNumber: 11
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
      lineNumber: 3054,
      columnNumber: 9
    }, this),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.activityMenu, children: Object.values(ACTIVITY_TYPES).map((activity) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
      "button",
      {
        className: `${styles.activityButton} ${gameState.currentActivity === activity.id ? styles.active : ""}`,
        onClick: () => changeActivity(activity.id),
        children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: activity.icon }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 3079,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: activity.name }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
            lineNumber: 3080,
            columnNumber: 15
          }, this)
        ]
      },
      activity.id,
      true,
      {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 3072,
        columnNumber: 13
      },
      this
    )) }, void 0, false, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
      lineNumber: 3070,
      columnNumber: 9
    }, this),
    renderActivityInterface(),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.gameControls, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("button", { className: styles.controlButton, onClick: explainGame, children: "🔊 Explicar" }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 3092,
        columnNumber: 11
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("button", { className: styles.controlButton, onClick: () => {
        if (gameSequence.length > 0) {
          showSequence();
        } else {
          generateNewRound();
        }
      }, children: [
        "🔄 ",
        gameSequence.length > 0 ? "Ver Sequência" : "Nova Sequência"
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 3095,
        columnNumber: 11
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("button", { className: styles.controlButton, onClick: onBack, children: "⬅️ Voltar" }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
        lineNumber: 3104,
        columnNumber: 11
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
      lineNumber: 3091,
      columnNumber: 9
    }, this)
  ] }, void 0, true, {
    fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
    lineNumber: 3034,
    columnNumber: 7
  }, this) }, void 0, false, {
    fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/PadroesVisuais/PadroesVisuaisGame.jsx",
    lineNumber: 3033,
    columnNumber: 5
  }, this);
}
const PadroesVisuaisGame$1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: PadroesVisuaisGame
}, Symbol.toStringTag, { value: "Module" }));
export {
  PadroesVisuaisProcessors as P,
  PadroesVisuaisCollectorsHub as a,
  PadroesVisuaisGame$1 as b
};
//# sourceMappingURL=game-patterns-C1u1YIjS.js.map
