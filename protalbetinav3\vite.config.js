import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";
import { visualizer } from "rollup-plugin-visualizer";

// Mock para databaseInstance
const mockDatabaseInstance = `export default {
  getInstance: () => Promise.resolve({
    mock: true,
    manager: {
      query: () => Promise.resolve([]),
      getStatus: () => ({ status: 'mock', connected: true })
    },
    getStatus: () => ({ status: 'mock', connected: true }),
    saveGameMetrics: () => Promise.resolve({ success: true })
  }),
  getStatus: () => ({ status: 'mock', connected: true }),
  saveGameMetrics: () => Promise.resolve({ success: true })
}`;

// Plugin para resolver databaseInstance
const virtualModules = {
  "../../../database/services/databaseInstance.js": mockDatabaseInstance,
  "../../../../database/services/databaseInstance.js": mockDatabaseInstance,
  "../database/services/databaseInstance.js": mockDatabaseInstance,
  "../../database/services/databaseInstance.js": mockDatabaseInstance,
  "database/services/databaseInstance.js": mockDatabaseInstance,
};

export default defineConfig({
  // Server configuration - SIMPLIFICADO
  server: {
    port: 5173,
    host: "0.0.0.0",
    proxy: {
      "/api": "http://api:3000",
    },
  },

  // Resolve configuration
  resolve: {
    extensions: [".js", ".jsx", ".ts", ".tsx"],
    alias: {
      "@": resolve(__dirname, "src"),
    },
  },

  // Build configuration
  build: {
    outDir: "dist",
    assetsDir: "assets",
    minify: false, // Desabilitado temporariamente para debug
    sourcemap: true, // Changed to true for better debugging
    emptyOutDir: true,
    target: "es2022", // Unified target with esbuild
    cssCodeSplit: true,
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        chunkFileNames: "assets/[name]-[hash].js",
        entryFileNames: "assets/[name]-[hash].js",
        assetFileNames: "assets/[name]-[hash].[ext]",
        manualChunks(id) {
          // Vendor chunks
          if (id.includes("node_modules")) {
            if (id.includes("react") || id.includes("react-dom"))
              return "vendor-react";
            if (id.includes("react-router")) return "vendor-router";
            if (id.includes("framer-motion")) return "vendor-motion";
            if (id.includes("chart.js") || id.includes("react-chartjs"))
              return "vendor-charts";
            if (id.includes("@tanstack")) return "vendor-query";
            if (
              id.includes("lodash") ||
              id.includes("date-fns") ||
              id.includes("uuid")
            )
              return "vendor-utils";
            return "vendor-misc";
          }

          // Game-specific chunks
          if (id.includes("/games/")) {
            if (id.includes("MemoryGame")) return "game-memory";
            if (id.includes("ColorMatch")) return "game-colors";
            if (id.includes("CreativePainting")) return "game-creative";
            if (id.includes("PadroesVisuais")) return "game-patterns";
            if (id.includes("ContagemNumeros")) return "game-numbers";
            if (id.includes("LetterRecognition")) return "game-letters";
            if (id.includes("ImageAssociation")) return "game-association";
            if (id.includes("MusicalSequence")) return "game-musical";
            if (id.includes("QuebraCabeca")) return "game-puzzle";
          }

          // Other specific chunks
          if (id.includes("collectors")) return "collectors";
          if (id.includes("admin") || id.includes("AdminPanel")) return "admin";
          if (id.includes("dashboard")) return "dashboard";
          if (id.includes("database")) return "database";
          if (id.includes("utils")) return "utils";
          if (id.includes("hooks")) return "hooks";
          if (id.includes("api") || id.includes("services")) return "services";
          if (id.includes("context")) return "context";
        },
      },
    },
  },

  // Dependency optimization
  optimizeDeps: {
    include: [
      "react",
      "react-dom",
      "react-router-dom",
      "framer-motion",
      "uuid",
      "axios",
    ],
    exclude: [
      "./src/games/MemoryGame/collectors",
      "./src/games/ColorMatch/collectors",
      "./src/components/admin/AdminPanel",
    ],
    esbuildOptions: {
      target: "es2022",
      supported: {
        "top-level-await": true,
      },
    },
  },

  // ESBuild configuration - CORRIGIDO
  esbuild: {
    jsx: "transform",
    jsxFactory: "React.createElement",
    jsxFragment: "React.Fragment",
  },

  // Plugins
  plugins: [
    // React plugin - SIMPLIFICADO
    react(),

    // MIME type fix middleware
    {
      name: "mime-fix",
      configureServer(server) {
        server.middlewares.use((req, res, next) => {
          const url = req.url || "";
          if (
            url.endsWith(".jsx") ||
            url.endsWith(".js") ||
            url.endsWith(".mjs") ||
            url.includes(".jsx?") ||
            url.includes(".js?") ||
            url.includes("/@modules/") ||
            url.includes("/@vite/") ||
            url.includes("/src/")
          ) {
            res.setHeader(
              "Content-Type",
              "application/javascript; charset=utf-8"
            );
          } else if (url.endsWith(".css") || url.includes(".css?")) {
            res.setHeader("Content-Type", "text/css; charset=utf-8");
          }
          next();
        });
      },
    },

    // Plugin para simular módulos do backend
    {
      name: "virtual-modules",
      resolveId(id) {
        if (virtualModules[id]) return id;

        if (id.includes("databaseInstance.js")) {
          const keys = Object.keys(virtualModules);
          for (const key of keys) {
            if (id.endsWith(key)) {
              return key;
            }
          }
          return Object.keys(virtualModules)[0];
        }
        return null;
      },
      load(id) {
        if (virtualModules[id]) return virtualModules[id];
        return null;
      },
    },

    // Bundle visualizer (optional, only for analysis)
    visualizer({
      open: false, // Set to true to auto-open visualizer in browser
      filename: "dist/stats.html",
      gzipSize: true,
      brotliSize: true,
    }),
  ],
});
