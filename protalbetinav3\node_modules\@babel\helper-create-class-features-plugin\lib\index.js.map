{"version": 3, "names": ["_core", "require", "_semver", "_fields", "_decorators", "_decorators2", "_misc", "_features", "_typescript", "version<PERSON>ey", "createClassFeaturePlugin", "name", "feature", "loose", "manipulateOptions", "api", "inherits", "decoratorVersion", "_api$assumption", "FEATURES", "decorators", "createDecoratorTransform", "assumption", "setPublicClassFields", "privateFieldsAsSymbols", "privateFieldsAsProperties", "noUninitializedPrivateFieldAccess", "constant<PERSON>uper", "noDocumentAll", "Error", "privateFieldsAsSymbolsOrProperties", "explicit", "undefined", "push", "length", "console", "warn", "join", "pre", "file", "enableFeature", "get", "set", "semver", "lt", "visitor", "Class", "path", "shouldTransform", "pathIsClassDeclaration", "isClassDeclaration", "assertFieldTransformed", "isLoose", "constructor", "isDecorated", "hasDecorators", "node", "props", "elements", "computedPaths", "privateNames", "Set", "body", "isClassProperty", "isClassMethod", "computed", "isPrivate", "key", "id", "getName", "setName", "isClassPrivateMethod", "kind", "has", "buildCodeFrameError", "add", "isProperty", "isStaticBlock", "innerBinding", "ref", "_path$ensureFunctionN", "ensureFunctionName", "NodePath", "prototype", "scope", "generateUidIdentifier", "classRefForDefine", "t", "cloneNode", "privateNamesMap", "buildPrivateNamesMap", "privateNamesNodes", "buildPrivateNamesNodes", "transformPrivateNamesUsage", "keysNodes", "staticNodes", "instanceNodes", "lastInstanceNodeReturnsThis", "pureStaticNodes", "classBindingNode", "wrapClass", "buildDecoratedClass", "extractComputedKeys", "buildFieldsInitNodes", "superClass", "injectInitialization", "referenceVisitor", "state", "prop", "static", "traverse", "<PERSON><PERSON><PERSON>", "insertBefore", "insertAfter", "find", "parent", "isStatement", "isDeclaration", "ExportDefaultDeclaration", "decl", "_path$splitExportDecl", "splitExportDeclaration", "type"], "sources": ["../src/index.ts"], "sourcesContent": ["import { types as t } from \"@babel/core\";\nimport type { PluginAPI, PluginObject, NodePath } from \"@babel/core\";\n\nimport semver from \"semver\";\n\nimport {\n  buildPrivateNamesNodes,\n  buildPrivateNamesMap,\n  transformPrivateNamesUsage,\n  buildFieldsInitNodes,\n  buildCheckInRHS,\n} from \"./fields.ts\";\nimport type { PropPath } from \"./fields.ts\";\nimport createDecoratorTransform, { hasDecorators } from \"./decorators.ts\";\nimport type { DecoratorVersionKind } from \"./decorators.ts\";\nimport { buildDecoratedClass } from \"./decorators-2018-09.ts\" with { if: \"!process.env.BABEL_8_BREAKING\" };\nimport { injectInitialization, extractComputedKeys } from \"./misc.ts\";\nimport {\n  enableFeature,\n  FEATURES,\n  isLoose,\n  shouldTransform,\n} from \"./features.ts\";\nimport { assertFieldTransformed } from \"./typescript.ts\";\n\nexport { FEATURES, enableFeature, injectInitialization, buildCheckInRHS };\n\nconst versionKey = \"@babel/plugin-class-features/version\";\n\ninterface Options {\n  name: string;\n  feature: number;\n  loose?: boolean;\n  inherits?: PluginObject[\"inherits\"];\n  manipulateOptions?: PluginObject[\"manipulateOptions\"];\n  api?: PluginAPI;\n  decoratorVersion?: DecoratorVersionKind | \"2018-09\";\n}\n\nexport function createClassFeaturePlugin({\n  name,\n  feature,\n  loose,\n  manipulateOptions,\n  api,\n  inherits,\n  decoratorVersion,\n}: Options): PluginObject {\n  if (feature & FEATURES.decorators) {\n    if (process.env.BABEL_8_BREAKING) {\n      return createDecoratorTransform(api, { loose }, \"2023-11\", inherits);\n    } else {\n      if (\n        decoratorVersion === \"2023-11\" ||\n        decoratorVersion === \"2023-05\" ||\n        decoratorVersion === \"2023-01\" ||\n        decoratorVersion === \"2022-03\" ||\n        decoratorVersion === \"2021-12\"\n      ) {\n        return createDecoratorTransform(\n          api,\n          { loose },\n          decoratorVersion,\n          inherits,\n        );\n      }\n    }\n  }\n  if (!process.env.BABEL_8_BREAKING) {\n    api ??= { assumption: () => void 0 as any } as any;\n  }\n  const setPublicClassFields = api.assumption(\"setPublicClassFields\");\n  const privateFieldsAsSymbols = api.assumption(\"privateFieldsAsSymbols\");\n  const privateFieldsAsProperties = api.assumption(\"privateFieldsAsProperties\");\n  const noUninitializedPrivateFieldAccess =\n    api.assumption(\"noUninitializedPrivateFieldAccess\") ?? false;\n  const constantSuper = api.assumption(\"constantSuper\");\n  const noDocumentAll = api.assumption(\"noDocumentAll\");\n\n  if (privateFieldsAsProperties && privateFieldsAsSymbols) {\n    throw new Error(\n      `Cannot enable both the \"privateFieldsAsProperties\" and ` +\n        `\"privateFieldsAsSymbols\" assumptions as the same time.`,\n    );\n  }\n  const privateFieldsAsSymbolsOrProperties =\n    privateFieldsAsProperties || privateFieldsAsSymbols;\n\n  if (loose === true) {\n    type AssumptionName = Parameters<PluginAPI[\"assumption\"]>[0];\n    const explicit: `\"${AssumptionName}\"`[] = [];\n\n    if (setPublicClassFields !== undefined) {\n      explicit.push(`\"setPublicClassFields\"`);\n    }\n    if (privateFieldsAsProperties !== undefined) {\n      explicit.push(`\"privateFieldsAsProperties\"`);\n    }\n    if (privateFieldsAsSymbols !== undefined) {\n      explicit.push(`\"privateFieldsAsSymbols\"`);\n    }\n    if (explicit.length !== 0) {\n      console.warn(\n        `[${name}]: You are using the \"loose: true\" option and you are` +\n          ` explicitly setting a value for the ${explicit.join(\" and \")}` +\n          ` assumption${explicit.length > 1 ? \"s\" : \"\"}. The \"loose\" option` +\n          ` can cause incompatibilities with the other class features` +\n          ` plugins, so it's recommended that you replace it with the` +\n          ` following top-level option:\\n` +\n          `\\t\"assumptions\": {\\n` +\n          `\\t\\t\"setPublicClassFields\": true,\\n` +\n          `\\t\\t\"privateFieldsAsSymbols\": true\\n` +\n          `\\t}`,\n      );\n    }\n  }\n\n  return {\n    name,\n    manipulateOptions,\n    inherits,\n\n    pre(file) {\n      enableFeature(file, feature, loose);\n\n      if (!process.env.BABEL_8_BREAKING) {\n        // Until 7.21.4, we used to encode the version as a number.\n        // If file.get(versionKey) is a number, it has thus been\n        // set by an older version of this plugin.\n        if (typeof file.get(versionKey) === \"number\") {\n          file.set(versionKey, PACKAGE_JSON.version);\n          return;\n        }\n      }\n      if (\n        !file.get(versionKey) ||\n        semver.lt(file.get(versionKey), PACKAGE_JSON.version)\n      ) {\n        file.set(versionKey, PACKAGE_JSON.version);\n      }\n    },\n\n    visitor: {\n      Class(path, { file }) {\n        if (file.get(versionKey) !== PACKAGE_JSON.version) return;\n\n        if (!shouldTransform(path, file)) return;\n\n        const pathIsClassDeclaration = path.isClassDeclaration();\n\n        if (pathIsClassDeclaration) assertFieldTransformed(path);\n\n        const loose = isLoose(file, feature);\n\n        let constructor: NodePath<t.ClassMethod>;\n        const isDecorated = hasDecorators(path.node);\n        const props: PropPath[] = [];\n        const elements = [];\n        const computedPaths: NodePath<t.ClassProperty | t.ClassMethod>[] = [];\n        const privateNames = new Set<string>();\n        const body = path.get(\"body\");\n\n        for (const path of body.get(\"body\")) {\n          if (\n            // check path.node.computed is enough, but ts will complain\n            (path.isClassProperty() || path.isClassMethod()) &&\n            path.node.computed\n          ) {\n            computedPaths.push(path);\n          }\n\n          if (path.isPrivate()) {\n            const { name } = path.node.key.id;\n            const getName = `get ${name}`;\n            const setName = `set ${name}`;\n\n            if (path.isClassPrivateMethod()) {\n              if (path.node.kind === \"get\") {\n                if (\n                  privateNames.has(getName) ||\n                  (privateNames.has(name) && !privateNames.has(setName))\n                ) {\n                  throw path.buildCodeFrameError(\"Duplicate private field\");\n                }\n                privateNames.add(getName).add(name);\n              } else if (path.node.kind === \"set\") {\n                if (\n                  privateNames.has(setName) ||\n                  (privateNames.has(name) && !privateNames.has(getName))\n                ) {\n                  throw path.buildCodeFrameError(\"Duplicate private field\");\n                }\n                privateNames.add(setName).add(name);\n              }\n            } else {\n              if (\n                (privateNames.has(name) &&\n                  !privateNames.has(getName) &&\n                  !privateNames.has(setName)) ||\n                (privateNames.has(name) &&\n                  (privateNames.has(getName) || privateNames.has(setName)))\n              ) {\n                throw path.buildCodeFrameError(\"Duplicate private field\");\n              }\n\n              privateNames.add(name);\n            }\n          }\n\n          if (path.isClassMethod({ kind: \"constructor\" })) {\n            constructor = path;\n          } else {\n            elements.push(path);\n            if (\n              path.isProperty() ||\n              path.isPrivate() ||\n              path.isStaticBlock?.()\n            ) {\n              props.push(path as PropPath);\n            }\n          }\n        }\n\n        if (process.env.BABEL_8_BREAKING) {\n          if (!props.length) return;\n        } else {\n          if (!props.length && !isDecorated) return;\n        }\n\n        const innerBinding = path.node.id;\n        let ref: t.Identifier | null;\n        if (!innerBinding || !pathIsClassDeclaration) {\n          if (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n            // polyfill when being run by an older Babel version\n            path.ensureFunctionName ??=\n              // eslint-disable-next-line no-restricted-globals\n              require(\"@babel/traverse\").NodePath.prototype.ensureFunctionName;\n          }\n          (path as NodePath<t.ClassExpression>).ensureFunctionName(false);\n          ref = path.scope.generateUidIdentifier(innerBinding?.name || \"Class\");\n        }\n        const classRefForDefine = ref ?? t.cloneNode(innerBinding);\n\n        const privateNamesMap = buildPrivateNamesMap(\n          classRefForDefine.name,\n          privateFieldsAsSymbolsOrProperties ?? loose,\n          props,\n          file,\n        );\n        const privateNamesNodes = buildPrivateNamesNodes(\n          privateNamesMap,\n          privateFieldsAsProperties ?? loose,\n          privateFieldsAsSymbols ?? false,\n          file,\n        );\n\n        transformPrivateNamesUsage(\n          classRefForDefine,\n          path,\n          privateNamesMap,\n          {\n            privateFieldsAsProperties:\n              privateFieldsAsSymbolsOrProperties ?? loose,\n            noUninitializedPrivateFieldAccess,\n            noDocumentAll,\n            innerBinding,\n          },\n          file,\n        );\n\n        let keysNodes: t.Statement[],\n          staticNodes: t.Statement[],\n          instanceNodes: t.ExpressionStatement[],\n          lastInstanceNodeReturnsThis: boolean,\n          pureStaticNodes: t.FunctionDeclaration[],\n          classBindingNode: t.Statement | null,\n          wrapClass: (path: NodePath<t.Class>) => NodePath;\n\n        if (!process.env.BABEL_8_BREAKING) {\n          if (isDecorated) {\n            staticNodes = pureStaticNodes = keysNodes = [];\n            ({ instanceNodes, wrapClass } = buildDecoratedClass(\n              classRefForDefine,\n              path,\n              elements,\n              file,\n            ));\n          } else {\n            keysNodes = extractComputedKeys(path, computedPaths, file);\n            ({\n              staticNodes,\n              pureStaticNodes,\n              instanceNodes,\n              lastInstanceNodeReturnsThis,\n              classBindingNode,\n              wrapClass,\n            } = buildFieldsInitNodes(\n              ref,\n              path.node.superClass,\n              props,\n              privateNamesMap,\n              file,\n              setPublicClassFields ?? loose,\n              privateFieldsAsSymbolsOrProperties ?? loose,\n              noUninitializedPrivateFieldAccess,\n              constantSuper ?? loose,\n              innerBinding,\n            ));\n          }\n        } else {\n          keysNodes = extractComputedKeys(path, computedPaths, file);\n          ({\n            staticNodes,\n            pureStaticNodes,\n            instanceNodes,\n            lastInstanceNodeReturnsThis,\n            classBindingNode,\n            wrapClass,\n          } = buildFieldsInitNodes(\n            ref,\n            path.node.superClass,\n            props,\n            privateNamesMap,\n            file,\n            setPublicClassFields ?? loose,\n            privateFieldsAsSymbolsOrProperties ?? loose,\n            noUninitializedPrivateFieldAccess,\n            constantSuper ?? loose,\n            innerBinding,\n          ));\n        }\n\n        if (instanceNodes.length > 0) {\n          injectInitialization(\n            path,\n            constructor,\n            instanceNodes,\n            (referenceVisitor, state) => {\n              if (!process.env.BABEL_8_BREAKING) {\n                if (isDecorated) return;\n              }\n              for (const prop of props) {\n                // @ts-expect-error: TS doesn't infer that prop.node is not a StaticBlock\n                if (t.isStaticBlock?.(prop.node) || prop.node.static) continue;\n                prop.traverse(referenceVisitor, state);\n              }\n            },\n            lastInstanceNodeReturnsThis,\n          );\n        }\n\n        // rename to make ts happy\n        const wrappedPath = wrapClass(path);\n        wrappedPath.insertBefore([...privateNamesNodes, ...keysNodes]);\n        if (staticNodes.length > 0) {\n          wrappedPath.insertAfter(staticNodes);\n        }\n        if (pureStaticNodes.length > 0) {\n          wrappedPath\n            .find(parent => parent.isStatement() || parent.isDeclaration())\n            .insertAfter(pureStaticNodes);\n        }\n        if (classBindingNode != null && pathIsClassDeclaration) {\n          wrappedPath.insertAfter(classBindingNode);\n        }\n      },\n\n      ExportDefaultDeclaration(path, { file }) {\n        if (!process.env.BABEL_8_BREAKING) {\n          if (file.get(versionKey) !== PACKAGE_JSON.version) return;\n\n          const decl = path.get(\"declaration\");\n\n          if (decl.isClassDeclaration() && hasDecorators(decl.node)) {\n            if (decl.node.id) {\n              // export default class Foo {}\n              //   -->\n              // class Foo {} export { Foo as default }\n              if (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n                // polyfill when being run by an older Babel version\n                path.splitExportDeclaration ??=\n                  // eslint-disable-next-line no-restricted-globals\n                  require(\"@babel/traverse\").NodePath.prototype.splitExportDeclaration;\n              }\n              path.splitExportDeclaration();\n            } else {\n              // @ts-expect-error Anonymous class declarations can be\n              // transformed as if they were expressions\n              decl.node.type = \"ClassExpression\";\n            }\n          }\n        }\n      },\n    },\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAGA,IAAAC,OAAA,GAAAD,OAAA;AAEA,IAAAE,OAAA,GAAAF,OAAA;AAQA,IAAAG,WAAA,GAAAH,OAAA;AAEA,IAAAI,YAAA,GAAAJ,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,SAAA,GAAAN,OAAA;AAMA,IAAAO,WAAA,GAAAP,OAAA;AAIA,MAAMQ,UAAU,GAAG,sCAAsC;AAYlD,SAASC,wBAAwBA,CAAC;EACvCC,IAAI;EACJC,OAAO;EACPC,KAAK;EACLC,iBAAiB;EACjBC,GAAG;EACHC,QAAQ;EACRC;AACO,CAAC,EAAgB;EAAA,IAAAC,eAAA;EACxB,IAAIN,OAAO,GAAGO,kBAAQ,CAACC,UAAU,EAAE;IAG1B;MACL,IACEH,gBAAgB,KAAK,SAAS,IAC9BA,gBAAgB,KAAK,SAAS,IAC9BA,gBAAgB,KAAK,SAAS,IAC9BA,gBAAgB,KAAK,SAAS,IAC9BA,gBAAgB,KAAK,SAAS,EAC9B;QACA,OAAO,IAAAI,mBAAwB,EAC7BN,GAAG,EACH;UAAEF;QAAM,CAAC,EACTI,gBAAgB,EAChBD,QACF,CAAC;MACH;IACF;EACF;EACmC;IACjCD,GAAG,WAAHA,GAAG,GAAHA,GAAG,GAAK;MAAEO,UAAU,EAAEA,CAAA,KAAM,KAAK;IAAS,CAAC;EAC7C;EACA,MAAMC,oBAAoB,GAAGR,GAAG,CAACO,UAAU,CAAC,sBAAsB,CAAC;EACnE,MAAME,sBAAsB,GAAGT,GAAG,CAACO,UAAU,CAAC,wBAAwB,CAAC;EACvE,MAAMG,yBAAyB,GAAGV,GAAG,CAACO,UAAU,CAAC,2BAA2B,CAAC;EAC7E,MAAMI,iCAAiC,IAAAR,eAAA,GACrCH,GAAG,CAACO,UAAU,CAAC,mCAAmC,CAAC,YAAAJ,eAAA,GAAI,KAAK;EAC9D,MAAMS,aAAa,GAAGZ,GAAG,CAACO,UAAU,CAAC,eAAe,CAAC;EACrD,MAAMM,aAAa,GAAGb,GAAG,CAACO,UAAU,CAAC,eAAe,CAAC;EAErD,IAAIG,yBAAyB,IAAID,sBAAsB,EAAE;IACvD,MAAM,IAAIK,KAAK,CACb,yDAAyD,GACvD,wDACJ,CAAC;EACH;EACA,MAAMC,kCAAkC,GACtCL,yBAAyB,IAAID,sBAAsB;EAErD,IAAIX,KAAK,KAAK,IAAI,EAAE;IAElB,MAAMkB,QAAiC,GAAG,EAAE;IAE5C,IAAIR,oBAAoB,KAAKS,SAAS,EAAE;MACtCD,QAAQ,CAACE,IAAI,CAAC,wBAAwB,CAAC;IACzC;IACA,IAAIR,yBAAyB,KAAKO,SAAS,EAAE;MAC3CD,QAAQ,CAACE,IAAI,CAAC,6BAA6B,CAAC;IAC9C;IACA,IAAIT,sBAAsB,KAAKQ,SAAS,EAAE;MACxCD,QAAQ,CAACE,IAAI,CAAC,0BAA0B,CAAC;IAC3C;IACA,IAAIF,QAAQ,CAACG,MAAM,KAAK,CAAC,EAAE;MACzBC,OAAO,CAACC,IAAI,CACV,IAAIzB,IAAI,uDAAuD,GAC7D,uCAAuCoB,QAAQ,CAACM,IAAI,CAAC,OAAO,CAAC,EAAE,GAC/D,cAAcN,QAAQ,CAACG,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,sBAAsB,GAClE,4DAA4D,GAC5D,4DAA4D,GAC5D,gCAAgC,GAChC,sBAAsB,GACtB,qCAAqC,GACrC,sCAAsC,GACtC,KACJ,CAAC;IACH;EACF;EAEA,OAAO;IACLvB,IAAI;IACJG,iBAAiB;IACjBE,QAAQ;IAERsB,GAAGA,CAACC,IAAI,EAAE;MACR,IAAAC,uBAAa,EAACD,IAAI,EAAE3B,OAAO,EAAEC,KAAK,CAAC;MAEA;QAIjC,IAAI,OAAO0B,IAAI,CAACE,GAAG,CAAChC,UAAU,CAAC,KAAK,QAAQ,EAAE;UAC5C8B,IAAI,CAACG,GAAG,CAACjC,UAAU,UAAsB,CAAC;UAC1C;QACF;MACF;MACA,IACE,CAAC8B,IAAI,CAACE,GAAG,CAAChC,UAAU,CAAC,IACrBkC,OAAM,CAACC,EAAE,CAACL,IAAI,CAACE,GAAG,CAAChC,UAAU,CAAC,UAAsB,CAAC,EACrD;QACA8B,IAAI,CAACG,GAAG,CAACjC,UAAU,UAAsB,CAAC;MAC5C;IACF,CAAC;IAEDoC,OAAO,EAAE;MACPC,KAAKA,CAACC,IAAI,EAAE;QAAER;MAAK,CAAC,EAAE;QACpB,IAAIA,IAAI,CAACE,GAAG,CAAChC,UAAU,CAAC,aAAyB,EAAE;QAEnD,IAAI,CAAC,IAAAuC,yBAAe,EAACD,IAAI,EAAER,IAAI,CAAC,EAAE;QAElC,MAAMU,sBAAsB,GAAGF,IAAI,CAACG,kBAAkB,CAAC,CAAC;QAExD,IAAID,sBAAsB,EAAE,IAAAE,kCAAsB,EAACJ,IAAI,CAAC;QAExD,MAAMlC,KAAK,GAAG,IAAAuC,iBAAO,EAACb,IAAI,EAAE3B,OAAO,CAAC;QAEpC,IAAIyC,WAAoC;QACxC,MAAMC,WAAW,GAAG,IAAAC,yBAAa,EAACR,IAAI,CAACS,IAAI,CAAC;QAC5C,MAAMC,KAAiB,GAAG,EAAE;QAC5B,MAAMC,QAAQ,GAAG,EAAE;QACnB,MAAMC,aAA0D,GAAG,EAAE;QACrE,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAAS,CAAC;QACtC,MAAMC,IAAI,GAAGf,IAAI,CAACN,GAAG,CAAC,MAAM,CAAC;QAE7B,KAAK,MAAMM,IAAI,IAAIe,IAAI,CAACrB,GAAG,CAAC,MAAM,CAAC,EAAE;UACnC,IAEE,CAACM,IAAI,CAACgB,eAAe,CAAC,CAAC,IAAIhB,IAAI,CAACiB,aAAa,CAAC,CAAC,KAC/CjB,IAAI,CAACS,IAAI,CAACS,QAAQ,EAClB;YACAN,aAAa,CAAC1B,IAAI,CAACc,IAAI,CAAC;UAC1B;UAEA,IAAIA,IAAI,CAACmB,SAAS,CAAC,CAAC,EAAE;YACpB,MAAM;cAAEvD;YAAK,CAAC,GAAGoC,IAAI,CAACS,IAAI,CAACW,GAAG,CAACC,EAAE;YACjC,MAAMC,OAAO,GAAG,OAAO1D,IAAI,EAAE;YAC7B,MAAM2D,OAAO,GAAG,OAAO3D,IAAI,EAAE;YAE7B,IAAIoC,IAAI,CAACwB,oBAAoB,CAAC,CAAC,EAAE;cAC/B,IAAIxB,IAAI,CAACS,IAAI,CAACgB,IAAI,KAAK,KAAK,EAAE;gBAC5B,IACEZ,YAAY,CAACa,GAAG,CAACJ,OAAO,CAAC,IACxBT,YAAY,CAACa,GAAG,CAAC9D,IAAI,CAAC,IAAI,CAACiD,YAAY,CAACa,GAAG,CAACH,OAAO,CAAE,EACtD;kBACA,MAAMvB,IAAI,CAAC2B,mBAAmB,CAAC,yBAAyB,CAAC;gBAC3D;gBACAd,YAAY,CAACe,GAAG,CAACN,OAAO,CAAC,CAACM,GAAG,CAAChE,IAAI,CAAC;cACrC,CAAC,MAAM,IAAIoC,IAAI,CAACS,IAAI,CAACgB,IAAI,KAAK,KAAK,EAAE;gBACnC,IACEZ,YAAY,CAACa,GAAG,CAACH,OAAO,CAAC,IACxBV,YAAY,CAACa,GAAG,CAAC9D,IAAI,CAAC,IAAI,CAACiD,YAAY,CAACa,GAAG,CAACJ,OAAO,CAAE,EACtD;kBACA,MAAMtB,IAAI,CAAC2B,mBAAmB,CAAC,yBAAyB,CAAC;gBAC3D;gBACAd,YAAY,CAACe,GAAG,CAACL,OAAO,CAAC,CAACK,GAAG,CAAChE,IAAI,CAAC;cACrC;YACF,CAAC,MAAM;cACL,IACGiD,YAAY,CAACa,GAAG,CAAC9D,IAAI,CAAC,IACrB,CAACiD,YAAY,CAACa,GAAG,CAACJ,OAAO,CAAC,IAC1B,CAACT,YAAY,CAACa,GAAG,CAACH,OAAO,CAAC,IAC3BV,YAAY,CAACa,GAAG,CAAC9D,IAAI,CAAC,KACpBiD,YAAY,CAACa,GAAG,CAACJ,OAAO,CAAC,IAAIT,YAAY,CAACa,GAAG,CAACH,OAAO,CAAC,CAAE,EAC3D;gBACA,MAAMvB,IAAI,CAAC2B,mBAAmB,CAAC,yBAAyB,CAAC;cAC3D;cAEAd,YAAY,CAACe,GAAG,CAAChE,IAAI,CAAC;YACxB;UACF;UAEA,IAAIoC,IAAI,CAACiB,aAAa,CAAC;YAAEQ,IAAI,EAAE;UAAc,CAAC,CAAC,EAAE;YAC/CnB,WAAW,GAAGN,IAAI;UACpB,CAAC,MAAM;YACLW,QAAQ,CAACzB,IAAI,CAACc,IAAI,CAAC;YACnB,IACEA,IAAI,CAAC6B,UAAU,CAAC,CAAC,IACjB7B,IAAI,CAACmB,SAAS,CAAC,CAAC,IAChBnB,IAAI,CAAC8B,aAAa,YAAlB9B,IAAI,CAAC8B,aAAa,CAAG,CAAC,EACtB;cACApB,KAAK,CAACxB,IAAI,CAACc,IAAgB,CAAC;YAC9B;UACF;QACF;QAIO;UACL,IAAI,CAACU,KAAK,CAACvB,MAAM,IAAI,CAACoB,WAAW,EAAE;QACrC;QAEA,MAAMwB,YAAY,GAAG/B,IAAI,CAACS,IAAI,CAACY,EAAE;QACjC,IAAIW,GAAwB;QAC5B,IAAI,CAACD,YAAY,IAAI,CAAC7B,sBAAsB,EAAE;UACqB;YAAA,IAAA+B,qBAAA;YAE/D,CAAAA,qBAAA,GAAAjC,IAAI,CAACkC,kBAAkB,YAAAD,qBAAA,GAAvBjC,IAAI,CAACkC,kBAAkB,GAErBhF,OAAO,CAAC,iBAAiB,CAAC,CAACiF,QAAQ,CAACC,SAAS,CAACF,kBAAkB;UACpE;UACClC,IAAI,CAAiCkC,kBAAkB,CAAC,KAAK,CAAC;UAC/DF,GAAG,GAAGhC,IAAI,CAACqC,KAAK,CAACC,qBAAqB,CAAC,CAAAP,YAAY,oBAAZA,YAAY,CAAEnE,IAAI,KAAI,OAAO,CAAC;QACvE;QACA,MAAM2E,iBAAiB,GAAGP,GAAG,WAAHA,GAAG,GAAIQ,WAAC,CAACC,SAAS,CAACV,YAAY,CAAC;QAE1D,MAAMW,eAAe,GAAG,IAAAC,4BAAoB,EAC1CJ,iBAAiB,CAAC3E,IAAI,EACtBmB,kCAAkC,WAAlCA,kCAAkC,GAAIjB,KAAK,EAC3C4C,KAAK,EACLlB,IACF,CAAC;QACD,MAAMoD,iBAAiB,GAAG,IAAAC,8BAAsB,EAC9CH,eAAe,EACfhE,yBAAyB,WAAzBA,yBAAyB,GAAIZ,KAAK,EAClCW,sBAAsB,WAAtBA,sBAAsB,GAAI,KAAK,EAC/Be,IACF,CAAC;QAED,IAAAsD,kCAA0B,EACxBP,iBAAiB,EACjBvC,IAAI,EACJ0C,eAAe,EACf;UACEhE,yBAAyB,EACvBK,kCAAkC,WAAlCA,kCAAkC,GAAIjB,KAAK;UAC7Ca,iCAAiC;UACjCE,aAAa;UACbkD;QACF,CAAC,EACDvC,IACF,CAAC;QAED,IAAIuD,SAAwB,EAC1BC,WAA0B,EAC1BC,aAAsC,EACtCC,2BAAoC,EACpCC,eAAwC,EACxCC,gBAAoC,EACpCC,SAAgD;QAEf;UACjC,IAAI9C,WAAW,EAAE;YACfyC,WAAW,GAAGG,eAAe,GAAGJ,SAAS,GAAG,EAAE;YAC9C,CAAC;cAAEE,aAAa;cAAEI;YAAU,CAAC,GAAG,IAAAC,gCAAmB,EACjDf,iBAAiB,EACjBvC,IAAI,EACJW,QAAQ,EACRnB,IACF,CAAC;UACH,CAAC,MAAM;YACLuD,SAAS,GAAG,IAAAQ,yBAAmB,EAACvD,IAAI,EAAEY,aAAa,EAAEpB,IAAI,CAAC;YAC1D,CAAC;cACCwD,WAAW;cACXG,eAAe;cACfF,aAAa;cACbC,2BAA2B;cAC3BE,gBAAgB;cAChBC;YACF,CAAC,GAAG,IAAAG,4BAAoB,EACtBxB,GAAG,EACHhC,IAAI,CAACS,IAAI,CAACgD,UAAU,EACpB/C,KAAK,EACLgC,eAAe,EACflD,IAAI,EACJhB,oBAAoB,WAApBA,oBAAoB,GAAIV,KAAK,EAC7BiB,kCAAkC,WAAlCA,kCAAkC,GAAIjB,KAAK,EAC3Ca,iCAAiC,EACjCC,aAAa,WAAbA,aAAa,GAAId,KAAK,EACtBiE,YACF,CAAC;UACH;QACF;QAuBA,IAAIkB,aAAa,CAAC9D,MAAM,GAAG,CAAC,EAAE;UAC5B,IAAAuE,0BAAoB,EAClB1D,IAAI,EACJM,WAAW,EACX2C,aAAa,EACb,CAACU,gBAAgB,EAAEC,KAAK,KAAK;YACQ;cACjC,IAAIrD,WAAW,EAAE;YACnB;YACA,KAAK,MAAMsD,IAAI,IAAInD,KAAK,EAAE;cAExB,IAAI8B,WAAC,CAACV,aAAa,YAAfU,WAAC,CAACV,aAAa,CAAG+B,IAAI,CAACpD,IAAI,CAAC,IAAIoD,IAAI,CAACpD,IAAI,CAACqD,MAAM,EAAE;cACtDD,IAAI,CAACE,QAAQ,CAACJ,gBAAgB,EAAEC,KAAK,CAAC;YACxC;UACF,CAAC,EACDV,2BACF,CAAC;QACH;QAGA,MAAMc,WAAW,GAAGX,SAAS,CAACrD,IAAI,CAAC;QACnCgE,WAAW,CAACC,YAAY,CAAC,CAAC,GAAGrB,iBAAiB,EAAE,GAAGG,SAAS,CAAC,CAAC;QAC9D,IAAIC,WAAW,CAAC7D,MAAM,GAAG,CAAC,EAAE;UAC1B6E,WAAW,CAACE,WAAW,CAAClB,WAAW,CAAC;QACtC;QACA,IAAIG,eAAe,CAAChE,MAAM,GAAG,CAAC,EAAE;UAC9B6E,WAAW,CACRG,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACC,WAAW,CAAC,CAAC,IAAID,MAAM,CAACE,aAAa,CAAC,CAAC,CAAC,CAC9DJ,WAAW,CAACf,eAAe,CAAC;QACjC;QACA,IAAIC,gBAAgB,IAAI,IAAI,IAAIlD,sBAAsB,EAAE;UACtD8D,WAAW,CAACE,WAAW,CAACd,gBAAgB,CAAC;QAC3C;MACF,CAAC;MAEDmB,wBAAwBA,CAACvE,IAAI,EAAE;QAAER;MAAK,CAAC,EAAE;QACJ;UACjC,IAAIA,IAAI,CAACE,GAAG,CAAChC,UAAU,CAAC,aAAyB,EAAE;UAEnD,MAAM8G,IAAI,GAAGxE,IAAI,CAACN,GAAG,CAAC,aAAa,CAAC;UAEpC,IAAI8E,IAAI,CAACrE,kBAAkB,CAAC,CAAC,IAAI,IAAAK,yBAAa,EAACgE,IAAI,CAAC/D,IAAI,CAAC,EAAE;YACzD,IAAI+D,IAAI,CAAC/D,IAAI,CAACY,EAAE,EAAE;cAIiD;gBAAA,IAAAoD,qBAAA;gBAE/D,CAAAA,qBAAA,GAAAzE,IAAI,CAAC0E,sBAAsB,YAAAD,qBAAA,GAA3BzE,IAAI,CAAC0E,sBAAsB,GAEzBxH,OAAO,CAAC,iBAAiB,CAAC,CAACiF,QAAQ,CAACC,SAAS,CAACsC,sBAAsB;cACxE;cACA1E,IAAI,CAAC0E,sBAAsB,CAAC,CAAC;YAC/B,CAAC,MAAM;cAGLF,IAAI,CAAC/D,IAAI,CAACkE,IAAI,GAAG,iBAAiB;YACpC;UACF;QACF;MACF;IACF;EACF,CAAC;AACH", "ignoreList": []}