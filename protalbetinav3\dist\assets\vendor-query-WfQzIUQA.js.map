{"version": 3, "file": "vendor-query-WfQzIUQA.js", "sources": ["../../node_modules/@tanstack/query-core/build/modern/subscribable.js", "../../node_modules/@tanstack/query-core/build/modern/utils.js", "../../node_modules/@tanstack/query-core/build/modern/focusManager.js", "../../node_modules/@tanstack/query-core/build/modern/onlineManager.js", "../../node_modules/@tanstack/query-core/build/modern/thenable.js", "../../node_modules/@tanstack/query-core/build/modern/retryer.js", "../../node_modules/@tanstack/query-core/build/modern/notifyManager.js", "../../node_modules/@tanstack/query-core/build/modern/removable.js", "../../node_modules/@tanstack/query-core/build/modern/query.js", "../../node_modules/@tanstack/query-core/build/modern/queryCache.js", "../../node_modules/@tanstack/query-core/build/modern/mutation.js", "../../node_modules/@tanstack/query-core/build/modern/mutationCache.js", "../../node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js", "../../node_modules/@tanstack/query-core/build/modern/queryClient.js"], "sourcesContent": ["// src/subscribable.ts\nvar Subscribable = class {\n  constructor() {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n  subscribe(listener) {\n    this.listeners.add(listener);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(listener);\n      this.onUnsubscribe();\n    };\n  }\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n  onSubscribe() {\n  }\n  onUnsubscribe() {\n  }\n};\nexport {\n  Subscribable\n};\n//# sourceMappingURL=subscribable.js.map", "// src/utils.ts\nvar isServer = typeof window === \"undefined\" || \"Deno\" in globalThis;\nfunction noop() {\n}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === \"function\" ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === \"number\" && value >= 0 && value !== Infinity;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction resolveStaleTime(staleTime, query) {\n  return typeof staleTime === \"function\" ? staleTime(query) : staleTime;\n}\nfunction resolveEnabled(enabled, query) {\n  return typeof enabled === \"function\" ? enabled(query) : enabled;\n}\nfunction matchQuery(filters, query) {\n  const {\n    type = \"all\",\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale\n  } = filters;\n  if (queryKey) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n  if (type !== \"all\") {\n    const isActive = query.isActive();\n    if (type === \"active\" && !isActive) {\n      return false;\n    }\n    if (type === \"inactive\" && isActive) {\n      return false;\n    }\n  }\n  if (typeof stale === \"boolean\" && query.isStale() !== stale) {\n    return false;\n  }\n  if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n    return false;\n  }\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  const { exact, status, predicate, mutationKey } = filters;\n  if (mutationKey) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n    if (exact) {\n      if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n  if (status && mutation.state.status !== status) {\n    return false;\n  }\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  const hashFn = options?.queryKeyHashFn || hashKey;\n  return hashFn(queryKey);\n}\nfunction hashKey(queryKey) {\n  return JSON.stringify(\n    queryKey,\n    (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n      result[key] = val[key];\n      return result;\n    }, {}) : val\n  );\n}\nfunction partialMatchKey(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n    return Object.keys(b).every((key) => partialMatchKey(a[key], b[key]));\n  }\n  return false;\n}\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n  const array = isPlainArray(a) && isPlainArray(b);\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    const aItems = array ? a : Object.keys(a);\n    const aSize = aItems.length;\n    const bItems = array ? b : Object.keys(b);\n    const bSize = bItems.length;\n    const copy = array ? [] : {};\n    const aItemsSet = new Set(aItems);\n    let equalItems = 0;\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i];\n      if ((!array && aItemsSet.has(key) || array) && a[key] === void 0 && b[key] === void 0) {\n        copy[key] = void 0;\n        equalItems++;\n      } else {\n        copy[key] = replaceEqualDeep(a[key], b[key]);\n        if (copy[key] === a[key] && a[key] !== void 0) {\n          equalItems++;\n        }\n      }\n    }\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n  return b;\n}\nfunction shallowEqualObjects(a, b) {\n  if (!b || Object.keys(a).length !== Object.keys(b).length) {\n    return false;\n  }\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isPlainArray(value) {\n  return Array.isArray(value) && value.length === Object.keys(value).length;\n}\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  }\n  const ctor = o.constructor;\n  if (ctor === void 0) {\n    return true;\n  }\n  const prot = ctor.prototype;\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  }\n  if (!prot.hasOwnProperty(\"isPrototypeOf\")) {\n    return false;\n  }\n  if (Object.getPrototypeOf(o) !== Object.prototype) {\n    return false;\n  }\n  return true;\n}\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === \"[object Object]\";\n}\nfunction sleep(timeout) {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout);\n  });\n}\nfunction replaceData(prevData, data, options) {\n  if (typeof options.structuralSharing === \"function\") {\n    return options.structuralSharing(prevData, data);\n  } else if (options.structuralSharing !== false) {\n    if (process.env.NODE_ENV !== \"production\") {\n      try {\n        return replaceEqualDeep(prevData, data);\n      } catch (error) {\n        console.error(\n          `Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`\n        );\n        throw error;\n      }\n    }\n    return replaceEqualDeep(prevData, data);\n  }\n  return data;\n}\nfunction keepPreviousData(previousData) {\n  return previousData;\n}\nfunction addToEnd(items, item, max = 0) {\n  const newItems = [...items, item];\n  return max && newItems.length > max ? newItems.slice(1) : newItems;\n}\nfunction addToStart(items, item, max = 0) {\n  const newItems = [item, ...items];\n  return max && newItems.length > max ? newItems.slice(0, -1) : newItems;\n}\nvar skipToken = Symbol();\nfunction ensureQueryFn(options, fetchOptions) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (options.queryFn === skipToken) {\n      console.error(\n        `Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`\n      );\n    }\n  }\n  if (!options.queryFn && fetchOptions?.initialPromise) {\n    return () => fetchOptions.initialPromise;\n  }\n  if (!options.queryFn || options.queryFn === skipToken) {\n    return () => Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`));\n  }\n  return options.queryFn;\n}\nfunction shouldThrowError(throwOnError, params) {\n  if (typeof throwOnError === \"function\") {\n    return throwOnError(...params);\n  }\n  return !!throwOnError;\n}\nexport {\n  addToEnd,\n  addToStart,\n  ensureQueryFn,\n  functionalUpdate,\n  hashKey,\n  hashQueryKeyByOptions,\n  isPlainArray,\n  isPlainObject,\n  isServer,\n  isValidTimeout,\n  keepPreviousData,\n  matchMutation,\n  matchQuery,\n  noop,\n  partialMatchKey,\n  replaceData,\n  replaceEqualDeep,\n  resolveEnabled,\n  resolveStaleTime,\n  shallowEqualObjects,\n  shouldThrowError,\n  skipToken,\n  sleep,\n  timeUntilStale\n};\n//# sourceMappingURL=utils.js.map", "// src/focusManager.ts\nimport { Subscribable } from \"./subscribable.js\";\nimport { isServer } from \"./utils.js\";\nvar FocusManager = class extends Subscribable {\n  #focused;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onFocus) => {\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus();\n        window.addEventListener(\"visibilitychange\", listener, false);\n        return () => {\n          window.removeEventListener(\"visibilitychange\", listener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup((focused) => {\n      if (typeof focused === \"boolean\") {\n        this.setFocused(focused);\n      } else {\n        this.onFocus();\n      }\n    });\n  }\n  setFocused(focused) {\n    const changed = this.#focused !== focused;\n    if (changed) {\n      this.#focused = focused;\n      this.onFocus();\n    }\n  }\n  onFocus() {\n    const isFocused = this.isFocused();\n    this.listeners.forEach((listener) => {\n      listener(isFocused);\n    });\n  }\n  isFocused() {\n    if (typeof this.#focused === \"boolean\") {\n      return this.#focused;\n    }\n    return globalThis.document?.visibilityState !== \"hidden\";\n  }\n};\nvar focusManager = new FocusManager();\nexport {\n  FocusManager,\n  focusManager\n};\n//# sourceMappingURL=focusManager.js.map", "// src/onlineManager.ts\nimport { Subscribable } from \"./subscribable.js\";\nimport { isServer } from \"./utils.js\";\nvar OnlineManager = class extends Subscribable {\n  #online = true;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onOnline) => {\n      if (!isServer && window.addEventListener) {\n        const onlineListener = () => onOnline(true);\n        const offlineListener = () => onOnline(false);\n        window.addEventListener(\"online\", onlineListener, false);\n        window.addEventListener(\"offline\", offlineListener, false);\n        return () => {\n          window.removeEventListener(\"online\", onlineListener);\n          window.removeEventListener(\"offline\", offlineListener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup(this.setOnline.bind(this));\n  }\n  setOnline(online) {\n    const changed = this.#online !== online;\n    if (changed) {\n      this.#online = online;\n      this.listeners.forEach((listener) => {\n        listener(online);\n      });\n    }\n  }\n  isOnline() {\n    return this.#online;\n  }\n};\nvar onlineManager = new OnlineManager();\nexport {\n  OnlineManager,\n  onlineManager\n};\n//# sourceMappingURL=onlineManager.js.map", "// src/thenable.ts\nimport { noop } from \"./utils.js\";\nfunction pendingThenable() {\n  let resolve;\n  let reject;\n  const thenable = new Promise((_resolve, _reject) => {\n    resolve = _resolve;\n    reject = _reject;\n  });\n  thenable.status = \"pending\";\n  thenable.catch(() => {\n  });\n  function finalize(data) {\n    Object.assign(thenable, data);\n    delete thenable.resolve;\n    delete thenable.reject;\n  }\n  thenable.resolve = (value) => {\n    finalize({\n      status: \"fulfilled\",\n      value\n    });\n    resolve(value);\n  };\n  thenable.reject = (reason) => {\n    finalize({\n      status: \"rejected\",\n      reason\n    });\n    reject(reason);\n  };\n  return thenable;\n}\nfunction tryResolveSync(promise) {\n  let data;\n  promise.then((result) => {\n    data = result;\n    return result;\n  }, noop)?.catch(noop);\n  if (data !== void 0) {\n    return { data };\n  }\n  return void 0;\n}\nexport {\n  pendingThenable,\n  tryResolveSync\n};\n//# sourceMappingURL=thenable.js.map", "// src/retryer.ts\nimport { focusManager } from \"./focusManager.js\";\nimport { onlineManager } from \"./onlineManager.js\";\nimport { pendingThenable } from \"./thenable.js\";\nimport { isServer, sleep } from \"./utils.js\";\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1e3 * 2 ** failureCount, 3e4);\n}\nfunction canFetch(networkMode) {\n  return (networkMode ?? \"online\") === \"online\" ? onlineManager.isOnline() : true;\n}\nvar CancelledError = class extends Error {\n  constructor(options) {\n    super(\"CancelledError\");\n    this.revert = options?.revert;\n    this.silent = options?.silent;\n  }\n};\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n  let isRetryCancelled = false;\n  let failureCount = 0;\n  let isResolved = false;\n  let continueFn;\n  const thenable = pendingThenable();\n  const cancel = (cancelOptions) => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions));\n      config.abort?.();\n    }\n  };\n  const cancelRetry = () => {\n    isRetryCancelled = true;\n  };\n  const continueRetry = () => {\n    isRetryCancelled = false;\n  };\n  const canContinue = () => focusManager.isFocused() && (config.networkMode === \"always\" || onlineManager.isOnline()) && config.canRun();\n  const canStart = () => canFetch(config.networkMode) && config.canRun();\n  const resolve = (value) => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onSuccess?.(value);\n      continueFn?.();\n      thenable.resolve(value);\n    }\n  };\n  const reject = (value) => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onError?.(value);\n      continueFn?.();\n      thenable.reject(value);\n    }\n  };\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        if (isResolved || canContinue()) {\n          continueResolve(value);\n        }\n      };\n      config.onPause?.();\n    }).then(() => {\n      continueFn = void 0;\n      if (!isResolved) {\n        config.onContinue?.();\n      }\n    });\n  };\n  const run = () => {\n    if (isResolved) {\n      return;\n    }\n    let promiseOrValue;\n    const initialPromise = failureCount === 0 ? config.initialPromise : void 0;\n    try {\n      promiseOrValue = initialPromise ?? config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    }\n    Promise.resolve(promiseOrValue).then(resolve).catch((error) => {\n      if (isResolved) {\n        return;\n      }\n      const retry = config.retry ?? (isServer ? 0 : 3);\n      const retryDelay = config.retryDelay ?? defaultRetryDelay;\n      const delay = typeof retryDelay === \"function\" ? retryDelay(failureCount, error) : retryDelay;\n      const shouldRetry = retry === true || typeof retry === \"number\" && failureCount < retry || typeof retry === \"function\" && retry(failureCount, error);\n      if (isRetryCancelled || !shouldRetry) {\n        reject(error);\n        return;\n      }\n      failureCount++;\n      config.onFail?.(failureCount, error);\n      sleep(delay).then(() => {\n        return canContinue() ? void 0 : pause();\n      }).then(() => {\n        if (isRetryCancelled) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  };\n  return {\n    promise: thenable,\n    cancel,\n    continue: () => {\n      continueFn?.();\n      return thenable;\n    },\n    cancelRetry,\n    continueRetry,\n    canStart,\n    start: () => {\n      if (canStart()) {\n        run();\n      } else {\n        pause().then(run);\n      }\n      return thenable;\n    }\n  };\n}\nexport {\n  CancelledError,\n  canFetch,\n  createRetryer,\n  isCancelledError\n};\n//# sourceMappingURL=retryer.js.map", "// src/notifyManager.ts\nvar defaultScheduler = (cb) => setTimeout(cb, 0);\nfunction createNotifyManager() {\n  let queue = [];\n  let transactions = 0;\n  let notifyFn = (callback) => {\n    callback();\n  };\n  let batchNotifyFn = (callback) => {\n    callback();\n  };\n  let scheduleFn = defaultScheduler;\n  const schedule = (callback) => {\n    if (transactions) {\n      queue.push(callback);\n    } else {\n      scheduleFn(() => {\n        notifyFn(callback);\n      });\n    }\n  };\n  const flush = () => {\n    const originalQueue = queue;\n    queue = [];\n    if (originalQueue.length) {\n      scheduleFn(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback);\n          });\n        });\n      });\n    }\n  };\n  return {\n    batch: (callback) => {\n      let result;\n      transactions++;\n      try {\n        result = callback();\n      } finally {\n        transactions--;\n        if (!transactions) {\n          flush();\n        }\n      }\n      return result;\n    },\n    /**\n     * All calls to the wrapped function will be batched.\n     */\n    batchCalls: (callback) => {\n      return (...args) => {\n        schedule(() => {\n          callback(...args);\n        });\n      };\n    },\n    schedule,\n    /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */\n    setNotifyFunction: (fn) => {\n      notifyFn = fn;\n    },\n    /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */\n    setBatchNotifyFunction: (fn) => {\n      batchNotifyFn = fn;\n    },\n    setScheduler: (fn) => {\n      scheduleFn = fn;\n    }\n  };\n}\nvar notifyManager = createNotifyManager();\nexport {\n  createNotifyManager,\n  defaultScheduler,\n  notifyManager\n};\n//# sourceMappingURL=notifyManager.js.map", "// src/removable.ts\nimport { isServer, isValidTimeout } from \"./utils.js\";\nvar Removable = class {\n  #gcTimeout;\n  destroy() {\n    this.clearGcTimeout();\n  }\n  scheduleGc() {\n    this.clearGcTimeout();\n    if (isValidTimeout(this.gcTime)) {\n      this.#gcTimeout = setTimeout(() => {\n        this.optionalRemove();\n      }, this.gcTime);\n    }\n  }\n  updateGcTime(newGcTime) {\n    this.gcTime = Math.max(\n      this.gcTime || 0,\n      newGcTime ?? (isServer ? Infinity : 5 * 60 * 1e3)\n    );\n  }\n  clearGcTimeout() {\n    if (this.#gcTimeout) {\n      clearTimeout(this.#gcTimeout);\n      this.#gcTimeout = void 0;\n    }\n  }\n};\nexport {\n  Removable\n};\n//# sourceMappingURL=removable.js.map", "// src/query.ts\nimport {\n  ensureQueryFn,\n  noop,\n  replaceData,\n  resolveEnabled,\n  resolveStaleTime,\n  skipToken,\n  timeUntilStale\n} from \"./utils.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { canFetch, createRetryer, isCancelledError } from \"./retryer.js\";\nimport { Removable } from \"./removable.js\";\nvar Query = class extends Removable {\n  #initialState;\n  #revertState;\n  #cache;\n  #client;\n  #retryer;\n  #defaultOptions;\n  #abortSignalConsumed;\n  constructor(config) {\n    super();\n    this.#abortSignalConsumed = false;\n    this.#defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.#client = config.client;\n    this.#cache = this.#client.getQueryCache();\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.#initialState = getDefaultState(this.options);\n    this.state = config.state ?? this.#initialState;\n    this.scheduleGc();\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  get promise() {\n    return this.#retryer?.promise;\n  }\n  setOptions(options) {\n    this.options = { ...this.#defaultOptions, ...options };\n    this.updateGcTime(this.options.gcTime);\n  }\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n      this.#cache.remove(this);\n    }\n  }\n  setData(newData, options) {\n    const data = replaceData(this.state.data, newData, this.options);\n    this.#dispatch({\n      data,\n      type: \"success\",\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual\n    });\n    return data;\n  }\n  setState(state, setStateOptions) {\n    this.#dispatch({ type: \"setState\", state, setStateOptions });\n  }\n  cancel(options) {\n    const promise = this.#retryer?.promise;\n    this.#retryer?.cancel(options);\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve();\n  }\n  destroy() {\n    super.destroy();\n    this.cancel({ silent: true });\n  }\n  reset() {\n    this.destroy();\n    this.setState(this.#initialState);\n  }\n  isActive() {\n    return this.observers.some(\n      (observer) => resolveEnabled(observer.options.enabled, this) !== false\n    );\n  }\n  isDisabled() {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive();\n    }\n    return this.options.queryFn === skipToken || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;\n  }\n  isStatic() {\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => resolveStaleTime(observer.options.staleTime, this) === \"static\"\n      );\n    }\n    return false;\n  }\n  isStale() {\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => observer.getCurrentResult().isStale\n      );\n    }\n    return this.state.data === void 0 || this.state.isInvalidated;\n  }\n  isStaleByTime(staleTime = 0) {\n    if (this.state.data === void 0) {\n      return true;\n    }\n    if (staleTime === \"static\") {\n      return false;\n    }\n    if (this.state.isInvalidated) {\n      return true;\n    }\n    return !timeUntilStale(this.state.dataUpdatedAt, staleTime);\n  }\n  onFocus() {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  onOnline() {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer);\n      this.clearGcTimeout();\n      this.#cache.notify({ type: \"observerAdded\", query: this, observer });\n    }\n  }\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer);\n      if (!this.observers.length) {\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({ revert: true });\n          } else {\n            this.#retryer.cancelRetry();\n          }\n        }\n        this.scheduleGc();\n      }\n      this.#cache.notify({ type: \"observerRemoved\", query: this, observer });\n    }\n  }\n  getObserversCount() {\n    return this.observers.length;\n  }\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({ type: \"invalidate\" });\n    }\n  }\n  fetch(options, fetchOptions) {\n    if (this.state.fetchStatus !== \"idle\") {\n      if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n        this.cancel({ silent: true });\n      } else if (this.#retryer) {\n        this.#retryer.continueRetry();\n        return this.#retryer.promise;\n      }\n    }\n    if (options) {\n      this.setOptions(options);\n    }\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn);\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`\n        );\n      }\n    }\n    const abortController = new AbortController();\n    const addSignalProperty = (object) => {\n      Object.defineProperty(object, \"signal\", {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true;\n          return abortController.signal;\n        }\n      });\n    };\n    const fetchFn = () => {\n      const queryFn = ensureQueryFn(this.options, fetchOptions);\n      const createQueryFnContext = () => {\n        const queryFnContext2 = {\n          client: this.#client,\n          queryKey: this.queryKey,\n          meta: this.meta\n        };\n        addSignalProperty(queryFnContext2);\n        return queryFnContext2;\n      };\n      const queryFnContext = createQueryFnContext();\n      this.#abortSignalConsumed = false;\n      if (this.options.persister) {\n        return this.options.persister(\n          queryFn,\n          queryFnContext,\n          this\n        );\n      }\n      return queryFn(queryFnContext);\n    };\n    const createFetchContext = () => {\n      const context2 = {\n        fetchOptions,\n        options: this.options,\n        queryKey: this.queryKey,\n        client: this.#client,\n        state: this.state,\n        fetchFn\n      };\n      addSignalProperty(context2);\n      return context2;\n    };\n    const context = createFetchContext();\n    this.options.behavior?.onFetch(context, this);\n    this.#revertState = this.state;\n    if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n      this.#dispatch({ type: \"fetch\", meta: context.fetchOptions?.meta });\n    }\n    const onError = (error) => {\n      if (!(isCancelledError(error) && error.silent)) {\n        this.#dispatch({\n          type: \"error\",\n          error\n        });\n      }\n      if (!isCancelledError(error)) {\n        this.#cache.config.onError?.(\n          error,\n          this\n        );\n        this.#cache.config.onSettled?.(\n          this.state.data,\n          error,\n          this\n        );\n      }\n      this.scheduleGc();\n    };\n    this.#retryer = createRetryer({\n      initialPromise: fetchOptions?.initialPromise,\n      fn: context.fetchFn,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (data === void 0) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`\n            );\n          }\n          onError(new Error(`${this.queryHash} data is undefined`));\n          return;\n        }\n        try {\n          this.setData(data);\n        } catch (error) {\n          onError(error);\n          return;\n        }\n        this.#cache.config.onSuccess?.(data, this);\n        this.#cache.config.onSettled?.(\n          data,\n          this.state.error,\n          this\n        );\n        this.scheduleGc();\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue: () => {\n        this.#dispatch({ type: \"continue\" });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true\n    });\n    return this.#retryer.start();\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            fetchStatus: \"paused\"\n          };\n        case \"continue\":\n          return {\n            ...state,\n            fetchStatus: \"fetching\"\n          };\n        case \"fetch\":\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: \"success\",\n            ...!action.manual && {\n              fetchStatus: \"idle\",\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            }\n          };\n        case \"error\":\n          const error = action.error;\n          if (isCancelledError(error) && error.revert && this.#revertState) {\n            return { ...this.#revertState, fetchStatus: \"idle\" };\n          }\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: \"idle\",\n            status: \"error\"\n          };\n        case \"invalidate\":\n          return {\n            ...state,\n            isInvalidated: true\n          };\n        case \"setState\":\n          return {\n            ...state,\n            ...action.state\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate();\n      });\n      this.#cache.notify({ query: this, type: \"updated\", action });\n    });\n  }\n};\nfunction fetchState(data, options) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: canFetch(options.networkMode) ? \"fetching\" : \"paused\",\n    ...data === void 0 && {\n      error: null,\n      status: \"pending\"\n    }\n  };\n}\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n  const hasData = data !== void 0;\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? \"success\" : \"pending\",\n    fetchStatus: \"idle\"\n  };\n}\nexport {\n  Query,\n  fetchState\n};\n//# sourceMappingURL=query.js.map", "// src/queryCache.ts\nimport { hashQueryKeyByOptions, matchQuery } from \"./utils.js\";\nimport { Query } from \"./query.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Subscribable } from \"./subscribable.js\";\nvar QueryCache = class extends Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#queries = /* @__PURE__ */ new Map();\n  }\n  #queries;\n  build(client, options, state) {\n    const queryKey = options.queryKey;\n    const queryHash = options.queryHash ?? hashQueryKeyByOptions(queryKey, options);\n    let query = this.get(queryHash);\n    if (!query) {\n      query = new Query({\n        client,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey)\n      });\n      this.add(query);\n    }\n    return query;\n  }\n  add(query) {\n    if (!this.#queries.has(query.queryHash)) {\n      this.#queries.set(query.queryHash, query);\n      this.notify({\n        type: \"added\",\n        query\n      });\n    }\n  }\n  remove(query) {\n    const queryInMap = this.#queries.get(query.queryHash);\n    if (queryInMap) {\n      query.destroy();\n      if (queryInMap === query) {\n        this.#queries.delete(query.queryHash);\n      }\n      this.notify({ type: \"removed\", query });\n    }\n  }\n  clear() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        this.remove(query);\n      });\n    });\n  }\n  get(queryHash) {\n    return this.#queries.get(queryHash);\n  }\n  getAll() {\n    return [...this.#queries.values()];\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (query) => matchQuery(defaultedFilters, query)\n    );\n  }\n  findAll(filters = {}) {\n    const queries = this.getAll();\n    return Object.keys(filters).length > 0 ? queries.filter((query) => matchQuery(filters, query)) : queries;\n  }\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  onFocus() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onFocus();\n      });\n    });\n  }\n  onOnline() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onOnline();\n      });\n    });\n  }\n};\nexport {\n  QueryCache\n};\n//# sourceMappingURL=queryCache.js.map", "// src/mutation.ts\nimport { notify<PERSON>anager } from \"./notifyManager.js\";\nimport { Removable } from \"./removable.js\";\nimport { createRetryer } from \"./retryer.js\";\nvar Mutation = class extends Removable {\n  #observers;\n  #mutationCache;\n  #retryer;\n  constructor(config) {\n    super();\n    this.mutationId = config.mutationId;\n    this.#mutationCache = config.mutationCache;\n    this.#observers = [];\n    this.state = config.state || getDefaultState();\n    this.setOptions(config.options);\n    this.scheduleGc();\n  }\n  setOptions(options) {\n    this.options = options;\n    this.updateGcTime(this.options.gcTime);\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  addObserver(observer) {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer);\n      this.clearGcTimeout();\n      this.#mutationCache.notify({\n        type: \"observerAdded\",\n        mutation: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    this.#observers = this.#observers.filter((x) => x !== observer);\n    this.scheduleGc();\n    this.#mutationCache.notify({\n      type: \"observerRemoved\",\n      mutation: this,\n      observer\n    });\n  }\n  optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === \"pending\") {\n        this.scheduleGc();\n      } else {\n        this.#mutationCache.remove(this);\n      }\n    }\n  }\n  continue() {\n    return this.#retryer?.continue() ?? // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n    this.execute(this.state.variables);\n  }\n  async execute(variables) {\n    const onContinue = () => {\n      this.#dispatch({ type: \"continue\" });\n    };\n    this.#retryer = createRetryer({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject(new Error(\"No mutationFn found\"));\n        }\n        return this.options.mutationFn(variables);\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue,\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n      networkMode: this.options.networkMode,\n      canRun: () => this.#mutationCache.canRun(this)\n    });\n    const restored = this.state.status === \"pending\";\n    const isPaused = !this.#retryer.canStart();\n    try {\n      if (restored) {\n        onContinue();\n      } else {\n        this.#dispatch({ type: \"pending\", variables, isPaused });\n        await this.#mutationCache.config.onMutate?.(\n          variables,\n          this\n        );\n        const context = await this.options.onMutate?.(variables);\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: \"pending\",\n            context,\n            variables,\n            isPaused\n          });\n        }\n      }\n      const data = await this.#retryer.start();\n      await this.#mutationCache.config.onSuccess?.(\n        data,\n        variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSuccess?.(data, variables, this.state.context);\n      await this.#mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSettled?.(data, null, variables, this.state.context);\n      this.#dispatch({ type: \"success\", data });\n      return data;\n    } catch (error) {\n      try {\n        await this.#mutationCache.config.onError?.(\n          error,\n          variables,\n          this.state.context,\n          this\n        );\n        await this.options.onError?.(\n          error,\n          variables,\n          this.state.context\n        );\n        await this.#mutationCache.config.onSettled?.(\n          void 0,\n          error,\n          this.state.variables,\n          this.state.context,\n          this\n        );\n        await this.options.onSettled?.(\n          void 0,\n          error,\n          variables,\n          this.state.context\n        );\n        throw error;\n      } finally {\n        this.#dispatch({ type: \"error\", error });\n      }\n    } finally {\n      this.#mutationCache.runNext(this);\n    }\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            isPaused: true\n          };\n        case \"continue\":\n          return {\n            ...state,\n            isPaused: false\n          };\n        case \"pending\":\n          return {\n            ...state,\n            context: action.context,\n            data: void 0,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: action.isPaused,\n            status: \"pending\",\n            variables: action.variables,\n            submittedAt: Date.now()\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: \"success\",\n            isPaused: false\n          };\n        case \"error\":\n          return {\n            ...state,\n            data: void 0,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: \"error\"\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.#observers.forEach((observer) => {\n        observer.onMutationUpdate(action);\n      });\n      this.#mutationCache.notify({\n        mutation: this,\n        type: \"updated\",\n        action\n      });\n    });\n  }\n};\nfunction getDefaultState() {\n  return {\n    context: void 0,\n    data: void 0,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: \"idle\",\n    variables: void 0,\n    submittedAt: 0\n  };\n}\nexport {\n  Mutation,\n  getDefaultState\n};\n//# sourceMappingURL=mutation.js.map", "// src/mutationCache.ts\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Mutation } from \"./mutation.js\";\nimport { matchMutation, noop } from \"./utils.js\";\nimport { Subscribable } from \"./subscribable.js\";\nvar MutationCache = class extends Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#mutations = /* @__PURE__ */ new Set();\n    this.#scopes = /* @__PURE__ */ new Map();\n    this.#mutationId = 0;\n  }\n  #mutations;\n  #scopes;\n  #mutationId;\n  build(client, options, state) {\n    const mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state\n    });\n    this.add(mutation);\n    return mutation;\n  }\n  add(mutation) {\n    this.#mutations.add(mutation);\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const scopedMutations = this.#scopes.get(scope);\n      if (scopedMutations) {\n        scopedMutations.push(mutation);\n      } else {\n        this.#scopes.set(scope, [mutation]);\n      }\n    }\n    this.notify({ type: \"added\", mutation });\n  }\n  remove(mutation) {\n    if (this.#mutations.delete(mutation)) {\n      const scope = scopeFor(mutation);\n      if (typeof scope === \"string\") {\n        const scopedMutations = this.#scopes.get(scope);\n        if (scopedMutations) {\n          if (scopedMutations.length > 1) {\n            const index = scopedMutations.indexOf(mutation);\n            if (index !== -1) {\n              scopedMutations.splice(index, 1);\n            }\n          } else if (scopedMutations[0] === mutation) {\n            this.#scopes.delete(scope);\n          }\n        }\n      }\n    }\n    this.notify({ type: \"removed\", mutation });\n  }\n  canRun(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const mutationsWithSameScope = this.#scopes.get(scope);\n      const firstPendingMutation = mutationsWithSameScope?.find(\n        (m) => m.state.status === \"pending\"\n      );\n      return !firstPendingMutation || firstPendingMutation === mutation;\n    } else {\n      return true;\n    }\n  }\n  runNext(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const foundMutation = this.#scopes.get(scope)?.find((m) => m !== mutation && m.state.isPaused);\n      return foundMutation?.continue() ?? Promise.resolve();\n    } else {\n      return Promise.resolve();\n    }\n  }\n  clear() {\n    notifyManager.batch(() => {\n      this.#mutations.forEach((mutation) => {\n        this.notify({ type: \"removed\", mutation });\n      });\n      this.#mutations.clear();\n      this.#scopes.clear();\n    });\n  }\n  getAll() {\n    return Array.from(this.#mutations);\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (mutation) => matchMutation(defaultedFilters, mutation)\n    );\n  }\n  findAll(filters = {}) {\n    return this.getAll().filter((mutation) => matchMutation(filters, mutation));\n  }\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  resumePausedMutations() {\n    const pausedMutations = this.getAll().filter((x) => x.state.isPaused);\n    return notifyManager.batch(\n      () => Promise.all(\n        pausedMutations.map((mutation) => mutation.continue().catch(noop))\n      )\n    );\n  }\n};\nfunction scopeFor(mutation) {\n  return mutation.options.scope?.id;\n}\nexport {\n  MutationCache\n};\n//# sourceMappingURL=mutationCache.js.map", "// src/infiniteQueryBehavior.ts\nimport { addToEnd, addToStart, ensureQueryFn } from \"./utils.js\";\nfunction infiniteQueryBehavior(pages) {\n  return {\n    onFetch: (context, query) => {\n      const options = context.options;\n      const direction = context.fetchOptions?.meta?.fetchMore?.direction;\n      const oldPages = context.state.data?.pages || [];\n      const oldPageParams = context.state.data?.pageParams || [];\n      let result = { pages: [], pageParams: [] };\n      let currentPage = 0;\n      const fetchFn = async () => {\n        let cancelled = false;\n        const addSignalProperty = (object) => {\n          Object.defineProperty(object, \"signal\", {\n            enumerable: true,\n            get: () => {\n              if (context.signal.aborted) {\n                cancelled = true;\n              } else {\n                context.signal.addEventListener(\"abort\", () => {\n                  cancelled = true;\n                });\n              }\n              return context.signal;\n            }\n          });\n        };\n        const queryFn = ensureQueryFn(context.options, context.fetchOptions);\n        const fetchPage = async (data, param, previous) => {\n          if (cancelled) {\n            return Promise.reject();\n          }\n          if (param == null && data.pages.length) {\n            return Promise.resolve(data);\n          }\n          const createQueryFnContext = () => {\n            const queryFnContext2 = {\n              client: context.client,\n              queryKey: context.queryKey,\n              pageParam: param,\n              direction: previous ? \"backward\" : \"forward\",\n              meta: context.options.meta\n            };\n            addSignalProperty(queryFnContext2);\n            return queryFnContext2;\n          };\n          const queryFnContext = createQueryFnContext();\n          const page = await queryFn(queryFnContext);\n          const { maxPages } = context.options;\n          const addTo = previous ? addToStart : addToEnd;\n          return {\n            pages: addTo(data.pages, page, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages)\n          };\n        };\n        if (direction && oldPages.length) {\n          const previous = direction === \"backward\";\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n          const oldData = {\n            pages: oldPages,\n            pageParams: oldPageParams\n          };\n          const param = pageParamFn(options, oldData);\n          result = await fetchPage(oldData, param, previous);\n        } else {\n          const remainingPages = pages ?? oldPages.length;\n          do {\n            const param = currentPage === 0 ? oldPageParams[0] ?? options.initialPageParam : getNextPageParam(options, result);\n            if (currentPage > 0 && param == null) {\n              break;\n            }\n            result = await fetchPage(result, param);\n            currentPage++;\n          } while (currentPage < remainingPages);\n        }\n        return result;\n      };\n      if (context.options.persister) {\n        context.fetchFn = () => {\n          return context.options.persister?.(\n            fetchFn,\n            {\n              client: context.client,\n              queryKey: context.queryKey,\n              meta: context.options.meta,\n              signal: context.signal\n            },\n            query\n          );\n        };\n      } else {\n        context.fetchFn = fetchFn;\n      }\n    }\n  };\n}\nfunction getNextPageParam(options, { pages, pageParams }) {\n  const lastIndex = pages.length - 1;\n  return pages.length > 0 ? options.getNextPageParam(\n    pages[lastIndex],\n    pages,\n    pageParams[lastIndex],\n    pageParams\n  ) : void 0;\n}\nfunction getPreviousPageParam(options, { pages, pageParams }) {\n  return pages.length > 0 ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams) : void 0;\n}\nfunction hasNextPage(options, data) {\n  if (!data) return false;\n  return getNextPageParam(options, data) != null;\n}\nfunction hasPreviousPage(options, data) {\n  if (!data || !options.getPreviousPageParam) return false;\n  return getPreviousPageParam(options, data) != null;\n}\nexport {\n  hasNextPage,\n  hasPreviousPage,\n  infiniteQueryBehavior\n};\n//# sourceMappingURL=infiniteQueryBehavior.js.map", "// src/queryClient.ts\nimport {\n  functionalUpdate,\n  hashKey,\n  hashQueryKeyByOptions,\n  noop,\n  partialMatchKey,\n  resolveStaleTime,\n  skipToken\n} from \"./utils.js\";\nimport { QueryCache } from \"./queryCache.js\";\nimport { MutationCache } from \"./mutationCache.js\";\nimport { focusManager } from \"./focusManager.js\";\nimport { onlineManager } from \"./onlineManager.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { infiniteQueryBehavior } from \"./infiniteQueryBehavior.js\";\nvar QueryClient = class {\n  #queryCache;\n  #mutationCache;\n  #defaultOptions;\n  #queryDefaults;\n  #mutationDefaults;\n  #mountCount;\n  #unsubscribeFocus;\n  #unsubscribeOnline;\n  constructor(config = {}) {\n    this.#queryCache = config.queryCache || new QueryCache();\n    this.#mutationCache = config.mutationCache || new MutationCache();\n    this.#defaultOptions = config.defaultOptions || {};\n    this.#queryDefaults = /* @__PURE__ */ new Map();\n    this.#mutationDefaults = /* @__PURE__ */ new Map();\n    this.#mountCount = 0;\n  }\n  mount() {\n    this.#mountCount++;\n    if (this.#mountCount !== 1) return;\n    this.#unsubscribeFocus = focusManager.subscribe(async (focused) => {\n      if (focused) {\n        await this.resumePausedMutations();\n        this.#queryCache.onFocus();\n      }\n    });\n    this.#unsubscribeOnline = onlineManager.subscribe(async (online) => {\n      if (online) {\n        await this.resumePausedMutations();\n        this.#queryCache.onOnline();\n      }\n    });\n  }\n  unmount() {\n    this.#mountCount--;\n    if (this.#mountCount !== 0) return;\n    this.#unsubscribeFocus?.();\n    this.#unsubscribeFocus = void 0;\n    this.#unsubscribeOnline?.();\n    this.#unsubscribeOnline = void 0;\n  }\n  isFetching(filters) {\n    return this.#queryCache.findAll({ ...filters, fetchStatus: \"fetching\" }).length;\n  }\n  isMutating(filters) {\n    return this.#mutationCache.findAll({ ...filters, status: \"pending\" }).length;\n  }\n  /**\n   * Imperative (non-reactive) way to retrieve data for a QueryKey.\n   * Should only be used in callbacks or functions where reading the latest data is necessary, e.g. for optimistic updates.\n   *\n   * Hint: Do not use this function inside a component, because it won't receive updates.\n   * Use `useQuery` to create a `QueryObserver` that subscribes to changes.\n   */\n  getQueryData(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(options.queryHash)?.state.data;\n  }\n  ensureQueryData(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    const query = this.#queryCache.build(this, defaultedOptions);\n    const cachedData = query.state.data;\n    if (cachedData === void 0) {\n      return this.fetchQuery(options);\n    }\n    if (options.revalidateIfStale && query.isStaleByTime(resolveStaleTime(defaultedOptions.staleTime, query))) {\n      void this.prefetchQuery(defaultedOptions);\n    }\n    return Promise.resolve(cachedData);\n  }\n  getQueriesData(filters) {\n    return this.#queryCache.findAll(filters).map(({ queryKey, state }) => {\n      const data = state.data;\n      return [queryKey, data];\n    });\n  }\n  setQueryData(queryKey, updater, options) {\n    const defaultedOptions = this.defaultQueryOptions({ queryKey });\n    const query = this.#queryCache.get(\n      defaultedOptions.queryHash\n    );\n    const prevData = query?.state.data;\n    const data = functionalUpdate(updater, prevData);\n    if (data === void 0) {\n      return void 0;\n    }\n    return this.#queryCache.build(this, defaultedOptions).setData(data, { ...options, manual: true });\n  }\n  setQueriesData(filters, updater, options) {\n    return notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map(({ queryKey }) => [\n        queryKey,\n        this.setQueryData(queryKey, updater, options)\n      ])\n    );\n  }\n  getQueryState(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(\n      options.queryHash\n    )?.state;\n  }\n  removeQueries(filters) {\n    const queryCache = this.#queryCache;\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query);\n      });\n    });\n  }\n  resetQueries(filters, options) {\n    const queryCache = this.#queryCache;\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset();\n      });\n      return this.refetchQueries(\n        {\n          type: \"active\",\n          ...filters\n        },\n        options\n      );\n    });\n  }\n  cancelQueries(filters, cancelOptions = {}) {\n    const defaultedCancelOptions = { revert: true, ...cancelOptions };\n    const promises = notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map((query) => query.cancel(defaultedCancelOptions))\n    );\n    return Promise.all(promises).then(noop).catch(noop);\n  }\n  invalidateQueries(filters, options = {}) {\n    return notifyManager.batch(() => {\n      this.#queryCache.findAll(filters).forEach((query) => {\n        query.invalidate();\n      });\n      if (filters?.refetchType === \"none\") {\n        return Promise.resolve();\n      }\n      return this.refetchQueries(\n        {\n          ...filters,\n          type: filters?.refetchType ?? filters?.type ?? \"active\"\n        },\n        options\n      );\n    });\n  }\n  refetchQueries(filters, options = {}) {\n    const fetchOptions = {\n      ...options,\n      cancelRefetch: options.cancelRefetch ?? true\n    };\n    const promises = notifyManager.batch(\n      () => this.#queryCache.findAll(filters).filter((query) => !query.isDisabled() && !query.isStatic()).map((query) => {\n        let promise = query.fetch(void 0, fetchOptions);\n        if (!fetchOptions.throwOnError) {\n          promise = promise.catch(noop);\n        }\n        return query.state.fetchStatus === \"paused\" ? Promise.resolve() : promise;\n      })\n    );\n    return Promise.all(promises).then(noop);\n  }\n  fetchQuery(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    if (defaultedOptions.retry === void 0) {\n      defaultedOptions.retry = false;\n    }\n    const query = this.#queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(\n      resolveStaleTime(defaultedOptions.staleTime, query)\n    ) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  }\n  prefetchQuery(options) {\n    return this.fetchQuery(options).then(noop).catch(noop);\n  }\n  fetchInfiniteQuery(options) {\n    options.behavior = infiniteQueryBehavior(options.pages);\n    return this.fetchQuery(options);\n  }\n  prefetchInfiniteQuery(options) {\n    return this.fetchInfiniteQuery(options).then(noop).catch(noop);\n  }\n  ensureInfiniteQueryData(options) {\n    options.behavior = infiniteQueryBehavior(options.pages);\n    return this.ensureQueryData(options);\n  }\n  resumePausedMutations() {\n    if (onlineManager.isOnline()) {\n      return this.#mutationCache.resumePausedMutations();\n    }\n    return Promise.resolve();\n  }\n  getQueryCache() {\n    return this.#queryCache;\n  }\n  getMutationCache() {\n    return this.#mutationCache;\n  }\n  getDefaultOptions() {\n    return this.#defaultOptions;\n  }\n  setDefaultOptions(options) {\n    this.#defaultOptions = options;\n  }\n  setQueryDefaults(queryKey, options) {\n    this.#queryDefaults.set(hashKey(queryKey), {\n      queryKey,\n      defaultOptions: options\n    });\n  }\n  getQueryDefaults(queryKey) {\n    const defaults = [...this.#queryDefaults.values()];\n    const result = {};\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(queryKey, queryDefault.queryKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  setMutationDefaults(mutationKey, options) {\n    this.#mutationDefaults.set(hashKey(mutationKey), {\n      mutationKey,\n      defaultOptions: options\n    });\n  }\n  getMutationDefaults(mutationKey) {\n    const defaults = [...this.#mutationDefaults.values()];\n    const result = {};\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(mutationKey, queryDefault.mutationKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  defaultQueryOptions(options) {\n    if (options._defaulted) {\n      return options;\n    }\n    const defaultedOptions = {\n      ...this.#defaultOptions.queries,\n      ...this.getQueryDefaults(options.queryKey),\n      ...options,\n      _defaulted: true\n    };\n    if (!defaultedOptions.queryHash) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions\n      );\n    }\n    if (defaultedOptions.refetchOnReconnect === void 0) {\n      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== \"always\";\n    }\n    if (defaultedOptions.throwOnError === void 0) {\n      defaultedOptions.throwOnError = !!defaultedOptions.suspense;\n    }\n    if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n      defaultedOptions.networkMode = \"offlineFirst\";\n    }\n    if (defaultedOptions.queryFn === skipToken) {\n      defaultedOptions.enabled = false;\n    }\n    return defaultedOptions;\n  }\n  defaultMutationOptions(options) {\n    if (options?._defaulted) {\n      return options;\n    }\n    return {\n      ...this.#defaultOptions.mutations,\n      ...options?.mutationKey && this.getMutationDefaults(options.mutationKey),\n      ...options,\n      _defaulted: true\n    };\n  }\n  clear() {\n    this.#queryCache.clear();\n    this.#mutationCache.clear();\n  }\n};\nexport {\n  QueryClient\n};\n//# sourceMappingURL=queryClient.js.map"], "names": ["getDefaultState"], "mappings": "AACA,IAAI,eAAe,MAAM;AAAA,EACvB,cAAc;AACZ,SAAK,YAA4B,oBAAI,IAAK;AAC1C,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AAAA,EAC7C;AAAA,EACE,UAAU,UAAU;AAClB,SAAK,UAAU,IAAI,QAAQ;AAC3B,SAAK,YAAa;AAClB,WAAO,MAAM;AACX,WAAK,UAAU,OAAO,QAAQ;AAC9B,WAAK,cAAe;AAAA,IACrB;AAAA,EACL;AAAA,EACE,eAAe;AACb,WAAO,KAAK,UAAU,OAAO;AAAA,EACjC;AAAA,EACE,cAAc;AAAA,EAChB;AAAA,EACE,gBAAgB;AAAA,EAClB;AACA;ACpBA,IAAI,WAAW,OAAO,WAAW,eAAe,UAAU;AAC1D,SAAS,OAAO;AAChB;AACA,SAAS,iBAAiB,SAAS,OAAO;AACxC,SAAO,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AAC1D;AACA,SAAS,eAAe,OAAO;AAC7B,SAAO,OAAO,UAAU,YAAY,SAAS,KAAK,UAAU;AAC9D;AACA,SAAS,eAAe,WAAW,WAAW;AACrC,SAAA,KAAK,IAAI,aAAa,aAAa,KAAK,KAAK,OAAO,CAAC;AAC9D;AACA,SAAS,iBAAiB,WAAW,OAAO;AAC1C,SAAO,OAAO,cAAc,aAAa,UAAU,KAAK,IAAI;AAC9D;AACA,SAAS,eAAe,SAAS,OAAO;AACtC,SAAO,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AAC1D;AACA,SAAS,WAAW,SAAS,OAAO;AAC5B,QAAA;AAAA,IACJ,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA,IACE;AACJ,MAAI,UAAU;AACZ,QAAI,OAAO;AACT,UAAI,MAAM,cAAc,sBAAsB,UAAU,MAAM,OAAO,GAAG;AAC/D,eAAA;AAAA,MAAA;AAAA,eAEA,CAAC,gBAAgB,MAAM,UAAU,QAAQ,GAAG;AAC9C,aAAA;AAAA,IAAA;AAAA,EACT;AAEF,MAAI,SAAS,OAAO;AACZ,UAAA,WAAW,MAAM,SAAS;AAC5B,QAAA,SAAS,YAAY,CAAC,UAAU;AAC3B,aAAA;AAAA,IAAA;AAEL,QAAA,SAAS,cAAc,UAAU;AAC5B,aAAA;AAAA,IAAA;AAAA,EACT;AAEF,MAAI,OAAO,UAAU,aAAa,MAAM,QAAA,MAAc,OAAO;AACpD,WAAA;AAAA,EAAA;AAET,MAAI,eAAe,gBAAgB,MAAM,MAAM,aAAa;AACnD,WAAA;AAAA,EAAA;AAET,MAAI,aAAa,CAAC,UAAU,KAAK,GAAG;AAC3B,WAAA;AAAA,EAAA;AAEF,SAAA;AACT;AACA,SAAS,cAAc,SAAS,UAAU;AACxC,QAAM,EAAE,OAAO,QAAQ,WAAW,YAAgB,IAAA;AAClD,MAAI,aAAa;AACX,QAAA,CAAC,SAAS,QAAQ,aAAa;AAC1B,aAAA;AAAA,IAAA;AAET,QAAI,OAAO;AACT,UAAI,QAAQ,SAAS,QAAQ,WAAW,MAAM,QAAQ,WAAW,GAAG;AAC3D,eAAA;AAAA,MAAA;AAAA,IACT,WACS,CAAC,gBAAgB,SAAS,QAAQ,aAAa,WAAW,GAAG;AAC/D,aAAA;AAAA,IAAA;AAAA,EACT;AAEF,MAAI,UAAU,SAAS,MAAM,WAAW,QAAQ;AACvC,WAAA;AAAA,EAAA;AAET,MAAI,aAAa,CAAC,UAAU,QAAQ,GAAG;AAC9B,WAAA;AAAA,EAAA;AAEF,SAAA;AACT;AACA,SAAS,sBAAsB,UAAU,SAAS;AAC1C,QAAA,SAAS,SAAS,kBAAkB;AAC1C,SAAO,OAAO,QAAQ;AACxB;AACA,SAAS,QAAQ,UAAU;AACzB,SAAO,KAAK;AAAA,IACV;AAAA,IACA,CAAC,GAAG,QAAQ,cAAc,GAAG,IAAI,OAAO,KAAK,GAAG,EAAE,KAAK,EAAE,OAAO,CAAC,QAAQ,QAAQ;AACxE,aAAA,GAAG,IAAI,IAAI,GAAG;AACd,aAAA;AAAA,IACT,GAAG,CAAE,CAAA,IAAI;AAAA,EACX;AACF;AACA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,MAAI,MAAM,GAAG;AACJ,WAAA;AAAA,EAAA;AAEL,MAAA,OAAO,MAAM,OAAO,GAAG;AAClB,WAAA;AAAA,EAAA;AAET,MAAI,KAAK,KAAK,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;AAC5D,WAAO,OAAO,KAAK,CAAC,EAAE,MAAM,CAAC,QAAQ,gBAAgB,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;AAAA,EAAA;AAE/D,SAAA;AACT;AACA,SAAS,iBAAiB,GAAG,GAAG;AAC9B,MAAI,MAAM,GAAG;AACJ,WAAA;AAAA,EAAA;AAET,QAAM,QAAQ,aAAa,CAAC,KAAK,aAAa,CAAC;AAC/C,MAAI,SAAS,cAAc,CAAC,KAAK,cAAc,CAAC,GAAG;AACjD,UAAM,SAAS,QAAQ,IAAI,OAAO,KAAK,CAAC;AACxC,UAAM,QAAQ,OAAO;AACrB,UAAM,SAAS,QAAQ,IAAI,OAAO,KAAK,CAAC;AACxC,UAAM,QAAQ,OAAO;AACrB,UAAM,OAAO,QAAQ,CAAA,IAAK,CAAC;AACrB,UAAA,YAAY,IAAI,IAAI,MAAM;AAChC,QAAI,aAAa;AACjB,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,YAAM,MAAM,QAAQ,IAAI,OAAO,CAAC;AAChC,WAAK,CAAC,SAAS,UAAU,IAAI,GAAG,KAAK,UAAU,EAAE,GAAG,MAAM,UAAU,EAAE,GAAG,MAAM,QAAQ;AACrF,aAAK,GAAG,IAAI;AACZ;AAAA,MAAA,OACK;AACA,aAAA,GAAG,IAAI,iBAAiB,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AACvC,YAAA,KAAK,GAAG,MAAM,EAAE,GAAG,KAAK,EAAE,GAAG,MAAM,QAAQ;AAC7C;AAAA,QAAA;AAAA,MACF;AAAA,IACF;AAEF,WAAO,UAAU,SAAS,eAAe,QAAQ,IAAI;AAAA,EAAA;AAEhD,SAAA;AACT;AAYA,SAAS,aAAa,OAAO;AACpB,SAAA,MAAM,QAAQ,KAAK,KAAK,MAAM,WAAW,OAAO,KAAK,KAAK,EAAE;AACrE;AACA,SAAS,cAAc,GAAG;AACpB,MAAA,CAAC,mBAAmB,CAAC,GAAG;AACnB,WAAA;AAAA,EAAA;AAET,QAAM,OAAO,EAAE;AACf,MAAI,SAAS,QAAQ;AACZ,WAAA;AAAA,EAAA;AAET,QAAM,OAAO,KAAK;AACd,MAAA,CAAC,mBAAmB,IAAI,GAAG;AACtB,WAAA;AAAA,EAAA;AAET,MAAI,CAAC,KAAK,eAAe,eAAe,GAAG;AAClC,WAAA;AAAA,EAAA;AAET,MAAI,OAAO,eAAe,CAAC,MAAM,OAAO,WAAW;AAC1C,WAAA;AAAA,EAAA;AAEF,SAAA;AACT;AACA,SAAS,mBAAmB,GAAG;AAC7B,SAAO,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM;AAC/C;AACA,SAAS,MAAM,SAAS;AACf,SAAA,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,SAAS,OAAO;AAAA,EAAA,CAC5B;AACH;AACA,SAAS,YAAY,UAAU,MAAM,SAAS;AACxC,MAAA,OAAO,QAAQ,sBAAsB,YAAY;AAC5C,WAAA,QAAQ,kBAAkB,UAAU,IAAI;AAAA,EAAA,WACtC,QAAQ,sBAAsB,OAAO;AACH;AACrC,UAAA;AACK,eAAA,iBAAiB,UAAU,IAAI;AAAA,eAC/B,OAAO;AACN,gBAAA;AAAA,UACN,0JAA0J,QAAQ,SAAS,MAAM,KAAK;AAAA,QACxL;AACM,cAAA;AAAA,MAAA;AAAA,IACR;AAEK,WAAA,iBAAiB,UAAU,IAAI;AAAA,EAAA;AAEjC,SAAA;AACT;AAIA,SAAS,SAAS,OAAO,MAAM,MAAM,GAAG;AACtC,QAAM,WAAW,CAAC,GAAG,OAAO,IAAI;AAChC,SAAO,OAAO,SAAS,SAAS,MAAM,SAAS,MAAM,CAAC,IAAI;AAC5D;AACA,SAAS,WAAW,OAAO,MAAM,MAAM,GAAG;AACxC,QAAM,WAAW,CAAC,MAAM,GAAG,KAAK;AACzB,SAAA,OAAO,SAAS,SAAS,MAAM,SAAS,MAAM,GAAG,EAAE,IAAI;AAChE;AACA,IAAI,YAAY,OAAO;AACvB,SAAS,cAAc,SAAS,cAAc;AACD;AACrC,QAAA,QAAQ,YAAY,WAAW;AACzB,cAAA;AAAA,QACN,yGAAyG,QAAQ,SAAS;AAAA,MAC5H;AAAA,IAAA;AAAA,EACF;AAEF,MAAI,CAAC,QAAQ,WAAW,cAAc,gBAAgB;AACpD,WAAO,MAAM,aAAa;AAAA,EAAA;AAE5B,MAAI,CAAC,QAAQ,WAAW,QAAQ,YAAY,WAAW;AAC9C,WAAA,MAAM,QAAQ,OAAO,IAAI,MAAM,qBAAqB,QAAQ,SAAS,GAAG,CAAC;AAAA,EAAA;AAElF,SAAO,QAAQ;AACjB;ACzNA,IAAI,eAAe,cAAc,aAAa;AAAA,EAC5C;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc;AACZ,UAAO;AACP,SAAK,SAAS,CAAC,YAAY;AACzB,UAAI,CAAC,YAAY,OAAO,kBAAkB;AACxC,cAAM,WAAW,MAAM,QAAS;AAChC,eAAO,iBAAiB,oBAAoB,UAAU,KAAK;AAC3D,eAAO,MAAM;AACX,iBAAO,oBAAoB,oBAAoB,QAAQ;AAAA,QACxD;AAAA,MACT;AACM;AAAA,IACD;AAAA,EACL;AAAA,EACE,cAAc;AACZ,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,iBAAiB,KAAK,MAAM;AAAA,IACvC;AAAA,EACA;AAAA,EACE,gBAAgB;AACd,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,WAAY;AACjB,WAAK,WAAW;AAAA,IACtB;AAAA,EACA;AAAA,EACE,iBAAiB,OAAO;AACtB,SAAK,SAAS;AACd,SAAK,WAAY;AACjB,SAAK,WAAW,MAAM,CAAC,YAAY;AACjC,UAAI,OAAO,YAAY,WAAW;AAChC,aAAK,WAAW,OAAO;AAAA,MAC/B,OAAa;AACL,aAAK,QAAS;AAAA,MACtB;AAAA,IACA,CAAK;AAAA,EACL;AAAA,EACE,WAAW,SAAS;AAClB,UAAM,UAAU,KAAK,aAAa;AAClC,QAAI,SAAS;AACX,WAAK,WAAW;AAChB,WAAK,QAAS;AAAA,IACpB;AAAA,EACA;AAAA,EACE,UAAU;AACR,UAAM,YAAY,KAAK,UAAW;AAClC,SAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,eAAS,SAAS;AAAA,IACxB,CAAK;AAAA,EACL;AAAA,EACE,YAAY;AACV,QAAI,OAAO,KAAK,aAAa,WAAW;AACtC,aAAO,KAAK;AAAA,IAClB;AACI,WAAO,WAAW,UAAU,oBAAoB;AAAA,EACpD;AACA;AACA,IAAI,eAAe,IAAI,aAAc;AC3DrC,IAAI,gBAAgB,cAAc,aAAa;AAAA,EAC7C,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA,cAAc;AACZ,UAAO;AACP,SAAK,SAAS,CAAC,aAAa;AAC1B,UAAI,CAAC,YAAY,OAAO,kBAAkB;AACxC,cAAM,iBAAiB,MAAM,SAAS,IAAI;AAC1C,cAAM,kBAAkB,MAAM,SAAS,KAAK;AAC5C,eAAO,iBAAiB,UAAU,gBAAgB,KAAK;AACvD,eAAO,iBAAiB,WAAW,iBAAiB,KAAK;AACzD,eAAO,MAAM;AACX,iBAAO,oBAAoB,UAAU,cAAc;AACnD,iBAAO,oBAAoB,WAAW,eAAe;AAAA,QACtD;AAAA,MACT;AACM;AAAA,IACD;AAAA,EACL;AAAA,EACE,cAAc;AACZ,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,iBAAiB,KAAK,MAAM;AAAA,IACvC;AAAA,EACA;AAAA,EACE,gBAAgB;AACd,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,WAAY;AACjB,WAAK,WAAW;AAAA,IACtB;AAAA,EACA;AAAA,EACE,iBAAiB,OAAO;AACtB,SAAK,SAAS;AACd,SAAK,WAAY;AACjB,SAAK,WAAW,MAAM,KAAK,UAAU,KAAK,IAAI,CAAC;AAAA,EACnD;AAAA,EACE,UAAU,QAAQ;AAChB,UAAM,UAAU,KAAK,YAAY;AACjC,QAAI,SAAS;AACX,WAAK,UAAU;AACf,WAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,iBAAS,MAAM;AAAA,MACvB,CAAO;AAAA,IACP;AAAA,EACA;AAAA,EACE,WAAW;AACT,WAAO,KAAK;AAAA,EAChB;AACA;AACA,IAAI,gBAAgB,IAAI,cAAe;AClDvC,SAAS,kBAAkB;AACzB,MAAI;AACJ,MAAI;AACJ,QAAM,WAAW,IAAI,QAAQ,CAAC,UAAU,YAAY;AAClD,cAAU;AACV,aAAS;AAAA,EACb,CAAG;AACD,WAAS,SAAS;AAClB,WAAS,MAAM,MAAM;AAAA,EACvB,CAAG;AACD,WAAS,SAAS,MAAM;AACtB,WAAO,OAAO,UAAU,IAAI;AAC5B,WAAO,SAAS;AAChB,WAAO,SAAS;AAAA,EACpB;AACE,WAAS,UAAU,CAAC,UAAU;AAC5B,aAAS;AAAA,MACP,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AACD,YAAQ,KAAK;AAAA,EACd;AACD,WAAS,SAAS,CAAC,WAAW;AAC5B,aAAS;AAAA,MACP,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AACD,WAAO,MAAM;AAAA,EACd;AACD,SAAO;AACT;AC3BA,SAAS,kBAAkB,cAAc;AACvC,SAAO,KAAK,IAAI,MAAM,KAAK,cAAc,GAAG;AAC9C;AACA,SAAS,SAAS,aAAa;AAC7B,UAAQ,eAAe,cAAc,WAAW,cAAc,SAAQ,IAAK;AAC7E;AACA,IAAI,iBAAiB,cAAc,MAAM;AAAA,EACvC,YAAY,SAAS;AACnB,UAAM,gBAAgB;AACtB,SAAK,SAAS,SAAS;AACvB,SAAK,SAAS,SAAS;AAAA,EAC3B;AACA;AACA,SAAS,iBAAiB,OAAO;AAC/B,SAAO,iBAAiB;AAC1B;AACA,SAAS,cAAc,QAAQ;AAC7B,MAAI,mBAAmB;AACvB,MAAI,eAAe;AACnB,MAAI,aAAa;AACjB,MAAI;AACJ,QAAM,WAAW,gBAAiB;AAClC,QAAM,SAAS,CAAC,kBAAkB;AAChC,QAAI,CAAC,YAAY;AACf,aAAO,IAAI,eAAe,aAAa,CAAC;AACxC,aAAO,QAAS;AAAA,IACtB;AAAA,EACG;AACD,QAAM,cAAc,MAAM;AACxB,uBAAmB;AAAA,EACpB;AACD,QAAM,gBAAgB,MAAM;AAC1B,uBAAmB;AAAA,EACpB;AACD,QAAM,cAAc,MAAM,aAAa,UAAS,MAAO,OAAO,gBAAgB,YAAY,cAAc,SAAQ,MAAO,OAAO,OAAQ;AACtI,QAAM,WAAW,MAAM,SAAS,OAAO,WAAW,KAAK,OAAO,OAAQ;AACtE,QAAM,UAAU,CAAC,UAAU;AACzB,QAAI,CAAC,YAAY;AACf,mBAAa;AACb,aAAO,YAAY,KAAK;AACxB,mBAAc;AACd,eAAS,QAAQ,KAAK;AAAA,IAC5B;AAAA,EACG;AACD,QAAM,SAAS,CAAC,UAAU;AACxB,QAAI,CAAC,YAAY;AACf,mBAAa;AACb,aAAO,UAAU,KAAK;AACtB,mBAAc;AACd,eAAS,OAAO,KAAK;AAAA,IAC3B;AAAA,EACG;AACD,QAAM,QAAQ,MAAM;AAClB,WAAO,IAAI,QAAQ,CAAC,oBAAoB;AACtC,mBAAa,CAAC,UAAU;AACtB,YAAI,cAAc,eAAe;AAC/B,0BAAgB,KAAK;AAAA,QAC/B;AAAA,MACO;AACD,aAAO,UAAW;AAAA,IACxB,CAAK,EAAE,KAAK,MAAM;AACZ,mBAAa;AACb,UAAI,CAAC,YAAY;AACf,eAAO,aAAc;AAAA,MAC7B;AAAA,IACA,CAAK;AAAA,EACF;AACD,QAAM,MAAM,MAAM;AAChB,QAAI,YAAY;AACd;AAAA,IACN;AACI,QAAI;AACJ,UAAM,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB;AACpE,QAAI;AACF,uBAAiB,kBAAkB,OAAO,GAAI;AAAA,IAC/C,SAAQ,OAAO;AACd,uBAAiB,QAAQ,OAAO,KAAK;AAAA,IAC3C;AACI,YAAQ,QAAQ,cAAc,EAAE,KAAK,OAAO,EAAE,MAAM,CAAC,UAAU;AAC7D,UAAI,YAAY;AACd;AAAA,MACR;AACM,YAAM,QAAQ,OAAO,UAAU,WAAW,IAAI;AAC9C,YAAM,aAAa,OAAO,cAAc;AACxC,YAAM,QAAQ,OAAO,eAAe,aAAa,WAAW,cAAc,KAAK,IAAI;AACnF,YAAM,cAAc,UAAU,QAAQ,OAAO,UAAU,YAAY,eAAe,SAAS,OAAO,UAAU,cAAc,MAAM,cAAc,KAAK;AACnJ,UAAI,oBAAoB,CAAC,aAAa;AACpC,eAAO,KAAK;AACZ;AAAA,MACR;AACM;AACA,aAAO,SAAS,cAAc,KAAK;AACnC,YAAM,KAAK,EAAE,KAAK,MAAM;AACtB,eAAO,YAAW,IAAK,SAAS,MAAO;AAAA,MAC/C,CAAO,EAAE,KAAK,MAAM;AACZ,YAAI,kBAAkB;AACpB,iBAAO,KAAK;AAAA,QACtB,OAAe;AACL,cAAK;AAAA,QACf;AAAA,MACA,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AACD,SAAO;AAAA,IACL,SAAS;AAAA,IACT;AAAA,IACA,UAAU,MAAM;AACd,mBAAc;AACd,aAAO;AAAA,IACR;AAAA,IACD;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,MAAM;AACX,UAAI,SAAQ,GAAI;AACd,YAAK;AAAA,MACb,OAAa;AACL,cAAO,EAAC,KAAK,GAAG;AAAA,MACxB;AACM,aAAO;AAAA,IACb;AAAA,EACG;AACH;AC9HA,IAAI,mBAAmB,CAAC,OAAO,WAAW,IAAI,CAAC;AAC/C,SAAS,sBAAsB;AAC7B,MAAI,QAAQ,CAAE;AACd,MAAI,eAAe;AACnB,MAAI,WAAW,CAAC,aAAa;AAC3B,aAAU;AAAA,EACX;AACD,MAAI,gBAAgB,CAAC,aAAa;AAChC,aAAU;AAAA,EACX;AACD,MAAI,aAAa;AACjB,QAAM,WAAW,CAAC,aAAa;AAC7B,QAAI,cAAc;AAChB,YAAM,KAAK,QAAQ;AAAA,IACzB,OAAW;AACL,iBAAW,MAAM;AACf,iBAAS,QAAQ;AAAA,MACzB,CAAO;AAAA,IACP;AAAA,EACG;AACD,QAAM,QAAQ,MAAM;AAClB,UAAM,gBAAgB;AACtB,YAAQ,CAAE;AACV,QAAI,cAAc,QAAQ;AACxB,iBAAW,MAAM;AACf,sBAAc,MAAM;AAClB,wBAAc,QAAQ,CAAC,aAAa;AAClC,qBAAS,QAAQ;AAAA,UAC7B,CAAW;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AAAA,IACP;AAAA,EACG;AACD,SAAO;AAAA,IACL,OAAO,CAAC,aAAa;AACnB,UAAI;AACJ;AACA,UAAI;AACF,iBAAS,SAAU;AAAA,MAC3B,UAAgB;AACR;AACA,YAAI,CAAC,cAAc;AACjB,gBAAO;AAAA,QACjB;AAAA,MACA;AACM,aAAO;AAAA,IACR;AAAA;AAAA;AAAA;AAAA,IAID,YAAY,CAAC,aAAa;AACxB,aAAO,IAAI,SAAS;AAClB,iBAAS,MAAM;AACb,mBAAS,GAAG,IAAI;AAAA,QAC1B,CAAS;AAAA,MACF;AAAA,IACF;AAAA,IACD;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,mBAAmB,CAAC,OAAO;AACzB,iBAAW;AAAA,IACZ;AAAA;AAAA;AAAA;AAAA;AAAA,IAKD,wBAAwB,CAAC,OAAO;AAC9B,sBAAgB;AAAA,IACjB;AAAA,IACD,cAAc,CAAC,OAAO;AACpB,mBAAa;AAAA,IACnB;AAAA,EACG;AACH;AACA,IAAI,gBAAgB,oBAAqB;AC5EzC,IAAI,YAAY,MAAM;AAAA,EACpB;AAAA,EACA,UAAU;AACR,SAAK,eAAgB;AAAA,EACzB;AAAA,EACE,aAAa;AACX,SAAK,eAAgB;AACrB,QAAI,eAAe,KAAK,MAAM,GAAG;AAC/B,WAAK,aAAa,WAAW,MAAM;AACjC,aAAK,eAAgB;AAAA,MAC7B,GAAS,KAAK,MAAM;AAAA,IACpB;AAAA,EACA;AAAA,EACE,aAAa,WAAW;AACtB,SAAK,SAAS,KAAK;AAAA,MACjB,KAAK,UAAU;AAAA,MACf,cAAc,WAAW,WAAW,IAAI,KAAK;AAAA,IAC9C;AAAA,EACL;AAAA,EACE,iBAAiB;AACf,QAAI,KAAK,YAAY;AACnB,mBAAa,KAAK,UAAU;AAC5B,WAAK,aAAa;AAAA,IACxB;AAAA,EACA;AACA;ACdA,IAAI,QAAQ,cAAc,UAAU;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,QAAQ;AACZ,UAAA;AACN,SAAK,uBAAuB;AAC5B,SAAK,kBAAkB,OAAO;AACzB,SAAA,WAAW,OAAO,OAAO;AAC9B,SAAK,YAAY,CAAC;AAClB,SAAK,UAAU,OAAO;AACjB,SAAA,SAAS,KAAK,QAAQ,cAAc;AACzC,SAAK,WAAW,OAAO;AACvB,SAAK,YAAY,OAAO;AACnB,SAAA,gBAAgBA,kBAAgB,KAAK,OAAO;AAC5C,SAAA,QAAQ,OAAO,SAAS,KAAK;AAClC,SAAK,WAAW;AAAA,EAAA;AAAA,EAElB,IAAI,OAAO;AACT,WAAO,KAAK,QAAQ;AAAA,EAAA;AAAA,EAEtB,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU;AAAA,EAAA;AAAA,EAExB,WAAW,SAAS;AAClB,SAAK,UAAU,EAAE,GAAG,KAAK,iBAAiB,GAAG,QAAQ;AAChD,SAAA,aAAa,KAAK,QAAQ,MAAM;AAAA,EAAA;AAAA,EAEvC,iBAAiB;AACf,QAAI,CAAC,KAAK,UAAU,UAAU,KAAK,MAAM,gBAAgB,QAAQ;AAC1D,WAAA,OAAO,OAAO,IAAI;AAAA,IAAA;AAAA,EACzB;AAAA,EAEF,QAAQ,SAAS,SAAS;AACxB,UAAM,OAAO,YAAY,KAAK,MAAM,MAAM,SAAS,KAAK,OAAO;AAC/D,SAAK,UAAU;AAAA,MACb;AAAA,MACA,MAAM;AAAA,MACN,eAAe,SAAS;AAAA,MACxB,QAAQ,SAAS;AAAA,IAAA,CAClB;AACM,WAAA;AAAA,EAAA;AAAA,EAET,SAAS,OAAO,iBAAiB;AAC/B,SAAK,UAAU,EAAE,MAAM,YAAY,OAAO,iBAAiB;AAAA,EAAA;AAAA,EAE7D,OAAO,SAAS;AACR,UAAA,UAAU,KAAK,UAAU;AAC1B,SAAA,UAAU,OAAO,OAAO;AACtB,WAAA,UAAU,QAAQ,KAAK,IAAI,EAAE,MAAM,IAAI,IAAI,QAAQ,QAAQ;AAAA,EAAA;AAAA,EAEpE,UAAU;AACR,UAAM,QAAQ;AACd,SAAK,OAAO,EAAE,QAAQ,KAAA,CAAM;AAAA,EAAA;AAAA,EAE9B,QAAQ;AACN,SAAK,QAAQ;AACR,SAAA,SAAS,KAAK,aAAa;AAAA,EAAA;AAAA,EAElC,WAAW;AACT,WAAO,KAAK,UAAU;AAAA,MACpB,CAAC,aAAa,eAAe,SAAS,QAAQ,SAAS,IAAI,MAAM;AAAA,IACnE;AAAA,EAAA;AAAA,EAEF,aAAa;AACP,QAAA,KAAK,kBAAkB,IAAI,GAAG;AACzB,aAAA,CAAC,KAAK,SAAS;AAAA,IAAA;AAEjB,WAAA,KAAK,QAAQ,YAAY,aAAa,KAAK,MAAM,kBAAkB,KAAK,MAAM,qBAAqB;AAAA,EAAA;AAAA,EAE5G,WAAW;AACL,QAAA,KAAK,kBAAkB,IAAI,GAAG;AAChC,aAAO,KAAK,UAAU;AAAA,QACpB,CAAC,aAAa,iBAAiB,SAAS,QAAQ,WAAW,IAAI,MAAM;AAAA,MACvE;AAAA,IAAA;AAEK,WAAA;AAAA,EAAA;AAAA,EAET,UAAU;AACJ,QAAA,KAAK,kBAAkB,IAAI,GAAG;AAChC,aAAO,KAAK,UAAU;AAAA,QACpB,CAAC,aAAa,SAAS,mBAAmB;AAAA,MAC5C;AAAA,IAAA;AAEF,WAAO,KAAK,MAAM,SAAS,UAAU,KAAK,MAAM;AAAA,EAAA;AAAA,EAElD,cAAc,YAAY,GAAG;AACvB,QAAA,KAAK,MAAM,SAAS,QAAQ;AACvB,aAAA;AAAA,IAAA;AAET,QAAI,cAAc,UAAU;AACnB,aAAA;AAAA,IAAA;AAEL,QAAA,KAAK,MAAM,eAAe;AACrB,aAAA;AAAA,IAAA;AAET,WAAO,CAAC,eAAe,KAAK,MAAM,eAAe,SAAS;AAAA,EAAA;AAAA,EAE5D,UAAU;AACF,UAAA,WAAW,KAAK,UAAU,KAAK,CAAC,MAAM,EAAE,0BAA0B;AACxE,cAAU,QAAQ,EAAE,eAAe,MAAA,CAAO;AAC1C,SAAK,UAAU,SAAS;AAAA,EAAA;AAAA,EAE1B,WAAW;AACH,UAAA,WAAW,KAAK,UAAU,KAAK,CAAC,MAAM,EAAE,wBAAwB;AACtE,cAAU,QAAQ,EAAE,eAAe,MAAA,CAAO;AAC1C,SAAK,UAAU,SAAS;AAAA,EAAA;AAAA,EAE1B,YAAY,UAAU;AACpB,QAAI,CAAC,KAAK,UAAU,SAAS,QAAQ,GAAG;AACjC,WAAA,UAAU,KAAK,QAAQ;AAC5B,WAAK,eAAe;AACf,WAAA,OAAO,OAAO,EAAE,MAAM,iBAAiB,OAAO,MAAM,UAAU;AAAA,IAAA;AAAA,EACrE;AAAA,EAEF,eAAe,UAAU;AACvB,QAAI,KAAK,UAAU,SAAS,QAAQ,GAAG;AACrC,WAAK,YAAY,KAAK,UAAU,OAAO,CAAC,MAAM,MAAM,QAAQ;AACxD,UAAA,CAAC,KAAK,UAAU,QAAQ;AAC1B,YAAI,KAAK,UAAU;AACjB,cAAI,KAAK,sBAAsB;AAC7B,iBAAK,SAAS,OAAO,EAAE,QAAQ,MAAM;AAAA,UAAA,OAChC;AACL,iBAAK,SAAS,YAAY;AAAA,UAAA;AAAA,QAC5B;AAEF,aAAK,WAAW;AAAA,MAAA;AAEb,WAAA,OAAO,OAAO,EAAE,MAAM,mBAAmB,OAAO,MAAM,UAAU;AAAA,IAAA;AAAA,EACvE;AAAA,EAEF,oBAAoB;AAClB,WAAO,KAAK,UAAU;AAAA,EAAA;AAAA,EAExB,aAAa;AACP,QAAA,CAAC,KAAK,MAAM,eAAe;AAC7B,WAAK,UAAU,EAAE,MAAM,aAAA,CAAc;AAAA,IAAA;AAAA,EACvC;AAAA,EAEF,MAAM,SAAS,cAAc;AACvB,QAAA,KAAK,MAAM,gBAAgB,QAAQ;AACrC,UAAI,KAAK,MAAM,SAAS,UAAU,cAAc,eAAe;AAC7D,aAAK,OAAO,EAAE,QAAQ,KAAA,CAAM;AAAA,MAAA,WACnB,KAAK,UAAU;AACxB,aAAK,SAAS,cAAc;AAC5B,eAAO,KAAK,SAAS;AAAA,MAAA;AAAA,IACvB;AAEF,QAAI,SAAS;AACX,WAAK,WAAW,OAAO;AAAA,IAAA;AAErB,QAAA,CAAC,KAAK,QAAQ,SAAS;AACnB,YAAA,WAAW,KAAK,UAAU,KAAK,CAAC,MAAM,EAAE,QAAQ,OAAO;AAC7D,UAAI,UAAU;AACP,aAAA,WAAW,SAAS,OAAO;AAAA,MAAA;AAAA,IAClC;AAEyC;AACzC,UAAI,CAAC,MAAM,QAAQ,KAAK,QAAQ,QAAQ,GAAG;AACjC,gBAAA;AAAA,UACN;AAAA,QACF;AAAA,MAAA;AAAA,IACF;AAEI,UAAA,kBAAkB,IAAI,gBAAgB;AACtC,UAAA,oBAAoB,CAAC,WAAW;AAC7B,aAAA,eAAe,QAAQ,UAAU;AAAA,QACtC,YAAY;AAAA,QACZ,KAAK,MAAM;AACT,eAAK,uBAAuB;AAC5B,iBAAO,gBAAgB;AAAA,QAAA;AAAA,MACzB,CACD;AAAA,IACH;AACA,UAAM,UAAU,MAAM;AACpB,YAAM,UAAU,cAAc,KAAK,SAAS,YAAY;AACxD,YAAM,uBAAuB,MAAM;AACjC,cAAM,kBAAkB;AAAA,UACtB,QAAQ,KAAK;AAAA,UACb,UAAU,KAAK;AAAA,UACf,MAAM,KAAK;AAAA,QACb;AACA,0BAAkB,eAAe;AAC1B,eAAA;AAAA,MACT;AACA,YAAM,iBAAiB,qBAAqB;AAC5C,WAAK,uBAAuB;AACxB,UAAA,KAAK,QAAQ,WAAW;AAC1B,eAAO,KAAK,QAAQ;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MAAA;AAEF,aAAO,QAAQ,cAAc;AAAA,IAC/B;AACA,UAAM,qBAAqB,MAAM;AAC/B,YAAM,WAAW;AAAA,QACf;AAAA,QACA,SAAS,KAAK;AAAA,QACd,UAAU,KAAK;AAAA,QACf,QAAQ,KAAK;AAAA,QACb,OAAO,KAAK;AAAA,QACZ;AAAA,MACF;AACA,wBAAkB,QAAQ;AACnB,aAAA;AAAA,IACT;AACA,UAAM,UAAU,mBAAmB;AACnC,SAAK,QAAQ,UAAU,QAAQ,SAAS,IAAI;AAC5C,SAAK,eAAe,KAAK;AACrB,QAAA,KAAK,MAAM,gBAAgB,UAAU,KAAK,MAAM,cAAc,QAAQ,cAAc,MAAM;AACvF,WAAA,UAAU,EAAE,MAAM,SAAS,MAAM,QAAQ,cAAc,MAAM;AAAA,IAAA;AAE9D,UAAA,UAAU,CAAC,UAAU;AACzB,UAAI,EAAE,iBAAiB,KAAK,KAAK,MAAM,SAAS;AAC9C,aAAK,UAAU;AAAA,UACb,MAAM;AAAA,UACN;AAAA,QAAA,CACD;AAAA,MAAA;AAEC,UAAA,CAAC,iBAAiB,KAAK,GAAG;AAC5B,aAAK,OAAO,OAAO;AAAA,UACjB;AAAA,UACA;AAAA,QACF;AACA,aAAK,OAAO,OAAO;AAAA,UACjB,KAAK,MAAM;AAAA,UACX;AAAA,UACA;AAAA,QACF;AAAA,MAAA;AAEF,WAAK,WAAW;AAAA,IAClB;AACA,SAAK,WAAW,cAAc;AAAA,MAC5B,gBAAgB,cAAc;AAAA,MAC9B,IAAI,QAAQ;AAAA,MACZ,OAAO,gBAAgB,MAAM,KAAK,eAAe;AAAA,MACjD,WAAW,CAAC,SAAS;AACnB,YAAI,SAAS,QAAQ;AACwB;AACjC,oBAAA;AAAA,cACN,yIAAyI,KAAK,SAAS;AAAA,YACzJ;AAAA,UAAA;AAEF,kBAAQ,IAAI,MAAM,GAAG,KAAK,SAAS,oBAAoB,CAAC;AACxD;AAAA,QAAA;AAEE,YAAA;AACF,eAAK,QAAQ,IAAI;AAAA,iBACV,OAAO;AACd,kBAAQ,KAAK;AACb;AAAA,QAAA;AAEF,aAAK,OAAO,OAAO,YAAY,MAAM,IAAI;AACzC,aAAK,OAAO,OAAO;AAAA,UACjB;AAAA,UACA,KAAK,MAAM;AAAA,UACX;AAAA,QACF;AACA,aAAK,WAAW;AAAA,MAClB;AAAA,MACA;AAAA,MACA,QAAQ,CAAC,cAAc,UAAU;AAC/B,aAAK,UAAU,EAAE,MAAM,UAAU,cAAc,OAAO;AAAA,MACxD;AAAA,MACA,SAAS,MAAM;AACb,aAAK,UAAU,EAAE,MAAM,QAAA,CAAS;AAAA,MAClC;AAAA,MACA,YAAY,MAAM;AAChB,aAAK,UAAU,EAAE,MAAM,WAAA,CAAY;AAAA,MACrC;AAAA,MACA,OAAO,QAAQ,QAAQ;AAAA,MACvB,YAAY,QAAQ,QAAQ;AAAA,MAC5B,aAAa,QAAQ,QAAQ;AAAA,MAC7B,QAAQ,MAAM;AAAA,IAAA,CACf;AACM,WAAA,KAAK,SAAS,MAAM;AAAA,EAAA;AAAA,EAE7B,UAAU,QAAQ;AACV,UAAA,UAAU,CAAC,UAAU;AACzB,cAAQ,OAAO,MAAM;AAAA,QACnB,KAAK;AACI,iBAAA;AAAA,YACL,GAAG;AAAA,YACH,mBAAmB,OAAO;AAAA,YAC1B,oBAAoB,OAAO;AAAA,UAC7B;AAAA,QACF,KAAK;AACI,iBAAA;AAAA,YACL,GAAG;AAAA,YACH,aAAa;AAAA,UACf;AAAA,QACF,KAAK;AACI,iBAAA;AAAA,YACL,GAAG;AAAA,YACH,aAAa;AAAA,UACf;AAAA,QACF,KAAK;AACI,iBAAA;AAAA,YACL,GAAG;AAAA,YACH,GAAG,WAAW,MAAM,MAAM,KAAK,OAAO;AAAA,YACtC,WAAW,OAAO,QAAQ;AAAA,UAC5B;AAAA,QACF,KAAK;AACI,iBAAA;AAAA,YACL,GAAG;AAAA,YACH,MAAM,OAAO;AAAA,YACb,iBAAiB,MAAM,kBAAkB;AAAA,YACzC,eAAe,OAAO,iBAAiB,KAAK,IAAI;AAAA,YAChD,OAAO;AAAA,YACP,eAAe;AAAA,YACf,QAAQ;AAAA,YACR,GAAG,CAAC,OAAO,UAAU;AAAA,cACnB,aAAa;AAAA,cACb,mBAAmB;AAAA,cACnB,oBAAoB;AAAA,YAAA;AAAA,UAExB;AAAA,QACF,KAAK;AACH,gBAAM,QAAQ,OAAO;AACrB,cAAI,iBAAiB,KAAK,KAAK,MAAM,UAAU,KAAK,cAAc;AAChE,mBAAO,EAAE,GAAG,KAAK,cAAc,aAAa,OAAO;AAAA,UAAA;AAE9C,iBAAA;AAAA,YACL,GAAG;AAAA,YACH;AAAA,YACA,kBAAkB,MAAM,mBAAmB;AAAA,YAC3C,gBAAgB,KAAK,IAAI;AAAA,YACzB,mBAAmB,MAAM,oBAAoB;AAAA,YAC7C,oBAAoB;AAAA,YACpB,aAAa;AAAA,YACb,QAAQ;AAAA,UACV;AAAA,QACF,KAAK;AACI,iBAAA;AAAA,YACL,GAAG;AAAA,YACH,eAAe;AAAA,UACjB;AAAA,QACF,KAAK;AACI,iBAAA;AAAA,YACL,GAAG;AAAA,YACH,GAAG,OAAO;AAAA,UACZ;AAAA,MAAA;AAAA,IAEN;AACK,SAAA,QAAQ,QAAQ,KAAK,KAAK;AAC/B,kBAAc,MAAM,MAAM;AACnB,WAAA,UAAU,QAAQ,CAAC,aAAa;AACnC,iBAAS,cAAc;AAAA,MAAA,CACxB;AACI,WAAA,OAAO,OAAO,EAAE,OAAO,MAAM,MAAM,WAAW,QAAQ;AAAA,IAAA,CAC5D;AAAA,EAAA;AAEL;AACA,SAAS,WAAW,MAAM,SAAS;AAC1B,SAAA;AAAA,IACL,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,aAAa,SAAS,QAAQ,WAAW,IAAI,aAAa;AAAA,IAC1D,GAAG,SAAS,UAAU;AAAA,MACpB,OAAO;AAAA,MACP,QAAQ;AAAA,IAAA;AAAA,EAEZ;AACF;AACA,SAASA,kBAAgB,SAAS;AAC1B,QAAA,OAAO,OAAO,QAAQ,gBAAgB,aAAa,QAAQ,gBAAgB,QAAQ;AACzF,QAAM,UAAU,SAAS;AACnB,QAAA,uBAAuB,UAAU,OAAO,QAAQ,yBAAyB,aAAa,QAAQ,qBAAA,IAAyB,QAAQ,uBAAuB;AACrJ,SAAA;AAAA,IACL;AAAA,IACA,iBAAiB;AAAA,IACjB,eAAe,UAAU,wBAAwB,KAAK,IAAQ,IAAA;AAAA,IAC9D,OAAO;AAAA,IACP,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,QAAQ,UAAU,YAAY;AAAA,IAC9B,aAAa;AAAA,EACf;AACF;AC5YA,IAAI,aAAa,cAAc,aAAa;AAAA,EAC1C,YAAY,SAAS,IAAI;AACvB,UAAO;AACP,SAAK,SAAS;AACd,SAAK,WAA2B,oBAAI,IAAK;AAAA,EAC7C;AAAA,EACE;AAAA,EACA,MAAM,QAAQ,SAAS,OAAO;AAC5B,UAAM,WAAW,QAAQ;AACzB,UAAM,YAAY,QAAQ,aAAa,sBAAsB,UAAU,OAAO;AAC9E,QAAI,QAAQ,KAAK,IAAI,SAAS;AAC9B,QAAI,CAAC,OAAO;AACV,cAAQ,IAAI,MAAM;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS,OAAO,oBAAoB,OAAO;AAAA,QAC3C;AAAA,QACA,gBAAgB,OAAO,iBAAiB,QAAQ;AAAA,MACxD,CAAO;AACD,WAAK,IAAI,KAAK;AAAA,IACpB;AACI,WAAO;AAAA,EACX;AAAA,EACE,IAAI,OAAO;AACT,QAAI,CAAC,KAAK,SAAS,IAAI,MAAM,SAAS,GAAG;AACvC,WAAK,SAAS,IAAI,MAAM,WAAW,KAAK;AACxC,WAAK,OAAO;AAAA,QACV,MAAM;AAAA,QACN;AAAA,MACR,CAAO;AAAA,IACP;AAAA,EACA;AAAA,EACE,OAAO,OAAO;AACZ,UAAM,aAAa,KAAK,SAAS,IAAI,MAAM,SAAS;AACpD,QAAI,YAAY;AACd,YAAM,QAAS;AACf,UAAI,eAAe,OAAO;AACxB,aAAK,SAAS,OAAO,MAAM,SAAS;AAAA,MAC5C;AACM,WAAK,OAAO,EAAE,MAAM,WAAW,MAAK,CAAE;AAAA,IAC5C;AAAA,EACA;AAAA,EACE,QAAQ;AACN,kBAAc,MAAM,MAAM;AACxB,WAAK,OAAM,EAAG,QAAQ,CAAC,UAAU;AAC/B,aAAK,OAAO,KAAK;AAAA,MACzB,CAAO;AAAA,IACP,CAAK;AAAA,EACL;AAAA,EACE,IAAI,WAAW;AACb,WAAO,KAAK,SAAS,IAAI,SAAS;AAAA,EACtC;AAAA,EACE,SAAS;AACP,WAAO,CAAC,GAAG,KAAK,SAAS,OAAM,CAAE;AAAA,EACrC;AAAA,EACE,KAAK,SAAS;AACZ,UAAM,mBAAmB,EAAE,OAAO,MAAM,GAAG,QAAS;AACpD,WAAO,KAAK,OAAM,EAAG;AAAA,MACnB,CAAC,UAAU,WAAW,kBAAkB,KAAK;AAAA,IAC9C;AAAA,EACL;AAAA,EACE,QAAQ,UAAU,IAAI;AACpB,UAAM,UAAU,KAAK,OAAQ;AAC7B,WAAO,OAAO,KAAK,OAAO,EAAE,SAAS,IAAI,QAAQ,OAAO,CAAC,UAAU,WAAW,SAAS,KAAK,CAAC,IAAI;AAAA,EACrG;AAAA,EACE,OAAO,OAAO;AACZ,kBAAc,MAAM,MAAM;AACxB,WAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,iBAAS,KAAK;AAAA,MACtB,CAAO;AAAA,IACP,CAAK;AAAA,EACL;AAAA,EACE,UAAU;AACR,kBAAc,MAAM,MAAM;AACxB,WAAK,OAAM,EAAG,QAAQ,CAAC,UAAU;AAC/B,cAAM,QAAS;AAAA,MACvB,CAAO;AAAA,IACP,CAAK;AAAA,EACL;AAAA,EACE,WAAW;AACT,kBAAc,MAAM,MAAM;AACxB,WAAK,OAAM,EAAG,QAAQ,CAAC,UAAU;AAC/B,cAAM,SAAU;AAAA,MACxB,CAAO;AAAA,IACP,CAAK;AAAA,EACL;AACA;ACxFA,IAAI,WAAW,cAAc,UAAU;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,QAAQ;AAClB,UAAO;AACP,SAAK,aAAa,OAAO;AACzB,SAAK,iBAAiB,OAAO;AAC7B,SAAK,aAAa,CAAE;AACpB,SAAK,QAAQ,OAAO,SAAS,gBAAiB;AAC9C,SAAK,WAAW,OAAO,OAAO;AAC9B,SAAK,WAAY;AAAA,EACrB;AAAA,EACE,WAAW,SAAS;AAClB,SAAK,UAAU;AACf,SAAK,aAAa,KAAK,QAAQ,MAAM;AAAA,EACzC;AAAA,EACE,IAAI,OAAO;AACT,WAAO,KAAK,QAAQ;AAAA,EACxB;AAAA,EACE,YAAY,UAAU;AACpB,QAAI,CAAC,KAAK,WAAW,SAAS,QAAQ,GAAG;AACvC,WAAK,WAAW,KAAK,QAAQ;AAC7B,WAAK,eAAgB;AACrB,WAAK,eAAe,OAAO;AAAA,QACzB,MAAM;AAAA,QACN,UAAU;AAAA,QACV;AAAA,MACR,CAAO;AAAA,IACP;AAAA,EACA;AAAA,EACE,eAAe,UAAU;AACvB,SAAK,aAAa,KAAK,WAAW,OAAO,CAAC,MAAM,MAAM,QAAQ;AAC9D,SAAK,WAAY;AACjB,SAAK,eAAe,OAAO;AAAA,MACzB,MAAM;AAAA,MACN,UAAU;AAAA,MACV;AAAA,IACN,CAAK;AAAA,EACL;AAAA,EACE,iBAAiB;AACf,QAAI,CAAC,KAAK,WAAW,QAAQ;AAC3B,UAAI,KAAK,MAAM,WAAW,WAAW;AACnC,aAAK,WAAY;AAAA,MACzB,OAAa;AACL,aAAK,eAAe,OAAO,IAAI;AAAA,MACvC;AAAA,IACA;AAAA,EACA;AAAA,EACE,WAAW;AACT,WAAO,KAAK,UAAU,SAAU;AAAA,IAChC,KAAK,QAAQ,KAAK,MAAM,SAAS;AAAA,EACrC;AAAA,EACE,MAAM,QAAQ,WAAW;AACvB,UAAM,aAAa,MAAM;AACvB,WAAK,UAAU,EAAE,MAAM,WAAU,CAAE;AAAA,IACpC;AACD,SAAK,WAAW,cAAc;AAAA,MAC5B,IAAI,MAAM;AACR,YAAI,CAAC,KAAK,QAAQ,YAAY;AAC5B,iBAAO,QAAQ,OAAO,IAAI,MAAM,qBAAqB,CAAC;AAAA,QAChE;AACQ,eAAO,KAAK,QAAQ,WAAW,SAAS;AAAA,MACzC;AAAA,MACD,QAAQ,CAAC,cAAc,UAAU;AAC/B,aAAK,UAAU,EAAE,MAAM,UAAU,cAAc,OAAO;AAAA,MACvD;AAAA,MACD,SAAS,MAAM;AACb,aAAK,UAAU,EAAE,MAAM,QAAO,CAAE;AAAA,MACjC;AAAA,MACD;AAAA,MACA,OAAO,KAAK,QAAQ,SAAS;AAAA,MAC7B,YAAY,KAAK,QAAQ;AAAA,MACzB,aAAa,KAAK,QAAQ;AAAA,MAC1B,QAAQ,MAAM,KAAK,eAAe,OAAO,IAAI;AAAA,IACnD,CAAK;AACD,UAAM,WAAW,KAAK,MAAM,WAAW;AACvC,UAAM,WAAW,CAAC,KAAK,SAAS,SAAU;AAC1C,QAAI;AACF,UAAI,UAAU;AACZ,mBAAY;AAAA,MACpB,OAAa;AACL,aAAK,UAAU,EAAE,MAAM,WAAW,WAAW,UAAU;AACvD,cAAM,KAAK,eAAe,OAAO;AAAA,UAC/B;AAAA,UACA;AAAA,QACD;AACD,cAAM,UAAU,MAAM,KAAK,QAAQ,WAAW,SAAS;AACvD,YAAI,YAAY,KAAK,MAAM,SAAS;AAClC,eAAK,UAAU;AAAA,YACb,MAAM;AAAA,YACN;AAAA,YACA;AAAA,YACA;AAAA,UACZ,CAAW;AAAA,QACX;AAAA,MACA;AACM,YAAM,OAAO,MAAM,KAAK,SAAS,MAAO;AACxC,YAAM,KAAK,eAAe,OAAO;AAAA,QAC/B;AAAA,QACA;AAAA,QACA,KAAK,MAAM;AAAA,QACX;AAAA,MACD;AACD,YAAM,KAAK,QAAQ,YAAY,MAAM,WAAW,KAAK,MAAM,OAAO;AAClE,YAAM,KAAK,eAAe,OAAO;AAAA,QAC/B;AAAA,QACA;AAAA,QACA,KAAK,MAAM;AAAA,QACX,KAAK,MAAM;AAAA,QACX;AAAA,MACD;AACD,YAAM,KAAK,QAAQ,YAAY,MAAM,MAAM,WAAW,KAAK,MAAM,OAAO;AACxE,WAAK,UAAU,EAAE,MAAM,WAAW,KAAI,CAAE;AACxC,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,UAAI;AACF,cAAM,KAAK,eAAe,OAAO;AAAA,UAC/B;AAAA,UACA;AAAA,UACA,KAAK,MAAM;AAAA,UACX;AAAA,QACD;AACD,cAAM,KAAK,QAAQ;AAAA,UACjB;AAAA,UACA;AAAA,UACA,KAAK,MAAM;AAAA,QACZ;AACD,cAAM,KAAK,eAAe,OAAO;AAAA,UAC/B;AAAA,UACA;AAAA,UACA,KAAK,MAAM;AAAA,UACX,KAAK,MAAM;AAAA,UACX;AAAA,QACD;AACD,cAAM,KAAK,QAAQ;AAAA,UACjB;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK,MAAM;AAAA,QACZ;AACD,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,UAAU,EAAE,MAAM,SAAS,MAAK,CAAE;AAAA,MAC/C;AAAA,IACA,UAAc;AACR,WAAK,eAAe,QAAQ,IAAI;AAAA,IACtC;AAAA,EACA;AAAA,EACE,UAAU,QAAQ;AAChB,UAAM,UAAU,CAAC,UAAU;AACzB,cAAQ,OAAO,MAAI;AAAA,QACjB,KAAK;AACH,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,cAAc,OAAO;AAAA,YACrB,eAAe,OAAO;AAAA,UACvB;AAAA,QACH,KAAK;AACH,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,UAAU;AAAA,UACX;AAAA,QACH,KAAK;AACH,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,UAAU;AAAA,UACX;AAAA,QACH,KAAK;AACH,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,SAAS,OAAO;AAAA,YAChB,MAAM;AAAA,YACN,cAAc;AAAA,YACd,eAAe;AAAA,YACf,OAAO;AAAA,YACP,UAAU,OAAO;AAAA,YACjB,QAAQ;AAAA,YACR,WAAW,OAAO;AAAA,YAClB,aAAa,KAAK,IAAG;AAAA,UACtB;AAAA,QACH,KAAK;AACH,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,MAAM,OAAO;AAAA,YACb,cAAc;AAAA,YACd,eAAe;AAAA,YACf,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,UAAU;AAAA,UACX;AAAA,QACH,KAAK;AACH,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,MAAM;AAAA,YACN,OAAO,OAAO;AAAA,YACd,cAAc,MAAM,eAAe;AAAA,YACnC,eAAe,OAAO;AAAA,YACtB,UAAU;AAAA,YACV,QAAQ;AAAA,UACT;AAAA,MACX;AAAA,IACK;AACD,SAAK,QAAQ,QAAQ,KAAK,KAAK;AAC/B,kBAAc,MAAM,MAAM;AACxB,WAAK,WAAW,QAAQ,CAAC,aAAa;AACpC,iBAAS,iBAAiB,MAAM;AAAA,MACxC,CAAO;AACD,WAAK,eAAe,OAAO;AAAA,QACzB,UAAU;AAAA,QACV,MAAM;AAAA,QACN;AAAA,MACR,CAAO;AAAA,IACP,CAAK;AAAA,EACL;AACA;AACA,SAAS,kBAAkB;AACzB,SAAO;AAAA,IACL,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,IACP,cAAc;AAAA,IACd,eAAe;AAAA,IACf,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,EACd;AACH;ACnOA,IAAI,gBAAgB,cAAc,aAAa;AAAA,EAC7C,YAAY,SAAS,IAAI;AACvB,UAAO;AACP,SAAK,SAAS;AACd,SAAK,aAA6B,oBAAI,IAAK;AAC3C,SAAK,UAA0B,oBAAI,IAAK;AACxC,SAAK,cAAc;AAAA,EACvB;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM,QAAQ,SAAS,OAAO;AAC5B,UAAM,WAAW,IAAI,SAAS;AAAA,MAC5B,eAAe;AAAA,MACf,YAAY,EAAE,KAAK;AAAA,MACnB,SAAS,OAAO,uBAAuB,OAAO;AAAA,MAC9C;AAAA,IACN,CAAK;AACD,SAAK,IAAI,QAAQ;AACjB,WAAO;AAAA,EACX;AAAA,EACE,IAAI,UAAU;AACZ,SAAK,WAAW,IAAI,QAAQ;AAC5B,UAAM,QAAQ,SAAS,QAAQ;AAC/B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,kBAAkB,KAAK,QAAQ,IAAI,KAAK;AAC9C,UAAI,iBAAiB;AACnB,wBAAgB,KAAK,QAAQ;AAAA,MACrC,OAAa;AACL,aAAK,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC;AAAA,MAC1C;AAAA,IACA;AACI,SAAK,OAAO,EAAE,MAAM,SAAS,SAAQ,CAAE;AAAA,EAC3C;AAAA,EACE,OAAO,UAAU;AACf,QAAI,KAAK,WAAW,OAAO,QAAQ,GAAG;AACpC,YAAM,QAAQ,SAAS,QAAQ;AAC/B,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,kBAAkB,KAAK,QAAQ,IAAI,KAAK;AAC9C,YAAI,iBAAiB;AACnB,cAAI,gBAAgB,SAAS,GAAG;AAC9B,kBAAM,QAAQ,gBAAgB,QAAQ,QAAQ;AAC9C,gBAAI,UAAU,IAAI;AAChB,8BAAgB,OAAO,OAAO,CAAC;AAAA,YAC7C;AAAA,UACW,WAAU,gBAAgB,CAAC,MAAM,UAAU;AAC1C,iBAAK,QAAQ,OAAO,KAAK;AAAA,UACrC;AAAA,QACA;AAAA,MACA;AAAA,IACA;AACI,SAAK,OAAO,EAAE,MAAM,WAAW,SAAQ,CAAE;AAAA,EAC7C;AAAA,EACE,OAAO,UAAU;AACf,UAAM,QAAQ,SAAS,QAAQ;AAC/B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,yBAAyB,KAAK,QAAQ,IAAI,KAAK;AACrD,YAAM,uBAAuB,wBAAwB;AAAA,QACnD,CAAC,MAAM,EAAE,MAAM,WAAW;AAAA,MAC3B;AACD,aAAO,CAAC,wBAAwB,yBAAyB;AAAA,IAC/D,OAAW;AACL,aAAO;AAAA,IACb;AAAA,EACA;AAAA,EACE,QAAQ,UAAU;AAChB,UAAM,QAAQ,SAAS,QAAQ;AAC/B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,gBAAgB,KAAK,QAAQ,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,MAAM,YAAY,EAAE,MAAM,QAAQ;AAC7F,aAAO,eAAe,cAAc,QAAQ,QAAS;AAAA,IAC3D,OAAW;AACL,aAAO,QAAQ,QAAS;AAAA,IAC9B;AAAA,EACA;AAAA,EACE,QAAQ;AACN,kBAAc,MAAM,MAAM;AACxB,WAAK,WAAW,QAAQ,CAAC,aAAa;AACpC,aAAK,OAAO,EAAE,MAAM,WAAW,SAAQ,CAAE;AAAA,MACjD,CAAO;AACD,WAAK,WAAW,MAAO;AACvB,WAAK,QAAQ,MAAO;AAAA,IAC1B,CAAK;AAAA,EACL;AAAA,EACE,SAAS;AACP,WAAO,MAAM,KAAK,KAAK,UAAU;AAAA,EACrC;AAAA,EACE,KAAK,SAAS;AACZ,UAAM,mBAAmB,EAAE,OAAO,MAAM,GAAG,QAAS;AACpD,WAAO,KAAK,OAAM,EAAG;AAAA,MACnB,CAAC,aAAa,cAAc,kBAAkB,QAAQ;AAAA,IACvD;AAAA,EACL;AAAA,EACE,QAAQ,UAAU,IAAI;AACpB,WAAO,KAAK,OAAM,EAAG,OAAO,CAAC,aAAa,cAAc,SAAS,QAAQ,CAAC;AAAA,EAC9E;AAAA,EACE,OAAO,OAAO;AACZ,kBAAc,MAAM,MAAM;AACxB,WAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,iBAAS,KAAK;AAAA,MACtB,CAAO;AAAA,IACP,CAAK;AAAA,EACL;AAAA,EACE,wBAAwB;AACtB,UAAM,kBAAkB,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,MAAM,QAAQ;AACpE,WAAO,cAAc;AAAA,MACnB,MAAM,QAAQ;AAAA,QACZ,gBAAgB,IAAI,CAAC,aAAa,SAAS,SAAU,EAAC,MAAM,IAAI,CAAC;AAAA,MACzE;AAAA,IACK;AAAA,EACL;AACA;AACA,SAAS,SAAS,UAAU;AAC1B,SAAO,SAAS,QAAQ,OAAO;AACjC;ACpHA,SAAS,sBAAsB,OAAO;AACpC,SAAO;AAAA,IACL,SAAS,CAAC,SAAS,UAAU;AAC3B,YAAM,UAAU,QAAQ;AACxB,YAAM,YAAY,QAAQ,cAAc,MAAM,WAAW;AACzD,YAAM,WAAW,QAAQ,MAAM,MAAM,SAAS,CAAE;AAChD,YAAM,gBAAgB,QAAQ,MAAM,MAAM,cAAc,CAAE;AAC1D,UAAI,SAAS,EAAE,OAAO,CAAA,GAAI,YAAY,CAAA,EAAI;AAC1C,UAAI,cAAc;AAClB,YAAM,UAAU,YAAY;AAC1B,YAAI,YAAY;AAChB,cAAM,oBAAoB,CAAC,WAAW;AACpC,iBAAO,eAAe,QAAQ,UAAU;AAAA,YACtC,YAAY;AAAA,YACZ,KAAK,MAAM;AACT,kBAAI,QAAQ,OAAO,SAAS;AAC1B,4BAAY;AAAA,cAC5B,OAAqB;AACL,wBAAQ,OAAO,iBAAiB,SAAS,MAAM;AAC7C,8BAAY;AAAA,gBAC9B,CAAiB;AAAA,cACjB;AACc,qBAAO,QAAQ;AAAA,YAC7B;AAAA,UACA,CAAW;AAAA,QACF;AACD,cAAM,UAAU,cAAc,QAAQ,SAAS,QAAQ,YAAY;AACnE,cAAM,YAAY,OAAO,MAAM,OAAO,aAAa;AACjD,cAAI,WAAW;AACb,mBAAO,QAAQ,OAAQ;AAAA,UACnC;AACU,cAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AACtC,mBAAO,QAAQ,QAAQ,IAAI;AAAA,UACvC;AACU,gBAAM,uBAAuB,MAAM;AACjC,kBAAM,kBAAkB;AAAA,cACtB,QAAQ,QAAQ;AAAA,cAChB,UAAU,QAAQ;AAAA,cAClB,WAAW;AAAA,cACX,WAAW,WAAW,aAAa;AAAA,cACnC,MAAM,QAAQ,QAAQ;AAAA,YACvB;AACD,8BAAkB,eAAe;AACjC,mBAAO;AAAA,UACR;AACD,gBAAM,iBAAiB,qBAAsB;AAC7C,gBAAM,OAAO,MAAM,QAAQ,cAAc;AACzC,gBAAM,EAAE,aAAa,QAAQ;AAC7B,gBAAM,QAAQ,WAAW,aAAa;AACtC,iBAAO;AAAA,YACL,OAAO,MAAM,KAAK,OAAO,MAAM,QAAQ;AAAA,YACvC,YAAY,MAAM,KAAK,YAAY,OAAO,QAAQ;AAAA,UACnD;AAAA,QACF;AACD,YAAI,aAAa,SAAS,QAAQ;AAChC,gBAAM,WAAW,cAAc;AAC/B,gBAAM,cAAc,WAAW,uBAAuB;AACtD,gBAAM,UAAU;AAAA,YACd,OAAO;AAAA,YACP,YAAY;AAAA,UACb;AACD,gBAAM,QAAQ,YAAY,SAAS,OAAO;AAC1C,mBAAS,MAAM,UAAU,SAAS,OAAO,QAAQ;AAAA,QAC3D,OAAe;AACL,gBAAM,iBAAiB,SAAS,SAAS;AACzC,aAAG;AACD,kBAAM,QAAQ,gBAAgB,IAAI,cAAc,CAAC,KAAK,QAAQ,mBAAmB,iBAAiB,SAAS,MAAM;AACjH,gBAAI,cAAc,KAAK,SAAS,MAAM;AACpC;AAAA,YACd;AACY,qBAAS,MAAM,UAAU,QAAQ,KAAK;AACtC;AAAA,UACD,SAAQ,cAAc;AAAA,QACjC;AACQ,eAAO;AAAA,MACR;AACD,UAAI,QAAQ,QAAQ,WAAW;AAC7B,gBAAQ,UAAU,MAAM;AACtB,iBAAO,QAAQ,QAAQ;AAAA,YACrB;AAAA,YACA;AAAA,cACE,QAAQ,QAAQ;AAAA,cAChB,UAAU,QAAQ;AAAA,cAClB,MAAM,QAAQ,QAAQ;AAAA,cACtB,QAAQ,QAAQ;AAAA,YACjB;AAAA,YACD;AAAA,UACD;AAAA,QACF;AAAA,MACT,OAAa;AACL,gBAAQ,UAAU;AAAA,MAC1B;AAAA,IACA;AAAA,EACG;AACH;AACA,SAAS,iBAAiB,SAAS,EAAE,OAAO,WAAU,GAAI;AACxD,QAAM,YAAY,MAAM,SAAS;AACjC,SAAO,MAAM,SAAS,IAAI,QAAQ;AAAA,IAChC,MAAM,SAAS;AAAA,IACf;AAAA,IACA,WAAW,SAAS;AAAA,IACpB;AAAA,EACJ,IAAM;AACN;AACA,SAAS,qBAAqB,SAAS,EAAE,OAAO,WAAU,GAAI;AAC5D,SAAO,MAAM,SAAS,IAAI,QAAQ,uBAAuB,MAAM,CAAC,GAAG,OAAO,WAAW,CAAC,GAAG,UAAU,IAAI;AACzG;AC5FG,IAAC,cAAc,MAAM;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,SAAS,IAAI;AACvB,SAAK,cAAc,OAAO,cAAc,IAAI,WAAY;AACxD,SAAK,iBAAiB,OAAO,iBAAiB,IAAI,cAAe;AACjE,SAAK,kBAAkB,OAAO,kBAAkB,CAAE;AAClD,SAAK,iBAAiC,oBAAI,IAAK;AAC/C,SAAK,oBAAoC,oBAAI,IAAK;AAClD,SAAK,cAAc;AAAA,EACvB;AAAA,EACE,QAAQ;AACN,SAAK;AACL,QAAI,KAAK,gBAAgB,EAAG;AAC5B,SAAK,oBAAoB,aAAa,UAAU,OAAO,YAAY;AACjE,UAAI,SAAS;AACX,cAAM,KAAK,sBAAuB;AAClC,aAAK,YAAY,QAAS;AAAA,MAClC;AAAA,IACA,CAAK;AACD,SAAK,qBAAqB,cAAc,UAAU,OAAO,WAAW;AAClE,UAAI,QAAQ;AACV,cAAM,KAAK,sBAAuB;AAClC,aAAK,YAAY,SAAU;AAAA,MACnC;AAAA,IACA,CAAK;AAAA,EACL;AAAA,EACE,UAAU;AACR,SAAK;AACL,QAAI,KAAK,gBAAgB,EAAG;AAC5B,SAAK,oBAAqB;AAC1B,SAAK,oBAAoB;AACzB,SAAK,qBAAsB;AAC3B,SAAK,qBAAqB;AAAA,EAC9B;AAAA,EACE,WAAW,SAAS;AAClB,WAAO,KAAK,YAAY,QAAQ,EAAE,GAAG,SAAS,aAAa,WAAY,CAAA,EAAE;AAAA,EAC7E;AAAA,EACE,WAAW,SAAS;AAClB,WAAO,KAAK,eAAe,QAAQ,EAAE,GAAG,SAAS,QAAQ,UAAW,CAAA,EAAE;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQE,aAAa,UAAU;AACrB,UAAM,UAAU,KAAK,oBAAoB,EAAE,SAAQ,CAAE;AACrD,WAAO,KAAK,YAAY,IAAI,QAAQ,SAAS,GAAG,MAAM;AAAA,EAC1D;AAAA,EACE,gBAAgB,SAAS;AACvB,UAAM,mBAAmB,KAAK,oBAAoB,OAAO;AACzD,UAAM,QAAQ,KAAK,YAAY,MAAM,MAAM,gBAAgB;AAC3D,UAAM,aAAa,MAAM,MAAM;AAC/B,QAAI,eAAe,QAAQ;AACzB,aAAO,KAAK,WAAW,OAAO;AAAA,IACpC;AACI,QAAI,QAAQ,qBAAqB,MAAM,cAAc,iBAAiB,iBAAiB,WAAW,KAAK,CAAC,GAAG;AACzG,WAAK,KAAK,cAAc,gBAAgB;AAAA,IAC9C;AACI,WAAO,QAAQ,QAAQ,UAAU;AAAA,EACrC;AAAA,EACE,eAAe,SAAS;AACtB,WAAO,KAAK,YAAY,QAAQ,OAAO,EAAE,IAAI,CAAC,EAAE,UAAU,YAAY;AACpE,YAAM,OAAO,MAAM;AACnB,aAAO,CAAC,UAAU,IAAI;AAAA,IAC5B,CAAK;AAAA,EACL;AAAA,EACE,aAAa,UAAU,SAAS,SAAS;AACvC,UAAM,mBAAmB,KAAK,oBAAoB,EAAE,SAAQ,CAAE;AAC9D,UAAM,QAAQ,KAAK,YAAY;AAAA,MAC7B,iBAAiB;AAAA,IAClB;AACD,UAAM,WAAW,OAAO,MAAM;AAC9B,UAAM,OAAO,iBAAiB,SAAS,QAAQ;AAC/C,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,IACb;AACI,WAAO,KAAK,YAAY,MAAM,MAAM,gBAAgB,EAAE,QAAQ,MAAM,EAAE,GAAG,SAAS,QAAQ,KAAI,CAAE;AAAA,EACpG;AAAA,EACE,eAAe,SAAS,SAAS,SAAS;AACxC,WAAO,cAAc;AAAA,MACnB,MAAM,KAAK,YAAY,QAAQ,OAAO,EAAE,IAAI,CAAC,EAAE,eAAe;AAAA,QAC5D;AAAA,QACA,KAAK,aAAa,UAAU,SAAS,OAAO;AAAA,MAC7C,CAAA;AAAA,IACF;AAAA,EACL;AAAA,EACE,cAAc,UAAU;AACtB,UAAM,UAAU,KAAK,oBAAoB,EAAE,SAAQ,CAAE;AACrD,WAAO,KAAK,YAAY;AAAA,MACtB,QAAQ;AAAA,IACd,GAAO;AAAA,EACP;AAAA,EACE,cAAc,SAAS;AACrB,UAAM,aAAa,KAAK;AACxB,kBAAc,MAAM,MAAM;AACxB,iBAAW,QAAQ,OAAO,EAAE,QAAQ,CAAC,UAAU;AAC7C,mBAAW,OAAO,KAAK;AAAA,MAC/B,CAAO;AAAA,IACP,CAAK;AAAA,EACL;AAAA,EACE,aAAa,SAAS,SAAS;AAC7B,UAAM,aAAa,KAAK;AACxB,WAAO,cAAc,MAAM,MAAM;AAC/B,iBAAW,QAAQ,OAAO,EAAE,QAAQ,CAAC,UAAU;AAC7C,cAAM,MAAO;AAAA,MACrB,CAAO;AACD,aAAO,KAAK;AAAA,QACV;AAAA,UACE,MAAM;AAAA,UACN,GAAG;AAAA,QACJ;AAAA,QACD;AAAA,MACD;AAAA,IACP,CAAK;AAAA,EACL;AAAA,EACE,cAAc,SAAS,gBAAgB,IAAI;AACzC,UAAM,yBAAyB,EAAE,QAAQ,MAAM,GAAG,cAAe;AACjE,UAAM,WAAW,cAAc;AAAA,MAC7B,MAAM,KAAK,YAAY,QAAQ,OAAO,EAAE,IAAI,CAAC,UAAU,MAAM,OAAO,sBAAsB,CAAC;AAAA,IAC5F;AACD,WAAO,QAAQ,IAAI,QAAQ,EAAE,KAAK,IAAI,EAAE,MAAM,IAAI;AAAA,EACtD;AAAA,EACE,kBAAkB,SAAS,UAAU,IAAI;AACvC,WAAO,cAAc,MAAM,MAAM;AAC/B,WAAK,YAAY,QAAQ,OAAO,EAAE,QAAQ,CAAC,UAAU;AACnD,cAAM,WAAY;AAAA,MAC1B,CAAO;AACD,UAAI,SAAS,gBAAgB,QAAQ;AACnC,eAAO,QAAQ,QAAS;AAAA,MAChC;AACM,aAAO,KAAK;AAAA,QACV;AAAA,UACE,GAAG;AAAA,UACH,MAAM,SAAS,eAAe,SAAS,QAAQ;AAAA,QAChD;AAAA,QACD;AAAA,MACD;AAAA,IACP,CAAK;AAAA,EACL;AAAA,EACE,eAAe,SAAS,UAAU,IAAI;AACpC,UAAM,eAAe;AAAA,MACnB,GAAG;AAAA,MACH,eAAe,QAAQ,iBAAiB;AAAA,IACzC;AACD,UAAM,WAAW,cAAc;AAAA,MAC7B,MAAM,KAAK,YAAY,QAAQ,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,MAAM,WAAU,KAAM,CAAC,MAAM,SAAU,CAAA,EAAE,IAAI,CAAC,UAAU;AACjH,YAAI,UAAU,MAAM,MAAM,QAAQ,YAAY;AAC9C,YAAI,CAAC,aAAa,cAAc;AAC9B,oBAAU,QAAQ,MAAM,IAAI;AAAA,QACtC;AACQ,eAAO,MAAM,MAAM,gBAAgB,WAAW,QAAQ,QAAO,IAAK;AAAA,MACnE,CAAA;AAAA,IACF;AACD,WAAO,QAAQ,IAAI,QAAQ,EAAE,KAAK,IAAI;AAAA,EAC1C;AAAA,EACE,WAAW,SAAS;AAClB,UAAM,mBAAmB,KAAK,oBAAoB,OAAO;AACzD,QAAI,iBAAiB,UAAU,QAAQ;AACrC,uBAAiB,QAAQ;AAAA,IAC/B;AACI,UAAM,QAAQ,KAAK,YAAY,MAAM,MAAM,gBAAgB;AAC3D,WAAO,MAAM;AAAA,MACX,iBAAiB,iBAAiB,WAAW,KAAK;AAAA,IACxD,IAAQ,MAAM,MAAM,gBAAgB,IAAI,QAAQ,QAAQ,MAAM,MAAM,IAAI;AAAA,EACxE;AAAA,EACE,cAAc,SAAS;AACrB,WAAO,KAAK,WAAW,OAAO,EAAE,KAAK,IAAI,EAAE,MAAM,IAAI;AAAA,EACzD;AAAA,EACE,mBAAmB,SAAS;AAC1B,YAAQ,WAAW,sBAAsB,QAAQ,KAAK;AACtD,WAAO,KAAK,WAAW,OAAO;AAAA,EAClC;AAAA,EACE,sBAAsB,SAAS;AAC7B,WAAO,KAAK,mBAAmB,OAAO,EAAE,KAAK,IAAI,EAAE,MAAM,IAAI;AAAA,EACjE;AAAA,EACE,wBAAwB,SAAS;AAC/B,YAAQ,WAAW,sBAAsB,QAAQ,KAAK;AACtD,WAAO,KAAK,gBAAgB,OAAO;AAAA,EACvC;AAAA,EACE,wBAAwB;AACtB,QAAI,cAAc,YAAY;AAC5B,aAAO,KAAK,eAAe,sBAAuB;AAAA,IACxD;AACI,WAAO,QAAQ,QAAS;AAAA,EAC5B;AAAA,EACE,gBAAgB;AACd,WAAO,KAAK;AAAA,EAChB;AAAA,EACE,mBAAmB;AACjB,WAAO,KAAK;AAAA,EAChB;AAAA,EACE,oBAAoB;AAClB,WAAO,KAAK;AAAA,EAChB;AAAA,EACE,kBAAkB,SAAS;AACzB,SAAK,kBAAkB;AAAA,EAC3B;AAAA,EACE,iBAAiB,UAAU,SAAS;AAClC,SAAK,eAAe,IAAI,QAAQ,QAAQ,GAAG;AAAA,MACzC;AAAA,MACA,gBAAgB;AAAA,IACtB,CAAK;AAAA,EACL;AAAA,EACE,iBAAiB,UAAU;AACzB,UAAM,WAAW,CAAC,GAAG,KAAK,eAAe,OAAM,CAAE;AACjD,UAAM,SAAS,CAAE;AACjB,aAAS,QAAQ,CAAC,iBAAiB;AACjC,UAAI,gBAAgB,UAAU,aAAa,QAAQ,GAAG;AACpD,eAAO,OAAO,QAAQ,aAAa,cAAc;AAAA,MACzD;AAAA,IACA,CAAK;AACD,WAAO;AAAA,EACX;AAAA,EACE,oBAAoB,aAAa,SAAS;AACxC,SAAK,kBAAkB,IAAI,QAAQ,WAAW,GAAG;AAAA,MAC/C;AAAA,MACA,gBAAgB;AAAA,IACtB,CAAK;AAAA,EACL;AAAA,EACE,oBAAoB,aAAa;AAC/B,UAAM,WAAW,CAAC,GAAG,KAAK,kBAAkB,OAAM,CAAE;AACpD,UAAM,SAAS,CAAE;AACjB,aAAS,QAAQ,CAAC,iBAAiB;AACjC,UAAI,gBAAgB,aAAa,aAAa,WAAW,GAAG;AAC1D,eAAO,OAAO,QAAQ,aAAa,cAAc;AAAA,MACzD;AAAA,IACA,CAAK;AACD,WAAO;AAAA,EACX;AAAA,EACE,oBAAoB,SAAS;AAC3B,QAAI,QAAQ,YAAY;AACtB,aAAO;AAAA,IACb;AACI,UAAM,mBAAmB;AAAA,MACvB,GAAG,KAAK,gBAAgB;AAAA,MACxB,GAAG,KAAK,iBAAiB,QAAQ,QAAQ;AAAA,MACzC,GAAG;AAAA,MACH,YAAY;AAAA,IACb;AACD,QAAI,CAAC,iBAAiB,WAAW;AAC/B,uBAAiB,YAAY;AAAA,QAC3B,iBAAiB;AAAA,QACjB;AAAA,MACD;AAAA,IACP;AACI,QAAI,iBAAiB,uBAAuB,QAAQ;AAClD,uBAAiB,qBAAqB,iBAAiB,gBAAgB;AAAA,IAC7E;AACI,QAAI,iBAAiB,iBAAiB,QAAQ;AAC5C,uBAAiB,eAAe,CAAC,CAAC,iBAAiB;AAAA,IACzD;AACI,QAAI,CAAC,iBAAiB,eAAe,iBAAiB,WAAW;AAC/D,uBAAiB,cAAc;AAAA,IACrC;AACI,QAAI,iBAAiB,YAAY,WAAW;AAC1C,uBAAiB,UAAU;AAAA,IACjC;AACI,WAAO;AAAA,EACX;AAAA,EACE,uBAAuB,SAAS;AAC9B,QAAI,SAAS,YAAY;AACvB,aAAO;AAAA,IACb;AACI,WAAO;AAAA,MACL,GAAG,KAAK,gBAAgB;AAAA,MACxB,GAAG,SAAS,eAAe,KAAK,oBAAoB,QAAQ,WAAW;AAAA,MACvE,GAAG;AAAA,MACH,YAAY;AAAA,IACb;AAAA,EACL;AAAA,EACE,QAAQ;AACN,SAAK,YAAY,MAAO;AACxB,SAAK,eAAe,MAAO;AAAA,EAC/B;AACA;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]}