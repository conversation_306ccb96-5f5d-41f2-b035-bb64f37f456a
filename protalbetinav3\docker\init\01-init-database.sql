-- =====================================================
-- 🚀 PORTAL BETINA V3 - INICIALIZAÇÃO COMPLETA DO BANCO
-- =====================================================
-- Este script cria todas as tabelas e dados necessários
-- para o funcionamento completo do sistema
-- =====================================================

-- Configurações iniciais
SET timezone = 'America/Sao_Paulo';
SET client_encoding = 'UTF8';

-- =====================================================
-- 📋 1. TABELAS PRINCIPAIS DO SISTEMA
-- =====================================================

-- Tabela de sessões de jogos
CREATE TABLE IF NOT EXISTS game_sessions (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) UNIQUE NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    game_id VARCHAR(255) NOT NULL,
    session_data JSONB NOT NULL DEFAULT '{}',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP,
    duration INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de métricas de jogos
CREATE TABLE IF NOT EXISTS game_metrics (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    game_id VARCHAR(255) NOT NULL,
    metrics_data JSONB NOT NULL DEFAULT '{}',
    analysis_data JSONB DEFAULT '{}',
    accuracy DECIMAL(5,2) DEFAULT 0,
    response_time INTEGER DEFAULT 0,
    engagement_score DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de análises terapêuticas
CREATE TABLE IF NOT EXISTS therapeutic_analysis (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    analysis_type VARCHAR(100) NOT NULL,
    analysis_data JSONB NOT NULL DEFAULT '{}',
    confidence_score DECIMAL(3,2) DEFAULT 0,
    recommendations JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de progresso do usuário
CREATE TABLE IF NOT EXISTS user_progress (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    game_id VARCHAR(255) NOT NULL,
    progress_data JSONB NOT NULL DEFAULT '{}',
    milestone_data JSONB DEFAULT '{}',
    last_session TIMESTAMP,
    total_sessions INTEGER DEFAULT 0,
    average_accuracy DECIMAL(5,2) DEFAULT 0,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de logs do sistema
CREATE TABLE IF NOT EXISTS system_logs (
    id SERIAL PRIMARY KEY,
    level VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    component VARCHAR(100),
    user_id VARCHAR(255),
    session_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de sessões de usuário
CREATE TABLE IF NOT EXISTS user_sessions (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 🔬 2. TABELAS MULTISSENSORIAIS
-- =====================================================

-- Tabela principal de dados multissensoriais
CREATE TABLE IF NOT EXISTS multisensory_data (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    game_id VARCHAR(255) NOT NULL,
    sensor_type VARCHAR(100) NOT NULL,
    sensor_data JSONB NOT NULL DEFAULT '{}',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    calibration_data JSONB DEFAULT '{}',
    processing_metadata JSONB DEFAULT '{}'
);

-- Tabela de calibração de sensores
CREATE TABLE IF NOT EXISTS sensor_calibration (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    device_id VARCHAR(255),
    sensor_type VARCHAR(100) NOT NULL,
    calibration_data JSONB NOT NULL DEFAULT '{}',
    accuracy_score DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de leituras de sensores
CREATE TABLE IF NOT EXISTS sensor_readings (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    sensor_type VARCHAR(50) NOT NULL,
    reading_data JSONB NOT NULL DEFAULT '{}',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de interações touch
CREATE TABLE IF NOT EXISTS touch_interactions (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    x_coordinate INTEGER,
    y_coordinate INTEGER,
    pressure DECIMAL(3,2),
    duration INTEGER,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de dados do acelerômetro
CREATE TABLE IF NOT EXISTS accelerometer_data (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    x_axis DECIMAL(10,6),
    y_axis DECIMAL(10,6),
    z_axis DECIMAL(10,6),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de dados do giroscópio
CREATE TABLE IF NOT EXISTS gyroscope_data (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    alpha DECIMAL(10,6),
    beta DECIMAL(10,6),
    gamma DECIMAL(10,6),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 🎮 3. TABELAS ESPECÍFICAS DOS JOGOS
-- =====================================================

-- Função para criar tabelas de jogos dinamicamente
DO $$
DECLARE
    game_table TEXT;
    game_tables TEXT[] := ARRAY[
        'colormatch_metrics',
        'contagemnumeros_metrics', 
        'imageassociation_metrics',
        'memorygame_metrics',
        'musicalsequence_metrics',
        'padroesvisuais_metrics',
        'quebracabeca_metrics',
        'creativepainting_metrics',
        'letterrecognition_metrics'
    ];
BEGIN
    FOREACH game_table IN ARRAY game_tables
    LOOP
        EXECUTE format('
            CREATE TABLE IF NOT EXISTS %I (
                id SERIAL PRIMARY KEY,
                session_id VARCHAR(255) NOT NULL DEFAULT ''default_session'',
                user_id VARCHAR(255) NOT NULL DEFAULT ''default_user'',
                game_specific_data JSONB DEFAULT ''{}''::jsonb,
                collectors_data JSONB DEFAULT ''{}''::jsonb,
                performance_metrics JSONB DEFAULT ''{}''::jsonb,
                therapeutic_indicators JSONB DEFAULT ''{}''::jsonb,
                multisensory_data JSONB DEFAULT ''{}''::jsonb,
                accuracy DECIMAL(5,2) DEFAULT 0,
                response_time INTEGER DEFAULT 0,
                engagement_score DECIMAL(5,2) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )', game_table);
        
        RAISE NOTICE 'Tabela % criada com sucesso', game_table;
    END LOOP;
END $$;

-- =====================================================
-- 📊 4. TABELAS AUXILIARES PARA SISTEMA
-- =====================================================

-- Tabela para análises específicas de jogos
CREATE TABLE IF NOT EXISTS game_specific_analysis (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    game_id VARCHAR(255) NOT NULL,
    analysis_data JSONB NOT NULL DEFAULT '{}',
    collectors_results JSONB DEFAULT '{}',
    therapeutic_insights JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de perfis cognitivos
CREATE TABLE IF NOT EXISTS cognitive_profiles (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    profile_data JSONB NOT NULL DEFAULT '{}',
    cognitive_strengths JSONB DEFAULT '{}',
    areas_for_improvement JSONB DEFAULT '{}',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de métricas gerais
CREATE TABLE IF NOT EXISTS metrics (
    id SERIAL PRIMARY KEY,
    metric_type VARCHAR(100) NOT NULL,
    metric_data JSONB NOT NULL DEFAULT '{}',
    user_id VARCHAR(255),
    session_id VARCHAR(255),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 🔍 5. ÍNDICES PARA PERFORMANCE
-- =====================================================

-- Índices para game_sessions
CREATE INDEX IF NOT EXISTS idx_game_sessions_user_id ON game_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_game_sessions_game_id ON game_sessions(game_id);
CREATE INDEX IF NOT EXISTS idx_game_sessions_started_at ON game_sessions(started_at);

-- Índices para game_metrics
CREATE INDEX IF NOT EXISTS idx_game_metrics_user_id ON game_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_game_metrics_game_id ON game_metrics(game_id);
CREATE INDEX IF NOT EXISTS idx_game_metrics_created_at ON game_metrics(created_at);
CREATE INDEX IF NOT EXISTS idx_game_metrics_accuracy ON game_metrics(accuracy);

-- Índices para multisensory_data
CREATE INDEX IF NOT EXISTS idx_multisensory_session ON multisensory_data(session_id);
CREATE INDEX IF NOT EXISTS idx_multisensory_sensor_type ON multisensory_data(sensor_type);
CREATE INDEX IF NOT EXISTS idx_multisensory_timestamp ON multisensory_data(timestamp);

-- Índices para therapeutic_analysis
CREATE INDEX IF NOT EXISTS idx_therapeutic_analysis_user_id ON therapeutic_analysis(user_id);
CREATE INDEX IF NOT EXISTS idx_therapeutic_analysis_type ON therapeutic_analysis(analysis_type);

-- Índices para user_progress
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_progress_unique ON user_progress(user_id, game_id);
CREATE INDEX IF NOT EXISTS idx_user_progress_updated_at ON user_progress(updated_at);

-- Índices para game_specific_analysis
CREATE INDEX IF NOT EXISTS idx_game_specific_analysis_session ON game_specific_analysis(session_id);
CREATE INDEX IF NOT EXISTS idx_game_specific_analysis_user ON game_specific_analysis(user_id);
CREATE INDEX IF NOT EXISTS idx_game_specific_analysis_game ON game_specific_analysis(game_id);

-- Índices para sensor_calibration
CREATE INDEX IF NOT EXISTS idx_sensor_calibration_user ON sensor_calibration(user_id);
CREATE INDEX IF NOT EXISTS idx_sensor_calibration_type ON sensor_calibration(sensor_type);
CREATE INDEX IF NOT EXISTS idx_sensor_calibration_updated ON sensor_calibration(updated_at);

-- Índices para system_logs
CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level);
CREATE INDEX IF NOT EXISTS idx_system_logs_component ON system_logs(component);
CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at);

-- Índices para tabelas de jogos específicos
DO $$
DECLARE
    game_table TEXT;
    game_tables TEXT[] := ARRAY[
        'colormatch_metrics',
        'contagemnumeros_metrics',
        'imageassociation_metrics',
        'memorygame_metrics',
        'musicalsequence_metrics',
        'padroesvisuais_metrics',
        'quebracabeca_metrics',
        'creativepainting_metrics',
        'letterrecognition_metrics'
    ];
BEGIN
    FOREACH game_table IN ARRAY game_tables
    LOOP
        EXECUTE format('CREATE INDEX IF NOT EXISTS idx_%I_user_id ON %I(user_id)', game_table, game_table);
        EXECUTE format('CREATE INDEX IF NOT EXISTS idx_%I_session_id ON %I(session_id)', game_table, game_table);
        EXECUTE format('CREATE INDEX IF NOT EXISTS idx_%I_accuracy ON %I(accuracy)', game_table, game_table);
        EXECUTE format('CREATE INDEX IF NOT EXISTS idx_%I_created_at ON %I(created_at)', game_table, game_table);

        RAISE NOTICE 'Índices criados para tabela %', game_table;
    END LOOP;
END $$;

-- =====================================================
-- 📈 6. VIEW PARA DASHBOARD
-- =====================================================

-- View consolidada para dashboard
CREATE OR REPLACE VIEW dashboard_metrics AS
SELECT 
    gs.game_id,
    gs.user_id,
    gs.session_id,
    gs.started_at,
    gs.duration,
    gm.accuracy,
    gm.response_time,
    gm.engagement_score,
    COUNT(md.id) as multisensory_readings,
    ta.analysis_type,
    ta.confidence_score,
    up.total_sessions,
    up.average_accuracy as user_avg_accuracy
FROM game_sessions gs
LEFT JOIN game_metrics gm ON gs.session_id = gm.session_id
LEFT JOIN multisensory_data md ON gs.session_id = md.session_id
LEFT JOIN therapeutic_analysis ta ON gs.session_id = ta.session_id
LEFT JOIN user_progress up ON gs.user_id = up.user_id AND gs.game_id = up.game_id;

-- =====================================================
-- 🎯 7. DADOS INICIAIS DE TESTE
-- =====================================================

-- Inserir dados de exemplo para demonstração - TODOS OS 9 JOGOS
INSERT INTO game_sessions (session_id, user_id, game_id, session_data, duration) VALUES
('demo_session_001', 'demo_user', 'ColorMatch', '{"demo": true, "level": 1, "selectedItems": [{"id": 1, "color": "red", "correct": true}], "difficulty": "easy"}', 180000),
('demo_session_002', 'demo_user', 'MemoryGame', '{"demo": true, "level": 2, "pairs_found": 6, "difficulty": "medium"}', 240000),
('demo_session_003', 'demo_user', 'QuebraCabeca', '{"demo": true, "level": 1, "pieces_placed": 12, "difficulty": "easy"}', 300000),
('demo_session_004', 'demo_user', 'ContagemNumeros', '{"demo": true, "level": 2, "numbers_counted": 15, "difficulty": "medium"}', 220000),
('demo_session_005', 'demo_user', 'ImageAssociation', '{"demo": true, "level": 1, "associations_made": 7, "difficulty": "easy"}', 280000),
('demo_session_006', 'demo_user', 'MusicalSequence', '{"demo": true, "level": 3, "sequences_completed": 4, "difficulty": "medium"}', 320000),
('demo_session_007', 'demo_user', 'PadroesVisuais', '{"demo": true, "level": 4, "patterns_identified": 8, "difficulty": "hard"}', 350000),
('demo_session_008', 'demo_user', 'CreativePainting', '{"demo": true, "level": 1, "strokes_made": 45, "creativity_score": 85}', 450000),
('demo_session_009', 'demo_user', 'LetterRecognition', '{"demo": true, "level": 2, "letters_recognized": 18, "difficulty": "easy"}', 150000),
-- Sessões adicionais para demonstrar progresso
('demo_session_010', 'demo_user_2', 'ColorMatch', '{"demo": true, "level": 3, "selectedItems": [{"id": 2, "color": "blue", "correct": false}], "difficulty": "hard"}', 200000),
('demo_session_011', 'demo_user_2', 'MemoryGame', '{"demo": true, "level": 4, "pairs_found": 10, "difficulty": "hard"}', 280000),
('demo_session_012', 'admin_user', 'ColorMatch', '{"admin_test": true, "level": 1, "selectedItems": [{"id": 3, "color": "green", "correct": true}], "difficulty": "easy"}', 120000)
ON CONFLICT (session_id) DO NOTHING;

-- Inserir métricas de exemplo para TODOS OS JOGOS
INSERT INTO game_metrics (session_id, user_id, game_id, metrics_data, accuracy, response_time, engagement_score) VALUES
('demo_session_001', 'demo_user', 'ColorMatch', '{"colors_matched": 8, "total_colors": 10, "color_confusions": 2}', 80.0, 1500, 85.5),
('demo_session_002', 'demo_user', 'MemoryGame', '{"pairs_found": 6, "total_pairs": 8, "memory_strategy": "visual"}', 75.0, 2200, 78.3),
('demo_session_003', 'demo_user', 'QuebraCabeca', '{"pieces_placed": 12, "total_pieces": 16, "assembly_strategy": "edge_first"}', 75.0, 3000, 82.1),
('demo_session_004', 'demo_user', 'ContagemNumeros', '{"numbers_counted": 15, "total_numbers": 20, "counting_strategy": "sequential"}', 75.0, 1800, 80.2),
('demo_session_005', 'demo_user', 'ImageAssociation', '{"associations_made": 7, "total_associations": 10, "association_type": "semantic"}', 70.0, 2500, 75.8),
('demo_session_006', 'demo_user', 'MusicalSequence', '{"sequences_completed": 4, "total_sequences": 6, "rhythm_accuracy": 0.8}', 66.7, 3200, 72.5),
('demo_session_007', 'demo_user', 'PadroesVisuais', '{"patterns_identified": 8, "total_patterns": 12, "pattern_complexity": "high"}', 66.7, 2800, 78.9),
('demo_session_008', 'demo_user', 'CreativePainting', '{"strokes_made": 45, "colors_used": 8, "creativity_score": 85, "artistic_elements": 12}', 85.0, 4500, 92.3),
('demo_session_009', 'demo_user', 'LetterRecognition', '{"letters_recognized": 18, "total_letters": 24, "recognition_method": "phonetic"}', 75.0, 1200, 83.7),
-- Métricas adicionais para outros usuários
('demo_session_010', 'demo_user_2', 'ColorMatch', '{"colors_matched": 6, "total_colors": 10, "color_confusions": 4}', 60.0, 2200, 70.2),
('demo_session_011', 'demo_user_2', 'MemoryGame', '{"pairs_found": 10, "total_pairs": 12, "memory_strategy": "auditory"}', 83.3, 1800, 88.5),
('demo_session_012', 'admin_user', 'ColorMatch', '{"colors_matched": 9, "total_colors": 10, "color_confusions": 1}', 90.0, 1200, 95.0);

-- Inserir dados multissensoriais de exemplo - EXPANDIDO
INSERT INTO multisensory_data (session_id, user_id, game_id, sensor_type, sensor_data, calibration_data, processing_metadata) VALUES
('demo_session_001', 'demo_user', 'ColorMatch', 'touch', '{"x": 150, "y": 200, "pressure": 0.7, "duration": 250}', '{"sensitivity": 0.8, "baseline": 0.1}', '{"processed_at": "2024-01-01T10:00:00Z", "quality": "high"}'),
('demo_session_001', 'demo_user', 'ColorMatch', 'accelerometer', '{"x": 0.1, "y": 0.2, "z": 9.8, "magnitude": 9.82}', '{"calibrated": true, "offset": {"x": 0.01, "y": 0.02, "z": 0.1}}', '{"processed_at": "2024-01-01T10:00:01Z", "quality": "high"}'),
('demo_session_001', 'demo_user', 'ColorMatch', 'gyroscope', '{"alpha": 5.2, "beta": 2.1, "gamma": 1.8}', '{"calibrated": true, "drift_correction": true}', '{"processed_at": "2024-01-01T10:00:02Z", "quality": "medium"}'),
('demo_session_002', 'demo_user', 'MemoryGame', 'touch', '{"x": 300, "y": 400, "pressure": 0.8, "duration": 180}', '{"sensitivity": 0.9, "baseline": 0.05}', '{"processed_at": "2024-01-01T11:00:00Z", "quality": "high"}'),
('demo_session_002', 'demo_user', 'MemoryGame', 'accelerometer', '{"x": -0.05, "y": 0.15, "z": 9.85, "magnitude": 9.86}', '{"calibrated": true, "offset": {"x": -0.01, "y": 0.01, "z": 0.05}}', '{"processed_at": "2024-01-01T11:00:01Z", "quality": "high"}'),
('demo_session_003', 'demo_user', 'QuebraCabeca', 'gyroscope', '{"alpha": 10.5, "beta": 5.2, "gamma": 2.1}', '{"calibrated": true, "drift_correction": true}', '{"processed_at": "2024-01-01T12:00:00Z", "quality": "high"}'),
('demo_session_003', 'demo_user', 'QuebraCabeca', 'touch', '{"x": 250, "y": 350, "pressure": 0.6, "duration": 320}', '{"sensitivity": 0.7, "baseline": 0.15}', '{"processed_at": "2024-01-01T12:00:01Z", "quality": "medium"}'),
('demo_session_004', 'demo_user', 'ContagemNumeros', 'touch', '{"x": 180, "y": 220, "pressure": 0.9, "duration": 150}', '{"sensitivity": 0.85, "baseline": 0.08}', '{"processed_at": "2024-01-01T13:00:00Z", "quality": "high"}'),
('demo_session_005', 'demo_user', 'ImageAssociation', 'accelerometer', '{"x": 0.08, "y": -0.12, "z": 9.78, "magnitude": 9.79}', '{"calibrated": true, "offset": {"x": 0.005, "y": -0.01, "z": 0.02}}', '{"processed_at": "2024-01-01T14:00:00Z", "quality": "high"}'),
('demo_session_006', 'demo_user', 'MusicalSequence', 'gyroscope', '{"alpha": 8.3, "beta": 3.7, "gamma": 1.2}', '{"calibrated": true, "drift_correction": false}', '{"processed_at": "2024-01-01T15:00:00Z", "quality": "medium"}'),
('demo_session_007', 'demo_user', 'PadroesVisuais', 'touch', '{"x": 320, "y": 180, "pressure": 0.75, "duration": 280}', '{"sensitivity": 0.8, "baseline": 0.12}', '{"processed_at": "2024-01-01T16:00:00Z", "quality": "high"}'),
('demo_session_008', 'demo_user', 'CreativePainting', 'touch', '{"x": 400, "y": 300, "pressure": 0.95, "duration": 500}', '{"sensitivity": 0.9, "baseline": 0.05}', '{"processed_at": "2024-01-01T17:00:00Z", "quality": "high"}'),
('demo_session_009', 'demo_user', 'LetterRecognition', 'accelerometer', '{"x": -0.02, "y": 0.18, "z": 9.82, "magnitude": 9.84}', '{"calibrated": true, "offset": {"x": -0.005, "y": 0.015, "z": 0.08}}', '{"processed_at": "2024-01-01T18:00:00Z", "quality": "high"}');

-- Inserir dados de calibração de sensores
INSERT INTO sensor_calibration (user_id, device_id, sensor_type, calibration_data, accuracy_score) VALUES
('demo_user', 'device_001', 'touch', '{"sensitivity": 0.8, "baseline": 0.1, "max_pressure": 1.0, "calibration_points": 25}', 92.5),
('demo_user', 'device_001', 'accelerometer', '{"offset": {"x": 0.01, "y": 0.02, "z": 0.1}, "scale": {"x": 1.0, "y": 1.0, "z": 1.0}, "noise_threshold": 0.05}', 95.8),
('demo_user', 'device_001', 'gyroscope', '{"drift_rate": 0.02, "bias": {"alpha": 0.1, "beta": 0.05, "gamma": 0.08}, "temperature_compensation": true}', 88.3),
('demo_user_2', 'device_002', 'touch', '{"sensitivity": 0.9, "baseline": 0.05, "max_pressure": 1.0, "calibration_points": 30}', 96.2),
('demo_user_2', 'device_002', 'accelerometer', '{"offset": {"x": -0.01, "y": 0.01, "z": 0.05}, "scale": {"x": 1.0, "y": 1.0, "z": 1.0}, "noise_threshold": 0.03}', 97.1),
('admin_user', 'device_admin', 'touch', '{"sensitivity": 0.85, "baseline": 0.08, "max_pressure": 1.0, "calibration_points": 20}', 90.0);

-- Inserir análises terapêuticas de exemplo
INSERT INTO therapeutic_analysis (session_id, user_id, analysis_type, analysis_data, confidence_score) VALUES
('demo_session_001', 'demo_user', 'cognitive', '{"attention": 0.8, "memory": 0.7, "processing_speed": 0.75}', 0.85),
('demo_session_002', 'demo_user', 'behavioral', '{"engagement": 0.78, "persistence": 0.82, "frustration_tolerance": 0.65}', 0.75),
('demo_session_003', 'demo_user', 'motor', '{"fine_motor": 0.82, "coordination": 0.78, "spatial_awareness": 0.85}', 0.82);

-- Inserir progresso do usuário
INSERT INTO user_progress (user_id, game_id, progress_data, total_sessions, average_accuracy) VALUES
('demo_user', 'ColorMatch', '{"level": 2, "achievements": ["first_game", "color_master"]}', 5, 78.5),
('demo_user', 'MemoryGame', '{"level": 3, "achievements": ["memory_champion"]}', 8, 82.3),
('demo_user', 'QuebraCabeca', '{"level": 1, "achievements": ["puzzle_solver"]}', 3, 75.8)
ON CONFLICT (user_id, game_id) DO UPDATE SET
    total_sessions = EXCLUDED.total_sessions,
    average_accuracy = EXCLUDED.average_accuracy,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 🎮 9. DADOS ESPECÍFICOS DOS JOGOS
-- =====================================================

-- Inserir dados específicos para ColorMatch
INSERT INTO colormatch_metrics (session_id, user_id, game_specific_data, accuracy, response_time, engagement_score) VALUES
('demo_session_001', 'demo_user', '{"selectedItems": [{"id": 1, "color": "red", "correct": true}], "targetColors": ["red", "blue"], "difficulty": "easy"}', 80.0, 1500, 85.5);

-- Inserir dados específicos para MemoryGame
INSERT INTO memorygame_metrics (session_id, user_id, game_specific_data, accuracy, response_time, engagement_score) VALUES
('demo_session_002', 'demo_user', '{"pairs_found": 6, "total_pairs": 8, "difficulty": "medium", "memory_strategy": "visual"}', 75.0, 2200, 78.3);

-- Inserir dados específicos para QuebraCabeca
INSERT INTO quebracabeca_metrics (session_id, user_id, game_specific_data, accuracy, response_time, engagement_score) VALUES
('demo_session_003', 'demo_user', '{"pieces_placed": 12, "total_pieces": 16, "difficulty": "easy", "assembly_strategy": "edge_first"}', 75.0, 3000, 82.1);

-- Inserir dados para outros jogos
INSERT INTO contagemnumeros_metrics (session_id, user_id, game_specific_data, accuracy, response_time, engagement_score) VALUES
('demo_session_004', 'demo_user', '{"numbers_counted": 15, "total_numbers": 20, "difficulty": "medium"}', 75.0, 1800, 80.2);

INSERT INTO imageassociation_metrics (session_id, user_id, game_specific_data, accuracy, response_time, engagement_score) VALUES
('demo_session_005', 'demo_user', '{"associations_made": 7, "total_associations": 10, "difficulty": "easy"}', 70.0, 2500, 75.8);

INSERT INTO musicalsequence_metrics (session_id, user_id, game_specific_data, accuracy, response_time, engagement_score) VALUES
('demo_session_006', 'demo_user', '{"sequences_completed": 4, "total_sequences": 6, "difficulty": "medium"}', 66.7, 3200, 72.5);

INSERT INTO padroesvisuais_metrics (session_id, user_id, game_specific_data, accuracy, response_time, engagement_score) VALUES
('demo_session_007', 'demo_user', '{"patterns_identified": 8, "total_patterns": 12, "difficulty": "hard"}', 66.7, 2800, 78.9);

INSERT INTO creativepainting_metrics (session_id, user_id, game_specific_data, accuracy, response_time, engagement_score) VALUES
('demo_session_008', 'demo_user', '{"strokes_made": 45, "colors_used": 8, "creativity_score": 85}', 85.0, 4500, 92.3);

INSERT INTO letterrecognition_metrics (session_id, user_id, game_specific_data, accuracy, response_time, engagement_score) VALUES
('demo_session_009', 'demo_user', '{"letters_recognized": 18, "total_letters": 24, "difficulty": "easy"}', 75.0, 1200, 83.7);

-- =====================================================
-- 🔧 10. FUNÇÕES DE VALIDAÇÃO E TRIGGERS
-- =====================================================

-- Função para validar dados de sessão
CREATE OR REPLACE FUNCTION validate_session_data()
RETURNS TRIGGER AS $$
BEGIN
    -- Validar se session_id não está vazio
    IF NEW.session_id IS NULL OR NEW.session_id = '' THEN
        RAISE EXCEPTION 'session_id não pode estar vazio';
    END IF;

    -- Validar se user_id não está vazio
    IF NEW.user_id IS NULL OR NEW.user_id = '' THEN
        RAISE EXCEPTION 'user_id não pode estar vazio';
    END IF;

    -- Validar accuracy entre 0 e 100
    IF NEW.accuracy IS NOT NULL AND (NEW.accuracy < 0 OR NEW.accuracy > 100) THEN
        RAISE EXCEPTION 'accuracy deve estar entre 0 e 100, valor recebido: %', NEW.accuracy;
    END IF;

    -- Validar response_time positivo
    IF NEW.response_time IS NOT NULL AND NEW.response_time < 0 THEN
        RAISE EXCEPTION 'response_time deve ser positivo, valor recebido: %', NEW.response_time;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Aplicar trigger de validação nas tabelas principais
CREATE TRIGGER validate_game_metrics_trigger
    BEFORE INSERT OR UPDATE ON game_metrics
    FOR EACH ROW EXECUTE FUNCTION validate_session_data();

-- Função para atualizar user_progress automaticamente
CREATE OR REPLACE FUNCTION update_user_progress()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO user_progress (user_id, game_id, progress_data, total_sessions, average_accuracy)
    VALUES (
        NEW.user_id,
        NEW.game_id,
        jsonb_build_object('last_session', NEW.created_at, 'last_accuracy', NEW.accuracy),
        1,
        NEW.accuracy
    )
    ON CONFLICT (user_id, game_id) DO UPDATE SET
        total_sessions = user_progress.total_sessions + 1,
        average_accuracy = (user_progress.average_accuracy * user_progress.total_sessions + NEW.accuracy) / (user_progress.total_sessions + 1),
        progress_data = user_progress.progress_data || jsonb_build_object('last_session', NEW.created_at, 'last_accuracy', NEW.accuracy),
        updated_at = CURRENT_TIMESTAMP;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Aplicar trigger de atualização de progresso
CREATE TRIGGER update_progress_trigger
    AFTER INSERT ON game_metrics
    FOR EACH ROW EXECUTE FUNCTION update_user_progress();

-- Função para log de auditoria
CREATE OR REPLACE FUNCTION audit_log()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO system_logs (level, message, metadata, component, user_id, session_id)
    VALUES (
        'INFO',
        format('Operação %s na tabela %s', TG_OP, TG_TABLE_NAME),
        jsonb_build_object(
            'operation', TG_OP,
            'table', TG_TABLE_NAME,
            'old_data', CASE WHEN TG_OP = 'DELETE' THEN row_to_json(OLD) ELSE NULL END,
            'new_data', CASE WHEN TG_OP != 'DELETE' THEN row_to_json(NEW) ELSE NULL END
        ),
        'audit_system',
        CASE WHEN TG_OP != 'DELETE' THEN NEW.user_id ELSE OLD.user_id END,
        CASE WHEN TG_OP != 'DELETE' THEN NEW.session_id ELSE OLD.session_id END
    );

    RETURN CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;
END;
$$ LANGUAGE plpgsql;

-- Aplicar triggers de auditoria nas tabelas críticas
CREATE TRIGGER audit_game_sessions_trigger
    AFTER INSERT OR UPDATE OR DELETE ON game_sessions
    FOR EACH ROW EXECUTE FUNCTION audit_log();

CREATE TRIGGER audit_game_metrics_trigger
    AFTER INSERT OR UPDATE OR DELETE ON game_metrics
    FOR EACH ROW EXECUTE FUNCTION audit_log();

CREATE TRIGGER audit_multisensory_data_trigger
    AFTER INSERT OR UPDATE OR DELETE ON multisensory_data
    FOR EACH ROW EXECUTE FUNCTION audit_log();

-- =====================================================
-- 📊 11. FUNÇÕES DE MONITORAMENTO E ESTATÍSTICAS
-- =====================================================

-- Função para obter estatísticas do sistema
CREATE OR REPLACE FUNCTION get_system_stats()
RETURNS TABLE (
    metric_name TEXT,
    metric_value BIGINT,
    description TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 'total_sessions'::TEXT, COUNT(*)::BIGINT, 'Total de sessões de jogos'::TEXT FROM game_sessions
    UNION ALL
    SELECT 'total_users'::TEXT, COUNT(DISTINCT user_id)::BIGINT, 'Total de usuários únicos'::TEXT FROM game_sessions
    UNION ALL
    SELECT 'total_games'::TEXT, COUNT(DISTINCT game_id)::BIGINT, 'Total de jogos diferentes'::TEXT FROM game_sessions
    UNION ALL
    SELECT 'multisensory_readings'::TEXT, COUNT(*)::BIGINT, 'Total de leituras multissensoriais'::TEXT FROM multisensory_data
    UNION ALL
    SELECT 'therapeutic_analyses'::TEXT, COUNT(*)::BIGINT, 'Total de análises terapêuticas'::TEXT FROM therapeutic_analysis
    UNION ALL
    SELECT 'calibrated_sensors'::TEXT, COUNT(*)::BIGINT, 'Total de sensores calibrados'::TEXT FROM sensor_calibration
    UNION ALL
    SELECT 'system_logs'::TEXT, COUNT(*)::BIGINT, 'Total de logs do sistema'::TEXT FROM system_logs;
END;
$$ LANGUAGE plpgsql;

-- Função para obter métricas de performance por jogo
CREATE OR REPLACE FUNCTION get_game_performance(game_name TEXT DEFAULT NULL)
RETURNS TABLE (
    game_id TEXT,
    total_sessions BIGINT,
    avg_accuracy NUMERIC,
    avg_response_time NUMERIC,
    avg_engagement NUMERIC,
    total_users BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        gm.game_id::TEXT,
        COUNT(*)::BIGINT as total_sessions,
        ROUND(AVG(gm.accuracy), 2) as avg_accuracy,
        ROUND(AVG(gm.response_time), 0) as avg_response_time,
        ROUND(AVG(gm.engagement_score), 2) as avg_engagement,
        COUNT(DISTINCT gm.user_id)::BIGINT as total_users
    FROM game_metrics gm
    WHERE game_name IS NULL OR gm.game_id = game_name
    GROUP BY gm.game_id
    ORDER BY total_sessions DESC;
END;
$$ LANGUAGE plpgsql;

-- Função para obter status de saúde do banco
CREATE OR REPLACE FUNCTION get_database_health()
RETURNS TABLE (
    component TEXT,
    status TEXT,
    details JSONB
) AS $$
DECLARE
    table_count INTEGER;
    index_count INTEGER;
    recent_sessions INTEGER;
    error_logs INTEGER;
BEGIN
    -- Contar tabelas
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables
    WHERE table_schema = 'public';

    -- Contar índices
    SELECT COUNT(*) INTO index_count
    FROM pg_indexes
    WHERE schemaname = 'public';

    -- Contar sessões recentes (últimas 24h)
    SELECT COUNT(*) INTO recent_sessions
    FROM game_sessions
    WHERE created_at > NOW() - INTERVAL '24 hours';

    -- Contar logs de erro recentes
    SELECT COUNT(*) INTO error_logs
    FROM system_logs
    WHERE level = 'ERROR' AND created_at > NOW() - INTERVAL '1 hour';

    RETURN QUERY
    SELECT 'database'::TEXT, 'healthy'::TEXT, jsonb_build_object('tables', table_count, 'indexes', index_count)
    UNION ALL
    SELECT 'sessions'::TEXT,
           CASE WHEN recent_sessions > 0 THEN 'active' ELSE 'inactive' END::TEXT,
           jsonb_build_object('recent_sessions', recent_sessions)
    UNION ALL
    SELECT 'errors'::TEXT,
           CASE WHEN error_logs = 0 THEN 'clean' ELSE 'warnings' END::TEXT,
           jsonb_build_object('error_count', error_logs);
END;
$$ LANGUAGE plpgsql;

-- Função para limpeza de dados antigos
CREATE OR REPLACE FUNCTION cleanup_old_data(days_to_keep INTEGER DEFAULT 30)
RETURNS TABLE (
    table_name TEXT,
    deleted_rows INTEGER
) AS $$
DECLARE
    cutoff_date TIMESTAMP;
    deleted_count INTEGER;
BEGIN
    cutoff_date := NOW() - (days_to_keep || ' days')::INTERVAL;

    -- Limpar logs antigos
    DELETE FROM system_logs WHERE created_at < cutoff_date;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN QUERY SELECT 'system_logs'::TEXT, deleted_count;

    -- Limpar dados multissensoriais antigos
    DELETE FROM multisensory_data WHERE timestamp < cutoff_date;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN QUERY SELECT 'multisensory_data'::TEXT, deleted_count;

    -- Limpar sessões antigas (manter métricas)
    DELETE FROM game_sessions WHERE created_at < cutoff_date;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN QUERY SELECT 'game_sessions'::TEXT, deleted_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- ✅ 12. VERIFICAÇÃO FINAL E TESTES
-- =====================================================

-- Função para verificar se tudo foi criado corretamente
DO $$
DECLARE
    table_count INTEGER;
    data_count INTEGER;
    function_count INTEGER;
    trigger_count INTEGER;
    index_count INTEGER;
BEGIN
    -- Contar tabelas criadas
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables
    WHERE table_schema = 'public';

    -- Contar funções criadas
    SELECT COUNT(*) INTO function_count
    FROM information_schema.routines
    WHERE routine_schema = 'public' AND routine_type = 'FUNCTION';

    -- Contar triggers criados
    SELECT COUNT(*) INTO trigger_count
    FROM information_schema.triggers
    WHERE trigger_schema = 'public';

    -- Contar índices criados
    SELECT COUNT(*) INTO index_count
    FROM pg_indexes
    WHERE schemaname = 'public';

    -- Contar dados inseridos
    SELECT COUNT(*) INTO data_count FROM game_sessions;

    RAISE NOTICE '✅ Inicialização concluída:';
    RAISE NOTICE '   📋 Tabelas: %', table_count;
    RAISE NOTICE '   🔧 Funções: %', function_count;
    RAISE NOTICE '   ⚡ Triggers: %', trigger_count;
    RAISE NOTICE '   🔍 Índices: %', index_count;
    RAISE NOTICE '   📊 Sessões de exemplo: %', data_count;
END $$;

-- Testar funções criadas
DO $$
DECLARE
    stats_result RECORD;
    health_result RECORD;
BEGIN
    RAISE NOTICE '🧪 Testando funções do sistema...';

    -- Testar função de estatísticas
    FOR stats_result IN SELECT * FROM get_system_stats() LIMIT 3
    LOOP
        RAISE NOTICE '   📊 %: % (%)', stats_result.metric_name, stats_result.metric_value, stats_result.description;
    END LOOP;

    -- Testar função de saúde
    FOR health_result IN SELECT * FROM get_database_health()
    LOOP
        RAISE NOTICE '   🏥 %: % - %', health_result.component, health_result.status, health_result.details;
    END LOOP;

    RAISE NOTICE '✅ Todas as funções testadas com sucesso!';
END $$;

-- Mensagem final com estatísticas completas
SELECT
    '🎉 PORTAL BETINA V3 - BANCO INICIALIZADO COM SUCESSO!' as status,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public') as total_tables,
    (SELECT COUNT(*) FROM information_schema.routines WHERE routine_schema = 'public') as total_functions,
    (SELECT COUNT(*) FROM pg_indexes WHERE schemaname = 'public') as total_indexes,
    (SELECT COUNT(*) FROM game_sessions) as demo_sessions,
    (SELECT COUNT(*) FROM multisensory_data) as multisensory_readings,
    (SELECT COUNT(DISTINCT game_id) FROM game_metrics) as games_with_data;

-- Exibir estatísticas do sistema
SELECT '📊 ESTATÍSTICAS DO SISTEMA:' as info;
SELECT * FROM get_system_stats();

-- Exibir performance dos jogos
SELECT '🎮 PERFORMANCE DOS JOGOS:' as info;
SELECT * FROM get_game_performance();

-- Exibir saúde do banco
SELECT '🏥 SAÚDE DO BANCO:' as info;
SELECT * FROM get_database_health();

-- Mensagem de conclusão
SELECT
    '✅ SISTEMA PRONTO PARA PRODUÇÃO!' as final_status,
    'Todas as tabelas, índices, funções e dados de exemplo foram criados com sucesso.' as details,
    'O sistema está configurado para suportar todos os 9 jogos com coleta multissensorial completa.' as capabilities;
