#!/usr/bin/env node

/**
 * Script para encontrar o erro "Cannot access 'e' before initialization"
 * Procura por problemas específicos de inicialização de variáveis
 */

import fs from 'fs';
import path from 'path';

// Diretórios para verificar
const dirsToCheck = [
  './src/games',
  './src/utils',
  './src/components',
  './src/hooks'
];

// Padrões problemáticos
const problematicPatterns = [
  // Variáveis sendo usadas antes da declaração
  {
    name: 'Variable used before declaration',
    regex: /(\w+)\s*=.*\1/g,
    description: 'Variável sendo usada na própria inicialização'
  },
  // Dependências circulares em imports
  {
    name: 'Circular import pattern',
    regex: /import.*from\s+['"]\.\/.*\1/g,
    description: 'Possível importação circular'
  },
  // Variáveis 'e' específicas
  {
    name: 'Variable e usage',
    regex: /\be\b/g,
    description: 'Uso da variável "e"'
  },
  // Problemas com const/let/var
  {
    name: 'Const reassignment',
    regex: /const\s+(\w+).*\1\s*=/g,
    description: 'Tentativa de reatribuição de const'
  },
  // Problemas com destructuring
  {
    name: 'Destructuring issues',
    regex: /const\s*\{[^}]*\}\s*=\s*\{[^}]*\}/g,
    description: 'Possível problema com destructuring'
  }
];

// Função para encontrar arquivos JS/JSX recursivamente
function findJsFiles(dir) {
  let results = [];
  
  if (!fs.existsSync(dir)) {
    console.log(`⚠️ Diretório não existe: ${dir}`);
    return results;
  }
  
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      results = results.concat(findJsFiles(fullPath));
    } else if (file.endsWith('.js') || file.endsWith('.jsx')) {
      results.push(fullPath);
    }
  }
  
  return results;
}

// Função para analisar um arquivo
function analyzeFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const issues = [];
    
    // Verificar cada padrão problemático
    problematicPatterns.forEach(pattern => {
      const matches = [...content.matchAll(pattern.regex)];
      
      matches.forEach(match => {
        const lineNumber = content.substring(0, match.index).split('\n').length;
        const lineContent = lines[lineNumber - 1]?.trim();
        
        issues.push({
          pattern: pattern.name,
          description: pattern.description,
          line: lineNumber,
          content: lineContent,
          match: match[0]
        });
      });
    });
    
    // Verificações específicas para o erro "Cannot access 'e' before initialization"
    
    // 1. Procurar por variáveis 'e' sendo declaradas
    const eDeclarations = content.match(/(?:const|let|var)\s+e\s*=/g);
    if (eDeclarations) {
      eDeclarations.forEach(decl => {
        const lineNumber = content.substring(0, content.indexOf(decl)).split('\n').length;
        issues.push({
          pattern: 'Variable e declaration',
          description: 'Declaração da variável "e"',
          line: lineNumber,
          content: lines[lineNumber - 1]?.trim(),
          match: decl
        });
      });
    }
    
    // 2. Procurar por uso de 'e' antes de declaração
    const eUsages = [...content.matchAll(/\be\b/g)];
    eUsages.forEach(match => {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      const lineContent = lines[lineNumber - 1]?.trim();
      
      // Verificar se não é uma declaração
      if (!lineContent.includes('const e') && !lineContent.includes('let e') && !lineContent.includes('var e')) {
        issues.push({
          pattern: 'Variable e usage',
          description: 'Uso da variável "e" (possível uso antes da declaração)',
          line: lineNumber,
          content: lineContent,
          match: match[0]
        });
      }
    });
    
    // 3. Verificar problemas específicos de hoisting
    const hoistingProblems = content.match(/(\w+)\s*=.*\1/g);
    if (hoistingProblems) {
      hoistingProblems.forEach(problem => {
        const lineNumber = content.substring(0, content.indexOf(problem)).split('\n').length;
        issues.push({
          pattern: 'Hoisting problem',
          description: 'Possível problema de hoisting',
          line: lineNumber,
          content: lines[lineNumber - 1]?.trim(),
          match: problem
        });
      });
    }
    
    return {
      file: filePath,
      issues: issues,
      hasProblems: issues.length > 0
    };
    
  } catch (error) {
    return {
      file: filePath,
      error: error.message,
      hasProblems: true
    };
  }
}

// Função principal
async function main() {
  console.log('🔍 Procurando por erro "Cannot access \'e\' before initialization"...\n');
  
  let allFiles = [];
  for (const dir of dirsToCheck) {
    const files = findJsFiles(dir);
    allFiles = allFiles.concat(files);
  }
  
  console.log(`📄 Analisando ${allFiles.length} arquivos...\n`);
  
  const results = [];
  const problematicFiles = [];
  
  for (const file of allFiles) {
    const result = analyzeFile(file);
    results.push(result);
    
    if (result.hasProblems) {
      problematicFiles.push(result);
    }
  }
  
  // Relatório
  console.log('📊 RELATÓRIO DE ANÁLISE:\n');
  
  if (problematicFiles.length === 0) {
    console.log('✅ Nenhum problema encontrado!');
  } else {
    console.log(`❌ Encontrados ${problematicFiles.length} arquivos com possíveis problemas:\n`);
    
    problematicFiles.forEach(file => {
      console.log(`📄 ${file.file}:`);
      
      if (file.error) {
        console.log(`   ❌ Erro: ${file.error}`);
      } else {
        file.issues.forEach(issue => {
          console.log(`   🔸 Linha ${issue.line}: ${issue.pattern}`);
          console.log(`      ${issue.description}`);
          console.log(`      Código: ${issue.content}`);
          console.log(`      Match: ${issue.match}`);
          console.log('');
        });
      }
      console.log('');
    });
  }
  
  // Estatísticas
  console.log('📈 ESTATÍSTICAS:');
  console.log(`   Total de arquivos: ${allFiles.length}`);
  console.log(`   Arquivos com problemas: ${problematicFiles.length}`);
  console.log(`   Taxa de problemas: ${((problematicFiles.length / allFiles.length) * 100).toFixed(1)}%`);
}

main().catch(console.error);
