{"version": 3, "file": "AccessibilityPage-DjwRqkIe.js", "sources": ["../../src/components/pages/AccessibilityPage/AccessibilityPage.jsx"], "sourcesContent": ["/**\r\n * @file AccessibilityPage.jsx\r\n * @description Página dedicada às configurações de acessibilidade\r\n * @version 3.0.0\r\n */\r\n\r\nimport React, { useState, useCallback, useEffect } from 'react'\r\nimport { useAccessibilityContext } from '../../context/AccessibilityContext'\r\nimport styles from './AccessibilityPage.module.css'\r\n\r\nfunction AccessibilityPage({ onBack }) {\r\n  const { settings, updateSettings, applyPreset } = useAccessibilityContext()\r\n  const [savedSuccessfully, setSavedSuccessfully] = useState(false)\r\n\r\n  // Aplicar configurações ao documento\r\n  useEffect(() => {\r\n    const applyAccessibilitySettings = () => {\r\n      const root = document.documentElement\r\n      const body = document.body\r\n\r\n      // Alto Contraste\r\n      if (settings.highContrast) {\r\n        root.classList.add('high-contrast')\r\n      } else {\r\n        root.classList.remove('high-contrast')\r\n      }\r\n\r\n      // Tam<PERSON><PERSON> da Fonte\r\n      root.classList.remove('font-small', 'font-medium', 'font-large', 'font-extra-large')\r\n      root.classList.add(`font-${settings.fontSize}`)\r\n\r\n      // Fonte para Dislexia\r\n      if (settings.dyslexiaFriendly) {\r\n        root.classList.add('dyslexia-friendly')\r\n      } else {\r\n        root.classList.remove('dyslexia-friendly')\r\n      }\r\n\r\n      // Reduzir Animações\r\n      if (settings.reducedMotion) {\r\n        root.classList.add('reduced-motion')\r\n      } else {\r\n        root.classList.remove('reduced-motion')\r\n      }\r\n\r\n      // Esquema de Cores\r\n      root.classList.remove('scheme-default', 'scheme-dark', 'scheme-soft', 'scheme-high-contrast')\r\n      root.classList.add(`scheme-${settings.colorScheme}`)\r\n\r\n      // Aplicar estilos CSS personalizados\r\n      const customStyles = `\r\n        .high-contrast * {\r\n          filter: contrast(1.5) !important;\r\n          border: 1px solid #000 !important;\r\n        }\r\n        \r\n        .font-small { font-size: 12px !important; }\r\n        .font-medium { font-size: 16px !important; }\r\n        .font-large { font-size: 20px !important; }\r\n        .font-extra-large { font-size: 24px !important; }\r\n        \r\n        .dyslexia-friendly * {\r\n          font-family: 'OpenDyslexic', 'Comic Sans MS', cursive !important;\r\n        }\r\n        \r\n        .reduced-motion * {\r\n          animation: none !important;\r\n          transition: none !important;\r\n        }\r\n        \r\n        .scheme-dark {\r\n          background: #1a1a1a !important;\r\n          color: #ffffff !important;\r\n        }\r\n        \r\n        .scheme-soft {\r\n          filter: brightness(0.9) saturate(0.8) !important;\r\n        }\r\n        \r\n        .scheme-high-contrast {\r\n          filter: contrast(2) brightness(1.2) !important;\r\n        }\r\n      `\r\n\r\n      // Remover estilo anterior se existir\r\n      const existingStyle = document.getElementById('accessibility-styles')\r\n      if (existingStyle) {\r\n        existingStyle.remove()\r\n      }\r\n\r\n      // Adicionar novo estilo\r\n      const styleElement = document.createElement('style')\r\n      styleElement.id = 'accessibility-styles'\r\n      styleElement.textContent = customStyles\r\n      document.head.appendChild(styleElement)\r\n    }\r\n\r\n    applyAccessibilitySettings()\r\n  }, [settings])\r\n\r\n  const [textToSpeechEnabled, setTextToSpeechEnabled] = useState(settings.textToSpeech)\r\n\r\n  // Exibir mensagem de sucesso temporária após salvar\r\n  const showSuccessMessage = () => {\r\n    setSavedSuccessfully(true)\r\n    setTimeout(() => setSavedSuccessfully(false), 2000)\r\n  }\r\n\r\n  // Text-to-Speech\r\n  const speakText = (text) => {\r\n    if (settings.textToSpeech && 'speechSynthesis' in window) {\r\n      const utterance = new SpeechSynthesisUtterance(text)\r\n      utterance.rate = 0.8\r\n      utterance.pitch = 1\r\n      speechSynthesis.speak(utterance)\r\n    }\r\n  }\r\n\r\n  // Alterar configuração individual\r\n  const handleSettingChange = useCallback((key, value) => {\r\n    console.log(`🔧 Alterando configuração: ${key} = ${value}`)\r\n    const newSettings = { [key]: value }\r\n    updateSettings(newSettings)\r\n    showSuccessMessage()\r\n    \r\n    // Feedback por voz\r\n    speakText(`${key} ${value ? 'ativado' : 'desativado'}`)\r\n  }, [updateSettings, settings.textToSpeech])\r\n\r\n  // Aplicar tema preset\r\n  const handleApplyPreset = useCallback((preset) => {\r\n    console.log(`🎨 Aplicando preset: ${preset}`)\r\n    applyPreset(preset)\r\n    showSuccessMessage()\r\n    speakText(`Preset ${preset} aplicado`)\r\n  }, [applyPreset, settings.textToSpeech])\r\n\r\n  return (\r\n    <div className={styles.container}>\r\n      <div className={styles.pageHeader}>\r\n        <button className={styles.backButton} onClick={onBack}>\r\n          ← Voltar\r\n        </button>\r\n        <h1 className={styles.pageTitle}>♿ Configurações de Acessibilidade</h1>\r\n        <p className={styles.pageSubtitle}>\r\n          Personalize sua experiência para ter o melhor acesso ao Portal Betina\r\n        </p>\r\n      </div>\r\n      \r\n      <div className={styles.pageContent}>\r\n        <div className={styles.accessibilityPanel}>\r\n          <div className={styles.panelContent}>\r\n            <p className={styles.panelInfo}>\r\n              Configure suas preferências de acessibilidade para uma melhor experiência no Portal Betina.\r\n            </p>\r\n            \r\n            {/* Presets Rápidos */}\r\n            <div className={styles.accessibilityGroup}>\r\n              <h3 className={styles.groupTitle}>⚡ Configurações Rápidas</h3>\r\n              <div className={styles.presetsGrid}>\r\n                <button \r\n                  className={styles.presetButton}\r\n                  onClick={() => handleApplyPreset('default')}\r\n                >\r\n                  Padrão\r\n                </button>\r\n                <button \r\n                  className={styles.presetButton}\r\n                  onClick={() => handleApplyPreset('high-contrast')}\r\n                >\r\n                  Alto Contraste\r\n                </button>\r\n                <button \r\n                  className={styles.presetButton}\r\n                  onClick={() => handleApplyPreset('autism-friendly')}\r\n                >\r\n                  Autismo\r\n                </button>\r\n                <button \r\n                  className={styles.presetButton}\r\n                  onClick={() => handleApplyPreset('dyslexia')}\r\n                >\r\n                  Dislexia\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Configurações Visuais */}\r\n            <div className={styles.accessibilityGroup}>\r\n              <h3 className={styles.groupTitle}>👁️ Visual</h3>\r\n              \r\n              <div className={styles.optionRow}>\r\n                <label className={styles.optionLabel}>\r\n                  <span className={styles.optionText}>Alto Contraste</span>\r\n                  <div className={styles.switchContainer}>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      className={styles.switchInput}\r\n                      checked={settings.highContrast}\r\n                      onChange={(e) => handleSettingChange('highContrast', e.target.checked)}\r\n                    />\r\n                    <span className={styles.switchSlider}></span>\r\n                  </div>\r\n                </label>\r\n              </div>\r\n\r\n              <div className={styles.optionRow}>\r\n                <label className={styles.optionLabel}>\r\n                  <span className={styles.optionText}>Tamanho da Fonte</span>\r\n                  <select\r\n                    className={styles.selectInput}\r\n                    value={settings.fontSize}\r\n                    onChange={(e) => handleSettingChange('fontSize', e.target.value)}\r\n                  >\r\n                    <option value=\"small\">Pequena</option>\r\n                    <option value=\"medium\">Média</option>\r\n                    <option value=\"large\">Grande</option>\r\n                    <option value=\"extra-large\">Extra Grande</option>\r\n                  </select>\r\n                </label>\r\n              </div>\r\n\r\n              <div className={styles.optionRow}>\r\n                <label className={styles.optionLabel}>\r\n                  <span className={styles.optionText}>Fonte para Dislexia</span>\r\n                  <div className={styles.switchContainer}>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      className={styles.switchInput}\r\n                      checked={settings.dyslexiaFriendly}\r\n                      onChange={(e) => handleSettingChange('dyslexiaFriendly', e.target.checked)}\r\n                    />\r\n                    <span className={styles.switchSlider}></span>\r\n                  </div>\r\n                </label>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Configurações de Movimento */}\r\n            <div className={styles.accessibilityGroup}>\r\n              <h3 className={styles.groupTitle}>🎭 Movimento</h3>\r\n              \r\n              <div className={styles.optionRow}>\r\n                <label className={styles.optionLabel}>\r\n                  <span className={styles.optionText}>Reduzir Animações</span>\r\n                  <div className={styles.switchContainer}>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      className={styles.switchInput}\r\n                      checked={settings.reducedMotion}\r\n                      onChange={(e) => handleSettingChange('reducedMotion', e.target.checked)}\r\n                    />\r\n                    <span className={styles.switchSlider}></span>\r\n                  </div>\r\n                </label>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Configurações de Áudio */}\r\n            <div className={styles.accessibilityGroup}>\r\n              <h3 className={styles.groupTitle}>🔊 Áudio</h3>\r\n              \r\n              <div className={styles.optionRow}>\r\n                <label className={styles.optionLabel}>\r\n                  <span className={styles.optionText}>Leitura de Texto</span>\r\n                  <div className={styles.switchContainer}>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      className={styles.switchInput}\r\n                      checked={settings.textToSpeech}\r\n                      onChange={(e) => handleSettingChange('textToSpeech', e.target.checked)}\r\n                    />\r\n                    <span className={styles.switchSlider}></span>\r\n                  </div>\r\n                </label>\r\n              </div>\r\n\r\n              <div className={styles.optionRow}>\r\n                <label className={styles.optionLabel}>\r\n                  <span className={styles.optionText}>Sons Ativados</span>\r\n                  <div className={styles.switchContainer}>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      className={styles.switchInput}\r\n                      checked={settings.soundEnabled}\r\n                      onChange={(e) => handleSettingChange('soundEnabled', e.target.checked)}\r\n                    />\r\n                    <span className={styles.switchSlider}></span>\r\n                  </div>\r\n                </label>\r\n              </div>\r\n\r\n              <div className={styles.optionRow}>\r\n                <label className={styles.optionLabel}>\r\n                  <span className={styles.optionText}>Leitura Automática</span>\r\n                  <div className={styles.switchContainer}>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      className={styles.switchInput}\r\n                      checked={settings.autoRead}\r\n                      onChange={(e) => handleSettingChange('autoRead', e.target.checked)}\r\n                    />\r\n                    <span className={styles.switchSlider}></span>\r\n                  </div>\r\n                </label>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Temas */}\r\n            <div className={styles.accessibilityGroup}>\r\n              <h3 className={styles.groupTitle}>🎨 Tema</h3>\r\n              \r\n              <div className={styles.optionRow}>\r\n                <label className={styles.optionLabel}>\r\n                  <span className={styles.optionText}>Esquema de Cores</span>\r\n                  <select\r\n                    className={styles.selectInput}\r\n                    value={settings.colorScheme}\r\n                    onChange={(e) => handleSettingChange('colorScheme', e.target.value)}\r\n                  >\r\n                    <option value=\"default\">Padrão</option>\r\n                    <option value=\"dark\">Escuro</option>\r\n                    <option value=\"soft\">Suave</option>\r\n                    <option value=\"high-contrast\">Alto Contraste</option>\r\n                  </select>\r\n                </label>\r\n              </div>\r\n            </div>\r\n\r\n            {savedSuccessfully && (\r\n              <div className={styles.successMessage}>\r\n                ✅ Configurações salvas com sucesso!\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default AccessibilityPage\r\n"], "names": ["useState", "useEffect", "useCallback", "jsxDEV"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,SAAS,kBAAkB,EAAE,UAAU;AACrC,QAAM,EAAE,UAAU,gBAAgB,YAAA,IAAgB,wBAAwB;AAC1E,QAAM,CAAC,mBAAmB,oBAAoB,IAAIA,aAAAA,SAAS,KAAK;AAGhEC,eAAAA,UAAU,MAAM;AACd,UAAM,6BAA6B,MAAM;AACvC,YAAM,OAAO,SAAS;AAItB,UAAI,SAAS,cAAc;AACpB,aAAA,UAAU,IAAI,eAAe;AAAA,MAAA,OAC7B;AACA,aAAA,UAAU,OAAO,eAAe;AAAA,MAAA;AAIvC,WAAK,UAAU,OAAO,cAAc,eAAe,cAAc,kBAAkB;AACnF,WAAK,UAAU,IAAI,QAAQ,SAAS,QAAQ,EAAE;AAG9C,UAAI,SAAS,kBAAkB;AACxB,aAAA,UAAU,IAAI,mBAAmB;AAAA,MAAA,OACjC;AACA,aAAA,UAAU,OAAO,mBAAmB;AAAA,MAAA;AAI3C,UAAI,SAAS,eAAe;AACrB,aAAA,UAAU,IAAI,gBAAgB;AAAA,MAAA,OAC9B;AACA,aAAA,UAAU,OAAO,gBAAgB;AAAA,MAAA;AAIxC,WAAK,UAAU,OAAO,kBAAkB,eAAe,eAAe,sBAAsB;AAC5F,WAAK,UAAU,IAAI,UAAU,SAAS,WAAW,EAAE;AAGnD,YAAM,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmCf,YAAA,gBAAgB,SAAS,eAAe,sBAAsB;AACpE,UAAI,eAAe;AACjB,sBAAc,OAAO;AAAA,MAAA;AAIjB,YAAA,eAAe,SAAS,cAAc,OAAO;AACnD,mBAAa,KAAK;AAClB,mBAAa,cAAc;AAClB,eAAA,KAAK,YAAY,YAAY;AAAA,IACxC;AAE2B,+BAAA;AAAA,EAAA,GAC1B,CAAC,QAAQ,CAAC;AAEb,QAAM,CAAC,qBAAqB,sBAAsB,IAAID,aAAAA,SAAS,SAAS,YAAY;AAGpF,QAAM,qBAAqB,MAAM;AAC/B,yBAAqB,IAAI;AACzB,eAAW,MAAM,qBAAqB,KAAK,GAAG,GAAI;AAAA,EACpD;AAGM,QAAA,YAAY,CAAC,SAAS;AACtB,QAAA,SAAS,gBAAgB,qBAAqB,QAAQ;AAClD,YAAA,YAAY,IAAI,yBAAyB,IAAI;AACnD,gBAAU,OAAO;AACjB,gBAAU,QAAQ;AAClB,sBAAgB,MAAM,SAAS;AAAA,IAAA;AAAA,EAEnC;AAGA,QAAM,sBAAsBE,aAAAA,YAAY,CAAC,KAAK,UAAU;AACtD,YAAQ,IAAI,8BAA8B,GAAG,MAAM,KAAK,EAAE;AAC1D,UAAM,cAAc,EAAE,CAAC,GAAG,GAAG,MAAM;AACnC,mBAAe,WAAW;AACP,uBAAA;AAGnB,cAAU,GAAG,GAAG,IAAI,QAAQ,YAAY,YAAY,EAAE;AAAA,EACrD,GAAA,CAAC,gBAAgB,SAAS,YAAY,CAAC;AAGpC,QAAA,oBAAoBA,yBAAY,CAAC,WAAW;AACxC,YAAA,IAAI,wBAAwB,MAAM,EAAE;AAC5C,gBAAY,MAAM;AACC,uBAAA;AACT,cAAA,UAAU,MAAM,WAAW;AAAA,EACpC,GAAA,CAAC,aAAa,SAAS,YAAY,CAAC;AAEvC,SACGC,qCAAAA,OAAA,OAAA,EAAI,WAAW,OAAO,WACrB,UAAA;AAAA,IAACA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,YACrB,UAAA;AAAA,MAAAA,qCAAAA,OAAC,YAAO,WAAW,OAAO,YAAY,SAAS,QAAQ,UAAvD,cAAA,QAAA,OAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAEA,GAAA,IAAA;AAAA,MACCA,4CAAA,MAAA,EAAG,WAAW,OAAO,WAAW,UAAjC,oCAAA,GAAA,QAAA,OAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAkE,GAAA,IAAA;AAAA,MACjEA,4CAAA,KAAA,EAAE,WAAW,OAAO,cAAc,UAAnC,wEAAA,GAAA,QAAA,OAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA,GAEA,IAAA;AAAA,IAAA,EAPF,GAAA,QAAA,MAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IAQA,GAAA,IAAA;AAAA,IAECA,4CAAA,OAAA,EAAI,WAAW,OAAO,aACrB,UAACA,4CAAA,OAAA,EAAI,WAAW,OAAO,oBACrB,UAAAA,4CAAC,OAAI,EAAA,WAAW,OAAO,cACrB,UAAA;AAAA,MAAAA,4CAAC,KAAE,EAAA,WAAW,OAAO,WAAW,UAAhC,8FAAA,GAAA,QAAA,OAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAEA,GAAA,IAAA;AAAA,MAGCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,oBACrB,UAAA;AAAA,QAAAA,4CAAC,MAAG,EAAA,WAAW,OAAO,YAAY,UAAlC,0BAAA,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAyD,GAAA,IAAA;AAAA,QACxDA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,aACrB,UAAA;AAAA,UAAAA,qCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,WAAW,OAAO;AAAA,cAClB,SAAS,MAAM,kBAAkB,SAAS;AAAA,cAC3C,UAAA;AAAA,YAAA;AAAA,YAHD;AAAA,YAAA;AAAA,YAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA;AAAA,YAAA;AAAA,UAKA;AAAA,UACAA,qCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,WAAW,OAAO;AAAA,cAClB,SAAS,MAAM,kBAAkB,eAAe;AAAA,cACjD,UAAA;AAAA,YAAA;AAAA,YAHD;AAAA,YAAA;AAAA,YAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA;AAAA,YAAA;AAAA,UAKA;AAAA,UACAA,qCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,WAAW,OAAO;AAAA,cAClB,SAAS,MAAM,kBAAkB,iBAAiB;AAAA,cACnD,UAAA;AAAA,YAAA;AAAA,YAHD;AAAA,YAAA;AAAA,YAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA;AAAA,YAAA;AAAA,UAKA;AAAA,UACAA,qCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,WAAW,OAAO;AAAA,cAClB,SAAS,MAAM,kBAAkB,UAAU;AAAA,cAC5C,UAAA;AAAA,YAAA;AAAA,YAHD;AAAA,YAAA;AAAA,YAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QAKA,EAxBF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAyBA,IAAA;AAAA,MAAA,EA3BF,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MA4BA,GAAA,IAAA;AAAA,MAGCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,oBACrB,UAAA;AAAA,QAAAA,4CAAC,MAAG,EAAA,WAAW,OAAO,YAAY,UAAlC,aAAA,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAA4C,GAAA,IAAA;AAAA,QAE5CA,qCAAAA,OAAC,SAAI,WAAW,OAAO,WACrB,UAACA,qCAAA,OAAA,SAAA,EAAM,WAAW,OAAO,aACvB,UAAA;AAAA,UAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,YAAY,UAApC,iBAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAkD,GAAA,IAAA;AAAA,UACjDA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,iBACrB,UAAA;AAAA,YAAAA,qCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACC,MAAK;AAAA,gBACL,WAAW,OAAO;AAAA,gBAClB,SAAS,SAAS;AAAA,gBAClB,UAAU,CAAC,MAAM,oBAAoB,gBAAgB,EAAE,OAAO,OAAO;AAAA,cAAA;AAAA,cAJvE;AAAA,cAAA;AAAA,cAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA;AAAA,cAAA;AAAA,YAKA;AAAA,YACCA,qCAAA,OAAA,QAAA,EAAK,WAAW,OAAO,aAAxB,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAAsC,IAAA;AAAA,UAAA,EAPxC,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAQA,IAAA;AAAA,QAAA,EAVF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAAA,IAWA,EAZF,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAaA,GAAA,IAAA;AAAA,QAEAA,qCAAAA,OAAC,SAAI,WAAW,OAAO,WACrB,UAACA,qCAAA,OAAA,SAAA,EAAM,WAAW,OAAO,aACvB,UAAA;AAAA,UAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,YAAY,UAApC,mBAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAoD,GAAA,IAAA;AAAA,UACpDA,qCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,WAAW,OAAO;AAAA,cAClB,OAAO,SAAS;AAAA,cAChB,UAAU,CAAC,MAAM,oBAAoB,YAAY,EAAE,OAAO,KAAK;AAAA,cAE/D,UAAA;AAAA,gBAACA,4CAAA,UAAA,EAAO,OAAM,SAAQ,UAAtB,UAAA,GAAA,QAAA,OAAA;AAAA,kBAAA,UAAA;AAAA,kBAAA,YAAA;AAAA,kBAAA,cAAA;AAAA,gBAA6B,GAAA,IAAA;AAAA,gBAC5BA,4CAAA,UAAA,EAAO,OAAM,UAAS,UAAvB,QAAA,GAAA,QAAA,OAAA;AAAA,kBAAA,UAAA;AAAA,kBAAA,YAAA;AAAA,kBAAA,cAAA;AAAA,gBAA4B,GAAA,IAAA;AAAA,gBAC3BA,4CAAA,UAAA,EAAO,OAAM,SAAQ,UAAtB,SAAA,GAAA,QAAA,OAAA;AAAA,kBAAA,UAAA;AAAA,kBAAA,YAAA;AAAA,kBAAA,cAAA;AAAA,gBAA4B,GAAA,IAAA;AAAA,gBAC3BA,4CAAA,UAAA,EAAO,OAAM,eAAc,UAA5B,eAAA,GAAA,QAAA,OAAA;AAAA,kBAAA,UAAA;AAAA,kBAAA,YAAA;AAAA,kBAAA,cAAA;AAAA,gBAAA,GAAwC,IAAA;AAAA,cAAA;AAAA,YAAA;AAAA,YAR1C;AAAA,YAAA;AAAA,YAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QASA,EAXF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAAA,IAYA,EAbF,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAcA,GAAA,IAAA;AAAA,QAEAA,qCAAAA,OAAC,SAAI,WAAW,OAAO,WACrB,UAACA,qCAAA,OAAA,SAAA,EAAM,WAAW,OAAO,aACvB,UAAA;AAAA,UAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,YAAY,UAApC,sBAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAuD,GAAA,IAAA;AAAA,UACtDA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,iBACrB,UAAA;AAAA,YAAAA,qCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACC,MAAK;AAAA,gBACL,WAAW,OAAO;AAAA,gBAClB,SAAS,SAAS;AAAA,gBAClB,UAAU,CAAC,MAAM,oBAAoB,oBAAoB,EAAE,OAAO,OAAO;AAAA,cAAA;AAAA,cAJ3E;AAAA,cAAA;AAAA,cAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA;AAAA,cAAA;AAAA,YAKA;AAAA,YACCA,qCAAA,OAAA,QAAA,EAAK,WAAW,OAAO,aAAxB,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAAsC,IAAA;AAAA,UAAA,EAPxC,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAQA,IAAA;AAAA,QAAA,EAVF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAAA,IAWA,EAZF,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAaA,IAAA;AAAA,MAAA,EA/CF,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAgDA,GAAA,IAAA;AAAA,MAGCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,oBACrB,UAAA;AAAA,QAAAA,4CAAC,MAAG,EAAA,WAAW,OAAO,YAAY,UAAlC,eAAA,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAA8C,GAAA,IAAA;AAAA,QAE9CA,qCAAAA,OAAC,SAAI,WAAW,OAAO,WACrB,UAACA,qCAAA,OAAA,SAAA,EAAM,WAAW,OAAO,aACvB,UAAA;AAAA,UAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,YAAY,UAApC,oBAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAqD,GAAA,IAAA;AAAA,UACpDA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,iBACrB,UAAA;AAAA,YAAAA,qCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACC,MAAK;AAAA,gBACL,WAAW,OAAO;AAAA,gBAClB,SAAS,SAAS;AAAA,gBAClB,UAAU,CAAC,MAAM,oBAAoB,iBAAiB,EAAE,OAAO,OAAO;AAAA,cAAA;AAAA,cAJxE;AAAA,cAAA;AAAA,cAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA;AAAA,cAAA;AAAA,YAKA;AAAA,YACCA,qCAAA,OAAA,QAAA,EAAK,WAAW,OAAO,aAAxB,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAAsC,IAAA;AAAA,UAAA,EAPxC,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAQA,IAAA;AAAA,QAAA,EAVF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAAA,IAWA,EAZF,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAaA,IAAA;AAAA,MAAA,EAhBF,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAiBA,GAAA,IAAA;AAAA,MAGCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,oBACrB,UAAA;AAAA,QAAAA,4CAAC,MAAG,EAAA,WAAW,OAAO,YAAY,UAAlC,WAAA,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAA0C,GAAA,IAAA;AAAA,QAE1CA,qCAAAA,OAAC,SAAI,WAAW,OAAO,WACrB,UAACA,qCAAA,OAAA,SAAA,EAAM,WAAW,OAAO,aACvB,UAAA;AAAA,UAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,YAAY,UAApC,mBAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAoD,GAAA,IAAA;AAAA,UACnDA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,iBACrB,UAAA;AAAA,YAAAA,qCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACC,MAAK;AAAA,gBACL,WAAW,OAAO;AAAA,gBAClB,SAAS,SAAS;AAAA,gBAClB,UAAU,CAAC,MAAM,oBAAoB,gBAAgB,EAAE,OAAO,OAAO;AAAA,cAAA;AAAA,cAJvE;AAAA,cAAA;AAAA,cAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA;AAAA,cAAA;AAAA,YAKA;AAAA,YACCA,qCAAA,OAAA,QAAA,EAAK,WAAW,OAAO,aAAxB,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAAsC,IAAA;AAAA,UAAA,EAPxC,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAQA,IAAA;AAAA,QAAA,EAVF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAAA,IAWA,EAZF,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAaA,GAAA,IAAA;AAAA,QAEAA,qCAAAA,OAAC,SAAI,WAAW,OAAO,WACrB,UAACA,qCAAA,OAAA,SAAA,EAAM,WAAW,OAAO,aACvB,UAAA;AAAA,UAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,YAAY,UAApC,gBAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAiD,GAAA,IAAA;AAAA,UAChDA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,iBACrB,UAAA;AAAA,YAAAA,qCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACC,MAAK;AAAA,gBACL,WAAW,OAAO;AAAA,gBAClB,SAAS,SAAS;AAAA,gBAClB,UAAU,CAAC,MAAM,oBAAoB,gBAAgB,EAAE,OAAO,OAAO;AAAA,cAAA;AAAA,cAJvE;AAAA,cAAA;AAAA,cAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA;AAAA,cAAA;AAAA,YAKA;AAAA,YACCA,qCAAA,OAAA,QAAA,EAAK,WAAW,OAAO,aAAxB,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAAsC,IAAA;AAAA,UAAA,EAPxC,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAQA,IAAA;AAAA,QAAA,EAVF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAAA,IAWA,EAZF,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAaA,GAAA,IAAA;AAAA,QAEAA,qCAAAA,OAAC,SAAI,WAAW,OAAO,WACrB,UAACA,qCAAA,OAAA,SAAA,EAAM,WAAW,OAAO,aACvB,UAAA;AAAA,UAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,YAAY,UAApC,qBAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAsD,GAAA,IAAA;AAAA,UACrDA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,iBACrB,UAAA;AAAA,YAAAA,qCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACC,MAAK;AAAA,gBACL,WAAW,OAAO;AAAA,gBAClB,SAAS,SAAS;AAAA,gBAClB,UAAU,CAAC,MAAM,oBAAoB,YAAY,EAAE,OAAO,OAAO;AAAA,cAAA;AAAA,cAJnE;AAAA,cAAA;AAAA,cAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA;AAAA,cAAA;AAAA,YAKA;AAAA,YACCA,qCAAA,OAAA,QAAA,EAAK,WAAW,OAAO,aAAxB,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAAsC,IAAA;AAAA,UAAA,EAPxC,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAQA,IAAA;AAAA,QAAA,EAVF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAAA,IAWA,EAZF,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAaA,IAAA;AAAA,MAAA,EA9CF,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MA+CA,GAAA,IAAA;AAAA,MAGCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,oBACrB,UAAA;AAAA,QAAAA,4CAAC,MAAG,EAAA,WAAW,OAAO,YAAY,UAAlC,UAAA,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAyC,GAAA,IAAA;AAAA,QAEzCA,qCAAAA,OAAC,SAAI,WAAW,OAAO,WACrB,UAACA,qCAAA,OAAA,SAAA,EAAM,WAAW,OAAO,aACvB,UAAA;AAAA,UAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,YAAY,UAApC,mBAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAoD,GAAA,IAAA;AAAA,UACpDA,qCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,WAAW,OAAO;AAAA,cAClB,OAAO,SAAS;AAAA,cAChB,UAAU,CAAC,MAAM,oBAAoB,eAAe,EAAE,OAAO,KAAK;AAAA,cAElE,UAAA;AAAA,gBAACA,4CAAA,UAAA,EAAO,OAAM,WAAU,UAAxB,SAAA,GAAA,QAAA,OAAA;AAAA,kBAAA,UAAA;AAAA,kBAAA,YAAA;AAAA,kBAAA,cAAA;AAAA,gBAA8B,GAAA,IAAA;AAAA,gBAC7BA,4CAAA,UAAA,EAAO,OAAM,QAAO,UAArB,SAAA,GAAA,QAAA,OAAA;AAAA,kBAAA,UAAA;AAAA,kBAAA,YAAA;AAAA,kBAAA,cAAA;AAAA,gBAA2B,GAAA,IAAA;AAAA,gBAC1BA,4CAAA,UAAA,EAAO,OAAM,QAAO,UAArB,QAAA,GAAA,QAAA,OAAA;AAAA,kBAAA,UAAA;AAAA,kBAAA,YAAA;AAAA,kBAAA,cAAA;AAAA,gBAA0B,GAAA,IAAA;AAAA,gBACzBA,4CAAA,UAAA,EAAO,OAAM,iBAAgB,UAA9B,iBAAA,GAAA,QAAA,OAAA;AAAA,kBAAA,UAAA;AAAA,kBAAA,YAAA;AAAA,kBAAA,cAAA;AAAA,gBAAA,GAA4C,IAAA;AAAA,cAAA;AAAA,YAAA;AAAA,YAR9C;AAAA,YAAA;AAAA,YAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QASA,EAXF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAAA,IAYA,EAbF,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAcA,IAAA;AAAA,MAAA,EAjBF,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAkBA,GAAA,IAAA;AAAA,MAEC,qBACEA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,gBAAgB,UAAvC,yCAAA,QAAA,OAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA,GAEA,IAAA;AAAA,IAAA,EArLJ,GAAA,QAAA,MAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IAAA,GAAA,IAuLA,EAxLF,GAAA,QAAA,OAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IAAA,GAAA,IAyLA,EA1LF,GAAA,QAAA,OAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IAAA,GA2LA,IAAA;AAAA,EAAA,EAtMF,GAAA,QAAA,MAAA;AAAA,IAAA,UAAA;AAAA,IAAA,YAAA;AAAA,IAAA,cAAA;AAAA,EAuMA,GAAA,IAAA;AAEJ;"}