/**
 * @file QuebraCabeca.module.css
 * @description Estilos modulares para o Jogo Quebra-Cabeça - Adaptado ao padrão unificado
 * @version 3.1.0
 */

/* Variáveis CSS para consistência e reutilização */
:root {
  --card-background: rgba(255, 255, 255, 0.1);
  --card-border: 1px solid rgba(255, 255, 255, 0.2);
  --card-blur: blur(10px);
  --success-bg: rgba(76, 175, 80, 0.3);
  --success-border: rgba(76, 175, 80, 0.5);
  --error-bg: rgba(244, 67, 54, 0.3);
  --error-border: rgba(244, 67, 54, 0.5);
  --primary-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Container principal do QuebraCabeca */
._quebraCabecaGame_hipsz_41 {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Conteúdo do jogo */
._gameContent_hipsz_63 {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Header do jogo */
._gameHeader_hipsz_83 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 3rem 1rem 1rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  position: relative;
  min-height: 70px;
}

._gameTitle_hipsz_111 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

._activitySubtitle_hipsz_135 {
  font-size: 0.7rem;
  opacity: 0.8;
  margin-top: 0.25rem;
  background: var(--card-background);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: var(--card-border);
}

/* Botões TTS */
._headerTtsButton_hipsz_157 {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: white;
  z-index: 10;
}

._headerTtsButton_hipsz_157:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

._headerTtsButton_hipsz_157:active {
  transform: scale(0.95);
}

._ttsActive_hipsz_213 {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
}

._ttsInactive_hipsz_223 {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
}

/* Área do quebra-cabeça - Seguindo padrão Memory Game */
._puzzleContainer_hipsz_235 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 2rem 0;
  gap: 1.5rem;
}

/* Exibição da emoção alvo */
._targetEmotionDisplay_hipsz_253 {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1rem 2rem;
  margin-bottom: 1rem;
}

._targetEmotionIcon_hipsz_277 {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}

._targetEmotionName_hipsz_287 {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Container das peças disponíveis */
._piecesContainer_hipsz_303 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  margin-top: 1rem;
  width: 100%;
  max-width: 600px;
}

._piecesTitle_hipsz_325 {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  text-align: center;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Grid do quebra-cabeça - Seguindo padrão Memory Game */
._puzzleGrid_hipsz_345 {
  display: grid;
  gap: 1rem;
  justify-content: center;
  margin: 1rem auto;
  max-width: 400px;
  padding: 1.5rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Slots do quebra-cabeça - Seguindo padrão Memory Game */
._puzzleSlot_hipsz_375 {
  width: 80px;
  height: 80px;
  background: var(--card-background);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

._puzzleSlot_hipsz_375._empty_hipsz_407 {
  border: 2px dashed rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.05);
}

._puzzleSlot_hipsz_375._filled_hipsz_417 {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.1));
  border-color: var(--success-border);
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.2);
}

._puzzleSlot_hipsz_375:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

._placedPiece_hipsz_439 {
  font-size: 2rem;
  animation: _placeAnimation_hipsz_1 0.3s ease-out;
}

._emptySlot_hipsz_449 {
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.5);
  font-weight: bold;
}

@keyframes _placeAnimation_hipsz_1 {
  0% { transform: scale(0.8); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

._puzzlePiece_hipsz_471::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

._puzzlePiece_hipsz_471:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
}

._puzzlePiece_hipsz_471:hover::before {
  transform: translateX(100%);
}

._puzzlePiece_hipsz_471._filled_hipsz_417 {
  background: linear-gradient(135deg, rgba(255, 206, 84, 0.4), rgba(255, 206, 84, 0.2));
  border-color: #FECA57;
  box-shadow: 0 6px 20px rgba(254, 202, 87, 0.3);
}

._puzzlePiece_hipsz_471._correct_hipsz_529 {
  background: linear-gradient(135deg, var(--success-bg), rgba(76, 175, 80, 0.2));
  border-color: var(--success-border);
  animation: _correctPulse_hipsz_1 0.8s ease-in-out;
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

._puzzlePiece_hipsz_471._empty_hipsz_407 {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
  border: 2px dashed rgba(255, 255, 255, 0.4);
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.1);
}

@keyframes _correctPulse_hipsz_1 {
  0% { transform: scale(1); }
  25% { transform: scale(1.15); }
  50% { transform: scale(1.05); }
  75% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Área das peças - Design elegante mobile-first */
._piecesArea_hipsz_573 {
  width: 280px;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 24px;
  padding: 1.75rem;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

._piecesArea_hipsz_573:hover {
  box-shadow: 0 8px 35px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

._piecesTitle_hipsz_325 {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1.75rem;
  text-align: center;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Grid das peças disponíveis - Seguindo padrão Memory Game */
._piecesGrid_hipsz_625 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
  gap: 1rem;
  justify-content: center;
  max-width: 500px;
  margin: 0 auto;
}

._availablePiece_hipsz_643 {
  width: 70px;
  height: 70px;
  background: var(--card-background);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  color: white;
}

._availablePiece_hipsz_643:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

._availablePiece_hipsz_643:active {
  transform: scale(0.95);
}

._availablePiece_hipsz_643._used_hipsz_697 {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
}

._availablePiece_hipsz_643._used_hipsz_697:hover {
  transform: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

._availablePiece_hipsz_643::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

._availablePiece_hipsz_643:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.35), rgba(255, 255, 255, 0.15));
  transform: scale(1.1) rotate(3deg);
  cursor: grab;
  border-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
}

._availablePiece_hipsz_643:hover::before {
  left: 100%;
}

._availablePiece_hipsz_643:active {
  cursor: grabbing;
  transform: scale(1.05) rotate(1deg);
}

._availablePiece_hipsz_643._used_hipsz_697 {
  opacity: 0.4;
  cursor: not-allowed;
  transform: scale(0.9);
  filter: grayscale(100%);
  background: rgba(128, 128, 128, 0.2);
}

/* Imagem de referência */
._referenceImage_hipsz_795 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

._referenceTitle_hipsz_815 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

._referencePreview_hipsz_829 {
  width: 150px;
  height: 150px;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
}

/* Estatísticas do jogo - Cards elegantes mobile-first */
._gameStats_hipsz_857 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 0 0.5rem;
}

._statCard_hipsz_873 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.25rem 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

._statCard_hipsz_873:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
  background: rgba(255, 255, 255, 0.15);
}

._statCard_hipsz_873::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #E91E63);
  opacity: 0.8;
  border-radius: 16px 16px 0 0;
}

._statCard_hipsz_873::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

._statCard_hipsz_873:hover::after {
  animation: _shimmer_hipsz_1 1.5s ease-in-out infinite;
}

@keyframes _shimmer_hipsz_1 {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); opacity: 0; }
}

._statValue_hipsz_981 {
  font-size: 1.75rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #fff, #e3f2fd);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

._statLabel_hipsz_1005 {
  font-size: 0.85rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Controles do jogo - Botões elegantes mobile-first */
._gameControls_hipsz_1025 {
  display: flex;
  gap: 1.25rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
  padding: 0 1rem;
}

._controlButton_hipsz_1043 {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.95rem;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  min-width: 120px;
  text-align: center;
}

._controlButton_hipsz_1043::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

._controlButton_hipsz_1043:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
}

._controlButton_hipsz_1043:hover::before {
  left: 100%;
}

._controlButton_hipsz_1043:active {
  transform: translateY(-1px) scale(1.01);
}

._nextButton_hipsz_1131 {
  background: linear-gradient(135deg, var(--success-bg), rgba(76, 175, 80, 0.2));
  border-color: var(--success-border);
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.2);
}

._nextButton_hipsz_1131:hover {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.5), rgba(76, 175, 80, 0.3));
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
}

._hintButton_hipsz_1153 {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.4), rgba(255, 193, 7, 0.2));
  border-color: rgba(255, 193, 7, 0.6);
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
}

._hintButton_hipsz_1153:hover {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.5), rgba(255, 193, 7, 0.3));
  box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
}

._resetButton_hipsz_1175 {
  background: linear-gradient(135deg, var(--error-bg), rgba(244, 67, 54, 0.2));
  border-color: var(--error-border);
  box-shadow: 0 4px 15px rgba(244, 67, 54, 0.2);
}

._resetButton_hipsz_1175:hover {
  background: rgba(244, 67, 54, 0.4);
}

._changeDifficultyBtn_hipsz_1195 {
  background: rgba(156, 39, 176, 0.3);
  border-color: rgba(156, 39, 176, 0.5);
}

._changeDifficultyBtn_hipsz_1195:hover {
  background: rgba(156, 39, 176, 0.4);
}

/* Mensagens de feedback */
._feedbackMessage_hipsz_1215 {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 1.5rem 2.5rem;
  border-radius: 16px;
  font-size: 1.3rem;
  font-weight: 700;
  z-index: 1000;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: _messageSlide_hipsz_1 3s ease-in-out;
}

._feedbackMessage_hipsz_1215._success_hipsz_1245 {
  background: var(--success-bg);
  color: white;
}

._feedbackMessage_hipsz_1215._hint_hipsz_1153 {
  background: rgba(255, 193, 7, 0.95);
  color: white;
}

._feedbackMessage_hipsz_1215._error_hipsz_1265 {
  background: var(--error-bg);
  color: white;
}

@keyframes _messageSlide_hipsz_1 {
  0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
  15% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  85% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}

/* Tela de finalização */
._completionScreen_hipsz_1291 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: var(--card-blur);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

._completionCard_hipsz_1319 {
  background: var(--gradient-bg);
  border-radius: 20px;
  padding: 3rem;
  text-align: center;
  color: white;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border: var(--card-border);
}

._completionTitle_hipsz_1343 {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
}

._completionMessage_hipsz_1355 {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

._completionActions_hipsz_1367 {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsividade móvel-first - Design elegante */
@media (max-width: 768px) {
  ._quebraCabecaGame_hipsz_41 {
    padding: 0.75rem;
  }
  
  ._puzzleArea_hipsz_1393 {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  ._piecesArea_hipsz_573 {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
    padding: 1.5rem;
  }
  
  ._gameTitle_hipsz_111 {
    font-size: 1.6rem;
  }
  
  ._puzzleGrid_hipsz_345 {
    max-width: 280px;
    gap: 0.5rem;
    padding: 1.25rem;
  }
  
  ._puzzlePiece_hipsz_471 {
    width: 70px;
    height: 70px;
    font-size: 1.8rem;
  }
  
  ._availablePiece_hipsz_643 {
    width: 65px;
    height: 65px;
    font-size: 1.4rem;
  }
  
  ._piecesGrid_hipsz_625 {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }
  
  ._gameControls_hipsz_1025 {
    gap: 1rem;
    padding: 0 0.5rem;
  }
  
  ._controlButton_hipsz_1043 {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
    min-width: 100px;
  }
  
  ._gameStats_hipsz_857 {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.875rem;
  }
  
  ._statCard_hipsz_873 {
    padding: 1rem 0.75rem;
  }
  
  ._statValue_hipsz_981 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  ._gameHeader_hipsz_83 {
    padding: 1rem 2.5rem 1rem 1rem;
  }
  
  ._gameTitle_hipsz_111 {
    font-size: 1.4rem;
  }
  
  ._activitySubtitle_hipsz_135 {
    font-size: 0.65rem;
    padding: 0.2rem 0.6rem;
  }
  
  ._puzzleGrid_hipsz_345 {
    max-width: 240px;
    gap: 0.4rem;
    padding: 1rem;
  }
  
  ._puzzlePiece_hipsz_471 {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    border-radius: 12px;
  }
  
  ._availablePiece_hipsz_643 {
    width: 55px;
    height: 55px;
    font-size: 1.2rem;
    border-radius: 12px;
  }
  
  ._piecesGrid_hipsz_625 {
    grid-template-columns: repeat(4, 1fr);
    gap: 0.75rem;
  }
  
  ._piecesArea_hipsz_573 {
    padding: 1.25rem;
    border-radius: 20px;
  }
  
  ._puzzleBoard_hipsz_1609 {
    padding: 1.75rem;
    border-radius: 20px;
  }
  
  ._boardTitle_hipsz_1619 {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
  }
  
  ._gameControls_hipsz_1025 {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }
  
  ._controlButton_hipsz_1043 {
    width: 100%;
    max-width: 280px;
    padding: 1rem;
    font-size: 0.875rem;
  }
  
  ._referencePreview_hipsz_829 {
    width: 120px;
    height: 120px;
    font-size: 2rem;
  }
  
  ._completionCard_hipsz_1319 {
    padding: 2rem;
    margin: 1rem;
    border-radius: 20px;
  }
  
  ._completionTitle_hipsz_1343 {
    font-size: 2rem;
  }
  
  ._gameStats_hipsz_857 {
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }
  
  ._statCard_hipsz_873 {
    padding: 0.875rem 0.5rem;
  }
  
  ._statValue_hipsz_981 {
    font-size: 1.3rem;
  }
  
  ._statLabel_hipsz_1005 {
    font-size: 0.75rem;
  }
}

/* Suporte a alto contraste */
[data-theme="high-contrast"] {
  --card-background: #000;
  --card-border: 1px solid #fff;
  --success-bg: #28a745;
  --success-border: #1e7e34;
  --error-bg: #dc3545;
  --error-border: #bd2130;
}

/* Suporte a movimento reduzido */
._reduced-motion_hipsz_1745 {
  ._puzzlePiece_hipsz_471, ._controlButton_hipsz_1043, ._feedbackMessage_hipsz_1215, ._completionCard_hipsz_1319, ._availablePiece_hipsz_643 {
    animation: none !important;
    transition: none !important;
  }
}

/* =====================================================
   🎯 ESTILOS PARA ATIVIDADES ESPECÍFICAS - QUEBRA-CABEÇA
   ===================================================== */

._activityContainer_hipsz_1767 {
  width: 100%;
  padding: 1rem;
  min-height: 400px;
}

._activityHeader_hipsz_1779 {
  text-align: center;
  margin-bottom: 2rem;
}

._activityTitle_hipsz_1789 {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  margin-bottom: 0.5rem;
}

._activitySubtitle_hipsz_135 {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
}

/* Rotação de Peças */
._rotationInstructions_hipsz_1815 {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: rgba(33, 150, 243, 0.2);
  border-radius: 12px;
  border: 2px solid rgba(33, 150, 243, 0.5);
}

._instructionText_hipsz_1833 {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
}

._rotationActivity_hipsz_1843 {
  display: flex;
  justify-content: center;
  align-items: center;
}

._rotationGrid_hipsz_1855 {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

._rotationSlot_hipsz_1869 {
  position: relative;
  transition: transform 0.3s ease;
  cursor: pointer;
}

._rotationSlot_hipsz_1869:hover {
  transform: scale(1.05);
}

/* Quebra-Cabeça de Padrões */
._patternArea_hipsz_1891 {
  margin-bottom: 2rem;
}

._patternTitle_hipsz_1899 {
  text-align: center;
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
}

._patternSequence_hipsz_1915 {
  display: flex;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

._patternPiece_hipsz_1931 {
  position: relative;
  padding: 1rem;
  border: 2px solid rgba(255, 152, 0, 0.6);
  border-radius: 12px;
  background: rgba(255, 152, 0, 0.2);
  text-align: center;
  min-width: 80px;
}

._patternPiece_hipsz_1931._missing_hipsz_1951 {
  border: 3px dashed rgba(255, 193, 7, 0.8);
  background: rgba(255, 193, 7, 0.3);
  animation: _pulse_hipsz_1 2s infinite;
}

._patternEmoji_hipsz_1963 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

._missingPiece_hipsz_1973 {
  font-size: 2rem;
  color: rgba(255, 193, 7, 0.8);
}

._patternNumber_hipsz_1983 {
  font-size: 0.8rem;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.8);
}

._optionsArea_hipsz_1995 {
  text-align: center;
}

._optionsTitle_hipsz_2003 {
  font-size: 1.1rem;
  color: white;
  margin-bottom: 1rem;
}

._optionsGrid_hipsz_2015 {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

._optionButton_hipsz_2029 {
  padding: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: var(--card-background);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
}

._optionButton_hipsz_2029:hover {
  transform: scale(1.05);
  border-color: rgba(33, 150, 243, 0.6);
}

._optionButton_hipsz_2029._selected_hipsz_2059 {
  border: 3px solid #4CAF50;
  background: rgba(76, 175, 80, 0.3);
  transform: scale(1.05);
}

._optionButton_hipsz_2029:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

._optionEmoji_hipsz_2081 {
  font-size: 2rem;
}

/* Correspondência Emocional */
._targetEmotionArea_hipsz_2091 {
  text-align: center;
  margin-bottom: 2rem;
}

._targetEmotionTitle_hipsz_2101 {
  font-size: 1.2rem;
  color: white;
  margin-bottom: 1rem;
}

._targetEmotion_hipsz_253 {
  padding: 2rem;
  border: 3px solid rgba(244, 67, 54, 0.6);
  border-radius: 20px;
  background: rgba(244, 67, 54, 0.2);
  display: inline-block;
}

._targetEmotionEmoji_hipsz_2129 {
  font-size: 4rem;
  margin-bottom: 1rem;
}

._targetEmotionContext_hipsz_2139 {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
}

._matchingArea_hipsz_2149 {
  margin-bottom: 2rem;
  text-align: center;
}

._matchingTitle_hipsz_2159 {
  font-size: 1.1rem;
  color: white;
  margin-bottom: 1rem;
}

._selectedMatches_hipsz_2171 {
  min-height: 80px;
  padding: 1rem;
  border: 2px dashed rgba(76, 175, 80, 0.5);
  border-radius: 12px;
  background: rgba(76, 175, 80, 0.1);
  display: flex;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

._selectedMatch_hipsz_2171 {
  font-size: 2rem;
  padding: 0.5rem;
  border: 2px solid rgba(76, 175, 80, 0.6);
  border-radius: 8px;
  background: rgba(76, 175, 80, 0.2);
}

._emptyMatches_hipsz_2213 {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
}

._matchingOptions_hipsz_2223 {
  text-align: center;
}

._matchingOptionsTitle_hipsz_2231 {
  font-size: 1.1rem;
  color: white;
  margin-bottom: 1rem;
}

._matchingOptionsGrid_hipsz_2243 {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

._matchingOption_hipsz_2223 {
  font-size: 2rem;
  padding: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: var(--card-background);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
}

._matchingOption_hipsz_2223:hover {
  transform: scale(1.05);
}

._matchingOption_hipsz_2223._selected_hipsz_2059 {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Montagem Criativa */
._creativeCanvas_hipsz_2299 {
  margin-bottom: 2rem;
  text-align: center;
}

._canvasTitle_hipsz_2309 {
  font-size: 1.2rem;
  color: white;
  margin-bottom: 1rem;
}

._canvasGrid_hipsz_2321 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  max-width: 300px;
  margin: 0 auto 1rem;
  padding: 1rem;
  border: 2px dashed rgba(156, 39, 176, 0.5);
  border-radius: 12px;
  background: rgba(156, 39, 176, 0.1);
}

._canvasSlot_hipsz_2345 {
  width: 80px;
  height: 80px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

._canvasSlot_hipsz_2345._filled_hipsz_417 {
  background: rgba(156, 39, 176, 0.3);
  border-color: rgba(156, 39, 176, 0.6);
}

._canvasSlot_hipsz_2345._empty_hipsz_407 {
  background: rgba(255, 255, 255, 0.05);
  border: 2px dashed rgba(255, 255, 255, 0.3);
}

._canvasPiece_hipsz_2389 {
  font-size: 2rem;
}

._emptySlot_hipsz_449 {
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.5);
}

._creativeConstraints_hipsz_2407 {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

._creativePieces_hipsz_2417 {
  text-align: center;
}

._creativePiecesTitle_hipsz_2425 {
  font-size: 1.1rem;
  color: white;
  margin-bottom: 1rem;
}

._creativePiecesGrid_hipsz_2437 {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

._creativePiece_hipsz_2417 {
  font-size: 2rem;
  padding: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: var(--card-background);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
}

._creativePiece_hipsz_2417:hover {
  transform: scale(1.05);
}

._creativePiece_hipsz_2417._used_hipsz_697 {
  opacity: 0.5;
  cursor: not-allowed;
}

._progressArea_hipsz_2491 {
  text-align: center;
  margin-top: 2rem;
  padding: 1rem;
  background: rgba(33, 150, 243, 0.2);
  border-radius: 12px;
  border: 2px solid rgba(33, 150, 243, 0.5);
}

._progressTitle_hipsz_2509 {
  font-size: 1.1rem;
  color: white;
  font-weight: bold;
}

/* Menu de atividades - PADRÃO LETTERRECOGNITION */
._activityMenu_hipsz_2523 {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 2rem;
  padding: 0 1rem;
  flex-wrap: wrap;
}

._activityButton_hipsz_2541 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

._activityButton_hipsz_2541:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

._activityButton_hipsz_2541._active_hipsz_2577 {
  background: var(--success-bg) !important;
  border: 2px solid var(--success-border) !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

._activityIcon_hipsz_2589 {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

._activityName_hipsz_2599 {
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  line-height: 1.2;
}

/* =====================================================
   🎯 ESTILOS SEGUINDO PADRÃO MEMORY GAME - OBRIGATÓRIOS
   ===================================================== */

/* Área da pergunta - SEGUINDO PADRÃO MEMORY GAME */
._questionArea_hipsz_2623 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1rem;
}

._questionHeader_hipsz_2641 {
  text-align: center;
  margin-bottom: 2rem;
}

._questionTitle_hipsz_2651 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

._instructions_hipsz_2665 {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1rem;
  line-height: 1.4;
}

/* Removido - estilos duplicados que conflitam com o padrão Memory Game */

/* Removido - usando estilos do padrão Memory Game definidos anteriormente */

._noPieces_hipsz_2687 {
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
  text-align: center;
  padding: 2rem;
  grid-column: 1 / -1;
}

._emptySlot_hipsz_449 {
  color: rgba(255, 255, 255, 0.5);
  font-size: 2rem;
  font-weight: bold;
}

/* Elementos específicos do quebra-cabeça */
._targetEmotion_hipsz_253 {
  font-size: 4rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

._patternSequence_hipsz_1915 {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

._patternPiece_hipsz_1931 {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  transition: all 0.3s ease;
}

._patternPiece_hipsz_1931._missing_hipsz_1951 {
  border: 3px dashed var(--warning-border);
  background: var(--warning-bg);
  animation: _pulse_hipsz_1 2s infinite;
}

@keyframes _pulse_hipsz_1 {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes _bounce_hipsz_1 {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

@keyframes _correctPulse_hipsz_1 {
  0%, 100% { transform: translateY(-5px) scale(1.05); }
  50% { transform: translateY(-5px) scale(1.2); }
}

@keyframes _incorrectShake_hipsz_1 {
  0%, 100% { transform: translateY(-5px) translateX(0); }
  25% { transform: translateY(-5px) translateX(-10px); }
  75% { transform: translateY(-5px) translateX(10px); }
}

/* Área de exibição de objetos - SEGUINDO PADRÃO ASSOCIAÇÃO DE IMAGENS */
._objectsDisplay_hipsz_2833 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  margin: 1rem 0;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  min-height: 200px;
}

/* Área do quebra-cabeça - Drag and Drop */
._puzzleBoard_hipsz_1609 {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 2rem;
  margin: 2rem 0;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

._boardTitle_hipsz_1619 {
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  text-align: center;
  margin-bottom: 1.5rem;
}

._puzzleGrid_hipsz_345 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  max-width: 500px;
  margin: 0 auto;
}

._puzzleSlot_hipsz_375 {
  min-height: 120px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

._puzzleSlot_hipsz_375._empty_hipsz_407 {
  border: 2px dashed rgba(255, 255, 255, 0.5);
}

._puzzleSlot_hipsz_375._filled_hipsz_417 {
  border: 3px solid #4CAF50;
  background: rgba(76, 175, 80, 0.2);
}

._puzzleSlot_hipsz_375:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

._placedPiece_hipsz_439 {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 100%;
  height: 100%;
  justify-content: center;
}

._removePieceButton_hipsz_2983 {
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(244, 67, 54, 0.8);
  border: none;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  cursor: pointer;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

._emptySlotText_hipsz_3015 {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  font-style: italic;
}

._piecesTitle_hipsz_325 {
  font-size: 1.1rem;
  font-weight: bold;
  color: white;
  text-align: center;
  margin-bottom: 1rem;
  grid-column: 1 / -1;
}

._piecesGrid_hipsz_625 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  width: 100%;
}

/* Opções de resposta - PADRÃO IMAGE ASSOCIATION EXATO */
._answerOptions_hipsz_3063 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

._answerButton_hipsz_3079 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 1.5rem;
  color: white;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

._answerButton_hipsz_3079:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.25);
}

._answerButton_hipsz_3079._correct_hipsz_529 {
  border-color: var(--success-border);
  background: var(--success-bg);
  animation: _correctPulse_hipsz_1 0.6s ease-in-out;
}

._answerButton_hipsz_3079._incorrect_hipsz_3139 {
  border-color: var(--error-border);
  background: var(--error-bg);
  animation: _incorrectShake_hipsz_1 0.6s ease-in-out;
}

._answerButton_hipsz_3079:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* ========================================
   RESPONSIVIDADE MOBILE FIRST - PADRÃO IMAGE ASSOCIATION
   ======================================== */

/* Tablet */
@media (max-width: 768px) {
  ._activityMenu_hipsz_2523 {
    gap: 0.25rem;
  }

  ._activityButton_hipsz_2541 {
    padding: 0.4rem 0.8rem;
    font-size: 0.7rem;
  }

  ._answerOptions_hipsz_3063 {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 0.75rem;
  }

  ._answerButton_hipsz_3079 {
    padding: 1rem;
    font-size: 0.8rem;
  }

  ._objectsDisplay_hipsz_2833 {
    padding: 1rem;
  }

  ._gameStats_hipsz_857 {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  ._puzzleGrid_hipsz_345 {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 0.5rem;
  }
}

/* Mobile */
@media (max-width: 480px) {
  ._activityMenu_hipsz_2523 {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }

  ._activityButton_hipsz_2541 {
    width: 100%;
    max-width: 250px;
    justify-content: center;
    padding: 0.75rem;
  }

  ._answerOptions_hipsz_3063 {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    max-width: 100%;
  }

  ._answerButton_hipsz_3079 {
    padding: 0.75rem;
    font-size: 0.7rem;
    min-height: 80px;
  }

  ._objectsDisplay_hipsz_2833 {
    padding: 0.5rem;
  }

  ._questionTitle_hipsz_2651 {
    font-size: 1.2rem;
  }

  ._gameStats_hipsz_857 {
    grid-template-columns: 1fr;
    gap: 0.25rem;
  }

  ._statValue_hipsz_981 {
    font-size: 1.2rem;
  }

  ._statLabel_hipsz_1005 {
    font-size: 0.7rem;
  }

  ._puzzleGrid_hipsz_345 {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.25rem;
  }

  ._puzzleSlot_hipsz_375 {
    min-height: 60px;
    padding: 0.5rem;
  }
}

._creativeGrid_hipsz_3361 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
  max-width: 200px;
  margin: 0 auto;
}

._creativeSlot_hipsz_3377 {
  width: 60px;
  height: 60px;
  border: 2px dashed rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

._creativeSlot_hipsz_3377:hover {
  border-color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.1);
}

/* Responsividade Mobile First */
@media (max-width: 768px) {
  ._questionArea_hipsz_2623 {
    padding: 1.5rem;
  }

  ._puzzleGrid_hipsz_345 {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    max-width: 250px;
  }

  ._puzzleSlot_hipsz_375 {
    width: 100px;
    height: 100px;
    font-size: 1.5rem;
  }

  ._optionsContainer_hipsz_3449 {
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
  }

  ._answerOption_hipsz_3063 {
    padding: 0.75rem;
    font-size: 1.5rem;
    min-height: 70px;
  }

  ._patternPiece_hipsz_1931 {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  ._creativeGrid_hipsz_3361 {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.25rem;
    max-width: 150px;
  }

  ._creativeSlot_hipsz_3377 {
    width: 45px;
    height: 45px;
    font-size: 1.2rem;
  }
}