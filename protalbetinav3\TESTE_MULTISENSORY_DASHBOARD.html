<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Teste MultisensoryMetricsCollector - Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 20px;
            border-left: 5px solid #007bff;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .status-card:hover {
            transform: translateY(-5px);
        }
        
        .status-card h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 1.3em;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-active { background-color: #28a745; }
        .status-inactive { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        
        .metrics-display {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        
        .log-info { background-color: rgba(52, 152, 219, 0.2); }
        .log-success { background-color: rgba(46, 204, 113, 0.2); }
        .log-warning { background-color: rgba(241, 196, 15, 0.2); }
        .log-error { background-color: rgba(231, 76, 60, 0.2); }
        
        .sensor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .sensor-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            border: 2px solid #e0e0e0;
            text-align: center;
        }
        
        .sensor-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 Teste MultisensoryMetricsCollector</h1>
            <p>Verificação completa do sistema de métricas multissensoriais</p>
        </div>
        
        <div class="status-grid">
            <div class="status-card">
                <h3><span class="status-indicator status-warning" id="collector-status"></span>MultisensoryMetricsCollector</h3>
                <p>Status: <span id="collector-text">Verificando...</span></p>
                <div class="metrics-display" id="collector-metrics">Aguardando inicialização...</div>
            </div>
            
            <div class="status-card">
                <h3><span class="status-indicator status-warning" id="dashboard-status"></span>Dashboard Login</h3>
                <p>Status: <span id="dashboard-text">Verificando...</span></p>
                <div class="metrics-display" id="dashboard-info">Aguardando verificação...</div>
            </div>
            
            <div class="status-card">
                <h3><span class="status-indicator status-warning" id="sensors-status"></span>Sensores Disponíveis</h3>
                <p>Status: <span id="sensors-text">Verificando...</span></p>
                <div class="metrics-display" id="sensors-info">Aguardando verificação...</div>
            </div>
            
            <div class="status-card">
                <h3><span class="status-indicator status-warning" id="orchestrator-status"></span>SystemOrchestrator</h3>
                <p>Status: <span id="orchestrator-text">Verificando...</span></p>
                <div class="metrics-display" id="orchestrator-info">Aguardando verificação...</div>
            </div>
        </div>
        
        <div class="button-group">
            <button class="btn btn-primary" onclick="initializeCollector()">🚀 Inicializar Coletor</button>
            <button class="btn btn-success" onclick="startCollection()">▶️ Iniciar Coleta</button>
            <button class="btn btn-warning" onclick="getCurrentMetrics()">📊 Obter Métricas</button>
            <button class="btn btn-danger" onclick="stopCollection()">⏹️ Parar Coleta</button>
            <button class="btn btn-primary" onclick="checkDashboardLogin(); checkAvailableSensors();">🔄 Recarregar Status</button>
        </div>
        
        <div class="sensor-grid" id="sensor-readings">
            <!-- Sensores serão adicionados dinamicamente -->
        </div>
        
        <div class="log-container">
            <h3>📋 Log de Atividades</h3>
            <div id="log-output"></div>
        </div>
    </div>

    <script>
        let multisensoryCollector = null;
        let systemOrchestrator = null;
        let collectionInterval = null;
        
        // Função para adicionar logs
        function addLog(message, type = 'info') {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        // Função para atualizar status
        function updateStatus(elementId, status, text) {
            const indicator = document.getElementById(`${elementId}-status`);
            const textElement = document.getElementById(`${elementId}-text`);
            
            indicator.className = `status-indicator status-${status}`;
            textElement.textContent = text;
        }
        
        // Verificar login no dashboard
        function checkDashboardLogin() {
            try {
                // Verificar múltiplas chaves de autenticação possíveis
                const authToken = localStorage.getItem('authToken');
                const userData = localStorage.getItem('userData');
                const metricsAuth = sessionStorage.getItem('betina_metrics_auth');
                const adminAuth = sessionStorage.getItem('betina_admin_auth');
                const adminSession = localStorage.getItem('betina_admin_session');

                // Debug: mostrar todas as chaves do localStorage e sessionStorage
                const localKeys = Object.keys(localStorage);
                const sessionKeys = Object.keys(sessionStorage);
                addLog(`🔍 localStorage: ${localKeys.join(', ')}`, 'info');
                addLog(`🔍 sessionStorage: ${sessionKeys.join(', ')}`, 'info');

                if (authToken && userData) {
                    try {
                        const parsedUserData = JSON.parse(userData);
                        updateStatus('dashboard', 'active', 'Login Ativo');
                        document.getElementById('dashboard-info').innerHTML = `
                            <strong>Usuário:</strong> ${parsedUserData.email || parsedUserData.username || 'N/A'}<br>
                            <strong>Token:</strong> ${authToken.substring(0, 20)}...<br>
                            <strong>Status:</strong> Autenticado<br>
                            <strong>Tipo:</strong> Dashboard Principal
                        `;
                        addLog('✅ Login no dashboard detectado - métricas serão salvas', 'success');
                        return true;
                    } catch (parseError) {
                        addLog(`⚠️ Erro ao parsear userData: ${parseError.message}`, 'warning');
                    }
                }

                // Verificar autenticação de métricas
                if (metricsAuth === 'authenticated') {
                    updateStatus('dashboard', 'active', 'Login Métricas');
                    document.getElementById('dashboard-info').innerHTML = `
                        <strong>Usuário:</strong> <EMAIL><br>
                        <strong>Tipo:</strong> Dashboard de Métricas<br>
                        <strong>Status:</strong> Autenticado<br>
                        <strong>SessionStorage:</strong> betina_metrics_auth
                    `;
                    addLog('✅ Login de métricas detectado - métricas serão salvas', 'success');
                    return true;
                }

                // Verificar autenticação admin
                if (adminAuth === 'authenticated' || adminSession) {
                    updateStatus('dashboard', 'active', 'Login Admin');
                    document.getElementById('dashboard-info').innerHTML = `
                        <strong>Usuário:</strong> <EMAIL><br>
                        <strong>Tipo:</strong> Dashboard Admin<br>
                        <strong>Status:</strong> Autenticado<br>
                        <strong>SessionStorage:</strong> betina_admin_auth
                    `;
                    addLog('✅ Login admin detectado - métricas serão salvas', 'success');
                    return true;
                }

                // Nenhuma autenticação encontrada
                updateStatus('dashboard', 'inactive', 'Sem Login');
                document.getElementById('dashboard-info').innerHTML = `
                    <strong>Status:</strong> Não autenticado<br>
                    <strong>Modo:</strong> Gratuito<br>
                    <strong>Métricas:</strong> Não serão salvas<br>
                    <strong>localStorage:</strong> ${localKeys.length} chaves<br>
                    <strong>sessionStorage:</strong> ${sessionKeys.length} chaves<br>
                    <small>authToken: ${!!authToken}, userData: ${!!userData}</small><br>
                    <small>metricsAuth: ${!!metricsAuth}, adminAuth: ${!!adminAuth}</small>
                `;
                addLog('🚫 Nenhum login no dashboard - modo gratuito', 'warning');
                addLog(`📋 Debug - authToken: ${!!authToken}, userData: ${!!userData}, metricsAuth: ${!!metricsAuth}, adminAuth: ${!!adminAuth}`, 'info');
                return false;

            } catch (error) {
                updateStatus('dashboard', 'inactive', 'Erro');
                addLog(`❌ Erro ao verificar login: ${error.message}`, 'error');
                return false;
            }
        }
        
        // Verificar sensores disponíveis
        function checkAvailableSensors() {
            const sensors = [];
            let sensorInfo = '';
            
            // Verificar DeviceMotionEvent (acelerômetro/giroscópio)
            if (typeof DeviceMotionEvent !== 'undefined') {
                sensors.push('DeviceMotion');
                sensorInfo += '✅ DeviceMotion (Acelerômetro/Giroscópio)<br>';
            }
            
            // Verificar DeviceOrientationEvent
            if (typeof DeviceOrientationEvent !== 'undefined') {
                sensors.push('DeviceOrientation');
                sensorInfo += '✅ DeviceOrientation<br>';
            }
            
            // Verificar Geolocation
            if (navigator.geolocation) {
                sensors.push('Geolocation');
                sensorInfo += '✅ Geolocation<br>';
            }
            
            // Verificar Touch Events
            if ('ontouchstart' in window) {
                sensors.push('Touch');
                sensorInfo += '✅ Touch Events<br>';
            }
            
            if (sensors.length > 0) {
                updateStatus('sensors', 'active', `${sensors.length} Sensores`);
                document.getElementById('sensors-info').innerHTML = sensorInfo;
                addLog(`✅ ${sensors.length} sensores disponíveis: ${sensors.join(', ')}`, 'success');
            } else {
                updateStatus('sensors', 'inactive', 'Nenhum Sensor');
                document.getElementById('sensors-info').innerHTML = '❌ Nenhum sensor disponível';
                addLog('❌ Nenhum sensor disponível no dispositivo', 'error');
            }
            
            return sensors;
        }
        
        // Inicializar coletor
        async function initializeCollector() {
            try {
                addLog('🚀 Inicializando MultisensoryMetricsCollector...', 'info');
                
                // Importar o coletor
                const { default: MultisensoryMetricsCollector } = await import('./src/api/services/multisensoryAnalysis/multisensoryMetrics.js');
                
                // Criar instância
                multisensoryCollector = new MultisensoryMetricsCollector();
                
                updateStatus('collector', 'active', 'Inicializado');
                document.getElementById('collector-metrics').innerHTML = `
                    <strong>Instância:</strong> Criada<br>
                    <strong>Sessão ID:</strong> ${multisensoryCollector.sessionId || 'N/A'}<br>
                    <strong>Status:</strong> Pronto para coleta
                `;
                
                addLog('✅ MultisensoryMetricsCollector inicializado com sucesso', 'success');
                
                // Verificar SystemOrchestrator
                try {
                    const { getSystemOrchestrator } = await import('./src/api/services/core/SystemOrchestrator.js');
                    systemOrchestrator = await getSystemOrchestrator();
                    
                    updateStatus('orchestrator', 'active', 'Conectado');
                    document.getElementById('orchestrator-info').innerHTML = `
                        <strong>Status:</strong> Ativo<br>
                        <strong>Multisensory:</strong> ${systemOrchestrator.therapeuticSystems?.multisensoryCollector ? 'Integrado' : 'Não integrado'}<br>
                        <strong>Sessões:</strong> ${systemOrchestrator.sessionData?.size || 0}
                    `;
                    
                    addLog('✅ SystemOrchestrator conectado', 'success');
                } catch (orchestratorError) {
                    updateStatus('orchestrator', 'warning', 'Erro');
                    addLog(`⚠️ Erro ao conectar SystemOrchestrator: ${orchestratorError.message}`, 'warning');
                }
                
            } catch (error) {
                updateStatus('collector', 'inactive', 'Erro');
                addLog(`❌ Erro ao inicializar coletor: ${error.message}`, 'error');
            }
        }
        
        // Iniciar coleta
        async function startCollection() {
            if (!multisensoryCollector) {
                addLog('❌ Coletor não inicializado. Clique em "Inicializar Coletor" primeiro.', 'error');
                return;
            }

            try {
                addLog('▶️ Iniciando coleta de métricas multissensoriais...', 'info');

                // Gerar sessionId e userId para o teste
                const sessionId = `test_session_${Date.now()}`;
                const userId = 'test_user_' + Math.random().toString(36).substr(2, 9);

                addLog(`📋 SessionId: ${sessionId}`, 'info');
                addLog(`👤 UserId: ${userId}`, 'info');

                const result = await multisensoryCollector.startMetricsCollection(sessionId, userId, {
                    enableAllSensors: true,
                    collectNeurodivergencePatterns: true,
                    realTimeAnalysis: true
                });

                if (result.success) {
                    addLog('✅ Coleta iniciada com sucesso', 'success');

                    // Atualizar display do coletor
                    document.getElementById('collector-metrics').innerHTML = `
                        <strong>SessionId:</strong> ${sessionId}<br>
                        <strong>UserId:</strong> ${userId}<br>
                        <strong>Status:</strong> Coletando dados<br>
                        <strong>Sensores:</strong> ${result.activeSensors || 'N/A'}
                    `;

                    // Iniciar atualização em tempo real
                    collectionInterval = setInterval(async () => {
                        const currentMetrics = await multisensoryCollector.getCurrentMetrics();
                        if (currentMetrics) {
                            updateSensorReadings(currentMetrics);
                        }
                    }, 1000);

                } else {
                    addLog(`❌ Erro ao iniciar coleta: ${result.error}`, 'error');
                }
            } catch (error) {
                addLog(`❌ Erro ao iniciar coleta: ${error.message}`, 'error');
            }
        }
        
        // Obter métricas atuais
        async function getCurrentMetrics() {
            if (!multisensoryCollector) {
                addLog('❌ Coletor não inicializado.', 'error');
                return;
            }
            
            try {
                addLog('📊 Obtendo métricas atuais...', 'info');
                
                const metrics = await multisensoryCollector.getCurrentMetrics();
                
                if (metrics) {
                    addLog('✅ Métricas obtidas com sucesso', 'success');
                    console.log('Métricas atuais:', metrics);
                    
                    document.getElementById('collector-metrics').innerHTML = `
                        <strong>Timestamp:</strong> ${new Date(metrics.timestamp).toLocaleTimeString()}<br>
                        <strong>Sensores:</strong> ${Object.keys(metrics.mobileSensors || {}).length}<br>
                        <strong>Padrões:</strong> ${Object.keys(metrics.neurodivergencePatterns || {}).length}
                    `;
                } else {
                    addLog('⚠️ Nenhuma métrica disponível (coleta não iniciada)', 'warning');
                }
            } catch (error) {
                addLog(`❌ Erro ao obter métricas: ${error.message}`, 'error');
            }
        }
        
        // Parar coleta
        async function stopCollection() {
            if (!multisensoryCollector) {
                addLog('❌ Coletor não inicializado.', 'error');
                return;
            }
            
            try {
                addLog('⏹️ Parando coleta de métricas...', 'info');
                
                const result = await multisensoryCollector.stopMetricsCollection();
                
                if (result.success) {
                    addLog(`✅ Coleta parada. Total de pontos: ${result.totalDataPoints}`, 'success');
                    console.log('Relatório final:', result.report);
                    
                    // Parar atualização em tempo real
                    if (collectionInterval) {
                        clearInterval(collectionInterval);
                        collectionInterval = null;
                    }
                } else {
                    addLog(`❌ Erro ao parar coleta: ${result.error}`, 'error');
                }
            } catch (error) {
                addLog(`❌ Erro ao parar coleta: ${error.message}`, 'error');
            }
        }
        
        // Atualizar leituras dos sensores
        function updateSensorReadings(metrics) {
            const sensorGrid = document.getElementById('sensor-readings');
            sensorGrid.innerHTML = '';
            
            if (metrics.mobileSensors) {
                Object.entries(metrics.mobileSensors).forEach(([sensorType, data]) => {
                    const sensorCard = document.createElement('div');
                    sensorCard.className = 'sensor-card';
                    
                    let displayValue = 'N/A';
                    if (typeof data === 'object' && data !== null) {
                        if (data.x !== undefined) {
                            displayValue = `X: ${data.x.toFixed(2)}, Y: ${data.y.toFixed(2)}, Z: ${data.z.toFixed(2)}`;
                        } else if (data.value !== undefined) {
                            displayValue = data.value.toFixed(2);
                        }
                    } else if (typeof data === 'number') {
                        displayValue = data.toFixed(2);
                    }
                    
                    sensorCard.innerHTML = `
                        <h4>${sensorType}</h4>
                        <div class="sensor-value">${displayValue}</div>
                        <small>Atualizado: ${new Date().toLocaleTimeString()}</small>
                    `;
                    
                    sensorGrid.appendChild(sensorCard);
                });
            }
        }
        
        // Inicialização automática
        window.addEventListener('load', () => {
            addLog('🌟 Sistema de teste carregado', 'info');
            checkDashboardLogin();
            checkAvailableSensors();
        });
    </script>
</body>
</html>
