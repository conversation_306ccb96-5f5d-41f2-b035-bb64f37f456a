/**
 * @file MusicalSequence.module.css
 * @description Estilos modulares para o Jogo de Sequência Musical - Pa<PERSON><PERSON>egante Unificado
 * @version 3.1.0
 */

/* Variáveis CSS para consistência e reutilização */
:root {
  --card-background: rgba(255, 255, 255, 0.1);
  --card-border: 1px solid rgba(255, 255, 255, 0.2);
  --card-blur: blur(10px);
  --success-bg: rgba(76, 175, 80, 0.3);
  --success-border: rgba(76, 175, 80, 0.5);
  --error-bg: rgba(244, 67, 54, 0.3);
  --error-border: rgba(244, 67, 54, 0.5);
  --primary-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Container principal do MusicalSequence */
._musicalSequenceGame_1sa4m_41 {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Conteúdo do jogo */
._gameContent_1sa4m_63 {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Header do jogo */
._gameHeader_1sa4m_83 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 3rem 1rem 1rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  position: relative;
  min-height: 70px;
}

._gameTitle_1sa4m_111 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

._activitySubtitle_1sa4m_135 {
  font-size: 0.7rem;
  opacity: 0.8;
  margin-top: 0.25rem;
  background: var(--card-background);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: var(--card-border);
}

/* Botões TTS */
._headerTtsButton_1sa4m_157 {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: white;
  z-index: 10;
}

._headerTtsButton_1sa4m_157:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

._headerTtsButton_1sa4m_157._ttsActive_1sa4m_205 {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.5);
}

/* Estatísticas do jogo */
._gameStats_1sa4m_217 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

._statCard_1sa4m_231 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

._statCard_1sa4m_231::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #E91E63);
  opacity: 0.7;
}

._statValue_1sa4m_275 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: white;
}

._statLabel_1sa4m_289 {
  font-size: 0.8rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.8);
}

/* ===== ELEGANTE SISTEMA DE CARDS ===== */

/* Área da pergunta - Padrão ImageAssociation */
._questionArea_1sa4m_307 {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2.5rem;
  margin-bottom: 2rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-height: 140px;
}

._questionHeader_1sa4m_331 {
  margin-bottom: 2rem;
}

._questionTitle_1sa4m_339 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1.2rem;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  line-height: 1.3;
}

._repeatButton_1sa4m_365 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  padding: 0.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

._repeatButton_1sa4m_365:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

/* Display dos objetos principais - Padrão ImageAssociation */
._objectsDisplay_1sa4m_399 {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2.5rem;
  flex-wrap: wrap;
  min-height: 180px;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Container dos slots da sequência */
._sequenceSlots_1sa4m_429 {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

._countingObject_1sa4m_447 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  min-width: 140px;
  min-height: 120px;
}

._countingObject_1sa4m_447::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  animation: _shimmer_1sa4m_1 3s ease-in-out infinite;
}

._countingObject_1sa4m_447:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.2);
}

/* Animações */
@keyframes _shimmer_1sa4m_1 {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

@keyframes _pulse_1sa4m_1 {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
}

@keyframes _bounceIn_1sa4m_1 {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); opacity: 1; }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); }
}

/* Cards de opções de resposta */
._answerOptions_1sa4m_563 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

/* Layout específico para Altura Musical - Mobile First */
._pitchComparisonContainer_1sa4m_579 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  padding: 1rem;
  width: 100%;
}

._pitchCard_1sa4m_599 {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 1.5rem;
  text-align: center;
  width: 100%;
  max-width: 280px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

._pitchCard_1sa4m_599:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

._pitchCard_1sa4m_599[data-type="reference"] {
  border-color: rgba(76, 175, 80, 0.6);
  background: rgba(76, 175, 80, 0.1);
}

._pitchCard_1sa4m_599[data-type="test"] {
  border-color: rgba(255, 152, 0, 0.6);
  background: rgba(255, 152, 0, 0.1);
}

._pitchIcon_1sa4m_657 {
  font-size: 3rem;
  margin-bottom: 1rem;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

._pitchTitle_1sa4m_669 {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 0.8rem;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

._pitchFrequency_1sa4m_685 {
  font-size: 1rem;
  margin-bottom: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
}

._pitchButton_1sa4m_699 {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 0.8rem 1.2rem;
  color: white;
  font-size: 0.9rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 180px;
}

._pitchButton_1sa4m_699:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

._pitchButton_1sa4m_699:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

._referenceButton_1sa4m_755 {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.5);
}

._referenceButton_1sa4m_755:hover:not(:disabled) {
  background: rgba(76, 175, 80, 0.5);
}

._testButton_1sa4m_773 {
  background: rgba(255, 152, 0, 0.3);
  border-color: rgba(255, 152, 0, 0.5);
}

._testButton_1sa4m_773:hover:not(:disabled) {
  background: rgba(255, 152, 0, 0.5);
}

._vsIndicator_1sa4m_791 {
  font-size: 3rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: _pulse_1sa4m_1 2s ease-in-out infinite;
}

/* Botões de resposta específicos para Altura Musical */
._answerIcon_1sa4m_809 {
  font-size: 2.5rem;
  margin-bottom: 0.8rem;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

._answerText_1sa4m_821 {
  font-size: 1.1rem;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

._higherButton_1sa4m_833 {
  background: rgba(244, 67, 54, 0.3);
  border-color: rgba(244, 67, 54, 0.5);
}

._higherButton_1sa4m_833:hover:not(:disabled) {
  background: rgba(244, 67, 54, 0.5);
}

._sameButton_1sa4m_851 {
  background: rgba(255, 193, 7, 0.3);
  border-color: rgba(255, 193, 7, 0.5);
}

._sameButton_1sa4m_851:hover:not(:disabled) {
  background: rgba(255, 193, 7, 0.5);
}

._lowerButton_1sa4m_869 {
  background: rgba(33, 150, 243, 0.3);
  border-color: rgba(33, 150, 243, 0.5);
}

._lowerButton_1sa4m_869:hover:not(:disabled) {
  background: rgba(33, 150, 243, 0.5);
}

/* Feedback Container para Altura Musical */
._feedbackContainer_1sa4m_889 {
  text-align: center;
  margin-top: 2rem;
  padding: 2rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

._correctFeedback_1sa4m_911 {
  background: rgba(76, 175, 80, 0.2);
  border-color: rgba(76, 175, 80, 0.5);
}

._incorrectFeedback_1sa4m_921 {
  background: rgba(244, 67, 54, 0.2);
  border-color: rgba(244, 67, 54, 0.5);
}

._feedbackIcon_1sa4m_931 {
  font-size: 3rem;
  margin-bottom: 1rem;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

._feedbackTitle_1sa4m_943 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

._feedbackText_1sa4m_959 {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Layout específico para Predições de Padrões - Mobile First */
._patternContainer_1sa4m_975 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  width: 100%;
}

._patternHeader_1sa4m_993 {
  text-align: center;
  margin-bottom: 1rem;
}

._patternIcon_1sa4m_1003 {
  font-size: 3rem;
  margin-bottom: 1rem;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

._patternTitle_1sa4m_1015 {
  font-size: 1.3rem;
  font-weight: bold;
  margin-bottom: 0.8rem;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

._patternDescription_1sa4m_1031 {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  margin-bottom: 0;
}

._patternSequence_1sa4m_1045 {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 120px;
}

._patternItem_1sa4m_1071 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  transition: all 0.3s ease;
  min-width: 80px;
}

._patternItem_1sa4m_1071[data-missing="true"] {
  border-color: rgba(255, 193, 7, 0.6);
  background: rgba(255, 193, 7, 0.1);
  animation: _pulse_1sa4m_1 2s ease-in-out infinite;
}

._patternEmoji_1sa4m_1111 {
  font-size: 2.5rem;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

._patternName_1sa4m_1121 {
  font-size: 0.9rem;
  font-weight: bold;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  text-align: center;
}

._patternHint_1sa4m_1137 {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  padding: 0.8rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

._roundProgress_1sa4m_1157 {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: 0.5rem;
  display: inline-block;
}

._elegantCard_1sa4m_1181 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

._elegantCard_1sa4m_1181::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

._elegantCard_1sa4m_1181:hover::before {
  animation: _shimmer_1sa4m_1 1.5s ease-in-out;
  opacity: 1;
}

._elegantCard_1sa4m_1181:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

._elegantCard_1sa4m_1181:active {
  transform: translateY(-4px) scale(0.98);
  transition: all 0.1s ease;
}

/* Estados especiais dos cards */
._elegantCard_1sa4m_1181._selected_1sa4m_1283 {
  background: var(--success-bg);
  border-color: var(--success-border);
  box-shadow: 0 8px 32px rgba(76, 175, 80, 0.3);
}

._elegantCard_1sa4m_1181._correct_1sa4m_911 {
  background: var(--success-bg);
  border-color: var(--success-border);
  animation: _bounceIn_1sa4m_1 0.6s ease;
}

._elegantCard_1sa4m_1181._incorrect_1sa4m_921 {
  background: var(--error-bg);
  border-color: var(--error-border);
  animation: _pulse_1sa4m_1 0.5s ease 2;
}

/* Compatibilidade com sistema antigo */
._answerButton_1sa4m_1321 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

._answerButton_1sa4m_1321::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

._answerButton_1sa4m_1321:hover::before {
  animation: _shimmer_1sa4m_1 1.5s ease-in-out;
  opacity: 1;
}

._answerButton_1sa4m_1321:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

._answerButton_1sa4m_1321:active {
  transform: translateY(-4px) scale(0.98);
  transition: all 0.1s ease;
}

._optionNumber_1sa4m_1423 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

/* Feedback visual */
._feedbackContainer_1sa4m_889 {
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 12px;
  text-align: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

._feedbackContainer_1sa4m_889._correct_1sa4m_911 {
  background: var(--success-bg);
  border: 1px solid var(--success-border);
  color: white;
  animation: _bounceIn_1sa4m_1 0.6s ease;
}

._feedbackContainer_1sa4m_889._incorrect_1sa4m_921 {
  background: var(--error-bg);
  border: 1px solid var(--error-border);
  color: white;
  animation: _pulse_1sa4m_1 0.5s ease 2;
}

/* Controles do jogo */
._gameControls_1sa4m_1485 {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

._controlButton_1sa4m_1501 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

._controlButton_1sa4m_1501:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

._controlButton_1sa4m_1501:active {
  transform: translateY(0);
  transition: all 0.1s ease;
}

/* TTS Indicators */
._ttsIndicator_1sa4m_1555 {
  font-size: 0.8rem;
  opacity: 0.7;
  margin-top: 0.25rem;
}

/* Responsividade Mobile-First */
@media (max-width: 768px) {
  ._musicalSequenceGame_1sa4m_41 {
    padding: 0.5rem;
  }

  ._gameHeader_1sa4m_83 {
    padding: 0.75rem 2.5rem 0.75rem 0.75rem;
    min-height: 60px;
  }

  ._gameTitle_1sa4m_111 {
    font-size: 1.4rem;
  }

  ._activitySubtitle_1sa4m_135 {
    font-size: 0.65rem;
    padding: 0.2rem 0.5rem;
  }

  ._questionArea_1sa4m_307 {
    padding: 1.5rem;
    border-radius: 16px;
  }

  ._questionTitle_1sa4m_339 {
    font-size: 1.2rem;
  }

  ._objectsDisplay_1sa4m_399 {
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  ._countingObject_1sa4m_447 {
    padding: 1rem;
    font-size: 2rem;
    min-width: 80px;
    min-height: 80px;
  }

  ._answerOptions_1sa4m_563 {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
  }

  /* Responsividade para Altura Musical - Tablet */
  ._pitchComparisonContainer_1sa4m_579 {
    gap: 2rem;
    padding: 1.5rem;
  }

  ._pitchCard_1sa4m_599 {
    min-width: 180px;
    padding: 1.5rem;
  }

  ._pitchIcon_1sa4m_657 {
    font-size: 3.5rem;
  }

  ._pitchTitle_1sa4m_669 {
    font-size: 1.2rem;
  }

  ._pitchButton_1sa4m_699 {
    min-width: 140px;
    padding: 0.8rem 1.2rem;
  }

  ._vsIndicator_1sa4m_791 {
    font-size: 2.5rem;
  }

  ._answerIcon_1sa4m_809 {
    font-size: 2rem;
  }

  /* Responsividade para Predições de Padrões - Tablet */
  ._patternContainer_1sa4m_975 {
    padding: 2rem;
    gap: 2rem;
  }

  ._patternIcon_1sa4m_1003 {
    font-size: 3.5rem;
  }

  ._patternTitle_1sa4m_1015 {
    font-size: 1.4rem;
  }

  ._patternSequence_1sa4m_1045 {
    gap: 1.5rem;
    padding: 2rem;
  }

  ._patternItem_1sa4m_1071 {
    min-width: 90px;
    padding: 1.2rem;
  }

  ._patternEmoji_1sa4m_1111 {
    font-size: 3rem;
  }

  ._patternName_1sa4m_1121 {
    font-size: 1rem;
  }

  ._elegantCard_1sa4m_1181, ._answerButton_1sa4m_1321 {
    padding: 1rem;
    font-size: 1rem;
    min-height: 60px;
  }

  ._optionNumber_1sa4m_1423 {
    font-size: 1.5rem;
  }

  ._gameControls_1sa4m_1485 {
    gap: 0.5rem;
  }

  ._controlButton_1sa4m_1501 {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  ._gameStats_1sa4m_217 {
    grid-template-columns: repeat(2, 1fr);
  }

  ._answerOptions_1sa4m_563 {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
  }

  /* Responsividade para Altura Musical - Mobile */
  ._pitchComparisonContainer_1sa4m_579 {
    flex-direction: column;
    gap: 1.5rem;
    padding: 1rem;
  }

  ._pitchCard_1sa4m_599 {
    min-width: 160px;
    padding: 1.2rem;
  }

  ._pitchIcon_1sa4m_657 {
    font-size: 3rem;
  }

  ._pitchTitle_1sa4m_669 {
    font-size: 1.1rem;
  }

  ._pitchFrequency_1sa4m_685 {
    font-size: 1rem;
  }

  ._pitchButton_1sa4m_699 {
    min-width: 120px;
    padding: 0.7rem 1rem;
    font-size: 0.9rem;
  }

  ._vsIndicator_1sa4m_791 {
    font-size: 2rem;
    transform: rotate(90deg);
  }

  ._answerIcon_1sa4m_809 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
  }

  ._answerText_1sa4m_821 {
    font-size: 1rem;
  }

  /* Responsividade para Predições de Padrões - Desktop */
  ._patternContainer_1sa4m_975 {
    padding: 2.5rem;
    gap: 2.5rem;
  }

  ._patternIcon_1sa4m_1003 {
    font-size: 4rem;
  }

  ._patternTitle_1sa4m_1015 {
    font-size: 1.5rem;
  }

  ._patternSequence_1sa4m_1045 {
    gap: 2rem;
    padding: 2.5rem;
    flex-wrap: nowrap;
  }

  ._patternItem_1sa4m_1071 {
    min-width: 100px;
    padding: 1.5rem;
  }

  ._patternEmoji_1sa4m_1111 {
    font-size: 3.5rem;
  }

  ._patternName_1sa4m_1121 {
    font-size: 1.1rem;
  }

  ._roundProgress_1sa4m_1157 {
    font-size: 1rem;
  }

  ._gameControls_1sa4m_1485 {
    flex-direction: column;
    align-items: center;
  }

  ._controlButton_1sa4m_1501 {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }
}

/* Media query adicional para celulares em landscape e telas pequenas */
@media (max-width: 640px) and (orientation: landscape) {
  ._answerOptions_1sa4m_563 {
    grid-template-columns: repeat(6, 1fr);
    gap: 0.5rem;
  }
  
  ._elegantCard_1sa4m_1181, ._answerButton_1sa4m_1321 {
    padding: 0.75rem;
    font-size: 0.8rem;
    min-height: 50px;
  }
}

/* Estados de acessibilidade */
._musicalSequenceGame_1sa4m_41._high-contrast_1sa4m_2063 {
  --card-background: rgba(0, 0, 0, 0.8);
  --card-border: 2px solid rgba(255, 255, 255, 0.8);
}

._musicalSequenceGame_1sa4m_41._reduced-motion_1sa4m_2073 * {
  animation: none !important;
  transition: none !important;
}

/* Tamanhos de fonte personalizáveis */
._musicalSequenceGame_1sa4m_41[data-font-size="small"] {
  font-size: 0.875rem;
}

._musicalSequenceGame_1sa4m_41[data-font-size="large"] {
  font-size: 1.25rem;
}

/* Temas de cor */
._musicalSequenceGame_1sa4m_41[data-theme="dark"] {
  --gradient-bg: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

._musicalSequenceGame_1sa4m_41[data-theme="light"] {
  --gradient-bg: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: #2d3436;
}
._musicalSequenceGame_1sa4m_41 {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Container principal - genérico para outros jogos */
._gameContainer_1sa4m_2141 {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Conteúdo do jogo */
._gameContent_1sa4m_63 {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Header do jogo */
._gameHeader_1sa4m_83 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 3rem 1rem 1rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  position: relative;
  min-height: 70px;
}

._gameTitle_1sa4m_111 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

._activitySubtitle_1sa4m_135 {
  font-size: 0.7rem;
  opacity: 0.8;
  margin-top: 0.25rem;
  background: var(--card-background);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: var(--card-border);
}

/* Estatísticas */
._gameStats_1sa4m_217 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

._statCard_1sa4m_231 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

._statCard_1sa4m_231::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #E91E63);
  opacity: 0.7;
}

._statValue_1sa4m_275 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: white;
}

._statLabel_1sa4m_289 {
  font-size: 0.8rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.8);
}

/* Área da pergunta */
._questionArea_1sa4m_307 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  text-align: center;
}

._questionTitle_1sa4m_339 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: white;
}

._objectsDisplay_1sa4m_399 {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
  margin: 2rem 0;
  min-height: 150px;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 2px dashed rgba(255, 255, 255, 0.3);
}

._countingObject_1sa4m_447 {
  font-size: 3rem;
  transition: all 0.3s ease;
  animation: _objectAppear_1sa4m_1 0.5s ease-out;
  cursor: default;
  user-select: none;
}

._countingObject_1sa4m_447:hover {
  transform: scale(1.1);
}

@keyframes _objectAppear_1sa4m_1 {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-10deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* Opções de resposta */
._answerOptions_1sa4m_563 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

._answerButton_1sa4m_1321 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 1.5rem;
  color: white;
  font-size: 2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

._answerButton_1sa4m_1321:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.25);
}

._answerButton_1sa4m_1321._correct_1sa4m_911 {
  border-color: var(--success-border);
  background: var(--success-bg);
  animation: _correctPulse_1sa4m_1 0.6s ease-in-out;
}

._answerButton_1sa4m_1321._incorrect_1sa4m_921 {
  border-color: var(--error-border);
  background: var(--error-bg);
  animation: _incorrectShake_1sa4m_1 0.6s ease-in-out;
}

._answerButton_1sa4m_1321:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@keyframes _correctPulse_1sa4m_1 {
  0%, 100% { transform: translateY(-5px) scale(1.05); }
  50% { transform: translateY(-5px) scale(1.2); }
}

@keyframes _incorrectShake_1sa4m_1 {
  0%, 100% { transform: translateY(-5px) translateX(0); }
  25% { transform: translateY(-5px) translateX(-10px); }
  75% { transform: translateY(-5px) translateX(10px); }
}

/* Controles do jogo */
._gameControls_1sa4m_1485 {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

._controlButton_1sa4m_1501 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 600;
}

._controlButton_1sa4m_1501:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

._nextButton_1sa4m_2631 {
  background: var(--success-bg);
  border-color: var(--success-border);
}

._nextButton_1sa4m_2631:hover {
  background: rgba(76, 175, 80, 0.4);
}

/* Feedback */
._feedbackMessage_1sa4m_2651 {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 1.5rem 2.5rem;
  border-radius: 16px;
  font-size: 1.3rem;
  font-weight: 700;
  z-index: 1000;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: _messageSlide_1sa4m_1 3s ease-in-out;
}

._feedbackMessage_1sa4m_2651._success_1sa4m_2681 {
  background: var(--success-bg);
  color: white;
}

._feedbackMessage_1sa4m_2651._error_1sa4m_2691 {
  background: var(--error-bg);
  color: white;
}

@keyframes _messageSlide_1sa4m_1 {
  0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
  15% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  85% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}

/* Botões TTS */
._headerTtsButton_1sa4m_157 {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: white;
  z-index: 10;
}

._headerTtsButton_1sa4m_157:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

._headerTtsButton_1sa4m_157:active {
  transform: scale(0.95);
}

._ttsActive_1sa4m_205 {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
}

._ttsInactive_1sa4m_2783 {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
}

._repeatButton_1sa4m_365 {
  background: rgba(74, 144, 226, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 600;
  margin-left: 1rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

._repeatButton_1sa4m_365:hover {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.4);
}

._repeatButton_1sa4m_365:active {
  transform: scale(0.95);
}

._ttsIndicator_1sa4m_1555 {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(74, 144, 226, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 6px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  z-index: 5;
  pointer-events: none;
  transition: all 0.2s ease;
}

._answerButton_1sa4m_1321:hover ._ttsIndicator_1sa4m_1555 {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.1);
}

/* Menu de atividades */
._activityMenu_1sa4m_2893 {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

._activityButton_1sa4m_2909 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

._activityButton_1sa4m_2909:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

._activityButton_1sa4m_2909._active_1sa4m_2947 {
  background: var(--success-bg) !important;
  border: 2px solid var(--success-border) !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

._activeIndicator_1sa4m_2959 {
  color: var(--success-color);
  font-size: 0.8rem;
  margin-left: 0.5rem;
  animation: _pulse_1sa4m_1 1.5s infinite;
}

._activityIcon_1sa4m_2973 {
  font-size: 1.2rem;
  margin-right: 0.5rem;
}

._activityName_1sa4m_2983 {
  font-size: 0.9rem;
  font-weight: 500;
}

@keyframes _pulse_1sa4m_1 {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Atividade de Som */
._soundActivity_1sa4m_3005 {
  text-align: center;
}

._soundIndicator_1sa4m_3013 {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: _soundPulse_1sa4m_1 2s ease-in-out infinite;
}

@keyframes _soundPulse_1sa4m_1 {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.1); opacity: 1; }
}

._soundButton_1sa4m_3035 {
  background: rgba(74, 144, 226, 0.3);
  border: 2px solid rgba(74, 144, 226, 0.5);
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  margin: 1rem;
}

._soundButton_1sa4m_3035:hover {
  background: rgba(74, 144, 226, 0.5);
  transform: scale(1.05);
}

/* Atividade de Estimativa */
._estimationDisplay_1sa4m_3073 {
  position: relative;
}

._estimationObjects_1sa4m_3081 {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  opacity: 0.9;
}

._estimationObject_1sa4m_3081 {
  font-size: 1.5rem;
  transform: rotate(var(--rotation));
  transition: opacity 0.3s ease;
}

._estimationTip_1sa4m_3109 {
  text-align: center;
  margin-top: 1rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  background: rgba(255, 193, 7, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border-left: 4px solid #FFC107;
}

/* Atividade de Sequência */
._sequenceDisplay_1sa4m_3133 {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-wrap: wrap;
}

._sequenceNumber_1sa4m_3151 {
  background: var(--card-background);
  padding: 1rem;
  border-radius: 12px;
  min-width: 60px;
  text-align: center;
  color: white;
  font-weight: bold;
  transition: all 0.3s ease;
}

/* Grid de instrumentos musicais */
._instrumentsGrid_1sa4m_3175 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

._instrumentButton_1sa4m_3193 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

._instrumentButton_1sa4m_3193:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.2);
}

._instrumentButton_1sa4m_3193:active {
  transform: translateY(-2px) scale(1.02);
}

._instrumentButton_1sa4m_3193:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

._instrumentButton_1sa4m_3193._playing_1sa4m_3267 {
  background: var(--success-bg);
  border-color: var(--success-border);
  animation: _instrumentPulse_1sa4m_1 0.6s ease-in-out;
  transform: scale(1.1);
}

._instrumentIcon_1sa4m_3281 {
  font-size: 2.5rem;
  margin-bottom: 0.25rem;
}

._instrumentName_1sa4m_3291 {
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
}

@keyframes _instrumentPulse_1sa4m_1 {
  0%, 100% { transform: scale(1.1); }
  50% { transform: scale(1.2); }
}

/* Área principal do jogo */
._gameArea_1sa4m_3315 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
}

/* Controles do jogo */
._gameControls_1sa4m_1485 {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

._controlButton_1sa4m_1501 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

._controlButton_1sa4m_1501:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

._controlButton_1sa4m_1501:active {
  transform: translateY(0);
}

/* =====================================================
   🎵 INTERFACES ESPECÍFICAS PARA CADA ATIVIDADE
   ===================================================== */

/* Interface base para atividades */
._activityInterface_1sa4m_3415 {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

._instructionPanel_1sa4m_3433 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
}

._instructionPanel_1sa4m_3433 h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: white;
}

._instructionPanel_1sa4m_3433 p {
  font-size: 1rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

/* 🔄 REPRODUÇÃO DE SEQUÊNCIA */
._sequenceIndicator_1sa4m_3479 {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin: 1rem 0;
}

._sequenceItem_1sa4m_3493 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  font-size: 2rem;
  min-width: 60px;
  text-align: center;
  transition: all 0.3s ease;
}

._sequenceItem_1sa4m_3493._playing_1sa4m_3267 {
  background: var(--success-bg);
  border-color: var(--success-border);
  transform: scale(1.2);
  animation: _pulse_1sa4m_1 0.6s ease-in-out;
}

._progressArea_1sa4m_3529 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
}

._playerSequence_1sa4m_3547 {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  margin: 1rem 0;
}

._playerItem_1sa4m_3561 {
  background: rgba(76, 175, 80, 0.3);
  border: 2px solid rgba(76, 175, 80, 0.5);
  border-radius: 8px;
  padding: 0.5rem;
  font-size: 1.5rem;
  min-width: 40px;
  text-align: center;
}

/* 🥁 PADRÕES RÍTMICOS */
._rhythmInterface_1sa4m_3583 {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  align-items: center;
}

._rhythmDisplay_1sa4m_3597 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  width: 100%;
}

._rhythmPattern_1sa4m_3617 {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin: 1rem 0;
}

._rhythmBeat_1sa4m_3631 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

._rhythmBeat_1sa4m_3631._active_1sa4m_2947 {
  background: var(--success-bg);
  border-color: var(--success-border);
  transform: scale(1.2);
}

._rhythmControls_1sa4m_3669 {
  display: flex;
  gap: 1rem;
}

._rhythmButton_1sa4m_3679 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

._rhythmButton_1sa4m_3679:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

/* 🎼 COMPLETAR MELODIA */
._melodyInterface_1sa4m_3715 {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

._melodyDisplay_1sa4m_3727 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
}

._melodyPattern_1sa4m_3745 {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin: 1rem 0;
}

._melodyNote_1sa4m_3759 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  font-size: 2rem;
  min-width: 60px;
  text-align: center;
}

._melodyNote_1sa4m_3759._missing_1sa4m_3779 {
  background: rgba(255, 193, 7, 0.3);
  border-color: rgba(255, 193, 7, 0.5);
  animation: _pulse_1sa4m_1 2s infinite;
}

._melodyOptions_1sa4m_3791 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
}

._noteOptions_1sa4m_3809 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

._noteButton_1sa4m_3823 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

._noteButton_1sa4m_3823:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

/* 🎺 RECONHECIMENTO DE INSTRUMENTOS */
._recognitionInterface_1sa4m_3865 {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

._soundDisplay_1sa4m_3877 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
}

._soundIndicator_1sa4m_3013 {
  margin: 2rem 0;
}

._soundWave_1sa4m_3903 {
  font-size: 2rem;
  opacity: 0.6;
  transition: all 0.3s ease;
}

._soundWave_1sa4m_3903._playing_1sa4m_3267 {
  opacity: 1;
  animation: _soundPulse_1sa4m_1 1s ease-in-out infinite;
}

@keyframes _soundPulse_1sa4m_1 {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

._playButton_1sa4m_3935 {
  background: var(--success-bg);
  border: 2px solid var(--success-border);
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

._playButton_1sa4m_3935:hover {
  background: rgba(76, 175, 80, 0.5);
  transform: translateY(-2px);
}

._playButton_1sa4m_3935:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

._instrumentOptions_1sa4m_3979 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
}

._optionsGrid_1sa4m_3997 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

._optionButton_1sa4m_4011 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

._optionButton_1sa4m_4011:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

._optionButton_1sa4m_4011._selected_1sa4m_1283 {
  background: var(--success-bg);
  border-color: var(--success-border);
  transform: scale(1.05);
}

._optionIcon_1sa4m_4063 {
  font-size: 2rem;
}

._optionName_1sa4m_4071 {
  font-size: 0.9rem;
  font-weight: 600;
}

/* 🧠 MEMÓRIA MUSICAL */
._memoryInterface_1sa4m_4083 {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

._memoryChallenge_1sa4m_4095 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
}

._memorySequence_1sa4m_4113 {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
  margin: 1rem 0;
}

._memoryItem_1sa4m_4129 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  min-width: 60px;
  transition: all 0.3s ease;
}

._memoryItem_1sa4m_4129._playing_1sa4m_3267 {
  background: var(--success-bg);
  border-color: var(--success-border);
  transform: scale(1.1);
}

._memoryItem_1sa4m_4129._completed_1sa4m_4167 {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.5);
}

._itemNumber_1sa4m_4177 {
  font-size: 0.8rem;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

._itemIcon_1sa4m_4201 {
  font-size: 1.5rem;
}

._memoryInput_1sa4m_4209 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
}

._memoryProgress_1sa4m_4225 {
  margin-top: 1rem;
  text-align: center;
}

._progressBar_1sa4m_4235 {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  height: 10px;
  margin: 0.5rem 0;
  overflow: hidden;
}

._progressFill_1sa4m_4251 {
  background: linear-gradient(90deg, #4CAF50, #2196F3);
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 10px;
}

/* 🎨 COMPOSIÇÃO CRIATIVA */
._compositionInterface_1sa4m_4267 {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

._compositionArea_1sa4m_4279 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
}

._compositionSequence_1sa4m_4297 {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
  margin: 1rem 0;
  min-height: 80px;
  align-items: center;
}

._compositionNote_1sa4m_4317 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  min-width: 60px;
}

._noteNumber_1sa4m_4343 {
  font-size: 0.8rem;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

._noteIcon_1sa4m_4367 {
  font-size: 1.5rem;
}

._removeNote_1sa4m_4375 {
  position: absolute;
  top: -5px;
  right: -5px;
  background: rgba(244, 67, 54, 0.8);
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  color: white;
  cursor: pointer;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

._emptyComposition_1sa4m_4409 {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  padding: 2rem;
}

._compositionControls_1sa4m_4421 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
}

._compositionActions_1sa4m_4437 {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

._actionButton_1sa4m_4451 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

._actionButton_1sa4m_4451:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

._actionButton_1sa4m_4451:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* =====================================================
   🎵 ANIMAÇÕES PARA FUNCIONALIDADES ESPECÍFICAS
   ===================================================== */

/* Animação de pulse para elementos ativos */
@keyframes _pulse_1sa4m_1 {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Animação de bounce para elementos faltantes */
@keyframes _bounce_1sa4m_1 {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Animação de glow para elementos selecionados */
@keyframes _glow_1sa4m_1 {
  0% {
    box-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
  }
}

/* Estilos para visualizadores de frequência */
._frequencyVisualizer_1sa4m_4591 {
  position: relative;
  height: 60px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  overflow: hidden;
  margin: 1rem 0;
}

._frequencyBar_1sa4m_4609 {
  height: 100%;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}

._frequencyBar_1sa4m_4609._playing_1sa4m_3267 {
  animation: _pulse_1sa4m_1 0.5s infinite;
}

/* Estilos para padrões visuais */
._patternElement_1sa4m_4641 {
  transition: all 0.3s ease;
  border-radius: 16px;
  padding: 1.5rem;
  min-width: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

._patternElement_1sa4m_4641._completed_1sa4m_4167 {
  border: 3px solid rgba(76, 175, 80, 0.6);
  background: rgba(76, 175, 80, 0.2);
}

._patternElement_1sa4m_4641._missing_1sa4m_3779 {
  border: 4px dashed rgba(255, 193, 7, 0.8);
  background: rgba(255, 193, 7, 0.3);
  animation: _pulse_1sa4m_1 2s infinite;
  box-shadow: 0 0 20px rgba(255, 193, 7, 0.5);
}

/* Estilos para botões de gravação */
._recordingButton_1sa4m_4687 {
  background: rgba(244, 67, 54, 0.3);
  border: 2px solid rgba(244, 67, 54, 0.5);
  animation: _pulse_1sa4m_1 1s infinite;
}

._recordingButton_1sa4m_4687:hover {
  background: rgba(244, 67, 54, 0.5);
}

/* Estilos para elementos selecionados */
._selectedElement_1sa4m_4709 {
  border: 3px solid rgba(255, 193, 7, 0.8) !important;
  background: rgba(255, 193, 7, 0.2) !important;
  transform: scale(1.05);
  animation: _glow_1sa4m_1 2s infinite;
}

._sequenceNumber_1sa4m_3151:hover {
  transform: scale(1.05);
}

._sequenceArrow_1sa4m_4731 {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.5rem;
}

._sequenceMissing_1sa4m_4741 {
  background: rgba(255, 255, 80, 0.3) !important;
  border: 2px dashed rgba(255, 255, 255, 0.5);
  animation: _missingPulse_1sa4m_1 2s ease-in-out infinite;
}

@keyframes _missingPulse_1sa4m_1 {
  0%, 100% { box-shadow: 0 0 0 rgba(255, 255, 80, 0.3); }
  50% { box-shadow: 0 0 20px rgba(255, 255, 80, 0.6); }
}

/* Atividade de Comparação */
._comparisonDisplay_1sa4m_4765 {
  display: flex;
  gap: 3rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

._comparisonGroup_1sa4m_4781 {
  text-align: center;
  background: var(--card-background);
  padding: 1.5rem;
  border-radius: 16px;
  transition: all 0.3s ease;
}

._comparisonGroup_1sa4m_4781:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-5px);
}

._comparisonObjects_1sa4m_4807 {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  max-width: 150px;
  margin-bottom: 1rem;
}

._comparisonNumber_1sa4m_4825 {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  background: var(--card-background);
  padding: 0.5rem 1rem;
  border-radius: 8px;
}

/* Atividade de Padrões */
._patternDisplay_1sa4m_4845 {
  text-align: center;
}

._patternDescription_1sa4m_1031 {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1rem;
  background: rgba(156, 39, 176, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border-left: 4px solid #9C27B0;
}

._patternSequence_1sa4m_1045 {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-wrap: wrap;
}

._patternNumber_1sa4m_4891 {
  background: linear-gradient(135deg, rgba(156, 39, 176, 0.3), rgba(156, 39, 176, 0.1));
  padding: 1rem;
  border-radius: 12px;
  min-width: 60px;
  text-align: center;
  color: white;
  font-weight: bold;
  border: 1px solid rgba(156, 39, 176, 0.4);
  transition: all 0.3s ease;
}

._patternNumber_1sa4m_4891:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(156, 39, 176, 0.3);
}

/* Responsividade */
@media (max-width: 768px) {
  ._activityMenu_1sa4m_2893 {
    gap: 0.25rem;
  }

  ._activityButton_1sa4m_2909 {
    padding: 0.4rem 0.8rem;
    font-size: 0.7rem;
  }

  ._comparisonDisplay_1sa4m_4765 {
    gap: 1.5rem;
  }

  ._sequenceDisplay_1sa4m_3133,
  ._patternSequence_1sa4m_1045 {
    gap: 0.5rem;
    font-size: 1.5rem;
  }

  ._sequenceNumber_1sa4m_3151,
  ._patternNumber_1sa4m_4891 {
    padding: 0.75rem;
    min-width: 50px;
  }

  ._gameStats_1sa4m_217 {
    grid-template-columns: repeat(2, 1fr);
  }

  ._instrumentsGrid_1sa4m_3175 {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  ._instrumentButton_1sa4m_3193 {
    padding: 1rem;
    min-height: 80px;
  }

  ._instrumentIcon_1sa4m_3281 {
    font-size: 2rem;
  }

  ._instrumentName_1sa4m_3291 {
    font-size: 0.8rem;
  }

  /* Responsividade para interfaces de atividades */
  ._sequenceIndicator_1sa4m_3479,
  ._playerSequence_1sa4m_3547,
  ._rhythmPattern_1sa4m_3617,
  ._melodyPattern_1sa4m_3745,
  ._memorySequence_1sa4m_4113,
  ._compositionSequence_1sa4m_4297 {
    gap: 0.5rem;
  }

  ._optionsGrid_1sa4m_3997,
  ._noteOptions_1sa4m_3809 {
    grid-template-columns: repeat(2, 1fr);
  }

  ._rhythmControls_1sa4m_3669,
  ._compositionActions_1sa4m_4437 {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  ._activityMenu_1sa4m_2893 {
    flex-direction: column;
    align-items: center;
  }

  ._activityButton_1sa4m_2909 {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  ._comparisonDisplay_1sa4m_4765 {
    flex-direction: column;
    gap: 1rem;
  }

  ._sequenceDisplay_1sa4m_3133,
  ._patternSequence_1sa4m_1045 {
    flex-direction: column;
    gap: 0.5rem;
  }

  ._sequenceArrow_1sa4m_4731 {
    transform: rotate(90deg);
  }

  ._instrumentsGrid_1sa4m_3175 {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    max-width: 300px;
  }

  ._instrumentButton_1sa4m_3193 {
    padding: 0.75rem;
    min-height: 70px;
  }

  ._instrumentIcon_1sa4m_3281 {
    font-size: 1.8rem;
  }

  ._instrumentName_1sa4m_3291 {
    font-size: 0.75rem;
  }

  ._gameControls_1sa4m_1485 {
    flex-direction: column;
    gap: 0.5rem;
  }

  ._controlButton_1sa4m_1501 {
    width: 100%;
    max-width: 250px;
  }

  /* Responsividade mobile para interfaces de atividades */
  ._activityInterface_1sa4m_3415 {
    gap: 1rem;
  }

  ._instructionPanel_1sa4m_3433,
  ._rhythmDisplay_1sa4m_3597,
  ._melodyDisplay_1sa4m_3727,
  ._melodyOptions_1sa4m_3791,
  ._soundDisplay_1sa4m_3877,
  ._instrumentOptions_1sa4m_3979,
  ._memoryChallenge_1sa4m_4095,
  ._memoryInput_1sa4m_4209,
  ._compositionArea_1sa4m_4279,
  ._compositionControls_1sa4m_4421 {
    padding: 1rem;
  }

  ._sequenceIndicator_1sa4m_3479,
  ._playerSequence_1sa4m_3547,
  ._rhythmPattern_1sa4m_3617,
  ._melodyPattern_1sa4m_3745,
  ._memorySequence_1sa4m_4113,
  ._compositionSequence_1sa4m_4297 {
    flex-direction: column;
    gap: 0.5rem;
  }

  ._optionsGrid_1sa4m_3997,
  ._noteOptions_1sa4m_3809 {
    grid-template-columns: 1fr;
  }

  ._rhythmControls_1sa4m_3669,
  ._compositionActions_1sa4m_4437 {
    flex-direction: column;
    gap: 0.5rem;
  }

  ._sequenceItem_1sa4m_3493,
  ._playerItem_1sa4m_3561,
  ._rhythmBeat_1sa4m_3631,
  ._melodyNote_1sa4m_3759,
  ._memoryItem_1sa4m_4129,
  ._compositionNote_1sa4m_4317 {
    font-size: 1.2rem;
    padding: 0.75rem;
    min-width: 50px;
  }
}

/* Suporte a alto contraste */
[data-theme="high-contrast"] {
  --card-background: #000;
  --card-border: 1px solid #fff;
  --success-bg: #28a745;
  --success-border: #1e7e34;
  --error-bg: #dc3545;
  --error-border: #bd2130;
}

/* Suporte a movimento reduzido */
._reduced-motion_1sa4m_2073 {
  ._answerButton_1sa4m_1321, ._controlButton_1sa4m_1501, ._countingObject_1sa4m_447, ._feedbackMessage_1sa4m_2651, ._soundIndicator_1sa4m_3013, ._sequenceMissing_1sa4m_4741 {
    animation: none !important;
    transition: none !important;
  }
}