{"error":{"message":"Mobile access not allowed","status":"error","statusCode":500},"ip":"::1","level":"error","message":"Development Error:","method":"GET","timestamp":"2025-07-01T23:03:13.428Z","url":"/ws","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"}
{"error":{"message":"Mobile access not allowed","status":"error","statusCode":500},"ip":"::ffff:127.0.0.1","level":"error","message":"Development Error:","method":"GET","timestamp":"2025-07-01T23:05:23.770Z","url":"/ws","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"}
{"error":{"message":"Mobile access not allowed","status":"error","statusCode":500},"ip":"::1","level":"error","message":"Development Error:","method":"GET","timestamp":"2025-07-01T23:06:07.210Z","url":"/ws","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"}
{"error":{"message":"Mobile access not allowed","status":"error","statusCode":500},"ip":"::1","level":"error","message":"Development Error:","method":"GET","timestamp":"2025-07-01T23:06:08.615Z","url":"/ws","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"}
{"error":{"message":"Mobile access not allowed","status":"error","statusCode":500},"ip":"::1","level":"error","message":"Development Error:","method":"GET","timestamp":"2025-07-01T23:06:10.696Z","url":"/ws","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"}
{"error":{"message":"Mobile access not allowed","status":"error","statusCode":500},"ip":"::1","level":"error","message":"Development Error:","method":"GET","timestamp":"2025-07-01T23:06:15.411Z","url":"/ws","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"}
{"error":{"message":"Mobile access not allowed","status":"error","statusCode":500},"ip":"::1","level":"error","message":"Development Error:","method":"GET","timestamp":"2025-07-01T23:06:23.517Z","url":"/ws","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"}
{"error":{"message":"Mobile access not allowed","status":"error","statusCode":500},"ip":"::1","level":"error","message":"Development Error:","method":"GET","timestamp":"2025-07-01T23:06:40.406Z","url":"/ws","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"}
{"error":{"message":"Mobile access not allowed","status":"error","statusCode":500},"ip":"::1","level":"error","message":"Development Error:","method":"GET","timestamp":"2025-07-01T23:07:13.424Z","url":"/ws","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"}
{"error":{"message":"Mobile access not allowed","status":"error","statusCode":500},"ip":"::1","level":"error","message":"Development Error:","method":"GET","timestamp":"2025-07-01T23:08:18.432Z","url":"/ws","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"}
{"error":{"message":"Mobile access not allowed","status":"error","statusCode":500},"ip":"::1","level":"error","message":"Development Error:","method":"GET","timestamp":"2025-07-01T23:10:27.429Z","url":"/ws","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"}
{"level":"error","message":"Erro ao carregar middlewares de segurança: Duplicate export of 'createLogger'","stack":"SyntaxError: Duplicate export of 'createLogger'\n    at compileSourceTextModule (node:internal/modules/esm/utils:338:16)\n    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:102:18)\n    at #translate (node:internal/modules/esm/loader:468:12)\n    at ModuleLoader.loadAndTranslate (node:internal/modules/esm/loader:515:27)","timestamp":"2025-07-10T21:15:10.351Z"}
{"level":"error","message":"❌ Erro fatal ao iniciar o servidor: Unexpected token 'export'","stack":"SyntaxError: Unexpected token 'export'\n    at compileSourceTextModule (node:internal/modules/esm/utils:338:16)\n    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:102:18)\n    at #translate (node:internal/modules/esm/loader:468:12)\n    at ModuleLoader.loadAndTranslate (node:internal/modules/esm/loader:515:27)","timestamp":"2025-07-10T21:16:07.661Z"}
{"level":"error","message":"Erro ao carregar middlewares de segurança: The requested module '../../../utils/logger.js' does not provide an export named 'createLogger'","stack":"file:///C:/Projetos/protalbetinav3/src/api/middleware/security/cors.js:2\nimport { createLogger } from '../../../utils/logger.js'\n         ^^^^^^^^^^^^\nSyntaxError: The requested module '../../../utils/logger.js' does not provide an export named 'createLogger'\n    at ModuleJob._instantiate (node:internal/modules/esm/module_job:180:21)\n    at async ModuleJob.run (node:internal/modules/esm/module_job:263:5)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)\n    at async loadSecurityMiddleware (file:///C:/Projetos/protalbetinav3/src/api/server.js:54:29)\n    at async createApp (file:///C:/Projetos/protalbetinav3/src/api/server.js:89:68)\n    at async startServer (file:///C:/Projetos/protalbetinav3/src/api/server.js:224:17)","timestamp":"2025-07-10T21:23:08.560Z"}
{"level":"error","message":"❌ Erro fatal ao iniciar o servidor: Unexpected token 'export'","stack":"SyntaxError: Unexpected token 'export'\n    at compileSourceTextModule (node:internal/modules/esm/utils:338:16)\n    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:102:18)\n    at #translate (node:internal/modules/esm/loader:468:12)\n    at ModuleLoader.loadAndTranslate (node:internal/modules/esm/loader:515:27)","timestamp":"2025-07-10T21:23:18.181Z"}
{"level":"error","message":"❌ Erro fatal ao iniciar o servidor: Unexpected token 'export'","stack":"SyntaxError: Unexpected token 'export'\n    at compileSourceTextModule (node:internal/modules/esm/utils:338:16)\n    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:102:18)\n    at #translate (node:internal/modules/esm/loader:468:12)\n    at ModuleLoader.loadAndTranslate (node:internal/modules/esm/loader:515:27)","timestamp":"2025-07-10T21:23:47.864Z"}
{"level":"error","message":"❌ Erro fatal ao iniciar o servidor: Unexpected token 'export'","stack":"SyntaxError: Unexpected token 'export'\n    at compileSourceTextModule (node:internal/modules/esm/utils:338:16)\n    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:102:18)\n    at #translate (node:internal/modules/esm/loader:468:12)\n    at ModuleLoader.loadAndTranslate (node:internal/modules/esm/loader:515:27)","timestamp":"2025-07-10T21:24:01.453Z"}
{"level":"error","message":"❌ Erro fatal ao iniciar o servidor: Unexpected token 'export'","stack":"SyntaxError: Unexpected token 'export'\n    at compileSourceTextModule (node:internal/modules/esm/utils:338:16)\n    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:102:18)\n    at #translate (node:internal/modules/esm/loader:468:12)\n    at ModuleLoader.loadAndTranslate (node:internal/modules/esm/loader:515:27)","timestamp":"2025-07-10T21:24:15.363Z"}
{"level":"error","message":"❌ Erro fatal ao iniciar o servidor: Unexpected token 'export'","stack":"SyntaxError: Unexpected token 'export'\n    at compileSourceTextModule (node:internal/modules/esm/utils:338:16)\n    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:102:18)\n    at #translate (node:internal/modules/esm/loader:468:12)\n    at ModuleLoader.loadAndTranslate (node:internal/modules/esm/loader:515:27)","timestamp":"2025-07-10T21:24:45.689Z"}
{"level":"error","message":"❌ Erro fatal ao iniciar o servidor: Unexpected token 'export'","stack":"SyntaxError: Unexpected token 'export'\n    at compileSourceTextModule (node:internal/modules/esm/utils:338:16)\n    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:102:18)\n    at #translate (node:internal/modules/esm/loader:468:12)\n    at ModuleLoader.loadAndTranslate (node:internal/modules/esm/loader:515:27)","timestamp":"2025-07-10T21:40:46.685Z"}
{"level":"error","message":"❌ Erro fatal ao iniciar o servidor: Unexpected token 'export'","stack":"SyntaxError: Unexpected token 'export'\n    at compileSourceTextModule (node:internal/modules/esm/utils:338:16)\n    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:102:18)\n    at #translate (node:internal/modules/esm/loader:468:12)\n    at ModuleLoader.loadAndTranslate (node:internal/modules/esm/loader:515:27)","timestamp":"2025-07-10T21:40:57.335Z"}
