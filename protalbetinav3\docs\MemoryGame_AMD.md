# Documentação de Arquitetura e Métricas (AMD) - <PERSON><PERSON> da Memória V3

## 📝 Visão Geral

O Jogo da Memória V3 é uma aplicação cognitiva interativa com foco no desenvolvimento e avaliação de diferentes aspectos da memória. A aplicação conta com múltiplas atividades que abordam diferentes tipos de memória, incluindo memória visual, espacial, sequencial e de trabalho.

> **Atualizado em:** 21 de julho de 2025  
> **Última modificação:** Implementação do sistema de múltiplas rodadas para a atividade de localização espacial

## 🏗️ Arquitetura do Sistema

### Componentes Principais

1. **MemoryGame.jsx**: Componente principal que gerencia o estado do jogo, renderiza as interfaces e controla a lógica de jogo.

2. **MemoryGameConfig.js**: Conté<PERSON> todas as configurações do jogo, incluindo níveis de dificuldade, rotação de atividades, pontuações e tempos.

3. **MemoryGameMetrics.js**: Responsável pela coleta e processamento de métricas de desempenho do usuário.

4. **Coletores Especializados**: Hub de 15 coletores especializados para análise detalhada de diferentes aspectos do desempenho do jogador.

### Fluxo de Dados

```
Interação do Usuário → Estado do Jogo → Coletores de Dados → Métricas → Backend/Análise
```

## 🎮 Atividades do Jogo

O jogo conta com quatro atividades principais:

1. **Combinação de Pares** (pair_matching)
2. **Localização Espacial** (spatial_location)
3. **Reconstrução de Imagem** (image_reconstruction)
4. **Sequência Numérica** (number_sequence)

## 🔍 Foco: Atividade de Localização Espacial

### Descrição

A atividade de Localização Espacial testa a capacidade do jogador de memorizar e reproduzir posições espaciais em uma grade. O jogador deve observar um padrão de posições destacadas, memorizar sua localização e, em seguida, reproduzir o mesmo padrão.

### Níveis de Dificuldade

| Nível    | Tamanho da Grade | Posições Alvo | Tempo de Exibição (ms) |
|----------|------------------|---------------|------------------------|
| Fácil    | 3x3              | 3             | 3000                  |
| Médio    | 4x4              | 4             | 2500                  |
| Difícil  | 5x5              | 6             | 2000                  |

### Configuração de Rodadas

- **Fácil**: 4 rodadas
- **Médio**: 5 rodadas
- **Difícil**: 7 rodadas

### Fluxo da Atividade

1. Inicialização da grade e seleção aleatória de posições alvo
2. Exibição das posições para memorização (tempo baseado na dificuldade)
3. Remoção das dicas visuais e solicitação para o jogador reproduzir o padrão
4. Coleta de seleções do jogador e comparação com posições originais
5. Cálculo de pontuação baseado na precisão
6. Feedback e transição para próxima rodada ou atividade

### Implementação Técnica

A atividade é implementada através do método `renderSpatialLocationActivity()` no componente `MemoryGame.jsx`. A lógica inclui:

- Geração de grade dinâmica baseada no nível de dificuldade
- Seleção aleatória de posições alvo
- Exibição temporizada das posições
- Validação das seleções do usuário
- Feedback visual e sonoro para acertos e erros
- Cálculo de pontuação baseado na precisão

## 📊 Métricas e Coleta de Dados

### Coletor Especializado: SpatialLocationCollector

O SpatialLocationCollector é responsável por analisar especificamente o desempenho do usuário na atividade de localização espacial, coletando dados sobre:

#### Métricas Primárias:

1. **Precisão Espacial (Spatial Accuracy)**
   - Porcentagem de posições corretamente identificadas
   - Pontuação de proximidade para seleções incorretas

2. **Mapas Espaciais (Spatial Maps)**
   - Registro das posições originais vs. seleções do usuário
   - Análise de correspondência espacial

3. **Distorções Espaciais (Spatial Distortions)**
   - Tendência ao centro
   - Tendência às bordas
   - Viés horizontal/vertical

4. **Padrões de Navegação (Navigation Patterns)**
   - Sequência de seleção
   - Estratégias de varredura visual
   - Padrões de movimento

5. **Memória de Trabalho Espacial (Spatial Working Memory)**
   - Capacidade de reter informações espaciais
   - Performance sob diferentes cargas cognitivas

6. **Memória de Coordenadas (Coordinate Memory)**
   - Precisão na memorização de localizações específicas
   - Erros de deslocamento

7. **Sequenciamento Espacial (Spatial Sequencing)**
   - Capacidade de lembrar a ordem espacial
   - Reconhecimento de padrões espaciais

8. **Rotação Mental (Mental Rotation)**
   - Capacidade de processar informações espaciais rotacionadas
   - Erros de orientação espacial

### Análise de Erros

O coletor categoriza erros em diferentes tipos:

- **Adjacente**: Erro pequeno (célula adjacente)
- **Próximo**: Erro moderado (até 2 células de distância)
- **Moderado**: Erro significativo (até metade da grade)
- **Distante**: Erro grave (mais da metade da grade)
- **Omissão**: Posição original não selecionada
- **Falso Positivo**: Seleção de posição não original

### Integração com Sistema de Coleta

As métricas são coletadas em tempo real durante a interação do usuário e processadas pelo hub de coletores `MemoryGameCollectorsHub`, que integra dados de todas as atividades para uma análise abrangente do desempenho cognitivo.

## 📈 Pontuação e Feedback

### Sistema de Pontuação

- **Pontos Base**: 10 pontos por acerto
- **Bônus de Rodada Perfeita**: 100 pontos adicionais
- **Pontuação por Atividade**: Baseada na precisão (% de acertos * pontuação perfeita)

### Feedback ao Usuário

- Feedback visual: Células corretas (verde) e incorretas (vermelho)
- Feedback sonoro: Sons distintos para acertos e erros
- Feedback verbal: Mensagens de encorajamento por TTS
- Contador de progresso: Exibição do número de seleções feitas vs. necessárias

## 🧩 Integração com Outros Sistemas

### Multisensory Integration

A atividade integra-se com o sistema de integração multissensorial para:
- Adaptação de estímulos baseada no perfil de aprendizagem do usuário
- Coleta de dados de interação multissensorial
- Análise de padrões de uso dos diferentes canais sensoriais

### Therapeutic Orchestrator

Para usuários cadastrados, o sistema integra-se com o orquestrador terapêutico para:
- Personalização da dificuldade baseada no perfil cognitivo
- Recomendação de atividades específicas
- Acompanhamento de progresso longitudinal

## 🔄 Ciclo de Desenvolvimento e Evolução

### Histórico de Versões

- **V1**: Implementação básica de localização espacial
- **V2**: Adição de níveis de dificuldade e métricas simples
- **V3**: Integração com coletores especializados, análise aprofundada, adaptação multissensorial

### Próximos Passos

1. Implementação de estímulos espaciais mais complexos (3D, rotação)
2. Integração de feedback háptico para reforço de memória espacial
3. Desenvolvimento de relatórios detalhados para uso terapêutico
4. Personalização baseada em perfil cognitivo do usuário

## 🛠️ Considerações Técnicas

### Performance

- Otimizações de renderização para grades maiores
- Cache de análises para processamento em segundo plano
- Lazy loading de análises complexas

### Acessibilidade

- Suporte a TTS para instruções e feedback
- Configurações de tempo ajustáveis
- Feedback multimodal (visual, auditivo)

## 📊 Dashboard e Visualização de Dados

Os dados coletados pelo SpatialLocationCollector alimentam dashboards que exibem:

1. **Mapa de Calor Espacial**: Visualização da distribuição de erros espaciais
2. **Gráficos de Progresso**: Evolução da precisão espacial ao longo do tempo
3. **Análise Comparativa**: Desempenho em diferentes níveis de dificuldade
4. **Métricas Cognitivas**: Indicadores de capacidade espacial e working memory

---

Documento gerado em: 21 de julho de 2025
Versão: 1.0.0
