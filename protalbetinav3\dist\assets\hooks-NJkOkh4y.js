import { r as reactExports } from "./vendor-react-ByWh_-BW.js";
import { S as SystemContext } from "./context-Ch-5FaFa.js";
import { g as getPortalBetinaV3, S as StructuredLogger, A as AIBrainOrchestrator, M as MultisensoryMetricsCollector } from "./services-M1ydzWhv.js";
import { v as v4 } from "./vendor-utils-CjlX8hrF.js";
function useAccessibility() {
  const [settings, setSettings] = reactExports.useState({
    textToSpeech: true,
    highContrast: false,
    // SEMPRE FALSE por padrão
    reducedMotion: false,
    colorScheme: "default",
    dyslexiaFriendly: false,
    fontSize: "medium",
    soundEnabled: true,
    autoRead: false
  });
  reactExports.useEffect(() => {
    const systemPrefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
    const systemPrefersReducedMotion = window.matchMedia("(prefers-reduced-motion: reduce)").matches;
    const initialSettings = {
      textToSpeech: true,
      highContrast: false,
      // SEMPRE FALSE - não aplicar alto contraste por padrão
      reducedMotion: systemPrefersReducedMotion,
      colorScheme: systemPrefersDark ? "dark" : "default",
      dyslexiaFriendly: false,
      fontSize: "medium",
      soundEnabled: true,
      autoRead: false
    };
    const localSettings = localStorage.getItem("betina_accessibility_settings");
    if (localSettings) {
      try {
        const parsedSettings = JSON.parse(localSettings);
        const safeSettings = { ...initialSettings, ...parsedSettings, highContrast: false };
        setSettings(safeSettings);
        applySettingsToDOM(safeSettings);
      } catch (e) {
        console.error("Erro ao carregar configurações de acessibilidade:", e);
        setSettings(initialSettings);
        applySettingsToDOM(initialSettings);
      }
    } else {
      setSettings(initialSettings);
      applySettingsToDOM(initialSettings);
    }
  }, []);
  const applySettingsToDOM = reactExports.useCallback((newSettings) => {
    const root = document.documentElement;
    const body = document.body;
    if (newSettings.highContrast) {
      root.classList.add("high-contrast-active");
      body.classList.add("high-contrast-active");
    } else {
      root.classList.remove("high-contrast-active");
      body.classList.remove("high-contrast-active");
    }
    if (newSettings.reducedMotion) {
      root.classList.add("reduced-motion");
      body.classList.add("reduced-motion");
    } else {
      root.classList.remove("reduced-motion");
      body.classList.remove("reduced-motion");
    }
    const themeToApply = newSettings.highContrast ? "default" : newSettings.colorScheme;
    root.setAttribute("data-theme", themeToApply);
    body.setAttribute("data-theme", themeToApply);
    if (newSettings.dyslexiaFriendly) {
      root.classList.add("dyslexia-friendly");
      body.classList.add("dyslexia-friendly");
    } else {
      root.classList.remove("dyslexia-friendly");
      body.classList.remove("dyslexia-friendly");
    }
    root.setAttribute("data-font-size", newSettings.fontSize);
    body.setAttribute("data-font-size", newSettings.fontSize);
  }, []);
  const updateSettings = reactExports.useCallback((newSettings) => {
    const updatedSettings = { ...settings, ...newSettings };
    setSettings(updatedSettings);
    applySettingsToDOM(updatedSettings);
    try {
      localStorage.setItem("betina_accessibility_settings", JSON.stringify(updatedSettings));
    } catch (e) {
      console.error("Erro ao salvar configurações de acessibilidade:", e);
    }
    window.dispatchEvent(new CustomEvent("accessibility-changed", {
      detail: updatedSettings
    }));
  }, [settings, applySettingsToDOM]);
  const applyPreset = reactExports.useCallback((preset) => {
    let presetSettings = {};
    switch (preset) {
      case "high-contrast":
        presetSettings = {
          highContrast: true,
          colorScheme: "default",
          // Mantém o tema padrão, apenas ativa o contraste
          fontSize: "large"
        };
        break;
      case "autism-friendly":
        presetSettings = {
          reducedMotion: true,
          soundEnabled: false,
          colorScheme: "soft",
          fontSize: "medium"
        };
        break;
      case "dyslexia":
        presetSettings = {
          dyslexiaFriendly: true,
          fontSize: "large",
          textToSpeech: true
        };
        break;
      case "default":
        presetSettings = {
          textToSpeech: true,
          highContrast: false,
          reducedMotion: false,
          colorScheme: "default",
          dyslexiaFriendly: false,
          fontSize: "medium",
          soundEnabled: true,
          autoRead: false
        };
        break;
    }
    updateSettings(presetSettings);
  }, [updateSettings]);
  return {
    settings,
    updateSettings,
    applyPreset
  };
}
function useUnifiedGameLogic(gameType) {
  let systemContext = null;
  let user = null;
  let ttsEnabled = true;
  try {
    systemContext = reactExports.useContext(SystemContext);
    if (systemContext) {
      user = systemContext.user;
      ttsEnabled = systemContext.ttsEnabled !== void 0 ? systemContext.ttsEnabled : true;
    }
  } catch (error) {
    console.warn("⚠️ SystemContext not available, using defaults:", error.message);
  }
  const [sessionId, setSessionId] = reactExports.useState(null);
  const [isSessionActive, setIsSessionActive] = reactExports.useState(false);
  const [sessionMetrics, setSessionMetrics] = reactExports.useState({});
  const portalRef = reactExports.useRef(null);
  const [gameState, setGameState] = reactExports.useState({
    score: 0,
    round: 1,
    accuracy: 100,
    totalCorrect: 0,
    totalAttempts: 0,
    difficulty: "medium"
  });
  reactExports.useEffect(() => {
    const initializePortal = async () => {
      try {
        portalRef.current = await getPortalBetinaV3();
        console.log("✅ Portal Betina V3 connected to", gameType);
      } catch (error) {
        console.error("❌ Failed to initialize Portal Betina V3:", error);
      }
    };
    initializePortal();
  }, [gameType]);
  reactExports.useEffect(() => {
    return () => {
      if (sessionId && isSessionActive) {
        console.log("🧹 Cleaning up active session on unmount:", sessionId);
        (async () => {
          try {
            if (portalRef.current) {
              const result = await portalRef.current.finalizeSession(sessionId, {
                reason: "component_unmount",
                endTime: (/* @__PURE__ */ new Date()).toISOString()
              });
              if (result.success) {
                console.log("✅ Session cleanup completed successfully");
              } else {
                console.warn("⚠️ Session cleanup completed with warnings:", result.message);
              }
            }
          } catch (error) {
            console.warn("⚠️ Error during session cleanup (non-critical):", error.message);
          }
        })();
      }
    };
  }, [sessionId, isSessionActive]);
  const startUnifiedSession = reactExports.useCallback(async (difficulty = "medium") => {
    try {
      if (!portalRef.current) {
        console.debug("🔄 Portal not ready, starting local session for", gameType);
        const localSessionId = `local-${gameType}-${v4()}`;
        setSessionId(localSessionId);
        setIsSessionActive(true);
        setSessionMetrics({
          sessionId: localSessionId,
          gameType,
          userId: user?.id || "anonymous",
          difficulty,
          startTime: (/* @__PURE__ */ new Date()).toISOString(),
          mode: "local"
        });
        setGameState((prev) => ({
          ...prev,
          difficulty,
          sessionId: localSessionId
        }));
        console.log(`🎮 Started local session for ${gameType}:`, localSessionId);
        return {
          success: true,
          sessionId: localSessionId,
          mode: "local",
          multisensoryEnabled: false,
          predictiveEnabled: false
        };
      }
      const userId = user?.id || "anonymous";
      const result = await portalRef.current.startGameSession(userId, gameType, difficulty);
      if (result.success) {
        setSessionId(result.sessionId);
        setIsSessionActive(true);
        setSessionMetrics({
          sessionId: result.sessionId,
          gameType,
          userId,
          difficulty,
          startTime: (/* @__PURE__ */ new Date()).toISOString(),
          multisensoryEnabled: result.multisensoryEnabled,
          predictiveEnabled: result.predictiveEnabled,
          mode: "portal"
        });
        setGameState((prev) => ({
          ...prev,
          difficulty,
          sessionId: result.sessionId
        }));
        console.log(`🎮 Started unified session for ${gameType}:`, result.sessionId);
        return result;
      }
      throw new Error(result.error || "Failed to start session");
    } catch (error) {
      console.error("❌ Failed to start unified session, falling back to local:", error);
      const fallbackSessionId = `fallback-${gameType}-${v4()}`;
      setSessionId(fallbackSessionId);
      setIsSessionActive(true);
      setSessionMetrics({
        sessionId: fallbackSessionId,
        gameType,
        userId: user?.id || "anonymous",
        difficulty,
        startTime: (/* @__PURE__ */ new Date()).toISOString(),
        mode: "fallback",
        error: error.message
      });
      setGameState((prev) => ({
        ...prev,
        difficulty,
        sessionId: fallbackSessionId
      }));
      return {
        success: true,
        sessionId: fallbackSessionId,
        mode: "fallback",
        multisensoryEnabled: false,
        predictiveEnabled: false,
        warning: "Session started in fallback mode"
      };
    }
  }, [gameType, user]);
  const recordInteraction = reactExports.useCallback(async (actionType, data, isCorrect = null, duration = 0) => {
    try {
      if (!portalRef.current || !sessionId) {
        console.debug("📝 Recording interaction locally (portal/session not ready):", {
          actionType,
          gameType,
          isCorrect,
          duration
        });
        setGameState((prev) => {
          const newTotalAttempts = prev.totalAttempts + 1;
          const newTotalCorrect = prev.totalCorrect + (isCorrect ? 1 : 0);
          const newAccuracy = newTotalAttempts > 0 ? Math.round(newTotalCorrect / newTotalAttempts * 100) : 100;
          return {
            ...prev,
            totalAttempts: newTotalAttempts,
            totalCorrect: newTotalCorrect,
            accuracy: newAccuracy,
            score: prev.score + (isCorrect ? 10 : 0)
          };
        });
        return {
          success: true,
          mode: "local",
          message: "Interaction recorded locally"
        };
      }
      const action = {
        type: actionType,
        data,
        isCorrect,
        duration,
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        gameType,
        round: gameState.round
      };
      const result = await portalRef.current.recordGameAction(sessionId, action);
      if (result.success) {
        setGameState((prev) => {
          const newTotalAttempts = prev.totalAttempts + 1;
          const newTotalCorrect = prev.totalCorrect + (isCorrect ? 1 : 0);
          const newAccuracy = newTotalAttempts > 0 ? Math.round(newTotalCorrect / newTotalAttempts * 100) : 100;
          return {
            ...prev,
            totalAttempts: newTotalAttempts,
            totalCorrect: newTotalCorrect,
            accuracy: newAccuracy,
            score: prev.score + (isCorrect ? 10 : 0)
          };
        });
        setSessionMetrics((prev) => ({
          ...prev,
          lastAction: action,
          totalInteractions: (prev.totalInteractions || 0) + 1,
          hasMultisensoryData: result.multisensoryData?.captured || false,
          hasPredictiveAnalysis: result.predictions?.available || false
        }));
        console.log(`📊 Recorded interaction: ${actionType}`, result.analysis?.processed ? "✅" : "⚠️");
        return result;
      }
      throw new Error(result.error || "Failed to record interaction");
    } catch (error) {
      console.error("❌ Failed to record interaction:", error);
      return { success: false, error: error.message };
    }
  }, [sessionId, gameType, gameState.round]);
  const endUnifiedSession = reactExports.useCallback(async (finalData = {}) => {
    try {
      if (!sessionId || !isSessionActive) {
        console.log("ℹ️ No active session to finalize or session already finalized");
        return { success: true, message: "No active session or session already finalized" };
      }
      if (!portalRef.current) {
        console.log("🔄 Finalizing session locally (portal not available)");
        setIsSessionActive(false);
        setSessionMetrics((prev) => ({
          ...prev,
          endTime: (/* @__PURE__ */ new Date()).toISOString(),
          finalData
        }));
        return { success: true, mode: "local", message: "Session finalized locally" };
      }
      const result = await portalRef.current.finalizeSession(sessionId, finalData);
      if (result.success) {
        setIsSessionActive(false);
        setSessionMetrics((prev) => ({
          ...prev,
          endTime: (/* @__PURE__ */ new Date()).toISOString(),
          finalReport: result.report,
          therapeuticAnalysis: result.report?.therapeuticAnalysis,
          multisensoryReport: result.report?.multisensoryReport,
          predictions: result.report?.futurePredictions,
          finalData
        }));
        console.log(`🏁 Finalized session ${sessionId}:`, {
          multisensory: !!result.report?.multisensoryReport,
          therapeutic: !!result.report?.therapeuticAnalysis,
          predictive: !!result.report?.futurePredictions
        });
        return result;
      }
      throw new Error(result.error || "Failed to finalize session");
    } catch (error) {
      console.warn("⚠️ Session finalization error (may already be finalized):", error.message);
      setIsSessionActive(false);
      return {
        success: false,
        error: error.message,
        gracefulFailure: true
      };
    }
  }, [sessionId, isSessionActive]);
  const resetSession = reactExports.useCallback(() => {
    setSessionId(null);
    setIsSessionActive(false);
    setSessionMetrics({});
    setGameState({
      score: 0,
      round: 1,
      accuracy: 100,
      totalCorrect: 0,
      totalAttempts: 0,
      difficulty: "medium"
    });
    console.log("🔄 Session reset for", gameType);
  }, [gameType]);
  const getSessionStatus = reactExports.useCallback(() => {
    if (!portalRef.current || !sessionId) {
      return { active: false };
    }
    return {
      active: isSessionActive,
      sessionId,
      gameType,
      metrics: sessionMetrics,
      gameState,
      portalStatus: portalRef.current.getSessionStatus(sessionId)
    };
  }, [sessionId, isSessionActive, sessionMetrics, gameState, gameType]);
  const repeatInstruction = reactExports.useCallback(async (text) => {
    await recordInteraction("instruction_repeat", {
      text,
      accessibility: true,
      ttsEnabled
    });
    if (ttsEnabled && "speechSynthesis" in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = "pt-BR";
      utterance.rate = 0.8;
      speechSynthesis.speak(utterance);
    } else {
      alert(text);
    }
  }, [recordInteraction, ttsEnabled]);
  return {
    // Estados do jogo
    gameState,
    setGameState,
    sessionMetrics,
    isSessionActive,
    sessionId,
    // Funções principais do Portal Betina V3
    startUnifiedSession,
    recordInteraction,
    endUnifiedSession,
    resetSession,
    getSessionStatus,
    // Funções auxiliares
    repeatInstruction,
    // Compatibilidade com sistemas existentes
    ttsEnabled,
    user,
    // Status do Portal
    portalReady: !!portalRef.current
  };
}
class MultisensoryAIBrainIntegrator {
  constructor(options = {}) {
    this.logger = StructuredLogger.getInstance({
      serviceName: "MultisensoryAIBrainIntegrator",
      logLevel: options.logLevel || "info"
    });
    this.aiBrain = options.aiBrain || new AIBrainOrchestrator();
    this.enableRealTimeAnalysis = options.enableRealTimeAnalysis || false;
    this.analysisFrequency = options.analysisFrequency || "session-end";
    this.sessionData = null;
    this.isProcessing = false;
    this.processingQueue = [];
    this.lastProcessedTimestamp = null;
    this.logger.info("MultisensoryAIBrainIntegrator inicializado", {
      enableRealTimeAnalysis: this.enableRealTimeAnalysis,
      analysisFrequency: this.analysisFrequency
    });
  }
  /**
   * Processa métricas multissensoriais e envia para o AI Brain
   * @param {Object} metricsData - Dados coletados pelo MultisensoryMetricsCollector
   * @param {Object} options - Opções de processamento
   * @returns {Promise<Object>} - Resultado da análise do AI Brain
   */
  async processMultisensoryMetrics(metricsData, options = {}) {
    if (!metricsData) {
      this.logger.warn("Dados multissensoriais vazios ou inválidos");
      return { success: false, error: "Dados multissensoriais inválidos" };
    }
    try {
      this.logger.info("Processando métricas multissensoriais", {
        sessionId: metricsData.sessionId,
        dataPoints: metricsData.sensorData?.length || 0,
        gameType: metricsData.gameType || "unknown"
      });
      this.sessionData = metricsData;
      const processedData = this._preprocessMultisensoryData(metricsData);
      const aiResponse = await this._sendToAIBrain(processedData, options);
      this.lastProcessedTimestamp = (/* @__PURE__ */ new Date()).toISOString();
      return {
        success: true,
        analysisResults: aiResponse,
        timestamp: this.lastProcessedTimestamp,
        sessionId: metricsData.sessionId
      };
    } catch (error) {
      this.logger.error("Erro ao processar métricas multissensoriais", error);
      return {
        success: false,
        error: error.message || "Erro desconhecido no processamento",
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    }
  }
  /**
   * Pré-processa os dados multissensoriais para o formato esperado pelo AI Brain
   * @private
   * @param {Object} rawData - Dados brutos do MultisensoryMetricsCollector
   * @returns {Object} - Dados processados prontos para o AI Brain
   */
  _preprocessMultisensoryData(rawData) {
    const { sessionId, userId, gameType, sensorData = [], deviceInfo, startTime, endTime } = rawData;
    const accelerometerData = this._extractSensorData(sensorData, "accelerometer");
    const gyroscopeData = this._extractSensorData(sensorData, "gyroscope");
    const touchData = this._extractSensorData(sensorData, "touch");
    const geolocationData = this._extractSensorData(sensorData, "geolocation");
    const aggregatedMetrics = this._calculateAggregatedMetrics(sensorData);
    return {
      metadata: {
        sessionId,
        userId,
        gameType,
        deviceType: deviceInfo?.deviceType || "unknown",
        platform: deviceInfo?.platform || "unknown",
        startTime,
        endTime,
        duration: endTime && startTime ? new Date(endTime) - new Date(startTime) : null,
        dataPoints: sensorData.length
      },
      sensorMetrics: {
        accelerometer: {
          available: accelerometerData.length > 0,
          dataPoints: accelerometerData.length,
          data: accelerometerData
        },
        gyroscope: {
          available: gyroscopeData.length > 0,
          dataPoints: gyroscopeData.length,
          data: gyroscopeData
        },
        touch: {
          available: touchData.length > 0,
          dataPoints: touchData.length,
          data: touchData
        },
        geolocation: {
          available: geolocationData.length > 0,
          dataPoints: geolocationData.length,
          data: geolocationData
        }
      },
      aggregatedMetrics
    };
  }
  /**
   * Extrai dados de um sensor específico
   * @private
   * @param {Array} sensorData - Array de dados dos sensores
   * @param {String} sensorType - Tipo de sensor (accelerometer, gyroscope, etc.)
   * @returns {Array} - Dados filtrados do sensor especificado
   */
  _extractSensorData(sensorData, sensorType) {
    return sensorData.filter((data) => data.type === sensorType).map((item) => {
      const { type, timestamp, ...values } = item;
      return { timestamp, ...values };
    });
  }
  /**
   * Calcula métricas agregadas a partir dos dados dos sensores
   * @private
   * @param {Array} sensorData - Array de dados dos sensores
   * @returns {Object} - Métricas agregadas
   */
  _calculateAggregatedMetrics(sensorData) {
    const dataByType = sensorData.reduce((acc, item) => {
      if (!acc[item.type]) acc[item.type] = [];
      acc[item.type].push(item);
      return acc;
    }, {});
    const accelerometerData = dataByType.accelerometer || [];
    const suddenMovements = accelerometerData.filter((item) => {
      const magnitude = Math.sqrt(
        Math.pow(item.x || 0, 2) + Math.pow(item.y || 0, 2) + Math.pow(item.z || 0, 2)
      );
      return magnitude > 15;
    }).length;
    const touchData = dataByType.touch || [];
    const touchPatterns = this._analyzeTouchPatterns(touchData);
    const gyroscopeData = dataByType.gyroscope || [];
    const deviceStability = this._calculateDeviceStability(gyroscopeData);
    return {
      deviceHandling: {
        suddenMovements,
        suddenMovementsPerMinute: accelerometerData.length > 0 ? suddenMovements / (accelerometerData.length / 60) : 0,
        deviceStability: deviceStability * 100
        // Porcentagem
      },
      userInteraction: {
        touchFrequency: touchData.length,
        touchPrecision: touchPatterns.precision * 100,
        // Porcentagem
        touchConsistency: touchPatterns.consistency * 100
        // Porcentagem
      }
    };
  }
  /**
   * Analisa padrões de toque
   * @private
   * @param {Array} touchData - Dados de toque
   * @returns {Object} - Análise de padrões de toque
   */
  _analyzeTouchPatterns(touchData) {
    return {
      precision: touchData.length > 10 ? 0.85 : 0.7,
      consistency: touchData.length > 20 ? 0.9 : 0.75
    };
  }
  /**
   * Calcula a estabilidade do dispositivo
   * @private
   * @param {Array} gyroscopeData - Dados do giroscópio
   * @returns {Number} - Índice de estabilidade (0-1)
   */
  _calculateDeviceStability(gyroscopeData) {
    if (gyroscopeData.length < 5) return 0.5;
    const instabilitySum = gyroscopeData.reduce((sum, item) => {
      const magnitude = Math.sqrt(
        Math.pow(item.alpha || 0, 2) + Math.pow(item.beta || 0, 2) + Math.pow(item.gamma || 0, 2)
      );
      return sum + magnitude;
    }, 0);
    const avgInstability = instabilitySum / gyroscopeData.length;
    return Math.max(0, Math.min(1, 1 - avgInstability / 90));
  }
  /**
   * Envia dados processados para o AI Brain
   * @private
   * @param {Object} processedData - Dados processados
   * @param {Object} options - Opções adicionais
   * @returns {Promise<Object>} - Resposta do AI Brain
   */
  async _sendToAIBrain(processedData, options = {}) {
    try {
      this.logger.info("Enviando dados para AI Brain", {
        sessionId: processedData.metadata.sessionId,
        dataPoints: processedData.metadata.dataPoints
      });
      const payload = {
        analysisType: "multisensory-metrics",
        data: processedData,
        options: {
          includeDetailedAnalysis: options.includeDetailedAnalysis !== false,
          generateVisualizations: options.generateVisualizations || false,
          priority: options.priority || "normal",
          ...options
        }
      };
      const response = await this.aiBrain.processData(payload);
      this.logger.info("Resposta recebida do AI Brain", {
        sessionId: processedData.metadata.sessionId,
        analysisId: response.analysisId || "unknown"
      });
      return response;
    } catch (error) {
      this.logger.error("Erro ao enviar dados para AI Brain", error);
      throw new Error(`Falha na comunicação com AI Brain: ${error.message}`);
    }
  }
  /**
   * Obtém o último relatório de análise
   * @returns {Object|null} - Último relatório ou null se não houver
   */
  getLatestReport() {
    if (!this.sessionData || !this.lastProcessedTimestamp) {
      return null;
    }
    return {
      sessionId: this.sessionData.sessionId,
      userId: this.sessionData.userId,
      gameType: this.sessionData.gameType,
      processedAt: this.lastProcessedTimestamp,
      // Nota: Relatórios completos seriam armazenados em um sistema real
      reportAvailable: true,
      reportType: "multisensory-analysis"
    };
  }
}
async function storeSessionData(sessionData) {
  try {
    const storageKey = `multisensory_session_${sessionData.sessionId}`;
    const dataToStore = {
      ...sessionData,
      storedAt: Date.now(),
      version: "3.1.0"
    };
    localStorage.setItem(storageKey, JSON.stringify(dataToStore));
    const allKeys = Object.keys(localStorage).filter((key) => key.startsWith("multisensory_session_"));
    if (allKeys.length > 50) {
      const sortedKeys = allKeys.sort((a, b) => {
        const aData = JSON.parse(localStorage.getItem(a) || "{}");
        const bData = JSON.parse(localStorage.getItem(b) || "{}");
        return (aData.storedAt || 0) - (bData.storedAt || 0);
      });
      const keysToRemove = sortedKeys.slice(0, allKeys.length - 50);
      keysToRemove.forEach((key) => localStorage.removeItem(key));
    }
    try {
      const response = await fetch("/api/multisensory/sessions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(dataToStore)
      });
      if (!response.ok) {
        console.warn("API storage failed, using localStorage only");
      }
    } catch (apiError) {
      console.warn("API not available, using localStorage only:", apiError.message);
    }
    return true;
  } catch (error) {
    console.error("Failed to store session data:", error);
    return false;
  }
}
function useMultisensoryIntegration(gameType, collectors, options = {}) {
  const [logger] = reactExports.useState(() => StructuredLogger.getInstance({
    serviceName: "useMultisensoryIntegration",
    logLevel: options.logLevel || "info"
  }));
  const [multisensoryCollector] = reactExports.useState(() => new MultisensoryMetricsCollector(gameType));
  const [aiBrainIntegrator] = reactExports.useState(() => new MultisensoryAIBrainIntegrator({
    enableRealTimeAnalysis: options.enableRealTimeAnalysis || false,
    analysisFrequency: options.analysisFrequency || "session-end",
    logLevel: options.logLevel || "info"
  }));
  const [isInitialized, setIsInitialized] = reactExports.useState(false);
  const [currentSession, setCurrentSession] = reactExports.useState(null);
  const [multisensoryData, setMultisensoryData] = reactExports.useState(null);
  const [sessionReport, setSessionReport] = reactExports.useState(null);
  const [aiAnalysisResults, setAiAnalysisResults] = reactExports.useState(null);
  const [isLoading, setIsLoading] = reactExports.useState(false);
  const [error, setError] = reactExports.useState(null);
  const lastUpdateRef = reactExports.useRef(Date.now());
  const updateIntervalRef = reactExports.useRef(null);
  const sessionIdRef = reactExports.useRef(null);
  const defaultOptions = {
    autoUpdate: true,
    updateInterval: 1e3,
    // 1 segundo
    enablePatternAnalysis: true,
    logLevel: "info",
    enableSensorAccess: true,
    sensorUpdateRate: 16,
    // 60fps
    ...options
  };
  const initializeSession = reactExports.useCallback(async (sessionId, sessionConfig = {}) => {
    try {
      setIsLoading(true);
      setError(null);
      const finalSessionId = sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionIdRef.current = finalSessionId;
      const initResult = await multisensoryCollector.startMetricsCollection(
        finalSessionId,
        sessionConfig.userId || "anonymous",
        {
          gameType,
          enableSensorAccess: defaultOptions.enableSensorAccess,
          sensorUpdateRate: defaultOptions.sensorUpdateRate,
          ...sessionConfig
        }
      );
      if (!initResult.success) {
        throw new Error(initResult.error || "Falha ao inicializar sessão multissensorial");
      }
      setCurrentSession({
        sessionId: finalSessionId,
        gameType,
        startTime: (/* @__PURE__ */ new Date()).toISOString(),
        ...sessionConfig
      });
      setIsInitialized(true);
      if (defaultOptions.autoUpdate) {
        startAutoUpdate();
      }
      logger.info("🎯 Sessão multissensorial inicializada (DIRECT)", {
        type: "session_initialized",
        sessionId: finalSessionId,
        gameType,
        autoUpdate: defaultOptions.autoUpdate,
        sensorAccess: defaultOptions.enableSensorAccess,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      });
      return { success: true, sessionId: finalSessionId };
    } catch (err) {
      setError(err);
      logger.error("❌ Erro ao inicializar sessão multissensorial", {
        type: "session_init_error",
        error: err.message,
        gameType,
        sessionId
      });
      return { success: false, error: err.message };
    } finally {
      setIsLoading(false);
    }
  }, [gameType, multisensoryCollector, defaultOptions, logger]);
  const recordInteraction = reactExports.useCallback(async (action, data = {}) => {
    if (!isInitialized || !sessionIdRef.current) {
      logger.warn("⚠️ Sessão multissensorial não inicializada", {
        type: "interaction_not_initialized",
        action,
        gameType
      });
      return { success: false, error: "Session not initialized" };
    }
    try {
      const currentMetrics = await multisensoryCollector.getCurrentMetrics();
      const normalizedMetrics = {
        gameId: gameType,
        sessionId: sessionIdRef.current,
        userId: currentSession?.userId || "anonymous",
        action,
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        ...data
      };
      let result = { success: true, data: normalizedMetrics };
      if (currentMetrics && currentMetrics.mobileSensors) {
        const multisensoryData2 = [currentMetrics];
        const processedResult = await multisensoryCollector.processMultisensoryData(
          normalizedMetrics,
          multisensoryData2
        );
        if (!processedResult.error) {
          result.data = {
            ...normalizedMetrics,
            multisensoryAnalysis: processedResult
          };
        }
      }
      lastUpdateRef.current = Date.now();
      if (!defaultOptions.autoUpdate) {
        await updateMultisensoryData();
      }
      logger.debug("📊 Interação multissensorial registrada", {
        action,
        sessionId: sessionIdRef.current,
        success: result.success,
        hasMultisensoryData: !!currentMetrics
      });
      return result;
    } catch (err) {
      logger.error("❌ Erro ao registrar interação multissensorial", {
        type: "interaction_error",
        error: err.message,
        action,
        gameType
      });
      setError(err);
      return { success: false, error: err.message };
    }
  }, [isInitialized, gameType, multisensoryCollector, currentSession, defaultOptions, logger]);
  const updateMultisensoryData = reactExports.useCallback(async () => {
    if (!isInitialized || !sessionIdRef.current) return;
    try {
      const currentData = await multisensoryCollector.getCurrentMetrics();
      if (currentData) {
        setMultisensoryData(currentData);
      }
    } catch (err) {
      logger.error("❌ Erro ao atualizar dados multissensoriais", {
        type: "data_update_error",
        error: err.message,
        gameType
      });
    }
  }, [isInitialized, multisensoryCollector, gameType, logger]);
  const startAutoUpdate = reactExports.useCallback(() => {
    if (updateIntervalRef.current) {
      clearInterval(updateIntervalRef.current);
    }
    updateIntervalRef.current = setInterval(() => {
      updateMultisensoryData();
    }, defaultOptions.updateInterval);
  }, [updateMultisensoryData, defaultOptions.updateInterval]);
  const stopAutoUpdate = reactExports.useCallback(() => {
    if (updateIntervalRef.current) {
      clearInterval(updateIntervalRef.current);
      updateIntervalRef.current = null;
    }
  }, []);
  const analyzePatterns = reactExports.useCallback(async () => {
    if (!isInitialized || !defaultOptions.enablePatternAnalysis || !sessionIdRef.current) {
      return null;
    }
    try {
      const currentMetrics = await multisensoryCollector.getCurrentMetrics();
      if (currentMetrics && currentMetrics.neurodivergencePatterns) {
        return currentMetrics.neurodivergencePatterns;
      }
      return null;
    } catch (err) {
      logger.error("❌ Erro ao analisar padrões", {
        type: "pattern_analysis_error",
        error: err.message,
        gameType
      });
      return null;
    }
  }, [isInitialized, multisensoryCollector, defaultOptions.enablePatternAnalysis, gameType, logger]);
  const finalizeSession = reactExports.useCallback(async (sessionData = {}) => {
    if (!isInitialized || !sessionIdRef.current) {
      logger.warn("⚠️ Nenhuma sessão ativa para finalizar", {
        type: "finalize_not_initialized",
        gameType
      });
      return null;
    }
    try {
      setIsLoading(true);
      stopAutoUpdate();
      const report = await multisensoryCollector.stopMetricsCollection();
      if (report.success) {
        setSessionReport(report.report);
        logger.info("🧠 Enviando dados multissensoriais para AI Brain");
        const aiResult = await aiBrainIntegrator.processMultisensoryMetrics(report.report, {
          includeDetailedAnalysis: true,
          generateVisualizations: true
        });
        if (aiResult.success) {
          setAiAnalysisResults(aiResult.analysisResults);
          logger.info("✅ Análise AI Brain concluída", {
            analysisId: aiResult.analysisResults?.analysisId || "unknown"
          });
          try {
            await storeSessionData({
              sessionId: sessionIdRef.current,
              gameType,
              multisensoryReport: report.report,
              aiAnalysis: aiResult.analysisResults,
              sessionData,
              timestamp: (/* @__PURE__ */ new Date()).toISOString()
            });
            logger.info("💾 Dados da sessão armazenados com sucesso");
          } catch (storageError) {
            logger.warn("⚠️ Falha ao armazenar dados da sessão", {
              error: storageError.message
            });
          }
        } else {
          logger.warn("⚠️ Falha na análise AI Brain", {
            error: aiResult.error
          });
        }
      }
      setCurrentSession(null);
      setIsInitialized(false);
      setMultisensoryData(null);
      sessionIdRef.current = null;
      logger.info("✅ Sessão multissensorial finalizada (DIRECT)", {
        type: "session_finalized",
        sessionId: sessionIdRef.current,
        gameType,
        reportGenerated: report.success,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      });
      return report.success ? report.report : null;
    } catch (err) {
      setError(err);
      logger.error("❌ Erro ao finalizar sessão multissensorial", {
        type: "session_finalize_error",
        error: err.message,
        sessionId: sessionIdRef.current,
        gameType
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [isInitialized, multisensoryCollector, gameType, stopAutoUpdate, logger]);
  const getRecommendations = reactExports.useCallback(async () => {
    if (!multisensoryData || !sessionIdRef.current) return [];
    try {
      const basicRecommendations = [];
      if (multisensoryData.mobileSensors) {
        const sensorData = multisensoryData.mobileSensors;
        if (sensorData.accelerometer) {
          const movement = sensorData.accelerometer;
          if (movement.variability > 0.8) {
            basicRecommendations.push("Considere exercícios de estabilização motora");
          }
          if (movement.avgMagnitude < 0.2) {
            basicRecommendations.push("Tente atividades com mais movimentação");
          }
        }
        if (sensorData.gyroscope) {
          const orientation = sensorData.gyroscope;
          if (orientation.stability < 0.5) {
            basicRecommendations.push("Pratique exercícios de orientação espacial");
          }
        }
        if (sensorData.touch) {
          const touch = sensorData.touch;
          if (touch.pressure && touch.pressure.average > 0.8) {
            basicRecommendations.push("Experimente toques mais suaves na tela");
          }
          if (touch.frequency < 0.3) {
            basicRecommendations.push("Aumente a frequência de interações");
          }
        }
      }
      if (multisensoryData.neurodivergencePatterns) {
        const patterns = multisensoryData.neurodivergencePatterns;
        if (patterns.stimmingIndicators && patterns.stimmingIndicators.level > 0.7) {
          basicRecommendations.push("Considere pausas regulares para autorregulação");
        }
        if (patterns.sensorySeekingLevel > 0.8) {
          basicRecommendations.push("Experimente atividades com estímulos sensoriais variados");
        }
        if (patterns.sensoryAvoidanceLevel > 0.7) {
          basicRecommendations.push("Reduza estímulos sensoriais intensos");
        }
      }
      return basicRecommendations;
    } catch (err) {
      logger.error("❌ Erro ao gerar recomendações", {
        type: "recommendations_error",
        error: err.message,
        gameType
      });
      return [
        "Continue praticando para melhorar suas habilidades",
        "Experimente ajustar a velocidade do jogo",
        "Mantenha uma postura confortável durante o jogo"
      ];
    }
  }, [multisensoryData, multisensoryCollector, gameType, logger]);
  const getSensorData = reactExports.useCallback(async () => {
    if (!isInitialized || !sessionIdRef.current) return null;
    try {
      const currentMetrics = await multisensoryCollector.getCurrentMetrics();
      return currentMetrics ? currentMetrics.mobileSensors : null;
    } catch (err) {
      logger.error("❌ Erro ao obter dados dos sensores", {
        type: "sensor_data_error",
        error: err.message,
        gameType
      });
      return null;
    }
  }, [isInitialized, multisensoryCollector, gameType, logger]);
  const getNeurodivergenceMetrics = reactExports.useCallback(async () => {
    if (!isInitialized || !sessionIdRef.current) return null;
    try {
      const currentMetrics = await multisensoryCollector.getCurrentMetrics();
      return currentMetrics ? currentMetrics.neurodivergencePatterns : null;
    } catch (err) {
      logger.error("❌ Erro ao obter métricas de neurodivergência", {
        type: "neurodivergence_metrics_error",
        error: err.message,
        gameType
      });
      return null;
    }
  }, [isInitialized, multisensoryCollector, gameType, logger]);
  reactExports.useEffect(() => {
    return () => {
      stopAutoUpdate();
      if (multisensoryCollector && isInitialized) {
        multisensoryCollector.stopMetricsCollection().catch((err) => {
          logger.error("❌ Erro ao limpar sessão no cleanup", { error: err.message });
        });
      }
    };
  }, [stopAutoUpdate, multisensoryCollector, isInitialized, logger]);
  return {
    // Estado
    isInitialized,
    currentSession,
    multisensoryData,
    sessionReport,
    isLoading,
    error,
    // Métodos principais
    initializeSession,
    recordInteraction,
    finalizeSession,
    // Análise e recomendações
    analyzePatterns,
    getRecommendations,
    getSensorData,
    getNeurodivergenceMetrics,
    // Controle de atualizações
    updateMultisensoryData,
    startAutoUpdate,
    stopAutoUpdate,
    // Integração com AI Brain
    aiAnalysisResults,
    getAiBrainReport: () => aiBrainIntegrator.getLatestReport(),
    // Dados brutos (para depuração)
    collector: defaultOptions.logLevel === "debug" ? multisensoryCollector : null,
    sessionId: sessionIdRef.current
  };
}
const useTherapeuticOrchestrator = (options = {}) => {
  const systemContext = reactExports.useContext(SystemContext);
  const integratedSystem = systemContext?.system;
  const initUserId = options.userId && options.userId !== "anonymous" && options.userId !== "" ? options.userId : null;
  const [userId, setUserId] = reactExports.useState(initUserId);
  const [loading, setLoading] = reactExports.useState(false);
  const [error, setError] = reactExports.useState(null);
  const [recommendations, setRecommendations] = reactExports.useState([]);
  const [therapeuticData, setTherapeuticData] = reactExports.useState(null);
  const sessionStartTimeRef = reactExports.useRef(Date.now());
  const supportedGames = [
    "ImageAssociation",
    "MemoryGame",
    "MusicalSequence",
    "PadroesVisuais",
    "ContagemNumeros",
    "PatternMatching",
    "SequenceLearning",
    "CreativePainting",
    "ColorMatch",
    "LetterRecognition",
    "QuebraCabeca"
  ];
  const validateGameMetrics = reactExports.useCallback((gameId, metrics) => {
    try {
      const normalizedGameId = normalizeGameId(gameId);
      if (!supportedGames.includes(normalizedGameId)) {
        throw new Error(`Jogo não suportado: ${gameId} (normalizado: ${normalizedGameId}). Jogos suportados: ${supportedGames.join(", ")}`);
      }
      const validatedMetrics = {
        ...metrics,
        gameId: normalizedGameId,
        sessionId: metrics.sessionId || `session_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
        childId: metrics.childId || userId || `child_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`,
        sessionDuration: metrics.sessionDuration || calculateSessionDuration(metrics),
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        gameName: normalizedGameId
      };
      switch (normalizedGameId) {
        case "ImageAssociation":
          if (!metrics.category || !metrics.correct) {
            throw new Error("Campos category e correct são obrigatórios para ImageAssociation");
          }
          break;
        case "MemoryGame":
          if (!metrics.pairFound || !metrics.cardPosition) {
            throw new Error("Campos pairFound e cardPosition são obrigatórios para MemoryGame");
          }
          break;
        case "MusicalSequence":
          if (!metrics.sequenceLength || !metrics.expectedResponseTime) {
            throw new Error("Campos sequenceLength e expectedResponseTime são obrigatórios para MusicalSequence");
          }
          break;
        case "PadroesVisuais":
          if (!metrics.patternComplexity || !metrics.correct) {
            throw new Error("Campos patternComplexity e correct são obrigatórios para PadroesVisuais");
          }
          break;
        case "ContagemNumeros":
          if (!metrics.score || !metrics.correct) {
            throw new Error("Campos score e correct são obrigatórios para ContagemNumeros");
          }
          break;
        case "PatternMatching":
          if (!metrics.correctMatch || !metrics.patternComplexity) {
            throw new Error("Campos correctMatch e patternComplexity são obrigatórios para PatternMatching");
          }
          break;
        case "SequenceLearning":
          if (!metrics.isSequence || !metrics.sequenceLength) {
            throw new Error("Campos isSequence e sequenceLength são obrigatórios para SequenceLearning");
          }
          break;
        case "CreativePainting":
          if (!metrics.strokes || !metrics.creationTime || !metrics.colorDiversity) {
            throw new Error("Campos strokes, creationTime e colorDiversity são obrigatórios para CreativePainting");
          }
          break;
        case "ColorMatch":
          if (!metrics.correct || !metrics.colorSelected) {
            throw new Error("Campos correct e colorSelected são obrigatórios para ColorMatch");
          }
          break;
        case "LetterRecognition":
          if (!metrics.correct || !metrics.letterShown) {
            throw new Error("Campos correct e letterShown são obrigatórios para LetterRecognition");
          }
          break;
        case "QuebraCabeca":
          if (!metrics.piecesMoved || !metrics.completionRate) {
            throw new Error("Campos piecesMoved e completionRate são obrigatórios para QuebraCabeca");
          }
          break;
        default:
          throw new Error("Jogo não reconhecido durante validação");
      }
      return validatedMetrics;
    } catch (error2) {
      console.error("❌ Erro na validação de métricas:", { gameId, error: error2.message });
      throw error2;
    }
  }, [userId, supportedGames]);
  const checkSystem = reactExports.useCallback(() => {
    if (!integratedSystem?.systemOrchestrator) {
      throw new Error("SystemOrchestrator não está disponível no SystemContext");
    }
  }, [integratedSystem]);
  const calculateSessionDuration = (metrics) => {
    try {
      if (metrics?.sessionStartTime) {
        return (Date.now() - new Date(metrics.sessionStartTime).getTime()) / 1e3;
      }
      if (sessionStartTimeRef.current) {
        return (Date.now() - sessionStartTimeRef.current) / 1e3;
      }
      return 0;
    } catch (error2) {
      console.error("❌ Erro ao calcular duração da sessão:", { error: error2.message });
      return 0;
    }
  };
  const setUserContext = reactExports.useCallback((id) => {
    if (id && typeof id === "string") {
      setUserId(id);
      console.log("👤 UserId definido:", { userId: id });
    } else {
      console.warn("⚠️ ID de usuário inválido:", { id });
      setError("ID de usuário inválido");
    }
  }, []);
  const processGameMetrics = reactExports.useCallback(async (gameId, metrics, userIdOverride = null) => {
    try {
      checkSystem();
      setLoading(true);
      setError(null);
      const userIdToUse = userIdOverride || userId;
      if (!userIdToUse) {
        throw new Error("ID do usuário não especificado");
      }
      const validatedMetrics = validateGameMetrics(gameId, metrics);
      console.log("🧠 Processando métricas:", {
        gameId,
        sessionId: validatedMetrics.sessionId,
        userId: validatedMetrics.childId,
        sessionDuration: validatedMetrics.sessionDuration
      });
      const result = await integratedSystem.processGameMetrics(
        validatedMetrics.childId,
        gameId,
        validatedMetrics
      );
      if (result.success) {
        setRecommendations(result.recommendations || []);
        setTherapeuticData((prev) => ({
          ...prev || {},
          insights: result.insights || [],
          specificAnalysis: result.specificAnalysis || {},
          lastUpdate: (/* @__PURE__ */ new Date()).toISOString()
        }));
        console.log("✅ Métricas processadas com sucesso:", { gameId });
      } else {
        throw new Error(result.error || "Erro desconhecido no processamento de métricas");
      }
      return {
        success: true,
        data: result,
        recommendations: result.recommendations || [],
        insights: result.insights || []
      };
    } catch (err) {
      setError(err.message);
      console.error("❌ Erro ao processar métricas de jogo:", { gameId, error: err.message });
      return {
        success: false,
        error: err.message,
        recommendations: [],
        insights: []
      };
    } finally {
      setLoading(false);
    }
  }, [integratedSystem, userId, checkSystem, validateGameMetrics]);
  const getRecommendations = reactExports.useCallback(async (userIdOverride = null) => {
    try {
      checkSystem();
      setLoading(true);
      setError(null);
      const userIdToUse = userIdOverride || userId;
      if (!userIdToUse || userIdToUse === "anonymous" || userIdToUse === "") {
        console.log("⚠️ UserId inválido para recomendações terapêuticas:", { userId: userIdToUse });
        return {
          success: false,
          error: "ID do usuário não especificado ou inválido",
          recommendations: [],
          overview: {}
        };
      }
      const result = await integratedSystem.getTherapeuticRecommendations(userIdToUse);
      if (result.success && result.hasData) {
        setRecommendations(result.recommendations || []);
        setTherapeuticData((prev) => ({
          ...prev || {},
          overview: result.overview || {},
          therapyMode: result.therapyMode || "standard",
          lastUpdate: (/* @__PURE__ */ new Date()).toISOString()
        }));
        console.log("✅ Recomendações obtidas com sucesso:", { userId: userIdToUse });
        return {
          success: true,
          recommendations: result.recommendations || [],
          overview: result.overview || {},
          therapyMode: result.therapyMode || "standard"
        };
      } else {
        throw new Error(result.error || "Nenhuma recomendação disponível");
      }
    } catch (err) {
      setError(err.message);
      console.error("❌ Erro ao obter recomendações terapêuticas:", { userId: userIdOverride || userId, error: err.message });
      return {
        success: false,
        error: err.message,
        recommendations: [],
        overview: {}
      };
    } finally {
      setLoading(false);
    }
  }, [integratedSystem, userId, checkSystem]);
  const processCategorizedMetrics = reactExports.useCallback(async (categorizedMetrics) => {
    try {
      checkSystem();
      setLoading(true);
      setError(null);
      if (!userId) {
        throw new Error("ID do usuário não especificado");
      }
      if (!categorizedMetrics || typeof categorizedMetrics !== "object") {
        throw new Error("Métricas categorizadas inválidas");
      }
      const metrics = {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        categories: categorizedMetrics,
        meta: {
          source: "categorized-metrics",
          version: "3.2.2"
        }
      };
      const result = await integratedSystem.dispatchEvent("therapeutic:process-categorized-metrics", {
        userId,
        metrics
      });
      if (result.success) {
        setTherapeuticData((prev) => ({
          ...prev || {},
          categorizedInsights: result.insights || [],
          lastUpdate: (/* @__PURE__ */ new Date()).toISOString()
        }));
        console.log("✅ Métricas categorizadas processadas:", { userId });
      }
      return {
        success: result.success,
        insights: result.insights || [],
        error: result.error || null
      };
    } catch (err) {
      setError(err.message);
      console.error("❌ Erro ao processar métricas categorizadas:", { userId, error: err.message });
      return {
        success: false,
        error: err.message,
        insights: []
      };
    } finally {
      setLoading(false);
    }
  }, [integratedSystem, userId, checkSystem]);
  const getOrchestratorStats = reactExports.useCallback(async () => {
    try {
      checkSystem();
      const stats = await integratedSystem.getOrchestratorStats();
      console.log("📊 Estatísticas do orquestrador obtidas:", stats);
      return {
        success: true,
        stats
      };
    } catch (err) {
      console.error("❌ Erro ao obter estatísticas do orquestrador:", { error: err.message });
      return {
        success: false,
        error: err.message,
        stats: {}
      };
    }
  }, [integratedSystem, checkSystem]);
  const isGameSupported = reactExports.useCallback((gameId) => {
    const supported = supportedGames.includes(gameId);
    if (!supported) {
      console.warn("⚠️ Jogo não suportado:", { gameId, supportedGames });
    }
    return supported;
  }, [supportedGames]);
  const normalizeGameId = reactExports.useCallback((gameId) => {
    if (!gameId || gameId === "undefined") return "unknown";
    const cleanGameId = gameId.replace(/-\w+$/, "");
    const gameMapping = {
      "color-match": "ColorMatch",
      "memory-game": "MemoryGame",
      "musical-sequence": "MusicalSequence",
      "padroes-visuais": "PadroesVisuais",
      "contagem-numeros": "ContagemNumeros",
      "number-counting": "ContagemNumeros",
      "pattern-matching": "PatternMatching",
      "sequence-learning": "SequenceLearning",
      "creative-painting": "CreativePainting",
      "image-association": "ImageAssociation",
      "letter-recognition": "LetterRecognition",
      "quebra-cabeca": "QuebraCabeca"
    };
    return gameMapping[cleanGameId] || cleanGameId;
  }, []);
  const clearState = reactExports.useCallback(() => {
    setUserId(null);
    setError(null);
    setRecommendations([]);
    setTherapeuticData(null);
    sessionStartTimeRef.current = Date.now();
    console.log("🧹 Estado do hook limpo");
  }, []);
  reactExports.useEffect(() => {
    if (userId && userId !== "anonymous" && userId !== "" && integratedSystem?.systemOrchestrator) {
      getRecommendations().catch((err) => {
        console.warn("⚠️ Não foi possível carregar recomendações iniciais:", { error: err.message });
      });
    }
  }, [userId, integratedSystem, getRecommendations]);
  reactExports.useEffect(() => {
    console.log("🎮 Hook useTherapeuticOrchestrator inicializado:", {
      userId,
      hasSystem: !!integratedSystem?.systemOrchestrator,
      supportedGames
    });
  }, []);
  return {
    // Estado
    userId,
    loading,
    error,
    recommendations,
    therapeuticData,
    supportedGames,
    // Métodos
    setUserContext,
    processGameMetrics,
    getRecommendations,
    processCategorizedMetrics,
    getOrchestratorStats,
    isGameSupported,
    clearState,
    // Estado do sistema
    hasSystem: !!integratedSystem?.systemOrchestrator
  };
};
export {
  useUnifiedGameLogic as a,
  useMultisensoryIntegration as b,
  useTherapeuticOrchestrator as c,
  useAccessibility as u
};
//# sourceMappingURL=hooks-NJkOkh4y.js.map
