{"version": 3, "file": "UserProfiles-CVPlDxjR.js", "sources": ["../../src/components/pages/UserProfiles/UserProfiles.jsx"], "sourcesContent": ["/**\r\n * @file UserProfiles.jsx\r\n * @description Página de gerenciamento de perfis de usuário\r\n * @version 3.0.0\r\n */\r\n\r\nimport React, { useState, useEffect } from 'react'\r\nimport styles from './UserProfiles.module.css'\r\n\r\nfunction UserProfiles() {\r\n  const [profiles, setProfiles] = useState([])\r\n  const [activeProfile, setActiveProfile] = useState(null)\r\n  const [showCreateForm, setShowCreateForm] = useState(false)\r\n  const [newProfile, setNewProfile] = useState({\r\n    name: '',\r\n    age: '',\r\n    avatar: '👶',\r\n    preferences: {\r\n      theme: 'default',\r\n      difficulty: 'easy',\r\n      soundEnabled: true,\r\n      animationsEnabled: true\r\n    }\r\n  })\r\n  \r\n  // Novo estado para estatísticas dos perfis\r\n  const [profileStats, setProfileStats] = useState({})\r\n  const [loadingStats, setLoadingStats] = useState(false)\r\n\r\n  // Função para buscar estatísticas do perfil da API\r\n  const fetchProfileStats = async (profileId) => {\r\n    if (!profileId) return\r\n    \r\n    try {\r\n      setLoadingStats(true)\r\n      \r\n      // Buscar estatísticas de sessões de jogos\r\n      const response = await fetch(`/api/metrics/game-sessions?userId=${profileId}`)\r\n      if (response.ok) {\r\n        const data = await response.json()\r\n        \r\n        // Calcular estatísticas\r\n        const stats = {\r\n          gamesPlayed: data.sessions?.length || 0,\r\n          totalTime: data.sessions?.reduce((total, session) => total + (session.duration || 0), 0) || 0,\r\n          lastPlayed: data.sessions?.length > 0 ? \r\n            Math.max(...data.sessions.map(s => new Date(s.timestamp).getTime())) : null,\r\n          favoriteGames: data.favoriteGames || [],\r\n          achievements: data.achievements || [],\r\n          avgPerformance: data.avgPerformance || 0\r\n        }\r\n        \r\n        setProfileStats(prev => ({\r\n          ...prev,\r\n          [profileId]: stats\r\n        }))\r\n\r\n        // 🎯 NOVO: Vincular perfil ao usuário do dashboard (se logado)\r\n        const dashboardUser = localStorage.getItem('userData')\r\n        if (dashboardUser) {\r\n          try {\r\n            const user = JSON.parse(dashboardUser)\r\n            const linkResponse = await fetch('/api/dashboard/link-profile', {\r\n              method: 'POST',\r\n              headers: { \r\n                'Content-Type': 'application/json',\r\n                'Authorization': `Bearer ${localStorage.getItem('authToken')}`\r\n              },\r\n              body: JSON.stringify({ \r\n                profileId, \r\n                dashboardUserId: user.id,\r\n                profileData: { gamesPlayed: stats.gamesPlayed, totalTime: stats.totalTime }\r\n              })\r\n            })\r\n            if (linkResponse.ok) {\r\n              console.log(`✅ Perfil ${profileId} vinculado ao usuário do dashboard ${user.email}`)\r\n            }\r\n          } catch (linkError) {\r\n            console.warn('Falha ao vincular perfil ao dashboard:', linkError)\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Erro ao buscar estatísticas do perfil:', error)\r\n    } finally {\r\n      setLoadingStats(false)\r\n    }\r\n  }\r\n\r\n  // 🎯 NOVO: Carregar perfis localmente (sem autenticação)\r\n  useEffect(() => {\r\n    const loadProfiles = () => {\r\n      // Carregar perfis do localStorage (família local)\r\n      const savedProfiles = JSON.parse(localStorage.getItem('betina_profiles') || '[]')\r\n      setProfiles(savedProfiles)\r\n      \r\n      const activeId = localStorage.getItem('betina_active_profile')\r\n      if (activeId && savedProfiles.length > 0) {\r\n        const active = savedProfiles.find(p => p.id === activeId)\r\n        setActiveProfile(active)\r\n      }\r\n      \r\n      // Buscar estatísticas para todos os perfis\r\n      savedProfiles.forEach(profile => {\r\n        fetchProfileStats(profile.id)\r\n      })\r\n    }\r\n    \r\n    loadProfiles()\r\n  }, [])\r\n\r\n  // Atualizar estatísticas quando perfil ativo mudar\r\n  useEffect(() => {\r\n    if (activeProfile) {\r\n      fetchProfileStats(activeProfile.id)\r\n    }\r\n  }, [activeProfile])\r\n\r\n  // 🎯 NOVO: Salvar perfis localmente (sem API)\r\n  const saveProfiles = (newProfiles) => {\r\n    setProfiles(newProfiles)\r\n    localStorage.setItem('betina_profiles', JSON.stringify(newProfiles))\r\n  }\r\n\r\n  // Criar novo perfil\r\n  const handleCreateProfile = () => {\r\n    if (!newProfile.name.trim()) {\r\n      alert('Por favor, digite um nome para o perfil')\r\n      return\r\n    }\r\n\r\n    const profile = {\r\n      id: Date.now().toString(),\r\n      ...newProfile,\r\n      createdAt: new Date().toISOString(),\r\n      lastUsed: new Date().toISOString(),\r\n      gamesPlayed: 0,\r\n      totalTime: 0\r\n    }\r\n\r\n    const updatedProfiles = [...profiles, profile]\r\n    saveProfiles(updatedProfiles)\r\n    \r\n    setNewProfile({\r\n      name: '',\r\n      age: '',\r\n      avatar: '👶',\r\n      preferences: {\r\n        theme: 'default',\r\n        difficulty: 'easy',\r\n        soundEnabled: true,\r\n        animationsEnabled: true\r\n      }\r\n    })\r\n    setShowCreateForm(false)\r\n  }\r\n\r\n  // 🎯 NOVO: Selecionar perfil ativo localmente\r\n  const selectProfile = (profile) => {\r\n    setActiveProfile(profile)\r\n    localStorage.setItem('betina_active_profile', profile.id)\r\n    \r\n    // Atualizar último uso\r\n    const updatedProfiles = profiles.map(p => \r\n      p.id === profile.id \r\n        ? { ...p, lastUsed: new Date().toISOString() }\r\n        : p\r\n    )\r\n    saveProfiles(updatedProfiles)\r\n\r\n    // Buscar estatísticas do novo perfil ativo\r\n    fetchProfileStats(profile.id)\r\n  }\r\n\r\n  // 🎯 NOVO: Deletar perfil localmente\r\n  const deleteProfile = (profileId) => {\r\n    if (confirm('Tem certeza que deseja deletar este perfil?')) {\r\n      const updatedProfiles = profiles.filter(p => p.id !== profileId)\r\n      saveProfiles(updatedProfiles)\r\n      \r\n      if (activeProfile?.id === profileId) {\r\n        setActiveProfile(null)\r\n        localStorage.removeItem('betina_active_profile')\r\n      }\r\n    }\r\n  }\r\n\r\n  const avatarOptions = ['👶', '👧', '👦', '🧒', '👨', '👩', '🐱', '🐶', '🦋', '🌟']\r\n  return (\r\n    <div className={styles.container}>\r\n      {/* Header Principal Centralizado */}\r\n      <div className={styles.header}>\r\n        <h1 className={styles.mainTitle}>� Gerenciar Perfis</h1>\r\n        <p className={styles.subtitle}>\r\n          Crie e gerencie perfis personalizados para toda a família\r\n        </p>\r\n      </div>\r\n\r\n      {/* Perfil Ativo */}\r\n      {activeProfile && (\r\n        <section className={styles.section}>\r\n          <h2 className={styles.sectionTitle}>\r\n            <span>⭐</span>\r\n            Perfil Ativo\r\n          </h2>\r\n          <div className={styles.activeProfileCard}>\r\n            <div className={styles.activeProfileAvatar}>{activeProfile.avatar}</div>\r\n            <div className={styles.activeProfileInfo}>\r\n              <h3 className={styles.activeProfileName}>{activeProfile.name}</h3>\r\n              <div className={styles.activeProfileStats}>\r\n                <div className={styles.statItem}>\r\n                  <span className={styles.statIcon}>🎂</span>\r\n                  <span>{activeProfile.age ? `${activeProfile.age} anos` : 'Idade não informada'}</span>\r\n                </div>\r\n                <div className={styles.statItem}>\r\n                  <span className={styles.statIcon}>🎮</span>\r\n                  <span>\r\n                    {loadingStats ? 'Carregando...' : \r\n                     `${profileStats[activeProfile.id]?.gamesPlayed || 0} jogos jogados`}\r\n                  </span>\r\n                </div>\r\n                <div className={styles.statItem}>\r\n                  <span className={styles.statIcon}>⏰</span>\r\n                  <span>\r\n                    {profileStats[activeProfile.id]?.totalTime ? \r\n                     `${Math.round(profileStats[activeProfile.id].totalTime / 1000 / 60)} min jogados` :\r\n                     `Último acesso: ${new Date(activeProfile.lastUsed).toLocaleDateString()}`}\r\n                  </span>\r\n                </div>\r\n                {profileStats[activeProfile.id]?.favoriteGames?.length > 0 && (\r\n                  <div className={styles.statItem}>\r\n                    <span className={styles.statIcon}>⭐</span>\r\n                    <span>Jogo favorito: {profileStats[activeProfile.id].favoriteGames[0]}</span>\r\n                  </div>\r\n                )}\r\n                {profileStats[activeProfile.id]?.avgPerformance > 0 && (\r\n                  <div className={styles.statItem}>\r\n                    <span className={styles.statIcon}>📊</span>\r\n                    <span>Performance média: {Math.round(profileStats[activeProfile.id].avgPerformance)}%</span>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </section>\r\n      )}\r\n\r\n      {/* Seção: Todos os Perfis */}\r\n      <section className={styles.section}>\r\n        <div className={styles.profilesGrid}>\r\n          {profiles.map(profile => (\r\n            <div \r\n              key={profile.id}\r\n              className={`${styles.profileCard} ${activeProfile?.id === profile.id ? styles.active : ''}`}\r\n              onClick={() => selectProfile(profile)}\r\n            >\r\n              <div className={styles.profileAvatar}>{profile.avatar}</div>\r\n              <div className={styles.profileInfo}>\r\n                <h4 className={styles.profileName}>{profile.name}</h4>\r\n                <p className={styles.profileAge}>\r\n                  {profile.age ? `${profile.age} anos` : 'Idade não informada'}\r\n                </p>\r\n                <p className={styles.profileStats}>\r\n                  {profile.gamesPlayed || 0} jogos jogados\r\n                </p>\r\n              </div>\r\n              <button \r\n                className={styles.profileDeleteBtn}\r\n                onClick={(e) => {\r\n                  e.stopPropagation()\r\n                  deleteProfile(profile.id)\r\n                }}\r\n                title=\"Deletar perfil\"\r\n              >\r\n                🗑️\r\n              </button>\r\n            </div>\r\n          ))}\r\n\r\n          {/* Botão Adicionar Perfil */}\r\n          <div \r\n            className={`${styles.profileCard} ${styles.addProfileCard}`}\r\n            onClick={() => setShowCreateForm(true)}\r\n          >\r\n            <div className={styles.addProfileIcon}>➕</div>\r\n            <p>Adicionar Perfil</p>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Seção: Informações */}\r\n      <section className={styles.section}>\r\n        <h2 className={styles.sectionTitle}>\r\n          <span>💡</span>\r\n          Como Funciona?\r\n        </h2>\r\n        <div className={styles.sectionContent}>\r\n          <p>\r\n            Os <strong>perfis de usuário</strong> permitem que cada criança da família \r\n            tenha sua própria experiência personalizada. As métricas e progressos são \r\n            automaticamente vinculados ao responsável que fizer login no dashboard.\r\n          </p>\r\n          \r\n          <ul className={styles.benefitsList}>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>📊</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Progresso Individual:</strong> Cada criança mantém seu próprio histórico de jogos e conquistas\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>👨‍👩‍👧‍👦</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Fácil para Crianças:</strong> Interface simples, sem necessidade de login ou senhas\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>�</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Métricas no Dashboard:</strong> Pais/responsáveis acessam relatórios detalhados via dashboard\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>�</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Dados Seguros:</strong> Todas as informações ficam salvas localmente no seu dispositivo\r\n              </span>\r\n            </li>\r\n          </ul>        </div>\r\n      </section>\r\n\r\n      {/* Formulário de Criação */}\r\n      {showCreateForm && (\r\n        <div className={styles.createProfileOverlay}>\r\n          <div className={styles.createProfileForm}>\r\n            <div className={styles.formHeader}>\r\n              <h3 className={styles.formTitle}>➕ Criar Novo Perfil</h3>\r\n              <p className={styles.formSubtitle}>Adicione um novo membro da família</p>\r\n            </div>\r\n            \r\n            <div className={styles.formGroup}>\r\n              <label htmlFor=\"profile-name\" className={styles.formLabel}>Nome:</label>\r\n              <input\r\n                type=\"text\"\r\n                id=\"profile-name\"\r\n                name=\"profile-name\"\r\n                value={newProfile.name}\r\n                onChange={(e) => setNewProfile({...newProfile, name: e.target.value})}\r\n                placeholder=\"Digite o nome...\"\r\n                maxLength={20}\r\n                className={styles.input}\r\n                autoComplete=\"given-name\"\r\n              />\r\n            </div>\r\n\r\n            <div className={styles.formGroup}>\r\n              <label htmlFor=\"profile-age\" className={styles.formLabel}>Idade (opcional):</label>\r\n              <input\r\n                type=\"number\"\r\n                id=\"profile-age\"\r\n                name=\"profile-age\"\r\n                value={newProfile.age}\r\n                onChange={(e) => setNewProfile({...newProfile, age: e.target.value})}\r\n                min=\"1\"\r\n                max=\"100\"\r\n                placeholder=\"Ex: 5\"\r\n                className={styles.input}\r\n                autoComplete=\"age\"\r\n              />\r\n            </div>\r\n\r\n            <div className={styles.formGroup}>\r\n              <label htmlFor=\"avatar-selector\" className={styles.formLabel}>Escolha um Avatar:</label>\r\n              <div className={styles.avatarSelector} id=\"avatar-selector\" role=\"radiogroup\" aria-labelledby=\"avatar-selector\">\r\n                {avatarOptions.map((avatar, index) => (\r\n                  <button\r\n                    key={avatar}\r\n                    type=\"button\"\r\n                    role=\"radio\"\r\n                    aria-checked={newProfile.avatar === avatar}\r\n                    aria-label={`Avatar ${index + 1}: ${avatar}`}\r\n                    className={`${styles.avatarOption} ${newProfile.avatar === avatar ? styles.selected : ''}`}\r\n                    onClick={() => setNewProfile({...newProfile, avatar})}\r\n                  >\r\n                    {avatar}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            <div className={styles.formActions}>\r\n              <button \r\n                className={`${styles.btn} ${styles.btnPrimary}`}\r\n                onClick={handleCreateProfile}\r\n              >\r\n                ✅ Criar Perfil\r\n              </button>\r\n              <button \r\n                className={`${styles.btn} ${styles.btnSecondary}`}\r\n                onClick={() => setShowCreateForm(false)}\r\n              >\r\n                ❌ Cancelar\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default UserProfiles\r\n"], "names": ["UserProfiles", "profiles", "setProfiles", "useState", "activeProfile", "setActiveProfile", "showCreateForm", "setShowCreateForm", "newProfile", "setNewProfile", "name", "age", "avatar", "preferences", "theme", "difficulty", "soundEnabled", "animationsEnabled", "profileStats", "setProfileStats", "loadingStats", "setLoadingStats", "fetchProfileStats", "profileId", "response", "fetch", "ok", "data", "json", "stats", "gamesPlayed", "sessions", "length", "totalTime", "reduce", "total", "session", "duration", "lastPlayed", "Math", "max", "map", "s", "Date", "timestamp", "getTime", "favoriteGames", "achievements", "avgPerformance", "prev", "dashboardUser", "localStorage", "getItem", "user", "JSON", "parse", "linkResponse", "method", "headers", "body", "stringify", "dashboardUserId", "id", "profileData", "console", "log", "email", "linkError", "warn", "error", "useEffect", "loadProfiles", "savedProfiles", "activeId", "active", "find", "p", "for<PERSON>ach", "profile", "saveProfiles", "newProfiles", "setItem", "handleCreateProfile", "trim", "alert", "now", "toString", "createdAt", "toISOString", "lastUsed", "updatedProfiles", "selectProfile", "deleteProfile", "confirm", "filter", "removeItem", "avatarOptions", "styles", "container", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "header", "mainTitle", "subtitle", "section", "sectionTitle", "activeProfileCard", "activeProfileAvatar", "activeProfileInfo", "activeProfileName", "activeProfileStats", "statItem", "statIcon", "round", "toLocaleDateString", "profilesGrid", "profileCard", "<PERSON><PERSON><PERSON><PERSON>", "profileInfo", "profileName", "profileAge", "profileDeleteBtn", "e", "stopPropagation", "addProfileCard", "addProfileIcon", "sectionContent", "benefitsList", "benefitItem", "benefitEmoji", "benefitText", "createProfileOverlay", "createProfileForm", "formHeader", "formTitle", "formSubtitle", "formGroup", "formLabel", "target", "value", "input", "avatarSelector", "index", "avatarOption", "selected", "formActions", "btn", "btnPrimary", "btnSecondary"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,SAASA,eAAe;AACtB,QAAM,CAACC,UAAUC,WAAW,IAAIC,aAAAA,SAAS,CAAA,CAAE;AAC3C,QAAM,CAACC,eAAeC,gBAAgB,IAAIF,aAAAA,SAAS,IAAI;AACvD,QAAM,CAACG,gBAAgBC,iBAAiB,IAAIJ,aAAAA,SAAS,KAAK;AAC1D,QAAM,CAACK,YAAYC,aAAa,IAAIN,sBAAS;AAAA,IAC3CO,MAAM;AAAA,IACNC,KAAK;AAAA,IACLC,QAAQ;AAAA,IACRC,aAAa;AAAA,MACXC,OAAO;AAAA,MACPC,YAAY;AAAA,MACZC,cAAc;AAAA,MACdC,mBAAmB;AAAA,IAAA;AAAA,EACrB,CACD;AAGD,QAAM,CAACC,eAAcC,eAAe,IAAIhB,aAAAA,SAAS,CAAA,CAAE;AACnD,QAAM,CAACiB,cAAcC,eAAe,IAAIlB,aAAAA,SAAS,KAAK;AAGhDmB,QAAAA,oBAAoB,OAAOC,cAAc;AAC7C,QAAI,CAACA,UAAW;AAEZ,QAAA;AACFF,sBAAgB,IAAI;AAGpB,YAAMG,WAAW,MAAMC,MAAM,qCAAqCF,SAAS,EAAE;AAC7E,UAAIC,SAASE,IAAI;AACTC,cAAAA,OAAO,MAAMH,SAASI,KAAK;AAGjC,cAAMC,QAAQ;AAAA,UACZC,aAAaH,KAAKI,UAAUC,UAAU;AAAA,UACtCC,WAAWN,KAAKI,UAAUG,OAAO,CAACC,OAAOC,YAAYD,SAASC,QAAQC,YAAY,IAAI,CAAC,KAAK;AAAA,UAC5FC,YAAYX,KAAKI,UAAUC,SAAS,IAClCO,KAAKC,IAAI,GAAGb,KAAKI,SAASU,IAAIC,CAAK,MAAA,IAAIC,KAAKD,EAAEE,SAAS,EAAEC,SAAS,CAAC,IAAI;AAAA,UACzEC,eAAenB,KAAKmB,iBAAiB,CAAE;AAAA,UACvCC,cAAcpB,KAAKoB,gBAAgB,CAAE;AAAA,UACrCC,gBAAgBrB,KAAKqB,kBAAkB;AAAA,QACzC;AAEA7B,wBAAgB8B,CAAS,UAAA;AAAA,UACvB,GAAGA;AAAAA,UACH,CAAC1B,SAAS,GAAGM;AAAAA,QAAAA,EACb;AAGIqB,cAAAA,gBAAgBC,aAAaC,QAAQ,UAAU;AACrD,YAAIF,eAAe;AACb,cAAA;AACIG,kBAAAA,OAAOC,KAAKC,MAAML,aAAa;AAC/BM,kBAAAA,eAAe,MAAM/B,MAAM,+BAA+B;AAAA,cAC9DgC,QAAQ;AAAA,cACRC,SAAS;AAAA,gBACP,gBAAgB;AAAA,gBAChB,iBAAiB,UAAUP,aAAaC,QAAQ,WAAW,CAAC;AAAA,cAC9D;AAAA,cACAO,MAAML,KAAKM,UAAU;AAAA,gBACnBrC;AAAAA,gBACAsC,iBAAiBR,KAAKS;AAAAA,gBACtBC,aAAa;AAAA,kBAAEjC,aAAaD,MAAMC;AAAAA,kBAAaG,WAAWJ,MAAMI;AAAAA,gBAAAA;AAAAA,cACjE,CAAA;AAAA,YAAA,CACF;AACD,gBAAIuB,aAAa9B,IAAI;AACnBsC,sBAAQC,IAAI,YAAY1C,SAAS,sCAAsC8B,KAAKa,KAAK,EAAE;AAAA,YAAA;AAAA,mBAE9EC,WAAW;AACVC,oBAAAA,KAAK,0CAA0CD,SAAS;AAAA,UAAA;AAAA,QAClE;AAAA,MACF;AAAA,aAEKE,OAAO;AACNA,cAAAA,MAAM,0CAA0CA,KAAK;AAAA,IAAA,UACrD;AACRhD,sBAAgB,KAAK;AAAA,IAAA;AAAA,EAEzB;AAGAiD,eAAAA,UAAU,MAAM;AACd,UAAMC,eAAeA,MAAM;AAEzB,YAAMC,gBAAgBlB,KAAKC,MAAMJ,aAAaC,QAAQ,iBAAiB,KAAK,IAAI;AAChFlD,kBAAYsE,aAAa;AAEnBC,YAAAA,WAAWtB,aAAaC,QAAQ,uBAAuB;AACzDqB,UAAAA,YAAYD,cAAcxC,SAAS,GAAG;AACxC,cAAM0C,UAASF,cAAcG,KAAKC,CAAKA,MAAAA,EAAEd,OAAOW,QAAQ;AACxDpE,yBAAiBqE,OAAM;AAAA,MAAA;AAIzBF,oBAAcK,QAAQC,CAAW,YAAA;AAC/BxD,0BAAkBwD,QAAQhB,EAAE;AAAA,MAAA,CAC7B;AAAA,IACH;AAEa,iBAAA;AAAA,EACf,GAAG,EAAE;AAGLQ,eAAAA,UAAU,MAAM;AACd,QAAIlE,eAAe;AACjBkB,wBAAkBlB,cAAc0D,EAAE;AAAA,IAAA;AAAA,EACpC,GACC,CAAC1D,aAAa,CAAC;AAGlB,QAAM2E,eAAgBC,CAAgB,gBAAA;AACpC9E,gBAAY8E,WAAW;AACvB7B,iBAAa8B,QAAQ,mBAAmB3B,KAAKM,UAAUoB,WAAW,CAAC;AAAA,EACrE;AAGA,QAAME,sBAAsBA,MAAM;AAChC,QAAI,CAAC1E,WAAWE,KAAKyE,QAAQ;AAC3BC,YAAM,yCAAyC;AAC/C;AAAA,IAAA;AAGF,UAAMN,UAAU;AAAA,MACdhB,IAAInB,KAAK0C,IAAI,EAAEC,SAAS;AAAA,MACxB,GAAG9E;AAAAA,MACH+E,YAAW,oBAAI5C,KAAK,GAAE6C,YAAY;AAAA,MAClCC,WAAU,oBAAI9C,KAAK,GAAE6C,YAAY;AAAA,MACjC1D,aAAa;AAAA,MACbG,WAAW;AAAA,IACb;AAEA,UAAMyD,kBAAkB,CAAC,GAAGzF,UAAU6E,OAAO;AAC7CC,iBAAaW,eAAe;AAEd,kBAAA;AAAA,MACZhF,MAAM;AAAA,MACNC,KAAK;AAAA,MACLC,QAAQ;AAAA,MACRC,aAAa;AAAA,QACXC,OAAO;AAAA,QACPC,YAAY;AAAA,QACZC,cAAc;AAAA,QACdC,mBAAmB;AAAA,MAAA;AAAA,IACrB,CACD;AACDV,sBAAkB,KAAK;AAAA,EACzB;AAGA,QAAMoF,gBAAiBb,CAAY,YAAA;AACjCzE,qBAAiByE,OAAO;AACXG,iBAAAA,QAAQ,yBAAyBH,QAAQhB,EAAE;AAGxD,UAAM4B,kBAAkBzF,SAASwC,IAAImC,OACnCA,EAAEd,OAAOgB,QAAQhB,KACb;AAAA,MAAE,GAAGc;AAAAA,MAAGa,WAAU,oBAAI9C,KAAK,GAAE6C,YAAY;AAAA,QACzCZ,CACN;AACAG,iBAAaW,eAAe;AAG5BpE,sBAAkBwD,QAAQhB,EAAE;AAAA,EAC9B;AAGA,QAAM8B,gBAAiBrE,CAAc,cAAA;AAC/BsE,QAAAA,QAAQ,6CAA6C,GAAG;AAC1D,YAAMH,kBAAkBzF,SAAS6F,OAAOlB,CAAKA,MAAAA,EAAEd,OAAOvC,SAAS;AAC/DwD,mBAAaW,eAAe;AAExBtF,UAAAA,eAAe0D,OAAOvC,WAAW;AACnClB,yBAAiB,IAAI;AACrB8C,qBAAa4C,WAAW,uBAAuB;AAAA,MAAA;AAAA,IACjD;AAAA,EAEJ;AAEMC,QAAAA,gBAAgB,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AACjF,6CACG,OAAI,EAAA,WAAWC,OAAOC,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAE9B,OAAI,EAAA,WAAWL,OAAOM,QAAO,QAAA,MAAA,UAAA;AAAA,IAAAJ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC3B,MAAG,EAAA,WAAWL,OAAOO,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAL,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,oBAAkB,GAClD,sBAAA,cAAA,KAAA,EAAE,WAAWL,OAAOQ,UAAS,QAAA,MAAA,UAAA;AAAA,IAAAN,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,2DAE/B,CACF,GAGClG,iBACC,sBAAA,cAAC,WAAQ,EAAA,WAAW6F,OAAOS,SAAQ,QAAA,MAAA,UAAA;AAAA,IAAAP,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAChC,MAAG,EAAA,WAAWL,OAAOU,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAR,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EACjC,GAAA,sBAAA,cAAC,QAAI,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,GAAC,GAAO,cAEhB,GACA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAOW,mBAAkB,QAAA,MAAA,UAAA;AAAA,IAAAT,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACtC,OAAI,EAAA,WAAWL,OAAOY,qBAAoB,QAAA,MAAA,UAAA;AAAA,IAAAV,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAElG,cAAcQ,MAAO,GAClE,sBAAA,cAAC,OAAI,EAAA,WAAWqF,OAAOa,mBAAkB,QAAA,MAAA,UAAA;AAAA,IAAAX,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACtC,MAAG,EAAA,WAAWL,OAAOc,mBAAkB,QAAA,MAAA,UAAA;AAAA,IAAAZ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAElG,cAAcM,IAAK,GAC7D,sBAAA,cAAC,OAAI,EAAA,WAAWuF,OAAOe,oBAAmB,QAAA,MAAA,UAAA;AAAA,IAAAb,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACvC,OAAI,EAAA,WAAWL,OAAOgB,UAAS,QAAA,MAAA,UAAA;AAAA,IAAAd,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC7B,QAAK,EAAA,WAAWL,OAAOiB,UAAS,QAAA,MAAA,UAAA;AAAA,IAAAf,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAC,IAAE,uCACnC,QAAI,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAElG,cAAcO,MAAM,GAAGP,cAAcO,GAAG,UAAU,qBAAsB,CACjF,uCACC,OAAI,EAAA,WAAWsF,OAAOgB,UAAS,QAAA,MAAA,UAAA;AAAA,IAAAd,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC7B,QAAK,EAAA,WAAWL,OAAOiB,UAAS,QAAA,MAAA,UAAA;AAAA,IAAAf,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAC,IAAE,uCACnC,QAAI,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KACFlF,eAAe,kBACf,GAAGF,cAAad,cAAc0D,EAAE,GAAGhC,eAAe,CAAC,gBACtD,CACF,uCACC,OAAI,EAAA,WAAWmE,OAAOgB,UAAS,QAAA,MAAA,UAAA;AAAA,IAAAd,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC7B,QAAK,EAAA,WAAWL,OAAOiB,UAAS,QAAA,MAAA,UAAA;AAAA,IAAAf,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAC,GAAC,uCAClC,QAAI,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KACFpF,cAAad,cAAc0D,EAAE,GAAG7B,YAChC,GAAGM,KAAK4E,MAAMjG,cAAad,cAAc0D,EAAE,EAAE7B,YAAY,MAAO,EAAE,CAAC,iBACnE,kBAAkB,IAAIU,KAAKvC,cAAcqF,QAAQ,EAAE2B,oBAAoB,EAC1E,CACF,GACClG,cAAad,cAAc0D,EAAE,GAAGhB,eAAed,SAAS,yCACtD,OAAI,EAAA,WAAWiE,OAAOgB,UAAS,QAAA,MAAA,UAAA;AAAA,IAAAd,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC7B,QAAK,EAAA,WAAWL,OAAOiB,UAAS,QAAA,MAAA,UAAA;AAAA,IAAAf,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAC,GAAC,uCAClC,QAAI,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,mBAAgBpF,cAAad,cAAc0D,EAAE,EAAEhB,cAAc,CAAC,CAAE,CACxE,GAED5B,cAAad,cAAc0D,EAAE,GAAGd,iBAAiB,KAC/C,sBAAA,cAAA,OAAA,EAAI,WAAWiD,OAAOgB,UAAS,QAAA,MAAA,UAAA;AAAA,IAAAd,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC7B,QAAK,EAAA,WAAWL,OAAOiB,UAAS,QAAA,MAAA,UAAA;AAAA,IAAAf,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAC,IAAE,uCACnC,QAAI,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,uBAAoB/D,KAAK4E,MAAMjG,cAAad,cAAc0D,EAAE,EAAEd,cAAc,GAAE,GAAC,CACvF,CAEJ,CACF,CACF,CACF,GAID,sBAAA,cAAA,WAAA,EAAQ,WAAWiD,OAAOS,SAAQ,QAAA,MAAA,UAAA;AAAA,IAAAP,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAChC,OAAI,EAAA,WAAWL,OAAOoB,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAlB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EACjCrG,GAAAA,SAASwC,IAAIqC,CAAAA,YACX,sBAAA,cAAA,OAAA,EACC,KAAKA,QAAQhB,IACb,WAAW,GAAGmC,OAAOqB,WAAW,IAAIlH,eAAe0D,OAAOgB,QAAQhB,KAAKmC,OAAOvB,SAAS,EAAE,IACzF,SAAS,MAAMiB,cAAcb,OAAO,GAAE,QAAA,MAAA,UAAA;AAAA,IAAAqB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAErC,OAAI,EAAA,WAAWL,OAAOsB,eAAc,QAAA,MAAA,UAAA;AAAA,IAAApB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAExB,QAAQlE,MAAO,GACtD,sBAAA,cAAC,OAAI,EAAA,WAAWqF,OAAOuB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAArB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAChC,MAAG,EAAA,WAAWL,OAAOwB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAtB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAExB,QAAQpE,IAAK,GACjD,sBAAA,cAAC,KAAE,EAAA,WAAWuF,OAAOyB,YAAW,QAAA,MAAA,UAAA;AAAA,IAAAvB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAC7BxB,QAAQnE,MAAM,GAAGmE,QAAQnE,GAAG,UAAU,qBACzC,GACA,sBAAA,cAAC,OAAE,WAAWsF,OAAO/E,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAiF,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAC/BxB,GAAAA,QAAQhD,eAAe,GAAE,gBAC5B,CACF,GACA,sBAAA,cAAC,UACC,EAAA,WAAWmE,OAAO0B,kBAClB,SAAUC,CAAM,MAAA;AACdA,MAAEC,gBAAgB;AAClBjC,kBAAcd,QAAQhB,EAAE;AAAA,EAE1B,GAAA,OAAM,kBAAgB,QAAA,MAAA,UAAA;AAAA,IAAAqC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KACvB,KAED,CACF,CACD,GAGA,sBAAA,cAAA,OAAA,EACC,WAAW,GAAGL,OAAOqB,WAAW,IAAIrB,OAAO6B,cAAc,IACzD,SAAS,MAAMvH,kBAAkB,IAAI,GAAE,QAAA,MAAA,UAAA;AAAA,IAAA4F,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAEtC,OAAI,EAAA,WAAWL,OAAO8B,gBAAe,QAAA,MAAA,UAAA;AAAA,IAAA5B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAC,GAAC,uCACvC,KAAC,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,kBAAgB,CACrB,CACF,CACF,GAGC,sBAAA,cAAA,WAAA,EAAQ,WAAWL,OAAOS,SAAQ,QAAA,MAAA,UAAA;AAAA,IAAAP,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAChC,MAAG,EAAA,WAAWL,OAAOU,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAR,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EACjC,GAAA,sBAAA,cAAC,QAAI,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,IAAE,GAAO,gBAEjB,GACA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAO+B,gBAAe,QAAA,MAAA,UAAA;AAAA,IAAA7B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EACpC,GAAA,sBAAA,cAAC,KAAC,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAC,OACE,sBAAA,cAAC,UAAM,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,mBAAiB,GAAS,yLAGvC,GAEA,sBAAA,cAAC,MAAG,EAAA,WAAWL,OAAOgC,cAAa,QAAA,MAAA,UAAA;AAAA,IAAA9B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAChC,MAAG,EAAA,WAAWL,OAAOiC,aAAY,QAAA,MAAA,UAAA;AAAA,IAAA/B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,QAAK,EAAA,WAAWL,OAAOkC,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAhC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,IAAE,GACvC,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOmC,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAClC,GAAA,sBAAA,cAAC,UAAM,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,uBAAqB,GAAS,kEACxC,CACF,GACC,sBAAA,cAAA,MAAA,EAAG,WAAWL,OAAOiC,aAAY,QAAA,MAAA,UAAA;AAAA,IAAA/B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,QAAK,EAAA,WAAWL,OAAOkC,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAhC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,aAAW,GAChD,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOmC,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAClC,GAAA,sBAAA,cAAC,UAAM,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,sBAAoB,GAAS,wDACvC,CACF,GACC,sBAAA,cAAA,MAAA,EAAG,WAAWL,OAAOiC,aAAY,QAAA,MAAA,UAAA;AAAA,IAAA/B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,QAAK,EAAA,WAAWL,OAAOkC,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAhC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,GAAC,GACtC,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOmC,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAClC,GAAA,sBAAA,cAAC,UAAM,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,wBAAsB,GAAS,gEACzC,CACF,GACC,sBAAA,cAAA,MAAA,EAAG,WAAWL,OAAOiC,aAAY,QAAA,MAAA,UAAA;AAAA,IAAA/B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,QAAK,EAAA,WAAWL,OAAOkC,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAhC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,GAAC,GACtC,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOmC,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAClC,GAAA,sBAAA,cAAC,UAAM,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAC,gBAAc,GAAS,kEACjC,CACF,CACF,GAAK,UAAQ,CACjB,GAGChG,sDACE,OAAI,EAAA,WAAW2F,OAAOoC,sBAAqB,QAAA,MAAA,UAAA;AAAA,IAAAlC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACzC,OAAI,EAAA,WAAWL,OAAOqC,mBAAkB,QAAA,MAAA,UAAA;AAAA,IAAAnC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACtC,OAAI,EAAA,WAAWL,OAAOsC,YAAW,QAAA,MAAA,UAAA;AAAA,IAAApC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,MAAG,EAAA,WAAWL,OAAOuC,WAAU,QAAA,MAAA,UAAA;AAAA,IAAArC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,qBAAmB,GACnD,sBAAA,cAAA,KAAA,EAAE,WAAWL,OAAOwC,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAtC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,oCAAkC,CACvE,GAEA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAOyC,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAvC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAC9B,sBAAA,cAAA,SAAA,EAAM,SAAQ,gBAAe,WAAWL,OAAO0C,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAxC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAC,OAAK,GAC/D,sBAAA,cAAA,SAAA,EACC,MAAK,QACL,IAAG,gBACH,MAAK,gBACL,OAAO9F,WAAWE,MAClB,UAAWkH,OAAMnH,cAAc;AAAA,IAAC,GAAGD;AAAAA,IAAYE,MAAMkH,EAAEgB,OAAOC;AAAAA,EAAM,CAAA,GACpE,aAAY,oBACZ,WAAW,IACX,WAAW5C,OAAO6C,OAClB,cAAa,cAAY,QAAA,MAAA,UAAA;AAAA,IAAA3C,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,CACzB,CACJ,GAEC,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOyC,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAvC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAC9B,sBAAA,cAAA,SAAA,EAAM,SAAQ,eAAc,WAAWL,OAAO0C,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAxC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAC,mBAAiB,GAC1E,sBAAA,cAAA,SAAA,EACC,MAAK,UACL,IAAG,eACH,MAAK,eACL,OAAO9F,WAAWG,KAClB,UAAWiH,OAAMnH,cAAc;AAAA,IAAC,GAAGD;AAAAA,IAAYG,KAAKiH,EAAEgB,OAAOC;AAAAA,EAAAA,CAAM,GACnE,KAAI,KACJ,KAAI,OACJ,aAAY,SACZ,WAAW5C,OAAO6C,OAClB,cAAa,OAAK,QAAA,MAAA,UAAA;AAAA,IAAA3C,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,CAClB,CACJ,GAEC,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOyC,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAvC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAC9B,sBAAA,cAAA,SAAA,EAAM,SAAQ,mBAAkB,WAAWL,OAAO0C,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAxC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAC,oBAAkB,GAC/E,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAO8C,gBAAgB,IAAG,mBAAkB,MAAK,cAAa,mBAAgB,mBAAiB,QAAA,MAAA,UAAA;AAAA,IAAA5C,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAC5GN,cAAcvD,IAAI,CAAC7B,QAAQoI,UACzB,sBAAA,cAAA,UAAA,EACC,KAAKpI,QACL,MAAK,UACL,MAAK,SACL,gBAAcJ,WAAWI,WAAWA,QACpC,cAAY,UAAUoI,QAAQ,CAAC,KAAKpI,MAAM,IAC1C,WAAW,GAAGqF,OAAOgD,YAAY,IAAIzI,WAAWI,WAAWA,SAASqF,OAAOiD,WAAW,EAAE,IACxF,SAAS,MAAMzI,cAAc;AAAA,IAAC,GAAGD;AAAAA,IAAYI;AAAAA,EAAAA,CAAO,GAAE,QAAA,MAAA,UAAA;AAAA,IAAAuF,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAErD1F,EAAAA,GAAAA,MACH,CACD,CACH,CACF,GAEC,sBAAA,cAAA,OAAA,EAAI,WAAWqF,OAAOkD,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAhD,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EACjC,GAAA,sBAAA,cAAC,UACC,EAAA,WAAW,GAAGL,OAAOmD,GAAG,IAAInD,OAAOoD,UAAU,IAC7C,SAASnE,qBAAoB,QAAA,MAAA,UAAA;AAAA,IAAAiB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAC9B,gBAED,GACA,sBAAA,cAAC,YACC,WAAW,GAAGL,OAAOmD,GAAG,IAAInD,OAAOqD,YAAY,IAC/C,SAAS,MAAM/I,kBAAkB,KAAK,GAAE,QAAA,MAAA,UAAA;AAAA,IAAA4F,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KACzC,YAED,CACF,CACF,CACF,CAEJ;AAEJ;"}