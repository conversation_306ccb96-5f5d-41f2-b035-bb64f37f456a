# 🎯 Portal Betina V3 - Demonstração das Dashboards Premium

Este guia te ajudará a subir o Portal Betina V3 com Docker e visualizar as dashboards premium protegidas por autenticação JWT e verificação de permissões.

## 🚀 Setup Rápido

### 1. Configurar o Ambiente de Diagnóstico

Para instalar as dependências necessárias para os scripts de diagnóstico:

```powershell
# Método 1: Usando o script PowerShell
.\setup-dashboard-diagnostics.ps1

# Método 2: Usando o batch file
run-diagnostics.bat

# Método 3: Usando NPM diretamente
npm install node-fetch
```

### 2. Verificar o Ambiente

Depois de instalar as dependências, verifique se o ambiente está configurado corretamente:

```bash
node check-dashboard-environment.js
```

### 3. Iniciar o Sistema

Execute o script de inicialização (Windows PowerShell):
```powershell
# Modo produção (usa docker-compose.yml)
.\start-dashboard-demo.ps1

# Modo desenvolvimento (usa docker-compose.dev.yml)
.\start-dashboard-demo.ps1 -Dev

# Limpar contêineres existentes e iniciar em modo produção
.\start-dashboard-demo.ps1 -Clean

# Iniciar e mostrar logs
.\start-dashboard-demo.ps1 -Logs

# Ver todas as opções
.\start-dashboard-demo.ps1 -Help
```

### 3. Gerar Métricas de Demonstração

```bash
# Gerar métricas de teste para as dashboards
node test-dashboard-metrics.js
```

Este script irá:
- Criar 3 crianças fictícias com diferentes condições
- Gerar 30+ sessões de jogos com métricas realistas
- Alimentar as dashboards com dados variados de desempenho

## 👥 Usuários de Demonstração

| Usuário | Email | Senha | Papel | Acesso |
|---------|-------|-------|-------|--------|
| **Admin** | <EMAIL> | admin123 | Administrador | Todas as dashboards premium |
| **Terapeuta** | <EMAIL> | terapeuta123 | Terapeuta | Dashboards terapêuticas premium |
| **Demo** | <EMAIL> | demo123 | Usuário demo | Dashboards premium limitadas |
| **Pai/Mãe** | <EMAIL> | pai123 | Responsável | Apenas dashboards básicas |

## 📊 Dashboards Disponíveis

### 🌐 Acesso Principal
- **Frontend**: http://localhost:5173
- **API**: http://localhost:3000

### 📈 Dashboards Específicas

1. **Dashboard Principal** - http://localhost:5173/dashboard
   - Visão geral do sistema
   - Estatísticas gerais
   - Status dos componentes

2. **Dashboard de Progresso** - http://localhost:5173/progress
   - Evolução das crianças
   - Gráficos de desempenho
   - Métricas por jogo

3. **Dashboard de Analytics** - http://localhost:5173/analytics
   - Análises detalhadas
   - Padrões comportamentais
   - Relatórios avançados

4. **Dashboard Terapêutica** - http://localhost:5173/therapeutic
   - Insights clínicos
   - Recomendações terapêuticas
   - Análises especializadas

## 🎮 Dados de Demonstração

### Crianças Fictícias
- **Ana Clara** (6 anos) - Autismo, TDAH
- **Pedro Santos** (8 anos) - Atraso cognitivo
- **Sofia Lima** (5 anos) - Dificuldades motoras

### Jogos Incluídos
- ColorMatch (Combinação de Cores)
- MemoryGame (Jogo da Memória)
- CreativePainting (Pintura Criativa)
- MusicalSequence (Sequência Musical)
- PadroesVisuais (Padrões Visuais)
- QuebraCabeca (Quebra-Cabeça)
- ImageAssociation (Associação de Imagens)
- ContagemNumeros (Contagem de Números)
- LetterRecognition (Reconhecimento de Letras)

### Métricas Geradas
- **Cognitivas**: Accuracy, tempo de resposta, atenção
- **Comportamentais**: Engajamento, persistência
- **Específicas por jogo**: Métricas personalizadas
- **Padrões de erro**: Identificação automática

## 🔧 Comandos Úteis

```bash
# Ver logs em tempo real
docker-compose -f docker-compose.yml logs -f
# OU: docker-compose -f docker-compose.dev.yml logs -f

# Parar o sistema
docker-compose -f docker-compose.yml down
# OU: docker-compose -f docker-compose.dev.yml down

# Reiniciar containers
docker-compose -f docker-compose.yml restart
# OU: docker-compose -f docker-compose.dev.yml restart

# Verificar status
docker-compose -f docker-compose.yml ps
# OU: docker-compose -f docker-compose.dev.yml ps

# Acessar banco de dados
docker-compose exec portal-betina-db psql -U betina_user -d betina_db

# Gerar mais métricas
node test-dashboard-metrics.js

# Verificar ambiente
node check-dashboard-environment.js

# Validar segurança
node check-dashboard-security.js
```

## 🔐 Recursos de Segurança

### Proteções Implementadas
- **JWT Authentication**: Autenticação baseada em token JSON Web Token
- **RBAC (Role Based Access Control)**: Permissões baseadas em papéis
- **Helmet**: Proteções contra ataques comuns da web
- **Rate Limiting**: Prevenção contra ataques de força bruta
- **Input Sanitization**: Proteção contra injeções e XSS
- **CORS**: Configuração segura de Cross-Origin Resource Sharing
- **Auditoria**: Logging detalhado de tentativas de acesso

### Testes de Segurança
```bash
# Validar proteção das dashboards premium
.\test-dashboard-security.ps1

# Verificar aplicação de middlewares de segurança
node check-dashboard-security.js
```

## 📋 Verificação do Sistema

### Health Checks
- **API**: http://localhost:3000/api/public/health
- **Sistema**: http://localhost:3000/api/health/detailed

### Endpoints de Teste
- **Login**: POST http://localhost:3000/api/auth/dashboard/login
- **Verificação Token**: GET http://localhost:3000/api/auth/dashboard/verify 
- **Dashboard Premium**: GET http://localhost:3000/api/premium/dashboards/overview

## 🎯 Fluxo de Demonstração

1. **Verificar o Ambiente**
   ```bash
   node check-dashboard-environment.js
   ```

2. **Iniciar Sistema**
   ```bash
   .\start-dashboard-demo.ps1
   ```

3. **Gerar Dados**
   ```bash
   node test-dashboard-metrics.js
   ```

4. **Acessar Frontend**
   - Ir para http://localhost:5173
   - Fazer login com um dos usuários demo:
     - Admin: `<EMAIL>` / `admin123`
     - Terapeuta: `<EMAIL>` / `terapeuta123`
     - Demo: `<EMAIL>` / `demo123`
     - Pai: `<EMAIL>` / `pai123`

5. **Explorar Dashboards Premium**
   - Dashboard principal para visão geral
   - Progress para acompanhar evolução
   - Analytics para insights detalhados
   - Therapeutic para análises clínicas

6. **Validar Segurança**
   ```bash
   node check-dashboard-security.js
   ```

## 🔍 Diferenças entre Arquivos Docker Compose

### docker-compose.yml (Produção)
- Usa `Dockerfile.backend` para API
- Usa `Dockerfile.frontend` para Frontend
- Nível de log: `info`
- DEBUG_MODE: `false`
- Porta Frontend: 5173

### docker-compose.dev.yml (Desenvolvimento)
- Usa `Dockerfile.api` para API
- Usa `Dockerfile` para Frontend
- Nível de log: `debug`
- DEBUG_MODE: `true`
- Porta Frontend: 3001

## 🛠️ Troubleshooting

### Erro de logger
- Verifica se `createLogger` está sendo exportada corretamente em `src/utils/logger.js`
- Execute `node check-dashboard-environment.js` para diagnóstico

### Containers não inicializam
```bash
docker-compose -f docker-compose.yml down
docker system prune -f
docker-compose -f docker-compose.yml up -d --build
```

### Frontend não carrega
- Verificar se porta 5173 está livre (produção) ou 3001 (desenvolvimento)
- Aguardar alguns segundos após inicialização dos containers

### API não responde
- Verificar se porta 3000 está livre
- Verificar logs: `docker-compose logs api`
- Aguardar banco de dados estar pronto

### Banco não conecta
- Verificar se porta 5432 está livre
- Verificar health check: `docker inspect portal-betina-v3-db | grep Health`

## 🎉 Sucesso!

Se tudo estiver funcionando, você verá:
- ✅ Containers rodando: `docker-compose ps`
- ✅ Frontend acessível: http://localhost:5173
- ✅ API respondendo: http://localhost:3000/api/public/health
- ✅ Login funcionando com usuários de demo
- ✅ Dashboards carregando com dados

---

## 📚 Recursos Adicionais

- **Documentação**: Veja os arquivos `RELATORIO_*.md` para detalhes técnicos
- **Arquitetura**: `docs/ARQUITETURA_SISTEMA.md`
- **APIs**: Explore `/api/` endpoints
- **Logs**: `docker-compose logs` para debugging

Aproveite a demonstração! 🚀
