# Guia de Execução e Teste do Portal Betina V3 com Docker

Este documento explica como executar e testar o Portal Betina V3 usando Docker e Docker Compose.

## Pré-requisitos

- Docker (20.10.x ou superior)
- Docker Compose (2.0.x ou superior)
- Git (para clonar o repositório, se necessário)

## Estrutura de Containers

O sistema é composto pelos seguintes containers:

1. **portal-betina-v3-db**: Banco de dados PostgreSQL
2. **portal-betina-v3-api**: API Node.js com o backend
3. **portal-betina-v3-frontend**: Frontend servido pelo Nginx
4. **portal-betina-v3-monitoring**: Prometheus para monitoramento (opcional)

## Arquivos de Configuração

Os principais arquivos de configuração são:

- `docker-compose.yml`: Define todos os serviços e suas dependências
- `Dockerfile`: Build multi-estágio para o frontend
- `Dockerfile.api`: Build para a API
- `nginx.prod.conf`: Configuração do Nginx para produção
- `.env`: Variáveis de ambiente para produção
- `.env.development`: Variáveis de ambiente para desenvolvimento

## Instruções para Execução

### Windows (PowerShell)

1. Abra um terminal PowerShell na pasta do projeto
2. Execute o script de teste:

```powershell
.\test-docker.ps1
```

### Linux/Mac (ou Git Bash no Windows)

1. Abra um terminal na pasta do projeto
2. Dê permissão de execução ao script (apenas Linux/Mac):

```bash
chmod +x ./test-docker.sh
```

3. Execute o script de teste:

```bash
./test-docker.sh
```

### Execução Manual

Para iniciar os containers manualmente:

```bash
docker-compose up -d
```

Para verificar os logs:

```bash
docker-compose logs -f
```

Para parar os containers:

```bash
docker-compose down
```

## Acessando o Sistema

- **Frontend**: http://localhost:80
- **API**: http://localhost:3000/api
- **Monitoramento**: http://localhost:9090

## Verificações do Sistema

O script de teste realiza as seguintes verificações:

1. Conectividade com o banco de dados
2. Disponibilidade da API (endpoint /api/health)
3. Disponibilidade do Frontend
4. Mostra o status dos containers
5. Mostra as últimas linhas do log da API

## Troubleshooting

### Problema de conexão com o banco de dados

Se o banco de dados não estiver acessível:

```bash
docker-compose logs portal-betina-db
```

### API não está respondendo

Verifique os logs da API:

```bash
docker-compose logs api
```

Reinicie a API:

```bash
docker-compose restart api
```

### Frontend não está acessível

Verifique os logs do frontend:

```bash
docker-compose logs frontend
```

### Limpeza completa (em último caso)

Para remover todos os containers, imagens e volumes:

```bash
docker-compose down -v
docker system prune -a
```

## Notas Adicionais

- As configurações de produção estão em `.env`
- Para desenvolvimento, use `.env.development`
- Os dados do banco são persistidos em volumes Docker
- O sistema inclui monitoramento com Prometheus

## Ambiente de Desenvolvimento

Para executar em modo de desenvolvimento com hot reload:

1. Copie `.env.development` para `.env`:

```bash
cp .env.development .env
```

2. Use o docker-compose.dev.yml para iniciar o ambiente de desenvolvimento:

```bash
docker-compose -f docker-compose.dev.yml up -d
```

3. O ambiente de desenvolvimento fornece:
   - Hot reloading para frontend (React + Vite) acessível em http://localhost:5173
   - API em modo desenvolvimento acessível em http://localhost:3000/api
   - Volumes montados para refletir alterações em tempo real
   - Configurações otimizadas para depuração
