{"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,SAASA,OAAO;AAAA,EAAEC;AAAY,GAAG;AAExB;AACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACFA,SAASC,wBAAwB;AAAA,EAAEC;AAAAA,EAAYC;AAAiB,GAAG;AAC3DC,8BAAsBA,CAACC,YAAYC,kBAAkB;AACzD,QAAIH,kBAAkB;AACpBA,uBAAiBE,UAAU;AAAA;AAAA,EAE/B;AAEA,QAAME,oBAAoB,CACxB;AAAA,IACEC,IAAI;AAAA,IACJC,OAAO;AAAA,IACPC,aAAa;AAAA,IACbC,MAAM;AAAA,IACNC,OAAO;AAAA,IACPC,OAAO;AAAA,KAET;AAAA,IACEL,IAAI;AAAA,IACJC,OAAO;AAAA,IACPC,aAAa;AAAA,IACbC,MAAM;AAAA,IACNC,OAAO;AAAA,IACPC,OAAO;AAAA,KAET;AAAA,IACEL,IAAI;AAAA,IACJC,OAAO;AAAA,IACPC,aAAa;AAAA,IACbC,MAAM;AAAA,IACNC,OAAO;AAAA,IACPC,OAAO;AAAA,KAET;AAAA,IACEL,IAAI;AAAA,IACJC,OAAO;AAAA,IACPC,aAAa;AAAA,IACbC,MAAM;AAAA,IACNC,OAAO;AAAA,IACPC,OAAO;AAAA,KAET;AAAA,IACEL,IAAI;AAAA,IACJC,OAAO;AAAA,IACPC,aAAa;AAAA,IACbC,MAAM;AAAA,IACNC,OAAO;AAAA,IACPC,OAAO;AAAA,KAET;AAAA,IACEL,IAAI;AAAA,IACJC,OAAO;AAAA,IACPC,aAAa;AAAA,IACbC,MAAM;AAAA,IACNC,OAAO;AAAA,IACPC,OAAO;AAAA,KAET;AAAA,IACEL,IAAI;AAAA,IACJC,OAAO;AAAA,IACPC,aAAa;AAAA,IACbC,MAAM;AAAA,IACNC,OAAO;AAAA,IACPC,OAAO;AAAA,KAET;AAAA,IACEL,IAAI;AAAA,IACJC,OAAO;AAAA,IACPC,aAAa;AAAA,IACbC,MAAM;AAAA,IACNC,OAAO;AAAA,IACPC,OAAO;AAAA,KAET;AAAA,IACEL,IAAI;AAAA,IACJC,OAAO;AAAA,IACPC,aAAa;AAAA,IACbC,MAAM;AAAA,IACNC,OAAO;AAAA,IACPC,OAAO;AAAA,GACR;AAGGC,4BAAoBZ,aAAYa,SAASb,cAAaK;AAE5D,6CACG,WAAQ,aAAWS,SAAOC,WAAU;AAAA,IAAAC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,2CAClC,MAAG,aAAWL,SAAOP,OAAM;AAAA,IAAAS,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OAAC,8BAE7B,GAEC,6CAAI,WAAWL,SAAOM,MAAK;AAAA,IAAAJ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OACzBP,kBAAkBS,IAAI,CAACC,UAAUC,UAChC,oCAAC,UACC,OAAKD,SAAShB,IACd,WAAWQ,SAAOU,MAClB,SAAS,MAAMtB,oBAAoBoB,SAAShB,IAAIgB,SAASf,KAAK,GAC9D,OAAO;AAAA,IAAEkB,gBAAgB,GAAGF,QAAQ,GAAG;AAAA,KAAM;AAAA,IAAAP,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,2CAE5C,OAAI,aAAWL,SAAOY,YAAW;AAAA,IAAAV,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,2CAC/B,OAAI,aAAWL,SAAOL,MAAK;AAAA,IAAAO,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OAAEG,SAASb,IAAK,uCAC3C,QAAK,aAAW,GAAGK,SAAOJ,KAAK,IAAII,SAAOQ,SAASX,KAAK,KAAKG,SAAOa,IAAI,IAAG;AAAA,IAAAX,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EACzEG,cAASZ,KACZ,CACF,GAEA,oCAAC,MAAG,aAAWI,SAAOc,WAAU;AAAA,IAAAZ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OAAEG,SAASf,KAAM,GACjD,oCAAC,KAAE,aAAWO,SAAOe,iBAAgB;AAAA,IAAAb,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OAAEG,SAASd,WAAY,CAC9D,CACD,CACH,CACF;AAEJ;;;;;;;;;;;;;;;;;;;;;;;;;;AC/GA,SAASsB,iBAAiB;AACxB,QAAMC,SAAS;AAEf,QAAMC,mBAAmBA,CAACC,SAASC,OAAO,cAAc;AAChDC,yBAAeC,SAASC,cAAc,KAAK;AACpCC,6BAAY,gBAAgBJ,IAAI;AAC7CC,iBAAaI,cAAcN;AAEpBO,kBAAOL,aAAaM,OAAO;AAAA,MAChCC,UAAU;AAAA,MACVC,KAAK;AAAA,MACLC,OAAO;AAAA,MACPC,SAAS;AAAA,MACTC,cAAc;AAAA,MACdnC,OAAO;AAAA,MACPoC,YAAY;AAAA,MACZC,UAAU;AAAA,MACVC,QAAQ;AAAA,MACRC,WAAW;AAAA,MACXC,YAAY;AAAA,MACZC,WAAW;AAAA,MACXC,SAAS;AAAA,MACTC,YAAY;AAAA,MACZC,KAAK;AAAA,KACN;AAED,UAAMC,SAAS;AAAA,MACbC,SAAS;AAAA,MACTC,OAAO;AAAA,IACT;AACAvB,iBAAaM,MAAMkB,aAAaH,OAAOtB,IAAI,KAAKsB,OAAOC;AAEjDhD,kBAAO2B,SAASC,cAAc,MAAM;AACrCE,wBAAcL,SAAS,YAAY,MAAM;AAC9CC,iBAAayB,QAAQnD,KAAI;AAEhBoD,kBAAKC,YAAY3B,YAAY;AAEtC4B,eAAW,MAAM;AACf5B,mBAAaM,MAAMS,YAAY;AAC/Bf,mBAAaM,MAAMuB,UAAU;AAAA,OAC5B,GAAG;AAEND,eAAW,MAAM;AACf5B,mBAAaM,MAAMS,YAAY;AAC/Bf,mBAAaM,MAAMuB,UAAU;AAC7BD,iBAAW,MAAM5B,aAAa8B,OAAO,GAAG,GAAG;AAAA,OAC1C,GAAI;AAAA,EACT;AAEA,QAAMC,UAAUA,MAAM;AACpBC,cAAUC,UAAUC,UAAUtC,MAAM,EAAEuC,KAAK,MAAM;AAC/CtC,uBAAiB,kCAAkC,SAAS;AAAA,KAC7D,EAAEuC,MAAM,MAAM;AACbvC,uBAAiB,4BAA4B,OAAO;AAAA,KACrD;AAAA,EACH;AAEA,6CACG,OAAO,SAAP,EACC,WAAWlB,SAAO0D,QAClB,SAAS;AAAA,IAAER,SAAS;AAAA,IAAGS,GAAG;AAAA,KAC1B,SAAS;AAAA,IAAET,SAAS;AAAA,IAAGS,GAAG;AAAA,KAC1B,YAAY;AAAA,IAAEC,UAAU;AAAA,KACxB,MAAK,UACL,cAAW,oBAAkB;AAAA,IAAA1D,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,2CAE5B,OAAI,aAAWL,SAAO6D,eAAc;AAAA,IAAA3D,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,2CAClC,MAAG,aAAWL,SAAO8D,cAAa;AAAA,IAAA5D,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OAAC,qCAEpC,CACF,GAEA,oCAAC,MAAG,aAAWL,SAAOP,OAAM;AAAA,IAAAS,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OAAC,oCAE3B,oCAAC,QAAK,aAAWL,SAAO+D,OAAO,eAAY,QAAM;AAAA,IAAA7D,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OAAC,IAAE,CACtD,GAEA,oCAAC,KAAE,aAAWL,SAAOgE,MAAK;AAAA,IAAA9D,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OAAC,uPAGzB,oCAAC,UAAM;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OAAC,+BAA6B,CACvC,GAEA,oCAAC,OAAI,aAAWL,SAAOiE,WAAU;AAAA,IAAA/D,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,2CAC9B,OAAI,aAAWL,SAAOkE,aAAY;AAAA,IAAAhE,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,2CAChC,OAAO,KAAP,EACC,KAAK,iEAAiE8D,mBAAmBlD,MAAM,CAAC,IAChG,KAAI,2BACJ,WAAWjB,SAAOoE,SAClB,OAAM,4DACN,SAAShB,SACT,YAAY;AAAA,IAAEiB,OAAO;AAAA,KACrB,UAAU;AAAA,IAAEA,OAAO;AAAA,KACnB,MAAK,UACL,UAAU,GACV,WAAYC,OAAMA,EAAEC,QAAQ,WAAWnB,WAAU;AAAA,IAAAlD,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,KACjD,CACJ,GAEC,6CAAI,WAAWL,SAAOwE,cAAa;AAAA,IAAAtE,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,2CACjC,MAAG,aAAWL,SAAOyE,cAAa;AAAA,IAAAvE,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,IACjC,uCAAC,MAAE;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OAAC,kCAAgC,uCACnC,MAAE;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OAAC,4CAA0C,uCAC7C,MAAE;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OAAC,gCAA8B,CACpC,CACF,CACF,CACF;AAEJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/GA,SAASqE,aAAa;AAAA,EAAEC;AAAa,GAAG;AAAG,QAAMC,kBAAkBA,CAACC,QAAQC,WAAWC,WAAW,WAAW;AACzG,QAAIJ,cAAc;AAChBA,mBAAaE,MAAM;AAAA;AAAA,EAGvB;AACA,QAAMG,QAAQ,CACZ;AAAA,IACExF,IAAI;AAAA,IACJC,OAAO;AAAA,IACPC,aAAa;AAAA,IACbC,MAAM;AAAA,IACNC,OAAO;AAAA,IACPC,OAAO;AAAA,IACPuB,MAAM;AAAA,KAER;AAAA,IACE5B,IAAI;AAAA,IACJC,OAAO;AAAA,IACPC,aAAa;AAAA,IACbC,MAAM;AAAA,IACNC,OAAO;AAAA,IACPC,OAAO;AAAA,IACPuB,MAAM;AAAA,KAER;AAAA,IACE5B,IAAI;AAAA,IACJC,OAAO;AAAA,IACPC,aAAa;AAAA,IACbC,MAAM;AAAA,IACNC,OAAO;AAAA,IACPC,OAAO;AAAA,IACPuB,MAAM;AAAA,KAER;AAAA,IACE5B,IAAI;AAAA,IACJC,OAAO;AAAA,IACPC,aAAa;AAAA,IACbC,MAAM;AAAA,IACNC,OAAO;AAAA,IACPC,OAAO;AAAA,IACPuB,MAAM;AAAA,KAER;AAAA,IACE5B,IAAI;AAAA,IACJC,OAAO;AAAA,IACPC,aAAa;AAAA,IACbC,MAAM;AAAA,IACNC,OAAO;AAAA,IACPC,OAAO;AAAA,IACPuB,MAAM;AAAA,GACP;AAGH,6CACG,WAAQ,aAAWpB,SAAOC,WAAU;AAAA,IAAAC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,2CAClC,MAAG,aAAWL,SAAOP,OAAM;AAAA,IAAAS,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OAAC,gCAE7B,GAEC,6CAAI,WAAWL,SAAOM,MAAK;AAAA,IAAAJ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,IACzB2E,SAAMzE,IAAI,CAAC0E,MAAMxE,UAChB,oCAAC,UACC,OAAKwE,KAAKzF,IACV,WAAW,GAAGQ,SAAOU,IAAI,IAAIV,SAAOiF,KAAKpF,KAAK,KAAKG,SAAOa,IAAI,IAC9D,SAAS,MAAM+D,gBAAgBK,KAAKzF,IAAIyF,KAAKxF,OAAOwF,KAAK7D,IAAI,GAC7D,OAAO;AAAA,IAAET,gBAAgB,GAAGF,QAAQ,GAAG;AAAA,KAAM;AAAA,IAAAP,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,2CAE5C,OAAI,aAAWL,SAAOY,YAAW;AAAA,IAAAV,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,2CAC/B,OAAI,aAAWL,SAAOL,MAAK;AAAA,IAAAO,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,IAAE4E,QAAKtF,IAAK,uCACvC,QAAK,aAAW,GAAGK,SAAOJ,KAAK,IAAII,SAAO,QAAQiF,KAAKpF,MAAMqF,OAAO,CAAC,EAAEC,YAAY,IAAIF,KAAKpF,MAAMuF,MAAM,CAAC,CAAC,EAAE,KAAKpF,SAAOqF,SAAS,IAAG;AAAA,IAAAnF,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAClI4E,UAAKrF,KACR,CACF,GAEA,oCAAC,MAAG,aAAWI,SAAOc,WAAU;AAAA,IAAAZ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OAAE4E,KAAKxF,KAAM,GAC7C,oCAAC,KAAE,aAAWO,SAAOe,iBAAgB;AAAA,IAAAb,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OAAE4E,KAAKvF,WAAY,CAC1D,CACD,CACH,CACF;AAEJ;;;;;;;;;;;;;;;;;;AClFA,MAAMR,aAAa,CACjB;AAAA,EAAEM,IAAI;AAAA,EAAQ8F,MAAM;AAAA,EAAU3F,MAAM;AAAK,GACzC;AAAA,EAAEH,IAAI;AAAA,EAAsB8F,MAAM;AAAA,EAAU3F,MAAM;AAAK,GACvD;AAAA,EAAEH,IAAI;AAAA,EAAmB8F,MAAM;AAAA,EAAW3F,MAAM;AAAK,GACrD;AAAA,EAAEH,IAAI;AAAA,EAAe8F,MAAM;AAAA,EAAW3F,MAAM;AAAK,GACjD;AAAA,EAAEH,IAAI;AAAA,EAAoB8F,MAAM;AAAA,EAAU3F,MAAM;AAAK,GACrD;AAAA,EAAEH,IAAI;AAAA,EAAe8F,MAAM;AAAA,EAAS3F,MAAM;AAAK,GAC/C;AAAA,EAAEH,IAAI;AAAA,EAAqB8F,MAAM;AAAA,EAAW3F,MAAM;AAAM,GACxD;AAAA,EAAEH,IAAI;AAAA,EAAmB8F,MAAM;AAAA,EAAW3F,MAAM;AAAK,GACrD;AAAA,EAAEH,IAAI;AAAA,EAAiB8F,MAAM;AAAA,EAAU3F,MAAM;AAAK,GAClD;AAAA,EAAEH,IAAI;AAAA,EAAqB8F,MAAM;AAAA,EAAQ3F,MAAM;AAAK,CAAC;AAMvD,SAAS4F,OAAO;AAAA,EAAEC;AAAAA,EAAiBC;AAAiB,GAAG;AAC/CrG,8BAAsBA,CAACC,YAAYqG,iBAAiB;AACxD,QAAID,kBAAkB;AACpBA,uBAAiBpG,UAAU;AAAA;AAAA,EAE/B;AAEA,6CACG,UAAO,aAAWW,OAAO2F,QAAO;AAAA,IAAAzF,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,2CAC9B,OAAI,aAAWL,OAAO4F,eAAc;AAAA,IAAA1F,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,2CAClC,OAAI,aAAWL,OAAO6F,gBAAe;AAAA,IAAA3F,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OACnCnB,WAAWqB,IAAKC,CAA0B,iDAAC,YACxC,KAAKA,SAAShB,IACd,WAAW,GAAGQ,OAAO8F,SAAS,IAAIN,oBAAoBhF,SAAShB,KAAKQ,OAAO+F,SAAS,EAAE,IACtF,SAAS,MAAM3G,oBAAoBoB,SAAShB,IAAIgB,SAAS8E,IAAI,GAC7D,MAAK,UACL,cAAY,gBAAgB9E,SAAS8E,IAAI,IAAG;AAAA,IAAApF,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,2CAE3C,OAAI,aAAWL,OAAOgG,SAAQ;AAAA,IAAA9F,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OAAEG,SAASb,IAAK,GAC/C,oCAAC,OAAI,aAAWK,OAAOiG,UAAS;AAAA,IAAA/F,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OAAEG,SAAS8E,IAAK,CAClD,CACD,CACH,CACF,CACF;AAEJ;;AChDA,MAAMY,sBAAsBC,kBAAK,0BAAM,OAAO,4BAAqD,0IAAC;AACpG,MAAMC,wBAAwBD,kBAAK,0BAAM,OAAO,4BAAyD,0IAAC;AAC1G,MAAME,sBAAsBF,kBAAK,0BAAM,OAAO,4BAAqD,0IAAC;AACpG,MAAMG,aAAaH,kBAAK,0BAAM,OAAO,2BAAuC,0IAAC;AAC7E,MAAMI,iBAAiBJ,kBAAK,0BAAM,OAAO,2BAA2C,0IAAC;AACrF,MAAMK,uBAAuBL,kBAAK,0BAAM,OAAO,gCAAuD,0IAAC;AACvG,MAAMM,qBAAqBN,kBAAK,0BAAM,OAAO,6BAAmD,0IAAC;AACjG,MAAMO,mBAAmBP,kBAAK,0BAAM,OAAO,2BAA+C,0IAAC;AAC3F,MAAMQ,uBAAuBR,kBAAK,0BAAM,OAAO,6BAAuD,0IAAC;AAGvG,MAAMS,sBAAsBA,MACzB,6CAAI,OAAO;AAAA,EACVrE,SAAS;AAAA,EACTsE,eAAe;AAAA,EACfC,gBAAgB;AAAA,EAChBtE,YAAY;AAAA,EACZuE,QAAQ;AAAA,EACRlE,YAAY;AAAA,EACZhD,OAAO;AAAA,EACPqC,UAAU;AAAA,EACV8E,YAAY;AACd,GAAE;AAAA,EAAA9G,UAAAC;AAAAA,EAAAC,YAAA;AAAA,EAAAC,cAAA;AAAA,EACA,uCAAC,SAAI,OAAO;AAAA,EAAE4G,cAAc;AAAA,EAAQ/E,UAAU;AAAO,GAAE;AAAA,EAAAhC,UAAAC;AAAAA,EAAAC,YAAA;AAAA,EAAAC,cAAA;AAAA,KAAC,IAAE,GAC1D,oCAAC,OAAG;AAAA,EAAAH,UAAAC;AAAAA,EAAAC,YAAA;AAAA,EAAAC,cAAA;AAAA,KAAC,oBAAkB,CACzB;AAGF,MAAM6G,WAAWA,CAAC;AAAA,EAAEC;AAAAA,EAAQC;AAAO,MAAM;AAEvC,QAAMC,iBAAiB;AAAA,IACrB,WAAWnB;AAAAA,IACX,mBAAmBA;AAAAA,IACnB,oBAAoBA;AAAAA,IACpB,WAAWE;AAAAA,IACX,sBAAsBA;AAAAA,IACtB,SAASC;AAAAA,IACT,oBAAoBA;AAAAA,IACpB,UAAUC;AAAAA,IACV,eAAeA;AAAAA,IACf,UAAUC;AAAAA,IACV,eAAeA;AAAAA,IACf,UAAUC;AAAAA,IACV,qBAAqBA;AAAAA,IACrB,YAAYC;AAAAA,IACZ,mBAAmBA;AAAAA,IACnB,iBAAiBC;AAAAA,IACjB,oBAAoBA;AAAAA;AAAAA,IACpB,qBAAqBC;AAAAA,IACrB,oBAAoBA;AAAAA,EACtB;AAGMW,wBAAgBD,eAAeF,MAAM;AAE3C,MAAIG,eAAe;AACjB,+CACGC,aAAS,0DAAW,qBAAmB;AAAA,MAAArH,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,UAAI;AAAA,MAAAH,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,SACzC,qDAAc,QAAe;AAAA,MAAAH,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,OAAG,CACnC;AAAA;AAKJ,QAAMmH,WAAW;AAAA,IACf,sBAAsB;AAAA,MACpB/H,OAAO;AAAA,MACPC,aAAa;AAAA,MACb+H,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACThI,OAAO;AAAA,MACPC,aAAa;AAAA,MACb+H,SAAS;AAAA,IACX;AAAA,IACA,mBAAmB;AAAA,MACjBhI,OAAO;AAAA,MACPC,aAAa;AAAA,MACb+H,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACThI,OAAO;AAAA,MACPC,aAAa;AAAA,MACb+H,SAAS;AAAA,IACX;AAAA,IACA,oBAAoB;AAAA,MAClBhI,OAAO;AAAA,MACPC,aAAa;AAAA,MACb+H,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACPhI,OAAO;AAAA,MACPC,aAAa;AAAA,MACb+H,SAAS;AAAA,IACX;AAAA,IACA,eAAe;AAAA,MACbhI,OAAO;AAAA,MACPC,aAAa;AAAA,MACb+H,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACRhI,OAAO;AAAA,MACPC,aAAa;AAAA,MACb+H,SAAS;AAAA,IACX;AAAA,IACA,eAAe;AAAA,MACbhI,OAAO;AAAA,MACPC,aAAa;AAAA,MACb+H,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACRhI,OAAO;AAAA,MACPC,aAAa;AAAA,MACb+H,SAAS;AAAA,IACX;AAAA,IACA,qBAAqB;AAAA,MACnBhI,OAAO;AAAA,MACPC,aAAa;AAAA,MACb+H,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACRhI,OAAO;AAAA,MACPC,aAAa;AAAA,MACb+H,SAAS;AAAA;AAAA,EAEb;AAEMC,eAAOF,SAASL,MAAM,KAAK;AAAA,IAC/B1H,OAAO;AAAA,IACPC,aAAa;AAAA,IACb+H,SAAS;AAAA,EACX;AAGE,6CAAC,SAAI,OAAO;AAAA,IACV1F,SAAS;AAAA,IACT4F,WAAW;AAAA,IACX9E,YAAY;AAAA,IACZb,cAAc;AAAA,IACd4F,QAAQ;AAAA,IACRtF,WAAW;AAAA,KACX;AAAA,IAAApC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OACC,4CAAG,OAAO;AAAA,IAAER,OAAO;AAAA,IAAWoH,cAAc;AAAA,KAAS;AAAA,IAAA/G,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OACnDqH,KAAKjI,KACR,GACA,oCAAC,OAAE,OAAO;AAAA,IAAEI,OAAO;AAAA,IAAQoH,cAAc;AAAA,IAAQ/E,UAAU;AAAA,KAAW;AAAA,IAAAhC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OACnEqH,KAAKhI,WACR,GACA,oCAAC,SAAI,OAAO;AAAA,IACVqC,SAAS;AAAA,IACTc,YAAY;AAAA,IACZb,cAAc;AAAA,IACdiF,cAAc;AAAA,IACd/E,UAAU;AAAA,KACV;AAAA,IAAAhC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OACCqH,KAAKD,OACR,CACF;AAEJ;;ACvJA,MAAMI,QAAQ1B,kBAAK,MAAM,2BAAO,qBAAe,+HAAC;AAChD,MAAM2B,aAAa3B,kBAAK,MAAM,2BAAO,qBAAyB,0IAAC;AAC/D,MAAM4B,oBAAoB5B,kBAAK,MAAM,2BAAO,iCAAuC,+HAAC;AACpF,MAAM6B,eAAe7B,kBAAK,MAAM,2BAAO,4BAA6B,+HAAC;AAErE,MAAM8B,qBAAqB9B,kBAAK,0BAAM,OAAO,yBAAiC,0IAAC;AAK/E,SAAS+B,MAAM;AACb,QAAM,CAAC1C,iBAAiB2C,kBAAkB,IAAIC,sBAAS,MAAM;AAC7D,QAAM,CAACC,aAAaC,cAAc,IAAIF,sBAAS,MAAM;AAErD,QAAMG,kBAAkBA,MAAM;AAC5BJ,uBAAmB,MAAM;AACzBG,mBAAe,MAAM;AAAA,EAAG;AAE1B,QAAME,uBAAwBnJ,CAAe;AAE3C,UAAMoJ,UAAU,CACd,sBAAsB,mBAAmB,eACzC,oBAAoB,eAAe,qBACnC,qBAAqB,mBAAmB,iBACxC,WAAW,WAAW,UAAU,SAAS,UAAU,UAAU,UAAU;AAGrEA,gBAAQC,SAASrJ,UAAU,GAAG;AAEhC8I,yBAAmB9I,UAAU;AAC7BiJ,qBAAe,MAAM;AAAA,WAChB;AAELH,yBAAmB9I,UAAU;AAC7BiJ,qBAAe,MAAM;AAAA;AAAA,EAEzB;AAEA,QAAMK,mBAAoB9D,CAAW;AACnC,QAAIA,WAAW,cAAc;AAC3ByD,qBAAe,OAAO;AAAA,eACbzD,WAAW,eAAe;AACnCyD,qBAAe,OAAO;AAAA,eACbzD,WAAW,0BAA0B;AAC9CyD,qBAAe,eAAe;AAAA,eACrBzD,WAAW,iBAAiB;AACrCyD,qBAAe,UAAU;AAAA,eAChBzD,WAAW,yBAAyB;AAC7CyD,qBAAe,YAAY;AAAA,WACtB;AACLH,yBAAmBtD,MAAM;AACzByD,qBAAe,MAAM;AAAA;AAAA,EAEzB;AAEA,QAAMM,mBAAmBA,MAAM;AAC7BN,mBAAe,MAAM;AACrBH,uBAAmB,MAAM;AAAA,EAC3B;AAGA,QAAMU,kBAAkBA,MACtB,oCAAC,OAAI,aAAU,qBAAoB,OAAO;AAAA,IACxCtG,SAAS;AAAA,IACTuE,gBAAgB;AAAA,IAChBtE,YAAY;AAAA,IACZuE,QAAQ;AAAA,IACR7E,UAAU;AAAA,IACVrC,OAAO;AAAA,KACP;AAAA,IAAAK,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,IACA,uCAAC,OAAG;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OAAC,eAAa,CACpB;AAGF,6CACG,OAAI,aAAU,OAAK;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OACjB,8CAAO,aAAakI,iBAAgB;AAAA,IAAArI,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,KAAG,GAEvC,oCAAAkH,uBAAA,EAAS,UAAW,uDAAe;AAAA,IAAArH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,QAAI;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OACrCgI,gBAAgB,UACf,oCAAC,SAAM,cAAcO,kBAAiB;AAAA,IAAA1I,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,SACpCgI,gBAAgB,UAClB,oCAAC,cAAW,QAAQO,kBAAiB;AAAA,IAAA1I,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,SACnCgI,gBAAgB,kBAClB,oCAAC,qBAAkB,QAAQO,kBAAiB;AAAA,IAAA1I,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,SAC1CgI,gBAAgB,aAClB,oCAAC,gBAAa,QAAQO,kBAAiB;AAAA,IAAA1I,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,SACrCgI,gBAAgB,eAClB,oCAAC,sBAAmB,QAAQO,kBAAiB;AAAA,IAAA1I,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,SAC3CgI,gBAAgB,SACjB,gDAAS,QAAQ7C,iBAAiB,QAAQoD,kBAAiB;AAAA,IAAA1I,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,SAE3D,8CAAK,WAAU,gBAAc;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,IAC5B,uCAAC,gBAAc;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,KAAG,GACjB,+DAAwB,kBAAkBmI,sBAAqB;AAAA,IAAAtI,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,KAAG,GAClE,oDAAa,cAAcsI,kBAAiB;AAAA,IAAAzI,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,KAAG,CAClD,CAEJ,GAEA,oCAAC,QACC,mBACA,kBAAkBmI,sBAAqB;AAAA,IAAAtI,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,KACvC,CACJ;AAEJ;;AC/FA,MAAMyI,cAAc,IAAIC,YAAY;AAAA,EAClCC,gBAAgB;AAAA,IACdC,SAAS;AAAA,MACPC,OAAO;AAAA,MACPC,WAAW;AAAA;AAAA,MACXC,sBAAsB;AAAA;AAAA,EACxB;AAEJ,CAAC;AAGD,eAAeC,gBAAgB;AACzB;AACFC,YAAQC,KAAK,sCAAsC;AAG7CC,mBAAS,MAAMC,6BAA6B;AAGlDC,aAASC,WAAWrI,SAASsI,eAAe,MAAM,CAAC,EAAEC,OACnD,oCAAC,MAAM,YAAN,EAAgB;AAAA,MAAA3J,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,SACf,oCAAC,gBAAc;AAAA,MAAAH,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,SACZ,2DAAoB,QAAQyI,aAAY;AAAA,MAAA5I,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,SACtC,sDAAe,QAAe;AAAA,MAAAH,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,SAC7B,oCAAC,kBAAgB;AAAA,MAAAH,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,SACf,oCAAC,iBAAe;AAAA,MAAAH,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,SACd,oCAAC,eAAa;AAAA,MAAAH,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,SACZ,oCAAC,uBAAqB;AAAA,MAAAH,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,SACpB,oCAAC,KAAG;AAAA,MAAAH,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,OAAG,CACT,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF;AAEAiJ,YAAQC,KAAK,8CAA8C;AAAA,WACpD3G,OAAO;AACNA,kBAAM,sDAAsDA,KAAK;AAGzE8G,aAASC,WAAWrI,SAASsI,eAAe,MAAM,CAAC,EAAEC,OACnD,oCAAC,OAAI,aAAU,kBAAgB;AAAA,MAAA3J,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,SAC7B,oCAAC,MAAE;AAAA,MAAAH,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,SAAC,uBAAqB,uCACxB,KAAC;AAAA,MAAAH,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,SAAC,oEAAkE,GACrE,oCAAC,UAAO,WAAS,MAAMyJ,OAAOC,SAASC,UAAS;AAAA,MAAA9J,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAC,iBAEjD,GAEG,6CAAI,WAAU,iBAAe;AAAA,MAAAH,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,SAC5B,oCAAC,KAAC;AAAA,MAAAH,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,SAAEuC,MAAMzB,OAAQ,uCACjB,OAAG;AAAA,MAAAjB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,SAAEuC,MAAMqH,KAAM,CACpB,CAEJ,CACF;AAAA;AAEJ;AAGAZ,cAAc", "names": ["Header", "onLogoClick", "ActivitiesGridComponent", "activities", "onActivitySelect", "handleActivityClick", "activityId", "activityTitle", "defaultActivities", "id", "title", "description", "icon", "badge", "color", "displayActivities", "length", "styles", "container", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "grid", "map", "activity", "index", "card", "animationDelay", "<PERSON><PERSON><PERSON><PERSON>", "blue", "cardTitle", "cardDescription", "DonationBanner", "pix<PERSON><PERSON>", "showNotification", "message", "type", "notification", "document", "createElement", "className", "textContent", "assign", "style", "position", "top", "right", "padding", "borderRadius", "fontWeight", "fontSize", "zIndex", "transform", "transition", "boxShadow", "display", "alignItems", "gap", "colors", "success", "error", "background", "prepend", "body", "append<PERSON><PERSON><PERSON>", "setTimeout", "opacity", "remove", "copyPix", "navigator", "clipboard", "writeText", "then", "catch", "banner", "y", "duration", "<PERSON><PERSON><PERSON><PERSON>", "welcomeTitle", "heart", "text", "qrSection", "qr<PERSON><PERSON><PERSON>", "encodeURIComponent", "qrImage", "scale", "e", "key", "donationInfo", "donationText", "ToolsSection", "onToolSelect", "handleToolClick", "toolId", "toolTitle", "toolType", "tools", "tool", "char<PERSON>t", "toUpperCase", "slice", "badgeBlue", "name", "Footer", "currentActivity", "onActivityChange", "activityName", "footer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "navigationGrid", "navButton", "active", "navIcon", "navLabel", "ContagemNumerosGame", "lazy", "LetterRecognitionGame", "MusicalSequenceGame", "MemoryGame", "ColorMatchGame", "ImageAssociationGame", "PadroesVisuaisGame", "QuebraCabecaGame", "CreativePaintingGame", "GameLoading<PERSON><PERSON><PERSON>", "flexDirection", "justifyContent", "height", "fontFamily", "marginBottom", "GamePage", "gameId", "onBack", "gameComponents", "GameComponent", "Suspense", "gameInfo", "content", "game", "textAlign", "margin", "About", "AdminPanel", "AccessibilityPage", "UserProfiles", "DashboardContainer", "App", "setCurrentActivity", "useState", "currentPage", "setCurrentPage", "handleLogoClick", "handleActivityChange", "gameIds", "includes", "handleToolSelect", "handleBackToHome", "LoadingFallback", "queryClient", "QueryClient", "defaultOptions", "queries", "retry", "staleTime", "refetchOnWindowFocus", "initializeApp", "console", "info", "system", "initializePortalBetinaSystem", "ReactDOM", "createRoot", "getElementById", "render", "window", "location", "reload", "stack"], "ignoreList": [], "sources": ["../../src/components/navigation/Header/Header.jsx", "../../src/components/navigation/ActivitiesGrid/ActivitiesGrid.jsx", "../../src/components/navigation/DonationBanner/DonationBanner.jsx", "../../src/components/navigation/ToolsSection/ToolsSection.jsx", "../../src/components/navigation/Footer/Footer.jsx", "../../src/components/pages/GamePage.jsx", "../../src/components/pages/App.jsx", "../../src/main.jsx"], "sourcesContent": ["/**\r\n * @file Header.jsx\r\n * @description Header do Portal Betina - Temporariamente desabilitado\r\n * @version 3.0.0\r\n */\r\n\r\nimport React from 'react'\r\n\r\nfunction Header({ onLogoClick }) {\r\n  // Header temporariamente desabilitado\r\n  return null\r\n}\r\n\r\nexport default Header\r\n", "/**\r\n * @file ActivitiesGrid.jsx\r\n * @description Grid de atividades do Portal Betina\r\n * @version 3.0.0\r\n */\r\n\r\nimport React, { useState, useEffect } from 'react'\r\nimport styles from './ActivitiesGrid.module.css'\r\n\r\nfunction ActivitiesGridComponent({ activities, onActivitySelect }) {\r\n  const handleActivityClick = (activityId, activityTitle) => {\r\n    if (onActivitySelect) {\r\n      onActivitySelect(activityId)\r\n    }\r\n  }\r\n\r\n  const defaultActivities = [\r\n    {\r\n      id: 'letter-recognition',\r\n      title: 'Reconhecimento de Letras',\r\n      description: 'Aprenda o alfabeto de forma divertida e interativa',\r\n      icon: '🔤',\r\n      badge: 'Letras',\r\n      color: 'blue'\r\n    },\r\n    {\r\n      id: 'number-counting',\r\n      title: 'Contagem de Números',\r\n      description: 'Pratique contagem e reconhecimento numérico',\r\n      icon: '🔢',\r\n      badge: 'Números',\r\n      color: 'orange'\r\n    },\r\n    {\r\n      id: 'memory-game',\r\n      title: '<PERSON><PERSON> da Memória',\r\n      description: 'Encontre os pares e exercite sua memória',\r\n      icon: '🧠',\r\n      badge: 'Memória',\r\n      color: 'green'\r\n    },\r\n    {\r\n      id: 'color-match',\r\n      title: 'Combinação de Cores',\r\n      description: 'Combine cores e desenvolva percepção visual',\r\n      icon: '🌈',\r\n      badge: 'Cores',\r\n      color: 'purple'\r\n    },\r\n    {\r\n      id: 'musical-sequence',\r\n      title: 'Sequência Musical',\r\n      description: 'Repita sequências sonoras e desenvolva a memória auditiva',\r\n      icon: '🎵',\r\n      badge: 'Música',\r\n      color: 'pink'\r\n    },\r\n    {\r\n      id: 'image-association',\r\n      title: 'Associação de Imagens',\r\n      description: 'Associe imagens e desenvolva conexões cognitivas',\r\n      icon: '🧩',\r\n      badge: 'Imagens',\r\n      color: 'cyan'\r\n    },\r\n    {\r\n      id: 'padroes-visuais',\r\n      title: 'Padrões Visuais',\r\n      description: 'Identifique e complete padrões visuais complexos',\r\n      icon: '🔷',\r\n      badge: 'Padrões',\r\n      color: 'indigo'\r\n    },\r\n    {\r\n      id: 'quebra-cabeca',\r\n      title: 'Quebra-Cabeça',\r\n      description: 'Monte quebra-cabeças e desenvolva raciocínio espacial',\r\n      icon: '🧩',\r\n      badge: 'Puzzle',\r\n      color: 'teal'\r\n    },\r\n    {\r\n      id: 'creative-painting',\r\n      title: 'Pintura Criativa',\r\n      description: 'Expresse sua criatividade através da arte digital',\r\n      icon: '🎨',\r\n      badge: 'Arte',\r\n      color: 'red'\r\n    }\r\n  ]\r\n\r\n  const displayActivities = activities?.length ? activities : defaultActivities\r\n\r\n  return (\r\n    <section className={styles.container}>\r\n      <h2 className={styles.title}>\r\n        🎯 Atividades Mais Populares\r\n      </h2>\r\n\r\n      <div className={styles.grid}>\r\n        {displayActivities.map((activity, index) => (\r\n          <button\r\n            key={activity.id}\r\n            className={styles.card}\r\n            onClick={() => handleActivityClick(activity.id, activity.title)}\r\n            style={{ animationDelay: `${index * 0.1}s` }}\r\n          >\r\n            <div className={styles.cardHeader}>\r\n              <div className={styles.icon}>{activity.icon}</div>\r\n              <span className={`${styles.badge} ${styles[activity.color] || styles.blue}`}>\r\n                {activity.badge}\r\n              </span>\r\n            </div>\r\n            \r\n            <h3 className={styles.cardTitle}>{activity.title}</h3>\r\n            <p className={styles.cardDescription}>{activity.description}</p>\r\n          </button>\r\n        ))}\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n\r\nexport default ActivitiesGridComponent\r\n", "/**\r\n * @file DonationBanner.jsx\r\n * @description Banner de doação do Portal Betina com UI/UX aprimorada\r\n * @version 3.1.0\r\n */\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport styles from './DonationBanner.module.css';\r\n\r\nfunction DonationBanner() {\r\n  const pixKey = \"<EMAIL>\";\r\n\r\n  const showNotification = (message, type = 'success') => {\r\n    const notification = document.createElement('div');\r\n    notification.className = `notification ${type}`;\r\n    notification.textContent = message;\r\n    \r\n    Object.assign(notification.style, {\r\n      position: 'fixed',\r\n      top: '20px',\r\n      right: '20px',\r\n      padding: '12px 24px',\r\n      borderRadius: '8px',\r\n      color: '#fff',\r\n      fontWeight: '600',\r\n      fontSize: '14px',\r\n      zIndex: '10000',\r\n      transform: 'translateX(100%)',\r\n      transition: 'transform 0.3s ease, opacity 0.3s ease',\r\n      boxShadow: '0 4px 12px rgba(0,0,0,0.2)',\r\n      display: 'flex',\r\n      alignItems: 'center',\r\n      gap: '8px',\r\n    });\r\n\r\n    const colors = {\r\n      success: 'linear-gradient(135deg, #10b981, #059669)',\r\n      error: 'linear-gradient(135deg, #ef4444, #dc2626)',\r\n    };\r\n    notification.style.background = colors[type] || colors.success;\r\n\r\n    const icon = document.createElement('span');\r\n    icon.textContent = type === 'success' ? '✅' : '❌';\r\n    notification.prepend(icon);\r\n\r\n    document.body.appendChild(notification);\r\n\r\n    setTimeout(() => {\r\n      notification.style.transform = 'translateX(0)';\r\n      notification.style.opacity = '1';\r\n    }, 100);\r\n\r\n    setTimeout(() => {\r\n      notification.style.transform = 'translateX(100%)';\r\n      notification.style.opacity = '0';\r\n      setTimeout(() => notification.remove(), 300);\r\n    }, 3000);\r\n  };\r\n\r\n  const copyPix = () => {\r\n    navigator.clipboard.writeText(pixKey).then(() => {\r\n      showNotification('Chave PIX copiada com sucesso!', 'success');\r\n    }).catch(() => {\r\n      showNotification('Erro ao copiar chave PIX', 'error');\r\n    });\r\n  };\r\n\r\n  return (\r\n    <motion.section\r\n      className={styles.banner}\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.5 }}\r\n      role=\"region\"\r\n      aria-label=\"Banner de doação\"\r\n    >\r\n      <div className={styles.welcomeHeader}>\r\n        <h1 className={styles.welcomeTitle}>\r\n          🌟 Bem-vindos ao Portal Bettina! 🌟\r\n        </h1>\r\n      </div>\r\n\r\n      <h2 className={styles.title}>\r\n        Ajude a manter este projeto vivo\r\n        <span className={styles.heart} aria-hidden=\"true\">💖</span>\r\n      </h2>\r\n\r\n      <p className={styles.text}>\r\n        Este portal nasceu da história da minha filha Bettina e do desejo de apoiar outras crianças no seu desenvolvimento. \r\n        Oferecemos gratuitamente atividades terapêuticas e educativas para crianças com autismo, TDAH e outras necessidades cognitivas.\r\n        <strong> Faça sua doação se possível!</strong>\r\n      </p>\r\n      \r\n      <div className={styles.qrSection}>\r\n        <div className={styles.qrContainer}>\r\n          <motion.img \r\n            src={`https://api.qrserver.com/v1/create-qr-code/?size=120x120&data=${encodeURIComponent(pixKey)}`}\r\n            alt=\"QR Code PIX para doação\"\r\n            className={styles.qrImage}\r\n            title=\"Clique para copiar a chave PIX ou escaneie com seu banco\"\r\n            onClick={copyPix}\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            role=\"button\"\r\n            tabIndex={0}\r\n            onKeyDown={(e) => e.key === 'Enter' && copyPix()}\r\n          />\r\n        </div>\r\n\r\n        <div className={styles.donationInfo}>\r\n          <ul className={styles.donationText}>\r\n            <li>Escaneie o QR Code com seu banco</li>\r\n            <li>Ou pressione o QR code para copiar a chave</li>\r\n            <li>Qualquer valor é bem-vindo! 🙏</li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </motion.section>\r\n  );\r\n}\r\n\r\nexport default DonationBanner;", "/**\r\n * @file ToolsSection.jsx\r\n * @description Seção de ferramentas e configurações do Portal Betina\r\n * @version 3.0.0\r\n */\r\n\r\nimport React from 'react'\r\nimport styles from './ToolsSection.module.css'\r\n\r\nfunction ToolsSection({ onToolSelect }) {  const handleToolClick = (toolId, toolTitle, toolType = 'info') => {\r\n    if (onToolSelect) {\r\n      onToolSelect(toolId)\r\n    }\r\n    // Removido: notificação desnecessária\r\n  }\r\n  const tools = [\r\n    {\r\n      id: 'dashboard-performance',\r\n      title: 'Dashboards do Sistema',\r\n      description: 'Performance, IA, Neuropedagógico, Multissensorial e Sistema Integrado',\r\n      icon: '📊',\r\n      badge: 'Premium',\r\n      color: 'premium',\r\n      type: 'premium'\r\n    },\r\n    {\r\n      id: 'user-profiles',\r\n      title: 'Perfis de Usuário',\r\n      description: 'Gerencie diferentes perfis para toda a família',\r\n      icon: '👤',\r\n      badge: 'Perfis',\r\n      color: 'purple',\r\n      type: 'info'\r\n    },\r\n    {\r\n      id: 'admin-panel',\r\n      title: 'Painel Administrativo',\r\n      description: 'Configurações avançadas e gerenciamento do sistema',\r\n      icon: '🔐',\r\n      badge: 'Admin',\r\n      color: 'red',\r\n      type: 'warning'\r\n    },\r\n    {\r\n      id: 'about-info',\r\n      title: 'Sobre o Portal',\r\n      description: 'Informações sobre o projeto, versão e créditos',\r\n      icon: 'ℹ️',\r\n      badge: 'Info',\r\n      color: 'cyan',\r\n      type: 'info'\r\n    },\r\n    {\r\n      id: 'accessibility-settings',\r\n      title: 'Configurações de Acessibilidade',\r\n      description: 'Alto contraste, tamanho da fonte e outras opções de acessibilidade',\r\n      icon: '♿',\r\n      badge: 'Acessibilidade',\r\n      color: 'special',\r\n      type: 'special'\r\n    }\r\n  ]\r\n\r\n  return (\r\n    <section className={styles.container}>\r\n      <h2 className={styles.title}>\r\n        ⚙️ Ferramentas e Configurações\r\n      </h2>\r\n\r\n      <div className={styles.grid}>\r\n        {tools.map((tool, index) => (\r\n          <button\r\n            key={tool.id}\r\n            className={`${styles.card} ${styles[tool.color] || styles.blue}`}\r\n            onClick={() => handleToolClick(tool.id, tool.title, tool.type)}\r\n            style={{ animationDelay: `${index * 0.1}s` }}\r\n          >\r\n            <div className={styles.cardHeader}>\r\n              <div className={styles.icon}>{tool.icon}</div>\r\n              <span className={`${styles.badge} ${styles[`badge${tool.color.charAt(0).toUpperCase() + tool.color.slice(1)}`] || styles.badgeBlue}`}>\r\n                {tool.badge}\r\n              </span>\r\n            </div>\r\n            \r\n            <h3 className={styles.cardTitle}>{tool.title}</h3>\r\n            <p className={styles.cardDescription}>{tool.description}</p>\r\n          </button>\r\n        ))}\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n\r\nexport default ToolsSection\r\n", "/**\r\n * @file Footer.jsx\r\n * @description Footer simples do Portal Betina\r\n * @version 3.0.0\r\n */\r\n\r\nimport React from 'react'\r\nimport styles from './Footer.module.css'\r\n\r\nconst activities = [\r\n  { id: 'home', name: '<PERSON><PERSON><PERSON>', icon: '🏠' },\r\n  { id: 'letter-recognition', name: '<PERSON><PERSON>', icon: '🔤' },\r\n  { id: 'number-counting', name: '<PERSON><PERSON><PERSON><PERSON>', icon: '🔢' },\r\n  { id: 'memory-game', name: 'Me<PERSON><PERSON><PERSON>', icon: '🧠' },\r\n  { id: 'musical-sequence', name: 'Música', icon: '🎵' },\r\n  { id: 'color-match', name: 'Cores', icon: '🌈' },\r\n  { id: 'image-association', name: 'Imagens', icon: '🖼️' },\r\n  { id: 'padroes-visuais', name: '<PERSON><PERSON><PERSON><PERSON>', icon: '🔷' },\r\n  { id: 'quebra-cabeca', name: 'Puzzle', icon: '🧩' },\r\n  { id: 'creative-painting', name: 'Arte', icon: '🎨' }\r\n]\r\n\r\n/**\r\n * Componente de rodapé com navegação para atividades\r\n */\r\nfunction Footer({ currentActivity, onActivityChange }) {\r\n  const handleActivityClick = (activityId, activityName) => {\r\n    if (onActivityChange) {\r\n      onActivityChange(activityId)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <footer className={styles.footer}>\r\n      <div className={styles.footerContent}>\r\n        <div className={styles.navigationGrid}>\r\n          {activities.map((activity) => (            <button\r\n              key={activity.id}\r\n              className={`${styles.navButton} ${currentActivity === activity.id ? styles.active : ''}`}\r\n              onClick={() => handleActivityClick(activity.id, activity.name)}\r\n              role=\"button\"\r\n              aria-label={`Navegar para ${activity.name}`}\r\n            >\r\n              <div className={styles.navIcon}>{activity.icon}</div>\r\n              <div className={styles.navLabel}>{activity.name}</div>\r\n            </button>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  )\r\n}\r\n\r\nexport default Footer\r\n", "import React, { Suspense, lazy } from 'react'\r\n\r\n// Lazy loading dos jogos para reduzir bundle size\r\nconst ContagemNumerosGame = lazy(() => import('../../games/ContagemNumeros/ContagemNumerosGame.jsx'))\r\nconst LetterRecognitionGame = lazy(() => import('../../games/LetterRecognition/LetterRecognitionGame.jsx'))\r\nconst MusicalSequenceGame = lazy(() => import('../../games/MusicalSequence/MusicalSequenceGame.jsx'))\r\nconst MemoryGame = lazy(() => import('../../games/MemoryGame/MemoryGame.jsx'))\r\nconst ColorMatchGame = lazy(() => import('../../games/ColorMatch/ColorMatchGame.jsx'))\r\nconst ImageAssociationGame = lazy(() => import('../../games/ImageAssociation/ImageAssociationGame.jsx'))\r\nconst PadroesVisuaisGame = lazy(() => import('../../games/PadroesVisuais/PadroesVisuaisGame.jsx'))\r\nconst QuebraCabecaGame = lazy(() => import('../../games/QuebraCabeca/QuebraCabecaGame.jsx'))\r\nconst CreativePaintingGame = lazy(() => import('../../games/CreativePainting/CreativePaintingGame.jsx'))\r\n\r\n// Componente de carregamento otimizado\r\nconst GameLoadingFallback = () => (\r\n  <div style={{\r\n    display: 'flex',\r\n    flexDirection: 'column',\r\n    justifyContent: 'center',\r\n    alignItems: 'center',\r\n    height: '100vh',\r\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n    color: 'white',\r\n    fontSize: '18px',\r\n    fontFamily: 'Arial, sans-serif'\r\n  }}>\r\n    <div style={{ marginBottom: '20px', fontSize: '48px' }}>🎮</div>\r\n    <div>Carregando jogo...</div>\r\n  </div>\r\n)\r\n\r\nconst GamePage = ({ gameId, onBack }) => {\r\n  // Mapear IDs para componentes lazy-loaded\r\n  const gameComponents = {\r\n    'numbers': ContagemNumerosGame,\r\n    'number-counting': ContagemNumerosGame,\r\n    'contagem-numeros': ContagemNumerosGame,\r\n    'letters': LetterRecognitionGame,\r\n    'letter-recognition': LetterRecognitionGame,\r\n    'music': MusicalSequenceGame,\r\n    'musical-sequence': MusicalSequenceGame,\r\n    'memory': MemoryGame,\r\n    'memory-game': MemoryGame,\r\n    'colors': ColorMatchGame,\r\n    'color-match': ColorMatchGame,\r\n    'images': ImageAssociationGame,\r\n    'image-association': ImageAssociationGame,\r\n    'patterns': PadroesVisuaisGame,\r\n    'padroes-visuais': PadroesVisuaisGame,\r\n    'quebra-cabeca': QuebraCabecaGame,\r\n    'emotional-puzzle': QuebraCabecaGame, // Quebra-cabeça emocional\r\n    'creative-painting': CreativePaintingGame,\r\n    'pintura-criativa': CreativePaintingGame\r\n  }\r\n\r\n  // Se existe componente real, renderizar o jogo com Suspense\r\n  const GameComponent = gameComponents[gameId]\r\n\r\n  if (GameComponent) {\r\n    return (\r\n      <Suspense fallback={<GameLoadingFallback />}>\r\n        <GameComponent onBack={onBack} />\r\n      </Suspense>\r\n    )\r\n  }\r\n\r\n  // Fallback para jogos não implementados\r\n  const gameInfo = {\r\n    'letter-recognition': {\r\n      title: 'Reconhecimento de Letras',\r\n      description: 'Jogo educativo para aprender o alfabeto',\r\n      content: '🔤 Este é o jogo de reconhecimento de letras! Em breve estará totalmente funcional.'\r\n    },\r\n    'letters': {\r\n      title: 'Reconhecimento de Letras',\r\n      description: 'Jogo educativo para aprender o alfabeto',\r\n      content: '🔤 Este é o jogo de reconhecimento de letras! Em breve estará totalmente funcional.'\r\n    },\r\n    'number-counting': {\r\n      title: 'Contagem de Números',\r\n      description: 'Jogo para aprender números e contagem',\r\n      content: '🔢 Este é o jogo de contagem de números! Em breve estará totalmente funcional.'\r\n    },\r\n    'numbers': {\r\n      title: 'Contagem de Números',\r\n      description: 'Jogo para aprender números e contagem',\r\n      content: '🔢 Este é o jogo de contagem de números! Em breve estará totalmente funcional.'\r\n    },\r\n    'musical-sequence': {\r\n      title: 'Sequência Musical',\r\n      description: 'Jogo de memória auditiva',\r\n      content: '🎵 Este é o jogo de sequência musical! Em breve estará totalmente funcional.'\r\n    },\r\n    'music': {\r\n      title: 'Sequência Musical',\r\n      description: 'Jogo de memória auditiva',\r\n      content: '🎵 Este é o jogo de sequência musical! Em breve estará totalmente funcional.'\r\n    },\r\n    'memory-game': {\r\n      title: 'Jogo da Memória',\r\n      description: 'Jogo clássico de memória',\r\n      content: '🧠 Este é o jogo da memória! Em breve estará totalmente funcional.'\r\n    },\r\n    'memory': {\r\n      title: 'Jogo da Memória',\r\n      description: 'Jogo clássico de memória',\r\n      content: '🧠 Este é o jogo da memória! Em breve estará totalmente funcional.'\r\n    },\r\n    'color-match': {\r\n      title: 'Combinação de Cores',\r\n      description: 'Jogo de combinação de cores',\r\n      content: '🌈 Este é o jogo de combinação de cores! Em breve estará totalmente funcional.'\r\n    },\r\n    'colors': {\r\n      title: 'Combinação de Cores',\r\n      description: 'Jogo de combinação de cores',\r\n      content: '🌈 Este é o jogo de combinação de cores! Em breve estará totalmente funcional.'\r\n    },\r\n    'image-association': {\r\n      title: 'Associação de Imagens',\r\n      description: 'Jogo de associação cognitiva',\r\n      content: '🧩 Este é o jogo de associação de imagens! Em breve estará totalmente funcional.'\r\n    },\r\n    'images': {\r\n      title: 'Associação de Imagens',\r\n      description: 'Jogo de associação cognitiva',\r\n      content: '🧩 Este é o jogo de associação de imagens! Em breve estará totalmente funcional.'\r\n    }\r\n  }\r\n\r\n  const game = gameInfo[gameId] || {\r\n    title: 'Jogo não encontrado',\r\n    description: 'Este jogo ainda não foi implementado',\r\n    content: '❌ Jogo não encontrado'\r\n  }\r\n\r\n  return (\r\n    <div style={{\r\n      padding: '2rem',\r\n      textAlign: 'center',\r\n      background: 'rgba(255, 255, 255, 0.95)',\r\n      borderRadius: '1rem',\r\n      margin: '2rem',\r\n      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'\r\n    }}>\r\n      <h1 style={{ color: '#0066cc', marginBottom: '1rem' }}>\r\n        {game.title}\r\n      </h1>\r\n      <p style={{ color: '#666', marginBottom: '2rem', fontSize: '1.1rem' }}>\r\n        {game.description}\r\n      </p>\r\n      <div style={{ \r\n        padding: '3rem', \r\n        background: '#f8f9fa', \r\n        borderRadius: '0.5rem',\r\n        marginBottom: '2rem',\r\n        fontSize: '1.2rem'\r\n      }}>\r\n        {game.content}\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default GamePage\r\n", "import React, { useState, Suspense, lazy } from 'react'\r\nimport './App.css'\r\n\r\n// Importando componentes essenciais (sempre carregados)\r\nimport Header from '../navigation/Header'\r\nimport ActivitiesGridComponent from '../navigation/ActivitiesGrid'\r\nimport DonationBanner from '../navigation/DonationBanner'\r\nimport ToolsSection from '../navigation/ToolsSection'\r\nimport Footer from '../navigation/Footer'\r\n\r\n// Lazy loading para componentes pesados\r\nconst About = lazy(() => import('./About/About'))\r\nconst AdminPanel = lazy(() => import('./AdminPanel/AdminPanel'))\r\nconst AccessibilityPage = lazy(() => import('./AccessibilityPage/AccessibilityPage'))\r\nconst UserProfiles = lazy(() => import('./UserProfiles/UserProfiles'))\r\n// BackupExport removido - funcionalidade disponível apenas no dashboard premium\r\nconst DashboardContainer = lazy(() => import('../dashboard/DashboardContainer'))\r\n\r\n// Importação estática do GamePage para evitar problemas de lazy loading\r\nimport GamePage from './GamePage.jsx'\r\n\r\nfunction App() {\r\n  const [currentActivity, setCurrentActivity] = useState('home')\r\n  const [currentPage, setCurrentPage] = useState('home')\r\n\r\n  const handleLogoClick = () => {\r\n    setCurrentActivity('home')\r\n    setCurrentPage('home')  }\r\n  \r\n  const handleActivityChange = (activityId) => {\r\n    // Lista de jogos válidos\r\n    const gameIds = [\r\n      'letter-recognition', 'number-counting', 'memory-game', \r\n      'musical-sequence', 'color-match', 'image-association',\r\n      'creative-painting', 'padroes-visuais', 'quebra-cabeca',\r\n      'letters', 'numbers', 'memory', 'music', 'colors', 'images', 'patterns'\r\n    ]\r\n    \r\n    if (gameIds.includes(activityId)) {\r\n      // Se é um jogo, navegar para a página do jogo\r\n      setCurrentActivity(activityId)\r\n      setCurrentPage('game')\r\n    } else {\r\n      // Se não é um jogo, manter na home\r\n      setCurrentActivity(activityId)\r\n      setCurrentPage('home')\r\n    }\r\n  }\r\n\r\n  const handleToolSelect = (toolId) => {\r\n    if (toolId === 'about-info') {\r\n      setCurrentPage('about')\r\n    } else if (toolId === 'admin-panel') {\r\n      setCurrentPage('admin')\r\n    } else if (toolId === 'accessibility-settings') {\r\n      setCurrentPage('accessibility')\r\n    } else if (toolId === 'user-profiles') {\r\n      setCurrentPage('profiles')\r\n    } else if (toolId === 'dashboard-performance') {\r\n      setCurrentPage('dashboards')\r\n    } else {\r\n      setCurrentActivity(toolId)\r\n      setCurrentPage('home')\r\n    }\r\n  }\r\n\r\n  const handleBackToHome = () => {\r\n    setCurrentPage('home')\r\n    setCurrentActivity('home')\r\n  }\r\n\r\n  // Componente de carregamento\r\n  const LoadingFallback = () => (\r\n    <div className=\"loading-container\" style={{\r\n      display: 'flex',\r\n      justifyContent: 'center',\r\n      alignItems: 'center',\r\n      height: '60vh',\r\n      fontSize: '18px',\r\n      color: '#666'\r\n    }}>\r\n      <div>Carregando...</div>\r\n    </div>\r\n  )\r\n\r\n  return (\r\n    <div className=\"app\">\r\n      <Header onLogoClick={handleLogoClick} />\r\n      \r\n      <Suspense fallback={<LoadingFallback />}>\r\n        {currentPage === 'about' ? (\r\n          <About onBackToHome={handleBackToHome} />\r\n        ) : currentPage === 'admin' ? (\r\n          <AdminPanel onBack={handleBackToHome} />\r\n        ) : currentPage === 'accessibility' ? (\r\n          <AccessibilityPage onBack={handleBackToHome} />\r\n        ) : currentPage === 'profiles' ? (\r\n          <UserProfiles onBack={handleBackToHome} />\r\n        ) : currentPage === 'dashboards' ? (\r\n          <DashboardContainer onBack={handleBackToHome} />\r\n        ) : currentPage === 'game' ? (\r\n          <GamePage gameId={currentActivity} onBack={handleBackToHome} />\r\n        ) : (\r\n          <main className=\"main-content\">\r\n            <DonationBanner />\r\n            <ActivitiesGridComponent onActivitySelect={handleActivityChange} />\r\n            <ToolsSection onToolSelect={handleToolSelect} />\r\n          </main>\r\n        )}\r\n      </Suspense>\r\n\r\n      <Footer \r\n        currentActivity={currentActivity}\r\n        onActivityChange={handleActivityChange}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default App\r\n", "/**\r\n * @file main.jsx\r\n * @description Entry point principal do Portal Betina V3 com sistema de resiliência integrado\r\n * @version 3.0.0\r\n */\r\n\r\nimport React from 'react'\r\nimport ReactDOM from 'react-dom/client'\r\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query'\r\nimport { HelmetProvider } from 'react-helmet-async'\r\nimport App from './components/pages/App.jsx'\r\nimport { initializePortalBetinaSystem } from './api/services/AppInitializer'\r\nimport { SystemProvider } from './components/context/SystemContext'\r\nimport { DatabaseProvider } from './components/context/DatabaseProvider'\r\nimport { AccessibilityProvider } from './components/context/AccessibilityContext'\r\nimport { PremiumProvider } from './context/PremiumContext'\r\nimport { AdminProvider } from './context/AdminContext'\r\n\r\n// Importar estilos de acessibilidade\r\nimport './styles/accessibility.css'\r\n\r\n// Configuração do React Query para gerenciamento de estado e cache\r\nconst queryClient = new QueryClient({\r\n  defaultOptions: {\r\n    queries: {\r\n      retry: 2,\r\n      staleTime: 60000, // 1 minuto\r\n      refetchOnWindowFocus: false,\r\n    },\r\n  },\r\n})\r\n\r\n// Função de inicialização principal\r\nasync function initializeApp() {\r\n  try {\r\n    console.info('🚀 Inicializando Portal Betina V3...')\r\n    \r\n    // Inicializar sistema integrado\r\n    const system = await initializePortalBetinaSystem()\r\n    \r\n    // Renderizar a aplicação com o sistema inicializado\r\n    ReactDOM.createRoot(document.getElementById('root')).render(\r\n      <React.StrictMode>\r\n        <HelmetProvider>\r\n          <QueryClientProvider client={queryClient}>\r\n            <SystemProvider system={system}>\r\n              <DatabaseProvider>\r\n                <PremiumProvider>\r\n                  <AdminProvider>\r\n                    <AccessibilityProvider>\r\n                      <App />\r\n                    </AccessibilityProvider>\r\n                  </AdminProvider>\r\n                </PremiumProvider>\r\n              </DatabaseProvider>\r\n            </SystemProvider>\r\n          </QueryClientProvider>\r\n        </HelmetProvider>\r\n      </React.StrictMode>\r\n    )\r\n    \r\n    console.info('✅ Portal Betina V3 inicializado com sucesso!')\r\n  } catch (error) {\r\n    console.error('❌ Erro fatal na inicialização do Portal Betina V3:', error)\r\n    \r\n    // Renderizar página de fallback em caso de erro crítico na inicialização\r\n    ReactDOM.createRoot(document.getElementById('root')).render(\r\n      <div className=\"critical-error\">\r\n        <h1>Erro na Inicialização</h1>\r\n        <p>Não foi possível inicializar o sistema. Tente recarregar a página.</p>\r\n        <button onClick={() => window.location.reload()}>\r\n          Recarregar\r\n        </button>\r\n        {import.meta.env.DEV && (\r\n          <div className=\"error-details\">\r\n            <p>{error.message}</p>\r\n            <pre>{error.stack}</pre>\r\n          </div>\r\n        )}\r\n      </div>\r\n    )\r\n  }\r\n}\r\n\r\n// Iniciar a aplicação\r\ninitializeApp()\r\n"], "file": "assets/index-DSRZDZjW.js"}