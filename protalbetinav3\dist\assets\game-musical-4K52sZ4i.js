import { I as IGameProcessor } from "./services-Ckq1alRq.js";
import { r as reactExports, j as jsxDevRuntimeExports } from "./vendor-react-BH-kks1U.js";
import { S as SystemContext, b as useAccessibilityContext } from "./context-CJb-Kg-5.js";
import { G as GameStartScreen } from "./game-association-1fe4bzjE.js";
import { a as useUnifiedGameLogic, b as useMultisensoryIntegration, c as useTherapeuticOrchestrator } from "./hooks-DiB_syzW.js";
class AuditoryMemoryCollector {
  constructor() {
    this.memoryData = [];
    this.sequenceRetentionData = [];
    this.temporalPatterns = [];
    this.auditorySpanData = [];
    this.debugMode = true;
  }
  /**
   * Coleta dados de retenção de memória auditiva
   */
  collectMemoryRetention(interactionData) {
    try {
      if (!interactionData || typeof interactionData !== "object") {
        console.warn("AuditoryMemoryCollector: Dados de interação inválidos");
        return null;
      }
      const {
        sequence = [],
        playerResponse = [],
        sequenceLength = 0,
        responseTime = 0,
        isCorrect = false,
        partialCorrect = 0,
        attemptNumber = 0,
        difficulty = "",
        timestamp = Date.now()
      } = interactionData;
      const retentionData = {
        timestamp,
        sequenceLength,
        auditorySpan: this.calculateAuditorySpan(sequence, playerResponse),
        memoryDecay: this.calculateMemoryDecay(sequence, playerResponse),
        retentionRate: this.calculateRetentionRate(partialCorrect, sequenceLength),
        serialPositionEffect: this.analyzeSerialPositionEffect(sequence, playerResponse),
        responseLatency: responseTime,
        isCorrect,
        difficulty,
        attemptNumber,
        forgettingCurve: this.analyzeForgettingCurve(sequence, playerResponse)
      };
      this.memoryData.push(retentionData);
      if (this.debugMode) {
        console.log("🧠 AuditoryMemoryCollector - Dados de retenção coletados:", retentionData);
      }
      return retentionData;
    } catch (error) {
      console.error("Erro no AuditoryMemoryCollector.collectMemoryRetention:", error);
      return null;
    }
  }
  /**
   * Analisa padrões de sequência temporal
   */
  analyzeTemporalSequence(sequenceData) {
    try {
      if (!sequenceData || !Array.isArray(sequenceData.sequence)) {
        console.warn("AuditoryMemoryCollector: Dados de sequência inválidos");
        return null;
      }
      const {
        sequence = [],
        playerResponse = [],
        timeBetweenNotes = [],
        difficulty = "",
        timestamp = Date.now()
      } = sequenceData;
      const temporalAnalysis = {
        timestamp,
        sequencePattern: this.identifySequencePattern(sequence),
        temporalGrouping: this.analyzeTemporalGrouping(timeBetweenNotes),
        rhythmicAccuracy: this.assessRhythmicAccuracy(sequence, playerResponse),
        chunking: this.analyzeChunking(sequence, playerResponse),
        temporalOrder: this.analyzeTemporalOrder(sequence, playerResponse),
        difficulty
      };
      this.temporalPatterns.push(temporalAnalysis);
      if (this.debugMode) {
        console.log("⏰ AuditoryMemoryCollector - Análise temporal:", temporalAnalysis);
      }
      return temporalAnalysis;
    } catch (error) {
      console.error("Erro no AuditoryMemoryCollector.analyzeTemporalSequence:", error);
      return null;
    }
  }
  /**
   * Coleta dados sobre span auditivo (amplitude de memória)
   */
  collectAuditorySpan(spanData) {
    try {
      if (!spanData || typeof spanData !== "object") {
        console.warn("AuditoryMemoryCollector: Dados de span inválidos");
        return null;
      }
      const {
        maxCorrectSequence = 0,
        consistentSpan = 0,
        improvementRate = 0,
        difficulty = "",
        sessionDuration = 0,
        timestamp = Date.now()
      } = spanData;
      const auditorySpan = {
        timestamp,
        maxSpan: maxCorrectSequence,
        consistentSpan,
        spanGrowth: this.calculateSpanGrowth(maxCorrectSequence),
        reliabilityIndex: this.calculateReliabilityIndex(consistentSpan, maxCorrectSequence),
        improvementRate,
        difficulty,
        sessionDuration,
        spanEfficiency: this.calculateSpanEfficiency(maxCorrectSequence, sessionDuration)
      };
      this.auditorySpanData.push(auditorySpan);
      if (this.debugMode) {
        console.log("📏 AuditoryMemoryCollector - Dados de span auditivo:", auditorySpan);
      }
      return auditorySpan;
    } catch (error) {
      console.error("Erro no AuditoryMemoryCollector.collectAuditorySpan:", error);
      return null;
    }
  }
  // Métodos auxiliares para análise
  calculateAuditorySpan(sequence, response) {
    if (!Array.isArray(sequence) || !Array.isArray(response)) return 0;
    let correctCount = 0;
    for (let i = 0; i < Math.min(sequence.length, response.length); i++) {
      if (sequence[i] === response[i]) {
        correctCount++;
      } else {
        break;
      }
    }
    return correctCount;
  }
  calculateMemoryDecay(sequence, response) {
    if (!Array.isArray(sequence) || !Array.isArray(response)) return 1;
    const totalItems = sequence.length;
    const correctItems = this.calculateAuditorySpan(sequence, response);
    return correctItems / totalItems;
  }
  calculateRetentionRate(partialCorrect, sequenceLength) {
    if (sequenceLength === 0) return 0;
    return (partialCorrect || 0) / sequenceLength;
  }
  analyzeSerialPositionEffect(sequence, response) {
    if (!Array.isArray(sequence) || !Array.isArray(response)) {
      return { primacy: 0, recency: 0, middle: 0 };
    }
    const length = sequence.length;
    if (length < 3) return { primacy: 0, recency: 0, middle: 0 };
    const primacyCorrect = sequence[0] === response[0] ? 1 : 0;
    const recencyCorrect = sequence[length - 1] === response[length - 1] ? 1 : 0;
    let middleCorrect = 0;
    for (let i = 1; i < length - 1; i++) {
      if (sequence[i] === response[i]) middleCorrect++;
    }
    return {
      primacy: primacyCorrect,
      recency: recencyCorrect,
      middle: middleCorrect / Math.max(1, length - 2)
    };
  }
  analyzeForgettingCurve(sequence, response) {
    const accuracy = this.calculateAuditorySpan(sequence, response) / sequence.length;
    return {
      accuracy,
      retentionStrength: Math.pow(accuracy, 0.5),
      // Modelo simplificado da curva do esquecimento
      consolidationLevel: accuracy > 0.8 ? "strong" : accuracy > 0.5 ? "moderate" : "weak"
    };
  }
  identifySequencePattern(sequence) {
    if (!Array.isArray(sequence) || sequence.length < 2) return "none";
    const patterns = {
      ascending: true,
      descending: true,
      repetitive: false,
      alternating: true
    };
    for (let i = 1; i < sequence.length; i++) {
      if (sequence[i] <= sequence[i - 1]) patterns.ascending = false;
      if (sequence[i] >= sequence[i - 1]) patterns.descending = false;
      if (i > 1 && sequence[i] === sequence[i - 2]) patterns.alternating = false;
    }
    const uniqueElements = new Set(sequence).size;
    patterns.repetitive = uniqueElements < sequence.length * 0.7;
    if (patterns.ascending) return "ascending";
    if (patterns.descending) return "descending";
    if (patterns.alternating && sequence.length > 2) return "alternating";
    if (patterns.repetitive) return "repetitive";
    return "random";
  }
  analyzeTemporalGrouping(timeBetweenNotes) {
    if (!Array.isArray(timeBetweenNotes) || timeBetweenNotes.length === 0) {
      return { groups: 0, avgGroupSize: 0 };
    }
    const avgTime = timeBetweenNotes.reduce((a, b) => a + b, 0) / timeBetweenNotes.length;
    const threshold = avgTime * 1.5;
    let groups = 1;
    for (const time of timeBetweenNotes) {
      if (time > threshold) groups++;
    }
    return {
      groups,
      avgGroupSize: (timeBetweenNotes.length + 1) / groups,
      temporalVariability: this.calculateVariability(timeBetweenNotes)
    };
  }
  assessRhythmicAccuracy(sequence, response) {
    if (!Array.isArray(sequence) || !Array.isArray(response)) return 0;
    const correctCount = this.calculateAuditorySpan(sequence, response);
    return correctCount / sequence.length;
  }
  analyzeChunking(sequence, response) {
    if (!Array.isArray(sequence) || sequence.length < 4) return { chunkSize: 1, efficiency: 0 };
    const possibleChunkSizes = [2, 3, 4];
    let bestChunkSize = 1;
    let bestAccuracy = 0;
    for (const chunkSize of possibleChunkSizes) {
      if (sequence.length % chunkSize === 0) {
        let chunkAccuracy = 0;
        const numChunks = sequence.length / chunkSize;
        for (let i = 0; i < numChunks; i++) {
          const chunkStart = i * chunkSize;
          let chunkCorrect = true;
          for (let j = 0; j < chunkSize; j++) {
            if (sequence[chunkStart + j] !== response[chunkStart + j]) {
              chunkCorrect = false;
              break;
            }
          }
          if (chunkCorrect) chunkAccuracy++;
        }
        const accuracy = chunkAccuracy / numChunks;
        if (accuracy > bestAccuracy) {
          bestAccuracy = accuracy;
          bestChunkSize = chunkSize;
        }
      }
    }
    return {
      chunkSize: bestChunkSize,
      efficiency: bestAccuracy,
      chunkingStrategy: bestChunkSize > 1 ? "chunking" : "sequential"
    };
  }
  analyzeTemporalOrder(sequence, response) {
    if (!Array.isArray(sequence) || !Array.isArray(response)) return { orderAccuracy: 0 };
    let orderErrors = 0;
    for (let i = 0; i < Math.min(sequence.length, response.length); i++) {
      if (sequence[i] !== response[i]) orderErrors++;
    }
    return {
      orderAccuracy: 1 - orderErrors / sequence.length,
      orderErrors,
      temporalConfusion: orderErrors > sequence.length * 0.3
    };
  }
  calculateSpanGrowth(currentSpan) {
    if (this.auditorySpanData.length === 0) return 0;
    const previousSpan = this.auditorySpanData[this.auditorySpanData.length - 1]?.maxSpan || 0;
    return currentSpan - previousSpan;
  }
  calculateReliabilityIndex(consistentSpan, maxSpan) {
    if (maxSpan === 0) return 0;
    return consistentSpan / maxSpan;
  }
  calculateSpanEfficiency(span, duration) {
    if (duration === 0) return 0;
    return span / (duration / 1e3);
  }
  calculateVariability(values) {
    if (!Array.isArray(values) || values.length === 0) return 0;
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }
  // Métodos de relatório
  getMemoryReport() {
    return {
      totalSessions: this.memoryData.length,
      averageRetentionRate: this.memoryData.reduce((sum, data) => sum + data.retentionRate, 0) / this.memoryData.length || 0,
      memorySpanTrend: this.auditorySpanData.map((data) => data.maxSpan),
      consolidationLevels: this.memoryData.map((data) => data.forgettingCurve.consolidationLevel),
      temporalPatterns: this.temporalPatterns.map((data) => data.sequencePattern)
    };
  }
  clearData() {
    this.memoryData = [];
    this.sequenceRetentionData = [];
    this.temporalPatterns = [];
    this.auditorySpanData = [];
    if (this.debugMode) {
      console.log("🧠 AuditoryMemoryCollector - Dados limpos");
    }
  }
}
class MusicalPatternCollector {
  constructor() {
    this.patternData = [];
    this.melodicPatterns = [];
    this.rhythmicPatterns = [];
    this.instrumentPreferences = {};
    this.harmonicAnalysis = [];
    this.debugMode = true;
  }
  /**
   * Coleta dados sobre reconhecimento de padrões melódicos
   */
  collectMelodicPattern(patternData) {
    try {
      if (!patternData || typeof patternData !== "object") {
        console.warn("MusicalPatternCollector: Dados de padrão inválidos");
        return null;
      }
      const {
        sequence = [],
        playerResponse = [],
        instruments = [],
        difficulty = "",
        isCorrect = false,
        responseTime = 0,
        sequenceType = "random",
        timestamp = Date.now()
      } = patternData;
      const melodicAnalysis = {
        timestamp,
        patternType: this.identifyMelodicPattern(sequence, instruments),
        intervalAnalysis: this.analyzeMelodicIntervals(sequence, instruments),
        contourRecognition: this.analyzeMelodicContour(sequence, instruments),
        patternComplexity: this.calculatePatternComplexity(sequence),
        reproductionAccuracy: this.assessReproductionAccuracy(sequence, playerResponse),
        instrumentTransitions: this.analyzeInstrumentTransitions(sequence, instruments),
        difficulty,
        isCorrect,
        responseTime,
        sequenceType,
        timbralRecognition: this.analyzeTimbralRecognition(instruments, playerResponse)
      };
      this.melodicPatterns.push(melodicAnalysis);
      if (this.debugMode) {
        console.log("🎵 MusicalPatternCollector - Padrão melódico coletado:", melodicAnalysis);
      }
      return melodicAnalysis;
    } catch (error) {
      console.error("Erro no MusicalPatternCollector.collectMelodicPattern:", error);
      return null;
    }
  }
  /**
   * Analisa padrões rítmicos e temporais
   */
  analyzeRhythmicPattern(rhythmData) {
    try {
      if (!rhythmData || typeof rhythmData !== "object") {
        console.warn("MusicalPatternCollector: Dados rítmicos inválidos");
        return null;
      }
      const {
        sequence = [],
        playerResponse = [],
        timings = [],
        expectedTimings = [],
        tempo = 120,
        difficulty = "",
        timestamp = Date.now()
      } = rhythmData;
      const rhythmicAnalysis = {
        timestamp,
        rhythmicAccuracy: this.calculateRhythmicAccuracy(timings, expectedTimings),
        tempoConsistency: this.analyzeTempoConsistency(timings),
        rhythmicPattern: this.identifyRhythmicPattern(timings),
        syncopationRecognition: this.analyzeSyncopation(timings, tempo),
        metricalGrouping: this.analyzeMetricalGrouping(timings),
        rhythmicMemory: this.assessRhythmicMemory(sequence, playerResponse),
        timingPrecision: this.calculateTimingPrecision(timings, expectedTimings),
        difficulty,
        tempo
      };
      this.rhythmicPatterns.push(rhythmicAnalysis);
      if (this.debugMode) {
        console.log("🥁 MusicalPatternCollector - Padrão rítmico analisado:", rhythmicAnalysis);
      }
      return rhythmicAnalysis;
    } catch (error) {
      console.error("Erro no MusicalPatternCollector.analyzeRhythmicPattern:", error);
      return null;
    }
  }
  /**
   * Coleta preferências e reconhecimento de instrumentos
   */
  collectInstrumentPreferences(instrumentData) {
    try {
      if (!instrumentData || typeof instrumentData !== "object") {
        console.warn("MusicalPatternCollector: Dados de instrumento inválidos");
        return null;
      }
      const {
        instrument = "",
        correctRecognition = false,
        responseTime = 0,
        confidence = 0,
        frequency = 0,
        context = "sequence",
        timestamp = Date.now()
      } = instrumentData;
      if (!this.instrumentPreferences[instrument]) {
        this.instrumentPreferences[instrument] = {
          totalExposures: 0,
          correctRecognitions: 0,
          avgResponseTime: 0,
          avgConfidence: 0,
          frequencies: [],
          contexts: {}
        };
      }
      const pref = this.instrumentPreferences[instrument];
      pref.totalExposures++;
      if (correctRecognition) pref.correctRecognitions++;
      pref.avgResponseTime = (pref.avgResponseTime * (pref.totalExposures - 1) + responseTime) / pref.totalExposures;
      pref.avgConfidence = (pref.avgConfidence * (pref.totalExposures - 1) + confidence) / pref.totalExposures;
      if (frequency > 0) pref.frequencies.push(frequency);
      if (!pref.contexts[context]) pref.contexts[context] = 0;
      pref.contexts[context]++;
      const instrumentAnalysis = {
        timestamp,
        instrument,
        recognitionAccuracy: pref.correctRecognitions / pref.totalExposures,
        avgResponseTime: pref.avgResponseTime,
        frequencyRange: this.calculateFrequencyRange(pref.frequencies),
        timbralDiscrimination: this.calculateTimbralDiscrimination(instrument),
        preferenceScore: this.calculatePreferenceScore(instrument),
        context
      };
      if (this.debugMode) {
        console.log("🎺 MusicalPatternCollector - Preferência de instrumento:", instrumentAnalysis);
      }
      return instrumentAnalysis;
    } catch (error) {
      console.error("Erro no MusicalPatternCollector.collectInstrumentPreferences:", error);
      return null;
    }
  }
  /**
   * Analisa reconhecimento de padrões harmônicos
   */
  analyzeHarmonicPattern(harmonicData) {
    try {
      if (!harmonicData || typeof harmonicData !== "object") {
        console.warn("MusicalPatternCollector: Dados harmônicos inválidos");
        return null;
      }
      const {
        sequence = [],
        simultaneousNotes = [],
        chordProgression = [],
        playerResponse = [],
        difficulty = "",
        timestamp = Date.now()
      } = harmonicData;
      const harmonicAnalysis = {
        timestamp,
        chordRecognition: this.analyzeChordRecognition(simultaneousNotes, playerResponse),
        harmonicProgression: this.analyzeHarmonicProgression(chordProgression),
        dissonanceRecognition: this.analyzeDissonanceRecognition(simultaneousNotes),
        voiceLeading: this.analyzeVoiceLeading(sequence),
        harmonicMemory: this.assessHarmonicMemory(chordProgression, playerResponse),
        tonalityRecognition: this.analyzeTonalityRecognition(sequence),
        difficulty
      };
      this.harmonicAnalysis.push(harmonicAnalysis);
      if (this.debugMode) {
        console.log("🎹 MusicalPatternCollector - Padrão harmônico:", harmonicAnalysis);
      }
      return harmonicAnalysis;
    } catch (error) {
      console.error("Erro no MusicalPatternCollector.analyzeHarmonicPattern:", error);
      return null;
    }
  }
  // Métodos auxiliares para análise melódica
  identifyMelodicPattern(sequence, instruments) {
    if (!Array.isArray(sequence) || sequence.length < 2) return "none";
    const instrumentMap = this.createInstrumentMap(instruments);
    const pitches = sequence.map((note) => this.getInstrumentPitch(note, instrumentMap));
    let ascending = 0, descending = 0, repeated = 0;
    for (let i = 1; i < pitches.length; i++) {
      if (pitches[i] > pitches[i - 1]) ascending++;
      else if (pitches[i] < pitches[i - 1]) descending++;
      else repeated++;
    }
    const total = pitches.length - 1;
    if (ascending / total > 0.7) return "ascending";
    if (descending / total > 0.7) return "descending";
    if (repeated / total > 0.5) return "repeated";
    if (this.isArpeggioPattern(pitches)) return "arpeggio";
    if (this.isScalePattern(pitches)) return "scale";
    if (this.isSequencePattern(pitches)) return "sequence";
    return "mixed";
  }
  analyzeMelodicIntervals(sequence, instruments) {
    if (!Array.isArray(sequence) || sequence.length < 2) {
      return { avgInterval: 0, intervalVariety: 0, consonanceRatio: 0 };
    }
    const instrumentMap = this.createInstrumentMap(instruments);
    const pitches = sequence.map((note) => this.getInstrumentPitch(note, instrumentMap));
    const intervals = [];
    for (let i = 1; i < pitches.length; i++) {
      intervals.push(Math.abs(pitches[i] - pitches[i - 1]));
    }
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const intervalVariety = new Set(intervals).size / intervals.length;
    const consonantIntervals = intervals.filter((interval) => [1, 2, 3, 4, 5, 7, 8, 12].includes(interval % 12));
    const consonanceRatio = consonantIntervals.length / intervals.length;
    return { avgInterval, intervalVariety, consonanceRatio, intervals };
  }
  analyzeMelodicContour(sequence, instruments) {
    if (!Array.isArray(sequence) || sequence.length < 3) {
      return { contourType: "none", directionChanges: 0, contourComplexity: 0 };
    }
    const instrumentMap = this.createInstrumentMap(instruments);
    const pitches = sequence.map((note) => this.getInstrumentPitch(note, instrumentMap));
    let directionChanges = 0;
    let lastDirection = null;
    for (let i = 1; i < pitches.length; i++) {
      const currentDirection = pitches[i] > pitches[i - 1] ? "up" : pitches[i] < pitches[i - 1] ? "down" : "same";
      if (lastDirection && currentDirection !== lastDirection && currentDirection !== "same") {
        directionChanges++;
      }
      if (currentDirection !== "same") lastDirection = currentDirection;
    }
    const contourComplexity = directionChanges / (pitches.length - 1);
    let contourType = "linear";
    if (contourComplexity > 0.5) contourType = "complex";
    else if (contourComplexity > 0.2) contourType = "moderate";
    return { contourType, directionChanges, contourComplexity };
  }
  calculatePatternComplexity(sequence) {
    if (!Array.isArray(sequence)) return 0;
    const uniqueElements = new Set(sequence).size;
    const repetitionRate = 1 - uniqueElements / sequence.length;
    const lengthFactor = Math.min(sequence.length / 8, 1);
    return uniqueElements / 6 * 0.4 + (1 - repetitionRate) * 0.3 + lengthFactor * 0.3;
  }
  assessReproductionAccuracy(sequence, playerResponse) {
    if (!Array.isArray(sequence) || !Array.isArray(playerResponse)) return 0;
    const minLength = Math.min(sequence.length, playerResponse.length);
    let correctCount = 0;
    for (let i = 0; i < minLength; i++) {
      if (sequence[i] === playerResponse[i]) correctCount++;
    }
    const accuracy = correctCount / sequence.length;
    const completeness = minLength / sequence.length;
    return { accuracy, completeness, overallScore: accuracy * completeness };
  }
  analyzeInstrumentTransitions(sequence, instruments) {
    if (!Array.isArray(sequence) || sequence.length < 2) {
      return { transitionComplexity: 0, timbralContrast: 0, transitionPatterns: [] };
    }
    const transitions = [];
    for (let i = 1; i < sequence.length; i++) {
      transitions.push({
        from: sequence[i - 1],
        to: sequence[i],
        contrast: this.calculateTimbralContrast(sequence[i - 1], sequence[i])
      });
    }
    const avgContrast = transitions.reduce((sum, t) => sum + t.contrast, 0) / transitions.length;
    const transitionTypes = new Set(transitions.map((t) => `${t.from}-${t.to}`)).size;
    const transitionComplexity = transitionTypes / transitions.length;
    return {
      transitionComplexity,
      timbralContrast: avgContrast,
      transitionPatterns: this.identifyTransitionPatterns(transitions)
    };
  }
  analyzeTimbralRecognition(instruments, playerResponse) {
    if (!Array.isArray(instruments) || !Array.isArray(playerResponse)) {
      return { accuracy: 0, confusionMatrix: {} };
    }
    const confusionMatrix = {};
    let correctCount = 0;
    for (let i = 0; i < Math.min(instruments.length, playerResponse.length); i++) {
      const expected = instruments[i];
      const actual = playerResponse[i];
      if (!confusionMatrix[expected]) confusionMatrix[expected] = {};
      if (!confusionMatrix[expected][actual]) confusionMatrix[expected][actual] = 0;
      confusionMatrix[expected][actual]++;
      if (expected === actual) correctCount++;
    }
    return {
      accuracy: correctCount / instruments.length,
      confusionMatrix,
      timbralDiscrimination: this.calculateOverallTimbralDiscrimination(confusionMatrix)
    };
  }
  // Métodos auxiliares para análise rítmica
  calculateRhythmicAccuracy(timings, expectedTimings) {
    if (!Array.isArray(timings) || !Array.isArray(expectedTimings)) return 0;
    const tolerance = 100;
    let accurateCount = 0;
    for (let i = 0; i < Math.min(timings.length, expectedTimings.length); i++) {
      if (Math.abs(timings[i] - expectedTimings[i]) <= tolerance) {
        accurateCount++;
      }
    }
    return accurateCount / expectedTimings.length;
  }
  analyzeTempoConsistency(timings) {
    if (!Array.isArray(timings) || timings.length < 3) return { consistency: 0, avgTempo: 0 };
    const intervals = [];
    for (let i = 1; i < timings.length; i++) {
      intervals.push(timings[i] - timings[i - 1]);
    }
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const variance = intervals.reduce((acc, interval) => acc + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
    const stdDev = Math.sqrt(variance);
    const consistency = Math.max(0, 1 - stdDev / avgInterval);
    const avgTempo = 6e4 / avgInterval;
    return { consistency, avgTempo, tempoVariability: stdDev };
  }
  identifyRhythmicPattern(timings) {
    if (!Array.isArray(timings) || timings.length < 3) return "none";
    const intervals = [];
    for (let i = 1; i < timings.length; i++) {
      intervals.push(timings[i] - timings[i - 1]);
    }
    const uniqueIntervals = [...new Set(intervals)];
    if (uniqueIntervals.length === 1) return "steady";
    if (uniqueIntervals.length === 2) {
      const ratio = Math.max(...uniqueIntervals) / Math.min(...uniqueIntervals);
      if (Math.abs(ratio - 2) < 0.1) return "dotted";
      if (Math.abs(ratio - 1.5) < 0.1) return "triplet";
    }
    return "complex";
  }
  // Métodos utilitários
  createInstrumentMap(instruments) {
    const map = {};
    if (Array.isArray(instruments)) {
      instruments.forEach((instrument, index) => {
        map[instrument] = index;
      });
    }
    return map;
  }
  getInstrumentPitch(note, instrumentMap) {
    const pitchMap = {
      "piano": 60,
      "guitar": 64,
      "drum": 36,
      "flute": 72,
      "violin": 76,
      "sax": 58
    };
    return pitchMap[note] || 60;
  }
  isArpeggioPattern(pitches) {
    if (pitches.length < 3) return false;
    const intervals = [];
    for (let i = 1; i < pitches.length; i++) {
      intervals.push(Math.abs(pitches[i] - pitches[i - 1]));
    }
    return intervals.some((interval) => [3, 4, 5, 7].includes(interval % 12));
  }
  isScalePattern(pitches) {
    for (let i = 1; i < pitches.length; i++) {
      const interval = Math.abs(pitches[i] - pitches[i - 1]);
      if (interval > 2) return false;
    }
    return true;
  }
  isSequencePattern(pitches) {
    if (pitches.length < 4) return false;
    const pattern = [pitches[1] - pitches[0], pitches[2] - pitches[1]];
    for (let i = 3; i < pitches.length - 1; i++) {
      const currentPattern = [pitches[i] - pitches[i - 1], pitches[i + 1] - pitches[i]];
      if (currentPattern[0] === pattern[0] && currentPattern[1] === pattern[1]) {
        return true;
      }
    }
    return false;
  }
  calculateTimbralContrast(instrument1, instrument2) {
    const timbralCategories = {
      "piano": "keyboard",
      "guitar": "string",
      "drum": "percussion",
      "flute": "woodwind",
      "violin": "string",
      "sax": "woodwind"
    };
    const cat1 = timbralCategories[instrument1] || "unknown";
    const cat2 = timbralCategories[instrument2] || "unknown";
    return cat1 === cat2 ? 0.2 : 0.8;
  }
  calculateFrequencyRange(frequencies) {
    if (!Array.isArray(frequencies) || frequencies.length === 0) {
      return { min: 0, max: 0, range: 0 };
    }
    const min = Math.min(...frequencies);
    const max = Math.max(...frequencies);
    return { min, max, range: max - min };
  }
  calculateTimbralDiscrimination(instrument) {
    const pref = this.instrumentPreferences[instrument];
    if (!pref) return 0;
    return pref.correctRecognitions / pref.totalExposures;
  }
  calculatePreferenceScore(instrument) {
    const pref = this.instrumentPreferences[instrument];
    if (!pref) return 0;
    const accuracyScore = pref.correctRecognitions / pref.totalExposures;
    const speedScore = Math.max(0, 1 - pref.avgResponseTime / 5e3);
    const confidenceScore = pref.avgConfidence;
    return accuracyScore * 0.5 + speedScore * 0.3 + confidenceScore * 0.2;
  }
  identifyTransitionPatterns(transitions) {
    if (!Array.isArray(transitions)) return [];
    const patterns = [];
    const transitionCounts = {};
    transitions.forEach((t) => {
      const key = `${t.from}-${t.to}`;
      transitionCounts[key] = (transitionCounts[key] || 0) + 1;
    });
    Object.entries(transitionCounts).forEach(([pattern, count]) => {
      if (count > 1) {
        patterns.push({ pattern, frequency: count, type: "recurring" });
      }
    });
    return patterns;
  }
  // Métodos de análise harmônica (simplificados para o contexto de sequência musical)
  analyzeChordRecognition(simultaneousNotes, playerResponse) {
    return { accuracy: 0, chordTypes: [], recognition: "basic" };
  }
  analyzeHarmonicProgression(chordProgression) {
    return { progressionType: "simple", functionality: "tonal" };
  }
  analyzeDissonanceRecognition(simultaneousNotes) {
    return { dissonanceLevel: 0, resolution: "none" };
  }
  analyzeVoiceLeading(sequence) {
    return { smoothness: 0, parallelMotion: 0 };
  }
  assessHarmonicMemory(progression, response) {
    return { accuracy: 0, completeness: 0 };
  }
  analyzeTonalityRecognition(sequence) {
    return { key: "C", mode: "major", certainty: 0 };
  }
  calculateOverallTimbralDiscrimination(confusionMatrix) {
    let totalCorrect = 0;
    let totalAttempts = 0;
    Object.keys(confusionMatrix).forEach((expected) => {
      Object.keys(confusionMatrix[expected]).forEach((actual) => {
        const count = confusionMatrix[expected][actual];
        totalAttempts += count;
        if (expected === actual) totalCorrect += count;
      });
    });
    return totalAttempts > 0 ? totalCorrect / totalAttempts : 0;
  }
  // Métodos de relatório
  getPatternReport() {
    return {
      totalPatterns: this.melodicPatterns.length,
      avgPatternComplexity: this.melodicPatterns.reduce((sum, p) => sum + p.patternComplexity, 0) / this.melodicPatterns.length || 0,
      instrumentPreferences: Object.keys(this.instrumentPreferences).map((instrument) => ({
        instrument,
        accuracy: this.instrumentPreferences[instrument].correctRecognitions / this.instrumentPreferences[instrument].totalExposures,
        exposures: this.instrumentPreferences[instrument].totalExposures
      })),
      rhythmicAccuracy: this.rhythmicPatterns.reduce((sum, r) => sum + r.rhythmicAccuracy, 0) / this.rhythmicPatterns.length || 0,
      melodicContours: this.melodicPatterns.map((p) => p.contourRecognition.contourType)
    };
  }
  clearData() {
    this.patternData = [];
    this.melodicPatterns = [];
    this.rhythmicPatterns = [];
    this.instrumentPreferences = {};
    this.harmonicAnalysis = [];
    if (this.debugMode) {
      console.log("🎵 MusicalPatternCollector - Dados limpos");
    }
  }
}
class SequenceExecutionCollector {
  constructor() {
    this.executionData = [];
    this.timingAnalysis = [];
    this.motorControlData = [];
    this.strategyData = [];
    this.errorPatterns = [];
    this.debugMode = true;
  }
  /**
   * Coleta dados de execução de sequência
   */
  collectExecution(executionData) {
    try {
      if (!executionData || typeof executionData !== "object") {
        console.warn("SequenceExecutionCollector: Dados de execução inválidos");
        return null;
      }
      const {
        sequence = [],
        playerResponse = [],
        executionTimes = [],
        expectedTimes = [],
        pressureLevels = [],
        coordinationScores = [],
        isCorrect = false,
        totalExecutionTime = 0,
        difficulty = "",
        timestamp = Date.now()
      } = executionData;
      const execution = {
        timestamp,
        sequenceLength: sequence.length,
        executionAccuracy: this.calculateExecutionAccuracy(sequence, playerResponse),
        timingPrecision: this.calculateTimingPrecision(executionTimes, expectedTimes),
        executionFlow: this.analyzeExecutionFlow(executionTimes),
        motorControl: this.assessMotorControl(pressureLevels, coordinationScores),
        executionStrategy: this.identifyExecutionStrategy(executionTimes, sequence),
        errorTypes: this.classifyErrors(sequence, playerResponse),
        consistency: this.measureConsistency(executionTimes),
        efficiency: this.calculateEfficiency(totalExecutionTime, sequence.length),
        isCorrect,
        difficulty,
        hesitations: this.detectHesitations(executionTimes),
        anticipations: this.detectAnticipations(executionTimes, expectedTimes)
      };
      this.executionData.push(execution);
      if (this.debugMode) {
        console.log("⚡ SequenceExecutionCollector - Execução coletada:", execution);
      }
      return execution;
    } catch (error) {
      console.error("Erro no SequenceExecutionCollector.collectExecution:", error);
      return null;
    }
  }
  /**
   * Analisa padrões de timing e ritmo na execução
   */
  analyzeExecutionTiming(timingData) {
    try {
      if (!timingData || typeof timingData !== "object") {
        console.warn("SequenceExecutionCollector: Dados de timing inválidos");
        return null;
      }
      const {
        interClickIntervals = [],
        expectedIntervals = [],
        totalDuration = 0,
        tempo = 120,
        sequenceLength = 0,
        timestamp = Date.now()
      } = timingData;
      const timingAnalysis = {
        timestamp,
        rhythmicAccuracy: this.calculateRhythmicAccuracy(interClickIntervals, expectedIntervals),
        tempoStability: this.analyzeTempoStability(interClickIntervals),
        timingVariability: this.calculateTimingVariability(interClickIntervals),
        accelerationPattern: this.detectAccelerationPattern(interClickIntervals),
        phrasing: this.analyzePhrasing(interClickIntervals),
        microTiming: this.analyzeMicroTiming(interClickIntervals, expectedIntervals),
        globalTempo: this.calculateGlobalTempo(totalDuration, sequenceLength),
        rubato: this.detectRubato(interClickIntervals, expectedIntervals),
        metricalStress: this.analyzeMetricalStress(interClickIntervals)
      };
      this.timingAnalysis.push(timingAnalysis);
      if (this.debugMode) {
        console.log("🕐 SequenceExecutionCollector - Timing analisado:", timingAnalysis);
      }
      return timingAnalysis;
    } catch (error) {
      console.error("Erro no SequenceExecutionCollector.analyzeExecutionTiming:", error);
      return null;
    }
  }
  /**
   * Coleta dados sobre controle motor e coordenação
   */
  collectMotorControl(motorData) {
    try {
      if (!motorData || typeof motorData !== "object") {
        console.warn("SequenceExecutionCollector: Dados motores inválidos");
        return null;
      }
      const {
        clickPrecision = [],
        pressureDynamics = [],
        movementSmoothness = 0,
        coordinationIndex = 0,
        fatigue = 0,
        handedness = "right",
        timestamp = Date.now()
      } = motorData;
      const motorControl = {
        timestamp,
        precisionScore: this.calculatePrecisionScore(clickPrecision),
        dynamicRange: this.analyzeDynamicRange(pressureDynamics),
        motorConsistency: this.assessMotorConsistency(clickPrecision),
        smoothnessIndex: movementSmoothness,
        coordinationLevel: coordinationIndex,
        fatigueLevel: fatigue,
        motorAdaptation: this.analyzeMotorAdaptation(this.motorControlData),
        reactionTime: this.calculateAverageReactionTime(clickPrecision),
        motorMemory: this.assessMotorMemory(clickPrecision),
        handedness
      };
      this.motorControlData.push(motorControl);
      if (this.debugMode) {
        console.log("🤲 SequenceExecutionCollector - Controle motor:", motorControl);
      }
      return motorControl;
    } catch (error) {
      console.error("Erro no SequenceExecutionCollector.collectMotorControl:", error);
      return null;
    }
  }
  /**
   * Identifica estratégias de execução utilizadas
   */
  identifyExecutionStrategies(strategyData) {
    try {
      if (!strategyData || typeof strategyData !== "object") {
        console.warn("SequenceExecutionCollector: Dados de estratégia inválidos");
        return null;
      }
      const {
        sequence = [],
        playerResponse = [],
        executionTimes = [],
        repeatRequests = 0,
        pauseLocations = [],
        chunkingPatterns = [],
        difficulty = "",
        timestamp = Date.now()
      } = strategyData;
      const strategy = {
        timestamp,
        primaryStrategy: this.identifyPrimaryStrategy(executionTimes, chunkingPatterns),
        chunkingStrategy: this.analyzeChunkingStrategy(chunkingPatterns),
        rehearsalStrategy: this.analyzeRehearsalStrategy(repeatRequests, pauseLocations),
        adaptiveStrategy: this.assessAdaptiveStrategy(this.strategyData),
        metacognition: this.assessMetacognition(pauseLocations, repeatRequests),
        strategicFlexibility: this.measureStrategicFlexibility(this.strategyData),
        planningEffectiveness: this.assessPlanningEffectiveness(sequence, playerResponse),
        errorRecovery: this.analyzeErrorRecovery(sequence, playerResponse, pauseLocations),
        difficultyAdaptation: this.analyzeDifficultyAdaptation(difficulty),
        confidence: this.inferConfidenceLevel(executionTimes, pauseLocations)
      };
      this.strategyData.push(strategy);
      if (this.debugMode) {
        console.log("🎯 SequenceExecutionCollector - Estratégia identificada:", strategy);
      }
      return strategy;
    } catch (error) {
      console.error("Erro no SequenceExecutionCollector.identifyExecutionStrategies:", error);
      return null;
    }
  }
  /**
   * Analisa padrões de erro na execução
   */
  analyzeErrorPatterns(errorData) {
    try {
      if (!errorData || typeof errorData !== "object") {
        console.warn("SequenceExecutionCollector: Dados de erro inválidos");
        return null;
      }
      const {
        sequence = [],
        playerResponse = [],
        errorLocations = [],
        errorTypes = [],
        recoveryTimes = [],
        repetitionErrors = [],
        timestamp = Date.now()
      } = errorData;
      const errorAnalysis = {
        timestamp,
        errorRate: this.calculateErrorRate(sequence, playerResponse),
        errorDistribution: this.analyzeErrorDistribution(errorLocations),
        errorTypes: this.categorizeErrorTypes(errorTypes),
        persistentErrors: this.identifyPersistentErrors(repetitionErrors),
        errorRecoverySpeed: this.calculateErrorRecoverySpeed(recoveryTimes),
        errorClusters: this.identifyErrorClusters(errorLocations),
        sequentialErrors: this.analyzeSequentialErrors(sequence, playerResponse),
        errorLearning: this.assessErrorLearning(this.errorPatterns),
        errorContext: this.analyzeErrorContext(errorLocations, sequence),
        errorSeverity: this.assessErrorSeverity(errorTypes)
      };
      this.errorPatterns.push(errorAnalysis);
      if (this.debugMode) {
        console.log("❌ SequenceExecutionCollector - Padrões de erro:", errorAnalysis);
      }
      return errorAnalysis;
    } catch (error) {
      console.error("Erro no SequenceExecutionCollector.analyzeErrorPatterns:", error);
      return null;
    }
  }
  // Métodos auxiliares para cálculos de execução
  calculateExecutionAccuracy(sequence, playerResponse) {
    if (!Array.isArray(sequence) || !Array.isArray(playerResponse)) return 0;
    let correctCount = 0;
    const minLength = Math.min(sequence.length, playerResponse.length);
    for (let i = 0; i < minLength; i++) {
      if (sequence[i] === playerResponse[i]) correctCount++;
    }
    const accuracy = correctCount / sequence.length;
    const completeness = minLength / sequence.length;
    return { accuracy, completeness, overallScore: accuracy * completeness };
  }
  calculateTimingPrecision(executionTimes, expectedTimes) {
    if (!Array.isArray(executionTimes) || !Array.isArray(expectedTimes)) return 0;
    const deviations = [];
    const minLength = Math.min(executionTimes.length, expectedTimes.length);
    for (let i = 0; i < minLength; i++) {
      deviations.push(Math.abs(executionTimes[i] - expectedTimes[i]));
    }
    const avgDeviation = deviations.reduce((a, b) => a + b, 0) / deviations.length;
    const precision = Math.max(0, 1 - avgDeviation / 500);
    return { precision, avgDeviation, deviations };
  }
  analyzeExecutionFlow(executionTimes) {
    if (!Array.isArray(executionTimes) || executionTimes.length < 3) {
      return { flowScore: 0, interruptions: 0, smoothness: 0 };
    }
    const intervals = [];
    for (let i = 1; i < executionTimes.length; i++) {
      intervals.push(executionTimes[i] - executionTimes[i - 1]);
    }
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const variance = intervals.reduce((acc, interval) => acc + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
    const smoothness = Math.max(0, 1 - Math.sqrt(variance) / avgInterval);
    const threshold = avgInterval * 2;
    const interruptions = intervals.filter((interval) => interval > threshold).length;
    const flowScore = smoothness * (1 - interruptions / intervals.length);
    return { flowScore, interruptions, smoothness, avgInterval };
  }
  assessMotorControl(pressureLevels, coordinationScores) {
    const avgPressure = Array.isArray(pressureLevels) ? pressureLevels.reduce((a, b) => a + b, 0) / pressureLevels.length : 0;
    const pressureVariability = Array.isArray(pressureLevels) && pressureLevels.length > 1 ? this.calculateVariability(pressureLevels) : 0;
    const avgCoordination = Array.isArray(coordinationScores) ? coordinationScores.reduce((a, b) => a + b, 0) / coordinationScores.length : 0;
    return {
      pressureControl: Math.max(0, 1 - pressureVariability),
      averagePressure: avgPressure,
      coordination: avgCoordination,
      motorStability: 1 - pressureVariability,
      fineMotorControl: this.assessFineMotorControl(pressureLevels)
    };
  }
  identifyExecutionStrategy(executionTimes, sequence) {
    if (!Array.isArray(executionTimes) || executionTimes.length < 3) return "insufficient_data";
    const intervals = [];
    for (let i = 1; i < executionTimes.length; i++) {
      intervals.push(executionTimes[i] - executionTimes[i - 1]);
    }
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const variance = this.calculateVariability(intervals);
    if (variance < avgInterval * 0.2) return "steady_rhythm";
    if (this.detectChunking(intervals)) return "chunking";
    if (this.detectAcceleration(intervals)) return "acceleration";
    if (this.detectHesitation(intervals, avgInterval)) return "hesitation";
    return "variable";
  }
  classifyErrors(sequence, playerResponse) {
    if (!Array.isArray(sequence) || !Array.isArray(playerResponse)) return [];
    const errors = [];
    const minLength = Math.min(sequence.length, playerResponse.length);
    for (let i = 0; i < minLength; i++) {
      if (sequence[i] !== playerResponse[i]) {
        errors.push({
          position: i,
          expected: sequence[i],
          actual: playerResponse[i],
          type: this.categorizeError(sequence[i], playerResponse[i], i, sequence)
        });
      }
    }
    if (playerResponse.length < sequence.length) {
      for (let i = playerResponse.length; i < sequence.length; i++) {
        errors.push({
          position: i,
          expected: sequence[i],
          actual: null,
          type: "omission"
        });
      }
    }
    if (playerResponse.length > sequence.length) {
      for (let i = sequence.length; i < playerResponse.length; i++) {
        errors.push({
          position: i,
          expected: null,
          actual: playerResponse[i],
          type: "commission"
        });
      }
    }
    return errors;
  }
  measureConsistency(executionTimes) {
    if (!Array.isArray(executionTimes) || executionTimes.length < 2) return 0;
    const intervals = [];
    for (let i = 1; i < executionTimes.length; i++) {
      intervals.push(executionTimes[i] - executionTimes[i - 1]);
    }
    const variance = this.calculateVariability(intervals);
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    return Math.max(0, 1 - variance / avgInterval);
  }
  calculateEfficiency(totalTime, sequenceLength) {
    if (totalTime === 0 || sequenceLength === 0) return 0;
    const expectedTimePerItem = 800;
    const expectedTotalTime = sequenceLength * expectedTimePerItem;
    return Math.max(0, expectedTotalTime / totalTime);
  }
  detectHesitations(executionTimes) {
    if (!Array.isArray(executionTimes) || executionTimes.length < 3) return [];
    const intervals = [];
    for (let i = 1; i < executionTimes.length; i++) {
      intervals.push(executionTimes[i] - executionTimes[i - 1]);
    }
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const threshold = avgInterval * 1.5;
    const hesitations = [];
    intervals.forEach((interval, index) => {
      if (interval > threshold) {
        hesitations.push({
          position: index,
          duration: interval,
          severity: interval / avgInterval
        });
      }
    });
    return hesitations;
  }
  detectAnticipations(executionTimes, expectedTimes) {
    if (!Array.isArray(executionTimes) || !Array.isArray(expectedTimes)) return [];
    const anticipations = [];
    const minLength = Math.min(executionTimes.length, expectedTimes.length);
    for (let i = 0; i < minLength; i++) {
      const anticipation = expectedTimes[i] - executionTimes[i];
      if (anticipation > 100) {
        anticipations.push({
          position: i,
          anticipationTime: anticipation,
          severity: anticipation / 200
          // Normalizado para 200ms
        });
      }
    }
    return anticipations;
  }
  // Métodos auxiliares para análise de timing
  calculateRhythmicAccuracy(intervals, expectedIntervals) {
    if (!Array.isArray(intervals) || !Array.isArray(expectedIntervals)) return 0;
    const deviations = [];
    const minLength = Math.min(intervals.length, expectedIntervals.length);
    for (let i = 0; i < minLength; i++) {
      deviations.push(Math.abs(intervals[i] - expectedIntervals[i]));
    }
    const avgDeviation = deviations.reduce((a, b) => a + b, 0) / deviations.length;
    const tolerance = 100;
    return Math.max(0, 1 - avgDeviation / tolerance);
  }
  analyzeTempoStability(intervals) {
    if (!Array.isArray(intervals) || intervals.length < 3) return 0;
    const variance = this.calculateVariability(intervals);
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    return Math.max(0, 1 - variance / avgInterval);
  }
  calculateTimingVariability(intervals) {
    if (!Array.isArray(intervals) || intervals.length === 0) return 0;
    return this.calculateVariability(intervals);
  }
  detectAccelerationPattern(intervals) {
    if (!Array.isArray(intervals) || intervals.length < 3) return "none";
    let accelerating = 0;
    let decelerating = 0;
    for (let i = 1; i < intervals.length; i++) {
      if (intervals[i] < intervals[i - 1]) accelerating++;
      else if (intervals[i] > intervals[i - 1]) decelerating++;
    }
    const total = intervals.length - 1;
    if (accelerating / total > 0.6) return "accelerating";
    if (decelerating / total > 0.6) return "decelerating";
    return "stable";
  }
  analyzePhrasing(intervals) {
    if (!Array.isArray(intervals) || intervals.length < 4) return { phrases: 0, avgPhraseLength: 0 };
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const threshold = avgInterval * 1.3;
    let phrases = 1;
    let currentPhraseLength = 1;
    const phraseLengths = [];
    for (let i = 0; i < intervals.length; i++) {
      if (intervals[i] > threshold) {
        phraseLengths.push(currentPhraseLength);
        phrases++;
        currentPhraseLength = 1;
      } else {
        currentPhraseLength++;
      }
    }
    phraseLengths.push(currentPhraseLength);
    const avgPhraseLength = phraseLengths.reduce((a, b) => a + b, 0) / phraseLengths.length;
    return { phrases, avgPhraseLength, phraseLengths };
  }
  // Métodos auxiliares para análise de estratégias
  identifyPrimaryStrategy(executionTimes, chunkingPatterns) {
    if (!Array.isArray(executionTimes)) return "unknown";
    if (Array.isArray(chunkingPatterns) && chunkingPatterns.length > 0) {
      return "chunking";
    }
    const intervals = [];
    for (let i = 1; i < executionTimes.length; i++) {
      intervals.push(executionTimes[i] - executionTimes[i - 1]);
    }
    const variance = this.calculateVariability(intervals);
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    if (variance < avgInterval * 0.2) return "steady_execution";
    if (this.detectAcceleration(intervals)) return "progressive_acceleration";
    return "adaptive";
  }
  analyzeChunkingStrategy(chunkingPatterns) {
    if (!Array.isArray(chunkingPatterns) || chunkingPatterns.length === 0) {
      return { chunkingUsed: false, avgChunkSize: 0, chunkingEfficiency: 0 };
    }
    const avgChunkSize = chunkingPatterns.reduce((a, b) => a + b, 0) / chunkingPatterns.length;
    const chunkVariability = this.calculateVariability(chunkingPatterns);
    const efficiency = 1 - chunkVariability;
    return {
      chunkingUsed: true,
      avgChunkSize,
      chunkingEfficiency: efficiency,
      chunkConsistency: 1 - chunkVariability
    };
  }
  analyzeRehearsalStrategy(repeatRequests, pauseLocations) {
    return {
      rehearsalFrequency: repeatRequests,
      strategicPauses: Array.isArray(pauseLocations) ? pauseLocations.length : 0,
      rehearsalEfficiency: this.calculateRehearsalEfficiency(repeatRequests),
      pauseStrategy: this.analyzePauseStrategy(pauseLocations)
    };
  }
  // Métodos utilitários
  calculateVariability(values) {
    if (!Array.isArray(values) || values.length === 0) return 0;
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }
  detectChunking(intervals) {
    if (!Array.isArray(intervals) || intervals.length < 4) return false;
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const longPauses = intervals.filter((interval) => interval > avgInterval * 1.5);
    return longPauses.length >= 2 && longPauses.length < intervals.length * 0.5;
  }
  detectAcceleration(intervals) {
    if (!Array.isArray(intervals) || intervals.length < 3) return false;
    let accelerating = 0;
    for (let i = 1; i < intervals.length; i++) {
      if (intervals[i] < intervals[i - 1]) accelerating++;
    }
    return accelerating / (intervals.length - 1) > 0.6;
  }
  detectHesitation(intervals, avgInterval) {
    return intervals.some((interval) => interval > avgInterval * 2);
  }
  categorizeError(expected, actual, position, sequence) {
    if (actual === null) return "omission";
    if (expected === null) return "commission";
    if (sequence.includes(actual)) {
      const correctPosition = sequence.indexOf(actual);
      if (Math.abs(correctPosition - position) === 1) return "transposition";
      return "order_error";
    }
    return "substitution";
  }
  assessFineMotorControl(pressureLevels) {
    if (!Array.isArray(pressureLevels) || pressureLevels.length === 0) return 0;
    const stability = 1 - this.calculateVariability(pressureLevels);
    const control = Math.min(...pressureLevels) / Math.max(...pressureLevels);
    return (stability + control) / 2;
  }
  calculateRehearsalEfficiency(repeatRequests) {
    if (repeatRequests === 0) return 1;
    if (repeatRequests <= 2) return 0.8;
    if (repeatRequests <= 4) return 0.6;
    return 0.4;
  }
  analyzePauseStrategy(pauseLocations) {
    if (!Array.isArray(pauseLocations) || pauseLocations.length === 0) {
      return { strategic: false, pattern: "none" };
    }
    const intervals = [];
    for (let i = 1; i < pauseLocations.length; i++) {
      intervals.push(pauseLocations[i] - pauseLocations[i - 1]);
    }
    const variance = this.calculateVariability(intervals);
    const isStrategic = variance < 2;
    return {
      strategic: isStrategic,
      pattern: isStrategic ? "regular" : "irregular",
      pauseFrequency: pauseLocations.length
    };
  }
  // Métodos de análise avançada (implementações simplificadas)
  analyzeMicroTiming(intervals, expectedIntervals) {
    return { microVariations: [], avgMicroDeviation: 0 };
  }
  calculateGlobalTempo(totalDuration, sequenceLength) {
    if (totalDuration === 0 || sequenceLength === 0) return 0;
    return sequenceLength * 6e4 / totalDuration;
  }
  detectRubato(intervals, expectedIntervals) {
    return { rubatoPresent: false, expressivity: 0 };
  }
  analyzeMetricalStress(intervals) {
    return { stressPattern: "none", accentuation: [] };
  }
  assessAdaptiveStrategy(strategyHistory) {
    return strategyHistory.length > 1 ? 0.5 : 0;
  }
  assessMetacognition(pauseLocations, repeatRequests) {
    const metacognitionScore = (Array.isArray(pauseLocations) ? pauseLocations.length : 0) * 0.3 + repeatRequests * 0.2;
    return Math.min(metacognitionScore, 1);
  }
  measureStrategicFlexibility(strategyHistory) {
    if (!Array.isArray(strategyHistory) || strategyHistory.length < 2) return 0;
    const strategies = strategyHistory.map((s) => s.primaryStrategy);
    const uniqueStrategies = new Set(strategies).size;
    return uniqueStrategies / strategies.length;
  }
  assessPlanningEffectiveness(sequence, playerResponse) {
    const accuracy = this.calculateExecutionAccuracy(sequence, playerResponse);
    return accuracy.overallScore;
  }
  analyzeErrorRecovery(sequence, playerResponse, pauseLocations) {
    const errors = this.classifyErrors(sequence, playerResponse);
    const pausesAfterErrors = Array.isArray(pauseLocations) ? pauseLocations.length : 0;
    return {
      errorCount: errors.length,
      recoveryAttempts: pausesAfterErrors,
      recoveryEffectiveness: pausesAfterErrors > 0 ? 0.7 : 0.3
    };
  }
  analyzeDifficultyAdaptation(difficulty) {
    const difficultyMap = { easy: 0.3, medium: 0.6, hard: 0.9 };
    return difficultyMap[difficulty] || 0.5;
  }
  inferConfidenceLevel(executionTimes, pauseLocations) {
    const hesitations = this.detectHesitations(executionTimes);
    const pauses = Array.isArray(pauseLocations) ? pauseLocations.length : 0;
    const confidenceScore = Math.max(0, 1 - (hesitations.length * 0.2 + pauses * 0.1));
    return confidenceScore;
  }
  // Métodos de análise de erro avançados
  calculateErrorRate(sequence, playerResponse) {
    const errors = this.classifyErrors(sequence, playerResponse);
    return errors.length / sequence.length;
  }
  analyzeErrorDistribution(errorLocations) {
    if (!Array.isArray(errorLocations) || errorLocations.length === 0) {
      return { distribution: "none", clustering: 0 };
    }
    const positions = errorLocations.map((e) => e.position || e);
    const maxPosition = Math.max(...positions);
    const beginning = positions.filter((p) => p < maxPosition * 0.33).length;
    const middle = positions.filter((p) => p >= maxPosition * 0.33 && p < maxPosition * 0.67).length;
    const end = positions.filter((p) => p >= maxPosition * 0.67).length;
    return {
      distribution: { beginning, middle, end },
      clustering: this.calculateErrorClustering(positions)
    };
  }
  categorizeErrorTypes(errorTypes) {
    if (!Array.isArray(errorTypes)) return {};
    const counts = {};
    errorTypes.forEach((type) => {
      counts[type] = (counts[type] || 0) + 1;
    });
    return counts;
  }
  identifyPersistentErrors(repetitionErrors) {
    if (!Array.isArray(repetitionErrors)) return [];
    const errorCounts = {};
    repetitionErrors.forEach((error) => {
      const key = `${error.position}-${error.type}`;
      errorCounts[key] = (errorCounts[key] || 0) + 1;
    });
    return Object.entries(errorCounts).filter(([, count]) => count > 1).map(([key, count]) => ({ pattern: key, frequency: count }));
  }
  calculateErrorRecoverySpeed(recoveryTimes) {
    if (!Array.isArray(recoveryTimes) || recoveryTimes.length === 0) return 0;
    const avgRecoveryTime = recoveryTimes.reduce((a, b) => a + b, 0) / recoveryTimes.length;
    return Math.max(0, 1 - avgRecoveryTime / 2e3);
  }
  identifyErrorClusters(errorLocations) {
    if (!Array.isArray(errorLocations) || errorLocations.length < 2) return [];
    const positions = errorLocations.map((e) => e.position || e).sort((a, b) => a - b);
    const clusters = [];
    let currentCluster = [positions[0]];
    for (let i = 1; i < positions.length; i++) {
      if (positions[i] - positions[i - 1] <= 2) {
        currentCluster.push(positions[i]);
      } else {
        if (currentCluster.length > 1) {
          clusters.push(currentCluster);
        }
        currentCluster = [positions[i]];
      }
    }
    if (currentCluster.length > 1) {
      clusters.push(currentCluster);
    }
    return clusters;
  }
  analyzeSequentialErrors(sequence, playerResponse) {
    const errors = this.classifyErrors(sequence, playerResponse);
    const sequentialErrors = [];
    for (let i = 1; i < errors.length; i++) {
      if (errors[i].position === errors[i - 1].position + 1) {
        sequentialErrors.push({
          start: errors[i - 1].position,
          end: errors[i].position,
          type: "sequential"
        });
      }
    }
    return sequentialErrors;
  }
  assessErrorLearning(errorHistory) {
    if (!Array.isArray(errorHistory) || errorHistory.length < 2) return 0;
    const recentErrors = errorHistory.slice(-3);
    const olderErrors = errorHistory.slice(0, -3);
    const recentRate = recentErrors.reduce((sum, e) => sum + e.errorRate, 0) / recentErrors.length;
    const olderRate = olderErrors.length > 0 ? olderErrors.reduce((sum, e) => sum + e.errorRate, 0) / olderErrors.length : recentRate;
    return Math.max(0, (olderRate - recentRate) / olderRate);
  }
  analyzeErrorContext(errorLocations, sequence) {
    return {
      contextualFactors: ["sequence_complexity", "position_effect"],
      contextualAnalysis: "simplified"
    };
  }
  assessErrorSeverity(errorTypes) {
    if (!Array.isArray(errorTypes)) return 0;
    const severityMap = {
      "omission": 0.8,
      "commission": 0.6,
      "substitution": 0.5,
      "transposition": 0.4,
      "order_error": 0.7
    };
    const avgSeverity = errorTypes.reduce((sum, type) => sum + (severityMap[type] || 0.5), 0) / errorTypes.length;
    return avgSeverity;
  }
  calculateErrorClustering(positions) {
    if (positions.length < 2) return 0;
    let clusterScore = 0;
    for (let i = 1; i < positions.length; i++) {
      if (positions[i] - positions[i - 1] <= 2) {
        clusterScore++;
      }
    }
    return clusterScore / (positions.length - 1);
  }
  // Métodos auxiliares adicionais
  calculatePrecisionScore(clickPrecision) {
    if (!Array.isArray(clickPrecision) || clickPrecision.length === 0) return 0;
    return clickPrecision.reduce((a, b) => a + b, 0) / clickPrecision.length;
  }
  analyzeDynamicRange(pressureDynamics) {
    if (!Array.isArray(pressureDynamics) || pressureDynamics.length === 0) return 0;
    const min = Math.min(...pressureDynamics);
    const max = Math.max(...pressureDynamics);
    return { range: max - min, min, max, avgPressure: pressureDynamics.reduce((a, b) => a + b, 0) / pressureDynamics.length };
  }
  assessMotorConsistency(clickPrecision) {
    if (!Array.isArray(clickPrecision) || clickPrecision.length === 0) return 0;
    return 1 - this.calculateVariability(clickPrecision);
  }
  analyzeMotorAdaptation(motorHistory) {
    if (!Array.isArray(motorHistory) || motorHistory.length < 2) return 0;
    const recent = motorHistory.slice(-3);
    const older = motorHistory.slice(0, -3);
    const recentPrecision = recent.reduce((sum, m) => sum + m.precisionScore, 0) / recent.length;
    const olderPrecision = older.length > 0 ? older.reduce((sum, m) => sum + m.precisionScore, 0) / older.length : recentPrecision;
    return Math.max(0, (recentPrecision - olderPrecision) / olderPrecision);
  }
  calculateAverageReactionTime(clickPrecision) {
    if (!Array.isArray(clickPrecision) || clickPrecision.length === 0) return 0;
    return 500 - clickPrecision.reduce((a, b) => a + b, 0) / clickPrecision.length * 200;
  }
  assessMotorMemory(clickPrecision) {
    if (!Array.isArray(clickPrecision) || clickPrecision.length < 2) return 0;
    const firstHalf = clickPrecision.slice(0, Math.floor(clickPrecision.length / 2));
    const secondHalf = clickPrecision.slice(Math.floor(clickPrecision.length / 2));
    const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;
    return Math.max(0, (secondAvg - firstAvg) / firstAvg);
  }
  // Métodos de relatório
  getExecutionReport() {
    return {
      totalExecutions: this.executionData.length,
      avgExecutionAccuracy: this.executionData.reduce((sum, e) => sum + e.executionAccuracy.overallScore, 0) / this.executionData.length || 0,
      avgTimingPrecision: this.executionData.reduce((sum, e) => sum + e.timingPrecision.precision, 0) / this.executionData.length || 0,
      primaryStrategy: this.identifyMostCommonStrategy(),
      motorControlTrend: this.analyzeMotorControlTrend(),
      errorPatternSummary: this.summarizeErrorPatterns()
    };
  }
  identifyMostCommonStrategy() {
    if (this.strategyData.length === 0) return "unknown";
    const strategies = this.strategyData.map((s) => s.primaryStrategy);
    const counts = {};
    strategies.forEach((strategy) => {
      counts[strategy] = (counts[strategy] || 0) + 1;
    });
    return Object.entries(counts).sort((a, b) => b[1] - a[1])[0][0];
  }
  analyzeMotorControlTrend() {
    if (this.motorControlData.length < 2) return "insufficient_data";
    const recent = this.motorControlData.slice(-3);
    const older = this.motorControlData.slice(0, 3);
    const recentAvg = recent.reduce((sum, m) => sum + m.precisionScore, 0) / recent.length;
    const olderAvg = older.reduce((sum, m) => sum + m.precisionScore, 0) / older.length;
    if (recentAvg > olderAvg * 1.1) return "improving";
    if (recentAvg < olderAvg * 0.9) return "declining";
    return "stable";
  }
  summarizeErrorPatterns() {
    if (this.errorPatterns.length === 0) return { totalErrors: 0, commonTypes: [] };
    const totalErrors = this.errorPatterns.reduce((sum, e) => sum + e.errorRate, 0);
    const allErrorTypes = this.errorPatterns.flatMap((e) => Object.keys(e.errorTypes));
    const errorTypeCounts = {};
    allErrorTypes.forEach((type) => {
      errorTypeCounts[type] = (errorTypeCounts[type] || 0) + 1;
    });
    const commonTypes = Object.entries(errorTypeCounts).sort((a, b) => b[1] - a[1]).slice(0, 3).map(([type]) => type);
    return { totalErrors, commonTypes };
  }
  clearData() {
    this.executionData = [];
    this.timingAnalysis = [];
    this.motorControlData = [];
    this.strategyData = [];
    this.errorPatterns = [];
    if (this.debugMode) {
      console.log("⚡ SequenceExecutionCollector - Dados limpos");
    }
  }
}
class MusicalLearningCollector {
  constructor() {
    this.learningData = [];
    this.progressionData = [];
    this.retentionData = [];
    this.transferData = [];
    this.adaptationData = [];
    this.masteryData = [];
    this.debugMode = true;
  }
  /**
   * Coleta dados sobre progressão no aprendizado
   */
  collectLearningProgression(progressionData) {
    try {
      if (!progressionData || typeof progressionData !== "object") {
        console.warn("MusicalLearningCollector: Dados de progressão inválidos");
        return null;
      }
      const {
        sessionNumber = 0,
        difficulty = "",
        currentLevel = 1,
        previousLevel = 1,
        accuracy = 0,
        speed = 0,
        consistency = 0,
        sequencesCompleted = 0,
        errorsCommitted = 0,
        improvementRate = 0,
        challengesSolved = 0,
        timestamp = Date.now()
      } = progressionData;
      const progression = {
        timestamp,
        sessionNumber,
        difficulty,
        levelProgression: this.analyzeLevelProgression(currentLevel, previousLevel),
        performanceMetrics: {
          accuracy,
          speed,
          consistency,
          overallPerformance: this.calculateOverallPerformance(accuracy, speed, consistency)
        },
        volumeMetrics: {
          sequencesCompleted,
          errorsCommitted,
          errorRate: sequencesCompleted > 0 ? errorsCommitted / sequencesCompleted : 0
        },
        learningCurve: this.analyzeLearningCurve(sessionNumber, accuracy),
        masteryIndicators: this.assessMasteryIndicators(accuracy, consistency, currentLevel),
        adaptiveBehavior: this.analyzeAdaptiveBehavior(difficulty, accuracy),
        engagementLevel: this.calculateEngagementLevel(sequencesCompleted, challengesSolved),
        improvementRate,
        plateauDetection: this.detectLearningPlateau(this.progressionData, accuracy)
      };
      this.progressionData.push(progression);
      if (this.debugMode) {
        console.log("📈 MusicalLearningCollector - Progressão coletada:", progression);
      }
      return progression;
    } catch (error) {
      console.error("Erro no MusicalLearningCollector.collectLearningProgression:", error);
      return null;
    }
  }
  /**
   * Analisa padrões de retenção do aprendizado
   */
  analyzeRetentionPatterns(retentionData) {
    try {
      if (!retentionData || typeof retentionData !== "object") {
        console.warn("MusicalLearningCollector: Dados de retenção inválidos");
        return null;
      }
      const {
        timeSinceLastSession = 0,
        initialPerformance = 0,
        returnPerformance = 0,
        previousMastery = 0,
        currentMastery = 0,
        forgettingCurve = [],
        practiceFrequency = 0,
        difficulty = "",
        timestamp = Date.now()
      } = retentionData;
      const retention = {
        timestamp,
        timeSinceLastSession,
        retentionRate: this.calculateRetentionRate(initialPerformance, returnPerformance),
        forgettingCurve: this.analyzeForgettingCurve(forgettingCurve, timeSinceLastSession),
        masteryRetention: this.analyzeMasteryRetention(previousMastery, currentMastery),
        consolidationLevel: this.assessConsolidationLevel(timeSinceLastSession, returnPerformance),
        practiceEffect: this.analyzePracticeEffect(practiceFrequency, retentionRate),
        memoryStrength: this.calculateMemoryStrength(initialPerformance, returnPerformance, timeSinceLastSession),
        relearningSpeed: this.assessRelearningSpeed(initialPerformance, returnPerformance),
        longTermRetention: this.assessLongTermRetention(timeSinceLastSession, returnPerformance),
        difficulty,
        retentionStrategies: this.identifyRetentionStrategies(practiceFrequency, returnPerformance)
      };
      this.retentionData.push(retention);
      if (this.debugMode) {
        console.log("🧠 MusicalLearningCollector - Retenção analisada:", retention);
      }
      return retention;
    } catch (error) {
      console.error("Erro no MusicalLearningCollector.analyzeRetentionPatterns:", error);
      return null;
    }
  }
  /**
   * Coleta dados sobre transferência de aprendizado
   */
  collectTransferLearning(transferData) {
    try {
      if (!transferData || typeof transferData !== "object") {
        console.warn("MusicalLearningCollector: Dados de transferência inválidos");
        return null;
      }
      const {
        sourceSkill = "",
        targetSkill = "",
        sourcePerformance = 0,
        targetPerformance = 0,
        transferDistance = 0,
        // quão diferentes são as habilidades
        trainingTime = 0,
        transferEffectiveness = 0,
        skillSimilarity = 0,
        timestamp = Date.now()
      } = transferData;
      const transfer = {
        timestamp,
        transferType: this.classifyTransferType(sourceSkill, targetSkill),
        transferEffectiveness,
        skillMapping: {
          source: sourceSkill,
          target: targetSkill,
          similarity: skillSimilarity,
          distance: transferDistance
        },
        performanceTransfer: this.analyzePerformanceTransfer(sourcePerformance, targetPerformance),
        transferMechanisms: this.identifyTransferMechanisms(sourceSkill, targetSkill),
        generalizationLevel: this.assessGeneralizationLevel(transferEffectiveness, skillSimilarity),
        nearTransfer: this.assessNearTransfer(skillSimilarity, transferEffectiveness),
        farTransfer: this.assessFarTransfer(transferDistance, transferEffectiveness),
        transferSpeed: this.calculateTransferSpeed(trainingTime, transferEffectiveness),
        metacognitiveTransfer: this.assessMetacognitiveTransfer(sourceSkill, targetSkill),
        transferInhibition: this.detectTransferInhibition(sourcePerformance, targetPerformance)
      };
      this.transferData.push(transfer);
      if (this.debugMode) {
        console.log("🔄 MusicalLearningCollector - Transferência coletada:", transfer);
      }
      return transfer;
    } catch (error) {
      console.error("Erro no MusicalLearningCollector.collectTransferLearning:", error);
      return null;
    }
  }
  /**
   * Analisa capacidade de adaptação a novos desafios
   */
  analyzeAdaptation(adaptationData) {
    try {
      if (!adaptationData || typeof adaptationData !== "object") {
        console.warn("MusicalLearningCollector: Dados de adaptação inválidos");
        return null;
      }
      const {
        newChallenge = "",
        challengeDifficulty = 0,
        adaptationTime = 0,
        initialPerformance = 0,
        adaptedPerformance = 0,
        strategiesUsed = [],
        flexibilityScore = 0,
        innovationLevel = 0,
        timestamp = Date.now()
      } = adaptationData;
      const adaptation = {
        timestamp,
        challengeType: newChallenge,
        adaptationMetrics: {
          adaptationSpeed: this.calculateAdaptationSpeed(adaptationTime, challengeDifficulty),
          performanceGain: adaptedPerformance - initialPerformance,
          adaptationEfficiency: this.calculateAdaptationEfficiency(adaptationTime, performanceGain),
          flexibilityScore
        },
        strategicAdaptation: {
          strategiesUsed,
          strategyDiversity: strategiesUsed.length,
          innovativeStrategies: this.identifyInnovativeStrategies(strategiesUsed),
          strategyEffectiveness: this.assessStrategyEffectiveness(strategiesUsed, adaptedPerformance)
        },
        cognitiveFlexibility: this.assessCognitiveFlexibility(flexibilityScore, adaptationTime),
        problemSolving: this.analyzeeProblemSolving(newChallenge, strategiesUsed),
        innovationCapacity: innovationLevel,
        adaptationPatterns: this.identifyAdaptationPatterns(this.adaptationData),
        learningTransfer: this.assessAdaptationTransfer(newChallenge, adaptedPerformance)
      };
      this.adaptationData.push(adaptation);
      if (this.debugMode) {
        console.log("🎯 MusicalLearningCollector - Adaptação analisada:", adaptation);
      }
      return adaptation;
    } catch (error) {
      console.error("Erro no MusicalLearningCollector.analyzeAdaptation:", error);
      return null;
    }
  }
  /**
   * Avalia níveis de maestria em diferentes aspectos musicais
   */
  assessMastery(masteryData) {
    try {
      if (!masteryData || typeof masteryData !== "object") {
        console.warn("MusicalLearningCollector: Dados de maestria inválidos");
        return null;
      }
      const {
        skill = "",
        currentLevel = 0,
        consistency = 0,
        automaticity = 0,
        flexibility = 0,
        creativity = 0,
        transferability = 0,
        complexity = 0,
        fluency = 0,
        timestamp = Date.now()
      } = masteryData;
      const mastery = {
        timestamp,
        skill,
        masteryLevel: this.calculateMasteryLevel(currentLevel, consistency, automaticity),
        masteryDimensions: {
          proficiency: currentLevel,
          consistency,
          automaticity,
          flexibility,
          creativity,
          transferability,
          complexity,
          fluency
        },
        expertiseMarkers: this.identifyExpertiseMarkers(masteryData),
        masteryProgression: this.analyzeMasteryProgression(skill, this.masteryData),
        masteryPrediction: this.predictMasteryTrajectory(skill, currentLevel, consistency),
        competencyProfile: this.createCompetencyProfile(masteryData),
        masteryGaps: this.identifyMasteryGaps(masteryData),
        developmentPriorities: this.suggestDevelopmentPriorities(masteryData),
        masteryValidation: this.validateMasteryLevel(masteryData)
      };
      this.masteryData.push(mastery);
      if (this.debugMode) {
        console.log("🏆 MusicalLearningCollector - Maestria avaliada:", mastery);
      }
      return mastery;
    } catch (error) {
      console.error("Erro no MusicalLearningCollector.assessMastery:", error);
      return null;
    }
  }
  /**
   * Analisa eficácia de diferentes estratégias de aprendizado
   */
  analyzeLearningStrategies(strategyData) {
    try {
      if (!strategyData || typeof strategyData !== "object") {
        console.warn("MusicalLearningCollector: Dados de estratégia inválidos");
        return null;
      }
      const {
        strategy = "",
        context = "",
        effectiveness = 0,
        usageFrequency = 0,
        outcomeImprovement = 0,
        timeInvestment = 0,
        difficulty = "",
        timestamp = Date.now()
      } = strategyData;
      const strategyAnalysis = {
        timestamp,
        strategy,
        context,
        effectiveness,
        efficiency: this.calculateStrategyEfficiency(effectiveness, timeInvestment),
        applicability: this.assessStrategyApplicability(strategy, context),
        learningAcceleration: this.measureLearningAcceleration(strategy, outcomeImprovement),
        strategicFit: this.assessStrategicFit(strategy, difficulty),
        costBenefit: this.analyzeCostBenefit(timeInvestment, outcomeImprovement),
        strategicEvolution: this.analyzeStrategicEvolution(strategy, this.learningData),
        personalizedEffectiveness: this.assessPersonalizedEffectiveness(strategy, this.learningData),
        contextualOptimization: this.suggestContextualOptimizations(strategy, context)
      };
      this.learningData.push(strategyAnalysis);
      if (this.debugMode) {
        console.log("📚 MusicalLearningCollector - Estratégia analisada:", strategyAnalysis);
      }
      return strategyAnalysis;
    } catch (error) {
      console.error("Erro no MusicalLearningCollector.analyzeLearningStrategies:", error);
      return null;
    }
  }
  // Métodos auxiliares para análise de progressão
  analyzeLevelProgression(currentLevel, previousLevel) {
    const levelGain = currentLevel - previousLevel;
    const progressionRate = previousLevel > 0 ? levelGain / previousLevel : 0;
    return {
      levelGain,
      progressionRate,
      progressionType: this.classifyProgressionType(levelGain),
      progressionSpeed: this.assessProgressionSpeed(levelGain)
    };
  }
  calculateOverallPerformance(accuracy, speed, consistency) {
    return accuracy * 0.4 + speed * 0.3 + consistency * 0.3;
  }
  analyzeLearningCurve(sessionNumber, accuracy) {
    if (this.progressionData.length < 3) {
      return { trend: "insufficient_data", steepness: 0, plateau: false };
    }
    const recentSessions = this.progressionData.slice(-5);
    const accuracies = recentSessions.map((s) => s.performanceMetrics.accuracy);
    const trend = this.calculateTrend(accuracies);
    const steepness = this.calculateCurveSteepness(accuracies);
    const plateau = this.detectPlateau(accuracies);
    return { trend, steepness, plateau, curveType: this.classifyCurveType(trend, steepness) };
  }
  assessMasteryIndicators(accuracy, consistency, level) {
    const masteryThreshold = 0.8;
    const consistencyThreshold = 0.85;
    return {
      accuracyMastery: accuracy >= masteryThreshold,
      consistencyMastery: consistency >= consistencyThreshold,
      levelMastery: level >= 5,
      // Simplified level-based mastery
      overallMastery: accuracy >= masteryThreshold && consistency >= consistencyThreshold,
      masteryProgress: Math.min((accuracy + consistency) / 2, 1)
    };
  }
  analyzeAdaptiveBehavior(difficulty, accuracy) {
    return {
      difficultyHandling: this.assessDifficultyHandling(difficulty, accuracy),
      adaptiveResponse: this.measureAdaptiveResponse(accuracy),
      challengeSeeking: this.assessChallengeSeeking(difficulty),
      resilienceLevel: this.calculateResilienceLevel(accuracy)
    };
  }
  calculateEngagementLevel(sequencesCompleted, challengesSolved) {
    const engagementScore = sequencesCompleted * 0.6 + challengesSolved * 0.4;
    return Math.min(engagementScore / 10, 1);
  }
  detectLearningPlateau(progressionHistory, currentAccuracy) {
    if (!Array.isArray(progressionHistory) || progressionHistory.length < 5) {
      return { plateauDetected: false, plateauDuration: 0 };
    }
    const recentAccuracies = progressionHistory.slice(-5).map((p) => p.performanceMetrics.accuracy);
    const variance = this.calculateVariance(recentAccuracies);
    const plateauThreshold = 0.05;
    return {
      plateauDetected: variance < plateauThreshold,
      plateauDuration: variance < plateauThreshold ? recentAccuracies.length : 0,
      plateauLevel: variance < plateauThreshold ? currentAccuracy : 0
    };
  }
  // Métodos auxiliares para análise de retenção
  calculateRetentionRate(initialPerformance, returnPerformance) {
    if (initialPerformance === 0) return 0;
    return Math.min(returnPerformance / initialPerformance, 1);
  }
  analyzeForgettingCurve(forgettingData, timeSinceLastSession) {
    if (!Array.isArray(forgettingData) || forgettingData.length === 0) {
      return this.estimateForgettingCurve(timeSinceLastSession);
    }
    const decayRate = this.calculateDecayRate(forgettingData);
    const memoryStrength = this.calculateMemoryStrength(forgettingData);
    return {
      decayRate,
      memoryStrength,
      forgettingFunction: this.modelForgettingFunction(forgettingData),
      retentionPrediction: this.predictRetention(decayRate, timeSinceLastSession)
    };
  }
  analyzeMasteryRetention(previousMastery, currentMastery) {
    const masteryRetention = currentMastery / Math.max(previousMastery, 0.1);
    return {
      masteryRetention,
      masteryLoss: Math.max(0, previousMastery - currentMastery),
      masteryStability: this.assessMasteryStability(masteryRetention),
      reconsolidationNeeded: masteryRetention < 0.8
    };
  }
  assessConsolidationLevel(timeSinceLastSession, returnPerformance) {
    const timeDecayFactor = Math.exp(-timeSinceLastSession / (7 * 24 * 60 * 60 * 1e3));
    const consolidationScore = returnPerformance * timeDecayFactor;
    return {
      consolidationScore,
      consolidationLevel: this.classifyConsolidationLevel(consolidationScore),
      reconsolidationStrength: this.assessReconsolidationStrength(consolidationScore)
    };
  }
  analyzePracticeEffect(practiceFrequency, retentionRate2) {
    return {
      practiceEffect: practiceFrequency * retentionRate2,
      practiceEfficiency: this.calculatePracticeEfficiency(practiceFrequency, retentionRate2),
      optimalPracticeFrequency: this.suggestOptimalPracticeFrequency(retentionRate2)
    };
  }
  calculateMemoryStrength(initialPerformance, returnPerformance, timeSinceLastSession) {
    const retentionRate2 = this.calculateRetentionRate(initialPerformance, returnPerformance);
    const timeDecay = Math.exp(-timeSinceLastSession / (24 * 60 * 60 * 1e3));
    return retentionRate2 * timeDecay;
  }
  assessRelearningSpeed(initialPerformance, returnPerformance) {
    const performanceDifference = initialPerformance - returnPerformance;
    if (performanceDifference <= 0) return 1;
    const retentionRate2 = this.calculateRetentionRate(initialPerformance, returnPerformance);
    return Math.max(0.1, retentionRate2);
  }
  assessLongTermRetention(timeSinceLastSession, returnPerformance) {
    const longTermThreshold = 7 * 24 * 60 * 60 * 1e3;
    const isLongTerm = timeSinceLastSession > longTermThreshold;
    return {
      isLongTermRetention: isLongTerm,
      longTermPerformance: isLongTerm ? returnPerformance : null,
      retentionCategory: this.categorizePetention(timeSinceLastSession, returnPerformance)
    };
  }
  identifyRetentionStrategies(practiceFrequency, returnPerformance) {
    const strategies = [];
    if (practiceFrequency > 0.7) strategies.push("distributed_practice");
    if (returnPerformance > 0.8) strategies.push("deep_learning");
    if (practiceFrequency < 0.3 && returnPerformance > 0.6) strategies.push("efficient_encoding");
    return strategies.length > 0 ? strategies : ["basic_repetition"];
  }
  // Métodos auxiliares para análise de transferência
  classifyTransferType(sourceSkill, targetSkill) {
    const similarityScore = this.calculateSkillSimilarity(sourceSkill, targetSkill);
    if (similarityScore > 0.8) return "near_transfer";
    if (similarityScore > 0.4) return "intermediate_transfer";
    return "far_transfer";
  }
  analyzePerformanceTransfer(sourcePerformance, targetPerformance) {
    const transferGain = targetPerformance - sourcePerformance;
    const transferEfficiency = targetPerformance / Math.max(sourcePerformance, 0.1);
    return {
      transferGain,
      transferEfficiency,
      transferQuality: this.assessTransferQuality(transferGain, transferEfficiency)
    };
  }
  identifyTransferMechanisms(sourceSkill, targetSkill) {
    const mechanisms = [];
    if (this.sharesCognitiveDemands(sourceSkill, targetSkill)) {
      mechanisms.push("cognitive_overlap");
    }
    if (this.sharesMotorComponents(sourceSkill, targetSkill)) {
      mechanisms.push("motor_transfer");
    }
    if (this.sharesStrategicElements(sourceSkill, targetSkill)) {
      mechanisms.push("strategic_transfer");
    }
    return mechanisms.length > 0 ? mechanisms : ["general_transfer"];
  }
  assessGeneralizationLevel(transferEffectiveness, skillSimilarity) {
    return {
      generalizationScore: transferEffectiveness * (1 - skillSimilarity),
      generalizationLevel: this.classifyGeneralizationLevel(transferEffectiveness, skillSimilarity)
    };
  }
  // Métodos auxiliares para análise de adaptação
  calculateAdaptationSpeed(adaptationTime, challengeDifficulty) {
    const normalizedTime = adaptationTime / (challengeDifficulty * 1e3);
    return Math.max(0, 1 - normalizedTime / 10);
  }
  calculateAdaptationEfficiency(adaptationTime, performanceGain2) {
    if (adaptationTime === 0) return 0;
    return performanceGain2 / adaptationTime;
  }
  identifyInnovativeStrategies(strategiesUsed) {
    const commonStrategies = ["repetition", "chunking", "practice"];
    return strategiesUsed.filter((strategy) => !commonStrategies.includes(strategy));
  }
  assessStrategyEffectiveness(strategiesUsed, adaptedPerformance) {
    return {
      effectivenessScore: adaptedPerformance,
      strategyDiversity: strategiesUsed.length,
      strategicSuccess: adaptedPerformance > 0.7
    };
  }
  assessCognitiveFlexibility(flexibilityScore, adaptationTime) {
    return {
      flexibility: flexibilityScore,
      flexibilitySpeed: this.calculateFlexibilitySpeed(adaptationTime),
      flexibilityEfficiency: flexibilityScore / Math.max(adaptationTime / 1e3, 1)
    };
  }
  // Métodos auxiliares para avaliação de maestria
  calculateMasteryLevel(currentLevel, consistency, automaticity) {
    return currentLevel * 0.4 + consistency * 0.3 + automaticity * 0.3;
  }
  identifyExpertiseMarkers(masteryData) {
    const markers = [];
    if (masteryData.consistency >= 0.9) markers.push("high_consistency");
    if (masteryData.automaticity >= 0.8) markers.push("automaticity");
    if (masteryData.flexibility >= 0.8) markers.push("adaptive_expertise");
    if (masteryData.creativity >= 0.7) markers.push("creative_application");
    if (masteryData.transferability >= 0.8) markers.push("transfer_ability");
    return markers;
  }
  analyzeMasteryProgression(skill, masteryHistory) {
    if (!Array.isArray(masteryHistory) || masteryHistory.length < 2) {
      return { progression: "insufficient_data", rate: 0 };
    }
    const skillHistory = masteryHistory.filter((m) => m.skill === skill);
    if (skillHistory.length < 2) {
      return { progression: "new_skill", rate: 0 };
    }
    const recent = skillHistory.slice(-3);
    const older = skillHistory.slice(0, -3);
    const recentMastery = recent.reduce((sum, m) => sum + m.masteryLevel, 0) / recent.length;
    const olderMastery = older.length > 0 ? older.reduce((sum, m) => sum + m.masteryLevel, 0) / older.length : recentMastery;
    const progressionRate = (recentMastery - olderMastery) / Math.max(olderMastery, 0.1);
    return {
      progression: this.classifyProgressionPattern(progressionRate),
      rate: progressionRate,
      trajectory: this.analyzeTrajectory(skillHistory)
    };
  }
  predictMasteryTrajectory(skill, currentLevel, consistency) {
    const growthRate = consistency * 0.1;
    const projectedLevel = currentLevel + growthRate;
    return {
      projectedLevel,
      timeToMastery: this.estimateTimeToMastery(currentLevel, growthRate),
      masteryProbability: this.calculateMasteryProbability(currentLevel, consistency)
    };
  }
  createCompetencyProfile(masteryData) {
    return {
      strengths: this.identifyStrengths(masteryData),
      weaknesses: this.identifyWeaknesses(masteryData),
      balanceScore: this.calculateBalanceScore(masteryData),
      developmentAreas: this.identifyDevelopmentAreas(masteryData)
    };
  }
  // Métodos utilitários
  calculateVariance(values) {
    if (!Array.isArray(values) || values.length === 0) return 0;
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    return values.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / values.length;
  }
  calculateTrend(values) {
    if (!Array.isArray(values) || values.length < 2) return "stable";
    let increasing = 0;
    let decreasing = 0;
    for (let i = 1; i < values.length; i++) {
      if (values[i] > values[i - 1]) increasing++;
      else if (values[i] < values[i - 1]) decreasing++;
    }
    const total = values.length - 1;
    if (increasing / total > 0.6) return "increasing";
    if (decreasing / total > 0.6) return "decreasing";
    return "stable";
  }
  calculateCurveSteepness(values) {
    if (!Array.isArray(values) || values.length < 2) return 0;
    const firstValue = values[0];
    const lastValue = values[values.length - 1];
    const range = lastValue - firstValue;
    const timeSpan = values.length - 1;
    return Math.abs(range / timeSpan);
  }
  detectPlateau(values) {
    if (!Array.isArray(values) || values.length < 3) return false;
    const variance = this.calculateVariance(values);
    return variance < 0.01;
  }
  classifyCurveType(trend, steepness) {
    if (trend === "increasing" && steepness > 0.1) return "steep_learning";
    if (trend === "increasing" && steepness <= 0.1) return "gradual_learning";
    if (trend === "stable") return "plateau";
    if (trend === "decreasing") return "decline";
    return "irregular";
  }
  classifyProgressionType(levelGain) {
    if (levelGain > 2) return "rapid_progression";
    if (levelGain > 0) return "steady_progression";
    if (levelGain === 0) return "stable";
    return "regression";
  }
  assessProgressionSpeed(levelGain) {
    if (levelGain > 3) return "very_fast";
    if (levelGain > 1) return "fast";
    if (levelGain > 0) return "normal";
    return "slow";
  }
  assessDifficultyHandling(difficulty, accuracy) {
    const difficultyMap = { easy: 0.3, medium: 0.6, hard: 0.9 };
    const expectedAccuracy = 1 - difficultyMap[difficulty] || 0.5;
    return accuracy / expectedAccuracy;
  }
  measureAdaptiveResponse(accuracy) {
    return accuracy > 0.7 ? "adaptive" : "struggling";
  }
  assessChallengeSeeking(difficulty) {
    const challengeMap = { easy: 0.2, medium: 0.6, hard: 1 };
    return challengeMap[difficulty] || 0.5;
  }
  calculateResilienceLevel(accuracy) {
    return Math.min(accuracy * 1.2, 1);
  }
  estimateForgettingCurve(timeSinceLastSession) {
    const timeInDays = timeSinceLastSession / (24 * 60 * 60 * 1e3);
    const retention = Math.exp(-0.5 * timeInDays);
    return { estimatedRetention: retention, curveType: "exponential_decay" };
  }
  calculateDecayRate(forgettingData) {
    if (!Array.isArray(forgettingData) || forgettingData.length < 2) return 0.5;
    const firstPoint = forgettingData[0];
    const lastPoint = forgettingData[forgettingData.length - 1];
    const timeSpan = lastPoint.time - firstPoint.time;
    const performanceDecay = firstPoint.performance - lastPoint.performance;
    return timeSpan > 0 ? performanceDecay / timeSpan : 0.5;
  }
  calculateSkillSimilarity(skill1, skill2) {
    const skillCategories = {
      "musical_sequence": ["auditory", "memory", "pattern"],
      "letter_recognition": ["visual", "linguistic", "pattern"],
      "rhythm_pattern": ["auditory", "temporal", "pattern"],
      "melody_recognition": ["auditory", "musical", "pattern"]
    };
    const cat1 = skillCategories[skill1] || [];
    const cat2 = skillCategories[skill2] || [];
    const intersection = cat1.filter((c) => cat2.includes(c));
    const union = [.../* @__PURE__ */ new Set([...cat1, ...cat2])];
    return union.length > 0 ? intersection.length / union.length : 0;
  }
  sharesCognitiveDemands(skill1, skill2) {
    const cognitiveSkills = ["memory", "attention", "pattern_recognition"];
    return this.skillsShareAttributes(skill1, skill2, cognitiveSkills);
  }
  sharesMotorComponents(skill1, skill2) {
    const motorSkills = ["timing", "coordination", "precision"];
    return this.skillsShareAttributes(skill1, skill2, motorSkills);
  }
  sharesStrategicElements(skill1, skill2) {
    const strategicSkills = ["planning", "sequencing", "organization"];
    return this.skillsShareAttributes(skill1, skill2, strategicSkills);
  }
  skillsShareAttributes(skill1, skill2, attributes) {
    return Math.random() > 0.5;
  }
  identifyStrengths(masteryData) {
    const threshold = 0.8;
    const strengths = [];
    Object.keys(masteryData.masteryDimensions || {}).forEach((dimension) => {
      if (masteryData.masteryDimensions[dimension] >= threshold) {
        strengths.push(dimension);
      }
    });
    return strengths;
  }
  identifyWeaknesses(masteryData) {
    const threshold = 0.4;
    const weaknesses = [];
    Object.keys(masteryData.masteryDimensions || {}).forEach((dimension) => {
      if (masteryData.masteryDimensions[dimension] <= threshold) {
        weaknesses.push(dimension);
      }
    });
    return weaknesses;
  }
  calculateBalanceScore(masteryData) {
    const dimensions = Object.values(masteryData.masteryDimensions || {});
    if (dimensions.length === 0) return 0;
    const variance = this.calculateVariance(dimensions);
    return Math.max(0, 1 - variance);
  }
  // Métodos de relatório e análise agregada
  getLearningReport() {
    return {
      totalSessions: this.progressionData.length,
      overallProgression: this.calculateOverallProgression(),
      retentionSummary: this.summarizeRetention(),
      transferEffectiveness: this.summarizeTransfer(),
      adaptationCapacity: this.summarizeAdaptation(),
      masteryStatus: this.summarizeMastery(),
      learningTrends: this.identifyLearningTrends(),
      recommendations: this.generateLearningRecommendations()
    };
  }
  calculateOverallProgression() {
    if (this.progressionData.length === 0) return { trend: "no_data", rate: 0 };
    const accuracies = this.progressionData.map((p) => p.performanceMetrics.accuracy);
    const trend = this.calculateTrend(accuracies);
    const avgImprovement = this.progressionData.reduce((sum, p) => sum + p.improvementRate, 0) / this.progressionData.length;
    return { trend, rate: avgImprovement, overallScore: accuracies[accuracies.length - 1] || 0 };
  }
  summarizeRetention() {
    if (this.retentionData.length === 0) return { avgRetention: 0, retentionStability: "unknown" };
    const avgRetention = this.retentionData.reduce((sum, r) => sum + r.retentionRate, 0) / this.retentionData.length;
    const retentionVariance = this.calculateVariance(this.retentionData.map((r) => r.retentionRate));
    return {
      avgRetention,
      retentionStability: retentionVariance < 0.1 ? "stable" : "variable",
      longTermRetention: this.assessOverallLongTermRetention()
    };
  }
  summarizeTransfer() {
    if (this.transferData.length === 0) return { transferCapacity: 0, transferTypes: [] };
    const avgEffectiveness = this.transferData.reduce((sum, t) => sum + t.transferEffectiveness, 0) / this.transferData.length;
    const transferTypes = [...new Set(this.transferData.map((t) => t.transferType))];
    return { transferCapacity: avgEffectiveness, transferTypes, transferCount: this.transferData.length };
  }
  summarizeAdaptation() {
    if (this.adaptationData.length === 0) return { adaptationSpeed: 0, flexibility: 0 };
    const avgSpeed = this.adaptationData.reduce((sum, a) => sum + a.adaptationMetrics.adaptationSpeed, 0) / this.adaptationData.length;
    const avgFlexibility = this.adaptationData.reduce((sum, a) => sum + a.cognitiveFlexibility.flexibility, 0) / this.adaptationData.length;
    return { adaptationSpeed: avgSpeed, flexibility: avgFlexibility, adaptationCount: this.adaptationData.length };
  }
  summarizeMastery() {
    if (this.masteryData.length === 0) return { masteryLevel: 0, masteredSkills: [] };
    const avgMastery = this.masteryData.reduce((sum, m) => sum + m.masteryLevel, 0) / this.masteryData.length;
    const masteredSkills = this.masteryData.filter((m) => m.masteryLevel >= 0.8).map((m) => m.skill);
    return { masteryLevel: avgMastery, masteredSkills, totalSkills: this.masteryData.length };
  }
  identifyLearningTrends() {
    return {
      progressionTrend: this.calculateOverallProgression().trend,
      retentionTrend: this.analyzeRetentionTrend(),
      adaptationTrend: this.analyzeAdaptationTrend(),
      masteryTrend: this.analyzeMasteryTrend()
    };
  }
  generateLearningRecommendations() {
    const recommendations = [];
    if (this.progressionData.length > 0) {
      const latestProgression = this.progressionData[this.progressionData.length - 1];
      if (latestProgression.plateauDetection.plateauDetected) {
        recommendations.push("Consider increasing difficulty or trying new strategies to overcome learning plateau");
      }
      if (latestProgression.performanceMetrics.consistency < 0.7) {
        recommendations.push("Focus on consistent practice to improve stability");
      }
    }
    if (this.retentionData.length > 0) {
      const avgRetention = this.retentionData.reduce((sum, r) => sum + r.retentionRate, 0) / this.retentionData.length;
      if (avgRetention < 0.6) {
        recommendations.push("Implement spaced repetition to improve long-term retention");
      }
    }
    if (this.adaptationData.length > 0) {
      const avgFlexibility = this.adaptationData.reduce((sum, a) => sum + a.cognitiveFlexibility.flexibility, 0) / this.adaptationData.length;
      if (avgFlexibility < 0.6) {
        recommendations.push("Practice with varied challenges to improve cognitive flexibility");
      }
    }
    return recommendations.length > 0 ? recommendations : ["Continue practicing regularly to maintain progress"];
  }
  // Métodos auxiliares adicionais (implementações simplificadas)
  classifyConsolidationLevel(score) {
    if (score > 0.8) return "strong";
    if (score > 0.5) return "moderate";
    return "weak";
  }
  assessReconsolidationStrength(score) {
    return score;
  }
  calculatePracticeEfficiency(frequency, retention) {
    return frequency * retention;
  }
  suggestOptimalPracticeFrequency(retentionRate2) {
    if (retentionRate2 > 0.8) return "maintain_current";
    if (retentionRate2 > 0.6) return "increase_slightly";
    return "increase_significantly";
  }
  categorizePetention(timeSinceLastSession, performance) {
    const hours = timeSinceLastSession / (60 * 60 * 1e3);
    if (hours < 24) return "short_term";
    if (hours < 168) return "medium_term";
    return "long_term";
  }
  assessTransferQuality(gain, efficiency) {
    return (gain + efficiency) / 2;
  }
  assessNearTransfer(similarity, effectiveness) {
    return similarity > 0.7 ? effectiveness : effectiveness * 0.5;
  }
  assessFarTransfer(distance, effectiveness) {
    return distance > 0.7 ? effectiveness : effectiveness * 0.5;
  }
  calculateTransferSpeed(trainingTime, effectiveness) {
    return trainingTime > 0 ? effectiveness / trainingTime : 0;
  }
  assessMetacognitiveTransfer(sourceSkill, targetSkill) {
    return this.calculateSkillSimilarity(sourceSkill, targetSkill) * 0.7;
  }
  detectTransferInhibition(sourcePerformance, targetPerformance) {
    return sourcePerformance > targetPerformance ? "possible_inhibition" : "no_inhibition";
  }
  classifyGeneralizationLevel(effectiveness, similarity) {
    const generalizationScore = effectiveness * (1 - similarity);
    if (generalizationScore > 0.7) return "high_generalization";
    if (generalizationScore > 0.4) return "moderate_generalization";
    return "low_generalization";
  }
  calculateFlexibilitySpeed(adaptationTime) {
    return Math.max(0, 1 - adaptationTime / 1e4);
  }
  analyzeeProblemSolving(challenge, strategies) {
    return {
      challengeComplexity: this.assessChallengeComplexity(challenge),
      solutionCreativity: this.assessSolutionCreativity(strategies),
      problemSolvingEfficiency: strategies.length > 0 ? 1 / strategies.length : 0
    };
  }
  identifyAdaptationPatterns(adaptationHistory) {
    if (!Array.isArray(adaptationHistory) || adaptationHistory.length < 2) return [];
    return ["pattern_recognition", "strategy_refinement"];
  }
  assessAdaptationTransfer(challenge, performance) {
    return { transferEvidence: performance > 0.7, transferStrength: performance };
  }
  estimateTimeToMastery(currentLevel, growthRate) {
    const masteryThreshold = 0.9;
    const timeToMastery = growthRate > 0 ? (masteryThreshold - currentLevel) / growthRate : Infinity;
    return Math.max(0, timeToMastery);
  }
  calculateMasteryProbability(currentLevel, consistency) {
    return Math.min((currentLevel + consistency) / 2, 1);
  }
  identifyMasteryGaps(masteryData) {
    const threshold = 0.6;
    const gaps = [];
    Object.keys(masteryData.masteryDimensions || {}).forEach((dimension) => {
      if (masteryData.masteryDimensions[dimension] < threshold) {
        gaps.push({
          dimension,
          gap: threshold - masteryData.masteryDimensions[dimension],
          priority: this.calculateGapPriority(dimension, masteryData.masteryDimensions[dimension])
        });
      }
    });
    return gaps.sort((a, b) => b.priority - a.priority);
  }
  suggestDevelopmentPriorities(masteryData) {
    const gaps = this.identifyMasteryGaps(masteryData);
    return gaps.slice(0, 3).map((gap) => gap.dimension);
  }
  validateMasteryLevel(masteryData) {
    const dimensions = Object.values(masteryData.masteryDimensions || {});
    const avgScore = dimensions.reduce((a, b) => a + b, 0) / dimensions.length;
    const calculatedMastery = this.calculateMasteryLevel(avgScore, masteryData.consistency || 0, masteryData.automaticity || 0);
    return {
      validated: Math.abs(calculatedMastery - masteryData.currentLevel) < 0.1,
      confidence: 1 - Math.abs(calculatedMastery - masteryData.currentLevel),
      recommendedLevel: calculatedMastery
    };
  }
  identifyDevelopmentAreas(masteryData) {
    return this.identifyWeaknesses(masteryData);
  }
  calculateStrategyEfficiency(effectiveness, timeInvestment) {
    return timeInvestment > 0 ? effectiveness / timeInvestment : 0;
  }
  assessStrategyApplicability(strategy, context) {
    const strategyContextMap = {
      "repetition": ["basic", "memorization"],
      "chunking": ["complex", "sequence"],
      "elaboration": ["understanding", "concept"],
      "practice": ["skill", "motor"]
    };
    const applicableContexts = strategyContextMap[strategy] || [];
    return applicableContexts.includes(context) ? 1 : 0.5;
  }
  measureLearningAcceleration(strategy, improvement) {
    return improvement;
  }
  assessStrategicFit(strategy, difficulty) {
    const strategyDifficultyFit = {
      "repetition": { easy: 0.8, medium: 0.6, hard: 0.4 },
      "chunking": { easy: 0.5, medium: 0.8, hard: 0.9 },
      "elaboration": { easy: 0.6, medium: 0.8, hard: 0.7 }
    };
    return strategyDifficultyFit[strategy]?.[difficulty] || 0.5;
  }
  analyzeCostBenefit(timeInvestment, improvement) {
    return {
      costBenefitRatio: timeInvestment > 0 ? improvement / timeInvestment : 0,
      efficiency: this.calculateStrategyEfficiency(improvement, timeInvestment),
      recommendation: improvement > timeInvestment ? "continue" : "reconsider"
    };
  }
  analyzeStrategicEvolution(strategy, learningHistory) {
    return { evolutionPattern: "stable", adaptationLevel: 0.5 };
  }
  assessPersonalizedEffectiveness(strategy, learningHistory) {
    if (!Array.isArray(learningHistory) || learningHistory.length === 0) return 0.5;
    const strategyUses = learningHistory.filter((l) => l.strategy === strategy);
    if (strategyUses.length === 0) return 0.5;
    const avgEffectiveness = strategyUses.reduce((sum, s) => sum + s.effectiveness, 0) / strategyUses.length;
    return avgEffectiveness;
  }
  suggestContextualOptimizations(strategy, context) {
    return [`Optimize ${strategy} for ${context} context`];
  }
  analyzeRetentionTrend() {
    if (this.retentionData.length < 2) return "insufficient_data";
    const retentionRates = this.retentionData.map((r) => r.retentionRate);
    return this.calculateTrend(retentionRates);
  }
  analyzeAdaptationTrend() {
    if (this.adaptationData.length < 2) return "insufficient_data";
    const adaptationSpeeds = this.adaptationData.map((a) => a.adaptationMetrics.adaptationSpeed);
    return this.calculateTrend(adaptationSpeeds);
  }
  analyzeMasteryTrend() {
    if (this.masteryData.length < 2) return "insufficient_data";
    const masteryLevels = this.masteryData.map((m) => m.masteryLevel);
    return this.calculateTrend(masteryLevels);
  }
  assessOverallLongTermRetention() {
    const longTermRetentions = this.retentionData.filter((r) => r.longTermRetention.isLongTermRetention);
    if (longTermRetentions.length === 0) return "no_long_term_data";
    const avgLongTermRetention = longTermRetentions.reduce((sum, r) => sum + r.retentionRate, 0) / longTermRetentions.length;
    return avgLongTermRetention;
  }
  assessChallengeComplexity(challenge) {
    const complexityMap = {
      "sequence_extension": 0.6,
      "tempo_change": 0.4,
      "instrument_change": 0.5,
      "pattern_variation": 0.7,
      "difficulty_increase": 0.8
    };
    return complexityMap[challenge] || 0.5;
  }
  assessSolutionCreativity(strategies) {
    const uniqueStrategies = new Set(strategies);
    const creativityScore = uniqueStrategies.size / Math.max(strategies.length, 1);
    return creativityScore;
  }
  calculateGapPriority(dimension, currentLevel) {
    const priorityWeights = {
      "accuracy": 0.9,
      "consistency": 0.8,
      "automaticity": 0.7,
      "flexibility": 0.6,
      "creativity": 0.5,
      "transferability": 0.6
    };
    const weight = priorityWeights[dimension] || 0.5;
    const gap = 1 - currentLevel;
    return weight * gap;
  }
  clearData() {
    this.learningData = [];
    this.progressionData = [];
    this.retentionData = [];
    this.transferData = [];
    this.adaptationData = [];
    this.masteryData = [];
    if (this.debugMode) {
      console.log("📚 MusicalLearningCollector - Dados limpos");
    }
  }
}
class RhythmPatternCollector {
  constructor() {
    this.collectorId = "rhythm-pattern";
    this.collectorName = "Rhythm Pattern Collector";
    this.version = "1.0.0";
    this.isActive = true;
    this.metrics = {
      rhythmAccuracy: 0,
      tempoConsistency: 0,
      rhythmComplexityHandling: 0,
      syncronizationSkill: 0
    };
    this.collectionHistory = [];
    this.patterns = {
      rhythmPatterns: [],
      tempoVariations: [],
      syncEvents: []
    };
    console.log(`🎵 ${this.collectorName} inicializado`);
  }
  /**
   * Coleta dados de padrões rítmicos
   */
  async collectRhythmData(gameData) {
    try {
      const rhythmData = {
        timestamp: Date.now(),
        sessionId: gameData.sessionId,
        rhythmAccuracy: this.analyzeRhythmAccuracy(gameData),
        tempoAnalysis: this.analyzeTempoConsistency(gameData),
        complexityHandling: this.analyzeComplexityHandling(gameData),
        synchronization: this.analyzeSynchronization(gameData)
      };
      this.collectionHistory.push(rhythmData);
      this.updateMetrics(rhythmData);
      return rhythmData;
    } catch (error) {
      console.error("Erro ao coletar dados de padrão rítmico:", error);
      return null;
    }
  }
  /**
   * Analisa precisão rítmica
   */
  analyzeRhythmAccuracy(gameData) {
    const attempts = gameData.attempts || [];
    const rhythmAnalysis = [];
    attempts.forEach((attempt) => {
      const expectedRhythm = attempt.expectedRhythm || [];
      const userRhythm = attempt.userRhythm || [];
      const accuracy = this.calculateRhythmAccuracy(expectedRhythm, userRhythm);
      const timing = this.analyzeRhythmTiming(expectedRhythm, userRhythm);
      rhythmAnalysis.push({
        attemptId: attempt.id,
        accuracy,
        timing,
        complexity: this.calculateRhythmComplexity(expectedRhythm)
      });
    });
    return this.summarizeRhythmAccuracy(rhythmAnalysis);
  }
  /**
   * Calcula precisão do ritmo
   */
  calculateRhythmAccuracy(expected, user) {
    if (expected.length === 0 || user.length === 0) return 0;
    const minLength = Math.min(expected.length, user.length);
    let correctBeats = 0;
    for (let i = 0; i < minLength; i++) {
      const expectedBeat = expected[i];
      const userBeat = user[i];
      const timingTolerance = 100;
      const timingDiff = Math.abs((expectedBeat.timing || 0) - (userBeat.timing || 0));
      if (timingDiff <= timingTolerance) {
        correctBeats++;
      }
    }
    return correctBeats / expected.length * 100;
  }
  /**
   * Analisa timing do ritmo
   */
  analyzeRhythmTiming(expected, user) {
    if (expected.length === 0 || user.length === 0) return {};
    const timingDeviations = [];
    const minLength = Math.min(expected.length, user.length);
    for (let i = 0; i < minLength; i++) {
      const expectedTiming = expected[i].timing || 0;
      const userTiming = user[i].timing || 0;
      const deviation = userTiming - expectedTiming;
      timingDeviations.push(deviation);
    }
    const avgDeviation = timingDeviations.reduce((a, b) => a + b, 0) / timingDeviations.length;
    const absAvgDeviation = timingDeviations.reduce((a, b) => a + Math.abs(b), 0) / timingDeviations.length;
    return {
      averageDeviation: avgDeviation,
      absoluteAverageDeviation: absAvgDeviation,
      consistency: Math.max(0, 100 - absAvgDeviation / 10),
      tendency: avgDeviation > 0 ? "late" : avgDeviation < 0 ? "early" : "accurate"
    };
  }
  /**
   * Calcula complexidade do ritmo
   */
  calculateRhythmComplexity(rhythm) {
    if (rhythm.length === 0) return 0;
    let complexity = 0;
    const durations = rhythm.map((beat) => beat.duration || 500);
    const uniqueDurations = new Set(durations);
    complexity += uniqueDurations.size;
    const accents = rhythm.map((beat) => beat.accent || false);
    const accentPattern = accents.filter(Boolean).length;
    complexity += accentPattern;
    const syncopations = this.detectSyncopations(rhythm);
    complexity += syncopations * 2;
    return Math.min(complexity, 10);
  }
  /**
   * Detecta síncopes
   */
  detectSyncopations(rhythm) {
    let syncopations = 0;
    for (let i = 1; i < rhythm.length; i++) {
      const current = rhythm[i];
      const previous = rhythm[i - 1];
      const expectedTiming = (previous.timing || 0) + (previous.duration || 500);
      const actualTiming = current.timing || 0;
      const difference = Math.abs(actualTiming - expectedTiming);
      if (difference > 50) {
        syncopations++;
      }
    }
    return syncopations;
  }
  /**
   * Sumariza precisão rítmica
   */
  summarizeRhythmAccuracy(rhythmAnalysis) {
    if (rhythmAnalysis.length === 0) return {};
    const avgAccuracy = rhythmAnalysis.reduce((a, b) => a + b.accuracy, 0) / rhythmAnalysis.length;
    const avgTiming = rhythmAnalysis.reduce((a, b) => a + (b.timing.consistency || 0), 0) / rhythmAnalysis.length;
    const avgComplexity = rhythmAnalysis.reduce((a, b) => a + b.complexity, 0) / rhythmAnalysis.length;
    return {
      averageAccuracy: avgAccuracy,
      averageTimingConsistency: avgTiming,
      averageComplexity: avgComplexity,
      improvementTrend: this.calculateImprovementTrend(rhythmAnalysis)
    };
  }
  /**
   * Calcula tendência de melhoria
   */
  calculateImprovementTrend(rhythmAnalysis) {
    if (rhythmAnalysis.length < 3) return 0;
    const firstThird = rhythmAnalysis.slice(0, Math.floor(rhythmAnalysis.length / 3));
    const lastThird = rhythmAnalysis.slice(-Math.floor(rhythmAnalysis.length / 3));
    const firstAvg = firstThird.reduce((a, b) => a + b.accuracy, 0) / firstThird.length;
    const lastAvg = lastThird.reduce((a, b) => a + b.accuracy, 0) / lastThird.length;
    return (lastAvg - firstAvg) / firstAvg * 100;
  }
  /**
   * Analisa consistência de tempo
   */
  analyzeTempoConsistency(gameData) {
    const attempts = gameData.attempts || [];
    const tempoAnalysis = [];
    attempts.forEach((attempt) => {
      const userRhythm = attempt.userRhythm || [];
      const tempoConsistency = this.calculateTempoConsistency(userRhythm);
      tempoAnalysis.push({
        attemptId: attempt.id,
        consistency: tempoConsistency,
        averageTempo: this.calculateAverageTempo(userRhythm),
        tempoVariations: this.analyzeTempoVariations(userRhythm)
      });
    });
    return this.summarizeTempoAnalysis(tempoAnalysis);
  }
  /**
   * Calcula consistência de tempo
   */
  calculateTempoConsistency(rhythm) {
    if (rhythm.length < 2) return 100;
    const intervals = [];
    for (let i = 1; i < rhythm.length; i++) {
      const interval = (rhythm[i].timing || 0) - (rhythm[i - 1].timing || 0);
      intervals.push(interval);
    }
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const variance = intervals.reduce((a, b) => a + Math.pow(b - avgInterval, 2), 0) / intervals.length;
    const standardDeviation = Math.sqrt(variance);
    const consistency = Math.max(0, 100 - standardDeviation / avgInterval * 100);
    return consistency;
  }
  /**
   * Calcula tempo médio
   */
  calculateAverageTempo(rhythm) {
    if (rhythm.length < 2) return 0;
    const intervals = [];
    for (let i = 1; i < rhythm.length; i++) {
      const interval = (rhythm[i].timing || 0) - (rhythm[i - 1].timing || 0);
      intervals.push(interval);
    }
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const bpm = avgInterval > 0 ? 6e4 / avgInterval : 0;
    return bpm;
  }
  /**
   * Analisa variações de tempo
   */
  analyzeTempoVariations(rhythm) {
    if (rhythm.length < 3) return {};
    const intervals = [];
    for (let i = 1; i < rhythm.length; i++) {
      const interval = (rhythm[i].timing || 0) - (rhythm[i - 1].timing || 0);
      intervals.push(interval);
    }
    const accelerations = [];
    const decelerations = [];
    for (let i = 1; i < intervals.length; i++) {
      const change = intervals[i] - intervals[i - 1];
      if (change > 0) {
        decelerations.push(change);
      } else if (change < 0) {
        accelerations.push(Math.abs(change));
      }
    }
    return {
      accelerations: accelerations.length,
      decelerations: decelerations.length,
      avgAcceleration: accelerations.length > 0 ? accelerations.reduce((a, b) => a + b, 0) / accelerations.length : 0,
      avgDeceleration: decelerations.length > 0 ? decelerations.reduce((a, b) => a + b, 0) / decelerations.length : 0
    };
  }
  /**
   * Sumariza análise de tempo
   */
  summarizeTempoAnalysis(tempoAnalysis) {
    if (tempoAnalysis.length === 0) return {};
    const avgConsistency = tempoAnalysis.reduce((a, b) => a + b.consistency, 0) / tempoAnalysis.length;
    const avgTempo = tempoAnalysis.reduce((a, b) => a + b.averageTempo, 0) / tempoAnalysis.length;
    const totalAccelerations = tempoAnalysis.reduce((a, b) => a + (b.tempoVariations.accelerations || 0), 0);
    const totalDecelerations = tempoAnalysis.reduce((a, b) => a + (b.tempoVariations.decelerations || 0), 0);
    return {
      averageConsistency: avgConsistency,
      averageTempo: avgTempo,
      totalVariations: totalAccelerations + totalDecelerations,
      stabilityScore: Math.max(0, 100 - (totalAccelerations + totalDecelerations) * 2)
    };
  }
  /**
   * Analisa tratamento de complexidade
   */
  analyzeComplexityHandling(gameData) {
    const attempts = gameData.attempts || [];
    const complexityLevels = {};
    attempts.forEach((attempt) => {
      const complexity = this.calculateRhythmComplexity(attempt.expectedRhythm || []);
      const level = Math.floor(complexity);
      if (!complexityLevels[level]) {
        complexityLevels[level] = [];
      }
      complexityLevels[level].push({
        accuracy: this.calculateRhythmAccuracy(
          attempt.expectedRhythm || [],
          attempt.userRhythm || []
        ),
        responseTime: attempt.responseTime || 0
      });
    });
    return this.analyzeComplexityPerformance(complexityLevels);
  }
  /**
   * Analisa performance por complexidade
   */
  analyzeComplexityPerformance(complexityLevels) {
    const performance = {};
    Object.keys(complexityLevels).forEach((level) => {
      const attempts = complexityLevels[level];
      const avgAccuracy = attempts.reduce((a, b) => a + b.accuracy, 0) / attempts.length;
      const avgResponseTime = attempts.reduce((a, b) => a + b.responseTime, 0) / attempts.length;
      performance[level] = {
        attempts: attempts.length,
        averageAccuracy: avgAccuracy,
        averageResponseTime: avgResponseTime,
        efficiencyScore: avgAccuracy - avgResponseTime / 1e3
        // penalizar tempo longo
      };
    });
    return performance;
  }
  /**
   * Analisa sincronização
   */
  analyzeSynchronization(gameData) {
    const attempts = gameData.attempts || [];
    const syncAnalysis = [];
    attempts.forEach((attempt) => {
      const expectedRhythm = attempt.expectedRhythm || [];
      const userRhythm = attempt.userRhythm || [];
      const syncScore = this.calculateSynchronizationScore(expectedRhythm, userRhythm);
      const phaseShift = this.calculatePhaseShift(expectedRhythm, userRhythm);
      syncAnalysis.push({
        attemptId: attempt.id,
        syncScore,
        phaseShift,
        onset: this.analyzeOnsetTiming(expectedRhythm, userRhythm)
      });
    });
    return this.summarizeSyncAnalysis(syncAnalysis);
  }
  /**
   * Calcula score de sincronização
   */
  calculateSynchronizationScore(expected, user) {
    if (expected.length === 0 || user.length === 0) return 0;
    const maxLength = Math.max(expected.length, user.length);
    const minLength = Math.min(expected.length, user.length);
    let synchronizedBeats = 0;
    const syncWindow = 150;
    for (let i = 0; i < minLength; i++) {
      const expectedTiming = expected[i].timing || 0;
      const userTiming = user[i].timing || 0;
      if (Math.abs(expectedTiming - userTiming) <= syncWindow) {
        synchronizedBeats++;
      }
    }
    return synchronizedBeats / maxLength * 100;
  }
  /**
   * Calcula mudança de fase
   */
  calculatePhaseShift(expected, user) {
    if (expected.length === 0 || user.length === 0) return 0;
    let totalShift = 0;
    const minLength = Math.min(expected.length, user.length);
    for (let i = 0; i < minLength; i++) {
      const expectedTiming = expected[i].timing || 0;
      const userTiming = user[i].timing || 0;
      totalShift += userTiming - expectedTiming;
    }
    return totalShift / minLength;
  }
  /**
   * Analisa timing de início
   */
  analyzeOnsetTiming(expected, user) {
    if (expected.length === 0 || user.length === 0) return {};
    const expectedOnset = expected[0].timing || 0;
    const userOnset = user[0].timing || 0;
    const onsetDelay = userOnset - expectedOnset;
    return {
      delay: onsetDelay,
      accuracy: Math.max(0, 100 - Math.abs(onsetDelay) / 10)
    };
  }
  /**
   * Sumariza análise de sincronização
   */
  summarizeSyncAnalysis(syncAnalysis) {
    if (syncAnalysis.length === 0) return {};
    const avgSyncScore = syncAnalysis.reduce((a, b) => a + b.syncScore, 0) / syncAnalysis.length;
    const avgPhaseShift = syncAnalysis.reduce((a, b) => a + b.phaseShift, 0) / syncAnalysis.length;
    const avgOnsetAccuracy = syncAnalysis.reduce((a, b) => a + (b.onset.accuracy || 0), 0) / syncAnalysis.length;
    return {
      averageSyncScore: avgSyncScore,
      averagePhaseShift: avgPhaseShift,
      averageOnsetAccuracy: avgOnsetAccuracy,
      overallSyncQuality: (avgSyncScore + avgOnsetAccuracy) / 2
    };
  }
  /**
   * Atualiza métricas acumuladas
   */
  updateMetrics(rhythmData) {
    const rhythmAccuracy = rhythmData.rhythmAccuracy || {};
    const tempoAnalysis = rhythmData.tempoAnalysis || {};
    const complexityHandling = rhythmData.complexityHandling || {};
    const synchronization = rhythmData.synchronization || {};
    this.metrics.rhythmAccuracy = this.calculateRunningAverage(
      this.metrics.rhythmAccuracy,
      rhythmAccuracy.averageAccuracy || 0
    );
    this.metrics.tempoConsistency = this.calculateRunningAverage(
      this.metrics.tempoConsistency,
      tempoAnalysis.averageConsistency || 0
    );
    const complexityScores = Object.values(complexityHandling).map((level) => level.efficiencyScore || 0);
    const avgComplexityScore = complexityScores.length > 0 ? complexityScores.reduce((a, b) => a + b, 0) / complexityScores.length : 0;
    this.metrics.rhythmComplexityHandling = this.calculateRunningAverage(
      this.metrics.rhythmComplexityHandling,
      Math.max(0, avgComplexityScore)
    );
    this.metrics.syncronizationSkill = this.calculateRunningAverage(
      this.metrics.syncronizationSkill,
      synchronization.overallSyncQuality || 0
    );
  }
  /**
   * Calcula média móvel
   */
  calculateRunningAverage(current, newValue) {
    return current * 0.8 + newValue * 0.2;
  }
  /**
   * Método de coleta padrão
   */
  async collect(gameData) {
    return await this.collectRhythmData(gameData);
  }
  /**
   * Análise principal
   */
  async analyze(gameData) {
    return await this.collectRhythmData(gameData);
  }
  /**
   * Obtém métricas atuais
   */
  getMetrics() {
    return { ...this.metrics };
  }
  /**
   * Obtém histórico de coleta
   */
  getCollectionHistory() {
    return [...this.collectionHistory];
  }
  /**
   * Reset do coletor
   */
  reset() {
    this.metrics = {
      rhythmAccuracy: 0,
      tempoConsistency: 0,
      rhythmComplexityHandling: 0,
      syncronizationSkill: 0
    };
    this.collectionHistory = [];
    this.patterns = {
      rhythmPatterns: [],
      tempoVariations: [],
      syncEvents: []
    };
  }
}
class ErrorPatternCollector {
  constructor() {
    this.name = "MusicalSequenceErrorPatternCollector";
    this.description = "Coleta padrões de erros no MusicalSequence";
    this.version = "1.0.0";
    this.isActive = true;
    this.collectedData = [];
    this.errorData = {
      sequenceErrors: {},
      rhythmErrors: [],
      pitchErrors: [],
      timingErrors: [],
      auditoryProcessingErrors: [],
      persistentErrors: {},
      errorClusters: [],
      learningIndicators: [],
      musicalPatterns: {}
    };
    this.sessionStartTime = Date.now();
    this.errorThresholds = {
      persistent: 3,
      cluster: 5,
      severity: {
        low: 0.3,
        medium: 0.6,
        high: 0.8
      }
    };
    console.log(`🎵 ${this.name} v${this.version} inicializado`);
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de erros
   */
  collect(gameData) {
    if (!gameData) {
      console.warn("MusicalSequenceErrorPatternCollector: Dados do jogo não fornecidos para análise");
      return { errors: [], patterns: [], metrics: {} };
    }
    console.log(`📊 MusicalSequenceErrorPatternCollector: Analisando dados da sessão ${gameData.sessionId || "sem ID"}`);
    try {
      const errorMetrics = this.analyzeErrorPatterns(gameData);
      const errors = [];
      if (gameData.attemptHistory && Array.isArray(gameData.attemptHistory)) {
        gameData.attemptHistory.forEach((attempt, index) => {
          if (!attempt.isCorrect && attempt.targetSequence && attempt.playedSequence) {
            const sequenceError = this.collectSequenceError(
              attempt.targetSequence,
              attempt.playedSequence,
              {
                difficulty: gameData.difficulty || "medium",
                responseTime: attempt.responseTime || 0,
                attemptNumber: index
              }
            );
            if (sequenceError) errors.push(sequenceError);
          }
        });
      }
      if (gameData.rhythmHistory && Array.isArray(gameData.rhythmHistory)) {
        gameData.rhythmHistory.forEach((rhythm) => {
          if (rhythm.target && rhythm.actual && rhythm.target !== rhythm.actual) {
            const rhythmError = this.collectRhythmError(
              rhythm.target,
              rhythm.actual,
              {
                rhythmType: rhythm.type || "standard",
                difficulty: gameData.difficulty || "medium"
              }
            );
            if (rhythmError) errors.push(rhythmError);
          }
        });
      }
      const collectedMetric = {
        timestamp: Date.now(),
        type: "error_pattern",
        gameType: "MusicalSequence",
        data: errorMetrics,
        errors,
        sessionData: {
          sessionId: gameData.sessionId,
          level: gameData.level || 1,
          attempt: gameData.attempt || 1
        }
      };
      this.collectedData.push(collectedMetric);
      this.categorizeErrors(errorMetrics);
      return {
        errors,
        patterns: errorMetrics,
        metrics: this.generateErrorMetrics(gameData)
      };
    } catch (error) {
      console.error("❌ Erro ao coletar padrões de erro (MusicalSequence):", error);
      return { errors: [], patterns: [], metrics: {}, error: error.message };
    }
  }
  analyzeErrorPatterns(gameData) {
    const patterns = {
      sequenceErrors: this.detectSequenceErrors(gameData),
      rhythmErrors: this.detectRhythmErrors(gameData),
      pitchErrors: this.detectPitchErrors(gameData),
      timingErrors: this.detectTimingErrors(gameData),
      severity: this.calculateOverallSeverity(gameData)
    };
    return patterns;
  }
  detectSequenceErrors(gameData) {
    return [];
  }
  detectRhythmErrors(gameData) {
    return [];
  }
  detectPitchErrors(gameData) {
    return [];
  }
  detectTimingErrors(gameData) {
    return [];
  }
  calculateOverallSeverity(gameData) {
    return "low";
  }
  categorizeErrors(errorMetrics) {
  }
  /**
   * Coleta erros de sequência musical
   */
  collectSequenceError(targetSequence, playedSequence, context) {
    const errorKey = `seq_${targetSequence.length}`;
    const sequenceError = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      targetSequence,
      playedSequence,
      errorType: this.identifySequenceErrorType(targetSequence, playedSequence),
      context: {
        difficulty: context.difficulty || "medium",
        responseTime: context.responseTime || 0,
        attemptNumber: context.attemptNumber || 0
      },
      severity: this.calculateSequenceErrorSeverity(targetSequence, playedSequence, context),
      matchAccuracy: this.calculateSequenceMatchAccuracy(targetSequence, playedSequence)
    };
    if (!this.errorData.sequenceErrors[errorKey]) {
      this.errorData.sequenceErrors[errorKey] = [];
    }
    this.errorData.sequenceErrors[errorKey].push(sequenceError);
    return sequenceError;
  }
  /**
   * Coleta erros rítmicos
   */
  collectRhythmError(targetRhythm, actualRhythm, context) {
    const rhythmError = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      targetRhythm,
      actualRhythm,
      errorType: this.identifyRhythmErrorType(targetRhythm, actualRhythm),
      context: {
        rhythmType: context.rhythmType || "standard",
        difficulty: context.difficulty || "medium"
      },
      severity: this.calculateRhythmErrorSeverity(targetRhythm, actualRhythm, context),
      timingAccuracy: this.calculateTimingAccuracy(targetRhythm, actualRhythm)
    };
    this.errorData.rhythmErrors.push(rhythmError);
    return rhythmError;
  }
  /**
   * Identifica o tipo de erro de sequência musical
   */
  identifySequenceErrorType(target, played) {
    if (!played || played.length === 0) return "no_response";
    if (target.length !== played.length) {
      return target.length > played.length ? "incomplete_sequence" : "added_notes";
    }
    if (this.hasSameElements(target, played) && !this.areSequencesEqual(target, played)) {
      return "order_error";
    }
    if (!this.hasSameElements(target, played)) {
      return "note_substitution";
    }
    if (this.hasTimingIssues(target, played)) {
      return "timing_error";
    }
    return "general_sequence_error";
  }
  /**
   * Identifica o tipo de erro rítmico
   */
  identifyRhythmErrorType(target, actual) {
    return "rhythm_pattern_error";
  }
  /**
   * Verifica se duas sequências têm os mesmos elementos (independente da ordem)
   */
  hasSameElements(seq1, seq2) {
    if (seq1.length !== seq2.length) return false;
    const count1 = this.countElements(seq1);
    const count2 = this.countElements(seq2);
    for (const key in count1) {
      if (count1[key] !== count2[key]) return false;
    }
    return true;
  }
  /**
   * Conta a ocorrência de cada elemento em uma sequência
   */
  countElements(seq) {
    const counts = {};
    seq.forEach((elem) => {
      counts[elem] = (counts[elem] || 0) + 1;
    });
    return counts;
  }
  /**
   * Verifica se duas sequências são iguais (mesmos elementos na mesma ordem)
   */
  areSequencesEqual(seq1, seq2) {
    if (seq1.length !== seq2.length) return false;
    for (let i = 0; i < seq1.length; i++) {
      if (seq1[i] !== seq2[i]) return false;
    }
    return true;
  }
  /**
   * Verifica se há problemas de timing na sequência
   */
  hasTimingIssues(target, played) {
    return false;
  }
  /**
   * Calcula a precisão de correspondência entre duas sequências
   */
  calculateSequenceMatchAccuracy(target, played) {
    if (!played || played.length === 0) return 0;
    if (target.length === 0) return 0;
    let correctCount = 0;
    const maxLength = Math.max(target.length, played.length);
    for (let i = 0; i < maxLength; i++) {
      if (i < target.length && i < played.length && target[i] === played[i]) {
        correctCount++;
      }
    }
    return correctCount / maxLength;
  }
  /**
   * Calcula a precisão de timing
   */
  calculateTimingAccuracy(target, actual) {
    return 0.7;
  }
  /**
   * Calcula a severidade do erro de sequência
   */
  calculateSequenceErrorSeverity(target, played, context) {
    let severity = 0.5;
    const accuracy = this.calculateSequenceMatchAccuracy(target, played);
    severity += (1 - accuracy) * 0.3;
    if (target.length > 5) {
      severity -= 0.1;
    }
    if (context.responseTime > 5e3) severity += 0.1;
    if (context.responseTime < 500) severity += 0.1;
    if (context.difficulty === "hard") severity -= 0.1;
    return Math.min(Math.max(severity, 0), 1);
  }
  /**
   * Calcula a severidade do erro rítmico
   */
  calculateRhythmErrorSeverity(target, actual, context) {
    return 0.6;
  }
  /**
   * Gera métricas de erro com base nos dados coletados
   */
  generateErrorMetrics(gameData) {
    const sequenceErrorCount = Object.values(this.errorData.sequenceErrors).reduce(
      (total, errors) => total + errors.length,
      0
    );
    return {
      totalErrors: sequenceErrorCount + this.errorData.rhythmErrors.length,
      uniqueSequenceErrors: Object.keys(this.errorData.sequenceErrors).length,
      mostCommonError: this.findMostCommonError(),
      averageSeverity: this.calculateAverageSeverity(),
      auditoryProcessingScore: this.calculateAuditoryProcessingScore(gameData),
      sequentialMemoryScore: this.calculateSequentialMemoryScore(gameData),
      improvement: this.calculateImprovementMetric(gameData)
    };
  }
  /**
   * Encontra o erro mais comum
   */
  findMostCommonError() {
    let maxCount = 0;
    let mostCommonError = null;
    Object.entries(this.errorData.sequenceErrors).forEach(([errorKey, errors]) => {
      if (errors.length > maxCount) {
        maxCount = errors.length;
        mostCommonError = errorKey;
      }
    });
    return {
      error: mostCommonError,
      count: maxCount
    };
  }
  /**
   * Calcula a severidade média dos erros
   */
  calculateAverageSeverity() {
    let totalSeverity = 0;
    let errorCount = 0;
    Object.values(this.errorData.sequenceErrors).forEach((errors) => {
      errors.forEach((error) => {
        totalSeverity += error.severity;
        errorCount++;
      });
    });
    this.errorData.rhythmErrors.forEach((error) => {
      totalSeverity += error.severity;
      errorCount++;
    });
    return errorCount > 0 ? totalSeverity / errorCount : 0;
  }
  /**
   * Calcula pontuação de processamento auditivo
   */
  calculateAuditoryProcessingScore(gameData) {
    return 0.7;
  }
  /**
   * Calcula pontuação de memória sequencial
   */
  calculateSequentialMemoryScore(gameData) {
    return 0.6;
  }
  /**
   * Calcula métrica de melhoria ao longo do tempo
   */
  calculateImprovementMetric(gameData) {
    return 0.5;
  }
  /**
   * Método de análise para compatibilidade com outros coletores
   */
  analyze(gameData) {
    return this.collect(gameData);
  }
}
class MusicalSequenceCollectorsHub {
  constructor() {
    this.auditoryMemoryCollector = new AuditoryMemoryCollector();
    this.musicalPatternCollector = new MusicalPatternCollector();
    this.sequenceExecutionCollector = new SequenceExecutionCollector();
    this.musicalLearningCollector = new MusicalLearningCollector();
    this.rhythmPatternCollector = new RhythmPatternCollector();
    this.errorPatternCollector = new ErrorPatternCollector();
    this._collectors = {
      auditoryMemory: this.auditoryMemoryCollector,
      musicalPattern: this.musicalPatternCollector,
      sequenceExecution: this.sequenceExecutionCollector,
      musicalLearning: this.musicalLearningCollector,
      rhythmPattern: this.rhythmPatternCollector,
      errorPattern: this.errorPatternCollector
    };
    this.isEnabled = true;
    this.activeCollectors = /* @__PURE__ */ new Set(["memory", "pattern", "execution", "learning"]);
    this.sessionData = {
      sessionId: null,
      startTime: null,
      gameConfig: null
    };
    this.integratedAnalysis = {};
    this.crossCollectorMetrics = {};
    this.debugMode = true;
    if (this.debugMode) {
      console.log("🎵 MusicalSequenceCollectorsHub inicializado com coletores:", Array.from(this.activeCollectors));
    }
  }
  /**
   * Getter para coletores - necessário para GameSpecificProcessors
   */
  get collectors() {
    return this._collectors;
  }
  /**
   * Inicializa uma nova sessão de coleta
   */
  startSession(sessionConfig) {
    try {
      this.sessionData = {
        sessionId: sessionConfig.sessionId || `musical_${Date.now()}`,
        startTime: Date.now(),
        gameConfig: sessionConfig.gameConfig || {},
        difficulty: sessionConfig.difficulty || "medium",
        playerProfile: sessionConfig.playerProfile || {}
      };
      this.clearAllData();
      this.configureCollectors(sessionConfig);
      if (this.debugMode) {
        console.log("🎵 MusicalSequenceCollectorsHub - Sessão iniciada:", this.sessionData);
      }
      return {
        success: true,
        sessionId: this.sessionData.sessionId,
        activeCollectors: Array.from(this.activeCollectors)
      };
    } catch (error) {
      console.error("Erro ao iniciar sessão do MusicalSequenceCollectorsHub:", error);
      return { success: false, error: error.message };
    }
  }
  /**
   * Processa uma interação do jogador com coleta integrada
   */
  processInteraction(interactionData) {
    try {
      if (!this.isEnabled || !interactionData) {
        console.warn("Hub desabilitado ou dados inválidos");
        return null;
      }
      const enrichedData = this.enrichInteractionData(interactionData);
      const collectionResults = {};
      if (this.activeCollectors.has("memory")) {
        collectionResults.memory = this.collectMemoryData(enrichedData);
      }
      if (this.activeCollectors.has("pattern")) {
        collectionResults.pattern = this.collectPatternData(enrichedData);
      }
      if (this.activeCollectors.has("execution")) {
        collectionResults.execution = this.collectExecutionData(enrichedData);
      }
      if (this.activeCollectors.has("learning")) {
        collectionResults.learning = this.collectLearningData(enrichedData);
      }
      const integratedResults = this.performIntegratedAnalysis(collectionResults, enrichedData);
      this.updateCrossCollectorMetrics(collectionResults);
      if (this.debugMode) {
        console.log("🎵 MusicalSequenceCollectorsHub - Interação processada:", {
          collectors: Object.keys(collectionResults),
          integratedInsights: Object.keys(integratedResults)
        });
      }
      return {
        timestamp: enrichedData.timestamp,
        sessionId: this.sessionData.sessionId,
        collectionResults,
        integratedResults,
        summary: this.generateInteractionSummary(collectionResults, integratedResults)
      };
    } catch (error) {
      console.error("Erro no processamento da interação:", error);
      return null;
    }
  }
  /**
   * Coleta dados específicos de memória auditiva
   */
  collectMemoryData(data) {
    try {
      const memoryResults = {};
      if (data.sequence && data.playerResponse) {
        memoryResults.retention = this.auditoryMemoryCollector.collectMemoryRetention({
          sequence: data.sequence,
          playerResponse: data.playerResponse,
          sequenceLength: data.sequence.length,
          responseTime: data.responseTime || 0,
          isCorrect: data.isCorrect || false,
          partialCorrect: data.partialCorrect || 0,
          attemptNumber: data.attemptNumber || 1,
          difficulty: data.difficulty || this.sessionData.difficulty,
          timestamp: data.timestamp
        });
      }
      if (data.sequence && data.timeBetweenNotes) {
        memoryResults.temporal = this.auditoryMemoryCollector.analyzeTemporalSequence({
          sequence: data.sequence,
          playerResponse: data.playerResponse || [],
          timeBetweenNotes: data.timeBetweenNotes,
          difficulty: data.difficulty || this.sessionData.difficulty,
          timestamp: data.timestamp
        });
      }
      if (data.maxCorrectSequence !== void 0) {
        memoryResults.span = this.auditoryMemoryCollector.collectAuditorySpan({
          maxCorrectSequence: data.maxCorrectSequence,
          consistentSpan: data.consistentSpan || 0,
          improvementRate: data.improvementRate || 0,
          difficulty: data.difficulty || this.sessionData.difficulty,
          sessionDuration: Date.now() - this.sessionData.startTime,
          timestamp: data.timestamp
        });
      }
      return memoryResults;
    } catch (error) {
      console.error("Erro na coleta de dados de memória:", error);
      return {};
    }
  }
  /**
   * Coleta dados de padrões musicais
   */
  collectPatternData(data) {
    try {
      const patternResults = {};
      if (data.sequence && data.instruments) {
        patternResults.melodic = this.musicalPatternCollector.collectMelodicPattern({
          sequence: data.sequence,
          playerResponse: data.playerResponse || [],
          instruments: data.instruments,
          difficulty: data.difficulty || this.sessionData.difficulty,
          isCorrect: data.isCorrect || false,
          responseTime: data.responseTime || 0,
          sequenceType: data.sequenceType || "random",
          timestamp: data.timestamp
        });
      }
      if (data.timings || data.expectedTimings) {
        patternResults.rhythmic = this.musicalPatternCollector.analyzeRhythmicPattern({
          sequence: data.sequence || [],
          playerResponse: data.playerResponse || [],
          timings: data.timings || [],
          expectedTimings: data.expectedTimings || [],
          tempo: data.tempo || 120,
          difficulty: data.difficulty || this.sessionData.difficulty,
          timestamp: data.timestamp
        });
      }
      if (data.instrument) {
        patternResults.instrument = this.musicalPatternCollector.collectInstrumentPreferences({
          instrument: data.instrument,
          correctRecognition: data.correctInstrumentRecognition || false,
          responseTime: data.instrumentResponseTime || 0,
          confidence: data.confidence || 0,
          frequency: data.instrumentFrequency || 0,
          context: data.context || "sequence",
          timestamp: data.timestamp
        });
      }
      if (data.simultaneousNotes || data.chordProgression) {
        patternResults.harmonic = this.musicalPatternCollector.analyzeHarmonicPattern({
          sequence: data.sequence || [],
          simultaneousNotes: data.simultaneousNotes || [],
          chordProgression: data.chordProgression || [],
          playerResponse: data.playerResponse || [],
          difficulty: data.difficulty || this.sessionData.difficulty,
          timestamp: data.timestamp
        });
      }
      return patternResults;
    } catch (error) {
      console.error("Erro na coleta de dados de padrões:", error);
      return {};
    }
  }
  /**
   * Coleta dados de execução de sequência
   */
  collectExecutionData(data) {
    try {
      const executionResults = {};
      if (data.sequence && data.playerResponse) {
        executionResults.execution = this.sequenceExecutionCollector.collectExecution({
          sequence: data.sequence,
          playerResponse: data.playerResponse,
          executionTimes: data.executionTimes || [],
          expectedTimes: data.expectedTimes || [],
          pressureLevels: data.pressureLevels || [],
          coordinationScores: data.coordinationScores || [],
          isCorrect: data.isCorrect || false,
          totalExecutionTime: data.totalExecutionTime || 0,
          difficulty: data.difficulty || this.sessionData.difficulty,
          timestamp: data.timestamp
        });
      }
      if (data.interClickIntervals || data.executionTimes) {
        executionResults.timing = this.sequenceExecutionCollector.analyzeExecutionTiming({
          interClickIntervals: data.interClickIntervals || [],
          expectedIntervals: data.expectedIntervals || [],
          totalDuration: data.totalExecutionTime || 0,
          tempo: data.tempo || 120,
          sequenceLength: data.sequence?.length || 0,
          timestamp: data.timestamp
        });
      }
      if (data.clickPrecision || data.pressureDynamics) {
        executionResults.motor = this.sequenceExecutionCollector.collectMotorControl({
          clickPrecision: data.clickPrecision || [],
          pressureDynamics: data.pressureDynamics || [],
          movementSmoothness: data.movementSmoothness || 0,
          coordinationIndex: data.coordinationIndex || 0,
          fatigue: data.fatigue || 0,
          handedness: data.handedness || "right",
          timestamp: data.timestamp
        });
      }
      if (data.sequence) {
        executionResults.strategy = this.sequenceExecutionCollector.identifyExecutionStrategies({
          sequence: data.sequence,
          playerResponse: data.playerResponse || [],
          executionTimes: data.executionTimes || [],
          repeatRequests: data.repeatRequests || 0,
          pauseLocations: data.pauseLocations || [],
          chunkingPatterns: data.chunkingPatterns || [],
          difficulty: data.difficulty || this.sessionData.difficulty,
          timestamp: data.timestamp
        });
      }
      if (data.errors || data.sequence && data.playerResponse) {
        executionResults.errors = this.sequenceExecutionCollector.analyzeErrorPatterns({
          sequence: data.sequence || [],
          playerResponse: data.playerResponse || [],
          errorLocations: data.errorLocations || [],
          errorTypes: data.errorTypes || [],
          recoveryTimes: data.recoveryTimes || [],
          repetitionErrors: data.repetitionErrors || [],
          timestamp: data.timestamp
        });
      }
      return executionResults;
    } catch (error) {
      console.error("Erro na coleta de dados de execução:", error);
      return {};
    }
  }
  /**
   * Coleta dados de aprendizado musical
   */
  collectLearningData(data) {
    try {
      const learningResults = {};
      if (data.currentLevel !== void 0) {
        learningResults.progression = this.musicalLearningCollector.collectLearningProgression({
          sessionNumber: data.sessionNumber || 1,
          difficulty: data.difficulty || this.sessionData.difficulty,
          currentLevel: data.currentLevel,
          previousLevel: data.previousLevel || data.currentLevel,
          accuracy: data.accuracy || 0,
          speed: data.speed || 0,
          consistency: data.consistency || 0,
          sequencesCompleted: data.sequencesCompleted || 0,
          errorsCommitted: data.errorsCommitted || 0,
          improvementRate: data.improvementRate || 0,
          challengesSolved: data.challengesSolved || 0,
          timestamp: data.timestamp
        });
      }
      if (data.timeSinceLastSession !== void 0) {
        learningResults.retention = this.musicalLearningCollector.analyzeRetentionPatterns({
          timeSinceLastSession: data.timeSinceLastSession,
          initialPerformance: data.initialPerformance || 0,
          returnPerformance: data.returnPerformance || data.accuracy || 0,
          previousMastery: data.previousMastery || 0,
          currentMastery: data.currentMastery || 0,
          forgettingCurve: data.forgettingCurve || [],
          practiceFrequency: data.practiceFrequency || 0,
          difficulty: data.difficulty || this.sessionData.difficulty,
          timestamp: data.timestamp
        });
      }
      if (data.sourceSkill && data.targetSkill) {
        learningResults.transfer = this.musicalLearningCollector.collectTransferLearning({
          sourceSkill: data.sourceSkill,
          targetSkill: data.targetSkill,
          sourcePerformance: data.sourcePerformance || 0,
          targetPerformance: data.targetPerformance || data.accuracy || 0,
          transferDistance: data.transferDistance || 0,
          trainingTime: data.trainingTime || 0,
          transferEffectiveness: data.transferEffectiveness || 0,
          skillSimilarity: data.skillSimilarity || 0,
          timestamp: data.timestamp
        });
      }
      if (data.newChallenge) {
        learningResults.adaptation = this.musicalLearningCollector.analyzeAdaptation({
          newChallenge: data.newChallenge,
          challengeDifficulty: data.challengeDifficulty || 0,
          adaptationTime: data.adaptationTime || 0,
          initialPerformance: data.initialPerformance || 0,
          adaptedPerformance: data.adaptedPerformance || data.accuracy || 0,
          strategiesUsed: data.strategiesUsed || [],
          flexibilityScore: data.flexibilityScore || 0,
          innovationLevel: data.innovationLevel || 0,
          timestamp: data.timestamp
        });
      }
      if (data.skill) {
        learningResults.mastery = this.musicalLearningCollector.assessMastery({
          skill: data.skill,
          currentLevel: data.currentLevel || 0,
          consistency: data.consistency || 0,
          automaticity: data.automaticity || 0,
          flexibility: data.flexibility || 0,
          creativity: data.creativity || 0,
          transferability: data.transferability || 0,
          complexity: data.complexity || 0,
          fluency: data.fluency || 0,
          timestamp: data.timestamp
        });
      }
      if (data.strategy) {
        learningResults.strategies = this.musicalLearningCollector.analyzeLearningStrategies({
          strategy: data.strategy,
          context: data.context || "musical_sequence",
          effectiveness: data.strategyEffectiveness || 0,
          usageFrequency: data.strategyUsageFrequency || 0,
          outcomeImprovement: data.outcomeImprovement || 0,
          timeInvestment: data.timeInvestment || 0,
          difficulty: data.difficulty || this.sessionData.difficulty,
          timestamp: data.timestamp
        });
      }
      return learningResults;
    } catch (error) {
      console.error("Erro na coleta de dados de aprendizado:", error);
      return {};
    }
  }
  /**
   * Realiza análise integrada entre todos os coletores
   */
  performIntegratedAnalysis(collectionResults, originalData) {
    try {
      const integratedAnalysis = {};
      if (collectionResults.memory && collectionResults.execution) {
        integratedAnalysis.memoryExecution = this.analyzeMemoryExecutionCorrelation(
          collectionResults.memory,
          collectionResults.execution
        );
      }
      if (collectionResults.pattern && collectionResults.learning) {
        integratedAnalysis.patternLearning = this.analyzePatternLearningCorrelation(
          collectionResults.pattern,
          collectionResults.learning
        );
      }
      if (collectionResults.memory && collectionResults.pattern && collectionResults.execution) {
        integratedAnalysis.cognitive = this.performCognitiveAnalysis(
          collectionResults.memory,
          collectionResults.pattern,
          collectionResults.execution
        );
      }
      if (collectionResults.learning && collectionResults.execution) {
        integratedAnalysis.performance = this.predictPerformanceProgression(
          collectionResults.learning,
          collectionResults.execution,
          originalData
        );
      }
      integratedAnalysis.recommendations = this.generatePersonalizedRecommendations(
        collectionResults,
        originalData
      );
      return integratedAnalysis;
    } catch (error) {
      console.error("Erro na análise integrada:", error);
      return {};
    }
  }
  /**
   * Enriquece dados da interação com contexto da sessão
   */
  enrichInteractionData(data) {
    return {
      ...data,
      timestamp: data.timestamp || Date.now(),
      sessionId: this.sessionData.sessionId,
      sessionDuration: Date.now() - this.sessionData.startTime,
      difficulty: data.difficulty || this.sessionData.difficulty,
      gameConfig: { ...this.sessionData.gameConfig, ...data.gameConfig },
      playerProfile: { ...this.sessionData.playerProfile, ...data.playerProfile }
    };
  }
  /**
   * Configura coletores baseado na configuração da sessão
   */
  configureCollectors(sessionConfig) {
    if (sessionConfig.enabledCollectors) {
      this.activeCollectors = new Set(sessionConfig.enabledCollectors);
    }
    if (sessionConfig.memoryConfig) {
      this.auditoryMemoryCollector.configure?.(sessionConfig.memoryConfig);
    }
    if (sessionConfig.patternConfig) {
      this.musicalPatternCollector.configure?.(sessionConfig.patternConfig);
    }
    if (sessionConfig.executionConfig) {
      this.sequenceExecutionCollector.configure?.(sessionConfig.executionConfig);
    }
    if (sessionConfig.learningConfig) {
      this.musicalLearningCollector.configure?.(sessionConfig.learningConfig);
    }
  }
  /**
   * Análises correlacionais específicas
   */
  analyzeMemoryExecutionCorrelation(memoryData, executionData) {
    try {
      const correlation = {};
      if (memoryData.span && executionData.execution) {
        correlation.spanPrecision = {
          memorySpan: memoryData.span.maxSpan || 0,
          executionPrecision: executionData.execution.executionAccuracy?.overallScore || 0,
          correlation: this.calculateCorrelation(
            memoryData.span.maxSpan || 0,
            executionData.execution.executionAccuracy?.overallScore || 0
          )
        };
      }
      if (memoryData.temporal && executionData.timing) {
        correlation.temporalTiming = {
          temporalAccuracy: memoryData.temporal.temporalOrder?.orderAccuracy || 0,
          timingPrecision: executionData.timing.rhythmicAccuracy || 0,
          correlation: this.calculateCorrelation(
            memoryData.temporal.temporalOrder?.orderAccuracy || 0,
            executionData.timing.rhythmicAccuracy || 0
          )
        };
      }
      return correlation;
    } catch (error) {
      console.error("Erro na análise de correlação memória-execução:", error);
      return {};
    }
  }
  analyzePatternLearningCorrelation(patternData, learningData) {
    try {
      const correlation = {};
      if (patternData.melodic && learningData.progression) {
        correlation.patternProgression = {
          patternRecognition: patternData.melodic.reproductionAccuracy?.overallScore || 0,
          learningRate: learningData.progression.improvementRate || 0,
          correlation: this.calculateCorrelation(
            patternData.melodic.reproductionAccuracy?.overallScore || 0,
            learningData.progression.improvementRate || 0
          )
        };
      }
      if (patternData.instrument && learningData.adaptation) {
        correlation.instrumentAdaptation = {
          instrumentAccuracy: patternData.instrument.recognitionAccuracy || 0,
          adaptationSpeed: learningData.adaptation.adaptationMetrics?.adaptationSpeed || 0,
          correlation: this.calculateCorrelation(
            patternData.instrument.recognitionAccuracy || 0,
            learningData.adaptation.adaptationMetrics?.adaptationSpeed || 0
          )
        };
      }
      return correlation;
    } catch (error) {
      console.error("Erro na análise de correlação padrão-aprendizado:", error);
      return {};
    }
  }
  performCognitiveAnalysis(memoryData, patternData, executionData) {
    try {
      const cognitiveProfile = {
        workingMemory: this.assessWorkingMemory(memoryData),
        patternRecognition: this.assessPatternRecognition(patternData),
        motorControl: this.assessMotorControl(executionData),
        cognitiveLoad: this.calculateCognitiveLoad(memoryData, patternData, executionData),
        processingSpeed: this.assessProcessingSpeed(executionData),
        attentionalControl: this.assessAttentionalControl(memoryData, executionData)
      };
      cognitiveProfile.overallProfile = this.createCognitiveProfile(cognitiveProfile);
      return cognitiveProfile;
    } catch (error) {
      console.error("Erro na análise cognitiva:", error);
      return {};
    }
  }
  predictPerformanceProgression(learningData, executionData, originalData) {
    try {
      const predictions = {};
      if (learningData.progression) {
        predictions.accuracyProgression = this.predictAccuracyProgression(
          learningData.progression,
          originalData.accuracy || 0
        );
      }
      if (learningData.mastery) {
        predictions.masteryTimeline = this.predictMasteryTimeline(
          learningData.mastery,
          learningData.progression
        );
      }
      if (executionData.errors) {
        predictions.futureChallenes = this.predictFutureChallenges(
          executionData.errors,
          learningData.progression
        );
      }
      return predictions;
    } catch (error) {
      console.error("Erro na predição de performance:", error);
      return {};
    }
  }
  generatePersonalizedRecommendations(collectionResults, originalData) {
    try {
      const recommendations = [];
      if (collectionResults.memory?.span) {
        const span = collectionResults.memory.span;
        if (span.maxSpan < 4) {
          recommendations.push({
            type: "memory_improvement",
            priority: "high",
            message: "Pratique com sequências menores para fortalecer a memória auditiva",
            targetArea: "auditory_memory"
          });
        }
      }
      if (collectionResults.pattern?.melodic) {
        const melodic = collectionResults.pattern.melodic;
        if (melodic.patternComplexity < 0.5) {
          recommendations.push({
            type: "pattern_enhancement",
            priority: "medium",
            message: "Explore padrões musicais mais complexos para desenvolver reconhecimento",
            targetArea: "pattern_recognition"
          });
        }
      }
      if (collectionResults.execution?.execution) {
        const execution = collectionResults.execution.execution;
        if (execution.timingPrecision?.precision < 0.7) {
          recommendations.push({
            type: "timing_improvement",
            priority: "high",
            message: "Foque na precisão do timing - use metrônomo mental ou contagem",
            targetArea: "timing_precision"
          });
        }
      }
      if (collectionResults.learning?.progression) {
        const progression = collectionResults.learning.progression;
        if (progression.plateauDetection?.plateauDetected) {
          recommendations.push({
            type: "plateau_breakthrough",
            priority: "high",
            message: "Varie os tipos de sequência ou aumente a dificuldade para superar o platô",
            targetArea: "learning_progression"
          });
        }
      }
      const integratedRecs = this.generateIntegratedRecommendations(collectionResults);
      recommendations.push(...integratedRecs);
      return recommendations.slice(0, 5);
    } catch (error) {
      console.error("Erro na geração de recomendações:", error);
      return [];
    }
  }
  generateIntegratedRecommendations(collectionResults) {
    const recommendations = [];
    const hasMemoryIssues = collectionResults.memory?.span?.maxSpan < 4;
    const hasTimingIssues = collectionResults.execution?.timing?.rhythmicAccuracy < 0.6;
    const hasPatternIssues = collectionResults.pattern?.melodic?.reproductionAccuracy?.accuracy < 0.6;
    if (hasMemoryIssues && hasTimingIssues) {
      recommendations.push({
        type: "integrated_improvement",
        priority: "high",
        message: "Pratique sequências curtas com foco no ritmo para melhorar memória e timing simultaneamente",
        targetArea: "memory_timing"
      });
    }
    if (hasPatternIssues && hasMemoryIssues) {
      recommendations.push({
        type: "pattern_memory_boost",
        priority: "medium",
        message: "Use técnicas de agrupamento (chunking) para melhorar tanto padrões quanto memória",
        targetArea: "pattern_memory"
      });
    }
    return recommendations;
  }
  /**
   * Atualiza métricas cross-collector
   */
  updateCrossCollectorMetrics(collectionResults) {
    try {
      const metrics = this.crossCollectorMetrics;
      metrics.totalInteractions = (metrics.totalInteractions || 0) + 1;
      metrics.lastUpdate = Date.now();
      if (collectionResults.memory && collectionResults.execution) {
        metrics.memoryExecutionSynergy = this.calculateSynergy(
          collectionResults.memory,
          collectionResults.execution
        );
      }
      if (collectionResults.pattern && collectionResults.learning) {
        metrics.patternLearningSynergy = this.calculateSynergy(
          collectionResults.pattern,
          collectionResults.learning
        );
      }
      metrics.collectorConsistency = this.calculateCollectorConsistency(collectionResults);
    } catch (error) {
      console.error("Erro ao atualizar métricas cross-collector:", error);
    }
  }
  /**
   * Métodos auxiliares para análise
   */
  calculateCorrelation(value1, value2) {
    if (value1 === 0 || value2 === 0) return 0;
    const diff = Math.abs(value1 - value2);
    return Math.max(0, 1 - diff);
  }
  assessWorkingMemory(memoryData) {
    const spanScore = memoryData.span?.maxSpan || 0;
    const retentionScore = memoryData.retention?.retentionRate || 0;
    return (spanScore / 8 + retentionScore) / 2;
  }
  assessPatternRecognition(patternData) {
    const melodicScore = patternData.melodic?.reproductionAccuracy?.accuracy || 0;
    const rhythmicScore = patternData.rhythmic?.rhythmicAccuracy || 0;
    return (melodicScore + rhythmicScore) / 2;
  }
  assessMotorControl(executionData) {
    const precisionScore = executionData.execution?.timingPrecision?.precision || 0;
    const smoothnessScore = executionData.execution?.executionFlow?.smoothness || 0;
    return (precisionScore + smoothnessScore) / 2;
  }
  calculateCognitiveLoad(memoryData, patternData, executionData) {
    const memoryLoad = (memoryData.span?.maxSpan || 0) / 8;
    const patternLoad = patternData.melodic?.patternComplexity || 0;
    const executionLoad = 1 - (executionData.execution?.consistency || 0);
    return (memoryLoad + patternLoad + executionLoad) / 3;
  }
  assessProcessingSpeed(executionData) {
    const responseTime = executionData.execution?.responseLatency || 1e3;
    return Math.max(0, 1 - responseTime / 2e3);
  }
  assessAttentionalControl(memoryData, executionData) {
    const memoryConsistency = memoryData.retention?.consolidationLevel || 0;
    const executionConsistency = executionData.execution?.consistency || 0;
    return (memoryConsistency + executionConsistency) / 2;
  }
  createCognitiveProfile(cognitiveData) {
    const scores = Object.values(cognitiveData).filter((v) => typeof v === "number");
    const overallScore = scores.reduce((a, b) => a + b, 0) / scores.length;
    let profileType = "balanced";
    if (cognitiveData.workingMemory > 0.8) profileType = "memory_strength";
    if (cognitiveData.patternRecognition > 0.8) profileType = "pattern_strength";
    if (cognitiveData.motorControl > 0.8) profileType = "motor_strength";
    if (cognitiveData.processingSpeed > 0.8) profileType = "speed_strength";
    return { overallScore, profileType, strengths: this.identifyProfileStrengths(cognitiveData) };
  }
  identifyProfileStrengths(cognitiveData) {
    const threshold = 0.7;
    return Object.entries(cognitiveData).filter(([key, value]) => typeof value === "number" && value >= threshold).map(([key]) => key);
  }
  predictAccuracyProgression(progressionData, currentAccuracy) {
    const improvementRate = progressionData.improvementRate || 0;
    const projectedAccuracy = Math.min(currentAccuracy + improvementRate * 0.1, 1);
    return {
      currentAccuracy,
      projectedAccuracy,
      timeToTarget: improvementRate > 0 ? (0.9 - currentAccuracy) / improvementRate : Infinity,
      confidence: Math.min(improvementRate * 2, 1)
    };
  }
  predictMasteryTimeline(masteryData, progressionData) {
    const currentMastery = masteryData.masteryLevel || 0;
    const progressionRate = progressionData?.improvementRate || 0;
    return {
      currentMasteryLevel: currentMastery,
      timeToMastery: progressionRate > 0 ? (0.8 - currentMastery) / progressionRate : Infinity,
      masteryProbability: Math.min(currentMastery + progressionRate, 1)
    };
  }
  predictFutureChallenges(errorData, progressionData) {
    const errorRate = errorData.errorRate || 0;
    const persistentErrors = errorData.persistentErrors || [];
    return {
      likelyErrorTypes: persistentErrors.slice(0, 3),
      errorTrend: errorRate > 0.3 ? "increasing" : "stable",
      recommendedFocus: this.identifyErrorFocus(persistentErrors)
    };
  }
  identifyErrorFocus(persistentErrors) {
    if (!Array.isArray(persistentErrors) || persistentErrors.length === 0) {
      return "general_practice";
    }
    const errorTypes = persistentErrors.map((e) => e.pattern || e.type);
    const mostCommon = this.findMostCommon(errorTypes);
    const focusMap = {
      "omission": "attention_training",
      "substitution": "pattern_practice",
      "order_error": "sequence_training",
      "timing": "rhythm_practice"
    };
    return focusMap[mostCommon] || "general_practice";
  }
  findMostCommon(array) {
    if (!Array.isArray(array) || array.length === 0) return null;
    const counts = {};
    array.forEach((item) => {
      counts[item] = (counts[item] || 0) + 1;
    });
    return Object.entries(counts).sort((a, b) => b[1] - a[1])[0][0];
  }
  calculateSynergy(collector1Data, collector2Data) {
    const scores1 = this.extractScores(collector1Data);
    const scores2 = this.extractScores(collector2Data);
    if (scores1.length === 0 || scores2.length === 0) return 0;
    const avg1 = scores1.reduce((a, b) => a + b, 0) / scores1.length;
    const avg2 = scores2.reduce((a, b) => a + b, 0) / scores2.length;
    return 1 - Math.abs(avg1 - avg2);
  }
  extractScores(collectorData) {
    const scores = [];
    const extractFromObject = (obj) => {
      if (typeof obj === "number" && obj >= 0 && obj <= 1) {
        scores.push(obj);
      } else if (typeof obj === "object" && obj !== null) {
        Object.values(obj).forEach(extractFromObject);
      }
    };
    extractFromObject(collectorData);
    return scores;
  }
  calculateCollectorConsistency(collectionResults) {
    const collectors = Object.keys(collectionResults);
    if (collectors.length < 2) return 1;
    const avgScores = collectors.map((collector) => {
      const scores = this.extractScores(collectionResults[collector]);
      return scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0;
    });
    const variance = this.calculateVariance(avgScores);
    return Math.max(0, 1 - variance);
  }
  calculateVariance(values) {
    if (!Array.isArray(values) || values.length === 0) return 0;
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    return values.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / values.length;
  }
  generateInteractionSummary(collectionResults, integratedResults) {
    return {
      collectorsActive: Object.keys(collectionResults),
      dataPointsCollected: Object.values(collectionResults).reduce((sum, collector) => sum + Object.keys(collector).length, 0),
      integratedInsights: Object.keys(integratedResults).length,
      overallQuality: this.assessDataQuality(collectionResults),
      keyFindings: this.extractKeyFindings(collectionResults, integratedResults)
    };
  }
  assessDataQuality(collectionResults) {
    const qualityScores = Object.values(collectionResults).map((collector) => {
      const scores = this.extractScores(collector);
      return scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0;
    });
    return qualityScores.length > 0 ? qualityScores.reduce((a, b) => a + b, 0) / qualityScores.length : 0;
  }
  extractKeyFindings(collectionResults, integratedResults) {
    const findings = [];
    Object.entries(collectionResults).forEach(([collector, data]) => {
      const scores = this.extractScores(data);
      if (scores.length > 0) {
        const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length;
        if (avgScore > 0.8) {
          findings.push(`${collector}_strength`);
        } else if (avgScore < 0.4) {
          findings.push(`${collector}_weakness`);
        }
      }
    });
    if (integratedResults.recommendations?.length > 0) {
      findings.push("actionable_insights");
    }
    return findings;
  }
  /**
   * Métodos de controle e relatório
   */
  enableCollector(collectorName) {
    if (["memory", "pattern", "execution", "learning"].includes(collectorName)) {
      this.activeCollectors.add(collectorName);
      if (this.debugMode) {
        console.log(`🎵 Coletor ${collectorName} habilitado`);
      }
      return true;
    }
    return false;
  }
  disableCollector(collectorName) {
    this.activeCollectors.delete(collectorName);
    if (this.debugMode) {
      console.log(`🎵 Coletor ${collectorName} desabilitado`);
    }
    return true;
  }
  getComprehensiveReport() {
    try {
      return {
        session: {
          sessionId: this.sessionData.sessionId,
          duration: Date.now() - this.sessionData.startTime,
          activeCollectors: Array.from(this.activeCollectors)
        },
        memory: this.auditoryMemoryCollector.getMemoryReport(),
        patterns: this.musicalPatternCollector.getPatternReport(),
        execution: this.sequenceExecutionCollector.getExecutionReport(),
        learning: this.musicalLearningCollector.getLearningReport(),
        integrated: {
          crossCollectorMetrics: this.crossCollectorMetrics,
          integratedAnalysis: this.integratedAnalysis
        },
        summary: this.generateOverallSummary()
      };
    } catch (error) {
      console.error("Erro ao gerar relatório comprehensivo:", error);
      return { error: "Failed to generate report" };
    }
  }
  generateOverallSummary() {
    return {
      totalInteractions: this.crossCollectorMetrics.totalInteractions || 0,
      sessionDuration: Date.now() - this.sessionData.startTime,
      dataQuality: this.assessOverallDataQuality(),
      keyInsights: this.extractOverallInsights(),
      recommendations: this.generateOverallRecommendations()
    };
  }
  assessOverallDataQuality() {
    const qualityMetrics = [];
    if (this.activeCollectors.has("memory")) {
      qualityMetrics.push(this.assessCollectorQuality("memory"));
    }
    if (this.activeCollectors.has("pattern")) {
      qualityMetrics.push(this.assessCollectorQuality("pattern"));
    }
    if (this.activeCollectors.has("execution")) {
      qualityMetrics.push(this.assessCollectorQuality("execution"));
    }
    if (this.activeCollectors.has("learning")) {
      qualityMetrics.push(this.assessCollectorQuality("learning"));
    }
    return qualityMetrics.length > 0 ? qualityMetrics.reduce((a, b) => a + b, 0) / qualityMetrics.length : 0;
  }
  assessCollectorQuality(collectorName) {
    return Math.random() * 0.4 + 0.6;
  }
  extractOverallInsights() {
    return [
      "Dados coletados de múltiplas dimensões cognitivas",
      "Análise integrada fornece visão holística",
      "Padrões identificados em memória, execução e aprendizado"
    ];
  }
  generateOverallRecommendations() {
    return [
      "Continue praticando regularmente para manter progresso",
      "Foque nas áreas identificadas como pontos fracos",
      "Use feedback integrado para otimizar estratégias de aprendizado"
    ];
  }
  clearAllData() {
    this.auditoryMemoryCollector.clearData();
    this.musicalPatternCollector.clearData();
    this.sequenceExecutionCollector.clearData();
    this.musicalLearningCollector.clearData();
    this.integratedAnalysis = {};
    this.crossCollectorMetrics = {};
    if (this.debugMode) {
      console.log("🎵 MusicalSequenceCollectorsHub - Todos os dados limpos");
    }
  }
  endSession() {
    try {
      const finalReport = this.getComprehensiveReport();
      this.saveSessionData(finalReport);
      this.clearAllData();
      this.sessionData = {
        sessionId: null,
        startTime: null,
        gameConfig: null
      };
      if (this.debugMode) {
        console.log("🎵 MusicalSequenceCollectorsHub - Sessão finalizada");
      }
      return finalReport;
    } catch (error) {
      console.error("Erro ao finalizar sessão:", error);
      return { error: "Failed to end session" };
    }
  }
  saveSessionData(reportData) {
    if (this.debugMode) {
      console.log("🎵 Dados da sessão salvos:", {
        sessionId: reportData.session?.sessionId,
        dataPoints: Object.keys(reportData).length
      });
    }
  }
  // Status e diagnóstico
  getStatus() {
    return {
      isEnabled: this.isEnabled,
      activeCollectors: Array.from(this.activeCollectors),
      sessionActive: !!this.sessionData.sessionId,
      sessionDuration: this.sessionData.startTime ? Date.now() - this.sessionData.startTime : 0,
      totalInteractions: this.crossCollectorMetrics.totalInteractions || 0
    };
  }
  setDebugMode(enabled) {
    this.debugMode = enabled;
    if (this.auditoryMemoryCollector.debugMode !== void 0) {
      this.auditoryMemoryCollector.debugMode = enabled;
    }
    if (this.musicalPatternCollector.debugMode !== void 0) {
      this.musicalPatternCollector.debugMode = enabled;
    }
    if (this.sequenceExecutionCollector.debugMode !== void 0) {
      this.sequenceExecutionCollector.debugMode = enabled;
    }
    if (this.musicalLearningCollector.debugMode !== void 0) {
      this.musicalLearningCollector.debugMode = enabled;
    }
    console.log(`🎵 Debug mode ${enabled ? "habilitado" : "desabilitado"} para MusicalSequenceCollectorsHub`);
  }
}
const isBrowser = typeof window !== "undefined" && typeof window.document !== "undefined";
const logger = isBrowser ? {
  info: (...args) => console.info("%c🎵 [MUSICAL-SEQUENCE]", "color: #2196F3", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  error: (...args) => console.error("%c🔴 [MUSICAL-ERROR]", "color: #F44336", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  warn: (...args) => console.warn("%c🟡 [MUSICAL-WARN]", "color: #FF9800", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  debug: (...args) => console.debug("%c⚪ [MUSICAL-DEBUG]", "color: #9E9E9E", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  therapeutic: (...args) => console.info("%c🏥 [MUSICAL-THERAPEUTIC]", "color: #4CAF50", (/* @__PURE__ */ new Date()).toISOString(), ...args)
} : {
  info: (...args) => console.info("🎵 [MUSICAL-SEQUENCE]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  error: (...args) => console.error("🔴 [MUSICAL-ERROR]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  warn: (...args) => console.warn("🟡 [MUSICAL-WARN]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  debug: (...args) => console.debug("⚪ [MUSICAL-DEBUG]", (/* @__PURE__ */ new Date()).toISOString(), ...args),
  therapeutic: (...args) => console.info("🏥 [MUSICAL-THERAPEUTIC]", (/* @__PURE__ */ new Date()).toISOString(), ...args)
};
class MusicalSequenceProcessors extends IGameProcessor {
  constructor(loggerInstance = null) {
    const config = {
      category: "auditory_processing",
      therapeuticFocus: ["auditory_processing", "sequence_memory", "musical_cognition"],
      cognitiveAreas: ["auditory_processing", "memory", "attention"],
      thresholds: {
        accuracy: 60,
        responseTime: 4e3,
        engagement: 65
      }
    };
    super(config);
    this.logger = loggerInstance && typeof loggerInstance.therapeutic === "function" ? loggerInstance : logger;
    this.config = {
      category: "auditory-processing",
      therapeuticFocus: ["auditory_processing", "sequential_memory", "rhythm_perception"],
      cognitiveAreas: ["auditory_processing", "memory", "temporal_processing"],
      thresholds: {
        accuracy: 65,
        responseTime: 3500,
        engagement: 60
      }
    };
    this.logger.info("🎵 Processadores Musical Sequence inicializados");
  }
  /**
   * Processa dados do jogo Musical Sequence
   * @param {Object} gameData - Dados coletados do jogo
   * @param {Object} collectorsHub - Hub de coletores específico do jogo
   * @returns {Promise<Object>} Análise terapêutica específica
   */
  async processGameData(gameData, collectorsHub2 = null) {
    try {
      this.logger?.info("🎮 Processando dados MusicalSequence", {
        sessionId: gameData.sessionId,
        userId: gameData.userId,
        collectorsActive: collectorsHub2 ? Object.keys(collectorsHub2.collectors || {}).length : 0
      });
      const metrics = await this.processMusicalSequenceMetrics(gameData, gameData);
      const therapeuticAnalysis = this.generateTherapeuticAnalysis(metrics, gameData);
      const processedMetrics = this.processMetricsForDatabase(metrics, gameData);
      return {
        success: true,
        gameType: this.gameType,
        metrics,
        therapeuticAnalysis,
        processedMetrics,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    } catch (error) {
      this.logger?.error("❌ Erro ao processar dados MusicalSequence:", error);
      return {
        success: false,
        gameType: this.gameType,
        error: error.message,
        fallbackAnalysis: this.generateFallbackTherapeuticAnalysis(gameData),
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    }
  }
  /**
   * Calcula tempo médio de resposta
   */
  calculateAverageResponseTime(sequences) {
    if (!sequences || sequences.length === 0) return 3e3;
    const responseTimes = sequences.map((s) => s.responseTime).filter((t) => t && t > 0);
    if (responseTimes.length === 0) return 3e3;
    return responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
  }
  /**
   * Verifica se há melhoria consistente
   */
  hasConsistentImprovement(sequences) {
    if (!sequences || sequences.length < 3) return false;
    let improvements = 0;
    for (let i = 1; i < sequences.length; i++) {
      if (sequences[i].score > sequences[i - 1].score) {
        improvements++;
      }
    }
    return improvements > sequences.length * 0.6;
  }
  /**
   * Verifica se há timing consistente
   */
  hasConsistentTiming(sequences) {
    if (!sequences || sequences.length < 3) return false;
    const timingVariances = sequences.map((s) => s.timingVariance || 0);
    const avgVariance = timingVariances.reduce((sum, v) => sum + v, 0) / timingVariances.length;
    return avgVariance < 0.2;
  }
  /**
   * Verifica se há melhoria na coordenação
   */
  hasImprovingCoordination(sequences) {
    if (!sequences || sequences.length < 3) return false;
    const firstHalf = sequences.slice(0, Math.floor(sequences.length / 2));
    const secondHalf = sequences.slice(Math.floor(sequences.length / 2));
    const firstAvg = firstHalf.reduce((sum, s) => sum + (s.coordinationScore || 0), 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, s) => sum + (s.coordinationScore || 0), 0) / secondHalf.length;
    return secondAvg > firstAvg * 1.1;
  }
  /**
   * Verifica se sequências são similares
   */
  areSequencesSimilar(seq1, seq2) {
    if (!seq1 || !seq2) return false;
    return seq1.type === seq2.type && Math.abs((seq1.difficulty || 1) - (seq2.difficulty || 1)) <= 1;
  }
  /**
   * Identifica padrões auditivos
   */
  identifyAuditoryPatterns(sequences) {
    if (!sequences || sequences.length === 0) return [];
    const patterns = [];
    if (this.hasConsistentImprovement(sequences)) {
      patterns.push("consistent_improvement");
    }
    const longSequences = sequences.filter((s) => (s.originalSequence || []).length > 6);
    if (longSequences.length > 0 && longSequences.filter((s) => !s.correct).length / longSequences.length > 0.7) {
      patterns.push("long_sequence_difficulty");
    }
    return patterns;
  }
  /**
   * Identifica forças auditivas
   */
  identifyAuditoryStrengths(sequences) {
    if (!sequences || sequences.length === 0) return [];
    const strengths = [];
    const accuracy = sequences.filter((s) => s.correct).length / sequences.length;
    if (accuracy > 0.8) strengths.push("high_auditory_accuracy");
    if (this.calculateAuditorySpan(sequences) > 6) strengths.push("large_auditory_span");
    return strengths;
  }
  /**
   * Identifica desafios auditivos
   */
  identifyAuditoryChallenges(sequences) {
    if (!sequences || sequences.length === 0) return ["insufficient_data"];
    const challenges = [];
    const accuracy = sequences.filter((s) => s.correct).length / sequences.length;
    if (accuracy < 0.4) challenges.push("low_auditory_accuracy");
    if (this.calculateAuditorySpan(sequences) < 3) challenges.push("limited_auditory_span");
    return challenges.length > 0 ? challenges : ["none_identified"];
  }
  /**
   * Gera recomendações para memória auditiva
   */
  generateAuditoryMemoryRecommendations(auditorySpan, auditoryAccuracy, temporalMemory) {
    const recommendations = [];
    if (auditorySpan < 4) {
      recommendations.push("Exercícios progressivos de extensão de sequências auditivas");
    }
    if (auditoryAccuracy < 0.6) {
      recommendations.push("Práticas de discriminação auditiva e atenção focada");
    }
    if (temporalMemory < 0.5) {
      recommendations.push("Atividades de processamento temporal e ritmo");
    }
    return recommendations;
  }
  /**
   * Calcula aptidão musical
   */
  calculateMusicalAptitude(sequences) {
    if (!sequences || sequences.length === 0) return 0.5;
    const musicalTasks = sequences.filter((s) => s.musicalElements);
    const correctMusical = musicalTasks.filter((s) => s.correct);
    return musicalTasks.length > 0 ? correctMusical.length / musicalTasks.length : 0.5;
  }
  /**
   * Calcula precisão rítmica
   */
  calculateRhythmAccuracy(sequences) {
    if (!sequences || sequences.length === 0) return 0.5;
    const rhythmTasks = sequences.filter((s) => s.rhythmPattern);
    const correctRhythm = rhythmTasks.filter((s) => s.correct);
    return rhythmTasks.length > 0 ? correctRhythm.length / rhythmTasks.length : 0.5;
  }
  /**
   * Calcula precisão temporal
   */
  calculateTemporalPrecision(sequences) {
    if (!sequences || sequences.length === 0) return 0.5;
    let precisionSum = 0;
    let validSequences = 0;
    sequences.forEach((sequence) => {
      if (sequence.temporalAccuracy !== void 0) {
        precisionSum += sequence.temporalAccuracy;
        validSequences++;
      }
    });
    return validSequences > 0 ? precisionSum / validSequences : 0.5;
  }
  /**
   * Identifica padrões musicais
   */
  identifyMusicalPatterns(sequences, rhythmPatterns) {
    if (!sequences || sequences.length === 0) return [];
    const patterns = [];
    if (rhythmPatterns && rhythmPatterns.length > 0) {
      patterns.push("rhythmic_patterns_detected");
    }
    const melodicSequences = sequences.filter((s) => s.melodicElements);
    if (melodicSequences.length > sequences.length * 0.5) {
      patterns.push("melodic_consistency");
    }
    return patterns;
  }
  /**
   * Identifica forças musicais
   */
  identifyMusicalStrengths(sequences) {
    if (!sequences || sequences.length === 0) return [];
    const strengths = [];
    if (this.calculateMusicalAptitude(sequences) > 0.7) {
      strengths.push("high_musical_aptitude");
    }
    if (this.calculateRhythmAccuracy(sequences) > 0.7) {
      strengths.push("strong_rhythm_perception");
    }
    return strengths;
  }
  /**
   * Identifica desafios musicais
   */
  identifyMusicalChallenges(sequences) {
    if (!sequences || sequences.length === 0) return ["insufficient_data"];
    const challenges = [];
    if (this.calculateMusicalAptitude(sequences) < 0.4) {
      challenges.push("musical_processing_difficulties");
    }
    if (this.calculateRhythmAccuracy(sequences) < 0.4) {
      challenges.push("rhythm_perception_challenges");
    }
    return challenges.length > 0 ? challenges : ["none_identified"];
  }
  /**
   * Gera recomendações para padrões musicais
   */
  generateMusicalPatternRecommendations(patternRecognition, rhythmPerception, melodicProcessing) {
    const recommendations = [];
    if (patternRecognition < 0.5) {
      recommendations.push("Exercícios de reconhecimento de padrões musicais simples");
    }
    if (rhythmPerception < 0.5) {
      recommendations.push("Atividades de percepção rítmica e pulsação");
    }
    if (melodicProcessing < 0.5) {
      recommendations.push("Práticas de processamento melódico e intervalos");
    }
    return recommendations;
  }
  /**
   * Calcula coordenação motora
   */
  calculateMotorCoordination(sequences) {
    if (!sequences || sequences.length === 0) return 0.5;
    const motorTasks = sequences.filter((s) => s.requiresMotorCoordination);
    const correctMotor = motorTasks.filter((s) => s.correct);
    return motorTasks.length > 0 ? correctMotor.length / motorTasks.length : 0.5;
  }
  /**
   * Calcula precisão de tempo
   */
  calculateTimingPrecision(sequences) {
    if (!sequences || sequences.length === 0) return 0.5;
    let timingSum = 0;
    let validTiming = 0;
    sequences.forEach((sequence) => {
      if (sequence.timingPrecision !== void 0) {
        timingSum += sequence.timingPrecision;
        validTiming++;
      }
    });
    return validTiming > 0 ? timingSum / validTiming : 0.5;
  }
  /**
   * Avalia complexidade da sequência
   */
  assessSequenceComplexity(sequences) {
    if (!sequences || sequences.length === 0) return 0.5;
    const complexityScores = sequences.map((s) => s.complexity || 1);
    const avgComplexity = complexityScores.reduce((sum, c) => sum + c, 0) / complexityScores.length;
    return Math.min(1, avgComplexity / 5);
  }
  /**
   * Calcula fidelidade de reprodução
   */
  calculateReproductionFidelity(sequences) {
    if (!sequences || sequences.length === 0) return 0.5;
    let fidelitySum = 0;
    let validReproductions = 0;
    sequences.forEach((sequence) => {
      if (sequence.reproductionFidelity !== void 0) {
        fidelitySum += sequence.reproductionFidelity;
        validReproductions++;
      }
    });
    return validReproductions > 0 ? fidelitySum / validReproductions : 0.5;
  }
  /**
   * Calcula velocidade de execução
   */
  calculateExecutionSpeed(sequences) {
    if (!sequences || sequences.length === 0) return 0.5;
    const executionTimes = sequences.map((s) => s.executionTime).filter((t) => t && t > 0);
    if (executionTimes.length === 0) return 0.5;
    const avgTime = executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length;
    return Math.max(0, Math.min(1, (1e4 - avgTime) / 1e4));
  }
  /**
   * Calcula estabilidade de coordenação
   */
  calculateCoordinationStability(sequences) {
    if (!sequences || sequences.length === 0) return 0.5;
    const coordinationScores = sequences.map((s) => s.coordinationScore || 0.5);
    const variance = this.calculateVariance(coordinationScores);
    return Math.max(0, 1 - variance);
  }
  /**
   * Calcula variância
   */
  calculateVariance(values) {
    if (values.length === 0) return 0;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map((val) => Math.pow(val - mean, 2));
    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
  }
  /**
   * Identifica padrões de execução
   */
  identifyExecutionPatterns(sequences) {
    if (!sequences || sequences.length === 0) return [];
    const patterns = [];
    if (this.hasConsistentTiming(sequences)) {
      patterns.push("consistent_timing");
    }
    if (this.hasImprovingCoordination(sequences)) {
      patterns.push("improving_coordination");
    }
    return patterns;
  }
  /**
   * Identifica forças de execução
   */
  identifyExecutionStrengths(sequences) {
    if (!sequences || sequences.length === 0) return [];
    const strengths = [];
    if (this.calculateExecutionAccuracy(sequences) > 0.8) {
      strengths.push("high_execution_accuracy");
    }
    if (this.calculateTimingPrecision(sequences) > 0.8) {
      strengths.push("precise_timing");
    }
    return strengths;
  }
  /**
   * Identifica desafios de execução
   */
  identifyExecutionChallenges(sequences) {
    if (!sequences || sequences.length === 0) return ["insufficient_data"];
    const challenges = [];
    if (this.calculateExecutionAccuracy(sequences) < 0.4) {
      challenges.push("execution_accuracy_issues");
    }
    if (this.calculateTimingPrecision(sequences) < 0.4) {
      challenges.push("timing_precision_problems");
    }
    return challenges.length > 0 ? challenges : ["none_identified"];
  }
  /**
   * Gera recomendações para execução de sequência
   */
  generateSequenceExecutionRecommendations(executionAccuracy, sequenceReproduction, motorCoordination) {
    const recommendations = [];
    if (executionAccuracy < 0.5) {
      recommendations.push("Praticar execução de sequências com feedback visual");
    }
    if (sequenceReproduction < 0.5) {
      recommendations.push("Exercícios de reprodução fiel de sequências simples");
    }
    if (motorCoordination < 0.5) {
      recommendations.push("Atividades de coordenação motora fina");
    }
    return recommendations;
  }
  /**
   * Calcula retenção musical
   */
  calculateMusicalRetention(sequences) {
    if (!sequences || sequences.length < 2) return 0.5;
    let retentionSum = 0;
    let retentionCount = 0;
    for (let i = 1; i < sequences.length; i++) {
      const prev = sequences[i - 1];
      const curr = sequences[i];
      if (this.areSequencesSimilar(prev, curr)) {
        if (prev.correct && curr.correct) {
          retentionSum += 1;
        } else if (!prev.correct && curr.correct) {
          retentionSum += 0.8;
        } else if (prev.correct && !curr.correct) {
          retentionSum += 0.3;
        } else {
          retentionSum += 0.1;
        }
        retentionCount++;
      }
    }
    return retentionCount > 0 ? retentionSum / retentionCount : 0.5;
  }
  /**
   * Analisa trajetória de melhoria
   */
  analyzeImprovementTrajectory(sequences) {
    if (!sequences || sequences.length < 3) return [];
    const trajectory = [];
    const windowSize = 3;
    for (let i = 0; i <= sequences.length - windowSize; i++) {
      const window2 = sequences.slice(i, i + windowSize);
      const accuracy = window2.filter((s) => s.correct).length / windowSize;
      trajectory.push({
        position: i + windowSize - 1,
        accuracy,
        trend: i > 0 ? accuracy - trajectory[i - 1].accuracy : 0
      });
    }
    return trajectory;
  }
  /**
   * Verifica estratégia de repetição
   */
  hasRepetitionStrategy(sequences) {
    if (!sequences || sequences.length < 2) return false;
    let repetitions = 0;
    for (let i = 1; i < sequences.length; i++) {
      if (this.areSequencesSimilar(sequences[i - 1], sequences[i])) {
        repetitions++;
      }
    }
    return repetitions > sequences.length * 0.3;
  }
  /**
   * Verifica estratégia de segmentação
   */
  hasSegmentationStrategy(sequences) {
    if (!sequences || sequences.length === 0) return false;
    const longSequences = sequences.filter((s) => (s.originalSequence || []).length > 6);
    const segmentedApproach = longSequences.filter((s) => s.usedSegmentation);
    return longSequences.length > 0 && segmentedApproach.length / longSequences.length > 0.5;
  }
  /**
   * Identifica estratégias de aprendizagem
   */
  identifyLearningStrategies(sequences) {
    if (!sequences || sequences.length === 0) return [];
    const strategies = [];
    if (this.hasRepetitionStrategy(sequences)) {
      strategies.push("repetition_based");
    }
    if (this.hasSegmentationStrategy(sequences)) {
      strategies.push("segmentation_based");
    }
    return strategies;
  }
  /**
   * Calcula eficiência de aprendizagem
   */
  calculateLearningEfficiency(sequences) {
    if (!sequences || sequences.length === 0) return 0.5;
    const accuracy = sequences.filter((s) => s.correct).length / sequences.length;
    const avgTime = this.calculateAverageResponseTime(sequences);
    return accuracy * Math.max(0, (5e3 - avgTime) / 5e3);
  }
  /**
   * Calcula estabilidade de retenção
   */
  calculateRetentionStability(sequences) {
    if (!sequences || sequences.length < 3) return 0.5;
    const retentionScores = [];
    for (let i = 1; i < sequences.length; i++) {
      if (this.areSequencesSimilar(sequences[i - 1], sequences[i])) {
        retentionScores.push(sequences[i].correct ? 1 : 0);
      }
    }
    if (retentionScores.length === 0) return 0.5;
    const variance = this.calculateVariance(retentionScores);
    return Math.max(0, 1 - variance);
  }
  /**
   * Calcula capacidade de transferência
   */
  calculateTransferCapacity(sequences) {
    if (!sequences || sequences.length === 0) return 0.5;
    const sequenceTypes = this.groupSequencesByType(sequences);
    let transferSum = 0;
    let transferCount = 0;
    Object.values(sequenceTypes).forEach((typeSequences) => {
      if (typeSequences.length > 1) {
        const accuracy = typeSequences.filter((s) => s.correct).length / typeSequences.length;
        transferSum += accuracy;
        transferCount++;
      }
    });
    return transferCount > 0 ? transferSum / transferCount : 0.5;
  }
  /**
   * Identifica padrões de aprendizagem
   */
  identifyLearningPatterns(sequences) {
    if (!sequences || sequences.length === 0) return [];
    const patterns = [];
    if (this.hasProgressiveImprovement(sequences)) {
      patterns.push("progressive_improvement");
    }
    if (this.hasPlateauPattern(sequences)) {
      patterns.push("learning_plateau");
    }
    return patterns;
  }
  /**
   * Identifica forças de aprendizagem
   */
  identifyLearningStrengths(sequences) {
    if (!sequences || sequences.length === 0) return [];
    const strengths = [];
    if (this.calculateLearningRate(sequences) > 0.7) {
      strengths.push("fast_learning");
    }
    if (this.calculateMusicalRetention(sequences) > 0.7) {
      strengths.push("strong_retention");
    }
    return strengths;
  }
  /**
   * Identifica desafios de aprendizagem
   */
  identifyLearningChallenges(sequences) {
    if (!sequences || sequences.length === 0) return ["insufficient_data"];
    const challenges = [];
    if (this.calculateLearningRate(sequences) < 0.3) {
      challenges.push("slow_learning_rate");
    }
    if (this.calculateMusicalRetention(sequences) < 0.3) {
      challenges.push("poor_retention");
    }
    return challenges.length > 0 ? challenges : ["none_identified"];
  }
  /**
   * Gera recomendações para aprendizagem musical
   */
  generateMusicalLearningRecommendations(learningRate, adaptiveCapacity, musicalRetention) {
    const recommendations = [];
    if (learningRate < 0.5) {
      recommendations.push("Usar estratégias de aprendizagem mais estruturadas");
    }
    if (adaptiveCapacity < 0.5) {
      recommendations.push("Praticar adaptação a diferentes contextos musicais");
    }
    if (musicalRetention < 0.5) {
      recommendations.push("Implementar técnicas de reforço e revisão");
    }
    return recommendations;
  }
  // === MÉTODOS AUXILIARES DE ANÁLISE MUSICAL =====
  /**
   * Calcula memória musical
   */
  calculateMusicalMemory(sequences) {
    if (!sequences || sequences.length === 0) return 0.5;
    const memoryTasks = sequences.filter((s) => s.requiresMemory || s.memoryBased);
    const correctMemory = memoryTasks.filter((s) => s.correct);
    return memoryTasks.length > 0 ? correctMemory.length / memoryTasks.length : 0.5;
  }
  /**
   * Calcula rastreamento de batida
   */
  calculateBeatTracking(sequences) {
    if (!sequences || sequences.length === 0) return 0.5;
    const beatTasks = sequences.filter((s) => s.beatPattern || s.rhythmPattern);
    const correctBeat = beatTasks.filter((s) => s.correct);
    return beatTasks.length > 0 ? correctBeat.length / beatTasks.length : 0.5;
  }
  // ===== MÉTODOS DE SÍNTESE TERAPÊUTICA =====
  /**
   * Avalia nível de processamento auditivo
   */
  assessAuditoryProcessingLevel(auditoryMemoryResults, sequenceExecutionResults) {
    const auditoryScore = auditoryMemoryResults?.auditoryAccuracy || 0.5;
    const executionScore = sequenceExecutionResults?.executionAccuracy || 0.5;
    const overallScore = (auditoryScore + executionScore) / 2;
    if (overallScore >= 0.8) return "advanced";
    if (overallScore >= 0.6) return "proficient";
    if (overallScore >= 0.4) return "developing";
    return "needs_support";
  }
  /**
   * Avalia capacidade auditiva
   */
  assessAuditoryCapacity(auditoryMemoryResults) {
    const span = auditoryMemoryResults?.auditorySpan || 3;
    const accuracy = auditoryMemoryResults?.auditoryAccuracy || 0.5;
    if (span >= 7 && accuracy >= 0.8) return "high";
    if (span >= 5 && accuracy >= 0.6) return "moderate";
    if (span >= 3 && accuracy >= 0.4) return "basic";
    return "limited";
  }
  /**
   * Avalia precisão auditiva
   */
  assessAuditoryPrecision(sequenceExecutionResults) {
    const timing = sequenceExecutionResults?.timingPrecision || 0.5;
    const coordination = sequenceExecutionResults?.motorCoordination || 0.5;
    const overallPrecision = (timing + coordination) / 2;
    if (overallPrecision >= 0.8) return "high";
    if (overallPrecision >= 0.6) return "moderate";
    if (overallPrecision >= 0.4) return "basic";
    return "limited";
  }
  /**
   * Gera recomendações de processamento auditivo
   */
  generateAuditoryProcessingRecommendations(auditoryMemoryResults, sequenceExecutionResults) {
    const recommendations = [];
    if (auditoryMemoryResults?.auditoryAccuracy < 0.5) {
      recommendations.push("Exercícios de discriminação auditiva");
    }
    if (sequenceExecutionResults?.timingPrecision < 0.5) {
      recommendations.push("Treinamento de precisão temporal");
    }
    if (auditoryMemoryResults?.auditorySpan < 4) {
      recommendations.push("Expansão gradual do span auditivo");
    }
    return recommendations;
  }
  /**
   * Avalia nível de desenvolvimento musical
   */
  assessMusicalDevelopmentLevel(musicalPatternResults, musicalLearningResults) {
    const patternScore = musicalPatternResults?.patternRecognition || 0.5;
    const learningScore = musicalLearningResults?.learningRate || 0.5;
    const overallScore = (patternScore + learningScore) / 2;
    if (overallScore >= 0.8) return "advanced";
    if (overallScore >= 0.6) return "intermediate";
    if (overallScore >= 0.4) return "beginner";
    return "emergent";
  }
  /**
   * Método principal para processar dados (compatibilidade com GameSpecificProcessors)
   * @param {Object} gameData - Dados do jogo
   * @returns {Promise<Object>} - Dados processados
   */
  async processData(gameData) {
    try {
      this.logger.info("🎯 Processando dados Musical Sequence", {
        sessionId: gameData.sessionId,
        userId: gameData.userId
      });
      const result = await this.processGameData(gameData);
      this.logger.therapeutic("✅ Processamento Musical Sequence concluído com sucesso");
      return {
        success: true,
        gameType: "MusicalSequence",
        metrics: {
          auditoryMemory: result.auditoryMemory || {},
          musicalPattern: result.musicalPattern || {},
          sequenceExecution: result.sequenceExecution || {},
          musicalLearning: result.musicalLearning || {},
          therapeuticOutcomes: result.therapeuticOutcomes || {}
        },
        therapeuticAnalysis: result.therapeuticOutcomes || {},
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    } catch (error) {
      this.logger.error("❌ Erro ao processar dados Musical Sequence:", error);
      return {
        success: false,
        gameType: "MusicalSequence",
        error: error.message,
        metrics: {},
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    }
  }
  // === MÉTODOS AUXILIARES ===
  extractRhythmPatterns(attempts) {
    const patterns = [];
    attempts.forEach((attempt) => {
      if (attempt.tempo && attempt.notes) {
        patterns.push({
          tempo: attempt.tempo,
          notePattern: attempt.notes,
          duration: attempt.duration || 0
        });
      }
    });
    return patterns;
  }
  compareTimingAccuracy(original, player) {
    if (!original || !player || original.length !== player.length) return 0;
    let matches = 0;
    const tolerance = 100;
    for (let i = 0; i < original.length; i++) {
      const originalTiming = original[i].timestamp || 0;
      const playerTiming = player[i].timestamp || 0;
      if (Math.abs(originalTiming - playerTiming) <= tolerance) {
        matches++;
      }
    }
    return matches / original.length;
  }
  hasRecognizablePattern(sequence, rhythmPatterns) {
    return sequence.correct && sequence.difficulty > 1;
  }
  calculateRhythmPrecision(sequence) {
    if (!sequence.originalSequence || !sequence.playerSequence) return 0.5;
    return this.compareTimingAccuracy(sequence.originalSequence, sequence.playerSequence);
  }
  assessDataQuality(gameData) {
    let quality = 1;
    if (!gameData.sessionId) quality -= 0.2;
    if (!gameData.userId) quality -= 0.2;
    if (!gameData.attempts || gameData.attempts.length === 0) quality -= 0.3;
    if (!gameData.metrics) quality -= 0.3;
    return Math.max(0, quality);
  }
  // === MÉTODOS FALLBACK ===
  getFallbackAuditoryMemoryData() {
    return {
      auditorySpan: 3,
      auditoryAccuracy: 0.5,
      temporalMemory: 0.5,
      auditoryRetention: 0.5,
      sequentialProcessing: 0.5,
      workingMemoryCapacity: 0.5,
      auditoryProcessingSpeed: 0.5,
      auditoryDiscrimination: 0.5,
      auditoryPatterns: [],
      auditoryStrengths: [],
      auditoryChallenges: ["insufficient_data"],
      recommendations: ["Desenvolver exercícios de memória auditiva estruturados"]
    };
  }
  getFallbackMusicalPatternData() {
    return {
      patternRecognition: 0.5,
      rhythmPerception: 0.5,
      melodicProcessing: 0.5,
      musicalMemory: 0.5,
      beatTracking: 0.5,
      musicalAptitude: 0.5,
      rhythmAccuracy: 0.5,
      temporalPrecision: 0.5,
      identifiedPatterns: [],
      musicalStrengths: [],
      musicalChallenges: ["insufficient_data"],
      recommendations: ["Praticar reconhecimento de padrões musicais simples"]
    };
  }
  getFallbackSequenceExecutionData() {
    return {
      executionAccuracy: 0.5,
      sequenceReproduction: 0.5,
      motorCoordination: 0.5,
      timingPrecision: 0.5,
      sequenceComplexity: 0.5,
      reproductionFidelity: 0.5,
      executionSpeed: 0.5,
      coordinationStability: 0.5,
      executionPatterns: [],
      executionStrengths: [],
      executionChallenges: ["insufficient_data"],
      recommendations: ["Trabalhar coordenação motora e precisão temporal"]
    };
  }
  getFallbackMusicalLearningData() {
    return {
      learningRate: 0.5,
      adaptiveCapacity: 0.5,
      musicalRetention: 0.5,
      improvementTrajectory: [],
      learningStrategies: [],
      learningEfficiency: 0.5,
      retentionStability: 0.5,
      transferCapacity: 0.5,
      learningPatterns: [],
      learningStrengths: [],
      learningChallenges: ["insufficient_data"],
      recommendations: ["Estabelecer rotina de prática musical consistente"]
    };
  }
  generateFallbackAnalysis(gameData) {
    return {
      gameId: "MusicalSequence",
      sessionId: gameData.sessionId || "unknown",
      userId: gameData.userId || "unknown",
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      auditoryMemory: this.getFallbackAuditoryMemoryData(),
      musicalPatterns: this.getFallbackMusicalPatternData(),
      sequenceExecution: this.getFallbackSequenceExecutionData(),
      musicalLearning: this.getFallbackMusicalLearningData(),
      integratedAnalysis: this.getIntegratedFallback(),
      therapeuticOutcomes: {
        auditoryProcessing: { level: "unknown", capacity: "needs_assessment" },
        musicalDevelopment: { level: "unknown", aptitude: "needs_assessment" },
        cognitiveSupport: { needs: [], strategies: [] },
        sequencingSkills: { level: "unknown", precision: "needs_assessment" }
      },
      progressMetrics: {
        overallPerformance: 0.5,
        strengthAreas: [],
        challengeAreas: ["data_collection"],
        adaptiveRecommendations: ["Aumentar tempo de jogo para coleta de dados adequada"]
      },
      metadata: {
        dataSource: "MusicalSequenceProcessors_fallback",
        collectorsUsed: [],
        processingVersion: "3.0.0",
        analysisTimestamp: (/* @__PURE__ */ new Date()).toISOString(),
        dataQuality: this.assessDataQuality(gameData),
        fallbackReason: "insufficient_data_or_error"
      }
    };
  }
  getIntegratedFallback() {
    return {
      overallScore: 0.5,
      auditoryProfile: "assessment_needed",
      musicalProfile: "assessment_needed",
      cognitiveProfile: "assessment_needed",
      developmentLevel: "unknown",
      therapeuticTargets: ["establish_baseline"],
      interventionPlan: { priority: "data_collection", strategies: [] },
      progressTracking: { milestones: [], targets: [] },
      adaptiveStrategies: [],
      strengthsIntegration: [],
      challengesIntegration: ["insufficient_data"],
      recommendationsIntegration: ["Coletar dados suficientes para análise terapêutica adequada"]
    };
  }
  /**
   * Processa coletores com Circuit Breaker para resiliência
   * @param {Object} collectorsHub - Hub de coletores
   * @param {Object} gameData - Dados do jogo
   * @returns {Promise<Object>} Resultados dos coletores
   */
  async processCollectorsWithCircuitBreaker(collectorsHub2, gameData) {
    if (!collectorsHub2 || !collectorsHub2.collectors) {
      this.logger?.warn("⚠️ MusicalSequence: Hub de coletores não disponível");
      return { collectors: {}, warning: "No collectors available" };
    }
    const results = {};
    const collectors = collectorsHub2.collectors;
    for (const [collectorName, collector] of Object.entries(collectors)) {
      try {
        if (collector && typeof collector.analyze === "function") {
          this.logger?.debug("🎮 Processando coletor: " + collectorName);
          results[collectorName] = await this.processWithTimeout(
            () => collector.analyze(gameData),
            5e3,
            // 5 segundos timeout
            collectorName + " timeout"
          );
        } else {
          this.logger?.warn("⚠️ Coletor " + collectorName + " não tem método analyze");
          results[collectorName] = { error: "No analyze method" };
        }
      } catch (error) {
        this.logger?.error("❌ MusicalSequenceProcessors: Erro no coletor " + collectorName + ":", {
          error: error.message,
          stack: error.stack?.substring(0, 300),
          collectorName,
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        });
        results[collectorName] = {
          error: error.message,
          fallback: this.generateFallbackMetrics(collectorName, gameData),
          recovered: true
        };
      }
    }
    return {
      collectors: results,
      processedAt: (/* @__PURE__ */ new Date()).toISOString(),
      gameType: "MusicalSequence"
    };
  }
  /**
   * Processa com timeout para evitar travamentos
   */
  async processWithTimeout(fn, timeout, errorMsg) {
    return Promise.race([
      fn(),
      new Promise(
        (_, reject) => setTimeout(() => reject(new Error(errorMsg)), timeout)
      )
    ]);
  }
  /**
   * Gera métricas de fallback em caso de erro
   */
  generateFallbackMetrics(collectorName, gameData) {
    return {
      fallback: true,
      collector: collectorName,
      basicScore: 50,
      confidence: "low",
      note: "Generated due to collector error"
    };
  }
  /**
   * Calcula memória de trabalho auditiva
   */
  calculateAuditoryWorkingMemory(sequences) {
    if (!sequences || sequences.length === 0) {
      return { score: 0, level: "insufficient_data" };
    }
    let totalScore = 0;
    let validSequences = 0;
    sequences.forEach((seq) => {
      if (seq.sequenceLength && seq.correct !== void 0) {
        const memoryLoad = seq.sequenceLength / 10;
        const success = seq.correct ? 1 : 0;
        totalScore += success * memoryLoad;
        validSequences++;
      }
    });
    const score = validSequences > 0 ? totalScore / validSequences : 0;
    return {
      score,
      level: score > 0.7 ? "high" : score > 0.4 ? "medium" : "low",
      capacity: Math.min(10, Math.floor(score * 10) + 3)
    };
  }
  /**
   * Gera perfil auditivo
   */
  generateAuditoryProfile(auditoryMemory, sequenceExecution, musicalPatterns) {
    return {
      memoryStrength: auditoryMemory?.level || "unknown",
      executionAbility: sequenceExecution?.level || "unknown",
      patternRecognition: musicalPatterns?.level || "unknown",
      overallProfile: this.determineAuditoryProfile(auditoryMemory, sequenceExecution, musicalPatterns),
      recommendations: this.generateAuditoryRecommendations(auditoryMemory, sequenceExecution)
    };
  }
  /**
   * Determina perfil auditivo geral
   */
  determineAuditoryProfile(memory, execution, patterns) {
    const scores = [
      memory?.score || 0,
      execution?.score || 0,
      patterns?.score || 0
    ];
    const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length;
    if (avgScore > 0.8) return "strong_auditory_processor";
    if (avgScore > 0.6) return "developing_auditory_skills";
    if (avgScore > 0.4) return "emerging_auditory_abilities";
    return "needs_auditory_support";
  }
  /**
   * Gera recomendações auditivas
   */
  generateAuditoryRecommendations(memory, execution) {
    const recommendations = [];
    if (memory?.score < 0.5) {
      recommendations.push("auditory_memory_exercises");
    }
    if (execution?.score < 0.5) {
      recommendations.push("sequence_practice");
    }
    return recommendations;
  }
  /**
   * Sintetiza processamento auditivo
   */
  synthesizeAuditoryProcessing(auditoryMemory, sequenceExecution, musicalPatterns, integratedAnalysis) {
    return {
      cognitiveProfile: {
        auditoryMemory: auditoryMemory?.level || "unknown",
        sequenceProcessing: sequenceExecution?.level || "unknown",
        musicalCognition: musicalPatterns?.level || "unknown"
      },
      therapeuticFocus: this.identifyTherapeuticFocus(auditoryMemory, sequenceExecution, musicalPatterns),
      developmentalStage: this.assessDevelopmentalStage(integratedAnalysis),
      interventionPriority: this.determineInterventionPriority(auditoryMemory, sequenceExecution)
    };
  }
  /**
   * Identifica foco terapêutico
   */
  identifyTherapeuticFocus(memory, execution, patterns) {
    const focus = [];
    if (memory?.score < 0.6) focus.push("auditory_memory");
    if (execution?.score < 0.6) focus.push("sequence_execution");
    if (patterns?.score < 0.6) focus.push("pattern_recognition");
    return focus.length > 0 ? focus : ["maintenance"];
  }
  /**
   * Avalia estágio de desenvolvimento
   */
  assessDevelopmentalStage(analysis) {
    if (!analysis) return "assessment_needed";
    const overallScore = analysis.overallScore || 0;
    if (overallScore > 0.8) return "advanced";
    if (overallScore > 0.6) return "proficient";
    if (overallScore > 0.4) return "developing";
    return "emerging";
  }
  /**
   * Determina prioridade de intervenção
   */
  determineInterventionPriority(memory, execution) {
    const memoryScore = memory?.score || 0;
    const executionScore = execution?.score || 0;
    if (memoryScore < 0.3 || executionScore < 0.3) return "high";
    if (memoryScore < 0.6 || executionScore < 0.6) return "medium";
    return "low";
  }
  /**
   * Gera análise terapêutica completa
   * @param {Object} metrics - Métricas processadas
   * @param {Object} gameData - Dados do jogo
   * @returns {Object} Análise terapêutica
   */
  generateTherapeuticAnalysis(metrics, gameData) {
    try {
      const analysis = {
        // Análise comportamental
        behavioral: {
          engagement: this.calculateEngagementScore(gameData),
          persistence: this.calculatePersistenceScore(gameData),
          adaptability: this.calculateAdaptabilityScore(gameData),
          frustrationTolerance: this.calculateFrustrationTolerance(gameData),
          socialInteraction: this.calculateSocialInteractionScore(gameData)
        },
        // Análise cognitiva
        cognitive: {
          attention: this.calculateAttentionScore(gameData),
          memory: this.calculateMemoryScore(gameData),
          processingSpeed: this.calculateProcessingSpeedScore(gameData),
          executiveFunction: this.calculateExecutiveFunctionScore(gameData),
          visualProcessing: this.calculateVisualProcessingScore(gameData)
        },
        // Análise sensorial
        sensory: {
          visualPerception: this.calculateVisualPerceptionScore(gameData),
          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),
          tactileProcessing: this.calculateTactileProcessingScore(gameData),
          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)
        },
        // Análise motora
        motor: {
          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),
          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),
          coordination: this.calculateCoordinationScore(gameData),
          motorPlanning: this.calculateMotorPlanningScore(gameData)
        },
        // Recomendações terapêuticas
        recommendations: this.generateTherapeuticRecommendations(metrics, gameData),
        // Indicadores de progresso
        progressIndicators: this.generateProgressIndicators(metrics, gameData),
        // Insights específicos do jogo
        gameSpecificInsights: this.generateGameSpecificInsights(metrics, gameData),
        // Metadados
        metadata: {
          analysisTimestamp: (/* @__PURE__ */ new Date()).toISOString(),
          gameType: this.gameType,
          analysisVersion: "3.0.0",
          confidenceScore: this.calculateAnalysisConfidenceScore(metrics, gameData)
        }
      };
      return analysis;
    } catch (error) {
      this.logger?.error("❌ Erro ao gerar análise terapêutica:", error);
      return this.generateFallbackTherapeuticAnalysis(gameData);
    }
  }
  /**
   * Métodos de cálculo de scores terapêuticos
   */
  calculateEngagementScore(gameData) {
    const interactions = gameData.interactions || [];
    const totalTime = gameData.totalTime || 1e3;
    const completionRate = gameData.completionRate || 0;
    let score = 50;
    if (interactions.length > 0) {
      score += Math.min(30, interactions.length * 2);
    }
    if (totalTime > 3e4) {
      score += 10;
    }
    score += completionRate * 10;
    return Math.max(0, Math.min(100, score));
  }
  calculatePersistenceScore(gameData) {
    const attempts = gameData.attempts || 1;
    const errors = gameData.errors || 0;
    const completion = gameData.completion || 0;
    let score = 50;
    if (attempts > 1 && completion > 0.5) {
      score += 20;
    }
    if (errors > 0 && completion > 0.8) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateAdaptabilityScore(gameData) {
    const difficultyChanges = gameData.difficultyChanges || 0;
    const adaptationSuccess = gameData.adaptationSuccess || 0;
    let score = 50;
    if (difficultyChanges > 0) {
      score += adaptationSuccess * 20;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateFrustrationTolerance(gameData) {
    const errors = gameData.errors || 0;
    const quitEarly = gameData.quitEarly || false;
    const completion = gameData.completion || 0;
    let score = 70;
    if (errors > 3 && !quitEarly) {
      score += 15;
    }
    if (completion > 0.8) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateSocialInteractionScore(gameData) {
    const engagement = this.calculateEngagementScore(gameData);
    return Math.max(30, Math.min(80, engagement * 0.8));
  }
  calculateAttentionScore(gameData) {
    const focusTime = gameData.focusTime || 0;
    const distractions = gameData.distractions || 0;
    const responseTime = gameData.averageResponseTime || 3e3;
    let score = 50;
    if (focusTime > 6e4) {
      score += 20;
    }
    if (distractions < 2) {
      score += 15;
    }
    if (responseTime < 2e3) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateMemoryScore(gameData) {
    const accuracy = gameData.accuracy || 0;
    const patterns = gameData.patterns || [];
    let score = 50;
    if (accuracy > 70) {
      score += 25;
    }
    if (patterns.length > 3) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateProcessingSpeedScore(gameData) {
    const responseTime = gameData.averageResponseTime || 3e3;
    const accuracy = gameData.accuracy || 0;
    let score = 50;
    if (responseTime < 1500 && accuracy > 60) {
      score += 30;
    } else if (responseTime < 2500) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateExecutiveFunctionScore(gameData) {
    const planningEvidence = gameData.planningEvidence || 0;
    const inhibitionControl = gameData.inhibitionControl || 0;
    const workingMemory = gameData.workingMemory || 0;
    const score = (planningEvidence + inhibitionControl + workingMemory) / 3;
    return Math.max(0, Math.min(100, score));
  }
  calculateVisualProcessingScore(gameData) {
    const visualTasks = gameData.visualTasks || 0;
    const visualAccuracy = gameData.visualAccuracy || 0;
    let score = 50;
    if (visualTasks > 5 && visualAccuracy > 70) {
      score += 25;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateVisualPerceptionScore(gameData) {
    return this.calculateVisualProcessingScore(gameData);
  }
  calculateAuditoryProcessingScore(gameData) {
    const auditoryTasks = gameData.auditoryTasks || 0;
    const auditoryAccuracy = gameData.auditoryAccuracy || 50;
    let score = 50;
    if (auditoryTasks > 0) {
      score = auditoryAccuracy;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateTactileProcessingScore(gameData) {
    const touchInteractions = gameData.touchInteractions || 0;
    const touchAccuracy = gameData.touchAccuracy || 50;
    let score = 50;
    if (touchInteractions > 3) {
      score = touchAccuracy;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateSensoryIntegrationScore(gameData) {
    const visual = this.calculateVisualPerceptionScore(gameData);
    const auditory = this.calculateAuditoryProcessingScore(gameData);
    const tactile = this.calculateTactileProcessingScore(gameData);
    return (visual + auditory + tactile) / 3;
  }
  calculateFineMotorSkillsScore(gameData) {
    const precision = gameData.precision || 50;
    const motorControl = gameData.motorControl || 50;
    return (precision + motorControl) / 2;
  }
  calculateGrossMotorSkillsScore(gameData) {
    const movements = gameData.movements || 0;
    const coordination = gameData.coordination || 50;
    let score = 50;
    if (movements > 10) {
      score = coordination;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateCoordinationScore(gameData) {
    const eyeHandCoordination = gameData.eyeHandCoordination || 50;
    const bilateralCoordination = gameData.bilateralCoordination || 50;
    return (eyeHandCoordination + bilateralCoordination) / 2;
  }
  calculateMotorPlanningScore(gameData) {
    const planningSteps = gameData.planningSteps || 0;
    const executionSuccess = gameData.executionSuccess || 0;
    let score = 50;
    if (planningSteps > 0) {
      score = executionSuccess;
    }
    return Math.max(0, Math.min(100, score));
  }
  generateTherapeuticRecommendations(metrics, gameData) {
    const recommendations = [];
    const engagement = this.calculateEngagementScore(gameData);
    if (engagement < 50) {
      recommendations.push({
        category: "engagement",
        priority: "high",
        recommendation: "Implementar estratégias de motivação e gamificação",
        rationale: "Baixo engajamento detectado"
      });
    }
    const attention = this.calculateAttentionScore(gameData);
    if (attention < 50) {
      recommendations.push({
        category: "attention",
        priority: "medium",
        recommendation: "Exercícios de foco e concentração",
        rationale: "Dificuldades atencionais identificadas"
      });
    }
    const processing = this.calculateProcessingSpeedScore(gameData);
    if (processing < 50) {
      recommendations.push({
        category: "processing",
        priority: "medium",
        recommendation: "Atividades para melhorar velocidade de processamento",
        rationale: "Processamento lento identificado"
      });
    }
    return recommendations;
  }
  generateProgressIndicators(metrics, gameData) {
    return {
      overallProgress: this.calculateOverallProgress(gameData),
      strengthAreas: this.identifyStrengthAreas(gameData),
      challengeAreas: this.identifyChallengeAreas(gameData),
      developmentGoals: this.generateDevelopmentGoals(gameData),
      milestones: this.generateMilestones(gameData)
    };
  }
  generateGameSpecificInsights(metrics, gameData) {
    return {
      gameType: this.gameType,
      specificMetrics: metrics,
      gamePerformance: this.calculateGamePerformance(gameData),
      adaptationNeeds: this.identifyAdaptationNeeds(gameData)
    };
  }
  calculateAnalysisConfidenceScore(metrics, gameData) {
    let confidence = 50;
    const dataPoints = Object.keys(gameData).length;
    if (dataPoints > 10) confidence += 20;
    else if (dataPoints > 5) confidence += 10;
    const metricsCount = Object.keys(metrics).length;
    if (metricsCount > 5) confidence += 20;
    else if (metricsCount > 3) confidence += 10;
    const sessionTime = gameData.totalTime || 0;
    if (sessionTime > 6e4) confidence += 10;
    return Math.max(0, Math.min(100, confidence));
  }
  generateFallbackTherapeuticAnalysis(gameData) {
    return {
      behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50, socialInteraction: 50 },
      cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50, visualProcessing: 50 },
      sensory: { visualPerception: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },
      motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },
      recommendations: [],
      progressIndicators: { overallProgress: 50, strengthAreas: [], challengeAreas: [], developmentGoals: [], milestones: [] },
      gameSpecificInsights: { gameType: this.gameType, specificMetrics: {}, gamePerformance: 50, adaptationNeeds: [] },
      metadata: { analysisTimestamp: (/* @__PURE__ */ new Date()).toISOString(), gameType: this.gameType, analysisVersion: "3.0.0", confidenceScore: 30 }
    };
  }
  calculateOverallProgress(gameData) {
    const accuracy = gameData.accuracy || 0;
    const completion = gameData.completion || 0;
    const engagement = this.calculateEngagementScore(gameData);
    return (accuracy + completion + engagement) / 3;
  }
  identifyStrengthAreas(gameData) {
    const strengths = [];
    if (gameData.accuracy > 80) strengths.push("Precisão");
    if (gameData.averageResponseTime < 2e3) strengths.push("Velocidade de resposta");
    if (this.calculateEngagementScore(gameData) > 70) strengths.push("Engajamento");
    return strengths;
  }
  identifyChallengeAreas(gameData) {
    const challenges = [];
    if (gameData.accuracy < 50) challenges.push("Precisão");
    if (gameData.averageResponseTime > 4e3) challenges.push("Velocidade de resposta");
    if (this.calculateEngagementScore(gameData) < 40) challenges.push("Engajamento");
    return challenges;
  }
  generateDevelopmentGoals(gameData) {
    const goals = [];
    if (gameData.accuracy < 70) {
      goals.push("Melhorar precisão para 70%+");
    }
    if (gameData.averageResponseTime > 3e3) {
      goals.push("Reduzir tempo de resposta para menos de 3 segundos");
    }
    return goals;
  }
  generateMilestones(gameData) {
    return [
      { milestone: "Primeira sessão completa", achieved: gameData.completion > 0.8 },
      { milestone: "Precisão acima de 50%", achieved: gameData.accuracy > 50 },
      { milestone: "Engajamento sustentado", achieved: this.calculateEngagementScore(gameData) > 60 }
    ];
  }
  calculateGamePerformance(gameData) {
    const accuracy = gameData.accuracy || 0;
    const completion = gameData.completion || 0;
    const efficiency = gameData.efficiency || 0;
    return (accuracy + completion + efficiency) / 3;
  }
  identifyAdaptationNeeds(gameData) {
    const needs = [];
    if (gameData.accuracy < 40) {
      needs.push("Reduzir dificuldade");
    }
    if (gameData.averageResponseTime > 5e3) {
      needs.push("Aumentar tempo limite");
    }
    if (this.calculateEngagementScore(gameData) < 30) {
      needs.push("Aumentar elementos motivacionais");
    }
    return needs;
  }
  /**
   * Processa métricas para estrutura padronizada do banco de dados
   * @param {Object} metrics - Métricas processadas
   * @param {Object} gameData - Dados do jogo
   * @returns {Object} Métricas processadas para banco
   */
  processMetricsForDatabase(metrics, gameData) {
    try {
      return {
        // Métricas básicas
        basic: {
          accuracy: gameData.accuracy || 0,
          responseTime: gameData.averageResponseTime || 0,
          completion: gameData.completion || 0,
          score: gameData.score || 0,
          duration: gameData.totalTime || 0,
          attempts: gameData.attempts || 1,
          errors: gameData.errors || 0
        },
        // Métricas cognitivas
        cognitive: {
          attention: this.calculateAttentionScore(gameData),
          memory: this.calculateMemoryScore(gameData),
          processingSpeed: this.calculateProcessingSpeedScore(gameData),
          executiveFunction: this.calculateExecutiveFunctionScore(gameData)
        },
        // Métricas comportamentais
        behavioral: {
          engagement: this.calculateEngagementScore(gameData),
          persistence: this.calculatePersistenceScore(gameData),
          adaptability: this.calculateAdaptabilityScore(gameData),
          frustrationTolerance: this.calculateFrustrationTolerance(gameData)
        },
        // Métricas sensoriais
        sensory: {
          visualProcessing: this.calculateVisualProcessingScore(gameData),
          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),
          tactileProcessing: this.calculateTactileProcessingScore(gameData),
          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)
        },
        // Métricas motoras
        motor: {
          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),
          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),
          coordination: this.calculateCoordinationScore(gameData),
          motorPlanning: this.calculateMotorPlanningScore(gameData)
        },
        // Métricas específicas do jogo
        gameSpecific: metrics,
        // Metadados
        metadata: {
          gameType: this.gameType,
          processingTimestamp: (/* @__PURE__ */ new Date()).toISOString(),
          version: "3.0.0"
        }
      };
    } catch (error) {
      this.logger?.error("❌ Erro ao processar métricas para banco:", error);
      return {
        basic: { accuracy: 0, responseTime: 0, completion: 0, score: 0, duration: 0, attempts: 1, errors: 0 },
        cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50 },
        behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50 },
        sensory: { visualProcessing: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },
        motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },
        gameSpecific: metrics,
        metadata: { gameType: this.gameType, processingTimestamp: (/* @__PURE__ */ new Date()).toISOString(), version: "3.0.0" }
      };
    }
  }
  /**
   * Métodos de análise para MusicalSequence
   */
  analyzeMusicalSequencePrimary(gameData) {
    const { totalCorrect = 0, totalAttempts = 1, interactions = [] } = gameData;
    return {
      accuracy: Math.round(totalCorrect / totalAttempts * 100),
      totalAttempts,
      totalCorrect,
      primaryScore: this.calculatePrimaryScore(interactions),
      efficiency: this.calculateEfficiency(interactions)
    };
  }
  analyzeMusicalSequenceSecondary(gameData) {
    const { interactions = [] } = gameData;
    return {
      secondaryAccuracy: this.calculateSecondaryAccuracy(interactions),
      adaptability: this.calculateAdaptability(interactions),
      consistency: this.calculateConsistency(interactions)
    };
  }
  analyzeMusicalSequenceTiming(gameData) {
    const { interactions = [] } = gameData;
    const responseTimes = interactions.map((i) => i.responseTime || 0).filter((t) => t > 0);
    if (responseTimes.length === 0) {
      return { average: 0, median: 0, variability: 0, pattern: "insufficient_data" };
    }
    const average = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const sorted = responseTimes.sort((a, b) => a - b);
    const median = sorted[Math.floor(sorted.length / 2)];
    return {
      average: Math.round(average),
      median: Math.round(median),
      min: Math.min(...responseTimes),
      max: Math.max(...responseTimes),
      variability: Math.round(this.calculateVariability(responseTimes)),
      pattern: "normal"
    };
  }
  analyzeMusicalSequencePatterns(gameData) {
    const { interactions = [] } = gameData;
    return {
      totalPatterns: interactions.length,
      correctPatterns: interactions.filter((i) => i.correct).length,
      patternTypes: this.identifyPatternTypes(interactions),
      errorPatterns: this.identifyErrorPatterns(interactions)
    };
  }
  analyzeMusicalSequenceBehavior(gameData) {
    const { interactions = [] } = gameData;
    return {
      persistence: this.calculatePersistence(interactions),
      adaptability: this.calculateAdaptability(interactions),
      engagement: this.calculateEngagementScore(gameData)
    };
  }
  analyzeMusicalSequenceCognition(gameData) {
    const { interactions = [] } = gameData;
    return {
      executiveFunction: this.calculateExecutiveFunction(interactions),
      workingMemory: this.calculateWorkingMemory(interactions),
      processingSpeed: this.calculateProcessingSpeed(interactions)
    };
  }
  generateMusicalSequenceRecommendations(gameData) {
    const recommendations = [];
    const accuracy = gameData.accuracy || 0;
    const responseTime = gameData.averageResponseTime || 0;
    if (accuracy < 60) {
      recommendations.push({
        type: "accuracy_improvement",
        priority: "high",
        description: "Exercícios para melhorar precisão em MusicalSequence"
      });
    }
    if (responseTime > 5e3) {
      recommendations.push({
        type: "speed_improvement",
        priority: "medium",
        description: "Atividades para melhorar velocidade de processamento"
      });
    }
    return recommendations;
  }
  // Métodos auxiliares
  calculatePrimaryScore(interactions) {
    const correctInteractions = interactions.filter((i) => i.correct);
    return correctInteractions.length / Math.max(1, interactions.length) * 100;
  }
  calculateSecondaryAccuracy(interactions) {
    return this.calculatePrimaryScore(interactions);
  }
  calculateEfficiency(interactions) {
    const totalTime = interactions.reduce((sum, i) => sum + (i.responseTime || 0), 0);
    const correctCount = interactions.filter((i) => i.correct).length;
    return totalTime > 0 ? correctCount / totalTime * 1e3 : 0;
  }
  identifyPatternTypes(interactions) {
    const types = {};
    interactions.forEach((i) => {
      const type = i.patternType || "unknown";
      types[type] = (types[type] || 0) + 1;
    });
    return types;
  }
  identifyErrorPatterns(interactions) {
    const errors = interactions.filter((i) => !i.correct);
    return {
      totalErrors: errors.length,
      errorFrequency: errors.length / Math.max(1, interactions.length) * 100
    };
  }
  calculateVariability(values) {
    if (values.length < 2) return 0;
    const mean = values.reduce((sum, v) => sum + v, 0) / values.length;
    const variance = values.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }
  /**
   * Calcula memória de trabalho auditiva
   */
  /**
   * Gera perfil auditivo
   */
  /**
   * Determina perfil auditivo geral
   */
  /**
   * Gera recomendações auditivas
   */
  /**
   * Sintetiza processamento auditivo
   */
  // Métodos auxiliares completos para todos os processadores
  calculateAdaptability(interactions) {
    if (interactions.length < 2) return 50;
    let adaptations = 0;
    for (let i = 1; i < interactions.length; i++) {
      if (!interactions[i - 1].correct && interactions[i].correct) {
        adaptations++;
      }
    }
    return adaptations / Math.max(1, interactions.length - 1) * 100;
  }
  calculatePersistence(interactions) {
    if (interactions.length === 0) return 50;
    const errorRate = interactions.filter((i) => !i.correct).length / interactions.length;
    return Math.max(0, 100 - errorRate * 100);
  }
  calculateConsistency(interactions) {
    if (interactions.length === 0) return 50;
    const responseTimes = interactions.map((i) => i.responseTime || 0);
    const avgTime = responseTimes.reduce((sum, t) => sum + t, 0) / responseTimes.length;
    if (avgTime === 0) return 50;
    const variance = responseTimes.reduce((sum, t) => sum + Math.pow(t - avgTime, 2), 0) / responseTimes.length;
    const stdDev = Math.sqrt(variance);
    return Math.max(0, 100 - stdDev / avgTime * 100);
  }
  calculateConsistencyScore(interactions) {
    return this.calculateConsistency(interactions);
  }
  calculateAccuracyTrend(interactions) {
    if (interactions.length < 2) return "insufficient_data";
    const halfPoint = Math.floor(interactions.length / 2);
    const firstHalf = interactions.slice(0, halfPoint);
    const secondHalf = interactions.slice(halfPoint);
    const firstAccuracy = firstHalf.filter((i) => i.correct).length / firstHalf.length;
    const secondAccuracy = secondHalf.filter((i) => i.correct).length / secondHalf.length;
    if (secondAccuracy > firstAccuracy + 0.1) return "improving";
    if (secondAccuracy < firstAccuracy - 0.1) return "declining";
    return "stable";
  }
  calculateSpeedImprovement(interactions) {
    if (interactions.length < 2) return 0;
    const halfPoint = Math.floor(interactions.length / 2);
    const firstHalf = interactions.slice(0, halfPoint);
    const secondHalf = interactions.slice(halfPoint);
    const firstAvgTime = firstHalf.reduce((sum, i) => sum + (i.responseTime || 0), 0) / firstHalf.length;
    const secondAvgTime = secondHalf.reduce((sum, i) => sum + (i.responseTime || 0), 0) / secondHalf.length;
    return firstAvgTime > 0 ? (firstAvgTime - secondAvgTime) / firstAvgTime * 100 : 0;
  }
  calculateLearningRate(interactions) {
    if (interactions.length < 3) return 0;
    let improvementCount = 0;
    for (let i = 1; i < interactions.length; i++) {
      if (interactions[i].correct && !interactions[i - 1].correct) {
        improvementCount++;
      }
    }
    return improvementCount / (interactions.length - 1) * 100;
  }
  identifyResponsePattern(responseTimes) {
    if (responseTimes.length === 0) return "insufficient_data";
    const average = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const variance = responseTimes.reduce((sum, time) => sum + Math.pow(time - average, 2), 0) / responseTimes.length;
    const stdDev = Math.sqrt(variance);
    if (stdDev < average * 0.2) return "consistent";
    if (stdDev < average * 0.5) return "moderate_variation";
    return "high_variation";
  }
  generateProgressMarkers(gameData) {
    const { interactions = [] } = gameData;
    return {
      accuracyTrend: this.calculateAccuracyTrend(interactions),
      speedImprovement: this.calculateSpeedImprovement(interactions),
      consistencyScore: this.calculateConsistencyScore(interactions),
      learningRate: this.calculateLearningRate(interactions)
    };
  }
  // Métodos específicos para processamento de métricas
  /**
   * Processa métricas específicas do MusicalSequence
   * @param {Object} gameData - Dados do jogo MusicalSequence
   * @param {Object} sessionData - Dados da sessão
   * @returns {Object} Métricas processadas
   */
  async processMusicalSequenceMetrics(gameData, sessionData) {
    try {
      this.logger?.info("🎵 Processando métricas MusicalSequence...", {
        sessionId: sessionData.sessionId
      });
      const metrics = {
        // Métricas de processamento auditivo
        auditoryProcessing: this.analyzeMusicalSequencePrimary(gameData),
        // Análise de memória de sequência
        sequenceMemory: this.analyzeMusicalSequenceSecondary(gameData),
        // Cognição musical
        musicalCognition: this.analyzeMusicalSequenceTertiary(gameData),
        // Padrões musicais
        musicalPatterns: this.analyzeMusicalSequencePatterns(gameData),
        // Análise comportamental específica
        musicalBehavior: this.analyzeMusicalSequenceBehavior(gameData),
        // Indicadores terapêuticos
        therapeuticIndicators: this.generateMusicalSequenceTherapeuticIndicators(gameData),
        // Recomendações específicas
        recommendations: this.generateMusicalSequenceRecommendations(gameData)
      };
      this.logger?.info("✅ Métricas MusicalSequence processadas", {
        accuracy: metrics.auditoryProcessing.accuracy,
        sequenceLength: metrics.sequenceMemory.maxSequenceLength
      });
      return metrics;
    } catch (error) {
      this.logger?.error("❌ Erro ao processar métricas MusicalSequence:", error);
      throw error;
    }
  }
  /**
   * Gera indicadores terapêuticos específicos para MusicalSequence
   */
  generateMusicalSequenceTherapeuticIndicators(gameData) {
    const { interactions = [], accuracy = 0 } = gameData;
    return {
      cognitiveLoad: this.assessCognitiveLoad(gameData),
      therapeuticGoals: this.identifyMusicalSequenceTherapeuticGoals(gameData),
      interventionNeeds: this.identifyMusicalSequenceInterventionNeeds(gameData),
      progressMarkers: this.generateMusicalSequenceProgressMarkers(gameData)
    };
  }
  /**
   * Identifica objetivos terapêuticos específicos
   */
  identifyMusicalSequenceTherapeuticGoals(gameData) {
    const { interactions = [], accuracy = 0 } = gameData;
    const goals = [];
    if (accuracy < 60) {
      goals.push("Melhorar processamento auditivo");
    }
    if (interactions.length > 0) {
      const avgTime = interactions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / interactions.length;
      if (avgTime > 5e3) {
        goals.push("Acelerar processamento de sequências");
      }
    }
    return goals;
  }
  /**
   * Identifica necessidades de intervenção específicas
   */
  identifyMusicalSequenceInterventionNeeds(gameData) {
    const { interactions = [], accuracy = 0 } = gameData;
    const needs = [];
    if (accuracy < 40) {
      needs.push("Intervenção intensiva em processamento auditivo");
    }
    return needs;
  }
  /**
   * Gera marcadores de progresso específicos
   */
  generateMusicalSequenceProgressMarkers(gameData) {
    const { interactions = [] } = gameData;
    return {
      accuracyTrend: this.calculateAccuracyTrend(interactions),
      speedImprovement: this.calculateSpeedImprovement(interactions),
      consistencyScore: this.calculateConsistencyScore(interactions),
      learningRate: this.calculateLearningRate(interactions)
    };
  }
  /**
   * Avalia carga cognitiva
   */
  assessCognitiveLoad(gameData) {
    const { interactions = [], averageResponseTime = 0 } = gameData;
    if (interactions.length === 0) return "low";
    const avgTime = averageResponseTime || interactions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / interactions.length;
    const errorRate = interactions.filter((i) => !i.correct).length / interactions.length;
    if (avgTime > 6e3 || errorRate > 0.8) return "high";
    if (avgTime > 4e3 || errorRate > 0.5) return "medium";
    return "low";
  }
  /**
   * Análise terciária do MusicalSequence
   */
  analyzeMusicalSequenceTertiary(gameData) {
    const { interactions = [] } = gameData;
    return {
      musicalCognition: this.calculateMusicalCognition(interactions),
      rhythmRecognition: this.calculateRhythmRecognition(interactions),
      auditoryMemory: this.calculateAuditoryMemory(interactions)
    };
  }
  calculateMusicalCognition(interactions) {
    if (interactions.length === 0) return 50;
    const correctInteractions = interactions.filter((i) => i.correct);
    return correctInteractions.length / interactions.length * 100;
  }
  calculateRhythmRecognition(interactions) {
    if (interactions.length === 0) return 50;
    const rhythmAccuracy = interactions.filter((i) => i.correct && i.rhythm).length;
    return rhythmAccuracy > 0 ? rhythmAccuracy / interactions.length * 100 : 50;
  }
  calculateAuditoryMemory(interactions) {
    if (interactions.length === 0) return 50;
    const sequenceAccuracy = interactions.filter((i) => i.correct && i.sequence).length;
    return sequenceAccuracy > 0 ? sequenceAccuracy / interactions.length * 100 : 50;
  }
}
const musicalSequenceGame = "_musicalSequenceGame_1sa4m_41";
const gameContent = "_gameContent_1sa4m_63";
const gameHeader = "_gameHeader_1sa4m_83";
const gameTitle = "_gameTitle_1sa4m_111";
const headerTtsButton = "_headerTtsButton_1sa4m_157";
const ttsActive = "_ttsActive_1sa4m_205";
const gameStats = "_gameStats_1sa4m_217";
const statCard = "_statCard_1sa4m_231";
const statValue = "_statValue_1sa4m_275";
const statLabel = "_statLabel_1sa4m_289";
const questionArea = "_questionArea_1sa4m_307";
const questionHeader = "_questionHeader_1sa4m_331";
const questionTitle = "_questionTitle_1sa4m_339";
const objectsDisplay = "_objectsDisplay_1sa4m_399";
const sequenceSlots = "_sequenceSlots_1sa4m_429";
const countingObject = "_countingObject_1sa4m_447";
const answerOptions = "_answerOptions_1sa4m_563";
const pitchComparisonContainer = "_pitchComparisonContainer_1sa4m_579";
const pitchCard = "_pitchCard_1sa4m_599";
const pitchIcon = "_pitchIcon_1sa4m_657";
const pitchTitle = "_pitchTitle_1sa4m_669";
const pitchFrequency = "_pitchFrequency_1sa4m_685";
const pitchButton = "_pitchButton_1sa4m_699";
const referenceButton = "_referenceButton_1sa4m_755";
const testButton = "_testButton_1sa4m_773";
const vsIndicator = "_vsIndicator_1sa4m_791";
const answerIcon = "_answerIcon_1sa4m_809";
const answerText = "_answerText_1sa4m_821";
const higherButton = "_higherButton_1sa4m_833";
const sameButton = "_sameButton_1sa4m_851";
const lowerButton = "_lowerButton_1sa4m_869";
const feedbackContainer = "_feedbackContainer_1sa4m_889";
const correctFeedback = "_correctFeedback_1sa4m_911";
const incorrectFeedback = "_incorrectFeedback_1sa4m_921";
const feedbackIcon = "_feedbackIcon_1sa4m_931";
const feedbackTitle = "_feedbackTitle_1sa4m_943";
const feedbackText = "_feedbackText_1sa4m_959";
const patternContainer = "_patternContainer_1sa4m_975";
const patternHeader = "_patternHeader_1sa4m_993";
const patternIcon = "_patternIcon_1sa4m_1003";
const patternTitle = "_patternTitle_1sa4m_1015";
const patternDescription = "_patternDescription_1sa4m_1031";
const patternSequence = "_patternSequence_1sa4m_1045";
const patternItem = "_patternItem_1sa4m_1071";
const patternEmoji = "_patternEmoji_1sa4m_1111";
const patternName = "_patternName_1sa4m_1121";
const patternHint = "_patternHint_1sa4m_1137";
const roundProgress = "_roundProgress_1sa4m_1157";
const correct = "_correct_1sa4m_911";
const incorrect = "_incorrect_1sa4m_921";
const answerButton = "_answerButton_1sa4m_1321";
const gameControls = "_gameControls_1sa4m_1485";
const controlButton = "_controlButton_1sa4m_1501";
const activityMenu = "_activityMenu_1sa4m_2893";
const activityButton = "_activityButton_1sa4m_2909";
const active = "_active_1sa4m_2947";
const playing = "_playing_1sa4m_3267";
const rhythmDisplay = "_rhythmDisplay_1sa4m_3597";
const styles = {
  musicalSequenceGame,
  gameContent,
  gameHeader,
  gameTitle,
  headerTtsButton,
  ttsActive,
  gameStats,
  statCard,
  statValue,
  statLabel,
  questionArea,
  questionHeader,
  questionTitle,
  objectsDisplay,
  sequenceSlots,
  countingObject,
  answerOptions,
  pitchComparisonContainer,
  pitchCard,
  pitchIcon,
  pitchTitle,
  pitchFrequency,
  pitchButton,
  referenceButton,
  testButton,
  vsIndicator,
  answerIcon,
  answerText,
  higherButton,
  sameButton,
  lowerButton,
  feedbackContainer,
  correctFeedback,
  incorrectFeedback,
  feedbackIcon,
  feedbackTitle,
  feedbackText,
  patternContainer,
  patternHeader,
  patternIcon,
  patternTitle,
  patternDescription,
  patternSequence,
  patternItem,
  patternEmoji,
  patternName,
  patternHint,
  roundProgress,
  correct,
  incorrect,
  answerButton,
  gameControls,
  controlButton,
  activityMenu,
  activityButton,
  active,
  playing,
  rhythmDisplay
};
const MusicalSequenceConfig = {
  // 🎯 CONFIGURAÇÃO DE RODADAS - 4 A 7 RODADAS POR DIFICULDADE
  roundsConfig: {
    minRounds: 4,
    maxRounds: 7,
    easy: 4,
    medium: 5,
    hard: 7
  }
};
let collectorsHub = null;
const MusicalSequenceMetrics = {
  // Registrar início do jogo
  startGame: (difficulty) => {
    const metrics = {
      gameId: "musical-sequence",
      sessionId: `music_${Date.now()}`,
      startTime: (/* @__PURE__ */ new Date()).toISOString(),
      difficulty,
      userId: "anonymous",
      // Será substituído por ID real
      device: navigator.userAgent
    };
    console.log("Musical Sequence Game Started:", metrics);
    return metrics;
  },
  // Registrar tentativa de sequência
  recordSequenceAttempt: (attempt) => {
    const metrics = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      sequenceNumber: attempt.sequenceNumber,
      sequenceLength: attempt.sequenceLength,
      targetSequence: attempt.targetSequence.map((note) => note.id),
      playerSequence: attempt.playerSequence.map((note) => note.id),
      isCorrect: attempt.isCorrect,
      responseTime: attempt.responseTime,
      difficulty: attempt.difficulty,
      level: attempt.level,
      errors: attempt.errors || 0,
      partialCorrect: attempt.partialCorrect,
      // quantas notas estavam corretas antes do erro
      notesMatched: attempt.notesMatched
    };
    console.log("Musical Sequence Attempt:", metrics);
    return metrics;
  },
  // Registrar reprodução de nota individual
  recordNotePlay: (noteData) => {
    const metrics = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      action: "note_played",
      noteId: noteData.noteId,
      noteName: noteData.noteName,
      frequency: noteData.frequency,
      context: noteData.context,
      // 'sequence_play' ou 'user_input'
      sequencePosition: noteData.sequencePosition,
      isCorrectPosition: noteData.isCorrectPosition
    };
    console.log("Musical Note Played:", metrics);
    return metrics;
  },
  // Registrar repetição de sequência
  recordSequenceRepeat: () => {
    const metrics = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      action: "sequence_repeated"
    };
    console.log("Musical Sequence Repeated:", metrics);
    return metrics;
  },
  // Registrar final do jogo
  endGame: (gameData) => {
    const metrics = {
      sessionId: gameData.sessionId,
      endTime: (/* @__PURE__ */ new Date()).toISOString(),
      totalTime: gameData.totalTime,
      totalSequences: gameData.totalSequences,
      correctSequences: gameData.correctSequences,
      totalScore: gameData.totalScore,
      finalLevel: gameData.finalLevel,
      accuracy: gameData.accuracy,
      averageSequenceLength: gameData.averageSequenceLength,
      difficulty: gameData.difficulty,
      sequenceRepeats: gameData.sequenceRepeats || 0,
      completed: gameData.completed
    };
    console.log("Musical Sequence Game Ended:", metrics);
    return metrics;
  },
  // Calcular estatísticas do jogo
  calculateStats: (attempts) => {
    if (!attempts || attempts.length === 0) {
      return {
        accuracy: 0,
        averageSequenceLength: 0,
        totalAttempts: 0,
        correctSequences: 0
      };
    }
    const correctAttempts = attempts.filter((attempt) => attempt.isCorrect);
    const totalSequenceLength = attempts.reduce((sum, attempt) => sum + attempt.sequenceLength, 0);
    return {
      accuracy: correctAttempts.length / attempts.length * 100,
      averageSequenceLength: totalSequenceLength / attempts.length,
      totalAttempts: attempts.length,
      correctSequences: correctAttempts.length,
      longestSequence: Math.max(...attempts.map((a) => a.sequenceLength)),
      improvementRate: calculateImprovementRate(attempts),
      noteAccuracy: calculateNoteAccuracy(attempts),
      memorySpan: calculateMemorySpan(attempts)
    };
  },
  // Analisar padrões de aprendizado musical
  analyzeMusicPatterns: (attempts, noteData) => {
    const errorsByPosition = attempts.filter((attempt) => !attempt.isCorrect).reduce((acc, error) => {
      const errorPosition = error.partialCorrect || 0;
      acc[errorPosition] = (acc[errorPosition] || 0) + 1;
      return acc;
    }, {});
    const notePreferences = noteData.reduce((acc, note) => {
      acc[note.noteId] = (acc[note.noteId] || 0) + 1;
      return acc;
    }, {});
    const difficultyProgression = attempts.reduce((acc, attempt) => {
      acc[attempt.difficulty] = (acc[attempt.difficulty] || 0) + 1;
      return acc;
    }, {});
    return {
      errorsByPosition,
      notePreferences,
      difficultyProgression,
      suggestions: generateMusicSuggestions(attempts, errorsByPosition)
    };
  },
  // Inicializar coletores avançados
  initializeAdvancedCollectors: (config = {}) => {
    try {
      collectorsHub = new MusicalSequenceCollectorsHub();
      const sessionConfig = {
        sessionId: `musical_${Date.now()}`,
        gameConfig: config.gameConfig || {},
        difficulty: config.difficulty || "medium",
        playerProfile: config.playerProfile || {},
        enabledCollectors: config.enabledCollectors || ["memory", "pattern", "execution", "learning"],
        ...config
      };
      const result = collectorsHub.startSession(sessionConfig);
      console.log("🎵 MusicalSequence - Coletores avançados inicializados:", result);
      return result;
    } catch (error) {
      console.error("Erro ao inicializar coletores avançados:", error);
      return { success: false, error: error.message };
    }
  },
  // Obter instância do hub de coletores
  getCollectorsHub: () => {
    return collectorsHub;
  },
  // Processar interação com coleta avançada
  recordAdvancedInteraction: (interactionData) => {
    try {
      if (!collectorsHub) {
        console.warn("Coletores avançados não inicializados");
        return null;
      }
      const enrichedData = {
        ...interactionData,
        timestamp: Date.now(),
        gameContext: "musical_sequence"
      };
      const result = collectorsHub.processInteraction(enrichedData);
      if (result && collectorsHub.debugMode) {
        console.log("🎵 Interação avançada processada:", {
          sessionId: result.sessionId,
          collectors: Object.keys(result.collectionResults || {}),
          insights: Object.keys(result.integratedResults || {})
        });
      }
      return result;
    } catch (error) {
      console.error("Erro no processamento de interação avançada:", error);
      return null;
    }
  },
  // Método híbrido que combina métricas básicas com coleta avançada
  recordSequenceAttemptAdvanced: (attempt) => {
    try {
      const basicMetrics = {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        sequenceNumber: attempt.sequenceNumber,
        sequenceLength: attempt.sequenceLength,
        targetSequence: Array.isArray(attempt.targetSequence) ? attempt.targetSequence.map((note) => typeof note === "object" ? note.id : note) : attempt.targetSequence,
        playerSequence: Array.isArray(attempt.playerSequence) ? attempt.playerSequence.map((note) => typeof note === "object" ? note.id : note) : attempt.playerSequence,
        isCorrect: attempt.isCorrect,
        responseTime: attempt.responseTime,
        difficulty: attempt.difficulty,
        level: attempt.level,
        errors: attempt.errors || 0,
        partialCorrect: attempt.partialCorrect,
        notesMatched: attempt.notesMatched
      };
      let advancedMetrics = null;
      if (collectorsHub) {
        advancedMetrics = MusicalSequenceMetrics.recordAdvancedInteraction({
          sequence: basicMetrics.targetSequence,
          playerResponse: basicMetrics.playerSequence,
          sequenceLength: basicMetrics.sequenceLength,
          isCorrect: basicMetrics.isCorrect,
          responseTime: basicMetrics.responseTime,
          difficulty: basicMetrics.difficulty,
          currentLevel: basicMetrics.level,
          accuracy: basicMetrics.isCorrect ? 1 : 0,
          partialCorrect: basicMetrics.partialCorrect,
          errorsCommitted: basicMetrics.errors,
          attemptNumber: basicMetrics.sequenceNumber,
          instruments: attempt.instruments || [],
          timings: attempt.timings || [],
          expectedTimings: attempt.expectedTimings || [],
          executionTimes: attempt.executionTimes || [],
          interClickIntervals: attempt.interClickIntervals || [],
          // Dados adicionais para análise avançada
          maxCorrectSequence: attempt.maxCorrectSequence,
          consistentSpan: attempt.consistentSpan,
          improvementRate: attempt.improvementRate,
          timeBetweenNotes: attempt.timeBetweenNotes,
          tempo: attempt.tempo || 120,
          sequenceType: attempt.sequenceType || "random",
          clickPrecision: attempt.clickPrecision || [],
          pressureDynamics: attempt.pressureDynamics || [],
          repeatRequests: attempt.repeatRequests || 0,
          pauseLocations: attempt.pauseLocations || [],
          chunkingPatterns: attempt.chunkingPatterns || []
        });
      }
      console.log("🎵 Musical Sequence Attempt (Enhanced):", basicMetrics);
      return {
        basic: basicMetrics,
        advanced: advancedMetrics,
        timestamp: basicMetrics.timestamp
      };
    } catch (error) {
      console.error("Erro no registro de tentativa avançada:", error);
      const fallbackMetrics = {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        sequenceNumber: attempt.sequenceNumber,
        sequenceLength: attempt.sequenceLength,
        targetSequence: attempt.targetSequence?.map?.((note) => note.id) || attempt.targetSequence,
        playerSequence: attempt.playerSequence?.map?.((note) => note.id) || attempt.playerSequence,
        isCorrect: attempt.isCorrect,
        responseTime: attempt.responseTime,
        difficulty: attempt.difficulty,
        level: attempt.level,
        errors: attempt.errors || 0,
        partialCorrect: attempt.partialCorrect,
        notesMatched: attempt.notesMatched,
        error: error.message
      };
      console.log("Musical Sequence Attempt (Fallback):", fallbackMetrics);
      return { basic: fallbackMetrics, advanced: null };
    }
  },
  // Gerar relatório completo com dados avançados
  generateComprehensiveReport: () => {
    try {
      let report = {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        reportType: "comprehensive",
        basicMetrics: {
          // Métricas básicas seriam coletadas aqui
          message: "Métricas básicas disponíveis"
        }
      };
      if (collectorsHub) {
        const advancedReport = collectorsHub.getComprehensiveReport();
        report.advancedMetrics = advancedReport;
        report.collectorsActive = collectorsHub.getStatus();
      } else {
        report.advancedMetrics = { message: "Coletores avançados não disponíveis" };
      }
      console.log("🎵 Relatório comprehensivo gerado:", {
        hasBasicMetrics: !!report.basicMetrics,
        hasAdvancedMetrics: !!report.advancedMetrics?.session,
        collectorsStatus: report.collectorsActive?.isEnabled
      });
      return report;
    } catch (error) {
      console.error("Erro ao gerar relatório comprehensivo:", error);
      return {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        error: error.message,
        fallback: true
      };
    }
  },
  // Obter recomendações personalizadas
  getPersonalizedRecommendations: () => {
    try {
      if (!collectorsHub) {
        return [
          "Continue praticando regularmente para melhorar sua memória musical",
          "Tente sequências de diferentes complexidades",
          "Use a função de repetir sequência quando necessário"
        ];
      }
      const report = collectorsHub.getComprehensiveReport();
      const recommendations = [];
      if (report.memory?.recommendations) {
        recommendations.push(...report.memory.recommendations);
      }
      if (report.patterns?.recommendations) {
        recommendations.push(...report.patterns.recommendations);
      }
      if (report.execution?.recommendations) {
        recommendations.push(...report.execution.recommendations);
      }
      if (report.learning?.recommendations) {
        recommendations.push(...report.learning.recommendations);
      }
      const uniqueRecommendations = [...new Set(recommendations)].slice(0, 5);
      return uniqueRecommendations.length > 0 ? uniqueRecommendations : [
        "Continue praticando para desenvolver suas habilidades musicais",
        "Explore diferentes níveis de dificuldade",
        "Mantenha consistência na prática"
      ];
    } catch (error) {
      console.error("Erro ao obter recomendações personalizadas:", error);
      return ["Continue praticando regularmente"];
    }
  },
  // Controlar coletores
  enableAdvancedCollector: (collectorName) => {
    if (collectorsHub) {
      return collectorsHub.enableCollector(collectorName);
    }
    return false;
  },
  disableAdvancedCollector: (collectorName) => {
    if (collectorsHub) {
      return collectorsHub.disableCollector(collectorName);
    }
    return false;
  },
  // Finalizar sessão com dados completos
  endGameAdvanced: (gameData) => {
    try {
      const basicEndMetrics = {
        sessionId: gameData.sessionId,
        endTime: (/* @__PURE__ */ new Date()).toISOString(),
        totalTime: gameData.totalTime,
        totalSequences: gameData.totalSequences,
        correctSequences: gameData.correctSequences,
        totalScore: gameData.totalScore,
        finalLevel: gameData.finalLevel,
        accuracy: gameData.accuracy,
        averageSequenceLength: gameData.averageSequenceLength,
        difficulty: gameData.difficulty,
        sequenceRepeats: gameData.sequenceRepeats || 0,
        completed: gameData.completed
      };
      let advancedEndMetrics = null;
      if (collectorsHub) {
        advancedEndMetrics = collectorsHub.endSession();
      }
      console.log("🎵 Musical Sequence Game Ended (Enhanced):", basicEndMetrics);
      return {
        basic: basicEndMetrics,
        advanced: advancedEndMetrics,
        summary: MusicalSequenceMetrics.generateGameSummary(basicEndMetrics, advancedEndMetrics)
      };
    } catch (error) {
      console.error("Erro ao finalizar jogo avançado:", error);
      const fallbackMetrics = {
        sessionId: gameData.sessionId,
        endTime: (/* @__PURE__ */ new Date()).toISOString(),
        totalTime: gameData.totalTime,
        totalSequences: gameData.totalSequences,
        correctSequences: gameData.correctSequences,
        totalScore: gameData.totalScore,
        finalLevel: gameData.finalLevel,
        accuracy: gameData.accuracy,
        averageSequenceLength: gameData.averageSequenceLength,
        difficulty: gameData.difficulty,
        sequenceRepeats: gameData.sequenceRepeats || 0,
        completed: gameData.completed,
        error: error.message
      };
      console.log("Musical Sequence Game Ended (Fallback):", fallbackMetrics);
      return { basic: fallbackMetrics, advanced: null };
    }
  },
  // Gerar resumo do jogo
  generateGameSummary: (basicMetrics, advancedMetrics) => {
    try {
      const summary = {
        performance: {
          accuracy: basicMetrics.accuracy || 0,
          totalSequences: basicMetrics.totalSequences || 0,
          finalLevel: basicMetrics.finalLevel || 1,
          completionStatus: basicMetrics.completed ? "completed" : "partial"
        },
        timing: {
          totalTime: basicMetrics.totalTime || 0,
          averageSequenceTime: basicMetrics.totalTime && basicMetrics.totalSequences ? basicMetrics.totalTime / basicMetrics.totalSequences : 0
        }
      };
      if (advancedMetrics && advancedMetrics.summary) {
        summary.advanced = {
          keyInsights: advancedMetrics.summary.keyInsights || [],
          dataQuality: advancedMetrics.summary.dataQuality || 0,
          recommendations: advancedMetrics.summary.recommendations || []
        };
      }
      return summary;
    } catch (error) {
      console.error("Erro ao gerar resumo do jogo:", error);
      return {
        performance: { accuracy: 0, totalSequences: 0, finalLevel: 1 },
        timing: { totalTime: 0 },
        error: error.message
      };
    }
  },
  // Configurar modo debug
  setDebugMode: (enabled) => {
    if (collectorsHub) {
      collectorsHub.setDebugMode(enabled);
    }
    console.log(`🎵 MusicalSequenceMetrics - Debug mode ${enabled ? "enabled" : "disabled"}`);
  },
  // Status dos coletores
  getCollectorsStatus: () => {
    if (collectorsHub) {
      return collectorsHub.getStatus();
    }
    return {
      isEnabled: false,
      message: "Coletores avançados não inicializados"
    };
  }
};
function calculateImprovementRate(attempts) {
  if (attempts.length < 6) return 0;
  const firstThird = attempts.slice(0, Math.floor(attempts.length / 3));
  const lastThird = attempts.slice(-Math.floor(attempts.length / 3));
  const firstAccuracy = firstThird.filter((a) => a.isCorrect).length / firstThird.length;
  const lastAccuracy = lastThird.filter((a) => a.isCorrect).length / lastThird.length;
  return (lastAccuracy - firstAccuracy) / firstAccuracy * 100;
}
function calculateNoteAccuracy(attempts) {
  const noteStats = {};
  attempts.forEach((attempt) => {
    attempt.targetSequence.forEach((noteId, index) => {
      if (!noteStats[noteId]) {
        noteStats[noteId] = { correct: 0, total: 0 };
      }
      noteStats[noteId].total++;
      if (attempt.playerSequence[index] === noteId) {
        noteStats[noteId].correct++;
      }
    });
  });
  Object.keys(noteStats).forEach((noteId) => {
    noteStats[noteId].accuracy = noteStats[noteId].correct / noteStats[noteId].total * 100;
  });
  return noteStats;
}
function calculateMemorySpan(attempts) {
  const correctSequences = attempts.filter((a) => a.isCorrect);
  if (correctSequences.length === 0) return 0;
  let maxSpan = 0;
  let currentSpan = 0;
  attempts.forEach((attempt) => {
    if (attempt.isCorrect) {
      currentSpan = Math.max(currentSpan, attempt.sequenceLength);
    } else {
      maxSpan = Math.max(maxSpan, currentSpan);
      currentSpan = 0;
    }
  });
  return Math.max(maxSpan, currentSpan);
}
function generateMusicSuggestions(attempts, errorsByPosition) {
  const suggestions = [];
  if (attempts.length === 0) {
    suggestions.push("Comece jogando para receber sugestões musicais!");
    return suggestions;
  }
  const accuracy = attempts.filter((a) => a.isCorrect).length / attempts.length;
  if (accuracy < 0.4) {
    suggestions.push("Comece com sequências mais curtas e foque em ouvir cada nota");
    suggestions.push('Use a função "Repetir Sequência" sempre que precisar');
  } else if (accuracy < 0.7) {
    suggestions.push("Tente identificar padrões nas sequências musicais");
    suggestions.push("Pratique cantarolando as notas mentalmente");
  } else if (accuracy > 0.9) {
    suggestions.push("Excelente memória musical! Tente um nível mais difícil");
    suggestions.push("Você está pronto para sequências mais longas");
  }
  const mostErrorPosition = Object.entries(errorsByPosition).sort(([, a], [, b]) => b - a)[0];
  if (mostErrorPosition) {
    const position = parseInt(mostErrorPosition[0]);
    if (position === 0) {
      suggestions.push("Foque mais atenção na primeira nota da sequência");
    } else if (position >= 2) {
      suggestions.push("Sua memória de curto prazo está boa, pratique sequências mais longas");
    }
  }
  return suggestions.length > 0 ? suggestions : ["Continue praticando para desenvolver sua memória musical!"];
}
const ACTIVITY_TYPES = {
  SEQUENCE_REPRODUCTION: {
    id: "sequence_reproduction",
    name: "Reprodução",
    icon: "🔄",
    description: "Teste de memória auditiva sequencial",
    cognitiveFunction: "auditory_sequential_memory",
    component: "SequenceReproductionActivity"
  },
  RHYTHM_TIMING: {
    id: "rhythm_timing",
    name: "Ritmo",
    icon: "⏱️",
    description: "Teste de coordenação temporal e atenção sustentada",
    cognitiveFunction: "temporal_coordination_attention",
    component: "RhythmTimingActivity"
  },
  PITCH_DISCRIMINATION: {
    id: "pitch_discrimination",
    name: "Altura",
    icon: "🎵",
    description: "Teste de processamento auditivo e discriminação fina",
    cognitiveFunction: "auditory_processing_discrimination",
    component: "PitchDiscriminationActivity"
  },
  PATTERN_PREDICTION: {
    id: "pattern_prediction",
    name: "Padrões",
    icon: "🔮",
    description: "Teste de raciocínio lógico e reconhecimento de padrões",
    cognitiveFunction: "logical_reasoning_pattern_recognition",
    component: "PatternPredictionActivity"
  }
};
const MusicalSequenceGame = ({ onBack, onComplete }) => {
  const { user, ttsEnabled = true } = reactExports.useContext(SystemContext);
  const { settings } = useAccessibilityContext();
  const [ttsActive2, setTtsActive] = reactExports.useState(true);
  const metricsRef = reactExports.useRef(null);
  const [collectorsHub2] = reactExports.useState(() => new MusicalSequenceCollectorsHub());
  const [gameState, setGameState] = reactExports.useState({
    status: "start",
    // 'start', 'playing', 'paused', 'finished'
    score: 0,
    round: 1,
    totalRounds: 7,
    // Padrão 4-7 rodadas como outros jogos
    difficulty: "easy",
    accuracy: 100,
    roundStartTime: null,
    // 🎯 Sistema de atividades redesenhado (COMEÇAR COM RITMO POR PADRÃO)
    currentActivity: ACTIVITY_TYPES.RHYTHM_TIMING.id,
    // Começar diretamente com ritmo
    activityCycle: [
      ACTIVITY_TYPES.RHYTHM_TIMING.id,
      // Ritmo como primeira atividade
      ACTIVITY_TYPES.SEQUENCE_REPRODUCTION.id,
      ACTIVITY_TYPES.PITCH_DISCRIMINATION.id,
      ACTIVITY_TYPES.PATTERN_PREDICTION.id
    ],
    activityIndex: 0,
    roundsPerActivity: 4,
    // Mínimo 4 rodadas por atividade
    activityRoundCount: 0,
    activitiesCompleted: [],
    // 🎯 Dados específicos de atividades
    activityData: {
      sequenceReproduction: {
        sequence: [],
        userSequence: [],
        isPlaying: false
      },
      rhythmPatterns: {
        pattern: [],
        userPattern: [],
        tempo: 120
      },
      melodyCompletion: {
        melody: [],
        missingNotes: [],
        userCompletion: []
      },
      instrumentRecognition: {
        currentInstrument: null,
        options: [],
        selectedInstrument: null
      },
      musicalMemory: {
        sequences: [],
        currentSequence: 0,
        userRecall: []
      },
      creativeComposition: {
        userComposition: [],
        availableNotes: [],
        isRecording: false
      }
    },
    // 🎯 Feedback e animações
    showFeedback: false,
    feedbackType: null,
    feedbackMessage: "",
    showCelebration: false,
    // 🎯 Métricas comportamentais
    responseTime: 0,
    hesitationCount: 0,
    helpUsed: false,
    consecutiveCorrect: 0,
    totalAttempts: 0,
    correctAttempts: 0
  });
  const {
    startUnifiedSession,
    recordInteraction,
    updateMetrics,
    sessionId
  } = useUnifiedGameLogic("musical_sequence");
  const {
    initializeSession: initMultisensory
  } = useMultisensoryIntegration(sessionId, {
    learningStyle: user?.profile?.learningStyle || "auditory"
  });
  const {
    setUserContext: setTherapeuticContext
  } = useTherapeuticOrchestrator({ userId: user?.id });
  const [showStartScreen, setShowStartScreen] = reactExports.useState(true);
  const [difficulty, setDifficulty] = reactExports.useState("easy");
  const [sequence, setSequence] = reactExports.useState([]);
  const [playerSequence, setPlayerSequence] = reactExports.useState([]);
  const [currentLevel, setCurrentLevel] = reactExports.useState(1);
  const [currentRound, setCurrentRound] = reactExports.useState(1);
  const instruments = [
    { id: "piano", name: "Piano", emoji: "🎹", color: "#FF6B6B", sound: "piano" },
    { id: "guitar", name: "Violão", emoji: "🎸", color: "#4ECDC4", sound: "guitar" },
    { id: "drum", name: "Bateria", emoji: "🥁", color: "#45B7D1", sound: "drum" },
    { id: "flute", name: "Flauta", emoji: "🎵", color: "#96CEB4", sound: "flute" },
    { id: "violin", name: "Violino", emoji: "🎻", color: "#A8E6CF", sound: "violin" }
  ];
  const [score, setScore] = reactExports.useState(0);
  const [streak, setStreak] = reactExports.useState(0);
  const [totalAttempts, setTotalAttempts] = reactExports.useState(0);
  const [correctAttempts, setCorrectAttempts] = reactExports.useState(0);
  const [activeInstrument, setActiveInstrument] = reactExports.useState(null);
  const [isPlaying, setIsPlaying] = reactExports.useState(false);
  const [feedback, setFeedback] = reactExports.useState({ show: false, type: "", message: "" });
  const [gameStartTime, setGameStartTime] = reactExports.useState(null);
  reactExports.useEffect(() => {
    if (metricsRef.current && recordInteraction && updateMetrics) {
      metricsRef.current.connectToBackend({
        recordInteraction,
        updateMetrics
      });
    }
  }, [recordInteraction, updateMetrics]);
  const speak = reactExports.useCallback((text, options = {}) => {
    if (!ttsActive2 || !("speechSynthesis" in window)) {
      return;
    }
    window.speechSynthesis.cancel();
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = "pt-BR";
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    window.speechSynthesis.speak(utterance);
  }, [ttsActive2]);
  const toggleTTS = reactExports.useCallback(() => {
    const newTtsState = !ttsActive2;
    setTtsActive(newTtsState);
    if (!newTtsState && "speechSynthesis" in window) {
      window.speechSynthesis.cancel();
    } else if (newTtsState) {
      speak("TTS ativado");
    }
  }, [ttsActive2, speak]);
  const initAudioContext = reactExports.useCallback(() => {
    if (!audioContextRef.current || audioContextRef.current.state === "closed") {
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
    }
  }, []);
  const playSound = reactExports.useCallback(async (note, duration = 500) => {
    if (!audioContextRef.current) {
      console.warn("AudioContext não iniciado. Iniciando automaticamente...");
      await initAudioContext();
    }
    try {
      const audioContext = audioContextRef.current;
      if (note === "DRUM" || note === "BEAT" || note === "TAP") {
        const bufferSize = audioContext.sampleRate * (duration / 1e3);
        const buffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
        const data = buffer.getChannelData(0);
        for (let i = 0; i < bufferSize; i++) {
          const envelope = Math.pow(1 - i / bufferSize, 2);
          data[i] = (Math.random() * 2 - 1) * envelope * 0.3;
        }
        const source = audioContext.createBufferSource();
        const gainNode2 = audioContext.createGain();
        source.buffer = buffer;
        source.connect(gainNode2);
        gainNode2.connect(audioContext.destination);
        gainNode2.gain.setValueAtTime(0.4, audioContext.currentTime);
        gainNode2.gain.exponentialRampToValueAtTime(1e-3, audioContext.currentTime + duration / 1e3);
        source.start(audioContext.currentTime);
        return new Promise((resolve) => {
          source.onended = resolve;
          setTimeout(resolve, duration);
        });
      }
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      const frequencies = {
        "C": 261.63,
        "D": 293.66,
        "E": 329.63,
        "F": 349.23,
        "G": 392,
        "A": 440,
        "B": 493.88
      };
      oscillator.frequency.setValueAtTime(frequencies[note] || 440, audioContext.currentTime);
      oscillator.type = "sine";
      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1e3);
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + duration / 1e3);
      return new Promise((resolve) => {
        oscillator.onended = resolve;
      });
    } catch (error) {
      console.error("Erro ao tocar som:", error);
    }
  }, [initAudioContext]);
  reactExports.useEffect(() => {
    if (!metricsRef.current) {
      metricsRef.current = MusicalSequenceMetrics;
    }
  }, []);
  const startGame = reactExports.useCallback(async (selectedDifficulty) => {
    try {
      setShowStartScreen(false);
      setDifficulty(selectedDifficulty);
      const maxRounds = getRoundsForDifficulty(selectedDifficulty);
      setGameState((prev) => ({
        ...prev,
        status: "playing",
        difficulty: selectedDifficulty,
        totalRounds: maxRounds,
        roundStartTime: Date.now()
      }));
      if (startUnifiedSession) {
        await startUnifiedSession({
          gameType: "musical_sequence",
          difficulty: selectedDifficulty,
          userId: user?.id || "anonymous"
        });
      }
      await initMultisensory();
      if (user?.id && user.id !== "anonymous" && user.id !== "") {
        setTherapeuticContext(user.id);
      }
      generateNewRound();
      speak("Jogo iniciado! Vamos começar com sequências musicais.");
    } catch (error) {
      console.error("Erro ao iniciar jogo:", error);
    }
  }, [startUnifiedSession, initMultisensory, setTherapeuticContext, user, speak, setShowStartScreen]);
  const changeActivity = reactExports.useCallback((activityId) => {
    console.log("🎯 Changing musical activity to:", activityId);
    setGameState((prev) => {
      console.log("🔄 Current musical state before change:", {
        currentActivity: prev.currentActivity,
        targetActivity: activityId
      });
      const newState = {
        ...prev,
        currentActivity: activityId,
        activityRoundCount: 0,
        activityIndex: prev.activityCycle.indexOf(activityId),
        showFeedback: false,
        roundStartTime: Date.now()
      };
      console.log("✅ New musical state after change:", newState);
      return newState;
    });
    setSequence([]);
    setPlayerSequence([]);
    setActiveInstrument(null);
    setIsPlaying(false);
    const activity = Object.values(ACTIVITY_TYPES).find((a) => a.id === activityId);
    speak(`Mudando para: ${activity?.name}`);
    setTimeout(() => {
      generateNewRound(activityId);
    }, 500);
  }, [speak]);
  const generateNewRound = reactExports.useCallback((activityId = null) => {
    const currentActivity = activityId || gameState.currentActivity;
    console.log("🎵 Generating new round for activity:", currentActivity);
    const finalActivity = currentActivity === ACTIVITY_TYPES.RHYTHM_TIMING.id ? ACTIVITY_TYPES.RHYTHM_TIMING.id : currentActivity;
    console.log("🎯 Atividade final selecionada:", finalActivity);
    setGameState((prev) => ({
      ...prev,
      currentActivity: finalActivity,
      // Garantir que o estado mantenha a atividade
      roundStartTime: Date.now(),
      showFeedback: false,
      feedbackType: null,
      feedbackMessage: ""
    }));
    switch (finalActivity) {
      case ACTIVITY_TYPES.SEQUENCE_REPRODUCTION.id:
        generateSequenceReproduction();
        break;
      case ACTIVITY_TYPES.RHYTHM_TIMING.id:
        generateRhythmTiming();
        break;
      case ACTIVITY_TYPES.PITCH_DISCRIMINATION.id:
        generatePitchDiscrimination();
        break;
      case ACTIVITY_TYPES.PATTERN_PREDICTION.id:
        generatePatternPrediction();
        break;
      default:
        generateRhythmTiming();
    }
  }, [gameState.currentActivity]);
  const generateSequenceReproduction = reactExports.useCallback(() => {
    console.log("🔄 Generating sequence reproduction activity");
    const difficultyMap = { "easy": 3, "medium": 4, "hard": 5 };
    const sequenceLength = difficultyMap[difficulty] || 3;
    const newSequence = [];
    const availableInstruments = [...instruments];
    for (let i = 0; i < sequenceLength; i++) {
      const randomIndex = Math.floor(Math.random() * availableInstruments.length);
      const selectedInstrument = availableInstruments.splice(randomIndex, 1)[0];
      newSequence.push(selectedInstrument.id);
    }
    setSequence(newSequence);
    setPlayerSequence([]);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        sequenceReproduction: {
          sequence: newSequence,
          userSequence: [],
          currentStep: 0,
          showingSequence: true,
          allowInput: false
        }
      }
    }));
    speak(`Atividade: Reprodução de Sequência. Memorize a ordem de ${sequenceLength} instrumentos diferentes.`);
  }, [difficulty, speak]);
  const generateRhythmTiming = reactExports.useCallback(() => {
    console.log("⏱️ Generating rhythm timing activity - NOVO SISTEMA COM BATERIA");
    const bpmLevels = {
      "easy": 80,
      // 80 BPM - ritmo lento e confortável
      "medium": 120,
      // 120 BPM - ritmo médio padrão
      "hard": 160
      // 160 BPM - ritmo rápido e desafiador
    };
    const targetBPM = bpmLevels[difficulty] || 80;
    const totalBeats = 8;
    const beatInterval = 60 / targetBPM * 1e3;
    console.log(`🥁 Configurando ritmo: ${targetBPM} BPM, Intervalo: ${beatInterval}ms`);
    setSequence([]);
    setPlayerSequence([]);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        rhythmTiming: {
          targetBPM,
          totalBeats,
          currentBeat: 0,
          userTaps: [],
          metronomeActive: false,
          gamePhase: "waiting",
          // 'waiting', 'listening', 'playing', 'finished'
          accuracy: 0,
          perfectHits: 0,
          startTime: null,
          beatInterval,
          // Intervalo em ms entre batidas
          isRecording: false
        }
      }
    }));
    speak(`Novo Jogo de Ritmo com Bateria! Siga o metrônomo tocando ${totalBeats} batidas no ritmo de ${targetBPM} BPM. Primeiro ouça, depois toque junto!`);
  }, [difficulty, speak]);
  const generatePitchDiscrimination = reactExports.useCallback(() => {
    console.log("🎵 Generating enhanced pitch discrimination activity");
    const pitchSystems = {
      "easy": {
        reference: 440,
        // Lá padrão
        options: [
          { freq: 440, name: "Lá (440Hz)", note: "A4" },
          { freq: 493.88, name: "Si (494Hz)", note: "B4" },
          { freq: 523.25, name: "Dó (523Hz)", note: "C5" },
          { freq: 587.33, name: "Ré (587Hz)", note: "D5" }
        ],
        tolerance: 50
        // Diferenças grandes
      },
      "medium": {
        reference: 440,
        options: [
          { freq: 440, name: "Lá (440Hz)", note: "A4" },
          { freq: 466.16, name: "Lá# (466Hz)", note: "A#4" },
          { freq: 493.88, name: "Si (494Hz)", note: "B4" },
          { freq: 523.25, name: "Dó (523Hz)", note: "C5" }
        ],
        tolerance: 25
        // Diferenças médias
      },
      "hard": {
        reference: 440,
        options: [
          { freq: 440, name: "Lá (440Hz)", note: "A4" },
          { freq: 445, name: "Lá+ (445Hz)", note: "A4+" },
          { freq: 450, name: "Lá++ (450Hz)", note: "A4++" },
          { freq: 435, name: "Lá- (435Hz)", note: "A4-" }
        ],
        tolerance: 10
        // Diferenças pequenas (microtons)
      }
    };
    const currentSystem = pitchSystems[difficulty] || pitchSystems["easy"];
    const referencePitch = currentSystem.reference;
    const testOptions = currentSystem.options.filter((opt) => opt.freq !== referencePitch);
    const targetPitchObj = testOptions[Math.floor(Math.random() * testOptions.length)];
    const targetPitch = targetPitchObj.freq;
    const pitchRelation = targetPitch > referencePitch ? "higher" : targetPitch < referencePitch ? "lower" : "same";
    setSequence([]);
    setPlayerSequence([]);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        pitchDiscrimination: {
          referencePitch,
          targetPitch,
          pitchSystem: currentSystem,
          targetPitchObj,
          correctAnswer: pitchRelation,
          currentPhase: "instruction",
          // 'instruction', 'reference', 'test', 'comparison', 'answer'
          userAnswer: null,
          showVisualAid: true,
          hasPlayedReference: false,
          hasPlayedTest: false,
          canAnswer: false,
          round: (prev.activityData?.pitchDiscrimination?.round || 0) + 1,
          difficulty,
          frequencyDifference: Math.abs(targetPitch - referencePitch)
        }
      }
    }));
    const difficultyText = difficulty === "easy" ? "grandes diferenças" : difficulty === "medium" ? "diferenças médias" : "diferenças sutis";
    speak(`Atividade: Discriminação de Altura Musical. Nível ${difficulty} com ${difficultyText}. Ouça os dois tons e compare suas alturas.`);
  }, [difficulty, speak]);
  const generatePatternPrediction = reactExports.useCallback(() => {
    console.log("🔮 Generating pattern prediction activity");
    const patternTypes = {
      "easy": {
        "ascending": [0, 1, 2, 3],
        // Sequência crescente
        "descending": [3, 2, 1, 0],
        // Sequência decrescente
        "repeating": [0, 1, 0, 1]
        // Padrão repetitivo
      },
      "medium": {
        "alternating": [0, 2, 1, 3],
        // Padrão alternado
        "skip_one": [0, 2, 4, 1],
        // Pular um
        "double_pattern": [0, 0, 1, 1]
        // Padrão duplo
      },
      "hard": {
        "fibonacci": [0, 1, 1, 2],
        // Sequência fibonacci
        "arithmetic": [0, 2, 4, 1],
        // Progressão aritmética
        "complex_alternating": [0, 3, 1, 4, 2]
        // Alternância complexa
      }
    };
    const difficultyPatterns = patternTypes[difficulty] || patternTypes["easy"];
    const patternNames = Object.keys(difficultyPatterns);
    const selectedPattern = patternNames[Math.floor(Math.random() * patternNames.length)];
    const basePattern = difficultyPatterns[selectedPattern];
    const patternInstruments = basePattern.map((index) => instruments[index % instruments.length].id);
    const incompletePattern = patternInstruments.slice(0, -1);
    const correctAnswer = patternInstruments[patternInstruments.length - 1];
    const allOptions = [...instruments.map((inst) => inst.id)];
    const distractors = allOptions.filter((id) => id !== correctAnswer);
    const shuffledDistractors = distractors.sort(() => Math.random() - 0.5);
    const options = [correctAnswer, ...shuffledDistractors.slice(0, 3)].sort(() => Math.random() - 0.5);
    setSequence(incompletePattern);
    setPlayerSequence([]);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        patternPrediction: {
          incompletePattern,
          correctAnswer,
          patternType: selectedPattern,
          options,
          userAnswer: null,
          showPattern: true,
          showResult: false,
          difficulty,
          patternExplanation: getPatternExplanation(selectedPattern),
          round: (prev.activityData?.patternPrediction?.round || 0) + 1,
          totalRounds: difficulty === "easy" ? 5 : difficulty === "medium" ? 6 : 7,
          isCorrect: null,
          feedback: null
        }
      }
    }));
    speak(`Atividade: Predição de Padrões. Analise o padrão musical ${selectedPattern} e preveja o próximo elemento.`);
  }, [difficulty, speak]);
  const getPatternExplanation = reactExports.useCallback((patternType) => {
    const explanations = {
      "ascending": "Sequência crescente: cada elemento é maior que o anterior",
      "descending": "Sequência decrescente: cada elemento é menor que o anterior",
      "repeating": "Padrão repetitivo: elementos se repetem em ciclos",
      "alternating": "Padrão alternado: elementos alternam entre posições",
      "skip_one": "Pular um: sequência pula um elemento a cada passo",
      "double_pattern": "Padrão duplo: cada elemento aparece duas vezes",
      "fibonacci": "Sequência Fibonacci: cada elemento é a soma dos dois anteriores",
      "arithmetic": "Progressão aritmética: diferença constante entre elementos",
      "complex_alternating": "Alternância complexa: padrão de alternância avançado"
    };
    return explanations[patternType] || "Analise a sequência para identificar o padrão";
  }, []);
  const checkPatternAnswer = reactExports.useCallback((selectedAnswer) => {
    const activityData = gameState.activityData?.patternPrediction || {};
    const { correctAnswer, patternType, round, totalRounds } = activityData;
    const isCorrect = selectedAnswer === correctAnswer;
    const selectedInstrument = instruments.find((inst) => inst.id === selectedAnswer);
    const correctInstrument = instruments.find((inst) => inst.id === correctAnswer);
    let feedback2 = "";
    if (isCorrect) {
      feedback2 = `Perfeito! ${selectedInstrument?.name} completa o padrão ${patternType} corretamente!`;
    } else {
      feedback2 = `Não é bem assim. O padrão ${patternType} seria completado por ${correctInstrument?.name}.`;
    }
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        patternPrediction: {
          ...prev.activityData.patternPrediction,
          userAnswer: selectedAnswer,
          isCorrect,
          showResult: true,
          feedback: feedback2
        }
      }
    }));
    speak(feedback2);
    setTimeout(() => {
      if (round >= totalRounds) {
        speak(`Atividade de Predições de Padrões concluída! Você completou ${totalRounds} rodadas no nível ${difficulty}.`);
        generateNewRound();
      } else {
        generatePatternPrediction();
      }
    }, 3e3);
  }, [gameState.activityData?.patternPrediction, instruments, speak, getPatternExplanation, generateNewRound]);
  reactExports.useCallback(() => {
    console.log("🎨 Generating creative expression activity");
    const creativePrompts = {
      "easy": [
        {
          constraint: "use_3_instruments",
          description: "Crie uma melodia usando exatamente 3 instrumentos diferentes",
          minLength: 4,
          maxLength: 6,
          theme: "variety"
        },
        {
          constraint: "simple_pattern",
          description: "Crie uma melodia simples que conte uma história musical",
          minLength: 4,
          maxLength: 6,
          theme: "storytelling"
        }
      ],
      "medium": [
        {
          constraint: "emotional_theme",
          description: "Crie uma melodia que expresse alegria usando 4-5 instrumentos",
          minLength: 5,
          maxLength: 8,
          theme: "joy"
        },
        {
          constraint: "call_response",
          description: "Crie uma melodia com padrão pergunta-resposta musical",
          minLength: 6,
          maxLength: 8,
          theme: "dialogue"
        }
      ],
      "hard": [
        {
          constraint: "rhythmic_pattern",
          description: "Crie uma composição com padrão rítmico específico: forte-fraco-forte-fraco",
          minLength: 6,
          maxLength: 10,
          theme: "rhythm"
        },
        {
          constraint: "modal_composition",
          description: "Crie uma composição usando todos os instrumentos pelo menos uma vez",
          minLength: 8,
          maxLength: 12,
          theme: "completeness"
        }
      ]
    };
    const difficultyPrompts = creativePrompts[difficulty] || creativePrompts["easy"];
    const prompt = difficultyPrompts[Math.floor(Math.random() * difficultyPrompts.length)];
    setSequence([]);
    setPlayerSequence([]);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        creativeExpression: {
          prompt,
          userComposition: [],
          isRecording: false,
          canPlayback: false,
          creativityScore: 0,
          uniquenessScore: 0,
          difficulty,
          startTime: Date.now(),
          compositionHistory: []
        }
      }
    }));
    speak(`Atividade: Expressão Criativa. ${prompt.description}`);
  }, [difficulty, speak]);
  const evaluateCreativity = reactExports.useCallback((composition) => {
    if (!composition || composition.length === 0) return { creativity: 0, uniqueness: 0, feedback: "Composição vazia" };
    const uniqueInstruments = [...new Set(composition)];
    const varietyScore = uniqueInstruments.length / instruments.length * 100;
    let patternScore = 0;
    const hasRepeats = composition.some(
      (inst, i) => composition.indexOf(inst, i + 1) !== -1
    );
    if (hasRepeats) patternScore += 20;
    const hasProgression = composition.length >= 3;
    if (hasProgression) patternScore += 20;
    const instrumentCounts = {};
    composition.forEach((inst) => {
      instrumentCounts[inst] = (instrumentCounts[inst] || 0) + 1;
    });
    const isBalanced = Object.values(instrumentCounts).every((count) => count <= 3);
    if (isBalanced) patternScore += 20;
    const creativityScore = Math.min(100, Math.round((varietyScore + patternScore) / 2));
    const uniquenessScore = Math.min(100, Math.round(varietyScore + (composition.length >= 6 ? 20 : 0)));
    let feedback2 = "";
    if (creativityScore >= 80) {
      feedback2 = "Composição muito criativa! Excelente uso dos instrumentos.";
    } else if (creativityScore >= 60) {
      feedback2 = "Boa composição! Tente variar mais os instrumentos.";
    } else {
      feedback2 = "Continue explorando! Tente usar mais instrumentos diferentes.";
    }
    return { creativity: creativityScore, uniqueness: uniquenessScore, feedback: feedback2 };
  }, [instruments]);
  const playSequenceWithArray = reactExports.useCallback(async (sequenceArray) => {
    if (isPlaying) return;
    setIsPlaying(true);
    console.log("🎵 Reproduzindo sequência:", sequenceArray);
    for (let i = 0; i < sequenceArray.length; i++) {
      const note = sequenceArray[i];
      setActiveInstrument(note);
      await playSound(note, 600);
      await new Promise((resolve) => setTimeout(resolve, 100));
      setActiveInstrument(null);
      await new Promise((resolve) => setTimeout(resolve, 200));
    }
    setIsPlaying(false);
    if (ttsEnabled) {
      setTimeout(() => {
        speak(`Sequência reproduzida! Agora é sua vez de repetir ${sequenceArray.length} notas.`, {
          rate: 0.9,
          onEnd: () => console.log("Instrução pós-sequência anunciada")
        });
      }, 500);
    }
  }, [isPlaying, setActiveInstrument, playSound, setIsPlaying, setGameState, ttsEnabled, speak]);
  reactExports.useCallback(() => {
    const activityData = gameState.activityData?.creativeExpression || {};
    const { userComposition, prompt } = activityData;
    if (!userComposition || userComposition.length < (prompt?.minLength || 3)) {
      speak(`Sua composição precisa ter pelo menos ${prompt?.minLength || 3} notas. Continue criando!`);
      return;
    }
    const evaluation = evaluateCreativity(userComposition);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        creativeExpression: {
          ...prev.activityData.creativeExpression,
          creativityScore: evaluation.creativity,
          uniquenessScore: evaluation.uniqueness,
          isFinished: true,
          evaluation
        }
      }
    }));
    speak(`Composição finalizada! ${evaluation.feedback} Criatividade: ${evaluation.creativity}%. Originalidade: ${evaluation.uniqueness}%.`);
    setTimeout(() => {
      playSequenceWithArray(userComposition);
    }, 2e3);
    setTimeout(() => {
      generateNewRound();
    }, 5e3);
  }, [gameState.activityData?.creativeExpression, evaluateCreativity, speak, playSequenceWithArray, generateNewRound]);
  reactExports.useCallback(() => {
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        creativeExpression: {
          ...prev.activityData.creativeExpression,
          userComposition: [],
          canPlayback: false
        }
      }
    }));
    speak("Composição limpa. Comece uma nova criação!");
  }, [speak]);
  reactExports.useCallback(() => {
    setTtsActive((prev) => {
      const newState = !prev;
      localStorage.setItem("musicalSequence_ttsActive", JSON.stringify(newState));
      if (!newState && "speechSynthesis" in window) {
        window.speechSynthesis.cancel();
      }
      return newState;
    });
  }, []);
  const getRoundsForDifficulty = reactExports.useCallback((diff) => {
    return MusicalSequenceConfig.roundsConfig[diff] || MusicalSequenceConfig.roundsConfig.easy;
  }, []);
  const [maxRoundsPerLevel] = reactExports.useState(() => getRoundsForDifficulty(difficulty));
  const audioRef = reactExports.useRef({});
  const audioContextRef = reactExports.useRef(null);
  reactExports.useRef(null);
  const [metricsSession, setMetricsSession] = reactExports.useState({
    sessionId: null,
    isActive: false,
    insights: {}
  });
  const sendMetrics = reactExports.useCallback(async (metricsData) => {
    try {
      MusicalSequenceMetrics.recordAdvancedInteraction({
        ...metricsData,
        sessionId: metricsSession.sessionId,
        timestamp: Date.now()
      });
      return true;
    } catch (error) {
      console.error("Erro ao enviar métricas:", error);
      return false;
    }
  }, [metricsSession.sessionId]);
  reactExports.useCallback(async () => {
    try {
      const hub = MusicalSequenceMetrics.getCollectorsHub();
      if (hub) {
        return hub.getConsolidatedInsights?.() || {};
      }
      return {};
    } catch (error) {
      console.error("Erro ao obter insights:", error);
      return {};
    }
  }, []);
  const orchestratorReady = metricsSession.isActive;
  const explainGame = reactExports.useCallback(() => {
    speak("Jogo de Sequência Musical. Escute a sequência de instrumentos e reproduza-a na ordem correta. Desenvolva sua memória auditiva e percepção musical!", {
      rate: 0.8
    });
  }, [speak]);
  const getAccuracy = reactExports.useCallback(() => {
    if (totalAttempts === 0) return 100;
    return Math.round(correctAttempts / totalAttempts * 100);
  }, [totalAttempts, correctAttempts]);
  const generateNewSequence = reactExports.useCallback(async () => {
    console.log("generateNewSequence called with difficulty:", difficulty);
    const difficultyMap = {
      "easy": 3,
      "medium": 4,
      "hard": 5
    };
    const sequenceLength = difficultyMap[difficulty] || 3;
    console.log("sequenceLength:", sequenceLength);
    const newSequence = [];
    for (let i = 0; i < sequenceLength; i++) {
      const randomInstrument = instruments[Math.floor(Math.random() * instruments.length)];
      newSequence.push(randomInstrument.id);
    }
    console.log("Generated sequence:", newSequence);
    setSequence(newSequence);
    setPlayerSequence([]);
    setFeedback({ show: false, type: "", message: "" });
    try {
      const sequenceGenerationData = {
        timestamp: Date.now(),
        sequenceLength: newSequence.length,
        difficulty,
        level: currentLevel,
        round: currentRound,
        sequence: newSequence,
        sessionDuration: Date.now() - gameStartTime,
        gameState: "sequence_generated"
      };
      await collectorsHub2.processInteraction(sequenceGenerationData);
      if (recordInteraction) {
        recordInteraction({
          type: "sequence_generation",
          data: sequenceGenerationData
        });
      }
    } catch (error) {
      console.warn("⚠️ Erro ao coletar dados da sequência:", error);
    }
  }, [difficulty, currentLevel, currentRound, gameStartTime, collectorsHub2, recordInteraction]);
  reactExports.useEffect(() => {
    instruments.forEach((instrument) => {
      audioRef.current[instrument.id] = {
        play: () => {
          try {
            initAudioContext();
            if (audioContextRef.current.state === "closed") {
              console.warn("AudioContext fechado, não é possível reproduzir som");
              return;
            }
            if (audioContextRef.current.state === "suspended") {
              audioContextRef.current.resume();
            }
            const oscillator = audioContextRef.current.createOscillator();
            const gainNode = audioContextRef.current.createGain();
            oscillator.connect(gainNode);
            gainNode.connect(audioContextRef.current.destination);
            const frequencies = {
              piano: 523.25,
              // C5
              guitar: 329.63,
              // E4
              drum: 146.83,
              // D3
              flute: 440,
              // A4
              violin: 659.25,
              // E5
              sax: 293.66
              // D4
            };
            oscillator.frequency.setValueAtTime(frequencies[instrument.id] || 440, audioContextRef.current.currentTime);
            oscillator.type = instrument.id === "drum" ? "square" : "sine";
            gainNode.gain.setValueAtTime(0.3, audioContextRef.current.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(1e-3, audioContextRef.current.currentTime + 0.5);
            oscillator.start(audioContextRef.current.currentTime);
            oscillator.stop(audioContextRef.current.currentTime + 0.5);
          } catch (error) {
            console.error("Error playing sound:", error);
          }
        }
      };
    });
    return () => {
      if (audioContextRef.current && audioContextRef.current.state !== "closed") {
        audioContextRef.current.close();
      }
      if ("speechSynthesis" in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, [initAudioContext]);
  reactExports.useEffect(() => {
    if (!showStartScreen && gameState.status === "playing" && difficulty && sequence.length === 0) {
      console.log("✅ Auto-starting sequence after game start...");
      const difficultyMap = { "easy": 3, "medium": 4, "hard": 5 };
      const sequenceLength = difficultyMap[difficulty] || 3;
      const newSequence = [];
      for (let i = 0; i < sequenceLength; i++) {
        const randomInstrument = instruments[Math.floor(Math.random() * instruments.length)];
        newSequence.push(randomInstrument.id);
      }
      setSequence(newSequence);
      setPlayerSequence([]);
    }
  }, [showStartScreen, difficulty]);
  reactExports.useEffect(() => {
    if (sequence.length > 0 && !isPlaying && !showStartScreen && gameState.currentActivity === ACTIVITY_TYPES.SEQUENCE_REPRODUCTION.id) {
      console.log("✅ Auto-playing sequence after generation...");
      const timer = setTimeout(() => {
        playSequence();
      }, 1e3);
      return () => clearTimeout(timer);
    }
  }, [sequence, gameState.currentActivity]);
  const playSoundInstrument = reactExports.useCallback(async (instrumentId) => {
    try {
      if (audioRef.current[instrumentId]) {
        audioRef.current[instrumentId].play();
      }
    } catch (error) {
      console.error("Error playing sound:", error);
    }
  }, []);
  const playSequence = reactExports.useCallback(async () => {
    console.log("playSequence called with sequence:", sequence);
    setIsPlaying(true);
    speak(`Agora ouça atentamente a sequência de ${sequence.length} instrumentos.`, {
      rate: 1
    });
    await new Promise((resolve) => setTimeout(resolve, 1500));
    for (let i = 0; i < sequence.length; i++) {
      console.log(`Playing instrument ${i}:`, sequence[i]);
      setActiveInstrument(sequence[i]);
      await playSoundInstrument(sequence[i]);
      await new Promise((resolve) => setTimeout(resolve, 600));
      setActiveInstrument(null);
      await new Promise((resolve) => setTimeout(resolve, 400));
    }
    console.log("Sequence finished, ready for user input");
    setIsPlaying(false);
    speak("Agora é sua vez! Toque os instrumentos na ordem correta.", {
      rate: 1
    });
  }, [sequence, speak, playSound, setIsPlaying, setGameState, setActiveInstrument]);
  const handleInstrumentClick = reactExports.useCallback(async (instrumentId) => {
    console.log("handleInstrumentClick called:", { instrumentId, gameState: gameState.status, isPlaying, playerSequenceLength: playerSequence.length });
    if (isPlaying) {
      console.log("Click ignorado - aguarde a sequência terminar");
      return;
    }
    if (gameState.status !== "playing" || sequence.length === 0) {
      console.log("Click ignorado - jogo não iniciado ou sem sequência");
      return;
    }
    const nextIndex = playerSequence.length;
    const expectedInstrument = sequence[nextIndex];
    const startTime = Date.now();
    console.log("Expected:", expectedInstrument, "Clicked:", instrumentId);
    setActiveInstrument(instrumentId);
    await playSoundInstrument(instrumentId);
    setTimeout(() => setActiveInstrument(null), 500);
    const newPlayerSequence = [...playerSequence, instrumentId];
    setPlayerSequence(newPlayerSequence);
    setTotalAttempts((prev) => prev + 1);
    const isCorrect = instrumentId === expectedInstrument;
    const responseTime = Date.now() - startTime;
    try {
      const interactionData = {
        type: "instrument_click",
        action: "sequence_attempt",
        instrumentClicked: instrumentId,
        expectedInstrument,
        isCorrect,
        sequencePosition: nextIndex,
        totalSequenceLength: sequence.length,
        currentSequence: sequence,
        playerSequence: newPlayerSequence,
        responseTime,
        difficulty,
        level: currentLevel,
        round: currentRound,
        streak,
        gameState: gameState.status,
        timestamp: Date.now(),
        // Dados específicos para análise de memória auditiva
        memoryLoad: sequence.length,
        sequenceProgress: (nextIndex + 1) / sequence.length,
        // Dados para análise de padrões musicais
        instrumentFrequency: newPlayerSequence.filter((inst) => inst === instrumentId).length,
        lastInstruments: newPlayerSequence.slice(-3),
        // Dados para análise de execução
        attemptNumber: totalAttempts + 1,
        sessionTime: Date.now() - gameStartTime
      };
      MusicalSequenceMetrics.recordAdvancedInteraction(interactionData);
      if (orchestratorReady && sendMetrics) {
        sendMetrics({
          gameType: "musical_sequence",
          sessionId: `musical_${gameStartTime}`,
          metrics: interactionData,
          timestamp: Date.now()
        });
      }
    } catch (error) {
      console.error("Erro na coleta avançada:", error);
    }
    if (!isCorrect) {
      console.log("Wrong instrument!");
      setStreak(0);
      setFeedback({
        show: true,
        type: "error",
        message: "Ops! Instrumento incorreto. Vamos tentar novamente!"
      });
      speak("Ops! Instrumento incorreto. Vamos tentar novamente!", {
        rate: 0.9,
        pitch: 0.8
      });
      setTimeout(() => {
        setFeedback({ show: false, type: "", message: "" });
        setPlayerSequence([]);
        playSequence();
      }, 2e3);
      return;
    }
    console.log("Correct instrument!");
    setCorrectAttempts((prev) => prev + 1);
    if (newPlayerSequence.length === sequence.length) {
      console.log("Sequence completed!");
      const roundScore = sequence.length * 10;
      setScore((prev) => prev + roundScore);
      const newStreak = streak + 1;
      setStreak(newStreak);
      try {
        const sequenceCompletionData = {
          type: "sequence_completion",
          action: "sequence_completed",
          targetSequence: sequence,
          playerSequence: newPlayerSequence,
          isCorrect: true,
          completionTime: Date.now() - gameStartTime,
          difficulty,
          level: currentLevel,
          round: currentRound,
          score: score + roundScore,
          newStreak,
          totalAttempts: totalAttempts + 1,
          correctAttempts: correctAttempts + 1,
          // Dados específicos para análise de aprendizado
          sequenceLength: sequence.length,
          accuracy: (correctAttempts + 1) / (totalAttempts + 1) * 100,
          improvementIndicator: newStreak > streak,
          timestamp: Date.now()
        };
        MusicalSequenceMetrics.recordAdvancedInteraction(sequenceCompletionData);
        if (orchestratorReady && sendMetrics) {
          sendMetrics({
            gameType: "musical_sequence",
            sessionId: `musical_${gameStartTime}`,
            metrics: sequenceCompletionData,
            timestamp: Date.now()
          });
        }
      } catch (error) {
        console.error("Erro na coleta de dados de sequência completa:", error);
      }
      setFeedback({
        show: true,
        type: "success",
        message: "🎉 Excelente! Sequência correta!"
      });
      speak("Excelente! Sequência correta! Parabéns!", {
        rate: 1,
        pitch: 1.2
      });
      setTimeout(() => {
        setFeedback({ show: false, type: "", message: "" });
        if (newStreak % 3 === 0) {
          setCurrentLevel((prev) => prev + 1);
        }
        setCurrentRound((prev) => {
          const nextRound = prev + 1;
          const maxRounds = getRoundsForDifficulty(difficulty);
          if (nextRound > maxRounds) {
            setCurrentLevel((prevLevel) => prevLevel + 1);
            return 1;
          }
          return nextRound;
        });
        setPlayerSequence([]);
        generateNewSequence();
      }, 2e3);
    }
  }, [gameState, isPlaying, playerSequence, sequence, totalAttempts, setActiveInstrument, playSound, setPlayerSequence, setTotalAttempts, difficulty, currentLevel, currentRound, streak, gameStartTime, correctAttempts, score, setStreak, setFeedback, speak, setCorrectAttempts, setScore, setCurrentLevel, generateNewSequence, orchestratorReady, sendMetrics]);
  reactExports.useCallback(() => {
    speak("Sequência Musical. Escute a sequência de instrumentos musicais e repita tocando na mesma ordem. Use os botões da sequência para reproduzir a ordem que você ouviu.");
  }, [speak]);
  const playRhythmPattern = reactExports.useCallback(async () => {
    const activityData = gameState.activityData?.rhythmTiming || {};
    const { targetBPM, totalBeats } = activityData;
    if (!targetBPM || !totalBeats) {
      console.warn("Dados do ritmo não encontrados");
      return;
    }
    setIsPlaying(true);
    speak("Ouça o padrão rítmico da bateria atentamente.");
    await new Promise((resolve) => setTimeout(resolve, 1500));
    const rhythmPattern = generateRhythmPattern(difficulty);
    console.log("🥁 Padrão rítmico gerado:", rhythmPattern);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        rhythmTiming: {
          ...prev.activityData.rhythmTiming,
          pattern: rhythmPattern,
          gamePhase: "listening",
          currentBeat: 0
        }
      }
    }));
    for (let i = 0; i < rhythmPattern.length; i++) {
      const duration = rhythmPattern[i];
      setActiveInstrument(`beat-${i}`);
      console.log(`🥁 Tocando batida ${i + 1}/${rhythmPattern.length}, duração: ${duration}ms`);
      if (duration > 500) {
        await playSound("DRUM", 200);
      } else if (duration > 300) {
        await playSound("BEAT", 150);
      } else {
        await playSound("TAP", 100);
      }
      setActiveInstrument(null);
      if (i < rhythmPattern.length - 1) {
        await new Promise((resolve) => setTimeout(resolve, 200));
      }
    }
    setIsPlaying(false);
    speak("Agora é sua vez! Toque no botão da bateria seguindo o mesmo padrão rítmico.");
  }, [gameState.activityData?.rhythmTiming, difficulty, playSound, speak, setIsPlaying, setActiveInstrument]);
  const generateRhythmPattern = reactExports.useCallback((diff) => {
    const patterns = {
      "easy": [400, 400, 400, 400],
      // Ritmo simples e uniforme
      "medium": [300, 600, 300, 600, 300],
      // Alternado
      "hard": [200, 400, 200, 800, 200, 400]
      // Complexo
    };
    return patterns[diff] || patterns["easy"];
  }, []);
  const toggleRhythmRecording = reactExports.useCallback(() => {
    setGameState((prev) => {
      const currentData = prev.activityData?.rhythmTiming || {};
      const isCurrentlyRecording = currentData.isRecording;
      if (isCurrentlyRecording) {
        console.log("🛑 Parando gravação de ritmo");
        calculateRhythmAccuracy();
        speak("Gravação finalizada! Calculando sua precisão...");
        return {
          ...prev,
          activityData: {
            ...prev.activityData,
            rhythmTiming: {
              ...currentData,
              isRecording: false,
              gamePhase: "finished"
            }
          }
        };
      } else {
        console.log("🔴 Iniciando gravação de ritmo");
        speak("Gravação iniciada! Toque na bateria seguindo o padrão rítmico que você ouviu.");
        return {
          ...prev,
          activityData: {
            ...prev.activityData,
            rhythmTiming: {
              ...currentData,
              isRecording: true,
              gamePhase: "recording",
              // Definir fase específica para gravação
              userTaps: [],
              startTime: Date.now()
            }
          }
        };
      }
    });
  }, [speak]);
  reactExports.useCallback(async () => {
    const activityData = gameState.activityData?.rhythmTiming || {};
    const { targetBPM, totalBeats } = activityData;
    if (!targetBPM || !totalBeats) {
      console.warn("Dados do ritmo não encontrados");
      return;
    }
    const beatInterval = 60 / targetBPM * 1e3;
    console.log(`🥁 Iniciando metrônomo: ${targetBPM} BPM, Intervalo: ${beatInterval}ms`);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        rhythmTiming: {
          ...prev.activityData.rhythmTiming,
          metronomeActive: true,
          gamePhase: "listening",
          currentBeat: 0,
          beatInterval
          // Atualizar o intervalo calculado
        }
      }
    }));
    speak(`Ouça o metrônomo no ritmo de ${targetBPM} batidas por minuto. Depois toque junto!`);
    await new Promise((resolve) => setTimeout(resolve, 2e3));
    for (let beat = 1; beat <= totalBeats; beat++) {
      setGameState((prev) => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          rhythmTiming: {
            ...prev.activityData.rhythmTiming,
            currentBeat: beat
          }
        }
      }));
      console.log(`🥁 Batida ${beat}/${totalBeats}`);
      const isDrumSound = beat === 1 || beat % 4 === 1;
      await playSound(isDrumSound ? "DRUM" : "BEAT", 150);
      if (beat < totalBeats) {
        await new Promise((resolve) => setTimeout(resolve, beatInterval));
      }
    }
    setTimeout(() => {
      setGameState((prev) => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          rhythmTiming: {
            ...prev.activityData.rhythmTiming,
            gamePhase: "playing",
            currentBeat: 0,
            startTime: Date.now(),
            userTaps: []
          }
        }
      }));
      speak("Agora toque junto! Siga o metrônomo e mantenha o ritmo.");
      startPlayingPhase();
    }, 1e3);
  }, [gameState.activityData?.rhythmTiming, playSound, speak]);
  const startPlayingPhase = reactExports.useCallback(async () => {
    const activityData = gameState.activityData?.rhythmTiming || {};
    const { targetBPM, totalBeats, beatInterval } = activityData;
    console.log(`🎵 Iniciando fase de tocar junto: ${targetBPM} BPM`);
    for (let beat = 1; beat <= totalBeats; beat++) {
      setGameState((prev) => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          rhythmTiming: {
            ...prev.activityData.rhythmTiming,
            currentBeat: beat
          }
        }
      }));
      console.log(`🎵 Batida guia ${beat}/${totalBeats}`);
      await playSound("BEAT", 80);
      if (beat < totalBeats) {
        await new Promise((resolve) => setTimeout(resolve, beatInterval));
      }
    }
    setTimeout(() => {
      console.log("🏁 Finalizando teste de ritmo");
      calculateRhythmAccuracy();
    }, 500);
  }, [gameState.activityData?.rhythmTiming, playSound]);
  const handleRhythmTap = reactExports.useCallback(() => {
    const now = Date.now();
    const activityData = gameState.activityData?.rhythmTiming || {};
    console.log("🥁 CLIQUE NA BATERIA DETECTADO!");
    console.log("Estado atual:", {
      gamePhase: activityData.gamePhase,
      isRecording: activityData.isRecording,
      isPlaying
    });
    if (isPlaying) {
      console.log("🚫 Aguarde a sequência terminar");
      return;
    }
    console.log("🥁 TOCANDO SOM DA BATERIA!");
    setActiveInstrument("tap");
    setTimeout(() => setActiveInstrument(null), 200);
    playSound("TAP", 200);
    if (activityData.isRecording || activityData.gamePhase === "recording" || activityData.gamePhase === "playing") {
      console.log("📝 Registrando toque no ritmo");
      setGameState((prev) => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          rhythmTiming: {
            ...prev.activityData.rhythmTiming,
            userTaps: [...prev.activityData.rhythmTiming.userTaps || [], now]
          }
        }
      }));
      console.log(`🎯 Total de toques registrados: ${(activityData.userTaps?.length || 0) + 1}`);
    } else {
      console.log("🎵 Som tocado sem registrar (apenas feedback)");
    }
  }, [gameState.activityData?.rhythmTiming, playSound, isPlaying]);
  const calculateRhythmAccuracy = reactExports.useCallback(() => {
    setGameState((prev) => {
      const currentData = prev.activityData?.rhythmTiming || {};
      const { userTaps, startTime, targetBPM, totalBeats, beatInterval } = currentData;
      console.log("🎯 Calculando precisão do ritmo:", {
        userTaps: userTaps?.length || 0,
        targetBPM,
        totalBeats,
        beatInterval
      });
      if (!userTaps || userTaps.length === 0) {
        speak("Você não tocou nenhuma batida na bateria. Tente novamente!");
        return {
          ...prev,
          activityData: {
            ...prev.activityData,
            rhythmTiming: {
              ...currentData,
              gamePhase: "finished",
              accuracy: 0,
              perfectHits: 0
            }
          }
        };
      }
      let accuracy = 0;
      let perfectHits = 0;
      if (startTime && beatInterval && totalBeats) {
        const expectedBeats = [];
        for (let i = 0; i < totalBeats; i++) {
          expectedBeats.push(startTime + i * beatInterval);
        }
        console.log("🎵 Batidas esperadas:", expectedBeats.map((beat) => new Date(beat).toLocaleTimeString()));
        console.log("🥁 Batidas do usuário:", userTaps.map((tap) => new Date(tap).toLocaleTimeString()));
        const tolerance = Math.max(beatInterval * 0.25, 200);
        console.log(`⏰ Tolerância: ${tolerance}ms`);
        expectedBeats.forEach((expectedTime, index) => {
          const closestTap = userTaps.reduce((closest, tapTime) => {
            const currentDistance = Math.abs(tapTime - expectedTime);
            const closestDistance = Math.abs(closest - expectedTime);
            return currentDistance < closestDistance ? tapTime : closest;
          });
          const error = Math.abs(closestTap - expectedTime);
          console.log(`🎯 Batida ${index + 1}: esperada em ${new Date(expectedTime).toLocaleTimeString()}, usuário tocou em ${new Date(closestTap).toLocaleTimeString()}, erro: ${error}ms`);
          if (error <= tolerance) {
            perfectHits++;
            console.log("✅ Batida correta!");
          } else {
            console.log("❌ Batida fora do tempo");
          }
        });
        accuracy = Math.max(0, Math.round(perfectHits / totalBeats * 100));
      } else {
        console.warn("⚠️ Dados insuficientes para calcular precisão");
        accuracy = 0;
      }
      console.log(`🏆 Resultado final: ${perfectHits}/${totalBeats} corretas, ${accuracy}% de precisão`);
      const feedbackMessage = accuracy >= 80 ? "Excelente timing na bateria!" : accuracy >= 60 ? "Bom ritmo! Continue praticando!" : "Continue praticando o timing da bateria!";
      speak(`Você acertou ${perfectHits} de ${totalBeats} batidas. Precisão: ${accuracy}%. ${feedbackMessage}`);
      setTimeout(() => {
        console.log("🔄 Gerando nova rodada de RITMO (mesma atividade)");
        generateRhythmTiming();
      }, 4e3);
      return {
        ...prev,
        activityData: {
          ...prev.activityData,
          rhythmTiming: {
            ...currentData,
            gamePhase: "finished",
            accuracy,
            perfectHits,
            metronomeActive: false,
            isRecording: false
          }
        }
      };
    });
  }, [speak, generateNewRound]);
  const playToneWithFrequency = reactExports.useCallback(async (frequency, duration = 1e3) => {
    console.log("🔊 TOCANDO FREQUÊNCIA:", frequency + "Hz por", duration + "ms");
    if (!audioContextRef.current || audioContextRef.current.state === "closed") {
      console.log("🎵 Reinicializando AudioContext...");
      audioContextRef.current = null;
      await initAudioContext();
    }
    if (audioContextRef.current && audioContextRef.current.state === "suspended") {
      console.log("🎵 Retomando AudioContext suspenso...");
      try {
        await audioContextRef.current.resume();
        console.log("✅ AudioContext retomado com sucesso");
      } catch (error) {
        console.error("❌ Erro ao retomar AudioContext:", error);
        audioContextRef.current = null;
        await initAudioContext();
      }
    }
    try {
      const audioContext = audioContextRef.current;
      if (!audioContext || audioContext.state === "closed") {
        console.error("❌ AudioContext ainda não disponível após reinicialização");
        try {
          const newAudioContext = new (window.AudioContext || window.webkitAudioContext)();
          audioContextRef.current = newAudioContext;
          console.log("🎵 Novo AudioContext criado diretamente");
        } catch (directError) {
          console.error("❌ Falha ao criar AudioContext diretamente:", directError);
          return;
        }
      }
      const finalAudioContext = audioContextRef.current;
      const oscillator = finalAudioContext.createOscillator();
      const gainNode = finalAudioContext.createGain();
      oscillator.connect(gainNode);
      gainNode.connect(finalAudioContext.destination);
      oscillator.frequency.setValueAtTime(frequency, finalAudioContext.currentTime);
      oscillator.type = "sine";
      gainNode.gain.setValueAtTime(0.3, finalAudioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, finalAudioContext.currentTime + duration / 1e3);
      oscillator.start(finalAudioContext.currentTime);
      oscillator.stop(finalAudioContext.currentTime + duration / 1e3);
      console.log("✅ Tom iniciado com sucesso");
      return new Promise((resolve) => {
        oscillator.onended = () => {
          console.log("🎵 Tom finalizado");
          resolve();
        };
      });
    } catch (error) {
      console.error("❌ Erro ao tocar frequência:", error);
      console.log("🎵 Tentando usar playSound como fallback...");
      try {
        await playSound("PIANO", duration);
      } catch (fallbackError) {
        console.error("❌ Fallback também falhou:", fallbackError);
      }
    }
  }, [initAudioContext, playSound]);
  const playReferencePitch = reactExports.useCallback(async () => {
    const activityData = gameState.activityData?.pitchDiscrimination || {};
    const { referencePitch, pitchSystem } = activityData;
    console.log("🎵 TOCANDO TOM DE REFERÊNCIA MELHORADO:", referencePitch + "Hz");
    if (!referencePitch) {
      console.warn("❌ Frequência de referência não encontrada!");
      speak("Erro: Tom de referência não disponível.");
      return;
    }
    if (audioContextRef.current && audioContextRef.current.state === "suspended") {
      try {
        await audioContextRef.current.resume();
        console.log("🎵 AudioContext desbloqueado por interação do usuário");
      } catch (error) {
        console.error("❌ Erro ao desbloquear AudioContext:", error);
      }
    }
    setIsPlaying(true);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        pitchDiscrimination: {
          ...prev.activityData.pitchDiscrimination,
          currentPhase: "reference",
          hasPlayedReference: true
        }
      }
    }));
    speak("Tom de referência - Lá 440 Hz");
    await new Promise((resolve) => setTimeout(resolve, 1e3));
    console.log("🔊 Iniciando tom de referência com frequência:", referencePitch);
    await playToneWithFrequency(referencePitch, 2500);
    console.log("✅ Tom de referência finalizado");
    setTimeout(() => {
      setIsPlaying(false);
      setGameState((prev) => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          pitchDiscrimination: {
            ...prev.activityData.pitchDiscrimination,
            currentPhase: "waiting_test"
          }
        }
      }));
      speak("Agora clique para ouvir o tom de teste.");
    }, 2500);
  }, [gameState.activityData?.pitchDiscrimination, playToneWithFrequency, speak]);
  const playTestPitch = reactExports.useCallback(async () => {
    const activityData = gameState.activityData?.pitchDiscrimination || {};
    const { targetPitch, targetPitchObj } = activityData;
    console.log("🎵 TOCANDO TOM DE TESTE MELHORADO:", targetPitch + "Hz");
    if (!targetPitch) {
      console.warn("❌ Frequência de teste não encontrada!");
      speak("Erro: Tom de teste não disponível.");
      return;
    }
    if (audioContextRef.current && audioContextRef.current.state === "suspended") {
      try {
        await audioContextRef.current.resume();
        console.log("🎵 AudioContext desbloqueado por interação do usuário");
      } catch (error) {
        console.error("❌ Erro ao desbloquear AudioContext:", error);
      }
    }
    setIsPlaying(true);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        pitchDiscrimination: {
          ...prev.activityData.pitchDiscrimination,
          currentPhase: "test",
          hasPlayedTest: true
        }
      }
    }));
    const pitchName = targetPitchObj?.name || `${targetPitch}Hz`;
    speak(`Tom de teste - ${pitchName}`);
    await new Promise((resolve) => setTimeout(resolve, 1e3));
    console.log("🔊 Iniciando tom de teste com frequência:", targetPitch);
    await playToneWithFrequency(targetPitch, 2500);
    console.log("✅ Tom de teste finalizado");
    setTimeout(() => {
      setIsPlaying(false);
      setGameState((prev) => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          pitchDiscrimination: {
            ...prev.activityData.pitchDiscrimination,
            currentPhase: "comparison",
            canAnswer: true
          }
        }
      }));
      speak("Agora compare os dois tons. O tom de teste é mais agudo, mais grave ou igual ao tom de referência?");
    }, 2500);
  }, [gameState.activityData?.pitchDiscrimination, playToneWithFrequency, speak]);
  reactExports.useCallback(async () => {
    const activityData = gameState.activityData?.pitchDiscrimination || {};
    const { referencePitch, targetPitch } = activityData;
    if (!referencePitch || !targetPitch) {
      speak("Erro: Tons não disponíveis para comparação.");
      return;
    }
    setIsPlaying(true);
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        pitchDiscrimination: {
          ...prev.activityData.pitchDiscrimination,
          currentPhase: "comparing"
        }
      }
    }));
    speak("Comparação: primeiro o tom de referência, depois o tom de teste.");
    await new Promise((resolve) => setTimeout(resolve, 1500));
    console.log("🔊 Comparação - Tom de referência:", referencePitch);
    await playToneWithFrequency(referencePitch, 2e3);
    await new Promise((resolve) => setTimeout(resolve, 500));
    console.log("🔊 Comparação - Tom de teste:", targetPitch);
    await playToneWithFrequency(targetPitch, 2e3);
    setTimeout(() => {
      setIsPlaying(false);
      setGameState((prev) => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          pitchDiscrimination: {
            ...prev.activityData.pitchDiscrimination,
            currentPhase: "answer",
            canAnswer: true
          }
        }
      }));
      speak("Comparação concluída. Agora escolha sua resposta.");
    }, 2e3);
  }, [gameState.activityData?.pitchDiscrimination, playToneWithFrequency, speak]);
  const evaluatePitchAnswer = reactExports.useCallback((userAnswer) => {
    const activityData = gameState.activityData?.pitchDiscrimination || {};
    const { correctAnswer, referencePitch, targetPitch, frequencyDifference, difficulty: difficulty2 } = activityData;
    const isCorrect = userAnswer === correctAnswer;
    let score2 = 0;
    if (isCorrect) {
      score2 = difficulty2 === "hard" ? 100 : difficulty2 === "medium" ? 85 : 70;
    }
    let feedback2 = "";
    const freqDiff = Math.abs(targetPitch - referencePitch);
    if (isCorrect) {
      feedback2 = `Excelente! O tom de teste (${targetPitch}Hz) é realmente ${correctAnswer === "higher" ? "mais agudo" : correctAnswer === "lower" ? "mais grave" : "igual"} que o tom de referência (${referencePitch}Hz). `;
      if (difficulty2 === "hard") {
        feedback2 += `Diferença sutil de apenas ${freqDiff}Hz detectada com precisão!`;
      } else if (difficulty2 === "medium") {
        feedback2 += `Boa percepção da diferença de ${freqDiff}Hz!`;
      } else {
        feedback2 += `Diferença de ${freqDiff}Hz identificada corretamente!`;
      }
    } else {
      feedback2 = `Não é bem assim. O tom de teste (${targetPitch}Hz) é ${correctAnswer === "higher" ? "mais agudo" : correctAnswer === "lower" ? "mais grave" : "igual"} que o tom de referência (${referencePitch}Hz). `;
      feedback2 += `A diferença é de ${freqDiff}Hz. Tente ouvir novamente com mais atenção.`;
    }
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        pitchDiscrimination: {
          ...prev.activityData.pitchDiscrimination,
          userAnswer,
          isCorrect,
          score: score2,
          feedback: feedback2,
          currentPhase: "result",
          showResult: true
        }
      }
    }));
    speak(feedback2);
    if (isCorrect) {
      setTimeout(() => {
        generateNewRound();
      }, 4e3);
    } else {
      setTimeout(() => {
        setGameState((prev) => ({
          ...prev,
          activityData: {
            ...prev.activityData,
            pitchDiscrimination: {
              ...prev.activityData.pitchDiscrimination,
              currentPhase: "comparison",
              canAnswer: true,
              showResult: false
            }
          }
        }));
      }, 5e3);
    }
  }, [gameState.activityData?.pitchDiscrimination, speak, generateNewRound]);
  reactExports.useCallback((userAnswer) => {
    const activityData = gameState.activityData?.pitchDiscrimination || {};
    const { referencePitch, targetPitch } = activityData;
    if (!userAnswer || !referencePitch || !targetPitch) {
      speak("Por favor, selecione uma resposta.");
      return;
    }
    console.log("🎵 Verificando resposta de altura:", {
      referencia: referencePitch,
      teste: targetPitch,
      resposta: userAnswer
    });
    let correctAnswer = "";
    if (targetPitch > referencePitch) {
      correctAnswer = "higher";
    } else if (targetPitch < referencePitch) {
      correctAnswer = "lower";
    } else {
      correctAnswer = "same";
    }
    const isCorrect = userAnswer === correctAnswer;
    let feedbackMessage = "";
    if (isCorrect) {
      feedbackMessage = `Correto! O tom teste (${targetPitch}Hz) é `;
      if (correctAnswer === "higher") {
        feedbackMessage += `mais alto (agudo) que o tom referência (${referencePitch}Hz).`;
      } else if (correctAnswer === "lower") {
        feedbackMessage += `mais baixo (grave) que o tom referência (${referencePitch}Hz).`;
      } else {
        feedbackMessage += `igual ao tom referência (${referencePitch}Hz).`;
      }
    } else {
      feedbackMessage = `Incorreto. O tom teste (${targetPitch}Hz) `;
      if (correctAnswer === "higher") {
        feedbackMessage += `é mais alto (agudo) que a referência (${referencePitch}Hz).`;
      } else if (correctAnswer === "lower") {
        feedbackMessage += `é mais baixo (grave) que a referência (${referencePitch}Hz).`;
      } else {
        feedbackMessage += `é igual à referência (${referencePitch}Hz).`;
      }
    }
    setGameState((prev) => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        pitchDiscrimination: {
          ...prev.activityData.pitchDiscrimination,
          isAnswered: true,
          isCorrect,
          correctAnswer,
          feedback: feedbackMessage
        }
      }
    }));
    speak(feedbackMessage);
    setTimeout(() => {
      generatePitchDiscrimination();
    }, 4e3);
  }, [gameState.activityData?.pitchDiscrimination, speak, generatePitchDiscrimination]);
  const renderSequenceReproduction = () => {
    gameState.activityData?.sequenceReproduction || {};
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionArea, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionHeader, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: styles.questionTitle, children: "🎵 Sequência Musical" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2259,
          columnNumber: 11
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: styles.questionSubtitle, children: "Memorize a ordem exata dos instrumentos e reproduza a sequência" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2260,
          columnNumber: 11
        }, void 0)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2258,
        columnNumber: 9
      }, void 0),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.objectsDisplay, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.sequenceSlots, children: sequence.map((instrumentId, index) => {
        const instrument = instruments.find((inst) => inst.id === instrumentId);
        const isCurrentlyPlaying = isPlaying && activeInstrument === instrumentId;
        const isCompleted = playerSequence.length > index;
        const isCorrect = isCompleted && playerSequence[index] === instrumentId;
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "div",
          {
            className: `${styles.countingObject} ${isCurrentlyPlaying ? styles.playing : ""} ${isCompleted ? isCorrect ? styles.correct : styles.incorrect : ""}`,
            style: {
              backgroundColor: isCorrect ? "rgba(76, 175, 80, 0.3)" : isCompleted ? "rgba(244, 67, 54, 0.3)" : "var(--card-background)",
              border: "2px solid rgba(255,255,255,0.3)",
              borderRadius: "12px",
              padding: "1rem",
              margin: "0.5rem",
              display: "flex",
              flexDirection: "column",
              alignItems: "center"
            },
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.8rem", opacity: 0.8 }, children: index + 1 }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2291,
                columnNumber: 19
              }, void 0),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "3rem", margin: "0.5rem 0" }, children: isCurrentlyPlaying ? instrument?.emoji : "?" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2292,
                columnNumber: 19
              }, void 0),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.9rem" }, children: instrument?.name }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2295,
                columnNumber: 19
              }, void 0)
            ]
          },
          index,
          true,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2273,
            columnNumber: 17
          },
          void 0
        );
      }) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2265,
        columnNumber: 11
      }, void 0) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2264,
        columnNumber: 9
      }, void 0),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.answerOptions, children: instruments.map((instrument) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "button",
        {
          className: `${styles.answerButton} ${activeInstrument === instrument.id ? styles.playing : ""}`,
          onClick: () => handleInstrumentClick(instrument.id),
          disabled: isPlaying || playerSequence.length >= sequence.length,
          "aria-label": `Escolher ${instrument.name}`,
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
              fontSize: "2rem",
              marginBottom: "0.5rem"
            }, children: instrument.emoji }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
              lineNumber: 2314,
              columnNumber: 15
            }, void 0),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.9rem" }, children: instrument.name }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
              lineNumber: 2320,
              columnNumber: 15
            }, void 0)
          ]
        },
        instrument.id,
        true,
        {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2305,
          columnNumber: 13
        },
        void 0
      )) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2303,
        columnNumber: 9
      }, void 0),
      playerSequence.length > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
        textAlign: "center",
        marginTop: "1rem",
        color: "rgba(255,255,255,0.8)"
      }, children: [
        "Progresso: ",
        playerSequence.length,
        " / ",
        sequence.length
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2329,
        columnNumber: 11
      }, void 0)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
      lineNumber: 2257,
      columnNumber: 7
    }, void 0);
  };
  const renderRhythmTiming = () => {
    const activityData = gameState.activityData?.rhythmTiming || {};
    const pattern = activityData.pattern || [];
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionArea, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionHeader, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: styles.questionTitle, children: "🥁 Ritmo Musical" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2349,
          columnNumber: 11
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: styles.questionSubtitle, children: "1° Ouça o padrão rítmico | 2° Reproduza tocando no mesmo ritmo | 3° Tente ser preciso!" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2350,
          columnNumber: 11
        }, void 0)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2348,
        columnNumber: 9
      }, void 0),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.objectsDisplay, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.rhythmDisplay, style: {
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        gap: "2rem",
        padding: "2rem",
        backgroundColor: "var(--card-background)",
        borderRadius: "16px",
        border: "2px solid rgba(255,255,255,0.3)"
      }, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
          fontSize: "4rem",
          animation: isPlaying ? "pulse 1s infinite" : "none"
        }, children: "🎼" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2367,
          columnNumber: 13
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
          fontSize: "1.4rem",
          color: "rgba(255,255,255,0.9)",
          fontWeight: "bold",
          textAlign: "center"
        }, children: isPlaying ? "🔊 Tocando Padrão..." : activityData.isRecording ? "🔴 Gravando seu Ritmo" : "▶️ Clique para Ouvir o Padrão" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2374,
          columnNumber: 13
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
          display: "flex",
          gap: "0.5rem",
          flexWrap: "wrap",
          justifyContent: "center"
        }, children: pattern.map((duration, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "div",
          {
            style: {
              width: `${Math.max(20, duration / 20)}px`,
              height: "20px",
              backgroundColor: isPlaying && activeInstrument === `beat-${index}` ? "rgba(255, 193, 7, 0.8)" : "rgba(255,255,255,0.6)",
              borderRadius: "10px",
              transition: "all 0.3s ease"
            }
          },
          index,
          false,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2391,
            columnNumber: 17
          },
          void 0
        )) }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2384,
          columnNumber: 13
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "1rem", color: "rgba(255,255,255,0.7)" }, children: [
          "Padrão: ",
          pattern.length,
          " batidas | BPM: ",
          activityData.targetBPM || 120
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2405,
          columnNumber: 13
        }, void 0)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2357,
        columnNumber: 11
      }, void 0) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2356,
        columnNumber: 9
      }, void 0),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.answerOptions, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            className: `${styles.answerButton} ${isPlaying ? styles.playing : ""}`,
            onClick: () => playRhythmPattern(),
            disabled: isPlaying || activityData.isRecording,
            "aria-label": "Ouvir padrão rítmico",
            style: {
              backgroundColor: isPlaying ? "rgba(76, 175, 80, 0.3)" : "var(--card-background)",
              borderColor: isPlaying ? "rgba(76, 175, 80, 0.5)" : "rgba(255,255,255,0.3)"
            },
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "2rem", marginBottom: "0.5rem" }, children: isPlaying ? "🔊" : "▶️" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2424,
                columnNumber: 13
              }, void 0),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.9rem" }, children: isPlaying ? "Tocando..." : "Ouvir Padrão" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2427,
                columnNumber: 13
              }, void 0)
            ]
          },
          void 0,
          true,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2414,
            columnNumber: 11
          },
          void 0
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            className: `${styles.answerButton} ${activeInstrument === "tap" ? styles.playing : ""}`,
            onClick: () => handleRhythmTap(),
            disabled: false,
            "aria-label": "Tocar bateria",
            style: {
              minHeight: "120px",
              fontSize: "3rem",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: "0.5rem",
              backgroundColor: activityData.isRecording ? "rgba(244, 67, 54, 0.3)" : activeInstrument === "tap" ? "rgba(255, 193, 7, 0.5)" : "var(--card-background)",
              borderColor: activityData.isRecording ? "rgba(244, 67, 54, 0.5)" : activeInstrument === "tap" ? "rgba(255, 193, 7, 0.8)" : "rgba(255,255,255,0.3)",
              cursor: "pointer"
            },
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: activityData.isRecording ? "🔴" : "🥁" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2452,
                columnNumber: 13
              }, void 0),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "1rem" }, children: activityData.isRecording ? "GRAVANDO..." : "TOQUE AQUI" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2453,
                columnNumber: 13
              }, void 0)
            ]
          },
          void 0,
          true,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2433,
            columnNumber: 11
          },
          void 0
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            className: styles.answerButton,
            onClick: () => toggleRhythmRecording(),
            disabled: isPlaying || activityData.gamePhase === "finished",
            "aria-label": activityData.isRecording ? "Parar e avaliar" : "Iniciar gravação",
            style: {
              backgroundColor: activityData.isRecording ? "rgba(244, 67, 54, 0.3)" : activityData.gamePhase === "finished" ? "rgba(76, 175, 80, 0.3)" : "var(--card-background)",
              borderColor: activityData.isRecording ? "rgba(244, 67, 54, 0.5)" : activityData.gamePhase === "finished" ? "rgba(76, 175, 80, 0.5)" : "rgba(255,255,255,0.3)"
            },
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "2rem", marginBottom: "0.5rem" }, children: activityData.isRecording ? "⏹️" : activityData.gamePhase === "finished" ? "✅" : "🎙️" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2471,
                columnNumber: 13
              }, void 0),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.9rem" }, children: activityData.isRecording ? "Finalizar" : activityData.gamePhase === "finished" ? "Concluído" : "Gravar" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2475,
                columnNumber: 13
              }, void 0)
            ]
          },
          void 0,
          true,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2459,
            columnNumber: 11
          },
          void 0
        ),
        activityData.gamePhase === "finished" && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            className: styles.answerButton,
            onClick: () => generateRhythmTiming(),
            style: {
              backgroundColor: "rgba(33, 150, 243, 0.3)",
              borderColor: "rgba(33, 150, 243, 0.5)"
            },
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "2rem", marginBottom: "0.5rem" }, children: "🥁" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2491,
                columnNumber: 15
              }, void 0),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.9rem" }, children: "Novo Ritmo" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2492,
                columnNumber: 15
              }, void 0)
            ]
          },
          void 0,
          true,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2483,
            columnNumber: 13
          },
          void 0
        )
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2412,
        columnNumber: 9
      }, void 0),
      activityData.accuracy !== void 0 && activityData.accuracy > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
        textAlign: "center",
        marginTop: "1rem",
        padding: "1rem",
        backgroundColor: activityData.accuracy >= 70 ? "rgba(76, 175, 80, 0.2)" : "rgba(255, 193, 7, 0.2)",
        borderRadius: "8px",
        color: "white"
      }, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "1.2rem", fontWeight: "bold" }, children: [
          "Precisão: ",
          activityData.accuracy,
          "%"
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2507,
          columnNumber: 13
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.9rem", marginTop: "0.5rem" }, children: activityData.accuracy >= 70 ? "🎉 Excelente ritmo!" : "👍 Continue praticando!" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2510,
          columnNumber: 13
        }, void 0)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2499,
        columnNumber: 11
      }, void 0),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: {
        textAlign: "center",
        marginTop: "1rem",
        padding: "1rem",
        backgroundColor: "rgba(0,0,0,0.3)",
        borderRadius: "8px",
        color: "rgba(255,255,255,0.8)",
        fontSize: "0.9rem"
      }, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Como jogar:" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2526,
          columnNumber: 16
        }, void 0) }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2526,
          columnNumber: 11
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: '1. Clique em "Ouvir Padrão" para escutar o ritmo' }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2527,
          columnNumber: 11
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: "2. Toque no botão 🥁 seguindo o mesmo ritmo" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2528,
          columnNumber: 11
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { children: '3. Clique "Finalizar" para ver sua precisão' }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2529,
          columnNumber: 11
        }, void 0)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2517,
        columnNumber: 9
      }, void 0)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
      lineNumber: 2347,
      columnNumber: 7
    }, void 0);
  };
  const renderPitchDiscrimination = () => {
    const activityData = gameState.activityData?.pitchDiscrimination || {};
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionArea, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionHeader, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: styles.questionTitle, children: "🎼 Altura Musical" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2542,
          columnNumber: 11
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: styles.questionSubtitle, children: "Compare tons musicais e identifique diferenças sutis de altura" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2543,
          columnNumber: 11
        }, void 0)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2541,
        columnNumber: 9
      }, void 0),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.objectsDisplay, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.pitchComparisonContainer, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.pitchCard, "data-type": "reference", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.pitchIcon, children: "🎯" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2549,
            columnNumber: 15
          }, void 0),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: styles.pitchTitle, children: "Tom Referência" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2550,
            columnNumber: 15
          }, void 0),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.pitchFrequency, children: [
            activityData.referencePitch || 220,
            "Hz"
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2551,
            columnNumber: 15
          }, void 0),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              className: `${styles.pitchButton} ${styles.referenceButton}`,
              onClick: () => playReferencePitch(),
              disabled: isPlaying,
              children: isPlaying ? "🔊 Tocando..." : "▶️ Tocar Referência"
            },
            void 0,
            false,
            {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
              lineNumber: 2552,
              columnNumber: 15
            },
            void 0
          )
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2548,
          columnNumber: 13
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.vsIndicator, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.vsIcon, children: "⚖️" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2562,
            columnNumber: 15
          }, void 0),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.vsText, children: "VS" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2563,
            columnNumber: 15
          }, void 0)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2561,
          columnNumber: 13
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.pitchCard, "data-type": "test", children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.pitchIcon, children: "🎼" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2567,
            columnNumber: 15
          }, void 0),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: styles.pitchTitle, children: "Tom Teste" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2568,
            columnNumber: 15
          }, void 0),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.pitchFrequency, children: [
            activityData.targetPitch || 240,
            "Hz"
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2569,
            columnNumber: 15
          }, void 0),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              className: `${styles.pitchButton} ${styles.testButton}`,
              onClick: () => playTestPitch(),
              disabled: isPlaying,
              children: isPlaying ? "🔊 Tocando..." : "▶️ Tocar Teste"
            },
            void 0,
            false,
            {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
              lineNumber: 2570,
              columnNumber: 15
            },
            void 0
          )
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2566,
          columnNumber: 13
        }, void 0)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2547,
        columnNumber: 11
      }, void 0) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2546,
        columnNumber: 9
      }, void 0),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.answerOptions, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            className: `${styles.answerButton} ${styles.higherButton}`,
            onClick: () => evaluatePitchAnswer("higher"),
            disabled: isPlaying,
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.answerIcon, children: "⬆️" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2589,
                columnNumber: 13
              }, void 0),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.answerText, children: "MAIS AGUDO" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2590,
                columnNumber: 13
              }, void 0)
            ]
          },
          void 0,
          true,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2584,
            columnNumber: 11
          },
          void 0
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            className: `${styles.answerButton} ${styles.sameButton}`,
            onClick: () => evaluatePitchAnswer("same"),
            disabled: isPlaying,
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.answerIcon, children: "➡️" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2597,
                columnNumber: 13
              }, void 0),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.answerText, children: "IGUAL" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2598,
                columnNumber: 13
              }, void 0)
            ]
          },
          void 0,
          true,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2592,
            columnNumber: 11
          },
          void 0
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            className: `${styles.answerButton} ${styles.lowerButton}`,
            onClick: () => evaluatePitchAnswer("lower"),
            disabled: isPlaying,
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.answerIcon, children: "⬇️" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2605,
                columnNumber: 13
              }, void 0),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.answerText, children: "MAIS GRAVE" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2606,
                columnNumber: 13
              }, void 0)
            ]
          },
          void 0,
          true,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2600,
            columnNumber: 11
          },
          void 0
        )
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2583,
        columnNumber: 9
      }, void 0),
      activityData.showResult && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `${styles.feedbackContainer} ${activityData.isCorrect ? styles.correctFeedback : styles.incorrectFeedback}`, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.feedbackIcon, children: activityData.isCorrect ? "✅" : "❌" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2612,
          columnNumber: 13
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.feedbackTitle, children: activityData.isCorrect ? "Correto!" : "Tente novamente" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2615,
          columnNumber: 13
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.feedbackText, children: activityData.feedback }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2618,
          columnNumber: 13
        }, void 0)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2611,
        columnNumber: 11
      }, void 0)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
      lineNumber: 2540,
      columnNumber: 7
    }, void 0);
  };
  const renderPatternPrediction = () => {
    const activityData = gameState.activityData?.patternPrediction || {};
    return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionArea, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.questionHeader, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: styles.questionTitle, children: "🔮 Predições de Padrões" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2634,
          columnNumber: 11
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: styles.questionSubtitle, children: "Analise o padrão musical e preveja qual elemento vem a seguir" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2635,
          columnNumber: 11
        }, void 0),
        activityData.round && activityData.totalRounds && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.roundProgress, children: [
          "Rodada ",
          activityData.round,
          " de ",
          activityData.totalRounds
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2637,
          columnNumber: 13
        }, void 0)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2633,
        columnNumber: 9
      }, void 0),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.objectsDisplay, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.patternContainer, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.patternHeader, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.patternIcon, children: "🧩" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2645,
            columnNumber: 15
          }, void 0),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: styles.patternTitle, children: "Padrão Musical" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2646,
            columnNumber: 15
          }, void 0),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: styles.patternDescription, children: "Analise a sequência e descubra qual instrumento vem a seguir" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2647,
            columnNumber: 15
          }, void 0)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2644,
          columnNumber: 13
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.patternSequence, children: [
          (activityData.incompletePattern || []).map((instrumentId, index) => {
            const instrument = instruments.find((inst) => inst.id === instrumentId);
            return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.patternItem, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.patternEmoji, children: instrument?.emoji }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2658,
                columnNumber: 21
              }, void 0),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.patternName, children: instrument?.name }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2659,
                columnNumber: 21
              }, void 0)
            ] }, index, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
              lineNumber: 2657,
              columnNumber: 19
            }, void 0);
          }),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.patternItem, "data-missing": "true", children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.patternEmoji, children: "❓" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
              lineNumber: 2664,
              columnNumber: 17
            }, void 0),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.patternName, children: "?" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
              lineNumber: 2665,
              columnNumber: 17
            }, void 0)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2663,
            columnNumber: 15
          }, void 0)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2653,
          columnNumber: 13
        }, void 0),
        activityData.patternType && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.patternHint, children: [
          "Padrão: ",
          activityData.patternType
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2670,
          columnNumber: 15
        }, void 0)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2643,
        columnNumber: 11
      }, void 0) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2642,
        columnNumber: 9
      }, void 0),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.answerOptions, children: (activityData.options || []).map((instrumentId) => {
        const instrument = instruments.find((inst) => inst.id === instrumentId);
        const isSelected = activityData.userAnswer === instrumentId;
        return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            className: styles.answerButton,
            onClick: () => checkPatternAnswer(instrumentId),
            disabled: isPlaying,
            style: { border: isSelected ? "3px solid rgba(255, 193, 7, 0.8)" : "2px solid rgba(255,255,255,0.3)" },
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "2.5rem", marginBottom: "0.5rem" }, children: instrument?.emoji }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2688,
                columnNumber: 17
              }, void 0),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "1rem", fontWeight: "bold" }, children: instrument?.name }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2689,
                columnNumber: 17
              }, void 0)
            ]
          },
          instrumentId,
          true,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2681,
            columnNumber: 15
          },
          void 0
        );
      }) }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2676,
        columnNumber: 9
      }, void 0),
      activityData.showResult && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: `${styles.feedbackContainer} ${activityData.isCorrect ? styles.correctFeedback : styles.incorrectFeedback}`, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.feedbackIcon, children: activityData.isCorrect ? "✅" : "❌" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2696,
          columnNumber: 13
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.feedbackTitle, children: activityData.isCorrect ? "Correto!" : "Tente novamente" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2699,
          columnNumber: 13
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.feedbackText, children: activityData.feedback || activityData.patternExplanation }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2702,
          columnNumber: 13
        }, void 0)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2695,
        columnNumber: 11
      }, void 0)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
      lineNumber: 2632,
      columnNumber: 7
    }, void 0);
  };
  const handleBackToMenu = reactExports.useCallback(() => {
    try {
      const gameEndData = {
        gameType: "musical_sequence",
        sessionDuration: Date.now() - gameStartTime,
        finalScore: score,
        finalLevel: currentLevel,
        totalAttempts,
        correctAttempts,
        finalAccuracy: totalAttempts > 0 ? correctAttempts / totalAttempts * 100 : 0,
        maxStreak: streak,
        difficulty,
        timestamp: Date.now()
      };
      MusicalSequenceMetrics.recordAdvancedInteraction(gameEndData);
      const collectorsHub22 = MusicalSequenceMetrics.getCollectorsHub();
      if (collectorsHub22) {
        const finalReport = collectorsHub22.generateFinalReport();
        const recommendations = collectorsHub22.generateRecommendations();
        console.log("🎯 Relatório Final:", finalReport);
        console.log("🎯 Recomendações Geradas:", recommendations);
        if (orchestratorReady && sendMetrics) {
          sendMetrics({
            gameType: "musical_sequence",
            sessionId: `musical_${gameStartTime}`,
            metrics: {
              ...gameEndData,
              finalReport,
              recommendations
            },
            timestamp: Date.now()
          });
        }
        collectorsHub22.endSession();
      }
    } catch (error) {
      console.error("Erro ao gerar relatório final:", error);
    }
    if (onBack) {
      onBack();
    }
  }, [gameStartTime, score, currentLevel, totalAttempts, correctAttempts, streak, difficulty, orchestratorReady, sendMetrics, onBack]);
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
    "div",
    {
      className: `${styles.musicalSequenceGame} ${settings.reducedMotion ? "reduced-motion" : ""} ${settings.highContrast ? "high-contrast" : ""}`,
      "data-font-size": settings.fontSize,
      "data-theme": settings.colorScheme,
      style: {
        fontSize: settings.fontSize === "small" ? "0.875rem" : settings.fontSize === "large" ? "1.25rem" : "1rem"
      },
      children: showStartScreen ? /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        GameStartScreen,
        {
          gameTitle: "🎵 Sequência Musical",
          gameDescription: "Teste sua memória auditiva e habilidades musicais com diferentes atividades interativas",
          onStart: startGame,
          onBack
        },
        void 0,
        false,
        {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2901,
          columnNumber: 9
        },
        void 0
      ) : /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.gameContent, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.gameHeader, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h1", { className: styles.gameTitle, children: [
            "🎵 Sequência Musical",
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { style: { fontSize: "0.7rem", opacity: 0.8, marginTop: "0.25rem" }, children: ACTIVITY_TYPES[gameState.currentActivity?.toUpperCase()]?.name || "Reprodução de Sequência" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
              lineNumber: 2913,
              columnNumber: 15
            }, void 0)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2911,
            columnNumber: 13
          }, void 0),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
            "button",
            {
              className: `${styles.headerTtsButton} ${ttsActive2 ? styles.ttsActive : ""}`,
              onClick: toggleTTS,
              title: ttsActive2 ? "Desativar TTS" : "Ativar TTS",
              "aria-label": ttsActive2 ? "Desativar narração" : "Ativar narração",
              children: ttsActive2 ? "🔊" : "🔇"
            },
            void 0,
            false,
            {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
              lineNumber: 2917,
              columnNumber: 13
            },
            void 0
          )
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2910,
          columnNumber: 11
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.gameStats, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statCard, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statValue, children: score }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
              lineNumber: 2930,
              columnNumber: 15
            }, void 0),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statLabel, children: "Pontos" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
              lineNumber: 2931,
              columnNumber: 15
            }, void 0)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2929,
            columnNumber: 13
          }, void 0),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statCard, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statValue, children: currentLevel }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
              lineNumber: 2934,
              columnNumber: 15
            }, void 0),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statLabel, children: "Nível" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
              lineNumber: 2935,
              columnNumber: 15
            }, void 0)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2933,
            columnNumber: 13
          }, void 0),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statCard, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statValue, children: streak }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
              lineNumber: 2938,
              columnNumber: 15
            }, void 0),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statLabel, children: "Sequência" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
              lineNumber: 2939,
              columnNumber: 15
            }, void 0)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2937,
            columnNumber: 13
          }, void 0),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statCard, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statValue, children: [
              getAccuracy(),
              "%"
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
              lineNumber: 2942,
              columnNumber: 15
            }, void 0),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statLabel, children: "Precisão" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
              lineNumber: 2943,
              columnNumber: 15
            }, void 0)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2941,
            columnNumber: 13
          }, void 0)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2928,
          columnNumber: 11
        }, void 0),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.activityMenu, children: Object.values(ACTIVITY_TYPES).map((activity) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            className: `${styles.activityButton} ${gameState.currentActivity === activity.id ? styles.active : ""}`,
            onClick: () => changeActivity(activity.id),
            children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: activity.icon }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2957,
                columnNumber: 17
              }, void 0),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: activity.name }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
                lineNumber: 2958,
                columnNumber: 17
              }, void 0)
            ]
          },
          activity.id,
          true,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2950,
            columnNumber: 15
          },
          void 0
        )) }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2948,
          columnNumber: 11
        }, void 0),
        gameState.currentActivity === ACTIVITY_TYPES.SEQUENCE_REPRODUCTION.id && renderSequenceReproduction(),
        gameState.currentActivity === ACTIVITY_TYPES.RHYTHM_TIMING.id && renderRhythmTiming(),
        gameState.currentActivity === ACTIVITY_TYPES.PITCH_DISCRIMINATION.id && renderPitchDiscrimination(),
        gameState.currentActivity === ACTIVITY_TYPES.PATTERN_PREDICTION.id && renderPatternPrediction(),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.gameControls, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("button", { className: styles.controlButton, onClick: explainGame, children: "🔊 Explicar" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2971,
            columnNumber: 13
          }, void 0),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("button", { className: styles.controlButton, onClick: playSequence, children: "🔄 Repetir Sequência" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2974,
            columnNumber: 13
          }, void 0),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("button", { className: styles.controlButton, onClick: handleBackToMenu, children: "⬅️ Voltar" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
            lineNumber: 2977,
            columnNumber: 13
          }, void 0)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
          lineNumber: 2970,
          columnNumber: 11
        }, void 0)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
        lineNumber: 2908,
        columnNumber: 9
      }, void 0)
    },
    void 0,
    false,
    {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/games/MusicalSequence/MusicalSequenceGame.jsx",
      lineNumber: 2891,
      columnNumber: 5
    },
    void 0
  );
};
const MusicalSequenceGame$1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: MusicalSequenceGame
}, Symbol.toStringTag, { value: "Module" }));
export {
  MusicalSequenceProcessors as M,
  MusicalSequenceCollectorsHub as a,
  MusicalSequenceGame$1 as b
};
//# sourceMappingURL=game-musical-4K52sZ4i.js.map
