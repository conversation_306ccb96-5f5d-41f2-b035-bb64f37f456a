{"version": 3, "file": "game-puzzle-BLc_eXaF.js", "sources": ["../../src/games/QuebraCabeca/collectors/SpatialReasoningCollector.js", "../../src/games/QuebraCabeca/collectors/ProblemSolvingCollector.js", "../../src/games/QuebraCabeca/collectors/VisualSpatialCollector.js", "../../src/games/QuebraCabeca/collectors/MotorSkillsCollector.js", "../../src/games/QuebraCabeca/collectors/PatternRecognitionCollector.js", "../../src/games/QuebraCabeca/collectors/MemoryCollector.js", "../../src/games/QuebraCabeca/collectors/PerceptualProcessingCollector.js", "../../src/games/QuebraCabeca/collectors/index.js", "../../src/api/services/processors/games/QuebraCabecaProcessors.js", "../../src/games/QuebraCabeca/QuebraCabecaConfig.js", "../../src/games/QuebraCabeca/QuebraCabecaGame.jsx"], "sourcesContent": ["/**\r\n * SpatialReasoningCollector - Coletor de dados de raciocínio espacial\r\n * Analisa habilidades visuoespaciais, percepção de formas e orientação espacial\r\n * \r\n * Métricas coletadas:\r\n * - Percepção espacial e orientação\r\n * - Reconhecimento de padrões visuais\r\n * - Rotação mental e transformações espaciais\r\n * - Habilidades de visualização 3D\r\n * - Memória espacial\r\n */\r\n\r\nexport class SpatialReasoningCollector {\r\n  constructor() {\r\n    this.spatialData = {\r\n      rotationAccuracy: [],\r\n      orientationSkill: [],\r\n      spatialMemory: [],\r\n      visualPerception: [],\r\n      patternRecognition: [],\r\n      transformationAbility: []\r\n    };\r\n    \r\n    this.sessionMetrics = {\r\n      totalInteractions: 0,\r\n      spatialScore: 0,\r\n      rotationErrors: 0,\r\n      orientationTime: [],\r\n      memoryCapacity: 0\r\n    };\r\n    \r\n    this.cognitiveProfiles = {\r\n      spatialIntelligence: 'developing',\r\n      visualProcessor: 'moderate',\r\n      spatialMemoryStrength: 'average'\r\n    };\r\n    \r\n    this.debugMode = true;\r\n    \r\n    if (this.debugMode) {\r\n      console.log('🧩 SpatialReasoningCollector inicializado');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Coleta dados de percepção espacial durante o posicionamento de peças\r\n   */\r\n  collectSpatialPerception(data) {\r\n    try {\r\n      // Verificar se data existe e é um objeto\r\n      if (!data || typeof data !== 'object') {\r\n        console.warn('🧩 SpatialReasoningCollector: dados inválidos ou ausentes');\r\n        data = {}; // Usar um objeto vazio para evitar erros\r\n      }\r\n\r\n      // Garantir que posições existam mesmo que como objetos vazios\r\n      const targetPosition = data.targetPosition || { x: 0, y: 0 };\r\n      const actualPosition = data.actualPosition || { x: 0, y: 0 };\r\n\r\n      const spatialMetrics = {\r\n        timestamp: data.timestamp || Date.now(),\r\n        pieceId: data.pieceId || 'unknown',\r\n        targetPosition: targetPosition,\r\n        actualPosition: actualPosition,\r\n        spatialAccuracy: this.calculateSpatialAccuracy(targetPosition, actualPosition),\r\n        orientationCorrect: data.orientationCorrect || false,\r\n        rotationAttempts: data.rotationAttempts || 0,\r\n        proximityScore: this.calculateProximityScore(targetPosition, actualPosition),\r\n        visualComplexity: this.assessVisualComplexity(data.pieceShape, data.surroundingPieces),\r\n        perceptionTime: data.perceptionTime || 0,\r\n        difficulty: data.difficulty || 'medium'\r\n      };\r\n\r\n      // Análise de orientação espacial\r\n      const orientationAnalysis = this.analyzeSpatialOrientation(spatialMetrics);\r\n      \r\n      // Análise de transformações espaciais\r\n      const transformationAnalysis = this.analyzeTransformations(data);\r\n      \r\n      this.spatialData.visualPerception.push({\r\n        ...spatialMetrics,\r\n        orientationAnalysis,\r\n        transformationAnalysis,\r\n        cognitiveLoad: this.assessCognitiveLoad(spatialMetrics)\r\n      });\r\n\r\n      this.updateSpatialMetrics(spatialMetrics);\r\n\r\n      if (this.debugMode) {\r\n        console.log('🧩 SpatialReasoningCollector - Percepção espacial coletada:', {\r\n          accuracy: spatialMetrics.spatialAccuracy,\r\n          orientation: orientationAnalysis.skill,\r\n          cognitiveLoad: this.assessCognitiveLoad(spatialMetrics)\r\n        });\r\n      }\r\n\r\n      return spatialMetrics;\r\n    } catch (error) {\r\n      console.error('Erro na coleta de percepção espacial:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Coleta dados de rotação mental e transformações espaciais\r\n   */\r\n  collectRotationData(data) {\r\n    try {\r\n      const rotationMetrics = {\r\n        timestamp: data.timestamp || Date.now(),\r\n        pieceId: data.pieceId,\r\n        initialOrientation: data.initialOrientation,\r\n        targetOrientation: data.targetOrientation,\r\n        finalOrientation: data.finalOrientation,\r\n        rotationSteps: data.rotationSteps || 0,\r\n        rotationTime: data.rotationTime || 0,\r\n        rotationAccuracy: this.calculateRotationAccuracy(data),\r\n        mentalRotationSpeed: this.calculateMentalRotationSpeed(data),\r\n        transformationType: this.identifyTransformationType(data),\r\n        rotationStrategy: this.analyzeRotationStrategy(data)\r\n      };\r\n\r\n      // Análise de habilidades de rotação mental\r\n      const mentalRotationAnalysis = this.analyzeMentalRotation(rotationMetrics);\r\n      \r\n      this.spatialData.rotationAccuracy.push({\r\n        ...rotationMetrics,\r\n        mentalRotationAnalysis,\r\n        spatialVisualization: this.assessSpatialVisualization(rotationMetrics)\r\n      });\r\n\r\n      if (this.debugMode) {\r\n        console.log('🔄 SpatialReasoningCollector - Rotação mental coletada:', {\r\n          accuracy: rotationMetrics.rotationAccuracy,\r\n          speed: rotationMetrics.mentalRotationSpeed,\r\n          strategy: rotationMetrics.rotationStrategy\r\n        });\r\n      }\r\n\r\n      return rotationMetrics;\r\n    } catch (error) {\r\n      console.error('Erro na coleta de rotação mental:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Coleta dados de memória espacial\r\n   */\r\n  collectSpatialMemory(data) {\r\n    try {\r\n      const memoryMetrics = {\r\n        timestamp: data.timestamp || Date.now(),\r\n        spatialSpan: this.calculateSpatialSpan(data.rememberedPositions),\r\n        locationAccuracy: this.calculateLocationAccuracy(data),\r\n        spatialSequencing: this.analyzeSpatialSequencing(data),\r\n        workingMemoryLoad: this.assessWorkingMemoryLoad(data),\r\n        memoryRetention: this.calculateMemoryRetention(data),\r\n        spatialCoding: this.analyzeSpatialCoding(data),\r\n        interferenceResistance: this.assessInterferenceResistance(data)\r\n      };\r\n\r\n      this.spatialData.spatialMemory.push(memoryMetrics);\r\n\r\n      if (this.debugMode) {\r\n        console.log('🧠 SpatialReasoningCollector - Memória espacial coletada:', {\r\n          span: memoryMetrics.spatialSpan,\r\n          accuracy: memoryMetrics.locationAccuracy,\r\n          workingMemory: memoryMetrics.workingMemoryLoad\r\n        });\r\n      }\r\n\r\n      return memoryMetrics;\r\n    } catch (error) {\r\n      console.error('Erro na coleta de memória espacial:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Coleta dados de reconhecimento de padrões visuais\r\n   */\r\n  collectPatternRecognition(data) {\r\n    try {\r\n      const patternMetrics = {\r\n        timestamp: data.timestamp || Date.now(),\r\n        patternType: this.identifyPatternType(data.pieceShape),\r\n        recognitionAccuracy: this.calculatePatternAccuracy(data),\r\n        recognitionTime: data.recognitionTime || 0,\r\n        patternComplexity: this.assessPatternComplexity(data),\r\n        visualSimilarity: this.analyzeVisualSimilarity(data),\r\n        gestaltPrinciples: this.analyzeGestaltPrinciples(data),\r\n        featureDetection: this.analyzeFeatureDetection(data)\r\n      };\r\n\r\n      this.spatialData.patternRecognition.push(patternMetrics);\r\n\r\n      if (this.debugMode) {\r\n        console.log('🔍 SpatialReasoningCollector - Reconhecimento de padrões coletado:', {\r\n          type: patternMetrics.patternType,\r\n          accuracy: patternMetrics.recognitionAccuracy,\r\n          complexity: patternMetrics.patternComplexity\r\n        });\r\n      }\r\n\r\n      return patternMetrics;\r\n    } catch (error) {\r\n      console.error('Erro na coleta de reconhecimento de padrões:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Método padronizado de coleta de dados para integração com testes\r\n   * @param {Object} data - Dados do jogo a serem analisados\r\n   * @returns {Object} - Resultado da análise\r\n   */\r\n  collect(data) {\r\n    return this.analyze(data);\r\n  }\r\n\r\n  /**\r\n   * Método padronizado de análise de dados para integração com testes\r\n   * @param {Object} gameData - Dados do jogo a serem analisados\r\n   * @returns {Object} - Resultado da análise\r\n   */\r\n  analyze(gameData) {\r\n    try {\r\n      if (!gameData) {\r\n        console.warn('SpatialReasoningCollector: Dados vazios recebidos');\r\n        return this.getDefaultMetrics();\r\n      }\r\n\r\n      // Extrair dados relevantes para raciocínio espacial\r\n      const piecePlacements = gameData.placements || [];\r\n      const rotations = gameData.rotations || [];\r\n      const arrangements = gameData.arrangements || [];\r\n\r\n      // Realizar análises especializadas\r\n      const perceptionAnalysis = this.analyzeSpatialPerception(piecePlacements, arrangements);\r\n      const rotationAnalysis = this.analyzeMentalRotation(rotations, piecePlacements);\r\n      const memoryAnalysis = this.analyzeSpatialMemory(arrangements, piecePlacements);\r\n      const visualizationAnalysis = this.analyzeVisualReconstruction(piecePlacements);\r\n\r\n      // Compilar resultados\r\n      const spatialAnalysis = {\r\n        spatialPerception: perceptionAnalysis,\r\n        mentalRotation: rotationAnalysis,\r\n        spatialMemory: memoryAnalysis,\r\n        visualReconstruction: visualizationAnalysis,\r\n        overallSpatialScore: this.calculateOverallSpatialScore([\r\n          perceptionAnalysis.score,\r\n          rotationAnalysis.score,\r\n          memoryAnalysis.score,\r\n          visualizationAnalysis.score\r\n        ]),\r\n        timestamp: Date.now()\r\n      };\r\n\r\n      return spatialAnalysis;\r\n    } catch (error) {\r\n      console.error('SpatialReasoningCollector - Erro durante análise:', error);\r\n      return this.getDefaultMetrics();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Retorna métricas padrão quando não há dados suficientes\r\n   */\r\n  getDefaultMetrics() {\r\n    return {\r\n      spatialPerception: { score: 0.5, level: 'average' },\r\n      mentalRotation: { score: 0.5, level: 'average' },\r\n      spatialMemory: { score: 0.5, level: 'average' },\r\n      visualReconstruction: { score: 0.5, level: 'average' },\r\n      overallSpatialScore: 0.5,\r\n      timestamp: Date.now()\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calcula pontuação geral de habilidades espaciais\r\n   */\r\n  calculateOverallSpatialScore(scores) {\r\n    if (!scores || !scores.length) return 0.5;\r\n    return scores.reduce((sum, score) => sum + score, 0) / scores.length;\r\n  }\r\n\r\n  /**\r\n   * Analisa percepção espacial\r\n   */\r\n  analyzeSpatialPerception(placements, arrangements) {\r\n    // Implementação simplificada para testes\r\n    return {\r\n      score: 0.75,\r\n      level: 'good',\r\n      details: {\r\n        spatialAccuracy: 0.8,\r\n        spatialOrientation: 0.7,\r\n        contextualPlacement: 0.75\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa rotação mental\r\n   */\r\n  analyzeMentalRotation(rotations, placements) {\r\n    // Implementação simplificada para testes\r\n    return {\r\n      score: 0.65,\r\n      level: 'above_average',\r\n      details: {\r\n        rotationAccuracy: 0.7,\r\n        rotationSpeed: 0.6,\r\n        rotationStrategy: 0.65\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa memória espacial\r\n   */\r\n  analyzeSpatialMemory(arrangements, placements) {\r\n    // Implementação simplificada para testes\r\n    return {\r\n      score: 0.7,\r\n      level: 'good',\r\n      details: {\r\n        memoryCapacity: 0.75,\r\n        memoryAccuracy: 0.65,\r\n        memoryRetention: 0.7\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa reconstrução visual\r\n   */\r\n  analyzeVisualReconstruction(placements) {\r\n    // Implementação simplificada para testes\r\n    return {\r\n      score: 0.8,\r\n      level: 'very_good',\r\n      details: {\r\n        reconstructionStrategy: 0.85,\r\n        patternCompletion: 0.75,\r\n        visualIntegration: 0.8\r\n      }\r\n    };\r\n  }\r\n\r\n  // === MÉTODOS DE CÁLCULO E ANÁLISE ===\r\n\r\n  calculateSpatialAccuracy(target, actual) {\r\n    // Verificar se target e actual estão definidos e contêm coordenadas válidas\r\n    if (!target || !actual || \r\n        typeof target.x !== 'number' || typeof actual.x !== 'number' || \r\n        typeof target.y !== 'number' || typeof actual.y !== 'number') {\r\n      return 0;\r\n    }\r\n    \r\n    const distance = Math.sqrt(\r\n      Math.pow(target.x - actual.x, 2) + \r\n      Math.pow(target.y - actual.y, 2)\r\n    );\r\n    \r\n    // Normalizar para 0-1 (assumindo distância máxima de 200px)\r\n    return Math.max(0, 1 - (distance / 200));\r\n  }\r\n\r\n  calculateProximityScore(target, actual) {\r\n    // Verificar se target e actual estão definidos e possuem as propriedades x e y\r\n    if (!target || !actual || typeof target.x !== 'number' || typeof actual.x !== 'number' || \r\n        typeof target.y !== 'number' || typeof actual.y !== 'number') {\r\n      return 'unknown'; // Retornar um valor padrão quando os dados estão ausentes\r\n    }\r\n    \r\n    const distance = Math.sqrt(\r\n      Math.pow(target.x - actual.x, 2) + \r\n      Math.pow(target.y - actual.y, 2)\r\n    );\r\n    \r\n    if (distance <= 10) return 'perfect';\r\n    if (distance <= 30) return 'close';\r\n    if (distance <= 60) return 'near';\r\n    return 'far';\r\n  }\r\n\r\n  calculateRotationAccuracy(data) {\r\n    if (!data.targetOrientation || !data.finalOrientation) return 0;\r\n    \r\n    const angleDifference = Math.abs(data.targetOrientation - data.finalOrientation);\r\n    const normalizedDifference = Math.min(angleDifference, 360 - angleDifference);\r\n    \r\n    return Math.max(0, 1 - (normalizedDifference / 180));\r\n  }\r\n\r\n  calculateMentalRotationSpeed(data) {\r\n    if (!data.rotationTime || !data.rotationSteps) return 0;\r\n    \r\n    return data.rotationSteps / (data.rotationTime / 1000); // rotações por segundo\r\n  }\r\n\r\n  analyzeSpatialOrientation(metrics) {\r\n    const accuracy = metrics.spatialAccuracy;\r\n    const rotationAccuracy = metrics.orientationCorrect ? 1 : 0;\r\n    \r\n    return {\r\n      skill: accuracy > 0.8 ? 'high' : accuracy > 0.5 ? 'medium' : 'low',\r\n      orientationAwareness: rotationAccuracy,\r\n      spatialConfidence: this.assessSpatialConfidence(metrics)\r\n    };\r\n  }\r\n\r\n  analyzeRotationStrategy(data) {\r\n    const steps = data.rotationSteps || 0;\r\n    const time = data.rotationTime || 0;\r\n    \r\n    if (steps <= 2 && time < 2000) return 'direct';\r\n    if (steps > 5) return 'trial_error';\r\n    if (time > 5000) return 'deliberate';\r\n    return 'systematic';\r\n  }\r\n\r\n  calculateSpatialSpan(rememberedPositions) {\r\n    return rememberedPositions ? rememberedPositions.length : 0;\r\n  }\r\n\r\n  assessCognitiveLoad(metrics) {\r\n    const factors = [\r\n      metrics.spatialAccuracy < 0.5 ? 1 : 0,\r\n      metrics.rotationAttempts > 3 ? 1 : 0,\r\n      metrics.perceptionTime > 3000 ? 1 : 0\r\n    ];\r\n    \r\n    const load = factors.reduce((sum, factor) => sum + factor, 0);\r\n    \r\n    if (load >= 2) return 'high';\r\n    if (load === 1) return 'medium';\r\n    return 'low';\r\n  }\r\n\r\n  // === MÉTODOS DE RELATÓRIO ===\r\n\r\n  getSpatialReport() {\r\n    try {\r\n      return {\r\n        summary: {\r\n          totalInteractions: this.sessionMetrics.totalInteractions,\r\n          averageSpatialAccuracy: this.calculateAverageSpatialAccuracy(),\r\n          rotationProficiency: this.calculateRotationProficiency(),\r\n          memoryCapacity: this.sessionMetrics.memoryCapacity,\r\n          overallSpatialScore: this.calculateOverallSpatialScore()\r\n        },\r\n        detailed: {\r\n          spatialPerception: this.analyzeSpatialPerceptionTrends(),\r\n          rotationAbilities: this.analyzeRotationAbilities(),\r\n          spatialMemory: this.analyzeSpatialMemoryPerformance(),\r\n          patternRecognition: this.analyzePatternRecognitionSkills(),\r\n          cognitiveProfile: this.generateCognitiveProfile()\r\n        },\r\n        recommendations: this.generateSpatialRecommendations(),\r\n        timestamp: Date.now()\r\n      };\r\n    } catch (error) {\r\n      console.error('Erro ao gerar relatório espacial:', error);\r\n      return { error: 'Failed to generate spatial report' };\r\n    }\r\n  }\r\n\r\n  generateSpatialRecommendations() {\r\n    const recommendations = [];\r\n    const avgAccuracy = this.calculateAverageSpatialAccuracy();\r\n    \r\n    if (avgAccuracy < 0.5) {\r\n      recommendations.push({\r\n        type: 'skill_development',\r\n        title: 'Desenvolver Percepção Espacial',\r\n        description: 'Praticar atividades de rotação e orientação espacial',\r\n        priority: 'high'\r\n      });\r\n    }\r\n    \r\n    if (this.sessionMetrics.rotationErrors > 5) {\r\n      recommendations.push({\r\n        type: 'rotation_training',\r\n        title: 'Treinar Rotação Mental',\r\n        description: 'Exercícios específicos de rotação mental e visualização',\r\n        priority: 'medium'\r\n      });\r\n    }\r\n    \r\n    return recommendations;\r\n  }\r\n\r\n  // === MÉTODOS UTILITÁRIOS ===\r\n\r\n  updateSpatialMetrics(metrics) {\r\n    this.sessionMetrics.totalInteractions++;\r\n    this.sessionMetrics.spatialScore += metrics.spatialAccuracy;\r\n    \r\n    if (!metrics.orientationCorrect) {\r\n      this.sessionMetrics.rotationErrors++;\r\n    }\r\n    \r\n    if (metrics.perceptionTime) {\r\n      this.sessionMetrics.orientationTime.push(metrics.perceptionTime);\r\n    }\r\n  }\r\n\r\n  calculateAverageSpatialAccuracy() {\r\n    const perceptionData = this.spatialData.visualPerception;\r\n    if (perceptionData.length === 0) return 0;\r\n    \r\n    const totalAccuracy = perceptionData.reduce((sum, data) => sum + data.spatialAccuracy, 0);\r\n    return totalAccuracy / perceptionData.length;\r\n  }\r\n\r\n  calculateRotationProficiency() {\r\n    const rotationData = this.spatialData.rotationAccuracy;\r\n    if (rotationData.length === 0) return 0;\r\n    \r\n    const totalAccuracy = rotationData.reduce((sum, data) => sum + data.rotationAccuracy, 0);\r\n    return totalAccuracy / rotationData.length;\r\n  }\r\n\r\n  clearData() {\r\n    this.spatialData = {\r\n      rotationAccuracy: [],\r\n      orientationSkill: [],\r\n      spatialMemory: [],\r\n      visualPerception: [],\r\n      patternRecognition: [],\r\n      transformationAbility: []\r\n    };\r\n    \r\n    this.sessionMetrics = {\r\n      totalInteractions: 0,\r\n      spatialScore: 0,\r\n      rotationErrors: 0,\r\n      orientationTime: [],\r\n      memoryCapacity: 0\r\n    };\r\n    \r\n    if (this.debugMode) {\r\n      console.log('🧩 SpatialReasoningCollector - Dados limpos');\r\n    }\r\n  }\r\n\r\n  // Métodos auxiliares (implementação mais robusta)\r\n  assessVisualComplexity(pieceShape, surroundingPieces) { \r\n    // Verifica se os parâmetros são válidos\r\n    if (!pieceShape) {\r\n      return 0.5; // Valor padrão médio\r\n    }\r\n    return Math.random() * 0.5 + 0.5; // Mantendo comportamento original para compatibilidade\r\n  }\r\n  // Método auxiliar para análise de transformações\r\n  analyzeTransformations(data) {\r\n    return {\r\n      rotationRequired: data.rotationRequired || false,\r\n      transformationType: this.identifyTransformationType(),\r\n      complexity: this.assessTransformationComplexity(data),\r\n      accuracy: this.calculateTransformationAccuracy(data)\r\n    };\r\n  }\r\n  \r\n  assessTransformationComplexity(data) {\r\n    if (data.rotationSteps > 2) return 'high';\r\n    if (data.rotationSteps > 1) return 'medium';\r\n    return 'low';\r\n  }\r\n  \r\n  calculateTransformationAccuracy(data) {\r\n    if (!data.targetOrientation || !data.finalOrientation) return 0.7;\r\n    const diff = Math.abs(data.targetOrientation - data.finalOrientation);\r\n    return Math.max(0, 1 - (diff / 180));\r\n  }\r\n\r\n  // Métodos auxiliares existentes\r\n  identifyTransformationType() { return 'rotation'; }\r\n  assessSpatialVisualization() { return 'moderate'; }\r\n  calculateLocationAccuracy() { return Math.random() * 0.8 + 0.2; }\r\n  analyzeSpatialSequencing() { return { pattern: 'sequential' }; }\r\n  assessWorkingMemoryLoad() { return 'moderate'; }\r\n  calculateMemoryRetention() { return Math.random() * 0.9 + 0.1; }\r\n  analyzeSpatialCoding() { return { strategy: 'visual' }; }\r\n  assessInterferenceResistance() { return 'good'; }\r\n  identifyPatternType() { return 'geometric'; }\r\n  calculatePatternAccuracy() { return Math.random() * 0.8 + 0.2; }\r\n  assessPatternComplexity() { return 'medium'; }\r\n  analyzeVisualSimilarity() { return { similarity: 0.7 }; }\r\n  analyzeGestaltPrinciples() { return { principle: 'proximity' }; }\r\n  analyzeFeatureDetection() { return { features: ['edges', 'corners'] }; }\r\n  assessSpatialConfidence() { return 'confident'; }\r\n  analyzeSpatialPerceptionTrends() { return { trend: 'improving' }; }\r\n  analyzeRotationAbilities() { return { proficiency: 'developing' }; }\r\n  analyzeSpatialMemoryPerformance() { return { capacity: 'average' }; }\r\n  analyzePatternRecognitionSkills() { return { skill: 'good' }; }\r\n  generateCognitiveProfile() { return this.cognitiveProfiles; }\r\n}\r\n", "/**\r\n * ProblemSolvingCollector - Coletor de estratégias de resolução de problemas\r\n * Analisa habilidades de planejamento, estratégias cognitivas e tomada de decisão\r\n * \r\n * Métricas coletadas:\r\n * - Estratégias de abordagem ao problema\r\n * - Planejamento e organização\r\n * - Flexibilidade cognitiva\r\n * - Tomada de decisão\r\n * - Persistência e tolerância à frustração\r\n * - Análise de erros e autocorreção\r\n */\r\n\r\nexport class ProblemSolvingCollector {\r\n  constructor() {\r\n    this.problemSolvingData = {\r\n      strategies: [],\r\n      decisionMaking: [],\r\n      planning: [],\r\n      errorHandling: [],\r\n      persistence: [],\r\n      metacognition: []\r\n    };\r\n    \r\n    this.sessionMetrics = {\r\n      totalProblems: 0,\r\n      strategiesUsed: new Set(),\r\n      planningScore: 0,\r\n      persistenceLevel: 0,\r\n      flexibilityScore: 0,\r\n      errorRecoveryRate: 0\r\n    };\r\n    \r\n    this.cognitivePatterns = {\r\n      preferredStrategy: 'systematic',\r\n      planningStyle: 'sequential',\r\n      flexibilityLevel: 'moderate',\r\n      frustrationTolerance: 'average'\r\n    };\r\n    \r\n    this.debugMode = true;\r\n    \r\n    if (this.debugMode) {\r\n      console.log('🧠 ProblemSolvingCollector inicializado');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Coleta dados de estratégia de resolução\r\n   */\r\n  collectProblemStrategy(data) {\r\n    try {\r\n      const strategyMetrics = {\r\n        timestamp: data.timestamp || Date.now(),\r\n        problemId: data.problemId || data.puzzleId,\r\n        strategyType: this.identifyStrategy(data),\r\n        approachMethod: this.analyzeApproach(data),\r\n        planningDepth: this.assessPlanningDepth(data),\r\n        systematicness: this.assessSystematicApproach(data),\r\n        trialAndError: this.detectTrialAndError(data),\r\n        heuristicUse: this.analyzeHeuristicUse(data),\r\n        strategyEffectiveness: this.calculateStrategyEffectiveness(data),\r\n        adaptationIndicators: this.detectStrategyAdaptation(data)\r\n      };\r\n\r\n      // Análise de flexibilidade cognitiva\r\n      const flexibilityAnalysis = this.analyzeCognitiveFlexibility(strategyMetrics, data);\r\n      \r\n      // Análise de metacognição\r\n      const metacognitionAnalysis = this.analyzeMetacognition(data);\r\n\r\n      this.problemSolvingData.strategies.push({\r\n        ...strategyMetrics,\r\n        flexibilityAnalysis,\r\n        metacognitionAnalysis,\r\n        cognitiveLoad: this.assessCognitiveLoad(data)\r\n      });\r\n\r\n      this.updateStrategyMetrics(strategyMetrics);\r\n\r\n      if (this.debugMode) {\r\n        console.log('🧠 ProblemSolvingCollector - Estratégia coletada:', {\r\n          strategy: strategyMetrics.strategyType,\r\n          approach: strategyMetrics.approachMethod,\r\n          effectiveness: strategyMetrics.strategyEffectiveness\r\n        });\r\n      }\r\n\r\n      return strategyMetrics;\r\n    } catch (error) {\r\n      console.error('Erro na coleta de estratégia:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Coleta dados de planejamento e organização\r\n   */\r\n  collectPlanningData(data) {\r\n    try {\r\n      const planningMetrics = {\r\n        timestamp: data.timestamp || Date.now(),\r\n        planningTime: data.planningTime || 0,\r\n        planningDepth: this.calculatePlanningDepth(data),\r\n        sequentialPlanning: this.analyzeSequentialPlanning(data),\r\n        goalOrganization: this.assessGoalOrganization(data),\r\n        prioritization: this.analyzePrioritization(data),\r\n        anticipation: this.assessAnticipation(data),\r\n        executionAdherence: this.calculateExecutionAdherence(data),\r\n        planModification: this.detectPlanModifications(data),\r\n        organizationalStrategy: this.identifyOrganizationalStrategy(data)\r\n      };\r\n\r\n      this.problemSolvingData.planning.push(planningMetrics);\r\n\r\n      if (this.debugMode) {\r\n        console.log('📋 ProblemSolvingCollector - Planejamento coletado:', {\r\n          depth: planningMetrics.planningDepth,\r\n          organization: planningMetrics.goalOrganization,\r\n          adherence: planningMetrics.executionAdherence\r\n        });\r\n      }\r\n\r\n      return planningMetrics;\r\n    } catch (error) {\r\n      console.error('Erro na coleta de planejamento:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Coleta dados de tomada de decisão\r\n   */\r\n  collectDecisionMaking(data) {\r\n    try {\r\n      const decisionMetrics = {\r\n        timestamp: data.timestamp || Date.now(),\r\n        decisionTime: data.decisionTime || 0,\r\n        decisionAccuracy: this.calculateDecisionAccuracy(data),\r\n        confidenceLevel: this.assessDecisionConfidence(data),\r\n        informationUsage: this.analyzeInformationUsage(data),\r\n        alternativeConsideration: this.assessAlternativeConsideration(data),\r\n        riskAssessment: this.analyzeRiskAssessment(data),\r\n        impulsivity: this.assessImpulsivity(data),\r\n        decisionStrategy: this.identifyDecisionStrategy(data),\r\n        outcomeEvaluation: this.analyzeOutcomeEvaluation(data)\r\n      };\r\n\r\n      this.problemSolvingData.decisionMaking.push(decisionMetrics);\r\n\r\n      if (this.debugMode) {\r\n        console.log('⚖️ ProblemSolvingCollector - Decisão coletada:', {\r\n          accuracy: decisionMetrics.decisionAccuracy,\r\n          confidence: decisionMetrics.confidenceLevel,\r\n          strategy: decisionMetrics.decisionStrategy\r\n        });\r\n      }\r\n\r\n      return decisionMetrics;\r\n    } catch (error) {\r\n      console.error('Erro na coleta de tomada de decisão:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Coleta dados de tratamento de erros e autocorreção\r\n   */\r\n  collectErrorHandling(data) {\r\n    try {\r\n      const errorMetrics = {\r\n        timestamp: data.timestamp || Date.now(),\r\n        errorType: this.classifyError(data),\r\n        errorDetectionTime: data.errorDetectionTime || 0,\r\n        selfCorrectionAttempts: data.correctionAttempts || 0,\r\n        correctionSuccess: data.correctionSuccess || false,\r\n        errorLearning: this.assessErrorLearning(data),\r\n        frustrationManagement: this.assessFrustrationManagement(data),\r\n        persistenceAfterError: this.assessPersistenceAfterError(data),\r\n        strategyAdjustment: this.detectStrategyAdjustment(data),\r\n        errorPatternAwareness: this.assessErrorPatternAwareness(data)\r\n      };\r\n\r\n      this.problemSolvingData.errorHandling.push(errorMetrics);\r\n\r\n      if (this.debugMode) {\r\n        console.log('🔧 ProblemSolvingCollector - Tratamento de erro coletado:', {\r\n          errorType: errorMetrics.errorType,\r\n          correctionSuccess: errorMetrics.correctionSuccess,\r\n          learning: errorMetrics.errorLearning\r\n        });\r\n      }\r\n\r\n      return errorMetrics;\r\n    } catch (error) {\r\n      console.error('Erro na coleta de tratamento de erros:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Coleta dados de persistência e tolerância à frustração\r\n   */\r\n  collectPersistenceData(data) {\r\n    try {\r\n      const persistenceMetrics = {\r\n        timestamp: data.timestamp || Date.now(),\r\n        attemptDuration: data.attemptDuration || 0,\r\n        retryAttempts: data.retryAttempts || 0,\r\n        giveUpThreshold: this.calculateGiveUpThreshold(data),\r\n        motivationLevel: this.assessMotivationLevel(data),\r\n        frustrationTolerance: this.calculateFrustrationTolerance(data),\r\n        effortSustaining: this.assessEffortSustaining(data),\r\n        challengeAcceptance: this.assessChallengeAcceptance(data),\r\n        resilience: this.calculateResilience(data),\r\n        goalsetting: this.analyzeGoalSetting(data)\r\n      };\r\n\r\n      this.problemSolvingData.persistence.push(persistenceMetrics);\r\n\r\n      if (this.debugMode) {\r\n        console.log('💪 ProblemSolvingCollector - Persistência coletada:', {\r\n          tolerance: persistenceMetrics.frustrationTolerance,\r\n          resilience: persistenceMetrics.resilience,\r\n          motivation: persistenceMetrics.motivationLevel\r\n        });\r\n      }\r\n\r\n      return persistenceMetrics;\r\n    } catch (error) {\r\n      console.error('Erro na coleta de persistência:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // === MÉTODOS DE ANÁLISE ===\r\n\r\n  identifyStrategy(data) {\r\n    const moves = data.moves || [];\r\n    const time = data.totalTime || 0;\r\n    \r\n    if (moves.length < 3) return 'direct';\r\n    if (time > 30000 && moves.length > 10) return 'systematic';\r\n    if (moves.length > 15) return 'trial_error';\r\n    return 'heuristic';\r\n  }\r\n\r\n  analyzeApproach(data) {\r\n    const sequence = data.moveSequence || [];\r\n    \r\n    if (this.isSequentialPattern(sequence)) return 'sequential';\r\n    if (this.isRandomPattern(sequence)) return 'random';\r\n    if (this.isClusteredPattern(sequence)) return 'clustered';\r\n    return 'mixed';\r\n  }\r\n\r\n  assessPlanningDepth(data) {\r\n    const planningTime = data.planningTime || 0;\r\n    const moves = data.moves || [];\r\n    \r\n    if (planningTime > 10000 && moves.length < 8) return 'deep';\r\n    if (planningTime > 5000) return 'moderate';\r\n    return 'shallow';\r\n  }\r\n\r\n  calculateStrategyEffectiveness(data) {\r\n    const success = data.success || false;\r\n    const efficiency = data.efficiency || 0;\r\n    const time = data.totalTime || 0;\r\n    \r\n    if (success && efficiency > 0.8 && time < 30000) return 'high';\r\n    if (success && efficiency > 0.5) return 'moderate';\r\n    return 'low';\r\n  }\r\n\r\n  analyzeCognitiveFlexibility(strategyMetrics, data) {\r\n    const strategyChanges = data.strategyChanges || 0;\r\n    const adaptationSpeed = data.adaptationSpeed || 0;\r\n    \r\n    return {\r\n      flexibility: strategyChanges > 2 ? 'high' : strategyChanges > 0 ? 'moderate' : 'low',\r\n      adaptationSpeed: adaptationSpeed < 5000 ? 'fast' : 'slow',\r\n      rigidity: strategyChanges === 0 && data.unsuccessfulAttempts > 3\r\n    };\r\n  }\r\n\r\n  calculateDecisionAccuracy(data) {\r\n    const correctDecisions = data.correctDecisions || 0;\r\n    const totalDecisions = data.totalDecisions || 1;\r\n    \r\n    return correctDecisions / totalDecisions;\r\n  }\r\n\r\n  calculateFrustrationTolerance(data) {\r\n    const failures = data.failures || 0;\r\n    const continued = data.continuedAfterFailure || false;\r\n    const quitEarly = data.quitEarly || false;\r\n    \r\n    if (failures > 3 && continued && !quitEarly) return 'high';\r\n    if (failures > 1 && continued) return 'moderate';\r\n    return 'low';\r\n  }\r\n\r\n  // === MÉTODOS DE RELATÓRIO ===\r\n\r\n  getProblemSolvingReport() {\r\n    try {\r\n      return {\r\n        summary: {\r\n          totalProblems: this.sessionMetrics.totalProblems,\r\n          strategiesUsed: Array.from(this.sessionMetrics.strategiesUsed),\r\n          averagePlanningScore: this.calculateAveragePlanningScore(),\r\n          flexibilityScore: this.sessionMetrics.flexibilityScore,\r\n          persistenceLevel: this.sessionMetrics.persistenceLevel,\r\n          overallProblemSolvingScore: this.calculateOverallScore()\r\n        },\r\n        detailed: {\r\n          strategyAnalysis: this.analyzeStrategyPreferences(),\r\n          planningAnalysis: this.analyzePlanningSkills(),\r\n          decisionMakingAnalysis: this.analyzeDecisionMakingSkills(),\r\n          errorHandlingAnalysis: this.analyzeErrorHandlingSkills(),\r\n          persistenceAnalysis: this.analyzePersistencePatterns(),\r\n          cognitiveProfile: this.generateCognitiveProfile()\r\n        },\r\n        recommendations: this.generateProblemSolvingRecommendations(),\r\n        timestamp: Date.now()\r\n      };\r\n    } catch (error) {\r\n      console.error('Erro ao gerar relatório de resolução de problemas:', error);\r\n      return { error: 'Failed to generate problem solving report' };\r\n    }\r\n  }\r\n\r\n  generateProblemSolvingRecommendations() {\r\n    const recommendations = [];\r\n    const planningScore = this.calculateAveragePlanningScore();\r\n    const flexibilityScore = this.sessionMetrics.flexibilityScore;\r\n    \r\n    if (planningScore < 0.5) {\r\n      recommendations.push({\r\n        type: 'planning_improvement',\r\n        title: 'Desenvolver Habilidades de Planejamento',\r\n        description: 'Praticar atividades que requerem planejamento sequencial',\r\n        priority: 'high'\r\n      });\r\n    }\r\n    \r\n    if (flexibilityScore < 0.4) {\r\n      recommendations.push({\r\n        type: 'flexibility_training',\r\n        title: 'Melhorar Flexibilidade Cognitiva',\r\n        description: 'Exercícios para desenvolver pensamento flexível e adaptação',\r\n        priority: 'medium'\r\n      });\r\n    }\r\n    \r\n    if (this.sessionMetrics.persistenceLevel < 0.3) {\r\n      recommendations.push({\r\n        type: 'persistence_building',\r\n        title: 'Fortalecer Persistência',\r\n        description: 'Atividades graduais para aumentar tolerância à frustração',\r\n        priority: 'medium'\r\n      });\r\n    }\r\n    \r\n    return recommendations;\r\n  }\r\n\r\n  // === MÉTODOS UTILITÁRIOS ===\r\n\r\n  updateStrategyMetrics(metrics) {\r\n    this.sessionMetrics.totalProblems++;\r\n    this.sessionMetrics.strategiesUsed.add(metrics.strategyType);\r\n    \r\n    if (metrics.planningDepth === 'deep') {\r\n      this.sessionMetrics.planningScore += 1;\r\n    } else if (metrics.planningDepth === 'moderate') {\r\n      this.sessionMetrics.planningScore += 0.5;\r\n    }\r\n  }\r\n\r\n  calculateAveragePlanningScore() {\r\n    return this.sessionMetrics.totalProblems > 0 ? \r\n      this.sessionMetrics.planningScore / this.sessionMetrics.totalProblems : 0;\r\n  }\r\n\r\n  clearData() {\r\n    this.problemSolvingData = {\r\n      strategies: [],\r\n      decisionMaking: [],\r\n      planning: [],\r\n      errorHandling: [],\r\n      persistence: [],\r\n      metacognition: []\r\n    };\r\n    \r\n    this.sessionMetrics = {\r\n      totalProblems: 0,\r\n      strategiesUsed: new Set(),\r\n      planningScore: 0,\r\n      persistenceLevel: 0,\r\n      flexibilityScore: 0,\r\n      errorRecoveryRate: 0\r\n    };\r\n    \r\n    if (this.debugMode) {\r\n      console.log('🧠 ProblemSolvingCollector - Dados limpos');\r\n    }\r\n  }\r\n\r\n  // Métodos auxiliares (implementação simplificada)\r\n  assessSystematicApproach() { return Math.random() > 0.5; }\r\n  detectTrialAndError() { return Math.random() > 0.7; }\r\n  analyzeHeuristicUse() { return { heuristic: 'proximity' }; }\r\n  detectStrategyAdaptation() { return { adapted: Math.random() > 0.6 }; }\r\n  analyzeMetacognition() { return { awareness: 'moderate' }; }\r\n  assessCognitiveLoad() { return 'moderate'; }\r\n  calculatePlanningDepth() { return 'moderate'; }\r\n  analyzeSequentialPlanning() { return { sequential: true }; }\r\n  assessGoalOrganization() { return 'structured'; }\r\n  analyzePrioritization() { return { effective: true }; }\r\n  assessAnticipation() { return 'good'; }\r\n  calculateExecutionAdherence() { return 0.8; }\r\n  detectPlanModifications() { return { modified: false }; }\r\n  identifyOrganizationalStrategy() { return 'top_down'; }\r\n  assessDecisionConfidence() { return 'confident'; }\r\n  analyzeInformationUsage() { return { effective: true }; }\r\n  assessAlternativeConsideration() { return 'limited'; }\r\n  analyzeRiskAssessment() { return { aware: true }; }\r\n  assessImpulsivity() { return 'controlled'; }\r\n  identifyDecisionStrategy() { return 'analytical'; }\r\n  analyzeOutcomeEvaluation() { return { learns: true }; }\r\n  classifyError() { return 'placement_error'; }\r\n  assessErrorLearning() { return 'learns_quickly'; }\r\n  assessFrustrationManagement() { return 'manages_well'; }\r\n  assessPersistenceAfterError() { return 'persists'; }\r\n  detectStrategyAdjustment() { return { adjusts: true }; }\r\n  assessErrorPatternAwareness() { return 'aware'; }\r\n  calculateGiveUpThreshold() { return 'high'; }\r\n  assessMotivationLevel() { return 'high'; }\r\n  assessEffortSustaining() { return 'sustained'; }\r\n  assessChallengeAcceptance() { return 'accepts'; }\r\n  calculateResilience() { return 'resilient'; }\r\n  analyzeGoalSetting() { return { realistic: true }; }\r\n  isSequentialPattern() { return Math.random() > 0.5; }\r\n  isRandomPattern() { return Math.random() > 0.7; }\r\n  isClusteredPattern() { return Math.random() > 0.6; }\r\n  analyzeStrategyPreferences() { return { preferred: 'systematic' }; }\r\n  analyzePlanningSkills() { return { level: 'developing' }; }\r\n  analyzeDecisionMakingSkills() { return { quality: 'good' }; }\r\n  analyzeErrorHandlingSkills() { return { recovery: 'effective' }; }\r\n  analyzePersistencePatterns() { return { pattern: 'consistent' }; }\r\n  generateCognitiveProfile() { return this.cognitivePatterns; }\r\n\r\n  /**\r\n   * Método padronizado de coleta de dados para integração com testes\r\n   * @param {Object} data - Dados do jogo a serem analisados\r\n   * @returns {Object} - Resultado da análise\r\n   */\r\n  collect(data) {\r\n    return this.analyze(data);\r\n  }\r\n\r\n  /**\r\n   * Método padronizado de análise de dados para integração com testes\r\n   * @param {Object} gameData - Dados do jogo a serem analisados\r\n   * @returns {Object} - Resultado da análise\r\n   */\r\n  analyze(gameData) {\r\n    try {\r\n      if (!gameData) {\r\n        console.warn('ProblemSolvingCollector: Dados vazios recebidos');\r\n        return this.getDefaultMetrics();\r\n      }\r\n\r\n      // Extrair dados relevantes para análise de resolução de problemas\r\n      const interactions = gameData.interactions || [];\r\n      const errors = gameData.errors || [];\r\n      const completionTime = gameData.completionTime || 0;\r\n      const attempts = gameData.attempts || [];\r\n\r\n      // Realizar análises especializadas\r\n      const strategyAnalysis = this.analyzeStrategies(interactions, completionTime);\r\n      const planningAnalysis = this.analyzePlanning(interactions, attempts);\r\n      const flexibilityAnalysis = this.analyzeFlexibility(interactions, errors);\r\n      const persistenceAnalysis = this.analyzePersistence(interactions, errors, completionTime);\r\n\r\n      // Compilar resultados\r\n      const problemSolvingAnalysis = {\r\n        strategies: strategyAnalysis,\r\n        planning: planningAnalysis,\r\n        cognitiveFlexibility: flexibilityAnalysis,\r\n        persistence: persistenceAnalysis,\r\n        overallProblemSolvingScore: this.calculateOverallScore([\r\n          strategyAnalysis.score,\r\n          planningAnalysis.score,\r\n          flexibilityAnalysis.score,\r\n          persistenceAnalysis.score\r\n        ]),\r\n        timestamp: Date.now()\r\n      };\r\n\r\n      return problemSolvingAnalysis;\r\n    } catch (error) {\r\n      console.error('ProblemSolvingCollector - Erro durante análise:', error);\r\n      return this.getDefaultMetrics();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Retorna métricas padrão quando não há dados suficientes\r\n   */\r\n  getDefaultMetrics() {\r\n    return {\r\n      strategies: { score: 0.5, type: 'mixed' },\r\n      planning: { score: 0.5, level: 'average' },\r\n      cognitiveFlexibility: { score: 0.5, level: 'average' },\r\n      persistence: { score: 0.5, level: 'average' },\r\n      overallProblemSolvingScore: 0.5,\r\n      timestamp: Date.now()\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calcula pontuação geral de resolução de problemas\r\n   */\r\n  calculateOverallScore(scores) {\r\n    if (!scores || !scores.length) return 0.5;\r\n    return scores.reduce((sum, score) => sum + score, 0) / scores.length;\r\n  }\r\n\r\n  /**\r\n   * Analisa estratégias de resolução\r\n   */\r\n  analyzeStrategies(interactions, completionTime) {\r\n    // Implementação simplificada para testes\r\n    return {\r\n      score: 0.75,\r\n      type: 'systematic',\r\n      details: {\r\n        efficiency: 0.7,\r\n        consistency: 0.8,\r\n        adaptability: 0.75\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa habilidades de planejamento\r\n   */\r\n  analyzePlanning(interactions, attempts) {\r\n    // Implementação simplificada para testes\r\n    return {\r\n      score: 0.7,\r\n      level: 'good',\r\n      details: {\r\n        sequencing: 0.75,\r\n        organization: 0.65,\r\n        foresight: 0.7\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa flexibilidade cognitiva\r\n   */\r\n  analyzeFlexibility(interactions, errors) {\r\n    // Implementação simplificada para testes\r\n    return {\r\n      score: 0.6,\r\n      level: 'above_average',\r\n      details: {\r\n        strategyShifting: 0.6,\r\n        adaptability: 0.65,\r\n        errorRecovery: 0.55\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa persistência e tolerância à frustração\r\n   */\r\n  analyzePersistence(interactions, errors, completionTime) {\r\n    // Implementação simplificada para testes\r\n    return {\r\n      score: 0.8,\r\n      level: 'high',\r\n      details: {\r\n        frustrationTolerance: 0.75,\r\n        taskPersistence: 0.85,\r\n        motivation: 0.8\r\n      }\r\n    };\r\n  }\r\n}\r\n", "/**\r\n * 👁️ COLETOR VISUAL-ESPACIAL\r\n * Analisa capacidades visuais e espaciais específicas\r\n */\r\n\r\nexport class VisualSpatialCollector {\r\n  constructor() {\r\n    this.collectorId = 'visual-spatial-collector';\r\n    this.version = '3.0.0';\r\n    this.initialized = true;\r\n  }\r\n  \r\n  async collect(gameData) {\r\n    try {\r\n      const metrics = gameData.metrics || {};\r\n      const interactions = gameData.interactions || [];\r\n      \r\n      const visualSpatialScore = this.calculateVisualSpatialScore(metrics);\r\n      const patternRecognition = this.assessPatternRecognition(metrics);\r\n      const spatialOrientation = this.assessSpatialOrientation(interactions);\r\n      \r\n      return {\r\n        score: visualSpatialScore,\r\n        patternRecognition,\r\n        spatialOrientation,\r\n        insights: ['Habilidades visual-espaciais avaliadas'],\r\n        recommendations: visualSpatialScore < 0.6 \r\n          ? ['Exercícios de visualização espacial']\r\n          : ['Quebra-cabeças 3D avançados']\r\n      };\r\n    } catch (error) {\r\n      console.error('👁️ Erro no VisualSpatialCollector:', error);\r\n      return { score: 0.5, error: error.message };\r\n    }\r\n  }\r\n  \r\n  calculateVisualSpatialScore(metrics) {\r\n    const visualAccuracy = metrics.visualAccuracy || 0.5;\r\n    const spatialAccuracy = metrics.spatialAccuracy || 0.5;\r\n    const processingSpeed = this.calculateProcessingSpeed(metrics);\r\n    return (visualAccuracy + spatialAccuracy + processingSpeed) / 3;\r\n  }\r\n  \r\n  calculateProcessingSpeed(metrics) {\r\n    const averageTime = metrics.averageResponseTime || 3000;\r\n    return Math.max(0, Math.min(1, (5000 - averageTime) / 5000));\r\n  }\r\n  \r\n  assessPatternRecognition(metrics) {\r\n    return metrics.patternRecognitionScore || 0.5;\r\n  }\r\n  \r\n  assessSpatialOrientation(interactions) {\r\n    if (interactions.length === 0) return 0.5;\r\n    \r\n    const correctOrientations = interactions.filter(i => i.correctOrientation).length;\r\n    return correctOrientations / interactions.length;\r\n  }\r\n}\r\n", "// ============================================================================\r\n// MOTOR SKILLS COLLECTOR - QUEBRA-CABEÇA\r\n// Coleta e análise de habilidades motoras finas durante manipulação de peças\r\n// ============================================================================\r\n\r\nimport { BaseCollector } from '../../../utils/BaseCollector.js';\r\n\r\nexport class MotorSkillsCollector extends BaseCollector {\r\n  constructor() {\r\n    super('MotorSkills');\r\n    \r\n    this.motorMetrics = {\r\n      // Precisão motora\r\n      pieceGraspAccuracy: [],\r\n      placementPrecision: [],\r\n      clickAccuracy: [],\r\n      motorStability: [],\r\n      \r\n      // Controle motor\r\n      movementSmoothness: [],\r\n      speedControl: [],\r\n      forceModulation: [],\r\n      coordinationControl: []\r\n    };\r\n\r\n    this.sessionData = {\r\n      startTime: null,\r\n      endTime: null,\r\n      totalPieceManipulations: 0,\r\n      successfulPlacements: 0,\r\n      failedPlacements: 0,\r\n      averageMovementTime: 0,\r\n      averageClickAccuracy: 0,\r\n      motorEfficiency: 0,\r\n      fatigueLevel: 0,\r\n      motorConsistency: 0\r\n    };\r\n  }\r\n\r\n  // ========================================================================\r\n  // MÉTODOS DE COLETA\r\n  // ========================================================================\r\n\r\n  async collect(gameData) {\r\n    try {\r\n      // Simular coleta de dados motores\r\n      const motorData = {\r\n        precision: this.analyzePrecision(gameData),\r\n        coordination: this.analyzeCoordination(gameData),\r\n        control: this.analyzeControl(gameData),\r\n        efficiency: this.calculateMotorEfficiency(gameData)\r\n      };\r\n\r\n      return {\r\n        timestamp: Date.now(),\r\n        gameType: 'QuebraCabeca',\r\n        motorData: motorData,\r\n        score: this.calculateOverallScore(motorData)\r\n      };\r\n    } catch (error) {\r\n      console.error('Erro na coleta de dados motores:', error);\r\n      return {\r\n        timestamp: Date.now(),\r\n        gameType: 'QuebraCabeca',\r\n        error: error.message,\r\n        score: 0.5\r\n      };\r\n    }\r\n  }\r\n\r\n  analyzePrecision(gameData) {\r\n    return {\r\n      clickAccuracy: 0.8,\r\n      placementPrecision: 0.75,\r\n      graspAccuracy: 0.7,\r\n      stability: 0.65\r\n    };\r\n  }\r\n\r\n  analyzeCoordination(gameData) {\r\n    return {\r\n      handEyeCoordination: 0.75,\r\n      bimanualCoordination: 0.7,\r\n      spatialCoordination: 0.8,\r\n      temporalCoordination: 0.65\r\n    };\r\n  }\r\n\r\n  analyzeControl(gameData) {\r\n    return {\r\n      movementSmoothness: 0.7,\r\n      speedControl: 0.75,\r\n      forceModulation: 0.6,\r\n      directionalControl: 0.8\r\n    };\r\n  }\r\n\r\n  calculateMotorEfficiency(gameData) {\r\n    return 0.72;\r\n  }\r\n\r\n  calculateOverallScore(motorData) {\r\n    const scores = [\r\n      motorData.precision.clickAccuracy,\r\n      motorData.coordination.handEyeCoordination,\r\n      motorData.control.movementSmoothness,\r\n      motorData.efficiency\r\n    ];\r\n\r\n    return scores.reduce((sum, score) => sum + score, 0) / scores.length;\r\n  }\r\n\r\n  // ========================================================================\r\n  // MÉTODOS DE ANÁLISE\r\n  // ========================================================================\r\n\r\n  generateReport() {\r\n    return {\r\n      precision: this.motorMetrics.placementPrecision,\r\n      coordination: this.motorMetrics.coordinationControl,\r\n      control: this.motorMetrics.movementSmoothness,\r\n      efficiency: this.sessionData.motorEfficiency,\r\n      recommendations: this.generateRecommendations()\r\n    };\r\n  }\r\n\r\n  generateRecommendations() {\r\n    return [\r\n      'Praticar exercícios de coordenação motora fina',\r\n      'Desenvolver controle de movimento com atividades precisas',\r\n      'Fortalecer estabilidade motora com treino específico'\r\n    ];\r\n  }\r\n\r\n  getActivityScore() {\r\n    return Math.round(this.sessionData.motorEfficiency * 1000);\r\n  }\r\n}\r\n\r\nexport default MotorSkillsCollector;\r\n", "// ============================================================================\r\n// PATTERN RECOGNITION COLLECTOR - QUEBRA-CABEÇA\r\n// Coleta e análise de reconhecimento de padrões visuais em quebra-cabeças\r\n// ============================================================================\r\n\r\nimport { BaseCollector } from '../../../utils/BaseCollector.js';\r\n\r\nexport class PatternRecognitionCollector extends BaseCollector {\r\n  constructor() {\r\n    super('PatternRecognition');\r\n    \r\n    this.patternMetrics = {\r\n      // Reconhecimento de padrões visuais\r\n      colorPatterns: [],\r\n      shapePatterns: [],\r\n      texturePatterns: [],\r\n      edgePatterns: [],\r\n      \r\n      // Análise de fragmentos\r\n      fragmentAnalysis: [],\r\n      pieceClassification: [],\r\n      contextualClues: [],\r\n      visualSimilarity: []\r\n    };\r\n\r\n    this.sessionData = {\r\n      startTime: null,\r\n      endTime: null,\r\n      totalPatternRecognitions: 0,\r\n      correctRecognitions: 0,\r\n      incorrectRecognitions: 0,\r\n      averageRecognitionTime: 0,\r\n      patternComplexityHandled: 0,\r\n      recognitionAccuracy: 0,\r\n      scanningEfficiency: 0,\r\n      visualProcessingSpeed: 0\r\n    };\r\n  }\r\n\r\n  // ========================================================================\r\n  // MÉTODOS DE COLETA\r\n  // ========================================================================\r\n\r\n  async collect(gameData) {\r\n    try {\r\n      // Simular coleta de dados de reconhecimento de padrões\r\n      const patternData = {\r\n        colorRecognition: this.analyzeColorRecognition(gameData),\r\n        shapeRecognition: this.analyzeShapeRecognition(gameData),\r\n        patternMatching: this.analyzePatternMatching(gameData),\r\n        scanningEfficiency: this.calculateScanningEfficiency(gameData)\r\n      };\r\n\r\n      return {\r\n        timestamp: Date.now(),\r\n        gameType: 'QuebraCabeca',\r\n        patternData: patternData,\r\n        score: this.calculateOverallScore(patternData)\r\n      };\r\n    } catch (error) {\r\n      console.error('Erro na coleta de dados de reconhecimento de padrões:', error);\r\n      return {\r\n        timestamp: Date.now(),\r\n        gameType: 'QuebraCabeca',\r\n        error: error.message,\r\n        score: 0.5\r\n      };\r\n    }\r\n  }\r\n\r\n  analyzeColorRecognition(gameData) {\r\n    return {\r\n      colorAccuracy: 0.8,\r\n      colorDiscrimination: 0.75,\r\n      colorMatching: 0.85,\r\n      colorMemory: 0.7\r\n    };\r\n  }\r\n\r\n  analyzeShapeRecognition(gameData) {\r\n    return {\r\n      shapeAccuracy: 0.78,\r\n      edgeRecognition: 0.82,\r\n      cornerDetection: 0.75,\r\n      shapeClassification: 0.8\r\n    };\r\n  }\r\n\r\n  analyzePatternMatching(gameData) {\r\n    return {\r\n      matchingAccuracy: 0.77,\r\n      patternComplexity: 0.6,\r\n      visualSimilarity: 0.73,\r\n      contextualClues: 0.68\r\n    };\r\n  }\r\n\r\n  calculateScanningEfficiency(gameData) {\r\n    return 0.74;\r\n  }\r\n\r\n  calculateOverallScore(patternData) {\r\n    const scores = [\r\n      patternData.colorRecognition.colorAccuracy,\r\n      patternData.shapeRecognition.shapeAccuracy,\r\n      patternData.patternMatching.matchingAccuracy,\r\n      patternData.scanningEfficiency\r\n    ];\r\n\r\n    return scores.reduce((sum, score) => sum + score, 0) / scores.length;\r\n  }\r\n\r\n  // ========================================================================\r\n  // MÉTODOS DE ANÁLISE\r\n  // ========================================================================\r\n\r\n  generateReport() {\r\n    return {\r\n      colorPatterns: this.patternMetrics.colorPatterns,\r\n      shapePatterns: this.patternMetrics.shapePatterns,\r\n      scanningEfficiency: this.sessionData.scanningEfficiency,\r\n      recognitionAccuracy: this.sessionData.recognitionAccuracy,\r\n      recommendations: this.generateRecommendations()\r\n    };\r\n  }\r\n\r\n  generateRecommendations() {\r\n    return [\r\n      'Praticar exercícios de reconhecimento de padrões visuais',\r\n      'Desenvolver habilidades de varredura visual sistemática',\r\n      'Fortalecer discriminação de cores e formas'\r\n    ];\r\n  }\r\n\r\n  getActivityScore() {\r\n    return Math.round(this.sessionData.recognitionAccuracy * 1000);\r\n  }\r\n}\r\n\r\nexport default PatternRecognitionCollector;\r\n", "// ============================================================================\r\n// MEMORY COLLECTOR - QUEBRA-CABEÇA\r\n// Coleta e análise de memória visual e espacial durante o jogo de quebra-cabeça\r\n// ============================================================================\r\n\r\nimport { BaseCollector } from '../../../utils/BaseCollector.js';\r\n\r\nexport class MemoryCollector extends BaseCollector {\r\n  constructor() {\r\n    super('Memory');\r\n    \r\n    this.memoryMetrics = {\r\n      // Memória visual\r\n      visualMemory: [],\r\n      imageRecall: [],\r\n      visualRecognition: [],\r\n      visualRetention: [],\r\n      \r\n      // Memória espacial\r\n      spatialMemory: [],\r\n      locationRecall: [],\r\n      spatialRelationships: [],\r\n      spatialMapping: [],\r\n      \r\n      // Memória de trabalho\r\n      workingMemory: [],\r\n      temporaryStorage: [],\r\n      informationManipulation: [],\r\n      memoryUpdating: [],\r\n      \r\n      // Memória a longo prazo\r\n      longTermMemory: [],\r\n      patternStorage: [],\r\n      strategicMemory: [],\r\n      proceduralMemory: []\r\n    };\r\n\r\n    this.sessionData = {\r\n      startTime: null,\r\n      endTime: null,\r\n      totalMemoryOperations: 0,\r\n      successfulRecalls: 0,\r\n      failedRecalls: 0,\r\n      averageRecallTime: 0,\r\n      memoryCapacityUsed: 0,\r\n      memoryEfficiency: 0,\r\n      interferenceLevels: 0,\r\n      forgettingRate: 0\r\n    };\r\n  }\r\n\r\n  // ========================================================================\r\n  // MÉTODOS DE COLETA\r\n  // ========================================================================\r\n\r\n  async collect(gameData) {\r\n    try {\r\n      // Simular coleta de dados de memória\r\n      const memoryData = {\r\n        visualMemory: this.analyzeVisualMemory(gameData),\r\n        spatialMemory: this.analyzeSpatialMemory(gameData),\r\n        workingMemory: this.analyzeWorkingMemory(gameData),\r\n        memoryEfficiency: this.calculateMemoryEfficiency(gameData)\r\n      };\r\n\r\n      return {\r\n        timestamp: Date.now(),\r\n        gameType: 'QuebraCabeca',\r\n        memoryData: memoryData,\r\n        score: this.calculateOverallScore(memoryData)\r\n      };\r\n    } catch (error) {\r\n      console.error('Erro na coleta de dados de memória:', error);\r\n      return {\r\n        timestamp: Date.now(),\r\n        gameType: 'QuebraCabeca',\r\n        error: error.message,\r\n        score: 0.5\r\n      };\r\n    }\r\n  }\r\n\r\n  analyzeVisualMemory(gameData) {\r\n    return {\r\n      visualRecall: 0.7,\r\n      imageRecognition: 0.6,\r\n      visualRetention: 0.8,\r\n      patternMemory: 0.65\r\n    };\r\n  }\r\n\r\n  analyzeSpatialMemory(gameData) {\r\n    return {\r\n      locationMemory: 0.75,\r\n      spatialRelations: 0.7,\r\n      spatialMapping: 0.6,\r\n      spatialSpan: 5\r\n    };\r\n  }\r\n\r\n  analyzeWorkingMemory(gameData) {\r\n    return {\r\n      capacity: 4,\r\n      efficiency: 0.7,\r\n      manipulation: 0.6,\r\n      updating: 0.65\r\n    };\r\n  }\r\n\r\n  calculateMemoryEfficiency(gameData) {\r\n    return 0.7;\r\n  }\r\n\r\n  calculateOverallScore(memoryData) {\r\n    const scores = [\r\n      memoryData.visualMemory.visualRecall,\r\n      memoryData.spatialMemory.locationMemory,\r\n      memoryData.workingMemory.efficiency,\r\n      memoryData.memoryEfficiency\r\n    ];\r\n\r\n    return scores.reduce((sum, score) => sum + score, 0) / scores.length;\r\n  }\r\n\r\n  // ========================================================================\r\n  // MÉTODOS DE ANÁLISE\r\n  // ========================================================================\r\n\r\n  generateReport() {\r\n    return {\r\n      visualMemory: this.memoryMetrics.visualMemory,\r\n      spatialMemory: this.memoryMetrics.spatialMemory,\r\n      workingMemory: this.memoryMetrics.workingMemory,\r\n      efficiency: this.sessionData.memoryEfficiency,\r\n      recommendations: this.generateRecommendations()\r\n    };\r\n  }\r\n\r\n  generateRecommendations() {\r\n    return [\r\n      'Praticar exercícios de memória visual',\r\n      'Desenvolver memória espacial com jogos de localização',\r\n      'Fortalecer memória de trabalho com tarefas duplas'\r\n    ];\r\n  }\r\n\r\n  getActivityScore() {\r\n    return Math.round(this.sessionData.memoryEfficiency * 1000);\r\n  }\r\n}\r\n\r\nexport default MemoryCollector;\r\n", "// ============================================================================\r\n// PERCEPTUAL PROCESSING COLLECTOR - QUEBRA-CABEÇA\r\n// Coleta e análise de processamento perceptual visual durante o quebra-cabeça\r\n// ============================================================================\r\n\r\nimport { BaseCollector } from '../../../utils/BaseCollector.js';\r\n\r\nexport class PerceptualProcessingCollector extends BaseCollector {\r\n  constructor() {\r\n    super('PerceptualProcessing');\r\n    \r\n    this.perceptualMetrics = {\r\n      // Processamento visual básico\r\n      visualAcuity: [],\r\n      contrastSensitivity: [],\r\n      colorDiscrimination: [],\r\n      motionDetection: [],\r\n      \r\n      // Processamento de formas e objetos\r\n      shapeProcessing: [],\r\n      objectRecognition: [],\r\n      figureGroundSeparation: [],\r\n      visualClosure: []\r\n    };\r\n\r\n    this.sessionData = {\r\n      startTime: null,\r\n      endTime: null,\r\n      totalPerceptualOperations: 0,\r\n      averageProcessingTime: 0,\r\n      perceptualAccuracy: 0,\r\n      visualEfficiency: 0,\r\n      processingSpeed: 0,\r\n      integrationScore: 0,\r\n      perceptualLoad: 0\r\n    };\r\n  }\r\n\r\n  // ========================================================================\r\n  // MÉTODOS DE COLETA\r\n  // ========================================================================\r\n\r\n  async collect(gameData) {\r\n    try {\r\n      // Simular coleta de dados de processamento perceptual\r\n      const perceptualData = {\r\n        visualProcessing: this.analyzeVisualProcessing(gameData),\r\n        objectRecognition: this.analyzeObjectRecognition(gameData),\r\n        perceptualIntegration: this.analyzePerceptualIntegration(gameData),\r\n        processingEfficiency: this.calculateProcessingEfficiency(gameData)\r\n      };\r\n\r\n      return {\r\n        timestamp: Date.now(),\r\n        gameType: 'QuebraCabeca',\r\n        perceptualData: perceptualData,\r\n        score: this.calculateOverallScore(perceptualData)\r\n      };\r\n    } catch (error) {\r\n      console.error('Erro na coleta de dados de processamento perceptual:', error);\r\n      return {\r\n        timestamp: Date.now(),\r\n        gameType: 'QuebraCabeca',\r\n        error: error.message,\r\n        score: 0.5\r\n      };\r\n    }\r\n  }\r\n\r\n  analyzeVisualProcessing(gameData) {\r\n    return {\r\n      visualAcuity: 0.82,\r\n      contrastSensitivity: 0.78,\r\n      colorDiscrimination: 0.85,\r\n      motionDetection: 0.73\r\n    };\r\n  }\r\n\r\n  analyzeObjectRecognition(gameData) {\r\n    return {\r\n      objectRecognition: 0.79,\r\n      shapeProcessing: 0.77,\r\n      figureGroundSeparation: 0.74,\r\n      visualClosure: 0.81\r\n    };\r\n  }\r\n\r\n  analyzePerceptualIntegration(gameData) {\r\n    return {\r\n      featureIntegration: 0.76,\r\n      perceptualBinding: 0.72,\r\n      contextualProcessing: 0.78,\r\n      gestaltProcessing: 0.75\r\n    };\r\n  }\r\n\r\n  calculateProcessingEfficiency(gameData) {\r\n    return 0.77;\r\n  }\r\n\r\n  calculateOverallScore(perceptualData) {\r\n    const scores = [\r\n      perceptualData.visualProcessing.visualAcuity,\r\n      perceptualData.objectRecognition.objectRecognition,\r\n      perceptualData.perceptualIntegration.featureIntegration,\r\n      perceptualData.processingEfficiency\r\n    ];\r\n\r\n    return scores.reduce((sum, score) => sum + score, 0) / scores.length;\r\n  }\r\n\r\n  // ========================================================================\r\n  // MÉTODOS DE ANÁLISE\r\n  // ========================================================================\r\n\r\n  generateReport() {\r\n    return {\r\n      visualAcuity: this.perceptualMetrics.visualAcuity,\r\n      objectRecognition: this.perceptualMetrics.objectRecognition,\r\n      processingEfficiency: this.sessionData.visualEfficiency,\r\n      integrationScore: this.sessionData.integrationScore,\r\n      recommendations: this.generateRecommendations()\r\n    };\r\n  }\r\n\r\n  generateRecommendations() {\r\n    return [\r\n      'Praticar exercícios de processamento visual',\r\n      'Desenvolver habilidades de reconhecimento de objetos',\r\n      'Fortalecer integração perceptual com atividades específicas'\r\n    ];\r\n  }\r\n\r\n  getActivityScore() {\r\n    return Math.round(this.sessionData.visualEfficiency * 1000);\r\n  }\r\n}\r\n\r\nexport default PerceptualProcessingCollector;\r\n", "/**\r\n * 🧩 QUEBRA-CABEÇA COLLECTORS HUB\r\n * Portal Betina V3 - Hub de coletores para jogos de quebra-cabeça\r\n * Coleta dados sobre raciocínio espacial, resolução de problemas e processamento visual-espacial\r\n */\r\n\r\nimport { SpatialReasoningCollector } from './SpatialReasoningCollector.js';\r\nimport { ProblemSolvingCollector } from './ProblemSolvingCollector.js';\r\nimport { VisualSpatialCollector } from './VisualSpatialCollector.js';\r\nimport { MotorSkillsCollector } from './MotorSkillsCollector.js';\r\nimport { PatternRecognitionCollector } from './PatternRecognitionCollector.js';\r\nimport { MemoryCollector } from './MemoryCollector.js';\r\nimport { PerceptualProcessingCollector } from './PerceptualProcessingCollector.js';\r\n\r\nexport class QuebraCabecaCollectorsHub {\r\n  constructor() {\r\n    this.hubId = 'quebra-cabeca-collectors-hub';\r\n    this.version = '3.0.0';\r\n    this.gameType = 'QuebraCabeca';\r\n    \r\n    // Inicializar coletores especializados\r\n    this._collectors = {\r\n      spatialReasoning: new SpatialReasoningCollector(),\r\n      problemSolving: new ProblemSolvingCollector(),\r\n      visualSpatial: new VisualSpatialCollector(),\r\n      motorSkills: new MotorSkillsCollector(),\r\n      patternRecognition: new PatternRecognitionCollector(),\r\n      memory: new MemoryCollector(),\r\n      perceptualProcessing: new PerceptualProcessingCollector()\r\n    };\r\n    \r\n    this.sessionData = [];\r\n    this.analysisCache = new Map();\r\n    this.isInitialized = true;\r\n\r\n    console.log('🧩 QuebraCabecaCollectorsHub inicializado v3.0.0');\r\n  }\r\n\r\n  /**\r\n   * Getter para coletores - necessário para GameSpecificProcessors\r\n   */\r\n  get collectors() {\r\n    return this._collectors;\r\n  }\r\n  \r\n  /**\r\n   * Executa análise completa dos dados do quebra-cabeça\r\n   */\r\n  async runCompleteAnalysis(gameData) {\r\n    try {\r\n      const timestamp = Date.now();\r\n      const sessionId = gameData.sessionId || `session_${timestamp}`;\r\n      \r\n      // Coletar dados de cada coletor\r\n      const spatialAnalysis = await this.collectors.spatialReasoning.collect(gameData);\r\n      const problemSolvingAnalysis = await this.collectors.problemSolving.collect(gameData);\r\n      const visualSpatialAnalysis = await this.collectors.visualSpatial.collect(gameData);\r\n      const motorSkillsAnalysis = await this.collectors.motorSkills.collect(gameData);\r\n      const patternRecognitionAnalysis = await this.collectors.patternRecognition.collect(gameData);\r\n      const memoryAnalysis = await this.collectors.memory.collect(gameData);\r\n      const perceptualProcessingAnalysis = await this.collectors.perceptualProcessing.collect(gameData);\r\n      \r\n      const completeAnalysis = {\r\n        timestamp,\r\n        sessionId,\r\n        gameType: 'QuebraCabeca',\r\n        spatialReasoning: spatialAnalysis,\r\n        problemSolving: problemSolvingAnalysis,\r\n        visualSpatial: visualSpatialAnalysis,\r\n        motorSkills: motorSkillsAnalysis,\r\n        patternRecognition: patternRecognitionAnalysis,\r\n        memory: memoryAnalysis,\r\n        perceptualProcessing: perceptualProcessingAnalysis,\r\n        overallPerformance: this.calculateOverallPerformance({\r\n          spatialAnalysis,\r\n          problemSolvingAnalysis, \r\n          visualSpatialAnalysis,\r\n          motorSkillsAnalysis,\r\n          patternRecognitionAnalysis,\r\n          memoryAnalysis,\r\n          perceptualProcessingAnalysis\r\n        }),\r\n        insights: this.generateInsights({\r\n          spatialAnalysis,\r\n          problemSolvingAnalysis,\r\n          visualSpatialAnalysis,\r\n          motorSkillsAnalysis,\r\n          patternRecognitionAnalysis,\r\n          memoryAnalysis,\r\n          perceptualProcessingAnalysis\r\n        }),\r\n        recommendations: this.generateRecommendations({\r\n          spatialAnalysis,\r\n          problemSolvingAnalysis,\r\n          visualSpatialAnalysis,\r\n          motorSkillsAnalysis,\r\n          patternRecognitionAnalysis,\r\n          memoryAnalysis,\r\n          perceptualProcessingAnalysis\r\n        })\r\n      };\r\n      \r\n      this.sessionData.push(completeAnalysis);\r\n      return completeAnalysis;\r\n      \r\n    } catch (error) {\r\n      console.error('🧩 Erro na análise completa do QuebraCabeca:', error);\r\n      return {\r\n        error: error.message,\r\n        timestamp: Date.now(),\r\n        gameType: 'QuebraCabeca'\r\n      };\r\n    }\r\n  }\r\n  \r\n  /**\r\n   * Calcula desempenho geral\r\n   */\r\n  calculateOverallPerformance(analyses) {\r\n    try {\r\n      const scores = Object.values(analyses).map(analysis => analysis.score || 0.5);\r\n      const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;\r\n      \r\n      return {\r\n        score: averageScore,\r\n        level: averageScore > 0.8 ? 'Avançado' : averageScore > 0.6 ? 'Intermediário' : 'Básico',\r\n        strengths: this.identifyStrengths(analyses),\r\n        challenges: this.identifyChallenges(analyses)\r\n      };\r\n    } catch (error) {\r\n      console.error('🧩 Erro ao calcular desempenho geral:', error);\r\n      return { score: 0.5, level: 'Básico', strengths: [], challenges: [] };\r\n    }\r\n  }\r\n  \r\n  /**\r\n   * Identifica pontos fortes\r\n   */\r\n  identifyStrengths(analyses) {\r\n    const strengths = [];\r\n    if (analyses.spatialAnalysis?.score > 0.7) strengths.push('Raciocínio espacial forte');\r\n    if (analyses.problemSolvingAnalysis?.score > 0.7) strengths.push('Boa resolução de problemas');\r\n    if (analyses.visualSpatialAnalysis?.score > 0.7) strengths.push('Processamento visual-espacial eficiente');\r\n    if (analyses.motorSkillsAnalysis?.score > 0.7) strengths.push('Habilidades motoras finas desenvolvidas');\r\n    if (analyses.patternRecognitionAnalysis?.score > 0.7) strengths.push('Excelente reconhecimento de padrões');\r\n    if (analyses.memoryAnalysis?.score > 0.7) strengths.push('Memória visual e espacial eficaz');\r\n    if (analyses.perceptualProcessingAnalysis?.score > 0.7) strengths.push('Processamento perceptual avançado');\r\n    return strengths;\r\n  }\r\n  \r\n  /**\r\n   * Identifica desafios\r\n   */\r\n  identifyChallenges(analyses) {\r\n    const challenges = [];\r\n    if (analyses.spatialAnalysis?.score < 0.5) challenges.push('Raciocínio espacial precisa de desenvolvimento');\r\n    if (analyses.problemSolvingAnalysis?.score < 0.5) challenges.push('Resolução de problemas requer prática');\r\n    if (analyses.visualSpatialAnalysis?.score < 0.5) challenges.push('Processamento visual-espacial precisa de suporte');\r\n    if (analyses.motorSkillsAnalysis?.score < 0.5) challenges.push('Habilidades motoras finas necessitam treino');\r\n    if (analyses.patternRecognitionAnalysis?.score < 0.5) challenges.push('Reconhecimento de padrões precisa ser fortalecido');\r\n    if (analyses.memoryAnalysis?.score < 0.5) challenges.push('Memória visual e espacial requer desenvolvimento');\r\n    if (analyses.perceptualProcessingAnalysis?.score < 0.5) challenges.push('Processamento perceptual necessita aprimoramento');\r\n    return challenges;\r\n  }\r\n  \r\n  /**\r\n   * Gera insights terapêuticos\r\n   */\r\n  generateInsights(analyses) {\r\n    const insights = [\r\n      'Quebra-cabeça desenvolve raciocínio espacial e resolução de problemas',\r\n      'Atividade estimula processamento visual-espacial e memória',\r\n      'Jogos de puzzle fortalecem persistência e paciência',\r\n      'Manipulação de peças desenvolve habilidades motoras finas',\r\n      'Reconhecimento de padrões aprimora processamento visual',\r\n      'Integração perceptual é exercitada através da montagem',\r\n      'Memória de trabalho é constantemente desafiada'\r\n    ];\r\n    \r\n    return insights;\r\n  }\r\n  \r\n  /**\r\n   * Gera recomendações\r\n   */\r\n  generateRecommendations(analyses) {\r\n    const recommendations = [];\r\n    \r\n    if (analyses.spatialAnalysis?.score < 0.6) {\r\n      recommendations.push('Praticar com quebra-cabeças mais simples primeiro');\r\n    }\r\n    \r\n    if (analyses.problemSolvingAnalysis?.score > 0.8) {\r\n      recommendations.push('Aumentar complexidade dos quebra-cabeças');\r\n    }\r\n    \r\n    if (analyses.motorSkillsAnalysis?.score < 0.6) {\r\n      recommendations.push('Incluir exercícios de coordenação motora fina');\r\n    }\r\n    \r\n    if (analyses.patternRecognitionAnalysis?.score < 0.6) {\r\n      recommendations.push('Praticar jogos de reconhecimento de padrões');\r\n    }\r\n    \r\n    if (analyses.memoryAnalysis?.score < 0.6) {\r\n      recommendations.push('Exercitar memória visual com atividades específicas');\r\n    }\r\n    \r\n    if (analyses.perceptualProcessingAnalysis?.score < 0.6) {\r\n      recommendations.push('Desenvolver processamento perceptual com exercícios visuais');\r\n    }\r\n    \r\n    recommendations.push('Combinar quebra-cabeças com atividades de construção');\r\n    \r\n    return recommendations;\r\n  }\r\n}\r\n\r\nexport default QuebraCabecaCollectorsHub;\r\n\r\n// Exportar coletores individuais para uso específico\r\nexport {\r\n  SpatialReasoningCollector,\r\n  ProblemSolvingCollector,\r\n  VisualSpatialCollector,\r\n  MotorSkillsCollector,\r\n  PatternRecognitionCollector,\r\n  MemoryCollector,\r\n  PerceptualProcessingCollector\r\n};\r\n", "/**\n * @file QuebraCabecaProcessors.js\n * @description Processadores específicos para o jogo Quebra-Cabeça\n * @version 3.0.0\n * <AUTHOR> Betina V3\n */\n\nimport { IGameProcessor } from '../IGameProcessor.js';\n\n// Detectar ambiente\nconst isBrowser = typeof window !== 'undefined' && typeof window.document !== 'undefined';\n\n// Logger adaptado ao ambiente\nconst logger = isBrowser \n  ? {\n      info: (...args) => console.info('%c🧩 [QUEBRA-CABECA]', 'color: #2196F3', new Date().toISOString(), ...args),\n      error: (...args) => console.error('%c🔴 [QUEBRA-ERROR]', 'color: #F44336', new Date().toISOString(), ...args),\n      warn: (...args) => console.warn('%c🟡 [QUEBRA-WARN]', 'color: #FF9800', new Date().toISOString(), ...args),\n      debug: (...args) => console.debug('%c⚪ [QUEBRA-DEBUG]', 'color: #9E9E9E', new Date().toISOString(), ...args),\n      therapeutic: (...args) => console.info('%c🏥 [QUEBRA-THERAPEUTIC]', 'color: #4CAF50', new Date().toISOString(), ...args)\n    }\n  : {\n      info: (...args) => console.info('🧩 [QUEBRA-CABECA]', new Date().toISOString(), ...args),\n      error: (...args) => console.error('🔴 [QUEBRA-ERROR]', new Date().toISOString(), ...args),\n      warn: (...args) => console.warn('🟡 [QUEBRA-WARN]', new Date().toISOString(), ...args),\n      debug: (...args) => console.debug('⚪ [QUEBRA-DEBUG]', new Date().toISOString(), ...args),\n      therapeutic: (...args) => console.info('🏥 [QUEBRA-THERAPEUTIC]', new Date().toISOString(), ...args)\n    };\n\n/**\n * @class QuebraCabecaProcessors\n * @description Processadores especializados para análise terapêutica do jogo Quebra-Cabeça\n */\nexport class QuebraCabecaProcessors extends IGameProcessor {\n  constructor(config = {}) {\n    // Configurações específicas para Quebra-Cabeça\n    const defaultConfig = {\n      category: 'spatial-reasoning',\n      therapeuticFocus: ['spatial_reasoning', 'problem_solving', 'fine_motor_skills'],\n      cognitiveAreas: ['spatial_processing', 'executive_function', 'motor_planning'],\n      thresholds: {\n        accuracy: 60,\n        responseTime: 8000,\n        engagement: 75\n      },\n      ...config\n    };\n    \n    super(defaultConfig);\n    \n    // Logger seguro com método therapeutic garantido\n    this.logger = config.logger && typeof config.logger.therapeutic === 'function' \n      ? config.logger \n      : this.logger || {\n          info: (...args) => console.info('🧩 [QUEBRA-CABECA]', new Date().toISOString(), ...args),\n          error: (...args) => console.error('🔴 [QUEBRA-ERROR]', new Date().toISOString(), ...args),\n          warn: (...args) => console.warn('🟡 [QUEBRA-WARN]', new Date().toISOString(), ...args),\n          debug: (...args) => console.debug('⚪ [QUEBRA-DEBUG]', new Date().toISOString(), ...args),\n          therapeutic: (...args) => console.info('🏥 [QUEBRA-THERAPEUTIC]', new Date().toISOString(), ...args)\n        }\n    \n    this.logger.info('🧩 Processadores Quebra-Cabeça inicializados')\n  };\n\n  /**\n   * Processa métricas específicas do QuebraCabeca\n   * @param {Object} gameData - Dados do jogo QuebraCabeca\n   * @param {Object} sessionData - Dados da sessão\n   * @returns {Object} Métricas processadas\n   */\n  async processQuebraCabecaMetrics(gameData, sessionData) {\n    try {\n      this.logger?.info('🧩 Processando métricas QuebraCabeca...', {\n        sessionId: sessionData.sessionId\n      });\n\n      const { attempts = [], totalTime = 0, completed = false, pieces = [] } = gameData;\n\n      // Métricas básicas\n      const totalAttempts = attempts.length || 1;\n      const correctPlacements = attempts.filter(a => a.correct).length;\n      const accuracy = Math.round((correctPlacements / totalAttempts) * 100);\n\n      // Métricas específicas do quebra-cabeça\n      const spatialReasoning = this.calculateSpatialReasoning(attempts, pieces);\n      const problemSolving = this.calculateProblemSolving(attempts, completed);\n      const persistence = this.calculatePersistence(attempts, totalTime);\n      const visualProcessing = this.calculateVisualProcessing(attempts, pieces);\n      const motorSkills = this.calculateMotorSkills(attempts);\n\n      const metrics = {\n        // Métricas básicas\n        accuracy,\n        totalAttempts,\n        correctPlacements,\n        completionTime: totalTime,\n        completed,\n\n        // Métricas específicas\n        spatialReasoning,\n        problemSolving,\n        persistence,\n        visualProcessing,\n        motorSkills,\n\n        // Métricas derivadas\n        efficiency: this.calculateEfficiency(correctPlacements, totalTime),\n        strategy: this.identifyStrategy(attempts),\n        difficultyHandling: this.assessDifficultyHandling(attempts, pieces)\n      };\n\n      this.logger?.info('✅ Métricas QuebraCabeca processadas', {\n        accuracy,\n        totalAttempts,\n        spatialReasoning: spatialReasoning.score\n      });\n\n      return metrics;\n    } catch (error) {\n      this.logger?.error('❌ Erro ao processar métricas QuebraCabeca:', error);\n      return this.generateFallbackMetrics(gameData);\n    }\n  }\n\n  /**\n   * Processa dados do jogo Quebra-Cabeça\n   * @param {Object} gameData - Dados coletados do jogo\n   * @param {Object} collectorsHub - Hub de coletores específico do jogo\n   * @returns {Promise<Object>} Análise terapêutica específica\n   */\n  async processGameData(gameData, collectorsHub = null) {\n    try {\n      this.logger?.info('🎮 Processando dados QuebraCabeca', {\n        sessionId: gameData.sessionId,\n        userId: gameData.userId,\n        collectorsActive: collectorsHub ? Object.keys(collectorsHub.collectors || {}).length : 0\n      });\n\n      // Processar métricas específicas do jogo\n      const metrics = await this.processQuebraCabecaMetrics(gameData, gameData);\n      \n      // Gerar análise terapêutica\n      const therapeuticAnalysis = this.generateTherapeuticAnalysis(metrics, gameData);\n      \n      // Processar métricas para estrutura padronizada\n      const processedMetrics = this.processMetricsForDatabase(metrics, gameData);\n\n      return {\n        success: true,\n        gameType: this.gameType,\n        metrics,\n        therapeuticAnalysis,\n        processedMetrics,\n        timestamp: new Date().toISOString()\n      };\n\n    } catch (error) {\n      this.logger?.error('❌ Erro ao processar dados QuebraCabeca:', error);\n      return {\n        success: false,\n        gameType: this.gameType,\n        error: error.message,\n        fallbackAnalysis: this.generateFallbackTherapeuticAnalysis(gameData),\n        timestamp: new Date().toISOString()\n      };\n    }\n  };\n\n  /**\n   * Processa coletores com Circuit Breaker para resiliência\n   * @param {Object} collectorsHub - Hub de coletores\n   * @param {Object} gameData - Dados do jogo\n   * @returns {Promise<Object>} Resultados dos coletores\n   */\n  async processCollectorsWithCircuitBreaker(collectorsHub, gameData) {\n    if (!collectorsHub || !collectorsHub.collectors) {\n      this.logger?.warn('⚠️ QuebraCabeca: Hub de coletores não disponível');\n      return { collectors: {}, warning: 'No collectors available' };\n    }\n\n    const results = {};\n    const collectors = collectorsHub.collectors;\n\n    // Processar cada coletor com tratamento de erro\n    for (const [collectorName, collector] of Object.entries(collectors)) {\n      try {\n        if (collector && typeof collector.analyze === 'function') {\n          this.logger?.debug('🎮 Processando coletor: ' + collectorName);\n          results[collectorName] = await this.processWithTimeout(\n            () => collector.analyze(gameData),\n            5000, // 5 segundos timeout\n            collectorName + ' timeout'\n          );\n        } else {\n          this.logger?.warn('⚠️ Coletor ' + collectorName + ' não tem método analyze');\n          results[collectorName] = { error: 'No analyze method' };\n        }\n      } catch (error) {\n        this.logger?.error('❌ QuebraCabecaProcessors: Erro no coletor ' + collectorName + ':', {\n          error: error.message,\n          stack: error.stack?.substring(0, 300),\n          collectorName,\n          timestamp: new Date().toISOString()\n        });\n        results[collectorName] = {\n          error: error.message,\n          fallback: this.generateFallbackMetrics(collectorName, gameData),\n          recovered: true\n        };\n      }\n    }\n\n    return {\n      collectors: results,\n      processedAt: new Date().toISOString(),\n      gameType: 'QuebraCabeca'\n    };\n  };\n\n  /**\n   * Processa com timeout para evitar travamentos\n   */\n  async processWithTimeout(fn, timeout, errorMsg) {\n    return Promise.race([\n      fn(),\n      new Promise((_, reject) => \n        setTimeout(() => reject(new Error(errorMsg)), timeout)\n      )\n    ]);\n  };\n\n  /**\n   * Gera métricas de fallback em caso de erro\n   */\n  generateFallbackMetrics(collectorName, gameData) {\n    return {\n      fallback: true,\n      collector: collectorName,\n      basicScore: 50,\n      confidence: 'low',\n      note: 'Generated due to collector error'\n    };\n  };\n  /**\n   * Gera análise terapêutica completa\n   * @param {Object} metrics - Métricas processadas\n   * @param {Object} gameData - Dados do jogo\n   * @returns {Object} Análise terapêutica\n   */\n  generateTherapeuticAnalysis(metrics, gameData) {\n    try {\n      const analysis = {\n        // Análise comportamental\n        behavioral: {\n          engagement: this.calculateEngagementScore(gameData),\n          persistence: this.calculatePersistenceScore(gameData),\n          adaptability: this.calculateAdaptabilityScore(gameData),\n          frustrationTolerance: this.calculateFrustrationTolerance(gameData),\n          socialInteraction: this.calculateSocialInteractionScore(gameData)\n        },\n        \n        // Análise cognitiva\n        cognitive: {\n          attention: this.calculateAttentionScore(gameData),\n          memory: this.calculateMemoryScore(gameData),\n          processingSpeed: this.calculateProcessingSpeedScore(gameData),\n          executiveFunction: this.calculateExecutiveFunctionScore(gameData),\n          visualProcessing: this.calculateVisualProcessingScore(gameData)\n        },\n        \n        // Análise sensorial\n        sensory: {\n          visualPerception: this.calculateVisualPerceptionScore(gameData),\n          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),\n          tactileProcessing: this.calculateTactileProcessingScore(gameData),\n          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)\n        },\n        \n        // Análise motora\n        motor: {\n          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),\n          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),\n          coordination: this.calculateCoordinationScore(gameData),\n          motorPlanning: this.calculateMotorPlanningScore(gameData)\n        },\n        \n        // Recomendações terapêuticas\n        recommendations: this.generateTherapeuticRecommendations(metrics, gameData),\n        \n        // Indicadores de progresso\n        progressIndicators: this.generateProgressIndicators(metrics, gameData),\n        \n        // Insights específicos do jogo\n        gameSpecificInsights: this.generateGameSpecificInsights(metrics, gameData),\n        \n        // Metadados\n        metadata: {\n          analysisTimestamp: new Date().toISOString(),\n          gameType: this.gameType,\n          analysisVersion: '3.0.0',\n          confidenceScore: this.calculateAnalysisConfidenceScore(metrics, gameData)\n        }\n      };\n      \n      return analysis;\n    } catch (error) {\n      this.logger?.error('❌ Erro ao gerar análise terapêutica:', error);\n      return this.generateFallbackTherapeuticAnalysis(gameData);\n    }\n  };\n  /**\n   * Métodos de cálculo de scores terapêuticos\n   */\n  calculateEngagementScore(gameData) {\n    const interactions = gameData.interactions || [];\n    const totalTime = gameData.totalTime || 1000;\n    const completionRate = gameData.completionRate || 0;\n    \n    let score = 50; // Base score\n    \n    // Fator de interação\n    if (interactions.length > 0) {\n      score += Math.min(30, interactions.length * 2);\n    }\n    \n    // Fator de tempo\n    if (totalTime > 30000) { // Mais de 30 segundos\n      score += 10;\n    }\n    \n    // Fator de completude\n    score += completionRate * 10;\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculatePersistenceScore(gameData) {\n    const attempts = gameData.attempts || 1;\n    const errors = gameData.errors || 0;\n    const completion = gameData.completion || 0;\n    \n    let score = 50;\n    \n    if (attempts > 1 && completion > 0.5) {\n      score += 20; // Persistiu após tentativas\n    }\n    \n    if (errors > 0 && completion > 0.8) {\n      score += 15; // Superou erros\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateAdaptabilityScore(gameData) {\n    const difficultyChanges = gameData.difficultyChanges || 0;\n    const adaptationSuccess = gameData.adaptationSuccess || 0;\n    \n    let score = 50;\n    \n    if (difficultyChanges > 0) {\n      score += adaptationSuccess * 20;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateFrustrationTolerance(gameData) {\n    const errors = gameData.errors || 0;\n    const quitEarly = gameData.quitEarly || false;\n    const completion = gameData.completion || 0;\n    \n    let score = 70; // Base high score\n    \n    if (errors > 3 && !quitEarly) {\n      score += 15; // Tolerou erros\n    }\n    \n    if (completion > 0.8) {\n      score += 15; // Completou apesar de dificuldades\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateSocialInteractionScore(gameData) {\n    // Para jogos individuais, score baseado em engajamento\n    const engagement = this.calculateEngagementScore(gameData);\n    return Math.max(30, Math.min(80, engagement * 0.8));\n  }\n\n  calculateAttentionScore(gameData) {\n    const focusTime = gameData.focusTime || 0;\n    const distractions = gameData.distractions || 0;\n    const responseTime = gameData.averageResponseTime || 3000;\n    \n    let score = 50;\n    \n    if (focusTime > 60000) { // Mais de 1 minuto focado\n      score += 20;\n    }\n    \n    if (distractions < 2) {\n      score += 15;\n    }\n    \n    if (responseTime < 2000) {\n      score += 15;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateMemoryScore(gameData) {\n    const accuracy = gameData.accuracy || 0;\n    const patterns = gameData.patterns || [];\n    \n    let score = 50;\n    \n    if (accuracy > 70) {\n      score += 25;\n    }\n    \n    if (patterns.length > 3) {\n      score += 15;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateProcessingSpeedScore(gameData) {\n    const responseTime = gameData.averageResponseTime || 3000;\n    const accuracy = gameData.accuracy || 0;\n    \n    let score = 50;\n    \n    if (responseTime < 1500 && accuracy > 60) {\n      score += 30;\n    } else if (responseTime < 2500) {\n      score += 15;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateExecutiveFunctionScore(gameData) {\n    const planningEvidence = gameData.planningEvidence || 0;\n    const inhibitionControl = gameData.inhibitionControl || 0;\n    const workingMemory = gameData.workingMemory || 0;\n    \n    const score = (planningEvidence + inhibitionControl + workingMemory) / 3;\n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateVisualProcessingScore(gameData) {\n    const visualTasks = gameData.visualTasks || 0;\n    const visualAccuracy = gameData.visualAccuracy || 0;\n    \n    let score = 50;\n    \n    if (visualTasks > 5 && visualAccuracy > 70) {\n      score += 25;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateVisualPerceptionScore(gameData) {\n    return this.calculateVisualProcessingScore(gameData);\n  }\n\n  calculateAuditoryProcessingScore(gameData) {\n    const auditoryTasks = gameData.auditoryTasks || 0;\n    const auditoryAccuracy = gameData.auditoryAccuracy || 50;\n    \n    let score = 50;\n    \n    if (auditoryTasks > 0) {\n      score = auditoryAccuracy;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateTactileProcessingScore(gameData) {\n    const touchInteractions = gameData.touchInteractions || 0;\n    const touchAccuracy = gameData.touchAccuracy || 50;\n    \n    let score = 50;\n    \n    if (touchInteractions > 3) {\n      score = touchAccuracy;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateSensoryIntegrationScore(gameData) {\n    const visual = this.calculateVisualPerceptionScore(gameData);\n    const auditory = this.calculateAuditoryProcessingScore(gameData);\n    const tactile = this.calculateTactileProcessingScore(gameData);\n    \n    return (visual + auditory + tactile) / 3;\n  }\n\n  calculateFineMotorSkillsScore(gameData) {\n    const precision = gameData.precision || 50;\n    const motorControl = gameData.motorControl || 50;\n    \n    return (precision + motorControl) / 2;\n  }\n\n  calculateGrossMotorSkillsScore(gameData) {\n    const movements = gameData.movements || 0;\n    const coordination = gameData.coordination || 50;\n    \n    let score = 50;\n    \n    if (movements > 10) {\n      score = coordination;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateCoordinationScore(gameData) {\n    const eyeHandCoordination = gameData.eyeHandCoordination || 50;\n    const bilateralCoordination = gameData.bilateralCoordination || 50;\n    \n    return (eyeHandCoordination + bilateralCoordination) / 2;\n  }\n\n  calculateMotorPlanningScore(gameData) {\n    const planningSteps = gameData.planningSteps || 0;\n    const executionSuccess = gameData.executionSuccess || 0;\n    \n    let score = 50;\n    \n    if (planningSteps > 0) {\n      score = executionSuccess;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  generateTherapeuticRecommendations(metrics, gameData) {\n    const recommendations = [];\n    \n    // Análise de engajamento\n    const engagement = this.calculateEngagementScore(gameData);\n    if (engagement < 50) {\n      recommendations.push({\n        category: 'engagement',\n        priority: 'high',\n        recommendation: 'Implementar estratégias de motivação e gamificação',\n        rationale: 'Baixo engajamento detectado'\n      });\n    }\n    \n    // Análise de atenção\n    const attention = this.calculateAttentionScore(gameData);\n    if (attention < 50) {\n      recommendations.push({\n        category: 'attention',\n        priority: 'medium',\n        recommendation: 'Exercícios de foco e concentração',\n        rationale: 'Dificuldades atencionais identificadas'\n      });\n    }\n    \n    // Análise de processamento\n    const processing = this.calculateProcessingSpeedScore(gameData);\n    if (processing < 50) {\n      recommendations.push({\n        category: 'processing',\n        priority: 'medium',\n        recommendation: 'Atividades para melhorar velocidade de processamento',\n        rationale: 'Processamento lento identificado'\n      });\n    }\n    \n    return recommendations;\n  }\n\n  generateProgressIndicators(metrics, gameData) {\n    return {\n      overallProgress: this.calculateOverallProgress(gameData),\n      strengthAreas: this.identifyStrengthAreas(gameData),\n      challengeAreas: this.identifyChallengeAreas(gameData),\n      developmentGoals: this.generateDevelopmentGoals(gameData),\n      milestones: this.generateMilestones(gameData)\n    };\n  }\n\n  generateGameSpecificInsights(metrics, gameData) {\n    // Implementação específica para cada jogo será adicionada pelos processadores\n    return {\n      gameType: this.gameType,\n      specificMetrics: metrics,\n      gamePerformance: this.calculateGamePerformance(gameData),\n      adaptationNeeds: this.identifyAdaptationNeeds(gameData)\n    };\n  }\n\n  calculateAnalysisConfidenceScore(metrics, gameData) {\n    let confidence = 50;\n    \n    // Fator de dados disponíveis\n    const dataPoints = Object.keys(gameData).length;\n    if (dataPoints > 10) confidence += 20;\n    else if (dataPoints > 5) confidence += 10;\n    \n    // Fator de métricas processadas\n    const metricsCount = Object.keys(metrics).length;\n    if (metricsCount > 5) confidence += 20;\n    else if (metricsCount > 3) confidence += 10;\n    \n    // Fator de tempo de sessão\n    const sessionTime = gameData.totalTime || 0;\n    if (sessionTime > 60000) confidence += 10; // Mais de 1 minuto\n    \n    return Math.max(0, Math.min(100, confidence));\n  }\n\n  generateFallbackTherapeuticAnalysis(gameData) {\n    return {\n      behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50, socialInteraction: 50 },\n      cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50, visualProcessing: 50 },\n      sensory: { visualPerception: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },\n      motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },\n      recommendations: [],\n      progressIndicators: { overallProgress: 50, strengthAreas: [], challengeAreas: [], developmentGoals: [], milestones: [] },\n      gameSpecificInsights: { gameType: this.gameType, specificMetrics: {}, gamePerformance: 50, adaptationNeeds: [] },\n      metadata: { analysisTimestamp: new Date().toISOString(), gameType: this.gameType, analysisVersion: '3.0.0', confidenceScore: 30 }\n    };\n  }\n\n  calculateOverallProgress(gameData) {\n    const accuracy = gameData.accuracy || 0;\n    const completion = gameData.completion || 0;\n    const engagement = this.calculateEngagementScore(gameData);\n    \n    return (accuracy + completion + engagement) / 3;\n  }\n\n  identifyStrengthAreas(gameData) {\n    const strengths = [];\n    \n    if (gameData.accuracy > 80) strengths.push('Precisão');\n    if (gameData.averageResponseTime < 2000) strengths.push('Velocidade de resposta');\n    if (this.calculateEngagementScore(gameData) > 70) strengths.push('Engajamento');\n    \n    return strengths;\n  }\n\n  identifyChallengeAreas(gameData) {\n    const challenges = [];\n    \n    if (gameData.accuracy < 50) challenges.push('Precisão');\n    if (gameData.averageResponseTime > 4000) challenges.push('Velocidade de resposta');\n    if (this.calculateEngagementScore(gameData) < 40) challenges.push('Engajamento');\n    \n    return challenges;\n  }\n\n  generateDevelopmentGoals(gameData) {\n    const goals = [];\n    \n    if (gameData.accuracy < 70) {\n      goals.push('Melhorar precisão para 70%+');\n    }\n    \n    if (gameData.averageResponseTime > 3000) {\n      goals.push('Reduzir tempo de resposta para menos de 3 segundos');\n    }\n    \n    return goals;\n  }\n\n  generateMilestones(gameData) {\n    return [\n      { milestone: 'Primeira sessão completa', achieved: gameData.completion > 0.8 },\n      { milestone: 'Precisão acima de 50%', achieved: gameData.accuracy > 50 },\n      { milestone: 'Engajamento sustentado', achieved: this.calculateEngagementScore(gameData) > 60 }\n    ];\n  }\n\n  calculateGamePerformance(gameData) {\n    const accuracy = gameData.accuracy || 0;\n    const completion = gameData.completion || 0;\n    const efficiency = gameData.efficiency || 0;\n    \n    return (accuracy + completion + efficiency) / 3;\n  }\n\n  identifyAdaptationNeeds(gameData) {\n    const needs = [];\n    \n    if (gameData.accuracy < 40) {\n      needs.push('Reduzir dificuldade');\n    }\n    \n    if (gameData.averageResponseTime > 5000) {\n      needs.push('Aumentar tempo limite');\n    }\n    \n    if (this.calculateEngagementScore(gameData) < 30) {\n      needs.push('Aumentar elementos motivacionais');\n    }\n    \n    return needs;\n  };\n  /**\n   * Processa métricas para estrutura padronizada do banco de dados\n   * @param {Object} metrics - Métricas processadas\n   * @param {Object} gameData - Dados do jogo\n   * @returns {Object} Métricas processadas para banco\n   */\n  processMetricsForDatabase(metrics, gameData) {\n    try {\n      return {\n        // Métricas básicas\n        basic: {\n          accuracy: gameData.accuracy || 0,\n          responseTime: gameData.averageResponseTime || 0,\n          completion: gameData.completion || 0,\n          score: gameData.score || 0,\n          duration: gameData.totalTime || 0,\n          attempts: gameData.attempts || 1,\n          errors: gameData.errors || 0\n        },\n        \n        // Métricas cognitivas\n        cognitive: {\n          attention: this.calculateAttentionScore(gameData),\n          memory: this.calculateMemoryScore(gameData),\n          processingSpeed: this.calculateProcessingSpeedScore(gameData),\n          executiveFunction: this.calculateExecutiveFunctionScore(gameData)\n        },\n        \n        // Métricas comportamentais\n        behavioral: {\n          engagement: this.calculateEngagementScore(gameData),\n          persistence: this.calculatePersistenceScore(gameData),\n          adaptability: this.calculateAdaptabilityScore(gameData),\n          frustrationTolerance: this.calculateFrustrationTolerance(gameData)\n        },\n        \n        // Métricas sensoriais\n        sensory: {\n          visualProcessing: this.calculateVisualProcessingScore(gameData),\n          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),\n          tactileProcessing: this.calculateTactileProcessingScore(gameData),\n          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)\n        },\n        \n        // Métricas motoras\n        motor: {\n          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),\n          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),\n          coordination: this.calculateCoordinationScore(gameData),\n          motorPlanning: this.calculateMotorPlanningScore(gameData)\n        },\n        \n        // Métricas específicas do jogo\n        gameSpecific: metrics,\n        \n        // Metadados\n        metadata: {\n          gameType: this.gameType,\n          processingTimestamp: new Date().toISOString(),\n          version: '3.0.0'\n        }\n      };\n    } catch (error) {\n      this.logger?.error('❌ Erro ao processar métricas para banco:', error);\n      return {\n        basic: { accuracy: 0, responseTime: 0, completion: 0, score: 0, duration: 0, attempts: 1, errors: 0 },\n        cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50 },\n        behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50 },\n        sensory: { visualProcessing: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },\n        motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },\n        gameSpecific: metrics,\n        metadata: { gameType: this.gameType, processingTimestamp: new Date().toISOString(), version: '3.0.0' }\n      };\n    }\n  }\n\n  /**\n   * Métodos de análise para QuebraCabeca\n   */\n  analyzeQuebraCabecaPrimary(gameData) {\n    const { totalCorrect = 0, totalAttempts = 1, interactions = [] } = gameData;\n    return {\n      accuracy: Math.round((totalCorrect / totalAttempts) * 100),\n      totalAttempts,\n      totalCorrect,\n      primaryScore: this.calculatePrimaryScore(interactions),\n      efficiency: this.calculateEfficiency(interactions)\n    };\n  }\n\n  analyzeQuebraCabecaSecondary(gameData) {\n    const { interactions = [] } = gameData;\n    return {\n      secondaryAccuracy: this.calculateSecondaryAccuracy(interactions),\n      adaptability: this.calculateAdaptability(interactions),\n      consistency: this.calculateConsistency(interactions)\n    };\n  }\n\n  analyzeQuebraCabecaTiming(gameData) {\n    const { interactions = [] } = gameData;\n    const responseTimes = interactions.map(i => i.responseTime || 0).filter(t => t > 0);\n    \n    if (responseTimes.length === 0) {\n      return { average: 0, median: 0, variability: 0, pattern: 'insufficient_data' };\n    }\n\n    const average = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;\n    const sorted = responseTimes.sort((a, b) => a - b);\n    const median = sorted[Math.floor(sorted.length / 2)];\n    \n    return {\n      average: Math.round(average),\n      median: Math.round(median),\n      min: Math.min(...responseTimes),\n      max: Math.max(...responseTimes),\n      variability: Math.round(this.calculateVariability(responseTimes)),\n      pattern: 'normal'\n    };\n  }\n\n  analyzeQuebraCabecaPatterns(gameData) {\n    const { interactions = [] } = gameData;\n    return {\n      totalPatterns: interactions.length,\n      correctPatterns: interactions.filter(i => i.correct).length,\n      patternTypes: this.identifyPatternTypes(interactions),\n      errorPatterns: this.identifyErrorPatterns(interactions)\n    };\n  }\n\n  analyzeQuebraCabecaBehavior(gameData) {\n    const { interactions = [] } = gameData;\n    return {\n      persistence: this.calculatePersistence(interactions),\n      adaptability: this.calculateAdaptability(interactions),\n      engagement: this.calculateEngagementScore(gameData)\n    };\n  }\n\n  analyzeQuebraCabecaCognition(gameData) {\n    const { interactions = [] } = gameData;\n    return {\n      executiveFunction: this.calculateExecutiveFunction(interactions),\n      workingMemory: this.calculateWorkingMemory(interactions),\n      processingSpeed: this.calculateProcessingSpeed(interactions)\n    };\n  }\n\n  generateQuebraCabecaRecommendations(gameData) {\n    const recommendations = [];\n    const accuracy = gameData.accuracy || 0;\n    const responseTime = gameData.averageResponseTime || 0;\n    \n    if (accuracy < 60) {\n      recommendations.push({\n        type: 'accuracy_improvement',\n        priority: 'high',\n        description: 'Exercícios para melhorar precisão em QuebraCabeca'\n      });\n    }\n    \n    if (responseTime > 5000) {\n      recommendations.push({\n        type: 'speed_improvement', \n        priority: 'medium',\n        description: 'Atividades para melhorar velocidade de processamento'\n      });\n    }\n    \n    return recommendations;\n  }\n\n  // Métodos auxiliares\n  calculatePrimaryScore(interactions) {\n    const correctInteractions = interactions.filter(i => i.correct);\n    return correctInteractions.length / Math.max(1, interactions.length) * 100;\n  }\n\n  calculateSecondaryAccuracy(interactions) {\n    return this.calculatePrimaryScore(interactions);\n  }\n\n  calculateEfficiency(interactions) {\n    const totalTime = interactions.reduce((sum, i) => sum + (i.responseTime || 0), 0);\n    const correctCount = interactions.filter(i => i.correct).length;\n    return totalTime > 0 ? (correctCount / totalTime) * 1000 : 0;\n  }\n\n  identifyPatternTypes(interactions) {\n    const types = {};\n    interactions.forEach(i => {\n      const type = i.patternType || 'unknown';\n      types[type] = (types[type] || 0) + 1;\n    });\n    return types;\n  }\n\n  identifyErrorPatterns(interactions) {\n    const errors = interactions.filter(i => !i.correct);\n    return {\n      totalErrors: errors.length,\n      errorFrequency: errors.length / Math.max(1, interactions.length) * 100\n    };\n  }\n\n  calculateVariability(values) {\n    if (values.length < 2) return 0;\n    const mean = values.reduce((sum, v) => sum + v, 0) / values.length;\n    const variance = values.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / values.length;\n    return Math.sqrt(variance);\n  }\n\n  // Métodos específicos para processQuebraCabecaMetrics\n  calculateSpatialReasoning(attempts, pieces) {\n    const correctPlacements = attempts.filter(a => a.correct).length;\n    const totalAttempts = attempts.length || 1;\n    const accuracy = correctPlacements / totalAttempts;\n\n    // Análise da complexidade espacial\n    const complexityScore = pieces.length > 0 ? Math.min(pieces.length / 20, 1) : 0.5;\n    const spatialScore = (accuracy * 0.7) + (complexityScore * 0.3);\n\n    return {\n      score: Math.round(spatialScore * 100),\n      accuracy,\n      complexity: complexityScore,\n      level: spatialScore > 0.8 ? 'advanced' : spatialScore > 0.6 ? 'intermediate' : 'basic'\n    };\n  }\n\n  calculateProblemSolving(attempts, completed) {\n    const totalAttempts = attempts.length || 1;\n    const efficiency = completed ? Math.max(0, 1 - (totalAttempts - 1) / 10) : 0;\n    const completionBonus = completed ? 0.3 : 0;\n\n    const problemSolvingScore = (efficiency * 0.7) + completionBonus;\n\n    return {\n      score: Math.round(problemSolvingScore * 100),\n      efficiency,\n      completed,\n      strategy: totalAttempts < 5 ? 'efficient' : totalAttempts < 10 ? 'methodical' : 'exploratory'\n    };\n  }\n\n  calculatePersistence(attempts, totalTime) {\n    const timePerAttempt = totalTime > 0 ? totalTime / attempts.length : 0;\n    const persistenceIndicator = attempts.length > 5 ? 1 : attempts.length / 5;\n    const timeConsistency = timePerAttempt > 1000 ? 1 : timePerAttempt / 1000;\n\n    const persistenceScore = (persistenceIndicator * 0.6) + (timeConsistency * 0.4);\n\n    return {\n      score: Math.round(persistenceScore * 100),\n      attempts: attempts.length,\n      timePerAttempt,\n      level: persistenceScore > 0.7 ? 'high' : persistenceScore > 0.4 ? 'moderate' : 'low'\n    };\n  }\n\n  calculateVisualProcessing(attempts, pieces) {\n    const visualComplexity = pieces.length > 0 ? pieces.length / 50 : 0.5;\n    const processingAccuracy = attempts.length > 0 ?\n      attempts.filter(a => a.correct).length / attempts.length : 0;\n\n    const visualScore = (processingAccuracy * 0.8) + (visualComplexity * 0.2);\n\n    return {\n      score: Math.round(visualScore * 100),\n      accuracy: processingAccuracy,\n      complexity: visualComplexity,\n      processing: visualScore > 0.7 ? 'efficient' : 'developing'\n    };\n  }\n\n  calculateMotorSkills(attempts) {\n    const avgResponseTime = attempts.length > 0 ?\n      attempts.reduce((sum, a) => sum + (a.responseTime || 2000), 0) / attempts.length : 2000;\n\n    const motorEfficiency = Math.max(0, 1 - (avgResponseTime - 1000) / 5000);\n    const precision = attempts.length > 0 ?\n      attempts.filter(a => a.correct).length / attempts.length : 0;\n\n    const motorScore = (motorEfficiency * 0.6) + (precision * 0.4);\n\n    return {\n      score: Math.round(motorScore * 100),\n      responseTime: avgResponseTime,\n      precision,\n      level: motorScore > 0.7 ? 'refined' : motorScore > 0.4 ? 'developing' : 'emerging'\n    };\n  }\n\n\n\n  identifyStrategy(attempts) {\n    if (attempts.length < 3) return 'insufficient_data';\n\n    const errorRate = attempts.filter(a => !a.correct).length / attempts.length;\n    const avgTime = attempts.reduce((sum, a) => sum + (a.responseTime || 2000), 0) / attempts.length;\n\n    if (errorRate < 0.2 && avgTime < 3000) return 'systematic';\n    if (errorRate < 0.4 && avgTime > 4000) return 'careful';\n    if (errorRate > 0.6) return 'trial_and_error';\n    return 'adaptive';\n  }\n\n  assessDifficultyHandling(attempts, pieces) {\n    const complexity = pieces.length || 10;\n    const performance = attempts.length > 0 ?\n      attempts.filter(a => a.correct).length / attempts.length : 0;\n\n    const difficultyScore = performance * (1 + complexity / 50);\n\n    return {\n      score: Math.round(Math.min(100, difficultyScore * 100)),\n      complexity,\n      performance,\n      handling: difficultyScore > 0.8 ? 'excellent' : difficultyScore > 0.6 ? 'good' : 'needs_support'\n    };\n  }\n\n\n}\n\nexport default QuebraCabecaProcessors;\n", "// ✅ CONFIGURAÇÃO V3 - QUEBRA-CABEÇA COM 6 ATIVIDADES TERAPÊUTICAS\r\nexport const QuebraCabecaV3Config = {\r\n  // 🎯 CONFIGURAÇÃO DAS 6 ATIVIDADES V3\r\n  ACTIVITY_CONFIG: {\r\n    FREE_ASSEMBLY: {\r\n      id: 'free_assembly',\r\n      name: '<PERSON><PERSON><PERSON> Liv<PERSON>',\r\n      icon: '🧩',\r\n      description: 'Monte quebra-cabeças emocionais livremente',\r\n      therapeuticFocus: ['motor_coordination', 'spatial_planning', 'problem_solving'],\r\n      cognitiveAreas: ['spatial_processing', 'motor_skills', 'executive_function'],\r\n      difficulties: {\r\n        easy: { \r\n          pieces: 3, \r\n          gridSize: '1fr 1fr 1fr',\r\n          timeLimit: 300000,\r\n          helpLevel: 'high',\r\n          distractors: 2\r\n        },\r\n        medium: { \r\n          pieces: 6, \r\n          gridSize: '1fr 1fr 1fr',\r\n          timeLimit: 450000,\r\n          helpLevel: 'medium',\r\n          distractors: 4\r\n        },\r\n        hard: { \r\n          pieces: 9, \r\n          gridSize: '1fr 1fr 1fr',\r\n          timeLimit: 600000,\r\n          helpLevel: 'low',\r\n          distractors: 6\r\n        }\r\n      },\r\n      metrics: ['completion_time', 'piece_placement_accuracy', 'strategy_efficiency', 'error_patterns']\r\n    },\r\n\r\n    GUIDED_ASSEMBLY: {\r\n      id: 'guided_assembly',\r\n      name: '<PERSON><PERSON><PERSON>',\r\n      icon: '🎯',\r\n      description: 'Siga instruções específicas para montar',\r\n      therapeuticFocus: ['instruction_following', 'working_memory', 'sequential_processing'],\r\n      cognitiveAreas: ['auditory_processing', 'memory', 'attention'],\r\n      difficulties: {\r\n        easy: { \r\n          sequenceLength: 3,\r\n          guidanceType: 'visual',\r\n          pauseBetweenSteps: 3000,\r\n          allowRepeats: true\r\n        },\r\n        medium: { \r\n          sequenceLength: 5,\r\n          guidanceType: 'visual_audio',\r\n          pauseBetweenSteps: 2000,\r\n          allowRepeats: true\r\n        },\r\n        hard: { \r\n          sequenceLength: 7,\r\n          guidanceType: 'audio_only',\r\n          pauseBetweenSteps: 1500,\r\n          allowRepeats: false\r\n        }\r\n      },\r\n      metrics: ['instruction_adherence', 'sequence_memory', 'guidance_dependency', 'completion_accuracy']\r\n    },\r\n\r\n    ROTATION_RECONSTRUCTION: {\r\n      id: 'rotation_reconstruction',\r\n      name: 'Reconstrução por Rotação',\r\n      icon: '🔄',\r\n      description: 'Monte peças que aparecem rotacionadas',\r\n      therapeuticFocus: ['spatial_transformation', 'mental_rotation', 'visual_processing'],\r\n      cognitiveAreas: ['spatial_reasoning', 'visual_perception', 'cognitive_flexibility'],\r\n      difficulties: {\r\n        easy: { \r\n          rotationAngles: [90, 180],\r\n          pieceCount: 4,\r\n          rotationPreview: true,\r\n          timePerPiece: 15000\r\n        },\r\n        medium: { \r\n          rotationAngles: [90, 180, 270],\r\n          pieceCount: 6,\r\n          rotationPreview: false,\r\n          timePerPiece: 12000\r\n        },\r\n        hard: { \r\n          rotationAngles: [45, 90, 135, 180, 225, 270, 315],\r\n          pieceCount: 8,\r\n          rotationPreview: false,\r\n          timePerPiece: 10000\r\n        }\r\n      },\r\n      metrics: ['rotation_accuracy', 'mental_rotation_speed', 'spatial_transformation_ability', 'error_recovery']\r\n    },\r\n\r\n    PIECE_CLASSIFICATION: {\r\n      id: 'piece_classification',\r\n      name: 'Classificação de Peças',\r\n      icon: '🎨',\r\n      description: 'Organize peças por categorias',\r\n      therapeuticFocus: ['categorization', 'visual_discrimination', 'organizational_skills'],\r\n      cognitiveAreas: ['executive_function', 'visual_processing', 'conceptual_thinking'],\r\n      difficulties: {\r\n        easy: { \r\n          categories: 2,\r\n          piecesPerCategory: 3,\r\n          categoryHints: true,\r\n          sortingCriteria: ['color', 'emotion']\r\n        },\r\n        medium: { \r\n          categories: 3,\r\n          piecesPerCategory: 4,\r\n          categoryHints: false,\r\n          sortingCriteria: ['emotion', 'context', 'symbol']\r\n        },\r\n        hard: { \r\n          categories: 4,\r\n          piecesPerCategory: 5,\r\n          categoryHints: false,\r\n          sortingCriteria: ['emotion', 'context', 'symbol', 'theme']\r\n        }\r\n      },\r\n      metrics: ['classification_accuracy', 'sorting_strategy', 'category_consistency', 'processing_speed']\r\n    },\r\n\r\n    PATTERN_IDENTIFICATION: {\r\n      id: 'pattern_identification',\r\n      name: 'Identificação de Padrões',\r\n      icon: '🔍',\r\n      description: 'Identifique padrões nos quebra-cabeças',\r\n      therapeuticFocus: ['pattern_recognition', 'logical_reasoning', 'predictive_thinking'],\r\n      cognitiveAreas: ['pattern_processing', 'logical_thinking', 'predictive_analysis'],\r\n      difficulties: {\r\n        easy: { \r\n          patternLength: 4,\r\n          patternTypes: ['alternating', 'sequential'],\r\n          hintLevel: 'high',\r\n          completionOptions: 2\r\n        },\r\n        medium: { \r\n          patternLength: 6,\r\n          patternTypes: ['alternating', 'sequential', 'progressive'],\r\n          hintLevel: 'medium',\r\n          completionOptions: 3\r\n        },\r\n        hard: { \r\n          patternLength: 8,\r\n          patternTypes: ['alternating', 'sequential', 'progressive', 'complex'],\r\n          hintLevel: 'low',\r\n          completionOptions: 4\r\n        }\r\n      },\r\n      metrics: ['pattern_recognition_speed', 'logical_accuracy', 'prediction_success', 'learning_progression']\r\n    },\r\n\r\n    COLLABORATIVE_SOLVING: {\r\n      id: 'collaborative_solving',\r\n      name: 'Resolução Colaborativa',\r\n      icon: '🧠',\r\n      description: 'Resolva em equipe com outros jogadores',\r\n      therapeuticFocus: ['social_cooperation', 'communication', 'shared_problem_solving'],\r\n      cognitiveAreas: ['social_cognition', 'communication_skills', 'collaborative_thinking'],\r\n      difficulties: {\r\n        easy: { \r\n          teamSize: 2,\r\n          communicationLevel: 'high',\r\n          sharedPieces: 4,\r\n          coordinationRequired: 'low'\r\n        },\r\n        medium: { \r\n          teamSize: 3,\r\n          communicationLevel: 'medium',\r\n          sharedPieces: 6,\r\n          coordinationRequired: 'medium'\r\n        },\r\n        hard: { \r\n          teamSize: 4,\r\n          communicationLevel: 'minimal',\r\n          sharedPieces: 8,\r\n          coordinationRequired: 'high'\r\n        }\r\n      },\r\n      metrics: ['cooperation_index', 'communication_effectiveness', 'shared_goal_achievement', 'leadership_emergence']\r\n    }\r\n  },\r\n\r\n  // 🎭 BIBLIOTECA EXPANDIDA DE EMOÇÕES V3\r\n  EMOTIONS_LIBRARY: {\r\n    basic: [\r\n      {\r\n        id: 'happy',\r\n        name: 'Feliz',\r\n        emoji: '😊',\r\n        pieces: ['😊', '🌞', '🎁', '🎉'],\r\n        context: 'Ganhar um presente',\r\n        color: '#FFD93D',\r\n        complexity: 'basic',\r\n        therapeuticValue: 'positive_emotion_recognition'\r\n      },\r\n      {\r\n        id: 'sad',\r\n        name: 'Triste',\r\n        emoji: '😢',\r\n        pieces: ['😢', '🌧️', '💔', '😔'],\r\n        context: 'Perder um brinquedo',\r\n        color: '#6BB6FF',\r\n        complexity: 'basic',\r\n        therapeuticValue: 'emotional_regulation'\r\n      },\r\n      {\r\n        id: 'surprised',\r\n        name: 'Surpreso',\r\n        emoji: '😲',\r\n        pieces: ['😲', '🎉', '❓', '✨'],\r\n        context: 'Ver algo inesperado',\r\n        color: '#FF6B6B',\r\n        complexity: 'basic',\r\n        therapeuticValue: 'reaction_processing'\r\n      },\r\n      {\r\n        id: 'angry',\r\n        name: 'Bravo',\r\n        emoji: '😠',\r\n        pieces: ['😠', '💥', '🌋', '⚡'],\r\n        context: 'Quando algo não sai como esperado',\r\n        color: '#FF8B94',\r\n        complexity: 'basic',\r\n        therapeuticValue: 'anger_management'\r\n      },\r\n      {\r\n        id: 'calm',\r\n        name: 'Calmo',\r\n        emoji: '😌',\r\n        pieces: ['😌', '🌊', '🕊️', '🌿'],\r\n        context: 'Relaxar na natureza',\r\n        color: '#4ECDC4',\r\n        complexity: 'basic',\r\n        therapeuticValue: 'self_regulation'\r\n      },\r\n      {\r\n        id: 'excited',\r\n        name: 'Animado',\r\n        emoji: '🤩',\r\n        pieces: ['🤩', '🎪', '🎢', '🎊'],\r\n        context: 'Ir ao parque de diversões',\r\n        color: '#A8E6CF',\r\n        complexity: 'basic',\r\n        therapeuticValue: 'positive_anticipation'\r\n      }\r\n    ],\r\n\r\n    intermediate: [\r\n      {\r\n        id: 'confused',\r\n        name: 'Confuso',\r\n        emoji: '😕',\r\n        pieces: ['😕', '❓', '🤔', '💭'],\r\n        context: 'Não entender algo',\r\n        color: '#F4A261',\r\n        complexity: 'intermediate',\r\n        therapeuticValue: 'uncertainty_tolerance'\r\n      },\r\n      {\r\n        id: 'proud',\r\n        name: 'Orgulhoso',\r\n        emoji: '😤',\r\n        pieces: ['😤', '🏆', '⭐', '👑'],\r\n        context: 'Conseguir algo difícil',\r\n        color: '#E76F51',\r\n        complexity: 'intermediate',\r\n        therapeuticValue: 'self_esteem'\r\n      },\r\n      {\r\n        id: 'worried',\r\n        name: 'Preocupado',\r\n        emoji: '😰',\r\n        pieces: ['😰', '☁️', '⚠️', '💭'],\r\n        context: 'Pensar em problemas',\r\n        color: '#264653',\r\n        complexity: 'intermediate',\r\n        therapeuticValue: 'anxiety_awareness'\r\n      },\r\n      {\r\n        id: 'love',\r\n        name: 'Amor',\r\n        emoji: '🥰',\r\n        pieces: ['🥰', '💖', '🌹', '✨'],\r\n        context: 'Estar com pessoas queridas',\r\n        color: '#E9C46A',\r\n        complexity: 'intermediate',\r\n        therapeuticValue: 'attachment_recognition'\r\n      }\r\n    ],\r\n\r\n    advanced: [\r\n      {\r\n        id: 'jealous',\r\n        name: 'Ciúmes',\r\n        emoji: '😒',\r\n        pieces: ['😒', '👀', '💚', '⚡'],\r\n        context: 'Ver outros com algo que queremos',\r\n        color: '#2A9D8F',\r\n        complexity: 'advanced',\r\n        therapeuticValue: 'complex_emotion_understanding'\r\n      },\r\n      {\r\n        id: 'embarrassed',\r\n        name: 'Envergonhado',\r\n        emoji: '😳',\r\n        pieces: ['😳', '🔥', '👥', '🙈'],\r\n        context: 'Fazer algo constrangedor',\r\n        color: '#F4A261',\r\n        complexity: 'advanced',\r\n        therapeuticValue: 'social_awareness'\r\n      },\r\n      {\r\n        id: 'disappointed',\r\n        name: 'Desapontado',\r\n        emoji: '😞',\r\n        pieces: ['😞', '💔', '😔', '🌧️'],\r\n        context: 'Expectativas não atendidas',\r\n        color: '#264653',\r\n        complexity: 'advanced',\r\n        therapeuticValue: 'expectation_management'\r\n      }\r\n    ]\r\n  },\r\n\r\n  // ⚙️ CONFIGURAÇÕES DE DIFICULDADE V3\r\n  DIFFICULTY_CONFIGS: {\r\n    EASY: {\r\n      name: 'Fácil',\r\n      description: 'Ideal para iniciantes - Mais ajuda e tempo',\r\n      color: '#4CAF50',\r\n      icon: '🟢',\r\n      emotionComplexity: 'basic',\r\n      roundsPerActivity: 2,\r\n      timeMultiplier: 1.5,\r\n      helpLevel: 'high',\r\n      feedbackFrequency: 'high'\r\n    },\r\n    MEDIUM: {\r\n      name: 'Médio',\r\n      description: 'Desafio equilibrado - Ajuda moderada',\r\n      color: '#FF9800',\r\n      icon: '🟡',\r\n      emotionComplexity: 'intermediate',\r\n      roundsPerActivity: 3,\r\n      timeMultiplier: 1.0,\r\n      helpLevel: 'medium',\r\n      feedbackFrequency: 'medium'\r\n    },\r\n    HARD: {\r\n      name: 'Difícil',\r\n      description: 'Para especialistas - Mínima assistência',\r\n      color: '#F44336',\r\n      icon: '🔴',\r\n      emotionComplexity: 'advanced',\r\n      roundsPerActivity: 4,\r\n      timeMultiplier: 0.8,\r\n      helpLevel: 'low',\r\n      feedbackFrequency: 'low'\r\n    }\r\n  },\r\n\r\n  // 🔄 SISTEMA DE ROTAÇÃO DE ATIVIDADES V3\r\n  ACTIVITY_ROTATION: {\r\n    defaultCycle: [\r\n      'free_assembly',\r\n      'guided_assembly', \r\n      'rotation_reconstruction',\r\n      'piece_classification',\r\n      'pattern_identification',\r\n      'collaborative_solving'\r\n    ],\r\n    adaptiveCycle: true, // Ajusta baseado na performance\r\n    rotationTriggers: {\r\n      roundsCompleted: 3,\r\n      timeElapsed: 300000, // 5 minutos\r\n      accuracyThreshold: 80,\r\n      engagementDrop: 20\r\n    }\r\n  },\r\n\r\n  // 📊 MÉTRICAS TERAPÊUTICAS V3\r\n  THERAPEUTIC_METRICS: {\r\n    spatial_reasoning: {\r\n      components: ['mental_rotation', 'spatial_memory', 'spatial_transformation'],\r\n      weights: { mental_rotation: 0.4, spatial_memory: 0.3, spatial_transformation: 0.3 },\r\n      normalization: 'age_based'\r\n    },\r\n    problem_solving: {\r\n      components: ['strategy_formation', 'error_correction', 'persistence'],\r\n      weights: { strategy_formation: 0.4, error_correction: 0.3, persistence: 0.3 },\r\n      normalization: 'adaptive'\r\n    },\r\n    motor_coordination: {\r\n      components: ['fine_motor', 'hand_eye_coordination', 'movement_precision'],\r\n      weights: { fine_motor: 0.4, hand_eye_coordination: 0.3, movement_precision: 0.3 },\r\n      normalization: 'developmental'\r\n    },\r\n    emotional_recognition: {\r\n      components: ['emotion_identification', 'context_understanding', 'expression_matching'],\r\n      weights: { emotion_identification: 0.4, context_understanding: 0.3, expression_matching: 0.3 },\r\n      normalization: 'clinical'\r\n    },\r\n    social_skills: {\r\n      components: ['cooperation', 'communication', 'leadership'],\r\n      weights: { cooperation: 0.4, communication: 0.3, leadership: 0.3 },\r\n      normalization: 'social_developmental'\r\n    },\r\n    attention_control: {\r\n      components: ['sustained_attention', 'selective_attention', 'attention_switching'],\r\n      weights: { sustained_attention: 0.4, selective_attention: 0.3, attention_switching: 0.3 },\r\n      normalization: 'clinical'\r\n    }\r\n  },\r\n\r\n  // 🎯 CONFIGURAÇÕES DO JOGO V3\r\n  GAME_SETTINGS: {\r\n    pointsByDifficulty: {\r\n      EASY: { base: 10, bonus: 5, time_bonus: 3 },\r\n      MEDIUM: { base: 15, bonus: 8, time_bonus: 5 },\r\n      HARD: { base: 20, bonus: 12, time_bonus: 8 }\r\n    },\r\n    completionFeedbackDuration: 3000,\r\n    nextPuzzleDelay: 1000,\r\n    autoSaveInterval: 30000,\r\n    sessionTimeLimit: 1800000, // 30 minutos\r\n    adaptiveDifficultyEnabled: true,\r\n    multisensoryFeedback: true,\r\n    accessibilityMode: false\r\n  },\r\n\r\n  // 🎨 ELEMENTOS VISUAIS V3\r\n  VISUAL_ELEMENTS: {\r\n    animations: {\r\n      pieceMovement: 'smooth',\r\n      feedbackDuration: 2000,\r\n      transitionSpeed: 300,\r\n      successAnimation: 'bounce',\r\n      errorAnimation: 'shake'\r\n    },\r\n    themes: {\r\n      default: 'modern_glassmorphism',\r\n      highContrast: 'accessibility_focused',\r\n      childFriendly: 'colorful_rounded'\r\n    },\r\n    sounds: {\r\n      piecePlace: 'soft_click',\r\n      success: 'cheerful_chime',\r\n      error: 'gentle_buzz',\r\n      completion: 'victory_fanfare'\r\n    }\r\n  },\r\n\r\n  // 📱 INFORMAÇÕES DO JOGO V3\r\n  GAME_INFO: {\r\n    title: 'Quebra-Cabeça V3',\r\n    subtitle: 'Sistema Completo de 6 Atividades Terapêuticas',\r\n    description: 'Desenvolva habilidades espaciais, emocionais e sociais através de quebra-cabeças especializados',\r\n    icon: '🧩',\r\n    category: 'spatial_emotional_development',\r\n    version: '3.0.0',\r\n    ageRange: '4-12',\r\n    therapeuticApplications: [\r\n      'Desenvolvimento espacial',\r\n      'Reconhecimento emocional', \r\n      'Coordenação motora fina',\r\n      'Resolução de problemas',\r\n      'Habilidades sociais',\r\n      'Controle executivo'\r\n    ],\r\n    clinicalValidation: true,\r\n    accessibilityCompliant: true\r\n  }\r\n};\r\n\r\n// ✅ COMPATIBILIDADE COM VERSÃO ANTERIOR\r\nexport const QuebraCabecaConfig = {\r\n  // Manter referências da versão anterior para compatibilidade\r\n  emotions: QuebraCabecaV3Config.EMOTIONS_LIBRARY.basic,\r\n  difficulties: [\r\n    { id: 'easy', name: 'Fácil', pieces: 3, gridSize: 3 },\r\n    { id: 'medium', name: 'Médio', pieces: 6, gridSize: 4 },\r\n    { id: 'hard', name: 'Difícil', pieces: 9, gridSize: 4 }\r\n  ],\r\n  encouragingMessages: [\r\n    'Muito bem! Você reconheceu a emoção! 🌟',\r\n    'Excelente! Continue assim! 🎉',\r\n    'Você está ótimo em montar emoções! 😊',\r\n    'Perfeito! Sua paciência é incrível! ✨',\r\n    'Fantástico! Você entende as emoções! 🧠'\r\n  ],\r\n  gameSettings: QuebraCabecaV3Config.GAME_SETTINGS,\r\n  gameInfo: QuebraCabecaV3Config.GAME_INFO\r\n};\r\n", "/**\r\n * 🧩 QUEBRA-CABEÇA V3 - JOGO DE QUEBRA-CABEÇA COM MÚLTIPLAS ATIVIDADES\r\n * Portal Betina V3 - Jogo educativo com 6 atividades diversificadas\r\n */\r\n\r\nimport React, { useState, useEffect, useContext, useCallback, useRef } from 'react'\r\nimport { QuebraCabecaConfig, QuebraCabecaV3Config } from './QuebraCabecaConfig'\r\nimport { QuebraCabecaMetrics } from './QuebraCabecaMetrics'\r\nimport { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic'\r\nimport { useAccessibilityContext } from '../../components/context/AccessibilityContext'\r\nimport { SystemContext } from '../../components/context/SystemContext.jsx'\r\nimport { useGameAudio } from '../../games/shared/GameUtils'\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\n// Importa o componente padrão de tela de dificuldade\r\nimport GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';\r\n\r\n// 🧩 Importar coletores avançados V3\r\nimport { QuebraCabecaCollectorsHub } from './collectors/index.js'\r\n// 🔄 Importar hook multissensorial\r\nimport { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';\r\n// 🎯 Importar hook orquestrador terapêutico\r\nimport { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';\r\n\r\n// 🎨 Importar estilos V3\r\nimport styles from './QuebraCabeca.module.css';\r\n\r\n// 🎯 SISTEMA DE ATIVIDADES REDESENHADO V3 - QUEBRA-CABEÇA EMOCIONAL\r\n// Cada atividade testa diferentes funções cognitivas com layouts únicos\r\nconst ACTIVITY_TYPES = {\r\n  EMOTION_PUZZLE: {\r\n    id: 'emotion_puzzle',\r\n    name: 'Emocional',\r\n    icon: '🎯',\r\n    description: 'Teste de coordenação motora e montagem de emoções',\r\n    cognitiveFunction: 'motor_coordination_emotion_assembly',\r\n    component: 'EmotionPuzzleActivity'\r\n  },\r\n  PIECE_ROTATION: {\r\n    id: 'piece_rotation',\r\n    name: 'Rotação',\r\n    icon: '🔄',\r\n    description: 'Teste de rotação mental e orientação espacial',\r\n    cognitiveFunction: 'spatial_rotation_mental_processing',\r\n    component: 'PieceRotationActivity'\r\n  },\r\n  PATTERN_PUZZLE: {\r\n    id: 'pattern_puzzle',\r\n    name: 'Padrões',\r\n    icon: '🔍',\r\n    description: 'Teste de reconhecimento de padrões em quebra-cabeças',\r\n    cognitiveFunction: 'pattern_recognition_spatial',\r\n    component: 'PatternPuzzleActivity'\r\n  },\r\n\r\n};\r\n\r\nfunction QuebraCabecaGame({ onBack }) {\r\n  const { user, ttsEnabled = true } = useContext(SystemContext);\r\n  const { settings } = useAccessibilityContext();\r\n  const { playSound } = useGameAudio();\r\n\r\n  // Referência para métricas\r\n  const metricsRef = useRef(null);\r\n\r\n  // 🎯 ESTADO EXPANDIDO COM SISTEMA DE ATIVIDADES V3\r\n  const [gameState, setGameState] = useState({\r\n    status: 'start', // 'start', 'playing', 'paused', 'finished'\r\n    score: 0,\r\n    round: 1,\r\n    level: 1,\r\n    totalRounds: 10,\r\n    difficulty: 'EASY',\r\n    accuracy: 100,\r\n    roundStartTime: null,\r\n\r\n    // 🎯 Sistema de atividades redesenhado (3 atividades principais)\r\n    currentActivity: ACTIVITY_TYPES.EMOTION_PUZZLE.id,\r\n    activityCycle: [\r\n      ACTIVITY_TYPES.EMOTION_PUZZLE.id,\r\n      ACTIVITY_TYPES.PIECE_ROTATION.id,\r\n      ACTIVITY_TYPES.PATTERN_PUZZLE.id\r\n    ],\r\n    activityIndex: 0,\r\n    roundsPerActivity: 5, // Mínimo 5 rodadas por atividade\r\n    activityRoundCount: 0,\r\n    activitiesCompleted: [],\r\n\r\n    // 🎯 Dados específicos de atividades\r\n    activityData: {\r\n      freeAssembly: {\r\n        puzzlePieces: [],\r\n        placedPieces: [],\r\n        completedPuzzles: 0\r\n      },\r\n      guidedAssembly: {\r\n        currentHint: null,\r\n        hintsUsed: 0,\r\n        guidanceLevel: 'basic'\r\n      },\r\n      patternMatching: {\r\n        patterns: [],\r\n        matchedPatterns: [],\r\n        currentPattern: null\r\n      },\r\n      shapeSorting: {\r\n        shapes: [],\r\n        sortedShapes: {},\r\n        categories: []\r\n      },\r\n      timedChallenge: {\r\n        timeLimit: 60,\r\n        timeRemaining: 60,\r\n        isTimerActive: false\r\n      },\r\n      creativeBuilding: {\r\n        availablePieces: [],\r\n        userCreation: [],\r\n        savedCreations: []\r\n      }\r\n    },\r\n\r\n    // 🎯 Feedback e animações\r\n    showFeedback: false,\r\n    feedbackType: null,\r\n    feedbackMessage: '',\r\n    showCelebration: false,\r\n\r\n    // 🎯 Métricas comportamentais\r\n    responseTime: 0,\r\n    hesitationCount: 0,\r\n    helpUsed: false,\r\n    consecutiveCorrect: 0,\r\n    totalAttempts: 0,\r\n    correctAttempts: 0,\r\n    \r\n    // Peças e controle do jogo\r\n    availablePieces: [],\r\n    draggedPiece: null,\r\n    isComplete: false,\r\n    \r\n    // Feedback e interação\r\n    feedback: null,\r\n    \r\n    // Estatísticas gerais\r\n    gameStats: {\r\n      score: 0,\r\n      completed: 0,\r\n      totalAttempts: 0,\r\n      accuracy: 100,\r\n      sessionStartTime: null,\r\n      roundStartTime: null\r\n    },\r\n    \r\n    // 🔥 Configurações especiais\r\n    specialConfig: {\r\n      mentalRotationTime: [],\r\n      spatialErrors: [],\r\n      pieceClassification: {\r\n        categoryAccuracy: {},\r\n        sortingStrategy: 'color_first',\r\n        classificationTime: []\r\n      },\r\n      patternIdentification: {\r\n        patternRecognitionSpeed: [],\r\n        logicalAccuracy: [],\r\n        predictionSuccess: []\r\n      },\r\n      collaborativeSolving: {\r\n        cooperationIndex: 0,\r\n        communicationTurns: 0,\r\n        leadershipEvents: []\r\n      }\r\n    },\r\n    \r\n    // Métricas comportamentais V3\r\n    behavioralMetrics: {\r\n      reactionTime: [],\r\n      accuracy: [],\r\n      attentionSpan: 0,\r\n      frustrationEvents: [],\r\n      persistenceLevel: 0,\r\n      engagementScore: 100,\r\n      multisensoryProcessing: {},\r\n      activitySpecific: {}\r\n    }\r\n  });\r\n\r\n  // ✅ REFS PARA CONTROLE DE SESSÃO\r\n  const sessionIdRef = useRef(null);\r\n  const roundStartTimeRef = useRef(null);\r\n  \r\n  // 🧠 Integração com sistema unificado de métricas\r\n  const { \r\n    collectMetrics, \r\n    processGameSession, \r\n    initializeSession: initUnifiedSession,\r\n    processAdvancedMetrics, // Novo: para AdvancedMetricsEngine\r\n    sessionId,\r\n    isSessionActive\r\n  } = useUnifiedGameLogic('QuebraCabeca')\r\n\r\n  // 🧩 Inicializar coletores avançados V3\r\n  const [collectorsHub] = useState(() => new QuebraCabecaCollectorsHub())\r\n\r\n  // TTS control\r\n  const [ttsActive, setTtsActive] = useState(() => {\r\n    const saved = localStorage.getItem('quebraCabeca_ttsActive');\r\n    return saved ? JSON.parse(saved) : true;\r\n  });\r\n\r\n  // 🔄 Hook multissensorial integrado\r\n  const {\r\n    initializeSession: initMultisensory,\r\n    recordInteraction: recordMultisensoryInteraction,\r\n    finalizeSession: finalizeMultisensory,\r\n    updateData: updateMultisensoryData,\r\n    multisensoryData,\r\n    isInitialized: multisensoryInitialized\r\n  } = useMultisensoryIntegration(sessionId, {\r\n    gameType: 'puzzle-game-v3',\r\n    sensorTypes: {\r\n      visual: true,\r\n      haptic: true,\r\n      tts: ttsEnabled,\r\n      gestural: true,\r\n      biometric: true\r\n    },\r\n    adaptiveMode: true,\r\n    autoUpdate: true,\r\n    enablePatternAnalysis: true,\r\n    logLevel: 'info',\r\n    learningStyle: user?.profile?.learningStyle || 'visual'\r\n  });\r\n\r\n  // 🎯 Hook orquestrador terapêutico integrado\r\n  const therapeuticOrchestrator = useTherapeuticOrchestrator({ \r\n    gameType: 'puzzle-game',\r\n    collectorsHub,\r\n    recordMultisensoryInteraction,\r\n    autoUpdate: true,\r\n    logLevel: 'info'\r\n  });\r\n\r\n  const [cognitiveAnalysisVisible, setCognitiveAnalysisVisible] = useState(false)\r\n  const [gameStarted, setGameStarted] = useState(false)\r\n  const [showStartScreen, setShowStartScreen] = useState(true)\r\n  const [gameStats, setGameStats] = useState({\r\n    level: 1,\r\n    score: 0,\r\n    completed: 0,\r\n    totalAttempts: 0,\r\n    accuracy: 100\r\n  })\r\n  const [difficulty, setDifficulty] = useState('easy')\r\n  const [draggedPiece, setDraggedPiece] = useState(null)\r\n  const [placedPieces, setPlacedPieces] = useState([])\r\n  const [puzzlePieces, setPuzzlePieces] = useState([])\r\n  const [isComplete, setIsComplete] = useState(false)\r\n  const [currentEmotion, setCurrentEmotion] = useState(null)\r\n  const [feedback, setFeedback] = useState(null)\r\n\r\n  // Função TTS padronizada\r\n  const speak = useCallback((text, options = {}) => {\r\n    if (!ttsActive || !('speechSynthesis' in window)) {\r\n      return;\r\n    }\r\n\r\n    window.speechSynthesis.cancel();\r\n\r\n    const utterance = new SpeechSynthesisUtterance(text);\r\n    utterance.lang = 'pt-BR';\r\n    utterance.rate = options.rate || 0.9;\r\n    utterance.pitch = options.pitch || 1;\r\n    utterance.volume = options.volume || 1;\r\n\r\n    window.speechSynthesis.speak(utterance);\r\n  }, [ttsActive]);\r\n\r\n  // ✅ FUNÇÕES V3 - GERAÇÃO DE ATIVIDADES\r\n\r\n  // 🎯 Gerar conteúdo para atividade atual - REDESENHADO\r\n  const generateActivityContent = useCallback((activityId, difficulty) => {\r\n    const config = QuebraCabecaV3Config.DIFFICULTY_CONFIGS[difficulty.toUpperCase()];\r\n    const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[activityId.toUpperCase()];\r\n\r\n    switch (activityId) {\r\n      case 'emotion_puzzle':\r\n        return generateEmotionPuzzleActivity(config, activityConfig);\r\n      case 'piece_rotation':\r\n        return generatePieceRotationActivity(config, activityConfig);\r\n      case 'pattern_puzzle':\r\n        return generatePatternPuzzleActivity(config, activityConfig);\r\n      default:\r\n        return generateEmotionPuzzleActivity(config, activityConfig);\r\n    }\r\n  }, []);\r\n\r\n  // =====================================================\r\n  // 🎯 FUNÇÕES DE GERAÇÃO REDESENHADAS - QUEBRA-CABEÇA ESPECÍFICO\r\n  // =====================================================\r\n\r\n  // 🧩 QUEBRA-CABEÇA EMOCIONAL - Montagem tradicional de peças emocionais\r\n  const generateEmotionPuzzleActivity = useCallback((difficultyConfig, activityConfig) => {\r\n    console.log('🧩 Generating emotion puzzle activity');\r\n\r\n    const emotionLibrary = QuebraCabecaV3Config.EMOTIONS_LIBRARY[difficultyConfig?.emotionComplexity || 'basic'];\r\n    const randomEmotion = emotionLibrary[Math.floor(Math.random() * emotionLibrary.length)];\r\n\r\n    const pieceCounts = {\r\n      EASY: 4,\r\n      MEDIUM: 6,\r\n      HARD: 9\r\n    };\r\n\r\n    const difficultyKey = gameState.difficulty || 'EASY';\r\n    const pieceCount = pieceCounts[difficultyKey] || 4;\r\n    const correctPieces = randomEmotion.pieces.slice(0, pieceCount);\r\n    const distractorPieces = generateDistractorPieces(correctPieces, 2);\r\n\r\n    return {\r\n      emotion: randomEmotion,\r\n      correctPieces,\r\n      availablePieces: [...correctPieces, ...distractorPieces].sort(() => Math.random() - 0.5),\r\n      targetSlots: pieceCount,\r\n      instruction: `Monte o quebra-cabeça da emoção \"${randomEmotion.name}\"`,\r\n      level: gameState.difficulty,\r\n      activityType: 'emotion_puzzle',\r\n      gridLayout: Math.ceil(Math.sqrt(pieceCount))\r\n    };\r\n  }, [gameState.difficulty]);\r\n\r\n  // 🔄 ROTAÇÃO DE PEÇAS - Rotação mental e orientação espacial\r\n  const generatePieceRotationActivity = useCallback((difficultyConfig, activityConfig) => {\r\n    console.log('🔄 Generating piece rotation activity');\r\n\r\n    const emotionLibrary = QuebraCabecaV3Config.EMOTIONS_LIBRARY[difficultyConfig?.emotionComplexity || 'basic'];\r\n    const randomEmotion = emotionLibrary[Math.floor(Math.random() * emotionLibrary.length)];\r\n\r\n    const rotationAngles = {\r\n      EASY: [90, 180],\r\n      MEDIUM: [90, 180, 270],\r\n      HARD: [45, 90, 135, 180, 225, 270, 315]\r\n    };\r\n\r\n    const pieces = randomEmotion.pieces.slice(0, 4);\r\n    const difficultyKey = gameState.difficulty || 'EASY';\r\n    const availableAngles = rotationAngles[difficultyKey] || rotationAngles.EASY;\r\n\r\n    const rotatedPieces = pieces.map((piece, index) => ({\r\n      id: index,\r\n      original: piece,\r\n      angle: availableAngles[Math.floor(Math.random() * availableAngles.length)],\r\n      placed: false,\r\n      correctPosition: index\r\n    }));\r\n\r\n    return {\r\n      emotion: randomEmotion,\r\n      rotatedPieces,\r\n      instruction: `Rotacione mentalmente as peças e monte \"${randomEmotion.name}\"`,\r\n      level: gameState.difficulty,\r\n      activityType: 'piece_rotation',\r\n      completedRotations: 0\r\n    };\r\n  }, [gameState.difficulty]);\r\n\r\n  // 🔍 QUEBRA-CABEÇA DE PADRÕES - Reconhecimento de padrões em quebra-cabeças\r\n  const generatePatternPuzzleActivity = useCallback((difficultyConfig, activityConfig) => {\r\n    console.log('🔍 Generating pattern puzzle activity');\r\n\r\n    const patternTypes = {\r\n      EASY: [\r\n        { type: 'alternating', sequence: ['😊', '😢', '😊', '😢'], missing: 2, correct: '😊' },\r\n        { type: 'sequential', sequence: ['😊', '🤩', '😍', '🥰'], missing: 3, correct: '🥰' }\r\n      ],\r\n      MEDIUM: [\r\n        { type: 'progressive', sequence: ['😐', '😊', '😄', '🤩'], missing: 2, correct: '😄' },\r\n        { type: 'emotional_scale', sequence: ['😢', '😔', '😐', '😊'], missing: 1, correct: '😔' }\r\n      ],\r\n      HARD: [\r\n        { type: 'complex_pattern', sequence: ['😊', '😢', '😡', '😊', '😢'], missing: 4, correct: '😡' },\r\n        { type: 'emotional_cycle', sequence: ['😴', '😊', '😰', '😴'], missing: 2, correct: '😰' }\r\n      ]\r\n    };\r\n\r\n    const difficultyKey = gameState.difficulty || 'EASY';\r\n    const levelPatterns = patternTypes[difficultyKey] || patternTypes.EASY;\r\n    const selectedPattern = levelPatterns[Math.floor(Math.random() * levelPatterns.length)];\r\n\r\n    // Criar opções de resposta\r\n    const wrongOptions = ['😲', '🤔', '😏', '🥳'].filter(opt => opt !== selectedPattern.correct);\r\n    const options = [selectedPattern.correct, ...wrongOptions.slice(0, 2)].sort(() => Math.random() - 0.5);\r\n\r\n    return {\r\n      pattern: selectedPattern,\r\n      options,\r\n      instruction: `Complete o padrão emocional observando a sequência`,\r\n      level: gameState.difficulty,\r\n      activityType: 'pattern_puzzle',\r\n      userAnswer: null,\r\n      solved: false\r\n    };\r\n  }, [gameState.difficulty]);\r\n\r\n\r\n\r\n  // 🔧 Funções auxiliares de geração\r\n  const generateDistractorPieces = useCallback((correctPieces, count) => {\r\n    const allPieces = ['😊', '😢', '😲', '😠', '😌', '🤩', '🌞', '🌧️', '💔', '🎁', '🎉', '❓', '✨', '🌊', '🕊️', '🌿', '💥', '🌋', '⚡', '🎪', '🎢', '🎊'];\r\n    const distractors = allPieces.filter(piece => !correctPieces.includes(piece));\r\n    return distractors.sort(() => Math.random() - 0.5).slice(0, count);\r\n  }, []);\r\n\r\n  // Funções auxiliares para as novas atividades\r\n  const generateEmotionCategories = useCallback((settings) => {\r\n    const baseCategories = {\r\n      positive: { name: 'Emoções Positivas', icon: '😊', pieces: [] },\r\n      negative: { name: 'Emoções Negativas', icon: '😢', pieces: [] },\r\n      neutral: { name: 'Emoções Neutras', icon: '😐', pieces: [] },\r\n      intense: { name: 'Emoções Intensas', icon: '🤯', pieces: [] }\r\n    };\r\n\r\n    const selectedCategories = Object.keys(baseCategories).slice(0, settings.categories || 2);\r\n    const result = {};\r\n    selectedCategories.forEach(key => {\r\n      result[key] = baseCategories[key];\r\n    });\r\n\r\n    return result;\r\n  }, []);\r\n\r\n  const generateEmotionPiecesForSorting = useCallback((categories, settings) => {\r\n    const categoryKeys = Object.keys(categories);\r\n    const pieces = [];\r\n\r\n    const emotionMap = {\r\n      positive: ['😊', '😄', '🤩', '😍', '🥰', '😌'],\r\n      negative: ['😢', '😭', '😠', '😡', '😰', '😔'],\r\n      neutral: ['😐', '🤔', '😑', '😶', '🙄', '😏'],\r\n      intense: ['🤯', '😱', '🤬', '😵', '🥵', '🥶']\r\n    };\r\n\r\n    categoryKeys.forEach(categoryKey => {\r\n      const categoryPieces = emotionMap[categoryKey] || [];\r\n      const selectedPieces = categoryPieces.slice(0, settings.piecesPerCategory || 3);\r\n      pieces.push(...selectedPieces.map(piece => ({ piece, category: categoryKey, emoji: piece })));\r\n    });\r\n\r\n    return pieces.sort(() => Math.random() - 0.5);\r\n  }, []);\r\n\r\n  const generatePiecesForClassification = useCallback((categories, settings) => {\r\n    const categoryKeys = Object.keys(categories);\r\n    const pieces = [];\r\n    \r\n    categoryKeys.forEach(categoryKey => {\r\n      const categoryPieces = getCategoryPieces(categoryKey);\r\n      const selectedPieces = categoryPieces.slice(0, settings.piecesPerCategory);\r\n      pieces.push(...selectedPieces.map(piece => ({ piece, category: categoryKey })));\r\n    });\r\n    \r\n    return pieces.sort(() => Math.random() - 0.5);\r\n  }, []);\r\n\r\n  const getCategoryPieces = useCallback((category) => {\r\n    const categoryMap = {\r\n      emotions: ['😊', '😢', '😲', '😠', '😌', '🤩'],\r\n      nature: ['🌞', '🌧️', '🌊', '🕊️', '🌿', '🌋'],\r\n      objects: ['🎁', '🎉', '🎪', '🎢', '🎊', '💔'],\r\n      symbols: ['❓', '✨', '💥', '⚡', '🏆', '⭐']\r\n    };\r\n    return categoryMap[category] || [];\r\n  }, []);\r\n\r\n  const generateLogicalPattern = useCallback((settings) => {\r\n    const patternTypes = settings.patternTypes;\r\n    const selectedType = patternTypes[Math.floor(Math.random() * patternTypes.length)];\r\n    \r\n    switch (selectedType) {\r\n      case 'alternating':\r\n        return { type: 'alternating', sequence: ['😊', '😢', '😊', '😢'], next: '😊' };\r\n      case 'sequential':\r\n        return { type: 'sequential', sequence: ['😊', '🌞', '😢', '🌧️'], next: '😲' };\r\n      case 'progressive':\r\n        return { type: 'progressive', sequence: ['😊', '🤩', '😢', '😭'], next: '😡' };\r\n      default:\r\n        return { type: 'alternating', sequence: ['😊', '😢', '😊', '😢'], next: '😊' };\r\n    }\r\n  }, []);\r\n\r\n  const generatePatternOptions = useCallback((pattern, settings) => {\r\n    const correctAnswer = pattern.next;\r\n    const wrongOptions = ['😲', '🤔', '😴', '🥳'].filter(opt => opt !== correctAnswer);\r\n    const selectedWrong = wrongOptions.slice(0, settings.completionOptions - 1);\r\n    \r\n    return [correctAnswer, ...selectedWrong].sort(() => Math.random() - 0.5);\r\n  }, []);\r\n\r\n  // ✅ FUNÇÕES V3 - ROTAÇÃO DE ATIVIDADES\r\n\r\n  // 🔄 Rotacionar para próxima atividade\r\n  const rotateToNextActivity = useCallback(() => {\r\n    setGameState(prev => {\r\n      const nextIndex = (prev.activityIndex + 1) % prev.activityCycle.length;\r\n      const nextActivity = prev.activityCycle[nextIndex];\r\n      \r\n      // Registrar rotação de atividade\r\n      if (recordMultisensoryInteraction) {\r\n        recordMultisensoryInteraction('activity_rotation', {\r\n          from: prev.currentActivity,\r\n          to: nextActivity,\r\n          automatic: true,\r\n          round: prev.round\r\n        });\r\n      }\r\n      \r\n      const newState = {\r\n        ...prev,\r\n        currentActivity: nextActivity,\r\n        activityIndex: nextIndex,\r\n        activityRoundCount: 0,\r\n        round: prev.round + 1,\r\n        roundStartTime: Date.now()\r\n      };\r\n      \r\n      // Gerar novo conteúdo para a atividade\r\n      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);\r\n      \r\n      // Atualizar dados específicos da atividade\r\n      newState.currentEmotion = activityContent.emotion || null;\r\n      newState.puzzlePieces = activityContent.availablePieces || activityContent.pieces || [];\r\n      newState.placedPieces = new Array(activityContent.targetSlots || 3).fill(null);\r\n      \r\n      // Anunciar nova atividade\r\n      const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[nextActivity.toUpperCase()];\r\n      const activityName = activityConfig?.name || 'Nova Atividade';\r\n      \r\n      setTimeout(() => {\r\n        if (speak) {\r\n          speak(`Nova atividade: ${activityName}. ${activityContent.instruction}`, { rate: 0.8 });\r\n        }\r\n      }, 500);\r\n      \r\n      return newState;\r\n    });\r\n  }, [generateActivityContent, recordMultisensoryInteraction, speak]);\r\n\r\n  // 🎯 Trocar atividade manualmente\r\n  const switchActivity = useCallback((activityId) => {\r\n    setGameState(prev => {\r\n      const activityIndex = prev.activityCycle.indexOf(activityId);\r\n      if (activityIndex === -1) return prev;\r\n      \r\n      const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[activityId.toUpperCase()];\r\n      const activityName = activityConfig?.name || 'Nova Atividade';\r\n      \r\n      // Registrar troca manual de atividade\r\n      if (recordMultisensoryInteraction) {\r\n        recordMultisensoryInteraction('activity_switch', {\r\n          from: prev.currentActivity,\r\n          to: activityId,\r\n          manual: true,\r\n          round: prev.round\r\n        });\r\n      }\r\n      \r\n      const newState = {\r\n        ...prev,\r\n        currentActivity: activityId,\r\n        activityIndex: activityIndex,\r\n        activityRoundCount: 0,\r\n        round: prev.round + 1,\r\n        roundStartTime: Date.now()\r\n      };\r\n      \r\n      // Gerar novo conteúdo\r\n      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);\r\n      \r\n      newState.currentEmotion = activityContent.emotion || null;\r\n      newState.puzzlePieces = activityContent.availablePieces || activityContent.pieces || [];\r\n      newState.placedPieces = new Array(activityContent.targetSlots || 3).fill(null);\r\n      \r\n      // Anunciar atividade\r\n      setTimeout(() => {\r\n        if (speak) {\r\n          speak(`Atividade alterada para: ${activityName}. ${activityContent.instruction}`, { rate: 0.8 });\r\n        }\r\n      }, 300);\r\n      \r\n      return newState;\r\n    });\r\n  }, [generateActivityContent, recordMultisensoryInteraction, speak]);\r\n\r\n  // ✅ FUNÇÕES V3 - GERAÇÃO DE NOVA RODADA\r\n  const generateNewRound = useCallback(() => {\r\n    console.log('🎯 Gerando nova rodada V3...', { \r\n      currentActivity: gameState.currentActivity,\r\n      activityRoundCount: gameState.activityRoundCount,\r\n      round: gameState.round \r\n    });\r\n    \r\n    setGameState(prev => {\r\n      // Verificar se precisa rotar atividade\r\n      const shouldRotateActivity = prev.activityRoundCount >= prev.roundsPerActivity;\r\n      \r\n      console.log('🔄 Verificando rotação de atividade:', { \r\n        shouldRotateActivity, \r\n        activityRoundCount: prev.activityRoundCount,\r\n        roundsPerActivity: prev.roundsPerActivity \r\n      });\r\n      \r\n      let newState = { ...prev };\r\n      \r\n      if (shouldRotateActivity) {\r\n        // Rotar para próxima atividade\r\n        const nextActivityIndex = (prev.activityIndex + 1) % prev.activityCycle.length;\r\n        const nextActivity = prev.activityCycle[nextActivityIndex];\r\n        \r\n        console.log('🎮 Rotacionando para nova atividade:', { \r\n          from: prev.currentActivity, \r\n          to: nextActivity,\r\n          nextActivityIndex \r\n        });\r\n        \r\n        newState = {\r\n          ...newState,\r\n          currentActivity: nextActivity,\r\n          activityIndex: nextActivityIndex,\r\n          activityRoundCount: 0,\r\n          activitiesCompleted: prev.activitiesCompleted + 1\r\n        };\r\n        \r\n        // 🔊 Anunciar nova atividade\r\n        const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[nextActivity.toUpperCase()];\r\n        const activityName = activityConfig?.name || 'Nova Atividade';\r\n        setTimeout(() => {\r\n          if (speak) {\r\n            speak(`Nova atividade: ${activityName}`, { pitch: 1.2, rate: 0.8 });\r\n          }\r\n        }, 500);\r\n      }\r\n      \r\n      // Gerar conteúdo específico da atividade\r\n      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);\r\n      \r\n      console.log('📝 Conteúdo gerado para atividade:', { \r\n        activity: newState.currentActivity, \r\n        content: activityContent \r\n      });\r\n      \r\n      // Atualizar estado baseado na atividade atual\r\n      newState.currentEmotion = activityContent.emotion || newState.currentEmotion;\r\n      newState.puzzlePieces = activityContent.availablePieces || activityContent.pieces || [];\r\n      newState.placedPieces = new Array(activityContent.targetSlots || 3).fill(null);\r\n      newState.isComplete = false;\r\n      newState.draggedPiece = null;\r\n      newState.feedback = null;\r\n      newState.showFeedback = false;\r\n      \r\n      // Incrementar contadores\r\n      newState.activityRoundCount = newState.activityRoundCount + 1;\r\n      newState.round = newState.round + 1;\r\n      newState.roundStartTime = Date.now();\r\n      \r\n      return newState;\r\n    });\r\n  }, [gameState.currentActivity, gameState.activityRoundCount, gameState.round, generateActivityContent, speak]);\r\n\r\n  // =====================================================\r\n  // 🎯 FUNÇÕES DE RENDERIZAÇÃO PARA CADA ATIVIDADE\r\n  // =====================================================\r\n\r\n  // 🧩 MONTAGEM EMOCIONAL - Layout de quebra-cabeça tradicional\r\n  const renderEmotionAssembly = () => {\r\n    return (\r\n      <div className={styles.activityContainer}>\r\n        <div className={styles.activityHeader}>\r\n          <h2 className={styles.activityTitle}>🎯 Montagem Emocional</h2>\r\n        </div>\r\n\r\n        {currentEmotion && (\r\n          <>\r\n            {/* Área do quebra-cabeça */}\r\n            <div className={styles.puzzleArea}>\r\n              <div className={styles.puzzleBoard}>\r\n                <div className={styles.boardTitle}>\r\n                  Monte a emoção: {currentEmotion.name}\r\n                </div>\r\n                <div className={styles.puzzleGrid}>\r\n                  {placedPieces.map((piece, index) => (\r\n                    <div\r\n                      key={index}\r\n                      className={`${styles.puzzlePiece} ${piece ? styles.filled : styles.empty}`}\r\n                      onDragOver={(e) => e.preventDefault()}\r\n                      onDrop={() => handleDrop(index)}\r\n                      style={{ backgroundColor: currentEmotion.color + '20' }}\r\n                    >\r\n                      {piece && (\r\n                        <div className={`${styles.puzzlePiece} ${styles.correct}`}>\r\n                          {piece.content}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Área das peças disponíveis */}\r\n              <div className={styles.piecesArea}>\r\n                <div className={styles.piecesTitle}>Peças Disponíveis</div>\r\n                <div className={styles.piecesGrid}>\r\n                  {puzzlePieces.map((piece) => (\r\n                    <div\r\n                      key={piece.id}\r\n                      className={`${styles.availablePiece} ${piece.used ? styles.used : ''}`}\r\n                      draggable\r\n                      onDragStart={() => handleDragStart(piece)}\r\n                      onClick={() => {\r\n                        const emptySlotIndex = placedPieces.findIndex(p => p === null)\r\n                        if (emptySlotIndex !== -1) {\r\n                          handleDrop(emptySlotIndex)\r\n                        }\r\n                      }}\r\n                    >\r\n                      {piece.content}\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 🔍 COMPLETAR PADRÕES - Layout de sequência lógica\r\n  const renderPatternCompletion = () => {\r\n    const activityData = gameState.activityData?.patternCompletion || {};\r\n\r\n    return (\r\n      <div className={styles.activityContainer}>\r\n        <div className={styles.activityHeader}>\r\n          <h2 className={styles.activityTitle}>🔍 Completar Padrões</h2>\r\n        </div>\r\n\r\n        {activityData.pattern && (\r\n          <>\r\n            {/* Área do padrão */}\r\n            <div className={styles.patternArea}>\r\n              <div className={styles.patternTitle}>\r\n                Padrão: {activityData.pattern.type}\r\n              </div>\r\n              <div className={styles.patternSequence}>\r\n                {activityData.pattern.sequence.map((item, index) => (\r\n                  <div key={index} className={styles.patternItem}>\r\n                    <div className={styles.patternNumber}>{index + 1}</div>\r\n                    <div className={styles.patternEmoji}>{item}</div>\r\n                  </div>\r\n                ))}\r\n                <div className={styles.patternItem}>\r\n                  <div className={styles.patternNumber}>{activityData.pattern.sequence.length + 1}</div>\r\n                  <div className={styles.patternQuestion}>❓</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Opções de resposta */}\r\n            <div className={styles.optionsArea}>\r\n              <div className={styles.optionsTitle}>Escolha o próximo elemento:</div>\r\n              <div className={styles.optionsGrid}>\r\n                {activityData.options?.map((option, index) => (\r\n                  <button\r\n                    key={index}\r\n                    className={`${styles.optionButton} ${\r\n                      activityData.userAnswer === option ? styles.selected : ''\r\n                    }`}\r\n                    onClick={() => {\r\n                      setGameState(prev => ({\r\n                        ...prev,\r\n                        activityData: {\r\n                          ...prev.activityData,\r\n                          patternCompletion: {\r\n                            ...prev.activityData.patternCompletion,\r\n                            userAnswer: option\r\n                          }\r\n                        }\r\n                      }));\r\n                    }}\r\n                    disabled={activityData.userAnswer !== null}\r\n                  >\r\n                    {option}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 🔄 ROTAÇÃO ESPACIAL - Layout de peças rotacionadas\r\n  const renderSpatialRotation = () => {\r\n    const activityData = gameState.activityData?.spatialRotation || {};\r\n\r\n    return (\r\n      <div className={styles.activityContainer}>\r\n        <div className={styles.activityHeader}>\r\n          <h2 className={styles.activityTitle}>🔄 Rotação Espacial</h2>\r\n        </div>\r\n\r\n        {activityData.rotatedPieces && (\r\n          <>\r\n            {/* Área de rotação */}\r\n            <div className={styles.rotationArea}>\r\n              <div className={styles.rotationTitle}>\r\n                Peças Rotacionadas - Mentalize a posição original\r\n              </div>\r\n              <div className={styles.rotationGrid}>\r\n                {activityData.rotatedPieces.map((piece, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className={`${styles.rotatedPiece} ${piece.placed ? styles.placed : ''}`}\r\n                    style={{ transform: `rotate(${piece.angle}deg)` }}\r\n                    onClick={() => {\r\n                      // Lógica de posicionamento da peça rotacionada\r\n                      setGameState(prev => ({\r\n                        ...prev,\r\n                        activityData: {\r\n                          ...prev.activityData,\r\n                          spatialRotation: {\r\n                            ...prev.activityData.spatialRotation,\r\n                            rotatedPieces: prev.activityData.spatialRotation.rotatedPieces.map((p, i) =>\r\n                              i === index ? { ...p, placed: true } : p\r\n                            )\r\n                          }\r\n                        }\r\n                      }));\r\n                    }}\r\n                  >\r\n                    <div className={styles.pieceContent}>{piece.original}</div>\r\n                    <div className={styles.rotationAngle}>{piece.angle}°</div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Área de posicionamento */}\r\n            <div className={styles.positionArea}>\r\n              <div className={styles.positionTitle}>Posições Corretas</div>\r\n              <div className={styles.positionGrid}>\r\n                {Array.from({ length: activityData.targetPositions || 4 }).map((_, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className={`${styles.positionSlot} ${\r\n                      activityData.rotatedPieces?.some(p => p.correctPosition === index && p.placed)\r\n                        ? styles.filled : styles.empty\r\n                    }`}\r\n                  >\r\n                    {index + 1}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 😊 CLASSIFICAÇÃO EMOCIONAL - Layout de categorização\r\n  const renderEmotionSorting = () => {\r\n    const activityData = gameState.activityData?.emotionSorting || {};\r\n\r\n    return (\r\n      <div className={styles.activityContainer}>\r\n        <div className={styles.activityHeader}>\r\n          <h2 className={styles.activityTitle}>😊 Classificação Emocional</h2>\r\n        </div>\r\n\r\n        {activityData.categories && (\r\n          <>\r\n            {/* Área de categorias */}\r\n            <div className={styles.categoriesArea}>\r\n              {Object.entries(activityData.categories).map(([categoryKey, category]) => (\r\n                <div key={categoryKey} className={styles.categoryContainer}>\r\n                  <div className={styles.categoryHeader}>\r\n                    <span className={styles.categoryIcon}>{category.icon}</span>\r\n                    <span className={styles.categoryName}>{category.name}</span>\r\n                  </div>\r\n                  <div className={styles.categoryDropZone}>\r\n                    {activityData.classified?.[categoryKey]?.map((item, index) => (\r\n                      <div key={index} className={styles.classifiedItem}>\r\n                        {item.emoji}\r\n                      </div>\r\n                    )) || <div className={styles.emptyCategory}>Arraste emoções aqui</div>}\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Peças para classificar */}\r\n            <div className={styles.sortingPieces}>\r\n              <div className={styles.sortingTitle}>Emoções para Classificar</div>\r\n              <div className={styles.sortingGrid}>\r\n                {activityData.pieces?.map((piece, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className={`${styles.sortingPiece} ${\r\n                      Object.values(activityData.classified || {}).flat().some(item => item.emoji === piece.emoji)\r\n                        ? styles.classified : ''\r\n                    }`}\r\n                    draggable\r\n                    onClick={() => {\r\n                      // Lógica de classificação automática para touch\r\n                      const targetCategory = piece.category;\r\n                      setGameState(prev => ({\r\n                        ...prev,\r\n                        activityData: {\r\n                          ...prev.activityData,\r\n                          emotionSorting: {\r\n                            ...prev.activityData.emotionSorting,\r\n                            classified: {\r\n                              ...prev.activityData.emotionSorting.classified,\r\n                              [targetCategory]: [\r\n                                ...(prev.activityData.emotionSorting.classified?.[targetCategory] || []),\r\n                                piece\r\n                              ]\r\n                            }\r\n                          }\r\n                        }\r\n                      }));\r\n                    }}\r\n                  >\r\n                    {piece.emoji}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 🎨 COMPOSIÇÃO CRIATIVA - Layout de criação livre\r\n  const renderCreativeComposition = () => {\r\n    const activityData = gameState.activityData?.creativeComposition || {};\r\n\r\n    return (\r\n      <div className={styles.activityContainer}>\r\n        <div className={styles.activityHeader}>\r\n          <h2 className={styles.activityTitle}>🎨 Composição Criativa</h2>\r\n        </div>\r\n\r\n        {activityData.creativePieces && (\r\n          <>\r\n            {/* Área de composição */}\r\n            <div className={styles.compositionArea}>\r\n              <div className={styles.compositionTitle}>Sua Composição Criativa</div>\r\n              <div className={styles.compositionCanvas}>\r\n                {activityData.userComposition?.length > 0 ? (\r\n                  activityData.userComposition.map((piece, index) => (\r\n                    <div key={index} className={styles.compositionPiece}>\r\n                      {piece}\r\n                    </div>\r\n                  ))\r\n                ) : (\r\n                  <div className={styles.emptyCanvas}>\r\n                    Clique nas peças abaixo para criar sua composição\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Restrições */}\r\n              <div className={styles.compositionConstraints}>\r\n                <div className={styles.constraintItem}>\r\n                  Peças: {activityData.userComposition?.length || 0} / {activityData.constraints?.maxPieces || 5}\r\n                </div>\r\n                <div className={styles.constraintItem}>\r\n                  Temas: {activityData.constraints?.themes?.join(', ') || 'Livre'}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Peças disponíveis */}\r\n            <div className={styles.creativePieces}>\r\n              <div className={styles.creativePiecesTitle}>Peças Disponíveis</div>\r\n              <div className={styles.creativePiecesGrid}>\r\n                {activityData.creativePieces?.map((piece, index) => (\r\n                  <button\r\n                    key={index}\r\n                    className={`${styles.creativePiece} ${\r\n                      activityData.userComposition?.includes(piece) ? styles.used : ''\r\n                    }`}\r\n                    onClick={() => {\r\n                      if ((activityData.userComposition?.length || 0) < (activityData.constraints?.maxPieces || 5)) {\r\n                        setGameState(prev => ({\r\n                          ...prev,\r\n                          activityData: {\r\n                            ...prev.activityData,\r\n                            creativeComposition: {\r\n                              ...prev.activityData.creativeComposition,\r\n                              userComposition: [\r\n                                ...(prev.activityData.creativeComposition?.userComposition || []),\r\n                                piece\r\n                              ]\r\n                            }\r\n                          }\r\n                        }));\r\n                      }\r\n                    }}\r\n                    disabled={activityData.userComposition?.includes(piece)}\r\n                  >\r\n                    {piece}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // =====================================================\r\n  // 🎯 FUNÇÕES DE RENDERIZAÇÃO PARA CADA ATIVIDADE\r\n  // =====================================================\r\n\r\n  // 🧩 QUEBRA-CABEÇA EMOCIONAL - DRAG AND DROP TRADICIONAL\r\n  const renderEmotionPuzzle = () => {\r\n    // Se não há emoção atual, gerar uma nova\r\n    if (!currentEmotion && gameStarted) {\r\n      generateNewPuzzle();\r\n      return (\r\n        <div className={styles.questionArea}>\r\n          <div className={styles.questionHeader}>\r\n            <h2 className={styles.questionTitle}>🧩 Quebra-Cabeça Emocional</h2>\r\n          </div>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <div className={styles.questionArea}>\r\n        <div className={styles.questionHeader}>\r\n          <h2 className={styles.questionTitle}>🧩 Monte a emoção: {currentEmotion?.name}</h2>\r\n        </div>\r\n\r\n        {currentEmotion && (\r\n          <>\r\n            {/* Área de objetos - Emoção principal */}\r\n            <div className={styles.objectsDisplay}>\r\n              <div style={{\r\n                textAlign: 'center',\r\n                padding: '2rem',\r\n                border: '3px solid rgba(33, 150, 243, 0.6)',\r\n                borderRadius: '20px',\r\n                backgroundColor: 'rgba(33, 150, 243, 0.2)',\r\n                minWidth: '200px',\r\n                boxShadow: '0 0 20px rgba(33, 150, 243, 0.3)',\r\n                marginBottom: '2rem'\r\n              }}>\r\n                <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>\r\n                  {currentEmotion.emoji}\r\n                </div>\r\n                <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'white' }}>\r\n                  {currentEmotion.name}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Área do quebra-cabeça - Slots para drag and drop */}\r\n              <div className={styles.puzzleBoard}>\r\n                <div className={styles.boardTitle}>\r\n                  Monte as peças que representam esta emoção:\r\n                </div>\r\n                <div className={styles.puzzleGrid}>\r\n                  {placedPieces.map((piece, index) => (\r\n                    <div\r\n                      key={index}\r\n                      className={`${styles.puzzleSlot} ${piece ? styles.filled : styles.empty}`}\r\n                      onDragOver={(e) => e.preventDefault()}\r\n                      onDrop={() => handleDrop(index)}\r\n                      style={{\r\n                        backgroundColor: currentEmotion.color + '20',\r\n                        border: piece ? '3px solid #4CAF50' : '2px dashed rgba(255,255,255,0.5)'\r\n                      }}\r\n                    >\r\n                      {piece && (\r\n                        <div className={styles.placedPiece}>\r\n                          <div style={{ fontSize: '2.5rem', marginBottom: '0.5rem' }}>\r\n                            {piece.content}\r\n                          </div>\r\n                          <button\r\n                            className={styles.removePieceButton}\r\n                            onClick={() => handleRemovePiece(index)}\r\n                            title=\"Remover peça\"\r\n                          >\r\n                            ❌\r\n                          </button>\r\n                        </div>\r\n                      )}\r\n                      {!piece && (\r\n                        <div className={styles.emptySlotText}>\r\n                          Arraste uma peça aqui\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Peças disponíveis - PADRÃO IMAGE ASSOCIATION HORIZONTAL */}\r\n            <div className={styles.answerOptions}>\r\n              {puzzlePieces.length > 0 ? puzzlePieces.map((piece) => {\r\n                const isUsed = piece.used;\r\n\r\n                return (\r\n                  <button\r\n                    key={piece.id}\r\n                    className={`${styles.answerButton} ${isUsed ? styles.used : ''}`}\r\n                    draggable={!isUsed}\r\n                    onDragStart={() => !isUsed && handleDragStart(piece)}\r\n                    style={{\r\n                      opacity: isUsed ? 0.3 : 1,\r\n                      cursor: isUsed ? 'not-allowed' : 'grab',\r\n                      pointerEvents: isUsed ? 'none' : 'auto'\r\n                    }}\r\n                  >\r\n                    <div style={{\r\n                      fontSize: '2.5rem',\r\n                      marginBottom: '0.5rem'\r\n                    }}>\r\n                      {piece.content}\r\n                    </div>\r\n                    <div style={{\r\n                      fontSize: '0.9rem',\r\n                      fontWeight: 'bold',\r\n                      color: isUsed ? 'rgba(255,255,255,0.5)' : 'white'\r\n                    }}>\r\n                      {isUsed ? 'Usada' : 'Arraste'}\r\n                    </div>\r\n                  </button>\r\n                );\r\n              }) : (\r\n                <div className={styles.noPieces}>\r\n                  Carregando peças...\r\n                </div>\r\n              )}\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 🔄 ROTAÇÃO DE PEÇAS - PADRÃO IMAGE ASSOCIATION EXATO\r\n  const renderPieceRotation = () => {\r\n    return (\r\n      <div className={styles.questionArea}>\r\n        <div className={styles.questionHeader}>\r\n          <h2 className={styles.questionTitle}>🔄 Rotação de Peças</h2>\r\n        </div>\r\n\r\n        {/* Área de objetos - PADRÃO IMAGE ASSOCIATION EXATO */}\r\n        <div className={styles.objectsDisplay}>\r\n          <div style={{\r\n            textAlign: 'center',\r\n            padding: '2rem',\r\n            border: '3px solid rgba(33, 150, 243, 0.6)',\r\n            borderRadius: '20px',\r\n            backgroundColor: 'rgba(33, 150, 243, 0.2)',\r\n            minWidth: '200px',\r\n            boxShadow: '0 0 20px rgba(33, 150, 243, 0.3)'\r\n          }}>\r\n            <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>\r\n              🧩\r\n            </div>\r\n            <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'white' }}>\r\n              Peça Original\r\n            </div>\r\n          </div>\r\n\r\n          {/* Seta de conexão - PADRÃO IMAGE ASSOCIATION */}\r\n          <div style={{\r\n            display: 'flex',\r\n            flexDirection: 'column',\r\n            alignItems: 'center',\r\n            gap: '0.5rem',\r\n            margin: '1rem 0'\r\n          }}>\r\n            <div style={{\r\n              fontSize: '3rem',\r\n              animation: 'bounce 2s infinite',\r\n              color: 'rgba(255, 193, 7, 0.8)'\r\n            }}>\r\n              ⬇️\r\n            </div>\r\n            <div style={{\r\n              fontSize: '1rem',\r\n              color: 'rgba(255,255,255,0.8)',\r\n              fontWeight: 'bold'\r\n            }}>\r\n              SE RELACIONA COM\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Opções de resposta - PADRÃO IMAGE ASSOCIATION EXATO */}\r\n        <div className={styles.answerOptions}>\r\n          {[0, 90, 180, 270].map((rotation, index) => (\r\n            <button\r\n              key={index}\r\n              className={styles.answerButton}\r\n              onClick={() => handlePieceRotation(index)}\r\n              aria-label={`Girar ${rotation} graus`}\r\n            >\r\n              <div style={{\r\n                fontSize: '2.5rem',\r\n                marginBottom: '0.5rem',\r\n                transform: `rotate(${rotation}deg)`\r\n              }}>\r\n                🧩\r\n              </div>\r\n              <div style={{ fontSize: '1rem', fontWeight: 'bold' }}>\r\n                {rotation}°\r\n              </div>\r\n            </button>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 🔍 QUEBRA-CABEÇA DE PADRÕES - PADRÃO IMAGE ASSOCIATION EXATO\r\n  const renderPatternPuzzle = () => {\r\n    return (\r\n      <div className={styles.questionArea}>\r\n        <div className={styles.questionHeader}>\r\n          <h2 className={styles.questionTitle}>🔍 Quebra-Cabeça de Padrões</h2>\r\n        </div>\r\n\r\n        {/* Área de objetos - PADRÃO IMAGE ASSOCIATION EXATO */}\r\n        <div className={styles.objectsDisplay}>\r\n          <div style={{\r\n            textAlign: 'center',\r\n            padding: '2rem',\r\n            border: '3px solid rgba(33, 150, 243, 0.6)',\r\n            borderRadius: '20px',\r\n            backgroundColor: 'rgba(33, 150, 243, 0.2)',\r\n            minWidth: '300px',\r\n            boxShadow: '0 0 20px rgba(33, 150, 243, 0.3)'\r\n          }}>\r\n            <div style={{\r\n              display: 'flex',\r\n              gap: '1rem',\r\n              justifyContent: 'center',\r\n              alignItems: 'center',\r\n              marginBottom: '1rem'\r\n            }}>\r\n              {['😊', '😢', '😊', '😢', '❓'].map((item, index) => (\r\n                <div\r\n                  key={index}\r\n                  style={{\r\n                    fontSize: '2.5rem',\r\n                    padding: '0.5rem',\r\n                    border: index === 4 ? '2px dashed rgba(255, 193, 7, 0.8)' : '2px solid rgba(255,255,255,0.3)',\r\n                    borderRadius: '12px',\r\n                    backgroundColor: index === 4 ? 'rgba(255, 193, 7, 0.3)' : 'rgba(255,255,255,0.1)',\r\n                    minWidth: '50px',\r\n                    minHeight: '50px',\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    justifyContent: 'center'\r\n                  }}\r\n                >\r\n                  {item}\r\n                </div>\r\n              ))}\r\n            </div>\r\n            <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'white' }}>\r\n              Complete o Padrão\r\n            </div>\r\n          </div>\r\n\r\n          {/* Seta de conexão - PADRÃO IMAGE ASSOCIATION */}\r\n          <div style={{\r\n            display: 'flex',\r\n            flexDirection: 'column',\r\n            alignItems: 'center',\r\n            gap: '0.5rem',\r\n            margin: '1rem 0'\r\n          }}>\r\n            <div style={{\r\n              fontSize: '3rem',\r\n              animation: 'bounce 2s infinite',\r\n              color: 'rgba(255, 193, 7, 0.8)'\r\n            }}>\r\n              ⬇️\r\n            </div>\r\n            <div style={{\r\n              fontSize: '1rem',\r\n              color: 'rgba(255,255,255,0.8)',\r\n              fontWeight: 'bold'\r\n            }}>\r\n              SE RELACIONA COM\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Opções de resposta - PADRÃO IMAGE ASSOCIATION EXATO */}\r\n        <div className={styles.answerOptions}>\r\n          {['😊', '😢', '😡', '😨'].map((option, index) => (\r\n            <button\r\n              key={index}\r\n              className={styles.answerButton}\r\n              onClick={() => handlePatternAnswer(option)}\r\n              aria-label={`Opção ${option}`}\r\n            >\r\n              <div style={{ fontSize: '2.5rem', marginBottom: '0.5rem' }}>\r\n                {option}\r\n              </div>\r\n              <div style={{ fontSize: '1rem', fontWeight: 'bold' }}>\r\n                {option === '😊' ? 'Feliz' : option === '😢' ? 'Triste' : option === '😡' ? 'Bravo' : 'Medo'}\r\n              </div>\r\n            </button>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  // 🔊 Cleanup do TTS quando componente é desmontado\r\n  useEffect(() => {\r\n    return () => {\r\n      // Cancelar qualquer TTS ativo quando sair do jogo\r\n      if ('speechSynthesis' in window) {\r\n        window.speechSynthesis.cancel();\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  const [analysisResults, setAnalysisResults] = useState(null)\r\n  const [attemptCount, setAttemptCount] = useState(0)\r\n\r\n  // Estados para métricas avançadas\r\n  const [sessionStartTime, setSessionStartTime] = useState(null)\r\n  const [pieceInteractions, setPieceInteractions] = useState([])\r\n  const [spatialStrategies, setSpatialStrategies] = useState([])\r\n  const [problemSolvingApproach, setProblemSolvingApproach] = useState('systematic')\r\n\r\n  // Inicializar sistema multissensorial quando sessionId estiver disponível\r\n  useEffect(() => {\r\n    if (sessionId && typeof sessionId === 'string' && sessionId.length > 0 && gameStarted && !multisensoryInitialized) {\r\n      const initializeMultisensorial = async () => {\r\n        try {\r\n          await initMultisensory();\r\n          console.log('✅ Sistema multissensorial inicializado com sessionId:', sessionId);\r\n        } catch (error) {\r\n          console.error('❌ Erro ao inicializar sistema multissensorial:', error);\r\n        }\r\n      };\r\n      initializeMultisensorial();\r\n    }\r\n  }, [sessionId, gameStarted, multisensoryInitialized, initMultisensory]);\r\n\r\n  // Calcular precisão\r\n  const getAccuracy = useCallback(() => {\r\n    if (gameStats.totalAttempts === 0) return 100\r\n    return Math.round((gameStats.completed / gameStats.totalAttempts) * 100)\r\n  }, [gameStats])\r\n\r\n  // 🧠 Coletar métricas específicas do quebra-cabeça\r\n  const collectPuzzleMetrics = async () => {\r\n    const currentTime = Date.now()\r\n    const sessionDuration = sessionStartTime ? currentTime - sessionStartTime : 0\r\n\r\n    // Métricas básicas do jogo\r\n    const basicMetrics = {\r\n      totalTime: sessionDuration,\r\n      correctAnswers: gameStats.completed,\r\n      incorrectAnswers: gameStats.totalAttempts - gameStats.completed,\r\n      accuracy: getAccuracy(),\r\n      difficultyLevel: difficulty,\r\n      completionLevel: isComplete ? 100 : (placedPieces.length / puzzlePieces.length) * 100\r\n    }\r\n\r\n    // Métricas específicas do quebra-cabeça\r\n    const puzzleSpecificMetrics = {\r\n      spatialReasoning: calculateSpatialReasoning(),\r\n      piecePlacementAccuracy: calculatePlacementAccuracy(),\r\n      completionStrategy: identifyCompletionStrategy(),\r\n      visualSpatialMemory: analyzeVisualSpatialMemory(),\r\n      problemSolvingApproach: problemSolvingApproach,\r\n      frustranceTolerance: calculateFrustranceTolerance(),\r\n      persistenceLevel: calculatePersistenceLevel(),\r\n      rotationAttempts: countRotationAttempts(),\r\n      sequentialPlacement: analyzeSequentialPlacement()\r\n    }\r\n\r\n    // Coletar métricas através do sistema unificado\r\n    await collectMetrics({\r\n      ...basicMetrics,\r\n      ...puzzleSpecificMetrics\r\n    })\r\n\r\n    // 🚀 Processar métricas avançadas para análise espacial\r\n    await processAdvancedMetrics({\r\n      gameType: 'QuebraCabeca',\r\n      sessionData: {\r\n        ...basicMetrics,\r\n        ...puzzleSpecificMetrics,\r\n        interactions: pieceInteractions,\r\n        spatialStrategies: spatialStrategies,\r\n        duration: sessionDuration\r\n      },\r\n      userProfile: {\r\n        preferredDifficulty: difficulty,\r\n        spatialPreferences: identifySpatialPreferences()\r\n      }\r\n    })\r\n  }\r\n\r\n  // Funções auxiliares para análise de métricas espaciais\r\n  const calculateSpatialReasoning = () => {\r\n    const correctPlacements = pieceInteractions.filter(i => i.correct).length\r\n    const totalInteractions = pieceInteractions.length\r\n    return totalInteractions > 0 ? (correctPlacements / totalInteractions) * 100 : 0\r\n  }\r\n\r\n  const calculatePlacementAccuracy = () => {\r\n    const firstTryCorrect = pieceInteractions.filter(i => i.attemptNumber === 1 && i.correct).length\r\n    const totalPieces = puzzlePieces.length\r\n    return totalPieces > 0 ? (firstTryCorrect / totalPieces) * 100 : 0\r\n  }\r\n\r\n  const identifyCompletionStrategy = () => {\r\n    if (spatialStrategies.length === 0) return 'unknown'\r\n    \r\n    const strategies = spatialStrategies.reduce((count, strategy) => {\r\n      count[strategy] = (count[strategy] || 0) + 1\r\n      return count\r\n    }, {})\r\n    \r\n    return Object.keys(strategies).reduce((a, b) => strategies[a] > strategies[b] ? a : b)\r\n  }\r\n\r\n  const analyzeVisualSpatialMemory = () => {\r\n    const memoryScore = pieceInteractions.reduce((score, interaction, index) => {\r\n      if (index > 0) {\r\n        const prevInteraction = pieceInteractions[index - 1]\r\n        if (interaction.pieceId === prevInteraction.pieceId && \r\n            interaction.position.x === prevInteraction.position.x &&\r\n            interaction.position.y === prevInteraction.position.y) {\r\n          score += 10 // Bonus por lembrar posição\r\n        }\r\n      }\r\n      return score\r\n    }, 0)\r\n    \r\n    return Math.min(memoryScore, 100)\r\n  }\r\n\r\n  const calculateFrustranceTolerance = () => {\r\n    const incorrectAttempts = pieceInteractions.filter(i => !i.correct).length\r\n    const totalAttempts = pieceInteractions.length\r\n    \r\n    if (totalAttempts === 0) return 100\r\n    \r\n    // Menor % de tentativas incorretas = maior tolerância\r\n    return Math.max(0, 100 - ((incorrectAttempts / totalAttempts) * 100))\r\n  }\r\n\r\n  const calculatePersistenceLevel = () => {\r\n    const maxAttemptsPerPiece = Math.max(...puzzlePieces.map(piece => {\r\n      return pieceInteractions.filter(i => i.pieceId === piece.id).length\r\n    }), 1)\r\n    \r\n    // Persistência baseada no número máximo de tentativas por peça\r\n    return Math.min(maxAttemptsPerPiece * 20, 100)\r\n  }\r\n\r\n  const countRotationAttempts = () => {\r\n    return pieceInteractions.filter(i => i.action === 'rotate').length\r\n  }\r\n\r\n  const analyzeSequentialPlacement = () => {\r\n    if (pieceInteractions.length < 2) return 'insufficient_data'\r\n    \r\n    let sequentialCount = 0\r\n    for (let i = 1; i < pieceInteractions.length; i++) {\r\n      const curr = pieceInteractions[i]\r\n      const prev = pieceInteractions[i - 1]\r\n      \r\n      if (curr.pieceId === prev.pieceId + 1) {\r\n        sequentialCount++\r\n      }\r\n    }\r\n    \r\n    const sequentialRatio = sequentialCount / (pieceInteractions.length - 1)\r\n    return sequentialRatio > 0.7 ? 'sequential' : sequentialRatio > 0.4 ? 'mixed' : 'random'\r\n  }\r\n\r\n  const identifySpatialPreferences = () => {\r\n    return {\r\n      preferredStartPosition: 'top-left', // Placeholder\r\n      rotationFrequency: countRotationAttempts() / Math.max(pieceInteractions.length, 1),\r\n      systematicApproach: problemSolvingApproach === 'systematic'\r\n    }\r\n  }\r\n\r\n  // 🧩 Registrar interação com peça para análise cognitiva\r\n  const recordPieceInteraction = async (pieceId, position, action, correct) => {\r\n    const interactionData = {\r\n      pieceId,\r\n      position,\r\n      action,\r\n      correct,\r\n      timestamp: Date.now(),\r\n      attemptNumber: attemptCount + 1,\r\n      difficultyLevel: difficulty,\r\n      sessionTime: Date.now() - (sessionStartTime || Date.now())\r\n    }\r\n\r\n    // Adicionar à lista de interações\r\n    setPieceInteractions(prev => [...prev, interactionData])\r\n    \r\n    // Coletar dados com os coletores especializados\r\n    try {\r\n      await collectorsHub.collectMoveData({\r\n        ...interactionData,\r\n        puzzleState: {\r\n          totalPieces: puzzlePieces.length,\r\n          placedPieces: placedPieces.length,\r\n          completionPercentage: (placedPieces.filter(p => p !== null).length / puzzlePieces.length) * 100\r\n        }\r\n      });\r\n      \r\n      // 🔄 Registrar interação multissensorial\r\n      await recordMultisensoryInteraction('game_interaction', {\r\n        interactionType: 'user_action',\r\n        gameSpecificData: interactionData,\r\n        multisensoryProcessing: {\r\n          spatialProcessing: { spatialReasoning: 0.7, visualSpatialMemory: 0.7, spatialOrientation: 0.7 },\r\n          cognitiveProcessing: { problemSolving: 0.7, processingSpeed: 0.7, adaptability: 0.7 },\r\n          behavioralProcessing: { interactionCount: 0.7, averageResponseTime: 0.7, persistence: 0.7 }\r\n        }\r\n      });\r\n\r\n      // A cada 3 tentativas, fazer análise cognitiva completa\r\n      const newAttemptCount = attemptCount + 1\r\n      setAttemptCount(newAttemptCount)\r\n\r\n      if (newAttemptCount % 3 === 0) {\r\n        performCognitiveAnalysis()\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('Erro ao coletar dados da interação:', error)\r\n    }\r\n  }\r\n\r\n  // 🧠 Realizar análise cognitiva completa\r\n  const performCognitiveAnalysis = async () => {\r\n    try {\r\n      setCognitiveAnalysisVisible(true)\r\n      \r\n      const analysisData = await collectorsHub.collectComprehensiveData({\r\n        sessionId: sessionStartTime?.toString() || 'session',\r\n        gameState: {\r\n          difficulty,\r\n          currentLevel: gameStats.level,\r\n          score: gameStats.score,\r\n          accuracy: getAccuracy(),\r\n          isComplete\r\n        },\r\n        interactions: pieceInteractions,\r\n        spatialData: {\r\n          strategies: spatialStrategies,\r\n          approach: problemSolvingApproach\r\n        }\r\n      })\r\n\r\n      setAnalysisResults(analysisData)\r\n\r\n      // Manter visível por 3 segundos\r\n      setTimeout(() => {\r\n        setCognitiveAnalysisVisible(false)\r\n      }, 3000)\r\n\r\n    } catch (error) {\r\n      console.error('Erro na análise cognitiva:', error)\r\n      setCognitiveAnalysisVisible(false)\r\n    }\r\n  }\r\n\r\n  // Gerar novo quebra-cabeça\r\n  const generateNewPuzzle = useCallback(() => {\r\n    const randomEmotion = QuebraCabecaConfig.emotions[Math.floor(Math.random() * QuebraCabecaConfig.emotions.length)]\r\n    const difficultyData = QuebraCabecaConfig.difficulties.find(d => d.id === difficulty)\r\n    \r\n    // Verificar se difficultyData existe\r\n    if (!difficultyData) {\r\n      console.error('Difficulty data not found for:', difficulty)\r\n      return\r\n    }\r\n    \r\n    setCurrentEmotion(randomEmotion)\r\n    setIsComplete(false)\r\n    setFeedback(null)\r\n    setGameStats(prev => ({ ...prev, totalAttempts: prev.totalAttempts + 1 }))\r\n\r\n    // Criar peças corretas da emoção\r\n    const correctPieces = randomEmotion.pieces.slice(0, difficultyData.pieces).map((piece, index) => ({\r\n      id: `correct_${index}`,\r\n      content: piece,\r\n      used: false,\r\n      isCorrect: true\r\n    }))\r\n\r\n    // Criar algumas peças distratoras (incorretas)\r\n    const allEmotions = QuebraCabecaConfig.emotions\r\n    const otherEmotions = allEmotions.filter(e => e.id !== randomEmotion.id)\r\n    const distractorPieces = []\r\n\r\n    // Adicionar 2-3 peças distratoras de outras emoções\r\n    for (let i = 0; i < Math.min(3, otherEmotions.length); i++) {\r\n      const randomOtherEmotion = otherEmotions[Math.floor(Math.random() * otherEmotions.length)]\r\n      const randomPiece = randomOtherEmotion.pieces[Math.floor(Math.random() * randomOtherEmotion.pieces.length)]\r\n      distractorPieces.push({\r\n        id: `distractor_${i}`,\r\n        content: randomPiece,\r\n        used: false,\r\n        isCorrect: false\r\n      })\r\n    }\r\n\r\n    // Combinar e embaralhar todas as peças\r\n    const allPieces = [...correctPieces, ...distractorPieces]\r\n    setPuzzlePieces(allPieces.sort(() => Math.random() - 0.5))\r\n    setPlacedPieces(Array(difficultyData.pieces).fill(null))\r\n  }, [difficulty]);\r\n\r\n  // Iniciar o jogo\r\n  const startGame = useCallback(async (selectedDifficulty) => {\r\n    // Configurar dificuldade se fornecida\r\n    if (selectedDifficulty) {\r\n      setDifficulty(selectedDifficulty);\r\n    }\r\n\r\n    // Esconder tela inicial\r\n    setShowStartScreen(false);\r\n    setGameStarted(true)\r\n    setGameStats({\r\n      level: 1,\r\n      score: 0,\r\n      completed: 0,\r\n      totalAttempts: 0\r\n    });\r\n\r\n    // 🔄 Inicializar integração multissensorial\r\n    try {\r\n      await initMultisensory(`session_${Date.now()}`, {\r\n        difficulty: difficulty,\r\n        gameMode: 'puzzle_solving',\r\n        userId: user?.id || 'anonymous'\r\n      });\r\n    } catch (error) {\r\n      console.warn('⚠️ Erro ao inicializar sessão multissensorial:', error);\r\n    }\r\n\r\n    generateNewPuzzle()\r\n  }, [initMultisensory, generateNewPuzzle, user]);\r\n\r\n  // Toggle TTS\r\n  const toggleTTS = useCallback(() => {\r\n    setTtsActive(prev => {\r\n      const newState = !prev;\r\n      localStorage.setItem('quebraCabeca_ttsActive', JSON.stringify(newState));\r\n      if (!newState && 'speechSynthesis' in window) {\r\n        window.speechSynthesis.cancel();\r\n      }\r\n      return newState;\r\n    });\r\n  }, []);\r\n\r\n  // Lidar com início do drag\r\n  const handleDragStart = useCallback((piece) => {\r\n    setDraggedPiece(piece)\r\n  }, [])\r\n\r\n  // Lidar com quebra-cabeça completo\r\n  const handlePuzzleComplete = useCallback(async () => {\r\n    setIsComplete(true)\r\n    \r\n    const points = QuebraCabecaConfig.gameSettings.pointsByDifficulty[difficulty.toUpperCase()] || 10\r\n    setGameStats(prev => ({\r\n      ...prev,\r\n      completed: prev.completed + 1,\r\n      score: prev.score + points,\r\n      level: prev.level + 1\r\n    }))\r\n\r\n    const message = QuebraCabecaConfig.encouragingMessages[Math.floor(Math.random() * QuebraCabecaConfig.encouragingMessages.length)]\r\n    \r\n    // Feedback sonoro ao completar (substituindo feedback visual)\r\n    speak(`${message} Você ganhou ${points} pontos!`);\r\n\r\n    // 🧠 Coletar métricas completas ao completar o quebra-cabeça\r\n    try {\r\n      await collectPuzzleMetrics()\r\n      console.log('🧩 Métricas do quebra-cabeça coletadas com sucesso!')\r\n    } catch (error) {\r\n      console.error('❌ Erro ao coletar métricas do quebra-cabeça:', error)\r\n    }\r\n  }, [difficulty, speak, collectPuzzleMetrics])\r\n\r\n  // Lidar com drop\r\n  const handleDrop = useCallback((targetIndex) => {\r\n    if (!draggedPiece) return\r\n\r\n    // Verificar se o slot já está ocupado\r\n    if (placedPieces[targetIndex] !== null) {\r\n      speak('Este slot já está ocupado!');\r\n      setDraggedPiece(null);\r\n      return;\r\n    }\r\n\r\n    const newPlacedPieces = [...placedPieces]\r\n    newPlacedPieces[targetIndex] = draggedPiece\r\n\r\n    // No quebra-cabeça emocional, verificar se a peça pertence à emoção atual\r\n    const isCorrectPiece = currentEmotion && currentEmotion.pieces.includes(draggedPiece.content)\r\n\r\n    // 🧠 Registrar interação com métricas avançadas\r\n    recordPieceInteraction(\r\n      draggedPiece.id,\r\n      { x: targetIndex % 2, y: Math.floor(targetIndex / 2) },\r\n      'place',\r\n      isCorrectPiece\r\n    )\r\n\r\n    setPlacedPieces(newPlacedPieces)\r\n\r\n    // Marcar peça como usada apenas se for correta\r\n    if (isCorrectPiece) {\r\n      setPuzzlePieces(prev => prev.map(p =>\r\n        p.id === draggedPiece.id ? { ...p, used: true } : p\r\n      ))\r\n\r\n      speak('Peça colocada corretamente!');\r\n      setGameStats(prev => ({\r\n        ...prev,\r\n        score: prev.score + 10,\r\n        completed: prev.completed + 1\r\n      }))\r\n\r\n      // Verificar se completou o quebra-cabeça\r\n      const correctPiecesPlaced = newPlacedPieces.filter(piece =>\r\n        piece !== null && currentEmotion.pieces.includes(piece.content)\r\n      ).length;\r\n\r\n      if (correctPiecesPlaced === currentEmotion.pieces.length) {\r\n        setTimeout(() => handlePuzzleComplete(), 500);\r\n      }\r\n    } else {\r\n      speak('Esta peça não combina com a emoção!');\r\n      // Remover peça incorreta após um tempo\r\n      setTimeout(() => {\r\n        setPlacedPieces(prev => {\r\n          const updated = [...prev]\r\n          updated[targetIndex] = null\r\n          return updated\r\n        })\r\n      }, 1500)\r\n    }\r\n\r\n    setDraggedPiece(null)\r\n  }, [draggedPiece, placedPieces, currentEmotion, recordPieceInteraction, speak, handlePuzzleComplete]);\r\n\r\n  // Função para remover peça do slot\r\n  const handleRemovePiece = useCallback((slotIndex) => {\r\n    const pieceToRemove = placedPieces[slotIndex];\r\n    if (!pieceToRemove) return;\r\n\r\n    // Remover do slot\r\n    const newPlacedPieces = [...placedPieces];\r\n    newPlacedPieces[slotIndex] = null;\r\n    setPlacedPieces(newPlacedPieces);\r\n\r\n    // Marcar peça como disponível novamente\r\n    setPuzzlePieces(prev => prev.map(p =>\r\n      p.id === pieceToRemove.id ? { ...p, used: false } : p\r\n    ));\r\n\r\n    speak('Peça removida!');\r\n  }, [placedPieces, speak]);\r\n\r\n\r\n\r\n  // Próximo quebra-cabeça\r\n  const nextPuzzle = () => {\r\n    // Resetar métricas para nova rodada\r\n    setPieceInteractions([])\r\n    setSpatialStrategies([])\r\n    setProblemSolvingApproach('systematic')\r\n    \r\n    setTimeout(() => {\r\n      generateNewPuzzle()\r\n    }, 100)\r\n  }\r\n\r\n  // Função para finalizar sessão e coletar métricas finais\r\n  const handleGameEnd = async () => {\r\n    if (sessionStartTime && pieceInteractions.length > 0) {\r\n      try {\r\n        await collectPuzzleMetrics()\r\n        console.log('🎯 Métricas finais do quebra-cabeça coletadas!')\r\n      } catch (error) {\r\n        console.error('❌ Erro ao coletar métricas finais:', error)\r\n      }\r\n    }\r\n    \r\n    if (onBack) {\r\n      onBack()\r\n    }\r\n  }\r\n\r\n\r\n  // Tela de início padronizada\r\n  if (showStartScreen) {\r\n    console.log('🧩 QuebraCabeca: Renderizando tela de início padronizada');\r\n    return (\r\n      <GameStartScreen\r\n        gameTitle=\"Quebra-cabeça Emocional\"\r\n        gameDescription=\"Desenvolva inteligência emocional através de puzzles interativos\"\r\n        gameIcon=\"🧩\"\r\n        onStart={startGame}\r\n        onBack={onBack}\r\n        difficulties={[\r\n          { id: 'easy', name: 'Fácil', description: 'Peças grandes\\nIdeal para iniciantes', icon: '😊' },\r\n          { id: 'medium', name: 'Médio', description: 'Peças médias\\nDesafio equilibrado', icon: '🎯' },\r\n          { id: 'hard', name: 'Avançado', description: 'Peças pequenas\\nPara especialistas', icon: '🚀' }\r\n        ]}\r\n      />\r\n    );\r\n  }\r\n  return (\r\n    <div \r\n      className={`${styles.quebraCabecaGame} ${settings.reducedMotion ? 'reduced-motion' : ''} ${settings.highContrast ? 'high-contrast' : ''}`}\r\n      data-font-size={settings.fontSize}\r\n      data-theme={settings.colorScheme}\r\n      style={{\r\n        fontSize: settings.fontSize === 'small' ? '0.875rem' : \r\n                 settings.fontSize === 'large' ? '1.25rem' : '1rem'\r\n      }}\r\n    >\r\n      {/* 🧠 Indicador de Análise Cognitiva */}\r\n      {cognitiveAnalysisVisible && (\r\n        <div className=\"cognitive-analysis-indicator\">\r\n          <div className=\"analysis-content\">\r\n            <div className=\"analysis-icon\">🎯🧠</div>\r\n            <div className=\"analysis-text\">\r\n              <div className=\"analysis-title\">Análise Espacial em Progresso</div>\r\n              <div className=\"analysis-details\">\r\n                Avaliando raciocínio espacial e estratégias de resolução...\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* 📊 Painel de Insights Cognitivos */}\r\n      {analysisResults && (\r\n        <div className=\"cognitive-insights-panel\">\r\n          <div className=\"insights-header\">\r\n            <span className=\"insights-icon\">🎯</span>\r\n            <span className=\"insights-title\">Análise Cognitiva</span>\r\n          </div>\r\n          <div className=\"insights-content\">\r\n            <div className=\"insight-item\">\r\n              <span className=\"insight-label\">Raciocínio Espacial:</span>\r\n              <span className=\"insight-value\">{analysisResults.spatialReasoning || 'Em análise'}</span>\r\n            </div>\r\n            <div className=\"insight-item\">\r\n              <span className=\"insight-label\">Estratégia:</span>\r\n              <span className=\"insight-value\">{analysisResults.strategy || problemSolvingApproach}</span>\r\n            </div>\r\n            <div className=\"insight-item\">\r\n              <span className=\"insight-label\">Padrão Cognitivo:</span>\r\n              <span className=\"insight-value\">{analysisResults.cognitivePattern || 'Identificando...'}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <div className={styles.gameContent}>\r\n        {/* Header do jogo - PADRÃO LETTERRECOGNITION EXATO */}\r\n        <div className={styles.gameHeader}>\r\n          <h1 className={styles.gameTitle}>\r\n            🧩 Quebra-Cabeça Emocional V3\r\n            <div style={{ fontSize: '0.7rem', opacity: 0.8, marginTop: '0.25rem' }}>\r\n              {ACTIVITY_TYPES[gameState.currentActivity?.toUpperCase()]?.name || 'Emocional'}\r\n            </div>\r\n          </h1>\r\n          <button\r\n            className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : ''}`}\r\n            onClick={toggleTTS}\r\n            title={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}\r\n            aria-label={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}\r\n          >\r\n            {ttsActive ? '🔊' : '🔇'}\r\n          </button>\r\n        </div>\r\n\r\n        {/* Header com estatísticas - PADRÃO LETTERRECOGNITION EXATO */}\r\n        <div className={styles.gameStats}>\r\n          <div className={styles.statCard}>\r\n            <div className={styles.statValue}>{gameState.score}</div>\r\n            <div className={styles.statLabel}>Pontos</div>\r\n          </div>\r\n          <div className={styles.statCard}>\r\n            <div className={styles.statValue}>{gameState.round}</div>\r\n            <div className={styles.statLabel}>Rodada</div>\r\n          </div>\r\n          <div className={styles.statCard}>\r\n            <div className={styles.statValue}>{gameState.accuracy}%</div>\r\n            <div className={styles.statLabel}>Precisão</div>\r\n          </div>\r\n          <div className={styles.statCard}>\r\n            <div className={styles.statValue}>{gameState.level}</div>\r\n            <div className={styles.statLabel}>Nível</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Menu de atividades - PADRÃO IMAGE ASSOCIATION EXATO */}\r\n        <div className={styles.activityMenu}>\r\n          {Object.values(ACTIVITY_TYPES).map((activity) => (\r\n            <button\r\n              key={activity.id}\r\n              className={`${styles.activityButton} ${\r\n                gameState.currentActivity === activity.id ? styles.active : ''\r\n              }`}\r\n              onClick={() => switchActivity(activity.id)}\r\n            >\r\n              <span>{activity.icon}</span>\r\n              <span>{activity.name}</span>\r\n            </button>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Renderização da atividade atual - PADRÃO LETTERRECOGNITION EXATO */}\r\n        {gameState.currentActivity === ACTIVITY_TYPES.EMOTION_PUZZLE.id && renderEmotionPuzzle()}\r\n        {gameState.currentActivity === ACTIVITY_TYPES.PIECE_ROTATION.id && renderPieceRotation()}\r\n        {gameState.currentActivity === ACTIVITY_TYPES.PATTERN_PUZZLE.id && renderPatternPuzzle()}\r\n\r\n        {/* Controles do jogo - PADRÃO PADRONIZADO */}\r\n        <div className={styles.gameControls}>\r\n          <button className={styles.controlButton} onClick={() => speak('Quebra-cabeça emocional. Monte as peças para formar emoções e desenvolver inteligência emocional.')}>\r\n            🔊 Explicar\r\n          </button>\r\n          <button className={styles.controlButton} onClick={() => setShowStartScreen(true)}>\r\n            🔄 Reiniciar\r\n          </button>\r\n          <button className={styles.controlButton} onClick={onBack}>\r\n            ⬅️ Voltar\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default QuebraCabecaGame\r\n"], "names": ["ACTIVITY_TYPES", "EMOTION_PUZZLE", "id", "name", "icon", "description", "cognitiveFunction", "component", "PIECE_ROTATION", "PATTERN_PUZZLE", "QuebraCabecaGame", "onBack", "user", "ttsEnabled", "useContext", "SystemContext", "settings", "useAccessibilityContext", "useRef", "gameState", "setGameState", "useState", "status", "score", "round", "level", "totalRounds", "difficulty", "accuracy", "roundStartTime", "currentActivity", "activityCycle", "activityIndex", "roundsPerActivity", "activityRoundCount", "activitiesCompleted", "activityData", "freeAssembly", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "completedPuzzles", "guidedAssembly", "currentHint", "hintsUsed", "guidanceLevel", "patternMatching", "patterns", "matchedPatterns", "currentPattern", "shapeSorting", "shapes", "sortedShapes", "categories", "timedChallenge", "timeLimit", "timeRemaining", "isTimerActive", "creativeBuilding", "availablePieces", "userCreation", "savedCreations", "showFeedback", "feedbackType", "feedbackMessage", "showCelebration", "responseTime", "hesitationCount", "helpUsed", "consecutiveCorrect", "totalAttempts", "correctAttempts", "<PERSON><PERSON><PERSON><PERSON>", "isComplete", "feedback", "gameStats", "completed", "sessionStartTime", "specialConfig", "mentalRotationTime", "spatialErrors", "pieceClassification", "categoryAccuracy", "sortingStrategy", "classificationTime", "patternIdentification", "patternRecognitionSpeed", "logicalAccuracy", "predictionSuccess", "collaborativeSolving", "cooperationIndex", "communicationTurns", "leadershipEvents", "behavioralMetrics", "reactionTime", "attentionSpan", "frustrationEvents", "persistenceLevel", "engagementScore", "multisensoryProcessing", "activitySpecific", "collectMetrics", "processAdvancedMetrics", "sessionId", "useUnifiedGameLogic", "collectorsHub", "QuebraCabecaCollectorsHub", "ttsActive", "setTtsActive", "saved", "localStorage", "getItem", "JSON", "parse", "initializeSession", "initMultisensory", "recordInteraction", "recordMultisensoryInteraction", "isInitialized", "multisensoryInitialized", "useMultisensoryIntegration", "learningStyle", "profile", "useTherapeuticOrchestrator", "cognitiveAnalysisVisible", "setCognitiveAnalysisVisible", "gameStarted", "setGameStarted", "showStartScreen", "setShowStartScreen", "setGameStats", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "setDraggedPiece", "setP<PERSON><PERSON><PERSON><PERSON>", "setPuzzlePieces", "setIsComplete", "currentEmotion", "setCurrentEmotion", "setFeedback", "speak", "useCallback", "text", "options", "window", "speechSynthesis", "cancel", "utterance", "SpeechSynthesisUtterance", "lang", "rate", "pitch", "volume", "generateActivityContent", "activityId", "config", "QuebraCabecaV3Config", "DIFFICULTY_CONFIGS", "toUpperCase", "activityConfig", "ACTIVITY_CONFIG", "generateEmotionPuzzleActivity", "generatePieceRotationActivity", "generatePatternPuzzleActivity", "difficultyConfig", "console", "log", "emotionLibrary", "EMOTIONS_LIBRARY", "emotionComplexity", "randomEmotion", "Math", "floor", "random", "length", "pieceCounts", "EASY", "MEDIUM", "HARD", "<PERSON><PERSON><PERSON>", "pieceCount", "correct<PERSON><PERSON>ces", "pieces", "slice", "distractor<PERSON><PERSON><PERSON>", "generateDistractorPieces", "emotion", "sort", "targetSlots", "instruction", "activityType", "gridLayout", "ceil", "sqrt", "rotationAngles", "availableAngles", "rotated<PERSON><PERSON><PERSON>", "map", "piece", "index", "original", "angle", "placed", "correctPosition", "completedRotations", "patternTypes", "type", "sequence", "missing", "correct", "levelPatterns", "selectedPatt<PERSON>", "wrongOptions", "filter", "opt", "pattern", "userAnswer", "solved", "count", "allPieces", "distractors", "includes", "baseCategories", "positive", "negative", "neutral", "intense", "selectedCategories", "Object", "keys", "result", "for<PERSON>ach", "key", "categoryKeys", "emotionMap", "categoryKey", "categoryPieces", "<PERSON><PERSON><PERSON><PERSON>", "piecesPerCategory", "push", "category", "emoji", "getCategory<PERSON>ieces", "categoryMap", "emotions", "nature", "objects", "symbols", "selectedType", "next", "<PERSON><PERSON><PERSON><PERSON>", "selectedWrong", "completionOptions", "prev", "nextIndex", "nextActivity", "from", "to", "automatic", "newState", "Date", "now", "activityContent", "Array", "fill", "activityName", "setTimeout", "switchActivity", "indexOf", "manual", "shouldRotateActivity", "nextActivityIndex", "activity", "content", "renderEmotionPuzzle", "styles", "questionArea", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON><PERSON><PERSON>", "questionTitle", "objectsDisplay", "textAlign", "padding", "border", "borderRadius", "backgroundColor", "min<PERSON><PERSON><PERSON>", "boxShadow", "marginBottom", "fontSize", "fontWeight", "color", "puzzleBoard", "boardTitle", "puzzle<PERSON>rid", "puzzleSlot", "filled", "empty", "e", "preventDefault", "handleDrop", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleRemovePiece", "emptySlotText", "answerOptions", "isUsed", "used", "answerButton", "handleDragStart", "opacity", "cursor", "pointerEvents", "no<PERSON>ieces", "renderPieceRotation", "display", "flexDirection", "alignItems", "gap", "margin", "animation", "rotation", "handlePieceRotation", "transform", "renderPatternPuzzle", "justifyContent", "item", "minHeight", "option", "handlePatternAnswer", "useEffect", "analysisResults", "setAnalysisResults", "attemptCount", "setAttemptCount", "setSessionStartTime", "pieceInteractions", "setPieceInteractions", "spatialStrategies", "setSpatialStrategies", "problemSolvingApproach", "setProblemSolvingApproach", "initializeMultisensorial", "error", "getAccuracy", "collectPuzzleMetrics", "currentTime", "sessionDuration", "basicMetrics", "totalTime", "correctAnswers", "incorrectAnswers", "difficultyLevel", "completionLevel", "puzzleSpecificMetrics", "spatialReasoning", "calculateSpatialReasoning", "piecePlacementAccuracy", "calculatePlacementAccuracy", "completionStrategy", "identifyCompletionStrategy", "visualSpatialMemory", "analyzeVisualSpatialMemory", "frustranceTolerance", "calculateFrustranceTolerance", "calculatePersistenceLevel", "rotationAttempts", "countRotationAttempts", "sequentialPlacement", "analyzeSequentialPlacement", "gameType", "sessionData", "interactions", "duration", "userProfile", "preferred<PERSON><PERSON><PERSON><PERSON><PERSON>", "spatialPreferences", "identifySpatialPreferences", "correctPlacements", "i", "totalInteractions", "firstTryCorrect", "attemptNumber", "totalPieces", "strategies", "reduce", "strategy", "a", "b", "memoryScore", "interaction", "prevInteraction", "pieceId", "position", "x", "y", "min", "incorrectAttempts", "max", "maxAttemptsPer<PERSON><PERSON>ce", "action", "sequentialCount", "curr", "sequentialRatio", "preferredStartPosition", "rotationFrequency", "systematicApproach", "recordPieceInteraction", "interactionData", "timestamp", "sessionTime", "collectMoveData", "puzzleState", "completionPercentage", "p", "interactionType", "gameSpecificData", "spatialProcessing", "spatialOrientation", "cognitiveProcessing", "problemSolving", "processingSpeed", "adaptability", "behavioralProcessing", "interactionCount", "averageResponseTime", "persistence", "newAttemptCount", "performCognitiveAnalysis", "analysisData", "collectComprehensiveData", "toString", "currentLevel", "spatialData", "approach", "generateNewPuzzle", "QuebraCabecaConfig", "difficultyData", "difficulties", "find", "d", "isCorrect", "allEmotions", "otherEmotions", "randomOtherEmotion", "<PERSON><PERSON><PERSON><PERSON>", "startGame", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameMode", "userId", "warn", "toggleTTS", "setItem", "stringify", "handlePuzzleComplete", "points", "gameSettings", "pointsByDiff<PERSON>ulty", "message", "encouragingMessages", "targetIndex", "newPlacedPieces", "isCorrectPiece", "correctPiecesPlaced", "updated", "slotIndex", "pieceToRemove", "quebraCabecaGame", "reducedMotion", "highContrast", "colorScheme", "cognitivePattern", "gameContent", "gameHeader", "gameTitle", "marginTop", "headerTtsButton", "statCard", "statValue", "statLabel", "activityMenu", "values", "activityButton", "active", "gameControls", "controlButton"], "mappings": ";;;;;;AAYO,MAAM,0BAA0B;AAAA,EACrC,cAAc;AACZ,SAAK,cAAc;AAAA,MACjB,kBAAkB,CAAC;AAAA,MACnB,kBAAkB,CAAC;AAAA,MACnB,eAAe,CAAC;AAAA,MAChB,kBAAkB,CAAC;AAAA,MACnB,oBAAoB,CAAC;AAAA,MACrB,uBAAuB,CAAA;AAAA,IACzB;AAEA,SAAK,iBAAiB;AAAA,MACpB,mBAAmB;AAAA,MACnB,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,iBAAiB,CAAC;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAEA,SAAK,oBAAoB;AAAA,MACvB,qBAAqB;AAAA,MACrB,iBAAiB;AAAA,MACjB,uBAAuB;AAAA,IACzB;AAEA,SAAK,YAAY;AAEjB,QAAI,KAAK,WAAW;AAClB,cAAQ,IAAI,2CAA2C;AAAA,IAAA;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA,EAMF,yBAAyB,MAAM;AACzB,QAAA;AAEF,UAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;AACrC,gBAAQ,KAAK,2DAA2D;AACxE,eAAO,CAAC;AAAA,MAAA;AAIV,YAAM,iBAAiB,KAAK,kBAAkB,EAAE,GAAG,GAAG,GAAG,EAAE;AAC3D,YAAM,iBAAiB,KAAK,kBAAkB,EAAE,GAAG,GAAG,GAAG,EAAE;AAE3D,YAAM,iBAAiB;AAAA,QACrB,WAAW,KAAK,aAAa,KAAK,IAAI;AAAA,QACtC,SAAS,KAAK,WAAW;AAAA,QACzB;AAAA,QACA;AAAA,QACA,iBAAiB,KAAK,yBAAyB,gBAAgB,cAAc;AAAA,QAC7E,oBAAoB,KAAK,sBAAsB;AAAA,QAC/C,kBAAkB,KAAK,oBAAoB;AAAA,QAC3C,gBAAgB,KAAK,wBAAwB,gBAAgB,cAAc;AAAA,QAC3E,kBAAkB,KAAK,uBAAuB,KAAK,YAAY,KAAK,iBAAiB;AAAA,QACrF,gBAAgB,KAAK,kBAAkB;AAAA,QACvC,YAAY,KAAK,cAAc;AAAA,MACjC;AAGM,YAAA,sBAAsB,KAAK,0BAA0B,cAAc;AAGnE,YAAA,yBAAyB,KAAK,uBAAuB,IAAI;AAE1D,WAAA,YAAY,iBAAiB,KAAK;AAAA,QACrC,GAAG;AAAA,QACH;AAAA,QACA;AAAA,QACA,eAAe,KAAK,oBAAoB,cAAc;AAAA,MAAA,CACvD;AAED,WAAK,qBAAqB,cAAc;AAExC,UAAI,KAAK,WAAW;AAClB,gBAAQ,IAAI,+DAA+D;AAAA,UACzE,UAAU,eAAe;AAAA,UACzB,aAAa,oBAAoB;AAAA,UACjC,eAAe,KAAK,oBAAoB,cAAc;AAAA,QAAA,CACvD;AAAA,MAAA;AAGI,aAAA;AAAA,aACA,OAAO;AACN,cAAA,MAAM,yCAAyC,KAAK;AACrD,aAAA;AAAA,IAAA;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAMF,oBAAoB,MAAM;AACpB,QAAA;AACF,YAAM,kBAAkB;AAAA,QACtB,WAAW,KAAK,aAAa,KAAK,IAAI;AAAA,QACtC,SAAS,KAAK;AAAA,QACd,oBAAoB,KAAK;AAAA,QACzB,mBAAmB,KAAK;AAAA,QACxB,kBAAkB,KAAK;AAAA,QACvB,eAAe,KAAK,iBAAiB;AAAA,QACrC,cAAc,KAAK,gBAAgB;AAAA,QACnC,kBAAkB,KAAK,0BAA0B,IAAI;AAAA,QACrD,qBAAqB,KAAK,6BAA6B,IAAI;AAAA,QAC3D,oBAAoB,KAAK,2BAA2B,IAAI;AAAA,QACxD,kBAAkB,KAAK,wBAAwB,IAAI;AAAA,MACrD;AAGM,YAAA,yBAAyB,KAAK,sBAAsB,eAAe;AAEpE,WAAA,YAAY,iBAAiB,KAAK;AAAA,QACrC,GAAG;AAAA,QACH;AAAA,QACA,sBAAsB,KAAK,2BAA2B,eAAe;AAAA,MAAA,CACtE;AAED,UAAI,KAAK,WAAW;AAClB,gBAAQ,IAAI,2DAA2D;AAAA,UACrE,UAAU,gBAAgB;AAAA,UAC1B,OAAO,gBAAgB;AAAA,UACvB,UAAU,gBAAgB;AAAA,QAAA,CAC3B;AAAA,MAAA;AAGI,aAAA;AAAA,aACA,OAAO;AACN,cAAA,MAAM,qCAAqC,KAAK;AACjD,aAAA;AAAA,IAAA;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAMF,qBAAqB,MAAM;AACrB,QAAA;AACF,YAAM,gBAAgB;AAAA,QACpB,WAAW,KAAK,aAAa,KAAK,IAAI;AAAA,QACtC,aAAa,KAAK,qBAAqB,KAAK,mBAAmB;AAAA,QAC/D,kBAAkB,KAAK,0BAA0B,IAAI;AAAA,QACrD,mBAAmB,KAAK,yBAAyB,IAAI;AAAA,QACrD,mBAAmB,KAAK,wBAAwB,IAAI;AAAA,QACpD,iBAAiB,KAAK,yBAAyB,IAAI;AAAA,QACnD,eAAe,KAAK,qBAAqB,IAAI;AAAA,QAC7C,wBAAwB,KAAK,6BAA6B,IAAI;AAAA,MAChE;AAEK,WAAA,YAAY,cAAc,KAAK,aAAa;AAEjD,UAAI,KAAK,WAAW;AAClB,gBAAQ,IAAI,6DAA6D;AAAA,UACvE,MAAM,cAAc;AAAA,UACpB,UAAU,cAAc;AAAA,UACxB,eAAe,cAAc;AAAA,QAAA,CAC9B;AAAA,MAAA;AAGI,aAAA;AAAA,aACA,OAAO;AACN,cAAA,MAAM,uCAAuC,KAAK;AACnD,aAAA;AAAA,IAAA;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAMF,0BAA0B,MAAM;AAC1B,QAAA;AACF,YAAM,iBAAiB;AAAA,QACrB,WAAW,KAAK,aAAa,KAAK,IAAI;AAAA,QACtC,aAAa,KAAK,oBAAoB,KAAK,UAAU;AAAA,QACrD,qBAAqB,KAAK,yBAAyB,IAAI;AAAA,QACvD,iBAAiB,KAAK,mBAAmB;AAAA,QACzC,mBAAmB,KAAK,wBAAwB,IAAI;AAAA,QACpD,kBAAkB,KAAK,wBAAwB,IAAI;AAAA,QACnD,mBAAmB,KAAK,yBAAyB,IAAI;AAAA,QACrD,kBAAkB,KAAK,wBAAwB,IAAI;AAAA,MACrD;AAEK,WAAA,YAAY,mBAAmB,KAAK,cAAc;AAEvD,UAAI,KAAK,WAAW;AAClB,gBAAQ,IAAI,sEAAsE;AAAA,UAChF,MAAM,eAAe;AAAA,UACrB,UAAU,eAAe;AAAA,UACzB,YAAY,eAAe;AAAA,QAAA,CAC5B;AAAA,MAAA;AAGI,aAAA;AAAA,aACA,OAAO;AACN,cAAA,MAAM,gDAAgD,KAAK;AAC5D,aAAA;AAAA,IAAA;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQF,QAAQ,MAAM;AACL,WAAA,KAAK,QAAQ,IAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ1B,QAAQ,UAAU;AACZ,QAAA;AACF,UAAI,CAAC,UAAU;AACb,gBAAQ,KAAK,mDAAmD;AAChE,eAAO,KAAK,kBAAkB;AAAA,MAAA;AAI1B,YAAA,kBAAkB,SAAS,cAAc,CAAC;AAC1C,YAAA,YAAY,SAAS,aAAa,CAAC;AACnC,YAAA,eAAe,SAAS,gBAAgB,CAAC;AAG/C,YAAM,qBAAqB,KAAK,yBAAyB,iBAAiB,YAAY;AACtF,YAAM,mBAAmB,KAAK,sBAAsB,WAAW,eAAe;AAC9E,YAAM,iBAAiB,KAAK,qBAAqB,cAAc,eAAe;AACxE,YAAA,wBAAwB,KAAK,4BAA4B,eAAe;AAG9E,YAAM,kBAAkB;AAAA,QACtB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,sBAAsB;AAAA,QACtB,qBAAqB,KAAK,6BAA6B;AAAA,UACrD,mBAAmB;AAAA,UACnB,iBAAiB;AAAA,UACjB,eAAe;AAAA,UACf,sBAAsB;AAAA,QAAA,CACvB;AAAA,QACD,WAAW,KAAK,IAAI;AAAA,MACtB;AAEO,aAAA;AAAA,aACA,OAAO;AACN,cAAA,MAAM,qDAAqD,KAAK;AACxE,aAAO,KAAK,kBAAkB;AAAA,IAAA;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAMF,oBAAoB;AACX,WAAA;AAAA,MACL,mBAAmB,EAAE,OAAO,KAAK,OAAO,UAAU;AAAA,MAClD,gBAAgB,EAAE,OAAO,KAAK,OAAO,UAAU;AAAA,MAC/C,eAAe,EAAE,OAAO,KAAK,OAAO,UAAU;AAAA,MAC9C,sBAAsB,EAAE,OAAO,KAAK,OAAO,UAAU;AAAA,MACrD,qBAAqB;AAAA,MACrB,WAAW,KAAK,IAAI;AAAA,IACtB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,6BAA6B,QAAQ;AACnC,QAAI,CAAC,UAAU,CAAC,OAAO,OAAe,QAAA;AAC/B,WAAA,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC,IAAI,OAAO;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMhE,yBAAyB,YAAY,cAAc;AAE1C,WAAA;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,QACP,iBAAiB;AAAA,QACjB,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,MAAA;AAAA,IAEzB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,sBAAsB,WAAW,YAAY;AAEpC,WAAA;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,QACP,kBAAkB;AAAA,QAClB,eAAe;AAAA,QACf,kBAAkB;AAAA,MAAA;AAAA,IAEtB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,qBAAqB,cAAc,YAAY;AAEtC,WAAA;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,MAAA;AAAA,IAErB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,4BAA4B,YAAY;AAE/B,WAAA;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,QACP,wBAAwB;AAAA,QACxB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MAAA;AAAA,IAEvB;AAAA,EAAA;AAAA;AAAA,EAKF,yBAAyB,QAAQ,QAAQ;AAEvC,QAAI,CAAC,UAAU,CAAC,UACZ,OAAO,OAAO,MAAM,YAAY,OAAO,OAAO,MAAM,YACpD,OAAO,OAAO,MAAM,YAAY,OAAO,OAAO,MAAM,UAAU;AACzD,aAAA;AAAA,IAAA;AAGT,UAAM,WAAW,KAAK;AAAA,MACpB,KAAK,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC,IAC/B,KAAK,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC;AAAA,IACjC;AAGA,WAAO,KAAK,IAAI,GAAG,IAAK,WAAW,GAAI;AAAA,EAAA;AAAA,EAGzC,wBAAwB,QAAQ,QAAQ;AAEtC,QAAI,CAAC,UAAU,CAAC,UAAU,OAAO,OAAO,MAAM,YAAY,OAAO,OAAO,MAAM,YAC1E,OAAO,OAAO,MAAM,YAAY,OAAO,OAAO,MAAM,UAAU;AACzD,aAAA;AAAA,IAAA;AAGT,UAAM,WAAW,KAAK;AAAA,MACpB,KAAK,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC,IAC/B,KAAK,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC;AAAA,IACjC;AAEI,QAAA,YAAY,GAAW,QAAA;AACvB,QAAA,YAAY,GAAW,QAAA;AACvB,QAAA,YAAY,GAAW,QAAA;AACpB,WAAA;AAAA,EAAA;AAAA,EAGT,0BAA0B,MAAM;AAC9B,QAAI,CAAC,KAAK,qBAAqB,CAAC,KAAK,iBAAyB,QAAA;AAE9D,UAAM,kBAAkB,KAAK,IAAI,KAAK,oBAAoB,KAAK,gBAAgB;AAC/E,UAAM,uBAAuB,KAAK,IAAI,iBAAiB,MAAM,eAAe;AAE5E,WAAO,KAAK,IAAI,GAAG,IAAK,uBAAuB,GAAI;AAAA,EAAA;AAAA,EAGrD,6BAA6B,MAAM;AACjC,QAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,cAAsB,QAAA;AAE/C,WAAA,KAAK,iBAAiB,KAAK,eAAe;AAAA,EAAA;AAAA,EAGnD,0BAA0B,SAAS;AACjC,UAAM,WAAW,QAAQ;AACnB,UAAA,mBAAmB,QAAQ,qBAAqB,IAAI;AAEnD,WAAA;AAAA,MACL,OAAO,WAAW,MAAM,SAAS,WAAW,MAAM,WAAW;AAAA,MAC7D,sBAAsB;AAAA,MACtB,mBAAmB,KAAK,wBAAwB,OAAO;AAAA,IACzD;AAAA,EAAA;AAAA,EAGF,wBAAwB,MAAM;AACtB,UAAA,QAAQ,KAAK,iBAAiB;AAC9B,UAAA,OAAO,KAAK,gBAAgB;AAElC,QAAI,SAAS,KAAK,OAAO,IAAa,QAAA;AAClC,QAAA,QAAQ,EAAU,QAAA;AAClB,QAAA,OAAO,IAAa,QAAA;AACjB,WAAA;AAAA,EAAA;AAAA,EAGT,qBAAqB,qBAAqB;AACjC,WAAA,sBAAsB,oBAAoB,SAAS;AAAA,EAAA;AAAA,EAG5D,oBAAoB,SAAS;AAC3B,UAAM,UAAU;AAAA,MACd,QAAQ,kBAAkB,MAAM,IAAI;AAAA,MACpC,QAAQ,mBAAmB,IAAI,IAAI;AAAA,MACnC,QAAQ,iBAAiB,MAAO,IAAI;AAAA,IACtC;AAEM,UAAA,OAAO,QAAQ,OAAO,CAAC,KAAK,WAAW,MAAM,QAAQ,CAAC;AAExD,QAAA,QAAQ,EAAU,QAAA;AAClB,QAAA,SAAS,EAAU,QAAA;AAChB,WAAA;AAAA,EAAA;AAAA;AAAA,EAKT,mBAAmB;AACb,QAAA;AACK,aAAA;AAAA,QACL,SAAS;AAAA,UACP,mBAAmB,KAAK,eAAe;AAAA,UACvC,wBAAwB,KAAK,gCAAgC;AAAA,UAC7D,qBAAqB,KAAK,6BAA6B;AAAA,UACvD,gBAAgB,KAAK,eAAe;AAAA,UACpC,qBAAqB,KAAK,6BAA6B;AAAA,QACzD;AAAA,QACA,UAAU;AAAA,UACR,mBAAmB,KAAK,+BAA+B;AAAA,UACvD,mBAAmB,KAAK,yBAAyB;AAAA,UACjD,eAAe,KAAK,gCAAgC;AAAA,UACpD,oBAAoB,KAAK,gCAAgC;AAAA,UACzD,kBAAkB,KAAK,yBAAyB;AAAA,QAClD;AAAA,QACA,iBAAiB,KAAK,+BAA+B;AAAA,QACrD,WAAW,KAAK,IAAI;AAAA,MACtB;AAAA,aACO,OAAO;AACN,cAAA,MAAM,qCAAqC,KAAK;AACjD,aAAA,EAAE,OAAO,oCAAoC;AAAA,IAAA;AAAA,EACtD;AAAA,EAGF,iCAAiC;AAC/B,UAAM,kBAAkB,CAAC;AACnB,UAAA,cAAc,KAAK,gCAAgC;AAEzD,QAAI,cAAc,KAAK;AACrB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,MAAA,CACX;AAAA,IAAA;AAGC,QAAA,KAAK,eAAe,iBAAiB,GAAG;AAC1C,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,MAAA,CACX;AAAA,IAAA;AAGI,WAAA;AAAA,EAAA;AAAA;AAAA,EAKT,qBAAqB,SAAS;AAC5B,SAAK,eAAe;AACf,SAAA,eAAe,gBAAgB,QAAQ;AAExC,QAAA,CAAC,QAAQ,oBAAoB;AAC/B,WAAK,eAAe;AAAA,IAAA;AAGtB,QAAI,QAAQ,gBAAgB;AAC1B,WAAK,eAAe,gBAAgB,KAAK,QAAQ,cAAc;AAAA,IAAA;AAAA,EACjE;AAAA,EAGF,kCAAkC;AAC1B,UAAA,iBAAiB,KAAK,YAAY;AACpC,QAAA,eAAe,WAAW,EAAU,QAAA;AAElC,UAAA,gBAAgB,eAAe,OAAO,CAAC,KAAK,SAAS,MAAM,KAAK,iBAAiB,CAAC;AACxF,WAAO,gBAAgB,eAAe;AAAA,EAAA;AAAA,EAGxC,+BAA+B;AACvB,UAAA,eAAe,KAAK,YAAY;AAClC,QAAA,aAAa,WAAW,EAAU,QAAA;AAEhC,UAAA,gBAAgB,aAAa,OAAO,CAAC,KAAK,SAAS,MAAM,KAAK,kBAAkB,CAAC;AACvF,WAAO,gBAAgB,aAAa;AAAA,EAAA;AAAA,EAGtC,YAAY;AACV,SAAK,cAAc;AAAA,MACjB,kBAAkB,CAAC;AAAA,MACnB,kBAAkB,CAAC;AAAA,MACnB,eAAe,CAAC;AAAA,MAChB,kBAAkB,CAAC;AAAA,MACnB,oBAAoB,CAAC;AAAA,MACrB,uBAAuB,CAAA;AAAA,IACzB;AAEA,SAAK,iBAAiB;AAAA,MACpB,mBAAmB;AAAA,MACnB,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,iBAAiB,CAAC;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAEA,QAAI,KAAK,WAAW;AAClB,cAAQ,IAAI,6CAA6C;AAAA,IAAA;AAAA,EAC3D;AAAA;AAAA,EAIF,uBAAuB,YAAY,mBAAmB;AAEpD,QAAI,CAAC,YAAY;AACR,aAAA;AAAA,IAAA;AAEF,WAAA,KAAK,WAAW,MAAM;AAAA,EAAA;AAAA;AAAA,EAG/B,uBAAuB,MAAM;AACpB,WAAA;AAAA,MACL,kBAAkB,KAAK,oBAAoB;AAAA,MAC3C,oBAAoB,KAAK,2BAA2B;AAAA,MACpD,YAAY,KAAK,+BAA+B,IAAI;AAAA,MACpD,UAAU,KAAK,gCAAgC,IAAI;AAAA,IACrD;AAAA,EAAA;AAAA,EAGF,+BAA+B,MAAM;AAC/B,QAAA,KAAK,gBAAgB,EAAU,QAAA;AAC/B,QAAA,KAAK,gBAAgB,EAAU,QAAA;AAC5B,WAAA;AAAA,EAAA;AAAA,EAGT,gCAAgC,MAAM;AACpC,QAAI,CAAC,KAAK,qBAAqB,CAAC,KAAK,iBAAyB,QAAA;AAC9D,UAAM,OAAO,KAAK,IAAI,KAAK,oBAAoB,KAAK,gBAAgB;AACpE,WAAO,KAAK,IAAI,GAAG,IAAK,OAAO,GAAI;AAAA,EAAA;AAAA;AAAA,EAIrC,6BAA6B;AAAS,WAAA;AAAA,EAAA;AAAA,EACtC,6BAA6B;AAAS,WAAA;AAAA,EAAA;AAAA,EACtC,4BAA4B;AAAS,WAAA,KAAK,WAAW,MAAM;AAAA,EAAA;AAAA,EAC3D,2BAA2B;AAAS,WAAA,EAAE,SAAS,aAAa;AAAA,EAAA;AAAA,EAC5D,0BAA0B;AAAS,WAAA;AAAA,EAAA;AAAA,EACnC,2BAA2B;AAAS,WAAA,KAAK,WAAW,MAAM;AAAA,EAAA;AAAA,EAC1D,uBAAuB;AAAS,WAAA,EAAE,UAAU,SAAS;AAAA,EAAA;AAAA,EACrD,+BAA+B;AAAS,WAAA;AAAA,EAAA;AAAA,EACxC,sBAAsB;AAAS,WAAA;AAAA,EAAA;AAAA,EAC/B,2BAA2B;AAAS,WAAA,KAAK,WAAW,MAAM;AAAA,EAAA;AAAA,EAC1D,0BAA0B;AAAS,WAAA;AAAA,EAAA;AAAA,EACnC,0BAA0B;AAAS,WAAA,EAAE,YAAY,IAAI;AAAA,EAAA;AAAA,EACrD,2BAA2B;AAAS,WAAA,EAAE,WAAW,YAAY;AAAA,EAAA;AAAA,EAC7D,0BAA0B;AAAE,WAAO,EAAE,UAAU,CAAC,SAAS,SAAS,EAAE;AAAA,EAAA;AAAA,EACpE,0BAA0B;AAAS,WAAA;AAAA,EAAA;AAAA,EACnC,iCAAiC;AAAS,WAAA,EAAE,OAAO,YAAY;AAAA,EAAA;AAAA,EAC/D,2BAA2B;AAAS,WAAA,EAAE,aAAa,aAAa;AAAA,EAAA;AAAA,EAChE,kCAAkC;AAAS,WAAA,EAAE,UAAU,UAAU;AAAA,EAAA;AAAA,EACjE,kCAAkC;AAAS,WAAA,EAAE,OAAO,OAAO;AAAA,EAAA;AAAA,EAC3D,2BAA2B;AAAE,WAAO,KAAK;AAAA,EAAA;AAC3C;AC5kBO,MAAM,wBAAwB;AAAA,EACnC,cAAc;AACZ,SAAK,qBAAqB;AAAA,MACxB,YAAY,CAAC;AAAA,MACb,gBAAgB,CAAC;AAAA,MACjB,UAAU,CAAC;AAAA,MACX,eAAe,CAAC;AAAA,MAChB,aAAa,CAAC;AAAA,MACd,eAAe,CAAA;AAAA,IACjB;AAEA,SAAK,iBAAiB;AAAA,MACpB,eAAe;AAAA,MACf,oCAAoB,IAAI;AAAA,MACxB,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,IACrB;AAEA,SAAK,oBAAoB;AAAA,MACvB,mBAAmB;AAAA,MACnB,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,sBAAsB;AAAA,IACxB;AAEA,SAAK,YAAY;AAEjB,QAAI,KAAK,WAAW;AAClB,cAAQ,IAAI,yCAAyC;AAAA,IAAA;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAMF,uBAAuB,MAAM;AACvB,QAAA;AACF,YAAM,kBAAkB;AAAA,QACtB,WAAW,KAAK,aAAa,KAAK,IAAI;AAAA,QACtC,WAAW,KAAK,aAAa,KAAK;AAAA,QAClC,cAAc,KAAK,iBAAiB,IAAI;AAAA,QACxC,gBAAgB,KAAK,gBAAgB,IAAI;AAAA,QACzC,eAAe,KAAK,oBAAoB,IAAI;AAAA,QAC5C,gBAAgB,KAAK,yBAAyB,IAAI;AAAA,QAClD,eAAe,KAAK,oBAAoB,IAAI;AAAA,QAC5C,cAAc,KAAK,oBAAoB,IAAI;AAAA,QAC3C,uBAAuB,KAAK,+BAA+B,IAAI;AAAA,QAC/D,sBAAsB,KAAK,yBAAyB,IAAI;AAAA,MAC1D;AAGA,YAAM,sBAAsB,KAAK,4BAA4B,iBAAiB,IAAI;AAG5E,YAAA,wBAAwB,KAAK,qBAAqB,IAAI;AAEvD,WAAA,mBAAmB,WAAW,KAAK;AAAA,QACtC,GAAG;AAAA,QACH;AAAA,QACA;AAAA,QACA,eAAe,KAAK,oBAAoB,IAAI;AAAA,MAAA,CAC7C;AAED,WAAK,sBAAsB,eAAe;AAE1C,UAAI,KAAK,WAAW;AAClB,gBAAQ,IAAI,qDAAqD;AAAA,UAC/D,UAAU,gBAAgB;AAAA,UAC1B,UAAU,gBAAgB;AAAA,UAC1B,eAAe,gBAAgB;AAAA,QAAA,CAChC;AAAA,MAAA;AAGI,aAAA;AAAA,aACA,OAAO;AACN,cAAA,MAAM,iCAAiC,KAAK;AAC7C,aAAA;AAAA,IAAA;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAMF,oBAAoB,MAAM;AACpB,QAAA;AACF,YAAM,kBAAkB;AAAA,QACtB,WAAW,KAAK,aAAa,KAAK,IAAI;AAAA,QACtC,cAAc,KAAK,gBAAgB;AAAA,QACnC,eAAe,KAAK,uBAAuB,IAAI;AAAA,QAC/C,oBAAoB,KAAK,0BAA0B,IAAI;AAAA,QACvD,kBAAkB,KAAK,uBAAuB,IAAI;AAAA,QAClD,gBAAgB,KAAK,sBAAsB,IAAI;AAAA,QAC/C,cAAc,KAAK,mBAAmB,IAAI;AAAA,QAC1C,oBAAoB,KAAK,4BAA4B,IAAI;AAAA,QACzD,kBAAkB,KAAK,wBAAwB,IAAI;AAAA,QACnD,wBAAwB,KAAK,+BAA+B,IAAI;AAAA,MAClE;AAEK,WAAA,mBAAmB,SAAS,KAAK,eAAe;AAErD,UAAI,KAAK,WAAW;AAClB,gBAAQ,IAAI,uDAAuD;AAAA,UACjE,OAAO,gBAAgB;AAAA,UACvB,cAAc,gBAAgB;AAAA,UAC9B,WAAW,gBAAgB;AAAA,QAAA,CAC5B;AAAA,MAAA;AAGI,aAAA;AAAA,aACA,OAAO;AACN,cAAA,MAAM,mCAAmC,KAAK;AAC/C,aAAA;AAAA,IAAA;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAMF,sBAAsB,MAAM;AACtB,QAAA;AACF,YAAM,kBAAkB;AAAA,QACtB,WAAW,KAAK,aAAa,KAAK,IAAI;AAAA,QACtC,cAAc,KAAK,gBAAgB;AAAA,QACnC,kBAAkB,KAAK,0BAA0B,IAAI;AAAA,QACrD,iBAAiB,KAAK,yBAAyB,IAAI;AAAA,QACnD,kBAAkB,KAAK,wBAAwB,IAAI;AAAA,QACnD,0BAA0B,KAAK,+BAA+B,IAAI;AAAA,QAClE,gBAAgB,KAAK,sBAAsB,IAAI;AAAA,QAC/C,aAAa,KAAK,kBAAkB,IAAI;AAAA,QACxC,kBAAkB,KAAK,yBAAyB,IAAI;AAAA,QACpD,mBAAmB,KAAK,yBAAyB,IAAI;AAAA,MACvD;AAEK,WAAA,mBAAmB,eAAe,KAAK,eAAe;AAE3D,UAAI,KAAK,WAAW;AAClB,gBAAQ,IAAI,kDAAkD;AAAA,UAC5D,UAAU,gBAAgB;AAAA,UAC1B,YAAY,gBAAgB;AAAA,UAC5B,UAAU,gBAAgB;AAAA,QAAA,CAC3B;AAAA,MAAA;AAGI,aAAA;AAAA,aACA,OAAO;AACN,cAAA,MAAM,wCAAwC,KAAK;AACpD,aAAA;AAAA,IAAA;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAMF,qBAAqB,MAAM;AACrB,QAAA;AACF,YAAM,eAAe;AAAA,QACnB,WAAW,KAAK,aAAa,KAAK,IAAI;AAAA,QACtC,WAAW,KAAK,cAAc,IAAI;AAAA,QAClC,oBAAoB,KAAK,sBAAsB;AAAA,QAC/C,wBAAwB,KAAK,sBAAsB;AAAA,QACnD,mBAAmB,KAAK,qBAAqB;AAAA,QAC7C,eAAe,KAAK,oBAAoB,IAAI;AAAA,QAC5C,uBAAuB,KAAK,4BAA4B,IAAI;AAAA,QAC5D,uBAAuB,KAAK,4BAA4B,IAAI;AAAA,QAC5D,oBAAoB,KAAK,yBAAyB,IAAI;AAAA,QACtD,uBAAuB,KAAK,4BAA4B,IAAI;AAAA,MAC9D;AAEK,WAAA,mBAAmB,cAAc,KAAK,YAAY;AAEvD,UAAI,KAAK,WAAW;AAClB,gBAAQ,IAAI,6DAA6D;AAAA,UACvE,WAAW,aAAa;AAAA,UACxB,mBAAmB,aAAa;AAAA,UAChC,UAAU,aAAa;AAAA,QAAA,CACxB;AAAA,MAAA;AAGI,aAAA;AAAA,aACA,OAAO;AACN,cAAA,MAAM,0CAA0C,KAAK;AACtD,aAAA;AAAA,IAAA;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAMF,uBAAuB,MAAM;AACvB,QAAA;AACF,YAAM,qBAAqB;AAAA,QACzB,WAAW,KAAK,aAAa,KAAK,IAAI;AAAA,QACtC,iBAAiB,KAAK,mBAAmB;AAAA,QACzC,eAAe,KAAK,iBAAiB;AAAA,QACrC,iBAAiB,KAAK,yBAAyB,IAAI;AAAA,QACnD,iBAAiB,KAAK,sBAAsB,IAAI;AAAA,QAChD,sBAAsB,KAAK,8BAA8B,IAAI;AAAA,QAC7D,kBAAkB,KAAK,uBAAuB,IAAI;AAAA,QAClD,qBAAqB,KAAK,0BAA0B,IAAI;AAAA,QACxD,YAAY,KAAK,oBAAoB,IAAI;AAAA,QACzC,aAAa,KAAK,mBAAmB,IAAI;AAAA,MAC3C;AAEK,WAAA,mBAAmB,YAAY,KAAK,kBAAkB;AAE3D,UAAI,KAAK,WAAW;AAClB,gBAAQ,IAAI,uDAAuD;AAAA,UACjE,WAAW,mBAAmB;AAAA,UAC9B,YAAY,mBAAmB;AAAA,UAC/B,YAAY,mBAAmB;AAAA,QAAA,CAChC;AAAA,MAAA;AAGI,aAAA;AAAA,aACA,OAAO;AACN,cAAA,MAAM,mCAAmC,KAAK;AAC/C,aAAA;AAAA,IAAA;AAAA,EACT;AAAA;AAAA,EAKF,iBAAiB,MAAM;AACf,UAAA,QAAQ,KAAK,SAAS,CAAC;AACvB,UAAA,OAAO,KAAK,aAAa;AAE3B,QAAA,MAAM,SAAS,EAAU,QAAA;AAC7B,QAAI,OAAO,OAAS,MAAM,SAAS,GAAW,QAAA;AAC1C,QAAA,MAAM,SAAS,GAAW,QAAA;AACvB,WAAA;AAAA,EAAA;AAAA,EAGT,gBAAgB,MAAM;AACd,UAAA,WAAW,KAAK,gBAAgB,CAAC;AAEvC,QAAI,KAAK,oBAAoB,QAAQ,EAAU,QAAA;AAC/C,QAAI,KAAK,gBAAgB,QAAQ,EAAU,QAAA;AAC3C,QAAI,KAAK,mBAAmB,QAAQ,EAAU,QAAA;AACvC,WAAA;AAAA,EAAA;AAAA,EAGT,oBAAoB,MAAM;AAClB,UAAA,eAAe,KAAK,gBAAgB;AACpC,UAAA,QAAQ,KAAK,SAAS,CAAC;AAE7B,QAAI,eAAe,OAAS,MAAM,SAAS,EAAU,QAAA;AACjD,QAAA,eAAe,IAAa,QAAA;AACzB,WAAA;AAAA,EAAA;AAAA,EAGT,+BAA+B,MAAM;AAC7B,UAAA,UAAU,KAAK,WAAW;AAC1B,UAAA,aAAa,KAAK,cAAc;AAChC,UAAA,OAAO,KAAK,aAAa;AAE/B,QAAI,WAAW,aAAa,OAAO,OAAO,IAAc,QAAA;AACpD,QAAA,WAAW,aAAa,IAAY,QAAA;AACjC,WAAA;AAAA,EAAA;AAAA,EAGT,4BAA4B,iBAAiB,MAAM;AAC3C,UAAA,kBAAkB,KAAK,mBAAmB;AAC1C,UAAA,kBAAkB,KAAK,mBAAmB;AAEzC,WAAA;AAAA,MACL,aAAa,kBAAkB,IAAI,SAAS,kBAAkB,IAAI,aAAa;AAAA,MAC/E,iBAAiB,kBAAkB,MAAO,SAAS;AAAA,MACnD,UAAU,oBAAoB,KAAK,KAAK,uBAAuB;AAAA,IACjE;AAAA,EAAA;AAAA,EAGF,0BAA0B,MAAM;AACxB,UAAA,mBAAmB,KAAK,oBAAoB;AAC5C,UAAA,iBAAiB,KAAK,kBAAkB;AAE9C,WAAO,mBAAmB;AAAA,EAAA;AAAA,EAG5B,8BAA8B,MAAM;AAC5B,UAAA,WAAW,KAAK,YAAY;AAC5B,UAAA,YAAY,KAAK,yBAAyB;AAC1C,UAAA,YAAY,KAAK,aAAa;AAEpC,QAAI,WAAW,KAAK,aAAa,CAAC,UAAkB,QAAA;AAChD,QAAA,WAAW,KAAK,UAAkB,QAAA;AAC/B,WAAA;AAAA,EAAA;AAAA;AAAA,EAKT,0BAA0B;AACpB,QAAA;AACK,aAAA;AAAA,QACL,SAAS;AAAA,UACP,eAAe,KAAK,eAAe;AAAA,UACnC,gBAAgB,MAAM,KAAK,KAAK,eAAe,cAAc;AAAA,UAC7D,sBAAsB,KAAK,8BAA8B;AAAA,UACzD,kBAAkB,KAAK,eAAe;AAAA,UACtC,kBAAkB,KAAK,eAAe;AAAA,UACtC,4BAA4B,KAAK,sBAAsB;AAAA,QACzD;AAAA,QACA,UAAU;AAAA,UACR,kBAAkB,KAAK,2BAA2B;AAAA,UAClD,kBAAkB,KAAK,sBAAsB;AAAA,UAC7C,wBAAwB,KAAK,4BAA4B;AAAA,UACzD,uBAAuB,KAAK,2BAA2B;AAAA,UACvD,qBAAqB,KAAK,2BAA2B;AAAA,UACrD,kBAAkB,KAAK,yBAAyB;AAAA,QAClD;AAAA,QACA,iBAAiB,KAAK,sCAAsC;AAAA,QAC5D,WAAW,KAAK,IAAI;AAAA,MACtB;AAAA,aACO,OAAO;AACN,cAAA,MAAM,sDAAsD,KAAK;AAClE,aAAA,EAAE,OAAO,4CAA4C;AAAA,IAAA;AAAA,EAC9D;AAAA,EAGF,wCAAwC;AACtC,UAAM,kBAAkB,CAAC;AACnB,UAAA,gBAAgB,KAAK,8BAA8B;AACnD,UAAA,mBAAmB,KAAK,eAAe;AAE7C,QAAI,gBAAgB,KAAK;AACvB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,MAAA,CACX;AAAA,IAAA;AAGH,QAAI,mBAAmB,KAAK;AAC1B,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,MAAA,CACX;AAAA,IAAA;AAGC,QAAA,KAAK,eAAe,mBAAmB,KAAK;AAC9C,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,MAAA,CACX;AAAA,IAAA;AAGI,WAAA;AAAA,EAAA;AAAA;AAAA,EAKT,sBAAsB,SAAS;AAC7B,SAAK,eAAe;AACpB,SAAK,eAAe,eAAe,IAAI,QAAQ,YAAY;AAEvD,QAAA,QAAQ,kBAAkB,QAAQ;AACpC,WAAK,eAAe,iBAAiB;AAAA,IAAA,WAC5B,QAAQ,kBAAkB,YAAY;AAC/C,WAAK,eAAe,iBAAiB;AAAA,IAAA;AAAA,EACvC;AAAA,EAGF,gCAAgC;AACvB,WAAA,KAAK,eAAe,gBAAgB,IACzC,KAAK,eAAe,gBAAgB,KAAK,eAAe,gBAAgB;AAAA,EAAA;AAAA,EAG5E,YAAY;AACV,SAAK,qBAAqB;AAAA,MACxB,YAAY,CAAC;AAAA,MACb,gBAAgB,CAAC;AAAA,MACjB,UAAU,CAAC;AAAA,MACX,eAAe,CAAC;AAAA,MAChB,aAAa,CAAC;AAAA,MACd,eAAe,CAAA;AAAA,IACjB;AAEA,SAAK,iBAAiB;AAAA,MACpB,eAAe;AAAA,MACf,oCAAoB,IAAI;AAAA,MACxB,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,IACrB;AAEA,QAAI,KAAK,WAAW;AAClB,cAAQ,IAAI,2CAA2C;AAAA,IAAA;AAAA,EACzD;AAAA;AAAA,EAIF,2BAA2B;AAAS,WAAA,KAAK,WAAW;AAAA,EAAA;AAAA,EACpD,sBAAsB;AAAS,WAAA,KAAK,WAAW;AAAA,EAAA;AAAA,EAC/C,sBAAsB;AAAS,WAAA,EAAE,WAAW,YAAY;AAAA,EAAA;AAAA,EACxD,2BAA2B;AAAE,WAAO,EAAE,SAAS,KAAK,OAAA,IAAW,IAAI;AAAA,EAAA;AAAA,EACnE,uBAAuB;AAAS,WAAA,EAAE,WAAW,WAAW;AAAA,EAAA;AAAA,EACxD,sBAAsB;AAAS,WAAA;AAAA,EAAA;AAAA,EAC/B,yBAAyB;AAAS,WAAA;AAAA,EAAA;AAAA,EAClC,4BAA4B;AAAS,WAAA,EAAE,YAAY,KAAK;AAAA,EAAA;AAAA,EACxD,yBAAyB;AAAS,WAAA;AAAA,EAAA;AAAA,EAClC,wBAAwB;AAAS,WAAA,EAAE,WAAW,KAAK;AAAA,EAAA;AAAA,EACnD,qBAAqB;AAAS,WAAA;AAAA,EAAA;AAAA,EAC9B,8BAA8B;AAAS,WAAA;AAAA,EAAA;AAAA,EACvC,0BAA0B;AAAS,WAAA,EAAE,UAAU,MAAM;AAAA,EAAA;AAAA,EACrD,iCAAiC;AAAS,WAAA;AAAA,EAAA;AAAA,EAC1C,2BAA2B;AAAS,WAAA;AAAA,EAAA;AAAA,EACpC,0BAA0B;AAAS,WAAA,EAAE,WAAW,KAAK;AAAA,EAAA;AAAA,EACrD,iCAAiC;AAAS,WAAA;AAAA,EAAA;AAAA,EAC1C,wBAAwB;AAAS,WAAA,EAAE,OAAO,KAAK;AAAA,EAAA;AAAA,EAC/C,oBAAoB;AAAS,WAAA;AAAA,EAAA;AAAA,EAC7B,2BAA2B;AAAS,WAAA;AAAA,EAAA;AAAA,EACpC,2BAA2B;AAAS,WAAA,EAAE,QAAQ,KAAK;AAAA,EAAA;AAAA,EACnD,gBAAgB;AAAS,WAAA;AAAA,EAAA;AAAA,EACzB,sBAAsB;AAAS,WAAA;AAAA,EAAA;AAAA,EAC/B,8BAA8B;AAAS,WAAA;AAAA,EAAA;AAAA,EACvC,8BAA8B;AAAS,WAAA;AAAA,EAAA;AAAA,EACvC,2BAA2B;AAAS,WAAA,EAAE,SAAS,KAAK;AAAA,EAAA;AAAA,EACpD,8BAA8B;AAAS,WAAA;AAAA,EAAA;AAAA,EACvC,2BAA2B;AAAS,WAAA;AAAA,EAAA;AAAA,EACpC,wBAAwB;AAAS,WAAA;AAAA,EAAA;AAAA,EACjC,yBAAyB;AAAS,WAAA;AAAA,EAAA;AAAA,EAClC,4BAA4B;AAAS,WAAA;AAAA,EAAA;AAAA,EACrC,sBAAsB;AAAS,WAAA;AAAA,EAAA;AAAA,EAC/B,qBAAqB;AAAS,WAAA,EAAE,WAAW,KAAK;AAAA,EAAA;AAAA,EAChD,sBAAsB;AAAS,WAAA,KAAK,WAAW;AAAA,EAAA;AAAA,EAC/C,kBAAkB;AAAS,WAAA,KAAK,WAAW;AAAA,EAAA;AAAA,EAC3C,qBAAqB;AAAS,WAAA,KAAK,WAAW;AAAA,EAAA;AAAA,EAC9C,6BAA6B;AAAS,WAAA,EAAE,WAAW,aAAa;AAAA,EAAA;AAAA,EAChE,wBAAwB;AAAS,WAAA,EAAE,OAAO,aAAa;AAAA,EAAA;AAAA,EACvD,8BAA8B;AAAS,WAAA,EAAE,SAAS,OAAO;AAAA,EAAA;AAAA,EACzD,6BAA6B;AAAS,WAAA,EAAE,UAAU,YAAY;AAAA,EAAA;AAAA,EAC9D,6BAA6B;AAAS,WAAA,EAAE,SAAS,aAAa;AAAA,EAAA;AAAA,EAC9D,2BAA2B;AAAE,WAAO,KAAK;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOzC,QAAQ,MAAM;AACL,WAAA,KAAK,QAAQ,IAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ1B,QAAQ,UAAU;AACZ,QAAA;AACF,UAAI,CAAC,UAAU;AACb,gBAAQ,KAAK,iDAAiD;AAC9D,eAAO,KAAK,kBAAkB;AAAA,MAAA;AAI1B,YAAA,eAAe,SAAS,gBAAgB,CAAC;AACzC,YAAA,SAAS,SAAS,UAAU,CAAC;AAC7B,YAAA,iBAAiB,SAAS,kBAAkB;AAC5C,YAAA,WAAW,SAAS,YAAY,CAAC;AAGvC,YAAM,mBAAmB,KAAK,kBAAkB,cAAc,cAAc;AAC5E,YAAM,mBAAmB,KAAK,gBAAgB,cAAc,QAAQ;AACpE,YAAM,sBAAsB,KAAK,mBAAmB,cAAc,MAAM;AACxE,YAAM,sBAAsB,KAAK,mBAAmB,cAAc,QAAQ,cAAc;AAGxF,YAAM,yBAAyB;AAAA,QAC7B,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,sBAAsB;AAAA,QACtB,aAAa;AAAA,QACb,4BAA4B,KAAK,sBAAsB;AAAA,UACrD,iBAAiB;AAAA,UACjB,iBAAiB;AAAA,UACjB,oBAAoB;AAAA,UACpB,oBAAoB;AAAA,QAAA,CACrB;AAAA,QACD,WAAW,KAAK,IAAI;AAAA,MACtB;AAEO,aAAA;AAAA,aACA,OAAO;AACN,cAAA,MAAM,mDAAmD,KAAK;AACtE,aAAO,KAAK,kBAAkB;AAAA,IAAA;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAMF,oBAAoB;AACX,WAAA;AAAA,MACL,YAAY,EAAE,OAAO,KAAK,MAAM,QAAQ;AAAA,MACxC,UAAU,EAAE,OAAO,KAAK,OAAO,UAAU;AAAA,MACzC,sBAAsB,EAAE,OAAO,KAAK,OAAO,UAAU;AAAA,MACrD,aAAa,EAAE,OAAO,KAAK,OAAO,UAAU;AAAA,MAC5C,4BAA4B;AAAA,MAC5B,WAAW,KAAK,IAAI;AAAA,IACtB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,sBAAsB,QAAQ;AAC5B,QAAI,CAAC,UAAU,CAAC,OAAO,OAAe,QAAA;AAC/B,WAAA,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC,IAAI,OAAO;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMhE,kBAAkB,cAAc,gBAAgB;AAEvC,WAAA;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,QACP,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,cAAc;AAAA,MAAA;AAAA,IAElB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,gBAAgB,cAAc,UAAU;AAE/B,WAAA;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,QACP,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,WAAW;AAAA,MAAA;AAAA,IAEf;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,mBAAmB,cAAc,QAAQ;AAEhC,WAAA;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,QACP,kBAAkB;AAAA,QAClB,cAAc;AAAA,QACd,eAAe;AAAA,MAAA;AAAA,IAEnB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,mBAAmB,cAAc,QAAQ,gBAAgB;AAEhD,WAAA;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,QACP,sBAAsB;AAAA,QACtB,iBAAiB;AAAA,QACjB,YAAY;AAAA,MAAA;AAAA,IAEhB;AAAA,EAAA;AAEJ;AC7kBO,MAAM,uBAAuB;AAAA,EAClC,cAAc;AACZ,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,cAAc;AAAA,EAAA;AAAA,EAGrB,MAAM,QAAQ,UAAU;AAClB,QAAA;AACI,YAAA,UAAU,SAAS,WAAW,CAAC;AAC/B,YAAA,eAAe,SAAS,gBAAgB,CAAC;AAEzC,YAAA,qBAAqB,KAAK,4BAA4B,OAAO;AAC7D,YAAA,qBAAqB,KAAK,yBAAyB,OAAO;AAC1D,YAAA,qBAAqB,KAAK,yBAAyB,YAAY;AAE9D,aAAA;AAAA,QACL,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA,UAAU,CAAC,wCAAwC;AAAA,QACnD,iBAAiB,qBAAqB,MAClC,CAAC,qCAAqC,IACtC,CAAC,6BAA6B;AAAA,MACpC;AAAA,aACO,OAAO;AACN,cAAA,MAAM,uCAAuC,KAAK;AAC1D,aAAO,EAAE,OAAO,KAAK,OAAO,MAAM,QAAQ;AAAA,IAAA;AAAA,EAC5C;AAAA,EAGF,4BAA4B,SAAS;AAC7B,UAAA,iBAAiB,QAAQ,kBAAkB;AAC3C,UAAA,kBAAkB,QAAQ,mBAAmB;AAC7C,UAAA,kBAAkB,KAAK,yBAAyB,OAAO;AACrD,YAAA,iBAAiB,kBAAkB,mBAAmB;AAAA,EAAA;AAAA,EAGhE,yBAAyB,SAAS;AAC1B,UAAA,cAAc,QAAQ,uBAAuB;AAC5C,WAAA,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,MAAO,eAAe,GAAI,CAAC;AAAA,EAAA;AAAA,EAG7D,yBAAyB,SAAS;AAChC,WAAO,QAAQ,2BAA2B;AAAA,EAAA;AAAA,EAG5C,yBAAyB,cAAc;AACjC,QAAA,aAAa,WAAW,EAAU,QAAA;AAEtC,UAAM,sBAAsB,aAAa,OAAO,CAAK,MAAA,EAAE,kBAAkB,EAAE;AAC3E,WAAO,sBAAsB,aAAa;AAAA,EAAA;AAE9C;ACnDO,MAAM,6BAA6B,cAAc;AAAA,EACtD,cAAc;AACZ,UAAM,aAAa;AAEnB,SAAK,eAAe;AAAA;AAAA,MAElB,oBAAoB,CAAC;AAAA,MACrB,oBAAoB,CAAC;AAAA,MACrB,eAAe,CAAC;AAAA,MAChB,gBAAgB,CAAC;AAAA;AAAA,MAGjB,oBAAoB,CAAC;AAAA,MACrB,cAAc,CAAC;AAAA,MACf,iBAAiB,CAAC;AAAA,MAClB,qBAAqB,CAAA;AAAA,IACvB;AAEA,SAAK,cAAc;AAAA,MACjB,WAAW;AAAA,MACX,SAAS;AAAA,MACT,yBAAyB;AAAA,MACzB,sBAAsB;AAAA,MACtB,kBAAkB;AAAA,MAClB,qBAAqB;AAAA,MACrB,sBAAsB;AAAA,MACtB,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,kBAAkB;AAAA,IACpB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAOF,MAAM,QAAQ,UAAU;AAClB,QAAA;AAEF,YAAM,YAAY;AAAA,QAChB,WAAW,KAAK,iBAAiB,QAAQ;AAAA,QACzC,cAAc,KAAK,oBAAoB,QAAQ;AAAA,QAC/C,SAAS,KAAK,eAAe,QAAQ;AAAA,QACrC,YAAY,KAAK,yBAAyB,QAAQ;AAAA,MACpD;AAEO,aAAA;AAAA,QACL,WAAW,KAAK,IAAI;AAAA,QACpB,UAAU;AAAA,QACV;AAAA,QACA,OAAO,KAAK,sBAAsB,SAAS;AAAA,MAC7C;AAAA,aACO,OAAO;AACN,cAAA,MAAM,oCAAoC,KAAK;AAChD,aAAA;AAAA,QACL,WAAW,KAAK,IAAI;AAAA,QACpB,UAAU;AAAA,QACV,OAAO,MAAM;AAAA,QACb,OAAO;AAAA,MACT;AAAA,IAAA;AAAA,EACF;AAAA,EAGF,iBAAiB,UAAU;AAClB,WAAA;AAAA,MACL,eAAe;AAAA,MACf,oBAAoB;AAAA,MACpB,eAAe;AAAA,MACf,WAAW;AAAA,IACb;AAAA,EAAA;AAAA,EAGF,oBAAoB,UAAU;AACrB,WAAA;AAAA,MACL,qBAAqB;AAAA,MACrB,sBAAsB;AAAA,MACtB,qBAAqB;AAAA,MACrB,sBAAsB;AAAA,IACxB;AAAA,EAAA;AAAA,EAGF,eAAe,UAAU;AAChB,WAAA;AAAA,MACL,oBAAoB;AAAA,MACpB,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,IACtB;AAAA,EAAA;AAAA,EAGF,yBAAyB,UAAU;AAC1B,WAAA;AAAA,EAAA;AAAA,EAGT,sBAAsB,WAAW;AAC/B,UAAM,SAAS;AAAA,MACb,UAAU,UAAU;AAAA,MACpB,UAAU,aAAa;AAAA,MACvB,UAAU,QAAQ;AAAA,MAClB,UAAU;AAAA,IACZ;AAEO,WAAA,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC,IAAI,OAAO;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAOhE,iBAAiB;AACR,WAAA;AAAA,MACL,WAAW,KAAK,aAAa;AAAA,MAC7B,cAAc,KAAK,aAAa;AAAA,MAChC,SAAS,KAAK,aAAa;AAAA,MAC3B,YAAY,KAAK,YAAY;AAAA,MAC7B,iBAAiB,KAAK,wBAAwB;AAAA,IAChD;AAAA,EAAA;AAAA,EAGF,0BAA0B;AACjB,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EAAA;AAAA,EAGF,mBAAmB;AACjB,WAAO,KAAK,MAAM,KAAK,YAAY,kBAAkB,GAAI;AAAA,EAAA;AAE7D;AClIO,MAAM,oCAAoC,cAAc;AAAA,EAC7D,cAAc;AACZ,UAAM,oBAAoB;AAE1B,SAAK,iBAAiB;AAAA;AAAA,MAEpB,eAAe,CAAC;AAAA,MAChB,eAAe,CAAC;AAAA,MAChB,iBAAiB,CAAC;AAAA,MAClB,cAAc,CAAC;AAAA;AAAA,MAGf,kBAAkB,CAAC;AAAA,MACnB,qBAAqB,CAAC;AAAA,MACtB,iBAAiB,CAAC;AAAA,MAClB,kBAAkB,CAAA;AAAA,IACpB;AAEA,SAAK,cAAc;AAAA,MACjB,WAAW;AAAA,MACX,SAAS;AAAA,MACT,0BAA0B;AAAA,MAC1B,qBAAqB;AAAA,MACrB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,0BAA0B;AAAA,MAC1B,qBAAqB;AAAA,MACrB,oBAAoB;AAAA,MACpB,uBAAuB;AAAA,IACzB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAOF,MAAM,QAAQ,UAAU;AAClB,QAAA;AAEF,YAAM,cAAc;AAAA,QAClB,kBAAkB,KAAK,wBAAwB,QAAQ;AAAA,QACvD,kBAAkB,KAAK,wBAAwB,QAAQ;AAAA,QACvD,iBAAiB,KAAK,uBAAuB,QAAQ;AAAA,QACrD,oBAAoB,KAAK,4BAA4B,QAAQ;AAAA,MAC/D;AAEO,aAAA;AAAA,QACL,WAAW,KAAK,IAAI;AAAA,QACpB,UAAU;AAAA,QACV;AAAA,QACA,OAAO,KAAK,sBAAsB,WAAW;AAAA,MAC/C;AAAA,aACO,OAAO;AACN,cAAA,MAAM,yDAAyD,KAAK;AACrE,aAAA;AAAA,QACL,WAAW,KAAK,IAAI;AAAA,QACpB,UAAU;AAAA,QACV,OAAO,MAAM;AAAA,QACb,OAAO;AAAA,MACT;AAAA,IAAA;AAAA,EACF;AAAA,EAGF,wBAAwB,UAAU;AACzB,WAAA;AAAA,MACL,eAAe;AAAA,MACf,qBAAqB;AAAA,MACrB,eAAe;AAAA,MACf,aAAa;AAAA,IACf;AAAA,EAAA;AAAA,EAGF,wBAAwB,UAAU;AACzB,WAAA;AAAA,MACL,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,qBAAqB;AAAA,IACvB;AAAA,EAAA;AAAA,EAGF,uBAAuB,UAAU;AACxB,WAAA;AAAA,MACL,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,IACnB;AAAA,EAAA;AAAA,EAGF,4BAA4B,UAAU;AAC7B,WAAA;AAAA,EAAA;AAAA,EAGT,sBAAsB,aAAa;AACjC,UAAM,SAAS;AAAA,MACb,YAAY,iBAAiB;AAAA,MAC7B,YAAY,iBAAiB;AAAA,MAC7B,YAAY,gBAAgB;AAAA,MAC5B,YAAY;AAAA,IACd;AAEO,WAAA,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC,IAAI,OAAO;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAOhE,iBAAiB;AACR,WAAA;AAAA,MACL,eAAe,KAAK,eAAe;AAAA,MACnC,eAAe,KAAK,eAAe;AAAA,MACnC,oBAAoB,KAAK,YAAY;AAAA,MACrC,qBAAqB,KAAK,YAAY;AAAA,MACtC,iBAAiB,KAAK,wBAAwB;AAAA,IAChD;AAAA,EAAA;AAAA,EAGF,0BAA0B;AACjB,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EAAA;AAAA,EAGF,mBAAmB;AACjB,WAAO,KAAK,MAAM,KAAK,YAAY,sBAAsB,GAAI;AAAA,EAAA;AAEjE;AClIO,MAAM,wBAAwB,cAAc;AAAA,EACjD,cAAc;AACZ,UAAM,QAAQ;AAEd,SAAK,gBAAgB;AAAA;AAAA,MAEnB,cAAc,CAAC;AAAA,MACf,aAAa,CAAC;AAAA,MACd,mBAAmB,CAAC;AAAA,MACpB,iBAAiB,CAAC;AAAA;AAAA,MAGlB,eAAe,CAAC;AAAA,MAChB,gBAAgB,CAAC;AAAA,MACjB,sBAAsB,CAAC;AAAA,MACvB,gBAAgB,CAAC;AAAA;AAAA,MAGjB,eAAe,CAAC;AAAA,MAChB,kBAAkB,CAAC;AAAA,MACnB,yBAAyB,CAAC;AAAA,MAC1B,gBAAgB,CAAC;AAAA;AAAA,MAGjB,gBAAgB,CAAC;AAAA,MACjB,gBAAgB,CAAC;AAAA,MACjB,iBAAiB,CAAC;AAAA,MAClB,kBAAkB,CAAA;AAAA,IACpB;AAEA,SAAK,cAAc;AAAA,MACjB,WAAW;AAAA,MACX,SAAS;AAAA,MACT,uBAAuB;AAAA,MACvB,mBAAmB;AAAA,MACnB,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,gBAAgB;AAAA,IAClB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAOF,MAAM,QAAQ,UAAU;AAClB,QAAA;AAEF,YAAM,aAAa;AAAA,QACjB,cAAc,KAAK,oBAAoB,QAAQ;AAAA,QAC/C,eAAe,KAAK,qBAAqB,QAAQ;AAAA,QACjD,eAAe,KAAK,qBAAqB,QAAQ;AAAA,QACjD,kBAAkB,KAAK,0BAA0B,QAAQ;AAAA,MAC3D;AAEO,aAAA;AAAA,QACL,WAAW,KAAK,IAAI;AAAA,QACpB,UAAU;AAAA,QACV;AAAA,QACA,OAAO,KAAK,sBAAsB,UAAU;AAAA,MAC9C;AAAA,aACO,OAAO;AACN,cAAA,MAAM,uCAAuC,KAAK;AACnD,aAAA;AAAA,QACL,WAAW,KAAK,IAAI;AAAA,QACpB,UAAU;AAAA,QACV,OAAO,MAAM;AAAA,QACb,OAAO;AAAA,MACT;AAAA,IAAA;AAAA,EACF;AAAA,EAGF,oBAAoB,UAAU;AACrB,WAAA;AAAA,MACL,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,eAAe;AAAA,IACjB;AAAA,EAAA;AAAA,EAGF,qBAAqB,UAAU;AACtB,WAAA;AAAA,MACL,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EAAA;AAAA,EAGF,qBAAqB,UAAU;AACtB,WAAA;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,EAAA;AAAA,EAGF,0BAA0B,UAAU;AAC3B,WAAA;AAAA,EAAA;AAAA,EAGT,sBAAsB,YAAY;AAChC,UAAM,SAAS;AAAA,MACb,WAAW,aAAa;AAAA,MACxB,WAAW,cAAc;AAAA,MACzB,WAAW,cAAc;AAAA,MACzB,WAAW;AAAA,IACb;AAEO,WAAA,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC,IAAI,OAAO;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAOhE,iBAAiB;AACR,WAAA;AAAA,MACL,cAAc,KAAK,cAAc;AAAA,MACjC,eAAe,KAAK,cAAc;AAAA,MAClC,eAAe,KAAK,cAAc;AAAA,MAClC,YAAY,KAAK,YAAY;AAAA,MAC7B,iBAAiB,KAAK,wBAAwB;AAAA,IAChD;AAAA,EAAA;AAAA,EAGF,0BAA0B;AACjB,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EAAA;AAAA,EAGF,mBAAmB;AACjB,WAAO,KAAK,MAAM,KAAK,YAAY,mBAAmB,GAAI;AAAA,EAAA;AAE9D;AC9IO,MAAM,sCAAsC,cAAc;AAAA,EAC/D,cAAc;AACZ,UAAM,sBAAsB;AAE5B,SAAK,oBAAoB;AAAA;AAAA,MAEvB,cAAc,CAAC;AAAA,MACf,qBAAqB,CAAC;AAAA,MACtB,qBAAqB,CAAC;AAAA,MACtB,iBAAiB,CAAC;AAAA;AAAA,MAGlB,iBAAiB,CAAC;AAAA,MAClB,mBAAmB,CAAC;AAAA,MACpB,wBAAwB,CAAC;AAAA,MACzB,eAAe,CAAA;AAAA,IACjB;AAEA,SAAK,cAAc;AAAA,MACjB,WAAW;AAAA,MACX,SAAS;AAAA,MACT,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAOF,MAAM,QAAQ,UAAU;AAClB,QAAA;AAEF,YAAM,iBAAiB;AAAA,QACrB,kBAAkB,KAAK,wBAAwB,QAAQ;AAAA,QACvD,mBAAmB,KAAK,yBAAyB,QAAQ;AAAA,QACzD,uBAAuB,KAAK,6BAA6B,QAAQ;AAAA,QACjE,sBAAsB,KAAK,8BAA8B,QAAQ;AAAA,MACnE;AAEO,aAAA;AAAA,QACL,WAAW,KAAK,IAAI;AAAA,QACpB,UAAU;AAAA,QACV;AAAA,QACA,OAAO,KAAK,sBAAsB,cAAc;AAAA,MAClD;AAAA,aACO,OAAO;AACN,cAAA,MAAM,wDAAwD,KAAK;AACpE,aAAA;AAAA,QACL,WAAW,KAAK,IAAI;AAAA,QACpB,UAAU;AAAA,QACV,OAAO,MAAM;AAAA,QACb,OAAO;AAAA,MACT;AAAA,IAAA;AAAA,EACF;AAAA,EAGF,wBAAwB,UAAU;AACzB,WAAA;AAAA,MACL,cAAc;AAAA,MACd,qBAAqB;AAAA,MACrB,qBAAqB;AAAA,MACrB,iBAAiB;AAAA,IACnB;AAAA,EAAA;AAAA,EAGF,yBAAyB,UAAU;AAC1B,WAAA;AAAA,MACL,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,wBAAwB;AAAA,MACxB,eAAe;AAAA,IACjB;AAAA,EAAA;AAAA,EAGF,6BAA6B,UAAU;AAC9B,WAAA;AAAA,MACL,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,MACnB,sBAAsB;AAAA,MACtB,mBAAmB;AAAA,IACrB;AAAA,EAAA;AAAA,EAGF,8BAA8B,UAAU;AAC/B,WAAA;AAAA,EAAA;AAAA,EAGT,sBAAsB,gBAAgB;AACpC,UAAM,SAAS;AAAA,MACb,eAAe,iBAAiB;AAAA,MAChC,eAAe,kBAAkB;AAAA,MACjC,eAAe,sBAAsB;AAAA,MACrC,eAAe;AAAA,IACjB;AAEO,WAAA,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC,IAAI,OAAO;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAOhE,iBAAiB;AACR,WAAA;AAAA,MACL,cAAc,KAAK,kBAAkB;AAAA,MACrC,mBAAmB,KAAK,kBAAkB;AAAA,MAC1C,sBAAsB,KAAK,YAAY;AAAA,MACvC,kBAAkB,KAAK,YAAY;AAAA,MACnC,iBAAiB,KAAK,wBAAwB;AAAA,IAChD;AAAA,EAAA;AAAA,EAGF,0BAA0B;AACjB,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EAAA;AAAA,EAGF,mBAAmB;AACjB,WAAO,KAAK,MAAM,KAAK,YAAY,mBAAmB,GAAI;AAAA,EAAA;AAE9D;AC1HO,MAAM,0BAA0B;AAAA,EACrC,cAAc;AACZ,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,WAAW;AAGhB,SAAK,cAAc;AAAA,MACjB,kBAAkB,IAAI,0BAA0B;AAAA,MAChD,gBAAgB,IAAI,wBAAwB;AAAA,MAC5C,eAAe,IAAI,uBAAuB;AAAA,MAC1C,aAAa,IAAI,qBAAqB;AAAA,MACtC,oBAAoB,IAAI,4BAA4B;AAAA,MACpD,QAAQ,IAAI,gBAAgB;AAAA,MAC5B,sBAAsB,IAAI,8BAA8B;AAAA,IAC1D;AAEA,SAAK,cAAc,CAAC;AACf,SAAA,oCAAoB,IAAI;AAC7B,SAAK,gBAAgB;AAErB,YAAQ,IAAI,kDAAkD;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMhE,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMd,MAAM,oBAAoB,UAAU;AAC9B,QAAA;AACI,YAAA,YAAY,KAAK,IAAI;AAC3B,YAAM,YAAY,SAAS,aAAa,WAAW,SAAS;AAG5D,YAAM,kBAAkB,MAAM,KAAK,WAAW,iBAAiB,QAAQ,QAAQ;AAC/E,YAAM,yBAAyB,MAAM,KAAK,WAAW,eAAe,QAAQ,QAAQ;AACpF,YAAM,wBAAwB,MAAM,KAAK,WAAW,cAAc,QAAQ,QAAQ;AAClF,YAAM,sBAAsB,MAAM,KAAK,WAAW,YAAY,QAAQ,QAAQ;AAC9E,YAAM,6BAA6B,MAAM,KAAK,WAAW,mBAAmB,QAAQ,QAAQ;AAC5F,YAAM,iBAAiB,MAAM,KAAK,WAAW,OAAO,QAAQ,QAAQ;AACpE,YAAM,+BAA+B,MAAM,KAAK,WAAW,qBAAqB,QAAQ,QAAQ;AAEhG,YAAM,mBAAmB;AAAA,QACvB;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,aAAa;AAAA,QACb,oBAAoB;AAAA,QACpB,QAAQ;AAAA,QACR,sBAAsB;AAAA,QACtB,oBAAoB,KAAK,4BAA4B;AAAA,UACnD;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QAAA,CACD;AAAA,QACD,UAAU,KAAK,iBAAiB;AAAA,UAC9B;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QAAA,CACD;AAAA,QACD,iBAAiB,KAAK,wBAAwB;AAAA,UAC5C;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD,CAAA;AAAA,MACH;AAEK,WAAA,YAAY,KAAK,gBAAgB;AAC/B,aAAA;AAAA,aAEA,OAAO;AACN,cAAA,MAAM,gDAAgD,KAAK;AAC5D,aAAA;AAAA,QACL,OAAO,MAAM;AAAA,QACb,WAAW,KAAK,IAAI;AAAA,QACpB,UAAU;AAAA,MACZ;AAAA,IAAA;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAMF,4BAA4B,UAAU;AAChC,QAAA;AACI,YAAA,SAAS,OAAO,OAAO,QAAQ,EAAE,IAAI,CAAA,aAAY,SAAS,SAAS,GAAG;AACtE,YAAA,eAAe,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC,IAAI,OAAO;AAErE,aAAA;AAAA,QACL,OAAO;AAAA,QACP,OAAO,eAAe,MAAM,aAAa,eAAe,MAAM,kBAAkB;AAAA,QAChF,WAAW,KAAK,kBAAkB,QAAQ;AAAA,QAC1C,YAAY,KAAK,mBAAmB,QAAQ;AAAA,MAC9C;AAAA,aACO,OAAO;AACN,cAAA,MAAM,yCAAyC,KAAK;AACrD,aAAA,EAAE,OAAO,KAAK,OAAO,UAAU,WAAW,CAAA,GAAI,YAAY,GAAG;AAAA,IAAA;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA,EAMF,kBAAkB,UAAU;AAC1B,UAAM,YAAY,CAAC;AACnB,QAAI,SAAS,iBAAiB,QAAQ,IAAK,WAAU,KAAK,2BAA2B;AACrF,QAAI,SAAS,wBAAwB,QAAQ,IAAK,WAAU,KAAK,4BAA4B;AAC7F,QAAI,SAAS,uBAAuB,QAAQ,IAAK,WAAU,KAAK,yCAAyC;AACzG,QAAI,SAAS,qBAAqB,QAAQ,IAAK,WAAU,KAAK,yCAAyC;AACvG,QAAI,SAAS,4BAA4B,QAAQ,IAAK,WAAU,KAAK,qCAAqC;AAC1G,QAAI,SAAS,gBAAgB,QAAQ,IAAK,WAAU,KAAK,kCAAkC;AAC3F,QAAI,SAAS,8BAA8B,QAAQ,IAAK,WAAU,KAAK,mCAAmC;AACnG,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,mBAAmB,UAAU;AAC3B,UAAM,aAAa,CAAC;AACpB,QAAI,SAAS,iBAAiB,QAAQ,IAAK,YAAW,KAAK,gDAAgD;AAC3G,QAAI,SAAS,wBAAwB,QAAQ,IAAK,YAAW,KAAK,uCAAuC;AACzG,QAAI,SAAS,uBAAuB,QAAQ,IAAK,YAAW,KAAK,kDAAkD;AACnH,QAAI,SAAS,qBAAqB,QAAQ,IAAK,YAAW,KAAK,6CAA6C;AAC5G,QAAI,SAAS,4BAA4B,QAAQ,IAAK,YAAW,KAAK,mDAAmD;AACzH,QAAI,SAAS,gBAAgB,QAAQ,IAAK,YAAW,KAAK,kDAAkD;AAC5G,QAAI,SAAS,8BAA8B,QAAQ,IAAK,YAAW,KAAK,kDAAkD;AACnH,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,iBAAiB,UAAU;AACzB,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEO,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,wBAAwB,UAAU;AAChC,UAAM,kBAAkB,CAAC;AAErB,QAAA,SAAS,iBAAiB,QAAQ,KAAK;AACzC,sBAAgB,KAAK,mDAAmD;AAAA,IAAA;AAGtE,QAAA,SAAS,wBAAwB,QAAQ,KAAK;AAChD,sBAAgB,KAAK,0CAA0C;AAAA,IAAA;AAG7D,QAAA,SAAS,qBAAqB,QAAQ,KAAK;AAC7C,sBAAgB,KAAK,+CAA+C;AAAA,IAAA;AAGlE,QAAA,SAAS,4BAA4B,QAAQ,KAAK;AACpD,sBAAgB,KAAK,6CAA6C;AAAA,IAAA;AAGhE,QAAA,SAAS,gBAAgB,QAAQ,KAAK;AACxC,sBAAgB,KAAK,qDAAqD;AAAA,IAAA;AAGxE,QAAA,SAAS,8BAA8B,QAAQ,KAAK;AACtD,sBAAgB,KAAK,6DAA6D;AAAA,IAAA;AAGpF,oBAAgB,KAAK,sDAAsD;AAEpE,WAAA;AAAA,EAAA;AAEX;ACvLO,MAAM,+BAA+B,eAAe;AAAA,EACzD,YAAY,SAAS,IAAI;AAEvB,UAAM,gBAAgB;AAAA,MACpB,UAAU;AAAA,MACV,kBAAkB,CAAC,qBAAqB,mBAAmB,mBAAmB;AAAA,MAC9E,gBAAgB,CAAC,sBAAsB,sBAAsB,gBAAgB;AAAA,MAC7E,YAAY;AAAA,QACV,UAAU;AAAA,QACV,cAAc;AAAA,QACd,YAAY;AAAA,MACd;AAAA,MACA,GAAG;AAAA,IACL;AAEA,UAAM,aAAa;AAGd,SAAA,SAAS,OAAO,UAAU,OAAO,OAAO,OAAO,gBAAgB,aAChE,OAAO,SACP,KAAK,UAAU;AAAA,MACb,MAAM,IAAI,SAAS,QAAQ,KAAK,uBAA0B,oBAAA,KAAO,GAAA,YAAe,GAAA,GAAG,IAAI;AAAA,MACvF,OAAO,IAAI,SAAS,QAAQ,MAAM,sBAAyB,oBAAA,KAAO,GAAA,YAAe,GAAA,GAAG,IAAI;AAAA,MACxF,MAAM,IAAI,SAAS,QAAQ,KAAK,qBAAwB,oBAAA,KAAO,GAAA,YAAe,GAAA,GAAG,IAAI;AAAA,MACrF,OAAO,IAAI,SAAS,QAAQ,MAAM,qBAAwB,oBAAA,KAAO,GAAA,YAAe,GAAA,GAAG,IAAI;AAAA,MACvF,aAAa,IAAI,SAAS,QAAQ,KAAK,4BAA+B,oBAAA,KAAA,GAAO,YAAA,GAAe,GAAG,IAAI;AAAA,IACrG;AAEC,SAAA,OAAO,KAAK,8CAA8C;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASjE,MAAM,2BAA2B,UAAU,aAAa;AAClD,QAAA;AACG,WAAA,QAAQ,KAAK,2CAA2C;AAAA,QAC3D,WAAW,YAAY;AAAA,MAAA,CACxB;AAEK,YAAA,EAAE,WAAW,CAAI,GAAA,YAAY,GAAG,YAAY,OAAO,SAAS,CAAC,EAAA,IAAM;AAGnE,YAAA,gBAAgB,SAAS,UAAU;AACzC,YAAM,oBAAoB,SAAS,OAAO,CAAK,MAAA,EAAE,OAAO,EAAE;AAC1D,YAAM,WAAW,KAAK,MAAO,oBAAoB,gBAAiB,GAAG;AAGrE,YAAM,mBAAmB,KAAK,0BAA0B,UAAU,MAAM;AACxE,YAAM,iBAAiB,KAAK,wBAAwB,UAAU,SAAS;AACvE,YAAM,cAAc,KAAK,qBAAqB,UAAU,SAAS;AACjE,YAAM,mBAAmB,KAAK,0BAA0B,UAAU,MAAM;AAClE,YAAA,cAAc,KAAK,qBAAqB,QAAQ;AAEtD,YAAM,UAAU;AAAA;AAAA,QAEd;AAAA,QACA;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB;AAAA;AAAA,QAGA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAGA,YAAY,KAAK,oBAAoB,mBAAmB,SAAS;AAAA,QACjE,UAAU,KAAK,iBAAiB,QAAQ;AAAA,QACxC,oBAAoB,KAAK,yBAAyB,UAAU,MAAM;AAAA,MACpE;AAEK,WAAA,QAAQ,KAAK,uCAAuC;AAAA,QACvD;AAAA,QACA;AAAA,QACA,kBAAkB,iBAAiB;AAAA,MAAA,CACpC;AAEM,aAAA;AAAA,aACA,OAAO;AACT,WAAA,QAAQ,MAAM,8CAA8C,KAAK;AAC/D,aAAA,KAAK,wBAAwB,QAAQ;AAAA,IAAA;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASF,MAAM,gBAAgB,UAAU,gBAAgB,MAAM;AAChD,QAAA;AACG,WAAA,QAAQ,KAAK,qCAAqC;AAAA,QACrD,WAAW,SAAS;AAAA,QACpB,QAAQ,SAAS;AAAA,QACjB,kBAAkB,gBAAgB,OAAO,KAAK,cAAc,cAAc,CAAA,CAAE,EAAE,SAAS;AAAA,MAAA,CACxF;AAGD,YAAM,UAAU,MAAM,KAAK,2BAA2B,UAAU,QAAQ;AAGxE,YAAM,sBAAsB,KAAK,4BAA4B,SAAS,QAAQ;AAG9E,YAAM,mBAAmB,KAAK,0BAA0B,SAAS,QAAQ;AAElE,aAAA;AAAA,QACL,SAAS;AAAA,QACT,UAAU,KAAK;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MACpC;AAAA,aAEO,OAAO;AACT,WAAA,QAAQ,MAAM,2CAA2C,KAAK;AAC5D,aAAA;AAAA,QACL,SAAS;AAAA,QACT,UAAU,KAAK;AAAA,QACf,OAAO,MAAM;AAAA,QACb,kBAAkB,KAAK,oCAAoC,QAAQ;AAAA,QACnE,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MACpC;AAAA,IAAA;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASF,MAAM,oCAAoC,eAAe,UAAU;AACjE,QAAI,CAAC,iBAAiB,CAAC,cAAc,YAAY;AAC1C,WAAA,QAAQ,KAAK,kDAAkD;AACpE,aAAO,EAAE,YAAY,IAAI,SAAS,0BAA0B;AAAA,IAAA;AAG9D,UAAM,UAAU,CAAC;AACjB,UAAM,aAAa,cAAc;AAGjC,eAAW,CAAC,eAAe,SAAS,KAAK,OAAO,QAAQ,UAAU,GAAG;AAC/D,UAAA;AACF,YAAI,aAAa,OAAO,UAAU,YAAY,YAAY;AACnD,eAAA,QAAQ,MAAM,6BAA6B,aAAa;AACrD,kBAAA,aAAa,IAAI,MAAM,KAAK;AAAA,YAClC,MAAM,UAAU,QAAQ,QAAQ;AAAA,YAChC;AAAA;AAAA,YACA,gBAAgB;AAAA,UAClB;AAAA,QAAA,OACK;AACL,eAAK,QAAQ,KAAK,gBAAgB,gBAAgB,yBAAyB;AAC3E,kBAAQ,aAAa,IAAI,EAAE,OAAO,oBAAoB;AAAA,QAAA;AAAA,eAEjD,OAAO;AACd,aAAK,QAAQ,MAAM,+CAA+C,gBAAgB,KAAK;AAAA,UACrF,OAAO,MAAM;AAAA,UACb,OAAO,MAAM,OAAO,UAAU,GAAG,GAAG;AAAA,UACpC;AAAA,UACA,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAAA,CACnC;AACD,gBAAQ,aAAa,IAAI;AAAA,UACvB,OAAO,MAAM;AAAA,UACb,UAAU,KAAK,wBAAwB,eAAe,QAAQ;AAAA,UAC9D,WAAW;AAAA,QACb;AAAA,MAAA;AAAA,IACF;AAGK,WAAA;AAAA,MACL,YAAY;AAAA,MACZ,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA,MACpC,UAAU;AAAA,IACZ;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,MAAM,mBAAmB,IAAI,SAAS,UAAU;AAC9C,WAAO,QAAQ,KAAK;AAAA,MAClB,GAAG;AAAA,MACH,IAAI;AAAA,QAAQ,CAAC,GAAG,WACd,WAAW,MAAM,OAAO,IAAI,MAAM,QAAQ,CAAC,GAAG,OAAO;AAAA,MAAA;AAAA,IACvD,CACD;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMH,wBAAwB,eAAe,UAAU;AACxC,WAAA;AAAA,MACL,UAAU;AAAA,MACV,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,MAAM;AAAA,IACR;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQF,4BAA4B,SAAS,UAAU;AACzC,QAAA;AACF,YAAM,WAAW;AAAA;AAAA,QAEf,YAAY;AAAA,UACV,YAAY,KAAK,yBAAyB,QAAQ;AAAA,UAClD,aAAa,KAAK,0BAA0B,QAAQ;AAAA,UACpD,cAAc,KAAK,2BAA2B,QAAQ;AAAA,UACtD,sBAAsB,KAAK,8BAA8B,QAAQ;AAAA,UACjE,mBAAmB,KAAK,gCAAgC,QAAQ;AAAA,QAClE;AAAA;AAAA,QAGA,WAAW;AAAA,UACT,WAAW,KAAK,wBAAwB,QAAQ;AAAA,UAChD,QAAQ,KAAK,qBAAqB,QAAQ;AAAA,UAC1C,iBAAiB,KAAK,8BAA8B,QAAQ;AAAA,UAC5D,mBAAmB,KAAK,gCAAgC,QAAQ;AAAA,UAChE,kBAAkB,KAAK,+BAA+B,QAAQ;AAAA,QAChE;AAAA;AAAA,QAGA,SAAS;AAAA,UACP,kBAAkB,KAAK,+BAA+B,QAAQ;AAAA,UAC9D,oBAAoB,KAAK,iCAAiC,QAAQ;AAAA,UAClE,mBAAmB,KAAK,gCAAgC,QAAQ;AAAA,UAChE,oBAAoB,KAAK,iCAAiC,QAAQ;AAAA,QACpE;AAAA;AAAA,QAGA,OAAO;AAAA,UACL,iBAAiB,KAAK,8BAA8B,QAAQ;AAAA,UAC5D,kBAAkB,KAAK,+BAA+B,QAAQ;AAAA,UAC9D,cAAc,KAAK,2BAA2B,QAAQ;AAAA,UACtD,eAAe,KAAK,4BAA4B,QAAQ;AAAA,QAC1D;AAAA;AAAA,QAGA,iBAAiB,KAAK,mCAAmC,SAAS,QAAQ;AAAA;AAAA,QAG1E,oBAAoB,KAAK,2BAA2B,SAAS,QAAQ;AAAA;AAAA,QAGrE,sBAAsB,KAAK,6BAA6B,SAAS,QAAQ;AAAA;AAAA,QAGzE,UAAU;AAAA,UACR,oBAAmB,oBAAI,KAAK,GAAE,YAAY;AAAA,UAC1C,UAAU,KAAK;AAAA,UACf,iBAAiB;AAAA,UACjB,iBAAiB,KAAK,iCAAiC,SAAS,QAAQ;AAAA,QAAA;AAAA,MAE5E;AAEO,aAAA;AAAA,aACA,OAAO;AACT,WAAA,QAAQ,MAAM,wCAAwC,KAAK;AACzD,aAAA,KAAK,oCAAoC,QAAQ;AAAA,IAAA;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA,EAKF,yBAAyB,UAAU;AAC3B,UAAA,eAAe,SAAS,gBAAgB,CAAC;AACzC,UAAA,YAAY,SAAS,aAAa;AAClC,UAAA,iBAAiB,SAAS,kBAAkB;AAElD,QAAI,QAAQ;AAGR,QAAA,aAAa,SAAS,GAAG;AAC3B,eAAS,KAAK,IAAI,IAAI,aAAa,SAAS,CAAC;AAAA,IAAA;AAI/C,QAAI,YAAY,KAAO;AACZ,eAAA;AAAA,IAAA;AAIX,aAAS,iBAAiB;AAE1B,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,0BAA0B,UAAU;AAC5B,UAAA,WAAW,SAAS,YAAY;AAChC,UAAA,SAAS,SAAS,UAAU;AAC5B,UAAA,aAAa,SAAS,cAAc;AAE1C,QAAI,QAAQ;AAER,QAAA,WAAW,KAAK,aAAa,KAAK;AAC3B,eAAA;AAAA,IAAA;AAGP,QAAA,SAAS,KAAK,aAAa,KAAK;AACzB,eAAA;AAAA,IAAA;AAGX,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,2BAA2B,UAAU;AAC7B,UAAA,oBAAoB,SAAS,qBAAqB;AAClD,UAAA,oBAAoB,SAAS,qBAAqB;AAExD,QAAI,QAAQ;AAEZ,QAAI,oBAAoB,GAAG;AACzB,eAAS,oBAAoB;AAAA,IAAA;AAG/B,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,8BAA8B,UAAU;AAChC,UAAA,SAAS,SAAS,UAAU;AAC5B,UAAA,YAAY,SAAS,aAAa;AAClC,UAAA,aAAa,SAAS,cAAc;AAE1C,QAAI,QAAQ;AAER,QAAA,SAAS,KAAK,CAAC,WAAW;AACnB,eAAA;AAAA,IAAA;AAGX,QAAI,aAAa,KAAK;AACX,eAAA;AAAA,IAAA;AAGX,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,gCAAgC,UAAU;AAElC,UAAA,aAAa,KAAK,yBAAyB,QAAQ;AAClD,WAAA,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,aAAa,GAAG,CAAC;AAAA,EAAA;AAAA,EAGpD,wBAAwB,UAAU;AAC1B,UAAA,YAAY,SAAS,aAAa;AAClC,UAAA,eAAe,SAAS,gBAAgB;AACxC,UAAA,eAAe,SAAS,uBAAuB;AAErD,QAAI,QAAQ;AAEZ,QAAI,YAAY,KAAO;AACZ,eAAA;AAAA,IAAA;AAGX,QAAI,eAAe,GAAG;AACX,eAAA;AAAA,IAAA;AAGX,QAAI,eAAe,KAAM;AACd,eAAA;AAAA,IAAA;AAGX,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,qBAAqB,UAAU;AACvB,UAAA,WAAW,SAAS,YAAY;AAChC,UAAA,WAAW,SAAS,YAAY,CAAC;AAEvC,QAAI,QAAQ;AAEZ,QAAI,WAAW,IAAI;AACR,eAAA;AAAA,IAAA;AAGP,QAAA,SAAS,SAAS,GAAG;AACd,eAAA;AAAA,IAAA;AAGX,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,8BAA8B,UAAU;AAChC,UAAA,eAAe,SAAS,uBAAuB;AAC/C,UAAA,WAAW,SAAS,YAAY;AAEtC,QAAI,QAAQ;AAER,QAAA,eAAe,QAAQ,WAAW,IAAI;AAC/B,eAAA;AAAA,IAAA,WACA,eAAe,MAAM;AACrB,eAAA;AAAA,IAAA;AAGX,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,gCAAgC,UAAU;AAClC,UAAA,mBAAmB,SAAS,oBAAoB;AAChD,UAAA,oBAAoB,SAAS,qBAAqB;AAClD,UAAA,gBAAgB,SAAS,iBAAiB;AAE1C,UAAA,SAAS,mBAAmB,oBAAoB,iBAAiB;AACvE,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,+BAA+B,UAAU;AACjC,UAAA,cAAc,SAAS,eAAe;AACtC,UAAA,iBAAiB,SAAS,kBAAkB;AAElD,QAAI,QAAQ;AAER,QAAA,cAAc,KAAK,iBAAiB,IAAI;AACjC,eAAA;AAAA,IAAA;AAGX,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,+BAA+B,UAAU;AAChC,WAAA,KAAK,+BAA+B,QAAQ;AAAA,EAAA;AAAA,EAGrD,iCAAiC,UAAU;AACnC,UAAA,gBAAgB,SAAS,iBAAiB;AAC1C,UAAA,mBAAmB,SAAS,oBAAoB;AAEtD,QAAI,QAAQ;AAEZ,QAAI,gBAAgB,GAAG;AACb,cAAA;AAAA,IAAA;AAGV,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,gCAAgC,UAAU;AAClC,UAAA,oBAAoB,SAAS,qBAAqB;AAClD,UAAA,gBAAgB,SAAS,iBAAiB;AAEhD,QAAI,QAAQ;AAEZ,QAAI,oBAAoB,GAAG;AACjB,cAAA;AAAA,IAAA;AAGV,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,iCAAiC,UAAU;AACnC,UAAA,SAAS,KAAK,+BAA+B,QAAQ;AACrD,UAAA,WAAW,KAAK,iCAAiC,QAAQ;AACzD,UAAA,UAAU,KAAK,gCAAgC,QAAQ;AAErD,YAAA,SAAS,WAAW,WAAW;AAAA,EAAA;AAAA,EAGzC,8BAA8B,UAAU;AAChC,UAAA,YAAY,SAAS,aAAa;AAClC,UAAA,eAAe,SAAS,gBAAgB;AAE9C,YAAQ,YAAY,gBAAgB;AAAA,EAAA;AAAA,EAGtC,+BAA+B,UAAU;AACjC,UAAA,YAAY,SAAS,aAAa;AAClC,UAAA,eAAe,SAAS,gBAAgB;AAE9C,QAAI,QAAQ;AAEZ,QAAI,YAAY,IAAI;AACV,cAAA;AAAA,IAAA;AAGV,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,2BAA2B,UAAU;AAC7B,UAAA,sBAAsB,SAAS,uBAAuB;AACtD,UAAA,wBAAwB,SAAS,yBAAyB;AAEhE,YAAQ,sBAAsB,yBAAyB;AAAA,EAAA;AAAA,EAGzD,4BAA4B,UAAU;AAC9B,UAAA,gBAAgB,SAAS,iBAAiB;AAC1C,UAAA,mBAAmB,SAAS,oBAAoB;AAEtD,QAAI,QAAQ;AAEZ,QAAI,gBAAgB,GAAG;AACb,cAAA;AAAA,IAAA;AAGV,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,mCAAmC,SAAS,UAAU;AACpD,UAAM,kBAAkB,CAAC;AAGnB,UAAA,aAAa,KAAK,yBAAyB,QAAQ;AACzD,QAAI,aAAa,IAAI;AACnB,sBAAgB,KAAK;AAAA,QACnB,UAAU;AAAA,QACV,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,WAAW;AAAA,MAAA,CACZ;AAAA,IAAA;AAIG,UAAA,YAAY,KAAK,wBAAwB,QAAQ;AACvD,QAAI,YAAY,IAAI;AAClB,sBAAgB,KAAK;AAAA,QACnB,UAAU;AAAA,QACV,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,WAAW;AAAA,MAAA,CACZ;AAAA,IAAA;AAIG,UAAA,aAAa,KAAK,8BAA8B,QAAQ;AAC9D,QAAI,aAAa,IAAI;AACnB,sBAAgB,KAAK;AAAA,QACnB,UAAU;AAAA,QACV,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,WAAW;AAAA,MAAA,CACZ;AAAA,IAAA;AAGI,WAAA;AAAA,EAAA;AAAA,EAGT,2BAA2B,SAAS,UAAU;AACrC,WAAA;AAAA,MACL,iBAAiB,KAAK,yBAAyB,QAAQ;AAAA,MACvD,eAAe,KAAK,sBAAsB,QAAQ;AAAA,MAClD,gBAAgB,KAAK,uBAAuB,QAAQ;AAAA,MACpD,kBAAkB,KAAK,yBAAyB,QAAQ;AAAA,MACxD,YAAY,KAAK,mBAAmB,QAAQ;AAAA,IAC9C;AAAA,EAAA;AAAA,EAGF,6BAA6B,SAAS,UAAU;AAEvC,WAAA;AAAA,MACL,UAAU,KAAK;AAAA,MACf,iBAAiB;AAAA,MACjB,iBAAiB,KAAK,yBAAyB,QAAQ;AAAA,MACvD,iBAAiB,KAAK,wBAAwB,QAAQ;AAAA,IACxD;AAAA,EAAA;AAAA,EAGF,iCAAiC,SAAS,UAAU;AAClD,QAAI,aAAa;AAGjB,UAAM,aAAa,OAAO,KAAK,QAAQ,EAAE;AACrC,QAAA,aAAa,GAAkB,eAAA;AAAA,aAC1B,aAAa,EAAiB,eAAA;AAGvC,UAAM,eAAe,OAAO,KAAK,OAAO,EAAE;AACtC,QAAA,eAAe,EAAiB,eAAA;AAAA,aAC3B,eAAe,EAAiB,eAAA;AAGnC,UAAA,cAAc,SAAS,aAAa;AACtC,QAAA,cAAc,IAAqB,eAAA;AAEvC,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,UAAU,CAAC;AAAA,EAAA;AAAA,EAG9C,oCAAoC,UAAU;AACrC,WAAA;AAAA,MACL,YAAY,EAAE,YAAY,IAAI,aAAa,IAAI,cAAc,IAAI,sBAAsB,IAAI,mBAAmB,GAAG;AAAA,MACjH,WAAW,EAAE,WAAW,IAAI,QAAQ,IAAI,iBAAiB,IAAI,mBAAmB,IAAI,kBAAkB,GAAG;AAAA,MACzG,SAAS,EAAE,kBAAkB,IAAI,oBAAoB,IAAI,mBAAmB,IAAI,oBAAoB,GAAG;AAAA,MACvG,OAAO,EAAE,iBAAiB,IAAI,kBAAkB,IAAI,cAAc,IAAI,eAAe,GAAG;AAAA,MACxF,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,EAAE,iBAAiB,IAAI,eAAe,CAAC,GAAG,gBAAgB,CAAA,GAAI,kBAAkB,IAAI,YAAY,CAAA,EAAG;AAAA,MACvH,sBAAsB,EAAE,UAAU,KAAK,UAAU,iBAAiB,CAAC,GAAG,iBAAiB,IAAI,iBAAiB,GAAG;AAAA,MAC/G,UAAU,EAAE,oBAAmB,oBAAI,KAAO,GAAA,YAAA,GAAe,UAAU,KAAK,UAAU,iBAAiB,SAAS,iBAAiB,GAAG;AAAA,IAClI;AAAA,EAAA;AAAA,EAGF,yBAAyB,UAAU;AAC3B,UAAA,WAAW,SAAS,YAAY;AAChC,UAAA,aAAa,SAAS,cAAc;AACpC,UAAA,aAAa,KAAK,yBAAyB,QAAQ;AAEjD,YAAA,WAAW,aAAa,cAAc;AAAA,EAAA;AAAA,EAGhD,sBAAsB,UAAU;AAC9B,UAAM,YAAY,CAAC;AAEnB,QAAI,SAAS,WAAW,GAAI,WAAU,KAAK,UAAU;AACrD,QAAI,SAAS,sBAAsB,IAAM,WAAU,KAAK,wBAAwB;AAChF,QAAI,KAAK,yBAAyB,QAAQ,IAAI,GAAI,WAAU,KAAK,aAAa;AAEvE,WAAA;AAAA,EAAA;AAAA,EAGT,uBAAuB,UAAU;AAC/B,UAAM,aAAa,CAAC;AAEpB,QAAI,SAAS,WAAW,GAAI,YAAW,KAAK,UAAU;AACtD,QAAI,SAAS,sBAAsB,IAAM,YAAW,KAAK,wBAAwB;AACjF,QAAI,KAAK,yBAAyB,QAAQ,IAAI,GAAI,YAAW,KAAK,aAAa;AAExE,WAAA;AAAA,EAAA;AAAA,EAGT,yBAAyB,UAAU;AACjC,UAAM,QAAQ,CAAC;AAEX,QAAA,SAAS,WAAW,IAAI;AAC1B,YAAM,KAAK,6BAA6B;AAAA,IAAA;AAGtC,QAAA,SAAS,sBAAsB,KAAM;AACvC,YAAM,KAAK,oDAAoD;AAAA,IAAA;AAG1D,WAAA;AAAA,EAAA;AAAA,EAGT,mBAAmB,UAAU;AACpB,WAAA;AAAA,MACL,EAAE,WAAW,4BAA4B,UAAU,SAAS,aAAa,IAAI;AAAA,MAC7E,EAAE,WAAW,yBAAyB,UAAU,SAAS,WAAW,GAAG;AAAA,MACvE,EAAE,WAAW,0BAA0B,UAAU,KAAK,yBAAyB,QAAQ,IAAI,GAAG;AAAA,IAChG;AAAA,EAAA;AAAA,EAGF,yBAAyB,UAAU;AAC3B,UAAA,WAAW,SAAS,YAAY;AAChC,UAAA,aAAa,SAAS,cAAc;AACpC,UAAA,aAAa,SAAS,cAAc;AAElC,YAAA,WAAW,aAAa,cAAc;AAAA,EAAA;AAAA,EAGhD,wBAAwB,UAAU;AAChC,UAAM,QAAQ,CAAC;AAEX,QAAA,SAAS,WAAW,IAAI;AAC1B,YAAM,KAAK,qBAAqB;AAAA,IAAA;AAG9B,QAAA,SAAS,sBAAsB,KAAM;AACvC,YAAM,KAAK,uBAAuB;AAAA,IAAA;AAGpC,QAAI,KAAK,yBAAyB,QAAQ,IAAI,IAAI;AAChD,YAAM,KAAK,kCAAkC;AAAA,IAAA;AAGxC,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQT,0BAA0B,SAAS,UAAU;AACvC,QAAA;AACK,aAAA;AAAA;AAAA,QAEL,OAAO;AAAA,UACL,UAAU,SAAS,YAAY;AAAA,UAC/B,cAAc,SAAS,uBAAuB;AAAA,UAC9C,YAAY,SAAS,cAAc;AAAA,UACnC,OAAO,SAAS,SAAS;AAAA,UACzB,UAAU,SAAS,aAAa;AAAA,UAChC,UAAU,SAAS,YAAY;AAAA,UAC/B,QAAQ,SAAS,UAAU;AAAA,QAC7B;AAAA;AAAA,QAGA,WAAW;AAAA,UACT,WAAW,KAAK,wBAAwB,QAAQ;AAAA,UAChD,QAAQ,KAAK,qBAAqB,QAAQ;AAAA,UAC1C,iBAAiB,KAAK,8BAA8B,QAAQ;AAAA,UAC5D,mBAAmB,KAAK,gCAAgC,QAAQ;AAAA,QAClE;AAAA;AAAA,QAGA,YAAY;AAAA,UACV,YAAY,KAAK,yBAAyB,QAAQ;AAAA,UAClD,aAAa,KAAK,0BAA0B,QAAQ;AAAA,UACpD,cAAc,KAAK,2BAA2B,QAAQ;AAAA,UACtD,sBAAsB,KAAK,8BAA8B,QAAQ;AAAA,QACnE;AAAA;AAAA,QAGA,SAAS;AAAA,UACP,kBAAkB,KAAK,+BAA+B,QAAQ;AAAA,UAC9D,oBAAoB,KAAK,iCAAiC,QAAQ;AAAA,UAClE,mBAAmB,KAAK,gCAAgC,QAAQ;AAAA,UAChE,oBAAoB,KAAK,iCAAiC,QAAQ;AAAA,QACpE;AAAA;AAAA,QAGA,OAAO;AAAA,UACL,iBAAiB,KAAK,8BAA8B,QAAQ;AAAA,UAC5D,kBAAkB,KAAK,+BAA+B,QAAQ;AAAA,UAC9D,cAAc,KAAK,2BAA2B,QAAQ;AAAA,UACtD,eAAe,KAAK,4BAA4B,QAAQ;AAAA,QAC1D;AAAA;AAAA,QAGA,cAAc;AAAA;AAAA,QAGd,UAAU;AAAA,UACR,UAAU,KAAK;AAAA,UACf,sBAAqB,oBAAI,KAAK,GAAE,YAAY;AAAA,UAC5C,SAAS;AAAA,QAAA;AAAA,MAEb;AAAA,aACO,OAAO;AACT,WAAA,QAAQ,MAAM,4CAA4C,KAAK;AAC7D,aAAA;AAAA,QACL,OAAO,EAAE,UAAU,GAAG,cAAc,GAAG,YAAY,GAAG,OAAO,GAAG,UAAU,GAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,QACpG,WAAW,EAAE,WAAW,IAAI,QAAQ,IAAI,iBAAiB,IAAI,mBAAmB,GAAG;AAAA,QACnF,YAAY,EAAE,YAAY,IAAI,aAAa,IAAI,cAAc,IAAI,sBAAsB,GAAG;AAAA,QAC1F,SAAS,EAAE,kBAAkB,IAAI,oBAAoB,IAAI,mBAAmB,IAAI,oBAAoB,GAAG;AAAA,QACvG,OAAO,EAAE,iBAAiB,IAAI,kBAAkB,IAAI,cAAc,IAAI,eAAe,GAAG;AAAA,QACxF,cAAc;AAAA,QACd,UAAU,EAAE,UAAU,KAAK,UAAU,sBAAyB,oBAAA,KAAO,GAAA,YAAe,GAAA,SAAS,QAAQ;AAAA,MACvG;AAAA,IAAA;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAMF,2BAA2B,UAAU;AAC7B,UAAA,EAAE,eAAe,GAAG,gBAAgB,GAAG,eAAe,OAAO;AAC5D,WAAA;AAAA,MACL,UAAU,KAAK,MAAO,eAAe,gBAAiB,GAAG;AAAA,MACzD;AAAA,MACA;AAAA,MACA,cAAc,KAAK,sBAAsB,YAAY;AAAA,MACrD,YAAY,KAAK,oBAAoB,YAAY;AAAA,IACnD;AAAA,EAAA;AAAA,EAGF,6BAA6B,UAAU;AACrC,UAAM,EAAE,eAAe,CAAC,EAAA,IAAM;AACvB,WAAA;AAAA,MACL,mBAAmB,KAAK,2BAA2B,YAAY;AAAA,MAC/D,cAAc,KAAK,sBAAsB,YAAY;AAAA,MACrD,aAAa,KAAK,qBAAqB,YAAY;AAAA,IACrD;AAAA,EAAA;AAAA,EAGF,0BAA0B,UAAU;AAClC,UAAM,EAAE,eAAe,CAAC,EAAA,IAAM;AACxB,UAAA,gBAAgB,aAAa,IAAI,CAAK,MAAA,EAAE,gBAAgB,CAAC,EAAE,OAAO,CAAK,MAAA,IAAI,CAAC;AAE9E,QAAA,cAAc,WAAW,GAAG;AACvB,aAAA,EAAE,SAAS,GAAG,QAAQ,GAAG,aAAa,GAAG,SAAS,oBAAoB;AAAA,IAAA;AAGzE,UAAA,UAAU,cAAc,OAAO,CAAC,KAAK,SAAS,MAAM,MAAM,CAAC,IAAI,cAAc;AACnF,UAAM,SAAS,cAAc,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AACjD,UAAM,SAAS,OAAO,KAAK,MAAM,OAAO,SAAS,CAAC,CAAC;AAE5C,WAAA;AAAA,MACL,SAAS,KAAK,MAAM,OAAO;AAAA,MAC3B,QAAQ,KAAK,MAAM,MAAM;AAAA,MACzB,KAAK,KAAK,IAAI,GAAG,aAAa;AAAA,MAC9B,KAAK,KAAK,IAAI,GAAG,aAAa;AAAA,MAC9B,aAAa,KAAK,MAAM,KAAK,qBAAqB,aAAa,CAAC;AAAA,MAChE,SAAS;AAAA,IACX;AAAA,EAAA;AAAA,EAGF,4BAA4B,UAAU;AACpC,UAAM,EAAE,eAAe,CAAC,EAAA,IAAM;AACvB,WAAA;AAAA,MACL,eAAe,aAAa;AAAA,MAC5B,iBAAiB,aAAa,OAAO,CAAK,MAAA,EAAE,OAAO,EAAE;AAAA,MACrD,cAAc,KAAK,qBAAqB,YAAY;AAAA,MACpD,eAAe,KAAK,sBAAsB,YAAY;AAAA,IACxD;AAAA,EAAA;AAAA,EAGF,4BAA4B,UAAU;AACpC,UAAM,EAAE,eAAe,CAAC,EAAA,IAAM;AACvB,WAAA;AAAA,MACL,aAAa,KAAK,qBAAqB,YAAY;AAAA,MACnD,cAAc,KAAK,sBAAsB,YAAY;AAAA,MACrD,YAAY,KAAK,yBAAyB,QAAQ;AAAA,IACpD;AAAA,EAAA;AAAA,EAGF,6BAA6B,UAAU;AACrC,UAAM,EAAE,eAAe,CAAC,EAAA,IAAM;AACvB,WAAA;AAAA,MACL,mBAAmB,KAAK,2BAA2B,YAAY;AAAA,MAC/D,eAAe,KAAK,uBAAuB,YAAY;AAAA,MACvD,iBAAiB,KAAK,yBAAyB,YAAY;AAAA,IAC7D;AAAA,EAAA;AAAA,EAGF,oCAAoC,UAAU;AAC5C,UAAM,kBAAkB,CAAC;AACnB,UAAA,WAAW,SAAS,YAAY;AAChC,UAAA,eAAe,SAAS,uBAAuB;AAErD,QAAI,WAAW,IAAI;AACjB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,aAAa;AAAA,MAAA,CACd;AAAA,IAAA;AAGH,QAAI,eAAe,KAAM;AACvB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,aAAa;AAAA,MAAA,CACd;AAAA,IAAA;AAGI,WAAA;AAAA,EAAA;AAAA;AAAA,EAIT,sBAAsB,cAAc;AAClC,UAAM,sBAAsB,aAAa,OAAO,CAAA,MAAK,EAAE,OAAO;AAC9D,WAAO,oBAAoB,SAAS,KAAK,IAAI,GAAG,aAAa,MAAM,IAAI;AAAA,EAAA;AAAA,EAGzE,2BAA2B,cAAc;AAChC,WAAA,KAAK,sBAAsB,YAAY;AAAA,EAAA;AAAA,EAGhD,oBAAoB,cAAc;AAC1B,UAAA,YAAY,aAAa,OAAO,CAAC,KAAK,MAAM,OAAO,EAAE,gBAAgB,IAAI,CAAC;AAChF,UAAM,eAAe,aAAa,OAAO,CAAK,MAAA,EAAE,OAAO,EAAE;AACzD,WAAO,YAAY,IAAK,eAAe,YAAa,MAAO;AAAA,EAAA;AAAA,EAG7D,qBAAqB,cAAc;AACjC,UAAM,QAAQ,CAAC;AACf,iBAAa,QAAQ,CAAK,MAAA;AAClB,YAAA,OAAO,EAAE,eAAe;AAC9B,YAAM,IAAI,KAAK,MAAM,IAAI,KAAK,KAAK;AAAA,IAAA,CACpC;AACM,WAAA;AAAA,EAAA;AAAA,EAGT,sBAAsB,cAAc;AAClC,UAAM,SAAS,aAAa,OAAO,CAAK,MAAA,CAAC,EAAE,OAAO;AAC3C,WAAA;AAAA,MACL,aAAa,OAAO;AAAA,MACpB,gBAAgB,OAAO,SAAS,KAAK,IAAI,GAAG,aAAa,MAAM,IAAI;AAAA,IACrE;AAAA,EAAA;AAAA,EAGF,qBAAqB,QAAQ;AACvB,QAAA,OAAO,SAAS,EAAU,QAAA;AACxB,UAAA,OAAO,OAAO,OAAO,CAAC,KAAK,MAAM,MAAM,GAAG,CAAC,IAAI,OAAO;AAC5D,UAAM,WAAW,OAAO,OAAO,CAAC,KAAK,MAAM,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,OAAO;AAC7E,WAAA,KAAK,KAAK,QAAQ;AAAA,EAAA;AAAA;AAAA,EAI3B,0BAA0B,UAAU,QAAQ;AAC1C,UAAM,oBAAoB,SAAS,OAAO,CAAK,MAAA,EAAE,OAAO,EAAE;AACpD,UAAA,gBAAgB,SAAS,UAAU;AACzC,UAAM,WAAW,oBAAoB;AAG/B,UAAA,kBAAkB,OAAO,SAAS,IAAI,KAAK,IAAI,OAAO,SAAS,IAAI,CAAC,IAAI;AACxE,UAAA,eAAgB,WAAW,MAAQ,kBAAkB;AAEpD,WAAA;AAAA,MACL,OAAO,KAAK,MAAM,eAAe,GAAG;AAAA,MACpC;AAAA,MACA,YAAY;AAAA,MACZ,OAAO,eAAe,MAAM,aAAa,eAAe,MAAM,iBAAiB;AAAA,IACjF;AAAA,EAAA;AAAA,EAGF,wBAAwB,UAAU,WAAW;AACrC,UAAA,gBAAgB,SAAS,UAAU;AACnC,UAAA,aAAa,YAAY,KAAK,IAAI,GAAG,KAAK,gBAAgB,KAAK,EAAE,IAAI;AACrE,UAAA,kBAAkB,YAAY,MAAM;AAEpC,UAAA,sBAAuB,aAAa,MAAO;AAE1C,WAAA;AAAA,MACL,OAAO,KAAK,MAAM,sBAAsB,GAAG;AAAA,MAC3C;AAAA,MACA;AAAA,MACA,UAAU,gBAAgB,IAAI,cAAc,gBAAgB,KAAK,eAAe;AAAA,IAClF;AAAA,EAAA;AAAA,EAGF,qBAAqB,UAAU,WAAW;AACxC,UAAM,iBAAiB,YAAY,IAAI,YAAY,SAAS,SAAS;AACrE,UAAM,uBAAuB,SAAS,SAAS,IAAI,IAAI,SAAS,SAAS;AACzE,UAAM,kBAAkB,iBAAiB,MAAO,IAAI,iBAAiB;AAE/D,UAAA,mBAAoB,uBAAuB,MAAQ,kBAAkB;AAEpE,WAAA;AAAA,MACL,OAAO,KAAK,MAAM,mBAAmB,GAAG;AAAA,MACxC,UAAU,SAAS;AAAA,MACnB;AAAA,MACA,OAAO,mBAAmB,MAAM,SAAS,mBAAmB,MAAM,aAAa;AAAA,IACjF;AAAA,EAAA;AAAA,EAGF,0BAA0B,UAAU,QAAQ;AAC1C,UAAM,mBAAmB,OAAO,SAAS,IAAI,OAAO,SAAS,KAAK;AAClE,UAAM,qBAAqB,SAAS,SAAS,IAC3C,SAAS,OAAO,CAAK,MAAA,EAAE,OAAO,EAAE,SAAS,SAAS,SAAS;AAEvD,UAAA,cAAe,qBAAqB,MAAQ,mBAAmB;AAE9D,WAAA;AAAA,MACL,OAAO,KAAK,MAAM,cAAc,GAAG;AAAA,MACnC,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,YAAY,cAAc,MAAM,cAAc;AAAA,IAChD;AAAA,EAAA;AAAA,EAGF,qBAAqB,UAAU;AAC7B,UAAM,kBAAkB,SAAS,SAAS,IACxC,SAAS,OAAO,CAAC,KAAK,MAAM,OAAO,EAAE,gBAAgB,MAAO,CAAC,IAAI,SAAS,SAAS;AAErF,UAAM,kBAAkB,KAAK,IAAI,GAAG,KAAK,kBAAkB,OAAQ,GAAI;AACvE,UAAM,YAAY,SAAS,SAAS,IAClC,SAAS,OAAO,CAAK,MAAA,EAAE,OAAO,EAAE,SAAS,SAAS,SAAS;AAEvD,UAAA,aAAc,kBAAkB,MAAQ,YAAY;AAEnD,WAAA;AAAA,MACL,OAAO,KAAK,MAAM,aAAa,GAAG;AAAA,MAClC,cAAc;AAAA,MACd;AAAA,MACA,OAAO,aAAa,MAAM,YAAY,aAAa,MAAM,eAAe;AAAA,IAC1E;AAAA,EAAA;AAAA,EAKF,iBAAiB,UAAU;AACrB,QAAA,SAAS,SAAS,EAAU,QAAA;AAE1B,UAAA,YAAY,SAAS,OAAO,CAAA,MAAK,CAAC,EAAE,OAAO,EAAE,SAAS,SAAS;AACrE,UAAM,UAAU,SAAS,OAAO,CAAC,KAAK,MAAM,OAAO,EAAE,gBAAgB,MAAO,CAAC,IAAI,SAAS;AAE1F,QAAI,YAAY,OAAO,UAAU,IAAa,QAAA;AAC9C,QAAI,YAAY,OAAO,UAAU,IAAa,QAAA;AAC1C,QAAA,YAAY,IAAY,QAAA;AACrB,WAAA;AAAA,EAAA;AAAA,EAGT,yBAAyB,UAAU,QAAQ;AACnC,UAAA,aAAa,OAAO,UAAU;AACpC,UAAM,cAAc,SAAS,SAAS,IACpC,SAAS,OAAO,CAAK,MAAA,EAAE,OAAO,EAAE,SAAS,SAAS,SAAS;AAEvD,UAAA,kBAAkB,eAAe,IAAI,aAAa;AAEjD,WAAA;AAAA,MACL,OAAO,KAAK,MAAM,KAAK,IAAI,KAAK,kBAAkB,GAAG,CAAC;AAAA,MACtD;AAAA,MACA;AAAA,MACA,UAAU,kBAAkB,MAAM,cAAc,kBAAkB,MAAM,SAAS;AAAA,IACnF;AAAA,EAAA;AAIJ;AC3gCO,MAAM,uBAAuB;AAAA;AAAA,EAElC,iBAAiB;AAAA,IACf,eAAe;AAAA,MACb,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,kBAAkB,CAAC,sBAAsB,oBAAoB,iBAAiB;AAAA,MAC9E,gBAAgB,CAAC,sBAAsB,gBAAgB,oBAAoB;AAAA,MAC3E,cAAc;AAAA,QACZ,MAAM;AAAA,UACJ,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,WAAW;AAAA,UACX,WAAW;AAAA,UACX,aAAa;AAAA,QACf;AAAA,QACA,QAAQ;AAAA,UACN,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,WAAW;AAAA,UACX,WAAW;AAAA,UACX,aAAa;AAAA,QACf;AAAA,QACA,MAAM;AAAA,UACJ,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,WAAW;AAAA,UACX,WAAW;AAAA,UACX,aAAa;AAAA,QAAA;AAAA,MAEjB;AAAA,MACA,SAAS,CAAC,mBAAmB,4BAA4B,uBAAuB,gBAAgB;AAAA,IAClG;AAAA,IAEA,iBAAiB;AAAA,MACf,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,kBAAkB,CAAC,yBAAyB,kBAAkB,uBAAuB;AAAA,MACrF,gBAAgB,CAAC,uBAAuB,UAAU,WAAW;AAAA,MAC7D,cAAc;AAAA,QACZ,MAAM;AAAA,UACJ,gBAAgB;AAAA,UAChB,cAAc;AAAA,UACd,mBAAmB;AAAA,UACnB,cAAc;AAAA,QAChB;AAAA,QACA,QAAQ;AAAA,UACN,gBAAgB;AAAA,UAChB,cAAc;AAAA,UACd,mBAAmB;AAAA,UACnB,cAAc;AAAA,QAChB;AAAA,QACA,MAAM;AAAA,UACJ,gBAAgB;AAAA,UAChB,cAAc;AAAA,UACd,mBAAmB;AAAA,UACnB,cAAc;AAAA,QAAA;AAAA,MAElB;AAAA,MACA,SAAS,CAAC,yBAAyB,mBAAmB,uBAAuB,qBAAqB;AAAA,IACpG;AAAA,IAEA,yBAAyB;AAAA,MACvB,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,kBAAkB,CAAC,0BAA0B,mBAAmB,mBAAmB;AAAA,MACnF,gBAAgB,CAAC,qBAAqB,qBAAqB,uBAAuB;AAAA,MAClF,cAAc;AAAA,QACZ,MAAM;AAAA,UACJ,gBAAgB,CAAC,IAAI,GAAG;AAAA,UACxB,YAAY;AAAA,UACZ,iBAAiB;AAAA,UACjB,cAAc;AAAA,QAChB;AAAA,QACA,QAAQ;AAAA,UACN,gBAAgB,CAAC,IAAI,KAAK,GAAG;AAAA,UAC7B,YAAY;AAAA,UACZ,iBAAiB;AAAA,UACjB,cAAc;AAAA,QAChB;AAAA,QACA,MAAM;AAAA,UACJ,gBAAgB,CAAC,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,UAChD,YAAY;AAAA,UACZ,iBAAiB;AAAA,UACjB,cAAc;AAAA,QAAA;AAAA,MAElB;AAAA,MACA,SAAS,CAAC,qBAAqB,yBAAyB,kCAAkC,gBAAgB;AAAA,IAC5G;AAAA,IAEA,sBAAsB;AAAA,MACpB,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,kBAAkB,CAAC,kBAAkB,yBAAyB,uBAAuB;AAAA,MACrF,gBAAgB,CAAC,sBAAsB,qBAAqB,qBAAqB;AAAA,MACjF,cAAc;AAAA,QACZ,MAAM;AAAA,UACJ,YAAY;AAAA,UACZ,mBAAmB;AAAA,UACnB,eAAe;AAAA,UACf,iBAAiB,CAAC,SAAS,SAAS;AAAA,QACtC;AAAA,QACA,QAAQ;AAAA,UACN,YAAY;AAAA,UACZ,mBAAmB;AAAA,UACnB,eAAe;AAAA,UACf,iBAAiB,CAAC,WAAW,WAAW,QAAQ;AAAA,QAClD;AAAA,QACA,MAAM;AAAA,UACJ,YAAY;AAAA,UACZ,mBAAmB;AAAA,UACnB,eAAe;AAAA,UACf,iBAAiB,CAAC,WAAW,WAAW,UAAU,OAAO;AAAA,QAAA;AAAA,MAE7D;AAAA,MACA,SAAS,CAAC,2BAA2B,oBAAoB,wBAAwB,kBAAkB;AAAA,IACrG;AAAA,IAEA,wBAAwB;AAAA,MACtB,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,kBAAkB,CAAC,uBAAuB,qBAAqB,qBAAqB;AAAA,MACpF,gBAAgB,CAAC,sBAAsB,oBAAoB,qBAAqB;AAAA,MAChF,cAAc;AAAA,QACZ,MAAM;AAAA,UACJ,eAAe;AAAA,UACf,cAAc,CAAC,eAAe,YAAY;AAAA,UAC1C,WAAW;AAAA,UACX,mBAAmB;AAAA,QACrB;AAAA,QACA,QAAQ;AAAA,UACN,eAAe;AAAA,UACf,cAAc,CAAC,eAAe,cAAc,aAAa;AAAA,UACzD,WAAW;AAAA,UACX,mBAAmB;AAAA,QACrB;AAAA,QACA,MAAM;AAAA,UACJ,eAAe;AAAA,UACf,cAAc,CAAC,eAAe,cAAc,eAAe,SAAS;AAAA,UACpE,WAAW;AAAA,UACX,mBAAmB;AAAA,QAAA;AAAA,MAEvB;AAAA,MACA,SAAS,CAAC,6BAA6B,oBAAoB,sBAAsB,sBAAsB;AAAA,IACzG;AAAA,IAEA,uBAAuB;AAAA,MACrB,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,kBAAkB,CAAC,sBAAsB,iBAAiB,wBAAwB;AAAA,MAClF,gBAAgB,CAAC,oBAAoB,wBAAwB,wBAAwB;AAAA,MACrF,cAAc;AAAA,QACZ,MAAM;AAAA,UACJ,UAAU;AAAA,UACV,oBAAoB;AAAA,UACpB,cAAc;AAAA,UACd,sBAAsB;AAAA,QACxB;AAAA,QACA,QAAQ;AAAA,UACN,UAAU;AAAA,UACV,oBAAoB;AAAA,UACpB,cAAc;AAAA,UACd,sBAAsB;AAAA,QACxB;AAAA,QACA,MAAM;AAAA,UACJ,UAAU;AAAA,UACV,oBAAoB;AAAA,UACpB,cAAc;AAAA,UACd,sBAAsB;AAAA,QAAA;AAAA,MAE1B;AAAA,MACA,SAAS,CAAC,qBAAqB,+BAA+B,2BAA2B,sBAAsB;AAAA,IAAA;AAAA,EAEnH;AAAA;AAAA,EAGA,kBAAkB;AAAA,IAChB,OAAO;AAAA,MACL;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,QAC/B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACpB;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,OAAO,MAAM,IAAI;AAAA,QAChC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACpB;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,KAAK,GAAG;AAAA,QAC7B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACpB;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,MAAM,GAAG;AAAA,QAC9B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACpB;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,OAAO,IAAI;AAAA,QAChC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACpB;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,QAC/B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MAAA;AAAA,IAEtB;AAAA,IAEA,cAAc;AAAA,MACZ;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,KAAK,MAAM,IAAI;AAAA,QAC9B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACpB;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,KAAK,IAAI;AAAA,QAC9B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACpB;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,QAC/B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACpB;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,MAAM,GAAG;AAAA,QAC9B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MAAA;AAAA,IAEtB;AAAA,IAEA,UAAU;AAAA,MACR;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,MAAM,GAAG;AAAA,QAC9B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACpB;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,QAC/B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACpB;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,MAAM,KAAK;AAAA,QAChC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MAAA;AAAA,IACpB;AAAA,EAEJ;AAAA;AAAA,EAGA,oBAAoB;AAAA,IAClB,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,aAAa;AAAA,MACb,OAAO;AAAA,MACP,MAAM;AAAA,MACN,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,mBAAmB;AAAA,IACrB;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,OAAO;AAAA,MACP,MAAM;AAAA,MACN,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,mBAAmB;AAAA,IACrB;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,aAAa;AAAA,MACb,OAAO;AAAA,MACP,MAAM;AAAA,MACN,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,mBAAmB;AAAA,IAAA;AAAA,EAEvB;AAAA;AAAA,EAwDA,eAAe;AAAA,IACb,oBAAoB;AAAA,MAClB,MAAM,EAAE,MAAM,IAAI,OAAO,GAAG,YAAY,EAAE;AAAA,MAC1C,QAAQ,EAAE,MAAM,IAAI,OAAO,GAAG,YAAY,EAAE;AAAA,MAC5C,MAAM,EAAE,MAAM,IAAI,OAAO,IAAI,YAAY,EAAE;AAAA,IAAA;AAAA,EAS/C;AA4CF;AAGO,MAAM,qBAAqB;AAAA;AAAA,EAEhC,UAAU,qBAAqB,iBAAiB;AAAA,EAChD,cAAc;AAAA,IACZ,EAAE,IAAI,QAAQ,MAAM,SAAS,QAAQ,GAAG,UAAU,EAAE;AAAA,IACpD,EAAE,IAAI,UAAU,MAAM,SAAS,QAAQ,GAAG,UAAU,EAAE;AAAA,IACtD,EAAE,IAAI,QAAQ,MAAM,WAAW,QAAQ,GAAG,UAAU,EAAE;AAAA,EACxD;AAAA,EACA,qBAAqB;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,cAAc,qBAAqB;AAErC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrdA,MAAMA,iBAAiB;AAAA,EACrBC,gBAAgB;AAAA,IACdC,IAAI;AAAA,IACJC,MAAM;AAAA,IACNC,MAAM;AAAA,IACNC,aAAa;AAAA,IACbC,mBAAmB;AAAA,IACnBC,WAAW;AAAA,EACb;AAAA,EACAC,gBAAgB;AAAA,IACdN,IAAI;AAAA,IACJC,MAAM;AAAA,IACNC,MAAM;AAAA,IACNC,aAAa;AAAA,IACbC,mBAAmB;AAAA,IACnBC,WAAW;AAAA,EACb;AAAA,EACAE,gBAAgB;AAAA,IACdP,IAAI;AAAA,IACJC,MAAM;AAAA,IACNC,MAAM;AAAA,IACNC,aAAa;AAAA,IACbC,mBAAmB;AAAA,IACnBC,WAAW;AAAA,EAAA;AAGf;AAEA,SAASG,iBAAiB;AAAA,EAAEC;AAAO,GAAG;AAC9B,QAAA;AAAA,IAAEC;AAAAA,IAAMC,aAAa;AAAA,EAAA,IAASC,aAAAA,WAAWC,aAAa;AACtD,QAAA;AAAA,IAAEC;AAAAA,MAAaC,wBAAwB;AAI1BC,eAAAA,OAAO,IAAI;AAG9B,QAAM,CAACC,WAAWC,YAAY,IAAIC,sBAAS;AAAA,IACzCC,QAAQ;AAAA;AAAA,IACRC,OAAO;AAAA,IACPC,OAAO;AAAA,IACPC,OAAO;AAAA,IACPC,aAAa;AAAA,IACbC,YAAY;AAAA,IACZC,UAAU;AAAA,IACVC,gBAAgB;AAAA;AAAA,IAGhBC,iBAAiB9B,eAAeC,eAAeC;AAAAA,IAC/C6B,eAAe,CACb/B,eAAeC,eAAeC,IAC9BF,eAAeQ,eAAeN,IAC9BF,eAAeS,eAAeP,EAAE;AAAA,IAElC8B,eAAe;AAAA,IACfC,mBAAmB;AAAA;AAAA,IACnBC,oBAAoB;AAAA,IACpBC,qBAAqB,CAAE;AAAA;AAAA,IAGvBC,cAAc;AAAA,MACZC,cAAc;AAAA,QACZC,cAAc,CAAE;AAAA,QAChBC,cAAc,CAAE;AAAA,QAChBC,kBAAkB;AAAA,MACpB;AAAA,MACAC,gBAAgB;AAAA,QACdC,aAAa;AAAA,QACbC,WAAW;AAAA,QACXC,eAAe;AAAA,MACjB;AAAA,MACAC,iBAAiB;AAAA,QACfC,UAAU,CAAE;AAAA,QACZC,iBAAiB,CAAE;AAAA,QACnBC,gBAAgB;AAAA,MAClB;AAAA,MACAC,cAAc;AAAA,QACZC,QAAQ,CAAE;AAAA,QACVC,cAAc,CAAC;AAAA,QACfC,YAAY,CAAA;AAAA,MACd;AAAA,MACAC,gBAAgB;AAAA,QACdC,WAAW;AAAA,QACXC,eAAe;AAAA,QACfC,eAAe;AAAA,MACjB;AAAA,MACAC,kBAAkB;AAAA,QAChBC,iBAAiB,CAAE;AAAA,QACnBC,cAAc,CAAE;AAAA,QAChBC,gBAAgB,CAAA;AAAA,MAAA;AAAA,IAEpB;AAAA;AAAA,IAGAC,cAAc;AAAA,IACdC,cAAc;AAAA,IACdC,iBAAiB;AAAA,IACjBC,iBAAiB;AAAA;AAAA,IAGjBC,cAAc;AAAA,IACdC,iBAAiB;AAAA,IACjBC,UAAU;AAAA,IACVC,oBAAoB;AAAA,IACpBC,eAAe;AAAA,IACfC,iBAAiB;AAAA;AAAA,IAGjBZ,iBAAiB,CAAE;AAAA,IACnBa,cAAc;AAAA,IACdC,YAAY;AAAA;AAAA,IAGZC,UAAU;AAAA;AAAA,IAGVC,WAAW;AAAA,MACTnD,OAAO;AAAA,MACPoD,WAAW;AAAA,MACXN,eAAe;AAAA,MACfzC,UAAU;AAAA,MACVgD,kBAAkB;AAAA,MAClB/C,gBAAgB;AAAA,IAClB;AAAA;AAAA,IAGAgD,eAAe;AAAA,MACbC,oBAAoB,CAAE;AAAA,MACtBC,eAAe,CAAE;AAAA,MACjBC,qBAAqB;AAAA,QACnBC,kBAAkB,CAAC;AAAA,QACnBC,iBAAiB;AAAA,QACjBC,oBAAoB,CAAA;AAAA,MACtB;AAAA,MACAC,uBAAuB;AAAA,QACrBC,yBAAyB,CAAE;AAAA,QAC3BC,iBAAiB,CAAE;AAAA,QACnBC,mBAAmB,CAAA;AAAA,MACrB;AAAA,MACAC,sBAAsB;AAAA,QACpBC,kBAAkB;AAAA,QAClBC,oBAAoB;AAAA,QACpBC,kBAAkB,CAAA;AAAA,MAAA;AAAA,IAEtB;AAAA;AAAA,IAGAC,mBAAmB;AAAA,MACjBC,cAAc,CAAE;AAAA,MAChBjE,UAAU,CAAE;AAAA,MACZkE,eAAe;AAAA,MACfC,mBAAmB,CAAE;AAAA,MACrBC,kBAAkB;AAAA,MAClBC,iBAAiB;AAAA,MACjBC,wBAAwB,CAAC;AAAA,MACzBC,kBAAkB,CAAA;AAAA,IAAC;AAAA,EACrB,CACD;AAGoBjF,eAAAA,OAAO,IAAI;AACNA,eAAAA,OAAO,IAAI;AAG/B,QAAA;AAAA,IACJkF;AAAAA,IAGAC;AAAAA;AAAAA,IACAC;AAAAA,EAEF,IAAIC,oBAAoB,cAAc;AAGtC,QAAM,CAACC,aAAa,IAAInF,sBAAS,MAAM,IAAIoF,2BAA2B;AAGtE,QAAM,CAACC,YAAWC,YAAY,IAAItF,sBAAS,MAAM;AACzCuF,UAAAA,QAAQC,aAAaC,QAAQ,wBAAwB;AAC3D,WAAOF,QAAQG,KAAKC,MAAMJ,KAAK,IAAI;AAAA,EAAA,CACpC;AAGK,QAAA;AAAA,IACJK,mBAAmBC;AAAAA,IACnBC,mBAAmBC;AAAAA,IAInBC,eAAeC;AAAAA,EAAAA,IACbC,2BAA2BjB,WAAW;AAAA,IAaxCkB,eAAe5G,MAAM6G,SAASD,iBAAiB;AAAA,EAAA,CAChD;AAG+BE,6BAA2B,CAM3D,CAAC;AAED,QAAM,CAACC,0BAA0BC,2BAA2B,IAAIvG,aAAAA,SAAS,KAAK;AAC9E,QAAM,CAACwG,aAAaC,cAAc,IAAIzG,aAAAA,SAAS,KAAK;AACpD,QAAM,CAAC0G,iBAAiBC,kBAAkB,IAAI3G,aAAAA,SAAS,IAAI;AAC3D,QAAM,CAACqD,YAAWuD,YAAY,IAAI5G,sBAAS;AAAA,IACzCI,OAAO;AAAA,IACPF,OAAO;AAAA,IACPoD,WAAW;AAAA,IACXN,eAAe;AAAA,IACfzC,UAAU;AAAA,EAAA,CACX;AACD,QAAM,CAACD,YAAYuG,aAAa,IAAI7G,aAAAA,SAAS,MAAM;AACnD,QAAM,CAACkD,cAAc4D,eAAe,IAAI9G,aAAAA,SAAS,IAAI;AACrD,QAAM,CAACkB,cAAc6F,eAAe,IAAI/G,aAAAA,SAAS,CAAA,CAAE;AACnD,QAAM,CAACiB,cAAc+F,eAAe,IAAIhH,aAAAA,SAAS,CAAA,CAAE;AACnD,QAAM,CAACmD,YAAY8D,aAAa,IAAIjH,aAAAA,SAAS,KAAK;AAClD,QAAM,CAACkH,gBAAgBC,iBAAiB,IAAInH,aAAAA,SAAS,IAAI;AACzD,QAAM,CAACoD,UAAUgE,WAAW,IAAIpH,aAAAA,SAAS,IAAI;AAG7C,QAAMqH,QAAQC,aAAY,YAAA,CAACC,MAAMC,UAAU,CAAA,MAAO;AAChD,QAAI,CAACnC,cAAa,EAAE,qBAAqBoC,SAAS;AAChD;AAAA,IAAA;AAGFA,WAAOC,gBAAgBC,OAAO;AAExBC,UAAAA,YAAY,IAAIC,yBAAyBN,IAAI;AACnDK,cAAUE,OAAO;AACPC,cAAAA,OAAOP,QAAQO,QAAQ;AACvBC,cAAAA,QAAQR,QAAQQ,SAAS;AACzBC,cAAAA,SAAST,QAAQS,UAAU;AAE9BP,WAAAA,gBAAgBL,MAAMO,SAAS;AAAA,EAAA,GACrC,CAACvC,UAAS,CAAC;AAKd,QAAM6C,0BAA0BZ,aAAAA,YAAY,CAACa,YAAY7H,gBAAe;AACtE,UAAM8H,SAASC,qBAAqBC,mBAAmBhI,YAAWiI,aAAa;AAC/E,UAAMC,iBAAiBH,qBAAqBI,gBAAgBN,WAAWI,aAAa;AAEpF,YAAQJ,YAAU;AAAA,MAChB,KAAK;AACIO,eAAAA,8BAA8BN,QAAQI,cAAc;AAAA,MAC7D,KAAK;AACIG,eAAAA,8BAA8BP,QAAQI,cAAc;AAAA,MAC7D,KAAK;AACII,eAAAA,8BAA8BR,QAAQI,cAAc;AAAA,MAC7D;AACSE,eAAAA,8BAA8BN,QAAQI,cAAc;AAAA,IAAA;AAAA,EAEjE,GAAG,EAAE;AAOL,QAAME,gCAAgCpB,aAAAA,YAAY,CAACuB,kBAAkBL,mBAAmB;AACtFM,YAAQC,IAAI,uCAAuC;AAEnD,UAAMC,iBAAiBX,qBAAqBY,iBAAiBJ,kBAAkBK,qBAAqB,OAAO;AACrGC,UAAAA,gBAAgBH,eAAeI,KAAKC,MAAMD,KAAKE,OAAO,IAAIN,eAAeO,MAAM,CAAC;AAEtF,UAAMC,cAAc;AAAA,MAClBC,MAAM;AAAA,MACNC,QAAQ;AAAA,MACRC,MAAM;AAAA,IACR;AAEMC,UAAAA,gBAAgB9J,UAAUQ,cAAc;AACxCuJ,UAAAA,aAAaL,YAAYI,aAAa,KAAK;AACjD,UAAME,gBAAgBX,cAAcY,OAAOC,MAAM,GAAGH,UAAU;AACxDI,UAAAA,mBAAmBC,yBAAyBJ,eAAe,CAAC;AAE3D,WAAA;AAAA,MACLK,SAAShB;AAAAA,MACTW;AAAAA,MACAzH,iBAAiB,CAAC,GAAGyH,eAAe,GAAGG,gBAAgB,EAAEG,KAAK,MAAMhB,KAAKE,OAAO,IAAI,GAAG;AAAA,MACvFe,aAAaR;AAAAA,MACbS,aAAa,oCAAoCnB,cAAcrK,IAAI;AAAA,MACnEsB,OAAON,UAAUQ;AAAAA,MACjBiK,cAAc;AAAA,MACdC,YAAYpB,KAAKqB,KAAKrB,KAAKsB,KAAKb,UAAU,CAAC;AAAA,IAC7C;AAAA,EAAA,GACC,CAAC/J,UAAUQ,UAAU,CAAC;AAGzB,QAAMqI,gCAAgCrB,aAAAA,YAAY,CAACuB,kBAAkBL,mBAAmB;AACtFM,YAAQC,IAAI,uCAAuC;AAEnD,UAAMC,iBAAiBX,qBAAqBY,iBAAiBJ,kBAAkBK,qBAAqB,OAAO;AACrGC,UAAAA,gBAAgBH,eAAeI,KAAKC,MAAMD,KAAKE,OAAO,IAAIN,eAAeO,MAAM,CAAC;AAEtF,UAAMoB,iBAAiB;AAAA,MACrBlB,MAAM,CAAC,IAAI,GAAG;AAAA,MACdC,QAAQ,CAAC,IAAI,KAAK,GAAG;AAAA,MACrBC,MAAM,CAAC,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,IACxC;AAEA,UAAMI,SAASZ,cAAcY,OAAOC,MAAM,GAAG,CAAC;AACxCJ,UAAAA,gBAAgB9J,UAAUQ,cAAc;AAC9C,UAAMsK,kBAAkBD,eAAef,aAAa,KAAKe,eAAelB;AAExE,UAAMoB,gBAAgBd,OAAOe,IAAI,CAACC,OAAOC,WAAW;AAAA,MAClDnM,IAAImM;AAAAA,MACJC,UAAUF;AAAAA,MACVG,OAAON,gBAAgBxB,KAAKC,MAAMD,KAAKE,OAAO,IAAIsB,gBAAgBrB,MAAM,CAAC;AAAA,MACzE4B,QAAQ;AAAA,MACRC,iBAAiBJ;AAAAA,IAAAA,EACjB;AAEK,WAAA;AAAA,MACLb,SAAShB;AAAAA,MACT0B;AAAAA,MACAP,aAAa,2CAA2CnB,cAAcrK,IAAI;AAAA,MAC1EsB,OAAON,UAAUQ;AAAAA,MACjBiK,cAAc;AAAA,MACdc,oBAAoB;AAAA,IACtB;AAAA,EAAA,GACC,CAACvL,UAAUQ,UAAU,CAAC;AAGzB,QAAMsI,gCAAgCtB,aAAAA,YAAY,CAACuB,kBAAkBL,mBAAmB;AACtFM,YAAQC,IAAI,uCAAuC;AAEnD,UAAMuC,eAAe;AAAA,MACnB7B,MAAM,CACJ;AAAA,QAAE8B,MAAM;AAAA,QAAeC,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,QAAGC,SAAS;AAAA,QAAGC,SAAS;AAAA,MAAA,GAChF;AAAA,QAAEH,MAAM;AAAA,QAAcC,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,QAAGC,SAAS;AAAA,QAAGC,SAAS;AAAA,MAAA,CAAM;AAAA,MAEvFhC,QAAQ,CACN;AAAA,QAAE6B,MAAM;AAAA,QAAeC,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,QAAGC,SAAS;AAAA,QAAGC,SAAS;AAAA,MAAA,GAChF;AAAA,QAAEH,MAAM;AAAA,QAAmBC,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,QAAGC,SAAS;AAAA,QAAGC,SAAS;AAAA,MAAA,CAAM;AAAA,MAE5F/B,MAAM,CACJ;AAAA,QAAE4B,MAAM;AAAA,QAAmBC,UAAU,CAAC,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,QAAGC,SAAS;AAAA,QAAGC,SAAS;AAAA,MAAA,GAC1F;AAAA,QAAEH,MAAM;AAAA,QAAmBC,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,QAAGC,SAAS;AAAA,QAAGC,SAAS;AAAA,MAAM,CAAA;AAAA,IAE9F;AAEM9B,UAAAA,gBAAgB9J,UAAUQ,cAAc;AAC9C,UAAMqL,gBAAgBL,aAAa1B,aAAa,KAAK0B,aAAa7B;AAC5DmC,UAAAA,kBAAkBD,cAAcvC,KAAKC,MAAMD,KAAKE,OAAO,IAAIqC,cAAcpC,MAAM,CAAC;AAGhFsC,UAAAA,eAAe,CAAC,MAAM,MAAM,MAAM,IAAI,EAAEC,OAAOC,CAAAA,QAAOA,QAAQH,gBAAgBF,OAAO;AAC3F,UAAMlE,UAAU,CAACoE,gBAAgBF,SAAS,GAAGG,aAAa7B,MAAM,GAAG,CAAC,CAAC,EAAEI,KAAK,MAAMhB,KAAKE,OAAAA,IAAW,GAAG;AAE9F,WAAA;AAAA,MACL0C,SAASJ;AAAAA,MACTpE;AAAAA,MACA8C,aAAa;AAAA,MACblK,OAAON,UAAUQ;AAAAA,MACjBiK,cAAc;AAAA,MACd0B,YAAY;AAAA,MACZC,QAAQ;AAAA,IACV;AAAA,EAAA,GACC,CAACpM,UAAUQ,UAAU,CAAC;AAKzB,QAAM4J,2BAA2B5C,aAAAA,YAAY,CAACwC,eAAeqC,UAAU;AAC/DC,UAAAA,YAAY,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,OAAO,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,IAAI;AAC9IC,UAAAA,cAAcD,UAAUN,OAAOf,CAAAA,UAAS,CAACjB,cAAcwC,SAASvB,KAAK,CAAC;AACrEsB,WAAAA,YAAYjC,KAAK,MAAMhB,KAAKE,WAAW,GAAG,EAAEU,MAAM,GAAGmC,KAAK;AAAA,EACnE,GAAG,EAAE;AAG6B7E,eAAa3H,YAAAA,CAAAA,cAAa;AAC1D,UAAM4M,iBAAiB;AAAA,MACrBC,UAAU;AAAA,QAAE1N,MAAM;AAAA,QAAqBC,MAAM;AAAA,QAAMgL,QAAQ,CAAA;AAAA,MAAG;AAAA,MAC9D0C,UAAU;AAAA,QAAE3N,MAAM;AAAA,QAAqBC,MAAM;AAAA,QAAMgL,QAAQ,CAAA;AAAA,MAAG;AAAA,MAC9D2C,SAAS;AAAA,QAAE5N,MAAM;AAAA,QAAmBC,MAAM;AAAA,QAAMgL,QAAQ,CAAA;AAAA,MAAG;AAAA,MAC3D4C,SAAS;AAAA,QAAE7N,MAAM;AAAA,QAAoBC,MAAM;AAAA,QAAMgL,QAAQ,CAAA;AAAA,MAAA;AAAA,IAC3D;AAEM6C,UAAAA,qBAAqBC,OAAOC,KAAKP,cAAc,EAAEvC,MAAM,GAAGrK,UAASoC,cAAc,CAAC;AACxF,UAAMgL,SAAS,CAAC;AAChBH,uBAAmBI,QAAQC,CAAO,QAAA;AACzBA,aAAAA,GAAG,IAAIV,eAAeU,GAAG;AAAA,IAAA,CACjC;AAEMF,WAAAA;AAAAA,EAAAA,GACN,CAAE,CAAA;AAEmCzF,2BAAY,CAACvF,YAAYpC,cAAa;AACtEuN,UAAAA,eAAeL,OAAOC,KAAK/K,UAAU;AAC3C,UAAMgI,SAAS,CAAE;AAEjB,UAAMoD,aAAa;AAAA,MACjBX,UAAU,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,MAC7CC,UAAU,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,MAC7CC,SAAS,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,MAC5CC,SAAS,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,IAC9C;AAEAO,iBAAaF,QAAQI,CAAe,gBAAA;AAClC,YAAMC,iBAAiBF,WAAWC,WAAW,KAAK,CAAE;AACpD,YAAME,iBAAiBD,eAAerD,MAAM,GAAGrK,UAAS4N,qBAAqB,CAAC;AAC9ExD,aAAOyD,KAAK,GAAGF,eAAexC,IAAIC,CAAU,WAAA;AAAA,QAAEA;AAAAA,QAAO0C,UAAUL;AAAAA,QAAaM,OAAO3C;AAAAA,QAAQ,CAAC;AAAA,IAAA,CAC7F;AAED,WAAOhB,OAAOK,KAAK,MAAMhB,KAAKE,OAAAA,IAAW,GAAG;AAAA,EAAA,GAC3C,CAAE,CAAA;AAEmChC,2BAAY,CAACvF,YAAYpC,cAAa;AACtEuN,UAAAA,eAAeL,OAAOC,KAAK/K,UAAU;AAC3C,UAAMgI,SAAS,CAAE;AAEjBmD,iBAAaF,QAAQI,CAAe,gBAAA;AAC5BC,YAAAA,iBAAiBM,kBAAkBP,WAAW;AACpD,YAAME,iBAAiBD,eAAerD,MAAM,GAAGrK,UAAS4N,iBAAiB;AACzExD,aAAOyD,KAAK,GAAGF,eAAexC,IAAIC,CAAU,WAAA;AAAA,QAAEA;AAAAA,QAAO0C,UAAUL;AAAAA,QAAc,CAAC;AAAA,IAAA,CAC/E;AAED,WAAOrD,OAAOK,KAAK,MAAMhB,KAAKE,OAAAA,IAAW,GAAG;AAAA,EAAA,GAC3C,CAAE,CAAA;AAECqE,QAAAA,oBAAoBrG,yBAAamG,CAAa,aAAA;AAClD,UAAMG,cAAc;AAAA,MAClBC,UAAU,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,MAC7CC,QAAQ,CAAC,MAAM,OAAO,MAAM,OAAO,MAAM,IAAI;AAAA,MAC7CC,SAAS,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,MAC5CC,SAAS,CAAC,KAAK,KAAK,MAAM,KAAK,MAAM,GAAG;AAAA,IAC1C;AACOJ,WAAAA,YAAYH,QAAQ,KAAK,CAAE;AAAA,EACpC,GAAG,EAAE;AAE0BnG,eAAa3H,YAAAA,CAAAA,cAAa;AACvD,UAAM2L,eAAe3L,UAAS2L;AACxB2C,UAAAA,eAAe3C,aAAalC,KAAKC,MAAMD,KAAKE,OAAO,IAAIgC,aAAa/B,MAAM,CAAC;AAEjF,YAAQ0E,cAAY;AAAA,MAClB,KAAK;AACI,eAAA;AAAA,UAAE1C,MAAM;AAAA,UAAeC,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,UAAG0C,MAAM;AAAA,QAAK;AAAA,MAC/E,KAAK;AACI,eAAA;AAAA,UAAE3C,MAAM;AAAA,UAAcC,UAAU,CAAC,MAAM,MAAM,MAAM,KAAK;AAAA,UAAG0C,MAAM;AAAA,QAAK;AAAA,MAC/E,KAAK;AACI,eAAA;AAAA,UAAE3C,MAAM;AAAA,UAAeC,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,UAAG0C,MAAM;AAAA,QAAK;AAAA,MAC/E;AACS,eAAA;AAAA,UAAE3C,MAAM;AAAA,UAAeC,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,UAAG0C,MAAM;AAAA,QAAK;AAAA,IAAA;AAAA,EACjF,GACC,CAAE,CAAA;AAE0B5G,2BAAY,CAAC0E,SAASrM,cAAa;AAChE,UAAMwO,gBAAgBnC,QAAQkC;AACxBrC,UAAAA,eAAe,CAAC,MAAM,MAAM,MAAM,IAAI,EAAEC,OAAOC,CAAOA,QAAAA,QAAQoC,aAAa;AACjF,UAAMC,gBAAgBvC,aAAa7B,MAAM,GAAGrK,UAAS0O,oBAAoB,CAAC;AAEnE,WAAA,CAACF,eAAe,GAAGC,aAAa,EAAEhE,KAAK,MAAMhB,KAAKE,OAAO,IAAI,GAAG;AAAA,EAAA,GACtE,CAAE,CAAA;AAKwBhC,eAAAA,YAAY,MAAM;AAC7CvH,iBAAauO,CAAQ,SAAA;AACnB,YAAMC,aAAaD,KAAK3N,gBAAgB,KAAK2N,KAAK5N,cAAc6I;AAC1DiF,YAAAA,eAAeF,KAAK5N,cAAc6N,SAAS;AAGjD,UAAIxI,+BAA+B;AACjCA,sCAA8B,qBAAqB;AAAA,UACjD0I,MAAMH,KAAK7N;AAAAA,UACXiO,IAAIF;AAAAA,UACJG,WAAW;AAAA,UACXxO,OAAOmO,KAAKnO;AAAAA,QAAAA,CACb;AAAA,MAAA;AAGH,YAAMyO,WAAW;AAAA,QACf,GAAGN;AAAAA,QACH7N,iBAAiB+N;AAAAA,QACjB7N,eAAe4N;AAAAA,QACf1N,oBAAoB;AAAA,QACpBV,OAAOmO,KAAKnO,QAAQ;AAAA,QACpBK,gBAAgBqO,KAAKC,IAAI;AAAA,MAC3B;AAGA,YAAMC,kBAAkB7G,wBAAwB0G,SAASnO,iBAAiBmO,SAAStO,UAAU;AAGpF4G,eAAAA,iBAAiB6H,gBAAgB5E,WAAW;AACrDyE,eAAS3N,eAAe8N,gBAAgB1M,mBAAmB0M,gBAAgBhF,UAAU,CAAE;AAC9E7I,eAAAA,eAAe,IAAI8N,MAAMD,gBAAgB1E,eAAe,CAAC,EAAE4E,KAAK,IAAI;AAG7E,YAAMzG,iBAAiBH,qBAAqBI,gBAAgB+F,aAAajG,aAAa;AAChF2G,YAAAA,eAAe1G,gBAAgB1J,QAAQ;AAE7CqQ,iBAAW,MAAM;AACf,YAAI9H,OAAO;AACTA,gBAAM,mBAAmB6H,YAAY,KAAKH,gBAAgBzE,WAAW,IAAI;AAAA,YAAEvC,MAAM;AAAA,UAAA,CAAK;AAAA,QAAA;AAAA,SAEvF,GAAG;AAEC6G,aAAAA;AAAAA,IAAAA,CACR;AAAA,EAAA,GACA,CAAC1G,yBAAyBnC,+BAA+BsB,KAAK,CAAC;AAG5D+H,QAAAA,iBAAiB9H,yBAAaa,CAAe,eAAA;AACjDpI,iBAAauO,CAAQ,SAAA;AACnB,YAAM3N,gBAAgB2N,KAAK5N,cAAc2O,QAAQlH,UAAU;AACvDxH,UAAAA,kBAAkB,GAAW2N,QAAAA;AAEjC,YAAM9F,iBAAiBH,qBAAqBI,gBAAgBN,WAAWI,aAAa;AAC9E2G,YAAAA,eAAe1G,gBAAgB1J,QAAQ;AAG7C,UAAIiH,+BAA+B;AACjCA,sCAA8B,mBAAmB;AAAA,UAC/C0I,MAAMH,KAAK7N;AAAAA,UACXiO,IAAIvG;AAAAA,UACJmH,QAAQ;AAAA,UACRnP,OAAOmO,KAAKnO;AAAAA,QAAAA,CACb;AAAA,MAAA;AAGH,YAAMyO,WAAW;AAAA,QACf,GAAGN;AAAAA,QACH7N,iBAAiB0H;AAAAA,QACjBxH;AAAAA,QACAE,oBAAoB;AAAA,QACpBV,OAAOmO,KAAKnO,QAAQ;AAAA,QACpBK,gBAAgBqO,KAAKC,IAAI;AAAA,MAC3B;AAGA,YAAMC,kBAAkB7G,wBAAwB0G,SAASnO,iBAAiBmO,SAAStO,UAAU;AAEpF4G,eAAAA,iBAAiB6H,gBAAgB5E,WAAW;AACrDyE,eAAS3N,eAAe8N,gBAAgB1M,mBAAmB0M,gBAAgBhF,UAAU,CAAE;AAC9E7I,eAAAA,eAAe,IAAI8N,MAAMD,gBAAgB1E,eAAe,CAAC,EAAE4E,KAAK,IAAI;AAG7EE,iBAAW,MAAM;AACf,YAAI9H,OAAO;AACTA,gBAAM,4BAA4B6H,YAAY,KAAKH,gBAAgBzE,WAAW,IAAI;AAAA,YAAEvC,MAAM;AAAA,UAAA,CAAK;AAAA,QAAA;AAAA,SAEhG,GAAG;AAEC6G,aAAAA;AAAAA,IAAAA,CACR;AAAA,EACA,GAAA,CAAC1G,yBAAyBnC,+BAA+BsB,KAAK,CAAC;AAGzCC,eAAAA,YAAY,MAAM;AACzCwB,YAAQC,IAAI,gCAAgC;AAAA,MAC1CtI,iBAAiBX,UAAUW;AAAAA,MAC3BI,oBAAoBf,UAAUe;AAAAA,MAC9BV,OAAOL,UAAUK;AAAAA,IAAAA,CAClB;AAEDJ,iBAAauO,CAAQ,SAAA;AAEbiB,YAAAA,uBAAuBjB,KAAKzN,sBAAsByN,KAAK1N;AAE7DkI,cAAQC,IAAI,wCAAwC;AAAA,QAClDwG;AAAAA,QACA1O,oBAAoByN,KAAKzN;AAAAA,QACzBD,mBAAmB0N,KAAK1N;AAAAA,MAAAA,CACzB;AAED,UAAIgO,WAAW;AAAA,QAAE,GAAGN;AAAAA,MAAK;AAEzB,UAAIiB,sBAAsB;AAExB,cAAMC,qBAAqBlB,KAAK3N,gBAAgB,KAAK2N,KAAK5N,cAAc6I;AAClEiF,cAAAA,eAAeF,KAAK5N,cAAc8O,iBAAiB;AAEzD1G,gBAAQC,IAAI,wCAAwC;AAAA,UAClD0F,MAAMH,KAAK7N;AAAAA,UACXiO,IAAIF;AAAAA,UACJgB;AAAAA,QAAAA,CACD;AAEU,mBAAA;AAAA,UACT,GAAGZ;AAAAA,UACHnO,iBAAiB+N;AAAAA,UACjB7N,eAAe6O;AAAAA,UACf3O,oBAAoB;AAAA,UACpBC,qBAAqBwN,KAAKxN,sBAAsB;AAAA,QAClD;AAGA,cAAM0H,iBAAiBH,qBAAqBI,gBAAgB+F,aAAajG,aAAa;AAChF2G,cAAAA,eAAe1G,gBAAgB1J,QAAQ;AAC7CqQ,mBAAW,MAAM;AACf,cAAI9H,OAAO;AACH,kBAAA,mBAAmB6H,YAAY,IAAI;AAAA,cAAElH,OAAO;AAAA,cAAKD,MAAM;AAAA,YAAA,CAAK;AAAA,UAAA;AAAA,WAEnE,GAAG;AAAA,MAAA;AAIR,YAAMgH,kBAAkB7G,wBAAwB0G,SAASnO,iBAAiBmO,SAAStO,UAAU;AAE7FwI,cAAQC,IAAI,sCAAsC;AAAA,QAChD0G,UAAUb,SAASnO;AAAAA,QACnBiP,SAASX;AAAAA,MAAAA,CACV;AAGQ7H,eAAAA,iBAAiB6H,gBAAgB5E,WAAWyE,SAAS1H;AAC9D0H,eAAS3N,eAAe8N,gBAAgB1M,mBAAmB0M,gBAAgBhF,UAAU,CAAE;AAC9E7I,eAAAA,eAAe,IAAI8N,MAAMD,gBAAgB1E,eAAe,CAAC,EAAE4E,KAAK,IAAI;AAC7EL,eAASzL,aAAa;AACtByL,eAAS1L,eAAe;AACxB0L,eAASxL,WAAW;AACpBwL,eAASpM,eAAe;AAGf3B,eAAAA,qBAAqB+N,SAAS/N,qBAAqB;AACnDV,eAAAA,QAAQyO,SAASzO,QAAQ;AACzBK,eAAAA,iBAAiBqO,KAAKC,IAAI;AAE5BF,aAAAA;AAAAA,IAAAA,CACR;AAAA,EAAA,GACA,CAAC9O,UAAUW,iBAAiBX,UAAUe,oBAAoBf,UAAUK,OAAO+H,yBAAyBb,KAAK,CAAC;AA6W7G,QAAMsI,sBAAsBA,MAAM;AAE5B,QAAA,CAACzI,kBAAkBV,aAAa;AAChB,wBAAA;AAClB,iDACG,OAAI,EAAA,WAAWoJ,OAAOC,cAAa,QAAA,MAAA,UAAA;AAAA,QAAAC,UAAAC;AAAAA,QAAAC,YAAA;AAAA,QAAAC,cAAA;AAAA,MAAA,EAAA,uCACjC,OAAI,EAAA,WAAWL,OAAOM,gBAAe,QAAA,MAAA,UAAA;AAAA,QAAAJ,UAAAC;AAAAA,QAAAC,YAAA;AAAA,QAAAC,cAAA;AAAA,MAAA,EAAA,uCACnC,MAAG,EAAA,WAAWL,OAAOO,eAAc,QAAA,MAAA,UAAA;AAAA,QAAAL,UAAAC;AAAAA,QAAAC,YAAA;AAAA,QAAAC,cAAA;AAAA,MAAA,EAAA,GAAC,4BAA0B,CACjE,CACF;AAAA,IAAA;AAIJ,+CACG,OAAI,EAAA,WAAWL,OAAOC,cAAa,QAAA,MAAA,UAAA;AAAA,MAAAC,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCACjC,OAAI,EAAA,WAAWL,OAAOM,gBAAe,QAAA,MAAA,UAAA;AAAA,MAAAJ,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCACnC,MAAG,EAAA,WAAWL,OAAOO,eAAc,QAAA,MAAA,UAAA;AAAA,MAAAL,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,KAAC,uBAAoB/I,gBAAgBpI,IAAK,CAChF,GAECoI,kBAGG,sBAAA,cAAA,MAAA,UAAA,MAAA,sBAAA,cAAC,SAAI,WAAW0I,OAAOQ,gBAAe,QAAA,MAAA,UAAA;AAAA,MAAAN,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GACnC,sBAAA,cAAA,OAAA,EAAI,OAAO;AAAA,MACVI,WAAW;AAAA,MACXC,SAAS;AAAA,MACTC,QAAQ;AAAA,MACRC,cAAc;AAAA,MACdC,iBAAiB;AAAA,MACjBC,UAAU;AAAA,MACVC,WAAW;AAAA,MACXC,cAAc;AAAA,IAAA,GACd,QAAA,MAAA,UAAA;AAAA,MAAAd,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GACC,sBAAA,cAAA,OAAA,EAAI,OAAO;AAAA,MAAEY,UAAU;AAAA,MAAQD,cAAc;AAAA,IAAA,GAAS,QAAA,MAAA,UAAA;AAAA,MAAAd,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,KACpD/I,eAAewG,KAClB,GACA,sBAAA,cAAC,SAAI,OAAO;AAAA,MAAEmD,UAAU;AAAA,MAAUC,YAAY;AAAA,MAAQC,OAAO;AAAA,IAAA,GAAU,QAAA,MAAA,UAAA;AAAA,MAAAjB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IACpE/I,EAAAA,GAAAA,eAAepI,IAClB,CACF,GAGA,sBAAA,cAAC,OAAI,EAAA,WAAW8Q,OAAOoB,aAAY,QAAA,MAAA,UAAA;AAAA,MAAAlB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCAChC,OAAI,EAAA,WAAWL,OAAOqB,YAAW,QAAA,MAAA,UAAA;AAAA,MAAAnB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GAAC,6CAEnC,GACC,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOsB,YAAW,QAAA,MAAA,UAAA;AAAA,MAAApB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAC/B/O,GAAAA,aAAa4J,IAAI,CAACC,OAAOC,UACvB,sBAAA,cAAA,OAAA,EACC,KAAKA,OACL,WAAW,GAAG4E,OAAOuB,UAAU,IAAIpG,QAAQ6E,OAAOwB,SAASxB,OAAOyB,KAAK,IACvE,YAAaC,CAAMA,MAAAA,EAAEC,eAAe,GACpC,QAAQ,MAAMC,WAAWxG,KAAK,GAC9B,OAAO;AAAA,MACLyF,iBAAiBvJ,eAAe6J,QAAQ;AAAA,MACxCR,QAAQxF,QAAQ,sBAAsB;AAAA,IAAA,GACtC,QAAA,MAAA,UAAA;AAAA,MAAA+E,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GAEDlF,SACE,sBAAA,cAAA,OAAA,EAAI,WAAW6E,OAAO6B,aAAY,QAAA,MAAA,UAAA;AAAA,MAAA3B,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GAChC,sBAAA,cAAA,OAAA,EAAI,OAAO;AAAA,MAAEY,UAAU;AAAA,MAAUD,cAAc;AAAA,IAAA,GAAW,QAAA,MAAA,UAAA;AAAA,MAAAd,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,KACxDlF,MAAM2E,OACT,GACC,sBAAA,cAAA,UAAA,EACC,WAAWE,OAAO8B,mBAClB,SAAS,MAAMC,kBAAkB3G,KAAK,GACtC,OAAM,gBAAc,QAAA,MAAA,UAAA;AAAA,MAAA8E,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IACrB,EAAA,GAAA,GAED,CACF,GAED,CAAClF,SACC,sBAAA,cAAA,OAAA,EAAI,WAAW6E,OAAOgC,eAAc,QAAA,MAAA,UAAA;AAAA,MAAA9B,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAC,GAAA,uBAEtC,CAEJ,CACD,CACH,CACF,CACF,GAGA,sBAAA,cAAC,SAAI,WAAWL,OAAOiC,eAAc,QAAA,MAAA,UAAA;AAAA,MAAA/B,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,KAClChP,aAAasI,SAAS,IAAItI,aAAa6J,IAAKC,CAAU,UAAA;AACrD,YAAM+G,SAAS/G,MAAMgH;AAGnB,aAAA,sBAAA,cAAC,UACC,EAAA,KAAKhH,MAAMlM,IACX,WAAW,GAAG+Q,OAAOoC,YAAY,IAAIF,SAASlC,OAAOmC,OAAO,EAAE,IAC9D,WAAW,CAACD,QACZ,aAAa,MAAM,CAACA,UAAUG,gBAAgBlH,KAAK,GACnD,OAAO;AAAA,QACLmH,SAASJ,SAAS,MAAM;AAAA,QACxBK,QAAQL,SAAS,gBAAgB;AAAA,QACjCM,eAAeN,SAAS,SAAS;AAAA,MAAA,GACjC,QAAA,MAAA,UAAA;AAAA,QAAAhC,UAAAC;AAAAA,QAAAC,YAAA;AAAA,QAAAC,cAAA;AAAA,MAAA,EAAA,GAED,sBAAA,cAAA,OAAA,EAAI,OAAO;AAAA,QACVY,UAAU;AAAA,QACVD,cAAc;AAAA,MAAA,GACd,QAAA,MAAA,UAAA;AAAA,QAAAd,UAAAC;AAAAA,QAAAC,YAAA;AAAA,QAAAC,cAAA;AAAA,MAAA,KACClF,MAAM2E,OACT,GACA,sBAAA,cAAC,SAAI,OAAO;AAAA,QACVmB,UAAU;AAAA,QACVC,YAAY;AAAA,QACZC,OAAOe,SAAS,0BAA0B;AAAA,MAAA,GAC1C,QAAA,MAAA,UAAA;AAAA,QAAAhC,UAAAC;AAAAA,QAAAC,YAAA;AAAA,QAAAC,cAAA;AAAA,MACC6B,EAAAA,GAAAA,SAAS,UAAU,SACtB,CACF;AAAA,IAEH,CAAA,IACE,sBAAA,cAAA,OAAA,EAAI,WAAWlC,OAAOyC,UAAS,QAAA,MAAA,UAAA;AAAA,MAAAvC,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,KAAC,qBAEjC,CAEJ,CACF,CAEJ;AAAA,EAEJ;AAGA,QAAMqC,sBAAsBA,MAAM;AAChC,+CACG,OAAI,EAAA,WAAW1C,OAAOC,cAAa,QAAA,MAAA,UAAA;AAAA,MAAAC,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCACjC,OAAI,EAAA,WAAWL,OAAOM,gBAAe,QAAA,MAAA,UAAA;AAAA,MAAAJ,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCACnC,MAAG,EAAA,WAAWL,OAAOO,eAAc,QAAA,MAAA,UAAA;AAAA,MAAAL,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GAAC,qBAAmB,CAC1D,GAGA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAOQ,gBAAe,QAAA,MAAA,UAAA;AAAA,MAAAN,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GACnC,sBAAA,cAAA,OAAA,EAAI,OAAO;AAAA,MACVI,WAAW;AAAA,MACXC,SAAS;AAAA,MACTC,QAAQ;AAAA,MACRC,cAAc;AAAA,MACdC,iBAAiB;AAAA,MACjBC,UAAU;AAAA,MACVC,WAAW;AAAA,IAAA,GACX,QAAA,MAAA,UAAA;AAAA,MAAAb,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GACC,sBAAA,cAAA,OAAA,EAAI,OAAO;AAAA,MAAEY,UAAU;AAAA,MAAQD,cAAc;AAAA,IAAA,GAAS,QAAA,MAAA,UAAA;AAAA,MAAAd,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAC,GAAA,IAExD,GACA,sBAAA,cAAC,SAAI,OAAO;AAAA,MAAEY,UAAU;AAAA,MAAUC,YAAY;AAAA,MAAQC,OAAO;AAAA,IAAA,GAAU,QAAA,MAAA,UAAA;AAAA,MAAAjB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,KAAC,eAExE,CACF,GAGA,sBAAA,cAAC,SAAI,OAAO;AAAA,MACVsC,SAAS;AAAA,MACTC,eAAe;AAAA,MACfC,YAAY;AAAA,MACZC,KAAK;AAAA,MACLC,QAAQ;AAAA,IAAA,GACR,QAAA,MAAA,UAAA;AAAA,MAAA7C,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GACC,sBAAA,cAAA,OAAA,EAAI,OAAO;AAAA,MACVY,UAAU;AAAA,MACV+B,WAAW;AAAA,MACX7B,OAAO;AAAA,IAAA,GACP,QAAA,MAAA,UAAA;AAAA,MAAAjB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAC,GAAA,IAEH,GACA,sBAAA,cAAC,SAAI,OAAO;AAAA,MACVY,UAAU;AAAA,MACVE,OAAO;AAAA,MACPD,YAAY;AAAA,IAAA,GACZ,QAAA,MAAA,UAAA;AAAA,MAAAhB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAC,EAAA,GAAA,kBAEH,CACF,CACF,GAGA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAOiC,eAAc,QAAA,MAAA,UAAA;AAAA,MAAA/B,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAClC,EAAA,GAAA,CAAC,GAAG,IAAI,KAAK,GAAG,EAAEnF,IAAI,CAAC+H,UAAU7H,UAChC,sBAAA,cAAC,UACC,EAAA,KAAKA,OACL,WAAW4E,OAAOoC,cAClB,SAAS,MAAMc,oBAAoB9H,KAAK,GACxC,cAAY,SAAS6H,QAAQ,UAAS,QAAA,MAAA,UAAA;AAAA,MAAA/C,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GAErC,sBAAA,cAAA,OAAA,EAAI,OAAO;AAAA,MACVY,UAAU;AAAA,MACVD,cAAc;AAAA,MACdmC,WAAW,UAAUF,QAAQ;AAAA,IAAA,GAC7B,QAAA,MAAA,UAAA;AAAA,MAAA/C,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAC,GAAA,IAEH,GACA,sBAAA,cAAC,SAAI,OAAO;AAAA,MAAEY,UAAU;AAAA,MAAQC,YAAY;AAAA,IAAA,GAAS,QAAA,MAAA,UAAA;AAAA,MAAAhB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,SAClD4C,UAAS,GACZ,CACF,CACD,CACH,CACF;AAAA,EAEJ;AAGA,QAAMG,sBAAsBA,MAAM;AAChC,+CACG,OAAI,EAAA,WAAWpD,OAAOC,cAAa,QAAA,MAAA,UAAA;AAAA,MAAAC,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCACjC,OAAI,EAAA,WAAWL,OAAOM,gBAAe,QAAA,MAAA,UAAA;AAAA,MAAAJ,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCACnC,MAAG,EAAA,WAAWL,OAAOO,eAAc,QAAA,MAAA,UAAA;AAAA,MAAAL,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GAAC,6BAA2B,CAClE,GAGA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAOQ,gBAAe,QAAA,MAAA,UAAA;AAAA,MAAAN,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GACnC,sBAAA,cAAA,OAAA,EAAI,OAAO;AAAA,MACVI,WAAW;AAAA,MACXC,SAAS;AAAA,MACTC,QAAQ;AAAA,MACRC,cAAc;AAAA,MACdC,iBAAiB;AAAA,MACjBC,UAAU;AAAA,MACVC,WAAW;AAAA,IAAA,GACX,QAAA,MAAA,UAAA;AAAA,MAAAb,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GACC,sBAAA,cAAA,OAAA,EAAI,OAAO;AAAA,MACVsC,SAAS;AAAA,MACTG,KAAK;AAAA,MACLO,gBAAgB;AAAA,MAChBR,YAAY;AAAA,MACZ7B,cAAc;AAAA,IAAA,GACd,QAAA,MAAA,UAAA;AAAA,MAAAd,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,KACC,CAAC,MAAM,MAAM,MAAM,MAAM,GAAG,EAAEnF,IAAI,CAACoI,MAAMlI,UACxC,sBAAA,cAAC,OACC,EAAA,KAAKA,OACL,OAAO;AAAA,MACL6F,UAAU;AAAA,MACVP,SAAS;AAAA,MACTC,QAAQvF,UAAU,IAAI,sCAAsC;AAAA,MAC5DwF,cAAc;AAAA,MACdC,iBAAiBzF,UAAU,IAAI,2BAA2B;AAAA,MAC1D0F,UAAU;AAAA,MACVyC,WAAW;AAAA,MACXZ,SAAS;AAAA,MACTE,YAAY;AAAA,MACZQ,gBAAgB;AAAA,IAAA,GAChB,QAAA,MAAA,UAAA;AAAA,MAAAnD,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,KAEDiD,IACH,CACD,CACH,GACA,sBAAA,cAAC,SAAI,OAAO;AAAA,MAAErC,UAAU;AAAA,MAAUC,YAAY;AAAA,MAAQC,OAAO;AAAA,IAAA,GAAU,QAAA,MAAA,UAAA;AAAA,MAAAjB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,KAAC,mBAExE,CACF,GAGA,sBAAA,cAAC,SAAI,OAAO;AAAA,MACVsC,SAAS;AAAA,MACTC,eAAe;AAAA,MACfC,YAAY;AAAA,MACZC,KAAK;AAAA,MACLC,QAAQ;AAAA,IAAA,GACR,QAAA,MAAA,UAAA;AAAA,MAAA7C,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GACC,sBAAA,cAAA,OAAA,EAAI,OAAO;AAAA,MACVY,UAAU;AAAA,MACV+B,WAAW;AAAA,MACX7B,OAAO;AAAA,IAAA,GACP,QAAA,MAAA,UAAA;AAAA,MAAAjB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAC,GAAA,IAEH,GACA,sBAAA,cAAC,SAAI,OAAO;AAAA,MACVY,UAAU;AAAA,MACVE,OAAO;AAAA,MACPD,YAAY;AAAA,IAAA,GACZ,QAAA,MAAA,UAAA;AAAA,MAAAhB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAC,EAAA,GAAA,kBAEH,CACF,CACF,GAGA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAOiC,eAAc,QAAA,MAAA,UAAA;AAAA,MAAA/B,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAClC,EAAA,GAAA,CAAC,MAAM,MAAM,MAAM,IAAI,EAAEnF,IAAI,CAACsI,QAAQpI,UACrC,sBAAA,cAAC,UACC,EAAA,KAAKA,OACL,WAAW4E,OAAOoC,cAClB,SAAS,MAAMqB,oBAAoBD,MAAM,GACzC,cAAY,SAASA,MAAM,IAAG,QAAA,MAAA,UAAA;AAAA,MAAAtD,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GAE7B,sBAAA,cAAA,OAAA,EAAI,OAAO;AAAA,MAAEY,UAAU;AAAA,MAAUD,cAAc;AAAA,IAAA,GAAW,QAAA,MAAA,UAAA;AAAA,MAAAd,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EACxDmD,GAAAA,MACH,GACA,sBAAA,cAAC,SAAI,OAAO;AAAA,MAAEvC,UAAU;AAAA,MAAQC,YAAY;AAAA,IAAA,GAAS,QAAA,MAAA,UAAA;AAAA,MAAAhB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAClDmD,EAAAA,GAAAA,WAAW,OAAO,UAAUA,WAAW,OAAO,WAAWA,WAAW,OAAO,UAAU,MACxF,CACF,CACD,CACH,CACF;AAAA,EAEJ;AASAE,eAAAA,UAAU,MAAM;AACd,WAAO,MAAM;AAEX,UAAI,qBAAqB7L,QAAQ;AAC/BA,eAAOC,gBAAgBC,OAAO;AAAA,MAAA;AAAA,IAElC;AAAA,EACF,GAAG,EAAE;AAEL,QAAM,CAAC4L,iBAAiBC,kBAAkB,IAAIxT,aAAAA,SAAS,IAAI;AAC3D,QAAM,CAACyT,cAAcC,eAAe,IAAI1T,aAAAA,SAAS,CAAC;AAGlD,QAAM,CAACuD,kBAAkBoQ,mBAAmB,IAAI3T,aAAAA,SAAS,IAAI;AAC7D,QAAM,CAAC4T,mBAAmBC,oBAAoB,IAAI7T,aAAAA,SAAS,CAAA,CAAE;AAC7D,QAAM,CAAC8T,mBAAmBC,oBAAoB,IAAI/T,aAAAA,SAAS,CAAA,CAAE;AAC7D,QAAM,CAACgU,wBAAwBC,yBAAyB,IAAIjU,aAAAA,SAAS,YAAY;AAGjFsT,eAAAA,UAAU,MAAM;AACVrO,QAAAA,aAAa,OAAOA,cAAc,YAAYA,UAAUsE,SAAS,KAAK/C,eAAe,CAACP,yBAAyB;AACjH,YAAMiO,2BAA2B,YAAY;AACvC,YAAA;AACF,gBAAMrO,iBAAiB;AACfkD,kBAAAA,IAAI,yDAAyD9D,SAAS;AAAA,iBACvEkP,OAAO;AACNA,kBAAAA,MAAM,kDAAkDA,KAAK;AAAA,QAAA;AAAA,MAEzE;AACyB,+BAAA;AAAA,IAAA;AAAA,KAE1B,CAAClP,WAAWuB,aAAaP,yBAAyBJ,gBAAgB,CAAC;AAGhEuO,QAAAA,cAAc9M,aAAAA,YAAY,MAAM;AAChCjE,QAAAA,WAAUL,kBAAkB,EAAU,QAAA;AAC1C,WAAOoG,KAAKjJ,MAAOkD,WAAUC,YAAYD,WAAUL,gBAAiB,GAAG;AAAA,EAAA,GACtE,CAACK,UAAS,CAAC;AAGd,QAAMgR,uBAAuB,YAAY;AACjCC,UAAAA,cAAczF,KAAKC,IAAI;AACvByF,UAAAA,kBAAkBhR,mBAAmB+Q,cAAc/Q,mBAAmB;AAG5E,UAAMiR,eAAe;AAAA,MACnBC,WAAWF;AAAAA,MACXG,gBAAgBrR,WAAUC;AAAAA,MAC1BqR,kBAAkBtR,WAAUL,gBAAgBK,WAAUC;AAAAA,MACtD/C,UAAU6T,YAAY;AAAA,MACtBQ,iBAAiBtU;AAAAA,MACjBuU,iBAAiB1R,aAAa,MAAOjC,aAAaqI,SAAStI,aAAasI,SAAU;AAAA,IACpF;AAGA,UAAMuL,wBAAwB;AAAA,MAC5BC,kBAAkBC,0BAA0B;AAAA,MAC5CC,wBAAwBC,2BAA2B;AAAA,MACnDC,oBAAoBC,2BAA2B;AAAA,MAC/CC,qBAAqBC,2BAA2B;AAAA,MAChDtB;AAAAA,MACAuB,qBAAqBC,6BAA6B;AAAA,MAClD7Q,kBAAkB8Q,0BAA0B;AAAA,MAC5CC,kBAAkBC,sBAAsB;AAAA,MACxCC,qBAAqBC,2BAA2B;AAAA,IAClD;AAGA,UAAM9Q,eAAe;AAAA,MACnB,GAAGyP;AAAAA,MACH,GAAGM;AAAAA,IAAAA,CACJ;AAGD,UAAM9P,uBAAuB;AAAA,MAC3B8Q,UAAU;AAAA,MACVC,aAAa;AAAA,QACX,GAAGvB;AAAAA,QACH,GAAGM;AAAAA,QACHkB,cAAcpC;AAAAA,QACdE;AAAAA,QACAmC,UAAU1B;AAAAA,MACZ;AAAA,MACA2B,aAAa;AAAA,QACXC,qBAAqB7V;AAAAA,QACrB8V,oBAAoBC,2BAA2B;AAAA,MAAA;AAAA,IACjD,CACD;AAAA,EACH;AAGA,QAAMrB,4BAA4BA,MAAM;AACtC,UAAMsB,oBAAoB1C,kBAAkB9H,OAAOyK,CAAKA,MAAAA,EAAE7K,OAAO,EAAEnC;AACnE,UAAMiN,oBAAoB5C,kBAAkBrK;AAC5C,WAAOiN,oBAAoB,IAAKF,oBAAoBE,oBAAqB,MAAM;AAAA,EACjF;AAEA,QAAMtB,6BAA6BA,MAAM;AACjCuB,UAAAA,kBAAkB7C,kBAAkB9H,OAAOyK,CAAAA,MAAKA,EAAEG,kBAAkB,KAAKH,EAAE7K,OAAO,EAAEnC;AAC1F,UAAMoN,cAAc1V,aAAasI;AACjC,WAAOoN,cAAc,IAAKF,kBAAkBE,cAAe,MAAM;AAAA,EACnE;AAEA,QAAMvB,6BAA6BA,MAAM;AACnCtB,QAAAA,kBAAkBvK,WAAW,EAAU,QAAA;AAE3C,UAAMqN,aAAa9C,kBAAkB+C,OAAO,CAAC1K,OAAO2K,aAAa;AAC/D3K,YAAM2K,QAAQ,KAAK3K,MAAM2K,QAAQ,KAAK,KAAK;AACpC3K,aAAAA;AAAAA,IACT,GAAG,EAAE;AAEL,WAAOU,OAAOC,KAAK8J,UAAU,EAAEC,OAAO,CAACE,GAAGC,MAAMJ,WAAWG,CAAC,IAAIH,WAAWI,CAAC,IAAID,IAAIC,CAAC;AAAA,EACvF;AAEA,QAAM1B,6BAA6BA,MAAM;AACvC,UAAM2B,cAAcrD,kBAAkBiD,OAAO,CAAC3W,OAAOgX,aAAalM,UAAU;AAC1E,UAAIA,QAAQ,GAAG;AACPmM,cAAAA,kBAAkBvD,kBAAkB5I,QAAQ,CAAC;AACnD,YAAIkM,YAAYE,YAAYD,gBAAgBC,WACxCF,YAAYG,SAASC,MAAMH,gBAAgBE,SAASC,KACpDJ,YAAYG,SAASE,MAAMJ,gBAAgBE,SAASE,GAAG;AAChD,mBAAA;AAAA,QAAA;AAAA,MACX;AAEKrX,aAAAA;AAAAA,OACN,CAAC;AAEGkJ,WAAAA,KAAKoO,IAAIP,aAAa,GAAG;AAAA,EAClC;AAEA,QAAMzB,+BAA+BA,MAAM;AACzC,UAAMiC,oBAAoB7D,kBAAkB9H,OAAOyK,OAAK,CAACA,EAAE7K,OAAO,EAAEnC;AACpE,UAAMvG,gBAAgB4Q,kBAAkBrK;AAEpCvG,QAAAA,kBAAkB,EAAU,QAAA;AAGhC,WAAOoG,KAAKsO,IAAI,GAAG,MAAQD,oBAAoBzU,gBAAiB,GAAI;AAAA,EACtE;AAEA,QAAMyS,4BAA4BA,MAAM;AACtC,UAAMkC,sBAAsBvO,KAAKsO,IAAI,GAAGzW,aAAa6J,IAAIC,CAAS,UAAA;AAChE,aAAO6I,kBAAkB9H,OAAOyK,CAAAA,MAAKA,EAAEa,YAAYrM,MAAMlM,EAAE,EAAE0K;AAAAA,IAC9D,CAAA,GAAG,CAAC;AAGL,WAAOH,KAAKoO,IAAIG,sBAAsB,IAAI,GAAG;AAAA,EAC/C;AAEA,QAAMhC,wBAAwBA,MAAM;AAClC,WAAO/B,kBAAkB9H,OAAOyK,CAAAA,MAAKA,EAAEqB,WAAW,QAAQ,EAAErO;AAAAA,EAC9D;AAEA,QAAMsM,6BAA6BA,MAAM;AACnCjC,QAAAA,kBAAkBrK,SAAS,EAAU,QAAA;AAEzC,QAAIsO,kBAAkB;AACtB,aAAStB,IAAI,GAAGA,IAAI3C,kBAAkBrK,QAAQgN,KAAK;AAC3CuB,YAAAA,OAAOlE,kBAAkB2C,CAAC;AAC1BjI,YAAAA,OAAOsF,kBAAkB2C,IAAI,CAAC;AAEpC,UAAIuB,KAAKV,YAAY9I,KAAK8I,UAAU,GAAG;AACrCS;AAAAA,MAAAA;AAAAA,IACF;AAGIE,UAAAA,kBAAkBF,mBAAmBjE,kBAAkBrK,SAAS;AACtE,WAAOwO,kBAAkB,MAAM,eAAeA,kBAAkB,MAAM,UAAU;AAAA,EAClF;AAEA,QAAM1B,6BAA6BA,MAAM;AAChC,WAAA;AAAA,MACL2B,wBAAwB;AAAA;AAAA,MACxBC,mBAAmBtC,sBAAsB,IAAIvM,KAAKsO,IAAI9D,kBAAkBrK,QAAQ,CAAC;AAAA,MACjF2O,oBAAoBlE,2BAA2B;AAAA,IACjD;AAAA,EACF;AAGA,QAAMmE,yBAAyB,OAAOf,SAASC,UAAUO,QAAQlM,YAAY;AAC3E,UAAM0M,kBAAkB;AAAA,MACtBhB;AAAAA,MACAC;AAAAA,MACAO;AAAAA,MACAlM;AAAAA,MACA2M,WAAWxJ,KAAKC,IAAI;AAAA,MACpB4H,eAAejD,eAAe;AAAA,MAC9BmB,iBAAiBtU;AAAAA,MACjBgY,aAAazJ,KAAKC,IAAAA,KAASvL,oBAAoBsL,KAAKC,IAAI;AAAA,IAC1D;AAGA+E,yBAAqBvF,CAAQ,SAAA,CAAC,GAAGA,MAAM8J,eAAe,CAAC;AAGnD,QAAA;AACF,YAAMjT,cAAcoT,gBAAgB;AAAA,QAClC,GAAGH;AAAAA,QACHI,aAAa;AAAA,UACX7B,aAAa1V,aAAasI;AAAAA,UAC1BrI,cAAcA,aAAaqI;AAAAA,UAC3BkP,sBAAuBvX,aAAa4K,OAAO4M,CAAAA,MAAKA,MAAM,IAAI,EAAEnP,SAAStI,aAAasI,SAAU;AAAA,QAAA;AAAA,MAC9F,CACD;AAGD,YAAMxD,8BAA8B,oBAAoB;AAAA,QACtD4S,iBAAiB;AAAA,QACjBC,kBAAkBR;AAAAA,QAClBvT,wBAAwB;AAAA,UACtBgU,mBAAmB;AAAA,YAAE9D,kBAAkB;AAAA,YAAKM,qBAAqB;AAAA,YAAKyD,oBAAoB;AAAA,UAAI;AAAA,UAC9FC,qBAAqB;AAAA,YAAEC,gBAAgB;AAAA,YAAKC,iBAAiB;AAAA,YAAKC,cAAc;AAAA,UAAI;AAAA,UACpFC,sBAAsB;AAAA,YAAEC,kBAAkB;AAAA,YAAKC,qBAAqB;AAAA,YAAKC,aAAa;AAAA,UAAA;AAAA,QAAI;AAAA,MAC5F,CACD;AAGD,YAAMC,kBAAkB9F,eAAe;AACvCC,sBAAgB6F,eAAe;AAE3BA,UAAAA,kBAAkB,MAAM,GAAG;AACJ,iCAAA;AAAA,MAAA;AAAA,aAGpBpF,OAAO;AACNA,cAAAA,MAAM,uCAAuCA,KAAK;AAAA,IAAA;AAAA,EAE9D;AAGA,QAAMqF,2BAA2B,YAAY;AACvC,QAAA;AACFjT,kCAA4B,IAAI;AAE1BkT,YAAAA,eAAe,MAAMtU,cAAcuU,yBAAyB;AAAA,QAChEzU,WAAW1B,kBAAkBoW,SAAAA,KAAc;AAAA,QAC3C7Z,WAAW;AAAA,UACTQ;AAAAA,UACAsZ,cAAcvW,WAAUjD;AAAAA,UACxBF,OAAOmD,WAAUnD;AAAAA,UACjBK,UAAU6T,YAAY;AAAA,UACtBjR;AAAAA,QACF;AAAA,QACA6S,cAAcpC;AAAAA,QACdiG,aAAa;AAAA,UACXjD,YAAY9C;AAAAA,UACZgG,UAAU9F;AAAAA,QAAAA;AAAAA,MACZ,CACD;AAEDR,yBAAmBiG,YAAY;AAG/BtK,iBAAW,MAAM;AACf5I,oCAA4B,KAAK;AAAA,SAChC,GAAI;AAAA,aAEA4N,OAAO;AACNA,cAAAA,MAAM,8BAA8BA,KAAK;AACjD5N,kCAA4B,KAAK;AAAA,IAAA;AAAA,EAErC;AAGMwT,QAAAA,oBAAoBzS,aAAAA,YAAY,MAAM;AACpC6B,UAAAA,gBAAgB6Q,mBAAmBnM,SAASzE,KAAKC,MAAMD,KAAKE,WAAW0Q,mBAAmBnM,SAAStE,MAAM,CAAC;AAChH,UAAM0Q,iBAAiBD,mBAAmBE,aAAaC,KAAKC,CAAKA,MAAAA,EAAEvb,OAAOyB,UAAU;AAGpF,QAAI,CAAC2Z,gBAAgB;AACX9F,cAAAA,MAAM,kCAAkC7T,UAAU;AAC1D;AAAA,IAAA;AAGF6G,sBAAkBgC,aAAa;AAC/BlC,kBAAc,KAAK;AACnBG,gBAAY,IAAI;AAChBR,iBAAa0H,CAAS,UAAA;AAAA,MAAE,GAAGA;AAAAA,MAAMtL,eAAesL,KAAKtL,gBAAgB;AAAA,IAAA,EAAI;AAGnE8G,UAAAA,gBAAgBX,cAAcY,OAAOC,MAAM,GAAGiQ,eAAelQ,MAAM,EAAEe,IAAI,CAACC,OAAOC,WAAW;AAAA,MAChGnM,IAAI,WAAWmM,KAAK;AAAA,MACpB0E,SAAS3E;AAAAA,MACTgH,MAAM;AAAA,MACNsI,WAAW;AAAA,IAAA,EACX;AAGF,UAAMC,cAAcN,mBAAmBnM;AACvC,UAAM0M,gBAAgBD,YAAYxO,OAAOwF,OAAKA,EAAEzS,OAAOsK,cAActK,EAAE;AACvE,UAAMoL,mBAAmB,CAAE;AAGlBsM,aAAAA,IAAI,GAAGA,IAAInN,KAAKoO,IAAI,GAAG+C,cAAchR,MAAM,GAAGgN,KAAK;AACpDiE,YAAAA,qBAAqBD,cAAcnR,KAAKC,MAAMD,KAAKE,OAAO,IAAIiR,cAAchR,MAAM,CAAC;AACnFkR,YAAAA,cAAcD,mBAAmBzQ,OAAOX,KAAKC,MAAMD,KAAKE,WAAWkR,mBAAmBzQ,OAAOR,MAAM,CAAC;AAC1GU,uBAAiBuD,KAAK;AAAA,QACpB3O,IAAI,cAAc0X,CAAC;AAAA,QACnB7G,SAAS+K;AAAAA,QACT1I,MAAM;AAAA,QACNsI,WAAW;AAAA,MAAA,CACZ;AAAA,IAAA;AAIH,UAAMjO,YAAY,CAAC,GAAGtC,eAAe,GAAGG,gBAAgB;AACxDjD,oBAAgBoF,UAAUhC,KAAK,MAAMhB,KAAKE,OAAO,IAAI,GAAG,CAAC;AACzDvC,oBAAgBiI,MAAMiL,eAAelQ,MAAM,EAAEkF,KAAK,IAAI,CAAC;AAAA,EAAA,GACtD,CAAC3O,UAAU,CAAC;AAGToa,QAAAA,YAAYpT,yBAAY,OAAOqT,uBAAuB;AAE1D,QAAIA,oBAAoB;AACtB9T,oBAAc8T,kBAAkB;AAAA,IAAA;AAIlChU,uBAAmB,KAAK;AACxBF,mBAAe,IAAI;AACN,iBAAA;AAAA,MACXrG,OAAO;AAAA,MACPF,OAAO;AAAA,MACPoD,WAAW;AAAA,MACXN,eAAe;AAAA,IAAA,CAChB;AAGG,QAAA;AACF,YAAM6C,iBAAiB,WAAWgJ,KAAKC,IAAAA,CAAK,IAAI;AAAA,QAC9CxO;AAAAA,QACAsa,UAAU;AAAA,QACVC,QAAQtb,MAAMV,MAAM;AAAA,MAAA,CACrB;AAAA,aACMsV,OAAO;AACN2G,cAAAA,KAAK,kDAAkD3G,KAAK;AAAA,IAAA;AAGpD,sBAAA;AAAA,EACjB,GAAA,CAACtO,kBAAkBkU,mBAAmBxa,IAAI,CAAC;AAGxCwb,QAAAA,YAAYzT,aAAAA,YAAY,MAAM;AAClChC,iBAAagJ,CAAQ,SAAA;AACnB,YAAMM,WAAW,CAACN;AAClB9I,mBAAawV,QAAQ,0BAA0BtV,KAAKuV,UAAUrM,QAAQ,CAAC;AACnE,UAAA,CAACA,YAAY,qBAAqBnH,QAAQ;AAC5CA,eAAOC,gBAAgBC,OAAO;AAAA,MAAA;AAEzBiH,aAAAA;AAAAA,IAAAA,CACR;AAAA,EACH,GAAG,EAAE;AAGCqD,QAAAA,kBAAkB3K,yBAAayD,CAAU,UAAA;AAC7CjE,oBAAgBiE,KAAK;AAAA,EACvB,GAAG,EAAE;AAGCmQ,QAAAA,uBAAuB5T,aAAAA,YAAY,YAAY;AACnDL,kBAAc,IAAI;AAElB,UAAMkU,SAASnB,mBAAmBoB,aAAaC,mBAAmB/a,WAAWiI,YAAa,CAAA,KAAK;AAC/F3B,iBAAa0H,CAAS,UAAA;AAAA,MACpB,GAAGA;AAAAA,MACHhL,WAAWgL,KAAKhL,YAAY;AAAA,MAC5BpD,OAAOoO,KAAKpO,QAAQib;AAAAA,MACpB/a,OAAOkO,KAAKlO,QAAQ;AAAA,IAAA,EACpB;AAEIkb,UAAAA,UAAUtB,mBAAmBuB,oBAAoBnS,KAAKC,MAAMD,KAAKE,WAAW0Q,mBAAmBuB,oBAAoBhS,MAAM,CAAC;AAGhIlC,UAAM,GAAGiU,OAAO,gBAAgBH,MAAM,UAAU;AAG5C,QAAA;AACF,YAAM9G,qBAAqB;AAC3BvL,cAAQC,IAAI,qDAAqD;AAAA,aAC1DoL,OAAO;AACNA,cAAAA,MAAM,gDAAgDA,KAAK;AAAA,IAAA;AAAA,EAEpE,GAAA,CAAC7T,YAAY+G,OAAOgN,oBAAoB,CAAC;AAGtC7C,QAAAA,aAAalK,yBAAakU,CAAgB,gBAAA;AAC9C,QAAI,CAACtY,aAAc;AAGfhC,QAAAA,aAAasa,WAAW,MAAM,MAAM;AACtCnU,YAAM,4BAA4B;AAClCP,sBAAgB,IAAI;AACpB;AAAA,IAAA;AAGI2U,UAAAA,kBAAkB,CAAC,GAAGva,YAAY;AACxCua,oBAAgBD,WAAW,IAAItY;AAG/B,UAAMwY,iBAAiBxU,kBAAkBA,eAAe6C,OAAOuC,SAASpJ,aAAawM,OAAO;AAG5FyI,2BACEjV,aAAarE,IACb;AAAA,MAAEyY,GAAGkE,cAAc;AAAA,MAAGjE,GAAGnO,KAAKC,MAAMmS,cAAc,CAAC;AAAA,IAAA,GACnD,SACAE,cACF;AAEA3U,oBAAgB0U,eAAe;AAG/B,QAAIC,gBAAgB;AAClB1U,sBAAgBsH,UAAQA,KAAKxD,IAAI4N,OAC/BA,EAAE7Z,OAAOqE,aAAarE,KAAK;AAAA,QAAE,GAAG6Z;AAAAA,QAAG3G,MAAM;AAAA,MAAK,IAAI2G,CACpD,CAAC;AAEDrR,YAAM,6BAA6B;AACnCT,mBAAa0H,CAAS,UAAA;AAAA,QACpB,GAAGA;AAAAA,QACHpO,OAAOoO,KAAKpO,QAAQ;AAAA,QACpBoD,WAAWgL,KAAKhL,YAAY;AAAA,MAAA,EAC5B;AAGF,YAAMqY,sBAAsBF,gBAAgB3P,OAAOf,CAAAA,UACjDA,UAAU,QAAQ7D,eAAe6C,OAAOuC,SAASvB,MAAM2E,OAAO,CAChE,EAAEnG;AAEEoS,UAAAA,wBAAwBzU,eAAe6C,OAAOR,QAAQ;AAC7C,mBAAA,MAAM2R,qBAAqB,GAAG,GAAG;AAAA,MAAA;AAAA,IAC9C,OACK;AACL7T,YAAM,qCAAqC;AAE3C8H,iBAAW,MAAM;AACfpI,wBAAgBuH,CAAQ,SAAA;AAChBsN,gBAAAA,UAAU,CAAC,GAAGtN,IAAI;AACxBsN,kBAAQJ,WAAW,IAAI;AAChBI,iBAAAA;AAAAA,QAAAA,CACR;AAAA,SACA,IAAI;AAAA,IAAA;AAGT9U,oBAAgB,IAAI;AAAA,EAAA,GACnB,CAAC5D,cAAchC,cAAcgG,gBAAgBiR,wBAAwB9Q,OAAO6T,oBAAoB,CAAC;AAG9FvJ,QAAAA,oBAAoBrK,yBAAauU,CAAc,cAAA;AAC7CC,UAAAA,gBAAgB5a,aAAa2a,SAAS;AAC5C,QAAI,CAACC,cAAe;AAGdL,UAAAA,kBAAkB,CAAC,GAAGva,YAAY;AACxCua,oBAAgBI,SAAS,IAAI;AAC7B9U,oBAAgB0U,eAAe;AAG/BzU,oBAAgBsH,UAAQA,KAAKxD,IAAI4N,OAC/BA,EAAE7Z,OAAOid,cAAcjd,KAAK;AAAA,MAAE,GAAG6Z;AAAAA,MAAG3G,MAAM;AAAA,IAAM,IAAI2G,CACtD,CAAC;AAEDrR,UAAM,gBAAgB;AAAA,EAAA,GACrB,CAACnG,cAAcmG,KAAK,CAAC;AAkCxB,MAAIX,iBAAiB;AACnBoC,YAAQC,IAAI,0DAA0D;AACtE,WACG,sBAAA,cAAA,iBAAA,EACC,WAAU,2BACV,iBAAgB,oEAChB,UAAS,MACT,SAAS2R,WACT,QACA,cAAc,CACZ;AAAA,MAAE7b,IAAI;AAAA,MAAQC,MAAM;AAAA,MAASE,aAAa;AAAA,MAAwCD,MAAM;AAAA,IAAA,GACxF;AAAA,MAAEF,IAAI;AAAA,MAAUC,MAAM;AAAA,MAASE,aAAa;AAAA,MAAqCD,MAAM;AAAA,IAAA,GACvF;AAAA,MAAEF,IAAI;AAAA,MAAQC,MAAM;AAAA,MAAYE,aAAa;AAAA,MAAsCD,MAAM;AAAA,IAAA,CAAM,GAC/F,QAAA,MAAA,UAAA;AAAA,MAAA+Q,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,GACF;AAAA,EAAA;AAIJ,SAAA,sBAAA,cAAC,SACC,WAAW,GAAGL,OAAOmM,gBAAgB,IAAIpc,SAASqc,gBAAgB,mBAAmB,EAAE,IAAIrc,SAASsc,eAAe,kBAAkB,EAAE,IACvI,kBAAgBtc,SAASkR,UACzB,cAAYlR,SAASuc,aACrB,OAAO;AAAA,IACLrL,UAAUlR,SAASkR,aAAa,UAAU,aACjClR,SAASkR,aAAa,UAAU,YAAY;AAAA,EAAA,GACrD,QAAA,MAAA,UAAA;AAAA,IAAAf,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAGD3J,4BACE,sBAAA,cAAA,OAAA,EAAI,WAAU,gCAA8B,QAAA,MAAA,UAAA;AAAA,IAAAwJ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAC1C,sBAAA,cAAA,OAAA,EAAI,WAAU,oBAAkB,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAC9B,sBAAA,cAAA,OAAA,EAAI,WAAU,iBAAe,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,MAAI,GACnC,sBAAA,cAAC,SAAI,WAAU,iBAAe,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAC3B,sBAAA,cAAA,OAAA,EAAI,WAAU,kBAAgB,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,+BAA6B,GAC7D,sBAAA,cAAC,SAAI,WAAU,oBAAkB,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,6DAElC,CACF,CACF,CACF,GAIDsD,mBACC,sBAAA,cAAC,OAAI,EAAA,WAAU,4BAA0B,QAAA,MAAA,UAAA;AAAA,IAAAzD,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KACtC,sBAAA,cAAA,OAAA,EAAI,WAAU,mBAAiB,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAC7B,sBAAA,cAAA,QAAA,EAAK,WAAU,iBAAe,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,IAAE,GAClC,sBAAA,cAAC,UAAK,WAAU,kBAAgB,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,mBAAiB,CACpD,GACA,sBAAA,cAAC,SAAI,WAAU,oBAAkB,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAC9B,sBAAA,cAAA,OAAA,EAAI,WAAU,gBAAc,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAC1B,sBAAA,cAAA,QAAA,EAAK,WAAU,iBAAe,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,sBAAoB,GACpD,sBAAA,cAAC,UAAK,WAAU,iBAAe,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAEsD,EAAAA,GAAAA,gBAAgBwB,oBAAoB,YAAa,CACpF,GACC,sBAAA,cAAA,OAAA,EAAI,WAAU,gBAAc,QAAA,MAAA,UAAA;AAAA,IAAAjF,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAC1B,sBAAA,cAAA,QAAA,EAAK,WAAU,iBAAe,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,aAAW,GAC3C,sBAAA,cAAC,UAAK,WAAU,iBAAe,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAEsD,EAAAA,GAAAA,gBAAgBuD,YAAY9C,sBAAuB,CACtF,GACC,sBAAA,cAAA,OAAA,EAAI,WAAU,gBAAc,QAAA,MAAA,UAAA;AAAA,IAAAlE,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAC1B,sBAAA,cAAA,QAAA,EAAK,WAAU,iBAAe,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,mBAAiB,GACjD,sBAAA,cAAC,UAAK,WAAU,iBAAe,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAEsD,GAAAA,gBAAgB4I,oBAAoB,kBAAmB,CAC1F,CACF,CACF,GAGF,sBAAA,cAAC,SAAI,WAAWvM,OAAOwM,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAtM,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAEhC,OAAI,EAAA,WAAWL,OAAOyM,YAAW,QAAA,MAAA,UAAA;AAAA,IAAAvM,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,MAAG,EAAA,WAAWL,OAAO0M,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAxM,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAC,GAAA,iCAE9B,sBAAA,cAAA,OAAA,EAAI,OAAO;AAAA,IAAEY,UAAU;AAAA,IAAUqB,SAAS;AAAA,IAAKqK,WAAW;AAAA,EAAA,GAAY,QAAA,MAAA,UAAA;AAAA,IAAAzM,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EACpEtR,GAAAA,eAAemB,UAAUW,iBAAiB8H,YAAa,CAAA,GAAGzJ,QAAQ,WACrE,CACF,GACA,sBAAA,cAAC,UACC,EAAA,WAAW,GAAG8Q,OAAO4M,eAAe,IAAInX,aAAYuK,OAAOvK,YAAY,EAAE,IACzE,SAAS0V,WACT,OAAO1V,aAAY,kBAAkB,cACrC,cAAYA,aAAY,kBAAkB,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAyK,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAEtD5K,GAAAA,aAAY,OAAO,IACtB,CACF,GAGC,sBAAA,cAAA,OAAA,EAAI,WAAWuK,OAAOvM,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAyM,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC9B,OAAI,EAAA,WAAWL,OAAO6M,UAAS,QAAA,MAAA,UAAA;AAAA,IAAA3M,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC7B,OAAI,EAAA,WAAWL,OAAO8M,WAAU,QAAA,MAAA,UAAA;AAAA,IAAA5M,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAEnQ,UAAUI,KAAM,GACnD,sBAAA,cAAC,OAAI,EAAA,WAAW0P,OAAO+M,WAAU,QAAA,MAAA,UAAA;AAAA,IAAA7M,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,QAAM,CAC1C,GACA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAO6M,UAAS,QAAA,MAAA,UAAA;AAAA,IAAA3M,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC7B,OAAI,EAAA,WAAWL,OAAO8M,WAAU,QAAA,MAAA,UAAA;AAAA,IAAA5M,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAEnQ,UAAUK,KAAM,GACnD,sBAAA,cAAC,OAAI,EAAA,WAAWyP,OAAO+M,WAAU,QAAA,MAAA,UAAA;AAAA,IAAA7M,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,QAAM,CAC1C,GACA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAO6M,UAAS,QAAA,MAAA,UAAA;AAAA,IAAA3M,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC7B,OAAI,EAAA,WAAWL,OAAO8M,WAAU,QAAA,MAAA,UAAA;AAAA,IAAA5M,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAEnQ,EAAAA,GAAAA,UAAUS,UAAS,GAAC,GACvD,sBAAA,cAAC,OAAI,EAAA,WAAWqP,OAAO+M,WAAU,QAAA,MAAA,UAAA;AAAA,IAAA7M,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,UAAQ,CAC5C,GACA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAO6M,UAAS,QAAA,MAAA,UAAA;AAAA,IAAA3M,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC7B,OAAI,EAAA,WAAWL,OAAO8M,WAAU,QAAA,MAAA,UAAA;AAAA,IAAA5M,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAEnQ,UAAUM,KAAM,GACnD,sBAAA,cAAC,OAAI,EAAA,WAAWwP,OAAO+M,WAAU,QAAA,MAAA,UAAA;AAAA,IAAA7M,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,OAAK,CACzC,CACF,GAGA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAOgN,cAAa,QAAA,MAAA,UAAA;AAAA,IAAA9M,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EACjCpD,GAAAA,OAAOgQ,OAAOle,cAAc,EAAEmM,IAAK2E,CAAAA,aACjC,sBAAA,cAAA,UAAA,EACC,KAAKA,SAAS5Q,IACd,WAAW,GAAG+Q,OAAOkN,cAAc,IACjChd,UAAUW,oBAAoBgP,SAAS5Q,KAAK+Q,OAAOmN,SAAS,EAAE,IAEhE,SAAS,MAAM3N,eAAeK,SAAS5Q,EAAE,GAAE,QAAA,MAAA,UAAA;AAAA,IAAAiR,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAE3C,GAAA,sBAAA,cAAC,QAAI,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAER,SAAS1Q,IAAK,uCACpB,QAAI,EAAA,QAAA,MAAA,UAAA;AAAA,IAAA+Q,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAER,GAAAA,SAAS3Q,IAAK,CACvB,CACD,CACH,GAGCgB,UAAUW,oBAAoB9B,eAAeC,eAAeC,MAAM8Q,uBAClE7P,UAAUW,oBAAoB9B,eAAeQ,eAAeN,MAAMyT,uBAClExS,UAAUW,oBAAoB9B,eAAeS,eAAeP,MAAMmU,oBAAAA,GAGnE,sBAAA,cAAC,SAAI,WAAWpD,OAAOoN,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAlN,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAClC,GAAA,sBAAA,cAAC,UAAO,EAAA,WAAWL,OAAOqN,eAAe,SAAS,MAAM5V,MAAM,mGAAmG,GAAE,QAAA,MAAA,UAAA;AAAA,IAAAyI,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAC,GAAA,aAEpK,GACA,sBAAA,cAAC,YAAO,WAAWL,OAAOqN,eAAe,SAAS,MAAMtW,mBAAmB,IAAI,GAAE,QAAA,MAAA,UAAA;AAAA,IAAAmJ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,cAElF,GACA,sBAAA,cAAC,UAAO,EAAA,WAAWL,OAAOqN,eAAe,SAAS3d,QAAO,QAAA,MAAA,UAAA;AAAA,IAAAwQ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAC,WAE1D,CACF,CACF,CACF;AAEJ;;;;;"}