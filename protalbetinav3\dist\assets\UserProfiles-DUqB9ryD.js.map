{"version": 3, "file": "UserProfiles-DUqB9ryD.js", "sources": ["../../src/components/pages/UserProfiles/UserProfiles.jsx"], "sourcesContent": ["/**\r\n * @file UserProfiles.jsx\r\n * @description Página de gerenciamento de perfis de usuário\r\n * @version 3.0.0\r\n */\r\n\r\nimport React, { useState, useEffect } from 'react'\r\nimport styles from './UserProfiles.module.css'\r\n\r\nfunction UserProfiles() {\r\n  const [profiles, setProfiles] = useState([])\r\n  const [activeProfile, setActiveProfile] = useState(null)\r\n  const [showCreateForm, setShowCreateForm] = useState(false)\r\n  const [newProfile, setNewProfile] = useState({\r\n    name: '',\r\n    age: '',\r\n    avatar: '👶',\r\n    preferences: {\r\n      theme: 'default',\r\n      difficulty: 'easy',\r\n      soundEnabled: true,\r\n      animationsEnabled: true\r\n    }\r\n  })\r\n  \r\n  // Novo estado para estatísticas dos perfis\r\n  const [profileStats, setProfileStats] = useState({})\r\n  const [loadingStats, setLoadingStats] = useState(false)\r\n\r\n  // Função para buscar estatísticas do perfil da API\r\n  const fetchProfileStats = async (profileId) => {\r\n    if (!profileId) return\r\n    \r\n    try {\r\n      setLoadingStats(true)\r\n      \r\n      // Buscar estatísticas de sessões de jogos\r\n      const response = await fetch(`/api/metrics/game-sessions?userId=${profileId}`)\r\n      if (response.ok) {\r\n        const data = await response.json()\r\n        \r\n        // Calcular estatísticas\r\n        const stats = {\r\n          gamesPlayed: data.sessions?.length || 0,\r\n          totalTime: data.sessions?.reduce((total, session) => total + (session.duration || 0), 0) || 0,\r\n          lastPlayed: data.sessions?.length > 0 ? \r\n            Math.max(...data.sessions.map(s => new Date(s.timestamp).getTime())) : null,\r\n          favoriteGames: data.favoriteGames || [],\r\n          achievements: data.achievements || [],\r\n          avgPerformance: data.avgPerformance || 0\r\n        }\r\n        \r\n        setProfileStats(prev => ({\r\n          ...prev,\r\n          [profileId]: stats\r\n        }))\r\n\r\n        // 🎯 NOVO: Vincular perfil ao usuário do dashboard (se logado)\r\n        const dashboardUser = localStorage.getItem('userData')\r\n        if (dashboardUser) {\r\n          try {\r\n            const user = JSON.parse(dashboardUser)\r\n            const linkResponse = await fetch('/api/dashboard/link-profile', {\r\n              method: 'POST',\r\n              headers: { \r\n                'Content-Type': 'application/json',\r\n                'Authorization': `Bearer ${localStorage.getItem('authToken')}`\r\n              },\r\n              body: JSON.stringify({ \r\n                profileId, \r\n                dashboardUserId: user.id,\r\n                profileData: { gamesPlayed: stats.gamesPlayed, totalTime: stats.totalTime }\r\n              })\r\n            })\r\n            if (linkResponse.ok) {\r\n              console.log(`✅ Perfil ${profileId} vinculado ao usuário do dashboard ${user.email}`)\r\n            }\r\n          } catch (linkError) {\r\n            console.warn('Falha ao vincular perfil ao dashboard:', linkError)\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Erro ao buscar estatísticas do perfil:', error)\r\n    } finally {\r\n      setLoadingStats(false)\r\n    }\r\n  }\r\n\r\n  // 🎯 NOVO: Carregar perfis localmente (sem autenticação)\r\n  useEffect(() => {\r\n    const loadProfiles = () => {\r\n      // Carregar perfis do localStorage (família local)\r\n      const savedProfiles = JSON.parse(localStorage.getItem('betina_profiles') || '[]')\r\n      setProfiles(savedProfiles)\r\n      \r\n      const activeId = localStorage.getItem('betina_active_profile')\r\n      if (activeId && savedProfiles.length > 0) {\r\n        const active = savedProfiles.find(p => p.id === activeId)\r\n        setActiveProfile(active)\r\n      }\r\n      \r\n      // Buscar estatísticas para todos os perfis\r\n      savedProfiles.forEach(profile => {\r\n        fetchProfileStats(profile.id)\r\n      })\r\n    }\r\n    \r\n    loadProfiles()\r\n  }, [])\r\n\r\n  // Atualizar estatísticas quando perfil ativo mudar\r\n  useEffect(() => {\r\n    if (activeProfile) {\r\n      fetchProfileStats(activeProfile.id)\r\n    }\r\n  }, [activeProfile])\r\n\r\n  // 🎯 NOVO: Salvar perfis localmente (sem API)\r\n  const saveProfiles = (newProfiles) => {\r\n    setProfiles(newProfiles)\r\n    localStorage.setItem('betina_profiles', JSON.stringify(newProfiles))\r\n  }\r\n\r\n  // Criar novo perfil\r\n  const handleCreateProfile = () => {\r\n    if (!newProfile.name.trim()) {\r\n      alert('Por favor, digite um nome para o perfil')\r\n      return\r\n    }\r\n\r\n    const profile = {\r\n      id: Date.now().toString(),\r\n      ...newProfile,\r\n      createdAt: new Date().toISOString(),\r\n      lastUsed: new Date().toISOString(),\r\n      gamesPlayed: 0,\r\n      totalTime: 0\r\n    }\r\n\r\n    const updatedProfiles = [...profiles, profile]\r\n    saveProfiles(updatedProfiles)\r\n    \r\n    setNewProfile({\r\n      name: '',\r\n      age: '',\r\n      avatar: '👶',\r\n      preferences: {\r\n        theme: 'default',\r\n        difficulty: 'easy',\r\n        soundEnabled: true,\r\n        animationsEnabled: true\r\n      }\r\n    })\r\n    setShowCreateForm(false)\r\n  }\r\n\r\n  // 🎯 NOVO: Selecionar perfil ativo localmente\r\n  const selectProfile = (profile) => {\r\n    setActiveProfile(profile)\r\n    localStorage.setItem('betina_active_profile', profile.id)\r\n    \r\n    // Atualizar último uso\r\n    const updatedProfiles = profiles.map(p => \r\n      p.id === profile.id \r\n        ? { ...p, lastUsed: new Date().toISOString() }\r\n        : p\r\n    )\r\n    saveProfiles(updatedProfiles)\r\n\r\n    // Buscar estatísticas do novo perfil ativo\r\n    fetchProfileStats(profile.id)\r\n  }\r\n\r\n  // 🎯 NOVO: Deletar perfil localmente\r\n  const deleteProfile = (profileId) => {\r\n    if (confirm('Tem certeza que deseja deletar este perfil?')) {\r\n      const updatedProfiles = profiles.filter(p => p.id !== profileId)\r\n      saveProfiles(updatedProfiles)\r\n      \r\n      if (activeProfile?.id === profileId) {\r\n        setActiveProfile(null)\r\n        localStorage.removeItem('betina_active_profile')\r\n      }\r\n    }\r\n  }\r\n\r\n  const avatarOptions = ['👶', '👧', '👦', '🧒', '👨', '👩', '🐱', '🐶', '🦋', '🌟']\r\n  return (\r\n    <div className={styles.container}>\r\n      {/* Header Principal Centralizado */}\r\n      <div className={styles.header}>\r\n        <h1 className={styles.mainTitle}>� Gerenciar Perfis</h1>\r\n        <p className={styles.subtitle}>\r\n          Crie e gerencie perfis personalizados para toda a família\r\n        </p>\r\n      </div>\r\n\r\n      {/* Perfil Ativo */}\r\n      {activeProfile && (\r\n        <section className={styles.section}>\r\n          <h2 className={styles.sectionTitle}>\r\n            <span>⭐</span>\r\n            Perfil Ativo\r\n          </h2>\r\n          <div className={styles.activeProfileCard}>\r\n            <div className={styles.activeProfileAvatar}>{activeProfile.avatar}</div>\r\n            <div className={styles.activeProfileInfo}>\r\n              <h3 className={styles.activeProfileName}>{activeProfile.name}</h3>\r\n              <div className={styles.activeProfileStats}>\r\n                <div className={styles.statItem}>\r\n                  <span className={styles.statIcon}>🎂</span>\r\n                  <span>{activeProfile.age ? `${activeProfile.age} anos` : 'Idade não informada'}</span>\r\n                </div>\r\n                <div className={styles.statItem}>\r\n                  <span className={styles.statIcon}>🎮</span>\r\n                  <span>\r\n                    {loadingStats ? 'Carregando...' : \r\n                     `${profileStats[activeProfile.id]?.gamesPlayed || 0} jogos jogados`}\r\n                  </span>\r\n                </div>\r\n                <div className={styles.statItem}>\r\n                  <span className={styles.statIcon}>⏰</span>\r\n                  <span>\r\n                    {profileStats[activeProfile.id]?.totalTime ? \r\n                     `${Math.round(profileStats[activeProfile.id].totalTime / 1000 / 60)} min jogados` :\r\n                     `Último acesso: ${new Date(activeProfile.lastUsed).toLocaleDateString()}`}\r\n                  </span>\r\n                </div>\r\n                {profileStats[activeProfile.id]?.favoriteGames?.length > 0 && (\r\n                  <div className={styles.statItem}>\r\n                    <span className={styles.statIcon}>⭐</span>\r\n                    <span>Jogo favorito: {profileStats[activeProfile.id].favoriteGames[0]}</span>\r\n                  </div>\r\n                )}\r\n                {profileStats[activeProfile.id]?.avgPerformance > 0 && (\r\n                  <div className={styles.statItem}>\r\n                    <span className={styles.statIcon}>📊</span>\r\n                    <span>Performance média: {Math.round(profileStats[activeProfile.id].avgPerformance)}%</span>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </section>\r\n      )}\r\n\r\n      {/* Seção: Todos os Perfis */}\r\n      <section className={styles.section}>\r\n        <div className={styles.profilesGrid}>\r\n          {profiles.map(profile => (\r\n            <div \r\n              key={profile.id}\r\n              className={`${styles.profileCard} ${activeProfile?.id === profile.id ? styles.active : ''}`}\r\n              onClick={() => selectProfile(profile)}\r\n            >\r\n              <div className={styles.profileAvatar}>{profile.avatar}</div>\r\n              <div className={styles.profileInfo}>\r\n                <h4 className={styles.profileName}>{profile.name}</h4>\r\n                <p className={styles.profileAge}>\r\n                  {profile.age ? `${profile.age} anos` : 'Idade não informada'}\r\n                </p>\r\n                <p className={styles.profileStats}>\r\n                  {profile.gamesPlayed || 0} jogos jogados\r\n                </p>\r\n              </div>\r\n              <button \r\n                className={styles.profileDeleteBtn}\r\n                onClick={(e) => {\r\n                  e.stopPropagation()\r\n                  deleteProfile(profile.id)\r\n                }}\r\n                title=\"Deletar perfil\"\r\n              >\r\n                🗑️\r\n              </button>\r\n            </div>\r\n          ))}\r\n\r\n          {/* Botão Adicionar Perfil */}\r\n          <div \r\n            className={`${styles.profileCard} ${styles.addProfileCard}`}\r\n            onClick={() => setShowCreateForm(true)}\r\n          >\r\n            <div className={styles.addProfileIcon}>➕</div>\r\n            <p>Adicionar Perfil</p>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Seção: Informações */}\r\n      <section className={styles.section}>\r\n        <h2 className={styles.sectionTitle}>\r\n          <span>💡</span>\r\n          Como Funciona?\r\n        </h2>\r\n        <div className={styles.sectionContent}>\r\n          <p>\r\n            Os <strong>perfis de usuário</strong> permitem que cada criança da família \r\n            tenha sua própria experiência personalizada. As métricas e progressos são \r\n            automaticamente vinculados ao responsável que fizer login no dashboard.\r\n          </p>\r\n          \r\n          <ul className={styles.benefitsList}>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>📊</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Progresso Individual:</strong> Cada criança mantém seu próprio histórico de jogos e conquistas\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>👨‍👩‍👧‍👦</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Fácil para Crianças:</strong> Interface simples, sem necessidade de login ou senhas\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>�</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Métricas no Dashboard:</strong> Pais/responsáveis acessam relatórios detalhados via dashboard\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>�</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Dados Seguros:</strong> Todas as informações ficam salvas localmente no seu dispositivo\r\n              </span>\r\n            </li>\r\n          </ul>        </div>\r\n      </section>\r\n\r\n      {/* Formulário de Criação */}\r\n      {showCreateForm && (\r\n        <div className={styles.createProfileOverlay}>\r\n          <div className={styles.createProfileForm}>\r\n            <div className={styles.formHeader}>\r\n              <h3 className={styles.formTitle}>➕ Criar Novo Perfil</h3>\r\n              <p className={styles.formSubtitle}>Adicione um novo membro da família</p>\r\n            </div>\r\n            \r\n            <div className={styles.formGroup}>\r\n              <label htmlFor=\"profile-name\" className={styles.formLabel}>Nome:</label>\r\n              <input\r\n                type=\"text\"\r\n                id=\"profile-name\"\r\n                name=\"profile-name\"\r\n                value={newProfile.name}\r\n                onChange={(e) => setNewProfile({...newProfile, name: e.target.value})}\r\n                placeholder=\"Digite o nome...\"\r\n                maxLength={20}\r\n                className={styles.input}\r\n                autoComplete=\"given-name\"\r\n              />\r\n            </div>\r\n\r\n            <div className={styles.formGroup}>\r\n              <label htmlFor=\"profile-age\" className={styles.formLabel}>Idade (opcional):</label>\r\n              <input\r\n                type=\"number\"\r\n                id=\"profile-age\"\r\n                name=\"profile-age\"\r\n                value={newProfile.age}\r\n                onChange={(e) => setNewProfile({...newProfile, age: e.target.value})}\r\n                min=\"1\"\r\n                max=\"100\"\r\n                placeholder=\"Ex: 5\"\r\n                className={styles.input}\r\n                autoComplete=\"age\"\r\n              />\r\n            </div>\r\n\r\n            <div className={styles.formGroup}>\r\n              <label htmlFor=\"avatar-selector\" className={styles.formLabel}>Escolha um Avatar:</label>\r\n              <div className={styles.avatarSelector} id=\"avatar-selector\" role=\"radiogroup\" aria-labelledby=\"avatar-selector\">\r\n                {avatarOptions.map((avatar, index) => (\r\n                  <button\r\n                    key={avatar}\r\n                    type=\"button\"\r\n                    role=\"radio\"\r\n                    aria-checked={newProfile.avatar === avatar}\r\n                    aria-label={`Avatar ${index + 1}: ${avatar}`}\r\n                    className={`${styles.avatarOption} ${newProfile.avatar === avatar ? styles.selected : ''}`}\r\n                    onClick={() => setNewProfile({...newProfile, avatar})}\r\n                  >\r\n                    {avatar}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            <div className={styles.formActions}>\r\n              <button \r\n                className={`${styles.btn} ${styles.btnPrimary}`}\r\n                onClick={handleCreateProfile}\r\n              >\r\n                ✅ Criar Perfil\r\n              </button>\r\n              <button \r\n                className={`${styles.btn} ${styles.btnSecondary}`}\r\n                onClick={() => setShowCreateForm(false)}\r\n              >\r\n                ❌ Cancelar\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default UserProfiles\r\n"], "names": ["useState", "profileStats", "useEffect", "active", "jsxDEV"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,SAAS,eAAe;AACtB,QAAM,CAAC,UAAU,WAAW,IAAIA,aAAAA,SAAS,CAAA,CAAE;AAC3C,QAAM,CAAC,eAAe,gBAAgB,IAAIA,aAAAA,SAAS,IAAI;AACvD,QAAM,CAAC,gBAAgB,iBAAiB,IAAIA,aAAAA,SAAS,KAAK;AAC1D,QAAM,CAAC,YAAY,aAAa,IAAIA,sBAAS;AAAA,IAC3C,MAAM;AAAA,IACN,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,aAAa;AAAA,MACX,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,mBAAmB;AAAA,IAAA;AAAA,EACrB,CACD;AAGD,QAAM,CAACC,eAAc,eAAe,IAAID,aAAAA,SAAS,CAAA,CAAE;AACnD,QAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAS,KAAK;AAGhD,QAAA,oBAAoB,OAAO,cAAc;AAC7C,QAAI,CAAC,UAAW;AAEZ,QAAA;AACF,sBAAgB,IAAI;AAGpB,YAAM,WAAW,MAAM,MAAM,qCAAqC,SAAS,EAAE;AAC7E,UAAI,SAAS,IAAI;AACT,cAAA,OAAO,MAAM,SAAS,KAAK;AAGjC,cAAM,QAAQ;AAAA,UACZ,aAAa,KAAK,UAAU,UAAU;AAAA,UACtC,WAAW,KAAK,UAAU,OAAO,CAAC,OAAO,YAAY,SAAS,QAAQ,YAAY,IAAI,CAAC,KAAK;AAAA,UAC5F,YAAY,KAAK,UAAU,SAAS,IAClC,KAAK,IAAI,GAAG,KAAK,SAAS,IAAI,CAAK,MAAA,IAAI,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,IAAI;AAAA,UACzE,eAAe,KAAK,iBAAiB,CAAC;AAAA,UACtC,cAAc,KAAK,gBAAgB,CAAC;AAAA,UACpC,gBAAgB,KAAK,kBAAkB;AAAA,QACzC;AAEA,wBAAgB,CAAS,UAAA;AAAA,UACvB,GAAG;AAAA,UACH,CAAC,SAAS,GAAG;AAAA,QAAA,EACb;AAGI,cAAA,gBAAgB,aAAa,QAAQ,UAAU;AACrD,YAAI,eAAe;AACb,cAAA;AACI,kBAAA,OAAO,KAAK,MAAM,aAAa;AAC/B,kBAAA,eAAe,MAAM,MAAM,+BAA+B;AAAA,cAC9D,QAAQ;AAAA,cACR,SAAS;AAAA,gBACP,gBAAgB;AAAA,gBAChB,iBAAiB,UAAU,aAAa,QAAQ,WAAW,CAAC;AAAA,cAC9D;AAAA,cACA,MAAM,KAAK,UAAU;AAAA,gBACnB;AAAA,gBACA,iBAAiB,KAAK;AAAA,gBACtB,aAAa,EAAE,aAAa,MAAM,aAAa,WAAW,MAAM,UAAU;AAAA,cAC3E,CAAA;AAAA,YAAA,CACF;AACD,gBAAI,aAAa,IAAI;AACnB,sBAAQ,IAAI,YAAY,SAAS,sCAAsC,KAAK,KAAK,EAAE;AAAA,YAAA;AAAA,mBAE9E,WAAW;AACV,oBAAA,KAAK,0CAA0C,SAAS;AAAA,UAAA;AAAA,QAClE;AAAA,MACF;AAAA,aAEK,OAAO;AACN,cAAA,MAAM,0CAA0C,KAAK;AAAA,IAAA,UAC7D;AACA,sBAAgB,KAAK;AAAA,IAAA;AAAA,EAEzB;AAGAE,eAAAA,UAAU,MAAM;AACd,UAAM,eAAe,MAAM;AAEzB,YAAM,gBAAgB,KAAK,MAAM,aAAa,QAAQ,iBAAiB,KAAK,IAAI;AAChF,kBAAY,aAAa;AAEnB,YAAA,WAAW,aAAa,QAAQ,uBAAuB;AACzD,UAAA,YAAY,cAAc,SAAS,GAAG;AACxC,cAAMC,UAAS,cAAc,KAAK,CAAK,MAAA,EAAE,OAAO,QAAQ;AACxD,yBAAiBA,OAAM;AAAA,MAAA;AAIzB,oBAAc,QAAQ,CAAW,YAAA;AAC/B,0BAAkB,QAAQ,EAAE;AAAA,MAAA,CAC7B;AAAA,IACH;AAEa,iBAAA;AAAA,EACf,GAAG,EAAE;AAGLD,eAAAA,UAAU,MAAM;AACd,QAAI,eAAe;AACjB,wBAAkB,cAAc,EAAE;AAAA,IAAA;AAAA,EACpC,GACC,CAAC,aAAa,CAAC;AAGZ,QAAA,eAAe,CAAC,gBAAgB;AACpC,gBAAY,WAAW;AACvB,iBAAa,QAAQ,mBAAmB,KAAK,UAAU,WAAW,CAAC;AAAA,EACrE;AAGA,QAAM,sBAAsB,MAAM;AAChC,QAAI,CAAC,WAAW,KAAK,QAAQ;AAC3B,YAAM,yCAAyC;AAC/C;AAAA,IAAA;AAGF,UAAM,UAAU;AAAA,MACd,IAAI,KAAK,IAAI,EAAE,SAAS;AAAA,MACxB,GAAG;AAAA,MACH,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,WAAU,oBAAI,KAAK,GAAE,YAAY;AAAA,MACjC,aAAa;AAAA,MACb,WAAW;AAAA,IACb;AAEA,UAAM,kBAAkB,CAAC,GAAG,UAAU,OAAO;AAC7C,iBAAa,eAAe;AAEd,kBAAA;AAAA,MACZ,MAAM;AAAA,MACN,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,aAAa;AAAA,QACX,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,mBAAmB;AAAA,MAAA;AAAA,IACrB,CACD;AACD,sBAAkB,KAAK;AAAA,EACzB;AAGM,QAAA,gBAAgB,CAAC,YAAY;AACjC,qBAAiB,OAAO;AACX,iBAAA,QAAQ,yBAAyB,QAAQ,EAAE;AAGxD,UAAM,kBAAkB,SAAS;AAAA,MAAI,CACnC,MAAA,EAAE,OAAO,QAAQ,KACb,EAAE,GAAG,GAAG,WAAc,oBAAA,KAAA,GAAO,kBAC7B;AAAA,IACN;AACA,iBAAa,eAAe;AAG5B,sBAAkB,QAAQ,EAAE;AAAA,EAC9B;AAGM,QAAA,gBAAgB,CAAC,cAAc;AAC/B,QAAA,QAAQ,6CAA6C,GAAG;AAC1D,YAAM,kBAAkB,SAAS,OAAO,CAAK,MAAA,EAAE,OAAO,SAAS;AAC/D,mBAAa,eAAe;AAExB,UAAA,eAAe,OAAO,WAAW;AACnC,yBAAiB,IAAI;AACrB,qBAAa,WAAW,uBAAuB;AAAA,MAAA;AAAA,IACjD;AAAA,EAEJ;AAEM,QAAA,gBAAgB,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AACjF,SACGE,qCAAAA,OAAA,OAAA,EAAI,WAAW,OAAO,WAErB,UAAA;AAAA,IAACA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,QACrB,UAAA;AAAA,MAAAA,4CAAC,MAAG,EAAA,WAAW,OAAO,WAAW,UAAjC,qBAAA,GAAA,QAAA,OAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAmD,GAAA,IAAA;AAAA,MAClDA,4CAAA,KAAA,EAAE,WAAW,OAAO,UAAU,UAA/B,4DAAA,GAAA,QAAA,OAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA,GAEA,IAAA;AAAA,IAAA,EAJF,GAAA,QAAA,MAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IAKA,GAAA,IAAA;AAAA,IAGC,iBACCA,qCAAA,OAAC,WAAQ,EAAA,WAAW,OAAO,SACzB,UAAA;AAAA,MAACA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,cACpB,UAAA;AAAA,QAAAA,qCAAA,OAAC,UAAK,UAAN,IAAA,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAO,GAAA,IAAA;AAAA,QAAO;AAAA,MAAA,EADhB,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAGA,GAAA,IAAA;AAAA,MACCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,mBACrB,UAAA;AAAA,QAAAA,qCAAAA,OAAC,OAAI,EAAA,WAAW,OAAO,qBAAsB,wBAAc,UAA3D,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAkE,GAAA,IAAA;AAAA,QACjEA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,mBACrB,UAAA;AAAA,UAAAA,qCAAAA,OAAC,MAAG,EAAA,WAAW,OAAO,mBAAoB,wBAAc,QAAxD,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAA6D,GAAA,IAAA;AAAA,UAC5DA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,oBACrB,UAAA;AAAA,YAACA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,UACrB,UAAA;AAAA,cAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,UAAU,UAAlC,KAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAoC,GAAA,IAAA;AAAA,cACpCA,qCAAAA,OAAC,UAAM,UAAc,cAAA,MAAM,GAAG,cAAc,GAAG,UAAU,sBAAzD,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA,GAA+E,IAAA;AAAA,YAAA,EAFjF,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAGA,GAAA,IAAA;AAAA,YACCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,UACrB,UAAA;AAAA,cAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,UAAU,UAAlC,KAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAoC,GAAA,IAAA;AAAA,cACpCA,qCAAA,OAAC,QACE,EAAA,UAAA,eAAe,kBACf,GAAGH,cAAa,cAAc,EAAE,GAAG,eAAe,CAAC,iBAFtD,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA,GAGA,IAAA;AAAA,YAAA,EALF,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAMA,GAAA,IAAA;AAAA,YACCG,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,UACrB,UAAA;AAAA,cAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,UAAU,UAAlC,IAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAmC,GAAA,IAAA;AAAA,cACnCA,qCAAAA,OAAC,QACE,EAAA,UAAAH,cAAa,cAAc,EAAE,GAAG,YAChC,GAAG,KAAK,MAAMA,cAAa,cAAc,EAAE,EAAE,YAAY,MAAO,EAAE,CAAC,iBACnE,kBAAkB,IAAI,KAAK,cAAc,QAAQ,EAAE,mBAAoB,CAAA,GAH1E,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA,GAIA,IAAA;AAAA,YAAA,EANF,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAOA,GAAA,IAAA;AAAA,YACCA,cAAa,cAAc,EAAE,GAAG,eAAe,SAAS,KACtDG,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,UACrB,UAAA;AAAA,cAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,UAAU,UAAlC,IAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAmC,GAAA,IAAA;AAAA,0DAClC,QAAK,EAAA,UAAA;AAAA,gBAAA;AAAA,gBAAgBH,cAAa,cAAc,EAAE,EAAE,cAAc,CAAC;AAAA,cAAA,EAApE,GAAA,QAAA,MAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA,GAAsE,IAAA;AAAA,YAAA,EAFxE,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAGA,GAAA,IAAA;AAAA,YAEDA,cAAa,cAAc,EAAE,GAAG,iBAAiB,KAC/CG,4CAAA,OAAA,EAAI,WAAW,OAAO,UACrB,UAAA;AAAA,cAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,UAAU,UAAlC,KAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAoC,GAAA,IAAA;AAAA,0DACnC,QAAK,EAAA,UAAA;AAAA,gBAAA;AAAA,gBAAoB,KAAK,MAAMH,cAAa,cAAc,EAAE,EAAE,cAAc;AAAA,gBAAE;AAAA,cAAA,EAApF,GAAA,QAAA,MAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA,GAAqF,IAAA;AAAA,YAAA,EAFvF,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAGA,IAAA;AAAA,UAAA,EA9BJ,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAgCA,IAAA;AAAA,QAAA,EAlCF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAmCA,IAAA;AAAA,MAAA,EArCF,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA,GAsCA,IAAA;AAAA,IAAA,EA3CF,GAAA,QAAA,MAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IA4CA,GAAA,IAAA;AAAA,IAIFG,qCAAAA,OAAC,aAAQ,WAAW,OAAO,SACzB,UAACA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,cACpB,UAAA;AAAA,MAAA,SAAS,IAAI,CACZ,YAAAA,qCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UAEC,WAAW,GAAG,OAAO,WAAW,IAAI,eAAe,OAAO,QAAQ,KAAK,OAAO,SAAS,EAAE;AAAA,UACzF,SAAS,MAAM,cAAc,OAAO;AAAA,UAEpC,UAAA;AAAA,YAAAA,qCAAAA,OAAC,OAAI,EAAA,WAAW,OAAO,eAAgB,kBAAQ,UAA/C,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAsD,GAAA,IAAA;AAAA,YACrDA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,aACrB,UAAA;AAAA,cAAAA,qCAAAA,OAAC,MAAG,EAAA,WAAW,OAAO,aAAc,kBAAQ,QAA5C,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAiD,GAAA,IAAA;AAAA,cACjDA,qCAAA,OAAC,KAAE,EAAA,WAAW,OAAO,YAClB,UAAQ,QAAA,MAAM,GAAG,QAAQ,GAAG,UAAU,sBADzC,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAEA,GAAA,IAAA;AAAA,cACCA,qCAAA,OAAA,KAAA,EAAE,WAAW,OAAO,cAClB,UAAA;AAAA,gBAAA,QAAQ,eAAe;AAAA,gBAAE;AAAA,cAAA,EAD5B,GAAA,QAAA,MAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA,GAEA,IAAA;AAAA,YAAA,EAPF,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAQA,GAAA,IAAA;AAAA,YACAA,qCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACC,WAAW,OAAO;AAAA,gBAClB,SAAS,CAAC,MAAM;AACd,oBAAE,gBAAgB;AAClB,gCAAc,QAAQ,EAAE;AAAA,gBAC1B;AAAA,gBACA,OAAM;AAAA,gBACP,UAAA;AAAA,cAAA;AAAA,cAPD;AAAA,cAAA;AAAA,cAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA;AAAA,cAAA;AAAA,YAAA;AAAA,UASA;AAAA,QAAA;AAAA,QAvBK,QAAQ;AAAA,QADf;AAAA,QAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA;AAAA,QAAA;AAAA,MAAA,CA0BD;AAAA,MAGDA,qCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,WAAW,GAAG,OAAO,WAAW,IAAI,OAAO,cAAc;AAAA,UACzD,SAAS,MAAM,kBAAkB,IAAI;AAAA,UAErC,UAAA;AAAA,YAAAA,4CAAC,OAAI,EAAA,WAAW,OAAO,gBAAgB,UAAvC,IAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAwC,GAAA,IAAA;AAAA,YACxCA,qCAAA,OAAC,OAAE,UAAH,mBAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAAmB,IAAA;AAAA,UAAA;AAAA,QAAA;AAAA,QALrB;AAAA,QAAA;AAAA,QAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IAMA,EArCF,GAAA,QAAA,MAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IAAA,GAAA,IAsCA,EAvCF,GAAA,QAAA,OAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IAwCA,GAAA,IAAA;AAAA,IAGCA,qCAAA,OAAA,WAAA,EAAQ,WAAW,OAAO,SACzB,UAAA;AAAA,MAACA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,cACpB,UAAA;AAAA,QAAAA,qCAAA,OAAC,UAAK,UAAN,KAAA,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAQ,GAAA,IAAA;AAAA,QAAO;AAAA,MAAA,EADjB,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAGA,GAAA,IAAA;AAAA,MACCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,gBACrB,UAAA;AAAA,QAAAA,4CAAC,KAAE,EAAA,UAAA;AAAA,UAAA;AAAA,UACEA,qCAAA,OAAC,YAAO,UAAR,oBAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAyB,GAAA,IAAA;AAAA,UAAS;AAAA,QAAA,EADvC,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAIA,GAAA,IAAA;AAAA,QAECA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,cACpB,UAAA;AAAA,UAACA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,aACpB,UAAA;AAAA,YAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,cAAc,UAAtC,KAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAwC,GAAA,IAAA;AAAA,YACvCA,qCAAA,OAAA,QAAA,EAAK,WAAW,OAAO,aACtB,UAAA;AAAA,cAAAA,qCAAA,OAAC,YAAO,UAAR,wBAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAA6B,GAAA,IAAA;AAAA,cAAS;AAAA,YAAA,EADxC,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EAJF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAKA,GAAA,IAAA;AAAA,UACCA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,aACpB,UAAA;AAAA,YAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,cAAc,UAAtC,cAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAiD,GAAA,IAAA;AAAA,YAChDA,qCAAA,OAAA,QAAA,EAAK,WAAW,OAAO,aACtB,UAAA;AAAA,cAAAA,qCAAA,OAAC,YAAO,UAAR,uBAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAA4B,GAAA,IAAA;AAAA,cAAS;AAAA,YAAA,EADvC,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EAJF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAKA,GAAA,IAAA;AAAA,UACCA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,aACpB,UAAA;AAAA,YAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,cAAc,UAAtC,IAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAuC,GAAA,IAAA;AAAA,YACtCA,qCAAA,OAAA,QAAA,EAAK,WAAW,OAAO,aACtB,UAAA;AAAA,cAAAA,qCAAA,OAAC,YAAO,UAAR,yBAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAA8B,GAAA,IAAA;AAAA,cAAS;AAAA,YAAA,EADzC,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EAJF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAKA,GAAA,IAAA;AAAA,UACCA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,aACpB,UAAA;AAAA,YAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,cAAc,UAAtC,IAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAuC,GAAA,IAAA;AAAA,YACtCA,qCAAA,OAAA,QAAA,EAAK,WAAW,OAAO,aACtB,UAAA;AAAA,cAAAA,qCAAA,OAAC,YAAO,UAAR,iBAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAsB,GAAA,IAAA;AAAA,cAAS;AAAA,YAAA,EADjC,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EAJF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAKA,IAAA;AAAA,QAAA,EAxBF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAyBA,GAAA,IAAA;AAAA,QAAK;AAAA,MAAA,EAhCP,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA,GAgCe,IAAA;AAAA,IAAA,EArCjB,GAAA,QAAA,MAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IAsCA,GAAA,IAAA;AAAA,IAGC,kBACEA,qCAAAA,OAAA,OAAA,EAAI,WAAW,OAAO,sBACrB,UAACA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,mBACrB,UAAA;AAAA,MAACA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,YACrB,UAAA;AAAA,QAAAA,4CAAC,MAAG,EAAA,WAAW,OAAO,WAAW,UAAjC,sBAAA,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAoD,GAAA,IAAA;AAAA,QACnDA,4CAAA,KAAA,EAAE,WAAW,OAAO,cAAc,UAAnC,qCAAA,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAAqE,IAAA;AAAA,MAAA,EAFvE,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAGA,GAAA,IAAA;AAAA,MAECA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,WACrB,UAAA;AAAA,QAAAA,qCAAAA,OAAC,WAAM,SAAQ,gBAAe,WAAW,OAAO,WAAW,UAA3D,WAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAgE,GAAA,IAAA;AAAA,QAChEA,qCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACC,MAAK;AAAA,YACL,IAAG;AAAA,YACH,MAAK;AAAA,YACL,OAAO,WAAW;AAAA,YAClB,UAAU,CAAC,MAAM,cAAc,EAAC,GAAG,YAAY,MAAM,EAAE,OAAO,OAAM;AAAA,YACpE,aAAY;AAAA,YACZ,WAAW;AAAA,YACX,WAAW,OAAO;AAAA,YAClB,cAAa;AAAA,UAAA;AAAA,UATf;AAAA,UAAA;AAAA,UAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MAUA,EAZF,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAaA,GAAA,IAAA;AAAA,MAECA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,WACrB,UAAA;AAAA,QAAAA,qCAAAA,OAAC,WAAM,SAAQ,eAAc,WAAW,OAAO,WAAW,UAA1D,uBAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAA2E,GAAA,IAAA;AAAA,QAC3EA,qCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACC,MAAK;AAAA,YACL,IAAG;AAAA,YACH,MAAK;AAAA,YACL,OAAO,WAAW;AAAA,YAClB,UAAU,CAAC,MAAM,cAAc,EAAC,GAAG,YAAY,KAAK,EAAE,OAAO,OAAM;AAAA,YACnE,KAAI;AAAA,YACJ,KAAI;AAAA,YACJ,aAAY;AAAA,YACZ,WAAW,OAAO;AAAA,YAClB,cAAa;AAAA,UAAA;AAAA,UAVf;AAAA,UAAA;AAAA,UAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MAWA,EAbF,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAcA,GAAA,IAAA;AAAA,MAECA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,WACrB,UAAA;AAAA,QAAAA,qCAAAA,OAAC,WAAM,SAAQ,mBAAkB,WAAW,OAAO,WAAW,UAA9D,wBAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAgF,GAAA,IAAA;AAAA,QAC/EA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,gBAAgB,IAAG,mBAAkB,MAAK,cAAa,mBAAgB,mBAC3F,UAAA,cAAc,IAAI,CAAC,QAAQ,UAC1BA,qCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YAEC,MAAK;AAAA,YACL,MAAK;AAAA,YACL,gBAAc,WAAW,WAAW;AAAA,YACpC,cAAY,UAAU,QAAQ,CAAC,KAAK,MAAM;AAAA,YAC1C,WAAW,GAAG,OAAO,YAAY,IAAI,WAAW,WAAW,SAAS,OAAO,WAAW,EAAE;AAAA,YACxF,SAAS,MAAM,cAAc,EAAC,GAAG,YAAY,QAAO;AAAA,YAEnD,UAAA;AAAA,UAAA;AAAA,UARI;AAAA,UADP;AAAA,UAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA;AAAA,UAAA;AAAA,QAAA,CAWD,EAbH,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAcA,IAAA;AAAA,MAAA,EAhBF,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAiBA,GAAA,IAAA;AAAA,MAECA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,aACrB,UAAA;AAAA,QAAAA,qCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACC,WAAW,GAAG,OAAO,GAAG,IAAI,OAAO,UAAU;AAAA,YAC7C,SAAS;AAAA,YACV,UAAA;AAAA,UAAA;AAAA,UAHD;AAAA,UAAA;AAAA,UAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA;AAAA,UAAA;AAAA,QAKA;AAAA,QACAA,qCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACC,WAAW,GAAG,OAAO,GAAG,IAAI,OAAO,YAAY;AAAA,YAC/C,SAAS,MAAM,kBAAkB,KAAK;AAAA,YACvC,UAAA;AAAA,UAAA;AAAA,UAHD;AAAA,UAAA;AAAA,UAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MAKA,EAZF,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA,GAaA,IAAA;AAAA,IAAA,EArEF,GAAA,QAAA,MAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IAAA,GAAA,IAsEA,EAvEF,GAAA,QAAA,OAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IAAA,GAwEA,IAAA;AAAA,EAAA,EAxNJ,GAAA,QAAA,MAAA;AAAA,IAAA,UAAA;AAAA,IAAA,YAAA;AAAA,IAAA,cAAA;AAAA,EA0NA,GAAA,IAAA;AAEJ;"}