import { R as React } from "./vendor-react-Bw1F4Ko6.js";
import { P as PropTypes } from "./vendor-misc-DTF8jSja.js";
import { u as useAccessibility } from "./hooks-Cl3iVr0l.js";
var _jsxFileName$4 = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\components\\context\\SystemContext.jsx";
const SystemContext = React.createContext(null);
function useSystem() {
  const context = React.useContext(SystemContext);
  if (!context) {
    throw new Error("useSystem deve ser usado dentro de um SystemProvider");
  }
  return context;
}
function SystemProvider({
  system,
  children
}) {
  if (!system || typeof system !== "object") {
    throw new Error("SystemProvider requer um objeto system válido");
  }
  const systemValue = React.useMemo(() => ({
    system,
    // Métodos utilitários com binding seguro
    healthCheck: (...args) => system?.healthCheck?.(...args),
    getStatistics: (...args) => system?.getStatistics?.(...args),
    dispatchEvent: (...args) => system?.dispatchEvent?.(...args),
    trackUserInteraction: (...args) => system?.trackUserInteraction?.(...args),
    // Informações de status do sistema com fallback seguro
    getStatus: () => ({
      database: system?.databaseInstance?.getStatus?.() ?? {
        status: "unavailable"
      },
      resilience: system?.resilience?.getCircuitBreakersStatus?.() ?? {
        status: "unavailable"
      },
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      version: "3.1.0"
    })
  }), [system]);
  return /* @__PURE__ */ React.createElement(SystemContext.Provider, { value: systemValue, __self: this, __source: {
    fileName: _jsxFileName$4,
    lineNumber: 61,
    columnNumber: 5
  } }, children);
}
SystemProvider.propTypes = {
  system: PropTypes.shape({
    healthCheck: PropTypes.func,
    getStatistics: PropTypes.func,
    dispatchEvent: PropTypes.func,
    trackUserInteraction: PropTypes.func,
    databaseInstance: PropTypes.shape({
      getStatus: PropTypes.func
    }),
    resilience: PropTypes.shape({
      getCircuitBreakersStatus: PropTypes.func
    })
  }).isRequired,
  children: PropTypes.node.isRequired
};
var _jsxFileName$3 = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\components\\context\\DatabaseProvider.jsx";
const DatabaseContext = React.createContext(null);
function DatabaseProvider({
  children
}) {
  const {
    system
  } = useSystem();
  const databaseInstance = system.databaseInstance;
  const databaseValue = React.useMemo(() => {
    const safeBind = (method) => method ? method.bind(databaseInstance) : () => console.warn("Method not available");
    return {
      // Expõe apenas o que componentes precisam acessar
      db: databaseInstance,
      databaseService: databaseInstance?.manager || databaseInstance,
      saveGameMetrics: safeBind(databaseInstance.saveGameMetrics),
      getUserData: safeBind(databaseInstance.getUserData),
      saveUserData: safeBind(databaseInstance.saveUserData),
      getStatus: safeBind(databaseInstance.getStatus),
      // Acesso ao sistema de resiliência para casos especiais
      resilience: system.resilience
    };
  }, [databaseInstance, system.resilience]);
  return /* @__PURE__ */ React.createElement(DatabaseContext.Provider, { value: databaseValue, __self: this, __source: {
    fileName: _jsxFileName$3,
    lineNumber: 61,
    columnNumber: 5
  } }, children);
}
DatabaseProvider.propTypes = {
  children: PropTypes.node.isRequired
};
var _jsxFileName$2 = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\components\\context\\AccessibilityContext.jsx";
const AccessibilityContext = React.createContext({
  settings: {
    textToSpeech: true,
    highContrast: false,
    reducedMotion: false,
    colorScheme: "default",
    dyslexiaFriendly: false,
    fontSize: "medium",
    soundEnabled: true,
    autoRead: false
  },
  updateSettings: () => {
  },
  applyPreset: () => {
  }
});
function AccessibilityProvider({
  children
}) {
  const accessibilityManager = useAccessibility();
  return /* @__PURE__ */ React.createElement(AccessibilityContext.Provider, { value: accessibilityManager, __self: this, __source: {
    fileName: _jsxFileName$2,
    lineNumber: 34,
    columnNumber: 5
  } }, children);
}
function useAccessibilityContext() {
  return React.useContext(AccessibilityContext);
}
AccessibilityProvider.propTypes = {
  children: PropTypes.node.isRequired
};
var _jsxFileName$1 = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\context\\PremiumContext.jsx";
const PremiumContext = React.createContext();
const usePremium = () => {
  const context = React.useContext(PremiumContext);
  if (!context) {
    throw new Error("usePremium deve ser usado dentro de PremiumProvider");
  }
  return context;
};
const PremiumProvider = ({
  children
}) => {
  const [user, setUser] = React.useState({
    id: "demo_user",
    name: "Usuário Demo",
    email: "<EMAIL>",
    isPremium: false,
    // Por padrão, usuário não é premium
    isLoggedIn: false,
    // Por padrão, usuário não está logado
    subscription: "free",
    permissions: []
  });
  const [premiumFeatures] = React.useState({
    dashboard: true,
    // Dashboard do sistema
    analytics: true,
    // Métricas e análises
    backupFull: true,
    // Backup completo
    performance: true,
    // Análise de performance
    exportData: true,
    // Exportação de dados
    advancedReports: true,
    // Relatórios avançados
    multiUser: true,
    // Múltiplos usuários
    cloudSync: true
    // Sincronização na nuvem
  });
  const login = async (email, password, isPremium2 = false) => {
    try {
      const userData = {
        id: Date.now(),
        name: email.split("@")[0],
        email,
        isPremium: isPremium2,
        isLoggedIn: true,
        subscription: isPremium2 ? "premium" : "free",
        permissions: isPremium2 ? Object.keys(premiumFeatures) : []
      };
      setUser(userData);
      localStorage.setItem("betina_user", JSON.stringify(userData));
      return {
        success: true,
        user: userData
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  };
  const logout = () => {
    setUser({
      id: null,
      name: "",
      email: "",
      isPremium: false,
      isLoggedIn: false,
      subscription: null,
      permissions: []
    });
    localStorage.removeItem("betina_user");
  };
  const hasAccess = (feature) => {
    if (!premiumFeatures[feature]) return true;
    return user.isPremium && user.permissions.includes(feature);
  };
  const isLoggedIn = () => user.isLoggedIn;
  const isPremium = () => user.isPremium;
  const canAccessDashboard = () => {
    return user.isPremium && user.permissions.includes("dashboard");
  };
  const canAccessAIReports = () => {
    return user.isPremium && user.permissions.includes("ai_reports");
  };
  const canAccessAdvancedMetrics = () => {
    return user.isPremium && user.permissions.includes("advanced_metrics");
  };
  React.useEffect(() => {
    const savedUser = localStorage.getItem("betina_user");
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser);
        setUser(userData);
      } catch (error) {
        console.error("Erro ao carregar dados do usuário:", error);
        localStorage.removeItem("betina_user");
      }
    }
  }, []);
  const value = {
    user,
    login,
    logout,
    hasAccess,
    isLoggedIn,
    isPremium,
    canAccessDashboard,
    canAccessAIReports,
    canAccessAdvancedMetrics,
    premiumFeatures
  };
  return /* @__PURE__ */ React.createElement(PremiumContext.Provider, { value, __self: void 0, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 130,
    columnNumber: 5
  } }, children);
};
var _jsxFileName = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\context\\AdminContext.jsx";
const AdminContext = React.createContext();
const useAdmin = () => {
  const context = React.useContext(AdminContext);
  if (!context) {
    throw new Error("useAdmin deve ser usado dentro de um AdminProvider");
  }
  return context;
};
const AdminProvider = ({
  children
}) => {
  const [isAdmin, setIsAdmin] = React.useState(false);
  const [adminSession, setAdminSession] = React.useState(null);
  React.useEffect(() => {
    const savedSession = localStorage.getItem("betina_admin_session");
    if (savedSession) {
      try {
        const session = JSON.parse(savedSession);
        const now = (/* @__PURE__ */ new Date()).getTime();
        const sessionTime = new Date(session.timestamp).getTime();
        const hoursDiff = (now - sessionTime) / (1e3 * 60 * 60);
        if (hoursDiff < 24) {
          setIsAdmin(true);
          setAdminSession(session);
        } else {
          localStorage.removeItem("betina_admin_session");
        }
      } catch (error) {
        console.error("Erro ao verificar sessão admin:", error);
        localStorage.removeItem("betina_admin_session");
      }
    }
  }, []);
  const loginAdmin = (credentials) => {
    if (credentials.username === "admin" && credentials.password === "betina2024admin") {
      const session = {
        user: "admin",
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        permissions: ["dashboard_integrated", "system_admin", "user_management"]
      };
      setIsAdmin(true);
      setAdminSession(session);
      localStorage.setItem("betina_admin_session", JSON.stringify(session));
      return {
        success: true,
        message: "Login administrativo realizado com sucesso!"
      };
    }
    return {
      success: false,
      message: "Credenciais administrativas inválidas"
    };
  };
  const logoutAdmin = () => {
    setIsAdmin(false);
    setAdminSession(null);
    localStorage.removeItem("betina_admin_session");
  };
  const hasPermission = (permission) => {
    if (!isAdmin || !adminSession) return false;
    return adminSession.permissions && adminSession.permissions.includes(permission);
  };
  const canAccessIntegratedDashboard = () => {
    return hasPermission("dashboard_integrated");
  };
  const value = {
    isAdmin,
    adminSession,
    loginAdmin,
    logoutAdmin,
    hasPermission,
    canAccessIntegratedDashboard
  };
  return /* @__PURE__ */ React.createElement(AdminContext.Provider, { value, __self: void 0, __source: {
    fileName: _jsxFileName,
    lineNumber: 93,
    columnNumber: 5
  } }, children);
};
AdminProvider.propTypes = {
  children: PropTypes.node.isRequired
};
export {
  AdminProvider as A,
  DatabaseProvider as D,
  PremiumProvider as P,
  SystemContext as S,
  useAdmin as a,
  useAccessibilityContext as b,
  SystemProvider as c,
  AccessibilityProvider as d,
  usePremium as u
};
//# sourceMappingURL=context-D6Rxw-Zf.js.map
