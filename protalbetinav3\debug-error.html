<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Debug Error - Portal Betina V3</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 20px;
            background: #1a1a1a;
            color: #00ff00;
        }
        .error { color: #ff4444; }
        .success { color: #44ff44; }
        .warning { color: #ffaa44; }
        .info { color: #4444ff; }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
        }
        .log {
            background: #000;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #333;
        }
        button {
            background: #333;
            color: #fff;
            border: 1px solid #555;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Error - Portal Betina V3</h1>
        <p>Ferramenta para identificar o erro "Cannot access 'e' before initialization"</p>
        
        <div>
            <button onclick="testFrontendLoad()">🌐 Testar Carregamento Frontend</button>
            <button onclick="testSpecificModules()">📦 Testar Módulos Específicos</button>
            <button onclick="testColorMatch()">🎨 Testar ColorMatch</button>
            <button onclick="clearLog()">🧹 Limpar Log</button>
        </div>
        
        <div id="log" class="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        async function testFrontendLoad() {
            log('🔍 Iniciando teste de carregamento do frontend...', 'info');
            
            try {
                // Teste 1: Verificar se o frontend responde
                log('📡 Testando conectividade com http://localhost:5173...', 'info');
                
                const response = await fetch('http://localhost:5173', {
                    method: 'GET',
                    mode: 'no-cors' // Para evitar problemas de CORS
                });
                
                log('✅ Frontend respondeu (modo no-cors)', 'success');
                
                // Teste 2: Tentar carregar em iframe
                log('🖼️ Tentando carregar em iframe...', 'info');
                
                const iframe = document.createElement('iframe');
                iframe.src = 'http://localhost:5173';
                iframe.style.width = '1px';
                iframe.style.height = '1px';
                iframe.style.position = 'absolute';
                iframe.style.left = '-9999px';
                
                iframe.onload = () => {
                    log('✅ Iframe carregou sem erros críticos', 'success');
                    setTimeout(() => document.body.removeChild(iframe), 5000);
                };
                
                iframe.onerror = (error) => {
                    log('❌ Erro ao carregar iframe: ' + error, 'error');
                    document.body.removeChild(iframe);
                };
                
                document.body.appendChild(iframe);
                
                // Teste 3: Verificar console errors
                log('👂 Monitorando erros do console...', 'info');
                
                const originalError = console.error;
                console.error = function(...args) {
                    log('🚨 ERRO DETECTADO: ' + args.join(' '), 'error');
                    originalError.apply(console, args);
                };
                
                setTimeout(() => {
                    console.error = originalError;
                    log('⏰ Monitoramento de erros finalizado', 'info');
                }, 10000);
                
            } catch (error) {
                log('❌ Erro no teste: ' + error.message, 'error');
            }
        }

        async function testSpecificModules() {
            log('🔍 Testando módulos específicos que podem causar o erro...', 'info');
            
            const problematicModules = [
                'StatisticalCalculations',
                'VisualProcessingUtils', 
                'BaseCollector',
                'ColorMatchCollectorsHub',
                'DatabaseIntegrator'
            ];
            
            for (const module of problematicModules) {
                try {
                    log(`📦 Testando módulo: ${module}...`, 'info');
                    
                    // Simular importação do módulo
                    const testScript = document.createElement('script');
                    testScript.type = 'module';
                    testScript.textContent = `
                        try {
                            console.log('Testando ${module}...');
                            // Aqui tentaríamos importar o módulo real
                            console.log('${module} OK');
                        } catch (error) {
                            console.error('Erro em ${module}:', error);
                        }
                    `;
                    
                    document.head.appendChild(testScript);
                    
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    log(`✅ ${module} testado`, 'success');
                    
                } catch (error) {
                    log(`❌ Erro ao testar ${module}: ${error.message}`, 'error');
                }
            }
        }

        async function testColorMatch() {
            log('🎨 Testando especificamente o ColorMatch...', 'info');
            
            try {
                // Abrir ColorMatch em nova janela pequena
                const colorMatchWindow = window.open(
                    'http://localhost:5173', 
                    'colorMatchTest',
                    'width=400,height=300,scrollbars=yes'
                );
                
                if (colorMatchWindow) {
                    log('✅ Janela ColorMatch aberta', 'success');
                    
                    // Monitorar se a janela fecha inesperadamente (indicando erro)
                    const checkWindow = setInterval(() => {
                        if (colorMatchWindow.closed) {
                            log('⚠️ Janela ColorMatch foi fechada', 'warning');
                            clearInterval(checkWindow);
                        }
                    }, 1000);
                    
                    // Fechar automaticamente após 30 segundos
                    setTimeout(() => {
                        if (!colorMatchWindow.closed) {
                            colorMatchWindow.close();
                            log('🔄 Janela ColorMatch fechada automaticamente', 'info');
                        }
                        clearInterval(checkWindow);
                    }, 30000);
                    
                    // Tentar acessar o console da janela (se possível)
                    setTimeout(() => {
                        try {
                            if (!colorMatchWindow.closed) {
                                log('🔍 Tentando acessar console da janela...', 'info');
                                // Isso pode não funcionar devido a políticas de segurança
                                const errors = colorMatchWindow.console?.error || null;
                                if (errors) {
                                    log('📊 Console da janela acessível', 'success');
                                } else {
                                    log('⚠️ Console da janela não acessível (normal)', 'warning');
                                }
                            }
                        } catch (error) {
                            log('⚠️ Não foi possível acessar console da janela: ' + error.message, 'warning');
                        }
                    }, 3000);
                    
                } else {
                    log('❌ Não foi possível abrir janela ColorMatch', 'error');
                }
                
            } catch (error) {
                log('❌ Erro ao testar ColorMatch: ' + error.message, 'error');
            }
        }

        // Capturar erros globais
        window.addEventListener('error', (event) => {
            log(`🚨 ERRO GLOBAL: ${event.message} em ${event.filename}:${event.lineno}`, 'error');
        });

        window.addEventListener('unhandledrejection', (event) => {
            log(`🚨 PROMISE REJEITADA: ${event.reason}`, 'error');
        });

        // Auto-iniciar teste básico
        setTimeout(() => {
            log('🚀 Iniciando debug automático...', 'info');
            testFrontendLoad();
        }, 1000);
    </script>
</body>
</html>
