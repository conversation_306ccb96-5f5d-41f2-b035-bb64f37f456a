{"version": 3, "file": "context-Ch-5FaFa.js", "sources": ["../../src/components/context/SystemContext.jsx", "../../src/components/context/DatabaseProvider.jsx", "../../src/components/context/AccessibilityContext.jsx", "../../src/context/PremiumContext.jsx", "../../src/context/AdminContext.jsx"], "sourcesContent": ["/**\r\n * @file SystemContext.jsx\r\n * @description Provedor de contexto para acesso ao sistema integrado em toda a aplicação\r\n * @version 3.1.0\r\n */\r\n\r\nimport React, { createContext, useContext, useMemo } from 'react';\r\nimport PropTypes from 'prop-types';\r\n\r\n// Criar o contexto com valor inicial null\r\nexport const SystemContext = createContext(null);\r\n\r\n/**\r\n * Hook para acessar o sistema integrado\r\n * @returns {Object} Sistema integrado completo com métodos utilitários\r\n * @throws {Error} Se usado fora de um SystemProvider\r\n */\r\nexport function useSystem() {\r\n  const context = useContext(SystemContext);\r\n  if (!context) {\r\n    throw new Error('useSystem deve ser usado dentro de um SystemProvider');\r\n  }\r\n  return context;\r\n}\r\n\r\n// Alias para compatibilidade\r\nexport const useSystemContext = useSystem;\r\n\r\n/**\r\n * Provedor que disponibiliza o sistema integrado para a árvore de componentes\r\n * @param {Object} props - Propriedades do componente\r\n * @param {Object} props.system - Sistema integrado inicializado\r\n * @param {React.ReactNode} props.children - Componentes filhos\r\n * @returns {JSX.Element}\r\n */\r\nexport function SystemProvider({ system, children }) {\r\n  // Validar sistema na inicialização\r\n  if (!system || typeof system !== 'object') {\r\n    throw new Error('SystemProvider requer um objeto system válido');\r\n  }\r\n\r\n  // Memoizar o valor do contexto para evitar renderizações desnecessárias\r\n  const systemValue = useMemo(() => ({\r\n    system,\r\n    // Métodos utilitários com binding seguro\r\n    healthCheck: (...args) => system?.healthCheck?.(...args),\r\n    getStatistics: (...args) => system?.getStatistics?.(...args),\r\n    dispatchEvent: (...args) => system?.dispatchEvent?.(...args),\r\n    trackUserInteraction: (...args) => system?.trackUserInteraction?.(...args),\r\n    \r\n    // Informações de status do sistema com fallback seguro\r\n    getStatus: () => ({\r\n      database: system?.databaseInstance?.getStatus?.() ?? { status: 'unavailable' },\r\n      resilience: system?.resilience?.getCircuitBreakersStatus?.() ?? { status: 'unavailable' },\r\n      timestamp: new Date().toISOString(),\r\n      version: '3.1.0'\r\n    })\r\n  }), [system]);\r\n\r\n  return (\r\n    <SystemContext.Provider value={systemValue}>\r\n      {children}\r\n    </SystemContext.Provider>\r\n  );\r\n}\r\n\r\nSystemProvider.propTypes = {\r\n  system: PropTypes.shape({\r\n    healthCheck: PropTypes.func,\r\n    getStatistics: PropTypes.func,\r\n    dispatchEvent: PropTypes.func,\r\n    trackUserInteraction: PropTypes.func,\r\n    databaseInstance: PropTypes.shape({\r\n      getStatus: PropTypes.func\r\n    }),\r\n    resilience: PropTypes.shape({\r\n      getCircuitBreakersStatus: PropTypes.func\r\n    })\r\n  }).isRequired,\r\n  children: PropTypes.node.isRequired\r\n};\r\n\r\n// Exportação padrão\r\nexport default SystemProvider;", "/**\r\n * @file DatabaseProvider.jsx\r\n * @description Provedor de contexto para acesso ao DatabaseIntegrator em toda a aplicação\r\n * @version 3.0.0\r\n */\r\n\r\nimport React from 'react';\r\nimport PropTypes from 'prop-types';\r\nimport { useSystem } from './SystemContext';\r\n\r\n// Criar o contexto\r\nexport const DatabaseContext = React.createContext(null);\r\n\r\n/**\r\n * Hook para acessar o DatabaseIntegrator em qualquer componente\r\n * @returns {Object} DatabaseIntegrator instance\r\n */\r\nexport function useDatabase() {\r\n  const context = React.useContext(DatabaseContext);\r\n  if (!context) {\r\n    throw new Error('useDatabase deve ser usado dentro de um DatabaseProvider');\r\n  }\r\n  return context;\r\n}\r\n\r\n/**\r\n * Alias para useDatabase - mantém compatibilidade\r\n * @returns {Object} DatabaseIntegrator instance \r\n */\r\nexport const useDatabaseContext = useDatabase;\r\n\r\n/**\r\n * Provedor que disponibiliza o DatabaseIntegrator para a árvore de componentes\r\n * @param {Object} props - Propriedades do componente\r\n * @returns {JSX.Element}\r\n */\r\nexport function DatabaseProvider({ children }) {\r\n  // Obter o database diretamente do sistema integrado\r\n  const { system } = useSystem();\r\n  const databaseInstance = system.databaseInstance;\r\n  // O valor memoizado evita renderizações desnecessárias\r\n  const databaseValue = React.useMemo(() => {\r\n    // Verificar se os métodos existem antes de fazer bind\r\n    const safeBind = (method) => method ? method.bind(databaseInstance) : () => console.warn('Method not available');\r\n    \r\n    return {\r\n      // Expõe apenas o que componentes precisam acessar\r\n      db: databaseInstance,\r\n      databaseService: databaseInstance?.manager || databaseInstance,\r\n      saveGameMetrics: safeBind(databaseInstance.saveGameMetrics),\r\n      getUserData: safeBind(databaseInstance.getUserData),\r\n      saveUserData: safeBind(databaseInstance.saveUserData),\r\n      getStatus: safeBind(databaseInstance.getStatus),\r\n      \r\n      // Acesso ao sistema de resiliência para casos especiais\r\n      resilience: system.resilience\r\n    };\r\n  }, [databaseInstance, system.resilience]);\r\n\r\n  return (\r\n    <DatabaseContext.Provider value={databaseValue}>\r\n      {children}\r\n    </DatabaseContext.Provider>\r\n  );\r\n}\r\n\r\nDatabaseProvider.propTypes = {\r\n  children: PropTypes.node.isRequired\r\n};\r\n\r\nexport default DatabaseProvider;\r\n", "/**\r\n * @file AccessibilityContext.jsx\r\n * @description Contexto para gerenciar configurações de acessibilidade em toda a aplicação\r\n * @version 3.0.0\r\n */\r\n\r\nimport React from 'react'\r\nimport PropTypes from 'prop-types'\r\nimport useAccessibility from '../../hooks/useAccessibility'\r\n\r\n// Criando o contexto\r\nconst AccessibilityContext = React.createContext({\r\n  settings: {\r\n    textToSpeech: true,\r\n    highContrast: false,\r\n    reducedMotion: false,\r\n    colorScheme: 'default',\r\n    dyslexiaFriendly: false,\r\n    fontSize: 'medium',\r\n    soundEnabled: true,\r\n    autoRead: false\r\n  },\r\n  updateSettings: () => {},\r\n  applyPreset: () => {}\r\n})\r\n\r\n/**\r\n * Provider para o contexto de acessibilidade\r\n */\r\nexport function AccessibilityProvider({ children }) {\r\n  const accessibilityManager = useAccessibility()\r\n\r\n  return (\r\n    <AccessibilityContext.Provider value={accessibilityManager}>\r\n      {children}\r\n    </AccessibilityContext.Provider>\r\n  )\r\n}\r\n\r\n/**\r\n * Hook para usar o contexto de acessibilidade\r\n */\r\nexport function useAccessibilityContext() {\r\n  return React.useContext(AccessibilityContext)\r\n}\r\n\r\nAccessibilityProvider.propTypes = {\r\n  children: PropTypes.node.isRequired\r\n}\r\n\r\nexport default AccessibilityContext\r\n", "/**\r\n * @file PremiumContext.jsx\r\n * @description Contexto para controle de acesso premium do Portal Betina V3\r\n * @version 3.0.0\r\n */\r\n\r\nimport React from 'react';\r\n\r\nconst PremiumContext = React.createContext();\r\n\r\n// Hook para usar o contexto premium\r\nexport const usePremium = () => {\r\n  const context = React.useContext(PremiumContext);\r\n  if (!context) {\r\n    throw new Error('usePremium deve ser usado dentro de PremiumProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\nexport const PremiumProvider = ({ children }) => {  const [user, setUser] = React.useState({\r\n    id: 'demo_user',\r\n    name: '<PERSON>u<PERSON><PERSON> De<PERSON>',\r\n    email: '<EMAIL>',\r\n    isPremium: false, // Por padrão, usuário não é premium\r\n    isLoggedIn: false, // Por padrão, usuário não está logado\r\n    subscription: 'free',\r\n    permissions: []\r\n  });\r\n\r\n  const [premiumFeatures] = React.useState({\r\n    dashboard: true,           // Dashboard do sistema\r\n    analytics: true,          // Métricas e análises\r\n    backupFull: true,         // Backup completo\r\n    performance: true,        // Análise de performance\r\n    exportData: true,         // Exportação de dados\r\n    advancedReports: true,    // Relatórios avançados\r\n    multiUser: true,          // Múltiplos usuários\r\n    cloudSync: true           // Sincronização na nuvem\r\n  });\r\n\r\n  // Simular login (em produção, viria de uma API)\r\n  const login = async (email, password, isPremium = false) => {\r\n    try {\r\n      // Simulação de autenticação\r\n      const userData = {\r\n        id: Date.now(),\r\n        name: email.split('@')[0],\r\n        email,\r\n        isPremium,\r\n        isLoggedIn: true,\r\n        subscription: isPremium ? 'premium' : 'free',\r\n        permissions: isPremium ? Object.keys(premiumFeatures) : []\r\n      };\r\n\r\n      setUser(userData);\r\n      localStorage.setItem('betina_user', JSON.stringify(userData));\r\n      return { success: true, user: userData };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  };\r\n\r\n  // Logout\r\n  const logout = () => {\r\n    setUser({\r\n      id: null,\r\n      name: '',\r\n      email: '',\r\n      isPremium: false,\r\n      isLoggedIn: false,\r\n      subscription: null,\r\n      permissions: []\r\n    });\r\n    localStorage.removeItem('betina_user');\r\n  };\r\n\r\n  // Verificar se o usuário tem acesso a uma funcionalidade premium\r\n  const hasAccess = (feature) => {\r\n    if (!premiumFeatures[feature]) return true; // Feature não é premium\r\n    return user.isPremium && user.permissions.includes(feature);\r\n  };\r\n\r\n  // Verificar se o usuário está logado\r\n  const isLoggedIn = () => user.isLoggedIn;\r\n  // Verificar se o usuário é premium\r\n  const isPremium = () => user.isPremium;\r\n\r\n  // Verificar se pode acessar dashboard\r\n  const canAccessDashboard = () => {\r\n    return user.isPremium && user.permissions.includes('dashboard');\r\n  };\r\n\r\n  // Verificar se pode acessar relatórios IA\r\n  const canAccessAIReports = () => {\r\n    return user.isPremium && user.permissions.includes('ai_reports');\r\n  };\r\n\r\n  // Verificar se pode acessar métricas avançadas\r\n  const canAccessAdvancedMetrics = () => {\r\n    return user.isPremium && user.permissions.includes('advanced_metrics');\r\n  };\r\n\r\n  // Carregar dados do usuário do localStorage na inicialização\r\n  React.useEffect(() => {\r\n    const savedUser = localStorage.getItem('betina_user');\r\n    if (savedUser) {\r\n      try {\r\n        const userData = JSON.parse(savedUser);\r\n        setUser(userData);\r\n      } catch (error) {\r\n        console.error('Erro ao carregar dados do usuário:', error);\r\n        localStorage.removeItem('betina_user');\r\n      }\r\n    }\r\n  }, []);\r\n  const value = {\r\n    user,\r\n    login,\r\n    logout,\r\n    hasAccess,\r\n    isLoggedIn,\r\n    isPremium,\r\n    canAccessDashboard,\r\n    canAccessAIReports,\r\n    canAccessAdvancedMetrics,\r\n    premiumFeatures\r\n  };\r\n\r\n  return (\r\n    <PremiumContext.Provider value={value}>\r\n      {children}\r\n    </PremiumContext.Provider>\r\n  );\r\n};\r\n\r\nexport default PremiumContext;\r\n", "/**\r\n * @file AdminContext.jsx\r\n * @description Contexto para gerenciar permissões administrativas\r\n * @version 3.0.0\r\n */\r\n\r\nimport React from 'react'\r\nimport PropTypes from 'prop-types'\r\n\r\nconst AdminContext = React.createContext()\r\n\r\nexport const useAdmin = () => {\r\n  const context = React.useContext(AdminContext)\r\n  if (!context) {\r\n    throw new Error('useAdmin deve ser usado dentro de um AdminProvider')\r\n  }\r\n  return context\r\n}\r\n\r\nexport const AdminProvider = ({ children }) => {\r\n  const [isAdmin, setIsAdmin] = React.useState(false)\r\n  const [adminSession, setAdminSession] = React.useState(null)\r\n\r\n  // Verificar se há uma sessão admin ativa ao carregar\r\n  React.useEffect(() => {\r\n    const savedSession = localStorage.getItem('betina_admin_session')\r\n    if (savedSession) {\r\n      try {\r\n        const session = JSON.parse(savedSession)\r\n        // Verificar se a sessão não expirou (24 horas)\r\n        const now = new Date().getTime()\r\n        const sessionTime = new Date(session.timestamp).getTime()\r\n        const hoursDiff = (now - sessionTime) / (1000 * 60 * 60)\r\n        \r\n        if (hoursDiff < 24) {\r\n          setIsAdmin(true)\r\n          setAdminSession(session)\r\n        } else {\r\n          // Sessão expirada, remover\r\n          localStorage.removeItem('betina_admin_session')\r\n        }\r\n      } catch (error) {\r\n        console.error('Erro ao verificar sessão admin:', error)\r\n        localStorage.removeItem('betina_admin_session')\r\n      }\r\n    }\r\n  }, [])\r\n\r\n  const loginAdmin = (credentials) => {\r\n    // Verificar credenciais (em produção, isso seria uma API)\r\n    if (credentials.username === 'admin' && credentials.password === 'betina2024admin') {\r\n      const session = {\r\n        user: 'admin',\r\n        timestamp: new Date().toISOString(),\r\n        permissions: ['dashboard_integrated', 'system_admin', 'user_management']\r\n      }\r\n      \r\n      setIsAdmin(true)\r\n      setAdminSession(session)\r\n      localStorage.setItem('betina_admin_session', JSON.stringify(session))\r\n      \r\n      return { success: true, message: 'Login administrativo realizado com sucesso!' }\r\n    }\r\n    \r\n    return { success: false, message: 'Credenciais administrativas inválidas' }\r\n  }\r\n\r\n  const logoutAdmin = () => {\r\n    setIsAdmin(false)\r\n    setAdminSession(null)\r\n    localStorage.removeItem('betina_admin_session')\r\n  }\r\n\r\n  const hasPermission = (permission) => {\r\n    if (!isAdmin || !adminSession) return false\r\n    return adminSession.permissions && adminSession.permissions.includes(permission)\r\n  }\r\n\r\n  const canAccessIntegratedDashboard = () => {\r\n    return hasPermission('dashboard_integrated')\r\n  }\r\n\r\n  const value = {\r\n    isAdmin,\r\n    adminSession,\r\n    loginAdmin,\r\n    logoutAdmin,\r\n    hasPermission,\r\n    canAccessIntegratedDashboard\r\n  }\r\n\r\n  return (\r\n    <AdminContext.Provider value={value}>\r\n      {children}\r\n    </AdminContext.Provider>\r\n  )\r\n}\r\n\r\nAdminProvider.propTypes = {\r\n  children: PropTypes.node.isRequired\r\n}\r\n"], "names": ["SystemContext", "createContext", "useSystem", "context", "useContext", "Error", "SystemProvider", "system", "children", "systemValue", "useMemo", "healthCheck", "args", "getStatistics", "dispatchEvent", "trackUserInteraction", "getStatus", "database", "databaseInstance", "status", "resilience", "getCircuitBreakersStatus", "timestamp", "Date", "toISOString", "version", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "propTypes", "PropTypes", "shape", "func", "isRequired", "node", "DatabaseContext", "React", "DatabaseProvider", "databaseValue", "safeBind", "method", "bind", "console", "warn", "db", "databaseService", "manager", "saveGameMetrics", "getUserData", "saveUserData", "AccessibilityContext", "settings", "textToSpeech", "highContrast", "reducedMotion", "colorScheme", "dyslexiaFriendly", "fontSize", "soundEnabled", "autoRead", "updateSettings", "applyPreset", "AccessibilityProvider", "accessibilityManager", "useAccessibility", "useAccessibilityContext", "PremiumContext", "usePremium", "PremiumProvider", "user", "setUser", "useState", "id", "name", "email", "isPremium", "isLoggedIn", "subscription", "permissions", "premiumFeatures", "dashboard", "analytics", "<PERSON><PERSON><PERSON>", "performance", "exportData", "advancedReports", "multiUser", "cloudSync", "login", "password", "userData", "now", "split", "Object", "keys", "localStorage", "setItem", "JSON", "stringify", "success", "error", "message", "logout", "removeItem", "hasAccess", "feature", "includes", "canAccessDashboard", "canAccessAIReports", "canAccessAdvancedMetrics", "useEffect", "savedUser", "getItem", "parse", "value", "AdminContext", "useAdmin", "Admin<PERSON><PERSON><PERSON>", "isAdmin", "setIsAdmin", "adminSession", "setAdminSession", "savedSession", "session", "getTime", "sessionTime", "hoursDiff", "loginAdmin", "credentials", "username", "logoutAdmin", "hasPermission", "permission", "canAccessIntegratedDashboard"], "mappings": ";;;;AAUaA,MAAAA,gBAAgBC,2BAAc,IAAI;AAOxC,SAASC,YAAY;AACpBC,QAAAA,UAAUC,wBAAWJ,aAAa;AACxC,MAAI,CAACG,SAAS;AACN,UAAA,IAAIE,MAAM,sDAAsD;AAAA,EAAA;AAEjEF,SAAAA;AACT;AAYO,SAASG,eAAe;AAAA,EAAEC;AAAAA,EAAQC;AAAS,GAAG;AAEnD,MAAI,CAACD,UAAU,OAAOA,WAAW,UAAU;AACnC,UAAA,IAAIF,MAAM,+CAA+C;AAAA,EAAA;AAI3DI,QAAAA,cAAcC,aAAAA,QAAQ,OAAO;AAAA,IACjCH;AAAAA;AAAAA,IAEAI,aAAaA,IAAIC,SAASL,QAAQI,cAAc,GAAGC,IAAI;AAAA,IACvDC,eAAeA,IAAID,SAASL,QAAQM,gBAAgB,GAAGD,IAAI;AAAA,IAC3DE,eAAeA,IAAIF,SAASL,QAAQO,gBAAgB,GAAGF,IAAI;AAAA,IAC3DG,sBAAsBA,IAAIH,SAASL,QAAQQ,uBAAuB,GAAGH,IAAI;AAAA;AAAA,IAGzEI,WAAWA,OAAO;AAAA,MAChBC,UAAUV,QAAQW,kBAAkBF,iBAAiB;AAAA,QAAEG,QAAQ;AAAA,MAAc;AAAA,MAC7EC,YAAYb,QAAQa,YAAYC,gCAAgC;AAAA,QAAEF,QAAQ;AAAA,MAAc;AAAA,MACxFG,YAAW,oBAAIC,KAAK,GAAEC,YAAY;AAAA,MAClCC,SAAS;AAAA,IACX;AAAA,EAAA,IACE,CAAClB,MAAM,CAAC;AAGV,SAAA,sBAAA,cAAC,cAAc,UAAd,EAAuB,OAAOE,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAiB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OACxCrB,QACH;AAEJ;AAEAF,eAAewB,YAAY;AAAA,EACzBvB,QAAQwB,UAAUC,MAAM;AAAA,IACtBrB,aAAaoB,UAAUE;AAAAA,IACvBpB,eAAekB,UAAUE;AAAAA,IACzBnB,eAAeiB,UAAUE;AAAAA,IACzBlB,sBAAsBgB,UAAUE;AAAAA,IAChCf,kBAAkBa,UAAUC,MAAM;AAAA,MAChChB,WAAWe,UAAUE;AAAAA,IAAAA,CACtB;AAAA,IACDb,YAAYW,UAAUC,MAAM;AAAA,MAC1BX,0BAA0BU,UAAUE;AAAAA,IACrC,CAAA;AAAA,EACF,CAAA,EAAEC;AAAAA,EACH1B,UAAUuB,UAAUI,KAAKD;AAC3B;;ACrEaE,MAAAA,kBAAkBC,MAAMpC,cAAc,IAAI;AAyBhD,SAASqC,iBAAiB;AAAA,EAAE9B;AAAS,GAAG;AAEvC,QAAA;AAAA,IAAED;AAAAA,MAAWL,UAAU;AAC7B,QAAMgB,mBAAmBX,OAAOW;AAE1BqB,QAAAA,gBAAgBF,MAAM3B,QAAQ,MAAM;AAElC8B,UAAAA,WAAYC,CAAWA,WAAAA,SAASA,OAAOC,KAAKxB,gBAAgB,IAAI,MAAMyB,QAAQC,KAAK,sBAAsB;AAExG,WAAA;AAAA;AAAA,MAELC,IAAI3B;AAAAA,MACJ4B,iBAAiB5B,kBAAkB6B,WAAW7B;AAAAA,MAC9C8B,iBAAiBR,SAAStB,iBAAiB8B,eAAe;AAAA,MAC1DC,aAAaT,SAAStB,iBAAiB+B,WAAW;AAAA,MAClDC,cAAcV,SAAStB,iBAAiBgC,YAAY;AAAA,MACpDlC,WAAWwB,SAAStB,iBAAiBF,SAAS;AAAA;AAAA,MAG9CI,YAAYb,OAAOa;AAAAA,IACrB;AAAA,EACC,GAAA,CAACF,kBAAkBX,OAAOa,UAAU,CAAC;AAGtC,SAAA,sBAAA,cAAC,gBAAgB,UAAhB,EAAyB,OAAOmB,eAAc,QAAA,MAAA,UAAA;AAAA,IAAAb,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OAC5CrB,QACH;AAEJ;AAEA8B,iBAAiBR,YAAY;AAAA,EAC3BtB,UAAUuB,UAAUI,KAAKD;AAC3B;;ACzDA,MAAMiB,uBAAuBd,MAAMpC,cAAc;AAAA,EAC/CmD,UAAU;AAAA,IACRC,cAAc;AAAA,IACdC,cAAc;AAAA,IACdC,eAAe;AAAA,IACfC,aAAa;AAAA,IACbC,kBAAkB;AAAA,IAClBC,UAAU;AAAA,IACVC,cAAc;AAAA,IACdC,UAAU;AAAA,EACZ;AAAA,EACAC,gBAAgBA,MAAM;AAAA,EAAC;AAAA,EACvBC,aAAaA,MAAM;AAAA,EAAA;AACrB,CAAC;AAKM,SAASC,sBAAsB;AAAA,EAAEvD;AAAS,GAAG;AAClD,QAAMwD,uBAAuBC,iBAAiB;AAG5C,SAAA,sBAAA,cAAC,qBAAqB,UAArB,EAA8B,OAAOD,sBAAqB,QAAA,MAAA,UAAA;AAAA,IAAAtC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OACxDrB,QACH;AAEJ;AAKO,SAAS0D,0BAA0B;AACjC7B,SAAAA,MAAMjC,WAAW+C,oBAAoB;AAC9C;AAEAY,sBAAsBjC,YAAY;AAAA,EAChCtB,UAAUuB,UAAUI,KAAKD;AAC3B;;ACxCA,MAAMiC,iBAAiB9B,MAAMpC,cAAc;AAGpC,MAAMmE,aAAaA,MAAM;AACxBjE,QAAAA,UAAUkC,MAAMjC,WAAW+D,cAAc;AAC/C,MAAI,CAAChE,SAAS;AACN,UAAA,IAAIE,MAAM,qDAAqD;AAAA,EAAA;AAEhEF,SAAAA;AACT;AAEO,MAAMkE,kBAAkBA,CAAC;AAAA,EAAE7D;AAAS,MAAM;AAAG,QAAM,CAAC8D,MAAMC,OAAO,IAAIlC,MAAMmC,SAAS;AAAA,IACvFC,IAAI;AAAA,IACJC,MAAM;AAAA,IACNC,OAAO;AAAA,IACPC,WAAW;AAAA;AAAA,IACXC,YAAY;AAAA;AAAA,IACZC,cAAc;AAAA,IACdC,aAAa,CAAA;AAAA,EAAA,CACd;AAED,QAAM,CAACC,eAAe,IAAI3C,MAAMmC,SAAS;AAAA,IACvCS,WAAW;AAAA;AAAA,IACXC,WAAW;AAAA;AAAA,IACXC,YAAY;AAAA;AAAA,IACZC,aAAa;AAAA;AAAA,IACbC,YAAY;AAAA;AAAA,IACZC,iBAAiB;AAAA;AAAA,IACjBC,WAAW;AAAA;AAAA,IACXC,WAAW;AAAA;AAAA,EAAA,CACZ;AAGD,QAAMC,QAAQ,OAAOd,OAAOe,UAAUd,aAAY,UAAU;AACtD,QAAA;AAEF,YAAMe,WAAW;AAAA,QACflB,IAAIlD,KAAKqE,IAAI;AAAA,QACblB,MAAMC,MAAMkB,MAAM,GAAG,EAAE,CAAC;AAAA,QACxBlB;AAAAA,QACAC,WAAAA;AAAAA,QACAC,YAAY;AAAA,QACZC,cAAcF,aAAY,YAAY;AAAA,QACtCG,aAAaH,aAAYkB,OAAOC,KAAKf,eAAe,IAAI,CAAA;AAAA,MAC1D;AAEAT,cAAQoB,QAAQ;AAChBK,mBAAaC,QAAQ,eAAeC,KAAKC,UAAUR,QAAQ,CAAC;AACrD,aAAA;AAAA,QAAES,SAAS;AAAA,QAAM9B,MAAMqB;AAAAA,MAAS;AAAA,aAChCU,OAAO;AACP,aAAA;AAAA,QAAED,SAAS;AAAA,QAAOC,OAAOA,MAAMC;AAAAA,MAAQ;AAAA,IAAA;AAAA,EAElD;AAGA,QAAMC,SAASA,MAAM;AACX,YAAA;AAAA,MACN9B,IAAI;AAAA,MACJC,MAAM;AAAA,MACNC,OAAO;AAAA,MACPC,WAAW;AAAA,MACXC,YAAY;AAAA,MACZC,cAAc;AAAA,MACdC,aAAa,CAAA;AAAA,IAAA,CACd;AACDiB,iBAAaQ,WAAW,aAAa;AAAA,EACvC;AAGA,QAAMC,YAAaC,CAAY,YAAA;AAC7B,QAAI,CAAC1B,gBAAgB0B,OAAO,EAAU,QAAA;AACtC,WAAOpC,KAAKM,aAAaN,KAAKS,YAAY4B,SAASD,OAAO;AAAA,EAC5D;AAGM7B,QAAAA,aAAaA,MAAMP,KAAKO;AAExBD,QAAAA,YAAYA,MAAMN,KAAKM;AAG7B,QAAMgC,qBAAqBA,MAAM;AAC/B,WAAOtC,KAAKM,aAAaN,KAAKS,YAAY4B,SAAS,WAAW;AAAA,EAChE;AAGA,QAAME,qBAAqBA,MAAM;AAC/B,WAAOvC,KAAKM,aAAaN,KAAKS,YAAY4B,SAAS,YAAY;AAAA,EACjE;AAGA,QAAMG,2BAA2BA,MAAM;AACrC,WAAOxC,KAAKM,aAAaN,KAAKS,YAAY4B,SAAS,kBAAkB;AAAA,EACvE;AAGAtE,QAAM0E,UAAU,MAAM;AACdC,UAAAA,YAAYhB,aAAaiB,QAAQ,aAAa;AACpD,QAAID,WAAW;AACT,UAAA;AACIrB,cAAAA,WAAWO,KAAKgB,MAAMF,SAAS;AACrCzC,gBAAQoB,QAAQ;AAAA,eACTU,OAAO;AACNA,gBAAAA,MAAM,sCAAsCA,KAAK;AACzDL,qBAAaQ,WAAW,aAAa;AAAA,MAAA;AAAA,IACvC;AAAA,EAEJ,GAAG,EAAE;AACL,QAAMW,QAAQ;AAAA,IACZ7C;AAAAA,IACAmB;AAAAA,IACAc;AAAAA,IACAE;AAAAA,IACA5B;AAAAA,IACAD;AAAAA,IACAgC;AAAAA,IACAC;AAAAA,IACAC;AAAAA,IACA9B;AAAAA,EACF;AAEA,6CACG,eAAe,UAAf,EAAwB,OAAa,QAAA,QAAA,UAAA;AAAA,IAAAtD,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OACnCrB,QACH;AAEJ;;AC5HA,MAAM4G,eAAe/E,MAAMpC,cAAc;AAElC,MAAMoH,WAAWA,MAAM;AACtBlH,QAAAA,UAAUkC,MAAMjC,WAAWgH,YAAY;AAC7C,MAAI,CAACjH,SAAS;AACN,UAAA,IAAIE,MAAM,oDAAoD;AAAA,EAAA;AAE/DF,SAAAA;AACT;AAEO,MAAMmH,gBAAgBA,CAAC;AAAA,EAAE9G;AAAS,MAAM;AAC7C,QAAM,CAAC+G,SAASC,UAAU,IAAInF,MAAMmC,SAAS,KAAK;AAClD,QAAM,CAACiD,cAAcC,eAAe,IAAIrF,MAAMmC,SAAS,IAAI;AAG3DnC,QAAM0E,UAAU,MAAM;AACdY,UAAAA,eAAe3B,aAAaiB,QAAQ,sBAAsB;AAChE,QAAIU,cAAc;AACZ,UAAA;AACIC,cAAAA,UAAU1B,KAAKgB,MAAMS,YAAY;AAEvC,cAAM/B,OAAM,oBAAIrE,KAAK,GAAEsG,QAAQ;AAC/B,cAAMC,cAAc,IAAIvG,KAAKqG,QAAQtG,SAAS,EAAEuG,QAAQ;AACxD,cAAME,aAAanC,MAAMkC,gBAAgB,MAAO,KAAK;AAErD,YAAIC,YAAY,IAAI;AAClBP,qBAAW,IAAI;AACfE,0BAAgBE,OAAO;AAAA,QAAA,OAClB;AAEL5B,uBAAaQ,WAAW,sBAAsB;AAAA,QAAA;AAAA,eAEzCH,OAAO;AACNA,gBAAAA,MAAM,mCAAmCA,KAAK;AACtDL,qBAAaQ,WAAW,sBAAsB;AAAA,MAAA;AAAA,IAChD;AAAA,EAEJ,GAAG,EAAE;AAEL,QAAMwB,aAAcC,CAAgB,gBAAA;AAElC,QAAIA,YAAYC,aAAa,WAAWD,YAAYvC,aAAa,mBAAmB;AAClF,YAAMkC,UAAU;AAAA,QACdtD,MAAM;AAAA,QACNhD,YAAW,oBAAIC,KAAK,GAAEC,YAAY;AAAA,QAClCuD,aAAa,CAAC,wBAAwB,gBAAgB,iBAAiB;AAAA,MACzE;AAEAyC,iBAAW,IAAI;AACfE,sBAAgBE,OAAO;AACvB5B,mBAAaC,QAAQ,wBAAwBC,KAAKC,UAAUyB,OAAO,CAAC;AAE7D,aAAA;AAAA,QAAExB,SAAS;AAAA,QAAME,SAAS;AAAA,MAA8C;AAAA,IAAA;AAG1E,WAAA;AAAA,MAAEF,SAAS;AAAA,MAAOE,SAAS;AAAA,IAAwC;AAAA,EAC5E;AAEA,QAAM6B,cAAcA,MAAM;AACxBX,eAAW,KAAK;AAChBE,oBAAgB,IAAI;AACpB1B,iBAAaQ,WAAW,sBAAsB;AAAA,EAChD;AAEA,QAAM4B,gBAAiBC,CAAe,eAAA;AACpC,QAAI,CAACd,WAAW,CAACE,aAAqB,QAAA;AACtC,WAAOA,aAAa1C,eAAe0C,aAAa1C,YAAY4B,SAAS0B,UAAU;AAAA,EACjF;AAEA,QAAMC,+BAA+BA,MAAM;AACzC,WAAOF,cAAc,sBAAsB;AAAA,EAC7C;AAEA,QAAMjB,QAAQ;AAAA,IACZI;AAAAA,IACAE;AAAAA,IACAO;AAAAA,IACAG;AAAAA,IACAC;AAAAA,IACAE;AAAAA,EACF;AAEA,6CACG,aAAa,UAAb,EAAsB,OAAa,QAAA,QAAA,UAAA;AAAA,IAAA5G,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,OACjCrB,QACH;AAEJ;AAEA8G,cAAcxF,YAAY;AAAA,EACxBtB,UAAUuB,UAAUI,KAAKD;AAC3B;"}