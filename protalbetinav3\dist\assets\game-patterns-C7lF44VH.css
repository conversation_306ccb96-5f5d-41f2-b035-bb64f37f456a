/**
 * @file PadroesVisuais.module.css
 * @description Estilos modulares para o Jogo de Padrões Visuais - Padrão Elegante Unificado
 * @version 3.1.0
 */

/* Variáveis CSS para consistência e reutilização */
:root {
  --card-background: rgba(255, 255, 255, 0.15);
  --card-border: 1px solid rgba(255, 255, 255, 0.25);
  --card-blur: blur(15px);
  --success-bg: rgba(76, 175, 80, 0.25);
  --success-border: rgba(76, 175, 80, 0.6);
  --error-bg: rgba(244, 67, 54, 0.25);
  --error-border: rgba(244, 67, 54, 0.6);
  --warning-bg: rgba(255, 193, 7, 0.25);
  --warning-border: rgba(255, 193, 7, 0.6);
  --primary-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
  --shadow-light: 0 8px 32px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 12px 40px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 16px 48px rgba(0, 0, 0, 0.2);
}

/* Container principal do PadroesVisuais */
._padroesVisuaisGame_19evq_51 {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Conteúdo do jogo */
._gameContent_19evq_73 {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Header do jogo */
._gameHeader_19evq_93 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1.5rem 4rem 1.5rem 1.5rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  position: relative;
  min-height: 80px;
  box-shadow: var(--shadow-medium);
  transition: all 0.3s ease;
}

._gameHeader_19evq_93:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-heavy);
}

._gameTitle_19evq_135 {
  font-size: 2rem;
  font-weight: 800;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

._activitySubtitle_19evq_161 {
  font-size: 0.8rem;
  opacity: 0.9;
  margin-top: 0.5rem;
  background: var(--card-background);
  padding: 0.4rem 1rem;
  border-radius: 15px;
  border: var(--card-border);
  backdrop-filter: var(--card-blur);
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* Botões TTS */
._headerTtsButton_19evq_189 {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
  color: white;
  z-index: 10;
  backdrop-filter: var(--card-blur);
  box-shadow: var(--shadow-light);
}

._headerTtsButton_19evq_189:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.1);
  box-shadow: var(--shadow-medium);
}

._headerTtsButton_19evq_189._ttsActive_19evq_243 {
  background: var(--success-bg);
  border: var(--success-border);
  animation: _pulse_19evq_1 2s infinite;
}

._headerTtsButton_19evq_189._speaking_19evq_255 {
  background: var(--warning-bg) !important;
  border: var(--warning-border) !important;
  animation: _speaking_19evq_255 1s infinite;
}

@keyframes _pulse_19evq_1 {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes _speaking_19evq_255 {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.8);
  }
}

._headerTtsButton_19evq_189._active_19evq_299 {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.5);
}

/* Estatísticas do jogo */
._gameStats_19evq_311 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

._statCard_19evq_325 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-light);
}

._statCard_19evq_325:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

._statCard_19evq_325::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #E91E63);
  opacity: 0.8;
}

._statValue_19evq_383 {
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  color: white;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  line-height: 1;
}

._statLabel_19evq_401 {
  font-size: 0.85rem;
  opacity: 0.9;
  color: white;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
}

/* ===== ELEGANTE SISTEMA DE CARDS ===== */

/* Área do jogo */
._gameArea_19evq_425 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
  min-height: 400px;
  /* DEBUG: Adicionar borda temporária para visualizar */
  border: 2px solid rgba(255, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.05);
}

/* Área da pergunta */
._questionArea_19evq_451 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  padding: 2.5rem;
  margin-bottom: 2rem;
  text-align: center;
  box-shadow: var(--shadow-medium);
  transition: all 0.3s ease;
  min-height: 200px;
  /* DEBUG: Adicionar borda temporária para visualizar */
  border: 3px solid rgba(0, 255, 0, 0.5) !important;
}

._questionArea_19evq_451:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-heavy);
}

._questionHeader_19evq_491 {
  margin-bottom: 2rem;
}

._questionTitle_19evq_499 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

._repeatButton_19evq_521 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  padding: 0.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

._repeatButton_19evq_521:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

/* Display dos objetos principais */
._objectsDisplay_19evq_555 {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  min-height: 120px;
}

._countingObject_19evq_575 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  min-width: 100px;
  min-height: 100px;
}

._countingObject_19evq_575::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  animation: _shimmer_19evq_1 3s ease-in-out infinite;
}

._countingObject_19evq_575:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.2);
}

/* Animações */
@keyframes _shimmer_19evq_1 {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

@keyframes _pulse_19evq_1 {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
}

@keyframes _bounceIn_19evq_1 {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); opacity: 1; }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); }
}

/* Cards de opções de resposta */
._answerOptions_19evq_691 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

._elegantCard_19evq_705 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

._elegantCard_19evq_705::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

._elegantCard_19evq_705:hover::before {
  animation: _shimmer_19evq_1 1.5s ease-in-out;
  opacity: 1;
}

._elegantCard_19evq_705:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

._elegantCard_19evq_705:active {
  transform: translateY(-4px) scale(0.98);
  transition: all 0.1s ease;
}

/* Estados especiais dos cards */
._elegantCard_19evq_705._selected_19evq_807 {
  background: var(--success-bg);
  border-color: var(--success-border);
  box-shadow: 0 8px 32px rgba(76, 175, 80, 0.3);
}

._elegantCard_19evq_705._correct_19evq_819 {
  background: var(--success-bg);
  border-color: var(--success-border);
  animation: _bounceIn_19evq_1 0.6s ease;
}

._elegantCard_19evq_705._incorrect_19evq_831 {
  background: var(--error-bg);
  border-color: var(--error-border);
  animation: _pulse_19evq_1 0.5s ease 2;
}

/* Compatibilidade com sistema antigo */
._answerButton_19evq_845 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

._answerButton_19evq_845::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

._answerButton_19evq_845:hover::before {
  animation: _shimmer_19evq_1 1.5s ease-in-out;
  opacity: 1;
}

._answerButton_19evq_845:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

._answerButton_19evq_845:active {
  transform: translateY(-4px) scale(0.98);
  transition: all 0.1s ease;
}

._optionNumber_19evq_947 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

/* Feedback visual */
._feedbackContainer_19evq_961 {
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 12px;
  text-align: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

._feedbackContainer_19evq_961._correct_19evq_819 {
  background: var(--success-bg);
  border: 1px solid var(--success-border);
  color: white;
  animation: _bounceIn_19evq_1 0.6s ease;
}

._feedbackContainer_19evq_961._incorrect_19evq_831 {
  background: var(--error-bg);
  border: 1px solid var(--error-border);
  color: white;
  animation: _pulse_19evq_1 0.5s ease 2;
}

/* Controles do jogo */
._gameControls_19evq_1009 {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-top: 2.5rem;
  flex-wrap: wrap;
  padding: 1rem;
}

._controlButton_19evq_1027 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 15px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-weight: 700;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  box-shadow: var(--shadow-light);
  min-width: 120px;
  justify-content: center;
  letter-spacing: 0.3px;
}

._controlButton_19evq_1027:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-3px);
  box-shadow: var(--shadow-heavy);
}

._controlButton_19evq_1027:active {
  transform: translateY(-1px);
  transition: all 0.1s ease;
}

/* TTS Indicators */
._ttsIndicator_19evq_1091 {
  font-size: 0.8rem;
  opacity: 0.7;
  margin-top: 0.25rem;
}

/* Responsividade Mobile-First */
@media (max-width: 768px) {
  ._padroesVisuaisGame_19evq_51 {
    padding: 0.5rem;
  }

  ._gameHeader_19evq_93 {
    padding: 0.75rem 2.5rem 0.75rem 0.75rem;
    min-height: 60px;
  }

  ._gameTitle_19evq_135 {
    font-size: 1.4rem;
  }

  ._activitySubtitle_19evq_161 {
    font-size: 0.65rem;
    padding: 0.2rem 0.5rem;
  }

  ._questionArea_19evq_451 {
    padding: 1.5rem;
    border-radius: 16px;
  }

  ._questionTitle_19evq_499 {
    font-size: 1.2rem;
  }

  ._objectsDisplay_19evq_555 {
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  ._countingObject_19evq_575 {
    padding: 1rem;
    font-size: 2rem;
    min-width: 80px;
    min-height: 80px;
  }

  ._answerOptions_19evq_691 {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
  }

  ._elegantCard_19evq_705, ._answerButton_19evq_845 {
    padding: 1rem;
    font-size: 1rem;
    min-height: 60px;
  }

  ._optionNumber_19evq_947 {
    font-size: 1.5rem;
  }

  ._gameControls_19evq_1009 {
    gap: 0.5rem;
  }

  ._controlButton_19evq_1027 {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  /* Responsividade para Padrões Visuais - Tablet */
  ._patternContainer_19evq_1235 {
    padding: 2rem;
    gap: 2rem;
  }

  ._patternSequence_19evq_1245 {
    gap: 1.5rem;
    padding: 2rem;
  }

  ._patternEmoji_19evq_1255 {
    font-size: 3rem;
  }

  ._feedbackIcon_19evq_1263 {
    font-size: 3rem;
  }

  ._feedbackTitle_19evq_1271 {
    font-size: 1.4rem;
  }

  /* Ícones de escolha maiores - Tablet */
  ._optionEmoji_19evq_1281 {
    font-size: 3.5rem;
  }

  ._answerOption_19evq_691 {
    min-width: 140px;
    min-height: 140px;
    padding: 2rem;
  }
}

@media (min-width: 1024px) {
  /* Responsividade para Padrões Visuais - Desktop */
  ._patternContainer_19evq_1235 {
    padding: 2.5rem;
    gap: 2.5rem;
  }

  ._patternSequence_19evq_1245 {
    gap: 2rem;
    padding: 2.5rem;
    flex-wrap: nowrap;
  }

  ._patternEmoji_19evq_1255 {
    font-size: 3.5rem;
  }

  ._feedbackIcon_19evq_1263 {
    font-size: 3.5rem;
  }

  ._feedbackTitle_19evq_1271 {
    font-size: 1.5rem;
  }

  ._roundProgress_19evq_1353 {
    font-size: 1rem;
  }

  /* Ícones de escolha maiores - Desktop */
  ._optionEmoji_19evq_1281 {
    font-size: 4rem;
  }

  ._answerOption_19evq_691 {
    min-width: 160px;
    min-height: 160px;
    padding: 2.5rem;
  }
}

@media (max-width: 480px) {
  ._gameStats_19evq_311 {
    grid-template-columns: repeat(2, 1fr);
  }

  ._answerOptions_19evq_691 {
    grid-template-columns: repeat(2, 1fr);
  }

  ._gameControls_19evq_1009 {
    flex-direction: column;
    align-items: center;
  }

  ._controlButton_19evq_1027 {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }
}

/* Estados de acessibilidade */
._padroesVisuaisGame_19evq_51._high-contrast_19evq_1429 {
  --card-background: rgba(0, 0, 0, 0.8);
  --card-border: 2px solid rgba(255, 255, 255, 0.8);
}

._padroesVisuaisGame_19evq_51._reduced-motion_19evq_1439 * {
  animation: none !important;
  transition: none !important;
}

/* Tamanhos de fonte personalizáveis */
._padroesVisuaisGame_19evq_51[data-font-size="small"] {
  font-size: 0.875rem;
}

._padroesVisuaisGame_19evq_51[data-font-size="large"] {
  font-size: 1.25rem;
}

/* Temas de cor */
._padroesVisuaisGame_19evq_51[data-theme="dark"] {
  --gradient-bg: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

._padroesVisuaisGame_19evq_51[data-theme="light"] {
  --gradient-bg: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: #2d3436;
}

/* ========================================
   🎯 LAYOUT MODERNO MELHORADO - PADRÕES VISUAIS
   ======================================== */

/* Container principal moderno */
._modernGameContainer_19evq_1497 {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  min-height: 100vh;
}

/* Cabeçalho da atividade */
._activityHeader_19evq_1519 {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: var(--shadow-medium);
}

._activityIcon_19evq_1543 {
  font-size: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

._activityInfo_19evq_1567 {
  flex: 1;
}

._activityTitle_19evq_1575 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

._activityDescription_19evq_1591 {
  font-size: 1.1rem;
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

._progressIndicator_19evq_1605 {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

._roundCounter_19evq_1619 {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
}

._progressBar_19evq_1631 {
  width: 120px;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

._progressFill_19evq_1647 {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* Área de exibição do padrão */
._patternDisplayArea_19evq_1663 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--shadow-medium);
}

._patternInstructions_19evq_1681 {
  text-align: center;
  margin-bottom: 2rem;
}

._patternInstructions_19evq_1681 h3 {
  font-size: 1.4rem;
  margin: 0;
  color: white;
  font-weight: 600;
}

/* Grid do padrão */
._patternGrid_19evq_1707 {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
  margin: 0 auto;
  max-width: 800px;
}

._patternCell_19evq_1727 {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

._patternElement_19evq_1743 {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

._missingCell_19evq_1771 ._patternElement_19evq_1743 {
  background: rgba(255, 193, 7, 0.2);
  border: 2px solid rgba(255, 193, 7, 0.6);
  animation: _pulse_19evq_1 2s infinite;
}

._questionMark_19evq_1783 {
  font-size: 2.5rem;
  color: rgba(255, 193, 7, 0.9);
  font-weight: bold;
}

._cellNumber_19evq_1795 {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 600;
  background: rgba(0, 0, 0, 0.3);
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

@keyframes _pulse_19evq_1 {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.05); opacity: 1; }
}

/* Seção de opções */
._optionsSection_19evq_1829 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--shadow-medium);
}

._optionsTitle_19evq_1847 {
  text-align: center;
  font-size: 1.3rem;
  margin: 0 0 1.5rem 0;
  color: white;
  font-weight: 600;
}

._optionsContainer_19evq_1863 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

._optionButton_19evq_1879 {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  min-height: 120px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

._optionButton_19evq_1879:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

._optionButton_19evq_1879._selected_19evq_807 {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.8);
  transform: scale(1.05);
}

._optionButton_19evq_1879:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

._optionContent_19evq_1945 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

._optionEmoji_19evq_1281 {
  font-size: 2.5rem;
  line-height: 1;
}

._optionLabel_19evq_1969 {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
  text-align: center;
}

/* Container de resultado/feedback */
._resultContainer_19evq_1985 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--shadow-medium);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  animation: _slideInUp_19evq_1 0.5s ease;
}

._resultContainer_19evq_1985._success_19evq_2011 {
  background: var(--success-bg);
  border-color: var(--success-border);
}

._resultContainer_19evq_1985._error_19evq_2021 {
  background: var(--error-bg);
  border-color: var(--error-border);
}

._resultIcon_19evq_2031 {
  font-size: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  flex-shrink: 0;
}

._resultContent_19evq_2055 {
  flex: 1;
}

._resultTitle_19evq_2063 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: white;
}

._resultMessage_19evq_2077 {
  font-size: 1.1rem;
  margin: 0 0 0.5rem 0;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

._correctAnswer_19evq_2091 {
  font-size: 1rem;
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  font-style: italic;
}

/* Loading container */
._loadingContainer_19evq_2107 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 3rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  box-shadow: var(--shadow-medium);
}

._loadingSpinner_19evq_2135 {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: _spin_19evq_1 1s linear infinite;
}

@keyframes _spin_19evq_1 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes _slideInUp_19evq_1 {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ========================================
   📱 RESPONSIVIDADE PARA LAYOUT MODERNO
   ======================================== */

/* Tablet */
@media (max-width: 768px) {
  ._modernGameContainer_19evq_1497 {
    padding: 1rem;
    gap: 1.5rem;
  }

  ._activityHeader_19evq_1519 {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  ._activityIcon_19evq_1543 {
    width: 60px;
    height: 60px;
    font-size: 2.5rem;
  }

  ._activityTitle_19evq_1575 {
    font-size: 1.5rem;
  }

  ._progressIndicator_19evq_1605 {
    align-items: center;
  }

  ._patternElement_19evq_1743 {
    width: 60px;
    height: 60px;
    font-size: 2.5rem;
  }

  ._optionsContainer_19evq_1863 {
    grid-template-columns: repeat(2, 1fr);
  }

  ._resultContainer_19evq_1985 {
    flex-direction: column;
    text-align: center;
  }
}

/* Mobile */
@media (max-width: 480px) {
  ._modernGameContainer_19evq_1497 {
    padding: 0.5rem;
    gap: 1rem;
  }

  ._activityHeader_19evq_1519,
  ._patternDisplayArea_19evq_1663,
  ._optionsSection_19evq_1829,
  ._resultContainer_19evq_1985 {
    padding: 1rem;
    border-radius: 15px;
  }

  ._activityIcon_19evq_1543 {
    width: 50px;
    height: 50px;
    font-size: 2rem;
  }

  ._activityTitle_19evq_1575 {
    font-size: 1.3rem;
  }

  ._activityDescription_19evq_1591 {
    font-size: 1rem;
  }

  ._patternGrid_19evq_1707 {
    gap: 0.5rem;
  }

  ._patternElement_19evq_1743 {
    width: 50px;
    height: 50px;
    font-size: 2rem;
  }

  ._optionsContainer_19evq_1863 {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  ._optionButton_19evq_1879 {
    min-height: 80px;
    padding: 0.5rem;
  }

  ._optionEmoji_19evq_1281 {
    font-size: 2rem;
  }

  ._resultIcon_19evq_2031 {
    width: 60px;
    height: 60px;
    font-size: 2.5rem;
  }

  ._resultTitle_19evq_2063 {
    font-size: 1.3rem;
  }

  ._resultMessage_19evq_2077 {
    font-size: 1rem;
  }
}

/* Variáveis CSS para consistência e reutilização */
:root {
  --card-background: rgba(255, 255, 255, 0.1);
  --card-border: 1px solid rgba(255, 255, 255, 0.2);
  --card-blur: blur(10px);
  --success-bg: rgba(76, 175, 80, 0.3);
  --success-border: rgba(76, 175, 80, 0.5);
  --error-bg: rgba(244, 67, 54, 0.3);
  --error-border: rgba(244, 67, 54, 0.5);
  --primary-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Container principal do PadroesVisuais */
._padroesVisuaisGame_19evq_51 {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Container principal - genérico para outros jogos */
._gameContainer_19evq_2465 {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Conteúdo do jogo */
._gameContent_19evq_73 {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Header do jogo */
._gameHeader_19evq_93 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 3rem 1rem 1rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  position: relative;
  min-height: 70px;
}

._gameTitle_19evq_135 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

._activitySubtitle_19evq_161 {
  font-size: 0.7rem;
  opacity: 0.8;
  margin-top: 0.25rem;
  background: var(--card-background);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: var(--card-border);
}

/* Estatísticas */
._gameStats_19evq_311 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

._statCard_19evq_325 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

._statCard_19evq_325::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #E91E63);
  opacity: 0.7;
}

._statValue_19evq_383 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: white;
}

._statLabel_19evq_401 {
  font-size: 0.8rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.8);
}

/* REMOVIDO - Duplicação da classe questionArea */

._questionTitle_19evq_499 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: white;
}

._objectsDisplay_19evq_555 {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
  margin: 2rem 0;
  min-height: 150px;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 2px dashed rgba(255, 255, 255, 0.3);
}

._countingObject_19evq_575 {
  font-size: 3rem;
  transition: all 0.3s ease;
  animation: _objectAppear_19evq_1 0.5s ease-out;
  cursor: default;
  user-select: none;
}

._countingObject_19evq_575:hover {
  transform: scale(1.1);
}

@keyframes _objectAppear_19evq_1 {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-10deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* Opções de resposta */
._answerOptions_19evq_691 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

._answerButton_19evq_845 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 1.5rem;
  color: white;
  font-size: 2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

._answerButton_19evq_845:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.25);
}

._answerButton_19evq_845._correct_19evq_819 {
  border-color: var(--success-border);
  background: var(--success-bg);
  animation: _correctPulse_19evq_1 0.6s ease-in-out;
}

._answerButton_19evq_845._incorrect_19evq_831 {
  border-color: var(--error-border);
  background: var(--error-bg);
  animation: _incorrectShake_19evq_1 0.6s ease-in-out;
}

._answerButton_19evq_845:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@keyframes _correctPulse_19evq_1 {
  0%, 100% { transform: translateY(-5px) scale(1.05); }
  50% { transform: translateY(-5px) scale(1.2); }
}

@keyframes _incorrectShake_19evq_1 {
  0%, 100% { transform: translateY(-5px) translateX(0); }
  25% { transform: translateY(-5px) translateX(-10px); }
  75% { transform: translateY(-5px) translateX(10px); }
}

/* Controles do jogo */
._gameControls_19evq_1009 {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

._controlButton_19evq_1027 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 600;
}

._controlButton_19evq_1027:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

._nextButton_19evq_2937 {
  background: var(--success-bg);
  border-color: var(--success-border);
}

._nextButton_19evq_2937:hover {
  background: rgba(76, 175, 80, 0.4);
}

/* Feedback */
._feedbackMessage_19evq_2957 {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 1.5rem 2.5rem;
  border-radius: 16px;
  font-size: 1.3rem;
  font-weight: 700;
  z-index: 1000;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: _messageSlide_19evq_1 3s ease-in-out;
}

._feedbackMessage_19evq_2957._success_19evq_2011 {
  background: var(--success-bg);
  color: white;
}

._feedbackMessage_19evq_2957._error_19evq_2021 {
  background: var(--error-bg);
  color: white;
}

@keyframes _messageSlide_19evq_1 {
  0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
  15% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  85% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}

/* Botões TTS */
._headerTtsButton_19evq_189 {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: white;
  z-index: 10;
}

._headerTtsButton_19evq_189:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

._headerTtsButton_19evq_189:active {
  transform: scale(0.95);
}

._ttsActive_19evq_243 {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
}

._ttsInactive_19evq_3089 {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
}

._repeatButton_19evq_521 {
  background: rgba(74, 144, 226, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 600;
  margin-left: 1rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

._repeatButton_19evq_521:hover {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.4);
}

._repeatButton_19evq_521:active {
  transform: scale(0.95);
}

._ttsIndicator_19evq_1091 {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(74, 144, 226, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 6px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  z-index: 5;
  pointer-events: none;
  transition: all 0.2s ease;
}

._answerButton_19evq_845:hover ._ttsIndicator_19evq_1091 {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.1);
}

/* Menu de atividades */
._activityMenu_19evq_3199 {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  justify-content: center;
  padding: 0.5rem;
}

._activityButton_19evq_3217 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 15px;
  padding: 0.8rem 1.2rem;
  color: white;
  cursor: pointer;
  font-size: 0.85rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.6rem;
  backdrop-filter: var(--card-blur);
  box-shadow: var(--shadow-light);
  min-width: 140px;
  justify-content: center;
}

._activityButton_19evq_3217:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

._activityButton_19evq_3217._active_19evq_299 {
  background: var(--success-bg) !important;
  border: 2px solid var(--success-border) !important;
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
  transform: translateY(-2px);
}

._activityIcon_19evq_1543 {
  font-size: 1.2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

._activityName_19evq_3291 {
  font-weight: 600;
  letter-spacing: 0.3px;
}

/* Classes específicas do ColorMatch */
._questionHeader_19evq_491 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  position: relative;
}

._optionNumber_19evq_947 {
  font-size: 1.2rem;
  font-weight: bold;
}

._colorDisplay_19evq_3329 {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
  border: 3px solid rgba(255,255,255,0.3);
  box-shadow: 0 4px 20px rgba(0,0,0,0.2);
  transition: all 0.3s ease;
}

._colorDisplay_19evq_3329:hover {
  transform: scale(1.05);
}

/* Atividade de Som */
._soundActivity_19evq_3373 {
  text-align: center;
}

._soundIndicator_19evq_3381 {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: _soundPulse_19evq_1 2s ease-in-out infinite;
}

@keyframes _soundPulse_19evq_1 {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.1); opacity: 1; }
}

._soundButton_19evq_3403 {
  background: rgba(74, 144, 226, 0.3);
  border: 2px solid rgba(74, 144, 226, 0.5);
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  margin: 1rem;
}

._soundButton_19evq_3403:hover {
  background: rgba(74, 144, 226, 0.5);
  transform: scale(1.05);
}

/* Atividade de Estimativa */
._estimationDisplay_19evq_3441 {
  position: relative;
}

._estimationObjects_19evq_3449 {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  opacity: 0.9;
}

._estimationObject_19evq_3449 {
  font-size: 1.5rem;
  transform: rotate(var(--rotation));
  transition: opacity 0.3s ease;
}

._estimationTip_19evq_3477 {
  text-align: center;
  margin-top: 1rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  background: rgba(255, 193, 7, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border-left: 4px solid #FFC107;
}

/* Atividade de Sequência */
._sequenceDisplay_19evq_3501 {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-wrap: wrap;
}

._sequenceNumber_19evq_3519 {
  background: var(--card-background);
  padding: 1rem;
  border-radius: 12px;
  min-width: 60px;
  text-align: center;
  color: white;
  font-weight: bold;
  transition: all 0.3s ease;
}

._sequenceNumber_19evq_3519:hover {
  transform: scale(1.05);
}

._sequenceArrow_19evq_3549 {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.5rem;
}

._sequenceMissing_19evq_3559 {
  background: rgba(255, 255, 80, 0.3) !important;
  border: 2px dashed rgba(255, 255, 255, 0.5);
  animation: _missingPulse_19evq_1 2s ease-in-out infinite;
}

@keyframes _missingPulse_19evq_1 {
  0%, 100% { box-shadow: 0 0 0 rgba(255, 255, 80, 0.3); }
  50% { box-shadow: 0 0 20px rgba(255, 255, 80, 0.6); }
}

/* Atividade de Comparação */
._comparisonDisplay_19evq_3583 {
  display: flex;
  gap: 3rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

._comparisonGroup_19evq_3599 {
  text-align: center;
  background: var(--card-background);
  padding: 1.5rem;
  border-radius: 16px;
  transition: all 0.3s ease;
}

._comparisonGroup_19evq_3599:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-5px);
}

._comparisonObjects_19evq_3625 {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  max-width: 150px;
  margin-bottom: 1rem;
}

._comparisonNumber_19evq_3643 {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  background: var(--card-background);
  padding: 0.5rem 1rem;
  border-radius: 8px;
}

/* Atividade de Padrões */
._patternDisplay_19evq_1663 {
  text-align: center;
}

._patternDescription_19evq_3671 {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1rem;
  background: rgba(156, 39, 176, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border-left: 4px solid #9C27B0;
}

._patternSequence_19evq_1245 {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-wrap: wrap;
}

._patternNumber_19evq_3709 {
  background: linear-gradient(135deg, rgba(156, 39, 176, 0.3), rgba(156, 39, 176, 0.1));
  padding: 1rem;
  border-radius: 12px;
  min-width: 60px;
  text-align: center;
  color: white;
  font-weight: bold;
  border: 1px solid rgba(156, 39, 176, 0.4);
  transition: all 0.3s ease;
}

._patternNumber_19evq_3709:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(156, 39, 176, 0.3);
}

/* ========================================
   RESPONSIVIDADE MOBILE FIRST - PADRÃO IMAGE ASSOCIATION EXATO
   ======================================== */

/* Tablet */
@media (max-width: 768px) {
  ._activityMenu_19evq_3199 {
    gap: 0.25rem;
  }

  ._activityButton_19evq_3217 {
    padding: 0.4rem 0.8rem;
    font-size: 0.7rem;
  }

  ._answerOptions_19evq_691 {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 0.75rem;
  }

  ._answerButton_19evq_845 {
    padding: 1rem;
    font-size: 0.8rem;
    min-height: 80px;
  }

  ._objectsDisplay_19evq_555 {
    padding: 1rem;
  }

  ._comparisonDisplay_19evq_3583 {
    gap: 1.5rem;
  }

  ._sequenceDisplay_19evq_3501,
  ._patternSequence_19evq_1245 {
    gap: 0.5rem;
    font-size: 1.5rem;
  }

  ._sequenceNumber_19evq_3519,
  ._patternNumber_19evq_3709 {
    padding: 0.75rem;
    min-width: 50px;
  }

  ._gameStats_19evq_311 {
    grid-template-columns: repeat(2, 1fr);
  }

  ._questionTitle_19evq_499 {
    font-size: 1.5rem;
  }
}

/* Mobile */
@media (max-width: 480px) {
  ._activityMenu_19evq_3199 {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }

  ._activityButton_19evq_3217 {
    width: 100%;
    max-width: 250px;
    justify-content: center;
    padding: 0.75rem;
  }

  ._answerOptions_19evq_691 {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    max-width: 100%;
  }

  ._answerButton_19evq_845 {
    padding: 0.75rem;
    font-size: 0.7rem;
    min-height: 70px;
  }

  ._objectsDisplay_19evq_555 {
    padding: 0.5rem;
  }

  ._comparisonDisplay_19evq_3583 {
    flex-direction: column;
    gap: 1rem;
  }

  ._sequenceDisplay_19evq_3501,
  ._patternSequence_19evq_1245 {
    flex-direction: column;
    gap: 0.5rem;
  }

  ._sequenceArrow_19evq_3549 {
    transform: rotate(90deg);
  }

  ._questionTitle_19evq_499 {
    font-size: 1.2rem;
  }

  ._gameStats_19evq_311 {
    grid-template-columns: 1fr;
    gap: 0.25rem;
  }

  ._statValue_19evq_383 {
    font-size: 1.2rem;
  }

  ._statLabel_19evq_401 {
    font-size: 0.7rem;
  }
}

/* Suporte a alto contraste */
[data-theme="high-contrast"] {
  --card-background: #000;
  --card-border: 1px solid #fff;
  --success-bg: #28a745;
  --success-border: #1e7e34;
  --error-bg: #dc3545;
  --error-border: #bd2130;
}

/* =====================================================
 * 🎯 LAYOUT SEGUINDO PADRÃO MEMORY GAME - ESTRUTURA PADRÃO PORTAL BETINA V3
 * ===================================================== */

/* Área da pergunta - seguindo padrão Memory Game */
._questionArea_19evq_451 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1rem;
}

._questionHeader_19evq_491 {
  text-align: center;
  margin-bottom: 2rem;
}

._questionTitle_19evq_499 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

._instructions_19evq_4053 {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1rem;
  line-height: 1.4;
}

/* Display de padrões - seguindo padrão Memory Game */
._patternDisplay_19evq_1663 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  margin: 1rem 0;
  max-width: 600px;
}

._patternGrid_19evq_1707 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1rem;
  justify-content: center;
  max-width: 500px;
  margin: 0 auto;
}

._patternItem_19evq_4107 {
  width: 80px;
  height: 80px;
  background: var(--card-background);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  transition: all 0.3s ease;
}

._patternItem_19evq_4107._missing_19evq_1771 {
  border: 3px dashed rgba(255, 193, 7, 0.8);
  background: rgba(255, 193, 7, 0.2);
  animation: _pulse_19evq_1 2s infinite;
  color: rgba(255, 193, 7, 0.9);
  font-weight: bold;
}

/* Layout específico para Padrões Visuais - Mobile First */
._patternContainer_19evq_1235 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  width: 100%;
}

._patternSequence_19evq_1245 {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 120px;
}

._patternItem_19evq_4107[data-missing="true"] {
  border-color: rgba(255, 193, 7, 0.6);
  background: rgba(255, 193, 7, 0.1);
  animation: _pulse_19evq_1 2s ease-in-out infinite;
}

._patternEmoji_19evq_1255 {
  font-size: 2.5rem;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

._roundProgress_19evq_1353 {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: 0.5rem;
  display: inline-block;
}

._feedbackContainer_19evq_961 {
  padding: 1.5rem;
  border-radius: 20px;
  margin-top: 1.5rem;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

._correctFeedback_19evq_4261 {
  background: rgba(76, 175, 80, 0.2);
  border-color: rgba(76, 175, 80, 0.6);
}

._incorrectFeedback_19evq_4271 {
  background: rgba(244, 67, 54, 0.2);
  border-color: rgba(244, 67, 54, 0.6);
}

._feedbackIcon_19evq_1263 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

._feedbackTitle_19evq_1271 {
  font-size: 1.3rem;
  font-weight: bold;
  margin-bottom: 0.8rem;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

._feedbackText_19evq_4309 {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Área de opções - seguindo padrão Memory Game */
._optionsArea_19evq_4325 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  margin-top: 1rem;
  max-width: 500px;
}

._optionsTitle_19evq_1847 {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  text-align: center;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

._optionsGrid_19evq_4363 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1rem;
  justify-content: center;
}

._optionButton_19evq_1879 {
  width: 80px;
  height: 80px;
  background: var(--card-background);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
}

._optionButton_19evq_1879:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

._optionButton_19evq_1879:active {
  transform: scale(0.95);
}

/* Estilos para sequências - seguindo padrão Memory Game */
._sequenceDisplay_19evq_3501 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  margin: 1rem 0;
  max-width: 600px;
}

._displayTitle_19evq_4451 {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  text-align: center;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

._objectGrid_19evq_4469 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1rem;
  justify-content: center;
  max-width: 500px;
  margin: 0 auto;
}

._objectItem_19evq_4487 {
  width: 80px;
  height: 80px;
  background: var(--card-background);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  transition: all 0.3s ease;
}

._objectItem_19evq_4487._memorizing_19evq_4513 {
  animation: _memorizeGlow_19evq_1 0.6s ease-in-out;
}

._objectItem_19evq_4487._active_19evq_299 {
  background: rgba(255, 193, 7, 0.3);
  border-color: rgba(255, 193, 7, 0.8);
  box-shadow: 0 0 20px rgba(255, 193, 7, 0.5);
}

@keyframes _memorizeGlow_19evq_1 {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); box-shadow: 0 0 20px rgba(255, 255, 255, 0.5); }
  100% { transform: scale(1); }
}

/* Área de resposta do jogador - seguindo padrão Memory Game */
._playerResponseArea_19evq_4547 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  margin: 1rem 0;
  max-width: 600px;
}

._responseTitle_19evq_4567 {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  text-align: center;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

._responseGrid_19evq_4585 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1rem;
  justify-content: center;
  max-width: 500px;
  margin: 0 auto;
}

._responseSlot_19evq_4603 {
  width: 80px;
  height: 80px;
  background: var(--card-background);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

._responseSlot_19evq_4603._empty_19evq_4631 {
  border: 2px dashed rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.5);
}

._responseSlot_19evq_4603._filled_19evq_4643 {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.1));
  border-color: var(--success-border);
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.2);
}

._responseSlot_19evq_4603:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

/* Padrões complexos - seguindo padrão Memory Game */
._complexPatternGrid_19evq_4667 {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

._patternRow_19evq_4681 {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* Animação de pulso para elementos faltantes */
@keyframes _pulse_19evq_1 {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Responsividade específica para elementos dos padrões visuais - MOBILE FIRST */
@media (max-width: 768px) {
  ._patternGrid_19evq_1707,
  ._optionsGrid_19evq_4363,
  ._objectGrid_19evq_4469,
  ._responseGrid_19evq_4585 {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 0.75rem;
  }

  ._patternItem_19evq_4107,
  ._optionButton_19evq_1879,
  ._objectItem_19evq_4487,
  ._responseSlot_19evq_4603 {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  ._patternDisplay_19evq_1663,
  ._optionsArea_19evq_4325,
  ._sequenceDisplay_19evq_3501,
  ._playerResponseArea_19evq_4547 {
    padding: 1.5rem;
    margin: 0.75rem 0;
  }

  ._instructions_19evq_4053 {
    font-size: 1rem;
  }

  ._optionsContainer_19evq_1863 {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.75rem;
  }

  ._answerOption_19evq_691 {
    min-height: 80px;
    padding: 1rem;
    font-size: 0.8rem;
  }

  ._optionEmoji_19evq_1281 {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  ._patternGrid_19evq_1707,
  ._optionsGrid_19evq_4363,
  ._objectGrid_19evq_4469,
  ._responseGrid_19evq_4585 {
    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
    gap: 0.5rem;
  }

  ._patternItem_19evq_4107,
  ._optionButton_19evq_1879,
  ._objectItem_19evq_4487,
  ._responseSlot_19evq_4603 {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  ._patternDisplay_19evq_1663,
  ._optionsArea_19evq_4325,
  ._sequenceDisplay_19evq_3501,
  ._playerResponseArea_19evq_4547 {
    padding: 1rem;
    margin: 0.5rem 0;
  }

  ._instructions_19evq_4053 {
    font-size: 0.9rem;
  }

  ._optionsContainer_19evq_1863 {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  ._answerOption_19evq_691 {
    min-height: 70px;
    padding: 0.75rem;
    font-size: 0.7rem;
  }

  ._optionEmoji_19evq_1281 {
    font-size: 2rem;
  }

  ._optionLabel_19evq_1969 {
    font-size: 0.8rem;
  }
}

._activityTitle_19evq_1575 {
  font-size: 1.6rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

._activityDescription_19evq_1591 {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0;
  line-height: 1.4;
}

/* Objects Display - Conteúdo principal da atividade */
._objectsDisplay_19evq_555 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

._displayTitle_19evq_4451 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: white;
  text-align: center;
}

._objectGrid_19evq_4469 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1rem;
  justify-content: center;
  max-width: 700px;
  margin: 1.5rem auto;
  padding: 1rem;
}

._objectItem_19evq_4487 {
  background: var(--card-background);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 1rem;
  font-size: 2.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 80px;
  position: relative;
  backdrop-filter: var(--card-blur);
  box-shadow: var(--shadow-light);
}

._objectItem_19evq_4487:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.08);
  box-shadow: var(--shadow-medium);
}

._objectItem_19evq_4487._active_19evq_299 {
  background: rgba(255, 215, 0, 0.25);
  border-color: #ffd700;
  transform: scale(1.12);
  box-shadow: 0 0 25px rgba(255, 215, 0, 0.6);
  animation: _activeGlow_19evq_1 1.5s ease-in-out infinite alternate;
}

@keyframes _activeGlow_19evq_1 {
  0% { box-shadow: 0 0 25px rgba(255, 215, 0, 0.6); }
  100% { box-shadow: 0 0 35px rgba(255, 215, 0, 0.8); }
}

._objectItem_19evq_4487._missing_19evq_1771 {
  background: rgba(255, 255, 255, 0.08);
  border: 2px dashed rgba(255, 255, 255, 0.4);
  opacity: 0.8;
  font-size: 1.8rem;
  color: rgba(255, 255, 255, 0.6);
}

._objectItem_19evq_4487._anomaly_19evq_5087 {
  animation: _anomalyPulse_19evq_1 1s infinite alternate;
}

@keyframes _anomalyPulse_19evq_1 {
  0% { box-shadow: 0 0 5px rgba(255, 69, 58, 0.5); }
  100% { box-shadow: 0 0 15px rgba(255, 69, 58, 0.8); }
}

._objectSlot_19evq_5105 {
  background: rgba(255, 255, 255, 0.1);
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.75rem;
  min-height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

._objectSlot_19evq_5105._filled_19evq_4643 {
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.4);
}

._objectSlot_19evq_5105._empty_19evq_4631:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.5);
}

._slotNumber_19evq_5155 {
  font-size: 0.7rem;
  opacity: 0.6;
  margin-bottom: 0.25rem;
}

._slotContent_19evq_5167 {
  font-size: 1.6rem;
  line-height: 1;
}

._progressBar_19evq_1631 {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  height: 8px;
  margin: 1rem 0 0.5rem 0;
  overflow: hidden;
  position: relative;
}

._progressFill_19evq_1647 {
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  height: 100%;
  transition: width 0.5s ease;
  border-radius: 10px;
}

._progressText_19evq_5209 {
  text-align: center;
  font-size: 0.9rem;
  opacity: 0.9;
  margin-top: 0.5rem;
  display: block;
}

._timer_19evq_5225 {
  text-align: center;
  font-size: 1rem;
  color: #FFD700;
  margin-top: 1rem;
  padding: 0.5rem;
  background: rgba(255, 215, 0, 0.1);
  border-radius: 8px;
}

/* Answer Options - Opções de resposta */
._answerOptions_19evq_691 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
}

._answerGrid_19evq_5263 {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

._optionsTitle_19evq_1847 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: white;
  text-align: center;
}

._optionsContainer_19evq_1863 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
  justify-content: center;
  margin-top: 1rem;
}

._answerOption_19evq_691 {
  background: var(--card-background);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 1.8rem;
  color: white;
  min-width: 120px;
  min-height: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 90px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.6rem;
  position: relative;
  overflow: hidden;
  backdrop-filter: var(--card-blur);
  box-shadow: var(--shadow-light);
  font-weight: 600;
}

._answerOption_19evq_691:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.6);
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

._answerOption_19evq_691._selected_19evq_807 {
  background: rgba(0, 122, 255, 0.25);
  border-color: #007AFF;
  box-shadow: 0 0 20px rgba(0, 122, 255, 0.5);
  transform: translateY(-2px);
}

._answerOption_19evq_691._dragging_19evq_5381 {
  opacity: 0.8;
  transform: scale(0.98) rotate(2deg);
  box-shadow: var(--shadow-heavy);
}

._answerOption_19evq_691._primary_19evq_5393 {
  background: var(--success-bg);
  border-color: var(--success-border);
}

._answerOption_19evq_691._primary_19evq_5393:hover {
  background: rgba(52, 199, 89, 0.35);
  box-shadow: 0 0 20px rgba(52, 199, 89, 0.4);
}

._optionContent_19evq_1945 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

._optionEmoji_19evq_1281 {
  font-size: 3rem;
  line-height: 1;
}

._optionLabel_19evq_1969 {
  font-size: 0.9rem;
  text-align: center;
  opacity: 0.9;
  line-height: 1.2;
}

._gameHint_19evq_5451 {
  text-align: center;
  font-size: 0.9rem;
  opacity: 0.8;
  margin-top: 1rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  line-height: 1.4;
}

/* Responsividade */
@media (max-width: 768px) {
  ._gameContainer_19evq_2465 {
    gap: 1rem;
  }
  
  ._questionArea_19evq_451,
  ._objectsDisplay_19evq_555,
  ._answerOptions_19evq_691 {
    padding: 1rem;
  }
  
  ._objectGrid_19evq_4469 {
    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
    gap: 0.5rem;
  }
  
  ._objectItem_19evq_4487,
  ._objectSlot_19evq_5105 {
    min-height: 50px;
    padding: 0.5rem;
    font-size: 1.4rem;
  }
  
  ._optionsContainer_19evq_1863 {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
  }
  
  ._answerOption_19evq_691 {
    min-height: 70px;
    padding: 0.75rem;
  }
  
  ._activityTitle_19evq_1575 {
    font-size: 1.4rem;
  }
  
  ._displayTitle_19evq_4451 {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  ._objectGrid_19evq_4469 {
    grid-template-columns: repeat(auto-fit, minmax(45px, 1fr));
  }
  
  ._objectItem_19evq_4487,
  ._objectSlot_19evq_5105 {
    min-height: 45px;
    font-size: 1.2rem;
  }
  
  ._optionsContainer_19evq_1863 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Suporte a movimento reduzido */
._reduced-motion_19evq_1439 {
  ._answerButton_19evq_845, ._controlButton_19evq_1027, ._countingObject_19evq_575, ._feedbackMessage_19evq_2957, ._soundIndicator_19evq_3381, ._sequenceMissing_19evq_3559 {
    animation: none !important;
    transition: none !important;
  }
}


