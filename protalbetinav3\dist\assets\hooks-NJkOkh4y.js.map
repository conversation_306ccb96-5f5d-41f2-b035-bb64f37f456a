{"version": 3, "file": "hooks-NJkOkh4y.js", "sources": ["../../src/hooks/useAccessibility.js", "../../src/hooks/useUnifiedGameLogic.js", "../../src/ai/integrators/MultisensoryAIBrainIntegrator.js", "../../src/hooks/useMultisensoryIntegration.js", "../../src/hooks/useTherapeuticOrchestrator.js"], "sourcesContent": ["/**\r\n * @file useAccessibility.js\r\n * @description Hook personalizado para gerenciar configurações de acessibilidade em toda a aplicação\r\n * @version 3.0.0\r\n */\r\n\r\nimport { useState, useEffect, useCallback } from 'react'\r\n\r\n/**\r\n * Hook para gerenciar configurações de acessibilidade globalmente\r\n * @returns {Object} Configurações e métodos para acessibilidade\r\n */\r\nfunction useAccessibility () {  const [settings, setSettings] = useState({\r\n  textToSpeech: true,\r\n  highContrast: false, // SEMPRE FALSE por padrão\r\n  reducedMotion: false,\r\n  colorScheme: 'default',\r\n  dyslexiaFriendly: false,\r\n  fontSize: 'medium',\r\n  soundEnabled: true,\r\n  autoRead: false\r\n})\r\n  \r\n// Carregar configurações do localStorage ao montar\r\nuseEffect(() => {\r\n  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches\r\n  const systemPrefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches\r\n  const initialSettings = {\r\n    textToSpeech: true,\r\n    highContrast: false, // SEMPRE FALSE - não aplicar alto contraste por padrão\r\n    reducedMotion: systemPrefersReducedMotion,\r\n    colorScheme: systemPrefersDark ? 'dark' : 'default',\r\n    dyslexiaFriendly: false,\r\n    fontSize: 'medium',\r\n    soundEnabled: true,\r\n    autoRead: false\r\n  }\r\n\r\n  const localSettings = localStorage.getItem('betina_accessibility_settings')\r\n    \r\n  if (localSettings) {\r\n    try {\r\n      const parsedSettings = JSON.parse(localSettings)\r\n      // IMPORTANTE: Forçar highContrast como false mesmo se salvo como true\r\n      const safeSettings = { ...initialSettings, ...parsedSettings, highContrast: false }\r\n      setSettings(safeSettings)\r\n      applySettingsToDOM(safeSettings)\r\n    } catch (e) {\r\n      console.error('Erro ao carregar configurações de acessibilidade:', e)\r\n      setSettings(initialSettings)\r\n      applySettingsToDOM(initialSettings)\r\n    }\r\n  } else {\r\n    setSettings(initialSettings)\r\n    applySettingsToDOM(initialSettings)\r\n  }\r\n}, [])\r\n  \r\n// Aplicar configurações ao DOM\r\nconst applySettingsToDOM = useCallback((newSettings) => {\r\n  const root = document.documentElement\r\n  const body = document.body    // Alto contraste - usar apenas classe CSS, não data-theme\r\n  if (newSettings.highContrast) {\r\n    root.classList.add('high-contrast-active')\r\n    body.classList.add('high-contrast-active')\r\n  } else {\r\n    root.classList.remove('high-contrast-active')\r\n    body.classList.remove('high-contrast-active')\r\n  }\r\n\r\n  // Animações reduzidas\r\n  if (newSettings.reducedMotion) {\r\n    root.classList.add('reduced-motion')\r\n    body.classList.add('reduced-motion')\r\n  } else {\r\n    root.classList.remove('reduced-motion')\r\n    body.classList.remove('reduced-motion')\r\n  }    // Esquema de cores - NÃO aplicar \"high-contrast\" como tema\r\n  const themeToApply = newSettings.highContrast ? 'default' : newSettings.colorScheme\r\n  root.setAttribute('data-theme', themeToApply)\r\n  body.setAttribute('data-theme', themeToApply)\r\n\r\n  // Fonte para dislexia\r\n  if (newSettings.dyslexiaFriendly) {\r\n    root.classList.add('dyslexia-friendly')\r\n    body.classList.add('dyslexia-friendly')\r\n  } else {\r\n    root.classList.remove('dyslexia-friendly')\r\n    body.classList.remove('dyslexia-friendly')\r\n  }\r\n\r\n  // Tamanho da fonte\r\n  root.setAttribute('data-font-size', newSettings.fontSize)\r\n  body.setAttribute('data-font-size', newSettings.fontSize)\r\n}, [])\r\n\r\n// Atualizar e salvar novas configurações\r\nconst updateSettings = useCallback((newSettings) => {\r\n  const updatedSettings = { ...settings, ...newSettings }\r\n  setSettings(updatedSettings)\r\n  applySettingsToDOM(updatedSettings)\r\n    \r\n  try {\r\n    localStorage.setItem('betina_accessibility_settings', JSON.stringify(updatedSettings))\r\n  } catch (e) {\r\n    console.error('Erro ao salvar configurações de acessibilidade:', e)\r\n  }\r\n    \r\n  // Disparar evento para outros componentes que precisam saber sobre mudanças\r\n  window.dispatchEvent(new CustomEvent('accessibility-changed', { \r\n    detail: updatedSettings \r\n  }))\r\n}, [settings, applySettingsToDOM])\r\n  \r\n// Aplicar preset de configurações\r\nconst applyPreset = useCallback((preset) => {\r\n  let presetSettings = {}\r\n    \r\n  switch (preset) {      case 'high-contrast':\r\n    presetSettings = {\r\n      highContrast: true,\r\n      colorScheme: 'default', // Mantém o tema padrão, apenas ativa o contraste\r\n      fontSize: 'large'\r\n    }\r\n    break\r\n  case 'autism-friendly':\r\n    presetSettings = {\r\n      reducedMotion: true,\r\n      soundEnabled: false,\r\n      colorScheme: 'soft',\r\n      fontSize: 'medium'\r\n    }\r\n    break\r\n  case 'dyslexia':\r\n    presetSettings = {\r\n      dyslexiaFriendly: true,\r\n      fontSize: 'large',\r\n      textToSpeech: true\r\n    }\r\n    break\r\n  case 'default':\r\n    presetSettings = {\r\n      textToSpeech: true,\r\n      highContrast: false,\r\n      reducedMotion: false,\r\n      colorScheme: 'default',\r\n      dyslexiaFriendly: false,\r\n      fontSize: 'medium',\r\n      soundEnabled: true,\r\n      autoRead: false\r\n    }\r\n    break\r\n  }\r\n    \r\n  updateSettings(presetSettings)\r\n}, [updateSettings])\r\n\r\nreturn {\r\n  settings,\r\n  updateSettings,\r\n  applyPreset\r\n}\r\n}\r\n\r\nexport default useAccessibility\r\n", "/**\r\n * @file useUnifiedGameLogic.js\r\n * @description Hook unificado para lógica de jogos - Portal Betina V3\r\n * @version 3.0.0 - Integrado com PortalBetinaV3 conforme arquitetura\r\n */\r\n\r\nimport { useState, useEffect, useContext, useRef, useCallback } from 'react'\r\nimport { SystemContext } from '../components/context/SystemContext.jsx'\r\nimport { getPortalBetinaV3 } from '../api/services/PortalBetinaV3.js'\r\nimport { v4 as uuidv4 } from 'uuid'\r\n\r\n/**\r\n * Hook unificado para lógica de jogos\r\n * INTEGRAÇÃO COMPLETA: PortalBetinaV3 → MultisensoryCollector → PredictiveEngine → AdvancedMetrics\r\n */\r\nexport function useUnifiedGameLogic(gameType) {\r\n  // Usar context de forma mais resiliente\r\n  let systemContext = null;\r\n  let user = null;\r\n  let ttsEnabled = true;\r\n  \r\n  try {\r\n    systemContext = useContext(SystemContext);\r\n    if (systemContext) {\r\n      user = systemContext.user;\r\n      ttsEnabled = systemContext.ttsEnabled !== undefined ? systemContext.ttsEnabled : true;\r\n    }\r\n  } catch (error) {\r\n    console.warn('⚠️ SystemContext not available, using defaults:', error.message);\r\n  }\r\n  \r\n  const [sessionId, setSessionId] = useState(null)\r\n  const [isSessionActive, setIsSessionActive] = useState(false)\r\n  const [sessionMetrics, setSessionMetrics] = useState({})\r\n  const portalRef = useRef(null)\r\n  \r\n  // Estados básicos do jogo (mantidos para compatibilidade)\r\n  const [gameState, setGameState] = useState({\r\n    score: 0,\r\n    round: 1,\r\n    accuracy: 100,\r\n    totalCorrect: 0,\r\n    totalAttempts: 0,\r\n    difficulty: 'medium'\r\n  })\r\n\r\n  /**\r\n   * Inicializa Portal Betina V3\r\n   */\r\n  useEffect(() => {\r\n    const initializePortal = async () => {\r\n      try {\r\n        portalRef.current = await getPortalBetinaV3()\r\n        console.log('✅ Portal Betina V3 connected to', gameType)\r\n      } catch (error) {\r\n        console.error('❌ Failed to initialize Portal Betina V3:', error)\r\n      }\r\n    }\r\n    \r\n    initializePortal()\r\n  }, [gameType])\r\n\r\n  /**\r\n   * Cleanup: Finalizar sessão ativa quando componente for desmontado\r\n   */\r\n  useEffect(() => {\r\n    return () => {\r\n      if (sessionId && isSessionActive) {\r\n        console.log('🧹 Cleaning up active session on unmount:', sessionId);\r\n        \r\n        // Finalização assíncrona sem bloquear o unmount\r\n        (async () => {\r\n          try {\r\n            if (portalRef.current) {\r\n              const result = await portalRef.current.finalizeSession(sessionId, { \r\n                reason: 'component_unmount',\r\n                endTime: new Date().toISOString()\r\n              });\r\n              \r\n              if (result.success) {\r\n                console.log('✅ Session cleanup completed successfully');\r\n              } else {\r\n                console.warn('⚠️ Session cleanup completed with warnings:', result.message);\r\n              }\r\n            }\r\n          } catch (error) {\r\n            console.warn('⚠️ Error during session cleanup (non-critical):', error.message);\r\n          }\r\n        })();\r\n      }\r\n    };\r\n  }, [sessionId, isSessionActive])\r\n\r\n  /**\r\n   * FUNÇÃO PRINCIPAL: Inicia sessão de jogo\r\n   * Integra com PortalBetinaV3.startGameSession()\r\n   */\r\n  const startUnifiedSession = useCallback(async (difficulty = 'medium') => {\r\n    try {\r\n      if (!portalRef.current) {\r\n        console.debug('🔄 Portal not ready, starting local session for', gameType)\r\n        \r\n        // Iniciar sessão local se portal não estiver pronto\r\n        const localSessionId = `local-${gameType}-${uuidv4()}`\r\n        setSessionId(localSessionId)\r\n        setIsSessionActive(true)\r\n        setSessionMetrics({\r\n          sessionId: localSessionId,\r\n          gameType,\r\n          userId: user?.id || 'anonymous',\r\n          difficulty,\r\n          startTime: new Date().toISOString(),\r\n          mode: 'local'\r\n        })\r\n        \r\n        setGameState(prev => ({\r\n          ...prev,\r\n          difficulty,\r\n          sessionId: localSessionId\r\n        }))\r\n        \r\n        console.log(`🎮 Started local session for ${gameType}:`, localSessionId)\r\n        return { \r\n          success: true, \r\n          sessionId: localSessionId,\r\n          mode: 'local',\r\n          multisensoryEnabled: false,\r\n          predictiveEnabled: false\r\n        }\r\n      }\r\n      \r\n      const userId = user?.id || 'anonymous'\r\n      const result = await portalRef.current.startGameSession(userId, gameType, difficulty)\r\n      \r\n      if (result.success) {\r\n        setSessionId(result.sessionId)\r\n        setIsSessionActive(true)\r\n        setSessionMetrics({\r\n          sessionId: result.sessionId,\r\n          gameType,\r\n          userId,\r\n          difficulty,\r\n          startTime: new Date().toISOString(),\r\n          multisensoryEnabled: result.multisensoryEnabled,\r\n          predictiveEnabled: result.predictiveEnabled,\r\n          mode: 'portal'\r\n        })\r\n        \r\n        setGameState(prev => ({\r\n          ...prev,\r\n          difficulty,\r\n          sessionId: result.sessionId\r\n        }))\r\n        \r\n        console.log(`🎮 Started unified session for ${gameType}:`, result.sessionId)\r\n        return result\r\n      }\r\n      \r\n      throw new Error(result.error || 'Failed to start session')\r\n      \r\n    } catch (error) {\r\n      console.error('❌ Failed to start unified session, falling back to local:', error)\r\n      \r\n      // Fallback para sessão local em caso de erro\r\n      const fallbackSessionId = `fallback-${gameType}-${uuidv4()}`\r\n      setSessionId(fallbackSessionId)\r\n      setIsSessionActive(true)\r\n      setSessionMetrics({\r\n        sessionId: fallbackSessionId,\r\n        gameType,\r\n        userId: user?.id || 'anonymous',\r\n        difficulty,\r\n        startTime: new Date().toISOString(),\r\n        mode: 'fallback',\r\n        error: error.message\r\n      })\r\n      \r\n      setGameState(prev => ({\r\n        ...prev,\r\n        difficulty,\r\n        sessionId: fallbackSessionId\r\n      }))\r\n      \r\n      return { \r\n        success: true, \r\n        sessionId: fallbackSessionId,\r\n        mode: 'fallback',\r\n        multisensoryEnabled: false,\r\n        predictiveEnabled: false,\r\n        warning: 'Session started in fallback mode'\r\n      }\r\n    }\r\n  }, [gameType, user])\r\n\r\n  /**\r\n   * FUNÇÃO PRINCIPAL: Registra interação do jogo\r\n   * Integra com PortalBetinaV3.recordGameAction()\r\n   */\r\n  const recordInteraction = useCallback(async (actionType, data, isCorrect = null, duration = 0) => {\r\n    try {\r\n      // Se portal ou sessão não estão disponíveis, registrar localmente\r\n      if (!portalRef.current || !sessionId) {\r\n        // Não mostrar warning desnecessariamente, apenas debug\r\n        console.debug('📝 Recording interaction locally (portal/session not ready):', {\r\n          actionType,\r\n          gameType,\r\n          isCorrect,\r\n          duration\r\n        })\r\n        \r\n        // Atualizar estado local mesmo sem sessão ativa\r\n        setGameState(prev => {\r\n          const newTotalAttempts = prev.totalAttempts + 1\r\n          const newTotalCorrect = prev.totalCorrect + (isCorrect ? 1 : 0)\r\n          const newAccuracy = newTotalAttempts > 0 ? Math.round((newTotalCorrect / newTotalAttempts) * 100) : 100\r\n          \r\n          return {\r\n            ...prev,\r\n            totalAttempts: newTotalAttempts,\r\n            totalCorrect: newTotalCorrect,\r\n            accuracy: newAccuracy,\r\n            score: prev.score + (isCorrect ? 10 : 0)\r\n          }\r\n        })\r\n        \r\n        return { \r\n          success: true, \r\n          mode: 'local',\r\n          message: 'Interaction recorded locally' \r\n        }\r\n      }\r\n      \r\n      const action = {\r\n        type: actionType,\r\n        data,\r\n        isCorrect,\r\n        duration,\r\n        timestamp: new Date().toISOString(),\r\n        gameType,\r\n        round: gameState.round\r\n      }\r\n      \r\n      const result = await portalRef.current.recordGameAction(sessionId, action)\r\n      \r\n      if (result.success) {\r\n        // Atualizar estado do jogo\r\n        setGameState(prev => {\r\n          const newTotalAttempts = prev.totalAttempts + 1\r\n          const newTotalCorrect = prev.totalCorrect + (isCorrect ? 1 : 0)\r\n          const newAccuracy = newTotalAttempts > 0 ? Math.round((newTotalCorrect / newTotalAttempts) * 100) : 100\r\n          \r\n          return {\r\n            ...prev,\r\n            totalAttempts: newTotalAttempts,\r\n            totalCorrect: newTotalCorrect,\r\n            accuracy: newAccuracy,\r\n            score: prev.score + (isCorrect ? 10 : 0)\r\n          }\r\n        })\r\n        \r\n        // Atualizar métricas da sessão\r\n        setSessionMetrics(prev => ({\r\n          ...prev,\r\n          lastAction: action,\r\n          totalInteractions: (prev.totalInteractions || 0) + 1,\r\n          hasMultisensoryData: result.multisensoryData?.captured || false,\r\n          hasPredictiveAnalysis: result.predictions?.available || false\r\n        }))\r\n        \r\n        console.log(`📊 Recorded interaction: ${actionType}`, result.analysis?.processed ? '✅' : '⚠️')\r\n        return result\r\n      }\r\n      \r\n      throw new Error(result.error || 'Failed to record interaction')\r\n      \r\n    } catch (error) {\r\n      console.error('❌ Failed to record interaction:', error)\r\n      return { success: false, error: error.message }\r\n    }\r\n  }, [sessionId, gameType, gameState.round])\r\n\r\n  /**\r\n   * FUNÇÃO PRINCIPAL: Finaliza sessão\r\n   * Integra com PortalBetinaV3.finalizeSession()\r\n   */\r\n  const endUnifiedSession = useCallback(async (finalData = {}) => {\r\n    try {\r\n      // Verificar se há sessão ativa para finalizar\r\n      if (!sessionId || !isSessionActive) {\r\n        console.log('ℹ️ No active session to finalize or session already finalized');\r\n        return { success: true, message: 'No active session or session already finalized' };\r\n      }\r\n      \r\n      // Se portal não está disponível, finalizar localmente\r\n      if (!portalRef.current) {\r\n        console.log('🔄 Finalizing session locally (portal not available)');\r\n        setIsSessionActive(false);\r\n        setSessionMetrics(prev => ({\r\n          ...prev,\r\n          endTime: new Date().toISOString(),\r\n          finalData\r\n        }));\r\n        return { success: true, mode: 'local', message: 'Session finalized locally' };\r\n      }\r\n      \r\n      const result = await portalRef.current.finalizeSession(sessionId, finalData)\r\n      \r\n      if (result.success) {\r\n        setIsSessionActive(false)\r\n        setSessionMetrics(prev => ({\r\n          ...prev,\r\n          endTime: new Date().toISOString(),\r\n          finalReport: result.report,\r\n          therapeuticAnalysis: result.report?.therapeuticAnalysis,\r\n          multisensoryReport: result.report?.multisensoryReport,\r\n          predictions: result.report?.futurePredictions,\r\n          finalData\r\n        }))\r\n        \r\n        console.log(`🏁 Finalized session ${sessionId}:`, {\r\n          multisensory: !!result.report?.multisensoryReport,\r\n          therapeutic: !!result.report?.therapeuticAnalysis,\r\n          predictive: !!result.report?.futurePredictions\r\n        })\r\n        \r\n        return result\r\n      }\r\n      \r\n      throw new Error(result.error || 'Failed to finalize session')\r\n      \r\n    } catch (error) {\r\n      console.warn('⚠️ Session finalization error (may already be finalized):', error.message);\r\n      \r\n      // Marcar como inativa mesmo com erro para evitar loops\r\n      setIsSessionActive(false);\r\n      \r\n      return { \r\n        success: false, \r\n        error: error.message,\r\n        gracefulFailure: true \r\n      };\r\n    }\r\n  }, [sessionId, isSessionActive])\r\n\r\n  /**\r\n   * Reseta sessão\r\n   */\r\n  const resetSession = useCallback(() => {\r\n    setSessionId(null)\r\n    setIsSessionActive(false)\r\n    setSessionMetrics({})\r\n    setGameState({\r\n      score: 0,\r\n      round: 1,\r\n      accuracy: 100,\r\n      totalCorrect: 0,\r\n      totalAttempts: 0,\r\n      difficulty: 'medium'\r\n    })\r\n    \r\n    console.log('🔄 Session reset for', gameType)\r\n  }, [gameType])\r\n\r\n  /**\r\n   * Obtém status da sessão\r\n   */\r\n  const getSessionStatus = useCallback(() => {\r\n    if (!portalRef.current || !sessionId) {\r\n      return { active: false }\r\n    }\r\n    \r\n    return {\r\n      active: isSessionActive,\r\n      sessionId,\r\n      gameType,\r\n      metrics: sessionMetrics,\r\n      gameState,\r\n      portalStatus: portalRef.current.getSessionStatus(sessionId)\r\n    }\r\n  }, [sessionId, isSessionActive, sessionMetrics, gameState, gameType])\r\n\r\n  /**\r\n   * Repetir instrução (com TTS)\r\n   */\r\n  const repeatInstruction = useCallback(async (text) => {\r\n    // Registrar uso de acessibilidade\r\n    await recordInteraction('instruction_repeat', {\r\n      text,\r\n      accessibility: true,\r\n      ttsEnabled\r\n    })\r\n    \r\n    if (ttsEnabled && 'speechSynthesis' in window) {\r\n      const utterance = new SpeechSynthesisUtterance(text)\r\n      utterance.lang = 'pt-BR'\r\n      utterance.rate = 0.8\r\n      speechSynthesis.speak(utterance)\r\n    } else {\r\n      alert(text)\r\n    }\r\n  }, [recordInteraction, ttsEnabled])\r\n\r\n  return {\r\n    // Estados do jogo\r\n    gameState,\r\n    setGameState,\r\n    sessionMetrics,\r\n    isSessionActive,\r\n    sessionId,\r\n    \r\n    // Funções principais do Portal Betina V3\r\n    startUnifiedSession,\r\n    recordInteraction,\r\n    endUnifiedSession,\r\n    resetSession,\r\n    getSessionStatus,\r\n    \r\n    // Funções auxiliares\r\n    repeatInstruction,\r\n    \r\n    // Compatibilidade com sistemas existentes\r\n    ttsEnabled,\r\n    user,\r\n    \r\n    // Status do Portal\r\n    portalReady: !!portalRef.current\r\n  }\r\n}\r\n\r\nexport default useUnifiedGameLogic\r\n", "/**\r\n * @file MultisensoryAIBrainIntegrator.js\r\n * @description Integrador entre sistema multissensorial e AI Brain\r\n * @version 1.0.0\r\n */\r\n\r\nimport { AIBrainOrchestrator } from '../../api/services/ai/AIBrainOrchestrator.js';\r\nimport { StructuredLogger } from '../../api/services/core/logging/StructuredLogger.js';\r\n\r\n/**\r\n * Classe responsável pela integração das métricas multissensoriais com o AI Brain\r\n */\r\nexport class MultisensoryAIBrainIntegrator {\r\n  constructor(options = {}) {\r\n    this.logger = StructuredLogger.getInstance({\r\n      serviceName: 'MultisensoryAIBrainIntegrator',\r\n      logLevel: options.logLevel || 'info'\r\n    });\r\n    \r\n    this.aiBrain = options.aiBrain || new AIBrainOrchestrator();\r\n    this.enableRealTimeAnalysis = options.enableRealTimeAnalysis || false;\r\n    this.analysisFrequency = options.analysisFrequency || 'session-end'; // 'real-time', 'interval', 'session-end'\r\n    this.sessionData = null;\r\n    \r\n    this.isProcessing = false;\r\n    this.processingQueue = [];\r\n    this.lastProcessedTimestamp = null;\r\n    \r\n    this.logger.info('MultisensoryAIBrainIntegrator inicializado', {\r\n      enableRealTimeAnalysis: this.enableRealTimeAnalysis,\r\n      analysisFrequency: this.analysisFrequency\r\n    });\r\n  }\r\n  \r\n  /**\r\n   * Processa métricas multissensoriais e envia para o AI Brain\r\n   * @param {Object} metricsData - Dados coletados pelo MultisensoryMetricsCollector\r\n   * @param {Object} options - Opções de processamento\r\n   * @returns {Promise<Object>} - Resultado da análise do AI Brain\r\n   */\r\n  async processMultisensoryMetrics(metricsData, options = {}) {\r\n    if (!metricsData) {\r\n      this.logger.warn('Dados multissensoriais vazios ou inválidos');\r\n      return { success: false, error: 'Dados multissensoriais inválidos' };\r\n    }\r\n    \r\n    try {\r\n      this.logger.info('Processando métricas multissensoriais', {\r\n        sessionId: metricsData.sessionId,\r\n        dataPoints: metricsData.sensorData?.length || 0,\r\n        gameType: metricsData.gameType || 'unknown'\r\n      });\r\n      \r\n      // Armazenar dados da sessão\r\n      this.sessionData = metricsData;\r\n      \r\n      // Preparar dados para o AI Brain\r\n      const processedData = this._preprocessMultisensoryData(metricsData);\r\n      \r\n      // Enviar para o AI Brain\r\n      const aiResponse = await this._sendToAIBrain(processedData, options);\r\n      \r\n      // Registrar resultado\r\n      this.lastProcessedTimestamp = new Date().toISOString();\r\n      \r\n      return {\r\n        success: true,\r\n        analysisResults: aiResponse,\r\n        timestamp: this.lastProcessedTimestamp,\r\n        sessionId: metricsData.sessionId\r\n      };\r\n    } catch (error) {\r\n      this.logger.error('Erro ao processar métricas multissensoriais', error);\r\n      return {\r\n        success: false,\r\n        error: error.message || 'Erro desconhecido no processamento',\r\n        timestamp: new Date().toISOString()\r\n      };\r\n    }\r\n  }\r\n  \r\n  /**\r\n   * Pré-processa os dados multissensoriais para o formato esperado pelo AI Brain\r\n   * @private\r\n   * @param {Object} rawData - Dados brutos do MultisensoryMetricsCollector\r\n   * @returns {Object} - Dados processados prontos para o AI Brain\r\n   */\r\n  _preprocessMultisensoryData(rawData) {\r\n    // Extrair informações relevantes dos dados brutos\r\n    const { sessionId, userId, gameType, sensorData = [], deviceInfo, startTime, endTime } = rawData;\r\n    \r\n    // Extrair métricas multissensoriais específicas\r\n    const accelerometerData = this._extractSensorData(sensorData, 'accelerometer');\r\n    const gyroscopeData = this._extractSensorData(sensorData, 'gyroscope');\r\n    const touchData = this._extractSensorData(sensorData, 'touch');\r\n    const geolocationData = this._extractSensorData(sensorData, 'geolocation');\r\n    \r\n    // Calcular métricas agregadas\r\n    const aggregatedMetrics = this._calculateAggregatedMetrics(sensorData);\r\n    \r\n    // Formatar para o AI Brain\r\n    return {\r\n      metadata: {\r\n        sessionId,\r\n        userId,\r\n        gameType,\r\n        deviceType: deviceInfo?.deviceType || 'unknown',\r\n        platform: deviceInfo?.platform || 'unknown',\r\n        startTime,\r\n        endTime,\r\n        duration: endTime && startTime ? new Date(endTime) - new Date(startTime) : null,\r\n        dataPoints: sensorData.length\r\n      },\r\n      sensorMetrics: {\r\n        accelerometer: {\r\n          available: accelerometerData.length > 0,\r\n          dataPoints: accelerometerData.length,\r\n          data: accelerometerData\r\n        },\r\n        gyroscope: {\r\n          available: gyroscopeData.length > 0,\r\n          dataPoints: gyroscopeData.length,\r\n          data: gyroscopeData\r\n        },\r\n        touch: {\r\n          available: touchData.length > 0,\r\n          dataPoints: touchData.length,\r\n          data: touchData\r\n        },\r\n        geolocation: {\r\n          available: geolocationData.length > 0,\r\n          dataPoints: geolocationData.length,\r\n          data: geolocationData\r\n        }\r\n      },\r\n      aggregatedMetrics\r\n    };\r\n  }\r\n  \r\n  /**\r\n   * Extrai dados de um sensor específico\r\n   * @private\r\n   * @param {Array} sensorData - Array de dados dos sensores\r\n   * @param {String} sensorType - Tipo de sensor (accelerometer, gyroscope, etc.)\r\n   * @returns {Array} - Dados filtrados do sensor especificado\r\n   */\r\n  _extractSensorData(sensorData, sensorType) {\r\n    return sensorData.filter(data => data.type === sensorType)\r\n      .map(item => {\r\n        // Remover propriedades desnecessárias para economia de espaço\r\n        const { type, timestamp, ...values } = item;\r\n        return { timestamp, ...values };\r\n      });\r\n  }\r\n  \r\n  /**\r\n   * Calcula métricas agregadas a partir dos dados dos sensores\r\n   * @private\r\n   * @param {Array} sensorData - Array de dados dos sensores\r\n   * @returns {Object} - Métricas agregadas\r\n   */\r\n  _calculateAggregatedMetrics(sensorData) {\r\n    // Dados agrupados por tipo\r\n    const dataByType = sensorData.reduce((acc, item) => {\r\n      if (!acc[item.type]) acc[item.type] = [];\r\n      acc[item.type].push(item);\r\n      return acc;\r\n    }, {});\r\n    \r\n    // Frequência de movimentos bruscos (acelerômetro)\r\n    const accelerometerData = dataByType.accelerometer || [];\r\n    const suddenMovements = accelerometerData.filter(item => {\r\n      const magnitude = Math.sqrt(\r\n        Math.pow(item.x || 0, 2) + \r\n        Math.pow(item.y || 0, 2) + \r\n        Math.pow(item.z || 0, 2)\r\n      );\r\n      return magnitude > 15; // Valor arbitrário para movimento brusco\r\n    }).length;\r\n    \r\n    // Padrões de toque\r\n    const touchData = dataByType.touch || [];\r\n    const touchPatterns = this._analyzeTouchPatterns(touchData);\r\n    \r\n    // Estabilidade do dispositivo (giroscópio)\r\n    const gyroscopeData = dataByType.gyroscope || [];\r\n    const deviceStability = this._calculateDeviceStability(gyroscopeData);\r\n    \r\n    return {\r\n      deviceHandling: {\r\n        suddenMovements,\r\n        suddenMovementsPerMinute: accelerometerData.length > 0 ? \r\n          (suddenMovements / (accelerometerData.length / 60)) : 0,\r\n        deviceStability: deviceStability * 100 // Porcentagem\r\n      },\r\n      userInteraction: {\r\n        touchFrequency: touchData.length,\r\n        touchPrecision: touchPatterns.precision * 100, // Porcentagem\r\n        touchConsistency: touchPatterns.consistency * 100 // Porcentagem\r\n      }\r\n    };\r\n  }\r\n  \r\n  /**\r\n   * Analisa padrões de toque\r\n   * @private\r\n   * @param {Array} touchData - Dados de toque\r\n   * @returns {Object} - Análise de padrões de toque\r\n   */\r\n  _analyzeTouchPatterns(touchData) {\r\n    // Implementação simplificada\r\n    return {\r\n      precision: touchData.length > 10 ? 0.85 : 0.7,\r\n      consistency: touchData.length > 20 ? 0.9 : 0.75\r\n    };\r\n  }\r\n  \r\n  /**\r\n   * Calcula a estabilidade do dispositivo\r\n   * @private\r\n   * @param {Array} gyroscopeData - Dados do giroscópio\r\n   * @returns {Number} - Índice de estabilidade (0-1)\r\n   */\r\n  _calculateDeviceStability(gyroscopeData) {\r\n    if (gyroscopeData.length < 5) return 0.5; // Dados insuficientes\r\n    \r\n    // Implementação simplificada\r\n    const instabilitySum = gyroscopeData.reduce((sum, item) => {\r\n      const magnitude = Math.sqrt(\r\n        Math.pow(item.alpha || 0, 2) + \r\n        Math.pow(item.beta || 0, 2) + \r\n        Math.pow(item.gamma || 0, 2)\r\n      );\r\n      return sum + magnitude;\r\n    }, 0);\r\n    \r\n    const avgInstability = instabilitySum / gyroscopeData.length;\r\n    return Math.max(0, Math.min(1, 1 - (avgInstability / 90))); // 90 graus como máximo\r\n  }\r\n  \r\n  /**\r\n   * Envia dados processados para o AI Brain\r\n   * @private\r\n   * @param {Object} processedData - Dados processados\r\n   * @param {Object} options - Opções adicionais\r\n   * @returns {Promise<Object>} - Resposta do AI Brain\r\n   */\r\n  async _sendToAIBrain(processedData, options = {}) {\r\n    try {\r\n      this.logger.info('Enviando dados para AI Brain', {\r\n        sessionId: processedData.metadata.sessionId,\r\n        dataPoints: processedData.metadata.dataPoints\r\n      });\r\n      \r\n      // Preparar payload para o AI Brain\r\n      const payload = {\r\n        analysisType: 'multisensory-metrics',\r\n        data: processedData,\r\n        options: {\r\n          includeDetailedAnalysis: options.includeDetailedAnalysis !== false,\r\n          generateVisualizations: options.generateVisualizations || false,\r\n          priority: options.priority || 'normal',\r\n          ...options\r\n        }\r\n      };\r\n      \r\n      // Chamar o AI Brain\r\n      const response = await this.aiBrain.processData(payload);\r\n      \r\n      this.logger.info('Resposta recebida do AI Brain', {\r\n        sessionId: processedData.metadata.sessionId,\r\n        analysisId: response.analysisId || 'unknown'\r\n      });\r\n      \r\n      return response;\r\n    } catch (error) {\r\n      this.logger.error('Erro ao enviar dados para AI Brain', error);\r\n      throw new Error(`Falha na comunicação com AI Brain: ${error.message}`);\r\n    }\r\n  }\r\n  \r\n  /**\r\n   * Obtém o último relatório de análise\r\n   * @returns {Object|null} - Último relatório ou null se não houver\r\n   */\r\n  getLatestReport() {\r\n    if (!this.sessionData || !this.lastProcessedTimestamp) {\r\n      return null;\r\n    }\r\n    \r\n    return {\r\n      sessionId: this.sessionData.sessionId,\r\n      userId: this.sessionData.userId,\r\n      gameType: this.sessionData.gameType,\r\n      processedAt: this.lastProcessedTimestamp,\r\n      // Nota: Relatórios completos seriam armazenados em um sistema real\r\n      reportAvailable: true,\r\n      reportType: 'multisensory-analysis'\r\n    };\r\n  }\r\n}\r\n\r\nexport default MultisensoryAIBrainIntegrator;\r\n", "import { useState, useEffect, useCallback, useRef } from 'react';\r\nimport { MultisensoryMetricsCollector } from '../api/services/multisensoryAnalysis/multisensoryMetrics.js';\r\nimport { MultisensoryAIBrainIntegrator } from '../ai/integrators/MultisensoryAIBrainIntegrator.js';\r\n\r\n// 📝 Sistema de Logs Estruturados\r\nimport { StructuredLogger } from '../api/services/core/logging/StructuredLogger.js';\r\n\r\n/**\r\n * Armazena dados da sessão multissensorial\r\n * @param {Object} sessionData - Dados da sessão para armazenar\r\n * @returns {Promise<boolean>} Sucesso do armazenamento\r\n */\r\nasync function storeSessionData(sessionData) {\r\n  try {\r\n    // Tentar armazenar no localStorage primeiro\r\n    const storageKey = `multisensory_session_${sessionData.sessionId}`;\r\n    const dataToStore = {\r\n      ...sessionData,\r\n      storedAt: Date.now(),\r\n      version: '3.1.0'\r\n    };\r\n\r\n    localStorage.setItem(storageKey, JSON.stringify(dataToStore));\r\n\r\n    // Manter apenas as últimas 50 sessões\r\n    const allKeys = Object.keys(localStorage).filter(key => key.startsWith('multisensory_session_'));\r\n    if (allKeys.length > 50) {\r\n      const sortedKeys = allKeys.sort((a, b) => {\r\n        const aData = JSON.parse(localStorage.getItem(a) || '{}');\r\n        const bData = JSON.parse(localStorage.getItem(b) || '{}');\r\n        return (aData.storedAt || 0) - (bData.storedAt || 0);\r\n      });\r\n\r\n      // Remover as mais antigas\r\n      const keysToRemove = sortedKeys.slice(0, allKeys.length - 50);\r\n      keysToRemove.forEach(key => localStorage.removeItem(key));\r\n    }\r\n\r\n    // Tentar enviar para API (se disponível)\r\n    try {\r\n      const response = await fetch('/api/multisensory/sessions', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(dataToStore)\r\n      });\r\n\r\n      if (!response.ok) {\r\n        console.warn('API storage failed, using localStorage only');\r\n      }\r\n    } catch (apiError) {\r\n      console.warn('API not available, using localStorage only:', apiError.message);\r\n    }\r\n\r\n    return true;\r\n  } catch (error) {\r\n    console.error('Failed to store session data:', error);\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * Hook para integração multissensorial em jogos\r\n * \r\n * REFATORADO v3.1: Agora usa MultisensoryMetricsCollector diretamente,\r\n * eliminando a camada intermediária redundante do GameSensorIntegrator.\r\n * \r\n * @param {string} gameType - Tipo do jogo (ex: 'color-match', 'memory-game', etc.)\r\n * @param {Object} collectors - Coletores específicos do jogo (opcional)\r\n * @param {Object} options - Opções de configuração\r\n * @returns {Object} Interface do hook multissensorial\r\n */\r\nexport function useMultisensoryIntegration(gameType, collectors, options = {}) {\r\n  // 📝 Logger estruturado para o hook\r\n  const [logger] = useState(() => StructuredLogger.getInstance({\r\n    serviceName: 'useMultisensoryIntegration',\r\n    logLevel: options.logLevel || 'info'\r\n  }));\r\n\r\n  const [multisensoryCollector] = useState(() => new MultisensoryMetricsCollector(gameType));\r\n  const [aiBrainIntegrator] = useState(() => new MultisensoryAIBrainIntegrator({\r\n    enableRealTimeAnalysis: options.enableRealTimeAnalysis || false,\r\n    analysisFrequency: options.analysisFrequency || 'session-end',\r\n    logLevel: options.logLevel || 'info'\r\n  }));\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n  const [currentSession, setCurrentSession] = useState(null);\r\n  const [multisensoryData, setMultisensoryData] = useState(null);\r\n  const [sessionReport, setSessionReport] = useState(null);\r\n  const [aiAnalysisResults, setAiAnalysisResults] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n\r\n  const lastUpdateRef = useRef(Date.now());\r\n  const updateIntervalRef = useRef(null);\r\n  const sessionIdRef = useRef(null);\r\n\r\n  // Configurações padrão\r\n  const defaultOptions = {\r\n    autoUpdate: true,\r\n    updateInterval: 1000, // 1 segundo\r\n    enablePatternAnalysis: true,\r\n    logLevel: 'info',\r\n    enableSensorAccess: true,\r\n    sensorUpdateRate: 16, // 60fps\r\n    ...options\r\n  };\r\n\r\n  /**\r\n   * Inicializa a sessão multissensorial\r\n   */\r\n  const initializeSession = useCallback(async (sessionId, sessionConfig = {}) => {\r\n    try {\r\n      setIsLoading(true);\r\n      setError(null);\r\n\r\n      // Gerar sessionId se não fornecido\r\n      const finalSessionId = sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n      sessionIdRef.current = finalSessionId;\r\n\r\n      // Inicializar o coletor multissensorial usando os métodos corretos\r\n      const initResult = await multisensoryCollector.startMetricsCollection(\r\n        finalSessionId,\r\n        sessionConfig.userId || 'anonymous',\r\n        {\r\n          gameType,\r\n          enableSensorAccess: defaultOptions.enableSensorAccess,\r\n          sensorUpdateRate: defaultOptions.sensorUpdateRate,\r\n          ...sessionConfig\r\n        }\r\n      );\r\n\r\n      if (!initResult.success) {\r\n        throw new Error(initResult.error || 'Falha ao inicializar sessão multissensorial');\r\n      }\r\n\r\n      // Configurar sessão\r\n      setCurrentSession({\r\n        sessionId: finalSessionId,\r\n        gameType,\r\n        startTime: new Date().toISOString(),\r\n        ...sessionConfig\r\n      });\r\n\r\n      setIsInitialized(true);\r\n      \r\n      // Iniciar atualizações automáticas se habilitado\r\n      if (defaultOptions.autoUpdate) {\r\n        startAutoUpdate();\r\n      }\r\n      \r\n      logger.info('🎯 Sessão multissensorial inicializada (DIRECT)', {\r\n        type: 'session_initialized',\r\n        sessionId: finalSessionId,\r\n        gameType,\r\n        autoUpdate: defaultOptions.autoUpdate,\r\n        sensorAccess: defaultOptions.enableSensorAccess,\r\n        timestamp: new Date().toISOString()\r\n      });\r\n\r\n      return { success: true, sessionId: finalSessionId };\r\n\r\n    } catch (err) {\r\n      setError(err);\r\n      logger.error('❌ Erro ao inicializar sessão multissensorial', {\r\n        type: 'session_init_error',\r\n        error: err.message,\r\n        gameType,\r\n        sessionId\r\n      });\r\n      return { success: false, error: err.message };\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [gameType, multisensoryCollector, defaultOptions, logger]);\r\n\r\n  /**\r\n   * Registra uma interação multissensorial\r\n   */\r\n  const recordInteraction = useCallback(async (action, data = {}) => {\r\n    if (!isInitialized || !sessionIdRef.current) {\r\n      logger.warn('⚠️ Sessão multissensorial não inicializada', {\r\n        type: 'interaction_not_initialized',\r\n        action,\r\n        gameType\r\n      });\r\n      return { success: false, error: 'Session not initialized' };\r\n    }\r\n\r\n    try {\r\n      // Obter métricas atuais do multissensorial\r\n      const currentMetrics = await multisensoryCollector.getCurrentMetrics();\r\n      \r\n      // Normalizar dados da interação do jogo\r\n      const normalizedMetrics = {\r\n        gameId: gameType,\r\n        sessionId: sessionIdRef.current,\r\n        userId: currentSession?.userId || 'anonymous',\r\n        action,\r\n        timestamp: new Date().toISOString(),\r\n        ...data\r\n      };\r\n\r\n      // Processar dados multissensoriais se disponíveis\r\n      let result = { success: true, data: normalizedMetrics };\r\n      \r\n      if (currentMetrics && currentMetrics.mobileSensors) {\r\n        const multisensoryData = [currentMetrics];\r\n        const processedResult = await multisensoryCollector.processMultisensoryData(\r\n          normalizedMetrics,\r\n          multisensoryData\r\n        );\r\n        \r\n        if (!processedResult.error) {\r\n          result.data = {\r\n            ...normalizedMetrics,\r\n            multisensoryAnalysis: processedResult\r\n          };\r\n        }\r\n      }\r\n\r\n      lastUpdateRef.current = Date.now();\r\n\r\n      // Atualizar dados locais se não estiver em modo auto-update\r\n      if (!defaultOptions.autoUpdate) {\r\n        await updateMultisensoryData();\r\n      }\r\n\r\n      logger.debug('📊 Interação multissensorial registrada', {\r\n        action,\r\n        sessionId: sessionIdRef.current,\r\n        success: result.success,\r\n        hasMultisensoryData: !!currentMetrics\r\n      });\r\n      \r\n      return result;\r\n\r\n    } catch (err) {\r\n      logger.error('❌ Erro ao registrar interação multissensorial', {\r\n        type: 'interaction_error',\r\n        error: err.message,\r\n        action,\r\n        gameType\r\n      });\r\n      setError(err);\r\n      return { success: false, error: err.message };\r\n    }\r\n  }, [isInitialized, gameType, multisensoryCollector, currentSession, defaultOptions, logger]);\r\n\r\n  /**\r\n   * Atualiza dados multissensoriais\r\n   */\r\n  const updateMultisensoryData = useCallback(async () => {\r\n    if (!isInitialized || !sessionIdRef.current) return;\r\n\r\n    try {\r\n      const currentData = await multisensoryCollector.getCurrentMetrics();\r\n      if (currentData) {\r\n        setMultisensoryData(currentData);\r\n      }\r\n    } catch (err) {\r\n      logger.error('❌ Erro ao atualizar dados multissensoriais', {\r\n        type: 'data_update_error',\r\n        error: err.message,\r\n        gameType\r\n      });\r\n    }\r\n  }, [isInitialized, multisensoryCollector, gameType, logger]);\r\n\r\n  /**\r\n   * Inicia atualizações automáticas\r\n   */\r\n  const startAutoUpdate = useCallback(() => {\r\n    if (updateIntervalRef.current) {\r\n      clearInterval(updateIntervalRef.current);\r\n    }\r\n    \r\n    updateIntervalRef.current = setInterval(() => {\r\n      updateMultisensoryData();\r\n    }, defaultOptions.updateInterval);\r\n  }, [updateMultisensoryData, defaultOptions.updateInterval]);\r\n\r\n  /**\r\n   * Para atualizações automáticas\r\n   */\r\n  const stopAutoUpdate = useCallback(() => {\r\n    if (updateIntervalRef.current) {\r\n      clearInterval(updateIntervalRef.current);\r\n      updateIntervalRef.current = null;\r\n    }\r\n  }, []);\r\n\r\n  /**\r\n   * Analisa padrões multissensoriais\r\n   */\r\n  const analyzePatterns = useCallback(async () => {\r\n    if (!isInitialized || !defaultOptions.enablePatternAnalysis || !sessionIdRef.current) {\r\n      return null;\r\n    }\r\n    \r\n    try {\r\n      // Usar getCurrentMetrics para obter padrões atuais\r\n      const currentMetrics = await multisensoryCollector.getCurrentMetrics();\r\n      if (currentMetrics && currentMetrics.neurodivergencePatterns) {\r\n        return currentMetrics.neurodivergencePatterns;\r\n      }\r\n      return null;\r\n    } catch (err) {\r\n      logger.error('❌ Erro ao analisar padrões', {\r\n        type: 'pattern_analysis_error',\r\n        error: err.message,\r\n        gameType\r\n      });\r\n      return null;\r\n    }\r\n  }, [isInitialized, multisensoryCollector, defaultOptions.enablePatternAnalysis, gameType, logger]);\r\n\r\n  /**\r\n   * Finaliza a sessão multissensorial\r\n   */\r\n  const finalizeSession = useCallback(async (sessionData = {}) => {\r\n    if (!isInitialized || !sessionIdRef.current) {\r\n      logger.warn('⚠️ Nenhuma sessão ativa para finalizar', {\r\n        type: 'finalize_not_initialized',\r\n        gameType\r\n      });\r\n      return null;\r\n    }\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      stopAutoUpdate();\r\n      \r\n      // Finalizar sessão usando stopMetricsCollection\r\n      const report = await multisensoryCollector.stopMetricsCollection();\r\n      \r\n      if (report.success) {\r\n        setSessionReport(report.report);\r\n        \r\n        // Enviar dados para o AI Brain para análise\r\n        logger.info('🧠 Enviando dados multissensoriais para AI Brain');\r\n        const aiResult = await aiBrainIntegrator.processMultisensoryMetrics(report.report, {\r\n          includeDetailedAnalysis: true,\r\n          generateVisualizations: true\r\n        });\r\n        \r\n        if (aiResult.success) {\r\n          setAiAnalysisResults(aiResult.analysisResults);\r\n          logger.info('✅ Análise AI Brain concluída', {\r\n            analysisId: aiResult.analysisResults?.analysisId || 'unknown'\r\n          });\r\n\r\n          // Armazenar dados da sessão\r\n          try {\r\n            await storeSessionData({\r\n              sessionId: sessionIdRef.current,\r\n              gameType,\r\n              multisensoryReport: report.report,\r\n              aiAnalysis: aiResult.analysisResults,\r\n              sessionData,\r\n              timestamp: new Date().toISOString()\r\n            });\r\n            logger.info('💾 Dados da sessão armazenados com sucesso');\r\n          } catch (storageError) {\r\n            logger.warn('⚠️ Falha ao armazenar dados da sessão', {\r\n              error: storageError.message\r\n            });\r\n          }\r\n        } else {\r\n          logger.warn('⚠️ Falha na análise AI Brain', {\r\n            error: aiResult.error\r\n          });\r\n        }\r\n      }\r\n      \r\n      // Limpar estado\r\n      setCurrentSession(null);\r\n      setIsInitialized(false);\r\n      setMultisensoryData(null);\r\n      sessionIdRef.current = null;\r\n      \r\n      logger.info('✅ Sessão multissensorial finalizada (DIRECT)', {\r\n        type: 'session_finalized',\r\n        sessionId: sessionIdRef.current,\r\n        gameType,\r\n        reportGenerated: report.success,\r\n        timestamp: new Date().toISOString()\r\n      });\r\n\r\n      return report.success ? report.report : null;\r\n\r\n    } catch (err) {\r\n      setError(err);\r\n      logger.error('❌ Erro ao finalizar sessão multissensorial', {\r\n        type: 'session_finalize_error',\r\n        error: err.message,\r\n        sessionId: sessionIdRef.current,\r\n        gameType\r\n      });\r\n      return null;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [isInitialized, multisensoryCollector, gameType, stopAutoUpdate, logger]);\r\n\r\n  /**\r\n   * Obtém recomendações baseadas nos dados atuais\r\n   */\r\n  const getRecommendations = useCallback(async () => {\r\n    if (!multisensoryData || !sessionIdRef.current) return [];\r\n    \r\n    try {\r\n      // Gerar recomendações básicas baseadas nos dados multissensoriais\r\n      const basicRecommendations = [];\r\n      \r\n      // Análise visual baseada nos dados atuais\r\n      if (multisensoryData.mobileSensors) {\r\n        const sensorData = multisensoryData.mobileSensors;\r\n        \r\n        // Análise de movimento\r\n        if (sensorData.accelerometer) {\r\n          const movement = sensorData.accelerometer;\r\n          if (movement.variability > 0.8) {\r\n            basicRecommendations.push('Considere exercícios de estabilização motora');\r\n          }\r\n          if (movement.avgMagnitude < 0.2) {\r\n            basicRecommendations.push('Tente atividades com mais movimentação');\r\n          }\r\n        }\r\n        \r\n        // Análise de orientação\r\n        if (sensorData.gyroscope) {\r\n          const orientation = sensorData.gyroscope;\r\n          if (orientation.stability < 0.5) {\r\n            basicRecommendations.push('Pratique exercícios de orientação espacial');\r\n          }\r\n        }\r\n        \r\n        // Análise tátil\r\n        if (sensorData.touch) {\r\n          const touch = sensorData.touch;\r\n          if (touch.pressure && touch.pressure.average > 0.8) {\r\n            basicRecommendations.push('Experimente toques mais suaves na tela');\r\n          }\r\n          if (touch.frequency < 0.3) {\r\n            basicRecommendations.push('Aumente a frequência de interações');\r\n          }\r\n        }\r\n      }\r\n      \r\n      // Análise de padrões de neurodivergência\r\n      if (multisensoryData.neurodivergencePatterns) {\r\n        const patterns = multisensoryData.neurodivergencePatterns;\r\n        \r\n        if (patterns.stimmingIndicators && patterns.stimmingIndicators.level > 0.7) {\r\n          basicRecommendations.push('Considere pausas regulares para autorregulação');\r\n        }\r\n        \r\n        if (patterns.sensorySeekingLevel > 0.8) {\r\n          basicRecommendations.push('Experimente atividades com estímulos sensoriais variados');\r\n        }\r\n        \r\n        if (patterns.sensoryAvoidanceLevel > 0.7) {\r\n          basicRecommendations.push('Reduza estímulos sensoriais intensos');\r\n        }\r\n      }\r\n      \r\n      return basicRecommendations;\r\n      \r\n    } catch (err) {\r\n      logger.error('❌ Erro ao gerar recomendações', {\r\n        type: 'recommendations_error',\r\n        error: err.message,\r\n        gameType\r\n      });\r\n      \r\n      // Fallback para recomendações muito básicas\r\n      return [\r\n        'Continue praticando para melhorar suas habilidades',\r\n        'Experimente ajustar a velocidade do jogo',\r\n        'Mantenha uma postura confortável durante o jogo'\r\n      ];\r\n    }\r\n  }, [multisensoryData, multisensoryCollector, gameType, logger]);\r\n\r\n  /**\r\n   * Obtém dados em tempo real dos sensores\r\n   */\r\n  const getSensorData = useCallback(async () => {\r\n    if (!isInitialized || !sessionIdRef.current) return null;\r\n    \r\n    try {\r\n      const currentMetrics = await multisensoryCollector.getCurrentMetrics();\r\n      return currentMetrics ? currentMetrics.mobileSensors : null;\r\n    } catch (err) {\r\n      logger.error('❌ Erro ao obter dados dos sensores', {\r\n        type: 'sensor_data_error',\r\n        error: err.message,\r\n        gameType\r\n      });\r\n      return null;\r\n    }\r\n  }, [isInitialized, multisensoryCollector, gameType, logger]);\r\n\r\n  /**\r\n   * Obtém métricas de neurodivergência\r\n   */\r\n  const getNeurodivergenceMetrics = useCallback(async () => {\r\n    if (!isInitialized || !sessionIdRef.current) return null;\r\n    \r\n    try {\r\n      const currentMetrics = await multisensoryCollector.getCurrentMetrics();\r\n      return currentMetrics ? currentMetrics.neurodivergencePatterns : null;\r\n    } catch (err) {\r\n      logger.error('❌ Erro ao obter métricas de neurodivergência', {\r\n        type: 'neurodivergence_metrics_error',\r\n        error: err.message,\r\n        gameType\r\n      });\r\n      return null;\r\n    }\r\n  }, [isInitialized, multisensoryCollector, gameType, logger]);\r\n\r\n  /**\r\n   * Limpa recursos e para atualizações ao desmontar\r\n   */\r\n  useEffect(() => {\r\n    return () => {\r\n      stopAutoUpdate();\r\n      if (multisensoryCollector && isInitialized) {\r\n        multisensoryCollector.stopMetricsCollection().catch(err => {\r\n          logger.error('❌ Erro ao limpar sessão no cleanup', { error: err.message });\r\n        });\r\n      }\r\n    };\r\n  }, [stopAutoUpdate, multisensoryCollector, isInitialized, logger]);\r\n\r\n  // Interface pública do hook\r\n  return {\r\n    // Estado\r\n    isInitialized,\r\n    currentSession,\r\n    multisensoryData,\r\n    sessionReport,\r\n    isLoading,\r\n    error,\r\n    \r\n    // Métodos principais\r\n    initializeSession,\r\n    recordInteraction,\r\n    finalizeSession,\r\n    \r\n    // Análise e recomendações\r\n    analyzePatterns,\r\n    getRecommendations,\r\n    getSensorData,\r\n    getNeurodivergenceMetrics,\r\n    \r\n    // Controle de atualizações\r\n    updateMultisensoryData,\r\n    startAutoUpdate,\r\n    stopAutoUpdate,\r\n    \r\n    // Integração com AI Brain\r\n    aiAnalysisResults,\r\n    getAiBrainReport: () => aiBrainIntegrator.getLatestReport(),\r\n    \r\n    // Dados brutos (para depuração)\r\n    collector: defaultOptions.logLevel === 'debug' ? multisensoryCollector : null,\r\n    sessionId: sessionIdRef.current\r\n  };\r\n}\r\n\r\n/**\r\n * Hook simplificado para jogos básicos\r\n * \r\n * REFATORADO v3.1: Versão mais simples do hook para jogos que não precisam \r\n * de funcionalidades avançadas. Agora usa MultisensoryMetricsCollector diretamente.\r\n * \r\n * @param {string} gameType - Tipo do jogo\r\n * @param {Object} collectors - Coletores do jogo (opcional)\r\n * @returns {Object} Interface simplificada\r\n */\r\nexport function useSimpleMultisensoryIntegration(gameType, collectors) {\r\n  const integration = useMultisensoryIntegration(gameType, collectors, {\r\n    autoUpdate: false,\r\n    enablePatternAnalysis: false,\r\n    logLevel: 'warn',\r\n    enableSensorAccess: true\r\n  });\r\n  \r\n  return {\r\n    isReady: integration.isInitialized,\r\n    startSession: integration.initializeSession,\r\n    recordAction: integration.recordInteraction,\r\n    endSession: integration.finalizeSession,\r\n    getCurrentData: integration.multisensoryData,\r\n    getReport: integration.sessionReport,\r\n    getSensors: integration.getSensorData,\r\n    isLoading: integration.isLoading,\r\n    error: integration.error,\r\n    sessionId: integration.sessionId\r\n  };\r\n}\r\n\r\nexport default useMultisensoryIntegration;\r\n", "/**\r\n * @file useTherapeuticOrchestrator.js\r\n * @description Hook React avançado para orquestração terapêutica com AI Brain integrado\r\n * @version 4.0.0 - Versão com AI Brain, análise preditiva e recomendações em tempo real\r\n * <AUTHOR> Betina V3\r\n */\r\n\r\nimport { useState, useEffect, useCallback, useContext, useRef, useMemo } from 'react';\r\nimport { SystemContext } from '../components/context/SystemContext';\r\n\r\n/**\r\n * Hook para utilização das funcionalidades terapêuticas do SystemOrchestrator\r\n * @param {Object} [options] - Opções para o hook\r\n * @param {string} [options.userId] - ID do usuário (opcional)\r\n * @returns {Object} Métodos e estado das funcionalidades terapêuticas\r\n */\r\nexport const useTherapeuticOrchestrator = (options = {}) => {\r\n  const systemContext = useContext(SystemContext);\r\n  const integratedSystem = systemContext?.system;\r\n  \r\n  // Validar e normalizar o userId na inicialização\r\n  const initUserId = options.userId && options.userId !== 'anonymous' && options.userId !== '' \r\n    ? options.userId \r\n    : null;\r\n    \r\n  const [userId, setUserId] = useState(initUserId);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  const [recommendations, setRecommendations] = useState([]);\r\n  const [therapeuticData, setTherapeuticData] = useState(null);\r\n  const sessionStartTimeRef = useRef(Date.now());\r\n\r\n  /**\r\n   * Lista de jogos suportados\r\n   * @private\r\n   */\r\n  const supportedGames = [\r\n    'ImageAssociation',\r\n    'MemoryGame',\r\n    'MusicalSequence',\r\n    'PadroesVisuais',\r\n    'ContagemNumeros',\r\n    'PatternMatching',\r\n    'SequenceLearning',\r\n    'CreativePainting',\r\n    'ColorMatch',\r\n    'LetterRecognition',\r\n    'QuebraCabeca'\r\n  ];\r\n\r\n  /**\r\n   * Valida dados de entrada para cada jogo\r\n   * @param {string} gameId - ID do jogo\r\n   * @param {Object} metrics - Métricas do jogo\r\n   * @returns {Object} Métricas validadas\r\n   * @private\r\n   */\r\n  const validateGameMetrics = useCallback((gameId, metrics) => {\r\n    try {\r\n      const normalizedGameId = normalizeGameId(gameId);\r\n      \r\n      if (!supportedGames.includes(normalizedGameId)) {\r\n        throw new Error(`Jogo não suportado: ${gameId} (normalizado: ${normalizedGameId}). Jogos suportados: ${supportedGames.join(', ')}`);\r\n      }\r\n\r\n      const validatedMetrics = {\r\n        ...metrics,\r\n        gameId: normalizedGameId,\r\n        sessionId: metrics.sessionId || `session_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,\r\n        childId: metrics.childId || userId || `child_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`,\r\n        sessionDuration: metrics.sessionDuration || calculateSessionDuration(metrics),\r\n        timestamp: new Date().toISOString(),\r\n        gameName: normalizedGameId\r\n      };\r\n\r\n      // Validações específicas por jogo\r\n      switch (normalizedGameId) {\r\n        case 'ImageAssociation':\r\n          if (!metrics.category || !metrics.correct) {\r\n            throw new Error('Campos category e correct são obrigatórios para ImageAssociation');\r\n          }\r\n          break;\r\n        case 'MemoryGame':\r\n          if (!metrics.pairFound || !metrics.cardPosition) {\r\n            throw new Error('Campos pairFound e cardPosition são obrigatórios para MemoryGame');\r\n          }\r\n          break;\r\n        case 'MusicalSequence':\r\n          if (!metrics.sequenceLength || !metrics.expectedResponseTime) {\r\n            throw new Error('Campos sequenceLength e expectedResponseTime são obrigatórios para MusicalSequence');\r\n          }\r\n          break;\r\n        case 'PadroesVisuais':\r\n          if (!metrics.patternComplexity || !metrics.correct) {\r\n            throw new Error('Campos patternComplexity e correct são obrigatórios para PadroesVisuais');\r\n          }\r\n          break;\r\n        case 'ContagemNumeros':\r\n          if (!metrics.score || !metrics.correct) {\r\n            throw new Error('Campos score e correct são obrigatórios para ContagemNumeros');\r\n          }\r\n          break;\r\n        case 'PatternMatching':\r\n          if (!metrics.correctMatch || !metrics.patternComplexity) {\r\n            throw new Error('Campos correctMatch e patternComplexity são obrigatórios para PatternMatching');\r\n          }\r\n          break;\r\n        case 'SequenceLearning':\r\n          if (!metrics.isSequence || !metrics.sequenceLength) {\r\n            throw new Error('Campos isSequence e sequenceLength são obrigatórios para SequenceLearning');\r\n          }\r\n          break;\r\n        case 'CreativePainting':\r\n          if (!metrics.strokes || !metrics.creationTime || !metrics.colorDiversity) {\r\n            throw new Error('Campos strokes, creationTime e colorDiversity são obrigatórios para CreativePainting');\r\n          }\r\n          break;\r\n        case 'ColorMatch':\r\n          if (!metrics.correct || !metrics.colorSelected) {\r\n            throw new Error('Campos correct e colorSelected são obrigatórios para ColorMatch');\r\n          }\r\n          break;\r\n        case 'LetterRecognition':\r\n          if (!metrics.correct || !metrics.letterShown) {\r\n            throw new Error('Campos correct e letterShown são obrigatórios para LetterRecognition');\r\n          }\r\n          break;\r\n        case 'QuebraCabeca':\r\n          if (!metrics.piecesMoved || !metrics.completionRate) {\r\n            throw new Error('Campos piecesMoved e completionRate são obrigatórios para QuebraCabeca');\r\n          }\r\n          break;\r\n        default:\r\n          throw new Error('Jogo não reconhecido durante validação');\r\n      }\r\n\r\n      return validatedMetrics;\r\n    } catch (error) {\r\n      console.error('❌ Erro na validação de métricas:', { gameId, error: error.message });\r\n      throw error;\r\n    }\r\n  }, [userId, supportedGames]);\r\n\r\n  /**\r\n   * Verifica se o sistema está inicializado\r\n   * @private\r\n   */\r\n  const checkSystem = useCallback(() => {\r\n    if (!integratedSystem?.systemOrchestrator) {\r\n      throw new Error('SystemOrchestrator não está disponível no SystemContext');\r\n    }\r\n  }, [integratedSystem]);\r\n\r\n  /**\r\n   * Calcula a duração da sessão\r\n   * @param {Object} metrics - Métricas do jogo\r\n   * @returns {number} Duração em segundos\r\n   * @private\r\n   */\r\n  const calculateSessionDuration = (metrics) => {\r\n    try {\r\n      if (metrics?.sessionStartTime) {\r\n        return (Date.now() - new Date(metrics.sessionStartTime).getTime()) / 1000;\r\n      }\r\n      if (sessionStartTimeRef.current) {\r\n        return (Date.now() - sessionStartTimeRef.current) / 1000;\r\n      }\r\n      return 0;\r\n    } catch (error) {\r\n      console.error('❌ Erro ao calcular duração da sessão:', { error: error.message });\r\n      return 0;\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Define o ID do usuário para o hook\r\n   * @param {string} id - ID do usuário\r\n   */\r\n  const setUserContext = useCallback((id) => {\r\n    if (id && typeof id === 'string') {\r\n      setUserId(id);\r\n      console.log('👤 UserId definido:', { userId: id });\r\n    } else {\r\n      console.warn('⚠️ ID de usuário inválido:', { id });\r\n      setError('ID de usuário inválido');\r\n    }\r\n  }, []);\r\n\r\n  /**\r\n   * Processa métricas de jogo através do orquestrador terapêutico\r\n   * @param {string} gameId - ID do jogo\r\n   * @param {Object} metrics - Métricas do jogo\r\n   * @param {string} [userIdOverride] - ID do usuário (opcional, sobrescreve o estado)\r\n   * @returns {Promise<Object>} Resultado do processamento\r\n   */\r\n  const processGameMetrics = useCallback(async (gameId, metrics, userIdOverride = null) => {\r\n    try {\r\n      checkSystem();\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      const userIdToUse = userIdOverride || userId;\r\n      if (!userIdToUse) {\r\n        throw new Error('ID do usuário não especificado');\r\n      }\r\n\r\n      const validatedMetrics = validateGameMetrics(gameId, metrics);\r\n\r\n      console.log('🧠 Processando métricas:', {\r\n        gameId,\r\n        sessionId: validatedMetrics.sessionId,\r\n        userId: validatedMetrics.childId,\r\n        sessionDuration: validatedMetrics.sessionDuration\r\n      });\r\n\r\n      const result = await integratedSystem.processGameMetrics(\r\n        validatedMetrics.childId,\r\n        gameId,\r\n        validatedMetrics\r\n      );\r\n\r\n      if (result.success) {\r\n        setRecommendations(result.recommendations || []);\r\n        setTherapeuticData(prev => ({\r\n          ...(prev || {}),\r\n          insights: result.insights || [],\r\n          specificAnalysis: result.specificAnalysis || {},\r\n          lastUpdate: new Date().toISOString()\r\n        }));\r\n        console.log('✅ Métricas processadas com sucesso:', { gameId });\r\n      } else {\r\n        throw new Error(result.error || 'Erro desconhecido no processamento de métricas');\r\n      }\r\n\r\n      return {\r\n        success: true,\r\n        data: result,\r\n        recommendations: result.recommendations || [],\r\n        insights: result.insights || []\r\n      };\r\n    } catch (err) {\r\n      setError(err.message);\r\n      console.error('❌ Erro ao processar métricas de jogo:', { gameId, error: err.message });\r\n      return {\r\n        success: false,\r\n        error: err.message,\r\n        recommendations: [],\r\n        insights: []\r\n      };\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [integratedSystem, userId, checkSystem, validateGameMetrics]);\r\n\r\n  /**\r\n   * Obtém recomendações terapêuticas para o usuário\r\n   * @param {string} [userIdOverride] - ID do usuário (opcional, sobrescreve o estado)\r\n   * @returns {Promise<Object>} Recomendações terapêuticas\r\n   */\r\n  const getRecommendations = useCallback(async (userIdOverride = null) => {\r\n    try {\r\n      checkSystem();\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      const userIdToUse = userIdOverride || userId;\r\n      if (!userIdToUse || userIdToUse === 'anonymous' || userIdToUse === '') {\r\n        console.log('⚠️ UserId inválido para recomendações terapêuticas:', { userId: userIdToUse });\r\n        return {\r\n          success: false,\r\n          error: 'ID do usuário não especificado ou inválido',\r\n          recommendations: [],\r\n          overview: {}\r\n        };\r\n      }\r\n\r\n      const result = await integratedSystem.getTherapeuticRecommendations(userIdToUse);\r\n\r\n      if (result.success && result.hasData) {\r\n        setRecommendations(result.recommendations || []);\r\n        setTherapeuticData(prev => ({\r\n          ...(prev || {}),\r\n          overview: result.overview || {},\r\n          therapyMode: result.therapyMode || 'standard',\r\n          lastUpdate: new Date().toISOString()\r\n        }));\r\n        console.log('✅ Recomendações obtidas com sucesso:', { userId: userIdToUse });\r\n        return {\r\n          success: true,\r\n          recommendations: result.recommendations || [],\r\n          overview: result.overview || {},\r\n          therapyMode: result.therapyMode || 'standard'\r\n        };\r\n      } else {\r\n        throw new Error(result.error || 'Nenhuma recomendação disponível');\r\n      }\r\n    } catch (err) {\r\n      setError(err.message);\r\n      console.error('❌ Erro ao obter recomendações terapêuticas:', { userId: userIdOverride || userId, error: err.message });\r\n      return {\r\n        success: false,\r\n        error: err.message,\r\n        recommendations: [],\r\n        overview: {}\r\n      };\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [integratedSystem, userId, checkSystem]);\r\n\r\n  /**\r\n   * Processa métricas categorizadas\r\n   * @param {Object} categorizedMetrics - Métricas categorizadas\r\n   * @returns {Promise<Object>} Insights processados por categoria\r\n   */\r\n  const processCategorizedMetrics = useCallback(async (categorizedMetrics) => {\r\n    try {\r\n      checkSystem();\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      if (!userId) {\r\n        throw new Error('ID do usuário não especificado');\r\n      }\r\n\r\n      // Validação básica de métricas categorizadas\r\n      if (!categorizedMetrics || typeof categorizedMetrics !== 'object') {\r\n        throw new Error('Métricas categorizadas inválidas');\r\n      }\r\n\r\n      const metrics = {\r\n        timestamp: new Date().toISOString(),\r\n        categories: categorizedMetrics,\r\n        meta: {\r\n          source: 'categorized-metrics',\r\n          version: '3.2.2'\r\n        }\r\n      };\r\n\r\n      const result = await integratedSystem.dispatchEvent('therapeutic:process-categorized-metrics', {\r\n        userId,\r\n        metrics\r\n      });\r\n\r\n      if (result.success) {\r\n        setTherapeuticData(prev => ({\r\n          ...(prev || {}),\r\n          categorizedInsights: result.insights || [],\r\n          lastUpdate: new Date().toISOString()\r\n        }));\r\n        console.log('✅ Métricas categorizadas processadas:', { userId });\r\n      }\r\n\r\n      return {\r\n        success: result.success,\r\n        insights: result.insights || [],\r\n        error: result.error || null\r\n      };\r\n    } catch (err) {\r\n      setError(err.message);\r\n      console.error('❌ Erro ao processar métricas categorizadas:', { userId, error: err.message });\r\n      return {\r\n        success: false,\r\n        error: err.message,\r\n        insights: []\r\n      };\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [integratedSystem, userId, checkSystem]);\r\n\r\n  /**\r\n   * Obtém estatísticas do orquestrador\r\n   * @returns {Promise<Object>} Estatísticas do sistema\r\n   */\r\n  const getOrchestratorStats = useCallback(async () => {\r\n    try {\r\n      checkSystem();\r\n      const stats = await integratedSystem.getOrchestratorStats();\r\n      console.log('📊 Estatísticas do orquestrador obtidas:', stats);\r\n      return {\r\n        success: true,\r\n        stats\r\n      };\r\n    } catch (err) {\r\n      console.error('❌ Erro ao obter estatísticas do orquestrador:', { error: err.message });\r\n      return {\r\n        success: false,\r\n        error: err.message,\r\n        stats: {}\r\n      };\r\n    }\r\n  }, [integratedSystem, checkSystem]);\r\n\r\n  /**\r\n   * Verifica se um jogo é suportado\r\n   * @param {string} gameId - ID do jogo\r\n   * @returns {boolean} Indica se o jogo é suportado\r\n   */\r\n  const isGameSupported = useCallback((gameId) => {\r\n    const supported = supportedGames.includes(gameId);\r\n    if (!supported) {\r\n      console.warn('⚠️ Jogo não suportado:', { gameId, supportedGames });\r\n    }\r\n    return supported;\r\n  }, [supportedGames]);\r\n\r\n  /**\r\n   * Normaliza o nome do jogo para corresponder aos suportados\r\n   * @param {string} gameId - ID do jogo\r\n   * @returns {string} Nome normalizado do jogo\r\n   * @private\r\n   */\r\n  const normalizeGameId = useCallback((gameId) => {\r\n    if (!gameId || gameId === 'undefined') return 'unknown';\r\n    \r\n    // Remove sufixos como -Start, -End, etc.\r\n    const cleanGameId = gameId.replace(/-\\w+$/, '');\r\n    \r\n    // Mapear variações de nomes\r\n    const gameMapping = {\r\n      'color-match': 'ColorMatch',\r\n      'memory-game': 'MemoryGame',\r\n      'musical-sequence': 'MusicalSequence',\r\n      'padroes-visuais': 'PadroesVisuais',\r\n      'contagem-numeros': 'ContagemNumeros',\r\n      'number-counting': 'ContagemNumeros',\r\n      'pattern-matching': 'PatternMatching',\r\n      'sequence-learning': 'SequenceLearning',\r\n      'creative-painting': 'CreativePainting',\r\n      'image-association': 'ImageAssociation',\r\n      'letter-recognition': 'LetterRecognition',\r\n      'quebra-cabeca': 'QuebraCabeca'\r\n    };\r\n    \r\n    return gameMapping[cleanGameId] || cleanGameId;\r\n  }, []);\r\n\r\n  /**\r\n   * Limpa o estado do hook\r\n   */\r\n  const clearState = useCallback(() => {\r\n    setUserId(null);\r\n    setError(null);\r\n    setRecommendations([]);\r\n    setTherapeuticData(null);\r\n    sessionStartTimeRef.current = Date.now();\r\n    console.log('🧹 Estado do hook limpo');\r\n  }, []);\r\n\r\n  // Carrega recomendações iniciais quando userId ou integratedSystem mudar\r\n  useEffect(() => {\r\n    if (userId && userId !== 'anonymous' && userId !== '' && integratedSystem?.systemOrchestrator) {\r\n      getRecommendations().catch(err => {\r\n        console.warn('⚠️ Não foi possível carregar recomendações iniciais:', { error: err.message });\r\n      });\r\n    }\r\n  }, [userId, integratedSystem, getRecommendations]);\r\n\r\n  // Log de inicialização do hook\r\n  useEffect(() => {\r\n    console.log('🎮 Hook useTherapeuticOrchestrator inicializado:', {\r\n      userId,\r\n      hasSystem: !!integratedSystem?.systemOrchestrator,\r\n      supportedGames\r\n    });\r\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\r\n\r\n  return {\r\n    // Estado\r\n    userId,\r\n    loading,\r\n    error,\r\n    recommendations,\r\n    therapeuticData,\r\n    supportedGames,\r\n\r\n    // Métodos\r\n    setUserContext,\r\n    processGameMetrics,\r\n    getRecommendations,\r\n    processCategorizedMetrics,\r\n    getOrchestratorStats,\r\n    isGameSupported,\r\n    clearState,\r\n\r\n    // Estado do sistema\r\n    hasSystem: !!integratedSystem?.systemOrchestrator\r\n  };\r\n};\r\n\r\nexport default useTherapeuticOrchestrator;"], "names": ["useState", "useEffect", "useCallback", "useContext", "useRef", "uuidv4", "multisensoryData", "error"], "mappings": ";;;;AAYA,SAAS,mBAAoB;AAAG,QAAM,CAAC,UAAU,WAAW,IAAIA,sBAAS;AAAA,IACvE,cAAc;AAAA,IACd,cAAc;AAAA;AAAA,IACd,eAAe;AAAA,IACf,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,cAAc;AAAA,IACd,UAAU;AAAA,EAAA,CACX;AAGDC,eAAAA,UAAU,MAAM;AACd,UAAM,oBAAoB,OAAO,WAAW,8BAA8B,EAAE;AAC5E,UAAM,6BAA6B,OAAO,WAAW,kCAAkC,EAAE;AACzF,UAAM,kBAAkB;AAAA,MACtB,cAAc;AAAA,MACd,cAAc;AAAA;AAAA,MACd,eAAe;AAAA,MACf,aAAa,oBAAoB,SAAS;AAAA,MAC1C,kBAAkB;AAAA,MAClB,UAAU;AAAA,MACV,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAEM,UAAA,gBAAgB,aAAa,QAAQ,+BAA+B;AAE1E,QAAI,eAAe;AACb,UAAA;AACI,cAAA,iBAAiB,KAAK,MAAM,aAAa;AAE/C,cAAM,eAAe,EAAE,GAAG,iBAAiB,GAAG,gBAAgB,cAAc,MAAM;AAClF,oBAAY,YAAY;AACxB,2BAAmB,YAAY;AAAA,eACxB,GAAG;AACF,gBAAA,MAAM,qDAAqD,CAAC;AACpE,oBAAY,eAAe;AAC3B,2BAAmB,eAAe;AAAA,MAAA;AAAA,IACpC,OACK;AACL,kBAAY,eAAe;AAC3B,yBAAmB,eAAe;AAAA,IAAA;AAAA,EAEtC,GAAG,EAAE;AAGC,QAAA,qBAAqBC,yBAAY,CAAC,gBAAgB;AACtD,UAAM,OAAO,SAAS;AACtB,UAAM,OAAO,SAAS;AACtB,QAAI,YAAY,cAAc;AACvB,WAAA,UAAU,IAAI,sBAAsB;AACpC,WAAA,UAAU,IAAI,sBAAsB;AAAA,IAAA,OACpC;AACA,WAAA,UAAU,OAAO,sBAAsB;AACvC,WAAA,UAAU,OAAO,sBAAsB;AAAA,IAAA;AAI9C,QAAI,YAAY,eAAe;AACxB,WAAA,UAAU,IAAI,gBAAgB;AAC9B,WAAA,UAAU,IAAI,gBAAgB;AAAA,IAAA,OAC9B;AACA,WAAA,UAAU,OAAO,gBAAgB;AACjC,WAAA,UAAU,OAAO,gBAAgB;AAAA,IAAA;AAExC,UAAM,eAAe,YAAY,eAAe,YAAY,YAAY;AACnE,SAAA,aAAa,cAAc,YAAY;AACvC,SAAA,aAAa,cAAc,YAAY;AAG5C,QAAI,YAAY,kBAAkB;AAC3B,WAAA,UAAU,IAAI,mBAAmB;AACjC,WAAA,UAAU,IAAI,mBAAmB;AAAA,IAAA,OACjC;AACA,WAAA,UAAU,OAAO,mBAAmB;AACpC,WAAA,UAAU,OAAO,mBAAmB;AAAA,IAAA;AAItC,SAAA,aAAa,kBAAkB,YAAY,QAAQ;AACnD,SAAA,aAAa,kBAAkB,YAAY,QAAQ;AAAA,EAC1D,GAAG,EAAE;AAGC,QAAA,iBAAiBA,yBAAY,CAAC,gBAAgB;AAClD,UAAM,kBAAkB,EAAE,GAAG,UAAU,GAAG,YAAY;AACtD,gBAAY,eAAe;AAC3B,uBAAmB,eAAe;AAE9B,QAAA;AACF,mBAAa,QAAQ,iCAAiC,KAAK,UAAU,eAAe,CAAC;AAAA,aAC9E,GAAG;AACF,cAAA,MAAM,mDAAmD,CAAC;AAAA,IAAA;AAI7D,WAAA,cAAc,IAAI,YAAY,yBAAyB;AAAA,MAC5D,QAAQ;AAAA,IAAA,CACT,CAAC;AAAA,EAAA,GACD,CAAC,UAAU,kBAAkB,CAAC;AAG3B,QAAA,cAAcA,yBAAY,CAAC,WAAW;AAC1C,QAAI,iBAAiB,CAAC;AAEtB,YAAQ,QAAQ;AAAA,MAAO,KAAK;AACT,yBAAA;AAAA,UACf,cAAc;AAAA,UACd,aAAa;AAAA;AAAA,UACb,UAAU;AAAA,QACZ;AACA;AAAA,MACF,KAAK;AACc,yBAAA;AAAA,UACf,eAAe;AAAA,UACf,cAAc;AAAA,UACd,aAAa;AAAA,UACb,UAAU;AAAA,QACZ;AACA;AAAA,MACF,KAAK;AACc,yBAAA;AAAA,UACf,kBAAkB;AAAA,UAClB,UAAU;AAAA,UACV,cAAc;AAAA,QAChB;AACA;AAAA,MACF,KAAK;AACc,yBAAA;AAAA,UACf,cAAc;AAAA,UACd,cAAc;AAAA,UACd,eAAe;AAAA,UACf,aAAa;AAAA,UACb,kBAAkB;AAAA,UAClB,UAAU;AAAA,UACV,cAAc;AAAA,UACd,UAAU;AAAA,QACZ;AACA;AAAA,IAAA;AAGF,mBAAe,cAAc;AAAA,EAAA,GAC5B,CAAC,cAAc,CAAC;AAEZ,SAAA;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA;ACnJO,SAAS,oBAAoB,UAAU;AAE5C,MAAI,gBAAgB;AACpB,MAAI,OAAO;AACX,MAAI,aAAa;AAEb,MAAA;AACF,oBAAgBC,wBAAW,aAAa;AACxC,QAAI,eAAe;AACjB,aAAO,cAAc;AACrB,mBAAa,cAAc,eAAe,SAAY,cAAc,aAAa;AAAA,IAAA;AAAA,WAE5E,OAAO;AACN,YAAA,KAAK,mDAAmD,MAAM,OAAO;AAAA,EAAA;AAG/E,QAAM,CAAC,WAAW,YAAY,IAAIH,aAAAA,SAAS,IAAI;AAC/C,QAAM,CAAC,iBAAiB,kBAAkB,IAAIA,aAAAA,SAAS,KAAK;AAC5D,QAAM,CAAC,gBAAgB,iBAAiB,IAAIA,aAAAA,SAAS,CAAA,CAAE;AACjD,QAAA,YAAYI,oBAAO,IAAI;AAG7B,QAAM,CAAC,WAAW,YAAY,IAAIJ,sBAAS;AAAA,IACzC,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,IACV,cAAc;AAAA,IACd,eAAe;AAAA,IACf,YAAY;AAAA,EAAA,CACb;AAKDC,eAAAA,UAAU,MAAM;AACd,UAAM,mBAAmB,YAAY;AAC/B,UAAA;AACQ,kBAAA,UAAU,MAAM,kBAAkB;AACpC,gBAAA,IAAI,mCAAmC,QAAQ;AAAA,eAChD,OAAO;AACN,gBAAA,MAAM,4CAA4C,KAAK;AAAA,MAAA;AAAA,IAEnE;AAEiB,qBAAA;AAAA,EAAA,GAChB,CAAC,QAAQ,CAAC;AAKbA,eAAAA,UAAU,MAAM;AACd,WAAO,MAAM;AACX,UAAI,aAAa,iBAAiB;AACxB,gBAAA,IAAI,6CAA6C,SAAS;AAGlE,SAAC,YAAY;AACP,cAAA;AACF,gBAAI,UAAU,SAAS;AACrB,oBAAM,SAAS,MAAM,UAAU,QAAQ,gBAAgB,WAAW;AAAA,gBAChE,QAAQ;AAAA,gBACR,UAAS,oBAAI,KAAK,GAAE,YAAY;AAAA,cAAA,CACjC;AAED,kBAAI,OAAO,SAAS;AAClB,wBAAQ,IAAI,0CAA0C;AAAA,cAAA,OACjD;AACG,wBAAA,KAAK,+CAA+C,OAAO,OAAO;AAAA,cAAA;AAAA,YAC5E;AAAA,mBAEK,OAAO;AACN,oBAAA,KAAK,mDAAmD,MAAM,OAAO;AAAA,UAAA;AAAA,QAC/E,GACC;AAAA,MAAA;AAAA,IAEP;AAAA,EAAA,GACC,CAAC,WAAW,eAAe,CAAC;AAM/B,QAAM,sBAAsBC,aAAAA,YAAY,OAAO,aAAa,aAAa;AACnE,QAAA;AACE,UAAA,CAAC,UAAU,SAAS;AACd,gBAAA,MAAM,mDAAmD,QAAQ;AAGzE,cAAM,iBAAiB,SAAS,QAAQ,IAAIG,IAAQ;AACpD,qBAAa,cAAc;AAC3B,2BAAmB,IAAI;AACL,0BAAA;AAAA,UAChB,WAAW;AAAA,UACX;AAAA,UACA,QAAQ,MAAM,MAAM;AAAA,UACpB;AAAA,UACA,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,UAClC,MAAM;AAAA,QAAA,CACP;AAED,qBAAa,CAAS,UAAA;AAAA,UACpB,GAAG;AAAA,UACH;AAAA,UACA,WAAW;AAAA,QAAA,EACX;AAEF,gBAAQ,IAAI,gCAAgC,QAAQ,KAAK,cAAc;AAChE,eAAA;AAAA,UACL,SAAS;AAAA,UACT,WAAW;AAAA,UACX,MAAM;AAAA,UACN,qBAAqB;AAAA,UACrB,mBAAmB;AAAA,QACrB;AAAA,MAAA;AAGI,YAAA,SAAS,MAAM,MAAM;AAC3B,YAAM,SAAS,MAAM,UAAU,QAAQ,iBAAiB,QAAQ,UAAU,UAAU;AAEpF,UAAI,OAAO,SAAS;AAClB,qBAAa,OAAO,SAAS;AAC7B,2BAAmB,IAAI;AACL,0BAAA;AAAA,UAChB,WAAW,OAAO;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,UACA,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,UAClC,qBAAqB,OAAO;AAAA,UAC5B,mBAAmB,OAAO;AAAA,UAC1B,MAAM;AAAA,QAAA,CACP;AAED,qBAAa,CAAS,UAAA;AAAA,UACpB,GAAG;AAAA,UACH;AAAA,UACA,WAAW,OAAO;AAAA,QAAA,EAClB;AAEF,gBAAQ,IAAI,kCAAkC,QAAQ,KAAK,OAAO,SAAS;AACpE,eAAA;AAAA,MAAA;AAGT,YAAM,IAAI,MAAM,OAAO,SAAS,yBAAyB;AAAA,aAElD,OAAO;AACN,cAAA,MAAM,6DAA6D,KAAK;AAGhF,YAAM,oBAAoB,YAAY,QAAQ,IAAIA,IAAQ;AAC1D,mBAAa,iBAAiB;AAC9B,yBAAmB,IAAI;AACL,wBAAA;AAAA,QAChB,WAAW;AAAA,QACX;AAAA,QACA,QAAQ,MAAM,MAAM;AAAA,QACpB;AAAA,QACA,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAClC,MAAM;AAAA,QACN,OAAO,MAAM;AAAA,MAAA,CACd;AAED,mBAAa,CAAS,UAAA;AAAA,QACpB,GAAG;AAAA,QACH;AAAA,QACA,WAAW;AAAA,MAAA,EACX;AAEK,aAAA;AAAA,QACL,SAAS;AAAA,QACT,WAAW;AAAA,QACX,MAAM;AAAA,QACN,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,SAAS;AAAA,MACX;AAAA,IAAA;AAAA,EACF,GACC,CAAC,UAAU,IAAI,CAAC;AAMb,QAAA,oBAAoBH,yBAAY,OAAO,YAAY,MAAM,YAAY,MAAM,WAAW,MAAM;AAC5F,QAAA;AAEF,UAAI,CAAC,UAAU,WAAW,CAAC,WAAW;AAEpC,gBAAQ,MAAM,gEAAgE;AAAA,UAC5E;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QAAA,CACD;AAGD,qBAAa,CAAQ,SAAA;AACb,gBAAA,mBAAmB,KAAK,gBAAgB;AAC9C,gBAAM,kBAAkB,KAAK,gBAAgB,YAAY,IAAI;AACvD,gBAAA,cAAc,mBAAmB,IAAI,KAAK,MAAO,kBAAkB,mBAAoB,GAAG,IAAI;AAE7F,iBAAA;AAAA,YACL,GAAG;AAAA,YACH,eAAe;AAAA,YACf,cAAc;AAAA,YACd,UAAU;AAAA,YACV,OAAO,KAAK,SAAS,YAAY,KAAK;AAAA,UACxC;AAAA,QAAA,CACD;AAEM,eAAA;AAAA,UACL,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MAAA;AAGF,YAAM,SAAS;AAAA,QACb,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAClC;AAAA,QACA,OAAO,UAAU;AAAA,MACnB;AAEA,YAAM,SAAS,MAAM,UAAU,QAAQ,iBAAiB,WAAW,MAAM;AAEzE,UAAI,OAAO,SAAS;AAElB,qBAAa,CAAQ,SAAA;AACb,gBAAA,mBAAmB,KAAK,gBAAgB;AAC9C,gBAAM,kBAAkB,KAAK,gBAAgB,YAAY,IAAI;AACvD,gBAAA,cAAc,mBAAmB,IAAI,KAAK,MAAO,kBAAkB,mBAAoB,GAAG,IAAI;AAE7F,iBAAA;AAAA,YACL,GAAG;AAAA,YACH,eAAe;AAAA,YACf,cAAc;AAAA,YACd,UAAU;AAAA,YACV,OAAO,KAAK,SAAS,YAAY,KAAK;AAAA,UACxC;AAAA,QAAA,CACD;AAGD,0BAAkB,CAAS,UAAA;AAAA,UACzB,GAAG;AAAA,UACH,YAAY;AAAA,UACZ,oBAAoB,KAAK,qBAAqB,KAAK;AAAA,UACnD,qBAAqB,OAAO,kBAAkB,YAAY;AAAA,UAC1D,uBAAuB,OAAO,aAAa,aAAa;AAAA,QAAA,EACxD;AAEM,gBAAA,IAAI,4BAA4B,UAAU,IAAI,OAAO,UAAU,YAAY,MAAM,IAAI;AACtF,eAAA;AAAA,MAAA;AAGT,YAAM,IAAI,MAAM,OAAO,SAAS,8BAA8B;AAAA,aAEvD,OAAO;AACN,cAAA,MAAM,mCAAmC,KAAK;AACtD,aAAO,EAAE,SAAS,OAAO,OAAO,MAAM,QAAQ;AAAA,IAAA;AAAA,KAE/C,CAAC,WAAW,UAAU,UAAU,KAAK,CAAC;AAMzC,QAAM,oBAAoBA,aAAAA,YAAY,OAAO,YAAY,CAAA,MAAO;AAC1D,QAAA;AAEE,UAAA,CAAC,aAAa,CAAC,iBAAiB;AAClC,gBAAQ,IAAI,+DAA+D;AAC3E,eAAO,EAAE,SAAS,MAAM,SAAS,iDAAiD;AAAA,MAAA;AAIhF,UAAA,CAAC,UAAU,SAAS;AACtB,gBAAQ,IAAI,sDAAsD;AAClE,2BAAmB,KAAK;AACxB,0BAAkB,CAAS,UAAA;AAAA,UACzB,GAAG;AAAA,UACH,UAAS,oBAAI,KAAK,GAAE,YAAY;AAAA,UAChC;AAAA,QAAA,EACA;AACF,eAAO,EAAE,SAAS,MAAM,MAAM,SAAS,SAAS,4BAA4B;AAAA,MAAA;AAG9E,YAAM,SAAS,MAAM,UAAU,QAAQ,gBAAgB,WAAW,SAAS;AAE3E,UAAI,OAAO,SAAS;AAClB,2BAAmB,KAAK;AACxB,0BAAkB,CAAS,UAAA;AAAA,UACzB,GAAG;AAAA,UACH,UAAS,oBAAI,KAAK,GAAE,YAAY;AAAA,UAChC,aAAa,OAAO;AAAA,UACpB,qBAAqB,OAAO,QAAQ;AAAA,UACpC,oBAAoB,OAAO,QAAQ;AAAA,UACnC,aAAa,OAAO,QAAQ;AAAA,UAC5B;AAAA,QAAA,EACA;AAEM,gBAAA,IAAI,wBAAwB,SAAS,KAAK;AAAA,UAChD,cAAc,CAAC,CAAC,OAAO,QAAQ;AAAA,UAC/B,aAAa,CAAC,CAAC,OAAO,QAAQ;AAAA,UAC9B,YAAY,CAAC,CAAC,OAAO,QAAQ;AAAA,QAAA,CAC9B;AAEM,eAAA;AAAA,MAAA;AAGT,YAAM,IAAI,MAAM,OAAO,SAAS,4BAA4B;AAAA,aAErD,OAAO;AACN,cAAA,KAAK,6DAA6D,MAAM,OAAO;AAGvF,yBAAmB,KAAK;AAEjB,aAAA;AAAA,QACL,SAAS;AAAA,QACT,OAAO,MAAM;AAAA,QACb,iBAAiB;AAAA,MACnB;AAAA,IAAA;AAAA,EACF,GACC,CAAC,WAAW,eAAe,CAAC;AAKzB,QAAA,eAAeA,aAAAA,YAAY,MAAM;AACrC,iBAAa,IAAI;AACjB,uBAAmB,KAAK;AACxB,sBAAkB,CAAA,CAAE;AACP,iBAAA;AAAA,MACX,OAAO;AAAA,MACP,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,MACd,eAAe;AAAA,MACf,YAAY;AAAA,IAAA,CACb;AAEO,YAAA,IAAI,wBAAwB,QAAQ;AAAA,EAAA,GAC3C,CAAC,QAAQ,CAAC;AAKP,QAAA,mBAAmBA,aAAAA,YAAY,MAAM;AACzC,QAAI,CAAC,UAAU,WAAW,CAAC,WAAW;AAC7B,aAAA,EAAE,QAAQ,MAAM;AAAA,IAAA;AAGlB,WAAA;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA,cAAc,UAAU,QAAQ,iBAAiB,SAAS;AAAA,IAC5D;AAAA,EAAA,GACC,CAAC,WAAW,iBAAiB,gBAAgB,WAAW,QAAQ,CAAC;AAK9D,QAAA,oBAAoBA,yBAAY,OAAO,SAAS;AAEpD,UAAM,kBAAkB,sBAAsB;AAAA,MAC5C;AAAA,MACA,eAAe;AAAA,MACf;AAAA,IAAA,CACD;AAEG,QAAA,cAAc,qBAAqB,QAAQ;AACvC,YAAA,YAAY,IAAI,yBAAyB,IAAI;AACnD,gBAAU,OAAO;AACjB,gBAAU,OAAO;AACjB,sBAAgB,MAAM,SAAS;AAAA,IAAA,OAC1B;AACL,YAAM,IAAI;AAAA,IAAA;AAAA,EACZ,GACC,CAAC,mBAAmB,UAAU,CAAC;AAE3B,SAAA;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA;AAAA,IAGA,aAAa,CAAC,CAAC,UAAU;AAAA,EAC3B;AACF;AC/ZO,MAAM,8BAA8B;AAAA,EACzC,YAAY,UAAU,IAAI;AACnB,SAAA,SAAS,iBAAiB,YAAY;AAAA,MACzC,aAAa;AAAA,MACb,UAAU,QAAQ,YAAY;AAAA,IAAA,CAC/B;AAED,SAAK,UAAU,QAAQ,WAAW,IAAI,oBAAoB;AACrD,SAAA,yBAAyB,QAAQ,0BAA0B;AAC3D,SAAA,oBAAoB,QAAQ,qBAAqB;AACtD,SAAK,cAAc;AAEnB,SAAK,eAAe;AACpB,SAAK,kBAAkB,CAAC;AACxB,SAAK,yBAAyB;AAEzB,SAAA,OAAO,KAAK,8CAA8C;AAAA,MAC7D,wBAAwB,KAAK;AAAA,MAC7B,mBAAmB,KAAK;AAAA,IAAA,CACzB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASH,MAAM,2BAA2B,aAAa,UAAU,IAAI;AAC1D,QAAI,CAAC,aAAa;AACX,WAAA,OAAO,KAAK,4CAA4C;AAC7D,aAAO,EAAE,SAAS,OAAO,OAAO,mCAAmC;AAAA,IAAA;AAGjE,QAAA;AACG,WAAA,OAAO,KAAK,yCAAyC;AAAA,QACxD,WAAW,YAAY;AAAA,QACvB,YAAY,YAAY,YAAY,UAAU;AAAA,QAC9C,UAAU,YAAY,YAAY;AAAA,MAAA,CACnC;AAGD,WAAK,cAAc;AAGb,YAAA,gBAAgB,KAAK,4BAA4B,WAAW;AAGlE,YAAM,aAAa,MAAM,KAAK,eAAe,eAAe,OAAO;AAGnE,WAAK,0BAAyB,oBAAI,KAAK,GAAE,YAAY;AAE9C,aAAA;AAAA,QACL,SAAS;AAAA,QACT,iBAAiB;AAAA,QACjB,WAAW,KAAK;AAAA,QAChB,WAAW,YAAY;AAAA,MACzB;AAAA,aACO,OAAO;AACT,WAAA,OAAO,MAAM,+CAA+C,KAAK;AAC/D,aAAA;AAAA,QACL,SAAS;AAAA,QACT,OAAO,MAAM,WAAW;AAAA,QACxB,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MACpC;AAAA,IAAA;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASF,4BAA4B,SAAS;AAE7B,UAAA,EAAE,WAAW,QAAQ,UAAU,aAAa,CAAI,GAAA,YAAY,WAAW,QAAA,IAAY;AAGzF,UAAM,oBAAoB,KAAK,mBAAmB,YAAY,eAAe;AAC7E,UAAM,gBAAgB,KAAK,mBAAmB,YAAY,WAAW;AACrE,UAAM,YAAY,KAAK,mBAAmB,YAAY,OAAO;AAC7D,UAAM,kBAAkB,KAAK,mBAAmB,YAAY,aAAa;AAGnE,UAAA,oBAAoB,KAAK,4BAA4B,UAAU;AAG9D,WAAA;AAAA,MACL,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA,YAAY,YAAY,cAAc;AAAA,QACtC,UAAU,YAAY,YAAY;AAAA,QAClC;AAAA,QACA;AAAA,QACA,UAAU,WAAW,YAAY,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,SAAS,IAAI;AAAA,QAC3E,YAAY,WAAW;AAAA,MACzB;AAAA,MACA,eAAe;AAAA,QACb,eAAe;AAAA,UACb,WAAW,kBAAkB,SAAS;AAAA,UACtC,YAAY,kBAAkB;AAAA,UAC9B,MAAM;AAAA,QACR;AAAA,QACA,WAAW;AAAA,UACT,WAAW,cAAc,SAAS;AAAA,UAClC,YAAY,cAAc;AAAA,UAC1B,MAAM;AAAA,QACR;AAAA,QACA,OAAO;AAAA,UACL,WAAW,UAAU,SAAS;AAAA,UAC9B,YAAY,UAAU;AAAA,UACtB,MAAM;AAAA,QACR;AAAA,QACA,aAAa;AAAA,UACX,WAAW,gBAAgB,SAAS;AAAA,UACpC,YAAY,gBAAgB;AAAA,UAC5B,MAAM;AAAA,QAAA;AAAA,MAEV;AAAA,MACA;AAAA,IACF;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUF,mBAAmB,YAAY,YAAY;AAClC,WAAA,WAAW,OAAO,CAAQ,SAAA,KAAK,SAAS,UAAU,EACtD,IAAI,CAAQ,SAAA;AAEX,YAAM,EAAE,MAAM,WAAW,GAAG,OAAW,IAAA;AAChC,aAAA,EAAE,WAAW,GAAG,OAAO;AAAA,IAAA,CAC/B;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASL,4BAA4B,YAAY;AAEtC,UAAM,aAAa,WAAW,OAAO,CAAC,KAAK,SAAS;AAC9C,UAAA,CAAC,IAAI,KAAK,IAAI,EAAO,KAAA,KAAK,IAAI,IAAI,CAAC;AACvC,UAAI,KAAK,IAAI,EAAE,KAAK,IAAI;AACjB,aAAA;AAAA,IACT,GAAG,EAAE;AAGC,UAAA,oBAAoB,WAAW,iBAAiB,CAAC;AACjD,UAAA,kBAAkB,kBAAkB,OAAO,CAAQ,SAAA;AACvD,YAAM,YAAY,KAAK;AAAA,QACrB,KAAK,IAAI,KAAK,KAAK,GAAG,CAAC,IACvB,KAAK,IAAI,KAAK,KAAK,GAAG,CAAC,IACvB,KAAK,IAAI,KAAK,KAAK,GAAG,CAAC;AAAA,MACzB;AACA,aAAO,YAAY;AAAA,IACpB,CAAA,EAAE;AAGG,UAAA,YAAY,WAAW,SAAS,CAAC;AACjC,UAAA,gBAAgB,KAAK,sBAAsB,SAAS;AAGpD,UAAA,gBAAgB,WAAW,aAAa,CAAC;AACzC,UAAA,kBAAkB,KAAK,0BAA0B,aAAa;AAE7D,WAAA;AAAA,MACL,gBAAgB;AAAA,QACd;AAAA,QACA,0BAA0B,kBAAkB,SAAS,IAClD,mBAAmB,kBAAkB,SAAS,MAAO;AAAA,QACxD,iBAAiB,kBAAkB;AAAA;AAAA,MACrC;AAAA,MACA,iBAAiB;AAAA,QACf,gBAAgB,UAAU;AAAA,QAC1B,gBAAgB,cAAc,YAAY;AAAA;AAAA,QAC1C,kBAAkB,cAAc,cAAc;AAAA;AAAA,MAAA;AAAA,IAElD;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASF,sBAAsB,WAAW;AAExB,WAAA;AAAA,MACL,WAAW,UAAU,SAAS,KAAK,OAAO;AAAA,MAC1C,aAAa,UAAU,SAAS,KAAK,MAAM;AAAA,IAC7C;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASF,0BAA0B,eAAe;AACnC,QAAA,cAAc,SAAS,EAAU,QAAA;AAGrC,UAAM,iBAAiB,cAAc,OAAO,CAAC,KAAK,SAAS;AACzD,YAAM,YAAY,KAAK;AAAA,QACrB,KAAK,IAAI,KAAK,SAAS,GAAG,CAAC,IAC3B,KAAK,IAAI,KAAK,QAAQ,GAAG,CAAC,IAC1B,KAAK,IAAI,KAAK,SAAS,GAAG,CAAC;AAAA,MAC7B;AACA,aAAO,MAAM;AAAA,OACZ,CAAC;AAEE,UAAA,iBAAiB,iBAAiB,cAAc;AAC/C,WAAA,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,IAAK,iBAAiB,EAAG,CAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU3D,MAAM,eAAe,eAAe,UAAU,IAAI;AAC5C,QAAA;AACG,WAAA,OAAO,KAAK,gCAAgC;AAAA,QAC/C,WAAW,cAAc,SAAS;AAAA,QAClC,YAAY,cAAc,SAAS;AAAA,MAAA,CACpC;AAGD,YAAM,UAAU;AAAA,QACd,cAAc;AAAA,QACd,MAAM;AAAA,QACN,SAAS;AAAA,UACP,yBAAyB,QAAQ,4BAA4B;AAAA,UAC7D,wBAAwB,QAAQ,0BAA0B;AAAA,UAC1D,UAAU,QAAQ,YAAY;AAAA,UAC9B,GAAG;AAAA,QAAA;AAAA,MAEP;AAGA,YAAM,WAAW,MAAM,KAAK,QAAQ,YAAY,OAAO;AAElD,WAAA,OAAO,KAAK,iCAAiC;AAAA,QAChD,WAAW,cAAc,SAAS;AAAA,QAClC,YAAY,SAAS,cAAc;AAAA,MAAA,CACpC;AAEM,aAAA;AAAA,aACA,OAAO;AACT,WAAA,OAAO,MAAM,sCAAsC,KAAK;AAC7D,YAAM,IAAI,MAAM,sCAAsC,MAAM,OAAO,EAAE;AAAA,IAAA;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA,EAOF,kBAAkB;AAChB,QAAI,CAAC,KAAK,eAAe,CAAC,KAAK,wBAAwB;AAC9C,aAAA;AAAA,IAAA;AAGF,WAAA;AAAA,MACL,WAAW,KAAK,YAAY;AAAA,MAC5B,QAAQ,KAAK,YAAY;AAAA,MACzB,UAAU,KAAK,YAAY;AAAA,MAC3B,aAAa,KAAK;AAAA;AAAA,MAElB,iBAAiB;AAAA,MACjB,YAAY;AAAA,IACd;AAAA,EAAA;AAEJ;AChSA,eAAe,iBAAiB,aAAa;AACvC,MAAA;AAEI,UAAA,aAAa,wBAAwB,YAAY,SAAS;AAChE,UAAM,cAAc;AAAA,MAClB,GAAG;AAAA,MACH,UAAU,KAAK,IAAI;AAAA,MACnB,SAAS;AAAA,IACX;AAEA,iBAAa,QAAQ,YAAY,KAAK,UAAU,WAAW,CAAC;AAGtD,UAAA,UAAU,OAAO,KAAK,YAAY,EAAE,OAAO,CAAO,QAAA,IAAI,WAAW,uBAAuB,CAAC;AAC3F,QAAA,QAAQ,SAAS,IAAI;AACvB,YAAM,aAAa,QAAQ,KAAK,CAAC,GAAG,MAAM;AACxC,cAAM,QAAQ,KAAK,MAAM,aAAa,QAAQ,CAAC,KAAK,IAAI;AACxD,cAAM,QAAQ,KAAK,MAAM,aAAa,QAAQ,CAAC,KAAK,IAAI;AACxD,gBAAQ,MAAM,YAAY,MAAM,MAAM,YAAY;AAAA,MAAA,CACnD;AAGD,YAAM,eAAe,WAAW,MAAM,GAAG,QAAQ,SAAS,EAAE;AAC5D,mBAAa,QAAQ,CAAA,QAAO,aAAa,WAAW,GAAG,CAAC;AAAA,IAAA;AAItD,QAAA;AACI,YAAA,WAAW,MAAM,MAAM,8BAA8B;AAAA,QACzD,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,WAAW;AAAA,MAAA,CACjC;AAEG,UAAA,CAAC,SAAS,IAAI;AAChB,gBAAQ,KAAK,6CAA6C;AAAA,MAAA;AAAA,aAErD,UAAU;AACT,cAAA,KAAK,+CAA+C,SAAS,OAAO;AAAA,IAAA;AAGvE,WAAA;AAAA,WACA,OAAO;AACN,YAAA,MAAM,iCAAiC,KAAK;AAC7C,WAAA;AAAA,EAAA;AAEX;AAaO,SAAS,2BAA2B,UAAU,YAAY,UAAU,CAAA,GAAI;AAE7E,QAAM,CAAC,MAAM,IAAIF,aAAS,SAAA,MAAM,iBAAiB,YAAY;AAAA,IAC3D,aAAa;AAAA,IACb,UAAU,QAAQ,YAAY;AAAA,EAAA,CAC/B,CAAC;AAEI,QAAA,CAAC,qBAAqB,IAAIA,aAAAA,SAAS,MAAM,IAAI,6BAA6B,QAAQ,CAAC;AACzF,QAAM,CAAC,iBAAiB,IAAIA,sBAAS,MAAM,IAAI,8BAA8B;AAAA,IAC3E,wBAAwB,QAAQ,0BAA0B;AAAA,IAC1D,mBAAmB,QAAQ,qBAAqB;AAAA,IAChD,UAAU,QAAQ,YAAY;AAAA,EAAA,CAC/B,CAAC;AACF,QAAM,CAAC,eAAe,gBAAgB,IAAIA,aAAAA,SAAS,KAAK;AACxD,QAAM,CAAC,gBAAgB,iBAAiB,IAAIA,aAAAA,SAAS,IAAI;AACzD,QAAM,CAAC,kBAAkB,mBAAmB,IAAIA,aAAAA,SAAS,IAAI;AAC7D,QAAM,CAAC,eAAe,gBAAgB,IAAIA,aAAAA,SAAS,IAAI;AACvD,QAAM,CAAC,mBAAmB,oBAAoB,IAAIA,aAAAA,SAAS,IAAI;AAC/D,QAAM,CAAC,WAAW,YAAY,IAAIA,aAAAA,SAAS,KAAK;AAChD,QAAM,CAAC,OAAO,QAAQ,IAAIA,aAAAA,SAAS,IAAI;AAEvC,QAAM,gBAAgBI,aAAAA,OAAO,KAAK,IAAA,CAAK;AACjC,QAAA,oBAAoBA,oBAAO,IAAI;AAC/B,QAAA,eAAeA,oBAAO,IAAI;AAGhC,QAAM,iBAAiB;AAAA,IACrB,YAAY;AAAA,IACZ,gBAAgB;AAAA;AAAA,IAChB,uBAAuB;AAAA,IACvB,UAAU;AAAA,IACV,oBAAoB;AAAA,IACpB,kBAAkB;AAAA;AAAA,IAClB,GAAG;AAAA,EACL;AAKA,QAAM,oBAAoBF,aAAY,YAAA,OAAO,WAAW,gBAAgB,CAAA,MAAO;AACzE,QAAA;AACF,mBAAa,IAAI;AACjB,eAAS,IAAI;AAGb,YAAM,iBAAiB,aAAa,WAAW,KAAK,IAAK,CAAA,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,GAAG,CAAC,CAAC;AACpG,mBAAa,UAAU;AAGjB,YAAA,aAAa,MAAM,sBAAsB;AAAA,QAC7C;AAAA,QACA,cAAc,UAAU;AAAA,QACxB;AAAA,UACE;AAAA,UACA,oBAAoB,eAAe;AAAA,UACnC,kBAAkB,eAAe;AAAA,UACjC,GAAG;AAAA,QAAA;AAAA,MAEP;AAEI,UAAA,CAAC,WAAW,SAAS;AACvB,cAAM,IAAI,MAAM,WAAW,SAAS,6CAA6C;AAAA,MAAA;AAIjE,wBAAA;AAAA,QAChB,WAAW;AAAA,QACX;AAAA,QACA,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAClC,GAAG;AAAA,MAAA,CACJ;AAED,uBAAiB,IAAI;AAGrB,UAAI,eAAe,YAAY;AACb,wBAAA;AAAA,MAAA;AAGlB,aAAO,KAAK,mDAAmD;AAAA,QAC7D,MAAM;AAAA,QACN,WAAW;AAAA,QACX;AAAA,QACA,YAAY,eAAe;AAAA,QAC3B,cAAc,eAAe;AAAA,QAC7B,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAAA,CACnC;AAED,aAAO,EAAE,SAAS,MAAM,WAAW,eAAe;AAAA,aAE3C,KAAK;AACZ,eAAS,GAAG;AACZ,aAAO,MAAM,gDAAgD;AAAA,QAC3D,MAAM;AAAA,QACN,OAAO,IAAI;AAAA,QACX;AAAA,QACA;AAAA,MAAA,CACD;AACD,aAAO,EAAE,SAAS,OAAO,OAAO,IAAI,QAAQ;AAAA,IAAA,UAC5C;AACA,mBAAa,KAAK;AAAA,IAAA;AAAA,KAEnB,CAAC,UAAU,uBAAuB,gBAAgB,MAAM,CAAC;AAK5D,QAAM,oBAAoBA,aAAY,YAAA,OAAO,QAAQ,OAAO,CAAA,MAAO;AACjE,QAAI,CAAC,iBAAiB,CAAC,aAAa,SAAS;AAC3C,aAAO,KAAK,8CAA8C;AAAA,QACxD,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MAAA,CACD;AACD,aAAO,EAAE,SAAS,OAAO,OAAO,0BAA0B;AAAA,IAAA;AAGxD,QAAA;AAEI,YAAA,iBAAiB,MAAM,sBAAsB,kBAAkB;AAGrE,YAAM,oBAAoB;AAAA,QACxB,QAAQ;AAAA,QACR,WAAW,aAAa;AAAA,QACxB,QAAQ,gBAAgB,UAAU;AAAA,QAClC;AAAA,QACA,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAClC,GAAG;AAAA,MACL;AAGA,UAAI,SAAS,EAAE,SAAS,MAAM,MAAM,kBAAkB;AAElD,UAAA,kBAAkB,eAAe,eAAe;AAC5CI,cAAAA,oBAAmB,CAAC,cAAc;AAClC,cAAA,kBAAkB,MAAM,sBAAsB;AAAA,UAClD;AAAA,UACAA;AAAAA,QACF;AAEI,YAAA,CAAC,gBAAgB,OAAO;AAC1B,iBAAO,OAAO;AAAA,YACZ,GAAG;AAAA,YACH,sBAAsB;AAAA,UACxB;AAAA,QAAA;AAAA,MACF;AAGY,oBAAA,UAAU,KAAK,IAAI;AAG7B,UAAA,CAAC,eAAe,YAAY;AAC9B,cAAM,uBAAuB;AAAA,MAAA;AAG/B,aAAO,MAAM,2CAA2C;AAAA,QACtD;AAAA,QACA,WAAW,aAAa;AAAA,QACxB,SAAS,OAAO;AAAA,QAChB,qBAAqB,CAAC,CAAC;AAAA,MAAA,CACxB;AAEM,aAAA;AAAA,aAEA,KAAK;AACZ,aAAO,MAAM,iDAAiD;AAAA,QAC5D,MAAM;AAAA,QACN,OAAO,IAAI;AAAA,QACX;AAAA,QACA;AAAA,MAAA,CACD;AACD,eAAS,GAAG;AACZ,aAAO,EAAE,SAAS,OAAO,OAAO,IAAI,QAAQ;AAAA,IAAA;AAAA,EAC9C,GACC,CAAC,eAAe,UAAU,uBAAuB,gBAAgB,gBAAgB,MAAM,CAAC;AAKrF,QAAA,yBAAyBJ,aAAAA,YAAY,YAAY;AACrD,QAAI,CAAC,iBAAiB,CAAC,aAAa,QAAS;AAEzC,QAAA;AACI,YAAA,cAAc,MAAM,sBAAsB,kBAAkB;AAClE,UAAI,aAAa;AACf,4BAAoB,WAAW;AAAA,MAAA;AAAA,aAE1B,KAAK;AACZ,aAAO,MAAM,8CAA8C;AAAA,QACzD,MAAM;AAAA,QACN,OAAO,IAAI;AAAA,QACX;AAAA,MAAA,CACD;AAAA,IAAA;AAAA,KAEF,CAAC,eAAe,uBAAuB,UAAU,MAAM,CAAC;AAKrD,QAAA,kBAAkBA,aAAAA,YAAY,MAAM;AACxC,QAAI,kBAAkB,SAAS;AAC7B,oBAAc,kBAAkB,OAAO;AAAA,IAAA;AAGvB,sBAAA,UAAU,YAAY,MAAM;AACrB,6BAAA;AAAA,IAAA,GACtB,eAAe,cAAc;AAAA,EAC/B,GAAA,CAAC,wBAAwB,eAAe,cAAc,CAAC;AAKpD,QAAA,iBAAiBA,aAAAA,YAAY,MAAM;AACvC,QAAI,kBAAkB,SAAS;AAC7B,oBAAc,kBAAkB,OAAO;AACvC,wBAAkB,UAAU;AAAA,IAAA;AAAA,EAEhC,GAAG,EAAE;AAKC,QAAA,kBAAkBA,aAAAA,YAAY,YAAY;AAC9C,QAAI,CAAC,iBAAiB,CAAC,eAAe,yBAAyB,CAAC,aAAa,SAAS;AAC7E,aAAA;AAAA,IAAA;AAGL,QAAA;AAEI,YAAA,iBAAiB,MAAM,sBAAsB,kBAAkB;AACjE,UAAA,kBAAkB,eAAe,yBAAyB;AAC5D,eAAO,eAAe;AAAA,MAAA;AAEjB,aAAA;AAAA,aACA,KAAK;AACZ,aAAO,MAAM,8BAA8B;AAAA,QACzC,MAAM;AAAA,QACN,OAAO,IAAI;AAAA,QACX;AAAA,MAAA,CACD;AACM,aAAA;AAAA,IAAA;AAAA,EACT,GACC,CAAC,eAAe,uBAAuB,eAAe,uBAAuB,UAAU,MAAM,CAAC;AAKjG,QAAM,kBAAkBA,aAAAA,YAAY,OAAO,cAAc,CAAA,MAAO;AAC9D,QAAI,CAAC,iBAAiB,CAAC,aAAa,SAAS;AAC3C,aAAO,KAAK,0CAA0C;AAAA,QACpD,MAAM;AAAA,QACN;AAAA,MAAA,CACD;AACM,aAAA;AAAA,IAAA;AAGL,QAAA;AACF,mBAAa,IAAI;AACF,qBAAA;AAGT,YAAA,SAAS,MAAM,sBAAsB,sBAAsB;AAEjE,UAAI,OAAO,SAAS;AAClB,yBAAiB,OAAO,MAAM;AAG9B,eAAO,KAAK,kDAAkD;AAC9D,cAAM,WAAW,MAAM,kBAAkB,2BAA2B,OAAO,QAAQ;AAAA,UACjF,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAAA,CACzB;AAED,YAAI,SAAS,SAAS;AACpB,+BAAqB,SAAS,eAAe;AAC7C,iBAAO,KAAK,gCAAgC;AAAA,YAC1C,YAAY,SAAS,iBAAiB,cAAc;AAAA,UAAA,CACrD;AAGG,cAAA;AACF,kBAAM,iBAAiB;AAAA,cACrB,WAAW,aAAa;AAAA,cACxB;AAAA,cACA,oBAAoB,OAAO;AAAA,cAC3B,YAAY,SAAS;AAAA,cACrB;AAAA,cACA,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,YAAA,CACnC;AACD,mBAAO,KAAK,4CAA4C;AAAA,mBACjD,cAAc;AACrB,mBAAO,KAAK,yCAAyC;AAAA,cACnD,OAAO,aAAa;AAAA,YAAA,CACrB;AAAA,UAAA;AAAA,QACH,OACK;AACL,iBAAO,KAAK,gCAAgC;AAAA,YAC1C,OAAO,SAAS;AAAA,UAAA,CACjB;AAAA,QAAA;AAAA,MACH;AAIF,wBAAkB,IAAI;AACtB,uBAAiB,KAAK;AACtB,0BAAoB,IAAI;AACxB,mBAAa,UAAU;AAEvB,aAAO,KAAK,gDAAgD;AAAA,QAC1D,MAAM;AAAA,QACN,WAAW,aAAa;AAAA,QACxB;AAAA,QACA,iBAAiB,OAAO;AAAA,QACxB,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAAA,CACnC;AAEM,aAAA,OAAO,UAAU,OAAO,SAAS;AAAA,aAEjC,KAAK;AACZ,eAAS,GAAG;AACZ,aAAO,MAAM,8CAA8C;AAAA,QACzD,MAAM;AAAA,QACN,OAAO,IAAI;AAAA,QACX,WAAW,aAAa;AAAA,QACxB;AAAA,MAAA,CACD;AACM,aAAA;AAAA,IAAA,UACP;AACA,mBAAa,KAAK;AAAA,IAAA;AAAA,EACpB,GACC,CAAC,eAAe,uBAAuB,UAAU,gBAAgB,MAAM,CAAC;AAKrE,QAAA,qBAAqBA,aAAAA,YAAY,YAAY;AACjD,QAAI,CAAC,oBAAoB,CAAC,aAAa,gBAAgB,CAAC;AAEpD,QAAA;AAEF,YAAM,uBAAuB,CAAC;AAG9B,UAAI,iBAAiB,eAAe;AAClC,cAAM,aAAa,iBAAiB;AAGpC,YAAI,WAAW,eAAe;AAC5B,gBAAM,WAAW,WAAW;AACxB,cAAA,SAAS,cAAc,KAAK;AAC9B,iCAAqB,KAAK,8CAA8C;AAAA,UAAA;AAEtE,cAAA,SAAS,eAAe,KAAK;AAC/B,iCAAqB,KAAK,wCAAwC;AAAA,UAAA;AAAA,QACpE;AAIF,YAAI,WAAW,WAAW;AACxB,gBAAM,cAAc,WAAW;AAC3B,cAAA,YAAY,YAAY,KAAK;AAC/B,iCAAqB,KAAK,4CAA4C;AAAA,UAAA;AAAA,QACxE;AAIF,YAAI,WAAW,OAAO;AACpB,gBAAM,QAAQ,WAAW;AACzB,cAAI,MAAM,YAAY,MAAM,SAAS,UAAU,KAAK;AAClD,iCAAqB,KAAK,wCAAwC;AAAA,UAAA;AAEhE,cAAA,MAAM,YAAY,KAAK;AACzB,iCAAqB,KAAK,oCAAoC;AAAA,UAAA;AAAA,QAChE;AAAA,MACF;AAIF,UAAI,iBAAiB,yBAAyB;AAC5C,cAAM,WAAW,iBAAiB;AAElC,YAAI,SAAS,sBAAsB,SAAS,mBAAmB,QAAQ,KAAK;AAC1E,+BAAqB,KAAK,gDAAgD;AAAA,QAAA;AAGxE,YAAA,SAAS,sBAAsB,KAAK;AACtC,+BAAqB,KAAK,0DAA0D;AAAA,QAAA;AAGlF,YAAA,SAAS,wBAAwB,KAAK;AACxC,+BAAqB,KAAK,sCAAsC;AAAA,QAAA;AAAA,MAClE;AAGK,aAAA;AAAA,aAEA,KAAK;AACZ,aAAO,MAAM,iCAAiC;AAAA,QAC5C,MAAM;AAAA,QACN,OAAO,IAAI;AAAA,QACX;AAAA,MAAA,CACD;AAGM,aAAA;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IAAA;AAAA,KAED,CAAC,kBAAkB,uBAAuB,UAAU,MAAM,CAAC;AAKxD,QAAA,gBAAgBA,aAAAA,YAAY,YAAY;AAC5C,QAAI,CAAC,iBAAiB,CAAC,aAAa,QAAgB,QAAA;AAEhD,QAAA;AACI,YAAA,iBAAiB,MAAM,sBAAsB,kBAAkB;AAC9D,aAAA,iBAAiB,eAAe,gBAAgB;AAAA,aAChD,KAAK;AACZ,aAAO,MAAM,sCAAsC;AAAA,QACjD,MAAM;AAAA,QACN,OAAO,IAAI;AAAA,QACX;AAAA,MAAA,CACD;AACM,aAAA;AAAA,IAAA;AAAA,KAER,CAAC,eAAe,uBAAuB,UAAU,MAAM,CAAC;AAKrD,QAAA,4BAA4BA,aAAAA,YAAY,YAAY;AACxD,QAAI,CAAC,iBAAiB,CAAC,aAAa,QAAgB,QAAA;AAEhD,QAAA;AACI,YAAA,iBAAiB,MAAM,sBAAsB,kBAAkB;AAC9D,aAAA,iBAAiB,eAAe,0BAA0B;AAAA,aAC1D,KAAK;AACZ,aAAO,MAAM,gDAAgD;AAAA,QAC3D,MAAM;AAAA,QACN,OAAO,IAAI;AAAA,QACX;AAAA,MAAA,CACD;AACM,aAAA;AAAA,IAAA;AAAA,KAER,CAAC,eAAe,uBAAuB,UAAU,MAAM,CAAC;AAK3DD,eAAAA,UAAU,MAAM;AACd,WAAO,MAAM;AACI,qBAAA;AACf,UAAI,yBAAyB,eAAe;AACpB,8BAAA,sBAAA,EAAwB,MAAM,CAAO,QAAA;AACzD,iBAAO,MAAM,sCAAsC,EAAE,OAAO,IAAI,SAAS;AAAA,QAAA,CAC1E;AAAA,MAAA;AAAA,IAEL;AAAA,KACC,CAAC,gBAAgB,uBAAuB,eAAe,MAAM,CAAC;AAG1D,SAAA;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA,kBAAkB,MAAM,kBAAkB,gBAAgB;AAAA;AAAA,IAG1D,WAAW,eAAe,aAAa,UAAU,wBAAwB;AAAA,IACzE,WAAW,aAAa;AAAA,EAC1B;AACF;AC5iBO,MAAM,6BAA6B,CAAC,UAAU,OAAO;AACpD,QAAA,gBAAgBE,wBAAW,aAAa;AAC9C,QAAM,mBAAmB,eAAe;AAGlC,QAAA,aAAa,QAAQ,UAAU,QAAQ,WAAW,eAAe,QAAQ,WAAW,KACtF,QAAQ,SACR;AAEJ,QAAM,CAAC,QAAQ,SAAS,IAAIH,aAAAA,SAAS,UAAU;AAC/C,QAAM,CAAC,SAAS,UAAU,IAAIA,aAAAA,SAAS,KAAK;AAC5C,QAAM,CAAC,OAAO,QAAQ,IAAIA,aAAAA,SAAS,IAAI;AACvC,QAAM,CAAC,iBAAiB,kBAAkB,IAAIA,aAAAA,SAAS,CAAA,CAAE;AACzD,QAAM,CAAC,iBAAiB,kBAAkB,IAAIA,aAAAA,SAAS,IAAI;AAC3D,QAAM,sBAAsBI,aAAAA,OAAO,KAAK,IAAA,CAAK;AAM7C,QAAM,iBAAiB;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AASA,QAAM,sBAAsBF,aAAAA,YAAY,CAAC,QAAQ,YAAY;AACvD,QAAA;AACI,YAAA,mBAAmB,gBAAgB,MAAM;AAE/C,UAAI,CAAC,eAAe,SAAS,gBAAgB,GAAG;AACxC,cAAA,IAAI,MAAM,uBAAuB,MAAM,kBAAkB,gBAAgB,wBAAwB,eAAe,KAAK,IAAI,CAAC,EAAE;AAAA,MAAA;AAGpI,YAAM,mBAAmB;AAAA,QACvB,GAAG;AAAA,QACH,QAAQ;AAAA,QACR,WAAW,QAAQ,aAAa,WAAW,KAAK,KAAK,IAAI,KAAK,OAAA,EAAS,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE,CAAC;AAAA,QACpG,SAAS,QAAQ,WAAW,UAAU,SAAS,KAAK,KAAK,IAAI,KAAK,OAAA,EAAS,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE,CAAC;AAAA,QACxG,iBAAiB,QAAQ,mBAAmB,yBAAyB,OAAO;AAAA,QAC5E,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAClC,UAAU;AAAA,MACZ;AAGA,cAAQ,kBAAkB;AAAA,QACxB,KAAK;AACH,cAAI,CAAC,QAAQ,YAAY,CAAC,QAAQ,SAAS;AACnC,kBAAA,IAAI,MAAM,kEAAkE;AAAA,UAAA;AAEpF;AAAA,QACF,KAAK;AACH,cAAI,CAAC,QAAQ,aAAa,CAAC,QAAQ,cAAc;AACzC,kBAAA,IAAI,MAAM,kEAAkE;AAAA,UAAA;AAEpF;AAAA,QACF,KAAK;AACH,cAAI,CAAC,QAAQ,kBAAkB,CAAC,QAAQ,sBAAsB;AACtD,kBAAA,IAAI,MAAM,oFAAoF;AAAA,UAAA;AAEtG;AAAA,QACF,KAAK;AACH,cAAI,CAAC,QAAQ,qBAAqB,CAAC,QAAQ,SAAS;AAC5C,kBAAA,IAAI,MAAM,yEAAyE;AAAA,UAAA;AAE3F;AAAA,QACF,KAAK;AACH,cAAI,CAAC,QAAQ,SAAS,CAAC,QAAQ,SAAS;AAChC,kBAAA,IAAI,MAAM,8DAA8D;AAAA,UAAA;AAEhF;AAAA,QACF,KAAK;AACH,cAAI,CAAC,QAAQ,gBAAgB,CAAC,QAAQ,mBAAmB;AACjD,kBAAA,IAAI,MAAM,+EAA+E;AAAA,UAAA;AAEjG;AAAA,QACF,KAAK;AACH,cAAI,CAAC,QAAQ,cAAc,CAAC,QAAQ,gBAAgB;AAC5C,kBAAA,IAAI,MAAM,2EAA2E;AAAA,UAAA;AAE7F;AAAA,QACF,KAAK;AACC,cAAA,CAAC,QAAQ,WAAW,CAAC,QAAQ,gBAAgB,CAAC,QAAQ,gBAAgB;AAClE,kBAAA,IAAI,MAAM,sFAAsF;AAAA,UAAA;AAExG;AAAA,QACF,KAAK;AACH,cAAI,CAAC,QAAQ,WAAW,CAAC,QAAQ,eAAe;AACxC,kBAAA,IAAI,MAAM,iEAAiE;AAAA,UAAA;AAEnF;AAAA,QACF,KAAK;AACH,cAAI,CAAC,QAAQ,WAAW,CAAC,QAAQ,aAAa;AACtC,kBAAA,IAAI,MAAM,sEAAsE;AAAA,UAAA;AAExF;AAAA,QACF,KAAK;AACH,cAAI,CAAC,QAAQ,eAAe,CAAC,QAAQ,gBAAgB;AAC7C,kBAAA,IAAI,MAAM,wEAAwE;AAAA,UAAA;AAE1F;AAAA,QACF;AACQ,gBAAA,IAAI,MAAM,wCAAwC;AAAA,MAAA;AAGrD,aAAA;AAAA,aACAK,QAAO;AACd,cAAQ,MAAM,oCAAoC,EAAE,QAAQ,OAAOA,OAAM,SAAS;AAC5EA,YAAAA;AAAAA,IAAA;AAAA,EACR,GACC,CAAC,QAAQ,cAAc,CAAC;AAMrB,QAAA,cAAcL,aAAAA,YAAY,MAAM;AAChC,QAAA,CAAC,kBAAkB,oBAAoB;AACnC,YAAA,IAAI,MAAM,yDAAyD;AAAA,IAAA;AAAA,EAC3E,GACC,CAAC,gBAAgB,CAAC;AAQf,QAAA,2BAA2B,CAAC,YAAY;AACxC,QAAA;AACF,UAAI,SAAS,kBAAkB;AACrB,gBAAA,KAAK,QAAQ,IAAI,KAAK,QAAQ,gBAAgB,EAAE,QAAA,KAAa;AAAA,MAAA;AAEvE,UAAI,oBAAoB,SAAS;AAC/B,gBAAQ,KAAK,IAAQ,IAAA,oBAAoB,WAAW;AAAA,MAAA;AAE/C,aAAA;AAAA,aACAK,QAAO;AACd,cAAQ,MAAM,yCAAyC,EAAE,OAAOA,OAAM,SAAS;AACxE,aAAA;AAAA,IAAA;AAAA,EAEX;AAMM,QAAA,iBAAiBL,yBAAY,CAAC,OAAO;AACrC,QAAA,MAAM,OAAO,OAAO,UAAU;AAChC,gBAAU,EAAE;AACZ,cAAQ,IAAI,uBAAuB,EAAE,QAAQ,IAAI;AAAA,IAAA,OAC5C;AACL,cAAQ,KAAK,8BAA8B,EAAE,GAAA,CAAI;AACjD,eAAS,wBAAwB;AAAA,IAAA;AAAA,EAErC,GAAG,EAAE;AASL,QAAM,qBAAqBA,aAAAA,YAAY,OAAO,QAAQ,SAAS,iBAAiB,SAAS;AACnF,QAAA;AACU,kBAAA;AACZ,iBAAW,IAAI;AACf,eAAS,IAAI;AAEb,YAAM,cAAc,kBAAkB;AACtC,UAAI,CAAC,aAAa;AACV,cAAA,IAAI,MAAM,gCAAgC;AAAA,MAAA;AAG5C,YAAA,mBAAmB,oBAAoB,QAAQ,OAAO;AAE5D,cAAQ,IAAI,4BAA4B;AAAA,QACtC;AAAA,QACA,WAAW,iBAAiB;AAAA,QAC5B,QAAQ,iBAAiB;AAAA,QACzB,iBAAiB,iBAAiB;AAAA,MAAA,CACnC;AAEK,YAAA,SAAS,MAAM,iBAAiB;AAAA,QACpC,iBAAiB;AAAA,QACjB;AAAA,QACA;AAAA,MACF;AAEA,UAAI,OAAO,SAAS;AACC,2BAAA,OAAO,mBAAmB,EAAE;AAC/C,2BAAmB,CAAS,UAAA;AAAA,UAC1B,GAAI,QAAQ,CAAC;AAAA,UACb,UAAU,OAAO,YAAY,CAAC;AAAA,UAC9B,kBAAkB,OAAO,oBAAoB,CAAC;AAAA,UAC9C,aAAY,oBAAI,KAAK,GAAE,YAAY;AAAA,QAAA,EACnC;AACF,gBAAQ,IAAI,uCAAuC,EAAE,OAAA,CAAQ;AAAA,MAAA,OACxD;AACL,cAAM,IAAI,MAAM,OAAO,SAAS,gDAAgD;AAAA,MAAA;AAG3E,aAAA;AAAA,QACL,SAAS;AAAA,QACT,MAAM;AAAA,QACN,iBAAiB,OAAO,mBAAmB,CAAC;AAAA,QAC5C,UAAU,OAAO,YAAY,CAAA;AAAA,MAC/B;AAAA,aACO,KAAK;AACZ,eAAS,IAAI,OAAO;AACpB,cAAQ,MAAM,yCAAyC,EAAE,QAAQ,OAAO,IAAI,SAAS;AAC9E,aAAA;AAAA,QACL,SAAS;AAAA,QACT,OAAO,IAAI;AAAA,QACX,iBAAiB,CAAC;AAAA,QAClB,UAAU,CAAA;AAAA,MACZ;AAAA,IAAA,UACA;AACA,iBAAW,KAAK;AAAA,IAAA;AAAA,KAEjB,CAAC,kBAAkB,QAAQ,aAAa,mBAAmB,CAAC;AAO/D,QAAM,qBAAqBA,aAAAA,YAAY,OAAO,iBAAiB,SAAS;AAClE,QAAA;AACU,kBAAA;AACZ,iBAAW,IAAI;AACf,eAAS,IAAI;AAEb,YAAM,cAAc,kBAAkB;AACtC,UAAI,CAAC,eAAe,gBAAgB,eAAe,gBAAgB,IAAI;AACrE,gBAAQ,IAAI,uDAAuD,EAAE,QAAQ,aAAa;AACnF,eAAA;AAAA,UACL,SAAS;AAAA,UACT,OAAO;AAAA,UACP,iBAAiB,CAAC;AAAA,UAClB,UAAU,CAAA;AAAA,QACZ;AAAA,MAAA;AAGF,YAAM,SAAS,MAAM,iBAAiB,8BAA8B,WAAW;AAE3E,UAAA,OAAO,WAAW,OAAO,SAAS;AACjB,2BAAA,OAAO,mBAAmB,EAAE;AAC/C,2BAAmB,CAAS,UAAA;AAAA,UAC1B,GAAI,QAAQ,CAAC;AAAA,UACb,UAAU,OAAO,YAAY,CAAC;AAAA,UAC9B,aAAa,OAAO,eAAe;AAAA,UACnC,aAAY,oBAAI,KAAK,GAAE,YAAY;AAAA,QAAA,EACnC;AACF,gBAAQ,IAAI,wCAAwC,EAAE,QAAQ,aAAa;AACpE,eAAA;AAAA,UACL,SAAS;AAAA,UACT,iBAAiB,OAAO,mBAAmB,CAAC;AAAA,UAC5C,UAAU,OAAO,YAAY,CAAC;AAAA,UAC9B,aAAa,OAAO,eAAe;AAAA,QACrC;AAAA,MAAA,OACK;AACL,cAAM,IAAI,MAAM,OAAO,SAAS,iCAAiC;AAAA,MAAA;AAAA,aAE5D,KAAK;AACZ,eAAS,IAAI,OAAO;AACZ,cAAA,MAAM,+CAA+C,EAAE,QAAQ,kBAAkB,QAAQ,OAAO,IAAI,SAAS;AAC9G,aAAA;AAAA,QACL,SAAS;AAAA,QACT,OAAO,IAAI;AAAA,QACX,iBAAiB,CAAC;AAAA,QAClB,UAAU,CAAA;AAAA,MACZ;AAAA,IAAA,UACA;AACA,iBAAW,KAAK;AAAA,IAAA;AAAA,EAEjB,GAAA,CAAC,kBAAkB,QAAQ,WAAW,CAAC;AAOpC,QAAA,4BAA4BA,yBAAY,OAAO,uBAAuB;AACtE,QAAA;AACU,kBAAA;AACZ,iBAAW,IAAI;AACf,eAAS,IAAI;AAEb,UAAI,CAAC,QAAQ;AACL,cAAA,IAAI,MAAM,gCAAgC;AAAA,MAAA;AAIlD,UAAI,CAAC,sBAAsB,OAAO,uBAAuB,UAAU;AAC3D,cAAA,IAAI,MAAM,kCAAkC;AAAA,MAAA;AAGpD,YAAM,UAAU;AAAA,QACd,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAClC,YAAY;AAAA,QACZ,MAAM;AAAA,UACJ,QAAQ;AAAA,UACR,SAAS;AAAA,QAAA;AAAA,MAEb;AAEA,YAAM,SAAS,MAAM,iBAAiB,cAAc,2CAA2C;AAAA,QAC7F;AAAA,QACA;AAAA,MAAA,CACD;AAED,UAAI,OAAO,SAAS;AAClB,2BAAmB,CAAS,UAAA;AAAA,UAC1B,GAAI,QAAQ,CAAC;AAAA,UACb,qBAAqB,OAAO,YAAY,CAAC;AAAA,UACzC,aAAY,oBAAI,KAAK,GAAE,YAAY;AAAA,QAAA,EACnC;AACF,gBAAQ,IAAI,yCAAyC,EAAE,OAAA,CAAQ;AAAA,MAAA;AAG1D,aAAA;AAAA,QACL,SAAS,OAAO;AAAA,QAChB,UAAU,OAAO,YAAY,CAAC;AAAA,QAC9B,OAAO,OAAO,SAAS;AAAA,MACzB;AAAA,aACO,KAAK;AACZ,eAAS,IAAI,OAAO;AACpB,cAAQ,MAAM,+CAA+C,EAAE,QAAQ,OAAO,IAAI,SAAS;AACpF,aAAA;AAAA,QACL,SAAS;AAAA,QACT,OAAO,IAAI;AAAA,QACX,UAAU,CAAA;AAAA,MACZ;AAAA,IAAA,UACA;AACA,iBAAW,KAAK;AAAA,IAAA;AAAA,EAEjB,GAAA,CAAC,kBAAkB,QAAQ,WAAW,CAAC;AAMpC,QAAA,uBAAuBA,aAAAA,YAAY,YAAY;AAC/C,QAAA;AACU,kBAAA;AACN,YAAA,QAAQ,MAAM,iBAAiB,qBAAqB;AAClD,cAAA,IAAI,4CAA4C,KAAK;AACtD,aAAA;AAAA,QACL,SAAS;AAAA,QACT;AAAA,MACF;AAAA,aACO,KAAK;AACZ,cAAQ,MAAM,iDAAiD,EAAE,OAAO,IAAI,SAAS;AAC9E,aAAA;AAAA,QACL,SAAS;AAAA,QACT,OAAO,IAAI;AAAA,QACX,OAAO,CAAA;AAAA,MACT;AAAA,IAAA;AAAA,EACF,GACC,CAAC,kBAAkB,WAAW,CAAC;AAO5B,QAAA,kBAAkBA,yBAAY,CAAC,WAAW;AACxC,UAAA,YAAY,eAAe,SAAS,MAAM;AAChD,QAAI,CAAC,WAAW;AACd,cAAQ,KAAK,0BAA0B,EAAE,QAAQ,gBAAgB;AAAA,IAAA;AAE5D,WAAA;AAAA,EAAA,GACN,CAAC,cAAc,CAAC;AAQb,QAAA,kBAAkBA,yBAAY,CAAC,WAAW;AAC9C,QAAI,CAAC,UAAU,WAAW,YAAoB,QAAA;AAG9C,UAAM,cAAc,OAAO,QAAQ,SAAS,EAAE;AAG9C,UAAM,cAAc;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,qBAAqB;AAAA,MACrB,qBAAqB;AAAA,MACrB,qBAAqB;AAAA,MACrB,sBAAsB;AAAA,MACtB,iBAAiB;AAAA,IACnB;AAEO,WAAA,YAAY,WAAW,KAAK;AAAA,EACrC,GAAG,EAAE;AAKC,QAAA,aAAaA,aAAAA,YAAY,MAAM;AACnC,cAAU,IAAI;AACd,aAAS,IAAI;AACb,uBAAmB,CAAA,CAAE;AACrB,uBAAmB,IAAI;AACH,wBAAA,UAAU,KAAK,IAAI;AACvC,YAAQ,IAAI,yBAAyB;AAAA,EACvC,GAAG,EAAE;AAGLD,eAAAA,UAAU,MAAM;AACd,QAAI,UAAU,WAAW,eAAe,WAAW,MAAM,kBAAkB,oBAAoB;AAC1E,yBAAA,EAAE,MAAM,CAAO,QAAA;AAChC,gBAAQ,KAAK,wDAAwD,EAAE,OAAO,IAAI,SAAS;AAAA,MAAA,CAC5F;AAAA,IAAA;AAAA,EAEF,GAAA,CAAC,QAAQ,kBAAkB,kBAAkB,CAAC;AAGjDA,eAAAA,UAAU,MAAM;AACd,YAAQ,IAAI,oDAAoD;AAAA,MAC9D;AAAA,MACA,WAAW,CAAC,CAAC,kBAAkB;AAAA,MAC/B;AAAA,IAAA,CACD;AAAA,EACH,GAAG,EAAE;AAEE,SAAA;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA,WAAW,CAAC,CAAC,kBAAkB;AAAA,EACjC;AACF;"}