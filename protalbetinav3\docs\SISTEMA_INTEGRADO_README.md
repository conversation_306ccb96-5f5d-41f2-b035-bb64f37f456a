# Sistema Integrado Portal Betina V3

## Visão Geral

O Sistema Integrado do Portal Betina V3 é uma arquitetura robusta e resiliente para gerenciamento de jogos terapêuticos e análise de métricas cognitivas. Foi projetado com foco em resiliência, extensibilidade e modularidade.

## Componentes Principais

- **Database Integrator**: Interface unificada para operações de banco de dados
- **Sistema de Resiliência**: Circuit Breakers e estratégias de resiliência
- **Sistema de Eventos**: Comunicação desacoplada entre componentes
- **Orquestrador de Jogos**: Gerenciamento de progresso e recomendações
- **Gestão de Perfis**: Sistema para gerenciar perfis de usuários
- **Orquestrador Terapêutico**: Análise terapêutica e recomendações adaptativas

## Arquitetura

```
main.jsx → AppInitializer.js → createIntegratedSystem.js → IntegratedSystem
    ↓                                             ↑
SystemContext → [Componentes React] ← DatabaseProvider
    ↓                ↓                            ↓
   Hooks  ←→  Sistema de Eventos  ←→  DatabaseIntegrator
    ↓                ↓
TherapeuticOrchestrator ← OrchestratorAdapter
```

## Como Usar

### 1. Inicialização do Sistema

O sistema é inicializado automaticamente através do `main.jsx`, que chama o `AppInitializer.js`:

```jsx
// Em main.jsx
import { initializePortalBetinaSystem } from './utils/AppInitializer'

async function initializeApp() {
  const system = await initializePortalBetinaSystem()
  
  // Renderizar aplicação com o sistema
  ReactDOM.createRoot(document.getElementById('root')).render(
    <SystemProvider system={system}>
      <App />
    </SystemProvider>
  )
}
```

### 2. Usando Hooks em Componentes

Para acessar o sistema em componentes React, utilize os hooks especializados:

```jsx
import { useSystem, useGameOrchestrator, useSystemEvents } from '../hooks'

function GameComponent({ userId, gameId }) {
  const { system } = useSystem()
  const { startGame, completeGame } = useGameOrchestrator({ userId })
  const { dispatchEvent } = useSystemEvents()
  
  // Implementação do componente...
}
```

### 3. Eventos do Sistema

Para comunicação entre componentes, use o sistema de eventos:

```jsx
// Disparar evento
dispatchEvent('game:complete', {
  userId: 'user123',
  gameId: 'memory-game',
  score: 100
})
```

### 4. Estratégias de Resiliência

As operações de banco de dados são automaticamente protegidas com:

- Circuit Breaker: evita chamadas repetidas a serviços falhos
- Retry: tenta novamente operações com falhas transitórias
- Cache: armazena resultados para minimizar chamadas
- Fallback: fornece dados alternativos quando necessário

## Documentação

Para mais detalhes, consulte:

- [Arquitetura do Sistema Integrado](./docs/arquitetura/ARQUITETURA_SISTEMA_INTEGRADO_V3.md)
- [Guia de Uso dos Hooks](./docs/arquitetura/GUIA_USO_HOOKS_INTEGRACAO.md)
- [Plano de Implantação](./docs/arquitetura/PLANO_IMPLANTACAO_SISTEMA_INTEGRADO.md)

## Próximos Passos

- Integração com sistemas de IA para análise cognitiva
- Implementação de PWA para funcionamento offline
- Dashboard avançado para visualização de métricas
- Extensão do sistema de plugins
