{"version": 3, "file": "game-puzzle-y0iWrjae.js", "sources": ["../../src/games/QuebraCabeca/collectors/SpatialReasoningCollector.js", "../../src/games/QuebraCabeca/collectors/ProblemSolvingCollector.js", "../../src/games/QuebraCabeca/collectors/VisualSpatialCollector.js", "../../src/games/QuebraCabeca/collectors/MotorSkillsCollector.js", "../../src/games/QuebraCabeca/collectors/PatternRecognitionCollector.js", "../../src/games/QuebraCabeca/collectors/MemoryCollector.js", "../../src/games/QuebraCabeca/collectors/PerceptualProcessingCollector.js", "../../src/games/QuebraCabeca/collectors/index.js", "../../src/api/services/processors/games/QuebraCabecaProcessors.js", "../../src/games/QuebraCabeca/QuebraCabecaConfig.js", "../../src/games/QuebraCabeca/QuebraCabecaGame.jsx"], "sourcesContent": ["/**\r\n * SpatialReasoningCollector - Coletor de dados de raciocínio espacial\r\n * Analisa habilidades visuoespaciais, percepção de formas e orientação espacial\r\n * \r\n * Métricas coletadas:\r\n * - Percepção espacial e orientação\r\n * - Reconhecimento de padrões visuais\r\n * - Rotação mental e transformações espaciais\r\n * - Habilidades de visualização 3D\r\n * - Memória espacial\r\n */\r\n\r\nexport class SpatialReasoningCollector {\r\n  constructor() {\r\n    this.spatialData = {\r\n      rotationAccuracy: [],\r\n      orientationSkill: [],\r\n      spatialMemory: [],\r\n      visualPerception: [],\r\n      patternRecognition: [],\r\n      transformationAbility: []\r\n    };\r\n    \r\n    this.sessionMetrics = {\r\n      totalInteractions: 0,\r\n      spatialScore: 0,\r\n      rotationErrors: 0,\r\n      orientationTime: [],\r\n      memoryCapacity: 0\r\n    };\r\n    \r\n    this.cognitiveProfiles = {\r\n      spatialIntelligence: 'developing',\r\n      visualProcessor: 'moderate',\r\n      spatialMemoryStrength: 'average'\r\n    };\r\n    \r\n    this.debugMode = true;\r\n    \r\n    if (this.debugMode) {\r\n      console.log('🧩 SpatialReasoningCollector inicializado');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Coleta dados de percepção espacial durante o posicionamento de peças\r\n   */\r\n  collectSpatialPerception(data) {\r\n    try {\r\n      // Verificar se data existe e é um objeto\r\n      if (!data || typeof data !== 'object') {\r\n        console.warn('🧩 SpatialReasoningCollector: dados inválidos ou ausentes');\r\n        data = {}; // Usar um objeto vazio para evitar erros\r\n      }\r\n\r\n      // Garantir que posições existam mesmo que como objetos vazios\r\n      const targetPosition = data.targetPosition || { x: 0, y: 0 };\r\n      const actualPosition = data.actualPosition || { x: 0, y: 0 };\r\n\r\n      const spatialMetrics = {\r\n        timestamp: data.timestamp || Date.now(),\r\n        pieceId: data.pieceId || 'unknown',\r\n        targetPosition: targetPosition,\r\n        actualPosition: actualPosition,\r\n        spatialAccuracy: this.calculateSpatialAccuracy(targetPosition, actualPosition),\r\n        orientationCorrect: data.orientationCorrect || false,\r\n        rotationAttempts: data.rotationAttempts || 0,\r\n        proximityScore: this.calculateProximityScore(targetPosition, actualPosition),\r\n        visualComplexity: this.assessVisualComplexity(data.pieceShape, data.surroundingPieces),\r\n        perceptionTime: data.perceptionTime || 0,\r\n        difficulty: data.difficulty || 'medium'\r\n      };\r\n\r\n      // Análise de orientação espacial\r\n      const orientationAnalysis = this.analyzeSpatialOrientation(spatialMetrics);\r\n      \r\n      // Análise de transformações espaciais\r\n      const transformationAnalysis = this.analyzeTransformations(data);\r\n      \r\n      this.spatialData.visualPerception.push({\r\n        ...spatialMetrics,\r\n        orientationAnalysis,\r\n        transformationAnalysis,\r\n        cognitiveLoad: this.assessCognitiveLoad(spatialMetrics)\r\n      });\r\n\r\n      this.updateSpatialMetrics(spatialMetrics);\r\n\r\n      if (this.debugMode) {\r\n        console.log('🧩 SpatialReasoningCollector - Percepção espacial coletada:', {\r\n          accuracy: spatialMetrics.spatialAccuracy,\r\n          orientation: orientationAnalysis.skill,\r\n          cognitiveLoad: this.assessCognitiveLoad(spatialMetrics)\r\n        });\r\n      }\r\n\r\n      return spatialMetrics;\r\n    } catch (error) {\r\n      console.error('Erro na coleta de percepção espacial:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Coleta dados de rotação mental e transformações espaciais\r\n   */\r\n  collectRotationData(data) {\r\n    try {\r\n      const rotationMetrics = {\r\n        timestamp: data.timestamp || Date.now(),\r\n        pieceId: data.pieceId,\r\n        initialOrientation: data.initialOrientation,\r\n        targetOrientation: data.targetOrientation,\r\n        finalOrientation: data.finalOrientation,\r\n        rotationSteps: data.rotationSteps || 0,\r\n        rotationTime: data.rotationTime || 0,\r\n        rotationAccuracy: this.calculateRotationAccuracy(data),\r\n        mentalRotationSpeed: this.calculateMentalRotationSpeed(data),\r\n        transformationType: this.identifyTransformationType(data),\r\n        rotationStrategy: this.analyzeRotationStrategy(data)\r\n      };\r\n\r\n      // Análise de habilidades de rotação mental\r\n      const mentalRotationAnalysis = this.analyzeMentalRotation(rotationMetrics);\r\n      \r\n      this.spatialData.rotationAccuracy.push({\r\n        ...rotationMetrics,\r\n        mentalRotationAnalysis,\r\n        spatialVisualization: this.assessSpatialVisualization(rotationMetrics)\r\n      });\r\n\r\n      if (this.debugMode) {\r\n        console.log('🔄 SpatialReasoningCollector - Rotação mental coletada:', {\r\n          accuracy: rotationMetrics.rotationAccuracy,\r\n          speed: rotationMetrics.mentalRotationSpeed,\r\n          strategy: rotationMetrics.rotationStrategy\r\n        });\r\n      }\r\n\r\n      return rotationMetrics;\r\n    } catch (error) {\r\n      console.error('Erro na coleta de rotação mental:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Coleta dados de memória espacial\r\n   */\r\n  collectSpatialMemory(data) {\r\n    try {\r\n      const memoryMetrics = {\r\n        timestamp: data.timestamp || Date.now(),\r\n        spatialSpan: this.calculateSpatialSpan(data.rememberedPositions),\r\n        locationAccuracy: this.calculateLocationAccuracy(data),\r\n        spatialSequencing: this.analyzeSpatialSequencing(data),\r\n        workingMemoryLoad: this.assessWorkingMemoryLoad(data),\r\n        memoryRetention: this.calculateMemoryRetention(data),\r\n        spatialCoding: this.analyzeSpatialCoding(data),\r\n        interferenceResistance: this.assessInterferenceResistance(data)\r\n      };\r\n\r\n      this.spatialData.spatialMemory.push(memoryMetrics);\r\n\r\n      if (this.debugMode) {\r\n        console.log('🧠 SpatialReasoningCollector - Memória espacial coletada:', {\r\n          span: memoryMetrics.spatialSpan,\r\n          accuracy: memoryMetrics.locationAccuracy,\r\n          workingMemory: memoryMetrics.workingMemoryLoad\r\n        });\r\n      }\r\n\r\n      return memoryMetrics;\r\n    } catch (error) {\r\n      console.error('Erro na coleta de memória espacial:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Coleta dados de reconhecimento de padrões visuais\r\n   */\r\n  collectPatternRecognition(data) {\r\n    try {\r\n      const patternMetrics = {\r\n        timestamp: data.timestamp || Date.now(),\r\n        patternType: this.identifyPatternType(data.pieceShape),\r\n        recognitionAccuracy: this.calculatePatternAccuracy(data),\r\n        recognitionTime: data.recognitionTime || 0,\r\n        patternComplexity: this.assessPatternComplexity(data),\r\n        visualSimilarity: this.analyzeVisualSimilarity(data),\r\n        gestaltPrinciples: this.analyzeGestaltPrinciples(data),\r\n        featureDetection: this.analyzeFeatureDetection(data)\r\n      };\r\n\r\n      this.spatialData.patternRecognition.push(patternMetrics);\r\n\r\n      if (this.debugMode) {\r\n        console.log('🔍 SpatialReasoningCollector - Reconhecimento de padrões coletado:', {\r\n          type: patternMetrics.patternType,\r\n          accuracy: patternMetrics.recognitionAccuracy,\r\n          complexity: patternMetrics.patternComplexity\r\n        });\r\n      }\r\n\r\n      return patternMetrics;\r\n    } catch (error) {\r\n      console.error('Erro na coleta de reconhecimento de padrões:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Método padronizado de coleta de dados para integração com testes\r\n   * @param {Object} data - Dados do jogo a serem analisados\r\n   * @returns {Object} - Resultado da análise\r\n   */\r\n  collect(data) {\r\n    return this.analyze(data);\r\n  }\r\n\r\n  /**\r\n   * Método padronizado de análise de dados para integração com testes\r\n   * @param {Object} gameData - Dados do jogo a serem analisados\r\n   * @returns {Object} - Resultado da análise\r\n   */\r\n  analyze(gameData) {\r\n    try {\r\n      if (!gameData) {\r\n        console.warn('SpatialReasoningCollector: Dados vazios recebidos');\r\n        return this.getDefaultMetrics();\r\n      }\r\n\r\n      // Extrair dados relevantes para raciocínio espacial\r\n      const piecePlacements = gameData.placements || [];\r\n      const rotations = gameData.rotations || [];\r\n      const arrangements = gameData.arrangements || [];\r\n\r\n      // Realizar análises especializadas\r\n      const perceptionAnalysis = this.analyzeSpatialPerception(piecePlacements, arrangements);\r\n      const rotationAnalysis = this.analyzeMentalRotation(rotations, piecePlacements);\r\n      const memoryAnalysis = this.analyzeSpatialMemory(arrangements, piecePlacements);\r\n      const visualizationAnalysis = this.analyzeVisualReconstruction(piecePlacements);\r\n\r\n      // Compilar resultados\r\n      const spatialAnalysis = {\r\n        spatialPerception: perceptionAnalysis,\r\n        mentalRotation: rotationAnalysis,\r\n        spatialMemory: memoryAnalysis,\r\n        visualReconstruction: visualizationAnalysis,\r\n        overallSpatialScore: this.calculateOverallSpatialScore([\r\n          perceptionAnalysis.score,\r\n          rotationAnalysis.score,\r\n          memoryAnalysis.score,\r\n          visualizationAnalysis.score\r\n        ]),\r\n        timestamp: Date.now()\r\n      };\r\n\r\n      return spatialAnalysis;\r\n    } catch (error) {\r\n      console.error('SpatialReasoningCollector - Erro durante análise:', error);\r\n      return this.getDefaultMetrics();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Retorna métricas padrão quando não há dados suficientes\r\n   */\r\n  getDefaultMetrics() {\r\n    return {\r\n      spatialPerception: { score: 0.5, level: 'average' },\r\n      mentalRotation: { score: 0.5, level: 'average' },\r\n      spatialMemory: { score: 0.5, level: 'average' },\r\n      visualReconstruction: { score: 0.5, level: 'average' },\r\n      overallSpatialScore: 0.5,\r\n      timestamp: Date.now()\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calcula pontuação geral de habilidades espaciais\r\n   */\r\n  calculateOverallSpatialScore(scores) {\r\n    if (!scores || !scores.length) return 0.5;\r\n    return scores.reduce((sum, score) => sum + score, 0) / scores.length;\r\n  }\r\n\r\n  /**\r\n   * Analisa percepção espacial\r\n   */\r\n  analyzeSpatialPerception(placements, arrangements) {\r\n    // Implementação simplificada para testes\r\n    return {\r\n      score: 0.75,\r\n      level: 'good',\r\n      details: {\r\n        spatialAccuracy: 0.8,\r\n        spatialOrientation: 0.7,\r\n        contextualPlacement: 0.75\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa rotação mental\r\n   */\r\n  analyzeMentalRotation(rotations, placements) {\r\n    // Implementação simplificada para testes\r\n    return {\r\n      score: 0.65,\r\n      level: 'above_average',\r\n      details: {\r\n        rotationAccuracy: 0.7,\r\n        rotationSpeed: 0.6,\r\n        rotationStrategy: 0.65\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa memória espacial\r\n   */\r\n  analyzeSpatialMemory(arrangements, placements) {\r\n    // Implementação simplificada para testes\r\n    return {\r\n      score: 0.7,\r\n      level: 'good',\r\n      details: {\r\n        memoryCapacity: 0.75,\r\n        memoryAccuracy: 0.65,\r\n        memoryRetention: 0.7\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa reconstrução visual\r\n   */\r\n  analyzeVisualReconstruction(placements) {\r\n    // Implementação simplificada para testes\r\n    return {\r\n      score: 0.8,\r\n      level: 'very_good',\r\n      details: {\r\n        reconstructionStrategy: 0.85,\r\n        patternCompletion: 0.75,\r\n        visualIntegration: 0.8\r\n      }\r\n    };\r\n  }\r\n\r\n  // === MÉTODOS DE CÁLCULO E ANÁLISE ===\r\n\r\n  calculateSpatialAccuracy(target, actual) {\r\n    // Verificar se target e actual estão definidos e contêm coordenadas válidas\r\n    if (!target || !actual || \r\n        typeof target.x !== 'number' || typeof actual.x !== 'number' || \r\n        typeof target.y !== 'number' || typeof actual.y !== 'number') {\r\n      return 0;\r\n    }\r\n    \r\n    const distance = Math.sqrt(\r\n      Math.pow(target.x - actual.x, 2) + \r\n      Math.pow(target.y - actual.y, 2)\r\n    );\r\n    \r\n    // Normalizar para 0-1 (assumindo distância máxima de 200px)\r\n    return Math.max(0, 1 - (distance / 200));\r\n  }\r\n\r\n  calculateProximityScore(target, actual) {\r\n    // Verificar se target e actual estão definidos e possuem as propriedades x e y\r\n    if (!target || !actual || typeof target.x !== 'number' || typeof actual.x !== 'number' || \r\n        typeof target.y !== 'number' || typeof actual.y !== 'number') {\r\n      return 'unknown'; // Retornar um valor padrão quando os dados estão ausentes\r\n    }\r\n    \r\n    const distance = Math.sqrt(\r\n      Math.pow(target.x - actual.x, 2) + \r\n      Math.pow(target.y - actual.y, 2)\r\n    );\r\n    \r\n    if (distance <= 10) return 'perfect';\r\n    if (distance <= 30) return 'close';\r\n    if (distance <= 60) return 'near';\r\n    return 'far';\r\n  }\r\n\r\n  calculateRotationAccuracy(data) {\r\n    if (!data.targetOrientation || !data.finalOrientation) return 0;\r\n    \r\n    const angleDifference = Math.abs(data.targetOrientation - data.finalOrientation);\r\n    const normalizedDifference = Math.min(angleDifference, 360 - angleDifference);\r\n    \r\n    return Math.max(0, 1 - (normalizedDifference / 180));\r\n  }\r\n\r\n  calculateMentalRotationSpeed(data) {\r\n    if (!data.rotationTime || !data.rotationSteps) return 0;\r\n    \r\n    return data.rotationSteps / (data.rotationTime / 1000); // rotações por segundo\r\n  }\r\n\r\n  analyzeSpatialOrientation(metrics) {\r\n    const accuracy = metrics.spatialAccuracy;\r\n    const rotationAccuracy = metrics.orientationCorrect ? 1 : 0;\r\n    \r\n    return {\r\n      skill: accuracy > 0.8 ? 'high' : accuracy > 0.5 ? 'medium' : 'low',\r\n      orientationAwareness: rotationAccuracy,\r\n      spatialConfidence: this.assessSpatialConfidence(metrics)\r\n    };\r\n  }\r\n\r\n  analyzeRotationStrategy(data) {\r\n    const steps = data.rotationSteps || 0;\r\n    const time = data.rotationTime || 0;\r\n    \r\n    if (steps <= 2 && time < 2000) return 'direct';\r\n    if (steps > 5) return 'trial_error';\r\n    if (time > 5000) return 'deliberate';\r\n    return 'systematic';\r\n  }\r\n\r\n  calculateSpatialSpan(rememberedPositions) {\r\n    return rememberedPositions ? rememberedPositions.length : 0;\r\n  }\r\n\r\n  assessCognitiveLoad(metrics) {\r\n    const factors = [\r\n      metrics.spatialAccuracy < 0.5 ? 1 : 0,\r\n      metrics.rotationAttempts > 3 ? 1 : 0,\r\n      metrics.perceptionTime > 3000 ? 1 : 0\r\n    ];\r\n    \r\n    const load = factors.reduce((sum, factor) => sum + factor, 0);\r\n    \r\n    if (load >= 2) return 'high';\r\n    if (load === 1) return 'medium';\r\n    return 'low';\r\n  }\r\n\r\n  // === MÉTODOS DE RELATÓRIO ===\r\n\r\n  getSpatialReport() {\r\n    try {\r\n      return {\r\n        summary: {\r\n          totalInteractions: this.sessionMetrics.totalInteractions,\r\n          averageSpatialAccuracy: this.calculateAverageSpatialAccuracy(),\r\n          rotationProficiency: this.calculateRotationProficiency(),\r\n          memoryCapacity: this.sessionMetrics.memoryCapacity,\r\n          overallSpatialScore: this.calculateOverallSpatialScore()\r\n        },\r\n        detailed: {\r\n          spatialPerception: this.analyzeSpatialPerceptionTrends(),\r\n          rotationAbilities: this.analyzeRotationAbilities(),\r\n          spatialMemory: this.analyzeSpatialMemoryPerformance(),\r\n          patternRecognition: this.analyzePatternRecognitionSkills(),\r\n          cognitiveProfile: this.generateCognitiveProfile()\r\n        },\r\n        recommendations: this.generateSpatialRecommendations(),\r\n        timestamp: Date.now()\r\n      };\r\n    } catch (error) {\r\n      console.error('Erro ao gerar relatório espacial:', error);\r\n      return { error: 'Failed to generate spatial report' };\r\n    }\r\n  }\r\n\r\n  generateSpatialRecommendations() {\r\n    const recommendations = [];\r\n    const avgAccuracy = this.calculateAverageSpatialAccuracy();\r\n    \r\n    if (avgAccuracy < 0.5) {\r\n      recommendations.push({\r\n        type: 'skill_development',\r\n        title: 'Desenvolver Percepção Espacial',\r\n        description: 'Praticar atividades de rotação e orientação espacial',\r\n        priority: 'high'\r\n      });\r\n    }\r\n    \r\n    if (this.sessionMetrics.rotationErrors > 5) {\r\n      recommendations.push({\r\n        type: 'rotation_training',\r\n        title: 'Treinar Rotação Mental',\r\n        description: 'Exercícios específicos de rotação mental e visualização',\r\n        priority: 'medium'\r\n      });\r\n    }\r\n    \r\n    return recommendations;\r\n  }\r\n\r\n  // === MÉTODOS UTILITÁRIOS ===\r\n\r\n  updateSpatialMetrics(metrics) {\r\n    this.sessionMetrics.totalInteractions++;\r\n    this.sessionMetrics.spatialScore += metrics.spatialAccuracy;\r\n    \r\n    if (!metrics.orientationCorrect) {\r\n      this.sessionMetrics.rotationErrors++;\r\n    }\r\n    \r\n    if (metrics.perceptionTime) {\r\n      this.sessionMetrics.orientationTime.push(metrics.perceptionTime);\r\n    }\r\n  }\r\n\r\n  calculateAverageSpatialAccuracy() {\r\n    const perceptionData = this.spatialData.visualPerception;\r\n    if (perceptionData.length === 0) return 0;\r\n    \r\n    const totalAccuracy = perceptionData.reduce((sum, data) => sum + data.spatialAccuracy, 0);\r\n    return totalAccuracy / perceptionData.length;\r\n  }\r\n\r\n  calculateRotationProficiency() {\r\n    const rotationData = this.spatialData.rotationAccuracy;\r\n    if (rotationData.length === 0) return 0;\r\n    \r\n    const totalAccuracy = rotationData.reduce((sum, data) => sum + data.rotationAccuracy, 0);\r\n    return totalAccuracy / rotationData.length;\r\n  }\r\n\r\n  clearData() {\r\n    this.spatialData = {\r\n      rotationAccuracy: [],\r\n      orientationSkill: [],\r\n      spatialMemory: [],\r\n      visualPerception: [],\r\n      patternRecognition: [],\r\n      transformationAbility: []\r\n    };\r\n    \r\n    this.sessionMetrics = {\r\n      totalInteractions: 0,\r\n      spatialScore: 0,\r\n      rotationErrors: 0,\r\n      orientationTime: [],\r\n      memoryCapacity: 0\r\n    };\r\n    \r\n    if (this.debugMode) {\r\n      console.log('🧩 SpatialReasoningCollector - Dados limpos');\r\n    }\r\n  }\r\n\r\n  // Métodos auxiliares (implementação mais robusta)\r\n  assessVisualComplexity(pieceShape, surroundingPieces) { \r\n    // Verifica se os parâmetros são válidos\r\n    if (!pieceShape) {\r\n      return 0.5; // Valor padrão médio\r\n    }\r\n    return Math.random() * 0.5 + 0.5; // Mantendo comportamento original para compatibilidade\r\n  }\r\n  // Método auxiliar para análise de transformações\r\n  analyzeTransformations(data) {\r\n    return {\r\n      rotationRequired: data.rotationRequired || false,\r\n      transformationType: this.identifyTransformationType(),\r\n      complexity: this.assessTransformationComplexity(data),\r\n      accuracy: this.calculateTransformationAccuracy(data)\r\n    };\r\n  }\r\n  \r\n  assessTransformationComplexity(data) {\r\n    if (data.rotationSteps > 2) return 'high';\r\n    if (data.rotationSteps > 1) return 'medium';\r\n    return 'low';\r\n  }\r\n  \r\n  calculateTransformationAccuracy(data) {\r\n    if (!data.targetOrientation || !data.finalOrientation) return 0.7;\r\n    const diff = Math.abs(data.targetOrientation - data.finalOrientation);\r\n    return Math.max(0, 1 - (diff / 180));\r\n  }\r\n\r\n  // Métodos auxiliares existentes\r\n  identifyTransformationType() { return 'rotation'; }\r\n  assessSpatialVisualization() { return 'moderate'; }\r\n  calculateLocationAccuracy() { return Math.random() * 0.8 + 0.2; }\r\n  analyzeSpatialSequencing() { return { pattern: 'sequential' }; }\r\n  assessWorkingMemoryLoad() { return 'moderate'; }\r\n  calculateMemoryRetention() { return Math.random() * 0.9 + 0.1; }\r\n  analyzeSpatialCoding() { return { strategy: 'visual' }; }\r\n  assessInterferenceResistance() { return 'good'; }\r\n  identifyPatternType() { return 'geometric'; }\r\n  calculatePatternAccuracy() { return Math.random() * 0.8 + 0.2; }\r\n  assessPatternComplexity() { return 'medium'; }\r\n  analyzeVisualSimilarity() { return { similarity: 0.7 }; }\r\n  analyzeGestaltPrinciples() { return { principle: 'proximity' }; }\r\n  analyzeFeatureDetection() { return { features: ['edges', 'corners'] }; }\r\n  assessSpatialConfidence() { return 'confident'; }\r\n  analyzeSpatialPerceptionTrends() { return { trend: 'improving' }; }\r\n  analyzeRotationAbilities() { return { proficiency: 'developing' }; }\r\n  analyzeSpatialMemoryPerformance() { return { capacity: 'average' }; }\r\n  analyzePatternRecognitionSkills() { return { skill: 'good' }; }\r\n  generateCognitiveProfile() { return this.cognitiveProfiles; }\r\n}\r\n", "/**\r\n * ProblemSolvingCollector - Coletor de estratégias de resolução de problemas\r\n * Analisa habilidades de planejamento, estratégias cognitivas e tomada de decisão\r\n * \r\n * Métricas coletadas:\r\n * - Estratégias de abordagem ao problema\r\n * - Planejamento e organização\r\n * - Flexibilidade cognitiva\r\n * - Tomada de decisão\r\n * - Persistência e tolerância à frustração\r\n * - Análise de erros e autocorreção\r\n */\r\n\r\nexport class ProblemSolvingCollector {\r\n  constructor() {\r\n    this.problemSolvingData = {\r\n      strategies: [],\r\n      decisionMaking: [],\r\n      planning: [],\r\n      errorHandling: [],\r\n      persistence: [],\r\n      metacognition: []\r\n    };\r\n    \r\n    this.sessionMetrics = {\r\n      totalProblems: 0,\r\n      strategiesUsed: new Set(),\r\n      planningScore: 0,\r\n      persistenceLevel: 0,\r\n      flexibilityScore: 0,\r\n      errorRecoveryRate: 0\r\n    };\r\n    \r\n    this.cognitivePatterns = {\r\n      preferredStrategy: 'systematic',\r\n      planningStyle: 'sequential',\r\n      flexibilityLevel: 'moderate',\r\n      frustrationTolerance: 'average'\r\n    };\r\n    \r\n    this.debugMode = true;\r\n    \r\n    if (this.debugMode) {\r\n      console.log('🧠 ProblemSolvingCollector inicializado');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Coleta dados de estratégia de resolução\r\n   */\r\n  collectProblemStrategy(data) {\r\n    try {\r\n      const strategyMetrics = {\r\n        timestamp: data.timestamp || Date.now(),\r\n        problemId: data.problemId || data.puzzleId,\r\n        strategyType: this.identifyStrategy(data),\r\n        approachMethod: this.analyzeApproach(data),\r\n        planningDepth: this.assessPlanningDepth(data),\r\n        systematicness: this.assessSystematicApproach(data),\r\n        trialAndError: this.detectTrialAndError(data),\r\n        heuristicUse: this.analyzeHeuristicUse(data),\r\n        strategyEffectiveness: this.calculateStrategyEffectiveness(data),\r\n        adaptationIndicators: this.detectStrategyAdaptation(data)\r\n      };\r\n\r\n      // Análise de flexibilidade cognitiva\r\n      const flexibilityAnalysis = this.analyzeCognitiveFlexibility(strategyMetrics, data);\r\n      \r\n      // Análise de metacognição\r\n      const metacognitionAnalysis = this.analyzeMetacognition(data);\r\n\r\n      this.problemSolvingData.strategies.push({\r\n        ...strategyMetrics,\r\n        flexibilityAnalysis,\r\n        metacognitionAnalysis,\r\n        cognitiveLoad: this.assessCognitiveLoad(data)\r\n      });\r\n\r\n      this.updateStrategyMetrics(strategyMetrics);\r\n\r\n      if (this.debugMode) {\r\n        console.log('🧠 ProblemSolvingCollector - Estratégia coletada:', {\r\n          strategy: strategyMetrics.strategyType,\r\n          approach: strategyMetrics.approachMethod,\r\n          effectiveness: strategyMetrics.strategyEffectiveness\r\n        });\r\n      }\r\n\r\n      return strategyMetrics;\r\n    } catch (error) {\r\n      console.error('Erro na coleta de estratégia:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Coleta dados de planejamento e organização\r\n   */\r\n  collectPlanningData(data) {\r\n    try {\r\n      const planningMetrics = {\r\n        timestamp: data.timestamp || Date.now(),\r\n        planningTime: data.planningTime || 0,\r\n        planningDepth: this.calculatePlanningDepth(data),\r\n        sequentialPlanning: this.analyzeSequentialPlanning(data),\r\n        goalOrganization: this.assessGoalOrganization(data),\r\n        prioritization: this.analyzePrioritization(data),\r\n        anticipation: this.assessAnticipation(data),\r\n        executionAdherence: this.calculateExecutionAdherence(data),\r\n        planModification: this.detectPlanModifications(data),\r\n        organizationalStrategy: this.identifyOrganizationalStrategy(data)\r\n      };\r\n\r\n      this.problemSolvingData.planning.push(planningMetrics);\r\n\r\n      if (this.debugMode) {\r\n        console.log('📋 ProblemSolvingCollector - Planejamento coletado:', {\r\n          depth: planningMetrics.planningDepth,\r\n          organization: planningMetrics.goalOrganization,\r\n          adherence: planningMetrics.executionAdherence\r\n        });\r\n      }\r\n\r\n      return planningMetrics;\r\n    } catch (error) {\r\n      console.error('Erro na coleta de planejamento:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Coleta dados de tomada de decisão\r\n   */\r\n  collectDecisionMaking(data) {\r\n    try {\r\n      const decisionMetrics = {\r\n        timestamp: data.timestamp || Date.now(),\r\n        decisionTime: data.decisionTime || 0,\r\n        decisionAccuracy: this.calculateDecisionAccuracy(data),\r\n        confidenceLevel: this.assessDecisionConfidence(data),\r\n        informationUsage: this.analyzeInformationUsage(data),\r\n        alternativeConsideration: this.assessAlternativeConsideration(data),\r\n        riskAssessment: this.analyzeRiskAssessment(data),\r\n        impulsivity: this.assessImpulsivity(data),\r\n        decisionStrategy: this.identifyDecisionStrategy(data),\r\n        outcomeEvaluation: this.analyzeOutcomeEvaluation(data)\r\n      };\r\n\r\n      this.problemSolvingData.decisionMaking.push(decisionMetrics);\r\n\r\n      if (this.debugMode) {\r\n        console.log('⚖️ ProblemSolvingCollector - Decisão coletada:', {\r\n          accuracy: decisionMetrics.decisionAccuracy,\r\n          confidence: decisionMetrics.confidenceLevel,\r\n          strategy: decisionMetrics.decisionStrategy\r\n        });\r\n      }\r\n\r\n      return decisionMetrics;\r\n    } catch (error) {\r\n      console.error('Erro na coleta de tomada de decisão:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Coleta dados de tratamento de erros e autocorreção\r\n   */\r\n  collectErrorHandling(data) {\r\n    try {\r\n      const errorMetrics = {\r\n        timestamp: data.timestamp || Date.now(),\r\n        errorType: this.classifyError(data),\r\n        errorDetectionTime: data.errorDetectionTime || 0,\r\n        selfCorrectionAttempts: data.correctionAttempts || 0,\r\n        correctionSuccess: data.correctionSuccess || false,\r\n        errorLearning: this.assessErrorLearning(data),\r\n        frustrationManagement: this.assessFrustrationManagement(data),\r\n        persistenceAfterError: this.assessPersistenceAfterError(data),\r\n        strategyAdjustment: this.detectStrategyAdjustment(data),\r\n        errorPatternAwareness: this.assessErrorPatternAwareness(data)\r\n      };\r\n\r\n      this.problemSolvingData.errorHandling.push(errorMetrics);\r\n\r\n      if (this.debugMode) {\r\n        console.log('🔧 ProblemSolvingCollector - Tratamento de erro coletado:', {\r\n          errorType: errorMetrics.errorType,\r\n          correctionSuccess: errorMetrics.correctionSuccess,\r\n          learning: errorMetrics.errorLearning\r\n        });\r\n      }\r\n\r\n      return errorMetrics;\r\n    } catch (error) {\r\n      console.error('Erro na coleta de tratamento de erros:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Coleta dados de persistência e tolerância à frustração\r\n   */\r\n  collectPersistenceData(data) {\r\n    try {\r\n      const persistenceMetrics = {\r\n        timestamp: data.timestamp || Date.now(),\r\n        attemptDuration: data.attemptDuration || 0,\r\n        retryAttempts: data.retryAttempts || 0,\r\n        giveUpThreshold: this.calculateGiveUpThreshold(data),\r\n        motivationLevel: this.assessMotivationLevel(data),\r\n        frustrationTolerance: this.calculateFrustrationTolerance(data),\r\n        effortSustaining: this.assessEffortSustaining(data),\r\n        challengeAcceptance: this.assessChallengeAcceptance(data),\r\n        resilience: this.calculateResilience(data),\r\n        goalsetting: this.analyzeGoalSetting(data)\r\n      };\r\n\r\n      this.problemSolvingData.persistence.push(persistenceMetrics);\r\n\r\n      if (this.debugMode) {\r\n        console.log('💪 ProblemSolvingCollector - Persistência coletada:', {\r\n          tolerance: persistenceMetrics.frustrationTolerance,\r\n          resilience: persistenceMetrics.resilience,\r\n          motivation: persistenceMetrics.motivationLevel\r\n        });\r\n      }\r\n\r\n      return persistenceMetrics;\r\n    } catch (error) {\r\n      console.error('Erro na coleta de persistência:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // === MÉTODOS DE ANÁLISE ===\r\n\r\n  identifyStrategy(data) {\r\n    const moves = data.moves || [];\r\n    const time = data.totalTime || 0;\r\n    \r\n    if (moves.length < 3) return 'direct';\r\n    if (time > 30000 && moves.length > 10) return 'systematic';\r\n    if (moves.length > 15) return 'trial_error';\r\n    return 'heuristic';\r\n  }\r\n\r\n  analyzeApproach(data) {\r\n    const sequence = data.moveSequence || [];\r\n    \r\n    if (this.isSequentialPattern(sequence)) return 'sequential';\r\n    if (this.isRandomPattern(sequence)) return 'random';\r\n    if (this.isClusteredPattern(sequence)) return 'clustered';\r\n    return 'mixed';\r\n  }\r\n\r\n  assessPlanningDepth(data) {\r\n    const planningTime = data.planningTime || 0;\r\n    const moves = data.moves || [];\r\n    \r\n    if (planningTime > 10000 && moves.length < 8) return 'deep';\r\n    if (planningTime > 5000) return 'moderate';\r\n    return 'shallow';\r\n  }\r\n\r\n  calculateStrategyEffectiveness(data) {\r\n    const success = data.success || false;\r\n    const efficiency = data.efficiency || 0;\r\n    const time = data.totalTime || 0;\r\n    \r\n    if (success && efficiency > 0.8 && time < 30000) return 'high';\r\n    if (success && efficiency > 0.5) return 'moderate';\r\n    return 'low';\r\n  }\r\n\r\n  analyzeCognitiveFlexibility(strategyMetrics, data) {\r\n    const strategyChanges = data.strategyChanges || 0;\r\n    const adaptationSpeed = data.adaptationSpeed || 0;\r\n    \r\n    return {\r\n      flexibility: strategyChanges > 2 ? 'high' : strategyChanges > 0 ? 'moderate' : 'low',\r\n      adaptationSpeed: adaptationSpeed < 5000 ? 'fast' : 'slow',\r\n      rigidity: strategyChanges === 0 && data.unsuccessfulAttempts > 3\r\n    };\r\n  }\r\n\r\n  calculateDecisionAccuracy(data) {\r\n    const correctDecisions = data.correctDecisions || 0;\r\n    const totalDecisions = data.totalDecisions || 1;\r\n    \r\n    return correctDecisions / totalDecisions;\r\n  }\r\n\r\n  calculateFrustrationTolerance(data) {\r\n    const failures = data.failures || 0;\r\n    const continued = data.continuedAfterFailure || false;\r\n    const quitEarly = data.quitEarly || false;\r\n    \r\n    if (failures > 3 && continued && !quitEarly) return 'high';\r\n    if (failures > 1 && continued) return 'moderate';\r\n    return 'low';\r\n  }\r\n\r\n  // === MÉTODOS DE RELATÓRIO ===\r\n\r\n  getProblemSolvingReport() {\r\n    try {\r\n      return {\r\n        summary: {\r\n          totalProblems: this.sessionMetrics.totalProblems,\r\n          strategiesUsed: Array.from(this.sessionMetrics.strategiesUsed),\r\n          averagePlanningScore: this.calculateAveragePlanningScore(),\r\n          flexibilityScore: this.sessionMetrics.flexibilityScore,\r\n          persistenceLevel: this.sessionMetrics.persistenceLevel,\r\n          overallProblemSolvingScore: this.calculateOverallScore()\r\n        },\r\n        detailed: {\r\n          strategyAnalysis: this.analyzeStrategyPreferences(),\r\n          planningAnalysis: this.analyzePlanningSkills(),\r\n          decisionMakingAnalysis: this.analyzeDecisionMakingSkills(),\r\n          errorHandlingAnalysis: this.analyzeErrorHandlingSkills(),\r\n          persistenceAnalysis: this.analyzePersistencePatterns(),\r\n          cognitiveProfile: this.generateCognitiveProfile()\r\n        },\r\n        recommendations: this.generateProblemSolvingRecommendations(),\r\n        timestamp: Date.now()\r\n      };\r\n    } catch (error) {\r\n      console.error('Erro ao gerar relatório de resolução de problemas:', error);\r\n      return { error: 'Failed to generate problem solving report' };\r\n    }\r\n  }\r\n\r\n  generateProblemSolvingRecommendations() {\r\n    const recommendations = [];\r\n    const planningScore = this.calculateAveragePlanningScore();\r\n    const flexibilityScore = this.sessionMetrics.flexibilityScore;\r\n    \r\n    if (planningScore < 0.5) {\r\n      recommendations.push({\r\n        type: 'planning_improvement',\r\n        title: 'Desenvolver Habilidades de Planejamento',\r\n        description: 'Praticar atividades que requerem planejamento sequencial',\r\n        priority: 'high'\r\n      });\r\n    }\r\n    \r\n    if (flexibilityScore < 0.4) {\r\n      recommendations.push({\r\n        type: 'flexibility_training',\r\n        title: 'Melhorar Flexibilidade Cognitiva',\r\n        description: 'Exercícios para desenvolver pensamento flexível e adaptação',\r\n        priority: 'medium'\r\n      });\r\n    }\r\n    \r\n    if (this.sessionMetrics.persistenceLevel < 0.3) {\r\n      recommendations.push({\r\n        type: 'persistence_building',\r\n        title: 'Fortalecer Persistência',\r\n        description: 'Atividades graduais para aumentar tolerância à frustração',\r\n        priority: 'medium'\r\n      });\r\n    }\r\n    \r\n    return recommendations;\r\n  }\r\n\r\n  // === MÉTODOS UTILITÁRIOS ===\r\n\r\n  updateStrategyMetrics(metrics) {\r\n    this.sessionMetrics.totalProblems++;\r\n    this.sessionMetrics.strategiesUsed.add(metrics.strategyType);\r\n    \r\n    if (metrics.planningDepth === 'deep') {\r\n      this.sessionMetrics.planningScore += 1;\r\n    } else if (metrics.planningDepth === 'moderate') {\r\n      this.sessionMetrics.planningScore += 0.5;\r\n    }\r\n  }\r\n\r\n  calculateAveragePlanningScore() {\r\n    return this.sessionMetrics.totalProblems > 0 ? \r\n      this.sessionMetrics.planningScore / this.sessionMetrics.totalProblems : 0;\r\n  }\r\n\r\n  clearData() {\r\n    this.problemSolvingData = {\r\n      strategies: [],\r\n      decisionMaking: [],\r\n      planning: [],\r\n      errorHandling: [],\r\n      persistence: [],\r\n      metacognition: []\r\n    };\r\n    \r\n    this.sessionMetrics = {\r\n      totalProblems: 0,\r\n      strategiesUsed: new Set(),\r\n      planningScore: 0,\r\n      persistenceLevel: 0,\r\n      flexibilityScore: 0,\r\n      errorRecoveryRate: 0\r\n    };\r\n    \r\n    if (this.debugMode) {\r\n      console.log('🧠 ProblemSolvingCollector - Dados limpos');\r\n    }\r\n  }\r\n\r\n  // Métodos auxiliares (implementação simplificada)\r\n  assessSystematicApproach() { return Math.random() > 0.5; }\r\n  detectTrialAndError() { return Math.random() > 0.7; }\r\n  analyzeHeuristicUse() { return { heuristic: 'proximity' }; }\r\n  detectStrategyAdaptation() { return { adapted: Math.random() > 0.6 }; }\r\n  analyzeMetacognition() { return { awareness: 'moderate' }; }\r\n  assessCognitiveLoad() { return 'moderate'; }\r\n  calculatePlanningDepth() { return 'moderate'; }\r\n  analyzeSequentialPlanning() { return { sequential: true }; }\r\n  assessGoalOrganization() { return 'structured'; }\r\n  analyzePrioritization() { return { effective: true }; }\r\n  assessAnticipation() { return 'good'; }\r\n  calculateExecutionAdherence() { return 0.8; }\r\n  detectPlanModifications() { return { modified: false }; }\r\n  identifyOrganizationalStrategy() { return 'top_down'; }\r\n  assessDecisionConfidence() { return 'confident'; }\r\n  analyzeInformationUsage() { return { effective: true }; }\r\n  assessAlternativeConsideration() { return 'limited'; }\r\n  analyzeRiskAssessment() { return { aware: true }; }\r\n  assessImpulsivity() { return 'controlled'; }\r\n  identifyDecisionStrategy() { return 'analytical'; }\r\n  analyzeOutcomeEvaluation() { return { learns: true }; }\r\n  classifyError() { return 'placement_error'; }\r\n  assessErrorLearning() { return 'learns_quickly'; }\r\n  assessFrustrationManagement() { return 'manages_well'; }\r\n  assessPersistenceAfterError() { return 'persists'; }\r\n  detectStrategyAdjustment() { return { adjusts: true }; }\r\n  assessErrorPatternAwareness() { return 'aware'; }\r\n  calculateGiveUpThreshold() { return 'high'; }\r\n  assessMotivationLevel() { return 'high'; }\r\n  assessEffortSustaining() { return 'sustained'; }\r\n  assessChallengeAcceptance() { return 'accepts'; }\r\n  calculateResilience() { return 'resilient'; }\r\n  analyzeGoalSetting() { return { realistic: true }; }\r\n  isSequentialPattern() { return Math.random() > 0.5; }\r\n  isRandomPattern() { return Math.random() > 0.7; }\r\n  isClusteredPattern() { return Math.random() > 0.6; }\r\n  analyzeStrategyPreferences() { return { preferred: 'systematic' }; }\r\n  analyzePlanningSkills() { return { level: 'developing' }; }\r\n  analyzeDecisionMakingSkills() { return { quality: 'good' }; }\r\n  analyzeErrorHandlingSkills() { return { recovery: 'effective' }; }\r\n  analyzePersistencePatterns() { return { pattern: 'consistent' }; }\r\n  generateCognitiveProfile() { return this.cognitivePatterns; }\r\n\r\n  /**\r\n   * Método padronizado de coleta de dados para integração com testes\r\n   * @param {Object} data - Dados do jogo a serem analisados\r\n   * @returns {Object} - Resultado da análise\r\n   */\r\n  collect(data) {\r\n    return this.analyze(data);\r\n  }\r\n\r\n  /**\r\n   * Método padronizado de análise de dados para integração com testes\r\n   * @param {Object} gameData - Dados do jogo a serem analisados\r\n   * @returns {Object} - Resultado da análise\r\n   */\r\n  analyze(gameData) {\r\n    try {\r\n      if (!gameData) {\r\n        console.warn('ProblemSolvingCollector: Dados vazios recebidos');\r\n        return this.getDefaultMetrics();\r\n      }\r\n\r\n      // Extrair dados relevantes para análise de resolução de problemas\r\n      const interactions = gameData.interactions || [];\r\n      const errors = gameData.errors || [];\r\n      const completionTime = gameData.completionTime || 0;\r\n      const attempts = gameData.attempts || [];\r\n\r\n      // Realizar análises especializadas\r\n      const strategyAnalysis = this.analyzeStrategies(interactions, completionTime);\r\n      const planningAnalysis = this.analyzePlanning(interactions, attempts);\r\n      const flexibilityAnalysis = this.analyzeFlexibility(interactions, errors);\r\n      const persistenceAnalysis = this.analyzePersistence(interactions, errors, completionTime);\r\n\r\n      // Compilar resultados\r\n      const problemSolvingAnalysis = {\r\n        strategies: strategyAnalysis,\r\n        planning: planningAnalysis,\r\n        cognitiveFlexibility: flexibilityAnalysis,\r\n        persistence: persistenceAnalysis,\r\n        overallProblemSolvingScore: this.calculateOverallScore([\r\n          strategyAnalysis.score,\r\n          planningAnalysis.score,\r\n          flexibilityAnalysis.score,\r\n          persistenceAnalysis.score\r\n        ]),\r\n        timestamp: Date.now()\r\n      };\r\n\r\n      return problemSolvingAnalysis;\r\n    } catch (error) {\r\n      console.error('ProblemSolvingCollector - Erro durante análise:', error);\r\n      return this.getDefaultMetrics();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Retorna métricas padrão quando não há dados suficientes\r\n   */\r\n  getDefaultMetrics() {\r\n    return {\r\n      strategies: { score: 0.5, type: 'mixed' },\r\n      planning: { score: 0.5, level: 'average' },\r\n      cognitiveFlexibility: { score: 0.5, level: 'average' },\r\n      persistence: { score: 0.5, level: 'average' },\r\n      overallProblemSolvingScore: 0.5,\r\n      timestamp: Date.now()\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calcula pontuação geral de resolução de problemas\r\n   */\r\n  calculateOverallScore(scores) {\r\n    if (!scores || !scores.length) return 0.5;\r\n    return scores.reduce((sum, score) => sum + score, 0) / scores.length;\r\n  }\r\n\r\n  /**\r\n   * Analisa estratégias de resolução\r\n   */\r\n  analyzeStrategies(interactions, completionTime) {\r\n    // Implementação simplificada para testes\r\n    return {\r\n      score: 0.75,\r\n      type: 'systematic',\r\n      details: {\r\n        efficiency: 0.7,\r\n        consistency: 0.8,\r\n        adaptability: 0.75\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa habilidades de planejamento\r\n   */\r\n  analyzePlanning(interactions, attempts) {\r\n    // Implementação simplificada para testes\r\n    return {\r\n      score: 0.7,\r\n      level: 'good',\r\n      details: {\r\n        sequencing: 0.75,\r\n        organization: 0.65,\r\n        foresight: 0.7\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa flexibilidade cognitiva\r\n   */\r\n  analyzeFlexibility(interactions, errors) {\r\n    // Implementação simplificada para testes\r\n    return {\r\n      score: 0.6,\r\n      level: 'above_average',\r\n      details: {\r\n        strategyShifting: 0.6,\r\n        adaptability: 0.65,\r\n        errorRecovery: 0.55\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa persistência e tolerância à frustração\r\n   */\r\n  analyzePersistence(interactions, errors, completionTime) {\r\n    // Implementação simplificada para testes\r\n    return {\r\n      score: 0.8,\r\n      level: 'high',\r\n      details: {\r\n        frustrationTolerance: 0.75,\r\n        taskPersistence: 0.85,\r\n        motivation: 0.8\r\n      }\r\n    };\r\n  }\r\n}\r\n", "/**\r\n * 👁️ COLETOR VISUAL-ESPACIAL\r\n * Analisa capacidades visuais e espaciais específicas\r\n */\r\n\r\nexport class VisualSpatialCollector {\r\n  constructor() {\r\n    this.collectorId = 'visual-spatial-collector';\r\n    this.version = '3.0.0';\r\n    this.initialized = true;\r\n  }\r\n  \r\n  async collect(gameData) {\r\n    try {\r\n      const metrics = gameData.metrics || {};\r\n      const interactions = gameData.interactions || [];\r\n      \r\n      const visualSpatialScore = this.calculateVisualSpatialScore(metrics);\r\n      const patternRecognition = this.assessPatternRecognition(metrics);\r\n      const spatialOrientation = this.assessSpatialOrientation(interactions);\r\n      \r\n      return {\r\n        score: visualSpatialScore,\r\n        patternRecognition,\r\n        spatialOrientation,\r\n        insights: ['Habilidades visual-espaciais avaliadas'],\r\n        recommendations: visualSpatialScore < 0.6 \r\n          ? ['Exercícios de visualização espacial']\r\n          : ['Quebra-cabeças 3D avançados']\r\n      };\r\n    } catch (error) {\r\n      console.error('👁️ Erro no VisualSpatialCollector:', error);\r\n      return { score: 0.5, error: error.message };\r\n    }\r\n  }\r\n  \r\n  calculateVisualSpatialScore(metrics) {\r\n    const visualAccuracy = metrics.visualAccuracy || 0.5;\r\n    const spatialAccuracy = metrics.spatialAccuracy || 0.5;\r\n    const processingSpeed = this.calculateProcessingSpeed(metrics);\r\n    return (visualAccuracy + spatialAccuracy + processingSpeed) / 3;\r\n  }\r\n  \r\n  calculateProcessingSpeed(metrics) {\r\n    const averageTime = metrics.averageResponseTime || 3000;\r\n    return Math.max(0, Math.min(1, (5000 - averageTime) / 5000));\r\n  }\r\n  \r\n  assessPatternRecognition(metrics) {\r\n    return metrics.patternRecognitionScore || 0.5;\r\n  }\r\n  \r\n  assessSpatialOrientation(interactions) {\r\n    if (interactions.length === 0) return 0.5;\r\n    \r\n    const correctOrientations = interactions.filter(i => i.correctOrientation).length;\r\n    return correctOrientations / interactions.length;\r\n  }\r\n}\r\n", "// ============================================================================\r\n// MOTOR SKILLS COLLECTOR - QUEBRA-CABEÇA\r\n// Coleta e análise de habilidades motoras finas durante manipulação de peças\r\n// ============================================================================\r\n\r\nimport { BaseCollector } from '../../../utils/BaseCollector.js';\r\n\r\nexport class MotorSkillsCollector extends BaseCollector {\r\n  constructor() {\r\n    super('MotorSkills');\r\n    \r\n    this.motorMetrics = {\r\n      // Precisão motora\r\n      pieceGraspAccuracy: [],\r\n      placementPrecision: [],\r\n      clickAccuracy: [],\r\n      motorStability: [],\r\n      \r\n      // Controle motor\r\n      movementSmoothness: [],\r\n      speedControl: [],\r\n      forceModulation: [],\r\n      coordinationControl: []\r\n    };\r\n\r\n    this.sessionData = {\r\n      startTime: null,\r\n      endTime: null,\r\n      totalPieceManipulations: 0,\r\n      successfulPlacements: 0,\r\n      failedPlacements: 0,\r\n      averageMovementTime: 0,\r\n      averageClickAccuracy: 0,\r\n      motorEfficiency: 0,\r\n      fatigueLevel: 0,\r\n      motorConsistency: 0\r\n    };\r\n  }\r\n\r\n  // ========================================================================\r\n  // MÉTODOS DE COLETA\r\n  // ========================================================================\r\n\r\n  async collect(gameData) {\r\n    try {\r\n      // Simular coleta de dados motores\r\n      const motorData = {\r\n        precision: this.analyzePrecision(gameData),\r\n        coordination: this.analyzeCoordination(gameData),\r\n        control: this.analyzeControl(gameData),\r\n        efficiency: this.calculateMotorEfficiency(gameData)\r\n      };\r\n\r\n      return {\r\n        timestamp: Date.now(),\r\n        gameType: 'QuebraCabeca',\r\n        motorData: motorData,\r\n        score: this.calculateOverallScore(motorData)\r\n      };\r\n    } catch (error) {\r\n      console.error('Erro na coleta de dados motores:', error);\r\n      return {\r\n        timestamp: Date.now(),\r\n        gameType: 'QuebraCabeca',\r\n        error: error.message,\r\n        score: 0.5\r\n      };\r\n    }\r\n  }\r\n\r\n  analyzePrecision(gameData) {\r\n    return {\r\n      clickAccuracy: 0.8,\r\n      placementPrecision: 0.75,\r\n      graspAccuracy: 0.7,\r\n      stability: 0.65\r\n    };\r\n  }\r\n\r\n  analyzeCoordination(gameData) {\r\n    return {\r\n      handEyeCoordination: 0.75,\r\n      bimanualCoordination: 0.7,\r\n      spatialCoordination: 0.8,\r\n      temporalCoordination: 0.65\r\n    };\r\n  }\r\n\r\n  analyzeControl(gameData) {\r\n    return {\r\n      movementSmoothness: 0.7,\r\n      speedControl: 0.75,\r\n      forceModulation: 0.6,\r\n      directionalControl: 0.8\r\n    };\r\n  }\r\n\r\n  calculateMotorEfficiency(gameData) {\r\n    return 0.72;\r\n  }\r\n\r\n  calculateOverallScore(motorData) {\r\n    const scores = [\r\n      motorData.precision.clickAccuracy,\r\n      motorData.coordination.handEyeCoordination,\r\n      motorData.control.movementSmoothness,\r\n      motorData.efficiency\r\n    ];\r\n\r\n    return scores.reduce((sum, score) => sum + score, 0) / scores.length;\r\n  }\r\n\r\n  // ========================================================================\r\n  // MÉTODOS DE ANÁLISE\r\n  // ========================================================================\r\n\r\n  generateReport() {\r\n    return {\r\n      precision: this.motorMetrics.placementPrecision,\r\n      coordination: this.motorMetrics.coordinationControl,\r\n      control: this.motorMetrics.movementSmoothness,\r\n      efficiency: this.sessionData.motorEfficiency,\r\n      recommendations: this.generateRecommendations()\r\n    };\r\n  }\r\n\r\n  generateRecommendations() {\r\n    return [\r\n      'Praticar exercícios de coordenação motora fina',\r\n      'Desenvolver controle de movimento com atividades precisas',\r\n      'Fortalecer estabilidade motora com treino específico'\r\n    ];\r\n  }\r\n\r\n  getActivityScore() {\r\n    return Math.round(this.sessionData.motorEfficiency * 1000);\r\n  }\r\n}\r\n\r\nexport default MotorSkillsCollector;\r\n", "// ============================================================================\r\n// PATTERN RECOGNITION COLLECTOR - QUEBRA-CABEÇA\r\n// Coleta e análise de reconhecimento de padrões visuais em quebra-cabeças\r\n// ============================================================================\r\n\r\nimport { BaseCollector } from '../../../utils/BaseCollector.js';\r\n\r\nexport class PatternRecognitionCollector extends BaseCollector {\r\n  constructor() {\r\n    super('PatternRecognition');\r\n    \r\n    this.patternMetrics = {\r\n      // Reconhecimento de padrões visuais\r\n      colorPatterns: [],\r\n      shapePatterns: [],\r\n      texturePatterns: [],\r\n      edgePatterns: [],\r\n      \r\n      // Análise de fragmentos\r\n      fragmentAnalysis: [],\r\n      pieceClassification: [],\r\n      contextualClues: [],\r\n      visualSimilarity: []\r\n    };\r\n\r\n    this.sessionData = {\r\n      startTime: null,\r\n      endTime: null,\r\n      totalPatternRecognitions: 0,\r\n      correctRecognitions: 0,\r\n      incorrectRecognitions: 0,\r\n      averageRecognitionTime: 0,\r\n      patternComplexityHandled: 0,\r\n      recognitionAccuracy: 0,\r\n      scanningEfficiency: 0,\r\n      visualProcessingSpeed: 0\r\n    };\r\n  }\r\n\r\n  // ========================================================================\r\n  // MÉTODOS DE COLETA\r\n  // ========================================================================\r\n\r\n  async collect(gameData) {\r\n    try {\r\n      // Simular coleta de dados de reconhecimento de padrões\r\n      const patternData = {\r\n        colorRecognition: this.analyzeColorRecognition(gameData),\r\n        shapeRecognition: this.analyzeShapeRecognition(gameData),\r\n        patternMatching: this.analyzePatternMatching(gameData),\r\n        scanningEfficiency: this.calculateScanningEfficiency(gameData)\r\n      };\r\n\r\n      return {\r\n        timestamp: Date.now(),\r\n        gameType: 'QuebraCabeca',\r\n        patternData: patternData,\r\n        score: this.calculateOverallScore(patternData)\r\n      };\r\n    } catch (error) {\r\n      console.error('Erro na coleta de dados de reconhecimento de padrões:', error);\r\n      return {\r\n        timestamp: Date.now(),\r\n        gameType: 'QuebraCabeca',\r\n        error: error.message,\r\n        score: 0.5\r\n      };\r\n    }\r\n  }\r\n\r\n  analyzeColorRecognition(gameData) {\r\n    return {\r\n      colorAccuracy: 0.8,\r\n      colorDiscrimination: 0.75,\r\n      colorMatching: 0.85,\r\n      colorMemory: 0.7\r\n    };\r\n  }\r\n\r\n  analyzeShapeRecognition(gameData) {\r\n    return {\r\n      shapeAccuracy: 0.78,\r\n      edgeRecognition: 0.82,\r\n      cornerDetection: 0.75,\r\n      shapeClassification: 0.8\r\n    };\r\n  }\r\n\r\n  analyzePatternMatching(gameData) {\r\n    return {\r\n      matchingAccuracy: 0.77,\r\n      patternComplexity: 0.6,\r\n      visualSimilarity: 0.73,\r\n      contextualClues: 0.68\r\n    };\r\n  }\r\n\r\n  calculateScanningEfficiency(gameData) {\r\n    return 0.74;\r\n  }\r\n\r\n  calculateOverallScore(patternData) {\r\n    const scores = [\r\n      patternData.colorRecognition.colorAccuracy,\r\n      patternData.shapeRecognition.shapeAccuracy,\r\n      patternData.patternMatching.matchingAccuracy,\r\n      patternData.scanningEfficiency\r\n    ];\r\n\r\n    return scores.reduce((sum, score) => sum + score, 0) / scores.length;\r\n  }\r\n\r\n  // ========================================================================\r\n  // MÉTODOS DE ANÁLISE\r\n  // ========================================================================\r\n\r\n  generateReport() {\r\n    return {\r\n      colorPatterns: this.patternMetrics.colorPatterns,\r\n      shapePatterns: this.patternMetrics.shapePatterns,\r\n      scanningEfficiency: this.sessionData.scanningEfficiency,\r\n      recognitionAccuracy: this.sessionData.recognitionAccuracy,\r\n      recommendations: this.generateRecommendations()\r\n    };\r\n  }\r\n\r\n  generateRecommendations() {\r\n    return [\r\n      'Praticar exercícios de reconhecimento de padrões visuais',\r\n      'Desenvolver habilidades de varredura visual sistemática',\r\n      'Fortalecer discriminação de cores e formas'\r\n    ];\r\n  }\r\n\r\n  getActivityScore() {\r\n    return Math.round(this.sessionData.recognitionAccuracy * 1000);\r\n  }\r\n}\r\n\r\nexport default PatternRecognitionCollector;\r\n", "// ============================================================================\r\n// MEMORY COLLECTOR - QUEBRA-CABEÇA\r\n// Coleta e análise de memória visual e espacial durante o jogo de quebra-cabeça\r\n// ============================================================================\r\n\r\nimport { BaseCollector } from '../../../utils/BaseCollector.js';\r\n\r\nexport class MemoryCollector extends BaseCollector {\r\n  constructor() {\r\n    super('Memory');\r\n    \r\n    this.memoryMetrics = {\r\n      // Memória visual\r\n      visualMemory: [],\r\n      imageRecall: [],\r\n      visualRecognition: [],\r\n      visualRetention: [],\r\n      \r\n      // Memória espacial\r\n      spatialMemory: [],\r\n      locationRecall: [],\r\n      spatialRelationships: [],\r\n      spatialMapping: [],\r\n      \r\n      // Memória de trabalho\r\n      workingMemory: [],\r\n      temporaryStorage: [],\r\n      informationManipulation: [],\r\n      memoryUpdating: [],\r\n      \r\n      // Memória a longo prazo\r\n      longTermMemory: [],\r\n      patternStorage: [],\r\n      strategicMemory: [],\r\n      proceduralMemory: []\r\n    };\r\n\r\n    this.sessionData = {\r\n      startTime: null,\r\n      endTime: null,\r\n      totalMemoryOperations: 0,\r\n      successfulRecalls: 0,\r\n      failedRecalls: 0,\r\n      averageRecallTime: 0,\r\n      memoryCapacityUsed: 0,\r\n      memoryEfficiency: 0,\r\n      interferenceLevels: 0,\r\n      forgettingRate: 0\r\n    };\r\n  }\r\n\r\n  // ========================================================================\r\n  // MÉTODOS DE COLETA\r\n  // ========================================================================\r\n\r\n  async collect(gameData) {\r\n    try {\r\n      // Simular coleta de dados de memória\r\n      const memoryData = {\r\n        visualMemory: this.analyzeVisualMemory(gameData),\r\n        spatialMemory: this.analyzeSpatialMemory(gameData),\r\n        workingMemory: this.analyzeWorkingMemory(gameData),\r\n        memoryEfficiency: this.calculateMemoryEfficiency(gameData)\r\n      };\r\n\r\n      return {\r\n        timestamp: Date.now(),\r\n        gameType: 'QuebraCabeca',\r\n        memoryData: memoryData,\r\n        score: this.calculateOverallScore(memoryData)\r\n      };\r\n    } catch (error) {\r\n      console.error('Erro na coleta de dados de memória:', error);\r\n      return {\r\n        timestamp: Date.now(),\r\n        gameType: 'QuebraCabeca',\r\n        error: error.message,\r\n        score: 0.5\r\n      };\r\n    }\r\n  }\r\n\r\n  analyzeVisualMemory(gameData) {\r\n    return {\r\n      visualRecall: 0.7,\r\n      imageRecognition: 0.6,\r\n      visualRetention: 0.8,\r\n      patternMemory: 0.65\r\n    };\r\n  }\r\n\r\n  analyzeSpatialMemory(gameData) {\r\n    return {\r\n      locationMemory: 0.75,\r\n      spatialRelations: 0.7,\r\n      spatialMapping: 0.6,\r\n      spatialSpan: 5\r\n    };\r\n  }\r\n\r\n  analyzeWorkingMemory(gameData) {\r\n    return {\r\n      capacity: 4,\r\n      efficiency: 0.7,\r\n      manipulation: 0.6,\r\n      updating: 0.65\r\n    };\r\n  }\r\n\r\n  calculateMemoryEfficiency(gameData) {\r\n    return 0.7;\r\n  }\r\n\r\n  calculateOverallScore(memoryData) {\r\n    const scores = [\r\n      memoryData.visualMemory.visualRecall,\r\n      memoryData.spatialMemory.locationMemory,\r\n      memoryData.workingMemory.efficiency,\r\n      memoryData.memoryEfficiency\r\n    ];\r\n\r\n    return scores.reduce((sum, score) => sum + score, 0) / scores.length;\r\n  }\r\n\r\n  // ========================================================================\r\n  // MÉTODOS DE ANÁLISE\r\n  // ========================================================================\r\n\r\n  generateReport() {\r\n    return {\r\n      visualMemory: this.memoryMetrics.visualMemory,\r\n      spatialMemory: this.memoryMetrics.spatialMemory,\r\n      workingMemory: this.memoryMetrics.workingMemory,\r\n      efficiency: this.sessionData.memoryEfficiency,\r\n      recommendations: this.generateRecommendations()\r\n    };\r\n  }\r\n\r\n  generateRecommendations() {\r\n    return [\r\n      'Praticar exercícios de memória visual',\r\n      'Desenvolver memória espacial com jogos de localização',\r\n      'Fortalecer memória de trabalho com tarefas duplas'\r\n    ];\r\n  }\r\n\r\n  getActivityScore() {\r\n    return Math.round(this.sessionData.memoryEfficiency * 1000);\r\n  }\r\n}\r\n\r\nexport default MemoryCollector;\r\n", "// ============================================================================\r\n// PERCEPTUAL PROCESSING COLLECTOR - QUEBRA-CABEÇA\r\n// Coleta e análise de processamento perceptual visual durante o quebra-cabeça\r\n// ============================================================================\r\n\r\nimport { BaseCollector } from '../../../utils/BaseCollector.js';\r\n\r\nexport class PerceptualProcessingCollector extends BaseCollector {\r\n  constructor() {\r\n    super('PerceptualProcessing');\r\n    \r\n    this.perceptualMetrics = {\r\n      // Processamento visual básico\r\n      visualAcuity: [],\r\n      contrastSensitivity: [],\r\n      colorDiscrimination: [],\r\n      motionDetection: [],\r\n      \r\n      // Processamento de formas e objetos\r\n      shapeProcessing: [],\r\n      objectRecognition: [],\r\n      figureGroundSeparation: [],\r\n      visualClosure: []\r\n    };\r\n\r\n    this.sessionData = {\r\n      startTime: null,\r\n      endTime: null,\r\n      totalPerceptualOperations: 0,\r\n      averageProcessingTime: 0,\r\n      perceptualAccuracy: 0,\r\n      visualEfficiency: 0,\r\n      processingSpeed: 0,\r\n      integrationScore: 0,\r\n      perceptualLoad: 0\r\n    };\r\n  }\r\n\r\n  // ========================================================================\r\n  // MÉTODOS DE COLETA\r\n  // ========================================================================\r\n\r\n  async collect(gameData) {\r\n    try {\r\n      // Simular coleta de dados de processamento perceptual\r\n      const perceptualData = {\r\n        visualProcessing: this.analyzeVisualProcessing(gameData),\r\n        objectRecognition: this.analyzeObjectRecognition(gameData),\r\n        perceptualIntegration: this.analyzePerceptualIntegration(gameData),\r\n        processingEfficiency: this.calculateProcessingEfficiency(gameData)\r\n      };\r\n\r\n      return {\r\n        timestamp: Date.now(),\r\n        gameType: 'QuebraCabeca',\r\n        perceptualData: perceptualData,\r\n        score: this.calculateOverallScore(perceptualData)\r\n      };\r\n    } catch (error) {\r\n      console.error('Erro na coleta de dados de processamento perceptual:', error);\r\n      return {\r\n        timestamp: Date.now(),\r\n        gameType: 'QuebraCabeca',\r\n        error: error.message,\r\n        score: 0.5\r\n      };\r\n    }\r\n  }\r\n\r\n  analyzeVisualProcessing(gameData) {\r\n    return {\r\n      visualAcuity: 0.82,\r\n      contrastSensitivity: 0.78,\r\n      colorDiscrimination: 0.85,\r\n      motionDetection: 0.73\r\n    };\r\n  }\r\n\r\n  analyzeObjectRecognition(gameData) {\r\n    return {\r\n      objectRecognition: 0.79,\r\n      shapeProcessing: 0.77,\r\n      figureGroundSeparation: 0.74,\r\n      visualClosure: 0.81\r\n    };\r\n  }\r\n\r\n  analyzePerceptualIntegration(gameData) {\r\n    return {\r\n      featureIntegration: 0.76,\r\n      perceptualBinding: 0.72,\r\n      contextualProcessing: 0.78,\r\n      gestaltProcessing: 0.75\r\n    };\r\n  }\r\n\r\n  calculateProcessingEfficiency(gameData) {\r\n    return 0.77;\r\n  }\r\n\r\n  calculateOverallScore(perceptualData) {\r\n    const scores = [\r\n      perceptualData.visualProcessing.visualAcuity,\r\n      perceptualData.objectRecognition.objectRecognition,\r\n      perceptualData.perceptualIntegration.featureIntegration,\r\n      perceptualData.processingEfficiency\r\n    ];\r\n\r\n    return scores.reduce((sum, score) => sum + score, 0) / scores.length;\r\n  }\r\n\r\n  // ========================================================================\r\n  // MÉTODOS DE ANÁLISE\r\n  // ========================================================================\r\n\r\n  generateReport() {\r\n    return {\r\n      visualAcuity: this.perceptualMetrics.visualAcuity,\r\n      objectRecognition: this.perceptualMetrics.objectRecognition,\r\n      processingEfficiency: this.sessionData.visualEfficiency,\r\n      integrationScore: this.sessionData.integrationScore,\r\n      recommendations: this.generateRecommendations()\r\n    };\r\n  }\r\n\r\n  generateRecommendations() {\r\n    return [\r\n      'Praticar exercícios de processamento visual',\r\n      'Desenvolver habilidades de reconhecimento de objetos',\r\n      'Fortalecer integração perceptual com atividades específicas'\r\n    ];\r\n  }\r\n\r\n  getActivityScore() {\r\n    return Math.round(this.sessionData.visualEfficiency * 1000);\r\n  }\r\n}\r\n\r\nexport default PerceptualProcessingCollector;\r\n", "/**\r\n * 🧩 QUEBRA-CABEÇA COLLECTORS HUB\r\n * Portal Betina V3 - Hub de coletores para jogos de quebra-cabeça\r\n * Coleta dados sobre raciocínio espacial, resolução de problemas e processamento visual-espacial\r\n */\r\n\r\nimport { SpatialReasoningCollector } from './SpatialReasoningCollector.js';\r\nimport { ProblemSolvingCollector } from './ProblemSolvingCollector.js';\r\nimport { VisualSpatialCollector } from './VisualSpatialCollector.js';\r\nimport { MotorSkillsCollector } from './MotorSkillsCollector.js';\r\nimport { PatternRecognitionCollector } from './PatternRecognitionCollector.js';\r\nimport { MemoryCollector } from './MemoryCollector.js';\r\nimport { PerceptualProcessingCollector } from './PerceptualProcessingCollector.js';\r\n\r\nexport class QuebraCabecaCollectorsHub {\r\n  constructor() {\r\n    this.hubId = 'quebra-cabeca-collectors-hub';\r\n    this.version = '3.0.0';\r\n    this.gameType = 'QuebraCabeca';\r\n    \r\n    // Inicializar coletores especializados\r\n    this._collectors = {\r\n      spatialReasoning: new SpatialReasoningCollector(),\r\n      problemSolving: new ProblemSolvingCollector(),\r\n      visualSpatial: new VisualSpatialCollector(),\r\n      motorSkills: new MotorSkillsCollector(),\r\n      patternRecognition: new PatternRecognitionCollector(),\r\n      memory: new MemoryCollector(),\r\n      perceptualProcessing: new PerceptualProcessingCollector()\r\n    };\r\n    \r\n    this.sessionData = [];\r\n    this.analysisCache = new Map();\r\n    this.isInitialized = true;\r\n\r\n    console.log('🧩 QuebraCabecaCollectorsHub inicializado v3.0.0');\r\n  }\r\n\r\n  /**\r\n   * Getter para coletores - necessário para GameSpecificProcessors\r\n   */\r\n  get collectors() {\r\n    return this._collectors;\r\n  }\r\n  \r\n  /**\r\n   * Executa análise completa dos dados do quebra-cabeça\r\n   */\r\n  async runCompleteAnalysis(gameData) {\r\n    try {\r\n      const timestamp = Date.now();\r\n      const sessionId = gameData.sessionId || `session_${timestamp}`;\r\n      \r\n      // Coletar dados de cada coletor\r\n      const spatialAnalysis = await this.collectors.spatialReasoning.collect(gameData);\r\n      const problemSolvingAnalysis = await this.collectors.problemSolving.collect(gameData);\r\n      const visualSpatialAnalysis = await this.collectors.visualSpatial.collect(gameData);\r\n      const motorSkillsAnalysis = await this.collectors.motorSkills.collect(gameData);\r\n      const patternRecognitionAnalysis = await this.collectors.patternRecognition.collect(gameData);\r\n      const memoryAnalysis = await this.collectors.memory.collect(gameData);\r\n      const perceptualProcessingAnalysis = await this.collectors.perceptualProcessing.collect(gameData);\r\n      \r\n      const completeAnalysis = {\r\n        timestamp,\r\n        sessionId,\r\n        gameType: 'QuebraCabeca',\r\n        spatialReasoning: spatialAnalysis,\r\n        problemSolving: problemSolvingAnalysis,\r\n        visualSpatial: visualSpatialAnalysis,\r\n        motorSkills: motorSkillsAnalysis,\r\n        patternRecognition: patternRecognitionAnalysis,\r\n        memory: memoryAnalysis,\r\n        perceptualProcessing: perceptualProcessingAnalysis,\r\n        overallPerformance: this.calculateOverallPerformance({\r\n          spatialAnalysis,\r\n          problemSolvingAnalysis, \r\n          visualSpatialAnalysis,\r\n          motorSkillsAnalysis,\r\n          patternRecognitionAnalysis,\r\n          memoryAnalysis,\r\n          perceptualProcessingAnalysis\r\n        }),\r\n        insights: this.generateInsights({\r\n          spatialAnalysis,\r\n          problemSolvingAnalysis,\r\n          visualSpatialAnalysis,\r\n          motorSkillsAnalysis,\r\n          patternRecognitionAnalysis,\r\n          memoryAnalysis,\r\n          perceptualProcessingAnalysis\r\n        }),\r\n        recommendations: this.generateRecommendations({\r\n          spatialAnalysis,\r\n          problemSolvingAnalysis,\r\n          visualSpatialAnalysis,\r\n          motorSkillsAnalysis,\r\n          patternRecognitionAnalysis,\r\n          memoryAnalysis,\r\n          perceptualProcessingAnalysis\r\n        })\r\n      };\r\n      \r\n      this.sessionData.push(completeAnalysis);\r\n      return completeAnalysis;\r\n      \r\n    } catch (error) {\r\n      console.error('🧩 Erro na análise completa do QuebraCabeca:', error);\r\n      return {\r\n        error: error.message,\r\n        timestamp: Date.now(),\r\n        gameType: 'QuebraCabeca'\r\n      };\r\n    }\r\n  }\r\n  \r\n  /**\r\n   * Calcula desempenho geral\r\n   */\r\n  calculateOverallPerformance(analyses) {\r\n    try {\r\n      const scores = Object.values(analyses).map(analysis => analysis.score || 0.5);\r\n      const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;\r\n      \r\n      return {\r\n        score: averageScore,\r\n        level: averageScore > 0.8 ? 'Avançado' : averageScore > 0.6 ? 'Intermediário' : 'Básico',\r\n        strengths: this.identifyStrengths(analyses),\r\n        challenges: this.identifyChallenges(analyses)\r\n      };\r\n    } catch (error) {\r\n      console.error('🧩 Erro ao calcular desempenho geral:', error);\r\n      return { score: 0.5, level: 'Básico', strengths: [], challenges: [] };\r\n    }\r\n  }\r\n  \r\n  /**\r\n   * Identifica pontos fortes\r\n   */\r\n  identifyStrengths(analyses) {\r\n    const strengths = [];\r\n    if (analyses.spatialAnalysis?.score > 0.7) strengths.push('Raciocínio espacial forte');\r\n    if (analyses.problemSolvingAnalysis?.score > 0.7) strengths.push('Boa resolução de problemas');\r\n    if (analyses.visualSpatialAnalysis?.score > 0.7) strengths.push('Processamento visual-espacial eficiente');\r\n    if (analyses.motorSkillsAnalysis?.score > 0.7) strengths.push('Habilidades motoras finas desenvolvidas');\r\n    if (analyses.patternRecognitionAnalysis?.score > 0.7) strengths.push('Excelente reconhecimento de padrões');\r\n    if (analyses.memoryAnalysis?.score > 0.7) strengths.push('Memória visual e espacial eficaz');\r\n    if (analyses.perceptualProcessingAnalysis?.score > 0.7) strengths.push('Processamento perceptual avançado');\r\n    return strengths;\r\n  }\r\n  \r\n  /**\r\n   * Identifica desafios\r\n   */\r\n  identifyChallenges(analyses) {\r\n    const challenges = [];\r\n    if (analyses.spatialAnalysis?.score < 0.5) challenges.push('Raciocínio espacial precisa de desenvolvimento');\r\n    if (analyses.problemSolvingAnalysis?.score < 0.5) challenges.push('Resolução de problemas requer prática');\r\n    if (analyses.visualSpatialAnalysis?.score < 0.5) challenges.push('Processamento visual-espacial precisa de suporte');\r\n    if (analyses.motorSkillsAnalysis?.score < 0.5) challenges.push('Habilidades motoras finas necessitam treino');\r\n    if (analyses.patternRecognitionAnalysis?.score < 0.5) challenges.push('Reconhecimento de padrões precisa ser fortalecido');\r\n    if (analyses.memoryAnalysis?.score < 0.5) challenges.push('Memória visual e espacial requer desenvolvimento');\r\n    if (analyses.perceptualProcessingAnalysis?.score < 0.5) challenges.push('Processamento perceptual necessita aprimoramento');\r\n    return challenges;\r\n  }\r\n  \r\n  /**\r\n   * Gera insights terapêuticos\r\n   */\r\n  generateInsights(analyses) {\r\n    const insights = [\r\n      'Quebra-cabeça desenvolve raciocínio espacial e resolução de problemas',\r\n      'Atividade estimula processamento visual-espacial e memória',\r\n      'Jogos de puzzle fortalecem persistência e paciência',\r\n      'Manipulação de peças desenvolve habilidades motoras finas',\r\n      'Reconhecimento de padrões aprimora processamento visual',\r\n      'Integração perceptual é exercitada através da montagem',\r\n      'Memória de trabalho é constantemente desafiada'\r\n    ];\r\n    \r\n    return insights;\r\n  }\r\n  \r\n  /**\r\n   * Gera recomendações\r\n   */\r\n  generateRecommendations(analyses) {\r\n    const recommendations = [];\r\n    \r\n    if (analyses.spatialAnalysis?.score < 0.6) {\r\n      recommendations.push('Praticar com quebra-cabeças mais simples primeiro');\r\n    }\r\n    \r\n    if (analyses.problemSolvingAnalysis?.score > 0.8) {\r\n      recommendations.push('Aumentar complexidade dos quebra-cabeças');\r\n    }\r\n    \r\n    if (analyses.motorSkillsAnalysis?.score < 0.6) {\r\n      recommendations.push('Incluir exercícios de coordenação motora fina');\r\n    }\r\n    \r\n    if (analyses.patternRecognitionAnalysis?.score < 0.6) {\r\n      recommendations.push('Praticar jogos de reconhecimento de padrões');\r\n    }\r\n    \r\n    if (analyses.memoryAnalysis?.score < 0.6) {\r\n      recommendations.push('Exercitar memória visual com atividades específicas');\r\n    }\r\n    \r\n    if (analyses.perceptualProcessingAnalysis?.score < 0.6) {\r\n      recommendations.push('Desenvolver processamento perceptual com exercícios visuais');\r\n    }\r\n    \r\n    recommendations.push('Combinar quebra-cabeças com atividades de construção');\r\n    \r\n    return recommendations;\r\n  }\r\n}\r\n\r\nexport default QuebraCabecaCollectorsHub;\r\n\r\n// Exportar coletores individuais para uso específico\r\nexport {\r\n  SpatialReasoningCollector,\r\n  ProblemSolvingCollector,\r\n  VisualSpatialCollector,\r\n  MotorSkillsCollector,\r\n  PatternRecognitionCollector,\r\n  MemoryCollector,\r\n  PerceptualProcessingCollector\r\n};\r\n", "/**\n * @file QuebraCabecaProcessors.js\n * @description Processadores específicos para o jogo Quebra-Cabeça\n * @version 3.0.0\n * <AUTHOR> Betina V3\n */\n\nimport { IGameProcessor } from '../IGameProcessor.js';\n\n// Detectar ambiente\nconst isBrowser = typeof window !== 'undefined' && typeof window.document !== 'undefined';\n\n// Logger adaptado ao ambiente\nconst logger = isBrowser \n  ? {\n      info: (...args) => console.info('%c🧩 [QUEBRA-CABECA]', 'color: #2196F3', new Date().toISOString(), ...args),\n      error: (...args) => console.error('%c🔴 [QUEBRA-ERROR]', 'color: #F44336', new Date().toISOString(), ...args),\n      warn: (...args) => console.warn('%c🟡 [QUEBRA-WARN]', 'color: #FF9800', new Date().toISOString(), ...args),\n      debug: (...args) => console.debug('%c⚪ [QUEBRA-DEBUG]', 'color: #9E9E9E', new Date().toISOString(), ...args),\n      therapeutic: (...args) => console.info('%c🏥 [QUEBRA-THERAPEUTIC]', 'color: #4CAF50', new Date().toISOString(), ...args)\n    }\n  : {\n      info: (...args) => console.info('🧩 [QUEBRA-CABECA]', new Date().toISOString(), ...args),\n      error: (...args) => console.error('🔴 [QUEBRA-ERROR]', new Date().toISOString(), ...args),\n      warn: (...args) => console.warn('🟡 [QUEBRA-WARN]', new Date().toISOString(), ...args),\n      debug: (...args) => console.debug('⚪ [QUEBRA-DEBUG]', new Date().toISOString(), ...args),\n      therapeutic: (...args) => console.info('🏥 [QUEBRA-THERAPEUTIC]', new Date().toISOString(), ...args)\n    };\n\n/**\n * @class QuebraCabecaProcessors\n * @description Processadores especializados para análise terapêutica do jogo Quebra-Cabeça\n */\nexport class QuebraCabecaProcessors extends IGameProcessor {\n  constructor(config = {}) {\n    // Configurações específicas para Quebra-Cabeça\n    const defaultConfig = {\n      category: 'spatial-reasoning',\n      therapeuticFocus: ['spatial_reasoning', 'problem_solving', 'fine_motor_skills'],\n      cognitiveAreas: ['spatial_processing', 'executive_function', 'motor_planning'],\n      thresholds: {\n        accuracy: 60,\n        responseTime: 8000,\n        engagement: 75\n      },\n      ...config\n    };\n    \n    super(defaultConfig);\n    \n    // Logger seguro com método therapeutic garantido\n    this.logger = config.logger && typeof config.logger.therapeutic === 'function' \n      ? config.logger \n      : this.logger || {\n          info: (...args) => console.info('🧩 [QUEBRA-CABECA]', new Date().toISOString(), ...args),\n          error: (...args) => console.error('🔴 [QUEBRA-ERROR]', new Date().toISOString(), ...args),\n          warn: (...args) => console.warn('🟡 [QUEBRA-WARN]', new Date().toISOString(), ...args),\n          debug: (...args) => console.debug('⚪ [QUEBRA-DEBUG]', new Date().toISOString(), ...args),\n          therapeutic: (...args) => console.info('🏥 [QUEBRA-THERAPEUTIC]', new Date().toISOString(), ...args)\n        }\n    \n    this.logger.info('🧩 Processadores Quebra-Cabeça inicializados')\n  };\n\n  /**\n   * Processa métricas específicas do QuebraCabeca\n   * @param {Object} gameData - Dados do jogo QuebraCabeca\n   * @param {Object} sessionData - Dados da sessão\n   * @returns {Object} Métricas processadas\n   */\n  async processQuebraCabecaMetrics(gameData, sessionData) {\n    try {\n      this.logger?.info('🧩 Processando métricas QuebraCabeca...', {\n        sessionId: sessionData.sessionId\n      });\n\n      const { attempts = [], totalTime = 0, completed = false, pieces = [] } = gameData;\n\n      // Métricas básicas\n      const totalAttempts = attempts.length || 1;\n      const correctPlacements = attempts.filter(a => a.correct).length;\n      const accuracy = Math.round((correctPlacements / totalAttempts) * 100);\n\n      // Métricas específicas do quebra-cabeça\n      const spatialReasoning = this.calculateSpatialReasoning(attempts, pieces);\n      const problemSolving = this.calculateProblemSolving(attempts, completed);\n      const persistence = this.calculatePersistence(attempts, totalTime);\n      const visualProcessing = this.calculateVisualProcessing(attempts, pieces);\n      const motorSkills = this.calculateMotorSkills(attempts);\n\n      const metrics = {\n        // Métricas básicas\n        accuracy,\n        totalAttempts,\n        correctPlacements,\n        completionTime: totalTime,\n        completed,\n\n        // Métricas específicas\n        spatialReasoning,\n        problemSolving,\n        persistence,\n        visualProcessing,\n        motorSkills,\n\n        // Métricas derivadas\n        efficiency: this.calculateEfficiency(correctPlacements, totalTime),\n        strategy: this.identifyStrategy(attempts),\n        difficultyHandling: this.assessDifficultyHandling(attempts, pieces)\n      };\n\n      this.logger?.info('✅ Métricas QuebraCabeca processadas', {\n        accuracy,\n        totalAttempts,\n        spatialReasoning: spatialReasoning.score\n      });\n\n      return metrics;\n    } catch (error) {\n      this.logger?.error('❌ Erro ao processar métricas QuebraCabeca:', error);\n      return this.generateFallbackMetrics(gameData);\n    }\n  }\n\n  /**\n   * Processa dados do jogo Quebra-Cabeça\n   * @param {Object} gameData - Dados coletados do jogo\n   * @param {Object} collectorsHub - Hub de coletores específico do jogo\n   * @returns {Promise<Object>} Análise terapêutica específica\n   */\n  async processGameData(gameData, collectorsHub = null) {\n    try {\n      this.logger?.info('🎮 Processando dados QuebraCabeca', {\n        sessionId: gameData.sessionId,\n        userId: gameData.userId,\n        collectorsActive: collectorsHub ? Object.keys(collectorsHub.collectors || {}).length : 0\n      });\n\n      // Processar métricas específicas do jogo\n      const metrics = await this.processQuebraCabecaMetrics(gameData, gameData);\n      \n      // Gerar análise terapêutica\n      const therapeuticAnalysis = this.generateTherapeuticAnalysis(metrics, gameData);\n      \n      // Processar métricas para estrutura padronizada\n      const processedMetrics = this.processMetricsForDatabase(metrics, gameData);\n\n      return {\n        success: true,\n        gameType: this.gameType,\n        metrics,\n        therapeuticAnalysis,\n        processedMetrics,\n        timestamp: new Date().toISOString()\n      };\n\n    } catch (error) {\n      this.logger?.error('❌ Erro ao processar dados QuebraCabeca:', error);\n      return {\n        success: false,\n        gameType: this.gameType,\n        error: error.message,\n        fallbackAnalysis: this.generateFallbackTherapeuticAnalysis(gameData),\n        timestamp: new Date().toISOString()\n      };\n    }\n  };\n\n  /**\n   * Processa coletores com Circuit Breaker para resiliência\n   * @param {Object} collectorsHub - Hub de coletores\n   * @param {Object} gameData - Dados do jogo\n   * @returns {Promise<Object>} Resultados dos coletores\n   */\n  async processCollectorsWithCircuitBreaker(collectorsHub, gameData) {\n    if (!collectorsHub || !collectorsHub.collectors) {\n      this.logger?.warn('⚠️ QuebraCabeca: Hub de coletores não disponível');\n      return { collectors: {}, warning: 'No collectors available' };\n    }\n\n    const results = {};\n    const collectors = collectorsHub.collectors;\n\n    // Processar cada coletor com tratamento de erro\n    for (const [collectorName, collector] of Object.entries(collectors)) {\n      try {\n        if (collector && typeof collector.analyze === 'function') {\n          this.logger?.debug('🎮 Processando coletor: ' + collectorName);\n          results[collectorName] = await this.processWithTimeout(\n            () => collector.analyze(gameData),\n            5000, // 5 segundos timeout\n            collectorName + ' timeout'\n          );\n        } else {\n          this.logger?.warn('⚠️ Coletor ' + collectorName + ' não tem método analyze');\n          results[collectorName] = { error: 'No analyze method' };\n        }\n      } catch (error) {\n        this.logger?.error('❌ QuebraCabecaProcessors: Erro no coletor ' + collectorName + ':', {\n          error: error.message,\n          stack: error.stack?.substring(0, 300),\n          collectorName,\n          timestamp: new Date().toISOString()\n        });\n        results[collectorName] = {\n          error: error.message,\n          fallback: this.generateFallbackMetrics(collectorName, gameData),\n          recovered: true\n        };\n      }\n    }\n\n    return {\n      collectors: results,\n      processedAt: new Date().toISOString(),\n      gameType: 'QuebraCabeca'\n    };\n  };\n\n  /**\n   * Processa com timeout para evitar travamentos\n   */\n  async processWithTimeout(fn, timeout, errorMsg) {\n    return Promise.race([\n      fn(),\n      new Promise((_, reject) => \n        setTimeout(() => reject(new Error(errorMsg)), timeout)\n      )\n    ]);\n  };\n\n  /**\n   * Gera métricas de fallback em caso de erro\n   */\n  generateFallbackMetrics(collectorName, gameData) {\n    return {\n      fallback: true,\n      collector: collectorName,\n      basicScore: 50,\n      confidence: 'low',\n      note: 'Generated due to collector error'\n    };\n  };\n  /**\n   * Gera análise terapêutica completa\n   * @param {Object} metrics - Métricas processadas\n   * @param {Object} gameData - Dados do jogo\n   * @returns {Object} Análise terapêutica\n   */\n  generateTherapeuticAnalysis(metrics, gameData) {\n    try {\n      const analysis = {\n        // Análise comportamental\n        behavioral: {\n          engagement: this.calculateEngagementScore(gameData),\n          persistence: this.calculatePersistenceScore(gameData),\n          adaptability: this.calculateAdaptabilityScore(gameData),\n          frustrationTolerance: this.calculateFrustrationTolerance(gameData),\n          socialInteraction: this.calculateSocialInteractionScore(gameData)\n        },\n        \n        // Análise cognitiva\n        cognitive: {\n          attention: this.calculateAttentionScore(gameData),\n          memory: this.calculateMemoryScore(gameData),\n          processingSpeed: this.calculateProcessingSpeedScore(gameData),\n          executiveFunction: this.calculateExecutiveFunctionScore(gameData),\n          visualProcessing: this.calculateVisualProcessingScore(gameData)\n        },\n        \n        // Análise sensorial\n        sensory: {\n          visualPerception: this.calculateVisualPerceptionScore(gameData),\n          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),\n          tactileProcessing: this.calculateTactileProcessingScore(gameData),\n          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)\n        },\n        \n        // Análise motora\n        motor: {\n          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),\n          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),\n          coordination: this.calculateCoordinationScore(gameData),\n          motorPlanning: this.calculateMotorPlanningScore(gameData)\n        },\n        \n        // Recomendações terapêuticas\n        recommendations: this.generateTherapeuticRecommendations(metrics, gameData),\n        \n        // Indicadores de progresso\n        progressIndicators: this.generateProgressIndicators(metrics, gameData),\n        \n        // Insights específicos do jogo\n        gameSpecificInsights: this.generateGameSpecificInsights(metrics, gameData),\n        \n        // Metadados\n        metadata: {\n          analysisTimestamp: new Date().toISOString(),\n          gameType: this.gameType,\n          analysisVersion: '3.0.0',\n          confidenceScore: this.calculateAnalysisConfidenceScore(metrics, gameData)\n        }\n      };\n      \n      return analysis;\n    } catch (error) {\n      this.logger?.error('❌ Erro ao gerar análise terapêutica:', error);\n      return this.generateFallbackTherapeuticAnalysis(gameData);\n    }\n  };\n  /**\n   * Métodos de cálculo de scores terapêuticos\n   */\n  calculateEngagementScore(gameData) {\n    const interactions = gameData.interactions || [];\n    const totalTime = gameData.totalTime || 1000;\n    const completionRate = gameData.completionRate || 0;\n    \n    let score = 50; // Base score\n    \n    // Fator de interação\n    if (interactions.length > 0) {\n      score += Math.min(30, interactions.length * 2);\n    }\n    \n    // Fator de tempo\n    if (totalTime > 30000) { // Mais de 30 segundos\n      score += 10;\n    }\n    \n    // Fator de completude\n    score += completionRate * 10;\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculatePersistenceScore(gameData) {\n    const attempts = gameData.attempts || 1;\n    const errors = gameData.errors || 0;\n    const completion = gameData.completion || 0;\n    \n    let score = 50;\n    \n    if (attempts > 1 && completion > 0.5) {\n      score += 20; // Persistiu após tentativas\n    }\n    \n    if (errors > 0 && completion > 0.8) {\n      score += 15; // Superou erros\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateAdaptabilityScore(gameData) {\n    const difficultyChanges = gameData.difficultyChanges || 0;\n    const adaptationSuccess = gameData.adaptationSuccess || 0;\n    \n    let score = 50;\n    \n    if (difficultyChanges > 0) {\n      score += adaptationSuccess * 20;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateFrustrationTolerance(gameData) {\n    const errors = gameData.errors || 0;\n    const quitEarly = gameData.quitEarly || false;\n    const completion = gameData.completion || 0;\n    \n    let score = 70; // Base high score\n    \n    if (errors > 3 && !quitEarly) {\n      score += 15; // Tolerou erros\n    }\n    \n    if (completion > 0.8) {\n      score += 15; // Completou apesar de dificuldades\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateSocialInteractionScore(gameData) {\n    // Para jogos individuais, score baseado em engajamento\n    const engagement = this.calculateEngagementScore(gameData);\n    return Math.max(30, Math.min(80, engagement * 0.8));\n  }\n\n  calculateAttentionScore(gameData) {\n    const focusTime = gameData.focusTime || 0;\n    const distractions = gameData.distractions || 0;\n    const responseTime = gameData.averageResponseTime || 3000;\n    \n    let score = 50;\n    \n    if (focusTime > 60000) { // Mais de 1 minuto focado\n      score += 20;\n    }\n    \n    if (distractions < 2) {\n      score += 15;\n    }\n    \n    if (responseTime < 2000) {\n      score += 15;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateMemoryScore(gameData) {\n    const accuracy = gameData.accuracy || 0;\n    const patterns = gameData.patterns || [];\n    \n    let score = 50;\n    \n    if (accuracy > 70) {\n      score += 25;\n    }\n    \n    if (patterns.length > 3) {\n      score += 15;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateProcessingSpeedScore(gameData) {\n    const responseTime = gameData.averageResponseTime || 3000;\n    const accuracy = gameData.accuracy || 0;\n    \n    let score = 50;\n    \n    if (responseTime < 1500 && accuracy > 60) {\n      score += 30;\n    } else if (responseTime < 2500) {\n      score += 15;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateExecutiveFunctionScore(gameData) {\n    const planningEvidence = gameData.planningEvidence || 0;\n    const inhibitionControl = gameData.inhibitionControl || 0;\n    const workingMemory = gameData.workingMemory || 0;\n    \n    const score = (planningEvidence + inhibitionControl + workingMemory) / 3;\n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateVisualProcessingScore(gameData) {\n    const visualTasks = gameData.visualTasks || 0;\n    const visualAccuracy = gameData.visualAccuracy || 0;\n    \n    let score = 50;\n    \n    if (visualTasks > 5 && visualAccuracy > 70) {\n      score += 25;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateVisualPerceptionScore(gameData) {\n    return this.calculateVisualProcessingScore(gameData);\n  }\n\n  calculateAuditoryProcessingScore(gameData) {\n    const auditoryTasks = gameData.auditoryTasks || 0;\n    const auditoryAccuracy = gameData.auditoryAccuracy || 50;\n    \n    let score = 50;\n    \n    if (auditoryTasks > 0) {\n      score = auditoryAccuracy;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateTactileProcessingScore(gameData) {\n    const touchInteractions = gameData.touchInteractions || 0;\n    const touchAccuracy = gameData.touchAccuracy || 50;\n    \n    let score = 50;\n    \n    if (touchInteractions > 3) {\n      score = touchAccuracy;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateSensoryIntegrationScore(gameData) {\n    const visual = this.calculateVisualPerceptionScore(gameData);\n    const auditory = this.calculateAuditoryProcessingScore(gameData);\n    const tactile = this.calculateTactileProcessingScore(gameData);\n    \n    return (visual + auditory + tactile) / 3;\n  }\n\n  calculateFineMotorSkillsScore(gameData) {\n    const precision = gameData.precision || 50;\n    const motorControl = gameData.motorControl || 50;\n    \n    return (precision + motorControl) / 2;\n  }\n\n  calculateGrossMotorSkillsScore(gameData) {\n    const movements = gameData.movements || 0;\n    const coordination = gameData.coordination || 50;\n    \n    let score = 50;\n    \n    if (movements > 10) {\n      score = coordination;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateCoordinationScore(gameData) {\n    const eyeHandCoordination = gameData.eyeHandCoordination || 50;\n    const bilateralCoordination = gameData.bilateralCoordination || 50;\n    \n    return (eyeHandCoordination + bilateralCoordination) / 2;\n  }\n\n  calculateMotorPlanningScore(gameData) {\n    const planningSteps = gameData.planningSteps || 0;\n    const executionSuccess = gameData.executionSuccess || 0;\n    \n    let score = 50;\n    \n    if (planningSteps > 0) {\n      score = executionSuccess;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  generateTherapeuticRecommendations(metrics, gameData) {\n    const recommendations = [];\n    \n    // Análise de engajamento\n    const engagement = this.calculateEngagementScore(gameData);\n    if (engagement < 50) {\n      recommendations.push({\n        category: 'engagement',\n        priority: 'high',\n        recommendation: 'Implementar estratégias de motivação e gamificação',\n        rationale: 'Baixo engajamento detectado'\n      });\n    }\n    \n    // Análise de atenção\n    const attention = this.calculateAttentionScore(gameData);\n    if (attention < 50) {\n      recommendations.push({\n        category: 'attention',\n        priority: 'medium',\n        recommendation: 'Exercícios de foco e concentração',\n        rationale: 'Dificuldades atencionais identificadas'\n      });\n    }\n    \n    // Análise de processamento\n    const processing = this.calculateProcessingSpeedScore(gameData);\n    if (processing < 50) {\n      recommendations.push({\n        category: 'processing',\n        priority: 'medium',\n        recommendation: 'Atividades para melhorar velocidade de processamento',\n        rationale: 'Processamento lento identificado'\n      });\n    }\n    \n    return recommendations;\n  }\n\n  generateProgressIndicators(metrics, gameData) {\n    return {\n      overallProgress: this.calculateOverallProgress(gameData),\n      strengthAreas: this.identifyStrengthAreas(gameData),\n      challengeAreas: this.identifyChallengeAreas(gameData),\n      developmentGoals: this.generateDevelopmentGoals(gameData),\n      milestones: this.generateMilestones(gameData)\n    };\n  }\n\n  generateGameSpecificInsights(metrics, gameData) {\n    // Implementação específica para cada jogo será adicionada pelos processadores\n    return {\n      gameType: this.gameType,\n      specificMetrics: metrics,\n      gamePerformance: this.calculateGamePerformance(gameData),\n      adaptationNeeds: this.identifyAdaptationNeeds(gameData)\n    };\n  }\n\n  calculateAnalysisConfidenceScore(metrics, gameData) {\n    let confidence = 50;\n    \n    // Fator de dados disponíveis\n    const dataPoints = Object.keys(gameData).length;\n    if (dataPoints > 10) confidence += 20;\n    else if (dataPoints > 5) confidence += 10;\n    \n    // Fator de métricas processadas\n    const metricsCount = Object.keys(metrics).length;\n    if (metricsCount > 5) confidence += 20;\n    else if (metricsCount > 3) confidence += 10;\n    \n    // Fator de tempo de sessão\n    const sessionTime = gameData.totalTime || 0;\n    if (sessionTime > 60000) confidence += 10; // Mais de 1 minuto\n    \n    return Math.max(0, Math.min(100, confidence));\n  }\n\n  generateFallbackTherapeuticAnalysis(gameData) {\n    return {\n      behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50, socialInteraction: 50 },\n      cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50, visualProcessing: 50 },\n      sensory: { visualPerception: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },\n      motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },\n      recommendations: [],\n      progressIndicators: { overallProgress: 50, strengthAreas: [], challengeAreas: [], developmentGoals: [], milestones: [] },\n      gameSpecificInsights: { gameType: this.gameType, specificMetrics: {}, gamePerformance: 50, adaptationNeeds: [] },\n      metadata: { analysisTimestamp: new Date().toISOString(), gameType: this.gameType, analysisVersion: '3.0.0', confidenceScore: 30 }\n    };\n  }\n\n  calculateOverallProgress(gameData) {\n    const accuracy = gameData.accuracy || 0;\n    const completion = gameData.completion || 0;\n    const engagement = this.calculateEngagementScore(gameData);\n    \n    return (accuracy + completion + engagement) / 3;\n  }\n\n  identifyStrengthAreas(gameData) {\n    const strengths = [];\n    \n    if (gameData.accuracy > 80) strengths.push('Precisão');\n    if (gameData.averageResponseTime < 2000) strengths.push('Velocidade de resposta');\n    if (this.calculateEngagementScore(gameData) > 70) strengths.push('Engajamento');\n    \n    return strengths;\n  }\n\n  identifyChallengeAreas(gameData) {\n    const challenges = [];\n    \n    if (gameData.accuracy < 50) challenges.push('Precisão');\n    if (gameData.averageResponseTime > 4000) challenges.push('Velocidade de resposta');\n    if (this.calculateEngagementScore(gameData) < 40) challenges.push('Engajamento');\n    \n    return challenges;\n  }\n\n  generateDevelopmentGoals(gameData) {\n    const goals = [];\n    \n    if (gameData.accuracy < 70) {\n      goals.push('Melhorar precisão para 70%+');\n    }\n    \n    if (gameData.averageResponseTime > 3000) {\n      goals.push('Reduzir tempo de resposta para menos de 3 segundos');\n    }\n    \n    return goals;\n  }\n\n  generateMilestones(gameData) {\n    return [\n      { milestone: 'Primeira sessão completa', achieved: gameData.completion > 0.8 },\n      { milestone: 'Precisão acima de 50%', achieved: gameData.accuracy > 50 },\n      { milestone: 'Engajamento sustentado', achieved: this.calculateEngagementScore(gameData) > 60 }\n    ];\n  }\n\n  calculateGamePerformance(gameData) {\n    const accuracy = gameData.accuracy || 0;\n    const completion = gameData.completion || 0;\n    const efficiency = gameData.efficiency || 0;\n    \n    return (accuracy + completion + efficiency) / 3;\n  }\n\n  identifyAdaptationNeeds(gameData) {\n    const needs = [];\n    \n    if (gameData.accuracy < 40) {\n      needs.push('Reduzir dificuldade');\n    }\n    \n    if (gameData.averageResponseTime > 5000) {\n      needs.push('Aumentar tempo limite');\n    }\n    \n    if (this.calculateEngagementScore(gameData) < 30) {\n      needs.push('Aumentar elementos motivacionais');\n    }\n    \n    return needs;\n  };\n  /**\n   * Processa métricas para estrutura padronizada do banco de dados\n   * @param {Object} metrics - Métricas processadas\n   * @param {Object} gameData - Dados do jogo\n   * @returns {Object} Métricas processadas para banco\n   */\n  processMetricsForDatabase(metrics, gameData) {\n    try {\n      return {\n        // Métricas básicas\n        basic: {\n          accuracy: gameData.accuracy || 0,\n          responseTime: gameData.averageResponseTime || 0,\n          completion: gameData.completion || 0,\n          score: gameData.score || 0,\n          duration: gameData.totalTime || 0,\n          attempts: gameData.attempts || 1,\n          errors: gameData.errors || 0\n        },\n        \n        // Métricas cognitivas\n        cognitive: {\n          attention: this.calculateAttentionScore(gameData),\n          memory: this.calculateMemoryScore(gameData),\n          processingSpeed: this.calculateProcessingSpeedScore(gameData),\n          executiveFunction: this.calculateExecutiveFunctionScore(gameData)\n        },\n        \n        // Métricas comportamentais\n        behavioral: {\n          engagement: this.calculateEngagementScore(gameData),\n          persistence: this.calculatePersistenceScore(gameData),\n          adaptability: this.calculateAdaptabilityScore(gameData),\n          frustrationTolerance: this.calculateFrustrationTolerance(gameData)\n        },\n        \n        // Métricas sensoriais\n        sensory: {\n          visualProcessing: this.calculateVisualProcessingScore(gameData),\n          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),\n          tactileProcessing: this.calculateTactileProcessingScore(gameData),\n          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)\n        },\n        \n        // Métricas motoras\n        motor: {\n          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),\n          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),\n          coordination: this.calculateCoordinationScore(gameData),\n          motorPlanning: this.calculateMotorPlanningScore(gameData)\n        },\n        \n        // Métricas específicas do jogo\n        gameSpecific: metrics,\n        \n        // Metadados\n        metadata: {\n          gameType: this.gameType,\n          processingTimestamp: new Date().toISOString(),\n          version: '3.0.0'\n        }\n      };\n    } catch (error) {\n      this.logger?.error('❌ Erro ao processar métricas para banco:', error);\n      return {\n        basic: { accuracy: 0, responseTime: 0, completion: 0, score: 0, duration: 0, attempts: 1, errors: 0 },\n        cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50 },\n        behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50 },\n        sensory: { visualProcessing: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },\n        motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },\n        gameSpecific: metrics,\n        metadata: { gameType: this.gameType, processingTimestamp: new Date().toISOString(), version: '3.0.0' }\n      };\n    }\n  }\n\n  /**\n   * Métodos de análise para QuebraCabeca\n   */\n  analyzeQuebraCabecaPrimary(gameData) {\n    const { totalCorrect = 0, totalAttempts = 1, interactions = [] } = gameData;\n    return {\n      accuracy: Math.round((totalCorrect / totalAttempts) * 100),\n      totalAttempts,\n      totalCorrect,\n      primaryScore: this.calculatePrimaryScore(interactions),\n      efficiency: this.calculateEfficiency(interactions)\n    };\n  }\n\n  analyzeQuebraCabecaSecondary(gameData) {\n    const { interactions = [] } = gameData;\n    return {\n      secondaryAccuracy: this.calculateSecondaryAccuracy(interactions),\n      adaptability: this.calculateAdaptability(interactions),\n      consistency: this.calculateConsistency(interactions)\n    };\n  }\n\n  analyzeQuebraCabecaTiming(gameData) {\n    const { interactions = [] } = gameData;\n    const responseTimes = interactions.map(i => i.responseTime || 0).filter(t => t > 0);\n    \n    if (responseTimes.length === 0) {\n      return { average: 0, median: 0, variability: 0, pattern: 'insufficient_data' };\n    }\n\n    const average = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;\n    const sorted = responseTimes.sort((a, b) => a - b);\n    const median = sorted[Math.floor(sorted.length / 2)];\n    \n    return {\n      average: Math.round(average),\n      median: Math.round(median),\n      min: Math.min(...responseTimes),\n      max: Math.max(...responseTimes),\n      variability: Math.round(this.calculateVariability(responseTimes)),\n      pattern: 'normal'\n    };\n  }\n\n  analyzeQuebraCabecaPatterns(gameData) {\n    const { interactions = [] } = gameData;\n    return {\n      totalPatterns: interactions.length,\n      correctPatterns: interactions.filter(i => i.correct).length,\n      patternTypes: this.identifyPatternTypes(interactions),\n      errorPatterns: this.identifyErrorPatterns(interactions)\n    };\n  }\n\n  analyzeQuebraCabecaBehavior(gameData) {\n    const { interactions = [] } = gameData;\n    return {\n      persistence: this.calculatePersistence(interactions),\n      adaptability: this.calculateAdaptability(interactions),\n      engagement: this.calculateEngagementScore(gameData)\n    };\n  }\n\n  analyzeQuebraCabecaCognition(gameData) {\n    const { interactions = [] } = gameData;\n    return {\n      executiveFunction: this.calculateExecutiveFunction(interactions),\n      workingMemory: this.calculateWorkingMemory(interactions),\n      processingSpeed: this.calculateProcessingSpeed(interactions)\n    };\n  }\n\n  generateQuebraCabecaRecommendations(gameData) {\n    const recommendations = [];\n    const accuracy = gameData.accuracy || 0;\n    const responseTime = gameData.averageResponseTime || 0;\n    \n    if (accuracy < 60) {\n      recommendations.push({\n        type: 'accuracy_improvement',\n        priority: 'high',\n        description: 'Exercícios para melhorar precisão em QuebraCabeca'\n      });\n    }\n    \n    if (responseTime > 5000) {\n      recommendations.push({\n        type: 'speed_improvement', \n        priority: 'medium',\n        description: 'Atividades para melhorar velocidade de processamento'\n      });\n    }\n    \n    return recommendations;\n  }\n\n  // Métodos auxiliares\n  calculatePrimaryScore(interactions) {\n    const correctInteractions = interactions.filter(i => i.correct);\n    return correctInteractions.length / Math.max(1, interactions.length) * 100;\n  }\n\n  calculateSecondaryAccuracy(interactions) {\n    return this.calculatePrimaryScore(interactions);\n  }\n\n  calculateEfficiency(interactions) {\n    const totalTime = interactions.reduce((sum, i) => sum + (i.responseTime || 0), 0);\n    const correctCount = interactions.filter(i => i.correct).length;\n    return totalTime > 0 ? (correctCount / totalTime) * 1000 : 0;\n  }\n\n  identifyPatternTypes(interactions) {\n    const types = {};\n    interactions.forEach(i => {\n      const type = i.patternType || 'unknown';\n      types[type] = (types[type] || 0) + 1;\n    });\n    return types;\n  }\n\n  identifyErrorPatterns(interactions) {\n    const errors = interactions.filter(i => !i.correct);\n    return {\n      totalErrors: errors.length,\n      errorFrequency: errors.length / Math.max(1, interactions.length) * 100\n    };\n  }\n\n  calculateVariability(values) {\n    if (values.length < 2) return 0;\n    const mean = values.reduce((sum, v) => sum + v, 0) / values.length;\n    const variance = values.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / values.length;\n    return Math.sqrt(variance);\n  }\n\n  // Métodos específicos para processQuebraCabecaMetrics\n  calculateSpatialReasoning(attempts, pieces) {\n    const correctPlacements = attempts.filter(a => a.correct).length;\n    const totalAttempts = attempts.length || 1;\n    const accuracy = correctPlacements / totalAttempts;\n\n    // Análise da complexidade espacial\n    const complexityScore = pieces.length > 0 ? Math.min(pieces.length / 20, 1) : 0.5;\n    const spatialScore = (accuracy * 0.7) + (complexityScore * 0.3);\n\n    return {\n      score: Math.round(spatialScore * 100),\n      accuracy,\n      complexity: complexityScore,\n      level: spatialScore > 0.8 ? 'advanced' : spatialScore > 0.6 ? 'intermediate' : 'basic'\n    };\n  }\n\n  calculateProblemSolving(attempts, completed) {\n    const totalAttempts = attempts.length || 1;\n    const efficiency = completed ? Math.max(0, 1 - (totalAttempts - 1) / 10) : 0;\n    const completionBonus = completed ? 0.3 : 0;\n\n    const problemSolvingScore = (efficiency * 0.7) + completionBonus;\n\n    return {\n      score: Math.round(problemSolvingScore * 100),\n      efficiency,\n      completed,\n      strategy: totalAttempts < 5 ? 'efficient' : totalAttempts < 10 ? 'methodical' : 'exploratory'\n    };\n  }\n\n  calculatePersistence(attempts, totalTime) {\n    const timePerAttempt = totalTime > 0 ? totalTime / attempts.length : 0;\n    const persistenceIndicator = attempts.length > 5 ? 1 : attempts.length / 5;\n    const timeConsistency = timePerAttempt > 1000 ? 1 : timePerAttempt / 1000;\n\n    const persistenceScore = (persistenceIndicator * 0.6) + (timeConsistency * 0.4);\n\n    return {\n      score: Math.round(persistenceScore * 100),\n      attempts: attempts.length,\n      timePerAttempt,\n      level: persistenceScore > 0.7 ? 'high' : persistenceScore > 0.4 ? 'moderate' : 'low'\n    };\n  }\n\n  calculateVisualProcessing(attempts, pieces) {\n    const visualComplexity = pieces.length > 0 ? pieces.length / 50 : 0.5;\n    const processingAccuracy = attempts.length > 0 ?\n      attempts.filter(a => a.correct).length / attempts.length : 0;\n\n    const visualScore = (processingAccuracy * 0.8) + (visualComplexity * 0.2);\n\n    return {\n      score: Math.round(visualScore * 100),\n      accuracy: processingAccuracy,\n      complexity: visualComplexity,\n      processing: visualScore > 0.7 ? 'efficient' : 'developing'\n    };\n  }\n\n  calculateMotorSkills(attempts) {\n    const avgResponseTime = attempts.length > 0 ?\n      attempts.reduce((sum, a) => sum + (a.responseTime || 2000), 0) / attempts.length : 2000;\n\n    const motorEfficiency = Math.max(0, 1 - (avgResponseTime - 1000) / 5000);\n    const precision = attempts.length > 0 ?\n      attempts.filter(a => a.correct).length / attempts.length : 0;\n\n    const motorScore = (motorEfficiency * 0.6) + (precision * 0.4);\n\n    return {\n      score: Math.round(motorScore * 100),\n      responseTime: avgResponseTime,\n      precision,\n      level: motorScore > 0.7 ? 'refined' : motorScore > 0.4 ? 'developing' : 'emerging'\n    };\n  }\n\n\n\n  identifyStrategy(attempts) {\n    if (attempts.length < 3) return 'insufficient_data';\n\n    const errorRate = attempts.filter(a => !a.correct).length / attempts.length;\n    const avgTime = attempts.reduce((sum, a) => sum + (a.responseTime || 2000), 0) / attempts.length;\n\n    if (errorRate < 0.2 && avgTime < 3000) return 'systematic';\n    if (errorRate < 0.4 && avgTime > 4000) return 'careful';\n    if (errorRate > 0.6) return 'trial_and_error';\n    return 'adaptive';\n  }\n\n  assessDifficultyHandling(attempts, pieces) {\n    const complexity = pieces.length || 10;\n    const performance = attempts.length > 0 ?\n      attempts.filter(a => a.correct).length / attempts.length : 0;\n\n    const difficultyScore = performance * (1 + complexity / 50);\n\n    return {\n      score: Math.round(Math.min(100, difficultyScore * 100)),\n      complexity,\n      performance,\n      handling: difficultyScore > 0.8 ? 'excellent' : difficultyScore > 0.6 ? 'good' : 'needs_support'\n    };\n  }\n\n\n}\n\nexport default QuebraCabecaProcessors;\n", "// ✅ CONFIGURAÇÃO V3 - QUEBRA-CABEÇA COM 6 ATIVIDADES TERAPÊUTICAS\r\nexport const QuebraCabecaV3Config = {\r\n  // 🎯 CONFIGURAÇÃO DAS 6 ATIVIDADES V3\r\n  ACTIVITY_CONFIG: {\r\n    FREE_ASSEMBLY: {\r\n      id: 'free_assembly',\r\n      name: '<PERSON><PERSON><PERSON> Liv<PERSON>',\r\n      icon: '🧩',\r\n      description: 'Monte quebra-cabeças emocionais livremente',\r\n      therapeuticFocus: ['motor_coordination', 'spatial_planning', 'problem_solving'],\r\n      cognitiveAreas: ['spatial_processing', 'motor_skills', 'executive_function'],\r\n      difficulties: {\r\n        easy: { \r\n          pieces: 3, \r\n          gridSize: '1fr 1fr 1fr',\r\n          timeLimit: 300000,\r\n          helpLevel: 'high',\r\n          distractors: 2\r\n        },\r\n        medium: { \r\n          pieces: 6, \r\n          gridSize: '1fr 1fr 1fr',\r\n          timeLimit: 450000,\r\n          helpLevel: 'medium',\r\n          distractors: 4\r\n        },\r\n        hard: { \r\n          pieces: 9, \r\n          gridSize: '1fr 1fr 1fr',\r\n          timeLimit: 600000,\r\n          helpLevel: 'low',\r\n          distractors: 6\r\n        }\r\n      },\r\n      metrics: ['completion_time', 'piece_placement_accuracy', 'strategy_efficiency', 'error_patterns']\r\n    },\r\n\r\n    GUIDED_ASSEMBLY: {\r\n      id: 'guided_assembly',\r\n      name: '<PERSON><PERSON><PERSON>',\r\n      icon: '🎯',\r\n      description: 'Siga instruções específicas para montar',\r\n      therapeuticFocus: ['instruction_following', 'working_memory', 'sequential_processing'],\r\n      cognitiveAreas: ['auditory_processing', 'memory', 'attention'],\r\n      difficulties: {\r\n        easy: { \r\n          sequenceLength: 3,\r\n          guidanceType: 'visual',\r\n          pauseBetweenSteps: 3000,\r\n          allowRepeats: true\r\n        },\r\n        medium: { \r\n          sequenceLength: 5,\r\n          guidanceType: 'visual_audio',\r\n          pauseBetweenSteps: 2000,\r\n          allowRepeats: true\r\n        },\r\n        hard: { \r\n          sequenceLength: 7,\r\n          guidanceType: 'audio_only',\r\n          pauseBetweenSteps: 1500,\r\n          allowRepeats: false\r\n        }\r\n      },\r\n      metrics: ['instruction_adherence', 'sequence_memory', 'guidance_dependency', 'completion_accuracy']\r\n    },\r\n\r\n    ROTATION_RECONSTRUCTION: {\r\n      id: 'rotation_reconstruction',\r\n      name: 'Reconstrução por Rotação',\r\n      icon: '🔄',\r\n      description: 'Monte peças que aparecem rotacionadas',\r\n      therapeuticFocus: ['spatial_transformation', 'mental_rotation', 'visual_processing'],\r\n      cognitiveAreas: ['spatial_reasoning', 'visual_perception', 'cognitive_flexibility'],\r\n      difficulties: {\r\n        easy: { \r\n          rotationAngles: [90, 180],\r\n          pieceCount: 4,\r\n          rotationPreview: true,\r\n          timePerPiece: 15000\r\n        },\r\n        medium: { \r\n          rotationAngles: [90, 180, 270],\r\n          pieceCount: 6,\r\n          rotationPreview: false,\r\n          timePerPiece: 12000\r\n        },\r\n        hard: { \r\n          rotationAngles: [45, 90, 135, 180, 225, 270, 315],\r\n          pieceCount: 8,\r\n          rotationPreview: false,\r\n          timePerPiece: 10000\r\n        }\r\n      },\r\n      metrics: ['rotation_accuracy', 'mental_rotation_speed', 'spatial_transformation_ability', 'error_recovery']\r\n    },\r\n\r\n    PIECE_CLASSIFICATION: {\r\n      id: 'piece_classification',\r\n      name: 'Classificação de Peças',\r\n      icon: '🎨',\r\n      description: 'Organize peças por categorias',\r\n      therapeuticFocus: ['categorization', 'visual_discrimination', 'organizational_skills'],\r\n      cognitiveAreas: ['executive_function', 'visual_processing', 'conceptual_thinking'],\r\n      difficulties: {\r\n        easy: { \r\n          categories: 2,\r\n          piecesPerCategory: 3,\r\n          categoryHints: true,\r\n          sortingCriteria: ['color', 'emotion']\r\n        },\r\n        medium: { \r\n          categories: 3,\r\n          piecesPerCategory: 4,\r\n          categoryHints: false,\r\n          sortingCriteria: ['emotion', 'context', 'symbol']\r\n        },\r\n        hard: { \r\n          categories: 4,\r\n          piecesPerCategory: 5,\r\n          categoryHints: false,\r\n          sortingCriteria: ['emotion', 'context', 'symbol', 'theme']\r\n        }\r\n      },\r\n      metrics: ['classification_accuracy', 'sorting_strategy', 'category_consistency', 'processing_speed']\r\n    },\r\n\r\n    PATTERN_IDENTIFICATION: {\r\n      id: 'pattern_identification',\r\n      name: 'Identificação de Padrões',\r\n      icon: '🔍',\r\n      description: 'Identifique padrões nos quebra-cabeças',\r\n      therapeuticFocus: ['pattern_recognition', 'logical_reasoning', 'predictive_thinking'],\r\n      cognitiveAreas: ['pattern_processing', 'logical_thinking', 'predictive_analysis'],\r\n      difficulties: {\r\n        easy: { \r\n          patternLength: 4,\r\n          patternTypes: ['alternating', 'sequential'],\r\n          hintLevel: 'high',\r\n          completionOptions: 2\r\n        },\r\n        medium: { \r\n          patternLength: 6,\r\n          patternTypes: ['alternating', 'sequential', 'progressive'],\r\n          hintLevel: 'medium',\r\n          completionOptions: 3\r\n        },\r\n        hard: { \r\n          patternLength: 8,\r\n          patternTypes: ['alternating', 'sequential', 'progressive', 'complex'],\r\n          hintLevel: 'low',\r\n          completionOptions: 4\r\n        }\r\n      },\r\n      metrics: ['pattern_recognition_speed', 'logical_accuracy', 'prediction_success', 'learning_progression']\r\n    },\r\n\r\n    COLLABORATIVE_SOLVING: {\r\n      id: 'collaborative_solving',\r\n      name: 'Resolução Colaborativa',\r\n      icon: '🧠',\r\n      description: 'Resolva em equipe com outros jogadores',\r\n      therapeuticFocus: ['social_cooperation', 'communication', 'shared_problem_solving'],\r\n      cognitiveAreas: ['social_cognition', 'communication_skills', 'collaborative_thinking'],\r\n      difficulties: {\r\n        easy: { \r\n          teamSize: 2,\r\n          communicationLevel: 'high',\r\n          sharedPieces: 4,\r\n          coordinationRequired: 'low'\r\n        },\r\n        medium: { \r\n          teamSize: 3,\r\n          communicationLevel: 'medium',\r\n          sharedPieces: 6,\r\n          coordinationRequired: 'medium'\r\n        },\r\n        hard: { \r\n          teamSize: 4,\r\n          communicationLevel: 'minimal',\r\n          sharedPieces: 8,\r\n          coordinationRequired: 'high'\r\n        }\r\n      },\r\n      metrics: ['cooperation_index', 'communication_effectiveness', 'shared_goal_achievement', 'leadership_emergence']\r\n    }\r\n  },\r\n\r\n  // 🎭 BIBLIOTECA EXPANDIDA DE EMOÇÕES V3\r\n  EMOTIONS_LIBRARY: {\r\n    basic: [\r\n      {\r\n        id: 'happy',\r\n        name: 'Feliz',\r\n        emoji: '😊',\r\n        pieces: ['😊', '🌞', '🎁', '🎉'],\r\n        context: 'Ganhar um presente',\r\n        color: '#FFD93D',\r\n        complexity: 'basic',\r\n        therapeuticValue: 'positive_emotion_recognition'\r\n      },\r\n      {\r\n        id: 'sad',\r\n        name: 'Triste',\r\n        emoji: '😢',\r\n        pieces: ['😢', '🌧️', '💔', '😔'],\r\n        context: 'Perder um brinquedo',\r\n        color: '#6BB6FF',\r\n        complexity: 'basic',\r\n        therapeuticValue: 'emotional_regulation'\r\n      },\r\n      {\r\n        id: 'surprised',\r\n        name: 'Surpreso',\r\n        emoji: '😲',\r\n        pieces: ['😲', '🎉', '❓', '✨'],\r\n        context: 'Ver algo inesperado',\r\n        color: '#FF6B6B',\r\n        complexity: 'basic',\r\n        therapeuticValue: 'reaction_processing'\r\n      },\r\n      {\r\n        id: 'angry',\r\n        name: 'Bravo',\r\n        emoji: '😠',\r\n        pieces: ['😠', '💥', '🌋', '⚡'],\r\n        context: 'Quando algo não sai como esperado',\r\n        color: '#FF8B94',\r\n        complexity: 'basic',\r\n        therapeuticValue: 'anger_management'\r\n      },\r\n      {\r\n        id: 'calm',\r\n        name: 'Calmo',\r\n        emoji: '😌',\r\n        pieces: ['😌', '🌊', '🕊️', '🌿'],\r\n        context: 'Relaxar na natureza',\r\n        color: '#4ECDC4',\r\n        complexity: 'basic',\r\n        therapeuticValue: 'self_regulation'\r\n      },\r\n      {\r\n        id: 'excited',\r\n        name: 'Animado',\r\n        emoji: '🤩',\r\n        pieces: ['🤩', '🎪', '🎢', '🎊'],\r\n        context: 'Ir ao parque de diversões',\r\n        color: '#A8E6CF',\r\n        complexity: 'basic',\r\n        therapeuticValue: 'positive_anticipation'\r\n      }\r\n    ],\r\n\r\n    intermediate: [\r\n      {\r\n        id: 'confused',\r\n        name: 'Confuso',\r\n        emoji: '😕',\r\n        pieces: ['😕', '❓', '🤔', '💭'],\r\n        context: 'Não entender algo',\r\n        color: '#F4A261',\r\n        complexity: 'intermediate',\r\n        therapeuticValue: 'uncertainty_tolerance'\r\n      },\r\n      {\r\n        id: 'proud',\r\n        name: 'Orgulhoso',\r\n        emoji: '😤',\r\n        pieces: ['😤', '🏆', '⭐', '👑'],\r\n        context: 'Conseguir algo difícil',\r\n        color: '#E76F51',\r\n        complexity: 'intermediate',\r\n        therapeuticValue: 'self_esteem'\r\n      },\r\n      {\r\n        id: 'worried',\r\n        name: 'Preocupado',\r\n        emoji: '😰',\r\n        pieces: ['😰', '☁️', '⚠️', '💭'],\r\n        context: 'Pensar em problemas',\r\n        color: '#264653',\r\n        complexity: 'intermediate',\r\n        therapeuticValue: 'anxiety_awareness'\r\n      },\r\n      {\r\n        id: 'love',\r\n        name: 'Amor',\r\n        emoji: '🥰',\r\n        pieces: ['🥰', '💖', '🌹', '✨'],\r\n        context: 'Estar com pessoas queridas',\r\n        color: '#E9C46A',\r\n        complexity: 'intermediate',\r\n        therapeuticValue: 'attachment_recognition'\r\n      }\r\n    ],\r\n\r\n    advanced: [\r\n      {\r\n        id: 'jealous',\r\n        name: 'Ciúmes',\r\n        emoji: '😒',\r\n        pieces: ['😒', '👀', '💚', '⚡'],\r\n        context: 'Ver outros com algo que queremos',\r\n        color: '#2A9D8F',\r\n        complexity: 'advanced',\r\n        therapeuticValue: 'complex_emotion_understanding'\r\n      },\r\n      {\r\n        id: 'embarrassed',\r\n        name: 'Envergonhado',\r\n        emoji: '😳',\r\n        pieces: ['😳', '🔥', '👥', '🙈'],\r\n        context: 'Fazer algo constrangedor',\r\n        color: '#F4A261',\r\n        complexity: 'advanced',\r\n        therapeuticValue: 'social_awareness'\r\n      },\r\n      {\r\n        id: 'disappointed',\r\n        name: 'Desapontado',\r\n        emoji: '😞',\r\n        pieces: ['😞', '💔', '😔', '🌧️'],\r\n        context: 'Expectativas não atendidas',\r\n        color: '#264653',\r\n        complexity: 'advanced',\r\n        therapeuticValue: 'expectation_management'\r\n      }\r\n    ]\r\n  },\r\n\r\n  // ⚙️ CONFIGURAÇÕES DE DIFICULDADE V3\r\n  DIFFICULTY_CONFIGS: {\r\n    EASY: {\r\n      name: 'Fácil',\r\n      description: 'Ideal para iniciantes - Mais ajuda e tempo',\r\n      color: '#4CAF50',\r\n      icon: '🟢',\r\n      emotionComplexity: 'basic',\r\n      roundsPerActivity: 2,\r\n      timeMultiplier: 1.5,\r\n      helpLevel: 'high',\r\n      feedbackFrequency: 'high'\r\n    },\r\n    MEDIUM: {\r\n      name: 'Médio',\r\n      description: 'Desafio equilibrado - Ajuda moderada',\r\n      color: '#FF9800',\r\n      icon: '🟡',\r\n      emotionComplexity: 'intermediate',\r\n      roundsPerActivity: 3,\r\n      timeMultiplier: 1.0,\r\n      helpLevel: 'medium',\r\n      feedbackFrequency: 'medium'\r\n    },\r\n    HARD: {\r\n      name: 'Difícil',\r\n      description: 'Para especialistas - Mínima assistência',\r\n      color: '#F44336',\r\n      icon: '🔴',\r\n      emotionComplexity: 'advanced',\r\n      roundsPerActivity: 4,\r\n      timeMultiplier: 0.8,\r\n      helpLevel: 'low',\r\n      feedbackFrequency: 'low'\r\n    }\r\n  },\r\n\r\n  // 🔄 SISTEMA DE ROTAÇÃO DE ATIVIDADES V3\r\n  ACTIVITY_ROTATION: {\r\n    defaultCycle: [\r\n      'free_assembly',\r\n      'guided_assembly', \r\n      'rotation_reconstruction',\r\n      'piece_classification',\r\n      'pattern_identification',\r\n      'collaborative_solving'\r\n    ],\r\n    adaptiveCycle: true, // Ajusta baseado na performance\r\n    rotationTriggers: {\r\n      roundsCompleted: 3,\r\n      timeElapsed: 300000, // 5 minutos\r\n      accuracyThreshold: 80,\r\n      engagementDrop: 20\r\n    }\r\n  },\r\n\r\n  // 📊 MÉTRICAS TERAPÊUTICAS V3\r\n  THERAPEUTIC_METRICS: {\r\n    spatial_reasoning: {\r\n      components: ['mental_rotation', 'spatial_memory', 'spatial_transformation'],\r\n      weights: { mental_rotation: 0.4, spatial_memory: 0.3, spatial_transformation: 0.3 },\r\n      normalization: 'age_based'\r\n    },\r\n    problem_solving: {\r\n      components: ['strategy_formation', 'error_correction', 'persistence'],\r\n      weights: { strategy_formation: 0.4, error_correction: 0.3, persistence: 0.3 },\r\n      normalization: 'adaptive'\r\n    },\r\n    motor_coordination: {\r\n      components: ['fine_motor', 'hand_eye_coordination', 'movement_precision'],\r\n      weights: { fine_motor: 0.4, hand_eye_coordination: 0.3, movement_precision: 0.3 },\r\n      normalization: 'developmental'\r\n    },\r\n    emotional_recognition: {\r\n      components: ['emotion_identification', 'context_understanding', 'expression_matching'],\r\n      weights: { emotion_identification: 0.4, context_understanding: 0.3, expression_matching: 0.3 },\r\n      normalization: 'clinical'\r\n    },\r\n    social_skills: {\r\n      components: ['cooperation', 'communication', 'leadership'],\r\n      weights: { cooperation: 0.4, communication: 0.3, leadership: 0.3 },\r\n      normalization: 'social_developmental'\r\n    },\r\n    attention_control: {\r\n      components: ['sustained_attention', 'selective_attention', 'attention_switching'],\r\n      weights: { sustained_attention: 0.4, selective_attention: 0.3, attention_switching: 0.3 },\r\n      normalization: 'clinical'\r\n    }\r\n  },\r\n\r\n  // 🎯 CONFIGURAÇÕES DO JOGO V3\r\n  GAME_SETTINGS: {\r\n    pointsByDifficulty: {\r\n      EASY: { base: 10, bonus: 5, time_bonus: 3 },\r\n      MEDIUM: { base: 15, bonus: 8, time_bonus: 5 },\r\n      HARD: { base: 20, bonus: 12, time_bonus: 8 }\r\n    },\r\n    completionFeedbackDuration: 3000,\r\n    nextPuzzleDelay: 1000,\r\n    autoSaveInterval: 30000,\r\n    sessionTimeLimit: 1800000, // 30 minutos\r\n    adaptiveDifficultyEnabled: true,\r\n    multisensoryFeedback: true,\r\n    accessibilityMode: false\r\n  },\r\n\r\n  // 🎨 ELEMENTOS VISUAIS V3\r\n  VISUAL_ELEMENTS: {\r\n    animations: {\r\n      pieceMovement: 'smooth',\r\n      feedbackDuration: 2000,\r\n      transitionSpeed: 300,\r\n      successAnimation: 'bounce',\r\n      errorAnimation: 'shake'\r\n    },\r\n    themes: {\r\n      default: 'modern_glassmorphism',\r\n      highContrast: 'accessibility_focused',\r\n      childFriendly: 'colorful_rounded'\r\n    },\r\n    sounds: {\r\n      piecePlace: 'soft_click',\r\n      success: 'cheerful_chime',\r\n      error: 'gentle_buzz',\r\n      completion: 'victory_fanfare'\r\n    }\r\n  },\r\n\r\n  // 📱 INFORMAÇÕES DO JOGO V3\r\n  GAME_INFO: {\r\n    title: 'Quebra-Cabeça V3',\r\n    subtitle: 'Sistema Completo de 6 Atividades Terapêuticas',\r\n    description: 'Desenvolva habilidades espaciais, emocionais e sociais através de quebra-cabeças especializados',\r\n    icon: '🧩',\r\n    category: 'spatial_emotional_development',\r\n    version: '3.0.0',\r\n    ageRange: '4-12',\r\n    therapeuticApplications: [\r\n      'Desenvolvimento espacial',\r\n      'Reconhecimento emocional', \r\n      'Coordenação motora fina',\r\n      'Resolução de problemas',\r\n      'Habilidades sociais',\r\n      'Controle executivo'\r\n    ],\r\n    clinicalValidation: true,\r\n    accessibilityCompliant: true\r\n  }\r\n};\r\n\r\n// ✅ COMPATIBILIDADE COM VERSÃO ANTERIOR\r\nexport const QuebraCabecaConfig = {\r\n  // Manter referências da versão anterior para compatibilidade\r\n  emotions: QuebraCabecaV3Config.EMOTIONS_LIBRARY.basic,\r\n  difficulties: [\r\n    { id: 'easy', name: 'Fácil', pieces: 3, gridSize: 3 },\r\n    { id: 'medium', name: 'Médio', pieces: 6, gridSize: 4 },\r\n    { id: 'hard', name: 'Difícil', pieces: 9, gridSize: 4 }\r\n  ],\r\n  encouragingMessages: [\r\n    'Muito bem! Você reconheceu a emoção! 🌟',\r\n    'Excelente! Continue assim! 🎉',\r\n    'Você está ótimo em montar emoções! 😊',\r\n    'Perfeito! Sua paciência é incrível! ✨',\r\n    'Fantástico! Você entende as emoções! 🧠'\r\n  ],\r\n  gameSettings: QuebraCabecaV3Config.GAME_SETTINGS,\r\n  gameInfo: QuebraCabecaV3Config.GAME_INFO\r\n};\r\n", "/**\r\n * 🧩 QUEBRA-CABEÇA V3 - JOGO DE QUEBRA-CABEÇA COM MÚLTIPLAS ATIVIDADES\r\n * Portal Betina V3 - Jogo educativo com 6 atividades diversificadas\r\n */\r\n\r\nimport React, { useState, useEffect, useContext, useCallback, useRef } from 'react'\r\nimport { QuebraCabecaConfig, QuebraCabecaV3Config } from './QuebraCabecaConfig'\r\nimport { QuebraCabecaMetrics } from './QuebraCabecaMetrics'\r\nimport { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic'\r\nimport { useAccessibilityContext } from '../../components/context/AccessibilityContext'\r\nimport { SystemContext } from '../../components/context/SystemContext.jsx'\r\nimport { useGameAudio } from '../../games/shared/GameUtils'\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\n// Importa o componente padrão de tela de dificuldade\r\nimport GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';\r\n\r\n// 🧩 Importar coletores avançados V3\r\nimport { QuebraCabecaCollectorsHub } from './collectors/index.js'\r\n// 🔄 Importar hook multissensorial\r\nimport { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';\r\n// 🎯 Importar hook orquestrador terapêutico\r\nimport { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';\r\n\r\n// 🎨 Importar estilos V3\r\nimport styles from './QuebraCabeca.module.css';\r\n\r\n// 🎯 SISTEMA DE ATIVIDADES REDESENHADO V3 - QUEBRA-CABEÇA EMOCIONAL\r\n// Cada atividade testa diferentes funções cognitivas com layouts únicos\r\nconst ACTIVITY_TYPES = {\r\n  EMOTION_PUZZLE: {\r\n    id: 'emotion_puzzle',\r\n    name: 'Emocional',\r\n    icon: '🎯',\r\n    description: 'Teste de coordenação motora e montagem de emoções',\r\n    cognitiveFunction: 'motor_coordination_emotion_assembly',\r\n    component: 'EmotionPuzzleActivity'\r\n  },\r\n  PIECE_ROTATION: {\r\n    id: 'piece_rotation',\r\n    name: 'Rotação',\r\n    icon: '🔄',\r\n    description: 'Teste de rotação mental e orientação espacial',\r\n    cognitiveFunction: 'spatial_rotation_mental_processing',\r\n    component: 'PieceRotationActivity'\r\n  },\r\n  PATTERN_PUZZLE: {\r\n    id: 'pattern_puzzle',\r\n    name: 'Padrões',\r\n    icon: '🔍',\r\n    description: 'Teste de reconhecimento de padrões em quebra-cabeças',\r\n    cognitiveFunction: 'pattern_recognition_spatial',\r\n    component: 'PatternPuzzleActivity'\r\n  },\r\n\r\n};\r\n\r\nfunction QuebraCabecaGame({ onBack }) {\r\n  const { user, ttsEnabled = true } = useContext(SystemContext);\r\n  const { settings } = useAccessibilityContext();\r\n  const { playSound } = useGameAudio();\r\n\r\n  // Referência para métricas\r\n  const metricsRef = useRef(null);\r\n\r\n  // 🎯 ESTADO EXPANDIDO COM SISTEMA DE ATIVIDADES V3\r\n  const [gameState, setGameState] = useState({\r\n    status: 'start', // 'start', 'playing', 'paused', 'finished'\r\n    score: 0,\r\n    round: 1,\r\n    level: 1,\r\n    totalRounds: 10,\r\n    difficulty: 'EASY',\r\n    accuracy: 100,\r\n    roundStartTime: null,\r\n\r\n    // 🎯 Sistema de atividades redesenhado (3 atividades principais)\r\n    currentActivity: ACTIVITY_TYPES.EMOTION_PUZZLE.id,\r\n    activityCycle: [\r\n      ACTIVITY_TYPES.EMOTION_PUZZLE.id,\r\n      ACTIVITY_TYPES.PIECE_ROTATION.id,\r\n      ACTIVITY_TYPES.PATTERN_PUZZLE.id\r\n    ],\r\n    activityIndex: 0,\r\n    roundsPerActivity: 5, // Mínimo 5 rodadas por atividade\r\n    activityRoundCount: 0,\r\n    activitiesCompleted: [],\r\n\r\n    // 🎯 Dados específicos de atividades\r\n    activityData: {\r\n      freeAssembly: {\r\n        puzzlePieces: [],\r\n        placedPieces: [],\r\n        completedPuzzles: 0\r\n      },\r\n      guidedAssembly: {\r\n        currentHint: null,\r\n        hintsUsed: 0,\r\n        guidanceLevel: 'basic'\r\n      },\r\n      patternMatching: {\r\n        patterns: [],\r\n        matchedPatterns: [],\r\n        currentPattern: null\r\n      },\r\n      shapeSorting: {\r\n        shapes: [],\r\n        sortedShapes: {},\r\n        categories: []\r\n      },\r\n      timedChallenge: {\r\n        timeLimit: 60,\r\n        timeRemaining: 60,\r\n        isTimerActive: false\r\n      },\r\n      creativeBuilding: {\r\n        availablePieces: [],\r\n        userCreation: [],\r\n        savedCreations: []\r\n      }\r\n    },\r\n\r\n    // 🎯 Feedback e animações\r\n    showFeedback: false,\r\n    feedbackType: null,\r\n    feedbackMessage: '',\r\n    showCelebration: false,\r\n\r\n    // 🎯 Métricas comportamentais\r\n    responseTime: 0,\r\n    hesitationCount: 0,\r\n    helpUsed: false,\r\n    consecutiveCorrect: 0,\r\n    totalAttempts: 0,\r\n    correctAttempts: 0,\r\n    \r\n    // Peças e controle do jogo\r\n    availablePieces: [],\r\n    draggedPiece: null,\r\n    isComplete: false,\r\n    \r\n    // Feedback e interação\r\n    feedback: null,\r\n    \r\n    // Estatísticas gerais\r\n    gameStats: {\r\n      score: 0,\r\n      completed: 0,\r\n      totalAttempts: 0,\r\n      accuracy: 100,\r\n      sessionStartTime: null,\r\n      roundStartTime: null\r\n    },\r\n    \r\n    // 🔥 Configurações especiais\r\n    specialConfig: {\r\n      mentalRotationTime: [],\r\n      spatialErrors: [],\r\n      pieceClassification: {\r\n        categoryAccuracy: {},\r\n        sortingStrategy: 'color_first',\r\n        classificationTime: []\r\n      },\r\n      patternIdentification: {\r\n        patternRecognitionSpeed: [],\r\n        logicalAccuracy: [],\r\n        predictionSuccess: []\r\n      },\r\n      collaborativeSolving: {\r\n        cooperationIndex: 0,\r\n        communicationTurns: 0,\r\n        leadershipEvents: []\r\n      }\r\n    },\r\n    \r\n    // Métricas comportamentais V3\r\n    behavioralMetrics: {\r\n      reactionTime: [],\r\n      accuracy: [],\r\n      attentionSpan: 0,\r\n      frustrationEvents: [],\r\n      persistenceLevel: 0,\r\n      engagementScore: 100,\r\n      multisensoryProcessing: {},\r\n      activitySpecific: {}\r\n    }\r\n  });\r\n\r\n  // ✅ REFS PARA CONTROLE DE SESSÃO\r\n  const sessionIdRef = useRef(null);\r\n  const roundStartTimeRef = useRef(null);\r\n  \r\n  // 🧠 Integração com sistema unificado de métricas\r\n  const { \r\n    collectMetrics, \r\n    processGameSession, \r\n    initializeSession: initUnifiedSession,\r\n    processAdvancedMetrics, // Novo: para AdvancedMetricsEngine\r\n    sessionId,\r\n    isSessionActive\r\n  } = useUnifiedGameLogic('QuebraCabeca')\r\n\r\n  // 🧩 Inicializar coletores avançados V3\r\n  const [collectorsHub] = useState(() => new QuebraCabecaCollectorsHub())\r\n\r\n  // TTS control\r\n  const [ttsActive, setTtsActive] = useState(() => {\r\n    const saved = localStorage.getItem('quebraCabeca_ttsActive');\r\n    return saved ? JSON.parse(saved) : true;\r\n  });\r\n\r\n  // 🔄 Hook multissensorial integrado\r\n  const {\r\n    initializeSession: initMultisensory,\r\n    recordInteraction: recordMultisensoryInteraction,\r\n    finalizeSession: finalizeMultisensory,\r\n    updateData: updateMultisensoryData,\r\n    multisensoryData,\r\n    isInitialized: multisensoryInitialized\r\n  } = useMultisensoryIntegration(sessionId, {\r\n    gameType: 'puzzle-game-v3',\r\n    sensorTypes: {\r\n      visual: true,\r\n      haptic: true,\r\n      tts: ttsEnabled,\r\n      gestural: true,\r\n      biometric: true\r\n    },\r\n    adaptiveMode: true,\r\n    autoUpdate: true,\r\n    enablePatternAnalysis: true,\r\n    logLevel: 'info',\r\n    learningStyle: user?.profile?.learningStyle || 'visual'\r\n  });\r\n\r\n  // 🎯 Hook orquestrador terapêutico integrado\r\n  const therapeuticOrchestrator = useTherapeuticOrchestrator({ \r\n    gameType: 'puzzle-game',\r\n    collectorsHub,\r\n    recordMultisensoryInteraction,\r\n    autoUpdate: true,\r\n    logLevel: 'info'\r\n  });\r\n\r\n  const [cognitiveAnalysisVisible, setCognitiveAnalysisVisible] = useState(false)\r\n  const [gameStarted, setGameStarted] = useState(false)\r\n  const [showStartScreen, setShowStartScreen] = useState(true)\r\n  const [gameStats, setGameStats] = useState({\r\n    level: 1,\r\n    score: 0,\r\n    completed: 0,\r\n    totalAttempts: 0,\r\n    accuracy: 100\r\n  })\r\n  const [difficulty, setDifficulty] = useState('easy')\r\n  const [draggedPiece, setDraggedPiece] = useState(null)\r\n  const [placedPieces, setPlacedPieces] = useState([])\r\n  const [puzzlePieces, setPuzzlePieces] = useState([])\r\n  const [isComplete, setIsComplete] = useState(false)\r\n  const [currentEmotion, setCurrentEmotion] = useState(null)\r\n  const [feedback, setFeedback] = useState(null)\r\n\r\n  // Função TTS padronizada\r\n  const speak = useCallback((text, options = {}) => {\r\n    if (!ttsActive || !('speechSynthesis' in window)) {\r\n      return;\r\n    }\r\n\r\n    window.speechSynthesis.cancel();\r\n\r\n    const utterance = new SpeechSynthesisUtterance(text);\r\n    utterance.lang = 'pt-BR';\r\n    utterance.rate = options.rate || 0.9;\r\n    utterance.pitch = options.pitch || 1;\r\n    utterance.volume = options.volume || 1;\r\n\r\n    window.speechSynthesis.speak(utterance);\r\n  }, [ttsActive]);\r\n\r\n  // ✅ FUNÇÕES V3 - GERAÇÃO DE ATIVIDADES\r\n\r\n  // 🎯 Gerar conteúdo para atividade atual - REDESENHADO\r\n  const generateActivityContent = useCallback((activityId, difficulty) => {\r\n    const config = QuebraCabecaV3Config.DIFFICULTY_CONFIGS[difficulty.toUpperCase()];\r\n    const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[activityId.toUpperCase()];\r\n\r\n    switch (activityId) {\r\n      case 'emotion_puzzle':\r\n        return generateEmotionPuzzleActivity(config, activityConfig);\r\n      case 'piece_rotation':\r\n        return generatePieceRotationActivity(config, activityConfig);\r\n      case 'pattern_puzzle':\r\n        return generatePatternPuzzleActivity(config, activityConfig);\r\n      default:\r\n        return generateEmotionPuzzleActivity(config, activityConfig);\r\n    }\r\n  }, []);\r\n\r\n  // =====================================================\r\n  // 🎯 FUNÇÕES DE GERAÇÃO REDESENHADAS - QUEBRA-CABEÇA ESPECÍFICO\r\n  // =====================================================\r\n\r\n  // 🧩 QUEBRA-CABEÇA EMOCIONAL - Montagem tradicional de peças emocionais\r\n  const generateEmotionPuzzleActivity = useCallback((difficultyConfig, activityConfig) => {\r\n    console.log('🧩 Generating emotion puzzle activity');\r\n\r\n    const emotionLibrary = QuebraCabecaV3Config.EMOTIONS_LIBRARY[difficultyConfig?.emotionComplexity || 'basic'];\r\n    const randomEmotion = emotionLibrary[Math.floor(Math.random() * emotionLibrary.length)];\r\n\r\n    const pieceCounts = {\r\n      EASY: 4,\r\n      MEDIUM: 6,\r\n      HARD: 9\r\n    };\r\n\r\n    const difficultyKey = gameState.difficulty || 'EASY';\r\n    const pieceCount = pieceCounts[difficultyKey] || 4;\r\n    const correctPieces = randomEmotion.pieces.slice(0, pieceCount);\r\n    const distractorPieces = generateDistractorPieces(correctPieces, 2);\r\n\r\n    return {\r\n      emotion: randomEmotion,\r\n      correctPieces,\r\n      availablePieces: [...correctPieces, ...distractorPieces].sort(() => Math.random() - 0.5),\r\n      targetSlots: pieceCount,\r\n      instruction: `Monte o quebra-cabeça da emoção \"${randomEmotion.name}\"`,\r\n      level: gameState.difficulty,\r\n      activityType: 'emotion_puzzle',\r\n      gridLayout: Math.ceil(Math.sqrt(pieceCount))\r\n    };\r\n  }, [gameState.difficulty]);\r\n\r\n  // 🔄 ROTAÇÃO DE PEÇAS - Rotação mental e orientação espacial\r\n  const generatePieceRotationActivity = useCallback((difficultyConfig, activityConfig) => {\r\n    console.log('🔄 Generating piece rotation activity');\r\n\r\n    const emotionLibrary = QuebraCabecaV3Config.EMOTIONS_LIBRARY[difficultyConfig?.emotionComplexity || 'basic'];\r\n    const randomEmotion = emotionLibrary[Math.floor(Math.random() * emotionLibrary.length)];\r\n\r\n    const rotationAngles = {\r\n      EASY: [90, 180],\r\n      MEDIUM: [90, 180, 270],\r\n      HARD: [45, 90, 135, 180, 225, 270, 315]\r\n    };\r\n\r\n    const pieces = randomEmotion.pieces.slice(0, 4);\r\n    const difficultyKey = gameState.difficulty || 'EASY';\r\n    const availableAngles = rotationAngles[difficultyKey] || rotationAngles.EASY;\r\n\r\n    const rotatedPieces = pieces.map((piece, index) => ({\r\n      id: index,\r\n      original: piece,\r\n      angle: availableAngles[Math.floor(Math.random() * availableAngles.length)],\r\n      placed: false,\r\n      correctPosition: index\r\n    }));\r\n\r\n    return {\r\n      emotion: randomEmotion,\r\n      rotatedPieces,\r\n      instruction: `Rotacione mentalmente as peças e monte \"${randomEmotion.name}\"`,\r\n      level: gameState.difficulty,\r\n      activityType: 'piece_rotation',\r\n      completedRotations: 0\r\n    };\r\n  }, [gameState.difficulty]);\r\n\r\n  // 🔍 QUEBRA-CABEÇA DE PADRÕES - Reconhecimento de padrões em quebra-cabeças\r\n  const generatePatternPuzzleActivity = useCallback((difficultyConfig, activityConfig) => {\r\n    console.log('🔍 Generating pattern puzzle activity');\r\n\r\n    const patternTypes = {\r\n      EASY: [\r\n        { type: 'alternating', sequence: ['😊', '😢', '😊', '😢'], missing: 2, correct: '😊' },\r\n        { type: 'sequential', sequence: ['😊', '🤩', '😍', '🥰'], missing: 3, correct: '🥰' }\r\n      ],\r\n      MEDIUM: [\r\n        { type: 'progressive', sequence: ['😐', '😊', '😄', '🤩'], missing: 2, correct: '😄' },\r\n        { type: 'emotional_scale', sequence: ['😢', '😔', '😐', '😊'], missing: 1, correct: '😔' }\r\n      ],\r\n      HARD: [\r\n        { type: 'complex_pattern', sequence: ['😊', '😢', '😡', '😊', '😢'], missing: 4, correct: '😡' },\r\n        { type: 'emotional_cycle', sequence: ['😴', '😊', '😰', '😴'], missing: 2, correct: '😰' }\r\n      ]\r\n    };\r\n\r\n    const difficultyKey = gameState.difficulty || 'EASY';\r\n    const levelPatterns = patternTypes[difficultyKey] || patternTypes.EASY;\r\n    const selectedPattern = levelPatterns[Math.floor(Math.random() * levelPatterns.length)];\r\n\r\n    // Criar opções de resposta\r\n    const wrongOptions = ['😲', '🤔', '😏', '🥳'].filter(opt => opt !== selectedPattern.correct);\r\n    const options = [selectedPattern.correct, ...wrongOptions.slice(0, 2)].sort(() => Math.random() - 0.5);\r\n\r\n    return {\r\n      pattern: selectedPattern,\r\n      options,\r\n      instruction: `Complete o padrão emocional observando a sequência`,\r\n      level: gameState.difficulty,\r\n      activityType: 'pattern_puzzle',\r\n      userAnswer: null,\r\n      solved: false\r\n    };\r\n  }, [gameState.difficulty]);\r\n\r\n\r\n\r\n  // 🔧 Funções auxiliares de geração\r\n  const generateDistractorPieces = useCallback((correctPieces, count) => {\r\n    const allPieces = ['😊', '😢', '😲', '😠', '😌', '🤩', '🌞', '🌧️', '💔', '🎁', '🎉', '❓', '✨', '🌊', '🕊️', '🌿', '💥', '🌋', '⚡', '🎪', '🎢', '🎊'];\r\n    const distractors = allPieces.filter(piece => !correctPieces.includes(piece));\r\n    return distractors.sort(() => Math.random() - 0.5).slice(0, count);\r\n  }, []);\r\n\r\n  // Funções auxiliares para as novas atividades\r\n  const generateEmotionCategories = useCallback((settings) => {\r\n    const baseCategories = {\r\n      positive: { name: 'Emoções Positivas', icon: '😊', pieces: [] },\r\n      negative: { name: 'Emoções Negativas', icon: '😢', pieces: [] },\r\n      neutral: { name: 'Emoções Neutras', icon: '😐', pieces: [] },\r\n      intense: { name: 'Emoções Intensas', icon: '🤯', pieces: [] }\r\n    };\r\n\r\n    const selectedCategories = Object.keys(baseCategories).slice(0, settings.categories || 2);\r\n    const result = {};\r\n    selectedCategories.forEach(key => {\r\n      result[key] = baseCategories[key];\r\n    });\r\n\r\n    return result;\r\n  }, []);\r\n\r\n  const generateEmotionPiecesForSorting = useCallback((categories, settings) => {\r\n    const categoryKeys = Object.keys(categories);\r\n    const pieces = [];\r\n\r\n    const emotionMap = {\r\n      positive: ['😊', '😄', '🤩', '😍', '🥰', '😌'],\r\n      negative: ['😢', '😭', '😠', '😡', '😰', '😔'],\r\n      neutral: ['😐', '🤔', '😑', '😶', '🙄', '😏'],\r\n      intense: ['🤯', '😱', '🤬', '😵', '🥵', '🥶']\r\n    };\r\n\r\n    categoryKeys.forEach(categoryKey => {\r\n      const categoryPieces = emotionMap[categoryKey] || [];\r\n      const selectedPieces = categoryPieces.slice(0, settings.piecesPerCategory || 3);\r\n      pieces.push(...selectedPieces.map(piece => ({ piece, category: categoryKey, emoji: piece })));\r\n    });\r\n\r\n    return pieces.sort(() => Math.random() - 0.5);\r\n  }, []);\r\n\r\n  const generatePiecesForClassification = useCallback((categories, settings) => {\r\n    const categoryKeys = Object.keys(categories);\r\n    const pieces = [];\r\n    \r\n    categoryKeys.forEach(categoryKey => {\r\n      const categoryPieces = getCategoryPieces(categoryKey);\r\n      const selectedPieces = categoryPieces.slice(0, settings.piecesPerCategory);\r\n      pieces.push(...selectedPieces.map(piece => ({ piece, category: categoryKey })));\r\n    });\r\n    \r\n    return pieces.sort(() => Math.random() - 0.5);\r\n  }, []);\r\n\r\n  const getCategoryPieces = useCallback((category) => {\r\n    const categoryMap = {\r\n      emotions: ['😊', '😢', '😲', '😠', '😌', '🤩'],\r\n      nature: ['🌞', '🌧️', '🌊', '🕊️', '🌿', '🌋'],\r\n      objects: ['🎁', '🎉', '🎪', '🎢', '🎊', '💔'],\r\n      symbols: ['❓', '✨', '💥', '⚡', '🏆', '⭐']\r\n    };\r\n    return categoryMap[category] || [];\r\n  }, []);\r\n\r\n  const generateLogicalPattern = useCallback((settings) => {\r\n    const patternTypes = settings.patternTypes;\r\n    const selectedType = patternTypes[Math.floor(Math.random() * patternTypes.length)];\r\n    \r\n    switch (selectedType) {\r\n      case 'alternating':\r\n        return { type: 'alternating', sequence: ['😊', '😢', '😊', '😢'], next: '😊' };\r\n      case 'sequential':\r\n        return { type: 'sequential', sequence: ['😊', '🌞', '😢', '🌧️'], next: '😲' };\r\n      case 'progressive':\r\n        return { type: 'progressive', sequence: ['😊', '🤩', '😢', '😭'], next: '😡' };\r\n      default:\r\n        return { type: 'alternating', sequence: ['😊', '😢', '😊', '😢'], next: '😊' };\r\n    }\r\n  }, []);\r\n\r\n  const generatePatternOptions = useCallback((pattern, settings) => {\r\n    const correctAnswer = pattern.next;\r\n    const wrongOptions = ['😲', '🤔', '😴', '🥳'].filter(opt => opt !== correctAnswer);\r\n    const selectedWrong = wrongOptions.slice(0, settings.completionOptions - 1);\r\n    \r\n    return [correctAnswer, ...selectedWrong].sort(() => Math.random() - 0.5);\r\n  }, []);\r\n\r\n  // ✅ FUNÇÕES V3 - ROTAÇÃO DE ATIVIDADES\r\n\r\n  // 🔄 Rotacionar para próxima atividade\r\n  const rotateToNextActivity = useCallback(() => {\r\n    setGameState(prev => {\r\n      const nextIndex = (prev.activityIndex + 1) % prev.activityCycle.length;\r\n      const nextActivity = prev.activityCycle[nextIndex];\r\n      \r\n      // Registrar rotação de atividade\r\n      if (recordMultisensoryInteraction) {\r\n        recordMultisensoryInteraction('activity_rotation', {\r\n          from: prev.currentActivity,\r\n          to: nextActivity,\r\n          automatic: true,\r\n          round: prev.round\r\n        });\r\n      }\r\n      \r\n      const newState = {\r\n        ...prev,\r\n        currentActivity: nextActivity,\r\n        activityIndex: nextIndex,\r\n        activityRoundCount: 0,\r\n        round: prev.round + 1,\r\n        roundStartTime: Date.now()\r\n      };\r\n      \r\n      // Gerar novo conteúdo para a atividade\r\n      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);\r\n      \r\n      // Atualizar dados específicos da atividade\r\n      newState.currentEmotion = activityContent.emotion || null;\r\n      newState.puzzlePieces = activityContent.availablePieces || activityContent.pieces || [];\r\n      newState.placedPieces = new Array(activityContent.targetSlots || 3).fill(null);\r\n      \r\n      // Anunciar nova atividade\r\n      const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[nextActivity.toUpperCase()];\r\n      const activityName = activityConfig?.name || 'Nova Atividade';\r\n      \r\n      setTimeout(() => {\r\n        if (speak) {\r\n          speak(`Nova atividade: ${activityName}. ${activityContent.instruction}`, { rate: 0.8 });\r\n        }\r\n      }, 500);\r\n      \r\n      return newState;\r\n    });\r\n  }, [generateActivityContent, recordMultisensoryInteraction, speak]);\r\n\r\n  // 🎯 Trocar atividade manualmente\r\n  const switchActivity = useCallback((activityId) => {\r\n    setGameState(prev => {\r\n      const activityIndex = prev.activityCycle.indexOf(activityId);\r\n      if (activityIndex === -1) return prev;\r\n      \r\n      const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[activityId.toUpperCase()];\r\n      const activityName = activityConfig?.name || 'Nova Atividade';\r\n      \r\n      // Registrar troca manual de atividade\r\n      if (recordMultisensoryInteraction) {\r\n        recordMultisensoryInteraction('activity_switch', {\r\n          from: prev.currentActivity,\r\n          to: activityId,\r\n          manual: true,\r\n          round: prev.round\r\n        });\r\n      }\r\n      \r\n      const newState = {\r\n        ...prev,\r\n        currentActivity: activityId,\r\n        activityIndex: activityIndex,\r\n        activityRoundCount: 0,\r\n        round: prev.round + 1,\r\n        roundStartTime: Date.now()\r\n      };\r\n      \r\n      // Gerar novo conteúdo\r\n      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);\r\n      \r\n      newState.currentEmotion = activityContent.emotion || null;\r\n      newState.puzzlePieces = activityContent.availablePieces || activityContent.pieces || [];\r\n      newState.placedPieces = new Array(activityContent.targetSlots || 3).fill(null);\r\n      \r\n      // Anunciar atividade\r\n      setTimeout(() => {\r\n        if (speak) {\r\n          speak(`Atividade alterada para: ${activityName}. ${activityContent.instruction}`, { rate: 0.8 });\r\n        }\r\n      }, 300);\r\n      \r\n      return newState;\r\n    });\r\n  }, [generateActivityContent, recordMultisensoryInteraction, speak]);\r\n\r\n  // ✅ FUNÇÕES V3 - GERAÇÃO DE NOVA RODADA\r\n  const generateNewRound = useCallback(() => {\r\n    console.log('🎯 Gerando nova rodada V3...', { \r\n      currentActivity: gameState.currentActivity,\r\n      activityRoundCount: gameState.activityRoundCount,\r\n      round: gameState.round \r\n    });\r\n    \r\n    setGameState(prev => {\r\n      // Verificar se precisa rotar atividade\r\n      const shouldRotateActivity = prev.activityRoundCount >= prev.roundsPerActivity;\r\n      \r\n      console.log('🔄 Verificando rotação de atividade:', { \r\n        shouldRotateActivity, \r\n        activityRoundCount: prev.activityRoundCount,\r\n        roundsPerActivity: prev.roundsPerActivity \r\n      });\r\n      \r\n      let newState = { ...prev };\r\n      \r\n      if (shouldRotateActivity) {\r\n        // Rotar para próxima atividade\r\n        const nextActivityIndex = (prev.activityIndex + 1) % prev.activityCycle.length;\r\n        const nextActivity = prev.activityCycle[nextActivityIndex];\r\n        \r\n        console.log('🎮 Rotacionando para nova atividade:', { \r\n          from: prev.currentActivity, \r\n          to: nextActivity,\r\n          nextActivityIndex \r\n        });\r\n        \r\n        newState = {\r\n          ...newState,\r\n          currentActivity: nextActivity,\r\n          activityIndex: nextActivityIndex,\r\n          activityRoundCount: 0,\r\n          activitiesCompleted: prev.activitiesCompleted + 1\r\n        };\r\n        \r\n        // 🔊 Anunciar nova atividade\r\n        const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[nextActivity.toUpperCase()];\r\n        const activityName = activityConfig?.name || 'Nova Atividade';\r\n        setTimeout(() => {\r\n          if (speak) {\r\n            speak(`Nova atividade: ${activityName}`, { pitch: 1.2, rate: 0.8 });\r\n          }\r\n        }, 500);\r\n      }\r\n      \r\n      // Gerar conteúdo específico da atividade\r\n      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);\r\n      \r\n      console.log('📝 Conteúdo gerado para atividade:', { \r\n        activity: newState.currentActivity, \r\n        content: activityContent \r\n      });\r\n      \r\n      // Atualizar estado baseado na atividade atual\r\n      newState.currentEmotion = activityContent.emotion || newState.currentEmotion;\r\n      newState.puzzlePieces = activityContent.availablePieces || activityContent.pieces || [];\r\n      newState.placedPieces = new Array(activityContent.targetSlots || 3).fill(null);\r\n      newState.isComplete = false;\r\n      newState.draggedPiece = null;\r\n      newState.feedback = null;\r\n      newState.showFeedback = false;\r\n      \r\n      // Incrementar contadores\r\n      newState.activityRoundCount = newState.activityRoundCount + 1;\r\n      newState.round = newState.round + 1;\r\n      newState.roundStartTime = Date.now();\r\n      \r\n      return newState;\r\n    });\r\n  }, [gameState.currentActivity, gameState.activityRoundCount, gameState.round, generateActivityContent, speak]);\r\n\r\n  // =====================================================\r\n  // 🎯 FUNÇÕES DE RENDERIZAÇÃO PARA CADA ATIVIDADE\r\n  // =====================================================\r\n\r\n  // 🧩 MONTAGEM EMOCIONAL - Layout de quebra-cabeça tradicional\r\n  const renderEmotionAssembly = () => {\r\n    return (\r\n      <div className={styles.activityContainer}>\r\n        <div className={styles.activityHeader}>\r\n          <h2 className={styles.activityTitle}>🎯 Montagem Emocional</h2>\r\n        </div>\r\n\r\n        {currentEmotion && (\r\n          <>\r\n            {/* Área do quebra-cabeça */}\r\n            <div className={styles.puzzleArea}>\r\n              <div className={styles.puzzleBoard}>\r\n                <div className={styles.boardTitle}>\r\n                  Monte a emoção: {currentEmotion.name}\r\n                </div>\r\n                <div className={styles.puzzleGrid}>\r\n                  {placedPieces.map((piece, index) => (\r\n                    <div\r\n                      key={index}\r\n                      className={`${styles.puzzlePiece} ${piece ? styles.filled : styles.empty}`}\r\n                      onDragOver={(e) => e.preventDefault()}\r\n                      onDrop={() => handleDrop(index)}\r\n                      style={{ backgroundColor: currentEmotion.color + '20' }}\r\n                    >\r\n                      {piece && (\r\n                        <div className={`${styles.puzzlePiece} ${styles.correct}`}>\r\n                          {piece.content}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Área das peças disponíveis */}\r\n              <div className={styles.piecesArea}>\r\n                <div className={styles.piecesTitle}>Peças Disponíveis</div>\r\n                <div className={styles.piecesGrid}>\r\n                  {puzzlePieces.map((piece) => (\r\n                    <div\r\n                      key={piece.id}\r\n                      className={`${styles.availablePiece} ${piece.used ? styles.used : ''}`}\r\n                      draggable\r\n                      onDragStart={() => handleDragStart(piece)}\r\n                      onClick={() => {\r\n                        const emptySlotIndex = placedPieces.findIndex(p => p === null)\r\n                        if (emptySlotIndex !== -1) {\r\n                          handleDrop(emptySlotIndex)\r\n                        }\r\n                      }}\r\n                    >\r\n                      {piece.content}\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 🔍 COMPLETAR PADRÕES - Layout de sequência lógica\r\n  const renderPatternCompletion = () => {\r\n    const activityData = gameState.activityData?.patternCompletion || {};\r\n\r\n    return (\r\n      <div className={styles.activityContainer}>\r\n        <div className={styles.activityHeader}>\r\n          <h2 className={styles.activityTitle}>🔍 Completar Padrões</h2>\r\n        </div>\r\n\r\n        {activityData.pattern && (\r\n          <>\r\n            {/* Área do padrão */}\r\n            <div className={styles.patternArea}>\r\n              <div className={styles.patternTitle}>\r\n                Padrão: {activityData.pattern.type}\r\n              </div>\r\n              <div className={styles.patternSequence}>\r\n                {activityData.pattern.sequence.map((item, index) => (\r\n                  <div key={index} className={styles.patternItem}>\r\n                    <div className={styles.patternNumber}>{index + 1}</div>\r\n                    <div className={styles.patternEmoji}>{item}</div>\r\n                  </div>\r\n                ))}\r\n                <div className={styles.patternItem}>\r\n                  <div className={styles.patternNumber}>{activityData.pattern.sequence.length + 1}</div>\r\n                  <div className={styles.patternQuestion}>❓</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Opções de resposta */}\r\n            <div className={styles.optionsArea}>\r\n              <div className={styles.optionsTitle}>Escolha o próximo elemento:</div>\r\n              <div className={styles.optionsGrid}>\r\n                {activityData.options?.map((option, index) => (\r\n                  <button\r\n                    key={index}\r\n                    className={`${styles.optionButton} ${\r\n                      activityData.userAnswer === option ? styles.selected : ''\r\n                    }`}\r\n                    onClick={() => {\r\n                      setGameState(prev => ({\r\n                        ...prev,\r\n                        activityData: {\r\n                          ...prev.activityData,\r\n                          patternCompletion: {\r\n                            ...prev.activityData.patternCompletion,\r\n                            userAnswer: option\r\n                          }\r\n                        }\r\n                      }));\r\n                    }}\r\n                    disabled={activityData.userAnswer !== null}\r\n                  >\r\n                    {option}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 🔄 ROTAÇÃO ESPACIAL - Layout de peças rotacionadas\r\n  const renderSpatialRotation = () => {\r\n    const activityData = gameState.activityData?.spatialRotation || {};\r\n\r\n    return (\r\n      <div className={styles.activityContainer}>\r\n        <div className={styles.activityHeader}>\r\n          <h2 className={styles.activityTitle}>🔄 Rotação Espacial</h2>\r\n        </div>\r\n\r\n        {activityData.rotatedPieces && (\r\n          <>\r\n            {/* Área de rotação */}\r\n            <div className={styles.rotationArea}>\r\n              <div className={styles.rotationTitle}>\r\n                Peças Rotacionadas - Mentalize a posição original\r\n              </div>\r\n              <div className={styles.rotationGrid}>\r\n                {activityData.rotatedPieces.map((piece, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className={`${styles.rotatedPiece} ${piece.placed ? styles.placed : ''}`}\r\n                    style={{ transform: `rotate(${piece.angle}deg)` }}\r\n                    onClick={() => {\r\n                      // Lógica de posicionamento da peça rotacionada\r\n                      setGameState(prev => ({\r\n                        ...prev,\r\n                        activityData: {\r\n                          ...prev.activityData,\r\n                          spatialRotation: {\r\n                            ...prev.activityData.spatialRotation,\r\n                            rotatedPieces: prev.activityData.spatialRotation.rotatedPieces.map((p, i) =>\r\n                              i === index ? { ...p, placed: true } : p\r\n                            )\r\n                          }\r\n                        }\r\n                      }));\r\n                    }}\r\n                  >\r\n                    <div className={styles.pieceContent}>{piece.original}</div>\r\n                    <div className={styles.rotationAngle}>{piece.angle}°</div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Área de posicionamento */}\r\n            <div className={styles.positionArea}>\r\n              <div className={styles.positionTitle}>Posições Corretas</div>\r\n              <div className={styles.positionGrid}>\r\n                {Array.from({ length: activityData.targetPositions || 4 }).map((_, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className={`${styles.positionSlot} ${\r\n                      activityData.rotatedPieces?.some(p => p.correctPosition === index && p.placed)\r\n                        ? styles.filled : styles.empty\r\n                    }`}\r\n                  >\r\n                    {index + 1}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 😊 CLASSIFICAÇÃO EMOCIONAL - Layout de categorização\r\n  const renderEmotionSorting = () => {\r\n    const activityData = gameState.activityData?.emotionSorting || {};\r\n\r\n    return (\r\n      <div className={styles.activityContainer}>\r\n        <div className={styles.activityHeader}>\r\n          <h2 className={styles.activityTitle}>😊 Classificação Emocional</h2>\r\n        </div>\r\n\r\n        {activityData.categories && (\r\n          <>\r\n            {/* Área de categorias */}\r\n            <div className={styles.categoriesArea}>\r\n              {Object.entries(activityData.categories).map(([categoryKey, category]) => (\r\n                <div key={categoryKey} className={styles.categoryContainer}>\r\n                  <div className={styles.categoryHeader}>\r\n                    <span className={styles.categoryIcon}>{category.icon}</span>\r\n                    <span className={styles.categoryName}>{category.name}</span>\r\n                  </div>\r\n                  <div className={styles.categoryDropZone}>\r\n                    {activityData.classified?.[categoryKey]?.map((item, index) => (\r\n                      <div key={index} className={styles.classifiedItem}>\r\n                        {item.emoji}\r\n                      </div>\r\n                    )) || <div className={styles.emptyCategory}>Arraste emoções aqui</div>}\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Peças para classificar */}\r\n            <div className={styles.sortingPieces}>\r\n              <div className={styles.sortingTitle}>Emoções para Classificar</div>\r\n              <div className={styles.sortingGrid}>\r\n                {activityData.pieces?.map((piece, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className={`${styles.sortingPiece} ${\r\n                      Object.values(activityData.classified || {}).flat().some(item => item.emoji === piece.emoji)\r\n                        ? styles.classified : ''\r\n                    }`}\r\n                    draggable\r\n                    onClick={() => {\r\n                      // Lógica de classificação automática para touch\r\n                      const targetCategory = piece.category;\r\n                      setGameState(prev => ({\r\n                        ...prev,\r\n                        activityData: {\r\n                          ...prev.activityData,\r\n                          emotionSorting: {\r\n                            ...prev.activityData.emotionSorting,\r\n                            classified: {\r\n                              ...prev.activityData.emotionSorting.classified,\r\n                              [targetCategory]: [\r\n                                ...(prev.activityData.emotionSorting.classified?.[targetCategory] || []),\r\n                                piece\r\n                              ]\r\n                            }\r\n                          }\r\n                        }\r\n                      }));\r\n                    }}\r\n                  >\r\n                    {piece.emoji}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 🎨 COMPOSIÇÃO CRIATIVA - Layout de criação livre\r\n  const renderCreativeComposition = () => {\r\n    const activityData = gameState.activityData?.creativeComposition || {};\r\n\r\n    return (\r\n      <div className={styles.activityContainer}>\r\n        <div className={styles.activityHeader}>\r\n          <h2 className={styles.activityTitle}>🎨 Composição Criativa</h2>\r\n        </div>\r\n\r\n        {activityData.creativePieces && (\r\n          <>\r\n            {/* Área de composição */}\r\n            <div className={styles.compositionArea}>\r\n              <div className={styles.compositionTitle}>Sua Composição Criativa</div>\r\n              <div className={styles.compositionCanvas}>\r\n                {activityData.userComposition?.length > 0 ? (\r\n                  activityData.userComposition.map((piece, index) => (\r\n                    <div key={index} className={styles.compositionPiece}>\r\n                      {piece}\r\n                    </div>\r\n                  ))\r\n                ) : (\r\n                  <div className={styles.emptyCanvas}>\r\n                    Clique nas peças abaixo para criar sua composição\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Restrições */}\r\n              <div className={styles.compositionConstraints}>\r\n                <div className={styles.constraintItem}>\r\n                  Peças: {activityData.userComposition?.length || 0} / {activityData.constraints?.maxPieces || 5}\r\n                </div>\r\n                <div className={styles.constraintItem}>\r\n                  Temas: {activityData.constraints?.themes?.join(', ') || 'Livre'}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Peças disponíveis */}\r\n            <div className={styles.creativePieces}>\r\n              <div className={styles.creativePiecesTitle}>Peças Disponíveis</div>\r\n              <div className={styles.creativePiecesGrid}>\r\n                {activityData.creativePieces?.map((piece, index) => (\r\n                  <button\r\n                    key={index}\r\n                    className={`${styles.creativePiece} ${\r\n                      activityData.userComposition?.includes(piece) ? styles.used : ''\r\n                    }`}\r\n                    onClick={() => {\r\n                      if ((activityData.userComposition?.length || 0) < (activityData.constraints?.maxPieces || 5)) {\r\n                        setGameState(prev => ({\r\n                          ...prev,\r\n                          activityData: {\r\n                            ...prev.activityData,\r\n                            creativeComposition: {\r\n                              ...prev.activityData.creativeComposition,\r\n                              userComposition: [\r\n                                ...(prev.activityData.creativeComposition?.userComposition || []),\r\n                                piece\r\n                              ]\r\n                            }\r\n                          }\r\n                        }));\r\n                      }\r\n                    }}\r\n                    disabled={activityData.userComposition?.includes(piece)}\r\n                  >\r\n                    {piece}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // =====================================================\r\n  // 🎯 FUNÇÕES DE RENDERIZAÇÃO PARA CADA ATIVIDADE\r\n  // =====================================================\r\n\r\n  // 🧩 QUEBRA-CABEÇA EMOCIONAL - DRAG AND DROP TRADICIONAL\r\n  const renderEmotionPuzzle = () => {\r\n    // Se não há emoção atual, gerar uma nova\r\n    if (!currentEmotion && gameStarted) {\r\n      generateNewPuzzle();\r\n      return (\r\n        <div className={styles.questionArea}>\r\n          <div className={styles.questionHeader}>\r\n            <h2 className={styles.questionTitle}>🧩 Quebra-Cabeça Emocional</h2>\r\n          </div>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <div className={styles.questionArea}>\r\n        <div className={styles.questionHeader}>\r\n          <h2 className={styles.questionTitle}>🧩 Monte a emoção: {currentEmotion?.name}</h2>\r\n        </div>\r\n\r\n        {currentEmotion && (\r\n          <>\r\n            {/* Área de objetos - Emoção principal */}\r\n            <div className={styles.objectsDisplay}>\r\n              <div style={{\r\n                textAlign: 'center',\r\n                padding: '2rem',\r\n                border: '3px solid rgba(33, 150, 243, 0.6)',\r\n                borderRadius: '20px',\r\n                backgroundColor: 'rgba(33, 150, 243, 0.2)',\r\n                minWidth: '200px',\r\n                boxShadow: '0 0 20px rgba(33, 150, 243, 0.3)',\r\n                marginBottom: '2rem'\r\n              }}>\r\n                <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>\r\n                  {currentEmotion.emoji}\r\n                </div>\r\n                <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'white' }}>\r\n                  {currentEmotion.name}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Área do quebra-cabeça - Slots para drag and drop */}\r\n              <div className={styles.puzzleBoard}>\r\n                <div className={styles.boardTitle}>\r\n                  Monte as peças que representam esta emoção:\r\n                </div>\r\n                <div className={styles.puzzleGrid}>\r\n                  {placedPieces.map((piece, index) => (\r\n                    <div\r\n                      key={index}\r\n                      className={`${styles.puzzleSlot} ${piece ? styles.filled : styles.empty}`}\r\n                      onDragOver={(e) => e.preventDefault()}\r\n                      onDrop={() => handleDrop(index)}\r\n                      style={{\r\n                        backgroundColor: currentEmotion.color + '20',\r\n                        border: piece ? '3px solid #4CAF50' : '2px dashed rgba(255,255,255,0.5)'\r\n                      }}\r\n                    >\r\n                      {piece && (\r\n                        <div className={styles.placedPiece}>\r\n                          <div style={{ fontSize: '2.5rem', marginBottom: '0.5rem' }}>\r\n                            {piece.content}\r\n                          </div>\r\n                          <button\r\n                            className={styles.removePieceButton}\r\n                            onClick={() => handleRemovePiece(index)}\r\n                            title=\"Remover peça\"\r\n                          >\r\n                            ❌\r\n                          </button>\r\n                        </div>\r\n                      )}\r\n                      {!piece && (\r\n                        <div className={styles.emptySlotText}>\r\n                          Arraste uma peça aqui\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Peças disponíveis - PADRÃO IMAGE ASSOCIATION HORIZONTAL */}\r\n            <div className={styles.answerOptions}>\r\n              {puzzlePieces.length > 0 ? puzzlePieces.map((piece) => {\r\n                const isUsed = piece.used;\r\n\r\n                return (\r\n                  <button\r\n                    key={piece.id}\r\n                    className={`${styles.answerButton} ${isUsed ? styles.used : ''}`}\r\n                    draggable={!isUsed}\r\n                    onDragStart={() => !isUsed && handleDragStart(piece)}\r\n                    style={{\r\n                      opacity: isUsed ? 0.3 : 1,\r\n                      cursor: isUsed ? 'not-allowed' : 'grab',\r\n                      pointerEvents: isUsed ? 'none' : 'auto'\r\n                    }}\r\n                  >\r\n                    <div style={{\r\n                      fontSize: '2.5rem',\r\n                      marginBottom: '0.5rem'\r\n                    }}>\r\n                      {piece.content}\r\n                    </div>\r\n                    <div style={{\r\n                      fontSize: '0.9rem',\r\n                      fontWeight: 'bold',\r\n                      color: isUsed ? 'rgba(255,255,255,0.5)' : 'white'\r\n                    }}>\r\n                      {isUsed ? 'Usada' : 'Arraste'}\r\n                    </div>\r\n                  </button>\r\n                );\r\n              }) : (\r\n                <div className={styles.noPieces}>\r\n                  Carregando peças...\r\n                </div>\r\n              )}\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 🔄 ROTAÇÃO DE PEÇAS - PADRÃO IMAGE ASSOCIATION EXATO\r\n  const renderPieceRotation = () => {\r\n    return (\r\n      <div className={styles.questionArea}>\r\n        <div className={styles.questionHeader}>\r\n          <h2 className={styles.questionTitle}>🔄 Rotação de Peças</h2>\r\n        </div>\r\n\r\n        {/* Área de objetos - PADRÃO IMAGE ASSOCIATION EXATO */}\r\n        <div className={styles.objectsDisplay}>\r\n          <div style={{\r\n            textAlign: 'center',\r\n            padding: '2rem',\r\n            border: '3px solid rgba(33, 150, 243, 0.6)',\r\n            borderRadius: '20px',\r\n            backgroundColor: 'rgba(33, 150, 243, 0.2)',\r\n            minWidth: '200px',\r\n            boxShadow: '0 0 20px rgba(33, 150, 243, 0.3)'\r\n          }}>\r\n            <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>\r\n              🧩\r\n            </div>\r\n            <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'white' }}>\r\n              Peça Original\r\n            </div>\r\n          </div>\r\n\r\n          {/* Seta de conexão - PADRÃO IMAGE ASSOCIATION */}\r\n          <div style={{\r\n            display: 'flex',\r\n            flexDirection: 'column',\r\n            alignItems: 'center',\r\n            gap: '0.5rem',\r\n            margin: '1rem 0'\r\n          }}>\r\n            <div style={{\r\n              fontSize: '3rem',\r\n              animation: 'bounce 2s infinite',\r\n              color: 'rgba(255, 193, 7, 0.8)'\r\n            }}>\r\n              ⬇️\r\n            </div>\r\n            <div style={{\r\n              fontSize: '1rem',\r\n              color: 'rgba(255,255,255,0.8)',\r\n              fontWeight: 'bold'\r\n            }}>\r\n              SE RELACIONA COM\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Opções de resposta - PADRÃO IMAGE ASSOCIATION EXATO */}\r\n        <div className={styles.answerOptions}>\r\n          {[0, 90, 180, 270].map((rotation, index) => (\r\n            <button\r\n              key={index}\r\n              className={styles.answerButton}\r\n              onClick={() => handlePieceRotation(index)}\r\n              aria-label={`Girar ${rotation} graus`}\r\n            >\r\n              <div style={{\r\n                fontSize: '2.5rem',\r\n                marginBottom: '0.5rem',\r\n                transform: `rotate(${rotation}deg)`\r\n              }}>\r\n                🧩\r\n              </div>\r\n              <div style={{ fontSize: '1rem', fontWeight: 'bold' }}>\r\n                {rotation}°\r\n              </div>\r\n            </button>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 🔍 QUEBRA-CABEÇA DE PADRÕES - PADRÃO IMAGE ASSOCIATION EXATO\r\n  const renderPatternPuzzle = () => {\r\n    return (\r\n      <div className={styles.questionArea}>\r\n        <div className={styles.questionHeader}>\r\n          <h2 className={styles.questionTitle}>🔍 Quebra-Cabeça de Padrões</h2>\r\n        </div>\r\n\r\n        {/* Área de objetos - PADRÃO IMAGE ASSOCIATION EXATO */}\r\n        <div className={styles.objectsDisplay}>\r\n          <div style={{\r\n            textAlign: 'center',\r\n            padding: '2rem',\r\n            border: '3px solid rgba(33, 150, 243, 0.6)',\r\n            borderRadius: '20px',\r\n            backgroundColor: 'rgba(33, 150, 243, 0.2)',\r\n            minWidth: '300px',\r\n            boxShadow: '0 0 20px rgba(33, 150, 243, 0.3)'\r\n          }}>\r\n            <div style={{\r\n              display: 'flex',\r\n              gap: '1rem',\r\n              justifyContent: 'center',\r\n              alignItems: 'center',\r\n              marginBottom: '1rem'\r\n            }}>\r\n              {['😊', '😢', '😊', '😢', '❓'].map((item, index) => (\r\n                <div\r\n                  key={index}\r\n                  style={{\r\n                    fontSize: '2.5rem',\r\n                    padding: '0.5rem',\r\n                    border: index === 4 ? '2px dashed rgba(255, 193, 7, 0.8)' : '2px solid rgba(255,255,255,0.3)',\r\n                    borderRadius: '12px',\r\n                    backgroundColor: index === 4 ? 'rgba(255, 193, 7, 0.3)' : 'rgba(255,255,255,0.1)',\r\n                    minWidth: '50px',\r\n                    minHeight: '50px',\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    justifyContent: 'center'\r\n                  }}\r\n                >\r\n                  {item}\r\n                </div>\r\n              ))}\r\n            </div>\r\n            <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'white' }}>\r\n              Complete o Padrão\r\n            </div>\r\n          </div>\r\n\r\n          {/* Seta de conexão - PADRÃO IMAGE ASSOCIATION */}\r\n          <div style={{\r\n            display: 'flex',\r\n            flexDirection: 'column',\r\n            alignItems: 'center',\r\n            gap: '0.5rem',\r\n            margin: '1rem 0'\r\n          }}>\r\n            <div style={{\r\n              fontSize: '3rem',\r\n              animation: 'bounce 2s infinite',\r\n              color: 'rgba(255, 193, 7, 0.8)'\r\n            }}>\r\n              ⬇️\r\n            </div>\r\n            <div style={{\r\n              fontSize: '1rem',\r\n              color: 'rgba(255,255,255,0.8)',\r\n              fontWeight: 'bold'\r\n            }}>\r\n              SE RELACIONA COM\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Opções de resposta - PADRÃO IMAGE ASSOCIATION EXATO */}\r\n        <div className={styles.answerOptions}>\r\n          {['😊', '😢', '😡', '😨'].map((option, index) => (\r\n            <button\r\n              key={index}\r\n              className={styles.answerButton}\r\n              onClick={() => handlePatternAnswer(option)}\r\n              aria-label={`Opção ${option}`}\r\n            >\r\n              <div style={{ fontSize: '2.5rem', marginBottom: '0.5rem' }}>\r\n                {option}\r\n              </div>\r\n              <div style={{ fontSize: '1rem', fontWeight: 'bold' }}>\r\n                {option === '😊' ? 'Feliz' : option === '😢' ? 'Triste' : option === '😡' ? 'Bravo' : 'Medo'}\r\n              </div>\r\n            </button>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  // 🔊 Cleanup do TTS quando componente é desmontado\r\n  useEffect(() => {\r\n    return () => {\r\n      // Cancelar qualquer TTS ativo quando sair do jogo\r\n      if ('speechSynthesis' in window) {\r\n        window.speechSynthesis.cancel();\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  const [analysisResults, setAnalysisResults] = useState(null)\r\n  const [attemptCount, setAttemptCount] = useState(0)\r\n\r\n  // Estados para métricas avançadas\r\n  const [sessionStartTime, setSessionStartTime] = useState(null)\r\n  const [pieceInteractions, setPieceInteractions] = useState([])\r\n  const [spatialStrategies, setSpatialStrategies] = useState([])\r\n  const [problemSolvingApproach, setProblemSolvingApproach] = useState('systematic')\r\n\r\n  // Inicializar sistema multissensorial quando sessionId estiver disponível\r\n  useEffect(() => {\r\n    if (sessionId && typeof sessionId === 'string' && sessionId.length > 0 && gameStarted && !multisensoryInitialized) {\r\n      const initializeMultisensorial = async () => {\r\n        try {\r\n          await initMultisensory();\r\n          console.log('✅ Sistema multissensorial inicializado com sessionId:', sessionId);\r\n        } catch (error) {\r\n          console.error('❌ Erro ao inicializar sistema multissensorial:', error);\r\n        }\r\n      };\r\n      initializeMultisensorial();\r\n    }\r\n  }, [sessionId, gameStarted, multisensoryInitialized, initMultisensory]);\r\n\r\n  // Calcular precisão\r\n  const getAccuracy = useCallback(() => {\r\n    if (gameStats.totalAttempts === 0) return 100\r\n    return Math.round((gameStats.completed / gameStats.totalAttempts) * 100)\r\n  }, [gameStats])\r\n\r\n  // 🧠 Coletar métricas específicas do quebra-cabeça\r\n  const collectPuzzleMetrics = async () => {\r\n    const currentTime = Date.now()\r\n    const sessionDuration = sessionStartTime ? currentTime - sessionStartTime : 0\r\n\r\n    // Métricas básicas do jogo\r\n    const basicMetrics = {\r\n      totalTime: sessionDuration,\r\n      correctAnswers: gameStats.completed,\r\n      incorrectAnswers: gameStats.totalAttempts - gameStats.completed,\r\n      accuracy: getAccuracy(),\r\n      difficultyLevel: difficulty,\r\n      completionLevel: isComplete ? 100 : (placedPieces.length / puzzlePieces.length) * 100\r\n    }\r\n\r\n    // Métricas específicas do quebra-cabeça\r\n    const puzzleSpecificMetrics = {\r\n      spatialReasoning: calculateSpatialReasoning(),\r\n      piecePlacementAccuracy: calculatePlacementAccuracy(),\r\n      completionStrategy: identifyCompletionStrategy(),\r\n      visualSpatialMemory: analyzeVisualSpatialMemory(),\r\n      problemSolvingApproach: problemSolvingApproach,\r\n      frustranceTolerance: calculateFrustranceTolerance(),\r\n      persistenceLevel: calculatePersistenceLevel(),\r\n      rotationAttempts: countRotationAttempts(),\r\n      sequentialPlacement: analyzeSequentialPlacement()\r\n    }\r\n\r\n    // Coletar métricas através do sistema unificado\r\n    await collectMetrics({\r\n      ...basicMetrics,\r\n      ...puzzleSpecificMetrics\r\n    })\r\n\r\n    // 🚀 Processar métricas avançadas para análise espacial\r\n    await processAdvancedMetrics({\r\n      gameType: 'QuebraCabeca',\r\n      sessionData: {\r\n        ...basicMetrics,\r\n        ...puzzleSpecificMetrics,\r\n        interactions: pieceInteractions,\r\n        spatialStrategies: spatialStrategies,\r\n        duration: sessionDuration\r\n      },\r\n      userProfile: {\r\n        preferredDifficulty: difficulty,\r\n        spatialPreferences: identifySpatialPreferences()\r\n      }\r\n    })\r\n  }\r\n\r\n  // Funções auxiliares para análise de métricas espaciais\r\n  const calculateSpatialReasoning = () => {\r\n    const correctPlacements = pieceInteractions.filter(i => i.correct).length\r\n    const totalInteractions = pieceInteractions.length\r\n    return totalInteractions > 0 ? (correctPlacements / totalInteractions) * 100 : 0\r\n  }\r\n\r\n  const calculatePlacementAccuracy = () => {\r\n    const firstTryCorrect = pieceInteractions.filter(i => i.attemptNumber === 1 && i.correct).length\r\n    const totalPieces = puzzlePieces.length\r\n    return totalPieces > 0 ? (firstTryCorrect / totalPieces) * 100 : 0\r\n  }\r\n\r\n  const identifyCompletionStrategy = () => {\r\n    if (spatialStrategies.length === 0) return 'unknown'\r\n    \r\n    const strategies = spatialStrategies.reduce((count, strategy) => {\r\n      count[strategy] = (count[strategy] || 0) + 1\r\n      return count\r\n    }, {})\r\n    \r\n    return Object.keys(strategies).reduce((a, b) => strategies[a] > strategies[b] ? a : b)\r\n  }\r\n\r\n  const analyzeVisualSpatialMemory = () => {\r\n    const memoryScore = pieceInteractions.reduce((score, interaction, index) => {\r\n      if (index > 0) {\r\n        const prevInteraction = pieceInteractions[index - 1]\r\n        if (interaction.pieceId === prevInteraction.pieceId && \r\n            interaction.position.x === prevInteraction.position.x &&\r\n            interaction.position.y === prevInteraction.position.y) {\r\n          score += 10 // Bonus por lembrar posição\r\n        }\r\n      }\r\n      return score\r\n    }, 0)\r\n    \r\n    return Math.min(memoryScore, 100)\r\n  }\r\n\r\n  const calculateFrustranceTolerance = () => {\r\n    const incorrectAttempts = pieceInteractions.filter(i => !i.correct).length\r\n    const totalAttempts = pieceInteractions.length\r\n    \r\n    if (totalAttempts === 0) return 100\r\n    \r\n    // Menor % de tentativas incorretas = maior tolerância\r\n    return Math.max(0, 100 - ((incorrectAttempts / totalAttempts) * 100))\r\n  }\r\n\r\n  const calculatePersistenceLevel = () => {\r\n    const maxAttemptsPerPiece = Math.max(...puzzlePieces.map(piece => {\r\n      return pieceInteractions.filter(i => i.pieceId === piece.id).length\r\n    }), 1)\r\n    \r\n    // Persistência baseada no número máximo de tentativas por peça\r\n    return Math.min(maxAttemptsPerPiece * 20, 100)\r\n  }\r\n\r\n  const countRotationAttempts = () => {\r\n    return pieceInteractions.filter(i => i.action === 'rotate').length\r\n  }\r\n\r\n  const analyzeSequentialPlacement = () => {\r\n    if (pieceInteractions.length < 2) return 'insufficient_data'\r\n    \r\n    let sequentialCount = 0\r\n    for (let i = 1; i < pieceInteractions.length; i++) {\r\n      const curr = pieceInteractions[i]\r\n      const prev = pieceInteractions[i - 1]\r\n      \r\n      if (curr.pieceId === prev.pieceId + 1) {\r\n        sequentialCount++\r\n      }\r\n    }\r\n    \r\n    const sequentialRatio = sequentialCount / (pieceInteractions.length - 1)\r\n    return sequentialRatio > 0.7 ? 'sequential' : sequentialRatio > 0.4 ? 'mixed' : 'random'\r\n  }\r\n\r\n  const identifySpatialPreferences = () => {\r\n    return {\r\n      preferredStartPosition: 'top-left', // Placeholder\r\n      rotationFrequency: countRotationAttempts() / Math.max(pieceInteractions.length, 1),\r\n      systematicApproach: problemSolvingApproach === 'systematic'\r\n    }\r\n  }\r\n\r\n  // 🧩 Registrar interação com peça para análise cognitiva\r\n  const recordPieceInteraction = async (pieceId, position, action, correct) => {\r\n    const interactionData = {\r\n      pieceId,\r\n      position,\r\n      action,\r\n      correct,\r\n      timestamp: Date.now(),\r\n      attemptNumber: attemptCount + 1,\r\n      difficultyLevel: difficulty,\r\n      sessionTime: Date.now() - (sessionStartTime || Date.now())\r\n    }\r\n\r\n    // Adicionar à lista de interações\r\n    setPieceInteractions(prev => [...prev, interactionData])\r\n    \r\n    // Coletar dados com os coletores especializados\r\n    try {\r\n      await collectorsHub.collectMoveData({\r\n        ...interactionData,\r\n        puzzleState: {\r\n          totalPieces: puzzlePieces.length,\r\n          placedPieces: placedPieces.length,\r\n          completionPercentage: (placedPieces.filter(p => p !== null).length / puzzlePieces.length) * 100\r\n        }\r\n      });\r\n      \r\n      // 🔄 Registrar interação multissensorial\r\n      await recordMultisensoryInteraction('game_interaction', {\r\n        interactionType: 'user_action',\r\n        gameSpecificData: interactionData,\r\n        multisensoryProcessing: {\r\n          spatialProcessing: { spatialReasoning: 0.7, visualSpatialMemory: 0.7, spatialOrientation: 0.7 },\r\n          cognitiveProcessing: { problemSolving: 0.7, processingSpeed: 0.7, adaptability: 0.7 },\r\n          behavioralProcessing: { interactionCount: 0.7, averageResponseTime: 0.7, persistence: 0.7 }\r\n        }\r\n      });\r\n\r\n      // A cada 3 tentativas, fazer análise cognitiva completa\r\n      const newAttemptCount = attemptCount + 1\r\n      setAttemptCount(newAttemptCount)\r\n\r\n      if (newAttemptCount % 3 === 0) {\r\n        performCognitiveAnalysis()\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('Erro ao coletar dados da interação:', error)\r\n    }\r\n  }\r\n\r\n  // 🧠 Realizar análise cognitiva completa\r\n  const performCognitiveAnalysis = async () => {\r\n    try {\r\n      setCognitiveAnalysisVisible(true)\r\n      \r\n      const analysisData = await collectorsHub.collectComprehensiveData({\r\n        sessionId: sessionStartTime?.toString() || 'session',\r\n        gameState: {\r\n          difficulty,\r\n          currentLevel: gameStats.level,\r\n          score: gameStats.score,\r\n          accuracy: getAccuracy(),\r\n          isComplete\r\n        },\r\n        interactions: pieceInteractions,\r\n        spatialData: {\r\n          strategies: spatialStrategies,\r\n          approach: problemSolvingApproach\r\n        }\r\n      })\r\n\r\n      setAnalysisResults(analysisData)\r\n\r\n      // Manter visível por 3 segundos\r\n      setTimeout(() => {\r\n        setCognitiveAnalysisVisible(false)\r\n      }, 3000)\r\n\r\n    } catch (error) {\r\n      console.error('Erro na análise cognitiva:', error)\r\n      setCognitiveAnalysisVisible(false)\r\n    }\r\n  }\r\n\r\n  // Gerar novo quebra-cabeça\r\n  const generateNewPuzzle = useCallback(() => {\r\n    const randomEmotion = QuebraCabecaConfig.emotions[Math.floor(Math.random() * QuebraCabecaConfig.emotions.length)]\r\n    const difficultyData = QuebraCabecaConfig.difficulties.find(d => d.id === difficulty)\r\n    \r\n    // Verificar se difficultyData existe\r\n    if (!difficultyData) {\r\n      console.error('Difficulty data not found for:', difficulty)\r\n      return\r\n    }\r\n    \r\n    setCurrentEmotion(randomEmotion)\r\n    setIsComplete(false)\r\n    setFeedback(null)\r\n    setGameStats(prev => ({ ...prev, totalAttempts: prev.totalAttempts + 1 }))\r\n\r\n    // Criar peças corretas da emoção\r\n    const correctPieces = randomEmotion.pieces.slice(0, difficultyData.pieces).map((piece, index) => ({\r\n      id: `correct_${index}`,\r\n      content: piece,\r\n      used: false,\r\n      isCorrect: true\r\n    }))\r\n\r\n    // Criar algumas peças distratoras (incorretas)\r\n    const allEmotions = QuebraCabecaConfig.emotions\r\n    const otherEmotions = allEmotions.filter(e => e.id !== randomEmotion.id)\r\n    const distractorPieces = []\r\n\r\n    // Adicionar 2-3 peças distratoras de outras emoções\r\n    for (let i = 0; i < Math.min(3, otherEmotions.length); i++) {\r\n      const randomOtherEmotion = otherEmotions[Math.floor(Math.random() * otherEmotions.length)]\r\n      const randomPiece = randomOtherEmotion.pieces[Math.floor(Math.random() * randomOtherEmotion.pieces.length)]\r\n      distractorPieces.push({\r\n        id: `distractor_${i}`,\r\n        content: randomPiece,\r\n        used: false,\r\n        isCorrect: false\r\n      })\r\n    }\r\n\r\n    // Combinar e embaralhar todas as peças\r\n    const allPieces = [...correctPieces, ...distractorPieces]\r\n    setPuzzlePieces(allPieces.sort(() => Math.random() - 0.5))\r\n    setPlacedPieces(Array(difficultyData.pieces).fill(null))\r\n  }, [difficulty]);\r\n\r\n  // Iniciar o jogo\r\n  const startGame = useCallback(async (selectedDifficulty) => {\r\n    // Configurar dificuldade se fornecida\r\n    if (selectedDifficulty) {\r\n      setDifficulty(selectedDifficulty);\r\n    }\r\n\r\n    // Esconder tela inicial\r\n    setShowStartScreen(false);\r\n    setGameStarted(true)\r\n    setGameStats({\r\n      level: 1,\r\n      score: 0,\r\n      completed: 0,\r\n      totalAttempts: 0\r\n    });\r\n\r\n    // 🔄 Inicializar integração multissensorial\r\n    try {\r\n      await initMultisensory(`session_${Date.now()}`, {\r\n        difficulty: difficulty,\r\n        gameMode: 'puzzle_solving',\r\n        userId: user?.id || 'anonymous'\r\n      });\r\n    } catch (error) {\r\n      console.warn('⚠️ Erro ao inicializar sessão multissensorial:', error);\r\n    }\r\n\r\n    generateNewPuzzle()\r\n  }, [initMultisensory, generateNewPuzzle, user]);\r\n\r\n  // Toggle TTS\r\n  const toggleTTS = useCallback(() => {\r\n    setTtsActive(prev => {\r\n      const newState = !prev;\r\n      localStorage.setItem('quebraCabeca_ttsActive', JSON.stringify(newState));\r\n      if (!newState && 'speechSynthesis' in window) {\r\n        window.speechSynthesis.cancel();\r\n      }\r\n      return newState;\r\n    });\r\n  }, []);\r\n\r\n  // Lidar com início do drag\r\n  const handleDragStart = useCallback((piece) => {\r\n    setDraggedPiece(piece)\r\n  }, [])\r\n\r\n  // Lidar com quebra-cabeça completo\r\n  const handlePuzzleComplete = useCallback(async () => {\r\n    setIsComplete(true)\r\n    \r\n    const points = QuebraCabecaConfig.gameSettings.pointsByDifficulty[difficulty.toUpperCase()] || 10\r\n    setGameStats(prev => ({\r\n      ...prev,\r\n      completed: prev.completed + 1,\r\n      score: prev.score + points,\r\n      level: prev.level + 1\r\n    }))\r\n\r\n    const message = QuebraCabecaConfig.encouragingMessages[Math.floor(Math.random() * QuebraCabecaConfig.encouragingMessages.length)]\r\n    \r\n    // Feedback sonoro ao completar (substituindo feedback visual)\r\n    speak(`${message} Você ganhou ${points} pontos!`);\r\n\r\n    // 🧠 Coletar métricas completas ao completar o quebra-cabeça\r\n    try {\r\n      await collectPuzzleMetrics()\r\n      console.log('🧩 Métricas do quebra-cabeça coletadas com sucesso!')\r\n    } catch (error) {\r\n      console.error('❌ Erro ao coletar métricas do quebra-cabeça:', error)\r\n    }\r\n  }, [difficulty, speak, collectPuzzleMetrics])\r\n\r\n  // Lidar com drop\r\n  const handleDrop = useCallback((targetIndex) => {\r\n    if (!draggedPiece) return\r\n\r\n    // Verificar se o slot já está ocupado\r\n    if (placedPieces[targetIndex] !== null) {\r\n      speak('Este slot já está ocupado!');\r\n      setDraggedPiece(null);\r\n      return;\r\n    }\r\n\r\n    const newPlacedPieces = [...placedPieces]\r\n    newPlacedPieces[targetIndex] = draggedPiece\r\n\r\n    // No quebra-cabeça emocional, verificar se a peça pertence à emoção atual\r\n    const isCorrectPiece = currentEmotion && currentEmotion.pieces.includes(draggedPiece.content)\r\n\r\n    // 🧠 Registrar interação com métricas avançadas\r\n    recordPieceInteraction(\r\n      draggedPiece.id,\r\n      { x: targetIndex % 2, y: Math.floor(targetIndex / 2) },\r\n      'place',\r\n      isCorrectPiece\r\n    )\r\n\r\n    setPlacedPieces(newPlacedPieces)\r\n\r\n    // Marcar peça como usada apenas se for correta\r\n    if (isCorrectPiece) {\r\n      setPuzzlePieces(prev => prev.map(p =>\r\n        p.id === draggedPiece.id ? { ...p, used: true } : p\r\n      ))\r\n\r\n      speak('Peça colocada corretamente!');\r\n      setGameStats(prev => ({\r\n        ...prev,\r\n        score: prev.score + 10,\r\n        completed: prev.completed + 1\r\n      }))\r\n\r\n      // Verificar se completou o quebra-cabeça\r\n      const correctPiecesPlaced = newPlacedPieces.filter(piece =>\r\n        piece !== null && currentEmotion.pieces.includes(piece.content)\r\n      ).length;\r\n\r\n      if (correctPiecesPlaced === currentEmotion.pieces.length) {\r\n        setTimeout(() => handlePuzzleComplete(), 500);\r\n      }\r\n    } else {\r\n      speak('Esta peça não combina com a emoção!');\r\n      // Remover peça incorreta após um tempo\r\n      setTimeout(() => {\r\n        setPlacedPieces(prev => {\r\n          const updated = [...prev]\r\n          updated[targetIndex] = null\r\n          return updated\r\n        })\r\n      }, 1500)\r\n    }\r\n\r\n    setDraggedPiece(null)\r\n  }, [draggedPiece, placedPieces, currentEmotion, recordPieceInteraction, speak, handlePuzzleComplete]);\r\n\r\n  // Função para remover peça do slot\r\n  const handleRemovePiece = useCallback((slotIndex) => {\r\n    const pieceToRemove = placedPieces[slotIndex];\r\n    if (!pieceToRemove) return;\r\n\r\n    // Remover do slot\r\n    const newPlacedPieces = [...placedPieces];\r\n    newPlacedPieces[slotIndex] = null;\r\n    setPlacedPieces(newPlacedPieces);\r\n\r\n    // Marcar peça como disponível novamente\r\n    setPuzzlePieces(prev => prev.map(p =>\r\n      p.id === pieceToRemove.id ? { ...p, used: false } : p\r\n    ));\r\n\r\n    speak('Peça removida!');\r\n  }, [placedPieces, speak]);\r\n\r\n\r\n\r\n  // Próximo quebra-cabeça\r\n  const nextPuzzle = () => {\r\n    // Resetar métricas para nova rodada\r\n    setPieceInteractions([])\r\n    setSpatialStrategies([])\r\n    setProblemSolvingApproach('systematic')\r\n    \r\n    setTimeout(() => {\r\n      generateNewPuzzle()\r\n    }, 100)\r\n  }\r\n\r\n  // Função para finalizar sessão e coletar métricas finais\r\n  const handleGameEnd = async () => {\r\n    if (sessionStartTime && pieceInteractions.length > 0) {\r\n      try {\r\n        await collectPuzzleMetrics()\r\n        console.log('🎯 Métricas finais do quebra-cabeça coletadas!')\r\n      } catch (error) {\r\n        console.error('❌ Erro ao coletar métricas finais:', error)\r\n      }\r\n    }\r\n    \r\n    if (onBack) {\r\n      onBack()\r\n    }\r\n  }\r\n\r\n\r\n  // Tela de início padronizada\r\n  if (showStartScreen) {\r\n    console.log('🧩 QuebraCabeca: Renderizando tela de início padronizada');\r\n    return (\r\n      <GameStartScreen\r\n        gameTitle=\"Quebra-cabeça Emocional\"\r\n        gameDescription=\"Desenvolva inteligência emocional através de puzzles interativos\"\r\n        gameIcon=\"🧩\"\r\n        onStart={startGame}\r\n        onBack={onBack}\r\n        difficulties={[\r\n          { id: 'easy', name: 'Fácil', description: 'Peças grandes\\nIdeal para iniciantes', icon: '😊' },\r\n          { id: 'medium', name: 'Médio', description: 'Peças médias\\nDesafio equilibrado', icon: '🎯' },\r\n          { id: 'hard', name: 'Avançado', description: 'Peças pequenas\\nPara especialistas', icon: '🚀' }\r\n        ]}\r\n      />\r\n    );\r\n  }\r\n  return (\r\n    <div \r\n      className={`${styles.quebraCabecaGame} ${settings.reducedMotion ? 'reduced-motion' : ''} ${settings.highContrast ? 'high-contrast' : ''}`}\r\n      data-font-size={settings.fontSize}\r\n      data-theme={settings.colorScheme}\r\n      style={{\r\n        fontSize: settings.fontSize === 'small' ? '0.875rem' : \r\n                 settings.fontSize === 'large' ? '1.25rem' : '1rem'\r\n      }}\r\n    >\r\n      {/* 🧠 Indicador de Análise Cognitiva */}\r\n      {cognitiveAnalysisVisible && (\r\n        <div className=\"cognitive-analysis-indicator\">\r\n          <div className=\"analysis-content\">\r\n            <div className=\"analysis-icon\">🎯🧠</div>\r\n            <div className=\"analysis-text\">\r\n              <div className=\"analysis-title\">Análise Espacial em Progresso</div>\r\n              <div className=\"analysis-details\">\r\n                Avaliando raciocínio espacial e estratégias de resolução...\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* 📊 Painel de Insights Cognitivos */}\r\n      {analysisResults && (\r\n        <div className=\"cognitive-insights-panel\">\r\n          <div className=\"insights-header\">\r\n            <span className=\"insights-icon\">🎯</span>\r\n            <span className=\"insights-title\">Análise Cognitiva</span>\r\n          </div>\r\n          <div className=\"insights-content\">\r\n            <div className=\"insight-item\">\r\n              <span className=\"insight-label\">Raciocínio Espacial:</span>\r\n              <span className=\"insight-value\">{analysisResults.spatialReasoning || 'Em análise'}</span>\r\n            </div>\r\n            <div className=\"insight-item\">\r\n              <span className=\"insight-label\">Estratégia:</span>\r\n              <span className=\"insight-value\">{analysisResults.strategy || problemSolvingApproach}</span>\r\n            </div>\r\n            <div className=\"insight-item\">\r\n              <span className=\"insight-label\">Padrão Cognitivo:</span>\r\n              <span className=\"insight-value\">{analysisResults.cognitivePattern || 'Identificando...'}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <div className={styles.gameContent}>\r\n        {/* Header do jogo - PADRÃO LETTERRECOGNITION EXATO */}\r\n        <div className={styles.gameHeader}>\r\n          <h1 className={styles.gameTitle}>\r\n            🧩 Quebra-Cabeça Emocional V3\r\n            <div style={{ fontSize: '0.7rem', opacity: 0.8, marginTop: '0.25rem' }}>\r\n              {ACTIVITY_TYPES[gameState.currentActivity?.toUpperCase()]?.name || 'Emocional'}\r\n            </div>\r\n          </h1>\r\n          <button\r\n            className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : ''}`}\r\n            onClick={toggleTTS}\r\n            title={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}\r\n            aria-label={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}\r\n          >\r\n            {ttsActive ? '🔊' : '🔇'}\r\n          </button>\r\n        </div>\r\n\r\n        {/* Header com estatísticas - PADRÃO LETTERRECOGNITION EXATO */}\r\n        <div className={styles.gameStats}>\r\n          <div className={styles.statCard}>\r\n            <div className={styles.statValue}>{gameState.score}</div>\r\n            <div className={styles.statLabel}>Pontos</div>\r\n          </div>\r\n          <div className={styles.statCard}>\r\n            <div className={styles.statValue}>{gameState.round}</div>\r\n            <div className={styles.statLabel}>Rodada</div>\r\n          </div>\r\n          <div className={styles.statCard}>\r\n            <div className={styles.statValue}>{gameState.accuracy}%</div>\r\n            <div className={styles.statLabel}>Precisão</div>\r\n          </div>\r\n          <div className={styles.statCard}>\r\n            <div className={styles.statValue}>{gameState.level}</div>\r\n            <div className={styles.statLabel}>Nível</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Menu de atividades - PADRÃO IMAGE ASSOCIATION EXATO */}\r\n        <div className={styles.activityMenu}>\r\n          {Object.values(ACTIVITY_TYPES).map((activity) => (\r\n            <button\r\n              key={activity.id}\r\n              className={`${styles.activityButton} ${\r\n                gameState.currentActivity === activity.id ? styles.active : ''\r\n              }`}\r\n              onClick={() => switchActivity(activity.id)}\r\n            >\r\n              <span>{activity.icon}</span>\r\n              <span>{activity.name}</span>\r\n            </button>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Renderização da atividade atual - PADRÃO LETTERRECOGNITION EXATO */}\r\n        {gameState.currentActivity === ACTIVITY_TYPES.EMOTION_PUZZLE.id && renderEmotionPuzzle()}\r\n        {gameState.currentActivity === ACTIVITY_TYPES.PIECE_ROTATION.id && renderPieceRotation()}\r\n        {gameState.currentActivity === ACTIVITY_TYPES.PATTERN_PUZZLE.id && renderPatternPuzzle()}\r\n\r\n        {/* Controles do jogo - PADRÃO PADRONIZADO */}\r\n        <div className={styles.gameControls}>\r\n          <button className={styles.controlButton} onClick={() => speak('Quebra-cabeça emocional. Monte as peças para formar emoções e desenvolver inteligência emocional.')}>\r\n            🔊 Explicar\r\n          </button>\r\n          <button className={styles.controlButton} onClick={() => setShowStartScreen(true)}>\r\n            🔄 Reiniciar\r\n          </button>\r\n          <button className={styles.controlButton} onClick={onBack}>\r\n            ⬅️ Voltar\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default QuebraCabecaGame\r\n"], "names": ["useContext", "useRef", "useState", "ttsActive", "gameStats", "useCallback", "difficulty", "settings", "jsxDEV", "Fragment", "useEffect"], "mappings": ";;;;;;AAYO,MAAM,0BAA0B;AAAA,EACrC,cAAc;AACZ,SAAK,cAAc;AAAA,MACjB,kBAAkB,CAAE;AAAA,MACpB,kBAAkB,CAAE;AAAA,MACpB,eAAe,CAAE;AAAA,MACjB,kBAAkB,CAAE;AAAA,MACpB,oBAAoB,CAAE;AAAA,MACtB,uBAAuB,CAAE;AAAA,IAC/B;AAEI,SAAK,iBAAiB;AAAA,MACpB,mBAAmB;AAAA,MACnB,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,iBAAiB,CAAE;AAAA,MACnB,gBAAgB;AAAA,IACtB;AAEI,SAAK,oBAAoB;AAAA,MACvB,qBAAqB;AAAA,MACrB,iBAAiB;AAAA,MACjB,uBAAuB;AAAA,IAC7B;AAEI,SAAK,YAAY;AAEjB,QAAI,KAAK,WAAW;AAClB,cAAQ,IAAI,2CAA2C;AAAA,IACxD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,yBAAyB,MAAM;AAC7B,QAAI;AAEF,UAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;AACrC,gBAAQ,KAAK,2DAA2D;AACxE,eAAO,CAAA;AAAA,MACR;AAGD,YAAM,iBAAiB,KAAK,kBAAkB,EAAE,GAAG,GAAG,GAAG;AACzD,YAAM,iBAAiB,KAAK,kBAAkB,EAAE,GAAG,GAAG,GAAG;AAEzD,YAAM,iBAAiB;AAAA,QACrB,WAAW,KAAK,aAAa,KAAK,IAAK;AAAA,QACvC,SAAS,KAAK,WAAW;AAAA,QACzB;AAAA,QACA;AAAA,QACA,iBAAiB,KAAK,yBAAyB,gBAAgB,cAAc;AAAA,QAC7E,oBAAoB,KAAK,sBAAsB;AAAA,QAC/C,kBAAkB,KAAK,oBAAoB;AAAA,QAC3C,gBAAgB,KAAK,wBAAwB,gBAAgB,cAAc;AAAA,QAC3E,kBAAkB,KAAK,uBAAuB,KAAK,YAAY,KAAK,iBAAiB;AAAA,QACrF,gBAAgB,KAAK,kBAAkB;AAAA,QACvC,YAAY,KAAK,cAAc;AAAA,MACvC;AAGM,YAAM,sBAAsB,KAAK,0BAA0B,cAAc;AAGzE,YAAM,yBAAyB,KAAK,uBAAuB,IAAI;AAE/D,WAAK,YAAY,iBAAiB,KAAK;AAAA,QACrC,GAAG;AAAA,QACH;AAAA,QACA;AAAA,QACA,eAAe,KAAK,oBAAoB,cAAc;AAAA,MAC9D,CAAO;AAED,WAAK,qBAAqB,cAAc;AAExC,UAAI,KAAK,WAAW;AAClB,gBAAQ,IAAI,+DAA+D;AAAA,UACzE,UAAU,eAAe;AAAA,UACzB,aAAa,oBAAoB;AAAA,UACjC,eAAe,KAAK,oBAAoB,cAAc;AAAA,QAChE,CAAS;AAAA,MACF;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,cAAQ,MAAM,yCAAyC,KAAK;AAC5D,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,oBAAoB,MAAM;AACxB,QAAI;AACF,YAAM,kBAAkB;AAAA,QACtB,WAAW,KAAK,aAAa,KAAK,IAAK;AAAA,QACvC,SAAS,KAAK;AAAA,QACd,oBAAoB,KAAK;AAAA,QACzB,mBAAmB,KAAK;AAAA,QACxB,kBAAkB,KAAK;AAAA,QACvB,eAAe,KAAK,iBAAiB;AAAA,QACrC,cAAc,KAAK,gBAAgB;AAAA,QACnC,kBAAkB,KAAK,0BAA0B,IAAI;AAAA,QACrD,qBAAqB,KAAK,6BAA6B,IAAI;AAAA,QAC3D,oBAAoB,KAAK,2BAA2B,IAAI;AAAA,QACxD,kBAAkB,KAAK,wBAAwB,IAAI;AAAA,MAC3D;AAGM,YAAM,yBAAyB,KAAK,sBAAsB,eAAe;AAEzE,WAAK,YAAY,iBAAiB,KAAK;AAAA,QACrC,GAAG;AAAA,QACH;AAAA,QACA,sBAAsB,KAAK,2BAA2B,eAAe;AAAA,MAC7E,CAAO;AAED,UAAI,KAAK,WAAW;AAClB,gBAAQ,IAAI,2DAA2D;AAAA,UACrE,UAAU,gBAAgB;AAAA,UAC1B,OAAO,gBAAgB;AAAA,UACvB,UAAU,gBAAgB;AAAA,QACpC,CAAS;AAAA,MACF;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,cAAQ,MAAM,qCAAqC,KAAK;AACxD,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,qBAAqB,MAAM;AACzB,QAAI;AACF,YAAM,gBAAgB;AAAA,QACpB,WAAW,KAAK,aAAa,KAAK,IAAK;AAAA,QACvC,aAAa,KAAK,qBAAqB,KAAK,mBAAmB;AAAA,QAC/D,kBAAkB,KAAK,0BAA0B,IAAI;AAAA,QACrD,mBAAmB,KAAK,yBAAyB,IAAI;AAAA,QACrD,mBAAmB,KAAK,wBAAwB,IAAI;AAAA,QACpD,iBAAiB,KAAK,yBAAyB,IAAI;AAAA,QACnD,eAAe,KAAK,qBAAqB,IAAI;AAAA,QAC7C,wBAAwB,KAAK,6BAA6B,IAAI;AAAA,MACtE;AAEM,WAAK,YAAY,cAAc,KAAK,aAAa;AAEjD,UAAI,KAAK,WAAW;AAClB,gBAAQ,IAAI,6DAA6D;AAAA,UACvE,MAAM,cAAc;AAAA,UACpB,UAAU,cAAc;AAAA,UACxB,eAAe,cAAc;AAAA,QACvC,CAAS;AAAA,MACF;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,cAAQ,MAAM,uCAAuC,KAAK;AAC1D,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,0BAA0B,MAAM;AAC9B,QAAI;AACF,YAAM,iBAAiB;AAAA,QACrB,WAAW,KAAK,aAAa,KAAK,IAAK;AAAA,QACvC,aAAa,KAAK,oBAAoB,KAAK,UAAU;AAAA,QACrD,qBAAqB,KAAK,yBAAyB,IAAI;AAAA,QACvD,iBAAiB,KAAK,mBAAmB;AAAA,QACzC,mBAAmB,KAAK,wBAAwB,IAAI;AAAA,QACpD,kBAAkB,KAAK,wBAAwB,IAAI;AAAA,QACnD,mBAAmB,KAAK,yBAAyB,IAAI;AAAA,QACrD,kBAAkB,KAAK,wBAAwB,IAAI;AAAA,MAC3D;AAEM,WAAK,YAAY,mBAAmB,KAAK,cAAc;AAEvD,UAAI,KAAK,WAAW;AAClB,gBAAQ,IAAI,sEAAsE;AAAA,UAChF,MAAM,eAAe;AAAA,UACrB,UAAU,eAAe;AAAA,UACzB,YAAY,eAAe;AAAA,QACrC,CAAS;AAAA,MACF;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,cAAQ,MAAM,gDAAgD,KAAK;AACnE,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,QAAQ,MAAM;AACZ,WAAO,KAAK,QAAQ,IAAI;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,QAAQ,UAAU;AAChB,QAAI;AACF,UAAI,CAAC,UAAU;AACb,gBAAQ,KAAK,mDAAmD;AAChE,eAAO,KAAK;MACb;AAGD,YAAM,kBAAkB,SAAS,cAAc;AAC/C,YAAM,YAAY,SAAS,aAAa;AACxC,YAAM,eAAe,SAAS,gBAAgB;AAG9C,YAAM,qBAAqB,KAAK,yBAAyB,iBAAiB,YAAY;AACtF,YAAM,mBAAmB,KAAK,sBAAsB,WAAW,eAAe;AAC9E,YAAM,iBAAiB,KAAK,qBAAqB,cAAc,eAAe;AAC9E,YAAM,wBAAwB,KAAK,4BAA4B,eAAe;AAG9E,YAAM,kBAAkB;AAAA,QACtB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,sBAAsB;AAAA,QACtB,qBAAqB,KAAK,6BAA6B;AAAA,UACrD,mBAAmB;AAAA,UACnB,iBAAiB;AAAA,UACjB,eAAe;AAAA,UACf,sBAAsB;AAAA,QAChC,CAAS;AAAA,QACD,WAAW,KAAK,IAAK;AAAA,MAC7B;AAEM,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,cAAQ,MAAM,qDAAqD,KAAK;AACxE,aAAO,KAAK;IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,oBAAoB;AAClB,WAAO;AAAA,MACL,mBAAmB,EAAE,OAAO,KAAK,OAAO,UAAW;AAAA,MACnD,gBAAgB,EAAE,OAAO,KAAK,OAAO,UAAW;AAAA,MAChD,eAAe,EAAE,OAAO,KAAK,OAAO,UAAW;AAAA,MAC/C,sBAAsB,EAAE,OAAO,KAAK,OAAO,UAAW;AAAA,MACtD,qBAAqB;AAAA,MACrB,WAAW,KAAK,IAAK;AAAA,IAC3B;AAAA,EACG;AAAA;AAAA;AAAA;AAAA,EAKD,6BAA6B,QAAQ;AACnC,QAAI,CAAC,UAAU,CAAC,OAAO,OAAQ,QAAO;AACtC,WAAO,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC,IAAI,OAAO;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAKD,yBAAyB,YAAY,cAAc;AAEjD,WAAO;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,QACP,iBAAiB;AAAA,QACjB,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,MACtB;AAAA,IACP;AAAA,EACG;AAAA;AAAA;AAAA;AAAA,EAKD,sBAAsB,WAAW,YAAY;AAE3C,WAAO;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,QACP,kBAAkB;AAAA,QAClB,eAAe;AAAA,QACf,kBAAkB;AAAA,MACnB;AAAA,IACP;AAAA,EACG;AAAA;AAAA;AAAA;AAAA,EAKD,qBAAqB,cAAc,YAAY;AAE7C,WAAO;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,MAClB;AAAA,IACP;AAAA,EACG;AAAA;AAAA;AAAA;AAAA,EAKD,4BAA4B,YAAY;AAEtC,WAAO;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,QACP,wBAAwB;AAAA,QACxB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACpB;AAAA,IACP;AAAA,EACG;AAAA;AAAA,EAID,yBAAyB,QAAQ,QAAQ;AAEvC,QAAI,CAAC,UAAU,CAAC,UACZ,OAAO,OAAO,MAAM,YAAY,OAAO,OAAO,MAAM,YACpD,OAAO,OAAO,MAAM,YAAY,OAAO,OAAO,MAAM,UAAU;AAChE,aAAO;AAAA,IACR;AAED,UAAM,WAAW,KAAK;AAAA,MACpB,KAAK,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC,IAC/B,KAAK,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC;AAAA,IACrC;AAGI,WAAO,KAAK,IAAI,GAAG,IAAK,WAAW,GAAI;AAAA,EACxC;AAAA,EAED,wBAAwB,QAAQ,QAAQ;AAEtC,QAAI,CAAC,UAAU,CAAC,UAAU,OAAO,OAAO,MAAM,YAAY,OAAO,OAAO,MAAM,YAC1E,OAAO,OAAO,MAAM,YAAY,OAAO,OAAO,MAAM,UAAU;AAChE,aAAO;AAAA,IACR;AAED,UAAM,WAAW,KAAK;AAAA,MACpB,KAAK,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC,IAC/B,KAAK,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC;AAAA,IACrC;AAEI,QAAI,YAAY,GAAI,QAAO;AAC3B,QAAI,YAAY,GAAI,QAAO;AAC3B,QAAI,YAAY,GAAI,QAAO;AAC3B,WAAO;AAAA,EACR;AAAA,EAED,0BAA0B,MAAM;AAC9B,QAAI,CAAC,KAAK,qBAAqB,CAAC,KAAK,iBAAkB,QAAO;AAE9D,UAAM,kBAAkB,KAAK,IAAI,KAAK,oBAAoB,KAAK,gBAAgB;AAC/E,UAAM,uBAAuB,KAAK,IAAI,iBAAiB,MAAM,eAAe;AAE5E,WAAO,KAAK,IAAI,GAAG,IAAK,uBAAuB,GAAI;AAAA,EACpD;AAAA,EAED,6BAA6B,MAAM;AACjC,QAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,cAAe,QAAO;AAEtD,WAAO,KAAK,iBAAiB,KAAK,eAAe;AAAA,EAClD;AAAA,EAED,0BAA0B,SAAS;AACjC,UAAM,WAAW,QAAQ;AACzB,UAAM,mBAAmB,QAAQ,qBAAqB,IAAI;AAE1D,WAAO;AAAA,MACL,OAAO,WAAW,MAAM,SAAS,WAAW,MAAM,WAAW;AAAA,MAC7D,sBAAsB;AAAA,MACtB,mBAAmB,KAAK,wBAAwB,OAAO;AAAA,IAC7D;AAAA,EACG;AAAA,EAED,wBAAwB,MAAM;AAC5B,UAAM,QAAQ,KAAK,iBAAiB;AACpC,UAAM,OAAO,KAAK,gBAAgB;AAElC,QAAI,SAAS,KAAK,OAAO,IAAM,QAAO;AACtC,QAAI,QAAQ,EAAG,QAAO;AACtB,QAAI,OAAO,IAAM,QAAO;AACxB,WAAO;AAAA,EACR;AAAA,EAED,qBAAqB,qBAAqB;AACxC,WAAO,sBAAsB,oBAAoB,SAAS;AAAA,EAC3D;AAAA,EAED,oBAAoB,SAAS;AAC3B,UAAM,UAAU;AAAA,MACd,QAAQ,kBAAkB,MAAM,IAAI;AAAA,MACpC,QAAQ,mBAAmB,IAAI,IAAI;AAAA,MACnC,QAAQ,iBAAiB,MAAO,IAAI;AAAA,IAC1C;AAEI,UAAM,OAAO,QAAQ,OAAO,CAAC,KAAK,WAAW,MAAM,QAAQ,CAAC;AAE5D,QAAI,QAAQ,EAAG,QAAO;AACtB,QAAI,SAAS,EAAG,QAAO;AACvB,WAAO;AAAA,EACR;AAAA;AAAA,EAID,mBAAmB;AACjB,QAAI;AACF,aAAO;AAAA,QACL,SAAS;AAAA,UACP,mBAAmB,KAAK,eAAe;AAAA,UACvC,wBAAwB,KAAK,gCAAiC;AAAA,UAC9D,qBAAqB,KAAK,6BAA8B;AAAA,UACxD,gBAAgB,KAAK,eAAe;AAAA,UACpC,qBAAqB,KAAK,6BAA8B;AAAA,QACzD;AAAA,QACD,UAAU;AAAA,UACR,mBAAmB,KAAK,+BAAgC;AAAA,UACxD,mBAAmB,KAAK,yBAA0B;AAAA,UAClD,eAAe,KAAK,gCAAiC;AAAA,UACrD,oBAAoB,KAAK,gCAAiC;AAAA,UAC1D,kBAAkB,KAAK,yBAA0B;AAAA,QAClD;AAAA,QACD,iBAAiB,KAAK,+BAAgC;AAAA,QACtD,WAAW,KAAK,IAAK;AAAA,MAC7B;AAAA,IACK,SAAQ,OAAO;AACd,cAAQ,MAAM,qCAAqC,KAAK;AACxD,aAAO,EAAE,OAAO;IACjB;AAAA,EACF;AAAA,EAED,iCAAiC;AAC/B,UAAM,kBAAkB,CAAA;AACxB,UAAM,cAAc,KAAK;AAEzB,QAAI,cAAc,KAAK;AACrB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,MAClB,CAAO;AAAA,IACF;AAED,QAAI,KAAK,eAAe,iBAAiB,GAAG;AAC1C,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,MAClB,CAAO;AAAA,IACF;AAED,WAAO;AAAA,EACR;AAAA;AAAA,EAID,qBAAqB,SAAS;AAC5B,SAAK,eAAe;AACpB,SAAK,eAAe,gBAAgB,QAAQ;AAE5C,QAAI,CAAC,QAAQ,oBAAoB;AAC/B,WAAK,eAAe;AAAA,IACrB;AAED,QAAI,QAAQ,gBAAgB;AAC1B,WAAK,eAAe,gBAAgB,KAAK,QAAQ,cAAc;AAAA,IAChE;AAAA,EACF;AAAA,EAED,kCAAkC;AAChC,UAAM,iBAAiB,KAAK,YAAY;AACxC,QAAI,eAAe,WAAW,EAAG,QAAO;AAExC,UAAM,gBAAgB,eAAe,OAAO,CAAC,KAAK,SAAS,MAAM,KAAK,iBAAiB,CAAC;AACxF,WAAO,gBAAgB,eAAe;AAAA,EACvC;AAAA,EAED,+BAA+B;AAC7B,UAAM,eAAe,KAAK,YAAY;AACtC,QAAI,aAAa,WAAW,EAAG,QAAO;AAEtC,UAAM,gBAAgB,aAAa,OAAO,CAAC,KAAK,SAAS,MAAM,KAAK,kBAAkB,CAAC;AACvF,WAAO,gBAAgB,aAAa;AAAA,EACrC;AAAA,EAED,YAAY;AACV,SAAK,cAAc;AAAA,MACjB,kBAAkB,CAAE;AAAA,MACpB,kBAAkB,CAAE;AAAA,MACpB,eAAe,CAAE;AAAA,MACjB,kBAAkB,CAAE;AAAA,MACpB,oBAAoB,CAAE;AAAA,MACtB,uBAAuB,CAAE;AAAA,IAC/B;AAEI,SAAK,iBAAiB;AAAA,MACpB,mBAAmB;AAAA,MACnB,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,iBAAiB,CAAE;AAAA,MACnB,gBAAgB;AAAA,IACtB;AAEI,QAAI,KAAK,WAAW;AAClB,cAAQ,IAAI,6CAA6C;AAAA,IAC1D;AAAA,EACF;AAAA;AAAA,EAGD,uBAAuB,YAAY,mBAAmB;AAEpD,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACR;AACD,WAAO,KAAK,WAAW,MAAM;AAAA,EAC9B;AAAA;AAAA,EAED,uBAAuB,MAAM;AAC3B,WAAO;AAAA,MACL,kBAAkB,KAAK,oBAAoB;AAAA,MAC3C,oBAAoB,KAAK,2BAA4B;AAAA,MACrD,YAAY,KAAK,+BAA+B,IAAI;AAAA,MACpD,UAAU,KAAK,gCAAgC,IAAI;AAAA,IACzD;AAAA,EACG;AAAA,EAED,+BAA+B,MAAM;AACnC,QAAI,KAAK,gBAAgB,EAAG,QAAO;AACnC,QAAI,KAAK,gBAAgB,EAAG,QAAO;AACnC,WAAO;AAAA,EACR;AAAA,EAED,gCAAgC,MAAM;AACpC,QAAI,CAAC,KAAK,qBAAqB,CAAC,KAAK,iBAAkB,QAAO;AAC9D,UAAM,OAAO,KAAK,IAAI,KAAK,oBAAoB,KAAK,gBAAgB;AACpE,WAAO,KAAK,IAAI,GAAG,IAAK,OAAO,GAAI;AAAA,EACpC;AAAA;AAAA,EAGD,6BAA6B;AAAE,WAAO;AAAA,EAAa;AAAA,EACnD,6BAA6B;AAAE,WAAO;AAAA,EAAa;AAAA,EACnD,4BAA4B;AAAE,WAAO,KAAK,OAAM,IAAK,MAAM;AAAA,EAAM;AAAA,EACjE,2BAA2B;AAAE,WAAO,EAAE,SAAS,aAAc;AAAA,EAAG;AAAA,EAChE,0BAA0B;AAAE,WAAO;AAAA,EAAa;AAAA,EAChD,2BAA2B;AAAE,WAAO,KAAK,OAAM,IAAK,MAAM;AAAA,EAAM;AAAA,EAChE,uBAAuB;AAAE,WAAO,EAAE,UAAU,SAAU;AAAA,EAAG;AAAA,EACzD,+BAA+B;AAAE,WAAO;AAAA,EAAS;AAAA,EACjD,sBAAsB;AAAE,WAAO;AAAA,EAAc;AAAA,EAC7C,2BAA2B;AAAE,WAAO,KAAK,OAAM,IAAK,MAAM;AAAA,EAAM;AAAA,EAChE,0BAA0B;AAAE,WAAO;AAAA,EAAW;AAAA,EAC9C,0BAA0B;AAAE,WAAO,EAAE,YAAY,IAAK;AAAA,EAAG;AAAA,EACzD,2BAA2B;AAAE,WAAO,EAAE,WAAW,YAAa;AAAA,EAAG;AAAA,EACjE,0BAA0B;AAAE,WAAO,EAAE,UAAU,CAAC,SAAS,SAAS,EAAC;AAAA,EAAK;AAAA,EACxE,0BAA0B;AAAE,WAAO;AAAA,EAAc;AAAA,EACjD,iCAAiC;AAAE,WAAO,EAAE,OAAO,YAAa;AAAA,EAAG;AAAA,EACnE,2BAA2B;AAAE,WAAO,EAAE,aAAa,aAAc;AAAA,EAAG;AAAA,EACpE,kCAAkC;AAAE,WAAO,EAAE,UAAU,UAAW;AAAA,EAAG;AAAA,EACrE,kCAAkC;AAAE,WAAO,EAAE,OAAO,OAAQ;AAAA,EAAG;AAAA,EAC/D,2BAA2B;AAAE,WAAO,KAAK;AAAA,EAAoB;AAC/D;AC5kBO,MAAM,wBAAwB;AAAA,EACnC,cAAc;AACZ,SAAK,qBAAqB;AAAA,MACxB,YAAY,CAAE;AAAA,MACd,gBAAgB,CAAE;AAAA,MAClB,UAAU,CAAE;AAAA,MACZ,eAAe,CAAE;AAAA,MACjB,aAAa,CAAE;AAAA,MACf,eAAe,CAAE;AAAA,IACvB;AAEI,SAAK,iBAAiB;AAAA,MACpB,eAAe;AAAA,MACf,gBAAgB,oBAAI,IAAK;AAAA,MACzB,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,IACzB;AAEI,SAAK,oBAAoB;AAAA,MACvB,mBAAmB;AAAA,MACnB,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,sBAAsB;AAAA,IAC5B;AAEI,SAAK,YAAY;AAEjB,QAAI,KAAK,WAAW;AAClB,cAAQ,IAAI,yCAAyC;AAAA,IACtD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,uBAAuB,MAAM;AAC3B,QAAI;AACF,YAAM,kBAAkB;AAAA,QACtB,WAAW,KAAK,aAAa,KAAK,IAAK;AAAA,QACvC,WAAW,KAAK,aAAa,KAAK;AAAA,QAClC,cAAc,KAAK,iBAAiB,IAAI;AAAA,QACxC,gBAAgB,KAAK,gBAAgB,IAAI;AAAA,QACzC,eAAe,KAAK,oBAAoB,IAAI;AAAA,QAC5C,gBAAgB,KAAK,yBAAyB,IAAI;AAAA,QAClD,eAAe,KAAK,oBAAoB,IAAI;AAAA,QAC5C,cAAc,KAAK,oBAAoB,IAAI;AAAA,QAC3C,uBAAuB,KAAK,+BAA+B,IAAI;AAAA,QAC/D,sBAAsB,KAAK,yBAAyB,IAAI;AAAA,MAChE;AAGM,YAAM,sBAAsB,KAAK,4BAA4B,iBAAiB,IAAI;AAGlF,YAAM,wBAAwB,KAAK,qBAAqB,IAAI;AAE5D,WAAK,mBAAmB,WAAW,KAAK;AAAA,QACtC,GAAG;AAAA,QACH;AAAA,QACA;AAAA,QACA,eAAe,KAAK,oBAAoB,IAAI;AAAA,MACpD,CAAO;AAED,WAAK,sBAAsB,eAAe;AAE1C,UAAI,KAAK,WAAW;AAClB,gBAAQ,IAAI,qDAAqD;AAAA,UAC/D,UAAU,gBAAgB;AAAA,UAC1B,UAAU,gBAAgB;AAAA,UAC1B,eAAe,gBAAgB;AAAA,QACzC,CAAS;AAAA,MACF;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,cAAQ,MAAM,iCAAiC,KAAK;AACpD,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,oBAAoB,MAAM;AACxB,QAAI;AACF,YAAM,kBAAkB;AAAA,QACtB,WAAW,KAAK,aAAa,KAAK,IAAK;AAAA,QACvC,cAAc,KAAK,gBAAgB;AAAA,QACnC,eAAe,KAAK,uBAAuB,IAAI;AAAA,QAC/C,oBAAoB,KAAK,0BAA0B,IAAI;AAAA,QACvD,kBAAkB,KAAK,uBAAuB,IAAI;AAAA,QAClD,gBAAgB,KAAK,sBAAsB,IAAI;AAAA,QAC/C,cAAc,KAAK,mBAAmB,IAAI;AAAA,QAC1C,oBAAoB,KAAK,4BAA4B,IAAI;AAAA,QACzD,kBAAkB,KAAK,wBAAwB,IAAI;AAAA,QACnD,wBAAwB,KAAK,+BAA+B,IAAI;AAAA,MACxE;AAEM,WAAK,mBAAmB,SAAS,KAAK,eAAe;AAErD,UAAI,KAAK,WAAW;AAClB,gBAAQ,IAAI,uDAAuD;AAAA,UACjE,OAAO,gBAAgB;AAAA,UACvB,cAAc,gBAAgB;AAAA,UAC9B,WAAW,gBAAgB;AAAA,QACrC,CAAS;AAAA,MACF;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,cAAQ,MAAM,mCAAmC,KAAK;AACtD,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,sBAAsB,MAAM;AAC1B,QAAI;AACF,YAAM,kBAAkB;AAAA,QACtB,WAAW,KAAK,aAAa,KAAK,IAAK;AAAA,QACvC,cAAc,KAAK,gBAAgB;AAAA,QACnC,kBAAkB,KAAK,0BAA0B,IAAI;AAAA,QACrD,iBAAiB,KAAK,yBAAyB,IAAI;AAAA,QACnD,kBAAkB,KAAK,wBAAwB,IAAI;AAAA,QACnD,0BAA0B,KAAK,+BAA+B,IAAI;AAAA,QAClE,gBAAgB,KAAK,sBAAsB,IAAI;AAAA,QAC/C,aAAa,KAAK,kBAAkB,IAAI;AAAA,QACxC,kBAAkB,KAAK,yBAAyB,IAAI;AAAA,QACpD,mBAAmB,KAAK,yBAAyB,IAAI;AAAA,MAC7D;AAEM,WAAK,mBAAmB,eAAe,KAAK,eAAe;AAE3D,UAAI,KAAK,WAAW;AAClB,gBAAQ,IAAI,kDAAkD;AAAA,UAC5D,UAAU,gBAAgB;AAAA,UAC1B,YAAY,gBAAgB;AAAA,UAC5B,UAAU,gBAAgB;AAAA,QACpC,CAAS;AAAA,MACF;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,cAAQ,MAAM,wCAAwC,KAAK;AAC3D,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,qBAAqB,MAAM;AACzB,QAAI;AACF,YAAM,eAAe;AAAA,QACnB,WAAW,KAAK,aAAa,KAAK,IAAK;AAAA,QACvC,WAAW,KAAK,cAAc,IAAI;AAAA,QAClC,oBAAoB,KAAK,sBAAsB;AAAA,QAC/C,wBAAwB,KAAK,sBAAsB;AAAA,QACnD,mBAAmB,KAAK,qBAAqB;AAAA,QAC7C,eAAe,KAAK,oBAAoB,IAAI;AAAA,QAC5C,uBAAuB,KAAK,4BAA4B,IAAI;AAAA,QAC5D,uBAAuB,KAAK,4BAA4B,IAAI;AAAA,QAC5D,oBAAoB,KAAK,yBAAyB,IAAI;AAAA,QACtD,uBAAuB,KAAK,4BAA4B,IAAI;AAAA,MACpE;AAEM,WAAK,mBAAmB,cAAc,KAAK,YAAY;AAEvD,UAAI,KAAK,WAAW;AAClB,gBAAQ,IAAI,6DAA6D;AAAA,UACvE,WAAW,aAAa;AAAA,UACxB,mBAAmB,aAAa;AAAA,UAChC,UAAU,aAAa;AAAA,QACjC,CAAS;AAAA,MACF;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,cAAQ,MAAM,0CAA0C,KAAK;AAC7D,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,uBAAuB,MAAM;AAC3B,QAAI;AACF,YAAM,qBAAqB;AAAA,QACzB,WAAW,KAAK,aAAa,KAAK,IAAK;AAAA,QACvC,iBAAiB,KAAK,mBAAmB;AAAA,QACzC,eAAe,KAAK,iBAAiB;AAAA,QACrC,iBAAiB,KAAK,yBAAyB,IAAI;AAAA,QACnD,iBAAiB,KAAK,sBAAsB,IAAI;AAAA,QAChD,sBAAsB,KAAK,8BAA8B,IAAI;AAAA,QAC7D,kBAAkB,KAAK,uBAAuB,IAAI;AAAA,QAClD,qBAAqB,KAAK,0BAA0B,IAAI;AAAA,QACxD,YAAY,KAAK,oBAAoB,IAAI;AAAA,QACzC,aAAa,KAAK,mBAAmB,IAAI;AAAA,MACjD;AAEM,WAAK,mBAAmB,YAAY,KAAK,kBAAkB;AAE3D,UAAI,KAAK,WAAW;AAClB,gBAAQ,IAAI,uDAAuD;AAAA,UACjE,WAAW,mBAAmB;AAAA,UAC9B,YAAY,mBAAmB;AAAA,UAC/B,YAAY,mBAAmB;AAAA,QACzC,CAAS;AAAA,MACF;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,cAAQ,MAAM,mCAAmC,KAAK;AACtD,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAID,iBAAiB,MAAM;AACrB,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,OAAO,KAAK,aAAa;AAE/B,QAAI,MAAM,SAAS,EAAG,QAAO;AAC7B,QAAI,OAAO,OAAS,MAAM,SAAS,GAAI,QAAO;AAC9C,QAAI,MAAM,SAAS,GAAI,QAAO;AAC9B,WAAO;AAAA,EACR;AAAA,EAED,gBAAgB,MAAM;AACpB,UAAM,WAAW,KAAK,gBAAgB;AAEtC,QAAI,KAAK,oBAAoB,QAAQ,EAAG,QAAO;AAC/C,QAAI,KAAK,gBAAgB,QAAQ,EAAG,QAAO;AAC3C,QAAI,KAAK,mBAAmB,QAAQ,EAAG,QAAO;AAC9C,WAAO;AAAA,EACR;AAAA,EAED,oBAAoB,MAAM;AACxB,UAAM,eAAe,KAAK,gBAAgB;AAC1C,UAAM,QAAQ,KAAK,SAAS;AAE5B,QAAI,eAAe,OAAS,MAAM,SAAS,EAAG,QAAO;AACrD,QAAI,eAAe,IAAM,QAAO;AAChC,WAAO;AAAA,EACR;AAAA,EAED,+BAA+B,MAAM;AACnC,UAAM,UAAU,KAAK,WAAW;AAChC,UAAM,aAAa,KAAK,cAAc;AACtC,UAAM,OAAO,KAAK,aAAa;AAE/B,QAAI,WAAW,aAAa,OAAO,OAAO,IAAO,QAAO;AACxD,QAAI,WAAW,aAAa,IAAK,QAAO;AACxC,WAAO;AAAA,EACR;AAAA,EAED,4BAA4B,iBAAiB,MAAM;AACjD,UAAM,kBAAkB,KAAK,mBAAmB;AAChD,UAAM,kBAAkB,KAAK,mBAAmB;AAEhD,WAAO;AAAA,MACL,aAAa,kBAAkB,IAAI,SAAS,kBAAkB,IAAI,aAAa;AAAA,MAC/E,iBAAiB,kBAAkB,MAAO,SAAS;AAAA,MACnD,UAAU,oBAAoB,KAAK,KAAK,uBAAuB;AAAA,IACrE;AAAA,EACG;AAAA,EAED,0BAA0B,MAAM;AAC9B,UAAM,mBAAmB,KAAK,oBAAoB;AAClD,UAAM,iBAAiB,KAAK,kBAAkB;AAE9C,WAAO,mBAAmB;AAAA,EAC3B;AAAA,EAED,8BAA8B,MAAM;AAClC,UAAM,WAAW,KAAK,YAAY;AAClC,UAAM,YAAY,KAAK,yBAAyB;AAChD,UAAM,YAAY,KAAK,aAAa;AAEpC,QAAI,WAAW,KAAK,aAAa,CAAC,UAAW,QAAO;AACpD,QAAI,WAAW,KAAK,UAAW,QAAO;AACtC,WAAO;AAAA,EACR;AAAA;AAAA,EAID,0BAA0B;AACxB,QAAI;AACF,aAAO;AAAA,QACL,SAAS;AAAA,UACP,eAAe,KAAK,eAAe;AAAA,UACnC,gBAAgB,MAAM,KAAK,KAAK,eAAe,cAAc;AAAA,UAC7D,sBAAsB,KAAK,8BAA+B;AAAA,UAC1D,kBAAkB,KAAK,eAAe;AAAA,UACtC,kBAAkB,KAAK,eAAe;AAAA,UACtC,4BAA4B,KAAK,sBAAuB;AAAA,QACzD;AAAA,QACD,UAAU;AAAA,UACR,kBAAkB,KAAK,2BAA4B;AAAA,UACnD,kBAAkB,KAAK,sBAAuB;AAAA,UAC9C,wBAAwB,KAAK,4BAA6B;AAAA,UAC1D,uBAAuB,KAAK,2BAA4B;AAAA,UACxD,qBAAqB,KAAK,2BAA4B;AAAA,UACtD,kBAAkB,KAAK,yBAA0B;AAAA,QAClD;AAAA,QACD,iBAAiB,KAAK,sCAAuC;AAAA,QAC7D,WAAW,KAAK,IAAK;AAAA,MAC7B;AAAA,IACK,SAAQ,OAAO;AACd,cAAQ,MAAM,sDAAsD,KAAK;AACzE,aAAO,EAAE,OAAO;IACjB;AAAA,EACF;AAAA,EAED,wCAAwC;AACtC,UAAM,kBAAkB,CAAA;AACxB,UAAM,gBAAgB,KAAK;AAC3B,UAAM,mBAAmB,KAAK,eAAe;AAE7C,QAAI,gBAAgB,KAAK;AACvB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,MAClB,CAAO;AAAA,IACF;AAED,QAAI,mBAAmB,KAAK;AAC1B,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,MAClB,CAAO;AAAA,IACF;AAED,QAAI,KAAK,eAAe,mBAAmB,KAAK;AAC9C,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,MAClB,CAAO;AAAA,IACF;AAED,WAAO;AAAA,EACR;AAAA;AAAA,EAID,sBAAsB,SAAS;AAC7B,SAAK,eAAe;AACpB,SAAK,eAAe,eAAe,IAAI,QAAQ,YAAY;AAE3D,QAAI,QAAQ,kBAAkB,QAAQ;AACpC,WAAK,eAAe,iBAAiB;AAAA,IAC3C,WAAe,QAAQ,kBAAkB,YAAY;AAC/C,WAAK,eAAe,iBAAiB;AAAA,IACtC;AAAA,EACF;AAAA,EAED,gCAAgC;AAC9B,WAAO,KAAK,eAAe,gBAAgB,IACzC,KAAK,eAAe,gBAAgB,KAAK,eAAe,gBAAgB;AAAA,EAC3E;AAAA,EAED,YAAY;AACV,SAAK,qBAAqB;AAAA,MACxB,YAAY,CAAE;AAAA,MACd,gBAAgB,CAAE;AAAA,MAClB,UAAU,CAAE;AAAA,MACZ,eAAe,CAAE;AAAA,MACjB,aAAa,CAAE;AAAA,MACf,eAAe,CAAE;AAAA,IACvB;AAEI,SAAK,iBAAiB;AAAA,MACpB,eAAe;AAAA,MACf,gBAAgB,oBAAI,IAAK;AAAA,MACzB,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,IACzB;AAEI,QAAI,KAAK,WAAW;AAClB,cAAQ,IAAI,2CAA2C;AAAA,IACxD;AAAA,EACF;AAAA;AAAA,EAGD,2BAA2B;AAAE,WAAO,KAAK,OAAQ,IAAG;AAAA,EAAM;AAAA,EAC1D,sBAAsB;AAAE,WAAO,KAAK,OAAQ,IAAG;AAAA,EAAM;AAAA,EACrD,sBAAsB;AAAE,WAAO,EAAE,WAAW,YAAa;AAAA,EAAG;AAAA,EAC5D,2BAA2B;AAAE,WAAO,EAAE,SAAS,KAAK,OAAQ,IAAG,IAAG;AAAA,EAAK;AAAA,EACvE,uBAAuB;AAAE,WAAO,EAAE,WAAW,WAAY;AAAA,EAAG;AAAA,EAC5D,sBAAsB;AAAE,WAAO;AAAA,EAAa;AAAA,EAC5C,yBAAyB;AAAE,WAAO;AAAA,EAAa;AAAA,EAC/C,4BAA4B;AAAE,WAAO,EAAE,YAAY,KAAM;AAAA,EAAG;AAAA,EAC5D,yBAAyB;AAAE,WAAO;AAAA,EAAe;AAAA,EACjD,wBAAwB;AAAE,WAAO,EAAE,WAAW,KAAM;AAAA,EAAG;AAAA,EACvD,qBAAqB;AAAE,WAAO;AAAA,EAAS;AAAA,EACvC,8BAA8B;AAAE,WAAO;AAAA,EAAM;AAAA,EAC7C,0BAA0B;AAAE,WAAO,EAAE,UAAU,MAAO;AAAA,EAAG;AAAA,EACzD,iCAAiC;AAAE,WAAO;AAAA,EAAa;AAAA,EACvD,2BAA2B;AAAE,WAAO;AAAA,EAAc;AAAA,EAClD,0BAA0B;AAAE,WAAO,EAAE,WAAW,KAAM;AAAA,EAAG;AAAA,EACzD,iCAAiC;AAAE,WAAO;AAAA,EAAY;AAAA,EACtD,wBAAwB;AAAE,WAAO,EAAE,OAAO,KAAM;AAAA,EAAG;AAAA,EACnD,oBAAoB;AAAE,WAAO;AAAA,EAAe;AAAA,EAC5C,2BAA2B;AAAE,WAAO;AAAA,EAAe;AAAA,EACnD,2BAA2B;AAAE,WAAO,EAAE,QAAQ,KAAM;AAAA,EAAG;AAAA,EACvD,gBAAgB;AAAE,WAAO;AAAA,EAAoB;AAAA,EAC7C,sBAAsB;AAAE,WAAO;AAAA,EAAmB;AAAA,EAClD,8BAA8B;AAAE,WAAO;AAAA,EAAiB;AAAA,EACxD,8BAA8B;AAAE,WAAO;AAAA,EAAa;AAAA,EACpD,2BAA2B;AAAE,WAAO,EAAE,SAAS,KAAM;AAAA,EAAG;AAAA,EACxD,8BAA8B;AAAE,WAAO;AAAA,EAAU;AAAA,EACjD,2BAA2B;AAAE,WAAO;AAAA,EAAS;AAAA,EAC7C,wBAAwB;AAAE,WAAO;AAAA,EAAS;AAAA,EAC1C,yBAAyB;AAAE,WAAO;AAAA,EAAc;AAAA,EAChD,4BAA4B;AAAE,WAAO;AAAA,EAAY;AAAA,EACjD,sBAAsB;AAAE,WAAO;AAAA,EAAc;AAAA,EAC7C,qBAAqB;AAAE,WAAO,EAAE,WAAW,KAAM;AAAA,EAAG;AAAA,EACpD,sBAAsB;AAAE,WAAO,KAAK,OAAQ,IAAG;AAAA,EAAM;AAAA,EACrD,kBAAkB;AAAE,WAAO,KAAK,OAAQ,IAAG;AAAA,EAAM;AAAA,EACjD,qBAAqB;AAAE,WAAO,KAAK,OAAQ,IAAG;AAAA,EAAM;AAAA,EACpD,6BAA6B;AAAE,WAAO,EAAE,WAAW,aAAc;AAAA,EAAG;AAAA,EACpE,wBAAwB;AAAE,WAAO,EAAE,OAAO,aAAc;AAAA,EAAG;AAAA,EAC3D,8BAA8B;AAAE,WAAO,EAAE,SAAS,OAAQ;AAAA,EAAG;AAAA,EAC7D,6BAA6B;AAAE,WAAO,EAAE,UAAU,YAAa;AAAA,EAAG;AAAA,EAClE,6BAA6B;AAAE,WAAO,EAAE,SAAS,aAAc;AAAA,EAAG;AAAA,EAClE,2BAA2B;AAAE,WAAO,KAAK;AAAA,EAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO7D,QAAQ,MAAM;AACZ,WAAO,KAAK,QAAQ,IAAI;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,QAAQ,UAAU;AAChB,QAAI;AACF,UAAI,CAAC,UAAU;AACb,gBAAQ,KAAK,iDAAiD;AAC9D,eAAO,KAAK;MACb;AAGD,YAAM,eAAe,SAAS,gBAAgB;AAC9C,YAAM,SAAS,SAAS,UAAU;AAClC,YAAM,iBAAiB,SAAS,kBAAkB;AAClD,YAAM,WAAW,SAAS,YAAY;AAGtC,YAAM,mBAAmB,KAAK,kBAAkB,cAAc,cAAc;AAC5E,YAAM,mBAAmB,KAAK,gBAAgB,cAAc,QAAQ;AACpE,YAAM,sBAAsB,KAAK,mBAAmB,cAAc,MAAM;AACxE,YAAM,sBAAsB,KAAK,mBAAmB,cAAc,QAAQ,cAAc;AAGxF,YAAM,yBAAyB;AAAA,QAC7B,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,sBAAsB;AAAA,QACtB,aAAa;AAAA,QACb,4BAA4B,KAAK,sBAAsB;AAAA,UACrD,iBAAiB;AAAA,UACjB,iBAAiB;AAAA,UACjB,oBAAoB;AAAA,UACpB,oBAAoB;AAAA,QAC9B,CAAS;AAAA,QACD,WAAW,KAAK,IAAK;AAAA,MAC7B;AAEM,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,cAAQ,MAAM,mDAAmD,KAAK;AACtE,aAAO,KAAK;IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,oBAAoB;AAClB,WAAO;AAAA,MACL,YAAY,EAAE,OAAO,KAAK,MAAM,QAAS;AAAA,MACzC,UAAU,EAAE,OAAO,KAAK,OAAO,UAAW;AAAA,MAC1C,sBAAsB,EAAE,OAAO,KAAK,OAAO,UAAW;AAAA,MACtD,aAAa,EAAE,OAAO,KAAK,OAAO,UAAW;AAAA,MAC7C,4BAA4B;AAAA,MAC5B,WAAW,KAAK,IAAK;AAAA,IAC3B;AAAA,EACG;AAAA;AAAA;AAAA;AAAA,EAKD,sBAAsB,QAAQ;AAC5B,QAAI,CAAC,UAAU,CAAC,OAAO,OAAQ,QAAO;AACtC,WAAO,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC,IAAI,OAAO;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAKD,kBAAkB,cAAc,gBAAgB;AAE9C,WAAO;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,QACP,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,cAAc;AAAA,MACf;AAAA,IACP;AAAA,EACG;AAAA;AAAA;AAAA;AAAA,EAKD,gBAAgB,cAAc,UAAU;AAEtC,WAAO;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,QACP,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,WAAW;AAAA,MACZ;AAAA,IACP;AAAA,EACG;AAAA;AAAA;AAAA;AAAA,EAKD,mBAAmB,cAAc,QAAQ;AAEvC,WAAO;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,QACP,kBAAkB;AAAA,QAClB,cAAc;AAAA,QACd,eAAe;AAAA,MAChB;AAAA,IACP;AAAA,EACG;AAAA;AAAA;AAAA;AAAA,EAKD,mBAAmB,cAAc,QAAQ,gBAAgB;AAEvD,WAAO;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,QACP,sBAAsB;AAAA,QACtB,iBAAiB;AAAA,QACjB,YAAY;AAAA,MACb;AAAA,IACP;AAAA,EACG;AACH;AC7kBO,MAAM,uBAAuB;AAAA,EAClC,cAAc;AACZ,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,cAAc;AAAA,EACpB;AAAA,EAED,MAAM,QAAQ,UAAU;AACtB,QAAI;AACF,YAAM,UAAU,SAAS,WAAW;AACpC,YAAM,eAAe,SAAS,gBAAgB;AAE9C,YAAM,qBAAqB,KAAK,4BAA4B,OAAO;AACnE,YAAM,qBAAqB,KAAK,yBAAyB,OAAO;AAChE,YAAM,qBAAqB,KAAK,yBAAyB,YAAY;AAErE,aAAO;AAAA,QACL,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA,UAAU,CAAC,wCAAwC;AAAA,QACnD,iBAAiB,qBAAqB,MAClC,CAAC,qCAAqC,IACtC,CAAC,6BAA6B;AAAA,MAC1C;AAAA,IACK,SAAQ,OAAO;AACd,cAAQ,MAAM,uCAAuC,KAAK;AAC1D,aAAO,EAAE,OAAO,KAAK,OAAO,MAAM,QAAO;AAAA,IAC1C;AAAA,EACF;AAAA,EAED,4BAA4B,SAAS;AACnC,UAAM,iBAAiB,QAAQ,kBAAkB;AACjD,UAAM,kBAAkB,QAAQ,mBAAmB;AACnD,UAAM,kBAAkB,KAAK,yBAAyB,OAAO;AAC7D,YAAQ,iBAAiB,kBAAkB,mBAAmB;AAAA,EAC/D;AAAA,EAED,yBAAyB,SAAS;AAChC,UAAM,cAAc,QAAQ,uBAAuB;AACnD,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,MAAO,eAAe,GAAI,CAAC;AAAA,EAC5D;AAAA,EAED,yBAAyB,SAAS;AAChC,WAAO,QAAQ,2BAA2B;AAAA,EAC3C;AAAA,EAED,yBAAyB,cAAc;AACrC,QAAI,aAAa,WAAW,EAAG,QAAO;AAEtC,UAAM,sBAAsB,aAAa,OAAO,OAAK,EAAE,kBAAkB,EAAE;AAC3E,WAAO,sBAAsB,aAAa;AAAA,EAC3C;AACH;ACnDO,MAAM,6BAA6B,cAAc;AAAA,EACtD,cAAc;AACZ,UAAM,aAAa;AAEnB,SAAK,eAAe;AAAA;AAAA,MAElB,oBAAoB,CAAE;AAAA,MACtB,oBAAoB,CAAE;AAAA,MACtB,eAAe,CAAE;AAAA,MACjB,gBAAgB,CAAE;AAAA;AAAA,MAGlB,oBAAoB,CAAE;AAAA,MACtB,cAAc,CAAE;AAAA,MAChB,iBAAiB,CAAE;AAAA,MACnB,qBAAqB,CAAE;AAAA,IAC7B;AAEI,SAAK,cAAc;AAAA,MACjB,WAAW;AAAA,MACX,SAAS;AAAA,MACT,yBAAyB;AAAA,MACzB,sBAAsB;AAAA,MACtB,kBAAkB;AAAA,MAClB,qBAAqB;AAAA,MACrB,sBAAsB;AAAA,MACtB,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,kBAAkB;AAAA,IACxB;AAAA,EACG;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,QAAQ,UAAU;AACtB,QAAI;AAEF,YAAM,YAAY;AAAA,QAChB,WAAW,KAAK,iBAAiB,QAAQ;AAAA,QACzC,cAAc,KAAK,oBAAoB,QAAQ;AAAA,QAC/C,SAAS,KAAK,eAAe,QAAQ;AAAA,QACrC,YAAY,KAAK,yBAAyB,QAAQ;AAAA,MAC1D;AAEM,aAAO;AAAA,QACL,WAAW,KAAK,IAAK;AAAA,QACrB,UAAU;AAAA,QACV;AAAA,QACA,OAAO,KAAK,sBAAsB,SAAS;AAAA,MACnD;AAAA,IACK,SAAQ,OAAO;AACd,cAAQ,MAAM,oCAAoC,KAAK;AACvD,aAAO;AAAA,QACL,WAAW,KAAK,IAAK;AAAA,QACrB,UAAU;AAAA,QACV,OAAO,MAAM;AAAA,QACb,OAAO;AAAA,MACf;AAAA,IACK;AAAA,EACF;AAAA,EAED,iBAAiB,UAAU;AACzB,WAAO;AAAA,MACL,eAAe;AAAA,MACf,oBAAoB;AAAA,MACpB,eAAe;AAAA,MACf,WAAW;AAAA,IACjB;AAAA,EACG;AAAA,EAED,oBAAoB,UAAU;AAC5B,WAAO;AAAA,MACL,qBAAqB;AAAA,MACrB,sBAAsB;AAAA,MACtB,qBAAqB;AAAA,MACrB,sBAAsB;AAAA,IAC5B;AAAA,EACG;AAAA,EAED,eAAe,UAAU;AACvB,WAAO;AAAA,MACL,oBAAoB;AAAA,MACpB,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,IAC1B;AAAA,EACG;AAAA,EAED,yBAAyB,UAAU;AACjC,WAAO;AAAA,EACR;AAAA,EAED,sBAAsB,WAAW;AAC/B,UAAM,SAAS;AAAA,MACb,UAAU,UAAU;AAAA,MACpB,UAAU,aAAa;AAAA,MACvB,UAAU,QAAQ;AAAA,MAClB,UAAU;AAAA,IAChB;AAEI,WAAO,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC,IAAI,OAAO;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAMD,iBAAiB;AACf,WAAO;AAAA,MACL,WAAW,KAAK,aAAa;AAAA,MAC7B,cAAc,KAAK,aAAa;AAAA,MAChC,SAAS,KAAK,aAAa;AAAA,MAC3B,YAAY,KAAK,YAAY;AAAA,MAC7B,iBAAiB,KAAK,wBAAyB;AAAA,IACrD;AAAA,EACG;AAAA,EAED,0BAA0B;AACxB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACN;AAAA,EACG;AAAA,EAED,mBAAmB;AACjB,WAAO,KAAK,MAAM,KAAK,YAAY,kBAAkB,GAAI;AAAA,EAC1D;AACH;AClIO,MAAM,oCAAoC,cAAc;AAAA,EAC7D,cAAc;AACZ,UAAM,oBAAoB;AAE1B,SAAK,iBAAiB;AAAA;AAAA,MAEpB,eAAe,CAAE;AAAA,MACjB,eAAe,CAAE;AAAA,MACjB,iBAAiB,CAAE;AAAA,MACnB,cAAc,CAAE;AAAA;AAAA,MAGhB,kBAAkB,CAAE;AAAA,MACpB,qBAAqB,CAAE;AAAA,MACvB,iBAAiB,CAAE;AAAA,MACnB,kBAAkB,CAAE;AAAA,IAC1B;AAEI,SAAK,cAAc;AAAA,MACjB,WAAW;AAAA,MACX,SAAS;AAAA,MACT,0BAA0B;AAAA,MAC1B,qBAAqB;AAAA,MACrB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,0BAA0B;AAAA,MAC1B,qBAAqB;AAAA,MACrB,oBAAoB;AAAA,MACpB,uBAAuB;AAAA,IAC7B;AAAA,EACG;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,QAAQ,UAAU;AACtB,QAAI;AAEF,YAAM,cAAc;AAAA,QAClB,kBAAkB,KAAK,wBAAwB,QAAQ;AAAA,QACvD,kBAAkB,KAAK,wBAAwB,QAAQ;AAAA,QACvD,iBAAiB,KAAK,uBAAuB,QAAQ;AAAA,QACrD,oBAAoB,KAAK,4BAA4B,QAAQ;AAAA,MACrE;AAEM,aAAO;AAAA,QACL,WAAW,KAAK,IAAK;AAAA,QACrB,UAAU;AAAA,QACV;AAAA,QACA,OAAO,KAAK,sBAAsB,WAAW;AAAA,MACrD;AAAA,IACK,SAAQ,OAAO;AACd,cAAQ,MAAM,yDAAyD,KAAK;AAC5E,aAAO;AAAA,QACL,WAAW,KAAK,IAAK;AAAA,QACrB,UAAU;AAAA,QACV,OAAO,MAAM;AAAA,QACb,OAAO;AAAA,MACf;AAAA,IACK;AAAA,EACF;AAAA,EAED,wBAAwB,UAAU;AAChC,WAAO;AAAA,MACL,eAAe;AAAA,MACf,qBAAqB;AAAA,MACrB,eAAe;AAAA,MACf,aAAa;AAAA,IACnB;AAAA,EACG;AAAA,EAED,wBAAwB,UAAU;AAChC,WAAO;AAAA,MACL,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,qBAAqB;AAAA,IAC3B;AAAA,EACG;AAAA,EAED,uBAAuB,UAAU;AAC/B,WAAO;AAAA,MACL,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,IACvB;AAAA,EACG;AAAA,EAED,4BAA4B,UAAU;AACpC,WAAO;AAAA,EACR;AAAA,EAED,sBAAsB,aAAa;AACjC,UAAM,SAAS;AAAA,MACb,YAAY,iBAAiB;AAAA,MAC7B,YAAY,iBAAiB;AAAA,MAC7B,YAAY,gBAAgB;AAAA,MAC5B,YAAY;AAAA,IAClB;AAEI,WAAO,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC,IAAI,OAAO;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAMD,iBAAiB;AACf,WAAO;AAAA,MACL,eAAe,KAAK,eAAe;AAAA,MACnC,eAAe,KAAK,eAAe;AAAA,MACnC,oBAAoB,KAAK,YAAY;AAAA,MACrC,qBAAqB,KAAK,YAAY;AAAA,MACtC,iBAAiB,KAAK,wBAAyB;AAAA,IACrD;AAAA,EACG;AAAA,EAED,0BAA0B;AACxB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACN;AAAA,EACG;AAAA,EAED,mBAAmB;AACjB,WAAO,KAAK,MAAM,KAAK,YAAY,sBAAsB,GAAI;AAAA,EAC9D;AACH;AClIO,MAAM,wBAAwB,cAAc;AAAA,EACjD,cAAc;AACZ,UAAM,QAAQ;AAEd,SAAK,gBAAgB;AAAA;AAAA,MAEnB,cAAc,CAAE;AAAA,MAChB,aAAa,CAAE;AAAA,MACf,mBAAmB,CAAE;AAAA,MACrB,iBAAiB,CAAE;AAAA;AAAA,MAGnB,eAAe,CAAE;AAAA,MACjB,gBAAgB,CAAE;AAAA,MAClB,sBAAsB,CAAE;AAAA,MACxB,gBAAgB,CAAE;AAAA;AAAA,MAGlB,eAAe,CAAE;AAAA,MACjB,kBAAkB,CAAE;AAAA,MACpB,yBAAyB,CAAE;AAAA,MAC3B,gBAAgB,CAAE;AAAA;AAAA,MAGlB,gBAAgB,CAAE;AAAA,MAClB,gBAAgB,CAAE;AAAA,MAClB,iBAAiB,CAAE;AAAA,MACnB,kBAAkB,CAAE;AAAA,IAC1B;AAEI,SAAK,cAAc;AAAA,MACjB,WAAW;AAAA,MACX,SAAS;AAAA,MACT,uBAAuB;AAAA,MACvB,mBAAmB;AAAA,MACnB,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,gBAAgB;AAAA,IACtB;AAAA,EACG;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,QAAQ,UAAU;AACtB,QAAI;AAEF,YAAM,aAAa;AAAA,QACjB,cAAc,KAAK,oBAAoB,QAAQ;AAAA,QAC/C,eAAe,KAAK,qBAAqB,QAAQ;AAAA,QACjD,eAAe,KAAK,qBAAqB,QAAQ;AAAA,QACjD,kBAAkB,KAAK,0BAA0B,QAAQ;AAAA,MACjE;AAEM,aAAO;AAAA,QACL,WAAW,KAAK,IAAK;AAAA,QACrB,UAAU;AAAA,QACV;AAAA,QACA,OAAO,KAAK,sBAAsB,UAAU;AAAA,MACpD;AAAA,IACK,SAAQ,OAAO;AACd,cAAQ,MAAM,uCAAuC,KAAK;AAC1D,aAAO;AAAA,QACL,WAAW,KAAK,IAAK;AAAA,QACrB,UAAU;AAAA,QACV,OAAO,MAAM;AAAA,QACb,OAAO;AAAA,MACf;AAAA,IACK;AAAA,EACF;AAAA,EAED,oBAAoB,UAAU;AAC5B,WAAO;AAAA,MACL,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,eAAe;AAAA,IACrB;AAAA,EACG;AAAA,EAED,qBAAqB,UAAU;AAC7B,WAAO;AAAA,MACL,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACnB;AAAA,EACG;AAAA,EAED,qBAAqB,UAAU;AAC7B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IAChB;AAAA,EACG;AAAA,EAED,0BAA0B,UAAU;AAClC,WAAO;AAAA,EACR;AAAA,EAED,sBAAsB,YAAY;AAChC,UAAM,SAAS;AAAA,MACb,WAAW,aAAa;AAAA,MACxB,WAAW,cAAc;AAAA,MACzB,WAAW,cAAc;AAAA,MACzB,WAAW;AAAA,IACjB;AAEI,WAAO,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC,IAAI,OAAO;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAMD,iBAAiB;AACf,WAAO;AAAA,MACL,cAAc,KAAK,cAAc;AAAA,MACjC,eAAe,KAAK,cAAc;AAAA,MAClC,eAAe,KAAK,cAAc;AAAA,MAClC,YAAY,KAAK,YAAY;AAAA,MAC7B,iBAAiB,KAAK,wBAAyB;AAAA,IACrD;AAAA,EACG;AAAA,EAED,0BAA0B;AACxB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACN;AAAA,EACG;AAAA,EAED,mBAAmB;AACjB,WAAO,KAAK,MAAM,KAAK,YAAY,mBAAmB,GAAI;AAAA,EAC3D;AACH;AC9IO,MAAM,sCAAsC,cAAc;AAAA,EAC/D,cAAc;AACZ,UAAM,sBAAsB;AAE5B,SAAK,oBAAoB;AAAA;AAAA,MAEvB,cAAc,CAAE;AAAA,MAChB,qBAAqB,CAAE;AAAA,MACvB,qBAAqB,CAAE;AAAA,MACvB,iBAAiB,CAAE;AAAA;AAAA,MAGnB,iBAAiB,CAAE;AAAA,MACnB,mBAAmB,CAAE;AAAA,MACrB,wBAAwB,CAAE;AAAA,MAC1B,eAAe,CAAE;AAAA,IACvB;AAEI,SAAK,cAAc;AAAA,MACjB,WAAW;AAAA,MACX,SAAS;AAAA,MACT,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IACtB;AAAA,EACG;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,QAAQ,UAAU;AACtB,QAAI;AAEF,YAAM,iBAAiB;AAAA,QACrB,kBAAkB,KAAK,wBAAwB,QAAQ;AAAA,QACvD,mBAAmB,KAAK,yBAAyB,QAAQ;AAAA,QACzD,uBAAuB,KAAK,6BAA6B,QAAQ;AAAA,QACjE,sBAAsB,KAAK,8BAA8B,QAAQ;AAAA,MACzE;AAEM,aAAO;AAAA,QACL,WAAW,KAAK,IAAK;AAAA,QACrB,UAAU;AAAA,QACV;AAAA,QACA,OAAO,KAAK,sBAAsB,cAAc;AAAA,MACxD;AAAA,IACK,SAAQ,OAAO;AACd,cAAQ,MAAM,wDAAwD,KAAK;AAC3E,aAAO;AAAA,QACL,WAAW,KAAK,IAAK;AAAA,QACrB,UAAU;AAAA,QACV,OAAO,MAAM;AAAA,QACb,OAAO;AAAA,MACf;AAAA,IACK;AAAA,EACF;AAAA,EAED,wBAAwB,UAAU;AAChC,WAAO;AAAA,MACL,cAAc;AAAA,MACd,qBAAqB;AAAA,MACrB,qBAAqB;AAAA,MACrB,iBAAiB;AAAA,IACvB;AAAA,EACG;AAAA,EAED,yBAAyB,UAAU;AACjC,WAAO;AAAA,MACL,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,wBAAwB;AAAA,MACxB,eAAe;AAAA,IACrB;AAAA,EACG;AAAA,EAED,6BAA6B,UAAU;AACrC,WAAO;AAAA,MACL,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,MACnB,sBAAsB;AAAA,MACtB,mBAAmB;AAAA,IACzB;AAAA,EACG;AAAA,EAED,8BAA8B,UAAU;AACtC,WAAO;AAAA,EACR;AAAA,EAED,sBAAsB,gBAAgB;AACpC,UAAM,SAAS;AAAA,MACb,eAAe,iBAAiB;AAAA,MAChC,eAAe,kBAAkB;AAAA,MACjC,eAAe,sBAAsB;AAAA,MACrC,eAAe;AAAA,IACrB;AAEI,WAAO,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC,IAAI,OAAO;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAMD,iBAAiB;AACf,WAAO;AAAA,MACL,cAAc,KAAK,kBAAkB;AAAA,MACrC,mBAAmB,KAAK,kBAAkB;AAAA,MAC1C,sBAAsB,KAAK,YAAY;AAAA,MACvC,kBAAkB,KAAK,YAAY;AAAA,MACnC,iBAAiB,KAAK,wBAAyB;AAAA,IACrD;AAAA,EACG;AAAA,EAED,0BAA0B;AACxB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACN;AAAA,EACG;AAAA,EAED,mBAAmB;AACjB,WAAO,KAAK,MAAM,KAAK,YAAY,mBAAmB,GAAI;AAAA,EAC3D;AACH;AC1HO,MAAM,0BAA0B;AAAA,EACrC,cAAc;AACZ,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,WAAW;AAGhB,SAAK,cAAc;AAAA,MACjB,kBAAkB,IAAI,0BAA2B;AAAA,MACjD,gBAAgB,IAAI,wBAAyB;AAAA,MAC7C,eAAe,IAAI,uBAAwB;AAAA,MAC3C,aAAa,IAAI,qBAAsB;AAAA,MACvC,oBAAoB,IAAI,4BAA6B;AAAA,MACrD,QAAQ,IAAI,gBAAiB;AAAA,MAC7B,sBAAsB,IAAI,8BAA+B;AAAA,IAC/D;AAEI,SAAK,cAAc;AACnB,SAAK,gBAAgB,oBAAI;AACzB,SAAK,gBAAgB;AAErB,YAAQ,IAAI,kDAAkD;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAKD,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACb;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,oBAAoB,UAAU;AAClC,QAAI;AACF,YAAM,YAAY,KAAK;AACvB,YAAM,YAAY,SAAS,aAAa,WAAW,SAAS;AAG5D,YAAM,kBAAkB,MAAM,KAAK,WAAW,iBAAiB,QAAQ,QAAQ;AAC/E,YAAM,yBAAyB,MAAM,KAAK,WAAW,eAAe,QAAQ,QAAQ;AACpF,YAAM,wBAAwB,MAAM,KAAK,WAAW,cAAc,QAAQ,QAAQ;AAClF,YAAM,sBAAsB,MAAM,KAAK,WAAW,YAAY,QAAQ,QAAQ;AAC9E,YAAM,6BAA6B,MAAM,KAAK,WAAW,mBAAmB,QAAQ,QAAQ;AAC5F,YAAM,iBAAiB,MAAM,KAAK,WAAW,OAAO,QAAQ,QAAQ;AACpE,YAAM,+BAA+B,MAAM,KAAK,WAAW,qBAAqB,QAAQ,QAAQ;AAEhG,YAAM,mBAAmB;AAAA,QACvB;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,aAAa;AAAA,QACb,oBAAoB;AAAA,QACpB,QAAQ;AAAA,QACR,sBAAsB;AAAA,QACtB,oBAAoB,KAAK,4BAA4B;AAAA,UACnD;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACV,CAAS;AAAA,QACD,UAAU,KAAK,iBAAiB;AAAA,UAC9B;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACV,CAAS;AAAA,QACD,iBAAiB,KAAK,wBAAwB;AAAA,UAC5C;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACV,CAAS;AAAA,MACT;AAEM,WAAK,YAAY,KAAK,gBAAgB;AACtC,aAAO;AAAA,IAER,SAAQ,OAAO;AACd,cAAQ,MAAM,gDAAgD,KAAK;AACnE,aAAO;AAAA,QACL,OAAO,MAAM;AAAA,QACb,WAAW,KAAK,IAAK;AAAA,QACrB,UAAU;AAAA,MAClB;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,4BAA4B,UAAU;AACpC,QAAI;AACF,YAAM,SAAS,OAAO,OAAO,QAAQ,EAAE,IAAI,cAAY,SAAS,SAAS,GAAG;AAC5E,YAAM,eAAe,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC,IAAI,OAAO;AAE5E,aAAO;AAAA,QACL,OAAO;AAAA,QACP,OAAO,eAAe,MAAM,aAAa,eAAe,MAAM,kBAAkB;AAAA,QAChF,WAAW,KAAK,kBAAkB,QAAQ;AAAA,QAC1C,YAAY,KAAK,mBAAmB,QAAQ;AAAA,MACpD;AAAA,IACK,SAAQ,OAAO;AACd,cAAQ,MAAM,yCAAyC,KAAK;AAC5D,aAAO,EAAE,OAAO,KAAK,OAAO,UAAU,WAAW,CAAE,GAAE,YAAY,CAAA;IAClE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,kBAAkB,UAAU;AAC1B,UAAM,YAAY,CAAA;AAClB,QAAI,SAAS,iBAAiB,QAAQ,IAAK,WAAU,KAAK,2BAA2B;AACrF,QAAI,SAAS,wBAAwB,QAAQ,IAAK,WAAU,KAAK,4BAA4B;AAC7F,QAAI,SAAS,uBAAuB,QAAQ,IAAK,WAAU,KAAK,yCAAyC;AACzG,QAAI,SAAS,qBAAqB,QAAQ,IAAK,WAAU,KAAK,yCAAyC;AACvG,QAAI,SAAS,4BAA4B,QAAQ,IAAK,WAAU,KAAK,qCAAqC;AAC1G,QAAI,SAAS,gBAAgB,QAAQ,IAAK,WAAU,KAAK,kCAAkC;AAC3F,QAAI,SAAS,8BAA8B,QAAQ,IAAK,WAAU,KAAK,mCAAmC;AAC1G,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKD,mBAAmB,UAAU;AAC3B,UAAM,aAAa,CAAA;AACnB,QAAI,SAAS,iBAAiB,QAAQ,IAAK,YAAW,KAAK,gDAAgD;AAC3G,QAAI,SAAS,wBAAwB,QAAQ,IAAK,YAAW,KAAK,uCAAuC;AACzG,QAAI,SAAS,uBAAuB,QAAQ,IAAK,YAAW,KAAK,kDAAkD;AACnH,QAAI,SAAS,qBAAqB,QAAQ,IAAK,YAAW,KAAK,6CAA6C;AAC5G,QAAI,SAAS,4BAA4B,QAAQ,IAAK,YAAW,KAAK,mDAAmD;AACzH,QAAI,SAAS,gBAAgB,QAAQ,IAAK,YAAW,KAAK,kDAAkD;AAC5G,QAAI,SAAS,8BAA8B,QAAQ,IAAK,YAAW,KAAK,kDAAkD;AAC1H,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKD,iBAAiB,UAAU;AACzB,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACN;AAEI,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKD,wBAAwB,UAAU;AAChC,UAAM,kBAAkB,CAAA;AAExB,QAAI,SAAS,iBAAiB,QAAQ,KAAK;AACzC,sBAAgB,KAAK,mDAAmD;AAAA,IACzE;AAED,QAAI,SAAS,wBAAwB,QAAQ,KAAK;AAChD,sBAAgB,KAAK,0CAA0C;AAAA,IAChE;AAED,QAAI,SAAS,qBAAqB,QAAQ,KAAK;AAC7C,sBAAgB,KAAK,+CAA+C;AAAA,IACrE;AAED,QAAI,SAAS,4BAA4B,QAAQ,KAAK;AACpD,sBAAgB,KAAK,6CAA6C;AAAA,IACnE;AAED,QAAI,SAAS,gBAAgB,QAAQ,KAAK;AACxC,sBAAgB,KAAK,qDAAqD;AAAA,IAC3E;AAED,QAAI,SAAS,8BAA8B,QAAQ,KAAK;AACtD,sBAAgB,KAAK,6DAA6D;AAAA,IACnF;AAED,oBAAgB,KAAK,sDAAsD;AAE3E,WAAO;AAAA,EACR;AACH;ACvLO,MAAM,+BAA+B,eAAe;AAAA,EACzD,YAAY,SAAS,IAAI;AAEvB,UAAM,gBAAgB;AAAA,MACpB,UAAU;AAAA,MACV,kBAAkB,CAAC,qBAAqB,mBAAmB,mBAAmB;AAAA,MAC9E,gBAAgB,CAAC,sBAAsB,sBAAsB,gBAAgB;AAAA,MAC7E,YAAY;AAAA,QACV,UAAU;AAAA,QACV,cAAc;AAAA,QACd,YAAY;AAAA,MACb;AAAA,MACD,GAAG;AAAA,IACJ;AAED,UAAM,aAAa;AAGnB,SAAK,SAAS,OAAO,UAAU,OAAO,OAAO,OAAO,gBAAgB,aAChE,OAAO,SACP,KAAK,UAAU;AAAA,MACb,MAAM,IAAI,SAAS,QAAQ,KAAK,uBAAsB,oBAAI,KAAM,GAAC,YAAa,GAAE,GAAG,IAAI;AAAA,MACvF,OAAO,IAAI,SAAS,QAAQ,MAAM,sBAAqB,oBAAI,KAAM,GAAC,YAAa,GAAE,GAAG,IAAI;AAAA,MACxF,MAAM,IAAI,SAAS,QAAQ,KAAK,qBAAoB,oBAAI,KAAM,GAAC,YAAa,GAAE,GAAG,IAAI;AAAA,MACrF,OAAO,IAAI,SAAS,QAAQ,MAAM,qBAAoB,oBAAI,KAAM,GAAC,YAAa,GAAE,GAAG,IAAI;AAAA,MACvF,aAAa,IAAI,SAAS,QAAQ,KAAK,4BAA2B,oBAAI,KAAM,GAAC,YAAa,GAAE,GAAG,IAAI;AAAA,IAC7G;AAEI,SAAK,OAAO,KAAK,8CAA8C;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,MAAM,2BAA2B,UAAU,aAAa;AACtD,QAAI;AACF,WAAK,QAAQ,KAAK,2CAA2C;AAAA,QAC3D,WAAW,YAAY;AAAA,MAC/B,CAAO;AAED,YAAM,EAAE,WAAW,CAAE,GAAE,YAAY,GAAG,YAAY,OAAO,SAAS,CAAE,EAAA,IAAK;AAGzE,YAAM,gBAAgB,SAAS,UAAU;AACzC,YAAM,oBAAoB,SAAS,OAAO,OAAK,EAAE,OAAO,EAAE;AAC1D,YAAM,WAAW,KAAK,MAAO,oBAAoB,gBAAiB,GAAG;AAGrE,YAAM,mBAAmB,KAAK,0BAA0B,UAAU,MAAM;AACxE,YAAM,iBAAiB,KAAK,wBAAwB,UAAU,SAAS;AACvE,YAAM,cAAc,KAAK,qBAAqB,UAAU,SAAS;AACjE,YAAM,mBAAmB,KAAK,0BAA0B,UAAU,MAAM;AACxE,YAAM,cAAc,KAAK,qBAAqB,QAAQ;AAEtD,YAAM,UAAU;AAAA;AAAA,QAEd;AAAA,QACA;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB;AAAA;AAAA,QAGA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAGA,YAAY,KAAK,oBAAoB,mBAAmB,SAAS;AAAA,QACjE,UAAU,KAAK,iBAAiB,QAAQ;AAAA,QACxC,oBAAoB,KAAK,yBAAyB,UAAU,MAAM;AAAA,MACnE;AAED,WAAK,QAAQ,KAAK,uCAAuC;AAAA,QACvD;AAAA,QACA;AAAA,QACA,kBAAkB,iBAAiB;AAAA,MAC3C,CAAO;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,WAAK,QAAQ,MAAM,8CAA8C,KAAK;AACtE,aAAO,KAAK,wBAAwB,QAAQ;AAAA,IAClD;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQE,MAAM,gBAAgB,UAAU,gBAAgB,MAAM;AACpD,QAAI;AACF,WAAK,QAAQ,KAAK,qCAAqC;AAAA,QACrD,WAAW,SAAS;AAAA,QACpB,QAAQ,SAAS;AAAA,QACjB,kBAAkB,gBAAgB,OAAO,KAAK,cAAc,cAAc,CAAA,CAAE,EAAE,SAAS;AAAA,MAC/F,CAAO;AAGD,YAAM,UAAU,MAAM,KAAK,2BAA2B,UAAU,QAAQ;AAGxE,YAAM,sBAAsB,KAAK,4BAA4B,SAAS,QAAQ;AAG9E,YAAM,mBAAmB,KAAK,0BAA0B,SAAS,QAAQ;AAEzE,aAAO;AAAA,QACL,SAAS;AAAA,QACT,UAAU,KAAK;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA,YAAW,oBAAI,KAAI,GAAG,YAAW;AAAA,MAClC;AAAA,IAEF,SAAQ,OAAO;AACd,WAAK,QAAQ,MAAM,2CAA2C,KAAK;AACnE,aAAO;AAAA,QACL,SAAS;AAAA,QACT,UAAU,KAAK;AAAA,QACf,OAAO,MAAM;AAAA,QACb,kBAAkB,KAAK,oCAAoC,QAAQ;AAAA,QACnE,YAAW,oBAAI,KAAI,GAAG,YAAW;AAAA,MAClC;AAAA,IACP;AAAA,EACG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,MAAM,oCAAoC,eAAe,UAAU;AACjE,QAAI,CAAC,iBAAiB,CAAC,cAAc,YAAY;AAC/C,WAAK,QAAQ,KAAK,kDAAkD;AACpE,aAAO,EAAE,YAAY,IAAI,SAAS,0BAA2B;AAAA,IACnE;AAEI,UAAM,UAAU,CAAE;AAClB,UAAM,aAAa,cAAc;AAGjC,eAAW,CAAC,eAAe,SAAS,KAAK,OAAO,QAAQ,UAAU,GAAG;AACnE,UAAI;AACF,YAAI,aAAa,OAAO,UAAU,YAAY,YAAY;AACxD,eAAK,QAAQ,MAAM,6BAA6B,aAAa;AAC7D,kBAAQ,aAAa,IAAI,MAAM,KAAK;AAAA,YAClC,MAAM,UAAU,QAAQ,QAAQ;AAAA,YAChC;AAAA;AAAA,YACA,gBAAgB;AAAA,UACjB;AAAA,QACX,OAAe;AACL,eAAK,QAAQ,KAAK,gBAAgB,gBAAgB,yBAAyB;AAC3E,kBAAQ,aAAa,IAAI,EAAE,OAAO,oBAAqB;AAAA,QACjE;AAAA,MACO,SAAQ,OAAO;AACd,aAAK,QAAQ,MAAM,+CAA+C,gBAAgB,KAAK;AAAA,UACrF,OAAO,MAAM;AAAA,UACb,OAAO,MAAM,OAAO,UAAU,GAAG,GAAG;AAAA,UACpC;AAAA,UACA,YAAW,oBAAI,KAAI,GAAG,YAAW;AAAA,QAC3C,CAAS;AACD,gBAAQ,aAAa,IAAI;AAAA,UACvB,OAAO,MAAM;AAAA,UACb,UAAU,KAAK,wBAAwB,eAAe,QAAQ;AAAA,UAC9D,WAAW;AAAA,QACZ;AAAA,MACT;AAAA,IACA;AAEI,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,cAAa,oBAAI,KAAM,GAAC,YAAa;AAAA,MACrC,UAAU;AAAA,IACX;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,mBAAmB,IAAI,SAAS,UAAU;AAC9C,WAAO,QAAQ,KAAK;AAAA,MAClB,GAAI;AAAA,MACJ,IAAI;AAAA,QAAQ,CAAC,GAAG,WACd,WAAW,MAAM,OAAO,IAAI,MAAM,QAAQ,CAAC,GAAG,OAAO;AAAA,MAC7D;AAAA,IACA,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,wBAAwB,eAAe,UAAU;AAC/C,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,MAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,4BAA4B,SAAS,UAAU;AAC7C,QAAI;AACF,YAAM,WAAW;AAAA;AAAA,QAEf,YAAY;AAAA,UACV,YAAY,KAAK,yBAAyB,QAAQ;AAAA,UAClD,aAAa,KAAK,0BAA0B,QAAQ;AAAA,UACpD,cAAc,KAAK,2BAA2B,QAAQ;AAAA,UACtD,sBAAsB,KAAK,8BAA8B,QAAQ;AAAA,UACjE,mBAAmB,KAAK,gCAAgC,QAAQ;AAAA,QACjE;AAAA;AAAA,QAGD,WAAW;AAAA,UACT,WAAW,KAAK,wBAAwB,QAAQ;AAAA,UAChD,QAAQ,KAAK,qBAAqB,QAAQ;AAAA,UAC1C,iBAAiB,KAAK,8BAA8B,QAAQ;AAAA,UAC5D,mBAAmB,KAAK,gCAAgC,QAAQ;AAAA,UAChE,kBAAkB,KAAK,+BAA+B,QAAQ;AAAA,QAC/D;AAAA;AAAA,QAGD,SAAS;AAAA,UACP,kBAAkB,KAAK,+BAA+B,QAAQ;AAAA,UAC9D,oBAAoB,KAAK,iCAAiC,QAAQ;AAAA,UAClE,mBAAmB,KAAK,gCAAgC,QAAQ;AAAA,UAChE,oBAAoB,KAAK,iCAAiC,QAAQ;AAAA,QACnE;AAAA;AAAA,QAGD,OAAO;AAAA,UACL,iBAAiB,KAAK,8BAA8B,QAAQ;AAAA,UAC5D,kBAAkB,KAAK,+BAA+B,QAAQ;AAAA,UAC9D,cAAc,KAAK,2BAA2B,QAAQ;AAAA,UACtD,eAAe,KAAK,4BAA4B,QAAQ;AAAA,QACzD;AAAA;AAAA,QAGD,iBAAiB,KAAK,mCAAmC,SAAS,QAAQ;AAAA;AAAA,QAG1E,oBAAoB,KAAK,2BAA2B,SAAS,QAAQ;AAAA;AAAA,QAGrE,sBAAsB,KAAK,6BAA6B,SAAS,QAAQ;AAAA;AAAA,QAGzE,UAAU;AAAA,UACR,oBAAmB,oBAAI,KAAM,GAAC,YAAa;AAAA,UAC3C,UAAU,KAAK;AAAA,UACf,iBAAiB;AAAA,UACjB,iBAAiB,KAAK,iCAAiC,SAAS,QAAQ;AAAA,QAClF;AAAA,MACO;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,WAAK,QAAQ,MAAM,wCAAwC,KAAK;AAChE,aAAO,KAAK,oCAAoC,QAAQ;AAAA,IAC9D;AAAA,EACG;AAAA;AAAA;AAAA;AAAA,EAID,yBAAyB,UAAU;AACjC,UAAM,eAAe,SAAS,gBAAgB,CAAE;AAChD,UAAM,YAAY,SAAS,aAAa;AACxC,UAAM,iBAAiB,SAAS,kBAAkB;AAElD,QAAI,QAAQ;AAGZ,QAAI,aAAa,SAAS,GAAG;AAC3B,eAAS,KAAK,IAAI,IAAI,aAAa,SAAS,CAAC;AAAA,IACnD;AAGI,QAAI,YAAY,KAAO;AACrB,eAAS;AAAA,IACf;AAGI,aAAS,iBAAiB;AAE1B,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAC3C;AAAA,EAEE,0BAA0B,UAAU;AAClC,UAAM,WAAW,SAAS,YAAY;AACtC,UAAM,SAAS,SAAS,UAAU;AAClC,UAAM,aAAa,SAAS,cAAc;AAE1C,QAAI,QAAQ;AAEZ,QAAI,WAAW,KAAK,aAAa,KAAK;AACpC,eAAS;AAAA,IACf;AAEI,QAAI,SAAS,KAAK,aAAa,KAAK;AAClC,eAAS;AAAA,IACf;AAEI,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAC3C;AAAA,EAEE,2BAA2B,UAAU;AACnC,UAAM,oBAAoB,SAAS,qBAAqB;AACxD,UAAM,oBAAoB,SAAS,qBAAqB;AAExD,QAAI,QAAQ;AAEZ,QAAI,oBAAoB,GAAG;AACzB,eAAS,oBAAoB;AAAA,IACnC;AAEI,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAC3C;AAAA,EAEE,8BAA8B,UAAU;AACtC,UAAM,SAAS,SAAS,UAAU;AAClC,UAAM,YAAY,SAAS,aAAa;AACxC,UAAM,aAAa,SAAS,cAAc;AAE1C,QAAI,QAAQ;AAEZ,QAAI,SAAS,KAAK,CAAC,WAAW;AAC5B,eAAS;AAAA,IACf;AAEI,QAAI,aAAa,KAAK;AACpB,eAAS;AAAA,IACf;AAEI,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAC3C;AAAA,EAEE,gCAAgC,UAAU;AAExC,UAAM,aAAa,KAAK,yBAAyB,QAAQ;AACzD,WAAO,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,aAAa,GAAG,CAAC;AAAA,EACtD;AAAA,EAEE,wBAAwB,UAAU;AAChC,UAAM,YAAY,SAAS,aAAa;AACxC,UAAM,eAAe,SAAS,gBAAgB;AAC9C,UAAM,eAAe,SAAS,uBAAuB;AAErD,QAAI,QAAQ;AAEZ,QAAI,YAAY,KAAO;AACrB,eAAS;AAAA,IACf;AAEI,QAAI,eAAe,GAAG;AACpB,eAAS;AAAA,IACf;AAEI,QAAI,eAAe,KAAM;AACvB,eAAS;AAAA,IACf;AAEI,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAC3C;AAAA,EAEE,qBAAqB,UAAU;AAC7B,UAAM,WAAW,SAAS,YAAY;AACtC,UAAM,WAAW,SAAS,YAAY,CAAE;AAExC,QAAI,QAAQ;AAEZ,QAAI,WAAW,IAAI;AACjB,eAAS;AAAA,IACf;AAEI,QAAI,SAAS,SAAS,GAAG;AACvB,eAAS;AAAA,IACf;AAEI,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAC3C;AAAA,EAEE,8BAA8B,UAAU;AACtC,UAAM,eAAe,SAAS,uBAAuB;AACrD,UAAM,WAAW,SAAS,YAAY;AAEtC,QAAI,QAAQ;AAEZ,QAAI,eAAe,QAAQ,WAAW,IAAI;AACxC,eAAS;AAAA,IACf,WAAe,eAAe,MAAM;AAC9B,eAAS;AAAA,IACf;AAEI,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAC3C;AAAA,EAEE,gCAAgC,UAAU;AACxC,UAAM,mBAAmB,SAAS,oBAAoB;AACtD,UAAM,oBAAoB,SAAS,qBAAqB;AACxD,UAAM,gBAAgB,SAAS,iBAAiB;AAEhD,UAAM,SAAS,mBAAmB,oBAAoB,iBAAiB;AACvE,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAC3C;AAAA,EAEE,+BAA+B,UAAU;AACvC,UAAM,cAAc,SAAS,eAAe;AAC5C,UAAM,iBAAiB,SAAS,kBAAkB;AAElD,QAAI,QAAQ;AAEZ,QAAI,cAAc,KAAK,iBAAiB,IAAI;AAC1C,eAAS;AAAA,IACf;AAEI,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAC3C;AAAA,EAEE,+BAA+B,UAAU;AACvC,WAAO,KAAK,+BAA+B,QAAQ;AAAA,EACvD;AAAA,EAEE,iCAAiC,UAAU;AACzC,UAAM,gBAAgB,SAAS,iBAAiB;AAChD,UAAM,mBAAmB,SAAS,oBAAoB;AAEtD,QAAI,QAAQ;AAEZ,QAAI,gBAAgB,GAAG;AACrB,cAAQ;AAAA,IACd;AAEI,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAC3C;AAAA,EAEE,gCAAgC,UAAU;AACxC,UAAM,oBAAoB,SAAS,qBAAqB;AACxD,UAAM,gBAAgB,SAAS,iBAAiB;AAEhD,QAAI,QAAQ;AAEZ,QAAI,oBAAoB,GAAG;AACzB,cAAQ;AAAA,IACd;AAEI,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAC3C;AAAA,EAEE,iCAAiC,UAAU;AACzC,UAAM,SAAS,KAAK,+BAA+B,QAAQ;AAC3D,UAAM,WAAW,KAAK,iCAAiC,QAAQ;AAC/D,UAAM,UAAU,KAAK,gCAAgC,QAAQ;AAE7D,YAAQ,SAAS,WAAW,WAAW;AAAA,EAC3C;AAAA,EAEE,8BAA8B,UAAU;AACtC,UAAM,YAAY,SAAS,aAAa;AACxC,UAAM,eAAe,SAAS,gBAAgB;AAE9C,YAAQ,YAAY,gBAAgB;AAAA,EACxC;AAAA,EAEE,+BAA+B,UAAU;AACvC,UAAM,YAAY,SAAS,aAAa;AACxC,UAAM,eAAe,SAAS,gBAAgB;AAE9C,QAAI,QAAQ;AAEZ,QAAI,YAAY,IAAI;AAClB,cAAQ;AAAA,IACd;AAEI,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAC3C;AAAA,EAEE,2BAA2B,UAAU;AACnC,UAAM,sBAAsB,SAAS,uBAAuB;AAC5D,UAAM,wBAAwB,SAAS,yBAAyB;AAEhE,YAAQ,sBAAsB,yBAAyB;AAAA,EAC3D;AAAA,EAEE,4BAA4B,UAAU;AACpC,UAAM,gBAAgB,SAAS,iBAAiB;AAChD,UAAM,mBAAmB,SAAS,oBAAoB;AAEtD,QAAI,QAAQ;AAEZ,QAAI,gBAAgB,GAAG;AACrB,cAAQ;AAAA,IACd;AAEI,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAC3C;AAAA,EAEE,mCAAmC,SAAS,UAAU;AACpD,UAAM,kBAAkB,CAAE;AAG1B,UAAM,aAAa,KAAK,yBAAyB,QAAQ;AACzD,QAAI,aAAa,IAAI;AACnB,sBAAgB,KAAK;AAAA,QACnB,UAAU;AAAA,QACV,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,WAAW;AAAA,MACnB,CAAO;AAAA,IACP;AAGI,UAAM,YAAY,KAAK,wBAAwB,QAAQ;AACvD,QAAI,YAAY,IAAI;AAClB,sBAAgB,KAAK;AAAA,QACnB,UAAU;AAAA,QACV,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,WAAW;AAAA,MACnB,CAAO;AAAA,IACP;AAGI,UAAM,aAAa,KAAK,8BAA8B,QAAQ;AAC9D,QAAI,aAAa,IAAI;AACnB,sBAAgB,KAAK;AAAA,QACnB,UAAU;AAAA,QACV,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,WAAW;AAAA,MACnB,CAAO;AAAA,IACP;AAEI,WAAO;AAAA,EACX;AAAA,EAEE,2BAA2B,SAAS,UAAU;AAC5C,WAAO;AAAA,MACL,iBAAiB,KAAK,yBAAyB,QAAQ;AAAA,MACvD,eAAe,KAAK,sBAAsB,QAAQ;AAAA,MAClD,gBAAgB,KAAK,uBAAuB,QAAQ;AAAA,MACpD,kBAAkB,KAAK,yBAAyB,QAAQ;AAAA,MACxD,YAAY,KAAK,mBAAmB,QAAQ;AAAA,IAC7C;AAAA,EACL;AAAA,EAEE,6BAA6B,SAAS,UAAU;AAE9C,WAAO;AAAA,MACL,UAAU,KAAK;AAAA,MACf,iBAAiB;AAAA,MACjB,iBAAiB,KAAK,yBAAyB,QAAQ;AAAA,MACvD,iBAAiB,KAAK,wBAAwB,QAAQ;AAAA,IACvD;AAAA,EACL;AAAA,EAEE,iCAAiC,SAAS,UAAU;AAClD,QAAI,aAAa;AAGjB,UAAM,aAAa,OAAO,KAAK,QAAQ,EAAE;AACzC,QAAI,aAAa,GAAI,eAAc;AAAA,aAC1B,aAAa,EAAG,eAAc;AAGvC,UAAM,eAAe,OAAO,KAAK,OAAO,EAAE;AAC1C,QAAI,eAAe,EAAG,eAAc;AAAA,aAC3B,eAAe,EAAG,eAAc;AAGzC,UAAM,cAAc,SAAS,aAAa;AAC1C,QAAI,cAAc,IAAO,eAAc;AAEvC,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,UAAU,CAAC;AAAA,EAChD;AAAA,EAEE,oCAAoC,UAAU;AAC5C,WAAO;AAAA,MACL,YAAY,EAAE,YAAY,IAAI,aAAa,IAAI,cAAc,IAAI,sBAAsB,IAAI,mBAAmB,GAAI;AAAA,MAClH,WAAW,EAAE,WAAW,IAAI,QAAQ,IAAI,iBAAiB,IAAI,mBAAmB,IAAI,kBAAkB,GAAI;AAAA,MAC1G,SAAS,EAAE,kBAAkB,IAAI,oBAAoB,IAAI,mBAAmB,IAAI,oBAAoB,GAAI;AAAA,MACxG,OAAO,EAAE,iBAAiB,IAAI,kBAAkB,IAAI,cAAc,IAAI,eAAe,GAAI;AAAA,MACzF,iBAAiB,CAAE;AAAA,MACnB,oBAAoB,EAAE,iBAAiB,IAAI,eAAe,CAAE,GAAE,gBAAgB,CAAA,GAAI,kBAAkB,IAAI,YAAY,CAAA,EAAI;AAAA,MACxH,sBAAsB,EAAE,UAAU,KAAK,UAAU,iBAAiB,CAAE,GAAE,iBAAiB,IAAI,iBAAiB,GAAI;AAAA,MAChH,UAAU,EAAE,oBAAmB,oBAAI,KAAI,GAAG,YAAa,GAAE,UAAU,KAAK,UAAU,iBAAiB,SAAS,iBAAiB,GAAE;AAAA,IAChI;AAAA,EACL;AAAA,EAEE,yBAAyB,UAAU;AACjC,UAAM,WAAW,SAAS,YAAY;AACtC,UAAM,aAAa,SAAS,cAAc;AAC1C,UAAM,aAAa,KAAK,yBAAyB,QAAQ;AAEzD,YAAQ,WAAW,aAAa,cAAc;AAAA,EAClD;AAAA,EAEE,sBAAsB,UAAU;AAC9B,UAAM,YAAY,CAAE;AAEpB,QAAI,SAAS,WAAW,GAAI,WAAU,KAAK,UAAU;AACrD,QAAI,SAAS,sBAAsB,IAAM,WAAU,KAAK,wBAAwB;AAChF,QAAI,KAAK,yBAAyB,QAAQ,IAAI,GAAI,WAAU,KAAK,aAAa;AAE9E,WAAO;AAAA,EACX;AAAA,EAEE,uBAAuB,UAAU;AAC/B,UAAM,aAAa,CAAE;AAErB,QAAI,SAAS,WAAW,GAAI,YAAW,KAAK,UAAU;AACtD,QAAI,SAAS,sBAAsB,IAAM,YAAW,KAAK,wBAAwB;AACjF,QAAI,KAAK,yBAAyB,QAAQ,IAAI,GAAI,YAAW,KAAK,aAAa;AAE/E,WAAO;AAAA,EACX;AAAA,EAEE,yBAAyB,UAAU;AACjC,UAAM,QAAQ,CAAE;AAEhB,QAAI,SAAS,WAAW,IAAI;AAC1B,YAAM,KAAK,6BAA6B;AAAA,IAC9C;AAEI,QAAI,SAAS,sBAAsB,KAAM;AACvC,YAAM,KAAK,oDAAoD;AAAA,IACrE;AAEI,WAAO;AAAA,EACX;AAAA,EAEE,mBAAmB,UAAU;AAC3B,WAAO;AAAA,MACL,EAAE,WAAW,4BAA4B,UAAU,SAAS,aAAa,IAAK;AAAA,MAC9E,EAAE,WAAW,yBAAyB,UAAU,SAAS,WAAW,GAAI;AAAA,MACxE,EAAE,WAAW,0BAA0B,UAAU,KAAK,yBAAyB,QAAQ,IAAI,GAAE;AAAA,IAC9F;AAAA,EACL;AAAA,EAEE,yBAAyB,UAAU;AACjC,UAAM,WAAW,SAAS,YAAY;AACtC,UAAM,aAAa,SAAS,cAAc;AAC1C,UAAM,aAAa,SAAS,cAAc;AAE1C,YAAQ,WAAW,aAAa,cAAc;AAAA,EAClD;AAAA,EAEE,wBAAwB,UAAU;AAChC,UAAM,QAAQ,CAAE;AAEhB,QAAI,SAAS,WAAW,IAAI;AAC1B,YAAM,KAAK,qBAAqB;AAAA,IACtC;AAEI,QAAI,SAAS,sBAAsB,KAAM;AACvC,YAAM,KAAK,uBAAuB;AAAA,IACxC;AAEI,QAAI,KAAK,yBAAyB,QAAQ,IAAI,IAAI;AAChD,YAAM,KAAK,kCAAkC;AAAA,IACnD;AAEI,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,0BAA0B,SAAS,UAAU;AAC3C,QAAI;AACF,aAAO;AAAA;AAAA,QAEL,OAAO;AAAA,UACL,UAAU,SAAS,YAAY;AAAA,UAC/B,cAAc,SAAS,uBAAuB;AAAA,UAC9C,YAAY,SAAS,cAAc;AAAA,UACnC,OAAO,SAAS,SAAS;AAAA,UACzB,UAAU,SAAS,aAAa;AAAA,UAChC,UAAU,SAAS,YAAY;AAAA,UAC/B,QAAQ,SAAS,UAAU;AAAA,QAC5B;AAAA;AAAA,QAGD,WAAW;AAAA,UACT,WAAW,KAAK,wBAAwB,QAAQ;AAAA,UAChD,QAAQ,KAAK,qBAAqB,QAAQ;AAAA,UAC1C,iBAAiB,KAAK,8BAA8B,QAAQ;AAAA,UAC5D,mBAAmB,KAAK,gCAAgC,QAAQ;AAAA,QACjE;AAAA;AAAA,QAGD,YAAY;AAAA,UACV,YAAY,KAAK,yBAAyB,QAAQ;AAAA,UAClD,aAAa,KAAK,0BAA0B,QAAQ;AAAA,UACpD,cAAc,KAAK,2BAA2B,QAAQ;AAAA,UACtD,sBAAsB,KAAK,8BAA8B,QAAQ;AAAA,QAClE;AAAA;AAAA,QAGD,SAAS;AAAA,UACP,kBAAkB,KAAK,+BAA+B,QAAQ;AAAA,UAC9D,oBAAoB,KAAK,iCAAiC,QAAQ;AAAA,UAClE,mBAAmB,KAAK,gCAAgC,QAAQ;AAAA,UAChE,oBAAoB,KAAK,iCAAiC,QAAQ;AAAA,QACnE;AAAA;AAAA,QAGD,OAAO;AAAA,UACL,iBAAiB,KAAK,8BAA8B,QAAQ;AAAA,UAC5D,kBAAkB,KAAK,+BAA+B,QAAQ;AAAA,UAC9D,cAAc,KAAK,2BAA2B,QAAQ;AAAA,UACtD,eAAe,KAAK,4BAA4B,QAAQ;AAAA,QACzD;AAAA;AAAA,QAGD,cAAc;AAAA;AAAA,QAGd,UAAU;AAAA,UACR,UAAU,KAAK;AAAA,UACf,sBAAqB,oBAAI,KAAM,GAAC,YAAa;AAAA,UAC7C,SAAS;AAAA,QACnB;AAAA,MACO;AAAA,IACF,SAAQ,OAAO;AACd,WAAK,QAAQ,MAAM,4CAA4C,KAAK;AACpE,aAAO;AAAA,QACL,OAAO,EAAE,UAAU,GAAG,cAAc,GAAG,YAAY,GAAG,OAAO,GAAG,UAAU,GAAG,UAAU,GAAG,QAAQ,EAAG;AAAA,QACrG,WAAW,EAAE,WAAW,IAAI,QAAQ,IAAI,iBAAiB,IAAI,mBAAmB,GAAI;AAAA,QACpF,YAAY,EAAE,YAAY,IAAI,aAAa,IAAI,cAAc,IAAI,sBAAsB,GAAI;AAAA,QAC3F,SAAS,EAAE,kBAAkB,IAAI,oBAAoB,IAAI,mBAAmB,IAAI,oBAAoB,GAAI;AAAA,QACxG,OAAO,EAAE,iBAAiB,IAAI,kBAAkB,IAAI,cAAc,IAAI,eAAe,GAAI;AAAA,QACzF,cAAc;AAAA,QACd,UAAU,EAAE,UAAU,KAAK,UAAU,sBAAqB,oBAAI,QAAO,eAAe,SAAS,QAAO;AAAA,MACrG;AAAA,IACP;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAKE,2BAA2B,UAAU;AACnC,UAAM,EAAE,eAAe,GAAG,gBAAgB,GAAG,eAAe,CAAE,EAAA,IAAK;AACnE,WAAO;AAAA,MACL,UAAU,KAAK,MAAO,eAAe,gBAAiB,GAAG;AAAA,MACzD;AAAA,MACA;AAAA,MACA,cAAc,KAAK,sBAAsB,YAAY;AAAA,MACrD,YAAY,KAAK,oBAAoB,YAAY;AAAA,IAClD;AAAA,EACL;AAAA,EAEE,6BAA6B,UAAU;AACrC,UAAM,EAAE,eAAe,CAAE,EAAA,IAAK;AAC9B,WAAO;AAAA,MACL,mBAAmB,KAAK,2BAA2B,YAAY;AAAA,MAC/D,cAAc,KAAK,sBAAsB,YAAY;AAAA,MACrD,aAAa,KAAK,qBAAqB,YAAY;AAAA,IACpD;AAAA,EACL;AAAA,EAEE,0BAA0B,UAAU;AAClC,UAAM,EAAE,eAAe,CAAE,EAAA,IAAK;AAC9B,UAAM,gBAAgB,aAAa,IAAI,OAAK,EAAE,gBAAgB,CAAC,EAAE,OAAO,OAAK,IAAI,CAAC;AAElF,QAAI,cAAc,WAAW,GAAG;AAC9B,aAAO,EAAE,SAAS,GAAG,QAAQ,GAAG,aAAa,GAAG,SAAS,oBAAqB;AAAA,IACpF;AAEI,UAAM,UAAU,cAAc,OAAO,CAAC,KAAK,SAAS,MAAM,MAAM,CAAC,IAAI,cAAc;AACnF,UAAM,SAAS,cAAc,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AACjD,UAAM,SAAS,OAAO,KAAK,MAAM,OAAO,SAAS,CAAC,CAAC;AAEnD,WAAO;AAAA,MACL,SAAS,KAAK,MAAM,OAAO;AAAA,MAC3B,QAAQ,KAAK,MAAM,MAAM;AAAA,MACzB,KAAK,KAAK,IAAI,GAAG,aAAa;AAAA,MAC9B,KAAK,KAAK,IAAI,GAAG,aAAa;AAAA,MAC9B,aAAa,KAAK,MAAM,KAAK,qBAAqB,aAAa,CAAC;AAAA,MAChE,SAAS;AAAA,IACV;AAAA,EACL;AAAA,EAEE,4BAA4B,UAAU;AACpC,UAAM,EAAE,eAAe,CAAE,EAAA,IAAK;AAC9B,WAAO;AAAA,MACL,eAAe,aAAa;AAAA,MAC5B,iBAAiB,aAAa,OAAO,OAAK,EAAE,OAAO,EAAE;AAAA,MACrD,cAAc,KAAK,qBAAqB,YAAY;AAAA,MACpD,eAAe,KAAK,sBAAsB,YAAY;AAAA,IACvD;AAAA,EACL;AAAA,EAEE,4BAA4B,UAAU;AACpC,UAAM,EAAE,eAAe,CAAE,EAAA,IAAK;AAC9B,WAAO;AAAA,MACL,aAAa,KAAK,qBAAqB,YAAY;AAAA,MACnD,cAAc,KAAK,sBAAsB,YAAY;AAAA,MACrD,YAAY,KAAK,yBAAyB,QAAQ;AAAA,IACnD;AAAA,EACL;AAAA,EAEE,6BAA6B,UAAU;AACrC,UAAM,EAAE,eAAe,CAAE,EAAA,IAAK;AAC9B,WAAO;AAAA,MACL,mBAAmB,KAAK,2BAA2B,YAAY;AAAA,MAC/D,eAAe,KAAK,uBAAuB,YAAY;AAAA,MACvD,iBAAiB,KAAK,yBAAyB,YAAY;AAAA,IAC5D;AAAA,EACL;AAAA,EAEE,oCAAoC,UAAU;AAC5C,UAAM,kBAAkB,CAAE;AAC1B,UAAM,WAAW,SAAS,YAAY;AACtC,UAAM,eAAe,SAAS,uBAAuB;AAErD,QAAI,WAAW,IAAI;AACjB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,aAAa;AAAA,MACrB,CAAO;AAAA,IACP;AAEI,QAAI,eAAe,KAAM;AACvB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,aAAa;AAAA,MACrB,CAAO;AAAA,IACP;AAEI,WAAO;AAAA,EACX;AAAA;AAAA,EAGE,sBAAsB,cAAc;AAClC,UAAM,sBAAsB,aAAa,OAAO,OAAK,EAAE,OAAO;AAC9D,WAAO,oBAAoB,SAAS,KAAK,IAAI,GAAG,aAAa,MAAM,IAAI;AAAA,EAC3E;AAAA,EAEE,2BAA2B,cAAc;AACvC,WAAO,KAAK,sBAAsB,YAAY;AAAA,EAClD;AAAA,EAEE,oBAAoB,cAAc;AAChC,UAAM,YAAY,aAAa,OAAO,CAAC,KAAK,MAAM,OAAO,EAAE,gBAAgB,IAAI,CAAC;AAChF,UAAM,eAAe,aAAa,OAAO,OAAK,EAAE,OAAO,EAAE;AACzD,WAAO,YAAY,IAAK,eAAe,YAAa,MAAO;AAAA,EAC/D;AAAA,EAEE,qBAAqB,cAAc;AACjC,UAAM,QAAQ,CAAE;AAChB,iBAAa,QAAQ,OAAK;AACxB,YAAM,OAAO,EAAE,eAAe;AAC9B,YAAM,IAAI,KAAK,MAAM,IAAI,KAAK,KAAK;AAAA,IACzC,CAAK;AACD,WAAO;AAAA,EACX;AAAA,EAEE,sBAAsB,cAAc;AAClC,UAAM,SAAS,aAAa,OAAO,OAAK,CAAC,EAAE,OAAO;AAClD,WAAO;AAAA,MACL,aAAa,OAAO;AAAA,MACpB,gBAAgB,OAAO,SAAS,KAAK,IAAI,GAAG,aAAa,MAAM,IAAI;AAAA,IACpE;AAAA,EACL;AAAA,EAEE,qBAAqB,QAAQ;AAC3B,QAAI,OAAO,SAAS,EAAG,QAAO;AAC9B,UAAM,OAAO,OAAO,OAAO,CAAC,KAAK,MAAM,MAAM,GAAG,CAAC,IAAI,OAAO;AAC5D,UAAM,WAAW,OAAO,OAAO,CAAC,KAAK,MAAM,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,OAAO;AACpF,WAAO,KAAK,KAAK,QAAQ;AAAA,EAC7B;AAAA;AAAA,EAGE,0BAA0B,UAAU,QAAQ;AAC1C,UAAM,oBAAoB,SAAS,OAAO,OAAK,EAAE,OAAO,EAAE;AAC1D,UAAM,gBAAgB,SAAS,UAAU;AACzC,UAAM,WAAW,oBAAoB;AAGrC,UAAM,kBAAkB,OAAO,SAAS,IAAI,KAAK,IAAI,OAAO,SAAS,IAAI,CAAC,IAAI;AAC9E,UAAM,eAAgB,WAAW,MAAQ,kBAAkB;AAE3D,WAAO;AAAA,MACL,OAAO,KAAK,MAAM,eAAe,GAAG;AAAA,MACpC;AAAA,MACA,YAAY;AAAA,MACZ,OAAO,eAAe,MAAM,aAAa,eAAe,MAAM,iBAAiB;AAAA,IAChF;AAAA,EACL;AAAA,EAEE,wBAAwB,UAAU,WAAW;AAC3C,UAAM,gBAAgB,SAAS,UAAU;AACzC,UAAM,aAAa,YAAY,KAAK,IAAI,GAAG,KAAK,gBAAgB,KAAK,EAAE,IAAI;AAC3E,UAAM,kBAAkB,YAAY,MAAM;AAE1C,UAAM,sBAAuB,aAAa,MAAO;AAEjD,WAAO;AAAA,MACL,OAAO,KAAK,MAAM,sBAAsB,GAAG;AAAA,MAC3C;AAAA,MACA;AAAA,MACA,UAAU,gBAAgB,IAAI,cAAc,gBAAgB,KAAK,eAAe;AAAA,IACjF;AAAA,EACL;AAAA,EAEE,qBAAqB,UAAU,WAAW;AACxC,UAAM,iBAAiB,YAAY,IAAI,YAAY,SAAS,SAAS;AACrE,UAAM,uBAAuB,SAAS,SAAS,IAAI,IAAI,SAAS,SAAS;AACzE,UAAM,kBAAkB,iBAAiB,MAAO,IAAI,iBAAiB;AAErE,UAAM,mBAAoB,uBAAuB,MAAQ,kBAAkB;AAE3E,WAAO;AAAA,MACL,OAAO,KAAK,MAAM,mBAAmB,GAAG;AAAA,MACxC,UAAU,SAAS;AAAA,MACnB;AAAA,MACA,OAAO,mBAAmB,MAAM,SAAS,mBAAmB,MAAM,aAAa;AAAA,IAChF;AAAA,EACL;AAAA,EAEE,0BAA0B,UAAU,QAAQ;AAC1C,UAAM,mBAAmB,OAAO,SAAS,IAAI,OAAO,SAAS,KAAK;AAClE,UAAM,qBAAqB,SAAS,SAAS,IAC3C,SAAS,OAAO,OAAK,EAAE,OAAO,EAAE,SAAS,SAAS,SAAS;AAE7D,UAAM,cAAe,qBAAqB,MAAQ,mBAAmB;AAErE,WAAO;AAAA,MACL,OAAO,KAAK,MAAM,cAAc,GAAG;AAAA,MACnC,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,YAAY,cAAc,MAAM,cAAc;AAAA,IAC/C;AAAA,EACL;AAAA,EAEE,qBAAqB,UAAU;AAC7B,UAAM,kBAAkB,SAAS,SAAS,IACxC,SAAS,OAAO,CAAC,KAAK,MAAM,OAAO,EAAE,gBAAgB,MAAO,CAAC,IAAI,SAAS,SAAS;AAErF,UAAM,kBAAkB,KAAK,IAAI,GAAG,KAAK,kBAAkB,OAAQ,GAAI;AACvE,UAAM,YAAY,SAAS,SAAS,IAClC,SAAS,OAAO,OAAK,EAAE,OAAO,EAAE,SAAS,SAAS,SAAS;AAE7D,UAAM,aAAc,kBAAkB,MAAQ,YAAY;AAE1D,WAAO;AAAA,MACL,OAAO,KAAK,MAAM,aAAa,GAAG;AAAA,MAClC,cAAc;AAAA,MACd;AAAA,MACA,OAAO,aAAa,MAAM,YAAY,aAAa,MAAM,eAAe;AAAA,IACzE;AAAA,EACL;AAAA,EAIE,iBAAiB,UAAU;AACzB,QAAI,SAAS,SAAS,EAAG,QAAO;AAEhC,UAAM,YAAY,SAAS,OAAO,OAAK,CAAC,EAAE,OAAO,EAAE,SAAS,SAAS;AACrE,UAAM,UAAU,SAAS,OAAO,CAAC,KAAK,MAAM,OAAO,EAAE,gBAAgB,MAAO,CAAC,IAAI,SAAS;AAE1F,QAAI,YAAY,OAAO,UAAU,IAAM,QAAO;AAC9C,QAAI,YAAY,OAAO,UAAU,IAAM,QAAO;AAC9C,QAAI,YAAY,IAAK,QAAO;AAC5B,WAAO;AAAA,EACX;AAAA,EAEE,yBAAyB,UAAU,QAAQ;AACzC,UAAM,aAAa,OAAO,UAAU;AACpC,UAAM,cAAc,SAAS,SAAS,IACpC,SAAS,OAAO,OAAK,EAAE,OAAO,EAAE,SAAS,SAAS,SAAS;AAE7D,UAAM,kBAAkB,eAAe,IAAI,aAAa;AAExD,WAAO;AAAA,MACL,OAAO,KAAK,MAAM,KAAK,IAAI,KAAK,kBAAkB,GAAG,CAAC;AAAA,MACtD;AAAA,MACA;AAAA,MACA,UAAU,kBAAkB,MAAM,cAAc,kBAAkB,MAAM,SAAS;AAAA,IAClF;AAAA,EACL;AAGA;AC3gCO,MAAM,uBAAuB;AAAA;AAAA,EAElC,iBAAiB;AAAA,IACf,eAAe;AAAA,MACb,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,kBAAkB,CAAC,sBAAsB,oBAAoB,iBAAiB;AAAA,MAC9E,gBAAgB,CAAC,sBAAsB,gBAAgB,oBAAoB;AAAA,MAC3E,cAAc;AAAA,QACZ,MAAM;AAAA,UACJ,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,WAAW;AAAA,UACX,WAAW;AAAA,UACX,aAAa;AAAA,QACd;AAAA,QACD,QAAQ;AAAA,UACN,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,WAAW;AAAA,UACX,WAAW;AAAA,UACX,aAAa;AAAA,QACd;AAAA,QACD,MAAM;AAAA,UACJ,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,WAAW;AAAA,UACX,WAAW;AAAA,UACX,aAAa;AAAA,QACd;AAAA,MACF;AAAA,MACD,SAAS,CAAC,mBAAmB,4BAA4B,uBAAuB,gBAAgB;AAAA,IACjG;AAAA,IAED,iBAAiB;AAAA,MACf,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,kBAAkB,CAAC,yBAAyB,kBAAkB,uBAAuB;AAAA,MACrF,gBAAgB,CAAC,uBAAuB,UAAU,WAAW;AAAA,MAC7D,cAAc;AAAA,QACZ,MAAM;AAAA,UACJ,gBAAgB;AAAA,UAChB,cAAc;AAAA,UACd,mBAAmB;AAAA,UACnB,cAAc;AAAA,QACf;AAAA,QACD,QAAQ;AAAA,UACN,gBAAgB;AAAA,UAChB,cAAc;AAAA,UACd,mBAAmB;AAAA,UACnB,cAAc;AAAA,QACf;AAAA,QACD,MAAM;AAAA,UACJ,gBAAgB;AAAA,UAChB,cAAc;AAAA,UACd,mBAAmB;AAAA,UACnB,cAAc;AAAA,QACf;AAAA,MACF;AAAA,MACD,SAAS,CAAC,yBAAyB,mBAAmB,uBAAuB,qBAAqB;AAAA,IACnG;AAAA,IAED,yBAAyB;AAAA,MACvB,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,kBAAkB,CAAC,0BAA0B,mBAAmB,mBAAmB;AAAA,MACnF,gBAAgB,CAAC,qBAAqB,qBAAqB,uBAAuB;AAAA,MAClF,cAAc;AAAA,QACZ,MAAM;AAAA,UACJ,gBAAgB,CAAC,IAAI,GAAG;AAAA,UACxB,YAAY;AAAA,UACZ,iBAAiB;AAAA,UACjB,cAAc;AAAA,QACf;AAAA,QACD,QAAQ;AAAA,UACN,gBAAgB,CAAC,IAAI,KAAK,GAAG;AAAA,UAC7B,YAAY;AAAA,UACZ,iBAAiB;AAAA,UACjB,cAAc;AAAA,QACf;AAAA,QACD,MAAM;AAAA,UACJ,gBAAgB,CAAC,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,UAChD,YAAY;AAAA,UACZ,iBAAiB;AAAA,UACjB,cAAc;AAAA,QACf;AAAA,MACF;AAAA,MACD,SAAS,CAAC,qBAAqB,yBAAyB,kCAAkC,gBAAgB;AAAA,IAC3G;AAAA,IAED,sBAAsB;AAAA,MACpB,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,kBAAkB,CAAC,kBAAkB,yBAAyB,uBAAuB;AAAA,MACrF,gBAAgB,CAAC,sBAAsB,qBAAqB,qBAAqB;AAAA,MACjF,cAAc;AAAA,QACZ,MAAM;AAAA,UACJ,YAAY;AAAA,UACZ,mBAAmB;AAAA,UACnB,eAAe;AAAA,UACf,iBAAiB,CAAC,SAAS,SAAS;AAAA,QACrC;AAAA,QACD,QAAQ;AAAA,UACN,YAAY;AAAA,UACZ,mBAAmB;AAAA,UACnB,eAAe;AAAA,UACf,iBAAiB,CAAC,WAAW,WAAW,QAAQ;AAAA,QACjD;AAAA,QACD,MAAM;AAAA,UACJ,YAAY;AAAA,UACZ,mBAAmB;AAAA,UACnB,eAAe;AAAA,UACf,iBAAiB,CAAC,WAAW,WAAW,UAAU,OAAO;AAAA,QAC1D;AAAA,MACF;AAAA,MACD,SAAS,CAAC,2BAA2B,oBAAoB,wBAAwB,kBAAkB;AAAA,IACpG;AAAA,IAED,wBAAwB;AAAA,MACtB,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,kBAAkB,CAAC,uBAAuB,qBAAqB,qBAAqB;AAAA,MACpF,gBAAgB,CAAC,sBAAsB,oBAAoB,qBAAqB;AAAA,MAChF,cAAc;AAAA,QACZ,MAAM;AAAA,UACJ,eAAe;AAAA,UACf,cAAc,CAAC,eAAe,YAAY;AAAA,UAC1C,WAAW;AAAA,UACX,mBAAmB;AAAA,QACpB;AAAA,QACD,QAAQ;AAAA,UACN,eAAe;AAAA,UACf,cAAc,CAAC,eAAe,cAAc,aAAa;AAAA,UACzD,WAAW;AAAA,UACX,mBAAmB;AAAA,QACpB;AAAA,QACD,MAAM;AAAA,UACJ,eAAe;AAAA,UACf,cAAc,CAAC,eAAe,cAAc,eAAe,SAAS;AAAA,UACpE,WAAW;AAAA,UACX,mBAAmB;AAAA,QACpB;AAAA,MACF;AAAA,MACD,SAAS,CAAC,6BAA6B,oBAAoB,sBAAsB,sBAAsB;AAAA,IACxG;AAAA,IAED,uBAAuB;AAAA,MACrB,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,kBAAkB,CAAC,sBAAsB,iBAAiB,wBAAwB;AAAA,MAClF,gBAAgB,CAAC,oBAAoB,wBAAwB,wBAAwB;AAAA,MACrF,cAAc;AAAA,QACZ,MAAM;AAAA,UACJ,UAAU;AAAA,UACV,oBAAoB;AAAA,UACpB,cAAc;AAAA,UACd,sBAAsB;AAAA,QACvB;AAAA,QACD,QAAQ;AAAA,UACN,UAAU;AAAA,UACV,oBAAoB;AAAA,UACpB,cAAc;AAAA,UACd,sBAAsB;AAAA,QACvB;AAAA,QACD,MAAM;AAAA,UACJ,UAAU;AAAA,UACV,oBAAoB;AAAA,UACpB,cAAc;AAAA,UACd,sBAAsB;AAAA,QACvB;AAAA,MACF;AAAA,MACD,SAAS,CAAC,qBAAqB,+BAA+B,2BAA2B,sBAAsB;AAAA,IAChH;AAAA,EACF;AAAA;AAAA,EAGD,kBAAkB;AAAA,IAChB,OAAO;AAAA,MACL;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,QAC/B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACnB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,OAAO,MAAM,IAAI;AAAA,QAChC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACnB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,KAAK,GAAG;AAAA,QAC7B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACnB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,MAAM,GAAG;AAAA,QAC9B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACnB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,OAAO,IAAI;AAAA,QAChC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACnB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,QAC/B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACnB;AAAA,IACF;AAAA,IAED,cAAc;AAAA,MACZ;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,KAAK,MAAM,IAAI;AAAA,QAC9B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACnB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,KAAK,IAAI;AAAA,QAC9B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACnB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,QAC/B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACnB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,MAAM,GAAG;AAAA,QAC9B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACnB;AAAA,IACF;AAAA,IAED,UAAU;AAAA,MACR;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,MAAM,GAAG;AAAA,QAC9B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACnB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,QAC/B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACnB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,CAAC,MAAM,MAAM,MAAM,KAAK;AAAA,QAChC,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGD,oBAAoB;AAAA,IAClB,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,aAAa;AAAA,MACb,OAAO;AAAA,MACP,MAAM;AAAA,MACN,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,mBAAmB;AAAA,IACpB;AAAA,IACD,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,OAAO;AAAA,MACP,MAAM;AAAA,MACN,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,mBAAmB;AAAA,IACpB;AAAA,IACD,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,aAAa;AAAA,MACb,OAAO;AAAA,MACP,MAAM;AAAA,MACN,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,mBAAmB;AAAA,IACpB;AAAA,EACF;AAAA;AAAA,EAwDD,eAAe;AAAA,IACb,oBAAoB;AAAA,MAClB,MAAM,EAAE,MAAM,IAAI,OAAO,GAAG,YAAY,EAAG;AAAA,MAC3C,QAAQ,EAAE,MAAM,IAAI,OAAO,GAAG,YAAY,EAAG;AAAA,MAC7C,MAAM,EAAE,MAAM,IAAI,OAAO,IAAI,YAAY,EAAG;AAAA,IAShD;AAAA,EAAA;AA4CF;AAGO,MAAM,qBAAqB;AAAA;AAAA,EAEhC,UAAU,qBAAqB,iBAAiB;AAAA,EAChD,cAAc;AAAA,IACZ,EAAE,IAAI,QAAQ,MAAM,SAAS,QAAQ,GAAG,UAAU,EAAG;AAAA,IACrD,EAAE,IAAI,UAAU,MAAM,SAAS,QAAQ,GAAG,UAAU,EAAG;AAAA,IACvD,EAAE,IAAI,QAAQ,MAAM,WAAW,QAAQ,GAAG,UAAU,EAAG;AAAA,EACxD;AAAA,EACD,qBAAqB;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EACD,cAAc,qBAAqB;AAErC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrdA,MAAM,iBAAiB;AAAA,EACrB,gBAAgB;AAAA,IACd,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,WAAW;AAAA,EACb;AAAA,EACA,gBAAgB;AAAA,IACd,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,WAAW;AAAA,EACb;AAAA,EACA,gBAAgB;AAAA,IACd,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,WAAW;AAAA,EAAA;AAGf;AAEA,SAAS,iBAAiB,EAAE,UAAU;AACpC,QAAM,EAAE,MAAM,aAAa,KAAK,IAAIA,aAAAA,WAAW,aAAa;AACtD,QAAA,EAAE,SAAS,IAAI,wBAAwB;AAI1BC,eAAAA,OAAO,IAAI;AAG9B,QAAM,CAAC,WAAW,YAAY,IAAIC,sBAAS;AAAA,IACzC,QAAQ;AAAA;AAAA,IACR,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,gBAAgB;AAAA;AAAA,IAGhB,iBAAiB,eAAe,eAAe;AAAA,IAC/C,eAAe;AAAA,MACb,eAAe,eAAe;AAAA,MAC9B,eAAe,eAAe;AAAA,MAC9B,eAAe,eAAe;AAAA,IAChC;AAAA,IACA,eAAe;AAAA,IACf,mBAAmB;AAAA;AAAA,IACnB,oBAAoB;AAAA,IACpB,qBAAqB,CAAC;AAAA;AAAA,IAGtB,cAAc;AAAA,MACZ,cAAc;AAAA,QACZ,cAAc,CAAC;AAAA,QACf,cAAc,CAAC;AAAA,QACf,kBAAkB;AAAA,MACpB;AAAA,MACA,gBAAgB;AAAA,QACd,aAAa;AAAA,QACb,WAAW;AAAA,QACX,eAAe;AAAA,MACjB;AAAA,MACA,iBAAiB;AAAA,QACf,UAAU,CAAC;AAAA,QACX,iBAAiB,CAAC;AAAA,QAClB,gBAAgB;AAAA,MAClB;AAAA,MACA,cAAc;AAAA,QACZ,QAAQ,CAAC;AAAA,QACT,cAAc,CAAC;AAAA,QACf,YAAY,CAAA;AAAA,MACd;AAAA,MACA,gBAAgB;AAAA,QACd,WAAW;AAAA,QACX,eAAe;AAAA,QACf,eAAe;AAAA,MACjB;AAAA,MACA,kBAAkB;AAAA,QAChB,iBAAiB,CAAC;AAAA,QAClB,cAAc,CAAC;AAAA,QACf,gBAAgB,CAAA;AAAA,MAAC;AAAA,IAErB;AAAA;AAAA,IAGA,cAAc;AAAA,IACd,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,iBAAiB;AAAA;AAAA,IAGjB,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,oBAAoB;AAAA,IACpB,eAAe;AAAA,IACf,iBAAiB;AAAA;AAAA,IAGjB,iBAAiB,CAAC;AAAA,IAClB,cAAc;AAAA,IACd,YAAY;AAAA;AAAA,IAGZ,UAAU;AAAA;AAAA,IAGV,WAAW;AAAA,MACT,OAAO;AAAA,MACP,WAAW;AAAA,MACX,eAAe;AAAA,MACf,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA;AAAA,IAGA,eAAe;AAAA,MACb,oBAAoB,CAAC;AAAA,MACrB,eAAe,CAAC;AAAA,MAChB,qBAAqB;AAAA,QACnB,kBAAkB,CAAC;AAAA,QACnB,iBAAiB;AAAA,QACjB,oBAAoB,CAAA;AAAA,MACtB;AAAA,MACA,uBAAuB;AAAA,QACrB,yBAAyB,CAAC;AAAA,QAC1B,iBAAiB,CAAC;AAAA,QAClB,mBAAmB,CAAA;AAAA,MACrB;AAAA,MACA,sBAAsB;AAAA,QACpB,kBAAkB;AAAA,QAClB,oBAAoB;AAAA,QACpB,kBAAkB,CAAA;AAAA,MAAC;AAAA,IAEvB;AAAA;AAAA,IAGA,mBAAmB;AAAA,MACjB,cAAc,CAAC;AAAA,MACf,UAAU,CAAC;AAAA,MACX,eAAe;AAAA,MACf,mBAAmB,CAAC;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,wBAAwB,CAAC;AAAA,MACzB,kBAAkB,CAAA;AAAA,IAAC;AAAA,EACrB,CACD;AAGoBD,eAAAA,OAAO,IAAI;AACNA,eAAAA,OAAO,IAAI;AAG/B,QAAA;AAAA,IACJ;AAAA,IAGA;AAAA;AAAA,IACA;AAAA,EAEF,IAAI,oBAAoB,cAAc;AAGtC,QAAM,CAAC,aAAa,IAAIC,sBAAS,MAAM,IAAI,2BAA2B;AAGtE,QAAM,CAACC,YAAW,YAAY,IAAID,sBAAS,MAAM;AACzC,UAAA,QAAQ,aAAa,QAAQ,wBAAwB;AAC3D,WAAO,QAAQ,KAAK,MAAM,KAAK,IAAI;AAAA,EAAA,CACpC;AAGK,QAAA;AAAA,IACJ,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,IAInB,eAAe;AAAA,EAAA,IACb,2BAA2B,WAAW;AAAA,IAaxC,eAAe,MAAM,SAAS,iBAAiB;AAAA,EAAA,CAChD;AAG+B,6BAA2B,CAM3D,CAAC;AAED,QAAM,CAAC,0BAA0B,2BAA2B,IAAIA,aAAAA,SAAS,KAAK;AAC9E,QAAM,CAAC,aAAa,cAAc,IAAIA,aAAAA,SAAS,KAAK;AACpD,QAAM,CAAC,iBAAiB,kBAAkB,IAAIA,aAAAA,SAAS,IAAI;AAC3D,QAAM,CAACE,YAAW,YAAY,IAAIF,sBAAS;AAAA,IACzC,OAAO;AAAA,IACP,OAAO;AAAA,IACP,WAAW;AAAA,IACX,eAAe;AAAA,IACf,UAAU;AAAA,EAAA,CACX;AACD,QAAM,CAAC,YAAY,aAAa,IAAIA,aAAAA,SAAS,MAAM;AACnD,QAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAS,IAAI;AACrD,QAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAS,CAAA,CAAE;AACnD,QAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAS,CAAA,CAAE;AACnD,QAAM,CAAC,YAAY,aAAa,IAAIA,aAAAA,SAAS,KAAK;AAClD,QAAM,CAAC,gBAAgB,iBAAiB,IAAIA,aAAAA,SAAS,IAAI;AACzD,QAAM,CAAC,UAAU,WAAW,IAAIA,aAAAA,SAAS,IAAI;AAG7C,QAAM,QAAQG,aAAY,YAAA,CAAC,MAAM,UAAU,CAAA,MAAO;AAChD,QAAI,CAACF,cAAa,EAAE,qBAAqB,SAAS;AAChD;AAAA,IAAA;AAGF,WAAO,gBAAgB,OAAO;AAExB,UAAA,YAAY,IAAI,yBAAyB,IAAI;AACnD,cAAU,OAAO;AACP,cAAA,OAAO,QAAQ,QAAQ;AACvB,cAAA,QAAQ,QAAQ,SAAS;AACzB,cAAA,SAAS,QAAQ,UAAU;AAE9B,WAAA,gBAAgB,MAAM,SAAS;AAAA,EAAA,GACrC,CAACA,UAAS,CAAC;AAKd,QAAM,0BAA0BE,aAAAA,YAAY,CAAC,YAAYC,gBAAe;AACtE,UAAM,SAAS,qBAAqB,mBAAmBA,YAAW,aAAa;AAC/E,UAAM,iBAAiB,qBAAqB,gBAAgB,WAAW,aAAa;AAEpF,YAAQ,YAAY;AAAA,MAClB,KAAK;AACI,eAAA,8BAA8B,QAAQ,cAAc;AAAA,MAC7D,KAAK;AACI,eAAA,8BAA8B,QAAQ,cAAc;AAAA,MAC7D,KAAK;AACI,eAAA,8BAA8B,QAAQ,cAAc;AAAA,MAC7D;AACS,eAAA,8BAA8B,QAAQ,cAAc;AAAA,IAAA;AAAA,EAEjE,GAAG,EAAE;AAOL,QAAM,gCAAgCD,aAAAA,YAAY,CAAC,kBAAkB,mBAAmB;AACtF,YAAQ,IAAI,uCAAuC;AAEnD,UAAM,iBAAiB,qBAAqB,iBAAiB,kBAAkB,qBAAqB,OAAO;AACrG,UAAA,gBAAgB,eAAe,KAAK,MAAM,KAAK,OAAO,IAAI,eAAe,MAAM,CAAC;AAEtF,UAAM,cAAc;AAAA,MAClB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAEM,UAAA,gBAAgB,UAAU,cAAc;AACxC,UAAA,aAAa,YAAY,aAAa,KAAK;AACjD,UAAM,gBAAgB,cAAc,OAAO,MAAM,GAAG,UAAU;AACxD,UAAA,mBAAmB,yBAAyB,eAAe,CAAC;AAE3D,WAAA;AAAA,MACL,SAAS;AAAA,MACT;AAAA,MACA,iBAAiB,CAAC,GAAG,eAAe,GAAG,gBAAgB,EAAE,KAAK,MAAM,KAAK,OAAO,IAAI,GAAG;AAAA,MACvF,aAAa;AAAA,MACb,aAAa,oCAAoC,cAAc,IAAI;AAAA,MACnE,OAAO,UAAU;AAAA,MACjB,cAAc;AAAA,MACd,YAAY,KAAK,KAAK,KAAK,KAAK,UAAU,CAAC;AAAA,IAC7C;AAAA,EAAA,GACC,CAAC,UAAU,UAAU,CAAC;AAGzB,QAAM,gCAAgCA,aAAAA,YAAY,CAAC,kBAAkB,mBAAmB;AACtF,YAAQ,IAAI,uCAAuC;AAEnD,UAAM,iBAAiB,qBAAqB,iBAAiB,kBAAkB,qBAAqB,OAAO;AACrG,UAAA,gBAAgB,eAAe,KAAK,MAAM,KAAK,OAAO,IAAI,eAAe,MAAM,CAAC;AAEtF,UAAM,iBAAiB;AAAA,MACrB,MAAM,CAAC,IAAI,GAAG;AAAA,MACd,QAAQ,CAAC,IAAI,KAAK,GAAG;AAAA,MACrB,MAAM,CAAC,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,IACxC;AAEA,UAAM,SAAS,cAAc,OAAO,MAAM,GAAG,CAAC;AACxC,UAAA,gBAAgB,UAAU,cAAc;AAC9C,UAAM,kBAAkB,eAAe,aAAa,KAAK,eAAe;AAExE,UAAM,gBAAgB,OAAO,IAAI,CAAC,OAAO,WAAW;AAAA,MAClD,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,OAAO,gBAAgB,KAAK,MAAM,KAAK,OAAO,IAAI,gBAAgB,MAAM,CAAC;AAAA,MACzE,QAAQ;AAAA,MACR,iBAAiB;AAAA,IAAA,EACjB;AAEK,WAAA;AAAA,MACL,SAAS;AAAA,MACT;AAAA,MACA,aAAa,2CAA2C,cAAc,IAAI;AAAA,MAC1E,OAAO,UAAU;AAAA,MACjB,cAAc;AAAA,MACd,oBAAoB;AAAA,IACtB;AAAA,EAAA,GACC,CAAC,UAAU,UAAU,CAAC;AAGzB,QAAM,gCAAgCA,aAAAA,YAAY,CAAC,kBAAkB,mBAAmB;AACtF,YAAQ,IAAI,uCAAuC;AAEnD,UAAM,eAAe;AAAA,MACnB,MAAM;AAAA,QACJ,EAAE,MAAM,eAAe,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI,GAAG,SAAS,GAAG,SAAS,KAAK;AAAA,QACrF,EAAE,MAAM,cAAc,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI,GAAG,SAAS,GAAG,SAAS,KAAK;AAAA,MACtF;AAAA,MACA,QAAQ;AAAA,QACN,EAAE,MAAM,eAAe,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI,GAAG,SAAS,GAAG,SAAS,KAAK;AAAA,QACrF,EAAE,MAAM,mBAAmB,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI,GAAG,SAAS,GAAG,SAAS,KAAK;AAAA,MAC3F;AAAA,MACA,MAAM;AAAA,QACJ,EAAE,MAAM,mBAAmB,UAAU,CAAC,MAAM,MAAM,MAAM,MAAM,IAAI,GAAG,SAAS,GAAG,SAAS,KAAK;AAAA,QAC/F,EAAE,MAAM,mBAAmB,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI,GAAG,SAAS,GAAG,SAAS,KAAK;AAAA,MAAA;AAAA,IAE7F;AAEM,UAAA,gBAAgB,UAAU,cAAc;AAC9C,UAAM,gBAAgB,aAAa,aAAa,KAAK,aAAa;AAC5D,UAAA,kBAAkB,cAAc,KAAK,MAAM,KAAK,OAAO,IAAI,cAAc,MAAM,CAAC;AAGhF,UAAA,eAAe,CAAC,MAAM,MAAM,MAAM,IAAI,EAAE,OAAO,CAAA,QAAO,QAAQ,gBAAgB,OAAO;AAC3F,UAAM,UAAU,CAAC,gBAAgB,SAAS,GAAG,aAAa,MAAM,GAAG,CAAC,CAAC,EAAE,KAAK,MAAM,KAAK,OAAA,IAAW,GAAG;AAE9F,WAAA;AAAA,MACL,SAAS;AAAA,MACT;AAAA,MACA,aAAa;AAAA,MACb,OAAO,UAAU;AAAA,MACjB,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV;AAAA,EAAA,GACC,CAAC,UAAU,UAAU,CAAC;AAKzB,QAAM,2BAA2BA,aAAAA,YAAY,CAAC,eAAe,UAAU;AAC/D,UAAA,YAAY,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,OAAO,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,IAAI;AAC9I,UAAA,cAAc,UAAU,OAAO,CAAA,UAAS,CAAC,cAAc,SAAS,KAAK,CAAC;AACrE,WAAA,YAAY,KAAK,MAAM,KAAK,WAAW,GAAG,EAAE,MAAM,GAAG,KAAK;AAAA,EACnE,GAAG,EAAE;AAG6BA,eAAY,YAAA,CAACE,cAAa;AAC1D,UAAM,iBAAiB;AAAA,MACrB,UAAU,EAAE,MAAM,qBAAqB,MAAM,MAAM,QAAQ,GAAG;AAAA,MAC9D,UAAU,EAAE,MAAM,qBAAqB,MAAM,MAAM,QAAQ,GAAG;AAAA,MAC9D,SAAS,EAAE,MAAM,mBAAmB,MAAM,MAAM,QAAQ,GAAG;AAAA,MAC3D,SAAS,EAAE,MAAM,oBAAoB,MAAM,MAAM,QAAQ,CAAG,EAAA;AAAA,IAC9D;AAEM,UAAA,qBAAqB,OAAO,KAAK,cAAc,EAAE,MAAM,GAAGA,UAAS,cAAc,CAAC;AACxF,UAAM,SAAS,CAAC;AAChB,uBAAmB,QAAQ,CAAO,QAAA;AACzB,aAAA,GAAG,IAAI,eAAe,GAAG;AAAA,IAAA,CACjC;AAEM,WAAA;AAAA,EAAA,GACN,CAAE,CAAA;AAEmCF,2BAAY,CAAC,YAAYE,cAAa;AACtE,UAAA,eAAe,OAAO,KAAK,UAAU;AAC3C,UAAM,SAAS,CAAC;AAEhB,UAAM,aAAa;AAAA,MACjB,UAAU,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,MAC7C,UAAU,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,MAC7C,SAAS,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,MAC5C,SAAS,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,IAC9C;AAEA,iBAAa,QAAQ,CAAe,gBAAA;AAClC,YAAM,iBAAiB,WAAW,WAAW,KAAK,CAAC;AACnD,YAAM,iBAAiB,eAAe,MAAM,GAAGA,UAAS,qBAAqB,CAAC;AAC9E,aAAO,KAAK,GAAG,eAAe,IAAI,CAAU,WAAA,EAAE,OAAO,UAAU,aAAa,OAAO,MAAM,EAAE,CAAC;AAAA,IAAA,CAC7F;AAED,WAAO,OAAO,KAAK,MAAM,KAAK,OAAA,IAAW,GAAG;AAAA,EAAA,GAC3C,CAAE,CAAA;AAEmCF,2BAAY,CAAC,YAAYE,cAAa;AACtE,UAAA,eAAe,OAAO,KAAK,UAAU;AAC3C,UAAM,SAAS,CAAC;AAEhB,iBAAa,QAAQ,CAAe,gBAAA;AAC5B,YAAA,iBAAiB,kBAAkB,WAAW;AACpD,YAAM,iBAAiB,eAAe,MAAM,GAAGA,UAAS,iBAAiB;AAClE,aAAA,KAAK,GAAG,eAAe,IAAI,CAAA,WAAU,EAAE,OAAO,UAAU,YAAY,EAAE,CAAC;AAAA,IAAA,CAC/E;AAED,WAAO,OAAO,KAAK,MAAM,KAAK,OAAA,IAAW,GAAG;AAAA,EAAA,GAC3C,CAAE,CAAA;AAEC,QAAA,oBAAoBF,yBAAY,CAAC,aAAa;AAClD,UAAM,cAAc;AAAA,MAClB,UAAU,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,MAC7C,QAAQ,CAAC,MAAM,OAAO,MAAM,OAAO,MAAM,IAAI;AAAA,MAC7C,SAAS,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,MAC5C,SAAS,CAAC,KAAK,KAAK,MAAM,KAAK,MAAM,GAAG;AAAA,IAC1C;AACO,WAAA,YAAY,QAAQ,KAAK,CAAC;AAAA,EACnC,GAAG,EAAE;AAE0BA,eAAY,YAAA,CAACE,cAAa;AACvD,UAAM,eAAeA,UAAS;AACxB,UAAA,eAAe,aAAa,KAAK,MAAM,KAAK,OAAO,IAAI,aAAa,MAAM,CAAC;AAEjF,YAAQ,cAAc;AAAA,MACpB,KAAK;AACI,eAAA,EAAE,MAAM,eAAe,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI,GAAG,MAAM,KAAK;AAAA,MAC/E,KAAK;AACI,eAAA,EAAE,MAAM,cAAc,UAAU,CAAC,MAAM,MAAM,MAAM,KAAK,GAAG,MAAM,KAAK;AAAA,MAC/E,KAAK;AACI,eAAA,EAAE,MAAM,eAAe,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI,GAAG,MAAM,KAAK;AAAA,MAC/E;AACS,eAAA,EAAE,MAAM,eAAe,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI,GAAG,MAAM,KAAK;AAAA,IAAA;AAAA,EACjF,GACC,CAAE,CAAA;AAE0BF,2BAAY,CAAC,SAASE,cAAa;AAChE,UAAM,gBAAgB,QAAQ;AACxB,UAAA,eAAe,CAAC,MAAM,MAAM,MAAM,IAAI,EAAE,OAAO,CAAO,QAAA,QAAQ,aAAa;AACjF,UAAM,gBAAgB,aAAa,MAAM,GAAGA,UAAS,oBAAoB,CAAC;AAEnE,WAAA,CAAC,eAAe,GAAG,aAAa,EAAE,KAAK,MAAM,KAAK,OAAO,IAAI,GAAG;AAAA,EAAA,GACtE,CAAE,CAAA;AAKwBF,eAAAA,YAAY,MAAM;AAC7C,iBAAa,CAAQ,SAAA;AACnB,YAAM,aAAa,KAAK,gBAAgB,KAAK,KAAK,cAAc;AAC1D,YAAA,eAAe,KAAK,cAAc,SAAS;AAGjD,UAAI,+BAA+B;AACjC,sCAA8B,qBAAqB;AAAA,UACjD,MAAM,KAAK;AAAA,UACX,IAAI;AAAA,UACJ,WAAW;AAAA,UACX,OAAO,KAAK;AAAA,QAAA,CACb;AAAA,MAAA;AAGH,YAAM,WAAW;AAAA,QACf,GAAG;AAAA,QACH,iBAAiB;AAAA,QACjB,eAAe;AAAA,QACf,oBAAoB;AAAA,QACpB,OAAO,KAAK,QAAQ;AAAA,QACpB,gBAAgB,KAAK,IAAI;AAAA,MAC3B;AAGA,YAAM,kBAAkB,wBAAwB,SAAS,iBAAiB,SAAS,UAAU;AAGpF,eAAA,iBAAiB,gBAAgB,WAAW;AACrD,eAAS,eAAe,gBAAgB,mBAAmB,gBAAgB,UAAU,CAAC;AAC7E,eAAA,eAAe,IAAI,MAAM,gBAAgB,eAAe,CAAC,EAAE,KAAK,IAAI;AAG7E,YAAM,iBAAiB,qBAAqB,gBAAgB,aAAa,aAAa;AAChF,YAAA,eAAe,gBAAgB,QAAQ;AAE7C,iBAAW,MAAM;AACf,YAAI,OAAO;AACH,gBAAA,mBAAmB,YAAY,KAAK,gBAAgB,WAAW,IAAI,EAAE,MAAM,KAAK;AAAA,QAAA;AAAA,SAEvF,GAAG;AAEC,aAAA;AAAA,IAAA,CACR;AAAA,EAAA,GACA,CAAC,yBAAyB,+BAA+B,KAAK,CAAC;AAG5D,QAAA,iBAAiBA,yBAAY,CAAC,eAAe;AACjD,iBAAa,CAAQ,SAAA;AACnB,YAAM,gBAAgB,KAAK,cAAc,QAAQ,UAAU;AACvD,UAAA,kBAAkB,GAAW,QAAA;AAEjC,YAAM,iBAAiB,qBAAqB,gBAAgB,WAAW,aAAa;AAC9E,YAAA,eAAe,gBAAgB,QAAQ;AAG7C,UAAI,+BAA+B;AACjC,sCAA8B,mBAAmB;AAAA,UAC/C,MAAM,KAAK;AAAA,UACX,IAAI;AAAA,UACJ,QAAQ;AAAA,UACR,OAAO,KAAK;AAAA,QAAA,CACb;AAAA,MAAA;AAGH,YAAM,WAAW;AAAA,QACf,GAAG;AAAA,QACH,iBAAiB;AAAA,QACjB;AAAA,QACA,oBAAoB;AAAA,QACpB,OAAO,KAAK,QAAQ;AAAA,QACpB,gBAAgB,KAAK,IAAI;AAAA,MAC3B;AAGA,YAAM,kBAAkB,wBAAwB,SAAS,iBAAiB,SAAS,UAAU;AAEpF,eAAA,iBAAiB,gBAAgB,WAAW;AACrD,eAAS,eAAe,gBAAgB,mBAAmB,gBAAgB,UAAU,CAAC;AAC7E,eAAA,eAAe,IAAI,MAAM,gBAAgB,eAAe,CAAC,EAAE,KAAK,IAAI;AAG7E,iBAAW,MAAM;AACf,YAAI,OAAO;AACH,gBAAA,4BAA4B,YAAY,KAAK,gBAAgB,WAAW,IAAI,EAAE,MAAM,KAAK;AAAA,QAAA;AAAA,SAEhG,GAAG;AAEC,aAAA;AAAA,IAAA,CACR;AAAA,EACA,GAAA,CAAC,yBAAyB,+BAA+B,KAAK,CAAC;AAGzCA,eAAAA,YAAY,MAAM;AACzC,YAAQ,IAAI,gCAAgC;AAAA,MAC1C,iBAAiB,UAAU;AAAA,MAC3B,oBAAoB,UAAU;AAAA,MAC9B,OAAO,UAAU;AAAA,IAAA,CAClB;AAED,iBAAa,CAAQ,SAAA;AAEb,YAAA,uBAAuB,KAAK,sBAAsB,KAAK;AAE7D,cAAQ,IAAI,wCAAwC;AAAA,QAClD;AAAA,QACA,oBAAoB,KAAK;AAAA,QACzB,mBAAmB,KAAK;AAAA,MAAA,CACzB;AAEG,UAAA,WAAW,EAAE,GAAG,KAAK;AAEzB,UAAI,sBAAsB;AAExB,cAAM,qBAAqB,KAAK,gBAAgB,KAAK,KAAK,cAAc;AAClE,cAAA,eAAe,KAAK,cAAc,iBAAiB;AAEzD,gBAAQ,IAAI,wCAAwC;AAAA,UAClD,MAAM,KAAK;AAAA,UACX,IAAI;AAAA,UACJ;AAAA,QAAA,CACD;AAEU,mBAAA;AAAA,UACT,GAAG;AAAA,UACH,iBAAiB;AAAA,UACjB,eAAe;AAAA,UACf,oBAAoB;AAAA,UACpB,qBAAqB,KAAK,sBAAsB;AAAA,QAClD;AAGA,cAAM,iBAAiB,qBAAqB,gBAAgB,aAAa,aAAa;AAChF,cAAA,eAAe,gBAAgB,QAAQ;AAC7C,mBAAW,MAAM;AACf,cAAI,OAAO;AACH,kBAAA,mBAAmB,YAAY,IAAI,EAAE,OAAO,KAAK,MAAM,KAAK;AAAA,UAAA;AAAA,WAEnE,GAAG;AAAA,MAAA;AAIR,YAAM,kBAAkB,wBAAwB,SAAS,iBAAiB,SAAS,UAAU;AAE7F,cAAQ,IAAI,sCAAsC;AAAA,QAChD,UAAU,SAAS;AAAA,QACnB,SAAS;AAAA,MAAA,CACV;AAGQ,eAAA,iBAAiB,gBAAgB,WAAW,SAAS;AAC9D,eAAS,eAAe,gBAAgB,mBAAmB,gBAAgB,UAAU,CAAC;AAC7E,eAAA,eAAe,IAAI,MAAM,gBAAgB,eAAe,CAAC,EAAE,KAAK,IAAI;AAC7E,eAAS,aAAa;AACtB,eAAS,eAAe;AACxB,eAAS,WAAW;AACpB,eAAS,eAAe;AAGf,eAAA,qBAAqB,SAAS,qBAAqB;AACnD,eAAA,QAAQ,SAAS,QAAQ;AACzB,eAAA,iBAAiB,KAAK,IAAI;AAE5B,aAAA;AAAA,IAAA,CACR;AAAA,EAAA,GACA,CAAC,UAAU,iBAAiB,UAAU,oBAAoB,UAAU,OAAO,yBAAyB,KAAK,CAAC;AA6W7G,QAAM,sBAAsB,MAAM;AAE5B,QAAA,CAAC,kBAAkB,aAAa;AAChB,wBAAA;AAClB,aACGG,4CAAA,OAAA,EAAI,WAAW,OAAO,cACrB,UAACA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,gBACrB,UAACA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,eAAe,UAArC,gCAAA,QAAA,OAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA,GAAA,IAA+D,EADjE,GAAA,QAAA,OAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA,GAAA,IAEA,EAHF,GAAA,QAAA,OAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAIA,GAAA,IAAA;AAAA,IAAA;AAIJ,WACGA,qCAAAA,OAAA,OAAA,EAAI,WAAW,OAAO,cACrB,UAAA;AAAA,MAACA,qCAAAA,OAAA,OAAA,EAAI,WAAW,OAAO,gBACrB,sDAAC,MAAG,EAAA,WAAW,OAAO,eAAe,UAAA;AAAA,QAAA;AAAA,QAAoB,gBAAgB;AAAA,MAAA,EAAzE,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA,GAAA,IAA8E,EADhF,GAAA,QAAA,OAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAEA,GAAA,IAAA;AAAA,MAEC,kBAGGA,qCAAA,OAAAC,+BAAA,EAAA,UAAA;AAAA,QAACD,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,gBACrB,UAAA;AAAA,UAAAA,4CAAC,SAAI,OAAO;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,cAAc;AAAA,YACd,iBAAiB;AAAA,YACjB,UAAU;AAAA,YACV,WAAW;AAAA,YACX,cAAc;AAAA,UAEd,GAAA,UAAA;AAAA,YAACA,qCAAAA,OAAA,OAAA,EAAI,OAAO,EAAE,UAAU,QAAQ,cAAc,OAC3C,GAAA,UAAA,eAAe,MADlB,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAEA,GAAA,IAAA;AAAA,YACCA,qCAAA,OAAA,OAAA,EAAI,OAAO,EAAE,UAAU,UAAU,YAAY,QAAQ,OAAO,QAC1D,GAAA,UAAA,eAAe,KADlB,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EAfF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAgBA,GAAA,IAAA;AAAA,UAGCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,aACrB,UAAA;AAAA,YAAAA,4CAAC,OAAI,EAAA,WAAW,OAAO,YAAY,UAAnC,8CAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAEA,GAAA,IAAA;AAAA,YACAA,qCAAAA,OAAC,SAAI,WAAW,OAAO,YACpB,UAAa,aAAA,IAAI,CAAC,OAAO,UACxBA,qCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBAEC,WAAW,GAAG,OAAO,UAAU,IAAI,QAAQ,OAAO,SAAS,OAAO,KAAK;AAAA,gBACvE,YAAY,CAAC,MAAM,EAAE,eAAe;AAAA,gBACpC,QAAQ,MAAM,WAAW,KAAK;AAAA,gBAC9B,OAAO;AAAA,kBACL,iBAAiB,eAAe,QAAQ;AAAA,kBACxC,QAAQ,QAAQ,sBAAsB;AAAA,gBACxC;AAAA,gBAEC,UAAA;AAAA,kBAAA,SACEA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,aACrB,UAAA;AAAA,oBAACA,qCAAAA,OAAA,OAAA,EAAI,OAAO,EAAE,UAAU,UAAU,cAAc,SAC7C,GAAA,UAAA,MAAM,QADT,GAAA,QAAA,OAAA;AAAA,sBAAA,UAAA;AAAA,sBAAA,YAAA;AAAA,sBAAA,cAAA;AAAA,oBAEA,GAAA,IAAA;AAAA,oBACAA,qCAAA;AAAA,sBAAC;AAAA,sBAAA;AAAA,wBACC,WAAW,OAAO;AAAA,wBAClB,SAAS,MAAM,kBAAkB,KAAK;AAAA,wBACtC,OAAM;AAAA,wBACP,UAAA;AAAA,sBAAA;AAAA,sBAJD;AAAA,sBAAA;AAAA,sBAAA;AAAA,wBAAA,UAAA;AAAA,wBAAA,YAAA;AAAA,wBAAA,cAAA;AAAA,sBAAA;AAAA,sBAAA;AAAA,oBAAA;AAAA,kBAMA,EAVF,GAAA,QAAA,MAAA;AAAA,oBAAA,UAAA;AAAA,oBAAA,YAAA;AAAA,oBAAA,cAAA;AAAA,kBAWA,GAAA,IAAA;AAAA,kBAED,CAAC,SACAA,qCAAAA,OAAC,SAAI,WAAW,OAAO,eAAe,UAAtC,2BAAA,QAAA,OAAA;AAAA,oBAAA,UAAA;AAAA,oBAAA,YAAA;AAAA,oBAAA,cAAA;AAAA,kBAAA,GAEA,IAAA;AAAA,gBAAA;AAAA,cAAA;AAAA,cA1BG;AAAA,cADP;AAAA,cAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA;AAAA,cAAA;AAAA,YAAA,CA8BD,EAhCH,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAiCA,IAAA;AAAA,UAAA,EArCF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAsCA,IAAA;AAAA,QAAA,EA1DF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QA2DA,GAAA,IAAA;AAAA,QAGAA,qCAAAA,OAAC,OAAI,EAAA,WAAW,OAAO,eACpB,UAAa,aAAA,SAAS,IAAI,aAAa,IAAI,CAAC,UAAU;AACrD,gBAAM,SAAS,MAAM;AAGnB,iBAAAA,qCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cAEC,WAAW,GAAG,OAAO,YAAY,IAAI,SAAS,OAAO,OAAO,EAAE;AAAA,cAC9D,WAAW,CAAC;AAAA,cACZ,aAAa,MAAM,CAAC,UAAU,gBAAgB,KAAK;AAAA,cACnD,OAAO;AAAA,gBACL,SAAS,SAAS,MAAM;AAAA,gBACxB,QAAQ,SAAS,gBAAgB;AAAA,gBACjC,eAAe,SAAS,SAAS;AAAA,cACnC;AAAA,cAEA,UAAA;AAAA,gBAAAA,4CAAC,SAAI,OAAO;AAAA,kBACV,UAAU;AAAA,kBACV,cAAc;AAAA,gBAChB,GACG,gBAAM,QAJT,GAAA,QAAA,OAAA;AAAA,kBAAA,UAAA;AAAA,kBAAA,YAAA;AAAA,kBAAA,cAAA;AAAA,gBAKA,GAAA,IAAA;AAAA,gBACAA,4CAAC,SAAI,OAAO;AAAA,kBACV,UAAU;AAAA,kBACV,YAAY;AAAA,kBACZ,OAAO,SAAS,0BAA0B;AAAA,gBAAA,GAEzC,UAAS,SAAA,UAAU,UALtB,GAAA,QAAA,OAAA;AAAA,kBAAA,UAAA;AAAA,kBAAA,YAAA;AAAA,kBAAA,cAAA;AAAA,gBAAA,GAMA,IAAA;AAAA,cAAA;AAAA,YAAA;AAAA,YAtBK,MAAM;AAAA,YADb;AAAA,YAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA;AAAA,YAAA;AAAA,UAwBA;AAAA,QAEH,CAAA,IACCA,qCAAA,OAAC,SAAI,WAAW,OAAO,UAAU,UAAjC,yBAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAAA,IAEA,EAlCJ,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAoCA,IAAA;AAAA,MAAA,EApGF,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA,GAqGA,IAAA;AAAA,IAAA,EA3GJ,GAAA,QAAA,MAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IA6GA,GAAA,IAAA;AAAA,EAEJ;AAGA,QAAM,sBAAsB,MAAM;AAChC,WACGA,qCAAAA,OAAA,OAAA,EAAI,WAAW,OAAO,cACrB,UAAA;AAAA,MAACA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,gBACrB,sDAAC,MAAG,EAAA,WAAW,OAAO,eAAe,UAArC,sBAAA,GAAA,QAAA,OAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA,GAAA,IAAwD,EAD1D,GAAA,QAAA,OAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAEA,GAAA,IAAA;AAAA,MAGCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,gBACrB,UAAA;AAAA,QAAAA,4CAAC,SAAI,OAAO;AAAA,UACV,WAAW;AAAA,UACX,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,cAAc;AAAA,UACd,iBAAiB;AAAA,UACjB,UAAU;AAAA,UACV,WAAW;AAAA,QAEX,GAAA,UAAA;AAAA,UAACA,qCAAAA,OAAA,OAAA,EAAI,OAAO,EAAE,UAAU,QAAQ,cAAc,UAAU,UAAxD,KAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAEA,GAAA,IAAA;AAAA,UACAA,qCAAA,OAAC,OAAI,EAAA,OAAO,EAAE,UAAU,UAAU,YAAY,QAAQ,OAAO,QAAQ,GAAG,UAAxE,gBAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAEA,IAAA;AAAA,QAAA,EAdF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAeA,GAAA,IAAA;AAAA,QAGAA,4CAAC,SAAI,OAAO;AAAA,UACV,SAAS;AAAA,UACT,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,KAAK;AAAA,UACL,QAAQ;AAAA,QAER,GAAA,UAAA;AAAA,UAAAA,4CAAC,SAAI,OAAO;AAAA,YACV,UAAU;AAAA,YACV,WAAW;AAAA,YACX,OAAO;AAAA,aACN,UAJH,QAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAMA,GAAA,IAAA;AAAA,UACAA,4CAAC,SAAI,OAAO;AAAA,YACV,UAAU;AAAA,YACV,OAAO;AAAA,YACP,YAAY;AAAA,aACX,UAJH,sBAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAMA,IAAA;AAAA,QAAA,EApBF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAqBA,IAAA;AAAA,MAAA,EAxCF,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAyCA,GAAA,IAAA;AAAA,MAGCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,eACpB,UAAC,CAAA,GAAG,IAAI,KAAK,GAAG,EAAE,IAAI,CAAC,UAAU,UAChCA,qCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UAEC,WAAW,OAAO;AAAA,UAClB,SAAS,MAAM,oBAAoB,KAAK;AAAA,UACxC,cAAY,SAAS,QAAQ;AAAA,UAE7B,UAAA;AAAA,YAAAA,4CAAC,SAAI,OAAO;AAAA,cACV,UAAU;AAAA,cACV,cAAc;AAAA,cACd,WAAW,UAAU,QAAQ;AAAA,eAC5B,UAJH,QAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAMA,GAAA,IAAA;AAAA,YACAA,4CAAC,SAAI,OAAO,EAAE,UAAU,QAAQ,YAAY,OACzC,GAAA,UAAA;AAAA,cAAA;AAAA,cAAS;AAAA,YAAA,EADZ,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA;AAAA,QAAA;AAAA,QAdK;AAAA,QADP;AAAA,QAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA;AAAA,QAAA;AAAA,MAAA,CAiBD,EAnBH,GAAA,QAAA,OAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA,GAoBA,IAAA;AAAA,IAAA,EAtEF,GAAA,QAAA,MAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IAuEA,GAAA,IAAA;AAAA,EAEJ;AAGA,QAAM,sBAAsB,MAAM;AAChC,WACGA,qCAAAA,OAAA,OAAA,EAAI,WAAW,OAAO,cACrB,UAAA;AAAA,MAACA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,gBACrB,sDAAC,MAAG,EAAA,WAAW,OAAO,eAAe,UAArC,8BAAA,GAAA,QAAA,OAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA,GAAA,IAAgE,EADlE,GAAA,QAAA,OAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAEA,GAAA,IAAA;AAAA,MAGCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,gBACrB,UAAA;AAAA,QAAAA,4CAAC,SAAI,OAAO;AAAA,UACV,WAAW;AAAA,UACX,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,cAAc;AAAA,UACd,iBAAiB;AAAA,UACjB,UAAU;AAAA,UACV,WAAW;AAAA,QAEX,GAAA,UAAA;AAAA,UAAAA,4CAAC,SAAI,OAAO;AAAA,YACV,SAAS;AAAA,YACT,KAAK;AAAA,YACL,gBAAgB;AAAA,YAChB,YAAY;AAAA,YACZ,cAAc;AAAA,UAChB,GACG,UAAC,CAAA,MAAM,MAAM,MAAM,MAAM,GAAG,EAAE,IAAI,CAAC,MAAM,UACxCA,qCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cAEC,OAAO;AAAA,gBACL,UAAU;AAAA,gBACV,SAAS;AAAA,gBACT,QAAQ,UAAU,IAAI,sCAAsC;AAAA,gBAC5D,cAAc;AAAA,gBACd,iBAAiB,UAAU,IAAI,2BAA2B;AAAA,gBAC1D,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,gBAAgB;AAAA,cAClB;AAAA,cAEC,UAAA;AAAA,YAAA;AAAA,YAdI;AAAA,YADP;AAAA,YAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA;AAAA,YAAA;AAAA,UAAA,CAiBD,EAzBH,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UA0BA,GAAA,IAAA;AAAA,UACAA,qCAAA,OAAC,OAAI,EAAA,OAAO,EAAE,UAAU,UAAU,YAAY,QAAQ,OAAO,QAAQ,GAAG,UAAxE,oBAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAEA,IAAA;AAAA,QAAA,EAtCF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAuCA,GAAA,IAAA;AAAA,QAGAA,4CAAC,SAAI,OAAO;AAAA,UACV,SAAS;AAAA,UACT,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,KAAK;AAAA,UACL,QAAQ;AAAA,QAER,GAAA,UAAA;AAAA,UAAAA,4CAAC,SAAI,OAAO;AAAA,YACV,UAAU;AAAA,YACV,WAAW;AAAA,YACX,OAAO;AAAA,aACN,UAJH,QAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAMA,GAAA,IAAA;AAAA,UACAA,4CAAC,SAAI,OAAO;AAAA,YACV,UAAU;AAAA,YACV,OAAO;AAAA,YACP,YAAY;AAAA,aACX,UAJH,sBAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAMA,IAAA;AAAA,QAAA,EApBF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAqBA,IAAA;AAAA,MAAA,EAhEF,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAiEA,GAAA,IAAA;AAAA,MAGCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,eACpB,UAAC,CAAA,MAAM,MAAM,MAAM,IAAI,EAAE,IAAI,CAAC,QAAQ,UACrCA,qCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UAEC,WAAW,OAAO;AAAA,UAClB,SAAS,MAAM,oBAAoB,MAAM;AAAA,UACzC,cAAY,SAAS,MAAM;AAAA,UAE3B,UAAA;AAAA,YAACA,qCAAAA,OAAA,OAAA,EAAI,OAAO,EAAE,UAAU,UAAU,cAAc,YAC7C,UADH,OAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAEA,GAAA,IAAA;AAAA,wDACC,OAAI,EAAA,OAAO,EAAE,UAAU,QAAQ,YAAY,UACzC,UAAW,WAAA,OAAO,UAAU,WAAW,OAAO,WAAW,WAAW,OAAO,UAAU,OADxF,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA;AAAA,QAAA;AAAA,QAVK;AAAA,QADP;AAAA,QAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA;AAAA,QAAA;AAAA,MAAA,CAaD,EAfH,GAAA,QAAA,OAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA,GAgBA,IAAA;AAAA,IAAA,EA1FF,GAAA,QAAA,MAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IA2FA,GAAA,IAAA;AAAA,EAEJ;AASAE,eAAAA,UAAU,MAAM;AACd,WAAO,MAAM;AAEX,UAAI,qBAAqB,QAAQ;AAC/B,eAAO,gBAAgB,OAAO;AAAA,MAAA;AAAA,IAElC;AAAA,EACF,GAAG,EAAE;AAEL,QAAM,CAAC,iBAAiB,kBAAkB,IAAIR,aAAAA,SAAS,IAAI;AAC3D,QAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAS,CAAC;AAGlD,QAAM,CAAC,kBAAkB,mBAAmB,IAAIA,aAAAA,SAAS,IAAI;AAC7D,QAAM,CAAC,mBAAmB,oBAAoB,IAAIA,aAAAA,SAAS,CAAA,CAAE;AAC7D,QAAM,CAAC,mBAAmB,oBAAoB,IAAIA,aAAAA,SAAS,CAAA,CAAE;AAC7D,QAAM,CAAC,wBAAwB,yBAAyB,IAAIA,aAAAA,SAAS,YAAY;AAGjFQ,eAAAA,UAAU,MAAM;AACV,QAAA,aAAa,OAAO,cAAc,YAAY,UAAU,SAAS,KAAK,eAAe,CAAC,yBAAyB;AACjH,YAAM,2BAA2B,YAAY;AACvC,YAAA;AACF,gBAAM,iBAAiB;AACf,kBAAA,IAAI,yDAAyD,SAAS;AAAA,iBACvE,OAAO;AACN,kBAAA,MAAM,kDAAkD,KAAK;AAAA,QAAA;AAAA,MAEzE;AACyB,+BAAA;AAAA,IAAA;AAAA,KAE1B,CAAC,WAAW,aAAa,yBAAyB,gBAAgB,CAAC;AAGhE,QAAA,cAAcL,aAAAA,YAAY,MAAM;AAChC,QAAAD,WAAU,kBAAkB,EAAU,QAAA;AAC1C,WAAO,KAAK,MAAOA,WAAU,YAAYA,WAAU,gBAAiB,GAAG;AAAA,EAAA,GACtE,CAACA,UAAS,CAAC;AAGd,QAAM,uBAAuB,YAAY;AACjC,UAAA,cAAc,KAAK,IAAI;AACvB,UAAA,kBAAkB,mBAAmB,cAAc,mBAAmB;AAG5E,UAAM,eAAe;AAAA,MACnB,WAAW;AAAA,MACX,gBAAgBA,WAAU;AAAA,MAC1B,kBAAkBA,WAAU,gBAAgBA,WAAU;AAAA,MACtD,UAAU,YAAY;AAAA,MACtB,iBAAiB;AAAA,MACjB,iBAAiB,aAAa,MAAO,aAAa,SAAS,aAAa,SAAU;AAAA,IACpF;AAGA,UAAM,wBAAwB;AAAA,MAC5B,kBAAkB,0BAA0B;AAAA,MAC5C,wBAAwB,2BAA2B;AAAA,MACnD,oBAAoB,2BAA2B;AAAA,MAC/C,qBAAqB,2BAA2B;AAAA,MAChD;AAAA,MACA,qBAAqB,6BAA6B;AAAA,MAClD,kBAAkB,0BAA0B;AAAA,MAC5C,kBAAkB,sBAAsB;AAAA,MACxC,qBAAqB,2BAA2B;AAAA,IAClD;AAGA,UAAM,eAAe;AAAA,MACnB,GAAG;AAAA,MACH,GAAG;AAAA,IAAA,CACJ;AAGD,UAAM,uBAAuB;AAAA,MAC3B,UAAU;AAAA,MACV,aAAa;AAAA,QACX,GAAG;AAAA,QACH,GAAG;AAAA,QACH,cAAc;AAAA,QACd;AAAA,QACA,UAAU;AAAA,MACZ;AAAA,MACA,aAAa;AAAA,QACX,qBAAqB;AAAA,QACrB,oBAAoB,2BAA2B;AAAA,MAAA;AAAA,IACjD,CACD;AAAA,EACH;AAGA,QAAM,4BAA4B,MAAM;AACtC,UAAM,oBAAoB,kBAAkB,OAAO,CAAK,MAAA,EAAE,OAAO,EAAE;AACnE,UAAM,oBAAoB,kBAAkB;AAC5C,WAAO,oBAAoB,IAAK,oBAAoB,oBAAqB,MAAM;AAAA,EACjF;AAEA,QAAM,6BAA6B,MAAM;AACjC,UAAA,kBAAkB,kBAAkB,OAAO,CAAA,MAAK,EAAE,kBAAkB,KAAK,EAAE,OAAO,EAAE;AAC1F,UAAM,cAAc,aAAa;AACjC,WAAO,cAAc,IAAK,kBAAkB,cAAe,MAAM;AAAA,EACnE;AAEA,QAAM,6BAA6B,MAAM;AACnC,QAAA,kBAAkB,WAAW,EAAU,QAAA;AAE3C,UAAM,aAAa,kBAAkB,OAAO,CAAC,OAAO,aAAa;AAC/D,YAAM,QAAQ,KAAK,MAAM,QAAQ,KAAK,KAAK;AACpC,aAAA;AAAA,IACT,GAAG,EAAE;AAEL,WAAO,OAAO,KAAK,UAAU,EAAE,OAAO,CAAC,GAAG,MAAM,WAAW,CAAC,IAAI,WAAW,CAAC,IAAI,IAAI,CAAC;AAAA,EACvF;AAEA,QAAM,6BAA6B,MAAM;AACvC,UAAM,cAAc,kBAAkB,OAAO,CAAC,OAAO,aAAa,UAAU;AAC1E,UAAI,QAAQ,GAAG;AACP,cAAA,kBAAkB,kBAAkB,QAAQ,CAAC;AACnD,YAAI,YAAY,YAAY,gBAAgB,WACxC,YAAY,SAAS,MAAM,gBAAgB,SAAS,KACpD,YAAY,SAAS,MAAM,gBAAgB,SAAS,GAAG;AAChD,mBAAA;AAAA,QAAA;AAAA,MACX;AAEK,aAAA;AAAA,OACN,CAAC;AAEG,WAAA,KAAK,IAAI,aAAa,GAAG;AAAA,EAClC;AAEA,QAAM,+BAA+B,MAAM;AACzC,UAAM,oBAAoB,kBAAkB,OAAO,OAAK,CAAC,EAAE,OAAO,EAAE;AACpE,UAAM,gBAAgB,kBAAkB;AAEpC,QAAA,kBAAkB,EAAU,QAAA;AAGhC,WAAO,KAAK,IAAI,GAAG,MAAQ,oBAAoB,gBAAiB,GAAI;AAAA,EACtE;AAEA,QAAM,4BAA4B,MAAM;AACtC,UAAM,sBAAsB,KAAK,IAAI,GAAG,aAAa,IAAI,CAAS,UAAA;AAChE,aAAO,kBAAkB,OAAO,CAAA,MAAK,EAAE,YAAY,MAAM,EAAE,EAAE;AAAA,IAC9D,CAAA,GAAG,CAAC;AAGL,WAAO,KAAK,IAAI,sBAAsB,IAAI,GAAG;AAAA,EAC/C;AAEA,QAAM,wBAAwB,MAAM;AAClC,WAAO,kBAAkB,OAAO,CAAA,MAAK,EAAE,WAAW,QAAQ,EAAE;AAAA,EAC9D;AAEA,QAAM,6BAA6B,MAAM;AACnC,QAAA,kBAAkB,SAAS,EAAU,QAAA;AAEzC,QAAI,kBAAkB;AACtB,aAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AAC3C,YAAA,OAAO,kBAAkB,CAAC;AAC1B,YAAA,OAAO,kBAAkB,IAAI,CAAC;AAEpC,UAAI,KAAK,YAAY,KAAK,UAAU,GAAG;AACrC;AAAA,MAAA;AAAA,IACF;AAGI,UAAA,kBAAkB,mBAAmB,kBAAkB,SAAS;AACtE,WAAO,kBAAkB,MAAM,eAAe,kBAAkB,MAAM,UAAU;AAAA,EAClF;AAEA,QAAM,6BAA6B,MAAM;AAChC,WAAA;AAAA,MACL,wBAAwB;AAAA;AAAA,MACxB,mBAAmB,sBAAsB,IAAI,KAAK,IAAI,kBAAkB,QAAQ,CAAC;AAAA,MACjF,oBAAoB,2BAA2B;AAAA,IACjD;AAAA,EACF;AAGA,QAAM,yBAAyB,OAAO,SAAS,UAAU,QAAQ,YAAY;AAC3E,UAAM,kBAAkB;AAAA,MACtB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW,KAAK,IAAI;AAAA,MACpB,eAAe,eAAe;AAAA,MAC9B,iBAAiB;AAAA,MACjB,aAAa,KAAK,IAAA,KAAS,oBAAoB,KAAK,IAAI;AAAA,IAC1D;AAGA,yBAAqB,CAAQ,SAAA,CAAC,GAAG,MAAM,eAAe,CAAC;AAGnD,QAAA;AACF,YAAM,cAAc,gBAAgB;AAAA,QAClC,GAAG;AAAA,QACH,aAAa;AAAA,UACX,aAAa,aAAa;AAAA,UAC1B,cAAc,aAAa;AAAA,UAC3B,sBAAuB,aAAa,OAAO,CAAA,MAAK,MAAM,IAAI,EAAE,SAAS,aAAa,SAAU;AAAA,QAAA;AAAA,MAC9F,CACD;AAGD,YAAM,8BAA8B,oBAAoB;AAAA,QACtD,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,wBAAwB;AAAA,UACtB,mBAAmB,EAAE,kBAAkB,KAAK,qBAAqB,KAAK,oBAAoB,IAAI;AAAA,UAC9F,qBAAqB,EAAE,gBAAgB,KAAK,iBAAiB,KAAK,cAAc,IAAI;AAAA,UACpF,sBAAsB,EAAE,kBAAkB,KAAK,qBAAqB,KAAK,aAAa,IAAI;AAAA,QAAA;AAAA,MAC5F,CACD;AAGD,YAAM,kBAAkB,eAAe;AACvC,sBAAgB,eAAe;AAE3B,UAAA,kBAAkB,MAAM,GAAG;AACJ,iCAAA;AAAA,MAAA;AAAA,aAGpB,OAAO;AACN,cAAA,MAAM,uCAAuC,KAAK;AAAA,IAAA;AAAA,EAE9D;AAGA,QAAM,2BAA2B,YAAY;AACvC,QAAA;AACF,kCAA4B,IAAI;AAE1B,YAAA,eAAe,MAAM,cAAc,yBAAyB;AAAA,QAChE,WAAW,kBAAkB,SAAA,KAAc;AAAA,QAC3C,WAAW;AAAA,UACT;AAAA,UACA,cAAcA,WAAU;AAAA,UACxB,OAAOA,WAAU;AAAA,UACjB,UAAU,YAAY;AAAA,UACtB;AAAA,QACF;AAAA,QACA,cAAc;AAAA,QACd,aAAa;AAAA,UACX,YAAY;AAAA,UACZ,UAAU;AAAA,QAAA;AAAA,MACZ,CACD;AAED,yBAAmB,YAAY;AAG/B,iBAAW,MAAM;AACf,oCAA4B,KAAK;AAAA,SAChC,GAAI;AAAA,aAEA,OAAO;AACN,cAAA,MAAM,8BAA8B,KAAK;AACjD,kCAA4B,KAAK;AAAA,IAAA;AAAA,EAErC;AAGM,QAAA,oBAAoBC,aAAAA,YAAY,MAAM;AACpC,UAAA,gBAAgB,mBAAmB,SAAS,KAAK,MAAM,KAAK,WAAW,mBAAmB,SAAS,MAAM,CAAC;AAChH,UAAM,iBAAiB,mBAAmB,aAAa,KAAK,CAAK,MAAA,EAAE,OAAO,UAAU;AAGpF,QAAI,CAAC,gBAAgB;AACX,cAAA,MAAM,kCAAkC,UAAU;AAC1D;AAAA,IAAA;AAGF,sBAAkB,aAAa;AAC/B,kBAAc,KAAK;AACnB,gBAAY,IAAI;AACH,iBAAA,CAAA,UAAS,EAAE,GAAG,MAAM,eAAe,KAAK,gBAAgB,IAAI;AAGnE,UAAA,gBAAgB,cAAc,OAAO,MAAM,GAAG,eAAe,MAAM,EAAE,IAAI,CAAC,OAAO,WAAW;AAAA,MAChG,IAAI,WAAW,KAAK;AAAA,MACpB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,WAAW;AAAA,IAAA,EACX;AAGF,UAAM,cAAc,mBAAmB;AACvC,UAAM,gBAAgB,YAAY,OAAO,OAAK,EAAE,OAAO,cAAc,EAAE;AACvE,UAAM,mBAAmB,CAAC;AAGjB,aAAA,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,cAAc,MAAM,GAAG,KAAK;AACpD,YAAA,qBAAqB,cAAc,KAAK,MAAM,KAAK,OAAO,IAAI,cAAc,MAAM,CAAC;AACnF,YAAA,cAAc,mBAAmB,OAAO,KAAK,MAAM,KAAK,WAAW,mBAAmB,OAAO,MAAM,CAAC;AAC1G,uBAAiB,KAAK;AAAA,QACpB,IAAI,cAAc,CAAC;AAAA,QACnB,SAAS;AAAA,QACT,MAAM;AAAA,QACN,WAAW;AAAA,MAAA,CACZ;AAAA,IAAA;AAIH,UAAM,YAAY,CAAC,GAAG,eAAe,GAAG,gBAAgB;AACxD,oBAAgB,UAAU,KAAK,MAAM,KAAK,OAAO,IAAI,GAAG,CAAC;AACzD,oBAAgB,MAAM,eAAe,MAAM,EAAE,KAAK,IAAI,CAAC;AAAA,EAAA,GACtD,CAAC,UAAU,CAAC;AAGT,QAAA,YAAYA,yBAAY,OAAO,uBAAuB;AAE1D,QAAI,oBAAoB;AACtB,oBAAc,kBAAkB;AAAA,IAAA;AAIlC,uBAAmB,KAAK;AACxB,mBAAe,IAAI;AACN,iBAAA;AAAA,MACX,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,MACX,eAAe;AAAA,IAAA,CAChB;AAGG,QAAA;AACF,YAAM,iBAAiB,WAAW,KAAK,IAAA,CAAK,IAAI;AAAA,QAC9C;AAAA,QACA,UAAU;AAAA,QACV,QAAQ,MAAM,MAAM;AAAA,MAAA,CACrB;AAAA,aACM,OAAO;AACN,cAAA,KAAK,kDAAkD,KAAK;AAAA,IAAA;AAGpD,sBAAA;AAAA,EACjB,GAAA,CAAC,kBAAkB,mBAAmB,IAAI,CAAC;AAGxC,QAAA,YAAYA,aAAAA,YAAY,MAAM;AAClC,iBAAa,CAAQ,SAAA;AACnB,YAAM,WAAW,CAAC;AAClB,mBAAa,QAAQ,0BAA0B,KAAK,UAAU,QAAQ,CAAC;AACnE,UAAA,CAAC,YAAY,qBAAqB,QAAQ;AAC5C,eAAO,gBAAgB,OAAO;AAAA,MAAA;AAEzB,aAAA;AAAA,IAAA,CACR;AAAA,EACH,GAAG,EAAE;AAGC,QAAA,kBAAkBA,yBAAY,CAAC,UAAU;AAC7C,oBAAgB,KAAK;AAAA,EACvB,GAAG,EAAE;AAGC,QAAA,uBAAuBA,aAAAA,YAAY,YAAY;AACnD,kBAAc,IAAI;AAElB,UAAM,SAAS,mBAAmB,aAAa,mBAAmB,WAAW,YAAa,CAAA,KAAK;AAC/F,iBAAa,CAAS,UAAA;AAAA,MACpB,GAAG;AAAA,MACH,WAAW,KAAK,YAAY;AAAA,MAC5B,OAAO,KAAK,QAAQ;AAAA,MACpB,OAAO,KAAK,QAAQ;AAAA,IAAA,EACpB;AAEI,UAAA,UAAU,mBAAmB,oBAAoB,KAAK,MAAM,KAAK,WAAW,mBAAmB,oBAAoB,MAAM,CAAC;AAGhI,UAAM,GAAG,OAAO,gBAAgB,MAAM,UAAU;AAG5C,QAAA;AACF,YAAM,qBAAqB;AAC3B,cAAQ,IAAI,qDAAqD;AAAA,aAC1D,OAAO;AACN,cAAA,MAAM,gDAAgD,KAAK;AAAA,IAAA;AAAA,EAEpE,GAAA,CAAC,YAAY,OAAO,oBAAoB,CAAC;AAGtC,QAAA,aAAaA,yBAAY,CAAC,gBAAgB;AAC9C,QAAI,CAAC,aAAc;AAGf,QAAA,aAAa,WAAW,MAAM,MAAM;AACtC,YAAM,4BAA4B;AAClC,sBAAgB,IAAI;AACpB;AAAA,IAAA;AAGI,UAAA,kBAAkB,CAAC,GAAG,YAAY;AACxC,oBAAgB,WAAW,IAAI;AAG/B,UAAM,iBAAiB,kBAAkB,eAAe,OAAO,SAAS,aAAa,OAAO;AAG5F;AAAA,MACE,aAAa;AAAA,MACb,EAAE,GAAG,cAAc,GAAG,GAAG,KAAK,MAAM,cAAc,CAAC,EAAE;AAAA,MACrD;AAAA,MACA;AAAA,IACF;AAEA,oBAAgB,eAAe;AAG/B,QAAI,gBAAgB;AAClB,sBAAgB,UAAQ,KAAK;AAAA,QAAI,CAAA,MAC/B,EAAE,OAAO,aAAa,KAAK,EAAE,GAAG,GAAG,MAAM,SAAS;AAAA,MAAA,CACnD;AAED,YAAM,6BAA6B;AACnC,mBAAa,CAAS,UAAA;AAAA,QACpB,GAAG;AAAA,QACH,OAAO,KAAK,QAAQ;AAAA,QACpB,WAAW,KAAK,YAAY;AAAA,MAAA,EAC5B;AAGF,YAAM,sBAAsB,gBAAgB;AAAA,QAAO,WACjD,UAAU,QAAQ,eAAe,OAAO,SAAS,MAAM,OAAO;AAAA,MAAA,EAC9D;AAEE,UAAA,wBAAwB,eAAe,OAAO,QAAQ;AAC7C,mBAAA,MAAM,qBAAqB,GAAG,GAAG;AAAA,MAAA;AAAA,IAC9C,OACK;AACL,YAAM,qCAAqC;AAE3C,iBAAW,MAAM;AACf,wBAAgB,CAAQ,SAAA;AAChB,gBAAA,UAAU,CAAC,GAAG,IAAI;AACxB,kBAAQ,WAAW,IAAI;AAChB,iBAAA;AAAA,QAAA,CACR;AAAA,SACA,IAAI;AAAA,IAAA;AAGT,oBAAgB,IAAI;AAAA,EAAA,GACnB,CAAC,cAAc,cAAc,gBAAgB,wBAAwB,OAAO,oBAAoB,CAAC;AAG9F,QAAA,oBAAoBA,yBAAY,CAAC,cAAc;AAC7C,UAAA,gBAAgB,aAAa,SAAS;AAC5C,QAAI,CAAC,cAAe;AAGd,UAAA,kBAAkB,CAAC,GAAG,YAAY;AACxC,oBAAgB,SAAS,IAAI;AAC7B,oBAAgB,eAAe;AAG/B,oBAAgB,UAAQ,KAAK;AAAA,MAAI,CAAA,MAC/B,EAAE,OAAO,cAAc,KAAK,EAAE,GAAG,GAAG,MAAM,UAAU;AAAA,IAAA,CACrD;AAED,UAAM,gBAAgB;AAAA,EAAA,GACrB,CAAC,cAAc,KAAK,CAAC;AAkCxB,MAAI,iBAAiB;AACnB,YAAQ,IAAI,0DAA0D;AAEpE,WAAAG,qCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,WAAU;AAAA,QACV,iBAAgB;AAAA,QAChB,UAAS;AAAA,QACT,SAAS;AAAA,QACT;AAAA,QACA,cAAc;AAAA,UACZ,EAAE,IAAI,QAAQ,MAAM,SAAS,aAAa,wCAAwC,MAAM,KAAK;AAAA,UAC7F,EAAE,IAAI,UAAU,MAAM,SAAS,aAAa,qCAAqC,MAAM,KAAK;AAAA,UAC5F,EAAE,IAAI,QAAQ,MAAM,YAAY,aAAa,sCAAsC,MAAM,KAAK;AAAA,QAAA;AAAA,MAChG;AAAA,MAVF;AAAA,MAAA;AAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA;AAAA,MAAA;AAAA,IAWA;AAAA,EAAA;AAIF,SAAAA,qCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAW,GAAG,OAAO,gBAAgB,IAAI,SAAS,gBAAgB,mBAAmB,EAAE,IAAI,SAAS,eAAe,kBAAkB,EAAE;AAAA,MACvI,kBAAgB,SAAS;AAAA,MACzB,cAAY,SAAS;AAAA,MACrB,OAAO;AAAA,QACL,UAAU,SAAS,aAAa,UAAU,aACjC,SAAS,aAAa,UAAU,YAAY;AAAA,MACvD;AAAA,MAGC,UAAA;AAAA,QAAA,wEACE,OAAI,EAAA,WAAU,gCACb,UAACA,qCAAAA,OAAA,OAAA,EAAI,WAAU,oBACb,UAAA;AAAA,UAACA,4CAAA,OAAA,EAAI,WAAU,iBAAgB,UAA/B,OAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAmC,GAAA,IAAA;AAAA,UACnCA,qCAAAA,OAAC,OAAI,EAAA,WAAU,iBACb,UAAA;AAAA,YAACA,4CAAA,OAAA,EAAI,WAAU,kBAAiB,UAAhC,gCAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAA6D,GAAA,IAAA;AAAA,YAC5DA,4CAAA,OAAA,EAAI,WAAU,oBAAmB,UAAlC,8DAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EAJF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAKA,IAAA;AAAA,QAAA,EAPF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAAA,IAQA,EATF,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAUA,GAAA,IAAA;AAAA,QAID,mBACCA,qCAAA,OAAC,OAAI,EAAA,WAAU,4BACb,UAAA;AAAA,UAACA,qCAAAA,OAAA,OAAA,EAAI,WAAU,mBACb,UAAA;AAAA,YAACA,4CAAA,QAAA,EAAK,WAAU,iBAAgB,UAAhC,KAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAkC,GAAA,IAAA;AAAA,YACjCA,4CAAA,QAAA,EAAK,WAAU,kBAAiB,UAAjC,oBAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAAkD,IAAA;AAAA,UAAA,EAFpD,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAGA,GAAA,IAAA;AAAA,UACAA,qCAAAA,OAAC,OAAI,EAAA,WAAU,oBACb,UAAA;AAAA,YAACA,qCAAAA,OAAA,OAAA,EAAI,WAAU,gBACb,UAAA;AAAA,cAACA,4CAAA,QAAA,EAAK,WAAU,iBAAgB,UAAhC,uBAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAoD,GAAA,IAAA;AAAA,0DACnD,QAAK,EAAA,WAAU,iBAAiB,UAAA,gBAAgB,oBAAoB,gBAArE,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA,GAAkF,IAAA;AAAA,YAAA,EAFpF,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAGA,GAAA,IAAA;AAAA,YACAA,qCAAAA,OAAC,OAAI,EAAA,WAAU,gBACb,UAAA;AAAA,cAACA,4CAAA,QAAA,EAAK,WAAU,iBAAgB,UAAhC,cAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAA2C,GAAA,IAAA;AAAA,0DAC1C,QAAK,EAAA,WAAU,iBAAiB,UAAA,gBAAgB,YAAY,0BAA7D,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA,GAAoF,IAAA;AAAA,YAAA,EAFtF,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAGA,GAAA,IAAA;AAAA,YACAA,qCAAAA,OAAC,OAAI,EAAA,WAAU,gBACb,UAAA;AAAA,cAACA,4CAAA,QAAA,EAAK,WAAU,iBAAgB,UAAhC,oBAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAiD,GAAA,IAAA;AAAA,0DAChD,QAAK,EAAA,WAAU,iBAAiB,UAAA,gBAAgB,oBAAoB,sBAArE,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA,GAAwF,IAAA;AAAA,YAAA,EAF1F,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAGA,IAAA;AAAA,UAAA,EAZF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAaA,IAAA;AAAA,QAAA,EAlBF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAmBA,GAAA,IAAA;AAAA,QAGDA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,aAErB,UAAA;AAAA,UAACA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,YACrB,UAAA;AAAA,YAACA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,WAAW,UAAA;AAAA,cAAA;AAAA,0DAE9B,OAAI,EAAA,OAAO,EAAE,UAAU,UAAU,SAAS,KAAK,WAAW,UAAU,GAClE,yBAAe,UAAU,iBAAiB,aAAa,GAAG,QAAQ,YADrE,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA,GAEA,IAAA;AAAA,YAAA,EAJF,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAKA,GAAA,IAAA;AAAA,YACAA,qCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACC,WAAW,GAAG,OAAO,eAAe,IAAIL,aAAY,OAAO,YAAY,EAAE;AAAA,gBACzE,SAAS;AAAA,gBACT,OAAOA,aAAY,kBAAkB;AAAA,gBACrC,cAAYA,aAAY,kBAAkB;AAAA,gBAEzC,uBAAY,OAAO;AAAA,cAAA;AAAA,cANtB;AAAA,cAAA;AAAA,cAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA;AAAA,cAAA;AAAA,YAAA;AAAA,UAOA,EAdF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAeA,GAAA,IAAA;AAAA,UAGCK,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,WACrB,UAAA;AAAA,YAACA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,UACrB,UAAA;AAAA,cAAAA,qCAAAA,OAAC,OAAI,EAAA,WAAW,OAAO,WAAY,oBAAU,SAA7C,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAmD,GAAA,IAAA;AAAA,cAClDA,4CAAA,OAAA,EAAI,WAAW,OAAO,WAAW,UAAlC,SAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA,GAAwC,IAAA;AAAA,YAAA,EAF1C,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAGA,GAAA,IAAA;AAAA,YACCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,UACrB,UAAA;AAAA,cAAAA,qCAAAA,OAAC,OAAI,EAAA,WAAW,OAAO,WAAY,oBAAU,SAA7C,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAmD,GAAA,IAAA;AAAA,cAClDA,4CAAA,OAAA,EAAI,WAAW,OAAO,WAAW,UAAlC,SAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA,GAAwC,IAAA;AAAA,YAAA,EAF1C,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAGA,GAAA,IAAA;AAAA,YACCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,UACrB,UAAA;AAAA,cAACA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,WAAY,UAAA;AAAA,gBAAU,UAAA;AAAA,gBAAS;AAAA,cAAA,EAAtD,GAAA,QAAA,MAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAuD,GAAA,IAAA;AAAA,cACtDA,4CAAA,OAAA,EAAI,WAAW,OAAO,WAAW,UAAlC,WAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA,GAA0C,IAAA;AAAA,YAAA,EAF5C,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAGA,GAAA,IAAA;AAAA,YACCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,UACrB,UAAA;AAAA,cAAAA,qCAAAA,OAAC,OAAI,EAAA,WAAW,OAAO,WAAY,oBAAU,SAA7C,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAmD,GAAA,IAAA;AAAA,cAClDA,4CAAA,OAAA,EAAI,WAAW,OAAO,WAAW,UAAlC,QAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAA,GAAuC,IAAA;AAAA,YAAA,EAFzC,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAGA,IAAA;AAAA,UAAA,EAhBF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAiBA,GAAA,IAAA;AAAA,UAGAA,qCAAAA,OAAC,OAAI,EAAA,WAAW,OAAO,cACpB,UAAO,OAAA,OAAO,cAAc,EAAE,IAAI,CAAC,aAClCA,qCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cAEC,WAAW,GAAG,OAAO,cAAc,IACjC,UAAU,oBAAoB,SAAS,KAAK,OAAO,SAAS,EAC9D;AAAA,cACA,SAAS,MAAM,eAAe,SAAS,EAAE;AAAA,cAEzC,UAAA;AAAA,gBAACA,qCAAA,OAAA,QAAA,EAAM,mBAAS,KAAhB,GAAA,QAAA,OAAA;AAAA,kBAAA,UAAA;AAAA,kBAAA,YAAA;AAAA,kBAAA,cAAA;AAAA,gBAAqB,GAAA,IAAA;AAAA,gBACrBA,qCAAA,OAAC,QAAM,EAAA,UAAA,SAAS,KAAhB,GAAA,QAAA,OAAA;AAAA,kBAAA,UAAA;AAAA,kBAAA,YAAA;AAAA,kBAAA,cAAA;AAAA,gBAAA,GAAqB,IAAA;AAAA,cAAA;AAAA,YAAA;AAAA,YAPhB,SAAS;AAAA,YADhB;AAAA,YAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA;AAAA,YAAA;AAAA,UAAA,CAUD,EAZH,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAaA,GAAA,IAAA;AAAA,UAGC,UAAU,oBAAoB,eAAe,eAAe,MAAM,oBAAoB;AAAA,UACtF,UAAU,oBAAoB,eAAe,eAAe,MAAM,oBAAoB;AAAA,UACtF,UAAU,oBAAoB,eAAe,eAAe,MAAM,oBAAoB;AAAA,UAGtFA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,cACrB,UAAA;AAAA,YAACA,qCAAAA,OAAA,UAAA,EAAO,WAAW,OAAO,eAAe,SAAS,MAAM,MAAM,mGAAmG,GAAG,UAApK,cAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAEA,GAAA,IAAA;AAAA,YACAA,qCAAAA,OAAC,UAAO,EAAA,WAAW,OAAO,eAAe,SAAS,MAAM,mBAAmB,IAAI,GAAG,UAAlF,eAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAEA,GAAA,IAAA;AAAA,wDACC,UAAO,EAAA,WAAW,OAAO,eAAe,SAAS,QAAQ,UAA1D,eAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EATF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAUA,IAAA;AAAA,QAAA,EAvEF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAwEA,IAAA;AAAA,MAAA;AAAA,IAAA;AAAA,IAxHF;AAAA,IAAA;AAAA,IAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IAAA;AAAA,IAAA;AAAA,EAyHA;AAEJ;;;;;"}