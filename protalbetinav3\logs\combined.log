{"level":"info","message":"🔄 Inicializando sistema integrado...","timestamp":"2025-07-10T21:15:10.097Z"}
{"level":"info","message":"✅ SystemOrchestrator inicializado e funcional","timestamp":"2025-07-10T21:15:10.296Z"}
{"level":"info","message":"🚀 Iniciando Portal Betina V3 API Server...","timestamp":"2025-07-10T21:15:10.298Z"}
{"level":"error","message":"Erro ao carregar middlewares de segurança: Duplicate export of 'createLogger'","stack":"SyntaxError: Duplicate export of 'createLogger'\n    at compileSourceTextModule (node:internal/modules/esm/utils:338:16)\n    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:102:18)\n    at #translate (node:internal/modules/esm/loader:468:12)\n    at ModuleLoader.loadAndTranslate (node:internal/modules/esm/loader:515:27)","timestamp":"2025-07-10T21:15:10.351Z"}
{"level":"info","message":"🔄 Inicializando sistema integrado...","timestamp":"2025-07-10T21:15:58.692Z"}
{"level":"info","message":"✅ SystemOrchestrator inicializado e funcional","timestamp":"2025-07-10T21:15:58.874Z"}
{"level":"info","message":"🚀 Iniciando Portal Betina V3 API Server...","timestamp":"2025-07-10T21:15:58.877Z"}
{"level":"error","message":"❌ Erro fatal ao iniciar o servidor: Unexpected token 'export'","stack":"SyntaxError: Unexpected token 'export'\n    at compileSourceTextModule (node:internal/modules/esm/utils:338:16)\n    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:102:18)\n    at #translate (node:internal/modules/esm/loader:468:12)\n    at ModuleLoader.loadAndTranslate (node:internal/modules/esm/loader:515:27)","timestamp":"2025-07-10T21:16:07.661Z"}
{"level":"info","message":"🔄 Inicializando sistema integrado...","timestamp":"2025-07-10T21:23:08.405Z"}
{"level":"info","message":"✅ SystemOrchestrator inicializado e funcional","timestamp":"2025-07-10T21:23:08.522Z"}
{"level":"info","message":"🚀 Iniciando Portal Betina V3 API Server...","timestamp":"2025-07-10T21:23:08.525Z"}
{"level":"error","message":"Erro ao carregar middlewares de segurança: The requested module '../../../utils/logger.js' does not provide an export named 'createLogger'","stack":"file:///C:/Projetos/protalbetinav3/src/api/middleware/security/cors.js:2\nimport { createLogger } from '../../../utils/logger.js'\n         ^^^^^^^^^^^^\nSyntaxError: The requested module '../../../utils/logger.js' does not provide an export named 'createLogger'\n    at ModuleJob._instantiate (node:internal/modules/esm/module_job:180:21)\n    at async ModuleJob.run (node:internal/modules/esm/module_job:263:5)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)\n    at async loadSecurityMiddleware (file:///C:/Projetos/protalbetinav3/src/api/server.js:54:29)\n    at async createApp (file:///C:/Projetos/protalbetinav3/src/api/server.js:89:68)\n    at async startServer (file:///C:/Projetos/protalbetinav3/src/api/server.js:224:17)","timestamp":"2025-07-10T21:23:08.560Z"}
{"level":"info","message":"🔄 Inicializando sistema integrado...","timestamp":"2025-07-10T21:23:17.301Z"}
{"level":"info","message":"✅ SystemOrchestrator inicializado e funcional","timestamp":"2025-07-10T21:23:17.402Z"}
{"level":"info","message":"🚀 Iniciando Portal Betina V3 API Server...","timestamp":"2025-07-10T21:23:17.404Z"}
{"level":"error","message":"❌ Erro fatal ao iniciar o servidor: Unexpected token 'export'","stack":"SyntaxError: Unexpected token 'export'\n    at compileSourceTextModule (node:internal/modules/esm/utils:338:16)\n    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:102:18)\n    at #translate (node:internal/modules/esm/loader:468:12)\n    at ModuleLoader.loadAndTranslate (node:internal/modules/esm/loader:515:27)","timestamp":"2025-07-10T21:23:18.181Z"}
{"level":"info","message":"🔄 Inicializando sistema integrado...","timestamp":"2025-07-10T21:23:45.264Z"}
{"level":"info","message":"✅ SystemOrchestrator inicializado e funcional","timestamp":"2025-07-10T21:23:45.435Z"}
{"level":"info","message":"🚀 Iniciando Portal Betina V3 API Server...","timestamp":"2025-07-10T21:23:45.439Z"}
{"level":"error","message":"❌ Erro fatal ao iniciar o servidor: Unexpected token 'export'","stack":"SyntaxError: Unexpected token 'export'\n    at compileSourceTextModule (node:internal/modules/esm/utils:338:16)\n    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:102:18)\n    at #translate (node:internal/modules/esm/loader:468:12)\n    at ModuleLoader.loadAndTranslate (node:internal/modules/esm/loader:515:27)","timestamp":"2025-07-10T21:23:47.864Z"}
{"level":"info","message":"🔄 Inicializando sistema integrado...","timestamp":"2025-07-10T21:24:00.528Z"}
{"level":"info","message":"✅ SystemOrchestrator inicializado e funcional","timestamp":"2025-07-10T21:24:00.641Z"}
{"level":"info","message":"🚀 Iniciando Portal Betina V3 API Server...","timestamp":"2025-07-10T21:24:00.644Z"}
{"level":"error","message":"❌ Erro fatal ao iniciar o servidor: Unexpected token 'export'","stack":"SyntaxError: Unexpected token 'export'\n    at compileSourceTextModule (node:internal/modules/esm/utils:338:16)\n    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:102:18)\n    at #translate (node:internal/modules/esm/loader:468:12)\n    at ModuleLoader.loadAndTranslate (node:internal/modules/esm/loader:515:27)","timestamp":"2025-07-10T21:24:01.453Z"}
{"level":"info","message":"🔄 Inicializando sistema integrado...","timestamp":"2025-07-10T21:24:14.212Z"}
{"level":"info","message":"✅ SystemOrchestrator inicializado e funcional","timestamp":"2025-07-10T21:24:14.332Z"}
{"level":"info","message":"🚀 Iniciando Portal Betina V3 API Server...","timestamp":"2025-07-10T21:24:14.333Z"}
{"level":"error","message":"❌ Erro fatal ao iniciar o servidor: Unexpected token 'export'","stack":"SyntaxError: Unexpected token 'export'\n    at compileSourceTextModule (node:internal/modules/esm/utils:338:16)\n    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:102:18)\n    at #translate (node:internal/modules/esm/loader:468:12)\n    at ModuleLoader.loadAndTranslate (node:internal/modules/esm/loader:515:27)","timestamp":"2025-07-10T21:24:15.363Z"}
{"level":"info","message":"🔄 Inicializando sistema integrado...","timestamp":"2025-07-10T21:24:44.839Z"}
{"level":"info","message":"✅ SystemOrchestrator inicializado e funcional","timestamp":"2025-07-10T21:24:44.967Z"}
{"level":"info","message":"🚀 Iniciando Portal Betina V3 API Server...","timestamp":"2025-07-10T21:24:44.969Z"}
{"level":"error","message":"❌ Erro fatal ao iniciar o servidor: Unexpected token 'export'","stack":"SyntaxError: Unexpected token 'export'\n    at compileSourceTextModule (node:internal/modules/esm/utils:338:16)\n    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:102:18)\n    at #translate (node:internal/modules/esm/loader:468:12)\n    at ModuleLoader.loadAndTranslate (node:internal/modules/esm/loader:515:27)","timestamp":"2025-07-10T21:24:45.689Z"}
{"level":"info","message":"🔄 Inicializando sistema integrado...","timestamp":"2025-07-10T21:40:45.874Z"}
{"level":"info","message":"✅ SystemOrchestrator inicializado e funcional","timestamp":"2025-07-10T21:40:45.953Z"}
{"level":"info","message":"🚀 Iniciando Portal Betina V3 API Server...","timestamp":"2025-07-10T21:40:45.954Z"}
{"level":"error","message":"❌ Erro fatal ao iniciar o servidor: Unexpected token 'export'","stack":"SyntaxError: Unexpected token 'export'\n    at compileSourceTextModule (node:internal/modules/esm/utils:338:16)\n    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:102:18)\n    at #translate (node:internal/modules/esm/loader:468:12)\n    at ModuleLoader.loadAndTranslate (node:internal/modules/esm/loader:515:27)","timestamp":"2025-07-10T21:40:46.685Z"}
{"level":"info","message":"🔄 Inicializando sistema integrado...","timestamp":"2025-07-10T21:40:56.703Z"}
{"level":"info","message":"✅ SystemOrchestrator inicializado e funcional","timestamp":"2025-07-10T21:40:56.795Z"}
{"level":"info","message":"🚀 Iniciando Portal Betina V3 API Server...","timestamp":"2025-07-10T21:40:56.796Z"}
{"level":"error","message":"❌ Erro fatal ao iniciar o servidor: Unexpected token 'export'","stack":"SyntaxError: Unexpected token 'export'\n    at compileSourceTextModule (node:internal/modules/esm/utils:338:16)\n    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:102:18)\n    at #translate (node:internal/modules/esm/loader:468:12)\n    at ModuleLoader.loadAndTranslate (node:internal/modules/esm/loader:515:27)","timestamp":"2025-07-10T21:40:57.335Z"}
