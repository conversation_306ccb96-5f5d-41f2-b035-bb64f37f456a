const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/game-numbers-tpTS4tK7.js","assets/services-M1ydzWhv.js","assets/dashboard-DanqcTsU.js","assets/vendor-react-ByWh_-BW.js","assets/vendor-misc-DneMUARX.js","assets/vendor-charts-Cii0KTpx.js","assets/context-Ch-5FaFa.js","assets/hooks-NJkOkh4y.js","assets/vendor-utils-CjlX8hrF.js","assets/admin-D2mpdgvV.js","assets/admin-BQPZfmpR.css","assets/utils-CLTxz6zX.js","assets/dashboard-C4HFo8oW.css","assets/game-colors-B_gd3llZ.js","assets/game-association-B9GAxBuN.js","assets/game-association-aRlfWsTT.css","assets/game-colors-WFKpsHgQ.css","assets/game-letters-v8KNWHXS.js","assets/game-letters-CgoS80mu.css","assets/game-memory-6_ujaMB2.js","assets/vendor-motion-CJek6P2z.js","assets/game-memory-BTGvJ0Wb.css","assets/game-musical-Ci_rqtJn.js","assets/game-musical-Srt0Rn1x.css","assets/game-patterns-GQY4qytf.js","assets/game-patterns-C7lF44VH.css","assets/game-puzzle-BLc_eXaF.js","assets/game-puzzle-jC3b1JUV.css","assets/game-creative-iDOKdRXI.js","assets/game-creative-ByDHG8hJ.css","assets/game-numbers-CWskqaet.css","assets/About-3oLEsZnh.js","assets/About-Bg6bvhr6.css","assets/AccessibilityPage-B2X7xTNK.js","assets/AccessibilityPage-BoFWZm28.css","assets/UserProfiles-Dd9TdgTt.js","assets/UserProfiles-T9kyVdi3.css"])))=>i.map(i=>d[i]);
import { R as React, r as reactExports, c as ReactDOM, H as HelmetProvider, Q as QueryClientProvider } from "./vendor-react-ByWh_-BW.js";
import { Q as QueryClient } from "./vendor-query-WfQzIUQA.js";
import { _ as __vitePreload } from "./dashboard-DanqcTsU.js";
import { m as motion } from "./vendor-motion-CJek6P2z.js";
import { i as initializePortalBetinaSystem } from "./services-M1ydzWhv.js";
import { c as SystemProvider, D as DatabaseProvider, P as PremiumProvider, A as AdminProvider, d as AccessibilityProvider } from "./context-Ch-5FaFa.js";
import "./vendor-misc-DneMUARX.js";
import "./vendor-charts-Cii0KTpx.js";
import "./admin-D2mpdgvV.js";
import "./utils-CLTxz6zX.js";
import "./vendor-utils-CjlX8hrF.js";
import "./game-colors-B_gd3llZ.js";
import "./game-association-B9GAxBuN.js";
import "./hooks-NJkOkh4y.js";
import "./game-letters-v8KNWHXS.js";
import "./game-memory-6_ujaMB2.js";
import "./game-musical-Ci_rqtJn.js";
import "./game-patterns-GQY4qytf.js";
import "./game-puzzle-BLc_eXaF.js";
import "./game-numbers-tpTS4tK7.js";
import "./game-creative-iDOKdRXI.js";
(function polyfill() {
  const relList = document.createElement("link").relList;
  if (relList && relList.supports && relList.supports("modulepreload")) {
    return;
  }
  for (const link of document.querySelectorAll('link[rel="modulepreload"]')) {
    processPreload(link);
  }
  new MutationObserver((mutations) => {
    for (const mutation of mutations) {
      if (mutation.type !== "childList") {
        continue;
      }
      for (const node of mutation.addedNodes) {
        if (node.tagName === "LINK" && node.rel === "modulepreload")
          processPreload(node);
      }
    }
  }).observe(document, { childList: true, subtree: true });
  function getFetchOpts(link) {
    const fetchOpts = {};
    if (link.integrity) fetchOpts.integrity = link.integrity;
    if (link.referrerPolicy) fetchOpts.referrerPolicy = link.referrerPolicy;
    if (link.crossOrigin === "use-credentials")
      fetchOpts.credentials = "include";
    else if (link.crossOrigin === "anonymous") fetchOpts.credentials = "omit";
    else fetchOpts.credentials = "same-origin";
    return fetchOpts;
  }
  function processPreload(link) {
    if (link.ep)
      return;
    link.ep = true;
    const fetchOpts = getFetchOpts(link);
    fetch(link.href, fetchOpts);
  }
})();
function Header({
  onLogoClick
}) {
  return null;
}
const container$1 = "_container_gltsb_8";
const title$2 = "_title_gltsb_12";
const grid$1 = "_grid_gltsb_36";
const card$1 = "_card_gltsb_45";
const gradient = "_gradient_gltsb_1";
const cardHeader$1 = "_cardHeader_gltsb_80";
const icon$1 = "_icon_gltsb_87";
const badge$1 = "_badge_gltsb_92";
const blue$1 = "_blue_gltsb_102";
const orange$1 = "_orange_gltsb_106";
const green$1 = "_green_gltsb_110";
const purple$1 = "_purple_gltsb_114";
const pink = "_pink_gltsb_118";
const cyan$1 = "_cyan_gltsb_122";
const cardTitle$1 = "_cardTitle_gltsb_126";
const cardDescription$1 = "_cardDescription_gltsb_135";
const cardCategory = "_cardCategory_gltsb_143";
const cardButton = "_cardButton_gltsb_157";
const loadingState = "_loadingState_gltsb_193";
const errorState = "_errorState_gltsb_202";
const cardIcon = "_cardIcon_gltsb_226";
const highContrast = "_highContrast_gltsb_254";
const reducedMotion = "_reducedMotion_gltsb_275";
const styles$3 = {
  container: container$1,
  title: title$2,
  grid: grid$1,
  card: card$1,
  gradient,
  cardHeader: cardHeader$1,
  icon: icon$1,
  badge: badge$1,
  blue: blue$1,
  orange: orange$1,
  green: green$1,
  purple: purple$1,
  pink,
  cyan: cyan$1,
  cardTitle: cardTitle$1,
  cardDescription: cardDescription$1,
  cardCategory,
  cardButton,
  loadingState,
  errorState,
  cardIcon,
  highContrast,
  reducedMotion
};
var _jsxFileName$6 = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\components\\navigation\\ActivitiesGrid\\ActivitiesGrid.jsx";
function ActivitiesGridComponent({
  activities: activities2,
  onActivitySelect
}) {
  const handleActivityClick = (activityId, activityTitle) => {
    if (onActivitySelect) {
      onActivitySelect(activityId);
    }
  };
  const defaultActivities = [{
    id: "letter-recognition",
    title: "Reconhecimento de Letras",
    description: "Aprenda o alfabeto de forma divertida e interativa",
    icon: "🔤",
    badge: "Letras",
    color: "blue"
  }, {
    id: "number-counting",
    title: "Contagem de Números",
    description: "Pratique contagem e reconhecimento numérico",
    icon: "🔢",
    badge: "Números",
    color: "orange"
  }, {
    id: "memory-game",
    title: "Jogo da Memória",
    description: "Encontre os pares e exercite sua memória",
    icon: "🧠",
    badge: "Memória",
    color: "green"
  }, {
    id: "color-match",
    title: "Combinação de Cores",
    description: "Combine cores e desenvolva percepção visual",
    icon: "🌈",
    badge: "Cores",
    color: "purple"
  }, {
    id: "musical-sequence",
    title: "Sequência Musical",
    description: "Repita sequências sonoras e desenvolva a memória auditiva",
    icon: "🎵",
    badge: "Música",
    color: "pink"
  }, {
    id: "image-association",
    title: "Associação de Imagens",
    description: "Associe imagens e desenvolva conexões cognitivas",
    icon: "🧩",
    badge: "Imagens",
    color: "cyan"
  }, {
    id: "padroes-visuais",
    title: "Padrões Visuais",
    description: "Identifique e complete padrões visuais complexos",
    icon: "🔷",
    badge: "Padrões",
    color: "indigo"
  }, {
    id: "quebra-cabeca",
    title: "Quebra-Cabeça",
    description: "Monte quebra-cabeças e desenvolva raciocínio espacial",
    icon: "🧩",
    badge: "Puzzle",
    color: "teal"
  }, {
    id: "creative-painting",
    title: "Pintura Criativa",
    description: "Expresse sua criatividade através da arte digital",
    icon: "🎨",
    badge: "Arte",
    color: "red"
  }];
  const displayActivities = activities2?.length ? activities2 : defaultActivities;
  return /* @__PURE__ */ React.createElement("section", { className: styles$3.container, __self: this, __source: {
    fileName: _jsxFileName$6,
    lineNumber: 95,
    columnNumber: 5
  } }, /* @__PURE__ */ React.createElement("h2", { className: styles$3.title, __self: this, __source: {
    fileName: _jsxFileName$6,
    lineNumber: 96,
    columnNumber: 7
  } }, "🎯 Atividades Mais Populares"), /* @__PURE__ */ React.createElement("div", { className: styles$3.grid, __self: this, __source: {
    fileName: _jsxFileName$6,
    lineNumber: 100,
    columnNumber: 7
  } }, displayActivities.map((activity, index) => /* @__PURE__ */ React.createElement("button", { key: activity.id, className: styles$3.card, onClick: () => handleActivityClick(activity.id, activity.title), style: {
    animationDelay: `${index * 0.1}s`
  }, __self: this, __source: {
    fileName: _jsxFileName$6,
    lineNumber: 102,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles$3.cardHeader, __self: this, __source: {
    fileName: _jsxFileName$6,
    lineNumber: 108,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("div", { className: styles$3.icon, __self: this, __source: {
    fileName: _jsxFileName$6,
    lineNumber: 109,
    columnNumber: 15
  } }, activity.icon), /* @__PURE__ */ React.createElement("span", { className: `${styles$3.badge} ${styles$3[activity.color] || styles$3.blue}`, __self: this, __source: {
    fileName: _jsxFileName$6,
    lineNumber: 110,
    columnNumber: 15
  } }, activity.badge)), /* @__PURE__ */ React.createElement("h3", { className: styles$3.cardTitle, __self: this, __source: {
    fileName: _jsxFileName$6,
    lineNumber: 115,
    columnNumber: 13
  } }, activity.title), /* @__PURE__ */ React.createElement("p", { className: styles$3.cardDescription, __self: this, __source: {
    fileName: _jsxFileName$6,
    lineNumber: 116,
    columnNumber: 13
  } }, activity.description)))));
}
const banner = "_banner_17zy2_7";
const welcomeHeader = "_welcomeHeader_17zy2_37";
const welcomeTitle = "_welcomeTitle_17zy2_41";
const title$1 = "_title_17zy2_57";
const heart = "_heart_17zy2_64";
const text = "_text_17zy2_76";
const qrSection = "_qrSection_17zy2_90";
const qrContainer = "_qrContainer_17zy2_99";
const donationInfo = "_donationInfo_17zy2_125";
const donationText = "_donationText_17zy2_139";
const qrImage = "_qrImage_17zy2_202";
const styles$2 = {
  banner,
  welcomeHeader,
  welcomeTitle,
  title: title$1,
  heart,
  text,
  qrSection,
  qrContainer,
  donationInfo,
  donationText,
  qrImage
};
var _jsxFileName$5 = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\components\\navigation\\DonationBanner\\DonationBanner.jsx";
function DonationBanner() {
  const pixKey = "<EMAIL>";
  const showNotification = (message, type = "success") => {
    const notification = document.createElement("div");
    notification.className = `notification ${type}`;
    notification.textContent = message;
    Object.assign(notification.style, {
      position: "fixed",
      top: "20px",
      right: "20px",
      padding: "12px 24px",
      borderRadius: "8px",
      color: "#fff",
      fontWeight: "600",
      fontSize: "14px",
      zIndex: "10000",
      transform: "translateX(100%)",
      transition: "transform 0.3s ease, opacity 0.3s ease",
      boxShadow: "0 4px 12px rgba(0,0,0,0.2)",
      display: "flex",
      alignItems: "center",
      gap: "8px"
    });
    const colors = {
      success: "linear-gradient(135deg, #10b981, #059669)",
      error: "linear-gradient(135deg, #ef4444, #dc2626)"
    };
    notification.style.background = colors[type] || colors.success;
    const icon2 = document.createElement("span");
    icon2.textContent = type === "success" ? "✅" : "❌";
    notification.prepend(icon2);
    document.body.appendChild(notification);
    setTimeout(() => {
      notification.style.transform = "translateX(0)";
      notification.style.opacity = "1";
    }, 100);
    setTimeout(() => {
      notification.style.transform = "translateX(100%)";
      notification.style.opacity = "0";
      setTimeout(() => notification.remove(), 300);
    }, 3e3);
  };
  const copyPix = () => {
    navigator.clipboard.writeText(pixKey).then(() => {
      showNotification("Chave PIX copiada com sucesso!", "success");
    }).catch(() => {
      showNotification("Erro ao copiar chave PIX", "error");
    });
  };
  return /* @__PURE__ */ React.createElement(motion.section, { className: styles$2.banner, initial: {
    opacity: 0,
    y: 20
  }, animate: {
    opacity: 1,
    y: 0
  }, transition: {
    duration: 0.5
  }, role: "region", "aria-label": "Banner de doação", __self: this, __source: {
    fileName: _jsxFileName$5,
    lineNumber: 70,
    columnNumber: 5
  } }, /* @__PURE__ */ React.createElement("div", { className: styles$2.welcomeHeader, __self: this, __source: {
    fileName: _jsxFileName$5,
    lineNumber: 78,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("h1", { className: styles$2.welcomeTitle, __self: this, __source: {
    fileName: _jsxFileName$5,
    lineNumber: 79,
    columnNumber: 9
  } }, "🌟 Bem-vindos ao Portal Bettina! 🌟")), /* @__PURE__ */ React.createElement("h2", { className: styles$2.title, __self: this, __source: {
    fileName: _jsxFileName$5,
    lineNumber: 84,
    columnNumber: 7
  } }, "Ajude a manter este projeto vivo", /* @__PURE__ */ React.createElement("span", { className: styles$2.heart, "aria-hidden": "true", __self: this, __source: {
    fileName: _jsxFileName$5,
    lineNumber: 86,
    columnNumber: 9
  } }, "💖")), /* @__PURE__ */ React.createElement("p", { className: styles$2.text, __self: this, __source: {
    fileName: _jsxFileName$5,
    lineNumber: 89,
    columnNumber: 7
  } }, "Este portal nasceu da história da minha filha Bettina e do desejo de apoiar outras crianças no seu desenvolvimento. Oferecemos gratuitamente atividades terapêuticas e educativas para crianças com autismo, TDAH e outras necessidades cognitivas.", /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName$5,
    lineNumber: 92,
    columnNumber: 9
  } }, " Faça sua doação se possível!")), /* @__PURE__ */ React.createElement("div", { className: styles$2.qrSection, __self: this, __source: {
    fileName: _jsxFileName$5,
    lineNumber: 95,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("div", { className: styles$2.qrContainer, __self: this, __source: {
    fileName: _jsxFileName$5,
    lineNumber: 96,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement(motion.img, { src: `https://api.qrserver.com/v1/create-qr-code/?size=120x120&data=${encodeURIComponent(pixKey)}`, alt: "QR Code PIX para doação", className: styles$2.qrImage, title: "Clique para copiar a chave PIX ou escaneie com seu banco", onClick: copyPix, whileHover: {
    scale: 1.05
  }, whileTap: {
    scale: 0.95
  }, role: "button", tabIndex: 0, onKeyDown: (e) => e.key === "Enter" && copyPix(), __self: this, __source: {
    fileName: _jsxFileName$5,
    lineNumber: 97,
    columnNumber: 11
  } })), /* @__PURE__ */ React.createElement("div", { className: styles$2.donationInfo, __self: this, __source: {
    fileName: _jsxFileName$5,
    lineNumber: 111,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("ul", { className: styles$2.donationText, __self: this, __source: {
    fileName: _jsxFileName$5,
    lineNumber: 112,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("li", { __self: this, __source: {
    fileName: _jsxFileName$5,
    lineNumber: 113,
    columnNumber: 13
  } }, "Escaneie o QR Code com seu banco"), /* @__PURE__ */ React.createElement("li", { __self: this, __source: {
    fileName: _jsxFileName$5,
    lineNumber: 114,
    columnNumber: 13
  } }, "Ou pressione o QR code para copiar a chave"), /* @__PURE__ */ React.createElement("li", { __self: this, __source: {
    fileName: _jsxFileName$5,
    lineNumber: 115,
    columnNumber: 13
  } }, "Qualquer valor é bem-vindo! 🙏")))));
}
const container = "_container_c1526_13";
const title = "_title_c1526_23";
const grid = "_grid_c1526_71";
const card = "_card_c1526_89";
const fadeInUp = "_fadeInUp_c1526_1";
const blue = "_blue_c1526_207";
const green = "_green_c1526_217";
const purple = "_purple_c1526_227";
const orange = "_orange_c1526_237";
const red = "_red_c1526_247";
const cyan = "_cyan_c1526_257";
const special = "_special_c1526_267";
const cardHeader = "_cardHeader_c1526_279";
const icon = "_icon_c1526_293";
const badge = "_badge_c1526_315";
const badgeBlue = "_badgeBlue_c1526_337";
const badgeGreen = "_badgeGreen_c1526_345";
const badgePremium = "_badgePremium_c1526_353";
const badgePurple = "_badgePurple_c1526_369";
const badgeOrange = "_badgeOrange_c1526_377";
const badgeRed = "_badgeRed_c1526_385";
const badgeCyan = "_badgeCyan_c1526_393";
const badgeSpecial = "_badgeSpecial_c1526_401";
const cardTitle = "_cardTitle_c1526_439";
const cardDescription = "_cardDescription_c1526_465";
const styles$1 = {
  container,
  title,
  grid,
  card,
  fadeInUp,
  blue,
  green,
  purple,
  orange,
  red,
  cyan,
  special,
  cardHeader,
  icon,
  badge,
  badgeBlue,
  badgeGreen,
  badgePremium,
  badgePurple,
  badgeOrange,
  badgeRed,
  badgeCyan,
  badgeSpecial,
  "badge-glow": "_badge-glow_c1526_1",
  cardTitle,
  cardDescription
};
var _jsxFileName$4 = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\components\\navigation\\ToolsSection\\ToolsSection.jsx";
function ToolsSection({
  onToolSelect
}) {
  const handleToolClick = (toolId, toolTitle, toolType = "info") => {
    if (onToolSelect) {
      onToolSelect(toolId);
    }
  };
  const tools = [{
    id: "dashboard-performance",
    title: "Dashboards do Sistema",
    description: "Performance, IA, Neuropedagógico, Multissensorial e Sistema Integrado",
    icon: "📊",
    badge: "Premium",
    color: "premium",
    type: "premium"
  }, {
    id: "user-profiles",
    title: "Perfis de Usuário",
    description: "Gerencie diferentes perfis para toda a família",
    icon: "👤",
    badge: "Perfis",
    color: "purple",
    type: "info"
  }, {
    id: "admin-panel",
    title: "Painel Administrativo",
    description: "Configurações avançadas e gerenciamento do sistema",
    icon: "🔐",
    badge: "Admin",
    color: "red",
    type: "warning"
  }, {
    id: "about-info",
    title: "Sobre o Portal",
    description: "Informações sobre o projeto, versão e créditos",
    icon: "ℹ️",
    badge: "Info",
    color: "cyan",
    type: "info"
  }, {
    id: "accessibility-settings",
    title: "Configurações de Acessibilidade",
    description: "Alto contraste, tamanho da fonte e outras opções de acessibilidade",
    icon: "♿",
    badge: "Acessibilidade",
    color: "special",
    type: "special"
  }];
  return /* @__PURE__ */ React.createElement("section", { className: styles$1.container, __self: this, __source: {
    fileName: _jsxFileName$4,
    lineNumber: 65,
    columnNumber: 5
  } }, /* @__PURE__ */ React.createElement("h2", { className: styles$1.title, __self: this, __source: {
    fileName: _jsxFileName$4,
    lineNumber: 66,
    columnNumber: 7
  } }, "⚙️ Ferramentas e Configurações"), /* @__PURE__ */ React.createElement("div", { className: styles$1.grid, __self: this, __source: {
    fileName: _jsxFileName$4,
    lineNumber: 70,
    columnNumber: 7
  } }, tools.map((tool, index) => /* @__PURE__ */ React.createElement("button", { key: tool.id, className: `${styles$1.card} ${styles$1[tool.color] || styles$1.blue}`, onClick: () => handleToolClick(tool.id, tool.title, tool.type), style: {
    animationDelay: `${index * 0.1}s`
  }, __self: this, __source: {
    fileName: _jsxFileName$4,
    lineNumber: 72,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles$1.cardHeader, __self: this, __source: {
    fileName: _jsxFileName$4,
    lineNumber: 78,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("div", { className: styles$1.icon, __self: this, __source: {
    fileName: _jsxFileName$4,
    lineNumber: 79,
    columnNumber: 15
  } }, tool.icon), /* @__PURE__ */ React.createElement("span", { className: `${styles$1.badge} ${styles$1[`badge${tool.color.charAt(0).toUpperCase() + tool.color.slice(1)}`] || styles$1.badgeBlue}`, __self: this, __source: {
    fileName: _jsxFileName$4,
    lineNumber: 80,
    columnNumber: 15
  } }, tool.badge)), /* @__PURE__ */ React.createElement("h3", { className: styles$1.cardTitle, __self: this, __source: {
    fileName: _jsxFileName$4,
    lineNumber: 85,
    columnNumber: 13
  } }, tool.title), /* @__PURE__ */ React.createElement("p", { className: styles$1.cardDescription, __self: this, __source: {
    fileName: _jsxFileName$4,
    lineNumber: 86,
    columnNumber: 13
  } }, tool.description)))));
}
const footer = "_footer_wq0j0_7";
const footerContent = "_footerContent_wq0j0_23";
const navigationGrid = "_navigationGrid_wq0j0_33";
const navButton = "_navButton_wq0j0_43";
const active = "_active_wq0j0_64";
const navIcon = "_navIcon_wq0j0_106";
const navLabel = "_navLabel_wq0j0_111";
const styles = {
  footer,
  footerContent,
  navigationGrid,
  navButton,
  active,
  navIcon,
  navLabel
};
var _jsxFileName$3 = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\components\\navigation\\Footer\\Footer.jsx";
const activities = [{
  id: "home",
  name: "Início",
  icon: "🏠"
}, {
  id: "letter-recognition",
  name: "Letras",
  icon: "🔤"
}, {
  id: "number-counting",
  name: "Números",
  icon: "🔢"
}, {
  id: "memory-game",
  name: "Memória",
  icon: "🧠"
}, {
  id: "musical-sequence",
  name: "Música",
  icon: "🎵"
}, {
  id: "color-match",
  name: "Cores",
  icon: "🌈"
}, {
  id: "image-association",
  name: "Imagens",
  icon: "🖼️"
}, {
  id: "padroes-visuais",
  name: "Padrões",
  icon: "🔷"
}, {
  id: "quebra-cabeca",
  name: "Puzzle",
  icon: "🧩"
}, {
  id: "creative-painting",
  name: "Arte",
  icon: "🎨"
}];
function Footer({
  currentActivity,
  onActivityChange
}) {
  const handleActivityClick = (activityId, activityName) => {
    if (onActivityChange) {
      onActivityChange(activityId);
    }
  };
  return /* @__PURE__ */ React.createElement("footer", { className: styles.footer, __self: this, __source: {
    fileName: _jsxFileName$3,
    lineNumber: 34,
    columnNumber: 5
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.footerContent, __self: this, __source: {
    fileName: _jsxFileName$3,
    lineNumber: 35,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.navigationGrid, __self: this, __source: {
    fileName: _jsxFileName$3,
    lineNumber: 36,
    columnNumber: 9
  } }, activities.map((activity) => /* @__PURE__ */ React.createElement("button", { key: activity.id, className: `${styles.navButton} ${currentActivity === activity.id ? styles.active : ""}`, onClick: () => handleActivityClick(activity.id, activity.name), role: "button", "aria-label": `Navegar para ${activity.name}`, __self: this, __source: {
    fileName: _jsxFileName$3,
    lineNumber: 37,
    columnNumber: 54
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.navIcon, __self: this, __source: {
    fileName: _jsxFileName$3,
    lineNumber: 44,
    columnNumber: 15
  } }, activity.icon), /* @__PURE__ */ React.createElement("div", { className: styles.navLabel, __self: this, __source: {
    fileName: _jsxFileName$3,
    lineNumber: 45,
    columnNumber: 15
  } }, activity.name))))));
}
var _jsxFileName$2 = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\components\\pages\\GamePage.jsx";
const ContagemNumerosGame = reactExports.lazy(() => __vitePreload(() => import("./game-numbers-tpTS4tK7.js").then((n) => n.a), true ? __vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30]) : void 0));
const LetterRecognitionGame = reactExports.lazy(() => __vitePreload(() => import("./game-letters-v8KNWHXS.js").then((n) => n.b), true ? __vite__mapDeps([17,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,19,20,21,22,23,24,25,26,27,0,30,28,29,18]) : void 0));
const MusicalSequenceGame = reactExports.lazy(() => __vitePreload(() => import("./game-musical-Ci_rqtJn.js").then((n) => n.b), true ? __vite__mapDeps([22,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,24,25,26,27,0,30,28,29,23]) : void 0));
const MemoryGame = reactExports.lazy(() => __vitePreload(() => import("./game-memory-6_ujaMB2.js").then((n) => n.b), true ? __vite__mapDeps([19,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,22,23,24,25,26,27,0,30,28,20,29,21]) : void 0));
const ColorMatchGame = reactExports.lazy(() => __vitePreload(() => import("./game-colors-B_gd3llZ.js").then((n) => n.b), true ? __vite__mapDeps([13,1,2,3,4,5,6,7,8,9,10,11,12,14,15,17,18,19,20,21,22,23,24,25,26,27,0,30,28,29,16]) : void 0));
const ImageAssociationGame = reactExports.lazy(() => __vitePreload(() => import("./game-association-B9GAxBuN.js").then((n) => n.b), true ? __vite__mapDeps([14,1,2,3,4,5,6,7,8,9,10,11,12,13,16,17,18,19,20,21,22,23,24,25,26,27,0,30,28,29,15]) : void 0));
const PadroesVisuaisGame = reactExports.lazy(() => __vitePreload(() => import("./game-patterns-GQY4qytf.js").then((n) => n.b), true ? __vite__mapDeps([24,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,26,27,0,30,28,29,25]) : void 0));
const QuebraCabecaGame = reactExports.lazy(() => __vitePreload(() => import("./game-puzzle-BLc_eXaF.js").then((n) => n.b), true ? __vite__mapDeps([26,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,0,30,28,29,27]) : void 0));
const CreativePaintingGame = reactExports.lazy(() => __vitePreload(() => import("./game-creative-iDOKdRXI.js").then((n) => n.b), true ? __vite__mapDeps([28,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,0,30,29]) : void 0));
const GameLoadingFallback = () => /* @__PURE__ */ React.createElement("div", { style: {
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  alignItems: "center",
  height: "100vh",
  background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
  color: "white",
  fontSize: "18px",
  fontFamily: "Arial, sans-serif"
}, __self: void 0, __source: {
  fileName: _jsxFileName$2,
  lineNumber: 16,
  columnNumber: 3
} }, /* @__PURE__ */ React.createElement("div", { style: {
  marginBottom: "20px",
  fontSize: "48px"
}, __self: void 0, __source: {
  fileName: _jsxFileName$2,
  lineNumber: 27,
  columnNumber: 5
} }, "🎮"), /* @__PURE__ */ React.createElement("div", { __self: void 0, __source: {
  fileName: _jsxFileName$2,
  lineNumber: 28,
  columnNumber: 5
} }, "Carregando jogo..."));
const GamePage = ({
  gameId,
  onBack
}) => {
  const gameComponents = {
    "numbers": ContagemNumerosGame,
    "number-counting": ContagemNumerosGame,
    "contagem-numeros": ContagemNumerosGame,
    "letters": LetterRecognitionGame,
    "letter-recognition": LetterRecognitionGame,
    "music": MusicalSequenceGame,
    "musical-sequence": MusicalSequenceGame,
    "memory": MemoryGame,
    "memory-game": MemoryGame,
    "colors": ColorMatchGame,
    "color-match": ColorMatchGame,
    "images": ImageAssociationGame,
    "image-association": ImageAssociationGame,
    "patterns": PadroesVisuaisGame,
    "padroes-visuais": PadroesVisuaisGame,
    "quebra-cabeca": QuebraCabecaGame,
    "emotional-puzzle": QuebraCabecaGame,
    // Quebra-cabeça emocional
    "creative-painting": CreativePaintingGame,
    "pintura-criativa": CreativePaintingGame
  };
  const GameComponent = gameComponents[gameId];
  if (GameComponent) {
    return /* @__PURE__ */ React.createElement(reactExports.Suspense, { fallback: /* @__PURE__ */ React.createElement(GameLoadingFallback, { __self: void 0, __source: {
      fileName: _jsxFileName$2,
      lineNumber: 61,
      columnNumber: 27
    } }), __self: void 0, __source: {
      fileName: _jsxFileName$2,
      lineNumber: 61,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement(GameComponent, { onBack, __self: void 0, __source: {
      fileName: _jsxFileName$2,
      lineNumber: 62,
      columnNumber: 9
    } }));
  }
  const gameInfo = {
    "letter-recognition": {
      title: "Reconhecimento de Letras",
      description: "Jogo educativo para aprender o alfabeto",
      content: "🔤 Este é o jogo de reconhecimento de letras! Em breve estará totalmente funcional."
    },
    "letters": {
      title: "Reconhecimento de Letras",
      description: "Jogo educativo para aprender o alfabeto",
      content: "🔤 Este é o jogo de reconhecimento de letras! Em breve estará totalmente funcional."
    },
    "number-counting": {
      title: "Contagem de Números",
      description: "Jogo para aprender números e contagem",
      content: "🔢 Este é o jogo de contagem de números! Em breve estará totalmente funcional."
    },
    "numbers": {
      title: "Contagem de Números",
      description: "Jogo para aprender números e contagem",
      content: "🔢 Este é o jogo de contagem de números! Em breve estará totalmente funcional."
    },
    "musical-sequence": {
      title: "Sequência Musical",
      description: "Jogo de memória auditiva",
      content: "🎵 Este é o jogo de sequência musical! Em breve estará totalmente funcional."
    },
    "music": {
      title: "Sequência Musical",
      description: "Jogo de memória auditiva",
      content: "🎵 Este é o jogo de sequência musical! Em breve estará totalmente funcional."
    },
    "memory-game": {
      title: "Jogo da Memória",
      description: "Jogo clássico de memória",
      content: "🧠 Este é o jogo da memória! Em breve estará totalmente funcional."
    },
    "memory": {
      title: "Jogo da Memória",
      description: "Jogo clássico de memória",
      content: "🧠 Este é o jogo da memória! Em breve estará totalmente funcional."
    },
    "color-match": {
      title: "Combinação de Cores",
      description: "Jogo de combinação de cores",
      content: "🌈 Este é o jogo de combinação de cores! Em breve estará totalmente funcional."
    },
    "colors": {
      title: "Combinação de Cores",
      description: "Jogo de combinação de cores",
      content: "🌈 Este é o jogo de combinação de cores! Em breve estará totalmente funcional."
    },
    "image-association": {
      title: "Associação de Imagens",
      description: "Jogo de associação cognitiva",
      content: "🧩 Este é o jogo de associação de imagens! Em breve estará totalmente funcional."
    },
    "images": {
      title: "Associação de Imagens",
      description: "Jogo de associação cognitiva",
      content: "🧩 Este é o jogo de associação de imagens! Em breve estará totalmente funcional."
    }
  };
  const game = gameInfo[gameId] || {
    title: "Jogo não encontrado",
    description: "Este jogo ainda não foi implementado",
    content: "❌ Jogo não encontrado"
  };
  return /* @__PURE__ */ React.createElement("div", { style: {
    padding: "2rem",
    textAlign: "center",
    background: "rgba(255, 255, 255, 0.95)",
    borderRadius: "1rem",
    margin: "2rem",
    boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)"
  }, __self: void 0, __source: {
    fileName: _jsxFileName$2,
    lineNumber: 138,
    columnNumber: 5
  } }, /* @__PURE__ */ React.createElement("h1", { style: {
    color: "#0066cc",
    marginBottom: "1rem"
  }, __self: void 0, __source: {
    fileName: _jsxFileName$2,
    lineNumber: 146,
    columnNumber: 7
  } }, game.title), /* @__PURE__ */ React.createElement("p", { style: {
    color: "#666",
    marginBottom: "2rem",
    fontSize: "1.1rem"
  }, __self: void 0, __source: {
    fileName: _jsxFileName$2,
    lineNumber: 149,
    columnNumber: 7
  } }, game.description), /* @__PURE__ */ React.createElement("div", { style: {
    padding: "3rem",
    background: "#f8f9fa",
    borderRadius: "0.5rem",
    marginBottom: "2rem",
    fontSize: "1.2rem"
  }, __self: void 0, __source: {
    fileName: _jsxFileName$2,
    lineNumber: 152,
    columnNumber: 7
  } }, game.content));
};
var _jsxFileName$1 = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\components\\pages\\App.jsx";
const About = reactExports.lazy(() => __vitePreload(() => import("./About-3oLEsZnh.js"), true ? __vite__mapDeps([31,3,4,1,2,6,7,8,5,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,0,30,28,29,32]) : void 0));
const AdminPanel = reactExports.lazy(() => __vitePreload(() => import("./admin-D2mpdgvV.js").then((n) => n.A), true ? __vite__mapDeps([9,3,4,1,2,6,7,8,5,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,0,30,28,29,10]) : void 0));
const AccessibilityPage = reactExports.lazy(() => __vitePreload(() => import("./AccessibilityPage-B2X7xTNK.js"), true ? __vite__mapDeps([33,3,4,1,2,6,7,8,5,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,0,30,28,29,34]) : void 0));
const UserProfiles = reactExports.lazy(() => __vitePreload(() => import("./UserProfiles-Dd9TdgTt.js"), true ? __vite__mapDeps([35,3,4,1,2,6,7,8,5,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,0,30,28,29,36]) : void 0));
const DashboardContainer = reactExports.lazy(() => __vitePreload(() => import("./dashboard-DanqcTsU.js").then((n) => n.D), true ? __vite__mapDeps([2,3,4,1,8,13,6,7,14,11,15,16,17,18,19,20,21,22,23,24,25,26,27,0,30,28,29,5,9,10,12]) : void 0));
function App() {
  const [currentActivity, setCurrentActivity] = reactExports.useState("home");
  const [currentPage, setCurrentPage] = reactExports.useState("home");
  const handleLogoClick = () => {
    setCurrentActivity("home");
    setCurrentPage("home");
  };
  const handleActivityChange = (activityId) => {
    const gameIds = ["letter-recognition", "number-counting", "memory-game", "musical-sequence", "color-match", "image-association", "creative-painting", "padroes-visuais", "quebra-cabeca", "letters", "numbers", "memory", "music", "colors", "images", "patterns"];
    if (gameIds.includes(activityId)) {
      setCurrentActivity(activityId);
      setCurrentPage("game");
    } else {
      setCurrentActivity(activityId);
      setCurrentPage("home");
    }
  };
  const handleToolSelect = (toolId) => {
    if (toolId === "about-info") {
      setCurrentPage("about");
    } else if (toolId === "admin-panel") {
      setCurrentPage("admin");
    } else if (toolId === "accessibility-settings") {
      setCurrentPage("accessibility");
    } else if (toolId === "user-profiles") {
      setCurrentPage("profiles");
    } else if (toolId === "dashboard-performance") {
      setCurrentPage("dashboards");
    } else {
      setCurrentActivity(toolId);
      setCurrentPage("home");
    }
  };
  const handleBackToHome = () => {
    setCurrentPage("home");
    setCurrentActivity("home");
  };
  const LoadingFallback = () => /* @__PURE__ */ React.createElement("div", { className: "loading-container", style: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "60vh",
    fontSize: "18px",
    color: "#666"
  }, __self: this, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 74,
    columnNumber: 5
  } }, /* @__PURE__ */ React.createElement("div", { __self: this, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 82,
    columnNumber: 7
  } }, "Carregando..."));
  return /* @__PURE__ */ React.createElement("div", { className: "app", __self: this, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 87,
    columnNumber: 5
  } }, /* @__PURE__ */ React.createElement(Header, { onLogoClick: handleLogoClick, __self: this, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 88,
    columnNumber: 7
  } }), /* @__PURE__ */ React.createElement(reactExports.Suspense, { fallback: /* @__PURE__ */ React.createElement(LoadingFallback, { __self: this, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 90,
    columnNumber: 27
  } }), __self: this, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 90,
    columnNumber: 7
  } }, currentPage === "about" ? /* @__PURE__ */ React.createElement(About, { onBackToHome: handleBackToHome, __self: this, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 92,
    columnNumber: 11
  } }) : currentPage === "admin" ? /* @__PURE__ */ React.createElement(AdminPanel, { onBack: handleBackToHome, __self: this, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 94,
    columnNumber: 11
  } }) : currentPage === "accessibility" ? /* @__PURE__ */ React.createElement(AccessibilityPage, { onBack: handleBackToHome, __self: this, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 96,
    columnNumber: 11
  } }) : currentPage === "profiles" ? /* @__PURE__ */ React.createElement(UserProfiles, { onBack: handleBackToHome, __self: this, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 98,
    columnNumber: 11
  } }) : currentPage === "dashboards" ? /* @__PURE__ */ React.createElement(DashboardContainer, { onBack: handleBackToHome, __self: this, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 100,
    columnNumber: 11
  } }) : currentPage === "game" ? /* @__PURE__ */ React.createElement(GamePage, { gameId: currentActivity, onBack: handleBackToHome, __self: this, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 102,
    columnNumber: 11
  } }) : /* @__PURE__ */ React.createElement("main", { className: "main-content", __self: this, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 104,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement(DonationBanner, { __self: this, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 105,
    columnNumber: 13
  } }), /* @__PURE__ */ React.createElement(ActivitiesGridComponent, { onActivitySelect: handleActivityChange, __self: this, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 106,
    columnNumber: 13
  } }), /* @__PURE__ */ React.createElement(ToolsSection, { onToolSelect: handleToolSelect, __self: this, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 107,
    columnNumber: 13
  } }))), /* @__PURE__ */ React.createElement(Footer, { currentActivity, onActivityChange: handleActivityChange, __self: this, __source: {
    fileName: _jsxFileName$1,
    lineNumber: 112,
    columnNumber: 7
  } }));
}
var _jsxFileName = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\main.jsx";
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 6e4,
      // 1 minuto
      refetchOnWindowFocus: false
    }
  }
});
async function initializeApp() {
  try {
    console.info("🚀 Inicializando Portal Betina V3...");
    const system = await initializePortalBetinaSystem();
    ReactDOM.createRoot(document.getElementById("root")).render(/* @__PURE__ */ React.createElement(React.StrictMode, { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 43,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement(HelmetProvider, { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 44,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement(QueryClientProvider, { client: queryClient, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 45,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement(SystemProvider, { system, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 46,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement(DatabaseProvider, { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 47,
      columnNumber: 15
    } }, /* @__PURE__ */ React.createElement(PremiumProvider, { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 48,
      columnNumber: 17
    } }, /* @__PURE__ */ React.createElement(AdminProvider, { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 49,
      columnNumber: 19
    } }, /* @__PURE__ */ React.createElement(AccessibilityProvider, { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 50,
      columnNumber: 21
    } }, /* @__PURE__ */ React.createElement(App, { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 51,
      columnNumber: 23
    } }))))))))));
    console.info("✅ Portal Betina V3 inicializado com sucesso!");
  } catch (error) {
    console.error("❌ Erro fatal na inicialização do Portal Betina V3:", error);
    ReactDOM.createRoot(document.getElementById("root")).render(/* @__PURE__ */ React.createElement("div", { className: "critical-error", __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 68,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("h1", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 69,
      columnNumber: 9
    } }, "Erro na Inicialização"), /* @__PURE__ */ React.createElement("p", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 70,
      columnNumber: 9
    } }, "Não foi possível inicializar o sistema. Tente recarregar a página."), /* @__PURE__ */ React.createElement("button", { onClick: () => window.location.reload(), __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 71,
      columnNumber: 9
    } }, "Recarregar"), /* @__PURE__ */ React.createElement("div", { className: "error-details", __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 75,
      columnNumber: 11
    } }, /* @__PURE__ */ React.createElement("p", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 76,
      columnNumber: 13
    } }, error.message), /* @__PURE__ */ React.createElement("pre", { __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 77,
      columnNumber: 13
    } }, error.stack))));
  }
}
initializeApp();
//# sourceMappingURL=index-BdBwru_X.js.map
