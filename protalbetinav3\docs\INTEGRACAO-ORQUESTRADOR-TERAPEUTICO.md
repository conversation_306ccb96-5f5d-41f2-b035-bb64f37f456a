# Atualização: Orquestrador Terapêutico

Um novo componente foi adicionado ao Sistema Integrado da V3: o **Orquestrador Terapêutico**. Este componente migra as capacidades avançadas de análise terapêutica da V2 para a nova arquitetura do Portal Betina V3.

## Orquestrador Terapêutico

O Orquestrador Terapêutico é responsável por processar métricas de jogos e gerar recomendações terapêuticas adaptativas. Ele se integra ao Sistema Integrado da V3 através de um adaptador, mantendo a compatibilidade com as funcionalidades existentes.

### Características Principais

- Processamento de métricas de jogos com foco terapêutico
- Geração de recomendações adaptativas baseadas em padrões detectados
- Análise de progresso terapêutico ao longo do tempo
- Integração com o sistema de resiliência da V3
- Suporte a diferentes modos terapêuticos (avaliação, monitoramento, intervenção)

### Como Utilizar

#### 1. Através do Sistema Integrado

```javascript
// Obter sistema integrado
const system = await createIntegratedSystem();

// Processar métricas de jogo
const result = await system.processGameMetrics(userId, gameId, metricsData);

// Obter recomendações terapêuticas
const recommendations = await system.getTherapeuticRecommendations(userId);
```

#### 2. Em Componentes React

```jsx
// Importar hooks necessários
import { useSystem } from '../hooks';
import { useSystemContext } from '../components/context/SystemContext';

function TherapeuticGameComponent({ userId, gameId }) {
  const { system } = useSystem();
  const { integratedSystem } = useSystemContext();
  const [recommendations, setRecommendations] = useState([]);
  
  // Processar métricas ao completar o jogo
  const handleGameComplete = async (metrics) => {
    const result = await integratedSystem.processGameMetrics(userId, gameId, metrics);
    
    if (result.success) {
      setRecommendations(result.recommendations);
    }
  };
  
  // Implementação do componente...
}
```

#### 3. Através do Sistema de Eventos

```javascript
// Processar métricas via eventos
const result = await dispatchEvent('therapeutic:process-metrics', {
  userId,
  gameId,
  metrics: gameMetrics
});

// Obter recomendações via eventos
const recommendations = await dispatchEvent('therapeutic:get-recommendations', {
  userId
});
```

### Documentação Detalhada

Para mais informações sobre como implementar e utilizar o orquestrador terapêutico, consulte o guia detalhado em:

- [Guia do Orquestrador Terapêutico](./docs/arquitetura/GUIA_ORQUESTRADOR_TERAPEUTICO.md)
- [Exemplo de Implementação](./src/examples/ExemploOrquestradorTerapeutico.jsx)
