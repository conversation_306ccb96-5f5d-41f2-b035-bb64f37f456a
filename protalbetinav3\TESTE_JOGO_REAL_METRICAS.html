<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 Teste Real: Jogo + Métricas</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .status {
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: rgba(76, 175, 80, 0.3); border-left: 4px solid #4CAF50; }
        .error { background: rgba(244, 67, 54, 0.3); border-left: 4px solid #f44336; }
        .warning { background: rgba(255, 193, 7, 0.3); border-left: 4px solid #FFC107; }
        .info { background: rgba(33, 150, 243, 0.3); border-left: 4px solid #2196F3; }
        
        button {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .metrics-display {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .step {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }
        .step-number {
            background: #4ECDC4;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .dashboard-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .dashboard-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        .dashboard-card h3 {
            margin: 0 0 10px 0;
            color: #4ECDC4;
        }
        .dashboard-card .value {
            font-size: 24px;
            font-weight: bold;
            color: white;
        }
        
        .game-link {
            display: inline-block;
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .game-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #4ECDC4;
        }
        .instructions h3 {
            margin-top: 0;
            color: #4ECDC4;
        }
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 8px 0;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 Teste Real: Jogo + Métricas</h1>
        <p>Este teste verifica se as métricas dos jogos estão sendo salvas corretamente no sistema real.</p>

        <!-- INFORMAÇÕES DO SISTEMA -->
        <div class="test-section">
            <h2>🔧 Configuração do Sistema</h2>
            <div class="dashboard-status">
                <div class="dashboard-card">
                    <h3>Frontend (Jogos)</h3>
                    <div class="value">Porta 5173</div>
                    <small>Interface dos jogos</small>
                </div>
                <div class="dashboard-card">
                    <h3>Backend (API)</h3>
                    <div class="value">Porta 3000</div>
                    <small>Endpoints e métricas</small>
                </div>
                <div class="dashboard-card">
                    <h3>Arquitetura</h3>
                    <div class="value">Frontend → Backend</div>
                    <small>Vite + Express</small>
                </div>
            </div>
        </div>
        
        <!-- INSTRUÇÕES -->
        <div class="instructions">
            <h3>📋 Como Fazer o Teste:</h3>
            <ol>
                <li><strong>Verificar Login:</strong> Clique em "Verificar Login do Dashboard" abaixo</li>
                <li><strong>Fazer Login (se necessário):</strong> Clique em "Simular Login" se não estiver logado</li>
                <li><strong>Abrir o Jogo:</strong> Clique no link "🎮 Abrir Portal Betina (Frontend)" - porta 5173</li>
                <li><strong>Jogar:</strong> No Portal Betina, clique em "Letras" ou "Reconhecimento de Letras" e jogue algumas rodadas</li>
                <li><strong>Testar API:</strong> Clique em "Testar Todos" para verificar se o backend (porta 3000) está funcionando</li>
                <li><strong>Verificar Métricas:</strong> Volte aqui e clique em "Verificar Métricas Salvas"</li>
                <li><strong>Verificar Dashboard:</strong> Clique em "Abrir Dashboard de Métricas" para ver se os dados aparecem</li>
            </ol>
        </div>
        
        <!-- ETAPA 1: Verificar Login -->
        <div class="test-section">
            <h2>🔐 ETAPA 1: Verificar Login do Dashboard</h2>
            <div class="step">
                <div class="step-number">1</div>
                <div>
                    <strong>Status do login no dashboard</strong>
                    <div id="loginStatus" class="status info">Verificando...</div>
                </div>
            </div>
            <button onclick="checkDashboardLogin()">🔍 Verificar Login</button>
            <button onclick="simulateLogin()">🔑 Simular Login</button>
            <button onclick="clearLogin()">🚪 Limpar Login</button>
        </div>
        
        <!-- ETAPA 2: Abrir Jogo -->
        <div class="test-section">
            <h2>🎮 ETAPA 2: Jogar no Portal Betina</h2>
            <div class="step">
                <div class="step-number">2</div>
                <div>
                    <strong>Abrir o Portal Betina e jogar</strong>
                    <div class="status info">Clique no link abaixo para abrir o jogo em nova aba</div>
                </div>
            </div>
            <a href="http://localhost:5173" target="_blank" class="game-link">🎮 Abrir Portal Betina (Frontend)</a>
            <div style="margin-top: 15px;">
                <strong>No Portal Betina:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Clique em "Letras" ou procure por "Reconhecimento de Letras"</li>
                    <li>Escolha uma dificuldade (Fácil, Médio ou Avançado)</li>
                    <li>Jogue pelo menos 2-3 rodadas</li>
                    <li>Volte aqui para verificar se as métricas foram salvas</li>
                </ul>
            </div>
        </div>
        
        <!-- ETAPA 3: Verificar Métricas -->
        <div class="test-section">
            <h2>📊 ETAPA 3: Verificar Métricas Salvas</h2>
            <div class="step">
                <div class="step-number">3</div>
                <div>
                    <strong>Verificar se métricas foram salvas no sistema</strong>
                    <div id="metricsStatus" class="status info">Aguardando verificação</div>
                </div>
            </div>
            <button onclick="checkMetrics()">📈 Verificar Métricas Salvas</button>
            <button onclick="checkLocalStorage()">💾 Verificar LocalStorage</button>
            <button onclick="checkSystemOrchestrator()">🎯 Verificar SystemOrchestrator</button>
            <button onclick="testSystemOrchestratorMetrics()">🔍 Testar SystemOrchestrator API</button>
            <button onclick="testDatabaseConnection()">🗄️ Testar Banco PostgreSQL</button>
        </div>
        
        <!-- ETAPA 4: Testar Endpoints da API -->
        <div class="test-section">
            <h2>🔌 ETAPA 4: Testar Endpoints da API</h2>
            <div class="step">
                <div class="step-number">4</div>
                <div>
                    <strong>Verificar se todos os endpoints estão funcionando</strong>
                    <div id="endpointsStatus" class="status info">Clique nos botões abaixo para testar</div>
                </div>
            </div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 15px 0;">
                <button onclick="testEndpoint('/api/public/health', 'GET')">🏥 Health Check</button>
                <button onclick="testEndpoint('/api/public/games', 'GET')">🎮 Lista de Jogos</button>
                <button onclick="testEndpoint('/api/public/activities', 'GET')">📋 Atividades</button>
                <button onclick="testEndpoint('/api/auth/login', 'GET')">🔐 Auth Login</button>
                <button onclick="testEndpoint('/api/public/metrics', 'GET')">📊 Métricas Públicas</button>
                <button onclick="testAllEndpoints()">🚀 Testar Todos</button>
            </div>
            <div id="endpointResults" class="metrics-display" style="max-height: 200px;">
                <div>⏰ Aguardando testes de endpoints...</div>
            </div>
        </div>

        <!-- ETAPA 5: Dashboard -->
        <div class="test-section">
            <h2>📋 ETAPA 5: Verificar Dashboard</h2>
            <div class="step">
                <div class="step-number">5</div>
                <div>
                    <strong>Abrir dashboard e verificar dados</strong>
                    <div class="status info">Clique no link abaixo para abrir o dashboard</div>
                </div>
            </div>
            <a href="http://localhost:5173" target="_blank" class="game-link" onclick="openDashboard()">📋 Abrir Dashboard de Métricas (Frontend)</a>
            <div style="margin-top: 15px;">
                <strong>No Dashboard:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Procure por "Dashboard" ou "Métricas" na interface</li>
                    <li>Verifique se aparecem dados reais (não MOCK/Placeholder)</li>
                    <li>Procure por dados do jogo "Letter Recognition" ou "Reconhecimento de Letras"</li>
                </ul>
            </div>
        </div>
        
        <!-- STATUS GERAL -->
        <div class="test-section">
            <h2>📊 Status Geral do Teste</h2>
            <div class="dashboard-status">
                <div class="dashboard-card">
                    <h3>Login Dashboard</h3>
                    <div id="dashboardLoginValue" class="value">❓</div>
                </div>
                <div class="dashboard-card">
                    <h3>Jogo Testado</h3>
                    <div id="gameTestedValue" class="value">Não</div>
                </div>
                <div class="dashboard-card">
                    <h3>Métricas Encontradas</h3>
                    <div id="metricsFoundValue" class="value">0</div>
                </div>
                <div class="dashboard-card">
                    <h3>Status Final</h3>
                    <div id="finalStatusValue" class="value">Pendente</div>
                </div>
            </div>
        </div>
        
        <!-- LOG DE ATIVIDADES -->
        <div class="test-section">
            <h2>📝 Log de Atividades</h2>
            <div id="activityLog" class="metrics-display">
                <div>🚀 Sistema de teste inicializado</div>
                <div>⏰ Aguardando comandos...</div>
            </div>
            <button onclick="clearLog()">🧹 Limpar Log</button>
        </div>
    </div>

    <script>
        // Estado global do teste
        let testState = {
            loginActive: false,
            gameSessionDetected: false,
            metricsFound: 0,
            lastCheck: null
        };

        // Função para adicionar log
        function addLog(message, type = 'info') {
            const log = document.getElementById('activityLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #4ECDC4">[${timestamp}]</span> ${message}`;
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }

        // ETAPA 1: Verificar Login do Dashboard
        function checkDashboardLogin() {
            addLog('🔍 Verificando login do dashboard...');
            
            try {
                const authToken = localStorage.getItem('authToken');
                const userData = localStorage.getItem('userData');
                const sessionAuth = sessionStorage.getItem('betina_metrics_auth');
                
                if (authToken || userData || sessionAuth) {
                    testState.loginActive = true;
                    document.getElementById('loginStatus').className = 'status success';
                    document.getElementById('loginStatus').textContent = '✅ Login ativo detectado';
                    document.getElementById('dashboardLoginValue').textContent = '✅ ATIVO';
                    addLog('✅ Login do dashboard está ativo', 'success');
                    
                    if (userData) {
                        try {
                            const user = JSON.parse(userData);
                            addLog(`👤 Usuário logado: ${user.email || 'N/A'}`, 'info');
                        } catch (e) {
                            addLog('⚠️ Dados de usuário inválidos', 'warning');
                        }
                    }

                    // 🔍 DEBUG: Testar se SystemOrchestrator detecta o login
                    addLog('🔍 DEBUG - Dados de autenticação encontrados:', 'info');
                    addLog(`   authToken: ${authToken ? 'PRESENTE (' + authToken.substring(0, 20) + '...)' : 'AUSENTE'}`, 'info');
                    addLog(`   userData: ${userData ? 'PRESENTE' : 'AUSENTE'}`, 'info');
                    addLog(`   sessionAuth: ${sessionAuth ? 'PRESENTE' : 'AUSENTE'}`, 'info');
                } else {
                    testState.loginActive = false;
                    document.getElementById('loginStatus').className = 'status error';
                    document.getElementById('loginStatus').textContent = '❌ Nenhum login detectado';
                    document.getElementById('dashboardLoginValue').textContent = '❌ INATIVO';
                    addLog('❌ Nenhum login ativo no dashboard', 'error');
                    addLog('⚠️ IMPORTANTE: Sem login, as métricas não serão salvas!', 'warning');
                }
            } catch (error) {
                addLog(`❌ Erro ao verificar login: ${error.message}`, 'error');
            }
        }

        async function simulateLogin() {
            addLog('🔑 Fazendo login real no sistema...', 'info');

            try {
                // Fazer login real via API
                const response = await fetch('http://localhost:3000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    addLog('✅ Login real realizado com sucesso', 'success');
                    addLog(`👤 Usuário: ${data.user.email}`, 'info');
                    addLog(`🎯 Role: ${data.user.role}`, 'info');

                    // Salvar dados de autenticação
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('userData', JSON.stringify(data.user));
                    sessionStorage.setItem('betina_metrics_auth', 'authenticated');

                    checkDashboardLogin();
                } else {
                    const errorData = await response.json();
                    addLog(`❌ Erro no login: ${errorData.message || 'Erro desconhecido'}`, 'error');
                }
            } catch (error) {
                addLog(`❌ Erro ao conectar com API de login: ${error.message}`, 'error');
                addLog('🔄 Usando login offline como fallback...', 'warning');

                // Fallback para login offline
                const userData = {
                    email: '<EMAIL>',
                    name: 'Admin (Offline)',
                    role: 'admin',
                    loginTime: new Date().toISOString()
                };

                localStorage.setItem('authToken', 'offline-token-' + Date.now());
                localStorage.setItem('userData', JSON.stringify(userData));
                sessionStorage.setItem('betina_metrics_auth', 'authenticated');

                addLog('✅ Login offline simulado', 'warning');
                checkDashboardLogin();
            }
        }

        function clearLogin() {
            addLog('🚪 Limpando dados de login...');
            
            localStorage.removeItem('authToken');
            localStorage.removeItem('userData');
            sessionStorage.removeItem('betina_metrics_auth');
            
            addLog('✅ Dados de login removidos', 'success');
            addLog('⚠️ Métricas dos jogos NÃO serão salvas até fazer login novamente', 'warning');
            checkDashboardLogin();
        }

        // ETAPA 3: Verificar Métricas
        function checkMetrics() {
            addLog('📈 Verificando métricas salvas...', 'info');
            testState.lastCheck = new Date();

            // 🚫 SISTEMA CORRIGIDO - NÃO USAR LOCALSTORAGE PARA MÉTRICAS
            addLog('🔍 IMPORTANTE: Métricas devem ser salvas APENAS no banco PostgreSQL', 'warning');
            addLog('🚫 localStorage NÃO deve ser usado para métricas (conforme solicitado)', 'warning');

            // Verificar se há dados incorretos no localStorage (para debug)
            let incorrectDataCount = 0;
            
            // Verificar diferentes chaves de métricas
            const metricsKeys = [
                'letter_recognition_metrics',
                'game_metrics',
                'session_metrics',
                'betina_game_data',
                'portal_betina_sessions',
                'system_orchestrator_sessions',
                'orchestrator_session_data',
                'unified_game_metrics'
            ];
            
            metricsKeys.forEach(key => {
                const data = localStorage.getItem(key);
                if (data) {
                    incorrectDataCount++;
                    addLog(`❌ PROBLEMA: ${key} encontrado no localStorage (deve estar no banco)`, 'error');
                }
            });
            
            testState.metricsFound = incorrectDataCount;
            document.getElementById('metricsFoundValue').textContent = `${incorrectDataCount} (localStorage - INCORRETO)`;

            if (incorrectDataCount > 0) {
                document.getElementById('metricsStatus').className = 'status error';
                document.getElementById('metricsStatus').textContent = `❌ ${incorrectDataCount} métricas no localStorage (INCORRETO)`;
                addLog(`⚠️ PROBLEMA: ${incorrectDataCount} chaves de métricas no localStorage (devem estar no banco)`, 'error');
                document.getElementById('finalStatusValue').textContent = '❌ Dados no local errado';
            } else {
                document.getElementById('metricsStatus').className = 'status success';
                document.getElementById('metricsStatus').textContent = '✅ Correto: Nenhuma métrica no localStorage';
                addLog('✅ Correto: Nenhuma métrica encontrada no localStorage', 'success');
                addLog('🔍 Para verificar métricas reais, use o botão "Testar Banco PostgreSQL"', 'info');
                document.getElementById('finalStatusValue').textContent = '✅ Configuração correta';
            }
        }

        // 🔍 Função para testar SystemOrchestrator diretamente
        async function testSystemOrchestratorMetrics() {
            addLog('🔍 Testando SystemOrchestrator diretamente...', 'info');

            // 1. Testar detecção de login do dashboard
            try {
                const loginResponse = await fetch('http://localhost:3000/api/public/debug/dashboard-login', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': localStorage.getItem('authToken') ? `Bearer ${localStorage.getItem('authToken')}` : ''
                    }
                });

                if (loginResponse.ok) {
                    const loginData = await loginResponse.json();
                    addLog('🔐 Teste de Login do Dashboard:', 'info');
                    addLog(`   Login detectado: ${loginData.loginDetected ? 'SIM' : 'NÃO'}`, loginData.loginDetected ? 'success' : 'error');
                    addLog(`   Motivo: ${loginData.reason}`, 'info');
                    addLog(`   SystemOrchestrator disponível: ${loginData.systemOrchestratorAvailable ? 'SIM' : 'NÃO'}`, 'info');

                    if (loginData.error) {
                        addLog(`   Erro: ${loginData.error}`, 'error');
                    }
                } else {
                    const errorData = await loginResponse.json().catch(() => ({ error: 'Resposta inválida' }));
                    addLog(`❌ Erro ao testar login do dashboard: ${loginResponse.status}`, 'error');
                    addLog(`   Detalhes: ${errorData.error || errorData.message || 'Erro desconhecido'}`, 'error');
                }
            } catch (error) {
                addLog(`❌ Erro ao conectar com endpoint de login: ${error.message}`, 'error');
            }

            // 2. Testar status geral do sistema
            try {
                const statusResponse = await fetch('http://localhost:3000/api/public/debug/system-status', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': localStorage.getItem('authToken') ? `Bearer ${localStorage.getItem('authToken')}` : ''
                    }
                });

                if (statusResponse.ok) {
                    const statusData = await statusResponse.json();
                    addLog('📊 Status do SystemOrchestrator:', 'info');
                    addLog(`   Sessões ativas: ${statusData.activeSessions ? statusData.activeSessions.length : 0}`, 'info');
                    addLog(`   Tamanho sessionData: ${statusData.sessionDataSize || 0}`, 'info');
                    addLog(`   Dashboard login: ${statusData.dashboardLogin ? 'ATIVO' : 'INATIVO'}`, statusData.dashboardLogin ? 'success' : 'error');

                    if (statusData.error) {
                        addLog(`   Erro: ${statusData.error}`, 'error');
                    }
                } else {
                    const errorData = await statusResponse.json().catch(() => ({ error: 'Resposta inválida' }));
                    addLog(`❌ Erro ao verificar status do sistema: ${statusResponse.status}`, 'error');
                    addLog(`   Detalhes: ${errorData.error || errorData.message || 'Erro desconhecido'}`, 'error');
                }
            } catch (error) {
                addLog(`❌ Erro ao verificar status: ${error.message}`, 'error');
            }

            // 3. Testar sessões armazenadas
            try {
                const sessionResponse = await fetch('http://localhost:3000/api/public/debug/sessions', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': localStorage.getItem('authToken') ? `Bearer ${localStorage.getItem('authToken')}` : ''
                    }
                });

                if (sessionResponse.ok) {
                    const sessionData = await sessionResponse.json();
                    addLog(`� Sessões no SystemOrchestrator:`, 'info');
                    addLog(`   Memória: ${sessionData.memoryCount || 0} sessões`, 'info');
                    addLog(`   Banco: ${sessionData.databaseCount || 0} sessões`, 'info');
                    addLog(`   Total: ${sessionData.totalCount || 0} sessões`, 'info');

                    if (sessionData.databaseSessions && sessionData.databaseSessions.length > 0) {
                        const lastSession = sessionData.databaseSessions[0];
                        addLog(`   Última sessão (banco): ${lastSession.timestamp || 'N/A'}`, 'info');
                        addLog(`   Jogo: ${lastSession.gameType || 'N/A'}`, 'info');
                        addLog(`   Score: ${lastSession.score || 0}`, 'info');
                    }

                    addLog(`   Dashboard login: ${sessionData.dashboardLogin ? 'ATIVO' : 'INATIVO'}`, sessionData.dashboardLogin ? 'success' : 'error');
                    if (sessionData.error) {
                        addLog(`   Erro: ${sessionData.error}`, 'error');
                    }
                } else {
                    const errorData = await sessionResponse.json().catch(() => ({ error: 'Resposta inválida' }));
                    addLog(`❌ Erro ao verificar sessões: ${sessionResponse.status}`, 'error');
                    addLog(`   Detalhes: ${errorData.error || errorData.message || 'Erro desconhecido'}`, 'error');
                }
            } catch (error) {
                addLog(`❌ Erro ao verificar sessões: ${error.message}`, 'error');
            }
        }

        // 🗄️ Função para testar conexão com banco PostgreSQL
        async function testDatabaseConnection() {
            addLog('🗄️ Testando conexão com banco PostgreSQL...', 'info');

            try {
                const response = await fetch('http://localhost:3000/api/public/debug/database', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': localStorage.getItem('authToken') ? `Bearer ${localStorage.getItem('authToken')}` : ''
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    addLog('🗄️ Status do Banco PostgreSQL:', 'info');
                    addLog(`   Conectado: ${data.connected ? 'SIM' : 'NÃO'}`, data.connected ? 'success' : 'error');
                    addLog(`   Tabelas existem: ${data.tablesExist ? 'SIM' : 'NÃO'}`, data.tablesExist ? 'success' : 'error');
                    addLog(`   Sessões no banco: ${data.sessionCount || 0}`, 'info');

                    if (data.error) {
                        addLog(`   Erro: ${data.error}`, 'error');
                    }

                    if (data.connected && data.tablesExist && data.sessionCount > 0) {
                        addLog('✅ Banco PostgreSQL funcionando com dados!', 'success');
                        document.getElementById('metricsFoundValue').textContent = `${data.sessionCount} (banco - CORRETO)`;
                        document.getElementById('finalStatusValue').textContent = '✅ Dados no banco';
                    } else if (data.connected && data.tablesExist) {
                        addLog('⚠️ Banco conectado mas sem dados de sessões', 'warning');
                        addLog('💡 Jogue algumas partidas para gerar dados', 'info');
                        document.getElementById('finalStatusValue').textContent = '⚠️ Banco vazio';
                    } else {
                        addLog('❌ Problema com banco PostgreSQL', 'error');
                        document.getElementById('finalStatusValue').textContent = '❌ Erro no banco';
                    }
                } else {
                    const errorData = await response.json().catch(() => ({ error: 'Resposta inválida' }));
                    addLog(`❌ Erro ao testar banco PostgreSQL: ${response.status}`, 'error');
                    addLog(`   Detalhes: ${errorData.error || errorData.message || 'Erro desconhecido'}`, 'error');
                }
            } catch (error) {
                addLog(`❌ Erro ao conectar com banco: ${error.message}`, 'error');
            }
        }

        function checkLocalStorage() {
            addLog('💾 Verificando todo o localStorage...');
            
            let totalKeys = 0;
            let gameRelatedKeys = 0;
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                totalKeys++;
                
                if (key.includes('game') || key.includes('metric') || key.includes('session') || 
                    key.includes('betina') || key.includes('letter') || key.includes('portal')) {
                    gameRelatedKeys++;
                    const value = localStorage.getItem(key);
                    addLog(`🔍 ${key}: ${value ? value.substring(0, 100) + '...' : 'vazio'}`, 'info');
                }
            }
            
            addLog(`📊 Total de chaves no localStorage: ${totalKeys}`, 'info');
            addLog(`🎮 Chaves relacionadas a jogos: ${gameRelatedKeys}`, 'info');
        }

        function checkSystemOrchestrator() {
            addLog('🎯 Verificando integração com SystemOrchestrator...');
            
            // Simular verificação do SystemOrchestrator
            setTimeout(() => {
                if (testState.loginActive) {
                    addLog('✅ SystemOrchestrator: checkDashboardLogin() retornaria TRUE', 'success');
                    addLog('✅ Métricas dos jogos DEVEM ser salvas no banco', 'success');
                } else {
                    addLog('❌ SystemOrchestrator: checkDashboardLogin() retornaria FALSE', 'error');
                    addLog('🎮 Modo gratuito: métricas NÃO são salvas no banco', 'warning');
                }
                
                addLog('🔗 PortalBetinaV3 → SystemOrchestrator → checkDashboardLogin() → Banco', 'info');
            }, 1000);
        }

        // ETAPA 4: Testar Endpoints da API
        async function testEndpoint(endpoint, method = 'GET') {
            const resultsDiv = document.getElementById('endpointResults');
            const url = `http://localhost:3000${endpoint}`;

            addLog(`🔌 Testando ${method} ${endpoint}...`);

            try {
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();
                const status = response.ok ? '✅' : '❌';
                const statusText = response.ok ? 'OK' : 'ERRO';

                const resultEntry = document.createElement('div');
                resultEntry.innerHTML = `
                    <div style="margin: 5px 0; padding: 5px; background: rgba(255,255,255,0.1); border-radius: 5px;">
                        <strong>${status} ${method} ${endpoint}</strong> (${response.status})
                        <br><small>${JSON.stringify(data).substring(0, 100)}...</small>
                    </div>
                `;
                resultsDiv.appendChild(resultEntry);
                resultsDiv.scrollTop = resultsDiv.scrollHeight;

                addLog(`${status} ${endpoint}: ${response.status} - ${statusText}`, response.ok ? 'success' : 'error');

                return { endpoint, status: response.status, ok: response.ok, data };
            } catch (error) {
                const resultEntry = document.createElement('div');
                resultEntry.innerHTML = `
                    <div style="margin: 5px 0; padding: 5px; background: rgba(255,0,0,0.2); border-radius: 5px;">
                        <strong>❌ ${method} ${endpoint}</strong> - ERRO DE CONEXÃO
                        <br><small>${error.message}</small>
                    </div>
                `;
                resultsDiv.appendChild(resultEntry);
                resultsDiv.scrollTop = resultsDiv.scrollHeight;

                addLog(`❌ Erro ao testar ${endpoint}: ${error.message}`, 'error');
                return { endpoint, error: error.message, ok: false };
            }
        }

        async function testAllEndpoints() {
            addLog('🚀 Iniciando teste completo de endpoints...');
            document.getElementById('endpointResults').innerHTML = '<div>🔄 Testando todos os endpoints...</div>';

            const endpoints = [
                { path: '/api/public/health', method: 'GET' },
                { path: '/api/public/games', method: 'GET' },
                { path: '/api/public/activities', method: 'GET' },
                { path: '/api/auth/login', method: 'GET' },
                { path: '/api/public/metrics', method: 'GET' }
            ];

            let successCount = 0;
            let totalCount = endpoints.length;

            for (const endpoint of endpoints) {
                const result = await testEndpoint(endpoint.path, endpoint.method);
                if (result.ok) successCount++;

                // Aguardar um pouco entre requests
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            const successRate = Math.round((successCount / totalCount) * 100);
            addLog(`📊 Teste completo: ${successCount}/${totalCount} endpoints funcionando (${successRate}%)`,
                   successRate >= 80 ? 'success' : successRate >= 50 ? 'warning' : 'error');

            document.getElementById('endpointsStatus').className = successRate >= 80 ? 'status success' :
                                                                  successRate >= 50 ? 'status warning' : 'status error';
            document.getElementById('endpointsStatus').textContent =
                `${successCount}/${totalCount} endpoints funcionando (${successRate}%)`;
        }

        function openDashboard() {
            addLog('📋 Abrindo dashboard de métricas...');
            addLog('💡 Procure por seções como "Performance", "Métricas", "Relatórios"', 'info');
            document.getElementById('gameTestedValue').textContent = 'Sim';
        }

        function clearLog() {
            document.getElementById('activityLog').innerHTML = `
                <div>🚀 Sistema de teste inicializado</div>
                <div>⏰ Log limpo - aguardando comandos...</div>
            `;
        }

        // Inicializar teste
        window.onload = function() {
            addLog('🚀 Sistema de teste carregado');
            addLog('📋 Siga as instruções acima para testar o sistema completo');
            checkDashboardLogin();
        };

        // Verificar métricas automaticamente a cada 30 segundos
        setInterval(() => {
            if (testState.lastCheck && (Date.now() - testState.lastCheck.getTime()) < 60000) {
                // Só verificar automaticamente se a última verificação foi há menos de 1 minuto
                checkMetrics();
            }
        }, 30000);
    </script>
</body>
</html>
