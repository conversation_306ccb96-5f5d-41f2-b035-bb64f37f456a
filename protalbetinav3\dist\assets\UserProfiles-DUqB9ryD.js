import { r as reactExports, j as jsxDevRuntimeExports } from "./vendor-react-BH-kks1U.js";
import "./vendor-misc-BhjEiCpb.js";
import "./services-Ckq1alRq.js";
import "./dashboard-D6oq-mHv.js";
import "./context-CJb-Kg-5.js";
import "./hooks-DiB_syzW.js";
import "./vendor-utils-CjlX8hrF.js";
import "./vendor-charts-JJkNskvH.js";
import "./admin-AVDlea_I.js";
import "./utils-C8SspVp8.js";
import "./game-colors-DknHlpST.js";
import "./game-association-1fe4bzjE.js";
import "./game-letters-C11f_WoE.js";
import "./game-memory-Q6N7kS_-.js";
import "./vendor-motion-CThs1zaH.js";
import "./game-musical-4K52sZ4i.js";
import "./game-patterns-C1u1YIjS.js";
import "./game-puzzle-y0iWrjae.js";
import "./game-numbers-CLPWZorL.js";
import "./game-creative-danUmc-P.js";
const container = "_container_2uq7c_15";
const header = "_header_2uq7c_37";
const mainTitle = "_mainTitle_2uq7c_49";
const subtitle = "_subtitle_2uq7c_65";
const section = "_section_2uq7c_81";
const sectionTitle = "_sectionTitle_2uq7c_93";
const sectionContent = "_sectionContent_2uq7c_123";
const activeProfileCard = "_activeProfileCard_2uq7c_157";
const activeProfileAvatar = "_activeProfileAvatar_2uq7c_181";
const activeProfileInfo = "_activeProfileInfo_2uq7c_205";
const activeProfileName = "_activeProfileName_2uq7c_213";
const activeProfileStats = "_activeProfileStats_2uq7c_227";
const statItem = "_statItem_2uq7c_239";
const statIcon = "_statIcon_2uq7c_255";
const profilesGrid = "_profilesGrid_2uq7c_265";
const profileCard = "_profileCard_2uq7c_279";
const active = "_active_2uq7c_157";
const profileAvatar = "_profileAvatar_2uq7c_325";
const profileInfo = "_profileInfo_2uq7c_353";
const profileName = "_profileName_2uq7c_363";
const profileAge = "_profileAge_2uq7c_377";
const profileStats = "_profileStats_2uq7c_377";
const profileDeleteBtn = "_profileDeleteBtn_2uq7c_389";
const addProfileCard = "_addProfileCard_2uq7c_443";
const addProfileIcon = "_addProfileIcon_2uq7c_475";
const benefitsList = "_benefitsList_2uq7c_489";
const benefitItem = "_benefitItem_2uq7c_501";
const benefitEmoji = "_benefitEmoji_2uq7c_525";
const benefitText = "_benefitText_2uq7c_535";
const createProfileOverlay = "_createProfileOverlay_2uq7c_551";
const createProfileForm = "_createProfileForm_2uq7c_581";
const formHeader = "_formHeader_2uq7c_607";
const formTitle = "_formTitle_2uq7c_617";
const formSubtitle = "_formSubtitle_2uq7c_631";
const formGroup = "_formGroup_2uq7c_643";
const formLabel = "_formLabel_2uq7c_651";
const input = "_input_2uq7c_665";
const avatarSelector = "_avatarSelector_2uq7c_709";
const avatarOption = "_avatarOption_2uq7c_723";
const selected = "_selected_2uq7c_761";
const formActions = "_formActions_2uq7c_771";
const btn = "_btn_2uq7c_785";
const btnPrimary = "_btnPrimary_2uq7c_807";
const btnSecondary = "_btnSecondary_2uq7c_827";
const styles = {
  container,
  header,
  mainTitle,
  subtitle,
  section,
  sectionTitle,
  sectionContent,
  activeProfileCard,
  activeProfileAvatar,
  activeProfileInfo,
  activeProfileName,
  activeProfileStats,
  statItem,
  statIcon,
  profilesGrid,
  profileCard,
  active,
  profileAvatar,
  profileInfo,
  profileName,
  profileAge,
  profileStats,
  profileDeleteBtn,
  addProfileCard,
  addProfileIcon,
  benefitsList,
  benefitItem,
  benefitEmoji,
  benefitText,
  createProfileOverlay,
  createProfileForm,
  formHeader,
  formTitle,
  formSubtitle,
  formGroup,
  formLabel,
  input,
  avatarSelector,
  avatarOption,
  selected,
  formActions,
  btn,
  btnPrimary,
  btnSecondary
};
function UserProfiles() {
  const [profiles, setProfiles] = reactExports.useState([]);
  const [activeProfile, setActiveProfile] = reactExports.useState(null);
  const [showCreateForm, setShowCreateForm] = reactExports.useState(false);
  const [newProfile, setNewProfile] = reactExports.useState({
    name: "",
    age: "",
    avatar: "👶",
    preferences: {
      theme: "default",
      difficulty: "easy",
      soundEnabled: true,
      animationsEnabled: true
    }
  });
  const [profileStats2, setProfileStats] = reactExports.useState({});
  const [loadingStats, setLoadingStats] = reactExports.useState(false);
  const fetchProfileStats = async (profileId) => {
    if (!profileId) return;
    try {
      setLoadingStats(true);
      const response = await fetch(`/api/metrics/game-sessions?userId=${profileId}`);
      if (response.ok) {
        const data = await response.json();
        const stats = {
          gamesPlayed: data.sessions?.length || 0,
          totalTime: data.sessions?.reduce((total, session) => total + (session.duration || 0), 0) || 0,
          lastPlayed: data.sessions?.length > 0 ? Math.max(...data.sessions.map((s) => new Date(s.timestamp).getTime())) : null,
          favoriteGames: data.favoriteGames || [],
          achievements: data.achievements || [],
          avgPerformance: data.avgPerformance || 0
        };
        setProfileStats((prev) => ({
          ...prev,
          [profileId]: stats
        }));
        const dashboardUser = localStorage.getItem("userData");
        if (dashboardUser) {
          try {
            const user = JSON.parse(dashboardUser);
            const linkResponse = await fetch("/api/dashboard/link-profile", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${localStorage.getItem("authToken")}`
              },
              body: JSON.stringify({
                profileId,
                dashboardUserId: user.id,
                profileData: { gamesPlayed: stats.gamesPlayed, totalTime: stats.totalTime }
              })
            });
            if (linkResponse.ok) {
              console.log(`✅ Perfil ${profileId} vinculado ao usuário do dashboard ${user.email}`);
            }
          } catch (linkError) {
            console.warn("Falha ao vincular perfil ao dashboard:", linkError);
          }
        }
      }
    } catch (error) {
      console.error("Erro ao buscar estatísticas do perfil:", error);
    } finally {
      setLoadingStats(false);
    }
  };
  reactExports.useEffect(() => {
    const loadProfiles = () => {
      const savedProfiles = JSON.parse(localStorage.getItem("betina_profiles") || "[]");
      setProfiles(savedProfiles);
      const activeId = localStorage.getItem("betina_active_profile");
      if (activeId && savedProfiles.length > 0) {
        const active2 = savedProfiles.find((p) => p.id === activeId);
        setActiveProfile(active2);
      }
      savedProfiles.forEach((profile) => {
        fetchProfileStats(profile.id);
      });
    };
    loadProfiles();
  }, []);
  reactExports.useEffect(() => {
    if (activeProfile) {
      fetchProfileStats(activeProfile.id);
    }
  }, [activeProfile]);
  const saveProfiles = (newProfiles) => {
    setProfiles(newProfiles);
    localStorage.setItem("betina_profiles", JSON.stringify(newProfiles));
  };
  const handleCreateProfile = () => {
    if (!newProfile.name.trim()) {
      alert("Por favor, digite um nome para o perfil");
      return;
    }
    const profile = {
      id: Date.now().toString(),
      ...newProfile,
      createdAt: (/* @__PURE__ */ new Date()).toISOString(),
      lastUsed: (/* @__PURE__ */ new Date()).toISOString(),
      gamesPlayed: 0,
      totalTime: 0
    };
    const updatedProfiles = [...profiles, profile];
    saveProfiles(updatedProfiles);
    setNewProfile({
      name: "",
      age: "",
      avatar: "👶",
      preferences: {
        theme: "default",
        difficulty: "easy",
        soundEnabled: true,
        animationsEnabled: true
      }
    });
    setShowCreateForm(false);
  };
  const selectProfile = (profile) => {
    setActiveProfile(profile);
    localStorage.setItem("betina_active_profile", profile.id);
    const updatedProfiles = profiles.map(
      (p) => p.id === profile.id ? { ...p, lastUsed: (/* @__PURE__ */ new Date()).toISOString() } : p
    );
    saveProfiles(updatedProfiles);
    fetchProfileStats(profile.id);
  };
  const deleteProfile = (profileId) => {
    if (confirm("Tem certeza que deseja deletar este perfil?")) {
      const updatedProfiles = profiles.filter((p) => p.id !== profileId);
      saveProfiles(updatedProfiles);
      if (activeProfile?.id === profileId) {
        setActiveProfile(null);
        localStorage.removeItem("betina_active_profile");
      }
    }
  };
  const avatarOptions = ["👶", "👧", "👦", "🧒", "👨", "👩", "🐱", "🐶", "🦋", "🌟"];
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.container, children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.header, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h1", { className: styles.mainTitle, children: "� Gerenciar Perfis" }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
        lineNumber: 193,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: styles.subtitle, children: "Crie e gerencie perfis personalizados para toda a família" }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
        lineNumber: 194,
        columnNumber: 9
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
      lineNumber: 192,
      columnNumber: 7
    }, this),
    activeProfile && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("section", { className: styles.section, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: styles.sectionTitle, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "⭐" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
          lineNumber: 203,
          columnNumber: 13
        }, this),
        "Perfil Ativo"
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
        lineNumber: 202,
        columnNumber: 11
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.activeProfileCard, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.activeProfileAvatar, children: activeProfile.avatar }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
          lineNumber: 207,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.activeProfileInfo, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: styles.activeProfileName, children: activeProfile.name }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
            lineNumber: 209,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.activeProfileStats, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statItem, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.statIcon, children: "🎂" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
                lineNumber: 212,
                columnNumber: 19
              }, this),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: activeProfile.age ? `${activeProfile.age} anos` : "Idade não informada" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
                lineNumber: 213,
                columnNumber: 19
              }, this)
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
              lineNumber: 211,
              columnNumber: 17
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statItem, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.statIcon, children: "🎮" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
                lineNumber: 216,
                columnNumber: 19
              }, this),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: loadingStats ? "Carregando..." : `${profileStats2[activeProfile.id]?.gamesPlayed || 0} jogos jogados` }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
                lineNumber: 217,
                columnNumber: 19
              }, this)
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
              lineNumber: 215,
              columnNumber: 17
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statItem, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.statIcon, children: "⏰" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
                lineNumber: 223,
                columnNumber: 19
              }, this),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: profileStats2[activeProfile.id]?.totalTime ? `${Math.round(profileStats2[activeProfile.id].totalTime / 1e3 / 60)} min jogados` : `Último acesso: ${new Date(activeProfile.lastUsed).toLocaleDateString()}` }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
                lineNumber: 224,
                columnNumber: 19
              }, this)
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
              lineNumber: 222,
              columnNumber: 17
            }, this),
            profileStats2[activeProfile.id]?.favoriteGames?.length > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statItem, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.statIcon, children: "⭐" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
                lineNumber: 232,
                columnNumber: 21
              }, this),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: [
                "Jogo favorito: ",
                profileStats2[activeProfile.id].favoriteGames[0]
              ] }, void 0, true, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
                lineNumber: 233,
                columnNumber: 21
              }, this)
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
              lineNumber: 231,
              columnNumber: 19
            }, this),
            profileStats2[activeProfile.id]?.avgPerformance > 0 && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.statItem, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.statIcon, children: "📊" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
                lineNumber: 238,
                columnNumber: 21
              }, this),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: [
                "Performance média: ",
                Math.round(profileStats2[activeProfile.id].avgPerformance),
                "%"
              ] }, void 0, true, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
                lineNumber: 239,
                columnNumber: 21
              }, this)
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
              lineNumber: 237,
              columnNumber: 19
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
            lineNumber: 210,
            columnNumber: 15
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
          lineNumber: 208,
          columnNumber: 13
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
        lineNumber: 206,
        columnNumber: 11
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
      lineNumber: 201,
      columnNumber: 9
    }, this),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("section", { className: styles.section, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.profilesGrid, children: [
      profiles.map((profile) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "div",
        {
          className: `${styles.profileCard} ${activeProfile?.id === profile.id ? styles.active : ""}`,
          onClick: () => selectProfile(profile),
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.profileAvatar, children: profile.avatar }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
              lineNumber: 257,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.profileInfo, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: styles.profileName, children: profile.name }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
                lineNumber: 259,
                columnNumber: 17
              }, this),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: styles.profileAge, children: profile.age ? `${profile.age} anos` : "Idade não informada" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
                lineNumber: 260,
                columnNumber: 17
              }, this),
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: styles.profileStats, children: [
                profile.gamesPlayed || 0,
                " jogos jogados"
              ] }, void 0, true, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
                lineNumber: 263,
                columnNumber: 17
              }, this)
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
              lineNumber: 258,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
              "button",
              {
                className: styles.profileDeleteBtn,
                onClick: (e) => {
                  e.stopPropagation();
                  deleteProfile(profile.id);
                },
                title: "Deletar perfil",
                children: "🗑️"
              },
              void 0,
              false,
              {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
                lineNumber: 267,
                columnNumber: 15
              },
              this
            )
          ]
        },
        profile.id,
        true,
        {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
          lineNumber: 252,
          columnNumber: 13
        },
        this
      )),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
        "div",
        {
          className: `${styles.profileCard} ${styles.addProfileCard}`,
          onClick: () => setShowCreateForm(true),
          children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.addProfileIcon, children: "➕" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
              lineNumber: 285,
              columnNumber: 13
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "Adicionar Perfil" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
              lineNumber: 286,
              columnNumber: 13
            }, this)
          ]
        },
        void 0,
        true,
        {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
          lineNumber: 281,
          columnNumber: 11
        },
        this
      )
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
      lineNumber: 250,
      columnNumber: 9
    }, this) }, void 0, false, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
      lineNumber: 249,
      columnNumber: 7
    }, this),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("section", { className: styles.section, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: styles.sectionTitle, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "💡" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
          lineNumber: 294,
          columnNumber: 11
        }, this),
        "Como Funciona?"
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
        lineNumber: 293,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.sectionContent, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: [
          "Os ",
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "perfis de usuário" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
            lineNumber: 299,
            columnNumber: 16
          }, this),
          " permitem que cada criança da família tenha sua própria experiência personalizada. As métricas e progressos são automaticamente vinculados ao responsável que fizer login no dashboard."
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
          lineNumber: 298,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: styles.benefitsList, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: styles.benefitItem, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitEmoji, children: "📊" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
              lineNumber: 306,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitText, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Progresso Individual:" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
                lineNumber: 308,
                columnNumber: 17
              }, this),
              " Cada criança mantém seu próprio histórico de jogos e conquistas"
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
              lineNumber: 307,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
            lineNumber: 305,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: styles.benefitItem, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitEmoji, children: "👨‍👩‍👧‍👦" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
              lineNumber: 312,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitText, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Fácil para Crianças:" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
                lineNumber: 314,
                columnNumber: 17
              }, this),
              " Interface simples, sem necessidade de login ou senhas"
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
              lineNumber: 313,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
            lineNumber: 311,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: styles.benefitItem, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitEmoji, children: "�" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
              lineNumber: 318,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitText, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Métricas no Dashboard:" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
                lineNumber: 320,
                columnNumber: 17
              }, this),
              " Pais/responsáveis acessam relatórios detalhados via dashboard"
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
              lineNumber: 319,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
            lineNumber: 317,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: styles.benefitItem, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitEmoji, children: "�" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
              lineNumber: 324,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitText, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Dados Seguros:" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
                lineNumber: 326,
                columnNumber: 17
              }, this),
              " Todas as informações ficam salvas localmente no seu dispositivo"
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
              lineNumber: 325,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
            lineNumber: 323,
            columnNumber: 13
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
          lineNumber: 304,
          columnNumber: 11
        }, this),
        "        "
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
        lineNumber: 297,
        columnNumber: 9
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
      lineNumber: 292,
      columnNumber: 7
    }, this),
    showCreateForm && /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.createProfileOverlay, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.createProfileForm, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.formHeader, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: styles.formTitle, children: "➕ Criar Novo Perfil" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
          lineNumber: 337,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: styles.formSubtitle, children: "Adicione um novo membro da família" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
          lineNumber: 338,
          columnNumber: 15
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
        lineNumber: 336,
        columnNumber: 13
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.formGroup, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { htmlFor: "profile-name", className: styles.formLabel, children: "Nome:" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
          lineNumber: 342,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "input",
          {
            type: "text",
            id: "profile-name",
            name: "profile-name",
            value: newProfile.name,
            onChange: (e) => setNewProfile({ ...newProfile, name: e.target.value }),
            placeholder: "Digite o nome...",
            maxLength: 20,
            className: styles.input,
            autoComplete: "given-name"
          },
          void 0,
          false,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
            lineNumber: 343,
            columnNumber: 15
          },
          this
        )
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
        lineNumber: 341,
        columnNumber: 13
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.formGroup, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { htmlFor: "profile-age", className: styles.formLabel, children: "Idade (opcional):" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
          lineNumber: 357,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "input",
          {
            type: "number",
            id: "profile-age",
            name: "profile-age",
            value: newProfile.age,
            onChange: (e) => setNewProfile({ ...newProfile, age: e.target.value }),
            min: "1",
            max: "100",
            placeholder: "Ex: 5",
            className: styles.input,
            autoComplete: "age"
          },
          void 0,
          false,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
            lineNumber: 358,
            columnNumber: 15
          },
          this
        )
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
        lineNumber: 356,
        columnNumber: 13
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.formGroup, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("label", { htmlFor: "avatar-selector", className: styles.formLabel, children: "Escolha um Avatar:" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
          lineNumber: 373,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.avatarSelector, id: "avatar-selector", role: "radiogroup", "aria-labelledby": "avatar-selector", children: avatarOptions.map((avatar, index) => /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            type: "button",
            role: "radio",
            "aria-checked": newProfile.avatar === avatar,
            "aria-label": `Avatar ${index + 1}: ${avatar}`,
            className: `${styles.avatarOption} ${newProfile.avatar === avatar ? styles.selected : ""}`,
            onClick: () => setNewProfile({ ...newProfile, avatar }),
            children: avatar
          },
          avatar,
          false,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
            lineNumber: 376,
            columnNumber: 19
          },
          this
        )) }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
          lineNumber: 374,
          columnNumber: 15
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
        lineNumber: 372,
        columnNumber: 13
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.formActions, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            className: `${styles.btn} ${styles.btnPrimary}`,
            onClick: handleCreateProfile,
            children: "✅ Criar Perfil"
          },
          void 0,
          false,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
            lineNumber: 392,
            columnNumber: 15
          },
          this
        ),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV(
          "button",
          {
            className: `${styles.btn} ${styles.btnSecondary}`,
            onClick: () => setShowCreateForm(false),
            children: "❌ Cancelar"
          },
          void 0,
          false,
          {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
            lineNumber: 398,
            columnNumber: 15
          },
          this
        )
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
        lineNumber: 391,
        columnNumber: 13
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
      lineNumber: 335,
      columnNumber: 11
    }, this) }, void 0, false, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
      lineNumber: 334,
      columnNumber: 9
    }, this)
  ] }, void 0, true, {
    fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",
    lineNumber: 190,
    columnNumber: 5
  }, this);
}
export {
  UserProfiles as default
};
//# sourceMappingURL=UserProfiles-DUqB9ryD.js.map
