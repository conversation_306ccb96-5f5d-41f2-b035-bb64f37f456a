{"version": 3, "names": ["_temporalUndefined"], "sources": ["../../src/helpers/temporalUndefined.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\n// This function isn't mean to be called, but to be used as a reference.\n// We can't use a normal object because it isn't hoisted.\nexport default function _temporalUndefined(this: never): void {}\n"], "mappings": ";;;;;;AAIe,SAASA,kBAAkBA,CAAA,EAAoB,CAAC", "ignoreList": []}