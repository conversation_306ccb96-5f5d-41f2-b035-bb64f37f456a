-- Teste simples para verificar se o banco está funcionando
SELECT 'Testando conexão...' as status;

-- Verificar tabelas existentes
SELECT 
    COUNT(*) as total_tables,
    'Tabelas no banco' as description
FROM information_schema.tables 
WHERE table_schema = 'public';

-- Verificar se as tabelas principais existem
SELECT 
    table_name,
    CASE WHEN table_name IS NOT NULL THEN 'EXISTS' ELSE 'MISSING' END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
    'game_sessions',
    'game_metrics', 
    'multisensory_data',
    'colormatch_metrics',
    'memorygame_metrics'
)
ORDER BY table_name;

-- Verificar dados de exemplo
SELECT 
    'game_sessions' as table_name,
    COUNT(*) as record_count
FROM game_sessions
UNION ALL
SELECT 
    'game_metrics' as table_name,
    COUNT(*) as record_count
FROM game_metrics
UNION ALL
SELECT 
    'multisensory_data' as table_name,
    COUNT(*) as record_count
FROM multisensory_data;

SELECT '✅ Teste concluído!' as final_status;
