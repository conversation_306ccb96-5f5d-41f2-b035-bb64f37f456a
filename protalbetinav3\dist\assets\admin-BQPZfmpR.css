/**
 * @file LoadingSpinner.module.css
 * @description Estilos modulares para o componente LoadingSpinner
 * @version 3.0.0
 */

/* Container base */
._spinnerContainer_1ybli_15 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--space-md);
}

/* Fullscreen overlay */
._fullscreenOverlay_1ybli_33 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  backdrop-filter: blur(2px);
}

/* Spinner principal */
._spinner_1ybli_15 {
  border-radius: 50%;
  border: 4px solid var(--border-light);
  border-top: 4px solid var(--primary-blue);
  animation: _spin_1ybli_15 1s linear infinite;
}

/* Tamanhos */
._spinner_1ybli_15._small_1ybli_79 {
  width: 24px;
  height: 24px;
  border-width: 2px;
}

._spinner_1ybli_15._medium_1ybli_91 {
  width: 40px;
  height: 40px;
  border-width: 3px;
}

._spinner_1ybli_15._large_1ybli_103 {
  width: 60px;
  height: 60px;
  border-width: 4px;
}

._spinner_1ybli_15._xlarge_1ybli_115 {
  width: 80px;
  height: 80px;
  border-width: 5px;
}

/* Mensagem de loading */
._message_1ybli_129 {
  font-size: var(--font-md);
  color: var(--text-secondary);
  text-align: center;
  font-weight: var(--font-weight-medium);
  margin: 0;
}

._message_1ybli_129._small_1ybli_79 {
  font-size: var(--font-sm);
}

/* Variantes de cor */
._spinner_1ybli_15._primary_1ybli_155 {
  border-top-color: var(--primary-blue);
}

._spinner_1ybli_15._success_1ybli_163 {
  border-top-color: var(--success);
}

._spinner_1ybli_15._warning_1ybli_171 {
  border-top-color: var(--warning);
}

._spinner_1ybli_15._error_1ybli_179 {
  border-top-color: var(--error);
}

/* Animação de rotação */
@keyframes _spin_1ybli_15 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsividade */
@media (max-width: 768px) {
  ._spinner_1ybli_15._large_1ybli_103 {
    width: 50px;
    height: 50px;
    border-width: 3px;
  }
  
  ._spinner_1ybli_15._xlarge_1ybli_115 {
    width: 60px;
    height: 60px;
    border-width: 4px;
  }
  
  ._message_1ybli_129 {
    font-size: var(--font-sm);
  }
}

/* Alto contraste */
._highContrast_1ybli_239 ._spinner_1ybli_15 {
  border-color: var(--text-primary);
  border-top-color: var(--primary-blue);
}

._highContrast_1ybli_239 ._message_1ybli_129 {
  color: var(--text-primary);
  font-weight: var(--font-weight-bold);
}

/* Redução de movimento */
._reducedMotion_1ybli_261 ._spinner_1ybli_15 {
  animation: none;
  border-top-color: var(--primary-blue);
  border-right-color: var(--primary-blue);
}

/* Versão inline */
._inline_1ybli_275 {
  display: inline-flex;
  vertical-align: middle;
}

._inline_1ybli_275 ._spinner_1ybli_15 {
  margin-right: var(--space-xs);
}

._inline_1ybli_275 ._message_1ybli_129 {
  font-size: inherit;
  color: inherit;
}
/**
 * @file IntegratedSystemDashboard.module.css
 * @description Estilos modulares para Dashboard do Sistema Integrado (Admin)
 * @version 3.0.0
 */

/* Container principal do dashboard */
._dashboardContainer_1c4g6_15 {
  padding: 24px;
  background-color: #f8fafc;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

/* Header do dashboard */
._dashboardHeader_1c4g6_37 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
}

._dashboardTitle_1c4g6_55 {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

._titleIcon_1c4g6_75 {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  padding: 8px;
  border-radius: 8px;
  color: white;
  font-size: 20px;
}

._adminBadge_1c4g6_91 {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Controles do dashboard */
._dashboardControls_1c4g6_115 {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

._refreshButton_1c4g6_129 {
  padding: 8px 16px;
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

._refreshButton_1c4g6_129:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
}

._lastUpdate_1c4g6_169 {
  font-size: 12px;
  color: #718096;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Grid de métricas do sistema */
._systemMetricsGrid_1c4g6_187 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

._systemMetricCard_1c4g6_201 {
  background: linear-gradient(135deg, #ffffff 0%, #f7fafc 100%);
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

._systemMetricCard_1c4g6_201:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

._systemMetricCard_1c4g6_201::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

._systemMetricCard_1c4g6_201._users_1c4g6_251::before {
  background: linear-gradient(90deg, #4299e1, #3182ce);
}

._systemMetricCard_1c4g6_201._sessions_1c4g6_259::before {
  background: linear-gradient(90deg, #48bb78, #38a169);
}

._systemMetricCard_1c4g6_201._performance_1c4g6_267::before {
  background: linear-gradient(90deg, #ed8936, #dd6b20);
}

._systemMetricCard_1c4g6_201._storage_1c4g6_275::before {
  background: linear-gradient(90deg, #9f7aea, #805ad5);
}

._systemMetricCard_1c4g6_201._errors_1c4g6_283::before {
  background: linear-gradient(90deg, #f56565, #e53e3e);
}

._systemMetricHeader_1c4g6_291 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

._systemMetricTitle_1c4g6_305 {
  font-size: 14px;
  font-weight: 600;
  color: #4a5568;
  margin: 0;
}

._systemMetricIcon_1c4g6_319 {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

._systemMetricIcon_1c4g6_319._users_1c4g6_251 {
  background: linear-gradient(135deg, #4299e1, #3182ce);
}

._systemMetricIcon_1c4g6_319._sessions_1c4g6_259 {
  background: linear-gradient(135deg, #48bb78, #38a169);
}

._systemMetricIcon_1c4g6_319._performance_1c4g6_267 {
  background: linear-gradient(135deg, #ed8936, #dd6b20);
}

._systemMetricIcon_1c4g6_319._storage_1c4g6_275 {
  background: linear-gradient(135deg, #9f7aea, #805ad5);
}

._systemMetricIcon_1c4g6_319._errors_1c4g6_283 {
  background: linear-gradient(135deg, #f56565, #e53e3e);
}

._systemMetricValue_1c4g6_381 {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin: 6px 0;
  line-height: 1;
}

._systemMetricUnit_1c4g6_397 {
  font-size: 12px;
  color: #718096;
  font-weight: 500;
}

._systemMetricTrend_1c4g6_409 {
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
}

._trendPositive_1c4g6_427 {
  color: #48bb78;
}

._trendNegative_1c4g6_435 {
  color: #f56565;
}

._trendNeutral_1c4g6_443 {
  color: #718096;
}

/* Seção de monitoramento em tempo real */
._realtimeSection_1c4g6_453 {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

._realtimeHeader_1c4g6_471 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

._realtimeTitle_1c4g6_485 {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

._realtimeStatus_1c4g6_505 {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #48bb78;
  font-weight: 500;
}

._statusIndicator_1c4g6_523 {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #48bb78;
  animation: _pulse_1c4g6_1 2s infinite;
}

._statusIndicator_1c4g6_523._offline_1c4g6_539 {
  background: #f56565;
}

._statusIndicator_1c4g6_523._warning_1c4g6_547 {
  background: #ed8936;
}

._realtimeGrid_1c4g6_555 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

._realtimeCard_1c4g6_567 {
  background: #f7fafc;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

._realtimeCardTitle_1c4g6_581 {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 8px 0;
}

._realtimeValue_1c4g6_595 {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin: 4px 0;
}

._realtimeDescription_1c4g6_609 {
  font-size: 12px;
  color: #718096;
}

/* Grid de gráficos do sistema */
._chartsGrid_1c4g6_621 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

._chartCard_1c4g6_635 {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

._chartTitle_1c4g6_651 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

._chartContainer_1c4g6_671 {
  position: relative;
  height: 300px;
}

._chartContainer_1c4g6_671._line_1c4g6_681 {
  height: 280px;
}

._chartContainer_1c4g6_671._bar_1c4g6_689 {
  height: 320px;
}

._chartContainer_1c4g6_671._doughnut_1c4g6_697 {
  height: 250px;
}

/* Seção de logs do sistema */
._logsSection_1c4g6_707 {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

._logsHeader_1c4g6_725 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

._logsTitle_1c4g6_739 {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

._logsFilters_1c4g6_753 {
  display: flex;
  gap: 8px;
}

._logLevelFilter_1c4g6_763 {
  padding: 4px 8px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

._logLevelFilter_1c4g6_763._active_1c4g6_783 {
  background: #e53e3e;
  color: white;
  border-color: #e53e3e;
}

._logLevelFilter_1c4g6_763:hover {
  border-color: #e53e3e;
}

._logsList_1c4g6_803 {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

._logItem_1c4g6_817 {
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  display: flex;
  gap: 12px;
}

._logItem_1c4g6_817:last-child {
  border-bottom: none;
}

._logTimestamp_1c4g6_843 {
  color: #718096;
  white-space: nowrap;
  font-weight: 500;
}

._logLevel_1c4g6_763 {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  white-space: nowrap;
}

._logLevel_1c4g6_763._info_1c4g6_873 {
  background: #bee3f8;
  color: #2c5282;
}

._logLevel_1c4g6_763._warning_1c4g6_547 {
  background: #fbd38d;
  color: #c05621;
}

._logLevel_1c4g6_763._error_1c4g6_283 {
  background: #fed7d7;
  color: #c53030;
}

._logLevel_1c4g6_763._debug_1c4g6_903 {
  background: #e6fffa;
  color: #234e52;
}

._logMessage_1c4g6_913 {
  flex: 1;
  color: #2d3748;
  word-break: break-word;
}

/* Seção de alertas do sistema */
._alertsSection_1c4g6_927 {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

._alertsTitle_1c4g6_945 {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 16px 0;
}

._alertsList_1c4g6_959 {
  display: grid;
  gap: 12px;
}

._alertItem_1c4g6_969 {
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid;
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

._alertItem_1c4g6_969._critical_1c4g6_987 {
  background: #fed7d7;
  border-left-color: #e53e3e;
}

._alertItem_1c4g6_969._warning_1c4g6_547 {
  background: #fbd38d;
  border-left-color: #ed8936;
}

._alertItem_1c4g6_969._info_1c4g6_873 {
  background: #bee3f8;
  border-left-color: #4299e1;
}

._alertIcon_1c4g6_1017 {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  flex-shrink: 0;
  margin-top: 2px;
}

._alertIcon_1c4g6_1017._critical_1c4g6_987 {
  background: #e53e3e;
}

._alertIcon_1c4g6_1017._warning_1c4g6_547 {
  background: #ed8936;
}

._alertIcon_1c4g6_1017._info_1c4g6_873 {
  background: #4299e1;
}

._alertContent_1c4g6_1067 {
  flex: 1;
}

._alertTitle_1c4g6_1075 {
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 4px 0;
}

._alertMessage_1c4g6_1089 {
  font-size: 13px;
  color: #4a5568;
  line-height: 1.4;
  margin-bottom: 4px;
}

._alertTime_1c4g6_1103 {
  font-size: 11px;
  color: #718096;
}

/* Estados de loading e erro */
._loadingContainer_1c4g6_1115 {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex-direction: column;
  gap: 16px;
}

._loadingText_1c4g6_1133 {
  color: #4a5568;
  font-size: 16px;
}

._errorContainer_1c4g6_1143 {
  background: #fed7d7;
  border: 1px solid #feb2b2;
  color: #c53030;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
}

/* Responsividade */
@media (max-width: 768px) {
  ._dashboardContainer_1c4g6_15 {
    padding: 16px;
    margin-bottom: 16px;
  }

  ._dashboardHeader_1c4g6_37 {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  ._dashboardControls_1c4g6_115 {
    justify-content: center;
  }

  ._dashboardTitle_1c4g6_55 {
    font-size: 24px;
    text-align: center;
  }

  ._systemMetricsGrid_1c4g6_187 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }

  ._chartsGrid_1c4g6_621 {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  ._chartContainer_1c4g6_671 {
    height: 250px;
  }

  ._realtimeGrid_1c4g6_555 {
    grid-template-columns: 1fr;
  }

  ._logsFilters_1c4g6_753 {
    flex-wrap: wrap;
  }

  ._logItem_1c4g6_817 {
    flex-direction: column;
    gap: 8px;
  }

  ._alertItem_1c4g6_969 {
    flex-direction: column;
    gap: 8px;
  }

  /* Responsividade Multissensorial */
  ._multisensoryContainer_1c4g6_1271 {
    grid-template-columns: 1fr;
  }

  ._sensorGrid_1c4g6_1279 {
    grid-template-columns: 1fr;
  }

  ._metricsGrid_1c4g6_1287 {
    grid-template-columns: 1fr;
  }

  ._realtimeMetrics_1c4g6_1295 {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  ._dashboardContainer_1c4g6_15 {
    padding: 12px;
  }

  ._dashboardTitle_1c4g6_55 {
    font-size: 20px;
  }

  ._systemMetricValue_1c4g6_381 {
    font-size: 28px;
  }

  ._chartContainer_1c4g6_671 {
    height: 200px;
  }

  ._systemMetricsGrid_1c4g6_187 {
    grid-template-columns: 1fr;
  }
}

/* Animações */
@keyframes _fadeIn_1c4g6_1 {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes _pulse_1c4g6_1 {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes _slideDown_1c4g6_1 {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

._dashboardContainer_1c4g6_15 {
  animation: _fadeIn_1c4g6_1 0.5s ease-out;
}

._systemMetricCard_1c4g6_201 {
  animation: _slideDown_1c4g6_1 0.6s ease-out;
}

._chartCard_1c4g6_635,
._realtimeSection_1c4g6_453,
._logsSection_1c4g6_707,
._alertsSection_1c4g6_927 {
  animation: _fadeIn_1c4g6_1 0.6s ease-out;
}

._logItem_1c4g6_817,
._alertItem_1c4g6_969 {
  animation: _slideDown_1c4g6_1 0.3s ease-out;
}

/* Acessibilidade */
._refreshButton_1c4g6_129:focus-visible,
._logLevelFilter_1c4g6_763:focus-visible {
  outline: 2px solid #e53e3e;
  outline-offset: 2px;
}

/* Tema escuro (futuro) */
@media (prefers-color-scheme: dark) {
  ._dashboardContainer_1c4g6_15 {
    background-color: #1a202c;
    color: #e2e8f0;
  }

  ._realtimeSection_1c4g6_453,
  ._logsSection_1c4g6_707,
  ._alertsSection_1c4g6_927,
  ._chartCard_1c4g6_635 {
    background-color: #2d3748;
    border-color: #4a5568;
  }

  ._systemMetricCard_1c4g6_201 {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    border-color: #4a5568;
  }

  ._dashboardTitle_1c4g6_55,
  ._realtimeTitle_1c4g6_485,
  ._logsTitle_1c4g6_739,
  ._alertsTitle_1c4g6_945,
  ._chartTitle_1c4g6_651,
  ._systemMetricValue_1c4g6_381 {
    color: #e2e8f0;
  }

  ._realtimeCard_1c4g6_567 {
    background-color: #4a5568;
    border-color: #718096;
  }

  ._logsList_1c4g6_803 {
    background-color: #4a5568;
    border-color: #718096;
  }

  ._logItem_1c4g6_817 {
    border-color: #718096;
  }

  ._logLevelFilter_1c4g6_763 {
    background-color: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
  }
}

/* ✅ NOVO: Estilos para Painel Multissensorial */
._multisensorySection_1c4g6_1569 {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

._multisensorySection_1c4g6_1569 h3 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

._multisensoryContainer_1c4g6_1271 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

/* Status dos Sensores */
._sensorStatusPanel_1c4g6_1613 {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
}

._sensorStatusPanel_1c4g6_1613 h4 {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 500;
}

._sensorGrid_1c4g6_1279 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

._sensorItem_1c4g6_1651 {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

._sensorItem_1c4g6_1651._active_1c4g6_783 {
  background: #dcfce7;
  border: 1px solid #16a34a;
}

._sensorItem_1c4g6_1651._inactive_1c4g6_1679 {
  background: #fef2f2;
  border: 1px solid #dc2626;
}

._sensorIcon_1c4g6_1689 {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  font-size: 14px;
}

._sensorItem_1c4g6_1651._active_1c4g6_783 ._sensorIcon_1c4g6_1689 {
  background: #16a34a;
  color: white;
}

._sensorItem_1c4g6_1651._inactive_1c4g6_1679 ._sensorIcon_1c4g6_1689 {
  background: #dc2626;
  color: white;
}

._sensorInfo_1c4g6_1729 {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

._sensorLabel_1c4g6_1741 {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

._sensorStatus_1c4g6_1613._online_1c4g6_1753 {
  font-size: 0.75rem;
  color: #16a34a;
  font-weight: 500;
}

._sensorStatus_1c4g6_1613._offline_1c4g6_539 {
  font-size: 0.75rem;
  color: #dc2626;
  font-weight: 500;
}

/* Métricas Multissensoriais */
._multisensoryMetrics_1c4g6_1779 {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
}

._multisensoryMetrics_1c4g6_1779 h4 {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 500;
}

._metricsGrid_1c4g6_1287 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

._metricCard_1c4g6_1817 {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

._metricCard_1c4g6_1817:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

._metricHeader_1c4g6_1843 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

._metricHeader_1c4g6_1843 i {
  color: #6366f1;
  font-size: 14px;
}

._metricHeader_1c4g6_1843 span {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

._metricValueLarge_1c4g6_1879 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
}

/* Painel Tempo Real */
._realtimePanel_1c4g6_1893 {
  grid-column: 1 / -1;
  background: #f0f9ff;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #0ea5e9;
}

._realtimePanel_1c4g6_1893 h4 {
  margin: 0 0 16px 0;
  color: #0c4a6e;
  font-size: 1rem;
  font-weight: 500;
}

._realtimeMetrics_1c4g6_1295 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

._realtimeItem_1c4g6_1935 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #bae6fd;
}

._realtimeLabel_1c4g6_1955 {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

._realtimeValue_1c4g6_595 {
  font-size: 0.875rem;
  color: #0c4a6e;
  font-weight: 600;
}
/**
 * 🔧 SYSTEM HEALTH MONITOR V3 - UI/UX MODERNO
 * @file SystemHealthMonitor.module.css
 * @description Monitor de Saúde do Sistema com design futurístico
 * @version 3.0.0
 * @features Glassmorphism, Real-time indicators, Animations
 */

/* ===== VARIÁVEIS LOCAIS ===== */
:root {
  --health-success: #10b981;
  --health-warning: #f59e0b;
  --health-error: #ef4444;
  --health-info: #3b82f6;

  --health-bg-primary: #0f172a;
  --health-bg-secondary: #1e293b;
  --health-bg-glass: rgba(255, 255, 255, 0.1);

  --health-text-primary: #f8fafc;
  --health-text-secondary: #cbd5e1;
  --health-text-muted: #94a3b8;

  --health-border-radius: 16px;
  --health-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* ===== CONTAINER PRINCIPAL ===== */
._systemHealthMonitor_tvz97_57 {
  padding: 0;
  max-width: 100%;
  margin: 0;
  animation: _fadeInUp_tvz97_1 0.6s ease-out;
}

@keyframes _fadeInUp_tvz97_1 {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== HEADER MODERNO ===== */
._header_tvz97_95 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 24px;
  padding: 24px 32px;
  background: var(--health-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--health-border-radius);
  box-shadow: var(--health-shadow);
}

._title_tvz97_125 {
  color: var(--health-text-primary);
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

._title_tvz97_125::before {
  content: '🔧';
  font-size: 28px;
}

._headerInfo_tvz97_155 {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

._lastUpdate_tvz97_169 {
  color: var(--health-text-muted);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

._lastUpdate_tvz97_169::before {
  content: '🕒';
  font-size: 16px;
}

._refreshButton_tvz97_195 {
  background: linear-gradient(135deg, var(--health-info), #6366f1);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

._refreshButton_tvz97_195::before {
  content: '🔄';
  font-size: 16px;
}

._refreshButton_tvz97_195:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

._refreshButton_tvz97_195:disabled {
  background: rgba(148, 163, 184, 0.3);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* ===== STATUS GERAL MODERNO ===== */
._overallStatus_tvz97_265 {
  background: var(--health-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--health-border-radius);
  padding: 40px 32px;
  margin: 0 32px 32px 32px;
  box-shadow: var(--health-shadow);
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

._overallStatus_tvz97_265::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  transition: all 0.3s ease;
}

._statusHealthy_tvz97_313::before {
  background: linear-gradient(90deg, var(--health-success), #34d399);
}

._statusWarning_tvz97_321::before {
  background: linear-gradient(90deg, var(--health-warning), #fbbf24);
}

._statusCritical_tvz97_329::before {
  background: linear-gradient(90deg, var(--health-error), #f87171);
}

._statusIcon_tvz97_337 {
  font-size: 48px;
  margin-bottom: 16px;
  animation: _pulse_tvz97_1 2s infinite;
}

@keyframes _pulse_tvz97_1 {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

._statusText_tvz97_359 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

._statusHealthyText_tvz97_375 {
  color: var(--health-success);
  text-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
}

._statusWarningText_tvz97_385 {
  color: var(--health-warning);
  text-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

._statusCriticalText_tvz97_395 {
  color: var(--health-error);
  text-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}

._statusDescription_tvz97_405 {
  color: var(--health-text-secondary);
  font-size: 16px;
  font-weight: 500;
}

/* ===== GRID DE MÉTRICAS MODERNO ===== */
._metricsGrid_tvz97_419 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  margin: 0 32px 32px 32px;
}

._metricCard_tvz97_433 {
  background: var(--health-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--health-border-radius);
  padding: 24px;
  box-shadow: var(--health-shadow);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

._metricCard_tvz97_433::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--health-info), #6366f1);
  transition: all 0.3s ease;
}

._metricCard_tvz97_433:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.2);
}

._metricHeader_tvz97_491 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

._metricTitle_tvz97_505 {
  font-size: 16px;
  font-weight: 600;
  color: var(--health-text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

._metricStatus_tvz97_525 {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 6px;
}

._metricStatus_tvz97_525::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: _pulse_tvz97_1 2s infinite;
}

._statusOk_tvz97_565 {
  background: rgba(16, 185, 129, 0.2);
  color: var(--health-success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

._statusOk_tvz97_565::before {
  background: var(--health-success);
}

._statusWarning_tvz97_321 {
  background: rgba(245, 158, 11, 0.2);
  color: var(--health-warning);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

._statusWarning_tvz97_321::before {
  background: var(--health-warning);
}

._statusError_tvz97_605 {
  background: rgba(239, 68, 68, 0.2);
  color: var(--health-error);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

._statusError_tvz97_605::before {
  background: var(--health-error);
}

._metricValue_tvz97_625 {
  font-size: 24px;
  font-weight: 700;
  color: var(--health-text-primary);
  margin-bottom: 6px;
  line-height: 1;
}

._metricDescription_tvz97_641 {
  color: var(--health-text-secondary);
  font-size: 14px;
  margin-bottom: 20px;
  line-height: 1.5;
}

/* ===== BARRA DE PROGRESSO MODERNA ===== */
._progressBar_tvz97_657 {
  width: 100%;
  height: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

._progressBar_tvz97_657::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: _shimmer_tvz97_1 2s infinite;
}

@keyframes _shimmer_tvz97_1 {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

._progressFill_tvz97_707 {
  height: 100%;
  transition: width 0.8s ease;
  border-radius: 6px;
  position: relative;
  overflow: hidden;
}

._progressFill_tvz97_707::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: _progressShine_tvz97_1 2s infinite;
}

@keyframes _progressShine_tvz97_1 {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

._progressOk_tvz97_755 {
  background: linear-gradient(90deg, var(--health-success), #34d399);
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
}

._progressWarning_tvz97_765 {
  background: linear-gradient(90deg, var(--health-warning), #fbbf24);
  box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
}

._progressCritical_tvz97_775 {
  background: linear-gradient(90deg, var(--health-error), #f87171);
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.3);
}

/* ===== GRID DE SERVIÇOS MODERNO ===== */
._servicesGrid_tvz97_787 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin: 0 32px 32px 32px;
}

._serviceCard_tvz97_801 {
  background: var(--health-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--health-border-radius);
  padding: 20px;
  box-shadow: var(--health-shadow);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

._serviceCard_tvz97_801::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  transition: all 0.3s ease;
}

._serviceCard_tvz97_801:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
}

._serviceIcon_tvz97_863 {
  font-size: 32px;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

._serviceInfo_tvz97_887 {
  flex: 1;
}

._serviceName_tvz97_895 {
  font-weight: 600;
  color: var(--health-text-primary);
  margin-bottom: 6px;
  font-size: 16px;
}

._serviceStatus_tvz97_909 {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

._serviceStatus_tvz97_909::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: _pulse_tvz97_1 2s infinite;
}

._serviceHealthy_tvz97_941 {
  color: var(--health-success);
}

._serviceHealthy_tvz97_941::before {
  background: var(--health-success);
}

._serviceCard_tvz97_801:has(._serviceHealthy_tvz97_941)::before {
  background: var(--health-success);
}

._serviceDown_tvz97_965 {
  color: var(--health-error);
}

._serviceDown_tvz97_965::before {
  background: var(--health-error);
}

._serviceCard_tvz97_801:has(._serviceDown_tvz97_965)::before {
  background: var(--health-error);
}

._serviceDegraded_tvz97_989 {
  color: var(--health-warning);
}

._serviceDegraded_tvz97_989::before {
  background: var(--health-warning);
}

._serviceCard_tvz97_801:has(._serviceDegraded_tvz97_989)::before {
  background: var(--health-warning);
}

/* ===== SEÇÃO DE ALERTAS MODERNA ===== */
._alertsSection_tvz97_1015 {
  margin: 32px 32px 0 32px;
}

._alertsTitle_tvz97_1023 {
  font-size: 20px;
  font-weight: 700;
  color: var(--health-text-primary);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

._alertsTitle_tvz97_1023::before {
  content: '🚨';
  font-size: 24px;
}

._alertsList_tvz97_1053 {
  background: var(--health-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--health-border-radius);
  box-shadow: var(--health-shadow);
  overflow: hidden;
}

._alertItem_tvz97_1071 {
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  align-items: flex-start;
  gap: 16px;
  transition: all 0.3s ease;
}

._alertItem_tvz97_1071:hover {
  background: rgba(255, 255, 255, 0.05);
}

._alertItem_tvz97_1071:last-child {
  border-bottom: none;
}

._alertItem_tvz97_1071:last-child {
  border-bottom: none;
}

/* ===== ÍCONES E CONTEÚDO DOS ALERTAS ===== */
._alertIcon_tvz97_1115 {
  font-size: 20px;
  margin-top: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

._alertCritical_tvz97_1139 ._alertIcon_tvz97_1115 {
  color: var(--health-error);
  background: rgba(239, 68, 68, 0.2);
}

._alertWarning_tvz97_1149 ._alertIcon_tvz97_1115 {
  color: var(--health-warning);
  background: rgba(245, 158, 11, 0.2);
}

._alertInfo_tvz97_1159 ._alertIcon_tvz97_1115 {
  color: var(--health-info);
  background: rgba(59, 130, 246, 0.2);
}

._alertContent_tvz97_1169 {
  flex: 1;
}

._alertTitle_tvz97_1177 {
  font-weight: 600;
  color: var(--health-text-primary);
  margin-bottom: 6px;
  font-size: 16px;
}

._alertMessage_tvz97_1191 {
  color: var(--health-text-secondary);
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.5;
}

._alertTime_tvz97_1205 {
  color: var(--health-text-muted);
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

._alertTime_tvz97_1205::before {
  content: '🕒';
  font-size: 14px;
}

._noAlerts_tvz97_1231 {
  text-align: center;
  padding: 60px 32px;
  color: var(--health-text-muted);
  font-size: 16px;
}

._noAlerts_tvz97_1231::before {
  content: '✅';
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.5;
}

._loading_tvz97_1261 {
  text-align: center;
  padding: 80px 32px;
  color: var(--health-text-secondary);
}

._spinner_tvz97_1273 {
  display: inline-block;
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-top: 4px solid var(--health-info);
  border-radius: 50%;
  animation: _spin_tvz97_1273 1s linear infinite;
  margin-bottom: 24px;
}

/* ===== RESPONSIVIDADE MODERNA ===== */
@media (max-width: 1024px) {
  ._metricsGrid_tvz97_419,
  ._servicesGrid_tvz97_787 {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    margin: 0 24px 32px 24px;
  }

  ._header_tvz97_95,
  ._overallStatus_tvz97_265,
  ._alertsSection_tvz97_1015 {
    margin: 0 24px 32px 24px;
  }
}

@media (max-width: 768px) {
  ._header_tvz97_95 {
    flex-direction: column;
    align-items: stretch;
    padding: 20px 24px;
    margin: 0 16px 24px 16px;
  }

  ._headerInfo_tvz97_155 {
    align-items: flex-start;
  }

  ._metricsGrid_tvz97_419,
  ._servicesGrid_tvz97_787 {
    grid-template-columns: 1fr;
    margin: 0 16px 24px 16px;
    gap: 16px;
  }

  ._overallStatus_tvz97_265 {
    padding: 32px 24px;
    margin: 0 16px 24px 16px;
  }

  ._alertsSection_tvz97_1015 {
    margin: 24px 16px 0 16px;
  }

  ._statusIcon_tvz97_337 {
    font-size: 48px;
  }

  ._statusText_tvz97_359 {
    font-size: 24px;
  }

  ._metricValue_tvz97_625 {
    font-size: 28px;
  }

  ._serviceCard_tvz97_801 {
    padding: 16px;
  }

  ._alertItem_tvz97_1071 {
    padding: 16px 20px;
  }
}

@media (max-width: 480px) {
  ._header_tvz97_95 {
    margin: 0 8px 20px 8px;
    padding: 16px 20px;
  }

  ._metricsGrid_tvz97_419,
  ._servicesGrid_tvz97_787,
  ._overallStatus_tvz97_265,
  ._alertsSection_tvz97_1015 {
    margin: 0 8px 20px 8px;
  }

  ._title_tvz97_125 {
    font-size: 20px;
  }

  ._statusIcon_tvz97_337 {
    font-size: 40px;
  }

  ._statusText_tvz97_359 {
    font-size: 20px;
  }

  ._metricCard_tvz97_433,
  ._serviceCard_tvz97_801 {
    padding: 16px;
  }

  ._metricValue_tvz97_625 {
    font-size: 24px;
  }

  ._serviceIcon_tvz97_863 {
    font-size: 24px;
    width: 48px;
    height: 48px;
  }
}
/**
 * 🧠 ANALYZERS MONITOR V3 - UI/UX MODERNO
 * @file AnalyzersMonitor.module.css
 * @description Monitor de Analisadores com design futurístico
 * @version 3.0.0
 * @features Glassmorphism, Real-time indicators, Organized metrics
 */

/* ===== VARIÁVEIS LOCAIS ===== */
:root {
  --analyzer-primary: #8b5cf6;
  --analyzer-success: #10b981;
  --analyzer-warning: #f59e0b;
  --analyzer-error: #ef4444;
  --analyzer-info: #3b82f6;

  --analyzer-bg-primary: #0f172a;
  --analyzer-bg-secondary: #1e293b;
  --analyzer-bg-glass: rgba(255, 255, 255, 0.1);

  --analyzer-text-primary: #f8fafc;
  --analyzer-text-secondary: #cbd5e1;
  --analyzer-text-muted: #94a3b8;

  --analyzer-border-radius: 16px;
  --analyzer-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* ===== CONTAINER PRINCIPAL ===== */
._analyzersMonitor_140d6_59 {
  padding: 0;
  max-width: 100%;
  margin: 0;
  animation: _fadeInUp_140d6_1 0.6s ease-out;
}

@keyframes _fadeInUp_140d6_1 {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== HEADER MODERNO ===== */
._header_140d6_97 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 24px;
  padding: 24px 32px;
  background: var(--analyzer-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--analyzer-border-radius);
  box-shadow: var(--analyzer-shadow);
}

._title_140d6_127 {
  color: var(--analyzer-text-primary);
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

._title_140d6_127::before {
  content: '🧠';
  font-size: 28px;
}

._refreshButton_140d6_157 {
  background: linear-gradient(135deg, var(--analyzer-primary), #a855f7);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

._refreshButton_140d6_157::before {
  content: '🔄';
  font-size: 16px;
}

._refreshButton_140d6_157:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

._refreshButton_140d6_157:disabled {
  background: rgba(148, 163, 184, 0.3);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* ===== GRID DE ANALISADORES ORGANIZADO ===== */
._analyzersGrid_140d6_225 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  margin: 0 32px 32px 32px;
}

._analyzerCard_140d6_239 {
  background: var(--analyzer-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--analyzer-border-radius);
  padding: 24px;
  box-shadow: var(--analyzer-shadow);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

._analyzerCard_140d6_239::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--analyzer-primary), #a855f7);
  transition: all 0.3s ease;
}

._analyzerCard_140d6_239:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.2);
}

._analyzerHeader_140d6_299 {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

._analyzerIcon_140d6_313 {
  font-size: 32px;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: rgba(139, 92, 246, 0.2);
  backdrop-filter: blur(10px);
  flex-shrink: 0;
}

._analyzerInfo_140d6_339 {
  flex: 1;
  min-width: 0;
}

._analyzerName_140d6_349 {
  font-size: 16px;
  font-weight: 600;
  color: var(--analyzer-text-primary);
  margin: 0 0 6px 0;
  line-height: 1.2;
}

._analyzerDescription_140d6_365 {
  color: var(--analyzer-text-secondary);
  font-size: 13px;
  margin: 0;
  line-height: 1.4;
}

._analyzerStatus_140d6_379 {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 6px;
  position: absolute;
  top: 20px;
  right: 20px;
}

._analyzerStatus_140d6_379::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: _pulse_140d6_1 2s infinite;
}

._statusActive_140d6_425 {
  background: rgba(16, 185, 129, 0.2);
  color: var(--analyzer-success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

._statusActive_140d6_425::before {
  background: var(--analyzer-success);
}

._statusInactive_140d6_445 {
  background: rgba(239, 68, 68, 0.2);
  color: var(--analyzer-error);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

._statusInactive_140d6_445::before {
  background: var(--analyzer-error);
}

._statusProcessing_140d6_465 {
  background: rgba(245, 158, 11, 0.2);
  color: var(--analyzer-warning);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

._statusProcessing_140d6_465::before {
  background: var(--analyzer-warning);
}

/* ===== MÉTRICAS ORGANIZADAS ===== */
._analyzerMetrics_140d6_487 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

._metric_140d6_501 {
  text-align: center;
  padding: 12px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

._metricValue_140d6_517 {
  font-size: 18px;
  font-weight: 700;
  color: var(--analyzer-text-primary);
  margin-bottom: 4px;
  line-height: 1;
}

._metricLabel_140d6_533 {
  font-size: 10px;
  color: var(--analyzer-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.3px;
  line-height: 1;
}

._analyzerActions_140d6_549 {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  margin-top: 1rem;
}

._actionButton_140d6_563 {
  padding: 0.4rem 0.8rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.2s;
}

._viewButton_140d6_581 {
  background: #007bff;
  color: white;
}

._viewButton_140d6_581:hover {
  background: #0056b3;
}

._configButton_140d6_599 {
  background: #28a745;
  color: white;
}

._configButton_140d6_599:hover {
  background: #1e7e34;
}

._restartButton_140d6_617 {
  background: #ffc107;
  color: #212529;
}

._restartButton_140d6_617:hover {
  background: #e0a800;
}

._modal_140d6_635 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

._modalContent_140d6_661 {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
  margin: 1rem;
  position: relative;
}

._modalHeader_140d6_683 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #dee2e6;
}

._modalTitle_140d6_701 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

._closeButton_140d6_715 {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

._closeButton_140d6_715:hover {
  color: #333;
}

._detailsGrid_140d6_751 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

._detailCard_140d6_765 {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
}

._detailLabel_140d6_777 {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  font-weight: 500;
}

._detailValue_140d6_793 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

._configSection_140d6_805 {
  margin-bottom: 2rem;
}

._configTitle_140d6_813 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

._configList_140d6_827 {
  list-style: none;
  padding: 0;
  margin: 0;
}

._configItem_140d6_839 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #dee2e6;
}

._configItem_140d6_839:last-child {
  border-bottom: none;
}

._configKey_140d6_863 {
  font-weight: 500;
  color: #333;
}

._configValue_140d6_873 {
  color: #666;
  font-family: monospace;
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

._logsSection_140d6_891 {
  margin-top: 2rem;
}

._logsTitle_140d6_899 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

._logsList_140d6_913 {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 1rem;
  max-height: 300px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 0.9rem;
}

._logEntry_140d6_933 {
  margin-bottom: 0.5rem;
  padding: 0.25rem 0;
}

._logTimestamp_140d6_943 {
  color: #666;
  margin-right: 0.5rem;
}

._logLevel_140d6_953 {
  padding: 0.1rem 0.3rem;
  border-radius: 3px;
  font-size: 0.8rem;
  margin-right: 0.5rem;
}

._logInfo_140d6_967 {
  background: #d1ecf1;
  color: #0c5460;
}

._logWarning_140d6_977 {
  background: #fff3cd;
  color: #856404;
}

._logError_140d6_987 {
  background: #f8d7da;
  color: #721c24;
}

._logMessage_140d6_997 {
  color: #333;
}

._loading_140d6_1005 {
  text-align: center;
  padding: 3rem;
  color: #666;
}

._spinner_140d6_1017 {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: _spin_140d6_1017 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes _spin_140d6_1017 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsividade */
@media (max-width: 768px) {
  ._header_140d6_97 {
    flex-direction: column;
    align-items: stretch;
  }

  ._analyzersGrid_140d6_225 {
    grid-template-columns: 1fr;
  }

  ._analyzerMetrics_140d6_487 {
    grid-template-columns: 1fr;
  }

  ._detailsGrid_140d6_751 {
    grid-template-columns: 1fr;
  }

  ._modalContent_140d6_661 {
    margin: 0.5rem;
    padding: 1rem;
  }

  ._configItem_140d6_839 {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
/**
 * 🎨 USER MANAGEMENT V3 - UI/UX MODERNO
 * @file UserManagement.module.css
 * @description Estilos modernos para Gerenciamento de Usuários
 * @version 3.0.0
 * @features Glassmorphism, Dark Mode, Animations
 */

/* ===== VARIÁVEIS LOCAIS ===== */
:root {
  --user-primary: #6366f1;
  --user-success: #10b981;
  --user-warning: #f59e0b;
  --user-error: #ef4444;
  --user-info: #3b82f6;

  --user-bg-primary: #0f172a;
  --user-bg-secondary: #1e293b;
  --user-bg-tertiary: #334155;
  --user-bg-glass: rgba(255, 255, 255, 0.1);

  --user-text-primary: #f8fafc;
  --user-text-secondary: #cbd5e1;
  --user-text-muted: #94a3b8;

  --user-border-radius: 16px;
  --user-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* ===== CONTAINER PRINCIPAL ===== */
._userManagement_1q3zf_61 {
  padding: 0;
  max-width: 100%;
  margin: 0;
  animation: _fadeInUp_1q3zf_1 0.6s ease-out;
}

@keyframes _fadeInUp_1q3zf_1 {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== HEADER MODERNO ===== */
._header_1q3zf_99 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 24px;
  padding: 24px 32px;
  background: var(--user-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--user-border-radius);
  box-shadow: var(--user-shadow);
}

._title_1q3zf_129 {
  color: var(--user-text-primary);
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

._title_1q3zf_129::before {
  content: '👥';
  font-size: 28px;
}

/* ===== CONTROLES MODERNOS ===== */
._controls_1q3zf_161 {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

._searchInput_1q3zf_175 {
  padding: 12px 20px;
  background: var(--user-bg-glass);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  font-size: 14px;
  color: var(--user-text-primary);
  min-width: 280px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
}

._searchInput_1q3zf_175::placeholder {
  color: var(--user-text-muted);
}

._searchInput_1q3zf_175:focus {
  outline: none;
  border-color: var(--user-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  transform: translateY(-2px);
}

._filterSelect_1q3zf_223 {
  padding: 12px 20px;
  background: var(--user-bg-glass);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  font-size: 14px;
  color: var(--user-text-primary);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 160px;
}

._filterSelect_1q3zf_223:focus {
  outline: none;
  border-color: var(--user-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

._filterSelect_1q3zf_223 option {
  background: var(--user-bg-secondary);
  color: var(--user-text-primary);
  padding: 8px;
}

/* ===== CARDS DE ESTATÍSTICAS MODERNOS ===== */
._statsCards_1q3zf_275 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
  padding: 0 32px;
}

._statCard_1q3zf_291 {
  background: var(--user-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--user-text-primary);
  padding: 32px 24px;
  border-radius: var(--user-border-radius);
  text-align: center;
  box-shadow: var(--user-shadow);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

._statCard_1q3zf_291::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--user-primary), var(--user-info));
}

._statCard_1q3zf_291:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.2);
}

._statValue_1q3zf_351 {
  font-size: 36px;
  font-weight: 800;
  margin-bottom: 8px;
  background: linear-gradient(135deg, var(--user-primary), var(--user-info));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

._statLabel_1q3zf_371 {
  font-size: 14px;
  color: var(--user-text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== TABELA MODERNA ===== */
._usersTable_1q3zf_389 {
  background: var(--user-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--user-border-radius);
  box-shadow: var(--user-shadow);
  overflow: hidden;
  margin: 0 32px;
}

._tableHeader_1q3zf_409 {
  background: rgba(255, 255, 255, 0.05);
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-weight: 600;
  font-size: 14px;
  color: var(--user-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr auto;
  gap: 24px;
  align-items: center;
}

._userRow_1q3zf_439 {
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr auto;
  gap: 24px;
  align-items: center;
  transition: all 0.3s ease;
  color: var(--user-text-primary);
}

._userRow_1q3zf_439:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateX(8px);
}

._userRow_1q3zf_439:last-child {
  border-bottom: none;
}

/* ===== INFORMAÇÕES DO USUÁRIO ===== */
._userInfo_1q3zf_481 {
  display: flex;
  align-items: center;
  gap: 16px;
}

._userAvatar_1q3zf_493 {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--user-primary), var(--user-info));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 18px;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
  transition: all 0.3s ease;
}

._userAvatar_1q3zf_493:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
}

._userDetails_1q3zf_533 {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

._userName_1q3zf_545 {
  font-weight: 600;
  color: var(--user-text-primary);
  font-size: 16px;
}

._userEmail_1q3zf_557 {
  font-size: 14px;
  color: var(--user-text-muted);
}

/* ===== BADGES DE STATUS MODERNOS ===== */
._statusBadge_1q3zf_569 {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

._statusBadge_1q3zf_569::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: _pulse_1q3zf_1 2s infinite;
}

._statusActive_1q3zf_611 {
  background: rgba(16, 185, 129, 0.2);
  color: var(--user-success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

._statusActive_1q3zf_611::before {
  background: var(--user-success);
}

._statusInactive_1q3zf_631 {
  background: rgba(239, 68, 68, 0.2);
  color: var(--user-error);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

._statusInactive_1q3zf_631::before {
  background: var(--user-error);
}

@keyframes _pulse_1q3zf_1 {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* ===== BOTÕES DE AÇÃO MODERNOS ===== */
._actionButtons_1q3zf_663 {
  display: flex;
  gap: 8px;
}

._actionButton_1q3zf_663 {
  padding: 10px 12px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

._actionButton_1q3zf_663::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

._actionButton_1q3zf_663:hover::before {
  left: 100%;
}

._viewButton_1q3zf_737 {
  background: rgba(59, 130, 246, 0.2);
  color: var(--user-info);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

._viewButton_1q3zf_737:hover {
  background: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

._editButton_1q3zf_761 {
  background: rgba(16, 185, 129, 0.2);
  color: var(--user-success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

._editButton_1q3zf_761:hover {
  background: rgba(16, 185, 129, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

._deleteButton_1q3zf_785 {
  background: rgba(239, 68, 68, 0.2);
  color: var(--user-error);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

._deleteButton_1q3zf_785:hover {
  background: rgba(239, 68, 68, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* ===== ESTADOS ESPECIAIS ===== */
._noUsers_1q3zf_811 {
  text-align: center;
  padding: 80px 32px;
  color: var(--user-text-muted);
  font-size: 18px;
  background: var(--user-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--user-border-radius);
  margin: 0 32px;
}

._noUsers_1q3zf_811::before {
  content: '👤';
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.5;
}

._loading_1q3zf_851 {
  text-align: center;
  padding: 80px 32px;
  color: var(--user-text-secondary);
  background: var(--user-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--user-border-radius);
  margin: 0 32px;
}

._spinner_1q3zf_873 {
  display: inline-block;
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-top: 4px solid var(--user-primary);
  border-radius: 50%;
  animation: _spin_1q3zf_873 1s linear infinite;
  margin-bottom: 24px;
}

@keyframes _spin_1q3zf_873 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== RESPONSIVIDADE MODERNA ===== */
@media (max-width: 1024px) {
  ._statsCards_1q3zf_275 {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    padding: 0 24px;
  }

  ._header_1q3zf_99,
  ._usersTable_1q3zf_389 {
    margin: 0 24px;
  }
}

@media (max-width: 768px) {
  ._header_1q3zf_99 {
    flex-direction: column;
    align-items: stretch;
    padding: 20px 24px;
    margin: 0 16px;
  }

  ._controls_1q3zf_161 {
    flex-direction: column;
    gap: 12px;
  }

  ._searchInput_1q3zf_175,
  ._filterSelect_1q3zf_223 {
    min-width: 100%;
  }

  ._statsCards_1q3zf_275 {
    grid-template-columns: 1fr;
    padding: 0 16px;
    gap: 16px;
  }

  ._usersTable_1q3zf_389 {
    margin: 0 16px;
  }

  ._tableHeader_1q3zf_409 {
    display: none; /* Ocultar header em mobile */
  }

  ._userRow_1q3zf_439 {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 24px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    margin-bottom: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  ._userRow_1q3zf_439:hover {
    transform: none;
    background: rgba(255, 255, 255, 0.05);
  }

  ._userInfo_1q3zf_481 {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 16px;
    margin-bottom: 16px;
  }

  ._actionButtons_1q3zf_663 {
    justify-content: center;
    gap: 12px;
  }

  ._actionButton_1q3zf_663 {
    flex: 1;
    max-width: 120px;
  }
}

@media (max-width: 480px) {
  ._header_1q3zf_99 {
    margin: 0 8px;
    padding: 16px 20px;
  }

  ._statsCards_1q3zf_275,
  ._usersTable_1q3zf_389 {
    margin: 0 8px;
  }

  ._title_1q3zf_129 {
    font-size: 20px;
  }

  ._statCard_1q3zf_291 {
    padding: 24px 20px;
  }

  ._statValue_1q3zf_351 {
    font-size: 28px;
  }

  ._userRow_1q3zf_439 {
    padding: 20px;
  }

  ._userAvatar_1q3zf_493 {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
}
/**
 * 🎨 Portal Betina V3 - Estilos do Gerenciamento de Cadastros
 */

._container_10qni_5 {
  padding: 24px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  min-height: 100vh;
  color: #ffffff;
}

._header_10qni_12 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 2px solid #3b82f6;
}

._header_10qni_12 h2 {
  color: #ffffff;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}

._refreshButton_10qni_28 {
  background: #3b82f6;
  color: #ffffff;
  border: none;
  padding: 12px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

._refreshButton_10qni_28:hover {
  background: #2563eb;
  transform: translateY(-2px);
}

._loading_10qni_44 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

._spinner_10qni_52 {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(59, 130, 246, 0.3);
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: _spin_10qni_52 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes _spin_10qni_52 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

._summary_10qni_67 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

._summaryCard_10qni_74 {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  transition: all 0.3s ease;
}

._summaryCard_10qni_74:hover {
  border-color: #3b82f6;
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.2);
}

._summaryNumber_10qni_89 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 8px;
}

._summaryLabel_10qni_96 {
  color: #d1d5db;
  font-size: 0.9rem;
  font-weight: 500;
}

._filters_10qni_102 {
  margin-bottom: 24px;
  display: flex;
  gap: 16px;
  align-items: center;
}

._statusFilter_10qni_109 {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: #ffffff;
  padding: 12px 16px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

._statusFilter_10qni_109:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

._registrationsList_10qni_126 {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

._emptyState_10qni_132 {
  text-align: center;
  padding: 60px 20px;
  color: #9ca3af;
}

._registrationCard_10qni_138 {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
}

._registrationCard_10qni_138:hover {
  border-color: #3b82f6;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
}

._cardHeader_10qni_151 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

._userInfo_10qni_158 h4 {
  color: #ffffff;
  margin: 0 0 4px 0;
  font-size: 1.2rem;
  font-weight: 600;
}

._userInfo_10qni_158 p {
  color: #9ca3af;
  margin: 0;
  font-size: 0.9rem;
}

._statusBadge_10qni_171 {
  color: #ffffff;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

._cardContent_10qni_180 {
  margin-bottom: 20px;
}

._cardInfo_10qni_184 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

._cardInfo_10qni_184 span {
  color: #e5e7eb;
  font-size: 0.9rem;
}

._cardInfo_10qni_184 strong {
  color: #ffffff;
}

._cardActions_10qni_199 {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

._detailsButton_10qni_205,
._quickApproveButton_10qni_206,
._quickRejectButton_10qni_207 {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

._detailsButton_10qni_205 {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  border: 1px solid #3b82f6;
}

._detailsButton_10qni_205:hover {
  background: #3b82f6;
  color: #ffffff;
}

._quickApproveButton_10qni_206 {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
  border: 1px solid #10b981;
}

._quickApproveButton_10qni_206:hover {
  background: #10b981;
  color: #ffffff;
}

._quickRejectButton_10qni_207 {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid #ef4444;
}

._quickRejectButton_10qni_207:hover {
  background: #ef4444;
  color: #ffffff;
}

._quickApproveButton_10qni_206:disabled,
._quickRejectButton_10qni_207:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modal Styles */
._modalOverlay_10qni_257 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

._modal_10qni_257 {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 20px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  border: 2px solid #3b82f6;
}

._modalHeader_10qni_282 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 2px solid #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

._modalHeader_10qni_282 h3 {
  color: #ffffff;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

._closeButton_10qni_298 {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

._closeButton_10qni_298:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

._modalContent_10qni_314 {
  padding: 32px;
}

._section_10qni_318 {
  margin-bottom: 32px;
}

._section_10qni_318 h4 {
  color: #3b82f6;
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  padding-bottom: 8px;
}

._infoGrid_10qni_331 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

._infoGrid_10qni_331 div {
  color: #e5e7eb;
  font-size: 0.9rem;
  line-height: 1.5;
}

._infoGrid_10qni_331 strong {
  color: #ffffff;
}

._planInfo_10qni_347 {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  padding: 20px;
}

._planName_10qni_354 {
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
}

._planPrice_10qni_361 {
  color: #3b82f6;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 8px;
}

._planDescription_10qni_368 {
  color: #d1d5db;
  font-size: 0.9rem;
  line-height: 1.4;
}

._modalActions_10qni_374 {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 24px 32px;
  border-top: 2px solid rgba(255, 255, 255, 0.1);
}

._approveButton_10qni_382,
._rejectButton_10qni_383 {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

._approveButton_10qni_382 {
  background: #10b981;
  color: #ffffff;
}

._approveButton_10qni_382:hover {
  background: #059669;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

._rejectButton_10qni_383 {
  background: #ef4444;
  color: #ffffff;
}

._rejectButton_10qni_383:hover {
  background: #dc2626;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

._approveButton_10qni_382:disabled,
._rejectButton_10qni_383:disabled {
  background: #6b7280;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}
/**
 * 📋 SYSTEM LOGS V3 - UI/UX MODERNO
 * @file SystemLogs.module.css
 * @description Visualizador de Logs com design futurístico
 * @version 3.0.0
 * @features Glassmorphism, Organized layout, Real-time updates
 */

/* ===== VARIÁVEIS LOCAIS ===== */
:root {
  --logs-primary: #6366f1;
  --logs-success: #10b981;
  --logs-warning: #f59e0b;
  --logs-error: #ef4444;
  --logs-info: #3b82f6;

  --logs-bg-primary: #0f172a;
  --logs-bg-secondary: #1e293b;
  --logs-bg-glass: rgba(255, 255, 255, 0.1);

  --logs-text-primary: #f8fafc;
  --logs-text-secondary: #cbd5e1;
  --logs-text-muted: #94a3b8;

  --logs-border-radius: 16px;
  --logs-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* ===== CONTAINER PRINCIPAL ===== */
._systemLogs_1bhl0_59 {
  padding: 0;
  max-width: 100%;
  margin: 0;
  animation: _fadeInUp_1bhl0_1 0.6s ease-out;
}

@keyframes _fadeInUp_1bhl0_1 {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== HEADER ORGANIZADO ===== */
._header_1bhl0_97 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 20px;
  padding: 24px 32px;
  background: var(--logs-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--logs-border-radius);
  box-shadow: var(--logs-shadow);
}

._title_1bhl0_127 {
  color: var(--logs-text-primary);
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

._title_1bhl0_127::before {
  content: '📋';
  font-size: 28px;
}

._controls_1bhl0_157 {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

._filterGroup_1bhl0_171 {
  display: flex;
  align-items: center;
  gap: 8px;
}

._filterLabel_1bhl0_183 {
  font-size: 14px;
  color: var(--logs-text-secondary);
  font-weight: 500;
  white-space: nowrap;
}

._filterSelect_1bhl0_197 {
  padding: 10px 16px;
  background: var(--logs-bg-glass);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  font-size: 14px;
  color: var(--logs-text-primary);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
}

._filterSelect_1bhl0_197:focus {
  outline: none;
  border-color: var(--logs-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

._filterSelect_1bhl0_197 option {
  background: var(--logs-bg-secondary);
  color: var(--logs-text-primary);
}

._searchInput_1bhl0_245 {
  padding: 10px 16px;
  background: var(--logs-bg-glass);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  font-size: 14px;
  color: var(--logs-text-primary);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  min-width: 240px;
}

._searchInput_1bhl0_245::placeholder {
  color: var(--logs-text-muted);
}

._searchInput_1bhl0_245:focus {
  outline: none;
  border-color: var(--logs-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  transform: translateY(-2px);
}

/* ===== CONTROLES MODERNOS ===== */
._autoRefreshToggle_1bhl0_293 {
  display: flex;
  align-items: center;
  gap: 8px;
}

._toggleSwitch_1bhl0_305 {
  position: relative;
  width: 48px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

._toggleSwitch_1bhl0_305._active_1bhl0_327 {
  background: var(--logs-primary);
  box-shadow: 0 0 12px rgba(99, 102, 241, 0.3);
}

._toggleSlider_1bhl0_337 {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

._toggleSwitch_1bhl0_305._active_1bhl0_327 ._toggleSlider_1bhl0_337 {
  transform: translateX(24px);
}

._refreshButton_1bhl0_369 {
  background: linear-gradient(135deg, var(--logs-info), #2563eb);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

._refreshButton_1bhl0_369::before {
  content: '🔄';
  font-size: 16px;
}

._refreshButton_1bhl0_369:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

._refreshButton_1bhl0_369:disabled {
  background: rgba(148, 163, 184, 0.3);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

._clearButton_1bhl0_435 {
  background: linear-gradient(135deg, var(--logs-error), #dc2626);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

._clearButton_1bhl0_435::before {
  content: '🗑️';
  font-size: 16px;
}

._clearButton_1bhl0_435:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

/* ===== BARRA DE ESTATÍSTICAS HORIZONTAL ===== */
._statsBar_1bhl0_489 {
  background: var(--logs-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--logs-border-radius);
  padding: 20px 32px;
  margin: 0 32px 24px 32px;
  box-shadow: var(--logs-shadow);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
}

._statItem_1bhl0_519 {
  text-align: center;
  min-width: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

._statValue_1bhl0_537 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 2px;
  line-height: 1;
}

._statLabel_1bhl0_551 {
  font-size: 12px;
  color: var(--logs-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  line-height: 1;
}

._statError_1bhl0_567 {
  color: var(--logs-error);
}

._statWarning_1bhl0_575 {
  color: var(--logs-warning);
}

._statInfo_1bhl0_583 {
  color: var(--logs-info);
}

._statSuccess_1bhl0_591 {
  color: var(--logs-success);
}

._statDebug_1bhl0_599 {
  color: #6c757d;
}

._statLabel_1bhl0_551 {
  font-size: 0.8rem;
  color: #666;
  text-transform: uppercase;
}

._logsContainer_1bhl0_619 {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

._logsHeader_1bhl0_633 {
  background: #f8f9fa;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

._logsTitle_1bhl0_651 {
  font-weight: 600;
  color: #333;
  margin: 0;
}

._logsCount_1bhl0_663 {
  font-size: 0.9rem;
  color: #666;
}

._logsList_1bhl0_673 {
  max-height: 600px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
}

._logEntry_1bhl0_687 {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f1f3f4;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  transition: background-color 0.2s;
}

._logEntry_1bhl0_687:hover {
  background: #f8f9fa;
}

._logEntry_1bhl0_687:last-child {
  border-bottom: none;
}

._logLevel_1bhl0_721 {
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  min-width: 60px;
  text-align: center;
  white-space: nowrap;
}

._levelError_1bhl0_743 {
  background: #f8d7da;
  color: #721c24;
}

._levelWarning_1bhl0_753 {
  background: #fff3cd;
  color: #856404;
}

._levelInfo_1bhl0_763 {
  background: #d1ecf1;
  color: #0c5460;
}

._levelDebug_1bhl0_773 {
  background: #d1d3d4;
  color: #383d41;
}

._logTimestamp_1bhl0_783 {
  color: #666;
  min-width: 140px;
  white-space: nowrap;
}

._logService_1bhl0_795 {
  color: #007bff;
  font-weight: 500;
  min-width: 100px;
  white-space: nowrap;
}

._logMessage_1bhl0_809 {
  flex: 1;
  color: #333;
  word-break: break-word;
}

._logDetails_1bhl0_821 {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 0.8rem;
  color: #666;
  white-space: pre-wrap;
}

._logStackTrace_1bhl0_841 {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #ffe6e6;
  border-radius: 4px;
  font-size: 0.8rem;
  color: #dc3545;
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;
}

._noLogs_1bhl0_863 {
  text-align: center;
  padding: 3rem;
  color: #666;
  font-size: 1.1rem;
}

._loading_1bhl0_877 {
  text-align: center;
  padding: 3rem;
  color: #666;
}

._spinner_1bhl0_889 {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: _spin_1bhl0_889 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes _spin_1bhl0_889 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

._exportButton_1bhl0_921 {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

._exportButton_1bhl0_921:hover {
  background: #1e7e34;
}

/* Responsividade */
@media (max-width: 1024px) {
  ._header_1bhl0_97 {
    flex-direction: column;
    align-items: stretch;
  }

  ._controls_1bhl0_157 {
    justify-content: space-between;
  }

  ._logEntry_1bhl0_687 {
    flex-direction: column;
    gap: 0.5rem;
  }

  ._logTimestamp_1bhl0_783,
  ._logService_1bhl0_795 {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  ._statsBar_1bhl0_489 {
    flex-direction: column;
  }

  ._controls_1bhl0_157 {
    flex-direction: column;
    gap: 0.75rem;
  }

  ._filterGroup_1bhl0_171 {
    width: 100%;
    justify-content: space-between;
  }

  ._filterSelect_1bhl0_197,
  ._searchInput_1bhl0_245 {
    min-width: auto;
    flex: 1;
  }

  ._logsContainer_1bhl0_619 {
    font-size: 0.8rem;
  }

  ._logLevel_1bhl0_721 {
    min-width: 50px;
    font-size: 0.7rem;
  }

  ._metricsGrid_1bhl0_1055,
  ._systemMetricsGrid_1bhl0_1057 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Seções de Métricas do Prometheus */
._prometheusSection_1bhl0_1069 {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

._prometheusSection_1bhl0_1069 h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

._metricsGrid_1bhl0_1055 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

._metricCard_1bhl0_1113 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
}

._metricTitle_1bhl0_1129 {
  font-size: 0.8rem;
  opacity: 0.9;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  font-weight: 500;
}

._metricValue_1bhl0_1145 {
  font-size: 1.5rem;
  font-weight: bold;
}

._alertsSection_1bhl0_1155 h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

._alertsList_1bhl0_1169 {
  background: #f8f9fa;
  border-radius: 6px;
  overflow: hidden;
}

._alertItem_1bhl0_1181 {
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

._alertItem_1bhl0_1181:last-child {
  border-bottom: none;
}

._alertWarning_1bhl0_1205 {
  border-left: 4px solid #ffc107;
  background: #fff8e1;
}

._alertError_1bhl0_1215 {
  border-left: 4px solid #dc3545;
  background: #ffebee;
}

._alertInfo_1bhl0_1225 {
  border-left: 4px solid #17a2b8;
  background: #e3f2fd;
}

._alertIcon_1bhl0_1235 {
  font-size: 1.2rem;
  margin-top: 0.1rem;
}

._alertContent_1bhl0_1245 {
  flex: 1;
}

._alertMessage_1bhl0_1253 {
  font-weight: 500;
  color: #333;
  margin-bottom: 0.25rem;
}

._alertTime_1bhl0_1265 {
  font-size: 0.85rem;
  color: #666;
}

/* Seção de Métricas do Sistema */
._systemMetricsSection_1bhl0_1277 {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

._systemMetricsSection_1bhl0_1277 h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

._systemMetricsGrid_1bhl0_1057 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

._systemMetricCard_1bhl0_1319 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
}

._headerActions_1bhl0_1335 {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-wrap: wrap;
}

._autoRefreshLabel_1bhl0_1349 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #666;
  cursor: pointer;
}

._filters_1bhl0_1367 {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
}

._searchBox_1bhl0_1391 {
  flex: 1;
  min-width: 250px;
}
/**
 * 🎨 ADMIN DASHBOARD V3 - UI/UX MODERNO
 * Design System: Material Design 3 + Glassmorphism
 * Paleta: Dark Mode + Accent Colors
 */

/* ===== VARIÁVEIS CSS ===== */
:root {
  /* Cores Principais */
  --admin-primary: #6366f1;
  --admin-primary-dark: #4f46e5;
  --admin-primary-light: #8b5cf6;

  /* Cores de Fundo */
  --admin-bg-primary: #0f172a;
  --admin-bg-secondary: #1e293b;
  --admin-bg-tertiary: #334155;
  --admin-bg-glass: rgba(255, 255, 255, 0.1);

  /* Cores de Texto */
  --admin-text-primary: #f8fafc;
  --admin-text-secondary: #cbd5e1;
  --admin-text-muted: #94a3b8;

  /* Cores de Status */
  --admin-success: #10b981;
  --admin-warning: #f59e0b;
  --admin-error: #ef4444;
  --admin-info: #3b82f6;

  /* Sombras */
  --admin-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --admin-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --admin-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --admin-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

  /* Bordas */
  --admin-border-radius: 12px;
  --admin-border-radius-lg: 16px;
  --admin-border-color: rgba(255, 255, 255, 0.1);
}

/* ===== LOGIN CONTAINER ===== */
._loginContainer_fkk97_44 {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg,
    var(--admin-bg-primary) 0%,
    var(--admin-bg-secondary) 50%,
    var(--admin-primary-dark) 100%
  );
  padding: 20px;
  position: relative;
  overflow: hidden;
}

._loginContainer_fkk97_44::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  animation: _gridMove_fkk97_1 20s linear infinite;
}

@keyframes _gridMove_fkk97_1 {
  0% { transform: translate(0, 0); }
  100% { transform: translate(10px, 10px); }
}

._loginBox_fkk97_75 {
  background: var(--admin-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-border-radius-lg);
  padding: 48px;
  box-shadow: var(--admin-shadow-xl);
  max-width: 450px;
  width: 100%;
  text-align: center;
  position: relative;
  z-index: 1;
  animation: _loginBoxAppear_fkk97_1 0.6s ease-out;
}

@keyframes _loginBoxAppear_fkk97_1 {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

._loginBox_fkk97_75 h2 {
  color: var(--admin-text-primary);
  margin-bottom: 12px;
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-primary-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

._loginBox_fkk97_75 p {
  color: var(--admin-text-secondary);
  margin-bottom: 32px;
  font-size: 16px;
  font-weight: 400;
}

/* ===== FORMULÁRIO DE LOGIN ===== */
._loginForm_fkk97_120 {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

._inputGroup_fkk97_126 {
  text-align: left;
  position: relative;
}

._inputGroup_fkk97_126 label {
  display: block;
  margin-bottom: 12px;
  color: var(--admin-text-primary);
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

._passwordInput_fkk97_141 {
  width: 100%;
  padding: 16px 20px;
  background: var(--admin-bg-glass);
  border: 2px solid var(--admin-border-color);
  border-radius: var(--admin-border-radius);
  font-size: 16px;
  color: var(--admin-text-primary);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-sizing: border-box;
}

._passwordInput_fkk97_141::placeholder {
  color: var(--admin-text-muted);
}

._passwordInput_fkk97_141:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  transform: translateY(-1px);
}

._passwordInput_fkk97_141:disabled {
  background: rgba(255, 255, 255, 0.05);
  cursor: not-allowed;
  opacity: 0.6;
}

._buttonGroup_fkk97_171 {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

._loginButton_fkk97_177 {
  flex: 1;
  padding: 16px 24px;
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-primary-light));
  color: white;
  border: none;
  border-radius: var(--admin-border-radius);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

._loginButton_fkk97_177::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

._loginButton_fkk97_177:hover:not(:disabled)::before {
  left: 100%;
}

._loginButton_fkk97_177:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--admin-shadow-lg);
}

._loginButton_fkk97_177:active {
  transform: translateY(0);
}

._loginButton_fkk97_177:disabled {
  background: var(--admin-bg-tertiary);
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
}

._backButton_fkk97_223 {
  flex: 1;
  padding: 16px 24px;
  background: var(--admin-bg-tertiary);
  color: var(--admin-text-primary);
  border: 2px solid var(--admin-border-color);
  border-radius: var(--admin-border-radius);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

._backButton_fkk97_223:hover {
  background: var(--admin-bg-glass);
  border-color: var(--admin-primary);
  transform: translateY(-2px);
  box-shadow: var(--admin-shadow-md);
}

._error_fkk97_243 {
  color: var(--admin-error);
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  padding: 16px;
  border-radius: var(--admin-border-radius);
  font-size: 14px;
  font-weight: 500;
  animation: _errorShake_fkk97_1 0.5s ease-in-out;
}

@keyframes _errorShake_fkk97_1 {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

._loginHint_fkk97_260 {
  margin-top: 24px;
  padding: 16px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: var(--admin-border-radius);
  color: var(--admin-info);
  font-size: 14px;
  font-weight: 500;
}

/* ===== CONTAINER PRINCIPAL ===== */
._adminContainer_fkk97_272 {
  min-height: 100vh;
  background: var(--admin-bg-primary);
  display: flex;
  flex-direction: column;
  position: relative;
}

._adminContainer_fkk97_272::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* ===== HEADER MODERNO ===== */
._adminHeader_fkk97_294 {
  background: var(--admin-bg-glass);
  backdrop-filter: blur(20px);
  padding: 24px 32px;
  border-bottom: 1px solid var(--admin-border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--admin-shadow-md);
  position: relative;
  z-index: 10;
}

._headerLeft_fkk97_307 {
  display: flex;
  align-items: center;
  gap: 16px;
}

._headerLeft_fkk97_307 h1 {
  margin: 0;
  color: var(--admin-text-primary);
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-primary-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

._version_fkk97_324 {
  color: var(--admin-text-muted);
  font-size: 14px;
  font-weight: 500;
  padding: 4px 12px;
  background: var(--admin-bg-tertiary);
  border-radius: 20px;
  border: 1px solid var(--admin-border-color);
}

._headerRight_fkk97_334 {
  display: flex;
  align-items: center;
  gap: 24px;
}

._adminUser_fkk97_340 {
  color: var(--admin-text-secondary);
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--admin-bg-tertiary);
  border-radius: var(--admin-border-radius);
  border: 1px solid var(--admin-border-color);
}

._logoutButton_fkk97_353 {
  padding: 12px 20px;
  background: linear-gradient(135deg, var(--admin-error), #dc2626);
  color: white;
  border: none;
  border-radius: var(--admin-border-radius);
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

._logoutButton_fkk97_353:hover {
  transform: translateY(-2px);
  box-shadow: var(--admin-shadow-lg);
  background: linear-gradient(135deg, #dc2626, #b91c1c);
}

/* ===== NAVEGAÇÃO POR ABAS MODERNA ===== */
._tabNavigation_fkk97_375 {
  background: var(--admin-bg-secondary);
  padding: 8px 32px;
  border-bottom: 1px solid var(--admin-border-color);
  display: flex;
  gap: 8px;
  overflow-x: auto;
  position: relative;
  z-index: 5;
}

._tabNavigation_fkk97_375::-webkit-scrollbar {
  height: 4px;
}

._tabNavigation_fkk97_375::-webkit-scrollbar-track {
  background: var(--admin-bg-tertiary);
  border-radius: 2px;
}

._tabNavigation_fkk97_375::-webkit-scrollbar-thumb {
  background: var(--admin-primary);
  border-radius: 2px;
}

._tabButton_fkk97_400 {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: var(--admin-bg-tertiary);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  color: var(--admin-text-secondary);
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

._tabButton_fkk97_400::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  transition: left 0.5s;
}

._tabButton_fkk97_400:hover::before {
  left: 100%;
}

._tabButton_fkk97_400:hover {
  background: var(--admin-bg-glass);
  color: var(--admin-text-primary);
  transform: translateY(-2px);
  box-shadow: var(--admin-shadow-md);
  border-color: var(--admin-primary);
}

._tabButton_fkk97_400._active_fkk97_440 {
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-primary-light));
  color: white;
  border-color: var(--admin-primary);
  box-shadow: var(--admin-shadow-lg);
  transform: translateY(-2px);
}

._tabButton_fkk97_400._active_fkk97_440::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid var(--admin-bg-primary);
}

._tabIcon_fkk97_461 {
  font-size: 20px;
  transition: transform 0.3s ease;
}

._tabButton_fkk97_400:hover ._tabIcon_fkk97_461 {
  transform: scale(1.1);
}

._tabButton_fkk97_400._active_fkk97_440 ._tabIcon_fkk97_461 {
  transform: scale(1.1) rotate(5deg);
}

._tabLabel_fkk97_474 {
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== ÁREA DE CONTEÚDO ===== */
._adminContent_fkk97_482 {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
  position: relative;
  z-index: 1;
}

._adminContent_fkk97_482::-webkit-scrollbar {
  width: 8px;
}

._adminContent_fkk97_482::-webkit-scrollbar-track {
  background: var(--admin-bg-secondary);
  border-radius: 4px;
}

._adminContent_fkk97_482::-webkit-scrollbar-thumb {
  background: var(--admin-primary);
  border-radius: 4px;
}

._tabContent_fkk97_504 {
  max-width: 1400px;
  margin: 0 auto;
  animation: _contentFadeIn_fkk97_1 0.5s ease-out;
}

@keyframes _contentFadeIn_fkk97_1 {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

._tabContent_fkk97_504 h2 {
  margin: 0 0 32px 0;
  color: var(--admin-text-primary);
  font-size: 32px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px 32px;
  background: var(--admin-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-border-radius-lg);
  box-shadow: var(--admin-shadow-md);
}

._tabContent_fkk97_504 h2::after {
  content: '';
  flex: 1;
  height: 2px;
  background: linear-gradient(90deg, var(--admin-primary), transparent);
  border-radius: 1px;
}

/* ===== FOOTER MODERNO ===== */
._adminFooter_fkk97_546 {
  background: var(--admin-bg-glass);
  backdrop-filter: blur(20px);
  padding: 24px 32px;
  border-top: 1px solid var(--admin-border-color);
  margin-top: auto;
  position: relative;
  z-index: 5;
}

._footerInfo_fkk97_556 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--admin-text-muted);
  font-size: 14px;
  font-weight: 500;
}

._footerInfo_fkk97_556 span:first-child {
  display: flex;
  align-items: center;
  gap: 8px;
}

._footerInfo_fkk97_556 span:first-child::before {
  content: '⚡';
  font-size: 16px;
}

._footerInfo_fkk97_556 span:last-child {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--admin-text-secondary);
}

._footerInfo_fkk97_556 span:last-child::before {
  content: '🕒';
  font-size: 14px;
}

/* ===== RESPONSIVIDADE MODERNA ===== */

/* Tablet */
@media (max-width: 1024px) {
  ._adminHeader_fkk97_294 {
    padding: 20px 24px;
  }

  ._headerLeft_fkk97_307 h1 {
    font-size: 24px;
  }

  ._tabNavigation_fkk97_375 {
    padding: 8px 24px;
  }

  ._adminContent_fkk97_482 {
    padding: 24px;
  }

  ._tabContent_fkk97_504 h2 {
    font-size: 28px;
    padding: 20px 24px;
  }
}

/* Mobile Large */
@media (max-width: 768px) {
  ._adminHeader_fkk97_294 {
    padding: 16px 20px;
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  ._headerLeft_fkk97_307 {
    flex-direction: column;
    gap: 8px;
  }

  ._headerLeft_fkk97_307 h1 {
    font-size: 22px;
  }

  ._headerRight_fkk97_334 {
    gap: 16px;
  }

  ._tabNavigation_fkk97_375 {
    padding: 8px 20px;
    gap: 4px;
  }

  ._tabButton_fkk97_400 {
    padding: 12px 16px;
    gap: 8px;
  }

  ._tabLabel_fkk97_474 {
    display: none;
  }

  ._tabIcon_fkk97_461 {
    font-size: 18px;
  }

  ._adminContent_fkk97_482 {
    padding: 20px;
  }

  ._tabContent_fkk97_504 h2 {
    font-size: 24px;
    padding: 16px 20px;
    margin-bottom: 24px;
  }

  ._footerInfo_fkk97_556 {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  ._loginBox_fkk97_75 {
    padding: 32px 24px;
  }

  ._buttonGroup_fkk97_171 {
    flex-direction: column;
    gap: 12px;
  }

  ._loginButton_fkk97_177,
  ._backButton_fkk97_223 {
    padding: 14px 20px;
  }
}

/* Mobile Small */
@media (max-width: 480px) {
  ._loginContainer_fkk97_44 {
    padding: 16px;
  }

  ._loginBox_fkk97_75 {
    padding: 24px 20px;
  }

  ._loginBox_fkk97_75 h2 {
    font-size: 24px;
  }

  ._adminHeader_fkk97_294 {
    padding: 12px 16px;
  }

  ._headerLeft_fkk97_307 h1 {
    font-size: 20px;
  }

  ._adminContent_fkk97_482 {
    padding: 16px;
  }

  ._tabNavigation_fkk97_375 {
    padding: 6px 16px;
  }

  ._tabButton_fkk97_400 {
    padding: 10px 12px;
    min-width: 48px;
  }

  ._tabContent_fkk97_504 h2 {
    font-size: 20px;
    padding: 12px 16px;
    margin-bottom: 20px;
  }

  ._adminFooter_fkk97_546 {
    padding: 16px 20px;
  }

  ._version_fkk97_324 {
    font-size: 12px;
    padding: 2px 8px;
  }

  ._adminUser_fkk97_340 {
    font-size: 14px;
    padding: 6px 12px;
  }

  ._logoutButton_fkk97_353 {
    padding: 8px 16px;
    font-size: 12px;
  }
}

/* Ultra Small */
@media (max-width: 320px) {
  ._loginBox_fkk97_75 {
    padding: 20px 16px;
  }

  ._adminHeader_fkk97_294 {
    padding: 10px 12px;
  }

  ._adminContent_fkk97_482 {
    padding: 12px;
  }

  ._tabNavigation_fkk97_375 {
    padding: 4px 12px;
  }

  ._tabContent_fkk97_504 h2 {
    font-size: 18px;
    padding: 10px 12px;
  }
}

/* ===== ANIMAÇÕES E EFEITOS ESPECIAIS ===== */
@keyframes _pulse_fkk97_1 {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes _glow_fkk97_1 {
  0%, 100% { box-shadow: 0 0 5px var(--admin-primary); }
  50% { box-shadow: 0 0 20px var(--admin-primary), 0 0 30px var(--admin-primary); }
}

._tabButton_fkk97_400._active_fkk97_440 {
  animation: _glow_fkk97_1 2s ease-in-out infinite;
}

/* ===== ELEMENTOS ADICIONAIS ===== */

/* Status indicators */
._status-indicator_fkk97_788 {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

._status-badge_fkk97_798 {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  background: var(--admin-bg-tertiary);
  border: 1px solid var(--admin-border-color);
}

._status-badge_fkk97_798._online_fkk97_807 {
  background: rgba(16, 185, 129, 0.1);
  border-color: var(--admin-success);
  color: var(--admin-success);
}

._status-badge_fkk97_798._offline_fkk97_813 {
  background: rgba(239, 68, 68, 0.1);
  border-color: var(--admin-error);
  color: var(--admin-error);
}

/* Header controls */
._header-controls_fkk97_820 {
  display: flex;
  align-items: center;
  gap: 12px;
}

._control-button_fkk97_826 {
  padding: 8px;
  background: var(--admin-bg-tertiary);
  border: 1px solid var(--admin-border-color);
  border-radius: 8px;
  color: var(--admin-text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
}

._control-button_fkk97_826:hover {
  background: var(--admin-bg-glass);
  border-color: var(--admin-primary);
  color: var(--admin-text-primary);
  transform: translateY(-1px);
}

._notifications-badge_fkk97_844 {
  position: relative;
  padding: 8px;
  background: var(--admin-bg-tertiary);
  border: 1px solid var(--admin-border-color);
  border-radius: 8px;
  color: var(--admin-text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
}

._notifications-badge_fkk97_844:hover {
  background: var(--admin-bg-glass);
  border-color: var(--admin-primary);
  color: var(--admin-text-primary);
}

._notification-count_fkk97_861 {
  position: absolute;
  top: -4px;
  right: -4px;
  background: var(--admin-error);
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Admin user info */
._adminUser_fkk97_340 {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

._adminUser_fkk97_340 small {
  font-size: 11px;
  color: var(--admin-text-muted);
  margin-top: 2px;
}

/* Tab indicator */
._tab-indicator_fkk97_891 {
  position: absolute;
  bottom: 0;
  height: 3px;
  background: var(--active-color, var(--admin-primary));
  border-radius: 2px 2px 0 0;
  transition: all 0.3s ease;
}

._active-indicator_fkk97_900 {
  margin-left: 8px;
  color: currentColor;
  animation: _pulse_fkk97_1 2s ease-in-out infinite;
}

/* Breadcrumb */
._breadcrumb_fkk97_907 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 24px;
  padding: 12px 20px;
  background: var(--admin-bg-glass);
  backdrop-filter: blur(10px);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-border-radius);
  color: var(--admin-text-secondary);
  font-size: 14px;
  font-weight: 500;
}

._breadcrumb_fkk97_907 span:first-child {
  color: var(--admin-text-primary);
}

/* Tab description */
._tab-description_fkk97_927 {
  display: block;
  font-size: 16px;
  font-weight: 400;
  color: var(--admin-text-secondary);
  margin-top: 8px;
}

/* Footer stats */
._footer-stats_fkk97_936 {
  display: flex;
  gap: 16px;
  font-size: 12px;
}

._footer-stats_fkk97_936 span {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Login enhancements */
._login-header_fkk97_949 {
  margin-bottom: 32px;
}

._login-header_fkk97_949 ._system-status_fkk97_953 {
  margin-top: 16px;
  padding: 8px 16px;
  background: var(--admin-bg-tertiary);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-border-radius);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

._login-footer_fkk97_966 {
  margin-top: 24px;
  text-align: center;
  color: var(--admin-text-muted);
}

/* ===== LOGIN CLEAN (NOVO DESIGN) ===== */
._cleanLoginContainer_fkk97_973 {
  min-height: 100vh;
  background: #f8fafc;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  padding: 1rem;
}

._cleanLoginBox_fkk97_983 {
  background: white;
  padding: 2.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  width: 100%;
  max-width: 400px;
  border: 1px solid #e2e8f0;
}

._cleanLoginHeader_fkk97_993 {
  text-align: center;
  margin-bottom: 2rem;
}

._cleanLoginHeader_fkk97_993 h2 {
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

._cleanLoginHeader_fkk97_993 p {
  color: #64748b;
  font-size: 0.9rem;
  margin: 0;
}

._cleanLoginForm_fkk97_1011 {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

._cleanInputGroup_fkk97_1017 {
  display: flex;
  flex-direction: column;
}

._cleanPasswordInput_fkk97_1022 {
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: #f8fafc;
}

._cleanPasswordInput_fkk97_1022:focus {
  outline: none;
  border-color: #3b82f6;
  background: white;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

._cleanPasswordInput_fkk97_1022:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

._cleanError_fkk97_1043 {
  color: #dc2626;
  font-size: 0.875rem;
  text-align: center;
  padding: 0.5rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
}

._cleanLoginButton_fkk97_1053 {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

._cleanLoginButton_fkk97_1053:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

._cleanLoginButton_fkk97_1053:active {
  transform: translateY(0);
}

._cleanLoginButton_fkk97_1053:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

._loading-spinner_fkk97_1081 {
  display: inline-block;
  animation: _spin_fkk97_1 1s linear infinite;
  margin-right: 8px;
}

@keyframes _spin_fkk97_1 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  :root {
    --admin-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --admin-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    --admin-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
    --admin-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);
  }
}
