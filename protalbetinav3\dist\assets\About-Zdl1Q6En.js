import { j as jsxDevRuntimeExports } from "./vendor-react-BH-kks1U.js";
import "./vendor-misc-BhjEiCpb.js";
import "./services-Ckq1alRq.js";
import "./dashboard-D6oq-mHv.js";
import "./context-CJb-Kg-5.js";
import "./hooks-DiB_syzW.js";
import "./vendor-utils-CjlX8hrF.js";
import "./vendor-charts-JJkNskvH.js";
import "./admin-AVDlea_I.js";
import "./utils-C8SspVp8.js";
import "./game-colors-DknHlpST.js";
import "./game-association-1fe4bzjE.js";
import "./game-letters-C11f_WoE.js";
import "./game-memory-Q6N7kS_-.js";
import "./vendor-motion-CThs1zaH.js";
import "./game-musical-4K52sZ4i.js";
import "./game-patterns-C1u1YIjS.js";
import "./game-puzzle-y0iWrjae.js";
import "./game-numbers-CLPWZorL.js";
import "./game-creative-danUmc-P.js";
const container = "_container_z1ypv_15";
const heroBanner = "_heroBanner_z1ypv_81";
const heroContent = "_heroContent_z1ypv_99";
const heroTitle = "_heroTitle_z1ypv_109";
const heroSubtitle = "_heroSubtitle_z1ypv_125";
const badgeContainer = "_badgeContainer_z1ypv_139";
const techBadge = "_techBadge_z1ypv_153";
const badgePrimary = "_badgePrimary_z1ypv_177";
const badgeGreen = "_badgeGreen_z1ypv_185";
const badgePurple = "_badgePurple_z1ypv_193";
const section = "_section_z1ypv_203";
const sectionTitle = "_sectionTitle_z1ypv_221";
const sectionContent = "_sectionContent_z1ypv_249";
const benefitsList = "_benefitsList_z1ypv_271";
const benefitItem = "_benefitItem_z1ypv_283";
const benefitEmoji = "_benefitEmoji_z1ypv_305";
const benefitText = "_benefitText_z1ypv_315";
const highlightBox = "_highlightBox_z1ypv_327";
const highlightTitle = "_highlightTitle_z1ypv_345";
const highlightText = "_highlightText_z1ypv_357";
const aiFeatureGrid = "_aiFeatureGrid_z1ypv_371";
const aiFeatureCard = "_aiFeatureCard_z1ypv_385";
const aiFeatureIcon = "_aiFeatureIcon_z1ypv_415";
const aiFeatureTitle = "_aiFeatureTitle_z1ypv_425";
const aiFeatureDescription = "_aiFeatureDescription_z1ypv_439";
const techCategory = "_techCategory_z1ypv_453";
const techCategoryTitle = "_techCategoryTitle_z1ypv_461";
const techBadges = "_techBadges_z1ypv_481";
const styles = {
  container,
  heroBanner,
  heroContent,
  heroTitle,
  heroSubtitle,
  badgeContainer,
  techBadge,
  badgePrimary,
  badgeGreen,
  badgePurple,
  section,
  sectionTitle,
  sectionContent,
  benefitsList,
  benefitItem,
  benefitEmoji,
  benefitText,
  highlightBox,
  highlightTitle,
  highlightText,
  aiFeatureGrid,
  aiFeatureCard,
  aiFeatureIcon,
  aiFeatureTitle,
  aiFeatureDescription,
  techCategory,
  techCategoryTitle,
  techBadges
};
function About({ onBackToHome }) {
  return /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.container, children: [
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.heroBanner, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.heroContent, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h1", { className: styles.heroTitle, children: "Portal Betina" }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
        lineNumber: 16,
        columnNumber: 11
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: styles.heroSubtitle, children: "Transformando vidas através de atividades neuropedagógicas potencializadas por inteligência artificial" }, void 0, false, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
        lineNumber: 17,
        columnNumber: 11
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.badgeContainer, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: `${styles.techBadge} ${styles.badgePrimary}`, children: "🧠 Desenvolvimento Cognitivo" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 21,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: `${styles.techBadge} ${styles.badgeGreen}`, children: "🤖 Potencializado por IA" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 24,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: `${styles.techBadge} ${styles.badgePurple}`, children: "♿ 100% Acessível" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 27,
          columnNumber: 13
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
        lineNumber: 20,
        columnNumber: 11
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
      lineNumber: 15,
      columnNumber: 9
    }, this) }, void 0, false, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
      lineNumber: 14,
      columnNumber: 7
    }, this),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("section", { className: styles.section, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: styles.sectionTitle, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "🧠" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 37,
          columnNumber: 11
        }, this),
        "O que são Atividades Neuropedagógicas?"
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
        lineNumber: 36,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.sectionContent, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Atividades neuropedagógicas" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 42,
            columnNumber: 13
          }, this),
          " são intervenções estruturadas que estimulam o desenvolvimento cognitivo, emocional e social, especialmente para crianças com autismo, TDAH ou outras necessidades específicas."
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 41,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: [
          "Elas combinam princípios da ",
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "neurociência" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 47,
            columnNumber: 41
          }, this),
          ", ",
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "psicologia" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 47,
            columnNumber: 72
          }, this),
          " e",
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "pedagogia" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 48,
            columnNumber: 13
          }, this),
          " para promover habilidades essenciais como:"
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 46,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: styles.benefitsList, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: styles.benefitItem, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitEmoji, children: "🎯" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 53,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitText, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Atenção e Concentração:" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
                lineNumber: 55,
                columnNumber: 17
              }, this),
              " Melhorar o foco e a capacidade de manter a atenção"
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 54,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 52,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: styles.benefitItem, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitEmoji, children: "🧠" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 59,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitText, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Memória:" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
                lineNumber: 61,
                columnNumber: 17
              }, this),
              " Fortalecer a memória de trabalho e de longo prazo"
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 60,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 58,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: styles.benefitItem, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitEmoji, children: "🤔" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 65,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitText, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Raciocínio Lógico:" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
                lineNumber: 67,
                columnNumber: 17
              }, this),
              " Desenvolver habilidades de resolução de problemas"
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 66,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 64,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: styles.benefitItem, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitEmoji, children: "✋" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 71,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitText, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Coordenação Motora:" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
                lineNumber: 73,
                columnNumber: 17
              }, this),
              " Aprimorar habilidades motoras finas e grossas"
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 72,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 70,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: styles.benefitItem, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitEmoji, children: "😊" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 77,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitText, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Regulação Emocional:" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
                lineNumber: 79,
                columnNumber: 17
              }, this),
              " Aprender a identificar e gerenciar emoções"
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 78,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 76,
            columnNumber: 13
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 51,
          columnNumber: 11
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
        lineNumber: 40,
        columnNumber: 9
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
      lineNumber: 35,
      columnNumber: 7
    }, this),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("section", { className: styles.section, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: styles.sectionTitle, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "🤖" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 89,
          columnNumber: 11
        }, this),
        "Como a Inteligência Artificial Potencializa o Aprendizado"
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
        lineNumber: 88,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.sectionContent, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.highlightBox, children: [
          "          ",
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h3", { className: styles.highlightTitle, children: "🚀 Portal Betina V3 - Tecnologia Avançada para Desenvolvimento" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 93,
            columnNumber: 58
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: styles.highlightText, children: "Nossa plataforma integra inteligência artificial com metodologias terapêuticas comprovadas para oferecer uma experiência personalizada e eficaz para cada criança." }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 96,
            columnNumber: 11
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 93,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.aiFeatureGrid, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.aiFeatureCard, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.aiFeatureIcon, children: "🎯" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 104,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: styles.aiFeatureTitle, children: "Personalização Inteligente" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 105,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: styles.aiFeatureDescription, children: "A IA adapta a dificuldade e o ritmo das atividades baseado no desempenho individual da criança" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 106,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 103,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.aiFeatureCard, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.aiFeatureIcon, children: "📊" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 112,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: styles.aiFeatureTitle, children: "Análise de Progresso" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 113,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: styles.aiFeatureDescription, children: "Algoritmos analisam padrões de aprendizado e fornecem insights sobre o desenvolvimento cognitivo" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 114,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 111,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.aiFeatureCard, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.aiFeatureIcon, children: "🎮" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 120,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: styles.aiFeatureTitle, children: "Engajamento Otimizado" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 121,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: styles.aiFeatureDescription, children: "IA determina os melhores momentos e tipos de feedback para manter a motivação e interesse" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 122,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 119,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.aiFeatureCard, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.aiFeatureIcon, children: "🔄" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 128,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: styles.aiFeatureTitle, children: "Adaptação Contínua" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 129,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: styles.aiFeatureDescription, children: "O sistema aprende continuamente com as interações, melhorando constantemente a experiência" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 130,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 127,
            columnNumber: 13
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 102,
          columnNumber: 11
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
        lineNumber: 92,
        columnNumber: 9
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
      lineNumber: 87,
      columnNumber: 7
    }, this),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("section", { className: styles.section, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: styles.sectionTitle, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "⚙️" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 141,
          columnNumber: 11
        }, this),
        "Tecnologias e Metodologias Aplicadas"
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
        lineNumber: 140,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.sectionContent, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "O Portal Betina utiliza tecnologias modernas e metodologias baseadas em evidências científicas:" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 145,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.techCategory, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: styles.techCategoryTitle, children: "🧬 Base Científica" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 150,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.techBadges, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.techBadge, children: "Neurociência Cognitiva" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 152,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.techBadge, children: "Psicologia do Desenvolvimento" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 153,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.techBadge, children: "Pedagogia Inclusiva" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 154,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.techBadge, children: "Terapia ABA" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 155,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.techBadge, children: "Neuroplasticidade" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 156,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 151,
            columnNumber: 13
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 149,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.techCategory, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: styles.techCategoryTitle, children: "💻 Tecnologia" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 161,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.techBadges, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.techBadge, children: "React + IA" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 163,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.techBadge, children: "Machine Learning" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 164,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.techBadge, children: "Design Responsivo" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 165,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.techBadge, children: "Acessibilidade Web" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 166,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.techBadge, children: "Progressive Web App" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 167,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 162,
            columnNumber: 13
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 160,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.techCategory, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h4", { className: styles.techCategoryTitle, children: "🌈 Acessibilidade" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 172,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.techBadges, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.techBadge, children: "Screen Reader" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 174,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.techBadge, children: "Alto Contraste" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 175,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.techBadge, children: "Navegação por Teclado" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 176,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.techBadge, children: "Feedback Háptico" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 177,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.techBadge, children: "WCAG 2.1 AA" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 178,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 173,
            columnNumber: 13
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 171,
          columnNumber: 11
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
        lineNumber: 144,
        columnNumber: 9
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
      lineNumber: 139,
      columnNumber: 7
    }, this),
    /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("section", { className: styles.section, children: [
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("h2", { className: styles.sectionTitle, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { children: "👨‍👩‍👧‍👦" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 187,
          columnNumber: 11
        }, this),
        "Para Pais, Terapeutas e Educadores"
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
        lineNumber: 186,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.sectionContent, children: [
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { children: "O Portal Betina foi desenvolvido para ser uma ferramenta colaborativa entre famílias e profissionais:" }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 191,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("ul", { className: styles.benefitsList, children: [
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: styles.benefitItem, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitEmoji, children: "👩‍⚕️" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 197,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitText, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Para Terapeutas:" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
                lineNumber: 199,
                columnNumber: 17
              }, this),
              " Ferramentas complementares para sessões presenciais e atividades para casa"
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 198,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 196,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: styles.benefitItem, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitEmoji, children: "👨‍🏫" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 203,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitText, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Para Educadores:" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
                lineNumber: 205,
                columnNumber: 17
              }, this),
              " Recursos para inclusão escolar e desenvolvimento de habilidades específicas"
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 204,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 202,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: styles.benefitItem, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitEmoji, children: "👨‍👩‍👧" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 209,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitText, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Para Famílias:" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
                lineNumber: 211,
                columnNumber: 17
              }, this),
              " Atividades estruturadas para momentos de qualidade e desenvolvimento em casa"
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 210,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 208,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("li", { className: styles.benefitItem, children: [
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitEmoji, children: "🤝" }, void 0, false, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 215,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("span", { className: styles.benefitText, children: [
              /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "Colaboração:" }, void 0, false, {
                fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
                lineNumber: 217,
                columnNumber: 17
              }, this),
              " Dados e progresso compartilhados entre todos os envolvidos no cuidado da criança"
            ] }, void 0, true, {
              fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
              lineNumber: 216,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 214,
            columnNumber: 13
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 195,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("div", { className: styles.highlightBox, children: /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("p", { className: styles.highlightText, children: [
          "💝 ",
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("strong", { children: "100% Gratuito e Sempre Será" }, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 224,
            columnNumber: 18
          }, this),
          /* @__PURE__ */ jsxDevRuntimeExports.jsxDEV("br", {}, void 0, false, {
            fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
            lineNumber: 224,
            columnNumber: 62
          }, this),
          "Acreditamos que toda criança merece acesso a ferramentas de qualidade para seu desenvolvimento, independentemente da condição socioeconômica da família."
        ] }, void 0, true, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 223,
          columnNumber: 13
        }, this) }, void 0, false, {
          fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
          lineNumber: 222,
          columnNumber: 11
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
        lineNumber: 190,
        columnNumber: 9
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
      lineNumber: 185,
      columnNumber: 7
    }, this)
  ] }, void 0, true, {
    fileName: "C:/Projetos/portalbettinabkp/protalbetinav3/src/components/pages/About/About.jsx",
    lineNumber: 12,
    columnNumber: 5
  }, this);
}
export {
  About as default
};
//# sourceMappingURL=About-Zdl1Q6En.js.map
