/**
 * 🔍 DEBUG ROUTES - Portal Betina V3
 * Rotas para testar funcionalidades específicas do sistema
 */

import express from 'express';

const router = express.Router();

/**
 * 🔍 Testar se o SystemOrchestrator detecta login do dashboard
 */
router.get('/dashboard-login', async (req, res) => {
  try {
    const systemOrchestrator = req.systemOrchestrator;
    
    if (!systemOrchestrator) {
      return res.status(500).json({
        success: false,
        error: 'SystemOrchestrator not available',
        loginDetected: false,
        reason: 'SystemOrchestrator não está disponível'
      });
    }

    // Testar checkDashboardLogin passando o request
    const loginDetected = systemOrchestrator.checkDashboardLogin(req);
    
    // Informações adicionais para debug
    const debugInfo = {
      success: true,
      loginDetected,
      reason: loginDetected ? 'Login ativo detectado' : 'Nenhum login ativo detectado',
      timestamp: new Date().toISOString(),
      systemOrchestratorAvailable: !!systemOrchestrator,
      checkDashboardLoginMethod: typeof systemOrchestrator.checkDashboardLogin
    };

    console.log('🔍 DEBUG - Dashboard Login Check:', debugInfo);

    res.json(debugInfo);
  } catch (error) {
    console.error('❌ Erro ao testar dashboard login:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      loginDetected: false,
      reason: 'Erro interno do servidor'
    });
  }
});

/**
 * 🔍 Verificar status do SystemOrchestrator
 */
router.get('/system-status', async (req, res) => {
  try {
    const systemOrchestrator = req.systemOrchestrator;
    
    if (!systemOrchestrator) {
      return res.status(500).json({
        success: false,
        error: 'SystemOrchestrator not available'
      });
    }

    // Coletar informações do sistema
    const status = {
      success: true,
      systemOrchestratorAvailable: true,
      sessionDataSize: systemOrchestrator.sessionData ? systemOrchestrator.sessionData.size : 0,
      activeSessions: systemOrchestrator.sessionData ? Array.from(systemOrchestrator.sessionData.keys()) : [],
      dashboardLogin: systemOrchestrator.checkDashboardLogin(req),
      timestamp: new Date().toISOString()
    };

    console.log('🔍 DEBUG - System Status:', status);

    res.json(status);
  } catch (error) {
    console.error('❌ Erro ao verificar status do sistema:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 🔍 Verificar sessões ativas no SystemOrchestrator E no banco PostgreSQL
 */
router.get('/sessions', async (req, res) => {
  try {
    const systemOrchestrator = req.systemOrchestrator;

    if (!systemOrchestrator) {
      return res.status(500).json({
        success: false,
        error: 'SystemOrchestrator not available'
      });
    }

    // 1. Sessões do SystemOrchestrator (memória)
    const memorySessions = [];
    if (systemOrchestrator.sessionData) {
      for (const [sessionId, sessionData] of systemOrchestrator.sessionData.entries()) {
        memorySessions.push({
          sessionId,
          timestamp: sessionData.timestamp || 'N/A',
          gameType: sessionData.gameType || 'N/A',
          childId: sessionData.childId || 'N/A',
          source: 'memory'
        });
      }
    }

    // 2. Sessões do banco PostgreSQL
    let databaseSessions = [];
    try {
      if (systemOrchestrator.db && typeof systemOrchestrator.db.query === 'function') {
        const dbResult = await systemOrchestrator.db.query(
          'SELECT session_id, game_id, user_id, score, level, duration, accuracy, created_at FROM game_sessions ORDER BY created_at DESC LIMIT 10'
        );

        databaseSessions = dbResult.rows.map(row => ({
          sessionId: row.session_id,
          timestamp: row.created_at,
          gameType: row.game_id,
          childId: row.user_id,
          score: row.score,
          level: row.level,
          duration: row.duration,
          accuracy: row.accuracy,
          source: 'database'
        }));
      }
    } catch (dbError) {
      console.error('❌ Erro ao buscar sessões do banco:', dbError);
    }

    const response = {
      success: true,
      memoryCount: memorySessions.length,
      databaseCount: databaseSessions.length,
      totalCount: memorySessions.length + databaseSessions.length,
      memorySessions: memorySessions.slice(0, 5),
      databaseSessions: databaseSessions.slice(0, 5),
      dashboardLogin: systemOrchestrator.checkDashboardLogin(req),
      timestamp: new Date().toISOString()
    };

    console.log('🔍 DEBUG - Sessions (Memory + Database):', response);

    res.json(response);
  } catch (error) {
    console.error('❌ Erro ao verificar sessões:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 🔍 Testar conexão com banco PostgreSQL
 */
router.get('/database', async (req, res) => {
  try {
    const systemOrchestrator = req.systemOrchestrator;

    if (!systemOrchestrator) {
      return res.status(500).json({
        success: false,
        error: 'SystemOrchestrator not available'
      });
    }

    const dbStatus = {
      success: true,
      connected: false,
      tablesExist: false,
      sessionCount: 0,
      error: null,
      timestamp: new Date().toISOString()
    };

    try {
      if (systemOrchestrator.db && typeof systemOrchestrator.db.query === 'function') {
        // Testar conexão básica
        await systemOrchestrator.db.query('SELECT NOW()');
        dbStatus.connected = true;

        // Verificar se tabela game_sessions existe
        const tableCheck = await systemOrchestrator.db.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name = 'game_sessions'
          );
        `);

        dbStatus.tablesExist = tableCheck.rows[0].exists;

        if (dbStatus.tablesExist) {
          // Contar sessões na tabela
          const countResult = await systemOrchestrator.db.query('SELECT COUNT(*) as count FROM game_sessions');
          dbStatus.sessionCount = parseInt(countResult.rows[0].count);
        }

      } else {
        dbStatus.error = 'Database service not available in SystemOrchestrator';
      }
    } catch (dbError) {
      dbStatus.connected = false;
      dbStatus.error = dbError.message;
    }

    console.log('🔍 DEBUG - Database Status:', dbStatus);

    res.json(dbStatus);
  } catch (error) {
    console.error('❌ Erro ao testar banco:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router;
