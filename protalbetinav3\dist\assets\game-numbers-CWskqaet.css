/**
 * @file ContagemNumeros.module.css
 * @description Estilos modulares para o Jogo de Contagem de Números
 * @version 3.1.0
 */

/* Variáveis CSS para consistência e reutilização */
:root {
  --card-background: rgba(255, 255, 255, 0.1);
  --card-border: 1px solid rgba(255, 255, 255, 0.2);
  --card-blur: blur(10px);
  --success-bg: rgba(76, 175, 80, 0.3);
  --success-border: rgba(76, 175, 80, 0.5);
  --error-bg: rgba(244, 67, 54, 0.3);
  --error-border: rgba(244, 67, 54, 0.5);
  --primary-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --game-background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-color: #667eea;
  --primary-hover: #5a6fd8;
}

/* Container principal - PADRÃO LETTERRECOGNITION */
._contagemNumerosGame_gekck_47 {
  width: 100%;
  height: 100vh;
  background: var(--game-background);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  box-sizing: border-box;
}

/* Conteúdo do jogo - PADRÃO LETTERRECOGNITION */
._gameContent_gekck_73 {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Área do jogo - PADRÃO LETTERRECOGNITION */
._gameArea_gekck_93 {
  flex: 1;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

/* Header do jogo - PADRÃO LETTERRECOGNITION EXATO */
._gameHeader_gekck_125 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 3rem 1rem 1rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  position: relative;
  min-height: 70px;
}

._gameTitle_gekck_153 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

._headerTtsButton_gekck_177 {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.75rem;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  min-width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

._headerTtsButton_gekck_177:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.05);
}

._headerTtsButton_gekck_177._ttsActive_gekck_229 {
  background: rgba(76, 175, 80, 0.3);
  border-color: #4CAF50;
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.4);
}

/* Estatísticas do jogo - PADRÃO LETTERRECOGNITION */
._gameStats_gekck_243 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

._statCard_gekck_257 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

._statCard_gekck_257::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #E91E63);
  opacity: 0.7;
}

._statValue_gekck_301 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: white;
}

._statLabel_gekck_315 {
  font-size: 0.8rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.8);
}

/* Menu de atividades - PADRÃO LETTERRECOGNITION */
._activityMenu_gekck_329 {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  justify-content: center;
  flex-wrap: wrap;
}

._activityButton_gekck_345 {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

._activityButton_gekck_345:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

._activityButton_gekck_345._active_gekck_383 {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.6);
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

/* Área do jogo - PADRÃO LETTERRECOGNITION EXATO */
._gameArea_gekck_93 {
  flex: 1;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

/* Atividade de som - PADRÃO LETTERRECOGNITION */
._soundActivity_gekck_429 {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 2rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 16px;
  backdrop-filter: var(--card-blur);
  margin-bottom: 2rem;
}

._soundActivity_gekck_429 h3 {
  color: white;
  font-size: 2rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
}

._activityTip_gekck_475 {
  color: rgba(255, 255, 255, 0.95);
  font-size: 1.4rem;
  margin-bottom: 1.5rem;
  line-height: 1.5;
  font-weight: 500;
  max-width: 800px;
}

._soundIndicator_gekck_493 {
  font-size: 3rem;
  margin-bottom: 1rem;
  animation: _bounce_gekck_1 2s infinite;
}

/* Grid de letras/números - PADRÃO LETTERRECOGNITION */
._lettersGrid_gekck_507 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
}

/* Botões de resposta - PADRÃO LETTERRECOGNITION */
._answerButton_gekck_527 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: var(--card-blur);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  position: relative;
  overflow: hidden;
}

._answerButton_gekck_527:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

._answerButton_gekck_527:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

._optionNumber_gekck_585 {
  font-size: 2rem;
  font-weight: bold;
  color: white;
  margin-bottom: 0.5rem;
}

/* Estados dos botões - PADRÃO LETTERRECOGNITION */
._answerButton_gekck_527._correct_gekck_601 {
  background: var(--success-bg) !important;
  border: 2px solid var(--success-border) !important;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.4);
  animation: _successPulse_gekck_1 0.6s ease-out;
}

._answerButton_gekck_527._incorrect_gekck_615 {
  background: var(--error-bg) !important;
  border: 2px solid var(--error-border) !important;
  box-shadow: 0 4px 20px rgba(244, 67, 54, 0.4);
  animation: _errorShake_gekck_1 0.6s ease-out;
}

._answerButton_gekck_527._selected_gekck_629 {
  background: rgba(255, 193, 7, 0.3) !important;
  border: 2px solid #FFC107 !important;
  box-shadow: 0 4px 20px rgba(255, 193, 7, 0.4);
  transform: scale(1.05);
}

/* Controles do jogo - PADRÃO LETTERRECOGNITION */
._gameControls_gekck_645 {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

._controlButton_gekck_661 {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

._controlButton_gekck_661:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* Animações - PADRÃO LETTERRECOGNITION */
@keyframes _successPulse_gekck_1 {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes _errorShake_gekck_1 {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Display dos objetos para contar - ESTÁVEL V4 */
._objectsDisplay_gekck_731 {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin: 2rem 0;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  border: 2px dashed rgba(255, 255, 255, 0.3);
  min-height: 120px;
  position: relative; /* NUNCA absolute para estabilidade */
  overflow: visible; /* Garantir visibilidade */
}

._countingObject_gekck_763 {
  font-size: 3rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: _fadeInScale_gekck_1 0.6s ease-out forwards;
  opacity: 1; /* SEMPRE visível - V4 */
  transform: scale(1); /* Estado estável - V4 */
  transition: transform 0.3s ease;
  position: static; /* NUNCA absolute - V4 */
}

._countingObject_gekck_763:hover {
  transform: scale(1.05); /* Hover mais sutil */
}

@keyframes _fadeInScale_gekck_1 {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* ========================================= */
/* 🎯 ESTILOS V4 - COMPONENTES ESTÁVEIS */
/* ========================================= */

/* Botão TTS no header - PADRÃO LETTERRECOGNITION EXATO */
._headerTtsButton_gekck_177 {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: white;
  z-index: 10;
}

._headerTtsButton_gekck_177:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

._headerTtsButton_gekck_177:active {
  transform: scale(0.95);
}

/* Estados do toggle TTS - PADRÃO LETTERRECOGNITION */
._headerTtsButton_gekck_177._ttsActive_gekck_229 {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
}

/* Indicador ativo no menu */
._activeIndicator_gekck_899 {
  color: #4CAF50;
  font-size: 0.8rem;
  margin-left: 0.5rem;
  animation: _pulse_gekck_1 1.5s infinite;
}

@keyframes _pulse_gekck_1 {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Display de soma simples */
._additionDisplay_gekck_925 {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  font-size: 2.5rem;
  font-weight: bold;
  color: white;
}

._additionNumber_gekck_943 {
  background: rgba(76, 175, 80, 0.3);
  padding: 1rem 1.5rem;
  border-radius: 12px;
  border: 2px solid rgba(76, 175, 80, 0.5);
  min-width: 80px;
  text-align: center;
}

._additionOperator_gekck_961 {
  color: rgba(255, 255, 255, 0.8);
  font-size: 3rem;
}

._additionResult_gekck_971 {
  background: rgba(255, 193, 7, 0.3);
  padding: 1rem 1.5rem;
  border-radius: 12px;
  border: 2px solid rgba(255, 193, 7, 0.5);
  min-width: 80px;
  text-align: center;
}

/* Número alvo para reconhecimento */
._targetNumber_gekck_991 {
  font-size: 4rem;
  font-weight: bold;
  color: white;
  background: rgba(33, 150, 243, 0.3);
  padding: 2rem;
  border-radius: 20px;
  border: 3px solid rgba(33, 150, 243, 0.5);
  text-align: center;
  min-width: 120px;
}

/* Comparação de grupos */
._comparisonGroups_gekck_1017 {
  display: flex;
  gap: 2rem;
  align-items: center;
  justify-content: center;
  width: 100%;
}

._comparisonGroup_gekck_1017 {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
  text-align: center;
}

._comparisonGroup_gekck_1017:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.05);
}

._groupLabel_gekck_1067 {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1rem;
  font-weight: bold;
}

._groupObjects_gekck_1081 {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
}

._groupObject_gekck_1081 {
  font-size: 2rem;
}

._vsIndicator_gekck_1103 {
  font-size: 1.5rem;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 8px;
}

/* Feedback overlay */
._feedbackOverlay_gekck_1123 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

._feedbackContent_gekck_1149 {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
  backdrop-filter: blur(10px);
}

._feedbackMessage_gekck_1165 {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
}

._feedbackOverlay_gekck_1123._success_gekck_1177 ._feedbackContent_gekck_1149 {
  border: 3px solid rgba(76, 175, 80, 0.8);
  background: rgba(76, 175, 80, 0.2);
}

._feedbackOverlay_gekck_1123._error_gekck_1187 ._feedbackContent_gekck_1149 {
  border: 3px solid rgba(244, 67, 54, 0.8);
  background: rgba(244, 67, 54, 0.2);
}

@keyframes _bounce_gekck_1 {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

/* =====================================================
   🎯 ESTILOS PARA RECONHECIMENTO NUMÉRICO MELHORADO V2
   ===================================================== */

/* Grid para objetos de contagem */
._countingObjectsGrid_gekck_1219 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
  gap: 1rem;
  max-width: 400px;
  margin: 0 auto;
  padding: 1rem;
}

/* Número por extenso */
._writtenNumberDisplay_gekck_1239 {
  text-align: center;
  padding: 2rem;
}

._writtenNumberText_gekck_1249 {
  font-size: 3rem;
  font-weight: bold;
  color: #FFD700;
  background: rgba(255, 215, 0, 0.2);
  padding: 1rem 2rem;
  border-radius: 15px;
  border: 3px solid rgba(255, 215, 0, 0.5);
  font-style: italic;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Sequência numérica */
._sequenceDisplay_gekck_1275 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  font-size: 3rem;
  font-weight: bold;
}

._sequenceNumber_gekck_1293 {
  background: rgba(76, 175, 80, 0.3);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 15px;
  border: 3px solid rgba(76, 175, 80, 0.5);
  min-width: 80px;
  text-align: center;
}

._sequenceArrow_gekck_1313 {
  color: rgba(255, 255, 255, 0.8);
  font-size: 2rem;
}

._sequencePlaceholder_gekck_1323 {
  background: rgba(255, 193, 7, 0.3);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 15px;
  border: 3px solid rgba(255, 193, 7, 0.5);
  min-width: 80px;
  text-align: center;
}

/* Comparação de números */
._comparisonDisplay_gekck_1345 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  font-size: 3rem;
  font-weight: bold;
}

._comparisonNumber_gekck_1363 {
  background: rgba(33, 150, 243, 0.3);
  color: white;
  padding: 1.5rem;
  border-radius: 50%;
  border: 3px solid rgba(33, 150, 243, 0.5);
  min-width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

._comparisonVs_gekck_1391 {
  color: rgba(255, 255, 255, 0.8);
  font-size: 2rem;
  font-weight: normal;
}

/* Padrões visuais */
._patternDisplay_gekck_1405 {
  text-align: center;
  padding: 2rem;
}

._dotsPattern_gekck_1415 {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  max-width: 300px;
  margin: 0 auto;
}

._dot_gekck_1415 {
  font-size: 3rem;
  color: #FF6B6B;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: _bounce_gekck_1 2s infinite;
}

._dot_gekck_1415:nth-child(even) {
  animation-delay: 0.2s;
}

._dicePattern_gekck_1455 {
  text-align: center;
}

._diceEmoji_gekck_1463 {
  font-size: 6rem;
  filter: drop-shadow(4px 4px 8px rgba(0, 0, 0, 0.3));
  animation: _pulse_gekck_1 2s infinite;
}

._fingersPattern_gekck_1475 {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

._finger_gekck_1475 {
  font-size: 4rem;
  animation: _wave_gekck_1 1s infinite;
  animation-delay: calc(var(--i) * 0.1s);
}

._finger_gekck_1475:nth-child(1) { --i: 0; }
._finger_gekck_1475:nth-child(2) { --i: 1; }
._finger_gekck_1475:nth-child(3) { --i: 2; }
._finger_gekck_1475:nth-child(4) { --i: 3; }
._finger_gekck_1475:nth-child(5) { --i: 4; }

@keyframes _pulse_gekck_1 {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes _wave_gekck_1 {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(10deg); }
  75% { transform: rotate(-10deg); }
}

._gameTitle_gekck_153 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

._activitySubtitle_gekck_1557 {
  font-size: 0.7rem;
  opacity: 0.8;
  margin-top: 0.25rem;
  background: var(--card-background);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: var(--card-border);
}

/* Área da pergunta */
._questionArea_gekck_1579 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  text-align: center;
}

._questionTitle_gekck_1599 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: white;
}

._objectsDisplay_gekck_731 {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
  margin: 2rem 0;
  min-height: 150px;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 2px dashed rgba(255, 255, 255, 0.3);
}

._countingObject_gekck_763 {
  font-size: 3rem;
  transition: all 0.3s ease;
  animation: _objectAppear_gekck_1 0.5s ease-out;
  cursor: default;
  user-select: none;
}

._countingObject_gekck_763:hover {
  transform: scale(1.1);
}

@keyframes _objectAppear_gekck_1 {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-10deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* Opções de resposta */
._answerOptions_gekck_1689 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

._answerButton_gekck_527 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 1.5rem;
  color: white;
  font-size: 2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

._answerButton_gekck_527:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.25);
}

._answerButton_gekck_527._correct_gekck_601 {
  border-color: var(--success-border);
  background: var(--success-bg);
  animation: _correctPulse_gekck_1 0.6s ease-in-out;
}

._answerButton_gekck_527._incorrect_gekck_615 {
  border-color: var(--error-border);
  background: var(--error-bg);
  animation: _incorrectShake_gekck_1 0.6s ease-in-out;
}

._answerButton_gekck_527:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@keyframes _correctPulse_gekck_1 {
  0%, 100% { transform: translateY(-5px) scale(1.05); }
  50% { transform: translateY(-5px) scale(1.2); }
}

@keyframes _incorrectShake_gekck_1 {
  0%, 100% { transform: translateY(-5px) translateX(0); }
  25% { transform: translateY(-5px) translateX(-10px); }
  75% { transform: translateY(-5px) translateX(10px); }
}

/* Controles do jogo */
._gameControls_gekck_645 {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

._controlButton_gekck_661 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 600;
}

._controlButton_gekck_661:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

._nextButton_gekck_1867 {
  background: var(--success-bg);
  border-color: var(--success-border);
}

._nextButton_gekck_1867:hover {
  background: rgba(76, 175, 80, 0.4);
}

/* Feedback */
._feedbackMessage_gekck_1165 {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 1.5rem 2.5rem;
  border-radius: 16px;
  font-size: 1.3rem;
  font-weight: 700;
  z-index: 1000;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: _messageSlide_gekck_1 3s ease-in-out;
}

._feedbackMessage_gekck_1165._success_gekck_1177 {
  background: var(--success-bg);
  color: white;
}

._feedbackMessage_gekck_1165._error_gekck_1187 {
  background: var(--error-bg);
  color: white;
}

@keyframes _messageSlide_gekck_1 {
  0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
  15% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  85% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}

/* Botões TTS */
._headerTtsButton_gekck_177 {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: white;
  z-index: 10;
}

._headerTtsButton_gekck_177:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

._headerTtsButton_gekck_177:active {
  transform: scale(0.95);
}

._ttsActive_gekck_229 {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
}

._ttsInactive_gekck_2019 {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
}

._repeatButton_gekck_2029 {
  background: rgba(74, 144, 226, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 600;
  margin-left: 1rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

._repeatButton_gekck_2029:hover {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.4);
}

._repeatButton_gekck_2029:active {
  transform: scale(0.95);
}

._ttsIndicator_gekck_2081 {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(74, 144, 226, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 6px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  z-index: 5;
  pointer-events: none;
  transition: all 0.2s ease;
}

._answerButton_gekck_527:hover ._ttsIndicator_gekck_2081 {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.1);
}

/* Menu de atividades */
._activityMenu_gekck_329 {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

._activityButton_gekck_345 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

._activityButton_gekck_345:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

._activityButton_gekck_345._active_gekck_383 {
  background: var(--success-bg) !important;
  border: 2px solid var(--success-border) !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

/* Atividade de Som */
._soundActivity_gekck_429 {
  text-align: center;
}

._soundIndicator_gekck_493 {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: _soundPulse_gekck_1 2s ease-in-out infinite;
}

@keyframes _soundPulse_gekck_1 {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.1); opacity: 1; }
}

._soundButton_gekck_2227 {
  background: rgba(74, 144, 226, 0.3);
  border: 2px solid rgba(74, 144, 226, 0.5);
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  margin: 1rem;
}

._soundButton_gekck_2227:hover {
  background: rgba(74, 144, 226, 0.5);
  transform: scale(1.05);
}

/* Atividade de Estimativa */
._estimationDisplay_gekck_2265 {
  position: relative;
}

._estimationObjects_gekck_2273 {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  opacity: 0.9;
}

._estimationObject_gekck_2273 {
  font-size: 1.5rem;
  transform: rotate(var(--rotation));
  transition: opacity 0.3s ease;
}

._estimationTip_gekck_2301 {
  text-align: center;
  margin-top: 1rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  background: rgba(255, 193, 7, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border-left: 4px solid #FFC107;
}

/* Atividade de Sequência */
._sequenceDisplay_gekck_1275 {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-wrap: wrap;
}

._sequenceNumber_gekck_1293 {
  background: var(--card-background);
  padding: 1rem;
  border-radius: 12px;
  min-width: 60px;
  text-align: center;
  color: white;
  font-weight: bold;
  transition: all 0.3s ease;
}

._sequenceNumber_gekck_1293:hover {
  transform: scale(1.05);
}

._sequenceArrow_gekck_1313 {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.5rem;
}

._sequenceMissing_gekck_2383 {
  background: rgba(255, 255, 80, 0.3) !important;
  border: 2px dashed rgba(255, 255, 255, 0.5);
  animation: _missingPulse_gekck_1 2s ease-in-out infinite;
}

@keyframes _missingPulse_gekck_1 {
  0%, 100% { box-shadow: 0 0 0 rgba(255, 255, 80, 0.3); }
  50% { box-shadow: 0 0 20px rgba(255, 255, 80, 0.6); }
}

/* Atividade de Comparação */
._comparisonDisplay_gekck_1345 {
  display: flex;
  gap: 3rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

._comparisonGroup_gekck_1017 {
  text-align: center;
  background: var(--card-background);
  padding: 1.5rem;
  border-radius: 16px;
  transition: all 0.3s ease;
}

._comparisonGroup_gekck_1017:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-5px);
}

._comparisonObjects_gekck_2449 {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  max-width: 150px;
  margin-bottom: 1rem;
}

._comparisonNumber_gekck_1363 {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  background: var(--card-background);
  padding: 0.5rem 1rem;
  border-radius: 8px;
}

/* Atividade de Padrões */
._patternDisplay_gekck_1405 {
  text-align: center;
}

._patternDescription_gekck_2495 {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1rem;
  background: rgba(156, 39, 176, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border-left: 4px solid #9C27B0;
}

._patternSequence_gekck_2515 {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-wrap: wrap;
}

._patternNumber_gekck_2533 {
  background: linear-gradient(135deg, rgba(156, 39, 176, 0.3), rgba(156, 39, 176, 0.1));
  padding: 1rem;
  border-radius: 12px;
  min-width: 60px;
  text-align: center;
  color: white;
  font-weight: bold;
  border: 1px solid rgba(156, 39, 176, 0.4);
  transition: all 0.3s ease;
}

._patternNumber_gekck_2533:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(156, 39, 176, 0.3);
}

/* ========================================= */
/* 🎯 RESPONSIVIDADE V4 - MOBILE FIRST */
/* ========================================= */

/* Tablet e telas médias */
@media (max-width: 768px) {
  ._contagemNumerosGame_gekck_47 {
    padding: 0.5rem;
  }

  ._gameHeader_gekck_125 {
    padding: 0.75rem 2.5rem 0.75rem 0.75rem;
    min-height: 60px;
  }

  ._gameTitle_gekck_153 {
    font-size: 1.4rem;
  }

  ._activitySubtitle_gekck_1557 {
    font-size: 0.65rem;
    padding: 0.2rem 0.5rem;
  }

  ._gameContent_gekck_73 {
    padding: 0.5rem;
  }

  ._questionArea_gekck_1579 {
    padding: 1.5rem;
    border-radius: 16px;
  }

  ._questionTitle_gekck_1599 {
    font-size: 1.2rem;
  }

  ._objectsDisplay_gekck_731 {
    margin: 1rem 0;
    padding: 1rem;
    gap: 0.75rem;
    min-height: 100px;
  }

  ._countingObject_gekck_763 {
    font-size: 2.5rem;
    padding: 0.75rem;
  }

  ._answerOptions_gekck_1689 {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
  }

  ._answerButton_gekck_527 {
    padding: 1rem;
    font-size: 1rem;
    min-height: 60px;
  }

  ._optionNumber_gekck_585 {
    font-size: 1.5rem;
  }

  ._gameStats_gekck_243 {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 0.5rem;
  }

  ._statCard_gekck_257 {
    padding: 0.75rem;
  }

  /* V4 - Componentes específicos */
  ._additionDisplay_gekck_925 {
    gap: 1rem;
    font-size: 2rem;
  }

  ._additionNumber_gekck_943, ._additionResult_gekck_971 {
    padding: 0.75rem 1rem;
    min-width: 60px;
    font-size: 1.8rem;
  }

  ._additionOperator_gekck_961 {
    font-size: 2.5rem;
  }

  ._targetNumber_gekck_991 {
    font-size: 3rem;
    padding: 1.5rem;
    min-width: 100px;
  }

  ._comparisonGroups_gekck_1017 {
    gap: 1rem;
  }

  ._comparisonGroup_gekck_1017 {
    padding: 1rem;
    min-width: 120px;
  }

  ._groupObject_gekck_1081 {
    font-size: 1.5rem;
  }

  ._ttsButton_gekck_2783 {
    padding: 0.4rem 0.8rem;
    font-size: 1rem;
  }
}

/* =====================================================
   🎯 ESTILOS PARA ATIVIDADES ESTRUTURAIS REDESENHADAS V3
   ===================================================== */

/* Estimativa de quantidade */
._estimationHint_gekck_2805 {
  font-size: 0.9rem;
  color: #FFD700;
  font-style: italic;
  margin-top: 0.5rem;
}

/* Composição numérica */
._compositionTarget_gekck_2821 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #4CAF50;
  margin: 1rem 0;
  padding: 1rem;
  background: rgba(76, 175, 80, 0.2);
  border: 2px solid rgba(76, 175, 80, 0.5);
  border-radius: 12px;
  text-align: center;
}

._decompositionOptions_gekck_2845 {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 2rem 0;
}

._decompositionButton_gekck_2859 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1.5rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

._decompositionButton_gekck_2859:hover {
  transform: translateY(-2px);
  background: rgba(156, 39, 176, 0.3);
  border-color: rgba(156, 39, 176, 0.6);
}

/* Comparação de magnitude */
._comparisonGroups_gekck_1017 {
  display: flex;
  gap: 2rem;
  justify-content: center;
  flex-wrap: wrap;
  margin: 2rem 0;
}

._comparisonGroup_gekck_1017 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  min-width: 200px;
  transition: all 0.3s ease;
}

._comparisonGroup_gekck_1017:hover {
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 0.1);
}

._groupLabel_gekck_1067 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #FFD700;
  margin-bottom: 1rem;
}

._groupObjects_gekck_1081 {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
  margin: 1.5rem 0;
  min-height: 100px;
  align-items: center;
}

._groupObject_gekck_1081 {
  font-size: 2rem;
  animation: _fadeInScale_gekck_1 0.5s ease-out;
}

._groupButton_gekck_2987 {
  background: var(--primary-color);
  border: none;
  border-radius: 8px;
  padding: 0.8rem 1.5rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

._groupButton_gekck_2987:hover {
  background: var(--primary-hover);
  transform: scale(1.05);
}

/* Sequências numéricas */
._sequenceDescription_gekck_3021 {
  font-size: 0.9rem;
  color: #81C784;
  font-style: italic;
  margin-top: 0.5rem;
}

._sequenceDisplay_gekck_1275 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

._sequenceNumber_gekck_1293 {
  font-size: 3rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  padding: 1rem;
  background: rgba(33, 150, 243, 0.2);
  border: 2px solid rgba(33, 150, 243, 0.4);
  border-radius: 12px;
  min-width: 80px;
  text-align: center;
  transition: all 0.3s ease;
}

._sequenceNumber_gekck_1293:hover {
  transform: scale(1.05);
  background: rgba(33, 150, 243, 0.3);
}

/* Animações específicas */
@keyframes _fadeInScale_gekck_1 {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsividade para atividades estruturais */
@media (max-width: 768px) {
  ._comparisonGroups_gekck_1017 {
    flex-direction: column;
    align-items: center;
  }

  ._sequenceDisplay_gekck_1275 {
    gap: 0.5rem;
    padding: 1rem;
  }

  ._sequenceNumber_gekck_1293 {
    font-size: 2rem;
    min-width: 60px;
    padding: 0.5rem;
  }

  ._decompositionOptions_gekck_2845 {
    gap: 0.5rem;
  }
}

/* Mobile pequeno - V4 */
@media (max-width: 480px) {
  ._contagemNumerosGame_gekck_47 {
    padding: 0.25rem;
  }

  ._gameHeader_gekck_125 {
    padding: 0.5rem;
    min-height: 50px;
  }

  ._gameTitle_gekck_153 {
    font-size: 1.2rem;
  }

  ._activitySubtitle_gekck_1557 {
    font-size: 0.6rem;
  }

  ._questionArea_gekck_1579 {
    padding: 0.75rem;
  }

  ._questionTitle_gekck_1599 {
    font-size: 1rem;
  }

  ._objectsDisplay_gekck_731 {
    margin: 0.5rem 0;
    padding: 0.75rem;
    gap: 0.5rem;
    min-height: 80px;
  }

  ._countingObject_gekck_763 {
    font-size: 2rem;
    padding: 0.5rem;
  }

  ._answerOptions_gekck_1689 {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  ._answerButton_gekck_527 {
    padding: 0.75rem;
    font-size: 0.9rem;
    min-height: 50px;
  }

  ._optionNumber_gekck_585 {
    font-size: 1.2rem;
  }

  ._gameStats_gekck_243 {
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
    gap: 0.25rem;
  }

  ._statCard_gekck_257 {
    padding: 0.5rem;
  }

  ._statLabel_gekck_315 {
    font-size: 0.7rem;
  }

  ._statValue_gekck_301 {
    font-size: 1.2rem;
  }

  /* V4 - Mobile específico */
  ._additionDisplay_gekck_925 {
    gap: 0.5rem;
    font-size: 1.5rem;
    flex-direction: column;
  }

  ._additionNumber_gekck_943, ._additionResult_gekck_971 {
    padding: 0.5rem;
    min-width: 50px;
    font-size: 1.5rem;
  }

  ._additionOperator_gekck_961 {
    font-size: 2rem;
  }

  ._targetNumber_gekck_991 {
    font-size: 2.5rem;
    padding: 1rem;
    min-width: 80px;
  }

  ._comparisonGroups_gekck_1017 {
    flex-direction: column;
    gap: 1rem;
  }

  ._comparisonGroup_gekck_1017 {
    padding: 0.75rem;
    min-width: 100px;
  }

  ._groupLabel_gekck_1067 {
    font-size: 0.8rem;
  }

  ._groupObject_gekck_1081 {
    font-size: 1.2rem;
  }

  ._vsIndicator_gekck_1103 {
    font-size: 1.2rem;
    padding: 0.25rem 0.5rem;
  }

  ._ttsButton_gekck_2783 {
    padding: 0.3rem 0.6rem;
    font-size: 0.9rem;
  }

  ._feedbackContent_gekck_1149 {
    padding: 1rem;
    margin: 1rem;
  }

  ._feedbackMessage_gekck_1165 {
    font-size: 1.2rem;
  }

  ._questionArea_gekck_1579 {
    padding: 0.75rem;
  }

  ._questionTitle_gekck_1599 {
    font-size: 1rem;
  }

  ._answerGrid_gekck_3445 {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  ._answerButton_gekck_527 {
    padding: 1rem;
    font-size: 1.2rem;
  }
  
  ._sequenceDisplay_gekck_1275,
  ._patternSequence_gekck_2515 {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  ._sequenceArrow_gekck_1313 {
    transform: rotate(90deg);
  }
}

/* Suporte a alto contraste */
[data-theme="high-contrast"] {
  --card-background: #000;
  --card-border: 1px solid #fff;
  --success-bg: #28a745;
  --success-border: #1e7e34;
  --error-bg: #dc3545;
  --error-border: #bd2130;
}

/* Suporte a movimento reduzido */
._reduced-motion_gekck_3509 {
  ._answerButton_gekck_527, ._controlButton_gekck_661, ._countingObject_gekck_763, ._feedbackMessage_gekck_1165, ._soundIndicator_gekck_493, ._sequenceMissing_gekck_2383 {
    animation: none !important;
    transition: none !important;
  }
}

/* 🎯 ESTILOS PARA COMPARAÇÃO DE QUANTIDADE V2 */

/* Indicador de tipo de desafio */
._challengeTypeIndicator_gekck_3529 {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

._challengeBadge_gekck_3541 {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  color: white;
  font-weight: bold;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
}

/* Grupos de comparação aprimorados */
._comparisonGroups_gekck_1017 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

._comparisonGroup_gekck_1017 {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 1.5rem;
  min-width: 200px;
  max-width: 300px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
}

._comparisonGroup_gekck_1017:hover {
  transform: translateY(-5px) scale(1.02);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Cabeçalho dos grupos */
._groupHeader_gekck_3621 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1rem;
  gap: 0.5rem;
}

._groupLabel_gekck_1067 {
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  text-align: center;
  padding: 0.25rem 0.75rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 15px;
}

._groupCategory_gekck_3657 {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  font-style: italic;
}

._groupCount_gekck_3671 {
  font-size: 1.5rem;
  font-weight: bold;
  color: #FFD700;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #FFD700;
}

/* Objetos dos grupos */
._groupObjects_gekck_1081 {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
  min-height: 100px;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 1px dashed rgba(255, 255, 255, 0.2);
}

._groupObject_gekck_1081 {
  font-size: 2rem;
  padding: 0.25rem;
  border-radius: 10px;
  transition: all 0.3s ease;
  cursor: pointer;
}

._groupObject_gekck_1081:hover {
  transform: scale(1.2);
  filter: brightness(1.2);
}

/* Indicador VS melhorado */
._vsIndicator_gekck_1103 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #FF6B6B, #4ECDC4);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  color: white;
  font-weight: bold;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

._vsText_gekck_3783 {
  font-size: 1.2rem;
  font-weight: bold;
}

._vsSubtext_gekck_3793 {
  font-size: 0.7rem;
  opacity: 0.8;
}

/* Botões de igualdade */
._equalityButtons_gekck_3805 {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

._equalityButton_gekck_3805 {
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  color: white;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-width: 180px;
}

._equalButton_gekck_3849 {
  border-color: #4CAF50;
  background: rgba(76, 175, 80, 0.2);
}

._equalButton_gekck_3849:hover {
  background: rgba(76, 175, 80, 0.4);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
}

._differentButton_gekck_3871 {
  border-color: #F44336;
  background: rgba(244, 67, 54, 0.2);
}

._differentButton_gekck_3871:hover {
  background: rgba(244, 67, 54, 0.4);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(244, 67, 54, 0.3);
}

/* Entrada numérica para diferença */
._differenceInput_gekck_3895 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 2rem;
  gap: 1rem;
}

._differencePrompt_gekck_3911 {
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  text-align: center;
  margin: 0;
}

._numberButtons_gekck_3927 {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
}

._numberButton_gekck_3927 {
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

._numberButton_gekck_3927:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Responsividade para comparação */
@media (max-width: 768px) {
  ._comparisonGroups_gekck_1017 {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  ._comparisonGroup_gekck_1017 {
    min-width: 280px;
    max-width: 350px;
  }
  
  ._vsIndicator_gekck_1103 {
    width: 60px;
    height: 60px;
    order: 2;
  }
  
  ._groupLeft_gekck_4017 {
    order: 1;
  }
  
  ._groupRight_gekck_4025 {
    order: 3;
  }
  
  ._equalityButtons_gekck_3805 {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }
  
  ._equalityButton_gekck_3805 {
    min-width: 200px;
  }
  
  ._numberButtons_gekck_3927 {
    max-width: 280px;
  }
}