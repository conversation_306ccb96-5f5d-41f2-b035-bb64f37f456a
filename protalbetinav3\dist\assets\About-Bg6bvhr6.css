/**
 * @file About.module.css
 * @description Estilos da página About do <PERSON> Betina
 * @version 3.0.0
 */

/* Container principal */
._container_z1ypv_15 {
  padding: 2rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
  background: #fafafa;
  min-height: 100vh;
}

/* Botão Voltar */
._backButton_z1ypv_33 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 2rem;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

._backButton_z1ypv_33:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

._backButton_z1ypv_33:active {
  transform: translateY(0);
}

/* Hero Banner */
._heroBanner_z1ypv_81 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 3rem 2rem;
  text-align: center;
  margin-bottom: 3rem;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

._heroContent_z1ypv_99 {
  max-width: 800px;
  margin: 0 auto;
}

._heroTitle_z1ypv_109 {
  font-size: 3.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

._heroSubtitle_z1ypv_125 {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
  line-height: 1.6;
}

._badgeContainer_z1ypv_139 {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
}

._techBadge_z1ypv_153 {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

._badgePrimary_z1ypv_177 {
  background: rgba(255, 255, 255, 0.2);
}

._badgeGreen_z1ypv_185 {
  background: rgba(76, 175, 80, 0.3);
}

._badgePurple_z1ypv_193 {
  background: rgba(156, 39, 176, 0.3);
}

/* Seções */
._section_z1ypv_203 {
  background: white;
  border-radius: 15px;
  padding: 2.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e0e6ed;
}

._sectionTitle_z1ypv_221 {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

._sectionTitle_z1ypv_221 span {
  font-size: 2.2rem;
}

._sectionContent_z1ypv_249 {
  color: #34495e;
  line-height: 1.7;
}

._sectionContent_z1ypv_249 p {
  margin-bottom: 1.2rem;
  font-size: 1.1rem;
}

/* Lista de benefícios */
._benefitsList_z1ypv_271 {
  list-style: none;
  padding: 0;
  margin: 1.5rem 0;
}

._benefitItem_z1ypv_283 {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #667eea;
}

._benefitEmoji_z1ypv_305 {
  font-size: 1.5rem;
  flex-shrink: 0;
}

._benefitText_z1ypv_315 {
  flex: 1;
  font-size: 1rem;
}

/* Highlight Box */
._highlightBox_z1ypv_327 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 15px;
  margin: 2rem 0;
  text-align: center;
}

._highlightTitle_z1ypv_345 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

._highlightText_z1ypv_357 {
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0;
}

/* Grid de recursos de IA */
._aiFeatureGrid_z1ypv_371 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

._aiFeatureCard_z1ypv_385 {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

._aiFeatureCard_z1ypv_385:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

._aiFeatureIcon_z1ypv_415 {
  font-size: 3rem;
  margin-bottom: 1rem;
}

._aiFeatureTitle_z1ypv_425 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.8rem;
}

._aiFeatureDescription_z1ypv_439 {
  color: #6c757d;
  line-height: 1.5;
  margin: 0;
}

/* Categorias de tecnologia */
._techCategory_z1ypv_453 {
  margin-bottom: 2rem;
}

._techCategoryTitle_z1ypv_461 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

._techBadges_z1ypv_481 {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
}

._techBadges_z1ypv_481 ._techBadge_z1ypv_153 {
  background: #e9ecef;
  color: #495057;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}

._techBadges_z1ypv_481 ._techBadge_z1ypv_153:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

/* Responsividade */
@media (max-width: 768px) {
  ._container_z1ypv_15 {
    padding: 1rem 0.5rem;
  }
  
  ._heroBanner_z1ypv_81 {
    padding: 2rem 1rem;
    margin-bottom: 2rem;
  }
  
  ._heroTitle_z1ypv_109 {
    font-size: 2.5rem;
  }
  
  ._heroSubtitle_z1ypv_125 {
    font-size: 1.1rem;
  }
  
  ._section_z1ypv_203 {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }
  
  ._sectionTitle_z1ypv_221 {
    font-size: 1.6rem;
  }
  
  ._aiFeatureGrid_z1ypv_371 {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  ._benefitItem_z1ypv_283 {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 0.5rem;
  }
  
  ._badgeContainer_z1ypv_139 {
    flex-direction: column;
    align-items: center;
  }
  
  ._techBadges_z1ypv_481 {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  ._heroTitle_z1ypv_109 {
    font-size: 2rem;
  }
  
  ._heroSubtitle_z1ypv_125 {
    font-size: 1rem;
  }
  
  ._section_z1ypv_203 {
    padding: 1rem;
  }
  
  ._sectionTitle_z1ypv_221 {
    font-size: 1.4rem;
  }
  
  ._techBadge_z1ypv_153 {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}
