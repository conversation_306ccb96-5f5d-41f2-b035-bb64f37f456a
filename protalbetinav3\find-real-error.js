#!/usr/bin/env node

/**
 * Script específico para encontrar o erro real "Cannot access 'e' before initialization"
 * Foca em problemas reais de JavaScript, não em comentários
 */

import fs from 'fs';
import path from 'path';

// Diretórios para verificar
const dirsToCheck = [
  './src/games',
  './src/utils',
  './src/components'
];

// Padrões específicos que podem causar o erro
const specificPatterns = [
  // Variável 'e' sendo declarada e usada na mesma linha
  {
    name: 'Variable e self-reference',
    regex: /(?:const|let|var)\s+e\s*=.*\be\b/g,
    description: 'Variável "e" sendo usada na própria inicialização'
  },
  // Destructuring com 'e'
  {
    name: 'Destructuring with e',
    regex: /(?:const|let|var)\s*\{[^}]*\be\b[^}]*\}\s*=.*\be\b/g,
    description: 'Destructuring usando "e" antes da declaração'
  },
  // Arrow functions com 'e' como parâmetro
  {
    name: 'Arrow function parameter e',
    regex: /\(\s*e\s*\)\s*=>/g,
    description: 'Arrow function com parâmetro "e"'
  },
  // Loops com 'e'
  {
    name: 'Loop variable e',
    regex: /for\s*\(\s*(?:const|let|var)?\s*e\s+/g,
    description: 'Loop usando variável "e"'
  },
  // Catch blocks com 'e'
  {
    name: 'Catch block with e',
    regex: /catch\s*\(\s*e\s*\)/g,
    description: 'Catch block com parâmetro "e"'
  }
];

// Função para encontrar arquivos JS/JSX recursivamente
function findJsFiles(dir) {
  let results = [];
  
  if (!fs.existsSync(dir)) {
    return results;
  }
  
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      results = results.concat(findJsFiles(fullPath));
    } else if (file.endsWith('.js') || file.endsWith('.jsx')) {
      results.push(fullPath);
    }
  }
  
  return results;
}

// Função para analisar um arquivo específico
function analyzeFileForRealErrors(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const issues = [];
    
    // Verificar cada padrão específico
    specificPatterns.forEach(pattern => {
      const matches = [...content.matchAll(pattern.regex)];
      
      matches.forEach(match => {
        const lineNumber = content.substring(0, match.index).split('\n').length;
        const lineContent = lines[lineNumber - 1]?.trim();
        
        // Filtrar comentários
        if (!lineContent.startsWith('//') && !lineContent.startsWith('*') && !lineContent.startsWith('/*')) {
          issues.push({
            pattern: pattern.name,
            description: pattern.description,
            line: lineNumber,
            content: lineContent,
            match: match[0],
            context: {
              before: lines[lineNumber - 2]?.trim() || '',
              after: lines[lineNumber]?.trim() || ''
            }
          });
        }
      });
    });
    
    // Verificar problemas específicos de hoisting com 'e'
    const hoistingPattern = /(?:const|let)\s+e\s*=.*\be\b/g;
    const hoistingMatches = [...content.matchAll(hoistingPattern)];
    
    hoistingMatches.forEach(match => {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      const lineContent = lines[lineNumber - 1]?.trim();
      
      if (!lineContent.startsWith('//') && !lineContent.startsWith('*')) {
        issues.push({
          pattern: 'Hoisting problem with e',
          description: 'Possível problema de hoisting com variável "e"',
          line: lineNumber,
          content: lineContent,
          match: match[0],
          severity: 'HIGH',
          context: {
            before: lines[lineNumber - 2]?.trim() || '',
            after: lines[lineNumber]?.trim() || ''
          }
        });
      }
    });
    
    return {
      file: filePath,
      issues: issues,
      hasProblems: issues.length > 0
    };
    
  } catch (error) {
    return {
      file: filePath,
      error: error.message,
      hasProblems: true
    };
  }
}

// Função principal
async function main() {
  console.log('🔍 Procurando por erro REAL "Cannot access \'e\' before initialization"...\n');
  
  let allFiles = [];
  for (const dir of dirsToCheck) {
    const files = findJsFiles(dir);
    allFiles = allFiles.concat(files);
  }
  
  console.log(`📄 Analisando ${allFiles.length} arquivos...\n`);
  
  const problematicFiles = [];
  
  for (const file of allFiles) {
    const result = analyzeFileForRealErrors(file);
    
    if (result.hasProblems) {
      problematicFiles.push(result);
    }
  }
  
  // Relatório focado
  console.log('🎯 PROBLEMAS REAIS ENCONTRADOS:\n');
  
  if (problematicFiles.length === 0) {
    console.log('✅ Nenhum problema real encontrado com variável "e"!');
    console.log('💡 O erro pode estar em:');
    console.log('   - Dependências circulares');
    console.log('   - Problemas de build do Vite');
    console.log('   - Arquivos minificados');
  } else {
    console.log(`❌ Encontrados ${problematicFiles.length} arquivos com problemas REAIS:\n`);
    
    problematicFiles.forEach(file => {
      console.log(`📄 ${file.file}:`);
      
      if (file.error) {
        console.log(`   ❌ Erro: ${file.error}`);
      } else {
        file.issues.forEach(issue => {
          console.log(`   🔸 Linha ${issue.line}: ${issue.pattern}`);
          console.log(`      ${issue.description}`);
          console.log(`      Código: ${issue.content}`);
          if (issue.severity === 'HIGH') {
            console.log(`      ⚠️ ALTA PRIORIDADE - POSSÍVEL CAUSA DO ERRO`);
          }
          console.log(`      Contexto:`);
          console.log(`        Antes: ${issue.context.before}`);
          console.log(`        Depois: ${issue.context.after}`);
          console.log('');
        });
      }
      console.log('');
    });
  }
  
  // Sugestões específicas
  console.log('💡 PRÓXIMOS PASSOS:');
  console.log('1. Verificar se há dependências circulares entre módulos');
  console.log('2. Verificar se o erro está em arquivos minificados do build');
  console.log('3. Verificar se há problemas no Vite config');
  console.log('4. Verificar logs do browser para stack trace completo');
}

main().catch(console.error);
