{"version": 3, "file": "About-DandRy7e.js", "sources": ["../../src/components/pages/About/About.jsx"], "sourcesContent": ["/**\r\n * @file About.jsx\r\n * @description Página sobre o Portal Betina\r\n * @version 3.0.0\r\n */\r\n\r\nimport React from 'react'\r\nimport styles from './About.module.css'\r\n\r\nfunction About({ onBackToHome }) {\r\n  return (\r\n    <div className={styles.container}>\r\n      {/* Hero Banner */}\r\n      <div className={styles.heroBanner}>\r\n        <div className={styles.heroContent}>\r\n          <h1 className={styles.heroTitle}>Portal Betina</h1>\r\n          <p className={styles.heroSubtitle}>\r\n            Transformando vidas através de atividades neuropedagógicas potencializadas por inteligência artificial\r\n          </p>\r\n          <div className={styles.badgeContainer}>\r\n            <span className={`${styles.techBadge} ${styles.badgePrimary}`}>\r\n              🧠 Desenvolvimento Cognitivo\r\n            </span>\r\n            <span className={`${styles.techBadge} ${styles.badgeGreen}`}>\r\n              🤖 Potencializado por IA\r\n            </span>\r\n            <span className={`${styles.techBadge} ${styles.badgePurple}`}>\r\n              ♿ 100% Acessível\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Seção: O que são Atividades Neuropedagógicas */}\r\n      <section className={styles.section}>\r\n        <h2 className={styles.sectionTitle}>\r\n          <span>🧠</span>\r\n          O que são Atividades Neuropedagógicas?\r\n        </h2>\r\n        <div className={styles.sectionContent}>\r\n          <p>\r\n            <strong>Atividades neuropedagógicas</strong> são intervenções estruturadas que estimulam o \r\n            desenvolvimento cognitivo, emocional e social, especialmente para crianças com autismo, \r\n            TDAH ou outras necessidades específicas.\r\n          </p>\r\n          <p>\r\n            Elas combinam princípios da <strong>neurociência</strong>, <strong>psicologia</strong> e \r\n            <strong>pedagogia</strong> para promover habilidades essenciais como:\r\n          </p>\r\n          \r\n          <ul className={styles.benefitsList}>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>🎯</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Atenção e Concentração:</strong> Melhorar o foco e a capacidade de manter a atenção\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>🧠</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Memória:</strong> Fortalecer a memória de trabalho e de longo prazo\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>🤔</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Raciocínio Lógico:</strong> Desenvolver habilidades de resolução de problemas\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>✋</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Coordenação Motora:</strong> Aprimorar habilidades motoras finas e grossas\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>😊</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Regulação Emocional:</strong> Aprender a identificar e gerenciar emoções\r\n              </span>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Seção: IA */}\r\n      <section className={styles.section}>\r\n        <h2 className={styles.sectionTitle}>\r\n          <span>🤖</span>\r\n          Como a Inteligência Artificial Potencializa o Aprendizado\r\n        </h2>\r\n        <div className={styles.sectionContent}>\r\n          <div className={styles.highlightBox}>          <h3 className={styles.highlightTitle}>\r\n            🚀 Portal Betina V3 - Tecnologia Avançada para Desenvolvimento\r\n          </h3>\r\n          <p className={styles.highlightText}>\r\n            Nossa plataforma integra inteligência artificial com metodologias terapêuticas \r\n            comprovadas para oferecer uma experiência personalizada e eficaz para cada criança.\r\n          </p>\r\n          </div>\r\n\r\n          <div className={styles.aiFeatureGrid}>\r\n            <div className={styles.aiFeatureCard}>\r\n              <div className={styles.aiFeatureIcon}>🎯</div>\r\n              <h4 className={styles.aiFeatureTitle}>Personalização Inteligente</h4>\r\n              <p className={styles.aiFeatureDescription}>\r\n                A IA adapta a dificuldade e o ritmo das atividades baseado no desempenho individual da criança\r\n              </p>\r\n            </div>\r\n\r\n            <div className={styles.aiFeatureCard}>\r\n              <div className={styles.aiFeatureIcon}>📊</div>\r\n              <h4 className={styles.aiFeatureTitle}>Análise de Progresso</h4>\r\n              <p className={styles.aiFeatureDescription}>\r\n                Algoritmos analisam padrões de aprendizado e fornecem insights sobre o desenvolvimento cognitivo\r\n              </p>\r\n            </div>\r\n\r\n            <div className={styles.aiFeatureCard}>\r\n              <div className={styles.aiFeatureIcon}>🎮</div>\r\n              <h4 className={styles.aiFeatureTitle}>Engajamento Otimizado</h4>\r\n              <p className={styles.aiFeatureDescription}>\r\n                IA determina os melhores momentos e tipos de feedback para manter a motivação e interesse\r\n              </p>\r\n            </div>\r\n\r\n            <div className={styles.aiFeatureCard}>\r\n              <div className={styles.aiFeatureIcon}>🔄</div>\r\n              <h4 className={styles.aiFeatureTitle}>Adaptação Contínua</h4>\r\n              <p className={styles.aiFeatureDescription}>\r\n                O sistema aprende continuamente com as interações, melhorando constantemente a experiência\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Seção: Tecnologias */}\r\n      <section className={styles.section}>\r\n        <h2 className={styles.sectionTitle}>\r\n          <span>⚙️</span>\r\n          Tecnologias e Metodologias Aplicadas\r\n        </h2>\r\n        <div className={styles.sectionContent}>\r\n          <p>\r\n            O Portal Betina utiliza tecnologias modernas e metodologias baseadas em evidências científicas:\r\n          </p>\r\n          \r\n          <div className={styles.techCategory}>\r\n            <h4 className={styles.techCategoryTitle}>🧬 Base Científica</h4>\r\n            <div className={styles.techBadges}>\r\n              <span className={styles.techBadge}>Neurociência Cognitiva</span>\r\n              <span className={styles.techBadge}>Psicologia do Desenvolvimento</span>\r\n              <span className={styles.techBadge}>Pedagogia Inclusiva</span>\r\n              <span className={styles.techBadge}>Terapia ABA</span>\r\n              <span className={styles.techBadge}>Neuroplasticidade</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div className={styles.techCategory}>\r\n            <h4 className={styles.techCategoryTitle}>💻 Tecnologia</h4>\r\n            <div className={styles.techBadges}>\r\n              <span className={styles.techBadge}>React + IA</span>\r\n              <span className={styles.techBadge}>Machine Learning</span>\r\n              <span className={styles.techBadge}>Design Responsivo</span>\r\n              <span className={styles.techBadge}>Acessibilidade Web</span>\r\n              <span className={styles.techBadge}>Progressive Web App</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div className={styles.techCategory}>\r\n            <h4 className={styles.techCategoryTitle}>🌈 Acessibilidade</h4>\r\n            <div className={styles.techBadges}>\r\n              <span className={styles.techBadge}>Screen Reader</span>\r\n              <span className={styles.techBadge}>Alto Contraste</span>\r\n              <span className={styles.techBadge}>Navegação por Teclado</span>\r\n              <span className={styles.techBadge}>Feedback Háptico</span>\r\n              <span className={styles.techBadge}>WCAG 2.1 AA</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Seção: Para Famílias */}\r\n      <section className={styles.section}>\r\n        <h2 className={styles.sectionTitle}>\r\n          <span>👨‍👩‍👧‍👦</span>\r\n          Para Pais, Terapeutas e Educadores\r\n        </h2>\r\n        <div className={styles.sectionContent}>\r\n          <p>\r\n            O Portal Betina foi desenvolvido para ser uma ferramenta colaborativa entre famílias e profissionais:\r\n          </p>\r\n          \r\n          <ul className={styles.benefitsList}>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>👩‍⚕️</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Para Terapeutas:</strong> Ferramentas complementares para sessões presenciais e atividades para casa\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>👨‍🏫</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Para Educadores:</strong> Recursos para inclusão escolar e desenvolvimento de habilidades específicas\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>👨‍👩‍👧</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Para Famílias:</strong> Atividades estruturadas para momentos de qualidade e desenvolvimento em casa\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>🤝</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Colaboração:</strong> Dados e progresso compartilhados entre todos os envolvidos no cuidado da criança\r\n              </span>\r\n            </li>\r\n          </ul>\r\n\r\n          <div className={styles.highlightBox}>\r\n            <p className={styles.highlightText}>\r\n              💝 <strong>100% Gratuito e Sempre Será</strong><br/>\r\n              Acreditamos que toda criança merece acesso a ferramentas de qualidade para seu desenvolvimento, \r\n              independentemente da condição socioeconômica da família.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default About\r\n"], "names": ["About", "onBackToHome", "styles", "container", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON><PERSON><PERSON>", "hero<PERSON><PERSON>nt", "<PERSON><PERSON><PERSON><PERSON>", "heroSubtitle", "badgeContainer", "techBadge", "badgePrimary", "badgeGreen", "<PERSON><PERSON><PERSON><PERSON>", "section", "sectionTitle", "sectionContent", "benefitsList", "benefitItem", "benefitEmoji", "benefitText", "highlightBox", "highlightTitle", "highlightText", "aiFeatureGrid", "aiFeatureCard", "aiFeatureIcon", "aiFeatureTitle", "aiFeatureDescription", "techCategory", "techCategoryTitle", "techBadges"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,SAASA,MAAM;AAAA,EAAEC;AAAa,GAAG;AAC/B,6CACG,OAAI,EAAA,WAAWC,OAAOC,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAC,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAE9B,OAAI,EAAA,WAAWL,OAAOM,YAAW,QAAA,MAAA,UAAA;AAAA,IAAAJ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,OAAI,EAAA,WAAWL,OAAOO,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAL,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAChC,MAAG,EAAA,WAAWL,OAAOQ,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAN,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,eAAa,GAC7C,sBAAA,cAAA,KAAA,EAAE,WAAWL,OAAOS,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAP,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,wGAEnC,GACC,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOU,gBAAe,QAAA,MAAA,UAAA;AAAA,IAAAR,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EACpC,GAAA,sBAAA,cAAC,QAAK,EAAA,WAAW,GAAGL,OAAOW,SAAS,IAAIX,OAAOY,YAAY,IAAG,QAAA,MAAA,UAAA;AAAA,IAAAV,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAC,GAAA,8BAE/D,GACA,sBAAA,cAAC,UAAK,WAAW,GAAGL,OAAOW,SAAS,IAAIX,OAAOa,UAAU,IAAG,QAAA,MAAA,UAAA;AAAA,IAAAX,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAC,GAAA,0BAE7D,GACA,sBAAA,cAAC,UAAK,WAAW,GAAGL,OAAOW,SAAS,IAAIX,OAAOc,WAAW,IAAG,QAAA,MAAA,UAAA;AAAA,IAAAZ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,kBAE9D,CACF,CACF,CACF,GAGC,sBAAA,cAAA,WAAA,EAAQ,WAAWL,OAAOe,SAAQ,QAAA,MAAA,UAAA;AAAA,IAAAb,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAChC,MAAG,EAAA,WAAWL,OAAOgB,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAd,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EACjC,GAAA,sBAAA,cAAC,QAAI,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,IAAE,GAAO,wCAEjB,GACA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAOiB,gBAAe,QAAA,MAAA,UAAA;AAAA,IAAAf,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EACpC,GAAA,sBAAA,cAAC,KAAC,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EACA,GAAA,sBAAA,cAAC,UAAM,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,6BAA2B,GAAS,iLAG9C,GACC,sBAAA,cAAA,KAAA,EAAC,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAC,gCAC2B,sBAAA,cAAC,UAAM,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAC,cAAY,GAAS,0CAAG,UAAM,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAC,YAAU,GAAS,0CACrF,UAAM,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,WAAS,GAAS,6CAC5B,GAEA,sBAAA,cAAC,MAAG,EAAA,WAAWL,OAAOkB,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAhB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAChC,MAAG,EAAA,WAAWL,OAAOmB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,QAAK,EAAA,WAAWL,OAAOoB,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAlB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,IAAE,GACvC,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOqB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAnB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAClC,GAAA,sBAAA,cAAC,UAAM,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,yBAAuB,GAAS,qDAC1C,CACF,GACC,sBAAA,cAAA,MAAA,EAAG,WAAWL,OAAOmB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,QAAK,EAAA,WAAWL,OAAOoB,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAlB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,IAAE,GACvC,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOqB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAnB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAClC,GAAA,sBAAA,cAAC,UAAM,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,UAAQ,GAAS,oDAC3B,CACF,GACC,sBAAA,cAAA,MAAA,EAAG,WAAWL,OAAOmB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,QAAK,EAAA,WAAWL,OAAOoB,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAlB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,IAAE,GACvC,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOqB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAnB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAClC,GAAA,sBAAA,cAAC,UAAM,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,oBAAkB,GAAS,oDACrC,CACF,GACC,sBAAA,cAAA,MAAA,EAAG,WAAWL,OAAOmB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,QAAK,EAAA,WAAWL,OAAOoB,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAlB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,GAAC,GACtC,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOqB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAnB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAClC,GAAA,sBAAA,cAAC,UAAM,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,qBAAmB,GAAS,gDACtC,CACF,GACC,sBAAA,cAAA,MAAA,EAAG,WAAWL,OAAOmB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,QAAK,EAAA,WAAWL,OAAOoB,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAlB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,IAAE,GACvC,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOqB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAnB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAClC,GAAA,sBAAA,cAAC,UAAM,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAC,sBAAoB,GAAS,6CACvC,CACF,CACF,CACF,CACF,GAGA,sBAAA,cAAC,aAAQ,WAAWL,OAAOe,SAAQ,QAAA,MAAA,UAAA;AAAA,IAAAb,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAChC,MAAG,EAAA,WAAWL,OAAOgB,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAd,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EACjC,GAAA,sBAAA,cAAC,QAAI,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,IAAE,GAAO,2DAEjB,GACA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAOiB,gBAAe,QAAA,MAAA,UAAA;AAAA,IAAAf,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACnC,OAAI,EAAA,WAAWL,OAAOsB,cAAa,QAAA,MAAA,UAAA;AAAA,IAAApB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,cAAW,sBAAA,cAAA,MAAA,EAAG,WAAWL,OAAOuB,gBAAe,QAAA,MAAA,UAAA;AAAA,IAAArB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,gEAErF,GACC,sBAAA,cAAA,KAAA,EAAE,WAAWL,OAAOwB,eAAc,QAAA,MAAA,UAAA;AAAA,IAAAtB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,oKAGpC,CACA,GAEA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAOyB,eAAc,QAAA,MAAA,UAAA;AAAA,IAAAvB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAClC,OAAI,EAAA,WAAWL,OAAO0B,eAAc,QAAA,MAAA,UAAA;AAAA,IAAAxB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAClC,OAAI,EAAA,WAAWL,OAAO2B,eAAc,QAAA,MAAA,UAAA;AAAA,IAAAzB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,IAAE,GACvC,sBAAA,cAAA,MAAA,EAAG,WAAWL,OAAO4B,gBAAe,QAAA,MAAA,UAAA;AAAA,IAAA1B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,4BAA0B,GAC/D,sBAAA,cAAA,KAAA,EAAE,WAAWL,OAAO6B,sBAAqB,QAAA,MAAA,UAAA;AAAA,IAAA3B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,gGAE3C,CACF,GAEA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAO0B,eAAc,QAAA,MAAA,UAAA;AAAA,IAAAxB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAClC,OAAI,EAAA,WAAWL,OAAO2B,eAAc,QAAA,MAAA,UAAA;AAAA,IAAAzB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,IAAE,GACvC,sBAAA,cAAA,MAAA,EAAG,WAAWL,OAAO4B,gBAAe,QAAA,MAAA,UAAA;AAAA,IAAA1B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,sBAAoB,GACzD,sBAAA,cAAA,KAAA,EAAE,WAAWL,OAAO6B,sBAAqB,QAAA,MAAA,UAAA;AAAA,IAAA3B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,kGAE3C,CACF,GAEA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAO0B,eAAc,QAAA,MAAA,UAAA;AAAA,IAAAxB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAClC,OAAI,EAAA,WAAWL,OAAO2B,eAAc,QAAA,MAAA,UAAA;AAAA,IAAAzB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,IAAE,GACvC,sBAAA,cAAA,MAAA,EAAG,WAAWL,OAAO4B,gBAAe,QAAA,MAAA,UAAA;AAAA,IAAA1B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,uBAAqB,GAC1D,sBAAA,cAAA,KAAA,EAAE,WAAWL,OAAO6B,sBAAqB,QAAA,MAAA,UAAA;AAAA,IAAA3B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,2FAE3C,CACF,GAEA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAO0B,eAAc,QAAA,MAAA,UAAA;AAAA,IAAAxB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAClC,OAAI,EAAA,WAAWL,OAAO2B,eAAc,QAAA,MAAA,UAAA;AAAA,IAAAzB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,IAAE,GACvC,sBAAA,cAAA,MAAA,EAAG,WAAWL,OAAO4B,gBAAe,QAAA,MAAA,UAAA;AAAA,IAAA1B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,oBAAkB,GACvD,sBAAA,cAAA,KAAA,EAAE,WAAWL,OAAO6B,sBAAqB,QAAA,MAAA,UAAA;AAAA,IAAA3B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,4FAE3C,CACF,CACF,CACF,CACF,GAGC,sBAAA,cAAA,WAAA,EAAQ,WAAWL,OAAOe,SAAQ,QAAA,MAAA,UAAA;AAAA,IAAAb,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAChC,MAAG,EAAA,WAAWL,OAAOgB,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAd,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EACjC,GAAA,sBAAA,cAAC,QAAI,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,IAAE,GAAO,sCAEjB,GACA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAOiB,gBAAe,QAAA,MAAA,UAAA;AAAA,IAAAf,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EACpC,GAAA,sBAAA,cAAC,KAAC,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,iGAEH,GAEC,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAO8B,cAAa,QAAA,MAAA,UAAA;AAAA,IAAA5B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACjC,MAAG,EAAA,WAAWL,OAAO+B,mBAAkB,QAAA,MAAA,UAAA;AAAA,IAAA7B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,oBAAkB,GAC1D,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOgC,YAAW,QAAA,MAAA,UAAA;AAAA,IAAA9B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,QAAK,EAAA,WAAWL,OAAOW,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAT,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,wBAAsB,GACxD,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOW,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAT,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,+BAA6B,GAC/D,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOW,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAT,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,qBAAmB,GACrD,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOW,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAT,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,aAAW,GAC7C,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOW,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAT,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,mBAAiB,CACtD,CACF,GAEA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAO8B,cAAa,QAAA,MAAA,UAAA;AAAA,IAAA5B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACjC,MAAG,EAAA,WAAWL,OAAO+B,mBAAkB,QAAA,MAAA,UAAA;AAAA,IAAA7B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,eAAa,GACrD,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOgC,YAAW,QAAA,MAAA,UAAA;AAAA,IAAA9B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,QAAK,EAAA,WAAWL,OAAOW,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAT,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,YAAU,GAC5C,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOW,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAT,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,kBAAgB,GAClD,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOW,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAT,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,mBAAiB,GACnD,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOW,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAT,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,oBAAkB,GACpD,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOW,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAT,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,qBAAmB,CACxD,CACF,GAEA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAO8B,cAAa,QAAA,MAAA,UAAA;AAAA,IAAA5B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACjC,MAAG,EAAA,WAAWL,OAAO+B,mBAAkB,QAAA,MAAA,UAAA;AAAA,IAAA7B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,mBAAiB,GACzD,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOgC,YAAW,QAAA,MAAA,UAAA;AAAA,IAAA9B,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,QAAK,EAAA,WAAWL,OAAOW,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAT,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,eAAa,GAC/C,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOW,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAT,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,gBAAc,GAChD,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOW,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAT,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,uBAAqB,GACvD,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOW,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAT,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,kBAAgB,GAClD,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOW,WAAU,QAAA,MAAA,UAAA;AAAA,IAAAT,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,aAAW,CAChD,CACF,CACF,CACF,GAGC,sBAAA,cAAA,WAAA,EAAQ,WAAWL,OAAOe,SAAQ,QAAA,MAAA,UAAA;AAAA,IAAAb,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAChC,MAAG,EAAA,WAAWL,OAAOgB,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAd,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EACjC,GAAA,sBAAA,cAAC,QAAI,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,aAAW,GAAO,oCAE1B,GACA,sBAAA,cAAC,OAAI,EAAA,WAAWL,OAAOiB,gBAAe,QAAA,MAAA,UAAA;AAAA,IAAAf,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EACpC,GAAA,sBAAA,cAAC,KAAC,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,uGAEH,GAEC,sBAAA,cAAA,MAAA,EAAG,WAAWL,OAAOkB,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAhB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAChC,MAAG,EAAA,WAAWL,OAAOmB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,QAAK,EAAA,WAAWL,OAAOoB,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAlB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,OAAK,GAC1C,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOqB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAnB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAClC,GAAA,sBAAA,cAAC,UAAM,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,kBAAgB,GAAS,6EACnC,CACF,GACC,sBAAA,cAAA,MAAA,EAAG,WAAWL,OAAOmB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,QAAK,EAAA,WAAWL,OAAOoB,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAlB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,OAAK,GAC1C,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOqB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAnB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAClC,GAAA,sBAAA,cAAC,UAAM,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,kBAAgB,GAAS,8EACnC,CACF,GACC,sBAAA,cAAA,MAAA,EAAG,WAAWL,OAAOmB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,QAAK,EAAA,WAAWL,OAAOoB,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAlB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,UAAQ,GAC7C,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOqB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAnB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAClC,GAAA,sBAAA,cAAC,UAAM,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,gBAAc,GAAS,+EACjC,CACF,GACC,sBAAA,cAAA,MAAA,EAAG,WAAWL,OAAOmB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAjB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,QAAK,EAAA,WAAWL,OAAOoB,cAAa,QAAA,MAAA,UAAA;AAAA,IAAAlB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,IAAE,GACvC,sBAAA,cAAA,QAAA,EAAK,WAAWL,OAAOqB,aAAY,QAAA,MAAA,UAAA;AAAA,IAAAnB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAClC,GAAA,sBAAA,cAAC,UAAM,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAC,GAAA,cAAY,GAAS,mFAC/B,CACF,CACF,GAEC,sBAAA,cAAA,OAAA,EAAI,WAAWL,OAAOsB,cAAa,QAAA,MAAA,UAAA;AAAA,IAAApB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACjC,KAAE,EAAA,WAAWL,OAAOwB,eAAc,QAAA,MAAA,UAAA;AAAA,IAAAtB,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAC,OAC/B,sBAAA,cAAC,UAAM,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAC,6BAA2B,uCAAU,MAAE,EAAA,QAAA,MAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,KAAE,GAAA,0JAGtD,CACF,CACF,CACF,CACF;AAEJ;"}