/**
 * 💾 DATABASE INSTANCE
 * Instância principal do banco de dados para Portal Betina V3
 * Conecta com PostgreSQL via Docker
 */

import DatabaseSingleton from './DatabaseSingleton.js';

/**
 * Instância singleton do banco de dados
 * @class DatabaseInstance
 */
class DatabaseInstance {
  constructor() {
    this.singleton = new DatabaseSingleton();
    this.logger = this.createLogger();
  }

  /**
   * Cria logger estruturado
   */
  createLogger() {
    const isBrowser = typeof window !== 'undefined';
    return {
      info: (...args) => console.info(
        isBrowser ? '%c💾 [DB-INSTANCE]' : '💾 [DB-INSTANCE]',
        isBrowser ? 'color: #2196F3' : '',
        new Date().toISOString(),
        ...args
      ),
      error: (...args) => console.error(
        isBrowser ? '%c🔴 [DB-INSTANCE-ERROR]' : '🔴 [DB-INSTANCE-ERROR]',
        isBrowser ? 'color: #F44336' : '',
        new Date().toISOString(),
        ...args
      ),
      warn: (...args) => console.warn(
        isBrowser ? '%c⚠️ [DB-INSTANCE-WARN]' : '⚠️ [DB-INSTANCE-WARN]',
        isBrowser ? 'color: #FF9800' : '',
        new Date().toISOString(),
        ...args
      )
    };
  }

  /**
   * Obtém instância do DatabaseIntegrator
   * @returns {Promise<Object>} Instância do DatabaseIntegrator
   */
  async getInstance() {
    try {
      this.logger.info('🔄 Obtendo instância do DatabaseIntegrator...');
      
      const dbIntegrator = await this.singleton.getInstance();
      
      if (!dbIntegrator) {
        throw new Error('DatabaseIntegrator não foi inicializado');
      }

      this.logger.info('✅ DatabaseIntegrator obtido com sucesso');
      
      // Retornar com interface compatível
      return {
        // Instância principal
        manager: dbIntegrator,
        
        // Métodos diretos para compatibilidade
        save: dbIntegrator.save?.bind(dbIntegrator),
        find: dbIntegrator.find?.bind(dbIntegrator),
        findOne: dbIntegrator.findOne?.bind(dbIntegrator),
        update: dbIntegrator.update?.bind(dbIntegrator),
        delete: dbIntegrator.delete?.bind(dbIntegrator),
        query: dbIntegrator.query?.bind(dbIntegrator),
        store: dbIntegrator.store?.bind(dbIntegrator),
        
        // Status e conexão
        isConnected: () => {
          if (dbIntegrator.isConnected) {
            return dbIntegrator.isConnected();
          }
          return dbIntegrator.connectionStatus === 'connected';
        },
        
        getStatus: () => {
          if (dbIntegrator.getStatus) {
            return dbIntegrator.getStatus();
          }
          return {
            status: 'active',
            connected: dbIntegrator.connectionStatus === 'connected',
            type: 'DatabaseIntegrator'
          };
        },
        
        // Métodos específicos para métricas
        saveGameMetrics: dbIntegrator.saveGameMetrics?.bind(dbIntegrator),
        saveCompleteSession: dbIntegrator.saveCompleteSession?.bind(dbIntegrator),
        saveSessionReport: dbIntegrator.saveSessionReport?.bind(dbIntegrator),
        
        // Resiliência
        resilience: dbIntegrator.resilience || {
          executeWithRetry: (fn) => fn()
        }
      };
      
    } catch (error) {
      this.logger.error('❌ Erro ao obter instância do DatabaseIntegrator:', error);
      
      // Retornar mock funcional em caso de erro
      this.logger.warn('🔄 Retornando mock funcional como fallback');
      return this.createMockInstance();
    }
  }

  /**
   * Cria instância mock para fallback
   * @returns {Object} Mock do DatabaseIntegrator
   */
  createMockInstance() {
    return {
      mock: true,
      manager: {
        query: async (sql, params) => {
          this.logger.warn('📄 [MOCK] Query executada:', { sql, params });
          return [];
        },
        save: async (data) => {
          this.logger.warn('📄 [MOCK] Dados salvos:', data);
          return { id: Date.now(), ...data };
        },
        store: async (table, data) => {
          this.logger.warn('📄 [MOCK] Dados armazenados em', table, ':', data);
          return { success: true, id: Date.now() };
        }
      },
      
      // Métodos diretos
      save: async (data) => ({ id: Date.now(), ...data }),
      find: async (query) => [],
      findOne: async (query) => null,
      update: async (id, data) => ({ id, ...data }),
      delete: async (id) => true,
      query: async (sql, params) => [],
      store: async (table, data) => ({ success: true, id: Date.now() }),
      
      // Status
      isConnected: () => false,
      getStatus: () => ({
        status: 'mock',
        connected: false,
        type: 'MockDatabase'
      }),
      
      // Métodos específicos
      saveGameMetrics: async (userId, gameId, metrics) => ({ id: Date.now(), userId, gameId, metrics }),
      saveCompleteSession: async (userId, gameId, sessionData) => ({ id: Date.now(), userId, gameId, sessionData }),
      saveSessionReport: async (report) => ({ id: Date.now(), ...report }),
      
      // Resiliência
      resilience: {
        executeWithRetry: (fn) => fn()
      }
    };
  }
}

// Instância singleton
const databaseInstance = new DatabaseInstance();

export default databaseInstance;
