const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/vendor-misc-DTF8jSja.js","assets/vendor-react-Bw1F4Ko6.js","assets/vendor-charts-3SQcuR87.js","assets/services-5spxllES.js","assets/dashboard-DOo2m0Zt.js","assets/context-D6Rxw-Zf.js","assets/hooks-Cl3iVr0l.js","assets/vendor-utils-CjlX8hrF.js","assets/admin-Dz2PlR0z.js","assets/admin-BQPZfmpR.css","assets/utils-Db58P6qE.js","assets/dashboard-C4HFo8oW.css","assets/game-colors-BC462aDt.js","assets/game-association-D8ixNKuX.js","assets/game-association-aRlfWsTT.css","assets/game-colors-WFKpsHgQ.css","assets/game-letters-BnM9Bog3.js","assets/game-letters-CgoS80mu.css","assets/game-memory-B1DjEnWS.js","assets/vendor-motion-CQp_RBQj.js","assets/game-memory-BTGvJ0Wb.css","assets/game-musical-CQfw8ygl.js","assets/game-musical-Srt0Rn1x.css","assets/game-patterns-CqswYgmX.js","assets/game-patterns-C7lF44VH.css","assets/game-puzzle-Bb-eyook.js","assets/game-puzzle-jC3b1JUV.css","assets/game-numbers-acpXfm09.js","assets/game-numbers-CWskqaet.css","assets/game-creative-CnQ0MOtQ.js","assets/game-creative-ByDHG8hJ.css"])))=>i.map(i=>d[i]);
import { _ as __vitePreload } from "./dashboard-DOo2m0Zt.js";
import { p as pg } from "./vendor-misc-DTF8jSja.js";
let pgPool = null;
async function loadPostgreSQL() {
  if (typeof window === "undefined" && typeof process !== "undefined") {
    try {
      const module = await __vitePreload(() => Promise.resolve().then(() => pool$1), true ? void 0 : void 0);
      pgPool = module.default;
      console.log("✅ Pool PostgreSQL carregado com sucesso no DatabaseIntegrator");
      return pgPool;
    } catch (error) {
      console.warn("⚠️ PostgreSQL não disponível:", error.message);
      return null;
    }
  }
  return null;
}
const InputValidator = {
  validateGameMetricsInput: (userId, gameId, metrics) => {
    const errors = [];
    if (!userId) errors.push("userId é obrigatório");
    if (!gameId) errors.push("gameId é obrigatório");
    if (!metrics || typeof metrics !== "object") errors.push("metrics deve ser um objeto");
    return {
      valid: errors.length === 0,
      errors,
      sanitized: {
        userId: String(userId || "").trim(),
        gameId: String(gameId || "").trim(),
        metrics: metrics || {}
      }
    };
  },
  validateInteractionsInput: (sessionId, interactions) => {
    const errors = [];
    if (!sessionId) errors.push("sessionId é obrigatório");
    if (!Array.isArray(interactions)) errors.push("interactions deve ser um array");
    return {
      valid: errors.length === 0,
      errors,
      sanitized: {
        sessionId: String(sessionId || "").trim(),
        interactions: Array.isArray(interactions) ? interactions : []
      }
    };
  }
};
class StructuredLogger {
  constructor() {
    this.component = "DatabaseIntegrator";
  }
  info(message, context = {}) {
    console.info(`ℹ️ [${this.component}] ${message}`, context);
  }
  warn(message, context = {}) {
    console.warn(`⚠️ [${this.component}] ${message}`, context);
  }
  error(message, context = {}) {
    console.error(`❌ [${this.component}] ${message}`, context);
  }
  debug(message, context = {}) {
    console.debug(`🔍 [${this.component}] ${message}`, context);
  }
}
let DatabaseIntegrator$1 = class DatabaseIntegrator {
  /**
   * @constructor
   * @param {Object} config - Configurações do integrador
   */
  constructor(config2 = {}) {
    this.config = {
      resilience: {
        enabled: true,
        monitoringEnabled: true,
        retryAttempts: 3,
        retryDelay: 1e3,
        timeoutDuration: 5e3
      },
      cache: {
        memoryMaxSize: 1e3,
        memoryTTL: 3e5,
        // 5 minutes
        redisEnabled: !!config2.redisUrl,
        redisUrl: config2.redisUrl || null
      },
      metrics: {
        enabled: true,
        detailedLogging: true,
        persistInterval: 6e4
        // 1 minute
      },
      ...config2
    };
    this.logger = new StructuredLogger();
    this.metricsBuffer = /* @__PURE__ */ new Map();
    this.intervals = /* @__PURE__ */ new Map();
    this.pool = pgPool;
    this.connectionStatus = "disconnected";
    this.isBackend = typeof window === "undefined" && typeof process !== "undefined";
    this.logger.info("DatabaseIntegrator: Initialized", {
      config: {
        resilience: this.config.resilience.enabled,
        cache: this.config.cache.redisEnabled ? "redis" : "memory",
        metrics: this.config.metrics.enabled,
        backend: this.isBackend,
        postgresql: !!this.pool
      }
    });
    if (this.isBackend) {
      this.initializeConnection();
    }
  }
  /**
   * Inicializa conexão com PostgreSQL
   */
  async initializeConnection() {
    if (!this.isBackend) {
      this.logger.info("PostgreSQL não disponível - usando modo mock");
      return;
    }
    try {
      const pool2 = await loadPostgreSQL();
      if (pool2) {
        this.pool = pool2;
        const client = await this.pool.connect();
        await client.query("SELECT 1 as test");
        client.release();
        this.connectionStatus = "connected";
        this.logger.info("✅ Conexão PostgreSQL estabelecida com sucesso");
      } else {
        throw new Error("Pool PostgreSQL não disponível");
      }
    } catch (error) {
      this.connectionStatus = "error";
      this.logger.error("❌ Erro ao conectar com PostgreSQL:", error.message);
    }
  }
  /**
   * Verifica se está conectado com PostgreSQL
   */
  isConnected() {
    return this.connectionStatus === "connected" && !!this.pool;
  }
  /**
   * Obtém status da conexão
   */
  getStatus() {
    return {
      status: this.connectionStatus,
      connected: this.isConnected(),
      type: this.isBackend ? "PostgreSQL" : "Mock",
      backend: this.isBackend,
      poolAvailable: !!this.pool
    };
  }
  /**
   * Executa query SQL no PostgreSQL
   */
  async query(sql, params = []) {
    if (!this.isConnected()) {
      this.logger.warn("📄 [MOCK] Query executada:", { sql, params });
      return [];
    }
    try {
      const client = await this.pool.connect();
      const result = await client.query(sql, params);
      client.release();
      this.logger.info("✅ Query executada com sucesso", {
        sql: sql.substring(0, 100) + "...",
        rowCount: result.rowCount
      });
      return result.rows;
    } catch (error) {
      this.logger.error("❌ Erro ao executar query:", error.message);
      throw error;
    }
  }
  /**
   * Armazena dados em uma tabela
   */
  async store(table, data) {
    if (!this.isConnected()) {
      this.logger.warn("📄 [MOCK] Dados armazenados em", table, ":", data);
      return { success: true, id: Date.now() };
    }
    try {
      const columns = Object.keys(data);
      const values = Object.values(data);
      const placeholders = values.map((_, index) => `$${index + 1}`).join(", ");
      const sql = `INSERT INTO ${table} (${columns.join(", ")}) VALUES (${placeholders}) RETURNING id`;
      const result = await this.query(sql, values);
      this.logger.info("✅ Dados armazenados com sucesso", { table, id: result[0]?.id });
      return { success: true, id: result[0]?.id, ...data };
    } catch (error) {
      this.logger.error("❌ Erro ao armazenar dados:", error.message);
      throw error;
    }
  }
  /**
   * Interface unificada para persistência de métricas detalhadas
   * @param {string} userId - ID do usuário
   * @param {string} gameId - ID do jogo
   * @param {Object} metrics - Objeto de métricas detalhadas
   * @returns {Promise<Object>} Confirmação da persistência
   */
  async saveGameMetrics(userId, gameId, metrics) {
    try {
      const validation = InputValidator.validateGameMetricsInput(userId, gameId, metrics);
      if (!validation.valid) {
        throw new Error(`Invalid input: ${validation.errors.join(", ")}`);
      }
      const { userId: sanitizedUserId, gameId: sanitizedGameId, metrics: sanitizedMetrics } = validation.sanitized;
      if (this.isConnected()) {
        const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const gameMetricsData = {
          session_id: sessionId,
          user_id: sanitizedUserId,
          game_id: sanitizedGameId,
          metrics_data: JSON.stringify(sanitizedMetrics),
          accuracy: sanitizedMetrics.accuracy || 0,
          response_time: sanitizedMetrics.averageResponseTime || 0,
          engagement_score: sanitizedMetrics.engagementScore || 0,
          created_at: (/* @__PURE__ */ new Date()).toISOString()
        };
        const result = await this.store("game_metrics", gameMetricsData);
        this.logger.info("✅ Game metrics saved to PostgreSQL", {
          userId: sanitizedUserId,
          gameId: sanitizedGameId,
          sessionId,
          metricsCount: Object.keys(sanitizedMetrics).length
        });
        return {
          sessionId,
          userId: sanitizedUserId,
          gameId: sanitizedGameId,
          timestamp: Date.now(),
          status: "success",
          database: "postgresql",
          id: result.id
        };
      } else {
        const result = {
          sessionId: Date.now(),
          userId: sanitizedUserId,
          gameId: sanitizedGameId,
          timestamp: Date.now(),
          status: "success",
          database: "mock"
        };
        this.logger.info("📄 [MOCK] Game metrics saved", {
          userId: sanitizedUserId,
          gameId: sanitizedGameId,
          sessionId: result.sessionId,
          metricsCount: Object.keys(sanitizedMetrics).length
        });
        return result;
      }
    } catch (error) {
      this.logger.error("Failed to save game metrics", {
        userId,
        gameId,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }
  /**
   * Salva as interações detalhadas no banco de dados
   * @param {number} sessionId - ID da sessão
   * @param {Array<Object>} interactions - Lista de interações
   * @returns {Promise<Object>} Confirmação da persistência
   */
  async saveInteractions(sessionId, interactions) {
    try {
      const validation = InputValidator.validateInteractionsInput(sessionId, interactions);
      if (!validation.valid) {
        throw new Error(`Invalid interactions input: ${validation.errors.join(", ")}`);
      }
      const { sessionId: sanitizedSessionId, interactions: sanitizedInteractions } = validation.sanitized;
      const result = {
        sessionId: sanitizedSessionId,
        interactionCount: sanitizedInteractions.length,
        timestamp: Date.now(),
        status: "success"
      };
      this.logger.info("Interactions saved successfully (frontend)", {
        sessionId: sanitizedSessionId,
        interactionCount: sanitizedInteractions.length
      });
      return result;
    } catch (error) {
      this.logger.error("Failed to save interactions", {
        sessionId,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }
  /**
   * Interface unificada para recuperação de dados com cache e resiliência
   * @param {string} userId - ID do usuário
   * @param {string} dataType - Tipo de dados ('progress', 'profile', 'predictions')
   * @returns {Promise<Object>} Dados solicitados
   */
  async getUserData(userId, dataType) {
    try {
      const validation = InputValidator.validateUserDataInput(userId, dataType);
      if (!validation.valid) {
        throw new Error(`Invalid input: ${validation.errors.join(", ")}`);
      }
      const { userId: sanitizedUserId, dataType: sanitizedDataType } = validation.sanitized;
      const result = {
        userId: sanitizedUserId,
        dataType: sanitizedDataType,
        data: {},
        timestamp: Date.now(),
        status: "success"
      };
      this.logger.info("User data retrieved successfully (frontend)", {
        userId: sanitizedUserId,
        dataType: sanitizedDataType
      });
      return result;
    } catch (error) {
      this.logger.error("Failed to retrieve user data", {
        userId,
        dataType,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }
  /**
   * Obtém status completo do sistema
   * @returns {Object} Status de todos os componentes
   */
  getCompleteSystemStatus() {
    try {
      const status = {
        database: {
          connected: true,
          isConnected: true,
          type: "frontend-simulation",
          status: "operational"
        },
        resilience: { status: "operational" },
        sessionManager: { status: "operational" },
        metricsEngine: { status: "operational" },
        systemOrchestrator: { status: "operational" },
        metricsBuffer: {
          size: this.metricsBuffer.size,
          status: "operational"
        },
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
      return status;
    } catch (error) {
      this.logger.error("Error retrieving system status", { error: error.message, stack: error.stack });
      return {
        database: { connected: false, isConnected: false, type: "frontend-simulation", status: "failed" },
        resilience: { status: "failed" },
        sessionManager: { status: "failed" },
        metricsEngine: { status: "failed" },
        systemOrchestrator: { status: "failed" },
        metricsBuffer: { size: 0, status: "failed" },
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    }
  }
  /**
   * Salva sessão completa no banco de dados
   * @param {string} userId - ID do usuário
   * @param {string} gameId - ID do jogo
   * @param {Object} sessionData - Dados da sessão
   * @returns {Promise<Object>} Confirmação da persistência
   */
  async saveCompleteSession(userId, gameId, sessionData) {
    try {
      if (this.isConnected()) {
        const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const gameSessionData = {
          session_id: sessionId,
          user_id: userId,
          game_id: gameId,
          session_data: JSON.stringify(sessionData),
          started_at: sessionData.startTime ? new Date(sessionData.startTime).toISOString() : (/* @__PURE__ */ new Date()).toISOString(),
          ended_at: sessionData.endTime ? new Date(sessionData.endTime).toISOString() : (/* @__PURE__ */ new Date()).toISOString(),
          duration: sessionData.duration || 0,
          created_at: (/* @__PURE__ */ new Date()).toISOString()
        };
        const result = await this.store("game_sessions", gameSessionData);
        this.logger.info("✅ Complete session saved to PostgreSQL", {
          userId,
          gameId,
          sessionId,
          duration: sessionData.duration
        });
        return { sessionId, userId, gameId, status: "success", database: "postgresql", id: result.id };
      } else {
        this.logger.info("📄 [MOCK] Complete session saved", { userId, gameId });
        return { sessionId: Date.now(), userId, gameId, status: "success", database: "mock" };
      }
    } catch (error) {
      this.logger.error("❌ Erro ao salvar sessão completa:", error.message);
      throw error;
    }
  }
  /**
   * Salva relatório de sessão
   * @param {Object} report - Relatório da sessão
   * @returns {Promise<Object>} Confirmação da persistência
   */
  async saveSessionReport(report) {
    try {
      if (this.isConnected()) {
        const reportData = {
          session_id: report.sessionId,
          user_id: report.userId,
          analysis_type: report.type || "session_report",
          analysis_data: JSON.stringify(report),
          confidence_score: report.confidence || 0,
          created_at: (/* @__PURE__ */ new Date()).toISOString()
        };
        const result = await this.store("therapeutic_analysis", reportData);
        this.logger.info("✅ Session report saved to PostgreSQL", {
          sessionId: report.sessionId,
          type: report.type
        });
        return { status: "success", database: "postgresql", id: result.id };
      } else {
        this.logger.info("📄 [MOCK] Session report saved", { sessionId: report.sessionId });
        return { status: "success", database: "mock", id: Date.now() };
      }
    } catch (error) {
      this.logger.error("❌ Erro ao salvar relatório:", error.message);
      throw error;
    }
  }
  /**
   * Obtém status do sistema integrador
   * @returns {Object} Status do sistema
   */
  getSystemStatus() {
    return this.getCompleteSystemStatus();
  }
  /**
   * Limpa recursos e finaliza intervalos
   * @returns {Promise<void>}
   */
  async cleanup() {
    try {
      this.logger.info("Initiating DatabaseIntegrator cleanup...");
      for (const [intervalName, intervalId] of this.intervals.entries()) {
        clearInterval(intervalId);
        this.logger.info(`Interval stopped: ${intervalName}`);
      }
      this.intervals.clear();
      this.logger.info("✅ DatabaseIntegrator cleanup completed");
    } catch (error) {
      this.logger.error("Error during DatabaseIntegrator cleanup", { error: error.message, stack: error.stack });
      throw error;
    }
  }
};
const DatabaseIntegrator$2 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  DatabaseIntegrator: DatabaseIntegrator$1,
  InputValidator,
  default: DatabaseIntegrator$1
}, Symbol.toStringTag, { value: "Module" }));
let DatabaseIntegrator2 = null;
let isLoading = false;
class DatabaseSingleton {
  constructor() {
    this.instance = null;
    this.isInitializing = false;
    this.logger = this.createLogger();
    this.supportedGames = [
      "ImageAssociation",
      "MemoryGame",
      "MusicalSequence",
      "PadroesVisuais",
      "ContagemNumeros",
      "PatternMatching",
      "SequenceLearning",
      "CreativePainting",
      "QuebraCabeca",
      "LetterRecognition",
      "ColorMatch"
    ];
  }
  /**
   * Cria logger estruturado
   */
  createLogger() {
    const isBrowser = typeof window !== "undefined";
    return {
      info: (...args) => console.info(
        isBrowser ? "%c💾 [DB-SINGLETON]" : "💾 [DB-SINGLETON]",
        isBrowser ? "color: #2196F3" : "",
        (/* @__PURE__ */ new Date()).toISOString(),
        ...args
      ),
      error: (...args) => console.error(
        isBrowser ? "%c🔴 [DB-ERROR]" : "🔴 [DB-ERROR]",
        isBrowser ? "color: #F44336" : "",
        (/* @__PURE__ */ new Date()).toISOString(),
        ...args
      ),
      warn: (...args) => console.warn(
        isBrowser ? "%c🟡 [DB-WARN]" : "🟡 [DB-WARN]",
        isBrowser ? "color: #FF9800" : "",
        (/* @__PURE__ */ new Date()).toISOString(),
        ...args
      )
    };
  }
  /**
   * Carrega o DatabaseIntegrator dinamicamente
   */
  async loadDatabaseIntegrator() {
    if (DatabaseIntegrator2) return DatabaseIntegrator2;
    if (isLoading) {
      while (isLoading) {
        await new Promise((resolve) => setTimeout(resolve, 10));
      }
      return DatabaseIntegrator2;
    }
    isLoading = true;
    try {
      const module = await __vitePreload(() => Promise.resolve().then(() => DatabaseIntegrator$2), true ? void 0 : void 0);
      DatabaseIntegrator2 = module.default || module.DatabaseIntegrator;
      return DatabaseIntegrator2;
    } catch (error) {
      this.logger.error("❌ Erro ao carregar DatabaseIntegrator:", error);
      throw error;
    } finally {
      isLoading = false;
    }
  }
  /**
   * Retorna a instância singleton do DatabaseIntegrator
   */
  async getInstance() {
    if (this.instance) {
      this.logger.info("✅ Retornando instância existente do DatabaseIntegrator");
      return this.instance;
    }
    if (this.isInitializing) {
      this.logger.info("⏳ Aguardando inicialização do DatabaseIntegrator");
      while (this.isInitializing) {
        await new Promise((resolve) => setTimeout(resolve, 10));
      }
      return this.instance;
    }
    this.isInitializing = true;
    try {
      const DatabaseIntegratorClass = await this.loadDatabaseIntegrator();
      const defaultConfig = {
        resilience: {
          enabled: true,
          monitoringEnabled: true,
          retryAttempts: 3,
          retryDelay: 1e3
        },
        cache: {
          memoryMaxSize: 2e3,
          memoryTTL: 3e5
          // 5 minutos
        },
        metrics: {
          enabled: true,
          detailedLogging: true,
          persistInterval: 6e4
          // 1 minuto
        }
      };
      this.instance = new DatabaseIntegratorClass(defaultConfig);
      this.logger.info("🔄 DatabaseIntegrator inicializado com sucesso");
      return this.instance;
    } catch (error) {
      this.logger.error("❌ Erro ao inicializar DatabaseIntegrator:", error);
      throw error;
    } finally {
      this.isInitializing = false;
    }
  }
  /**
   * Valida o esquema de dados para uma coleção
   */
  validateSchema(collection, data) {
    try {
      if (!data || typeof data !== "object") {
        this.logger.error("❌ Dados inválidos: deve ser um objeto", { collection });
        return false;
      }
      if (collection === "game_specific_analysis") {
        if (!data.gameName || !this.supportedGames.includes(data.gameName)) {
          this.logger.error("❌ gameName inválido ou não suportado", {
            gameName: data.gameName,
            supportedGames: this.supportedGames
          });
          return false;
        }
        if (!data.sessionId || !data.childId || !data.timestamp) {
          this.logger.error("❌ Campos obrigatórios ausentes", {
            sessionId: data.sessionId,
            childId: data.childId,
            timestamp: data.timestamp
          });
          return false;
        }
        return this.validateGameSpecificData(data);
      }
      return true;
    } catch (error) {
      this.logger.error("❌ Erro ao validar esquema:", error);
      return false;
    }
  }
  /**
   * Valida dados específicos por jogo
   */
  validateGameSpecificData(data) {
    switch (data.gameName) {
      case "ImageAssociation":
        return this.validateImageAssociationData(data);
      case "MemoryGame":
        return this.validateMemoryGameData(data);
      case "MusicalSequence":
        return this.validateMusicalSequenceData(data);
      case "PadroesVisuais":
        return this.validatePadroesVisuaisData(data);
      case "ContagemNumeros":
        return this.validateContagemNumerosData(data);
      case "PatternMatching":
        return this.validatePatternMatchingData(data);
      case "SequenceLearning":
        return this.validateSequenceLearningData(data);
      case "CreativePainting":
        return this.validateCreativePaintingData(data);
      case "QuebraCabeca":
        return this.validateQuebraCabecaData(data);
      case "LetterRecognition":
        return this.validateLetterRecognitionData(data);
      case "ColorMatch":
        return this.validateColorMatchData(data);
      default:
        this.logger.error("❌ Jogo não reconhecido:", data.gameName);
        return false;
    }
  }
  /**
   * Validações específicas por jogo
   */
  validateImageAssociationData(data) {
    return !!(data.analysisResult?.categoricalThinking && data.analysisResult?.semanticUnderstanding);
  }
  validateMemoryGameData(data) {
    return !!(data.analysisResult?.workingMemory && data.analysisResult?.visualMemory);
  }
  validateMusicalSequenceData(data) {
    return !!(data.analysisResult?.sequentialProcessing && data.analysisResult?.rhythmPerception);
  }
  validatePadroesVisuaisData(data) {
    return !!(data.analysisResult?.patternRecognition && data.analysisResult?.spatialProcessing);
  }
  validateContagemNumerosData(data) {
    return !!(data.analysisResult?.numberRecognition && data.analysisResult?.countingAbility);
  }
  validatePatternMatchingData(data) {
    return !!(data.analysisResult?.patternRecognition && data.analysisResult?.visualProcessing);
  }
  validateSequenceLearningData(data) {
    return !!(data.analysisResult?.sequentialMemory && data.analysisResult?.auditoryProcessing);
  }
  validateCreativePaintingData(data) {
    return !!(data.analysisResult?.creativityAnalysis && data.analysisResult?.motorSkills);
  }
  validateQuebraCabecaData(data) {
    return !!(data.analysisResult?.spatialReasoning && data.analysisResult?.problemSolving);
  }
  validateLetterRecognitionData(data) {
    return !!(data.analysisResult?.letterRecognition && data.analysisResult?.visualProcessing);
  }
  validateColorMatchData(data) {
    return !!(data.analysisResult?.colorRecognition && data.analysisResult?.visualDiscrimination);
  }
  /**
   * Armazena dados em uma coleção com validação
   */
  async store(collection, data) {
    try {
      if (!this.validateSchema(collection, data)) {
        throw new Error(`Validação de esquema falhou para coleção ${collection}`);
      }
      const instance = await this.getInstance();
      await instance.store(collection, {
        ...data,
        storedAt: (/* @__PURE__ */ new Date()).toISOString()
      });
      this.logger.info("💾 Dados armazenados com sucesso", {
        collection,
        sessionId: data.sessionId,
        gameName: data.gameName
      });
    } catch (error) {
      this.logger.error("❌ Erro ao armazenar dados:", error);
      throw error;
    }
  }
  /**
   * Consulta dados de uma coleção
   */
  async query(collection, query) {
    try {
      const instance = await this.getInstance();
      const results = await instance.query(collection, query);
      this.logger.info("🔍 Consulta realizada com sucesso", { collection, query });
      return results || [];
    } catch (error) {
      this.logger.error("❌ Erro ao consultar dados:", error);
      return [];
    }
  }
  /**
   * Atualiza dados em uma coleção
   */
  async update(collection, query, data) {
    try {
      const instance = await this.getInstance();
      const result = await instance.update(collection, query, data);
      this.logger.info("🔄 Dados atualizados com sucesso", { collection, query });
      return result;
    } catch (error) {
      this.logger.error("❌ Erro ao atualizar dados:", error);
      throw error;
    }
  }
  /**
   * Remove dados de uma coleção
   */
  async delete(collection, query) {
    try {
      const instance = await this.getInstance();
      const result = await instance.delete(collection, query);
      this.logger.info("🗑️ Dados removidos com sucesso", { collection, query });
      return result;
    } catch (error) {
      this.logger.error("❌ Erro ao remover dados:", error);
      throw error;
    }
  }
  /**
   * Limpa cache e reinicia instância
   */
  async reset() {
    try {
      this.logger.info("🔄 Reiniciando DatabaseSingleton...");
      if (this.instance && this.instance.clearCache) {
        await this.instance.clearCache();
      }
      this.instance = null;
      this.isInitializing = false;
      this.logger.info("✅ DatabaseSingleton reiniciado");
    } catch (error) {
      this.logger.error("❌ Erro ao reiniciar DatabaseSingleton:", error);
    }
  }
  /**
   * Obtém estatísticas da instância
   */
  async getStats() {
    try {
      const instance = await this.getInstance();
      if (instance && instance.getStats) {
        return await instance.getStats();
      }
      return null;
    } catch (error) {
      this.logger.error("❌ Erro ao obter estatísticas:", error);
      return null;
    }
  }
}
const DatabaseSingleton$1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: DatabaseSingleton
}, Symbol.toStringTag, { value: "Module" }));
const databaseInstance = {
  getInstance: () => Promise.resolve({
    mock: true,
    manager: {
      query: () => Promise.resolve([]),
      getStatus: () => ({ status: "mock", connected: true })
    },
    getStatus: () => ({ status: "mock", connected: true }),
    saveGameMetrics: () => Promise.resolve({ success: true })
  }),
  getStatus: () => ({ status: "mock", connected: true }),
  saveGameMetrics: () => Promise.resolve({ success: true })
};
const databaseInstance$1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: databaseInstance
}, Symbol.toStringTag, { value: "Module" }));
const compatibilityConfig = {
  // Configurações para desenvolvimento
  development: {
    ssl: false,
    connectionTimeoutMillis: 5e3,
    idleTimeoutMillis: 3e4,
    max: 10,
    statement_timeout: 3e4,
    query_timeout: 3e4
  },
  // Configurações para produção
  production: {
    ssl: {
      rejectUnauthorized: false
    },
    connectionTimeoutMillis: 1e4,
    idleTimeoutMillis: 6e4,
    max: 20,
    statement_timeout: 6e4,
    query_timeout: 6e4
  },
  // Configurações para testes
  test: {
    ssl: false,
    connectionTimeoutMillis: 3e3,
    idleTimeoutMillis: 1e4,
    max: 5,
    statement_timeout: 1e4,
    query_timeout: 1e4
  }
};
const currentEnv$1 = "development";
const config = compatibilityConfig[currentEnv$1] || compatibilityConfig.development;
console.log("🔧 Aplicando patches de compatibilidade para Node.js v22+");
if (!process.nextTick) {
  process.nextTick = function(callback, ...args) {
    setImmediate(() => callback(...args));
  };
}
if (typeof globalThis !== "undefined" && !globalThis.process) {
  globalThis.process = process;
}
if (!globalThis.setImmediate) {
  globalThis.setImmediate = setImmediate;
}
if (!globalThis.clearImmediate) {
  globalThis.clearImmediate = clearImmediate;
}
if (typeof EventEmitter !== "undefined") {
  const { EventEmitter: EventEmitter2 } = await __vitePreload(async () => {
    const { EventEmitter: EventEmitter22 } = await import("./vendor-misc-DTF8jSja.js").then((n) => n.a1);
    return { EventEmitter: EventEmitter22 };
  }, true ? __vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30]) : void 0);
  const originalEmit = EventEmitter2.prototype.emit;
  EventEmitter2.prototype.emit = function(type, ...args) {
    try {
      return originalEmit.call(this, type, ...args);
    } catch (error) {
      if (error.message.includes("process.nextTick")) {
        if (type === "error" && args[0]) {
          setImmediate(() => {
            throw args[0];
          });
        }
        return false;
      }
      throw error;
    }
  };
}
console.log("✅ PostgreSQL compatibility configuration loaded");
var define_process_env_default = {};
const databaseConfig = {
  // Configurações de conexão
  connection: {
    host: define_process_env_default.DB_HOST || "localhost",
    port: parseInt(define_process_env_default.DB_PORT) || 5432,
    user: define_process_env_default.DB_USER || "betina_user",
    password: define_process_env_default.DB_PASSWORD || "betina_password",
    database: define_process_env_default.DB_NAME || "betina_db",
    // Aplicar configurações de compatibilidade
    ...config,
    // Configurações específicas
    application_name: "Portal_Betina_V3",
    keepAlive: true,
    keepAliveInitialDelayMillis: 1e4
  },
  // Pool de conexões
  pool: {
    min: 2,
    max: config.max || 10,
    createTimeoutMillis: 8e3,
    acquireTimeoutMillis: 8e3,
    idleTimeoutMillis: config.idleTimeoutMillis || 3e4,
    reapIntervalMillis: 1e3,
    createRetryIntervalMillis: 100,
    propagateCreateError: false
  },
  // Configurações de migração
  migrations: {
    directory: "./src/database/migrations",
    tableName: "knex_migrations",
    extension: "js",
    disableTransactions: false
  },
  // Configurações de seeds
  seeds: {
    directory: "./src/database/seeds"
  },
  // Configurações de logging
  logging: {
    enabled: true,
    level: define_process_env_default.LOG_LEVEL || "info",
    queries: true
  },
  // Configurações de cache
  cache: {
    enabled: true,
    ttl: 3e5,
    // 5 minutos
    maxSize: 1e3
  },
  // Configurações de retry
  retry: {
    enabled: true,
    maxAttempts: 3,
    delay: 1e3,
    backoff: "exponential"
  },
  // Configurações de health check
  healthCheck: {
    enabled: true,
    interval: 3e4,
    // 30 segundos
    timeout: 5e3,
    query: "SELECT 1 as health_check"
  },
  // Configurações específicas para métricas multissensoriais
  multisensory: {
    batchSize: 100,
    flushInterval: 5e3,
    compressionEnabled: true,
    indexingStrategy: "btree_gin"
  }
};
const environmentConfigs = {
  development: {
    connection: {
      ...databaseConfig.connection,
      ssl: false
    },
    logging: {
      enabled: true,
      level: "debug",
      queries: true
    }
  },
  production: {
    connection: {
      ...databaseConfig.connection,
      ssl: define_process_env_default.DB_SSL_ENABLED === "true" ? {
        rejectUnauthorized: false
      } : false
    },
    logging: {
      enabled: false,
      level: "error",
      queries: false
    },
    pool: {
      ...databaseConfig.pool,
      max: 20,
      min: 5
    }
  },
  test: {
    connection: {
      ...databaseConfig.connection,
      database: define_process_env_default.DB_NAME_TEST || "betina_db_test",
      ssl: false
    },
    logging: {
      enabled: false,
      level: "error",
      queries: false
    },
    pool: {
      ...databaseConfig.pool,
      max: 5,
      min: 1
    }
  }
};
const currentEnv = "development";
const envConfig = environmentConfigs[currentEnv] || environmentConfigs.development;
const finalConfig = {
  ...databaseConfig,
  ...envConfig,
  connection: {
    ...databaseConfig.connection,
    ...envConfig.connection
  },
  pool: {
    ...databaseConfig.pool,
    ...envConfig.pool
  },
  logging: {
    ...databaseConfig.logging,
    ...envConfig.logging
  }
};
function validateConfig(config2) {
  const required = ["host", "port", "user", "password", "database"];
  const missing = required.filter((key) => !config2.connection[key]);
  if (missing.length > 0) {
    throw new Error(`Configurações obrigatórias faltando: ${missing.join(", ")}`);
  }
  return true;
}
validateConfig(finalConfig);
console.log(`✅ Database configuration loaded for environment: ${currentEnv}`);
const { Pool } = pg;
const pool = new Pool({
  host: finalConfig.connection.host,
  port: finalConfig.connection.port,
  user: finalConfig.connection.user,
  password: finalConfig.connection.password,
  database: finalConfig.connection.database,
  ssl: finalConfig.connection.ssl,
  application_name: finalConfig.connection.application_name,
  keepAlive: finalConfig.connection.keepAlive,
  keepAliveInitialDelayMillis: finalConfig.connection.keepAliveInitialDelayMillis,
  // Configurações do pool
  min: finalConfig.pool.min,
  max: finalConfig.pool.max,
  createTimeoutMillis: finalConfig.pool.createTimeoutMillis,
  acquireTimeoutMillis: finalConfig.pool.acquireTimeoutMillis,
  idleTimeoutMillis: finalConfig.pool.idleTimeoutMillis,
  reapIntervalMillis: finalConfig.pool.reapIntervalMillis,
  createRetryIntervalMillis: finalConfig.pool.createRetryIntervalMillis,
  propagateCreateError: finalConfig.pool.propagateCreateError
});
pool.on("connect", (client) => {
  if (finalConfig.logging.enabled) {
    console.log("✅ Nova conexão estabelecida com o banco de dados");
  }
});
pool.on("error", (err, client) => {
  console.error("❌ Erro inesperado no pool de conexões:", err);
});
pool.on("remove", (client) => {
  if (finalConfig.logging.enabled && finalConfig.logging.level === "debug") {
    console.log("🔄 Cliente removido do pool");
  }
});
async function checkPoolHealth() {
  try {
    const client = await pool.connect();
    await client.query(finalConfig.healthCheck.query);
    client.release();
    if (finalConfig.logging.enabled && finalConfig.logging.level === "debug") {
      console.log("💚 Pool de conexões saudável");
    }
    return true;
  } catch (error) {
    console.error("❌ Health check do pool falhou:", error.message);
    return false;
  }
}
if (finalConfig.healthCheck.enabled) {
  checkPoolHealth().then((healthy) => {
    if (healthy) {
      console.log(`✅ Pool de conexões inicializado com sucesso (${finalConfig.pool.min}-${finalConfig.pool.max} conexões)`);
    }
  });
  setInterval(checkPoolHealth, finalConfig.healthCheck.interval);
}
async function closePool() {
  try {
    await pool.end();
    console.log("✅ Pool de conexões finalizado com sucesso");
  } catch (error) {
    console.error("❌ Erro ao finalizar pool:", error.message);
  }
}
const testConnection = async () => {
  let client = null;
  try {
    console.log("🔍 Testando conexão com banco de dados...");
    client = await pool.connect();
    const result = await client.query("SELECT NOW() as current_time, version() as postgres_version");
    console.log("✅ Conexão com banco de dados bem-sucedida!");
    console.log("📅 Horário do servidor:", result.rows[0].current_time);
    console.log("🗄️ Versão PostgreSQL:", result.rows[0].postgres_version.split(" ")[0]);
    return true;
  } catch (error) {
    console.error("❌ Erro ao testar conexão:", error.message);
    return false;
  } finally {
    if (client) {
      client.release();
    }
  }
};
const initializeDatabase = async (retries = 3) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      console.log(`🚀 Tentativa ${attempt}/${retries} de inicialização do banco...`);
      const isConnected = await testConnection();
      if (isConnected) {
        console.log("✅ Banco de dados inicializado com sucesso!");
        return pool;
      } else {
        throw new Error("Falha na conexão");
      }
    } catch (error) {
      console.error(`❌ Tentativa ${attempt} falhou:`, error.message);
      if (attempt === retries) {
        throw new Error(`Falha ao inicializar banco após ${retries} tentativas: ${error.message}`);
      }
      const delay = 5e3 * attempt;
      console.log(`⏳ Aguardando ${delay}ms antes da próxima tentativa...`);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }
};
process.on("SIGINT", closePool);
process.on("SIGTERM", closePool);
const pool$1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  checkPoolHealth,
  closePool,
  default: pool,
  initializeDatabase,
  pool,
  testConnection
}, Symbol.toStringTag, { value: "Module" }));
export {
  DatabaseIntegrator$2 as D,
  DatabaseSingleton$1 as a,
  databaseInstance$1 as d
};
//# sourceMappingURL=database-CBRromfB.js.map
