/**
 * @file frontend-only.js
 * @description Configuração para dependências exclusivas do frontend
 * @version 3.0.0
 */

// Lista de módulos que devem ser excluídos do bundle frontend
export const BACKEND_ONLY_MODULES = [
  'pg',
  'redis',
  'winston',
  'express',
  'nodemon',
  'bcrypt',
  'bcryptjs',
  'jsonwebtoken',
  'helmet',
  'compression',
  'cors',
  'rate-limiter-flexible',
  'express-rate-limit',
  'multer',
  'body-parser',
  'cookie-parser',
  'morgan',
  'passport',
  'passport-local',
  'passport-jwt',
  'dotenv',
  'fs',
  'path',
  'os',
  'crypto',
  'util',
  'stream',
  'events',
  'url',
  'querystring',
  'zlib',
  'child_process'
];

// Lista de módulos que devem ser incluídos apenas no frontend
export const FRONTEND_ONLY_MODULES = [
  'react',
  'react-dom',
  'react-router-dom',
  'styled-components',
  'framer-motion',
  'chart.js',
  'react-chartjs-2',
  '@tanstack/react-query',
  'react-helmet-async'
];

// Configuração para code splitting
export const CHUNK_GROUPS = {
  react: ['react', 'react-dom'],
  router: ['react-router-dom'],
  ui: ['styled-components', 'framer-motion'],
  charts: ['chart.js', 'react-chartjs-2'],
  utils: ['lodash', 'moment', 'date-fns', 'uuid'],
  validation: ['joi', 'zod'],
  query: ['@tanstack/react-query'],
  accessibility: ['react-helmet-async']
};

export default {
  BACKEND_ONLY_MODULES,
  FRONTEND_ONLY_MODULES,
  CHUNK_GROUPS
};
