# Guia de Integração - Database V2 para V3

Este guia apresenta os passos para integrar os componentes migrados do sistema database V2 para o Portal Betina V3.

## Componentes Migrados

Os seguintes componentes foram migrados da V2 para a V3:

1. **Sistema de Resiliência**
   - CircuitBreaker
   - ResilienceStrategies

2. **DatabaseServiceExtended**
   - Versão estendida do DatabaseService com recursos avançados

3. **DatabaseIntegrator**
   - Integrador dos componentes migrados

## Instruções de Integração

### 1. Usar CircuitBreaker para Operações Críticas

O CircuitBreaker protege contra falhas em cascata e permite melhor tratamento de erros em operações críticas, especialmente em integrações externas.

```javascript
import { CircuitBreaker } from './api/services/core/resilience';

// Criar um circuit breaker para operações de API
const apiCircuitBreaker = new CircuitBreaker({
  failureThreshold: 3,
  timeout: 30000,
  retryTimeout: 5000
});

// Usar o circuit breaker
async function fetchExternalData() {
  return apiCircuitBreaker.execute(
    async () => {
      // Operação que pode falhar
      const response = await fetch('https://api.external.com/data');
      return response.json();
    },
    // Função de fallback
    async () => {
      return { error: true, fallback: true };
    },
    'fetch-external-data'
  );
}
```

### 2. Usar ResilienceStrategies para Múltiplas Estratégias

ResilienceStrategies combina múltiplos padrões de resiliência (retry, timeout, circuit breaker, fallback).

```javascript
import resilience from './api/services/core/resilience';

// Executar função com múltiplas estratégias
async function performCriticalOperation() {
  return resilience.execute(
    async () => {
      // Sua operação crítica aqui
      return await importantAPICall();
    },
    {
      context: 'critical-operation',
      retry: true,
      timeout: true,
      circuitBreaker: true,
      retryConfig: { attempts: 3, delay: 1000 },
      timeoutConfig: { duration: 5000 }
    }
  );
}
```

### 3. Usar DatabaseServiceExtended em vez de DatabaseService

DatabaseServiceExtended adiciona recursos de resiliência e cache avançado ao DatabaseService padrão.

```javascript
// Substituir
import DatabaseService from './api/services/DatabaseService';
const dbService = new DatabaseService();

// Por
import DatabaseServiceExtended from './api/services/core/DatabaseServiceExtended';
const dbService = new DatabaseServiceExtended();

// Usar métodos com resiliência automática
await dbService.saveMetrics(userId, gameId, metrics, analysis);

// Usar cache inteligente
const userData = await dbService.getCachedData(
  `user:${userId}:profile`,
  async () => {
    // Buscar dados do usuário
    return await fetchUserProfile(userId);
  },
  {
    cache: { ttl: 3600000 }, // 1 hora
    resilience: { retry: true }
  }
);
```

### 4. Usar o DatabaseIntegrator para Integração Completa

O DatabaseIntegrator fornece uma interface unificada para todos os componentes migrados.

```javascript
import DatabaseIntegrator from './api/services/DatabaseIntegrator';

// Inicializar o integrador
const dbIntegrator = new DatabaseIntegrator({
  redisUrl: process.env.REDIS_URL
});

// Salvar métricas com toda a pipeline de processamento
await dbIntegrator.saveGameMetrics(userId, gameId, metrics);

// Obter dados do usuário com cache e resiliência
const userProfile = await dbIntegrator.getUserData(userId, 'profile');
const userProgress = await dbIntegrator.getUserData(userId, 'progress');

// Verificar status do sistema
const systemStatus = dbIntegrator.getStatus();
console.log('Circuit breakers:', systemStatus.resilience);
```

### 5. Monitoramento e Logs

O sistema migrado inclui recursos de monitoramento:

```javascript
// Verificar status dos circuit breakers
const cbStatus = resilience.getCircuitBreakersStatus();
console.log('Circuit breaker status:', cbStatus);

// Monitorar mudanças de estado
const cb = resilience.getCircuitBreaker('api-operations');
cb.onStateChange((prevState, newState) => {
  console.log(`Circuit breaker changed from ${prevState} to ${newState}`);
});

// Obter status completo do DatabaseIntegrator
const status = dbIntegrator.getStatus();
```

## Exemplo de Uso em Componente React

```jsx
import { useEffect, useState } from 'react';
import DatabaseIntegrator from './api/services/DatabaseIntegrator';

const dbIntegrator = new DatabaseIntegrator();

function UserProfileComponent({ userId }) {
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function loadProfile() {
      try {
        setLoading(true);
        const userData = await dbIntegrator.getUserData(userId, 'profile');
        setProfile(userData);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
    
    loadProfile();
  }, [userId]);

  if (loading) return <div>Carregando...</div>;
  if (error) return <div>Erro: {error}</div>;
  
  return (
    <div>
      <h1>{profile.name}</h1>
      <p>Nível: {profile.level}</p>
      {/* ... outros dados do perfil ... */}
    </div>
  );
}
```

## Próximos Componentes a Migrar

1. **Sistema de Plugins**
   - Migrar o PluginManager para permitir extensibilidade via plugins

2. **Cache Avançado**
   - Estender o CacheService com mais recursos do IntelligentCache

3. **Sistema de Perfis**
   - Adaptar o sistema de perfis usando o padrão adaptador
