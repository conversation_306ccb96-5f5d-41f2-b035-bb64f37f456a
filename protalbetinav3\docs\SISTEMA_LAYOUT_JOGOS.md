# 🎮 Sistema de Layout dos Jogos - Portal Betina V3

## Visão Geral

O sistema de layout dos jogos foi projetado com foco **Mobile First** para fornecer uma experiência consistente e acessível em todos os dispositivos. O sistema é composto por:

1. **GameLayout.jsx** - Componente de layout base
2. **GameLayout.css** - Estilos do layout (Mobile First)
3. **GameBase.css** - Componentes reutilizáveis dos jogos
4. **withGameLayout.jsx** - HOC para facilitar implementação
5. **GameComponents.jsx** - Componentes compartilhados
6. **GameUtils.js** - Utilitários e hooks

## 🏗️ Arquitetura

### 1. Layout Base (GameLayout)

O layout base fornece a estrutura fundamental:

```jsx
import GameLayout from '../shared/GameLayout';

<GameLayout
  gameTitle="🎨 Meu Jogo"
  gameIcon="🎮"
  showProgressBar={true}
  progress={50}
  showStats={true}
  stats={{ pontuação: 100, rodada: 5 }}
  onBack={handleBack}
  onPause={handlePause}
  backgroundColor="primary"
>
  {/* Conteúdo do jogo */}
</GameLayout>
```

### 2. Higher-Order Component (Recomendado)

Para facilitar a implementação, use o HOC `withGameLayout`:

```jsx
import withGameLayout from '../shared/withGameLayout';

const MeuJogo = ({ gameState, stats, updateStats, onStart, onFinish }) => {
  // Lógica do jogo
  return (
    <div className="game-area">
      {/* Conteúdo do jogo */}
    </div>
  );
};

const gameConfig = {
  title: '🎮 Meu Jogo',
  icon: '🎮',
  backgroundColor: 'primary',
  maxProgress: 100
};

export default withGameLayout(MeuJogo, gameConfig);
```

### 3. Hook Personalizado

Para casos mais específicos, use o hook `useGameCore`:

```jsx
import { useGameCore } from '../shared/withGameLayout';

const MeuJogo = () => {
  const {
    gameState,
    progress,
    stats,
    startGame,
    updateProgress,
    updateStats,
    finishGame
  } = useGameCore();

  // Usar funções conforme necessário
};
```

## 🎨 Sistema de Estilos

### Variáveis CSS Disponíveis

O sistema usa CSS Custom Properties para consistência:

```css
/* Cores */
--primary-color: #4A90E2;
--secondary-color: #7ED321;
--accent-color: #FF6B35;

/* Espaçamentos */
--spacing-xs: 0.25rem;
--spacing-sm: 0.5rem;
--spacing-md: 1rem;
--spacing-lg: 1.5rem;

/* Tipografia */
--font-size-xs: 0.75rem;
--font-size-sm: 0.875rem;
--font-size-md: 1rem;

/* Bordas */
--border-radius-sm: 8px;
--border-radius-md: 12px;
--border-radius-lg: 16px;
```

### Classes CSS Principais

#### Layout
- `.game-area` - Área principal do jogo
- `.target-section` - Seção de objetivo/alvo
- `.options-grid` - Grade de opções
- `.game-controls` - Controles do jogo

#### Componentes
- `.difficulty-selector` - Seletor de dificuldade
- `.difficulty-button` - Botão de dificuldade
- `.game-stats` - Estatísticas
- `.option-item` - Item de opção
- `.game-button` - Botões de ação

#### Estados
- `.option-item--selected` - Opção selecionada
- `.option-item--correct` - Resposta correta
- `.option-item--incorrect` - Resposta incorreta
- `.game-button--primary` - Botão primário
- `.game-button--secondary` - Botão secundário

## 📱 Responsividade (Mobile First)

### Breakpoints

```css
/* Mobile: < 768px (padrão) */
/* Tablet: 768px+ */
@media (min-width: 768px) { ... }

/* Desktop: 1024px+ */
@media (min-width: 1024px) { ... }
```

### Adaptações por Dispositivo

#### Mobile (padrão)
- Header compacto (60px)
- Botões com área de toque mínima (44px)
- Grid responsivo 2-3 colunas
- Texto otimizado para tela pequena

#### Tablet (768px+)
- Header expandido (70px)
- Mais opções por linha
- Textos e botões maiores
- Melhor aproveitamento do espaço

#### Desktop (1024px+)
- Layout centralizado (max-width: 1200px)
- Header completo (80px)
- Grade otimizada para desktop
- Interações aprimoradas

## 🎮 Implementação de Jogos

### Estrutura Padrão

```
src/games/MeuJogo/
├── MeuJogo.jsx              # Componente principal
├── MeuJogoWithLayout.jsx    # Versão com layout
├── MeuJogo.module.css       # Estilos específicos
├── MeuJogoConfig.js         # Configurações
└── MeuJogoMetrics.js        # Métricas específicas
```

### Exemplo Completo

```jsx
// MeuJogo.jsx
const MeuJogoCore = ({ 
  gameState, 
  stats, 
  updateStats, 
  onStart, 
  onFinish,
  constants 
}) => {
  const [currentQuestion, setCurrentQuestion] = useState(null);

  if (gameState === constants.GAME_STATES.READY) {
    return (
      <div className="game-area">
        <DifficultySelector
          difficulties={DIFFICULTIES}
          currentDifficulty={difficulty}
          onSelect={setDifficulty}
        />
        <div className="game-controls">
          <button className="game-button game-button--primary" onClick={onStart}>
            🎮 Começar
          </button>
        </div>
      </div>
    );
  }

  if (gameState === constants.GAME_STATES.PLAYING) {
    return (
      <div className="game-area">
        <div className="target-section">
          <h2 className="target-name">Pergunta {stats.rodada}</h2>
          <p className="target-instruction">{currentQuestion}</p>
        </div>

        <div className="options-grid">
          {options.map((option, index) => (
            <button
              key={index}
              className="option-item"
              onClick={() => handleAnswer(option)}
            >
              {option}
            </button>
          ))}
        </div>
      </div>
    );
  }

  return null;
};

// Configuração
const gameConfig = {
  title: '🧩 Meu Jogo',
  icon: '🧩',
  backgroundColor: 'success',
  maxProgress: 100,
  enableTimer: true
};

// Exportar com layout
export default withGameLayout(MeuJogoCore, gameConfig);
```

## ♿ Acessibilidade

### Recursos Implementados

1. **Navegação por Teclado**
   - Todos os elementos interativos são focáveis
   - Ordem de foco lógica e consistente

2. **Screen Reader**
   - Labels apropriados em todos os elementos
   - Textos alternativos para elementos visuais
   - Classe `.game-layout__sr-only` para conteúdo apenas para leitores

3. **Contraste e Visibilidade**
   - Suporte a `prefers-contrast: high`
   - Cores com contraste adequado
   - Tamanhos de fonte legíveis

4. **Redução de Movimento**
   - Suporte a `prefers-reduced-motion: reduce`
   - Animações opcionais

5. **Área de Toque**
   - Mínimo 44px para elementos interativos
   - Espaçamento adequado entre elementos

### Implementação

```jsx
// Elemento acessível
<button
  className="option-item"
  aria-label={`Opção: ${option.name}`}
  tabIndex={0}
  onClick={handleClick}
  onKeyDown={(e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick();
    }
  }}
>
  {option.content}
</button>

// Texto apenas para screen readers
<span className="game-layout__sr-only">
  Instruções adicionais para leitores de tela
</span>
```

## 🔧 Utilitários Disponíveis

### GameUtils

```javascript
import { GameUtils } from '../shared/GameUtils';

// Embaralhar array
const shuffled = GameUtils.shuffleArray(myArray);

// Calcular pontuação
const score = GameUtils.calculateScore(basePoints, difficulty, timeMs, perfect);

// Calcular estrelas
const stars = GameUtils.calculateStars(accuracy);

// Formatar tempo
const timeString = GameUtils.formatTime(timeMs);

// Salvar/carregar progresso
GameUtils.saveProgress(gameId, progressData);
const progress = GameUtils.loadProgress(gameId);
```

### Constantes

```javascript
import { COMMON_CONSTANTS } from '../shared/GameUtils';

// Estados do jogo
COMMON_CONSTANTS.GAME_STATES.LOADING
COMMON_CONSTANTS.GAME_STATES.READY
COMMON_CONSTANTS.GAME_STATES.PLAYING
COMMON_CONSTANTS.GAME_STATES.PAUSED
COMMON_CONSTANTS.GAME_STATES.FINISHED

// Tipos de feedback
COMMON_CONSTANTS.FEEDBACK_TYPES.SUCCESS
COMMON_CONSTANTS.FEEDBACK_TYPES.ERROR
COMMON_CONSTANTS.FEEDBACK_TYPES.INFO
COMMON_CONSTANTS.FEEDBACK_TYPES.WARNING

// Dificuldades
COMMON_CONSTANTS.DIFFICULTIES.EASY
COMMON_CONSTANTS.DIFFICULTIES.MEDIUM
COMMON_CONSTANTS.DIFFICULTIES.HARD
```

## 🧪 Testando o Layout

### Preview HTML

Use os arquivos de preview na pasta `/preview/` para testar o visual:

```html
<!-- Exemplo no preview -->
<div class="game-area">
  <div class="target-section">
    <div class="target-circle" style="background: #FF0000;"></div>
    <h2 class="target-name">Encontre: Vermelho</h2>
  </div>
  
  <div class="options-grid">
    <button class="option-item" style="background: #FF0000;">
      <span class="option-label">Vermelho</span>
    </button>
  </div>
</div>
```

### Teste Responsivo

1. Teste em diferentes tamanhos de tela
2. Verifique a navegação por teclado
3. Teste com leitores de tela
4. Valide o contraste de cores
5. Teste em dispositivos reais

## 📋 Checklist de Implementação

### ✅ Layout Base
- [ ] Usar `withGameLayout` ou `GameLayout` diretamente
- [ ] Configurar título e ícone do jogo
- [ ] Implementar estados do jogo (ready, playing, finished)
- [ ] Configurar progresso e estatísticas

### ✅ Estilos
- [ ] Usar classes CSS padronizadas
- [ ] Implementar responsividade mobile-first
- [ ] Testar em diferentes dispositivos
- [ ] Validar contraste e acessibilidade

### ✅ Funcionalidade
- [ ] Implementar controles (start, pause, back)
- [ ] Gerenciar estados do jogo
- [ ] Salvar progresso localmente
- [ ] Implementar feedback visual/sonoro

### ✅ Acessibilidade
- [ ] Labels apropriados em elementos interativos
- [ ] Navegação por teclado funcional
- [ ] Suporte a leitores de tela
- [ ] Área de toque adequada (44px mínimo)

### ✅ Performance
- [ ] Otimizar imagens e recursos
- [ ] Implementar loading states
- [ ] Evitar re-renders desnecessários
- [ ] Testar em dispositivos menos potentes

## 🚀 Próximos Passos

1. **Migrar jogos existentes** para o novo sistema
2. **Implementar temas** (claro/escuro)
3. **Adicionar animações** personalizadas
4. **Melhorar feedback sonoro** 
5. **Implementar multiplayer** (futuro)

---

## 💡 Dicas e Boas Práticas

### Performance
- Use `React.memo` para componentes que renderizam frequentemente
- Implemente `useMemo` e `useCallback` quando apropriado
- Evite estados desnecessários

### UX/UI
- Mantenha consistência visual entre jogos
- Forneça feedback imediato para interações
- Use animações sutis para melhorar a experiência
- Teste em dispositivos reais sempre que possível

### Desenvolvimento
- Siga a estrutura de pastas padronizada
- Use TypeScript quando possível
- Documente componentes complexos
- Implemente testes unitários para lógica crítica

### Acessibilidade
- Teste com leitores de tela regularmente
- Valide navegação por teclado
- Use cores com contraste adequado
- Forneça alternativas para conteúdo visual

---

*Este documento é parte do Portal Betina V3 - Sistema de jogos terapêuticos com foco em acessibilidade e experiência do usuário.*
