/**
 * @file SystemContext.jsx
 * @description Provedor de contexto para acesso ao sistema integrado em toda a aplicação
 * @version 3.1.0
 */

import React, { createContext, useContext, useMemo } from "react";
import PropTypes from "prop-types";

// Criar o contexto com valor inicial null
export const SystemContext = createContext(null);

/**
 * Hook para acessar o sistema integrado
 * @returns {Object} Sistema integrado completo com métodos utilitários
 * @throws {Error} Se usado fora de um SystemProvider
 */
export function useSystem() {
  const context = useContext(SystemContext);
  if (!context) {
    throw new Error("useSystem deve ser usado dentro de um SystemProvider");
  }
  return context;
}

// Alias para compatibilidade
export const useSystemContext = useSystem;

/**
 * Provedor que disponibiliza o sistema integrado para a árvore de componentes
 * @param {Object} props - Propriedades do componente
 * @param {Object} props.system - Sistema integrado inicializado
 * @param {React.ReactNode} props.children - Componentes filhos
 * @returns {JSX.Element}
 */
export function SystemProvider({ system, children }) {
  // Validar sistema na inicialização
  if (!system || typeof system !== "object") {
    throw new Error("SystemProvider requer um objeto system válido");
  }

  // Memoizar o valor do contexto para evitar renderizações desnecessárias
  const systemValue = useMemo(
    () => ({
      system,
      // Métodos utilitários com binding seguro
      healthCheck: (...args) => system?.healthCheck?.(...args),
      getStatistics: (...args) => system?.getStatistics?.(...args),
      dispatchEvent: (...args) => system?.dispatchEvent?.(...args),
      trackUserInteraction: (...args) =>
        system?.trackUserInteraction?.(...args),

      // Informações de status do sistema com fallback seguro
      getStatus: () => ({
        database: system?.databaseInstance?.getStatus?.() ?? {
          status: "unavailable",
        },
        resilience: system?.resilience?.getCircuitBreakersStatus?.() ?? {
          status: "unavailable",
        },
        timestamp: new Date().toISOString(),
        version: "3.1.0",
      }),
    }),
    [system]
  );

  return (
    <SystemContext.Provider value={systemValue}>
      {children}
    </SystemContext.Provider>
  );
}

SystemProvider.propTypes = {
  system: PropTypes.shape({
    healthCheck: PropTypes.func,
    getStatistics: PropTypes.func,
    dispatchEvent: PropTypes.func,
    trackUserInteraction: PropTypes.func,
    databaseInstance: PropTypes.shape({
      getStatus: PropTypes.func,
    }),
    resilience: PropTypes.shape({
      getCircuitBreakersStatus: PropTypes.func,
    }),
  }).isRequired,
  children: PropTypes.node.isRequired,
};

// Exportação padrão
export default SystemProvider;
