import { I as IGameProcessor, G as GameProgressionAnalyzer, a as AttentionalSelectivityAnalyzer, V as VisualProcessingAnalyzer } from "./services-M1ydzWhv.js";
import { r as reactExports, R as React } from "./vendor-react-ByWh_-BW.js";
import { S as SystemContext, b as useAccessibilityContext } from "./context-Ch-5FaFa.js";
import { G as GameStartScreen } from "./game-association-B9GAxBuN.js";
import { a as useUnifiedGameLogic, b as useMultisensoryIntegration, c as useTherapeuticOrchestrator } from "./hooks-NJkOkh4y.js";
import { v as v4 } from "./vendor-utils-CjlX8hrF.js";
class ColorPerceptionCollector {
  constructor() {
    this.colorSpaces = {
      rgb: "Red-Green-Blue",
      hsv: "Hue-Saturation-Value",
      lab: "CIELAB Color Space"
    };
    this.perceptionThresholds = {
      excellent: 0.95,
      good: 0.85,
      average: 0.7,
      poor: 0.5,
      critical: 0.3
    };
    this.colorCategories = {
      primary: ["vermelho", "azul", "amarelo"],
      secondary: ["verde", "laranja", "roxo"],
      tertiary: ["rosa", "marrom", "cinza"],
      neutral: ["branco", "preto"]
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de percepção de cores
   */
  collect(data) {
    return this.analyze(data);
  }
  async analyze(data) {
    if (!data || !data.colorData) {
      console.warn("ColorPerceptionCollector: Dados inválidos recebidos", data);
      return {
        colorDiscrimination: 0.7,
        huePerception: 0.7,
        saturationSensitivity: 0.7,
        brightnessDiscrimination: 0.7,
        colorConstancy: 0.7,
        perceptualAccuracy: 0.7,
        colorMemory: 0.7,
        adaptationEfficiency: 0.7
      };
    }
    return {
      colorDiscrimination: this.assessColorDiscrimination(data),
      huePerception: this.assessHuePerception(data),
      saturationSensitivity: this.assessSaturationSensitivity(data),
      brightnessDiscrimination: this.assessBrightnessDiscrimination(data),
      colorConstancy: this.assessColorConstancy(data),
      perceptualAccuracy: this.calculatePerceptualAccuracy(data),
      colorMemory: this.assessColorMemory(data),
      adaptationEfficiency: this.calculateAdaptationEfficiency(data),
      colorConfusionMatrix: this.generateColorConfusionMatrix(data),
      dominantColorPreferences: this.identifyColorPreferences(data)
    };
  }
  assessColorDiscrimination(data) {
    const colorResponses = data.colorData.interactions || [];
    const correctColorMatches = colorResponses.filter((response) => response.correct).length;
    const totalColorAttempts = colorResponses.length;
    if (totalColorAttempts === 0) return 0.7;
    let discriminationScore = correctColorMatches / totalColorAttempts;
    const difficultColors = this.identifyDifficultColors(colorResponses);
    if (difficultColors.length > 0) {
      const difficultAccuracy = this.calculateDifficultColorAccuracy(colorResponses, difficultColors);
      discriminationScore = discriminationScore * 0.7 + difficultAccuracy * 0.3;
    }
    const systematicErrors = this.identifySystematicColorErrors(colorResponses);
    if (systematicErrors.length > 0) {
      discriminationScore *= 1 - systematicErrors.length * 0.1;
    }
    return Math.max(0, Math.min(1, discriminationScore));
  }
  assessHuePerception(data) {
    const colorResponses = data.colorData.interactions || [];
    const hueErrors = [];
    colorResponses.forEach((response) => {
      if (!response.correct && response.targetColor && response.selectedColor) {
        const hueDistance = this.calculateHueDistance(response.targetColor, response.selectedColor);
        hueErrors.push(hueDistance);
      }
    });
    if (hueErrors.length === 0) return 0.9;
    const averageHueError = hueErrors.reduce((sum, error) => sum + error, 0) / hueErrors.length;
    const maxHueError = 180;
    const hueAccuracy = 1 - averageHueError / maxHueError;
    const consistencyBonus = this.calculateHueConsistency(colorResponses) * 0.1;
    return Math.max(0, Math.min(1, hueAccuracy + consistencyBonus));
  }
  assessSaturationSensitivity(data) {
    const colorResponses = data.colorData.interactions || [];
    const saturationTests = colorResponses.filter(
      (response) => response.targetColor && response.selectedColor
    );
    if (saturationTests.length === 0) return 0.7;
    let saturationScore = 0;
    let saturationTestCount = 0;
    saturationTests.forEach((response) => {
      const targetSaturation = this.extractSaturation(response.targetColor);
      const selectedSaturation = this.extractSaturation(response.selectedColor);
      if (targetSaturation !== null && selectedSaturation !== null) {
        const saturationDifference = Math.abs(targetSaturation - selectedSaturation);
        const sensitivity = 1 - saturationDifference / 100;
        saturationScore += Math.max(0, sensitivity);
        saturationTestCount++;
      }
    });
    return saturationTestCount > 0 ? saturationScore / saturationTestCount : 0.7;
  }
  assessBrightnessDiscrimination(data) {
    const colorResponses = data.colorData.interactions || [];
    const brightnessTests = colorResponses.filter(
      (response) => response.targetColor && response.selectedColor
    );
    if (brightnessTests.length === 0) return 0.7;
    let brightnessScore = 0;
    let brightnessTestCount = 0;
    brightnessTests.forEach((response) => {
      const targetBrightness = this.extractBrightness(response.targetColor);
      const selectedBrightness = this.extractBrightness(response.selectedColor);
      if (targetBrightness !== null && selectedBrightness !== null) {
        const brightnessDifference = Math.abs(targetBrightness - selectedBrightness);
        const discrimination = 1 - brightnessDifference / 100;
        brightnessScore += Math.max(0, discrimination);
        brightnessTestCount++;
      }
    });
    return brightnessTestCount > 0 ? brightnessScore / brightnessTestCount : 0.7;
  }
  assessColorConstancy(data) {
    const colorResponses = data.colorData.interactions || [];
    const sessionDuration = data.sessionDuration || 0;
    const timeSegments = this.divideResponsesByTime(colorResponses, 3);
    if (timeSegments.length < 2) return 0.7;
    const segmentAccuracies = timeSegments.map((segment) => {
      const correct = segment.filter((r) => r.correct).length;
      return segment.length > 0 ? correct / segment.length : 0;
    });
    const meanAccuracy = segmentAccuracies.reduce((sum, acc) => sum + acc, 0) / segmentAccuracies.length;
    const variance = segmentAccuracies.reduce((sum, acc) => sum + Math.pow(acc - meanAccuracy, 2), 0) / segmentAccuracies.length;
    const constancyScore = 1 - Math.sqrt(variance);
    const durationFactor = Math.min(1, sessionDuration / 3e5);
    return Math.max(0, Math.min(1, constancyScore * (0.7 + 0.3 * durationFactor)));
  }
  calculatePerceptualAccuracy(data) {
    const colorResponses = data.colorData.interactions || [];
    if (colorResponses.length === 0) return 0.7;
    let weightedAccuracy = 0;
    let totalWeight = 0;
    colorResponses.forEach((response) => {
      const colorDifficulty = this.assessColorDifficulty(response.targetColor);
      const weight = colorDifficulty + 0.5;
      weightedAccuracy += (response.correct ? 1 : 0) * weight;
      totalWeight += weight;
    });
    const baseAccuracy = totalWeight > 0 ? weightedAccuracy / totalWeight : 0.7;
    const avgResponseTime = this.calculateAverageResponseTime(colorResponses);
    const speedBonus = avgResponseTime < 2e3 ? 0.1 : avgResponseTime < 4e3 ? 0.05 : 0;
    return Math.max(0, Math.min(1, baseAccuracy + speedBonus));
  }
  assessColorMemory(data) {
    const colorResponses = data.colorData.interactions || [];
    const repeatColorTests = this.identifyRepeatColorTests(colorResponses);
    if (repeatColorTests.length === 0) return 0.7;
    let memoryScore = 0;
    let memoryTestCount = 0;
    repeatColorTests.forEach((test) => {
      const firstAttempt = test.attempts[0];
      const laterAttempts = test.attempts.slice(1);
      if (laterAttempts.length > 0) {
        const firstAccuracy = firstAttempt.correct ? 1 : 0;
        const laterAccuracy = laterAttempts.filter((a) => a.correct).length / laterAttempts.length;
        const improvement = laterAccuracy - firstAccuracy;
        const memoryIndicator = Math.max(0, 0.5 + improvement);
        memoryScore += memoryIndicator;
        memoryTestCount++;
      }
    });
    return memoryTestCount > 0 ? memoryScore / memoryTestCount : 0.7;
  }
  calculateAdaptationEfficiency(data) {
    const colorResponses = data.colorData.interactions || [];
    if (colorResponses.length < 5) return 0.7;
    const learningCurve = this.calculateLearningCurve(colorResponses);
    const initialPerformance = learningCurve.slice(0, 3).reduce((sum, val) => sum + val, 0) / 3;
    const finalPerformance = learningCurve.slice(-3).reduce((sum, val) => sum + val, 0) / 3;
    const adaptationRate = finalPerformance - initialPerformance;
    const adaptationScore = 0.5 + adaptationRate * 0.5;
    return Math.max(0, Math.min(1, adaptationScore));
  }
  generateColorConfusionMatrix(data) {
    const colorResponses = data.colorData.interactions || [];
    const confusionMatrix = {};
    colorResponses.forEach((response) => {
      if (!response.correct && response.targetColor && response.selectedColor) {
        const target = response.targetColor;
        const selected = response.selectedColor;
        if (!confusionMatrix[target]) {
          confusionMatrix[target] = {};
        }
        confusionMatrix[target][selected] = (confusionMatrix[target][selected] || 0) + 1;
      }
    });
    return confusionMatrix;
  }
  identifyColorPreferences(data) {
    const colorResponses = data.colorData.interactions || [];
    const colorCounts = {};
    colorResponses.forEach((response) => {
      if (response.selectedColor) {
        colorCounts[response.selectedColor] = (colorCounts[response.selectedColor] || 0) + 1;
      }
    });
    const sortedColors = Object.entries(colorCounts).sort((a, b) => b[1] - a[1]).slice(0, 5).map(([color, count]) => ({ color, frequency: count }));
    return sortedColors;
  }
  // Métodos auxiliares
  calculateHueDistance(color1, color2) {
    const colorMap = {
      "vermelho": 0,
      "laranja": 30,
      "amarelo": 60,
      "verde": 120,
      "azul": 240,
      "roxo": 270,
      "rosa": 330,
      "marrom": 30
    };
    const hue1 = colorMap[color1] || 0;
    const hue2 = colorMap[color2] || 0;
    const distance = Math.abs(hue1 - hue2);
    return Math.min(distance, 360 - distance);
  }
  calculateHueConsistency(responses) {
    const hueErrors = responses.filter((r) => !r.correct).map((r) => this.calculateHueDistance(r.targetColor, r.selectedColor));
    if (hueErrors.length === 0) return 1;
    const variance = this.calculateVariance(hueErrors);
    return Math.max(0, 1 - variance / 100);
  }
  extractSaturation(color) {
    const saturationMap = {
      "vermelho": 90,
      "azul": 85,
      "verde": 80,
      "amarelo": 95,
      "laranja": 88,
      "roxo": 75,
      "rosa": 60,
      "marrom": 40
    };
    return saturationMap[color] || 70;
  }
  extractBrightness(color) {
    const brightnessMap = {
      "amarelo": 90,
      "laranja": 75,
      "vermelho": 60,
      "verde": 55,
      "azul": 45,
      "roxo": 40,
      "marrom": 30,
      "rosa": 80
    };
    return brightnessMap[color] || 50;
  }
  divideResponsesByTime(responses, segments) {
    const sortedResponses = responses.sort(
      (a, b) => new Date(a.timestamp) - new Date(b.timestamp)
    );
    const segmentSize = Math.ceil(sortedResponses.length / segments);
    const timeSegments = [];
    for (let i = 0; i < segments; i++) {
      const start = i * segmentSize;
      const end = Math.min(start + segmentSize, sortedResponses.length);
      timeSegments.push(sortedResponses.slice(start, end));
    }
    return timeSegments;
  }
  assessColorDifficulty(color) {
    const difficultyMap = {
      "vermelho": 0.3,
      "azul": 0.4,
      "amarelo": 0.2,
      "verde": 0.5,
      "laranja": 0.6,
      "roxo": 0.8,
      "rosa": 0.7,
      "marrom": 0.9
    };
    return difficultyMap[color] || 0.5;
  }
  calculateAverageResponseTime(responses) {
    const times = responses.filter((r) => r.responseTime).map((r) => r.responseTime);
    return times.length > 0 ? times.reduce((sum, time) => sum + time, 0) / times.length : 3e3;
  }
  identifyRepeatColorTests(responses) {
    const colorTests = {};
    responses.forEach((response, index) => {
      const color = response.targetColor;
      if (!colorTests[color]) {
        colorTests[color] = { attempts: [] };
      }
      colorTests[color].attempts.push({ ...response, index });
    });
    return Object.values(colorTests).filter((test) => test.attempts.length > 1);
  }
  calculateLearningCurve(responses) {
    const windowSize = 3;
    const curve = [];
    for (let i = 0; i <= responses.length - windowSize; i++) {
      const window2 = responses.slice(i, i + windowSize);
      const accuracy = window2.filter((r) => r.correct).length / window2.length;
      curve.push(accuracy);
    }
    return curve;
  }
  calculateVariance(values) {
    if (values.length === 0) return 0;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return variance;
  }
  identifyDifficultColors(responses) {
    const colorAccuracy = {};
    responses.forEach((response) => {
      const color = response.targetColor;
      if (!colorAccuracy[color]) {
        colorAccuracy[color] = { correct: 0, total: 0 };
      }
      colorAccuracy[color].total++;
      if (response.correct) {
        colorAccuracy[color].correct++;
      }
    });
    return Object.entries(colorAccuracy).filter(([color, stats]) => stats.total >= 2 && stats.correct / stats.total < 0.6).map(([color]) => color);
  }
  calculateDifficultColorAccuracy(responses, difficultColors) {
    const difficultResponses = responses.filter((r) => difficultColors.includes(r.targetColor));
    if (difficultResponses.length === 0) return 0.5;
    const correct = difficultResponses.filter((r) => r.correct).length;
    return correct / difficultResponses.length;
  }
  identifySystematicColorErrors(responses) {
    const confusionPairs = {};
    responses.forEach((response) => {
      if (!response.correct && response.targetColor && response.selectedColor) {
        const pair = `${response.targetColor}->${response.selectedColor}`;
        confusionPairs[pair] = (confusionPairs[pair] || 0) + 1;
      }
    });
    return Object.entries(confusionPairs).filter(([pair, count]) => count >= 3).map(([pair]) => pair);
  }
  generateColorInsights(results) {
    const insights = [];
    if (results.colorDiscrimination < 0.6) {
      insights.push("Dificuldades evidentes na discriminação de cores");
    }
    if (results.huePerception < 0.5) {
      insights.push("Limitações na percepção de matizes");
    }
    if (results.saturationSensitivity < 0.6) {
      insights.push("Baixa sensibilidade à saturação das cores");
    }
    if (results.brightnessDiscrimination < 0.6) {
      insights.push("Dificuldades na discriminação de brilho");
    }
    if (results.colorConstancy < 0.5) {
      insights.push("Problemas de constância perceptual de cores");
    }
    if (results.colorMemory < 0.6) {
      insights.push("Limitações na memória para cores");
    }
    if (Object.keys(results.colorConfusionMatrix || {}).length > 3) {
      insights.push("Padrões significativos de confusão entre cores identificados");
    }
    return insights;
  }
}
class VisualProcessingCollector {
  constructor() {
    this.processingStages = {
      detection: "detecção visual inicial",
      recognition: "reconhecimento de padrões",
      categorization: "categorização cognitiva",
      decision: "tomada de decisão"
    };
    this.speedThresholds = {
      veryFast: 500,
      // < 500ms
      fast: 1e3,
      // 500-1000ms
      normal: 2e3,
      // 1-2s
      slow: 4e3,
      // 2-4s
      verySlow: 8e3
      // > 4s
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de processamento visual
   */
  collect(data) {
    return this.analyze(data);
  }
  async analyze(data) {
    if (!data || !data.visualData) {
      console.warn("VisualProcessingCollector: Dados inválidos recebidos", data);
      return {
        processingSpeed: 0.6,
        visualScanning: 0.6,
        patternRecognition: 0.6,
        visualSearch: 0.6,
        figureGroundSeparation: 0.6,
        visualSpan: 0.6,
        processingEfficiency: 0.6,
        visualIntegration: 0.6
      };
    }
    return {
      processingSpeed: this.assessProcessingSpeed(data),
      visualScanning: this.assessVisualScanning(data),
      patternRecognition: this.assessPatternRecognition(data),
      visualSearch: this.assessVisualSearch(data),
      figureGroundSeparation: this.assessFigureGroundSeparation(data),
      visualSpan: this.calculateVisualSpan(data),
      processingEfficiency: this.calculateProcessingEfficiency(data),
      visualIntegration: this.assessVisualIntegration(data),
      scanningPatterns: this.analyzeScanningPatterns(data),
      processingBottlenecks: this.identifyProcessingBottlenecks(data)
    };
  }
  assessProcessingSpeed(data) {
    const interactions = data.visualData.interactions || [];
    if (interactions.length === 0) return 0.6;
    const responseTimes = interactions.filter((i) => i.responseTime && i.responseTime > 0).map((i) => i.responseTime);
    if (responseTimes.length === 0) return 0.6;
    const avgResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    let speedScore = 1;
    if (avgResponseTime <= this.speedThresholds.veryFast) {
      speedScore = 1;
    } else if (avgResponseTime <= this.speedThresholds.fast) {
      speedScore = 0.9;
    } else if (avgResponseTime <= this.speedThresholds.normal) {
      speedScore = 0.7;
    } else if (avgResponseTime <= this.speedThresholds.slow) {
      speedScore = 0.5;
    } else {
      speedScore = 0.3;
    }
    const consistencyBonus = this.calculateSpeedConsistency(responseTimes) * 0.1;
    return Math.max(0, Math.min(1, speedScore + consistencyBonus));
  }
  assessVisualScanning(data) {
    const interactions = data.visualData.interactions || [];
    const scanningData = data.visualData.scanningPatterns || [];
    if (scanningData.length === 0) {
      return this.estimateScanningFromResponses(interactions);
    }
    let scanningScore = 0;
    const scanningEfficiency = this.calculateScanningEfficiency(scanningData);
    scanningScore += scanningEfficiency * 0.4;
    const scanningSystematicity = this.calculateScanningSystematicity(scanningData);
    scanningScore += scanningSystematicity * 0.3;
    const scanningAdaptability = this.calculateScanningAdaptability(scanningData);
    scanningScore += scanningAdaptability * 0.3;
    return Math.max(0, Math.min(1, scanningScore));
  }
  assessPatternRecognition(data) {
    const interactions = data.visualData.interactions || [];
    const colorPatterns = this.extractColorPatterns(interactions);
    if (colorPatterns.length === 0) return 0.6;
    let recognitionScore = 0;
    let totalPatterns = 0;
    colorPatterns.forEach((pattern) => {
      const patternAccuracy = pattern.correctResponses / pattern.totalResponses;
      const patternComplexity = this.assessPatternComplexity(pattern);
      const weightedAccuracy = patternAccuracy * (1 + patternComplexity * 0.5);
      recognitionScore += weightedAccuracy;
      totalPatterns++;
    });
    const baseRecognition = totalPatterns > 0 ? recognitionScore / totalPatterns : 0.6;
    const speedBonus = this.calculatePatternSpeedBonus(interactions);
    return Math.max(0, Math.min(1, baseRecognition + speedBonus));
  }
  assessVisualSearch(data) {
    const interactions = data.visualData.interactions || [];
    if (interactions.length === 0) return 0.6;
    const searchStrategy = this.identifySearchStrategy(interactions);
    const searchEfficiency = this.calculateSearchEfficiency(interactions);
    const targetDetectionAccuracy = this.calculateTargetDetectionAccuracy(interactions);
    const searchScore = searchStrategy.efficiency * 0.3 + searchEfficiency * 0.4 + targetDetectionAccuracy * 0.3;
    return Math.max(0, Math.min(1, searchScore));
  }
  assessFigureGroundSeparation(data) {
    const interactions = data.visualData.interactions || [];
    const complexity = data.visualData.backgroundComplexity || "low";
    if (interactions.length === 0) return 0.6;
    const separationAccuracy = this.calculateSeparationAccuracy(interactions, complexity);
    const separationSpeed = this.calculateSeparationSpeed(interactions, complexity);
    const complexityMultiplier = this.getComplexityMultiplier(complexity);
    const separationScore = (separationAccuracy * 0.6 + separationSpeed * 0.4) * complexityMultiplier;
    return Math.max(0, Math.min(1, separationScore));
  }
  calculateVisualSpan(data) {
    const interactions = data.visualData.interactions || [];
    const simultaneousElements = this.calculateSimultaneousProcessing(interactions);
    const maxSpan = Math.max(...simultaneousElements, 1);
    const normalizedSpan = Math.min(maxSpan / 7, 1);
    const multiElementAccuracy = this.calculateMultiElementAccuracy(interactions);
    return normalizedSpan * multiElementAccuracy;
  }
  calculateProcessingEfficiency(data) {
    const interactions = data.visualData.interactions || [];
    if (interactions.length === 0) return 0.6;
    const totalAccuracy = interactions.filter((i) => i.correct).length / interactions.length;
    const avgResponseTime = this.calculateAverageResponseTime(interactions);
    const normalizedTime = Math.min(avgResponseTime / 2e3, 2);
    const efficiency = totalAccuracy / normalizedTime;
    return Math.max(0, Math.min(1, efficiency));
  }
  assessVisualIntegration(data) {
    const interactions = data.visualData.interactions || [];
    if (interactions.length === 0) return 0.6;
    const integrationTasks = this.identifyIntegrationTasks(interactions);
    if (integrationTasks.length === 0) return 0.6;
    let integrationScore = 0;
    integrationTasks.forEach((task) => {
      const taskAccuracy = task.correctResponses / task.totalResponses;
      const integrationComplexity = this.assessIntegrationComplexity(task);
      integrationScore += taskAccuracy * (1 + integrationComplexity * 0.3);
    });
    return Math.max(0, Math.min(1, integrationScore / integrationTasks.length));
  }
  analyzeScanningPatterns(data) {
    const interactions = data.visualData.interactions || [];
    const patterns = {
      systematic: this.identifySystematicScanning(interactions),
      random: this.identifyRandomScanning(interactions),
      guided: this.identifyGuidedScanning(interactions),
      adaptive: this.identifyAdaptiveScanning(interactions)
    };
    const dominantPattern = Object.entries(patterns).sort((a, b) => b[1] - a[1])[0];
    return {
      patterns,
      dominantPattern: dominantPattern[0],
      efficiency: dominantPattern[1]
    };
  }
  identifyProcessingBottlenecks(data) {
    const interactions = data.visualData.interactions || [];
    const bottlenecks = [];
    const slowResponses = interactions.filter(
      (i) => i.responseTime && i.responseTime > this.speedThresholds.slow
    );
    if (slowResponses.length > interactions.length * 0.3) {
      bottlenecks.push({
        type: "speed_bottleneck",
        severity: "high",
        description: "Velocidade de processamento significativamente reduzida"
      });
    }
    const errorPatterns = this.analyzeErrorPatterns(interactions);
    if (errorPatterns.systematicErrors > 0.3) {
      bottlenecks.push({
        type: "recognition_bottleneck",
        severity: "medium",
        description: "Dificuldades consistentes no reconhecimento visual"
      });
    }
    const integrationErrors = this.identifyIntegrationErrors(interactions);
    if (integrationErrors > 0.25) {
      bottlenecks.push({
        type: "integration_bottleneck",
        severity: "medium",
        description: "Dificuldades na integração de informações visuais"
      });
    }
    return bottlenecks;
  }
  // Métodos auxiliares
  calculateSpeedConsistency(responseTimes) {
    if (responseTimes.length < 3) return 0;
    const mean = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const variance = responseTimes.reduce((sum, time) => sum + Math.pow(time - mean, 2), 0) / responseTimes.length;
    const standardDeviation = Math.sqrt(variance);
    const coefficientOfVariation = standardDeviation / mean;
    return Math.max(0, 1 - coefficientOfVariation);
  }
  estimateScanningFromResponses(interactions) {
    if (interactions.length === 0) return 0.6;
    const responseSequence = interactions.map((i, index) => ({
      index,
      position: i.elementPosition || index,
      correct: i.correct,
      responseTime: i.responseTime
    }));
    const positionVariance = this.calculatePositionVariance(responseSequence);
    const systematicity = 1 - Math.min(positionVariance / 10, 1);
    return Math.max(0.3, Math.min(1, systematicity));
  }
  calculateScanningEfficiency(scanningData) {
    const totalMovements = scanningData.length;
    const uniquePositions = new Set(scanningData.map((s) => s.position)).size;
    const redundantMovements = totalMovements - uniquePositions;
    const efficiency = 1 - redundantMovements / totalMovements;
    return Math.max(0, efficiency);
  }
  calculateScanningSystematicity(scanningData) {
    const positions = scanningData.map((s) => s.position);
    if (positions.length < 3) return 0.5;
    let sequentialMoves = 0;
    for (let i = 1; i < positions.length; i++) {
      const distance = Math.abs(positions[i] - positions[i - 1]);
      if (distance <= 2) sequentialMoves++;
    }
    return sequentialMoves / (positions.length - 1);
  }
  calculateScanningAdaptability(scanningData) {
    return 0.7;
  }
  extractColorPatterns(interactions) {
    const patterns = {};
    interactions.forEach((interaction) => {
      const targetColor = interaction.targetColor;
      if (!patterns[targetColor]) {
        patterns[targetColor] = {
          color: targetColor,
          totalResponses: 0,
          correctResponses: 0,
          avgResponseTime: 0,
          responseTimes: []
        };
      }
      patterns[targetColor].totalResponses++;
      if (interaction.correct) {
        patterns[targetColor].correctResponses++;
      }
      if (interaction.responseTime) {
        patterns[targetColor].responseTimes.push(interaction.responseTime);
      }
    });
    Object.values(patterns).forEach((pattern) => {
      if (pattern.responseTimes.length > 0) {
        pattern.avgResponseTime = pattern.responseTimes.reduce((sum, time) => sum + time, 0) / pattern.responseTimes.length;
      }
    });
    return Object.values(patterns);
  }
  assessPatternComplexity(pattern) {
    const complexityMap = {
      "vermelho": 0.2,
      "azul": 0.3,
      "amarelo": 0.1,
      "verde": 0.4,
      "laranja": 0.6,
      "roxo": 0.8,
      "rosa": 0.7,
      "marrom": 0.9
    };
    return complexityMap[pattern.color] || 0.5;
  }
  calculatePatternSpeedBonus(interactions) {
    const patternSpeeds = this.extractColorPatterns(interactions).map((pattern) => pattern.avgResponseTime).filter((time) => time > 0);
    if (patternSpeeds.length === 0) return 0;
    const avgPatternSpeed = patternSpeeds.reduce((sum, speed) => sum + speed, 0) / patternSpeeds.length;
    return avgPatternSpeed < 1500 ? 0.1 : avgPatternSpeed < 2500 ? 0.05 : 0;
  }
  identifySearchStrategy(interactions) {
    const strategies = {
      systematic: 0,
      random: 0,
      targeted: 0
    };
    const correctOnFirst = interactions.filter((i, idx) => i.correct && idx < 3).length;
    const totalFirst = Math.min(3, interactions.length);
    if (totalFirst > 0) {
      const earlyAccuracy = correctOnFirst / totalFirst;
      if (earlyAccuracy > 0.8) {
        strategies.targeted = 0.9;
      } else if (earlyAccuracy > 0.5) {
        strategies.systematic = 0.7;
      } else {
        strategies.random = 0.6;
      }
    }
    const dominantStrategy = Object.entries(strategies).sort((a, b) => b[1] - a[1])[0];
    return {
      strategy: dominantStrategy[0],
      efficiency: dominantStrategy[1]
    };
  }
  calculateSearchEfficiency(interactions) {
    if (interactions.length === 0) return 0.6;
    const successfulSearches = interactions.filter((i) => i.correct);
    const avgSearchTime = successfulSearches.length > 0 ? successfulSearches.reduce((sum, i) => sum + (i.responseTime || 2e3), 0) / successfulSearches.length : 2e3;
    const efficiency = Math.max(0, 1 - (avgSearchTime - 1e3) / 3e3);
    return Math.min(1, efficiency);
  }
  calculateTargetDetectionAccuracy(interactions) {
    if (interactions.length === 0) return 0.6;
    const correct = interactions.filter((i) => i.correct).length;
    return correct / interactions.length;
  }
  calculateSeparationAccuracy(interactions, complexity) {
    const baseAccuracy = interactions.filter((i) => i.correct).length / interactions.length;
    const complexityPenalty = complexity === "high" ? 0.2 : complexity === "medium" ? 0.1 : 0;
    return Math.max(0, baseAccuracy - complexityPenalty);
  }
  calculateSeparationSpeed(interactions, complexity) {
    const avgTime = this.calculateAverageResponseTime(interactions);
    const idealTime = complexity === "high" ? 3e3 : complexity === "medium" ? 2e3 : 1500;
    return Math.max(0, 1 - Math.abs(avgTime - idealTime) / idealTime);
  }
  getComplexityMultiplier(complexity) {
    const multipliers = {
      "low": 1,
      "medium": 1.1,
      "high": 1.2
    };
    return multipliers[complexity] || 1;
  }
  calculateSimultaneousProcessing(interactions) {
    const groupedInteractions = this.groupInteractionsByTime(interactions, 1e3);
    return groupedInteractions.map((group) => group.length);
  }
  calculateMultiElementAccuracy(interactions) {
    const multiElementGroups = this.groupInteractionsByTime(interactions, 1e3).filter((group) => group.length > 1);
    if (multiElementGroups.length === 0) return 1;
    const accuracies = multiElementGroups.map((group) => {
      const correct = group.filter((i) => i.correct).length;
      return correct / group.length;
    });
    return accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
  }
  calculateAverageResponseTime(interactions) {
    const times = interactions.filter((i) => i.responseTime && i.responseTime > 0).map((i) => i.responseTime);
    return times.length > 0 ? times.reduce((sum, time) => sum + time, 0) / times.length : 2e3;
  }
  identifyIntegrationTasks(interactions) {
    return [{
      type: "color_integration",
      totalResponses: interactions.length,
      correctResponses: interactions.filter((i) => i.correct).length
    }];
  }
  assessIntegrationComplexity(task) {
    const complexityMap = {
      "color_integration": 0.5,
      "shape_integration": 0.7,
      "spatial_integration": 0.8
    };
    return complexityMap[task.type] || 0.5;
  }
  identifySystematicScanning(interactions) {
    return 0.7;
  }
  identifyRandomScanning(interactions) {
    return 0.3;
  }
  identifyGuidedScanning(interactions) {
    return 0.5;
  }
  identifyAdaptiveScanning(interactions) {
    return 0.6;
  }
  analyzeErrorPatterns(interactions) {
    const totalErrors = interactions.filter((i) => !i.correct).length;
    const totalInteractions = interactions.length;
    return {
      systematicErrors: totalInteractions > 0 ? totalErrors / totalInteractions : 0,
      errorTypes: this.categorizeErrors(interactions)
    };
  }
  categorizeErrors(interactions) {
    const errorTypes = {
      speed: 0,
      // Erros por velocidade excessiva
      accuracy: 0,
      // Erros por falta de precisão
      attention: 0
      // Erros por desatenção
    };
    interactions.forEach((interaction) => {
      if (!interaction.correct) {
        if (interaction.responseTime && interaction.responseTime < 1e3) {
          errorTypes.speed++;
        } else if (interaction.responseTime && interaction.responseTime > 4e3) {
          errorTypes.accuracy++;
        } else {
          errorTypes.attention++;
        }
      }
    });
    return errorTypes;
  }
  identifyIntegrationErrors(interactions) {
    const integrationErrors = interactions.filter(
      (i) => !i.correct && i.complexity && i.complexity === "high"
    ).length;
    return interactions.length > 0 ? integrationErrors / interactions.length : 0;
  }
  calculatePositionVariance(responseSequence) {
    const positions = responseSequence.map((r) => r.position);
    if (positions.length < 2) return 0;
    const mean = positions.reduce((sum, pos) => sum + pos, 0) / positions.length;
    const variance = positions.reduce((sum, pos) => sum + Math.pow(pos - mean, 2), 0) / positions.length;
    return variance;
  }
  groupInteractionsByTime(interactions, timeWindow) {
    const groups = [];
    let currentGroup = [];
    let lastTimestamp = null;
    interactions.forEach((interaction) => {
      const timestamp = new Date(interaction.timestamp).getTime();
      if (lastTimestamp === null || timestamp - lastTimestamp <= timeWindow) {
        currentGroup.push(interaction);
      } else {
        if (currentGroup.length > 0) {
          groups.push(currentGroup);
        }
        currentGroup = [interaction];
      }
      lastTimestamp = timestamp;
    });
    if (currentGroup.length > 0) {
      groups.push(currentGroup);
    }
    return groups;
  }
  generateProcessingInsights(results) {
    const insights = [];
    if (results.processingSpeed < 0.5) {
      insights.push("Velocidade de processamento visual reduzida");
    }
    if (results.visualScanning < 0.6) {
      insights.push("Padrões de varredura visual ineficientes");
    }
    if (results.patternRecognition < 0.6) {
      insights.push("Dificuldades no reconhecimento de padrões visuais");
    }
    if (results.visualSearch < 0.5) {
      insights.push("Estratégias de busca visual subótimas");
    }
    if (results.figureGroundSeparation < 0.6) {
      insights.push("Limitações na separação figura-fundo");
    }
    if (results.visualSpan < 0.5) {
      insights.push("Span visual reduzido");
    }
    if (results.processingEfficiency < 0.6) {
      insights.push("Eficiência geral do processamento visual comprometida");
    }
    if (results.processingBottlenecks && results.processingBottlenecks.length > 0) {
      insights.push("Gargalos específicos no processamento visual identificados");
    }
    return insights;
  }
}
class AttentionalSelectivityCollector {
  constructor() {
    this.attentionTypes = {
      focused: "atenção focada",
      selective: "atenção seletiva",
      divided: "atenção dividida",
      sustained: "atenção sustentada",
      alternating: "atenção alternada"
    };
    this.selectivityLevels = {
      excellent: { min: 0.9, label: "excelente" },
      good: { min: 0.75, label: "boa" },
      average: { min: 0.6, label: "média" },
      poor: { min: 0.4, label: "baixa" },
      critical: { min: 0, label: "crítica" }
    };
    this.distractorTypes = {
      visual: "distratores visuais",
      color: "distratores de cor",
      spatial: "distratores espaciais",
      temporal: "distratores temporais"
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de atenção seletiva
   */
  collect(data) {
    return this.analyze(data);
  }
  async analyze(data) {
    if (!data || !data.attentionData) {
      console.warn("AttentionalSelectivityCollector: Dados inválidos recebidos", data);
      return {
        selectiveAttention: 0.7,
        focusedAttention: 0.7,
        inhibitoryControl: 0.7,
        distractorResistance: 0.7,
        attentionalFlexibility: 0.7,
        vigilance: 0.7,
        executiveAttention: 0.7,
        attentionalCapacity: 0.7
      };
    }
    return {
      selectiveAttention: this.assessSelectiveAttention(data),
      focusedAttention: this.assessFocusedAttention(data),
      inhibitoryControl: this.assessInhibitoryControl(data),
      distractorResistance: this.assessDistractorResistance(data),
      attentionalFlexibility: this.assessAttentionalFlexibility(data),
      vigilance: this.assessVigilance(data),
      executiveAttention: this.assessExecutiveAttention(data),
      attentionalCapacity: this.calculateAttentionalCapacity(data),
      attentionProfile: this.generateAttentionProfile(data),
      filteringEfficiency: this.calculateFilteringEfficiency(data)
    };
  }
  assessSelectiveAttention(data) {
    const interactions = data.attentionData.interactions || [];
    if (interactions.length === 0) return 0.7;
    const targetedInteractions = interactions.filter((i) => i.isTarget);
    const nonTargetedInteractions = interactions.filter((i) => !i.isTarget);
    const targetAccuracy = targetedInteractions.length > 0 ? targetedInteractions.filter((i) => i.correct).length / targetedInteractions.length : 0.5;
    const nonTargetRejection = nonTargetedInteractions.length > 0 ? nonTargetedInteractions.filter((i) => !i.selected).length / nonTargetedInteractions.length : 0.8;
    const selectivityScore = targetAccuracy * 0.6 + nonTargetRejection * 0.4;
    const selectionSpeed = this.calculateSelectionSpeed(targetedInteractions);
    const speedBonus = selectionSpeed > 0.8 ? 0.1 : selectionSpeed > 0.6 ? 0.05 : 0;
    return Math.max(0, Math.min(1, selectivityScore + speedBonus));
  }
  assessFocusedAttention(data) {
    const interactions = data.attentionData.interactions || [];
    const sessionDuration = data.sessionDuration || 0;
    if (interactions.length === 0) return 0.7;
    const focusWindows = this.divideIntoFocusWindows(interactions, 3e4);
    let focusScore = 0;
    focusWindows.forEach((window2) => {
      const windowAccuracy = window2.filter((i) => i.correct).length / window2.length;
      const windowConsistency = this.calculateWindowConsistency(window2);
      const windowFocus = windowAccuracy * 0.7 + windowConsistency * 0.3;
      focusScore += windowFocus;
    });
    const avgFocusScore = focusWindows.length > 0 ? focusScore / focusWindows.length : 0.7;
    const durationFactor = Math.min(1, sessionDuration / 3e5);
    const focusStability = this.calculateFocusStability(focusWindows);
    return Math.max(0, Math.min(1, avgFocusScore * (0.8 + 0.2 * durationFactor) * focusStability));
  }
  assessInhibitoryControl(data) {
    const interactions = data.attentionData.interactions || [];
    const inhibitionTasks = interactions.filter((i) => i.requiresInhibition);
    if (inhibitionTasks.length === 0) {
      return this.estimateInhibitoryControl(interactions);
    }
    const inhibitionAccuracy = inhibitionTasks.filter((i) => i.correct).length / inhibitionTasks.length;
    const inhibitionSpeed = this.calculateInhibitionSpeed(inhibitionTasks);
    const interferenceResistance = this.calculateInterferenceResistance(interactions);
    const inhibitoryScore = inhibitionAccuracy * 0.4 + inhibitionSpeed * 0.3 + interferenceResistance * 0.3;
    return Math.max(0, Math.min(1, inhibitoryScore));
  }
  assessDistractorResistance(data) {
    const interactions = data.attentionData.interactions || [];
    const distractorData = data.attentionData.distractors || [];
    if (distractorData.length === 0) {
      return this.estimateDistractorResistance(interactions);
    }
    let resistanceScore = 0;
    let resistanceTests = 0;
    Object.keys(this.distractorTypes).forEach((type) => {
      const typeDistractors = distractorData.filter((d) => d.type === type);
      if (typeDistractors.length > 0) {
        const typeResistance = this.calculateTypeSpecificResistance(typeDistractors, interactions);
        resistanceScore += typeResistance;
        resistanceTests++;
      }
    });
    const avgResistance = resistanceTests > 0 ? resistanceScore / resistanceTests : 0.7;
    const consistencyBonus = this.calculateResistanceConsistency(distractorData, interactions) * 0.1;
    return Math.max(0, Math.min(1, avgResistance + consistencyBonus));
  }
  assessAttentionalFlexibility(data) {
    const interactions = data.attentionData.interactions || [];
    const contextChanges = data.attentionData.contextChanges || [];
    if (contextChanges.length === 0) {
      return this.estimateFlexibilityFromPatterns(interactions);
    }
    let flexibilityScore = 0;
    contextChanges.forEach((change) => {
      const preChangePerformance = this.getPerformanceBeforeChange(interactions, change);
      const postChangePerformance = this.getPerformanceAfterChange(interactions, change);
      const adaptationSpeed = this.calculateAdaptationSpeed(preChangePerformance, postChangePerformance);
      const adaptationEffectiveness = this.calculateAdaptationEffectiveness(postChangePerformance);
      const changeFlexibility = adaptationSpeed * 0.6 + adaptationEffectiveness * 0.4;
      flexibilityScore += changeFlexibility;
    });
    return Math.max(0, Math.min(1, flexibilityScore / contextChanges.length));
  }
  assessVigilance(data) {
    const interactions = data.attentionData.interactions || [];
    const sessionDuration = data.sessionDuration || 0;
    if (interactions.length === 0 || sessionDuration < 6e4) return 0.7;
    const timeSegments = this.divideIntoTimeSegments(interactions, sessionDuration, 5);
    if (timeSegments.length < 3) return 0.7;
    const segmentPerformances = timeSegments.map((segment) => {
      const accuracy = segment.filter((i) => i.correct).length / segment.length;
      const avgResponseTime = this.calculateAverageResponseTime(segment);
      return {
        accuracy,
        responseTime: avgResponseTime,
        alertness: accuracy * (1 - Math.min(avgResponseTime / 4e3, 1))
        // Penalizar tempos muito altos
      };
    });
    const vigilanceDecrement = this.calculateVigilanceDecrement(segmentPerformances);
    const averageVigilance = segmentPerformances.reduce((sum, perf) => sum + perf.alertness, 0) / segmentPerformances.length;
    const vigilanceScore = averageVigilance * (1 - vigilanceDecrement);
    return Math.max(0, Math.min(1, vigilanceScore));
  }
  assessExecutiveAttention(data) {
    const interactions = data.attentionData.interactions || [];
    if (interactions.length === 0) return 0.7;
    const conflictResolution = this.assessConflictResolution(interactions);
    const topDownControl = this.assessTopDownControl(interactions);
    const cognitiveFlexibility = this.assessCognitiveFlexibility(interactions);
    const executiveScore = conflictResolution * 0.4 + topDownControl * 0.3 + cognitiveFlexibility * 0.3;
    return Math.max(0, Math.min(1, executiveScore));
  }
  calculateAttentionalCapacity(data) {
    const interactions = data.attentionData.interactions || [];
    if (interactions.length === 0) return 0.7;
    const simultaneousProcessing = this.calculateSimultaneousProcessing(interactions);
    const multitaskingAbility = this.calculateMultitaskingAbility(interactions);
    const workingMemoryCapacity = this.estimateWorkingMemoryCapacity(interactions);
    const capacityScore = simultaneousProcessing * 0.4 + multitaskingAbility * 0.3 + workingMemoryCapacity * 0.3;
    return Math.max(0, Math.min(1, capacityScore));
  }
  generateAttentionProfile(data) {
    const results = {
      selectiveAttention: this.assessSelectiveAttention(data),
      focusedAttention: this.assessFocusedAttention(data),
      inhibitoryControl: this.assessInhibitoryControl(data),
      distractorResistance: this.assessDistractorResistance(data),
      attentionalFlexibility: this.assessAttentionalFlexibility(data)
    };
    const strengths = Object.entries(results).filter(([key, value]) => value > 0.75).map(([key]) => key);
    const weaknesses = Object.entries(results).filter(([key, value]) => value < 0.5).map(([key]) => key);
    const dominantAttention = Object.entries(results).sort((a, b) => b[1] - a[1])[0];
    return {
      dominantType: dominantAttention[0],
      dominantScore: dominantAttention[1],
      strengths,
      weaknesses,
      overallProfile: this.classifyAttentionProfile(results)
    };
  }
  calculateFilteringEfficiency(data) {
    const interactions = data.attentionData.interactions || [];
    if (interactions.length === 0) return 0.7;
    const relevantProcessing = interactions.filter((i) => i.isRelevant).length;
    const irrelevantProcessing = interactions.filter((i) => !i.isRelevant && i.processed).length;
    const totalRelevant = interactions.filter((i) => i.isRelevant).length;
    const totalIrrelevant = interactions.filter((i) => !i.isRelevant).length;
    const relevantEfficiency = totalRelevant > 0 ? relevantProcessing / totalRelevant : 1;
    const filteringEfficiency = totalIrrelevant > 0 ? 1 - irrelevantProcessing / totalIrrelevant : 1;
    return relevantEfficiency * 0.6 + filteringEfficiency * 0.4;
  }
  // Métodos auxiliares
  calculateSelectionSpeed(interactions) {
    if (interactions.length === 0) return 0.5;
    const selectionTimes = interactions.filter((i) => i.responseTime && i.correct).map((i) => i.responseTime);
    if (selectionTimes.length === 0) return 0.5;
    const avgTime = selectionTimes.reduce((sum, time) => sum + time, 0) / selectionTimes.length;
    const speedScore = Math.max(0, 1 - (avgTime - 1e3) / 3e3);
    return Math.min(1, speedScore);
  }
  divideIntoFocusWindows(interactions, windowSize) {
    const windows = [];
    let currentWindow = [];
    let windowStart = null;
    interactions.forEach((interaction) => {
      const timestamp = new Date(interaction.timestamp).getTime();
      if (windowStart === null) {
        windowStart = timestamp;
        currentWindow = [interaction];
      } else if (timestamp - windowStart <= windowSize) {
        currentWindow.push(interaction);
      } else {
        if (currentWindow.length > 0) {
          windows.push(currentWindow);
        }
        windowStart = timestamp;
        currentWindow = [interaction];
      }
    });
    if (currentWindow.length > 0) {
      windows.push(currentWindow);
    }
    return windows;
  }
  calculateWindowConsistency(window2) {
    if (window2.length < 2) return 1;
    const accuracies = window2.map((i) => i.correct ? 1 : 0);
    const mean = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    const variance = accuracies.reduce((sum, acc) => sum + Math.pow(acc - mean, 2), 0) / accuracies.length;
    return Math.max(0, 1 - variance);
  }
  calculateFocusStability(focusWindows) {
    if (focusWindows.length < 2) return 1;
    const windowScores = focusWindows.map((window2) => {
      return window2.filter((i) => i.correct).length / window2.length;
    });
    const mean = windowScores.reduce((sum, score) => sum + score, 0) / windowScores.length;
    const variance = windowScores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / windowScores.length;
    const coefficientOfVariation = mean > 0 ? Math.sqrt(variance) / mean : 0;
    return Math.max(0, 1 - coefficientOfVariation);
  }
  estimateInhibitoryControl(interactions) {
    const totalInteractions = interactions.length;
    const impulsiveErrors = interactions.filter(
      (i) => !i.correct && i.responseTime && i.responseTime < 800
    ).length;
    const inhibitionScore = totalInteractions > 0 ? 1 - impulsiveErrors / totalInteractions : 0.7;
    return Math.max(0.3, Math.min(1, inhibitionScore));
  }
  calculateInhibitionSpeed(inhibitionTasks) {
    const inhibitionTimes = inhibitionTasks.filter((i) => i.correct && i.inhibitionTime).map((i) => i.inhibitionTime);
    if (inhibitionTimes.length === 0) return 0.5;
    const avgInhibitionTime = inhibitionTimes.reduce((sum, time) => sum + time, 0) / inhibitionTimes.length;
    const speedScore = Math.max(0, 1 - Math.abs(avgInhibitionTime - 1500) / 2e3);
    return Math.min(1, speedScore);
  }
  calculateInterferenceResistance(interactions) {
    const interferenceTrials = interactions.filter((i) => i.hasInterference);
    if (interferenceTrials.length === 0) return 0.7;
    const resistanceScore = interferenceTrials.filter((i) => i.correct).length / interferenceTrials.length;
    return resistanceScore;
  }
  estimateDistractorResistance(interactions) {
    if (interactions.length < 5) return 0.7;
    const consistencyScore = this.calculateOverallConsistency(interactions);
    return Math.max(0.4, Math.min(1, consistencyScore));
  }
  calculateTypeSpecificResistance(typeDistractors, interactions) {
    let resistanceSum = 0;
    typeDistractors.forEach((distractor) => {
      const distractorTrials = interactions.filter(
        (i) => i.timestamp >= distractor.startTime && i.timestamp <= distractor.endTime
      );
      if (distractorTrials.length > 0) {
        const accuracy = distractorTrials.filter((i) => i.correct).length / distractorTrials.length;
        resistanceSum += accuracy;
      }
    });
    return typeDistractors.length > 0 ? resistanceSum / typeDistractors.length : 0.7;
  }
  calculateResistanceConsistency(distractorData, interactions) {
    const resistanceScores = distractorData.map((distractor) => {
      const distractorTrials = interactions.filter(
        (i) => i.timestamp >= distractor.startTime && i.timestamp <= distractor.endTime
      );
      return distractorTrials.length > 0 ? distractorTrials.filter((i) => i.correct).length / distractorTrials.length : 0;
    });
    if (resistanceScores.length < 2) return 1;
    const mean = resistanceScores.reduce((sum, score) => sum + score, 0) / resistanceScores.length;
    const variance = resistanceScores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / resistanceScores.length;
    return Math.max(0, 1 - variance);
  }
  estimateFlexibilityFromPatterns(interactions) {
    if (interactions.length < 10) return 0.7;
    const patterns = this.identifyAttentionPatterns(interactions);
    const patternChanges = this.countPatternChanges(patterns);
    const changeRate = patternChanges / patterns.length;
    const flexibilityScore = changeRate > 0.1 && changeRate < 0.4 ? 0.8 : 0.6;
    return flexibilityScore;
  }
  getPerformanceBeforeChange(interactions, change) {
    const beforeChange = interactions.filter(
      (i) => new Date(i.timestamp).getTime() < change.timestamp && new Date(i.timestamp).getTime() > change.timestamp - 3e4
      // 30s antes
    );
    return beforeChange.length > 0 ? beforeChange.filter((i) => i.correct).length / beforeChange.length : 0.5;
  }
  getPerformanceAfterChange(interactions, change) {
    const afterChange = interactions.filter(
      (i) => new Date(i.timestamp).getTime() > change.timestamp && new Date(i.timestamp).getTime() < change.timestamp + 3e4
      // 30s depois
    );
    return afterChange.length > 0 ? afterChange.filter((i) => i.correct).length / afterChange.length : 0.5;
  }
  calculateAdaptationSpeed(prePerformance, postPerformance) {
    const recoveryRate = postPerformance - prePerformance;
    return Math.max(0, 0.5 + recoveryRate);
  }
  calculateAdaptationEffectiveness(postPerformance) {
    return postPerformance;
  }
  divideIntoTimeSegments(interactions, sessionDuration, numSegments) {
    const segmentDuration = sessionDuration / numSegments;
    const segments = [];
    for (let i = 0; i < numSegments; i++) {
      const segmentStart = i * segmentDuration;
      const segmentEnd = (i + 1) * segmentDuration;
      const segmentInteractions = interactions.filter((interaction) => {
        const relativeTime = new Date(interaction.timestamp).getTime() - new Date(interactions[0].timestamp).getTime();
        return relativeTime >= segmentStart && relativeTime < segmentEnd;
      });
      if (segmentInteractions.length > 0) {
        segments.push(segmentInteractions);
      }
    }
    return segments;
  }
  calculateVigilanceDecrement(segmentPerformances) {
    if (segmentPerformances.length < 2) return 0;
    const firstSegment = segmentPerformances[0].alertness;
    const lastSegment = segmentPerformances[segmentPerformances.length - 1].alertness;
    const decrement = Math.max(0, firstSegment - lastSegment);
    return Math.min(0.5, decrement);
  }
  calculateAverageResponseTime(interactions) {
    const times = interactions.filter((i) => i.responseTime && i.responseTime > 0).map((i) => i.responseTime);
    return times.length > 0 ? times.reduce((sum, time) => sum + time, 0) / times.length : 2e3;
  }
  assessConflictResolution(interactions) {
    const conflictTrials = interactions.filter((i) => i.hasConflict);
    if (conflictTrials.length === 0) return 0.7;
    const conflictAccuracy = conflictTrials.filter((i) => i.correct).length / conflictTrials.length;
    const conflictSpeed = this.calculateAverageResponseTime(conflictTrials.filter((i) => i.correct));
    const speedScore = Math.max(0, 1 - (conflictSpeed - 2e3) / 3e3);
    return conflictAccuracy * 0.7 + speedScore * 0.3;
  }
  assessTopDownControl(interactions) {
    const controlledTrials = interactions.filter((i) => i.requiresTopDownControl);
    if (controlledTrials.length === 0) return 0.7;
    const controlAccuracy = controlledTrials.filter((i) => i.correct).length / controlledTrials.length;
    return controlAccuracy;
  }
  assessCognitiveFlexibility(interactions) {
    const flexibilityTrials = interactions.filter((i) => i.requiresFlexibility);
    if (flexibilityTrials.length === 0) return 0.7;
    const flexibilityAccuracy = flexibilityTrials.filter((i) => i.correct).length / flexibilityTrials.length;
    return flexibilityAccuracy;
  }
  calculateSimultaneousProcessing(interactions) {
    const simultaneousGroups = this.groupInteractionsByTime(interactions, 1e3);
    const maxSimultaneous = Math.max(...simultaneousGroups.map((g) => g.length), 1);
    return Math.min(maxSimultaneous / 7, 1);
  }
  calculateMultitaskingAbility(interactions) {
    const multitaskingTrials = interactions.filter((i) => i.isMultitasking);
    if (multitaskingTrials.length === 0) return 0.7;
    const multitaskingAccuracy = multitaskingTrials.filter((i) => i.correct).length / multitaskingTrials.length;
    return multitaskingAccuracy;
  }
  estimateWorkingMemoryCapacity(interactions) {
    const memoryLoadTrials = interactions.filter((i) => i.memoryLoad && i.memoryLoad > 1);
    if (memoryLoadTrials.length === 0) return 0.7;
    const memoryAccuracy = memoryLoadTrials.filter((i) => i.correct).length / memoryLoadTrials.length;
    return memoryAccuracy;
  }
  classifyAttentionProfile(results) {
    const avgScore = Object.values(results).reduce((sum, val) => sum + val, 0) / Object.keys(results).length;
    if (avgScore >= 0.85) return "superior";
    if (avgScore >= 0.7) return "above_average";
    if (avgScore >= 0.55) return "average";
    if (avgScore >= 0.4) return "below_average";
    return "impaired";
  }
  calculateOverallConsistency(interactions) {
    if (interactions.length < 3) return 1;
    const accuracies = interactions.map((i) => i.correct ? 1 : 0);
    const mean = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    const variance = accuracies.reduce((sum, acc) => sum + Math.pow(acc - mean, 2), 0) / accuracies.length;
    return Math.max(0, 1 - variance);
  }
  identifyAttentionPatterns(interactions) {
    const windowSize = 5;
    const patterns = [];
    for (let i = 0; i <= interactions.length - windowSize; i++) {
      const window2 = interactions.slice(i, i + windowSize);
      const accuracy = window2.filter((w) => w.correct).length / window2.length;
      const avgTime = this.calculateAverageResponseTime(window2);
      patterns.push({
        accuracy,
        responseTime: avgTime,
        pattern: accuracy > 0.8 ? "focused" : accuracy > 0.5 ? "moderate" : "distracted"
      });
    }
    return patterns;
  }
  countPatternChanges(patterns) {
    let changes = 0;
    for (let i = 1; i < patterns.length; i++) {
      if (patterns[i].pattern !== patterns[i - 1].pattern) {
        changes++;
      }
    }
    return changes;
  }
  groupInteractionsByTime(interactions, timeWindow) {
    const groups = [];
    let currentGroup = [];
    let lastTimestamp = null;
    interactions.forEach((interaction) => {
      const timestamp = new Date(interaction.timestamp).getTime();
      if (lastTimestamp === null || timestamp - lastTimestamp <= timeWindow) {
        currentGroup.push(interaction);
      } else {
        if (currentGroup.length > 0) {
          groups.push(currentGroup);
        }
        currentGroup = [interaction];
      }
      lastTimestamp = timestamp;
    });
    if (currentGroup.length > 0) {
      groups.push(currentGroup);
    }
    return groups;
  }
  generateAttentionalInsights(results) {
    const insights = [];
    if (results.selectiveAttention < 0.6) {
      insights.push("Dificuldades na atenção seletiva - problemas para filtrar informações relevantes");
    }
    if (results.focusedAttention < 0.5) {
      insights.push("Limitações na manutenção do foco atencional");
    }
    if (results.inhibitoryControl < 0.6) {
      insights.push("Controle inibitório reduzido - dificuldade para suprimir respostas inadequadas");
    }
    if (results.distractorResistance < 0.5) {
      insights.push("Baixa resistência a distratores - alta susceptibilidade à interferência");
    }
    if (results.attentionalFlexibility < 0.6) {
      insights.push("Flexibilidade atencional limitada - dificuldade para adaptar o foco conforme necessário");
    }
    if (results.vigilance < 0.5) {
      insights.push("Problemas de vigilância - declínio do estado de alerta ao longo do tempo");
    }
    if (results.executiveAttention < 0.6) {
      insights.push("Controle executivo da atenção comprometido");
    }
    if (results.attentionalCapacity < 0.5) {
      insights.push("Capacidade atencional reduzida - limitações no processamento simultâneo");
    }
    return insights;
  }
}
class ColorCognitionCollector {
  constructor() {
    this.cognitiveDomains = {
      categorization: "categorização cromática",
      semantics: "semântica de cores",
      memory: "memória cromática",
      processing: "processamento cognitivo",
      naming: "nomeação de cores",
      association: "associação cromática"
    };
    this.cognitiveComplexity = {
      basic: { level: 1, description: "reconhecimento básico" },
      intermediate: { level: 2, description: "discriminação avançada" },
      complex: { level: 3, description: "processamento multi-dimensional" },
      expert: { level: 4, description: "análise especializada" }
    };
    this.colorKnowledgeDomains = {
      basic: ["vermelho", "azul", "amarelo", "verde"],
      advanced: ["laranja", "roxo", "rosa"],
      complex: ["marrom", "cinza", "bege", "creme"]
    };
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise cognitiva relacionada à cor
   */
  collect(data) {
    return this.analyze(data);
  }
  async analyze(data) {
    if (!data || !data.cognitiveData) {
      console.warn("ColorCognitionCollector: Dados inválidos recebidos", data);
      return {
        colorCategorization: 0.7,
        colorSemantics: 0.7,
        colorMemory: 0.7,
        processingSpeed: 0.7,
        colorNaming: 0.7,
        colorAssociation: 0.7,
        conceptualFlexibility: 0.7,
        cognitiveLoad: 0.7
      };
    }
    return {
      colorCategorization: this.assessColorCategorization(data),
      colorSemantics: this.assessColorSemantics(data),
      colorMemory: this.assessColorMemory(data),
      processingSpeed: this.assessProcessingSpeed(data),
      colorNaming: this.assessColorNaming(data),
      colorAssociation: this.assessColorAssociation(data),
      conceptualFlexibility: this.assessConceptualFlexibility(data),
      cognitiveLoad: this.assessCognitiveLoad(data),
      colorKnowledgeBase: this.evaluateColorKnowledgeBase(data),
      cognitiveStrategies: this.identifyCognitiveStrategies(data)
    };
  }
  assessColorCategorization(data) {
    const interactions = data.cognitiveData.interactions || [];
    if (interactions.length === 0) return 0.7;
    const categorizationTasks = interactions.filter((i) => i.taskType === "categorization");
    if (categorizationTasks.length === 0) {
      return this.estimateCategorizationFromGeneral(interactions);
    }
    let categorizationScore = 0;
    Object.keys(this.cognitiveComplexity).forEach((complexity) => {
      const complexityTasks = categorizationTasks.filter((t) => t.complexity === complexity);
      if (complexityTasks.length > 0) {
        const accuracy = complexityTasks.filter((t) => t.correct).length / complexityTasks.length;
        const complexityWeight = this.cognitiveComplexity[complexity].level;
        categorizationScore += accuracy * complexityWeight;
      }
    });
    const testedComplexities = Object.keys(this.cognitiveComplexity).filter(
      (c) => categorizationTasks.some((t) => t.complexity === c)
    ).length;
    const finalScore = testedComplexities > 0 ? categorizationScore / (testedComplexities * 2.5) : 0.7;
    return Math.max(0, Math.min(1, finalScore));
  }
  assessColorSemantics(data) {
    const interactions = data.cognitiveData.interactions || [];
    const semanticTasks = interactions.filter((i) => i.taskType === "semantic" || i.hasSemanticComponent);
    if (semanticTasks.length === 0) {
      return this.estimateSemanticFromAssociations(interactions);
    }
    const semanticAccuracy = semanticTasks.filter((t) => t.correct).length / semanticTasks.length;
    const semanticSpeed = this.calculateSemanticSpeed(semanticTasks);
    const semanticRichness = this.calculateSemanticRichness(semanticTasks);
    const semanticScore = semanticAccuracy * 0.5 + semanticSpeed * 0.25 + semanticRichness * 0.25;
    return Math.max(0, Math.min(1, semanticScore));
  }
  assessColorMemory(data) {
    const interactions = data.cognitiveData.interactions || [];
    const memoryTasks = interactions.filter((i) => i.taskType === "memory");
    if (memoryTasks.length === 0) {
      return this.estimateMemoryFromRecall(interactions);
    }
    const shortTermMemory = this.assessShortTermColorMemory(memoryTasks);
    const longTermMemory = this.assessLongTermColorMemory(memoryTasks);
    const workingMemory = this.assessWorkingColorMemory(memoryTasks);
    const memoryScore = shortTermMemory * 0.4 + longTermMemory * 0.3 + workingMemory * 0.3;
    return Math.max(0, Math.min(1, memoryScore));
  }
  assessProcessingSpeed(data) {
    const interactions = data.cognitiveData.interactions || [];
    if (interactions.length === 0) return 0.7;
    const processingTimes = this.categorizeProcessingTimes(interactions);
    let speedScore = 0;
    let categoryCount = 0;
    Object.entries(processingTimes).forEach(([category, times]) => {
      if (times.length > 0) {
        const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
        const categorySpeed = this.calculateCategorySpeed(category, avgTime);
        speedScore += categorySpeed;
        categoryCount++;
      }
    });
    const overallSpeed = categoryCount > 0 ? speedScore / categoryCount : 0.7;
    const speedConsistency = this.calculateSpeedConsistency(interactions);
    return Math.max(0, Math.min(1, overallSpeed * speedConsistency));
  }
  assessColorNaming(data) {
    const interactions = data.cognitiveData.interactions || [];
    const namingTasks = interactions.filter((i) => i.taskType === "naming" || i.requiresNaming);
    if (namingTasks.length === 0) {
      return this.estimateNamingFromIdentification(interactions);
    }
    const namingAccuracy = namingTasks.filter((t) => t.correct).length / namingTasks.length;
    const namingSpeed = this.calculateNamingSpeed(namingTasks);
    const namingConsistency = this.calculateNamingConsistency(namingTasks);
    const colorVocabulary = this.assessColorVocabulary(namingTasks);
    const namingScore = namingAccuracy * 0.4 + namingSpeed * 0.2 + namingConsistency * 0.2 + colorVocabulary * 0.2;
    return Math.max(0, Math.min(1, namingScore));
  }
  assessColorAssociation(data) {
    const interactions = data.cognitiveData.interactions || [];
    const associationTasks = interactions.filter((i) => i.taskType === "association");
    if (associationTasks.length === 0) {
      return this.estimateAssociationFromPatterns(interactions);
    }
    const associationStrength = this.calculateAssociationStrength(associationTasks);
    const associativeFlexibility = this.calculateAssociativeFlexibility(associationTasks);
    const culturalAppropriation = this.assessCulturalAppropriation(associationTasks);
    const associationScore = associationStrength * 0.4 + associativeFlexibility * 0.3 + culturalAppropriation * 0.3;
    return Math.max(0, Math.min(1, associationScore));
  }
  assessConceptualFlexibility(data) {
    const interactions = data.cognitiveData.interactions || [];
    if (interactions.length === 0) return 0.7;
    const conceptualSwitches = this.identifyConceptualSwitches(interactions);
    const switchingEfficiency = this.calculateSwitchingEfficiency(conceptualSwitches);
    const contextualAdaptation = this.assessContextualAdaptation(interactions);
    const divergentThinking = this.assessDivergentColorThinking(interactions);
    const flexibilityScore = switchingEfficiency * 0.4 + contextualAdaptation * 0.3 + divergentThinking * 0.3;
    return Math.max(0, Math.min(1, flexibilityScore));
  }
  assessCognitiveLoad(data) {
    const interactions = data.cognitiveData.interactions || [];
    if (interactions.length === 0) return 0.7;
    const loadLevels = this.categorizeByLoad(interactions);
    let loadScore = 0;
    let loadCount = 0;
    Object.entries(loadLevels).forEach(([level, tasks]) => {
      if (tasks.length > 0) {
        const accuracy = tasks.filter((t) => t.correct).length / tasks.length;
        const expectedAccuracy = this.getExpectedAccuracy(level);
        const loadPerformance = accuracy / expectedAccuracy;
        loadScore += Math.min(1, loadPerformance);
        loadCount++;
      }
    });
    return loadCount > 0 ? loadScore / loadCount : 0.7;
  }
  evaluateColorKnowledgeBase(data) {
    const interactions = data.cognitiveData.interactions || [];
    const knowledgeEvaluation = {
      basicColors: this.evaluateBasicColorKnowledge(interactions),
      advancedColors: this.evaluateAdvancedColorKnowledge(interactions),
      complexColors: this.evaluateComplexColorKnowledge(interactions),
      colorRelationships: this.evaluateColorRelationships(interactions),
      colorTheory: this.evaluateColorTheoryKnowledge(interactions)
    };
    return knowledgeEvaluation;
  }
  identifyCognitiveStrategies(data) {
    const interactions = data.cognitiveData.interactions || [];
    const strategies = {
      verbal: this.identifyVerbalStrategies(interactions),
      visual: this.identifyVisualStrategies(interactions),
      associative: this.identifyAssociativeStrategies(interactions),
      categorical: this.identifyCategoricalStrategies(interactions),
      systematic: this.identifySystematicStrategies(interactions)
    };
    const dominantStrategy = Object.entries(strategies).sort((a, b) => b[1].score - a[1].score)[0];
    return {
      strategies,
      dominantStrategy: dominantStrategy[0],
      strategyEffectiveness: dominantStrategy[1].score,
      strategyFlexibility: this.calculateStrategyFlexibility(strategies)
    };
  }
  // Métodos auxiliares
  estimateCategorizationFromGeneral(interactions) {
    const colorCategories = {
      primary: ["vermelho", "azul", "amarelo"],
      secondary: ["verde", "laranja", "roxo"],
      tertiary: ["rosa", "marrom"]
    };
    let categorizationScore = 0;
    let categoryCount = 0;
    Object.entries(colorCategories).forEach(([category, colors]) => {
      const categoryInteractions = interactions.filter(
        (i) => colors.includes(i.targetColor)
      );
      if (categoryInteractions.length > 0) {
        const accuracy = categoryInteractions.filter((i) => i.correct).length / categoryInteractions.length;
        categorizationScore += accuracy;
        categoryCount++;
      }
    });
    return categoryCount > 0 ? categorizationScore / categoryCount : 0.7;
  }
  estimateSemanticFromAssociations(interactions) {
    const semanticAssociations = {
      "vermelho": ["maçã", "morango", "rosa", "coração"],
      "verde": ["folha", "grama", "sapo", "árvore"],
      "azul": ["céu", "mar", "água", "gelo"],
      "amarelo": ["sol", "banana", "ouro", "limão"]
    };
    let semanticScore = 0;
    let semanticTests = 0;
    interactions.forEach((interaction) => {
      const targetColor = interaction.targetColor;
      const selectedObject = interaction.selectedObject;
      if (semanticAssociations[targetColor] && selectedObject) {
        const isSemanticMatch = semanticAssociations[targetColor].includes(selectedObject);
        if (isSemanticMatch && interaction.correct) {
          semanticScore += 1;
        } else if (!isSemanticMatch && !interaction.correct) {
          semanticScore += 0.5;
        }
        semanticTests++;
      }
    });
    return semanticTests > 0 ? semanticScore / semanticTests : 0.7;
  }
  estimateMemoryFromRecall(interactions) {
    if (interactions.length < 5) return 0.7;
    const recallPatterns = this.analyzeRecallPatterns(interactions);
    const memoryIndicator = recallPatterns.improvementRate;
    return Math.max(0.3, Math.min(1, 0.5 + memoryIndicator));
  }
  assessShortTermColorMemory(memoryTasks) {
    if (memoryTasks.length === 0) return 0.7;
    const shortTermTasks = memoryTasks.filter(
      (task) => (task.retentionTime || 0) <= 3e4
      // 30 segundos ou menos
    );
    if (shortTermTasks.length === 0) {
      const accuracy2 = memoryTasks.filter((task) => task.correct).length / memoryTasks.length;
      return Math.max(0, Math.min(1, accuracy2));
    }
    let shortTermScore = 0;
    const maxItemsRetained = Math.max(...shortTermTasks.map((task) => task.itemsCount || 1));
    const capacityScore = Math.min(1, maxItemsRetained / 7);
    const accuracy = shortTermTasks.filter((task) => task.correct).length / shortTermTasks.length;
    const avgAccessTime = shortTermTasks.reduce((sum, task) => sum + (task.responseTime || 2e3), 0) / shortTermTasks.length;
    const speedScore = Math.max(0, Math.min(1, (3e3 - avgAccessTime) / 2e3));
    const persistenceScore = shortTermTasks.filter(
      (task) => task.retentionTime > 1e4 && task.correct
    ).length / Math.max(1, shortTermTasks.filter((task) => task.retentionTime > 1e4).length);
    shortTermScore = capacityScore * 0.3 + accuracy * 0.4 + speedScore * 0.2 + persistenceScore * 0.1;
    return Math.max(0, Math.min(1, shortTermScore));
  }
  assessLongTermColorMemory(memoryTasks) {
    if (memoryTasks.length === 0) return 0.7;
    const longTermTasks = memoryTasks.filter(
      (task) => (task.retentionTime || 0) > 3e4
      // Mais de 30 segundos
    );
    if (longTermTasks.length === 0) {
      const allTasks = memoryTasks.filter((task) => task.retentionTime > 5e3);
      if (allTasks.length === 0) return 0.7;
      const accuracy = allTasks.filter((task) => task.correct).length / allTasks.length;
      return Math.max(0.3, Math.min(1, accuracy * 0.9));
    }
    let longTermScore = 0;
    const consolidationTasks = longTermTasks.filter((task) => task.retentionTime > 6e4);
    const consolidationRate = consolidationTasks.length > 0 ? consolidationTasks.filter((task) => task.correct).length / consolidationTasks.length : 0.7;
    const retrievalTasks = longTermTasks.filter((task) => task.taskType === "recall" || task.requiresRecall);
    const retrievalRate = retrievalTasks.length > 0 ? retrievalTasks.filter((task) => task.correct).length / retrievalTasks.length : 0.7;
    const recognitionTasks = longTermTasks.filter((task) => task.taskType === "recognition");
    const recognitionRate = recognitionTasks.length > 0 ? recognitionTasks.filter((task) => task.correct).length / recognitionTasks.length : 0.8;
    const persistenceTasks = longTermTasks.filter(
      (task) => task.retentionTime > 12e4 && !task.hadIntermediateRehearsal
    );
    const persistenceRate = persistenceTasks.length > 0 ? persistenceTasks.filter((task) => task.correct).length / persistenceTasks.length : 0.6;
    const interferenceTasks = longTermTasks.filter((task) => task.hadInterference);
    const interferenceResistance = interferenceTasks.length > 0 ? interferenceTasks.filter((task) => task.correct).length / interferenceTasks.length : 0.7;
    longTermScore = consolidationRate * 0.25 + retrievalRate * 0.25 + recognitionRate * 0.2 + persistenceRate * 0.2 + interferenceResistance * 0.1;
    return Math.max(0, Math.min(1, longTermScore));
  }
  assessWorkingColorMemory(memoryTasks) {
    if (memoryTasks.length === 0) return 0.7;
    const workingMemoryTasks = memoryTasks.filter(
      (task) => (task.presentationTime || 0) <= 15e3
      // 15 segundos ou menos
    );
    if (workingMemoryTasks.length === 0) {
      const accuracy = memoryTasks.filter((task) => task.correct).length / memoryTasks.length;
      return Math.max(0, Math.min(1, accuracy * 0.8));
    }
    let workingMemoryScore = 0;
    const maxCapacity = Math.max(...workingMemoryTasks.map((task) => task.itemsCount || 1));
    const capacityScore = Math.min(1, maxCapacity / 5);
    const immediateRecallAccuracy = workingMemoryTasks.filter((task) => task.correct).length / workingMemoryTasks.length;
    const avgResponseTime = workingMemoryTasks.reduce((sum, task) => sum + (task.responseTime || 2e3), 0) / workingMemoryTasks.length;
    const speedScore = Math.max(0, Math.min(1, (3e3 - avgResponseTime) / 2e3));
    workingMemoryScore = capacityScore * 0.3 + immediateRecallAccuracy * 0.4 + speedScore * 0.3;
    return Math.max(0, Math.min(1, workingMemoryScore));
  }
  assessColorRecognition(data) {
    const interactions = data.cognitiveData.interactions || [];
    const recognitionTasks = interactions.filter((i) => i.taskType === "recognition");
    if (recognitionTasks.length === 0) {
      return this.estimateRecognitionFromTasks(interactions);
    }
    const recognitionAccuracy = recognitionTasks.filter((t) => t.correct).length / recognitionTasks.length;
    const recognitionSpeed = this.calculateRecognitionSpeed(recognitionTasks);
    const recognitionConsistency = this.calculateRecognitionConsistency(recognitionTasks);
    const recognitionScore = recognitionAccuracy * 0.5 + recognitionSpeed * 0.3 + recognitionConsistency * 0.2;
    return Math.max(0, Math.min(1, recognitionScore));
  }
  // Métodos auxiliares para avaliação de memória
  estimateRecognitionFromTasks(interactions) {
    const recognitionRate = interactions.filter((i) => i.taskType === "recognition" && i.correct).length / Math.max(1, interactions.filter((i) => i.taskType === "recognition").length);
    return Math.max(0.3, Math.min(1, recognitionRate));
  }
  calculateRecognitionSpeed(recognitionTasks) {
    const responseTimes = recognitionTasks.filter((t) => t.correct && t.responseTime).map((t) => t.responseTime);
    if (responseTimes.length === 0) return 0.5;
    const avgResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const speedScore = Math.max(0, 1 - (avgResponseTime - 1e3) / 1500);
    return Math.min(1, speedScore);
  }
  calculateRecognitionConsistency(recognitionTasks) {
    const colorRecognitions = {};
    recognitionTasks.forEach((task) => {
      const color = task.targetColor;
      const recognizedColor = task.recognizedColor || task.givenName;
      if (!colorRecognitions[color]) {
        colorRecognitions[color] = [];
      }
      colorRecognitions[color].push(recognizedColor);
    });
    let consistencySum = 0;
    let colorCount = 0;
    Object.values(colorRecognitions).forEach((recognizedColors) => {
      if (recognizedColors.length > 1) {
        const mostCommon = this.getMostCommonName(recognizedColors);
        const consistency = recognizedColors.filter((name) => name === mostCommon).length / recognizedColors.length;
        consistencySum += consistency;
        colorCount++;
      }
    });
    return colorCount > 0 ? consistencySum / colorCount : 1;
  }
  // Métodos auxiliares
  categorizeProcessingTimes(interactions) {
    const categories = {
      simple: [],
      complex: [],
      semantic: [],
      memory: []
    };
    interactions.forEach((interaction) => {
      if (interaction.responseTime && interaction.responseTime > 0) {
        const complexity = this.determineTaskComplexity(interaction);
        if (categories[complexity]) {
          categories[complexity].push(interaction.responseTime);
        }
      }
    });
    return categories;
  }
  calculateCategorySpeed(category, avgTime) {
    const speedThresholds = {
      simple: { fast: 800, normal: 1500, slow: 2500 },
      complex: { fast: 1200, normal: 2e3, slow: 3500 },
      semantic: { fast: 1e3, normal: 1800, slow: 3e3 },
      memory: { fast: 1500, normal: 2500, slow: 4e3 }
    };
    const thresholds = speedThresholds[category] || speedThresholds.simple;
    if (avgTime <= thresholds.fast) return 1;
    if (avgTime <= thresholds.normal) return 0.8;
    if (avgTime <= thresholds.slow) return 0.6;
    return 0.4;
  }
  calculateSpeedConsistency(interactions) {
    const times = interactions.filter((i) => i.responseTime && i.responseTime > 0).map((i) => i.responseTime);
    if (times.length < 3) return 1;
    const mean = times.reduce((sum, time) => sum + time, 0) / times.length;
    const variance = times.reduce((sum, time) => sum + Math.pow(time - mean, 2), 0) / times.length;
    const standardDeviation = Math.sqrt(variance);
    const coefficientOfVariation = standardDeviation / mean;
    return Math.max(0.5, 1 - Math.min(coefficientOfVariation, 0.5));
  }
  estimateNamingFromIdentification(interactions) {
    const identificationAccuracy = interactions.filter((i) => i.correct).length / interactions.length;
    const estimatedNaming = identificationAccuracy * 0.9;
    return Math.max(0.3, Math.min(1, estimatedNaming));
  }
  calculateNamingSpeed(namingTasks) {
    const namingTimes = namingTasks.filter((t) => t.correct && t.responseTime).map((t) => t.responseTime);
    if (namingTimes.length === 0) return 0.5;
    const avgNamingTime = namingTimes.reduce((sum, time) => sum + time, 0) / namingTimes.length;
    const speedScore = Math.max(0, 1 - (avgNamingTime - 1200) / 2e3);
    return Math.min(1, speedScore);
  }
  calculateNamingConsistency(namingTasks) {
    const colorNames = {};
    namingTasks.forEach((task) => {
      const color = task.targetColor;
      const name = task.givenName;
      if (!colorNames[color]) {
        colorNames[color] = [];
      }
      colorNames[color].push(name);
    });
    let consistencySum = 0;
    let colorCount = 0;
    Object.values(colorNames).forEach((names) => {
      if (names.length > 1) {
        const mostCommon = this.getMostCommonName(names);
        const consistency = names.filter((name) => name === mostCommon).length / names.length;
        consistencySum += consistency;
        colorCount++;
      }
    });
    return colorCount > 0 ? consistencySum / colorCount : 1;
  }
  assessColorVocabulary(namingTasks) {
    const uniqueNames = new Set(namingTasks.map((t) => t.givenName)).size;
    const totalColors = new Set(namingTasks.map((t) => t.targetColor)).size;
    const vocabularyRichness = totalColors > 0 ? Math.min(uniqueNames / totalColors, 2) / 2 : 0.5;
    return vocabularyRichness;
  }
  estimateAssociationFromPatterns(interactions) {
    const responsePatterns = this.analyzeResponsePatterns(interactions);
    const associationScore = responsePatterns.consistency * responsePatterns.appropriateness;
    return Math.max(0.4, Math.min(1, associationScore));
  }
  calculateAssociationStrength(associationTasks) {
    let strengthSum = 0;
    associationTasks.forEach((task) => {
      const accuracy = task.correct ? 1 : 0;
      const speed = task.responseTime ? Math.max(0, 1 - (task.responseTime - 1e3) / 2e3) : 0.5;
      const strength = accuracy * 0.7 + speed * 0.3;
      strengthSum += strength;
    });
    return associationTasks.length > 0 ? strengthSum / associationTasks.length : 0.7;
  }
  calculateAssociativeFlexibility(associationTasks) {
    const colorAssociations = {};
    associationTasks.forEach((task) => {
      const color = task.targetColor;
      const association = task.association;
      if (!colorAssociations[color]) {
        colorAssociations[color] = /* @__PURE__ */ new Set();
      }
      colorAssociations[color].add(association);
    });
    const avgAssociationsPerColor = Object.values(colorAssociations).reduce((sum, associations) => sum + associations.size, 0) / Object.keys(colorAssociations).length;
    return Math.min(avgAssociationsPerColor / 3, 1);
  }
  assessCulturalAppropriation(associationTasks) {
    const culturallyAppropriate = associationTasks.filter(
      (task) => this.isCulturallyAppropriate(task.targetColor, task.association)
    ).length;
    return associationTasks.length > 0 ? culturallyAppropriate / associationTasks.length : 0.8;
  }
  identifyConceptualSwitches(interactions) {
    const switches = [];
    for (let i = 1; i < interactions.length; i++) {
      const prev = interactions[i - 1];
      const curr = interactions[i];
      if (this.isConceptualSwitch(prev, curr)) {
        switches.push({
          from: prev.conceptualAspect,
          to: curr.conceptualAspect,
          efficiency: this.calculateSwitchEfficiency(prev, curr)
        });
      }
    }
    return switches;
  }
  calculateSwitchingEfficiency(conceptualSwitches) {
    if (conceptualSwitches.length === 0) return 0.8;
    const avgEfficiency = conceptualSwitches.reduce((sum, sw) => sum + sw.efficiency, 0) / conceptualSwitches.length;
    return avgEfficiency;
  }
  assessContextualAdaptation(interactions) {
    const contexts = this.identifyContexts(interactions);
    if (contexts.length < 2) return 0.7;
    let adaptationSum = 0;
    contexts.forEach((context) => {
      const contextAccuracy = context.interactions.filter((i) => i.correct).length / context.interactions.length;
      adaptationSum += contextAccuracy;
    });
    return adaptationSum / contexts.length;
  }
  assessDivergentColorThinking(interactions) {
    const creativityScores = interactions.filter((i) => i.creativityRating).map((i) => i.creativityRating);
    if (creativityScores.length === 0) return 0.6;
    const avgCreativity = creativityScores.reduce((sum, score) => sum + score, 0) / creativityScores.length;
    return Math.min(avgCreativity / 5, 1);
  }
  categorizeByLoad(interactions) {
    const loadLevels = {
      low: [],
      medium: [],
      high: [],
      extreme: []
    };
    interactions.forEach((interaction) => {
      const load = this.determineCognitiveLoad(interaction);
      if (loadLevels[load]) {
        loadLevels[load].push(interaction);
      }
    });
    return loadLevels;
  }
  getExpectedAccuracy(loadLevel) {
    const expectedAccuracies = {
      low: 0.9,
      medium: 0.8,
      high: 0.7,
      extreme: 0.6
    };
    return expectedAccuracies[loadLevel] || 0.8;
  }
  evaluateBasicColorKnowledge(interactions) {
    const basicColors = this.colorKnowledgeDomains.basic;
    const basicInteractions = interactions.filter((i) => basicColors.includes(i.targetColor));
    return basicInteractions.length > 0 ? basicInteractions.filter((i) => i.correct).length / basicInteractions.length : 0.8;
  }
  evaluateAdvancedColorKnowledge(interactions) {
    const advancedColors = this.colorKnowledgeDomains.advanced;
    const advancedInteractions = interactions.filter((i) => advancedColors.includes(i.targetColor));
    return advancedInteractions.length > 0 ? advancedInteractions.filter((i) => i.correct).length / advancedInteractions.length : 0.7;
  }
  evaluateComplexColorKnowledge(interactions) {
    const complexColors = this.colorKnowledgeDomains.complex;
    const complexInteractions = interactions.filter((i) => complexColors.includes(i.targetColor));
    return complexInteractions.length > 0 ? complexInteractions.filter((i) => i.correct).length / complexInteractions.length : 0.6;
  }
  evaluateColorRelationships(interactions) {
    const relationshipTasks = interactions.filter((i) => i.taskType === "relationship");
    return relationshipTasks.length > 0 ? relationshipTasks.filter((i) => i.correct).length / relationshipTasks.length : 0.6;
  }
  evaluateColorTheoryKnowledge(interactions) {
    const theoryTasks = interactions.filter((i) => i.taskType === "theory");
    return theoryTasks.length > 0 ? theoryTasks.filter((i) => i.correct).length / theoryTasks.length : 0.5;
  }
  identifyVerbalStrategies(interactions) {
    const verbalIndicators = interactions.filter(
      (i) => i.strategy === "verbal" || i.usedVerbalization
    ).length;
    return {
      score: interactions.length > 0 ? verbalIndicators / interactions.length : 0.3,
      indicators: verbalIndicators
    };
  }
  identifyVisualStrategies(interactions) {
    const visualIndicators = interactions.filter(
      (i) => i.strategy === "visual" || i.usedVisualization
    ).length;
    return {
      score: interactions.length > 0 ? visualIndicators / interactions.length : 0.5,
      indicators: visualIndicators
    };
  }
  identifyAssociativeStrategies(interactions) {
    const associativeIndicators = interactions.filter(
      (i) => i.strategy === "associative" || i.usedAssociation
    ).length;
    return {
      score: interactions.length > 0 ? associativeIndicators / interactions.length : 0.4,
      indicators: associativeIndicators
    };
  }
  identifyCategoricalStrategies(interactions) {
    const categoricalIndicators = interactions.filter(
      (i) => i.strategy === "categorical" || i.usedCategorization
    ).length;
    return {
      score: interactions.length > 0 ? categoricalIndicators / interactions.length : 0.6,
      indicators: categoricalIndicators
    };
  }
  identifySystematicStrategies(interactions) {
    const systematicIndicators = interactions.filter(
      (i) => i.strategy === "systematic" || i.usedSystematicApproach
    ).length;
    return {
      score: interactions.length > 0 ? systematicIndicators / interactions.length : 0.5,
      indicators: systematicIndicators
    };
  }
  calculateStrategyFlexibility(strategies) {
    const usedStrategies = Object.values(strategies).filter((s) => s.score > 0.2).length;
    return Math.min(usedStrategies / 3, 1);
  }
  // Métodos auxiliares menores
  analyzeRecallPatterns(interactions) {
    const colorRecalls = {};
    interactions.forEach((interaction, index) => {
      const color = interaction.targetColor;
      if (!colorRecalls[color]) {
        colorRecalls[color] = [];
      }
      colorRecalls[color].push({
        attempt: index,
        correct: interaction.correct,
        time: interaction.responseTime
      });
    });
    let improvementSum = 0;
    let colorCount = 0;
    Object.values(colorRecalls).forEach((recalls) => {
      if (recalls.length > 1) {
        const firstAccuracy = recalls[0].correct ? 1 : 0;
        const laterAccuracy = recalls.slice(1).filter((r) => r.correct).length / (recalls.length - 1);
        const improvement = laterAccuracy - firstAccuracy;
        improvementSum += Math.max(0, improvement);
        colorCount++;
      }
    });
    return {
      improvementRate: colorCount > 0 ? improvementSum / colorCount : 0,
      totalRecalls: Object.keys(colorRecalls).length
    };
  }
  determineTaskComplexity(interaction) {
    if (interaction.complexity) return interaction.complexity;
    this.getColorComplexity(interaction.targetColor);
    const taskComplexity = interaction.taskType === "memory" ? "complex" : interaction.taskType === "semantic" ? "semantic" : "simple";
    return taskComplexity;
  }
  getColorComplexity(color) {
    if (this.colorKnowledgeDomains.basic.includes(color)) return "simple";
    if (this.colorKnowledgeDomains.advanced.includes(color)) return "complex";
    if (this.colorKnowledgeDomains.complex.includes(color)) return "complex";
    return "simple";
  }
  getMostCommonName(names) {
    const nameCounts = {};
    names.forEach((name) => {
      nameCounts[name] = (nameCounts[name] || 0) + 1;
    });
    return Object.entries(nameCounts).sort((a, b) => b[1] - a[1])[0][0];
  }
  analyzeResponsePatterns(interactions) {
    const patterns = {
      consistency: this.calculatePatternConsistency(interactions),
      appropriateness: this.assessPatternAppropriation(interactions)
    };
    return patterns;
  }
  calculatePatternConsistency(interactions) {
    if (interactions.length < 3) return 0.8;
    const responseTimes = interactions.map((i) => i.responseTime || 2e3);
    const mean = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const variance = responseTimes.reduce((sum, time) => sum + Math.pow(time - mean, 2), 0) / responseTimes.length;
    const consistency = Math.max(0, 1 - Math.sqrt(variance) / mean);
    return consistency;
  }
  assessPatternAppropriation(interactions) {
    const appropriateResponses = interactions.filter(
      (i) => this.isAppropriateResponse(i)
    ).length;
    return interactions.length > 0 ? appropriateResponses / interactions.length : 0.7;
  }
  isCulturallyAppropriate(color, association) {
    const culturalAssociations = {
      "vermelho": ["amor", "paixão", "sangue", "fogo", "maçã"],
      "verde": ["natureza", "esperança", "dinheiro", "folha"],
      "azul": ["céu", "mar", "tranquilidade", "frio"],
      "amarelo": ["sol", "alegria", "ouro", "banana"],
      "roxo": ["realeza", "mistério", "uva"],
      "laranja": ["laranja", "outono", "energia"],
      "rosa": ["feminino", "romance", "flores"],
      "marrom": ["terra", "madeira", "estabilidade"]
    };
    return culturalAssociations[color]?.includes(association) || false;
  }
  isConceptualSwitch(prev, curr) {
    return prev.conceptualAspect !== curr.conceptualAspect;
  }
  calculateSwitchEfficiency(prev, curr) {
    const timeDiff = curr.responseTime - prev.responseTime;
    const expectedSwitchTime = 500;
    const efficiency = Math.max(0, 1 - Math.abs(timeDiff - expectedSwitchTime) / 1e3);
    return efficiency;
  }
  identifyContexts(interactions) {
    const contexts = [];
    let currentContext = null;
    interactions.forEach((interaction) => {
      const context = interaction.context || "default";
      if (currentContext !== context) {
        if (currentContext !== null) {
          contexts[contexts.length - 1].end = interaction.timestamp;
        }
        contexts.push({
          context,
          start: interaction.timestamp,
          interactions: []
        });
        currentContext = context;
      }
      contexts[contexts.length - 1].interactions.push(interaction);
    });
    return contexts;
  }
  determineCognitiveLoad(interaction) {
    const factors = [
      interaction.complexity || "medium",
      interaction.timeConstraint ? "high" : "low",
      interaction.distractors ? "high" : "low",
      interaction.memoryRequirement || "medium"
    ];
    const highFactors = factors.filter((f) => f === "high").length;
    if (highFactors >= 3) return "extreme";
    if (highFactors >= 2) return "high";
    if (highFactors >= 1) return "medium";
    return "low";
  }
  isAppropriateResponse(interaction) {
    return interaction.correct || interaction.responseAppropriation > 0.5;
  }
  generateCognitionInsights(results) {
    const insights = [];
    if (results.colorCategorization < 0.6) {
      insights.push("Dificuldades na categorização cromática");
    }
    if (results.colorSemantics < 0.6) {
      insights.push("Limitações no conhecimento semântico de cores");
    }
    if (results.colorMemory < 0.5) {
      insights.push("Problemas de memória cromática");
    }
    if (results.processingSpeed < 0.5) {
      insights.push("Velocidade de processamento cognitivo reduzida");
    }
    if (results.colorNaming < 0.6) {
      insights.push("Dificuldades na nomeação de cores");
    }
    if (results.colorAssociation < 0.6) {
      insights.push("Limitações nas associações cromáticas");
    }
    if (results.conceptualFlexibility < 0.5) {
      insights.push("Flexibilidade conceitual reduzida");
    }
    if (results.cognitiveLoad < 0.5) {
      insights.push("Sensibilidade elevada à carga cognitiva");
    }
    return insights;
  }
  calculateSemanticSpeed(semanticTasks) {
    if (semanticTasks.length === 0) return 0.7;
    const avgResponseTime = semanticTasks.reduce((sum, task) => sum + (task.responseTime || 2e3), 0) / semanticTasks.length;
    const speed = Math.max(0, Math.min(1, (3e3 - avgResponseTime) / 2e3));
    return speed;
  }
  calculateSemanticRichness(semanticTasks) {
    if (semanticTasks.length === 0) return 0.7;
    const uniqueAssociations = /* @__PURE__ */ new Set();
    semanticTasks.forEach((task) => {
      if (task.associations) {
        task.associations.forEach((assoc) => uniqueAssociations.add(assoc));
      }
      if (task.semanticCategory) {
        uniqueAssociations.add(task.semanticCategory);
      }
    });
    const expectedAssociations = Math.min(10, semanticTasks.length * 2);
    const richness = Math.min(1, uniqueAssociations.size / expectedAssociations);
    return richness;
  }
}
class ErrorPatternCollector {
  constructor() {
    this.name = "ColorMatchErrorPatternCollector";
    this.description = "Coleta padrões de erros no ColorMatch";
    this.version = "1.0.0";
    this.isActive = true;
    this.collectedData = [];
    this.errorData = {
      colorConfusions: {},
      visualErrors: [],
      spatialErrors: [],
      attentionErrors: [],
      persistentErrors: {},
      errorClusters: [],
      learningIndicators: [],
      colorPerceptionIssues: {}
    };
    this.sessionStartTime = Date.now();
    this.errorThresholds = {
      persistent: 3,
      // Erros repetidos considerados persistentes
      cluster: 5,
      // Número mínimo para formar cluster
      severity: {
        low: 0.3,
        medium: 0.6,
        high: 0.8
      }
    };
    console.log(`🎨 ${this.name} v${this.version} inicializado`);
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de erros
   */
  collect(gameData) {
    if (!gameData) {
      console.warn("ColorMatchErrorPatternCollector: Dados do jogo não fornecidos para análise");
      return { errors: [], patterns: [], metrics: {} };
    }
    console.log(`📊 ColorMatchErrorPatternCollector: Analisando dados da sessão ${gameData.sessionId || "sem ID"}`);
    try {
      const errorMetrics = this.analyzeErrorPatterns(gameData);
      this.collectedData.push({
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        sessionId: gameData.sessionId || `session_${Date.now()}`,
        metrics: errorMetrics
      });
      return {
        errors: this.errorData.visualErrors.concat(
          this.errorData.spatialErrors,
          this.errorData.attentionErrors
        ),
        patterns: this.errorData.errorClusters,
        metrics: errorMetrics
      };
    } catch (error) {
      console.error(`❌ ColorMatchErrorPatternCollector: Erro na análise`, error);
      return { errors: [], patterns: [], metrics: {} };
    }
  }
  /**
   * Método padronizado de análise para integração com testes e processadores
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de erros
   */
  analyze(gameData) {
    return this.collect(gameData);
  }
  /**
   * Extrai e categoriza erros dos dados do jogo
   */
  analyzeErrorPatterns(gameData) {
    const patterns = {
      colorConfusions: this.detectColorConfusions(gameData),
      visualErrors: this.detectVisualErrors(gameData),
      spatialErrors: this.detectSpatialErrors(gameData),
      attentionErrors: this.detectAttentionErrors(gameData),
      severity: this.calculateOverallSeverity(gameData)
    };
    return patterns;
  }
  detectColorConfusions(gameData) {
    return [];
  }
  detectVisualErrors(gameData) {
    return [];
  }
  detectSpatialErrors(gameData) {
    return [];
  }
  detectAttentionErrors(gameData) {
    return [];
  }
  calculateOverallSeverity(gameData) {
    return "low";
  }
  categorizeErrors(errorMetrics) {
  }
  /**
   * Coleta erros de confusão entre cores
   */
  collectColorConfusion(targetColor, selectedColor, context) {
    const confusionKey = `${targetColor}->${selectedColor}`;
    const confusionData = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      targetColor,
      selectedColor,
      confusionType: this.identifyColorConfusionType(targetColor, selectedColor),
      context: {
        difficulty: context.difficulty || "unknown",
        position: context.position || null,
        distractors: context.distractors || [],
        responseTime: context.responseTime || 0,
        attempts: context.attempts || 1,
        lightingCondition: context.lightingCondition || "normal"
      },
      severity: this.calculateColorErrorSeverity(targetColor, selectedColor, context),
      frequency: this.updateConfusionFrequency(confusionKey),
      colorDistance: this.calculateColorDistance(targetColor, selectedColor)
    };
    if (!this.errorData.colorConfusions[confusionKey]) {
      this.errorData.colorConfusions[confusionKey] = [];
    }
    this.errorData.colorConfusions[confusionKey].push(confusionData);
    this.detectPersistentColorError(confusionKey, confusionData);
    this.detectColorPerceptionIssues(confusionData);
    return confusionData;
  }
  /**
   * Identifica o tipo de confusão de cor
   */
  identifyColorConfusionType(targetColor, selectedColor) {
    const colorGroups = {
      warm: ["red", "orange", "yellow", "pink"],
      cool: ["blue", "green", "purple", "cyan"],
      neutral: ["black", "white", "gray", "brown"]
    };
    const targetGroup = this.getColorGroup(targetColor, colorGroups);
    const selectedGroup = this.getColorGroup(selectedColor, colorGroups);
    if (targetGroup === selectedGroup) {
      return "intra-group";
    } else if (this.areSimilarColors(targetColor, selectedColor)) {
      return "similarity-based";
    } else if (this.areComplementaryColors(targetColor, selectedColor)) {
      return "complementary";
    } else {
      return "random";
    }
  }
  /**
   * Coleta erros relacionados a cores
   */
  collectColorError(targetColor, selectedColor, context) {
    const errorKey = `${targetColor}->${selectedColor}`;
    const colorError = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      targetColor,
      selectedColor,
      errorType: this.identifyColorErrorType(targetColor, selectedColor),
      context: {
        difficulty: context.difficulty || "medium",
        responseTime: context.responseTime || 0,
        attemptNumber: context.attemptNumber || 0
      },
      severity: this.calculateColorErrorSeverity(targetColor, selectedColor, context),
      colorDistance: this.calculateColorDistance(targetColor, selectedColor)
    };
    if (!this.errorData.colorConfusions[errorKey]) {
      this.errorData.colorConfusions[errorKey] = [];
    }
    this.errorData.colorConfusions[errorKey].push(colorError);
    return colorError;
  }
  /**
   * Identifica o tipo de erro relacionado a cores
   */
  identifyColorErrorType(targetColor, selectedColor) {
    if (!selectedColor) return "no_selection";
    if (this.areSimilarColors(targetColor, selectedColor)) {
      return "similar_color";
    }
    if (this.areComplementaryColors(targetColor, selectedColor)) {
      return "complementary_color";
    }
    if (this.isBrightnessError(targetColor, selectedColor)) {
      return "brightness_error";
    }
    if (this.isSaturationError(targetColor, selectedColor)) {
      return "saturation_error";
    }
    return "general_color_error";
  }
  /**
   * Verifica se duas cores são similares
   */
  areSimilarColors(color1, color2) {
    return false;
  }
  /**
   * Verifica se duas cores são complementares
   */
  areComplementaryColors(color1, color2) {
    return false;
  }
  /**
   * Verifica se existe diferença principalmente de brilho entre as cores
   */
  isBrightnessError(color1, color2) {
    return false;
  }
  /**
   * Verifica se existe diferença principalmente de saturação entre as cores
   */
  isSaturationError(color1, color2) {
    return false;
  }
  /**
   * Calcula a distância entre duas cores (métrica para diferença)
   */
  calculateColorDistance(color1, color2) {
    return 0.5;
  }
  /**
   * Calcula a severidade do erro de cor
   */
  calculateColorErrorSeverity(targetColor, selectedColor, context) {
    let severity = 0.5;
    const distance = this.calculateColorDistance(targetColor, selectedColor);
    severity += distance * 0.3;
    if (context.responseTime > 5e3) severity += 0.1;
    if (context.responseTime < 500) severity += 0.1;
    if (context.difficulty === "hard") severity -= 0.1;
    return Math.min(Math.max(severity, 0), 1);
  }
  /**
   * Coleta erros espaciais (posição incorreta)
   */
  collectSpatialError(targetPosition, selectedPosition, context) {
    const spatialError = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      targetPosition,
      selectedPosition,
      distance: this.calculateSpatialDistance(targetPosition, selectedPosition),
      direction: this.calculateDirection(targetPosition, selectedPosition),
      context: {
        gridSize: context.gridSize || "3x3",
        difficulty: context.difficulty || "medium",
        responseTime: context.responseTime || 0
      },
      severity: this.calculateSpatialErrorSeverity(targetPosition, selectedPosition, context)
    };
    this.errorData.spatialErrors.push(spatialError);
    this.detectSpatialPattern(spatialError);
    return spatialError;
  }
  /**
   * Coleta erros de atenção (cliques em áreas não-alvo)
   */
  collectAttentionError(clickPosition, validTargets, context) {
    const attentionError = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      clickPosition,
      validTargets,
      nearestTarget: this.findNearestTarget(clickPosition, validTargets),
      context: {
        distractorCount: context.distractorCount || 0,
        timeRemaining: context.timeRemaining || 0,
        currentLevel: context.currentLevel || 1
      },
      errorType: this.classifyAttentionError(clickPosition, validTargets),
      severity: this.calculateAttentionErrorSeverity(clickPosition, validTargets, context)
    };
    this.errorData.attentionErrors.push(attentionError);
    this.detectAttentionPattern(attentionError);
    return attentionError;
  }
  /**
   * Detecta padrões persistentes de erro de cor
   */
  detectPersistentColorError(confusionKey, confusionData) {
    if (!this.errorData.persistentErrors[confusionKey]) {
      this.errorData.persistentErrors[confusionKey] = [];
    }
    this.errorData.persistentErrors[confusionKey].push(confusionData);
    if (this.errorData.persistentErrors[confusionKey].length >= this.errorThresholds.persistent) {
      confusionData.isPersistent = true;
      this.flagForIntervention(confusionKey, "color_confusion");
    }
  }
  /**
   * Detecta problemas de percepção de cor
   */
  detectColorPerceptionIssues(confusionData) {
    const { targetColor, selectedColor } = confusionData;
    if (this.isPossibleColorBlindnessError(targetColor, selectedColor)) {
      const issueKey = "color_blindness_indicator";
      if (!this.errorData.colorPerceptionIssues[issueKey]) {
        this.errorData.colorPerceptionIssues[issueKey] = [];
      }
      this.errorData.colorPerceptionIssues[issueKey].push(confusionData);
    }
    if (this.isSaturationIssue(targetColor, selectedColor)) {
      const issueKey = "saturation_difficulty";
      if (!this.errorData.colorPerceptionIssues[issueKey]) {
        this.errorData.colorPerceptionIssues[issueKey] = [];
      }
      this.errorData.colorPerceptionIssues[issueKey].push(confusionData);
    }
  }
  /**
   * Analisa padrões de erro coletados
   */
  /**
   * Gera métricas de erro estruturadas
   */
  generateErrorMetrics() {
    const metrics = {
      colorConfusion: {
        total: Object.keys(this.errorData.colorConfusions).length,
        mostCommon: this.getMostCommonColorError(),
        severity: this.getAverageColorErrorSeverity()
      },
      spatialErrors: {
        total: this.errorData.spatialErrors.length,
        averageDistance: this.getAverageSpatialDistance(),
        commonDirections: this.getCommonSpatialDirections()
      },
      attentionErrors: {
        total: this.errorData.attentionErrors.length,
        severity: this.getAverageAttentionErrorSeverity(),
        patterns: this.getAttentionErrorPatterns()
      },
      perceptionIssues: {
        colorBlindnessIndicators: this.errorData.colorPerceptionIssues.color_blindness_indicator?.length || 0,
        saturationDifficulties: this.errorData.colorPerceptionIssues.saturation_difficulty?.length || 0
      },
      learningIndicators: this.generateLearningIndicators()
    };
    return metrics;
  }
  // Métodos auxiliares
  getColorGroup(color, colorGroups) {
    for (const [group, colors] of Object.entries(colorGroups)) {
      if (colors.includes(color.toLowerCase())) {
        return group;
      }
    }
    return "unknown";
  }
  /**
   * Encontra o erro mais comum
   */
  findMostCommonError() {
    let maxCount = 0;
    let mostCommonError = null;
    Object.entries(this.errorData.colorConfusions).forEach(([errorKey, errors]) => {
      if (errors.length > maxCount) {
        maxCount = errors.length;
        mostCommonError = errorKey;
      }
    });
    return {
      error: mostCommonError,
      count: maxCount
    };
  }
  /**
   * Calcula a severidade média dos erros
   */
  calculateAverageSeverity() {
    let totalSeverity = 0;
    let errorCount = 0;
    Object.values(this.errorData.colorConfusions).forEach((errors) => {
      errors.forEach((error) => {
        totalSeverity += error.severity;
        errorCount++;
      });
    });
    return errorCount > 0 ? totalSeverity / errorCount : 0;
  }
  /**
   * Calcula a severidade do erro de cor
   */
  /**
   * Coleta erros espaciais (posição incorreta)
   */
  /**
   * Coleta erros de atenção (cliques em áreas não-alvo)
   */
  // Métodos auxiliares
  /**
   * Gera métricas de erro estruturadas
   */
  updateConfusionFrequency(confusionKey) {
    const current = this.getErrorFrequency(confusionKey);
    return current + 1;
  }
  getErrorFrequency(confusionKey) {
    return this.errorData.colorConfusions[confusionKey]?.length || 0;
  }
  calculateSpatialDistance(pos1, pos2) {
    if (!pos1 || !pos2) return 0;
    return Math.sqrt(Math.pow(pos1.x - pos2.x, 2) + Math.pow(pos1.y - pos2.y, 2));
  }
  calculateDirection(from, to) {
    if (!from || !to) return "unknown";
    const dx = to.x - from.x;
    const dy = to.y - from.y;
    if (Math.abs(dx) > Math.abs(dy)) {
      return dx > 0 ? "right" : "left";
    } else {
      return dy > 0 ? "down" : "up";
    }
  }
  calculateSpatialErrorSeverity(targetPos, selectedPos, context) {
    const distance = this.calculateSpatialDistance(targetPos, selectedPos);
    const maxDistance = Math.sqrt(Math.pow(context.gridSize || 3, 2) * 2);
    return Math.min(distance / maxDistance, 1);
  }
  findNearestTarget(clickPos, targets) {
    if (!targets || targets.length === 0) return null;
    return targets.reduce((nearest, target) => {
      const distance = this.calculateSpatialDistance(clickPos, target.position);
      if (!nearest || distance < nearest.distance) {
        return { target, distance };
      }
      return nearest;
    }, null);
  }
  classifyAttentionError(clickPos, validTargets) {
    const nearest = this.findNearestTarget(clickPos, validTargets);
    if (!nearest) return "random";
    if (nearest.distance < 50) return "near-miss";
    if (nearest.distance < 100) return "adjacent";
    return "distant";
  }
  calculateAttentionErrorSeverity(clickPos, validTargets, context) {
    const nearest = this.findNearestTarget(clickPos, validTargets);
    if (!nearest) return 1;
    let severity = nearest.distance / 200;
    if (context.distractorCount > 5) severity *= 0.8;
    if (context.timeRemaining < 5e3) severity *= 1.2;
    return Math.min(severity, 1);
  }
  detectSpatialPattern(spatialError) {
    const recentSpatialErrors = this.errorData.spatialErrors.slice(-5);
    const commonDirection = this.getMostCommonDirection(recentSpatialErrors);
    if (commonDirection.frequency >= 3) {
      spatialError.patternDetected = `consistent_${commonDirection.direction}_bias`;
    }
  }
  detectAttentionPattern(attentionError) {
    const recentAttentionErrors = this.errorData.attentionErrors.slice(-5);
    const errorTypes = recentAttentionErrors.map((e) => e.errorType);
    if (errorTypes.filter((t) => t === "random").length >= 3) {
      attentionError.patternDetected = "attention_deficit_indicator";
    }
  }
  isPossibleColorBlindnessError(target, selected) {
    const colorBlindPatterns = [
      ["red", "green"],
      ["green", "red"],
      ["blue", "purple"],
      ["purple", "blue"]
    ];
    return colorBlindPatterns.some(
      (pattern) => pattern[0] === target.toLowerCase() && pattern[1] === selected.toLowerCase()
    );
  }
  isSaturationIssue(target, selected) {
    const lowSaturationColors = ["gray", "lightgray", "darkgray"];
    const highSaturationColors = ["red", "blue", "green", "yellow"];
    return lowSaturationColors.includes(target.toLowerCase()) && highSaturationColors.includes(selected.toLowerCase()) || highSaturationColors.includes(target.toLowerCase()) && lowSaturationColors.includes(selected.toLowerCase());
  }
  flagForIntervention(errorKey, errorType) {
    console.warn(`Padrão persistente detectado: ${errorKey} (${errorType})`);
  }
  getTotalErrorCount() {
    const colorErrors = Object.values(this.errorData.colorConfusions).reduce((sum, errors) => sum + errors.length, 0);
    return colorErrors + this.errorData.spatialErrors.length + this.errorData.attentionErrors.length;
  }
  identifyPersistentPatterns() {
    return Object.entries(this.errorData.persistentErrors).filter(([key, errors]) => errors.length >= this.errorThresholds.persistent).map(([key, errors]) => ({
      pattern: key,
      frequency: errors.length,
      severity: errors.reduce((sum, e) => sum + e.severity, 0) / errors.length
    }));
  }
  assessLearningProgress() {
    const recentErrors = this.getTimeWindowErrors(3e5);
    const olderErrors = this.getTimeWindowErrors(6e5, 3e5);
    return {
      errorReduction: olderErrors.length > 0 ? (olderErrors.length - recentErrors.length) / olderErrors.length : 0,
      improvementTrend: this.calculateImprovementTrend(),
      learningRate: this.calculateLearningRate()
    };
  }
  generateInterventionRecommendations() {
    const recommendations = [];
    const persistentPatterns = this.identifyPersistentPatterns();
    persistentPatterns.forEach((pattern) => {
      if (pattern.pattern.includes("color_confusion")) {
        recommendations.push({
          type: "color_training",
          priority: "high",
          description: `Treino específico para confusão: ${pattern.pattern}`
        });
      }
    });
    if (this.errorData.colorPerceptionIssues.color_blindness_indicator?.length > 2) {
      recommendations.push({
        type: "accessibility_adjustment",
        priority: "high",
        description: "Considerar ajustes para daltonismo"
      });
    }
    return recommendations;
  }
  getAllErrors() {
    const colorErrors = Object.values(this.errorData.colorConfusions).flat();
    return [...colorErrors, ...this.errorData.spatialErrors, ...this.errorData.attentionErrors];
  }
  getTimeWindowErrors(windowMs, offsetMs = 0) {
    const now = Date.now();
    const startTime = now - windowMs - offsetMs;
    const endTime = now - offsetMs;
    return this.getAllErrors().filter((error) => {
      const errorTime = new Date(error.timestamp).getTime();
      return errorTime >= startTime && errorTime <= endTime;
    });
  }
  calculateImprovementTrend() {
    const errorsByTime = this.getErrorsByTimeSlices(5);
    if (errorsByTime.length < 2) return 0;
    let improvements = 0;
    for (let i = 1; i < errorsByTime.length; i++) {
      if (errorsByTime[i] < errorsByTime[i - 1]) improvements++;
    }
    return improvements / (errorsByTime.length - 1);
  }
  calculateLearningRate() {
    const totalTime = Date.now() - this.sessionStartTime;
    const totalErrors = this.getTotalErrorCount();
    if (totalTime === 0) return 0;
    return Math.max(0, 1 - totalErrors / (totalTime / 6e4));
  }
  getErrorsByTimeSlices(slices) {
    const sessionDuration = Date.now() - this.sessionStartTime;
    const sliceDuration = sessionDuration / slices;
    const sliceErrors = [];
    for (let i = 0; i < slices; i++) {
      const sliceStart = this.sessionStartTime + i * sliceDuration;
      const sliceEnd = sliceStart + sliceDuration;
      const errorsInSlice = this.getAllErrors().filter((error) => {
        const errorTime = new Date(error.timestamp).getTime();
        return errorTime >= sliceStart && errorTime < sliceEnd;
      });
      sliceErrors.push(errorsInSlice.length);
    }
    return sliceErrors;
  }
  getMostCommonColorError() {
    const errorCounts = {};
    Object.entries(this.errorData.colorConfusions).forEach(([key, errors]) => {
      errorCounts[key] = errors.length;
    });
    return Object.entries(errorCounts).reduce(
      (max, [key, count]) => count > max.count ? { pattern: key, count } : max,
      { pattern: null, count: 0 }
    );
  }
  getAverageColorErrorSeverity() {
    const allColorErrors = Object.values(this.errorData.colorConfusions).flat();
    if (allColorErrors.length === 0) return 0;
    const totalSeverity = allColorErrors.reduce((sum, error) => sum + error.severity, 0);
    return totalSeverity / allColorErrors.length;
  }
  getAverageSpatialDistance() {
    if (this.errorData.spatialErrors.length === 0) return 0;
    const totalDistance = this.errorData.spatialErrors.reduce((sum, error) => sum + error.distance, 0);
    return totalDistance / this.errorData.spatialErrors.length;
  }
  getCommonSpatialDirections() {
    const directionCounts = {};
    this.errorData.spatialErrors.forEach((error) => {
      directionCounts[error.direction] = (directionCounts[error.direction] || 0) + 1;
    });
    return Object.entries(directionCounts).sort(([, a], [, b]) => b - a).slice(0, 3).map(([direction, count]) => ({ direction, count }));
  }
  getMostCommonDirection(errors) {
    const directionCounts = {};
    errors.forEach((error) => {
      directionCounts[error.direction] = (directionCounts[error.direction] || 0) + 1;
    });
    const mostCommon = Object.entries(directionCounts).reduce(
      (max, [dir, count]) => count > max.frequency ? { direction: dir, frequency: count } : max,
      { direction: null, frequency: 0 }
    );
    return mostCommon;
  }
  getAverageAttentionErrorSeverity() {
    if (this.errorData.attentionErrors.length === 0) return 0;
    const totalSeverity = this.errorData.attentionErrors.reduce((sum, error) => sum + error.severity, 0);
    return totalSeverity / this.errorData.attentionErrors.length;
  }
  getAttentionErrorPatterns() {
    const patterns = {};
    this.errorData.attentionErrors.forEach((error) => {
      if (error.patternDetected) {
        patterns[error.patternDetected] = (patterns[error.patternDetected] || 0) + 1;
      }
    });
    return patterns;
  }
  generateLearningIndicators() {
    return {
      adaptationRate: this.calculateAdaptationRate(),
      errorRecovery: this.calculateErrorRecoveryRate(),
      patternRecognition: this.calculatePatternRecognitionImprovement(),
      consistencyScore: this.calculateConsistencyScore()
    };
  }
  calculateAdaptationRate() {
    const errorsBySlice = this.getErrorsByTimeSlices(10);
    if (errorsBySlice.length < 3) return 0;
    const firstHalf = errorsBySlice.slice(0, 5).reduce((sum, count) => sum + count, 0);
    const secondHalf = errorsBySlice.slice(5).reduce((sum, count) => sum + count, 0);
    if (firstHalf === 0) return 1;
    return Math.max(0, (firstHalf - secondHalf) / firstHalf);
  }
  calculateErrorRecoveryRate() {
    let recoveryCount = 0;
    let totalErrors = 0;
    Object.values(this.errorData.colorConfusions).forEach((errors) => {
      for (let i = 0; i < errors.length - 1; i++) {
        totalErrors++;
        const currentError = errors[i];
        const nextError = errors[i + 1];
        if (nextError.severity < currentError.severity) {
          recoveryCount++;
        }
      }
    });
    return totalErrors > 0 ? recoveryCount / totalErrors : 0;
  }
  calculatePatternRecognitionImprovement() {
    const persistentPatterns = this.identifyPersistentPatterns();
    if (persistentPatterns.length === 0) return 1;
    let improvementCount = 0;
    persistentPatterns.forEach((pattern) => {
      const errors = this.errorData.persistentErrors[pattern.pattern];
      if (errors.length >= 2) {
        const recentSeverity = errors.slice(-2).reduce((sum, e) => sum + e.severity, 0) / 2;
        const olderSeverity = errors.slice(0, 2).reduce((sum, e) => sum + e.severity, 0) / 2;
        if (recentSeverity < olderSeverity) {
          improvementCount++;
        }
      }
    });
    return persistentPatterns.length > 0 ? improvementCount / persistentPatterns.length : 0;
  }
  calculateConsistencyScore() {
    const allErrors = this.getAllErrors();
    if (allErrors.length < 2) return 1;
    const severities = allErrors.map((e) => e.severity || 0);
    const mean = severities.reduce((sum, s) => sum + s, 0) / severities.length;
    const variance = severities.reduce((sum, s) => sum + Math.pow(s - mean, 2), 0) / severities.length;
    const standardDeviation = Math.sqrt(variance);
    return Math.max(0, 1 - standardDeviation / mean);
  }
  /**
   * Reset dos dados de erro (para nova sessão)
   */
  reset() {
    this.errorData = {
      colorConfusions: {},
      visualErrors: [],
      spatialErrors: [],
      attentionErrors: [],
      persistentErrors: {},
      errorClusters: [],
      learningIndicators: [],
      colorPerceptionIssues: {}
    };
    this.sessionStartTime = Date.now();
  }
  /**
   * Exporta dados para análise externa
   */
  exportData() {
    return {
      ...this.errorData,
      sessionDuration: Date.now() - this.sessionStartTime,
      analysis: this.analyzeErrorPatterns(),
      metrics: this.generateErrorMetrics()
    };
  }
}
class ColorMatchCollectorsHub {
  constructor() {
    this._collectors = {
      colorPerception: new ColorPerceptionCollector(),
      visualProcessing: new VisualProcessingCollector(),
      attentionalSelectivity: new AttentionalSelectivityCollector(),
      colorCognition: new ColorCognitionCollector(),
      errorPattern: new ErrorPatternCollector()
    };
    this.analysisHistory = [];
    this.currentSession = null;
    this.performanceBaseline = null;
  }
  /**
   * Getter para coletores - necessário para GameSpecificProcessors
   */
  get collectors() {
    return this._collectors;
  }
  /**
   * Processa uma interação individual do jogo
   * @param {Object} interactionData - Dados da interação
   * @returns {Promise<Object>} Resultados do processamento
   */
  async processInteraction(interactionData) {
    try {
      console.log("🎨 ColorMatch: Processando interação...", {
        action: interactionData.action,
        isCorrect: interactionData.isCorrect,
        responseTime: interactionData.responseTime
      });
      if (!this.validateInteractionData(interactionData)) {
        throw new Error("Dados de interação inválidos");
      }
      const collectorData = this.prepareInteractionData(interactionData);
      const collectionResults = await Promise.all([
        this._collectors.colorPerception.analyze(collectorData.colorPerception),
        this._collectors.visualProcessing.analyze(collectorData.visualProcessing),
        this._collectors.attentionalSelectivity.analyze(collectorData.attentionalSelectivity),
        this._collectors.colorCognition.analyze(collectorData.colorCognition),
        this._collectors.errorPattern.analyze(collectorData.errorPattern)
      ]);
      const results = {
        sessionId: interactionData.sessionId,
        timestamp: Date.now(),
        interactionId: `interaction_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        collectionResults: {
          colorPerception: collectionResults[0],
          visualProcessing: collectionResults[1],
          attentionalSelectivity: collectionResults[2],
          colorCognition: collectionResults[3],
          errorPattern: collectionResults[4]
        },
        processingTime: Date.now() - (interactionData.timestamp || Date.now())
      };
      console.log("✅ ColorMatch: Interação processada com sucesso");
      return results;
    } catch (error) {
      console.error("❌ ColorMatch: Erro ao processar interação:", error);
      return this.generateFallbackInteractionResult(interactionData, error);
    }
  }
  /**
   * Valida dados de interação
   */
  validateInteractionData(data) {
    const requiredFields = ["action", "sessionId", "timestamp"];
    return requiredFields.every((field) => data.hasOwnProperty(field));
  }
  /**
   * Prepara dados de interação para coletores
   */
  prepareInteractionData(interactionData) {
    const baseData = {
      sessionId: interactionData.sessionId,
      timestamp: interactionData.timestamp,
      responseTime: interactionData.responseTime || 0,
      isCorrect: interactionData.isCorrect || false,
      difficulty: interactionData.difficulty || "medium"
    };
    return {
      colorPerception: {
        ...baseData,
        targetColor: interactionData.targetColor,
        selectedColor: interactionData.selectedAnswer || interactionData.answer,
        colorData: {
          interactions: [{
            targetColor: interactionData.targetColor,
            selectedColor: interactionData.selectedAnswer || interactionData.answer,
            correct: interactionData.isCorrect,
            responseTime: interactionData.responseTime,
            timestamp: interactionData.timestamp
          }]
        }
      },
      visualProcessing: {
        ...baseData,
        visualData: {
          interactions: [{
            responseTime: interactionData.responseTime,
            correct: interactionData.isCorrect,
            complexity: this.determineVisualComplexity({ difficulty: interactionData.difficulty })
          }]
        }
      },
      attentionalSelectivity: {
        ...baseData,
        attentionData: {
          interactions: [{
            isTarget: interactionData.isCorrect,
            correct: interactionData.isCorrect,
            responseTime: interactionData.responseTime,
            requiresInhibition: interactionData.difficulty !== "easy"
          }]
        }
      },
      colorCognition: {
        ...baseData,
        cognitiveData: {
          interactions: [{
            taskType: "color_matching",
            complexity: this.determineCognitiveComplexity({ difficulty: interactionData.difficulty }),
            responseTime: interactionData.responseTime,
            correct: interactionData.isCorrect
          }]
        }
      },
      errorPattern: {
        ...baseData,
        errorData: {
          interactions: [{
            correct: interactionData.isCorrect,
            errorType: interactionData.isCorrect ? null : "color_mismatch",
            responseTime: interactionData.responseTime
          }]
        }
      }
    };
  }
  /**
   * Gera resultado de fallback para interação
   */
  generateFallbackInteractionResult(interactionData, error) {
    return {
      sessionId: interactionData.sessionId,
      timestamp: Date.now(),
      interactionId: `fallback_${Date.now()}`,
      error: error.message,
      collectionResults: {
        colorPerception: { status: "error", data: null },
        visualProcessing: { status: "error", data: null },
        attentionalSelectivity: { status: "error", data: null },
        colorCognition: { status: "error", data: null },
        errorPattern: { status: "error", data: null }
      },
      processingTime: 0
    };
  }
  /**
   * Executa análise completa usando todos os coletores
   */
  async runCompleteAnalysis(gameData) {
    try {
      console.log("🎨 ColorMatch: Iniciando análise completa...");
      if (!this.validateGameData(gameData)) {
        throw new Error("Dados do jogo inválidos para análise");
      }
      const startTime = Date.now();
      const collectorData = this.prepareCollectorData(gameData);
      if (!collectorData) {
        throw new Error("Falha na preparação dos dados para coletores");
      }
      console.log("🎨 ColorMatch: Dados preparados com sucesso, executando coletores...");
      const analysisPromises = [
        this._collectors.colorPerception.analyze(collectorData.colorPerception),
        this._collectors.visualProcessing.analyze(collectorData.visualProcessing),
        this._collectors.attentionalSelectivity.analyze(collectorData.attentionalSelectivity),
        this._collectors.colorCognition.analyze(collectorData.colorCognition)
      ];
      console.log("🎨 ColorMatch: Aguardando resultados dos coletores...");
      const [
        colorPerceptionResults,
        visualProcessingResults,
        attentionalSelectivityResults,
        colorCognitionResults
      ] = await Promise.all(analysisPromises);
      console.log("🎨 ColorMatch: Resultados dos coletores obtidos:", {
        colorPerception: !!colorPerceptionResults,
        visualProcessing: !!visualProcessingResults,
        attentionalSelectivity: !!attentionalSelectivityResults,
        colorCognition: !!colorCognitionResults
      });
      const analysisResults = {
        sessionId: gameData.sessionId || `session_${Date.now()}`,
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        analysisTime: Date.now() - startTime,
        gameData: {
          duration: gameData.sessionDuration,
          difficulty: gameData.difficulty,
          totalColors: gameData.totalColors,
          correctMatches: gameData.correctMatches,
          accuracy: gameData.accuracy
        },
        collectorsResults: {
          colorPerception: colorPerceptionResults,
          visualProcessing: visualProcessingResults,
          attentionalSelectivity: attentionalSelectivityResults,
          colorCognition: colorCognitionResults
        },
        integratedMetrics: this.calculateIntegratedMetrics({
          colorPerceptionResults,
          visualProcessingResults,
          attentionalSelectivityResults,
          colorCognitionResults
        }),
        recommendations: this.generateIntegratedRecommendations({
          colorPerceptionResults,
          visualProcessingResults,
          attentionalSelectivityResults,
          colorCognitionResults
        }),
        colorMatchProfile: this.generateColorMatchProfile({
          colorPerceptionResults,
          visualProcessingResults,
          attentionalSelectivityResults,
          colorCognitionResults
        })
      };
      this.analysisHistory.push(analysisResults);
      console.log(`✅ ColorMatch: Análise completa finalizada em ${analysisResults.analysisTime}ms`);
      console.log("🎨 ColorMatch: Métricas integradas:", {
        overallColorPerception: analysisResults.integratedMetrics.overallColorPerception,
        colorDiscrimination: analysisResults.collectorsResults.colorPerception.colorDiscrimination,
        visualProcessingSpeed: analysisResults.collectorsResults.visualProcessing.processingSpeed,
        attentionalFocus: analysisResults.collectorsResults.attentionalSelectivity.selectiveAttention,
        cognitiveComplexity: analysisResults.collectorsResults.colorCognition.colorCategorization
      });
      return analysisResults;
    } catch (error) {
      console.error("❌ ColorMatch: Erro na análise completa:", error);
      console.error("🎨 ColorMatch: Stack trace:", error.stack);
      return this.generateFallbackAnalysis(gameData, error);
    }
  }
  /**
   * Prepara dados específicos para cada coletor - Nova versão para ColorMatch
   */
  prepareCollectorData(rawData) {
    if (!this.validateGameData(rawData)) {
      return null;
    }
    const difficultyConfig = {
      "easy": { expectedColors: 3, expectedItems: 6, timeLimit: null },
      "medium": { expectedColors: 5, expectedItems: 9, timeLimit: 90 },
      "hard": { expectedColors: 8, expectedItems: 12, timeLimit: 60 }
    };
    const config = difficultyConfig[rawData.difficulty];
    const currentTime = Date.now();
    const responseTime = rawData.timestamp ? currentTime - rawData.timestamp : 0;
    const gridAnalysis = this.analyzeGridState(rawData.gameGrid, rawData.targetColor);
    const progressAnalysis = this.analyzeProgress(rawData.selectedItems, config);
    const collectorData = {
      // Dados básicos
      sessionId: rawData.sessionId,
      timestamp: rawData.timestamp || currentTime,
      difficulty: rawData.difficulty,
      // Dados específicos do ColorMatch
      targetColor: rawData.targetColor,
      selectedItems: rawData.selectedItems,
      gameGrid: rawData.gameGrid,
      // Métricas calculadas
      responseTime,
      isCorrectSelection: gridAnalysis.isCorrect,
      itemsRemaining: gridAnalysis.itemsRemaining,
      totalCorrectItems: gridAnalysis.totalCorrectItems,
      progressPercentage: progressAnalysis.percentage,
      sessionDuration: progressAnalysis.sessionDuration,
      // Métricas de performance
      accuracy: progressAnalysis.accuracy,
      averageResponseTime: progressAnalysis.averageResponseTime,
      errorsCount: progressAnalysis.errorsCount,
      // Dados para análise específica
      gridPosition: rawData.gridPosition || null,
      previousSelection: rawData.previousSelection || null,
      timeRemaining: rawData.timeRemaining || null,
      // Contexto sensorial
      tactileUsed: rawData.tactile || false,
      audioUsed: rawData.audioCue || false
    };
    console.log(`🎨 ColorMatch: Dados preparados para ${rawData.difficulty}:`, {
      targetColor: collectorData.targetColor,
      itemsRemaining: collectorData.itemsRemaining,
      accuracy: collectorData.accuracy
    });
    return {
      colorPerception: {
        colorData: {
          interactions: [{
            targetColor: collectorData.targetColor,
            selectedColor: rawData.selectedColor || "unknown",
            correct: collectorData.isCorrectSelection,
            responseTime: collectorData.responseTime,
            timestamp: collectorData.timestamp
          }]
        },
        sessionDuration: collectorData.sessionDuration,
        difficulty: collectorData.difficulty
      },
      visualProcessing: {
        visualData: {
          interactions: [{
            responseTime: collectorData.responseTime,
            correct: collectorData.isCorrectSelection,
            elementPosition: collectorData.gridPosition,
            complexity: this.determineVisualComplexity({
              difficulty: collectorData.difficulty,
              gridSize: gridAnalysis.gridSize
            })
          }],
          scanningPatterns: [],
          backgroundComplexity: collectorData.difficulty
        },
        sessionDuration: collectorData.sessionDuration
      },
      attentionalSelectivity: {
        attentionData: {
          interactions: [{
            isTarget: collectorData.isCorrectSelection,
            correct: collectorData.isCorrectSelection,
            responseTime: collectorData.responseTime,
            isRelevant: collectorData.isCorrectSelection,
            requiresInhibition: this.requiresInhibition({
              distractors: gridAnalysis.gridSize - gridAnalysis.totalCorrectItems
            }),
            hasConflict: gridAnalysis.gridSize > 6,
            requiresTopDownControl: collectorData.difficulty !== "easy",
            requiresFlexibility: collectorData.errorsCount > 0,
            isMultitasking: collectorData.timeRemaining !== null,
            memoryLoad: this.calculateMemoryLoad({
              colors: config.expectedColors
            })
          }],
          distractors: gridAnalysis.gridSize - gridAnalysis.totalCorrectItems,
          contextChanges: []
        },
        sessionDuration: collectorData.sessionDuration
      },
      colorCognition: {
        cognitiveData: {
          interactions: [{
            taskType: "color_matching",
            complexity: this.determineCognitiveComplexity({
              difficulty: collectorData.difficulty
            }),
            requiresNaming: true,
            hasSemanticComponent: true,
            responseTime: collectorData.responseTime,
            correct: collectorData.isCorrectSelection,
            targetColor: collectorData.targetColor,
            selectedObject: rawData.selectedItem || null,
            association: "color_category",
            conceptualAspect: "discrimination",
            context: collectorData.difficulty,
            creativityRating: 0.5,
            strategy: "systematic",
            usedVerbalization: false,
            usedVisualization: true,
            usedAssociation: true,
            usedCategorization: true,
            usedSystematicApproach: collectorData.difficulty === "hard"
          }]
        },
        sessionDuration: collectorData.sessionDuration,
        difficulty: collectorData.difficulty
      }
    };
  }
  /**
   * Calcula métricas integradas combinando resultados de todos os coletores
   */
  calculateIntegratedMetrics(results) {
    const { colorPerceptionResults, visualProcessingResults, attentionalSelectivityResults, colorCognitionResults } = results;
    return {
      // Pontuação geral de percepção cromática
      overallColorPerception: this.calculateOverallColorPerception(results),
      // Índice de processamento visual
      visualProcessingIndex: (visualProcessingResults.processingSpeed + visualProcessingResults.visualScanning + visualProcessingResults.patternRecognition + visualProcessingResults.visualSearch) / 4,
      // Índice de atenção seletiva
      attentionalIndex: (attentionalSelectivityResults.selectiveAttention + attentionalSelectivityResults.focusedAttention + attentionalSelectivityResults.inhibitoryControl + attentionalSelectivityResults.distractorResistance) / 4,
      // Índice cognitivo cromático
      colorCognitionIndex: (colorCognitionResults.colorCategorization + colorCognitionResults.colorSemantics + colorCognitionResults.colorMemory + colorCognitionResults.processingSpeed) / 4,
      // Métricas específicas do ColorMatch
      colorDiscriminationAbility: colorPerceptionResults.colorDiscrimination,
      colorMemoryCapacity: colorCognitionResults.colorMemory,
      visualAttentionEfficiency: this.calculateVisualAttentionEfficiency(results),
      chromaticProcessingSpeed: this.calculateChromaticProcessingSpeed(results),
      // Potencial de melhoria específico
      improvementPotential: this.calculateColorMatchImprovementPotential(results),
      // Estabilidade perceptual
      perceptualStability: this.calculatePerceptualStability(results)
    };
  }
  calculateOverallColorPerception(results) {
    const weights = {
      perception: 0.35,
      // Percepção de cores
      processing: 0.25,
      // Processamento visual
      attention: 0.2,
      // Atenção seletiva
      cognition: 0.2
      // Cognição cromática
    };
    const perceptionScore = (results.colorPerceptionResults.colorDiscrimination + results.colorPerceptionResults.huePerception + results.colorPerceptionResults.perceptualAccuracy) / 3;
    const processingScore = (results.visualProcessingResults.processingSpeed + results.visualProcessingResults.processingEfficiency) / 2;
    const attentionScore = (results.attentionalSelectivityResults.selectiveAttention + results.attentionalSelectivityResults.focusedAttention) / 2;
    const cognitionScore = (results.colorCognitionResults.colorCategorization + results.colorCognitionResults.processingSpeed) / 2;
    return perceptionScore * weights.perception + processingScore * weights.processing + attentionScore * weights.attention + cognitionScore * weights.cognition;
  }
  calculateVisualAttentionEfficiency(results) {
    const visualEfficiency = results.visualProcessingResults.processingEfficiency || 0.7;
    const attentionalEfficiency = results.attentionalSelectivityResults.filteringEfficiency || 0.7;
    return (visualEfficiency + attentionalEfficiency) / 2;
  }
  calculateChromaticProcessingSpeed(results) {
    const perceptionSpeed = 1 - (results.colorPerceptionResults.adaptationEfficiency || 0.3);
    const processingSpeed = results.visualProcessingResults.processingSpeed || 0.7;
    const cognitionSpeed = results.colorCognitionResults.processingSpeed || 0.7;
    return (perceptionSpeed + processingSpeed + cognitionSpeed) / 3;
  }
  calculateColorMatchImprovementPotential(results) {
    const currentPerformance = this.calculateOverallColorPerception(results);
    const weakestAreas = this.identifyWeakestAreas(results);
    let potential = 1 - currentPerformance;
    if (weakestAreas.length >= 3) {
      potential *= 1.3;
    } else if (weakestAreas.length <= 1) {
      potential *= 0.8;
    }
    return Math.max(0, Math.min(1, potential));
  }
  calculatePerceptualStability(results) {
    const perceptionStability = results.colorPerceptionResults.colorConstancy || 0.7;
    const attentionStability = results.attentionalSelectivityResults.vigilance || 0.7;
    const processingStability = 1 - (results.visualProcessingResults.processingBottlenecks?.length || 0) * 0.2;
    return (perceptionStability + attentionStability + processingStability) / 3;
  }
  identifyWeakestAreas(results) {
    const areas = {
      colorDiscrimination: results.colorPerceptionResults.colorDiscrimination,
      visualProcessing: results.visualProcessingResults.processingEfficiency,
      attentionalControl: results.attentionalSelectivityResults.executiveAttention,
      colorCognition: results.colorCognitionResults.colorCategorization
    };
    return Object.entries(areas).filter(([area, score]) => score < 0.6).map(([area]) => area);
  }
  /**
   * Gera recomendações integradas baseadas nos resultados de todos os coletores
   */
  generateIntegratedRecommendations(results) {
    const recommendations = [];
    if (results.colorPerceptionResults.colorDiscrimination < 0.6) {
      recommendations.push({
        category: "color_perception",
        priority: "high",
        title: "Treino de Discriminação Cromática",
        description: "Exercícios específicos para melhorar a capacidade de distinguir cores similares",
        techniques: ["gradientes de cor", "jogos de diferenciação", "treino de contraste"],
        targetArea: "discriminação cromática"
      });
    }
    if (results.colorPerceptionResults.huePerception < 0.5) {
      recommendations.push({
        category: "color_perception",
        priority: "high",
        title: "Desenvolvimento da Percepção de Matiz",
        description: "Treino focado na identificação e diferenciação de matizes",
        techniques: ["círculo cromático", "sequências de matiz", "identificação de temperatura"],
        targetArea: "percepção de matiz"
      });
    }
    if (results.visualProcessingResults.processingSpeed < 0.5) {
      recommendations.push({
        category: "visual_processing",
        priority: "high",
        title: "Aceleração do Processamento Visual",
        description: "Exercícios para aumentar a velocidade de processamento visual",
        techniques: ["tarefas cronometradas", "pressão temporal gradual", "automatização"],
        targetArea: "velocidade de processamento"
      });
    }
    if (results.visualProcessingResults.visualScanning < 0.6) {
      recommendations.push({
        category: "visual_processing",
        priority: "medium",
        title: "Otimização da Varredura Visual",
        description: "Treino de padrões eficientes de varredura visual",
        techniques: ["padrões sistemáticos", "estratégias de busca", "mapas visuais"],
        targetArea: "varredura visual"
      });
    }
    if (results.attentionalSelectivityResults.selectiveAttention < 0.6) {
      recommendations.push({
        category: "attention",
        priority: "high",
        title: "Fortalecimento da Atenção Seletiva",
        description: "Exercícios para melhorar a capacidade de filtrar informações irrelevantes",
        techniques: ["filtragem progressiva", "foco dirigido", "eliminação de distratores"],
        targetArea: "atenção seletiva"
      });
    }
    if (results.attentionalSelectivityResults.inhibitoryControl < 0.5) {
      recommendations.push({
        category: "attention",
        priority: "high",
        title: "Desenvolvimento do Controle Inibitório",
        description: "Treino para fortalecer a capacidade de suprimir respostas inadequadas",
        techniques: ["tarefas stop-signal", "controle de impulsos", "pausa reflexiva"],
        targetArea: "controle inibitório"
      });
    }
    if (results.colorCognitionResults.colorCategorization < 0.6) {
      recommendations.push({
        category: "color_cognition",
        priority: "medium",
        title: "Aprimoramento da Categorização Cromática",
        description: "Exercícios para melhorar a organização conceitual de cores",
        techniques: ["classificação hierárquica", "agrupamento semântico", "taxonomias cromáticas"],
        targetArea: "categorização"
      });
    }
    if (results.colorCognitionResults.colorMemory < 0.5) {
      recommendations.push({
        category: "color_cognition",
        priority: "medium",
        title: "Fortalecimento da Memória Cromática",
        description: "Treino específico para melhorar a retenção de informações sobre cores",
        techniques: ["associações mnemônicas", "repetição espaçada", "contextualização"],
        targetArea: "memória cromática"
      });
    }
    const weakestAreas = this.identifyWeakestAreas(results);
    if (weakestAreas.length >= 2) {
      recommendations.push({
        category: "integrated",
        priority: "critical",
        title: "Programa Integrado de Treino Cromático",
        description: "Programa abrangente combinando múltiplas áreas de desenvolvimento",
        techniques: ["treino multimodal", "progressão gradual", "feedback contínuo"],
        targetArea: "desenvolvimento integrado"
      });
    }
    return recommendations.sort((a, b) => {
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }
  /**
   * Gera perfil específico do ColorMatch
   */
  generateColorMatchProfile(results) {
    const profile = {
      overallLevel: this.determineOverallLevel(results),
      strengths: this.identifyStrengths(results),
      weaknesses: this.identifyWeaknesses(results),
      dominantProcessingStyle: this.identifyDominantProcessingStyle(results),
      colorPerceptionProfile: this.generateColorPerceptionProfile(results),
      recommendations: this.generateProfileRecommendations(results)
    };
    return profile;
  }
  determineOverallLevel(results) {
    const overallScore = this.calculateOverallColorPerception(results);
    if (overallScore >= 0.85) return "superior";
    if (overallScore >= 0.7) return "above_average";
    if (overallScore >= 0.55) return "average";
    if (overallScore >= 0.4) return "below_average";
    return "needs_improvement";
  }
  identifyStrengths(results) {
    const areas = {
      "Discriminação Cromática": results.colorPerceptionResults.colorDiscrimination,
      "Processamento Visual": results.visualProcessingResults.processingEfficiency,
      "Atenção Seletiva": results.attentionalSelectivityResults.selectiveAttention,
      "Cognição Cromática": results.colorCognitionResults.colorCategorization,
      "Velocidade de Processamento": results.visualProcessingResults.processingSpeed,
      "Memória Cromática": results.colorCognitionResults.colorMemory
    };
    return Object.entries(areas).filter(([area, score]) => score >= 0.75).map(([area, score]) => ({ area, score })).sort((a, b) => b.score - a.score);
  }
  identifyWeaknesses(results) {
    const areas = {
      "Discriminação Cromática": results.colorPerceptionResults.colorDiscrimination,
      "Processamento Visual": results.visualProcessingResults.processingEfficiency,
      "Atenção Seletiva": results.attentionalSelectivityResults.selectiveAttention,
      "Cognição Cromática": results.colorCognitionResults.colorCategorization,
      "Velocidade de Processamento": results.visualProcessingResults.processingSpeed,
      "Memória Cromática": results.colorCognitionResults.colorMemory
    };
    return Object.entries(areas).filter(([area, score]) => score < 0.6).map(([area, score]) => ({ area, score })).sort((a, b) => a.score - b.score);
  }
  identifyDominantProcessingStyle(results) {
    const styles2 = {
      visual: results.visualProcessingResults.processingEfficiency,
      cognitive: results.colorCognitionResults.processingSpeed,
      attentional: results.attentionalSelectivityResults.executiveAttention,
      perceptual: results.colorPerceptionResults.perceptualAccuracy
    };
    const dominantStyle = Object.entries(styles2).sort((a, b) => b[1] - a[1])[0];
    return {
      style: dominantStyle[0],
      strength: dominantStyle[1],
      description: this.getProcessingStyleDescription(dominantStyle[0])
    };
  }
  getProcessingStyleDescription(style) {
    const descriptions = {
      visual: "Processamento predominantemente visual - responde bem a estímulos visuais claros",
      cognitive: "Processamento predominantemente cognitivo - utiliza estratégias analíticas",
      attentional: "Processamento predominantemente atencional - foca na seleção de informações",
      perceptual: "Processamento predominantemente perceptual - confia na intuição perceptiva"
    };
    return descriptions[style] || "Estilo misto de processamento";
  }
  generateColorPerceptionProfile(results) {
    const perception = results.colorPerceptionResults;
    return {
      discriminationLevel: this.categorizeScore(perception.colorDiscrimination),
      huePerceptionLevel: this.categorizeScore(perception.huePerception),
      saturationSensitivity: this.categorizeScore(perception.saturationSensitivity),
      brightnessDiscrimination: this.categorizeScore(perception.brightnessDiscrimination),
      colorConstancy: this.categorizeScore(perception.colorConstancy),
      dominantColorPreferences: perception.dominantColorPreferences || [],
      confusionPatterns: perception.colorConfusionMatrix || {}
    };
  }
  categorizeScore(score) {
    if (score >= 0.85) return "excelente";
    if (score >= 0.7) return "bom";
    if (score >= 0.55) return "médio";
    if (score >= 0.4) return "baixo";
    return "crítico";
  }
  generateProfileRecommendations(results) {
    const overallLevel = this.determineOverallLevel(results);
    const weaknesses = this.identifyWeaknesses(results);
    const recommendations = [];
    if (overallLevel === "needs_improvement") {
      recommendations.push("Programa intensivo de treino cromático recomendado");
    }
    if (weaknesses.length >= 3) {
      recommendations.push("Foco em desenvolvimento de habilidades fundamentais");
    }
    if (results.colorPerceptionResults.colorDiscrimination < 0.5) {
      recommendations.push("Avaliação oftalmológica recomendada para descartar problemas visuais");
    }
    return recommendations;
  }
  /**
   * Inicia uma nova sessão de coleta
   */
  startSession(sessionConfig = {}) {
    const sessionId = sessionConfig.sessionId || `colormatch_session_${Date.now()}`;
    this.currentSession = {
      id: sessionId,
      startTime: Date.now(),
      config: sessionConfig,
      collectedData: [],
      status: "active",
      gameType: "ColorMatch"
    };
    console.log(`🎨 Sessão ColorMatch ${sessionId} iniciada`);
    return this.currentSession;
  }
  /**
   * Coleta dados específicos do ColorMatch
   */
  collectColorMatchData(gameData) {
    if (!this.currentSession) {
      throw new Error("Nenhuma sessão ativa. Inicie uma sessão primeiro.");
    }
    const dataPoint = {
      timestamp: Date.now(),
      relativeTime: Date.now() - this.currentSession.startTime,
      data: gameData,
      type: this.identifyColorMatchDataType(gameData)
    };
    this.currentSession.collectedData.push(dataPoint);
    return {
      sessionId: this.currentSession.id,
      totalDataPoints: this.currentSession.collectedData.length,
      dataPoint
    };
  }
  identifyColorMatchDataType(gameData) {
    if (gameData.colorDiscrimination) return "color_perception_data";
    if (gameData.visualScanning) return "visual_processing_data";
    if (gameData.attentionMetrics) return "attention_data";
    if (gameData.cognitiveMetrics) return "cognition_data";
    return "general_colormatch_data";
  }
  /**
   * Gera relatório abrangente da sessão ColorMatch
   */
  generateComprehensiveReport() {
    if (!this.currentSession) {
      throw new Error("Nenhuma sessão ativa para gerar relatório.");
    }
    return {
      sessionInfo: {
        id: this.currentSession.id,
        gameType: "ColorMatch",
        startTime: this.currentSession.startTime,
        currentTime: Date.now(),
        duration: Date.now() - this.currentSession.startTime,
        status: this.currentSession.status
      },
      dataCollection: {
        totalDataPoints: this.currentSession.collectedData.length,
        dataTypes: this.categorizeColorMatchData(),
        qualityMetrics: this.assessColorMatchDataQuality(),
        colorSpecificMetrics: this.extractColorSpecificMetrics()
      },
      collectors: {
        colorPerception: { status: "active", type: "ColorPerceptionCollector" },
        visualProcessing: { status: "active", type: "VisualProcessingCollector" },
        attentionalSelectivity: { status: "active", type: "AttentionalSelectivityCollector" },
        colorCognition: { status: "active", type: "ColorCognitionCollector" }
      },
      recommendations: this.generateSessionRecommendations()
    };
  }
  categorizeColorMatchData() {
    const categories = {};
    this.currentSession.collectedData.forEach((point) => {
      categories[point.type] = (categories[point.type] || 0) + 1;
    });
    return categories;
  }
  assessColorMatchDataQuality() {
    const dataPoints = this.currentSession.collectedData;
    const quality = {
      completeness: 0,
      consistency: 0,
      colorRelevance: 0,
      overall: 0
    };
    if (dataPoints.length === 0) return quality;
    const completePoints = dataPoints.filter(
      (point) => point.data && (point.data.targetColor || point.data.selectedColor || point.data.colorResponse)
    ).length;
    quality.completeness = completePoints / dataPoints.length;
    const consistentPoints = dataPoints.filter(
      (point) => point.timestamp && point.data && point.type
    ).length;
    quality.consistency = consistentPoints / dataPoints.length;
    const colorRelevantPoints = dataPoints.filter(
      (point) => point.type.includes("color") || point.data && (point.data.targetColor || point.data.colorResponse)
    ).length;
    quality.colorRelevance = colorRelevantPoints / dataPoints.length;
    quality.overall = (quality.completeness + quality.consistency + quality.colorRelevance) / 3;
    return quality;
  }
  extractColorSpecificMetrics() {
    const dataPoints = this.currentSession.collectedData;
    const metrics = {
      colorsProcessed: /* @__PURE__ */ new Set(),
      avgResponseTime: 0,
      accuracyByColor: {},
      mostChallengingColor: null,
      easiestColor: null
    };
    const responseTimes = [];
    dataPoints.forEach((point) => {
      const data = point.data;
      if (data.targetColor) {
        metrics.colorsProcessed.add(data.targetColor);
        if (!metrics.accuracyByColor[data.targetColor]) {
          metrics.accuracyByColor[data.targetColor] = { correct: 0, total: 0 };
        }
        metrics.accuracyByColor[data.targetColor].total++;
        if (data.correct) {
          metrics.accuracyByColor[data.targetColor].correct++;
        }
        if (data.responseTime) {
          responseTimes.push(data.responseTime);
        }
      }
    });
    metrics.colorsProcessed = metrics.colorsProcessed.size;
    if (responseTimes.length > 0) {
      metrics.avgResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    }
    const colorAccuracies = Object.entries(metrics.accuracyByColor).map(([color, stats]) => ({
      color,
      accuracy: stats.total > 0 ? stats.correct / stats.total : 0,
      total: stats.total
    })).filter((item) => item.total >= 2);
    if (colorAccuracies.length > 0) {
      colorAccuracies.sort((a, b) => a.accuracy - b.accuracy);
      metrics.mostChallengingColor = colorAccuracies[0];
      metrics.easiestColor = colorAccuracies[colorAccuracies.length - 1];
    }
    return metrics;
  }
  generateSessionRecommendations() {
    const recommendations = [];
    const dataLength = this.currentSession.collectedData.length;
    if (dataLength === 0) {
      recommendations.push({
        type: "data_collection",
        priority: "high",
        description: "Nenhum dado cromático coletado ainda. Inicie atividades para coleta de dados específicos de cores."
      });
    } else if (dataLength < 10) {
      recommendations.push({
        type: "data_collection",
        priority: "medium",
        description: "Poucos dados cromáticos coletados. Continue a atividade para obter análise mais precisa da percepção de cores."
      });
    } else {
      recommendations.push({
        type: "analysis_ready",
        priority: "low",
        description: "Dados cromáticos suficientes coletados. Pronto para análise detalhada da percepção e cognição de cores."
      });
    }
    const colorMetrics = this.extractColorSpecificMetrics();
    if (colorMetrics.mostChallengingColor) {
      recommendations.push({
        type: "color_specific",
        priority: "medium",
        description: `Cor mais desafiadora identificada: ${colorMetrics.mostChallengingColor.color}. Considere treino específico.`
      });
    }
    return recommendations;
  }
  /**
   * Valida os dados do jogo antes da análise
   */
  validateGameData(gameData) {
    const requiredFields = [
      "selectedItems",
      "gameGrid",
      "difficulty",
      "targetColor",
      "timestamp",
      "sessionId"
    ];
    if (!gameData || typeof gameData !== "object") {
      console.warn("🎨 ColorMatch: Dados inválidos recebidos");
      return false;
    }
    for (const field of requiredFields) {
      if (!gameData.hasOwnProperty(field)) {
        console.warn(`🎨 ColorMatch: Campo obrigatório ausente: ${field}`);
        return false;
      }
    }
    if (!Array.isArray(gameData.selectedItems) || !Array.isArray(gameData.gameGrid)) {
      console.warn("🎨 ColorMatch: selectedItems e gameGrid devem ser arrays");
      return false;
    }
    const validDifficulties = ["easy", "medium", "hard"];
    if (!validDifficulties.includes(gameData.difficulty)) {
      console.warn(`🎨 ColorMatch: Dificuldade inválida: ${gameData.difficulty}`);
      return false;
    }
    console.log(`🎨 ColorMatch: Dados validados para dificuldade ${gameData.difficulty}`);
    return true;
  }
  /**
   * Analisa o estado atual do grid
   */
  analyzeGridState(gameGrid, targetColor) {
    if (!Array.isArray(gameGrid)) {
      return { isCorrect: false, itemsRemaining: 0, totalCorrectItems: 0 };
    }
    let totalCorrectItems = 0;
    let itemsRemaining = 0;
    gameGrid.forEach((item) => {
      if (item && item.color === targetColor) {
        totalCorrectItems++;
        if (!item.selected) {
          itemsRemaining++;
        }
      }
    });
    return {
      isCorrect: itemsRemaining >= 0,
      itemsRemaining,
      totalCorrectItems,
      gridSize: gameGrid.length
    };
  }
  /**
   * Analisa o progresso da sessão
   */
  analyzeProgress(selectedItems, phaseConfig) {
    if (!Array.isArray(selectedItems)) {
      return { percentage: 0, sessionDuration: 0, accuracy: 0, averageResponseTime: 0, errorsCount: 0 };
    }
    const totalSelections = selectedItems.length;
    const correctSelections = selectedItems.filter((item) => item.correct === true).length;
    const errorSelections = totalSelections - correctSelections;
    const timestamps = selectedItems.map((item) => item.timestamp).filter((ts) => ts && !isNaN(ts)).sort((a, b) => a - b);
    const sessionDuration = timestamps.length > 1 ? timestamps[timestamps.length - 1] - timestamps[0] : 0;
    const responseTimes = selectedItems.map((item) => item.responseTime).filter((rt) => rt && rt > 0);
    const averageResponseTime = responseTimes.length > 0 ? responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length : 0;
    const expectedItems = phaseConfig ? phaseConfig.expectedItems / phaseConfig.expectedColors : 3;
    const progressPercentage = Math.min(correctSelections / expectedItems * 100, 100);
    return {
      percentage: progressPercentage,
      sessionDuration,
      accuracy: totalSelections > 0 ? correctSelections / totalSelections * 100 : 0,
      averageResponseTime,
      errorsCount: errorSelections
    };
  }
  /**
   * Gera análise de fallback em caso de erro
   */
  generateFallbackAnalysis(gameData, error) {
    return {
      sessionId: gameData?.sessionId || `fallback_colormatch_${Date.now()}`,
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      analysisTime: 0,
      error: error.message,
      status: "fallback",
      gameType: "ColorMatch",
      collectorsResults: {
        colorPerception: { colorDiscrimination: 0.5, huePerception: 0.5, perceptualAccuracy: 0.5 },
        visualProcessing: { processingSpeed: 0.5, processingEfficiency: 0.5 },
        attentionalSelectivity: { selectiveAttention: 0.5, focusedAttention: 0.5 },
        colorCognition: { colorCategorization: 0.5, processingSpeed: 0.5 }
      },
      integratedMetrics: {
        overallColorPerception: 0.5,
        improvementPotential: 0.5
      },
      recommendations: [{
        category: "system",
        priority: "critical",
        title: "Erro na Análise ColorMatch",
        description: "Houve um problema na análise cromática. Tente novamente ou verifique os dados fornecidos.",
        techniques: ["verificação de dados", "nova tentativa"]
      }]
    };
  }
  /**
   * Análise completa da sessão - FASE 1.2
   * Processa todos os dados coletados durante a sessão para relatório final
   */
  async analyzeCompleteSession() {
    try {
      if (!this.currentSession || !this.currentSession.active) {
        console.warn("🎨 ColorMatch: Nenhuma sessão ativa para análise");
        return null;
      }
      console.log("🎨 ColorMatch: Iniciando análise completa da sessão...");
      const startTime = Date.now();
      const sessionInteractions = this.currentSession.interactions || [];
      const sessionConfig = this.currentSession.config || {};
      const phaseConfig = this.currentSession.phaseConfig || {};
      if (sessionInteractions.length === 0) {
        console.warn("🎨 ColorMatch: Sessão sem interações para análise");
        return this.generateEmptySessionAnalysis();
      }
      const sessionData = {
        sessionId: this.currentSession.sessionId,
        timestamp: Date.now(),
        difficulty: sessionConfig.difficulty,
        targetColor: "session_complete",
        // Indica análise de sessão completa
        selectedItems: sessionInteractions.map((i) => ({
          color: i.selectedColor,
          correct: i.isCorrect,
          timestamp: i.timestamp,
          responseTime: i.responseTime
        })),
        gameGrid: [],
        // Grid não é relevante para análise de sessão
        // Métricas consolidadas da sessão
        sessionMetrics: this.calculateSessionMetrics(sessionInteractions),
        phaseConfig,
        totalInteractions: sessionInteractions.length,
        sessionDuration: Date.now() - this.currentSession.startTime,
        // Contexto da sessão
        gameVersion: this.currentSession.gameVersion || "2.0",
        userSettings: this.currentSession.userSettings || {}
      };
      console.log("🎨 ColorMatch: Dados da sessão consolidados:", {
        totalInteractions: sessionData.totalInteractions,
        sessionDuration: sessionData.sessionDuration,
        difficulty: sessionData.difficulty,
        accuracy: sessionData.sessionMetrics.overallAccuracy
      });
      const analysisResult = await this.runCompleteAnalysis(sessionData);
      analysisResult.sessionAnalysis = {
        sessionId: this.currentSession.sessionId,
        totalDuration: sessionData.sessionDuration,
        interactionsCount: sessionData.totalInteractions,
        overallMetrics: sessionData.sessionMetrics,
        progressionAnalysis: this.analyzeProgressionThroughSession(sessionInteractions),
        learningCurve: this.calculateLearningCurve(sessionInteractions),
        consistencyMetrics: this.calculateConsistencyMetrics(sessionInteractions),
        finalRecommendations: this.generateSessionRecommendations(sessionData.sessionMetrics)
      };
      console.log(`✅ ColorMatch: Análise completa da sessão finalizada em ${Date.now() - startTime}ms`);
      console.log("🎨 ColorMatch: Resumo da sessão:", {
        overallAccuracy: analysisResult.sessionAnalysis.overallMetrics.overallAccuracy,
        learningCurveSlope: analysisResult.sessionAnalysis.learningCurve.slope,
        consistencyScore: analysisResult.sessionAnalysis.consistencyMetrics.consistencyScore
      });
      return analysisResult;
    } catch (error) {
      console.error("❌ ColorMatch: Erro na análise completa da sessão:", error);
      return this.generateFallbackSessionAnalysis(error);
    }
  }
  /**
   * Calcula métricas consolidadas da sessão
   */
  calculateSessionMetrics(interactions) {
    if (interactions.length === 0) {
      return { overallAccuracy: 0, averageResponseTime: 0, totalCorrect: 0, totalAttempts: 0 };
    }
    const totalAttempts = interactions.length;
    const correctInteractions = interactions.filter((i) => i.isCorrect);
    const totalCorrect = correctInteractions.length;
    const overallAccuracy = totalCorrect / totalAttempts * 100;
    const responseTimes = interactions.map((i) => i.responseTime).filter((rt) => rt && rt > 0);
    const averageResponseTime = responseTimes.length > 0 ? responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length : 0;
    const colorMetrics = {};
    interactions.forEach((interaction) => {
      const targetColor = interaction.targetColor;
      if (targetColor && targetColor !== "session_complete") {
        if (!colorMetrics[targetColor]) {
          colorMetrics[targetColor] = { total: 0, correct: 0, responseTimes: [] };
        }
        colorMetrics[targetColor].total++;
        if (interaction.isCorrect) {
          colorMetrics[targetColor].correct++;
        }
        if (interaction.responseTime) {
          colorMetrics[targetColor].responseTimes.push(interaction.responseTime);
        }
      }
    });
    return {
      overallAccuracy,
      averageResponseTime,
      totalCorrect,
      totalAttempts,
      colorMetrics,
      improvementTrend: this.calculateImprovementTrend(interactions),
      speedAccuracyBalance: this.calculateSpeedAccuracyBalance(interactions)
    };
  }
  /**
   * Analisa progressão durante a sessão
   */
  analyzeProgressionThroughSession(interactions) {
    const segmentSize = Math.max(1, Math.floor(interactions.length / 3));
    const segments = [
      interactions.slice(0, segmentSize),
      interactions.slice(segmentSize, segmentSize * 2),
      interactions.slice(segmentSize * 2)
    ].filter((segment) => segment.length > 0);
    return segments.map((segment, index) => ({
      segment: index + 1,
      accuracy: segment.filter((i) => i.isCorrect).length / segment.length * 100,
      averageResponseTime: segment.reduce((sum, i) => sum + (i.responseTime || 0), 0) / segment.length,
      interactionCount: segment.length
    }));
  }
  /**
   * Calcula curva de aprendizado
   */
  calculateLearningCurve(interactions) {
    if (interactions.length < 3) {
      return { slope: 0, trend: "insufficient_data" };
    }
    const windowSize = Math.max(2, Math.floor(interactions.length / 5));
    const accuracyPoints = [];
    for (let i = 0; i <= interactions.length - windowSize; i += windowSize) {
      const window2 = interactions.slice(i, i + windowSize);
      const accuracy = window2.filter((interaction) => interaction.isCorrect).length / window2.length;
      accuracyPoints.push(accuracy);
    }
    const slope = accuracyPoints.length > 1 ? (accuracyPoints[accuracyPoints.length - 1] - accuracyPoints[0]) / (accuracyPoints.length - 1) : 0;
    const trend = slope > 0.1 ? "improving" : slope < -0.1 ? "declining" : "stable";
    return { slope, trend, accuracyPoints };
  }
  /**
   * Calcula métricas de consistência
   */
  calculateConsistencyMetrics(interactions) {
    if (interactions.length < 3) {
      return { consistencyScore: 0.5, variability: "high" };
    }
    const responseTimes = interactions.map((i) => i.responseTime).filter((rt) => rt && rt > 0);
    if (responseTimes.length === 0) {
      return { consistencyScore: 0.5, variability: "unknown" };
    }
    const mean = responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length;
    const variance = responseTimes.reduce((sum, rt) => sum + Math.pow(rt - mean, 2), 0) / responseTimes.length;
    const standardDeviation = Math.sqrt(variance);
    const coefficientOfVariation = mean > 0 ? standardDeviation / mean : 1;
    const consistencyScore = Math.max(0, Math.min(1, 1 - coefficientOfVariation));
    const variability = coefficientOfVariation < 0.3 ? "low" : coefficientOfVariation < 0.6 ? "medium" : "high";
    return { consistencyScore, variability, coefficientOfVariation };
  }
  /**
   * Calcula tendência de melhoria
   */
  calculateImprovementTrend(interactions) {
    if (interactions.length < 4) return "insufficient_data";
    const midPoint = Math.floor(interactions.length / 2);
    const firstHalf = interactions.slice(0, midPoint);
    const secondHalf = interactions.slice(midPoint);
    const firstHalfAccuracy = firstHalf.filter((i) => i.isCorrect).length / firstHalf.length;
    const secondHalfAccuracy = secondHalf.filter((i) => i.isCorrect).length / secondHalf.length;
    const improvement = secondHalfAccuracy - firstHalfAccuracy;
    if (improvement > 0.15) return "significant_improvement";
    if (improvement > 0.05) return "moderate_improvement";
    if (improvement > -0.05) return "stable";
    if (improvement > -0.15) return "slight_decline";
    return "significant_decline";
  }
  /**
   * Calcula equilíbrio velocidade-precisão
   */
  calculateSpeedAccuracyBalance(interactions) {
    const correctInteractions = interactions.filter((i) => i.isCorrect);
    const incorrectInteractions = interactions.filter((i) => !i.isCorrect);
    if (correctInteractions.length === 0 || incorrectInteractions.length === 0) {
      return { balance: "skewed", type: correctInteractions.length === 0 ? "accuracy_focused" : "speed_focused" };
    }
    const avgCorrectTime = correctInteractions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / correctInteractions.length;
    const avgIncorrectTime = incorrectInteractions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / incorrectInteractions.length;
    const speedDifference = avgIncorrectTime - avgCorrectTime;
    if (Math.abs(speedDifference) < 200) return { balance: "optimal", speedDifference };
    if (speedDifference > 500) return { balance: "speed_over_accuracy", speedDifference };
    return { balance: "accuracy_over_speed", speedDifference };
  }
  /**
   * Finalizar sessão e gerar relatório completo - FASE 1.2
   */
  async finalizeSession() {
    try {
      if (!this.currentSession || !this.currentSession.active) {
        console.warn("🎨 ColorMatch: Nenhuma sessão ativa para finalizar");
        return null;
      }
      console.log("🎨 ColorMatch: Finalizando sessão...");
      const startTime = Date.now();
      this.currentSession.active = false;
      this.currentSession.endTime = Date.now();
      this.currentSession.totalDuration = this.currentSession.endTime - this.currentSession.startTime;
      const completeAnalysis = await this.analyzeCompleteSession();
      const finalReport = {
        sessionId: this.currentSession.sessionId,
        gameType: "ColorMatch",
        gameVersion: this.currentSession.gameVersion || "2.0",
        // Dados da sessão
        sessionData: {
          startTime: new Date(this.currentSession.startTime).toISOString(),
          endTime: new Date(this.currentSession.endTime).toISOString(),
          totalDuration: this.currentSession.totalDuration,
          difficulty: this.currentSession.config?.difficulty,
          phaseConfig: this.currentSession.phaseConfig,
          userSettings: this.currentSession.userSettings,
          totalInteractions: this.currentSession.interactions.length
        },
        // Resultados da análise completa
        analysisResults: completeAnalysis,
        // Resumo executivo da sessão
        executiveSummary: this.generateExecutiveSummary(completeAnalysis),
        // Recomendações terapêuticas
        therapeuticRecommendations: this.generateTherapeuticRecommendations(completeAnalysis),
        // Dados para próxima sessão
        nextSessionSuggestions: this.generateNextSessionSuggestions(completeAnalysis),
        // Metadados
        reportGeneratedAt: (/* @__PURE__ */ new Date()).toISOString(),
        reportGenerationTime: Date.now() - startTime,
        dataVersion: "2.0"
      };
      console.log(`✅ ColorMatch: Sessão finalizada e relatório gerado em ${finalReport.reportGenerationTime}ms`);
      console.log("🎨 ColorMatch: Resumo da sessão finalizada:", {
        sessionId: finalReport.sessionId,
        duration: `${Math.round(finalReport.sessionData.totalDuration / 1e3)}s`,
        interactions: finalReport.sessionData.totalInteractions,
        overallAccuracy: finalReport.executiveSummary.overallAccuracy,
        performanceLevel: finalReport.executiveSummary.performanceLevel
      });
      this.currentSession = null;
      return finalReport;
    } catch (error) {
      console.error("❌ ColorMatch: Erro ao finalizar sessão:", error);
      const errorReport = {
        sessionId: this.currentSession?.sessionId || "unknown",
        gameType: "ColorMatch",
        status: "error",
        error: error.message,
        sessionData: {
          totalInteractions: this.currentSession?.interactions.length || 0,
          duration: this.currentSession ? Date.now() - this.currentSession.startTime : 0
        },
        reportGeneratedAt: (/* @__PURE__ */ new Date()).toISOString()
      };
      this.currentSession = null;
      return errorReport;
    }
  }
  /**
   * Gera resumo executivo da sessão
   */
  generateExecutiveSummary(analysisResults) {
    if (!analysisResults || !analysisResults.sessionAnalysis) {
      return {
        overallAccuracy: 0,
        performanceLevel: "incomplete",
        keyFindings: ["Sessão incompleta ou sem dados suficientes"]
      };
    }
    const sessionAnalysis = analysisResults.sessionAnalysis;
    const overallMetrics = sessionAnalysis.overallMetrics;
    let performanceLevel = "developing";
    if (overallMetrics.overallAccuracy >= 90) performanceLevel = "excellent";
    else if (overallMetrics.overallAccuracy >= 75) performanceLevel = "proficient";
    else if (overallMetrics.overallAccuracy >= 60) performanceLevel = "developing";
    else performanceLevel = "needs_support";
    const keyFindings = [];
    if (sessionAnalysis.learningCurve.trend === "improving") {
      keyFindings.push("Demonstrou melhoria consistente durante a sessão");
    }
    if (sessionAnalysis.consistencyMetrics.variability === "low") {
      keyFindings.push("Manteve performance consistente");
    }
    if (overallMetrics.averageResponseTime < 1500) {
      keyFindings.push("Velocidade de processamento visual adequada");
    }
    if (overallMetrics.speedAccuracyBalance?.balance === "optimal") {
      keyFindings.push("Bom equilíbrio entre velocidade e precisão");
    }
    if (keyFindings.length === 0) {
      keyFindings.push("Sessão completada com dados básicos coletados");
    }
    return {
      overallAccuracy: Math.round(overallMetrics.overallAccuracy || 0),
      performanceLevel,
      keyFindings,
      totalInteractions: sessionAnalysis.interactionsCount,
      sessionDuration: Math.round(sessionAnalysis.totalDuration / 1e3),
      // em segundos
      improvementTrend: sessionAnalysis.learningCurve?.trend || "stable"
    };
  }
  /**
   * Gera recomendações terapêuticas específicas
   */
  generateTherapeuticRecommendations(analysisResults) {
    const recommendations = [];
    if (!analysisResults || !analysisResults.sessionAnalysis) {
      return [{
        category: "data",
        priority: "high",
        recommendation: "Coletar mais dados para análise terapêutica adequada",
        rationale: "Dados insuficientes na sessão atual"
      }];
    }
    const sessionAnalysis = analysisResults.sessionAnalysis;
    const overallMetrics = sessionAnalysis.overallMetrics;
    const collectorsResults = analysisResults.collectorsResults;
    if (overallMetrics.overallAccuracy < 60) {
      recommendations.push({
        category: "accuracy",
        priority: "high",
        recommendation: "Focar em exercícios de discriminação visual e atenção sustentada",
        rationale: `Precisão atual de ${Math.round(overallMetrics.overallAccuracy)}% indica necessidade de suporte adicional`
      });
    }
    if (overallMetrics.averageResponseTime > 3e3) {
      recommendations.push({
        category: "processing_speed",
        priority: "medium",
        recommendation: "Implementar exercícios para aumentar velocidade de processamento visual",
        rationale: `Tempo médio de resposta de ${Math.round(overallMetrics.averageResponseTime)}ms está acima do esperado`
      });
    }
    if (collectorsResults.colorPerception?.colorDiscrimination < 0.6) {
      recommendations.push({
        category: "color_perception",
        priority: "medium",
        recommendation: "Trabalhar especificamente com exercícios de discriminação cromática",
        rationale: "Dificuldades detectadas na percepção e discriminação de cores"
      });
    }
    if (collectorsResults.attentionalSelectivity?.selectiveAttention < 0.6) {
      recommendations.push({
        category: "attention",
        priority: "high",
        recommendation: "Incorporar atividades para desenvolver atenção seletiva",
        rationale: "Déficits observados na capacidade de manter foco em estímulos específicos"
      });
    }
    if (sessionAnalysis.learningCurve?.trend === "declining") {
      recommendations.push({
        category: "fatigue",
        priority: "high",
        recommendation: "Considerar sessões mais curtas ou pausas mais frequentes",
        rationale: "Declínio na performance sugere possível fadiga durante a atividade"
      });
    }
    return recommendations.length > 0 ? recommendations : [{
      category: "continuation",
      priority: "low",
      recommendation: "Continuar com atividades similares para consolidar habilidades",
      rationale: "Performance adequada observada na sessão atual"
    }];
  }
  /**
   * Gera sugestões para próxima sessão
   */
  generateNextSessionSuggestions(analysisResults) {
    if (!analysisResults || !analysisResults.sessionAnalysis) {
      return {
        suggestedDifficulty: "easy",
        suggestedDuration: 300,
        // 5 minutos
        specificFocus: ["basic_color_recognition"],
        rationale: "Dados insuficientes para recomendações específicas"
      };
    }
    const sessionAnalysis = analysisResults.sessionAnalysis;
    const overallMetrics = sessionAnalysis.overallMetrics;
    const currentDifficulty = analysisResults.gameData?.difficulty || "easy";
    let suggestedDifficulty = currentDifficulty;
    if (overallMetrics.overallAccuracy >= 90 && overallMetrics.averageResponseTime < 1500) {
      if (currentDifficulty === "easy") suggestedDifficulty = "medium";
      else if (currentDifficulty === "medium") suggestedDifficulty = "hard";
    } else if (overallMetrics.overallAccuracy < 50) {
      if (currentDifficulty === "hard") suggestedDifficulty = "medium";
      else if (currentDifficulty === "medium") suggestedDifficulty = "easy";
    }
    const baseDuration = 300;
    let suggestedDuration = baseDuration;
    if (sessionAnalysis.consistencyMetrics?.variability === "low") {
      suggestedDuration = Math.min(600, baseDuration * 1.5);
    } else if (sessionAnalysis.learningCurve?.trend === "declining") {
      suggestedDuration = Math.max(180, baseDuration * 0.7);
    }
    const specificFocus = [];
    if (overallMetrics.colorMetrics) {
      const colorAccuracies = Object.entries(overallMetrics.colorMetrics).map(([color, metrics]) => ({
        color,
        accuracy: metrics.correct / metrics.total
      })).filter((item) => item.accuracy < 0.7);
      if (colorAccuracies.length > 0) {
        specificFocus.push(`cores_específicas: ${colorAccuracies.map((item) => item.color).join(", ")}`);
      }
    }
    if (overallMetrics.averageResponseTime > 2500) {
      specificFocus.push("velocidade_processamento");
    }
    if (specificFocus.length === 0) {
      specificFocus.push("manutenção_habilidades");
    }
    return {
      suggestedDifficulty,
      suggestedDuration,
      specificFocus,
      rationale: `Baseado em precisão de ${Math.round(overallMetrics.overallAccuracy)}% e tempo médio de ${Math.round(overallMetrics.averageResponseTime)}ms`,
      adaptiveAdjustments: this.generateAdaptiveAdjustments(analysisResults)
    };
  }
  /**
   * Gera ajustes adaptativos para próxima sessão
   */
  generateAdaptiveAdjustments(analysisResults) {
    const adjustments = [];
    if (analysisResults?.sessionAnalysis?.overallMetrics?.speedAccuracyBalance?.balance === "speed_over_accuracy") {
      adjustments.push({
        parameter: "time_pressure",
        adjustment: "reduce",
        reason: "Priorizar precisão sobre velocidade"
      });
    }
    if (analysisResults?.sessionAnalysis?.consistencyMetrics?.variability === "high") {
      adjustments.push({
        parameter: "difficulty_progression",
        adjustment: "gradual",
        reason: "Variabilidade alta sugere necessidade de progressão mais suave"
      });
    }
    return adjustments;
  }
  // Métodos auxiliares para preparação de dados
  determineVisualComplexity(action) {
    if (action.distractors && action.distractors.length > 2) return "high";
    if (action.similarColors && action.similarColors.length > 1) return "medium";
    return "low";
  }
  requiresInhibition(action) {
    return action.hasDistractors || action.requiresInhibition || false;
  }
  hasConflict(action) {
    return action.conflictingStimuli || action.hasConflict || false;
  }
  requiresTopDownControl(action) {
    return action.complexity === "high" || action.requiresControl || false;
  }
  requiresFlexibility(action) {
    return action.contextChange || action.requiresFlexibility || false;
  }
  isMultitasking(action) {
    return action.multipleTargets || action.isMultitasking || false;
  }
  calculateMemoryLoad(action) {
    if (action.memoryLoad) return action.memoryLoad;
    if (action.complexity === "high") return 3;
    if (action.complexity === "medium") return 2;
    return 1;
  }
  identifyTaskType(action) {
    if (action.taskType) return action.taskType;
    if (action.requiresMemory) return "memory";
    if (action.requiresNaming) return "naming";
    if (action.hasSemanticComponent) return "semantic";
    if (action.requiresCategorization) return "categorization";
    return "basic";
  }
  determineCognitiveComplexity(action) {
    if (action.complexity) return action.complexity;
    if (action.multipleSteps || action.abstracts) return "complex";
    if (action.requiresComparison) return "intermediate";
    return "basic";
  }
  requiresNaming(action) {
    return action.requiresNaming || action.taskType === "naming" || false;
  }
  hasSemanticComponent(action) {
    return action.hasSemanticComponent || action.objectAssociation || false;
  }
  identifyConceptualAspect(action) {
    if (action.conceptualAspect) return action.conceptualAspect;
    if (action.taskType === "semantic") return "semantic";
    if (action.taskType === "categorization") return "categorical";
    if (action.taskType === "memory") return "memory";
    return "perceptual";
  }
  /**
   * Inicializar sessão de coleta - ATUALIZADO para nova estrutura
   */
  async initializeSession(sessionConfig) {
    try {
      if (typeof sessionConfig === "string") {
        const sessionId = sessionConfig;
        const config = arguments[1] || {};
        this.currentSession = {
          sessionId,
          config,
          startTime: Date.now(),
          interactions: [],
          active: true
        };
      } else {
        this.currentSession = {
          sessionId: sessionConfig.sessionId,
          config: sessionConfig,
          phaseConfig: sessionConfig.phaseConfig,
          userSettings: sessionConfig.userSettings,
          startTime: sessionConfig.startTime || Date.now(),
          interactions: [],
          active: true,
          gameVersion: sessionConfig.gameVersion || "2.0"
        };
      }
      console.log("🎨 ColorMatch: Sessão de coletores inicializada:", {
        sessionId: this.currentSession.sessionId,
        difficulty: this.currentSession.config.difficulty,
        gameVersion: this.currentSession.gameVersion,
        phaseConfig: this.currentSession.phaseConfig ? "presente" : "ausente"
      });
      return {
        status: "success",
        sessionId: this.currentSession.sessionId,
        message: "Sessão inicializada com nova estrutura"
      };
    } catch (error) {
      console.error("❌ ColorMatch: Erro ao inicializar sessão:", error);
      return { status: "error", message: error.message };
    }
  }
  /**
   * Coletar interação específica de cor - ATUALIZADO FASE 1.2
   */
  async collectColorInteraction(interactionData) {
    try {
      if (this.currentSession) {
        const sessionInteraction = {
          timestamp: interactionData.timestamp || Date.now(),
          selectedColor: interactionData.selectedColor,
          targetColor: interactionData.targetColor,
          isCorrect: interactionData.selectedItems && interactionData.selectedItems.length > 0 ? interactionData.selectedItems[interactionData.selectedItems.length - 1].correct : false,
          responseTime: interactionData.selectedItems && interactionData.selectedItems.length > 0 ? interactionData.selectedItems[interactionData.selectedItems.length - 1].responseTime : 0,
          difficulty: interactionData.difficulty,
          gridPosition: interactionData.gridPosition,
          timeRemaining: interactionData.timeRemaining
        };
        this.currentSession.interactions.push(sessionInteraction);
        console.log("🎨 ColorMatch: Interação armazenada na sessão:", {
          totalInteractions: this.currentSession.interactions.length,
          isCorrect: sessionInteraction.isCorrect,
          targetColor: sessionInteraction.targetColor,
          selectedColor: sessionInteraction.selectedColor
        });
      }
      if (!this.validateGameData(interactionData)) {
        console.warn("🎨 ColorMatch: Dados de interação inválidos, mas continuando...");
        return {
          status: "warning",
          message: "Dados inválidos, mas interação registrada",
          interactionCount: this.currentSession?.interactions.length || 0
        };
      }
      const quickAnalysis = {
        timestamp: Date.now(),
        difficulty: interactionData.difficulty,
        responseTime: interactionData.selectedItems && interactionData.selectedItems.length > 0 ? interactionData.selectedItems[interactionData.selectedItems.length - 1].responseTime : 0,
        accuracy: this.currentSession ? this.currentSession.interactions.filter((i) => i.isCorrect).length / this.currentSession.interactions.length * 100 : 100,
        interactionCount: this.currentSession?.interactions.length || 0
      };
      console.log("🎨 ColorMatch: Análise rápida da interação:", quickAnalysis);
      return {
        status: "success",
        message: "Interação coletada com sucesso",
        quickAnalysis,
        shouldPerformCompleteAnalysis: false
        // Análise completa será feita estrategicamente
      };
    } catch (error) {
      console.error("❌ ColorMatch: Erro ao coletar interação de cor:", error);
      return { status: "error", message: error.message };
    }
  }
  /**
   * Coletar dados abrangentes (compatibilidade)
   */
  async collectComprehensiveData(gameData) {
    try {
      return await this.runCompleteAnalysis(gameData);
    } catch (error) {
      console.error("❌ Erro ao coletar dados abrangentes:", error);
      return { status: "error", message: error.message };
    }
  }
  /**
   * Verificar se a sessão está ativa
   */
  get sessionActive() {
    return this.currentSession && this.currentSession.active;
  }
}
class ColorMatchProcessors extends IGameProcessor {
  constructor(logger) {
    const config = {
      category: "visual_processing",
      therapeuticFocus: ["color_discrimination", "visual_matching", "perception"],
      cognitiveAreas: ["visual_processing", "attention", "executive_function"],
      thresholds: {
        accuracy: 75,
        responseTime: 2500,
        engagement: 80
      }
    };
    super(config);
    this.logger = logger || this.logger;
    this.gameType = "ColorMatch";
    this.progressionAnalyzer = new GameProgressionAnalyzer();
    this.attentionalAnalyzer = new AttentionalSelectivityAnalyzer();
    this.visualAnalyzer = new VisualProcessingAnalyzer();
    this.logger?.info("🎮 ColorMatchProcessors inicializado com 3 analisadores especializados - FASE CRÍTICA ATIVA");
  }
  /**
   * Processa dados do jogo ColorMatch
   * @param {Object} gameData - Dados coletados do jogo
   * @param {Object} collectorsHub - Hub de coletores específico do jogo
   * @returns {Promise<Object>} Análise terapêutica específica
   */
  async processGameData(gameData, collectorsHub = null) {
    try {
      this.logger?.info("🎮 Processando dados ColorMatch", {
        sessionId: gameData.sessionId,
        userId: gameData.userId,
        collectorsActive: collectorsHub ? Object.keys(collectorsHub.collectors || {}).length : 0
      });
      let collectorsAnalysis = {};
      if (collectorsHub && collectorsHub.runCompleteAnalysis) {
        try {
          this.logger?.info("🎨 Executando análise completa com 10 coletores...");
          collectorsAnalysis = await collectorsHub.runCompleteAnalysis(gameData);
          this.logger?.info("✅ Análise de coletores concluída com sucesso");
        } catch (error) {
          this.logger?.error("❌ Erro na análise de coletores:", error);
          collectorsAnalysis = await this.processCollectorsWithCircuitBreaker(collectorsHub, gameData);
        }
      }
      let advancedAnalysis = {};
      try {
        this.logger?.info("🧠 Executando análise com 3 analisadores especializados...");
        if (collectorsAnalysis.ProgressionMasteryCollector) {
          advancedAnalysis.progressionAnalysis = await this.progressionAnalyzer.analyze(
            collectorsAnalysis.ProgressionMasteryCollector,
            { gameType: "ColorMatch", ...gameData }
          );
          this.logger?.info("✅ Análise de progressão concluída");
        }
        if (collectorsAnalysis.AttentionalSelectivityCollector) {
          advancedAnalysis.attentionalAnalysis = await this.attentionalAnalyzer.analyze(
            collectorsAnalysis.AttentionalSelectivityCollector,
            { gameType: "ColorMatch", ...gameData }
          );
          this.logger?.info("✅ Análise atencional concluída");
        }
        if (collectorsAnalysis.VisualProcessingCollector) {
          advancedAnalysis.visualAnalysis = await this.visualAnalyzer.analyze(
            collectorsAnalysis.VisualProcessingCollector,
            { gameType: "ColorMatch", ...gameData }
          );
          this.logger?.info("✅ Análise visual concluída");
        }
        this.logger?.info("🎯 FASE CRÍTICA: Todos os 3 analisadores executados com sucesso");
      } catch (error) {
        this.logger?.error("❌ Erro nos analisadores especializados:", error);
        advancedAnalysis.error = error.message;
      }
      const metrics = await this.processColorMatchMetrics(gameData, collectorsAnalysis);
      const therapeuticAnalysis = this.generateTherapeuticAnalysis(metrics, gameData, advancedAnalysis);
      const processedMetrics = this.processMetricsForDatabase(metrics, gameData);
      return {
        success: true,
        gameType: this.gameType,
        metrics,
        therapeuticAnalysis,
        processedMetrics,
        collectorsAnalysis,
        advancedAnalysis,
        // FASE CRÍTICA: Análises dos 3 processadores especializados
        analysisCapabilities: {
          collectors: Object.keys(collectorsAnalysis).length,
          analyzers: Object.keys(advancedAnalysis).length,
          totalAnalysisPoints: this.calculateAnalysisPoints(collectorsAnalysis, advancedAnalysis)
        },
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    } catch (error) {
      this.logger?.error("❌ Erro ao processar dados ColorMatch:", error);
      return {
        success: false,
        gameType: this.gameType,
        error: error.message,
        fallbackAnalysis: this.generateFallbackTherapeuticAnalysis(gameData),
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    }
  }
  /**
   * Método principal de processamento de dados
   * @param {Object} sessionData - Dados da sessão de jogo
   * @returns {Promise<Object>} Métricas processadas
   */
  async processData(sessionData) {
    try {
      this.logger?.info("🎨 Processando dados Color Match", {
        sessionId: sessionData.sessionId,
        userId: sessionData.userId
      });
      const result = await this.processGameData(sessionData);
      this.logger?.therapeutic("✅ Processamento Color Match concluído com sucesso");
      return result;
    } catch (error) {
      this.logger?.error("❌ Erro ao processar dados Color Match:", error);
      throw error;
    }
  }
  /**
   * Processa métricas específicas do ColorMatch
   * @param {Object} gameData - Dados do jogo ColorMatch
   * @param {Object} sessionData - Dados da sessão
   * @returns {Object} Métricas processadas
   */
  async processColorMatchMetrics(gameData, sessionData) {
    try {
      this.logger?.info("🎨 Processando métricas ColorMatch...", {
        sessionId: sessionData.sessionId
      });
      const metrics = {
        // Métricas de percepção visual
        visualPerception: this.analyzeVisualPerception(gameData),
        // Análise de discriminação cromática
        colorDiscrimination: this.analyzeColorDiscrimination(gameData),
        // Tempo de resposta visual
        visualResponseTime: this.analyzeVisualResponseTime(gameData),
        // Padrões de confusão de cores
        colorConfusionPatterns: this.analyzeColorConfusionPatterns(gameData),
        // Análise comportamental específica
        colorBehavior: this.analyzeColorBehavior(gameData),
        // Métricas cognitivas relacionadas à cor
        colorCognition: this.analyzeColorCognition(gameData),
        // Indicadores terapêuticos
        therapeuticIndicators: this.generateTherapeuticIndicators(gameData),
        // Recomendações específicas
        recommendations: this.generateColorMatchRecommendations(gameData)
      };
      this.logger?.info("✅ Métricas ColorMatch processadas", {
        accuracy: metrics.visualPerception.accuracy,
        responseTime: metrics.visualResponseTime.average,
        colorConfusions: metrics.colorConfusionPatterns.confusionCount
      });
      return metrics;
    } catch (error) {
      this.logger?.error("❌ Erro ao processar métricas ColorMatch:", error);
      throw error;
    }
  }
  /**
   * Analisa percepção visual geral
   */
  analyzeVisualPerception(gameData) {
    const { totalCorrect = 0, totalAttempts = 1, interactions = [] } = gameData;
    return {
      accuracy: Math.round(totalCorrect / totalAttempts * 100),
      totalAttempts,
      totalCorrect,
      visualAcuity: this.calculateVisualAcuity(interactions),
      colorSensitivity: this.calculateColorSensitivity(interactions),
      visualAttention: this.calculateVisualAttention(interactions)
    };
  }
  /**
   * Analisa discriminação cromática
   */
  analyzeColorDiscrimination(gameData) {
    const { interactions = [], targetColor = "unknown" } = gameData;
    const colorAccuracy = this.calculateColorAccuracy(interactions, targetColor);
    const discriminationIndex = this.calculateDiscriminationIndex(interactions);
    return {
      targetColor,
      colorAccuracy,
      discriminationIndex,
      confusionMatrix: this.buildColorConfusionMatrix(interactions),
      strongColors: this.identifyStrongColors(interactions),
      weakColors: this.identifyWeakColors(interactions)
    };
  }
  /**
   * Analisa tempo de resposta visual
   */
  analyzeVisualResponseTime(gameData) {
    const { interactions = [] } = gameData;
    const responseTimes = interactions.map((i) => i.responseTime || 0).filter((t) => t > 0);
    if (responseTimes.length === 0) {
      return { average: 0, median: 0, variability: 0, pattern: "insufficient_data" };
    }
    const average = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const sorted = responseTimes.sort((a, b) => a - b);
    const median = sorted[Math.floor(sorted.length / 2)];
    const variability = this.calculateVariability(responseTimes);
    return {
      average: Math.round(average),
      median: Math.round(median),
      min: Math.min(...responseTimes),
      max: Math.max(...responseTimes),
      variability,
      pattern: this.identifyResponsePattern(responseTimes),
      visualProcessingSpeed: this.assessVisualProcessingSpeed(average)
    };
  }
  /**
   * Analisa padrões de confusão de cores
   */
  analyzeColorConfusionPatterns(gameData) {
    const { interactions = [] } = gameData;
    const confusions = this.identifyColorConfusions(interactions);
    const patterns = this.detectConfusionPatterns(confusions);
    return {
      confusionCount: confusions.length,
      confusions,
      patterns,
      potentialColorBlindness: this.assessColorBlindnessRisk(confusions),
      visualProcessingIssues: this.assessVisualProcessingIssues(confusions)
    };
  }
  /**
   * Analisa comportamento específico relacionado a cores
   */
  analyzeColorBehavior(gameData) {
    const { interactions = [], difficulty = "easy" } = gameData;
    return {
      preferredColors: this.identifyPreferredColors(interactions),
      avoidedColors: this.identifyAvoidedColors(interactions),
      scanningPattern: this.analyzeVisualScanningPattern(interactions),
      adaptabilityToNewColors: this.assessColorAdaptability(interactions),
      difficultyResponse: this.analyzeDifficultyResponse(interactions, difficulty),
      persistenceLevel: this.assessPersistenceLevel(interactions)
    };
  }
  /**
   * Analisa aspectos cognitivos relacionados a cores
   */
  analyzeColorCognition(gameData) {
    const { difficulty = "easy", interactions = [], accuracy = 0 } = gameData;
    return {
      workingMemoryForColor: this.assessColorWorkingMemory(difficulty, accuracy),
      attentionalSelectivity: this.assessAttentionalSelectivity(interactions),
      processingSpeed: this.calculateColorProcessingSpeed(interactions),
      visualDiscrimination: this.calculateVisualDiscrimination(interactions),
      cognitiveFlexibility: this.assessCognitiveFlexibility(interactions),
      inhibitoryControl: this.assessInhibitoryControl(interactions)
    };
  }
  /**
   * Gera indicadores terapêuticos específicos
   */
  generateTherapeuticIndicators(gameData) {
    const { interactions = [], accuracy = 0 } = gameData;
    return {
      visualProcessingDelay: interactions.some((i) => (i.responseTime || 0) > 3e3),
      colorBlindnessIndicators: this.checkColorBlindnessPatterns(interactions),
      attentionDeficits: accuracy < 60,
      visualMemoryIssues: this.detectVisualMemoryIssues(interactions),
      processingSpeedConcerns: this.detectProcessingSpeedConcerns(interactions),
      executiveFunctionConcerns: this.detectExecutiveFunctionConcerns(interactions)
    };
  }
  /**
   * Gera recomendações específicas para ColorMatch
   */
  generateColorMatchRecommendations(gameData) {
    const recommendations = [];
    const { accuracy = 0, interactions = [] } = gameData;
    if (accuracy < 50) {
      recommendations.push({
        type: "difficulty_adjustment",
        priority: "high",
        action: "Reduzir número de cores e aumentar contraste",
        rationale: "Baixa precisão indica necessidade de simplificação"
      });
    } else if (accuracy > 90) {
      recommendations.push({
        type: "challenge_increase",
        priority: "medium",
        action: "Introduzir cores similares e maior variedade",
        rationale: "Alta precisão permite desafios maiores"
      });
    }
    const avgResponseTime = this.calculateAverageResponseTime(interactions);
    if (avgResponseTime > 4e3) {
      recommendations.push({
        type: "processing_support",
        priority: "medium",
        action: "Implementar pistas visuais e reduzir pressão temporal",
        rationale: "Tempo de resposta elevado indica necessidade de suporte"
      });
    }
    const confusions = this.identifyColorConfusions(interactions);
    if (confusions.length > 2) {
      recommendations.push({
        type: "visual_training",
        priority: "high",
        action: "Exercícios específicos de discriminação cromática",
        rationale: "Múltiplas confusões de cor detectadas"
      });
    }
    return recommendations;
  }
  // Métodos auxiliares de cálculo
  calculateVisualAcuity(interactions) {
    const correctInteractions = interactions.filter((i) => i.correct);
    return correctInteractions.length / Math.max(interactions.length, 1);
  }
  calculateColorSensitivity(interactions) {
    const similarColorInteractions = interactions.filter((i) => i.colorSimilarity && i.colorSimilarity > 0.8);
    const avgResponseTime = similarColorInteractions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / Math.max(similarColorInteractions.length, 1);
    return avgResponseTime < 2e3 ? "high" : avgResponseTime < 4e3 ? "medium" : "low";
  }
  calculateVisualAttention(interactions) {
    const responseTimes = interactions.map((i) => i.responseTime || 0).filter((t) => t > 0);
    const variability = this.calculateVariability(responseTimes);
    return variability < 1e3 ? "sustained" : variability < 2e3 ? "moderate" : "variable";
  }
  calculateColorAccuracy(interactions, targetColor) {
    const targetInteractions = interactions.filter((i) => i.targetColor === targetColor);
    const correctTarget = targetInteractions.filter((i) => i.correct);
    return targetInteractions.length > 0 ? Math.round(correctTarget.length / targetInteractions.length * 100) : 0;
  }
  calculateDiscriminationIndex(interactions) {
    const similarColorPairs = interactions.filter((i) => i.colorSimilarity && i.colorSimilarity > 0.7);
    const correctSimilar = similarColorPairs.filter((i) => i.correct);
    return similarColorPairs.length > 0 ? correctSimilar.length / similarColorPairs.length : 1;
  }
  buildColorConfusionMatrix(interactions) {
    const matrix = {};
    interactions.forEach((interaction) => {
      if (!interaction.correct && interaction.targetColor && interaction.selectedColor) {
        const key = `${interaction.targetColor}->${interaction.selectedColor}`;
        matrix[key] = (matrix[key] || 0) + 1;
      }
    });
    return matrix;
  }
  identifyStrongColors(interactions) {
    const colorAccuracy = {};
    interactions.forEach((interaction) => {
      const color = interaction.targetColor;
      if (color) {
        if (!colorAccuracy[color]) {
          colorAccuracy[color] = { correct: 0, total: 0 };
        }
        colorAccuracy[color].total++;
        if (interaction.correct) {
          colorAccuracy[color].correct++;
        }
      }
    });
    return Object.entries(colorAccuracy).filter(([_, data]) => data.total > 0 && data.correct / data.total > 0.8).map(([color, _]) => color);
  }
  identifyWeakColors(interactions) {
    const colorAccuracy = {};
    interactions.forEach((interaction) => {
      const color = interaction.targetColor;
      if (color) {
        if (!colorAccuracy[color]) {
          colorAccuracy[color] = { correct: 0, total: 0 };
        }
        colorAccuracy[color].total++;
        if (interaction.correct) {
          colorAccuracy[color].correct++;
        }
      }
    });
    return Object.entries(colorAccuracy).filter(([_, data]) => data.total > 0 && data.correct / data.total < 0.5).map(([color, _]) => color);
  }
  calculateVariability(values) {
    if (values.length < 2) return 0;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }
  identifyResponsePattern(responseTimes) {
    if (responseTimes.length < 3) return "insufficient_data";
    const firstHalf = responseTimes.slice(0, Math.floor(responseTimes.length / 2));
    const secondHalf = responseTimes.slice(Math.floor(responseTimes.length / 2));
    const firstAvg = firstHalf.reduce((sum, t) => sum + t, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, t) => sum + t, 0) / secondHalf.length;
    if (secondAvg < firstAvg * 0.8) return "improving";
    if (secondAvg > firstAvg * 1.2) return "declining";
    return "stable";
  }
  assessVisualProcessingSpeed(avgResponseTime) {
    if (avgResponseTime < 1500) return "fast";
    if (avgResponseTime < 3e3) return "normal";
    return "slow";
  }
  identifyColorConfusions(interactions) {
    return interactions.filter((i) => !i.correct && i.targetColor && i.selectedColor).map((i) => ({
      target: i.targetColor,
      selected: i.selectedColor,
      responseTime: i.responseTime,
      similarity: i.colorSimilarity || 0
    }));
  }
  detectConfusionPatterns(confusions) {
    const patterns = {
      redGreenConfusion: confusions.filter(
        (c) => c.target === "vermelha" && c.selected === "verde" || c.target === "verde" && c.selected === "vermelha"
      ).length,
      blueYellowConfusion: confusions.filter(
        (c) => c.target === "azul" && c.selected === "amarela" || c.target === "amarela" && c.selected === "azul"
      ).length,
      similarColorConfusion: confusions.filter((c) => c.similarity > 0.7).length
    };
    return patterns;
  }
  assessColorBlindnessRisk(confusions) {
    const redGreen = confusions.filter(
      (c) => ["vermelha", "verde"].includes(c.target) && ["vermelha", "verde"].includes(c.selected)
    ).length;
    const blueYellow = confusions.filter(
      (c) => ["azul", "amarela"].includes(c.target) && ["azul", "amarela"].includes(c.selected)
    ).length;
    return {
      protanopia: redGreen > 2 ? "possible" : "unlikely",
      deuteranopia: redGreen > 2 ? "possible" : "unlikely",
      tritanopia: blueYellow > 1 ? "possible" : "unlikely",
      overall: redGreen + blueYellow > 3 ? "moderate" : "low"
    };
  }
  assessVisualProcessingIssues(confusions) {
    return {
      processingDelay: confusions.some((c) => c.responseTime > 4e3),
      attentionIssues: confusions.length > 5,
      discriminationDifficulty: confusions.filter((c) => c.similarity > 0.8).length > 2
    };
  }
  // Mais métodos auxiliares...
  identifyPreferredColors(interactions) {
    const colorCounts = {};
    interactions.forEach((i) => {
      if (i.selectedColor) {
        colorCounts[i.selectedColor] = (colorCounts[i.selectedColor] || 0) + 1;
      }
    });
    return Object.entries(colorCounts).sort(([, a], [, b]) => b - a).slice(0, 3).map(([color]) => color);
  }
  identifyAvoidedColors(interactions) {
    const totalColors = ["vermelha", "azul", "verde", "amarela", "laranja", "roxa"];
    const usedColors = new Set(interactions.map((i) => i.selectedColor).filter(Boolean));
    return totalColors.filter((color) => !usedColors.has(color));
  }
  analyzeVisualScanningPattern(interactions) {
    const responseTimes = interactions.map((i) => i.responseTime || 0);
    const trend = this.identifyResponsePattern(responseTimes);
    return {
      trend,
      consistency: this.calculateVariability(responseTimes) < 1e3 ? "high" : "low",
      efficiency: responseTimes.reduce((sum, t) => sum + t, 0) / responseTimes.length < 2500 ? "high" : "low"
    };
  }
  assessColorAdaptability(interactions) {
    if (interactions.length < 5) return "insufficient_data";
    const early = interactions.slice(0, 3);
    const late = interactions.slice(-3);
    const earlyAccuracy = early.filter((i) => i.correct).length / early.length;
    const lateAccuracy = late.filter((i) => i.correct).length / late.length;
    if (lateAccuracy > earlyAccuracy + 0.2) return "high";
    if (lateAccuracy > earlyAccuracy) return "moderate";
    return "low";
  }
  analyzeDifficultyResponse(interactions, difficulty) {
    const difficultyMap = { easy: 1, medium: 2, hard: 3 };
    const difficultyLevel = difficultyMap[difficulty] || 1;
    const accuracy = interactions.filter((i) => i.correct).length / Math.max(interactions.length, 1);
    const avgResponseTime = interactions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / Math.max(interactions.length, 1);
    return {
      appropriateForLevel: accuracy > 0.9 - difficultyLevel * 0.1,
      responseTimeForLevel: avgResponseTime < 1e3 + difficultyLevel * 1e3,
      recommendation: accuracy < 0.5 ? "decrease" : accuracy > 0.9 ? "increase" : "maintain"
    };
  }
  assessPersistenceLevel(interactions) {
    const incorrectStreak = this.calculateLongestIncorrectStreak(interactions);
    const totalAttempts = interactions.length;
    if (incorrectStreak > 5 && totalAttempts > 10) return "low";
    if (incorrectStreak < 3) return "high";
    return "moderate";
  }
  calculateLongestIncorrectStreak(interactions) {
    let maxStreak = 0;
    let currentStreak = 0;
    interactions.forEach((interaction) => {
      if (!interaction.correct) {
        currentStreak++;
        maxStreak = Math.max(maxStreak, currentStreak);
      } else {
        currentStreak = 0;
      }
    });
    return maxStreak;
  }
  // Métodos de análise cognitiva
  assessColorWorkingMemory(difficulty, accuracy) {
    const difficultyMap = { easy: 0.3, medium: 0.6, hard: 0.9 };
    const baseScore = difficultyMap[difficulty] || 0.3;
    const performanceModifier = accuracy / 100;
    return Math.min(1, baseScore * performanceModifier);
  }
  assessAttentionalSelectivity(interactions) {
    const correctResponses = interactions.filter((i) => i.correct);
    const avgCorrectTime = correctResponses.reduce((sum, i) => sum + (i.responseTime || 0), 0) / Math.max(correctResponses.length, 1);
    const incorrectResponses = interactions.filter((i) => !i.correct);
    const avgIncorrectTime = incorrectResponses.reduce((sum, i) => sum + (i.responseTime || 0), 0) / Math.max(incorrectResponses.length, 1);
    return avgCorrectTime < avgIncorrectTime ? 0.8 : 0.4;
  }
  calculateColorProcessingSpeed(interactions) {
    const responseTimes = interactions.map((i) => i.responseTime || 0).filter((t) => t > 0);
    const avgTime = responseTimes.reduce((sum, t) => sum + t, 0) / Math.max(responseTimes.length, 1);
    return Math.max(0, Math.min(1, (5e3 - avgTime) / 5e3));
  }
  calculateVisualDiscrimination(interactions) {
    const totalDiscriminations = interactions.length;
    const correctDiscriminations = interactions.filter((i) => i.correct).length;
    return totalDiscriminations > 0 ? correctDiscriminations / totalDiscriminations : 0;
  }
  assessCognitiveFlexibility(interactions) {
    if (interactions.length < 5) return 0.5;
    const colorChanges = interactions.filter((interaction, index) => {
      return index > 0 && interaction.targetColor !== interactions[index - 1].targetColor;
    });
    const successfulChanges = colorChanges.filter((change) => change.correct);
    return colorChanges.length > 0 ? successfulChanges.length / colorChanges.length : 0.5;
  }
  assessInhibitoryControl(interactions) {
    const fastResponses = interactions.filter((i) => (i.responseTime || 0) < 1e3);
    const accurateFastResponses = fastResponses.filter((i) => i.correct);
    return fastResponses.length > 0 ? accurateFastResponses.length / fastResponses.length : 0.5;
  }
  // Métodos de detecção de problemas
  detectVisualMemoryIssues(interactions) {
    const errorPatterns = {};
    interactions.forEach((interaction) => {
      if (!interaction.correct && interaction.targetColor) {
        const key = interaction.targetColor;
        errorPatterns[key] = (errorPatterns[key] || 0) + 1;
      }
    });
    return Object.values(errorPatterns).some((count) => count > 2);
  }
  detectProcessingSpeedConcerns(interactions) {
    const responseTimes = interactions.map((i) => i.responseTime || 0).filter((t) => t > 0);
    const avgTime = responseTimes.reduce((sum, t) => sum + t, 0) / Math.max(responseTimes.length, 1);
    return avgTime > 4e3;
  }
  detectExecutiveFunctionConcerns(interactions) {
    const flexibility = this.assessCognitiveFlexibility(interactions);
    const inhibition = this.assessInhibitoryControl(interactions);
    const persistence = this.assessPersistenceLevel(interactions);
    return flexibility < 0.3 || inhibition < 0.3 || persistence === "low";
  }
  checkColorBlindnessPatterns(interactions) {
    const confusions = this.identifyColorConfusions(interactions);
    const patterns = this.detectConfusionPatterns(confusions);
    return {
      protanopia: patterns.redGreenConfusion > 2,
      deuteranopia: patterns.redGreenConfusion > 2,
      tritanopia: patterns.blueYellowConfusion > 1
    };
  }
  calculateAverageResponseTime(interactions) {
    const times = interactions.map((i) => i.responseTime || 0).filter((t) => t > 0);
    return times.length > 0 ? times.reduce((sum, t) => sum + t, 0) / times.length : 0;
  }
  /**
   * Processa coletores com Circuit Breaker para resiliência
   * @param {Object} collectorsHub - Hub de coletores
   * @param {Object} gameData - Dados do jogo
   * @returns {Promise<Object>} Resultados dos coletores
   */
  async processCollectorsWithCircuitBreaker(collectorsHub, gameData) {
    if (!collectorsHub || !collectorsHub.collectors) {
      this.logger?.warn("⚠️ ColorMatch: Hub de coletores não disponível");
      return { collectors: {}, warning: "No collectors available" };
    }
    const results = {};
    const collectors = collectorsHub.collectors;
    for (const [collectorName, collector] of Object.entries(collectors)) {
      try {
        if (collector && typeof collector.analyze === "function") {
          this.logger?.debug(`🎨 Processando coletor: ${collectorName}`);
          results[collectorName] = await this.processWithTimeout(
            () => collector.analyze(gameData),
            5e3,
            // 5 segundos timeout
            `${collectorName} timeout`
          );
        } else {
          this.logger?.warn(`⚠️ Coletor ${collectorName} não tem método analyze`);
          results[collectorName] = { error: "No analyze method" };
        }
      } catch (error) {
        this.logger?.error(`❌ Erro no coletor ${collectorName}:`, error);
        results[collectorName] = {
          error: error.message,
          fallback: this.generateFallbackMetrics(collectorName, gameData)
        };
      }
    }
    return {
      collectors: results,
      processedAt: (/* @__PURE__ */ new Date()).toISOString(),
      gameType: "ColorMatch"
    };
  }
  /**
   * Gera análise integrada combinando processador e coletores
   * @param {Object} processorResults - Resultados do processador
   * @param {Object} collectorsResults - Resultados dos coletores
   * @returns {Object} Análise integrada
   */
  generateIntegratedAnalysis(processorResults, collectorsResults = {}) {
    try {
      const integratedAnalysis = {
        gameType: "ColorMatch",
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        sessionMetrics: processorResults,
        collectorInsights: collectorsResults.collectors || {},
        // Análise integrada específica para ColorMatch
        colorPerformance: {
          accuracy: processorResults.accuracy || 0,
          responseTime: processorResults.averageResponseTime || 0,
          colorDiscrimination: this.analyzeColorDiscriminationIntegrated(processorResults, collectorsResults),
          visualProcessing: this.analyzeVisualProcessingIntegrated(processorResults, collectorsResults),
          attentionPatterns: this.analyzeAttentionPatternsIntegrated(processorResults, collectorsResults),
          progressionMastery: this.analyzeProgressionMasteryIntegrated(processorResults, collectorsResults),
          emojiEngagement: this.analyzeEmojiEngagementIntegrated(processorResults, collectorsResults)
        },
        // Insights terapêuticos
        therapeuticInsights: this.generateTherapeuticInsights(processorResults, collectorsResults),
        // Recomendações baseadas na análise integrada
        recommendations: this.generateRecommendations(processorResults, collectorsResults),
        // Métricas de qualidade da análise
        analysisQuality: {
          dataCompleteness: this.assessDataCompleteness(processorResults, collectorsResults),
          collectorsCoverage: Object.keys(collectorsResults.collectors || {}).length,
          confidence: this.calculateConfidenceScore(processorResults, collectorsResults)
        }
      };
      this.logger?.info("✅ ColorMatch: Análise integrada gerada", {
        accuracy: integratedAnalysis.colorPerformance.accuracy,
        collectorsUsed: Object.keys(collectorsResults.collectors || {}).length,
        confidence: integratedAnalysis.analysisQuality.confidence
      });
      return integratedAnalysis;
    } catch (error) {
      this.logger?.error("❌ Erro ao gerar análise integrada ColorMatch:", error);
      return {
        error: error.message,
        fallback: true,
        basicMetrics: processorResults,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    }
  }
  /**
   * Analisa discriminação de cores baseada em dados integrados
   */
  analyzeColorDiscriminationIntegrated(processorResults, collectorsResults) {
    const colorPerceptionData = collectorsResults.collectors?.colorPerception;
    const colorDiscriminationData = collectorsResults.collectors?.colorDiscrimination;
    return {
      accuracy: processorResults.accuracy || 0,
      confusionPatterns: processorResults.colorConfusionPatterns || {},
      perceptionScore: colorPerceptionData?.perceptionScore || 0,
      discriminationIndex: this.calculateDiscriminationIndex(processorResults),
      // Dados do novo ColorDiscriminationCollector
      hueDiscrimination: colorDiscriminationData?.hueDiscrimination || 0,
      saturationSensitivity: colorDiscriminationData?.saturationSensitivity || 0,
      contrastPerformance: colorDiscriminationData?.contrastPerformance || 0,
      colorConstancy: colorDiscriminationData?.colorConstancy || 0
    };
  }
  /**
   * Analisa processamento visual
   */
  analyzeVisualProcessingIntegrated(processorResults, collectorsResults) {
    const visualData = collectorsResults.collectors?.visualProcessing;
    const timeResponseData = collectorsResults.collectors?.timeResponse;
    const gridNavigationData = collectorsResults.collectors?.gridNavigation;
    return {
      responseTime: processorResults.averageResponseTime || 0,
      visualComplexity: visualData?.complexityHandling || 0,
      processingEfficiency: this.calculateProcessingEfficiency(processorResults),
      // Dados do novo TimeResponseCollector
      reactionTimeVariability: timeResponseData?.reactionTimeVariability || 0,
      responseConsistency: timeResponseData?.responseConsistency || 0,
      cognitiveLoad: timeResponseData?.cognitiveLoad || 0,
      // Dados do novo GridNavigationCollector
      spatialAccuracy: gridNavigationData?.spatialAccuracy || 0,
      navigationEfficiency: gridNavigationData?.navigationEfficiency || 0,
      visualScanningPattern: gridNavigationData?.visualScanningPattern || "unknown"
    };
  }
  /**
   * Analisa padrões de atenção
   */
  analyzeAttentionPatternsIntegrated(processorResults, collectorsResults) {
    const attentionData = collectorsResults.collectors?.attentionalSelectivity;
    return {
      focusConsistency: attentionData?.focusConsistency || 0,
      distractionResistance: attentionData?.distractionResistance || 0,
      attentionSpan: this.calculateAttentionSpan(processorResults)
    };
  }
  /**
   * Analisa progressão e mastery (NOVO)
   */
  analyzeProgressionMasteryIntegrated(processorResults, collectorsResults) {
    const progressionData = collectorsResults.collectors?.progressionMastery;
    return {
      masteryLevel: progressionData?.masteryLevel || 0,
      learningVelocity: progressionData?.learningVelocity || 0,
      difficultyAdaptation: progressionData?.difficultyAdaptation || 0,
      challengePreference: progressionData?.challengePreference || "unknown",
      skillPlateau: progressionData?.skillPlateau || false,
      improvementRate: this.calculateImprovementRate(processorResults),
      consistencyScore: this.calculateConsistencyScore(processorResults)
    };
  }
  /**
   * Analisa engajamento emocional (NOVO)
   */
  analyzeEmojiEngagementIntegrated(processorResults, collectorsResults) {
    const emojiData = collectorsResults.collectors?.emojiEngagement;
    return {
      emotionalResponse: emojiData?.emotionalResponse || "neutral",
      engagementLevel: emojiData?.engagementLevel || 0,
      motivationScore: emojiData?.motivationScore || 0,
      emotionalStability: emojiData?.emotionalStability || 0,
      frustrationIndicators: emojiData?.frustrationIndicators || [],
      positivityTrend: this.calculatePositivityTrend(emojiData)
    };
  }
  /**
   * Gera insights terapêuticos específicos
   */
  generateTherapeuticInsights(processorResults, collectorsResults) {
    const insights = [];
    if (processorResults.accuracy < 60) {
      insights.push("Dificuldade significativa na discriminação de cores detectada");
    }
    if (processorResults.averageResponseTime > 3e3) {
      insights.push("Tempo de processamento visual acima do esperado");
    }
    return insights;
  }
  /**
   * Gera recomendações baseadas na análise
   */
  generateRecommendations(processorResults, collectorsResults) {
    const recommendations = [];
    if (processorResults.accuracy < 70) {
      recommendations.push("Exercícios focados em discriminação cromática");
    }
    if (processorResults.averageResponseTime > 2500) {
      recommendations.push("Atividades para melhorar velocidade de processamento visual");
    }
    return recommendations;
  }
  /**
   * Métodos auxiliares para cálculos específicos
   */
  calculateProcessingEfficiency(results) {
    const time = results.averageResponseTime || 5e3;
    return Math.max(0, 100 - time / 50);
  }
  calculateAttentionSpan(results) {
    return Math.min(100, (results.accuracy || 0) + 10);
  }
  /**
   * Calcula taxa de melhoria baseada em dados históricos
   */
  calculateImprovementRate(results) {
    const accuracy = results.accuracy || 0;
    const responseTime = results.averageResponseTime || 5e3;
    return Math.max(0, Math.min(100, accuracy / 100 * 80 + (5e3 - responseTime) / 50));
  }
  /**
   * Calcula score de consistência baseado na variabilidade
   */
  calculateConsistencyScore(results) {
    const accuracy = results.accuracy || 0;
    const responseTime = results.averageResponseTime || 5e3;
    return Math.max(0, Math.min(100, accuracy - (responseTime > 3e3 ? 20 : 0)));
  }
  /**
   * Calcula tendência de positividade baseada em dados emocionais
   */
  calculatePositivityTrend(emojiData) {
    if (!emojiData) return 0;
    const motivationScore = emojiData.motivationScore || 0;
    const engagementLevel = emojiData.engagementLevel || 0;
    const emotionalStability = emojiData.emotionalStability || 0;
    return Math.round((motivationScore + engagementLevel + emotionalStability) / 3);
  }
  /**
   * Avalia completude dos dados
   */
  assessDataCompleteness(processorResults, collectorsResults) {
    let score = 0;
    if (processorResults.accuracy !== void 0) score += 25;
    if (processorResults.averageResponseTime !== void 0) score += 25;
    if (Object.keys(collectorsResults.collectors || {}).length > 0) score += 50;
    return score;
  }
  /**
   * Calcula score de confiança da análise
   */
  calculateConfidenceScore(processorResults, collectorsResults) {
    const dataQuality = this.assessDataCompleteness(processorResults, collectorsResults);
    const collectorCount = Object.keys(collectorsResults.collectors || {}).length;
    return Math.min(100, dataQuality + collectorCount * 5);
  }
  /**
   * Processa com timeout para evitar travamentos
   */
  async processWithTimeout(fn, timeout, errorMsg) {
    return Promise.race([
      fn(),
      new Promise(
        (_, reject) => setTimeout(() => reject(new Error(errorMsg)), timeout)
      )
    ]);
  }
  /**
   * Gera métricas de fallback em caso de erro
   */
  generateFallbackMetrics(collectorName, gameData) {
    return {
      fallback: true,
      collector: collectorName,
      basicScore: 50,
      confidence: "low",
      note: "Generated due to collector error"
    };
  }
  /**
   * Gera análise terapêutica completa
   * @param {Object} metrics - Métricas processadas
   * @param {Object} gameData - Dados do jogo
   * @returns {Object} Análise terapêutica
   */
  generateTherapeuticAnalysis(metrics, gameData, advancedAnalysis = {}) {
    try {
      const analysis = {
        // Análise comportamental
        behavioral: {
          engagement: this.calculateEngagementScore(gameData),
          persistence: this.calculatePersistenceScore(gameData),
          adaptability: this.calculateAdaptabilityScore(gameData),
          frustrationTolerance: this.calculateFrustrationTolerance(gameData),
          socialInteraction: this.calculateSocialInteractionScore(gameData)
        },
        // Análise cognitiva
        cognitive: {
          attention: this.calculateAttentionScore(gameData),
          memory: this.calculateMemoryScore(gameData),
          processingSpeed: this.calculateProcessingSpeedScore(gameData),
          executiveFunction: this.calculateExecutiveFunctionScore(gameData),
          visualProcessing: this.calculateVisualProcessingScore(gameData)
        },
        // FASE CRÍTICA: Análises especializadas integradas
        specialized: {
          progressionInsights: advancedAnalysis.progressionAnalysis || null,
          attentionalProfile: advancedAnalysis.attentionalAnalysis || null,
          visualProcessingProfile: advancedAnalysis.visualAnalysis || null,
          integrationScore: this.calculateIntegrationScore(advancedAnalysis)
        },
        // Análise sensorial
        sensory: {
          visualPerception: this.calculateVisualPerceptionScore(gameData),
          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),
          tactileProcessing: this.calculateTactileProcessingScore(gameData),
          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)
        },
        // Análise motora
        motor: {
          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),
          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),
          coordination: this.calculateCoordinationScore(gameData),
          motorPlanning: this.calculateMotorPlanningScore(gameData)
        },
        // Recomendações terapêuticas
        recommendations: this.generateTherapeuticRecommendations(metrics, gameData),
        // Indicadores de progresso
        progressIndicators: this.generateProgressIndicators(metrics, gameData),
        // Insights específicos do jogo
        gameSpecificInsights: this.generateGameSpecificInsights(metrics, gameData),
        // Metadados
        metadata: {
          analysisTimestamp: (/* @__PURE__ */ new Date()).toISOString(),
          gameType: this.gameType,
          analysisVersion: "3.0.0",
          confidenceScore: this.calculateAnalysisConfidenceScore(metrics, gameData)
        }
      };
      return analysis;
    } catch (error) {
      this.logger?.error("❌ Erro ao gerar análise terapêutica:", error);
      return this.generateFallbackTherapeuticAnalysis(gameData);
    }
  }
  /**
   * Métodos de cálculo de scores terapêuticos
   */
  calculateEngagementScore(gameData) {
    const interactions = gameData.interactions || [];
    const totalTime = gameData.totalTime || 1e3;
    const completionRate = gameData.completionRate || 0;
    let score = 50;
    if (interactions.length > 0) {
      score += Math.min(30, interactions.length * 2);
    }
    if (totalTime > 3e4) {
      score += 10;
    }
    score += completionRate * 10;
    return Math.max(0, Math.min(100, score));
  }
  calculatePersistenceScore(gameData) {
    const attempts = gameData.attempts || 1;
    const errors = gameData.errors || 0;
    const completion = gameData.completion || 0;
    let score = 50;
    if (attempts > 1 && completion > 0.5) {
      score += 20;
    }
    if (errors > 0 && completion > 0.8) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateAdaptabilityScore(gameData) {
    const difficultyChanges = gameData.difficultyChanges || 0;
    const adaptationSuccess = gameData.adaptationSuccess || 0;
    let score = 50;
    if (difficultyChanges > 0) {
      score += adaptationSuccess * 20;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateFrustrationTolerance(gameData) {
    const errors = gameData.errors || 0;
    const quitEarly = gameData.quitEarly || false;
    const completion = gameData.completion || 0;
    let score = 70;
    if (errors > 3 && !quitEarly) {
      score += 15;
    }
    if (completion > 0.8) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateSocialInteractionScore(gameData) {
    const engagement = this.calculateEngagementScore(gameData);
    return Math.max(30, Math.min(80, engagement * 0.8));
  }
  calculateAttentionScore(gameData) {
    const focusTime = gameData.focusTime || 0;
    const distractions = gameData.distractions || 0;
    const responseTime = gameData.averageResponseTime || 3e3;
    let score = 50;
    if (focusTime > 6e4) {
      score += 20;
    }
    if (distractions < 2) {
      score += 15;
    }
    if (responseTime < 2e3) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateMemoryScore(gameData) {
    const accuracy = gameData.accuracy || 0;
    const patterns = gameData.patterns || [];
    let score = 50;
    if (accuracy > 70) {
      score += 25;
    }
    if (patterns.length > 3) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateProcessingSpeedScore(gameData) {
    const responseTime = gameData.averageResponseTime || 3e3;
    const accuracy = gameData.accuracy || 0;
    let score = 50;
    if (responseTime < 1500 && accuracy > 60) {
      score += 30;
    } else if (responseTime < 2500) {
      score += 15;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateExecutiveFunctionScore(gameData) {
    const planningEvidence = gameData.planningEvidence || 0;
    const inhibitionControl = gameData.inhibitionControl || 0;
    const workingMemory = gameData.workingMemory || 0;
    const score = (planningEvidence + inhibitionControl + workingMemory) / 3;
    return Math.max(0, Math.min(100, score));
  }
  calculateVisualProcessingScore(gameData) {
    const visualTasks = gameData.visualTasks || 0;
    const visualAccuracy = gameData.visualAccuracy || 0;
    let score = 50;
    if (visualTasks > 5 && visualAccuracy > 70) {
      score += 25;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateVisualPerceptionScore(gameData) {
    return this.calculateVisualProcessingScore(gameData);
  }
  calculateAuditoryProcessingScore(gameData) {
    const auditoryTasks = gameData.auditoryTasks || 0;
    const auditoryAccuracy = gameData.auditoryAccuracy || 50;
    let score = 50;
    if (auditoryTasks > 0) {
      score = auditoryAccuracy;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateTactileProcessingScore(gameData) {
    const touchInteractions = gameData.touchInteractions || 0;
    const touchAccuracy = gameData.touchAccuracy || 50;
    let score = 50;
    if (touchInteractions > 3) {
      score = touchAccuracy;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateSensoryIntegrationScore(gameData) {
    const visual = this.calculateVisualPerceptionScore(gameData);
    const auditory = this.calculateAuditoryProcessingScore(gameData);
    const tactile = this.calculateTactileProcessingScore(gameData);
    return (visual + auditory + tactile) / 3;
  }
  calculateFineMotorSkillsScore(gameData) {
    const precision = gameData.precision || 50;
    const motorControl = gameData.motorControl || 50;
    return (precision + motorControl) / 2;
  }
  calculateGrossMotorSkillsScore(gameData) {
    const movements = gameData.movements || 0;
    const coordination = gameData.coordination || 50;
    let score = 50;
    if (movements > 10) {
      score = coordination;
    }
    return Math.max(0, Math.min(100, score));
  }
  calculateCoordinationScore(gameData) {
    const eyeHandCoordination = gameData.eyeHandCoordination || 50;
    const bilateralCoordination = gameData.bilateralCoordination || 50;
    return (eyeHandCoordination + bilateralCoordination) / 2;
  }
  calculateMotorPlanningScore(gameData) {
    const planningSteps = gameData.planningSteps || 0;
    const executionSuccess = gameData.executionSuccess || 0;
    let score = 50;
    if (planningSteps > 0) {
      score = executionSuccess;
    }
    return Math.max(0, Math.min(100, score));
  }
  generateTherapeuticRecommendations(metrics, gameData) {
    const recommendations = [];
    const engagement = this.calculateEngagementScore(gameData);
    if (engagement < 50) {
      recommendations.push({
        category: "engagement",
        priority: "high",
        recommendation: "Implementar estratégias de motivação e gamificação",
        rationale: "Baixo engajamento detectado"
      });
    }
    const attention = this.calculateAttentionScore(gameData);
    if (attention < 50) {
      recommendations.push({
        category: "attention",
        priority: "medium",
        recommendation: "Exercícios de foco e concentração",
        rationale: "Dificuldades atencionais identificadas"
      });
    }
    const processing = this.calculateProcessingSpeedScore(gameData);
    if (processing < 50) {
      recommendations.push({
        category: "processing",
        priority: "medium",
        recommendation: "Atividades para melhorar velocidade de processamento",
        rationale: "Processamento lento identificado"
      });
    }
    return recommendations;
  }
  generateProgressIndicators(metrics, gameData) {
    return {
      overallProgress: this.calculateOverallProgress(gameData),
      strengthAreas: this.identifyStrengthAreas(gameData),
      challengeAreas: this.identifyChallengeAreas(gameData),
      developmentGoals: this.generateDevelopmentGoals(gameData),
      milestones: this.generateMilestones(gameData)
    };
  }
  generateGameSpecificInsights(metrics, gameData) {
    return {
      gameType: this.gameType,
      specificMetrics: metrics,
      gamePerformance: this.calculateGamePerformance(gameData),
      adaptationNeeds: this.identifyAdaptationNeeds(gameData)
    };
  }
  calculateAnalysisConfidenceScore(metrics, gameData) {
    let confidence = 50;
    const dataPoints = Object.keys(gameData).length;
    if (dataPoints > 10) confidence += 20;
    else if (dataPoints > 5) confidence += 10;
    const metricsCount = Object.keys(metrics).length;
    if (metricsCount > 5) confidence += 20;
    else if (metricsCount > 3) confidence += 10;
    const sessionTime = gameData.totalTime || 0;
    if (sessionTime > 6e4) confidence += 10;
    return Math.max(0, Math.min(100, confidence));
  }
  generateFallbackTherapeuticAnalysis(gameData) {
    return {
      behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50, socialInteraction: 50 },
      cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50, visualProcessing: 50 },
      sensory: { visualPerception: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },
      motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },
      recommendations: [],
      progressIndicators: { overallProgress: 50, strengthAreas: [], challengeAreas: [], developmentGoals: [], milestones: [] },
      gameSpecificInsights: { gameType: this.gameType, specificMetrics: {}, gamePerformance: 50, adaptationNeeds: [] },
      metadata: { analysisTimestamp: (/* @__PURE__ */ new Date()).toISOString(), gameType: this.gameType, analysisVersion: "3.0.0", confidenceScore: 30 }
    };
  }
  calculateOverallProgress(gameData) {
    const accuracy = gameData.accuracy || 0;
    const completion = gameData.completion || 0;
    const engagement = this.calculateEngagementScore(gameData);
    return (accuracy + completion + engagement) / 3;
  }
  identifyStrengthAreas(gameData) {
    const strengths = [];
    if (gameData.accuracy > 80) strengths.push("Precisão");
    if (gameData.averageResponseTime < 2e3) strengths.push("Velocidade de resposta");
    if (this.calculateEngagementScore(gameData) > 70) strengths.push("Engajamento");
    return strengths;
  }
  identifyChallengeAreas(gameData) {
    const challenges = [];
    if (gameData.accuracy < 50) challenges.push("Precisão");
    if (gameData.averageResponseTime > 4e3) challenges.push("Velocidade de resposta");
    if (this.calculateEngagementScore(gameData) < 40) challenges.push("Engajamento");
    return challenges;
  }
  generateDevelopmentGoals(gameData) {
    const goals = [];
    if (gameData.accuracy < 70) {
      goals.push("Melhorar precisão para 70%+");
    }
    if (gameData.averageResponseTime > 3e3) {
      goals.push("Reduzir tempo de resposta para menos de 3 segundos");
    }
    return goals;
  }
  generateMilestones(gameData) {
    return [
      { milestone: "Primeira sessão completa", achieved: gameData.completion > 0.8 },
      { milestone: "Precisão acima de 50%", achieved: gameData.accuracy > 50 },
      { milestone: "Engajamento sustentado", achieved: this.calculateEngagementScore(gameData) > 60 }
    ];
  }
  calculateGamePerformance(gameData) {
    const accuracy = gameData.accuracy || 0;
    const completion = gameData.completion || 0;
    const efficiency = gameData.efficiency || 0;
    return (accuracy + completion + efficiency) / 3;
  }
  identifyAdaptationNeeds(gameData) {
    const needs = [];
    if (gameData.accuracy < 40) {
      needs.push("Reduzir dificuldade");
    }
    if (gameData.averageResponseTime > 5e3) {
      needs.push("Aumentar tempo limite");
    }
    if (this.calculateEngagementScore(gameData) < 30) {
      needs.push("Aumentar elementos motivacionais");
    }
    return needs;
  }
  /**
   * Processa métricas para estrutura padronizada do banco de dados
   * @param {Object} metrics - Métricas processadas
   * @param {Object} gameData - Dados do jogo
   * @returns {Object} Métricas processadas para banco
   */
  processMetricsForDatabase(metrics, gameData) {
    try {
      return {
        // Métricas básicas
        basic: {
          accuracy: gameData.accuracy || 0,
          responseTime: gameData.averageResponseTime || 0,
          completion: gameData.completion || 0,
          score: gameData.score || 0,
          duration: gameData.totalTime || 0,
          attempts: gameData.attempts || 1,
          errors: gameData.errors || 0
        },
        // Métricas cognitivas
        cognitive: {
          attention: this.calculateAttentionScore(gameData),
          memory: this.calculateMemoryScore(gameData),
          processingSpeed: this.calculateProcessingSpeedScore(gameData),
          executiveFunction: this.calculateExecutiveFunctionScore(gameData)
        },
        // Métricas comportamentais
        behavioral: {
          engagement: this.calculateEngagementScore(gameData),
          persistence: this.calculatePersistenceScore(gameData),
          adaptability: this.calculateAdaptabilityScore(gameData),
          frustrationTolerance: this.calculateFrustrationTolerance(gameData)
        },
        // Métricas sensoriais
        sensory: {
          visualProcessing: this.calculateVisualProcessingScore(gameData),
          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),
          tactileProcessing: this.calculateTactileProcessingScore(gameData),
          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)
        },
        // Métricas motoras
        motor: {
          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),
          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),
          coordination: this.calculateCoordinationScore(gameData),
          motorPlanning: this.calculateMotorPlanningScore(gameData)
        },
        // Métricas específicas do jogo
        gameSpecific: metrics,
        // Metadados
        metadata: {
          gameType: this.gameType,
          processingTimestamp: (/* @__PURE__ */ new Date()).toISOString(),
          version: "3.0.0"
        }
      };
    } catch (error) {
      this.logger?.error("❌ Erro ao processar métricas para banco:", error);
      return {
        basic: { accuracy: 0, responseTime: 0, completion: 0, score: 0, duration: 0, attempts: 1, errors: 0 },
        cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50 },
        behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50 },
        sensory: { visualProcessing: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },
        motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },
        gameSpecific: metrics,
        metadata: { gameType: this.gameType, processingTimestamp: (/* @__PURE__ */ new Date()).toISOString(), version: "3.0.0" }
      };
    }
  }
  /**
   * Calcula pontos de análise baseado na qualidade e quantidade de dados
   * FASE CRÍTICA: Novo método para avaliar capacidade analítica
   */
  calculateAnalysisPoints(collectorsAnalysis, advancedAnalysis) {
    let points = 0;
    points += Object.keys(collectorsAnalysis).length * 10;
    if (advancedAnalysis.progressionAnalysis) points += 25;
    if (advancedAnalysis.attentionalAnalysis) points += 25;
    if (advancedAnalysis.visualAnalysis) points += 25;
    if (Object.keys(advancedAnalysis).length >= 3) points += 25;
    return Math.min(points, 200);
  }
  /**
   * Calcula score de integração das análises especializadas
   * FASE CRÍTICA: Avalia qualidade da integração entre analisadores
   */
  calculateIntegrationScore(advancedAnalysis) {
    const analysisCount = Object.keys(advancedAnalysis).filter((key) => key !== "error").length;
    if (analysisCount === 0) return 0;
    if (analysisCount === 1) return 0.33;
    if (analysisCount === 2) return 0.66;
    if (analysisCount >= 3) return 1;
    return 0.5;
  }
}
const BaseGameConfig = {
  gameSettings: {
    basePoints: 10,
    streakBonus: 5,
    maxWrongOptions: 3,
    feedbackDuration: 2e3,
    celebrationDuration: 3e3,
    adaptiveDifficulty: true,
    showProgressIndicators: true,
    enableHints: true,
    enableEncouragement: true
  }
};
const ColorMatchConfig = {
  gameSettings: {
    ...BaseGameConfig.gameSettings
  },
  difficulties: {
    very_easy: {
      id: "very_easy",
      name: "Muito Fácil",
      gridSize: 4,
      minRoundsPerActivity: 4,
      maxRoundsPerActivity: 7,
      totalRounds: 4,
      colorNames: ["Vermelho", "Azul"],
      colors: [
        { name: "Vermelho", hex: "#FF0000", rgb: "rgb(255, 0, 0)" },
        { name: "Azul", hex: "#0000FF", rgb: "rgb(0, 0, 255)" }
      ],
      itemsPerColor: 2,
      totalItems: 4
    },
    easy: {
      id: "easy",
      name: "Fácil",
      gridSize: 6,
      minRoundsPerActivity: 4,
      maxRoundsPerActivity: 7,
      totalRounds: 5,
      colorNames: ["Vermelho", "Azul", "Verde"],
      colors: [
        { name: "Vermelho", hex: "#FF0000", rgb: "rgb(255, 0, 0)" },
        { name: "Azul", hex: "#0000FF", rgb: "rgb(0, 0, 255)" },
        { name: "Verde", hex: "#00FF00", rgb: "rgb(0, 255, 0)" }
      ],
      itemsPerColor: 3,
      totalItems: 9
    },
    medium: {
      id: "medium",
      name: "Médio",
      gridSize: 8,
      minRoundsPerActivity: 4,
      maxRoundsPerActivity: 7,
      totalRounds: 6,
      colorNames: ["Vermelho", "Azul", "Verde", "Amarelo", "Roxo"],
      colors: [
        { name: "Vermelho", hex: "#FF0000", rgb: "rgb(255, 0, 0)" },
        { name: "Azul", hex: "#0000FF", rgb: "rgb(0, 0, 255)" },
        { name: "Verde", hex: "#00FF00", rgb: "rgb(0, 255, 0)" },
        { name: "Amarelo", hex: "#FFFF00", rgb: "rgb(255, 255, 0)" },
        { name: "Roxo", hex: "#800080", rgb: "rgb(128, 0, 128)" }
      ],
      itemsPerColor: 3,
      totalItems: 15
    },
    hard: {
      id: "hard",
      name: "Avançado",
      gridSize: 8,
      minRoundsPerActivity: 4,
      maxRoundsPerActivity: 7,
      totalRounds: 7,
      colorNames: ["Vermelho", "Azul", "Verde", "Amarelo", "Roxo", "Laranja", "Rosa"],
      colors: [
        { name: "Vermelho", hex: "#FF0000", rgb: "rgb(255, 0, 0)" },
        { name: "Azul", hex: "#0000FF", rgb: "rgb(0, 0, 255)" },
        { name: "Verde", hex: "#00FF00", rgb: "rgb(0, 255, 0)" },
        { name: "Amarelo", hex: "#FFFF00", rgb: "rgb(255, 255, 0)" },
        { name: "Roxo", hex: "#800080", rgb: "rgb(128, 0, 128)" },
        { name: "Laranja", hex: "#FFA500", rgb: "rgb(255, 165, 0)" },
        { name: "Rosa", hex: "#FFC0CB", rgb: "rgb(255, 192, 203)" }
      ],
      itemsPerColor: 4,
      totalItems: 24
    },
    very_hard: {
      id: "very_hard",
      name: "Muito Avançado",
      gridSize: 10,
      minRoundsPerActivity: 4,
      maxRoundsPerActivity: 7,
      totalRounds: 7,
      colorNames: ["Carmesim", "Índigo", "Turquesa", "Magenta", "Ciano", "Coral", "Lavanda", "Dourado"],
      colors: [
        { name: "Carmesim", hex: "#DC143C", rgb: "rgb(220, 20, 60)" },
        { name: "Índigo", hex: "#4B0082", rgb: "rgb(75, 0, 130)" },
        { name: "Turquesa", hex: "#40E0D0", rgb: "rgb(64, 224, 208)" },
        { name: "Magenta", hex: "#FF00FF", rgb: "rgb(255, 0, 255)" },
        { name: "Ciano", hex: "#00FFFF", rgb: "rgb(0, 255, 255)" },
        { name: "Coral", hex: "#FF7F50", rgb: "rgb(255, 127, 80)" },
        { name: "Lavanda", hex: "#E6E6FA", rgb: "rgb(230, 230, 250)" },
        { name: "Dourado", hex: "#FFD700", rgb: "rgb(255, 215, 0)" }
      ],
      itemsPerColor: 5,
      totalItems: 40
    }
  }
};
class GameMetrics {
  constructor(backendConnector = null) {
    this.backendConnector = backendConnector;
    this.sessionData = this.createDefaultSessionData();
  }
  createDefaultSessionData() {
    return {
      startTime: Date.now(),
      totalQuestions: 0,
      correctAnswers: 0,
      wrongAnswers: 0,
      currentStreak: 0,
      maxStreak: 0,
      averageResponseTime: 0,
      responseTimes: [],
      questionsHistory: [],
      categoriesUsed: /* @__PURE__ */ new Set(),
      difficultiesPlayed: /* @__PURE__ */ new Set(),
      activityMetrics: {},
      behavioralAnalysis: {
        activityPreferences: {},
        learningProgression: [],
        difficultyAdaptation: [],
        errorPatterns: {},
        recoveryRate: 1,
        engagementLevel: 1,
        cognitiveLoad: 0.5,
        attentionSpan: 0,
        frustrationLevel: 0,
        motivationLevel: 1
      },
      temporalAnalysis: {
        peakPerformanceTimes: [],
        performanceFluctuations: [],
        sessionDurations: [],
        breakPatterns: [],
        fatigueIndicators: []
      }
    };
  }
  connectToBackend(backendConnector) {
    this.backendConnector = backendConnector;
  }
  async syncWithBackend(data) {
    if (this.backendConnector?.updateMetrics) {
      try {
        await this.backendConnector.updateMetrics(data);
      } catch (error) {
        console.warn("Erro ao sincronizar métricas com backend:", error);
      }
    }
  }
  recordAnswer(isCorrect, responseTime, questionData) {
    this.ensureValidSessionData();
    this.sessionData.totalQuestions++;
    this.sessionData.responseTimes.push(responseTime);
    if (isCorrect) {
      this.sessionData.correctAnswers++;
      this.sessionData.currentStreak++;
      this.sessionData.maxStreak = Math.max(this.sessionData.maxStreak, this.sessionData.currentStreak);
    } else {
      this.sessionData.wrongAnswers++;
      this.sessionData.currentStreak = 0;
    }
    this.sessionData.averageResponseTime = this.sessionData.responseTimes.reduce((a, b) => a + b, 0) / this.sessionData.responseTimes.length;
    if (questionData.category) this.sessionData.categoriesUsed.add(questionData.category);
    if (questionData.difficulty) this.sessionData.difficultiesPlayed.add(questionData.difficulty);
  }
  getAccuracy() {
    if (this.sessionData.totalQuestions === 0) return 100;
    return Math.round(this.sessionData.correctAnswers / this.sessionData.totalQuestions * 100);
  }
  getSessionStats() {
    return {
      ...this.sessionData,
      accuracy: this.getAccuracy(),
      sessionDuration: Date.now() - this.sessionData.startTime,
      categoriesUsed: Array.from(this.sessionData.categoriesUsed),
      difficultiesPlayed: Array.from(this.sessionData.difficultiesPlayed)
    };
  }
  getLearningInsights() {
    const stats = this.getSessionStats();
    const insights = [];
    if (stats.accuracy >= 90) {
      insights.push({ type: "success", category: "overall", message: "Excelente precisão geral!" });
    } else if (stats.accuracy >= 70) {
      insights.push({ type: "good", category: "overall", message: "Boa precisão geral! Continue praticando!" });
    } else {
      insights.push({ type: "improvement", category: "overall", message: "Pratique mais para melhorar suas habilidades!" });
    }
    if (stats.averageResponseTime < 3e3) {
      insights.push({ type: "success", category: "speed", message: "Você responde muito rapidamente!" });
    } else if (stats.averageResponseTime > 8e3) {
      insights.push({ type: "tip", category: "speed", message: "Não tenha pressa, pense com calma antes de responder!" });
    }
    if (stats.maxStreak >= 5) {
      insights.push({ type: "achievement", category: "consistency", message: `Sequência incrível de ${stats.maxStreak} acertos consecutivos!` });
    }
    return insights;
  }
  ensureValidSessionData() {
    if (!this.sessionData) {
      console.warn("sessionData corrompido, recriando...");
      this.sessionData = this.createDefaultSessionData();
    }
  }
  resetSession() {
    this.sessionData = this.createDefaultSessionData();
  }
  startSession(sessionId, userId, difficulty = "medium") {
    this.sessionData.sessionId = sessionId;
    this.sessionData.userId = userId;
    this.sessionData.difficulty = difficulty;
    this.sessionData.startTime = Date.now();
    console.log(`📊 Sessão de métricas iniciada: ${sessionId}`);
  }
  getSessionSummary() {
    return {
      sessionId: this.sessionData.sessionId,
      userId: this.sessionData.userId,
      gameType: "GenericGame",
      ...this.getSessionStats(),
      insights: this.getLearningInsights(),
      completed: true
    };
  }
}
class ColorMatchMetrics extends GameMetrics {
  constructor(backendConnector = null) {
    super(backendConnector);
    this.sessionData.gameVersion = "3.1.0";
    this.sessionData.errorTypes = { colorConfusion: 0, shapeMismatch: 0, patternError: 0 };
    this.sessionData.repetitions = 0;
    this.initializeActivityMetrics();
  }
  initializeActivityMetrics() {
    const activities = [
      "find_the_color",
      "name_the_color",
      "match_the_name",
      "memory_match",
      "sequence_repeat",
      "color_mixing"
    ];
    activities.forEach((activity) => {
      this.sessionData.activityMetrics[activity] = {
        attempts: 0,
        correct: 0,
        wrong: 0,
        averageTime: 0,
        responseTimes: [],
        accuracy: 100,
        difficulty: null,
        bestStreak: 0,
        currentStreak: 0
      };
      switch (activity) {
        case "name_the_color":
          this.sessionData.activityMetrics[activity].namingErrors = [];
          break;
        case "memory_match":
          this.sessionData.activityMetrics[activity].pairMatches = 0;
          break;
        case "sequence_repeat":
          this.sessionData.activityMetrics[activity].sequenceTypes = [];
          break;
        case "color_mixing":
          this.sessionData.activityMetrics[activity].mixingAccuracy = 100;
          break;
      }
    });
  }
  recordAnswer(isCorrect, responseTime, questionData) {
    super.recordAnswer(isCorrect, responseTime, questionData);
    const activity = questionData.activity || "find_the_color";
    if (!this.sessionData.activityMetrics[activity]) {
      this.sessionData.activityMetrics[activity] = {
        attempts: 0,
        correct: 0,
        responseTimes: [],
        difficulty: questionData.difficulty || "medium",
        currentStreak: 0,
        bestStreak: 0,
        averageResponseTime: 0
      };
    }
    const activityMetric = this.sessionData.activityMetrics[activity];
    activityMetric.attempts++;
    activityMetric.responseTimes.push(responseTime);
    activityMetric.difficulty = questionData.difficulty;
    if (isCorrect) {
      activityMetric.correct++;
      activityMetric.currentStreak++;
      activityMetric.bestStreak = Math.max(activityMetric.bestStreak, activityMetric.currentStreak);
    } else {
      activityMetric.wrong++;
      activityMetric.currentStreak = 0;
    }
    activityMetric.accuracy = activityMetric.attempts > 0 ? Math.round(activityMetric.correct / activityMetric.attempts * 100) : 100;
    activityMetric.averageTime = activityMetric.responseTimes.reduce((a, b) => a + b, 0) / activityMetric.responseTimes.length;
    this.recordActivitySpecificData(activity, questionData, isCorrect, responseTime);
    this.sessionData.questionsHistory.push({
      timestamp: Date.now(),
      activity,
      correct: isCorrect,
      responseTime,
      ...questionData
    });
    this.updateBehavioralAnalysis(activity, isCorrect, responseTime, questionData);
  }
  recordActivitySpecificData(activity, questionData, isCorrect, responseTime) {
    const activityMetric = this.sessionData.activityMetrics[activity];
    switch (activity) {
      case "name_the_color":
        if (!isCorrect && questionData.userInput) {
          activityMetric.namingErrors.push({
            expected: questionData.correctAnswer,
            input: questionData.userInput
          });
        }
        break;
      case "memory_match":
        if (isCorrect) {
          activityMetric.pairMatches++;
        }
        break;
      case "sequence_repeat":
        if (questionData.sequenceType) {
          activityMetric.sequenceTypes.push(questionData.sequenceType);
        }
        break;
      case "color_mixing":
        if (questionData.mixingResult) {
          const accuracy = questionData.mixingResult.accuracy || 100;
          activityMetric.mixingAccuracy = (activityMetric.mixingAccuracy * (activityMetric.attempts - 1) + accuracy) / activityMetric.attempts;
        }
        break;
    }
    if (!isCorrect && questionData.element === "color") {
      this.sessionData.errorTypes.colorConfusion++;
    } else if (!isCorrect && questionData.element === "shape") {
      this.sessionData.errorTypes.shapeMismatch++;
    } else if (!isCorrect && questionData.element === "pattern") {
      this.sessionData.errorTypes.patternError++;
    }
  }
  recordRepetition() {
    this.sessionData.repetitions++;
  }
  updateBehavioralAnalysis(activity, isCorrect, responseTime, questionData) {
    const behavioral = this.sessionData.behavioralAnalysis;
    if (!behavioral.activityPreferences[activity]) {
      behavioral.activityPreferences[activity] = { score: 0, attempts: 0 };
    }
    const activityPref = behavioral.activityPreferences[activity];
    activityPref.attempts++;
    const speedScore = Math.max(0, (1e4 - responseTime) / 1e3);
    const accuracyScore = isCorrect ? 10 : 0;
    activityPref.score = (activityPref.score * (activityPref.attempts - 1) + (speedScore + accuracyScore)) / activityPref.attempts;
    behavioral.learningProgression.push({
      activity,
      timestamp: Date.now(),
      correct: isCorrect,
      responseTime,
      difficulty: questionData.difficulty
    });
    const recentAttempts = behavioral.learningProgression.slice(-10);
    const consistency = recentAttempts.filter((attempt) => attempt.correct).length / recentAttempts.length;
    behavioral.engagementLevel = Math.max(0.1, Math.min(1, consistency * 1.2));
    behavioral.cognitiveLoad = Math.min(1, responseTime / 8e3);
    if (!isCorrect) {
      if (!behavioral.errorPatterns[activity]) {
        behavioral.errorPatterns[activity] = [];
      }
      behavioral.errorPatterns[activity].push({
        timestamp: Date.now(),
        questionData,
        responseTime
      });
    }
  }
  getActivityAccuracy(activity) {
    const activityMetric = this.sessionData.activityMetrics[activity];
    if (!activityMetric || activityMetric.attempts === 0) return 100;
    return activityMetric.accuracy;
  }
  getPreferredActivity() {
    const preferences = this.sessionData.behavioralAnalysis.activityPreferences;
    let bestActivity = null;
    let bestScore = -1;
    for (const [activity, data] of Object.entries(preferences)) {
      if (data.attempts >= 3 && data.score > bestScore) {
        bestScore = data.score;
        bestActivity = activity;
      }
    }
    return bestActivity;
  }
  getNeedsImprovementActivity() {
    const activities = this.sessionData.activityMetrics;
    let worstActivity = null;
    let worstAccuracy = 101;
    for (const [activity, data] of Object.entries(activities)) {
      if (data.attempts >= 3 && data.accuracy < worstAccuracy) {
        worstAccuracy = data.accuracy;
        worstActivity = activity;
      }
    }
    return worstActivity;
  }
  getLearningInsights() {
    const insights = super.getLearningInsights();
    const activityNames = {
      "find_the_color": "Encontrar a Cor",
      "name_the_color": "Nomear a Cor",
      "match_the_name": "Combinar o Nome",
      "memory_match": "Jogo da Memória",
      "sequence_repeat": "Repetir Sequência",
      "color_mixing": "Mistura de Cores"
    };
    for (const [activity, data] of Object.entries(this.sessionData.activityMetrics)) {
      if (data.attempts >= 3) {
        const name = activityNames[activity] || activity;
        if (data.accuracy >= 90) {
          insights.push({
            type: "success",
            category: "activity",
            activity,
            message: `Excelente em ${name}! Precisão de ${data.accuracy}%`
          });
        } else if (data.accuracy < 60) {
          insights.push({
            type: "improvement",
            category: "activity",
            activity,
            message: `${name} precisa de mais prática. Precisão: ${data.accuracy}%`
          });
        }
        if (data.averageTime < 3e3) {
          insights.push({
            type: "success",
            category: "speed",
            activity,
            message: `Muito rápido em ${name}! Tempo médio: ${(data.averageTime / 1e3).toFixed(1)}s`
          });
        }
      }
    }
    const preferred = this.getPreferredActivity();
    if (preferred) {
      insights.push({
        type: "info",
        category: "preference",
        message: `Você parece gostar mais de ${activityNames[preferred] || preferred}!`
      });
    }
    const needsWork = this.getNeedsImprovementActivity();
    if (needsWork && needsWork !== preferred) {
      insights.push({
        type: "suggestion",
        category: "improvement",
        message: `Tente praticar mais ${activityNames[needsWork] || needsWork} para equilibrar suas habilidades!`
      });
    }
    return insights;
  }
  getColorConfusionMatrix() {
    const confusions = {};
    this.sessionData.questionsHistory.filter((q) => !q.correct && q.activity === "find_the_color").forEach((q) => {
      const key = `${q.correctAnswer}->${q.answer}`;
      confusions[key] = (confusions[key] || 0) + 1;
    });
    return confusions;
  }
  checkColorBlindnessPatterns() {
    const incorrectColors = this.sessionData.questionsHistory.filter((q) => !q.correct && q.activity === "find_the_color").map((q) => ({ correct: q.correctAnswer, selected: q.answer }));
    return {
      protanopia: incorrectColors.some((c) => c.correct === "Verde" && c.selected === "Vermelho") || incorrectColors.some((c) => c.correct === "Vermelho" && c.selected === "Verde"),
      deuteranopia: incorrectColors.some((c) => c.correct === "Verde" && c.selected === "Vermelho") || incorrectColors.some((c) => c.correct === "Vermelho" && c.selected === "Verde"),
      tritanopia: incorrectColors.some((c) => c.correct === "Azul" && c.selected === "Amarelo") || incorrectColors.some((c) => c.correct === "Amarelo" && c.selected === "Azul")
    };
  }
  exportData() {
    return {
      gameType: "ColorMatch",
      version: "3.1.0",
      sessionData: this.getSessionStats(),
      exportTime: Date.now()
    };
  }
  getSessionSummary() {
    const summary = super.getSessionSummary();
    summary.gameType = "ColorMatch";
    summary.colorConfusionMatrix = this.getColorConfusionMatrix();
    summary.colorBlindnessPatterns = this.checkColorBlindnessPatterns();
    return summary;
  }
}
const colorMatchGame = "_colorMatchGame_px81d_41";
const gameContent = "_gameContent_px81d_85";
const gameHeader = "_gameHeader_px81d_105";
const gameTitle = "_gameTitle_px81d_133";
const gameStats = "_gameStats_px81d_179";
const statCard = "_statCard_px81d_193";
const statValue = "_statValue_px81d_237";
const statLabel = "_statLabel_px81d_251";
const questionArea = "_questionArea_px81d_265";
const questionTitle = "_questionTitle_px81d_285";
const objectsDisplay = "_objectsDisplay_px81d_299";
const countingObject = "_countingObject_px81d_327";
const answerOptions = "_answerOptions_px81d_375";
const answerButton = "_answerButton_px81d_391";
const gameControls = "_gameControls_px81d_501";
const controlButton = "_controlButton_px81d_517";
const headerTtsButton = "_headerTtsButton_px81d_639";
const ttsActive = "_ttsActive_px81d_695";
const activityMenu = "_activityMenu_px81d_815";
const activityButton = "_activityButton_px81d_831";
const active = "_active_px81d_869";
const questionHeader = "_questionHeader_px81d_883";
const optionNumber = "_optionNumber_px81d_899";
const styles = {
  colorMatchGame,
  gameContent,
  gameHeader,
  gameTitle,
  gameStats,
  statCard,
  statValue,
  statLabel,
  questionArea,
  questionTitle,
  objectsDisplay,
  countingObject,
  answerOptions,
  answerButton,
  gameControls,
  controlButton,
  headerTtsButton,
  ttsActive,
  activityMenu,
  activityButton,
  active,
  questionHeader,
  optionNumber
};
var _jsxFileName = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\games\\ColorMatch\\ColorMatchGame.jsx";
const ACTIVITY_TYPES = {
  SPEED_CHALLENGE: {
    id: "speed_challenge",
    name: "Desafio de Velocidade",
    icon: "⚡",
    description: "Identifique cores rapidamente",
    component: "SpeedChallengeActivity"
  },
  COLOR_MEMORY: {
    id: "color_memory",
    name: "Memória de Cores",
    icon: "🧠",
    description: "Lembre-se da sequência de cores",
    component: "ColorMemoryActivity"
  },
  SEQUENCE_COLORS: {
    id: "sequence_colors",
    name: "Sequência de Cores",
    icon: "📝",
    description: "Complete sequências coloridas",
    component: "SequenceColorsActivity"
  }
};
const generateNameTheColor = (config) => {
  const colorData = [{
    color: "#FF0000",
    name: "Vermelho"
  }, {
    color: "#00FF00",
    name: "Verde"
  }, {
    color: "#0000FF",
    name: "Azul"
  }, {
    color: "#FFFF00",
    name: "Amarelo"
  }, {
    color: "#FF00FF",
    name: "Rosa"
  }, {
    color: "#00FFFF",
    name: "Ciano"
  }, {
    color: "#FFA500",
    name: "Laranja"
  }, {
    color: "#800080",
    name: "Roxo"
  }, {
    color: "#008000",
    name: "Verde Escuro"
  }, {
    color: "#FFB6C1",
    name: "Rosa Claro"
  }, {
    color: "#8B4513",
    name: "Marrom"
  }, {
    color: "#000000",
    name: "Preto"
  }];
  const targetIndex = Math.floor(Math.random() * colorData.length);
  const target = colorData[targetIndex];
  const wrongOptions = colorData.filter((item) => item.name !== target.name);
  const shuffledWrong = wrongOptions.sort(() => Math.random() - 0.5).slice(0, 3);
  const allOptions = [target.name, ...shuffledWrong.map((item) => item.name)].sort(() => Math.random() - 0.5);
  return {
    targetColor: target.color,
    colorName: target.name,
    correctAnswer: target.name,
    options: allOptions,
    instruction: "Qual é o nome desta cor?",
    type: "name_challenge"
  };
};
const generateMemoryMatch = (config) => {
  const colors = ["#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF", "#00FFFF"];
  const sequenceLength = Math.min(Math.floor(Math.random() * 2) + 3, 5);
  const sequence = [];
  for (let i = 0; i < sequenceLength; i++) {
    sequence.push(colors[Math.floor(Math.random() * colors.length)]);
  }
  return {
    sequence,
    userSequence: [],
    showSequence: true,
    sequenceComplete: false,
    gamePhase: "showing",
    // 'showing', 'memorizing', 'reproducing', 'finished'
    currentStep: 0,
    instruction: `Memorize a sequência de ${sequenceLength} cores`,
    type: "memory_sequence",
    displayTime: 2e3,
    // 2 segundos para mostrar cada cor
    pauseTime: 500
    // 0.5 segundo entre cores
  };
};
const generatePatternRecognitionData = (config) => {
  const colors = ["#FF0000", "#00FF00", "#0000FF", "#FFFF00"];
  const pattern = [colors[0], colors[1], colors[2]];
  const repeat = [colors[0], colors[1]];
  return {
    pattern,
    repeat,
    correctAnswer: "ABC",
    options: ["ABC", "ABB", "AAB", "BAC"],
    instruction: "Qual padrão se repete?"
  };
};
const generateColorGradientData = (config) => {
  const baseColors = [{
    color: "#FF0000",
    name: "Vermelho"
  }, {
    color: "#00FF00",
    name: "Verde"
  }, {
    color: "#0000FF",
    name: "Azul"
  }];
  const base = baseColors[Math.floor(Math.random() * baseColors.length)];
  const colors = [base.color, base.color + "99", base.color + "66", base.color + "33"];
  return {
    colors,
    correctAnswer: "1,2,3,4",
    options: ["1,2,3,4", "4,3,2,1", "2,1,4,3", "3,4,1,2"],
    instruction: `Ordene os tons de ${base.name} do mais escuro para o mais claro`
  };
};
const generateSequenceData = (config) => {
  const colors = ["#FF0000", "#00FF00", "#0000FF", "#FFFF00"];
  const sequence = [colors[0], colors[1], colors[2]];
  return {
    sequence,
    showSequence: true,
    correctAnswer: colors[0],
    options: colors,
    instruction: "Reproduza a sequência: clique na primeira cor mostrada"
  };
};
const checkAnswer = (activityId, answer, activityData) => {
  return answer === activityData.correctAnswer;
};
function ColorMatchGame({
  onBack
}) {
  const {
    user,
    ttsEnabled = true
  } = reactExports.useContext(SystemContext);
  const {
    settings
  } = useAccessibilityContext();
  const metricsRef = reactExports.useRef(new ColorMatchMetrics());
  const sessionIdRef = reactExports.useRef(v4());
  const [collectorsHub] = reactExports.useState(() => {
    try {
      return new ColorMatchCollectorsHub();
    } catch (error) {
      console.error("❌ Erro ao inicializar ColorMatchCollectorsHub:", error);
      return null;
    }
  });
  const {
    startUnifiedSession,
    recordInteraction,
    updateMetrics,
    sessionId
  } = useUnifiedGameLogic("colormatch");
  const {
    recordInteraction: recordMultisensoryInteraction,
    multisensoryIntegration
  } = useMultisensoryIntegration(sessionId, {
    learningStyle: user?.profile?.learningStyle || "visual"
  });
  const shouldUseTherapeutic = user?.id && user.id !== "anonymous" && user.id !== "";
  useTherapeuticOrchestrator({
    userId: shouldUseTherapeutic ? user.id : null
  });
  const [ttsActive2, setTtsActive] = reactExports.useState(() => {
    const saved = localStorage.getItem("colorMatch_ttsActive");
    return saved !== null ? JSON.parse(saved) : ttsEnabled;
  });
  const toggleTTS = reactExports.useCallback(() => {
    setTtsActive((prev) => {
      const newState = !prev;
      localStorage.setItem("colorMatch_ttsActive", JSON.stringify(newState));
      if (!newState && "speechSynthesis" in window) {
        window.speechSynthesis.cancel();
      }
      return newState;
    });
  }, []);
  const speak = reactExports.useCallback((text, options = {}) => {
    if (!ttsActive2 || !("speechSynthesis" in window)) return;
    window.speechSynthesis.cancel();
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = "pt-BR";
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    window.speechSynthesis.speak(utterance);
  }, [ttsActive2]);
  const [showStartScreen, setShowStartScreen] = reactExports.useState(true);
  const [gameStarted, setGameStarted] = reactExports.useState(false);
  const [cognitiveAnalysisVisible, setCognitiveAnalysisVisible] = reactExports.useState(false);
  const [analysisResults, setAnalysisResults] = reactExports.useState(null);
  const [attemptCount, setAttemptCount] = reactExports.useState(0);
  const [gameState, setGameState] = reactExports.useState({
    score: 0,
    round: 1,
    targetColor: null,
    selectedAnswer: null,
    showFeedback: false,
    accuracy: 100,
    totalRounds: 10,
    difficulty: "easy",
    roundStartTime: null,
    // 🎯 Sistema de atividades
    currentActivity: ACTIVITY_TYPES.SPEED_CHALLENGE.id,
    activityCycle: [ACTIVITY_TYPES.SPEED_CHALLENGE.id, ACTIVITY_TYPES.COLOR_MEMORY.id, ACTIVITY_TYPES.SEQUENCE_COLORS.id],
    activityIndex: 0,
    roundsPerActivity: 10,
    activityRoundCount: 1,
    minRoundsPerActivity: 4,
    maxRoundsPerActivity: 7,
    canSwitchActivity: true,
    // 🔥 Começar permitindo a primeira escolha livre
    isFirstActivityChoice: true,
    // 🔥 Flag para permitir primeira escolha
    // 🎯 Dados específicos de atividades
    activityData: {
      basicMatching: {
        currentQuestion: null,
        colors: [],
        options: []
      },
      speedChallenge: {
        targetColor: null,
        timeLimit: 5e3,
        attempts: 0
      },
      colorMemory: {
        sequence: [],
        userSequence: [],
        showSequence: false,
        gamePhase: "showing",
        currentStep: 0,
        sequenceComplete: false,
        success: false
      },
      sequenceColors: {
        pattern: [],
        userProgress: []
      }
    },
    // 🎯 Métricas comportamentais avançadas
    behavioralMetrics: {
      activityPreferences: {},
      responsePatterns: [],
      adaptiveAdjustments: 0,
      engagementLevel: 1,
      difficultyProgression: [],
      errorPatterns: {},
      recoveryRate: 1,
      attentionSpan: 0,
      cognitiveLoad: 0.5
    },
    // 📊 Estatísticas detalhadas por atividade
    activityStats: {
      speed_challenge: {
        attempts: 0,
        correct: 0,
        averageTime: 0
      },
      color_memory: {
        attempts: 0,
        correct: 0,
        averageTime: 0
      },
      sequence_colors: {
        attempts: 0,
        correct: 0,
        averageTime: 0
      }
    }
  });
  const [gameStartTime, setGameStartTime] = reactExports.useState(null);
  const [attemptStartTime, setAttemptStartTime] = reactExports.useState(null);
  const [gameAttempts, setGameAttempts] = reactExports.useState([]);
  const [cognitiveAnalysis, setCognitiveAnalysis] = reactExports.useState(null);
  const [analysisInProgress, setAnalysisInProgress] = reactExports.useState(false);
  reactExports.useEffect(() => {
    if (metricsRef.current && recordInteraction && updateMetrics) {
      metricsRef.current.connectToBackend({
        recordInteraction,
        updateMetrics
      });
    }
  }, [recordInteraction, updateMetrics]);
  const shuffleArray = reactExports.useCallback((array) => {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  }, []);
  const runCognitiveAnalysis = reactExports.useCallback(async (attempts) => {
    if (analysisInProgress || attempts.length < 3) return;
    setAnalysisInProgress(true);
    try {
      const gameData = {
        sessionId: sessionId || `session_${Date.now()}`,
        attempts,
        difficulty: gameState.difficulty,
        gameMode: "standard",
        sessionDuration: Date.now() - gameStartTime,
        totalScore: gameState.score,
        currentLevel: gameState.round
      };
      const analysis = await collectorsHub.runCompleteAnalysis(gameData);
      setCognitiveAnalysis(analysis);
      if (updateMetrics && analysis && !analysis.error) {
        await updateMetrics({
          type: "cognitive_analysis",
          gameId: "colormatch",
          analysis: analysis.integratedAnalysis,
          synthesisMetrics: analysis.synthesisMetrics,
          developmentProfile: analysis.developmentProfile
        });
      }
    } catch (error) {
      console.error("❌ Erro na análise cognitiva:", error);
    } finally {
      setAnalysisInProgress(false);
    }
  }, [analysisInProgress, sessionId, gameState, gameStartTime, collectorsHub, updateMetrics]);
  reactExports.useEffect(() => {
    return () => {
      if (gameAttempts.length >= 3 && !analysisInProgress) {
        runCognitiveAnalysis(gameAttempts);
      }
    };
  }, [gameAttempts, analysisInProgress, runCognitiveAnalysis]);
  reactExports.useEffect(() => {
    const colorMemoryData = gameState.activityData?.colormemory;
    if (colorMemoryData && gameState.currentActivity === ACTIVITY_TYPES.COLOR_MEMORY.id) {
      if (colorMemoryData.gamePhase === "showing" && colorMemoryData.showSequence) {
        let currentColorIndex = 0;
        const showNextColor = () => {
          if (currentColorIndex < colorMemoryData.sequence.length) {
            setGameState((prev) => ({
              ...prev,
              activityData: {
                ...prev.activityData,
                colormemory: {
                  ...prev.activityData.colormemory,
                  currentStep: currentColorIndex,
                  gamePhase: "showing"
                }
              }
            }));
            currentColorIndex++;
            setTimeout(() => {
              if (currentColorIndex >= colorMemoryData.sequence.length) {
                setTimeout(() => {
                  setGameState((prev) => ({
                    ...prev,
                    activityData: {
                      ...prev.activityData,
                      colormemory: {
                        ...prev.activityData.colormemory,
                        showSequence: false,
                        gamePhase: "reproducing",
                        currentStep: 0
                      }
                    }
                  }));
                }, 1e3);
              } else {
                showNextColor();
              }
            }, (colorMemoryData.displayTime || 2e3) + (colorMemoryData.pauseTime || 500));
          }
        };
        showNextColor();
      }
    }
  }, [gameState.activityData?.colormemory?.gamePhase, gameState.activityData?.colormemory?.showSequence, gameState.currentActivity]);
  const generateActivityContent = reactExports.useCallback((activityId, difficulty) => {
    switch (activityId) {
      case ACTIVITY_TYPES.SPEED_CHALLENGE.id:
        return generateNameTheColor();
      case ACTIVITY_TYPES.COLOR_MEMORY.id:
        return generateMemoryMatch();
      case ACTIVITY_TYPES.SEQUENCE_COLORS.id:
        const colors = ["#FF0000", "#00FF00", "#0000FF", "#FFFF00"];
        const patternTypes = [{
          pattern: [colors[0], colors[1], colors[2]],
          next: colors[0]
        }, {
          pattern: [colors[0], colors[1]],
          next: colors[0]
        }, {
          pattern: [colors[0], colors[1], colors[0]],
          next: colors[1]
        }];
        const selectedPattern = patternTypes[Math.floor(Math.random() * patternTypes.length)];
        return {
          pattern: selectedPattern.pattern,
          correctAnswer: selectedPattern.next,
          options: colors,
          instruction: "Continue o padrão de cores",
          type: "sequence_colors"
        };
      case "pattern_recognition":
        return generatePatternRecognitionData();
      case "color_gradient":
        return generateColorGradientData();
      case "sequence":
        return generateSequenceData();
      default:
        return generateNameTheColor();
    }
  }, [shuffleArray]);
  const startGame = reactExports.useCallback((difficulty) => {
    setShowStartScreen(false);
    setGameStarted(true);
    setGameStartTime(Date.now());
    const config = ColorMatchConfig.difficulties[difficulty] || ColorMatchConfig.difficulties.easy;
    const initialActivity = ACTIVITY_TYPES.SPEED_CHALLENGE.id;
    const activityContent = generateActivityContent(initialActivity, difficulty);
    setGameState((prev) => ({
      ...prev,
      difficulty,
      totalRounds: config.totalRounds || 5,
      minRoundsPerActivity: config.minRoundsPerActivity || 4,
      maxRoundsPerActivity: config.maxRoundsPerActivity || 7,
      roundStartTime: Date.now(),
      currentActivity: initialActivity,
      activityData: {
        ...prev.activityData,
        [initialActivity.replace("_", "")]: activityContent
      }
    }));
    metricsRef.current.startSession(sessionIdRef.current, user?.id || "anonymous", difficulty);
    startUnifiedSession(difficulty);
    setTimeout(() => {
      const activityName = ACTIVITY_TYPES.SPEED_CHALLENGE.name;
      speak(`Bem-vindo ao Color Match! Vamos começar com ${activityName}. ${activityContent.instruction}`);
    }, 1e3);
  }, [generateActivityContent, speak, startUnifiedSession, user?.id]);
  const handleAnswer = reactExports.useCallback((answer) => {
    setGameState((prev) => {
      const currentActivityData = prev.activityData[prev.currentActivity.replace("_", "")];
      const isCorrect = checkAnswer(prev.currentActivity, answer, currentActivityData);
      const responseTime = Date.now() - prev.roundStartTime;
      const attemptData = {
        answer,
        isCorrect,
        responseTime,
        difficulty: prev.difficulty,
        round: prev.round,
        activity: prev.currentActivity
      };
      setGameAttempts((prevAttempts) => [...prevAttempts, attemptData]);
      if (metricsRef.current) {
        metricsRef.current.recordAnswer(isCorrect, responseTime, {
          correctAnswer: currentActivityData.correctAnswer,
          selectedAnswer: answer,
          activity: prev.currentActivity,
          difficulty: prev.difficulty,
          round: prev.round
        });
      }
      if (collectorsHub && typeof collectorsHub.processInteraction === "function") {
        const gameData = {
          action: "color_selection",
          answer,
          isCorrect,
          responseTime,
          difficulty: prev.difficulty,
          round: prev.round,
          activity: prev.currentActivity,
          correctAnswer: currentActivityData.correctAnswer,
          selectedAnswer: answer,
          timestamp: Date.now(),
          sessionId
        };
        collectorsHub.processInteraction(gameData).then((collectorResults) => {
          console.log("🎨 ColorMatch - Coletores executados:", {
            collectorsCount: Object.keys(collectorResults?.collectionResults || {}).length,
            results: collectorResults
          });
        }).catch((error) => {
          console.error("❌ Erro nos coletores ColorMatch:", error);
        });
      } else {
        console.warn("⚠️ ColorMatch - CollectorsHub não disponível ou método processInteraction não encontrado");
      }
      if (recordMultisensoryInteraction) {
        recordMultisensoryInteraction("user_answer", {
          answer,
          isCorrect,
          responseTime,
          activity: prev.currentActivity,
          difficulty: prev.difficulty,
          round: prev.round
        });
      }
      return {
        ...prev,
        selectedAnswer: answer,
        showFeedback: true,
        score: isCorrect ? prev.score + 10 : prev.score,
        activityStats: {
          ...prev.activityStats,
          [prev.currentActivity]: {
            ...prev.activityStats[prev.currentActivity],
            attempts: prev.activityStats[prev.currentActivity].attempts + 1,
            correct: isCorrect ? prev.activityStats[prev.currentActivity].correct + 1 : prev.activityStats[prev.currentActivity].correct
          }
        }
      };
    });
  }, []);
  const generateNewRound = reactExports.useCallback(() => {
    setGameState((prev) => {
      const newRound = prev.round + 1;
      const newActivityRoundCount = prev.activityRoundCount + 1;
      const newActivityContent = generateActivityContent(prev.currentActivity, prev.difficulty);
      return {
        ...prev,
        round: newRound,
        activityRoundCount: newActivityRoundCount,
        canSwitchActivity: newActivityRoundCount >= prev.minRoundsPerActivity,
        roundStartTime: Date.now(),
        showFeedback: false,
        selectedAnswer: null,
        activityData: {
          ...prev.activityData,
          [prev.currentActivity.replace("_", "")]: newActivityContent
        }
      };
    });
  }, [generateActivityContent]);
  const switchActivity = reactExports.useCallback((activityId) => {
    setGameState((prev) => {
      if (prev.currentActivity === activityId) {
        return prev;
      }
      const newActivityContent = generateActivityContent(activityId, prev.difficulty);
      const newActivityIndex = prev.activityCycle.indexOf(activityId);
      if (recordMultisensoryInteraction) {
        recordMultisensoryInteraction("activity_switch", {
          from: prev.currentActivity,
          to: activityId,
          manual: true,
          round: prev.round
        });
      }
      return {
        ...prev,
        currentActivity: activityId,
        activityIndex: newActivityIndex !== -1 ? newActivityIndex : 0,
        activityRoundCount: 1,
        // 🔥 Resetar contador para nova atividade
        canSwitchActivity: true,
        // 🔥 Manter permissão sempre ativa para permitir voltar
        isFirstActivityChoice: false,
        // 🔥 Não é mais a primeira escolha
        roundStartTime: Date.now(),
        showFeedback: false,
        selectedAnswer: null,
        activityData: {
          ...prev.activityData,
          [activityId.replace("_", "")]: newActivityContent
        }
      };
    });
  }, [generateActivityContent, multisensoryIntegration]);
  reactExports.useCallback(() => {
    setGameState((prev) => {
      const newActivityIndex = (prev.activityIndex + 1) % prev.activityCycle.length;
      const newCurrentActivity = prev.activityCycle[newActivityIndex];
      const newActivityContent = generateActivityContent(newCurrentActivity, prev.difficulty);
      if (recordMultisensoryInteraction) {
        recordMultisensoryInteraction("activity_rotation", {
          from: prev.currentActivity,
          to: newCurrentActivity,
          automatic: true,
          round: prev.round
        });
      }
      return {
        ...prev,
        activityIndex: newActivityIndex,
        currentActivity: newCurrentActivity,
        activityRoundCount: 0,
        roundStartTime: Date.now(),
        showFeedback: false,
        selectedAnswer: null,
        activityData: {
          ...prev.activityData,
          [newCurrentActivity.replace("_", "")]: newActivityContent
        }
      };
    });
  }, [generateActivityContent, multisensoryIntegration]);
  const explainGame = reactExports.useCallback(() => {
    const currentActivityInfo = ACTIVITY_TYPES[gameState.currentActivity.toUpperCase().replace("_", "_")];
    if (currentActivityInfo) {
      speak(`Atividade atual: ${currentActivityInfo.name}. ${currentActivityInfo.description}`, {
        rate: 0.8
      });
    }
  }, [gameState.currentActivity, speak]);
  reactExports.useCallback(() => {
    const totalAttempts = Object.values(gameState.activityStats).reduce((sum, stat) => sum + stat.attempts, 0);
    const totalCorrect = Object.values(gameState.activityStats).reduce((sum, stat) => sum + stat.correct, 0);
    return totalAttempts > 0 ? Math.round(totalCorrect / totalAttempts * 100) : 100;
  }, [gameState.activityStats]);
  const renderActivityContent = () => {
    const currentActivityData = gameState.activityData[gameState.currentActivity.replace("_", "")];
    if (!currentActivityData) return null;
    switch (gameState.currentActivity) {
      case ACTIVITY_TYPES.SPEED_CHALLENGE.id:
        return renderSpeedChallengeActivity(currentActivityData);
      case ACTIVITY_TYPES.COLOR_MEMORY.id:
        return renderColorMemoryActivity(currentActivityData);
      case ACTIVITY_TYPES.SEQUENCE_COLORS.id:
        return renderSequenceColorsActivity(currentActivityData);
      default:
        return renderBasicMatchingActivity(currentActivityData);
    }
  };
  const renderBasicMatchingActivity = (data) => /* @__PURE__ */ React.createElement("div", { className: styles.questionArea, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 793,
    columnNumber: 5
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.questionHeader, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 794,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("h2", { className: styles.questionTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 795,
    columnNumber: 9
  } }, data.instruction)), /* @__PURE__ */ React.createElement("div", { className: styles.objectsDisplay, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 798,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.countingObject, style: {
    backgroundColor: data.targetColor,
    width: "120px",
    height: "120px",
    borderRadius: "50%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    margin: "0 auto",
    border: "3px solid rgba(255,255,255,0.3)",
    boxShadow: "0 4px 20px rgba(0,0,0,0.2)"
  }, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 799,
    columnNumber: 9
  } })), /* @__PURE__ */ React.createElement("div", { className: styles.answerOptions, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 816,
    columnNumber: 7
  } }, data.options.map((option) => /* @__PURE__ */ React.createElement("button", { key: option, className: styles.answerButton, onClick: () => handleAnswer(option), "aria-label": `Escolher ${option}`, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 818,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.optionNumber, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 824,
    columnNumber: 13
  } }, option)))));
  const renderSpeedChallengeActivity = (data) => /* @__PURE__ */ React.createElement("div", { className: styles.questionArea, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 833,
    columnNumber: 5
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.questionHeader, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 834,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("h2", { className: styles.questionTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 835,
    columnNumber: 9
  } }, "⚡ Qual é o nome desta cor?")), /* @__PURE__ */ React.createElement("div", { className: styles.objectsDisplay, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 838,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.countingObject, style: {
    backgroundColor: data.targetColor,
    width: "200px",
    height: "200px",
    borderRadius: "20px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    margin: "0 auto",
    border: "4px solid rgba(255,255,255,0.5)",
    boxShadow: "0 8px 32px rgba(0,0,0,0.3)",
    animation: "pulse 2s infinite",
    position: "relative"
  }, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 839,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("div", { style: {
    position: "absolute",
    bottom: "-40px",
    left: "50%",
    transform: "translateX(-50%)",
    fontSize: "1.2rem",
    fontWeight: "bold",
    color: "rgba(255,255,255,0.9)",
    textShadow: "0 2px 4px rgba(0,0,0,0.5)"
  }, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 856,
    columnNumber: 11
  } }))), /* @__PURE__ */ React.createElement("div", { className: styles.answerOptions, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 870,
    columnNumber: 7
  } }, data.options.map((option) => /* @__PURE__ */ React.createElement("button", { key: option, className: styles.answerButton, onClick: () => handleAnswer(option), "aria-label": `Escolher ${option}`, style: {
    fontSize: "1.1rem",
    fontWeight: "bold",
    padding: "1rem 2rem",
    minHeight: "80px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center"
  }, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 872,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.optionText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 887,
    columnNumber: 13
  } }, option)))), /* @__PURE__ */ React.createElement("div", { style: {
    textAlign: "center",
    marginTop: "1rem",
    padding: "1rem",
    backgroundColor: "rgba(0,0,0,0.3)",
    borderRadius: "8px",
    color: "rgba(255,255,255,0.8)",
    fontSize: "0.9rem"
  }, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 892,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("div", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 901,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 901,
    columnNumber: 14
  } }, "⏱️ Desafio de Velocidade:")), /* @__PURE__ */ React.createElement("div", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 902,
    columnNumber: 9
  } }, "Identifique o nome da cor o mais rápido possível!")));
  const renderColorMemoryActivity = (data) => {
    return /* @__PURE__ */ React.createElement("div", { className: styles.questionArea, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 910,
      columnNumber: 7
    } }, /* @__PURE__ */ React.createElement("div", { className: styles.questionHeader, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 911,
      columnNumber: 9
    } }, /* @__PURE__ */ React.createElement("h2", { className: styles.questionTitle, style: {
      fontSize: "2rem"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 912,
      columnNumber: 11
    } }, "🧠 Memória de Cores"), /* @__PURE__ */ React.createElement("p", { className: styles.questionSubtitle, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 913,
      columnNumber: 11
    } }, data.gamePhase === "showing" ? "" : data.gamePhase === "reproducing" ? "" : "Concluído!")), /* @__PURE__ */ React.createElement("div", { className: styles.objectsDisplay, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 920,
      columnNumber: 9
    } }, data.gamePhase === "showing" && /* @__PURE__ */ React.createElement("div", { style: {
      display: "flex",
      gap: "1rem",
      justifyContent: "center",
      alignItems: "center",
      padding: "2rem",
      backgroundColor: "rgba(0,0,0,0.3)",
      borderRadius: "12px",
      border: "2px solid rgba(33, 150, 243, 0.5)"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 923,
      columnNumber: 13
    } }, data.sequence.map((color, index) => /* @__PURE__ */ React.createElement("div", { key: index, style: {
      backgroundColor: index === data.currentStep ? color : "rgba(255,255,255,0.1)",
      width: "80px",
      height: "80px",
      borderRadius: "50%",
      border: index === data.currentStep ? "4px solid #FFD700" : "2px solid rgba(255,255,255,0.3)",
      boxShadow: index === data.currentStep ? "0 0 20px rgba(255, 215, 0, 0.8)" : "none",
      transform: index === data.currentStep ? "scale(1.2)" : "scale(1)",
      transition: "all 0.3s ease",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      fontWeight: "bold",
      color: "white",
      fontSize: "1.2rem"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 934,
      columnNumber: 17
    } }, index + 1))), data.gamePhase === "reproducing" && /* @__PURE__ */ React.createElement("div", { style: {
      display: "flex",
      gap: "1rem",
      justifyContent: "center",
      alignItems: "center",
      padding: "2rem",
      backgroundColor: "rgba(0,0,0,0.3)",
      borderRadius: "12px",
      border: "2px solid rgba(76, 175, 80, 0.5)",
      marginBottom: "2rem"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 961,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      textAlign: "center"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 972,
      columnNumber: 15
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1.1rem",
      fontWeight: "bold",
      marginBottom: "1rem"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 973,
      columnNumber: 17
    } }, "Progresso: ", data.userSequence.length, " / ", data.sequence.length), /* @__PURE__ */ React.createElement("div", { style: {
      display: "flex",
      gap: "0.5rem"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 976,
      columnNumber: 17
    } }, data.sequence.map((_, index) => /* @__PURE__ */ React.createElement("div", { key: index, style: {
      width: "40px",
      height: "40px",
      borderRadius: "50%",
      backgroundColor: index < data.userSequence.length ? "rgba(76, 175, 80, 0.8)" : "rgba(255,255,255,0.2)",
      border: "2px solid rgba(255,255,255,0.3)",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      fontSize: "1rem",
      fontWeight: "bold",
      color: "white"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 978,
      columnNumber: 21
    } }, index < data.userSequence.length ? "✓" : index + 1))))), data.gamePhase === "finished" && /* @__PURE__ */ React.createElement("div", { style: {
      textAlign: "center",
      padding: "2rem",
      backgroundColor: data.success ? "rgba(76, 175, 80, 0.2)" : "rgba(244, 67, 54, 0.2)",
      borderRadius: "12px",
      border: `2px solid ${data.success ? "rgba(76, 175, 80, 0.5)" : "rgba(244, 67, 54, 0.5)"}`
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1005,
      columnNumber: 13
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "2rem",
      marginBottom: "1rem"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1012,
      columnNumber: 15
    } }, data.success ? "🎉" : "😔"), /* @__PURE__ */ React.createElement("div", { style: {
      fontSize: "1.2rem",
      fontWeight: "bold"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1015,
      columnNumber: 15
    } }, data.success ? "Perfeito!" : "Tente novamente!"))), data.gamePhase === "reproducing" && /* @__PURE__ */ React.createElement("div", { className: styles.answerOptions, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1024,
      columnNumber: 11
    } }, ["#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF", "#00FFFF"].map((color) => /* @__PURE__ */ React.createElement("button", { key: color, className: styles.answerButton, onClick: () => {
      const newUserSequence = [...data.userSequence, color];
      const isCorrect = newUserSequence.every((userColor, index) => userColor === data.sequence[index]);
      if (newUserSequence.length === data.sequence.length) {
        const allCorrect = isCorrect;
        handleAnswer(allCorrect ? "correct" : "incorrect");
        setGameState((prev) => ({
          ...prev,
          activityData: {
            ...prev.activityData,
            colormemory: {
              ...prev.activityData.colormemory,
              userSequence: newUserSequence,
              gamePhase: "finished",
              success: allCorrect
            }
          }
        }));
      } else {
        setGameState((prev) => ({
          ...prev,
          activityData: {
            ...prev.activityData,
            colormemory: {
              ...prev.activityData.colormemory,
              userSequence: newUserSequence
            }
          }
        }));
      }
    }, disabled: data.userSequence.length >= data.sequence.length, "aria-label": `Escolher cor`, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1026,
      columnNumber: 15
    } }, /* @__PURE__ */ React.createElement("div", { style: {
      width: "60px",
      height: "60px",
      borderRadius: "50%",
      backgroundColor: color,
      margin: "0 auto",
      border: "2px solid rgba(255,255,255,0.3)",
      boxShadow: "0 2px 8px rgba(0,0,0,0.3)"
    }, __self: this, __source: {
      fileName: _jsxFileName,
      lineNumber: 1069,
      columnNumber: 17
    } })))));
  };
  const renderSequenceColorsActivity = (data) => /* @__PURE__ */ React.createElement("div", { className: styles.questionArea, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1090,
    columnNumber: 5
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.questionHeader, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1091,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("h2", { className: styles.questionTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1092,
    columnNumber: 9
  } }, data.instruction), /* @__PURE__ */ React.createElement("div", { style: {
    fontSize: "0.9rem",
    color: "rgba(255, 255, 255, 0.8)",
    marginTop: "0.5rem"
  }, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1093,
    columnNumber: 9
  } }, "Padrão: Complete a sequência")), /* @__PURE__ */ React.createElement("div", { className: styles.objectsDisplay, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1098,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("div", { style: {
    display: "flex",
    gap: "1rem",
    alignItems: "center",
    justifyContent: "center"
  }, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1099,
    columnNumber: 9
  } }, data.pattern.map((color, index) => /* @__PURE__ */ React.createElement(React.Fragment, { key: index, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1101,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("div", { style: {
    backgroundColor: color,
    width: "60px",
    height: "60px",
    borderRadius: "50%",
    border: "2px solid rgba(255,255,255,0.3)"
  }, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1102,
    columnNumber: 15
  } }), index < data.pattern.length - 1 && /* @__PURE__ */ React.createElement("span", { style: {
    color: "white"
  }, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1109,
    columnNumber: 51
  } }, "→"))), /* @__PURE__ */ React.createElement("span", { style: {
    color: "white"
  }, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1112,
    columnNumber: 11
  } }, "→"), /* @__PURE__ */ React.createElement("div", { style: {
    width: "60px",
    height: "60px",
    borderRadius: "50%",
    border: "2px dashed rgba(255, 255, 255, 0.5)",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    color: "white",
    fontSize: "1.5rem",
    fontWeight: "bold"
  }, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1113,
    columnNumber: 11
  } }, "?"))), /* @__PURE__ */ React.createElement("div", { className: styles.answerOptions, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1130,
    columnNumber: 7
  } }, data.options.map((option) => /* @__PURE__ */ React.createElement("button", { key: option, className: styles.answerButton, onClick: () => handleAnswer(option), "aria-label": `Continuar com ${option}`, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1132,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { style: {
    width: "60px",
    height: "60px",
    borderRadius: "50%",
    backgroundColor: option,
    margin: "0 auto",
    border: "2px solid rgba(255,255,255,0.3)"
  }, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1138,
    columnNumber: 13
  } })))));
  return /* @__PURE__ */ React.createElement("div", { className: `${styles.colorMatchGame} ${settings.reducedMotion ? "reduced-motion" : ""} ${settings.highContrast ? "high-contrast" : ""}`, "data-font-size": settings.fontSize, "data-theme": settings.colorScheme, style: {
    fontSize: settings.fontSize === "small" ? "0.875rem" : settings.fontSize === "large" ? "1.25rem" : "1rem"
  }, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1324,
    columnNumber: 5
  } }, showStartScreen ? /* @__PURE__ */ React.createElement(GameStartScreen, { gameTitle: "Color Match", gameDescription: "Desenvolva sua percepção visual e reconhecimento de cores", gameIcon: "🎨", difficulties: [{
    id: "easy",
    name: "Fácil",
    description: "Cores básicas\nIdeal para iniciantes",
    icon: "😊"
  }, {
    id: "medium",
    name: "Médio",
    description: "Mais cores e tons\nDesafio equilibrado",
    icon: "🎯"
  }, {
    id: "hard",
    name: "Avançado",
    description: "Tons sutis e gradientes\nPara especialistas",
    icon: "🚀"
  }], onStart: startGame, onBack, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1334,
    columnNumber: 9
  } }) : /* @__PURE__ */ React.createElement("div", { className: styles.gameContent, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1347,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.gameHeader, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1349,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("h1", { className: styles.gameTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1350,
    columnNumber: 13
  } }, "🎨 Color Match V3", /* @__PURE__ */ React.createElement("div", { style: {
    fontSize: "0.7rem",
    opacity: 0.8,
    marginTop: "0.25rem"
  }, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1352,
    columnNumber: 15
  } }, ACTIVITY_TYPES[gameState.currentActivity.toUpperCase()]?.name || "Desafio de Velocidade")), /* @__PURE__ */ React.createElement("button", { className: `${styles.headerTtsButton} ${ttsActive2 ? styles.ttsActive : ""}`, onClick: toggleTTS, title: ttsActive2 ? "Desativar TTS" : "Ativar TTS", "aria-label": ttsActive2 ? "Desativar TTS" : "Ativar TTS", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1356,
    columnNumber: 13
  } }, ttsActive2 ? "🔊" : "🔇")), /* @__PURE__ */ React.createElement("div", { className: styles.gameStats, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1367,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statCard, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1368,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statValue, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1369,
    columnNumber: 15
  } }, gameState.score), /* @__PURE__ */ React.createElement("div", { className: styles.statLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1370,
    columnNumber: 15
  } }, "Pontos")), /* @__PURE__ */ React.createElement("div", { className: styles.statCard, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1372,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statValue, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1373,
    columnNumber: 15
  } }, gameState.round), /* @__PURE__ */ React.createElement("div", { className: styles.statLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1374,
    columnNumber: 15
  } }, "Rodada")), /* @__PURE__ */ React.createElement("div", { className: styles.statCard, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1376,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statValue, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1377,
    columnNumber: 15
  } }, gameState.accuracy, "%"), /* @__PURE__ */ React.createElement("div", { className: styles.statLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1378,
    columnNumber: 15
  } }, "Precisão"))), /* @__PURE__ */ React.createElement("div", { className: styles.activityMenu, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1383,
    columnNumber: 11
  } }, Object.values(ACTIVITY_TYPES).map((activity) => /* @__PURE__ */ React.createElement("button", { key: activity.id, className: `${styles.activityButton} ${gameState.currentActivity === activity.id ? styles.active : ""}`, onClick: () => switchActivity(activity.id), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1385,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1392,
    columnNumber: 17
  } }, activity.icon), /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1393,
    columnNumber: 17
  } }, activity.name)))), renderActivityContent(), /* @__PURE__ */ React.createElement("div", { className: styles.gameControls, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1402,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("button", { className: styles.controlButton, onClick: explainGame, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1403,
    columnNumber: 13
  } }, "🔊 Explicar"), /* @__PURE__ */ React.createElement("button", { className: styles.controlButton, onClick: generateNewRound, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1406,
    columnNumber: 13
  } }, "🔄 Nova Rodada"), /* @__PURE__ */ React.createElement("button", { className: styles.controlButton, onClick: onBack, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 1409,
    columnNumber: 13
  } }, "⬅️ Voltar"))));
}
const ColorMatchGame$1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: ColorMatchGame
}, Symbol.toStringTag, { value: "Module" }));
export {
  ColorMatchProcessors as C,
  ColorMatchCollectorsHub as a,
  ColorMatchGame$1 as b
};
//# sourceMappingURL=game-colors-B_gd3llZ.js.map
