{"version": 3, "file": "About-Zdl1Q6En.js", "sources": ["../../src/components/pages/About/About.jsx"], "sourcesContent": ["/**\r\n * @file About.jsx\r\n * @description Página sobre o Portal Betina\r\n * @version 3.0.0\r\n */\r\n\r\nimport React from 'react'\r\nimport styles from './About.module.css'\r\n\r\nfunction About({ onBackToHome }) {\r\n  return (\r\n    <div className={styles.container}>\r\n      {/* Hero Banner */}\r\n      <div className={styles.heroBanner}>\r\n        <div className={styles.heroContent}>\r\n          <h1 className={styles.heroTitle}>Portal Betina</h1>\r\n          <p className={styles.heroSubtitle}>\r\n            Transformando vidas através de atividades neuropedagógicas potencializadas por inteligência artificial\r\n          </p>\r\n          <div className={styles.badgeContainer}>\r\n            <span className={`${styles.techBadge} ${styles.badgePrimary}`}>\r\n              🧠 Desenvolvimento Cognitivo\r\n            </span>\r\n            <span className={`${styles.techBadge} ${styles.badgeGreen}`}>\r\n              🤖 Potencializado por IA\r\n            </span>\r\n            <span className={`${styles.techBadge} ${styles.badgePurple}`}>\r\n              ♿ 100% Acessível\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Seção: O que são Atividades Neuropedagógicas */}\r\n      <section className={styles.section}>\r\n        <h2 className={styles.sectionTitle}>\r\n          <span>🧠</span>\r\n          O que são Atividades Neuropedagógicas?\r\n        </h2>\r\n        <div className={styles.sectionContent}>\r\n          <p>\r\n            <strong>Atividades neuropedagógicas</strong> são intervenções estruturadas que estimulam o \r\n            desenvolvimento cognitivo, emocional e social, especialmente para crianças com autismo, \r\n            TDAH ou outras necessidades específicas.\r\n          </p>\r\n          <p>\r\n            Elas combinam princípios da <strong>neurociência</strong>, <strong>psicologia</strong> e \r\n            <strong>pedagogia</strong> para promover habilidades essenciais como:\r\n          </p>\r\n          \r\n          <ul className={styles.benefitsList}>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>🎯</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Atenção e Concentração:</strong> Melhorar o foco e a capacidade de manter a atenção\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>🧠</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Memória:</strong> Fortalecer a memória de trabalho e de longo prazo\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>🤔</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Raciocínio Lógico:</strong> Desenvolver habilidades de resolução de problemas\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>✋</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Coordenação Motora:</strong> Aprimorar habilidades motoras finas e grossas\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>😊</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Regulação Emocional:</strong> Aprender a identificar e gerenciar emoções\r\n              </span>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Seção: IA */}\r\n      <section className={styles.section}>\r\n        <h2 className={styles.sectionTitle}>\r\n          <span>🤖</span>\r\n          Como a Inteligência Artificial Potencializa o Aprendizado\r\n        </h2>\r\n        <div className={styles.sectionContent}>\r\n          <div className={styles.highlightBox}>          <h3 className={styles.highlightTitle}>\r\n            🚀 Portal Betina V3 - Tecnologia Avançada para Desenvolvimento\r\n          </h3>\r\n          <p className={styles.highlightText}>\r\n            Nossa plataforma integra inteligência artificial com metodologias terapêuticas \r\n            comprovadas para oferecer uma experiência personalizada e eficaz para cada criança.\r\n          </p>\r\n          </div>\r\n\r\n          <div className={styles.aiFeatureGrid}>\r\n            <div className={styles.aiFeatureCard}>\r\n              <div className={styles.aiFeatureIcon}>🎯</div>\r\n              <h4 className={styles.aiFeatureTitle}>Personalização Inteligente</h4>\r\n              <p className={styles.aiFeatureDescription}>\r\n                A IA adapta a dificuldade e o ritmo das atividades baseado no desempenho individual da criança\r\n              </p>\r\n            </div>\r\n\r\n            <div className={styles.aiFeatureCard}>\r\n              <div className={styles.aiFeatureIcon}>📊</div>\r\n              <h4 className={styles.aiFeatureTitle}>Análise de Progresso</h4>\r\n              <p className={styles.aiFeatureDescription}>\r\n                Algoritmos analisam padrões de aprendizado e fornecem insights sobre o desenvolvimento cognitivo\r\n              </p>\r\n            </div>\r\n\r\n            <div className={styles.aiFeatureCard}>\r\n              <div className={styles.aiFeatureIcon}>🎮</div>\r\n              <h4 className={styles.aiFeatureTitle}>Engajamento Otimizado</h4>\r\n              <p className={styles.aiFeatureDescription}>\r\n                IA determina os melhores momentos e tipos de feedback para manter a motivação e interesse\r\n              </p>\r\n            </div>\r\n\r\n            <div className={styles.aiFeatureCard}>\r\n              <div className={styles.aiFeatureIcon}>🔄</div>\r\n              <h4 className={styles.aiFeatureTitle}>Adaptação Contínua</h4>\r\n              <p className={styles.aiFeatureDescription}>\r\n                O sistema aprende continuamente com as interações, melhorando constantemente a experiência\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Seção: Tecnologias */}\r\n      <section className={styles.section}>\r\n        <h2 className={styles.sectionTitle}>\r\n          <span>⚙️</span>\r\n          Tecnologias e Metodologias Aplicadas\r\n        </h2>\r\n        <div className={styles.sectionContent}>\r\n          <p>\r\n            O Portal Betina utiliza tecnologias modernas e metodologias baseadas em evidências científicas:\r\n          </p>\r\n          \r\n          <div className={styles.techCategory}>\r\n            <h4 className={styles.techCategoryTitle}>🧬 Base Científica</h4>\r\n            <div className={styles.techBadges}>\r\n              <span className={styles.techBadge}>Neurociência Cognitiva</span>\r\n              <span className={styles.techBadge}>Psicologia do Desenvolvimento</span>\r\n              <span className={styles.techBadge}>Pedagogia Inclusiva</span>\r\n              <span className={styles.techBadge}>Terapia ABA</span>\r\n              <span className={styles.techBadge}>Neuroplasticidade</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div className={styles.techCategory}>\r\n            <h4 className={styles.techCategoryTitle}>💻 Tecnologia</h4>\r\n            <div className={styles.techBadges}>\r\n              <span className={styles.techBadge}>React + IA</span>\r\n              <span className={styles.techBadge}>Machine Learning</span>\r\n              <span className={styles.techBadge}>Design Responsivo</span>\r\n              <span className={styles.techBadge}>Acessibilidade Web</span>\r\n              <span className={styles.techBadge}>Progressive Web App</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div className={styles.techCategory}>\r\n            <h4 className={styles.techCategoryTitle}>🌈 Acessibilidade</h4>\r\n            <div className={styles.techBadges}>\r\n              <span className={styles.techBadge}>Screen Reader</span>\r\n              <span className={styles.techBadge}>Alto Contraste</span>\r\n              <span className={styles.techBadge}>Navegação por Teclado</span>\r\n              <span className={styles.techBadge}>Feedback Háptico</span>\r\n              <span className={styles.techBadge}>WCAG 2.1 AA</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Seção: Para Famílias */}\r\n      <section className={styles.section}>\r\n        <h2 className={styles.sectionTitle}>\r\n          <span>👨‍👩‍👧‍👦</span>\r\n          Para Pais, Terapeutas e Educadores\r\n        </h2>\r\n        <div className={styles.sectionContent}>\r\n          <p>\r\n            O Portal Betina foi desenvolvido para ser uma ferramenta colaborativa entre famílias e profissionais:\r\n          </p>\r\n          \r\n          <ul className={styles.benefitsList}>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>👩‍⚕️</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Para Terapeutas:</strong> Ferramentas complementares para sessões presenciais e atividades para casa\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>👨‍🏫</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Para Educadores:</strong> Recursos para inclusão escolar e desenvolvimento de habilidades específicas\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>👨‍👩‍👧</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Para Famílias:</strong> Atividades estruturadas para momentos de qualidade e desenvolvimento em casa\r\n              </span>\r\n            </li>\r\n            <li className={styles.benefitItem}>\r\n              <span className={styles.benefitEmoji}>🤝</span>\r\n              <span className={styles.benefitText}>\r\n                <strong>Colaboração:</strong> Dados e progresso compartilhados entre todos os envolvidos no cuidado da criança\r\n              </span>\r\n            </li>\r\n          </ul>\r\n\r\n          <div className={styles.highlightBox}>\r\n            <p className={styles.highlightText}>\r\n              💝 <strong>100% Gratuito e Sempre Será</strong><br/>\r\n              Acreditamos que toda criança merece acesso a ferramentas de qualidade para seu desenvolvimento, \r\n              independentemente da condição socioeconômica da família.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default About\r\n"], "names": ["jsxDEV"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,SAAS,MAAM,EAAE,gBAAgB;AAC/B,SACGA,qCAAAA,OAAA,OAAA,EAAI,WAAW,OAAO,WAErB,UAAA;AAAA,IAACA,qCAAAA,OAAA,OAAA,EAAI,WAAW,OAAO,YACrB,sDAAC,OAAI,EAAA,WAAW,OAAO,aACrB,UAAA;AAAA,MAAAA,4CAAC,MAAG,EAAA,WAAW,OAAO,WAAW,UAAjC,gBAAA,GAAA,QAAA,OAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAA8C,GAAA,IAAA;AAAA,MAC7CA,4CAAA,KAAA,EAAE,WAAW,OAAO,cAAc,UAAnC,yGAAA,GAAA,QAAA,OAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAEA,GAAA,IAAA;AAAA,MACCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,gBACrB,UAAA;AAAA,QAACA,qCAAA,OAAA,QAAA,EAAK,WAAW,GAAG,OAAO,SAAS,IAAI,OAAO,YAAY,IAAI,UAA/D,+BAAA,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAEA,GAAA,IAAA;AAAA,QACAA,qCAAA,OAAC,QAAK,EAAA,WAAW,GAAG,OAAO,SAAS,IAAI,OAAO,UAAU,IAAI,UAA7D,2BAAA,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAEA,GAAA,IAAA;AAAA,QACAA,qCAAA,OAAC,QAAK,EAAA,WAAW,GAAG,OAAO,SAAS,IAAI,OAAO,WAAW,IAAI,UAA9D,mBAAA,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAEA,IAAA;AAAA,MAAA,EATF,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA,GAUA,IAAA;AAAA,IAAA,EAfF,GAAA,QAAA,MAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IAAA,GAAA,IAgBA,EAjBF,GAAA,QAAA,OAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IAkBA,GAAA,IAAA;AAAA,IAGCA,qCAAA,OAAA,WAAA,EAAQ,WAAW,OAAO,SACzB,UAAA;AAAA,MAACA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,cACpB,UAAA;AAAA,QAAAA,qCAAA,OAAC,UAAK,UAAN,KAAA,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAQ,GAAA,IAAA;AAAA,QAAO;AAAA,MAAA,EADjB,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAGA,GAAA,IAAA;AAAA,MACCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,gBACrB,UAAA;AAAA,QAAAA,4CAAC,KACC,EAAA,UAAA;AAAA,UAAAA,qCAAA,OAAC,YAAO,UAAR,8BAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAmC,GAAA,IAAA;AAAA,UAAS;AAAA,QAAA,EAD9C,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAIA,GAAA,IAAA;AAAA,oDACC,KAAE,EAAA,UAAA;AAAA,UAAA;AAAA,UAC2BA,qCAAA,OAAC,YAAO,UAAR,eAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAoB,GAAA,IAAA;AAAA,UAAS;AAAA,UAAEA,qCAAA,OAAC,YAAO,UAAR,aAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAkB,GAAA,IAAA;AAAA,UAAS;AAAA,UACtFA,qCAAA,OAAC,YAAO,UAAR,YAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAiB,GAAA,IAAA;AAAA,UAAS;AAAA,QAAA,EAF5B,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAGA,GAAA,IAAA;AAAA,QAECA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,cACpB,UAAA;AAAA,UAACA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,aACpB,UAAA;AAAA,YAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,cAAc,UAAtC,KAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAwC,GAAA,IAAA;AAAA,YACvCA,qCAAA,OAAA,QAAA,EAAK,WAAW,OAAO,aACtB,UAAA;AAAA,cAAAA,qCAAA,OAAC,YAAO,UAAR,0BAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAA+B,GAAA,IAAA;AAAA,cAAS;AAAA,YAAA,EAD1C,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EAJF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAKA,GAAA,IAAA;AAAA,UACCA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,aACpB,UAAA;AAAA,YAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,cAAc,UAAtC,KAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAwC,GAAA,IAAA;AAAA,YACvCA,qCAAA,OAAA,QAAA,EAAK,WAAW,OAAO,aACtB,UAAA;AAAA,cAAAA,qCAAA,OAAC,YAAO,UAAR,WAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAgB,GAAA,IAAA;AAAA,cAAS;AAAA,YAAA,EAD3B,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EAJF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAKA,GAAA,IAAA;AAAA,UACCA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,aACpB,UAAA;AAAA,YAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,cAAc,UAAtC,KAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAwC,GAAA,IAAA;AAAA,YACvCA,qCAAA,OAAA,QAAA,EAAK,WAAW,OAAO,aACtB,UAAA;AAAA,cAAAA,qCAAA,OAAC,YAAO,UAAR,qBAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAA0B,GAAA,IAAA;AAAA,cAAS;AAAA,YAAA,EADrC,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EAJF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAKA,GAAA,IAAA;AAAA,UACCA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,aACpB,UAAA;AAAA,YAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,cAAc,UAAtC,IAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAuC,GAAA,IAAA;AAAA,YACtCA,qCAAA,OAAA,QAAA,EAAK,WAAW,OAAO,aACtB,UAAA;AAAA,cAAAA,qCAAA,OAAC,YAAO,UAAR,sBAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAA2B,GAAA,IAAA;AAAA,cAAS;AAAA,YAAA,EADtC,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EAJF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAKA,GAAA,IAAA;AAAA,UACCA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,aACpB,UAAA;AAAA,YAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,cAAc,UAAtC,KAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAwC,GAAA,IAAA;AAAA,YACvCA,qCAAA,OAAA,QAAA,EAAK,WAAW,OAAO,aACtB,UAAA;AAAA,cAAAA,qCAAA,OAAC,YAAO,UAAR,uBAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAA4B,GAAA,IAAA;AAAA,cAAS;AAAA,YAAA,EADvC,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EAJF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAKA,IAAA;AAAA,QAAA,EA9BF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GA+BA,IAAA;AAAA,MAAA,EA1CF,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA,GA2CA,IAAA;AAAA,IAAA,EAhDF,GAAA,QAAA,MAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IAiDA,GAAA,IAAA;AAAA,IAGCA,qCAAA,OAAA,WAAA,EAAQ,WAAW,OAAO,SACzB,UAAA;AAAA,MAACA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,cACpB,UAAA;AAAA,QAAAA,qCAAA,OAAC,UAAK,UAAN,KAAA,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAQ,GAAA,IAAA;AAAA,QAAO;AAAA,MAAA,EADjB,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAGA,GAAA,IAAA;AAAA,MACCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,gBACrB,UAAA;AAAA,QAACA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,cAAc,UAAA;AAAA,UAAA;AAAA,UAAWA,4CAAA,MAAA,EAAG,WAAW,OAAO,gBAAgB,UAAtC,iEAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAE/C,GAAA,IAAA;AAAA,UACCA,4CAAA,KAAA,EAAE,WAAW,OAAO,eAAe,UAApC,qKAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAGA,IAAA;AAAA,QAAA,EANA,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAOA,GAAA,IAAA;AAAA,QAECA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,eACrB,UAAA;AAAA,UAACA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,eACrB,UAAA;AAAA,YAAAA,4CAAC,OAAI,EAAA,WAAW,OAAO,eAAe,UAAtC,KAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAwC,GAAA,IAAA;AAAA,YACvCA,4CAAA,MAAA,EAAG,WAAW,OAAO,gBAAgB,UAAtC,6BAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAgE,GAAA,IAAA;AAAA,YAC/DA,4CAAA,KAAA,EAAE,WAAW,OAAO,sBAAsB,UAA3C,iGAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EALF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAMA,GAAA,IAAA;AAAA,UAECA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,eACrB,UAAA;AAAA,YAAAA,4CAAC,OAAI,EAAA,WAAW,OAAO,eAAe,UAAtC,KAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAwC,GAAA,IAAA;AAAA,YACvCA,4CAAA,MAAA,EAAG,WAAW,OAAO,gBAAgB,UAAtC,uBAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAA0D,GAAA,IAAA;AAAA,YACzDA,4CAAA,KAAA,EAAE,WAAW,OAAO,sBAAsB,UAA3C,mGAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EALF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAMA,GAAA,IAAA;AAAA,UAECA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,eACrB,UAAA;AAAA,YAAAA,4CAAC,OAAI,EAAA,WAAW,OAAO,eAAe,UAAtC,KAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAwC,GAAA,IAAA;AAAA,YACvCA,4CAAA,MAAA,EAAG,WAAW,OAAO,gBAAgB,UAAtC,wBAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAA2D,GAAA,IAAA;AAAA,YAC1DA,4CAAA,KAAA,EAAE,WAAW,OAAO,sBAAsB,UAA3C,4FAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EALF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAMA,GAAA,IAAA;AAAA,UAECA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,eACrB,UAAA;AAAA,YAAAA,4CAAC,OAAI,EAAA,WAAW,OAAO,eAAe,UAAtC,KAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAwC,GAAA,IAAA;AAAA,YACvCA,4CAAA,MAAA,EAAG,WAAW,OAAO,gBAAgB,UAAtC,qBAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAwD,GAAA,IAAA;AAAA,YACvDA,4CAAA,KAAA,EAAE,WAAW,OAAO,sBAAsB,UAA3C,6FAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EALF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAMA,IAAA;AAAA,QAAA,EA/BF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAgCA,IAAA;AAAA,MAAA,EA1CF,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA,GA2CA,IAAA;AAAA,IAAA,EAhDF,GAAA,QAAA,MAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IAiDA,GAAA,IAAA;AAAA,IAGCA,qCAAA,OAAA,WAAA,EAAQ,WAAW,OAAO,SACzB,UAAA;AAAA,MAACA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,cACpB,UAAA;AAAA,QAAAA,qCAAA,OAAC,UAAK,UAAN,KAAA,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAQ,GAAA,IAAA;AAAA,QAAO;AAAA,MAAA,EADjB,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAGA,GAAA,IAAA;AAAA,MACCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,gBACrB,UAAA;AAAA,QAAAA,qCAAA,OAAC,OAAE,UAAH,kGAAA,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAEA,GAAA,IAAA;AAAA,QAECA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,cACrB,UAAA;AAAA,UAAAA,4CAAC,MAAG,EAAA,WAAW,OAAO,mBAAmB,UAAzC,qBAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAA2D,GAAA,IAAA;AAAA,UAC1DA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,YACrB,UAAA;AAAA,YAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,WAAW,UAAnC,yBAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAyD,GAAA,IAAA;AAAA,YACxDA,4CAAA,QAAA,EAAK,WAAW,OAAO,WAAW,UAAnC,gCAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAgE,GAAA,IAAA;AAAA,YAC/DA,4CAAA,QAAA,EAAK,WAAW,OAAO,WAAW,UAAnC,sBAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAsD,GAAA,IAAA;AAAA,YACrDA,4CAAA,QAAA,EAAK,WAAW,OAAO,WAAW,UAAnC,cAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAA8C,GAAA,IAAA;AAAA,YAC7CA,4CAAA,QAAA,EAAK,WAAW,OAAO,WAAW,UAAnC,oBAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAAoD,IAAA;AAAA,UAAA,EALtD,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAMA,IAAA;AAAA,QAAA,EARF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QASA,GAAA,IAAA;AAAA,QAECA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,cACrB,UAAA;AAAA,UAAAA,4CAAC,MAAG,EAAA,WAAW,OAAO,mBAAmB,UAAzC,gBAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAsD,GAAA,IAAA;AAAA,UACrDA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,YACrB,UAAA;AAAA,YAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,WAAW,UAAnC,aAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAA6C,GAAA,IAAA;AAAA,YAC5CA,4CAAA,QAAA,EAAK,WAAW,OAAO,WAAW,UAAnC,mBAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAmD,GAAA,IAAA;AAAA,YAClDA,4CAAA,QAAA,EAAK,WAAW,OAAO,WAAW,UAAnC,oBAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAoD,GAAA,IAAA;AAAA,YACnDA,4CAAA,QAAA,EAAK,WAAW,OAAO,WAAW,UAAnC,qBAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAqD,GAAA,IAAA;AAAA,YACpDA,4CAAA,QAAA,EAAK,WAAW,OAAO,WAAW,UAAnC,sBAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAAsD,IAAA;AAAA,UAAA,EALxD,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAMA,IAAA;AAAA,QAAA,EARF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QASA,GAAA,IAAA;AAAA,QAECA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,cACrB,UAAA;AAAA,UAAAA,4CAAC,MAAG,EAAA,WAAW,OAAO,mBAAmB,UAAzC,oBAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAA0D,GAAA,IAAA;AAAA,UACzDA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,YACrB,UAAA;AAAA,YAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,WAAW,UAAnC,gBAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAgD,GAAA,IAAA;AAAA,YAC/CA,4CAAA,QAAA,EAAK,WAAW,OAAO,WAAW,UAAnC,iBAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAiD,GAAA,IAAA;AAAA,YAChDA,4CAAA,QAAA,EAAK,WAAW,OAAO,WAAW,UAAnC,wBAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAwD,GAAA,IAAA;AAAA,YACvDA,4CAAA,QAAA,EAAK,WAAW,OAAO,WAAW,UAAnC,mBAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAmD,GAAA,IAAA;AAAA,YAClDA,4CAAA,QAAA,EAAK,WAAW,OAAO,WAAW,UAAnC,cAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAA8C,IAAA;AAAA,UAAA,EALhD,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAMA,IAAA;AAAA,QAAA,EARF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GASA,IAAA;AAAA,MAAA,EApCF,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA,GAqCA,IAAA;AAAA,IAAA,EA1CF,GAAA,QAAA,MAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IA2CA,GAAA,IAAA;AAAA,IAGCA,qCAAA,OAAA,WAAA,EAAQ,WAAW,OAAO,SACzB,UAAA;AAAA,MAACA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,cACpB,UAAA;AAAA,QAAAA,qCAAA,OAAC,UAAK,UAAN,cAAA,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAiB,GAAA,IAAA;AAAA,QAAO;AAAA,MAAA,EAD1B,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAGA,GAAA,IAAA;AAAA,MACCA,qCAAA,OAAA,OAAA,EAAI,WAAW,OAAO,gBACrB,UAAA;AAAA,QAAAA,qCAAA,OAAC,OAAE,UAAH,wGAAA,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAEA,GAAA,IAAA;AAAA,QAECA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,cACpB,UAAA;AAAA,UAACA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,aACpB,UAAA;AAAA,YAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,cAAc,UAAtC,QAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAA2C,GAAA,IAAA;AAAA,YAC1CA,qCAAA,OAAA,QAAA,EAAK,WAAW,OAAO,aACtB,UAAA;AAAA,cAAAA,qCAAA,OAAC,YAAO,UAAR,mBAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAwB,GAAA,IAAA;AAAA,cAAS;AAAA,YAAA,EADnC,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EAJF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAKA,GAAA,IAAA;AAAA,UACCA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,aACpB,UAAA;AAAA,YAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,cAAc,UAAtC,QAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAA2C,GAAA,IAAA;AAAA,YAC1CA,qCAAA,OAAA,QAAA,EAAK,WAAW,OAAO,aACtB,UAAA;AAAA,cAAAA,qCAAA,OAAC,YAAO,UAAR,mBAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAwB,GAAA,IAAA;AAAA,cAAS;AAAA,YAAA,EADnC,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EAJF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAKA,GAAA,IAAA;AAAA,UACCA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,aACpB,UAAA;AAAA,YAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,cAAc,UAAtC,WAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAA8C,GAAA,IAAA;AAAA,YAC7CA,qCAAA,OAAA,QAAA,EAAK,WAAW,OAAO,aACtB,UAAA;AAAA,cAAAA,qCAAA,OAAC,YAAO,UAAR,iBAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAsB,GAAA,IAAA;AAAA,cAAS;AAAA,YAAA,EADjC,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EAJF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAKA,GAAA,IAAA;AAAA,UACCA,qCAAA,OAAA,MAAA,EAAG,WAAW,OAAO,aACpB,UAAA;AAAA,YAAAA,4CAAC,QAAK,EAAA,WAAW,OAAO,cAAc,UAAtC,KAAA,GAAA,QAAA,OAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAwC,GAAA,IAAA;AAAA,YACvCA,qCAAA,OAAA,QAAA,EAAK,WAAW,OAAO,aACtB,UAAA;AAAA,cAAAA,qCAAA,OAAC,YAAO,UAAR,eAAA,GAAA,QAAA,OAAA;AAAA,gBAAA,UAAA;AAAA,gBAAA,YAAA;AAAA,gBAAA,cAAA;AAAA,cAAoB,GAAA,IAAA;AAAA,cAAS;AAAA,YAAA,EAD/B,GAAA,QAAA,MAAA;AAAA,cAAA,UAAA;AAAA,cAAA,YAAA;AAAA,cAAA,cAAA;AAAA,YAAA,GAEA,IAAA;AAAA,UAAA,EAJF,GAAA,QAAA,MAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAA,GAKA,IAAA;AAAA,QAAA,EAxBF,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAyBA,GAAA,IAAA;AAAA,QAEAA,qCAAAA,OAAC,SAAI,WAAW,OAAO,cACrB,UAACA,qCAAA,OAAA,KAAA,EAAE,WAAW,OAAO,eAAe,UAAA;AAAA,UAAA;AAAA,UAC/BA,qCAAA,OAAC,YAAO,UAAR,8BAAA,GAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAmC,GAAA,IAAA;AAAA,sDAAU,MAAD,IAAA,QAAA,OAAA;AAAA,YAAA,UAAA;AAAA,YAAA,YAAA;AAAA,YAAA,cAAA;AAAA,UAAG,GAAA,IAAA;AAAA,UAAE;AAAA,QAAA,EADtD,GAAA,QAAA,MAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAAA,IAIA,EALF,GAAA,QAAA,OAAA;AAAA,UAAA,UAAA;AAAA,UAAA,YAAA;AAAA,UAAA,cAAA;AAAA,QAAA,GAMA,IAAA;AAAA,MAAA,EAtCF,GAAA,QAAA,MAAA;AAAA,QAAA,UAAA;AAAA,QAAA,YAAA;AAAA,QAAA,cAAA;AAAA,MAAA,GAuCA,IAAA;AAAA,IAAA,EA5CF,GAAA,QAAA,MAAA;AAAA,MAAA,UAAA;AAAA,MAAA,YAAA;AAAA,MAAA,cAAA;AAAA,IAAA,GA6CA,IAAA;AAAA,EAAA,EA1NF,GAAA,QAAA,MAAA;AAAA,IAAA,UAAA;AAAA,IAAA,YAAA;AAAA,IAAA,cAAA;AAAA,EA2NA,GAAA,IAAA;AAEJ;"}