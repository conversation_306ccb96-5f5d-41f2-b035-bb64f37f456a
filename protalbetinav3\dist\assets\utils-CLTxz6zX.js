const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/services-M1ydzWhv.js","assets/dashboard-DanqcTsU.js","assets/vendor-react-ByWh_-BW.js","assets/vendor-misc-DneMUARX.js","assets/vendor-charts-Cii0KTpx.js","assets/context-Ch-5FaFa.js","assets/hooks-NJkOkh4y.js","assets/vendor-utils-CjlX8hrF.js","assets/admin-D2mpdgvV.js","assets/admin-BQPZfmpR.css","assets/dashboard-C4HFo8oW.css","assets/game-colors-B_gd3llZ.js","assets/game-association-B9GAxBuN.js","assets/game-association-aRlfWsTT.css","assets/game-colors-WFKpsHgQ.css","assets/game-letters-v8KNWHXS.js","assets/game-letters-CgoS80mu.css","assets/game-memory-6_ujaMB2.js","assets/vendor-motion-CJek6P2z.js","assets/game-memory-BTGvJ0Wb.css","assets/game-musical-Ci_rqtJn.js","assets/game-musical-Srt0Rn1x.css","assets/game-patterns-GQY4qytf.js","assets/game-patterns-C7lF44VH.css","assets/game-puzzle-BLc_eXaF.js","assets/game-puzzle-jC3b1JUV.css","assets/game-numbers-tpTS4tK7.js","assets/game-numbers-CWskqaet.css","assets/game-creative-iDOKdRXI.js","assets/game-creative-ByDHG8hJ.css"])))=>i.map(i=>d[i]);
import { _ as __vitePreload } from "./dashboard-DanqcTsU.js";
import { r as reactExports } from "./vendor-react-ByWh_-BW.js";
import { c as SystemOrchestrator, d as MetricsAggregator } from "./services-M1ydzWhv.js";
class BaseCollector {
  constructor(name = "BaseCollector") {
    this.name = name;
    this.isActive = true;
    this.data = [];
    this.metadata = {
      createdAt: (/* @__PURE__ */ new Date()).toISOString(),
      version: "1.0.0",
      type: "base"
    };
  }
  /**
   * Coleta dados de interação
   * @param {Object} data - Dados a serem coletados
   * @returns {Promise<boolean>} - Sucesso da operação
   */
  async collect(data) {
    try {
      if (!this.isActive) {
        console.warn(`Collector ${this.name} está inativo`);
        return false;
      }
      const enrichedData = {
        ...data,
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        collectorName: this.name,
        id: this.generateId()
      };
      this.data.push(enrichedData);
      await this.onDataCollected(enrichedData);
      return true;
    } catch (error) {
      console.error(`Erro no collector ${this.name}:`, error);
      return false;
    }
  }
  /**
   * Hook chamado após coleta de dados
   * @param {Object} data - Dados coletados
   */
  async onDataCollected(data) {
  }
  /**
   * Gera ID único para os dados
   * @returns {string} - ID único
   */
  generateId() {
    return `${this.name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  /**
   * Obtém todos os dados coletados
   * @returns {Array} - Array de dados
   */
  getData() {
    return [...this.data];
  }
  /**
   * Filtra dados por critério
   * @param {Function} filter - Função de filtro
   * @returns {Array} - Dados filtrados
   */
  getFilteredData(filter) {
    return this.data.filter(filter);
  }
  /**
   * Limpa todos os dados coletados
   */
  clearData() {
    this.data = [];
  }
  /**
   * Ativa o collector
   */
  activate() {
    this.isActive = true;
  }
  /**
   * Desativa o collector
   */
  deactivate() {
    this.isActive = false;
  }
  /**
   * Obtém estatísticas básicas dos dados
   * @returns {Object} - Estatísticas
   */
  getStats() {
    return {
      totalEntries: this.data.length,
      firstEntry: this.data[0]?.timestamp || null,
      lastEntry: this.data[this.data.length - 1]?.timestamp || null,
      isActive: this.isActive,
      name: this.name
    };
  }
  /**
   * Exporta dados em formato JSON
   * @returns {Object} - Dados exportados
   */
  export() {
    return {
      metadata: this.metadata,
      stats: this.getStats(),
      data: this.getData()
    };
  }
  /**
   * Valida se os dados estão no formato correto
   * @param {Object} data - Dados para validar
   * @returns {boolean} - Se os dados são válidos
   */
  validateData(data) {
    return data && typeof data === "object";
  }
  /**
   * Processa dados antes da coleta
   * @param {Object} data - Dados brutos
   * @returns {Object} - Dados processados
   */
  preprocessData(data) {
    return data;
  }
  /**
   * Análise básica dos dados coletados
   * @returns {Object} - Resultado da análise
   */
  analyze() {
    const stats = this.getStats();
    return {
      collector: this.name,
      totalInteractions: stats.totalEntries,
      timeSpan: {
        start: stats.firstEntry,
        end: stats.lastEntry
      },
      averageDataSize: this.data.length > 0 ? this.data.reduce((sum, item) => sum + JSON.stringify(item).length, 0) / this.data.length : 0,
      dataTypes: [...new Set(this.data.map((item) => item.type || "unknown"))],
      status: this.isActive ? "active" : "inactive"
    };
  }
}
const getRealMetrics = async (userId = "demo_user") => {
  const last30Days = new Date(Date.now() - 30 * 24 * 60 * 60 * 1e3);
  let gameScores, gameSessions, userProgress, gameMetrics;
  try {
    gameScores = JSON.parse(localStorage.getItem("gameScores") || "[]");
    gameSessions = JSON.parse(localStorage.getItem("gameSessions") || "[]");
    userProgress = JSON.parse(localStorage.getItem("userProgress") || "{}");
    gameMetrics = JSON.parse(localStorage.getItem("gameMetrics") || "[]");
    let serverMetrics = {};
    try {
      const response = await fetch("/api/backup/user-data", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          userId,
          options: {
            gameMetrics: true,
            sessionData: true,
            gameProgress: true
          }
        })
      });
      if (response.ok) {
        const serverData = await response.json();
        if (serverData.success) {
          serverMetrics = serverData.data;
        }
      }
    } catch (serverError) {
      console.warn("Erro ao buscar dados do servidor, usando dados locais:", serverError);
    }
    const systemOrchestrator = new SystemOrchestrator();
    const metricsAggregator = new MetricsAggregator();
    const combinedScores = gameScores.slice();
    const combinedSessions = gameSessions.slice();
    if (serverMetrics.gameMetrics && serverMetrics.gameMetrics.processedMetrics) {
      Object.keys(serverMetrics.gameMetrics.processedMetrics).forEach((gameId) => {
        const serverGame = serverMetrics.gameMetrics.processedMetrics[gameId];
        const localGameData = combinedScores.filter((score) => score.gameId === gameId);
        if (localGameData.length === 0 && serverGame.sessions > 0) {
          for (let i = 0; i < Math.min(serverGame.sessions, 10); i++) {
            combinedScores.push({
              gameId,
              score: Math.round(serverGame.avgScore + (Math.random() - 0.5) * 20),
              accuracy: Math.round(serverGame.avgAccuracy || 85),
              timeSpent: serverGame.avgTime || 6e4,
              timestamp: new Date(Date.now() - i * 24 * 60 * 60 * 1e3).toISOString(),
              source: "server"
            });
          }
        }
      });
    }
    const recentScores = combinedScores.filter((score) => new Date(score.timestamp) >= last30Days);
    const recentSessions = combinedSessions.filter((session) => new Date(session.timestamp) >= last30Days);
    const totalSessions = recentSessions.length || 0;
    const totalScores = recentScores.length || 0;
    const avgAccuracy = totalScores > 0 ? Math.round(recentScores.reduce((acc, score) => acc + (score.accuracy || 0), 0) / totalScores) : 0;
    const avgTimeSpent = totalScores > 0 ? Math.round(recentScores.reduce((acc, score) => acc + (score.timeSpent || 0), 0) / totalScores) : 0;
    const completionRate = totalSessions > 0 ? Math.round(totalScores / totalSessions * 100) : 0;
    let aggregatedMetrics = {};
    if (recentScores.length > 0) {
      try {
        const metricsData = recentScores.map((score) => ({
          type: "performance",
          gameId: score.gameId,
          userId: score.userId || userId,
          timestamp: score.timestamp,
          accuracy: score.accuracy || 0,
          responseTime: score.responseTime || 5e3,
          completionRate: score.completionRate || 0,
          sessionTime: score.timeSpent || 0,
          correct: score.correct || false,
          totalAttempts: score.totalAttempts || 1,
          correctAnswers: score.correctAnswers || (score.correct ? 1 : 0),
          source: score.source || "local"
        }));
        aggregatedMetrics = await metricsAggregator.aggregateMetrics(metricsData);
      } catch (aggregatorError) {
        console.warn("Erro ao usar MetricsAggregator, usando métricas locais:", aggregatorError);
        aggregatedMetrics = generateLocalMetrics(recentScores);
      }
    }
    const gameProgress = calculateGameProgress(recentScores);
    const weeklyData = generateWeeklyData(recentScores);
    const monthlyData = generateMonthlyData(recentScores);
    const dataSourceInfo = {
      local: {
        scores: gameScores.length,
        sessions: gameSessions.length,
        hasUserProgress: Object.keys(userProgress).length > 0
      },
      server: {
        connected: Object.keys(serverMetrics).length > 0,
        categories: Object.keys(serverMetrics),
        lastSync: serverMetrics.timestamp || null
      },
      combined: {
        totalScores: combinedScores.length,
        totalSessions: combinedSessions.length,
        dataQuality: recentScores.length > 5 ? "high" : recentScores.length > 0 ? "medium" : "low"
      }
    };
    return {
      // Métricas gerais
      totalSessions,
      totalScores,
      avgAccuracy,
      avgTimeSpent,
      completionRate,
      // Métricas agregadas do sistema
      systemMetrics: aggregatedMetrics,
      // Progresso por jogo
      gameProgress,
      // Perfil cognitivo baseado em dados reais
      cognitiveProfile: generateCognitiveProfile(recentScores, aggregatedMetrics),
      // Métricas sensoriais
      sensoryMetrics: generateSensoryMetrics(recentScores),
      // Dados neuropedagógicos
      neuroPedagogicalData: generateNeuroPedagogicalData(recentScores, aggregatedMetrics),
      // Informações sobre fontes de dados
      dataSource: dataSourceInfo,
      // Metadados
      metadata: {
        lastUpdate: (/* @__PURE__ */ new Date()).toISOString(),
        dataQuality: dataSourceInfo.combined.dataQuality,
        hasServerData: dataSourceInfo.server.connected,
        recordCount: {
          local: dataSourceInfo.local.scores,
          server: serverMetrics.gameMetrics?.processedMetrics ? Object.keys(serverMetrics.gameMetrics.processedMetrics).length : 0,
          combined: combinedScores.length
        }
      },
      // Dados temporais - consolidados
      weeklyData,
      monthlyData,
      // Dados em tempo real e sistema
      lastUpdate: (/* @__PURE__ */ new Date()).toISOString(),
      activeUsers: getCurrentActiveUsers(),
      systemHealth: getSystemHealth(),
      // Metadados do sistema
      source: "SystemOrchestrator",
      version: "3.0.0"
    };
  } catch (error) {
    console.error("Erro ao coletar métricas reais:", error);
    return getDefaultMetrics();
  }
};
const getAIMetrics = async (childId, gameData) => {
  try {
    let AIBrainOrchestrator = null;
    try {
      const module = await __vitePreload(() => import("./services-M1ydzWhv.js").then((n) => n.e), true ? __vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29]) : void 0);
      AIBrainOrchestrator = module.AIBrainOrchestrator;
    } catch (importError) {
      console.warn("AIBrainOrchestrator não disponível, usando fallback:", importError);
      return {
        success: false,
        aiReport: null,
        aiConfidence: 0,
        systemAnalysis: null,
        metadata: { error: "AIBrainOrchestrator não disponível" },
        source: "fallback",
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    }
    const aiOrchestrator = AIBrainOrchestrator.getInstance();
    const aiAnalysis = await aiOrchestrator.processGameMetrics(gameData.gameName, gameData.metrics);
    return {
      success: aiAnalysis.success,
      aiReport: aiAnalysis.report,
      aiConfidence: aiAnalysis.aiConfidence,
      systemAnalysis: aiAnalysis.systemAnalysis,
      metadata: aiAnalysis.metadata,
      source: "AIBrainOrchestrator",
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    };
  } catch (error) {
    console.error("Erro ao coletar métricas de IA:", error);
    return {
      success: false,
      error: error.message,
      aiReport: null,
      aiConfidence: 0,
      source: "AIBrainOrchestrator",
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    };
  }
};
function generateLocalMetrics(recentScores) {
  return {
    performance: {
      averageAccuracy: recentScores.reduce((sum, s) => sum + (s.accuracy || 0), 0) / recentScores.length,
      averageResponseTime: recentScores.reduce((sum, s) => sum + (s.responseTime || 0), 0) / recentScores.length,
      totalSessions: recentScores.length,
      completionRate: recentScores.filter((s) => s.completed).length / recentScores.length
    },
    cognitive: {
      attentionScore: recentScores.length > 0 ? Math.round(recentScores.reduce((sum, s) => sum + (s.accuracy || 0), 0) / recentScores.length) : 82,
      memoryScore: recentScores.length > 0 ? Math.round(recentScores.reduce((sum, s) => sum + (s.correctAnswers || 0), 0) / recentScores.length * 10) : 78,
      processingSpeedScore: recentScores.length > 0 ? Math.max(50, Math.min(100, Math.round(5e3 / (recentScores.reduce((sum, s) => sum + (s.responseTime || 5e3), 0) / recentScores.length) * 100))) : 85
    },
    behavioral: {
      engagementLevel: recentScores.length > 0 ? Math.round(recentScores.reduce((sum, s) => sum + (s.timeSpent || 0), 0) / recentScores.length / 1e3) : 88,
      motivationLevel: recentScores.length > 0 ? Math.round(recentScores.filter((s) => s.completed).length / recentScores.length * 100) : 92,
      frustrationLevel: recentScores.length > 0 ? Math.round(recentScores.reduce((sum, s) => sum + (s.errorsCount || 0), 0) / recentScores.length * 5) : 15
    }
  };
}
function calculateGameProgress(recentScores) {
  const gameProgress = {};
  recentScores.forEach((score) => {
    if (!gameProgress[score.gameId]) {
      gameProgress[score.gameId] = {
        name: score.gameName || score.gameId,
        sessions: 0,
        totalScore: 0,
        bestScore: 0,
        avgAccuracy: 0,
        totalTime: 0
      };
    }
    gameProgress[score.gameId].sessions++;
    gameProgress[score.gameId].totalScore += score.score || 0;
    gameProgress[score.gameId].bestScore = Math.max(gameProgress[score.gameId].bestScore, score.score || 0);
    gameProgress[score.gameId].totalTime += score.timeSpent || 0;
  });
  Object.keys(gameProgress).forEach((gameId) => {
    const game = gameProgress[gameId];
    game.avgScore = game.sessions > 0 ? Math.round(game.totalScore / game.sessions) : 0;
    game.avgTime = game.sessions > 0 ? Math.round(game.totalTime / game.sessions) : 0;
  });
  return gameProgress;
}
function generateWeeklyData(scores) {
  const weeklyData = Array(7).fill(0).map((_, i) => {
    const date = /* @__PURE__ */ new Date();
    date.setDate(date.getDate() - i);
    return {
      date: date.toISOString().split("T")[0],
      sessions: 0,
      avgAccuracy: 0,
      totalTime: 0
    };
  }).reverse();
  scores.forEach((score) => {
    const scoreDate = new Date(score.timestamp).toISOString().split("T")[0];
    const dayData = weeklyData.find((day) => day.date === scoreDate);
    if (dayData) {
      dayData.sessions++;
      dayData.avgAccuracy += score.accuracy || 0;
      dayData.totalTime += score.timeSpent || 0;
    }
  });
  weeklyData.forEach((day) => {
    if (day.sessions > 0) {
      day.avgAccuracy = Math.round(day.avgAccuracy / day.sessions);
      day.avgTime = Math.round(day.totalTime / day.sessions);
    }
  });
  return weeklyData;
}
function generateMonthlyData(scores) {
  const monthlyData = Array(4).fill(0).map((_, i) => {
    return {
      week: `Semana ${i + 1}`,
      sessions: 0,
      avgAccuracy: 0,
      totalTime: 0
    };
  });
  scores.forEach((score) => {
    const scoreDate = new Date(score.timestamp);
    const weekOfMonth = Math.floor((scoreDate.getDate() - 1) / 7);
    const week = Math.min(weekOfMonth, 3);
    monthlyData[week].sessions++;
    monthlyData[week].avgAccuracy += score.accuracy || 0;
    monthlyData[week].totalTime += score.timeSpent || 0;
  });
  monthlyData.forEach((week) => {
    if (week.sessions > 0) {
      week.avgAccuracy = Math.round(week.avgAccuracy / week.sessions);
      week.avgTime = Math.round(week.totalTime / week.sessions);
    }
  });
  return monthlyData;
}
function generateCognitiveProfile(scores, systemMetrics = {}) {
  const profile = {
    attention: systemMetrics.cognitive?.attentionScore || 0,
    memory: systemMetrics.cognitive?.memoryScore || 0,
    processing: systemMetrics.cognitive?.processingSpeedScore || 0,
    executive: systemMetrics.cognitive?.executiveScore || 0
  };
  if (Object.values(profile).every((v) => v === 0)) {
    scores.forEach((score) => {
      switch (score.gameId) {
        case "memory-game":
          profile.memory += score.accuracy || 0;
          break;
        case "letter-recognition":
          profile.attention += score.accuracy || 0;
          break;
        case "musical-sequence":
          profile.processing += score.accuracy || 0;
          break;
        case "quebra-cabeca":
          profile.executive += score.accuracy || 0;
          break;
        default:
          profile.attention += (score.accuracy || 0) * 0.25;
          profile.memory += (score.accuracy || 0) * 0.25;
          profile.processing += (score.accuracy || 0) * 0.25;
          profile.executive += (score.accuracy || 0) * 0.25;
      }
    });
    const totalScores = scores.length;
    if (totalScores > 0) {
      Object.keys(profile).forEach((key) => {
        profile[key] = Math.round(profile[key] / totalScores);
      });
    }
  }
  return profile;
}
function generateSensoryMetrics(scores, systemMetrics = {}) {
  if (systemMetrics.sensory) {
    return systemMetrics.sensory;
  }
  const totalScores = scores.length;
  if (totalScores === 0) {
    return {
      visual: 85,
      auditory: 85,
      tactile: 85,
      vestibular: 85,
      proprioceptive: 85
    };
  }
  const avgAccuracy = scores.reduce((acc, s) => acc + (s.accuracy || 0), 0) / totalScores;
  scores.reduce((acc, s) => acc + (s.responseTime || 0), 0) / totalScores;
  return {
    visual: Math.min(100, Math.max(50, Math.round(avgAccuracy * 1.1))),
    auditory: Math.min(100, Math.max(50, Math.round(avgAccuracy * 1.05))),
    tactile: Math.min(100, Math.max(50, Math.round(avgAccuracy * 0.95))),
    vestibular: Math.min(100, Math.max(50, Math.round(avgAccuracy * 0.9))),
    proprioceptive: Math.min(100, Math.max(50, Math.round(avgAccuracy * 0.85)))
  };
}
function generateNeuroPedagogicalData(scores, systemMetrics = {}) {
  const totalScores = scores.length;
  if (systemMetrics.cognitive) {
    return {
      executiveFunction: systemMetrics.cognitive.executiveScore || 85,
      sustainedAttention: systemMetrics.cognitive.attentionScore || 78,
      workingMemory: systemMetrics.cognitive.memoryScore || 82,
      processingSpeed: systemMetrics.cognitive.processingSpeedScore || 87,
      cognitiveFlexibility: systemMetrics.behavioral?.engagementLevel || 80
    };
  }
  return {
    executiveFunction: totalScores > 0 ? Math.round(scores.reduce((acc, s) => acc + (s.accuracy || 0), 0) / totalScores) : 85,
    sustainedAttention: totalScores > 0 ? Math.round(scores.reduce((acc, s) => acc + (s.timeSpent || 0), 0) / totalScores) : 78,
    workingMemory: Math.round(Math.random() * 20 + 80),
    processingSpeed: Math.round(Math.random() * 15 + 85),
    cognitiveFlexibility: Math.round(Math.random() * 25 + 75)
  };
}
function getCurrentActiveUsers() {
  const activeSessions = JSON.parse(localStorage.getItem("betina_active_sessions") || "[]");
  const currentTime = /* @__PURE__ */ new Date();
  const fiveMinutesAgo = new Date(currentTime.getTime() - 5 * 60 * 1e3);
  const recentSessions = activeSessions.filter(
    (session) => new Date(session.lastActivity) >= fiveMinutesAgo
  );
  if (recentSessions.length === 0) {
    const hour = currentTime.getHours();
    let estimatedUsers = 1;
    if (hour >= 8 && hour <= 18) {
      estimatedUsers = Math.min(50, Math.max(5, Math.floor(hour * 2.5)));
    } else if (hour >= 19 && hour <= 22) {
      estimatedUsers = Math.min(30, Math.max(3, Math.floor((24 - hour) * 1.5)));
    } else {
      estimatedUsers = Math.min(10, Math.max(1, Math.floor(hour * 0.5)));
    }
    return estimatedUsers;
  }
  return recentSessions.length;
}
function getSystemHealth() {
  return {
    uptime: Math.round(Math.random() * 5 + 95),
    // 95-100%
    responseTime: Math.round(Math.random() * 100 + 50),
    // 50-150ms
    memoryUsage: Math.round(Math.random() * 30 + 40),
    // 40-70%
    cpuUsage: Math.round(Math.random() * 25 + 15),
    // 15-40%
    status: "healthy"
  };
}
function getDefaultMetrics() {
  return {
    totalSessions: 0,
    totalScores: 0,
    avgAccuracy: 0,
    avgTimeSpent: 0,
    completionRate: 0,
    gameProgress: {},
    weeklyData: [],
    monthlyData: [],
    cognitiveProfiling: {
      attention: 0,
      memory: 0,
      processing: 0,
      executive: 0
    },
    sensoryMetrics: {
      visual: 0,
      auditory: 0,
      tactile: 0,
      vestibular: 0,
      proprioceptive: 0
    },
    neuroPedagogicalData: {
      executiveFunction: 0,
      sustainedAttention: 0,
      workingMemory: 0,
      processingSpeed: 0,
      cognitiveFlexibility: 0
    },
    lastUpdate: (/* @__PURE__ */ new Date()).toISOString(),
    activeUsers: 0,
    systemHealth: {
      uptime: 0,
      responseTime: 0,
      memoryUsage: 0,
      cpuUsage: 0,
      status: "no-data"
    }
  };
}
const useRealMetrics = (userId = "demo_user") => {
  const [metrics, setMetrics] = reactExports.useState(getDefaultMetrics());
  const [loading, setLoading] = reactExports.useState(true);
  const [error, setError] = reactExports.useState(null);
  reactExports.useEffect(() => {
    let isMounted = true;
    const loadMetrics = async () => {
      setLoading(true);
      setError(null);
      try {
        const realMetrics = await getRealMetrics(userId);
        if (isMounted) {
          setMetrics(realMetrics);
        }
      } catch (err) {
        if (isMounted) {
          console.error("Erro ao carregar métricas:", err);
          setError(err.message);
          setMetrics(getDefaultMetrics());
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };
    loadMetrics();
    const interval = setInterval(loadMetrics, 3e4);
    return () => {
      isMounted = false;
      clearInterval(interval);
    };
  }, [userId]);
  return {
    metrics,
    loading,
    error,
    refresh: () => getRealMetrics(userId).then(setMetrics),
    refreshAI: (childId, gameData) => getAIMetrics(childId, gameData)
  };
};
const isValidBackupFormat = (data) => {
  return data && data.version && data.exportDate && data.data && typeof data.data === "object" && data.metadata && typeof data.metadata === "object";
};
const extractGameDataFromBackup = (backupData) => {
  if (!isValidBackupFormat(backupData)) {
    console.error("Formato de backup inválido", backupData);
    return null;
  }
  const { data, metadata } = backupData;
  const { userProfiles, gameProgress, gameMetrics, accessibilitySettings } = data;
  if (!gameProgress || Object.keys(gameProgress).length === 0) {
    console.warn("Backup sem dados de progresso de jogos");
  }
  if (!gameMetrics || Object.keys(gameMetrics).length === 0) {
    console.warn("Backup sem métricas de jogos");
  }
  return {
    // Dados para performance dashboard
    performance: {
      gameMetrics: gameMetrics || {},
      gameProgress: gameProgress || {},
      userProfiles: userProfiles || [],
      sessionCount: calculateSessionCount(gameProgress),
      avgAccuracy: calculateAverageAccuracy(gameProgress),
      timeSpent: calculateTotalTimeSpent(gameProgress),
      completionRate: calculateCompletionRate(gameProgress)
    },
    // Dados para dashboard neuropedagógico
    neuroPedagogical: {
      cognitiveProfile: extractCognitiveProfile(gameProgress),
      recommendedActivities: generateRecommendations(gameProgress),
      progressIndicators: calculateProgressIndicators(gameProgress),
      skillsDistribution: calculateSkillsDistribution(gameProgress)
    },
    // Dados para relatório A
    aiReport: {
      gameProgress: gameProgress || {},
      userProfiles: userProfiles || [],
      gameMetrics: gameMetrics || {},
      learningPatterns: analyzeLearningPatterns(gameProgress),
      preferredTimeframes: analyzeTimeframes(gameProgress),
      skillsGrowth: analyzeSkillsGrowth(gameProgress),
      emergingPatterns: detectEmergingPatterns()
    },
    // Dados para dashboard multissensorial
    multisensory: {
      visualData: extractVisualData(),
      auditoryData: extractAuditoryData(),
      tactileData: extractTactileData(),
      sensoryIntegration: calculateSensoryIntegration(),
      crossModalTransfer: analyzeModalTransfer()
    },
    // Metadados e informações de erro
    metadata: {
      lastUpdate: (/* @__PURE__ */ new Date()).toISOString(),
      source: "backup_adapter",
      originalMetadata: metadata,
      serverError: metadata.serverError || null,
      version: backupData.version
    }
  };
};
function calculateSessionCount(gameProgress) {
  if (!gameProgress) return 0;
  let sessionCount = 0;
  Object.values(gameProgress).forEach((sessions) => {
    if (Array.isArray(sessions)) {
      sessionCount += sessions.length;
    }
  });
  return sessionCount;
}
function calculateAverageAccuracy(gameProgress) {
  if (!gameProgress) return 0;
  let totalAccuracy = 0;
  let sessionCount = 0;
  Object.values(gameProgress).forEach((sessions) => {
    if (Array.isArray(sessions)) {
      sessions.forEach((session) => {
        const accuracy = session.accuracy || (session.correctCount && session.moveCount ? session.correctCount / session.moveCount * 100 : null) || session.score || 0;
        totalAccuracy += accuracy;
        sessionCount++;
      });
    }
  });
  return sessionCount > 0 ? Math.round(totalAccuracy / sessionCount) : 0;
}
function calculateTotalTimeSpent(gameProgress) {
  if (!gameProgress) return 0;
  let totalTime = 0;
  Object.values(gameProgress).forEach((sessions) => {
    if (Array.isArray(sessions)) {
      sessions.forEach((session) => {
        totalTime += session.timeSpent || session.duration || 0;
      });
    }
  });
  return totalTime;
}
function calculateCompletionRate(gameProgress) {
  if (!gameProgress) return 0;
  let totalCompleted = 0;
  let sessionCount = 0;
  Object.values(gameProgress).forEach((sessions) => {
    if (Array.isArray(sessions)) {
      sessions.forEach((session) => {
        if (session.completed || session.completed === true) {
          totalCompleted++;
        }
        sessionCount++;
      });
    }
  });
  return sessionCount > 0 ? Math.round(totalCompleted / sessionCount * 100) : 0;
}
function extractCognitiveProfile(gameProgress) {
  const gameSkillMap = {
    "number-counting": ["matemática", "raciocínio", "atenção"],
    "visual-patterns": ["percepção visual", "atenção", "memória visual"],
    "memory-game": ["memória de trabalho", "concentração"],
    "color-match": ["atenção visual", "tomada de decisão", "velocidade de processamento"]
  };
  const profile = {
    "atenção": 0,
    "memória": 0,
    "raciocínio": 0,
    "percepção": 0,
    "velocidade": 0,
    "concentração": 0
  };
  if (!gameProgress) return profile;
  let skillCounts = {};
  Object.entries(gameProgress).forEach(([gameId, sessions]) => {
    if (Array.isArray(sessions) && sessions.length > 0) {
      let gameType = gameId.replace("betina_", "").replace("_history", "");
      const skills = gameSkillMap[gameType] || ["atenção"];
      const avgScore = sessions.reduce((sum, s) => sum + (s.score || s.accuracy || 0), 0) / sessions.length;
      skills.forEach((skill) => {
        const mainSkill = mapSkillToMainCategory(skill);
        if (profile[mainSkill] !== void 0) {
          if (!skillCounts[mainSkill]) skillCounts[mainSkill] = 0;
          profile[mainSkill] += avgScore;
          skillCounts[mainSkill]++;
        }
      });
    }
  });
  Object.keys(profile).forEach((skill) => {
    if (skillCounts[skill] && skillCounts[skill] > 0) {
      profile[skill] = Math.round(profile[skill] / skillCounts[skill]);
    }
  });
  return profile;
}
function mapSkillToMainCategory(skill) {
  const skillMap = {
    "atenção visual": "atenção",
    "atenção auditiva": "atenção",
    "memória visual": "memória",
    "memória auditiva": "memória",
    "memória de trabalho": "memória",
    "raciocínio lógico": "raciocínio",
    "raciocínio espacial": "raciocínio",
    "percepção visual": "percepção",
    "percepção auditiva": "percepção",
    "velocidade de processamento": "velocidade",
    "tomada de decisão": "velocidade",
    "concentração": "concentração",
    "foco": "concentração",
    "matemática": "raciocínio"
  };
  return skillMap[skill.toLowerCase()] || skill.toLowerCase();
}
function generateRecommendations(gameProgress) {
  if (!gameProgress) return [];
  const recommendations = [];
  const profile = extractCognitiveProfile(gameProgress);
  const weakPoints = Object.entries(profile).filter(([_, score]) => score < 70 && score > 0).map(([skill, _]) => skill);
  weakPoints.forEach((skill) => {
    switch (skill) {
      case "atenção":
        recommendations.push("Recomendado: Atividades focadas em atenção visual e sustentada");
        break;
      case "memória":
        recommendations.push("Recomendado: Exercícios de memória de trabalho");
        break;
      case "raciocínio":
        recommendations.push("Recomendado: Atividades de raciocínio lógico e sequencial");
        break;
      case "percepção":
        recommendations.push("Recomendado: Exercícios de discriminação visual e espacial");
        break;
      case "velocidade":
        recommendations.push("Recomendado: Atividades para melhorar tempo de resposta");
        break;
      case "concentração":
        recommendations.push("Recomendado: Exercícios para desenvolvimento do foco atencional");
        break;
    }
  });
  if (recommendations.length === 0) {
    recommendations.push("Continue com a rotina atual de atividades");
  }
  return recommendations;
}
function calculateProgressIndicators(gameProgress) {
  if (!gameProgress) return {};
  const indicators = {};
  Object.entries(gameProgress).forEach(([gameId, sessions]) => {
    if (Array.isArray(sessions) && sessions.length >= 2) {
      const sortedSessions = [...sessions].sort(
        (a, b) => new Date(a.timestamp) - new Date(b.timestamp)
      );
      const firstHalf = sortedSessions.slice(0, Math.floor(sortedSessions.length / 2));
      const secondHalf = sortedSessions.slice(Math.floor(sortedSessions.length / 2));
      const firstAvg = firstHalf.reduce((sum, s) => sum + (s.score || s.accuracy || 0), 0) / firstHalf.length;
      const secondAvg = secondHalf.reduce((sum, s) => sum + (s.score || s.accuracy || 0), 0) / secondHalf.length;
      const improvement = secondAvg > 0 ? Math.round((secondAvg - firstAvg) / firstAvg * 100) : 0;
      const gameName = gameId.replace("betina_", "").replace("_history", "").replace(/-/g, " ").replace(/(^|\s)\S/g, (l) => l.toUpperCase());
      indicators[gameName] = {
        sessions: sessions.length,
        improvement,
        trend: improvement > 10 ? "crescimento" : improvement < -10 ? "declínio" : "estável",
        lastScore: sortedSessions[sortedSessions.length - 1].score || sortedSessions[sortedSessions.length - 1].accuracy || 0
      };
    }
  });
  return indicators;
}
function calculateSkillsDistribution(gameProgress) {
  if (!gameProgress) return {};
  const profile = extractCognitiveProfile(gameProgress);
  Object.keys(profile).forEach((skill) => {
    profile[skill] = Math.max(1, profile[skill]);
  });
  return profile;
}
function analyzeLearningPatterns(gameProgress) {
  if (!gameProgress) return {};
  const patterns = {
    consistencyScore: 0,
    preferredGameType: "",
    learningCurve: "estável",
    bestPerformanceMetric: "",
    challengeAreas: []
  };
  const gameStats = {};
  let totalSessions = 0;
  Object.entries(gameProgress).forEach(([gameId, sessions]) => {
    if (Array.isArray(sessions) && sessions.length > 0) {
      const gameName = gameId.replace("betina_", "").replace("_history", "").replace(/-/g, " ").replace(/(^|\s)\S/g, (l) => l.toUpperCase());
      gameStats[gameName] = {
        count: sessions.length,
        avgScore: sessions.reduce((sum, s) => sum + (s.score || s.accuracy || 0), 0) / sessions.length
      };
      totalSessions += sessions.length;
    }
  });
  if (Object.keys(gameStats).length > 0) {
    const sortedGames = Object.entries(gameStats).sort(([, a], [, b]) => b.count - a.count);
    patterns.preferredGameType = sortedGames[0][0];
    const sessionsPerGame = Object.values(gameStats).map((g) => g.count);
    const avgSessionsPerGame = totalSessions / Object.keys(gameStats).length;
    const variance = sessionsPerGame.reduce((v, s) => v + Math.pow(s - avgSessionsPerGame, 2), 0) / sessionsPerGame.length;
    patterns.consistencyScore = Math.min(100, Math.max(0, 100 - variance / avgSessionsPerGame * 10));
    const lowScoreGames = Object.entries(gameStats).filter(([, stats]) => stats.avgScore < 70).map(([game]) => game);
    if (lowScoreGames.length > 0) {
      patterns.challengeAreas = lowScoreGames;
    }
    const highestScore = Object.entries(gameStats).sort(([, a], [, b]) => b.avgScore - a.avgScore)[0];
    if (highestScore) {
      patterns.bestPerformanceMetric = highestScore[0];
    }
  }
  return patterns;
}
function analyzeTimeframes(gameProgress) {
  if (!gameProgress) return {};
  const hourCounts = {
    morning: 0,
    // 6h-12h
    afternoon: 0,
    // 12h-18h
    evening: 0,
    // 18h-24h
    night: 0
    // 0h-6h
  };
  let totalSessions = 0;
  Object.values(gameProgress).forEach((sessions) => {
    if (Array.isArray(sessions)) {
      sessions.forEach((session) => {
        if (session.timestamp) {
          const hour = new Date(session.timestamp).getHours();
          if (hour >= 6 && hour < 12) hourCounts.morning++;
          else if (hour >= 12 && hour < 18) hourCounts.afternoon++;
          else if (hour >= 18) hourCounts.evening++;
          else hourCounts.night++;
          totalSessions++;
        }
      });
    }
  });
  const timePreferences = {};
  if (totalSessions > 0) {
    Object.entries(hourCounts).forEach(([timeframe, count]) => {
      timePreferences[timeframe] = Math.round(count / totalSessions * 100);
    });
    const preferredTimeframe = Object.entries(hourCounts).sort(([, a], [, b]) => b - a)[0]?.[0] || "afternoon";
    timePreferences.preferred = preferredTimeframe;
  } else {
    timePreferences.morning = 25;
    timePreferences.afternoon = 25;
    timePreferences.evening = 25;
    timePreferences.night = 25;
    timePreferences.preferred = "afternoon";
  }
  return timePreferences;
}
function analyzeSkillsGrowth(gameProgress) {
  if (!gameProgress) return {};
  const sessionsByDate = [];
  Object.entries(gameProgress).forEach(([gameId, sessions]) => {
    if (Array.isArray(sessions)) {
      sessions.forEach((session) => {
        if (session.timestamp) {
          sessionsByDate.push({
            game: gameId.replace("betina_", "").replace("_history", ""),
            timestamp: new Date(session.timestamp),
            score: session.score || session.accuracy || 0
          });
        }
      });
    }
  });
  sessionsByDate.sort((a, b) => a.timestamp - b.timestamp);
  const monthlyData = {};
  sessionsByDate.forEach((session) => {
    const month = session.timestamp.toISOString().slice(0, 7);
    if (!monthlyData[month]) {
      monthlyData[month] = {
        scores: [],
        games: /* @__PURE__ */ new Set()
      };
    }
    monthlyData[month].scores.push(session.score);
    monthlyData[month].games.add(session.game);
  });
  const growthData = {
    labels: [],
    avgScores: [],
    diversity: [],
    trend: "estável"
  };
  Object.entries(monthlyData).forEach(([month, data]) => {
    growthData.labels.push(month);
    const avgScore = data.scores.reduce((sum, score) => sum + score, 0) / data.scores.length;
    growthData.avgScores.push(Math.round(avgScore));
    growthData.diversity.push(data.games.size);
  });
  if (growthData.avgScores.length >= 2) {
    const firstHalf = growthData.avgScores.slice(0, Math.floor(growthData.avgScores.length / 2));
    const secondHalf = growthData.avgScores.slice(Math.floor(growthData.avgScores.length / 2));
    const firstAvg = firstHalf.reduce((sum, score) => sum + score, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, score) => sum + score, 0) / secondHalf.length;
    const growthRate = (secondAvg - firstAvg) / firstAvg * 100;
    if (growthRate > 10) growthData.trend = "crescimento";
    else if (growthRate < -10) growthData.trend = "declínio";
    else growthData.trend = "estável";
  }
  return growthData;
}
function detectEmergingPatterns(gameProgress) {
  return {
    patterns: [],
    suggestions: [
      "Explorar novas atividades para estimular diferentes habilidades",
      "Continuar com a rotina atual para consolidar aprendizados"
    ],
    confidenceScore: 75
  };
}
function extractVisualData(gameProgress) {
  return {
    visualScore: 75,
    colorDiscrimination: 80,
    spatialPerception: 70,
    visualTracking: 75
  };
}
function extractAuditoryData(gameProgress) {
  return {
    auditoryScore: 70,
    soundDiscrimination: 75,
    rhythmicPerception: 65,
    sequentialProcessing: 70
  };
}
function extractTactileData(gameProgress) {
  return {
    tactileScore: 65,
    pressureSensitivity: 70,
    textureDiscrimination: 65,
    fineMotor: 60
  };
}
function calculateSensoryIntegration(gameProgress) {
  return {
    overallScore: 70,
    visualAuditory: 75,
    visualTactile: 65,
    auditoryTactile: 70
  };
}
function analyzeModalTransfer(gameProgress) {
  return {
    transferScore: 65,
    primaryModality: "visual",
    transferEfficiency: "moderada",
    recommendations: [
      "Exercícios de integração visual-auditiva",
      "Atividades multissensoriais variadas"
    ]
  };
}
const backupDataAdapter = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  extractGameDataFromBackup,
  isValidBackupFormat
}, Symbol.toStringTag, { value: "Module" }));
export {
  BaseCollector as B,
  backupDataAdapter as b,
  useRealMetrics as u
};
//# sourceMappingURL=utils-CLTxz6zX.js.map
