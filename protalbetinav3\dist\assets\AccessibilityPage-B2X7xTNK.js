import { r as reactExports, R as React } from "./vendor-react-ByWh_-BW.js";
import { b as useAccessibilityContext } from "./context-Ch-5FaFa.js";
import "./vendor-misc-DneMUARX.js";
import "./services-M1ydzWhv.js";
import "./dashboard-DanqcTsU.js";
import "./vendor-charts-Cii0KTpx.js";
import "./admin-D2mpdgvV.js";
import "./utils-CLTxz6zX.js";
import "./vendor-utils-CjlX8hrF.js";
import "./game-colors-B_gd3llZ.js";
import "./game-association-B9GAxBuN.js";
import "./hooks-NJkOkh4y.js";
import "./game-letters-v8KNWHXS.js";
import "./game-memory-6_ujaMB2.js";
import "./vendor-motion-CJek6P2z.js";
import "./game-musical-Ci_rqtJn.js";
import "./game-patterns-GQY4qytf.js";
import "./game-puzzle-BLc_eXaF.js";
import "./game-numbers-tpTS4tK7.js";
import "./game-creative-iDOKdRXI.js";
const container = "_container_xqmmk_15";
const pageHeader = "_pageHeader_xqmmk_33";
const backButton = "_backButton_xqmmk_53";
const pageTitle = "_pageTitle_xqmmk_89";
const pageSubtitle = "_pageSubtitle_xqmmk_103";
const pageContent = "_pageContent_xqmmk_117";
const accessibilityPanel = "_accessibilityPanel_xqmmk_127";
const panelContent = "_panelContent_xqmmk_139";
const panelInfo = "_panelInfo_xqmmk_147";
const accessibilityGroup = "_accessibilityGroup_xqmmk_171";
const groupTitle = "_groupTitle_xqmmk_191";
const presetsGrid = "_presetsGrid_xqmmk_207";
const presetButton = "_presetButton_xqmmk_219";
const optionRow = "_optionRow_xqmmk_257";
const optionLabel = "_optionLabel_xqmmk_267";
const optionText = "_optionText_xqmmk_281";
const switchContainer = "_switchContainer_xqmmk_295";
const switchInput = "_switchInput_xqmmk_307";
const switchSlider = "_switchSlider_xqmmk_319";
const selectInput = "_selectInput_xqmmk_385";
const successMessage = "_successMessage_xqmmk_419";
const styles = {
  container,
  pageHeader,
  backButton,
  pageTitle,
  pageSubtitle,
  pageContent,
  accessibilityPanel,
  panelContent,
  panelInfo,
  accessibilityGroup,
  groupTitle,
  presetsGrid,
  presetButton,
  optionRow,
  optionLabel,
  optionText,
  switchContainer,
  switchInput,
  switchSlider,
  selectInput,
  successMessage
};
var _jsxFileName = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\components\\pages\\AccessibilityPage\\AccessibilityPage.jsx";
function AccessibilityPage({
  onBack
}) {
  const {
    settings,
    updateSettings,
    applyPreset
  } = useAccessibilityContext();
  const [savedSuccessfully, setSavedSuccessfully] = reactExports.useState(false);
  reactExports.useEffect(() => {
    const applyAccessibilitySettings = () => {
      const root = document.documentElement;
      if (settings.highContrast) {
        root.classList.add("high-contrast");
      } else {
        root.classList.remove("high-contrast");
      }
      root.classList.remove("font-small", "font-medium", "font-large", "font-extra-large");
      root.classList.add(`font-${settings.fontSize}`);
      if (settings.dyslexiaFriendly) {
        root.classList.add("dyslexia-friendly");
      } else {
        root.classList.remove("dyslexia-friendly");
      }
      if (settings.reducedMotion) {
        root.classList.add("reduced-motion");
      } else {
        root.classList.remove("reduced-motion");
      }
      root.classList.remove("scheme-default", "scheme-dark", "scheme-soft", "scheme-high-contrast");
      root.classList.add(`scheme-${settings.colorScheme}`);
      const customStyles = `
        .high-contrast * {
          filter: contrast(1.5) !important;
          border: 1px solid #000 !important;
        }
        
        .font-small { font-size: 12px !important; }
        .font-medium { font-size: 16px !important; }
        .font-large { font-size: 20px !important; }
        .font-extra-large { font-size: 24px !important; }
        
        .dyslexia-friendly * {
          font-family: 'OpenDyslexic', 'Comic Sans MS', cursive !important;
        }
        
        .reduced-motion * {
          animation: none !important;
          transition: none !important;
        }
        
        .scheme-dark {
          background: #1a1a1a !important;
          color: #ffffff !important;
        }
        
        .scheme-soft {
          filter: brightness(0.9) saturate(0.8) !important;
        }
        
        .scheme-high-contrast {
          filter: contrast(2) brightness(1.2) !important;
        }
      `;
      const existingStyle = document.getElementById("accessibility-styles");
      if (existingStyle) {
        existingStyle.remove();
      }
      const styleElement = document.createElement("style");
      styleElement.id = "accessibility-styles";
      styleElement.textContent = customStyles;
      document.head.appendChild(styleElement);
    };
    applyAccessibilitySettings();
  }, [settings]);
  const [textToSpeechEnabled, setTextToSpeechEnabled] = reactExports.useState(settings.textToSpeech);
  const showSuccessMessage = () => {
    setSavedSuccessfully(true);
    setTimeout(() => setSavedSuccessfully(false), 2e3);
  };
  const speakText = (text) => {
    if (settings.textToSpeech && "speechSynthesis" in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.8;
      utterance.pitch = 1;
      speechSynthesis.speak(utterance);
    }
  };
  const handleSettingChange = reactExports.useCallback((key, value) => {
    console.log(`🔧 Alterando configuração: ${key} = ${value}`);
    const newSettings = {
      [key]: value
    };
    updateSettings(newSettings);
    showSuccessMessage();
    speakText(`${key} ${value ? "ativado" : "desativado"}`);
  }, [updateSettings, settings.textToSpeech]);
  const handleApplyPreset = reactExports.useCallback((preset) => {
    console.log(`🎨 Aplicando preset: ${preset}`);
    applyPreset(preset);
    showSuccessMessage();
    speakText(`Preset ${preset} aplicado`);
  }, [applyPreset, settings.textToSpeech]);
  return /* @__PURE__ */ React.createElement("div", { className: styles.container, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 139,
    columnNumber: 5
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.pageHeader, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 140,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("button", { className: styles.backButton, onClick: onBack, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 141,
    columnNumber: 9
  } }, "← Voltar"), /* @__PURE__ */ React.createElement("h1", { className: styles.pageTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 144,
    columnNumber: 9
  } }, "♿ Configurações de Acessibilidade"), /* @__PURE__ */ React.createElement("p", { className: styles.pageSubtitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 145,
    columnNumber: 9
  } }, "Personalize sua experiência para ter o melhor acesso ao Portal Betina")), /* @__PURE__ */ React.createElement("div", { className: styles.pageContent, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 150,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.accessibilityPanel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 151,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.panelContent, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 152,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("p", { className: styles.panelInfo, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 153,
    columnNumber: 13
  } }, "Configure suas preferências de acessibilidade para uma melhor experiência no Portal Betina."), /* @__PURE__ */ React.createElement("div", { className: styles.accessibilityGroup, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 158,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("h3", { className: styles.groupTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 159,
    columnNumber: 15
  } }, "⚡ Configurações Rápidas"), /* @__PURE__ */ React.createElement("div", { className: styles.presetsGrid, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 160,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("button", { className: styles.presetButton, onClick: () => handleApplyPreset("default"), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 161,
    columnNumber: 17
  } }, "Padrão"), /* @__PURE__ */ React.createElement("button", { className: styles.presetButton, onClick: () => handleApplyPreset("high-contrast"), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 167,
    columnNumber: 17
  } }, "Alto Contraste"), /* @__PURE__ */ React.createElement("button", { className: styles.presetButton, onClick: () => handleApplyPreset("autism-friendly"), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 173,
    columnNumber: 17
  } }, "Autismo"), /* @__PURE__ */ React.createElement("button", { className: styles.presetButton, onClick: () => handleApplyPreset("dyslexia"), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 179,
    columnNumber: 17
  } }, "Dislexia"))), /* @__PURE__ */ React.createElement("div", { className: styles.accessibilityGroup, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 189,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("h3", { className: styles.groupTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 190,
    columnNumber: 15
  } }, "👁️ Visual"), /* @__PURE__ */ React.createElement("div", { className: styles.optionRow, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 192,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("label", { className: styles.optionLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 193,
    columnNumber: 17
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.optionText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 194,
    columnNumber: 19
  } }, "Alto Contraste"), /* @__PURE__ */ React.createElement("div", { className: styles.switchContainer, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 195,
    columnNumber: 19
  } }, /* @__PURE__ */ React.createElement("input", { type: "checkbox", className: styles.switchInput, checked: settings.highContrast, onChange: (e) => handleSettingChange("highContrast", e.target.checked), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 196,
    columnNumber: 21
  } }), /* @__PURE__ */ React.createElement("span", { className: styles.switchSlider, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 202,
    columnNumber: 21
  } })))), /* @__PURE__ */ React.createElement("div", { className: styles.optionRow, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 207,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("label", { className: styles.optionLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 208,
    columnNumber: 17
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.optionText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 209,
    columnNumber: 19
  } }, "Tamanho da Fonte"), /* @__PURE__ */ React.createElement("select", { className: styles.selectInput, value: settings.fontSize, onChange: (e) => handleSettingChange("fontSize", e.target.value), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 210,
    columnNumber: 19
  } }, /* @__PURE__ */ React.createElement("option", { value: "small", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 215,
    columnNumber: 21
  } }, "Pequena"), /* @__PURE__ */ React.createElement("option", { value: "medium", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 216,
    columnNumber: 21
  } }, "Média"), /* @__PURE__ */ React.createElement("option", { value: "large", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 217,
    columnNumber: 21
  } }, "Grande"), /* @__PURE__ */ React.createElement("option", { value: "extra-large", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 218,
    columnNumber: 21
  } }, "Extra Grande")))), /* @__PURE__ */ React.createElement("div", { className: styles.optionRow, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 223,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("label", { className: styles.optionLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 224,
    columnNumber: 17
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.optionText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 225,
    columnNumber: 19
  } }, "Fonte para Dislexia"), /* @__PURE__ */ React.createElement("div", { className: styles.switchContainer, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 226,
    columnNumber: 19
  } }, /* @__PURE__ */ React.createElement("input", { type: "checkbox", className: styles.switchInput, checked: settings.dyslexiaFriendly, onChange: (e) => handleSettingChange("dyslexiaFriendly", e.target.checked), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 227,
    columnNumber: 21
  } }), /* @__PURE__ */ React.createElement("span", { className: styles.switchSlider, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 233,
    columnNumber: 21
  } }))))), /* @__PURE__ */ React.createElement("div", { className: styles.accessibilityGroup, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 240,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("h3", { className: styles.groupTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 241,
    columnNumber: 15
  } }, "🎭 Movimento"), /* @__PURE__ */ React.createElement("div", { className: styles.optionRow, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 243,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("label", { className: styles.optionLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 244,
    columnNumber: 17
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.optionText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 245,
    columnNumber: 19
  } }, "Reduzir Animações"), /* @__PURE__ */ React.createElement("div", { className: styles.switchContainer, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 246,
    columnNumber: 19
  } }, /* @__PURE__ */ React.createElement("input", { type: "checkbox", className: styles.switchInput, checked: settings.reducedMotion, onChange: (e) => handleSettingChange("reducedMotion", e.target.checked), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 247,
    columnNumber: 21
  } }), /* @__PURE__ */ React.createElement("span", { className: styles.switchSlider, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 253,
    columnNumber: 21
  } }))))), /* @__PURE__ */ React.createElement("div", { className: styles.accessibilityGroup, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 260,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("h3", { className: styles.groupTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 261,
    columnNumber: 15
  } }, "🔊 Áudio"), /* @__PURE__ */ React.createElement("div", { className: styles.optionRow, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 263,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("label", { className: styles.optionLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 264,
    columnNumber: 17
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.optionText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 265,
    columnNumber: 19
  } }, "Leitura de Texto"), /* @__PURE__ */ React.createElement("div", { className: styles.switchContainer, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 266,
    columnNumber: 19
  } }, /* @__PURE__ */ React.createElement("input", { type: "checkbox", className: styles.switchInput, checked: settings.textToSpeech, onChange: (e) => handleSettingChange("textToSpeech", e.target.checked), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 267,
    columnNumber: 21
  } }), /* @__PURE__ */ React.createElement("span", { className: styles.switchSlider, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 273,
    columnNumber: 21
  } })))), /* @__PURE__ */ React.createElement("div", { className: styles.optionRow, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 278,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("label", { className: styles.optionLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 279,
    columnNumber: 17
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.optionText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 280,
    columnNumber: 19
  } }, "Sons Ativados"), /* @__PURE__ */ React.createElement("div", { className: styles.switchContainer, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 281,
    columnNumber: 19
  } }, /* @__PURE__ */ React.createElement("input", { type: "checkbox", className: styles.switchInput, checked: settings.soundEnabled, onChange: (e) => handleSettingChange("soundEnabled", e.target.checked), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 282,
    columnNumber: 21
  } }), /* @__PURE__ */ React.createElement("span", { className: styles.switchSlider, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 288,
    columnNumber: 21
  } })))), /* @__PURE__ */ React.createElement("div", { className: styles.optionRow, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 293,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("label", { className: styles.optionLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 294,
    columnNumber: 17
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.optionText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 295,
    columnNumber: 19
  } }, "Leitura Automática"), /* @__PURE__ */ React.createElement("div", { className: styles.switchContainer, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 296,
    columnNumber: 19
  } }, /* @__PURE__ */ React.createElement("input", { type: "checkbox", className: styles.switchInput, checked: settings.autoRead, onChange: (e) => handleSettingChange("autoRead", e.target.checked), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 297,
    columnNumber: 21
  } }), /* @__PURE__ */ React.createElement("span", { className: styles.switchSlider, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 303,
    columnNumber: 21
  } }))))), /* @__PURE__ */ React.createElement("div", { className: styles.accessibilityGroup, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 310,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("h3", { className: styles.groupTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 311,
    columnNumber: 15
  } }, "🎨 Tema"), /* @__PURE__ */ React.createElement("div", { className: styles.optionRow, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 313,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("label", { className: styles.optionLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 314,
    columnNumber: 17
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.optionText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 315,
    columnNumber: 19
  } }, "Esquema de Cores"), /* @__PURE__ */ React.createElement("select", { className: styles.selectInput, value: settings.colorScheme, onChange: (e) => handleSettingChange("colorScheme", e.target.value), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 316,
    columnNumber: 19
  } }, /* @__PURE__ */ React.createElement("option", { value: "default", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 321,
    columnNumber: 21
  } }, "Padrão"), /* @__PURE__ */ React.createElement("option", { value: "dark", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 322,
    columnNumber: 21
  } }, "Escuro"), /* @__PURE__ */ React.createElement("option", { value: "soft", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 323,
    columnNumber: 21
  } }, "Suave"), /* @__PURE__ */ React.createElement("option", { value: "high-contrast", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 324,
    columnNumber: 21
  } }, "Alto Contraste"))))), savedSuccessfully && /* @__PURE__ */ React.createElement("div", { className: styles.successMessage, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 331,
    columnNumber: 15
  } }, "✅ Configurações salvas com sucesso!")))));
}
export {
  AccessibilityPage as default
};
//# sourceMappingURL=AccessibilityPage-B2X7xTNK.js.map
