import { r as reactExports, R as React } from "./vendor-react-ByWh_-BW.js";
import "./vendor-misc-DneMUARX.js";
import "./services-M1ydzWhv.js";
import "./dashboard-DanqcTsU.js";
import "./context-Ch-5FaFa.js";
import "./hooks-NJkOkh4y.js";
import "./vendor-utils-CjlX8hrF.js";
import "./vendor-charts-Cii0KTpx.js";
import "./admin-D2mpdgvV.js";
import "./utils-CLTxz6zX.js";
import "./game-colors-B_gd3llZ.js";
import "./game-association-B9GAxBuN.js";
import "./game-letters-v8KNWHXS.js";
import "./game-memory-6_ujaMB2.js";
import "./vendor-motion-CJek6P2z.js";
import "./game-musical-Ci_rqtJn.js";
import "./game-patterns-GQY4qytf.js";
import "./game-puzzle-BLc_eXaF.js";
import "./game-numbers-tpTS4tK7.js";
import "./game-creative-iDOKdRXI.js";
const container = "_container_2uq7c_15";
const header = "_header_2uq7c_37";
const mainTitle = "_mainTitle_2uq7c_49";
const subtitle = "_subtitle_2uq7c_65";
const section = "_section_2uq7c_81";
const sectionTitle = "_sectionTitle_2uq7c_93";
const sectionContent = "_sectionContent_2uq7c_123";
const activeProfileCard = "_activeProfileCard_2uq7c_157";
const activeProfileAvatar = "_activeProfileAvatar_2uq7c_181";
const activeProfileInfo = "_activeProfileInfo_2uq7c_205";
const activeProfileName = "_activeProfileName_2uq7c_213";
const activeProfileStats = "_activeProfileStats_2uq7c_227";
const statItem = "_statItem_2uq7c_239";
const statIcon = "_statIcon_2uq7c_255";
const profilesGrid = "_profilesGrid_2uq7c_265";
const profileCard = "_profileCard_2uq7c_279";
const active = "_active_2uq7c_157";
const profileAvatar = "_profileAvatar_2uq7c_325";
const profileInfo = "_profileInfo_2uq7c_353";
const profileName = "_profileName_2uq7c_363";
const profileAge = "_profileAge_2uq7c_377";
const profileStats = "_profileStats_2uq7c_377";
const profileDeleteBtn = "_profileDeleteBtn_2uq7c_389";
const addProfileCard = "_addProfileCard_2uq7c_443";
const addProfileIcon = "_addProfileIcon_2uq7c_475";
const benefitsList = "_benefitsList_2uq7c_489";
const benefitItem = "_benefitItem_2uq7c_501";
const benefitEmoji = "_benefitEmoji_2uq7c_525";
const benefitText = "_benefitText_2uq7c_535";
const createProfileOverlay = "_createProfileOverlay_2uq7c_551";
const createProfileForm = "_createProfileForm_2uq7c_581";
const formHeader = "_formHeader_2uq7c_607";
const formTitle = "_formTitle_2uq7c_617";
const formSubtitle = "_formSubtitle_2uq7c_631";
const formGroup = "_formGroup_2uq7c_643";
const formLabel = "_formLabel_2uq7c_651";
const input = "_input_2uq7c_665";
const avatarSelector = "_avatarSelector_2uq7c_709";
const avatarOption = "_avatarOption_2uq7c_723";
const selected = "_selected_2uq7c_761";
const formActions = "_formActions_2uq7c_771";
const btn = "_btn_2uq7c_785";
const btnPrimary = "_btnPrimary_2uq7c_807";
const btnSecondary = "_btnSecondary_2uq7c_827";
const styles = {
  container,
  header,
  mainTitle,
  subtitle,
  section,
  sectionTitle,
  sectionContent,
  activeProfileCard,
  activeProfileAvatar,
  activeProfileInfo,
  activeProfileName,
  activeProfileStats,
  statItem,
  statIcon,
  profilesGrid,
  profileCard,
  active,
  profileAvatar,
  profileInfo,
  profileName,
  profileAge,
  profileStats,
  profileDeleteBtn,
  addProfileCard,
  addProfileIcon,
  benefitsList,
  benefitItem,
  benefitEmoji,
  benefitText,
  createProfileOverlay,
  createProfileForm,
  formHeader,
  formTitle,
  formSubtitle,
  formGroup,
  formLabel,
  input,
  avatarSelector,
  avatarOption,
  selected,
  formActions,
  btn,
  btnPrimary,
  btnSecondary
};
var _jsxFileName = "C:\\Projetos\\portalbettinabkp\\protalbetinav3\\src\\components\\pages\\UserProfiles\\UserProfiles.jsx";
function UserProfiles() {
  const [profiles, setProfiles] = reactExports.useState([]);
  const [activeProfile, setActiveProfile] = reactExports.useState(null);
  const [showCreateForm, setShowCreateForm] = reactExports.useState(false);
  const [newProfile, setNewProfile] = reactExports.useState({
    name: "",
    age: "",
    avatar: "👶",
    preferences: {
      theme: "default",
      difficulty: "easy",
      soundEnabled: true,
      animationsEnabled: true
    }
  });
  const [profileStats2, setProfileStats] = reactExports.useState({});
  const [loadingStats, setLoadingStats] = reactExports.useState(false);
  const fetchProfileStats = async (profileId) => {
    if (!profileId) return;
    try {
      setLoadingStats(true);
      const response = await fetch(`/api/metrics/game-sessions?userId=${profileId}`);
      if (response.ok) {
        const data = await response.json();
        const stats = {
          gamesPlayed: data.sessions?.length || 0,
          totalTime: data.sessions?.reduce((total, session) => total + (session.duration || 0), 0) || 0,
          lastPlayed: data.sessions?.length > 0 ? Math.max(...data.sessions.map((s) => new Date(s.timestamp).getTime())) : null,
          favoriteGames: data.favoriteGames || [],
          achievements: data.achievements || [],
          avgPerformance: data.avgPerformance || 0
        };
        setProfileStats((prev) => ({
          ...prev,
          [profileId]: stats
        }));
        const dashboardUser = localStorage.getItem("userData");
        if (dashboardUser) {
          try {
            const user = JSON.parse(dashboardUser);
            const linkResponse = await fetch("/api/dashboard/link-profile", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${localStorage.getItem("authToken")}`
              },
              body: JSON.stringify({
                profileId,
                dashboardUserId: user.id,
                profileData: {
                  gamesPlayed: stats.gamesPlayed,
                  totalTime: stats.totalTime
                }
              })
            });
            if (linkResponse.ok) {
              console.log(`✅ Perfil ${profileId} vinculado ao usuário do dashboard ${user.email}`);
            }
          } catch (linkError) {
            console.warn("Falha ao vincular perfil ao dashboard:", linkError);
          }
        }
      }
    } catch (error) {
      console.error("Erro ao buscar estatísticas do perfil:", error);
    } finally {
      setLoadingStats(false);
    }
  };
  reactExports.useEffect(() => {
    const loadProfiles = () => {
      const savedProfiles = JSON.parse(localStorage.getItem("betina_profiles") || "[]");
      setProfiles(savedProfiles);
      const activeId = localStorage.getItem("betina_active_profile");
      if (activeId && savedProfiles.length > 0) {
        const active2 = savedProfiles.find((p) => p.id === activeId);
        setActiveProfile(active2);
      }
      savedProfiles.forEach((profile) => {
        fetchProfileStats(profile.id);
      });
    };
    loadProfiles();
  }, []);
  reactExports.useEffect(() => {
    if (activeProfile) {
      fetchProfileStats(activeProfile.id);
    }
  }, [activeProfile]);
  const saveProfiles = (newProfiles) => {
    setProfiles(newProfiles);
    localStorage.setItem("betina_profiles", JSON.stringify(newProfiles));
  };
  const handleCreateProfile = () => {
    if (!newProfile.name.trim()) {
      alert("Por favor, digite um nome para o perfil");
      return;
    }
    const profile = {
      id: Date.now().toString(),
      ...newProfile,
      createdAt: (/* @__PURE__ */ new Date()).toISOString(),
      lastUsed: (/* @__PURE__ */ new Date()).toISOString(),
      gamesPlayed: 0,
      totalTime: 0
    };
    const updatedProfiles = [...profiles, profile];
    saveProfiles(updatedProfiles);
    setNewProfile({
      name: "",
      age: "",
      avatar: "👶",
      preferences: {
        theme: "default",
        difficulty: "easy",
        soundEnabled: true,
        animationsEnabled: true
      }
    });
    setShowCreateForm(false);
  };
  const selectProfile = (profile) => {
    setActiveProfile(profile);
    localStorage.setItem("betina_active_profile", profile.id);
    const updatedProfiles = profiles.map((p) => p.id === profile.id ? {
      ...p,
      lastUsed: (/* @__PURE__ */ new Date()).toISOString()
    } : p);
    saveProfiles(updatedProfiles);
    fetchProfileStats(profile.id);
  };
  const deleteProfile = (profileId) => {
    if (confirm("Tem certeza que deseja deletar este perfil?")) {
      const updatedProfiles = profiles.filter((p) => p.id !== profileId);
      saveProfiles(updatedProfiles);
      if (activeProfile?.id === profileId) {
        setActiveProfile(null);
        localStorage.removeItem("betina_active_profile");
      }
    }
  };
  const avatarOptions = ["👶", "👧", "👦", "🧒", "👨", "👩", "🐱", "🐶", "🦋", "🌟"];
  return /* @__PURE__ */ React.createElement("div", { className: styles.container, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 190,
    columnNumber: 5
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.header, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 192,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("h1", { className: styles.mainTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 193,
    columnNumber: 9
  } }, "� Gerenciar Perfis"), /* @__PURE__ */ React.createElement("p", { className: styles.subtitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 194,
    columnNumber: 9
  } }, "Crie e gerencie perfis personalizados para toda a família")), activeProfile && /* @__PURE__ */ React.createElement("section", { className: styles.section, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 201,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("h2", { className: styles.sectionTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 202,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 203,
    columnNumber: 13
  } }, "⭐"), "Perfil Ativo"), /* @__PURE__ */ React.createElement("div", { className: styles.activeProfileCard, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 206,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.activeProfileAvatar, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 207,
    columnNumber: 13
  } }, activeProfile.avatar), /* @__PURE__ */ React.createElement("div", { className: styles.activeProfileInfo, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 208,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("h3", { className: styles.activeProfileName, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 209,
    columnNumber: 15
  } }, activeProfile.name), /* @__PURE__ */ React.createElement("div", { className: styles.activeProfileStats, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 210,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.statItem, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 211,
    columnNumber: 17
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.statIcon, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 212,
    columnNumber: 19
  } }, "🎂"), /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 213,
    columnNumber: 19
  } }, activeProfile.age ? `${activeProfile.age} anos` : "Idade não informada")), /* @__PURE__ */ React.createElement("div", { className: styles.statItem, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 215,
    columnNumber: 17
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.statIcon, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 216,
    columnNumber: 19
  } }, "🎮"), /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 217,
    columnNumber: 19
  } }, loadingStats ? "Carregando..." : `${profileStats2[activeProfile.id]?.gamesPlayed || 0} jogos jogados`)), /* @__PURE__ */ React.createElement("div", { className: styles.statItem, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 222,
    columnNumber: 17
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.statIcon, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 223,
    columnNumber: 19
  } }, "⏰"), /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 224,
    columnNumber: 19
  } }, profileStats2[activeProfile.id]?.totalTime ? `${Math.round(profileStats2[activeProfile.id].totalTime / 1e3 / 60)} min jogados` : `Último acesso: ${new Date(activeProfile.lastUsed).toLocaleDateString()}`)), profileStats2[activeProfile.id]?.favoriteGames?.length > 0 && /* @__PURE__ */ React.createElement("div", { className: styles.statItem, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 231,
    columnNumber: 19
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.statIcon, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 232,
    columnNumber: 21
  } }, "⭐"), /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 233,
    columnNumber: 21
  } }, "Jogo favorito: ", profileStats2[activeProfile.id].favoriteGames[0])), profileStats2[activeProfile.id]?.avgPerformance > 0 && /* @__PURE__ */ React.createElement("div", { className: styles.statItem, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 237,
    columnNumber: 19
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.statIcon, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 238,
    columnNumber: 21
  } }, "📊"), /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 239,
    columnNumber: 21
  } }, "Performance média: ", Math.round(profileStats2[activeProfile.id].avgPerformance), "%")))))), /* @__PURE__ */ React.createElement("section", { className: styles.section, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 249,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.profilesGrid, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 250,
    columnNumber: 9
  } }, profiles.map((profile) => /* @__PURE__ */ React.createElement("div", { key: profile.id, className: `${styles.profileCard} ${activeProfile?.id === profile.id ? styles.active : ""}`, onClick: () => selectProfile(profile), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 252,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.profileAvatar, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 257,
    columnNumber: 15
  } }, profile.avatar), /* @__PURE__ */ React.createElement("div", { className: styles.profileInfo, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 258,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("h4", { className: styles.profileName, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 259,
    columnNumber: 17
  } }, profile.name), /* @__PURE__ */ React.createElement("p", { className: styles.profileAge, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 260,
    columnNumber: 17
  } }, profile.age ? `${profile.age} anos` : "Idade não informada"), /* @__PURE__ */ React.createElement("p", { className: styles.profileStats, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 263,
    columnNumber: 17
  } }, profile.gamesPlayed || 0, " jogos jogados")), /* @__PURE__ */ React.createElement("button", { className: styles.profileDeleteBtn, onClick: (e) => {
    e.stopPropagation();
    deleteProfile(profile.id);
  }, title: "Deletar perfil", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 267,
    columnNumber: 15
  } }, "🗑️"))), /* @__PURE__ */ React.createElement("div", { className: `${styles.profileCard} ${styles.addProfileCard}`, onClick: () => setShowCreateForm(true), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 281,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.addProfileIcon, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 285,
    columnNumber: 13
  } }, "➕"), /* @__PURE__ */ React.createElement("p", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 286,
    columnNumber: 13
  } }, "Adicionar Perfil")))), /* @__PURE__ */ React.createElement("section", { className: styles.section, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 292,
    columnNumber: 7
  } }, /* @__PURE__ */ React.createElement("h2", { className: styles.sectionTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 293,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("span", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 294,
    columnNumber: 11
  } }, "💡"), "Como Funciona?"), /* @__PURE__ */ React.createElement("div", { className: styles.sectionContent, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 297,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("p", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 298,
    columnNumber: 11
  } }, "Os ", /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 299,
    columnNumber: 16
  } }, "perfis de usuário"), " permitem que cada criança da família tenha sua própria experiência personalizada. As métricas e progressos são automaticamente vinculados ao responsável que fizer login no dashboard."), /* @__PURE__ */ React.createElement("ul", { className: styles.benefitsList, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 304,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("li", { className: styles.benefitItem, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 305,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.benefitEmoji, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 306,
    columnNumber: 15
  } }, "📊"), /* @__PURE__ */ React.createElement("span", { className: styles.benefitText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 307,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 308,
    columnNumber: 17
  } }, "Progresso Individual:"), " Cada criança mantém seu próprio histórico de jogos e conquistas")), /* @__PURE__ */ React.createElement("li", { className: styles.benefitItem, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 311,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.benefitEmoji, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 312,
    columnNumber: 15
  } }, "👨‍👩‍👧‍👦"), /* @__PURE__ */ React.createElement("span", { className: styles.benefitText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 313,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 314,
    columnNumber: 17
  } }, "Fácil para Crianças:"), " Interface simples, sem necessidade de login ou senhas")), /* @__PURE__ */ React.createElement("li", { className: styles.benefitItem, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 317,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.benefitEmoji, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 318,
    columnNumber: 15
  } }, "�"), /* @__PURE__ */ React.createElement("span", { className: styles.benefitText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 319,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 320,
    columnNumber: 17
  } }, "Métricas no Dashboard:"), " Pais/responsáveis acessam relatórios detalhados via dashboard")), /* @__PURE__ */ React.createElement("li", { className: styles.benefitItem, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 323,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("span", { className: styles.benefitEmoji, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 324,
    columnNumber: 15
  } }, "�"), /* @__PURE__ */ React.createElement("span", { className: styles.benefitText, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 325,
    columnNumber: 15
  } }, /* @__PURE__ */ React.createElement("strong", { __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 326,
    columnNumber: 17
  } }, "Dados Seguros:"), " Todas as informações ficam salvas localmente no seu dispositivo"))), "        ")), showCreateForm && /* @__PURE__ */ React.createElement("div", { className: styles.createProfileOverlay, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 334,
    columnNumber: 9
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.createProfileForm, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 335,
    columnNumber: 11
  } }, /* @__PURE__ */ React.createElement("div", { className: styles.formHeader, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 336,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("h3", { className: styles.formTitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 337,
    columnNumber: 15
  } }, "➕ Criar Novo Perfil"), /* @__PURE__ */ React.createElement("p", { className: styles.formSubtitle, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 338,
    columnNumber: 15
  } }, "Adicione um novo membro da família")), /* @__PURE__ */ React.createElement("div", { className: styles.formGroup, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 341,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("label", { htmlFor: "profile-name", className: styles.formLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 342,
    columnNumber: 15
  } }, "Nome:"), /* @__PURE__ */ React.createElement("input", { type: "text", id: "profile-name", name: "profile-name", value: newProfile.name, onChange: (e) => setNewProfile({
    ...newProfile,
    name: e.target.value
  }), placeholder: "Digite o nome...", maxLength: 20, className: styles.input, autoComplete: "given-name", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 343,
    columnNumber: 15
  } })), /* @__PURE__ */ React.createElement("div", { className: styles.formGroup, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 356,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("label", { htmlFor: "profile-age", className: styles.formLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 357,
    columnNumber: 15
  } }, "Idade (opcional):"), /* @__PURE__ */ React.createElement("input", { type: "number", id: "profile-age", name: "profile-age", value: newProfile.age, onChange: (e) => setNewProfile({
    ...newProfile,
    age: e.target.value
  }), min: "1", max: "100", placeholder: "Ex: 5", className: styles.input, autoComplete: "age", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 358,
    columnNumber: 15
  } })), /* @__PURE__ */ React.createElement("div", { className: styles.formGroup, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 372,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("label", { htmlFor: "avatar-selector", className: styles.formLabel, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 373,
    columnNumber: 15
  } }, "Escolha um Avatar:"), /* @__PURE__ */ React.createElement("div", { className: styles.avatarSelector, id: "avatar-selector", role: "radiogroup", "aria-labelledby": "avatar-selector", __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 374,
    columnNumber: 15
  } }, avatarOptions.map((avatar, index) => /* @__PURE__ */ React.createElement("button", { key: avatar, type: "button", role: "radio", "aria-checked": newProfile.avatar === avatar, "aria-label": `Avatar ${index + 1}: ${avatar}`, className: `${styles.avatarOption} ${newProfile.avatar === avatar ? styles.selected : ""}`, onClick: () => setNewProfile({
    ...newProfile,
    avatar
  }), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 376,
    columnNumber: 19
  } }, avatar)))), /* @__PURE__ */ React.createElement("div", { className: styles.formActions, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 391,
    columnNumber: 13
  } }, /* @__PURE__ */ React.createElement("button", { className: `${styles.btn} ${styles.btnPrimary}`, onClick: handleCreateProfile, __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 392,
    columnNumber: 15
  } }, "✅ Criar Perfil"), /* @__PURE__ */ React.createElement("button", { className: `${styles.btn} ${styles.btnSecondary}`, onClick: () => setShowCreateForm(false), __self: this, __source: {
    fileName: _jsxFileName,
    lineNumber: 398,
    columnNumber: 15
  } }, "❌ Cancelar")))));
}
export {
  UserProfiles as default
};
//# sourceMappingURL=UserProfiles-Dd9TdgTt.js.map
