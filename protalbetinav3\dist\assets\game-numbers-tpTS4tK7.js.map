{"version": 3, "file": "game-numbers-tpTS4tK7.js", "sources": ["../../src/games/ContagemNumeros/collectors/NumericalCognitionCollector.js", "../../src/games/ContagemNumeros/collectors/AttentionFocusCollector.js", "../../src/games/ContagemNumeros/collectors/VisualProcessingCollector.js", "../../src/games/ContagemNumeros/collectors/MathematicalReasoningCollector.js", "../../src/games/ContagemNumeros/collectors/ErrorPatternCollector.js", "../../src/games/ContagemNumeros/collectors/EstimationSkillsCollector.js", "../../src/games/ContagemNumeros/collectors/SequenceAnalysisCollector.js", "../../src/games/ContagemNumeros/collectors/ComparisonSkillsCollector.js", "../../src/games/ContagemNumeros/collectors/SoundMatchingCollector.js", "../../src/games/ContagemNumeros/collectors/PatternRecognitionCollector.js", "../../src/games/ContagemNumeros/collectors/index.js", "../../src/api/services/processors/games/ContagemNumerosProcessors.js", "../../src/games/ContagemNumeros/ContagemNumerosConfig.js", "../../src/games/ContagemNumeros/ContagemNumerosGame.jsx"], "sourcesContent": ["/**\r\n * 🔢 NUMERICAL COGNITION COLLECTOR\r\n * Coletor especializado em análise de cognição numérica para NumberCounting\r\n * Portal Betina V3\r\n */\r\n\r\nexport class NumericalCognitionCollector {\r\n  constructor() {\r\n    this.cognitionThresholds = {\r\n      excellent: 0.95,\r\n      good: 0.85,\r\n      average: 0.70,\r\n      poor: 0.50,\r\n      critical: 0.30\r\n    };\r\n    \r\n    this.numericalSkills = {\r\n      counting: 'Habilidade de contagem sequencial',\r\n      subitizing: 'Reconhecimento imediato de quantidades pequenas',\r\n      numberSense: 'Senso numérico e magnitude',\r\n      cardinality: 'Compreensão do princípio da cardinalidade',\r\n      oneToOne: 'Correspondência um-para-um'\r\n    };\r\n    \r\n    this.difficultyRanges = {\r\n      easy: { min: 1, max: 5, expectedTime: 3000 },\r\n      medium: { min: 3, max: 10, expectedTime: 5000 },\r\n      hard: { min: 5, max: 15, expectedTime: 8000 }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Método padronizado de coleta de dados para integração com testes\r\n   * @param {Object} data - Dados do jogo a serem analisados\r\n   * @returns {Object} - Resultado da análise de cognição numérica\r\n   */\r\n  collect(data) {\r\n    return this.analyze(data);\r\n  }\r\n  \r\n  /**\r\n   * Método padronizado de análise para integração com testes e processadores\r\n   * @param {Object} data - Dados do jogo a serem analisados\r\n   * @returns {Object} - Resultado da análise de cognição numérica\r\n   */\r\n  async analyze(data) {\r\n    if (!data || !data.numberCounting) {\r\n      console.warn('NumericalCognitionCollector: Dados inválidos recebidos', data);\r\n      return {\r\n        countingAccuracy: 0.7,\r\n        numberSequencing: 0.7,\r\n        subitizing: 0.7,\r\n        numericalComprehension: 0.7,\r\n        quantityComparison: 0.7,\r\n        overallCognition: 0.7,\r\n        recommendations: []\r\n      };\r\n    }\r\n    \r\n    try {\r\n      const countingAccuracy = this.assessCountingAccuracy(data);\r\n      const numberSequencing = this.assessNumberSequencing(data);\r\n      const subitizing = this.assessSubitizing(data);\r\n      const numericalComprehension = this.assessNumericalComprehension(data);\r\n      const quantityComparison = this.assessQuantityComparison(data);\r\n      \r\n      // Calcular pontuação geral de cognição numérica\r\n      const overallCognition = (\r\n        countingAccuracy * 0.25 +\r\n        numberSequencing * 0.2 +\r\n        subitizing * 0.15 +\r\n        numericalComprehension * 0.25 +\r\n        quantityComparison * 0.15\r\n      );\r\n      \r\n      // Gerar recomendações baseadas nas pontuações\r\n      const recommendations = this.generateRecommendations({\r\n        countingAccuracy,\r\n        numberSequencing,\r\n        subitizing,\r\n        numericalComprehension,\r\n        quantityComparison,\r\n        overallCognition\r\n      });\r\n      \r\n      return {\r\n        countingAccuracy,\r\n        numberSequencing,\r\n        subitizing,\r\n        numericalComprehension,\r\n        quantityComparison,\r\n        overallCognition,\r\n        recommendations\r\n      };\r\n    } catch (error) {\r\n      console.error('Erro na análise de cognição numérica:', error);\r\n      return {\r\n        countingAccuracy: 0.7,\r\n        numberSequencing: 0.7,\r\n        subitizing: 0.7,\r\n        numericalComprehension: 0.7,\r\n        quantityComparison: 0.7,\r\n        overallCognition: 0.7,\r\n        recommendations: [],\r\n        error: error.message\r\n      };\r\n    }\r\n  }\r\n\r\n  assessCountingAccuracy(data) {\r\n    const attempts = data.attempts.filter(a => a.type === 'counting');\r\n    if (attempts.length === 0) return 0.7;\r\n\r\n    const correctAttempts = attempts.filter(a => a.isCorrect);\r\n    const accuracy = correctAttempts.length / attempts.length;\r\n\r\n    // Análise por faixa numérica\r\n    const accuracyByRange = this.analyzeAccuracyByNumberRange(attempts);\r\n    \r\n    return Math.min(1.0, accuracy * (1 + accuracyByRange.consistency * 0.2));\r\n  }\r\n\r\n  assessSubitizing(data) {\r\n    // Subitizing: reconhecimento imediato de quantidades 1-4\r\n    const subitizingAttempts = data.attempts.filter(a => \r\n      a.correctAnswer <= 4 && a.responseTime < 2000\r\n    );\r\n\r\n    if (subitizingAttempts.length === 0) return 0.6;\r\n\r\n    const accuracy = subitizingAttempts.filter(a => a.isCorrect).length / subitizingAttempts.length;\r\n    const avgResponseTime = subitizingAttempts.reduce((sum, a) => sum + a.responseTime, 0) / subitizingAttempts.length;\r\n    \r\n    // Subitizing eficiente deve ser rápido e preciso\r\n    const speedFactor = Math.max(0, 1 - (avgResponseTime - 1000) / 2000);\r\n    \r\n    return Math.min(1.0, accuracy * (0.7 + speedFactor * 0.3));\r\n  }\r\n\r\n  assessNumberSense(data) {\r\n    const attempts = data.attempts;\r\n    if (attempts.length === 0) return 0.7;\r\n\r\n    // Análise de consistência na estimativa\r\n    const estimationErrors = attempts.map(a => Math.abs(a.userAnswer - a.correctAnswer));\r\n    const avgError = estimationErrors.reduce((sum, e) => sum + e, 0) / estimationErrors.length;\r\n    \r\n    // Análise de padrões de erro sistemáticos\r\n    const systematicBias = this.detectSystematicBias(attempts);\r\n    \r\n    // Capacidade de discriminar magnitudes próximas\r\n    const magnitudeDiscrimination = this.assessMagnitudeDiscrimination(attempts);\r\n    \r\n    const baseScore = Math.max(0, 1 - avgError / 10);\r\n    const biaspenalty = systematicBias.hasBias ? 0.2 : 0;\r\n    \r\n    return Math.min(1.0, (baseScore + magnitudeDiscrimination) / 2 - biaspenalty);\r\n  }\r\n\r\n  assessCardinality(data) {\r\n    // Princípio da cardinalidade: último número contado representa o total\r\n    const countingSequences = data.countingSequences || [];\r\n    \r\n    if (countingSequences.length === 0) return 0.7;\r\n\r\n    const correctCardinalityUse = countingSequences.filter(seq => \r\n      seq.finalAnswer === seq.lastCountedNumber\r\n    ).length;\r\n\r\n    const cardinalityAccuracy = correctCardinalityUse / countingSequences.length;\r\n    \r\n    // Bonus para demonstrar compreensão consistente\r\n    const consistency = this.measureCardinalityConsistency(countingSequences);\r\n    \r\n    return Math.min(1.0, cardinalityAccuracy * (0.8 + consistency * 0.2));\r\n  }\r\n\r\n  assessOneToOneCorrespondence(data) {\r\n    // Análise de correspondência um-para-um durante contagem\r\n    const countingBehavior = data.countingBehavior || [];\r\n    \r\n    if (countingBehavior.length === 0) return 0.7;\r\n\r\n    const correctCorrespondence = countingBehavior.filter(behavior => \r\n      behavior.objectsPointed === behavior.numbersSpoken &&\r\n      behavior.noDoublePointing &&\r\n      behavior.noSkippedObjects\r\n    ).length;\r\n\r\n    return Math.min(1.0, correctCorrespondence / countingBehavior.length);\r\n  }\r\n\r\n  assessProcessingSpeed(data) {\r\n    const attempts = data.attempts;\r\n    if (attempts.length === 0) return 0.7;\r\n\r\n    const difficultyGroups = this.groupByDifficulty(attempts);\r\n    let totalSpeedScore = 0;\r\n    let groupCount = 0;\r\n\r\n    Object.keys(difficultyGroups).forEach(difficulty => {\r\n      const group = difficultyGroups[difficulty];\r\n      const expectedTime = this.difficultyRanges[difficulty]?.expectedTime || 5000;\r\n      \r\n      const avgTime = group.reduce((sum, a) => sum + a.responseTime, 0) / group.length;\r\n      const speedScore = Math.max(0, 1 - (avgTime - expectedTime) / expectedTime);\r\n      \r\n      totalSpeedScore += speedScore;\r\n      groupCount++;\r\n    });\r\n\r\n    return groupCount > 0 ? totalSpeedScore / groupCount : 0.7;\r\n  }\r\n\r\n  analyzeErrorPatterns(data) {\r\n    const attempts = data.attempts;\r\n    const errors = attempts.filter(a => !a.isCorrect);\r\n    \r\n    if (errors.length === 0) {\r\n      return {\r\n        countingErrors: 0,\r\n        systematicOvercount: 0,\r\n        systematicUndercount: 0,\r\n        skipErrors: 0,\r\n        doubleCountErrors: 0,\r\n        patternSeverity: 'none'\r\n      };\r\n    }\r\n\r\n    const patterns = {\r\n      countingErrors: errors.filter(e => e.errorType === 'counting').length / errors.length,\r\n      systematicOvercount: errors.filter(e => e.userAnswer > e.correctAnswer).length / errors.length,\r\n      systematicUndercount: errors.filter(e => e.userAnswer < e.correctAnswer).length / errors.length,\r\n      skipErrors: errors.filter(e => e.errorType === 'skip').length / errors.length,\r\n      doubleCountErrors: errors.filter(e => e.errorType === 'doubleCount').length / errors.length\r\n    };\r\n\r\n    // Determinar severidade dos padrões\r\n    const maxPattern = Math.max(...Object.values(patterns));\r\n    patterns.patternSeverity = maxPattern > 0.7 ? 'high' : maxPattern > 0.4 ? 'medium' : 'low';\r\n\r\n    return patterns;\r\n  }\r\n\r\n  assessCognitiveLoad(data) {\r\n    const attempts = data.attempts;\r\n    if (attempts.length === 0) return 0.7;\r\n\r\n    // Análise de degradação de performance ao longo do tempo\r\n    const performanceOverTime = this.analyzePerformanceDecline(attempts);\r\n    \r\n    // Análise de tempo de resposta em relação à dificuldade\r\n    const timeComplexityRelation = this.analyzeTimeComplexityRelation(attempts);\r\n    \r\n    // Análise de consistência de performance\r\n    const consistencyScore = this.measurePerformanceConsistency(attempts);\r\n    \r\n    const cognitiveLoadScore = (\r\n      (1 - performanceOverTime.decline) * 0.4 +\r\n      timeComplexityRelation.efficiency * 0.3 +\r\n      consistencyScore * 0.3\r\n    );\r\n\r\n    return Math.min(1.0, Math.max(0, cognitiveLoadScore));\r\n  }\r\n\r\n  assessNumericalFluency(data) {\r\n    const attempts = data.attempts;\r\n    if (attempts.length === 0) return 0.7;\r\n\r\n    // Fluência baseada em velocidade + precisão\r\n    const accuracyScore = attempts.filter(a => a.isCorrect).length / attempts.length;\r\n    const avgResponseTime = attempts.reduce((sum, a) => sum + a.responseTime, 0) / attempts.length;\r\n    \r\n    // Normalizar tempo de resposta (idealmente < 3000ms para números pequenos)\r\n    const speedScore = Math.max(0, 1 - (avgResponseTime - 2000) / 5000);\r\n    \r\n    // Análise de automaticidade (respostas muito rápidas e corretas)\r\n    const automaticResponses = attempts.filter(a => \r\n      a.isCorrect && a.responseTime < 1500 && a.correctAnswer <= 5\r\n    ).length;\r\n    \r\n    const automaticityBonus = (automaticResponses / attempts.length) * 0.2;\r\n    \r\n    return Math.min(1.0, (accuracyScore * 0.6 + speedScore * 0.4) + automaticityBonus);\r\n  }\r\n\r\n  assessAdaptivePerformance(data) {\r\n    const attempts = data.attempts;\r\n    if (attempts.length < 5) return 0.7;\r\n\r\n    // Análise de melhoria ao longo do tempo\r\n    const improvementRate = this.calculateLearningCurve(attempts);\r\n    \r\n    // Análise de adaptação a diferentes dificuldades\r\n    const difficultyAdaptation = this.analyzeDifficultyAdaptation(attempts);\r\n    \r\n    // Análise de recuperação após erros\r\n    const errorRecovery = this.analyzeErrorRecovery(attempts);\r\n    \r\n    return Math.min(1.0, (\r\n      improvementRate * 0.4 +\r\n      difficultyAdaptation * 0.3 +\r\n      errorRecovery * 0.3\r\n    ));\r\n  }\r\n\r\n  // Métodos auxiliares\r\n  analyzeAccuracyByNumberRange(attempts) {\r\n    const ranges = {\r\n      small: attempts.filter(a => a.correctAnswer <= 3),\r\n      medium: attempts.filter(a => a.correctAnswer > 3 && a.correctAnswer <= 8),\r\n      large: attempts.filter(a => a.correctAnswer > 8)\r\n    };\r\n\r\n    const accuracies = {};\r\n    Object.keys(ranges).forEach(range => {\r\n      if (ranges[range].length > 0) {\r\n        accuracies[range] = ranges[range].filter(a => a.isCorrect).length / ranges[range].length;\r\n      }\r\n    });\r\n\r\n    const consistencyScore = this.calculateConsistency(Object.values(accuracies));\r\n    return { accuracies, consistency: consistencyScore };\r\n  }\r\n\r\n  detectSystematicBias(attempts) {\r\n    const errors = attempts.filter(a => !a.isCorrect);\r\n    if (errors.length === 0) return { hasBias: false, type: 'none', magnitude: 0 };\r\n\r\n    const overcounts = errors.filter(e => e.userAnswer > e.correctAnswer).length;\r\n    const undercounts = errors.filter(e => e.userAnswer < e.correctAnswer).length;\r\n\r\n    const overPercent = overcounts / errors.length;\r\n    const underPercent = undercounts / errors.length;\r\n\r\n    if (overPercent > 0.7) {\r\n      return { hasBias: true, type: 'overcount', magnitude: overPercent };\r\n    } else if (underPercent > 0.7) {\r\n      return { hasBias: true, type: 'undercount', magnitude: underPercent };\r\n    }\r\n\r\n    return { hasBias: false, type: 'none', magnitude: 0 };\r\n  }\r\n\r\n  assessMagnitudeDiscrimination(attempts) {\r\n    // Avaliar capacidade de discriminar entre números próximos\r\n    const closeNumberPairs = attempts.filter(a => {\r\n      const otherAttempts = attempts.filter(b => \r\n        Math.abs(b.correctAnswer - a.correctAnswer) <= 2 && b !== a\r\n      );\r\n      return otherAttempts.length > 0;\r\n    });\r\n\r\n    if (closeNumberPairs.length === 0) return 0.7;\r\n\r\n    const correctDiscrimination = closeNumberPairs.filter(a => a.isCorrect).length;\r\n    return correctDiscrimination / closeNumberPairs.length;\r\n  }\r\n\r\n  groupByDifficulty(attempts) {\r\n    return attempts.reduce((groups, attempt) => {\r\n      const difficulty = attempt.difficulty || this.inferDifficulty(attempt.correctAnswer);\r\n      if (!groups[difficulty]) groups[difficulty] = [];\r\n      groups[difficulty].push(attempt);\r\n      return groups;\r\n    }, {});\r\n  }\r\n\r\n  inferDifficulty(number) {\r\n    if (number <= 5) return 'easy';\r\n    if (number <= 10) return 'medium';\r\n    return 'hard';\r\n  }\r\n\r\n  calculateConsistency(values) {\r\n    if (values.length < 2) return 1;\r\n    const mean = values.reduce((sum, v) => sum + v, 0) / values.length;\r\n    const variance = values.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / values.length;\r\n    return Math.max(0, 1 - variance);\r\n  }\r\n\r\n  analyzePerformanceDecline(attempts) {\r\n    if (attempts.length < 5) return { decline: 0 };\r\n\r\n    const firstHalf = attempts.slice(0, Math.floor(attempts.length / 2));\r\n    const secondHalf = attempts.slice(Math.floor(attempts.length / 2));\r\n\r\n    const firstAccuracy = firstHalf.filter(a => a.isCorrect).length / firstHalf.length;\r\n    const secondAccuracy = secondHalf.filter(a => a.isCorrect).length / secondHalf.length;\r\n\r\n    const decline = Math.max(0, firstAccuracy - secondAccuracy);\r\n    return { decline, firstAccuracy, secondAccuracy };\r\n  }\r\n\r\n  getDefaultMetrics() {\r\n    return {\r\n      countingAccuracy: 0.7,\r\n      subitizingAbility: 0.7,\r\n      numberSenseCapacity: 0.7,\r\n      cardinalityUnderstanding: 0.7,\r\n      oneToOneCorrespondence: 0.7,\r\n      processingSpeed: 0.7,\r\n      errorPatterns: {\r\n        countingErrors: 0.3,\r\n        systematicOvercount: 0.2,\r\n        systematicUndercount: 0.2,\r\n        skipErrors: 0.1,\r\n        doubleCountErrors: 0.1,\r\n        patternSeverity: 'low'\r\n      },\r\n      cognitiveLoad: 0.7,\r\n      numericalFluency: 0.7,\r\n      adaptivePerformance: 0.7\r\n    };\r\n  }\r\n\r\n  measureCardinalityConsistency(sequences) {\r\n    if (sequences.length < 3) return 1;\r\n    \r\n    const correctUses = sequences.filter(seq => \r\n      seq.finalAnswer === seq.lastCountedNumber\r\n    ).length;\r\n    \r\n    return correctUses / sequences.length;\r\n  }\r\n\r\n  analyzeTimeComplexityRelation(attempts) {\r\n    const complexityGroups = this.groupByDifficulty(attempts);\r\n    let efficiency = 0;\r\n    let groupCount = 0;\r\n\r\n    Object.keys(complexityGroups).forEach(difficulty => {\r\n      const group = complexityGroups[difficulty];\r\n      const expectedComplexity = this.difficultyRanges[difficulty]?.expectedTime || 5000;\r\n      const actualComplexity = group.reduce((sum, a) => sum + a.responseTime, 0) / group.length;\r\n      \r\n      const groupEfficiency = Math.max(0, 1 - Math.abs(actualComplexity - expectedComplexity) / expectedComplexity);\r\n      efficiency += groupEfficiency;\r\n      groupCount++;\r\n    });\r\n\r\n    return { efficiency: groupCount > 0 ? efficiency / groupCount : 0.7 };\r\n  }\r\n\r\n  measurePerformanceConsistency(attempts) {\r\n    if (attempts.length < 3) return 1;\r\n\r\n    const accuracies = [];\r\n    const windowSize = 3;\r\n    \r\n    for (let i = 0; i <= attempts.length - windowSize; i++) {\r\n      const window = attempts.slice(i, i + windowSize);\r\n      const windowAccuracy = window.filter(a => a.isCorrect).length / window.length;\r\n      accuracies.push(windowAccuracy);\r\n    }\r\n\r\n    return this.calculateConsistency(accuracies);\r\n  }\r\n\r\n  calculateLearningCurve(attempts) {\r\n    if (attempts.length < 5) return 0.5;\r\n\r\n    const segments = 5;\r\n    const segmentSize = Math.floor(attempts.length / segments);\r\n    const segmentAccuracies = [];\r\n\r\n    for (let i = 0; i < segments; i++) {\r\n      const start = i * segmentSize;\r\n      const end = i === segments - 1 ? attempts.length : (i + 1) * segmentSize;\r\n      const segment = attempts.slice(start, end);\r\n      \r\n      if (segment.length > 0) {\r\n        const accuracy = segment.filter(a => a.isCorrect).length / segment.length;\r\n        segmentAccuracies.push(accuracy);\r\n      }\r\n    }\r\n\r\n    // Calcular tendência de melhoria\r\n    const firstAccuracy = segmentAccuracies[0] || 0;\r\n    const lastAccuracy = segmentAccuracies[segmentAccuracies.length - 1] || 0;\r\n    \r\n    return Math.max(0, Math.min(1, 0.5 + (lastAccuracy - firstAccuracy)));\r\n  }\r\n\r\n  analyzeDifficultyAdaptation(attempts) {\r\n    const difficultyGroups = this.groupByDifficulty(attempts);\r\n    const difficultyLevels = Object.keys(difficultyGroups).length;\r\n    \r\n    if (difficultyLevels < 2) return 0.7;\r\n\r\n    let adaptationScore = 0;\r\n    Object.keys(difficultyGroups).forEach(difficulty => {\r\n      const group = difficultyGroups[difficulty];\r\n      const accuracy = group.filter(a => a.isCorrect).length / group.length;\r\n      \r\n      // Espera-se diferentes níveis de acurácia para diferentes dificuldades\r\n      const expectedAccuracy = difficulty === 'easy' ? 0.9 : difficulty === 'medium' ? 0.75 : 0.6;\r\n      const adaptationForLevel = 1 - Math.abs(accuracy - expectedAccuracy);\r\n      \r\n      adaptationScore += adaptationForLevel;\r\n    });\r\n\r\n    return adaptationScore / difficultyLevels;\r\n  }\r\n\r\n  analyzeErrorRecovery(attempts) {\r\n    const errors = attempts.map((attempt, index) => ({ ...attempt, index }))\r\n                          .filter(a => !a.isCorrect);\r\n    \r\n    if (errors.length === 0) return 1;\r\n\r\n    let recoveryCount = 0;\r\n    errors.forEach(error => {\r\n      // Verificar se os próximos 2-3 attemps foram corretos (recuperação)\r\n      const nextAttempts = attempts.slice(error.index + 1, error.index + 4);\r\n      const correctNext = nextAttempts.filter(a => a.isCorrect).length;\r\n      \r\n      if (correctNext >= Math.min(2, nextAttempts.length)) {\r\n        recoveryCount++;\r\n      }\r\n    });\r\n\r\n    return recoveryCount / errors.length;\r\n  }\r\n}\r\n", "/**\r\n * 🧠 ATTENTION AND FOCUS COLLECTOR\r\n * Coletor especializado em análise de atenção e foco para NumberCounting\r\n * Portal Betina V3\r\n */\r\n\r\nexport class AttentionFocusCollector {\r\n  constructor() {\r\n    this.attentionMetrics = {\r\n      sustained: 'Atenção sustentada ao longo do tempo',\r\n      selective: 'Atenção seletiva para objetos relevantes', \r\n      divided: 'Capacidade de dividir atenção entre tarefas',\r\n      focused: 'Intensidade do foco em objetos específicos'\r\n    };\r\n    \r\n    this.focusThresholds = {\r\n      excellent: 0.95,\r\n      good: 0.85,\r\n      average: 0.70,\r\n      poor: 0.50,\r\n      critical: 0.30\r\n    };\r\n    \r\n    this.distractionSources = {\r\n      visual: 'Distractores visuais na tela',\r\n      temporal: 'Fadiga ao longo do tempo',\r\n      cognitive: 'Sobrecarga cognitiva',\r\n      motor: 'Interferência motora'\r\n    };\r\n  }\r\n  \r\n  /**\r\n   * Método padronizado de coleta de dados para integração com testes\r\n   * @param {Object} data - Dados do jogo a serem analisados\r\n   * @returns {Object} - Resultado da análise\r\n   */\r\n  collect(data) {\r\n    return this.analyze(data);\r\n  }\r\n\r\n  async analyze(data) {\r\n    if (!data || !data.attempts) {\r\n      console.warn('AttentionFocusCollector: Dados inválidos recebidos', data);\r\n      return this.getDefaultMetrics();\r\n    }\r\n\r\n    return {\r\n      sustainedAttention: this.assessSustainedAttention(data),\r\n      selectiveAttention: this.assessSelectiveAttention(data),\r\n      attentionalControl: this.assessAttentionalControl(data),\r\n      focusStability: this.assessFocusStability(data),\r\n      distractionResistance: this.assessDistractionResistance(data),\r\n      vigilanceDecrement: this.assessVigilanceDecrement(data),\r\n      attentionalSwitching: this.assessAttentionalSwitching(data),\r\n      concentrationDepth: this.assessConcentrationDepth(data),\r\n      cognitiveFlexibility: this.assessCognitiveFlexibility(data),\r\n      attentionalEfficiency: this.assessAttentionalEfficiency(data)\r\n    };\r\n  }\r\n\r\n  assessSustainedAttention(data) {\r\n    const attempts = data.attempts;\r\n    if (attempts.length < 5) return 0.7;\r\n\r\n    // Analisar consistência de performance ao longo do tempo\r\n    const timeSegments = this.divideIntoTimeSegments(attempts, 5);\r\n    const segmentAccuracies = timeSegments.map(segment => \r\n      segment.filter(a => a.isCorrect).length / segment.length\r\n    );\r\n\r\n    // Calcular estabilidade da atenção\r\n    const meanAccuracy = segmentAccuracies.reduce((sum, acc) => sum + acc, 0) / segmentAccuracies.length;\r\n    const variance = segmentAccuracies.reduce((sum, acc) => sum + Math.pow(acc - meanAccuracy, 2), 0) / segmentAccuracies.length;\r\n    \r\n    // Atenção sustentada é medida pela estabilidade e nível geral\r\n    const stability = Math.max(0, 1 - variance * 2);\r\n    const sustainedScore = (meanAccuracy * 0.7 + stability * 0.3);\r\n\r\n    return Math.min(1.0, sustainedScore);\r\n  }\r\n\r\n  assessSelectiveAttention(data) {\r\n    const attempts = data.attempts;\r\n    if (attempts.length === 0) return 0.7;\r\n\r\n    // Analisar capacidade de focar em objetos relevantes vs distractores\r\n    const objectCounts = attempts.map(a => a.correctAnswer);\r\n    const responseAccuracy = attempts.filter(a => a.isCorrect).length / attempts.length;\r\n    \r\n    // Analisar tempo de resposta em relação à densidade de objetos\r\n    const timeByDensity = this.analyzeTimeByObjectDensity(attempts);\r\n    \r\n    // Analisar padrões de erro relacionados à seleção incorreta\r\n    const selectionErrors = this.analyzeSelectionErrors(attempts);\r\n    \r\n    const selectiveScore = (\r\n      responseAccuracy * 0.5 +\r\n      timeByDensity.efficiency * 0.3 +\r\n      (1 - selectionErrors.rate) * 0.2\r\n    );\r\n\r\n    return Math.min(1.0, selectiveScore);\r\n  }\r\n\r\n  assessAttentionalControl(data) {\r\n    const attempts = data.attempts;\r\n    if (attempts.length === 0) return 0.7;\r\n\r\n    // Controle atencional medido por:\r\n    // 1. Capacidade de manter foco em tarefas de diferentes dificuldades\r\n    // 2. Regulação da velocidade de resposta baseada na complexidade\r\n    // 3. Recuperação após distrações/erros\r\n\r\n    const difficultyGroups = this.groupByDifficulty(attempts);\r\n    let controlScore = 0;\r\n    let groupCount = 0;\r\n\r\n    Object.keys(difficultyGroups).forEach(difficulty => {\r\n      const group = difficultyGroups[difficulty];\r\n      if (group.length === 0) return;\r\n\r\n      // Analisar adaptação do tempo de resposta à dificuldade\r\n      const avgTime = group.reduce((sum, a) => sum + a.responseTime, 0) / group.length;\r\n      const expectedTime = this.getExpectedTimeForDifficulty(difficulty);\r\n      const timeAdaptation = 1 - Math.abs(avgTime - expectedTime) / expectedTime;\r\n      \r\n      // Analisar consistência dentro do grupo\r\n      const accuracy = group.filter(a => a.isCorrect).length / group.length;\r\n      \r\n      controlScore += (timeAdaptation * 0.6 + accuracy * 0.4);\r\n      groupCount++;\r\n    });\r\n\r\n    return groupCount > 0 ? controlScore / groupCount : 0.7;\r\n  }\r\n\r\n  assessFocusStability(data) {\r\n    const attempts = data.attempts;\r\n    if (attempts.length < 8) return 0.7;\r\n\r\n    // Analisar variabilidade no tempo de resposta (indica estabilidade do foco)\r\n    const responseTimes = attempts.map(a => a.responseTime);\r\n    const meanTime = responseTimes.reduce((sum, t) => sum + t, 0) / responseTimes.length;\r\n    const timeVariability = responseTimes.reduce((sum, t) => sum + Math.pow(t - meanTime, 2), 0) / responseTimes.length;\r\n    \r\n    // Normalizar variabilidade (menor variabilidade = maior estabilidade)\r\n    const stabilityScore = Math.max(0, 1 - Math.sqrt(timeVariability) / meanTime);\r\n    \r\n    // Analisar padrões de lapsos atencionais\r\n    const attentionalLapses = this.detectAttentionalLapses(attempts);\r\n    const lapsesPenalty = attentionalLapses.frequency * 0.3;\r\n    \r\n    return Math.min(1.0, Math.max(0, stabilityScore - lapsesPenalty));\r\n  }\r\n\r\n  assessDistractionResistance(data) {\r\n    const attempts = data.attempts;\r\n    if (attempts.length === 0) return 0.7;\r\n\r\n    // Resistência à distração medida por:\r\n    // 1. Performance consistente em condições com mais objetos (maior distração visual)\r\n    // 2. Recuperação rápida após erros\r\n    // 3. Manutenção de performance em sessões longas\r\n\r\n    const highDensityAttempts = attempts.filter(a => a.correctAnswer > 8);\r\n    const lowDensityAttempts = attempts.filter(a => a.correctAnswer <= 5);\r\n\r\n    if (highDensityAttempts.length === 0 || lowDensityAttempts.length === 0) {\r\n      return 0.7;\r\n    }\r\n\r\n    const highDensityAccuracy = highDensityAttempts.filter(a => a.isCorrect).length / highDensityAttempts.length;\r\n    const lowDensityAccuracy = lowDensityAttempts.filter(a => a.isCorrect).length / lowDensityAttempts.length;\r\n    \r\n    // Menor diferença = maior resistência à distração\r\n    const distractionResistance = 1 - Math.abs(highDensityAccuracy - lowDensityAccuracy);\r\n    \r\n    // Analisar recovery após erros (indica resistência à distração emocional)\r\n    const errorRecovery = this.analyzeErrorRecovery(attempts);\r\n    \r\n    return Math.min(1.0, (distractionResistance * 0.7 + errorRecovery * 0.3));\r\n  }\r\n\r\n  assessVigilanceDecrement(data) {\r\n    const attempts = data.attempts;\r\n    if (attempts.length < 10) return 0.7;\r\n\r\n    // Decremento de vigilância: degradação da performance ao longo do tempo\r\n    const timeQuarters = this.divideIntoTimeSegments(attempts, 4);\r\n    \r\n    if (timeQuarters.length < 2) return 0.7;\r\n\r\n    const quarterAccuracies = timeQuarters.map(quarter => \r\n      quarter.filter(a => a.isCorrect).length / quarter.length\r\n    );\r\n\r\n    // Calcular tendência de decline\r\n    const firstQuarter = quarterAccuracies[0];\r\n    const lastQuarter = quarterAccuracies[quarterAccuracies.length - 1];\r\n    const decline = Math.max(0, firstQuarter - lastQuarter);\r\n    \r\n    // Menor decline = melhor vigilância\r\n    const vigilanceScore = Math.max(0, 1 - decline * 2);\r\n    \r\n    return Math.min(1.0, vigilanceScore);\r\n  }\r\n\r\n  assessAttentionalSwitching(data) {\r\n    const attempts = data.attempts;\r\n    if (attempts.length === 0) return 0.7;\r\n\r\n    // Mudança atencional medida por adaptação a diferentes tipos de tarefas\r\n    const difficultyChanges = this.detectDifficultyChanges(attempts);\r\n    \r\n    if (difficultyChanges.length === 0) return 0.7;\r\n\r\n    // Analisar performance imediatamente após mudanças de dificuldade\r\n    const switchingEfficiency = difficultyChanges.map(change => {\r\n      const postSwitchAttempts = attempts.slice(change.index, change.index + 3);\r\n      return postSwitchAttempts.filter(a => a.isCorrect).length / postSwitchAttempts.length;\r\n    });\r\n\r\n    const avgSwitchingEfficiency = switchingEfficiency.reduce((sum, eff) => sum + eff, 0) / switchingEfficiency.length;\r\n    \r\n    return Math.min(1.0, avgSwitchingEfficiency);\r\n  }\r\n\r\n  assessConcentrationDepth(data) {\r\n    const attempts = data.attempts;\r\n    if (attempts.length === 0) return 0.7;\r\n\r\n    // Profundidade de concentração medida por:\r\n    // 1. Tempo médio de resposta (indicando processamento cuidadoso)\r\n    // 2. Precisão em tarefas complexas\r\n    // 3. Consistência na estratégia de contagem\r\n\r\n    const complexAttempts = attempts.filter(a => a.correctAnswer > 10);\r\n    \r\n    if (complexAttempts.length === 0) {\r\n      // Se não há tarefas complexas, usar todos os attempts\r\n      const accuracy = attempts.filter(a => a.isCorrect).length / attempts.length;\r\n      const avgTime = attempts.reduce((sum, a) => sum + a.responseTime, 0) / attempts.length;\r\n      \r\n      // Tempo moderado (nem muito rápido nem muito lento) indica boa concentração\r\n      const timeScore = this.assessOptimalResponseTime(avgTime);\r\n      \r\n      return Math.min(1.0, (accuracy * 0.6 + timeScore * 0.4));\r\n    }\r\n\r\n    const complexAccuracy = complexAttempts.filter(a => a.isCorrect).length / complexAttempts.length;\r\n    const avgComplexTime = complexAttempts.reduce((sum, a) => sum + a.responseTime, 0) / complexAttempts.length;\r\n    \r\n    const timeScore = this.assessOptimalResponseTime(avgComplexTime);\r\n    const concentrationScore = (complexAccuracy * 0.7 + timeScore * 0.3);\r\n    \r\n    return Math.min(1.0, concentrationScore);\r\n  }\r\n\r\n  assessCognitiveFlexibility(data) {\r\n    const attempts = data.attempts;\r\n    if (attempts.length < 5) return 0.7;\r\n\r\n    // Flexibilidade cognitiva medida por:\r\n    // 1. Adaptação a diferentes rangos numéricos\r\n    // 2. Variação na estratégia de contagem\r\n    // 3. Recuperação de estratégias ineficazes\r\n\r\n    const numberRanges = this.categorizeByNumberRange(attempts);\r\n    const rangeCount = Object.keys(numberRanges).length;\r\n    \r\n    if (rangeCount < 2) return 0.7;\r\n\r\n    // Analisar performance em diferentes ranges\r\n    let flexibilityScore = 0;\r\n    Object.keys(numberRanges).forEach(range => {\r\n      const rangeAttempts = numberRanges[range];\r\n      const rangeAccuracy = rangeAttempts.filter(a => a.isCorrect).length / rangeAttempts.length;\r\n      flexibilityScore += rangeAccuracy;\r\n    });\r\n\r\n    flexibilityScore /= rangeCount;\r\n\r\n    // Bonus por demonstrar estratégias adaptativas\r\n    const adaptiveStrategies = this.detectAdaptiveStrategies(attempts);\r\n    const strategyBonus = adaptiveStrategies.count * 0.1;\r\n    \r\n    return Math.min(1.0, flexibilityScore + strategyBonus);\r\n  }\r\n\r\n  assessAttentionalEfficiency(data) {\r\n    const attempts = data.attempts;\r\n    if (attempts.length === 0) return 0.7;\r\n\r\n    // Eficiência atencional: relação entre precisão e velocidade\r\n    const accuracy = attempts.filter(a => a.isCorrect).length / attempts.length;\r\n    const avgTime = attempts.reduce((sum, a) => sum + a.responseTime, 0) / attempts.length;\r\n    \r\n    // Normalizar tempo (idealmente entre 2-5 segundos para boa eficiência)\r\n    const timeEfficiency = this.calculateTimeEfficiency(avgTime);\r\n    \r\n    // Combinar precisão e eficiência temporal\r\n    const efficiencyScore = (accuracy * 0.6 + timeEfficiency * 0.4);\r\n    \r\n    // Analisar consistência da eficiência\r\n    const consistencyBonus = this.assessEfficiencyConsistency(attempts) * 0.1;\r\n    \r\n    return Math.min(1.0, efficiencyScore + consistencyBonus);\r\n  }\r\n\r\n  // Métodos auxiliares\r\n  divideIntoTimeSegments(attempts, segmentCount) {\r\n    const segmentSize = Math.floor(attempts.length / segmentCount);\r\n    const segments = [];\r\n    \r\n    for (let i = 0; i < segmentCount; i++) {\r\n      const start = i * segmentSize;\r\n      const end = i === segmentCount - 1 ? attempts.length : (i + 1) * segmentSize;\r\n      segments.push(attempts.slice(start, end));\r\n    }\r\n    \r\n    return segments.filter(segment => segment.length > 0);\r\n  }\r\n\r\n  analyzeTimeByObjectDensity(attempts) {\r\n    const densityGroups = {\r\n      low: attempts.filter(a => a.correctAnswer <= 3),\r\n      medium: attempts.filter(a => a.correctAnswer > 3 && a.correctAnswer <= 8),\r\n      high: attempts.filter(a => a.correctAnswer > 8)\r\n    };\r\n\r\n    let totalEfficiency = 0;\r\n    let groupCount = 0;\r\n\r\n    Object.keys(densityGroups).forEach(density => {\r\n      const group = densityGroups[density];\r\n      if (group.length === 0) return;\r\n\r\n      const avgTime = group.reduce((sum, a) => sum + a.responseTime, 0) / group.length;\r\n      const expectedTime = density === 'low' ? 2000 : density === 'medium' ? 4000 : 6000;\r\n      \r\n      const efficiency = Math.max(0, 1 - Math.abs(avgTime - expectedTime) / expectedTime);\r\n      totalEfficiency += efficiency;\r\n      groupCount++;\r\n    });\r\n\r\n    return { efficiency: groupCount > 0 ? totalEfficiency / groupCount : 0.7 };\r\n  }\r\n\r\n  analyzeSelectionErrors(attempts) {\r\n    const errors = attempts.filter(a => !a.isCorrect);\r\n    if (errors.length === 0) return { rate: 0, patterns: [] };\r\n\r\n    // Analisar tipos de erro de seleção\r\n    const offByOne = errors.filter(e => Math.abs(e.userAnswer - e.correctAnswer) === 1).length;\r\n    const offByMany = errors.filter(e => Math.abs(e.userAnswer - e.correctAnswer) > 3).length;\r\n    \r\n    const selectionErrorRate = errors.length / attempts.length;\r\n    const patterns = [];\r\n    \r\n    if (offByOne / errors.length > 0.5) patterns.push('off_by_one_tendency');\r\n    if (offByMany / errors.length > 0.3) patterns.push('gross_miscounting');\r\n    \r\n    return { rate: selectionErrorRate, patterns };\r\n  }\r\n\r\n  groupByDifficulty(attempts) {\r\n    return attempts.reduce((groups, attempt) => {\r\n      const difficulty = this.inferDifficulty(attempt.correctAnswer);\r\n      if (!groups[difficulty]) groups[difficulty] = [];\r\n      groups[difficulty].push(attempt);\r\n      return groups;\r\n    }, {});\r\n  }\r\n\r\n  inferDifficulty(number) {\r\n    if (number <= 5) return 'easy';\r\n    if (number <= 10) return 'medium';\r\n    return 'hard';\r\n  }\r\n\r\n  getExpectedTimeForDifficulty(difficulty) {\r\n    const times = { easy: 2500, medium: 4000, hard: 6000 };\r\n    return times[difficulty] || 4000;\r\n  }\r\n\r\n  detectAttentionalLapses(attempts) {\r\n    // Lapsos atencionais: respostas muito rápidas e incorretas ou muito lentas\r\n    const lapses = attempts.filter(a => \r\n      (!a.isCorrect && a.responseTime < 1000) || // Resposta impulsiva incorreta\r\n      (a.responseTime > 15000) // Resposta muito lenta (possível distração)\r\n    );\r\n\r\n    return {\r\n      frequency: lapses.length / attempts.length,\r\n      count: lapses.length,\r\n      types: {\r\n        impulsive: lapses.filter(l => l.responseTime < 1000).length,\r\n        distracted: lapses.filter(l => l.responseTime > 15000).length\r\n      }\r\n    };\r\n  }\r\n\r\n  analyzeErrorRecovery(attempts) {\r\n    const errors = attempts.map((attempt, index) => ({ ...attempt, index }))\r\n                          .filter(a => !a.isCorrect);\r\n    \r\n    if (errors.length === 0) return 1;\r\n\r\n    let recoveryCount = 0;\r\n    errors.forEach(error => {\r\n      const nextAttempt = attempts[error.index + 1];\r\n      if (nextAttempt && nextAttempt.isCorrect) {\r\n        recoveryCount++;\r\n      }\r\n    });\r\n\r\n    return recoveryCount / errors.length;\r\n  }\r\n\r\n  detectDifficultyChanges(attempts) {\r\n    const changes = [];\r\n    \r\n    for (let i = 1; i < attempts.length; i++) {\r\n      const prevDifficulty = this.inferDifficulty(attempts[i-1].correctAnswer);\r\n      const currDifficulty = this.inferDifficulty(attempts[i].correctAnswer);\r\n      \r\n      if (prevDifficulty !== currDifficulty) {\r\n        changes.push({\r\n          index: i,\r\n          from: prevDifficulty,\r\n          to: currDifficulty\r\n        });\r\n      }\r\n    }\r\n    \r\n    return changes;\r\n  }\r\n\r\n  assessOptimalResponseTime(avgTime) {\r\n    // Tempo ótimo está entre 2-5 segundos\r\n    const optimalMin = 2000;\r\n    const optimalMax = 5000;\r\n    \r\n    if (avgTime >= optimalMin && avgTime <= optimalMax) {\r\n      return 1.0;\r\n    } else if (avgTime < optimalMin) {\r\n      return Math.max(0, avgTime / optimalMin);\r\n    } else {\r\n      return Math.max(0, 1 - (avgTime - optimalMax) / optimalMax);\r\n    }\r\n  }\r\n\r\n  categorizeByNumberRange(attempts) {\r\n    return attempts.reduce((categories, attempt) => {\r\n      const range = attempt.correctAnswer <= 3 ? 'small' : \r\n                   attempt.correctAnswer <= 8 ? 'medium' : 'large';\r\n      \r\n      if (!categories[range]) categories[range] = [];\r\n      categories[range].push(attempt);\r\n      return categories;\r\n    }, {});\r\n  }\r\n\r\n  detectAdaptiveStrategies(attempts) {\r\n    // Detectar se o usuário adapta estratégias baseado na performance\r\n    let strategyChanges = 0;\r\n    \r\n    // Analisar mudanças no padrão de tempo de resposta após erros\r\n    for (let i = 1; i < attempts.length; i++) {\r\n      const prev = attempts[i-1];\r\n      const curr = attempts[i];\r\n      \r\n      if (!prev.isCorrect && curr.responseTime > prev.responseTime * 1.3) {\r\n        strategyChanges++; // Pessoa tornou-se mais cuidadosa após erro\r\n      }\r\n    }\r\n    \r\n    return { count: strategyChanges };\r\n  }\r\n\r\n  calculateTimeEfficiency(avgTime) {\r\n    // Eficiência baseada em tempo ideal (3 segundos)\r\n    const idealTime = 3000;\r\n    return Math.max(0, 1 - Math.abs(avgTime - idealTime) / idealTime);\r\n  }\r\n\r\n  assessEfficiencyConsistency(attempts) {\r\n    if (attempts.length < 5) return 1;\r\n\r\n    const efficiencies = attempts.map(a => {\r\n      const accuracy = a.isCorrect ? 1 : 0;\r\n      const timeEfficiency = this.calculateTimeEfficiency(a.responseTime);\r\n      return (accuracy * 0.6 + timeEfficiency * 0.4);\r\n    });\r\n\r\n    const meanEfficiency = efficiencies.reduce((sum, e) => sum + e, 0) / efficiencies.length;\r\n    const variance = efficiencies.reduce((sum, e) => sum + Math.pow(e - meanEfficiency, 2), 0) / efficiencies.length;\r\n    \r\n    return Math.max(0, 1 - variance);\r\n  }\r\n\r\n  getDefaultMetrics() {\r\n    return {\r\n      sustainedAttention: 0.7,\r\n      selectiveAttention: 0.7,\r\n      attentionalControl: 0.7,\r\n      focusStability: 0.7,\r\n      distractionResistance: 0.7,\r\n      vigilanceDecrement: 0.7,\r\n      attentionalSwitching: 0.7,\r\n      concentrationDepth: 0.7,\r\n      cognitiveFlexibility: 0.7,\r\n      attentionalEfficiency: 0.7\r\n    };\r\n  }\r\n}\r\n", "/**\r\n * 👁️ VISUAL PROCESSING COLLECTOR\r\n * Coletor especializado em análise de processamento visual para NumberCounting\r\n * Portal Betina V3\r\n */\r\n\r\nexport class VisualProcessingCollector {\r\n  constructor() {\r\n    this.visualComponents = {\r\n      objectRecognition: 'Reconhecimento de objetos visuais',\r\n      spatialDistribution: 'Processamento de distribuição espacial',\r\n      visualScanning: 'Varredura visual sistemática',\r\n      figureGround: 'Discriminação figura-fundo',\r\n      visualMemory: 'Memória visual de curto prazo'\r\n    };\r\n    \r\n    this.processingThresholds = {\r\n      excellent: 0.95,\r\n      good: 0.85,\r\n      average: 0.70,\r\n      poor: 0.50,\r\n      critical: 0.30\r\n    };\r\n    \r\n    this.scanningPatterns = {\r\n      systematic: 'Varredura visual sistemática',\r\n      random: 'Varredura aleatória',\r\n      focused: 'Varredura concentrada',\r\n      exhaustive: 'Varredura exaustiva'\r\n    };\r\n    \r\n    console.log(\"👁️ VisualProcessingCollector inicializado\");\r\n  }\r\n  \r\n  /**\r\n   * Método padronizado de coleta de dados para integração com testes\r\n   * @param {Object} data - Dados do jogo a serem analisados\r\n   * @returns {Object} - Resultado da análise\r\n   */\r\n  collect(data) {\r\n    return this.analyze(data);\r\n  }\r\n\r\n  /**\r\n   * Retorna métricas padrão quando dados são inválidos\r\n   */\r\n  getDefaultMetrics() {\r\n    return {\r\n      visualScanningEfficiency: { score: 0, efficiency: 0, scanningPattern: 'unknown' },\r\n      objectRecognition: { accuracy: 0, speed: 0, confidence: 0 },\r\n      spatialDistribution: { uniformity: 0, clustering: 0, coverage: 0 },\r\n      figureGroundPerception: { discrimination: 0, clarity: 0, focus: 0 },\r\n      visualMemoryPerformance: { retention: 0, recall: 0, recognition: 0 },\r\n      scanningStrategy: { strategy: 'unknown', consistency: 0, effectiveness: 0 },\r\n      processingSpeed: { averageTime: 0, variability: 0, efficiency: 0 },\r\n      visualDiscrimination: { accuracy: 0, precision: 0, sensitivity: 0 },\r\n      visualAttention: { focus: 0, sustain: 0, selective: 0 },\r\n      perceptualOrganization: { grouping: 0, structure: 0, coherence: 0 },\r\n      timestamp: Date.now(),\r\n      dataQuality: 'invalid'\r\n    };\r\n  }\r\n\r\n  async analyze(data) {\r\n    if (!data || !data.attempts) {\r\n      console.warn('VisualProcessingCollector: Dados inválidos recebidos', data);\r\n      return this.getDefaultMetrics();\r\n    }\r\n\r\n    return {\r\n      visualScanningEfficiency: this.assessVisualScanningEfficiency(data),\r\n      objectRecognition: this.assessObjectRecognition(data),\r\n      spatialDistribution: this.assessSpatialDistribution(data),\r\n      figureGroundPerception: this.assessFigureGroundPerception(data),\r\n      visualMemoryPerformance: this.assessVisualMemoryPerformance(data),\r\n      scanningStrategy: this.analyzeScanningStrategy(data),\r\n      processingSpeed: this.assessProcessingSpeed(data),\r\n      visualDiscrimination: this.assessVisualDiscrimination(data),\r\n      visualAttention: this.assessVisualAttention(data),\r\n      perceptualOrganization: this.assessPerceptualOrganization(data)\r\n    };\r\n  }\r\n}\r\n", "/**\r\n * 🧮 MATHEMATICAL REASONING COLLECTOR\r\n * Coletor especializado em análise de raciocínio matemático para NumberCounting\r\n * Portal Betina V3\r\n */\r\n\r\nexport class MathematicalReasoningCollector {\r\n  constructor() {\r\n    this.reasoningComponents = {\r\n      quantitativeReasoning: 'Raciocínio quantitativo fundamental',\r\n      mathematicalConcepts: 'Compreensão de conceitos matemáticos',\r\n      logicalSequencing: 'Sequenciamento lógico e ordenação',\r\n      abstractThinking: 'Pensamento abstrato com números',\r\n      problemSolving: 'Resolução de problemas matemáticos'\r\n    };\r\n    \r\n    this.mathematicalSkills = {\r\n      counting: 'Contagem sequencial',\r\n      cardinality: 'Compreensão de cardinalidade',\r\n      ordinality: 'Compreensão de ordinality',\r\n      conservation: 'Conservação numérica',\r\n      comparison: 'Comparação de quantidades'\r\n    };\r\n    \r\n    console.log(\"🧮 MathematicalReasoningCollector inicializado\");\r\n  }\r\n  \r\n  /**\r\n   * Método padronizado de coleta de dados para integração com testes\r\n   * @param {Object} data - Dados do jogo a serem analisados\r\n   * @returns {Object} - <PERSON>sultad<PERSON> da anális<PERSON>\r\n   */\r\n  collect(data) {\r\n    return this.analyze(data);\r\n  }\r\n\r\n  async analyze(data) {\r\n    if (!data || !data.attempts) {\r\n      console.warn('MathematicalReasoningCollector: Dados inválidos recebidos', data);\r\n      return this.getDefaultMetrics();\r\n    }\r\n\r\n    return {\r\n      quantitativeAccuracy: this.assessQuantitativeAccuracy(data),\r\n      countingProficiency: this.assessCountingProficiency(data),\r\n      mathematicalStrategies: this.assessMathematicalStrategies(data),\r\n      numericalConceptUnderstanding: this.assessNumericalConcepts(data),\r\n      logicalSequencing: this.assessLogicalSequencing(data),\r\n      numericalMemory: this.assessNumericalMemory(data),\r\n      errorAnalysis: this.analyzeNumericalErrors(data),\r\n      mathematicalFluency: this.assessMathematicalFluency(data),\r\n      abstractReasoning: this.assessAbstractReasoning(data),\r\n      patternRecognition: this.assessPatternRecognition(data)\r\n    };\r\n  }\r\n  \r\n  getDefaultMetrics() {\r\n    return {\r\n      quantitativeAccuracy: 0.7,\r\n      countingProficiency: 0.7,\r\n      mathematicalStrategies: { efficiency: 0.7, adaptability: 0.7 },\r\n      numericalConceptUnderstanding: 0.7,\r\n      logicalSequencing: 0.7,\r\n      numericalMemory: 0.7,\r\n      errorAnalysis: { patterns: [], frequency: 0 },\r\n      mathematicalFluency: 0.7,\r\n      abstractReasoning: 0.7,\r\n      patternRecognition: 0.7\r\n    };\r\n  }\r\n}\r\n", "/**\r\n * 🔢 CONTAGEM NÚMEROS ERROR PATTERN COLLECTOR\r\n * Algoritmo especializado para coleta e análise de padrões de erro no jogo de contagem de números\r\n */\r\n\r\nexport class ErrorPatternCollector {\r\n  constructor() {\r\n    this.name = 'ContagemNumerosErrorPatternCollector';\r\n    this.description = 'Coleta padrões de erros no ContagemNumeros';\r\n    this.version = '1.0.0';\r\n    this.isActive = true;\r\n    this.collectedData = [];\r\n    \r\n    this.errorData = {\r\n      countingErrors: {},\r\n      sequenceErrors: [],\r\n      numberRecognitionErrors: [],\r\n      quantityEstimationErrors: [],\r\n      persistentErrors: {},\r\n      errorClusters: [],\r\n      learningIndicators: [],\r\n      mathematicalConcepts: {}\r\n    };\r\n    this.sessionStartTime = Date.now();\r\n    this.errorThresholds = {\r\n      persistent: 3,\r\n      cluster: 5,\r\n      severity: {\r\n        low: 0.3,\r\n        medium: 0.6,\r\n        high: 0.8\r\n      }\r\n    };\r\n    console.log(`🔢 ${this.name} v${this.version} inicializado`);\r\n  }\r\n\r\n  /**\r\n   * Método padronizado de coleta de dados para integração com testes\r\n   * @param {Object} gameData - Dados do jogo a serem analisados\r\n   * @returns {Object} - Resultado da análise de erros\r\n   */\r\n  collect(gameData) {\r\n    if (!gameData) {\r\n      console.warn(\"ContagemNumerosErrorPatternCollector: Dados do jogo não fornecidos para análise\");\r\n      return { errors: [], patterns: [], metrics: {} };\r\n    }\r\n\r\n    console.log(`📊 ContagemNumerosErrorPatternCollector: Analisando dados da sessão ${gameData.sessionId || 'sem ID'}`);\r\n    \r\n    try {\r\n      // Extrair e categorizar erros dos dados do jogo\r\n      const errorMetrics = this.analyzeErrorPatterns(gameData);\r\n      const errors = [];\r\n      \r\n      // Analisar erros de contagem\r\n      if (gameData.attemptHistory && Array.isArray(gameData.attemptHistory)) {\r\n        gameData.attemptHistory.forEach((attempt, index) => {\r\n          if (!attempt.isCorrect && attempt.targetNumber !== undefined && attempt.selectedNumber !== undefined) {\r\n            const countingError = this.collectCountingError(\r\n              attempt.targetNumber,\r\n              attempt.selectedNumber,\r\n              { \r\n                difficulty: gameData.difficulty || 'medium',\r\n                responseTime: attempt.responseTime || 0,\r\n                attemptNumber: index\r\n              }\r\n            );\r\n            if (countingError) errors.push(countingError);\r\n          }\r\n        });\r\n      }\r\n      \r\n      // Analisar erros de sequência numérica\r\n      if (gameData.sequenceHistory && Array.isArray(gameData.sequenceHistory)) {\r\n        gameData.sequenceHistory.forEach((sequence) => {\r\n          if (sequence.expected && sequence.actual && sequence.expected !== sequence.actual) {\r\n            const sequenceError = this.collectSequenceError(\r\n              sequence.expected,\r\n              sequence.actual,\r\n              {\r\n                sequenceType: sequence.type || 'ascending',\r\n                difficulty: gameData.difficulty || 'medium'\r\n              }\r\n            );\r\n            if (sequenceError) errors.push(sequenceError);\r\n          }\r\n        });\r\n      }\r\n      \r\n      // Salvar dados coletados para análise futura\r\n      const collectedMetric = {\r\n        timestamp: Date.now(),\r\n        type: 'error_pattern',\r\n        gameType: 'ContagemNumeros',\r\n        data: errorMetrics,\r\n        errors: errors,\r\n        sessionData: {\r\n          sessionId: gameData.sessionId,\r\n          level: gameData.level || 1,\r\n          attempt: gameData.attempt || 1\r\n        }\r\n      };\r\n\r\n      this.collectedData.push(collectedMetric);\r\n      this.categorizeErrors(errorMetrics);\r\n      \r\n      return {\r\n        errors,\r\n        patterns: errorMetrics,\r\n        metrics: this.generateErrorMetrics(gameData)\r\n      };\r\n    } catch (error) {\r\n      console.error('❌ Erro ao coletar padrões de erro (ContagemNumeros):', error);\r\n      return { errors: [], patterns: [], metrics: {}, error: error.message };\r\n    }\r\n  }\r\n\r\n  analyzeErrorPatterns(gameData) {\r\n    const patterns = {\r\n      countingErrors: this.detectCountingErrors(gameData),\r\n      sequenceErrors: this.detectSequenceErrors(gameData),\r\n      numberRecognitionErrors: this.detectRecognitionErrors(gameData),\r\n      quantityEstimationErrors: this.detectEstimationErrors(gameData),\r\n      severity: this.calculateOverallSeverity(gameData)\r\n    };\r\n\r\n    return patterns;\r\n  }\r\n\r\n  detectCountingErrors(gameData) {\r\n    return [];\r\n  }\r\n\r\n  detectSequenceErrors(gameData) {\r\n    return [];\r\n  }\r\n\r\n  detectRecognitionErrors(gameData) {\r\n    return [];\r\n  }\r\n\r\n  detectEstimationErrors(gameData) {\r\n    return [];\r\n  }\r\n\r\n  categorizeErrors(errorMetrics) {\r\n    // Categorizar erros por tipo\r\n  }\r\n\r\n  /**\r\n   * Coleta erros de contagem\r\n   */\r\n  collectCountingError(correctCount, providedCount, context) {\r\n    const errorKey = `${correctCount}->${providedCount}`;\r\n    const difference = Math.abs(correctCount - providedCount);\r\n    \r\n    const countingError = {\r\n      timestamp: new Date().toISOString(),\r\n      correctCount,\r\n      providedCount,\r\n      difference,\r\n      errorType: this.identifyCountingErrorType(correctCount, providedCount, context),\r\n      context: {\r\n        difficulty: context.difficulty || 'medium',\r\n        objectType: context.objectType || 'unknown',\r\n        arrangement: context.arrangement || 'random',\r\n        responseTime: context.responseTime || 0,\r\n        attempts: context.attempts || 1,\r\n        visualComplexity: context.visualComplexity || 'medium'\r\n      },\r\n      severity: this.calculateCountingErrorSeverity(correctCount, providedCount, context),\r\n      relativeError: difference / correctCount,\r\n      direction: providedCount > correctCount ? 'overcount' : 'undercount'\r\n    };\r\n\r\n    if (!this.errorData.countingErrors[errorKey]) {\r\n      this.errorData.countingErrors[errorKey] = [];\r\n    }\r\n    this.errorData.countingErrors[errorKey].push(countingError);\r\n\r\n    this.detectPersistentCountingError(errorKey, countingError);\r\n    this.analyzeCountingPattern(countingError);\r\n\r\n    return countingError;\r\n  }\r\n\r\n  /**\r\n   * Coleta erros de sequência numérica\r\n   */\r\n  collectSequenceError(expectedSequence, actualSequence, context) {\r\n    const sequenceError = {\r\n      timestamp: new Date().toISOString(),\r\n      expectedSequence: expectedSequence,\r\n      actualSequence: actualSequence,\r\n      errorType: this.identifySequenceErrorType(expectedSequence, actualSequence),\r\n      context: {\r\n        sequenceType: context.sequenceType || 'ascending',\r\n        difficulty: context.difficulty || 'medium'\r\n      },\r\n      severity: this.calculateSequenceErrorSeverity(expectedSequence, actualSequence, context)\r\n    };\r\n\r\n    this.errorData.sequenceErrors.push(sequenceError);\r\n    return sequenceError;\r\n  }\r\n\r\n  /**\r\n   * Identifica o tipo de erro de contagem\r\n   */\r\n  identifyCountingErrorType(correct, provided, context) {\r\n    const difference = Math.abs(correct - provided);\r\n    const relativeError = difference / correct;\r\n\r\n    if (difference === 0) return 'no_error';\r\n    if (difference === 1) return 'off_by_one';\r\n    if (relativeError < 0.2) return 'minor_miscount';\r\n    if (relativeError < 0.5) return 'moderate_miscount';\r\n    if (relativeError >= 0.5) return 'major_miscount';\r\n    \r\n    // Padrões específicos\r\n    if (provided === correct * 2) return 'double_counting';\r\n    if (provided === Math.floor(correct / 2)) return 'half_counting';\r\n    if (provided % 5 === 0 && correct % 5 !== 0) return 'rounding_to_five';\r\n    if (provided % 10 === 0 && correct % 10 !== 0) return 'rounding_to_ten';\r\n    \r\n    return 'general_miscount';\r\n  }\r\n\r\n  /**\r\n   * Identifica o tipo de erro de sequência\r\n   */\r\n  identifySequenceErrorType(expected, actual) {\r\n    if (!actual) return 'no_sequence';\r\n    \r\n    // Análise simplificada\r\n    return 'sequence_pattern_error';\r\n  }\r\n\r\n  /**\r\n   * Calcula a severidade do erro de contagem\r\n   */\r\n  calculateCountingErrorSeverity(targetNumber, selectedNumber, context) {\r\n    let severity = 0.5; // Base\r\n    \r\n    // Ajuste por distância numérica\r\n    const distance = Math.abs(targetNumber - selectedNumber);\r\n    severity += distance * 0.05; // Quanto maior a distância, maior a severidade\r\n    \r\n    // Limitar o efeito da distância\r\n    if (severity > 0.8) severity = 0.8;\r\n    \r\n    // Ajuste por tempo de resposta\r\n    if (context.responseTime > 5000) severity += 0.1; // Muito lento\r\n    if (context.responseTime < 500) severity += 0.1; // Muito rápido, pode indicar impulsividade\r\n    \r\n    // Ajuste por dificuldade\r\n    if (context.difficulty === 'hard') severity -= 0.1; // Mais compreensível errar em níveis difíceis\r\n    \r\n    return Math.min(Math.max(severity, 0), 1); // Limitar entre 0 e 1\r\n  }\r\n\r\n  /**\r\n   * Calcula a severidade do erro de sequência\r\n   */\r\n  calculateSequenceErrorSeverity(expected, actual, context) {\r\n    // Implementação simplificada\r\n    return 0.6;\r\n  }\r\n\r\n  /**\r\n   * Salva dados coletados para análise futura\r\n   */\r\n  saveCollectedData(gameData, errorMetrics, errors) {\r\n    const collectedMetric = {\r\n      timestamp: Date.now(),\r\n      type: 'error_pattern',\r\n      gameType: 'ContagemNumeros',\r\n      data: errorMetrics,\r\n      errors: errors,\r\n      sessionData: {\r\n        sessionId: gameData.sessionId,\r\n        level: gameData.level || 1,\r\n        attempt: gameData.attempt || 1\r\n      }\r\n    };\r\n\r\n    this.collectedData.push(collectedMetric);\r\n  }\r\n\r\n  /**\r\n   * Gera métricas de erro com base nos dados coletados\r\n   */\r\n  generateErrorMetrics(gameData) {\r\n    const countingErrorCount = Object.values(this.errorData.countingErrors).reduce(\r\n      (total, errors) => total + errors.length, 0\r\n    );\r\n    \r\n    return {\r\n      totalErrors: countingErrorCount + this.errorData.sequenceErrors.length,\r\n      uniqueCountingErrors: Object.keys(this.errorData.countingErrors).length,\r\n      mostCommonError: this.findMostCommonError(),\r\n      averageSeverity: this.calculateAverageSeverity(),\r\n      mathematicalUnderstandingScore: this.calculateMathematicalUnderstandingScore(gameData),\r\n      numericalProcessingScore: this.calculateNumericalProcessingScore(gameData),\r\n      improvement: this.calculateImprovementMetric(gameData)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Encontra o erro mais comum\r\n   */\r\n  findMostCommonError() {\r\n    let maxCount = 0;\r\n    let mostCommonError = null;\r\n    \r\n    Object.entries(this.errorData.countingErrors).forEach(([errorKey, errors]) => {\r\n      if (errors.length > maxCount) {\r\n        maxCount = errors.length;\r\n        mostCommonError = errorKey;\r\n      }\r\n    });\r\n    \r\n    return {\r\n      error: mostCommonError,\r\n      count: maxCount\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calcula a severidade média dos erros\r\n   */\r\n  calculateAverageSeverity() {\r\n    let totalSeverity = 0;\r\n    let errorCount = 0;\r\n    \r\n    Object.values(this.errorData.countingErrors).forEach(errors => {\r\n      errors.forEach(error => {\r\n        totalSeverity += error.severity;\r\n        errorCount++;\r\n      });\r\n    });\r\n    \r\n    this.errorData.sequenceErrors.forEach(error => {\r\n      totalSeverity += error.severity;\r\n      errorCount++;\r\n    });\r\n    \r\n    return errorCount > 0 ? totalSeverity / errorCount : 0;\r\n  }\r\n\r\n  /**\r\n   * Calcula pontuação de compreensão matemática\r\n   */\r\n  calculateMathematicalUnderstandingScore(gameData) {\r\n    // Implementação simplificada\r\n    return 0.7;\r\n  }\r\n\r\n  /**\r\n   * Calcula pontuação de processamento numérico\r\n   */\r\n  calculateNumericalProcessingScore(gameData) {\r\n    // Implementação simplificada\r\n    return 0.6;\r\n  }\r\n\r\n  /**\r\n   * Calcula métrica de melhoria ao longo do tempo\r\n   */\r\n  calculateImprovementMetric(gameData) {\r\n    // Implementação simplificada\r\n    return 0.5;\r\n  }\r\n\r\n  /**\r\n   * Método de análise para compatibilidade com outros coletores\r\n   */\r\n  analyze(gameData) {\r\n    return this.collect(gameData);\r\n  }\r\n\r\n  /**\r\n   * Detecta padrões persistentes de contagem\r\n   */\r\n  detectPersistentCountingError(errorKey, errorData) {\r\n    if (!this.errorData.persistentErrors[errorKey]) {\r\n      this.errorData.persistentErrors[errorKey] = [];\r\n    }\r\n\r\n    this.errorData.persistentErrors[errorKey].push(errorData);\r\n\r\n    if (this.errorData.persistentErrors[errorKey].length >= this.errorThresholds.persistent) {\r\n      errorData.isPersistent = true;\r\n      this.flagForIntervention(errorKey, 'counting_error');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Analisa padrões de contagem\r\n   */\r\n  analyzeCountingPattern(countingError) {\r\n    // Detectar tendência de super ou sub-contagem\r\n    const recentCountingErrors = Object.values(this.errorData.countingErrors)\r\n      .flat()\r\n      .slice(-5);\r\n    \r\n    const overCountTrend = recentCountingErrors.filter(e => e.direction === 'overcount').length;\r\n    const underCountTrend = recentCountingErrors.filter(e => e.direction === 'undercount').length;\r\n    \r\n    if (overCountTrend >= 3) {\r\n      countingError.patternDetected = 'consistent_overcounting';\r\n    } else if (underCountTrend >= 3) {\r\n      countingError.patternDetected = 'consistent_undercounting';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Detecta padrões de sequência\r\n   */\r\n  detectSequencePattern(sequenceError) {\r\n    const recentSequenceErrors = this.errorData.sequenceErrors.slice(-5);\r\n    \r\n    // Verificar se há posições de erro consistentes\r\n    const errorPositions = recentSequenceErrors\r\n      .flatMap(e => e.errorPositions.map(ep => ep.position));\r\n    \r\n    const positionCounts = {};\r\n    errorPositions.forEach(pos => {\r\n      positionCounts[pos] = (positionCounts[pos] || 0) + 1;\r\n    });\r\n    \r\n    const frequentPosition = Object.entries(positionCounts)\r\n      .find(([pos, count]) => count >= 3);\r\n    \r\n    if (frequentPosition) {\r\n      sequenceError.patternDetected = `consistent_error_at_position_${frequentPosition[0]}`;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Detecta padrões de reconhecimento\r\n   */\r\n  detectRecognitionPattern(recognitionError) {\r\n    const recentRecognitionErrors = this.errorData.numberRecognitionErrors.slice(-5);\r\n    \r\n    // Verificar confusões consistentes\r\n    const confusionTypes = recentRecognitionErrors.map(e => e.confusionType);\r\n    const visualSimilarityErrors = confusionTypes.filter(t => t === 'visual_similarity').length;\r\n    \r\n    if (visualSimilarityErrors >= 3) {\r\n      recognitionError.patternDetected = 'consistent_visual_confusion';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Analisa padrões de estimativa\r\n   */\r\n  analyzeEstimationPattern(estimationError) {\r\n    const recentEstimationErrors = this.errorData.quantityEstimationErrors.slice(-5);\r\n    \r\n    // Verificar estratégias consistentes\r\n    const strategies = recentEstimationErrors.map(e => e.estimationStrategy);\r\n    const strategyCounts = {};\r\n    strategies.forEach(strategy => {\r\n      strategyCounts[strategy] = (strategyCounts[strategy] || 0) + 1;\r\n    });\r\n    \r\n    const dominantStrategy = Object.entries(strategyCounts)\r\n      .find(([strategy, count]) => count >= 3);\r\n    \r\n    if (dominantStrategy) {\r\n      estimationError.patternDetected = `consistent_${dominantStrategy[0]}`;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Analisa padrões de erro coletados\r\n   */\r\n\r\n  /**\r\n   * Avalia conceitos matemáticos\r\n   */\r\n  assessMathematicalConcepts() {\r\n    return {\r\n      counting: this.assessCountingSkills(),\r\n      numberRecognition: this.assessNumberRecognitionSkills(),\r\n      sequencing: this.assessSequencingSkills(),\r\n      quantityEstimation: this.assessQuantityEstimationSkills(),\r\n      placeValue: this.assessPlaceValueUnderstanding()\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Avalia habilidades de contagem\r\n   */\r\n  assessCountingSkills() {\r\n    const countingErrors = Object.values(this.errorData.countingErrors).flat();\r\n    if (countingErrors.length === 0) return { level: 'proficient', confidence: 1.0 };\r\n    \r\n    const averageSeverity = countingErrors.reduce((sum, e) => sum + e.severity, 0) / countingErrors.length;\r\n    const offByOneErrors = countingErrors.filter(e => e.errorType === 'off_by_one').length;\r\n    const majorErrors = countingErrors.filter(e => e.errorType === 'major_miscount').length;\r\n    \r\n    let level = 'developing';\r\n    let confidence = 0.5;\r\n    \r\n    if (averageSeverity < 0.3 && majorErrors === 0) {\r\n      level = 'proficient';\r\n      confidence = 0.8;\r\n    } else if (averageSeverity < 0.6 && majorErrors <= 1) {\r\n      level = 'emerging';\r\n      confidence = 0.6;\r\n    } else {\r\n      level = 'needs_support';\r\n      confidence = Math.max(0.2, 1 - averageSeverity);\r\n    }\r\n    \r\n    return { level, confidence, details: { offByOneErrors, majorErrors, averageSeverity } };\r\n  }\r\n\r\n  /**\r\n   * Avalia habilidades de reconhecimento numérico\r\n   */\r\n  assessNumberRecognitionSkills() {\r\n    const recognitionErrors = this.errorData.numberRecognitionErrors;\r\n    if (recognitionErrors.length === 0) return { level: 'proficient', confidence: 1.0 };\r\n    \r\n    const visualConfusions = recognitionErrors.filter(e => e.confusionType === 'visual_similarity').length;\r\n    const averageSeverity = recognitionErrors.reduce((sum, e) => sum + e.severity, 0) / recognitionErrors.length;\r\n    \r\n    let level = 'developing';\r\n    let confidence = 0.5;\r\n    \r\n    if (averageSeverity < 0.3) {\r\n      level = 'proficient';\r\n      confidence = 0.8;\r\n    } else if (visualConfusions > recognitionErrors.length * 0.5) {\r\n      level = 'visual_processing_support';\r\n      confidence = 0.4;\r\n    } else {\r\n      level = 'needs_support';\r\n      confidence = Math.max(0.2, 1 - averageSeverity);\r\n    }\r\n    \r\n    return { level, confidence, details: { visualConfusions, averageSeverity } };\r\n  }\r\n\r\n  /**\r\n   * Avalia habilidades de sequenciamento\r\n   */\r\n  assessSequencingSkills() {\r\n    const sequenceErrors = this.errorData.sequenceErrors;\r\n    if (sequenceErrors.length === 0) return { level: 'proficient', confidence: 1.0 };\r\n    \r\n    const incompleteSequences = sequenceErrors.filter(e => e.errorType === 'incomplete_sequence').length;\r\n    const averageSeverity = sequenceErrors.reduce((sum, e) => sum + e.severity, 0) / sequenceErrors.length;\r\n    \r\n    let level = 'developing';\r\n    let confidence = 0.5;\r\n    \r\n    if (averageSeverity < 0.4) {\r\n      level = 'proficient';\r\n      confidence = 0.8;\r\n    } else if (incompleteSequences > sequenceErrors.length * 0.6) {\r\n      level = 'sequence_completion_support';\r\n      confidence = 0.4;\r\n    } else {\r\n      level = 'needs_support';\r\n      confidence = Math.max(0.2, 1 - averageSeverity);\r\n    }\r\n    \r\n    return { level, confidence, details: { incompleteSequences, averageSeverity } };\r\n  }\r\n\r\n  /**\r\n   * Avalia habilidades de estimativa de quantidade\r\n   */\r\n  assessQuantityEstimationSkills() {\r\n    const estimationErrors = this.errorData.quantityEstimationErrors;\r\n    if (estimationErrors.length === 0) return { level: 'proficient', confidence: 1.0 };\r\n    \r\n    const accurateEstimations = estimationErrors.filter(e => e.estimationStrategy === 'accurate_subitizing').length;\r\n    const averageRelativeError = estimationErrors.reduce((sum, e) => sum + e.relativeError, 0) / estimationErrors.length;\r\n    \r\n    let level = 'developing';\r\n    let confidence = 0.5;\r\n    \r\n    if (averageRelativeError < 0.2) {\r\n      level = 'proficient';\r\n      confidence = 0.9;\r\n    } else if (accurateEstimations > estimationErrors.length * 0.3) {\r\n      level = 'emerging';\r\n      confidence = 0.7;\r\n    } else {\r\n      level = 'needs_support';\r\n      confidence = Math.max(0.2, 1 - averageRelativeError);\r\n    }\r\n    \r\n    return { level, confidence, details: { accurateEstimations, averageRelativeError } };\r\n  }\r\n\r\n  /**\r\n   * Avalia compreensão de valor posicional\r\n   */\r\n  assessPlaceValueUnderstanding() {\r\n    const recognitionErrors = this.errorData.numberRecognitionErrors;\r\n    const digitReversals = recognitionErrors.filter(e => e.confusionType === 'digit_reversal').length;\r\n    const digitOmissions = recognitionErrors.filter(e => e.confusionType === 'digit_omission').length;\r\n    \r\n    let level = 'developing';\r\n    let confidence = 0.6;\r\n    \r\n    if (digitReversals === 0 && digitOmissions === 0) {\r\n      level = 'proficient';\r\n      confidence = 0.8;\r\n    } else if (digitReversals > 2 || digitOmissions > 2) {\r\n      level = 'needs_support';\r\n      confidence = 0.3;\r\n    }\r\n    \r\n    return { level, confidence, details: { digitReversals, digitOmissions } };\r\n  }\r\n\r\n  /**\r\n   * Gera métricas de erro estruturadas\r\n   */\r\n\r\n  // Métodos auxiliares para métricas\r\n  getMostCommonCountingError() {\r\n    const allErrors = Object.values(this.errorData.countingErrors).flat();\r\n    const errorTypes = {};\r\n    \r\n    allErrors.forEach(error => {\r\n      errorTypes[error.errorType] = (errorTypes[error.errorType] || 0) + 1;\r\n    });\r\n    \r\n    return Object.entries(errorTypes).reduce((max, [type, count]) => \r\n      count > max.count ? { type, count } : max, \r\n      { type: null, count: 0 }\r\n    );\r\n  }\r\n\r\n  getAverageCountingErrorSeverity() {\r\n    const allErrors = Object.values(this.errorData.countingErrors).flat();\r\n    if (allErrors.length === 0) return 0;\r\n    \r\n    return allErrors.reduce((sum, error) => sum + error.severity, 0) / allErrors.length;\r\n  }\r\n\r\n  getCountingErrorTypes() {\r\n    const allErrors = Object.values(this.errorData.countingErrors).flat();\r\n    const errorTypes = {};\r\n    \r\n    allErrors.forEach(error => {\r\n      errorTypes[error.errorType] = (errorTypes[error.errorType] || 0) + 1;\r\n    });\r\n    \r\n    return errorTypes;\r\n  }\r\n\r\n  getAverageSequenceErrorSeverity() {\r\n    if (this.errorData.sequenceErrors.length === 0) return 0;\r\n    \r\n    return this.errorData.sequenceErrors.reduce((sum, error) => sum + error.severity, 0) / \r\n           this.errorData.sequenceErrors.length;\r\n  }\r\n\r\n  getCommonSequenceErrorPositions() {\r\n    const positions = {};\r\n    \r\n    if (!this.errorData.sequenceErrors || this.errorData.sequenceErrors.length === 0) {\r\n      return [];\r\n    }\r\n    \r\n    this.errorData.sequenceErrors.forEach(error => {\r\n      if (error.errorPositions && Array.isArray(error.errorPositions)) {\r\n        error.errorPositions.forEach(ep => {\r\n          positions[ep.position] = (positions[ep.position] || 0) + 1;\r\n        });\r\n      } else if (error.errorPosition) {\r\n        // Se não for um array mas tiver uma posição única\r\n        const position = error.errorPosition;\r\n        positions[position] = (positions[position] || 0) + 1;\r\n      }\r\n    });\r\n    \r\n    return Object.entries(positions)\r\n      .sort(([,a], [,b]) => b - a)\r\n      .slice(0, 3)\r\n      .map(([position, count]) => ({ position: parseInt(position), count }));\r\n  }\r\n\r\n  getAverageRecognitionErrorSeverity() {\r\n    if (this.errorData.numberRecognitionErrors.length === 0) return 0;\r\n    \r\n    return this.errorData.numberRecognitionErrors.reduce((sum, error) => sum + error.severity, 0) / \r\n           this.errorData.numberRecognitionErrors.length;\r\n  }\r\n\r\n  getRecognitionConfusionTypes() {\r\n    const confusionTypes = {};\r\n    \r\n    this.errorData.numberRecognitionErrors.forEach(error => {\r\n      confusionTypes[error.confusionType] = (confusionTypes[error.confusionType] || 0) + 1;\r\n    });\r\n    \r\n    return confusionTypes;\r\n  }\r\n\r\n  getAverageEstimationError() {\r\n    if (this.errorData.quantityEstimationErrors.length === 0) return 0;\r\n    \r\n    return this.errorData.quantityEstimationErrors.reduce((sum, error) => sum + error.relativeError, 0) / \r\n           this.errorData.quantityEstimationErrors.length;\r\n  }\r\n\r\n  getEstimationStrategies() {\r\n    const strategies = {};\r\n    \r\n    this.errorData.quantityEstimationErrors.forEach(error => {\r\n      strategies[error.estimationStrategy] = (strategies[error.estimationStrategy] || 0) + 1;\r\n    });\r\n    \r\n    return strategies;\r\n  }\r\n\r\n  generateLearningIndicators() {\r\n    return {\r\n      adaptationRate: this.calculateAdaptationRate(),\r\n      errorRecovery: this.calculateErrorRecoveryRate(),\r\n      conceptualGrowth: this.calculateConceptualGrowth(),\r\n      consistencyScore: this.calculateConsistencyScore()\r\n    };\r\n  }\r\n\r\n  calculateAdaptationRate() {\r\n    const errorsBySlice = this.getErrorsByTimeSlices(10);\r\n    if (errorsBySlice.length < 3) return 0;\r\n    \r\n    const firstHalf = errorsBySlice.slice(0, 5).reduce((sum, count) => sum + count, 0);\r\n    const secondHalf = errorsBySlice.slice(5).reduce((sum, count) => sum + count, 0);\r\n    \r\n    if (firstHalf === 0) return 1;\r\n    return Math.max(0, (firstHalf - secondHalf) / firstHalf);\r\n  }\r\n\r\n  calculateErrorRecoveryRate() {\r\n    const allErrors = this.getAllErrors();\r\n    let recoveryCount = 0;\r\n    let totalErrors = 0;\r\n    \r\n    for (let i = 0; i < allErrors.length - 1; i++) {\r\n      totalErrors++;\r\n      if (allErrors[i + 1].severity < allErrors[i].severity) {\r\n        recoveryCount++;\r\n      }\r\n    }\r\n    \r\n    return totalErrors > 0 ? recoveryCount / totalErrors : 0;\r\n  }\r\n\r\n  calculateConceptualGrowth() {\r\n    const conceptAssessments = this.assessMathematicalConcepts();\r\n    const averageConfidence = Object.values(conceptAssessments)\r\n      .reduce((sum, assessment) => sum + assessment.confidence, 0) / \r\n      Object.keys(conceptAssessments).length;\r\n    \r\n    return averageConfidence;\r\n  }\r\n\r\n  calculateConsistencyScore() {\r\n    const allErrors = this.getAllErrors();\r\n    if (allErrors.length < 2) return 1;\r\n    \r\n    const severities = allErrors.map(e => e.severity || 0);\r\n    const mean = severities.reduce((sum, s) => sum + s, 0) / severities.length;\r\n    const variance = severities.reduce((sum, s) => sum + Math.pow(s - mean, 2), 0) / severities.length;\r\n    const standardDeviation = Math.sqrt(variance);\r\n    \r\n    return Math.max(0, 1 - (standardDeviation / (mean + 0.1)));\r\n  }\r\n\r\n  // Métodos auxiliares gerais\r\n  flagForIntervention(errorKey, errorType) {\r\n    console.warn(`Padrão numérico persistente detectado: ${errorKey} (${errorType})`);\r\n  }\r\n\r\n  getTotalErrorCount() {\r\n    const countingErrors = Object.values(this.errorData.countingErrors).flat().length;\r\n    return countingErrors + \r\n           this.errorData.sequenceErrors.length + \r\n           this.errorData.numberRecognitionErrors.length + \r\n           this.errorData.quantityEstimationErrors.length;\r\n  }\r\n\r\n  identifyPersistentPatterns() {\r\n    return Object.entries(this.errorData.persistentErrors)\r\n      .filter(([key, errors]) => errors.length >= this.errorThresholds.persistent)\r\n      .map(([key, errors]) => ({\r\n        pattern: key,\r\n        frequency: errors.length,\r\n        severity: errors.reduce((sum, e) => sum + e.severity, 0) / errors.length\r\n      }));\r\n  }\r\n\r\n  assessLearningProgress() {\r\n    const recentErrors = this.getTimeWindowErrors(300000);\r\n    const olderErrors = this.getTimeWindowErrors(600000, 300000);\r\n    \r\n    return {\r\n      errorReduction: olderErrors.length > 0 ? \r\n        (olderErrors.length - recentErrors.length) / olderErrors.length : 0,\r\n      improvementTrend: this.calculateImprovementTrend(),\r\n      learningRate: this.calculateLearningRate()\r\n    };\r\n  }\r\n\r\n  generateInterventionRecommendations() {\r\n    const recommendations = [];\r\n    const conceptAssessments = this.assessMathematicalConcepts();\r\n    \r\n    // Recomendações baseadas em conceitos matemáticos\r\n    Object.entries(conceptAssessments).forEach(([concept, assessment]) => {\r\n      if (assessment.confidence < 0.5) {\r\n        recommendations.push({\r\n          type: `${concept}_support`,\r\n          priority: assessment.confidence < 0.3 ? 'high' : 'medium',\r\n          description: `Suporte adicional necessário em ${concept}`,\r\n          details: assessment.details\r\n        });\r\n      }\r\n    });\r\n    \r\n    // Recomendações baseadas em padrões persistentes\r\n    const persistentPatterns = this.identifyPersistentPatterns();\r\n    persistentPatterns.forEach(pattern => {\r\n      recommendations.push({\r\n        type: 'targeted_practice',\r\n        priority: pattern.severity > 0.7 ? 'high' : 'medium',\r\n        description: `Prática direcionada para: ${pattern.pattern}`,\r\n        frequency: pattern.frequency\r\n      });\r\n    });\r\n    \r\n    return recommendations;\r\n  }\r\n\r\n  calculateOverallSeverity() {\r\n    const allErrors = this.getAllErrors();\r\n    if (allErrors.length === 0) return 0;\r\n    \r\n    const totalSeverity = allErrors.reduce((sum, error) => sum + (error.severity || 0), 0);\r\n    return totalSeverity / allErrors.length;\r\n  }\r\n\r\n  calculateImprovementTrend() {\r\n    const errorsByTime = this.getErrorsByTimeSlices(5);\r\n    if (errorsByTime.length < 2) return 0;\r\n    \r\n    let improvements = 0;\r\n    for (let i = 1; i < errorsByTime.length; i++) {\r\n      if (errorsByTime[i] < errorsByTime[i-1]) improvements++;\r\n    }\r\n    \r\n    return improvements / (errorsByTime.length - 1);\r\n  }\r\n\r\n  calculateLearningRate() {\r\n    const totalTime = Date.now() - this.sessionStartTime;\r\n    const totalErrors = this.getTotalErrorCount();\r\n    \r\n    if (totalTime === 0) return 0;\r\n    return Math.max(0, 1 - (totalErrors / (totalTime / 60000)));\r\n  }\r\n\r\n  getAllErrors() {\r\n    const countingErrors = Object.values(this.errorData.countingErrors).flat();\r\n    return [\r\n      ...countingErrors,\r\n      ...this.errorData.sequenceErrors,\r\n      ...this.errorData.numberRecognitionErrors,\r\n      ...this.errorData.quantityEstimationErrors\r\n    ];\r\n  }\r\n\r\n  getTimeWindowErrors(windowMs, offsetMs = 0) {\r\n    const now = Date.now();\r\n    const startTime = now - windowMs - offsetMs;\r\n    const endTime = now - offsetMs;\r\n    \r\n    return this.getAllErrors().filter(error => {\r\n      const errorTime = new Date(error.timestamp).getTime();\r\n      return errorTime >= startTime && errorTime <= endTime;\r\n    });\r\n  }\r\n\r\n  getErrorsByTimeSlices(slices) {\r\n    const sessionDuration = Date.now() - this.sessionStartTime;\r\n    const sliceDuration = sessionDuration / slices;\r\n    const sliceErrors = [];\r\n    \r\n    for (let i = 0; i < slices; i++) {\r\n      const sliceStart = this.sessionStartTime + (i * sliceDuration);\r\n      const sliceEnd = sliceStart + sliceDuration;\r\n      \r\n      const errorsInSlice = this.getAllErrors().filter(error => {\r\n        const errorTime = new Date(error.timestamp).getTime();\r\n        return errorTime >= sliceStart && errorTime < sliceEnd;\r\n      });\r\n      \r\n      sliceErrors.push(errorsInSlice.length);\r\n    }\r\n    \r\n    return sliceErrors;\r\n  }\r\n\r\n  /**\r\n   * Reset dos dados de erro\r\n   */\r\n  reset() {\r\n    this.errorData = {\r\n      countingErrors: {},\r\n      sequenceErrors: [],\r\n      numberRecognitionErrors: [],\r\n      quantityEstimationErrors: [],\r\n      persistentErrors: {},\r\n      errorClusters: [],\r\n      learningIndicators: [],\r\n      mathematicalConcepts: {}\r\n    };\r\n    this.sessionStartTime = Date.now();\r\n  }\r\n\r\n  /**\r\n   * Exporta dados para análise externa\r\n   */\r\n  exportData() {\r\n    return {\r\n      ...this.errorData,\r\n      sessionDuration: Date.now() - this.sessionStartTime,\r\n      analysis: this.analyzeErrorPatterns(),\r\n      metrics: this.generateErrorMetrics()\r\n    };\r\n  }\r\n}\r\n\r\nexport default ErrorPatternCollector;\r\n", "/**\r\n * 🎯 ESTIMATION SKILLS COLLECTOR V3\r\n * Coletor especializado em análise de habilidades de estimativa numérica\r\n * Portal Betina V3\r\n */\r\n\r\nexport class EstimationSkillsCollector {\r\n  constructor() {\r\n    this.estimationThresholds = {\r\n      excellent: 0.95,  // Estimativas quase perfeitas\r\n      good: 0.85,       // Boa capacidade de estimativa\r\n      average: 0.70,    // Estimativas razoáveis\r\n      poor: 0.50,       // Dificuldade em estimar\r\n      critical: 0.30    // Estimativas muito distantes\r\n    };\r\n    \r\n    this.estimationSkills = {\r\n      visualEstimation: 'Estimativa visual de quantidades',\r\n      numericalApproximation: 'Aproximação numérica sem contagem',\r\n      magnitudeComparison: 'Comparação de magnitudes',\r\n      quantityPerception: 'Percepção de quantidade',\r\n      spatialNumerosity: 'Numerosidade espacial'\r\n    };\r\n    \r\n    this.toleranceByDifficulty = {\r\n      easy: 1,    // ±1 para números de 1-5\r\n      medium: 1,  // ±1 para números de 3-8\r\n      hard: 2     // ±2 para números de 6-12\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Método padronizado de coleta de dados\r\n   */\r\n  collect(data) {\r\n    return this.analyze(data);\r\n  }\r\n  \r\n  /**\r\n   * Análise principal das habilidades de estimativa\r\n   */\r\n  async analyze(data) {\r\n    if (!data || !data.numberEstimation) {\r\n      console.warn('EstimationSkillsCollector: Dados de estimativa não encontrados');\r\n      return this.getDefaultAnalysis();\r\n    }\r\n\r\n    const estimationData = data.numberEstimation;\r\n    \r\n    // Calcular precisão das estimativas\r\n    const estimationAccuracy = this.calculateEstimationAccuracy(estimationData);\r\n    \r\n    // Analisar padrões de erro\r\n    const errorPatterns = this.analyzeErrorPatterns(estimationData);\r\n    \r\n    // Avaliar progresso temporal\r\n    const temporalProgress = this.analyzeTemporalProgress(estimationData);\r\n    \r\n    // Detectar estratégias de estimativa\r\n    const estimationStrategies = this.detectEstimationStrategies(estimationData);\r\n    \r\n    // Avaliar confiança nas estimativas\r\n    const confidenceLevel = this.assessConfidenceLevel(estimationData);\r\n    \r\n    // Calcular índice de habilidade geral\r\n    const skillIndex = this.calculateSkillIndex(estimationData);\r\n\r\n    const analysis = {\r\n      timestamp: new Date().toISOString(),\r\n      collector: 'EstimationSkillsCollector',\r\n      version: '3.0.0',\r\n      \r\n      // Métricas principais\r\n      estimationAccuracy,\r\n      skillIndex,\r\n      confidenceLevel,\r\n      \r\n      // Análises detalhadas\r\n      errorPatterns,\r\n      temporalProgress,\r\n      estimationStrategies,\r\n      \r\n      // Habilidades específicas\r\n      skillAssessment: {\r\n        visualEstimation: this.assessVisualEstimation(estimationData),\r\n        numericalApproximation: this.assessNumericalApproximation(estimationData),\r\n        magnitudeComparison: this.assessMagnitudeComparison(estimationData),\r\n        quantityPerception: this.assessQuantityPerception(estimationData),\r\n        spatialNumerosity: this.assessSpatialNumerosity(estimationData)\r\n      },\r\n      \r\n      // Recomendações\r\n      recommendations: this.generateRecommendations(estimationData, skillIndex),\r\n      \r\n      // Metadados\r\n      metadata: {\r\n        totalAttempts: estimationData.length || 0,\r\n        validAttempts: estimationData.filter(attempt => attempt.isValid).length || 0,\r\n        averageError: this.calculateAverageError(estimationData),\r\n        maxError: this.calculateMaxError(estimationData),\r\n        minError: this.calculateMinError(estimationData)\r\n      }\r\n    };\r\n\r\n    return analysis;\r\n  }\r\n\r\n  /**\r\n   * Calcular precisão das estimativas\r\n   */\r\n  calculateEstimationAccuracy(data) {\r\n    if (!data || data.length === 0) return 0.5;\r\n    \r\n    let correctEstimations = 0;\r\n    \r\n    data.forEach(attempt => {\r\n      if (attempt.userEstimate && attempt.actualCount) {\r\n        const error = Math.abs(attempt.userEstimate - attempt.actualCount);\r\n        const tolerance = this.toleranceByDifficulty[attempt.difficulty] || 1;\r\n        \r\n        if (error <= tolerance) {\r\n          correctEstimations++;\r\n        }\r\n      }\r\n    });\r\n    \r\n    return data.length > 0 ? correctEstimations / data.length : 0.5;\r\n  }\r\n\r\n  /**\r\n   * Analisar padrões de erro\r\n   */\r\n  analyzeErrorPatterns(data) {\r\n    const errors = [];\r\n    const biases = {\r\n      overestimation: 0, // Tendência a superestimar\r\n      underestimation: 0, // Tendência a subestimar\r\n      systematic: false,  // Erro sistemático\r\n      random: false       // Erro aleatório\r\n    };\r\n\r\n    data.forEach(attempt => {\r\n      if (attempt.userEstimate && attempt.actualCount) {\r\n        const error = attempt.userEstimate - attempt.actualCount;\r\n        errors.push(error);\r\n        \r\n        if (error > 0) biases.overestimation++;\r\n        else if (error < 0) biases.underestimation++;\r\n      }\r\n    });\r\n\r\n    // Detectar viés sistemático\r\n    const totalAttempts = data.length;\r\n    if (totalAttempts > 0) {\r\n      const overestimationRate = biases.overestimation / totalAttempts;\r\n      const underestimationRate = biases.underestimation / totalAttempts;\r\n      \r\n      biases.systematic = Math.max(overestimationRate, underestimationRate) > 0.7;\r\n      biases.random = Math.abs(overestimationRate - underestimationRate) < 0.2;\r\n    }\r\n\r\n    return {\r\n      averageError: errors.length > 0 ? errors.reduce((a, b) => a + b, 0) / errors.length : 0,\r\n      errorVariance: this.calculateVariance(errors),\r\n      biases,\r\n      errorDistribution: this.analyzeErrorDistribution(errors)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Avaliar progresso temporal\r\n   */\r\n  analyzeTemporalProgress(data) {\r\n    if (data.length < 3) return { trend: 'insufficient_data', improvement: 0 };\r\n    \r\n    const firstHalf = data.slice(0, Math.floor(data.length / 2));\r\n    const secondHalf = data.slice(Math.floor(data.length / 2));\r\n    \r\n    const firstAccuracy = this.calculateEstimationAccuracy(firstHalf);\r\n    const secondAccuracy = this.calculateEstimationAccuracy(secondHalf);\r\n    \r\n    const improvement = secondAccuracy - firstAccuracy;\r\n    \r\n    let trend = 'stable';\r\n    if (improvement > 0.1) trend = 'improving';\r\n    else if (improvement < -0.1) trend = 'declining';\r\n    \r\n    return {\r\n      trend,\r\n      improvement,\r\n      firstHalfAccuracy: firstAccuracy,\r\n      secondHalfAccuracy: secondAccuracy,\r\n      consistencyScore: this.calculateConsistency(data)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Detectar estratégias de estimativa\r\n   */\r\n  detectEstimationStrategies(data) {\r\n    const strategies = {\r\n      visualChunking: false,    // Agrupamento visual\r\n      roundNumbers: false,      // Uso de números redondos\r\n      anchoring: false,         // Ancoragem em números conhecidos\r\n      systematicCounting: false // Contagem parcial sistemática\r\n    };\r\n\r\n    // Detectar uso de números redondos\r\n    const roundNumberUsage = data.filter(attempt => \r\n      attempt.userEstimate && attempt.userEstimate % 5 === 0\r\n    ).length / data.length;\r\n    \r\n    strategies.roundNumbers = roundNumberUsage > 0.6;\r\n\r\n    // Detectar ancoragem (estimativas sempre próximas de valores específicos)\r\n    const estimates = data.map(attempt => attempt.userEstimate).filter(Boolean);\r\n    const mostCommonEstimate = this.findMostCommon(estimates);\r\n    const anchoringRate = estimates.filter(est => Math.abs(est - mostCommonEstimate) <= 2).length / estimates.length;\r\n    \r\n    strategies.anchoring = anchoringRate > 0.5;\r\n\r\n    return strategies;\r\n  }\r\n\r\n  /**\r\n   * Avaliar nível de confiança\r\n   */\r\n  assessConfidenceLevel(data) {\r\n    // Baseado na consistência das estimativas e tempo de resposta\r\n    const responseTimes = data.map(attempt => attempt.responseTime).filter(Boolean);\r\n    const averageTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length || 0;\r\n    \r\n    const consistency = this.calculateConsistency(data);\r\n    const timeConfidence = averageTime < 5000 ? 0.8 : averageTime < 8000 ? 0.6 : 0.4;\r\n    \r\n    return (consistency + timeConfidence) / 2;\r\n  }\r\n\r\n  /**\r\n   * Calcular índice de habilidade geral\r\n   */\r\n  calculateSkillIndex(data) {\r\n    const accuracy = this.calculateEstimationAccuracy(data);\r\n    const consistency = this.calculateConsistency(data);\r\n    const confidence = this.assessConfidenceLevel(data);\r\n    \r\n    return (accuracy * 0.5) + (consistency * 0.3) + (confidence * 0.2);\r\n  }\r\n\r\n  /**\r\n   * Avaliar estimativa visual\r\n   */\r\n  assessVisualEstimation(data) {\r\n    // Analisar performance baseada apenas na visualização\r\n    const visualAttempts = data.filter(attempt => attempt.showTime <= 4000);\r\n    return this.calculateEstimationAccuracy(visualAttempts);\r\n  }\r\n\r\n  /**\r\n   * Avaliar aproximação numérica\r\n   */\r\n  assessNumericalApproximation(data) {\r\n    // Avaliar precisão em aproximações sem contagem exata\r\n    const approximationScore = data.reduce((score, attempt) => {\r\n      if (attempt.userEstimate && attempt.actualCount) {\r\n        const error = Math.abs(attempt.userEstimate - attempt.actualCount);\r\n        const relativeError = error / attempt.actualCount;\r\n        return score + Math.max(0, 1 - relativeError);\r\n      }\r\n      return score;\r\n    }, 0);\r\n    \r\n    return data.length > 0 ? approximationScore / data.length : 0.5;\r\n  }\r\n\r\n  /**\r\n   * Avaliar comparação de magnitudes\r\n   */\r\n  assessMagnitudeComparison(data) {\r\n    // Avaliar habilidade de comparar diferentes magnitudes\r\n    const magnitudes = data.map(attempt => attempt.actualCount).filter(Boolean);\r\n    const uniqueMagnitudes = [...new Set(magnitudes)];\r\n    \r\n    if (uniqueMagnitudes.length < 2) return 0.5;\r\n    \r\n    // Performance varia com diferentes magnitudes?\r\n    const performanceByMagnitude = {};\r\n    uniqueMagnitudes.forEach(magnitude => {\r\n      const attemptsForMagnitude = data.filter(attempt => attempt.actualCount === magnitude);\r\n      performanceByMagnitude[magnitude] = this.calculateEstimationAccuracy(attemptsForMagnitude);\r\n    });\r\n    \r\n    const performances = Object.values(performanceByMagnitude);\r\n    const averagePerformance = performances.reduce((a, b) => a + b, 0) / performances.length;\r\n    \r\n    return averagePerformance;\r\n  }\r\n\r\n  /**\r\n   * Avaliar percepção de quantidade\r\n   */\r\n  assessQuantityPerception(data) {\r\n    // Avaliar precisão na percepção inicial de quantidades\r\n    const quickResponses = data.filter(attempt => attempt.responseTime < 3000);\r\n    return this.calculateEstimationAccuracy(quickResponses);\r\n  }\r\n\r\n  /**\r\n   * Avaliar numerosidade espacial\r\n   */\r\n  assessSpatialNumerosity(data) {\r\n    // Avaliar capacidade de processar numerosidade em arranjos espaciais\r\n    return this.calculateEstimationAccuracy(data); // Simplificado para esta versão\r\n  }\r\n\r\n  /**\r\n   * Gerar recomendações\r\n   */\r\n  generateRecommendations(data, skillIndex) {\r\n    const recommendations = [];\r\n    \r\n    if (skillIndex < 0.6) {\r\n      recommendations.push({\r\n        type: 'improvement',\r\n        priority: 'high',\r\n        message: 'Pratique mais atividades de estimativa com números menores',\r\n        activities: ['number_estimation_easy', 'visual_quantity_games']\r\n      });\r\n    }\r\n    \r\n    const errorPatterns = this.analyzeErrorPatterns(data);\r\n    if (errorPatterns.biases.overestimation > errorPatterns.biases.underestimation) {\r\n      recommendations.push({\r\n        type: 'strategy',\r\n        priority: 'medium',\r\n        message: 'Tente olhar mais atentamente antes de estimar - você tende a superestimar',\r\n        activities: ['careful_observation', 'comparison_games']\r\n      });\r\n    }\r\n    \r\n    if (this.assessConfidenceLevel(data) < 0.5) {\r\n      recommendations.push({\r\n        type: 'confidence',\r\n        priority: 'medium',\r\n        message: 'Confie mais nas suas primeiras impressões',\r\n        activities: ['quick_estimation', 'intuitive_counting']\r\n      });\r\n    }\r\n    \r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Funções auxiliares\r\n   */\r\n  calculateVariance(numbers) {\r\n    if (numbers.length === 0) return 0;\r\n    const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;\r\n    const variance = numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;\r\n    return variance;\r\n  }\r\n\r\n  calculateConsistency(data) {\r\n    const errors = data.map(attempt => {\r\n      if (attempt.userEstimate && attempt.actualCount) {\r\n        return Math.abs(attempt.userEstimate - attempt.actualCount);\r\n      }\r\n      return null;\r\n    }).filter(Boolean);\r\n    \r\n    if (errors.length === 0) return 0.5;\r\n    \r\n    const variance = this.calculateVariance(errors);\r\n    return Math.max(0, 1 - (variance / 10)); // Normalizado\r\n  }\r\n\r\n  analyzeErrorDistribution(errors) {\r\n    const distribution = { negative: 0, zero: 0, positive: 0 };\r\n    \r\n    errors.forEach(error => {\r\n      if (error < 0) distribution.negative++;\r\n      else if (error === 0) distribution.zero++;\r\n      else distribution.positive++;\r\n    });\r\n    \r\n    return distribution;\r\n  }\r\n\r\n  findMostCommon(array) {\r\n    const frequency = {};\r\n    array.forEach(item => frequency[item] = (frequency[item] || 0) + 1);\r\n    return Object.keys(frequency).reduce((a, b) => frequency[a] > frequency[b] ? a : b, 0);\r\n  }\r\n\r\n  calculateAverageError(data) {\r\n    const errors = data.map(attempt => {\r\n      if (attempt.userEstimate && attempt.actualCount) {\r\n        return Math.abs(attempt.userEstimate - attempt.actualCount);\r\n      }\r\n      return null;\r\n    }).filter(Boolean);\r\n    \r\n    return errors.length > 0 ? errors.reduce((a, b) => a + b, 0) / errors.length : 0;\r\n  }\r\n\r\n  calculateMaxError(data) {\r\n    const errors = data.map(attempt => {\r\n      if (attempt.userEstimate && attempt.actualCount) {\r\n        return Math.abs(attempt.userEstimate - attempt.actualCount);\r\n      }\r\n      return null;\r\n    }).filter(Boolean);\r\n    \r\n    return errors.length > 0 ? Math.max(...errors) : 0;\r\n  }\r\n\r\n  calculateMinError(data) {\r\n    const errors = data.map(attempt => {\r\n      if (attempt.userEstimate && attempt.actualCount) {\r\n        return Math.abs(attempt.userEstimate - attempt.actualCount);\r\n      }\r\n      return null;\r\n    }).filter(Boolean);\r\n    \r\n    return errors.length > 0 ? Math.min(...errors) : 0;\r\n  }\r\n\r\n  getDefaultAnalysis() {\r\n    return {\r\n      timestamp: new Date().toISOString(),\r\n      collector: 'EstimationSkillsCollector',\r\n      version: '3.0.0',\r\n      estimationAccuracy: 0.5,\r\n      skillIndex: 0.5,\r\n      confidenceLevel: 0.5,\r\n      errorPatterns: { averageError: 0, errorVariance: 0, biases: {} },\r\n      temporalProgress: { trend: 'insufficient_data', improvement: 0 },\r\n      estimationStrategies: {},\r\n      skillAssessment: {},\r\n      recommendations: [],\r\n      metadata: { totalAttempts: 0, validAttempts: 0 }\r\n    };\r\n  }\r\n}\r\n", "/**\r\n * 📝 SEQUENCE ANALYSIS COLLECTOR V3\r\n * Coletor especializado em análise de habilidades de sequência numérica\r\n * Portal Betina V3\r\n */\r\n\r\nexport class SequenceAnalysisCollector {\r\n  constructor() {\r\n    this.sequenceThresholds = {\r\n      excellent: 0.95,\r\n      good: 0.85,\r\n      average: 0.70,\r\n      poor: 0.50,\r\n      critical: 0.30\r\n    };\r\n    \r\n    this.sequenceTypes = {\r\n      ascending: 'Sequências crescentes simples',\r\n      descending: 'Sequências decrescentes',\r\n      evenNumbers: 'Sequências de números pares',\r\n      oddNumbers: 'Sequências de números ímpares',\r\n      arithmetic: 'Progressões aritméticas',\r\n      geometric: 'Progressões geométricas',\r\n      fibonacci: 'Sequências tipo Fibonacci',\r\n      complex: 'Padrões complexos'\r\n    };\r\n    \r\n    this.cognitiveSkills = {\r\n      patternRecognition: 'Reconhecimento de padrões',\r\n      logicalReasoning: 'Raciocínio lógico',\r\n      sequentialMemory: 'Memória sequencial',\r\n      abstractThinking: 'Pensamento abstrato',\r\n      ruleInference: 'Inferência de regras'\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Método padronizado de coleta de dados\r\n   */\r\n  collect(data) {\r\n    return this.analyze(data);\r\n  }\r\n  \r\n  /**\r\n   * Análise principal das habilidades de sequência\r\n   */\r\n  async analyze(data) {\r\n    if (!data || !data.sequenceCompletion) {\r\n      console.warn('SequenceAnalysisCollector: Dados de sequência não encontrados');\r\n      return this.getDefaultAnalysis();\r\n    }\r\n\r\n    const sequenceData = data.sequenceCompletion;\r\n    \r\n    // Analisar precisão por tipo de sequência\r\n    const accuracyByType = this.analyzeAccuracyByType(sequenceData);\r\n    \r\n    // Detectar estratégias de resolução\r\n    const solvingStrategies = this.detectSolvingStrategies(sequenceData);\r\n    \r\n    // Avaliar complexidade cognitiva\r\n    const cognitiveComplexity = this.assessCognitiveComplexity(sequenceData);\r\n    \r\n    // Analisar progresso temporal\r\n    const temporalProgress = this.analyzeTemporalProgress(sequenceData);\r\n    \r\n    // Detectar padrões de erro\r\n    const errorPatterns = this.analyzeErrorPatterns(sequenceData);\r\n    \r\n    // Avaliar velocidade de processamento\r\n    const processingSpeed = this.assessProcessingSpeed(sequenceData);\r\n    \r\n    // Calcular índice de habilidade sequencial\r\n    const sequentialSkillIndex = this.calculateSequentialSkillIndex(sequenceData);\r\n\r\n    const analysis = {\r\n      timestamp: new Date().toISOString(),\r\n      collector: 'SequenceAnalysisCollector',\r\n      version: '3.0.0',\r\n      \r\n      // Métricas principais\r\n      overallAccuracy: this.calculateOverallAccuracy(sequenceData),\r\n      sequentialSkillIndex,\r\n      processingSpeed,\r\n      \r\n      // Análises detalhadas\r\n      accuracyByType,\r\n      solvingStrategies,\r\n      cognitiveComplexity,\r\n      temporalProgress,\r\n      errorPatterns,\r\n      \r\n      // Habilidades cognitivas específicas\r\n      cognitiveSkills: {\r\n        patternRecognition: this.assessPatternRecognition(sequenceData),\r\n        logicalReasoning: this.assessLogicalReasoning(sequenceData),\r\n        sequentialMemory: this.assessSequentialMemory(sequenceData),\r\n        abstractThinking: this.assessAbstractThinking(sequenceData),\r\n        ruleInference: this.assessRuleInference(sequenceData)\r\n      },\r\n      \r\n      // Análise de dificuldade\r\n      difficultyAnalysis: this.analyzeDifficultyProgression(sequenceData),\r\n      \r\n      // Recomendações\r\n      recommendations: this.generateRecommendations(sequenceData, sequentialSkillIndex),\r\n      \r\n      // Metadados\r\n      metadata: {\r\n        totalSequences: sequenceData.length || 0,\r\n        uniqueTypes: this.countUniqueTypes(sequenceData),\r\n        averageLength: this.calculateAverageLength(sequenceData),\r\n        complexityDistribution: this.analyzeComplexityDistribution(sequenceData)\r\n      }\r\n    };\r\n\r\n    return analysis;\r\n  }\r\n\r\n  /**\r\n   * Analisar precisão por tipo de sequência\r\n   */\r\n  analyzeAccuracyByType(data) {\r\n    const accuracyByType = {};\r\n    \r\n    // Agrupar por tipo de sequência\r\n    const groupedData = this.groupBySequenceType(data);\r\n    \r\n    Object.keys(groupedData).forEach(type => {\r\n      const typeData = groupedData[type];\r\n      const correct = typeData.filter(attempt => attempt.isCorrect).length;\r\n      accuracyByType[type] = {\r\n        accuracy: typeData.length > 0 ? correct / typeData.length : 0,\r\n        attempts: typeData.length,\r\n        averageTime: this.calculateAverageTime(typeData),\r\n        difficulty: this.assessTypeDifficulty(typeData)\r\n      };\r\n    });\r\n    \r\n    return accuracyByType;\r\n  }\r\n\r\n  /**\r\n   * Detectar estratégias de resolução\r\n   */\r\n  detectSolvingStrategies(data) {\r\n    const strategies = {\r\n      immediateRecognition: false,  // Reconhecimento imediato do padrão\r\n      systematicAnalysis: false,    // Análise sistemática passo a passo\r\n      trialAndError: false,         // Tentativa e erro\r\n      ruleApplication: false,       // Aplicação consciente de regras\r\n      visualPattern: false          // Reconhecimento visual de padrão\r\n    };\r\n\r\n    // Detectar reconhecimento imediato (respostas muito rápidas e corretas)\r\n    const quickCorrect = data.filter(attempt => \r\n      attempt.isCorrect && attempt.responseTime < 3000\r\n    ).length;\r\n    strategies.immediateRecognition = quickCorrect / data.length > 0.6;\r\n\r\n    // Detectar análise sistemática (tempo moderado, alta precisão)\r\n    const systematicAttempts = data.filter(attempt => \r\n      attempt.responseTime >= 3000 && attempt.responseTime <= 8000 && attempt.isCorrect\r\n    ).length;\r\n    strategies.systematicAnalysis = systematicAttempts / data.length > 0.5;\r\n\r\n    // Detectar tentativa e erro (múltiplas respostas incorretas seguidas de correção)\r\n    strategies.trialAndError = this.detectTrialAndErrorPattern(data);\r\n\r\n    return strategies;\r\n  }\r\n\r\n  /**\r\n   * Avaliar complexidade cognitiva\r\n   */\r\n  assessCognitiveComplexity(data) {\r\n    const complexityScores = data.map(attempt => {\r\n      let complexity = 0;\r\n      \r\n      // Complexidade baseada no tipo de sequência\r\n      if (attempt.sequenceType) {\r\n        switch (attempt.sequenceType) {\r\n          case 'crescente_simples':\r\n          case 'decrescente_simples':\r\n            complexity += 1;\r\n            break;\r\n          case 'pares':\r\n          case 'ímpares':\r\n            complexity += 2;\r\n            break;\r\n          case 'múltiplos_3':\r\n          case 'soma_3':\r\n            complexity += 3;\r\n            break;\r\n          case 'fibonacci':\r\n          case 'multiplicação':\r\n            complexity += 4;\r\n            break;\r\n          default:\r\n            complexity += 2;\r\n        }\r\n      }\r\n      \r\n      // Complexidade baseada no comprimento da sequência\r\n      if (attempt.sequenceLength) {\r\n        complexity += Math.max(0, attempt.sequenceLength - 3);\r\n      }\r\n      \r\n      // Complexidade baseada na magnitude dos números\r\n      if (attempt.maxNumber) {\r\n        complexity += attempt.maxNumber > 10 ? 1 : 0;\r\n      }\r\n      \r\n      return {\r\n        attempt: attempt,\r\n        complexity: complexity,\r\n        solved: attempt.isCorrect\r\n      };\r\n    });\r\n\r\n    return {\r\n      averageComplexity: complexityScores.reduce((sum, item) => sum + item.complexity, 0) / complexityScores.length,\r\n      complexityVsPerformance: this.analyzeComplexityVsPerformance(complexityScores),\r\n      adaptiveCapacity: this.assessAdaptiveCapacity(complexityScores)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisar progresso temporal\r\n   */\r\n  analyzeTemporalProgress(data) {\r\n    if (data.length < 3) return { trend: 'insufficient_data', improvement: 0 };\r\n    \r\n    const segments = this.divideIntoSegments(data, 3);\r\n    const segmentAccuracies = segments.map(segment => this.calculateAccuracy(segment));\r\n    \r\n    const trend = this.calculateTrend(segmentAccuracies);\r\n    const improvement = segmentAccuracies[segmentAccuracies.length - 1] - segmentAccuracies[0];\r\n    \r\n    return {\r\n      trend,\r\n      improvement,\r\n      segmentAccuracies,\r\n      learningRate: this.calculateLearningRate(segmentAccuracies),\r\n      stability: this.calculateStability(segmentAccuracies)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisar padrões de erro\r\n   */\r\n  analyzeErrorPatterns(data) {\r\n    const errors = data.filter(attempt => !attempt.isCorrect);\r\n    \r\n    const errorTypes = {\r\n      offByOne: 0,          // Erro de ±1\r\n      patternMisunderstanding: 0, // Não entendeu o padrão\r\n      calculationError: 0,   // Erro de cálculo\r\n      randomGuess: 0         // Resposta aleatória\r\n    };\r\n\r\n    errors.forEach(error => {\r\n      if (error.userAnswer && error.correctAnswer) {\r\n        const difference = Math.abs(error.userAnswer - error.correctAnswer);\r\n        \r\n        if (difference === 1) {\r\n          errorTypes.offByOne++;\r\n        } else if (difference > 5) {\r\n          errorTypes.randomGuess++;\r\n        } else {\r\n          errorTypes.calculationError++;\r\n        }\r\n      }\r\n    });\r\n\r\n    return {\r\n      totalErrors: errors.length,\r\n      errorTypes,\r\n      errorRate: data.length > 0 ? errors.length / data.length : 0,\r\n      criticalErrors: this.identifyCriticalErrors(errors),\r\n      errorProgression: this.analyzeErrorProgression(data)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Avaliar velocidade de processamento\r\n   */\r\n  assessProcessingSpeed(data) {\r\n    const responseTimes = data.map(attempt => attempt.responseTime).filter(Boolean);\r\n    \r\n    if (responseTimes.length === 0) return { speed: 'unknown', score: 0.5 };\r\n    \r\n    const averageTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;\r\n    const medianTime = this.calculateMedian(responseTimes);\r\n    \r\n    let speedCategory = 'average';\r\n    let speedScore = 0.5;\r\n    \r\n    if (averageTime < 4000) {\r\n      speedCategory = 'fast';\r\n      speedScore = 0.8;\r\n    } else if (averageTime < 7000) {\r\n      speedCategory = 'average';\r\n      speedScore = 0.6;\r\n    } else {\r\n      speedCategory = 'slow';\r\n      speedScore = 0.4;\r\n    }\r\n    \r\n    return {\r\n      averageTime,\r\n      medianTime,\r\n      speedCategory,\r\n      speedScore,\r\n      consistency: this.calculateTimeConsistency(responseTimes)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calcular índice de habilidade sequencial\r\n   */\r\n  calculateSequentialSkillIndex(data) {\r\n    const accuracy = this.calculateOverallAccuracy(data);\r\n    const speed = this.assessProcessingSpeed(data).speedScore;\r\n    const complexity = this.assessCognitiveComplexity(data).adaptiveCapacity || 0.5;\r\n    const consistency = this.calculateConsistency(data);\r\n    \r\n    return (accuracy * 0.4) + (speed * 0.2) + (complexity * 0.2) + (consistency * 0.2);\r\n  }\r\n\r\n  /**\r\n   * Avaliar habilidades cognitivas específicas\r\n   */\r\n  assessPatternRecognition(data) {\r\n    // Capacidade de reconhecer padrões rapidamente\r\n    const quickCorrect = data.filter(attempt => \r\n      attempt.isCorrect && attempt.responseTime < 5000\r\n    ).length;\r\n    \r\n    return data.length > 0 ? quickCorrect / data.length : 0.5;\r\n  }\r\n\r\n  assessLogicalReasoning(data) {\r\n    // Capacidade de aplicar lógica a sequências complexas\r\n    const complexSequences = data.filter(attempt => \r\n      attempt.sequenceType && \r\n      ['múltiplos_3', 'soma_3', 'fibonacci', 'multiplicação'].includes(attempt.sequenceType)\r\n    );\r\n    \r\n    return this.calculateAccuracy(complexSequences);\r\n  }\r\n\r\n  assessSequentialMemory(data) {\r\n    // Capacidade de lembrar e trabalhar com sequências longas\r\n    const longSequences = data.filter(attempt => \r\n      attempt.sequenceLength && attempt.sequenceLength >= 4\r\n    );\r\n    \r\n    return this.calculateAccuracy(longSequences);\r\n  }\r\n\r\n  assessAbstractThinking(data) {\r\n    // Capacidade de trabalhar com padrões abstratos\r\n    const abstractPatterns = data.filter(attempt => \r\n      attempt.sequenceType && \r\n      ['fibonacci', 'multiplicação', 'progressão_ímpar'].includes(attempt.sequenceType)\r\n    );\r\n    \r\n    return this.calculateAccuracy(abstractPatterns);\r\n  }\r\n\r\n  assessRuleInference(data) {\r\n    // Capacidade de inferir regras a partir de exemplos\r\n    const inferenceScore = data.reduce((score, attempt, index) => {\r\n      if (index === 0) return score;\r\n      \r\n      // Se acertou após ver exemplos similares\r\n      const similarPrevious = data.slice(0, index).filter(prev => \r\n        prev.sequenceType === attempt.sequenceType\r\n      );\r\n      \r\n      if (similarPrevious.length > 0 && attempt.isCorrect) {\r\n        return score + 1;\r\n      }\r\n      \r\n      return score;\r\n    }, 0);\r\n    \r\n    return data.length > 1 ? inferenceScore / (data.length - 1) : 0.5;\r\n  }\r\n\r\n  /**\r\n   * Gerar recomendações\r\n   */\r\n  generateRecommendations(data, skillIndex) {\r\n    const recommendations = [];\r\n    \r\n    if (skillIndex < 0.6) {\r\n      recommendations.push({\r\n        type: 'improvement',\r\n        priority: 'high',\r\n        message: 'Pratique sequências mais simples para fortalecer a base',\r\n        activities: ['simple_sequences', 'counting_games']\r\n      });\r\n    }\r\n    \r\n    const accuracyByType = this.analyzeAccuracyByType(data);\r\n    \r\n    // Identificar tipos de sequência com baixa performance\r\n    Object.keys(accuracyByType).forEach(type => {\r\n      if (accuracyByType[type].accuracy < 0.5 && accuracyByType[type].attempts >= 2) {\r\n        recommendations.push({\r\n          type: 'specific_skill',\r\n          priority: 'medium',\r\n          message: `Pratique mais sequências do tipo: ${this.sequenceTypes[type] || type}`,\r\n          activities: [`${type}_practice`, 'pattern_games']\r\n        });\r\n      }\r\n    });\r\n    \r\n    const processingSpeed = this.assessProcessingSpeed(data);\r\n    if (processingSpeed.speedCategory === 'slow') {\r\n      recommendations.push({\r\n        type: 'speed',\r\n        priority: 'low',\r\n        message: 'Tente resolver mais rapidamente - confie na sua primeira impressão',\r\n        activities: ['speed_sequences', 'quick_pattern_games']\r\n      });\r\n    }\r\n    \r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Funções auxiliares\r\n   */\r\n  calculateOverallAccuracy(data) {\r\n    if (!data || data.length === 0) return 0;\r\n    const correct = data.filter(attempt => attempt.isCorrect).length;\r\n    return correct / data.length;\r\n  }\r\n\r\n  groupBySequenceType(data) {\r\n    const grouped = {};\r\n    data.forEach(attempt => {\r\n      const type = attempt.sequenceType || 'unknown';\r\n      if (!grouped[type]) grouped[type] = [];\r\n      grouped[type].push(attempt);\r\n    });\r\n    return grouped;\r\n  }\r\n\r\n  calculateAverageTime(data) {\r\n    const times = data.map(attempt => attempt.responseTime).filter(Boolean);\r\n    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;\r\n  }\r\n\r\n  calculateAccuracy(data) {\r\n    if (!data || data.length === 0) return 0;\r\n    const correct = data.filter(attempt => attempt.isCorrect).length;\r\n    return correct / data.length;\r\n  }\r\n\r\n  calculateMedian(numbers) {\r\n    const sorted = numbers.slice().sort((a, b) => a - b);\r\n    const middle = Math.floor(sorted.length / 2);\r\n    return sorted.length % 2 === 0 ? \r\n      (sorted[middle - 1] + sorted[middle]) / 2 : \r\n      sorted[middle];\r\n  }\r\n\r\n  calculateConsistency(data) {\r\n    if (data.length < 2) return 0.5;\r\n    \r\n    const accuracies = this.divideIntoSegments(data, 3).map(segment => this.calculateAccuracy(segment));\r\n    const variance = this.calculateVariance(accuracies);\r\n    \r\n    return Math.max(0, 1 - variance);\r\n  }\r\n\r\n  calculateVariance(numbers) {\r\n    if (numbers.length === 0) return 0;\r\n    const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;\r\n    return numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;\r\n  }\r\n\r\n  divideIntoSegments(data, numSegments) {\r\n    const segmentSize = Math.floor(data.length / numSegments);\r\n    const segments = [];\r\n    \r\n    for (let i = 0; i < numSegments; i++) {\r\n      const start = i * segmentSize;\r\n      const end = i === numSegments - 1 ? data.length : (i + 1) * segmentSize;\r\n      segments.push(data.slice(start, end));\r\n    }\r\n    \r\n    return segments.filter(segment => segment.length > 0);\r\n  }\r\n\r\n  getDefaultAnalysis() {\r\n    return {\r\n      timestamp: new Date().toISOString(),\r\n      collector: 'SequenceAnalysisCollector',\r\n      version: '3.0.0',\r\n      overallAccuracy: 0.5,\r\n      sequentialSkillIndex: 0.5,\r\n      processingSpeed: { speed: 'unknown', score: 0.5 },\r\n      accuracyByType: {},\r\n      solvingStrategies: {},\r\n      cognitiveComplexity: {},\r\n      temporalProgress: { trend: 'insufficient_data', improvement: 0 },\r\n      errorPatterns: { totalErrors: 0, errorRate: 0 },\r\n      cognitiveSkills: {},\r\n      recommendations: [],\r\n      metadata: { totalSequences: 0, uniqueTypes: 0 }\r\n    };\r\n  }\r\n}\r\n", "/**\r\n * 📝 COMPARISON SKILLS COLLECTOR V3\r\n * Coletor especializado em análise de habilidades de comparação numérica\r\n * Portal Betina V3\r\n */\r\n\r\nexport class ComparisonSkillsCollector {\r\n  constructor() {\r\n    this.comparisonThresholds = {\r\n      excellent: 0.95,\r\n      good: 0.85,\r\n      average: 0.70,\r\n      poor: 0.50,\r\n      critical: 0.30\r\n    };\r\n    \r\n    this.comparisonTypes = {\r\n      magnitude: 'Comparação de magnitude (maior/menor)',\r\n      quantity: 'Comparação de quantidades',\r\n      difference: 'Cálculo de diferenças',\r\n      ordering: 'Ordenação de números',\r\n      relative: 'Posicionamento relativo',\r\n      range: 'Comparação por faixas'\r\n    };\r\n    \r\n    this.cognitiveSkills = {\r\n      numberSense: 'Senso numérico',\r\n      spatialReasoning: 'Raciocínio espacial',\r\n      relationalThinking: 'Pensamento relacional',\r\n      magnitudeEstimation: 'Estimativa de magnitude',\r\n      comparativeAnalysis: 'Análise comparativa'\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Método padronizado de coleta de dados\r\n   */\r\n  collect(data) {\r\n    return this.analyze(data);\r\n  }\r\n  \r\n  /**\r\n   * Análise principal das habilidades de comparação\r\n   */\r\n  async analyze(data) {\r\n    if (!data || !data.numberComparison) {\r\n      console.warn('ComparisonSkillsCollector: Dados de comparação não encontrados');\r\n      return this.getDefaultAnalysis();\r\n    }\r\n\r\n    const comparisonData = data.numberComparison;\r\n    \r\n    // Analisar precisão por tipo de comparação\r\n    const accuracyByType = this.analyzeAccuracyByType(comparisonData);\r\n    \r\n    // Avaliar discriminação de magnitude\r\n    const magnitudeDiscrimination = this.assessMagnitudeDiscrimination(comparisonData);\r\n    \r\n    // Analisar estratégias de comparação\r\n    const comparisonStrategies = this.analyzeComparisonStrategies(comparisonData);\r\n    \r\n    // Avaliar velocidade de processamento\r\n    const processingSpeed = this.assessProcessingSpeed(comparisonData);\r\n    \r\n    // Analisar progresso temporal\r\n    const temporalProgress = this.analyzeTemporalProgress(comparisonData);\r\n    \r\n    // Detectar padrões de erro\r\n    const errorPatterns = this.analyzeErrorPatterns(comparisonData);\r\n    \r\n    // Calcular índice de habilidade comparativa\r\n    const comparativeSkillIndex = this.calculateComparativeSkillIndex(comparisonData);\r\n\r\n    const analysis = {\r\n      timestamp: new Date().toISOString(),\r\n      collector: 'ComparisonSkillsCollector',\r\n      version: '3.0.0',\r\n      \r\n      // Métricas principais\r\n      overallAccuracy: this.calculateOverallAccuracy(comparisonData),\r\n      comparativeSkillIndex,\r\n      processingSpeed,\r\n      \r\n      // Análises detalhadas\r\n      accuracyByType,\r\n      magnitudeDiscrimination,\r\n      comparisonStrategies,\r\n      temporalProgress,\r\n      errorPatterns,\r\n      \r\n      // Habilidades cognitivas específicas\r\n      cognitiveSkills: {\r\n        numberSense: this.assessNumberSense(comparisonData),\r\n        spatialReasoning: this.assessSpatialReasoning(comparisonData),\r\n        relationalThinking: this.assessRelationalThinking(comparisonData),\r\n        magnitudeEstimation: this.assessMagnitudeEstimation(comparisonData),\r\n        comparativeAnalysis: this.assessComparativeAnalysis(comparisonData)\r\n      },\r\n      \r\n      // Análise de dificuldade\r\n      difficultyAnalysis: this.analyzeDifficultyProgression(comparisonData),\r\n      \r\n      // Análise de precisão por faixa numérica\r\n      rangeAccuracy: this.analyzeRangeAccuracy(comparisonData),\r\n      \r\n      // Recomendações\r\n      recommendations: this.generateRecommendations(comparisonData, comparativeSkillIndex),\r\n      \r\n      // Metadados\r\n      metadata: {\r\n        totalComparisons: comparisonData.length || 0,\r\n        uniqueTypes: this.countUniqueTypes(comparisonData),\r\n        averageDifference: this.calculateAverageDifference(comparisonData),\r\n        rangeDistribution: this.analyzeRangeDistribution(comparisonData)\r\n      }\r\n    };\r\n\r\n    return analysis;\r\n  }\r\n\r\n  /**\r\n   * Analisar precisão por tipo de comparação\r\n   */\r\n  analyzeAccuracyByType(data) {\r\n    const accuracyByType = {};\r\n    \r\n    // Agrupar por tipo de comparação\r\n    const groupedData = this.groupByComparisonType(data);\r\n    \r\n    Object.keys(groupedData).forEach(type => {\r\n      const typeData = groupedData[type];\r\n      const correct = typeData.filter(attempt => attempt.isCorrect).length;\r\n      accuracyByType[type] = {\r\n        accuracy: typeData.length > 0 ? correct / typeData.length : 0,\r\n        attempts: typeData.length,\r\n        averageTime: this.calculateAverageTime(typeData),\r\n        averageDifference: this.calculateAverageDifference(typeData),\r\n        difficulty: this.assessTypeDifficulty(typeData)\r\n      };\r\n    });\r\n    \r\n    return accuracyByType;\r\n  }\r\n\r\n  /**\r\n   * Avaliar discriminação de magnitude\r\n   */\r\n  assessMagnitudeDiscrimination(data) {\r\n    // Analisar precisão baseada na diferença entre números\r\n    const difficultyLevels = {\r\n      easy: [],     // diferença > 5\r\n      medium: [],   // diferença 3-5\r\n      hard: [],     // diferença 1-2\r\n      veryHard: []  // diferença < 1\r\n    };\r\n\r\n    data.forEach(attempt => {\r\n      const diff = Math.abs(attempt.number1 - attempt.number2);\r\n      \r\n      if (diff > 5) difficultyLevels.easy.push(attempt);\r\n      else if (diff >= 3) difficultyLevels.medium.push(attempt);\r\n      else if (diff >= 1) difficultyLevels.hard.push(attempt);\r\n      else difficultyLevels.veryHard.push(attempt);\r\n    });\r\n\r\n    const discrimination = {};\r\n    Object.keys(difficultyLevels).forEach(level => {\r\n      const levelData = difficultyLevels[level];\r\n      discrimination[level] = {\r\n        accuracy: this.calculateAccuracy(levelData),\r\n        averageTime: this.calculateAverageTime(levelData),\r\n        attempts: levelData.length\r\n      };\r\n    });\r\n\r\n    return {\r\n      byDifficulty: discrimination,\r\n      discriminationThreshold: this.calculateDiscriminationThreshold(data),\r\n      weberFraction: this.calculateWeberFraction(data)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisar estratégias de comparação\r\n   */\r\n  analyzeComparisonStrategies(data) {\r\n    const strategies = {\r\n      immediateComparison: false,   // Comparação imediata visual\r\n      countingStrategy: false,      // Estratégia de contagem\r\n      magnitudeEstimation: false,   // Estimativa de magnitude\r\n      digitalComparison: false,     // Comparação dígito por dígito\r\n      patternRecognition: false     // Reconhecimento de padrões\r\n    };\r\n\r\n    // Detectar comparação imediata (respostas muito rápidas e corretas)\r\n    const quickCorrect = data.filter(attempt => \r\n      attempt.isCorrect && attempt.responseTime < 2000\r\n    ).length;\r\n    strategies.immediateComparison = quickCorrect / data.length > 0.6;\r\n\r\n    // Detectar estratégia de contagem (tempo proporcional à diferença)\r\n    const timeVsDifference = this.analyzeTimeVsDifference(data);\r\n    strategies.countingStrategy = timeVsDifference.correlation > 0.7;\r\n\r\n    // Detectar estimativa de magnitude (precisão consistente em diferentes faixas)\r\n    strategies.magnitudeEstimation = this.detectMagnitudeStrategy(data);\r\n\r\n    // Detectar comparação digital (melhor performance com números de dígitos diferentes)\r\n    strategies.digitalComparison = this.detectDigitalStrategy(data);\r\n\r\n    return {\r\n      strategies,\r\n      dominantStrategy: this.identifyDominantStrategy(strategies),\r\n      strategyEffectiveness: this.assessStrategyEffectiveness(data, strategies)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Avaliar velocidade de processamento\r\n   */\r\n  assessProcessingSpeed(data) {\r\n    const responseTimes = data.map(attempt => attempt.responseTime).filter(Boolean);\r\n    \r\n    if (responseTimes.length === 0) return { speed: 'unknown', score: 0.5 };\r\n    \r\n    const averageTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;\r\n    const medianTime = this.calculateMedian(responseTimes);\r\n    \r\n    let speedCategory = 'average';\r\n    let speedScore = 0.5;\r\n    \r\n    if (averageTime < 2000) {\r\n      speedCategory = 'very_fast';\r\n      speedScore = 0.9;\r\n    } else if (averageTime < 3000) {\r\n      speedCategory = 'fast';\r\n      speedScore = 0.8;\r\n    } else if (averageTime < 5000) {\r\n      speedCategory = 'average';\r\n      speedScore = 0.6;\r\n    } else {\r\n      speedCategory = 'slow';\r\n      speedScore = 0.4;\r\n    }\r\n    \r\n    return {\r\n      averageTime,\r\n      medianTime,\r\n      speedCategory,\r\n      speedScore,\r\n      consistency: this.calculateTimeConsistency(responseTimes),\r\n      speedVsAccuracy: this.analyzeSpeedVsAccuracy(data)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisar progresso temporal\r\n   */\r\n  analyzeTemporalProgress(data) {\r\n    if (data.length < 3) return { trend: 'insufficient_data', improvement: 0 };\r\n    \r\n    const segments = this.divideIntoSegments(data, 3);\r\n    const segmentAccuracies = segments.map(segment => this.calculateAccuracy(segment));\r\n    const segmentTimes = segments.map(segment => this.calculateAverageTime(segment));\r\n    \r\n    const accuracyTrend = this.calculateTrend(segmentAccuracies);\r\n    const speedTrend = this.calculateTrend(segmentTimes.map(time => 1/time)); // Inverso para representar melhoria\r\n    \r\n    return {\r\n      accuracyTrend,\r\n      speedTrend,\r\n      improvement: segmentAccuracies[segmentAccuracies.length - 1] - segmentAccuracies[0],\r\n      segmentAccuracies,\r\n      segmentTimes,\r\n      learningRate: this.calculateLearningRate(segmentAccuracies),\r\n      stability: this.calculateStability(segmentAccuracies)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisar padrões de erro\r\n   */\r\n  analyzeErrorPatterns(data) {\r\n    const errors = data.filter(attempt => !attempt.isCorrect);\r\n    \r\n    const errorTypes = {\r\n      magnitudeConfusion: 0,     // Confusão de magnitude\r\n      digitalError: 0,          // Erro na comparação de dígitos\r\n      inverseComparison: 0,     // Comparação invertida\r\n      proximityError: 0,        // Erro em números próximos\r\n      randomError: 0            // Erro aparentemente aleatório\r\n    };\r\n\r\n    errors.forEach(error => {\r\n      if (error.userAnswer && error.correctAnswer) {\r\n        const difference = Math.abs(error.number1 - error.number2);\r\n        \r\n        if (difference <= 2) {\r\n          errorTypes.proximityError++;\r\n        } else if (this.isInverseComparison(error)) {\r\n          errorTypes.inverseComparison++;\r\n        } else if (this.isDigitalError(error)) {\r\n          errorTypes.digitalError++;\r\n        } else if (difference > 10) {\r\n          errorTypes.magnitudeConfusion++;\r\n        } else {\r\n          errorTypes.randomError++;\r\n        }\r\n      }\r\n    });\r\n\r\n    return {\r\n      totalErrors: errors.length,\r\n      errorTypes,\r\n      errorRate: data.length > 0 ? errors.length / data.length : 0,\r\n      criticalErrors: this.identifyCriticalErrors(errors),\r\n      errorProgression: this.analyzeErrorProgression(data),\r\n      errorsByDifficulty: this.analyzeErrorsByDifficulty(data)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calcular índice de habilidade comparativa\r\n   */\r\n  calculateComparativeSkillIndex(data) {\r\n    const accuracy = this.calculateOverallAccuracy(data);\r\n    const speed = this.assessProcessingSpeed(data).speedScore;\r\n    const discrimination = this.assessMagnitudeDiscrimination(data);\r\n    const discriminationScore = this.calculateDiscriminationScore(discrimination);\r\n    const consistency = this.calculateConsistency(data);\r\n    \r\n    return (accuracy * 0.4) + (speed * 0.2) + (discriminationScore * 0.2) + (consistency * 0.2);\r\n  }\r\n\r\n  /**\r\n   * Avaliar habilidades cognitivas específicas\r\n   */\r\n  assessNumberSense(data) {\r\n    // Capacidade geral de trabalhar com números\r\n    const overallAccuracy = this.calculateOverallAccuracy(data);\r\n    const speedScore = this.assessProcessingSpeed(data).speedScore;\r\n    \r\n    return (overallAccuracy * 0.7) + (speedScore * 0.3);\r\n  }\r\n\r\n  assessSpatialReasoning(data) {\r\n    // Capacidade de visualizar relações numéricas espacialmente\r\n    const quickComparisons = data.filter(attempt => \r\n      attempt.isCorrect && attempt.responseTime < 3000\r\n    ).length;\r\n    \r\n    return data.length > 0 ? quickComparisons / data.length : 0.5;\r\n  }\r\n\r\n  assessRelationalThinking(data) {\r\n    // Capacidade de entender relações entre números\r\n    const complexComparisons = data.filter(attempt => \r\n      attempt.comparisonType && \r\n      ['relative_position', 'range_comparison'].includes(attempt.comparisonType)\r\n    );\r\n    \r\n    return this.calculateAccuracy(complexComparisons);\r\n  }\r\n\r\n  assessMagnitudeEstimation(data) {\r\n    // Capacidade de estimar magnitudes numericas\r\n    const discrimination = this.assessMagnitudeDiscrimination(data);\r\n    return this.calculateDiscriminationScore(discrimination);\r\n  }\r\n\r\n  assessComparativeAnalysis(data) {\r\n    // Capacidade de fazer análises comparativas complexas\r\n    const multiStepComparisons = data.filter(attempt => \r\n      attempt.requiresMultipleSteps || \r\n      (attempt.number1 > 20 && attempt.number2 > 20)\r\n    );\r\n    \r\n    return this.calculateAccuracy(multiStepComparisons);\r\n  }\r\n\r\n  /**\r\n   * Analisar precisão por faixa numérica\r\n   */\r\n  analyzeRangeAccuracy(data) {\r\n    const ranges = {\r\n      small: [],    // 1-10\r\n      medium: [],   // 11-50\r\n      large: [],    // 51-100\r\n      veryLarge: [] // >100\r\n    };\r\n\r\n    data.forEach(attempt => {\r\n      const maxNum = Math.max(attempt.number1, attempt.number2);\r\n      \r\n      if (maxNum <= 10) ranges.small.push(attempt);\r\n      else if (maxNum <= 50) ranges.medium.push(attempt);\r\n      else if (maxNum <= 100) ranges.large.push(attempt);\r\n      else ranges.veryLarge.push(attempt);\r\n    });\r\n\r\n    const rangeAccuracy = {};\r\n    Object.keys(ranges).forEach(range => {\r\n      const rangeData = ranges[range];\r\n      rangeAccuracy[range] = {\r\n        accuracy: this.calculateAccuracy(rangeData),\r\n        averageTime: this.calculateAverageTime(rangeData),\r\n        attempts: rangeData.length\r\n      };\r\n    });\r\n\r\n    return rangeAccuracy;\r\n  }\r\n\r\n  /**\r\n   * Gerar recomendações\r\n   */\r\n  generateRecommendations(data, skillIndex) {\r\n    const recommendations = [];\r\n    \r\n    if (skillIndex < 0.6) {\r\n      recommendations.push({\r\n        type: 'improvement',\r\n        priority: 'high',\r\n        message: 'Pratique comparações simples com números pequenos',\r\n        activities: ['simple_comparisons', 'number_line_games']\r\n      });\r\n    }\r\n    \r\n    const magnitudeDiscrimination = this.assessMagnitudeDiscrimination(data);\r\n    if (magnitudeDiscrimination.byDifficulty.hard.accuracy < 0.5) {\r\n      recommendations.push({\r\n        type: 'specific_skill',\r\n        priority: 'high',\r\n        message: 'Trabalhe discriminação de números próximos',\r\n        activities: ['close_numbers_practice', 'magnitude_games']\r\n      });\r\n    }\r\n    \r\n    const processingSpeed = this.assessProcessingSpeed(data);\r\n    if (processingSpeed.speedCategory === 'slow') {\r\n      recommendations.push({\r\n        type: 'speed',\r\n        priority: 'medium',\r\n        message: 'Pratique comparações rápidas para desenvolver automatismo',\r\n        activities: ['speed_comparisons', 'quick_decision_games']\r\n      });\r\n    }\r\n    \r\n    const rangeAccuracy = this.analyzeRangeAccuracy(data);\r\n    Object.keys(rangeAccuracy).forEach(range => {\r\n      if (rangeAccuracy[range].accuracy < 0.6 && rangeAccuracy[range].attempts >= 2) {\r\n        recommendations.push({\r\n          type: 'range_specific',\r\n          priority: 'medium',\r\n          message: `Pratique mais comparações na faixa ${range}`,\r\n          activities: [`${range}_range_practice`, 'graduated_difficulty']\r\n        });\r\n      }\r\n    });\r\n    \r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Funções auxiliares\r\n   */\r\n  calculateOverallAccuracy(data) {\r\n    if (!data || data.length === 0) return 0;\r\n    const correct = data.filter(attempt => attempt.isCorrect).length;\r\n    return correct / data.length;\r\n  }\r\n\r\n  groupByComparisonType(data) {\r\n    const grouped = {};\r\n    data.forEach(attempt => {\r\n      const type = attempt.comparisonType || 'magnitude';\r\n      if (!grouped[type]) grouped[type] = [];\r\n      grouped[type].push(attempt);\r\n    });\r\n    return grouped;\r\n  }\r\n\r\n  calculateAverageTime(data) {\r\n    const times = data.map(attempt => attempt.responseTime).filter(Boolean);\r\n    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;\r\n  }\r\n\r\n  calculateAverageDifference(data) {\r\n    const differences = data.map(attempt => \r\n      Math.abs(attempt.number1 - attempt.number2)\r\n    ).filter(Boolean);\r\n    return differences.length > 0 ? differences.reduce((a, b) => a + b, 0) / differences.length : 0;\r\n  }\r\n\r\n  calculateAccuracy(data) {\r\n    if (!data || data.length === 0) return 0;\r\n    const correct = data.filter(attempt => attempt.isCorrect).length;\r\n    return correct / data.length;\r\n  }\r\n\r\n  calculateMedian(numbers) {\r\n    const sorted = numbers.slice().sort((a, b) => a - b);\r\n    const middle = Math.floor(sorted.length / 2);\r\n    return sorted.length % 2 === 0 ? \r\n      (sorted[middle - 1] + sorted[middle]) / 2 : \r\n      sorted[middle];\r\n  }\r\n\r\n  calculateDiscriminationScore(discrimination) {\r\n    const scores = Object.values(discrimination.byDifficulty).map(level => level.accuracy);\r\n    return scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0.5;\r\n  }\r\n\r\n  calculateConsistency(data) {\r\n    if (data.length < 2) return 0.5;\r\n    \r\n    const accuracies = this.divideIntoSegments(data, 3).map(segment => this.calculateAccuracy(segment));\r\n    const variance = this.calculateVariance(accuracies);\r\n    \r\n    return Math.max(0, 1 - variance);\r\n  }\r\n\r\n  calculateVariance(numbers) {\r\n    if (numbers.length === 0) return 0;\r\n    const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;\r\n    return numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;\r\n  }\r\n\r\n  divideIntoSegments(data, numSegments) {\r\n    const segmentSize = Math.floor(data.length / numSegments);\r\n    const segments = [];\r\n    \r\n    for (let i = 0; i < numSegments; i++) {\r\n      const start = i * segmentSize;\r\n      const end = i === numSegments - 1 ? data.length : (i + 1) * segmentSize;\r\n      segments.push(data.slice(start, end));\r\n    }\r\n    \r\n    return segments.filter(segment => segment.length > 0);\r\n  }\r\n\r\n  getDefaultAnalysis() {\r\n    return {\r\n      timestamp: new Date().toISOString(),\r\n      collector: 'ComparisonSkillsCollector',\r\n      version: '3.0.0',\r\n      overallAccuracy: 0.5,\r\n      comparativeSkillIndex: 0.5,\r\n      processingSpeed: { speed: 'unknown', score: 0.5 },\r\n      accuracyByType: {},\r\n      magnitudeDiscrimination: {},\r\n      comparisonStrategies: {},\r\n      temporalProgress: { trend: 'insufficient_data', improvement: 0 },\r\n      errorPatterns: { totalErrors: 0, errorRate: 0 },\r\n      cognitiveSkills: {},\r\n      recommendations: [],\r\n      metadata: { totalComparisons: 0, uniqueTypes: 0 }\r\n    };\r\n  }\r\n}\r\n", "/**\r\n * 📝 SOUND MATCHING COLLECTOR V3\r\n * Coletor especializado em análise de habilidades de correspondência som-número\r\n * Portal Betina V3\r\n */\r\n\r\nexport class SoundMatchingCollector {\r\n  constructor() {\r\n    this.soundThresholds = {\r\n      excellent: 0.95,\r\n      good: 0.85,\r\n      average: 0.70,\r\n      poor: 0.50,\r\n      critical: 0.30\r\n    };\r\n    \r\n    this.soundTypes = {\r\n      quantity: 'Correspondência quantidade-som',\r\n      sequence: 'Sequência sonora',\r\n      pattern: 'Padrão rítmico',\r\n      recognition: 'Reconhecimento auditivo',\r\n      memory: 'Memória auditiva'\r\n    };\r\n    \r\n    this.cognitiveSkills = {\r\n      auditoryProcessing: 'Processamento auditivo',\r\n      auditoryMemory: 'Memória auditiva',\r\n      audioVisualIntegration: 'Integração audiovisual',\r\n      sequentialProcessing: 'Processamento sequencial',\r\n      rhythmicPatterns: 'Padrões rítmicos'\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Método padronizado de coleta de dados\r\n   */\r\n  collect(data) {\r\n    return this.analyze(data);\r\n  }\r\n  \r\n  /**\r\n   * Análise principal das habilidades de correspondência som-número\r\n   */\r\n  async analyze(data) {\r\n    if (!data || !data.soundMatching) {\r\n      console.warn('SoundMatchingCollector: Dados de correspondência sonora não encontrados');\r\n      return this.getDefaultAnalysis();\r\n    }\r\n\r\n    const soundData = data.soundMatching;\r\n    \r\n    // Analisar precisão por tipo de som\r\n    const accuracyBySoundType = this.analyzeAccuracyBySoundType(soundData);\r\n    \r\n    // Avaliar capacidade de memória auditiva\r\n    const auditoryMemory = this.assessAuditoryMemory(soundData);\r\n    \r\n    // Analisar integração audiovisual\r\n    const audioVisualIntegration = this.analyzeAudioVisualIntegration(soundData);\r\n    \r\n    // Avaliar velocidade de processamento auditivo\r\n    const auditoryProcessingSpeed = this.assessAuditoryProcessingSpeed(soundData);\r\n    \r\n    // Analisar progresso temporal\r\n    const temporalProgress = this.analyzeTemporalProgress(soundData);\r\n    \r\n    // Detectar padrões de erro auditivo\r\n    const auditoryErrorPatterns = this.analyzeAuditoryErrorPatterns(soundData);\r\n    \r\n    // Calcular índice de habilidade auditiva\r\n    const auditorySkillIndex = this.calculateAuditorySkillIndex(soundData);\r\n\r\n    const analysis = {\r\n      timestamp: new Date().toISOString(),\r\n      collector: 'SoundMatchingCollector',\r\n      version: '3.0.0',\r\n      \r\n      // Métricas principais\r\n      overallAccuracy: this.calculateOverallAccuracy(soundData),\r\n      auditorySkillIndex,\r\n      auditoryProcessingSpeed,\r\n      \r\n      // Análises detalhadas\r\n      accuracyBySoundType,\r\n      auditoryMemory,\r\n      audioVisualIntegration,\r\n      temporalProgress,\r\n      auditoryErrorPatterns,\r\n      \r\n      // Habilidades cognitivas específicas\r\n      cognitiveSkills: {\r\n        auditoryProcessing: this.assessAuditoryProcessing(soundData),\r\n        auditoryMemory: this.assessAuditoryMemorySkill(soundData),\r\n        audioVisualIntegration: this.assessAudioVisualIntegrationSkill(soundData),\r\n        sequentialProcessing: this.assessSequentialProcessing(soundData),\r\n        rhythmicPatterns: this.assessRhythmicPatterns(soundData)\r\n      },\r\n      \r\n      // Análise de dificuldade auditiva\r\n      auditoryDifficultyAnalysis: this.analyzeAuditoryDifficultyProgression(soundData),\r\n      \r\n      // Análise de latência de resposta\r\n      responseLatency: this.analyzeResponseLatency(soundData),\r\n      \r\n      // Recomendações\r\n      recommendations: this.generateRecommendations(soundData, auditorySkillIndex),\r\n      \r\n      // Metadados\r\n      metadata: {\r\n        totalSounds: soundData.length || 0,\r\n        uniqueSoundTypes: this.countUniqueSoundTypes(soundData),\r\n        averageSoundDuration: this.calculateAverageSoundDuration(soundData),\r\n        soundComplexityDistribution: this.analyzeSoundComplexityDistribution(soundData)\r\n      }\r\n    };\r\n\r\n    return analysis;\r\n  }\r\n\r\n  /**\r\n   * Analisar precisão por tipo de som\r\n   */\r\n  analyzeAccuracyBySoundType(data) {\r\n    const accuracyBySoundType = {};\r\n    \r\n    // Agrupar por tipo de som\r\n    const groupedData = this.groupBySoundType(data);\r\n    \r\n    Object.keys(groupedData).forEach(type => {\r\n      const typeData = groupedData[type];\r\n      const correct = typeData.filter(attempt => attempt.isCorrect).length;\r\n      accuracyBySoundType[type] = {\r\n        accuracy: typeData.length > 0 ? correct / typeData.length : 0,\r\n        attempts: typeData.length,\r\n        averageResponseTime: this.calculateAverageTime(typeData),\r\n        averageLatency: this.calculateAverageLatency(typeData),\r\n        difficulty: this.assessSoundTypeDifficulty(typeData)\r\n      };\r\n    });\r\n    \r\n    return accuracyBySoundType;\r\n  }\r\n\r\n  /**\r\n   * Avaliar capacidade de memória auditiva\r\n   */\r\n  assessAuditoryMemory(data) {\r\n    // Analisar desempenho baseado no delay entre som e resposta\r\n    const memoryLevels = {\r\n      immediate: [],    // resposta imediata (< 2s após som)\r\n      shortTerm: [],    // resposta rápida (2-5s)\r\n      mediumTerm: [],   // resposta normal (5-10s)\r\n      longTerm: []      // resposta demorada (> 10s)\r\n    };\r\n\r\n    data.forEach(attempt => {\r\n      const latency = attempt.responseTime - (attempt.soundDuration || 1000);\r\n      \r\n      if (latency < 2000) memoryLevels.immediate.push(attempt);\r\n      else if (latency < 5000) memoryLevels.shortTerm.push(attempt);\r\n      else if (latency < 10000) memoryLevels.mediumTerm.push(attempt);\r\n      else memoryLevels.longTerm.push(attempt);\r\n    });\r\n\r\n    const memoryAnalysis = {};\r\n    Object.keys(memoryLevels).forEach(level => {\r\n      const levelData = memoryLevels[level];\r\n      memoryAnalysis[level] = {\r\n        accuracy: this.calculateAccuracy(levelData),\r\n        averageTime: this.calculateAverageTime(levelData),\r\n        attempts: levelData.length\r\n      };\r\n    });\r\n\r\n    return {\r\n      byLatency: memoryAnalysis,\r\n      memorySpan: this.calculateMemorySpan(data),\r\n      retentionRate: this.calculateRetentionRate(data),\r\n      decayPattern: this.analyzeDecayPattern(data)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisar integração audiovisual\r\n   */\r\n  analyzeAudioVisualIntegration(data) {\r\n    const integration = {\r\n      synchronization: 0,        // Sincronização som-visual\r\n      crossModalAccuracy: 0,     // Precisão cross-modal\r\n      interferenceResistance: 0,  // Resistência a interferência\r\n      modalityPreference: null    // Preferência de modalidade\r\n    };\r\n\r\n    // Analisar desempenho quando há estímulos visuais simultâneos\r\n    const audioOnlyTrials = data.filter(attempt => !attempt.hasVisualStimulus);\r\n    const audioVisualTrials = data.filter(attempt => attempt.hasVisualStimulus);\r\n\r\n    const audioOnlyAccuracy = this.calculateAccuracy(audioOnlyTrials);\r\n    const audioVisualAccuracy = this.calculateAccuracy(audioVisualTrials);\r\n\r\n    integration.crossModalAccuracy = audioVisualAccuracy;\r\n    integration.interferenceResistance = audioVisualAccuracy / Math.max(audioOnlyAccuracy, 0.1);\r\n    integration.modalityPreference = audioOnlyAccuracy > audioVisualAccuracy ? 'auditory' : 'visual';\r\n\r\n    // Analisar sincronização temporal\r\n    integration.synchronization = this.analyzeSynchronization(data);\r\n\r\n    return integration;\r\n  }\r\n\r\n  /**\r\n   * Avaliar velocidade de processamento auditivo\r\n   */\r\n  assessAuditoryProcessingSpeed(data) {\r\n    const responseTimes = data.map(attempt => attempt.responseTime).filter(Boolean);\r\n    const soundDurations = data.map(attempt => attempt.soundDuration || 1000).filter(Boolean);\r\n    \r\n    if (responseTimes.length === 0) return { speed: 'unknown', score: 0.5 };\r\n    \r\n    // Calcular latência real (tempo após fim do som)\r\n    const latencies = data.map((attempt, index) => \r\n      attempt.responseTime - soundDurations[index]\r\n    ).filter(latency => latency > 0);\r\n    \r\n    const averageLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;\r\n    const medianLatency = this.calculateMedian(latencies);\r\n    \r\n    let speedCategory = 'average';\r\n    let speedScore = 0.5;\r\n    \r\n    if (averageLatency < 1000) {\r\n      speedCategory = 'very_fast';\r\n      speedScore = 0.9;\r\n    } else if (averageLatency < 2000) {\r\n      speedCategory = 'fast';\r\n      speedScore = 0.8;\r\n    } else if (averageLatency < 4000) {\r\n      speedCategory = 'average';\r\n      speedScore = 0.6;\r\n    } else {\r\n      speedCategory = 'slow';\r\n      speedScore = 0.4;\r\n    }\r\n    \r\n    return {\r\n      averageLatency,\r\n      medianLatency,\r\n      speedCategory,\r\n      speedScore,\r\n      consistency: this.calculateLatencyConsistency(latencies),\r\n      processingEfficiency: this.calculateProcessingEfficiency(data)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisar progresso temporal\r\n   */\r\n  analyzeTemporalProgress(data) {\r\n    if (data.length < 3) return { trend: 'insufficient_data', improvement: 0 };\r\n    \r\n    const segments = this.divideIntoSegments(data, 3);\r\n    const segmentAccuracies = segments.map(segment => this.calculateAccuracy(segment));\r\n    const segmentLatencies = segments.map(segment => this.calculateAverageLatency(segment));\r\n    \r\n    const accuracyTrend = this.calculateTrend(segmentAccuracies);\r\n    const speedTrend = this.calculateTrend(segmentLatencies.map(latency => 1/latency));\r\n    \r\n    return {\r\n      accuracyTrend,\r\n      speedTrend,\r\n      improvement: segmentAccuracies[segmentAccuracies.length - 1] - segmentAccuracies[0],\r\n      segmentAccuracies,\r\n      segmentLatencies,\r\n      auditoryLearningRate: this.calculateLearningRate(segmentAccuracies),\r\n      auditoryStability: this.calculateStability(segmentAccuracies)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisar padrões de erro auditivo\r\n   */\r\n  analyzeAuditoryErrorPatterns(data) {\r\n    const errors = data.filter(attempt => !attempt.isCorrect);\r\n    \r\n    const errorTypes = {\r\n      quantityMiscount: 0,      // Erro na contagem de sons\r\n      sequenceConfusion: 0,     // Confusão na sequência\r\n      memoryFailure: 0,         // Falha de memória auditiva\r\n      attentionLapse: 0,        // Lapso de atenção\r\n      processingDelay: 0        // Atraso no processamento\r\n    };\r\n\r\n    errors.forEach(error => {\r\n      const latency = error.responseTime - (error.soundDuration || 1000);\r\n      \r\n      if (latency < 500) {\r\n        errorTypes.attentionLapse++;\r\n      } else if (latency > 10000) {\r\n        errorTypes.memoryFailure++;\r\n      } else if (error.soundType === 'sequence') {\r\n        errorTypes.sequenceConfusion++;\r\n      } else if (error.soundType === 'quantity') {\r\n        errorTypes.quantityMiscount++;\r\n      } else {\r\n        errorTypes.processingDelay++;\r\n      }\r\n    });\r\n\r\n    return {\r\n      totalErrors: errors.length,\r\n      errorTypes,\r\n      errorRate: data.length > 0 ? errors.length / data.length : 0,\r\n      criticalErrors: this.identifyCriticalAuditoryErrors(errors),\r\n      errorProgression: this.analyzeErrorProgression(data),\r\n      auditoryErrorsByComplexity: this.analyzeErrorsByComplexity(data)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calcular índice de habilidade auditiva\r\n   */\r\n  calculateAuditorySkillIndex(data) {\r\n    const accuracy = this.calculateOverallAccuracy(data);\r\n    const speed = this.assessAuditoryProcessingSpeed(data).speedScore;\r\n    const memory = this.assessAuditoryMemory(data);\r\n    const memoryScore = this.calculateMemoryScore(memory);\r\n    const integration = this.analyzeAudioVisualIntegration(data);\r\n    const integrationScore = integration.crossModalAccuracy;\r\n    const consistency = this.calculateConsistency(data);\r\n    \r\n    return (accuracy * 0.3) + (speed * 0.2) + (memoryScore * 0.2) + (integrationScore * 0.15) + (consistency * 0.15);\r\n  }\r\n\r\n  /**\r\n   * Avaliar habilidades cognitivas específicas\r\n   */\r\n  assessAuditoryProcessing(data) {\r\n    // Capacidade geral de processar informação auditiva\r\n    const overallAccuracy = this.calculateOverallAccuracy(data);\r\n    const speedScore = this.assessAuditoryProcessingSpeed(data).speedScore;\r\n    \r\n    return (overallAccuracy * 0.7) + (speedScore * 0.3);\r\n  }\r\n\r\n  assessAuditoryMemorySkill(data) {\r\n    // Capacidade de reter informação auditiva\r\n    const memory = this.assessAuditoryMemory(data);\r\n    return this.calculateMemoryScore(memory);\r\n  }\r\n\r\n  assessAudioVisualIntegrationSkill(data) {\r\n    // Capacidade de integrar informação auditiva e visual\r\n    const integration = this.analyzeAudioVisualIntegration(data);\r\n    return integration.crossModalAccuracy;\r\n  }\r\n\r\n  assessSequentialProcessing(data) {\r\n    // Capacidade de processar sequências auditivas\r\n    const sequenceTrials = data.filter(attempt => \r\n      attempt.soundType && attempt.soundType.includes('sequence')\r\n    );\r\n    \r\n    return this.calculateAccuracy(sequenceTrials);\r\n  }\r\n\r\n  assessRhythmicPatterns(data) {\r\n    // Capacidade de reconhecer padrões rítmicos\r\n    const rhythmTrials = data.filter(attempt => \r\n      attempt.soundType && attempt.soundType.includes('rhythm')\r\n    );\r\n    \r\n    return this.calculateAccuracy(rhythmTrials);\r\n  }\r\n\r\n  /**\r\n   * Analisar latência de resposta\r\n   */\r\n  analyzeResponseLatency(data) {\r\n    const latencies = data.map(attempt => \r\n      attempt.responseTime - (attempt.soundDuration || 1000)\r\n    ).filter(latency => latency > 0);\r\n\r\n    if (latencies.length === 0) return { status: 'no_data' };\r\n\r\n    return {\r\n      averageLatency: latencies.reduce((a, b) => a + b, 0) / latencies.length,\r\n      medianLatency: this.calculateMedian(latencies),\r\n      latencyVariability: this.calculateVariance(latencies),\r\n      optimalRange: this.calculateOptimalLatencyRange(data),\r\n      latencyDistribution: this.analyzeLatencyDistribution(latencies)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Gerar recomendações\r\n   */\r\n  generateRecommendations(data, skillIndex) {\r\n    const recommendations = [];\r\n    \r\n    if (skillIndex < 0.6) {\r\n      recommendations.push({\r\n        type: 'improvement',\r\n        priority: 'high',\r\n        message: 'Pratique exercícios básicos de correspondência som-número',\r\n        activities: ['simple_sound_counting', 'audio_visual_matching']\r\n      });\r\n    }\r\n    \r\n    const memory = this.assessAuditoryMemory(data);\r\n    if (this.calculateMemoryScore(memory) < 0.5) {\r\n      recommendations.push({\r\n        type: 'memory',\r\n        priority: 'high',\r\n        message: 'Trabalhe memória auditiva com sequências progressivamente mais longas',\r\n        activities: ['memory_sequences', 'auditory_span_games']\r\n      });\r\n    }\r\n    \r\n    const processing = this.assessAuditoryProcessingSpeed(data);\r\n    if (processing.speedCategory === 'slow') {\r\n      recommendations.push({\r\n        type: 'speed',\r\n        priority: 'medium',\r\n        message: 'Pratique respostas mais rápidas a estímulos auditivos',\r\n        activities: ['speed_sound_games', 'reaction_time_training']\r\n      });\r\n    }\r\n    \r\n    const integration = this.analyzeAudioVisualIntegration(data);\r\n    if (integration.crossModalAccuracy < 0.6) {\r\n      recommendations.push({\r\n        type: 'integration',\r\n        priority: 'medium',\r\n        message: 'Desenvolva integração audiovisual com exercícios multimodais',\r\n        activities: ['cross_modal_games', 'synchronization_training']\r\n      });\r\n    }\r\n    \r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Funções auxiliares\r\n   */\r\n  calculateOverallAccuracy(data) {\r\n    if (!data || data.length === 0) return 0;\r\n    const correct = data.filter(attempt => attempt.isCorrect).length;\r\n    return correct / data.length;\r\n  }\r\n\r\n  groupBySoundType(data) {\r\n    const grouped = {};\r\n    data.forEach(attempt => {\r\n      const type = attempt.soundType || 'quantity';\r\n      if (!grouped[type]) grouped[type] = [];\r\n      grouped[type].push(attempt);\r\n    });\r\n    return grouped;\r\n  }\r\n\r\n  calculateAverageTime(data) {\r\n    const times = data.map(attempt => attempt.responseTime).filter(Boolean);\r\n    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;\r\n  }\r\n\r\n  calculateAverageLatency(data) {\r\n    const latencies = data.map(attempt => \r\n      attempt.responseTime - (attempt.soundDuration || 1000)\r\n    ).filter(latency => latency > 0);\r\n    return latencies.length > 0 ? latencies.reduce((a, b) => a + b, 0) / latencies.length : 0;\r\n  }\r\n\r\n  calculateAccuracy(data) {\r\n    if (!data || data.length === 0) return 0;\r\n    const correct = data.filter(attempt => attempt.isCorrect).length;\r\n    return correct / data.length;\r\n  }\r\n\r\n  calculateMedian(numbers) {\r\n    const sorted = numbers.slice().sort((a, b) => a - b);\r\n    const middle = Math.floor(sorted.length / 2);\r\n    return sorted.length % 2 === 0 ? \r\n      (sorted[middle - 1] + sorted[middle]) / 2 : \r\n      sorted[middle];\r\n  }\r\n\r\n  calculateMemoryScore(memory) {\r\n    const scores = Object.values(memory.byLatency).map(level => level.accuracy);\r\n    return scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0.5;\r\n  }\r\n\r\n  calculateConsistency(data) {\r\n    if (data.length < 2) return 0.5;\r\n    \r\n    const accuracies = this.divideIntoSegments(data, 3).map(segment => this.calculateAccuracy(segment));\r\n    const variance = this.calculateVariance(accuracies);\r\n    \r\n    return Math.max(0, 1 - variance);\r\n  }\r\n\r\n  calculateVariance(numbers) {\r\n    if (numbers.length === 0) return 0;\r\n    const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;\r\n    return numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;\r\n  }\r\n\r\n  divideIntoSegments(data, numSegments) {\r\n    const segmentSize = Math.floor(data.length / numSegments);\r\n    const segments = [];\r\n    \r\n    for (let i = 0; i < numSegments; i++) {\r\n      const start = i * segmentSize;\r\n      const end = i === numSegments - 1 ? data.length : (i + 1) * segmentSize;\r\n      segments.push(data.slice(start, end));\r\n    }\r\n    \r\n    return segments.filter(segment => segment.length > 0);\r\n  }\r\n\r\n  getDefaultAnalysis() {\r\n    return {\r\n      timestamp: new Date().toISOString(),\r\n      collector: 'SoundMatchingCollector',\r\n      version: '3.0.0',\r\n      overallAccuracy: 0.5,\r\n      auditorySkillIndex: 0.5,\r\n      auditoryProcessingSpeed: { speed: 'unknown', score: 0.5 },\r\n      accuracyBySoundType: {},\r\n      auditoryMemory: {},\r\n      audioVisualIntegration: {},\r\n      temporalProgress: { trend: 'insufficient_data', improvement: 0 },\r\n      auditoryErrorPatterns: { totalErrors: 0, errorRate: 0 },\r\n      cognitiveSkills: {},\r\n      recommendations: [],\r\n      metadata: { totalSounds: 0, uniqueSoundTypes: 0 }\r\n    };\r\n  }\r\n}\r\n", "/**\r\n * 🔍 PATTERN RECOGNITION COLLECTOR V3\r\n * Coletor especializado em análise de reconhecimento de padrões numéricos\r\n * Portal Betina V3\r\n */\r\n\r\nexport class PatternRecognitionCollector {\r\n  constructor() {\r\n    this.patternThresholds = {\r\n      excellent: 0.95,\r\n      good: 0.85,\r\n      average: 0.70,\r\n      poor: 0.50,\r\n      critical: 0.30\r\n    };\r\n    \r\n    this.patternTypes = {\r\n      sequential: 'Padrões sequenciais (1,2,3...)',\r\n      arithmetic: 'Progressões aritméticas (+2, +3...)',\r\n      geometric: 'Progressões geométricas (×2, ×3...)',\r\n      visual: 'Padrões visuais e espaciais',\r\n      repetitive: 'Padrões repetitivos (A,B,A,B...)',\r\n      complex: 'Padrões complexos e mistos'\r\n    };\r\n    \r\n    this.cognitiveSkills = {\r\n      patternRecognition: 'Reconhecimento de padrões',\r\n      sequentialThinking: 'Pensamento sequencial',\r\n      logicalReasoning: 'Raciocínio lógico',\r\n      predictionAbility: 'Capacidade de predição',\r\n      abstractThinking: 'Pensamento abstrato'\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Método padronizado de coleta de dados\r\n   */\r\n  collect(data) {\r\n    return this.analyze(data);\r\n  }\r\n  \r\n  /**\r\n   * Análise principal do reconhecimento de padrões\r\n   */\r\n  async analyze(data) {\r\n    if (!data || !data.patternRecognition) {\r\n      console.warn('PatternRecognitionCollector: Dados de reconhecimento de padrões não encontrados');\r\n      return this.getDefaultAnalysis();\r\n    }\r\n\r\n    const patternData = data.patternRecognition;\r\n    \r\n    // Analisar precisão por tipo de padrão\r\n    const accuracyByType = this.analyzeAccuracyByType(patternData);\r\n    \r\n    // Avaliar velocidade de reconhecimento\r\n    const recognitionSpeed = this.assessRecognitionSpeed(patternData);\r\n    \r\n    // Analisar complexidade dos padrões resolvidos\r\n    const complexityAnalysis = this.analyzeComplexityHandling(patternData);\r\n    \r\n    // Avaliar capacidade de predição\r\n    const predictionAbility = this.assessPredictionAbility(patternData);\r\n    \r\n    // Analisar progresso temporal\r\n    const temporalProgress = this.analyzeTemporalProgress(patternData);\r\n    \r\n    // Detectar padrões de erro\r\n    const errorPatterns = this.analyzeErrorPatterns(patternData);\r\n    \r\n    // Calcular índice de reconhecimento de padrões\r\n    const patternRecognitionIndex = this.calculatePatternRecognitionIndex(patternData);\r\n\r\n    const analysis = {\r\n      timestamp: new Date().toISOString(),\r\n      collector: 'PatternRecognitionCollector',\r\n      version: '3.0.0',\r\n      \r\n      // Métricas principais\r\n      overallAccuracy: this.calculateOverallAccuracy(patternData),\r\n      patternRecognitionIndex,\r\n      recognitionSpeed,\r\n      \r\n      // Análises detalhadas\r\n      accuracyByType,\r\n      complexityAnalysis,\r\n      predictionAbility,\r\n      temporalProgress,\r\n      errorPatterns,\r\n      \r\n      // Habilidades cognitivas específicas\r\n      cognitiveSkills: {\r\n        patternRecognition: this.assessPatternRecognition(patternData),\r\n        sequentialThinking: this.assessSequentialThinking(patternData),\r\n        logicalReasoning: this.assessLogicalReasoning(patternData),\r\n        predictionAbility: this.assessPredictionSkill(patternData),\r\n        abstractThinking: this.assessAbstractThinking(patternData)\r\n      },\r\n      \r\n      // Análise de dificuldade\r\n      difficultyProgression: this.analyzeDifficultyProgression(patternData),\r\n      \r\n      // Recomendações adaptativas\r\n      adaptiveRecommendations: this.generateAdaptiveRecommendations(patternData),\r\n      \r\n      // Métricas de engajamento\r\n      engagementMetrics: this.calculateEngagementMetrics(patternData)\r\n    };\r\n\r\n    return analysis;\r\n  }\r\n\r\n  /**\r\n   * Calcula precisão geral\r\n   */\r\n  calculateOverallAccuracy(data) {\r\n    if (!data.attempts || data.attempts.length === 0) return 0.7;\r\n    \r\n    const correct = data.attempts.filter(attempt => attempt.correct).length;\r\n    return correct / data.attempts.length;\r\n  }\r\n\r\n  /**\r\n   * Analisa precisão por tipo de padrão\r\n   */\r\n  analyzeAccuracyByType(data) {\r\n    const accuracyByType = {};\r\n    \r\n    Object.keys(this.patternTypes).forEach(type => {\r\n      const typeAttempts = data.attempts?.filter(attempt => attempt.patternType === type) || [];\r\n      if (typeAttempts.length > 0) {\r\n        const correct = typeAttempts.filter(attempt => attempt.correct).length;\r\n        accuracyByType[type] = {\r\n          accuracy: correct / typeAttempts.length,\r\n          attempts: typeAttempts.length,\r\n          description: this.patternTypes[type]\r\n        };\r\n      }\r\n    });\r\n    \r\n    return accuracyByType;\r\n  }\r\n\r\n  /**\r\n   * Avalia velocidade de reconhecimento\r\n   */\r\n  assessRecognitionSpeed(data) {\r\n    if (!data.attempts || data.attempts.length === 0) {\r\n      return { average: 5000, category: 'average' };\r\n    }\r\n    \r\n    const responseTimes = data.attempts.map(attempt => attempt.responseTime || 5000);\r\n    const averageTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;\r\n    \r\n    let category = 'average';\r\n    if (averageTime < 2000) category = 'excellent';\r\n    else if (averageTime < 3000) category = 'good';\r\n    else if (averageTime < 5000) category = 'average';\r\n    else if (averageTime < 8000) category = 'poor';\r\n    else category = 'critical';\r\n    \r\n    return {\r\n      average: averageTime,\r\n      category,\r\n      distribution: this.calculateTimeDistribution(responseTimes)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa capacidade de lidar com complexidade\r\n   */\r\n  analyzeComplexityHandling(data) {\r\n    const complexityLevels = ['simple', 'medium', 'complex', 'advanced'];\r\n    const complexityAnalysis = {};\r\n    \r\n    complexityLevels.forEach(level => {\r\n      const levelAttempts = data.attempts?.filter(attempt => attempt.complexity === level) || [];\r\n      if (levelAttempts.length > 0) {\r\n        const correct = levelAttempts.filter(attempt => attempt.correct).length;\r\n        complexityAnalysis[level] = {\r\n          accuracy: correct / levelAttempts.length,\r\n          attempts: levelAttempts.length,\r\n          averageTime: levelAttempts.reduce((sum, attempt) => sum + (attempt.responseTime || 5000), 0) / levelAttempts.length\r\n        };\r\n      }\r\n    });\r\n    \r\n    return complexityAnalysis;\r\n  }\r\n\r\n  /**\r\n   * Avalia capacidade de predição\r\n   */\r\n  assessPredictionAbility(data) {\r\n    const predictionAttempts = data.attempts?.filter(attempt => attempt.type === 'prediction') || [];\r\n    \r\n    if (predictionAttempts.length === 0) {\r\n      return { accuracy: 0.7, confidence: 'medium' };\r\n    }\r\n    \r\n    const correct = predictionAttempts.filter(attempt => attempt.correct).length;\r\n    const accuracy = correct / predictionAttempts.length;\r\n    \r\n    let confidence = 'low';\r\n    if (accuracy >= 0.9) confidence = 'excellent';\r\n    else if (accuracy >= 0.8) confidence = 'high';\r\n    else if (accuracy >= 0.7) confidence = 'medium';\r\n    else if (accuracy >= 0.5) confidence = 'low';\r\n    else confidence = 'very-low';\r\n    \r\n    return {\r\n      accuracy,\r\n      confidence,\r\n      totalPredictions: predictionAttempts.length\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa progresso temporal\r\n   */\r\n  analyzeTemporalProgress(data) {\r\n    if (!data.attempts || data.attempts.length < 5) {\r\n      return { trend: 'insufficient-data', improvement: 0 };\r\n    }\r\n    \r\n    const attempts = data.attempts.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));\r\n    const firstHalf = attempts.slice(0, Math.floor(attempts.length / 2));\r\n    const secondHalf = attempts.slice(Math.floor(attempts.length / 2));\r\n    \r\n    const firstHalfAccuracy = firstHalf.filter(a => a.correct).length / firstHalf.length;\r\n    const secondHalfAccuracy = secondHalf.filter(a => a.correct).length / secondHalf.length;\r\n    \r\n    const improvement = secondHalfAccuracy - firstHalfAccuracy;\r\n    \r\n    let trend = 'stable';\r\n    if (improvement > 0.1) trend = 'improving';\r\n    else if (improvement < -0.1) trend = 'declining';\r\n    \r\n    return {\r\n      trend,\r\n      improvement,\r\n      firstHalfAccuracy,\r\n      secondHalfAccuracy\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analiza padrões de erro\r\n   */\r\n  analyzeErrorPatterns(data) {\r\n    const errors = data.attempts?.filter(attempt => !attempt.correct) || [];\r\n    \r\n    if (errors.length === 0) {\r\n      return { errorRate: 0, commonPatterns: [] };\r\n    }\r\n    \r\n    const errorTypes = {};\r\n    errors.forEach(error => {\r\n      const type = error.errorType || 'unknown';\r\n      errorTypes[type] = (errorTypes[type] || 0) + 1;\r\n    });\r\n    \r\n    const sortedErrors = Object.entries(errorTypes)\r\n      .sort(([,a], [,b]) => b - a)\r\n      .slice(0, 3)\r\n      .map(([type, count]) => ({ type, count, percentage: count / errors.length }));\r\n    \r\n    return {\r\n      errorRate: errors.length / data.attempts.length,\r\n      commonPatterns: sortedErrors,\r\n      totalErrors: errors.length\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calcula índice de reconhecimento de padrões\r\n   */\r\n  calculatePatternRecognitionIndex(data) {\r\n    const accuracy = this.calculateOverallAccuracy(data);\r\n    const speedScore = this.calculateSpeedScore(data);\r\n    const complexityScore = this.calculateComplexityScore(data);\r\n    \r\n    return (accuracy * 0.5 + speedScore * 0.25 + complexityScore * 0.25);\r\n  }\r\n\r\n  /**\r\n   * Calcula pontuação de velocidade\r\n   */\r\n  calculateSpeedScore(data) {\r\n    const speed = this.assessRecognitionSpeed(data);\r\n    const speedCategories = {\r\n      'excellent': 1.0,\r\n      'good': 0.8,\r\n      'average': 0.6,\r\n      'poor': 0.4,\r\n      'critical': 0.2\r\n    };\r\n    \r\n    return speedCategories[speed.category] || 0.6;\r\n  }\r\n\r\n  /**\r\n   * Calcula pontuação de complexidade\r\n   */\r\n  calculateComplexityScore(data) {\r\n    const complexityAnalysis = this.analyzeComplexityHandling(data);\r\n    \r\n    // Peso maior para níveis mais complexos\r\n    const weights = { simple: 0.1, medium: 0.3, complex: 0.4, advanced: 0.2 };\r\n    let weightedScore = 0;\r\n    let totalWeight = 0;\r\n    \r\n    Object.entries(complexityAnalysis).forEach(([level, analysis]) => {\r\n      const weight = weights[level] || 0.25;\r\n      weightedScore += analysis.accuracy * weight;\r\n      totalWeight += weight;\r\n    });\r\n    \r\n    return totalWeight > 0 ? weightedScore / totalWeight : 0.6;\r\n  }\r\n\r\n  /**\r\n   * Avalia habilidades cognitivas específicas\r\n   */\r\n  assessPatternRecognition(data) {\r\n    return this.calculateOverallAccuracy(data);\r\n  }\r\n\r\n  assessSequentialThinking(data) {\r\n    const sequentialAttempts = data.attempts?.filter(a => a.patternType === 'sequential') || [];\r\n    if (sequentialAttempts.length === 0) return 0.7;\r\n    \r\n    const correct = sequentialAttempts.filter(a => a.correct).length;\r\n    return correct / sequentialAttempts.length;\r\n  }\r\n\r\n  assessLogicalReasoning(data) {\r\n    const logicalAttempts = data.attempts?.filter(a => ['arithmetic', 'geometric'].includes(a.patternType)) || [];\r\n    if (logicalAttempts.length === 0) return 0.7;\r\n    \r\n    const correct = logicalAttempts.filter(a => a.correct).length;\r\n    return correct / logicalAttempts.length;\r\n  }\r\n\r\n  assessPredictionSkill(data) {\r\n    return this.assessPredictionAbility(data).accuracy;\r\n  }\r\n\r\n  assessAbstractThinking(data) {\r\n    const abstractAttempts = data.attempts?.filter(a => ['complex', 'visual'].includes(a.patternType)) || [];\r\n    if (abstractAttempts.length === 0) return 0.7;\r\n    \r\n    const correct = abstractAttempts.filter(a => a.correct).length;\r\n    return correct / abstractAttempts.length;\r\n  }\r\n\r\n  /**\r\n   * Analiza progressão de dificuldade\r\n   */\r\n  analyzeDifficultyProgression(data) {\r\n    // Implementação da análise de progressão de dificuldade\r\n    return {\r\n      currentLevel: 'medium',\r\n      suggestedNext: 'complex',\r\n      readiness: 0.8\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Gera recomendações adaptativas\r\n   */\r\n  generateAdaptiveRecommendations(data) {\r\n    const accuracy = this.calculateOverallAccuracy(data);\r\n    const speed = this.assessRecognitionSpeed(data);\r\n    \r\n    const recommendations = [];\r\n    \r\n    if (accuracy < 0.7) {\r\n      recommendations.push({\r\n        type: 'difficulty',\r\n        action: 'decrease',\r\n        reason: 'Baixa precisão detectada'\r\n      });\r\n    }\r\n    \r\n    if (speed.average > 8000) {\r\n      recommendations.push({\r\n        type: 'time',\r\n        action: 'extend',\r\n        reason: 'Tempo de resposta elevado'\r\n      });\r\n    }\r\n    \r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Calcula métricas de engajamento\r\n   */\r\n  calculateEngagementMetrics(data) {\r\n    return {\r\n      attentionLevel: 0.8,\r\n      motivationScore: 0.75,\r\n      frustrationLevel: 0.3\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calcula distribuição de tempos\r\n   */\r\n  calculateTimeDistribution(times) {\r\n    const sorted = times.sort((a, b) => a - b);\r\n    return {\r\n      min: Math.min(...times),\r\n      max: Math.max(...times),\r\n      median: sorted[Math.floor(sorted.length / 2)],\r\n      q1: sorted[Math.floor(sorted.length * 0.25)],\r\n      q3: sorted[Math.floor(sorted.length * 0.75)]\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Retorna análise padrão quando dados são insuficientes\r\n   */\r\n  getDefaultAnalysis() {\r\n    return {\r\n      timestamp: new Date().toISOString(),\r\n      collector: 'PatternRecognitionCollector',\r\n      version: '3.0.0',\r\n      overallAccuracy: 0.7,\r\n      patternRecognitionIndex: 0.7,\r\n      recognitionSpeed: { average: 5000, category: 'average' },\r\n      accuracyByType: {},\r\n      complexityAnalysis: {},\r\n      predictionAbility: { accuracy: 0.7, confidence: 'medium' },\r\n      temporalProgress: { trend: 'insufficient-data', improvement: 0 },\r\n      errorPatterns: { errorRate: 0.3, commonPatterns: [] },\r\n      cognitiveSkills: {\r\n        patternRecognition: 0.7,\r\n        sequentialThinking: 0.7,\r\n        logicalReasoning: 0.7,\r\n        predictionAbility: 0.7,\r\n        abstractThinking: 0.7\r\n      },\r\n      difficultyProgression: {\r\n        currentLevel: 'medium',\r\n        suggestedNext: 'complex',\r\n        readiness: 0.7\r\n      },\r\n      adaptiveRecommendations: [],\r\n      engagementMetrics: {\r\n        attentionLevel: 0.7,\r\n        motivationScore: 0.7,\r\n        frustrationLevel: 0.3\r\n      }\r\n    };\r\n  }\r\n}\r\n", "/**\r\n * 🔢 NUMBER COUNTING COLLECTORS HUB V3\r\n * Hub integrador para todos os coletores especializados do NumberCounting V3\r\n * Suporte para 6 atividades diversificadas\r\n * Portal Betina V3\r\n */\r\n\r\nimport { NumericalCognitionCollector } from './NumericalCognitionCollector.js';\r\nimport { AttentionFocusCollector } from './AttentionFocusCollector.js';\r\nimport { VisualProcessingCollector } from './VisualProcessingCollector.js';\r\nimport { MathematicalReasoningCollector } from './MathematicalReasoningCollector.js';\r\nimport { ErrorPatternCollector } from './ErrorPatternCollector.js';\r\nimport { EstimationSkillsCollector } from './EstimationSkillsCollector.js';\r\nimport { SequenceAnalysisCollector } from './SequenceAnalysisCollector.js';\r\nimport { ComparisonSkillsCollector } from './ComparisonSkillsCollector.js';\r\nimport { SoundMatchingCollector } from './SoundMatchingCollector.js';\r\nimport { PatternRecognitionCollector } from './PatternRecognitionCollector.js';\r\n\r\nexport class NumberCountingCollectorsHub {\r\n  constructor() {\r\n    this._collectors = {\r\n      numericalCognition: new NumericalCognitionCollector(),\r\n      attentionFocus: new AttentionFocusCollector(),\r\n      visualProcessing: new VisualProcessingCollector(),\r\n      mathematicalReasoning: new MathematicalReasoningCollector(),\r\n      errorPattern: new ErrorPatternCollector(),\r\n      estimationSkills: new EstimationSkillsCollector(),\r\n      sequenceAnalysis: new SequenceAnalysisCollector(),\r\n      comparisonSkills: new ComparisonSkillsCollector(),\r\n      soundMatching: new SoundMatchingCollector(),\r\n      patternRecognition: new PatternRecognitionCollector()\r\n    };\r\n\r\n    this.analysisHistory = [];\r\n    this.currentSession = null;\r\n    this.performanceBaseline = null;\r\n    this.cognitiveProfile = null;\r\n\r\n    // 🎯 CONFIGURAÇÕES ESPECÍFICAS V3 - PARA 6 ATIVIDADES\r\n    this.gameSpecificConfig = {\r\n      minAttempts: 3,\r\n      maxAnalysisTime: 5000,\r\n      significantChangeThreshold: 0.15,\r\n      adaptiveDifficultyEnabled: true,\r\n\r\n      // 🎯 CONFIGURAÇÕES POR ATIVIDADE V3\r\n      activityConfigs: {\r\n        number_counting: {\r\n          focusMetric: 'counting_accuracy',\r\n          timeWeight: 0.3,\r\n          accuracyWeight: 0.7,\r\n          patterns: ['sequential_errors', 'magnitude_errors']\r\n        },\r\n        sound_matching: {\r\n          focusMetric: 'auditory_processing',\r\n          timeWeight: 0.4,\r\n          accuracyWeight: 0.6,\r\n          patterns: ['audio_confusion', 'delay_patterns']\r\n        },\r\n        number_estimation: {\r\n          focusMetric: 'estimation_accuracy',\r\n          timeWeight: 0.2,\r\n          accuracyWeight: 0.8,\r\n          patterns: ['underestimation', 'overestimation', 'magnitude_bias']\r\n        },\r\n        sequence_completion: {\r\n          focusMetric: 'pattern_recognition',\r\n          timeWeight: 0.5,\r\n          accuracyWeight: 0.5,\r\n          patterns: ['sequence_logic', 'arithmetic_progression']\r\n        },\r\n        number_comparison: {\r\n          focusMetric: 'comparison_logic',\r\n          timeWeight: 0.3,\r\n          accuracyWeight: 0.7,\r\n          patterns: ['magnitude_comparison', 'relative_errors']\r\n        },\r\n        pattern_recognition: {\r\n          focusMetric: 'abstract_reasoning',\r\n          timeWeight: 0.6,\r\n          accuracyWeight: 0.4,\r\n          patterns: ['pattern_complexity', 'abstraction_level']\r\n        }\r\n      }\r\n    };\r\n\r\n    // 🧠 INICIALIZAR ANÁLISE COMPORTAMENTAL V3\r\n    this.behavioralAnalysis = {\r\n      activityPreferences: {},\r\n      learningProgression: {},\r\n      cognitivePatterns: {},\r\n      adaptiveRecommendations: []\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Getter para coletores - necessário para GameSpecificProcessors\r\n   */\r\n  get collectors() {\r\n    return this._collectors;\r\n  }\r\n\r\n  /**\r\n   * 🎯 EXECUTA ANÁLISE COMPLETA V3 - SUPORTE PARA 6 ATIVIDADES\r\n   */\r\n  async runCompleteAnalysis(gameData) {\r\n    try {\r\n      console.log('🔢 Iniciando análise completa do NumberCounting V3...');\r\n\r\n      if (!this.validateGameData(gameData)) {\r\n        throw new Error('Dados do jogo inválidos para análise V3');\r\n      }\r\n\r\n      const startTime = Date.now();\r\n\r\n      // 🎯 PREPARAR DADOS ESPECÍFICOS PARA CADA COLETOR V3\r\n      const collectorData = this.prepareCollectorDataV3(gameData);\r\n\r\n      // 🎯 ANÁLISE POR ATIVIDADE V3\r\n      const activityAnalysis = await this.analyzeByActivity(gameData);\r\n\r\n      // Executar todos os coletores em paralelo para eficiência\r\n      const analysisPromises = [\r\n        this.collectors.numericalCognition.analyze(collectorData.numericalCognition),\r\n        this.collectors.attentionFocus.analyze(collectorData.attentionFocus),\r\n        this.collectors.visualProcessing.analyze(collectorData.visualProcessing),\r\n        this.collectors.mathematicalReasoning.analyze(collectorData.mathematicalReasoning),\r\n        this.collectors.errorPattern.analyze(collectorData.errorPattern),\r\n        this.collectors.estimationSkills.analyze(collectorData.estimation),\r\n        this.collectors.sequenceAnalysis.analyze(collectorData.sequence),\r\n        this.collectors.comparisonSkills.analyze(collectorData.comparison),\r\n        this.collectors.soundMatching.analyze(collectorData.sound),\r\n        this.collectors.patternRecognition.analyze(collectorData.pattern)\r\n      ];\r\n\r\n      const [\r\n        numericalCognitionResults,\r\n        attentionFocusResults,\r\n        visualProcessingResults,\r\n        mathematicalReasoningResults,\r\n        errorPatternResults,\r\n        estimationSkillsResults,\r\n        sequenceAnalysisResults,\r\n        comparisonSkillsResults,\r\n        soundMatchingResults,\r\n        patternRecognitionResults\r\n      ] = await Promise.all(analysisPromises);\r\n\r\n      // 🎯 INTEGRAR RESULTADOS COM ANÁLISE POR ATIVIDADE V3\r\n      const integratedAnalysis = this.integrateAnalysisResultsV3({\r\n        numericalCognition: numericalCognitionResults,\r\n        attentionFocus: attentionFocusResults,\r\n        visualProcessing: visualProcessingResults,\r\n        mathematicalReasoning: mathematicalReasoningResults,\r\n        errorPattern: errorPatternResults,\r\n        estimationSkills: estimationSkillsResults,\r\n        sequenceAnalysis: sequenceAnalysisResults,\r\n        comparisonSkills: comparisonSkillsResults,\r\n        soundMatching: soundMatchingResults,\r\n        patternRecognition: patternRecognitionResults,\r\n        activityAnalysis\r\n      }, gameData);\r\n\r\n      // 🧠 CALCULAR MÉTRICAS DE SÍNTESE V3\r\n      const synthesisMetrics = this.calculateSynthesisMetricsV3(integratedAnalysis, gameData);\r\n\r\n      // 🎯 GERAR INSIGHTS COGNITIVOS ESPECÍFICOS V3\r\n      const cognitiveInsights = this.generateCognitiveInsightsV3(integratedAnalysis, gameData);\r\n\r\n      // 🎯 CRIAR PERFIL DE DESENVOLVIMENTO V3\r\n      const developmentProfile = this.createDevelopmentProfileV3(integratedAnalysis, activityAnalysis);\r\n\r\n      // 🎯 GERAR RECOMENDAÇÕES ADAPTATIVAS V3\r\n      const adaptiveRecommendations = this.generateAdaptiveRecommendations(integratedAnalysis, activityAnalysis);\r\n\r\n      const analysisTime = Date.now() - startTime;\r\n\r\n      const completeAnalysis = {\r\n        timestamp: new Date().toISOString(),\r\n        gameId: 'numbercounting_v3',\r\n        sessionId: gameData.sessionId,\r\n        analysisTime,\r\n        version: '3.0.0',\r\n\r\n        // 🎯 RESULTADOS INDIVIDUAIS DOS COLETORES V3\r\n        detailedResults: {\r\n          numericalCognition: numericalCognitionResults,\r\n          attentionFocus: attentionFocusResults,\r\n          visualProcessing: visualProcessingResults,\r\n          mathematicalReasoning: mathematicalReasoningResults,\r\n          errorPattern: errorPatternResults,\r\n          estimationSkills: estimationSkillsResults,\r\n          sequenceAnalysis: sequenceAnalysisResults,\r\n          comparisonSkills: comparisonSkillsResults,\r\n          soundMatching: soundMatchingResults,\r\n          patternRecognition: patternRecognitionResults\r\n        },\r\n\r\n        // 🎯 ANÁLISE POR ATIVIDADE V3\r\n        activityAnalysis,\r\n\r\n        // Análise integrada\r\n        integratedAnalysis,\r\n        synthesisMetrics,\r\n        cognitiveInsights,\r\n        developmentProfile,\r\n        adaptiveRecommendations,\r\n\r\n        // 📊 METADADOS DA ANÁLISE V3\r\n        metadata: {\r\n          totalAttempts: gameData.attempts?.length || 0,\r\n          activitiesPlayed: this.getActivitiesPlayed(gameData),\r\n          difficulty: gameData.difficulty,\r\n          accuracy: this.calculateOverallAccuracy(gameData),\r\n          avgResponseTime: this.calculateAverageResponseTime(gameData),\r\n          dataQuality: this.assessDataQuality(gameData),\r\n          engagementLevel: this.calculateEngagementLevel(gameData),\r\n          cognitiveLoad: this.estimateCognitiveLoad(gameData)\r\n        }\r\n      };\r\n\r\n      // Armazenar na história\r\n      this.analysisHistory.push(completeAnalysis);\r\n\r\n      // 🧠 ATUALIZAR PERFIL COGNITIVO V3\r\n      this.updateCognitiveProfileV3(completeAnalysis);\r\n\r\n      console.log(`✅ Análise NumberCounting V3 concluída em ${analysisTime}ms`);\r\n\r\n      return completeAnalysis;\r\n\r\n    } catch (error) {\r\n      console.error('❌ Erro na análise completa NumberCounting V3:', error);\r\n      return this.getErrorAnalysisV3(error, gameData);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Alias para runCompleteAnalysis para compatibilidade\r\n   */\r\n  async processGameData(gameData) {\r\n    return await this.runCompleteAnalysis(gameData);\r\n  }\r\n\r\n  /**\r\n   * Valida os dados do jogo antes da análise\r\n   */\r\n  validateGameData(gameData) {\r\n    if (!gameData) {\r\n      console.warn('NumberCountingCollectorsHub: Dados do jogo não fornecidos');\r\n      return false;\r\n    }\r\n\r\n    if (!gameData.attempts || !Array.isArray(gameData.attempts)) {\r\n      console.warn('NumberCountingCollectorsHub: Tentativas do jogo não encontradas');\r\n      return false;\r\n    }\r\n\r\n    if (gameData.attempts.length < this.gameSpecificConfig.minAttempts) {\r\n      console.warn(`NumberCountingCollectorsHub: Número insuficiente de tentativas (${gameData.attempts.length})`);\r\n      return false;\r\n    }\r\n\r\n    const validAttempts = gameData.attempts.every(attempt =>\r\n      typeof attempt.correctAnswer === 'number' &&\r\n      typeof attempt.userAnswer === 'number' &&\r\n      typeof attempt.responseTime === 'number' &&\r\n      typeof attempt.isCorrect === 'boolean'\r\n    );\r\n\r\n    if (!validAttempts) {\r\n      console.warn('NumberCountingCollectorsHub: Estrutura das tentativas inválida');\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * 🎯 ANÁLISE POR ATIVIDADE V3\r\n   */\r\n  async analyzeByActivity(gameData) {\r\n    console.log('🎯 Analisando por atividade V3...');\r\n\r\n    const activityData = {};\r\n    const activitiesPlayed = this.getActivitiesPlayed(gameData);\r\n\r\n    for (const activity of activitiesPlayed) {\r\n      const activityAttempts = this.getActivityAttempts(gameData, activity);\r\n\r\n      if (activityAttempts.length > 0) {\r\n        activityData[activity] = {\r\n          totalAttempts: activityAttempts.length,\r\n          correctAnswers: activityAttempts.filter(a => a.isCorrect).length,\r\n          accuracy: this.calculateActivityAccuracy(activityAttempts),\r\n          averageResponseTime: this.calculateActivityAverageTime(activityAttempts),\r\n          learningCurve: this.calculateLearningCurve(activityAttempts),\r\n          errorPatterns: this.identifyActivityErrorPatterns(activityAttempts, activity),\r\n          cognitiveMetrics: this.calculateActivityCognitiveMetrics(activityAttempts, activity),\r\n          difficultyProgression: this.analyzeDifficultyProgression(activityAttempts),\r\n          engagementIndicators: this.calculateActivityEngagement(activityAttempts)\r\n        };\r\n      }\r\n    }\r\n\r\n    return activityData;\r\n  }\r\n\r\n  /**\r\n   * 🎯 OBTER ATIVIDADES JOGADAS\r\n   */\r\n  getActivitiesPlayed(gameData) {\r\n    if (!gameData || !gameData.attempts) return [];\r\n\r\n    const activities = new Set();\r\n    gameData.attempts.forEach(attempt => {\r\n      if (attempt.activityType) {\r\n        activities.add(attempt.activityType);\r\n      }\r\n    });\r\n\r\n    return Array.from(activities);\r\n  }\r\n\r\n  /**\r\n   * 🎯 OBTER TENTATIVAS DE UMA ATIVIDADE ESPECÍFICA\r\n   */\r\n  getActivityAttempts(gameData, activity) {\r\n    if (!gameData || !gameData.attempts) return [];\r\n    return gameData.attempts.filter(attempt => attempt.activityType === activity);\r\n  }\r\n\r\n  /**\r\n   * Identifica eventos de distração\r\n   */\r\n  identifyDistractionEvents(gameData) {\r\n    if (!gameData || !gameData.attempts) return [];\r\n\r\n    const distractionEvents = [];\r\n\r\n    gameData.attempts.forEach((attempt, index) => {\r\n      if (attempt.responseTime && attempt.responseTime > 10000) {\r\n        distractionEvents.push({\r\n          attemptIndex: index,\r\n          type: 'slow_response',\r\n          duration: attempt.responseTime,\r\n          timestamp: attempt.timestamp\r\n        });\r\n      }\r\n\r\n      if (index > 0 && !attempt.isCorrect && !gameData.attempts[index - 1].isCorrect) {\r\n        distractionEvents.push({\r\n          attemptIndex: index,\r\n          type: 'consecutive_errors',\r\n          timestamp: attempt.timestamp\r\n        });\r\n      }\r\n    });\r\n\r\n    return distractionEvents;\r\n  }\r\n\r\n  /**\r\n   * Calcula métricas de foco\r\n   */\r\n  calculateFocusMetrics(gameData) {\r\n    if (!gameData || !gameData.attempts) {\r\n      return { avgResponseTime: 0, consistency: 0, attentionSpan: 0 };\r\n    }\r\n\r\n    const responseTimes = gameData.attempts\r\n      .filter(attempt => attempt.responseTime)\r\n      .map(attempt => attempt.responseTime);\r\n\r\n    const avgResponseTime = responseTimes.length > 0\r\n      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length\r\n      : 0;\r\n\r\n    const variance = responseTimes.length > 1\r\n      ? responseTimes.reduce((sum, time) => sum + Math.pow(time - avgResponseTime, 2), 0) / responseTimes.length\r\n      : 0;\r\n\r\n    const consistency = variance > 0 ? 1 / (1 + Math.sqrt(variance) / avgResponseTime) : 1;\r\n\r\n    const attentionSpan = this.calculateAttentionSpan(gameData);\r\n\r\n    return {\r\n      avgResponseTime,\r\n      consistency: Math.max(0, Math.min(1, consistency)),\r\n      attentionSpan\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calcula o span de atenção\r\n   */\r\n  calculateAttentionSpan(gameData) {\r\n    if (!gameData || !gameData.attempts) return 0;\r\n\r\n    let maxSpan = 0;\r\n    let currentSpan = 0;\r\n\r\n    gameData.attempts.forEach(attempt => {\r\n      if (attempt.responseTime && attempt.responseTime < 8000) {\r\n        currentSpan++;\r\n        maxSpan = Math.max(maxSpan, currentSpan);\r\n      } else {\r\n        currentSpan = 0;\r\n      }\r\n    });\r\n\r\n    return maxSpan;\r\n  }\r\n\r\n  /**\r\n   * Avalia a complexidade visual\r\n   */\r\n  assessVisualComplexity(gameData) {\r\n    if (!gameData || !gameData.attempts) return 'low';\r\n\r\n    let complexityScore = 0;\r\n\r\n    gameData.attempts.forEach(attempt => {\r\n      if (attempt.numbers && attempt.numbers.length > 5) complexityScore += 2;\r\n      if (attempt.patterns && attempt.patterns.length > 3) complexityScore += 2;\r\n      if (attempt.visualElements && attempt.visualElements > 10) complexityScore += 1;\r\n      if (attempt.difficulty && attempt.difficulty > 3) complexityScore += 1;\r\n    });\r\n\r\n    const avgComplexity = gameData.attempts.length > 0 ? complexityScore / gameData.attempts.length : 0;\r\n\r\n    if (avgComplexity > 3) return 'high';\r\n    if (avgComplexity > 1.5) return 'medium';\r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Extrai padrões espaciais\r\n   */\r\n  extractSpatialPatterns(gameData) {\r\n    if (!gameData || !gameData.attempts) return [];\r\n\r\n    const patterns = [];\r\n\r\n    gameData.attempts.forEach(attempt => {\r\n      if (attempt.position) {\r\n        patterns.push(attempt.position);\r\n      }\r\n      if (attempt.spatialArrangement) {\r\n        patterns.push(attempt.spatialArrangement);\r\n      }\r\n    });\r\n\r\n    return patterns;\r\n  }\r\n\r\n  /**\r\n   * Extrai tipos de raciocínio\r\n   */\r\n  extractReasoningTypes(gameData) {\r\n    if (!gameData || !gameData.attempts) return ['basic_counting'];\r\n\r\n    const reasoningTypes = new Set();\r\n\r\n    gameData.attempts.forEach(attempt => {\r\n      if (attempt.activityType === 'number_comparison') {\r\n        reasoningTypes.add('comparative_reasoning');\r\n      }\r\n      if (attempt.activityType === 'sequence_completion') {\r\n        reasoningTypes.add('sequential_reasoning');\r\n      }\r\n      if (attempt.activityType === 'number_estimation') {\r\n        reasoningTypes.add('estimation_reasoning');\r\n      }\r\n      if (attempt.activityType === 'pattern_recognition') {\r\n        reasoningTypes.add('pattern_reasoning');\r\n      }\r\n      if (attempt.isCorrect && attempt.responseTime < 2000) {\r\n        reasoningTypes.add('intuitive_reasoning');\r\n      }\r\n    });\r\n\r\n    if (reasoningTypes.size === 0) {\r\n      reasoningTypes.add('basic_counting');\r\n    }\r\n\r\n    return Array.from(reasoningTypes);\r\n  }\r\n\r\n  /**\r\n   * Avalia a complexidade do problema\r\n   */\r\n  assessProblemComplexity(gameData) {\r\n    if (!gameData || !gameData.attempts) return 'low';\r\n\r\n    let complexityScore = 0;\r\n\r\n    gameData.attempts.forEach(attempt => {\r\n      if (attempt.difficulty) complexityScore += attempt.difficulty;\r\n      if (attempt.numbers && attempt.numbers.some(n => n > 20)) complexityScore += 1;\r\n      if (attempt.operationSteps && attempt.operationSteps > 1) complexityScore += 2;\r\n    });\r\n\r\n    const avgComplexity = gameData.attempts.length > 0 ? complexityScore / gameData.attempts.length : 0;\r\n\r\n    if (avgComplexity > 4) return 'high';\r\n    if (avgComplexity > 2) return 'medium';\r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Categoriza erros\r\n   */\r\n  categorizeErrors(gameData) {\r\n    if (!gameData || !gameData.attempts) return {};\r\n\r\n    const errorTypes = {};\r\n\r\n    gameData.attempts.forEach(attempt => {\r\n      if (!attempt.isCorrect) {\r\n        const errorType = this.identifyErrorType(attempt);\r\n        errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;\r\n      }\r\n    });\r\n\r\n    return errorTypes;\r\n  }\r\n\r\n  /**\r\n   * Identifica tipo de erro\r\n   */\r\n  identifyErrorType(attempt) {\r\n    if (!attempt.answer || !attempt.targetValue) return 'unknown';\r\n\r\n    const difference = Math.abs(attempt.answer - attempt.targetValue);\r\n\r\n    if (difference === 1) return 'off_by_one';\r\n    if (difference <= 3) return 'close_estimate';\r\n    if (attempt.answer > attempt.targetValue * 2) return 'significant_overestimate';\r\n    if (attempt.answer < attempt.targetValue / 2) return 'significant_underestimate';\r\n    if (difference > 10) return 'major_miscalculation';\r\n\r\n    return 'moderate_error';\r\n  }\r\n\r\n  /**\r\n   * Analisa padrões de recuperação\r\n   */\r\n  analyzeRecoveryPatterns(gameData) {\r\n    if (!gameData || !gameData.attempts) return {};\r\n\r\n    const recoveryPatterns = {\r\n      immediateRecovery: 0,\r\n      gradualRecovery: 0,\r\n      persistentErrors: 0\r\n    };\r\n\r\n    for (let i = 1; i < gameData.attempts.length; i++) {\r\n      const prevAttempt = gameData.attempts[i - 1];\r\n      const currentAttempt = gameData.attempts[i];\r\n\r\n      if (!prevAttempt.isCorrect && currentAttempt.isCorrect) {\r\n        recoveryPatterns.immediateRecovery++;\r\n      } else if (!prevAttempt.isCorrect && !currentAttempt.isCorrect) {\r\n        recoveryPatterns.persistentErrors++;\r\n      }\r\n    }\r\n\r\n    return recoveryPatterns;\r\n  }\r\n\r\n  /**\r\n   * 🎯 PREPARAR DADOS PARA COLETORES V3\r\n   */\r\n  prepareCollectorDataV3(gameData) {\r\n    const baseData = {\r\n      attempts: gameData.attempts,\r\n      sessionId: gameData.sessionId,\r\n      difficulty: gameData.difficulty,\r\n      activitiesPlayed: this.getActivitiesPlayed(gameData),\r\n      sessionDuration: gameData.sessionDuration || 0,\r\n      timestamp: Date.now()\r\n    };\r\n\r\n    return {\r\n      numericalCognition: {\r\n        ...baseData,\r\n        focus: 'numerical_concepts',\r\n        numericalRange: this.extractNumericalRange(gameData),\r\n        operationTypes: this.extractOperationTypes(gameData)\r\n      },\r\n      attentionFocus: {\r\n        ...baseData,\r\n        focus: 'sustained_attention',\r\n        distractionEvents: this.identifyDistractionEvents(gameData),\r\n        focusMetrics: this.calculateFocusMetrics(gameData)\r\n      },\r\n      visualProcessing: {\r\n        ...baseData,\r\n        focus: 'visual_numerical_processing',\r\n        visualComplexity: this.assessVisualComplexity(gameData),\r\n        spatialPatterns: this.extractSpatialPatterns(gameData)\r\n      },\r\n      mathematicalReasoning: {\r\n        ...baseData,\r\n        focus: 'mathematical_logic',\r\n        reasoningTypes: this.extractReasoningTypes(gameData),\r\n        problemComplexity: this.assessProblemComplexity(gameData)\r\n      },\r\n      errorPattern: {\r\n        ...baseData,\r\n        focus: 'error_analysis',\r\n        errorTypes: this.categorizeErrors(gameData),\r\n        recoveryPatterns: this.analyzeRecoveryPatterns(gameData)\r\n      },\r\n      estimation: {\r\n        ...baseData,\r\n        numberEstimation: this.filterByActivity(gameData, 'number_estimation'),\r\n        focus: 'estimation_skills'\r\n      },\r\n      sequence: {\r\n        ...baseData,\r\n        sequenceCompletion: this.filterByActivity(gameData, 'sequence_completion'),\r\n        focus: 'sequence_analysis'\r\n      },\r\n      comparison: {\r\n        ...baseData,\r\n        numberComparison: this.filterByActivity(gameData, 'number_comparison'),\r\n        focus: 'comparison_skills'\r\n      },\r\n      sound: {\r\n        ...baseData,\r\n        soundMatching: this.filterByActivity(gameData, 'sound_matching'),\r\n        focus: 'auditory_processing'\r\n      },\r\n      pattern: {\r\n        ...baseData,\r\n        patternRecognition: this.filterByActivity(gameData, 'pattern_recognition'),\r\n        focus: 'pattern_analysis'\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Filtra dados por atividade específica\r\n   */\r\n  filterByActivity(gameData, activityType) {\r\n    if (!gameData.attempts) return [];\r\n    return gameData.attempts.filter(attempt => attempt.activityType === activityType);\r\n  }\r\n\r\n  /**\r\n   * Extrai faixa numérica\r\n   */\r\n  extractNumericalRange(gameData) {\r\n    if (!gameData || !gameData.attempts) {\r\n      return { min: 0, max: 10 };\r\n    }\r\n\r\n    let minValue = Infinity;\r\n    let maxValue = -Infinity;\r\n\r\n    gameData.attempts.forEach(attempt => {\r\n      if (attempt.numbers && Array.isArray(attempt.numbers)) {\r\n        attempt.numbers.forEach(num => {\r\n          if (typeof num === 'number') {\r\n            minValue = Math.min(minValue, num);\r\n            maxValue = Math.max(maxValue, num);\r\n          }\r\n        });\r\n      }\r\n\r\n      if (typeof attempt.answer === 'number') {\r\n        minValue = Math.min(minValue, attempt.answer);\r\n        maxValue = Math.max(maxValue, attempt.answer);\r\n      }\r\n\r\n      if (typeof attempt.targetValue === 'number') {\r\n        minValue = Math.min(minValue, attempt.targetValue);\r\n        maxValue = Math.max(maxValue, attempt.targetValue);\r\n      }\r\n    });\r\n\r\n    return minValue === Infinity || maxValue === -Infinity ? { min: 0, max: 10 } : { min: minValue, max: maxValue };\r\n  }\r\n\r\n  /**\r\n   * Extrai tipos de operação\r\n   */\r\n  extractOperationTypes(gameData) {\r\n    if (!gameData || !gameData.attempts) {\r\n      return ['counting'];\r\n    }\r\n\r\n    const operationTypes = new Set();\r\n\r\n    gameData.attempts.forEach(attempt => {\r\n      if (attempt.activityType) operationTypes.add(attempt.activityType);\r\n      if (attempt.operationType) operationTypes.add(attempt.operationType);\r\n      if (attempt.numbers && attempt.numbers.length > 1) operationTypes.add('comparison');\r\n      if (attempt.isEstimation) operationTypes.add('estimation');\r\n      if (attempt.hasSound || attempt.soundFile) operationTypes.add('auditory_matching');\r\n      if (attempt.patterns) operationTypes.add('pattern_recognition');\r\n    });\r\n\r\n    return Array.from(operationTypes);\r\n  }\r\n\r\n  /**\r\n   * Extrai padrões de resposta\r\n   */\r\n  extractResponsePatterns(gameData) {\r\n    if (!gameData || !gameData.attempts) {\r\n      return { patterns: [], frequency: {} };\r\n    }\r\n\r\n    const patterns = [];\r\n    const frequency = {};\r\n\r\n    gameData.attempts.forEach(attempt => {\r\n      if (attempt.responsePattern) {\r\n        patterns.push(attempt.responsePattern);\r\n        frequency[attempt.responsePattern] = (frequency[attempt.responsePattern] || 0) + 1;\r\n      }\r\n\r\n      if (attempt.responseTime && attempt.responseTime < 1000) {\r\n        patterns.push('quick_response');\r\n        frequency['quick_response'] = (frequency['quick_response'] || 0) + 1;\r\n      }\r\n\r\n      if (attempt.confidence && attempt.confidence < 0.5) {\r\n        patterns.push('low_confidence');\r\n        frequency['low_confidence'] = (frequency['low_confidence'] || 0) + 1;\r\n      }\r\n    });\r\n\r\n    return { patterns, frequency };\r\n  }\r\n\r\n  /**\r\n   * Extrai progressão de dificuldade\r\n   */\r\n  extractDifficultyProgression(gameData) {\r\n    if (!gameData || !gameData.attempts) {\r\n      return { levels: [], progression: 'stable' };\r\n    }\r\n\r\n    const levels = [];\r\n    let lastLevel = null;\r\n    let progressionType = 'stable';\r\n\r\n    gameData.attempts.forEach(attempt => {\r\n      if (attempt.difficulty !== undefined) {\r\n        levels.push(attempt.difficulty);\r\n\r\n        if (lastLevel !== null) {\r\n          if (attempt.difficulty > lastLevel) {\r\n            progressionType = 'increasing';\r\n          } else if (attempt.difficulty < lastLevel) {\r\n            progressionType = 'decreasing';\r\n          }\r\n        }\r\n\r\n        lastLevel = attempt.difficulty;\r\n      }\r\n    });\r\n\r\n    return { levels, progression: progressionType };\r\n  }\r\n\r\n  /**\r\n   * Extrai métricas de desempenho\r\n   */\r\n  extractPerformanceMetrics(gameData) {\r\n    if (!gameData || !gameData.attempts) {\r\n      return {\r\n        accuracy: 0,\r\n        averageResponseTime: 0,\r\n        totalAttempts: 0,\r\n        correctAttempts: 0\r\n      };\r\n    }\r\n\r\n    const totalAttempts = gameData.attempts.length;\r\n    let correctAttempts = 0;\r\n    let totalResponseTime = 0;\r\n    let responseTimeCount = 0;\r\n\r\n    gameData.attempts.forEach(attempt => {\r\n      if (attempt.isCorrect) correctAttempts++;\r\n      if (attempt.responseTime && typeof attempt.responseTime === 'number') {\r\n        totalResponseTime += attempt.responseTime;\r\n        responseTimeCount++;\r\n      }\r\n    });\r\n\r\n    const accuracy = totalAttempts > 0 ? correctAttempts / totalAttempts : 0;\r\n    const averageResponseTime = responseTimeCount > 0 ? totalResponseTime / responseTimeCount : 0;\r\n\r\n    return {\r\n      accuracy,\r\n      averageResponseTime,\r\n      totalAttempts,\r\n      correctAttempts\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calcula acurácia geral\r\n   */\r\n  calculateOverallAccuracy(gameData) {\r\n    const metrics = this.extractPerformanceMetrics(gameData);\r\n    return metrics.accuracy;\r\n  }\r\n\r\n  /**\r\n   * Calcula tempo médio de resposta\r\n   */\r\n  calculateAverageResponseTime(gameData) {\r\n    const metrics = this.extractPerformanceMetrics(gameData);\r\n    return metrics.averageResponseTime;\r\n  }\r\n\r\n  /**\r\n   * Avalia qualidade dos dados\r\n   */\r\n  assessDataQuality(gameData) {\r\n    if (!gameData || !gameData.attempts) return 'poor';\r\n\r\n    const validAttempts = gameData.attempts.every(attempt =>\r\n      attempt.correctAnswer !== undefined &&\r\n      attempt.userAnswer !== undefined &&\r\n      attempt.responseTime !== undefined &&\r\n      attempt.isCorrect !== undefined\r\n    );\r\n\r\n    const sufficientAttempts = gameData.attempts.length >= this.gameSpecificConfig.minAttempts;\r\n    const variety = new Set(gameData.attempts.map(a => a.activityType)).size > 1;\r\n\r\n    if (validAttempts && sufficientAttempts && variety) return 'high';\r\n    if (validAttempts && sufficientAttempts) return 'medium';\r\n    return 'poor';\r\n  }\r\n\r\n  /**\r\n   * Calcula nível de engajamento\r\n   */\r\n  calculateEngagementLevel(gameData) {\r\n    if (!gameData || !gameData.attempts) return 0;\r\n\r\n    const totalAttempts = gameData.attempts.length;\r\n    const responseTimes = gameData.attempts\r\n      .filter(a => a.responseTime)\r\n      .map(a => a.responseTime);\r\n    const avgResponseTime = responseTimes.length > 0\r\n      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length\r\n      : 0;\r\n\r\n    const engagementScore = totalAttempts * 0.4 + (avgResponseTime < 5000 ? 0.6 : 0.3);\r\n    return Math.min(1, Math.max(0, engagementScore));\r\n  }\r\n\r\n  /**\r\n   * Estima carga cognitiva\r\n   */\r\n  estimateCognitiveLoad(gameData) {\r\n    if (!gameData || !gameData.attempts) return 'low';\r\n\r\n    const complexity = this.assessProblemComplexity(gameData);\r\n    const responseTimes = gameData.attempts\r\n      .filter(a => a.responseTime)\r\n      .map(a => a.responseTime);\r\n    const avgResponseTime = responseTimes.length > 0\r\n      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length\r\n      : 0;\r\n\r\n    if (complexity === 'high' || avgResponseTime > 7000) return 'high';\r\n    if (complexity === 'medium' || avgResponseTime > 4000) return 'medium';\r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Calcula acurácia por atividade\r\n   */\r\n  calculateActivityAccuracy(activityAttempts) {\r\n    if (!activityAttempts || activityAttempts.length === 0) return 0;\r\n    const correct = activityAttempts.filter(a => a.isCorrect).length;\r\n    return correct / activityAttempts.length;\r\n  }\r\n\r\n  /**\r\n   * Calcula tempo médio por atividade\r\n   */\r\n  calculateActivityAverageTime(activityAttempts) {\r\n    if (!activityAttempts || activityAttempts.length === 0) return 0;\r\n    const times = activityAttempts.filter(a => a.responseTime).map(a => a.responseTime);\r\n    return times.length > 0 ? times.reduce((sum, time) => sum + time, 0) / times.length : 0;\r\n  }\r\n\r\n  /**\r\n   * Calcula curva de aprendizado\r\n   */\r\n  calculateLearningCurve(activityAttempts) {\r\n    if (!activityAttempts || activityAttempts.length < 2) return [];\r\n\r\n    const curve = [];\r\n    let correctStreak = 0;\r\n\r\n    activityAttempts.forEach(attempt => {\r\n      if (attempt.isCorrect) {\r\n        correctStreak++;\r\n      } else {\r\n        correctStreak = 0;\r\n      }\r\n      curve.push({ attempt: attempt, streak: correctStreak });\r\n    });\r\n\r\n    return curve;\r\n  }\r\n\r\n  /**\r\n   * Identifica padrões de erro por atividade\r\n   */\r\n  identifyActivityErrorPatterns(activityAttempts, activity) {\r\n    if (!activityAttempts || activityAttempts.length === 0) return [];\r\n\r\n    const patterns = this.gameSpecificConfig.activityConfigs[activity]?.patterns || [];\r\n    const errors = activityAttempts.filter(a => !a.isCorrect).map(a => this.identifyErrorType(a));\r\n    return [...new Set([...patterns, ...errors])];\r\n  }\r\n\r\n  /**\r\n   * Calcula métricas cognitivas por atividade\r\n   */\r\n  calculateActivityCognitiveMetrics(activityAttempts, activity) {\r\n    if (!activityAttempts || activityAttempts.length === 0) return {};\r\n\r\n    const config = this.gameSpecificConfig.activityConfigs[activity] || {};\r\n    const accuracy = this.calculateActivityAccuracy(activityAttempts);\r\n    const avgTime = this.calculateActivityAverageTime(activityAttempts);\r\n\r\n    return {\r\n      focusMetric: config.focusMetric || 'unknown',\r\n      weightedScore: (accuracy * (config.accuracyWeight || 0.5)) + (Math.max(0, 1 - avgTime / 10000) * (config.timeWeight || 0.5))\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa progressão de dificuldade\r\n   */\r\n  analyzeDifficultyProgression(activityAttempts) {\r\n    return this.extractDifficultyProgression({ attempts: activityAttempts });\r\n  }\r\n\r\n  /**\r\n   * Calcula indicadores de engajamento por atividade\r\n   */\r\n  calculateActivityEngagement(activityAttempts) {\r\n    if (!activityAttempts || activityAttempts.length === 0) return 0;\r\n    const totalAttempts = activityAttempts.length;\r\n    const avgTime = this.calculateActivityAverageTime(activityAttempts);\r\n    return Math.min(1, totalAttempts * 0.4 + (avgTime < 5000 ? 0.6 : 0.3));\r\n  }\r\n\r\n  /**\r\n   * Integra resultados da análise V3\r\n   */\r\n  integrateAnalysisResultsV3(results, gameData) {\r\n    const integrated = {\r\n      numericalSkills: results.numericalCognition.score || 0,\r\n      attentionMetrics: results.attentionFocus.metrics || {},\r\n      visualProcessing: results.visualProcessing.complexity || 'low',\r\n      reasoningAbility: results.mathematicalReasoning.level || 'basic',\r\n      errorPatterns: results.errorPattern.patterns || [],\r\n      estimationAccuracy: results.estimationSkills.accuracy || 0,\r\n      sequenceSkills: results.sequenceAnalysis.score || 0,\r\n      comparisonSkills: results.comparisonSkills.score || 0,\r\n      auditoryProcessing: results.soundMatching.score || 0,\r\n      patternRecognition: results.patternRecognition.score || 0,\r\n      activityPerformance: results.activityAnalysis\r\n    };\r\n\r\n    return integrated;\r\n  }\r\n\r\n  /**\r\n   * Calcula métricas de síntese V3\r\n   */\r\n  calculateSynthesisMetricsV3(integratedAnalysis, gameData) {\r\n    const weights = {\r\n      numericalSkills: 0.2,\r\n      attentionMetrics: 0.15,\r\n      visualProcessing: 0.15,\r\n      reasoningAbility: 0.2,\r\n      estimationAccuracy: 0.15,\r\n      sequenceSkills: 0.1,\r\n      comparisonSkills: 0.1,\r\n      auditoryProcessing: 0.05,\r\n      patternRecognition: 0.05\r\n    };\r\n\r\n    let compositeScore = 0;\r\n    Object.keys(weights).forEach(key => {\r\n      const value = typeof integratedAnalysis[key] === 'number' ? integratedAnalysis[key] : 0;\r\n      compositeScore += value * weights[key];\r\n    });\r\n\r\n    return {\r\n      compositeScore,\r\n      performanceTrend: this.analyzePerformanceTrend(gameData),\r\n      cognitiveLoad: this.estimateCognitiveLoad(gameData)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Gera insights cognitivos V3\r\n   */\r\n  generateCognitiveInsightsV3(integratedAnalysis, gameData) {\r\n    const insights = [];\r\n\r\n    if (integratedAnalysis.numericalSkills > 0.8) {\r\n      insights.push('Forte habilidade em conceitos numéricos');\r\n    } else if (integratedAnalysis.numericalSkills < 0.4) {\r\n      insights.push('Necessita reforço em conceitos numéricos básicos');\r\n    }\r\n\r\n    if (integratedAnalysis.attentionMetrics.consistency < 0.5) {\r\n      insights.push('Inconsistência na atenção sustentada detectada');\r\n    }\r\n\r\n    if (integratedAnalysis.errorPatterns.includes('significant_overestimate')) {\r\n      insights.push('Tendência a superestimar em tarefas de estimativa');\r\n    }\r\n\r\n    return insights;\r\n  }\r\n\r\n  /**\r\n   * Cria perfil de desenvolvimento V3\r\n   */\r\n  createDevelopmentProfileV3(integratedAnalysis, activityAnalysis) {\r\n    return {\r\n      strengths: Object.keys(integratedAnalysis.activityPerformance).filter(activity => \r\n        integratedAnalysis.activityPerformance[activity].accuracy > 0.7),\r\n      weaknesses: Object.keys(integratedAnalysis.activityPerformance).filter(activity => \r\n        integratedAnalysis.activityPerformance[activity].accuracy < 0.4),\r\n      progress: this.calculateProgressMetrics(activityAnalysis)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Gera recomendações adaptativas\r\n   */\r\n  generateAdaptiveRecommendations(integratedAnalysis, activityAnalysis) {\r\n    const recommendations = [];\r\n\r\n    Object.keys(activityAnalysis).forEach(activity => {\r\n      if (activityAnalysis[activity].accuracy < 0.5) {\r\n        recommendations.push(`Praticar mais ${activity} com dificuldade reduzida`);\r\n      }\r\n      if (activityAnalysis[activity].averageResponseTime > 7000) {\r\n        recommendations.push(`Focar em velocidade para ${activity}`);\r\n      }\r\n    });\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Atualiza perfil cognitivo V3\r\n   */\r\n  updateCognitiveProfileV3(completeAnalysis) {\r\n    this.cognitiveProfile = {\r\n      ...this.cognitiveProfile,\r\n      lastAnalysis: completeAnalysis,\r\n      performanceHistory: this.analysisHistory.map(a => a.metadata.accuracy)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa tendência de desempenho\r\n   */\r\n  analyzePerformanceTrend(gameData) {\r\n    const metrics = this.extractPerformanceMetrics(gameData);\r\n    return metrics.accuracy > 0.7 ? 'positive' : metrics.accuracy < 0.4 ? 'negative' : 'stable';\r\n  }\r\n\r\n  /**\r\n   * Calcula métricas de progresso\r\n   */\r\n  calculateProgressMetrics(activityAnalysis) {\r\n    const progress = {};\r\n    Object.keys(activityAnalysis).forEach(activity => {\r\n      progress[activity] = activityAnalysis[activity].learningCurve.slice(-1)[0]?.streak || 0;\r\n    });\r\n    return progress;\r\n  }\r\n\r\n  /**\r\n   * Retorna análise de erro V3\r\n   */\r\n  getErrorAnalysisV3(error, gameData) {\r\n    return {\r\n      error: error.message,\r\n      timestamp: new Date().toISOString(),\r\n      gameId: 'numbercounting_v3',\r\n      sessionId: gameData?.sessionId || 'unknown',\r\n      metadata: {\r\n        totalAttempts: gameData?.attempts?.length || 0,\r\n        dataQuality: 'poor'\r\n      }\r\n    };\r\n  }\r\n}", "/**\r\n * @file ContagemNumerosProcessors.js\r\n * @description Processador específico para o jogo Contagem de Números\r\n * @version 3.0.0\r\n * \r\n * Funcionalidades:\r\n * - Análise de habilidades numéricas básicas\r\n * - Avaliação de processamento numérico\r\n * - Métricas de contagem e subitização\r\n * - Análise de conceitos matemáticos fundamentais\r\n */\r\n\r\nimport { IGameProcessor } from '../IGameProcessor.js';\r\n\r\nexport class ContagemNumerosProcessors extends IGameProcessor {\r\n  constructor(logger) {\r\n    // Configurações específicas para ContagemNumeros\r\n    const config = {\r\n      category: 'numerical_processing',\r\n      therapeuticFocus: ['numerical_cognition', 'counting_skills', 'mathematical_reasoning'],\r\n      cognitiveAreas: ['numerical_processing', 'executive_function', 'memory'],\r\n      thresholds: {\r\n        accuracy: 70,\r\n        responseTime: 3500,\r\n        engagement: 75\r\n      }\r\n    };\r\n    \r\n    super(config); // Chama o construtor do IGameProcessor\r\n    this.logger = logger || this.logger;\r\n    this.gameType = 'ContagemNumeros';\r\n  }\r\n\r\n  /**\r\n   * Processa dados do jogo Contagem de Números\r\n   * @param {Object} gameData - Dados coletados do jogo\r\n   * @param {Object} collectorsHub - Hub de coletores específico do jogo\r\n   * @returns {Promise<Object>} Análise terapêutica específica\r\n   */\r\n  async processGameData(gameData, collectorsHub = null) {\n    try {\n      this.logger?.info('🎮 Processando dados ContagemNumeros', {\n        sessionId: gameData.sessionId,\n        userId: gameData.userId,\n        collectorsActive: collectorsHub ? Object.keys(collectorsHub.collectors || {}).length : 0\n      });\n\n      // Processar métricas específicas do jogo\n      const metrics = await this.processContagemNumerosMetrics(gameData, gameData);\n      \n      // Gerar análise terapêutica\n      const therapeuticAnalysis = this.generateTherapeuticAnalysis(metrics, gameData);\n      \n      // Processar métricas para estrutura padronizada\n      const processedMetrics = this.processMetricsForDatabase(metrics, gameData);\n\n      return {\n        success: true,\n        gameType: this.gameType,\n        metrics,\n        therapeuticAnalysis,\n        processedMetrics,\n        timestamp: new Date().toISOString()\n      };\n\n    } catch (error) {\n      this.logger?.error('❌ Erro ao processar dados ContagemNumeros:', error);\n      return {\n        success: false,\n        gameType: this.gameType,\n        error: error.message,\n        fallbackAnalysis: this.generateFallbackTherapeuticAnalysis(gameData),\n        timestamp: new Date().toISOString()\n      };\n    }\n  }\n\r\n  /**\r\n   * Método principal de processamento de dados\r\n   * @param {Object} sessionData - Dados da sessão de jogo\r\n   * @returns {Promise<Object>} Métricas processadas\r\n   */\r\n  async processData(sessionData) {\r\n    try {\r\n      this.logger?.info('🔢 Processando dados Contagem de Números', {\r\n        sessionId: sessionData.sessionId,\r\n        userId: sessionData.userId\r\n      });\r\n\r\n      const result = await this.processGameData(sessionData);\r\n      \r\n      this.logger?.therapeutic('✅ Processamento Contagem de Números concluído com sucesso');\r\n      \r\n      return result;\r\n\r\n    } catch (error) {\r\n      this.logger?.error('❌ Erro ao processar dados Contagem de Números:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Processa métricas específicas do Contagem de Números\r\n   * @param {Object} gameData - Dados do jogo\r\n   * @param {Object} sessionData - Dados da sessão\r\n   * @returns {Object} Métricas processadas\r\n   */\r\n  async processContagemNumerosMetrics(gameData, sessionData) {\r\n    try {\r\n      this.logger?.info('🔢 Processando métricas Contagem de Números...', { \r\n        sessionId: sessionData.sessionId \r\n      });\r\n\r\n      const metrics = {\r\n        // Análise de processamento numérico\r\n        numericalProcessing: this.analyzeNumericalProcessing(gameData),\r\n        \r\n        // Habilidades de contagem\r\n        countingAbilities: this.analyzeCountingAbilities(gameData),\r\n        \r\n        // Subitização (reconhecimento rápido de pequenas quantidades)\r\n        subitization: this.analyzeSubitization(gameData),\r\n        \r\n        // Conceitos numéricos\r\n        numericalConcepts: this.analyzeNumericalConcepts(gameData),\r\n        \r\n        // Estratégias de contagem\r\n        countingStrategies: this.analyzeCountingStrategies(gameData),\r\n        \r\n        // Precisão numérica\r\n        numericalAccuracy: this.analyzeNumericalAccuracy(gameData),\r\n        \r\n        // Velocidade de processamento numérico\r\n        numericalSpeed: this.analyzeNumericalSpeed(gameData),\r\n        \r\n        // Indicadores de dificuldades matemáticas\r\n        mathematicalConcerns: this.identifyMathematicalConcerns(gameData),\r\n        \r\n        // Recomendações específicas\r\n        recommendations: this.generateNumericalRecommendations(gameData)\r\n      };\r\n\r\n      this.logger?.info('✅ Métricas Contagem de Números processadas', {\r\n        accuracy: metrics.numericalAccuracy.overallAccuracy,\r\n        countingAbility: metrics.countingAbilities.level,\r\n        processingSpeed: metrics.numericalSpeed.category\r\n      });\r\n\r\n      return metrics;\r\n\r\n    } catch (error) {\r\n      this.logger?.error('❌ Erro ao processar métricas Contagem de Números:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Analisa processamento numérico geral\r\n   */\r\n  analyzeNumericalProcessing(gameData) {\r\n    const { interactions = [], totalCorrect = 0, totalAttempts = 1 } = gameData;\r\n    \r\n    return {\r\n      overallAccuracy: Math.round((totalCorrect / totalAttempts) * 100),\r\n      numberRecognition: this.assessNumberRecognition(interactions),\r\n      quantityDiscrimination: this.assessQuantityDiscrimination(interactions),\r\n      numericalComparison: this.assessNumericalComparison(interactions),\r\n      processing_efficiency: this.calculateProcessingEfficiency(interactions)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa habilidades de contagem\r\n   */\r\n  analyzeCountingAbilities(gameData) {\r\n    const { interactions = [], difficulty = 'easy' } = gameData;\r\n    \r\n    const countingAccuracy = this.calculateCountingAccuracy(interactions);\r\n    const countingRange = this.assessCountingRange(interactions);\r\n    const countingStrategy = this.identifyCountingStrategy(interactions);\r\n    \r\n    return {\r\n      level: this.determineCoutingLevel(countingAccuracy, countingRange),\r\n      accuracy: countingAccuracy,\r\n      range: countingRange,\r\n      strategy: countingStrategy,\r\n      consistency: this.assessCountingConsistency(interactions),\r\n      errorPatterns: this.identifyCountingErrorPatterns(interactions)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa subitização (reconhecimento rápido de quantidades pequenas)\r\n   */\r\n  analyzeSubitization(gameData) {\r\n    const { interactions = [] } = gameData;\r\n    \r\n    // Filtra interações com quantidades pequenas (1-4)\r\n    const smallQuantities = interactions.filter(i => \r\n      i.targetQuantity && i.targetQuantity <= 4\r\n    );\r\n    \r\n    const fastResponses = smallQuantities.filter(i => \r\n      i.responseTime && i.responseTime < 1500 && i.correct\r\n    );\r\n    \r\n    return {\r\n      subitizationRange: this.determineSubitizationRange(smallQuantities),\r\n      fastRecognition: fastResponses.length,\r\n      accuracy: smallQuantities.length > 0 ? \r\n        fastResponses.length / smallQuantities.length : 0,\r\n      averageResponseTime: this.calculateAverageResponseTime(smallQuantities),\r\n      abilityLevel: this.assessSubitizationAbility(fastResponses, smallQuantities)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa conceitos numéricos\r\n   */\r\n  analyzeNumericalConcepts(gameData) {\r\n    const { interactions = [] } = gameData;\r\n    \r\n    return {\r\n      oneToOneCorrespondence: this.assessOneToOneCorrespondence(interactions),\r\n      numberSequence: this.assessNumberSequenceUnderstanding(interactions),\r\n      cardinality: this.assessCardinalityUnderstanding(interactions),\r\n      magnitude: this.assessMagnitudeUnderstanding(interactions),\r\n      numberLine: this.assessNumberLineUnderstanding(interactions),\r\n      conceptualDevelopment: this.assessConceptualDevelopment(interactions)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa estratégias de contagem utilizadas\r\n   */\r\n  analyzeCountingStrategies(gameData) {\r\n    const { interactions = [] } = gameData;\r\n    \r\n    return {\r\n      primaryStrategy: this.identifyPrimaryCountingStrategy(interactions),\r\n      strategyFlexibility: this.assessStrategyFlexibility(interactions),\r\n      adaptiveStrategy: this.assessAdaptiveStrategyUse(interactions),\r\n      efficiency: this.assessStrategyEfficiency(interactions),\r\n      development: this.assessStrategyDevelopment(interactions)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa precisão numérica\r\n   */\r\n  analyzeNumericalAccuracy(gameData) {\r\n    const { interactions = [], totalCorrect = 0, totalAttempts = 1 } = gameData;\r\n    \r\n    const accuracyByRange = this.calculateAccuracyByNumberRange(interactions);\r\n    const errorAnalysis = this.analyzeNumericalErrors(interactions);\r\n    \r\n    return {\r\n      overallAccuracy: Math.round((totalCorrect / totalAttempts) * 100),\r\n      accuracyByRange,\r\n      errorPatterns: errorAnalysis.patterns,\r\n      errorTypes: errorAnalysis.types,\r\n      improvementTrend: this.calculateAccuracyTrend(interactions)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analisa velocidade de processamento numérico\r\n   */\r\n  analyzeNumericalSpeed(gameData) {\r\n    const { interactions = [] } = gameData;\r\n    \r\n    const responseTimes = interactions\r\n      .map(i => i.responseTime)\r\n      .filter(t => t && t > 0);\r\n    \r\n    if (responseTimes.length === 0) {\r\n      return { category: 'insufficient_data', average: 0 };\r\n    }\r\n\r\n    const average = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;\r\n    const speedByQuantity = this.analyzeSpeedByQuantity(interactions);\r\n    \r\n    return {\r\n      average: Math.round(average),\r\n      category: this.categorizeProcessingSpeed(average),\r\n      speedByQuantity,\r\n      processingEfficiency: this.calculateNumericalProcessingEfficiency(interactions),\r\n      speedTrend: this.calculateSpeedTrend(responseTimes)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Identifica preocupações matemáticas\r\n   */\r\n  identifyMathematicalConcerns(gameData) {\r\n    const { interactions = [], accuracy = 0 } = gameData;\r\n    \r\n    return {\r\n      dyscalculiaRisk: this.assessDyscalculiaRisk(interactions, accuracy),\r\n      numberSenseDeficit: this.assessNumberSenseDeficit(interactions),\r\n      countingDifficulties: this.identifyCountingDifficulties(interactions),\r\n      conceptualGaps: this.identifyConceptualGaps(interactions),\r\n      processingDelays: this.identifyProcessingDelays(interactions)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Gera recomendações específicas\r\n   */\r\n  generateNumericalRecommendations(gameData) {\r\n    const recommendations = [];\r\n    const { accuracy = 0, interactions = [] } = gameData;\r\n    \r\n    // Recomendações baseadas na precisão\r\n    if (accuracy < 50) {\r\n      recommendations.push({\r\n        type: 'foundational_support',\r\n        priority: 'high',\r\n        action: 'Reforçar conceitos básicos de contagem e correspondência um-para-um',\r\n        rationale: 'Baixa precisão indica necessidade de fundamentos sólidos'\r\n      });\r\n    }\r\n    \r\n    // Recomendações baseadas na velocidade\r\n    const avgSpeed = this.calculateAverageResponseTime(interactions);\r\n    if (avgSpeed > 5000) {\r\n      recommendations.push({\r\n        type: 'fluency_development',\r\n        priority: 'medium',\r\n        action: 'Exercícios de fluência numérica e reconhecimento rápido',\r\n        rationale: 'Velocidade baixa indica necessidade de automatização'\r\n      });\r\n    }\r\n    \r\n    // Recomendações baseadas em padrões de erro\r\n    const errorPatterns = this.identifyCountingErrorPatterns(interactions);\r\n    if (errorPatterns.sequenceErrors > 2) {\r\n      recommendations.push({\r\n        type: 'sequence_training',\r\n        priority: 'medium',\r\n        action: 'Prática intensiva da sequência numérica',\r\n        rationale: 'Múltiplos erros de sequência detectados'\r\n      });\r\n    }\r\n    \r\n    return recommendations;\r\n  }\r\n\r\n  // Métodos auxiliares de análise\r\n\r\n  assessNumberRecognition(interactions) {\r\n    const recognitionTasks = interactions.filter(i => i.taskType === 'recognition');\r\n    const correct = recognitionTasks.filter(i => i.correct);\r\n    \r\n    return recognitionTasks.length > 0 ? \r\n      Math.round((correct.length / recognitionTasks.length) * 100) : 0;\r\n  }\r\n\r\n  assessQuantityDiscrimination(interactions) {\r\n    const discriminationTasks = interactions.filter(i => \r\n      i.taskType === 'discrimination' || i.requiresComparison\r\n    );\r\n    const correct = discriminationTasks.filter(i => i.correct);\r\n    \r\n    return discriminationTasks.length > 0 ? \r\n      Math.round((correct.length / discriminationTasks.length) * 100) : 0;\r\n  }\r\n\r\n  assessNumericalComparison(interactions) {\r\n    const comparisonTasks = interactions.filter(i => \r\n      i.taskType === 'comparison' || i.involvesComparison\r\n    );\r\n    const correct = comparisonTasks.filter(i => i.correct);\r\n    \r\n    return comparisonTasks.length > 0 ? \r\n      Math.round((correct.length / comparisonTasks.length) * 100) : 0;\r\n  }\r\n\r\n  calculateProcessingEfficiency(interactions) {\r\n    const correctInteractions = interactions.filter(i => i.correct && i.responseTime);\r\n    if (correctInteractions.length === 0) return 0;\r\n    \r\n    const avgTime = correctInteractions.reduce((sum, i) => sum + i.responseTime, 0) / correctInteractions.length;\r\n    \r\n    // Eficiência = precisão / tempo (normalizado)\r\n    return Math.min(1, 3000 / avgTime);\r\n  }\r\n\r\n  calculateCountingAccuracy(interactions) {\r\n    const countingTasks = interactions.filter(i => \r\n      i.taskType === 'counting' || i.requiresCounting\r\n    );\r\n    const correct = countingTasks.filter(i => i.correct);\r\n    \r\n    return countingTasks.length > 0 ? \r\n      Math.round((correct.length / countingTasks.length) * 100) : 0;\r\n  }\r\n\r\n  assessCountingRange(interactions) {\r\n    const quantities = interactions\r\n      .map(i => i.targetQuantity)\r\n      .filter(q => q && q > 0);\r\n    \r\n    if (quantities.length === 0) return { min: 0, max: 0 };\r\n    \r\n    return {\r\n      min: Math.min(...quantities),\r\n      max: Math.max(...quantities),\r\n      comfortable: this.findComfortableRange(interactions)\r\n    };\r\n  }\r\n\r\n  findComfortableRange(interactions) {\r\n    // Encontra a faixa de números onde a precisão é maior que 80%\r\n    const rangeAccuracy = {};\r\n    \r\n    interactions.forEach(interaction => {\r\n      const quantity = interaction.targetQuantity;\r\n      if (quantity) {\r\n        const range = this.getNumberRange(quantity);\r\n        if (!rangeAccuracy[range]) {\r\n          rangeAccuracy[range] = { correct: 0, total: 0 };\r\n        }\r\n        rangeAccuracy[range].total++;\r\n        if (interaction.correct) {\r\n          rangeAccuracy[range].correct++;\r\n        }\r\n      }\r\n    });\r\n    \r\n    const comfortableRanges = Object.entries(rangeAccuracy)\r\n      .filter(([_, data]) => data.total > 0 && (data.correct / data.total) > 0.8)\r\n      .map(([range, _]) => range);\r\n    \r\n    return comfortableRanges;\r\n  }\r\n\r\n  getNumberRange(quantity) {\r\n    if (quantity <= 5) return '1-5';\r\n    if (quantity <= 10) return '6-10';\r\n    if (quantity <= 20) return '11-20';\r\n    return '20+';\r\n  }\r\n\r\n  identifyCountingStrategy(interactions) {\r\n    // Análise baseada em tempo de resposta e padrões\r\n    const avgResponseTime = this.calculateAverageResponseTime(interactions);\r\n    const fastResponses = interactions.filter(i => \r\n      i.responseTime && i.responseTime < 2000\r\n    ).length;\r\n    \r\n    if (fastResponses / interactions.length > 0.7) {\r\n      return 'subitization'; // Reconhecimento imediato\r\n    } else if (avgResponseTime < 3000) {\r\n      return 'efficient_counting'; // Contagem eficiente\r\n    } else {\r\n      return 'systematic_counting'; // Contagem sistemática\r\n    }\r\n  }\r\n\r\n  determineCoutingLevel(accuracy, range) {\r\n    if (accuracy < 50) return 'emergent';\r\n    if (accuracy < 70 || range.max <= 5) return 'developing';\r\n    if (accuracy < 85 || range.max <= 10) return 'proficient';\r\n    return 'advanced';\r\n  }\r\n\r\n  assessCountingConsistency(interactions) {\r\n    const accuracies = this.calculateRollingAccuracy(interactions, 5);\r\n    const variance = this.calculateVariance(accuracies);\r\n    \r\n    return variance < 0.1 ? 'high' : variance < 0.2 ? 'moderate' : 'low';\r\n  }\r\n\r\n  calculateRollingAccuracy(interactions, windowSize) {\r\n    const accuracies = [];\r\n    \r\n    for (let i = 0; i <= interactions.length - windowSize; i++) {\r\n      const window = interactions.slice(i, i + windowSize);\r\n      const correct = window.filter(item => item.correct).length;\r\n      accuracies.push(correct / windowSize);\r\n    }\r\n    \r\n    return accuracies;\r\n  }\r\n\r\n  calculateVariance(values) {\r\n    if (values.length === 0) return 0;\r\n    \r\n    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;\r\n    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));\r\n    \r\n    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;\r\n  }\r\n\r\n  identifyCountingErrorPatterns(interactions) {\r\n    const errors = interactions.filter(i => !i.correct);\r\n    \r\n    return {\r\n      sequenceErrors: errors.filter(e => e.errorType === 'sequence').length,\r\n      skippingErrors: errors.filter(e => e.errorType === 'skipping').length,\r\n      double_counting: errors.filter(e => e.errorType === 'double_counting').length,\r\n      magnitude_errors: errors.filter(e => e.errorType === 'magnitude').length\r\n    };\r\n  }\r\n\r\n  // Métodos de subitização\r\n\r\n  determineSubitizationRange(interactions) {\r\n    const maxQuickRecognition = Math.max(\r\n      ...interactions\r\n        .filter(i => i.responseTime < 1500 && i.correct)\r\n        .map(i => i.targetQuantity || 0)\r\n    );\r\n    \r\n    return Math.min(maxQuickRecognition, 4);\r\n  }\r\n\r\n  calculateAverageResponseTime(interactions) {\r\n    const times = interactions\r\n      .map(i => i.responseTime)\r\n      .filter(t => t && t > 0);\r\n    \r\n    return times.length > 0 ? \r\n      times.reduce((sum, time) => sum + time, 0) / times.length : 0;\r\n  }\r\n\r\n  assessSubitizationAbility(fastResponses, smallQuantities) {\r\n    if (smallQuantities.length === 0) return 'insufficient_data';\r\n    \r\n    const ratio = fastResponses.length / smallQuantities.length;\r\n    \r\n    if (ratio > 0.8) return 'excellent';\r\n    if (ratio > 0.6) return 'good';\r\n    if (ratio > 0.4) return 'developing';\r\n    return 'emerging';\r\n  }\r\n\r\n  // Métodos de conceitos numéricos\r\n\r\n  assessOneToOneCorrespondence(interactions) {\r\n    const correspondenceTasks = interactions.filter(i => \r\n      i.taskType === 'correspondence' || i.requiresCorrespondence\r\n    );\r\n    const correct = correspondenceTasks.filter(i => i.correct);\r\n    \r\n    return correspondenceTasks.length > 0 ? \r\n      Math.round((correct.length / correspondenceTasks.length) * 100) : 0;\r\n  }\r\n\r\n  assessNumberSequenceUnderstanding(interactions) {\r\n    const sequenceTasks = interactions.filter(i => \r\n      i.taskType === 'sequence' || i.requiresSequence\r\n    );\r\n    const correct = sequenceTasks.filter(i => i.correct);\r\n    \r\n    return sequenceTasks.length > 0 ? \r\n      Math.round((correct.length / sequenceTasks.length) * 100) : 0;\r\n  }\r\n\r\n  assessCardinalityUnderstanding(interactions) {\r\n    // Cardinalidade: entender que o último número da contagem representa a quantidade total\r\n    const cardinalityTasks = interactions.filter(i => \r\n      i.taskType === 'cardinality' || i.requiresCardinality\r\n    );\r\n    const correct = cardinalityTasks.filter(i => i.correct);\r\n    \r\n    return cardinalityTasks.length > 0 ? \r\n      Math.round((correct.length / cardinalityTasks.length) * 100) : 0;\r\n  }\r\n\r\n  assessMagnitudeUnderstanding(interactions) {\r\n    const magnitudeTasks = interactions.filter(i => \r\n      i.taskType === 'magnitude' || i.requiresMagnitude\r\n    );\r\n    const correct = magnitudeTasks.filter(i => i.correct);\r\n    \r\n    return magnitudeTasks.length > 0 ? \r\n      Math.round((correct.length / magnitudeTasks.length) * 100) : 0;\r\n  }\r\n\r\n  assessNumberLineUnderstanding(interactions) {\r\n    const numberLineTasks = interactions.filter(i => \r\n      i.taskType === 'numberline' || i.requiresNumberLine\r\n    );\r\n    const correct = numberLineTasks.filter(i => i.correct);\r\n    \r\n    return numberLineTasks.length > 0 ? \r\n      Math.round((correct.length / numberLineTasks.length) * 100) : 0;\r\n  }\r\n\r\n  assessConceptualDevelopment(interactions) {\r\n    const scores = {\r\n      correspondence: this.assessOneToOneCorrespondence(interactions),\r\n      sequence: this.assessNumberSequenceUnderstanding(interactions),\r\n      cardinality: this.assessCardinalityUnderstanding(interactions),\r\n      magnitude: this.assessMagnitudeUnderstanding(interactions)\r\n    };\r\n    \r\n    const averageScore = Object.values(scores).reduce((sum, score) => sum + score, 0) / 4;\r\n    \r\n    if (averageScore > 80) return 'advanced';\r\n    if (averageScore > 60) return 'proficient';\r\n    if (averageScore > 40) return 'developing';\r\n    return 'emerging';\r\n  }\r\n\r\n  // Métodos adicionais\r\n\r\n  identifyPrimaryCountingStrategy(interactions) {\r\n    // Lógica simplificada para identificar estratégia principal\r\n    const avgTime = this.calculateAverageResponseTime(interactions);\r\n    \r\n    if (avgTime < 2000) return 'subitization';\r\n    if (avgTime < 4000) return 'counting_on';\r\n    return 'counting_all';\r\n  }\r\n\r\n  assessStrategyFlexibility(interactions) {\r\n    // Avalia se adapta estratégias conforme a dificuldade\r\n    const strategies = interactions.map(i => this.identifyInteractionStrategy(i));\r\n    const uniqueStrategies = new Set(strategies).size;\r\n    \r\n    return uniqueStrategies > 1 ? 'flexible' : 'rigid';\r\n  }\r\n\r\n  identifyInteractionStrategy(interaction) {\r\n    if (!interaction.responseTime) return 'unknown';\r\n    \r\n    if (interaction.responseTime < 1500) return 'subitization';\r\n    if (interaction.responseTime < 3000) return 'counting_on';\r\n    return 'counting_all';\r\n  }\r\n\r\n  assessAdaptiveStrategyUse(interactions) {\r\n    // Verifica se usa estratégias mais eficientes para quantidades menores\r\n    const smallNumbers = interactions.filter(i => i.targetQuantity <= 4);\r\n    const largeNumbers = interactions.filter(i => i.targetQuantity > 4);\r\n    \r\n    const avgTimeSmall = this.calculateAverageResponseTime(smallNumbers);\r\n    const avgTimeLarge = this.calculateAverageResponseTime(largeNumbers);\r\n    \r\n    return avgTimeSmall < avgTimeLarge ? 'adaptive' : 'non_adaptive';\r\n  }\r\n\r\n  assessStrategyEfficiency(interactions) {\r\n    const correctInteractions = interactions.filter(i => i.correct);\r\n    const avgTime = this.calculateAverageResponseTime(correctInteractions);\r\n    \r\n    if (avgTime < 2000) return 'high';\r\n    if (avgTime < 4000) return 'moderate';\r\n    return 'low';\r\n  }\r\n\r\n  assessStrategyDevelopment(interactions) {\r\n    // Verifica melhoria nas estratégias ao longo do tempo\r\n    const firstHalf = interactions.slice(0, Math.floor(interactions.length / 2));\r\n    const secondHalf = interactions.slice(Math.floor(interactions.length / 2));\r\n    \r\n    const firstHalfTime = this.calculateAverageResponseTime(firstHalf);\r\n    const secondHalfTime = this.calculateAverageResponseTime(secondHalf);\r\n    \r\n    if (secondHalfTime < firstHalfTime * 0.8) return 'improving';\r\n    if (secondHalfTime > firstHalfTime * 1.2) return 'declining';\r\n    return 'stable';\r\n  }\r\n\r\n  // Métodos de análise de precisão\r\n\r\n  calculateAccuracyByNumberRange(interactions) {\r\n    const ranges = {\r\n      '1-3': { correct: 0, total: 0 },\r\n      '4-6': { correct: 0, total: 0 },\r\n      '7-10': { correct: 0, total: 0 },\r\n      '11+': { correct: 0, total: 0 }\r\n    };\r\n    \r\n    interactions.forEach(interaction => {\r\n      const quantity = interaction.targetQuantity;\r\n      if (!quantity) return;\r\n      \r\n      let range;\r\n      if (quantity <= 3) range = '1-3';\r\n      else if (quantity <= 6) range = '4-6';\r\n      else if (quantity <= 10) range = '7-10';\r\n      else range = '11+';\r\n      \r\n      ranges[range].total++;\r\n      if (interaction.correct) {\r\n        ranges[range].correct++;\r\n      }\r\n    });\r\n    \r\n    const result = {};\r\n    Object.entries(ranges).forEach(([range, data]) => {\r\n      result[range] = data.total > 0 ? \r\n        Math.round((data.correct / data.total) * 100) : 0;\r\n    });\r\n    \r\n    return result;\r\n  }\r\n\r\n  analyzeNumericalErrors(interactions) {\r\n    const errors = interactions.filter(i => !i.correct);\r\n    \r\n    const patterns = {\r\n      off_by_one: errors.filter(e => Math.abs((e.response || 0) - (e.targetQuantity || 0)) === 1).length,\r\n      sequence_errors: errors.filter(e => e.errorType === 'sequence').length,\r\n      magnitude_errors: errors.filter(e => e.errorType === 'magnitude').length,\r\n      random_errors: errors.filter(e => !e.errorType || e.errorType === 'random').length\r\n    };\r\n    \r\n    const types = {\r\n      systematic: patterns.off_by_one + patterns.sequence_errors,\r\n      conceptual: patterns.magnitude_errors,\r\n      careless: patterns.random_errors\r\n    };\r\n    \r\n    return { patterns, types };\r\n  }\r\n\r\n  calculateAccuracyTrend(interactions) {\r\n    if (interactions.length < 6) return 'insufficient_data';\r\n    \r\n    const firstThird = interactions.slice(0, Math.floor(interactions.length / 3));\r\n    const lastThird = interactions.slice(-Math.floor(interactions.length / 3));\r\n    \r\n    const firstAccuracy = firstThird.filter(i => i.correct).length / firstThird.length;\r\n    const lastAccuracy = lastThird.filter(i => i.correct).length / lastThird.length;\r\n    \r\n    if (lastAccuracy > firstAccuracy + 0.1) return 'improving';\r\n    if (lastAccuracy < firstAccuracy - 0.1) return 'declining';\r\n    return 'stable';\r\n  }\r\n\r\n  // Métodos de velocidade\r\n\r\n  analyzeSpeedByQuantity(interactions) {\r\n    const speedByQuantity = {};\r\n    \r\n    interactions.forEach(interaction => {\r\n      const quantity = interaction.targetQuantity;\r\n      const time = interaction.responseTime;\r\n      \r\n      if (quantity && time) {\r\n        if (!speedByQuantity[quantity]) {\r\n          speedByQuantity[quantity] = [];\r\n        }\r\n        speedByQuantity[quantity].push(time);\r\n      }\r\n    });\r\n    \r\n    const result = {};\r\n    Object.entries(speedByQuantity).forEach(([quantity, times]) => {\r\n      result[quantity] = times.reduce((sum, time) => sum + time, 0) / times.length;\r\n    });\r\n    \r\n    return result;\r\n  }\r\n\r\n  categorizeProcessingSpeed(averageTime) {\r\n    if (averageTime < 2000) return 'fast';\r\n    if (averageTime < 4000) return 'normal';\r\n    return 'slow';\r\n  }\r\n\r\n  calculateNumericalProcessingEfficiency(interactions) {\r\n    const correctInteractions = interactions.filter(i => i.correct && i.responseTime);\r\n    if (correctInteractions.length === 0) return 0;\r\n    \r\n    // Eficiência baseada na relação entre precisão e velocidade\r\n    const accuracy = correctInteractions.length / interactions.length;\r\n    const avgTime = this.calculateAverageResponseTime(correctInteractions);\r\n    \r\n    // Normalizar tempo (menor tempo = maior eficiência)\r\n    const timeEfficiency = Math.max(0, (5000 - avgTime) / 5000);\r\n    \r\n    return (accuracy + timeEfficiency) / 2;\r\n  }\r\n\r\n  calculateSpeedTrend(responseTimes) {\r\n    if (responseTimes.length < 6) return 'insufficient_data';\r\n    \r\n    const firstHalf = responseTimes.slice(0, Math.floor(responseTimes.length / 2));\r\n    const secondHalf = responseTimes.slice(Math.floor(responseTimes.length / 2));\r\n    \r\n    const firstAvg = firstHalf.reduce((sum, time) => sum + time, 0) / firstHalf.length;\r\n    const secondAvg = secondHalf.reduce((sum, time) => sum + time, 0) / secondHalf.length;\r\n    \r\n    if (secondAvg < firstAvg * 0.8) return 'improving';\r\n    if (secondAvg > firstAvg * 1.2) return 'declining';\r\n    return 'stable';\r\n  }\r\n\r\n  // Métodos de identificação de preocupações\r\n\r\n  assessDyscalculiaRisk(interactions, accuracy) {\r\n    const riskFactors = {\r\n      lowAccuracy: accuracy < 50,\r\n      sequenceErrors: this.identifyCountingErrorPatterns(interactions).sequenceErrors > 3,\r\n      slowProcessing: this.calculateAverageResponseTime(interactions) > 5000,\r\n      inconsistentPerformance: this.assessCountingConsistency(interactions) === 'low',\r\n      conceptualGaps: this.assessConceptualDevelopment(interactions) === 'emerging'\r\n    };\r\n    \r\n    const riskCount = Object.values(riskFactors).filter(Boolean).length;\r\n    \r\n    if (riskCount >= 3) return 'high';\r\n    if (riskCount >= 2) return 'moderate';\r\n    return 'low';\r\n  }\r\n\r\n  assessNumberSenseDeficit(interactions) {\r\n    const indicators = {\r\n      poorSubitization: this.assessSubitizationAbility([], interactions) === 'emerging',\r\n      weakMagnitude: this.assessMagnitudeUnderstanding(interactions) < 50,\r\n      limitedRange: this.assessCountingRange(interactions).max < 10,\r\n      strategicRigidity: this.assessStrategyFlexibility(interactions) === 'rigid'\r\n    };\r\n    \r\n    const indicatorCount = Object.values(indicators).filter(Boolean).length;\r\n    \r\n    return indicatorCount >= 2 ? 'likely' : 'unlikely';\r\n  }\r\n\r\n  identifyCountingDifficulties(interactions) {\r\n    const difficulties = [];\r\n    \r\n    const errorPatterns = this.identifyCountingErrorPatterns(interactions);\r\n    \r\n    if (errorPatterns.sequenceErrors > 2) {\r\n      difficulties.push('sequence_difficulties');\r\n    }\r\n    \r\n    if (errorPatterns.skippingErrors > 2) {\r\n      difficulties.push('attention_difficulties');\r\n    }\r\n    \r\n    if (errorPatterns.double_counting > 2) {\r\n      difficulties.push('working_memory_difficulties');\r\n    }\r\n    \r\n    return difficulties;\r\n  }\r\n\r\n  identifyConceptualGaps(interactions) {\r\n    const gaps = [];\r\n    \r\n    if (this.assessOneToOneCorrespondence(interactions) < 60) {\r\n      gaps.push('one_to_one_correspondence');\r\n    }\r\n    \r\n    if (this.assessCardinalityUnderstanding(interactions) < 60) {\r\n      gaps.push('cardinality_concept');\r\n    }\r\n    \r\n    if (this.assessNumberSequenceUnderstanding(interactions) < 60) {\r\n      gaps.push('number_sequence');\r\n    }\r\n    \r\n    return gaps;\r\n  }\r\n\r\n  identifyProcessingDelays(interactions) {\r\n    const avgTime = this.calculateAverageResponseTime(interactions);\r\n    const delays = [];\r\n    \r\n    if (avgTime > 5000) {\r\n      delays.push('general_processing_delay');\r\n    }\r\n    \r\n    const speedByQuantity = this.analyzeSpeedByQuantity(interactions);\r\n    Object.entries(speedByQuantity).forEach(([quantity, time]) => {\r\n      if (parseInt(quantity) <= 4 && time > 3000) {\r\n        delays.push('subitization_delay');\r\n      }\r\n    });\r\n    \r\n    return delays;\r\n  }\r\n\n  /**\n   * Processa coletores com Circuit Breaker para resiliência\n   * @param {Object} collectorsHub - Hub de coletores\n   * @param {Object} gameData - Dados do jogo\n   * @returns {Promise<Object>} Resultados dos coletores\n   */\n  async processCollectorsWithCircuitBreaker(collectorsHub, gameData) {\n    if (!collectorsHub || !collectorsHub.collectors) {\n      this.logger?.warn('⚠️ ContagemNumeros: Hub de coletores não disponível');\n      return { collectors: {}, warning: 'No collectors available' };\n    }\n\n    const results = {};\n    const collectors = collectorsHub.collectors;\n\n    // Processar cada coletor com tratamento de erro\n    for (const [collectorName, collector] of Object.entries(collectors)) {\n      try {\n        if (collector && typeof collector.analyze === 'function') {\n          this.logger?.debug('🎮 Processando coletor: ' + collectorName);\n          results[collectorName] = await this.processWithTimeout(\n            () => collector.analyze(gameData),\n            5000, // 5 segundos timeout\n            collectorName + ' timeout'\n          );\n        } else {\n          this.logger?.warn('⚠️ Coletor ' + collectorName + ' não tem método analyze');\n          results[collectorName] = { error: 'No analyze method' };\n        }\n      } catch (error) {\n        this.logger?.error('❌ Erro no coletor ' + collectorName + ':', error);\n        results[collectorName] = { \n          error: error.message,\n          fallback: this.generateFallbackMetrics(collectorName, gameData)\n        };\n      }\n    }\n\n    return {\n      collectors: results,\n      processedAt: new Date().toISOString(),\n      gameType: 'ContagemNumeros'\n    };\n  }\n\n  /**\n   * Processa com timeout para evitar travamentos\n   */\n  async processWithTimeout(fn, timeout, errorMsg) {\n    return Promise.race([\n      fn(),\n      new Promise((_, reject) => \n        setTimeout(() => reject(new Error(errorMsg)), timeout)\n      )\n    ]);\n  }\n\n  /**\n   * Gera métricas de fallback em caso de erro\n   */\n  generateFallbackMetrics(collectorName, gameData) {\n    return {\n      fallback: true,\n      collector: collectorName,\n      basicScore: 50,\n      confidence: 'low',\n      note: 'Generated due to collector error'\n    };\n  }\n\n  /**\n   * Gera análise integrada combinando processador e coletores\n   * @param {Object} processorResults - Resultados do processador\n   * @param {Object} collectorsResults - Resultados dos coletores\n   * @returns {Object} Análise integrada\n   */\n  generateIntegratedAnalysis(processorResults, collectorsResults = {}) {\n    try {\n      const integratedAnalysis = {\n        gameType: 'ContagemNumeros',\n        timestamp: new Date().toISOString(),\n        sessionMetrics: processorResults,\n        collectorInsights: collectorsResults.collectors || {},\n        \n        // Análise integrada específica para ContagemNumeros\n        gamePerformance: {\n          accuracy: processorResults.accuracy || 0,\n          responseTime: processorResults.averageResponseTime || 0,\n          engagement: this.calculateEngagement(processorResults),\n          cognitiveLoad: this.calculateCognitiveLoad(processorResults)\n        },\n\n        // Insights terapêuticos\n        therapeuticInsights: this.generateTherapeuticInsights(processorResults, collectorsResults),\n        \n        // Recomendações baseadas na análise integrada\n        recommendations: this.generateRecommendations(processorResults, collectorsResults),\n        \n        // Métricas de qualidade da análise\n        analysisQuality: {\n          dataCompleteness: this.assessDataCompleteness(processorResults, collectorsResults),\n          collectorsCoverage: Object.keys(collectorsResults.collectors || {}).length,\n          confidence: this.calculateConfidenceScore(processorResults, collectorsResults)\n        }\n      };\n\n      this.logger?.info('✅ ContagemNumeros: Análise integrada gerada', {\n        accuracy: integratedAnalysis.gamePerformance.accuracy,\n        collectorsUsed: Object.keys(collectorsResults.collectors || {}).length,\n        confidence: integratedAnalysis.analysisQuality.confidence\n      });\n\n      return integratedAnalysis;\n\n    } catch (error) {\n      this.logger?.error('❌ Erro ao gerar análise integrada ContagemNumeros:', error);\n      return {\n        error: error.message,\n        fallback: true,\n        basicMetrics: processorResults,\n        timestamp: new Date().toISOString()\n      };\n    }\n  }\n\n  /**\n   * Métodos auxiliares para análise integrada\n   */\n  calculateEngagement(results) {\n    return Math.min(100, (results.accuracy || 0) + 20);\n  }\n\n  calculateCognitiveLoad(results) {\n    const time = results.averageResponseTime || 3000;\n    return Math.max(0, Math.min(100, time / 30));\n  }\n\n  generateTherapeuticInsights(processorResults, collectorsResults) {\n    const insights = [];\n    \n    if (processorResults.accuracy < 60) {\n      insights.push('Desempenho abaixo do esperado detectado');\n    }\n    \n    if (processorResults.averageResponseTime > 3000) {\n      insights.push('Tempo de resposta acima da média');\n    }\n    \n    return insights;\n  }\n\n  generateRecommendations(processorResults, collectorsResults) {\n    const recommendations = [];\n    \n    if (processorResults.accuracy < 70) {\n      recommendations.push('Exercícios de reforço recomendados');\n    }\n    \n    return recommendations;\n  }\n\n  assessDataCompleteness(processorResults, collectorsResults) {\n    let score = 0;\n    if (processorResults.accuracy !== undefined) score += 25;\n    if (processorResults.averageResponseTime !== undefined) score += 25;\n    if (Object.keys(collectorsResults.collectors || {}).length > 0) score += 50;\n    return score;\n  }\n\n  calculateConfidenceScore(processorResults, collectorsResults) {\n    const dataQuality = this.assessDataCompleteness(processorResults, collectorsResults);\n    const collectorCount = Object.keys(collectorsResults.collectors || {}).length;\n    return Math.min(100, dataQuality + (collectorCount * 5));\n  }\n  /**\n   * Gera análise terapêutica completa\n   * @param {Object} metrics - Métricas processadas\n   * @param {Object} gameData - Dados do jogo\n   * @returns {Object} Análise terapêutica\n   */\n  generateTherapeuticAnalysis(metrics, gameData) {\n    try {\n      const analysis = {\n        // Análise comportamental\n        behavioral: {\n          engagement: this.calculateEngagementScore(gameData),\n          persistence: this.calculatePersistenceScore(gameData),\n          adaptability: this.calculateAdaptabilityScore(gameData),\n          frustrationTolerance: this.calculateFrustrationTolerance(gameData),\n          socialInteraction: this.calculateSocialInteractionScore(gameData)\n        },\n        \n        // Análise cognitiva\n        cognitive: {\n          attention: this.calculateAttentionScore(gameData),\n          memory: this.calculateMemoryScore(gameData),\n          processingSpeed: this.calculateProcessingSpeedScore(gameData),\n          executiveFunction: this.calculateExecutiveFunctionScore(gameData),\n          visualProcessing: this.calculateVisualProcessingScore(gameData)\n        },\n        \n        // Análise sensorial\n        sensory: {\n          visualPerception: this.calculateVisualPerceptionScore(gameData),\n          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),\n          tactileProcessing: this.calculateTactileProcessingScore(gameData),\n          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)\n        },\n        \n        // Análise motora\n        motor: {\n          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),\n          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),\n          coordination: this.calculateCoordinationScore(gameData),\n          motorPlanning: this.calculateMotorPlanningScore(gameData)\n        },\n        \n        // Recomendações terapêuticas\n        recommendations: this.generateTherapeuticRecommendations(metrics, gameData),\n        \n        // Indicadores de progresso\n        progressIndicators: this.generateProgressIndicators(metrics, gameData),\n        \n        // Insights específicos do jogo\n        gameSpecificInsights: this.generateGameSpecificInsights(metrics, gameData),\n        \n        // Metadados\n        metadata: {\n          analysisTimestamp: new Date().toISOString(),\n          gameType: this.gameType,\n          analysisVersion: '3.0.0',\n          confidenceScore: this.calculateAnalysisConfidenceScore(metrics, gameData)\n        }\n      };\n      \n      return analysis;\n    } catch (error) {\n      this.logger?.error('❌ Erro ao gerar análise terapêutica:', error);\n      return this.generateFallbackTherapeuticAnalysis(gameData);\n    }\n  }\n  /**\n   * Métodos de cálculo de scores terapêuticos\n   */\n  calculateEngagementScore(gameData) {\n    const interactions = gameData.interactions || [];\n    const totalTime = gameData.totalTime || 1000;\n    const completionRate = gameData.completionRate || 0;\n    \n    let score = 50; // Base score\n    \n    // Fator de interação\n    if (interactions.length > 0) {\n      score += Math.min(30, interactions.length * 2);\n    }\n    \n    // Fator de tempo\n    if (totalTime > 30000) { // Mais de 30 segundos\n      score += 10;\n    }\n    \n    // Fator de completude\n    score += completionRate * 10;\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculatePersistenceScore(gameData) {\n    const attempts = gameData.attempts || 1;\n    const errors = gameData.errors || 0;\n    const completion = gameData.completion || 0;\n    \n    let score = 50;\n    \n    if (attempts > 1 && completion > 0.5) {\n      score += 20; // Persistiu após tentativas\n    }\n    \n    if (errors > 0 && completion > 0.8) {\n      score += 15; // Superou erros\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateAdaptabilityScore(gameData) {\n    const difficultyChanges = gameData.difficultyChanges || 0;\n    const adaptationSuccess = gameData.adaptationSuccess || 0;\n    \n    let score = 50;\n    \n    if (difficultyChanges > 0) {\n      score += adaptationSuccess * 20;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateFrustrationTolerance(gameData) {\n    const errors = gameData.errors || 0;\n    const quitEarly = gameData.quitEarly || false;\n    const completion = gameData.completion || 0;\n    \n    let score = 70; // Base high score\n    \n    if (errors > 3 && !quitEarly) {\n      score += 15; // Tolerou erros\n    }\n    \n    if (completion > 0.8) {\n      score += 15; // Completou apesar de dificuldades\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateSocialInteractionScore(gameData) {\n    // Para jogos individuais, score baseado em engajamento\n    const engagement = this.calculateEngagementScore(gameData);\n    return Math.max(30, Math.min(80, engagement * 0.8));\n  }\n\n  calculateAttentionScore(gameData) {\n    const focusTime = gameData.focusTime || 0;\n    const distractions = gameData.distractions || 0;\n    const responseTime = gameData.averageResponseTime || 3000;\n    \n    let score = 50;\n    \n    if (focusTime > 60000) { // Mais de 1 minuto focado\n      score += 20;\n    }\n    \n    if (distractions < 2) {\n      score += 15;\n    }\n    \n    if (responseTime < 2000) {\n      score += 15;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateMemoryScore(gameData) {\n    const accuracy = gameData.accuracy || 0;\n    const patterns = gameData.patterns || [];\n    \n    let score = 50;\n    \n    if (accuracy > 70) {\n      score += 25;\n    }\n    \n    if (patterns.length > 3) {\n      score += 15;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateProcessingSpeedScore(gameData) {\n    const responseTime = gameData.averageResponseTime || 3000;\n    const accuracy = gameData.accuracy || 0;\n    \n    let score = 50;\n    \n    if (responseTime < 1500 && accuracy > 60) {\n      score += 30;\n    } else if (responseTime < 2500) {\n      score += 15;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateExecutiveFunctionScore(gameData) {\n    const planningEvidence = gameData.planningEvidence || 0;\n    const inhibitionControl = gameData.inhibitionControl || 0;\n    const workingMemory = gameData.workingMemory || 0;\n    \n    const score = (planningEvidence + inhibitionControl + workingMemory) / 3;\n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateVisualProcessingScore(gameData) {\n    const visualTasks = gameData.visualTasks || 0;\n    const visualAccuracy = gameData.visualAccuracy || 0;\n    \n    let score = 50;\n    \n    if (visualTasks > 5 && visualAccuracy > 70) {\n      score += 25;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateVisualPerceptionScore(gameData) {\n    return this.calculateVisualProcessingScore(gameData);\n  }\n\n  calculateAuditoryProcessingScore(gameData) {\n    const auditoryTasks = gameData.auditoryTasks || 0;\n    const auditoryAccuracy = gameData.auditoryAccuracy || 50;\n    \n    let score = 50;\n    \n    if (auditoryTasks > 0) {\n      score = auditoryAccuracy;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateTactileProcessingScore(gameData) {\n    const touchInteractions = gameData.touchInteractions || 0;\n    const touchAccuracy = gameData.touchAccuracy || 50;\n    \n    let score = 50;\n    \n    if (touchInteractions > 3) {\n      score = touchAccuracy;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateSensoryIntegrationScore(gameData) {\n    const visual = this.calculateVisualPerceptionScore(gameData);\n    const auditory = this.calculateAuditoryProcessingScore(gameData);\n    const tactile = this.calculateTactileProcessingScore(gameData);\n    \n    return (visual + auditory + tactile) / 3;\n  }\n\n  calculateFineMotorSkillsScore(gameData) {\n    const precision = gameData.precision || 50;\n    const motorControl = gameData.motorControl || 50;\n    \n    return (precision + motorControl) / 2;\n  }\n\n  calculateGrossMotorSkillsScore(gameData) {\n    const movements = gameData.movements || 0;\n    const coordination = gameData.coordination || 50;\n    \n    let score = 50;\n    \n    if (movements > 10) {\n      score = coordination;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  calculateCoordinationScore(gameData) {\n    const eyeHandCoordination = gameData.eyeHandCoordination || 50;\n    const bilateralCoordination = gameData.bilateralCoordination || 50;\n    \n    return (eyeHandCoordination + bilateralCoordination) / 2;\n  }\n\n  calculateMotorPlanningScore(gameData) {\n    const planningSteps = gameData.planningSteps || 0;\n    const executionSuccess = gameData.executionSuccess || 0;\n    \n    let score = 50;\n    \n    if (planningSteps > 0) {\n      score = executionSuccess;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  generateTherapeuticRecommendations(metrics, gameData) {\n    const recommendations = [];\n    \n    // Análise de engajamento\n    const engagement = this.calculateEngagementScore(gameData);\n    if (engagement < 50) {\n      recommendations.push({\n        category: 'engagement',\n        priority: 'high',\n        recommendation: 'Implementar estratégias de motivação e gamificação',\n        rationale: 'Baixo engajamento detectado'\n      });\n    }\n    \n    // Análise de atenção\n    const attention = this.calculateAttentionScore(gameData);\n    if (attention < 50) {\n      recommendations.push({\n        category: 'attention',\n        priority: 'medium',\n        recommendation: 'Exercícios de foco e concentração',\n        rationale: 'Dificuldades atencionais identificadas'\n      });\n    }\n    \n    // Análise de processamento\n    const processing = this.calculateProcessingSpeedScore(gameData);\n    if (processing < 50) {\n      recommendations.push({\n        category: 'processing',\n        priority: 'medium',\n        recommendation: 'Atividades para melhorar velocidade de processamento',\n        rationale: 'Processamento lento identificado'\n      });\n    }\n    \n    return recommendations;\n  }\n\n  generateProgressIndicators(metrics, gameData) {\n    return {\n      overallProgress: this.calculateOverallProgress(gameData),\n      strengthAreas: this.identifyStrengthAreas(gameData),\n      challengeAreas: this.identifyChallengeAreas(gameData),\n      developmentGoals: this.generateDevelopmentGoals(gameData),\n      milestones: this.generateMilestones(gameData)\n    };\n  }\n\n  generateGameSpecificInsights(metrics, gameData) {\n    // Implementação específica para cada jogo será adicionada pelos processadores\n    return {\n      gameType: this.gameType,\n      specificMetrics: metrics,\n      gamePerformance: this.calculateGamePerformance(gameData),\n      adaptationNeeds: this.identifyAdaptationNeeds(gameData)\n    };\n  }\n\n  calculateAnalysisConfidenceScore(metrics, gameData) {\n    let confidence = 50;\n    \n    // Fator de dados disponíveis\n    const dataPoints = Object.keys(gameData).length;\n    if (dataPoints > 10) confidence += 20;\n    else if (dataPoints > 5) confidence += 10;\n    \n    // Fator de métricas processadas\n    const metricsCount = Object.keys(metrics).length;\n    if (metricsCount > 5) confidence += 20;\n    else if (metricsCount > 3) confidence += 10;\n    \n    // Fator de tempo de sessão\n    const sessionTime = gameData.totalTime || 0;\n    if (sessionTime > 60000) confidence += 10; // Mais de 1 minuto\n    \n    return Math.max(0, Math.min(100, confidence));\n  }\n\n  generateFallbackTherapeuticAnalysis(gameData) {\n    return {\n      behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50, socialInteraction: 50 },\n      cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50, visualProcessing: 50 },\n      sensory: { visualPerception: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },\n      motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },\n      recommendations: [],\n      progressIndicators: { overallProgress: 50, strengthAreas: [], challengeAreas: [], developmentGoals: [], milestones: [] },\n      gameSpecificInsights: { gameType: this.gameType, specificMetrics: {}, gamePerformance: 50, adaptationNeeds: [] },\n      metadata: { analysisTimestamp: new Date().toISOString(), gameType: this.gameType, analysisVersion: '3.0.0', confidenceScore: 30 }\n    };\n  }\n\n  calculateOverallProgress(gameData) {\n    const accuracy = gameData.accuracy || 0;\n    const completion = gameData.completion || 0;\n    const engagement = this.calculateEngagementScore(gameData);\n    \n    return (accuracy + completion + engagement) / 3;\n  }\n\n  identifyStrengthAreas(gameData) {\n    const strengths = [];\n    \n    if (gameData.accuracy > 80) strengths.push('Precisão');\n    if (gameData.averageResponseTime < 2000) strengths.push('Velocidade de resposta');\n    if (this.calculateEngagementScore(gameData) > 70) strengths.push('Engajamento');\n    \n    return strengths;\n  }\n\n  identifyChallengeAreas(gameData) {\n    const challenges = [];\n    \n    if (gameData.accuracy < 50) challenges.push('Precisão');\n    if (gameData.averageResponseTime > 4000) challenges.push('Velocidade de resposta');\n    if (this.calculateEngagementScore(gameData) < 40) challenges.push('Engajamento');\n    \n    return challenges;\n  }\n\n  generateDevelopmentGoals(gameData) {\n    const goals = [];\n    \n    if (gameData.accuracy < 70) {\n      goals.push('Melhorar precisão para 70%+');\n    }\n    \n    if (gameData.averageResponseTime > 3000) {\n      goals.push('Reduzir tempo de resposta para menos de 3 segundos');\n    }\n    \n    return goals;\n  }\n\n  generateMilestones(gameData) {\n    return [\n      { milestone: 'Primeira sessão completa', achieved: gameData.completion > 0.8 },\n      { milestone: 'Precisão acima de 50%', achieved: gameData.accuracy > 50 },\n      { milestone: 'Engajamento sustentado', achieved: this.calculateEngagementScore(gameData) > 60 }\n    ];\n  }\n\n  calculateGamePerformance(gameData) {\n    const accuracy = gameData.accuracy || 0;\n    const completion = gameData.completion || 0;\n    const efficiency = gameData.efficiency || 0;\n    \n    return (accuracy + completion + efficiency) / 3;\n  }\n\n  identifyAdaptationNeeds(gameData) {\n    const needs = [];\n    \n    if (gameData.accuracy < 40) {\n      needs.push('Reduzir dificuldade');\n    }\n    \n    if (gameData.averageResponseTime > 5000) {\n      needs.push('Aumentar tempo limite');\n    }\n    \n    if (this.calculateEngagementScore(gameData) < 30) {\n      needs.push('Aumentar elementos motivacionais');\n    }\n    \n    return needs;\n  }\n  /**\n   * Processa métricas para estrutura padronizada do banco de dados\n   * @param {Object} metrics - Métricas processadas\n   * @param {Object} gameData - Dados do jogo\n   * @returns {Object} Métricas processadas para banco\n   */\n  processMetricsForDatabase(metrics, gameData) {\n    try {\n      return {\n        // Métricas básicas\n        basic: {\n          accuracy: gameData.accuracy || 0,\n          responseTime: gameData.averageResponseTime || 0,\n          completion: gameData.completion || 0,\n          score: gameData.score || 0,\n          duration: gameData.totalTime || 0,\n          attempts: gameData.attempts || 1,\n          errors: gameData.errors || 0\n        },\n        \n        // Métricas cognitivas\n        cognitive: {\n          attention: this.calculateAttentionScore(gameData),\n          memory: this.calculateMemoryScore(gameData),\n          processingSpeed: this.calculateProcessingSpeedScore(gameData),\n          executiveFunction: this.calculateExecutiveFunctionScore(gameData)\n        },\n        \n        // Métricas comportamentais\n        behavioral: {\n          engagement: this.calculateEngagementScore(gameData),\n          persistence: this.calculatePersistenceScore(gameData),\n          adaptability: this.calculateAdaptabilityScore(gameData),\n          frustrationTolerance: this.calculateFrustrationTolerance(gameData)\n        },\n        \n        // Métricas sensoriais\n        sensory: {\n          visualProcessing: this.calculateVisualProcessingScore(gameData),\n          auditoryProcessing: this.calculateAuditoryProcessingScore(gameData),\n          tactileProcessing: this.calculateTactileProcessingScore(gameData),\n          sensoryIntegration: this.calculateSensoryIntegrationScore(gameData)\n        },\n        \n        // Métricas motoras\n        motor: {\n          fineMotorSkills: this.calculateFineMotorSkillsScore(gameData),\n          grossMotorSkills: this.calculateGrossMotorSkillsScore(gameData),\n          coordination: this.calculateCoordinationScore(gameData),\n          motorPlanning: this.calculateMotorPlanningScore(gameData)\n        },\n        \n        // Métricas específicas do jogo\n        gameSpecific: metrics,\n        \n        // Metadados\n        metadata: {\n          gameType: this.gameType,\n          processingTimestamp: new Date().toISOString(),\n          version: '3.0.0'\n        }\n      };\n    } catch (error) {\n      this.logger?.error('❌ Erro ao processar métricas para banco:', error);\n      return {\n        basic: { accuracy: 0, responseTime: 0, completion: 0, score: 0, duration: 0, attempts: 1, errors: 0 },\n        cognitive: { attention: 50, memory: 50, processingSpeed: 50, executiveFunction: 50 },\n        behavioral: { engagement: 50, persistence: 50, adaptability: 50, frustrationTolerance: 50 },\n        sensory: { visualProcessing: 50, auditoryProcessing: 50, tactileProcessing: 50, sensoryIntegration: 50 },\n        motor: { fineMotorSkills: 50, grossMotorSkills: 50, coordination: 50, motorPlanning: 50 },\n        gameSpecific: metrics,\n        metadata: { gameType: this.gameType, processingTimestamp: new Date().toISOString(), version: '3.0.0' }\n      };\n    }\n  }\n\n}\n\nexport default ContagemNumerosProcessors;\n", "/**\r\n * @file ContagemNumerosConfig.js\r\n * @description Configurações do Jogo de Contagem de Números V3\r\n * @version 3.1.0\r\n */\r\n\r\n// 🎯 CONFIGURAÇÃO DE RODADAS - 4 A 7 RODADAS POR DIFICULDADE\r\nexport const ROUNDS_CONFIG = {\r\n  minRounds: 4,\r\n  maxRounds: 7,\r\n  difficultySettings: {\r\n    easy: { rounds: 4, numbersRange: [1, 5] },\r\n    medium: { rounds: 5, numbersRange: [1, 10] },\r\n    hard: { rounds: 7, numbersRange: [1, 15] }\r\n  }\r\n};\r\n\r\n// 🎯 CONFIGURAÇÃO DE ATIVIDADES E RODADAS\r\nexport const activitySettings = {\r\n  roundsPerActivity: {\r\n    EASY: 4,\r\n    MEDIUM: 5,\r\n    HARD: 7\r\n  },\r\n  minRoundsBeforeSwitch: 4,\r\n  maxActivityRounds: 7,\r\n  userControlledActivities: true // Usuário escolhe as atividades\r\n};\r\n\r\n/**\r\n * Configurações base para jogos\r\n */\r\nexport const BaseGameConfig = {\r\n  difficulties: [\r\n    { \r\n      id: 'easy', \r\n      name: '<PERSON><PERSON>cil', \r\n      description: 'Ideal para iniciantes'\r\n    },\r\n    { \r\n      id: 'medium', \r\n      name: '<PERSON><PERSON><PERSON>', \r\n      description: 'Desafio equilibrado'\r\n    },\r\n    { \r\n      id: 'hard', \r\n      name: 'Avançado', \r\n      description: 'Para especialistas'\r\n    }\r\n  ],\r\n  gameSettings: {\r\n    basePoints: 10,\r\n    streakBonus: 5,\r\n    maxWrongOptions: 3,\r\n    feedbackDuration: 2000,\r\n    celebrationDuration: 3000,\r\n    initialTtsDelay: 1000,\r\n    adaptiveDifficulty: true,\r\n    showProgressIndicators: true,\r\n    enableHints: true,\r\n    enableEncouragement: true\r\n  },\r\n  accessibility: {\r\n    tts: {\r\n      enabled: true,\r\n      speed: 0.8,\r\n      pitch: 1.0,\r\n      languages: ['pt-BR'],\r\n      autoRepeat: false,\r\n      contextualHelp: true\r\n    },\r\n    visual: {\r\n      highContrast: false,\r\n      largeText: false,\r\n      reducedMotion: false,\r\n      colorBlindSupport: true\r\n    },\r\n    motor: {\r\n      largeButtons: false,\r\n      keyboardNavigation: true,\r\n      touchOptimized: true\r\n    }\r\n  },\r\n  encouragingMessages: [\r\n    'Muito bem! Você está indo ótimo! 🎉',\r\n    'Excelente! Continue assim! ⭐',\r\n    'Fantástico! Suas habilidades estão crescendo! 👏'\r\n  ]\r\n};\r\n\r\n/**\r\n * Função para criar tipos de atividades\r\n */\r\nexport const createActivityType = (id, name, icon, description, difficultyConfig) => ({\r\n  id,\r\n  name,\r\n  icon,\r\n  description,\r\n  difficulty: difficultyConfig\r\n});\r\n\r\nexport const ContagemNumerosConfig = {\r\n  ...BaseGameConfig,\r\n  gameSettings: {\r\n    ...BaseGameConfig.gameSettings,\r\n    activityBonus: 3,\r\n    activityRotationInterval: 1,\r\n    maxConsecutiveErrors: 3,\r\n    // 🎯 Configuração padronizada de rodadas por atividade\r\n    activitySettings: {\r\n      roundsPerActivity: {\r\n        easy: 4,      // 4 rodadas para nível fácil\r\n        medium: 5,    // 5 rodadas para nível médio\r\n        hard: 7       // 7 rodadas para nível avançado\r\n      }\r\n    },\r\n    // 🎯 Configurações específicas para atividades simples (visual counting, soma simples, etc)\r\n    simpleDifficultyConfigs: {\r\n      easy: {\r\n        name: 'Fácil',\r\n        range: [1, 3],\r\n        maxNumber: 5\r\n      },\r\n      medium: {\r\n        name: 'Médio', \r\n        range: [1, 5],\r\n        maxNumber: 8\r\n      },\r\n      hard: {\r\n        name: 'Avançado',\r\n        range: [1, 8], \r\n        maxNumber: 10\r\n      }\r\n    }\r\n  },\r\n  difficulties: [\r\n    { \r\n      id: 'easy', \r\n      name: 'Fácil', \r\n      range: [1, 4], \r\n      description: 'Contagem de 1 a 5',\r\n      estimationRange: [3, 8],\r\n      sequenceLength: 3,\r\n      comparisonMax: 5,\r\n      patternLength: 4\r\n    },\r\n    { \r\n      id: 'medium', \r\n      name: 'Médio', \r\n      range: [3, 8], \r\n      description: 'Contagem de 3 a 8',\r\n      estimationRange: [5, 12],\r\n      sequenceLength: 4,\r\n      comparisonMax: 8,\r\n      patternLength: 5\r\n    },\r\n    { \r\n      id: 'hard', \r\n      name: 'Avançado', \r\n      range: [6, 12], \r\n      description: 'Contagem de 6 a 12',\r\n      estimationRange: [8, 20],\r\n      sequenceLength: 5,\r\n      comparisonMax: 12,\r\n      patternLength: 6\r\n    }\r\n  ],\r\n  activityTypes: {\r\n    NUMBER_COUNTING: createActivityType(\r\n      'number_counting',\r\n      'Contagem Simples',\r\n      '🔢',\r\n      'Conte os objetos na tela e escolha o número correto',\r\n      {\r\n        easy: { range: [1, 4], objects: 'small' },\r\n        medium: { range: [3, 8], objects: 'medium' },\r\n        hard: { range: [6, 12], objects: 'large' }\r\n      }\r\n    ),\r\n    SOUND_MATCHING: createActivityType(\r\n      'sound_matching',\r\n      'Combinação de Sons',\r\n      '🎵',\r\n      'Associe o som do número com a quantidade correta',\r\n      {\r\n        easy: { range: [1, 4], playSpeed: 'slow' },\r\n        medium: { range: [3, 8], playSpeed: 'normal' },\r\n        hard: { range: [6, 12], playSpeed: 'fast' }\r\n      }\r\n    ),\r\n    NUMBER_ESTIMATION: createActivityType(\r\n      'number_estimation',\r\n      'Estimativa Numérica',\r\n      '🎯',\r\n      'Estime quantidades sem contar individualmente',\r\n      {\r\n        easy: { range: [3, 8], showTime: 4000, tolerance: 1 },\r\n        medium: { range: [5, 12], showTime: 3000, tolerance: 1 },\r\n        hard: { range: [8, 20], showTime: 2000, tolerance: 2 }\r\n      }\r\n    ),\r\n    SEQUENCE_COMPLETION: createActivityType(\r\n      'sequence_completion',\r\n      'Completar Sequência',\r\n      '📝',\r\n      'Complete sequências numéricas crescentes',\r\n      {\r\n        easy: { length: 3, step: 1, maxNumber: 10 },\r\n        medium: { length: 4, step: [1, 2], maxNumber: 15 },\r\n        hard: { length: 5, step: [1, 2, 3], maxNumber: 20 }\r\n      }\r\n    ),\r\n    NUMBER_COMPARISON: createActivityType(\r\n      'number_comparison',\r\n      'Comparação Numérica',\r\n      '🔢',\r\n      'Compare quantidades e identifique maior/menor',\r\n      {\r\n        easy: { maxNumber: 5, groups: 2 },\r\n        medium: { maxNumber: 8, groups: 2 },\r\n        hard: { maxNumber: 12, groups: 3 }\r\n      }\r\n    ),\r\n    PATTERN_RECOGNITION: createActivityType(\r\n      'pattern_recognition',\r\n      'Reconhecimento de Padrões',\r\n      '🔍',\r\n      'Identifique padrões em sequências numéricas',\r\n      {\r\n        easy: { patternLength: 4, complexity: 'simple' },\r\n        medium: { patternLength: 5, complexity: 'medium' },\r\n        hard: { patternLength: 6, complexity: 'complex' }\r\n      }\r\n    )\r\n  },\r\n  sequences: {\r\n    easy: [\r\n      { sequence: [1, 2, 3], missing: 4, options: [4, 5, 6], type: 'crescente_simples' },\r\n      { sequence: [2, 3, 4], missing: 5, options: [5, 6, 7], type: 'crescente_simples' },\r\n      { sequence: [3, 4, 5], missing: 6, options: [6, 7, 8], type: 'crescente_simples' },\r\n      { sequence: [5, 4, 3], missing: 2, options: [2, 1, 6], type: 'decrescente_simples' }\r\n    ],\r\n    medium: [\r\n      { sequence: [2, 4, 6], missing: 8, options: [8, 9, 10], type: 'pares' },\r\n      { sequence: [1, 3, 5], missing: 7, options: [7, 8, 9], type: 'ímpares' },\r\n      { sequence: [5, 6, 7], missing: 8, options: [8, 9, 10], type: 'crescente_simples' },\r\n      { sequence: [10, 8, 6], missing: 4, options: [4, 3, 2], type: 'decrescente_pares' }\r\n    ],\r\n    hard: [\r\n      { sequence: [3, 6, 9], missing: 12, options: [12, 15, 18], type: 'múltiplos_3' },\r\n      { sequence: [2, 5, 8], missing: 11, options: [11, 14, 17], type: 'soma_3' },\r\n      { sequence: [10, 9, 8], missing: 7, options: [7, 6, 5], type: 'decrescente_simples' },\r\n      { sequence: [1, 4, 7], missing: 10, options: [10, 13, 16], type: 'soma_3' }\r\n    ]\r\n  },\r\n  patterns: {\r\n    easy: [\r\n      { pattern: [1, 1, 2, 2], next: [3, 3], description: 'Números dobrados', type: 'repetição' },\r\n      { pattern: [1, 2, 1, 2], next: [1, 2], description: 'Alternância simples', type: 'alternância' },\r\n      { pattern: [2, 2, 3, 3], next: [4, 4], description: 'Pares crescentes', type: 'repetição_crescente' }\r\n    ],\r\n    medium: [\r\n      { pattern: [1, 2, 3, 1, 2], next: [3], description: 'Sequência repetida', type: 'ciclo' },\r\n      { pattern: [2, 4, 2, 4], next: [2], description: 'Par-par alternado', type: 'alternância' },\r\n      { pattern: [1, 3, 1, 3], next: [1], description: 'Ímpar alternado', type: 'alternância_ímpar' }\r\n    ],\r\n    hard: [\r\n      { pattern: [1, 3, 5, 7], next: [9], description: 'Números ímpares', type: 'progressão_ímpar' },\r\n      { pattern: [2, 4, 8, 16], next: [32], description: 'Dobrar o anterior', type: 'multiplicação' },\r\n      { pattern: [1, 1, 2, 3], next: [5], description: 'Fibonacci simples', type: 'fibonacci' }\r\n    ]\r\n  },\r\n  categories: [\r\n    {\r\n      id: 'fruits',\r\n      name: 'Frutas',\r\n      emoji: '🍎',\r\n      objects: [\r\n        { id: 'apple', emoji: '🍎', name: 'Maçã' },\r\n        { id: 'banana', emoji: '🍌', name: 'Banana' },\r\n        { id: 'orange', emoji: '🍊', name: 'Laranja' },\r\n        { id: 'grapes', emoji: '🍇', name: 'Uvas' },\r\n        { id: 'strawberry', emoji: '🍓', name: 'Morango' },\r\n        { id: 'pineapple', emoji: '🍍', name: 'Abacaxi' }\r\n      ]\r\n    },\r\n    {\r\n      id: 'animals',\r\n      name: 'Animais',\r\n      emoji: '🐶',\r\n      objects: [\r\n        { id: 'dog', emoji: '🐶', name: 'Cachorro' },\r\n        { id: 'cat', emoji: '🐱', name: 'Gato' },\r\n        { id: 'rabbit', emoji: '🐰', name: 'Coelho' },\r\n        { id: 'bear', emoji: '🐻', name: 'Urso' },\r\n        { id: 'pig', emoji: '🐷', name: 'Porco' },\r\n        { id: 'cow', emoji: '🐮', name: 'Vaca' }\r\n      ]\r\n    },\r\n    {\r\n      id: 'toys',\r\n      name: 'Brinquedos',\r\n      emoji: '🧸',\r\n      objects: [\r\n        { id: 'teddy', emoji: '🧸', name: 'Ursinho' },\r\n        { id: 'ball', emoji: '⚽', name: 'Bola' },\r\n        { id: 'car', emoji: '🚗', name: 'Carrinho' },\r\n        { id: 'doll', emoji: '🪆', name: 'Boneca' },\r\n        { id: 'blocks', emoji: '🧱', name: 'Blocos' },\r\n        { id: 'kite', emoji: '🪁', name: 'Pipa' }\r\n      ]\r\n    },\r\n    {\r\n      id: 'nature',\r\n      name: 'Natureza',\r\n      emoji: '🌸',\r\n      objects: [\r\n        { id: 'flower', emoji: '🌸', name: 'Flor' },\r\n        { id: 'tree', emoji: '🌳', name: 'Árvore' },\r\n        { id: 'sun', emoji: '☀️', name: 'Sol' },\r\n        { id: 'star', emoji: '⭐', name: 'Estrela' },\r\n        { id: 'moon', emoji: '🌙', name: 'Lua' },\r\n        { id: 'cloud', emoji: '☁️', name: 'Nuvem' }\r\n      ]\r\n    },\r\n    {\r\n      id: 'shapes',\r\n      name: 'Formas',\r\n      emoji: '🔴',\r\n      objects: [\r\n        { id: 'circle', emoji: '🔴', name: 'Círculo' },\r\n        { id: 'square', emoji: '🟦', name: 'Quadrado' },\r\n        { id: 'triangle', emoji: '🔺', name: 'Triângulo' },\r\n        { id: 'diamond', emoji: '🔶', name: 'Losango' },\r\n        { id: 'heart', emoji: '❤️', name: 'Coração' },\r\n        { id: 'star_shape', emoji: '⭐', name: 'Estrela' }\r\n      ]\r\n    },\r\n    {\r\n      id: 'transport',\r\n      name: 'Transporte',\r\n      emoji: '🚗',\r\n      objects: [\r\n        { id: 'car', emoji: '🚗', name: 'Carro' },\r\n        { id: 'bus', emoji: '🚌', name: 'Ônibus' },\r\n        { id: 'bike', emoji: '🚲', name: 'Bicicleta' },\r\n        { id: 'plane', emoji: '✈️', name: 'Avião' },\r\n        { id: 'train', emoji: '🚂', name: 'Trem' },\r\n        { id: 'boat', emoji: '⛵', name: 'Barco' }\r\n      ]\r\n    }\r\n  ],\r\n  // 🎯 CONFIGURAÇÕES ESPECÍFICAS PARA COMPARAÇÃO DE QUANTIDADE V2\r\n  quantityComparisonConfig: {\r\n    // Tipos de desafios de comparação\r\n    challengeTypes: [\r\n      {\r\n        id: 'more_than',\r\n        question: 'Qual grupo tem MAIS objetos?',\r\n        instruction: 'Escolha o grupo com mais objetos',\r\n        tts: 'Qual grupo tem mais objetos? Clique no grupo maior.',\r\n        correctSide: 'larger'\r\n      },\r\n      {\r\n        id: 'less_than', \r\n        question: 'Qual grupo tem MENOS objetos?',\r\n        instruction: 'Escolha o grupo com menos objetos',\r\n        tts: 'Qual grupo tem menos objetos? Clique no grupo menor.',\r\n        correctSide: 'smaller'\r\n      },\r\n      {\r\n        id: 'equal_check',\r\n        question: 'Os grupos têm a MESMA quantidade?',\r\n        instruction: 'Clique em SIM se forem iguais, NÃO se diferentes',\r\n        tts: 'Os dois grupos têm a mesma quantidade de objetos?',\r\n        correctSide: 'equality'\r\n      },\r\n      {\r\n        id: 'count_difference',\r\n        question: 'Quantos objetos A MAIS tem o grupo maior?',\r\n        instruction: 'Conte a diferença entre os grupos',\r\n        tts: 'Quantos objetos a mais tem o grupo com mais objetos?',\r\n        correctSide: 'difference'\r\n      }\r\n    ],\r\n    // Configuração de dificuldade para comparação\r\n    difficultySettings: {\r\n      easy: {\r\n        maxNumber: 5,\r\n        minNumber: 1,\r\n        maxDifference: 2,\r\n        allowedTypes: ['more_than', 'less_than'],\r\n        equalityChance: 0.2\r\n      },\r\n      medium: {\r\n        maxNumber: 8, \r\n        minNumber: 2,\r\n        maxDifference: 4,\r\n        allowedTypes: ['more_than', 'less_than', 'equal_check'],\r\n        equalityChance: 0.25\r\n      },\r\n      hard: {\r\n        maxNumber: 12,\r\n        minNumber: 3, \r\n        maxDifference: 6,\r\n        allowedTypes: ['more_than', 'less_than', 'equal_check', 'count_difference'],\r\n        equalityChance: 0.3\r\n      }\r\n    },\r\n    // Mensagens contextuais expandidas\r\n    encouragementMessages: {\r\n      more_than: [\r\n        'Perfeito! Você identificou o grupo maior! 📈',\r\n        'Excelente! Você sabe qual tem mais! 🎯', \r\n        'Ótima observação! Grupo maior identificado! 👀'\r\n      ],\r\n      less_than: [\r\n        'Muito bem! Você encontrou o grupo menor! 📉',\r\n        'Correto! Você identificou o menor grupo! 🔍',\r\n        'Perfeito! Menor quantidade identificada! ✨'\r\n      ],\r\n      equal_check: [\r\n        'Incrível! Você percebeu que são iguais! ⚖️',\r\n        'Excelente! Mesma quantidade identificada! 🎯',\r\n        'Perfeito! Você tem um ótimo olho para igualdade! 👁️'\r\n      ],\r\n      count_difference: [\r\n        'Fantástico! Você calculou a diferença! 🧮', \r\n        'Perfeito! Matemática de subtração excelente! ➖',\r\n        'Incrível! Você domina as diferenças numéricas! 🔢'\r\n      ]\r\n    }\r\n  },\r\n  activityMessages: {\r\n    number_counting: {\r\n      instructions: [\r\n        'Conte quantos {object}s você vê na tela!',\r\n        'Quantos {object}s há aqui?',\r\n        'Vamos contar os {object}s juntos!'\r\n      ],\r\n      encouragement: [\r\n        'Ótima contagem!',\r\n        'Você sabe contar muito bem!',\r\n        'Perfeito! Continue contando!'\r\n      ]\r\n    },\r\n    sound_matching: {\r\n      instructions: [\r\n        'Ouça o número e encontre a quantidade correspondente!',\r\n        'Escute com atenção e escolha a resposta certa!',\r\n        'O que você ouviu? Encontre a quantidade!'\r\n      ],\r\n      encouragement: [\r\n        'Excelente audição!',\r\n        'Você ouve muito bem!',\r\n        'Perfeita associação de som!'\r\n      ]\r\n    },\r\n    number_estimation: {\r\n      instructions: [\r\n        'Estime quantos há sem contar um por um!',\r\n        'Olhe rapidamente e faça uma estimativa!',\r\n        'Quanto você acha que tem aqui?'\r\n      ],\r\n      encouragement: [\r\n        'Ótima estimativa!',\r\n        'Você tem um bom olho para números!',\r\n        'Estimativa perfeita!'\r\n      ]\r\n    },\r\n    sequence_completion: {\r\n      instructions: [\r\n        'Complete a sequência numérica!',\r\n        'Qual número vem a seguir?',\r\n        'Continue o padrão da sequência!'\r\n      ],\r\n      encouragement: [\r\n        'Excelente lógica sequencial!',\r\n        'Você entende padrões muito bem!',\r\n        'Sequência perfeita!'\r\n      ]\r\n    },\r\n    number_comparison: {\r\n      instructions: [\r\n        'Compare as quantidades e escolha a resposta!',\r\n        'Qual grupo tem mais? Qual tem menos?',\r\n        'Compare e decida!',\r\n        'Observe bem os dois grupos!',\r\n        'Use seus olhos de matemático!'\r\n      ],\r\n      encouragement: [\r\n        'Ótima comparação!',\r\n        'Você sabe comparar números!',\r\n        'Comparação perfeita!',\r\n        'Excelente olho matemático!',\r\n        'Você domina as comparações!'\r\n      ]\r\n    },\r\n    pattern_recognition: {\r\n      instructions: [\r\n        'Identifique o padrão e continue!',\r\n        'Que padrão você vê aqui?',\r\n        'Complete o padrão numérico!'\r\n      ],\r\n      encouragement: [\r\n        'Excelente reconhecimento de padrão!',\r\n        'Você é ótimo com padrões!',\r\n        'Padrão identificado perfeitamente!'\r\n      ]\r\n    }\r\n  },\r\n  // 🎯 GERADOR AVANÇADO DE DADOS DE COMPARAÇÃO V2\r\n  generateQuantityComparisonData: (difficulty = 'easy') => {\r\n    const settings = ContagemNumerosConfig.quantityComparisonConfig.difficultySettings[difficulty];\r\n    const challengeTypes = ContagemNumerosConfig.quantityComparisonConfig.challengeTypes;\r\n    const allowedTypes = settings.allowedTypes;\r\n    const categories = ContagemNumerosConfig.categories;\r\n    \r\n    // Selecionar tipo de desafio aleatório permitido para a dificuldade\r\n    const selectedType = allowedTypes[Math.floor(Math.random() * allowedTypes.length)];\r\n    const challengeConfig = challengeTypes.find(type => type.id === selectedType);\r\n    \r\n    // Gerar números para os grupos\r\n    let count1, count2;\r\n    \r\n    // Controlar se deve ser igual baseado na chance de igualdade\r\n    const shouldBeEqual = Math.random() < settings.equalityChance && selectedType === 'equal_check';\r\n    \r\n    if (shouldBeEqual) {\r\n      count1 = Math.floor(Math.random() * (settings.maxNumber - settings.minNumber + 1)) + settings.minNumber;\r\n      count2 = count1; // Grupos iguais\r\n    } else {\r\n      count1 = Math.floor(Math.random() * (settings.maxNumber - settings.minNumber + 1)) + settings.minNumber;\r\n      count2 = Math.floor(Math.random() * (settings.maxNumber - settings.minNumber + 1)) + settings.minNumber;\r\n      \r\n      // Garantir que sejam diferentes e respeitem a diferença máxima\r\n      while (count1 === count2 || Math.abs(count1 - count2) > settings.maxDifference) {\r\n        count2 = Math.floor(Math.random() * (settings.maxNumber - settings.minNumber + 1)) + settings.minNumber;\r\n      }\r\n    }\r\n    \r\n    // Selecionar duas categorias DIFERENTES de emojis\r\n    const category1 = categories[Math.floor(Math.random() * categories.length)];\r\n    let category2 = categories[Math.floor(Math.random() * categories.length)];\r\n    \r\n    // Garantir categorias diferentes para evitar confusão visual\r\n    while (category2.id === category1.id) {\r\n      category2 = categories[Math.floor(Math.random() * categories.length)];\r\n    }\r\n    \r\n    // Gerar grupos com emojis VARIADOS da mesma categoria\r\n    const generateGroupObjects = (category, count) => {\r\n      const objects = [];\r\n      for (let i = 0; i < count; i++) {\r\n        // Usar diferentes emojis da categoria (com repetição permitida se necessário)\r\n        const randomObject = category.objects[Math.floor(Math.random() * category.objects.length)];\r\n        objects.push({\r\n          id: i,\r\n          emoji: randomObject.emoji,\r\n          name: randomObject.name,\r\n          category: category.name\r\n        });\r\n      }\r\n      return objects;\r\n    };\r\n    \r\n    const group1 = generateGroupObjects(category1, count1);\r\n    const group2 = generateGroupObjects(category2, count2);\r\n    \r\n    // Determinar resposta correta baseada no tipo de desafio\r\n    let correctAnswer, explanation;\r\n    \r\n    switch (selectedType) {\r\n      case 'more_than':\r\n        correctAnswer = count1 > count2 ? 'left' : 'right';\r\n        explanation = `Grupo ${correctAnswer === 'left' ? 'A' : 'B'} tem mais objetos (${Math.max(count1, count2)} > ${Math.min(count1, count2)})`;\r\n        break;\r\n        \r\n      case 'less_than':\r\n        correctAnswer = count1 < count2 ? 'left' : 'right';\r\n        explanation = `Grupo ${correctAnswer === 'left' ? 'A' : 'B'} tem menos objetos (${Math.min(count1, count2)} < ${Math.max(count1, count2)})`;\r\n        break;\r\n        \r\n      case 'equal_check':\r\n        correctAnswer = count1 === count2 ? 'equal' : 'different';\r\n        explanation = count1 === count2 ? \r\n          `Ambos os grupos têm ${count1} objetos` : \r\n          `Grupos diferentes: ${count1} ≠ ${count2}`;\r\n        break;\r\n        \r\n      case 'count_difference':\r\n        const difference = Math.abs(count1 - count2);\r\n        correctAnswer = difference.toString();\r\n        explanation = `Diferença: |${count1} - ${count2}| = ${difference}`;\r\n        break;\r\n        \r\n      default:\r\n        correctAnswer = count1 > count2 ? 'left' : 'right';\r\n        explanation = `Comparação padrão: ${count1} vs ${count2}`;\r\n    }\r\n    \r\n    return {\r\n      challengeType: selectedType,\r\n      challengeConfig,\r\n      group1: {\r\n        objects: group1,\r\n        count: count1,\r\n        category: category1.name,\r\n        categoryEmoji: category1.emoji\r\n      },\r\n      group2: {\r\n        objects: group2,\r\n        count: count2, \r\n        category: category2.name,\r\n        categoryEmoji: category2.emoji\r\n      },\r\n      correctAnswer,\r\n      explanation,\r\n      instruction: challengeConfig.question,\r\n      ttsInstruction: challengeConfig.tts,\r\n      difficulty,\r\n      metadata: {\r\n        difference: Math.abs(count1 - count2),\r\n        isEqual: count1 === count2,\r\n        larger: count1 > count2 ? 'left' : 'right',\r\n        smaller: count1 < count2 ? 'left' : 'right'\r\n      }\r\n    };\r\n  },\r\n  encouragingMessages: [\r\n    ...BaseGameConfig.encouragingMessages,\r\n    'Perfeito! Você é ótimo com atividades numéricas! 🔢',\r\n    'Incrível! Você domina diferentes tipos de atividades! 🌟'\r\n  ],\r\n  gameInfo: {\r\n    title: 'Contagem de Números V3',\r\n    description: 'Desenvolva habilidades matemáticas com 6 atividades diversificadas',\r\n    icon: '🎯',\r\n    category: 'mathematics',\r\n    ageRange: '3-8',\r\n    skills: [\r\n      'contagem básica', \r\n      'estimativa numérica', \r\n      'sequências', \r\n      'comparação', \r\n      'padrões', \r\n      'associação sonora',\r\n      'raciocínio lógico',\r\n      'matemática básica'\r\n    ],\r\n    version: '3.1.0',\r\n    activities: 6\r\n  },\r\n  categoryNames: {\r\n    fruits: 'frutas',\r\n    animals: 'animais',\r\n    toys: 'brinquedos',\r\n    nature: 'elementos da natureza',\r\n    shapes: 'formas geométricas',\r\n    transport: 'meios de transporte'\r\n  }\r\n};", "/**\n * 🔢 CONTAGEM DE NÚMEROS V4 - REESTRUTURADO\n * Portal Betina V3 - Jogo educativo estável seguindo padrões de sucesso\n * Baseado nos padrões dos jogos Musical Sequence e Color Match\n */\n\nimport React, { useState, useEffect, useCallback, useContext } from 'react';\nimport { ContagemNumerosConfig } from './ContagemNumerosConfig.js';\nimport { SystemContext } from '../../components/context/SystemContext.jsx';\nimport { useAccessibilityContext } from '../../components/context/AccessibilityContext';\n\n// Importa o componente padrão de tela de dificuldade\nimport GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';\n\n// Hook unificado para integração com backend\nimport { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';\n\n// 🧠 COLETORES AVANÇADOS - Análise cognitiva em tempo real\nimport { NumberCountingCollectorsHub } from './collectors/index.js';\n\n// 🔄 Importar hook multissensorial\nimport { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';\n\n// 🔊 SISTEMA TTS PADRONIZADO - Portal Betina V3\nimport { useStandardTTS, StandardTTSButton, TTS_MESSAGES } from '../../components/shared/StandardTTS';\n\n// Importa estilos modulares\nimport styles from './ContagemNumeros.module.css';\n\n// 🎯 SISTEMA DE ATIVIDADES SIMPLIFICADO V4 - ESTÁVEL E INFANTIL\nconst ACTIVITY_TYPES = {\n  VISUAL_COUNTING: { \n    id: 'visual_counting', \n    name: 'Contagem Visual', \n    description: 'Conte os objetos que você vê',\n    icon: '👁️'\n  },\n  SIMPLE_ADDITION: { \n    id: 'simple_addition', \n    name: 'Soma Simples', \n    description: 'Some números pequenos',\n    icon: '➕'\n  },\n  NUMBER_RECOGNITION: { \n    id: 'number_recognition', \n    name: 'Reconhecimento', \n    description: 'Encontre o número correto',\n    icon: '🔍'\n  },\n  QUANTITY_COMPARISON: { \n    id: 'quantity_comparison', \n    name: 'Comparação', \n    description: 'Qual tem mais?',\n    icon: '⚖️'\n  }\n};\n\n//  COMPONENTE PRINCIPAL\nconst ContagemNumerosGame = ({ onBack }) => {\n  const { user, sessionId } = useContext(SystemContext);\n\n  // =====================================================\n  // 🔊 SISTEMA TTS PADRONIZADO - Portal Betina V3\n  // =====================================================\n  const {\n    ttsActive,\n    toggleTTS,\n    speak,\n    stopSpeaking,\n    speakGameInstructions,\n    speakFeedback,\n    speakProgress,\n    speakGameEnd\n  } = useStandardTTS('contagem_numeros');\n\n  // Instruções específicas do jogo\n  const gameInstructions = \"Conte os objetos na tela e escolha o número correto. Use os botões para selecionar sua resposta.\";\n\n  // 🎯 ESTADO SIMPLIFICADO E ESTÁVEL - Baseado nos padrões de sucesso\n  const [gameState, setGameState] = useState({\n    status: 'start', // 'start', 'playing', 'finished'\n    score: 0,\n    round: 1,\n    totalRounds: 4, // Usar sistema baseado na dificuldade (4-7 rodadas)\n    difficulty: 'easy',\n    accuracy: 100,\n    roundStartTime: null,\n    \n    // Sistema de atividades controlado pelo usuário\n    currentActivity: ACTIVITY_TYPES.VISUAL_COUNTING.id,\n    userControlledActivities: true, // Usuário escolhe as atividades\n    \n    // Dados específicos de atividades - PERSISTENTES\n    activityData: {\n      visual_counting: {\n        objects: [],\n        correctCount: 0,\n        options: [],\n        instruction: ''\n      },\n      simple_addition: {\n        number1: 0,\n        number2: 0,\n        correctAnswer: 0,\n        options: [],\n        instruction: ''\n      },\n      number_recognition: {\n        targetNumber: 0,\n        options: [],\n        instruction: ''\n      },\n      quantity_comparison: {\n        group1: [],\n        group2: [],\n        correctAnswer: '',\n        instruction: ''\n      }\n    }\n  });\n\n  const [showStartScreen, setShowStartScreen] = useState(true);\n  const [gameStarted, setGameStarted] = useState(false);\n  const [feedback, setFeedback] = useState(null);\n\n  // 🧠 COLETORES\n  const [collectorsHub] = useState(() => new NumberCountingCollectorsHub());\n\n  // 🎯 HOOKS INTEGRADOS\n  const {\n    startUnifiedSession,\n    recordInteraction,\n    metrics,\n    isSessionActive,\n    endUnifiedSession\n  } = useUnifiedGameLogic('contagem-numeros');\n\n  const {\n    initMultisensory,\n    multisensoryIntegration\n  } = useMultisensoryIntegration(sessionId, {\n    gameType: 'contagem_numeros',\n    sensorTypes: {\n      visual: true,\n      haptic: true,\n      tts: ttsActive,\n      gestural: true,\n      biometric: true\n    },\n    adaptiveMode: true,\n    learningStyle: user?.profile?.learningStyle || 'visual'\n  });\n\n  // TTS automático ao iniciar o jogo\n  useEffect(() => {\n    if (gameState.status === 'playing' && ttsActive) {\n      setTimeout(() => {\n        speakGameInstructions('Contagem de Números', gameInstructions);\n      }, 1000);\n    }\n  }, [gameState.status, ttsActive, speakGameInstructions, gameInstructions]);\n\n  // 🎯 GERAR DADOS DA ATIVIDADE - ESTÁVEL E PERSISTENTE\n  const generateActivityData = useCallback((activityId, difficulty) => {\n    const config = ContagemNumerosConfig.gameSettings.simpleDifficultyConfigs[difficulty];\n    \n    switch (activityId) {\n      case ACTIVITY_TYPES.VISUAL_COUNTING.id:\n        return generateVisualCountingData(config);\n      case ACTIVITY_TYPES.SIMPLE_ADDITION.id:\n        return generateSimpleAdditionData(config);\n      case ACTIVITY_TYPES.NUMBER_RECOGNITION.id:\n        return generateNumberRecognitionData(config);\n      case ACTIVITY_TYPES.QUANTITY_COMPARISON.id:\n        return generateQuantityComparisonData(config);\n      default:\n        return generateVisualCountingData(config);\n    }\n  }, []);\n\n  // 🎯 GERADOR DE CONTAGEM VISUAL - SIMPLES E ESTÁVEL\n  const generateVisualCountingData = useCallback((config) => {\n    const correctCount = Math.floor(Math.random() * (config.range[1] - config.range[0] + 1)) + config.range[0];\n    const categories = ContagemNumerosConfig.categories;\n    const randomCategory = categories[Math.floor(Math.random() * categories.length)];\n    const randomObject = randomCategory.objects[Math.floor(Math.random() * randomCategory.objects.length)];\n    \n    // Criar objetos para exibição - ARRAY SIMPLES\n    const objects = Array.from({ length: correctCount }, (_, index) => ({\n      id: index,\n      emoji: randomObject.emoji,\n      name: randomObject.name\n    }));\n    \n    // Criar opções de resposta\n    const options = new Set([correctCount]);\n    while (options.size < 4) {\n      const wrongOption = Math.floor(Math.random() * config.maxNumber) + 1;\n      if (wrongOption !== correctCount && wrongOption > 0) {\n        options.add(wrongOption);\n      }\n    }\n    \n    return {\n      objects,\n      correctCount,\n      options: Array.from(options).sort(() => Math.random() - 0.5),\n      instruction: `Conte quantos ${randomObject.name.toLowerCase()}s você vê na tela`\n    };\n  }, []);\n\n  // 🎯 GERADOR DE SOMA SIMPLES\n  const generateSimpleAdditionData = useCallback((config) => {\n    const number1 = Math.floor(Math.random() * config.range[1]) + 1;\n    const number2 = Math.floor(Math.random() * (config.range[1] - number1)) + 1;\n    const correctAnswer = number1 + number2;\n    \n    // Criar opções de resposta\n    const options = new Set([correctAnswer]);\n    while (options.size < 4) {\n      const wrongOption = Math.floor(Math.random() * config.maxNumber) + 1;\n      if (wrongOption !== correctAnswer && wrongOption > 0) {\n        options.add(wrongOption);\n      }\n    }\n    \n    return {\n      number1,\n      number2,\n      correctAnswer,\n      options: Array.from(options).sort(() => Math.random() - 0.5),\n      instruction: `Quanto é ${number1} + ${number2}?`\n    };\n  }, []);\n\n  // 🎯 GERADOR DE RECONHECIMENTO DE NÚMERO - VERSÃO MELHORADA V2\n  const generateNumberRecognitionData = useCallback((config) => {\n    // Tipos de desafio variados\n    const challengeTypes = ['visual_count', 'written_number', 'sequence', 'comparison', 'pattern'];\n    let availableTypes = challengeTypes;\n    \n    // Filtrar tipos baseado na dificuldade\n    if (gameState.difficulty === 'easy') {\n      availableTypes = ['visual_count', 'written_number'];\n    } else if (gameState.difficulty === 'medium') {\n      availableTypes = ['visual_count', 'written_number', 'sequence', 'pattern'];\n    }\n    \n    const challengeType = availableTypes[Math.floor(Math.random() * availableTypes.length)];\n    \n    switch (challengeType) {\n      case 'visual_count': {\n        // Mostrar objetos para contar\n        const targetNumber = Math.floor(Math.random() * config.maxNumber) + 1;\n        const categories = ContagemNumerosConfig.categories;\n        const randomCategory = categories[Math.floor(Math.random() * categories.length)];\n        const randomObject = randomCategory.objects[Math.floor(Math.random() * randomCategory.objects.length)];\n        \n        const objects = Array.from({ length: targetNumber }, (_, i) => ({\n          id: i,\n          emoji: randomObject.emoji,\n          name: randomObject.name\n        }));\n        \n        const options = generateNumberOptions(targetNumber, config.maxNumber);\n        \n        return {\n          challengeType: 'visual_count',\n          targetNumber,\n          objects,\n          options,\n          instruction: `Conte os ${randomObject.name.toLowerCase()}s e encontre o número correto`\n        };\n      }\n      \n      case 'written_number': {\n        // Número por extenso\n        const targetNumber = Math.floor(Math.random() * config.maxNumber) + 1;\n        const numberWords = ['', 'um', 'dois', 'três', 'quatro', 'cinco', 'seis', 'sete', 'oito', 'nove', 'dez'];\n        const options = generateNumberOptions(targetNumber, config.maxNumber);\n        \n        return {\n          challengeType: 'written_number',\n          targetNumber,\n          writtenNumber: numberWords[targetNumber] || targetNumber.toString(),\n          options,\n          instruction: `Encontre o número que representa: \"${numberWords[targetNumber]}\"`\n        };\n      }\n      \n      case 'sequence': {\n        // Sequência numérica\n        const targetNumber = Math.floor(Math.random() * (config.maxNumber - 1)) + 2; // mín 2 para ter antecessor\n        const beforeNumber = targetNumber - 1;\n        const options = generateNumberOptions(targetNumber, config.maxNumber);\n        \n        return {\n          challengeType: 'sequence',\n          targetNumber,\n          beforeNumber,\n          options,\n          instruction: ``\n        };\n      }\n      \n      case 'comparison': {\n        // Comparação entre números\n        const number1 = Math.floor(Math.random() * config.maxNumber) + 1;\n        let number2 = Math.floor(Math.random() * config.maxNumber) + 1;\n        while (number2 === number1) {\n          number2 = Math.floor(Math.random() * config.maxNumber) + 1;\n        }\n        \n        const targetNumber = Math.max(number1, number2);\n        const options = generateNumberOptions(targetNumber, config.maxNumber);\n        \n        return {\n          challengeType: 'comparison',\n          targetNumber,\n          number1,\n          number2,\n          options,\n          instruction: `Qual é o maior: ${number1} ou ${number2}?`\n        };\n      }\n      \n      case 'pattern': {\n        // Padrões visuais (pontos, dados, dedos)\n        const targetNumber = Math.floor(Math.random() * Math.min(6, config.maxNumber)) + 1; // máx 6 para padrões de dados\n        const patternTypes = ['dots', 'dice', 'fingers'];\n        const patternType = patternTypes[Math.floor(Math.random() * patternTypes.length)];\n        const options = generateNumberOptions(targetNumber, config.maxNumber);\n        \n        return {\n          challengeType: 'pattern',\n          targetNumber,\n          patternType,\n          options,\n          instruction: `Quantos pontos você vê?`\n        };\n      }\n      \n      default: {\n        // Fallback para o método original melhorado\n        const targetNumber = Math.floor(Math.random() * config.maxNumber) + 1;\n        const options = generateNumberOptions(targetNumber, config.maxNumber);\n        \n        return {\n          challengeType: 'basic',\n          targetNumber,\n          options,\n          instruction: `Encontre o número ${targetNumber}`\n        };\n      }\n    }\n  }, [gameState.difficulty]);\n  \n  // 🎯 FUNÇÃO AUXILIAR PARA GERAR OPÇÕES DE NÚMEROS\n  const generateNumberOptions = useCallback((correctNumber, maxNumber) => {\n    const options = new Set([correctNumber]);\n    let attempts = 0;\n    \n    while (options.size < 4 && attempts < 20) {\n      let wrongOption;\n      \n      // Gerar opções mais inteligentes (próximas ao número correto)\n      if (Math.random() < 0.5) {\n        // Números próximos (+/- 1 ou 2)\n        const offset = (Math.random() < 0.5 ? -1 : 1) * (Math.floor(Math.random() * 2) + 1);\n        wrongOption = correctNumber + offset;\n      } else {\n        // Números aleatórios\n        wrongOption = Math.floor(Math.random() * maxNumber) + 1;\n      }\n      \n      if (wrongOption > 0 && wrongOption <= maxNumber && wrongOption !== correctNumber) {\n        options.add(wrongOption);\n      }\n      attempts++;\n    }\n    \n    // Se ainda não temos 4 opções, completar com números aleatórios\n    while (options.size < 4) {\n      const wrongOption = Math.floor(Math.random() * maxNumber) + 1;\n      if (wrongOption !== correctNumber) {\n        options.add(wrongOption);\n      }\n    }\n    \n    return Array.from(options).sort(() => Math.random() - 0.5);\n  }, []);\n\n  // 🎯 GERADOR DE COMPARAÇÃO DE QUANTIDADE V2 - USANDO CONFIG.JS\n  const generateQuantityComparisonData = useCallback((config) => {\n    // Usar o gerador avançado do arquivo de configuração\n    return ContagemNumerosConfig.generateQuantityComparisonData(config.difficulty || gameState.difficulty || 'easy');\n  }, [gameState.difficulty]);\n\n  // 🎯 INICIAR JOGO - SIMPLES E DIRETO\n  const startGame = useCallback(async (selectedDifficulty) => {\n    // Gerar dados iniciais da primeira atividade\n    const initialData = generateActivityData(ACTIVITY_TYPES.VISUAL_COUNTING.id, selectedDifficulty);\n    \n    // 🎯 Definir número de rodadas baseado na dificuldade (4-7 rodadas)\n    const roundsConfig = {\n      easy: 4,\n      medium: 5,\n      hard: 7\n    };\n    const totalRounds = roundsConfig[selectedDifficulty] || 4;\n\n    setGameState(prev => ({\n      ...prev,\n      status: 'playing',\n      difficulty: selectedDifficulty,\n      totalRounds: totalRounds,\n      round: 1, // Resetar para rodada 1\n      roundStartTime: Date.now(),\n      activityData: {\n        ...prev.activityData,\n        [ACTIVITY_TYPES.VISUAL_COUNTING.id]: initialData\n      }\n    }));\n\n    setShowStartScreen(false);\n    setGameStarted(true);\n\n    // Inicializar sistemas\n    if (startUnifiedSession) {\n      startUnifiedSession(selectedDifficulty);\n    }\n\n    try {\n      if (initMultisensory && typeof initMultisensory === 'function') {\n        await initMultisensory(sessionId || `session_${Date.now()}`, {\n          difficulty: selectedDifficulty,\n          gameMode: 'number_counting_v4',\n          userId: user?.id || 'anonymous'\n        });\n      }\n    } catch (error) {\n      console.warn('⚠️ Erro ao inicializar sessão multissensorial:', error);\n    }\n\n    // TTS de boas-vindas padronizado\n    setTimeout(() => {\n      speakGameInstructions('Contagem de Números', `${initialData.instruction}`);\n    }, ContagemNumerosConfig.gameSettings.initialTtsDelay);\n  }, [generateActivityData, startUnifiedSession, initMultisensory, sessionId, user, speak]);\n\n  // 🎯 PROCESSAR RESPOSTA - LÓGICA SIMPLES\n  const handleAnswer = useCallback((answer) => {\n    const currentActivityData = gameState.activityData[gameState.currentActivity];\n    let isCorrect = false;\n    let correctAnswer = null;\n\n    switch (gameState.currentActivity) {\n      case ACTIVITY_TYPES.VISUAL_COUNTING.id:\n        correctAnswer = currentActivityData.correctCount;\n        isCorrect = answer === correctAnswer;\n        break;\n      case ACTIVITY_TYPES.SIMPLE_ADDITION.id:\n        correctAnswer = currentActivityData.correctAnswer;\n        isCorrect = answer === correctAnswer;\n        break;\n      case ACTIVITY_TYPES.NUMBER_RECOGNITION.id:\n        correctAnswer = currentActivityData.targetNumber;\n        isCorrect = answer === correctAnswer;\n        break;\n      case ACTIVITY_TYPES.QUANTITY_COMPARISON.id:\n        correctAnswer = currentActivityData.correctAnswer;\n        isCorrect = answer === correctAnswer;\n        break;\n      default:\n        isCorrect = false;\n    }\n\n    // Atualizar pontuação e precisão\n    setGameState(prev => {\n      const newScore = isCorrect ? prev.score + ContagemNumerosConfig.gameSettings.basePoints : prev.score;\n      const newRound = prev.round + 1;\n      const totalAttempts = prev.round;\n      const correctAnswers = Math.floor(prev.score / ContagemNumerosConfig.gameSettings.basePoints) + (isCorrect ? 1 : 0);\n      const newAccuracy = totalAttempts > 0 ? Math.round((correctAnswers / totalAttempts) * 100) : 100;\n\n      return {\n        ...prev,\n        score: newScore,\n        round: newRound,\n        accuracy: newAccuracy\n      };\n    });\n\n    // Feedback\n    setFeedback({\n      isCorrect,\n      message: isCorrect ? 'Muito bem! 🎉' : `Não foi dessa vez. A resposta era ${correctAnswer}`,\n      correctAnswer\n    });\n\n    // 🔍 COLETA DE DADOS COM COLLECTORS HUB\n    const collectionData = {\n      activity: gameState.currentActivity,\n      isCorrect,\n      responseTime: Date.now() - gameState.roundStartTime,\n      difficulty: gameState.difficulty,\n      round: gameState.round,\n      answer: answer,\n      correctAnswer: correctAnswer,\n      questionData: currentActivityData,\n      timestamp: Date.now()\n    };\n\n    // Ativar coleta de dados especializada\n    try {\n      collectorsHub.collectData(collectionData);\n    } catch (error) {\n      console.warn('⚠️ Erro na coleta de dados dos collectors:', error);\n    }\n\n    // TTS feedback padronizado\n    if (isCorrect) {\n      speakFeedback(true);\n    } else {\n      speakFeedback(false, `Não foi dessa vez. A resposta correta era ${correctAnswer}`);\n    }\n\n    // Próxima rodada\n    setTimeout(() => {\n      setFeedback(null);\n      generateNewRound();\n    }, 2500);\n  }, [gameState, speak]);\n\n  // 🎯 GERAR NOVA RODADA - SEM MUDANÇA AUTOMÁTICA DE ATIVIDADE\n  const generateNewRound = useCallback(() => {\n    // Manter a atividade atual - usuário controla mudanças\n    const currentActivity = gameState.currentActivity;\n    const newData = generateActivityData(currentActivity, gameState.difficulty);\n\n    setGameState(prev => ({\n      ...prev,\n      round: prev.round + 1,\n      roundStartTime: Date.now(),\n      activityData: {\n        ...prev.activityData,\n        [currentActivity]: newData\n      }\n    }));\n\n    // TTS da nova instrução padronizado\n    setTimeout(() => {\n      speak(newData.instruction, { rate: 0.8 });\n    }, 500);\n  }, [gameState, generateActivityData, speak]);\n\n  // 🎯 TROCAR ATIVIDADE - Padrão PadroesVisuais\n  const switchActivity = useCallback((activityId) => {\n    if (activityId === gameState.currentActivity) return;\n\n    const newData = generateActivityData(activityId, gameState.difficulty);\n\n    setGameState(prev => ({\n      ...prev,\n      currentActivity: activityId,\n      roundStartTime: Date.now(),\n      activityData: {\n        ...prev.activityData,\n        [activityId]: newData\n      }\n    }));\n\n    // TTS da nova atividade padronizado\n    setTimeout(() => {\n      speak(newData.instruction, { rate: 0.8 });\n    }, 500);\n  }, [gameState, generateActivityData, speak]);\n\n  // 🎯 RENDERIZAR ATIVIDADE VISUAL COUNTING - ESTÁVEL\n  const renderVisualCounting = () => {\n    const data = gameState.activityData.visual_counting;\n\n    // Se não há dados, gerar dados iniciais\n    if (!data || !data.objects) {\n      const initialData = generateActivityData(ACTIVITY_TYPES.VISUAL_COUNTING.id, gameState.difficulty);\n      setGameState(prev => ({\n        ...prev,\n        activityData: {\n          ...prev.activityData,\n          [ACTIVITY_TYPES.VISUAL_COUNTING.id]: initialData\n        }\n      }));\n      return <div>Gerando atividade...</div>;\n    }\n\n    return (\n      <div className={styles.questionArea}>\n        <div className={styles.questionHeader}>\n          <h2 className={styles.questionTitle}>{data.instruction}</h2>\n        </div>\n\n        {/* Object Display ESTÁVEL - sem position absolute */}\n        <div className={styles.objectsDisplay}>\n          {data.objects.map((obj, index) => (\n            <div\n              key={obj.id}\n              className={styles.countingObject}\n              style={{ animationDelay: `${index * 0.1}s` }}\n            >\n              {obj.emoji}\n            </div>\n          ))}\n        </div>\n\n        {/* Opções de resposta */}\n        <div className={styles.answerOptions}>\n          {data.options.map((option, index) => (\n            <button\n              key={index}\n              className={styles.answerButton}\n              onClick={() => handleAnswer(option)}\n            >\n              <span className={styles.optionNumber}>{option}</span>\n            </button>\n          ))}\n        </div>\n      </div>\n    );\n  };\n\n  // 🎯 RENDERIZAR SOMA SIMPLES\n  const renderSimpleAddition = () => {\n    const data = gameState.activityData.simple_addition;\n\n    // Se não há dados, gerar dados iniciais\n    if (!data || !data.options) {\n      const initialData = generateActivityData(ACTIVITY_TYPES.SIMPLE_ADDITION.id, gameState.difficulty);\n      setGameState(prev => ({\n        ...prev,\n        activityData: {\n          ...prev.activityData,\n          [ACTIVITY_TYPES.SIMPLE_ADDITION.id]: initialData\n        }\n      }));\n      return <div>Gerando atividade...</div>;\n    }\n\n    return (\n      <div className={styles.questionArea}>\n        <div className={styles.questionHeader}>\n          <h2 className={styles.questionTitle}>{data.instruction}</h2>\n        </div>\n\n        {/* Exibição da soma */}\n        <div className={styles.objectsDisplay}>\n          <div className={styles.additionDisplay}>\n            <span className={styles.additionNumber}>{data.number1}</span>\n            <span className={styles.additionOperator}>+</span>\n            <span className={styles.additionNumber}>{data.number2}</span>\n            <span className={styles.additionOperator}>=</span>\n            <span className={styles.additionResult}>?</span>\n          </div>\n        </div>\n\n        {/* Opções de resposta */}\n        <div className={styles.answerOptions}>\n          {data.options.map((option, index) => (\n            <button\n              key={index}\n              className={styles.answerButton}\n              onClick={() => handleAnswer(option)}\n            >\n              <span className={styles.optionNumber}>{option}</span>\n            </button>\n          ))}\n        </div>\n      </div>\n    );\n  };\n\n  // 🎯 RENDERIZAR RECONHECIMENTO DE NÚMERO\n  // 🎯 RENDERIZAR RECONHECIMENTO DE NÚMERO - VERSÃO MELHORADA V2\n  const renderNumberRecognition = () => {\n    const data = gameState.activityData.number_recognition;\n\n    // Se não há dados, gerar dados iniciais\n    if (!data || !data.options) {\n      const initialData = generateActivityData(ACTIVITY_TYPES.NUMBER_RECOGNITION.id, gameState.difficulty);\n      setGameState(prev => ({\n        ...prev,\n        activityData: {\n          ...prev.activityData,\n          [ACTIVITY_TYPES.NUMBER_RECOGNITION.id]: initialData\n        }\n      }));\n      return <div>Gerando atividade...</div>;\n    }\n\n    const renderChallengeContent = () => {\n      switch (data.challengeType) {\n        case 'visual_count':\n          return (\n            <div className={styles.objectsDisplay}>\n              <div className={styles.countingObjectsGrid}>\n                {data.objects?.map((obj, index) => (\n                  <div\n                    key={obj.id}\n                    className={styles.countingObject}\n                    style={{ animationDelay: `${index * 0.1}s` }}\n                  >\n                    {obj.emoji}\n                  </div>\n                ))}\n              </div>\n            </div>\n          );\n        \n        case 'written_number':\n          return (\n            <div className={styles.objectsDisplay}>\n              <div className={styles.writtenNumberDisplay}>\n                <span className={styles.writtenNumberText}>\"{data.writtenNumber}\"</span>\n              </div>\n            </div>\n          );\n        \n        case 'sequence':\n          return (\n            <div className={styles.objectsDisplay}>\n              <div className={styles.sequenceDisplay}>\n                <span className={styles.sequenceNumber}>{data.beforeNumber}</span>\n                <span className={styles.sequenceArrow}>→</span>\n                <span className={styles.sequencePlaceholder}>?</span>\n              </div>\n            </div>\n          );\n        \n        case 'comparison':\n          return (\n            <div className={styles.objectsDisplay}>\n              <div className={styles.comparisonDisplay}>\n                <span className={styles.comparisonNumber}>{data.number1}</span>\n                <span className={styles.comparisonVs}>VS</span>\n                <span className={styles.comparisonNumber}>{data.number2}</span>\n              </div>\n            </div>\n          );\n        \n        case 'pattern':\n          return (\n            <div className={styles.objectsDisplay}>\n              <div className={styles.patternDisplay}>\n                {renderPattern(data.patternType, data.targetNumber)}\n              </div>\n            </div>\n          );\n        \n        default:\n          return (\n            <div className={styles.objectsDisplay}>\n              <div className={styles.targetNumber}>\n                {data.targetNumber}\n              </div>\n            </div>\n          );\n      }\n    };\n\n    return (\n      <div className={styles.questionArea}>\n        <div className={styles.questionHeader}>\n          <h2 className={styles.questionTitle}>{data.instruction}</h2>\n        </div>\n\n        {renderChallengeContent()}\n\n        {/* Opções de resposta */}\n        <div className={styles.answerOptions}>\n          {data.options.map((option, index) => (\n            <button\n              key={index}\n              className={styles.answerButton}\n              onClick={() => handleAnswer(option)}\n            >\n              <span className={styles.optionNumber}>{option}</span>\n            </button>\n          ))}\n        </div>\n      </div>\n    );\n  };\n\n  // 🎯 FUNÇÃO AUXILIAR PARA RENDERIZAR PADRÕES\n  const renderPattern = useCallback((patternType, number) => {\n    switch (patternType) {\n      case 'dots':\n        return (\n          <div className={styles.dotsPattern}>\n            {Array.from({ length: number }, (_, i) => (\n              <span key={i} className={styles.dot}>●</span>\n            ))}\n          </div>\n        );\n      \n      case 'dice':\n        const dicePatterns = {\n          1: '⚀', 2: '⚁', 3: '⚂', 4: '⚃', 5: '⚄', 6: '⚅'\n        };\n        return (\n          <div className={styles.dicePattern}>\n            <span className={styles.diceEmoji}>{dicePatterns[number] || '⚀'}</span>\n          </div>\n        );\n      \n      case 'fingers':\n        return (\n          <div className={styles.fingersPattern}>\n            {Array.from({ length: number }, (_, i) => (\n              <span key={i} className={styles.finger}>👆</span>\n            ))}\n          </div>\n        );\n      \n      default:\n        return <span>{number}</span>;\n    }\n  }, []);\n\n  // 🎯 RENDERIZAR COMPARAÇÃO DE QUANTIDADE V2 - LAYOUT SIMPLES\n  const renderQuantityComparison = () => {\n    const data = gameState.activityData.quantity_comparison;\n\n    // Se não há dados, gerar dados iniciais\n    if (!data || !data.group1 || !data.group2) {\n      const initialData = generateActivityData(ACTIVITY_TYPES.QUANTITY_COMPARISON.id, gameState.difficulty);\n      setGameState(prev => ({\n        ...prev,\n        activityData: {\n          ...prev.activityData,\n          [ACTIVITY_TYPES.QUANTITY_COMPARISON.id]: initialData\n        }\n      }));\n      return <div>Gerando atividade...</div>;\n    }\n\n    // Manipular resposta baseada no tipo de desafio\n    const handleComparisonAnswer = (answer) => {\n      let isCorrect = false;\n      \n      switch (data.challengeType) {\n        case 'more_than':\n        case 'less_than':\n          isCorrect = data.correctAnswer === answer;\n          break;\n        case 'equal_check':\n          isCorrect = data.correctAnswer === answer;\n          break;\n        case 'count_difference':\n          isCorrect = data.correctAnswer === answer;\n          break;\n        default:\n          isCorrect = data.correctAnswer === answer;\n      }\n      \n      handleAnswer(isCorrect ? data.correctAnswer : 'wrong');\n    };\n\n    return (\n      <div className={styles.questionArea}>\n        <div className={styles.questionHeader}>\n          <h2 className={styles.questionTitle}>{data.instruction}</h2>\n        </div>\n\n        {/* Exibição simples dos grupos */}\n        <div className={styles.objectsDisplay}>\n          <div className={styles.comparisonGroups}>\n            <button\n              className={styles.comparisonGroup}\n              onClick={() => handleComparisonAnswer('left')}\n            >\n              <div className={styles.groupLabel}>Grupo A</div>\n              <div className={styles.groupObjects}>\n                {data.group1.objects.map((obj, index) => (\n                  <span \n                    key={`group1-${obj.id}-${index}`} \n                    className={styles.groupObject}\n                  >\n                    {obj.emoji}\n                  </span>\n                ))}\n              </div>\n            </button>\n\n            <button\n              className={styles.comparisonGroup}\n              onClick={() => handleComparisonAnswer('right')}\n            >\n              <div className={styles.groupLabel}>Grupo B</div>\n              <div className={styles.groupObjects}>\n                {data.group2.objects.map((obj, index) => (\n                  <span \n                    key={`group2-${obj.id}-${index}`} \n                    className={styles.groupObject}\n                  >\n                    {obj.emoji}\n                  </span>\n                ))}\n              </div>\n            </button>\n          </div>\n\n          {/* Botões especiais para desafios de igualdade */}\n          {data.challengeType === 'equal_check' && (\n            <div className={styles.equalityButtons}>\n              <button\n                className={styles.answerButton}\n                onClick={() => handleComparisonAnswer('equal')}\n              >\n                ✅ SIM, são iguais\n              </button>\n              <button\n                className={styles.answerButton}\n                onClick={() => handleComparisonAnswer('different')}\n              >\n                ❌ NÃO, são diferentes\n              </button>\n            </div>\n          )}\n\n          {/* Entrada numérica para desafios de diferença */}\n          {data.challengeType === 'count_difference' && (\n            <div className={styles.answerGrid}>\n              {[0, 1, 2, 3, 4, 5, 6].map(num => (\n                <button\n                  key={num}\n                  className={styles.answerButton}\n                  onClick={() => handleComparisonAnswer(num.toString())}\n                >\n                  {num}\n                </button>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  };\n\n  // 🎯 RENDERIZAR INTERFACE PRINCIPAL - Padrão dos jogos de sucesso\n  const renderCurrentActivity = () => {\n    switch (gameState.currentActivity) {\n      case ACTIVITY_TYPES.VISUAL_COUNTING.id:\n        return renderVisualCounting();\n      case ACTIVITY_TYPES.SIMPLE_ADDITION.id:\n        return renderSimpleAddition();\n      case ACTIVITY_TYPES.NUMBER_RECOGNITION.id:\n        return renderNumberRecognition();\n      case ACTIVITY_TYPES.QUANTITY_COMPARISON.id:\n        return renderQuantityComparison();\n      default:\n        return renderVisualCounting();\n    }\n  };\n\n  // 🎯 TELA DE INÍCIO\n  if (showStartScreen) {\n    return (\n      <GameStartScreen\n        gameTitle=\"Contagem de Números\"\n        gameDescription=\"Aprenda matemática de forma divertida e interativa\"\n        gameIcon=\"🔢\"\n        onStart={startGame}\n        onBack={onBack}\n        difficulties={ContagemNumerosConfig.difficulties.map(diff => ({\n          id: diff.id,\n          name: diff.name,\n          description: diff.description,\n          icon: diff.id === 'easy' ? '😊' : diff.id === 'medium' ? '🎯' : '🚀'\n        }))}\n      />\n    );\n  }\n\n  // 🎯 INTERFACE PRINCIPAL - REPLICANDO LAYOUT DO LETTERRECOGNITION\n  return (\n    <div className={styles.contagemNumerosGame}>\n      <div className={styles.gameContent}>\n        {/* Header do jogo - PADRÃO LETTERRECOGNITION EXATO */}\n        <div className={styles.gameHeader}>\n          <h1 className={styles.gameTitle}>\n            🔢 Contagem de Números V4\n            <div style={{ fontSize: '0.7rem', opacity: 0.8, marginTop: '0.25rem' }}>\n              {Object.values(ACTIVITY_TYPES).find(type => type.id === gameState.currentActivity)?.name || 'Atividade'}\n            </div>\n          </h1>\n          <StandardTTSButton\n            ttsActive={ttsActive}\n            toggleTTS={toggleTTS}\n            size=\"normal\"\n            position=\"header\"\n          />\n        </div>\n\n        {/* Header com estatísticas - padrão MemoryGame */}\n        <div className={styles.gameStats}>\n          <div className={styles.statCard}>\n            <div className={styles.statValue}>{gameState.score}</div>\n            <div className={styles.statLabel}>Pontos</div>\n          </div>\n          <div className={styles.statCard}>\n            <div className={styles.statValue}>{gameState.round}</div>\n            <div className={styles.statLabel}>Rodada</div>\n          </div>\n          <div className={styles.statCard}>\n            <div className={styles.statValue}>{gameState.accuracy || 100}%</div>\n            <div className={styles.statLabel}>Precisão</div>\n          </div>\n        </div>\n\n        {/* Menu de atividades - PADRÃO LETTERRECOGNITION */}\n        <div className={styles.activityMenu}>\n          {Object.values(ACTIVITY_TYPES).map((activity) => (\n            <button\n              key={activity.id}\n              className={`${styles.activityButton} ${\n                gameState.currentActivity === activity.id ? styles.active : ''\n              }`}\n              onClick={() => switchActivity(activity.id)}\n              title={activity.description}\n            >\n              <span>{activity.icon}</span>\n              <span>{activity.name}</span>\n            </button>\n          ))}\n        </div>\n\n        {/* Renderização da atividade atual - PADRÃO LETTERRECOGNITION EXATO */}\n        <div className={styles.gameArea}>\n          {renderCurrentActivity()}\n        </div>\n\n        {/* Controles do jogo - PADRÃO TTS PADRONIZADO */}\n        <div className={styles.gameControls}>\n          <button className={styles.controlButton} onClick={() => speakGameInstructions('Contagem de Números', 'Aprenda matemática de forma divertida contando objetos e resolvendo operações.')}>\n            🔊 Explicar\n          </button>\n          <button className={styles.controlButton} onClick={() => setShowStartScreen(true)}>\n            🔄 Reiniciar\n          </button>\n          <button className={styles.controlButton} onClick={() => { speak(TTS_MESSAGES.NAVIGATION.LEAVING_GAME); setTimeout(onBack, 1000); }}>\n            ⬅️ Voltar\n          </button>\n        </div>\n      </div>\n\n      {/* Feedback - se houver */}\n      {feedback && (\n        <div className={`${styles.feedbackOverlay} ${feedback.isCorrect ? styles.success : styles.error}`}>\n          <div className={styles.feedbackContent}>\n            <div className={styles.feedbackMessage}>{feedback.message}</div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ContagemNumerosGame;\n"], "names": ["error", "timeScore", "targetNumber", "ACTIVITY_TYPES", "VISUAL_COUNTING", "id", "name", "description", "icon", "SIMPLE_ADDITION", "NUMBER_RECOGNITION", "QUANTITY_COMPARISON", "ContagemNumerosGame", "onBack", "user", "sessionId", "useContext", "SystemContext", "ttsActive", "toggleTTS", "speak", "speakGameInstructions", "speakFeedback", "useStandardTTS", "gameInstructions", "gameState", "setGameState", "useState", "status", "score", "round", "totalRounds", "difficulty", "accuracy", "roundStartTime", "currentActivity", "userControlledActivities", "activityData", "visual_counting", "objects", "correctCount", "options", "instruction", "simple_addition", "number1", "number2", "<PERSON><PERSON><PERSON><PERSON>", "number_recognition", "quantity_comparison", "group1", "group2", "showStartScreen", "setShowStartScreen", "gameStarted", "setGameStarted", "feedback", "setFeedback", "collectorsHub", "NumberCountingCollectorsHub", "startUnifiedSession", "useUnifiedGameLogic", "initMultisensory", "useMultisensoryIntegration", "learningStyle", "profile", "useEffect", "setTimeout", "generateActivityData", "useCallback", "activityId", "config", "ContagemNumerosConfig", "gameSettings", "simpleDifficultyConfigs", "generateVisualCountingData", "generateSimpleAdditionData", "generateNumberRecognitionData", "generateQuantityComparisonData", "Math", "floor", "random", "range", "categories", "randomCategory", "length", "randomObject", "Array", "from", "_", "index", "emoji", "Set", "size", "wrongOption", "maxNumber", "add", "sort", "toLowerCase", "challengeTypes", "availableTypes", "challengeType", "i", "generateNumberOptions", "numberWords", "writtenNumber", "toString", "beforeNumber", "max", "min", "patternTypes", "patternType", "correctNumber", "attempts", "offset", "startGame", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initialData", "roundsConfig", "easy", "medium", "hard", "prev", "Date", "now", "gameMode", "userId", "warn", "initialTtsDelay", "handleAnswer", "answer", "currentActivityData", "isCorrect", "newScore", "basePoints", "newRound", "totalAttempts", "correctAnswers", "newAccuracy", "message", "collectionData", "activity", "responseTime", "questionData", "timestamp", "collectData", "generateNewRound", "newData", "rate", "switchActivity", "renderVisualCounting", "data", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "styles", "questionArea", "<PERSON><PERSON><PERSON><PERSON>", "questionTitle", "objectsDisplay", "map", "obj", "countingObject", "animationDelay", "answerOptions", "option", "answerButton", "optionNumber", "renderSimpleAddition", "additionDisplay", "additionNumber", "additionOperator", "additionResult", "renderNumberRecognition", "renderChallengeContent", "countingObjectsGrid", "writtenNumberDisplay", "writtenNumberText", "sequenceDisplay", "sequenceNumber", "sequenceArrow", "sequencePlaceholder", "comparisonDisplay", "comparisonNumber", "comparisonVs", "patternDisplay", "renderPattern", "number", "dotsPattern", "dot", "dicePatterns", "dicePattern", "dice<PERSON><PERSON>ji", "fingersPattern", "finger", "renderQuantityComparison", "handleComparisonAnswer", "comparisonGroups", "comparisonGroup", "groupLabel", "groupObjects", "groupObject", "equalityButtons", "answerGrid", "num", "renderCurrentActivity", "difficulties", "diff", "contagemNumerosGame", "gameContent", "gameHeader", "gameTitle", "fontSize", "opacity", "marginTop", "Object", "values", "find", "type", "gameStats", "statCard", "statValue", "statLabel", "activityMenu", "activityButton", "active", "gameArea", "gameControls", "controlButton", "TTS_MESSAGES", "NAVIGATION", "LEAVING_GAME", "feedbackOverlay", "success", "feedbackContent", "feedbackMessage"], "mappings": ";;;;;;AAMO,MAAM,4BAA4B;AAAA,EACvC,cAAc;AACZ,SAAK,sBAAsB;AAAA,MACzB,WAAW;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAEA,SAAK,kBAAkB;AAAA,MACrB,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,aAAa;AAAA,MACb,UAAU;AAAA,IACZ;AAEA,SAAK,mBAAmB;AAAA,MACtB,MAAM,EAAE,KAAK,GAAG,KAAK,GAAG,cAAc,IAAK;AAAA,MAC3C,QAAQ,EAAE,KAAK,GAAG,KAAK,IAAI,cAAc,IAAK;AAAA,MAC9C,MAAM,EAAE,KAAK,GAAG,KAAK,IAAI,cAAc,IAAK;AAAA,IAC9C;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQF,QAAQ,MAAM;AACL,WAAA,KAAK,QAAQ,IAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ1B,MAAM,QAAQ,MAAM;AAClB,QAAI,CAAC,QAAQ,CAAC,KAAK,gBAAgB;AACzB,cAAA,KAAK,0DAA0D,IAAI;AACpE,aAAA;AAAA,QACL,kBAAkB;AAAA,QAClB,kBAAkB;AAAA,QAClB,YAAY;AAAA,QACZ,wBAAwB;AAAA,QACxB,oBAAoB;AAAA,QACpB,kBAAkB;AAAA,QAClB,iBAAiB,CAAA;AAAA,MACnB;AAAA,IAAA;AAGE,QAAA;AACI,YAAA,mBAAmB,KAAK,uBAAuB,IAAI;AACnD,YAAA,mBAAmB,KAAK,uBAAuB,IAAI;AACnD,YAAA,aAAa,KAAK,iBAAiB,IAAI;AACvC,YAAA,yBAAyB,KAAK,6BAA6B,IAAI;AAC/D,YAAA,qBAAqB,KAAK,yBAAyB,IAAI;AAGvD,YAAA,mBACJ,mBAAmB,OACnB,mBAAmB,MACnB,aAAa,OACb,yBAAyB,OACzB,qBAAqB;AAIjB,YAAA,kBAAkB,KAAK,wBAAwB;AAAA,QACnD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA,CACD;AAEM,aAAA;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,aACOA,QAAO;AACN,cAAA,MAAM,yCAAyCA,MAAK;AACrD,aAAA;AAAA,QACL,kBAAkB;AAAA,QAClB,kBAAkB;AAAA,QAClB,YAAY;AAAA,QACZ,wBAAwB;AAAA,QACxB,oBAAoB;AAAA,QACpB,kBAAkB;AAAA,QAClB,iBAAiB,CAAC;AAAA,QAClB,OAAOA,OAAM;AAAA,MACf;AAAA,IAAA;AAAA,EACF;AAAA,EAGF,uBAAuB,MAAM;AAC3B,UAAM,WAAW,KAAK,SAAS,OAAO,CAAK,MAAA,EAAE,SAAS,UAAU;AAC5D,QAAA,SAAS,WAAW,EAAU,QAAA;AAElC,UAAM,kBAAkB,SAAS,OAAO,CAAA,MAAK,EAAE,SAAS;AAClD,UAAA,WAAW,gBAAgB,SAAS,SAAS;AAG7C,UAAA,kBAAkB,KAAK,6BAA6B,QAAQ;AAElE,WAAO,KAAK,IAAI,GAAK,YAAY,IAAI,gBAAgB,cAAc,IAAI;AAAA,EAAA;AAAA,EAGzE,iBAAiB,MAAM;AAEf,UAAA,qBAAqB,KAAK,SAAS;AAAA,MAAO,CAC9C,MAAA,EAAE,iBAAiB,KAAK,EAAE,eAAe;AAAA,IAC3C;AAEI,QAAA,mBAAmB,WAAW,EAAU,QAAA;AAEtC,UAAA,WAAW,mBAAmB,OAAO,CAAA,MAAK,EAAE,SAAS,EAAE,SAAS,mBAAmB;AACnF,UAAA,kBAAkB,mBAAmB,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,cAAc,CAAC,IAAI,mBAAmB;AAG5G,UAAM,cAAc,KAAK,IAAI,GAAG,KAAK,kBAAkB,OAAQ,GAAI;AAEnE,WAAO,KAAK,IAAI,GAAK,YAAY,MAAM,cAAc,IAAI;AAAA,EAAA;AAAA,EAG3D,kBAAkB,MAAM;AACtB,UAAM,WAAW,KAAK;AAClB,QAAA,SAAS,WAAW,EAAU,QAAA;AAG5B,UAAA,mBAAmB,SAAS,IAAI,CAAK,MAAA,KAAK,IAAI,EAAE,aAAa,EAAE,aAAa,CAAC;AAC7E,UAAA,WAAW,iBAAiB,OAAO,CAAC,KAAK,MAAM,MAAM,GAAG,CAAC,IAAI,iBAAiB;AAG9E,UAAA,iBAAiB,KAAK,qBAAqB,QAAQ;AAGnD,UAAA,0BAA0B,KAAK,8BAA8B,QAAQ;AAE3E,UAAM,YAAY,KAAK,IAAI,GAAG,IAAI,WAAW,EAAE;AACzC,UAAA,cAAc,eAAe,UAAU,MAAM;AAEnD,WAAO,KAAK,IAAI,IAAM,YAAY,2BAA2B,IAAI,WAAW;AAAA,EAAA;AAAA,EAG9E,kBAAkB,MAAM;AAEhB,UAAA,oBAAoB,KAAK,qBAAqB,CAAC;AAEjD,QAAA,kBAAkB,WAAW,EAAU,QAAA;AAE3C,UAAM,wBAAwB,kBAAkB;AAAA,MAAO,CAAA,QACrD,IAAI,gBAAgB,IAAI;AAAA,IAAA,EACxB;AAEI,UAAA,sBAAsB,wBAAwB,kBAAkB;AAGhE,UAAA,cAAc,KAAK,8BAA8B,iBAAiB;AAExE,WAAO,KAAK,IAAI,GAAK,uBAAuB,MAAM,cAAc,IAAI;AAAA,EAAA;AAAA,EAGtE,6BAA6B,MAAM;AAE3B,UAAA,mBAAmB,KAAK,oBAAoB,CAAC;AAE/C,QAAA,iBAAiB,WAAW,EAAU,QAAA;AAE1C,UAAM,wBAAwB,iBAAiB;AAAA,MAAO,cACpD,SAAS,mBAAmB,SAAS,iBACrC,SAAS,oBACT,SAAS;AAAA,IAAA,EACT;AAEF,WAAO,KAAK,IAAI,GAAK,wBAAwB,iBAAiB,MAAM;AAAA,EAAA;AAAA,EAGtE,sBAAsB,MAAM;AAC1B,UAAM,WAAW,KAAK;AAClB,QAAA,SAAS,WAAW,EAAU,QAAA;AAE5B,UAAA,mBAAmB,KAAK,kBAAkB,QAAQ;AACxD,QAAI,kBAAkB;AACtB,QAAI,aAAa;AAEjB,WAAO,KAAK,gBAAgB,EAAE,QAAQ,CAAc,eAAA;AAC5C,YAAA,QAAQ,iBAAiB,UAAU;AACzC,YAAM,eAAe,KAAK,iBAAiB,UAAU,GAAG,gBAAgB;AAElE,YAAA,UAAU,MAAM,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,cAAc,CAAC,IAAI,MAAM;AAC1E,YAAM,aAAa,KAAK,IAAI,GAAG,KAAK,UAAU,gBAAgB,YAAY;AAEvD,yBAAA;AACnB;AAAA,IAAA,CACD;AAEM,WAAA,aAAa,IAAI,kBAAkB,aAAa;AAAA,EAAA;AAAA,EAGzD,qBAAqB,MAAM;AACzB,UAAM,WAAW,KAAK;AACtB,UAAM,SAAS,SAAS,OAAO,CAAK,MAAA,CAAC,EAAE,SAAS;AAE5C,QAAA,OAAO,WAAW,GAAG;AAChB,aAAA;AAAA,QACL,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,QACrB,sBAAsB;AAAA,QACtB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,iBAAiB;AAAA,MACnB;AAAA,IAAA;AAGF,UAAM,WAAW;AAAA,MACf,gBAAgB,OAAO,OAAO,CAAA,MAAK,EAAE,cAAc,UAAU,EAAE,SAAS,OAAO;AAAA,MAC/E,qBAAqB,OAAO,OAAO,CAAK,MAAA,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,OAAO;AAAA,MACxF,sBAAsB,OAAO,OAAO,CAAK,MAAA,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,OAAO;AAAA,MACzF,YAAY,OAAO,OAAO,CAAA,MAAK,EAAE,cAAc,MAAM,EAAE,SAAS,OAAO;AAAA,MACvE,mBAAmB,OAAO,OAAO,CAAA,MAAK,EAAE,cAAc,aAAa,EAAE,SAAS,OAAO;AAAA,IACvF;AAGA,UAAM,aAAa,KAAK,IAAI,GAAG,OAAO,OAAO,QAAQ,CAAC;AACtD,aAAS,kBAAkB,aAAa,MAAM,SAAS,aAAa,MAAM,WAAW;AAE9E,WAAA;AAAA,EAAA;AAAA,EAGT,oBAAoB,MAAM;AACxB,UAAM,WAAW,KAAK;AAClB,QAAA,SAAS,WAAW,EAAU,QAAA;AAG5B,UAAA,sBAAsB,KAAK,0BAA0B,QAAQ;AAG7D,UAAA,yBAAyB,KAAK,8BAA8B,QAAQ;AAGpE,UAAA,mBAAmB,KAAK,8BAA8B,QAAQ;AAE9D,UAAA,sBACH,IAAI,oBAAoB,WAAW,MACpC,uBAAuB,aAAa,MACpC,mBAAmB;AAGrB,WAAO,KAAK,IAAI,GAAK,KAAK,IAAI,GAAG,kBAAkB,CAAC;AAAA,EAAA;AAAA,EAGtD,uBAAuB,MAAM;AAC3B,UAAM,WAAW,KAAK;AAClB,QAAA,SAAS,WAAW,EAAU,QAAA;AAG5B,UAAA,gBAAgB,SAAS,OAAO,CAAA,MAAK,EAAE,SAAS,EAAE,SAAS,SAAS;AACpE,UAAA,kBAAkB,SAAS,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,cAAc,CAAC,IAAI,SAAS;AAGxF,UAAM,aAAa,KAAK,IAAI,GAAG,KAAK,kBAAkB,OAAQ,GAAI;AAGlE,UAAM,qBAAqB,SAAS;AAAA,MAAO,OACzC,EAAE,aAAa,EAAE,eAAe,QAAQ,EAAE,iBAAiB;AAAA,IAAA,EAC3D;AAEI,UAAA,oBAAqB,qBAAqB,SAAS,SAAU;AAEnE,WAAO,KAAK,IAAI,GAAM,gBAAgB,MAAM,aAAa,MAAO,iBAAiB;AAAA,EAAA;AAAA,EAGnF,0BAA0B,MAAM;AAC9B,UAAM,WAAW,KAAK;AAClB,QAAA,SAAS,SAAS,EAAU,QAAA;AAG1B,UAAA,kBAAkB,KAAK,uBAAuB,QAAQ;AAGtD,UAAA,uBAAuB,KAAK,4BAA4B,QAAQ;AAGhE,UAAA,gBAAgB,KAAK,qBAAqB,QAAQ;AAEjD,WAAA,KAAK,IAAI,GACd,kBAAkB,MAClB,uBAAuB,MACvB,gBAAgB,GACjB;AAAA,EAAA;AAAA;AAAA,EAIH,6BAA6B,UAAU;AACrC,UAAM,SAAS;AAAA,MACb,OAAO,SAAS,OAAO,CAAK,MAAA,EAAE,iBAAiB,CAAC;AAAA,MAChD,QAAQ,SAAS,OAAO,CAAA,MAAK,EAAE,gBAAgB,KAAK,EAAE,iBAAiB,CAAC;AAAA,MACxE,OAAO,SAAS,OAAO,CAAK,MAAA,EAAE,gBAAgB,CAAC;AAAA,IACjD;AAEA,UAAM,aAAa,CAAC;AACpB,WAAO,KAAK,MAAM,EAAE,QAAQ,CAAS,UAAA;AACnC,UAAI,OAAO,KAAK,EAAE,SAAS,GAAG;AAC5B,mBAAW,KAAK,IAAI,OAAO,KAAK,EAAE,OAAO,CAAK,MAAA,EAAE,SAAS,EAAE,SAAS,OAAO,KAAK,EAAE;AAAA,MAAA;AAAA,IACpF,CACD;AAED,UAAM,mBAAmB,KAAK,qBAAqB,OAAO,OAAO,UAAU,CAAC;AACrE,WAAA,EAAE,YAAY,aAAa,iBAAiB;AAAA,EAAA;AAAA,EAGrD,qBAAqB,UAAU;AAC7B,UAAM,SAAS,SAAS,OAAO,CAAK,MAAA,CAAC,EAAE,SAAS;AAC5C,QAAA,OAAO,WAAW,EAAU,QAAA,EAAE,SAAS,OAAO,MAAM,QAAQ,WAAW,EAAE;AAEvE,UAAA,aAAa,OAAO,OAAO,CAAA,MAAK,EAAE,aAAa,EAAE,aAAa,EAAE;AAChE,UAAA,cAAc,OAAO,OAAO,CAAA,MAAK,EAAE,aAAa,EAAE,aAAa,EAAE;AAEjE,UAAA,cAAc,aAAa,OAAO;AAClC,UAAA,eAAe,cAAc,OAAO;AAE1C,QAAI,cAAc,KAAK;AACrB,aAAO,EAAE,SAAS,MAAM,MAAM,aAAa,WAAW,YAAY;AAAA,IAAA,WACzD,eAAe,KAAK;AAC7B,aAAO,EAAE,SAAS,MAAM,MAAM,cAAc,WAAW,aAAa;AAAA,IAAA;AAGtE,WAAO,EAAE,SAAS,OAAO,MAAM,QAAQ,WAAW,EAAE;AAAA,EAAA;AAAA,EAGtD,8BAA8B,UAAU;AAEhC,UAAA,mBAAmB,SAAS,OAAO,CAAK,MAAA;AAC5C,YAAM,gBAAgB,SAAS;AAAA,QAAO,CAAA,MACpC,KAAK,IAAI,EAAE,gBAAgB,EAAE,aAAa,KAAK,KAAK,MAAM;AAAA,MAC5D;AACA,aAAO,cAAc,SAAS;AAAA,IAAA,CAC/B;AAEG,QAAA,iBAAiB,WAAW,EAAU,QAAA;AAE1C,UAAM,wBAAwB,iBAAiB,OAAO,CAAK,MAAA,EAAE,SAAS,EAAE;AACxE,WAAO,wBAAwB,iBAAiB;AAAA,EAAA;AAAA,EAGlD,kBAAkB,UAAU;AAC1B,WAAO,SAAS,OAAO,CAAC,QAAQ,YAAY;AAC1C,YAAM,aAAa,QAAQ,cAAc,KAAK,gBAAgB,QAAQ,aAAa;AACnF,UAAI,CAAC,OAAO,UAAU,EAAU,QAAA,UAAU,IAAI,CAAC;AACxC,aAAA,UAAU,EAAE,KAAK,OAAO;AACxB,aAAA;AAAA,IACT,GAAG,EAAE;AAAA,EAAA;AAAA,EAGP,gBAAgB,QAAQ;AAClB,QAAA,UAAU,EAAU,QAAA;AACpB,QAAA,UAAU,GAAW,QAAA;AAClB,WAAA;AAAA,EAAA;AAAA,EAGT,qBAAqB,QAAQ;AACvB,QAAA,OAAO,SAAS,EAAU,QAAA;AACxB,UAAA,OAAO,OAAO,OAAO,CAAC,KAAK,MAAM,MAAM,GAAG,CAAC,IAAI,OAAO;AAC5D,UAAM,WAAW,OAAO,OAAO,CAAC,KAAK,MAAM,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,OAAO;AACpF,WAAO,KAAK,IAAI,GAAG,IAAI,QAAQ;AAAA,EAAA;AAAA,EAGjC,0BAA0B,UAAU;AAClC,QAAI,SAAS,SAAS,EAAU,QAAA,EAAE,SAAS,EAAE;AAEvC,UAAA,YAAY,SAAS,MAAM,GAAG,KAAK,MAAM,SAAS,SAAS,CAAC,CAAC;AAC7D,UAAA,aAAa,SAAS,MAAM,KAAK,MAAM,SAAS,SAAS,CAAC,CAAC;AAE3D,UAAA,gBAAgB,UAAU,OAAO,CAAA,MAAK,EAAE,SAAS,EAAE,SAAS,UAAU;AACtE,UAAA,iBAAiB,WAAW,OAAO,CAAA,MAAK,EAAE,SAAS,EAAE,SAAS,WAAW;AAE/E,UAAM,UAAU,KAAK,IAAI,GAAG,gBAAgB,cAAc;AACnD,WAAA,EAAE,SAAS,eAAe,eAAe;AAAA,EAAA;AAAA,EAGlD,oBAAoB;AACX,WAAA;AAAA,MACL,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,qBAAqB;AAAA,MACrB,0BAA0B;AAAA,MAC1B,wBAAwB;AAAA,MACxB,iBAAiB;AAAA,MACjB,eAAe;AAAA,QACb,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,QACrB,sBAAsB;AAAA,QACtB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,iBAAiB;AAAA,MACnB;AAAA,MACA,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,qBAAqB;AAAA,IACvB;AAAA,EAAA;AAAA,EAGF,8BAA8B,WAAW;AACnC,QAAA,UAAU,SAAS,EAAU,QAAA;AAEjC,UAAM,cAAc,UAAU;AAAA,MAAO,CAAA,QACnC,IAAI,gBAAgB,IAAI;AAAA,IAAA,EACxB;AAEF,WAAO,cAAc,UAAU;AAAA,EAAA;AAAA,EAGjC,8BAA8B,UAAU;AAChC,UAAA,mBAAmB,KAAK,kBAAkB,QAAQ;AACxD,QAAI,aAAa;AACjB,QAAI,aAAa;AAEjB,WAAO,KAAK,gBAAgB,EAAE,QAAQ,CAAc,eAAA;AAC5C,YAAA,QAAQ,iBAAiB,UAAU;AACzC,YAAM,qBAAqB,KAAK,iBAAiB,UAAU,GAAG,gBAAgB;AACxE,YAAA,mBAAmB,MAAM,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,cAAc,CAAC,IAAI,MAAM;AAE7E,YAAA,kBAAkB,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,mBAAmB,kBAAkB,IAAI,kBAAkB;AAC9F,oBAAA;AACd;AAAA,IAAA,CACD;AAED,WAAO,EAAE,YAAY,aAAa,IAAI,aAAa,aAAa,IAAI;AAAA,EAAA;AAAA,EAGtE,8BAA8B,UAAU;AAClC,QAAA,SAAS,SAAS,EAAU,QAAA;AAEhC,UAAM,aAAa,CAAC;AACpB,UAAM,aAAa;AAEnB,aAAS,IAAI,GAAG,KAAK,SAAS,SAAS,YAAY,KAAK;AACtD,YAAM,SAAS,SAAS,MAAM,GAAG,IAAI,UAAU;AACzC,YAAA,iBAAiB,OAAO,OAAO,CAAA,MAAK,EAAE,SAAS,EAAE,SAAS,OAAO;AACvE,iBAAW,KAAK,cAAc;AAAA,IAAA;AAGzB,WAAA,KAAK,qBAAqB,UAAU;AAAA,EAAA;AAAA,EAG7C,uBAAuB,UAAU;AAC3B,QAAA,SAAS,SAAS,EAAU,QAAA;AAEhC,UAAM,WAAW;AACjB,UAAM,cAAc,KAAK,MAAM,SAAS,SAAS,QAAQ;AACzD,UAAM,oBAAoB,CAAC;AAE3B,aAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,YAAM,QAAQ,IAAI;AAClB,YAAM,MAAM,MAAM,WAAW,IAAI,SAAS,UAAU,IAAI,KAAK;AAC7D,YAAM,UAAU,SAAS,MAAM,OAAO,GAAG;AAErC,UAAA,QAAQ,SAAS,GAAG;AAChB,cAAA,WAAW,QAAQ,OAAO,CAAA,MAAK,EAAE,SAAS,EAAE,SAAS,QAAQ;AACnE,0BAAkB,KAAK,QAAQ;AAAA,MAAA;AAAA,IACjC;AAII,UAAA,gBAAgB,kBAAkB,CAAC,KAAK;AAC9C,UAAM,eAAe,kBAAkB,kBAAkB,SAAS,CAAC,KAAK;AAEjE,WAAA,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,OAAO,eAAe,cAAc,CAAC;AAAA,EAAA;AAAA,EAGtE,4BAA4B,UAAU;AAC9B,UAAA,mBAAmB,KAAK,kBAAkB,QAAQ;AACxD,UAAM,mBAAmB,OAAO,KAAK,gBAAgB,EAAE;AAEnD,QAAA,mBAAmB,EAAU,QAAA;AAEjC,QAAI,kBAAkB;AACtB,WAAO,KAAK,gBAAgB,EAAE,QAAQ,CAAc,eAAA;AAC5C,YAAA,QAAQ,iBAAiB,UAAU;AACnC,YAAA,WAAW,MAAM,OAAO,CAAA,MAAK,EAAE,SAAS,EAAE,SAAS,MAAM;AAG/D,YAAM,mBAAmB,eAAe,SAAS,MAAM,eAAe,WAAW,OAAO;AACxF,YAAM,qBAAqB,IAAI,KAAK,IAAI,WAAW,gBAAgB;AAEhD,yBAAA;AAAA,IAAA,CACpB;AAED,WAAO,kBAAkB;AAAA,EAAA;AAAA,EAG3B,qBAAqB,UAAU;AAC7B,UAAM,SAAS,SAAS,IAAI,CAAC,SAAS,WAAW,EAAE,GAAG,SAAS,QAAQ,EAChD,OAAO,CAAK,MAAA,CAAC,EAAE,SAAS;AAE3C,QAAA,OAAO,WAAW,EAAU,QAAA;AAEhC,QAAI,gBAAgB;AACpB,WAAO,QAAQ,CAASA,WAAA;AAEhB,YAAA,eAAe,SAAS,MAAMA,OAAM,QAAQ,GAAGA,OAAM,QAAQ,CAAC;AACpE,YAAM,cAAc,aAAa,OAAO,CAAK,MAAA,EAAE,SAAS,EAAE;AAE1D,UAAI,eAAe,KAAK,IAAI,GAAG,aAAa,MAAM,GAAG;AACnD;AAAA,MAAA;AAAA,IACF,CACD;AAED,WAAO,gBAAgB,OAAO;AAAA,EAAA;AAElC;ACvgBO,MAAM,wBAAwB;AAAA,EACnC,cAAc;AACZ,SAAK,mBAAmB;AAAA,MACtB,WAAW;AAAA,MACX,WAAW;AAAA,MACX,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAEA,SAAK,kBAAkB;AAAA,MACrB,WAAW;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAEA,SAAK,qBAAqB;AAAA,MACxB,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,WAAW;AAAA,MACX,OAAO;AAAA,IACT;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQF,QAAQ,MAAM;AACL,WAAA,KAAK,QAAQ,IAAI;AAAA,EAAA;AAAA,EAG1B,MAAM,QAAQ,MAAM;AAClB,QAAI,CAAC,QAAQ,CAAC,KAAK,UAAU;AACnB,cAAA,KAAK,sDAAsD,IAAI;AACvE,aAAO,KAAK,kBAAkB;AAAA,IAAA;AAGzB,WAAA;AAAA,MACL,oBAAoB,KAAK,yBAAyB,IAAI;AAAA,MACtD,oBAAoB,KAAK,yBAAyB,IAAI;AAAA,MACtD,oBAAoB,KAAK,yBAAyB,IAAI;AAAA,MACtD,gBAAgB,KAAK,qBAAqB,IAAI;AAAA,MAC9C,uBAAuB,KAAK,4BAA4B,IAAI;AAAA,MAC5D,oBAAoB,KAAK,yBAAyB,IAAI;AAAA,MACtD,sBAAsB,KAAK,2BAA2B,IAAI;AAAA,MAC1D,oBAAoB,KAAK,yBAAyB,IAAI;AAAA,MACtD,sBAAsB,KAAK,2BAA2B,IAAI;AAAA,MAC1D,uBAAuB,KAAK,4BAA4B,IAAI;AAAA,IAC9D;AAAA,EAAA;AAAA,EAGF,yBAAyB,MAAM;AAC7B,UAAM,WAAW,KAAK;AAClB,QAAA,SAAS,SAAS,EAAU,QAAA;AAGhC,UAAM,eAAe,KAAK,uBAAuB,UAAU,CAAC;AAC5D,UAAM,oBAAoB,aAAa;AAAA,MAAI,CAAA,YACzC,QAAQ,OAAO,CAAA,MAAK,EAAE,SAAS,EAAE,SAAS,QAAQ;AAAA,IACpD;AAGM,UAAA,eAAe,kBAAkB,OAAO,CAAC,KAAK,QAAQ,MAAM,KAAK,CAAC,IAAI,kBAAkB;AAC9F,UAAM,WAAW,kBAAkB,OAAO,CAAC,KAAK,QAAQ,MAAM,KAAK,IAAI,MAAM,cAAc,CAAC,GAAG,CAAC,IAAI,kBAAkB;AAGtH,UAAM,YAAY,KAAK,IAAI,GAAG,IAAI,WAAW,CAAC;AACxC,UAAA,iBAAkB,eAAe,MAAM,YAAY;AAElD,WAAA,KAAK,IAAI,GAAK,cAAc;AAAA,EAAA;AAAA,EAGrC,yBAAyB,MAAM;AAC7B,UAAM,WAAW,KAAK;AAClB,QAAA,SAAS,WAAW,EAAU,QAAA;AAGb,aAAS,IAAI,CAAA,MAAK,EAAE,aAAa;AAChD,UAAA,mBAAmB,SAAS,OAAO,CAAA,MAAK,EAAE,SAAS,EAAE,SAAS,SAAS;AAGvE,UAAA,gBAAgB,KAAK,2BAA2B,QAAQ;AAGxD,UAAA,kBAAkB,KAAK,uBAAuB,QAAQ;AAEtD,UAAA,iBACJ,mBAAmB,MACnB,cAAc,aAAa,OAC1B,IAAI,gBAAgB,QAAQ;AAGxB,WAAA,KAAK,IAAI,GAAK,cAAc;AAAA,EAAA;AAAA,EAGrC,yBAAyB,MAAM;AAC7B,UAAM,WAAW,KAAK;AAClB,QAAA,SAAS,WAAW,EAAU,QAAA;AAO5B,UAAA,mBAAmB,KAAK,kBAAkB,QAAQ;AACxD,QAAI,eAAe;AACnB,QAAI,aAAa;AAEjB,WAAO,KAAK,gBAAgB,EAAE,QAAQ,CAAc,eAAA;AAC5C,YAAA,QAAQ,iBAAiB,UAAU;AACrC,UAAA,MAAM,WAAW,EAAG;AAGlB,YAAA,UAAU,MAAM,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,cAAc,CAAC,IAAI,MAAM;AACpE,YAAA,eAAe,KAAK,6BAA6B,UAAU;AACjE,YAAM,iBAAiB,IAAI,KAAK,IAAI,UAAU,YAAY,IAAI;AAGxD,YAAA,WAAW,MAAM,OAAO,CAAA,MAAK,EAAE,SAAS,EAAE,SAAS,MAAM;AAE9C,sBAAA,iBAAiB,MAAM,WAAW;AACnD;AAAA,IAAA,CACD;AAEM,WAAA,aAAa,IAAI,eAAe,aAAa;AAAA,EAAA;AAAA,EAGtD,qBAAqB,MAAM;AACzB,UAAM,WAAW,KAAK;AAClB,QAAA,SAAS,SAAS,EAAU,QAAA;AAGhC,UAAM,gBAAgB,SAAS,IAAI,CAAA,MAAK,EAAE,YAAY;AAChD,UAAA,WAAW,cAAc,OAAO,CAAC,KAAK,MAAM,MAAM,GAAG,CAAC,IAAI,cAAc;AAC9E,UAAM,kBAAkB,cAAc,OAAO,CAAC,KAAK,MAAM,MAAM,KAAK,IAAI,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,cAAc;AAGvG,UAAA,iBAAiB,KAAK,IAAI,GAAG,IAAI,KAAK,KAAK,eAAe,IAAI,QAAQ;AAGtE,UAAA,oBAAoB,KAAK,wBAAwB,QAAQ;AACzD,UAAA,gBAAgB,kBAAkB,YAAY;AAE7C,WAAA,KAAK,IAAI,GAAK,KAAK,IAAI,GAAG,iBAAiB,aAAa,CAAC;AAAA,EAAA;AAAA,EAGlE,4BAA4B,MAAM;AAChC,UAAM,WAAW,KAAK;AAClB,QAAA,SAAS,WAAW,EAAU,QAAA;AAOlC,UAAM,sBAAsB,SAAS,OAAO,CAAK,MAAA,EAAE,gBAAgB,CAAC;AACpE,UAAM,qBAAqB,SAAS,OAAO,CAAK,MAAA,EAAE,iBAAiB,CAAC;AAEpE,QAAI,oBAAoB,WAAW,KAAK,mBAAmB,WAAW,GAAG;AAChE,aAAA;AAAA,IAAA;AAGH,UAAA,sBAAsB,oBAAoB,OAAO,CAAA,MAAK,EAAE,SAAS,EAAE,SAAS,oBAAoB;AAChG,UAAA,qBAAqB,mBAAmB,OAAO,CAAA,MAAK,EAAE,SAAS,EAAE,SAAS,mBAAmB;AAGnG,UAAM,wBAAwB,IAAI,KAAK,IAAI,sBAAsB,kBAAkB;AAG7E,UAAA,gBAAgB,KAAK,qBAAqB,QAAQ;AAExD,WAAO,KAAK,IAAI,GAAM,wBAAwB,MAAM,gBAAgB,GAAI;AAAA,EAAA;AAAA,EAG1E,yBAAyB,MAAM;AAC7B,UAAM,WAAW,KAAK;AAClB,QAAA,SAAS,SAAS,GAAW,QAAA;AAGjC,UAAM,eAAe,KAAK,uBAAuB,UAAU,CAAC;AAExD,QAAA,aAAa,SAAS,EAAU,QAAA;AAEpC,UAAM,oBAAoB,aAAa;AAAA,MAAI,CAAA,YACzC,QAAQ,OAAO,CAAA,MAAK,EAAE,SAAS,EAAE,SAAS,QAAQ;AAAA,IACpD;AAGM,UAAA,eAAe,kBAAkB,CAAC;AACxC,UAAM,cAAc,kBAAkB,kBAAkB,SAAS,CAAC;AAClE,UAAM,UAAU,KAAK,IAAI,GAAG,eAAe,WAAW;AAGtD,UAAM,iBAAiB,KAAK,IAAI,GAAG,IAAI,UAAU,CAAC;AAE3C,WAAA,KAAK,IAAI,GAAK,cAAc;AAAA,EAAA;AAAA,EAGrC,2BAA2B,MAAM;AAC/B,UAAM,WAAW,KAAK;AAClB,QAAA,SAAS,WAAW,EAAU,QAAA;AAG5B,UAAA,oBAAoB,KAAK,wBAAwB,QAAQ;AAE3D,QAAA,kBAAkB,WAAW,EAAU,QAAA;AAGrC,UAAA,sBAAsB,kBAAkB,IAAI,CAAU,WAAA;AAC1D,YAAM,qBAAqB,SAAS,MAAM,OAAO,OAAO,OAAO,QAAQ,CAAC;AACxE,aAAO,mBAAmB,OAAO,CAAA,MAAK,EAAE,SAAS,EAAE,SAAS,mBAAmB;AAAA,IAAA,CAChF;AAEK,UAAA,yBAAyB,oBAAoB,OAAO,CAAC,KAAK,QAAQ,MAAM,KAAK,CAAC,IAAI,oBAAoB;AAErG,WAAA,KAAK,IAAI,GAAK,sBAAsB;AAAA,EAAA;AAAA,EAG7C,yBAAyB,MAAM;AAC7B,UAAM,WAAW,KAAK;AAClB,QAAA,SAAS,WAAW,EAAU,QAAA;AAOlC,UAAM,kBAAkB,SAAS,OAAO,CAAK,MAAA,EAAE,gBAAgB,EAAE;AAE7D,QAAA,gBAAgB,WAAW,GAAG;AAE1B,YAAA,WAAW,SAAS,OAAO,CAAA,MAAK,EAAE,SAAS,EAAE,SAAS,SAAS;AAC/D,YAAA,UAAU,SAAS,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,cAAc,CAAC,IAAI,SAAS;AAG1EC,YAAAA,aAAY,KAAK,0BAA0B,OAAO;AAExD,aAAO,KAAK,IAAI,GAAM,WAAW,MAAMA,aAAY,GAAI;AAAA,IAAA;AAGnD,UAAA,kBAAkB,gBAAgB,OAAO,CAAA,MAAK,EAAE,SAAS,EAAE,SAAS,gBAAgB;AACpF,UAAA,iBAAiB,gBAAgB,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,cAAc,CAAC,IAAI,gBAAgB;AAE/F,UAAA,YAAY,KAAK,0BAA0B,cAAc;AACzD,UAAA,qBAAsB,kBAAkB,MAAM,YAAY;AAEzD,WAAA,KAAK,IAAI,GAAK,kBAAkB;AAAA,EAAA;AAAA,EAGzC,2BAA2B,MAAM;AAC/B,UAAM,WAAW,KAAK;AAClB,QAAA,SAAS,SAAS,EAAU,QAAA;AAO1B,UAAA,eAAe,KAAK,wBAAwB,QAAQ;AAC1D,UAAM,aAAa,OAAO,KAAK,YAAY,EAAE;AAEzC,QAAA,aAAa,EAAU,QAAA;AAG3B,QAAI,mBAAmB;AACvB,WAAO,KAAK,YAAY,EAAE,QAAQ,CAAS,UAAA;AACnC,YAAA,gBAAgB,aAAa,KAAK;AAClC,YAAA,gBAAgB,cAAc,OAAO,CAAA,MAAK,EAAE,SAAS,EAAE,SAAS,cAAc;AAChE,0BAAA;AAAA,IAAA,CACrB;AAEmB,wBAAA;AAGd,UAAA,qBAAqB,KAAK,yBAAyB,QAAQ;AAC3D,UAAA,gBAAgB,mBAAmB,QAAQ;AAEjD,WAAO,KAAK,IAAI,GAAK,mBAAmB,aAAa;AAAA,EAAA;AAAA,EAGvD,4BAA4B,MAAM;AAChC,UAAM,WAAW,KAAK;AAClB,QAAA,SAAS,WAAW,EAAU,QAAA;AAG5B,UAAA,WAAW,SAAS,OAAO,CAAA,MAAK,EAAE,SAAS,EAAE,SAAS,SAAS;AAC/D,UAAA,UAAU,SAAS,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,cAAc,CAAC,IAAI,SAAS;AAG1E,UAAA,iBAAiB,KAAK,wBAAwB,OAAO;AAGrD,UAAA,kBAAmB,WAAW,MAAM,iBAAiB;AAG3D,UAAM,mBAAmB,KAAK,4BAA4B,QAAQ,IAAI;AAEtE,WAAO,KAAK,IAAI,GAAK,kBAAkB,gBAAgB;AAAA,EAAA;AAAA;AAAA,EAIzD,uBAAuB,UAAU,cAAc;AAC7C,UAAM,cAAc,KAAK,MAAM,SAAS,SAAS,YAAY;AAC7D,UAAM,WAAW,CAAC;AAElB,aAAS,IAAI,GAAG,IAAI,cAAc,KAAK;AACrC,YAAM,QAAQ,IAAI;AAClB,YAAM,MAAM,MAAM,eAAe,IAAI,SAAS,UAAU,IAAI,KAAK;AACjE,eAAS,KAAK,SAAS,MAAM,OAAO,GAAG,CAAC;AAAA,IAAA;AAG1C,WAAO,SAAS,OAAO,CAAW,YAAA,QAAQ,SAAS,CAAC;AAAA,EAAA;AAAA,EAGtD,2BAA2B,UAAU;AACnC,UAAM,gBAAgB;AAAA,MACpB,KAAK,SAAS,OAAO,CAAK,MAAA,EAAE,iBAAiB,CAAC;AAAA,MAC9C,QAAQ,SAAS,OAAO,CAAA,MAAK,EAAE,gBAAgB,KAAK,EAAE,iBAAiB,CAAC;AAAA,MACxE,MAAM,SAAS,OAAO,CAAK,MAAA,EAAE,gBAAgB,CAAC;AAAA,IAChD;AAEA,QAAI,kBAAkB;AACtB,QAAI,aAAa;AAEjB,WAAO,KAAK,aAAa,EAAE,QAAQ,CAAW,YAAA;AACtC,YAAA,QAAQ,cAAc,OAAO;AAC/B,UAAA,MAAM,WAAW,EAAG;AAElB,YAAA,UAAU,MAAM,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,cAAc,CAAC,IAAI,MAAM;AAC1E,YAAM,eAAe,YAAY,QAAQ,MAAO,YAAY,WAAW,MAAO;AAExE,YAAA,aAAa,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,UAAU,YAAY,IAAI,YAAY;AAC/D,yBAAA;AACnB;AAAA,IAAA,CACD;AAED,WAAO,EAAE,YAAY,aAAa,IAAI,kBAAkB,aAAa,IAAI;AAAA,EAAA;AAAA,EAG3E,uBAAuB,UAAU;AAC/B,UAAM,SAAS,SAAS,OAAO,CAAK,MAAA,CAAC,EAAE,SAAS;AAC5C,QAAA,OAAO,WAAW,EAAG,QAAO,EAAE,MAAM,GAAG,UAAU,GAAG;AAGxD,UAAM,WAAW,OAAO,OAAO,CAAA,MAAK,KAAK,IAAI,EAAE,aAAa,EAAE,aAAa,MAAM,CAAC,EAAE;AACpF,UAAM,YAAY,OAAO,OAAO,CAAA,MAAK,KAAK,IAAI,EAAE,aAAa,EAAE,aAAa,IAAI,CAAC,EAAE;AAE7E,UAAA,qBAAqB,OAAO,SAAS,SAAS;AACpD,UAAM,WAAW,CAAC;AAElB,QAAI,WAAW,OAAO,SAAS,IAAK,UAAS,KAAK,qBAAqB;AACvE,QAAI,YAAY,OAAO,SAAS,IAAK,UAAS,KAAK,mBAAmB;AAE/D,WAAA,EAAE,MAAM,oBAAoB,SAAS;AAAA,EAAA;AAAA,EAG9C,kBAAkB,UAAU;AAC1B,WAAO,SAAS,OAAO,CAAC,QAAQ,YAAY;AAC1C,YAAM,aAAa,KAAK,gBAAgB,QAAQ,aAAa;AAC7D,UAAI,CAAC,OAAO,UAAU,EAAU,QAAA,UAAU,IAAI,CAAC;AACxC,aAAA,UAAU,EAAE,KAAK,OAAO;AACxB,aAAA;AAAA,IACT,GAAG,EAAE;AAAA,EAAA;AAAA,EAGP,gBAAgB,QAAQ;AAClB,QAAA,UAAU,EAAU,QAAA;AACpB,QAAA,UAAU,GAAW,QAAA;AAClB,WAAA;AAAA,EAAA;AAAA,EAGT,6BAA6B,YAAY;AACvC,UAAM,QAAQ,EAAE,MAAM,MAAM,QAAQ,KAAM,MAAM,IAAK;AAC9C,WAAA,MAAM,UAAU,KAAK;AAAA,EAAA;AAAA,EAG9B,wBAAwB,UAAU;AAEhC,UAAM,SAAS,SAAS;AAAA,MAAO,CAC5B,MAAA,CAAC,EAAE,aAAa,EAAE,eAAe;AAAA,MACjC,EAAE,eAAe;AAAA;AAAA,IACpB;AAEO,WAAA;AAAA,MACL,WAAW,OAAO,SAAS,SAAS;AAAA,MACpC,OAAO,OAAO;AAAA,MACd,OAAO;AAAA,QACL,WAAW,OAAO,OAAO,OAAK,EAAE,eAAe,GAAI,EAAE;AAAA,QACrD,YAAY,OAAO,OAAO,OAAK,EAAE,eAAe,IAAK,EAAE;AAAA,MAAA;AAAA,IAE3D;AAAA,EAAA;AAAA,EAGF,qBAAqB,UAAU;AAC7B,UAAM,SAAS,SAAS,IAAI,CAAC,SAAS,WAAW,EAAE,GAAG,SAAS,QAAQ,EAChD,OAAO,CAAK,MAAA,CAAC,EAAE,SAAS;AAE3C,QAAA,OAAO,WAAW,EAAU,QAAA;AAEhC,QAAI,gBAAgB;AACpB,WAAO,QAAQ,CAASD,WAAA;AACtB,YAAM,cAAc,SAASA,OAAM,QAAQ,CAAC;AACxC,UAAA,eAAe,YAAY,WAAW;AACxC;AAAA,MAAA;AAAA,IACF,CACD;AAED,WAAO,gBAAgB,OAAO;AAAA,EAAA;AAAA,EAGhC,wBAAwB,UAAU;AAChC,UAAM,UAAU,CAAC;AAEjB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAM,iBAAiB,KAAK,gBAAgB,SAAS,IAAE,CAAC,EAAE,aAAa;AACvE,YAAM,iBAAiB,KAAK,gBAAgB,SAAS,CAAC,EAAE,aAAa;AAErE,UAAI,mBAAmB,gBAAgB;AACrC,gBAAQ,KAAK;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,IAAI;AAAA,QAAA,CACL;AAAA,MAAA;AAAA,IACH;AAGK,WAAA;AAAA,EAAA;AAAA,EAGT,0BAA0B,SAAS;AAEjC,UAAM,aAAa;AACnB,UAAM,aAAa;AAEf,QAAA,WAAW,cAAc,WAAW,YAAY;AAC3C,aAAA;AAAA,IAAA,WACE,UAAU,YAAY;AAC/B,aAAO,KAAK,IAAI,GAAG,UAAU,UAAU;AAAA,IAAA,OAClC;AACL,aAAO,KAAK,IAAI,GAAG,KAAK,UAAU,cAAc,UAAU;AAAA,IAAA;AAAA,EAC5D;AAAA,EAGF,wBAAwB,UAAU;AAChC,WAAO,SAAS,OAAO,CAAC,YAAY,YAAY;AACxC,YAAA,QAAQ,QAAQ,iBAAiB,IAAI,UAC9B,QAAQ,iBAAiB,IAAI,WAAW;AAErD,UAAI,CAAC,WAAW,KAAK,EAAc,YAAA,KAAK,IAAI,CAAC;AAClC,iBAAA,KAAK,EAAE,KAAK,OAAO;AACvB,aAAA;AAAA,IACT,GAAG,EAAE;AAAA,EAAA;AAAA,EAGP,yBAAyB,UAAU;AAEjC,QAAI,kBAAkB;AAGtB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AAClC,YAAA,OAAO,SAAS,IAAE,CAAC;AACnB,YAAA,OAAO,SAAS,CAAC;AAEvB,UAAI,CAAC,KAAK,aAAa,KAAK,eAAe,KAAK,eAAe,KAAK;AAClE;AAAA,MAAA;AAAA,IACF;AAGK,WAAA,EAAE,OAAO,gBAAgB;AAAA,EAAA;AAAA,EAGlC,wBAAwB,SAAS;AAE/B,UAAM,YAAY;AACX,WAAA,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,UAAU,SAAS,IAAI,SAAS;AAAA,EAAA;AAAA,EAGlE,4BAA4B,UAAU;AAChC,QAAA,SAAS,SAAS,EAAU,QAAA;AAE1B,UAAA,eAAe,SAAS,IAAI,CAAK,MAAA;AAC/B,YAAA,WAAW,EAAE,YAAY,IAAI;AACnC,YAAM,iBAAiB,KAAK,wBAAwB,EAAE,YAAY;AAC1D,aAAA,WAAW,MAAM,iBAAiB;AAAA,IAAA,CAC3C;AAEK,UAAA,iBAAiB,aAAa,OAAO,CAAC,KAAK,MAAM,MAAM,GAAG,CAAC,IAAI,aAAa;AAClF,UAAM,WAAW,aAAa,OAAO,CAAC,KAAK,MAAM,MAAM,KAAK,IAAI,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,aAAa;AAE1G,WAAO,KAAK,IAAI,GAAG,IAAI,QAAQ;AAAA,EAAA;AAAA,EAGjC,oBAAoB;AACX,WAAA;AAAA,MACL,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,gBAAgB;AAAA,MAChB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,sBAAsB;AAAA,MACtB,oBAAoB;AAAA,MACpB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,IACzB;AAAA,EAAA;AAEJ;AC7fO,MAAM,0BAA0B;AAAA,EACrC,cAAc;AACZ,SAAK,mBAAmB;AAAA,MACtB,mBAAmB;AAAA,MACnB,qBAAqB;AAAA,MACrB,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,IAChB;AAEA,SAAK,uBAAuB;AAAA,MAC1B,WAAW;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAEA,SAAK,mBAAmB;AAAA,MACtB,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAEA,YAAQ,IAAI,4CAA4C;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ1D,QAAQ,MAAM;AACL,WAAA,KAAK,QAAQ,IAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,oBAAoB;AACX,WAAA;AAAA,MACL,0BAA0B,EAAE,OAAO,GAAG,YAAY,GAAG,iBAAiB,UAAU;AAAA,MAChF,mBAAmB,EAAE,UAAU,GAAG,OAAO,GAAG,YAAY,EAAE;AAAA,MAC1D,qBAAqB,EAAE,YAAY,GAAG,YAAY,GAAG,UAAU,EAAE;AAAA,MACjE,wBAAwB,EAAE,gBAAgB,GAAG,SAAS,GAAG,OAAO,EAAE;AAAA,MAClE,yBAAyB,EAAE,WAAW,GAAG,QAAQ,GAAG,aAAa,EAAE;AAAA,MACnE,kBAAkB,EAAE,UAAU,WAAW,aAAa,GAAG,eAAe,EAAE;AAAA,MAC1E,iBAAiB,EAAE,aAAa,GAAG,aAAa,GAAG,YAAY,EAAE;AAAA,MACjE,sBAAsB,EAAE,UAAU,GAAG,WAAW,GAAG,aAAa,EAAE;AAAA,MAClE,iBAAiB,EAAE,OAAO,GAAG,SAAS,GAAG,WAAW,EAAE;AAAA,MACtD,wBAAwB,EAAE,UAAU,GAAG,WAAW,GAAG,WAAW,EAAE;AAAA,MAClE,WAAW,KAAK,IAAI;AAAA,MACpB,aAAa;AAAA,IACf;AAAA,EAAA;AAAA,EAGF,MAAM,QAAQ,MAAM;AAClB,QAAI,CAAC,QAAQ,CAAC,KAAK,UAAU;AACnB,cAAA,KAAK,wDAAwD,IAAI;AACzE,aAAO,KAAK,kBAAkB;AAAA,IAAA;AAGzB,WAAA;AAAA,MACL,0BAA0B,KAAK,+BAA+B,IAAI;AAAA,MAClE,mBAAmB,KAAK,wBAAwB,IAAI;AAAA,MACpD,qBAAqB,KAAK,0BAA0B,IAAI;AAAA,MACxD,wBAAwB,KAAK,6BAA6B,IAAI;AAAA,MAC9D,yBAAyB,KAAK,8BAA8B,IAAI;AAAA,MAChE,kBAAkB,KAAK,wBAAwB,IAAI;AAAA,MACnD,iBAAiB,KAAK,sBAAsB,IAAI;AAAA,MAChD,sBAAsB,KAAK,2BAA2B,IAAI;AAAA,MAC1D,iBAAiB,KAAK,sBAAsB,IAAI;AAAA,MAChD,wBAAwB,KAAK,6BAA6B,IAAI;AAAA,IAChE;AAAA,EAAA;AAEJ;AC5EO,MAAM,+BAA+B;AAAA,EAC1C,cAAc;AACZ,SAAK,sBAAsB;AAAA,MACzB,uBAAuB;AAAA,MACvB,sBAAsB;AAAA,MACtB,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAEA,SAAK,qBAAqB;AAAA,MACxB,UAAU;AAAA,MACV,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,IACd;AAEA,YAAQ,IAAI,gDAAgD;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ9D,QAAQ,MAAM;AACL,WAAA,KAAK,QAAQ,IAAI;AAAA,EAAA;AAAA,EAG1B,MAAM,QAAQ,MAAM;AAClB,QAAI,CAAC,QAAQ,CAAC,KAAK,UAAU;AACnB,cAAA,KAAK,6DAA6D,IAAI;AAC9E,aAAO,KAAK,kBAAkB;AAAA,IAAA;AAGzB,WAAA;AAAA,MACL,sBAAsB,KAAK,2BAA2B,IAAI;AAAA,MAC1D,qBAAqB,KAAK,0BAA0B,IAAI;AAAA,MACxD,wBAAwB,KAAK,6BAA6B,IAAI;AAAA,MAC9D,+BAA+B,KAAK,wBAAwB,IAAI;AAAA,MAChE,mBAAmB,KAAK,wBAAwB,IAAI;AAAA,MACpD,iBAAiB,KAAK,sBAAsB,IAAI;AAAA,MAChD,eAAe,KAAK,uBAAuB,IAAI;AAAA,MAC/C,qBAAqB,KAAK,0BAA0B,IAAI;AAAA,MACxD,mBAAmB,KAAK,wBAAwB,IAAI;AAAA,MACpD,oBAAoB,KAAK,yBAAyB,IAAI;AAAA,IACxD;AAAA,EAAA;AAAA,EAGF,oBAAoB;AACX,WAAA;AAAA,MACL,sBAAsB;AAAA,MACtB,qBAAqB;AAAA,MACrB,wBAAwB,EAAE,YAAY,KAAK,cAAc,IAAI;AAAA,MAC7D,+BAA+B;AAAA,MAC/B,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,eAAe,EAAE,UAAU,IAAI,WAAW,EAAE;AAAA,MAC5C,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,IACtB;AAAA,EAAA;AAEJ;ACjEO,MAAM,sBAAsB;AAAA,EACjC,cAAc;AACZ,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,gBAAgB,CAAC;AAEtB,SAAK,YAAY;AAAA,MACf,gBAAgB,CAAC;AAAA,MACjB,gBAAgB,CAAC;AAAA,MACjB,yBAAyB,CAAC;AAAA,MAC1B,0BAA0B,CAAC;AAAA,MAC3B,kBAAkB,CAAC;AAAA,MACnB,eAAe,CAAC;AAAA,MAChB,oBAAoB,CAAC;AAAA,MACrB,sBAAsB,CAAA;AAAA,IACxB;AACK,SAAA,mBAAmB,KAAK,IAAI;AACjC,SAAK,kBAAkB;AAAA,MACrB,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,UAAU;AAAA,QACR,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,MAAA;AAAA,IAEV;AACA,YAAQ,IAAI,MAAM,KAAK,IAAI,KAAK,KAAK,OAAO,eAAe;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ7D,QAAQ,UAAU;AAChB,QAAI,CAAC,UAAU;AACb,cAAQ,KAAK,iFAAiF;AACvF,aAAA,EAAE,QAAQ,CAAC,GAAG,UAAU,CAAC,GAAG,SAAS,GAAG;AAAA,IAAA;AAGjD,YAAQ,IAAI,uEAAuE,SAAS,aAAa,QAAQ,EAAE;AAE/G,QAAA;AAEI,YAAA,eAAe,KAAK,qBAAqB,QAAQ;AACvD,YAAM,SAAS,CAAC;AAGhB,UAAI,SAAS,kBAAkB,MAAM,QAAQ,SAAS,cAAc,GAAG;AACrE,iBAAS,eAAe,QAAQ,CAAC,SAAS,UAAU;AAC9C,cAAA,CAAC,QAAQ,aAAa,QAAQ,iBAAiB,UAAa,QAAQ,mBAAmB,QAAW;AACpG,kBAAM,gBAAgB,KAAK;AAAA,cACzB,QAAQ;AAAA,cACR,QAAQ;AAAA,cACR;AAAA,gBACE,YAAY,SAAS,cAAc;AAAA,gBACnC,cAAc,QAAQ,gBAAgB;AAAA,gBACtC,eAAe;AAAA,cAAA;AAAA,YAEnB;AACI,gBAAA,cAAsB,QAAA,KAAK,aAAa;AAAA,UAAA;AAAA,QAC9C,CACD;AAAA,MAAA;AAIH,UAAI,SAAS,mBAAmB,MAAM,QAAQ,SAAS,eAAe,GAAG;AAC9D,iBAAA,gBAAgB,QAAQ,CAAC,aAAa;AAC7C,cAAI,SAAS,YAAY,SAAS,UAAU,SAAS,aAAa,SAAS,QAAQ;AACjF,kBAAM,gBAAgB,KAAK;AAAA,cACzB,SAAS;AAAA,cACT,SAAS;AAAA,cACT;AAAA,gBACE,cAAc,SAAS,QAAQ;AAAA,gBAC/B,YAAY,SAAS,cAAc;AAAA,cAAA;AAAA,YAEvC;AACI,gBAAA,cAAsB,QAAA,KAAK,aAAa;AAAA,UAAA;AAAA,QAC9C,CACD;AAAA,MAAA;AAIH,YAAM,kBAAkB;AAAA,QACtB,WAAW,KAAK,IAAI;AAAA,QACpB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN;AAAA,QACA,aAAa;AAAA,UACX,WAAW,SAAS;AAAA,UACpB,OAAO,SAAS,SAAS;AAAA,UACzB,SAAS,SAAS,WAAW;AAAA,QAAA;AAAA,MAEjC;AAEK,WAAA,cAAc,KAAK,eAAe;AACvC,WAAK,iBAAiB,YAAY;AAE3B,aAAA;AAAA,QACL;AAAA,QACA,UAAU;AAAA,QACV,SAAS,KAAK,qBAAqB,QAAQ;AAAA,MAC7C;AAAA,aACOA,QAAO;AACN,cAAA,MAAM,wDAAwDA,MAAK;AAC3E,aAAO,EAAE,QAAQ,CAAC,GAAG,UAAU,CAAA,GAAI,SAAS,IAAI,OAAOA,OAAM,QAAQ;AAAA,IAAA;AAAA,EACvE;AAAA,EAGF,qBAAqB,UAAU;AAC7B,UAAM,WAAW;AAAA,MACf,gBAAgB,KAAK,qBAAqB,QAAQ;AAAA,MAClD,gBAAgB,KAAK,qBAAqB,QAAQ;AAAA,MAClD,yBAAyB,KAAK,wBAAwB,QAAQ;AAAA,MAC9D,0BAA0B,KAAK,uBAAuB,QAAQ;AAAA,MAC9D,UAAU,KAAK,yBAAyB,QAAQ;AAAA,IAClD;AAEO,WAAA;AAAA,EAAA;AAAA,EAGT,qBAAqB,UAAU;AAC7B,WAAO,CAAC;AAAA,EAAA;AAAA,EAGV,qBAAqB,UAAU;AAC7B,WAAO,CAAC;AAAA,EAAA;AAAA,EAGV,wBAAwB,UAAU;AAChC,WAAO,CAAC;AAAA,EAAA;AAAA,EAGV,uBAAuB,UAAU;AAC/B,WAAO,CAAC;AAAA,EAAA;AAAA,EAGV,iBAAiB,cAAc;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAO/B,qBAAqB,cAAc,eAAe,SAAS;AACzD,UAAM,WAAW,GAAG,YAAY,KAAK,aAAa;AAClD,UAAM,aAAa,KAAK,IAAI,eAAe,aAAa;AAExD,UAAM,gBAAgB;AAAA,MACpB,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW,KAAK,0BAA0B,cAAc,eAAe,OAAO;AAAA,MAC9E,SAAS;AAAA,QACP,YAAY,QAAQ,cAAc;AAAA,QAClC,YAAY,QAAQ,cAAc;AAAA,QAClC,aAAa,QAAQ,eAAe;AAAA,QACpC,cAAc,QAAQ,gBAAgB;AAAA,QACtC,UAAU,QAAQ,YAAY;AAAA,QAC9B,kBAAkB,QAAQ,oBAAoB;AAAA,MAChD;AAAA,MACA,UAAU,KAAK,+BAA+B,cAAc,eAAe,OAAO;AAAA,MAClF,eAAe,aAAa;AAAA,MAC5B,WAAW,gBAAgB,eAAe,cAAc;AAAA,IAC1D;AAEA,QAAI,CAAC,KAAK,UAAU,eAAe,QAAQ,GAAG;AAC5C,WAAK,UAAU,eAAe,QAAQ,IAAI,CAAC;AAAA,IAAA;AAE7C,SAAK,UAAU,eAAe,QAAQ,EAAE,KAAK,aAAa;AAErD,SAAA,8BAA8B,UAAU,aAAa;AAC1D,SAAK,uBAAuB,aAAa;AAElC,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,qBAAqB,kBAAkB,gBAAgB,SAAS;AAC9D,UAAM,gBAAgB;AAAA,MACpB,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC;AAAA,MACA;AAAA,MACA,WAAW,KAAK,0BAA0B,kBAAkB,cAAc;AAAA,MAC1E,SAAS;AAAA,QACP,cAAc,QAAQ,gBAAgB;AAAA,QACtC,YAAY,QAAQ,cAAc;AAAA,MACpC;AAAA,MACA,UAAU,KAAK,+BAA+B,kBAAkB,gBAAgB,OAAO;AAAA,IACzF;AAEK,SAAA,UAAU,eAAe,KAAK,aAAa;AACzC,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,0BAA0B,SAAS,UAAU,SAAS;AACpD,UAAM,aAAa,KAAK,IAAI,UAAU,QAAQ;AAC9C,UAAM,gBAAgB,aAAa;AAE/B,QAAA,eAAe,EAAU,QAAA;AACzB,QAAA,eAAe,EAAU,QAAA;AACzB,QAAA,gBAAgB,IAAY,QAAA;AAC5B,QAAA,gBAAgB,IAAY,QAAA;AAC5B,QAAA,iBAAiB,IAAY,QAAA;AAG7B,QAAA,aAAa,UAAU,EAAU,QAAA;AACrC,QAAI,aAAa,KAAK,MAAM,UAAU,CAAC,EAAU,QAAA;AACjD,QAAI,WAAW,MAAM,KAAK,UAAU,MAAM,EAAU,QAAA;AACpD,QAAI,WAAW,OAAO,KAAK,UAAU,OAAO,EAAU,QAAA;AAE/C,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,0BAA0B,UAAU,QAAQ;AACtC,QAAA,CAAC,OAAe,QAAA;AAGb,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,+BAA+BE,eAAc,gBAAgB,SAAS;AACpE,QAAI,WAAW;AAGf,UAAM,WAAW,KAAK,IAAIA,gBAAe,cAAc;AACvD,gBAAY,WAAW;AAGnB,QAAA,WAAW,IAAgB,YAAA;AAG3B,QAAA,QAAQ,eAAe,IAAkB,aAAA;AACzC,QAAA,QAAQ,eAAe,IAAiB,aAAA;AAGxC,QAAA,QAAQ,eAAe,OAAoB,aAAA;AAE/C,WAAO,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC,GAAG,CAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM1C,+BAA+B,UAAU,QAAQ,SAAS;AAEjD,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,kBAAkB,UAAU,cAAc,QAAQ;AAChD,UAAM,kBAAkB;AAAA,MACtB,WAAW,KAAK,IAAI;AAAA,MACpB,MAAM;AAAA,MACN,UAAU;AAAA,MACV,MAAM;AAAA,MACN;AAAA,MACA,aAAa;AAAA,QACX,WAAW,SAAS;AAAA,QACpB,OAAO,SAAS,SAAS;AAAA,QACzB,SAAS,SAAS,WAAW;AAAA,MAAA;AAAA,IAEjC;AAEK,SAAA,cAAc,KAAK,eAAe;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMzC,qBAAqB,UAAU;AAC7B,UAAM,qBAAqB,OAAO,OAAO,KAAK,UAAU,cAAc,EAAE;AAAA,MACtE,CAAC,OAAO,WAAW,QAAQ,OAAO;AAAA,MAAQ;AAAA,IAC5C;AAEO,WAAA;AAAA,MACL,aAAa,qBAAqB,KAAK,UAAU,eAAe;AAAA,MAChE,sBAAsB,OAAO,KAAK,KAAK,UAAU,cAAc,EAAE;AAAA,MACjE,iBAAiB,KAAK,oBAAoB;AAAA,MAC1C,iBAAiB,KAAK,yBAAyB;AAAA,MAC/C,gCAAgC,KAAK,wCAAwC,QAAQ;AAAA,MACrF,0BAA0B,KAAK,kCAAkC,QAAQ;AAAA,MACzE,aAAa,KAAK,2BAA2B,QAAQ;AAAA,IACvD;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,sBAAsB;AACpB,QAAI,WAAW;AACf,QAAI,kBAAkB;AAEf,WAAA,QAAQ,KAAK,UAAU,cAAc,EAAE,QAAQ,CAAC,CAAC,UAAU,MAAM,MAAM;AACxE,UAAA,OAAO,SAAS,UAAU;AAC5B,mBAAW,OAAO;AACA,0BAAA;AAAA,MAAA;AAAA,IACpB,CACD;AAEM,WAAA;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,2BAA2B;AACzB,QAAI,gBAAgB;AACpB,QAAI,aAAa;AAEjB,WAAO,OAAO,KAAK,UAAU,cAAc,EAAE,QAAQ,CAAU,WAAA;AAC7D,aAAO,QAAQ,CAASF,WAAA;AACtB,yBAAiBA,OAAM;AACvB;AAAA,MAAA,CACD;AAAA,IAAA,CACF;AAEI,SAAA,UAAU,eAAe,QAAQ,CAASA,WAAA;AAC7C,uBAAiBA,OAAM;AACvB;AAAA,IAAA,CACD;AAEM,WAAA,aAAa,IAAI,gBAAgB,aAAa;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMvD,wCAAwC,UAAU;AAEzC,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,kCAAkC,UAAU;AAEnC,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,2BAA2B,UAAU;AAE5B,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,QAAQ,UAAU;AACT,WAAA,KAAK,QAAQ,QAAQ;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM9B,8BAA8B,UAAU,WAAW;AACjD,QAAI,CAAC,KAAK,UAAU,iBAAiB,QAAQ,GAAG;AAC9C,WAAK,UAAU,iBAAiB,QAAQ,IAAI,CAAC;AAAA,IAAA;AAG/C,SAAK,UAAU,iBAAiB,QAAQ,EAAE,KAAK,SAAS;AAEpD,QAAA,KAAK,UAAU,iBAAiB,QAAQ,EAAE,UAAU,KAAK,gBAAgB,YAAY;AACvF,gBAAU,eAAe;AACpB,WAAA,oBAAoB,UAAU,gBAAgB;AAAA,IAAA;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAMF,uBAAuB,eAAe;AAE9B,UAAA,uBAAuB,OAAO,OAAO,KAAK,UAAU,cAAc,EACrE,KAAA,EACA,MAAM,EAAE;AAEX,UAAM,iBAAiB,qBAAqB,OAAO,OAAK,EAAE,cAAc,WAAW,EAAE;AACrF,UAAM,kBAAkB,qBAAqB,OAAO,OAAK,EAAE,cAAc,YAAY,EAAE;AAEvF,QAAI,kBAAkB,GAAG;AACvB,oBAAc,kBAAkB;AAAA,IAAA,WACvB,mBAAmB,GAAG;AAC/B,oBAAc,kBAAkB;AAAA,IAAA;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAMF,sBAAsB,eAAe;AACnC,UAAM,uBAAuB,KAAK,UAAU,eAAe,MAAM,EAAE;AAG7D,UAAA,iBAAiB,qBACpB,QAAQ,CAAK,MAAA,EAAE,eAAe,IAAI,CAAA,OAAM,GAAG,QAAQ,CAAC;AAEvD,UAAM,iBAAiB,CAAC;AACxB,mBAAe,QAAQ,CAAO,QAAA;AAC5B,qBAAe,GAAG,KAAK,eAAe,GAAG,KAAK,KAAK;AAAA,IAAA,CACpD;AAED,UAAM,mBAAmB,OAAO,QAAQ,cAAc,EACnD,KAAK,CAAC,CAAC,KAAK,KAAK,MAAM,SAAS,CAAC;AAEpC,QAAI,kBAAkB;AACpB,oBAAc,kBAAkB,gCAAgC,iBAAiB,CAAC,CAAC;AAAA,IAAA;AAAA,EACrF;AAAA;AAAA;AAAA;AAAA,EAMF,yBAAyB,kBAAkB;AACzC,UAAM,0BAA0B,KAAK,UAAU,wBAAwB,MAAM,EAAE;AAG/E,UAAM,iBAAiB,wBAAwB,IAAI,CAAA,MAAK,EAAE,aAAa;AACvE,UAAM,yBAAyB,eAAe,OAAO,CAAK,MAAA,MAAM,mBAAmB,EAAE;AAErF,QAAI,0BAA0B,GAAG;AAC/B,uBAAiB,kBAAkB;AAAA,IAAA;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAMF,yBAAyB,iBAAiB;AACxC,UAAM,yBAAyB,KAAK,UAAU,yBAAyB,MAAM,EAAE;AAG/E,UAAM,aAAa,uBAAuB,IAAI,CAAA,MAAK,EAAE,kBAAkB;AACvE,UAAM,iBAAiB,CAAC;AACxB,eAAW,QAAQ,CAAY,aAAA;AAC7B,qBAAe,QAAQ,KAAK,eAAe,QAAQ,KAAK,KAAK;AAAA,IAAA,CAC9D;AAED,UAAM,mBAAmB,OAAO,QAAQ,cAAc,EACnD,KAAK,CAAC,CAAC,UAAU,KAAK,MAAM,SAAS,CAAC;AAEzC,QAAI,kBAAkB;AACpB,sBAAgB,kBAAkB,cAAc,iBAAiB,CAAC,CAAC;AAAA,IAAA;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUF,6BAA6B;AACpB,WAAA;AAAA,MACL,UAAU,KAAK,qBAAqB;AAAA,MACpC,mBAAmB,KAAK,8BAA8B;AAAA,MACtD,YAAY,KAAK,uBAAuB;AAAA,MACxC,oBAAoB,KAAK,+BAA+B;AAAA,MACxD,YAAY,KAAK,8BAA8B;AAAA,IACjD;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,uBAAuB;AACrB,UAAM,iBAAiB,OAAO,OAAO,KAAK,UAAU,cAAc,EAAE,KAAK;AACrE,QAAA,eAAe,WAAW,EAAG,QAAO,EAAE,OAAO,cAAc,YAAY,EAAI;AAEzE,UAAA,kBAAkB,eAAe,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,UAAU,CAAC,IAAI,eAAe;AAChG,UAAM,iBAAiB,eAAe,OAAO,OAAK,EAAE,cAAc,YAAY,EAAE;AAChF,UAAM,cAAc,eAAe,OAAO,OAAK,EAAE,cAAc,gBAAgB,EAAE;AAEjF,QAAI,QAAQ;AACZ,QAAI,aAAa;AAEb,QAAA,kBAAkB,OAAO,gBAAgB,GAAG;AACtC,cAAA;AACK,mBAAA;AAAA,IACJ,WAAA,kBAAkB,OAAO,eAAe,GAAG;AAC5C,cAAA;AACK,mBAAA;AAAA,IAAA,OACR;AACG,cAAA;AACR,mBAAa,KAAK,IAAI,KAAK,IAAI,eAAe;AAAA,IAAA;AAGzC,WAAA,EAAE,OAAO,YAAY,SAAS,EAAE,gBAAgB,aAAa,kBAAkB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMxF,gCAAgC;AACxB,UAAA,oBAAoB,KAAK,UAAU;AACrC,QAAA,kBAAkB,WAAW,EAAG,QAAO,EAAE,OAAO,cAAc,YAAY,EAAI;AAElF,UAAM,mBAAmB,kBAAkB,OAAO,OAAK,EAAE,kBAAkB,mBAAmB,EAAE;AAC1F,UAAA,kBAAkB,kBAAkB,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,UAAU,CAAC,IAAI,kBAAkB;AAEtG,QAAI,QAAQ;AACZ,QAAI,aAAa;AAEjB,QAAI,kBAAkB,KAAK;AACjB,cAAA;AACK,mBAAA;AAAA,IACJ,WAAA,mBAAmB,kBAAkB,SAAS,KAAK;AACpD,cAAA;AACK,mBAAA;AAAA,IAAA,OACR;AACG,cAAA;AACR,mBAAa,KAAK,IAAI,KAAK,IAAI,eAAe;AAAA,IAAA;AAGhD,WAAO,EAAE,OAAO,YAAY,SAAS,EAAE,kBAAkB,kBAAkB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM7E,yBAAyB;AACjB,UAAA,iBAAiB,KAAK,UAAU;AAClC,QAAA,eAAe,WAAW,EAAG,QAAO,EAAE,OAAO,cAAc,YAAY,EAAI;AAE/E,UAAM,sBAAsB,eAAe,OAAO,OAAK,EAAE,cAAc,qBAAqB,EAAE;AACxF,UAAA,kBAAkB,eAAe,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,UAAU,CAAC,IAAI,eAAe;AAEhG,QAAI,QAAQ;AACZ,QAAI,aAAa;AAEjB,QAAI,kBAAkB,KAAK;AACjB,cAAA;AACK,mBAAA;AAAA,IACJ,WAAA,sBAAsB,eAAe,SAAS,KAAK;AACpD,cAAA;AACK,mBAAA;AAAA,IAAA,OACR;AACG,cAAA;AACR,mBAAa,KAAK,IAAI,KAAK,IAAI,eAAe;AAAA,IAAA;AAGhD,WAAO,EAAE,OAAO,YAAY,SAAS,EAAE,qBAAqB,kBAAkB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMhF,iCAAiC;AACzB,UAAA,mBAAmB,KAAK,UAAU;AACpC,QAAA,iBAAiB,WAAW,EAAG,QAAO,EAAE,OAAO,cAAc,YAAY,EAAI;AAEjF,UAAM,sBAAsB,iBAAiB,OAAO,OAAK,EAAE,uBAAuB,qBAAqB,EAAE;AACnG,UAAA,uBAAuB,iBAAiB,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,eAAe,CAAC,IAAI,iBAAiB;AAE9G,QAAI,QAAQ;AACZ,QAAI,aAAa;AAEjB,QAAI,uBAAuB,KAAK;AACtB,cAAA;AACK,mBAAA;AAAA,IACJ,WAAA,sBAAsB,iBAAiB,SAAS,KAAK;AACtD,cAAA;AACK,mBAAA;AAAA,IAAA,OACR;AACG,cAAA;AACR,mBAAa,KAAK,IAAI,KAAK,IAAI,oBAAoB;AAAA,IAAA;AAGrD,WAAO,EAAE,OAAO,YAAY,SAAS,EAAE,qBAAqB,uBAAuB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMrF,gCAAgC;AACxB,UAAA,oBAAoB,KAAK,UAAU;AACzC,UAAM,iBAAiB,kBAAkB,OAAO,OAAK,EAAE,kBAAkB,gBAAgB,EAAE;AAC3F,UAAM,iBAAiB,kBAAkB,OAAO,OAAK,EAAE,kBAAkB,gBAAgB,EAAE;AAE3F,QAAI,QAAQ;AACZ,QAAI,aAAa;AAEb,QAAA,mBAAmB,KAAK,mBAAmB,GAAG;AACxC,cAAA;AACK,mBAAA;AAAA,IACJ,WAAA,iBAAiB,KAAK,iBAAiB,GAAG;AAC3C,cAAA;AACK,mBAAA;AAAA,IAAA;AAGf,WAAO,EAAE,OAAO,YAAY,SAAS,EAAE,gBAAgB,iBAAiB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ1E,6BAA6B;AAC3B,UAAM,YAAY,OAAO,OAAO,KAAK,UAAU,cAAc,EAAE,KAAK;AACpE,UAAM,aAAa,CAAC;AAEpB,cAAU,QAAQ,CAASA,WAAA;AACzB,iBAAWA,OAAM,SAAS,KAAK,WAAWA,OAAM,SAAS,KAAK,KAAK;AAAA,IAAA,CACpE;AAEM,WAAA,OAAO,QAAQ,UAAU,EAAE;AAAA,MAAO,CAAC,KAAK,CAAC,MAAM,KAAK,MACzD,QAAQ,IAAI,QAAQ,EAAE,MAAM,MAAU,IAAA;AAAA,MACtC,EAAE,MAAM,MAAM,OAAO,EAAE;AAAA,IACzB;AAAA,EAAA;AAAA,EAGF,kCAAkC;AAChC,UAAM,YAAY,OAAO,OAAO,KAAK,UAAU,cAAc,EAAE,KAAK;AAChE,QAAA,UAAU,WAAW,EAAU,QAAA;AAE5B,WAAA,UAAU,OAAO,CAAC,KAAKA,WAAU,MAAMA,OAAM,UAAU,CAAC,IAAI,UAAU;AAAA,EAAA;AAAA,EAG/E,wBAAwB;AACtB,UAAM,YAAY,OAAO,OAAO,KAAK,UAAU,cAAc,EAAE,KAAK;AACpE,UAAM,aAAa,CAAC;AAEpB,cAAU,QAAQ,CAASA,WAAA;AACzB,iBAAWA,OAAM,SAAS,KAAK,WAAWA,OAAM,SAAS,KAAK,KAAK;AAAA,IAAA,CACpE;AAEM,WAAA;AAAA,EAAA;AAAA,EAGT,kCAAkC;AAChC,QAAI,KAAK,UAAU,eAAe,WAAW,EAAU,QAAA;AAEvD,WAAO,KAAK,UAAU,eAAe,OAAO,CAAC,KAAKA,WAAU,MAAMA,OAAM,UAAU,CAAC,IAC5E,KAAK,UAAU,eAAe;AAAA,EAAA;AAAA,EAGvC,kCAAkC;AAChC,UAAM,YAAY,CAAC;AAEf,QAAA,CAAC,KAAK,UAAU,kBAAkB,KAAK,UAAU,eAAe,WAAW,GAAG;AAChF,aAAO,CAAC;AAAA,IAAA;AAGL,SAAA,UAAU,eAAe,QAAQ,CAASA,WAAA;AAC7C,UAAIA,OAAM,kBAAkB,MAAM,QAAQA,OAAM,cAAc,GAAG;AACzD,QAAAA,OAAA,eAAe,QAAQ,CAAM,OAAA;AACjC,oBAAU,GAAG,QAAQ,KAAK,UAAU,GAAG,QAAQ,KAAK,KAAK;AAAA,QAAA,CAC1D;AAAA,MAAA,WACQA,OAAM,eAAe;AAE9B,cAAM,WAAWA,OAAM;AACvB,kBAAU,QAAQ,KAAK,UAAU,QAAQ,KAAK,KAAK;AAAA,MAAA;AAAA,IACrD,CACD;AAED,WAAO,OAAO,QAAQ,SAAS,EAC5B,KAAK,CAAC,GAAE,CAAC,GAAG,CAAA,EAAE,CAAC,MAAM,IAAI,CAAC,EAC1B,MAAM,GAAG,CAAC,EACV,IAAI,CAAC,CAAC,UAAU,KAAK,OAAO,EAAE,UAAU,SAAS,QAAQ,GAAG,MAAQ,EAAA;AAAA,EAAA;AAAA,EAGzE,qCAAqC;AACnC,QAAI,KAAK,UAAU,wBAAwB,WAAW,EAAU,QAAA;AAEhE,WAAO,KAAK,UAAU,wBAAwB,OAAO,CAAC,KAAKA,WAAU,MAAMA,OAAM,UAAU,CAAC,IACrF,KAAK,UAAU,wBAAwB;AAAA,EAAA;AAAA,EAGhD,+BAA+B;AAC7B,UAAM,iBAAiB,CAAC;AAEnB,SAAA,UAAU,wBAAwB,QAAQ,CAASA,WAAA;AACtD,qBAAeA,OAAM,aAAa,KAAK,eAAeA,OAAM,aAAa,KAAK,KAAK;AAAA,IAAA,CACpF;AAEM,WAAA;AAAA,EAAA;AAAA,EAGT,4BAA4B;AAC1B,QAAI,KAAK,UAAU,yBAAyB,WAAW,EAAU,QAAA;AAEjE,WAAO,KAAK,UAAU,yBAAyB,OAAO,CAAC,KAAKA,WAAU,MAAMA,OAAM,eAAe,CAAC,IAC3F,KAAK,UAAU,yBAAyB;AAAA,EAAA;AAAA,EAGjD,0BAA0B;AACxB,UAAM,aAAa,CAAC;AAEf,SAAA,UAAU,yBAAyB,QAAQ,CAASA,WAAA;AACvD,iBAAWA,OAAM,kBAAkB,KAAK,WAAWA,OAAM,kBAAkB,KAAK,KAAK;AAAA,IAAA,CACtF;AAEM,WAAA;AAAA,EAAA;AAAA,EAGT,6BAA6B;AACpB,WAAA;AAAA,MACL,gBAAgB,KAAK,wBAAwB;AAAA,MAC7C,eAAe,KAAK,2BAA2B;AAAA,MAC/C,kBAAkB,KAAK,0BAA0B;AAAA,MACjD,kBAAkB,KAAK,0BAA0B;AAAA,IACnD;AAAA,EAAA;AAAA,EAGF,0BAA0B;AAClB,UAAA,gBAAgB,KAAK,sBAAsB,EAAE;AAC/C,QAAA,cAAc,SAAS,EAAU,QAAA;AAErC,UAAM,YAAY,cAAc,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC;AAC3E,UAAA,aAAa,cAAc,MAAM,CAAC,EAAE,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC;AAE3E,QAAA,cAAc,EAAU,QAAA;AAC5B,WAAO,KAAK,IAAI,IAAI,YAAY,cAAc,SAAS;AAAA,EAAA;AAAA,EAGzD,6BAA6B;AACrB,UAAA,YAAY,KAAK,aAAa;AACpC,QAAI,gBAAgB;AACpB,QAAI,cAAc;AAElB,aAAS,IAAI,GAAG,IAAI,UAAU,SAAS,GAAG,KAAK;AAC7C;AACI,UAAA,UAAU,IAAI,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,UAAU;AACrD;AAAA,MAAA;AAAA,IACF;AAGK,WAAA,cAAc,IAAI,gBAAgB,cAAc;AAAA,EAAA;AAAA,EAGzD,4BAA4B;AACpB,UAAA,qBAAqB,KAAK,2BAA2B;AAC3D,UAAM,oBAAoB,OAAO,OAAO,kBAAkB,EACvD,OAAO,CAAC,KAAK,eAAe,MAAM,WAAW,YAAY,CAAC,IAC3D,OAAO,KAAK,kBAAkB,EAAE;AAE3B,WAAA;AAAA,EAAA;AAAA,EAGT,4BAA4B;AACpB,UAAA,YAAY,KAAK,aAAa;AAChC,QAAA,UAAU,SAAS,EAAU,QAAA;AAEjC,UAAM,aAAa,UAAU,IAAI,CAAK,MAAA,EAAE,YAAY,CAAC;AAC/C,UAAA,OAAO,WAAW,OAAO,CAAC,KAAK,MAAM,MAAM,GAAG,CAAC,IAAI,WAAW;AACpE,UAAM,WAAW,WAAW,OAAO,CAAC,KAAK,MAAM,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,WAAW;AACtF,UAAA,oBAAoB,KAAK,KAAK,QAAQ;AAE5C,WAAO,KAAK,IAAI,GAAG,IAAK,qBAAqB,OAAO,IAAK;AAAA,EAAA;AAAA;AAAA,EAI3D,oBAAoB,UAAU,WAAW;AACvC,YAAQ,KAAK,0CAA0C,QAAQ,KAAK,SAAS,GAAG;AAAA,EAAA;AAAA,EAGlF,qBAAqB;AACb,UAAA,iBAAiB,OAAO,OAAO,KAAK,UAAU,cAAc,EAAE,OAAO;AACpE,WAAA,iBACA,KAAK,UAAU,eAAe,SAC9B,KAAK,UAAU,wBAAwB,SACvC,KAAK,UAAU,yBAAyB;AAAA,EAAA;AAAA,EAGjD,6BAA6B;AACpB,WAAA,OAAO,QAAQ,KAAK,UAAU,gBAAgB,EAClD,OAAO,CAAC,CAAC,KAAK,MAAM,MAAM,OAAO,UAAU,KAAK,gBAAgB,UAAU,EAC1E,IAAI,CAAC,CAAC,KAAK,MAAM,OAAO;AAAA,MACvB,SAAS;AAAA,MACT,WAAW,OAAO;AAAA,MAClB,UAAU,OAAO,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,UAAU,CAAC,IAAI,OAAO;AAAA,IAAA,EAClE;AAAA,EAAA;AAAA,EAGN,yBAAyB;AACjB,UAAA,eAAe,KAAK,oBAAoB,GAAM;AACpD,UAAM,cAAc,KAAK,oBAAoB,KAAQ,GAAM;AAEpD,WAAA;AAAA,MACL,gBAAgB,YAAY,SAAS,KAClC,YAAY,SAAS,aAAa,UAAU,YAAY,SAAS;AAAA,MACpE,kBAAkB,KAAK,0BAA0B;AAAA,MACjD,cAAc,KAAK,sBAAsB;AAAA,IAC3C;AAAA,EAAA;AAAA,EAGF,sCAAsC;AACpC,UAAM,kBAAkB,CAAC;AACnB,UAAA,qBAAqB,KAAK,2BAA2B;AAGpD,WAAA,QAAQ,kBAAkB,EAAE,QAAQ,CAAC,CAAC,SAAS,UAAU,MAAM;AAChE,UAAA,WAAW,aAAa,KAAK;AAC/B,wBAAgB,KAAK;AAAA,UACnB,MAAM,GAAG,OAAO;AAAA,UAChB,UAAU,WAAW,aAAa,MAAM,SAAS;AAAA,UACjD,aAAa,mCAAmC,OAAO;AAAA,UACvD,SAAS,WAAW;AAAA,QAAA,CACrB;AAAA,MAAA;AAAA,IACH,CACD;AAGK,UAAA,qBAAqB,KAAK,2BAA2B;AAC3D,uBAAmB,QAAQ,CAAW,YAAA;AACpC,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU,QAAQ,WAAW,MAAM,SAAS;AAAA,QAC5C,aAAa,6BAA6B,QAAQ,OAAO;AAAA,QACzD,WAAW,QAAQ;AAAA,MAAA,CACpB;AAAA,IAAA,CACF;AAEM,WAAA;AAAA,EAAA;AAAA,EAGT,2BAA2B;AACnB,UAAA,YAAY,KAAK,aAAa;AAChC,QAAA,UAAU,WAAW,EAAU,QAAA;AAE7B,UAAA,gBAAgB,UAAU,OAAO,CAAC,KAAKA,WAAU,OAAOA,OAAM,YAAY,IAAI,CAAC;AACrF,WAAO,gBAAgB,UAAU;AAAA,EAAA;AAAA,EAGnC,4BAA4B;AACpB,UAAA,eAAe,KAAK,sBAAsB,CAAC;AAC7C,QAAA,aAAa,SAAS,EAAU,QAAA;AAEpC,QAAI,eAAe;AACnB,aAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,UAAI,aAAa,CAAC,IAAI,aAAa,IAAE,CAAC,EAAG;AAAA,IAAA;AAGpC,WAAA,gBAAgB,aAAa,SAAS;AAAA,EAAA;AAAA,EAG/C,wBAAwB;AACtB,UAAM,YAAY,KAAK,IAAI,IAAI,KAAK;AAC9B,UAAA,cAAc,KAAK,mBAAmB;AAExC,QAAA,cAAc,EAAU,QAAA;AAC5B,WAAO,KAAK,IAAI,GAAG,IAAK,eAAe,YAAY,IAAO;AAAA,EAAA;AAAA,EAG5D,eAAe;AACb,UAAM,iBAAiB,OAAO,OAAO,KAAK,UAAU,cAAc,EAAE,KAAK;AAClE,WAAA;AAAA,MACL,GAAG;AAAA,MACH,GAAG,KAAK,UAAU;AAAA,MAClB,GAAG,KAAK,UAAU;AAAA,MAClB,GAAG,KAAK,UAAU;AAAA,IACpB;AAAA,EAAA;AAAA,EAGF,oBAAoB,UAAU,WAAW,GAAG;AACpC,UAAA,MAAM,KAAK,IAAI;AACf,UAAA,YAAY,MAAM,WAAW;AACnC,UAAM,UAAU,MAAM;AAEtB,WAAO,KAAK,aAAA,EAAe,OAAO,CAASA,WAAA;AACzC,YAAM,YAAY,IAAI,KAAKA,OAAM,SAAS,EAAE,QAAQ;AAC7C,aAAA,aAAa,aAAa,aAAa;AAAA,IAAA,CAC/C;AAAA,EAAA;AAAA,EAGH,sBAAsB,QAAQ;AAC5B,UAAM,kBAAkB,KAAK,IAAI,IAAI,KAAK;AAC1C,UAAM,gBAAgB,kBAAkB;AACxC,UAAM,cAAc,CAAC;AAErB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AACzB,YAAA,aAAa,KAAK,mBAAoB,IAAI;AAChD,YAAM,WAAW,aAAa;AAE9B,YAAM,gBAAgB,KAAK,aAAa,EAAE,OAAO,CAASA,WAAA;AACxD,cAAM,YAAY,IAAI,KAAKA,OAAM,SAAS,EAAE,QAAQ;AAC7C,eAAA,aAAa,cAAc,YAAY;AAAA,MAAA,CAC/C;AAEW,kBAAA,KAAK,cAAc,MAAM;AAAA,IAAA;AAGhC,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,QAAQ;AACN,SAAK,YAAY;AAAA,MACf,gBAAgB,CAAC;AAAA,MACjB,gBAAgB,CAAC;AAAA,MACjB,yBAAyB,CAAC;AAAA,MAC1B,0BAA0B,CAAC;AAAA,MAC3B,kBAAkB,CAAC;AAAA,MACnB,eAAe,CAAC;AAAA,MAChB,oBAAoB,CAAC;AAAA,MACrB,sBAAsB,CAAA;AAAA,IACxB;AACK,SAAA,mBAAmB,KAAK,IAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMnC,aAAa;AACJ,WAAA;AAAA,MACL,GAAG,KAAK;AAAA,MACR,iBAAiB,KAAK,IAAI,IAAI,KAAK;AAAA,MACnC,UAAU,KAAK,qBAAqB;AAAA,MACpC,SAAS,KAAK,qBAAqB;AAAA,IACrC;AAAA,EAAA;AAEJ;AC16BO,MAAM,0BAA0B;AAAA,EACrC,cAAc;AACZ,SAAK,uBAAuB;AAAA,MAC1B,WAAW;AAAA;AAAA,MACX,MAAM;AAAA;AAAA,MACN,SAAS;AAAA;AAAA,MACT,MAAM;AAAA;AAAA,MACN,UAAU;AAAA;AAAA,IACZ;AAEA,SAAK,mBAAmB;AAAA,MACtB,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,qBAAqB;AAAA,MACrB,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,IACrB;AAEA,SAAK,wBAAwB;AAAA,MAC3B,MAAM;AAAA;AAAA,MACN,QAAQ;AAAA;AAAA,MACR,MAAM;AAAA;AAAA,IACR;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,QAAQ,MAAM;AACL,WAAA,KAAK,QAAQ,IAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,MAAM,QAAQ,MAAM;AAClB,QAAI,CAAC,QAAQ,CAAC,KAAK,kBAAkB;AACnC,cAAQ,KAAK,gEAAgE;AAC7E,aAAO,KAAK,mBAAmB;AAAA,IAAA;AAGjC,UAAM,iBAAiB,KAAK;AAGtB,UAAA,qBAAqB,KAAK,4BAA4B,cAAc;AAGpE,UAAA,gBAAgB,KAAK,qBAAqB,cAAc;AAGxD,UAAA,mBAAmB,KAAK,wBAAwB,cAAc;AAG9D,UAAA,uBAAuB,KAAK,2BAA2B,cAAc;AAGrE,UAAA,kBAAkB,KAAK,sBAAsB,cAAc;AAG3D,UAAA,aAAa,KAAK,oBAAoB,cAAc;AAE1D,UAAM,WAAW;AAAA,MACf,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,WAAW;AAAA,MACX,SAAS;AAAA;AAAA,MAGT;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAGA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAGA,iBAAiB;AAAA,QACf,kBAAkB,KAAK,uBAAuB,cAAc;AAAA,QAC5D,wBAAwB,KAAK,6BAA6B,cAAc;AAAA,QACxE,qBAAqB,KAAK,0BAA0B,cAAc;AAAA,QAClE,oBAAoB,KAAK,yBAAyB,cAAc;AAAA,QAChE,mBAAmB,KAAK,wBAAwB,cAAc;AAAA,MAChE;AAAA;AAAA,MAGA,iBAAiB,KAAK,wBAAwB,gBAAgB,UAAU;AAAA;AAAA,MAGxE,UAAU;AAAA,QACR,eAAe,eAAe,UAAU;AAAA,QACxC,eAAe,eAAe,OAAO,aAAW,QAAQ,OAAO,EAAE,UAAU;AAAA,QAC3E,cAAc,KAAK,sBAAsB,cAAc;AAAA,QACvD,UAAU,KAAK,kBAAkB,cAAc;AAAA,QAC/C,UAAU,KAAK,kBAAkB,cAAc;AAAA,MAAA;AAAA,IAEnD;AAEO,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,4BAA4B,MAAM;AAChC,QAAI,CAAC,QAAQ,KAAK,WAAW,EAAU,QAAA;AAEvC,QAAI,qBAAqB;AAEzB,SAAK,QAAQ,CAAW,YAAA;AAClB,UAAA,QAAQ,gBAAgB,QAAQ,aAAa;AAC/C,cAAMA,SAAQ,KAAK,IAAI,QAAQ,eAAe,QAAQ,WAAW;AACjE,cAAM,YAAY,KAAK,sBAAsB,QAAQ,UAAU,KAAK;AAEpE,YAAIA,UAAS,WAAW;AACtB;AAAA,QAAA;AAAA,MACF;AAAA,IACF,CACD;AAED,WAAO,KAAK,SAAS,IAAI,qBAAqB,KAAK,SAAS;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM9D,qBAAqB,MAAM;AACzB,UAAM,SAAS,CAAC;AAChB,UAAM,SAAS;AAAA,MACb,gBAAgB;AAAA;AAAA,MAChB,iBAAiB;AAAA;AAAA,MACjB,YAAY;AAAA;AAAA,MACZ,QAAQ;AAAA;AAAA,IACV;AAEA,SAAK,QAAQ,CAAW,YAAA;AAClB,UAAA,QAAQ,gBAAgB,QAAQ,aAAa;AACzC,cAAAA,SAAQ,QAAQ,eAAe,QAAQ;AAC7C,eAAO,KAAKA,MAAK;AAEb,YAAAA,SAAQ,EAAU,QAAA;AAAA,iBACbA,SAAQ,EAAU,QAAA;AAAA,MAAA;AAAA,IAC7B,CACD;AAGD,UAAM,gBAAgB,KAAK;AAC3B,QAAI,gBAAgB,GAAG;AACf,YAAA,qBAAqB,OAAO,iBAAiB;AAC7C,YAAA,sBAAsB,OAAO,kBAAkB;AAErD,aAAO,aAAa,KAAK,IAAI,oBAAoB,mBAAmB,IAAI;AACxE,aAAO,SAAS,KAAK,IAAI,qBAAqB,mBAAmB,IAAI;AAAA,IAAA;AAGhE,WAAA;AAAA,MACL,cAAc,OAAO,SAAS,IAAI,OAAO,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,OAAO,SAAS;AAAA,MACtF,eAAe,KAAK,kBAAkB,MAAM;AAAA,MAC5C;AAAA,MACA,mBAAmB,KAAK,yBAAyB,MAAM;AAAA,IACzD;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,wBAAwB,MAAM;AACxB,QAAA,KAAK,SAAS,EAAG,QAAO,EAAE,OAAO,qBAAqB,aAAa,EAAE;AAEnE,UAAA,YAAY,KAAK,MAAM,GAAG,KAAK,MAAM,KAAK,SAAS,CAAC,CAAC;AACrD,UAAA,aAAa,KAAK,MAAM,KAAK,MAAM,KAAK,SAAS,CAAC,CAAC;AAEnD,UAAA,gBAAgB,KAAK,4BAA4B,SAAS;AAC1D,UAAA,iBAAiB,KAAK,4BAA4B,UAAU;AAElE,UAAM,cAAc,iBAAiB;AAErC,QAAI,QAAQ;AACR,QAAA,cAAc,IAAa,SAAA;AAAA,aACtB,cAAc,KAAc,SAAA;AAE9B,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,kBAAkB,KAAK,qBAAqB,IAAI;AAAA,IAClD;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,2BAA2B,MAAM;AAC/B,UAAM,aAAa;AAAA,MACjB,gBAAgB;AAAA;AAAA,MAChB,cAAc;AAAA;AAAA,MACd,WAAW;AAAA;AAAA,MACX,oBAAoB;AAAA;AAAA,IACtB;AAGA,UAAM,mBAAmB,KAAK;AAAA,MAAO,CACnC,YAAA,QAAQ,gBAAgB,QAAQ,eAAe,MAAM;AAAA,IAAA,EACrD,SAAS,KAAK;AAEhB,eAAW,eAAe,mBAAmB;AAGvC,UAAA,YAAY,KAAK,IAAI,CAAA,YAAW,QAAQ,YAAY,EAAE,OAAO,OAAO;AACpE,UAAA,qBAAqB,KAAK,eAAe,SAAS;AACxD,UAAM,gBAAgB,UAAU,OAAO,CAAA,QAAO,KAAK,IAAI,MAAM,kBAAkB,KAAK,CAAC,EAAE,SAAS,UAAU;AAE1G,eAAW,YAAY,gBAAgB;AAEhC,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,sBAAsB,MAAM;AAEpB,UAAA,gBAAgB,KAAK,IAAI,CAAA,YAAW,QAAQ,YAAY,EAAE,OAAO,OAAO;AACxE,UAAA,cAAc,cAAc,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,cAAc,UAAU;AAEjF,UAAA,cAAc,KAAK,qBAAqB,IAAI;AAClD,UAAM,iBAAiB,cAAc,MAAO,MAAM,cAAc,MAAO,MAAM;AAE7E,YAAQ,cAAc,kBAAkB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM1C,oBAAoB,MAAM;AAClB,UAAA,WAAW,KAAK,4BAA4B,IAAI;AAChD,UAAA,cAAc,KAAK,qBAAqB,IAAI;AAC5C,UAAA,aAAa,KAAK,sBAAsB,IAAI;AAElD,WAAQ,WAAW,MAAQ,cAAc,MAAQ,aAAa;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMhE,uBAAuB,MAAM;AAE3B,UAAM,iBAAiB,KAAK,OAAO,CAAW,YAAA,QAAQ,YAAY,GAAI;AAC/D,WAAA,KAAK,4BAA4B,cAAc;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMxD,6BAA6B,MAAM;AAEjC,UAAM,qBAAqB,KAAK,OAAO,CAAC,OAAO,YAAY;AACrD,UAAA,QAAQ,gBAAgB,QAAQ,aAAa;AAC/C,cAAMA,SAAQ,KAAK,IAAI,QAAQ,eAAe,QAAQ,WAAW;AAC3D,cAAA,gBAAgBA,SAAQ,QAAQ;AACtC,eAAO,QAAQ,KAAK,IAAI,GAAG,IAAI,aAAa;AAAA,MAAA;AAEvC,aAAA;AAAA,OACN,CAAC;AAEJ,WAAO,KAAK,SAAS,IAAI,qBAAqB,KAAK,SAAS;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM9D,0BAA0B,MAAM;AAExB,UAAA,aAAa,KAAK,IAAI,CAAA,YAAW,QAAQ,WAAW,EAAE,OAAO,OAAO;AAC1E,UAAM,mBAAmB,CAAC,GAAG,IAAI,IAAI,UAAU,CAAC;AAE5C,QAAA,iBAAiB,SAAS,EAAU,QAAA;AAGxC,UAAM,yBAAyB,CAAC;AAChC,qBAAiB,QAAQ,CAAa,cAAA;AACpC,YAAM,uBAAuB,KAAK,OAAO,CAAW,YAAA,QAAQ,gBAAgB,SAAS;AACrF,6BAAuB,SAAS,IAAI,KAAK,4BAA4B,oBAAoB;AAAA,IAAA,CAC1F;AAEK,UAAA,eAAe,OAAO,OAAO,sBAAsB;AACnD,UAAA,qBAAqB,aAAa,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,aAAa;AAE3E,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,yBAAyB,MAAM;AAE7B,UAAM,iBAAiB,KAAK,OAAO,CAAW,YAAA,QAAQ,eAAe,GAAI;AAClE,WAAA,KAAK,4BAA4B,cAAc;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMxD,wBAAwB,MAAM;AAErB,WAAA,KAAK,4BAA4B,IAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM9C,wBAAwB,MAAM,YAAY;AACxC,UAAM,kBAAkB,CAAC;AAEzB,QAAI,aAAa,KAAK;AACpB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,YAAY,CAAC,0BAA0B,uBAAuB;AAAA,MAAA,CAC/D;AAAA,IAAA;AAGG,UAAA,gBAAgB,KAAK,qBAAqB,IAAI;AACpD,QAAI,cAAc,OAAO,iBAAiB,cAAc,OAAO,iBAAiB;AAC9E,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,YAAY,CAAC,uBAAuB,kBAAkB;AAAA,MAAA,CACvD;AAAA,IAAA;AAGH,QAAI,KAAK,sBAAsB,IAAI,IAAI,KAAK;AAC1C,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,YAAY,CAAC,oBAAoB,oBAAoB;AAAA,MAAA,CACtD;AAAA,IAAA;AAGI,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,kBAAkB,SAAS;AACrB,QAAA,QAAQ,WAAW,EAAU,QAAA;AAC3B,UAAA,OAAO,QAAQ,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,QAAQ;AAC1D,UAAM,WAAW,QAAQ,OAAO,CAAC,KAAK,QAAQ,MAAM,KAAK,IAAI,MAAM,MAAM,CAAC,GAAG,CAAC,IAAI,QAAQ;AACnF,WAAA;AAAA,EAAA;AAAA,EAGT,qBAAqB,MAAM;AACnB,UAAA,SAAS,KAAK,IAAI,CAAW,YAAA;AAC7B,UAAA,QAAQ,gBAAgB,QAAQ,aAAa;AAC/C,eAAO,KAAK,IAAI,QAAQ,eAAe,QAAQ,WAAW;AAAA,MAAA;AAErD,aAAA;AAAA,IAAA,CACR,EAAE,OAAO,OAAO;AAEb,QAAA,OAAO,WAAW,EAAU,QAAA;AAE1B,UAAA,WAAW,KAAK,kBAAkB,MAAM;AAC9C,WAAO,KAAK,IAAI,GAAG,IAAK,WAAW,EAAG;AAAA,EAAA;AAAA,EAGxC,yBAAyB,QAAQ;AAC/B,UAAM,eAAe,EAAE,UAAU,GAAG,MAAM,GAAG,UAAU,EAAE;AAEzD,WAAO,QAAQ,CAASA,WAAA;AAClB,UAAAA,SAAQ,EAAgB,cAAA;AAAA,eACnBA,WAAU,EAAgB,cAAA;AAAA,UACjB,cAAA;AAAA,IAAA,CACnB;AAEM,WAAA;AAAA,EAAA;AAAA,EAGT,eAAe,OAAO;AACpB,UAAM,YAAY,CAAC;AACb,UAAA,QAAQ,UAAQ,UAAU,IAAI,KAAK,UAAU,IAAI,KAAK,KAAK,CAAC;AAClE,WAAO,OAAO,KAAK,SAAS,EAAE,OAAO,CAAC,GAAG,MAAM,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,GAAG,CAAC;AAAA,EAAA;AAAA,EAGvF,sBAAsB,MAAM;AACpB,UAAA,SAAS,KAAK,IAAI,CAAW,YAAA;AAC7B,UAAA,QAAQ,gBAAgB,QAAQ,aAAa;AAC/C,eAAO,KAAK,IAAI,QAAQ,eAAe,QAAQ,WAAW;AAAA,MAAA;AAErD,aAAA;AAAA,IAAA,CACR,EAAE,OAAO,OAAO;AAEjB,WAAO,OAAO,SAAS,IAAI,OAAO,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,OAAO,SAAS;AAAA,EAAA;AAAA,EAGjF,kBAAkB,MAAM;AAChB,UAAA,SAAS,KAAK,IAAI,CAAW,YAAA;AAC7B,UAAA,QAAQ,gBAAgB,QAAQ,aAAa;AAC/C,eAAO,KAAK,IAAI,QAAQ,eAAe,QAAQ,WAAW;AAAA,MAAA;AAErD,aAAA;AAAA,IAAA,CACR,EAAE,OAAO,OAAO;AAEjB,WAAO,OAAO,SAAS,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI;AAAA,EAAA;AAAA,EAGnD,kBAAkB,MAAM;AAChB,UAAA,SAAS,KAAK,IAAI,CAAW,YAAA;AAC7B,UAAA,QAAQ,gBAAgB,QAAQ,aAAa;AAC/C,eAAO,KAAK,IAAI,QAAQ,eAAe,QAAQ,WAAW;AAAA,MAAA;AAErD,aAAA;AAAA,IAAA,CACR,EAAE,OAAO,OAAO;AAEjB,WAAO,OAAO,SAAS,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI;AAAA,EAAA;AAAA,EAGnD,qBAAqB;AACZ,WAAA;AAAA,MACL,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,WAAW;AAAA,MACX,SAAS;AAAA,MACT,oBAAoB;AAAA,MACpB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,eAAe,EAAE,cAAc,GAAG,eAAe,GAAG,QAAQ,GAAG;AAAA,MAC/D,kBAAkB,EAAE,OAAO,qBAAqB,aAAa,EAAE;AAAA,MAC/D,sBAAsB,CAAC;AAAA,MACvB,iBAAiB,CAAC;AAAA,MAClB,iBAAiB,CAAC;AAAA,MAClB,UAAU,EAAE,eAAe,GAAG,eAAe,EAAE;AAAA,IACjD;AAAA,EAAA;AAEJ;ACrbO,MAAM,0BAA0B;AAAA,EACrC,cAAc;AACZ,SAAK,qBAAqB;AAAA,MACxB,WAAW;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAEA,SAAK,gBAAgB;AAAA,MACnB,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,SAAS;AAAA,IACX;AAEA,SAAK,kBAAkB;AAAA,MACrB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,QAAQ,MAAM;AACL,WAAA,KAAK,QAAQ,IAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,MAAM,QAAQ,MAAM;AAClB,QAAI,CAAC,QAAQ,CAAC,KAAK,oBAAoB;AACrC,cAAQ,KAAK,+DAA+D;AAC5E,aAAO,KAAK,mBAAmB;AAAA,IAAA;AAGjC,UAAM,eAAe,KAAK;AAGpB,UAAA,iBAAiB,KAAK,sBAAsB,YAAY;AAGxD,UAAA,oBAAoB,KAAK,wBAAwB,YAAY;AAG7D,UAAA,sBAAsB,KAAK,0BAA0B,YAAY;AAGjE,UAAA,mBAAmB,KAAK,wBAAwB,YAAY;AAG5D,UAAA,gBAAgB,KAAK,qBAAqB,YAAY;AAGtD,UAAA,kBAAkB,KAAK,sBAAsB,YAAY;AAGzD,UAAA,uBAAuB,KAAK,8BAA8B,YAAY;AAE5E,UAAM,WAAW;AAAA,MACf,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,WAAW;AAAA,MACX,SAAS;AAAA;AAAA,MAGT,iBAAiB,KAAK,yBAAyB,YAAY;AAAA,MAC3D;AAAA,MACA;AAAA;AAAA,MAGA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAGA,iBAAiB;AAAA,QACf,oBAAoB,KAAK,yBAAyB,YAAY;AAAA,QAC9D,kBAAkB,KAAK,uBAAuB,YAAY;AAAA,QAC1D,kBAAkB,KAAK,uBAAuB,YAAY;AAAA,QAC1D,kBAAkB,KAAK,uBAAuB,YAAY;AAAA,QAC1D,eAAe,KAAK,oBAAoB,YAAY;AAAA,MACtD;AAAA;AAAA,MAGA,oBAAoB,KAAK,6BAA6B,YAAY;AAAA;AAAA,MAGlE,iBAAiB,KAAK,wBAAwB,cAAc,oBAAoB;AAAA;AAAA,MAGhF,UAAU;AAAA,QACR,gBAAgB,aAAa,UAAU;AAAA,QACvC,aAAa,KAAK,iBAAiB,YAAY;AAAA,QAC/C,eAAe,KAAK,uBAAuB,YAAY;AAAA,QACvD,wBAAwB,KAAK,8BAA8B,YAAY;AAAA,MAAA;AAAA,IAE3E;AAEO,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,sBAAsB,MAAM;AAC1B,UAAM,iBAAiB,CAAC;AAGlB,UAAA,cAAc,KAAK,oBAAoB,IAAI;AAEjD,WAAO,KAAK,WAAW,EAAE,QAAQ,CAAQ,SAAA;AACjC,YAAA,WAAW,YAAY,IAAI;AACjC,YAAM,UAAU,SAAS,OAAO,CAAW,YAAA,QAAQ,SAAS,EAAE;AAC9D,qBAAe,IAAI,IAAI;AAAA,QACrB,UAAU,SAAS,SAAS,IAAI,UAAU,SAAS,SAAS;AAAA,QAC5D,UAAU,SAAS;AAAA,QACnB,aAAa,KAAK,qBAAqB,QAAQ;AAAA,QAC/C,YAAY,KAAK,qBAAqB,QAAQ;AAAA,MAChD;AAAA,IAAA,CACD;AAEM,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,wBAAwB,MAAM;AAC5B,UAAM,aAAa;AAAA,MACjB,sBAAsB;AAAA;AAAA,MACtB,oBAAoB;AAAA;AAAA,MACpB,eAAe;AAAA;AAAA,MACf,iBAAiB;AAAA;AAAA,MACjB,eAAe;AAAA;AAAA,IACjB;AAGA,UAAM,eAAe,KAAK;AAAA,MAAO,CAC/B,YAAA,QAAQ,aAAa,QAAQ,eAAe;AAAA,IAAA,EAC5C;AACS,eAAA,uBAAuB,eAAe,KAAK,SAAS;AAG/D,UAAM,qBAAqB,KAAK;AAAA,MAAO,aACrC,QAAQ,gBAAgB,OAAQ,QAAQ,gBAAgB,OAAQ,QAAQ;AAAA,IAAA,EACxE;AACS,eAAA,qBAAqB,qBAAqB,KAAK,SAAS;AAGxD,eAAA,gBAAgB,KAAK,2BAA2B,IAAI;AAExD,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,0BAA0B,MAAM;AACxB,UAAA,mBAAmB,KAAK,IAAI,CAAW,YAAA;AAC3C,UAAI,aAAa;AAGjB,UAAI,QAAQ,cAAc;AACxB,gBAAQ,QAAQ,cAAc;AAAA,UAC5B,KAAK;AAAA,UACL,KAAK;AACW,0BAAA;AACd;AAAA,UACF,KAAK;AAAA,UACL,KAAK;AACW,0BAAA;AACd;AAAA,UACF,KAAK;AAAA,UACL,KAAK;AACW,0BAAA;AACd;AAAA,UACF,KAAK;AAAA,UACL,KAAK;AACW,0BAAA;AACd;AAAA,UACF;AACgB,0BAAA;AAAA,QAAA;AAAA,MAClB;AAIF,UAAI,QAAQ,gBAAgB;AAC1B,sBAAc,KAAK,IAAI,GAAG,QAAQ,iBAAiB,CAAC;AAAA,MAAA;AAItD,UAAI,QAAQ,WAAW;AACP,sBAAA,QAAQ,YAAY,KAAK,IAAI;AAAA,MAAA;AAGtC,aAAA;AAAA,QACL;AAAA,QACA;AAAA,QACA,QAAQ,QAAQ;AAAA,MAClB;AAAA,IAAA,CACD;AAEM,WAAA;AAAA,MACL,mBAAmB,iBAAiB,OAAO,CAAC,KAAK,SAAS,MAAM,KAAK,YAAY,CAAC,IAAI,iBAAiB;AAAA,MACvG,yBAAyB,KAAK,+BAA+B,gBAAgB;AAAA,MAC7E,kBAAkB,KAAK,uBAAuB,gBAAgB;AAAA,IAChE;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,wBAAwB,MAAM;AACxB,QAAA,KAAK,SAAS,EAAG,QAAO,EAAE,OAAO,qBAAqB,aAAa,EAAE;AAEzE,UAAM,WAAW,KAAK,mBAAmB,MAAM,CAAC;AAChD,UAAM,oBAAoB,SAAS,IAAI,aAAW,KAAK,kBAAkB,OAAO,CAAC;AAE3E,UAAA,QAAQ,KAAK,eAAe,iBAAiB;AACnD,UAAM,cAAc,kBAAkB,kBAAkB,SAAS,CAAC,IAAI,kBAAkB,CAAC;AAElF,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,KAAK,sBAAsB,iBAAiB;AAAA,MAC1D,WAAW,KAAK,mBAAmB,iBAAiB;AAAA,IACtD;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,qBAAqB,MAAM;AACzB,UAAM,SAAS,KAAK,OAAO,CAAW,YAAA,CAAC,QAAQ,SAAS;AAExD,UAAM,aAAa;AAAA,MACjB,UAAU;AAAA;AAAA,MACV,yBAAyB;AAAA;AAAA,MACzB,kBAAkB;AAAA;AAAA,MAClB,aAAa;AAAA;AAAA,IACf;AAEA,WAAO,QAAQ,CAASA,WAAA;AAClB,UAAAA,OAAM,cAAcA,OAAM,eAAe;AAC3C,cAAM,aAAa,KAAK,IAAIA,OAAM,aAAaA,OAAM,aAAa;AAElE,YAAI,eAAe,GAAG;AACT,qBAAA;AAAA,QAAA,WACF,aAAa,GAAG;AACd,qBAAA;AAAA,QAAA,OACN;AACM,qBAAA;AAAA,QAAA;AAAA,MACb;AAAA,IACF,CACD;AAEM,WAAA;AAAA,MACL,aAAa,OAAO;AAAA,MACpB;AAAA,MACA,WAAW,KAAK,SAAS,IAAI,OAAO,SAAS,KAAK,SAAS;AAAA,MAC3D,gBAAgB,KAAK,uBAAuB,MAAM;AAAA,MAClD,kBAAkB,KAAK,wBAAwB,IAAI;AAAA,IACrD;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,sBAAsB,MAAM;AACpB,UAAA,gBAAgB,KAAK,IAAI,CAAA,YAAW,QAAQ,YAAY,EAAE,OAAO,OAAO;AAE1E,QAAA,cAAc,WAAW,EAAG,QAAO,EAAE,OAAO,WAAW,OAAO,IAAI;AAEhE,UAAA,cAAc,cAAc,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,cAAc;AACvE,UAAA,aAAa,KAAK,gBAAgB,aAAa;AAErD,QAAI,gBAAgB;AACpB,QAAI,aAAa;AAEjB,QAAI,cAAc,KAAM;AACN,sBAAA;AACH,mBAAA;AAAA,IAAA,WACJ,cAAc,KAAM;AACb,sBAAA;AACH,mBAAA;AAAA,IAAA,OACR;AACW,sBAAA;AACH,mBAAA;AAAA,IAAA;AAGR,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa,KAAK,yBAAyB,aAAa;AAAA,IAC1D;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,8BAA8B,MAAM;AAC5B,UAAA,WAAW,KAAK,yBAAyB,IAAI;AACnD,UAAM,QAAQ,KAAK,sBAAsB,IAAI,EAAE;AAC/C,UAAM,aAAa,KAAK,0BAA0B,IAAI,EAAE,oBAAoB;AACtE,UAAA,cAAc,KAAK,qBAAqB,IAAI;AAElD,WAAQ,WAAW,MAAQ,QAAQ,MAAQ,aAAa,MAAQ,cAAc;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMhF,yBAAyB,MAAM;AAE7B,UAAM,eAAe,KAAK;AAAA,MAAO,CAC/B,YAAA,QAAQ,aAAa,QAAQ,eAAe;AAAA,IAAA,EAC5C;AAEF,WAAO,KAAK,SAAS,IAAI,eAAe,KAAK,SAAS;AAAA,EAAA;AAAA,EAGxD,uBAAuB,MAAM;AAE3B,UAAM,mBAAmB,KAAK;AAAA,MAAO,CAAA,YACnC,QAAQ,gBACR,CAAC,eAAe,UAAU,aAAa,eAAe,EAAE,SAAS,QAAQ,YAAY;AAAA,IACvF;AAEO,WAAA,KAAK,kBAAkB,gBAAgB;AAAA,EAAA;AAAA,EAGhD,uBAAuB,MAAM;AAE3B,UAAM,gBAAgB,KAAK;AAAA,MAAO,CAChC,YAAA,QAAQ,kBAAkB,QAAQ,kBAAkB;AAAA,IACtD;AAEO,WAAA,KAAK,kBAAkB,aAAa;AAAA,EAAA;AAAA,EAG7C,uBAAuB,MAAM;AAE3B,UAAM,mBAAmB,KAAK;AAAA,MAAO,CAAA,YACnC,QAAQ,gBACR,CAAC,aAAa,iBAAiB,kBAAkB,EAAE,SAAS,QAAQ,YAAY;AAAA,IAClF;AAEO,WAAA,KAAK,kBAAkB,gBAAgB;AAAA,EAAA;AAAA,EAGhD,oBAAoB,MAAM;AAExB,UAAM,iBAAiB,KAAK,OAAO,CAAC,OAAO,SAAS,UAAU;AACxD,UAAA,UAAU,EAAU,QAAA;AAGxB,YAAM,kBAAkB,KAAK,MAAM,GAAG,KAAK,EAAE;AAAA,QAAO,CAAA,SAClD,KAAK,iBAAiB,QAAQ;AAAA,MAChC;AAEA,UAAI,gBAAgB,SAAS,KAAK,QAAQ,WAAW;AACnD,eAAO,QAAQ;AAAA,MAAA;AAGV,aAAA;AAAA,OACN,CAAC;AAEJ,WAAO,KAAK,SAAS,IAAI,kBAAkB,KAAK,SAAS,KAAK;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMhE,wBAAwB,MAAM,YAAY;AACxC,UAAM,kBAAkB,CAAC;AAEzB,QAAI,aAAa,KAAK;AACpB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,YAAY,CAAC,oBAAoB,gBAAgB;AAAA,MAAA,CAClD;AAAA,IAAA;AAGG,UAAA,iBAAiB,KAAK,sBAAsB,IAAI;AAGtD,WAAO,KAAK,cAAc,EAAE,QAAQ,CAAQ,SAAA;AACtC,UAAA,eAAe,IAAI,EAAE,WAAW,OAAO,eAAe,IAAI,EAAE,YAAY,GAAG;AAC7E,wBAAgB,KAAK;AAAA,UACnB,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS,qCAAqC,KAAK,cAAc,IAAI,KAAK,IAAI;AAAA,UAC9E,YAAY,CAAC,GAAG,IAAI,aAAa,eAAe;AAAA,QAAA,CACjD;AAAA,MAAA;AAAA,IACH,CACD;AAEK,UAAA,kBAAkB,KAAK,sBAAsB,IAAI;AACnD,QAAA,gBAAgB,kBAAkB,QAAQ;AAC5C,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,YAAY,CAAC,mBAAmB,qBAAqB;AAAA,MAAA,CACtD;AAAA,IAAA;AAGI,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,yBAAyB,MAAM;AAC7B,QAAI,CAAC,QAAQ,KAAK,WAAW,EAAU,QAAA;AACvC,UAAM,UAAU,KAAK,OAAO,CAAW,YAAA,QAAQ,SAAS,EAAE;AAC1D,WAAO,UAAU,KAAK;AAAA,EAAA;AAAA,EAGxB,oBAAoB,MAAM;AACxB,UAAM,UAAU,CAAC;AACjB,SAAK,QAAQ,CAAW,YAAA;AAChB,YAAA,OAAO,QAAQ,gBAAgB;AACrC,UAAI,CAAC,QAAQ,IAAI,EAAW,SAAA,IAAI,IAAI,CAAC;AAC7B,cAAA,IAAI,EAAE,KAAK,OAAO;AAAA,IAAA,CAC3B;AACM,WAAA;AAAA,EAAA;AAAA,EAGT,qBAAqB,MAAM;AACnB,UAAA,QAAQ,KAAK,IAAI,CAAA,YAAW,QAAQ,YAAY,EAAE,OAAO,OAAO;AACtE,WAAO,MAAM,SAAS,IAAI,MAAM,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,MAAM,SAAS;AAAA,EAAA;AAAA,EAG9E,kBAAkB,MAAM;AACtB,QAAI,CAAC,QAAQ,KAAK,WAAW,EAAU,QAAA;AACvC,UAAM,UAAU,KAAK,OAAO,CAAW,YAAA,QAAQ,SAAS,EAAE;AAC1D,WAAO,UAAU,KAAK;AAAA,EAAA;AAAA,EAGxB,gBAAgB,SAAS;AACjB,UAAA,SAAS,QAAQ,QAAQ,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AACnD,UAAM,SAAS,KAAK,MAAM,OAAO,SAAS,CAAC;AAC3C,WAAO,OAAO,SAAS,MAAM,KAC1B,OAAO,SAAS,CAAC,IAAI,OAAO,MAAM,KAAK,IACxC,OAAO,MAAM;AAAA,EAAA;AAAA,EAGjB,qBAAqB,MAAM;AACrB,QAAA,KAAK,SAAS,EAAU,QAAA;AAEtB,UAAA,aAAa,KAAK,mBAAmB,MAAM,CAAC,EAAE,IAAI,CAAW,YAAA,KAAK,kBAAkB,OAAO,CAAC;AAC5F,UAAA,WAAW,KAAK,kBAAkB,UAAU;AAElD,WAAO,KAAK,IAAI,GAAG,IAAI,QAAQ;AAAA,EAAA;AAAA,EAGjC,kBAAkB,SAAS;AACrB,QAAA,QAAQ,WAAW,EAAU,QAAA;AAC3B,UAAA,OAAO,QAAQ,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,QAAQ;AAC1D,WAAO,QAAQ,OAAO,CAAC,KAAK,QAAQ,MAAM,KAAK,IAAI,MAAM,MAAM,CAAC,GAAG,CAAC,IAAI,QAAQ;AAAA,EAAA;AAAA,EAGlF,mBAAmB,MAAM,aAAa;AACpC,UAAM,cAAc,KAAK,MAAM,KAAK,SAAS,WAAW;AACxD,UAAM,WAAW,CAAC;AAElB,aAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,YAAM,QAAQ,IAAI;AAClB,YAAM,MAAM,MAAM,cAAc,IAAI,KAAK,UAAU,IAAI,KAAK;AAC5D,eAAS,KAAK,KAAK,MAAM,OAAO,GAAG,CAAC;AAAA,IAAA;AAGtC,WAAO,SAAS,OAAO,CAAW,YAAA,QAAQ,SAAS,CAAC;AAAA,EAAA;AAAA,EAGtD,qBAAqB;AACZ,WAAA;AAAA,MACL,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,WAAW;AAAA,MACX,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,sBAAsB;AAAA,MACtB,iBAAiB,EAAE,OAAO,WAAW,OAAO,IAAI;AAAA,MAChD,gBAAgB,CAAC;AAAA,MACjB,mBAAmB,CAAC;AAAA,MACpB,qBAAqB,CAAC;AAAA,MACtB,kBAAkB,EAAE,OAAO,qBAAqB,aAAa,EAAE;AAAA,MAC/D,eAAe,EAAE,aAAa,GAAG,WAAW,EAAE;AAAA,MAC9C,iBAAiB,CAAC;AAAA,MAClB,iBAAiB,CAAC;AAAA,MAClB,UAAU,EAAE,gBAAgB,GAAG,aAAa,EAAE;AAAA,IAChD;AAAA,EAAA;AAEJ;AC/fO,MAAM,0BAA0B;AAAA,EACrC,cAAc;AACZ,SAAK,uBAAuB;AAAA,MAC1B,WAAW;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAEA,SAAK,kBAAkB;AAAA,MACrB,WAAW;AAAA,MACX,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAEA,SAAK,kBAAkB;AAAA,MACrB,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,qBAAqB;AAAA,MACrB,qBAAqB;AAAA,IACvB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,QAAQ,MAAM;AACL,WAAA,KAAK,QAAQ,IAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,MAAM,QAAQ,MAAM;AAClB,QAAI,CAAC,QAAQ,CAAC,KAAK,kBAAkB;AACnC,cAAQ,KAAK,gEAAgE;AAC7E,aAAO,KAAK,mBAAmB;AAAA,IAAA;AAGjC,UAAM,iBAAiB,KAAK;AAGtB,UAAA,iBAAiB,KAAK,sBAAsB,cAAc;AAG1D,UAAA,0BAA0B,KAAK,8BAA8B,cAAc;AAG3E,UAAA,uBAAuB,KAAK,4BAA4B,cAAc;AAGtE,UAAA,kBAAkB,KAAK,sBAAsB,cAAc;AAG3D,UAAA,mBAAmB,KAAK,wBAAwB,cAAc;AAG9D,UAAA,gBAAgB,KAAK,qBAAqB,cAAc;AAGxD,UAAA,wBAAwB,KAAK,+BAA+B,cAAc;AAEhF,UAAM,WAAW;AAAA,MACf,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,WAAW;AAAA,MACX,SAAS;AAAA;AAAA,MAGT,iBAAiB,KAAK,yBAAyB,cAAc;AAAA,MAC7D;AAAA,MACA;AAAA;AAAA,MAGA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAGA,iBAAiB;AAAA,QACf,aAAa,KAAK,kBAAkB,cAAc;AAAA,QAClD,kBAAkB,KAAK,uBAAuB,cAAc;AAAA,QAC5D,oBAAoB,KAAK,yBAAyB,cAAc;AAAA,QAChE,qBAAqB,KAAK,0BAA0B,cAAc;AAAA,QAClE,qBAAqB,KAAK,0BAA0B,cAAc;AAAA,MACpE;AAAA;AAAA,MAGA,oBAAoB,KAAK,6BAA6B,cAAc;AAAA;AAAA,MAGpE,eAAe,KAAK,qBAAqB,cAAc;AAAA;AAAA,MAGvD,iBAAiB,KAAK,wBAAwB,gBAAgB,qBAAqB;AAAA;AAAA,MAGnF,UAAU;AAAA,QACR,kBAAkB,eAAe,UAAU;AAAA,QAC3C,aAAa,KAAK,iBAAiB,cAAc;AAAA,QACjD,mBAAmB,KAAK,2BAA2B,cAAc;AAAA,QACjE,mBAAmB,KAAK,yBAAyB,cAAc;AAAA,MAAA;AAAA,IAEnE;AAEO,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,sBAAsB,MAAM;AAC1B,UAAM,iBAAiB,CAAC;AAGlB,UAAA,cAAc,KAAK,sBAAsB,IAAI;AAEnD,WAAO,KAAK,WAAW,EAAE,QAAQ,CAAQ,SAAA;AACjC,YAAA,WAAW,YAAY,IAAI;AACjC,YAAM,UAAU,SAAS,OAAO,CAAW,YAAA,QAAQ,SAAS,EAAE;AAC9D,qBAAe,IAAI,IAAI;AAAA,QACrB,UAAU,SAAS,SAAS,IAAI,UAAU,SAAS,SAAS;AAAA,QAC5D,UAAU,SAAS;AAAA,QACnB,aAAa,KAAK,qBAAqB,QAAQ;AAAA,QAC/C,mBAAmB,KAAK,2BAA2B,QAAQ;AAAA,QAC3D,YAAY,KAAK,qBAAqB,QAAQ;AAAA,MAChD;AAAA,IAAA,CACD;AAEM,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,8BAA8B,MAAM;AAElC,UAAM,mBAAmB;AAAA,MACvB,MAAM,CAAC;AAAA;AAAA,MACP,QAAQ,CAAC;AAAA;AAAA,MACT,MAAM,CAAC;AAAA;AAAA,MACP,UAAU,CAAA;AAAA;AAAA,IACZ;AAEA,SAAK,QAAQ,CAAW,YAAA;AACtB,YAAM,OAAO,KAAK,IAAI,QAAQ,UAAU,QAAQ,OAAO;AAEvD,UAAI,OAAO,EAAoB,kBAAA,KAAK,KAAK,OAAO;AAAA,eACvC,QAAQ,EAAoB,kBAAA,OAAO,KAAK,OAAO;AAAA,eAC/C,QAAQ,EAAoB,kBAAA,KAAK,KAAK,OAAO;AAAA,UACjD,kBAAiB,SAAS,KAAK,OAAO;AAAA,IAAA,CAC5C;AAED,UAAM,iBAAiB,CAAC;AACxB,WAAO,KAAK,gBAAgB,EAAE,QAAQ,CAAS,UAAA;AACvC,YAAA,YAAY,iBAAiB,KAAK;AACxC,qBAAe,KAAK,IAAI;AAAA,QACtB,UAAU,KAAK,kBAAkB,SAAS;AAAA,QAC1C,aAAa,KAAK,qBAAqB,SAAS;AAAA,QAChD,UAAU,UAAU;AAAA,MACtB;AAAA,IAAA,CACD;AAEM,WAAA;AAAA,MACL,cAAc;AAAA,MACd,yBAAyB,KAAK,iCAAiC,IAAI;AAAA,MACnE,eAAe,KAAK,uBAAuB,IAAI;AAAA,IACjD;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,4BAA4B,MAAM;AAChC,UAAM,aAAa;AAAA,MACjB,qBAAqB;AAAA;AAAA,MACrB,kBAAkB;AAAA;AAAA,MAClB,qBAAqB;AAAA;AAAA,MACrB,mBAAmB;AAAA;AAAA,MACnB,oBAAoB;AAAA;AAAA,IACtB;AAGA,UAAM,eAAe,KAAK;AAAA,MAAO,CAC/B,YAAA,QAAQ,aAAa,QAAQ,eAAe;AAAA,IAAA,EAC5C;AACS,eAAA,sBAAsB,eAAe,KAAK,SAAS;AAGxD,UAAA,mBAAmB,KAAK,wBAAwB,IAAI;AAC/C,eAAA,mBAAmB,iBAAiB,cAAc;AAGlD,eAAA,sBAAsB,KAAK,wBAAwB,IAAI;AAGvD,eAAA,oBAAoB,KAAK,sBAAsB,IAAI;AAEvD,WAAA;AAAA,MACL;AAAA,MACA,kBAAkB,KAAK,yBAAyB,UAAU;AAAA,MAC1D,uBAAuB,KAAK,4BAA4B,MAAM,UAAU;AAAA,IAC1E;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,sBAAsB,MAAM;AACpB,UAAA,gBAAgB,KAAK,IAAI,CAAA,YAAW,QAAQ,YAAY,EAAE,OAAO,OAAO;AAE1E,QAAA,cAAc,WAAW,EAAG,QAAO,EAAE,OAAO,WAAW,OAAO,IAAI;AAEhE,UAAA,cAAc,cAAc,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,cAAc;AACvE,UAAA,aAAa,KAAK,gBAAgB,aAAa;AAErD,QAAI,gBAAgB;AACpB,QAAI,aAAa;AAEjB,QAAI,cAAc,KAAM;AACN,sBAAA;AACH,mBAAA;AAAA,IAAA,WACJ,cAAc,KAAM;AACb,sBAAA;AACH,mBAAA;AAAA,IAAA,WACJ,cAAc,KAAM;AACb,sBAAA;AACH,mBAAA;AAAA,IAAA,OACR;AACW,sBAAA;AACH,mBAAA;AAAA,IAAA;AAGR,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa,KAAK,yBAAyB,aAAa;AAAA,MACxD,iBAAiB,KAAK,uBAAuB,IAAI;AAAA,IACnD;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,wBAAwB,MAAM;AACxB,QAAA,KAAK,SAAS,EAAG,QAAO,EAAE,OAAO,qBAAqB,aAAa,EAAE;AAEzE,UAAM,WAAW,KAAK,mBAAmB,MAAM,CAAC;AAChD,UAAM,oBAAoB,SAAS,IAAI,aAAW,KAAK,kBAAkB,OAAO,CAAC;AACjF,UAAM,eAAe,SAAS,IAAI,aAAW,KAAK,qBAAqB,OAAO,CAAC;AAEzE,UAAA,gBAAgB,KAAK,eAAe,iBAAiB;AACrD,UAAA,aAAa,KAAK,eAAe,aAAa,IAAI,CAAQ,SAAA,IAAE,IAAI,CAAC;AAEhE,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA,aAAa,kBAAkB,kBAAkB,SAAS,CAAC,IAAI,kBAAkB,CAAC;AAAA,MAClF;AAAA,MACA;AAAA,MACA,cAAc,KAAK,sBAAsB,iBAAiB;AAAA,MAC1D,WAAW,KAAK,mBAAmB,iBAAiB;AAAA,IACtD;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,qBAAqB,MAAM;AACzB,UAAM,SAAS,KAAK,OAAO,CAAW,YAAA,CAAC,QAAQ,SAAS;AAExD,UAAM,aAAa;AAAA,MACjB,oBAAoB;AAAA;AAAA,MACpB,cAAc;AAAA;AAAA,MACd,mBAAmB;AAAA;AAAA,MACnB,gBAAgB;AAAA;AAAA,MAChB,aAAa;AAAA;AAAA,IACf;AAEA,WAAO,QAAQ,CAASA,WAAA;AAClB,UAAAA,OAAM,cAAcA,OAAM,eAAe;AAC3C,cAAM,aAAa,KAAK,IAAIA,OAAM,UAAUA,OAAM,OAAO;AAEzD,YAAI,cAAc,GAAG;AACR,qBAAA;AAAA,QACF,WAAA,KAAK,oBAAoBA,MAAK,GAAG;AAC/B,qBAAA;AAAA,QACF,WAAA,KAAK,eAAeA,MAAK,GAAG;AAC1B,qBAAA;AAAA,QAAA,WACF,aAAa,IAAI;AACf,qBAAA;AAAA,QAAA,OACN;AACM,qBAAA;AAAA,QAAA;AAAA,MACb;AAAA,IACF,CACD;AAEM,WAAA;AAAA,MACL,aAAa,OAAO;AAAA,MACpB;AAAA,MACA,WAAW,KAAK,SAAS,IAAI,OAAO,SAAS,KAAK,SAAS;AAAA,MAC3D,gBAAgB,KAAK,uBAAuB,MAAM;AAAA,MAClD,kBAAkB,KAAK,wBAAwB,IAAI;AAAA,MACnD,oBAAoB,KAAK,0BAA0B,IAAI;AAAA,IACzD;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,+BAA+B,MAAM;AAC7B,UAAA,WAAW,KAAK,yBAAyB,IAAI;AACnD,UAAM,QAAQ,KAAK,sBAAsB,IAAI,EAAE;AACzC,UAAA,iBAAiB,KAAK,8BAA8B,IAAI;AACxD,UAAA,sBAAsB,KAAK,6BAA6B,cAAc;AACtE,UAAA,cAAc,KAAK,qBAAqB,IAAI;AAElD,WAAQ,WAAW,MAAQ,QAAQ,MAAQ,sBAAsB,MAAQ,cAAc;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMzF,kBAAkB,MAAM;AAEhB,UAAA,kBAAkB,KAAK,yBAAyB,IAAI;AAC1D,UAAM,aAAa,KAAK,sBAAsB,IAAI,EAAE;AAE5C,WAAA,kBAAkB,MAAQ,aAAa;AAAA,EAAA;AAAA,EAGjD,uBAAuB,MAAM;AAE3B,UAAM,mBAAmB,KAAK;AAAA,MAAO,CACnC,YAAA,QAAQ,aAAa,QAAQ,eAAe;AAAA,IAAA,EAC5C;AAEF,WAAO,KAAK,SAAS,IAAI,mBAAmB,KAAK,SAAS;AAAA,EAAA;AAAA,EAG5D,yBAAyB,MAAM;AAE7B,UAAM,qBAAqB,KAAK;AAAA,MAAO,CAAA,YACrC,QAAQ,kBACR,CAAC,qBAAqB,kBAAkB,EAAE,SAAS,QAAQ,cAAc;AAAA,IAC3E;AAEO,WAAA,KAAK,kBAAkB,kBAAkB;AAAA,EAAA;AAAA,EAGlD,0BAA0B,MAAM;AAExB,UAAA,iBAAiB,KAAK,8BAA8B,IAAI;AACvD,WAAA,KAAK,6BAA6B,cAAc;AAAA,EAAA;AAAA,EAGzD,0BAA0B,MAAM;AAE9B,UAAM,uBAAuB,KAAK;AAAA,MAAO,aACvC,QAAQ,yBACP,QAAQ,UAAU,MAAM,QAAQ,UAAU;AAAA,IAC7C;AAEO,WAAA,KAAK,kBAAkB,oBAAoB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMpD,qBAAqB,MAAM;AACzB,UAAM,SAAS;AAAA,MACb,OAAO,CAAC;AAAA;AAAA,MACR,QAAQ,CAAC;AAAA;AAAA,MACT,OAAO,CAAC;AAAA;AAAA,MACR,WAAW,CAAA;AAAA;AAAA,IACb;AAEA,SAAK,QAAQ,CAAW,YAAA;AACtB,YAAM,SAAS,KAAK,IAAI,QAAQ,SAAS,QAAQ,OAAO;AAExD,UAAI,UAAU,GAAW,QAAA,MAAM,KAAK,OAAO;AAAA,eAClC,UAAU,GAAW,QAAA,OAAO,KAAK,OAAO;AAAA,eACxC,UAAU,IAAY,QAAA,MAAM,KAAK,OAAO;AAAA,UAC5C,QAAO,UAAU,KAAK,OAAO;AAAA,IAAA,CACnC;AAED,UAAM,gBAAgB,CAAC;AACvB,WAAO,KAAK,MAAM,EAAE,QAAQ,CAAS,UAAA;AAC7B,YAAA,YAAY,OAAO,KAAK;AAC9B,oBAAc,KAAK,IAAI;AAAA,QACrB,UAAU,KAAK,kBAAkB,SAAS;AAAA,QAC1C,aAAa,KAAK,qBAAqB,SAAS;AAAA,QAChD,UAAU,UAAU;AAAA,MACtB;AAAA,IAAA,CACD;AAEM,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,wBAAwB,MAAM,YAAY;AACxC,UAAM,kBAAkB,CAAC;AAEzB,QAAI,aAAa,KAAK;AACpB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,YAAY,CAAC,sBAAsB,mBAAmB;AAAA,MAAA,CACvD;AAAA,IAAA;AAGG,UAAA,0BAA0B,KAAK,8BAA8B,IAAI;AACvE,QAAI,wBAAwB,aAAa,KAAK,WAAW,KAAK;AAC5D,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,YAAY,CAAC,0BAA0B,iBAAiB;AAAA,MAAA,CACzD;AAAA,IAAA;AAGG,UAAA,kBAAkB,KAAK,sBAAsB,IAAI;AACnD,QAAA,gBAAgB,kBAAkB,QAAQ;AAC5C,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,YAAY,CAAC,qBAAqB,sBAAsB;AAAA,MAAA,CACzD;AAAA,IAAA;AAGG,UAAA,gBAAgB,KAAK,qBAAqB,IAAI;AACpD,WAAO,KAAK,aAAa,EAAE,QAAQ,CAAS,UAAA;AACtC,UAAA,cAAc,KAAK,EAAE,WAAW,OAAO,cAAc,KAAK,EAAE,YAAY,GAAG;AAC7E,wBAAgB,KAAK;AAAA,UACnB,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS,sCAAsC,KAAK;AAAA,UACpD,YAAY,CAAC,GAAG,KAAK,mBAAmB,sBAAsB;AAAA,QAAA,CAC/D;AAAA,MAAA;AAAA,IACH,CACD;AAEM,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,yBAAyB,MAAM;AAC7B,QAAI,CAAC,QAAQ,KAAK,WAAW,EAAU,QAAA;AACvC,UAAM,UAAU,KAAK,OAAO,CAAW,YAAA,QAAQ,SAAS,EAAE;AAC1D,WAAO,UAAU,KAAK;AAAA,EAAA;AAAA,EAGxB,sBAAsB,MAAM;AAC1B,UAAM,UAAU,CAAC;AACjB,SAAK,QAAQ,CAAW,YAAA;AAChB,YAAA,OAAO,QAAQ,kBAAkB;AACvC,UAAI,CAAC,QAAQ,IAAI,EAAW,SAAA,IAAI,IAAI,CAAC;AAC7B,cAAA,IAAI,EAAE,KAAK,OAAO;AAAA,IAAA,CAC3B;AACM,WAAA;AAAA,EAAA;AAAA,EAGT,qBAAqB,MAAM;AACnB,UAAA,QAAQ,KAAK,IAAI,CAAA,YAAW,QAAQ,YAAY,EAAE,OAAO,OAAO;AACtE,WAAO,MAAM,SAAS,IAAI,MAAM,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,MAAM,SAAS;AAAA,EAAA;AAAA,EAG9E,2BAA2B,MAAM;AAC/B,UAAM,cAAc,KAAK;AAAA,MAAI,aAC3B,KAAK,IAAI,QAAQ,UAAU,QAAQ,OAAO;AAAA,IAAA,EAC1C,OAAO,OAAO;AAChB,WAAO,YAAY,SAAS,IAAI,YAAY,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,YAAY,SAAS;AAAA,EAAA;AAAA,EAGhG,kBAAkB,MAAM;AACtB,QAAI,CAAC,QAAQ,KAAK,WAAW,EAAU,QAAA;AACvC,UAAM,UAAU,KAAK,OAAO,CAAW,YAAA,QAAQ,SAAS,EAAE;AAC1D,WAAO,UAAU,KAAK;AAAA,EAAA;AAAA,EAGxB,gBAAgB,SAAS;AACjB,UAAA,SAAS,QAAQ,QAAQ,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AACnD,UAAM,SAAS,KAAK,MAAM,OAAO,SAAS,CAAC;AAC3C,WAAO,OAAO,SAAS,MAAM,KAC1B,OAAO,SAAS,CAAC,IAAI,OAAO,MAAM,KAAK,IACxC,OAAO,MAAM;AAAA,EAAA;AAAA,EAGjB,6BAA6B,gBAAgB;AACrC,UAAA,SAAS,OAAO,OAAO,eAAe,YAAY,EAAE,IAAI,CAAS,UAAA,MAAM,QAAQ;AACrF,WAAO,OAAO,SAAS,IAAI,OAAO,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,OAAO,SAAS;AAAA,EAAA;AAAA,EAGjF,qBAAqB,MAAM;AACrB,QAAA,KAAK,SAAS,EAAU,QAAA;AAEtB,UAAA,aAAa,KAAK,mBAAmB,MAAM,CAAC,EAAE,IAAI,CAAW,YAAA,KAAK,kBAAkB,OAAO,CAAC;AAC5F,UAAA,WAAW,KAAK,kBAAkB,UAAU;AAElD,WAAO,KAAK,IAAI,GAAG,IAAI,QAAQ;AAAA,EAAA;AAAA,EAGjC,kBAAkB,SAAS;AACrB,QAAA,QAAQ,WAAW,EAAU,QAAA;AAC3B,UAAA,OAAO,QAAQ,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,QAAQ;AAC1D,WAAO,QAAQ,OAAO,CAAC,KAAK,QAAQ,MAAM,KAAK,IAAI,MAAM,MAAM,CAAC,GAAG,CAAC,IAAI,QAAQ;AAAA,EAAA;AAAA,EAGlF,mBAAmB,MAAM,aAAa;AACpC,UAAM,cAAc,KAAK,MAAM,KAAK,SAAS,WAAW;AACxD,UAAM,WAAW,CAAC;AAElB,aAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,YAAM,QAAQ,IAAI;AAClB,YAAM,MAAM,MAAM,cAAc,IAAI,KAAK,UAAU,IAAI,KAAK;AAC5D,eAAS,KAAK,KAAK,MAAM,OAAO,GAAG,CAAC;AAAA,IAAA;AAGtC,WAAO,SAAS,OAAO,CAAW,YAAA,QAAQ,SAAS,CAAC;AAAA,EAAA;AAAA,EAGtD,qBAAqB;AACZ,WAAA;AAAA,MACL,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,WAAW;AAAA,MACX,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,uBAAuB;AAAA,MACvB,iBAAiB,EAAE,OAAO,WAAW,OAAO,IAAI;AAAA,MAChD,gBAAgB,CAAC;AAAA,MACjB,yBAAyB,CAAC;AAAA,MAC1B,sBAAsB,CAAC;AAAA,MACvB,kBAAkB,EAAE,OAAO,qBAAqB,aAAa,EAAE;AAAA,MAC/D,eAAe,EAAE,aAAa,GAAG,WAAW,EAAE;AAAA,MAC9C,iBAAiB,CAAC;AAAA,MAClB,iBAAiB,CAAC;AAAA,MAClB,UAAU,EAAE,kBAAkB,GAAG,aAAa,EAAE;AAAA,IAClD;AAAA,EAAA;AAEJ;ACziBO,MAAM,uBAAuB;AAAA,EAClC,cAAc;AACZ,SAAK,kBAAkB;AAAA,MACrB,WAAW;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAEA,SAAK,aAAa;AAAA,MAChB,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,QAAQ;AAAA,IACV;AAEA,SAAK,kBAAkB;AAAA,MACrB,oBAAoB;AAAA,MACpB,gBAAgB;AAAA,MAChB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,kBAAkB;AAAA,IACpB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,QAAQ,MAAM;AACL,WAAA,KAAK,QAAQ,IAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,MAAM,QAAQ,MAAM;AAClB,QAAI,CAAC,QAAQ,CAAC,KAAK,eAAe;AAChC,cAAQ,KAAK,yEAAyE;AACtF,aAAO,KAAK,mBAAmB;AAAA,IAAA;AAGjC,UAAM,YAAY,KAAK;AAGjB,UAAA,sBAAsB,KAAK,2BAA2B,SAAS;AAG/D,UAAA,iBAAiB,KAAK,qBAAqB,SAAS;AAGpD,UAAA,yBAAyB,KAAK,8BAA8B,SAAS;AAGrE,UAAA,0BAA0B,KAAK,8BAA8B,SAAS;AAGtE,UAAA,mBAAmB,KAAK,wBAAwB,SAAS;AAGzD,UAAA,wBAAwB,KAAK,6BAA6B,SAAS;AAGnE,UAAA,qBAAqB,KAAK,4BAA4B,SAAS;AAErE,UAAM,WAAW;AAAA,MACf,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,WAAW;AAAA,MACX,SAAS;AAAA;AAAA,MAGT,iBAAiB,KAAK,yBAAyB,SAAS;AAAA,MACxD;AAAA,MACA;AAAA;AAAA,MAGA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAGA,iBAAiB;AAAA,QACf,oBAAoB,KAAK,yBAAyB,SAAS;AAAA,QAC3D,gBAAgB,KAAK,0BAA0B,SAAS;AAAA,QACxD,wBAAwB,KAAK,kCAAkC,SAAS;AAAA,QACxE,sBAAsB,KAAK,2BAA2B,SAAS;AAAA,QAC/D,kBAAkB,KAAK,uBAAuB,SAAS;AAAA,MACzD;AAAA;AAAA,MAGA,4BAA4B,KAAK,qCAAqC,SAAS;AAAA;AAAA,MAG/E,iBAAiB,KAAK,uBAAuB,SAAS;AAAA;AAAA,MAGtD,iBAAiB,KAAK,wBAAwB,WAAW,kBAAkB;AAAA;AAAA,MAG3E,UAAU;AAAA,QACR,aAAa,UAAU,UAAU;AAAA,QACjC,kBAAkB,KAAK,sBAAsB,SAAS;AAAA,QACtD,sBAAsB,KAAK,8BAA8B,SAAS;AAAA,QAClE,6BAA6B,KAAK,mCAAmC,SAAS;AAAA,MAAA;AAAA,IAElF;AAEO,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,2BAA2B,MAAM;AAC/B,UAAM,sBAAsB,CAAC;AAGvB,UAAA,cAAc,KAAK,iBAAiB,IAAI;AAE9C,WAAO,KAAK,WAAW,EAAE,QAAQ,CAAQ,SAAA;AACjC,YAAA,WAAW,YAAY,IAAI;AACjC,YAAM,UAAU,SAAS,OAAO,CAAW,YAAA,QAAQ,SAAS,EAAE;AAC9D,0BAAoB,IAAI,IAAI;AAAA,QAC1B,UAAU,SAAS,SAAS,IAAI,UAAU,SAAS,SAAS;AAAA,QAC5D,UAAU,SAAS;AAAA,QACnB,qBAAqB,KAAK,qBAAqB,QAAQ;AAAA,QACvD,gBAAgB,KAAK,wBAAwB,QAAQ;AAAA,QACrD,YAAY,KAAK,0BAA0B,QAAQ;AAAA,MACrD;AAAA,IAAA,CACD;AAEM,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,qBAAqB,MAAM;AAEzB,UAAM,eAAe;AAAA,MACnB,WAAW,CAAC;AAAA;AAAA,MACZ,WAAW,CAAC;AAAA;AAAA,MACZ,YAAY,CAAC;AAAA;AAAA,MACb,UAAU,CAAA;AAAA;AAAA,IACZ;AAEA,SAAK,QAAQ,CAAW,YAAA;AACtB,YAAM,UAAU,QAAQ,gBAAgB,QAAQ,iBAAiB;AAEjE,UAAI,UAAU,IAAmB,cAAA,UAAU,KAAK,OAAO;AAAA,eAC9C,UAAU,IAAmB,cAAA,UAAU,KAAK,OAAO;AAAA,eACnD,UAAU,IAAoB,cAAA,WAAW,KAAK,OAAO;AAAA,UACzD,cAAa,SAAS,KAAK,OAAO;AAAA,IAAA,CACxC;AAED,UAAM,iBAAiB,CAAC;AACxB,WAAO,KAAK,YAAY,EAAE,QAAQ,CAAS,UAAA;AACnC,YAAA,YAAY,aAAa,KAAK;AACpC,qBAAe,KAAK,IAAI;AAAA,QACtB,UAAU,KAAK,kBAAkB,SAAS;AAAA,QAC1C,aAAa,KAAK,qBAAqB,SAAS;AAAA,QAChD,UAAU,UAAU;AAAA,MACtB;AAAA,IAAA,CACD;AAEM,WAAA;AAAA,MACL,WAAW;AAAA,MACX,YAAY,KAAK,oBAAoB,IAAI;AAAA,MACzC,eAAe,KAAK,uBAAuB,IAAI;AAAA,MAC/C,cAAc,KAAK,oBAAoB,IAAI;AAAA,IAC7C;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,8BAA8B,MAAM;AAClC,UAAM,cAAc;AAAA,MAClB,iBAAiB;AAAA;AAAA,MACjB,oBAAoB;AAAA;AAAA,MACpB,wBAAwB;AAAA;AAAA,MACxB,oBAAoB;AAAA;AAAA,IACtB;AAGA,UAAM,kBAAkB,KAAK,OAAO,CAAW,YAAA,CAAC,QAAQ,iBAAiB;AACzE,UAAM,oBAAoB,KAAK,OAAO,CAAA,YAAW,QAAQ,iBAAiB;AAEpE,UAAA,oBAAoB,KAAK,kBAAkB,eAAe;AAC1D,UAAA,sBAAsB,KAAK,kBAAkB,iBAAiB;AAEpE,gBAAY,qBAAqB;AACjC,gBAAY,yBAAyB,sBAAsB,KAAK,IAAI,mBAAmB,GAAG;AAC9E,gBAAA,qBAAqB,oBAAoB,sBAAsB,aAAa;AAG5E,gBAAA,kBAAkB,KAAK,uBAAuB,IAAI;AAEvD,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,8BAA8B,MAAM;AAC5B,UAAA,gBAAgB,KAAK,IAAI,CAAA,YAAW,QAAQ,YAAY,EAAE,OAAO,OAAO;AACxE,UAAA,iBAAiB,KAAK,IAAI,CAAA,YAAW,QAAQ,iBAAiB,GAAI,EAAE,OAAO,OAAO;AAEpF,QAAA,cAAc,WAAW,EAAG,QAAO,EAAE,OAAO,WAAW,OAAO,IAAI;AAGtE,UAAM,YAAY,KAAK;AAAA,MAAI,CAAC,SAAS,UACnC,QAAQ,eAAe,eAAe,KAAK;AAAA,IAC3C,EAAA,OAAO,CAAW,YAAA,UAAU,CAAC;AAEzB,UAAA,iBAAiB,UAAU,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,UAAU;AAClE,UAAA,gBAAgB,KAAK,gBAAgB,SAAS;AAEpD,QAAI,gBAAgB;AACpB,QAAI,aAAa;AAEjB,QAAI,iBAAiB,KAAM;AACT,sBAAA;AACH,mBAAA;AAAA,IAAA,WACJ,iBAAiB,KAAM;AAChB,sBAAA;AACH,mBAAA;AAAA,IAAA,WACJ,iBAAiB,KAAM;AAChB,sBAAA;AACH,mBAAA;AAAA,IAAA,OACR;AACW,sBAAA;AACH,mBAAA;AAAA,IAAA;AAGR,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa,KAAK,4BAA4B,SAAS;AAAA,MACvD,sBAAsB,KAAK,8BAA8B,IAAI;AAAA,IAC/D;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,wBAAwB,MAAM;AACxB,QAAA,KAAK,SAAS,EAAG,QAAO,EAAE,OAAO,qBAAqB,aAAa,EAAE;AAEzE,UAAM,WAAW,KAAK,mBAAmB,MAAM,CAAC;AAChD,UAAM,oBAAoB,SAAS,IAAI,aAAW,KAAK,kBAAkB,OAAO,CAAC;AACjF,UAAM,mBAAmB,SAAS,IAAI,aAAW,KAAK,wBAAwB,OAAO,CAAC;AAEhF,UAAA,gBAAgB,KAAK,eAAe,iBAAiB;AACrD,UAAA,aAAa,KAAK,eAAe,iBAAiB,IAAI,CAAW,YAAA,IAAE,OAAO,CAAC;AAE1E,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA,aAAa,kBAAkB,kBAAkB,SAAS,CAAC,IAAI,kBAAkB,CAAC;AAAA,MAClF;AAAA,MACA;AAAA,MACA,sBAAsB,KAAK,sBAAsB,iBAAiB;AAAA,MAClE,mBAAmB,KAAK,mBAAmB,iBAAiB;AAAA,IAC9D;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,6BAA6B,MAAM;AACjC,UAAM,SAAS,KAAK,OAAO,CAAW,YAAA,CAAC,QAAQ,SAAS;AAExD,UAAM,aAAa;AAAA,MACjB,kBAAkB;AAAA;AAAA,MAClB,mBAAmB;AAAA;AAAA,MACnB,eAAe;AAAA;AAAA,MACf,gBAAgB;AAAA;AAAA,MAChB,iBAAiB;AAAA;AAAA,IACnB;AAEA,WAAO,QAAQ,CAASA,WAAA;AACtB,YAAM,UAAUA,OAAM,gBAAgBA,OAAM,iBAAiB;AAE7D,UAAI,UAAU,KAAK;AACN,mBAAA;AAAA,MAAA,WACF,UAAU,KAAO;AACf,mBAAA;AAAA,MAAA,WACFA,OAAM,cAAc,YAAY;AAC9B,mBAAA;AAAA,MAAA,WACFA,OAAM,cAAc,YAAY;AAC9B,mBAAA;AAAA,MAAA,OACN;AACM,mBAAA;AAAA,MAAA;AAAA,IACb,CACD;AAEM,WAAA;AAAA,MACL,aAAa,OAAO;AAAA,MACpB;AAAA,MACA,WAAW,KAAK,SAAS,IAAI,OAAO,SAAS,KAAK,SAAS;AAAA,MAC3D,gBAAgB,KAAK,+BAA+B,MAAM;AAAA,MAC1D,kBAAkB,KAAK,wBAAwB,IAAI;AAAA,MACnD,4BAA4B,KAAK,0BAA0B,IAAI;AAAA,IACjE;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,4BAA4B,MAAM;AAC1B,UAAA,WAAW,KAAK,yBAAyB,IAAI;AACnD,UAAM,QAAQ,KAAK,8BAA8B,IAAI,EAAE;AACjD,UAAA,SAAS,KAAK,qBAAqB,IAAI;AACvC,UAAA,cAAc,KAAK,qBAAqB,MAAM;AAC9C,UAAA,cAAc,KAAK,8BAA8B,IAAI;AAC3D,UAAM,mBAAmB,YAAY;AAC/B,UAAA,cAAc,KAAK,qBAAqB,IAAI;AAE1C,WAAA,WAAW,MAAQ,QAAQ,MAAQ,cAAc,MAAQ,mBAAmB,OAAS,cAAc;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM7G,yBAAyB,MAAM;AAEvB,UAAA,kBAAkB,KAAK,yBAAyB,IAAI;AAC1D,UAAM,aAAa,KAAK,8BAA8B,IAAI,EAAE;AAEpD,WAAA,kBAAkB,MAAQ,aAAa;AAAA,EAAA;AAAA,EAGjD,0BAA0B,MAAM;AAExB,UAAA,SAAS,KAAK,qBAAqB,IAAI;AACtC,WAAA,KAAK,qBAAqB,MAAM;AAAA,EAAA;AAAA,EAGzC,kCAAkC,MAAM;AAEhC,UAAA,cAAc,KAAK,8BAA8B,IAAI;AAC3D,WAAO,YAAY;AAAA,EAAA;AAAA,EAGrB,2BAA2B,MAAM;AAE/B,UAAM,iBAAiB,KAAK;AAAA,MAAO,aACjC,QAAQ,aAAa,QAAQ,UAAU,SAAS,UAAU;AAAA,IAC5D;AAEO,WAAA,KAAK,kBAAkB,cAAc;AAAA,EAAA;AAAA,EAG9C,uBAAuB,MAAM;AAE3B,UAAM,eAAe,KAAK;AAAA,MAAO,aAC/B,QAAQ,aAAa,QAAQ,UAAU,SAAS,QAAQ;AAAA,IAC1D;AAEO,WAAA,KAAK,kBAAkB,YAAY;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM5C,uBAAuB,MAAM;AAC3B,UAAM,YAAY,KAAK;AAAA,MAAI,CACzB,YAAA,QAAQ,gBAAgB,QAAQ,iBAAiB;AAAA,IACjD,EAAA,OAAO,CAAW,YAAA,UAAU,CAAC;AAE/B,QAAI,UAAU,WAAW,EAAU,QAAA,EAAE,QAAQ,UAAU;AAEhD,WAAA;AAAA,MACL,gBAAgB,UAAU,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,UAAU;AAAA,MACjE,eAAe,KAAK,gBAAgB,SAAS;AAAA,MAC7C,oBAAoB,KAAK,kBAAkB,SAAS;AAAA,MACpD,cAAc,KAAK,6BAA6B,IAAI;AAAA,MACpD,qBAAqB,KAAK,2BAA2B,SAAS;AAAA,IAChE;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,wBAAwB,MAAM,YAAY;AACxC,UAAM,kBAAkB,CAAC;AAEzB,QAAI,aAAa,KAAK;AACpB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,YAAY,CAAC,yBAAyB,uBAAuB;AAAA,MAAA,CAC9D;AAAA,IAAA;AAGG,UAAA,SAAS,KAAK,qBAAqB,IAAI;AAC7C,QAAI,KAAK,qBAAqB,MAAM,IAAI,KAAK;AAC3C,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,YAAY,CAAC,oBAAoB,qBAAqB;AAAA,MAAA,CACvD;AAAA,IAAA;AAGG,UAAA,aAAa,KAAK,8BAA8B,IAAI;AACtD,QAAA,WAAW,kBAAkB,QAAQ;AACvC,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,YAAY,CAAC,qBAAqB,wBAAwB;AAAA,MAAA,CAC3D;AAAA,IAAA;AAGG,UAAA,cAAc,KAAK,8BAA8B,IAAI;AACvD,QAAA,YAAY,qBAAqB,KAAK;AACxC,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,YAAY,CAAC,qBAAqB,0BAA0B;AAAA,MAAA,CAC7D;AAAA,IAAA;AAGI,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,yBAAyB,MAAM;AAC7B,QAAI,CAAC,QAAQ,KAAK,WAAW,EAAU,QAAA;AACvC,UAAM,UAAU,KAAK,OAAO,CAAW,YAAA,QAAQ,SAAS,EAAE;AAC1D,WAAO,UAAU,KAAK;AAAA,EAAA;AAAA,EAGxB,iBAAiB,MAAM;AACrB,UAAM,UAAU,CAAC;AACjB,SAAK,QAAQ,CAAW,YAAA;AAChB,YAAA,OAAO,QAAQ,aAAa;AAClC,UAAI,CAAC,QAAQ,IAAI,EAAW,SAAA,IAAI,IAAI,CAAC;AAC7B,cAAA,IAAI,EAAE,KAAK,OAAO;AAAA,IAAA,CAC3B;AACM,WAAA;AAAA,EAAA;AAAA,EAGT,qBAAqB,MAAM;AACnB,UAAA,QAAQ,KAAK,IAAI,CAAA,YAAW,QAAQ,YAAY,EAAE,OAAO,OAAO;AACtE,WAAO,MAAM,SAAS,IAAI,MAAM,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,MAAM,SAAS;AAAA,EAAA;AAAA,EAG9E,wBAAwB,MAAM;AAC5B,UAAM,YAAY,KAAK;AAAA,MAAI,CACzB,YAAA,QAAQ,gBAAgB,QAAQ,iBAAiB;AAAA,IACjD,EAAA,OAAO,CAAW,YAAA,UAAU,CAAC;AAC/B,WAAO,UAAU,SAAS,IAAI,UAAU,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,UAAU,SAAS;AAAA,EAAA;AAAA,EAG1F,kBAAkB,MAAM;AACtB,QAAI,CAAC,QAAQ,KAAK,WAAW,EAAU,QAAA;AACvC,UAAM,UAAU,KAAK,OAAO,CAAW,YAAA,QAAQ,SAAS,EAAE;AAC1D,WAAO,UAAU,KAAK;AAAA,EAAA;AAAA,EAGxB,gBAAgB,SAAS;AACjB,UAAA,SAAS,QAAQ,QAAQ,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AACnD,UAAM,SAAS,KAAK,MAAM,OAAO,SAAS,CAAC;AAC3C,WAAO,OAAO,SAAS,MAAM,KAC1B,OAAO,SAAS,CAAC,IAAI,OAAO,MAAM,KAAK,IACxC,OAAO,MAAM;AAAA,EAAA;AAAA,EAGjB,qBAAqB,QAAQ;AACrB,UAAA,SAAS,OAAO,OAAO,OAAO,SAAS,EAAE,IAAI,CAAS,UAAA,MAAM,QAAQ;AAC1E,WAAO,OAAO,SAAS,IAAI,OAAO,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,OAAO,SAAS;AAAA,EAAA;AAAA,EAGjF,qBAAqB,MAAM;AACrB,QAAA,KAAK,SAAS,EAAU,QAAA;AAEtB,UAAA,aAAa,KAAK,mBAAmB,MAAM,CAAC,EAAE,IAAI,CAAW,YAAA,KAAK,kBAAkB,OAAO,CAAC;AAC5F,UAAA,WAAW,KAAK,kBAAkB,UAAU;AAElD,WAAO,KAAK,IAAI,GAAG,IAAI,QAAQ;AAAA,EAAA;AAAA,EAGjC,kBAAkB,SAAS;AACrB,QAAA,QAAQ,WAAW,EAAU,QAAA;AAC3B,UAAA,OAAO,QAAQ,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,QAAQ;AAC1D,WAAO,QAAQ,OAAO,CAAC,KAAK,QAAQ,MAAM,KAAK,IAAI,MAAM,MAAM,CAAC,GAAG,CAAC,IAAI,QAAQ;AAAA,EAAA;AAAA,EAGlF,mBAAmB,MAAM,aAAa;AACpC,UAAM,cAAc,KAAK,MAAM,KAAK,SAAS,WAAW;AACxD,UAAM,WAAW,CAAC;AAElB,aAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,YAAM,QAAQ,IAAI;AAClB,YAAM,MAAM,MAAM,cAAc,IAAI,KAAK,UAAU,IAAI,KAAK;AAC5D,eAAS,KAAK,KAAK,MAAM,OAAO,GAAG,CAAC;AAAA,IAAA;AAGtC,WAAO,SAAS,OAAO,CAAW,YAAA,QAAQ,SAAS,CAAC;AAAA,EAAA;AAAA,EAGtD,qBAAqB;AACZ,WAAA;AAAA,MACL,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,WAAW;AAAA,MACX,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,yBAAyB,EAAE,OAAO,WAAW,OAAO,IAAI;AAAA,MACxD,qBAAqB,CAAC;AAAA,MACtB,gBAAgB,CAAC;AAAA,MACjB,wBAAwB,CAAC;AAAA,MACzB,kBAAkB,EAAE,OAAO,qBAAqB,aAAa,EAAE;AAAA,MAC/D,uBAAuB,EAAE,aAAa,GAAG,WAAW,EAAE;AAAA,MACtD,iBAAiB,CAAC;AAAA,MAClB,iBAAiB,CAAC;AAAA,MAClB,UAAU,EAAE,aAAa,GAAG,kBAAkB,EAAE;AAAA,IAClD;AAAA,EAAA;AAEJ;ACnhBO,MAAM,4BAA4B;AAAA,EACvC,cAAc;AACZ,SAAK,oBAAoB;AAAA,MACvB,WAAW;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAEA,SAAK,eAAe;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,SAAS;AAAA,IACX;AAEA,SAAK,kBAAkB;AAAA,MACrB,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,IACpB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,QAAQ,MAAM;AACL,WAAA,KAAK,QAAQ,IAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,MAAM,QAAQ,MAAM;AAClB,QAAI,CAAC,QAAQ,CAAC,KAAK,oBAAoB;AACrC,cAAQ,KAAK,iFAAiF;AAC9F,aAAO,KAAK,mBAAmB;AAAA,IAAA;AAGjC,UAAM,cAAc,KAAK;AAGnB,UAAA,iBAAiB,KAAK,sBAAsB,WAAW;AAGvD,UAAA,mBAAmB,KAAK,uBAAuB,WAAW;AAG1D,UAAA,qBAAqB,KAAK,0BAA0B,WAAW;AAG/D,UAAA,oBAAoB,KAAK,wBAAwB,WAAW;AAG5D,UAAA,mBAAmB,KAAK,wBAAwB,WAAW;AAG3D,UAAA,gBAAgB,KAAK,qBAAqB,WAAW;AAGrD,UAAA,0BAA0B,KAAK,iCAAiC,WAAW;AAEjF,UAAM,WAAW;AAAA,MACf,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,WAAW;AAAA,MACX,SAAS;AAAA;AAAA,MAGT,iBAAiB,KAAK,yBAAyB,WAAW;AAAA,MAC1D;AAAA,MACA;AAAA;AAAA,MAGA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAGA,iBAAiB;AAAA,QACf,oBAAoB,KAAK,yBAAyB,WAAW;AAAA,QAC7D,oBAAoB,KAAK,yBAAyB,WAAW;AAAA,QAC7D,kBAAkB,KAAK,uBAAuB,WAAW;AAAA,QACzD,mBAAmB,KAAK,sBAAsB,WAAW;AAAA,QACzD,kBAAkB,KAAK,uBAAuB,WAAW;AAAA,MAC3D;AAAA;AAAA,MAGA,uBAAuB,KAAK,6BAA6B,WAAW;AAAA;AAAA,MAGpE,yBAAyB,KAAK,gCAAgC,WAAW;AAAA;AAAA,MAGzE,mBAAmB,KAAK,2BAA2B,WAAW;AAAA,IAChE;AAEO,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,yBAAyB,MAAM;AAC7B,QAAI,CAAC,KAAK,YAAY,KAAK,SAAS,WAAW,EAAU,QAAA;AAEzD,UAAM,UAAU,KAAK,SAAS,OAAO,CAAW,YAAA,QAAQ,OAAO,EAAE;AAC1D,WAAA,UAAU,KAAK,SAAS;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMjC,sBAAsB,MAAM;AAC1B,UAAM,iBAAiB,CAAC;AAExB,WAAO,KAAK,KAAK,YAAY,EAAE,QAAQ,CAAQ,SAAA;AACvC,YAAA,eAAe,KAAK,UAAU,OAAO,aAAW,QAAQ,gBAAgB,IAAI,KAAK,CAAC;AACpF,UAAA,aAAa,SAAS,GAAG;AAC3B,cAAM,UAAU,aAAa,OAAO,CAAW,YAAA,QAAQ,OAAO,EAAE;AAChE,uBAAe,IAAI,IAAI;AAAA,UACrB,UAAU,UAAU,aAAa;AAAA,UACjC,UAAU,aAAa;AAAA,UACvB,aAAa,KAAK,aAAa,IAAI;AAAA,QACrC;AAAA,MAAA;AAAA,IACF,CACD;AAEM,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,uBAAuB,MAAM;AAC3B,QAAI,CAAC,KAAK,YAAY,KAAK,SAAS,WAAW,GAAG;AAChD,aAAO,EAAE,SAAS,KAAM,UAAU,UAAU;AAAA,IAAA;AAG9C,UAAM,gBAAgB,KAAK,SAAS,IAAI,CAAW,YAAA,QAAQ,gBAAgB,GAAI;AACzE,UAAA,cAAc,cAAc,OAAO,CAAC,KAAK,SAAS,MAAM,MAAM,CAAC,IAAI,cAAc;AAEvF,QAAI,WAAW;AACX,QAAA,cAAc,IAAiB,YAAA;AAAA,aAC1B,cAAc,IAAiB,YAAA;AAAA,aAC/B,cAAc,IAAiB,YAAA;AAAA,aAC/B,cAAc,IAAiB,YAAA;AAAA,QACxB,YAAA;AAET,WAAA;AAAA,MACL,SAAS;AAAA,MACT;AAAA,MACA,cAAc,KAAK,0BAA0B,aAAa;AAAA,IAC5D;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,0BAA0B,MAAM;AAC9B,UAAM,mBAAmB,CAAC,UAAU,UAAU,WAAW,UAAU;AACnE,UAAM,qBAAqB,CAAC;AAE5B,qBAAiB,QAAQ,CAAS,UAAA;AAC1B,YAAA,gBAAgB,KAAK,UAAU,OAAO,aAAW,QAAQ,eAAe,KAAK,KAAK,CAAC;AACrF,UAAA,cAAc,SAAS,GAAG;AAC5B,cAAM,UAAU,cAAc,OAAO,CAAW,YAAA,QAAQ,OAAO,EAAE;AACjE,2BAAmB,KAAK,IAAI;AAAA,UAC1B,UAAU,UAAU,cAAc;AAAA,UAClC,UAAU,cAAc;AAAA,UACxB,aAAa,cAAc,OAAO,CAAC,KAAK,YAAY,OAAO,QAAQ,gBAAgB,MAAO,CAAC,IAAI,cAAc;AAAA,QAC/G;AAAA,MAAA;AAAA,IACF,CACD;AAEM,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,wBAAwB,MAAM;AACtB,UAAA,qBAAqB,KAAK,UAAU,OAAO,aAAW,QAAQ,SAAS,YAAY,KAAK,CAAC;AAE3F,QAAA,mBAAmB,WAAW,GAAG;AACnC,aAAO,EAAE,UAAU,KAAK,YAAY,SAAS;AAAA,IAAA;AAG/C,UAAM,UAAU,mBAAmB,OAAO,CAAW,YAAA,QAAQ,OAAO,EAAE;AAChE,UAAA,WAAW,UAAU,mBAAmB;AAE9C,QAAI,aAAa;AACb,QAAA,YAAY,IAAkB,cAAA;AAAA,aACzB,YAAY,IAAkB,cAAA;AAAA,aAC9B,YAAY,IAAkB,cAAA;AAAA,aAC9B,YAAY,IAAkB,cAAA;AAAA,QACrB,cAAA;AAEX,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA,kBAAkB,mBAAmB;AAAA,IACvC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,wBAAwB,MAAM;AAC5B,QAAI,CAAC,KAAK,YAAY,KAAK,SAAS,SAAS,GAAG;AAC9C,aAAO,EAAE,OAAO,qBAAqB,aAAa,EAAE;AAAA,IAAA;AAGtD,UAAM,WAAW,KAAK,SAAS,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK,EAAE,SAAS,CAAC;AACrF,UAAA,YAAY,SAAS,MAAM,GAAG,KAAK,MAAM,SAAS,SAAS,CAAC,CAAC;AAC7D,UAAA,aAAa,SAAS,MAAM,KAAK,MAAM,SAAS,SAAS,CAAC,CAAC;AAE3D,UAAA,oBAAoB,UAAU,OAAO,CAAA,MAAK,EAAE,OAAO,EAAE,SAAS,UAAU;AACxE,UAAA,qBAAqB,WAAW,OAAO,CAAA,MAAK,EAAE,OAAO,EAAE,SAAS,WAAW;AAEjF,UAAM,cAAc,qBAAqB;AAEzC,QAAI,QAAQ;AACR,QAAA,cAAc,IAAa,SAAA;AAAA,aACtB,cAAc,KAAc,SAAA;AAE9B,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,qBAAqB,MAAM;AACnB,UAAA,SAAS,KAAK,UAAU,OAAO,aAAW,CAAC,QAAQ,OAAO,KAAK,CAAC;AAElE,QAAA,OAAO,WAAW,GAAG;AACvB,aAAO,EAAE,WAAW,GAAG,gBAAgB,CAAA,EAAG;AAAA,IAAA;AAG5C,UAAM,aAAa,CAAC;AACpB,WAAO,QAAQ,CAASA,WAAA;AAChB,YAAA,OAAOA,OAAM,aAAa;AAChC,iBAAW,IAAI,KAAK,WAAW,IAAI,KAAK,KAAK;AAAA,IAAA,CAC9C;AAED,UAAM,eAAe,OAAO,QAAQ,UAAU,EAC3C,KAAK,CAAC,GAAE,CAAC,GAAG,CAAA,EAAE,CAAC,MAAM,IAAI,CAAC,EAC1B,MAAM,GAAG,CAAC,EACV,IAAI,CAAC,CAAC,MAAM,KAAK,OAAO,EAAE,MAAM,OAAO,YAAY,QAAQ,OAAO,OAAS,EAAA;AAEvE,WAAA;AAAA,MACL,WAAW,OAAO,SAAS,KAAK,SAAS;AAAA,MACzC,gBAAgB;AAAA,MAChB,aAAa,OAAO;AAAA,IACtB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,iCAAiC,MAAM;AAC/B,UAAA,WAAW,KAAK,yBAAyB,IAAI;AAC7C,UAAA,aAAa,KAAK,oBAAoB,IAAI;AAC1C,UAAA,kBAAkB,KAAK,yBAAyB,IAAI;AAE1D,WAAQ,WAAW,MAAM,aAAa,OAAO,kBAAkB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMjE,oBAAoB,MAAM;AAClB,UAAA,QAAQ,KAAK,uBAAuB,IAAI;AAC9C,UAAM,kBAAkB;AAAA,MACtB,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,YAAY;AAAA,IACd;AAEO,WAAA,gBAAgB,MAAM,QAAQ,KAAK;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM5C,yBAAyB,MAAM;AACvB,UAAA,qBAAqB,KAAK,0BAA0B,IAAI;AAGxD,UAAA,UAAU,EAAE,QAAQ,KAAK,QAAQ,KAAK,SAAS,KAAK,UAAU,IAAI;AACxE,QAAI,gBAAgB;AACpB,QAAI,cAAc;AAEX,WAAA,QAAQ,kBAAkB,EAAE,QAAQ,CAAC,CAAC,OAAO,QAAQ,MAAM;AAC1D,YAAA,SAAS,QAAQ,KAAK,KAAK;AACjC,uBAAiB,SAAS,WAAW;AACtB,qBAAA;AAAA,IAAA,CAChB;AAEM,WAAA,cAAc,IAAI,gBAAgB,cAAc;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMzD,yBAAyB,MAAM;AACtB,WAAA,KAAK,yBAAyB,IAAI;AAAA,EAAA;AAAA,EAG3C,yBAAyB,MAAM;AACvB,UAAA,qBAAqB,KAAK,UAAU,OAAO,OAAK,EAAE,gBAAgB,YAAY,KAAK,CAAC;AACtF,QAAA,mBAAmB,WAAW,EAAU,QAAA;AAE5C,UAAM,UAAU,mBAAmB,OAAO,CAAK,MAAA,EAAE,OAAO,EAAE;AAC1D,WAAO,UAAU,mBAAmB;AAAA,EAAA;AAAA,EAGtC,uBAAuB,MAAM;AAC3B,UAAM,kBAAkB,KAAK,UAAU,OAAO,OAAK,CAAC,cAAc,WAAW,EAAE,SAAS,EAAE,WAAW,CAAC,KAAK,CAAC;AACxG,QAAA,gBAAgB,WAAW,EAAU,QAAA;AAEzC,UAAM,UAAU,gBAAgB,OAAO,CAAK,MAAA,EAAE,OAAO,EAAE;AACvD,WAAO,UAAU,gBAAgB;AAAA,EAAA;AAAA,EAGnC,sBAAsB,MAAM;AACnB,WAAA,KAAK,wBAAwB,IAAI,EAAE;AAAA,EAAA;AAAA,EAG5C,uBAAuB,MAAM;AAC3B,UAAM,mBAAmB,KAAK,UAAU,OAAO,OAAK,CAAC,WAAW,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC,KAAK,CAAC;AACnG,QAAA,iBAAiB,WAAW,EAAU,QAAA;AAE1C,UAAM,UAAU,iBAAiB,OAAO,CAAK,MAAA,EAAE,OAAO,EAAE;AACxD,WAAO,UAAU,iBAAiB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMpC,6BAA6B,MAAM;AAE1B,WAAA;AAAA,MACL,cAAc;AAAA,MACd,eAAe;AAAA,MACf,WAAW;AAAA,IACb;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,gCAAgC,MAAM;AAC9B,UAAA,WAAW,KAAK,yBAAyB,IAAI;AAC7C,UAAA,QAAQ,KAAK,uBAAuB,IAAI;AAE9C,UAAM,kBAAkB,CAAC;AAEzB,QAAI,WAAW,KAAK;AAClB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,MAAA,CACT;AAAA,IAAA;AAGC,QAAA,MAAM,UAAU,KAAM;AACxB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,MAAA,CACT;AAAA,IAAA;AAGI,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,2BAA2B,MAAM;AACxB,WAAA;AAAA,MACL,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,IACpB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,0BAA0B,OAAO;AAC/B,UAAM,SAAS,MAAM,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AAClC,WAAA;AAAA,MACL,KAAK,KAAK,IAAI,GAAG,KAAK;AAAA,MACtB,KAAK,KAAK,IAAI,GAAG,KAAK;AAAA,MACtB,QAAQ,OAAO,KAAK,MAAM,OAAO,SAAS,CAAC,CAAC;AAAA,MAC5C,IAAI,OAAO,KAAK,MAAM,OAAO,SAAS,IAAI,CAAC;AAAA,MAC3C,IAAI,OAAO,KAAK,MAAM,OAAO,SAAS,IAAI,CAAC;AAAA,IAC7C;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,qBAAqB;AACZ,WAAA;AAAA,MACL,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,WAAW;AAAA,MACX,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,yBAAyB;AAAA,MACzB,kBAAkB,EAAE,SAAS,KAAM,UAAU,UAAU;AAAA,MACvD,gBAAgB,CAAC;AAAA,MACjB,oBAAoB,CAAC;AAAA,MACrB,mBAAmB,EAAE,UAAU,KAAK,YAAY,SAAS;AAAA,MACzD,kBAAkB,EAAE,OAAO,qBAAqB,aAAa,EAAE;AAAA,MAC/D,eAAe,EAAE,WAAW,KAAK,gBAAgB,CAAA,EAAG;AAAA,MACpD,iBAAiB;AAAA,QACf,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,kBAAkB;AAAA,MACpB;AAAA,MACA,uBAAuB;AAAA,QACrB,cAAc;AAAA,QACd,eAAe;AAAA,QACf,WAAW;AAAA,MACb;AAAA,MACA,yBAAyB,CAAC;AAAA,MAC1B,mBAAmB;AAAA,QACjB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,MAAA;AAAA,IAEtB;AAAA,EAAA;AAEJ;ACvbO,MAAM,4BAA4B;AAAA,EACvC,cAAc;AACZ,SAAK,cAAc;AAAA,MACjB,oBAAoB,IAAI,4BAA4B;AAAA,MACpD,gBAAgB,IAAI,wBAAwB;AAAA,MAC5C,kBAAkB,IAAI,0BAA0B;AAAA,MAChD,uBAAuB,IAAI,+BAA+B;AAAA,MAC1D,cAAc,IAAI,sBAAsB;AAAA,MACxC,kBAAkB,IAAI,0BAA0B;AAAA,MAChD,kBAAkB,IAAI,0BAA0B;AAAA,MAChD,kBAAkB,IAAI,0BAA0B;AAAA,MAChD,eAAe,IAAI,uBAAuB;AAAA,MAC1C,oBAAoB,IAAI,4BAA4B;AAAA,IACtD;AAEA,SAAK,kBAAkB,CAAC;AACxB,SAAK,iBAAiB;AACtB,SAAK,sBAAsB;AAC3B,SAAK,mBAAmB;AAGxB,SAAK,qBAAqB;AAAA,MACxB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA;AAAA,MAG3B,iBAAiB;AAAA,QACf,iBAAiB;AAAA,UACf,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,gBAAgB;AAAA,UAChB,UAAU,CAAC,qBAAqB,kBAAkB;AAAA,QACpD;AAAA,QACA,gBAAgB;AAAA,UACd,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,gBAAgB;AAAA,UAChB,UAAU,CAAC,mBAAmB,gBAAgB;AAAA,QAChD;AAAA,QACA,mBAAmB;AAAA,UACjB,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,gBAAgB;AAAA,UAChB,UAAU,CAAC,mBAAmB,kBAAkB,gBAAgB;AAAA,QAClE;AAAA,QACA,qBAAqB;AAAA,UACnB,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,gBAAgB;AAAA,UAChB,UAAU,CAAC,kBAAkB,wBAAwB;AAAA,QACvD;AAAA,QACA,mBAAmB;AAAA,UACjB,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,gBAAgB;AAAA,UAChB,UAAU,CAAC,wBAAwB,iBAAiB;AAAA,QACtD;AAAA,QACA,qBAAqB;AAAA,UACnB,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,gBAAgB;AAAA,UAChB,UAAU,CAAC,sBAAsB,mBAAmB;AAAA,QAAA;AAAA,MACtD;AAAA,IAEJ;AAGA,SAAK,qBAAqB;AAAA,MACxB,qBAAqB,CAAC;AAAA,MACtB,qBAAqB,CAAC;AAAA,MACtB,mBAAmB,CAAC;AAAA,MACpB,yBAAyB,CAAA;AAAA,IAC3B;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMd,MAAM,oBAAoB,UAAU;AAC9B,QAAA;AACF,cAAQ,IAAI,uDAAuD;AAEnE,UAAI,CAAC,KAAK,iBAAiB,QAAQ,GAAG;AAC9B,cAAA,IAAI,MAAM,yCAAyC;AAAA,MAAA;AAGrD,YAAA,YAAY,KAAK,IAAI;AAGrB,YAAA,gBAAgB,KAAK,uBAAuB,QAAQ;AAG1D,YAAM,mBAAmB,MAAM,KAAK,kBAAkB,QAAQ;AAG9D,YAAM,mBAAmB;AAAA,QACvB,KAAK,WAAW,mBAAmB,QAAQ,cAAc,kBAAkB;AAAA,QAC3E,KAAK,WAAW,eAAe,QAAQ,cAAc,cAAc;AAAA,QACnE,KAAK,WAAW,iBAAiB,QAAQ,cAAc,gBAAgB;AAAA,QACvE,KAAK,WAAW,sBAAsB,QAAQ,cAAc,qBAAqB;AAAA,QACjF,KAAK,WAAW,aAAa,QAAQ,cAAc,YAAY;AAAA,QAC/D,KAAK,WAAW,iBAAiB,QAAQ,cAAc,UAAU;AAAA,QACjE,KAAK,WAAW,iBAAiB,QAAQ,cAAc,QAAQ;AAAA,QAC/D,KAAK,WAAW,iBAAiB,QAAQ,cAAc,UAAU;AAAA,QACjE,KAAK,WAAW,cAAc,QAAQ,cAAc,KAAK;AAAA,QACzD,KAAK,WAAW,mBAAmB,QAAQ,cAAc,OAAO;AAAA,MAClE;AAEM,YAAA;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA,IACE,MAAM,QAAQ,IAAI,gBAAgB;AAGhC,YAAA,qBAAqB,KAAK,2BAA2B;AAAA,QACzD,oBAAoB;AAAA,QACpB,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,QAClB,uBAAuB;AAAA,QACvB,cAAc;AAAA,QACd,kBAAkB;AAAA,QAClB,kBAAkB;AAAA,QAClB,kBAAkB;AAAA,QAClB,eAAe;AAAA,QACf,oBAAoB;AAAA,QACpB;AAAA,SACC,QAAQ;AAGX,YAAM,mBAAmB,KAAK,4BAA4B,oBAAoB,QAAQ;AAGtF,YAAM,oBAAoB,KAAK,4BAA4B,oBAAoB,QAAQ;AAGvF,YAAM,qBAAqB,KAAK,2BAA2B,oBAAoB,gBAAgB;AAG/F,YAAM,0BAA0B,KAAK,gCAAgC,oBAAoB,gBAAgB;AAEnG,YAAA,eAAe,KAAK,IAAA,IAAQ;AAElC,YAAM,mBAAmB;AAAA,QACvB,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAClC,QAAQ;AAAA,QACR,WAAW,SAAS;AAAA,QACpB;AAAA,QACA,SAAS;AAAA;AAAA,QAGT,iBAAiB;AAAA,UACf,oBAAoB;AAAA,UACpB,gBAAgB;AAAA,UAChB,kBAAkB;AAAA,UAClB,uBAAuB;AAAA,UACvB,cAAc;AAAA,UACd,kBAAkB;AAAA,UAClB,kBAAkB;AAAA,UAClB,kBAAkB;AAAA,UAClB,eAAe;AAAA,UACf,oBAAoB;AAAA,QACtB;AAAA;AAAA,QAGA;AAAA;AAAA,QAGA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAGA,UAAU;AAAA,UACR,eAAe,SAAS,UAAU,UAAU;AAAA,UAC5C,kBAAkB,KAAK,oBAAoB,QAAQ;AAAA,UACnD,YAAY,SAAS;AAAA,UACrB,UAAU,KAAK,yBAAyB,QAAQ;AAAA,UAChD,iBAAiB,KAAK,6BAA6B,QAAQ;AAAA,UAC3D,aAAa,KAAK,kBAAkB,QAAQ;AAAA,UAC5C,iBAAiB,KAAK,yBAAyB,QAAQ;AAAA,UACvD,eAAe,KAAK,sBAAsB,QAAQ;AAAA,QAAA;AAAA,MAEtD;AAGK,WAAA,gBAAgB,KAAK,gBAAgB;AAG1C,WAAK,yBAAyB,gBAAgB;AAEtC,cAAA,IAAI,4CAA4C,YAAY,IAAI;AAEjE,aAAA;AAAA,aAEAA,QAAO;AACN,cAAA,MAAM,iDAAiDA,MAAK;AAC7D,aAAA,KAAK,mBAAmBA,QAAO,QAAQ;AAAA,IAAA;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA,EAMF,MAAM,gBAAgB,UAAU;AACvB,WAAA,MAAM,KAAK,oBAAoB,QAAQ;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMhD,iBAAiB,UAAU;AACzB,QAAI,CAAC,UAAU;AACb,cAAQ,KAAK,2DAA2D;AACjE,aAAA;AAAA,IAAA;AAGL,QAAA,CAAC,SAAS,YAAY,CAAC,MAAM,QAAQ,SAAS,QAAQ,GAAG;AAC3D,cAAQ,KAAK,iEAAiE;AACvE,aAAA;AAAA,IAAA;AAGT,QAAI,SAAS,SAAS,SAAS,KAAK,mBAAmB,aAAa;AAClE,cAAQ,KAAK,mEAAmE,SAAS,SAAS,MAAM,GAAG;AACpG,aAAA;AAAA,IAAA;AAGH,UAAA,gBAAgB,SAAS,SAAS;AAAA,MAAM,CAC5C,YAAA,OAAO,QAAQ,kBAAkB,YACjC,OAAO,QAAQ,eAAe,YAC9B,OAAO,QAAQ,iBAAiB,YAChC,OAAO,QAAQ,cAAc;AAAA,IAC/B;AAEA,QAAI,CAAC,eAAe;AAClB,cAAQ,KAAK,gEAAgE;AACtE,aAAA;AAAA,IAAA;AAGF,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,MAAM,kBAAkB,UAAU;AAChC,YAAQ,IAAI,mCAAmC;AAE/C,UAAM,eAAe,CAAC;AAChB,UAAA,mBAAmB,KAAK,oBAAoB,QAAQ;AAE1D,eAAW,YAAY,kBAAkB;AACvC,YAAM,mBAAmB,KAAK,oBAAoB,UAAU,QAAQ;AAEhE,UAAA,iBAAiB,SAAS,GAAG;AAC/B,qBAAa,QAAQ,IAAI;AAAA,UACvB,eAAe,iBAAiB;AAAA,UAChC,gBAAgB,iBAAiB,OAAO,CAAK,MAAA,EAAE,SAAS,EAAE;AAAA,UAC1D,UAAU,KAAK,0BAA0B,gBAAgB;AAAA,UACzD,qBAAqB,KAAK,6BAA6B,gBAAgB;AAAA,UACvE,eAAe,KAAK,uBAAuB,gBAAgB;AAAA,UAC3D,eAAe,KAAK,8BAA8B,kBAAkB,QAAQ;AAAA,UAC5E,kBAAkB,KAAK,kCAAkC,kBAAkB,QAAQ;AAAA,UACnF,uBAAuB,KAAK,6BAA6B,gBAAgB;AAAA,UACzE,sBAAsB,KAAK,4BAA4B,gBAAgB;AAAA,QACzE;AAAA,MAAA;AAAA,IACF;AAGK,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,oBAAoB,UAAU;AAC5B,QAAI,CAAC,YAAY,CAAC,SAAS,iBAAiB,CAAC;AAEvC,UAAA,iCAAiB,IAAI;AAClB,aAAA,SAAS,QAAQ,CAAW,YAAA;AACnC,UAAI,QAAQ,cAAc;AACb,mBAAA,IAAI,QAAQ,YAAY;AAAA,MAAA;AAAA,IACrC,CACD;AAEM,WAAA,MAAM,KAAK,UAAU;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM9B,oBAAoB,UAAU,UAAU;AACtC,QAAI,CAAC,YAAY,CAAC,SAAS,iBAAiB,CAAC;AAC7C,WAAO,SAAS,SAAS,OAAO,CAAW,YAAA,QAAQ,iBAAiB,QAAQ;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM9E,0BAA0B,UAAU;AAClC,QAAI,CAAC,YAAY,CAAC,SAAS,iBAAiB,CAAC;AAE7C,UAAM,oBAAoB,CAAC;AAE3B,aAAS,SAAS,QAAQ,CAAC,SAAS,UAAU;AAC5C,UAAI,QAAQ,gBAAgB,QAAQ,eAAe,KAAO;AACxD,0BAAkB,KAAK;AAAA,UACrB,cAAc;AAAA,UACd,MAAM;AAAA,UACN,UAAU,QAAQ;AAAA,UAClB,WAAW,QAAQ;AAAA,QAAA,CACpB;AAAA,MAAA;AAGC,UAAA,QAAQ,KAAK,CAAC,QAAQ,aAAa,CAAC,SAAS,SAAS,QAAQ,CAAC,EAAE,WAAW;AAC9E,0BAAkB,KAAK;AAAA,UACrB,cAAc;AAAA,UACd,MAAM;AAAA,UACN,WAAW,QAAQ;AAAA,QAAA,CACpB;AAAA,MAAA;AAAA,IACH,CACD;AAEM,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,sBAAsB,UAAU;AAC9B,QAAI,CAAC,YAAY,CAAC,SAAS,UAAU;AACnC,aAAO,EAAE,iBAAiB,GAAG,aAAa,GAAG,eAAe,EAAE;AAAA,IAAA;AAG1D,UAAA,gBAAgB,SAAS,SAC5B,OAAO,CAAA,YAAW,QAAQ,YAAY,EACtC,IAAI,CAAW,YAAA,QAAQ,YAAY;AAEtC,UAAM,kBAAkB,cAAc,SAAS,IAC3C,cAAc,OAAO,CAAC,KAAK,SAAS,MAAM,MAAM,CAAC,IAAI,cAAc,SACnE;AAEJ,UAAM,WAAW,cAAc,SAAS,IACpC,cAAc,OAAO,CAAC,KAAK,SAAS,MAAM,KAAK,IAAI,OAAO,iBAAiB,CAAC,GAAG,CAAC,IAAI,cAAc,SAClG;AAEE,UAAA,cAAc,WAAW,IAAI,KAAK,IAAI,KAAK,KAAK,QAAQ,IAAI,mBAAmB;AAE/E,UAAA,gBAAgB,KAAK,uBAAuB,QAAQ;AAEnD,WAAA;AAAA,MACL;AAAA,MACA,aAAa,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,WAAW,CAAC;AAAA,MACjD;AAAA,IACF;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,uBAAuB,UAAU;AAC/B,QAAI,CAAC,YAAY,CAAC,SAAS,SAAiB,QAAA;AAE5C,QAAI,UAAU;AACd,QAAI,cAAc;AAET,aAAA,SAAS,QAAQ,CAAW,YAAA;AACnC,UAAI,QAAQ,gBAAgB,QAAQ,eAAe,KAAM;AACvD;AACU,kBAAA,KAAK,IAAI,SAAS,WAAW;AAAA,MAAA,OAClC;AACS,sBAAA;AAAA,MAAA;AAAA,IAChB,CACD;AAEM,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,uBAAuB,UAAU;AAC/B,QAAI,CAAC,YAAY,CAAC,SAAS,SAAiB,QAAA;AAE5C,QAAI,kBAAkB;AAEb,aAAA,SAAS,QAAQ,CAAW,YAAA;AACnC,UAAI,QAAQ,WAAW,QAAQ,QAAQ,SAAS,EAAsB,oBAAA;AACtE,UAAI,QAAQ,YAAY,QAAQ,SAAS,SAAS,EAAsB,oBAAA;AACxE,UAAI,QAAQ,kBAAkB,QAAQ,iBAAiB,GAAuB,oBAAA;AAC9E,UAAI,QAAQ,cAAc,QAAQ,aAAa,EAAsB,oBAAA;AAAA,IAAA,CACtE;AAEK,UAAA,gBAAgB,SAAS,SAAS,SAAS,IAAI,kBAAkB,SAAS,SAAS,SAAS;AAE9F,QAAA,gBAAgB,EAAU,QAAA;AAC1B,QAAA,gBAAgB,IAAY,QAAA;AACzB,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,uBAAuB,UAAU;AAC/B,QAAI,CAAC,YAAY,CAAC,SAAS,iBAAiB,CAAC;AAE7C,UAAM,WAAW,CAAC;AAET,aAAA,SAAS,QAAQ,CAAW,YAAA;AACnC,UAAI,QAAQ,UAAU;AACX,iBAAA,KAAK,QAAQ,QAAQ;AAAA,MAAA;AAEhC,UAAI,QAAQ,oBAAoB;AACrB,iBAAA,KAAK,QAAQ,kBAAkB;AAAA,MAAA;AAAA,IAC1C,CACD;AAEM,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,sBAAsB,UAAU;AAC9B,QAAI,CAAC,YAAY,CAAC,SAAS,SAAU,QAAO,CAAC,gBAAgB;AAEvD,UAAA,qCAAqB,IAAI;AAEtB,aAAA,SAAS,QAAQ,CAAW,YAAA;AAC/B,UAAA,QAAQ,iBAAiB,qBAAqB;AAChD,uBAAe,IAAI,uBAAuB;AAAA,MAAA;AAExC,UAAA,QAAQ,iBAAiB,uBAAuB;AAClD,uBAAe,IAAI,sBAAsB;AAAA,MAAA;AAEvC,UAAA,QAAQ,iBAAiB,qBAAqB;AAChD,uBAAe,IAAI,sBAAsB;AAAA,MAAA;AAEvC,UAAA,QAAQ,iBAAiB,uBAAuB;AAClD,uBAAe,IAAI,mBAAmB;AAAA,MAAA;AAExC,UAAI,QAAQ,aAAa,QAAQ,eAAe,KAAM;AACpD,uBAAe,IAAI,qBAAqB;AAAA,MAAA;AAAA,IAC1C,CACD;AAEG,QAAA,eAAe,SAAS,GAAG;AAC7B,qBAAe,IAAI,gBAAgB;AAAA,IAAA;AAG9B,WAAA,MAAM,KAAK,cAAc;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMlC,wBAAwB,UAAU;AAChC,QAAI,CAAC,YAAY,CAAC,SAAS,SAAiB,QAAA;AAE5C,QAAI,kBAAkB;AAEb,aAAA,SAAS,QAAQ,CAAW,YAAA;AAC/B,UAAA,QAAQ,WAAY,oBAAmB,QAAQ;AAC/C,UAAA,QAAQ,WAAW,QAAQ,QAAQ,KAAK,CAAK,MAAA,IAAI,EAAE,EAAsB,oBAAA;AAC7E,UAAI,QAAQ,kBAAkB,QAAQ,iBAAiB,EAAsB,oBAAA;AAAA,IAAA,CAC9E;AAEK,UAAA,gBAAgB,SAAS,SAAS,SAAS,IAAI,kBAAkB,SAAS,SAAS,SAAS;AAE9F,QAAA,gBAAgB,EAAU,QAAA;AAC1B,QAAA,gBAAgB,EAAU,QAAA;AACvB,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,iBAAiB,UAAU;AACzB,QAAI,CAAC,YAAY,CAAC,SAAS,iBAAiB,CAAC;AAE7C,UAAM,aAAa,CAAC;AAEX,aAAA,SAAS,QAAQ,CAAW,YAAA;AAC/B,UAAA,CAAC,QAAQ,WAAW;AAChB,cAAA,YAAY,KAAK,kBAAkB,OAAO;AAChD,mBAAW,SAAS,KAAK,WAAW,SAAS,KAAK,KAAK;AAAA,MAAA;AAAA,IACzD,CACD;AAEM,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,kBAAkB,SAAS;AACzB,QAAI,CAAC,QAAQ,UAAU,CAAC,QAAQ,YAAoB,QAAA;AAEpD,UAAM,aAAa,KAAK,IAAI,QAAQ,SAAS,QAAQ,WAAW;AAE5D,QAAA,eAAe,EAAU,QAAA;AACzB,QAAA,cAAc,EAAU,QAAA;AAC5B,QAAI,QAAQ,SAAS,QAAQ,cAAc,EAAU,QAAA;AACrD,QAAI,QAAQ,SAAS,QAAQ,cAAc,EAAU,QAAA;AACjD,QAAA,aAAa,GAAW,QAAA;AAErB,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,wBAAwB,UAAU;AAChC,QAAI,CAAC,YAAY,CAAC,SAAS,iBAAiB,CAAC;AAE7C,UAAM,mBAAmB;AAAA,MACvB,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,IACpB;AAEA,aAAS,IAAI,GAAG,IAAI,SAAS,SAAS,QAAQ,KAAK;AACjD,YAAM,cAAc,SAAS,SAAS,IAAI,CAAC;AACrC,YAAA,iBAAiB,SAAS,SAAS,CAAC;AAE1C,UAAI,CAAC,YAAY,aAAa,eAAe,WAAW;AACrC,yBAAA;AAAA,iBACR,CAAC,YAAY,aAAa,CAAC,eAAe,WAAW;AAC7C,yBAAA;AAAA,MAAA;AAAA,IACnB;AAGK,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,uBAAuB,UAAU;AAC/B,UAAM,WAAW;AAAA,MACf,UAAU,SAAS;AAAA,MACnB,WAAW,SAAS;AAAA,MACpB,YAAY,SAAS;AAAA,MACrB,kBAAkB,KAAK,oBAAoB,QAAQ;AAAA,MACnD,iBAAiB,SAAS,mBAAmB;AAAA,MAC7C,WAAW,KAAK,IAAI;AAAA,IACtB;AAEO,WAAA;AAAA,MACL,oBAAoB;AAAA,QAClB,GAAG;AAAA,QACH,OAAO;AAAA,QACP,gBAAgB,KAAK,sBAAsB,QAAQ;AAAA,QACnD,gBAAgB,KAAK,sBAAsB,QAAQ;AAAA,MACrD;AAAA,MACA,gBAAgB;AAAA,QACd,GAAG;AAAA,QACH,OAAO;AAAA,QACP,mBAAmB,KAAK,0BAA0B,QAAQ;AAAA,QAC1D,cAAc,KAAK,sBAAsB,QAAQ;AAAA,MACnD;AAAA,MACA,kBAAkB;AAAA,QAChB,GAAG;AAAA,QACH,OAAO;AAAA,QACP,kBAAkB,KAAK,uBAAuB,QAAQ;AAAA,QACtD,iBAAiB,KAAK,uBAAuB,QAAQ;AAAA,MACvD;AAAA,MACA,uBAAuB;AAAA,QACrB,GAAG;AAAA,QACH,OAAO;AAAA,QACP,gBAAgB,KAAK,sBAAsB,QAAQ;AAAA,QACnD,mBAAmB,KAAK,wBAAwB,QAAQ;AAAA,MAC1D;AAAA,MACA,cAAc;AAAA,QACZ,GAAG;AAAA,QACH,OAAO;AAAA,QACP,YAAY,KAAK,iBAAiB,QAAQ;AAAA,QAC1C,kBAAkB,KAAK,wBAAwB,QAAQ;AAAA,MACzD;AAAA,MACA,YAAY;AAAA,QACV,GAAG;AAAA,QACH,kBAAkB,KAAK,iBAAiB,UAAU,mBAAmB;AAAA,QACrE,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,GAAG;AAAA,QACH,oBAAoB,KAAK,iBAAiB,UAAU,qBAAqB;AAAA,QACzE,OAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,QACV,GAAG;AAAA,QACH,kBAAkB,KAAK,iBAAiB,UAAU,mBAAmB;AAAA,QACrE,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,GAAG;AAAA,QACH,eAAe,KAAK,iBAAiB,UAAU,gBAAgB;AAAA,QAC/D,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,GAAG;AAAA,QACH,oBAAoB,KAAK,iBAAiB,UAAU,qBAAqB;AAAA,QACzE,OAAO;AAAA,MAAA;AAAA,IAEX;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,iBAAiB,UAAU,cAAc;AACvC,QAAI,CAAC,SAAS,SAAU,QAAO,CAAC;AAChC,WAAO,SAAS,SAAS,OAAO,CAAW,YAAA,QAAQ,iBAAiB,YAAY;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMlF,sBAAsB,UAAU;AAC9B,QAAI,CAAC,YAAY,CAAC,SAAS,UAAU;AACnC,aAAO,EAAE,KAAK,GAAG,KAAK,GAAG;AAAA,IAAA;AAG3B,QAAI,WAAW;AACf,QAAI,WAAW;AAEN,aAAA,SAAS,QAAQ,CAAW,YAAA;AACnC,UAAI,QAAQ,WAAW,MAAM,QAAQ,QAAQ,OAAO,GAAG;AAC7C,gBAAA,QAAQ,QAAQ,CAAO,QAAA;AACzB,cAAA,OAAO,QAAQ,UAAU;AAChB,uBAAA,KAAK,IAAI,UAAU,GAAG;AACtB,uBAAA,KAAK,IAAI,UAAU,GAAG;AAAA,UAAA;AAAA,QACnC,CACD;AAAA,MAAA;AAGC,UAAA,OAAO,QAAQ,WAAW,UAAU;AACtC,mBAAW,KAAK,IAAI,UAAU,QAAQ,MAAM;AAC5C,mBAAW,KAAK,IAAI,UAAU,QAAQ,MAAM;AAAA,MAAA;AAG1C,UAAA,OAAO,QAAQ,gBAAgB,UAAU;AAC3C,mBAAW,KAAK,IAAI,UAAU,QAAQ,WAAW;AACjD,mBAAW,KAAK,IAAI,UAAU,QAAQ,WAAW;AAAA,MAAA;AAAA,IACnD,CACD;AAED,WAAO,aAAa,YAAY,aAAa,YAAY,EAAE,KAAK,GAAG,KAAK,GAAO,IAAA,EAAE,KAAK,UAAU,KAAK,SAAS;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMhH,sBAAsB,UAAU;AAC9B,QAAI,CAAC,YAAY,CAAC,SAAS,UAAU;AACnC,aAAO,CAAC,UAAU;AAAA,IAAA;AAGd,UAAA,qCAAqB,IAAI;AAEtB,aAAA,SAAS,QAAQ,CAAW,YAAA;AACnC,UAAI,QAAQ,aAA6B,gBAAA,IAAI,QAAQ,YAAY;AACjE,UAAI,QAAQ,cAA8B,gBAAA,IAAI,QAAQ,aAAa;AAC/D,UAAA,QAAQ,WAAW,QAAQ,QAAQ,SAAS,EAAG,gBAAe,IAAI,YAAY;AAClF,UAAI,QAAQ,aAA6B,gBAAA,IAAI,YAAY;AACzD,UAAI,QAAQ,YAAY,QAAQ,UAAW,gBAAe,IAAI,mBAAmB;AACjF,UAAI,QAAQ,SAAyB,gBAAA,IAAI,qBAAqB;AAAA,IAAA,CAC/D;AAEM,WAAA,MAAM,KAAK,cAAc;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMlC,wBAAwB,UAAU;AAChC,QAAI,CAAC,YAAY,CAAC,SAAS,UAAU;AACnC,aAAO,EAAE,UAAU,IAAI,WAAW,CAAA,EAAG;AAAA,IAAA;AAGvC,UAAM,WAAW,CAAC;AAClB,UAAM,YAAY,CAAC;AAEV,aAAA,SAAS,QAAQ,CAAW,YAAA;AACnC,UAAI,QAAQ,iBAAiB;AAClB,iBAAA,KAAK,QAAQ,eAAe;AACrC,kBAAU,QAAQ,eAAe,KAAK,UAAU,QAAQ,eAAe,KAAK,KAAK;AAAA,MAAA;AAGnF,UAAI,QAAQ,gBAAgB,QAAQ,eAAe,KAAM;AACvD,iBAAS,KAAK,gBAAgB;AAC9B,kBAAU,gBAAgB,KAAK,UAAU,gBAAgB,KAAK,KAAK;AAAA,MAAA;AAGrE,UAAI,QAAQ,cAAc,QAAQ,aAAa,KAAK;AAClD,iBAAS,KAAK,gBAAgB;AAC9B,kBAAU,gBAAgB,KAAK,UAAU,gBAAgB,KAAK,KAAK;AAAA,MAAA;AAAA,IACrE,CACD;AAEM,WAAA,EAAE,UAAU,UAAU;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM/B,6BAA6B,UAAU;AACrC,QAAI,CAAC,YAAY,CAAC,SAAS,UAAU;AACnC,aAAO,EAAE,QAAQ,IAAI,aAAa,SAAS;AAAA,IAAA;AAG7C,UAAM,SAAS,CAAC;AAChB,QAAI,YAAY;AAChB,QAAI,kBAAkB;AAEb,aAAA,SAAS,QAAQ,CAAW,YAAA;AAC/B,UAAA,QAAQ,eAAe,QAAW;AAC7B,eAAA,KAAK,QAAQ,UAAU;AAE9B,YAAI,cAAc,MAAM;AAClB,cAAA,QAAQ,aAAa,WAAW;AAChB,8BAAA;AAAA,UAAA,WACT,QAAQ,aAAa,WAAW;AACvB,8BAAA;AAAA,UAAA;AAAA,QACpB;AAGF,oBAAY,QAAQ;AAAA,MAAA;AAAA,IACtB,CACD;AAEM,WAAA,EAAE,QAAQ,aAAa,gBAAgB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMhD,0BAA0B,UAAU;AAClC,QAAI,CAAC,YAAY,CAAC,SAAS,UAAU;AAC5B,aAAA;AAAA,QACL,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,eAAe;AAAA,QACf,iBAAiB;AAAA,MACnB;AAAA,IAAA;AAGI,UAAA,gBAAgB,SAAS,SAAS;AACxC,QAAI,kBAAkB;AACtB,QAAI,oBAAoB;AACxB,QAAI,oBAAoB;AAEf,aAAA,SAAS,QAAQ,CAAW,YAAA;AACnC,UAAI,QAAQ,UAAW;AACvB,UAAI,QAAQ,gBAAgB,OAAO,QAAQ,iBAAiB,UAAU;AACpE,6BAAqB,QAAQ;AAC7B;AAAA,MAAA;AAAA,IACF,CACD;AAED,UAAM,WAAW,gBAAgB,IAAI,kBAAkB,gBAAgB;AACvE,UAAM,sBAAsB,oBAAoB,IAAI,oBAAoB,oBAAoB;AAErF,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,yBAAyB,UAAU;AAC3B,UAAA,UAAU,KAAK,0BAA0B,QAAQ;AACvD,WAAO,QAAQ;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,6BAA6B,UAAU;AAC/B,UAAA,UAAU,KAAK,0BAA0B,QAAQ;AACvD,WAAO,QAAQ;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,kBAAkB,UAAU;AAC1B,QAAI,CAAC,YAAY,CAAC,SAAS,SAAiB,QAAA;AAEtC,UAAA,gBAAgB,SAAS,SAAS;AAAA,MAAM,CAAA,YAC5C,QAAQ,kBAAkB,UAC1B,QAAQ,eAAe,UACvB,QAAQ,iBAAiB,UACzB,QAAQ,cAAc;AAAA,IACxB;AAEA,UAAM,qBAAqB,SAAS,SAAS,UAAU,KAAK,mBAAmB;AACzE,UAAA,UAAU,IAAI,IAAI,SAAS,SAAS,IAAI,CAAA,MAAK,EAAE,YAAY,CAAC,EAAE,OAAO;AAEvE,QAAA,iBAAiB,sBAAsB,QAAgB,QAAA;AACvD,QAAA,iBAAiB,mBAA2B,QAAA;AACzC,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,yBAAyB,UAAU;AACjC,QAAI,CAAC,YAAY,CAAC,SAAS,SAAiB,QAAA;AAEtC,UAAA,gBAAgB,SAAS,SAAS;AAClC,UAAA,gBAAgB,SAAS,SAC5B,OAAO,CAAA,MAAK,EAAE,YAAY,EAC1B,IAAI,CAAK,MAAA,EAAE,YAAY;AAC1B,UAAM,kBAAkB,cAAc,SAAS,IAC3C,cAAc,OAAO,CAAC,KAAK,SAAS,MAAM,MAAM,CAAC,IAAI,cAAc,SACnE;AAEJ,UAAM,kBAAkB,gBAAgB,OAAO,kBAAkB,MAAO,MAAM;AAC9E,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,eAAe,CAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMjD,sBAAsB,UAAU;AAC9B,QAAI,CAAC,YAAY,CAAC,SAAS,SAAiB,QAAA;AAEtC,UAAA,aAAa,KAAK,wBAAwB,QAAQ;AAClD,UAAA,gBAAgB,SAAS,SAC5B,OAAO,CAAA,MAAK,EAAE,YAAY,EAC1B,IAAI,CAAK,MAAA,EAAE,YAAY;AAC1B,UAAM,kBAAkB,cAAc,SAAS,IAC3C,cAAc,OAAO,CAAC,KAAK,SAAS,MAAM,MAAM,CAAC,IAAI,cAAc,SACnE;AAEJ,QAAI,eAAe,UAAU,kBAAkB,IAAa,QAAA;AAC5D,QAAI,eAAe,YAAY,kBAAkB,IAAa,QAAA;AACvD,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,0BAA0B,kBAAkB;AAC1C,QAAI,CAAC,oBAAoB,iBAAiB,WAAW,EAAU,QAAA;AAC/D,UAAM,UAAU,iBAAiB,OAAO,CAAK,MAAA,EAAE,SAAS,EAAE;AAC1D,WAAO,UAAU,iBAAiB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMpC,6BAA6B,kBAAkB;AAC7C,QAAI,CAAC,oBAAoB,iBAAiB,WAAW,EAAU,QAAA;AACzD,UAAA,QAAQ,iBAAiB,OAAO,CAAK,MAAA,EAAE,YAAY,EAAE,IAAI,CAAK,MAAA,EAAE,YAAY;AAClF,WAAO,MAAM,SAAS,IAAI,MAAM,OAAO,CAAC,KAAK,SAAS,MAAM,MAAM,CAAC,IAAI,MAAM,SAAS;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMxF,uBAAuB,kBAAkB;AACvC,QAAI,CAAC,oBAAoB,iBAAiB,SAAS,UAAU,CAAC;AAE9D,UAAM,QAAQ,CAAC;AACf,QAAI,gBAAgB;AAEpB,qBAAiB,QAAQ,CAAW,YAAA;AAClC,UAAI,QAAQ,WAAW;AACrB;AAAA,MAAA,OACK;AACW,wBAAA;AAAA,MAAA;AAElB,YAAM,KAAK,EAAE,SAAkB,QAAQ,eAAe;AAAA,IAAA,CACvD;AAEM,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,8BAA8B,kBAAkB,UAAU;AACxD,QAAI,CAAC,oBAAoB,iBAAiB,WAAW,UAAU,CAAC;AAEhE,UAAM,WAAW,KAAK,mBAAmB,gBAAgB,QAAQ,GAAG,YAAY,CAAC;AACjF,UAAM,SAAS,iBAAiB,OAAO,CAAA,MAAK,CAAC,EAAE,SAAS,EAAE,IAAI,CAAA,MAAK,KAAK,kBAAkB,CAAC,CAAC;AACrF,WAAA,CAAC,GAAG,oBAAI,IAAI,CAAC,GAAG,UAAU,GAAG,MAAM,CAAC,CAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM9C,kCAAkC,kBAAkB,UAAU;AAC5D,QAAI,CAAC,oBAAoB,iBAAiB,WAAW,UAAU,CAAC;AAEhE,UAAM,SAAS,KAAK,mBAAmB,gBAAgB,QAAQ,KAAK,CAAC;AAC/D,UAAA,WAAW,KAAK,0BAA0B,gBAAgB;AAC1D,UAAA,UAAU,KAAK,6BAA6B,gBAAgB;AAE3D,WAAA;AAAA,MACL,aAAa,OAAO,eAAe;AAAA,MACnC,eAAgB,YAAY,OAAO,kBAAkB,OAAS,KAAK,IAAI,GAAG,IAAI,UAAU,GAAK,KAAK,OAAO,cAAc;AAAA,IACzH;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,6BAA6B,kBAAkB;AAC7C,WAAO,KAAK,6BAA6B,EAAE,UAAU,kBAAkB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMzE,4BAA4B,kBAAkB;AAC5C,QAAI,CAAC,oBAAoB,iBAAiB,WAAW,EAAU,QAAA;AAC/D,UAAM,gBAAgB,iBAAiB;AACjC,UAAA,UAAU,KAAK,6BAA6B,gBAAgB;AAC3D,WAAA,KAAK,IAAI,GAAG,gBAAgB,OAAO,UAAU,MAAO,MAAM,IAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMvE,2BAA2B,SAAS,UAAU;AAC5C,UAAM,aAAa;AAAA,MACjB,iBAAiB,QAAQ,mBAAmB,SAAS;AAAA,MACrD,kBAAkB,QAAQ,eAAe,WAAW,CAAC;AAAA,MACrD,kBAAkB,QAAQ,iBAAiB,cAAc;AAAA,MACzD,kBAAkB,QAAQ,sBAAsB,SAAS;AAAA,MACzD,eAAe,QAAQ,aAAa,YAAY,CAAC;AAAA,MACjD,oBAAoB,QAAQ,iBAAiB,YAAY;AAAA,MACzD,gBAAgB,QAAQ,iBAAiB,SAAS;AAAA,MAClD,kBAAkB,QAAQ,iBAAiB,SAAS;AAAA,MACpD,oBAAoB,QAAQ,cAAc,SAAS;AAAA,MACnD,oBAAoB,QAAQ,mBAAmB,SAAS;AAAA,MACxD,qBAAqB,QAAQ;AAAA,IAC/B;AAEO,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,4BAA4B,oBAAoB,UAAU;AACxD,UAAM,UAAU;AAAA,MACd,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,IACtB;AAEA,QAAI,iBAAiB;AACrB,WAAO,KAAK,OAAO,EAAE,QAAQ,CAAO,QAAA;AAC5B,YAAA,QAAQ,OAAO,mBAAmB,GAAG,MAAM,WAAW,mBAAmB,GAAG,IAAI;AACpE,wBAAA,QAAQ,QAAQ,GAAG;AAAA,IAAA,CACtC;AAEM,WAAA;AAAA,MACL;AAAA,MACA,kBAAkB,KAAK,wBAAwB,QAAQ;AAAA,MACvD,eAAe,KAAK,sBAAsB,QAAQ;AAAA,IACpD;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,4BAA4B,oBAAoB,UAAU;AACxD,UAAM,WAAW,CAAC;AAEd,QAAA,mBAAmB,kBAAkB,KAAK;AAC5C,eAAS,KAAK,yCAAyC;AAAA,IAAA,WAC9C,mBAAmB,kBAAkB,KAAK;AACnD,eAAS,KAAK,kDAAkD;AAAA,IAAA;AAG9D,QAAA,mBAAmB,iBAAiB,cAAc,KAAK;AACzD,eAAS,KAAK,gDAAgD;AAAA,IAAA;AAGhE,QAAI,mBAAmB,cAAc,SAAS,0BAA0B,GAAG;AACzE,eAAS,KAAK,mDAAmD;AAAA,IAAA;AAG5D,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,2BAA2B,oBAAoB,kBAAkB;AACxD,WAAA;AAAA,MACL,WAAW,OAAO,KAAK,mBAAmB,mBAAmB,EAAE,OAAO,CAAA,aACpE,mBAAmB,oBAAoB,QAAQ,EAAE,WAAW,GAAG;AAAA,MACjE,YAAY,OAAO,KAAK,mBAAmB,mBAAmB,EAAE,OAAO,CAAA,aACrE,mBAAmB,oBAAoB,QAAQ,EAAE,WAAW,GAAG;AAAA,MACjE,UAAU,KAAK,yBAAyB,gBAAgB;AAAA,IAC1D;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,gCAAgC,oBAAoB,kBAAkB;AACpE,UAAM,kBAAkB,CAAC;AAEzB,WAAO,KAAK,gBAAgB,EAAE,QAAQ,CAAY,aAAA;AAChD,UAAI,iBAAiB,QAAQ,EAAE,WAAW,KAAK;AAC7B,wBAAA,KAAK,iBAAiB,QAAQ,2BAA2B;AAAA,MAAA;AAE3E,UAAI,iBAAiB,QAAQ,EAAE,sBAAsB,KAAM;AACzC,wBAAA,KAAK,4BAA4B,QAAQ,EAAE;AAAA,MAAA;AAAA,IAC7D,CACD;AAEM,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,yBAAyB,kBAAkB;AACzC,SAAK,mBAAmB;AAAA,MACtB,GAAG,KAAK;AAAA,MACR,cAAc;AAAA,MACd,oBAAoB,KAAK,gBAAgB,IAAI,CAAK,MAAA,EAAE,SAAS,QAAQ;AAAA,IACvE;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,wBAAwB,UAAU;AAC1B,UAAA,UAAU,KAAK,0BAA0B,QAAQ;AACvD,WAAO,QAAQ,WAAW,MAAM,aAAa,QAAQ,WAAW,MAAM,aAAa;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMrF,yBAAyB,kBAAkB;AACzC,UAAM,WAAW,CAAC;AAClB,WAAO,KAAK,gBAAgB,EAAE,QAAQ,CAAY,aAAA;AACvC,eAAA,QAAQ,IAAI,iBAAiB,QAAQ,EAAE,cAAc,MAAM,EAAE,EAAE,CAAC,GAAG,UAAU;AAAA,IAAA,CACvF;AACM,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,mBAAmBA,QAAO,UAAU;AAC3B,WAAA;AAAA,MACL,OAAOA,OAAM;AAAA,MACb,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,QAAQ;AAAA,MACR,WAAW,UAAU,aAAa;AAAA,MAClC,UAAU;AAAA,QACR,eAAe,UAAU,UAAU,UAAU;AAAA,QAC7C,aAAa;AAAA,MAAA;AAAA,IAEjB;AAAA,EAAA;AAEJ;AC3kCO,MAAM,kCAAkC,eAAe;AAAA,EAC5D,YAAY,QAAQ;AAElB,UAAM,SAAS;AAAA,MACb,UAAU;AAAA,MACV,kBAAkB,CAAC,uBAAuB,mBAAmB,wBAAwB;AAAA,MACrF,gBAAgB,CAAC,wBAAwB,sBAAsB,QAAQ;AAAA,MACvE,YAAY;AAAA,QACV,UAAU;AAAA,QACV,cAAc;AAAA,QACd,YAAY;AAAA,MAAA;AAAA,IAEhB;AAEA,UAAM,MAAM;AACP,SAAA,SAAS,UAAU,KAAK;AAC7B,SAAK,WAAW;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASlB,MAAM,gBAAgB,UAAU,gBAAgB,MAAM;AAChD,QAAA;AACG,WAAA,QAAQ,KAAK,wCAAwC;AAAA,QACxD,WAAW,SAAS;AAAA,QACpB,QAAQ,SAAS;AAAA,QACjB,kBAAkB,gBAAgB,OAAO,KAAK,cAAc,cAAc,CAAA,CAAE,EAAE,SAAS;AAAA,MAAA,CACxF;AAGD,YAAM,UAAU,MAAM,KAAK,8BAA8B,UAAU,QAAQ;AAG3E,YAAM,sBAAsB,KAAK,4BAA4B,SAAS,QAAQ;AAG9E,YAAM,mBAAmB,KAAK,0BAA0B,SAAS,QAAQ;AAElE,aAAA;AAAA,QACL,SAAS;AAAA,QACT,UAAU,KAAK;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MACpC;AAAA,aAEOA,QAAO;AACT,WAAA,QAAQ,MAAM,8CAA8CA,MAAK;AAC/D,aAAA;AAAA,QACL,SAAS;AAAA,QACT,UAAU,KAAK;AAAA,QACf,OAAOA,OAAM;AAAA,QACb,kBAAkB,KAAK,oCAAoC,QAAQ;AAAA,QACnE,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MACpC;AAAA,IAAA;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQF,MAAM,YAAY,aAAa;AACzB,QAAA;AACG,WAAA,QAAQ,KAAK,4CAA4C;AAAA,QAC5D,WAAW,YAAY;AAAA,QACvB,QAAQ,YAAY;AAAA,MAAA,CACrB;AAED,YAAM,SAAS,MAAM,KAAK,gBAAgB,WAAW;AAEhD,WAAA,QAAQ,YAAY,2DAA2D;AAE7E,aAAA;AAAA,aAEAA,QAAO;AACT,WAAA,QAAQ,MAAM,kDAAkDA,MAAK;AACpE,YAAAA;AAAA,IAAA;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASF,MAAM,8BAA8B,UAAU,aAAa;AACrD,QAAA;AACG,WAAA,QAAQ,KAAK,kDAAkD;AAAA,QAClE,WAAW,YAAY;AAAA,MAAA,CACxB;AAED,YAAM,UAAU;AAAA;AAAA,QAEd,qBAAqB,KAAK,2BAA2B,QAAQ;AAAA;AAAA,QAG7D,mBAAmB,KAAK,yBAAyB,QAAQ;AAAA;AAAA,QAGzD,cAAc,KAAK,oBAAoB,QAAQ;AAAA;AAAA,QAG/C,mBAAmB,KAAK,yBAAyB,QAAQ;AAAA;AAAA,QAGzD,oBAAoB,KAAK,0BAA0B,QAAQ;AAAA;AAAA,QAG3D,mBAAmB,KAAK,yBAAyB,QAAQ;AAAA;AAAA,QAGzD,gBAAgB,KAAK,sBAAsB,QAAQ;AAAA;AAAA,QAGnD,sBAAsB,KAAK,6BAA6B,QAAQ;AAAA;AAAA,QAGhE,iBAAiB,KAAK,iCAAiC,QAAQ;AAAA,MACjE;AAEK,WAAA,QAAQ,KAAK,8CAA8C;AAAA,QAC9D,UAAU,QAAQ,kBAAkB;AAAA,QACpC,iBAAiB,QAAQ,kBAAkB;AAAA,QAC3C,iBAAiB,QAAQ,eAAe;AAAA,MAAA,CACzC;AAEM,aAAA;AAAA,aAEAA,QAAO;AACT,WAAA,QAAQ,MAAM,qDAAqDA,MAAK;AACvE,YAAAA;AAAA,IAAA;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAMF,2BAA2B,UAAU;AAC7B,UAAA,EAAE,eAAe,IAAI,eAAe,GAAG,gBAAgB,MAAM;AAE5D,WAAA;AAAA,MACL,iBAAiB,KAAK,MAAO,eAAe,gBAAiB,GAAG;AAAA,MAChE,mBAAmB,KAAK,wBAAwB,YAAY;AAAA,MAC5D,wBAAwB,KAAK,6BAA6B,YAAY;AAAA,MACtE,qBAAqB,KAAK,0BAA0B,YAAY;AAAA,MAChE,uBAAuB,KAAK,8BAA8B,YAAY;AAAA,IACxE;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,yBAAyB,UAAU;AACjC,UAAM,EAAE,eAAe,CAAA,GAAI,aAAa,OAAW,IAAA;AAE7C,UAAA,mBAAmB,KAAK,0BAA0B,YAAY;AAC9D,UAAA,gBAAgB,KAAK,oBAAoB,YAAY;AACrD,UAAA,mBAAmB,KAAK,yBAAyB,YAAY;AAE5D,WAAA;AAAA,MACL,OAAO,KAAK,sBAAsB,kBAAkB,aAAa;AAAA,MACjE,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,MACV,aAAa,KAAK,0BAA0B,YAAY;AAAA,MACxD,eAAe,KAAK,8BAA8B,YAAY;AAAA,IAChE;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,oBAAoB,UAAU;AAC5B,UAAM,EAAE,eAAe,CAAC,EAAA,IAAM;AAG9B,UAAM,kBAAkB,aAAa;AAAA,MAAO,CAC1C,MAAA,EAAE,kBAAkB,EAAE,kBAAkB;AAAA,IAC1C;AAEA,UAAM,gBAAgB,gBAAgB;AAAA,MAAO,OAC3C,EAAE,gBAAgB,EAAE,eAAe,QAAQ,EAAE;AAAA,IAC/C;AAEO,WAAA;AAAA,MACL,mBAAmB,KAAK,2BAA2B,eAAe;AAAA,MAClE,iBAAiB,cAAc;AAAA,MAC/B,UAAU,gBAAgB,SAAS,IACjC,cAAc,SAAS,gBAAgB,SAAS;AAAA,MAClD,qBAAqB,KAAK,6BAA6B,eAAe;AAAA,MACtE,cAAc,KAAK,0BAA0B,eAAe,eAAe;AAAA,IAC7E;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,yBAAyB,UAAU;AACjC,UAAM,EAAE,eAAe,CAAC,EAAA,IAAM;AAEvB,WAAA;AAAA,MACL,wBAAwB,KAAK,6BAA6B,YAAY;AAAA,MACtE,gBAAgB,KAAK,kCAAkC,YAAY;AAAA,MACnE,aAAa,KAAK,+BAA+B,YAAY;AAAA,MAC7D,WAAW,KAAK,6BAA6B,YAAY;AAAA,MACzD,YAAY,KAAK,8BAA8B,YAAY;AAAA,MAC3D,uBAAuB,KAAK,4BAA4B,YAAY;AAAA,IACtE;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,0BAA0B,UAAU;AAClC,UAAM,EAAE,eAAe,CAAC,EAAA,IAAM;AAEvB,WAAA;AAAA,MACL,iBAAiB,KAAK,gCAAgC,YAAY;AAAA,MAClE,qBAAqB,KAAK,0BAA0B,YAAY;AAAA,MAChE,kBAAkB,KAAK,0BAA0B,YAAY;AAAA,MAC7D,YAAY,KAAK,yBAAyB,YAAY;AAAA,MACtD,aAAa,KAAK,0BAA0B,YAAY;AAAA,IAC1D;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,yBAAyB,UAAU;AAC3B,UAAA,EAAE,eAAe,IAAI,eAAe,GAAG,gBAAgB,MAAM;AAE7D,UAAA,kBAAkB,KAAK,+BAA+B,YAAY;AAClE,UAAA,gBAAgB,KAAK,uBAAuB,YAAY;AAEvD,WAAA;AAAA,MACL,iBAAiB,KAAK,MAAO,eAAe,gBAAiB,GAAG;AAAA,MAChE;AAAA,MACA,eAAe,cAAc;AAAA,MAC7B,YAAY,cAAc;AAAA,MAC1B,kBAAkB,KAAK,uBAAuB,YAAY;AAAA,IAC5D;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,sBAAsB,UAAU;AAC9B,UAAM,EAAE,eAAe,CAAC,EAAA,IAAM;AAExB,UAAA,gBAAgB,aACnB,IAAI,CAAK,MAAA,EAAE,YAAY,EACvB,OAAO,CAAA,MAAK,KAAK,IAAI,CAAC;AAErB,QAAA,cAAc,WAAW,GAAG;AAC9B,aAAO,EAAE,UAAU,qBAAqB,SAAS,EAAE;AAAA,IAAA;AAG/C,UAAA,UAAU,cAAc,OAAO,CAAC,KAAK,SAAS,MAAM,MAAM,CAAC,IAAI,cAAc;AAC7E,UAAA,kBAAkB,KAAK,uBAAuB,YAAY;AAEzD,WAAA;AAAA,MACL,SAAS,KAAK,MAAM,OAAO;AAAA,MAC3B,UAAU,KAAK,0BAA0B,OAAO;AAAA,MAChD;AAAA,MACA,sBAAsB,KAAK,uCAAuC,YAAY;AAAA,MAC9E,YAAY,KAAK,oBAAoB,aAAa;AAAA,IACpD;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,6BAA6B,UAAU;AACrC,UAAM,EAAE,eAAe,CAAA,GAAI,WAAW,EAAM,IAAA;AAErC,WAAA;AAAA,MACL,iBAAiB,KAAK,sBAAsB,cAAc,QAAQ;AAAA,MAClE,oBAAoB,KAAK,yBAAyB,YAAY;AAAA,MAC9D,sBAAsB,KAAK,6BAA6B,YAAY;AAAA,MACpE,gBAAgB,KAAK,uBAAuB,YAAY;AAAA,MACxD,kBAAkB,KAAK,yBAAyB,YAAY;AAAA,IAC9D;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,iCAAiC,UAAU;AACzC,UAAM,kBAAkB,CAAC;AACzB,UAAM,EAAE,WAAW,GAAG,eAAe,CAAA,EAAO,IAAA;AAG5C,QAAI,WAAW,IAAI;AACjB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,WAAW;AAAA,MAAA,CACZ;AAAA,IAAA;AAIG,UAAA,WAAW,KAAK,6BAA6B,YAAY;AAC/D,QAAI,WAAW,KAAM;AACnB,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,WAAW;AAAA,MAAA,CACZ;AAAA,IAAA;AAIG,UAAA,gBAAgB,KAAK,8BAA8B,YAAY;AACjE,QAAA,cAAc,iBAAiB,GAAG;AACpC,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,WAAW;AAAA,MAAA,CACZ;AAAA,IAAA;AAGI,WAAA;AAAA,EAAA;AAAA;AAAA,EAKT,wBAAwB,cAAc;AACpC,UAAM,mBAAmB,aAAa,OAAO,CAAK,MAAA,EAAE,aAAa,aAAa;AAC9E,UAAM,UAAU,iBAAiB,OAAO,CAAA,MAAK,EAAE,OAAO;AAE/C,WAAA,iBAAiB,SAAS,IAC/B,KAAK,MAAO,QAAQ,SAAS,iBAAiB,SAAU,GAAG,IAAI;AAAA,EAAA;AAAA,EAGnE,6BAA6B,cAAc;AACzC,UAAM,sBAAsB,aAAa;AAAA,MAAO,CAC9C,MAAA,EAAE,aAAa,oBAAoB,EAAE;AAAA,IACvC;AACA,UAAM,UAAU,oBAAoB,OAAO,CAAA,MAAK,EAAE,OAAO;AAElD,WAAA,oBAAoB,SAAS,IAClC,KAAK,MAAO,QAAQ,SAAS,oBAAoB,SAAU,GAAG,IAAI;AAAA,EAAA;AAAA,EAGtE,0BAA0B,cAAc;AACtC,UAAM,kBAAkB,aAAa;AAAA,MAAO,CAC1C,MAAA,EAAE,aAAa,gBAAgB,EAAE;AAAA,IACnC;AACA,UAAM,UAAU,gBAAgB,OAAO,CAAA,MAAK,EAAE,OAAO;AAE9C,WAAA,gBAAgB,SAAS,IAC9B,KAAK,MAAO,QAAQ,SAAS,gBAAgB,SAAU,GAAG,IAAI;AAAA,EAAA;AAAA,EAGlE,8BAA8B,cAAc;AAC1C,UAAM,sBAAsB,aAAa,OAAO,OAAK,EAAE,WAAW,EAAE,YAAY;AAC5E,QAAA,oBAAoB,WAAW,EAAU,QAAA;AAEvC,UAAA,UAAU,oBAAoB,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,cAAc,CAAC,IAAI,oBAAoB;AAGtG,WAAO,KAAK,IAAI,GAAG,MAAO,OAAO;AAAA,EAAA;AAAA,EAGnC,0BAA0B,cAAc;AACtC,UAAM,gBAAgB,aAAa;AAAA,MAAO,CACxC,MAAA,EAAE,aAAa,cAAc,EAAE;AAAA,IACjC;AACA,UAAM,UAAU,cAAc,OAAO,CAAA,MAAK,EAAE,OAAO;AAE5C,WAAA,cAAc,SAAS,IAC5B,KAAK,MAAO,QAAQ,SAAS,cAAc,SAAU,GAAG,IAAI;AAAA,EAAA;AAAA,EAGhE,oBAAoB,cAAc;AAC1B,UAAA,aAAa,aAChB,IAAI,CAAK,MAAA,EAAE,cAAc,EACzB,OAAO,CAAA,MAAK,KAAK,IAAI,CAAC;AAErB,QAAA,WAAW,WAAW,EAAG,QAAO,EAAE,KAAK,GAAG,KAAK,EAAE;AAE9C,WAAA;AAAA,MACL,KAAK,KAAK,IAAI,GAAG,UAAU;AAAA,MAC3B,KAAK,KAAK,IAAI,GAAG,UAAU;AAAA,MAC3B,aAAa,KAAK,qBAAqB,YAAY;AAAA,IACrD;AAAA,EAAA;AAAA,EAGF,qBAAqB,cAAc;AAEjC,UAAM,gBAAgB,CAAC;AAEvB,iBAAa,QAAQ,CAAe,gBAAA;AAClC,YAAM,WAAW,YAAY;AAC7B,UAAI,UAAU;AACN,cAAA,QAAQ,KAAK,eAAe,QAAQ;AACtC,YAAA,CAAC,cAAc,KAAK,GAAG;AACzB,wBAAc,KAAK,IAAI,EAAE,SAAS,GAAG,OAAO,EAAE;AAAA,QAAA;AAEhD,sBAAc,KAAK,EAAE;AACrB,YAAI,YAAY,SAAS;AACvB,wBAAc,KAAK,EAAE;AAAA,QAAA;AAAA,MACvB;AAAA,IACF,CACD;AAEK,UAAA,oBAAoB,OAAO,QAAQ,aAAa,EACnD,OAAO,CAAC,CAAC,GAAG,IAAI,MAAM,KAAK,QAAQ,KAAM,KAAK,UAAU,KAAK,QAAS,GAAG,EACzE,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,KAAK;AAErB,WAAA;AAAA,EAAA;AAAA,EAGT,eAAe,UAAU;AACnB,QAAA,YAAY,EAAU,QAAA;AACtB,QAAA,YAAY,GAAW,QAAA;AACvB,QAAA,YAAY,GAAW,QAAA;AACpB,WAAA;AAAA,EAAA;AAAA,EAGT,yBAAyB,cAAc;AAE/B,UAAA,kBAAkB,KAAK,6BAA6B,YAAY;AACtE,UAAM,gBAAgB,aAAa;AAAA,MAAO,CACxC,MAAA,EAAE,gBAAgB,EAAE,eAAe;AAAA,IAAA,EACnC;AAEE,QAAA,gBAAgB,aAAa,SAAS,KAAK;AACtC,aAAA;AAAA,IAAA,WACE,kBAAkB,KAAM;AAC1B,aAAA;AAAA,IAAA,OACF;AACE,aAAA;AAAA,IAAA;AAAA,EACT;AAAA,EAGF,sBAAsB,UAAU,OAAO;AACjC,QAAA,WAAW,GAAW,QAAA;AAC1B,QAAI,WAAW,MAAM,MAAM,OAAO,EAAU,QAAA;AAC5C,QAAI,WAAW,MAAM,MAAM,OAAO,GAAW,QAAA;AACtC,WAAA;AAAA,EAAA;AAAA,EAGT,0BAA0B,cAAc;AACtC,UAAM,aAAa,KAAK,yBAAyB,cAAc,CAAC;AAC1D,UAAA,WAAW,KAAK,kBAAkB,UAAU;AAElD,WAAO,WAAW,MAAM,SAAS,WAAW,MAAM,aAAa;AAAA,EAAA;AAAA,EAGjE,yBAAyB,cAAc,YAAY;AACjD,UAAM,aAAa,CAAC;AAEpB,aAAS,IAAI,GAAG,KAAK,aAAa,SAAS,YAAY,KAAK;AAC1D,YAAM,SAAS,aAAa,MAAM,GAAG,IAAI,UAAU;AACnD,YAAM,UAAU,OAAO,OAAO,CAAQ,SAAA,KAAK,OAAO,EAAE;AACzC,iBAAA,KAAK,UAAU,UAAU;AAAA,IAAA;AAG/B,WAAA;AAAA,EAAA;AAAA,EAGT,kBAAkB,QAAQ;AACpB,QAAA,OAAO,WAAW,EAAU,QAAA;AAE1B,UAAA,OAAO,OAAO,OAAO,CAAC,KAAK,QAAQ,MAAM,KAAK,CAAC,IAAI,OAAO;AAC1D,UAAA,eAAe,OAAO,IAAI,CAAA,QAAO,KAAK,IAAI,MAAM,MAAM,CAAC,CAAC;AAEvD,WAAA,aAAa,OAAO,CAAC,KAAK,SAAS,MAAM,MAAM,CAAC,IAAI,OAAO;AAAA,EAAA;AAAA,EAGpE,8BAA8B,cAAc;AAC1C,UAAM,SAAS,aAAa,OAAO,CAAK,MAAA,CAAC,EAAE,OAAO;AAE3C,WAAA;AAAA,MACL,gBAAgB,OAAO,OAAO,OAAK,EAAE,cAAc,UAAU,EAAE;AAAA,MAC/D,gBAAgB,OAAO,OAAO,OAAK,EAAE,cAAc,UAAU,EAAE;AAAA,MAC/D,iBAAiB,OAAO,OAAO,OAAK,EAAE,cAAc,iBAAiB,EAAE;AAAA,MACvE,kBAAkB,OAAO,OAAO,OAAK,EAAE,cAAc,WAAW,EAAE;AAAA,IACpE;AAAA,EAAA;AAAA;AAAA,EAKF,2BAA2B,cAAc;AACvC,UAAM,sBAAsB,KAAK;AAAA,MAC/B,GAAG,aACA,OAAO,CAAA,MAAK,EAAE,eAAe,QAAQ,EAAE,OAAO,EAC9C,IAAI,CAAK,MAAA,EAAE,kBAAkB,CAAC;AAAA,IACnC;AAEO,WAAA,KAAK,IAAI,qBAAqB,CAAC;AAAA,EAAA;AAAA,EAGxC,6BAA6B,cAAc;AACnC,UAAA,QAAQ,aACX,IAAI,CAAK,MAAA,EAAE,YAAY,EACvB,OAAO,CAAA,MAAK,KAAK,IAAI,CAAC;AAEzB,WAAO,MAAM,SAAS,IACpB,MAAM,OAAO,CAAC,KAAK,SAAS,MAAM,MAAM,CAAC,IAAI,MAAM,SAAS;AAAA,EAAA;AAAA,EAGhE,0BAA0B,eAAe,iBAAiB;AACpD,QAAA,gBAAgB,WAAW,EAAU,QAAA;AAEnC,UAAA,QAAQ,cAAc,SAAS,gBAAgB;AAEjD,QAAA,QAAQ,IAAY,QAAA;AACpB,QAAA,QAAQ,IAAY,QAAA;AACpB,QAAA,QAAQ,IAAY,QAAA;AACjB,WAAA;AAAA,EAAA;AAAA;AAAA,EAKT,6BAA6B,cAAc;AACzC,UAAM,sBAAsB,aAAa;AAAA,MAAO,CAC9C,MAAA,EAAE,aAAa,oBAAoB,EAAE;AAAA,IACvC;AACA,UAAM,UAAU,oBAAoB,OAAO,CAAA,MAAK,EAAE,OAAO;AAElD,WAAA,oBAAoB,SAAS,IAClC,KAAK,MAAO,QAAQ,SAAS,oBAAoB,SAAU,GAAG,IAAI;AAAA,EAAA;AAAA,EAGtE,kCAAkC,cAAc;AAC9C,UAAM,gBAAgB,aAAa;AAAA,MAAO,CACxC,MAAA,EAAE,aAAa,cAAc,EAAE;AAAA,IACjC;AACA,UAAM,UAAU,cAAc,OAAO,CAAA,MAAK,EAAE,OAAO;AAE5C,WAAA,cAAc,SAAS,IAC5B,KAAK,MAAO,QAAQ,SAAS,cAAc,SAAU,GAAG,IAAI;AAAA,EAAA;AAAA,EAGhE,+BAA+B,cAAc;AAE3C,UAAM,mBAAmB,aAAa;AAAA,MAAO,CAC3C,MAAA,EAAE,aAAa,iBAAiB,EAAE;AAAA,IACpC;AACA,UAAM,UAAU,iBAAiB,OAAO,CAAA,MAAK,EAAE,OAAO;AAE/C,WAAA,iBAAiB,SAAS,IAC/B,KAAK,MAAO,QAAQ,SAAS,iBAAiB,SAAU,GAAG,IAAI;AAAA,EAAA;AAAA,EAGnE,6BAA6B,cAAc;AACzC,UAAM,iBAAiB,aAAa;AAAA,MAAO,CACzC,MAAA,EAAE,aAAa,eAAe,EAAE;AAAA,IAClC;AACA,UAAM,UAAU,eAAe,OAAO,CAAA,MAAK,EAAE,OAAO;AAE7C,WAAA,eAAe,SAAS,IAC7B,KAAK,MAAO,QAAQ,SAAS,eAAe,SAAU,GAAG,IAAI;AAAA,EAAA;AAAA,EAGjE,8BAA8B,cAAc;AAC1C,UAAM,kBAAkB,aAAa;AAAA,MAAO,CAC1C,MAAA,EAAE,aAAa,gBAAgB,EAAE;AAAA,IACnC;AACA,UAAM,UAAU,gBAAgB,OAAO,CAAA,MAAK,EAAE,OAAO;AAE9C,WAAA,gBAAgB,SAAS,IAC9B,KAAK,MAAO,QAAQ,SAAS,gBAAgB,SAAU,GAAG,IAAI;AAAA,EAAA;AAAA,EAGlE,4BAA4B,cAAc;AACxC,UAAM,SAAS;AAAA,MACb,gBAAgB,KAAK,6BAA6B,YAAY;AAAA,MAC9D,UAAU,KAAK,kCAAkC,YAAY;AAAA,MAC7D,aAAa,KAAK,+BAA+B,YAAY;AAAA,MAC7D,WAAW,KAAK,6BAA6B,YAAY;AAAA,IAC3D;AAEA,UAAM,eAAe,OAAO,OAAO,MAAM,EAAE,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC,IAAI;AAEhF,QAAA,eAAe,GAAW,QAAA;AAC1B,QAAA,eAAe,GAAW,QAAA;AAC1B,QAAA,eAAe,GAAW,QAAA;AACvB,WAAA;AAAA,EAAA;AAAA;AAAA,EAKT,gCAAgC,cAAc;AAEtC,UAAA,UAAU,KAAK,6BAA6B,YAAY;AAE1D,QAAA,UAAU,IAAa,QAAA;AACvB,QAAA,UAAU,IAAa,QAAA;AACpB,WAAA;AAAA,EAAA;AAAA,EAGT,0BAA0B,cAAc;AAEtC,UAAM,aAAa,aAAa,IAAI,OAAK,KAAK,4BAA4B,CAAC,CAAC;AAC5E,UAAM,mBAAmB,IAAI,IAAI,UAAU,EAAE;AAEtC,WAAA,mBAAmB,IAAI,aAAa;AAAA,EAAA;AAAA,EAG7C,4BAA4B,aAAa;AACnC,QAAA,CAAC,YAAY,aAAqB,QAAA;AAElC,QAAA,YAAY,eAAe,KAAa,QAAA;AACxC,QAAA,YAAY,eAAe,IAAa,QAAA;AACrC,WAAA;AAAA,EAAA;AAAA,EAGT,0BAA0B,cAAc;AAEtC,UAAM,eAAe,aAAa,OAAO,CAAK,MAAA,EAAE,kBAAkB,CAAC;AACnE,UAAM,eAAe,aAAa,OAAO,CAAK,MAAA,EAAE,iBAAiB,CAAC;AAE5D,UAAA,eAAe,KAAK,6BAA6B,YAAY;AAC7D,UAAA,eAAe,KAAK,6BAA6B,YAAY;AAE5D,WAAA,eAAe,eAAe,aAAa;AAAA,EAAA;AAAA,EAGpD,yBAAyB,cAAc;AACrC,UAAM,sBAAsB,aAAa,OAAO,CAAA,MAAK,EAAE,OAAO;AACxD,UAAA,UAAU,KAAK,6BAA6B,mBAAmB;AAEjE,QAAA,UAAU,IAAa,QAAA;AACvB,QAAA,UAAU,IAAa,QAAA;AACpB,WAAA;AAAA,EAAA;AAAA,EAGT,0BAA0B,cAAc;AAEhC,UAAA,YAAY,aAAa,MAAM,GAAG,KAAK,MAAM,aAAa,SAAS,CAAC,CAAC;AACrE,UAAA,aAAa,aAAa,MAAM,KAAK,MAAM,aAAa,SAAS,CAAC,CAAC;AAEnE,UAAA,gBAAgB,KAAK,6BAA6B,SAAS;AAC3D,UAAA,iBAAiB,KAAK,6BAA6B,UAAU;AAE/D,QAAA,iBAAiB,gBAAgB,IAAY,QAAA;AAC7C,QAAA,iBAAiB,gBAAgB,IAAY,QAAA;AAC1C,WAAA;AAAA,EAAA;AAAA;AAAA,EAKT,+BAA+B,cAAc;AAC3C,UAAM,SAAS;AAAA,MACb,OAAO,EAAE,SAAS,GAAG,OAAO,EAAE;AAAA,MAC9B,OAAO,EAAE,SAAS,GAAG,OAAO,EAAE;AAAA,MAC9B,QAAQ,EAAE,SAAS,GAAG,OAAO,EAAE;AAAA,MAC/B,OAAO,EAAE,SAAS,GAAG,OAAO,EAAE;AAAA,IAChC;AAEA,iBAAa,QAAQ,CAAe,gBAAA;AAClC,YAAM,WAAW,YAAY;AAC7B,UAAI,CAAC,SAAU;AAEX,UAAA;AACA,UAAA,YAAY,EAAW,SAAA;AAAA,eAClB,YAAY,EAAW,SAAA;AAAA,eACvB,YAAY,GAAY,SAAA;AAAA,UACpB,SAAA;AAEb,aAAO,KAAK,EAAE;AACd,UAAI,YAAY,SAAS;AACvB,eAAO,KAAK,EAAE;AAAA,MAAA;AAAA,IAChB,CACD;AAED,UAAM,SAAS,CAAC;AACT,WAAA,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,OAAO,IAAI,MAAM;AAChD,aAAO,KAAK,IAAI,KAAK,QAAQ,IAC3B,KAAK,MAAO,KAAK,UAAU,KAAK,QAAS,GAAG,IAAI;AAAA,IAAA,CACnD;AAEM,WAAA;AAAA,EAAA;AAAA,EAGT,uBAAuB,cAAc;AACnC,UAAM,SAAS,aAAa,OAAO,CAAK,MAAA,CAAC,EAAE,OAAO;AAElD,UAAM,WAAW;AAAA,MACf,YAAY,OAAO,OAAO,CAAA,MAAK,KAAK,KAAK,EAAE,YAAY,MAAM,EAAE,kBAAkB,EAAE,MAAM,CAAC,EAAE;AAAA,MAC5F,iBAAiB,OAAO,OAAO,OAAK,EAAE,cAAc,UAAU,EAAE;AAAA,MAChE,kBAAkB,OAAO,OAAO,OAAK,EAAE,cAAc,WAAW,EAAE;AAAA,MAClE,eAAe,OAAO,OAAO,CAAK,MAAA,CAAC,EAAE,aAAa,EAAE,cAAc,QAAQ,EAAE;AAAA,IAC9E;AAEA,UAAM,QAAQ;AAAA,MACZ,YAAY,SAAS,aAAa,SAAS;AAAA,MAC3C,YAAY,SAAS;AAAA,MACrB,UAAU,SAAS;AAAA,IACrB;AAEO,WAAA,EAAE,UAAU,MAAM;AAAA,EAAA;AAAA,EAG3B,uBAAuB,cAAc;AAC/B,QAAA,aAAa,SAAS,EAAU,QAAA;AAE9B,UAAA,aAAa,aAAa,MAAM,GAAG,KAAK,MAAM,aAAa,SAAS,CAAC,CAAC;AACtE,UAAA,YAAY,aAAa,MAAM,CAAC,KAAK,MAAM,aAAa,SAAS,CAAC,CAAC;AAEnE,UAAA,gBAAgB,WAAW,OAAO,CAAA,MAAK,EAAE,OAAO,EAAE,SAAS,WAAW;AACtE,UAAA,eAAe,UAAU,OAAO,CAAA,MAAK,EAAE,OAAO,EAAE,SAAS,UAAU;AAErE,QAAA,eAAe,gBAAgB,IAAY,QAAA;AAC3C,QAAA,eAAe,gBAAgB,IAAY,QAAA;AACxC,WAAA;AAAA,EAAA;AAAA;AAAA,EAKT,uBAAuB,cAAc;AACnC,UAAM,kBAAkB,CAAC;AAEzB,iBAAa,QAAQ,CAAe,gBAAA;AAClC,YAAM,WAAW,YAAY;AAC7B,YAAM,OAAO,YAAY;AAEzB,UAAI,YAAY,MAAM;AAChB,YAAA,CAAC,gBAAgB,QAAQ,GAAG;AACd,0BAAA,QAAQ,IAAI,CAAC;AAAA,QAAA;AAEf,wBAAA,QAAQ,EAAE,KAAK,IAAI;AAAA,MAAA;AAAA,IACrC,CACD;AAED,UAAM,SAAS,CAAC;AACT,WAAA,QAAQ,eAAe,EAAE,QAAQ,CAAC,CAAC,UAAU,KAAK,MAAM;AACtD,aAAA,QAAQ,IAAI,MAAM,OAAO,CAAC,KAAK,SAAS,MAAM,MAAM,CAAC,IAAI,MAAM;AAAA,IAAA,CACvE;AAEM,WAAA;AAAA,EAAA;AAAA,EAGT,0BAA0B,aAAa;AACjC,QAAA,cAAc,IAAa,QAAA;AAC3B,QAAA,cAAc,IAAa,QAAA;AACxB,WAAA;AAAA,EAAA;AAAA,EAGT,uCAAuC,cAAc;AACnD,UAAM,sBAAsB,aAAa,OAAO,OAAK,EAAE,WAAW,EAAE,YAAY;AAC5E,QAAA,oBAAoB,WAAW,EAAU,QAAA;AAGvC,UAAA,WAAW,oBAAoB,SAAS,aAAa;AACrD,UAAA,UAAU,KAAK,6BAA6B,mBAAmB;AAGrE,UAAM,iBAAiB,KAAK,IAAI,IAAI,MAAO,WAAW,GAAI;AAE1D,YAAQ,WAAW,kBAAkB;AAAA,EAAA;AAAA,EAGvC,oBAAoB,eAAe;AAC7B,QAAA,cAAc,SAAS,EAAU,QAAA;AAE/B,UAAA,YAAY,cAAc,MAAM,GAAG,KAAK,MAAM,cAAc,SAAS,CAAC,CAAC;AACvE,UAAA,aAAa,cAAc,MAAM,KAAK,MAAM,cAAc,SAAS,CAAC,CAAC;AAErE,UAAA,WAAW,UAAU,OAAO,CAAC,KAAK,SAAS,MAAM,MAAM,CAAC,IAAI,UAAU;AACtE,UAAA,YAAY,WAAW,OAAO,CAAC,KAAK,SAAS,MAAM,MAAM,CAAC,IAAI,WAAW;AAE3E,QAAA,YAAY,WAAW,IAAY,QAAA;AACnC,QAAA,YAAY,WAAW,IAAY,QAAA;AAChC,WAAA;AAAA,EAAA;AAAA;AAAA,EAKT,sBAAsB,cAAc,UAAU;AAC5C,UAAM,cAAc;AAAA,MAClB,aAAa,WAAW;AAAA,MACxB,gBAAgB,KAAK,8BAA8B,YAAY,EAAE,iBAAiB;AAAA,MAClF,gBAAgB,KAAK,6BAA6B,YAAY,IAAI;AAAA,MAClE,yBAAyB,KAAK,0BAA0B,YAAY,MAAM;AAAA,MAC1E,gBAAgB,KAAK,4BAA4B,YAAY,MAAM;AAAA,IACrE;AAEA,UAAM,YAAY,OAAO,OAAO,WAAW,EAAE,OAAO,OAAO,EAAE;AAEzD,QAAA,aAAa,EAAU,QAAA;AACvB,QAAA,aAAa,EAAU,QAAA;AACpB,WAAA;AAAA,EAAA;AAAA,EAGT,yBAAyB,cAAc;AACrC,UAAM,aAAa;AAAA,MACjB,kBAAkB,KAAK,0BAA0B,CAAA,GAAI,YAAY,MAAM;AAAA,MACvE,eAAe,KAAK,6BAA6B,YAAY,IAAI;AAAA,MACjE,cAAc,KAAK,oBAAoB,YAAY,EAAE,MAAM;AAAA,MAC3D,mBAAmB,KAAK,0BAA0B,YAAY,MAAM;AAAA,IACtE;AAEA,UAAM,iBAAiB,OAAO,OAAO,UAAU,EAAE,OAAO,OAAO,EAAE;AAE1D,WAAA,kBAAkB,IAAI,WAAW;AAAA,EAAA;AAAA,EAG1C,6BAA6B,cAAc;AACzC,UAAM,eAAe,CAAC;AAEhB,UAAA,gBAAgB,KAAK,8BAA8B,YAAY;AAEjE,QAAA,cAAc,iBAAiB,GAAG;AACpC,mBAAa,KAAK,uBAAuB;AAAA,IAAA;AAGvC,QAAA,cAAc,iBAAiB,GAAG;AACpC,mBAAa,KAAK,wBAAwB;AAAA,IAAA;AAGxC,QAAA,cAAc,kBAAkB,GAAG;AACrC,mBAAa,KAAK,6BAA6B;AAAA,IAAA;AAG1C,WAAA;AAAA,EAAA;AAAA,EAGT,uBAAuB,cAAc;AACnC,UAAM,OAAO,CAAC;AAEd,QAAI,KAAK,6BAA6B,YAAY,IAAI,IAAI;AACxD,WAAK,KAAK,2BAA2B;AAAA,IAAA;AAGvC,QAAI,KAAK,+BAA+B,YAAY,IAAI,IAAI;AAC1D,WAAK,KAAK,qBAAqB;AAAA,IAAA;AAGjC,QAAI,KAAK,kCAAkC,YAAY,IAAI,IAAI;AAC7D,WAAK,KAAK,iBAAiB;AAAA,IAAA;AAGtB,WAAA;AAAA,EAAA;AAAA,EAGT,yBAAyB,cAAc;AAC/B,UAAA,UAAU,KAAK,6BAA6B,YAAY;AAC9D,UAAM,SAAS,CAAC;AAEhB,QAAI,UAAU,KAAM;AAClB,aAAO,KAAK,0BAA0B;AAAA,IAAA;AAGlC,UAAA,kBAAkB,KAAK,uBAAuB,YAAY;AACzD,WAAA,QAAQ,eAAe,EAAE,QAAQ,CAAC,CAAC,UAAU,IAAI,MAAM;AAC5D,UAAI,SAAS,QAAQ,KAAK,KAAK,OAAO,KAAM;AAC1C,eAAO,KAAK,oBAAoB;AAAA,MAAA;AAAA,IAClC,CACD;AAEM,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAST,MAAM,oCAAoC,eAAe,UAAU;AACjE,QAAI,CAAC,iBAAiB,CAAC,cAAc,YAAY;AAC1C,WAAA,QAAQ,KAAK,qDAAqD;AACvE,aAAO,EAAE,YAAY,IAAI,SAAS,0BAA0B;AAAA,IAAA;AAG9D,UAAM,UAAU,CAAC;AACjB,UAAM,aAAa,cAAc;AAGjC,eAAW,CAAC,eAAe,SAAS,KAAK,OAAO,QAAQ,UAAU,GAAG;AAC/D,UAAA;AACF,YAAI,aAAa,OAAO,UAAU,YAAY,YAAY;AACnD,eAAA,QAAQ,MAAM,6BAA6B,aAAa;AACrD,kBAAA,aAAa,IAAI,MAAM,KAAK;AAAA,YAClC,MAAM,UAAU,QAAQ,QAAQ;AAAA,YAChC;AAAA;AAAA,YACA,gBAAgB;AAAA,UAClB;AAAA,QAAA,OACK;AACL,eAAK,QAAQ,KAAK,gBAAgB,gBAAgB,yBAAyB;AAC3E,kBAAQ,aAAa,IAAI,EAAE,OAAO,oBAAoB;AAAA,QAAA;AAAA,eAEjDA,QAAO;AACd,aAAK,QAAQ,MAAM,uBAAuB,gBAAgB,KAAKA,MAAK;AACpE,gBAAQ,aAAa,IAAI;AAAA,UACvB,OAAOA,OAAM;AAAA,UACb,UAAU,KAAK,wBAAwB,eAAe,QAAQ;AAAA,QAChE;AAAA,MAAA;AAAA,IACF;AAGK,WAAA;AAAA,MACL,YAAY;AAAA,MACZ,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA,MACpC,UAAU;AAAA,IACZ;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMF,MAAM,mBAAmB,IAAI,SAAS,UAAU;AAC9C,WAAO,QAAQ,KAAK;AAAA,MAClB,GAAG;AAAA,MACH,IAAI;AAAA,QAAQ,CAAC,GAAG,WACd,WAAW,MAAM,OAAO,IAAI,MAAM,QAAQ,CAAC,GAAG,OAAO;AAAA,MAAA;AAAA,IACvD,CACD;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMH,wBAAwB,eAAe,UAAU;AACxC,WAAA;AAAA,MACL,UAAU;AAAA,MACV,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,MAAM;AAAA,IACR;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASF,2BAA2B,kBAAkB,oBAAoB,IAAI;AAC/D,QAAA;AACF,YAAM,qBAAqB;AAAA,QACzB,UAAU;AAAA,QACV,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAClC,gBAAgB;AAAA,QAChB,mBAAmB,kBAAkB,cAAc,CAAC;AAAA;AAAA,QAGpD,iBAAiB;AAAA,UACf,UAAU,iBAAiB,YAAY;AAAA,UACvC,cAAc,iBAAiB,uBAAuB;AAAA,UACtD,YAAY,KAAK,oBAAoB,gBAAgB;AAAA,UACrD,eAAe,KAAK,uBAAuB,gBAAgB;AAAA,QAC7D;AAAA;AAAA,QAGA,qBAAqB,KAAK,4BAA4B,kBAAkB,iBAAiB;AAAA;AAAA,QAGzF,iBAAiB,KAAK,wBAAwB,kBAAkB,iBAAiB;AAAA;AAAA,QAGjF,iBAAiB;AAAA,UACf,kBAAkB,KAAK,uBAAuB,kBAAkB,iBAAiB;AAAA,UACjF,oBAAoB,OAAO,KAAK,kBAAkB,cAAc,CAAE,CAAA,EAAE;AAAA,UACpE,YAAY,KAAK,yBAAyB,kBAAkB,iBAAiB;AAAA,QAAA;AAAA,MAEjF;AAEK,WAAA,QAAQ,KAAK,+CAA+C;AAAA,QAC/D,UAAU,mBAAmB,gBAAgB;AAAA,QAC7C,gBAAgB,OAAO,KAAK,kBAAkB,cAAc,CAAE,CAAA,EAAE;AAAA,QAChE,YAAY,mBAAmB,gBAAgB;AAAA,MAAA,CAChD;AAEM,aAAA;AAAA,aAEAA,QAAO;AACT,WAAA,QAAQ,MAAM,sDAAsDA,MAAK;AACvE,aAAA;AAAA,QACL,OAAOA,OAAM;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MACpC;AAAA,IAAA;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAMF,oBAAoB,SAAS;AAC3B,WAAO,KAAK,IAAI,MAAM,QAAQ,YAAY,KAAK,EAAE;AAAA,EAAA;AAAA,EAGnD,uBAAuB,SAAS;AACxB,UAAA,OAAO,QAAQ,uBAAuB;AACrC,WAAA,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,OAAO,EAAE,CAAC;AAAA,EAAA;AAAA,EAG7C,4BAA4B,kBAAkB,mBAAmB;AAC/D,UAAM,WAAW,CAAC;AAEd,QAAA,iBAAiB,WAAW,IAAI;AAClC,eAAS,KAAK,yCAAyC;AAAA,IAAA;AAGrD,QAAA,iBAAiB,sBAAsB,KAAM;AAC/C,eAAS,KAAK,kCAAkC;AAAA,IAAA;AAG3C,WAAA;AAAA,EAAA;AAAA,EAGT,wBAAwB,kBAAkB,mBAAmB;AAC3D,UAAM,kBAAkB,CAAC;AAErB,QAAA,iBAAiB,WAAW,IAAI;AAClC,sBAAgB,KAAK,oCAAoC;AAAA,IAAA;AAGpD,WAAA;AAAA,EAAA;AAAA,EAGT,uBAAuB,kBAAkB,mBAAmB;AAC1D,QAAI,QAAQ;AACR,QAAA,iBAAiB,aAAa,OAAoB,UAAA;AAClD,QAAA,iBAAiB,wBAAwB,OAAoB,UAAA;AAC7D,QAAA,OAAO,KAAK,kBAAkB,cAAc,CAAE,CAAA,EAAE,SAAS,EAAY,UAAA;AAClE,WAAA;AAAA,EAAA;AAAA,EAGT,yBAAyB,kBAAkB,mBAAmB;AAC5D,UAAM,cAAc,KAAK,uBAAuB,kBAAkB,iBAAiB;AACnF,UAAM,iBAAiB,OAAO,KAAK,kBAAkB,cAAc,CAAE,CAAA,EAAE;AACvE,WAAO,KAAK,IAAI,KAAK,cAAe,iBAAiB,CAAE;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQzD,4BAA4B,SAAS,UAAU;AACzC,QAAA;AACF,YAAM,WAAW;AAAA;AAAA,QAEf,YAAY;AAAA,UACV,YAAY,KAAK,yBAAyB,QAAQ;AAAA,UAClD,aAAa,KAAK,0BAA0B,QAAQ;AAAA,UACpD,cAAc,KAAK,2BAA2B,QAAQ;AAAA,UACtD,sBAAsB,KAAK,8BAA8B,QAAQ;AAAA,UACjE,mBAAmB,KAAK,gCAAgC,QAAQ;AAAA,QAClE;AAAA;AAAA,QAGA,WAAW;AAAA,UACT,WAAW,KAAK,wBAAwB,QAAQ;AAAA,UAChD,QAAQ,KAAK,qBAAqB,QAAQ;AAAA,UAC1C,iBAAiB,KAAK,8BAA8B,QAAQ;AAAA,UAC5D,mBAAmB,KAAK,gCAAgC,QAAQ;AAAA,UAChE,kBAAkB,KAAK,+BAA+B,QAAQ;AAAA,QAChE;AAAA;AAAA,QAGA,SAAS;AAAA,UACP,kBAAkB,KAAK,+BAA+B,QAAQ;AAAA,UAC9D,oBAAoB,KAAK,iCAAiC,QAAQ;AAAA,UAClE,mBAAmB,KAAK,gCAAgC,QAAQ;AAAA,UAChE,oBAAoB,KAAK,iCAAiC,QAAQ;AAAA,QACpE;AAAA;AAAA,QAGA,OAAO;AAAA,UACL,iBAAiB,KAAK,8BAA8B,QAAQ;AAAA,UAC5D,kBAAkB,KAAK,+BAA+B,QAAQ;AAAA,UAC9D,cAAc,KAAK,2BAA2B,QAAQ;AAAA,UACtD,eAAe,KAAK,4BAA4B,QAAQ;AAAA,QAC1D;AAAA;AAAA,QAGA,iBAAiB,KAAK,mCAAmC,SAAS,QAAQ;AAAA;AAAA,QAG1E,oBAAoB,KAAK,2BAA2B,SAAS,QAAQ;AAAA;AAAA,QAGrE,sBAAsB,KAAK,6BAA6B,SAAS,QAAQ;AAAA;AAAA,QAGzE,UAAU;AAAA,UACR,oBAAmB,oBAAI,KAAK,GAAE,YAAY;AAAA,UAC1C,UAAU,KAAK;AAAA,UACf,iBAAiB;AAAA,UACjB,iBAAiB,KAAK,iCAAiC,SAAS,QAAQ;AAAA,QAAA;AAAA,MAE5E;AAEO,aAAA;AAAA,aACAA,QAAO;AACT,WAAA,QAAQ,MAAM,wCAAwCA,MAAK;AACzD,aAAA,KAAK,oCAAoC,QAAQ;AAAA,IAAA;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA,EAKF,yBAAyB,UAAU;AAC3B,UAAA,eAAe,SAAS,gBAAgB,CAAC;AACzC,UAAA,YAAY,SAAS,aAAa;AAClC,UAAA,iBAAiB,SAAS,kBAAkB;AAElD,QAAI,QAAQ;AAGR,QAAA,aAAa,SAAS,GAAG;AAC3B,eAAS,KAAK,IAAI,IAAI,aAAa,SAAS,CAAC;AAAA,IAAA;AAI/C,QAAI,YAAY,KAAO;AACZ,eAAA;AAAA,IAAA;AAIX,aAAS,iBAAiB;AAE1B,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,0BAA0B,UAAU;AAC5B,UAAA,WAAW,SAAS,YAAY;AAChC,UAAA,SAAS,SAAS,UAAU;AAC5B,UAAA,aAAa,SAAS,cAAc;AAE1C,QAAI,QAAQ;AAER,QAAA,WAAW,KAAK,aAAa,KAAK;AAC3B,eAAA;AAAA,IAAA;AAGP,QAAA,SAAS,KAAK,aAAa,KAAK;AACzB,eAAA;AAAA,IAAA;AAGX,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,2BAA2B,UAAU;AAC7B,UAAA,oBAAoB,SAAS,qBAAqB;AAClD,UAAA,oBAAoB,SAAS,qBAAqB;AAExD,QAAI,QAAQ;AAEZ,QAAI,oBAAoB,GAAG;AACzB,eAAS,oBAAoB;AAAA,IAAA;AAG/B,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,8BAA8B,UAAU;AAChC,UAAA,SAAS,SAAS,UAAU;AAC5B,UAAA,YAAY,SAAS,aAAa;AAClC,UAAA,aAAa,SAAS,cAAc;AAE1C,QAAI,QAAQ;AAER,QAAA,SAAS,KAAK,CAAC,WAAW;AACnB,eAAA;AAAA,IAAA;AAGX,QAAI,aAAa,KAAK;AACX,eAAA;AAAA,IAAA;AAGX,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,gCAAgC,UAAU;AAElC,UAAA,aAAa,KAAK,yBAAyB,QAAQ;AAClD,WAAA,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,aAAa,GAAG,CAAC;AAAA,EAAA;AAAA,EAGpD,wBAAwB,UAAU;AAC1B,UAAA,YAAY,SAAS,aAAa;AAClC,UAAA,eAAe,SAAS,gBAAgB;AACxC,UAAA,eAAe,SAAS,uBAAuB;AAErD,QAAI,QAAQ;AAEZ,QAAI,YAAY,KAAO;AACZ,eAAA;AAAA,IAAA;AAGX,QAAI,eAAe,GAAG;AACX,eAAA;AAAA,IAAA;AAGX,QAAI,eAAe,KAAM;AACd,eAAA;AAAA,IAAA;AAGX,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,qBAAqB,UAAU;AACvB,UAAA,WAAW,SAAS,YAAY;AAChC,UAAA,WAAW,SAAS,YAAY,CAAC;AAEvC,QAAI,QAAQ;AAEZ,QAAI,WAAW,IAAI;AACR,eAAA;AAAA,IAAA;AAGP,QAAA,SAAS,SAAS,GAAG;AACd,eAAA;AAAA,IAAA;AAGX,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,8BAA8B,UAAU;AAChC,UAAA,eAAe,SAAS,uBAAuB;AAC/C,UAAA,WAAW,SAAS,YAAY;AAEtC,QAAI,QAAQ;AAER,QAAA,eAAe,QAAQ,WAAW,IAAI;AAC/B,eAAA;AAAA,IAAA,WACA,eAAe,MAAM;AACrB,eAAA;AAAA,IAAA;AAGX,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,gCAAgC,UAAU;AAClC,UAAA,mBAAmB,SAAS,oBAAoB;AAChD,UAAA,oBAAoB,SAAS,qBAAqB;AAClD,UAAA,gBAAgB,SAAS,iBAAiB;AAE1C,UAAA,SAAS,mBAAmB,oBAAoB,iBAAiB;AACvE,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,+BAA+B,UAAU;AACjC,UAAA,cAAc,SAAS,eAAe;AACtC,UAAA,iBAAiB,SAAS,kBAAkB;AAElD,QAAI,QAAQ;AAER,QAAA,cAAc,KAAK,iBAAiB,IAAI;AACjC,eAAA;AAAA,IAAA;AAGX,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,+BAA+B,UAAU;AAChC,WAAA,KAAK,+BAA+B,QAAQ;AAAA,EAAA;AAAA,EAGrD,iCAAiC,UAAU;AACnC,UAAA,gBAAgB,SAAS,iBAAiB;AAC1C,UAAA,mBAAmB,SAAS,oBAAoB;AAEtD,QAAI,QAAQ;AAEZ,QAAI,gBAAgB,GAAG;AACb,cAAA;AAAA,IAAA;AAGV,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,gCAAgC,UAAU;AAClC,UAAA,oBAAoB,SAAS,qBAAqB;AAClD,UAAA,gBAAgB,SAAS,iBAAiB;AAEhD,QAAI,QAAQ;AAEZ,QAAI,oBAAoB,GAAG;AACjB,cAAA;AAAA,IAAA;AAGV,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,iCAAiC,UAAU;AACnC,UAAA,SAAS,KAAK,+BAA+B,QAAQ;AACrD,UAAA,WAAW,KAAK,iCAAiC,QAAQ;AACzD,UAAA,UAAU,KAAK,gCAAgC,QAAQ;AAErD,YAAA,SAAS,WAAW,WAAW;AAAA,EAAA;AAAA,EAGzC,8BAA8B,UAAU;AAChC,UAAA,YAAY,SAAS,aAAa;AAClC,UAAA,eAAe,SAAS,gBAAgB;AAE9C,YAAQ,YAAY,gBAAgB;AAAA,EAAA;AAAA,EAGtC,+BAA+B,UAAU;AACjC,UAAA,YAAY,SAAS,aAAa;AAClC,UAAA,eAAe,SAAS,gBAAgB;AAE9C,QAAI,QAAQ;AAEZ,QAAI,YAAY,IAAI;AACV,cAAA;AAAA,IAAA;AAGV,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,2BAA2B,UAAU;AAC7B,UAAA,sBAAsB,SAAS,uBAAuB;AACtD,UAAA,wBAAwB,SAAS,yBAAyB;AAEhE,YAAQ,sBAAsB,yBAAyB;AAAA,EAAA;AAAA,EAGzD,4BAA4B,UAAU;AAC9B,UAAA,gBAAgB,SAAS,iBAAiB;AAC1C,UAAA,mBAAmB,SAAS,oBAAoB;AAEtD,QAAI,QAAQ;AAEZ,QAAI,gBAAgB,GAAG;AACb,cAAA;AAAA,IAAA;AAGV,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAAA;AAAA,EAGzC,mCAAmC,SAAS,UAAU;AACpD,UAAM,kBAAkB,CAAC;AAGnB,UAAA,aAAa,KAAK,yBAAyB,QAAQ;AACzD,QAAI,aAAa,IAAI;AACnB,sBAAgB,KAAK;AAAA,QACnB,UAAU;AAAA,QACV,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,WAAW;AAAA,MAAA,CACZ;AAAA,IAAA;AAIG,UAAA,YAAY,KAAK,wBAAwB,QAAQ;AACvD,QAAI,YAAY,IAAI;AAClB,sBAAgB,KAAK;AAAA,QACnB,UAAU;AAAA,QACV,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,WAAW;AAAA,MAAA,CACZ;AAAA,IAAA;AAIG,UAAA,aAAa,KAAK,8BAA8B,QAAQ;AAC9D,QAAI,aAAa,IAAI;AACnB,sBAAgB,KAAK;AAAA,QACnB,UAAU;AAAA,QACV,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,WAAW;AAAA,MAAA,CACZ;AAAA,IAAA;AAGI,WAAA;AAAA,EAAA;AAAA,EAGT,2BAA2B,SAAS,UAAU;AACrC,WAAA;AAAA,MACL,iBAAiB,KAAK,yBAAyB,QAAQ;AAAA,MACvD,eAAe,KAAK,sBAAsB,QAAQ;AAAA,MAClD,gBAAgB,KAAK,uBAAuB,QAAQ;AAAA,MACpD,kBAAkB,KAAK,yBAAyB,QAAQ;AAAA,MACxD,YAAY,KAAK,mBAAmB,QAAQ;AAAA,IAC9C;AAAA,EAAA;AAAA,EAGF,6BAA6B,SAAS,UAAU;AAEvC,WAAA;AAAA,MACL,UAAU,KAAK;AAAA,MACf,iBAAiB;AAAA,MACjB,iBAAiB,KAAK,yBAAyB,QAAQ;AAAA,MACvD,iBAAiB,KAAK,wBAAwB,QAAQ;AAAA,IACxD;AAAA,EAAA;AAAA,EAGF,iCAAiC,SAAS,UAAU;AAClD,QAAI,aAAa;AAGjB,UAAM,aAAa,OAAO,KAAK,QAAQ,EAAE;AACrC,QAAA,aAAa,GAAkB,eAAA;AAAA,aAC1B,aAAa,EAAiB,eAAA;AAGvC,UAAM,eAAe,OAAO,KAAK,OAAO,EAAE;AACtC,QAAA,eAAe,EAAiB,eAAA;AAAA,aAC3B,eAAe,EAAiB,eAAA;AAGnC,UAAA,cAAc,SAAS,aAAa;AACtC,QAAA,cAAc,IAAqB,eAAA;AAEvC,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,UAAU,CAAC;AAAA,EAAA;AAAA,EAG9C,oCAAoC,UAAU;AACrC,WAAA;AAAA,MACL,YAAY,EAAE,YAAY,IAAI,aAAa,IAAI,cAAc,IAAI,sBAAsB,IAAI,mBAAmB,GAAG;AAAA,MACjH,WAAW,EAAE,WAAW,IAAI,QAAQ,IAAI,iBAAiB,IAAI,mBAAmB,IAAI,kBAAkB,GAAG;AAAA,MACzG,SAAS,EAAE,kBAAkB,IAAI,oBAAoB,IAAI,mBAAmB,IAAI,oBAAoB,GAAG;AAAA,MACvG,OAAO,EAAE,iBAAiB,IAAI,kBAAkB,IAAI,cAAc,IAAI,eAAe,GAAG;AAAA,MACxF,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,EAAE,iBAAiB,IAAI,eAAe,CAAC,GAAG,gBAAgB,CAAA,GAAI,kBAAkB,IAAI,YAAY,CAAA,EAAG;AAAA,MACvH,sBAAsB,EAAE,UAAU,KAAK,UAAU,iBAAiB,CAAC,GAAG,iBAAiB,IAAI,iBAAiB,GAAG;AAAA,MAC/G,UAAU,EAAE,oBAAmB,oBAAI,KAAO,GAAA,YAAA,GAAe,UAAU,KAAK,UAAU,iBAAiB,SAAS,iBAAiB,GAAG;AAAA,IAClI;AAAA,EAAA;AAAA,EAGF,yBAAyB,UAAU;AAC3B,UAAA,WAAW,SAAS,YAAY;AAChC,UAAA,aAAa,SAAS,cAAc;AACpC,UAAA,aAAa,KAAK,yBAAyB,QAAQ;AAEjD,YAAA,WAAW,aAAa,cAAc;AAAA,EAAA;AAAA,EAGhD,sBAAsB,UAAU;AAC9B,UAAM,YAAY,CAAC;AAEnB,QAAI,SAAS,WAAW,GAAI,WAAU,KAAK,UAAU;AACrD,QAAI,SAAS,sBAAsB,IAAM,WAAU,KAAK,wBAAwB;AAChF,QAAI,KAAK,yBAAyB,QAAQ,IAAI,GAAI,WAAU,KAAK,aAAa;AAEvE,WAAA;AAAA,EAAA;AAAA,EAGT,uBAAuB,UAAU;AAC/B,UAAM,aAAa,CAAC;AAEpB,QAAI,SAAS,WAAW,GAAI,YAAW,KAAK,UAAU;AACtD,QAAI,SAAS,sBAAsB,IAAM,YAAW,KAAK,wBAAwB;AACjF,QAAI,KAAK,yBAAyB,QAAQ,IAAI,GAAI,YAAW,KAAK,aAAa;AAExE,WAAA;AAAA,EAAA;AAAA,EAGT,yBAAyB,UAAU;AACjC,UAAM,QAAQ,CAAC;AAEX,QAAA,SAAS,WAAW,IAAI;AAC1B,YAAM,KAAK,6BAA6B;AAAA,IAAA;AAGtC,QAAA,SAAS,sBAAsB,KAAM;AACvC,YAAM,KAAK,oDAAoD;AAAA,IAAA;AAG1D,WAAA;AAAA,EAAA;AAAA,EAGT,mBAAmB,UAAU;AACpB,WAAA;AAAA,MACL,EAAE,WAAW,4BAA4B,UAAU,SAAS,aAAa,IAAI;AAAA,MAC7E,EAAE,WAAW,yBAAyB,UAAU,SAAS,WAAW,GAAG;AAAA,MACvE,EAAE,WAAW,0BAA0B,UAAU,KAAK,yBAAyB,QAAQ,IAAI,GAAG;AAAA,IAChG;AAAA,EAAA;AAAA,EAGF,yBAAyB,UAAU;AAC3B,UAAA,WAAW,SAAS,YAAY;AAChC,UAAA,aAAa,SAAS,cAAc;AACpC,UAAA,aAAa,SAAS,cAAc;AAElC,YAAA,WAAW,aAAa,cAAc;AAAA,EAAA;AAAA,EAGhD,wBAAwB,UAAU;AAChC,UAAM,QAAQ,CAAC;AAEX,QAAA,SAAS,WAAW,IAAI;AAC1B,YAAM,KAAK,qBAAqB;AAAA,IAAA;AAG9B,QAAA,SAAS,sBAAsB,KAAM;AACvC,YAAM,KAAK,uBAAuB;AAAA,IAAA;AAGpC,QAAI,KAAK,yBAAyB,QAAQ,IAAI,IAAI;AAChD,YAAM,KAAK,kCAAkC;AAAA,IAAA;AAGxC,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQT,0BAA0B,SAAS,UAAU;AACvC,QAAA;AACK,aAAA;AAAA;AAAA,QAEL,OAAO;AAAA,UACL,UAAU,SAAS,YAAY;AAAA,UAC/B,cAAc,SAAS,uBAAuB;AAAA,UAC9C,YAAY,SAAS,cAAc;AAAA,UACnC,OAAO,SAAS,SAAS;AAAA,UACzB,UAAU,SAAS,aAAa;AAAA,UAChC,UAAU,SAAS,YAAY;AAAA,UAC/B,QAAQ,SAAS,UAAU;AAAA,QAC7B;AAAA;AAAA,QAGA,WAAW;AAAA,UACT,WAAW,KAAK,wBAAwB,QAAQ;AAAA,UAChD,QAAQ,KAAK,qBAAqB,QAAQ;AAAA,UAC1C,iBAAiB,KAAK,8BAA8B,QAAQ;AAAA,UAC5D,mBAAmB,KAAK,gCAAgC,QAAQ;AAAA,QAClE;AAAA;AAAA,QAGA,YAAY;AAAA,UACV,YAAY,KAAK,yBAAyB,QAAQ;AAAA,UAClD,aAAa,KAAK,0BAA0B,QAAQ;AAAA,UACpD,cAAc,KAAK,2BAA2B,QAAQ;AAAA,UACtD,sBAAsB,KAAK,8BAA8B,QAAQ;AAAA,QACnE;AAAA;AAAA,QAGA,SAAS;AAAA,UACP,kBAAkB,KAAK,+BAA+B,QAAQ;AAAA,UAC9D,oBAAoB,KAAK,iCAAiC,QAAQ;AAAA,UAClE,mBAAmB,KAAK,gCAAgC,QAAQ;AAAA,UAChE,oBAAoB,KAAK,iCAAiC,QAAQ;AAAA,QACpE;AAAA;AAAA,QAGA,OAAO;AAAA,UACL,iBAAiB,KAAK,8BAA8B,QAAQ;AAAA,UAC5D,kBAAkB,KAAK,+BAA+B,QAAQ;AAAA,UAC9D,cAAc,KAAK,2BAA2B,QAAQ;AAAA,UACtD,eAAe,KAAK,4BAA4B,QAAQ;AAAA,QAC1D;AAAA;AAAA,QAGA,cAAc;AAAA;AAAA,QAGd,UAAU;AAAA,UACR,UAAU,KAAK;AAAA,UACf,sBAAqB,oBAAI,KAAK,GAAE,YAAY;AAAA,UAC5C,SAAS;AAAA,QAAA;AAAA,MAEb;AAAA,aACOA,QAAO;AACT,WAAA,QAAQ,MAAM,4CAA4CA,MAAK;AAC7D,aAAA;AAAA,QACL,OAAO,EAAE,UAAU,GAAG,cAAc,GAAG,YAAY,GAAG,OAAO,GAAG,UAAU,GAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,QACpG,WAAW,EAAE,WAAW,IAAI,QAAQ,IAAI,iBAAiB,IAAI,mBAAmB,GAAG;AAAA,QACnF,YAAY,EAAE,YAAY,IAAI,aAAa,IAAI,cAAc,IAAI,sBAAsB,GAAG;AAAA,QAC1F,SAAS,EAAE,kBAAkB,IAAI,oBAAoB,IAAI,mBAAmB,IAAI,oBAAoB,GAAG;AAAA,QACvG,OAAO,EAAE,iBAAiB,IAAI,kBAAkB,IAAI,cAAc,IAAI,eAAe,GAAG;AAAA,QACxF,cAAc;AAAA,QACd,UAAU,EAAE,UAAU,KAAK,UAAU,sBAAyB,oBAAA,KAAO,GAAA,YAAe,GAAA,SAAS,QAAQ;AAAA,MACvG;AAAA,IAAA;AAAA,EACF;AAGJ;AChiDO,MAAM,iBAAiB;AAAA,EAC5B,cAAc;AAAA,IACZ;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,aAAa;AAAA,IACf;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,aAAa;AAAA,IACf;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,aAAa;AAAA,IAAA;AAAA,EAEjB;AAAA,EACA,cAAc;AAAA,IACZ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,qBAAqB;AAAA,IACrB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,wBAAwB;AAAA,IACxB,aAAa;AAAA,IACb,qBAAqB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,IACb,KAAK;AAAA,MACH,SAAS;AAAA,MACT,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW,CAAC,OAAO;AAAA,MACnB,YAAY;AAAA,MACZ,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,cAAc;AAAA,MACd,WAAW;AAAA,MACX,eAAe;AAAA,MACf,mBAAmB;AAAA,IACrB;AAAA,IACA,OAAO;AAAA,MACL,cAAc;AAAA,MACd,oBAAoB;AAAA,MACpB,gBAAgB;AAAA,IAAA;AAAA,EAEpB;AAAA,EACA,qBAAqB;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ;AAKO,MAAM,qBAAqB,CAAC,IAAI,MAAM,MAAM,aAAa,sBAAsB;AAAA,EACpF;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY;AACd;AAEO,MAAM,wBAAwB;AAAA,EACnC,GAAG;AAAA,EACH,cAAc;AAAA,IACZ,GAAG,eAAe;AAAA,IAClB,eAAe;AAAA,IACf,0BAA0B;AAAA,IAC1B,sBAAsB;AAAA;AAAA,IAEtB,kBAAkB;AAAA,MAChB,mBAAmB;AAAA,QACjB,MAAM;AAAA;AAAA,QACN,QAAQ;AAAA;AAAA,QACR,MAAM;AAAA;AAAA,MAAA;AAAA,IAEV;AAAA;AAAA,IAEA,yBAAyB;AAAA,MACvB,MAAM;AAAA,QACJ,MAAM;AAAA,QACN,OAAO,CAAC,GAAG,CAAC;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,MACA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,OAAO,CAAC,GAAG,CAAC;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,MACA,MAAM;AAAA,QACJ,MAAM;AAAA,QACN,OAAO,CAAC,GAAG,CAAC;AAAA,QACZ,WAAW;AAAA,MAAA;AAAA,IACb;AAAA,EAEJ;AAAA,EACA,cAAc;AAAA,IACZ;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO,CAAC,GAAG,CAAC;AAAA,MACZ,aAAa;AAAA,MACb,iBAAiB,CAAC,GAAG,CAAC;AAAA,MACtB,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,eAAe;AAAA,IACjB;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO,CAAC,GAAG,CAAC;AAAA,MACZ,aAAa;AAAA,MACb,iBAAiB,CAAC,GAAG,EAAE;AAAA,MACvB,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,eAAe;AAAA,IACjB;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO,CAAC,GAAG,EAAE;AAAA,MACb,aAAa;AAAA,MACb,iBAAiB,CAAC,GAAG,EAAE;AAAA,MACvB,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,eAAe;AAAA,IAAA;AAAA,EAEnB;AAAA,EACA,eAAe;AAAA,IACb,iBAAiB;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QACE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,SAAS,QAAQ;AAAA,QACxC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,SAAS,SAAS;AAAA,QAC3C,MAAM,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,SAAS,QAAQ;AAAA,MAAA;AAAA,IAE7C;AAAA,IACA,gBAAgB;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QACE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,WAAW,OAAO;AAAA,QACzC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,WAAW,SAAS;AAAA,QAC7C,MAAM,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,WAAW,OAAO;AAAA,MAAA;AAAA,IAE9C;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QACE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,UAAU,KAAM,WAAW,EAAE;AAAA,QACpD,QAAQ,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,UAAU,KAAM,WAAW,EAAE;AAAA,QACvD,MAAM,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,UAAU,KAAM,WAAW,EAAE;AAAA,MAAA;AAAA,IAEzD;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QACE,MAAM,EAAE,QAAQ,GAAG,MAAM,GAAG,WAAW,GAAG;AAAA,QAC1C,QAAQ,EAAE,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,WAAW,GAAG;AAAA,QACjD,MAAM,EAAE,QAAQ,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,WAAW,GAAG;AAAA,MAAA;AAAA,IAEtD;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QACE,MAAM,EAAE,WAAW,GAAG,QAAQ,EAAE;AAAA,QAChC,QAAQ,EAAE,WAAW,GAAG,QAAQ,EAAE;AAAA,QAClC,MAAM,EAAE,WAAW,IAAI,QAAQ,EAAE;AAAA,MAAA;AAAA,IAErC;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QACE,MAAM,EAAE,eAAe,GAAG,YAAY,SAAS;AAAA,QAC/C,QAAQ,EAAE,eAAe,GAAG,YAAY,SAAS;AAAA,QACjD,MAAM,EAAE,eAAe,GAAG,YAAY,UAAU;AAAA,MAAA;AAAA,IAClD;AAAA,EAEJ;AAAA,EACA,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,EAAE,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,oBAAoB;AAAA,MACjF,EAAE,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,oBAAoB;AAAA,MACjF,EAAE,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,oBAAoB;AAAA,MACjF,EAAE,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,sBAAsB;AAAA,IACrF;AAAA,IACA,QAAQ;AAAA,MACN,EAAE,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,GAAG,GAAG,EAAE,GAAG,MAAM,QAAQ;AAAA,MACtE,EAAE,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,UAAU;AAAA,MACvE,EAAE,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,GAAG,GAAG,EAAE,GAAG,MAAM,oBAAoB;AAAA,MAClF,EAAE,UAAU,CAAC,IAAI,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,oBAAoB;AAAA,IACpF;AAAA,IACA,MAAM;AAAA,MACJ,EAAE,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,SAAS,IAAI,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,MAAM,cAAc;AAAA,MAC/E,EAAE,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,SAAS,IAAI,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,MAAM,SAAS;AAAA,MAC1E,EAAE,UAAU,CAAC,IAAI,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,sBAAsB;AAAA,MACpF,EAAE,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,SAAS,IAAI,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,MAAM,SAAS;AAAA,IAAA;AAAA,EAE9E;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,aAAa,oBAAoB,MAAM,YAAY;AAAA,MAC1F,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,aAAa,uBAAuB,MAAM,cAAc;AAAA,MAC/F,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,aAAa,oBAAoB,MAAM,sBAAsB;AAAA,IACtG;AAAA,IACA,QAAQ;AAAA,MACN,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,aAAa,sBAAsB,MAAM,QAAQ;AAAA,MACxF,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,aAAa,qBAAqB,MAAM,cAAc;AAAA,MAC1F,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,aAAa,mBAAmB,MAAM,oBAAoB;AAAA,IAChG;AAAA,IACA,MAAM;AAAA,MACJ,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,aAAa,mBAAmB,MAAM,mBAAmB;AAAA,MAC7F,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG,aAAa,qBAAqB,MAAM,gBAAgB;AAAA,MAC9F,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,aAAa,qBAAqB,MAAM,YAAY;AAAA,IAAA;AAAA,EAE5F;AAAA,EACA,YAAY;AAAA,IACV;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,QACP,EAAE,IAAI,SAAS,OAAO,MAAM,MAAM,OAAO;AAAA,QACzC,EAAE,IAAI,UAAU,OAAO,MAAM,MAAM,SAAS;AAAA,QAC5C,EAAE,IAAI,UAAU,OAAO,MAAM,MAAM,UAAU;AAAA,QAC7C,EAAE,IAAI,UAAU,OAAO,MAAM,MAAM,OAAO;AAAA,QAC1C,EAAE,IAAI,cAAc,OAAO,MAAM,MAAM,UAAU;AAAA,QACjD,EAAE,IAAI,aAAa,OAAO,MAAM,MAAM,UAAU;AAAA,MAAA;AAAA,IAEpD;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,QACP,EAAE,IAAI,OAAO,OAAO,MAAM,MAAM,WAAW;AAAA,QAC3C,EAAE,IAAI,OAAO,OAAO,MAAM,MAAM,OAAO;AAAA,QACvC,EAAE,IAAI,UAAU,OAAO,MAAM,MAAM,SAAS;AAAA,QAC5C,EAAE,IAAI,QAAQ,OAAO,MAAM,MAAM,OAAO;AAAA,QACxC,EAAE,IAAI,OAAO,OAAO,MAAM,MAAM,QAAQ;AAAA,QACxC,EAAE,IAAI,OAAO,OAAO,MAAM,MAAM,OAAO;AAAA,MAAA;AAAA,IAE3C;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,QACP,EAAE,IAAI,SAAS,OAAO,MAAM,MAAM,UAAU;AAAA,QAC5C,EAAE,IAAI,QAAQ,OAAO,KAAK,MAAM,OAAO;AAAA,QACvC,EAAE,IAAI,OAAO,OAAO,MAAM,MAAM,WAAW;AAAA,QAC3C,EAAE,IAAI,QAAQ,OAAO,MAAM,MAAM,SAAS;AAAA,QAC1C,EAAE,IAAI,UAAU,OAAO,MAAM,MAAM,SAAS;AAAA,QAC5C,EAAE,IAAI,QAAQ,OAAO,MAAM,MAAM,OAAO;AAAA,MAAA;AAAA,IAE5C;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,QACP,EAAE,IAAI,UAAU,OAAO,MAAM,MAAM,OAAO;AAAA,QAC1C,EAAE,IAAI,QAAQ,OAAO,MAAM,MAAM,SAAS;AAAA,QAC1C,EAAE,IAAI,OAAO,OAAO,MAAM,MAAM,MAAM;AAAA,QACtC,EAAE,IAAI,QAAQ,OAAO,KAAK,MAAM,UAAU;AAAA,QAC1C,EAAE,IAAI,QAAQ,OAAO,MAAM,MAAM,MAAM;AAAA,QACvC,EAAE,IAAI,SAAS,OAAO,MAAM,MAAM,QAAQ;AAAA,MAAA;AAAA,IAE9C;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,QACP,EAAE,IAAI,UAAU,OAAO,MAAM,MAAM,UAAU;AAAA,QAC7C,EAAE,IAAI,UAAU,OAAO,MAAM,MAAM,WAAW;AAAA,QAC9C,EAAE,IAAI,YAAY,OAAO,MAAM,MAAM,YAAY;AAAA,QACjD,EAAE,IAAI,WAAW,OAAO,MAAM,MAAM,UAAU;AAAA,QAC9C,EAAE,IAAI,SAAS,OAAO,MAAM,MAAM,UAAU;AAAA,QAC5C,EAAE,IAAI,cAAc,OAAO,KAAK,MAAM,UAAU;AAAA,MAAA;AAAA,IAEpD;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,QACP,EAAE,IAAI,OAAO,OAAO,MAAM,MAAM,QAAQ;AAAA,QACxC,EAAE,IAAI,OAAO,OAAO,MAAM,MAAM,SAAS;AAAA,QACzC,EAAE,IAAI,QAAQ,OAAO,MAAM,MAAM,YAAY;AAAA,QAC7C,EAAE,IAAI,SAAS,OAAO,MAAM,MAAM,QAAQ;AAAA,QAC1C,EAAE,IAAI,SAAS,OAAO,MAAM,MAAM,OAAO;AAAA,QACzC,EAAE,IAAI,QAAQ,OAAO,KAAK,MAAM,QAAQ;AAAA,MAAA;AAAA,IAC1C;AAAA,EAEJ;AAAA;AAAA,EAEA,0BAA0B;AAAA;AAAA,IAExB,gBAAgB;AAAA,MACd;AAAA,QACE,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,aAAa;AAAA,QACb,KAAK;AAAA,QACL,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,aAAa;AAAA,QACb,KAAK;AAAA,QACL,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,aAAa;AAAA,QACb,KAAK;AAAA,QACL,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,aAAa;AAAA,QACb,KAAK;AAAA,QACL,aAAa;AAAA,MAAA;AAAA,IAEjB;AAAA;AAAA,IAEA,oBAAoB;AAAA,MAClB,MAAM;AAAA,QACJ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,eAAe;AAAA,QACf,cAAc,CAAC,aAAa,WAAW;AAAA,QACvC,gBAAgB;AAAA,MAClB;AAAA,MACA,QAAQ;AAAA,QACN,WAAW;AAAA,QACX,WAAW;AAAA,QACX,eAAe;AAAA,QACf,cAAc,CAAC,aAAa,aAAa,aAAa;AAAA,QACtD,gBAAgB;AAAA,MAClB;AAAA,MACA,MAAM;AAAA,QACJ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,eAAe;AAAA,QACf,cAAc,CAAC,aAAa,aAAa,eAAe,kBAAkB;AAAA,QAC1E,gBAAgB;AAAA,MAAA;AAAA,IAEpB;AAAA;AAAA,IAEA,uBAAuB;AAAA,MACrB,WAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,aAAa;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,kBAAkB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IACF;AAAA,EAEJ;AAAA,EACA,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,MACf,cAAc;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IAEJ;AAAA,IACA,gBAAgB;AAAA,MACd,cAAc;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IAEJ;AAAA,IACA,mBAAmB;AAAA,MACjB,cAAc;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IAEJ;AAAA,IACA,qBAAqB;AAAA,MACnB,cAAc;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IAEJ;AAAA,IACA,mBAAmB;AAAA,MACjB,cAAc;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IAEJ;AAAA,IACA,qBAAqB;AAAA,MACnB,cAAc;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAEA,gCAAgC,CAAC,aAAa,WAAW;AACvD,UAAM,WAAW,sBAAsB,yBAAyB,mBAAmB,UAAU;AACvF,UAAA,iBAAiB,sBAAsB,yBAAyB;AACtE,UAAM,eAAe,SAAS;AAC9B,UAAM,aAAa,sBAAsB;AAGnC,UAAA,eAAe,aAAa,KAAK,MAAM,KAAK,OAAO,IAAI,aAAa,MAAM,CAAC;AACjF,UAAM,kBAAkB,eAAe,KAAK,CAAQ,SAAA,KAAK,OAAO,YAAY;AAG5E,QAAI,QAAQ;AAGZ,UAAM,gBAAgB,KAAK,OAAA,IAAW,SAAS,kBAAkB,iBAAiB;AAElF,QAAI,eAAe;AACR,eAAA,KAAK,MAAM,KAAK,OAAO,KAAK,SAAS,YAAY,SAAS,YAAY,EAAE,IAAI,SAAS;AACrF,eAAA;AAAA,IAAA,OACJ;AACI,eAAA,KAAK,MAAM,KAAK,OAAO,KAAK,SAAS,YAAY,SAAS,YAAY,EAAE,IAAI,SAAS;AACrF,eAAA,KAAK,MAAM,KAAK,OAAO,KAAK,SAAS,YAAY,SAAS,YAAY,EAAE,IAAI,SAAS;AAGvF,aAAA,WAAW,UAAU,KAAK,IAAI,SAAS,MAAM,IAAI,SAAS,eAAe;AACrE,iBAAA,KAAK,MAAM,KAAK,OAAO,KAAK,SAAS,YAAY,SAAS,YAAY,EAAE,IAAI,SAAS;AAAA,MAAA;AAAA,IAChG;AAII,UAAA,YAAY,WAAW,KAAK,MAAM,KAAK,OAAO,IAAI,WAAW,MAAM,CAAC;AACtE,QAAA,YAAY,WAAW,KAAK,MAAM,KAAK,OAAO,IAAI,WAAW,MAAM,CAAC;AAGjE,WAAA,UAAU,OAAO,UAAU,IAAI;AACxB,kBAAA,WAAW,KAAK,MAAM,KAAK,OAAW,IAAA,WAAW,MAAM,CAAC;AAAA,IAAA;AAIhE,UAAA,uBAAuB,CAAC,UAAU,UAAU;AAChD,YAAM,UAAU,CAAC;AACjB,eAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAExB,cAAA,eAAe,SAAS,QAAQ,KAAK,MAAM,KAAK,WAAW,SAAS,QAAQ,MAAM,CAAC;AACzF,gBAAQ,KAAK;AAAA,UACX,IAAI;AAAA,UACJ,OAAO,aAAa;AAAA,UACpB,MAAM,aAAa;AAAA,UACnB,UAAU,SAAS;AAAA,QAAA,CACpB;AAAA,MAAA;AAEI,aAAA;AAAA,IACT;AAEM,UAAA,SAAS,qBAAqB,WAAW,MAAM;AAC/C,UAAA,SAAS,qBAAqB,WAAW,MAAM;AAGrD,QAAI,eAAe;AAEnB,YAAQ,cAAc;AAAA,MACpB,KAAK;AACa,wBAAA,SAAS,SAAS,SAAS;AAC3C,sBAAc,SAAS,kBAAkB,SAAS,MAAM,GAAG,sBAAsB,KAAK,IAAI,QAAQ,MAAM,CAAC,MAAM,KAAK,IAAI,QAAQ,MAAM,CAAC;AACvI;AAAA,MAEF,KAAK;AACa,wBAAA,SAAS,SAAS,SAAS;AAC3C,sBAAc,SAAS,kBAAkB,SAAS,MAAM,GAAG,uBAAuB,KAAK,IAAI,QAAQ,MAAM,CAAC,MAAM,KAAK,IAAI,QAAQ,MAAM,CAAC;AACxI;AAAA,MAEF,KAAK;AACa,wBAAA,WAAW,SAAS,UAAU;AAChC,sBAAA,WAAW,SACvB,uBAAuB,MAAM,aAC7B,sBAAsB,MAAM,MAAM,MAAM;AAC1C;AAAA,MAEF,KAAK;AACH,cAAM,aAAa,KAAK,IAAI,SAAS,MAAM;AAC3C,wBAAgB,WAAW,SAAS;AACpC,sBAAc,eAAe,MAAM,MAAM,MAAM,OAAO,UAAU;AAChE;AAAA,MAEF;AACkB,wBAAA,SAAS,SAAS,SAAS;AAC7B,sBAAA,sBAAsB,MAAM,OAAO,MAAM;AAAA,IAAA;AAGpD,WAAA;AAAA,MACL,eAAe;AAAA,MACf;AAAA,MACA,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,OAAO;AAAA,QACP,UAAU,UAAU;AAAA,QACpB,eAAe,UAAU;AAAA,MAC3B;AAAA,MACA,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,OAAO;AAAA,QACP,UAAU,UAAU;AAAA,QACpB,eAAe,UAAU;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa,gBAAgB;AAAA,MAC7B,gBAAgB,gBAAgB;AAAA,MAChC;AAAA,MACA,UAAU;AAAA,QACR,YAAY,KAAK,IAAI,SAAS,MAAM;AAAA,QACpC,SAAS,WAAW;AAAA,QACpB,QAAQ,SAAS,SAAS,SAAS;AAAA,QACnC,SAAS,SAAS,SAAS,SAAS;AAAA,MAAA;AAAA,IAExC;AAAA,EACF;AAAA,EACA,qBAAqB;AAAA,IACnB,GAAG,eAAe;AAAA,IAClB;AAAA,IACA;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,IACP,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AAAA,EACA,eAAe;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EAAA;AAEf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvnBA,MAAMG,iBAAiB;AAAA,EACrBC,iBAAiB;AAAA,IACfC,IAAI;AAAA,IACJC,MAAM;AAAA,IACNC,aAAa;AAAA,IACbC,MAAM;AAAA,EACR;AAAA,EACAC,iBAAiB;AAAA,IACfJ,IAAI;AAAA,IACJC,MAAM;AAAA,IACNC,aAAa;AAAA,IACbC,MAAM;AAAA,EACR;AAAA,EACAE,oBAAoB;AAAA,IAClBL,IAAI;AAAA,IACJC,MAAM;AAAA,IACNC,aAAa;AAAA,IACbC,MAAM;AAAA,EACR;AAAA,EACAG,qBAAqB;AAAA,IACnBN,IAAI;AAAA,IACJC,MAAM;AAAA,IACNC,aAAa;AAAA,IACbC,MAAM;AAAA,EAAA;AAEV;AAGA,MAAMI,sBAAsBA,CAAC;AAAA,EAAEC;AAAO,MAAM;AACpC,QAAA;AAAA,IAAEC;AAAAA,IAAMC;AAAAA,EAAAA,IAAcC,aAAAA,WAAWC,aAAa;AAK9C,QAAA;AAAA,IACJC;AAAAA,IACAC;AAAAA,IACAC;AAAAA,IAEAC;AAAAA,IACAC;AAAAA,EAGF,IAAIC,eAAe,kBAAkB;AAGrC,QAAMC,mBAAmB;AAGzB,QAAM,CAACC,WAAWC,YAAY,IAAIC,sBAAS;AAAA,IACzCC,QAAQ;AAAA;AAAA,IACRC,OAAO;AAAA,IACPC,OAAO;AAAA,IACPC,aAAa;AAAA;AAAA,IACbC,YAAY;AAAA,IACZC,UAAU;AAAA,IACVC,gBAAgB;AAAA;AAAA,IAGhBC,iBAAiBhC,eAAeC,gBAAgBC;AAAAA,IAChD+B,0BAA0B;AAAA;AAAA;AAAA,IAG1BC,cAAc;AAAA,MACZC,iBAAiB;AAAA,QACfC,SAAS,CAAE;AAAA,QACXC,cAAc;AAAA,QACdC,SAAS,CAAE;AAAA,QACXC,aAAa;AAAA,MACf;AAAA,MACAC,iBAAiB;AAAA,QACfC,SAAS;AAAA,QACTC,SAAS;AAAA,QACTC,eAAe;AAAA,QACfL,SAAS,CAAE;AAAA,QACXC,aAAa;AAAA,MACf;AAAA,MACAK,oBAAoB;AAAA,QAClB7C,cAAc;AAAA,QACduC,SAAS,CAAE;AAAA,QACXC,aAAa;AAAA,MACf;AAAA,MACAM,qBAAqB;AAAA,QACnBC,QAAQ,CAAE;AAAA,QACVC,QAAQ,CAAE;AAAA,QACVJ,eAAe;AAAA,QACfJ,aAAa;AAAA,MAAA;AAAA,IACf;AAAA,EACF,CACD;AAED,QAAM,CAACS,iBAAiBC,kBAAkB,IAAIzB,aAAAA,SAAS,IAAI;AAC3D,QAAM,CAAC0B,aAAaC,cAAc,IAAI3B,aAAAA,SAAS,KAAK;AACpD,QAAM,CAAC4B,UAAUC,WAAW,IAAI7B,aAAAA,SAAS,IAAI;AAG7C,QAAM,CAAC8B,aAAa,IAAI9B,sBAAS,MAAM,IAAI+B,6BAA6B;AAGlE,QAAA;AAAA,IACJC;AAAAA,EAKF,IAAIC,oBAAoB,kBAAkB;AAEpC,QAAA;AAAA,IACJC;AAAAA,EAEF,IAAIC,2BAA2B/C,WAAW;AAAA,IAUxCgD,eAAejD,MAAMkD,SAASD,iBAAiB;AAAA,EAAA,CAChD;AAGDE,eAAAA,UAAU,MAAM;AACVxC,QAAAA,UAAUG,WAAW,aAAaV,WAAW;AAC/CgD,iBAAW,MAAM;AACf7C,8BAAsB,uBAAuBG,gBAAgB;AAAA,SAC5D,GAAI;AAAA,IAAA;AAAA,EACT,GACC,CAACC,UAAUG,QAAQV,WAAWG,uBAAuBG,gBAAgB,CAAC;AAGzE,QAAM2C,uBAAuBC,aAAAA,YAAY,CAACC,YAAYrC,eAAe;AACnE,UAAMsC,SAASC,sBAAsBC,aAAaC,wBAAwBzC,UAAU;AAEpF,YAAQqC,YAAU;AAAA,MAChB,KAAKlE,eAAeC,gBAAgBC;AAClC,eAAOqE,2BAA2BJ,MAAM;AAAA,MAC1C,KAAKnE,eAAeM,gBAAgBJ;AAClC,eAAOsE,2BAA2BL,MAAM;AAAA,MAC1C,KAAKnE,eAAeO,mBAAmBL;AACrC,eAAOuE,8BAA8BN,MAAM;AAAA,MAC7C,KAAKnE,eAAeQ,oBAAoBN;AACtC,eAAOwE,+BAA+BP,MAAM;AAAA,MAC9C;AACE,eAAOI,2BAA2BJ,MAAM;AAAA,IAAA;AAAA,EAE9C,GAAG,EAAE;AAGCI,QAAAA,6BAA6BN,yBAAaE,CAAW,WAAA;AACzD,UAAM9B,eAAesC,KAAKC,MAAMD,KAAKE,OAAO,KAAKV,OAAOW,MAAM,CAAC,IAAIX,OAAOW,MAAM,CAAC,IAAI,EAAE,IAAIX,OAAOW,MAAM,CAAC;AACzG,UAAMC,aAAaX,sBAAsBW;AACnCC,UAAAA,iBAAiBD,WAAWJ,KAAKC,MAAMD,KAAKE,OAAO,IAAIE,WAAWE,MAAM,CAAC;AACzEC,UAAAA,eAAeF,eAAe5C,QAAQuC,KAAKC,MAAMD,KAAKE,WAAWG,eAAe5C,QAAQ6C,MAAM,CAAC;AAG/F7C,UAAAA,UAAU+C,MAAMC,KAAK;AAAA,MAAEH,QAAQ5C;AAAAA,IAAAA,GAAgB,CAACgD,GAAGC,WAAW;AAAA,MAClEpF,IAAIoF;AAAAA,MACJC,OAAOL,aAAaK;AAAAA,MACpBpF,MAAM+E,aAAa/E;AAAAA,IAAAA,EACnB;AAGF,UAAMmC,UAAU,oBAAIkD,IAAI,CAACnD,YAAY,CAAC;AAC/BC,WAAAA,QAAQmD,OAAO,GAAG;AACjBC,YAAAA,cAAcf,KAAKC,MAAMD,KAAKE,WAAWV,OAAOwB,SAAS,IAAI;AAC/DD,UAAAA,gBAAgBrD,gBAAgBqD,cAAc,GAAG;AACnDpD,gBAAQsD,IAAIF,WAAW;AAAA,MAAA;AAAA,IACzB;AAGK,WAAA;AAAA,MACLtD;AAAAA,MACAC;AAAAA,MACAC,SAAS6C,MAAMC,KAAK9C,OAAO,EAAEuD,KAAK,MAAMlB,KAAKE,OAAO,IAAI,GAAG;AAAA,MAC3DtC,aAAa,iBAAiB2C,aAAa/E,KAAK2F,YAAa,CAAA;AAAA,IAC/D;AAAA,EACF,GAAG,EAAE;AAGCtB,QAAAA,6BAA6BP,yBAAaE,CAAW,WAAA;AACnD1B,UAAAA,UAAUkC,KAAKC,MAAMD,KAAKE,WAAWV,OAAOW,MAAM,CAAC,CAAC,IAAI;AACxDpC,UAAAA,UAAUiC,KAAKC,MAAMD,KAAKE,OAAAA,KAAYV,OAAOW,MAAM,CAAC,IAAIrC,QAAQ,IAAI;AAC1E,UAAME,gBAAgBF,UAAUC;AAGhC,UAAMJ,UAAU,oBAAIkD,IAAI,CAAC7C,aAAa,CAAC;AAChCL,WAAAA,QAAQmD,OAAO,GAAG;AACjBC,YAAAA,cAAcf,KAAKC,MAAMD,KAAKE,WAAWV,OAAOwB,SAAS,IAAI;AAC/DD,UAAAA,gBAAgB/C,iBAAiB+C,cAAc,GAAG;AACpDpD,gBAAQsD,IAAIF,WAAW;AAAA,MAAA;AAAA,IACzB;AAGK,WAAA;AAAA,MACLjD;AAAAA,MACAC;AAAAA,MACAC;AAAAA,MACAL,SAAS6C,MAAMC,KAAK9C,OAAO,EAAEuD,KAAK,MAAMlB,KAAKE,OAAO,IAAI,GAAG;AAAA,MAC3DtC,aAAa,YAAYE,OAAO,MAAMC,OAAO;AAAA,IAC/C;AAAA,EACF,GAAG,EAAE;AAGC+B,QAAAA,gCAAgCR,yBAAaE,CAAW,WAAA;AAE5D,UAAM4B,iBAAiB,CAAC,gBAAgB,kBAAkB,YAAY,cAAc,SAAS;AAC7F,QAAIC,iBAAiBD;AAGjBzE,QAAAA,UAAUO,eAAe,QAAQ;AAClB,uBAAA,CAAC,gBAAgB,gBAAgB;AAAA,IAAA,WACzCP,UAAUO,eAAe,UAAU;AAC5CmE,uBAAiB,CAAC,gBAAgB,kBAAkB,YAAY,SAAS;AAAA,IAAA;AAGrEC,UAAAA,gBAAgBD,eAAerB,KAAKC,MAAMD,KAAKE,OAAO,IAAImB,eAAef,MAAM,CAAC;AAEtF,YAAQgB,eAAa;AAAA,MACnB,KAAK,gBAAgB;AAEblG,cAAAA,gBAAe4E,KAAKC,MAAMD,KAAKE,WAAWV,OAAOwB,SAAS,IAAI;AACpE,cAAMZ,aAAaX,sBAAsBW;AACnCC,cAAAA,iBAAiBD,WAAWJ,KAAKC,MAAMD,KAAKE,OAAO,IAAIE,WAAWE,MAAM,CAAC;AACzEC,cAAAA,eAAeF,eAAe5C,QAAQuC,KAAKC,MAAMD,KAAKE,WAAWG,eAAe5C,QAAQ6C,MAAM,CAAC;AAE/F7C,cAAAA,UAAU+C,MAAMC,KAAK;AAAA,UAAEH,QAAQlF;AAAAA,QAAAA,GAAgB,CAACsF,GAAGa,OAAO;AAAA,UAC9DhG,IAAIgG;AAAAA,UACJX,OAAOL,aAAaK;AAAAA,UACpBpF,MAAM+E,aAAa/E;AAAAA,QAAAA,EACnB;AAEF,cAAMmC,UAAU6D,sBAAsBpG,eAAcoE,OAAOwB,SAAS;AAE7D,eAAA;AAAA,UACLM,eAAe;AAAA,UACflG,cAAAA;AAAAA,UACAqC;AAAAA,UACAE;AAAAA,UACAC,aAAa,YAAY2C,aAAa/E,KAAK2F,YAAa,CAAA;AAAA,QAC1D;AAAA,MAAA;AAAA,MAGF,KAAK,kBAAkB;AAEf/F,cAAAA,gBAAe4E,KAAKC,MAAMD,KAAKE,WAAWV,OAAOwB,SAAS,IAAI;AACpE,cAAMS,cAAc,CAAC,IAAI,MAAM,QAAQ,QAAQ,UAAU,SAAS,QAAQ,QAAQ,QAAQ,QAAQ,KAAK;AACvG,cAAM9D,UAAU6D,sBAAsBpG,eAAcoE,OAAOwB,SAAS;AAE7D,eAAA;AAAA,UACLM,eAAe;AAAA,UACflG,cAAAA;AAAAA,UACAsG,eAAeD,YAAYrG,aAAY,KAAKA,cAAauG,SAAS;AAAA,UAClEhE;AAAAA,UACAC,aAAa,sCAAsC6D,YAAYrG,aAAY,CAAC;AAAA,QAC9E;AAAA,MAAA;AAAA,MAGF,KAAK,YAAY;AAETA,cAAAA,gBAAe4E,KAAKC,MAAMD,KAAKE,YAAYV,OAAOwB,YAAY,EAAE,IAAI;AAC1E,cAAMY,eAAexG,gBAAe;AACpC,cAAMuC,UAAU6D,sBAAsBpG,eAAcoE,OAAOwB,SAAS;AAE7D,eAAA;AAAA,UACLM,eAAe;AAAA,UACflG,cAAAA;AAAAA,UACAwG;AAAAA,UACAjE;AAAAA,UACAC,aAAa;AAAA,QACf;AAAA,MAAA;AAAA,MAGF,KAAK,cAAc;AAEXE,cAAAA,UAAUkC,KAAKC,MAAMD,KAAKE,WAAWV,OAAOwB,SAAS,IAAI;AAC3DjD,YAAAA,UAAUiC,KAAKC,MAAMD,KAAKE,WAAWV,OAAOwB,SAAS,IAAI;AAC7D,eAAOjD,YAAYD,SAAS;AAC1BC,oBAAUiC,KAAKC,MAAMD,KAAKE,WAAWV,OAAOwB,SAAS,IAAI;AAAA,QAAA;AAG3D,cAAM5F,gBAAe4E,KAAK6B,IAAI/D,SAASC,OAAO;AAC9C,cAAMJ,UAAU6D,sBAAsBpG,eAAcoE,OAAOwB,SAAS;AAE7D,eAAA;AAAA,UACLM,eAAe;AAAA,UACflG,cAAAA;AAAAA,UACA0C;AAAAA,UACAC;AAAAA,UACAJ;AAAAA,UACAC,aAAa,mBAAmBE,OAAO,OAAOC,OAAO;AAAA,QACvD;AAAA,MAAA;AAAA,MAGF,KAAK,WAAW;AAEd,cAAM3C,gBAAe4E,KAAKC,MAAMD,KAAKE,OAAO,IAAIF,KAAK8B,IAAI,GAAGtC,OAAOwB,SAAS,CAAC,IAAI;AACjF,cAAMe,eAAe,CAAC,QAAQ,QAAQ,SAAS;AACzCC,cAAAA,cAAcD,aAAa/B,KAAKC,MAAMD,KAAKE,OAAO,IAAI6B,aAAazB,MAAM,CAAC;AAChF,cAAM3C,UAAU6D,sBAAsBpG,eAAcoE,OAAOwB,SAAS;AAE7D,eAAA;AAAA,UACLM,eAAe;AAAA,UACflG,cAAAA;AAAAA,UACA4G;AAAAA,UACArE;AAAAA,UACAC,aAAa;AAAA,QACf;AAAA,MAAA;AAAA,MAGF,SAAS;AAEDxC,cAAAA,gBAAe4E,KAAKC,MAAMD,KAAKE,WAAWV,OAAOwB,SAAS,IAAI;AACpE,cAAMrD,UAAU6D,sBAAsBpG,eAAcoE,OAAOwB,SAAS;AAE7D,eAAA;AAAA,UACLM,eAAe;AAAA,UACflG,cAAAA;AAAAA,UACAuC;AAAAA,UACAC,aAAa,qBAAqBxC,aAAY;AAAA,QAChD;AAAA,MAAA;AAAA,IACF;AAAA,EACF,GACC,CAACuB,UAAUO,UAAU,CAAC;AAGzB,QAAMsE,wBAAwBlC,aAAAA,YAAY,CAAC2C,eAAejB,cAAc;AACtE,UAAMrD,UAAU,oBAAIkD,IAAI,CAACoB,aAAa,CAAC;AACvC,QAAIC,WAAW;AAEf,WAAOvE,QAAQmD,OAAO,KAAKoB,WAAW,IAAI;AACpCnB,UAAAA;AAGAf,UAAAA,KAAKE,OAAO,IAAI,KAAK;AAEvB,cAAMiC,UAAUnC,KAAKE,OAAO,IAAI,MAAM,KAAK,MAAMF,KAAKC,MAAMD,KAAKE,OAAO,IAAI,CAAC,IAAI;AACjFa,sBAAckB,gBAAgBE;AAAAA,MAAAA,OACzB;AAELpB,sBAAcf,KAAKC,MAAMD,KAAKE,OAAO,IAAIc,SAAS,IAAI;AAAA,MAAA;AAGxD,UAAID,cAAc,KAAKA,eAAeC,aAAaD,gBAAgBkB,eAAe;AAChFtE,gBAAQsD,IAAIF,WAAW;AAAA,MAAA;AAEzBmB;AAAAA,IAAAA;AAIKvE,WAAAA,QAAQmD,OAAO,GAAG;AACvB,YAAMC,cAAcf,KAAKC,MAAMD,KAAKE,OAAO,IAAIc,SAAS,IAAI;AAC5D,UAAID,gBAAgBkB,eAAe;AACjCtE,gBAAQsD,IAAIF,WAAW;AAAA,MAAA;AAAA,IACzB;AAGKP,WAAAA,MAAMC,KAAK9C,OAAO,EAAEuD,KAAK,MAAMlB,KAAKE,OAAO,IAAI,GAAG;AAAA,EAC3D,GAAG,EAAE;AAGCH,QAAAA,iCAAiCT,yBAAaE,CAAW,WAAA;AAE7D,WAAOC,sBAAsBM,+BAA+BP,OAAOtC,cAAcP,UAAUO,cAAc,MAAM;AAAA,EAAA,GAC9G,CAACP,UAAUO,UAAU,CAAC;AAGnBkF,QAAAA,YAAY9C,yBAAY,OAAO+C,uBAAuB;AAE1D,UAAMC,cAAcjD,qBAAqBhE,eAAeC,gBAAgBC,IAAI8G,kBAAkB;AAG9F,UAAME,eAAe;AAAA,MACnBC,MAAM;AAAA,MACNC,QAAQ;AAAA,MACRC,MAAM;AAAA,IACR;AACMzF,UAAAA,cAAcsF,aAAaF,kBAAkB,KAAK;AAExDzF,iBAAa+F,CAAS,UAAA;AAAA,MACpB,GAAGA;AAAAA,MACH7F,QAAQ;AAAA,MACRI,YAAYmF;AAAAA,MACZpF;AAAAA,MACAD,OAAO;AAAA;AAAA,MACPI,gBAAgBwF,KAAKC,IAAI;AAAA,MACzBtF,cAAc;AAAA,QACZ,GAAGoF,KAAKpF;AAAAA,QACR,CAAClC,eAAeC,gBAAgBC,EAAE,GAAG+G;AAAAA,MAAAA;AAAAA,IACvC,EACA;AAEFhE,uBAAmB,KAAK;AACxBE,mBAAe,IAAI;AAGnB,QAAIK,qBAAqB;AACvBA,0BAAoBwD,kBAAkB;AAAA,IAAA;AAGpC,QAAA;AACEtD,UAAAA,oBAAoB,OAAOA,qBAAqB,YAAY;AAC9D,cAAMA,iBAAiB9C,aAAa,WAAW2G,KAAKC,IAAK,CAAA,IAAI;AAAA,UAC3D3F,YAAYmF;AAAAA,UACZS,UAAU;AAAA,UACVC,QAAQ/G,MAAMT,MAAM;AAAA,QAAA,CACrB;AAAA,MAAA;AAAA,aAEIL,QAAO;AACN8H,cAAAA,KAAK,kDAAkD9H,MAAK;AAAA,IAAA;AAItEkE,eAAW,MAAM;AACf7C,4BAAsB,uBAAuB,GAAG+F,YAAY1E,WAAW,EAAE;AAAA,IAAA,GACxE6B,sBAAsBC,aAAauD,eAAe;AAAA,EAAA,GACpD,CAAC5D,sBAAsBR,qBAAqBE,kBAAkB9C,WAAWD,MAAMM,KAAK,CAAC;AAGlF4G,QAAAA,eAAe5D,yBAAa6D,CAAW,WAAA;AAC3C,UAAMC,sBAAsBzG,UAAUY,aAAaZ,UAAUU,eAAe;AAC5E,QAAIgG,YAAY;AAChB,QAAIrF,gBAAgB;AAEpB,YAAQrB,UAAUU,iBAAe;AAAA,MAC/B,KAAKhC,eAAeC,gBAAgBC;AAClCyC,wBAAgBoF,oBAAoB1F;AACpC2F,oBAAYF,WAAWnF;AACvB;AAAA,MACF,KAAK3C,eAAeM,gBAAgBJ;AAClCyC,wBAAgBoF,oBAAoBpF;AACpCqF,oBAAYF,WAAWnF;AACvB;AAAA,MACF,KAAK3C,eAAeO,mBAAmBL;AACrCyC,wBAAgBoF,oBAAoBhI;AACpCiI,oBAAYF,WAAWnF;AACvB;AAAA,MACF,KAAK3C,eAAeQ,oBAAoBN;AACtCyC,wBAAgBoF,oBAAoBpF;AACpCqF,oBAAYF,WAAWnF;AACvB;AAAA,MACF;AACc,oBAAA;AAAA,IAAA;AAIhBpB,iBAAa+F,CAAQ,SAAA;AACnB,YAAMW,WAAWD,YAAYV,KAAK5F,QAAQ0C,sBAAsBC,aAAa6D,aAAaZ,KAAK5F;AACzFyG,YAAAA,WAAWb,KAAK3F,QAAQ;AAC9B,YAAMyG,gBAAgBd,KAAK3F;AACrB0G,YAAAA,iBAAiB1D,KAAKC,MAAM0C,KAAK5F,QAAQ0C,sBAAsBC,aAAa6D,UAAU,KAAKF,YAAY,IAAI;AAC3GM,YAAAA,cAAcF,gBAAgB,IAAIzD,KAAKhD,MAAO0G,iBAAiBD,gBAAiB,GAAG,IAAI;AAEtF,aAAA;AAAA,QACL,GAAGd;AAAAA,QACH5F,OAAOuG;AAAAA,QACPtG,OAAOwG;AAAAA,QACPrG,UAAUwG;AAAAA,MACZ;AAAA,IAAA,CACD;AAGW,gBAAA;AAAA,MACVN;AAAAA,MACAO,SAASP,YAAY,kBAAkB,qCAAqCrF,aAAa;AAAA,MACzFA;AAAAA,IAAAA,CACD;AAGD,UAAM6F,iBAAiB;AAAA,MACrBC,UAAUnH,UAAUU;AAAAA,MACpBgG;AAAAA,MACAU,cAAcnB,KAAKC,IAAI,IAAIlG,UAAUS;AAAAA,MACrCF,YAAYP,UAAUO;AAAAA,MACtBF,OAAOL,UAAUK;AAAAA,MACjBmG;AAAAA,MACAnF;AAAAA,MACAgG,cAAcZ;AAAAA,MACda,WAAWrB,KAAKC,IAAI;AAAA,IACtB;AAGI,QAAA;AACFlE,oBAAcuF,YAAYL,cAAc;AAAA,aACjC3I,QAAO;AACN8H,cAAAA,KAAK,8CAA8C9H,MAAK;AAAA,IAAA;AAIlE,QAAImI,WAAW;AACb7G,oBAAc,IAAI;AAAA,IAAA,OACb;AACS,oBAAA,OAAO,6CAA6CwB,aAAa,EAAE;AAAA,IAAA;AAInFoB,eAAW,MAAM;AACfV,kBAAY,IAAI;AACC,uBAAA;AAAA,OAChB,IAAI;AAAA,EAAA,GACN,CAAC/B,WAAWL,KAAK,CAAC;AAGf6H,QAAAA,mBAAmB7E,aAAAA,YAAY,MAAM;AAEzC,UAAMjC,kBAAkBV,UAAUU;AAClC,UAAM+G,UAAU/E,qBAAqBhC,iBAAiBV,UAAUO,UAAU;AAE1EN,iBAAa+F,CAAS,UAAA;AAAA,MACpB,GAAGA;AAAAA,MACH3F,OAAO2F,KAAK3F,QAAQ;AAAA,MACpBI,gBAAgBwF,KAAKC,IAAI;AAAA,MACzBtF,cAAc;AAAA,QACZ,GAAGoF,KAAKpF;AAAAA,QACR,CAACF,eAAe,GAAG+G;AAAAA,MAAAA;AAAAA,IACrB,EACA;AAGFhF,eAAW,MAAM;AACf9C,YAAM8H,QAAQxG,aAAa;AAAA,QAAEyG,MAAM;AAAA,MAAA,CAAK;AAAA,OACvC,GAAG;AAAA,EACL,GAAA,CAAC1H,WAAW0C,sBAAsB/C,KAAK,CAAC;AAGrCgI,QAAAA,iBAAiBhF,yBAAaC,CAAe,eAAA;AAC7CA,QAAAA,eAAe5C,UAAUU,gBAAiB;AAE9C,UAAM+G,UAAU/E,qBAAqBE,YAAY5C,UAAUO,UAAU;AAErEN,iBAAa+F,CAAS,UAAA;AAAA,MACpB,GAAGA;AAAAA,MACHtF,iBAAiBkC;AAAAA,MACjBnC,gBAAgBwF,KAAKC,IAAI;AAAA,MACzBtF,cAAc;AAAA,QACZ,GAAGoF,KAAKpF;AAAAA,QACR,CAACgC,UAAU,GAAG6E;AAAAA,MAAAA;AAAAA,IAChB,EACA;AAGFhF,eAAW,MAAM;AACf9C,YAAM8H,QAAQxG,aAAa;AAAA,QAAEyG,MAAM;AAAA,MAAA,CAAK;AAAA,OACvC,GAAG;AAAA,EACL,GAAA,CAAC1H,WAAW0C,sBAAsB/C,KAAK,CAAC;AAG3C,QAAMiI,uBAAuBA,MAAM;AAC3BC,UAAAA,OAAO7H,UAAUY,aAAaC;AAGpC,QAAI,CAACgH,QAAQ,CAACA,KAAK/G,SAAS;AAC1B,YAAM6E,cAAcjD,qBAAqBhE,eAAeC,gBAAgBC,IAAIoB,UAAUO,UAAU;AAChGN,mBAAa+F,CAAS,UAAA;AAAA,QACpB,GAAGA;AAAAA,QACHpF,cAAc;AAAA,UACZ,GAAGoF,KAAKpF;AAAAA,UACR,CAAClC,eAAeC,gBAAgBC,EAAE,GAAG+G;AAAAA,QAAAA;AAAAA,MACvC,EACA;AACF,aAAQ,sBAAA,cAAA,OAAA,EAAG,QAAA,QAAA,UAAA;AAAA,QAAAmC,UAAAC;AAAAA,QAAAC,YAAA;AAAA,QAAAC,cAAA;AAAA,WAAC,sBAAoB;AAAA,IAAA;AAGlC,+CACG,OAAI,EAAA,WAAWC,OAAOC,cAAa,QAAA,QAAA,UAAA;AAAA,MAAAL,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCACjC,OAAI,EAAA,WAAWC,OAAOE,gBAAe,QAAA,QAAA,UAAA;AAAA,MAAAN,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCACnC,MAAG,EAAA,WAAWC,OAAOG,eAAc,QAAA,QAAA,UAAA;AAAA,MAAAP,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAEJ,EAAAA,GAAAA,KAAK5G,WAAY,CACzD,GAGA,sBAAA,cAAC,OAAI,EAAA,WAAWiH,OAAOI,gBAAe,QAAA,QAAA,UAAA;AAAA,MAAAR,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EACnCJ,GAAAA,KAAK/G,QAAQyH,IAAI,CAACC,KAAKxE,UACrB,sBAAA,cAAA,OAAA,EACC,KAAKwE,IAAI5J,IACT,WAAWsJ,OAAOO,gBAClB,OAAO;AAAA,MAAEC,gBAAgB,GAAG1E,QAAQ,GAAG;AAAA,IAAA,GAAM,QAAA,QAAA,UAAA;AAAA,MAAA8D,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAE5CO,EAAAA,GAAAA,IAAIvE,KACP,CACD,CACH,GAGC,sBAAA,cAAA,OAAA,EAAI,WAAWiE,OAAOS,eAAc,QAAA,QAAA,UAAA;AAAA,MAAAb,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,KAClCJ,KAAK7G,QAAQuH,IAAI,CAACK,QAAQ5E,8CACxB,UACC,EAAA,KAAKA,OACL,WAAWkE,OAAOW,cAClB,SAAS,MAAMtC,aAAaqC,MAAM,GAAE,QAAA,QAAA,UAAA;AAAA,MAAAd,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCAEnC,QAAK,EAAA,WAAWC,OAAOY,cAAa,QAAA,QAAA,UAAA;AAAA,MAAAhB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,KAAEW,MAAO,CAChD,CACD,CACH,CACF;AAAA,EAEJ;AAGA,QAAMG,uBAAuBA,MAAM;AAC3BlB,UAAAA,OAAO7H,UAAUY,aAAaM;AAGpC,QAAI,CAAC2G,QAAQ,CAACA,KAAK7G,SAAS;AAC1B,YAAM2E,cAAcjD,qBAAqBhE,eAAeM,gBAAgBJ,IAAIoB,UAAUO,UAAU;AAChGN,mBAAa+F,CAAS,UAAA;AAAA,QACpB,GAAGA;AAAAA,QACHpF,cAAc;AAAA,UACZ,GAAGoF,KAAKpF;AAAAA,UACR,CAAClC,eAAeM,gBAAgBJ,EAAE,GAAG+G;AAAAA,QAAAA;AAAAA,MACvC,EACA;AACF,aAAQ,sBAAA,cAAA,OAAA,EAAG,QAAA,QAAA,UAAA;AAAA,QAAAmC,UAAAC;AAAAA,QAAAC,YAAA;AAAA,QAAAC,cAAA;AAAA,WAAC,sBAAoB;AAAA,IAAA;AAGlC,+CACG,OAAI,EAAA,WAAWC,OAAOC,cAAa,QAAA,QAAA,UAAA;AAAA,MAAAL,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCACjC,OAAI,EAAA,WAAWC,OAAOE,gBAAe,QAAA,QAAA,UAAA;AAAA,MAAAN,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCACnC,MAAG,EAAA,WAAWC,OAAOG,eAAc,QAAA,QAAA,UAAA;AAAA,MAAAP,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAEJ,EAAAA,GAAAA,KAAK5G,WAAY,CACzD,GAGA,sBAAA,cAAC,OAAI,EAAA,WAAWiH,OAAOI,gBAAe,QAAA,QAAA,UAAA;AAAA,MAAAR,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCACnC,OAAI,EAAA,WAAWC,OAAOc,iBAAgB,QAAA,QAAA,UAAA;AAAA,MAAAlB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCACpC,QAAK,EAAA,WAAWC,OAAOe,gBAAe,QAAA,QAAA,UAAA;AAAA,MAAAnB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GAAEJ,KAAK1G,OAAQ,GACtD,sBAAA,cAAC,QAAK,EAAA,WAAW+G,OAAOgB,kBAAiB,QAAA,QAAA,UAAA;AAAA,MAAApB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GAAC,GAAC,GAC1C,sBAAA,cAAA,QAAA,EAAK,WAAWC,OAAOe,gBAAe,QAAA,QAAA,UAAA;AAAA,MAAAnB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GAAEJ,KAAKzG,OAAQ,GACtD,sBAAA,cAAC,QAAK,EAAA,WAAW8G,OAAOgB,kBAAiB,QAAA,QAAA,UAAA;AAAA,MAAApB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GAAC,GAAC,GAC1C,sBAAA,cAAA,QAAA,EAAK,WAAWC,OAAOiB,gBAAe,QAAA,QAAA,UAAA;AAAA,MAAArB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAC,EAAA,GAAA,GAAC,CAC3C,CACF,GAGA,sBAAA,cAAC,OAAI,EAAA,WAAWC,OAAOS,eAAc,QAAA,QAAA,UAAA;AAAA,MAAAb,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,KAClCJ,KAAK7G,QAAQuH,IAAI,CAACK,QAAQ5E,8CACxB,UACC,EAAA,KAAKA,OACL,WAAWkE,OAAOW,cAClB,SAAS,MAAMtC,aAAaqC,MAAM,GAAE,QAAA,QAAA,UAAA;AAAA,MAAAd,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCAEnC,QAAK,EAAA,WAAWC,OAAOY,cAAa,QAAA,QAAA,UAAA;AAAA,MAAAhB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,KAAEW,MAAO,CAChD,CACD,CACH,CACF;AAAA,EAEJ;AAIA,QAAMQ,0BAA0BA,MAAM;AAC9BvB,UAAAA,OAAO7H,UAAUY,aAAaU;AAGpC,QAAI,CAACuG,QAAQ,CAACA,KAAK7G,SAAS;AAC1B,YAAM2E,cAAcjD,qBAAqBhE,eAAeO,mBAAmBL,IAAIoB,UAAUO,UAAU;AACnGN,mBAAa+F,CAAS,UAAA;AAAA,QACpB,GAAGA;AAAAA,QACHpF,cAAc;AAAA,UACZ,GAAGoF,KAAKpF;AAAAA,UACR,CAAClC,eAAeO,mBAAmBL,EAAE,GAAG+G;AAAAA,QAAAA;AAAAA,MAC1C,EACA;AACF,aAAQ,sBAAA,cAAA,OAAA,EAAG,QAAA,QAAA,UAAA;AAAA,QAAAmC,UAAAC;AAAAA,QAAAC,YAAA;AAAA,QAAAC,cAAA;AAAA,WAAC,sBAAoB;AAAA,IAAA;AAGlC,UAAMoB,yBAAyBA,MAAM;AACnC,cAAQxB,KAAKlD,eAAa;AAAA,QACxB,KAAK;AACH,qDACG,OAAI,EAAA,WAAWuD,OAAOI,gBAAe,QAAA,QAAA,UAAA;AAAA,YAAAR,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,UAAA,EAAA,uCACnC,OAAI,EAAA,WAAWC,OAAOoB,qBAAoB,QAAA,QAAA,UAAA;AAAA,YAAAxB,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,UAAA,EACxCJ,GAAAA,KAAK/G,SAASyH,IAAI,CAACC,KAAKxE,UACtB,sBAAA,cAAA,OAAA,EACC,KAAKwE,IAAI5J,IACT,WAAWsJ,OAAOO,gBAClB,OAAO;AAAA,YAAEC,gBAAgB,GAAG1E,QAAQ,GAAG;AAAA,UAAA,GAAM,QAAA,QAAA,UAAA;AAAA,YAAA8D,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,UAE5CO,EAAAA,GAAAA,IAAIvE,KACP,CACD,CACH,CACF;AAAA,QAGJ,KAAK;AACH,qDACG,OAAI,EAAA,WAAWiE,OAAOI,gBAAe,QAAA,QAAA,UAAA;AAAA,YAAAR,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,UAAA,EAAA,uCACnC,OAAI,EAAA,WAAWC,OAAOqB,sBAAqB,QAAA,QAAA,UAAA;AAAA,YAAAzB,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,UAAA,EAAA,uCACzC,QAAK,EAAA,WAAWC,OAAOsB,mBAAkB,QAAA,QAAA,UAAA;AAAA,YAAA1B,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,eAAC,KAAEJ,KAAK9C,eAAc,GAAC,CACnE,CACF;AAAA,QAGJ,KAAK;AACH,qDACG,OAAI,EAAA,WAAWmD,OAAOI,gBAAe,QAAA,QAAA,UAAA;AAAA,YAAAR,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,UAAA,EAAA,uCACnC,OAAI,EAAA,WAAWC,OAAOuB,iBAAgB,QAAA,QAAA,UAAA;AAAA,YAAA3B,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,UAAA,EAAA,uCACpC,QAAK,EAAA,WAAWC,OAAOwB,gBAAe,QAAA,QAAA,UAAA;AAAA,YAAA5B,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,UAAA,EAAA,GAAEJ,KAAK5C,YAAa,GAC3D,sBAAA,cAAC,QAAK,EAAA,WAAWiD,OAAOyB,eAAc,QAAA,QAAA,UAAA;AAAA,YAAA7B,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,UAAA,EAAA,GAAC,GAAC,GACvC,sBAAA,cAAA,QAAA,EAAK,WAAWC,OAAO0B,qBAAoB,QAAA,QAAA,UAAA;AAAA,YAAA9B,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,UAAA,EAAA,GAAC,GAAC,CAChD,CACF;AAAA,QAGJ,KAAK;AACH,qDACG,OAAI,EAAA,WAAWC,OAAOI,gBAAe,QAAA,QAAA,UAAA;AAAA,YAAAR,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,UAAA,EAAA,uCACnC,OAAI,EAAA,WAAWC,OAAO2B,mBAAkB,QAAA,QAAA,UAAA;AAAA,YAAA/B,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,UAAA,EAAA,uCACtC,QAAK,EAAA,WAAWC,OAAO4B,kBAAiB,QAAA,QAAA,UAAA;AAAA,YAAAhC,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,UAAA,EAAA,GAAEJ,KAAK1G,OAAQ,GACxD,sBAAA,cAAC,QAAK,EAAA,WAAW+G,OAAO6B,cAAa,QAAA,QAAA,UAAA;AAAA,YAAAjC,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,UAAA,EAAA,GAAC,IAAE,GACvC,sBAAA,cAAA,QAAA,EAAK,WAAWC,OAAO4B,kBAAiB,QAAA,QAAA,UAAA;AAAA,YAAAhC,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,UAAA,KAAEJ,KAAKzG,OAAQ,CAC1D,CACF;AAAA,QAGJ,KAAK;AACH,qDACG,OAAI,EAAA,WAAW8G,OAAOI,gBAAe,QAAA,QAAA,UAAA;AAAA,YAAAR,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,UAAA,EAAA,uCACnC,OAAI,EAAA,WAAWC,OAAO8B,gBAAe,QAAA,QAAA,UAAA;AAAA,YAAAlC,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,UAAA,KACnCgC,cAAcpC,KAAKxC,aAAawC,KAAKpJ,YAAY,CACpD,CACF;AAAA,QAGJ;AACE,qDACG,OAAI,EAAA,WAAWyJ,OAAOI,gBAAe,QAAA,QAAA,UAAA;AAAA,YAAAR,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,UAAA,EAAA,uCACnC,OAAI,EAAA,WAAWC,OAAOzJ,cAAa,QAAA,QAAA,UAAA;AAAA,YAAAqJ,UAAAC;AAAAA,YAAAC,YAAA;AAAA,YAAAC,cAAA;AAAA,UAAA,KACjCJ,KAAKpJ,YACR,CACF;AAAA,MAAA;AAAA,IAGR;AAEA,+CACG,OAAI,EAAA,WAAWyJ,OAAOC,cAAa,QAAA,QAAA,UAAA;AAAA,MAAAL,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCACjC,OAAI,EAAA,WAAWC,OAAOE,gBAAe,QAAA,QAAA,UAAA;AAAA,MAAAN,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCACnC,MAAG,EAAA,WAAWC,OAAOG,eAAc,QAAA,QAAA,UAAA;AAAA,MAAAP,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAEJ,GAAAA,KAAK5G,WAAY,CACzD,GAECoI,uBAAuB,GAGvB,sBAAA,cAAA,OAAA,EAAI,WAAWnB,OAAOS,eAAc,QAAA,QAAA,UAAA;AAAA,MAAAb,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,KAClCJ,KAAK7G,QAAQuH,IAAI,CAACK,QAAQ5E,8CACxB,UACC,EAAA,KAAKA,OACL,WAAWkE,OAAOW,cAClB,SAAS,MAAMtC,aAAaqC,MAAM,GAAE,QAAA,QAAA,UAAA;AAAA,MAAAd,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCAEnC,QAAK,EAAA,WAAWC,OAAOY,cAAa,QAAA,QAAA,UAAA;AAAA,MAAAhB,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,KAAEW,MAAO,CAChD,CACD,CACH,CACF;AAAA,EAEJ;AAGA,QAAMqB,gBAAgBtH,aAAAA,YAAY,CAAC0C,aAAa6E,WAAW;AACzD,YAAQ7E,aAAW;AAAA,MACjB,KAAK;AACH,mDACG,OAAI,EAAA,WAAW6C,OAAOiC,aAAY,QAAA,QAAA,UAAA;AAAA,UAAArC,UAAAC;AAAAA,UAAAC,YAAA;AAAA,UAAAC,cAAA;AAAA,QAAA,EAAA,GAChCpE,MAAMC,KAAK;AAAA,UAAEH,QAAQuG;AAAAA,QAAAA,GAAU,CAACnG,GAAGa,MACjC,sBAAA,cAAA,QAAA,EAAK,KAAKA,GAAG,WAAWsD,OAAOkC,KAAI,QAAA,QAAA,UAAA;AAAA,UAAAtC,UAAAC;AAAAA,UAAAC,YAAA;AAAA,UAAAC,cAAA;AAAA,QAAA,EAAA,GAAC,GAAC,CACvC,CACH;AAAA,MAGJ,KAAK;AACH,cAAMoC,eAAe;AAAA,UACnB,GAAG;AAAA,UAAK,GAAG;AAAA,UAAK,GAAG;AAAA,UAAK,GAAG;AAAA,UAAK,GAAG;AAAA,UAAK,GAAG;AAAA,QAC7C;AACA,mDACG,OAAI,EAAA,WAAWnC,OAAOoC,aAAY,QAAA,QAAA,UAAA;AAAA,UAAAxC,UAAAC;AAAAA,UAAAC,YAAA;AAAA,UAAAC,cAAA;AAAA,QAAA,EAAA,uCAChC,QAAK,EAAA,WAAWC,OAAOqC,WAAU,QAAA,QAAA,UAAA;AAAA,UAAAzC,UAAAC;AAAAA,UAAAC,YAAA;AAAA,UAAAC,cAAA;AAAA,QAAEoC,EAAAA,GAAAA,aAAaH,MAAM,KAAK,GAAI,CAClE;AAAA,MAGJ,KAAK;AACH,mDACG,OAAI,EAAA,WAAWhC,OAAOsC,gBAAe,QAAA,QAAA,UAAA;AAAA,UAAA1C,UAAAC;AAAAA,UAAAC,YAAA;AAAA,UAAAC,cAAA;AAAA,QAAA,EAAA,GACnCpE,MAAMC,KAAK;AAAA,UAAEH,QAAQuG;AAAAA,QAAAA,GAAU,CAACnG,GAAGa,MACjC,sBAAA,cAAA,QAAA,EAAK,KAAKA,GAAG,WAAWsD,OAAOuC,QAAO,QAAA,QAAA,UAAA;AAAA,UAAA3C,UAAAC;AAAAA,UAAAC,YAAA;AAAA,UAAAC,cAAA;AAAA,QAAA,EAAA,GAAC,IAAE,CAC3C,CACH;AAAA,MAGJ;AACE,eAAQ,sBAAA,cAAA,QAAA,EAAI,QAAA,QAAA,UAAA;AAAA,UAAAH,UAAAC;AAAAA,UAAAC,YAAA;AAAA,UAAAC,cAAA;AAAA,aAAEiC,MAAO;AAAA,IAAA;AAAA,EAE3B,GAAG,EAAE;AAGL,QAAMQ,2BAA2BA,MAAM;AAC/B7C,UAAAA,OAAO7H,UAAUY,aAAaW;AAGpC,QAAI,CAACsG,QAAQ,CAACA,KAAKrG,UAAU,CAACqG,KAAKpG,QAAQ;AACzC,YAAMkE,cAAcjD,qBAAqBhE,eAAeQ,oBAAoBN,IAAIoB,UAAUO,UAAU;AACpGN,mBAAa+F,CAAS,UAAA;AAAA,QACpB,GAAGA;AAAAA,QACHpF,cAAc;AAAA,UACZ,GAAGoF,KAAKpF;AAAAA,UACR,CAAClC,eAAeQ,oBAAoBN,EAAE,GAAG+G;AAAAA,QAAAA;AAAAA,MAC3C,EACA;AACF,aAAQ,sBAAA,cAAA,OAAA,EAAG,QAAA,QAAA,UAAA;AAAA,QAAAmC,UAAAC;AAAAA,QAAAC,YAAA;AAAA,QAAAC,cAAA;AAAA,WAAC,sBAAoB;AAAA,IAAA;AAIlC,UAAM0C,yBAA0BnE,CAAW,WAAA;AACzC,UAAIE,YAAY;AAEhB,cAAQmB,KAAKlD,eAAa;AAAA,QACxB,KAAK;AAAA,QACL,KAAK;AACH+B,sBAAYmB,KAAKxG,kBAAkBmF;AACnC;AAAA,QACF,KAAK;AACHE,sBAAYmB,KAAKxG,kBAAkBmF;AACnC;AAAA,QACF,KAAK;AACHE,sBAAYmB,KAAKxG,kBAAkBmF;AACnC;AAAA,QACF;AACEE,sBAAYmB,KAAKxG,kBAAkBmF;AAAAA,MAAAA;AAG1BE,mBAAAA,YAAYmB,KAAKxG,gBAAgB,OAAO;AAAA,IACvD;AAEA,+CACG,OAAI,EAAA,WAAW6G,OAAOC,cAAa,QAAA,QAAA,UAAA;AAAA,MAAAL,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCACjC,OAAI,EAAA,WAAWC,OAAOE,gBAAe,QAAA,QAAA,UAAA;AAAA,MAAAN,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCACnC,MAAG,EAAA,WAAWC,OAAOG,eAAc,QAAA,QAAA,UAAA;AAAA,MAAAP,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAEJ,EAAAA,GAAAA,KAAK5G,WAAY,CACzD,GAGA,sBAAA,cAAC,OAAI,EAAA,WAAWiH,OAAOI,gBAAe,QAAA,QAAA,UAAA;AAAA,MAAAR,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCACnC,OAAI,EAAA,WAAWC,OAAO0C,kBAAiB,QAAA,QAAA,UAAA;AAAA,MAAA9C,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EACtC,GAAA,sBAAA,cAAC,UACC,EAAA,WAAWC,OAAO2C,iBAClB,SAAS,MAAMF,uBAAuB,MAAM,GAAE,QAAA,QAAA,UAAA;AAAA,MAAA7C,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCAE7C,OAAI,EAAA,WAAWC,OAAO4C,YAAW,QAAA,QAAA,UAAA;AAAA,MAAAhD,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GAAC,SAAO,GACzC,sBAAA,cAAA,OAAA,EAAI,WAAWC,OAAO6C,cAAa,QAAA,QAAA,UAAA;AAAA,MAAAjD,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GACjCJ,KAAKrG,OAAOV,QAAQyH,IAAI,CAACC,KAAKxE,UAC7B,sBAAA,cAAC,QACC,EAAA,KAAK,UAAUwE,IAAI5J,EAAE,IAAIoF,KAAK,IAC9B,WAAWkE,OAAO8C,aAAY,QAAA,QAAA,UAAA;AAAA,MAAAlD,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,KAE7BO,IAAIvE,KACP,CACD,CACH,CACF,GAEC,sBAAA,cAAA,UAAA,EACC,WAAWiE,OAAO2C,iBAClB,SAAS,MAAMF,uBAAuB,OAAO,GAAE,QAAA,QAAA,UAAA;AAAA,MAAA7C,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,uCAE9C,OAAI,EAAA,WAAWC,OAAO4C,YAAW,QAAA,QAAA,UAAA;AAAA,MAAAhD,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GAAC,SAAO,GACzC,sBAAA,cAAA,OAAA,EAAI,WAAWC,OAAO6C,cAAa,QAAA,QAAA,UAAA;AAAA,MAAAjD,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GACjCJ,KAAKpG,OAAOX,QAAQyH,IAAI,CAACC,KAAKxE,UAC7B,sBAAA,cAAC,QACC,EAAA,KAAK,UAAUwE,IAAI5J,EAAE,IAAIoF,KAAK,IAC9B,WAAWkE,OAAO8C,aAAY,QAAA,QAAA,UAAA;AAAA,MAAAlD,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,KAE7BO,IAAIvE,KACP,CACD,CACH,CACF,CACF,GAGC4D,KAAKlD,kBAAkB,qDACrB,OAAI,EAAA,WAAWuD,OAAO+C,iBAAgB,QAAA,QAAA,UAAA;AAAA,MAAAnD,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EACrC,GAAA,sBAAA,cAAC,UACC,EAAA,WAAWC,OAAOW,cAClB,SAAS,MAAM8B,uBAAuB,OAAO,GAAE,QAAA,QAAA,UAAA;AAAA,MAAA7C,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GAAA,mBAGjD,GACA,sBAAA,cAAC,YACC,WAAWC,OAAOW,cAClB,SAAS,MAAM8B,uBAAuB,WAAW,GAAE,QAAA,QAAA,UAAA;AAAA,MAAA7C,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,EAAA,GAAA,uBAGrD,CACF,GAIDJ,KAAKlD,kBAAkB,sBACrB,sBAAA,cAAA,OAAA,EAAI,WAAWuD,OAAOgD,YAAW,QAAA,QAAA,UAAA;AAAA,MAAApD,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAC/B,EAAA,GAAA,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAEM,IAAI4C,CAAAA,QACxB,sBAAA,cAAA,UAAA,EACC,KAAKA,KACL,WAAWjD,OAAOW,cAClB,SAAS,MAAM8B,uBAAuBQ,IAAInG,SAAU,CAAA,GAAE,QAAA,QAAA,UAAA;AAAA,MAAA8C,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,KAErDkD,GACH,CACD,CACH,CAEJ,CACF;AAAA,EAEJ;AAGA,QAAMC,wBAAwBA,MAAM;AAClC,YAAQpL,UAAUU,iBAAe;AAAA,MAC/B,KAAKhC,eAAeC,gBAAgBC;AAClC,eAAOgJ,qBAAqB;AAAA,MAC9B,KAAKlJ,eAAeM,gBAAgBJ;AAClC,eAAOmK,qBAAqB;AAAA,MAC9B,KAAKrK,eAAeO,mBAAmBL;AACrC,eAAOwK,wBAAwB;AAAA,MACjC,KAAK1K,eAAeQ,oBAAoBN;AACtC,eAAO8L,yBAAyB;AAAA,MAClC;AACE,eAAO9C,qBAAqB;AAAA,IAAA;AAAA,EAElC;AAGA,MAAIlG,iBAAiB;AACnB,WACG,sBAAA,cAAA,iBAAA,EACC,WAAU,uBACV,iBAAgB,sDAChB,UAAS,MACT,SAAS+D,WACT,QACA,cAAc3C,sBAAsBuI,aAAa9C,IAAI+C,CAAS,UAAA;AAAA,MAC5D1M,IAAI0M,KAAK1M;AAAAA,MACTC,MAAMyM,KAAKzM;AAAAA,MACXC,aAAawM,KAAKxM;AAAAA,MAClBC,MAAMuM,KAAK1M,OAAO,SAAS,OAAO0M,KAAK1M,OAAO,WAAW,OAAO;AAAA,IAChE,EAAA,GAAE,QAAA,QAAA,UAAA;AAAA,MAAAkJ,UAAAC;AAAAA,MAAAC,YAAA;AAAA,MAAAC,cAAA;AAAA,IAAA,GACJ;AAAA,EAAA;AAKN,6CACG,OAAI,EAAA,WAAWC,OAAOqD,qBAAoB,QAAA,QAAA,UAAA;AAAA,IAAAzD,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACxC,OAAI,EAAA,WAAWC,OAAOsD,aAAY,QAAA,QAAA,UAAA;AAAA,IAAA1D,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAEhC,OAAI,EAAA,WAAWC,OAAOuD,YAAW,QAAA,QAAA,UAAA;AAAA,IAAA3D,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/B,MAAG,EAAA,WAAWC,OAAOwD,WAAU,QAAA,QAAA,UAAA;AAAA,IAAA5D,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAA,6BAE7B,sBAAA,cAAA,OAAA,EAAI,OAAO;AAAA,IAAE0D,UAAU;AAAA,IAAUC,SAAS;AAAA,IAAKC,WAAW;AAAA,EAAA,GAAY,QAAA,QAAA,UAAA;AAAA,IAAA/D,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GACpE6D,OAAOC,OAAOrN,cAAc,EAAEsN,KAAKC,CAAAA,SAAQA,KAAKrN,OAAOoB,UAAUU,eAAe,GAAG7B,QAAQ,WAC9F,CACF,GACC,sBAAA,cAAA,mBAAA,EACC,WACA,WACA,MAAK,UACL,UAAS,UAAQ,QAAA,QAAA,UAAA;AAAA,IAAAiJ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,CAAA,CAErB,GAGC,sBAAA,cAAA,OAAA,EAAI,WAAWC,OAAOgE,WAAU,QAAA,QAAA,UAAA;AAAA,IAAApE,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC9B,OAAI,EAAA,WAAWC,OAAOiE,UAAS,QAAA,QAAA,UAAA;AAAA,IAAArE,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC7B,OAAI,EAAA,WAAWC,OAAOkE,WAAU,QAAA,QAAA,UAAA;AAAA,IAAAtE,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAEjI,UAAUI,KAAM,GACnD,sBAAA,cAAC,OAAI,EAAA,WAAW8H,OAAOmE,WAAU,QAAA,QAAA,UAAA;AAAA,IAAAvE,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,QAAM,CAC1C,GACA,sBAAA,cAAC,OAAI,EAAA,WAAWC,OAAOiE,UAAS,QAAA,QAAA,UAAA;AAAA,IAAArE,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC7B,OAAI,EAAA,WAAWC,OAAOkE,WAAU,QAAA,QAAA,UAAA;AAAA,IAAAtE,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAEjI,UAAUK,KAAM,GACnD,sBAAA,cAAC,OAAI,EAAA,WAAW6H,OAAOmE,WAAU,QAAA,QAAA,UAAA;AAAA,IAAAvE,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAC,QAAM,CAC1C,GACA,sBAAA,cAAC,OAAI,EAAA,WAAWC,OAAOiE,UAAS,QAAA,QAAA,UAAA;AAAA,IAAArE,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC7B,OAAI,EAAA,WAAWC,OAAOkE,WAAU,QAAA,QAAA,UAAA;AAAA,IAAAtE,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAEjI,GAAAA,UAAUQ,YAAY,KAAI,GAAC,GAC7D,sBAAA,cAAA,OAAA,EAAI,WAAW0H,OAAOmE,WAAU,QAAA,QAAA,UAAA;AAAA,IAAAvE,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAC,EAAA,GAAA,UAAQ,CAC5C,CACF,GAGA,sBAAA,cAAC,OAAI,EAAA,WAAWC,OAAOoE,cAAa,QAAA,QAAA,UAAA;AAAA,IAAAxE,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KACjC6D,OAAOC,OAAOrN,cAAc,EAAE6J,IAAKpB,CAClC,aAAA,sBAAA,cAAC,UACC,EAAA,KAAKA,SAASvI,IACd,WAAW,GAAGsJ,OAAOqE,cAAc,IACjCvM,UAAUU,oBAAoByG,SAASvI,KAAKsJ,OAAOsE,SAAS,EAAE,IAEhE,SAAS,MAAM7E,eAAeR,SAASvI,EAAE,GACzC,OAAOuI,SAASrI,aAAY,QAAA,QAAA,UAAA;AAAA,IAAAgJ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAE5B,GAAA,sBAAA,cAAC,QAAI,EAAA,QAAA,QAAA,UAAA;AAAA,IAAAH,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,KAAEd,SAASpI,IAAK,uCACpB,QAAI,EAAA,QAAA,QAAA,UAAA;AAAA,IAAA+I,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAEd,GAAAA,SAAStI,IAAK,CACvB,CACD,CACH,GAGC,sBAAA,cAAA,OAAA,EAAI,WAAWqJ,OAAOuE,UAAS,QAAA,QAAA,UAAA;AAAA,IAAA3E,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAC7BmD,sBACH,CAAA,GAGA,sBAAA,cAAC,OAAI,EAAA,WAAWlD,OAAOwE,cAAa,QAAA,QAAA,UAAA;AAAA,IAAA5E,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAClC,GAAA,sBAAA,cAAC,UAAO,EAAA,WAAWC,OAAOyE,eAAe,SAAS,MAAM/M,sBAAsB,uBAAuB,gFAAgF,GAAE,QAAA,QAAA,UAAA;AAAA,IAAAkI,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAA,aAEvL,GACA,sBAAA,cAAC,YAAO,WAAWC,OAAOyE,eAAe,SAAS,MAAMhL,mBAAmB,IAAI,GAAE,QAAA,QAAA,UAAA;AAAA,IAAAmG,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAA,cAEjF,GACA,sBAAA,cAAC,YAAO,WAAWC,OAAOyE,eAAe,SAAS,MAAM;AAAQC,UAAAA,aAAaC,WAAWC,YAAY;AAAGrK,eAAWrD,QAAQ,GAAI;AAAA,EAAA,GAAK,QAAA,QAAA,UAAA;AAAA,IAAA0I,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,GAAA,WAEnI,CACF,CACF,GAGCnG,YACC,sBAAA,cAAC,OAAI,EAAA,WAAW,GAAGoG,OAAO6E,eAAe,IAAIjL,SAAS4E,YAAYwB,OAAO8E,UAAU9E,OAAO3J,KAAK,IAAG,QAAA,QAAA,UAAA;AAAA,IAAAuJ,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCAC/F,OAAI,EAAA,WAAWC,OAAO+E,iBAAgB,QAAA,QAAA,UAAA;AAAA,IAAAnF,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAA,EAAA,uCACpC,OAAI,EAAA,WAAWC,OAAOgF,iBAAgB,QAAA,QAAA,UAAA;AAAA,IAAApF,UAAAC;AAAAA,IAAAC,YAAA;AAAA,IAAAC,cAAA;AAAA,EAAEnG,EAAAA,GAAAA,SAASmF,OAAQ,CAC5D,CACF,CAEJ;AAEJ;;;;;"}