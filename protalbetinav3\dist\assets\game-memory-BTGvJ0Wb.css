/**
 * @file StandardTTS.module.css
 * @description Estilos padronizados para sistema TTS do Portal Betina V3
 * @version 1.0.0
 */

/* ===== BOTÃO TTS BASE ===== */
._ttsButton_12keu_8 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 2px solid transparent;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  background: transparent;
  user-select: none;
}

._ttsButton_12keu_8:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

._ttsButton_12keu_8:active {
  transform: scale(0.95);
}

/* ===== ESTADOS DO TTS ===== */
._ttsActive_12keu_35 {
  background: rgba(34, 197, 94, 0.15);
  border-color: rgba(34, 197, 94, 0.3);
  color: #16a34a;
}

._ttsActive_12keu_35:hover {
  background: rgba(34, 197, 94, 0.25);
  border-color: rgba(34, 197, 94, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

._ttsInactive_12keu_48 {
  background: rgba(239, 68, 68, 0.15);
  border-color: rgba(239, 68, 68, 0.3);
  color: #dc2626;
}

._ttsInactive_12keu_48:hover {
  background: rgba(239, 68, 68, 0.25);
  border-color: rgba(239, 68, 68, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

/* ===== TAMANHOS DO BOTÃO ===== */
._ttsSmall_12keu_62 {
  padding: 0.5rem 0.75rem;
  font-size: 0.8rem;
  border-radius: 20px;
}

._ttsNormal_12keu_68 {
  padding: 0.75rem 1rem;
  font-size: 0.9rem;
  border-radius: 25px;
}

._ttsLarge_12keu_74 {
  padding: 1rem 1.5rem;
  font-size: 1rem;
  border-radius: 30px;
}

/* ===== POSIÇÕES DO BOTÃO ===== */
._ttsHeader_12keu_81 {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 100;
}

._ttsControls_12keu_88 {
  position: relative;
  z-index: 10;
}

._ttsFloating_12keu_93 {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* ===== ELEMENTOS DO BOTÃO ===== */
._ttsIcon_12keu_102 {
  font-size: 1.2em;
  line-height: 1;
  display: inline-block;
  min-width: 1.2em;
  text-align: center;
  vertical-align: middle;
}

._ttsLabel_12keu_111 {
  font-size: 0.85em;
  font-weight: 500;
  white-space: nowrap;
  margin-left: 0.5rem;
}

/* ===== CONTROLES TTS EXPANDIDOS ===== */
._ttsControls_12keu_88 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

._ttsActionButton_12keu_131 {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.5rem 0.75rem;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 20px;
  color: #2563eb;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

._ttsActionButton_12keu_131:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

._ttsActionButton_12keu_131:active {
  transform: translateY(0);
}

/* ===== ANIMAÇÕES ===== */
@keyframes _ttsActivate_12keu_1 {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

@keyframes _ttsDeactivate_12keu_1 {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

._ttsActive_12keu_35:focus {
  animation: _ttsActivate_12keu_1 0.6s ease-out;
}

._ttsInactive_12keu_48:focus {
  animation: _ttsDeactivate_12keu_1 0.6s ease-out;
}

/* ===== RESPONSIVIDADE ===== */
@media (max-width: 768px) {
  ._ttsButton_12keu_8 {
    padding: 0.6rem 0.8rem;
    font-size: 0.8rem;
    gap: 0.4rem;
  }
  
  ._ttsLabel_12keu_111 {
    display: none;
  }
  
  ._ttsIcon_12keu_102 {
    font-size: 1.4em;
  }
  
  ._ttsControls_12keu_88 {
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0.75rem;
  }
  
  ._ttsActionButton_12keu_131 {
    padding: 0.4rem 0.6rem;
    font-size: 0.75rem;
  }
  
  ._ttsFloating_12keu_93 {
    bottom: 1rem;
    right: 1rem;
  }
}

@media (max-width: 480px) {
  ._ttsButton_12keu_8 {
    padding: 0.5rem;
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
    justify-content: center;
  }
  
  ._ttsControls_12keu_88 {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  ._ttsActionButton_12keu_131 {
    justify-content: center;
    padding: 0.6rem;
  }
}

/* ===== ACESSIBILIDADE ===== */
@media (prefers-reduced-motion: reduce) {
  ._ttsButton_12keu_8,
  ._ttsActionButton_12keu_131 {
    transition: none;
    animation: none;
  }
  
  ._ttsButton_12keu_8:hover,
  ._ttsActionButton_12keu_131:hover {
    transform: none;
  }
  
  ._ttsActive_12keu_35:focus,
  ._ttsInactive_12keu_48:focus {
    animation: none;
  }
}

@media (prefers-contrast: high) {
  ._ttsButton_12keu_8 {
    border-width: 3px;
  }
  
  ._ttsActive_12keu_35 {
    background: #dcfce7;
    border-color: #16a34a;
    color: #14532d;
  }
  
  ._ttsInactive_12keu_48 {
    background: #fef2f2;
    border-color: #dc2626;
    color: #7f1d1d;
  }
  
  ._ttsControls_12keu_88 {
    background: white;
    border: 2px solid #e5e7eb;
  }
}

/* ===== TEMA ESCURO ===== */
@media (prefers-color-scheme: dark) {
  ._ttsControls_12keu_88 {
    background: rgba(31, 41, 55, 0.95);
    border-color: rgba(75, 85, 99, 0.3);
  }
  
  ._ttsActionButton_12keu_131 {
    background: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.3);
    color: #93c5fd;
  }
  
  ._ttsActionButton_12keu_131:hover {
    background: rgba(59, 130, 246, 0.3);
    border-color: rgba(59, 130, 246, 0.5);
  }
}

/* ===== ESTADOS DE FOCO PARA ACESSIBILIDADE ===== */
._ttsButton_12keu_8:focus-visible {
  outline: 3px solid #3b82f6;
  outline-offset: 2px;
}

._ttsActionButton_12keu_131:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 1px;
}

/* ===== INDICADOR DE ATIVIDADE ===== */
._ttsActive_12keu_35::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: #16a34a;
  border-radius: 50%;
  border: 2px solid white;
  animation: _pulse_12keu_1 2s infinite;
}

@keyframes _pulse_12keu_1 {
  0% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(34, 197, 94, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}
/**
 * @file MemoryGame.module.css
 * @description Estilos modulares para o Jogo da Memória - Layout padronizado com base em ContagemNumeros
 * @version 3.5.0
 */

/* Variáveis CSS para consistência e reutilização */
:root {
  --card-background: rgba(255, 255, 255, 0.1);
  --card-border: 1px solid rgba(255, 255, 255, 0.2);
  --card-blur: blur(10px);
  --success-bg: rgba(76, 175, 80, 0.3);
  --success-border: rgba(76, 175, 80, 0.5);
  --error-bg: rgba(244, 67, 54, 0.3);
  --error-border: rgba(244, 67, 54, 0.5);
  --primary-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Animações */
@keyframes _buttonPressEffect_hb9b3_1 {
  0% { transform: scale(1); }
  50% { transform: scale(1.15); box-shadow: 0 10px 30px rgba(255, 255, 255, 0.4); }
  100% { transform: scale(1); }
}

@keyframes _correctFeedback_hb9b3_2927 {
  0% { box-shadow: 0 0 0 rgba(76, 175, 80, 0.4); }
  50% { box-shadow: 0 0 30px rgba(76, 175, 80, 0.8); }
  100% { box-shadow: 0 0 0 rgba(76, 175, 80, 0.4); }
}

@keyframes _incorrectFeedback_hb9b3_2935 {
  0% { box-shadow: 0 0 0 rgba(244, 67, 54, 0.4); }
  25% { transform: translateX(-5px); }
  50% { transform: translateX(5px); box-shadow: 0 0 20px rgba(244, 67, 54, 0.8); }
  75% { transform: translateX(-5px); }
  100% { transform: translateX(0); box-shadow: 0 0 0 rgba(244, 67, 54, 0.4); }
}

/* Container principal */
._memoryGame_hb9b3_83 {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Conteúdo do jogo */
._gameContent_hb9b3_105 {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Header do jogo */
._gameHeader_hb9b3_125 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 3rem 1rem 1rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  position: relative;
  min-height: 70px;
}

._gameTitle_hb9b3_153 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

._activitySubtitle_hb9b3_177 {
  font-size: 0.7rem;
  opacity: 0.8;
  margin-top: 0.25rem;
  background: var(--card-background);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: var(--card-border);
}

/* Botão TTS no header */
._headerTtsButton_hb9b3_199 {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: white;
  z-index: 10;
}

._headerTtsButton_hb9b3_199:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

._headerTtsButton_hb9b3_199:active {
  transform: scale(0.95);
}

._ttsActive_hb9b3_255 {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
}

._ttsInactive_hb9b3_265 {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
}

/* Menu de atividades - PADRÃO CONTAGEM NÚMEROS */
._activityMenu_hb9b3_277 {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  justify-content: center;
  flex-wrap: wrap;
}

._activityButton_hb9b3_293 {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

._activityButton_hb9b3_293:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

._activityButton_hb9b3_293._active_hb9b3_331 {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.6);
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

._activitySelector_hb9b3_343 {
  margin-bottom: 2rem;
}

._selectorTitle_hb9b3_351 {
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-align: left;
}

._activityButtons_hb9b3_367 {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  justify-content: center;
}

/* Opções após completar atividade - NOVO SISTEMA */
._activityCompleteOptions_hb9b3_383 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: var(--card-blur);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

._activityCompleteCard_hb9b3_411 {
  background: var(--gradient-bg);
  border-radius: 20px;
  padding: 3rem;
  text-align: center;
  color: white;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

._activityCompleteTitle_hb9b3_435 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #4CAF50;
}

._activityCompleteTitle_hb9b3_435::before {
  content: '🎉';
  font-size: 2.5rem;
  display: block;
  margin-bottom: 0.5rem;
}

._activityCompleteMessage_hb9b3_463 {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.9);
}

._activityChoiceButtons_hb9b3_477 {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

._choiceButton_hb9b3_491 {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

._choiceButton_hb9b3_491:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

._choiceButton_hb9b3_491._continue_hb9b3_535 {
  background: rgba(76, 175, 80, 0.2);
  border-color: rgba(76, 175, 80, 0.5);
}

._choiceButton_hb9b3_491._continue_hb9b3_535:hover {
  background: rgba(76, 175, 80, 0.3);
}

._choiceButton_hb9b3_491._newActivity_hb9b3_553 {
  background: rgba(33, 150, 243, 0.2);
  border-color: rgba(33, 150, 243, 0.5);
}

._choiceButton_hb9b3_491._newActivity_hb9b3_553:hover {
  background: rgba(33, 150, 243, 0.3);
}

/* Elementos de atividade - SIMPLIFICADOS */
._activityIcon_hb9b3_573 {
  font-size: 1rem;
  margin-right: 0.3rem;
}

._activityName_hb9b3_583 {
  flex: 1;
  text-align: left;
  font-size: 0.8rem;
}

._activeIndicator_hb9b3_595 {
  color: #00ff00;
  font-size: 0.7rem;
  margin-left: auto;
  animation: _pulse_hb9b3_1 1.5s ease-in-out infinite;
}

@keyframes _pulse_hb9b3_1 {
  0%, 100% { opacity: 0.7; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.05); }
}

/* Atividade de Som */
._soundActivity_hb9b3_621 {
  text-align: center;
}

._soundIndicator_hb9b3_629 {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: _soundPulse_hb9b3_1 2s ease-in-out infinite;
}

@keyframes _soundPulse_hb9b3_1 {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.1); opacity: 1; }
}

._soundButton_hb9b3_651 {
  background: rgba(74, 144, 226, 0.3);
  border: 2px solid rgba(74, 144, 226, 0.5);
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  margin: 1rem;
}

._soundButton_hb9b3_651:hover {
  background: rgba(74, 144, 226, 0.5);
  transform: scale(1.05);
}

/* Atividade de Estimativa */
._estimationDisplay_hb9b3_689 {
  position: relative;
}

._estimationObjects_hb9b3_697 {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  opacity: 0.9;
}

._estimationObject_hb9b3_697 {
  font-size: 1.5rem;
  transform: rotate(var(--rotation));
  transition: opacity 0.3s ease;
}

._estimationTip_hb9b3_725 {
  text-align: center;
  margin-top: 1rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  background: rgba(255, 193, 7, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border-left: 4px solid #FFC107;
}

/* Atividade de Sequência */
._sequenceDisplay_hb9b3_749 {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-wrap: wrap;
}

._sequenceNumber_hb9b3_767 {
  background: var(--card-background);
  padding: 1rem;
  border-radius: 12px;
  min-width: 60px;
  text-align: center;
  color: white;
  font-weight: bold;
  transition: all 0.3s ease;
}

._sequenceNumber_hb9b3_767:hover {
  transform: scale(1.05);
}

._sequenceArrow_hb9b3_797 {
  color: rgba(255, 255, 255, 0.7);
}

/* Estatísticas */
._gameStats_hb9b3_807 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

._statCard_hb9b3_821 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

._statCard_hb9b3_821::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #E91E63);
  opacity: 0.7;
}

._statValue_hb9b3_865 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: white;
}

._statLabel_hb9b3_879 {
  font-size: 0.8rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.8);
}

/* Área da pergunta - MELHORADA PARA COMBINAÇÃO DE PARES */
._questionArea_hb9b3_893 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

._questionHeader_hb9b3_921 {
  text-align: center;
  margin-bottom: 1.5rem;
  width: 100%;
}

._questionTitle_hb9b3_933 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: white;
}

._instructions_hb9b3_947 {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 0.5rem;
}

/* Instruções específicas para Combinação de Pares - SEGUINDO PADRÃO CONTAGEM NÚMEROS */
._pairMatchingInstructions_hb9b3_961 {
  background: rgba(33, 150, 243, 0.15);
  border: 2px solid rgba(33, 150, 243, 0.4);
  border-radius: 16px;
  padding: 1.5rem;
  color: white;
  font-size: 1.1rem;
  line-height: 1.6;
  text-align: center;
  box-shadow: 0 8px 32px rgba(33, 150, 243, 0.2);
  position: relative;
  overflow: hidden;
  margin-bottom: 2rem;
  max-width: 600px;
}

._pairMatchingInstructions_hb9b3_961::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: _instructionShine_hb9b3_1 3s infinite;
}

._pairMatchingInstructions_hb9b3_961 strong {
  color: #64b5f6;
  font-weight: bold;
}

@keyframes _instructionShine_hb9b3_1 {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Indicador de progresso da atividade - SEGUINDO PADRÃO CONTAGEM NÚMEROS */
._pairMatchingProgress_hb9b3_1055 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin: 2rem 0;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.15) 0%, rgba(139, 195, 74, 0.15) 100%);
  border: 2px solid rgba(76, 175, 80, 0.4);
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(76, 175, 80, 0.2);
}

._progressInfo_hb9b3_1083 {
  font-size: 1.2rem;
  font-weight: 600;
  text-align: center;
}

._progressBar_hb9b3_1095 {
  width: 100%;
  max-width: 300px;
  height: 12px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
  margin: 0.5rem 0;
}

._progressFill_hb9b3_1115 {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  border-radius: 10px;
  transition: width 0.5s ease-out;
  position: relative;
}

._progressFill_hb9b3_1115::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: _progressGlow_hb9b3_1 2s ease-in-out infinite;
}

@keyframes _progressGlow_hb9b3_1 {
  0%, 100% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
}

._progressCounter_hb9b3_1181 {
  font-weight: bold;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 20px;
  padding: 0.3rem 1rem;
  font-size: 1rem;
}

._repeatButton_hb9b3_1199 {
  background: rgba(74, 144, 226, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

._repeatButton_hb9b3_1199:hover {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.4);
}

._repeatButton_hb9b3_1199:active {
  transform: scale(0.95);
}

._ttsIndicator_hb9b3_1249 {
  background: rgba(74, 144, 226, 0.8);
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
  margin-top: 0.5rem;
  transition: all 0.3s ease;
  border: 2px solid rgba(74, 144, 226, 1);
}

._answerButton_hb9b3_1281:hover ._ttsIndicator_hb9b3_1249 {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.1);
}

/* ==================== ATIVIDADE: 🧩 COMBINAÇÃO DE PARES ==================== */

/* Área da atividade de Combinação de Pares - SEGUINDO PADRÃO CONTAGEM NÚMEROS */
._pairMatchingActivity_hb9b3_1297 {
  flex: 1;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 500px;
  color: white;
}

._pairMatchingActivity_hb9b3_1297 h3 {
  color: white;
  font-size: 2rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
  text-align: center;
}

._pairMatchingTip_hb9b3_1347 {
  color: rgba(255, 255, 255, 0.95);
  font-size: 1.4rem;
  margin-bottom: 2rem;
  line-height: 1.5;
  font-weight: 500;
  max-width: 800px;
  text-align: center;
  background: rgba(255, 193, 7, 0.1);
  padding: 1rem 2rem;
  border-radius: 12px;
  border-left: 4px solid #FFC107;
}

/* Grid de memória - SISTEMA RESPONSIVO MELHORADO */
._memoryGrid_hb9b3_1377 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  justify-content: center;
  margin: 2rem auto;
  max-width: 800px;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  backdrop-filter: var(--card-blur);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Variações do grid baseadas no número de cartas - OTIMIZADAS */
._memoryGrid_hb9b3_1377._grid2x2_hb9b3_1409 {
  grid-template-columns: repeat(2, 1fr);
  max-width: 400px;
  gap: 2rem;
}

._memoryGrid_hb9b3_1377._grid2x2_hb9b3_1409 ._memoryCard_hb9b3_1421,
._memoryGrid_hb9b3_1377._grid2x2_hb9b3_1409 ._card_hb9b3_1423 {
  min-height: 160px;
  min-width: 160px;
}

._memoryGrid_hb9b3_1377._grid3x3_hb9b3_1433 {
  grid-template-columns: repeat(3, 1fr);
  max-width: 550px;
  gap: 1.8rem;
}

._memoryGrid_hb9b3_1377._grid3x3_hb9b3_1433 ._memoryCard_hb9b3_1421,
._memoryGrid_hb9b3_1377._grid3x3_hb9b3_1433 ._card_hb9b3_1423 {
  min-height: 140px;
  min-width: 140px;
}

._memoryGrid_hb9b3_1377._grid4x4_hb9b3_1457 {
  grid-template-columns: repeat(4, 1fr);
  max-width: 700px;
  gap: 1.5rem;
}

._memoryGrid_hb9b3_1377._grid4x4_hb9b3_1457 ._memoryCard_hb9b3_1421,
._memoryGrid_hb9b3_1377._grid4x4_hb9b3_1457 ._card_hb9b3_1423 {
  min-height: 130px;
  min-width: 130px;
}

._memoryGrid_hb9b3_1377._grid5x5_hb9b3_1481 {
  grid-template-columns: repeat(5, 1fr);
  max-width: 850px;
  gap: 1.2rem;
}

._memoryGrid_hb9b3_1377._grid5x5_hb9b3_1481 ._memoryCard_hb9b3_1421,
._memoryGrid_hb9b3_1377._grid5x5_hb9b3_1481 ._card_hb9b3_1423 {
  min-height: 110px;
  min-width: 110px;
}

._memoryCard_hb9b3_1421, 
._card_hb9b3_1423 {
  aspect-ratio: 1;
  border-radius: 12px;
  cursor: pointer;
  position: relative;
  perspective: 1000px;
  transition: transform 0.3s ease;
  min-height: 120px;
  min-width: 120px;
  user-select: none;
}

._memoryCard_hb9b3_1421:hover:not(._matched_hb9b3_1531):not(._cardDisabled_hb9b3_1531),
._card_hb9b3_1423:hover:not(._matched_hb9b3_1531):not(._cardDisabled_hb9b3_1531) {
  transform: scale(1.05);
}

._memoryCard_hb9b3_1421._cardDisabled_hb9b3_1531,
._card_hb9b3_1423._cardDisabled_hb9b3_1531 {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Container interno da carta - SIMPLES */
._cardInner_hb9b3_1555 {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

/* Quando a carta está virada */
._memoryCard_hb9b3_1421._flipped_hb9b3_1575 ._cardInner_hb9b3_1555,
._card_hb9b3_1423._flipped_hb9b3_1575 ._cardInner_hb9b3_1555 {
  transform: rotateY(180deg);
}

/* Quando carta está matched */
._memoryCard_hb9b3_1421._matched_hb9b3_1531 ._cardInner_hb9b3_1555,
._card_hb9b3_1423._matched_hb9b3_1531 ._cardInner_hb9b3_1555 {
  transform: rotateY(180deg);
}

/* Efeito de pressão no click */
._memoryCard_hb9b3_1421:active ._cardInner_hb9b3_1555,
._card_hb9b3_1423:active ._cardInner_hb9b3_1555 {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

/* Efeito de foco para acessibilidade */
._memoryCard_hb9b3_1421:focus,
._card_hb9b3_1423:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.5);
  border-radius: 16px;
}

/* Face traseira da carta (verso) - EMOJI VISÍVEL - CORRIGIDA */
._cardBack_hb9b3_1629 {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 3px solid rgba(255, 255, 255, 0.5);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
  color: white;
  backdrop-filter: blur(10px);
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.3),
    inset 0 2px 10px rgba(255, 255, 255, 0.2);
  transform: rotateY(180deg);
  -webkit-transform: rotateY(180deg);
  -moz-transform: rotateY(180deg);
  -ms-transform: rotateY(180deg);
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  z-index: 2;
}

/* Efeito brilho no conteúdo da carta - SIMPLIFICADO */
._cardBack_hb9b3_1629::after {
  content: '';
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  height: 40%;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.15),
    transparent
  );
  border-radius: 12px 12px 0 0;
  pointer-events: none;
}

/* Face frontal da carta (interrogação) - VISÍVEL INICIALMENTE - CORRIGIDA */
._cardFront_hb9b3_1723 {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(240, 248, 255, 0.9));
  border: 3px solid rgba(255, 255, 255, 0.6);
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transform: rotateY(0deg);
  -webkit-transform: rotateY(0deg);
  -moz-transform: rotateY(0deg);
  -ms-transform: rotateY(0deg);
  color: #2c3e50;
  box-shadow: 
    0 8px 20px rgba(0, 0, 0, 0.15),
    inset 0 2px 10px rgba(255, 255, 255, 0.3);
  font-size: 3.5rem;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  z-index: 1;
}

/* Efeito de interrogação - REMOVIDO SHIMMER PARA CORRIGIR FLIP */

/* Ícone da carta - MELHORADO */
._cardIcon_hb9b3_1789 {
  font-size: 3rem;
  margin-bottom: 0.5rem;
  display: block;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Nome da carta - MELHORADO */
._cardName_hb9b3_1807 {
  font-size: 0.9rem;
  font-weight: 700;
  text-align: center;
  line-height: 1.2;
  color: #2c3e50;
  text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
  letter-spacing: 0.5px;
}

/* Ícone do verso da carta */
._cardBackIcon_hb9b3_1829 {
  font-size: 2.5rem;
  font-weight: bold;
}

/* Cartas combinadas - FEEDBACK MELHORADO */
._memoryCard_hb9b3_1421._matched_hb9b3_1531 ._cardFront_hb9b3_1723,
._card_hb9b3_1423._matched_hb9b3_1531 ._cardFront_hb9b3_1723 {
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  border-color: rgba(76, 175, 80, 0.8);
  color: white;
  animation: _matchPulse_hb9b3_1 0.8s ease-in-out;
  box-shadow: 
    0 12px 30px rgba(76, 175, 80, 0.4),
    inset 0 2px 10px rgba(255, 255, 255, 0.3);
}

._memoryCard_hb9b3_1421._matched_hb9b3_1531 ._cardBack_hb9b3_1629,
._card_hb9b3_1423._matched_hb9b3_1531 ._cardBack_hb9b3_1629 {
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  border-color: rgba(76, 175, 80, 0.8);
  box-shadow: 
    0 12px 30px rgba(76, 175, 80, 0.4),
    inset 0 2px 10px rgba(255, 255, 255, 0.3);
}

/* Cartas combinadas - FEEDBACK MELHORADO SEGUINDO PADRÃO CONTAGEM NÚMEROS */
._memoryCard_hb9b3_1421._matched_hb9b3_1531 ._cardFront_hb9b3_1723,
._card_hb9b3_1423._matched_hb9b3_1531 ._cardFront_hb9b3_1723 {
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  border-color: rgba(76, 175, 80, 0.8);
  color: white;
  animation: _matchSuccess_hb9b3_1 1.2s ease-in-out;
  box-shadow: 
    0 12px 30px rgba(76, 175, 80, 0.4),
    inset 0 2px 10px rgba(255, 255, 255, 0.3);
}

._memoryCard_hb9b3_1421._matched_hb9b3_1531 ._cardBack_hb9b3_1629,
._card_hb9b3_1423._matched_hb9b3_1531 ._cardBack_hb9b3_1629 {
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  border-color: rgba(76, 175, 80, 0.8);
  box-shadow: 
    0 12px 30px rgba(76, 175, 80, 0.4),
    inset 0 2px 10px rgba(255, 255, 255, 0.3);
  animation: _matchCelebration_hb9b3_1 1.2s ease-in-out;
}

/* Animações de sucesso para pares encontrados */
@keyframes _matchSuccess_hb9b3_1 {
  0% { 
    transform: scale(1) rotateY(180deg); 
    box-shadow: 0 12px 30px rgba(76, 175, 80, 0.4);
  }
  25% {
    transform: scale(1.15) rotateY(180deg);
    box-shadow: 0 16px 40px rgba(76, 175, 80, 0.8);
  }
  50% {
    transform: scale(1.25) rotateY(180deg);
    box-shadow: 0 20px 50px rgba(76, 175, 80, 1);
  }
  75% {
    transform: scale(1.1) rotateY(180deg);
    box-shadow: 0 16px 40px rgba(76, 175, 80, 0.8);
  }
  100% { 
    transform: scale(1) rotateY(180deg); 
    box-shadow: 0 12px 30px rgba(76, 175, 80, 0.4);
  }
}

@keyframes _matchCelebration_hb9b3_1 {
  0% { 
    transform: rotateY(180deg) scale(1);
  }
  25% {
    transform: rotateY(180deg) scale(1.1) rotate(5deg);
  }
  50% {
    transform: rotateY(180deg) scale(1.2) rotate(-5deg);
  }
  75% {
    transform: rotateY(180deg) scale(1.1) rotate(3deg);
  }
  100% {
    transform: rotateY(180deg) scale(1) rotate(0deg);
  }
}

/* Mensagem de par encontrado - SEGUINDO PADRÃO CONTAGEM NÚMEROS */
._pairFoundMessage_hb9b3_2011 {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  color: white;
  padding: 2rem 3rem;
  border-radius: 20px;
  font-size: 1.5rem;
  font-weight: 700;
  box-shadow: 0 20px 40px rgba(76, 175, 80, 0.4);
  z-index: 1000;
  text-align: center;
  animation: _pairFoundSlide_hb9b3_1 2.5s ease-in-out;
  border: 3px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

._pairFoundMessage_hb9b3_2011::before {
  content: '🎉';
  font-size: 2rem;
  display: block;
  margin-bottom: 0.5rem;
  animation: _celebrationBounce_hb9b3_1 0.6s ease-in-out infinite alternate;
}

@keyframes _pairFoundSlide_hb9b3_1 {
  0% { 
    opacity: 0; 
    transform: translate(-50%, -50%) scale(0.5) rotateY(-90deg); 
  }
  15% { 
    opacity: 1; 
    transform: translate(-50%, -50%) scale(1.1) rotateY(10deg); 
  }
  25% { 
    transform: translate(-50%, -50%) scale(1) rotateY(0deg); 
  }
  75% { 
    opacity: 1; 
    transform: translate(-50%, -50%) scale(1) rotateY(0deg); 
  }
  100% { 
    opacity: 0; 
    transform: translate(-50%, -50%) scale(0.8) rotateY(90deg); 
  }
}

@keyframes _celebrationBounce_hb9b3_1 {
  0% { 
    transform: scale(1) rotate(-5deg); 
  }
  100% { 
    transform: scale(1.2) rotate(5deg); 
  }
}

/* Feedback para tentativa incorreta - SEGUINDO PADRÃO CONTAGEM NÚMEROS */
._incorrectPairMessage_hb9b3_2129 {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(135deg, #f44336 0%, #e57373 100%);
  color: white;
  padding: 1.5rem 2.5rem;
  border-radius: 16px;
  font-size: 1.2rem;
  font-weight: 600;
  box-shadow: 0 15px 30px rgba(244, 67, 54, 0.4);
  z-index: 1000;
  text-align: center;
  animation: _incorrectShake_hb9b3_1 1.5s ease-in-out;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

._incorrectPairMessage_hb9b3_2129::before {
  content: '❌';
  font-size: 1.5rem;
  display: block;
  margin-bottom: 0.5rem;
}

@keyframes _incorrectShake_hb9b3_1 {
  0%, 100% { 
    transform: translate(-50%, -50%); 
    opacity: 0;
  }
  10% { 
    transform: translate(-48%, -50%); 
    opacity: 1;
  }
  20% { 
    transform: translate(-52%, -50%); 
  }
  30% { 
    transform: translate(-48%, -50%); 
  }
  40% { 
    transform: translate(-52%, -50%); 
  }
  50% { 
    transform: translate(-50%, -50%); 
  }
  90% { 
    opacity: 1;
  }
}

/* Controles do jogo - SEGUINDO PADRÃO CONTAGEM NÚMEROS */
._gameControls_hb9b3_2233 {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

._controlButton_hb9b3_2249 {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

._controlButton_hb9b3_2249:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* Padrão seguido - sem botões específicos */

/* Tela de finalização */
._completionScreen_hb9b3_2297 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: var(--card-blur);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

._completionCard_hb9b3_2325 {
  background: var(--gradient-bg);
  border-radius: 20px;
  padding: 3rem;
  text-align: center;
  color: white;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

._completionTitle_hb9b3_2347 {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
}

._completionStats_hb9b3_2359 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
}

._completionStat_hb9b3_2359 {
  background: var(--card-background);
  padding: 1rem;
  border-radius: 12px;
}

._completionActions_hb9b3_2385 {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

/* Mensagem de encorajamento */
._encouragementMessage_hb9b3_2403 {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
  padding: 1.5rem 2rem;
  border-radius: 12px;
  font-size: 1.2rem;
  font-weight: 600;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  text-align: center;
  animation: _messageSlide_hb9b3_1 3s ease-in-out;
}

@keyframes _messageSlide_hb9b3_1 {
  0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
  15% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  85% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}

/* Análise cognitiva */
._cognitiveAnalysis_hb9b3_2453 {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  padding: 2rem;
  border-radius: 16px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  backdrop-filter: var(--card-blur);
}

._cognitiveAnalysis_hb9b3_2453 h3 {
  margin-top: 0;
  color: #667eea;
  text-align: center;
}

._cognitiveAnalysis_hb9b3_2453 div {
  margin: 0.5rem 0;
  line-height: 1.4;
}

/* Sistema de atividades */

/* Grid Container para o jogo da memória - SIMPLES */
._gridContainer_hb9b3_2513 {
  display: grid;
  gap: 1rem;
  margin: 1rem auto;
  max-width: 600px;
  justify-content: center;
  padding: 1rem;
}

/* Atividades específicas */
._sequenceContainer_hb9b3_2533,
._spatialContainer_hb9b3_2535,
._soundContainer_hb9b3_2537,
._imageContainer_hb9b3_2539,
._numberContainer_hb9b3_2541 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  color: white;
  min-height: 350px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 1.1rem;
  margin: 1rem 0;
}

/* Display da sequência */
._roundInfo_hb9b3_2577 {
  display: flex;
  justify-content: center;
  margin-top: 0.5rem;
}

._roundBadge_hb9b3_2589 {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 0.5rem 1.2rem;
  font-size: 1rem;
  font-weight: bold;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

._sequenceDisplay_hb9b3_749 {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  justify-content: center;
  align-items: center;
  margin: 2rem 0;
  min-height: 140px;
}

._sequenceRow_hb9b3_2629 {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

._sequenceItem_hb9b3_2645 {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  border: 6px solid rgba(255, 255, 255, 0.8);
  margin: 0 0.8rem;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

._sequenceItem_hb9b3_2645::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  box-shadow: inset 0 -8px 20px rgba(0, 0, 0, 0.2);
  pointer-events: none;
}

@keyframes _buttonPressEffect_hb9b3_1 {
  0% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.2); opacity: 1; }
  100% { transform: scale(1); opacity: 0.8; }
}

._pressEffect_hb9b3_2705 {
  animation: _buttonPressEffect_hb9b3_1 0.3s ease-in-out;
}

._sequenceTitle_hb9b3_2713 {
  width: 100%;
  text-align: center;
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  color: rgba(255, 255, 255, 0.9);
}

/* Botões de cores para sequência */
._colorButtons_hb9b3_2731 {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  align-items: center;
  margin: 2rem 0;
  flex-wrap: wrap;
}

._feedbackContainer_hb9b3_2749 {
  margin: 1.5rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

._progressBar_hb9b3_1095 {
  width: 80%;
  height: 12px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
  margin: 0.5rem 0;
}

._progressFill_hb9b3_1115 {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  border-radius: 10px;
  transition: width 0.3s ease-out;
}

._progressCounter_hb9b3_1181 {
  font-weight: bold;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 20px;
  padding: 0.2rem 0.8rem;
  margin-left: 0.5rem;
}

._sequenceHistory_hb9b3_2815 {
  display: flex;
  gap: 8px;
  margin-top: 0.5rem;
}

._historyDot_hb9b3_2827 {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
}

._colorButton_hb9b3_2731 {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  border: 4px solid rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  margin: 0.5rem;
  position: relative;
  overflow: hidden;
}

._colorButton_hb9b3_2731::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  box-shadow: inset 0 -10px 20px rgba(0, 0, 0, 0.2);
  pointer-events: none;
}

._colorButton_hb9b3_2731:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
  border-color: rgba(255, 255, 255, 0.8);
}

/* Estados ativos e feedback visual */
._activeButton_hb9b3_2905 {
  transform: scale(1.15);
  box-shadow: 0 0 30px rgba(255, 255, 255, 0.8);
  border-color: white;
}

/* Classes para feedback visual animado */
._buttonPress_hb9b3_2919 {
  animation: _buttonPressEffect_hb9b3_1 0.5s ease;
}

._correctFeedback_hb9b3_2927 {
  animation: _correctFeedback_hb9b3_2927 0.8s ease;
}

._incorrectFeedback_hb9b3_2935 {
  animation: _incorrectFeedback_hb9b3_2935 0.5s ease;
}

/* Cores específicas */
._red_hb9b3_2945 {
  background-color: #f44336;
  box-shadow: 0 6px 20px rgba(244, 67, 54, 0.6);
}

._blue_hb9b3_2955 {
  background-color: #2196f3;
  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.6);
}

._green_hb9b3_2965 {
  background-color: #4caf50;
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.6);
}

._yellow_hb9b3_2975 {
  background-color: #ffeb3b;
  box-shadow: 0 6px 20px rgba(255, 235, 59, 0.6);
}

._purple_hb9b3_2985 {
  background-color: #9c27b0;
  box-shadow: 0 6px 20px rgba(156, 39, 176, 0.6);
}

._orange_hb9b3_2995 {
  background-color: #ff9800;
  box-shadow: 0 6px 20px rgba(255, 152, 0, 0.6);
}

._blue_hb9b3_2955 {
  background-color: #2196f3;
}

._green_hb9b3_2965 {
  background-color: #4caf50;
}

._yellow_hb9b3_2975 {
  background-color: #ffeb3b;
}

/* Feedback da sequência */
._sequenceFeedback_hb9b3_3031 {
  margin-top: 2rem;
  color: white;
}

._sequenceProgress_hb9b3_3041 {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  margin-top: 1rem;
}

._sequenceStep_hb9b3_3055 {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

._sequenceStep_hb9b3_3055._completed_hb9b3_3073 {
  background: #4caf50;
  border-color: #4caf50;
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
}

/* Grid espacial */
._spatialGrid_hb9b3_3087 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.8rem;
  max-width: 450px;
  margin: 2rem auto;
  padding: 1.5rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

._spatialCell_hb9b3_3111 {
  width: 90px;
  height: 90px;
  background: rgba(255, 255, 255, 0.15);
  border: 3px solid rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

._spatialCell_hb9b3_3111:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

._spatialCell_hb9b3_3111._highlighted_hb9b3_3155 {
  background: #ffeb3b !important;
  border-color: #ffc107 !important;
  box-shadow: 0 0 25px rgba(255, 235, 59, 0.8) !important;
  animation: _spatialPulse_hb9b3_1 1.5s ease-in-out infinite;
  color: #333 !important;
}

._spatialCell_hb9b3_3111._selected_hb9b3_3171 {
  background: rgba(33, 150, 243, 0.7);
  border-color: #2196f3;
  box-shadow: 0 0 20px rgba(33, 150, 243, 0.6);
}

._spatialCell_hb9b3_3111._correct_hb9b3_2927 {
  background: rgba(76, 175, 80, 0.9) !important;
  border-color: #4caf50 !important;
  box-shadow: 0 0 25px rgba(76, 175, 80, 0.8) !important;
}

._spatialCell_hb9b3_3111._incorrect_hb9b3_2129 {
  background: rgba(244, 67, 54, 0.9) !important;
  border-color: #f44336 !important;
  box-shadow: 0 0 25px rgba(244, 67, 54, 0.8) !important;
}

@keyframes _spatialPulse_hb9b3_1 {
  0%, 100% { 
    opacity: 0.9; 
    transform: scale(1);
    box-shadow: 0 0 25px rgba(255, 235, 59, 0.8);
  }
  50% { 
    opacity: 1; 
    transform: scale(1.15);
    box-shadow: 0 0 35px rgba(255, 235, 59, 1);
  }
}

/* Feedback espacial */
._feedback_hb9b3_2749 {
  margin-top: 2rem;
  text-align: center;
  color: white;
  font-size: 1.1rem;
}

/* Sequência Numérica - Mobile First - Padrão ImageAssociation */
._numberSequenceContainer_hb9b3_3251 {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  margin: 1rem 0;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

._numberRow_hb9b3_3273 {
  display: flex;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  margin: 1.5rem 0;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

._numberCard_hb9b3_3299 {
  min-width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  font-weight: bold;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

._numberCard_hb9b3_3299._missingNumber_hb9b3_3335 {
  background: linear-gradient(135deg, rgba(255, 87, 34, 0.8) 0%, rgba(255, 152, 0, 0.8) 100%);
  backdrop-filter: blur(20px);
  border: 3px solid rgba(255, 255, 255, 0.5);
  color: white;
  font-size: 2.2rem;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: _numberPulse_hb9b3_1 2s ease-in-out infinite;
  position: relative;
  overflow: hidden;
  box-shadow: 0 12px 40px rgba(255, 87, 34, 0.3);
}

._numberCard_hb9b3_3299._missingNumber_hb9b3_3335::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: _shimmer_hb9b3_1 3s infinite;
}

@keyframes _numberPulse_hb9b3_1 {
  0%, 100% { 
    transform: scale(1);
    box-shadow: 0 4px 20px rgba(255, 87, 34, 0.4);
  }
  50% { 
    transform: scale(1.1);
    box-shadow: 0 8px 30px rgba(255, 87, 34, 0.6);
  }
}

@keyframes _shimmer_hb9b3_1 {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

._patternInfo_hb9b3_3435 {
  margin: 2rem 0;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.15) 0%, rgba(63, 81, 181, 0.15) 100%);
  border: 2px solid rgba(33, 150, 243, 0.4);
  border-radius: 16px;
  color: white;
  font-size: 1rem;
  line-height: 1.6;
  text-align: center;
  box-shadow: 0 8px 32px rgba(33, 150, 243, 0.2);
  position: relative;
  overflow: hidden;
}

._patternInfo_hb9b3_3435::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: _patternShine_hb9b3_1 3s infinite;
}

._patternInfo_hb9b3_3435 strong {
  color: #64b5f6;
  font-weight: bold;
}

@keyframes _patternShine_hb9b3_1 {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

._answerContainer_hb9b3_3525 {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: center;
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

._numberInput_hb9b3_3549 {
  width: 100%;
  max-width: 200px;
  height: 70px;
  font-size: 1.8rem;
  font-weight: bold;
  text-align: center;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  outline: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  -webkit-appearance: none;
  -moz-appearance: textfield;
  appearance: none;
  touch-action: manipulation;
}

._numberInput_hb9b3_3549::-webkit-outer-spin-button,
._numberInput_hb9b3_3549::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

._numberInput_hb9b3_3549::placeholder {
  color: rgba(255, 255, 255, 0.6);
  font-weight: normal;
  opacity: 0.8;
}

._numberInput_hb9b3_3549:focus {
  border-color: rgba(33, 150, 243, 0.8);
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 0 20px rgba(33, 150, 243, 0.4);
  transform: scale(1.05);
}

._submitButton_hb9b3_3631 {
  width: 100%;
  max-width: 200px;
  height: 70px;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.8), rgba(69, 160, 73, 0.8));
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  color: white;
  font-size: 1.4rem;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(76, 175, 80, 0.2);
  touch-action: manipulation;
  position: relative;
  overflow: hidden;
}

._submitButton_hb9b3_3631::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

._submitButton_hb9b3_3631:hover:not(:disabled)::before {
  width: 300px;
  height: 300px;
}

._submitButton_hb9b3_3631:hover:not(:disabled) {
  background: linear-gradient(135deg, #45a049, #3d8b40);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(76, 175, 80, 0.5);
}

._submitButton_hb9b3_3631:disabled {
  background: rgba(158, 158, 158, 0.5);
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* Responsividade para tablets */
@media (min-width: 768px) {
  ._numberRow_hb9b3_3273 {
    gap: 1.5rem;
    padding: 2rem;
  }

  ._numberCard_hb9b3_3299 {
    min-width: 100px;
    height: 100px;
    font-size: 2rem;
  }

  ._numberCard_hb9b3_3299._missingNumber_hb9b3_3335 {
    font-size: 2.5rem;
  }
  
  ._answerContainer_hb9b3_3525 {
    flex-direction: row;
    justify-content: center;
  }
  
  ._numberInput_hb9b3_3549 {
    max-width: 150px;
  }
  
  ._submitButton_hb9b3_3631 {
    max-width: 150px;
  }
}

/* Responsividade para desktop */
@media (min-width: 1024px) {
  ._numberSequenceContainer_hb9b3_3251 {
    padding: 2.5rem;
  }

  ._numberRow_hb9b3_3273 {
    gap: 2rem;
    padding: 2.5rem;
  }

  ._numberCard_hb9b3_3299 {
    min-width: 120px;
    height: 120px;
    font-size: 2.2rem;
  }

  ._numberCard_hb9b3_3299._missingNumber_hb9b3_3335 {
    font-size: 2.8rem;
  }
}

/* Reconstrução de Imagem - Mobile First */
._imageContainer_hb9b3_2539 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1rem;
  text-align: center;
  color: white;
  margin: 1rem 0;
}

._imagePreview_hb9b3_3863 {
  padding: 1rem;
  margin-bottom: 2rem;
}

._emojiPreviewGrid_hb9b3_3873 {
  display: grid;
  gap: 0.5rem;
  max-width: 280px;
  margin: 1rem auto;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

._previewItem_hb9b3_3895 {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

._previewEmoji_hb9b3_3919 {
  font-size: 3rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Grid de reconstrução - Mobile First - Padrão ImageAssociation */
._reconstructionGrid_hb9b3_3931 {
  display: grid;
  gap: 1rem;
  max-width: 400px;
  margin: 2rem auto;
  padding: 2rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

._dropZone_hb9b3_3957 {
  width: 100px;
  height: 100px;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  margin: 0 auto;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  min-height: 100px;
}

._dropZone_hb9b3_3957:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.25);
}

._dropZone_hb9b3_3957:empty::after {
  content: '+';
  color: rgba(255, 255, 255, 0.5);
  font-size: 2rem;
  font-weight: 700;
}

._puzzlePiece_hb9b3_4021 {
  width: 100%;
  height: 100%;
  background: var(--success-bg);
  border: 3px solid var(--success-border);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

._puzzlePiece_hb9b3_4021:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 15px 30px rgba(76, 175, 80, 0.3);
  border-color: var(--success-border);
  background: rgba(76, 175, 80, 0.4);
}

/* Estados de feedback */
._dropZone_hb9b3_3957._correct_hb9b3_2927 {
  border-color: var(--success-border);
  background: var(--success-bg);
  animation: _correctPulse_hb9b3_1 0.6s ease-in-out;
}

._dropZone_hb9b3_3957._incorrect_hb9b3_2129 {
  border-color: var(--error-border);
  background: var(--error-bg);
  animation: _incorrectShake_hb9b3_1 0.6s ease-in-out;
}

@keyframes _correctPulse_hb9b3_1 {
  0%, 100% { transform: translateY(-5px) scale(1.05); }
  50% { transform: translateY(-5px) scale(1.2); }
}

@keyframes _incorrectShake_hb9b3_1 {
  0%, 100% { transform: translateY(-5px) translateX(0); }
  25% { transform: translateY(-5px) translateX(-10px); }
  75% { transform: translateY(-5px) translateX(10px); }
}

/* Peças disponíveis - Mobile First - Padrão ImageAssociation */
._availablePieces_hb9b3_4113 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  padding: 2rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

._pieceContainer_hb9b3_4147 {
  flex: 0 0 auto;
}

._availablePiece_hb9b3_4113 {
  width: 100px;
  height: 100px;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  min-height: 100px;
  padding: 1.5rem;
}

._availablePiece_hb9b3_4113:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.25);
}

._availablePiece_hb9b3_4113:active {
  transform: translateY(-2px) scale(0.95);
}

._emojiPiece_hb9b3_4211 {
  font-size: 3.5rem;
  font-weight: 700;
  user-select: none;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Responsividade para tablets */
@media (min-width: 768px) {
  ._imageContainer_hb9b3_2539 {
    padding: 2rem;
  }
  
  ._emojiPreviewGrid_hb9b3_3873 {
    max-width: 350px;
    gap: 0.8rem;
  }
  
  ._previewItem_hb9b3_3895 {
    width: 75px;
    height: 75px;
  }
  
  ._previewEmoji_hb9b3_3919 {
    font-size: 3.5rem;
  }
  
  ._reconstructionGrid_hb9b3_3931 {
    max-width: 500px;
    gap: 1.2rem;
  }
  
  ._dropZone_hb9b3_3957 {
    width: 120px;
    height: 120px;
    min-height: 120px;
  }

  ._availablePieces_hb9b3_4113 {
    max-width: 600px;
    gap: 1.2rem;
  }

  ._availablePiece_hb9b3_4113 {
    width: 120px;
    height: 120px;
    min-height: 120px;
  }

  ._emojiPiece_hb9b3_4211 {
    font-size: 4rem;
  }
}

/* Responsividade para desktop */
@media (min-width: 1024px) {
  ._emojiPreviewGrid_hb9b3_3873 {
    max-width: 450px;
    gap: 1rem;
  }
  
  ._previewItem_hb9b3_3895 {
    width: 90px;
    height: 90px;
  }
  
  ._previewEmoji_hb9b3_3919 {
    font-size: 4rem;
  }
  
  ._reconstructionGrid_hb9b3_3931 {
    max-width: 600px;
    gap: 1.5rem;
  }
  
  ._dropZone_hb9b3_3957 {
    width: 140px;
    height: 140px;
    min-height: 140px;
  }

  ._availablePieces_hb9b3_4113 {
    max-width: 700px;
    gap: 1.5rem;
  }

  ._availablePiece_hb9b3_4113 {
    width: 140px;
    height: 140px;
    min-height: 140px;
  }

  ._emojiPiece_hb9b3_4211 {
    font-size: 4.5rem;
  }
}

/* Tela de conclusão */
._activitiesCompleted_hb9b3_4409 {
  margin: 1.5rem 0;
  text-align: left;
}

._activitiesCompleted_hb9b3_4409 h4 {
  color: #4CAF50;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  text-align: center;
}

._activityList_hb9b3_4433 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
  margin-top: 1rem;
}

._activityItem_hb9b3_4447 {
  background: var(--success-bg);
  border: 1px solid var(--success-border);
  border-radius: 8px;
  padding: 0.75rem;
  text-align: center;
  font-size: 0.9rem;
  color: white;
}

/* Responsividade */
@media (max-width: 768px) {
  ._memoryGame_hb9b3_83 {
    padding: 0.5rem;
  }
  
  ._gameTitle_hb9b3_153 {
    font-size: 1.5rem;
  }
  
  ._memoryGrid_hb9b3_1377 {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    margin: 1rem auto;
    max-width: 400px;
    padding: 0.5rem;
  }
  
  ._memoryCard_hb9b3_1421 {
    font-size: 1.5rem;
    min-height: 60px;
  }
  
  ._gameStats_hb9b3_807 {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
  
  ._questionArea_hb9b3_893 {
    padding: 1rem;
  }
  
  ._completionCard_hb9b3_2325 {
    padding: 2rem;
    margin: 1rem;
  }
  
  ._completionTitle_hb9b3_2347 {
    font-size: 2rem;
  }
  
  ._activityHeader_hb9b3_4549 {
    padding: 1rem;
    margin-bottom: 1rem;
  }
  
  ._currentActivityName_hb9b3_4559 {
    font-size: 1rem;
  }
  
  ._activityList_hb9b3_4433 {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  ._activityItem_hb9b3_4447 {
    font-size: 0.8rem;
    padding: 0.5rem;
  }
  
  ._activityMenu_hb9b3_277 {
    gap: 0.25rem;
  }
  
  ._activityButton_hb9b3_293 {
    padding: 0.4rem 0.8rem;
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  ._gameStats_hb9b3_807 {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  ._statCard_hb9b3_821 {
    padding: 0.75rem;
  }

  ._memoryGrid_hb9b3_1377 {
    gap: 0.3rem;
    margin: 1rem auto;
    padding: 0.3rem;
  }

  ._memoryGrid_hb9b3_1377._grid2x2_hb9b3_1409 {
    max-width: 250px;
  }

  ._memoryGrid_hb9b3_1377._grid3x3_hb9b3_1433 {
    max-width: 300px;
  }

  ._memoryGrid_hb9b3_1377._grid4x4_hb9b3_1457 {
    grid-template-columns: repeat(3, 1fr);
    max-width: 300px;
  }

  ._memoryGrid_hb9b3_1377._grid5x5_hb9b3_1481 {
    grid-template-columns: repeat(3, 1fr);
    max-width: 300px;
  }

  ._memoryCard_hb9b3_1421 {
    font-size: 1.2rem;
    min-height: 50px;
    border-radius: 12px;
  }
  
  ._controlButtons_hb9b3_4687 {
    flex-direction: column;
    align-items: center;
  }
  
  ._completionActions_hb9b3_2385 {
    flex-direction: column;
  }
  
  ._progressBar_hb9b3_1095 {
    max-width: 250px;
  }
  
  ._sequenceContainer_hb9b3_2533,
  ._spatialContainer_hb9b3_2535,
  ._soundContainer_hb9b3_2537,
  ._imageContainer_hb9b3_2539,
  ._numberContainer_hb9b3_2541 {
    padding: 1.5rem;
    font-size: 1rem;
  }
  
  ._activityButton_hb9b3_293 {
    padding: 0.4rem 0.8rem;
    font-size: 0.7rem;
  }
}

/* Media query adicional para celulares em landscape e telas pequenas */
@media (max-width: 640px) and (orientation: landscape) {
  ._memoryGrid_hb9b3_1377 {
    gap: 0.3rem;
    margin: 0.5rem auto;
    padding: 0.3rem;
  }

  ._memoryGrid_hb9b3_1377._grid2x2_hb9b3_1409 {
    max-width: 200px;
  }

  ._memoryGrid_hb9b3_1377._grid3x3_hb9b3_1433 {
    max-width: 250px;
  }

  ._memoryGrid_hb9b3_1377._grid4x4_hb9b3_1457 {
    grid-template-columns: repeat(4, 1fr);
    max-width: 320px;
  }

  ._memoryGrid_hb9b3_1377._grid5x5_hb9b3_1481 {
    grid-template-columns: repeat(5, 1fr);
    max-width: 400px;
  }

  ._memoryCard_hb9b3_1421 {
    font-size: 1rem;
    min-height: 40px;
    border-radius: 8px;
  }

  ._gameHeader_hb9b3_125 {
    padding: 0.5rem 2rem 0.5rem 0.5rem;
    min-height: 50px;
  }

  ._gameTitle_hb9b3_153 {
    font-size: 1.3rem;
  }

  ._activitySubtitle_hb9b3_177 {
    font-size: 0.6rem;
  }
}

/* Suporte a alto contraste */
[data-theme="high-contrast"] {
  --card-background: #000;
  --card-border: 1px solid #fff;
  --success-bg: #28a745;
  --success-border: #1e7e34;
  --error-bg: #dc3545;
  --error-border: #bd2130;
}

/* Suporte a movimento reduzido */
._reduced-motion_hb9b3_4857 {
  ._memoryCard_hb9b3_1421, ._controlButton_hb9b3_2249, ._encouragementMessage_hb9b3_2403, ._activityButton_hb9b3_293 {
    animation: none !important;
    transition: none !important;
  }
}

/* Classes adicionais para atividades */
._questionHeader_hb9b3_921 {
  text-align: center;
  margin-bottom: 2rem;
}

._questionTitle_hb9b3_933 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

._instructions_hb9b3_947 {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1rem;
  line-height: 1.4;
}

._questionArea_hb9b3_893 {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  margin: 1rem 0;
  color: white;
}

/* ========================================
   RESPONSIVIDADE PARA DISPOSITIVOS MÓVEIS
   ======================================== */

@media (max-width: 768px) {
  ._gridContainer_hb9b3_2513 {
    gap: 1rem;
    padding: 1.5rem;
    margin: 1rem auto;
  }

  ._memoryGrid_hb9b3_1377 {
    gap: 1rem;
    padding: 1.5rem;
    margin: 1rem auto;
  }

  ._memoryCard_hb9b3_1421, 
  ._card_hb9b3_1423 {
    min-height: 90px;
    min-width: 90px;
  }

  ._memoryGrid_hb9b3_1377._grid2x2_hb9b3_1409 ._memoryCard_hb9b3_1421,
  ._memoryGrid_hb9b3_1377._grid2x2_hb9b3_1409 ._card_hb9b3_1423 {
    min-height: 120px;
    min-width: 120px;
  }

  ._memoryGrid_hb9b3_1377._grid3x3_hb9b3_1433 ._memoryCard_hb9b3_1421,
  ._memoryGrid_hb9b3_1377._grid3x3_hb9b3_1433 ._card_hb9b3_1423 {
    min-height: 100px;
    min-width: 100px;
  }

  ._memoryGrid_hb9b3_1377._grid4x4_hb9b3_1457 ._memoryCard_hb9b3_1421,
  ._memoryGrid_hb9b3_1377._grid4x4_hb9b3_1457 ._card_hb9b3_1423 {
    min-height: 85px;
    min-width: 85px;
  }

  ._memoryGrid_hb9b3_1377._grid5x5_hb9b3_1481 ._memoryCard_hb9b3_1421,
  ._memoryGrid_hb9b3_1377._grid5x5_hb9b3_1481 ._card_hb9b3_1423 {
    min-height: 75px;
    min-width: 75px;
  }

  ._cardFront_hb9b3_1723, ._cardBack_hb9b3_1629 {
    font-size: 2.5rem;
  }

  ._memoryGrid_hb9b3_1377._grid5x5_hb9b3_1481 ._cardFront_hb9b3_1723,
  ._memoryGrid_hb9b3_1377._grid5x5_hb9b3_1481 ._cardBack_hb9b3_1629 {
    font-size: 1.8rem;
  }
}

@media (max-width: 480px) {
  ._gridContainer_hb9b3_2513 {
    gap: 0.8rem;
    padding: 1rem;
    margin: 0.5rem auto;
  }

  ._memoryGrid_hb9b3_1377 {
    gap: 0.8rem;
    padding: 1rem;
    margin: 0.5rem auto;
  }

  ._memoryCard_hb9b3_1421, 
  ._card_hb9b3_1423 {
    min-height: 65px;
    min-width: 65px;
  }

  ._memoryGrid_hb9b3_1377._grid4x4_hb9b3_1457 ._memoryCard_hb9b3_1421,
  ._memoryGrid_hb9b3_1377._grid4x4_hb9b3_1457 ._card_hb9b3_1423 {
    min-height: 60px;
    min-width: 60px;
  }

  ._memoryGrid_hb9b3_1377._grid5x5_hb9b3_1481 ._memoryCard_hb9b3_1421,
  ._memoryGrid_hb9b3_1377._grid5x5_hb9b3_1481 ._card_hb9b3_1423 {
    min-height: 50px;
    min-width: 50px;
  }

  ._cardFront_hb9b3_1723, ._cardBack_hb9b3_1629 {
    font-size: 1.8rem;
  }

  ._memoryGrid_hb9b3_1377._grid5x5_hb9b3_1481 ._cardFront_hb9b3_1723,
  ._memoryGrid_hb9b3_1377._grid5x5_hb9b3_1481 ._cardBack_hb9b3_1629 {
    font-size: 1.2rem;
  }
}

/* ==================== DEBUG FIXES PARA FLIP DAS CARTAS ==================== */

/* Força a visibilidade correta do emoji quando carta está virada */
._card_hb9b3_1423._flipped_hb9b3_1575 ._cardBack_hb9b3_1629,
._memoryCard_hb9b3_1421._flipped_hb9b3_1575 ._cardBack_hb9b3_1629 {
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 10 !important;
  display: flex !important;
}

/* Garante que a frente fique oculta quando virada */
._card_hb9b3_1423._flipped_hb9b3_1575 ._cardFront_hb9b3_1723,
._memoryCard_hb9b3_1421._flipped_hb9b3_1575 ._cardFront_hb9b3_1723 {
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 1 !important;
}

/* Força rotação para cartas matched */
._card_hb9b3_1423._matched_hb9b3_1531 ._cardInner_hb9b3_1555,
._memoryCard_hb9b3_1421._matched_hb9b3_1531 ._cardInner_hb9b3_1555 {
  transform: rotateY(180deg) !important;
}

/* DEBUG: Força emoji sempre visível no verso */
._cardBack_hb9b3_1629 {
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Segoe UI Symbol", sans-serif !important;
  line-height: 1 !important;
}