# 🎯 GUIA DE PADRONIZAÇÃO DE JOGOS - PORTAL BETINA V3

## 📋 **PADRÃO ESTABELECIDO**

Todos os jogos do Portal Betina V3 devem seguir o padrão estabelecido pelos jogos:
- ✅ **ColorMatch** (Referência principal)
- ✅ **MemoryGame** (Referência principal)  
- ✅ **ContagemNumeros** (Referência principal)
- ✅ **LetterRecognition** (Já padronizado)
- 🔄 **PadroesVisuais** (Em padronização)

## 🎮 **ESTRUTURA PADRÃO OBRIGATÓRIA**

### 1. **Sistema de Atividades (6 atividades por jogo)**
```javascript
const ACTIVITY_TYPES = {
  ACTIVITY_1: {
    id: 'activity_1',
    name: 'Nome da Atividade',
    icon: '🎯',
    description: 'Descrição clara',
    component: 'ComponentName'
  },
  // ... mais 5 atividades
};
```

### 2. **Estado do Jogo Padronizado**
```javascript
const [gameState, setGameState] = useState({
  status: 'start', // 'start', 'playing', 'paused', 'finished'
  score: 0,
  round: 1,
  totalRounds: 10,
  difficulty: 'EASY',
  accuracy: 100,
  roundStartTime: null,
  
  // Sistema de atividades
  currentActivity: ACTIVITY_TYPES.ACTIVITY_1.id,
  activityCycle: [...],
  activityIndex: 0,
  roundsPerActivity: 4,
  activityRoundCount: 0,
  activitiesCompleted: [],
  
  // Dados específicos de atividades
  activityData: { ... },
  
  // Feedback e animações
  showFeedback: false,
  feedbackType: null,
  feedbackMessage: '',
  showCelebration: false,
  
  // Métricas comportamentais
  responseTime: 0,
  hesitationCount: 0,
  helpUsed: false,
  consecutiveCorrect: 0,
  totalAttempts: 0,
  correctAttempts: 0
});
```

### 3. **Hooks Obrigatórios**
```javascript
// Sistema unificado
const { startUnifiedSession, recordInteraction, endUnifiedSession, ... } = useUnifiedGameLogic('game_name');

// Multissensorial
const { initializeSession, recordInteraction, finalizeSession, ... } = useMultisensoryIntegration('game_name', collectorsHub);

// Terapêutico
const { initializeSession, recordActivity, generateReport, ... } = useTherapeuticOrchestrator();
```

### 4. **TTS Padronizado**
```javascript
const [ttsActive, setTtsActive] = useState(true);

const speak = useCallback((text, options = {}) => {
  if (!ttsActive || !('speechSynthesis' in window)) return;
  
  window.speechSynthesis.cancel();
  const utterance = new SpeechSynthesisUtterance(text);
  utterance.lang = 'pt-BR';
  utterance.rate = options.rate || 0.9;
  window.speechSynthesis.speak(utterance);
}, [ttsActive]);

const toggleTTS = useCallback(() => {
  setTtsActive(prev => !prev);
  if (!ttsActive) speak('TTS ativado');
}, [ttsActive, speak]);
```

### 5. **Interface Padronizada**
```jsx
return (
  <div className="gameContainer">
    <div className="gameContent">
      {/* Header padrão */}
      <div className="gameHeader">
        <h1 className="gameTitle">
          🎮 Nome do Jogo
          <div className="activitySubtitle">
            {currentActivityName}
          </div>
        </h1>
        <button className="headerTtsButton" onClick={toggleTTS}>
          {ttsActive ? '🔊' : '🔇'}
        </button>
      </div>

      {/* Menu de Atividades OBRIGATÓRIO */}
      <div className="activityMenu">
        {Object.values(ACTIVITY_TYPES).map((activity) => (
          <button
            key={activity.id}
            className={`activityButton ${isCurrentActivity ? 'active' : ''}`}
            onClick={() => changeActivity(activity.id)}
            disabled={!canSwitch}
          >
            <span className="activityIcon">{activity.icon}</span>
            <span className="activityName">{activity.name}</span>
            {isCurrentActivity && <span className="activeIndicator">●</span>}
          </button>
        ))}
      </div>

      {/* Estatísticas Padronizadas */}
      <div className="gameStats">
        <div className="statCard">
          <div className="statValue">{gameState.round}</div>
          <div className="statLabel">Rodada</div>
        </div>
        <div className="statCard">
          <div className="statValue">{gameState.score}</div>
          <div className="statLabel">Pontos</div>
        </div>
        <div className="statCard">
          <div className="statValue">{gameState.accuracy}%</div>
          <div className="statLabel">Precisão</div>
        </div>
        <div className="statCard">
          <div className="statValue">{gameState.activityRoundCount}/4+</div>
          <div className="statLabel">Progresso</div>
        </div>
      </div>

      {/* Interface da atividade atual */}
      {renderCurrentActivity()}
    </div>
  </div>
);
```

## 🎯 **JOGOS A SEREM PADRONIZADOS**

### ❌ **Pendentes de Padronização:**
1. **ImageAssociation** - Associação de imagens
2. **MusicalSequence** - Sequência musical
3. **QuebraCabeca** - Quebra-cabeça
4. **PatternMatching** - Correspondência de padrões (remover - duplicata)
5. **SequenceLearning** - Aprendizado de sequência (remover - duplicata)

### 📝 **Checklist de Padronização:**

Para cada jogo, verificar:
- [ ] Sistema de 6 atividades implementado
- [ ] Menu de atividades visível e funcional
- [ ] Estado do jogo padronizado
- [ ] Hooks obrigatórios integrados
- [ ] TTS padronizado funcionando
- [ ] Interface seguindo o padrão exato
- [ ] Estatísticas padronizadas
- [ ] Tela de início com GameStartScreen
- [ ] Prop `onBack` implementada
- [ ] Métricas conectadas ao backend
- [ ] Logs estruturados implementados

## 🚀 **COMO PADRONIZAR UM JOGO**

### Passo 1: Analisar o jogo atual
```bash
# Verificar estrutura atual
- Identificar funcionalidades existentes
- Mapear atividades possíveis
- Verificar integrações existentes
```

### Passo 2: Definir as 6 atividades
```javascript
// Criar atividades específicas do jogo
const ACTIVITY_TYPES = {
  BASIC_ACTIVITY: { ... },
  INTERMEDIATE_ACTIVITY: { ... },
  ADVANCED_ACTIVITY: { ... },
  CREATIVE_ACTIVITY: { ... },
  CHALLENGE_ACTIVITY: { ... },
  BONUS_ACTIVITY: { ... }
};
```

### Passo 3: Implementar estado padronizado
```javascript
// Substituir estados antigos pelo gameState padronizado
const [gameState, setGameState] = useState({ ... });
```

### Passo 4: Integrar hooks obrigatórios
```javascript
// Adicionar todos os hooks necessários
const unifiedLogic = useUnifiedGameLogic('game_name');
const multisensory = useMultisensoryIntegration('game_name', collectorsHub);
const therapeutic = useTherapeuticOrchestrator();
```

### Passo 5: Implementar interface padronizada
```jsx
// Seguir exatamente o padrão da interface
<div className="gameContainer">
  {/* Header + Menu + Stats + Atividade */}
</div>
```

### Passo 6: Testar funcionalidades
```bash
# Verificar se tudo funciona
- Menu de atividades
- Troca entre atividades
- TTS funcionando
- Métricas sendo coletadas
- Interface responsiva
```

## ⚠️ **REGRAS IMPORTANTES**

1. **NUNCA** remover funcionalidades existentes
2. **SEMPRE** manter compatibilidade com o sistema atual
3. **OBRIGATÓRIO** ter exatamente 6 atividades por jogo
4. **OBRIGATÓRIO** seguir o padrão de interface exato
5. **OBRIGATÓRIO** implementar todos os hooks
6. **OBRIGATÓRIO** ter TTS funcionando
7. **OBRIGATÓRIO** ter métricas conectadas

## 🎨 **ESTILOS CSS PADRÃO**

Todos os jogos devem usar as mesmas classes CSS:
- `.gameContainer`
- `.gameContent`
- `.gameHeader`
- `.gameTitle`
- `.activitySubtitle`
- `.headerTtsButton`
- `.activityMenu`
- `.activityButton`
- `.activityIcon`
- `.activityName`
- `.activeIndicator`
- `.gameStats`
- `.statCard`
- `.statValue`
- `.statLabel`

## 📞 **SUPORTE**

Para dúvidas sobre padronização:
1. Consultar jogos de referência (ColorMatch, MemoryGame, ContagemNumeros)
2. Usar o template em `src/templates/GameStandardizationTemplate.jsx`
3. Seguir este guia rigorosamente
4. Testar em todos os dispositivos

---

**🎯 Portal Betina V3 - Padrão de Excelência em Jogos Educativos**
