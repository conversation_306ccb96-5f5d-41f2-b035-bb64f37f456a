/* 🎯 NAVEGAÇÃO UNIFICADA DOS DASHBOARDS PREMIUM */

._navigation_1m5b9_3 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

._navigation_1m5b9_3._compact_1m5b9_13 {
  padding: 16px;
  margin-bottom: 16px;
}

._navigationHeader_1m5b9_18 {
  text-align: center;
  margin-bottom: 24px;
  color: white;
}

._title_1m5b9_24 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

._titleIcon_1m5b9_34 {
  font-size: 1.8rem;
  animation: _pulse_1m5b9_1 2s infinite;
}

@keyframes _pulse_1m5b9_1 {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

._subtitle_1m5b9_44 {
  font-size: 0.9rem;
  opacity: 0.9;
  font-weight: 400;
}

._dashboardGrid_1m5b9_50 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

._compact_1m5b9_13 ._dashboardGrid_1m5b9_50 {
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

._dashboardCard_1m5b9_62 {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  color: white;
  text-align: center;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

._compact_1m5b9_13 ._dashboardCard_1m5b9_62 {
  padding: 16px;
  min-height: 80px;
}

._dashboardCard_1m5b9_62:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

._dashboardCard_1m5b9_62._active_1m5b9_92 {
  background: var(--dashboard-gradient);
  border-color: var(--dashboard-color);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

._dashboardCard_1m5b9_62._active_1m5b9_92::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  animation: _shimmer_1m5b9_1 2s infinite;
}

@keyframes _shimmer_1m5b9_1 {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

._cardIcon_1m5b9_115 {
  font-size: 2.5rem;
  margin-bottom: 12px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

._compact_1m5b9_13 ._cardIcon_1m5b9_115 {
  font-size: 2rem;
  margin-bottom: 8px;
}

._cardLabel_1m5b9_126 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

._compact_1m5b9_13 ._cardLabel_1m5b9_126 {
  font-size: 0.9rem;
  margin-bottom: 4px;
}

._cardDescription_1m5b9_138 {
  font-size: 0.8rem;
  opacity: 0.9;
  line-height: 1.4;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

._activeIndicator_1m5b9_145 {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

._activeIcon_1m5b9_159 {
  font-size: 0.8rem;
  font-weight: bold;
  color: #10b981;
}

._statusIndicator_1m5b9_165 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
}

._statusDot_1m5b9_176 {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: _pulse_1m5b9_1 2s infinite;
}

._statusText_1m5b9_184 {
  font-weight: 500;
}

/* Responsividade */
@media (max-width: 768px) {
  ._navigation_1m5b9_3 {
    padding: 16px;
    margin-bottom: 16px;
  }

  ._dashboardGrid_1m5b9_50 {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }

  ._dashboardCard_1m5b9_62 {
    padding: 16px;
    min-height: 100px;
  }

  ._cardIcon_1m5b9_115 {
    font-size: 2rem;
  }

  ._cardLabel_1m5b9_126 {
    font-size: 0.9rem;
  }

  ._cardDescription_1m5b9_138 {
    font-size: 0.75rem;
  }

  ._title_1m5b9_24 {
    font-size: 1.3rem;
  }

  ._subtitle_1m5b9_44 {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  ._dashboardGrid_1m5b9_50 {
    grid-template-columns: repeat(2, 1fr);
  }

  ._dashboardCard_1m5b9_62 {
    min-height: 80px;
    padding: 12px;
  }

  ._cardIcon_1m5b9_115 {
    font-size: 1.8rem;
    margin-bottom: 6px;
  }

  ._cardLabel_1m5b9_126 {
    font-size: 0.8rem;
    margin-bottom: 4px;
  }

  ._cardDescription_1m5b9_138 {
    display: none; /* Ocultar descrição em telas muito pequenas */
  }
}
/* 🎨 LAYOUT UNIFICADO DOS DASHBOARDS PREMIUM */

._dashboardLayout_rlevl_3 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Loading State */
._loadingContainer_rlevl_11 {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

/* Error State */
._errorContainer_rlevl_22 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

._errorIcon_rlevl_35 {
  font-size: 4rem;
  margin-bottom: 16px;
}

._errorTitle_rlevl_40 {
  color: #ef4444;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 12px 0;
}

._errorMessage_rlevl_47 {
  color: #6b7280;
  font-size: 1rem;
  margin: 0 0 24px 0;
  max-width: 400px;
  line-height: 1.5;
}

._retryButton_rlevl_55 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

._retryButton_rlevl_55:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

._retryButton_rlevl_55:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Header */
._dashboardHeader_rlevl_78 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

._headerContent_rlevl_88 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}

._titleSection_rlevl_95 {
  display: flex;
  align-items: center;
  gap: 16px;
}

._titleIcon_rlevl_101 {
  font-size: 2.5rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

._titleText_rlevl_106 {
  flex: 1;
}

._title_rlevl_95 {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 4px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

._subtitle_rlevl_121 {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
  font-weight: 400;
}

._headerActions_rlevl_128 {
  display: flex;
  align-items: center;
  gap: 12px;
}

._actionButton_rlevl_134 {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #374151;
}

._actionButton_rlevl_134:hover:not(:disabled) {
  background: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

._refreshButton_rlevl_155 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

._refreshButton_rlevl_155:hover:not(:disabled) {
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

._buttonIcon_rlevl_165 {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

._buttonIcon_rlevl_165._spinning_rlevl_170 {
  animation: _spin_rlevl_170 1s linear infinite;
}

@keyframes _spin_rlevl_170 {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

._buttonText_rlevl_179 {
  font-weight: 500;
}

/* Content */
._dashboardContent_rlevl_184 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 400px;
}

/* Footer */
._dashboardFooter_rlevl_196 {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 16px 24px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

._footerContent_rlevl_205 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  font-size: 0.85rem;
  color: #6b7280;
}

._footerInfo_rlevl_214 {
  display: flex;
  align-items: center;
  gap: 8px;
}

._footerIcon_rlevl_220 {
  font-size: 1rem;
}

._footerText_rlevl_224 {
  font-weight: 500;
}

._footerStatus_rlevl_228 {
  display: flex;
  align-items: center;
  gap: 8px;
}

._statusDot_rlevl_234 {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: _pulse_rlevl_1 2s infinite;
}

@keyframes _pulse_rlevl_1 {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

._statusText_rlevl_247 {
  font-weight: 500;
  color: #10b981;
}

._footerTime_rlevl_252 {
  font-weight: 400;
  font-style: italic;
}

/* Responsividade */
@media (max-width: 768px) {
  ._dashboardLayout_rlevl_3 {
    padding: 12px;
  }

  ._dashboardHeader_rlevl_78 {
    padding: 20px;
    margin-bottom: 20px;
  }

  ._headerContent_rlevl_88 {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  ._titleSection_rlevl_95 {
    width: 100%;
  }

  ._headerActions_rlevl_128 {
    width: 100%;
    justify-content: flex-end;
  }

  ._title_rlevl_95 {
    font-size: 1.75rem;
  }

  ._titleIcon_rlevl_101 {
    font-size: 2rem;
  }

  ._dashboardContent_rlevl_184 {
    padding: 20px;
    margin-bottom: 20px;
  }

  ._footerContent_rlevl_205 {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  ._dashboardLayout_rlevl_3 {
    padding: 8px;
  }

  ._dashboardHeader_rlevl_78 {
    padding: 16px;
  }

  ._title_rlevl_95 {
    font-size: 1.5rem;
  }

  ._titleIcon_rlevl_101 {
    font-size: 1.8rem;
  }

  ._dashboardContent_rlevl_184 {
    padding: 16px;
  }

  ._actionButton_rlevl_134 {
    padding: 8px 12px;
    font-size: 0.8rem;
  }

  ._buttonText_rlevl_179 {
    display: none; /* Mostrar apenas ícones em telas pequenas */
  }
}
/**
 * @file styles.module.css
 * @description Estilos modulares responsivos para o Performance Dashboard
 * @version 4.0.0 - Layout Moderno e Responsivo
 */

/* ========== VARIÁVEIS CSS ========== */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);

  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-light: #e2e8f0;

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
}

/* ========== MULTISENSORY METRICS PANEL ========== */
._metricsPanelRoot_301m6_63 {
  padding: 2rem;
  margin-bottom: 2rem;
  position: relative;
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

._metricsPanelRoot_301m6_63::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
}

._metricsPanelRoot_301m6_63:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

._metricsPanelRoot_301m6_63:hover::before {
  opacity: 1;
}

._metricsHeader_301m6_129 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

._metricsTitle_301m6_147 {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  letter-spacing: -0.025em;
}

._metricsTitle_301m6_147::before {
  content: '🎯';
  font-size: 1.25rem;
  padding: 0.5rem;
  background: var(--primary-gradient);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

._metricsContent_301m6_189 {
  min-height: 400px;
  position: relative;
}

._metricsEmptyState_301m6_199 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
  text-align: center;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border-radius: var(--radius-lg);
  border: 2px dashed var(--border-light);
}

._metricsEmptyState_301m6_199::before {
  content: '📊';
  font-size: 4rem;
  opacity: 0.3;
  margin-bottom: 1rem;
  animation: _pulse_301m6_1 2s infinite;
}

@keyframes _pulse_301m6_1 {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.05); }
}

._metricsChart_301m6_251 {
  margin: 2rem 0;
  height: 400px;
  position: relative;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 1rem;
  box-shadow: var(--shadow-sm);
}

/* ========== LOADING E ESTADOS ========== */
._metricsLoadingOverlay_301m6_273 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: var(--radius-xl);
}

._metricsSpinner_301m6_305 {
  width: 3rem;
  height: 3rem;
  border: 4px solid var(--border-light);
  border-top: 4px solid transparent;
  border-radius: 50%;
  background: conic-gradient(from 0deg, transparent, var(--primary-gradient));
  animation: _spin_301m6_1 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes _spin_301m6_1 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

._metricsLoadingText_301m6_337 {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.875rem;
}

._metricsInfoBox_301m6_349 {
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border-radius: var(--radius-lg);
  margin-top: 1.5rem;
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
}

/* ========== SISTEMA DE TABS MODERNO ========== */
._metricsTabs_301m6_369 {
  display: flex;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: 0.25rem;
  margin-bottom: 2rem;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

._metricsTabs_301m6_369::-webkit-scrollbar {
  display: none;
}

._metricsTab_301m6_369 {
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--text-secondary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

._metricsTab_301m6_369::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

._metricsTab_301m6_369:hover {
  color: var(--text-primary);
  transform: translateY(-1px);
}

._metricsTab_301m6_369._active_301m6_461 {
  color: white;
  background: var(--primary-gradient);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

._metricsTab_301m6_369._active_301m6_461::before {
  opacity: 1;
}

/* ========== GRID DE MÉTRICAS ========== */
._metricsGrid_301m6_485 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

._metricCard_301m6_499 {
  padding: 1.5rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

._metricCard_301m6_499::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
}

._metricCard_301m6_499:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

._metricCard_301m6_499:hover::before {
  opacity: 1;
}

._metricValue_301m6_565 {
  font-size: 2rem;
  font-weight: 800;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

._metricLabel_301m6_585 {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

._metricsButton_301m6_601 {
  padding: 0.75rem 1.5rem;
  background: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

._metricsButton_301m6_601:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

._metricsButton_301m6_601:active {
  transform: translateY(0);
}

._metricsButtonSecondary_301m6_649 {
  padding: 8px 16px;
  background-color: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
}

._metricsButtonSecondary_301m6_649:hover {
  background-color: #f1f5f9;
}

._icon_301m6_677 {
  font-size: 18px;
  margin-right: 8px;
}

._metricsDivider_301m6_687 {
  height: 1px;
  background-color: #e2e8f0;
  margin: 16px 0;
}

@keyframes _spin_301m6_1 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Container principal do dashboard */
._dashboardBase_301m6_711 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  padding: 2rem;
  z-index: 1;
}

/* Header do dashboard */
._dashboardHeader_301m6_733 {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

._dashboardTitle_301m6_753 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #fff, #f0f9ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

._dashboardSubtitle_301m6_781 {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 1.5rem;
  font-weight: 300;
}

/* Grid de conteúdo */
._dashboardContent_301m6_797 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

._dashboardCard_301m6_811 {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

._dashboardCard_301m6_811:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Estatísticas */
._statsGrid_301m6_843 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

._statCard_301m6_857 {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

._statCard_301m6_857:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

._statValue_301m6_885 {
  font-size: 2rem;
  font-weight: bold;
  color: #4ECDC4;
  margin-bottom: 0.5rem;
}

._statLabel_301m6_899 {
  font-size: 0.9rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Gráficos */
._chartsSection_301m6_915 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

._chartContainer_301m6_929 {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 1rem;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

._chartTitle_301m6_943 {
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  color: white;
  text-align: center;
}

._chartWrapper_301m6_957 {
  height: 300px;
  position: relative;
}

/* Controles */
._dashboardControls_301m6_969 {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

._controlButton_301m6_985 {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

._controlButton_301m6_985:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

._controlButton_301m6_985._active_301m6_461 {
  background: linear-gradient(135deg, #10b981, #047857);
  border-color: #10b981;
}

/* Estados */
._loadingContainer_301m6_1029 {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex-direction: column;
  gap: 1rem;
}

._errorContainer_301m6_1047 {
  background: rgba(220, 38, 38, 0.1);
  border: 1px solid rgba(220, 38, 38, 0.3);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  margin: 2rem 0;
}

._errorMessage_301m6_1065 {
  color: #fca5a5;
  margin-bottom: 1rem;
}

._retryButton_301m6_1075 {
  background: #dc2626;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

._retryButton_301m6_1075:hover {
  background: #b91c1c;
}

/* Headers de cards */
._cardHeader_301m6_1105 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

._cardTitle_301m6_1119 {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  margin: 0;
}

._cardIcon_301m6_1133 {
  font-size: 1.5rem;
  opacity: 0.8;
}

/* Responsividade */
@media (max-width: 768px) {
  ._dashboardBase_301m6_711 {
    padding: 1rem;
  }
  
  ._dashboardTitle_301m6_753 {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  ._dashboardContent_301m6_797 {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  ._chartsSection_301m6_915 {
    grid-template-columns: 1fr;
  }
  
  ._statsGrid_301m6_843 {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  ._dashboardControls_301m6_969 {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  ._dashboardBase_301m6_711 {
    padding: 0.5rem;
  }
  
  ._dashboardHeader_301m6_733 {
    padding: 1rem;
  }
  
  ._dashboardTitle_301m6_753 {
    font-size: 1.5rem;
  }
  
  ._dashboardCard_301m6_811 {
    padding: 1rem;
  }
}

/* ========== RESPONSIVIDADE MULTISENSORY PANEL ========== */

/* Tablets e telas médias */
@media (max-width: 1024px) {
  ._metricsPanelRoot_301m6_63 {
    padding: 1.5rem;
  }

  ._metricsGrid_301m6_485 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.25rem;
  }

  ._metricsChart_301m6_251 {
    height: 350px;
  }
}

/* Tablets pequenos */
@media (max-width: 768px) {
  ._metricsPanelRoot_301m6_63 {
    padding: 1.25rem;
    margin-bottom: 1.5rem;
  }

  ._metricsHeader_301m6_129 {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
    text-align: center;
  }

  ._metricsTitle_301m6_147 {
    font-size: 1.25rem;
    justify-content: center;
  }

  ._metricsTabs_301m6_369 {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  ._metricsTab_301m6_369 {
    flex: 1;
    min-width: 120px;
    text-align: center;
  }

  ._metricsGrid_301m6_485 {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
  }

  ._metricsChart_301m6_251 {
    height: 300px;
    margin: 1.5rem 0;
  }

  ._metricsEmptyState_301m6_199 {
    padding: 2rem;
  }
}

/* Dispositivos móveis */
@media (max-width: 480px) {
  ._metricsPanelRoot_301m6_63 {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: var(--radius-lg);
  }

  ._metricsTitle_301m6_147 {
    font-size: 1.125rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  ._metricsTitle_301m6_147::before {
    font-size: 1rem;
    padding: 0.375rem;
  }

  ._metricsTabs_301m6_369 {
    padding: 0.125rem;
    gap: 0.25rem;
  }

  ._metricsTab_301m6_369 {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    min-width: 100px;
  }

  ._metricsGrid_301m6_485 {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  ._metricCard_301m6_499 {
    padding: 1.25rem;
  }

  ._metricValue_301m6_565 {
    font-size: 1.75rem;
  }

  ._metricsChart_301m6_251 {
    height: 250px;
    margin: 1rem 0;
    padding: 0.75rem;
  }

  ._metricsEmptyState_301m6_199 {
    padding: 1.5rem;
  }

  ._metricsEmptyState_301m6_199::before {
    font-size: 3rem;
  }

  ._metricsButton_301m6_601 {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }
}

/* ========== ACESSIBILIDADE ========== */

/* Redução de movimento para acessibilidade */
@media (prefers-reduced-motion: reduce) {
  ._metricsPanelRoot_301m6_63,
  ._metricCard_301m6_499,
  ._metricsTab_301m6_369,
  ._metricsButton_301m6_601 {
    transition: none;
    animation: none;
  }

  ._metricsPanelRoot_301m6_63:hover,
  ._metricCard_301m6_499:hover,
  ._metricsTab_301m6_369:hover,
  ._metricsButton_301m6_601:hover {
    transform: none;
  }

  ._metricsEmptyState_301m6_199::before {
    animation: none;
  }

  ._metricsSpinner_301m6_305 {
    animation: _spin_301m6_1 0.01s linear infinite;
  }
}

/* Alto contraste */
@media (prefers-contrast: high) {
  ._metricsPanelRoot_301m6_63,
  ._metricCard_301m6_499,
  ._metricsInfoBox_301m6_349 {
    border: 2px solid var(--text-primary);
  }

  ._metricsTab_301m6_369._active_301m6_461 {
    border: 2px solid white;
  }

  ._metricsButton_301m6_601 {
    border: 2px solid transparent;
  }
}

/* Tema escuro para multisensory */
@media (prefers-color-scheme: dark) {
  ._metricsPanelRoot_301m6_63 {
    background: rgba(30, 41, 59, 0.9);
    border-color: #334155;
  }

  ._metricsEmptyState_301m6_199 {
    background: rgba(51, 65, 85, 0.6);
    border-color: #475569;
  }

  ._metricCard_301m6_499 {
    background: rgba(51, 65, 85, 0.8);
    border-color: #475569;
  }

  ._metricsInfoBox_301m6_349 {
    background: rgba(51, 65, 85, 0.6);
    border-color: #475569;
  }

  ._metricsTabs_301m6_369 {
    background: rgba(15, 23, 42, 0.8);
  }
}
/**
 * @file PerformanceDashboard.module.css
 * @description Estilos modulares para Dashboard de Performance - Layout Moderno
 * @version 4.0.0
 */

/* ========== VARIÁVEIS CSS ========== */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;

  --border-light: #e2e8f0;
  --border-medium: #cbd5e1;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
}

/* ========== CONTAINER PRINCIPAL ========== */
._dashboardContainer_173xw_71 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: relative;
  overflow-x: hidden;
}

._dashboardContainer_173xw_71::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: var(--primary-gradient);
  opacity: 0.05;
  z-index: 0;
}

/* ========== HEADER MODERNO ========== */
._dashboardHeader_173xw_115 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2.5rem;
  padding: 1.5rem 2rem;
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  position: relative;
  z-index: 2;
  border: 1px solid var(--border-light);
}

._dashboardTitle_173xw_143 {
  font-size: 2rem;
  font-weight: 800;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 1rem;
  letter-spacing: -0.025em;
}

._titleIcon_173xw_165 {
  background: var(--primary-gradient);
  padding: 0.75rem;
  border-radius: var(--radius-lg);
  color: white;
  font-size: 1.5rem;
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ========== CONTROLES MODERNOS ========== */
._dashboardControls_173xw_191 {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

._timeframeSelector_173xw_205 {
  padding: 0.75rem 1.25rem;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
  min-width: 120px;
}

._timeframeSelector_173xw_205:hover {
  border-color: #667eea;
  box-shadow: var(--shadow-md), 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

._timeframeSelector_173xw_205:focus {
  border-color: #667eea;
  box-shadow: var(--shadow-md), 0 0 0 3px rgba(102, 126, 234, 0.2);
  outline: none;
}

._refreshButton_173xw_257 {
  padding: 0.75rem 1.5rem;
  background: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

._refreshButton_173xw_257:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

._refreshButton_173xw_257:active {
  transform: translateY(0);
}

._refreshButton_173xw_257:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* ========== GRID DE MÉTRICAS MODERNO ========== */
._metricsGrid_173xw_321 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2.5rem;
  position: relative;
  z-index: 2;
}

._metricCard_173xw_339 {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-lg);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

._metricCard_173xw_339::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
}

._metricCard_173xw_339:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: rgba(102, 126, 234, 0.2);
}

._metricCard_173xw_339:hover::before {
  opacity: 1;
}
/* ========== ELEMENTOS INTERNOS DOS CARDS ========== */
._metricHeader_173xw_407 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

._metricTitle_173xw_421 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

._metricIcon_173xw_439 {
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease;
}

._metricCard_173xw_339:hover ._metricIcon_173xw_439 {
  transform: scale(1.1);
}

._metricIcon_173xw_439._sessions_173xw_473 {
  background: var(--primary-gradient);
}

._metricIcon_173xw_439._accuracy_173xw_481 {
  background: var(--success-gradient);
}

._metricIcon_173xw_439._time_173xw_205 {
  background: var(--warning-gradient);
}

._metricIcon_173xw_439._completion_173xw_497 {
  background: var(--secondary-gradient);
}

._metricIcon_173xw_439._improvement_173xw_505 {
  background: var(--danger-gradient);
}

._metricValue_173xw_513 {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--text-primary);
  margin: 1rem 0 0.5rem 0;
  line-height: 1;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--text-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

._metricTrend_173xw_539 {
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

._trendPositive_173xw_563 {
  color: #059669;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

._trendNegative_173xw_575 {
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

._trendNeutral_173xw_587 {
  color: var(--text-secondary);
  background: rgba(148, 163, 184, 0.1);
  border: 1px solid rgba(148, 163, 184, 0.2);
}

/* ========== GRID DE GRÁFICOS MODERNO ========== */
._chartsGrid_173xw_601 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 2rem;
  margin-bottom: 2.5rem;
  position: relative;
  z-index: 2;
}

._chartCard_173xw_619 {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

._chartCard_173xw_619::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
}

._chartCard_173xw_619:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  border-color: rgba(102, 126, 234, 0.2);
}

._chartCard_173xw_619:hover::before {
  opacity: 1;
}

._chartTitle_173xw_687 {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  letter-spacing: -0.025em;
}

._chartContainer_173xw_709 {
  position: relative;
  height: 350px;
  padding: 1rem 0;
}

/* ========== SEÇÕES MODERNAS ========== */
._sectionContainer_173xw_723 {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

._sectionContainer_173xw_723:hover {
  box-shadow: var(--shadow-xl);
}

._insightsSection_173xw_757 {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  padding: 2.5rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

/* ========== INSIGHTS MODERNOS ========== */
._insightsTitle_173xw_781 {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--text-primary);
  margin: 0 0 2rem 0;
  display: flex;
  align-items: center;
  gap: 1rem;
  letter-spacing: -0.025em;
}

._insightsGrid_173xw_803 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

._insightCard_173xw_815 {
  background: var(--bg-primary);
  padding: 1.5rem;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-md);
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

._insightCard_173xw_815::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 4px;
  background: var(--primary-gradient);
  transition: width 0.3s ease;
}

._insightCard_173xw_815:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: rgba(102, 126, 234, 0.2);
}

._insightCard_173xw_815:hover::before {
  width: 6px;
}

._insightTitle_173xw_879 {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

._insightContent_173xw_899 {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

._insightContent_173xw_899 p {
  margin: 0.5rem 0;
}

._insightContent_173xw_899 strong {
  color: var(--text-primary);
  font-weight: 600;
}

/* ========== ESTADOS DE LOADING ========== */
._loadingContainer_173xw_931 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  gap: 1.5rem;
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
}

._loadingContainer_173xw_931::before {
  content: '📊';
  font-size: 4rem;
  opacity: 0.3;
  animation: _pulse_173xw_1 2s infinite;
}

@keyframes _pulse_173xw_1 {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.1); }
}

/* ========== RESPONSIVIDADE AVANÇADA ========== */

/* Tablets e telas médias */
@media (max-width: 1024px) {
  ._dashboardContainer_173xw_71 {
    padding: 1.5rem;
  }

  ._metricsGrid_173xw_321 {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.25rem;
  }

  ._chartsGrid_173xw_601 {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }
}

/* Tablets pequenos */
@media (max-width: 768px) {
  ._dashboardContainer_173xw_71 {
    padding: 1rem;
    background: var(--bg-secondary);
  }

  ._dashboardHeader_173xw_115 {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
    padding: 1.25rem;
  }

  ._dashboardControls_173xw_191 {
    justify-content: center;
    flex-wrap: wrap;
  }

  ._dashboardTitle_173xw_143 {
    font-size: 1.75rem;
    text-align: center;
    justify-content: center;
  }

  ._metricsGrid_173xw_321 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }

  ._chartsGrid_173xw_601 {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  ._chartContainer_173xw_709 {
    height: 280px;
  }

  ._insightsGrid_173xw_803 {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  ._metricCard_173xw_339 {
    padding: 1.5rem;
  }

  ._chartCard_173xw_619 {
    padding: 1.5rem;
  }
}

/* Dispositivos móveis */
@media (max-width: 480px) {
  ._dashboardContainer_173xw_71 {
    padding: 0.75rem;
    margin: 0;
  }

  ._dashboardHeader_173xw_115 {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  ._dashboardTitle_173xw_143 {
    font-size: 1.5rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  ._titleIcon_173xw_165 {
    font-size: 1.25rem;
    padding: 0.5rem;
  }

  ._metricValue_173xw_513 {
    font-size: 2rem;
  }

  ._metricCard_173xw_339 {
    padding: 1.25rem;
  }

  ._chartContainer_173xw_709 {
    height: 250px;
  }

  ._chartCard_173xw_619 {
    padding: 1.25rem;
  }

  ._insightsSection_173xw_757 {
    padding: 1.5rem;
  }

  ._timeframeSelector_173xw_205,
  ._refreshButton_173xw_257 {
    padding: 0.625rem 1rem;
    font-size: 0.8rem;
  }
}

/* ========== ANIMAÇÕES AVANÇADAS ========== */
@keyframes _fadeInUp_173xw_1 {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes _slideInLeft_173xw_1 {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes _scaleIn_173xw_1 {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

._dashboardContainer_173xw_71 {
  animation: _fadeInUp_173xw_1 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

._dashboardHeader_173xw_115 {
  animation: _slideInLeft_173xw_1 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

._metricCard_173xw_339 {
  animation: _scaleIn_173xw_1 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  animation-fill-mode: both;
}

._metricCard_173xw_339:nth-child(1) { animation-delay: 0.1s; }
._metricCard_173xw_339:nth-child(2) { animation-delay: 0.2s; }
._metricCard_173xw_339:nth-child(3) { animation-delay: 0.3s; }
._metricCard_173xw_339:nth-child(4) { animation-delay: 0.4s; }
._metricCard_173xw_339:nth-child(5) { animation-delay: 0.5s; }

._chartCard_173xw_619 {
  animation: _fadeInUp_173xw_1 0.7s cubic-bezier(0.4, 0, 0.2, 1);
  animation-fill-mode: both;
}

._chartCard_173xw_619:nth-child(1) { animation-delay: 0.2s; }
._chartCard_173xw_619:nth-child(2) { animation-delay: 0.4s; }

._insightsSection_173xw_757 {
  animation: _fadeInUp_173xw_1 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  animation-delay: 0.6s;
  animation-fill-mode: both;
}

/* ========== ACESSIBILIDADE AVANÇADA ========== */
._refreshButton_173xw_257:focus-visible,
._timeframeSelector_173xw_205:focus-visible {
  outline: 3px solid #667eea;
  outline-offset: 2px;
  box-shadow: 0 0 0 6px rgba(102, 126, 234, 0.2);
}

/* Reduzir movimento para usuários sensíveis */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Alto contraste */
@media (prefers-contrast: high) {
  ._metricCard_173xw_339,
  ._chartCard_173xw_619,
  ._insightsSection_173xw_757 {
    border: 2px solid var(--text-primary);
  }

  ._refreshButton_173xw_257,
  ._timeframeSelector_173xw_205 {
    border: 2px solid var(--text-primary);
  }
}

/* ========== TEMA ESCURO AVANÇADO ========== */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1e293b;
    --bg-secondary: #0f172a;
    --bg-tertiary: #334155;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-light: #334155;
    --border-medium: #475569;
  }

  ._dashboardContainer_173xw_71 {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  }

  ._dashboardContainer_173xw_71::before {
    opacity: 0.1;
  }

  ._metricCard_173xw_339,
  ._chartCard_173xw_619,
  ._insightsSection_173xw_757,
  ._sectionContainer_173xw_723 {
    background: rgba(30, 41, 59, 0.8);
    border-color: var(--border-light);
    backdrop-filter: blur(20px);
  }

  ._insightCard_173xw_815 {
    background: rgba(51, 65, 85, 0.6);
    border-color: var(--border-medium);
  }

  ._loadingContainer_173xw_931 {
    background: rgba(30, 41, 59, 0.9);
  }
}

/* ========== UTILITÁRIOS ========== */
._visuallyHidden_173xw_1503 {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

._skipLink_173xw_1527 {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-gradient);
  color: white;
  padding: 8px;
  border-radius: 4px;
  text-decoration: none;
  z-index: 1000;
  transition: top 0.3s;
}

._skipLink_173xw_1527:focus {
  top: 6px;
}
/* AIChat.module.css - Estilos para o Chat IA */

._chatContainer_xzuhq_5 {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  width: 380px;
  height: 600px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  animation: _slideInRight_xzuhq_1 0.3s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.1);
  /* Prevent page scroll interference */
  contain: layout style paint;
  isolation: isolate;
}

@keyframes _slideInRight_xzuhq_1 {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

/* Header do Chat */
._chatHeader_xzuhq_69 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px 16px 0 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

._chatHeaderInfo_xzuhq_91 {
  display: flex;
  align-items: center;
  gap: 12px;
}

._aiAvatar_xzuhq_103 {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

._chatHeaderText_xzuhq_127 {
  color: white;
}

._chatTitle_xzuhq_135 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: white;
}

._chatStatus_xzuhq_149 {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  opacity: 0.9;
  margin-top: 2px;
}

._statusIndicator_xzuhq_167 {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ff4444;
  animation: _pulse_xzuhq_1 2s infinite;
}

._statusIndicator_xzuhq_167._connected_xzuhq_183 {
  background: #4CAF50;
}

._statusIndicator_xzuhq_167._disconnected_xzuhq_191 {
  background: #ff9800;
}

@keyframes _pulse_xzuhq_1 {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

._closeButton_xzuhq_211 {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

._closeButton_xzuhq_211:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* Área de Mensagens */
._messagesContainer_xzuhq_253 {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  overflow-x: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  gap: 12px;
  /* Ensure isolated scrolling */
  overscroll-behavior: contain;
  scroll-behavior: smooth;
}

._messagesContainer_xzuhq_253::-webkit-scrollbar {
  width: 6px;
}

._messagesContainer_xzuhq_253::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

._messagesContainer_xzuhq_253::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

._messagesContainer_xzuhq_253::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

._messageWrapper_xzuhq_319 {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

._messageWrapper_xzuhq_319._ai_xzuhq_103 {
  align-items: flex-start;
}

._messageWrapper_xzuhq_319._user_xzuhq_339 {
  align-items: flex-end;
}

._messageContent_xzuhq_347 {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  max-width: 85%;
}

._messageWrapper_xzuhq_319._user_xzuhq_339 ._messageContent_xzuhq_347 {
  flex-direction: row-reverse;
}

._messageAvatar_xzuhq_369 {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  flex-shrink: 0;
}

._messageWrapper_xzuhq_319._ai_xzuhq_103 ._messageAvatar_xzuhq_369 {
  background: linear-gradient(135deg, #4CAF50, #45a049);
}

._messageWrapper_xzuhq_319._user_xzuhq_339 ._messageAvatar_xzuhq_369 {
  background: linear-gradient(135deg, #2196F3, #1976D2);
}

._messageText_xzuhq_407 {
  padding: 12px 16px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.4;
  word-wrap: break-word;
}

._messageWrapper_xzuhq_319._ai_xzuhq_103 ._messageText_xzuhq_407 {
  background: #f5f5f5;
  color: #333;
  border-bottom-left-radius: 6px;
}

._messageWrapper_xzuhq_319._user_xzuhq_339 ._messageText_xzuhq_407 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-bottom-right-radius: 6px;
}

._messageTime_xzuhq_447 {
  font-size: 11px;
  color: #666;
  padding: 0 8px;
}

/* Indicador de Digitação */
._typingIndicator_xzuhq_461 {
  padding: 12px 16px;
  background: #f5f5f5;
  border-radius: 18px;
  border-bottom-left-radius: 6px;
  display: flex;
  gap: 4px;
  align-items: center;
}

._typingIndicator_xzuhq_461 span {
  width: 6px;
  height: 6px;
  background: #999;
  border-radius: 50%;
  animation: _typing_xzuhq_461 1.4s infinite ease-in-out;
}

._typingIndicator_xzuhq_461 span:nth-child(2) {
  animation-delay: 0.2s;
}

._typingIndicator_xzuhq_461 span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes _typing_xzuhq_461 {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

/* Input Container */
._inputContainer_xzuhq_537 {
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 0 0 16px 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

._inputWrapper_xzuhq_553 {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

._messageInput_xzuhq_565 {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  resize: none;
  outline: none;
  transition: all 0.2s ease;
  min-height: 40px;
  max-height: 100px;
  /* Prevent scroll issues */
  overflow-y: auto;
  overscroll-behavior: contain;
}

._messageInput_xzuhq_565:focus {
  border-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

._messageInput_xzuhq_565:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

._sendButton_xzuhq_619 {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

._sendButton_xzuhq_619:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

._sendButton_xzuhq_619:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

._inputHint_xzuhq_673 {
  margin-top: 8px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  line-height: 1.3;
}

/* Header Actions */
._headerActions_xzuhq_691 {
  display: flex;
  gap: 8px;
  align-items: center;
}

._expandButton_xzuhq_703 {
  background: none;
  border: none;
  color: #667eea;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

._expandButton_xzuhq_703:hover {
  background-color: rgba(102, 126, 234, 0.1);
}

/* MCP Selector */
._mcpSelector_xzuhq_735 {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-bottom: 1px solid #e2e8f0;
  padding: 12px 16px;
}

._mcpSelectorTitle_xzuhq_747 {
  font-size: 12px;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

._mcpTabs_xzuhq_765 {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

._mcpTab_xzuhq_765 {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 16px;
  background: white;
  color: #4a5568;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

._mcpTab_xzuhq_765:hover {
  border-color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

._mcpTab_xzuhq_765._active_xzuhq_819 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: transparent;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

._mcpIcon_xzuhq_833 {
  font-size: 14px;
}

._mcpName_xzuhq_841 {
  font-weight: 500;
}

/* Chat expandido */
._chatContainer_xzuhq_5._expanded_xzuhq_851 {
  width: 500px;
  height: 700px;
}

._chatContainer_xzuhq_5._expanded_xzuhq_851 ._messagesContainer_xzuhq_253 {
  height: 450px;
}

/* Responsividade */
@media (max-width: 768px) {
  ._chatContainer_xzuhq_5 {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    border-radius: 0;
    transform: none;
  }
  
  ._chatHeader_xzuhq_69 {
    border-radius: 0;
  }
  
  ._inputContainer_xzuhq_537 {
    border-radius: 0;
  }
}

@media (max-width: 480px) {
  ._chatContainer_xzuhq_5 {
    width: 100vw;
    height: 100vh;
  }
  
  ._messagesContainer_xzuhq_253 {
    padding: 12px;
  }
  
  ._inputContainer_xzuhq_537 {
    padding: 12px;
  }
}

/* Estados de hover e focus para acessibilidade */
._closeButton_xzuhq_211:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

._sendButton_xzuhq_619:focus {
  outline: 2px solid rgba(76, 175, 80, 0.5);
  outline-offset: 2px;
}

._messageInput_xzuhq_565:focus {
  outline: none;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  ._messagesContainer_xzuhq_253 {
    background: rgba(30, 30, 30, 0.95);
  }
  
  ._messageWrapper_xzuhq_319._ai_xzuhq_103 ._messageText_xzuhq_407 {
    background: #2a2a2a;
    color: #e0e0e0;
  }
  
  ._typingIndicator_xzuhq_461 {
    background: #2a2a2a;
  }
  
  ._messageInput_xzuhq_565 {
    background: rgba(40, 40, 40, 0.9);
    color: #e0e0e0;
    border-color: rgba(255, 255, 255, 0.1);
  }
}

/* AIBrain integration styles */
._aiBrainBadge_xzuhq_1021 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  font-size: 18px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  animation: _pulseBrain_xzuhq_1 2s infinite;
}

@keyframes _pulseBrain_xzuhq_1 {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

._aiBrainConnected_xzuhq_1061 {
  background-color: #00c851;
}

._aiBrainStatus_xzuhq_1069 {
  margin-left: 6px;
  font-size: 12px;
  color: #00c851;
  font-weight: 500;
}

/* Message with AIBrain enhanced content */
._messageContent_xzuhq_347._aiBrainEnhanced_xzuhq_1085 {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-left: 3px solid #667eea;
}

._aiBrainIcon_xzuhq_1095 {
  display: inline-block;
  margin-right: 6px;
  font-size: 14px;
}
/* IEBrandMetrics.module.css - Estilos para métricas IE Brand */

._metricsContainer_137xb_5 {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Header */
._metricsHeader_137xb_25 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(0, 0, 0, 0.1);
}

._headerInfo_137xb_43 {
  flex: 1;
}

._metricsTitle_137xb_51 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
}

._brandIcon_137xb_71 {
  font-size: 32px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

._metricsSubtitle_137xb_87 {
  margin: 0;
  color: #6c757d;
  font-size: 16px;
  line-height: 1.4;
}

._headerControls_137xb_101 {
  display: flex;
  gap: 12px;
  align-items: center;
}

._timeSelector_137xb_113 {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

._timeSelector_137xb_113:hover {
  border-color: #667eea;
}

._timeSelector_137xb_113:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Seletor de Métricas */
._metricsSelector_137xb_155 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

._metricButton_137xb_169 {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

._metricButton_137xb_169:hover {
  border-color: var(--metric-color);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

._metricButton_137xb_169._active_137xb_213 {
  border-color: var(--metric-color);
  background: linear-gradient(135deg, var(--metric-color)10, var(--metric-color)05);
  color: #2c3e50;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

._metricIcon_137xb_229 {
  font-size: 24px;
  flex-shrink: 0;
}

._metricLabel_137xb_239 {
  font-weight: 600;
}

/* Métricas Principais */
._mainMetrics_137xb_249 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

._scoreCard_137xb_263 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  padding: 24px;
  color: white;
  position: relative;
  overflow: hidden;
}

._scoreCard_137xb_263::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

._scoreHeader_137xb_303 {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

._scoreIcon_137xb_317 {
  font-size: 32px;
}

._scoreTitle_137xb_325 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
}

._scoreDescription_137xb_337 {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

._scoreValue_137xb_349 {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 12px;
}

._scoreNumber_137xb_363 {
  font-size: 48px;
  font-weight: 700;
  line-height: 1;
}

._scoreUnit_137xb_375 {
  font-size: 24px;
  opacity: 0.8;
}

._scoreTrend_137xb_385 {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

._scoreTrend_137xb_385._positive_137xb_405 {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
}

._scoreTrend_137xb_385._stable_137xb_415 {
  background: rgba(255, 152, 0, 0.2);
  color: #FF9800;
}

._scoreTrend_137xb_385._improving_137xb_425 {
  background: rgba(33, 150, 243, 0.2);
  color: #2196F3;
}

._factorsCard_137xb_435 {
  background: white;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e9ecef;
}

._factorsTitle_137xb_449 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

._factorsList_137xb_463 {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

._factorItem_137xb_475 {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

._factorIcon_137xb_489 {
  width: 20px;
  height: 20px;
  background: #4CAF50;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

._factorText_137xb_517 {
  font-size: 14px;
  color: #495057;
  line-height: 1.4;
}

/* Gráficos */
._chartsGrid_137xb_531 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

._chartCard_137xb_545 {
  background: white;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e9ecef;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

._chartTitle_137xb_561 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

._chartContainer_137xb_581 {
  height: 300px;
  position: relative;
}

/* Recomendações */
._recommendationsSection_137xb_593 {
  margin-bottom: 32px;
}

._recommendationsTitle_137xb_601 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

._recommendationsList_137xb_621 {
  display: grid;
  gap: 16px;
}

._recommendationCard_137xb_631 {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  border-left: 4px solid #667eea;
  transition: all 0.2s ease;
}

._recommendationCard_137xb_631:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

._recommendationIcon_137xb_665 {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

._recommendationContent_137xb_689 {
  flex: 1;
}

._recommendationContent_137xb_689 p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: #495057;
}

/* Footer IE Brand */
._brandFooter_137xb_713 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-top: 2px solid rgba(0, 0, 0, 0.1);
  margin-top: 32px;
}

._brandInfo_137xb_731 {
  display: flex;
  align-items: center;
  gap: 16px;
}

._brandLogo_137xb_743 {
  font-size: 32px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

._brandInfo_137xb_731 strong {
  display: block;
  font-size: 16px;
  color: #2c3e50;
  margin-bottom: 4px;
}

._brandInfo_137xb_731 p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
  line-height: 1.3;
}

._mcpStatus_137xb_787 {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

._mcpIndicator_137xb_811 {
  font-size: 14px;
}

/* Responsividade */
@media (max-width: 1200px) {
  ._chartsGrid_137xb_531 {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  ._metricsContainer_137xb_5 {
    padding: 16px;
  }
  
  ._metricsHeader_137xb_25 {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  ._metricsSelector_137xb_155 {
    grid-template-columns: 1fr;
  }
  
  ._mainMetrics_137xb_249 {
    grid-template-columns: 1fr;
  }
  
  ._chartsGrid_137xb_531 {
    grid-template-columns: 1fr;
  }
  
  ._chartContainer_137xb_581 {
    height: 250px;
  }
  
  ._brandFooter_137xb_713 {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  ._metricsTitle_137xb_51 {
    font-size: 24px;
  }
  
  ._scoreNumber_137xb_363 {
    font-size: 36px;
  }
  
  ._scoreUnit_137xb_375 {
    font-size: 18px;
  }
  
  ._chartContainer_137xb_581 {
    height: 200px;
  }
}

/* Estados para acessibilidade */
._metricButton_137xb_169:focus {
  outline: 3px solid rgba(102, 126, 234, 0.3);
  outline-offset: 2px;
}

._timeSelector_137xb_113:focus {
  outline: 3px solid rgba(102, 126, 234, 0.3);
  outline-offset: 2px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  ._metricsContainer_137xb_5 {
    background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  ._metricsTitle_137xb_51 {
    color: #e0e0e0;
  }
  
  ._metricsSubtitle_137xb_87 {
    color: #a0a0a0;
  }
  
  ._metricButton_137xb_169 {
    background: #333;
    border-color: #444;
    color: #e0e0e0;
  }
  
  ._factorsCard_137xb_435,
  ._chartCard_137xb_545,
  ._recommendationCard_137xb_631 {
    background: #333;
    border-color: #444;
  }
  
  ._factorsTitle_137xb_449,
  ._chartTitle_137xb_561,
  ._recommendationsTitle_137xb_601 {
    color: #e0e0e0;
  }
  
  ._factorText_137xb_517,
  ._recommendationContent_137xb_689 p {
    color: #c0c0c0;
  }
}
/* MCPIntegration.module.css - Estilos para integração MCP */

._mcpContainer_1uhai_5 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  color: white;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Header */
._mcpHeader_1uhai_27 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

._mcpTitle_1uhai_45 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

._mcpIcon_1uhai_63 {
  font-size: 24px;
}

._statusBadge_1uhai_71 {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

._statusIcon_1uhai_91 {
  font-size: 12px;
}

._statusText_1uhai_99 {
  font-size: 12px;
  font-weight: 600;
}

/* Seções */
._configSection_1uhai_111,
._capabilitiesSection_1uhai_113,
._resultsSection_1uhai_115,
._instructionsSection_1uhai_117 {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

._sectionTitle_1uhai_135 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Formulário de Configuração */
._configForm_1uhai_155 {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

._inputGroup_1uhai_167 {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

._inputLabel_1uhai_179 {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

._configInput_1uhai_191 {
  padding: 12px 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
}

._configInput_1uhai_191:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

._configInput_1uhai_191::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

._checkboxGroup_1uhai_235 {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

._checkboxLabel_1uhai_249 {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
}

._checkbox_1uhai_235 {
  width: 18px;
  height: 18px;
  accent-color: white;
}

._configActions_1uhai_277 {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

._saveButton_1uhai_289,
._testButton_1uhai_291,
._testMessageButton_1uhai_293 {
  padding: 10px 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

._saveButton_1uhai_289:hover:not(:disabled),
._testButton_1uhai_291:hover:not(:disabled),
._testMessageButton_1uhai_293:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

._saveButton_1uhai_289:disabled,
._testButton_1uhai_291:disabled,
._testMessageButton_1uhai_293:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Capacidades */
._capabilitiesList_1uhai_353 {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

._capabilityItem_1uhai_365 {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

._capabilityIcon_1uhai_379 {
  width: 20px;
  height: 20px;
  background: rgba(76, 175, 80, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

._capabilityText_1uhai_405 {
  font-size: 14px;
  line-height: 1.4;
}

/* Resultados */
._successResults_1uhai_417,
._errorResults_1uhai_419 {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

._resultItem_1uhai_431 {
  padding: 8px 0;
  font-size: 14px;
  line-height: 1.4;
}

._errorResults_1uhai_419 {
  color: #ffcdd2;
}

._testMessageResult_1uhai_451 {
  margin-top: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 4px solid #4CAF50;
}

._testMessageResult_1uhai_451 h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

._testMessageResult_1uhai_451 p {
  margin: 0 0 12px 0;
  font-size: 14px;
  line-height: 1.4;
}

._responseMetadata_1uhai_491 {
  display: flex;
  gap: 16px;
  font-size: 12px;
  opacity: 0.8;
}

/* Instruções */
._instructionsList_1uhai_507 {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

._instructionStep_1uhai_519 {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

._stepNumber_1uhai_531 {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  flex-shrink: 0;
}

._stepContent_1uhai_557 {
  flex: 1;
}

._stepContent_1uhai_557 strong {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
}

._stepContent_1uhai_557 p {
  margin: 0;
  font-size: 13px;
  line-height: 1.4;
  opacity: 0.9;
}

/* Footer */
._statusFooter_1uhai_593 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: 20px;
}

._integrationInfo_1uhai_611 {
  display: flex;
  align-items: center;
  gap: 12px;
}

._integrationIcon_1uhai_623 {
  font-size: 24px;
}

._integrationInfo_1uhai_611 strong {
  display: block;
  font-size: 14px;
  margin-bottom: 2px;
}

._integrationInfo_1uhai_611 p {
  margin: 0;
  font-size: 12px;
  opacity: 0.8;
}

._loadingIndicator_1uhai_655 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

._spinner_1uhai_669 {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: _spin_1uhai_669 1s linear infinite;
}

@keyframes _spin_1uhai_669 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsividade */
@media (max-width: 768px) {
  ._mcpContainer_1uhai_5 {
    padding: 16px;
  }
  
  ._mcpHeader_1uhai_27 {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  ._configActions_1uhai_277 {
    flex-direction: column;
  }
  
  ._statusFooter_1uhai_593 {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
  
  ._responseMetadata_1uhai_491 {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  ._mcpTitle_1uhai_45 {
    font-size: 18px;
  }
  
  ._configInput_1uhai_191 {
    padding: 10px 12px;
    font-size: 13px;
  }
  
  ._saveButton_1uhai_289,
  ._testButton_1uhai_291,
  ._testMessageButton_1uhai_293 {
    padding: 8px 16px;
    font-size: 13px;
  }
}

/* Estados para acessibilidade */
._configInput_1uhai_191:focus,
._saveButton_1uhai_289:focus,
._testButton_1uhai_291:focus,
._testMessageButton_1uhai_293:focus {
  outline: 3px solid rgba(255, 255, 255, 0.3);
  outline-offset: 2px;
}

._checkboxLabel_1uhai_249:focus-within {
  outline: 2px solid rgba(255, 255, 255, 0.3);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Animações */
._mcpContainer_1uhai_5 {
  animation: _slideInUp_1uhai_1 0.3s ease-out;
}

@keyframes _slideInUp_1uhai_1 {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

._successResults_1uhai_417 {
  animation: _fadeIn_1uhai_1 0.5s ease-out;
}

._testMessageResult_1uhai_451 {
  animation: _slideInDown_1uhai_1 0.3s ease-out;
}

@keyframes _fadeIn_1uhai_1 {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes _slideInDown_1uhai_1 {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dark mode adicional (o componente já é escuro por padrão) */
@media (prefers-color-scheme: light) {
  ._mcpContainer_1uhai_5 {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1a1a1a;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
  
  ._configSection_1uhai_111,
  ._capabilitiesSection_1uhai_113,
  ._resultsSection_1uhai_115,
  ._instructionsSection_1uhai_117 {
    background: rgba(255, 255, 255, 0.7);
    color: #1a1a1a;
  }
  
  ._configInput_1uhai_191 {
    background: rgba(255, 255, 255, 0.8);
    color: #1a1a1a;
    border-color: rgba(0, 0, 0, 0.2);
  }
  
  ._configInput_1uhai_191::placeholder {
    color: rgba(0, 0, 0, 0.5);
  }
  
  ._saveButton_1uhai_289,
  ._testButton_1uhai_291,
  ._testMessageButton_1uhai_293 {
    background: rgba(102, 126, 234, 0.1);
    color: #1a1a1a;
    border-color: rgba(102, 126, 234, 0.3);
  }
}

/* Environment Variables Info */
._envInfo_1uhai_971 {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 1px solid #cbd5e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

._envStatus_1uhai_987 {
  margin-bottom: 16px;
}

._envList_1uhai_995 {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

._envItem_1uhai_1009 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

._envItem_1uhai_1009._envSet_1uhai_1029 {
  background-color: rgba(72, 187, 120, 0.1);
  border-left: 3px solid #48bb78;
}

._envItem_1uhai_1009._envNotSet_1uhai_1039 {
  background-color: rgba(245, 101, 101, 0.1);
  border-left: 3px solid #f56565;
}

._envInstructions_1uhai_1049 {
  background-color: rgba(102, 126, 234, 0.05);
  border-left: 4px solid #667eea;
  padding: 12px;
  border-radius: 4px;
}

._envInstructions_1uhai_1049 p {
  margin: 0 0 8px 0;
  font-weight: 600;
  color: #2d3748;
}

._envInstructions_1uhai_1049 ol {
  margin: 0;
  padding-left: 20px;
}

._envInstructions_1uhai_1049 li {
  margin-bottom: 4px;
  font-size: 14px;
  color: #4a5568;
}

._envInstructions_1uhai_1049 code {
  background-color: #2d3748;
  color: #e2e8f0;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

._envNote_1uhai_1115 {
  color: #667eea;
  font-style: italic;
  margin-top: 4px;
  display: block;
}
/**
 * @file UnifiedDashboard.module.css
 * @description Estilos para Dashboard Unificado - Todos os dashboards integrados
 * @version 3.0.0
 */

/* Container principal */
._unifiedContainer_1shsg_15 {
  background: #f8fafc;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 24px;
}

/* Tabs dos Dashboards */
._dashboardTabs_1shsg_33 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 0;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

._dashboardTabs_1shsg_33::-webkit-scrollbar {
  display: none;
}

._tabsContainer_1shsg_57 {
  display: flex;
  min-width: max-content;
  padding: 12px 16px;
  gap: 8px;
}

._dashboardTab_1shsg_33 {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.15);
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  backdrop-filter: blur(10px);
}

._dashboardTab_1shsg_33:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

._dashboardTab_1shsg_33._active_1shsg_115 {
  background: white;
  color: var(--tab-color, #667eea);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

._tabIcon_1shsg_127 {
  font-size: 16px;
}

._tabTitle_1shsg_135 {
  font-weight: 600;
}

/* Seção de Filtros */
._filtersSection_1shsg_145 {
  display: flex;
  gap: 24px;
  padding: 20px 24px;
  background: white;
  border-bottom: 1px solid #e2e8f0;
  flex-wrap: wrap;
}

._filterGroup_1shsg_163 {
  display: flex;
  align-items: center;
  gap: 8px;
}

._filterLabel_1shsg_175 {
  font-size: 14px;
  font-weight: 600;
  color: #4a5568;
  min-width: max-content;
}

._filterSelect_1shsg_189 {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

._filterSelect_1shsg_189:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Conteúdo do Dashboard */
._dashboardContent_1shsg_225 {
  padding: 24px;
  background: white;
}

._contentHeader_1shsg_235 {
  margin-bottom: 24px;
}

._contentTitle_1shsg_243 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
}

._contentIcon_1shsg_263 {
  font-size: 28px;
}

._contentDescription_1shsg_271 {
  margin: 0;
  font-size: 16px;
  color: #64748b;
}

._contentBody_1shsg_283 {
  min-height: 400px;
}

/* Overview Content */
._overviewContent_1shsg_293 {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

._metricsGrid_1shsg_305 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

._metricCard_1shsg_317 {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

._metricCard_1shsg_317:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

._metricIcon_1shsg_345 {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}

._metricValue_1shsg_357 {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 4px;
}

._metricLabel_1shsg_371 {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* Key Metrics Section */
._keyMetricsSection_1shsg_385 {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
}

._sectionTitle_1shsg_399 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  display: flex;
  align-items: center;
  gap: 8px;
}

._keyMetricsList_1shsg_419 {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

._keyMetricItem_1shsg_431 {
  display: grid;
  grid-template-columns: 2fr 3fr auto auto;
  gap: 16px;
  align-items: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
}

._metricName_1shsg_451 {
  font-weight: 600;
  color: #2d3748;
}

._metricProgress_1shsg_461 {
  position: relative;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

._progressBar_1shsg_477 {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: width 0.3s ease;
}

._metricValueText_1shsg_491 {
  font-weight: 600;
  color: #4a5568;
  font-size: 14px;
}

._trendIcon_1shsg_503 {
  font-size: 16px;
}

/* Behavioral Content */
._behavioralContent_1shsg_513 {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

._patternsSection_1shsg_525,
._adaptationsSection_1shsg_527 {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
}

._patternsList_1shsg_541 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

._patternCard_1shsg_553 {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 16px;
}

._patternType_1shsg_567 {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

._patternDetails_1shsg_579 {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #64748b;
}

._adaptationsList_1shsg_593 {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

._adaptationItem_1shsg_605 {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

._adaptationIcon_1shsg_625 {
  font-size: 16px;
  flex-shrink: 0;
}

._adaptationText_1shsg_635 {
  color: #4a5568;
  line-height: 1.5;
}

/* Games Content */
._gamesContent_1shsg_647 {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

._gamesGrid_1shsg_659 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

._gameStatsCard_1shsg_671 {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
}

._gameStatsCard_1shsg_671 h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
}

._favoriteGamesList_1shsg_699 {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

._favoriteGame_1shsg_699 {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f8fafc;
  border-radius: 6px;
}

._gameIcon_1shsg_729 {
  font-size: 16px;
}

._difficultyInfo_1shsg_737,
._achievementsInfo_1shsg_739 {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 14px;
  color: #4a5568;
}

/* Therapeutic Content */
._therapeuticContent_1shsg_757 {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

._goalsSection_1shsg_769,
._interventionsSection_1shsg_771 {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
}

._goalsList_1shsg_785,
._interventionsList_1shsg_787 {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

._goalItem_1shsg_799,
._interventionItem_1shsg_801 {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #48bb78;
}

._goalIcon_1shsg_821,
._interventionIcon_1shsg_823 {
  font-size: 16px;
  flex-shrink: 0;
}

._goalText_1shsg_833,
._interventionText_1shsg_835 {
  color: #4a5568;
  line-height: 1.5;
}

/* Progress Content */
._progressContent_1shsg_847 {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

._growthSection_1shsg_859,
._milestonesSection_1shsg_861 {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
}

._growthBars_1shsg_875 {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

._growthBar_1shsg_875 {
  display: grid;
  grid-template-columns: 1fr 3fr auto;
  gap: 16px;
  align-items: center;
}

._skillName_1shsg_901 {
  font-weight: 600;
  color: #2d3748;
}

._growthBarContainer_1shsg_911 {
  position: relative;
  height: 12px;
  background: #e2e8f0;
  border-radius: 6px;
  overflow: hidden;
}

._growthBarFill_1shsg_927 {
  height: 100%;
  background: linear-gradient(90deg, #48bb78, #38a169);
  border-radius: 6px;
  transition: width 0.3s ease;
}

._growthValue_1shsg_941 {
  font-weight: 600;
  color: #48bb78;
  font-size: 14px;
}

._milestonesList_1shsg_953 {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

._milestoneItem_1shsg_965 {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 8px;
  border-left: 4px solid #ed8936;
}

._milestoneIcon_1shsg_985 {
  font-size: 24px;
  flex-shrink: 0;
}

._milestoneInfo_1shsg_995 {
  flex: 1;
}

._milestoneSkill_1shsg_1003 {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4px;
}

._milestoneDetails_1shsg_1015 {
  font-size: 14px;
  color: #64748b;
}

/* Sensory Content */
._sensoryContent_1shsg_1027 {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

._sensoryProfile_1shsg_1039,
._strategiesSection_1shsg_1041 {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
}

._sensoryGrid_1shsg_1055 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

._sensoryItem_1shsg_1067 {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

._sensoryName_1shsg_1083 {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

._sensoryLevel_1shsg_1095 {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

._sensoryLevel_1shsg_1095._hiperresponsivo_1shsg_1111 {
  background: rgba(245, 101, 101, 0.1);
  color: #c53030;
}

._sensoryLevel_1shsg_1095._típico_1shsg_1121 {
  background: rgba(72, 187, 120, 0.1);
  color: #2f855a;
}

._sensoryLevel_1shsg_1095._hiporesponsivo_1shsg_1131 {
  background: rgba(237, 137, 54, 0.1);
  color: #c05621;
}

._sensoryLevel_1shsg_1095._buscasensorial_1shsg_1141 {
  background: rgba(159, 122, 234, 0.1);
  color: #6b46c1;
}

._strategiesList_1shsg_1151 {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

._strategyItem_1shsg_1163 {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

._strategyIcon_1shsg_1183 {
  font-size: 16px;
  flex-shrink: 0;
}

._strategyText_1shsg_1193 {
  color: #4a5568;
  line-height: 1.5;
}

/* Responsividade */
@media (max-width: 768px) {
  ._dashboardTabs_1shsg_33 {
    padding: 8px;
  }
  
  ._tabsContainer_1shsg_57 {
    padding: 8px;
    gap: 4px;
  }
  
  ._dashboardTab_1shsg_33 {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  ._filtersSection_1shsg_145 {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }
  
  ._filterGroup_1shsg_163 {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  ._dashboardContent_1shsg_225 {
    padding: 16px;
  }
  
  ._metricsGrid_1shsg_305 {
    grid-template-columns: 1fr;
  }
  
  ._keyMetricItem_1shsg_431 {
    grid-template-columns: 1fr;
    gap: 8px;
    text-align: left;
  }
  
  ._gamesGrid_1shsg_659 {
    grid-template-columns: 1fr;
  }
  
  ._sensoryGrid_1shsg_1055 {
    grid-template-columns: 1fr;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  ._unifiedContainer_1shsg_15 {
    background: #1a202c;
    color: #e2e8f0;
  }
  
  ._filtersSection_1shsg_145,
  ._dashboardContent_1shsg_225 {
    background: #2d3748;
  }
  
  ._metricCard_1shsg_317,
  ._gameStatsCard_1shsg_671,
  ._goalsSection_1shsg_769,
  ._interventionsSection_1shsg_771,
  ._growthSection_1shsg_859,
  ._milestonesSection_1shsg_861,
  ._sensoryProfile_1shsg_1039,
  ._strategiesSection_1shsg_1041 {
    background: #4a5568;
    border-color: #718096;
  }
  
  ._contentTitle_1shsg_243,
  ._sectionTitle_1shsg_399 {
    color: #e2e8f0;
  }
}
/**
 * @file AdvancedAIReport.module.css
 * @description Estilos modulares para Dashboard de Relatório de IA Avançado
 * @version 3.0.0
 */

/* Container principal do dashboard */
._dashboardContainer_11avn_15 {
  padding: 24px;
  background-color: #f8fafc;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

/* Loading e Error States */
._loadingContainer_11avn_37 {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background-color: #f8fafc;
  border-radius: 12px;
}

._errorState_11avn_55 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  text-align: center;
  background-color: #f8fafc;
  border-radius: 12px;
  color: #e53e3e;
}

/* Header do dashboard */
._dashboardHeader_11avn_81 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
  gap: 20px;
}

._headerLeft_11avn_101 {
  flex: 1;
}

._dashboardTitle_11avn_109 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

._dashboardSubtitle_11avn_137 {
  margin: 0;
  font-size: 16px;
  color: #64748b;
  line-height: 1.4;
}

._titleIcon_11avn_151 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 8px;
  border-radius: 8px;
  color: white;
  font-size: 20px;
}

/* Controles do dashboard */
._dashboardControls_11avn_169 {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

._analysisSelector_11avn_183,
._timeframeSelector_11avn_185,
._chatButton_11avn_187,
._mcpButton_11avn_189,
._refreshButton_11avn_191 {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

._analysisSelector_11avn_183:hover,
._timeframeSelector_11avn_185:hover,
._chatButton_11avn_187:hover,
._mcpButton_11avn_189:hover,
._refreshButton_11avn_191:hover {
  border-color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}

._analysisSelector_11avn_183:focus,
._timeframeSelector_11avn_185:focus,
._chatButton_11avn_187:focus,
._mcpButton_11avn_189:focus,
._refreshButton_11avn_191:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

._chatButton_11avn_187._active_11avn_253,
._mcpButton_11avn_189._active_11avn_253 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: transparent;
}

/* Status Bar */
._statusBar_11avn_269 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  padding: 12px 20px;
  border-radius: 8px;
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
}

._statusItem_11avn_291 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #64748b;
}

._statusIcon_11avn_307 {
  font-size: 16px;
}

._statusIcon_11avn_307._connected_11avn_315 {
  color: #10b981;
}

._statusIcon_11avn_307._disconnected_11avn_323 {
  color: #ef4444;
}

/* Seções dos componentes */
._mcpSection_11avn_333 {
  margin-bottom: 32px;
}

._ieBrandSection_11avn_341 {
  margin-bottom: 32px;
}

._aiChatComponent_11avn_349 {
  z-index: 1001;
}

._analysisSelector_11avn_183:hover,
._timeframeSelector_11avn_185:hover,
._analysisSelector_11avn_183:focus,
._timeframeSelector_11avn_185:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  outline: none;
}

._refreshButton_11avn_191 {
  padding: 8px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

._refreshButton_11avn_191:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Dashboard Type Controls */
._dashboardTypeControls_11avn_417 {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 16px;
}

._typeButton_11avn_431 {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 14px;
  border-radius: 8px;
  border: none;
  background: #f3f4f6;
  color: #4b5563;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

._typeButton_11avn_431:hover {
  background: #e5e7eb;
}

._typeButton_11avn_431._active_11avn_253 {
  background: #3b82f6;
  color: white;
}

._typeIcon_11avn_479 {
  font-size: 18px;
}

/* Dashboard Mode Controls */
._dashboardModeControls_11avn_489 {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
}

._modeButton_11avn_509 {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 6px;
  border: none;
  background: #f3f4f6;
  color: #4b5563;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

._modeButton_11avn_509:hover {
  background: #e5e7eb;
}

._modeButton_11avn_509._active_11avn_253 {
  background: #4b5563;
  color: white;
}

._modeIcon_11avn_557 {
  font-size: 16px;
}

/* Dashboard Container and Placeholders */
._unifiedDashboardWrapper_11avn_567 {
  margin-bottom: 24px;
}

._dashboardContainer_11avn_15 {
  background: #ffffff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

._dashboardTitle_11avn_109 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px;
  color: #111827;
}

._dashboardInfo_11avn_605 {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 16px;
}

._dashboardPlaceholder_11avn_617 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: #f9fafb;
  border-radius: 8px;
  text-align: center;
}

._placeholderIcon_11avn_639 {
  font-size: 32px;
  margin-bottom: 12px;
}

._dashboardComponent_11avn_649 {
  margin-top: 16px;
}

/* Grid de métricas */
._metricsGrid_11avn_659 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

._metricCard_11avn_673 {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

._metricCard_11avn_673:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

._metricHeader_11avn_707 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

._metricTitle_11avn_721 {
  font-size: 14px;
  font-weight: 500;
  color: #718096;
  margin: 0;
}

._metricIcon_11avn_735 {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

._metricIcon_11avn_735._style_11avn_757 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

._metricIcon_11avn_735._strengths_11avn_765 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

._metricIcon_11avn_735._milestone_11avn_773 {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

._metricIcon_11avn_735._recommendations_11avn_781 {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

._metricValue_11avn_789 {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
  line-height: 1;
}

._metricTrend_11avn_805 {
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

._metricTrend_11avn_805._trendPositive_11avn_821 {
  color: #38a169;
}

/* Grid de gráficos */
._chartsGrid_11avn_831 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

._chartCard_11avn_845 {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

._chartCard_11avn_845:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

._chartTitle_11avn_875 {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

._chartContainer_11avn_895 {
  height: 300px;
  position: relative;
}

/* Seção de Insights */
._insightsSection_11avn_907 {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

._insightsTitle_11avn_925 {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

._insightsGrid_11avn_945 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

._insightCard_11avn_957 {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
}

._insightCard_11avn_957:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

._insightTitle_11avn_985 {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

._insightContent_11avn_1005 {
  color: #4a5568;
  line-height: 1.6;
}

._insightContent_11avn_1005 p {
  margin: 8px 0;
  font-size: 14px;
}

/* Seção de Recomendações */
._recommendationsSection_11avn_1027 {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

._recommendationsTitle_11avn_1045 {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

._recommendationsGrid_11avn_1065 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 16px;
}

._recommendationCard_11avn_1077 {
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border: 1px solid #feb2b2;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  transition: all 0.3s ease;
}

._recommendationCard_11avn_1077:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #f56565;
}

._recommendationIcon_11avn_1111 {
  font-size: 20px;
  color: #e53e3e;
  flex-shrink: 0;
}

._recommendationContent_11avn_1123 {
  flex: 1;
}

._recommendationContent_11avn_1123 p {
  margin: 0;
  color: #2d3748;
  font-size: 14px;
  line-height: 1.5;
}

/* Seção de Insights da IA */
._aiInsightsSection_11avn_1147 {
  background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
  border: 1px solid #81e6d9;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

._aiInsightsTitle_11avn_1165 {
  font-size: 20px;
  font-weight: 600;
  color: #234e52;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

._aiInsightsGrid_11avn_1185 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

._aiInsightCard_11avn_1197 {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid #81e6d9;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  transition: all 0.3s ease;
}

._aiInsightCard_11avn_1197:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #4fd1c7;
  background: rgba(255, 255, 255, 0.9);
}

._aiInsightIcon_11avn_1233 {
  font-size: 20px;
  color: #319795;
  flex-shrink: 0;
}

._aiInsightContent_11avn_1245 {
  flex: 1;
}

._aiInsightContent_11avn_1245 p {
  margin: 0;
  color: #234e52;
  font-size: 14px;
  line-height: 1.5;
}

/* Responsividade */
@media (max-width: 768px) {
  ._dashboardContainer_11avn_15 {
    padding: 16px;
  }

  ._dashboardHeader_11avn_81 {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  ._dashboardControls_11avn_169 {
    flex-wrap: wrap;
    justify-content: flex-start;
  }

  ._metricsGrid_11avn_659 {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  ._chartsGrid_11avn_831 {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  ._insightsGrid_11avn_945,
  ._recommendationsGrid_11avn_1065,
  ._aiInsightsGrid_11avn_1185 {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  ._chartContainer_11avn_895 {
    height: 250px;
  }

  ._dashboardTitle_11avn_109 {
    font-size: 24px;
  }

  ._metricValue_11avn_789 {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  ._dashboardContainer_11avn_15 {
    padding: 12px;
  }

  ._chartsGrid_11avn_831 {
    grid-template-columns: 1fr;
  }

  ._chartContainer_11avn_895 {
    height: 200px;
  }

  ._metricCard_11avn_673 {
    padding: 16px;
  }

  ._insightsSection_11avn_907,
  ._recommendationsSection_11avn_1027,
  ._aiInsightsSection_11avn_1147 {
    padding: 16px;
  }
}

/* ========== ERROR BOUNDARY STYLES ========== */
._errorBoundary_11avn_1411 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border: 2px solid #fca5a5;
  border-radius: 1rem;
  margin: 2rem 0;
  text-align: center;
}

._errorIcon_11avn_1437 {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: _shake_11avn_1 0.5s ease-in-out infinite alternate;
}

@keyframes _shake_11avn_1 {
  0% { transform: translateX(0); }
  100% { transform: translateX(5px); }
}

._errorTitle_11avn_1459 {
  color: #dc2626;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

._errorMessage_11avn_1473 {
  color: #7f1d1d;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  max-width: 500px;
}

._errorDetails_11avn_1489 {
  background: #fef2f2;
  border: 1px solid #fca5a5;
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
  text-align: left;
  max-width: 600px;
  width: 100%;
}

._errorStack_11avn_1511 {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  color: #991b1b;
  white-space: pre-wrap;
  overflow-x: auto;
}

._errorRetryButton_11avn_1527 {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

._errorRetryButton_11avn_1527:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}
/**
 * @file NeuropedagogicalDashboard.module.css
 * @description Estilos modulares para Dashboard Neuropedagógico Premium
 * @version 3.0.0
 */

/* Container principal do dashboard */
._dashboardContainer_lbydi_15 {
  padding: 24px;
  background-color: #f8fafc;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

/* Header do dashboard */
._dashboardHeader_lbydi_37 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
}

._dashboardTitle_lbydi_55 {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

._titleIcon_lbydi_75 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 8px;
  border-radius: 8px;
  color: white;
  font-size: 20px;
}

/* Controles do dashboard */
._dashboardControls_lbydi_93 {
  display: flex;
  gap: 12px;
  align-items: center;
}

._timeframeSelector_lbydi_105 {
  padding: 8px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background-color: white;
  font-size: 14px;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.2s ease;
}

._timeframeSelector_lbydi_105:hover {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

._timeframeSelector_lbydi_105:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

._refreshButton_lbydi_149 {
  padding: 8px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

._refreshButton_lbydi_149:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Grid de métricas */
._metricsGrid_lbydi_191 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

._metricCard_lbydi_205 {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

._metricCard_lbydi_205:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

._metricCard_lbydi_205::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

._metricHeader_lbydi_257 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

._metricTitle_lbydi_271 {
  font-size: 14px;
  font-weight: 600;
  color: #4a5568;
  margin: 0;
}

._metricIcon_lbydi_285 {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

._metricValue_lbydi_307 {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin: 8px 0;
  line-height: 1;
}

._metricTrend_lbydi_323 {
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

._trendPositive_lbydi_339 {
  color: #48bb78;
}

._trendNegative_lbydi_347 {
  color: #f56565;
}

._trendNeutral_lbydi_355 {
  color: #718096;
}

/* Grid de gráficos */
._chartsGrid_lbydi_365 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

._chartCard_lbydi_379 {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

._chartTitle_lbydi_395 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 16px 0;
}

._chartContainer_lbydi_409 {
  position: relative;
  height: 300px;
}

/* Seção de análise */
._analysisSection_lbydi_421 {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

._analysisTitle_lbydi_439 {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

._analysisGrid_lbydi_459 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

._analysisCard_lbydi_471 {
  background: #f7fafc;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

._analysisCardTitle_lbydi_485 {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 8px 0;
}

._analysisCardContent_lbydi_499 {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
}

/* Estados de loading e erro */
._loadingContainer_lbydi_513 {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

._errorContainer_lbydi_527 {
  background: #fed7d7;
  border: 1px solid #feb2b2;
  color: #c53030;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
}

/* Responsividade */
@media (max-width: 768px) {
  ._dashboardContainer_lbydi_15 {
    padding: 16px;
    margin-bottom: 16px;
  }

  ._dashboardHeader_lbydi_37 {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  ._dashboardControls_lbydi_93 {
    justify-content: center;
  }

  ._dashboardTitle_lbydi_55 {
    font-size: 24px;
    text-align: center;
  }

  ._metricsGrid_lbydi_191 {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  ._chartsGrid_lbydi_365 {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  ._chartContainer_lbydi_409 {
    height: 250px;
  }

  ._analysisGrid_lbydi_459 {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  ._dashboardContainer_lbydi_15 {
    padding: 12px;
  }

  ._dashboardTitle_lbydi_55 {
    font-size: 20px;
  }

  ._metricValue_lbydi_307 {
    font-size: 28px;
  }

  ._chartContainer_lbydi_409 {
    height: 200px;
  }
}

/* Animações */
@keyframes _fadeIn_lbydi_1 {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

._dashboardContainer_lbydi_15 {
  animation: _fadeIn_lbydi_1 0.5s ease-out;
}

._metricCard_lbydi_205,
._chartCard_lbydi_379,
._analysisSection_lbydi_421 {
  animation: _fadeIn_lbydi_1 0.6s ease-out;
}

/* Acessibilidade */
._refreshButton_lbydi_149:focus-visible,
._timeframeSelector_lbydi_105:focus-visible {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Tema escuro (futuro) */
@media (prefers-color-scheme: dark) {
  ._dashboardContainer_lbydi_15 {
    background-color: #1a202c;
    color: #e2e8f0;
  }

  ._chartCard_lbydi_379,
  ._analysisSection_lbydi_421 {
    background-color: #2d3748;
    border-color: #4a5568;
  }

  ._metricCard_lbydi_205 {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    border-color: #4a5568;
  }

  ._dashboardTitle_lbydi_55,
  ._chartTitle_lbydi_395,
  ._analysisTitle_lbydi_439,
  ._metricValue_lbydi_307 {
    color: #e2e8f0;
  }

  ._analysisCard_lbydi_471 {
    background-color: #4a5568;
  }
}
/**
 * @file DashboardContainer.module.css
 * @description Estilos modulares para o container dos dashboards
 * @version 3.0.0
 */

/* Container principal */
._dashboardContainer_g47x9_15 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  z-index: 1;
  /* Garantir que o dashboard permaneça opaco */
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

/* Estado quando o dashboard está "subindo" - deve sumir completamente */
._dashboardContainer_g47x9_15._hidden_g47x9_45 {
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

/* Estado quando o dashboard está "descendo" - deve aparecer completamente */
._dashboardContainer_g47x9_15._visible_g47x9_61 {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

/* Botão de voltar */
._backButton_g47x9_77 {
  position: absolute;
  top: 1rem;
  left: 2rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: 8px;
  padding: 0.7rem 1rem;
  color: white;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 900;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

._backButton_g47x9_77:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
}

/* Header do dashboard */
._dashboardHeader_g47x9_131 {  padding: 5rem 2rem 2rem 2rem;
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

._dashboardTitle_g47x9_147 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #fff, #f0f9ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-flex;
  align-items: center;
  gap: 1rem;
}

._premiumBadge_g47x9_173 {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 10px rgba(245, 158, 11, 0.3);
}

._dashboardSubtitle_g47x9_197 {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 1.5rem;
  font-weight: 300;
}

/* Navegação por abas - FIXO */
._dashboardTabs_g47x9_213 {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 0.8rem;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  /* Garantir que as abas sejam sempre visíveis e opacas */
  opacity: 1;
  visibility: visible;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

._dashboardTabs_g47x9_213::-webkit-scrollbar {
  display: none; /* Chrome/Safari */
}

/* Conteúdo do dashboard - ajustado para layout sem tabs */

._dashboardTab_g47x9_213 {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 0.8rem 1.5rem;
  color: white;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  text-transform: capitalize;
  white-space: nowrap;
  min-width: fit-content;
  flex-shrink: 0;
}

._dashboardTab_g47x9_213:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

._dashboardTab_g47x9_213._active_g47x9_323 {
  background: linear-gradient(135deg, #10b981, #047857);
  border-color: #10b981;
  box-shadow: 0 4px 20px rgba(16, 185, 129, 0.3);
}

._dashboardTab_g47x9_213[aria-pressed="true"] {
  background: linear-gradient(135deg, #10b981, #047857);
  border-color: #10b981;
  box-shadow: 0 4px 20px rgba(16, 185, 129, 0.3);
}

/* Conteúdo do dashboard */
._dashboardContent_g47x9_349 {
  min-height: calc(100vh - 120px);
  position: relative;
  background: transparent;
  padding: 1rem 2rem;
  margin-top: 0;
  /* Garantir que o conteúdo não fique transparente */
  opacity: 1;
  visibility: visible;
  transition: opacity 0.3s ease-in-out;
}

/* Quando o dashboard está oculto, o conteúdo também deve sumir */
._dashboardContainer_g47x9_15._hidden_g47x9_45 ._dashboardContent_g47x9_349 {
  opacity: 0;
  visibility: hidden;
}

/* Wrapper para cada dashboard individual */
._dashboardWrapper_g47x9_387 {
  width: 100%;
  height: 100%;
  background: transparent;
  overflow-x: hidden;
}

/* Remover espaçamentos desnecessários para melhor integração */
._dashboardContent_g47x9_349 > div {
  margin: 0;
  padding: 0;
}

._dashboardWrapper_g47x9_387 > div {
  max-width: 100%;
  box-sizing: border-box;
}

/* Tela de login premium já está definida inline no componente */

/* Responsividade */
@media (max-width: 768px) {
  ._dashboardHeader_g47x9_131 {
    padding: 5rem 1rem 1.5rem 1rem;
  }
  
  ._dashboardTitle_g47x9_147 {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  ._dashboardSubtitle_g47x9_197 {
    font-size: 1rem;
  }
  
  ._dashboardTabs_g47x9_213 {
    padding: 0.8rem 0.5rem;
    gap: 0.5rem;
    justify-content: flex-start;
    overflow-x: auto;
    scrollbar-width: none;
  }

  ._dashboardTab_g47x9_213 {
    padding: 0.6rem 1rem;
    font-size: 0.75rem;
    min-width: max-content;
    flex-shrink: 0;
  }

  ._dashboardContent_g47x9_349 {
    padding: 1rem;
    margin-top: 0;
  }
  
  ._backButton_g47x9_77 {
    top: 1rem;
    left: 1rem;
    padding: 0.8rem 1.2rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  ._dashboardTitle_g47x9_147 {
    font-size: 1.8rem;
  }

  ._dashboardTabs_g47x9_213 {
    padding: 0.6rem 0.3rem;
    gap: 0.3rem;
    justify-content: flex-start;
    overflow-x: auto;
    flex-wrap: nowrap;
  }

  ._dashboardTab_g47x9_213 {
    padding: 0.5rem 0.8rem;
    font-size: 0.7rem;
    min-width: max-content;
    flex-shrink: 0;
    gap: 0.2rem;
  }

  ._dashboardContent_g47x9_349 {
    padding: 0.5rem;
    margin-top: 0;
  }
}

/* Header do dashboard com logout */
._dashboardHeader_g47x9_131 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px 15px 0 0;
  margin-bottom: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

._headerLeft_g47x9_595 {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

._dashboardTitle_g47x9_147 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

._dashboardSubtitle_g47x9_197 {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0;
}

._headerRight_g47x9_637 {
  display: flex;
  align-items: center;
  gap: 1rem;
}

._logoutButton_g47x9_649 {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 0.7rem 1.5rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

._logoutButton_g47x9_649:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

._logoutButton_g47x9_649:active {
  transform: translateY(0);
}

._logoutButton_g47x9_649:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Botão de logout nas tabs */
._logoutTab_g47x9_715 {
  background: #dc2626;
  color: white;
  border: none;
  padding: 0.7rem 1.5rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: auto;
}

._logoutTab_g47x9_715:hover:not(:disabled) {
  background: #b91c1c;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
}

._logoutTab_g47x9_715:active {
  transform: translateY(0);
}

._logoutTab_g47x9_715:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Estilos para tela de login compacta */
._loginContainer_g47x9_781 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  padding: 1rem;
}

._loginBox_g47x9_803 {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 360px;
  width: 100%;
}

@media (max-width: 480px) {
  ._loginBox_g47x9_803 {
    padding: 1.5rem;
    max-width: 320px;
    border-radius: 12px;
  }
}

._loginInput_g47x9_841 {
  width: 100%;
  padding: 0.8rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

._loginInput_g47x9_841::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

._loginInput_g47x9_841:focus {
  outline: none;
  border-color: rgba(59, 130, 246, 0.8);
  background: rgba(255, 255, 255, 0.15);
}

._loginButton_g47x9_883 {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 0.8rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  width: 100%;
}

._loginButton_g47x9_883:disabled {
  background: rgba(107, 114, 128, 0.8);
  cursor: not-allowed;
}

._loginButton_g47x9_883:hover:not(:disabled) {
  background: linear-gradient(135deg, #16a34a, #15803d);
  transform: translateY(-1px);
}
/* Dashboard Container */
._dashboardContainer_1x5np_2 {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
  color: #ffffff;
  padding: 2rem;
  overflow-y: auto;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Header */
._dashboardHeader_1x5np_13 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

._headerContent_1x5np_22 {
  flex: 1;
}

._title_1x5np_26 {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

._titleIcon_1x5np_39 {
  font-size: 2.5rem;
  filter: drop-shadow(0 0 10px rgba(102, 126, 234, 0.5));
}

._subtitle_1x5np_44 {
  margin: 0;
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

._premiumBadge_1x5np_51 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
  color: #000;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

._premiumIcon_1x5np_64 {
  font-size: 1.2rem;
}

/* Alertas */
._alert_1x5np_69 {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
  border-radius: 12px;
  border: 1px solid;
  position: relative;
}

._alert_1x5np_69._success_1x5np_80 {
  background: rgba(34, 197, 94, 0.1);
  border-color: #22c55e;
  color: #22c55e;
}

._alert_1x5np_69._error_1x5np_86 {
  background: rgba(239, 68, 68, 0.1);
  border-color: #ef4444;
  color: #ef4444;
}

._alert_1x5np_69._info_1x5np_92 {
  background: rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
  color: #3b82f6;
}

._alertIcon_1x5np_98 {
  font-size: 1.2rem;
  flex-shrink: 0;
}

._alertMessage_1x5np_103 {
  flex: 1;
  font-weight: 500;
}

._alertClose_1x5np_108 {
  background: none;
  border: none;
  color: inherit;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

._alertClose_1x5np_108:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Grid Layout */
._dashboardGrid_1x5np_129 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

/* Cards */
._card_1x5np_137 {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

._card_1x5np_137:hover {
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

._cardHeader_1x5np_151 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
}

._cardTitle_1x5np_160 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #ffffff;
}

._cardIcon_1x5np_170 {
  font-size: 1.4rem;
}

._refreshButton_1x5np_174 {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  cursor: pointer;
  font-size: 1rem;
  padding: 0.5rem;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

._refreshButton_1x5np_174:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: rotate(180deg);
}

._cardContent_1x5np_196 {
  padding: 1.5rem;
}

/* Status Grid */
._statusGrid_1x5np_201 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

._statusItem_1x5np_207 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

._statusLabel_1x5np_217 {
  font-size: 0.9rem;
  color: #d1d5db;
  font-weight: 500;
}

._statusValue_1x5np_223 {
  font-weight: 600;
  color: #f3f4f6;
  font-size: 0.95rem;
}

._statusValue_1x5np_223._enabled_1x5np_229 {
  color: #22c55e;
}

._statusValue_1x5np_223._disabled_1x5np_233 {
  color: #ef4444;
}

._loadingStatus_1x5np_237 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  text-align: center;
}

._loadingSpinner_1x5np_246 {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: _spin_1x5np_1 1s linear infinite;
}

@keyframes _spin_1x5np_1 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Export Options */
._exportOptions_1x5np_261 {
  display: grid;
  gap: 1rem;
}

._optionItem_1x5np_266 {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.3s ease;
}

._optionItem_1x5np_266:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
}

._optionLabel_1x5np_279 {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  cursor: pointer;
  width: 100%;
}

._optionCheckbox_1x5np_287 {
  width: 18px;
  height: 18px;
  margin-top: 2px;
  accent-color: #667eea;
  cursor: pointer;
}

._optionText_1x5np_295 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

._optionText_1x5np_295 strong {
  color: #ffffff;
  font-size: 1rem;
  font-weight: 600;
}

._optionText_1x5np_295 small {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.85rem;
  line-height: 1.4;
}

/* Progress Bar */
._progressContainer_1x5np_315 {
  margin: 1.5rem 0;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

._progressLabel_1x5np_323 {
  margin-bottom: 1rem;
  font-weight: 600;
  color: #e5e7eb;
  text-align: center;
}

._progressBar_1x5np_330 {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

._progressFill_1x5np_338 {
  height: 100%;
  background: linear-gradient(90deg, #22c55e, #16a34a);
  border-radius: 4px;
  transition: width 0.3s ease;
  animation: _progressPulse_1x5np_1 2s infinite;
}

@keyframes _progressPulse_1x5np_1 {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* Action Buttons */
._actionButtons_1x5np_352 {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

._actionButton_1x5np_352 {
  flex: 1;
  min-width: 200px;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

._primaryButton_1x5np_375 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

._primaryButton_1x5np_375:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

._primaryButton_1x5np_375:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

._successButton_1x5np_392 {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

._successButton_1x5np_392:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4);
}

/* Stats Container */
._statsContainer_1x5np_404 {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

._statsContainer_1x5np_404 h4 {
  margin-bottom: 1rem;
  color: #e5e7eb;
  font-size: 1.1rem;
  text-align: center;
}

._statsGrid_1x5np_419 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

._statItem_1x5np_425 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

._statLabel_1x5np_435 {
  font-size: 0.9rem;
  color: #d1d5db;
}

._statValue_1x5np_440 {
  font-weight: 600;
  color: #3b82f6;
  font-size: 1rem;
}

/* Preview Container */
._previewContainer_1x5np_447 {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

._previewContainer_1x5np_447 h4 {
  margin-bottom: 1rem;
  color: #e5e7eb;
  font-size: 1.1rem;
}

._previewInfo_1x5np_461 {
  margin-bottom: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

._previewInfo_1x5np_461 p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: #d1d5db;
}

._previewInfo_1x5np_461 strong {
  color: #f3f4f6;
}

._previewContent_1x5np_479 {
  background: rgba(0, 0, 0, 0.3);
  padding: 1rem;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  color: #f0f0f0;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.1);
  line-height: 1.4;
}

/* Premium Features */
._premiumFeatures_1x5np_495 {
  display: grid;
  gap: 1.5rem;
}

._featureItem_1x5np_500 {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

._featureItem_1x5np_500:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
}

._featureIcon_1x5np_516 {
  font-size: 2rem;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

._featureContent_1x5np_522 h4 {
  margin: 0 0 0.5rem 0;
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
}

._featureContent_1x5np_522 p {
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Responsividade */
@media (max-width: 768px) {
  ._dashboardContainer_1x5np_2 {
    padding: 1rem;
  }

  ._dashboardHeader_1x5np_13 {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  ._title_1x5np_26 {
    font-size: 2rem;
  }

  ._dashboardGrid_1x5np_129 {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  ._statusGrid_1x5np_201 {
    grid-template-columns: 1fr;
  }

  ._statusItem_1x5np_207 {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  ._actionButtons_1x5np_352 {
    flex-direction: column;
  }

  ._actionButton_1x5np_352 {
    min-width: auto;
  }

  ._statsGrid_1x5np_419 {
    grid-template-columns: 1fr;
  }

  ._statItem_1x5np_425 {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
}
/**
 * @file AdminGate.module.css
 * @description Estilos para o componente AdminGate
 * @version 3.0.0
 */

._adminGate_1p3zc_13 {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  padding: 2rem;
}

._backButton_1p3zc_37 {
  position: absolute;
  top: 2rem;
  left: 2rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.7rem 1rem;
  color: white;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 100;
}

._backButton_1p3zc_37:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

._gateContent_1p3zc_83 {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 3rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
  text-align: center;
}

._gateIcon_1p3zc_107 {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  animation: _pulse_1p3zc_1 2s infinite;
}

@keyframes _pulse_1p3zc_1 {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1); 
  }
  50% { 
    opacity: 0.7; 
    transform: scale(1.05); 
  }
}

._gateTitle_1p3zc_141 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #ff6b6b, #ffa500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

._gateMessage_1p3zc_161 {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 2rem;
  line-height: 1.6;
}

._gateInfo_1p3zc_175 {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-align: left;
}

._gateInfo_1p3zc_175 h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #ffd700;
  text-align: center;
}

._gateInfo_1p3zc_175 p {
  margin-bottom: 1.5rem;
  line-height: 1.6;
  opacity: 0.9;
}

._accessRequirements_1p3zc_217 {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

._accessRequirements_1p3zc_217 h4 {
  color: #4ecdc4;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

._accessRequirements_1p3zc_217 ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

._accessRequirements_1p3zc_217 li {
  padding: 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

._contactInfo_1p3zc_269 {
  background: rgba(59, 130, 246, 0.2);
  border: 1px solid #3b82f6;
  border-radius: 10px;
  padding: 1.5rem;
  text-align: center;
}

._contactInfo_1p3zc_269 strong {
  color: #60a5fa;
}

/* Responsividade */
@media (max-width: 768px) {
  ._adminGate_1p3zc_13 {
    padding: 1rem;
  }
  
  ._gateContent_1p3zc_83 {
    padding: 2rem;
  }
  
  ._gateTitle_1p3zc_141 {
    font-size: 2rem;
  }
  
  ._gateIcon_1p3zc_107 {
    font-size: 3rem;
  }
  
  ._backButton_1p3zc_37 {
    top: 1rem;
    left: 1rem;
    padding: 0.6rem 0.8rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  ._gateContent_1p3zc_83 {
    padding: 1.5rem;
  }
  
  ._gateTitle_1p3zc_141 {
    font-size: 1.5rem;
  }
  
  ._gateMessage_1p3zc_161 {
    font-size: 1rem;
  }
  
  ._gateInfo_1p3zc_175 {
    padding: 1rem;
  }
}
/**
 * 🎨 Portal Betina V3 - Estilos do Formulário de Cadastro
 */

._overlay_qqx89_5 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

._modal_qqx89_19 {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 20px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  border: 2px solid #6366f1;
}

._header_qqx89_30 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 2px solid #6366f1;
  background: rgba(99, 102, 241, 0.1);
}

._header_qqx89_30 h2 {
  color: #ffffff;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

._closeButton_qqx89_46 {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

._closeButton_qqx89_46:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

._progressBar_qqx89_62 {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px 32px;
  gap: 16px;
}

._progressStep_qqx89_70 {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  color: #9ca3af;
  font-weight: 600;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

._progressStep_qqx89_70._active_qqx89_84 {
  background: #6366f1;
  color: #ffffff;
  border-color: #6366f1;
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
}

._content_qqx89_91 {
  padding: 32px;
}

._step_qqx89_95 h3 {
  color: #ffffff;
  margin: 0 0 24px 0;
  font-size: 1.25rem;
  font-weight: 600;
}

._fieldGroup_qqx89_102 {
  margin-bottom: 20px;
}

._label_qqx89_106 {
  display: block;
  color: #e5e7eb;
  font-weight: 500;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

._required_qqx89_114 {
  color: #ef4444;
  margin-left: 4px;
}

._input_qqx89_119,
._select_qqx89_120 {
  width: 100%;
  padding: 12px 16px;
  background: rgba(26, 26, 46, 0.8);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

._select_qqx89_120 option {
  background: #1a1a2e;
  color: #ffffff;
  padding: 10px 12px;
  font-size: 1rem;
  font-weight: 500;
  border: none;
  line-height: 1.4;
}

._select_qqx89_120 option:hover,
._select_qqx89_120 option:focus,
._select_qqx89_120 option:checked {
  background: #6366f1 !important;
  color: #ffffff !important;
  font-weight: 600;
}

._select_qqx89_120 option:disabled {
  background: #374151;
  color: #9ca3af;
}

._input_qqx89_119:focus,
._select_qqx89_120:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

._input_qqx89_119::placeholder {
  color: #9ca3af;
}

._input_qqx89_119._error_qqx89_166,
._select_qqx89_120._error_qqx89_166 {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

._errorText_qqx89_172 {
  color: #fecaca;
  font-size: 0.8rem;
  margin-top: 4px;
  display: block;
}

._planSelection_qqx89_179 {
  margin-top: 32px;
}

._planSelection_qqx89_179 h3 {
  color: #ffffff;
  margin-bottom: 20px;
  text-align: center;
}

._plansGrid_qqx89_189 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

._planCard_qqx89_196 {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

._planCard_qqx89_196:hover {
  border-color: #6366f1;
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.2);
}

._planCard_qqx89_196._selected_qqx89_213 {
  border-color: #6366f1;
  background: rgba(99, 102, 241, 0.1);
  box-shadow: 0 0 30px rgba(99, 102, 241, 0.3);
}

._planCard_qqx89_196._popular_qqx89_219 {
  border-color: #f59e0b;
}

._popularBadge_qqx89_223 {
  position: absolute;
  top: -2px;
  right: 20px;
  background: #f59e0b;
  color: #ffffff;
  padding: 4px 12px;
  border-radius: 0 0 8px 8px;
  font-size: 0.8rem;
  font-weight: 600;
}

._planCard_qqx89_196 h4 {
  color: #ffffff;
  margin: 0 0 12px 0;
  font-size: 1.2rem;
  font-weight: 600;
}

._price_qqx89_242 {
  color: #ffffff;
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

._period_qqx89_250 {
  font-size: 0.9rem;
  color: #9ca3af;
  font-weight: 400;
}

._description_qqx89_256 {
  color: #d1d5db;
  font-size: 0.9rem;
  margin-bottom: 16px;
}

._features_qqx89_262 {
  list-style: none;
  padding: 0;
  margin: 0;
}

._features_qqx89_262 li {
  color: #e5e7eb;
  font-size: 0.85rem;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

._features_qqx89_262 li:before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #10b981;
  font-weight: 600;
}

._moreFeatures_qqx89_284 {
  color: #9ca3af !important;
  font-style: italic;
}

._paymentStep_qqx89_289 {
  text-align: center;
}

._successMessage_qqx89_293 h3 {
  color: #10b981;
  margin-bottom: 16px;
}

._successMessage_qqx89_293 p {
  color: #d1d5db;
  margin-bottom: 32px;
}

._paymentInfo_qqx89_303 {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
}

._planSummary_qqx89_310 {
  margin-bottom: 24px;
}

._planSummary_qqx89_310 h4 {
  color: #ffffff;
  margin-bottom: 8px;
}

._amount_qqx89_319 {
  color: #3b82f6;
  font-size: 2rem;
  font-weight: 700;
}

._pixSection_qqx89_325 h4 {
  color: #ffffff;
  margin-bottom: 16px;
}

._pixCode_qqx89_330 {
  margin-bottom: 16px;
}

._pixCode_qqx89_330 label {
  display: block;
  color: #e5e7eb;
  margin-bottom: 8px;
  text-align: left;
}

._codeContainer_qqx89_341 {
  display: flex;
  gap: 8px;
}

._pixInput_qqx89_346 {
  flex: 1;
  padding: 12px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  font-family: monospace;
  font-size: 0.9rem;
}

._copyButton_qqx89_357 {
  padding: 12px 16px;
  background: #6366f1;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

._copyButton_qqx89_357:hover {
  background: #4f46e5;
}

._pixInstructions_qqx89_372 {
  color: #d1d5db;
  font-size: 0.9rem;
  text-align: left;
  line-height: 1.6;
  margin-bottom: 16px;
}

._registrationId_qqx89_380 {
  color: #9ca3af;
  font-size: 0.9rem;
  padding: 12px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

._nextSteps_qqx89_388 {
  text-align: left;
}

._nextSteps_qqx89_388 h4 {
  color: #ffffff;
  margin-bottom: 12px;
}

._nextSteps_qqx89_388 ol {
  color: #d1d5db;
  padding-left: 20px;
}

._nextSteps_qqx89_388 li {
  margin-bottom: 8px;
  line-height: 1.5;
}

._footer_qqx89_407 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-top: 2px solid rgba(255, 255, 255, 0.1);
}

._prevButton_qqx89_415,
._nextButton_qqx89_416,
._submitButton_qqx89_417,
._closeModalButton_qqx89_418 {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

._prevButton_qqx89_415 {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

._prevButton_qqx89_415:hover {
  background: rgba(255, 255, 255, 0.2);
}

._nextButton_qqx89_416,
._submitButton_qqx89_417 {
  background: #6366f1;
  color: #ffffff;
}

._nextButton_qqx89_416:hover,
._submitButton_qqx89_417:hover {
  background: #4f46e5;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

._submitButton_qqx89_417:disabled {
  background: #6b7280;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

._closeModalButton_qqx89_418 {
  background: #10b981;
  color: #ffffff;
  margin: 0 auto;
}

._closeModalButton_qqx89_418:hover {
  background: #059669;
}
