{"version": 3, "mappings": ";;;AAQA,IAAI,SAAS;AAGb,eAAe,iBAAiB;AAC9B,MAAI,OAAO,WAAW,eAAe,OAAO,YAAY,aAAa;AAC/D;AACI,qBAAS,0BAAM,QAA0B;AAC/C,eAAS,OAAO;AAChB,cAAQ,IAAI,+DAA+D;AACpE;AAAA,aACA,OAAO;AACN,mBAAK,iCAAiC,MAAM,OAAO;AACpD;AAAA;AAAA,EACT;AAEK;AACT;AAGA,MAAM,iBAAiB;AAAA,EACrB,0BAA0B,CAAC,QAAQ,QAAQ,YAAY;AACrD,UAAM,SAAS,CAAC;AAChB,QAAI,CAAC,OAAe,aAAK,sBAAsB;AAC/C,QAAI,CAAC,OAAe,aAAK,sBAAsB;AAC/C,QAAI,CAAC,WAAW,OAAO,YAAY,SAAU,QAAO,KAAK,4BAA4B;AAE9E;AAAA,MACL,OAAO,OAAO,WAAW;AAAA,MACzB;AAAA,MACA,WAAW;AAAA,QACT,QAAQ,OAAO,UAAU,EAAE,EAAE,KAAK;AAAA,QAClC,QAAQ,OAAO,UAAU,EAAE,EAAE,KAAK;AAAA,QAClC,SAAS,WAAW;AAAA,MAAC;AAAA,IAEzB;AAAA,EACF;AAAA,EAEA,2BAA2B,CAAC,WAAW,iBAAiB;AACtD,UAAM,SAAS,CAAC;AAChB,QAAI,CAAC,UAAkB,aAAK,yBAAyB;AACrD,QAAI,CAAC,MAAM,QAAQ,YAAY,EAAG,QAAO,KAAK,gCAAgC;AAEvE;AAAA,MACL,OAAO,OAAO,WAAW;AAAA,MACzB;AAAA,MACA,WAAW;AAAA,QACT,WAAW,OAAO,aAAa,EAAE,EAAE,KAAK;AAAA,QACxC,cAAc,MAAM,QAAQ,YAAY,IAAI,eAAe;AAAA,MAAC;AAAA,IAEhE;AAAA;AAEJ;AAMA,MAAM,iBAAiB;AAAA,EACrB,cAAc;AACZ,SAAK,YAAY;AAAA;AAAA,EAGnB,KAAK,SAAS,UAAU,IAAI;AAC1B,YAAQ,KAAK,OAAO,KAAK,SAAS,KAAK,OAAO,IAAI,OAAO;AAAA;AAAA,EAG3D,KAAK,SAAS,UAAU,IAAI;AAC1B,YAAQ,KAAK,OAAO,KAAK,SAAS,KAAK,OAAO,IAAI,OAAO;AAAA;AAAA,EAG3D,MAAM,SAAS,UAAU,IAAI;AAC3B,YAAQ,MAAM,MAAM,KAAK,SAAS,KAAK,OAAO,IAAI,OAAO;AAAA;AAAA,EAG3D,MAAM,SAAS,UAAU,IAAI;AAC3B,YAAQ,MAAM,OAAO,KAAK,SAAS,KAAK,OAAO,IAAI,OAAO;AAAA;AAE9D;AAMA,iCAAM,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,YAAYA,UAAS,IAAI;AACvB,SAAK,SAAS;AAAA,MACZ,YAAY;AAAA,QACV,SAAS;AAAA,QACT,mBAAmB;AAAA,QACnB,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,eAAe;AAAA,QACf,WAAW;AAAA;AAAA,QACX,cAAc,CAAC,CAACA,QAAO;AAAA,QACvB,UAAUA,QAAO,YAAY;AAAA,MAC/B;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,QACT,iBAAiB;AAAA,QACjB,iBAAiB;AAAA;AAAA,MACnB;AAAA,MACA,GAAGA;AAAA,IACL;AAEK,kBAAS,IAAI,iBAAiB;AAC9B,6CAAoB,IAAI;AACxB,yCAAgB,IAAI;AAGzB,SAAK,OAAO;AACZ,SAAK,mBAAmB;AACxB,SAAK,YAAY,OAAO,WAAW,eAAe,OAAO,YAAY;AAEhE,gBAAO,KAAK,mCAAmC;AAAA,MAClD,QAAQ;AAAA,QACN,YAAY,KAAK,OAAO,WAAW;AAAA,QACnC,OAAO,KAAK,OAAO,MAAM,eAAe,UAAU;AAAA,QAClD,SAAS,KAAK,OAAO,QAAQ;AAAA,QAC7B,SAAS,KAAK;AAAA,QACd,YAAY,CAAC,CAAC,KAAK;AAAA;AAAA,IACrB,CACD;AAGD,QAAI,KAAK,WAAW;AAClB,WAAK,qBAAqB;AAAA;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAMF,MAAM,uBAAuB;AACvB,SAAC,KAAK,WAAW;AACd,kBAAO,KAAK,8CAA8C;AAC/D;AAAA;AAGE;AAEI,YAAAC,QAAO,MAAM,eAAe;AAElC,UAAIA,OAAM;AACR,aAAK,OAAOA;AAEZ,cAAM,SAAS,MAAM,KAAK,KAAK,QAAQ;AACjC,qBAAO,MAAM,kBAAkB;AACrC,eAAO,QAAQ;AAEf,aAAK,mBAAmB;AACnB,oBAAO,KAAK,+CAA+C;AAAA,aAC3D;AACC,kBAAI,MAAM,gCAAgC;AAAA;AAAA,aAE3C,OAAO;AACd,WAAK,mBAAmB;AACxB,WAAK,OAAO,MAAM,sCAAsC,MAAM,OAAO;AAAA;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA,EAMF,cAAc;AACZ,WAAO,KAAK,qBAAqB,eAAe,CAAC,CAAC,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzD,YAAY;AACH;AAAA,MACL,QAAQ,KAAK;AAAA,MACb,WAAW,KAAK,YAAY;AAAA,MAC5B,MAAM,KAAK,YAAY,eAAe;AAAA,MACtC,SAAS,KAAK;AAAA,MACd,eAAe,CAAC,CAAC,KAAK;AAAA,IACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMF,MAAM,MAAM,KAAK,SAAS,IAAI;AACxB,SAAC,KAAK,eAAe;AACvB,WAAK,OAAO,KAAK,8BAA8B,EAAE,KAAK,QAAQ;AAC9D,aAAO,CAAC;AAAA;AAGN;AACF,YAAM,SAAS,MAAM,KAAK,KAAK,QAAQ;AACvC,YAAM,SAAS,MAAM,OAAO,MAAM,KAAK,MAAM;AAC7C,aAAO,QAAQ;AAEV,kBAAO,KAAK,iCAAiC;AAAA,QAChD,KAAK,IAAI,UAAU,GAAG,GAAG,IAAI;AAAA,QAC7B,UAAU,OAAO;AAAA,OAClB;AAED,aAAO,OAAO;AAAA,aACP,OAAO;AACd,WAAK,OAAO,MAAM,6BAA6B,MAAM,OAAO;AACtD;AAAA;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAMF,MAAM,MAAM,OAAO,MAAM;AACnB,SAAC,KAAK,eAAe;AACvB,WAAK,OAAO,KAAK,kCAAkC,OAAO,KAAK,IAAI;AACnE,aAAO,EAAE,SAAS,MAAM,IAAI,KAAK,MAAM;AAAA;AAGrC;AACI,sBAAU,OAAO,KAAK,IAAI;AAC1B,qBAAS,OAAO,OAAO,IAAI;AACjC,YAAM,eAAe,OAAO,IAAI,CAAC,GAAG,UAAU,IAAI,QAAQ,CAAC,EAAE,EAAE,KAAK,IAAI;AAElE,kBAAM,eAAe,KAAK,KAAK,QAAQ,KAAK,IAAI,CAAC,aAAa,YAAY;AAEhF,YAAM,SAAS,MAAM,KAAK,MAAM,KAAK,MAAM;AAEtC,kBAAO,KAAK,mCAAmC,EAAE,OAAO,IAAI,OAAO,CAAC,GAAG,IAAI;AAEzE,eAAE,SAAS,MAAM,IAAI,OAAO,CAAC,GAAG,IAAI,GAAG,KAAK;AAAA,aAC5C,OAAO;AACd,WAAK,OAAO,MAAM,8BAA8B,MAAM,OAAO;AACvD;AAAA;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUF,MAAM,gBAAgB,QAAQ,QAAQ,SAAS;AACzC;AACF,YAAM,aAAa,eAAe,yBAAyB,QAAQ,QAAQ,OAAO;AAC9E,WAAC,WAAW,OAAO;AACf,kBAAI,MAAM,kBAAkB,WAAW,OAAO,KAAK,IAAI,CAAC,EAAE;AAAA;AAG5D,cAAE,QAAQ,iBAAiB,QAAQ,iBAAiB,SAAS,qBAAqB,WAAW;AAG/F,eAAK,eAAe;AACtB,cAAM,YAAY,WAAW,KAAK,KAAK,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,GAAG,CAAC,CAAC;AAElF,cAAM,kBAAkB;AAAA,UACtB,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,SAAS;AAAA,UACT,cAAc,KAAK,UAAU,gBAAgB;AAAA,UAC7C,UAAU,iBAAiB,YAAY;AAAA,UACvC,eAAe,iBAAiB,uBAAuB;AAAA,UACvD,kBAAkB,iBAAiB,mBAAmB;AAAA,UACtD,aAAY,oBAAI,KAAK,GAAE,YAAY;AAAA,QACrC;AAEA,cAAM,SAAS,MAAM,KAAK,MAAM,gBAAgB,eAAe;AAE1D,oBAAO,KAAK,sCAAsC;AAAA,UACrD,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR;AAAA,UACA,cAAc,OAAO,KAAK,gBAAgB,EAAE;AAAA,SAC7C;AAEM;AAAA,UACL;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,WAAW,KAAK,IAAI;AAAA,UACpB,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,IAAI,OAAO;AAAA,QACb;AAAA,aACK;AAEL,cAAM,SAAS;AAAA,UACb,WAAW,KAAK,IAAI;AAAA,UACpB,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,WAAW,KAAK,IAAI;AAAA,UACpB,QAAQ;AAAA,UACR,UAAU;AAAA,QACZ;AAEK,oBAAO,KAAK,gCAAgC;AAAA,UAC/C,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,WAAW,OAAO;AAAA,UAClB,cAAc,OAAO,KAAK,gBAAgB,EAAE;AAAA,SAC7C;AAEM;AAAA;AAAA,aAEF,OAAO;AACT,kBAAO,MAAM,+BAA+B;AAAA,QAC/C;AAAA,QACA;AAAA,QACA,OAAO,MAAM;AAAA,QACb,OAAO,MAAM;AAAA,OACd;AACK;AAAA;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASF,MAAM,iBAAiB,WAAW,cAAc;AAC1C;AACF,YAAM,aAAa,eAAe,0BAA0B,WAAW,YAAY;AAC/E,WAAC,WAAW,OAAO;AACf,kBAAI,MAAM,+BAA+B,WAAW,OAAO,KAAK,IAAI,CAAC,EAAE;AAAA;AAG/E,YAAM,EAAE,WAAW,oBAAoB,cAAc,0BAA0B,WAAW;AAG1F,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,kBAAkB,sBAAsB;AAAA,QACxC,WAAW,KAAK,IAAI;AAAA,QACpB,QAAQ;AAAA,MACV;AAEK,kBAAO,KAAK,8CAA8C;AAAA,QAC7D,WAAW;AAAA,QACX,kBAAkB,sBAAsB;AAAA,OACzC;AAEM;AAAA,aACA,OAAO;AACT,kBAAO,MAAM,+BAA+B;AAAA,QAC/C;AAAA,QACA,OAAO,MAAM;AAAA,QACb,OAAO,MAAM;AAAA,OACd;AACK;AAAA;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASF,MAAM,YAAY,QAAQ,UAAU;AAC9B;AACF,YAAM,aAAa,eAAe,sBAAsB,QAAQ,QAAQ;AACpE,WAAC,WAAW,OAAO;AACf,kBAAI,MAAM,kBAAkB,WAAW,OAAO,KAAK,IAAI,CAAC,EAAE;AAAA;AAGlE,YAAM,EAAE,QAAQ,iBAAiB,UAAU,sBAAsB,WAAW;AAG5E,YAAM,SAAS;AAAA,QACb,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,MAAM,CAAC;AAAA,QACP,WAAW,KAAK,IAAI;AAAA,QACpB,QAAQ;AAAA,MACV;AAEK,kBAAO,KAAK,+CAA+C;AAAA,QAC9D,QAAQ;AAAA,QACR,UAAU;AAAA,OACX;AAEM;AAAA,aACA,OAAO;AACT,kBAAO,MAAM,gCAAgC;AAAA,QAChD;AAAA,QACA;AAAA,QACA,OAAO,MAAM;AAAA,QACb,OAAO,MAAM;AAAA,OACd;AACK;AAAA;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA,EAOF,0BAA0B;AACpB;AACF,YAAM,SAAS;AAAA,QACb,UAAU;AAAA,UACR,WAAW;AAAA,UACX,aAAa;AAAA,UACb,MAAM;AAAA,UACN,QAAQ;AAAA,QACV;AAAA,QACA,YAAY,EAAE,QAAQ,cAAc;AAAA,QACpC,gBAAgB,EAAE,QAAQ,cAAc;AAAA,QACxC,eAAe,EAAE,QAAQ,cAAc;AAAA,QACvC,oBAAoB,EAAE,QAAQ,cAAc;AAAA,QAC5C,eAAe;AAAA,UACb,MAAM,KAAK,cAAc;AAAA,UACzB,QAAQ;AAAA,QACV;AAAA,QACA,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MACpC;AAEO;AAAA,aACA,OAAO;AACT,kBAAO,MAAM,kCAAkC,EAAE,OAAO,MAAM,SAAS,OAAO,MAAM,OAAO;AACzF;AAAA,QACL,UAAU,EAAE,WAAW,OAAO,aAAa,OAAO,MAAM,uBAAuB,QAAQ,SAAS;AAAA,QAChG,YAAY,EAAE,QAAQ,SAAS;AAAA,QAC/B,gBAAgB,EAAE,QAAQ,SAAS;AAAA,QACnC,eAAe,EAAE,QAAQ,SAAS;AAAA,QAClC,oBAAoB,EAAE,QAAQ,SAAS;AAAA,QACvC,eAAe,EAAE,MAAM,GAAG,QAAQ,SAAS;AAAA,QAC3C,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MACpC;AAAA;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUF,MAAM,oBAAoB,QAAQ,QAAQ,aAAa;AACjD;AACE,eAAK,eAAe;AACtB,cAAM,YAAY,WAAW,KAAK,KAAK,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,GAAG,CAAC,CAAC;AAElF,cAAM,kBAAkB;AAAA,UACtB,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,SAAS;AAAA,UACT,cAAc,KAAK,UAAU,WAAW;AAAA,UACxC,YAAY,YAAY,YAAY,IAAI,KAAK,YAAY,SAAS,EAAE,YAAY,KAAQ,4BAAO,YAAY;AAAA,UAC3G,UAAU,YAAY,UAAU,IAAI,KAAK,YAAY,OAAO,EAAE,YAAY,KAAQ,4BAAO,YAAY;AAAA,UACrG,UAAU,YAAY,YAAY;AAAA,UAClC,aAAY,oBAAI,KAAK,GAAE,YAAY;AAAA,QACrC;AAEA,cAAM,SAAS,MAAM,KAAK,MAAM,iBAAiB,eAAe;AAE3D,oBAAO,KAAK,0CAA0C;AAAA,UACzD;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAW,UAAU,YAAY;AAAA,SAClD;AAEM,iBAAE,WAAW,QAAQ,QAAQ,QAAQ,WAAW,UAAU,cAAc,IAAI,OAAO,GAAG;AAAA,aACxF;AACL,aAAK,OAAO,KAAK,oCAAoC,EAAE,QAAQ,QAAQ;AAChE,iBAAE,WAAW,KAAK,OAAO,QAAQ,QAAQ,QAAQ,WAAW,UAAU,OAAO;AAAA;AAAA,aAE/E,OAAO;AACd,WAAK,OAAO,MAAM,qCAAqC,MAAM,OAAO;AAC9D;AAAA;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQF,MAAM,kBAAkB,QAAQ;AAC1B;AACE,eAAK,eAAe;AACtB,cAAM,aAAa;AAAA,UACjB,YAAY,OAAO;AAAA,UACnB,SAAS,OAAO;AAAA,UAChB,eAAe,OAAO,QAAQ;AAAA,UAC9B,eAAe,KAAK,UAAU,MAAM;AAAA,UACpC,kBAAkB,OAAO,cAAc;AAAA,UACvC,aAAY,oBAAI,KAAK,GAAE,YAAY;AAAA,QACrC;AAEA,cAAM,SAAS,MAAM,KAAK,MAAM,wBAAwB,UAAU;AAE7D,oBAAO,KAAK,wCAAwC;AAAA,UACvD,WAAW,OAAO;AAAA,UAAW,MAAM,OAAO;AAAA,SAC3C;AAED,eAAO,EAAE,QAAQ,WAAW,UAAU,cAAc,IAAI,OAAO,GAAG;AAAA,aAC7D;AACL,aAAK,OAAO,KAAK,kCAAkC,EAAE,WAAW,OAAO,WAAW;AAC3E,iBAAE,QAAQ,WAAW,UAAU,QAAQ,IAAI,KAAK,MAAM;AAAA;AAAA,aAExD,OAAO;AACd,WAAK,OAAO,MAAM,+BAA+B,MAAM,OAAO;AACxD;AAAA;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA,EAOF,kBAAkB;AAChB,WAAO,KAAK,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOtC,MAAM,UAAU;AACV;AACG,kBAAO,KAAK,0CAA0C;AAG3D,iBAAW,CAAC,cAAc,UAAU,KAAK,KAAK,UAAU,WAAW;AACjE,sBAAc,UAAU;AACxB,aAAK,OAAO,KAAK,qBAAqB,YAAY,EAAE;AAAA;AAEtD,WAAK,UAAU,MAAM;AAEhB,kBAAO,KAAK,wCAAwC;AAAA,aAClD,OAAO;AACT,kBAAO,MAAM,2CAA2C,EAAE,OAAO,MAAM,SAAS,OAAO,MAAM,OAAO;AACnG;AAAA;AAAA,EACR;AAEJ;;;;;;;AChiBA,IAAIC,sBAAqB;AACzB,IAAI,YAAY;AAMhB,MAAM,kBAAkB;AAAA,EACtB,cAAc;AACZ,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACjB,kBAAS,KAAK,aAAa;AAChC,SAAK,iBAAiB;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMF,eAAe;AACP,sBAAY,OAAO,WAAW;AAC7B;AAAA,MACL,MAAM,IAAI,SAAS,QAAQ;AAAA,QACzB,YAAY,wBAAwB;AAAA,QACpC,YAAY,mBAAmB;AAAA,SAC/B,oBAAI,KAAK,GAAE,YAAY;AAAA,QACvB,GAAG;AAAA,MACL;AAAA,MACA,OAAO,IAAI,SAAS,QAAQ;AAAA,QAC1B,YAAY,oBAAoB;AAAA,QAChC,YAAY,mBAAmB;AAAA,SAC/B,oBAAI,KAAK,GAAE,YAAY;AAAA,QACvB,GAAG;AAAA,MACL;AAAA,MACA,MAAM,IAAI,SAAS,QAAQ;AAAA,QACzB,YAAY,mBAAmB;AAAA,QAC/B,YAAY,mBAAmB;AAAA,SAC/B,oBAAI,KAAK,GAAE,YAAY;AAAA,QACvB,GAAG;AAAA;AAAA,IAEP;AAAA;AAAA;AAAA;AAAA;AAAA,EAMF,MAAM,yBAAyB;AAC7B,QAAIA,oBAA2B,QAAAA;AAC/B,QAAI,WAAW;AACb,aAAO,WAAW;AAChB,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,EAAE,CAAC;AAAA;AAE/C,aAAAA;AAAA;AAGG;AACR;AAEI,qBAAS,MAAM,4BAAgC;AAEhC,MAAAA,sBAAA,OAAO,WAAW,OAAO;AACvC,aAAAA;AAAA,aACA,OAAO;AACT,kBAAO,MAAM,0CAA0C,KAAK;AAC3D;AAAA,cACN;AACY;AAAA;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAMF,MAAM,cAAc;AAClB,QAAI,KAAK,UAAU;AACZ,kBAAO,KAAK,wDAAwD;AACzE,aAAO,KAAK;AAAA;AAGd,QAAI,KAAK,gBAAgB;AAClB,kBAAO,KAAK,kDAAkD;AACnE,aAAO,KAAK,gBAAgB;AAC1B,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,EAAE,CAAC;AAAA;AAEtD,aAAO,KAAK;AAAA;AAGd,SAAK,iBAAiB;AAElB;AACI,sCAA0B,MAAM,KAAK,uBAAuB;AAElE,YAAM,gBAAgB;AAAA,QACpB,YAAY;AAAA,UACV,SAAS;AAAA,UACT,mBAAmB;AAAA,UACnB,eAAe;AAAA,UACf,YAAY;AAAA,QACd;AAAA,QACA,OAAO;AAAA,UACL,eAAe;AAAA,UACf,WAAW;AAAA;AAAA,QACb;AAAA,QACA,SAAS;AAAA,UACP,SAAS;AAAA,UACT,iBAAiB;AAAA,UACjB,iBAAiB;AAAA;AAAA;AAAA,MAErB;AAEK,sBAAW,IAAI,wBAAwB,aAAa;AACpD,kBAAO,KAAK,gDAAgD;AAEjE,aAAO,KAAK;AAAA,aACL,OAAO;AACT,kBAAO,MAAM,6CAA6C,KAAK;AAC9D;AAAA,cACN;AACA,WAAK,iBAAiB;AAAA;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAMF,eAAe,YAAY,MAAM;AAC3B;AACF,UAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;AACrC,aAAK,OAAO,MAAM,yCAAyC,EAAE,YAAY;AAClE;AAAA;AAGT,UAAI,eAAe,0BAA0B;AACvC,aAAC,KAAK,YAAY,CAAC,KAAK,eAAe,SAAS,KAAK,QAAQ,GAAG;AAC7D,sBAAO,MAAM,wCAAwC;AAAA,YACxD,UAAU,KAAK;AAAA,YACf,gBAAgB,KAAK;AAAA,WACtB;AACM;AAAA;AAGL,aAAC,KAAK,aAAa,CAAC,KAAK,WAAW,CAAC,KAAK,WAAW;AAClD,sBAAO,MAAM,kCAAkC;AAAA,YAClD,WAAW,KAAK;AAAA,YAChB,SAAS,KAAK;AAAA,YACd,WAAW,KAAK;AAAA,WACjB;AACM;AAAA;AAIF,oBAAK,yBAAyB,IAAI;AAAA;AAGpC;AAAA,aACA,OAAO;AACT,kBAAO,MAAM,8BAA8B,KAAK;AAC9C;AAAA;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAMF,yBAAyB,MAAM;AAC7B,YAAQ,KAAK,UAAU;AAAA,MACrB,KAAK;AACI,oBAAK,6BAA6B,IAAI;AAAA,MAC/C,KAAK;AACI,oBAAK,uBAAuB,IAAI;AAAA,MACzC,KAAK;AACI,oBAAK,4BAA4B,IAAI;AAAA,MAC9C,KAAK;AACI,oBAAK,2BAA2B,IAAI;AAAA,MAC7C,KAAK;AACI,oBAAK,4BAA4B,IAAI;AAAA,MAC9C,KAAK;AACI,oBAAK,4BAA4B,IAAI;AAAA,MAC9C,KAAK;AACI,oBAAK,6BAA6B,IAAI;AAAA,MAC/C,KAAK;AACI,oBAAK,6BAA6B,IAAI;AAAA,MAC/C,KAAK;AACI,oBAAK,yBAAyB,IAAI;AAAA,MAC3C,KAAK;AACI,oBAAK,8BAA8B,IAAI;AAAA,MAChD,KAAK;AACI,oBAAK,uBAAuB,IAAI;AAAA,MACzC;AACE,aAAK,OAAO,MAAM,2BAA2B,KAAK,QAAQ;AACnD;AAAA;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAMF,6BAA6B,MAAM;AACjC,WAAO,CAAC,EAAE,KAAK,gBAAgB,uBAAuB,KAAK,gBAAgB;AAAA;AAAA,EAG7E,uBAAuB,MAAM;AAC3B,WAAO,CAAC,EAAE,KAAK,gBAAgB,iBAAiB,KAAK,gBAAgB;AAAA;AAAA,EAGvE,4BAA4B,MAAM;AAChC,WAAO,CAAC,EAAE,KAAK,gBAAgB,wBAAwB,KAAK,gBAAgB;AAAA;AAAA,EAG9E,2BAA2B,MAAM;AAC/B,WAAO,CAAC,EAAE,KAAK,gBAAgB,sBAAsB,KAAK,gBAAgB;AAAA;AAAA,EAG5E,4BAA4B,MAAM;AAChC,WAAO,CAAC,EAAE,KAAK,gBAAgB,qBAAqB,KAAK,gBAAgB;AAAA;AAAA,EAG3E,4BAA4B,MAAM;AAChC,WAAO,CAAC,EAAE,KAAK,gBAAgB,sBAAsB,KAAK,gBAAgB;AAAA;AAAA,EAG5E,6BAA6B,MAAM;AACjC,WAAO,CAAC,EAAE,KAAK,gBAAgB,oBAAoB,KAAK,gBAAgB;AAAA;AAAA,EAG1E,6BAA6B,MAAM;AACjC,WAAO,CAAC,EAAE,KAAK,gBAAgB,sBAAsB,KAAK,gBAAgB;AAAA;AAAA,EAG5E,yBAAyB,MAAM;AAC7B,WAAO,CAAC,EAAE,KAAK,gBAAgB,oBAAoB,KAAK,gBAAgB;AAAA;AAAA,EAG1E,8BAA8B,MAAM;AAClC,WAAO,CAAC,EAAE,KAAK,gBAAgB,qBAAqB,KAAK,gBAAgB;AAAA;AAAA,EAG3E,uBAAuB,MAAM;AAC3B,WAAO,CAAC,EAAE,KAAK,gBAAgB,oBAAoB,KAAK,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1E,MAAM,MAAM,YAAY,MAAM;AACxB;AACF,UAAI,CAAC,KAAK,eAAe,YAAY,IAAI,GAAG;AAC1C,cAAM,IAAI,MAAM,4CAA4C,UAAU,EAAE;AAAA;AAGpE,uBAAW,MAAM,KAAK,YAAY;AAClC,qBAAS,MAAM,YAAY;AAAA,QAC/B,GAAG;AAAA,QACH,WAAU,oBAAI,KAAK,GAAE,YAAY;AAAA,OAClC;AAEI,kBAAO,KAAK,oCAAoC;AAAA,QACnD;AAAA,QACA,WAAW,KAAK;AAAA,QAChB,UAAU,KAAK;AAAA,OAChB;AAAA,aACM,OAAO;AACT,kBAAO,MAAM,8BAA8B,KAAK;AAC/C;AAAA;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAMF,MAAM,MAAM,YAAY,OAAO;AACzB;AACI,uBAAW,MAAM,KAAK,YAAY;AACxC,YAAM,UAAU,MAAM,SAAS,MAAM,YAAY,KAAK;AACtD,WAAK,OAAO,KAAK,qCAAqC,EAAE,YAAY,OAAO;AAC3E,aAAO,WAAW,CAAC;AAAA,aACZ,OAAO;AACT,kBAAO,MAAM,8BAA8B,KAAK;AACrD,aAAO,CAAC;AAAA;AAAA,EACV;AAAA;AAAA;AAAA;AAAA,EAMF,MAAM,OAAO,YAAY,OAAO,MAAM;AAChC;AACI,uBAAW,MAAM,KAAK,YAAY;AACxC,YAAM,SAAS,MAAM,SAAS,OAAO,YAAY,OAAO,IAAI;AAC5D,WAAK,OAAO,KAAK,oCAAoC,EAAE,YAAY,OAAO;AACnE;AAAA,aACA,OAAO;AACT,kBAAO,MAAM,8BAA8B,KAAK;AAC/C;AAAA;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAMF,MAAM,OAAO,YAAY,OAAO;AAC1B;AACI,uBAAW,MAAM,KAAK,YAAY;AACxC,YAAM,SAAS,MAAM,SAAS,OAAO,YAAY,KAAK;AACtD,WAAK,OAAO,KAAK,mCAAmC,EAAE,YAAY,OAAO;AAClE;AAAA,aACA,OAAO;AACT,kBAAO,MAAM,4BAA4B,KAAK;AAC7C;AAAA;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAMF,MAAM,QAAQ;AACR;AACG,kBAAO,KAAK,qCAAqC;AAEtD,UAAI,KAAK,YAAY,KAAK,SAAS,YAAY;AACvC,mBAAK,SAAS,WAAW;AAAA;AAGjC,WAAK,WAAW;AAChB,WAAK,iBAAiB;AAEjB,kBAAO,KAAK,gCAAgC;AAAA,aAC1C,OAAO;AACT,kBAAO,MAAM,0CAA0C,KAAK;AAAA;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA,EAMF,MAAM,WAAW;AACX;AACI,uBAAW,MAAM,KAAK,YAAY;AACpC,sBAAY,SAAS,UAAU;AAC1B,qBAAM,SAAS,SAAS;AAAA;AAE1B;AAAA,aACA,OAAO;AACT,kBAAO,MAAM,iCAAiC,KAAK;AACjD;AAAA;AAAA,EACT;AAEJ;;;;;AC5WA,MAAe;AAAA,EACb,aAAa,MAAM,QAAQ,QAAQ;AAAA,IACjC,MAAM;AAAA,IACN,SAAS;AAAA,MACP,OAAO,MAAM,QAAQ,QAAQ,EAAE;AAAA,MAC/B,WAAW,OAAO,EAAE,QAAQ,QAAQ,WAAW,KAAM;AAAA,IACtD;AAAA,IACD,WAAW,OAAO,EAAE,QAAQ,QAAQ,WAAW,KAAI;AAAA,IACnD,iBAAiB,MAAM,QAAQ,QAAQ,EAAE,SAAS,KAAM;AAAA,EAC5D,CAAG;AAAA,EACD,WAAW,OAAO,EAAE,QAAQ,QAAQ,WAAW,KAAI;AAAA,EACnD,iBAAiB,MAAM,QAAQ,QAAQ,EAAE,SAAS,KAAM;AAC1D;;;;;ACSA,MAAM,sBAAsB;AAAA;AAAA,EAE1B,aAAa;AAAA,IACX,KAAK;AAAA,IACL,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,KAAK;AAAA,IACL,mBAAmB;AAAA,IACnB,eAAe;AAAA,EACjB;AAAA;AAAA,EAGA,YAAY;AAAA,IACV,KAAK;AAAA,MACH,oBAAoB;AAAA,IACtB;AAAA,IACA,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,KAAK;AAAA,IACL,mBAAmB;AAAA,IACnB,eAAe;AAAA,EACjB;AAAA;AAAA,EAGA,MAAM;AAAA,IACJ,KAAK;AAAA,IACL,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,KAAK;AAAA,IACL,mBAAmB;AAAA,IACnB,eAAe;AAAA;AAEnB;AAGA,MAAMC,eAAa;AACnB,MAAM,SAAS,oBAAoBA,YAAU,KAAK,oBAAoB;AA2DtE,QAAQ,IAAI,2DAA2D;AAGvE,IAAI,CAAC,QAAQ,UAAU;AACb,qBAAW,SAAS,aAAa,MAAM;AAC7C,iBAAa,MAAM,SAAS,GAAG,IAAI,CAAC;AAAA,EACtC;AACF;AAGA,IAAI,OAAO,eAAe,eAAe,CAAC,WAAW,SAAS;AAC5D,aAAW,UAAU;AACvB;AAGA,IAAI,CAAC,WAAW,cAAc;AAC5B,aAAW,eAAe;AAC5B;AAEA,IAAI,CAAC,WAAW,gBAAgB;AAC9B,aAAW,iBAAiB;AAC9B;AAGA,IAAI,OAAO,iBAAiB,aAAa;AACvC,QAAM,EAAE,cAAAC,kBAAiB;AAAA,0BAAAC,eAAA,UAAM,OAAO,2BAAQ;AAAA,2BAAAA,eAAA;AAAA;AACxC,uBAAeD,cAAa,UAAU;AAE5CA,gBAAa,UAAU,OAAO,SAAS,SAAS,MAAM;AAChD;AACF,aAAO,aAAa,KAAK,MAAM,MAAM,GAAG,IAAI;AAAA,aACrC,OAAO;AACd,UAAI,MAAM,QAAQ,SAAS,kBAAkB,GAAG;AAE9C,YAAI,SAAS,WAAW,KAAK,CAAC,GAAG;AAC/B,uBAAa,MAAM;AACjB,kBAAM,KAAK,CAAC;AAAA,WACb;AAAA;AAEI;AAAA;AAEH;AAAA;AAAA,EAEV;AACF;AAmBA,QAAQ,IAAI,iDAAiD;;AC1K7D,MAAM,iBAAiB;AAAA;AAAA,EAErB,YAAY;AAAA,IACV,MAAM,2BAAY,WAAW;AAAA,IAC7B,MAAM,SAAS,2BAAY,OAAO,KAAK;AAAA,IACvC,MAAM,2BAAY,WAAW;AAAA,IAC7B,UAAU,2BAAY,eAAe;AAAA,IACrC,UAAU,2BAAY,WAAW;AAAA;AAAA,IAGjC,GAAGE;AAAAA;AAAAA,IAGH,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,6BAA6B;AAAA,EAC/B;AAAA;AAAA,EAGA,MAAM;AAAA,IACJ,KAAK;AAAA,IACL,KAAKA,OAAgB,OAAO;AAAA,IAC5B,qBAAqB;AAAA,IACrB,sBAAsB;AAAA,IACtB,mBAAmBA,OAAgB,qBAAqB;AAAA,IACxD,oBAAoB;AAAA,IACpB,2BAA2B;AAAA,IAC3B,sBAAsB;AAAA,EACxB;AAAA;AAAA,EAGA,YAAY;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,IACX,WAAW;AAAA,IACX,qBAAqB;AAAA,EACvB;AAAA;AAAA,EAGA,OAAO;AAAA,IACL,WAAW;AAAA,EACb;AAAA;AAAA,EAGA,SAAS;AAAA,IACP,SAAS;AAAA,IACT,OAAO,2BAAY,aAAa;AAAA,IAChC,SAAS;AAAA,EACX;AAAA;AAAA,EAGA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,KAAK;AAAA;AAAA,IACL,SAAS;AAAA,EACX;AAAA;AAAA,EAGA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,aAAa;AAAA,IACb,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AAAA;AAAA,EAGA,aAAa;AAAA,IACX,SAAS;AAAA,IACT,UAAU;AAAA;AAAA,IACV,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA;AAAA,EAGA,cAAc;AAAA,IACZ,WAAW;AAAA,IACX,eAAe;AAAA,IACf,oBAAoB;AAAA,IACpB,kBAAkB;AAAA;AAEtB;AAGA,MAAM,qBAAqB;AAAA,EACzB,aAAa;AAAA,IACX,YAAY;AAAA,MACV,GAAG,eAAe;AAAA,MAClB,KAAK;AAAA,IACP;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,OAAO;AAAA,MACP,SAAS;AAAA;AAAA,EAEb;AAAA,EAEA,YAAY;AAAA,IACV,YAAY;AAAA,MACV,GAAG,eAAe;AAAA,MAClB,KAAK,2BAAY,mBAAmB,SAAS;AAAA,QAC3C,oBAAoB;AAAA,UAClB;AAAA,IACN;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,MACJ,GAAG,eAAe;AAAA,MAClB,KAAK;AAAA,MACL,KAAK;AAAA;AAAA,EAET;AAAA,EAEA,MAAM;AAAA,IACJ,YAAY;AAAA,MACV,GAAG,eAAe;AAAA,MAClB,UAAU,2BAAY,gBAAgB;AAAA,MACtC,KAAK;AAAA,IACP;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,MACJ,GAAG,eAAe;AAAA,MAClB,KAAK;AAAA,MACL,KAAK;AAAA;AAAA,EACP;AAEJ;AAGA,MAAM,aAAa;AACnB,MAAM,YAAY,mBAAmB,UAAU,KAAK,mBAAmB;AAGvE,MAAM,cAAc;AAAA,EAClB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,YAAY;AAAA,IACV,GAAG,eAAe;AAAA,IAClB,GAAG,UAAU;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,GAAG,eAAe;AAAA,IAClB,GAAG,UAAU;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,GAAG,eAAe;AAAA,IAClB,GAAG,UAAU;AAAA;AAEjB;AAGA,SAAS,eAAeN,SAAQ;AAC9B,QAAM,WAAW,CAAC,QAAQ,QAAQ,QAAQ,YAAY,UAAU;AAC1D,kBAAU,SAAS,OAAO,SAAO,CAACA,QAAO,WAAW,GAAG,CAAC;AAE1D,cAAQ,SAAS,GAAG;AACtB,UAAM,IAAI,MAAM,wCAAwC,QAAQ,KAAK,IAAI,CAAC,EAAE;AAAA;AAGvE;AACT;AAGA,eAAe,WAAW;AAE1B,QAAQ,IAAI,oDAAoD,UAAU,EAAE;AC3K5E,MAAM,EAAE,KAAS;AAGjB,MAAM,OAAO,IAAI,KAAK;AAAA,EACpB,MAAMO,YAAe,WAAW;AAAA,EAChC,MAAMA,YAAe,WAAW;AAAA,EAChC,MAAMA,YAAe,WAAW;AAAA,EAChC,UAAUA,YAAe,WAAW;AAAA,EACpC,UAAUA,YAAe,WAAW;AAAA,EACpC,KAAKA,YAAe,WAAW;AAAA,EAC/B,kBAAkBA,YAAe,WAAW;AAAA,EAC5C,WAAWA,YAAe,WAAW;AAAA,EACrC,6BAA6BA,YAAe,WAAW;AAAA;AAAA,EAGvD,KAAKA,YAAe,KAAK;AAAA,EACzB,KAAKA,YAAe,KAAK;AAAA,EACzB,qBAAqBA,YAAe,KAAK;AAAA,EACzC,sBAAsBA,YAAe,KAAK;AAAA,EAC1C,mBAAmBA,YAAe,KAAK;AAAA,EACvC,oBAAoBA,YAAe,KAAK;AAAA,EACxC,2BAA2BA,YAAe,KAAK;AAAA,EAC/C,sBAAsBA,YAAe,KAAK;AAC5C,CAAC;AAGD,KAAK,GAAG,WAAW,CAAC,WAAW;AACzB,MAAAA,YAAe,QAAQ,SAAS;AAClC,YAAQ,IAAI,kDAAkD;AAAA;AAElE,CAAC;AAED,KAAK,GAAG,SAAS,CAAC,KAAK,WAAW;AACxB,gBAAM,0CAA0C,GAAG;AAC7D,CAAC;AAED,KAAK,GAAG,UAAU,CAAC,WAAW;AAC5B,MAAIA,YAAe,QAAQ,WAAWA,YAAe,QAAQ,UAAU,SAAS;AAC9E,YAAQ,IAAI,6BAA6B;AAAA;AAE7C,CAAC;AAGD,eAAe,kBAAkB;AAC3B;AACI,mBAAS,MAAM,KAAK,QAAQ;AAClC,UAAM,OAAO,MAAMA,YAAe,YAAY,KAAK;AACnD,WAAO,QAAQ;AAEf,QAAIA,YAAe,QAAQ,WAAWA,YAAe,QAAQ,UAAU,SAAS;AAC9E,cAAQ,IAAI,8BAA8B;AAAA;AAErC;AAAA,WACA,OAAO;AACN,kBAAM,kCAAkC,MAAM,OAAO;AACtD;AAAA;AAEX;AAGA,IAAIA,YAAe,YAAY,SAAS;AACtB,oBAAE,KAAK,CAAC,YAAY;AAClC,QAAI,SAAS;AACH,kBAAI,gDAAgDA,YAAe,KAAK,GAAG,IAAIA,YAAe,KAAK,GAAG,YAAY;AAAA;AAAA,EAC5H,CACD;AAGW,+BAAiBA,YAAe,YAAY,QAAQ;AAClE;AAGA,eAAe,YAAY;AACrB;AACF,UAAM,KAAK,IAAI;AACf,YAAQ,IAAI,2CAA2C;AAAA,WAChD,OAAO;AACN,kBAAM,6BAA6B,MAAM,OAAO;AAAA;AAE5D;AAKO,MAAM,iBAAiB,YAAY;AACxC,MAAI,SAAS;AAET;AACF,YAAQ,IAAI,2CAA2C;AAC9C,mBAAM,KAAK,QAAQ;AAE5B,UAAM,SAAS,MAAM,OAAO,MAAM,6DAA6D;AAE/F,YAAQ,IAAI,4CAA4C;AACxD,YAAQ,IAAI,2BAA2B,OAAO,KAAK,CAAC,EAAE,YAAY;AAC1D,gBAAI,0BAA0B,OAAO,KAAK,CAAC,EAAE,iBAAiB,MAAM,GAAG,EAAE,CAAC,CAAC;AAE5E;AAAA,WACA,OAAO;AACN,kBAAM,6BAA6B,MAAM,OAAO;AACjD;AAAA,YACP;AACA,QAAI,QAAQ;AACV,aAAO,QAAQ;AAAA;AAAA,EACjB;AAEJ;AAKa,2BAAqB,OAAO,UAAU,MAAM;AACvD,WAAS,UAAU,GAAG,WAAW,SAAS,WAAW;AAC/C;AACF,cAAQ,IAAI,gBAAgB,OAAO,IAAI,OAAO,+BAA+B;AAEvE,0BAAc,MAAM,eAAe;AAEzC,UAAI,aAAa;AACf,gBAAQ,IAAI,4CAA4C;AACjD;AAAA,aACF;AACC,kBAAI,MAAM,kBAAkB;AAAA;AAAA,aAE7B,OAAO;AACd,cAAQ,MAAM,eAAe,OAAO,YAAY,MAAM,OAAO;AAE7D,UAAI,YAAY,SAAS;AACvB,cAAM,IAAI,MAAM,mCAAmC,OAAO,gBAAgB,MAAM,OAAO,EAAE;AAAA;AAI3F,YAAM,QAAQ,MAAO;AACb,kBAAI,gBAAgB,KAAK,kCAAkC;AACnE,YAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,KAAK,CAAC;AAAA;AAAA,EACzD;AAEJ;AAGA,QAAQ,GAAG,UAAU,SAAS;AAC9B,QAAQ,GAAG,WAAW,SAAS", "names": ["config", "pool", "DatabaseIntegrator", "currentEnv", "EventEmitter", "EventEmitter2", "pgCompatibility", "databaseConfig"], "ignoreList": [], "sources": ["../../src/database/services/DatabaseIntegrator.js", "../../src/database/services/DatabaseSingleton.js", "../../../../../database/services/databaseInstance.js", "../../src/database/config/pg-compatibility.js", "../../src/database/config/database.js", "../../src/database/config/pool.js"], "sourcesContent": ["/**\r\n * @file DatabaseIntegrator.js\r\n * @description Integrador dos componentes do database da V2 para a V3\r\n * @version 3.0.1\r\n * FLUXO: JOGOS → MÉTRICAS → ORQUESTRADOR → DATABASE → DASHBOARDS\r\n */\r\n\r\n// Importação condicional para PostgreSQL (apenas no backend)\r\nlet pgPool = null;\r\n\r\n// Função para carregar PostgreSQL dinamicamente\r\nasync function loadPostgreSQL() {\r\n  if (typeof window === 'undefined' && typeof process !== 'undefined') {\r\n    try {\r\n      const module = await import('../config/pool.js');\r\n      pgPool = module.default;\r\n      console.log('✅ Pool PostgreSQL carregado com sucesso no DatabaseIntegrator');\r\n      return pgPool;\r\n    } catch (error) {\r\n      console.warn('⚠️ PostgreSQL não disponível:', error.message);\r\n      return null;\r\n    }\r\n  }\r\n  return null;\r\n}\r\n\r\n// Mock do InputValidator se não existir\r\nconst InputValidator = {\r\n  validateGameMetricsInput: (userId, gameId, metrics) => {\r\n    const errors = [];\r\n    if (!userId) errors.push('userId é obrigatório');\r\n    if (!gameId) errors.push('gameId é obrigatório');\r\n    if (!metrics || typeof metrics !== 'object') errors.push('metrics deve ser um objeto');\r\n\r\n    return {\r\n      valid: errors.length === 0,\r\n      errors,\r\n      sanitized: {\r\n        userId: String(userId || '').trim(),\r\n        gameId: String(gameId || '').trim(),\r\n        metrics: metrics || {}\r\n      }\r\n    };\r\n  },\r\n\r\n  validateInteractionsInput: (sessionId, interactions) => {\r\n    const errors = [];\r\n    if (!sessionId) errors.push('sessionId é obrigatório');\r\n    if (!Array.isArray(interactions)) errors.push('interactions deve ser um array');\r\n\r\n    return {\r\n      valid: errors.length === 0,\r\n      errors,\r\n      sanitized: {\r\n        sessionId: String(sessionId || '').trim(),\r\n        interactions: Array.isArray(interactions) ? interactions : []\r\n      }\r\n    };\r\n  }\r\n};\r\n\r\n/**\r\n * @class StructuredLogger\r\n * @description Logger estruturado simplificado para DatabaseIntegrator\r\n */\r\nclass StructuredLogger {\r\n  constructor() {\r\n    this.component = 'DatabaseIntegrator';\r\n  }\r\n  \r\n  info(message, context = {}) {\r\n    console.info(`ℹ️ [${this.component}] ${message}`, context);\r\n  }\r\n  \r\n  warn(message, context = {}) {\r\n    console.warn(`⚠️ [${this.component}] ${message}`, context);\r\n  }\r\n  \r\n  error(message, context = {}) {\r\n    console.error(`❌ [${this.component}] ${message}`, context);\r\n  }\r\n  \r\n  debug(message, context = {}) {\r\n    console.debug(`🔍 [${this.component}] ${message}`, context);\r\n  }\r\n}\r\n\r\n/**\r\n * @class DatabaseIntegrator\r\n * @description Integra os recursos avançados do banco de dados da V2 com a V3\r\n */\r\nclass DatabaseIntegrator {\r\n  /**\r\n   * @constructor\r\n   * @param {Object} config - Configurações do integrador\r\n   */\r\n  constructor(config = {}) {\r\n    this.config = {\r\n      resilience: {\r\n        enabled: true,\r\n        monitoringEnabled: true,\r\n        retryAttempts: 3,\r\n        retryDelay: 1000,\r\n        timeoutDuration: 5000\r\n      },\r\n      cache: {\r\n        memoryMaxSize: 1000,\r\n        memoryTTL: 300000, // 5 minutes\r\n        redisEnabled: !!config.redisUrl,\r\n        redisUrl: config.redisUrl || null\r\n      },\r\n      metrics: {\r\n        enabled: true,\r\n        detailedLogging: true,\r\n        persistInterval: 60000 // 1 minute\r\n      },\r\n      ...config\r\n    };\r\n\r\n    this.logger = new StructuredLogger();\r\n    this.metricsBuffer = new Map();\r\n    this.intervals = new Map();\r\n\r\n    // Propriedades de conexão PostgreSQL\r\n    this.pool = pgPool;\r\n    this.connectionStatus = 'disconnected';\r\n    this.isBackend = typeof window === 'undefined' && typeof process !== 'undefined';\r\n\r\n    this.logger.info('DatabaseIntegrator: Initialized', {\r\n      config: {\r\n        resilience: this.config.resilience.enabled,\r\n        cache: this.config.cache.redisEnabled ? 'redis' : 'memory',\r\n        metrics: this.config.metrics.enabled,\r\n        backend: this.isBackend,\r\n        postgresql: !!this.pool\r\n      }\r\n    });\r\n\r\n    // Inicializar conexão se estivermos no backend\r\n    if (this.isBackend) {\r\n      this.initializeConnection();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Inicializa conexão com PostgreSQL\r\n   */\r\n  async initializeConnection() {\r\n    if (!this.isBackend) {\r\n      this.logger.info('PostgreSQL não disponível - usando modo mock');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Carregar PostgreSQL dinamicamente\r\n      const pool = await loadPostgreSQL();\r\n\r\n      if (pool) {\r\n        this.pool = pool;\r\n        // Testar conexão\r\n        const client = await this.pool.connect();\r\n        await client.query('SELECT 1 as test');\r\n        client.release();\r\n\r\n        this.connectionStatus = 'connected';\r\n        this.logger.info('✅ Conexão PostgreSQL estabelecida com sucesso');\r\n      } else {\r\n        throw new Error('Pool PostgreSQL não disponível');\r\n      }\r\n    } catch (error) {\r\n      this.connectionStatus = 'error';\r\n      this.logger.error('❌ Erro ao conectar com PostgreSQL:', error.message);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Verifica se está conectado com PostgreSQL\r\n   */\r\n  isConnected() {\r\n    return this.connectionStatus === 'connected' && !!this.pool;\r\n  }\r\n\r\n  /**\r\n   * Obtém status da conexão\r\n   */\r\n  getStatus() {\r\n    return {\r\n      status: this.connectionStatus,\r\n      connected: this.isConnected(),\r\n      type: this.isBackend ? 'PostgreSQL' : 'Mock',\r\n      backend: this.isBackend,\r\n      poolAvailable: !!this.pool\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Executa query SQL no PostgreSQL\r\n   */\r\n  async query(sql, params = []) {\r\n    if (!this.isConnected()) {\r\n      this.logger.warn('📄 [MOCK] Query executada:', { sql, params });\r\n      return [];\r\n    }\r\n\r\n    try {\r\n      const client = await this.pool.connect();\r\n      const result = await client.query(sql, params);\r\n      client.release();\r\n\r\n      this.logger.info('✅ Query executada com sucesso', {\r\n        sql: sql.substring(0, 100) + '...',\r\n        rowCount: result.rowCount\r\n      });\r\n\r\n      return result.rows;\r\n    } catch (error) {\r\n      this.logger.error('❌ Erro ao executar query:', error.message);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Armazena dados em uma tabela\r\n   */\r\n  async store(table, data) {\r\n    if (!this.isConnected()) {\r\n      this.logger.warn('📄 [MOCK] Dados armazenados em', table, ':', data);\r\n      return { success: true, id: Date.now() };\r\n    }\r\n\r\n    try {\r\n      const columns = Object.keys(data);\r\n      const values = Object.values(data);\r\n      const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');\r\n\r\n      const sql = `INSERT INTO ${table} (${columns.join(', ')}) VALUES (${placeholders}) RETURNING id`;\r\n\r\n      const result = await this.query(sql, values);\r\n\r\n      this.logger.info('✅ Dados armazenados com sucesso', { table, id: result[0]?.id });\r\n\r\n      return { success: true, id: result[0]?.id, ...data };\r\n    } catch (error) {\r\n      this.logger.error('❌ Erro ao armazenar dados:', error.message);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Interface unificada para persistência de métricas detalhadas\r\n   * @param {string} userId - ID do usuário\r\n   * @param {string} gameId - ID do jogo\r\n   * @param {Object} metrics - Objeto de métricas detalhadas\r\n   * @returns {Promise<Object>} Confirmação da persistência\r\n   */\r\n  async saveGameMetrics(userId, gameId, metrics) {\r\n    try {\r\n      const validation = InputValidator.validateGameMetricsInput(userId, gameId, metrics);\r\n      if (!validation.valid) {\r\n        throw new Error(`Invalid input: ${validation.errors.join(', ')}`);\r\n      }\r\n\r\n      const { userId: sanitizedUserId, gameId: sanitizedGameId, metrics: sanitizedMetrics } = validation.sanitized;\r\n\r\n      // Se conectado com PostgreSQL, salvar no banco real\r\n      if (this.isConnected()) {\r\n        const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n\r\n        const gameMetricsData = {\r\n          session_id: sessionId,\r\n          user_id: sanitizedUserId,\r\n          game_id: sanitizedGameId,\r\n          metrics_data: JSON.stringify(sanitizedMetrics),\r\n          accuracy: sanitizedMetrics.accuracy || 0,\r\n          response_time: sanitizedMetrics.averageResponseTime || 0,\r\n          engagement_score: sanitizedMetrics.engagementScore || 0,\r\n          created_at: new Date().toISOString()\r\n        };\r\n\r\n        const result = await this.store('game_metrics', gameMetricsData);\r\n\r\n        this.logger.info('✅ Game metrics saved to PostgreSQL', {\r\n          userId: sanitizedUserId,\r\n          gameId: sanitizedGameId,\r\n          sessionId: sessionId,\r\n          metricsCount: Object.keys(sanitizedMetrics).length\r\n        });\r\n\r\n        return {\r\n          sessionId: sessionId,\r\n          userId: sanitizedUserId,\r\n          gameId: sanitizedGameId,\r\n          timestamp: Date.now(),\r\n          status: 'success',\r\n          database: 'postgresql',\r\n          id: result.id\r\n        };\r\n      } else {\r\n        // Fallback para mock se não conectado\r\n        const result = {\r\n          sessionId: Date.now(),\r\n          userId: sanitizedUserId,\r\n          gameId: sanitizedGameId,\r\n          timestamp: Date.now(),\r\n          status: 'success',\r\n          database: 'mock'\r\n        };\r\n\r\n        this.logger.info('📄 [MOCK] Game metrics saved', {\r\n          userId: sanitizedUserId,\r\n          gameId: sanitizedGameId,\r\n          sessionId: result.sessionId,\r\n          metricsCount: Object.keys(sanitizedMetrics).length\r\n        });\r\n\r\n        return result;\r\n      }\r\n    } catch (error) {\r\n      this.logger.error('Failed to save game metrics', {\r\n        userId,\r\n        gameId,\r\n        error: error.message,\r\n        stack: error.stack\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Salva as interações detalhadas no banco de dados\r\n   * @param {number} sessionId - ID da sessão\r\n   * @param {Array<Object>} interactions - Lista de interações\r\n   * @returns {Promise<Object>} Confirmação da persistência\r\n   */\r\n  async saveInteractions(sessionId, interactions) {\r\n    try {\r\n      const validation = InputValidator.validateInteractionsInput(sessionId, interactions);\r\n      if (!validation.valid) {\r\n        throw new Error(`Invalid interactions input: ${validation.errors.join(', ')}`);\r\n      }\r\n\r\n      const { sessionId: sanitizedSessionId, interactions: sanitizedInteractions } = validation.sanitized;\r\n\r\n      // Simulação para o frontend\r\n      const result = {\r\n        sessionId: sanitizedSessionId,\r\n        interactionCount: sanitizedInteractions.length,\r\n        timestamp: Date.now(),\r\n        status: 'success'\r\n      };\r\n\r\n      this.logger.info('Interactions saved successfully (frontend)', {\r\n        sessionId: sanitizedSessionId,\r\n        interactionCount: sanitizedInteractions.length\r\n      });\r\n\r\n      return result;\r\n    } catch (error) {\r\n      this.logger.error('Failed to save interactions', {\r\n        sessionId,\r\n        error: error.message,\r\n        stack: error.stack\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Interface unificada para recuperação de dados com cache e resiliência\r\n   * @param {string} userId - ID do usuário\r\n   * @param {string} dataType - Tipo de dados ('progress', 'profile', 'predictions')\r\n   * @returns {Promise<Object>} Dados solicitados\r\n   */\r\n  async getUserData(userId, dataType) {\r\n    try {\r\n      const validation = InputValidator.validateUserDataInput(userId, dataType);\r\n      if (!validation.valid) {\r\n        throw new Error(`Invalid input: ${validation.errors.join(', ')}`);\r\n      }\r\n\r\n      const { userId: sanitizedUserId, dataType: sanitizedDataType } = validation.sanitized;\r\n\r\n      // Simulação para o frontend\r\n      const result = {\r\n        userId: sanitizedUserId,\r\n        dataType: sanitizedDataType,\r\n        data: {},\r\n        timestamp: Date.now(),\r\n        status: 'success'\r\n      };\r\n\r\n      this.logger.info('User data retrieved successfully (frontend)', {\r\n        userId: sanitizedUserId,\r\n        dataType: sanitizedDataType\r\n      });\r\n\r\n      return result;\r\n    } catch (error) {\r\n      this.logger.error('Failed to retrieve user data', {\r\n        userId,\r\n        dataType,\r\n        error: error.message,\r\n        stack: error.stack\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Obtém status completo do sistema\r\n   * @returns {Object} Status de todos os componentes\r\n   */\r\n  getCompleteSystemStatus() {\r\n    try {\r\n      const status = {\r\n        database: {\r\n          connected: true,\r\n          isConnected: true,\r\n          type: 'frontend-simulation',\r\n          status: 'operational'\r\n        },\r\n        resilience: { status: 'operational' },\r\n        sessionManager: { status: 'operational' },\r\n        metricsEngine: { status: 'operational' },\r\n        systemOrchestrator: { status: 'operational' },\r\n        metricsBuffer: {\r\n          size: this.metricsBuffer.size,\r\n          status: 'operational'\r\n        },\r\n        timestamp: new Date().toISOString()\r\n      };\r\n\r\n      return status;\r\n    } catch (error) {\r\n      this.logger.error('Error retrieving system status', { error: error.message, stack: error.stack });\r\n      return {\r\n        database: { connected: false, isConnected: false, type: 'frontend-simulation', status: 'failed' },\r\n        resilience: { status: 'failed' },\r\n        sessionManager: { status: 'failed' },\r\n        metricsEngine: { status: 'failed' },\r\n        systemOrchestrator: { status: 'failed' },\r\n        metricsBuffer: { size: 0, status: 'failed' },\r\n        timestamp: new Date().toISOString()\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Salva sessão completa no banco de dados\r\n   * @param {string} userId - ID do usuário\r\n   * @param {string} gameId - ID do jogo\r\n   * @param {Object} sessionData - Dados da sessão\r\n   * @returns {Promise<Object>} Confirmação da persistência\r\n   */\r\n  async saveCompleteSession(userId, gameId, sessionData) {\r\n    try {\r\n      if (this.isConnected()) {\r\n        const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n\r\n        const gameSessionData = {\r\n          session_id: sessionId,\r\n          user_id: userId,\r\n          game_id: gameId,\r\n          session_data: JSON.stringify(sessionData),\r\n          started_at: sessionData.startTime ? new Date(sessionData.startTime).toISOString() : new Date().toISOString(),\r\n          ended_at: sessionData.endTime ? new Date(sessionData.endTime).toISOString() : new Date().toISOString(),\r\n          duration: sessionData.duration || 0,\r\n          created_at: new Date().toISOString()\r\n        };\r\n\r\n        const result = await this.store('game_sessions', gameSessionData);\r\n\r\n        this.logger.info('✅ Complete session saved to PostgreSQL', {\r\n          userId, gameId, sessionId, duration: sessionData.duration\r\n        });\r\n\r\n        return { sessionId, userId, gameId, status: 'success', database: 'postgresql', id: result.id };\r\n      } else {\r\n        this.logger.info('📄 [MOCK] Complete session saved', { userId, gameId });\r\n        return { sessionId: Date.now(), userId, gameId, status: 'success', database: 'mock' };\r\n      }\r\n    } catch (error) {\r\n      this.logger.error('❌ Erro ao salvar sessão completa:', error.message);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Salva relatório de sessão\r\n   * @param {Object} report - Relatório da sessão\r\n   * @returns {Promise<Object>} Confirmação da persistência\r\n   */\r\n  async saveSessionReport(report) {\r\n    try {\r\n      if (this.isConnected()) {\r\n        const reportData = {\r\n          session_id: report.sessionId,\r\n          user_id: report.userId,\r\n          analysis_type: report.type || 'session_report',\r\n          analysis_data: JSON.stringify(report),\r\n          confidence_score: report.confidence || 0,\r\n          created_at: new Date().toISOString()\r\n        };\r\n\r\n        const result = await this.store('therapeutic_analysis', reportData);\r\n\r\n        this.logger.info('✅ Session report saved to PostgreSQL', {\r\n          sessionId: report.sessionId, type: report.type\r\n        });\r\n\r\n        return { status: 'success', database: 'postgresql', id: result.id };\r\n      } else {\r\n        this.logger.info('📄 [MOCK] Session report saved', { sessionId: report.sessionId });\r\n        return { status: 'success', database: 'mock', id: Date.now() };\r\n      }\r\n    } catch (error) {\r\n      this.logger.error('❌ Erro ao salvar relatório:', error.message);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Obtém status do sistema integrador\r\n   * @returns {Object} Status do sistema\r\n   */\r\n  getSystemStatus() {\r\n    return this.getCompleteSystemStatus();\r\n  }\r\n\r\n  /**\r\n   * Limpa recursos e finaliza intervalos\r\n   * @returns {Promise<void>}\r\n   */\r\n  async cleanup() {\r\n    try {\r\n      this.logger.info('Initiating DatabaseIntegrator cleanup...');\r\n\r\n      // Parar intervalos\r\n      for (const [intervalName, intervalId] of this.intervals.entries()) {\r\n        clearInterval(intervalId);\r\n        this.logger.info(`Interval stopped: ${intervalName}`);\r\n      }\r\n      this.intervals.clear();\r\n\r\n      this.logger.info('✅ DatabaseIntegrator cleanup completed');\r\n    } catch (error) {\r\n      this.logger.error('Error during DatabaseIntegrator cleanup', { error: error.message, stack: error.stack });\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\nexport default DatabaseIntegrator;\r\nexport { InputValidator, DatabaseIntegrator };\r\n", "/**\r\n * 🔄 DATABASE SINGLETON\r\n * Singleton para gerenciar instância única do DatabaseIntegrator\r\n * Portal Betina V3 - Versão organizada\r\n */\r\n\r\nlet DatabaseIntegrator = null;\r\nlet isLoading = false;\r\n\r\n/**\r\n * @class DatabaseSingleton\r\n * @description Singleton para garantir uma única instância do DatabaseIntegrator\r\n */\r\nclass DatabaseSingleton {\r\n  constructor() {\r\n    this.instance = null;\r\n    this.isInitializing = false;\r\n    this.logger = this.createLogger();\r\n    this.supportedGames = [\r\n      'ImageAssociation',\r\n      'MemoryGame',\r\n      'MusicalSequence',\r\n      'PadroesVisuais',\r\n      'ContagemNumeros',\r\n      'PatternMatching',\r\n      'SequenceLearning',\r\n      'CreativePainting',\r\n      'QuebraCabeca',\r\n      'LetterRecognition',\r\n      'ColorMatch'\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Cria logger estruturado\r\n   */\r\n  createLogger() {\r\n    const isBrowser = typeof window !== 'undefined';\r\n    return {\r\n      info: (...args) => console.info(\r\n        isBrowser ? '%c💾 [DB-SINGLETON]' : '💾 [DB-SINGLETON]',\r\n        isBrowser ? 'color: #2196F3' : '',\r\n        new Date().toISOString(),\r\n        ...args\r\n      ),\r\n      error: (...args) => console.error(\r\n        isBrowser ? '%c🔴 [DB-ERROR]' : '🔴 [DB-ERROR]',\r\n        isBrowser ? 'color: #F44336' : '',\r\n        new Date().toISOString(),\r\n        ...args\r\n      ),\r\n      warn: (...args) => console.warn(\r\n        isBrowser ? '%c🟡 [DB-WARN]' : '🟡 [DB-WARN]',\r\n        isBrowser ? 'color: #FF9800' : '',\r\n        new Date().toISOString(),\r\n        ...args\r\n      )\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Carrega o DatabaseIntegrator dinamicamente\r\n   */\r\n  async loadDatabaseIntegrator() {\r\n    if (DatabaseIntegrator) return DatabaseIntegrator;\r\n    if (isLoading) {\r\n      while (isLoading) {\r\n        await new Promise(resolve => setTimeout(resolve, 10));\r\n      }\r\n      return DatabaseIntegrator;\r\n    }\r\n\r\n    isLoading = true;\r\n    try {\r\n      // Carregar DatabaseIntegrator\r\n      const module = await import('./DatabaseIntegrator.js');\r\n      \r\n      DatabaseIntegrator = module.default || module.DatabaseIntegrator;\r\n      return DatabaseIntegrator;\r\n    } catch (error) {\r\n      this.logger.error('❌ Erro ao carregar DatabaseIntegrator:', error);\r\n      throw error;\r\n    } finally {\r\n      isLoading = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Retorna a instância singleton do DatabaseIntegrator\r\n   */\r\n  async getInstance() {\r\n    if (this.instance) {\r\n      this.logger.info('✅ Retornando instância existente do DatabaseIntegrator');\r\n      return this.instance;\r\n    }\r\n\r\n    if (this.isInitializing) {\r\n      this.logger.info('⏳ Aguardando inicialização do DatabaseIntegrator');\r\n      while (this.isInitializing) {\r\n        await new Promise(resolve => setTimeout(resolve, 10));\r\n      }\r\n      return this.instance;\r\n    }\r\n\r\n    this.isInitializing = true;\r\n\r\n    try {\r\n      const DatabaseIntegratorClass = await this.loadDatabaseIntegrator();\r\n      \r\n      const defaultConfig = {\r\n        resilience: {\r\n          enabled: true,\r\n          monitoringEnabled: true,\r\n          retryAttempts: 3,\r\n          retryDelay: 1000\r\n        },\r\n        cache: {\r\n          memoryMaxSize: 2000,\r\n          memoryTTL: 300000 // 5 minutos\r\n        },\r\n        metrics: {\r\n          enabled: true,\r\n          detailedLogging: import.meta.env?.DEV || false,\r\n          persistInterval: 60000 // 1 minuto\r\n        }\r\n      };\r\n\r\n      this.instance = new DatabaseIntegratorClass(defaultConfig);\r\n      this.logger.info('🔄 DatabaseIntegrator inicializado com sucesso');\r\n\r\n      return this.instance;\r\n    } catch (error) {\r\n      this.logger.error('❌ Erro ao inicializar DatabaseIntegrator:', error);\r\n      throw error;\r\n    } finally {\r\n      this.isInitializing = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Valida o esquema de dados para uma coleção\r\n   */\r\n  validateSchema(collection, data) {\r\n    try {\r\n      if (!data || typeof data !== 'object') {\r\n        this.logger.error('❌ Dados inválidos: deve ser um objeto', { collection });\r\n        return false;\r\n      }\r\n\r\n      if (collection === 'game_specific_analysis') {\r\n        if (!data.gameName || !this.supportedGames.includes(data.gameName)) {\r\n          this.logger.error('❌ gameName inválido ou não suportado', {\r\n            gameName: data.gameName,\r\n            supportedGames: this.supportedGames\r\n          });\r\n          return false;\r\n        }\r\n        \r\n        if (!data.sessionId || !data.childId || !data.timestamp) {\r\n          this.logger.error('❌ Campos obrigatórios ausentes', {\r\n            sessionId: data.sessionId,\r\n            childId: data.childId,\r\n            timestamp: data.timestamp\r\n          });\r\n          return false;\r\n        }\r\n\r\n        // Validações específicas por jogo\r\n        return this.validateGameSpecificData(data);\r\n      }\r\n\r\n      return true;\r\n    } catch (error) {\r\n      this.logger.error('❌ Erro ao validar esquema:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Valida dados específicos por jogo\r\n   */\r\n  validateGameSpecificData(data) {\r\n    switch (data.gameName) {\r\n      case 'ImageAssociation':\r\n        return this.validateImageAssociationData(data);\r\n      case 'MemoryGame':\r\n        return this.validateMemoryGameData(data);\r\n      case 'MusicalSequence':\r\n        return this.validateMusicalSequenceData(data);\r\n      case 'PadroesVisuais':\r\n        return this.validatePadroesVisuaisData(data);\r\n      case 'ContagemNumeros':\r\n        return this.validateContagemNumerosData(data);\r\n      case 'PatternMatching':\r\n        return this.validatePatternMatchingData(data);\r\n      case 'SequenceLearning':\r\n        return this.validateSequenceLearningData(data);\r\n      case 'CreativePainting':\r\n        return this.validateCreativePaintingData(data);\r\n      case 'QuebraCabeca':\r\n        return this.validateQuebraCabecaData(data);\r\n      case 'LetterRecognition':\r\n        return this.validateLetterRecognitionData(data);\r\n      case 'ColorMatch':\r\n        return this.validateColorMatchData(data);\r\n      default:\r\n        this.logger.error('❌ Jogo não reconhecido:', data.gameName);\r\n        return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validações específicas por jogo\r\n   */\r\n  validateImageAssociationData(data) {\r\n    return !!(data.analysisResult?.categoricalThinking && data.analysisResult?.semanticUnderstanding);\r\n  }\r\n\r\n  validateMemoryGameData(data) {\r\n    return !!(data.analysisResult?.workingMemory && data.analysisResult?.visualMemory);\r\n  }\r\n\r\n  validateMusicalSequenceData(data) {\r\n    return !!(data.analysisResult?.sequentialProcessing && data.analysisResult?.rhythmPerception);\r\n  }\r\n\r\n  validatePadroesVisuaisData(data) {\r\n    return !!(data.analysisResult?.patternRecognition && data.analysisResult?.spatialProcessing);\r\n  }\r\n\r\n  validateContagemNumerosData(data) {\r\n    return !!(data.analysisResult?.numberRecognition && data.analysisResult?.countingAbility);\r\n  }\r\n\r\n  validatePatternMatchingData(data) {\r\n    return !!(data.analysisResult?.patternRecognition && data.analysisResult?.visualProcessing);\r\n  }\r\n\r\n  validateSequenceLearningData(data) {\r\n    return !!(data.analysisResult?.sequentialMemory && data.analysisResult?.auditoryProcessing);\r\n  }\r\n\r\n  validateCreativePaintingData(data) {\r\n    return !!(data.analysisResult?.creativityAnalysis && data.analysisResult?.motorSkills);\r\n  }\r\n\r\n  validateQuebraCabecaData(data) {\r\n    return !!(data.analysisResult?.spatialReasoning && data.analysisResult?.problemSolving);\r\n  }\r\n\r\n  validateLetterRecognitionData(data) {\r\n    return !!(data.analysisResult?.letterRecognition && data.analysisResult?.visualProcessing);\r\n  }\r\n\r\n  validateColorMatchData(data) {\r\n    return !!(data.analysisResult?.colorRecognition && data.analysisResult?.visualDiscrimination);\r\n  }\r\n\r\n  /**\r\n   * Armazena dados em uma coleção com validação\r\n   */\r\n  async store(collection, data) {\r\n    try {\r\n      if (!this.validateSchema(collection, data)) {\r\n        throw new Error(`Validação de esquema falhou para coleção ${collection}`);\r\n      }\r\n\r\n      const instance = await this.getInstance();\r\n      await instance.store(collection, {\r\n        ...data,\r\n        storedAt: new Date().toISOString()\r\n      });\r\n      \r\n      this.logger.info('💾 Dados armazenados com sucesso', { \r\n        collection, \r\n        sessionId: data.sessionId,\r\n        gameName: data.gameName \r\n      });\r\n    } catch (error) {\r\n      this.logger.error('❌ Erro ao armazenar dados:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Consulta dados de uma coleção\r\n   */\r\n  async query(collection, query) {\r\n    try {\r\n      const instance = await this.getInstance();\r\n      const results = await instance.query(collection, query);\r\n      this.logger.info('🔍 Consulta realizada com sucesso', { collection, query });\r\n      return results || [];\r\n    } catch (error) {\r\n      this.logger.error('❌ Erro ao consultar dados:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Atualiza dados em uma coleção\r\n   */\r\n  async update(collection, query, data) {\r\n    try {\r\n      const instance = await this.getInstance();\r\n      const result = await instance.update(collection, query, data);\r\n      this.logger.info('🔄 Dados atualizados com sucesso', { collection, query });\r\n      return result;\r\n    } catch (error) {\r\n      this.logger.error('❌ Erro ao atualizar dados:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove dados de uma coleção\r\n   */\r\n  async delete(collection, query) {\r\n    try {\r\n      const instance = await this.getInstance();\r\n      const result = await instance.delete(collection, query);\r\n      this.logger.info('🗑️ Dados removidos com sucesso', { collection, query });\r\n      return result;\r\n    } catch (error) {\r\n      this.logger.error('❌ Erro ao remover dados:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Limpa cache e reinicia instância\r\n   */\r\n  async reset() {\r\n    try {\r\n      this.logger.info('🔄 Reiniciando DatabaseSingleton...');\r\n      \r\n      if (this.instance && this.instance.clearCache) {\r\n        await this.instance.clearCache();\r\n      }\r\n      \r\n      this.instance = null;\r\n      this.isInitializing = false;\r\n      \r\n      this.logger.info('✅ DatabaseSingleton reiniciado');\r\n    } catch (error) {\r\n      this.logger.error('❌ Erro ao reiniciar DatabaseSingleton:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Obtém estatísticas da instância\r\n   */\r\n  async getStats() {\r\n    try {\r\n      const instance = await this.getInstance();\r\n      if (instance && instance.getStats) {\r\n        return await instance.getStats();\r\n      }\r\n      return null;\r\n    } catch (error) {\r\n      this.logger.error('❌ Erro ao obter estatísticas:', error);\r\n      return null;\r\n    }\r\n  }\r\n}\r\n\r\nexport default DatabaseSingleton;\r\n", "export default {\n  getInstance: () => Promise.resolve({\n    mock: true,\n    manager: {\n      query: () => Promise.resolve([]),\n      getStatus: () => ({ status: 'mock', connected: true })\n    },\n    getStatus: () => ({ status: 'mock', connected: true }),\n    saveGameMetrics: () => Promise.resolve({ success: true })\n  }),\n  getStatus: () => ({ status: 'mock', connected: true }),\n  saveGameMetrics: () => Promise.resolve({ success: true })\n}", "/**\n * 🔧 POSTGRESQL COMPATIBILITY CONFIGURATION\n * Configurações de compatibilidade para PostgreSQL\n * Portal Betina V3\n */\n\n// Configurações globais para PostgreSQL\nif (typeof global !== 'undefined') {\n  // Configurar timezone padrão\n  process.env.TZ = process.env.TZ || 'America/Sao_Paulo';\n  \n  // Configurar encoding padrão\n  process.env.PGCLIENTENCODING = process.env.PGCLIENTENCODING || 'UTF8';\n  \n  // Configurar SSL se necessário\n  if (process.env.DB_SSL_ENABLED === 'true') {\n    process.env.PGSSLMODE = process.env.PGSSLMODE || 'require';\n  }\n}\n\n// Configurações de compatibilidade para diferentes ambientes\nconst compatibilityConfig = {\n  // Configurações para desenvolvimento\n  development: {\n    ssl: false,\n    connectionTimeoutMillis: 5000,\n    idleTimeoutMillis: 30000,\n    max: 10,\n    statement_timeout: 30000,\n    query_timeout: 30000\n  },\n  \n  // Configurações para produção\n  production: {\n    ssl: {\n      rejectUnauthorized: false\n    },\n    connectionTimeoutMillis: 10000,\n    idleTimeoutMillis: 60000,\n    max: 20,\n    statement_timeout: 60000,\n    query_timeout: 60000\n  },\n  \n  // Configurações para testes\n  test: {\n    ssl: false,\n    connectionTimeoutMillis: 3000,\n    idleTimeoutMillis: 10000,\n    max: 5,\n    statement_timeout: 10000,\n    query_timeout: 10000\n  }\n};\n\n// Aplicar configurações baseadas no ambiente\nconst currentEnv = process.env.NODE_ENV || 'development';\nconst config = compatibilityConfig[currentEnv] || compatibilityConfig.development;\n\n// Exportar configurações\nexport default config;\n\n// Configurações específicas para tipos de dados PostgreSQL\nexport const pgTypes = {\n  // Configurar parsing de tipos específicos\n  TIMESTAMP: 1114,\n  TIMESTAMPTZ: 1184,\n  JSON: 114,\n  JSONB: 3802,\n  UUID: 2950,\n  NUMERIC: 1700,\n  BIGINT: 20\n};\n\n// Funções de compatibilidade\nexport const pgUtils = {\n  /**\n   * Escapar strings para PostgreSQL\n   */\n  escapeString: (str) => {\n    if (typeof str !== 'string') return str;\n    return str.replace(/'/g, \"''\");\n  },\n  \n  /**\n   * Formatar timestamp para PostgreSQL\n   */\n  formatTimestamp: (date) => {\n    if (!date) return null;\n    if (typeof date === 'string') date = new Date(date);\n    return date.toISOString();\n  },\n  \n  /**\n   * Converter JSON para PostgreSQL JSONB\n   */\n  toJsonb: (obj) => {\n    if (obj === null || obj === undefined) return null;\n    return JSON.stringify(obj);\n  },\n  \n  /**\n   * Converter JSONB para objeto JavaScript\n   */\n  fromJsonb: (jsonbStr) => {\n    if (!jsonbStr) return null;\n    try {\n      return JSON.parse(jsonbStr);\n    } catch (error) {\n      console.warn('Erro ao parsear JSONB:', error);\n      return null;\n    }\n  }\n};\n\n// Patches de compatibilidade para Node.js v22+\nconsole.log('🔧 Aplicando patches de compatibilidade para Node.js v22+');\n\n// Polyfill para process.nextTick se não existir\nif (!process.nextTick) {\n  process.nextTick = function(callback, ...args) {\n    setImmediate(() => callback(...args));\n  };\n}\n\n// Garantir que process.nextTick está disponível globalmente\nif (typeof globalThis !== 'undefined' && !globalThis.process) {\n  globalThis.process = process;\n}\n\n// Garantir que setImmediate e clearImmediate estão disponíveis\nif (!globalThis.setImmediate) {\n  globalThis.setImmediate = setImmediate;\n}\n\nif (!globalThis.clearImmediate) {\n  globalThis.clearImmediate = clearImmediate;\n}\n\n// Patch para EventEmitter com fallback para process.nextTick\nif (typeof EventEmitter !== 'undefined') {\n  const { EventEmitter } = await import('events');\n  const originalEmit = EventEmitter.prototype.emit;\n\n  EventEmitter.prototype.emit = function(type, ...args) {\n    try {\n      return originalEmit.call(this, type, ...args);\n    } catch (error) {\n      if (error.message.includes('process.nextTick')) {\n        // Fallback para setImmediate se process.nextTick falhar\n        if (type === 'error' && args[0]) {\n          setImmediate(() => {\n            throw args[0];\n          });\n        }\n        return false;\n      }\n      throw error;\n    }\n  };\n}\n\nexport const applyPatches = () => {\n  console.log('🔧 Aplicando patches de compatibilidade para Node.js v22+');\n  \n  // Garantir que todas as funções assíncronas estão disponíveis\n  if (!global.process) {\n    global.process = process;\n  }\n  \n  if (!process.nextTick) {\n    process.nextTick = function(callback, ...args) {\n      setImmediate(() => callback(...args));\n    };\n  }\n  \n  console.log('✅ Patches aplicados com sucesso');\n};\n\nconsole.log('✅ PostgreSQL compatibility configuration loaded');\n", "/**\n * 💾 DATABASE CONFIGURATION\n * Configuração principal do banco de dados PostgreSQL\n * Portal Betina V3\n */\n\nimport pgCompatibility from './pg-compatibility.js';\n\n// Configuração base do banco de dados\nconst databaseConfig = {\n  // Configurações de conexão\n  connection: {\n    host: process.env.DB_HOST || 'localhost',\n    port: parseInt(process.env.DB_PORT) || 5432,\n    user: process.env.DB_USER || 'betina_user',\n    password: process.env.DB_PASSWORD || 'betina_password',\n    database: process.env.DB_NAME || 'betina_db',\n    \n    // Aplicar configurações de compatibilidade\n    ...pgCompatibility,\n    \n    // Configurações específicas\n    application_name: 'Portal_Betina_V3',\n    keepAlive: true,\n    keepAliveInitialDelayMillis: 10000\n  },\n  \n  // Pool de conexões\n  pool: {\n    min: 2,\n    max: pgCompatibility.max || 10,\n    createTimeoutMillis: 8000,\n    acquireTimeoutMillis: 8000,\n    idleTimeoutMillis: pgCompatibility.idleTimeoutMillis || 30000,\n    reapIntervalMillis: 1000,\n    createRetryIntervalMillis: 100,\n    propagateCreateError: false\n  },\n  \n  // Configurações de migração\n  migrations: {\n    directory: './src/database/migrations',\n    tableName: 'knex_migrations',\n    extension: 'js',\n    disableTransactions: false\n  },\n  \n  // Configurações de seeds\n  seeds: {\n    directory: './src/database/seeds'\n  },\n  \n  // Configurações de logging\n  logging: {\n    enabled: process.env.NODE_ENV === 'development',\n    level: process.env.LOG_LEVEL || 'info',\n    queries: process.env.NODE_ENV === 'development'\n  },\n  \n  // Configurações de cache\n  cache: {\n    enabled: true,\n    ttl: 300000, // 5 minutos\n    maxSize: 1000\n  },\n  \n  // Configurações de retry\n  retry: {\n    enabled: true,\n    maxAttempts: 3,\n    delay: 1000,\n    backoff: 'exponential'\n  },\n  \n  // Configurações de health check\n  healthCheck: {\n    enabled: true,\n    interval: 30000, // 30 segundos\n    timeout: 5000,\n    query: 'SELECT 1 as health_check'\n  },\n  \n  // Configurações específicas para métricas multissensoriais\n  multisensory: {\n    batchSize: 100,\n    flushInterval: 5000,\n    compressionEnabled: true,\n    indexingStrategy: 'btree_gin'\n  }\n};\n\n// Configurações específicas por ambiente\nconst environmentConfigs = {\n  development: {\n    connection: {\n      ...databaseConfig.connection,\n      ssl: false\n    },\n    logging: {\n      enabled: true,\n      level: 'debug',\n      queries: true\n    }\n  },\n  \n  production: {\n    connection: {\n      ...databaseConfig.connection,\n      ssl: process.env.DB_SSL_ENABLED === 'true' ? {\n        rejectUnauthorized: false\n      } : false\n    },\n    logging: {\n      enabled: false,\n      level: 'error',\n      queries: false\n    },\n    pool: {\n      ...databaseConfig.pool,\n      max: 20,\n      min: 5\n    }\n  },\n  \n  test: {\n    connection: {\n      ...databaseConfig.connection,\n      database: process.env.DB_NAME_TEST || 'betina_db_test',\n      ssl: false\n    },\n    logging: {\n      enabled: false,\n      level: 'error',\n      queries: false\n    },\n    pool: {\n      ...databaseConfig.pool,\n      max: 5,\n      min: 1\n    }\n  }\n};\n\n// Aplicar configurações do ambiente atual\nconst currentEnv = process.env.NODE_ENV || 'development';\nconst envConfig = environmentConfigs[currentEnv] || environmentConfigs.development;\n\n// Mesclar configurações\nconst finalConfig = {\n  ...databaseConfig,\n  ...envConfig,\n  connection: {\n    ...databaseConfig.connection,\n    ...envConfig.connection\n  },\n  pool: {\n    ...databaseConfig.pool,\n    ...envConfig.pool\n  },\n  logging: {\n    ...databaseConfig.logging,\n    ...envConfig.logging\n  }\n};\n\n// Validar configurações\nfunction validateConfig(config) {\n  const required = ['host', 'port', 'user', 'password', 'database'];\n  const missing = required.filter(key => !config.connection[key]);\n  \n  if (missing.length > 0) {\n    throw new Error(`Configurações obrigatórias faltando: ${missing.join(', ')}`);\n  }\n  \n  return true;\n}\n\n// Validar antes de exportar\nvalidateConfig(finalConfig);\n\nconsole.log(`✅ Database configuration loaded for environment: ${currentEnv}`);\n\nexport default finalConfig;\n", "/**\r\n * 💾 DATABASE CONNECTION POOL\r\n * Pool de conexões PostgreSQL\r\n * Portal Betina V3\r\n */\r\n\r\nimport pg from 'pg';\r\nimport databaseConfig from './database.js';\r\n\r\nconst { Pool } = pg;\r\n\r\n// Criar pool de conexões usando a configuração\r\nconst pool = new Pool({\r\n  host: databaseConfig.connection.host,\r\n  port: databaseConfig.connection.port,\r\n  user: databaseConfig.connection.user,\r\n  password: databaseConfig.connection.password,\r\n  database: databaseConfig.connection.database,\r\n  ssl: databaseConfig.connection.ssl,\r\n  application_name: databaseConfig.connection.application_name,\r\n  keepAlive: databaseConfig.connection.keepAlive,\r\n  keepAliveInitialDelayMillis: databaseConfig.connection.keepAliveInitialDelayMillis,\r\n  \r\n  // Configurações do pool\r\n  min: databaseConfig.pool.min,\r\n  max: databaseConfig.pool.max,\r\n  createTimeoutMillis: databaseConfig.pool.createTimeoutMillis,\r\n  acquireTimeoutMillis: databaseConfig.pool.acquireTimeoutMillis,\r\n  idleTimeoutMillis: databaseConfig.pool.idleTimeoutMillis,\r\n  reapIntervalMillis: databaseConfig.pool.reapIntervalMillis,\r\n  createRetryIntervalMillis: databaseConfig.pool.createRetryIntervalMillis,\r\n  propagateCreateError: databaseConfig.pool.propagateCreateError\r\n});\r\n\r\n// Event listeners para monitoramento\r\npool.on('connect', (client) => {\r\n  if (databaseConfig.logging.enabled) {\r\n    console.log('✅ Nova conexão estabelecida com o banco de dados');\r\n  }\r\n});\r\n\r\npool.on('error', (err, client) => {\r\n  console.error('❌ Erro inesperado no pool de conexões:', err);\r\n});\r\n\r\npool.on('remove', (client) => {\r\n  if (databaseConfig.logging.enabled && databaseConfig.logging.level === 'debug') {\r\n    console.log('🔄 Cliente removido do pool');\r\n  }\r\n});\r\n\r\n// Health check do pool\r\nasync function checkPoolHealth() {\r\n  try {\r\n    const client = await pool.connect();\r\n    await client.query(databaseConfig.healthCheck.query);\r\n    client.release();\r\n    \r\n    if (databaseConfig.logging.enabled && databaseConfig.logging.level === 'debug') {\r\n      console.log('💚 Pool de conexões saudável');\r\n    }\r\n    return true;\r\n  } catch (error) {\r\n    console.error('❌ Health check do pool falhou:', error.message);\r\n    return false;\r\n  }\r\n}\r\n\r\n// Executar health check inicial\r\nif (databaseConfig.healthCheck.enabled) {\r\n  checkPoolHealth().then((healthy) => {\r\n    if (healthy) {\r\n      console.log(`✅ Pool de conexões inicializado com sucesso (${databaseConfig.pool.min}-${databaseConfig.pool.max} conexões)`);\r\n    }\r\n  });\r\n  \r\n  // Configurar health check periódico\r\n  setInterval(checkPoolHealth, databaseConfig.healthCheck.interval);\r\n}\r\n\r\n// Função para finalizar o pool graciosamente\r\nasync function closePool() {\r\n  try {\r\n    await pool.end();\r\n    console.log('✅ Pool de conexões finalizado com sucesso');\r\n  } catch (error) {\r\n    console.error('❌ Erro ao finalizar pool:', error.message);\r\n  }\r\n}\r\n\r\n/**\r\n * Testa a conexão com o banco de dados\r\n */\r\nexport const testConnection = async () => {\r\n  let client = null;\r\n  \r\n  try {\r\n    console.log('🔍 Testando conexão com banco de dados...');\r\n    client = await pool.connect();\r\n    \r\n    const result = await client.query('SELECT NOW() as current_time, version() as postgres_version');\r\n    \r\n    console.log('✅ Conexão com banco de dados bem-sucedida!');\r\n    console.log('📅 Horário do servidor:', result.rows[0].current_time);\r\n    console.log('🗄️ Versão PostgreSQL:', result.rows[0].postgres_version.split(' ')[0]);\r\n    \r\n    return true;\r\n  } catch (error) {\r\n    console.error('❌ Erro ao testar conexão:', error.message);\r\n    return false;\r\n  } finally {\r\n    if (client) {\r\n      client.release();\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Inicializa o banco de dados com retry automático\r\n */\r\nexport const initializeDatabase = async (retries = 3) => {\r\n  for (let attempt = 1; attempt <= retries; attempt++) {\r\n    try {\r\n      console.log(`🚀 Tentativa ${attempt}/${retries} de inicialização do banco...`);\r\n      \r\n      const isConnected = await testConnection();\r\n      \r\n      if (isConnected) {\r\n        console.log('✅ Banco de dados inicializado com sucesso!');\r\n        return pool;\r\n      } else {\r\n        throw new Error('Falha na conexão');\r\n      }\r\n    } catch (error) {\r\n      console.error(`❌ Tentativa ${attempt} falhou:`, error.message);\r\n      \r\n      if (attempt === retries) {\r\n        throw new Error(`Falha ao inicializar banco após ${retries} tentativas: ${error.message}`);\r\n      }\r\n      \r\n      // Aguardar antes da próxima tentativa\r\n      const delay = 5000 * attempt;\r\n      console.log(`⏳ Aguardando ${delay}ms antes da próxima tentativa...`);\r\n      await new Promise(resolve => setTimeout(resolve, delay));\r\n    }\r\n  }\r\n};\r\n\r\n// Interceptar sinais de finalização\r\nprocess.on('SIGINT', closePool);\r\nprocess.on('SIGTERM', closePool);\r\n\r\nexport { pool, checkPoolHealth, closePool };\r\nexport default pool;\r\n"], "file": "assets/database-CBRromfB.js"}