# Guia de Orquestração Terapêutica - Portal Betina V3

## Visão Geral

Este guia descreve a implementação do Orquestrador Terapêutico no Portal Betina V3, que integra as funcionalidades avançadas de análise e recomendação terapêutica do V2 com a nova arquitetura resiliente do V3.

## Componentes Integrados

1. **TherapeuticOrchestrator**: Serviço principal que gerencia o fluxo terapêutico
2. **OrchestratorAdapter**: Adaptador que facilita a integração do orquestrador V2 com a arquitetura V3
3. **IntegratedSystem**: Sistema que encapsula todos os serviços, incluindo o orquestrador terapêutico

## Fluxo de Dados

```
[Jogos] → [Coleta de Métricas] → [TherapeuticOrchestrator] → [Processamento] → [Recomendações]
```

## Como Utilizar

### Inicialização Padrão

O orquestrador terapêutico é inicializado automaticamente como parte do sistema integrado:

```javascript
// Importações
import { createIntegratedSystem } from './utils/createIntegratedSystem';

// Criar o sistema integrado
const integratedSystem = await createIntegratedSystem();

// O sistema já inclui o orquestrador terapêutico inicializado
```

### Processamento de Métricas de Jogos

```javascript
// Processar métricas de um jogo
const result = await integratedSystem.processGameMetrics(
  'user123',  // userId
  'game456',  // gameId
  {           // métricas coletadas
    accuracy: 85,
    timeSpent: 240,
    interactions: 15,
    responseTime: 800
  }
);

// Resultado contém insights e recomendações
console.log(result.insights);
console.log(result.recommendations);
```

### Obtenção de Recomendações Terapêuticas

```javascript
// Obter recomendações para um usuário
const recommendations = await integratedSystem.getTherapeuticRecommendations('user123');

// Usar recomendações para personalizar a experiência
if (recommendations.success && recommendations.hasData) {
  console.log('Visão geral:', recommendations.overview);
  console.log('Recomendações:', recommendations.recommendations);
}
```

### Eventos Terapêuticos via Event Bus

O sistema também suporta o modelo de eventos:

```javascript
// Processar métricas via eventos
const result = await integratedSystem.dispatchEvent('therapeutic:process-metrics', {
  userId: 'user123',
  gameId: 'game456',
  metrics: {
    accuracy: 85,
    timeSpent: 240
  }
});

// Obter recomendações via eventos
const recommendations = await integratedSystem.dispatchEvent('therapeutic:get-recommendations', {
  userId: 'user123'
});
```

### Integração com Hooks

Para integração com componentes React, utilize os hooks dedicados:

```javascript
// No componente React
import { useGameOrchestrator } from '../hooks/useGameOrchestrator';

function GameComponent() {
  const { processMetrics, recommendations, loading } = useGameOrchestrator();
  
  // Processar métricas ao completar um jogo
  const handleGameComplete = async (gameData) => {
    await processMetrics(userId, gameId, gameData);
  };
  
  // Renderizar recomendações
  return (
    <div>
      {loading ? <LoadingIndicator /> : (
        <RecommendationsList recommendations={recommendations} />
      )}
    </div>
  );
}
```

## Modos Terapêuticos

O sistema suporta diferentes modos terapêuticos:

1. **ASSESSMENT**: Modo de avaliação inicial
2. **MONITORING**: Modo de monitoramento contínuo (padrão)
3. **INTERVENTION**: Modo para intervenções terapêuticas (quando detectados problemas)
4. **ADAPTIVE**: Modo adaptativo personalizado

O modo é automaticamente ajustado com base nos padrões detectados nas métricas do usuário.

## Categorias de Métricas Suportadas

- **ENGAGEMENT**: Métricas de engajamento do usuário
- **COGNITIVE**: Métricas cognitivas
- **BEHAVIORAL**: Métricas comportamentais
- **SENSORY**: Métricas sensoriais
- **EMOTIONAL**: Métricas emocionais
- **SOCIAL**: Métricas sociais
- **COMMUNICATION**: Métricas de comunicação
- **MOTOR**: Métricas motoras

## Resiliência

O orquestrador terapêutico é implementado com padrões de resiliência:

- **Circuit Breakers**: Para evitar falhas em cascata
- **Retry**: Para operações que podem falhar temporariamente
- **Fallback**: Fornece recomendações básicas mesmo em caso de falha
- **Monitoramento**: Rastreamento de estado e desempenho

## Exemplos Práticos

### Integração Completa em um Jogo

```javascript
import React, { useEffect, useState } from 'react';
import { useGameOrchestrator } from '../hooks/useGameOrchestrator';
import { useResilientDatabase } from '../hooks/useResilientDatabase';

function GameWithTherapeuticOrchestration({ userId, gameId }) {
  const { processMetrics, recommendations } = useGameOrchestrator();
  const { saveGameMetrics } = useResilientDatabase();
  const [gameState, setGameState] = useState({});
  
  // Processar métricas ao completar o jogo
  const handleGameComplete = async (metrics) => {
    // Salvar métricas no banco de dados
    await saveGameMetrics(userId, gameId, metrics);
    
    // Processar métricas no orquestrador terapêutico
    const result = await processMetrics(userId, gameId, metrics);
    
    // Aplicar recomendações
    if (result.success) {
      applyTherapeuticRecommendations(result.recommendations);
    }
  };
  
  // Renderizar o jogo com adaptações baseadas nas recomendações
  return (
    <div>
      <GameComponent 
        difficulty={recommendedDifficulty(recommendations)}
        adaptations={generateAdaptations(recommendations)}
        onComplete={handleGameComplete}
      />
      
      {recommendations.length > 0 && (
        <TherapeuticFeedback recommendations={recommendations} />
      )}
    </div>
  );
}
```

## Próximos Passos

1. Implementar testes específicos para o orquestrador terapêutico
2. Expandir integrações para novos jogos
3. Desenvolver dashboards para visualização de métricas terapêuticas
4. Aprimorar algoritmos de recomendação baseados em dados reais

---

Desenvolvido pela equipe Portal Betina - v3.0.0
