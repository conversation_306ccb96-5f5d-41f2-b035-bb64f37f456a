# 🔄 GUIA DE MIGRAÇÃO DE HOOKS - Portal Betina V3

## 📋 Resumo das Mudanças

### ✅ HOOKS PRINCIPAIS (usar em todos os jogos):
- `useUnifiedGameLogic` - Lógica base de jogos
- `useTherapeuticOrchestrator` - An<PERSON><PERSON><PERSON> terapêutica  
- `useMultisensoryIntegration` - Sensores e neurodivergência
- `useGameManager` - **NOVO**: Gerenciamento completo (sessões + métricas)

### ❌ HOOKS DEPRECADOS (substituir):

#### useGameSession → useGameManager
```javascript
// ❌ ANTES (deprecado)
import { useGameSession } from './hooks/useGameSession';
const { startSession, endSession } = useGameSession(gameId);

// ✅ DEPOIS (recomendado)
import { useGameManager } from './hooks/useGameManager';
const { startSession, endSession } = useGameManager(gameType);
```

#### useGameMetrics → useGameManager
```javascript
// ❌ ANTES (deprecado)
import { useGameMetrics } from './hooks/useGameMetrics';
const { recordInteraction, getMetrics } = useGameMetrics(gameId, userId);

// ✅ DEPOIS (recomendado)
import { useGameManager } from './hooks/useGameManager';
const { recordInteraction, getCurrentMetrics } = useGameManager(gameType);
```

#### useGameOrchestrator → useGameManager
```javascript
// ❌ ANTES (deprecado)
import { useGameOrchestrator } from './hooks/useGameOrchestrator';
const { adjustDifficulty, getRecommendations } = useGameOrchestrator({userId});

// ✅ DEPOIS (recomendado)
import { useGameManager } from './hooks/useGameManager';
const { adjustDifficulty, getRecommendations } = useGameManager(gameType);
```

## 🎮 Padrão Recomendado para Novos Jogos

```javascript
import React from 'react';
import { 
  useUnifiedGameLogic,
  useTherapeuticOrchestrator,
  useMultisensoryIntegration,
  useGameManager
} from '../../hooks';

export function NovoJogoGame() {
  // Lógica base do jogo
  const gameLogic = useUnifiedGameLogic('novo-jogo');
  
  // Análise terapêutica
  const therapeutic = useTherapeuticOrchestrator();
  
  // Sensores multissensoriais
  const multisensory = useMultisensoryIntegration('novo-jogo');
  
  // Gerenciamento completo (sessões + métricas + orquestração)
  const gameManager = useGameManager('novo-jogo');
  
  // ... resto do componente
}
```

## ⏰ Cronograma de Depreciação

- **Fase 1 (Atual)**: Hooks marcados como deprecados, ainda funcionais
- **Fase 2 (Próxima versão)**: Avisos de console para uso de hooks deprecados  
- **Fase 3 (Versão futura)**: Remoção completa dos hooks deprecados

## 🎯 Benefícios da Consolidação

1. **Menos Imports**: 4 hooks ao invés de 7+ 
2. **API Consistente**: Interface unificada para gerenciamento
3. **Melhor Performance**: Menos hooks = menos re-renders
4. **Manutenção Simplificada**: Código centralizado
5. **Arquitetura Mais Limpa**: Separação clara de responsabilidades
