{
  "timestamp": "2025-06-24T13:11:27.225Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:236:26)"
  }
}
{
  "timestamp": "2025-06-24T13:12:33.669Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:13:39.965Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:14:55.719Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:16:30.810Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:16:58.451Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:17:23.185Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:36:42.228Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:37:48.053Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:41:01.931Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:42:12.570Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:43:46.472Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:44:25.797Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:46:47.742Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T13:51:15.281Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T14:07:14.431Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T14:53:39.986Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T15:22:48.631Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T15:25:15.681Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T15:27:40.941Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T19:51:14.776Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T20:17:49.471Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T20:26:01.041Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T20:29:28.124Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T20:37:44.678Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T20:39:42.588Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T20:45:14.998Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3045:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseServiceExtended.getInstance (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:164:42)\n    at getDatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:30:55)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/core/index.js:53:28\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)"
  }
}
{
  "timestamp": "2025-06-24T22:11:01.973Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3049:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at new DatabaseService (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseService.js:23:28)\n    at new DatabaseServiceExtended (file:///C:/Projetos/protalbetinav3/src/api/services/core/DatabaseServiceExtended.js:19:5)\n    at DatabaseIntegrator._initializeComponents (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseIntegrator.js:51:28)\n    at new DatabaseIntegrator (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseIntegrator.js:40:10)\n    at new DatabaseSingleton (file:///C:/Projetos/protalbetinav3/src/api/services/databaseInstance.js:32:36)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/databaseInstance.js:59:27\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)"
  }
}
{
  "timestamp": "2025-06-24T22:11:02.017Z",
  "level": "ERROR",
  "message": "Erro ao conectar ao SystemOrchestrator:",
  "meta": {},
  "error": {
    "name": "ReferenceError",
    "message": "sharedLogger is not defined",
    "stack": "ReferenceError: sharedLogger is not defined\n    at getSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:3049:15)\n    at SessionAnalyzer.connectToSystemOrchestrator (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:836:33)\n    at new SessionAnalyzer (file:///C:/Projetos/protalbetinav3/src/api/services/analysis/SessionAnalyzer.js:56:10)\n    at DatabaseIntegrator._initializeComponents (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseIntegrator.js:61:28)\n    at new DatabaseIntegrator (file:///C:/Projetos/protalbetinav3/src/api/services/DatabaseIntegrator.js:40:10)\n    at new DatabaseSingleton (file:///C:/Projetos/protalbetinav3/src/api/services/databaseInstance.js:32:36)\n    at file:///C:/Projetos/protalbetinav3/src/api/services/databaseInstance.js:59:27\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)\n    at async importModuleDynamicallyWrapper (node:internal/vm/module:436:15)"
  }
}
{
  "timestamp": "2025-06-24T22:50:00.540Z",
  "level": "ERROR",
  "message": "Erro ao analisar padrões terapêuticos:",
  "meta": {},
  "error": {
    "name": "TypeError",
    "message": "Cannot read properties of undefined (reading 'timeEngaged')",
    "stack": "TypeError: Cannot read properties of undefined (reading 'timeEngaged')\n    at SystemOrchestrator.identifyEngagementPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:985:25)\n    at SystemOrchestrator.analyzeTherapeuticPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:923:34)\n    at SystemOrchestrator.processTherapeuticData (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:755:35)\n    at testIntegrationFlow (file:///C:/Projetos/protalbetinav3/test-final-integration.js:105:46)"
  }
}
{
  "timestamp": "2025-06-24T22:51:01.948Z",
  "level": "ERROR",
  "message": "Erro ao analisar padrões terapêuticos:",
  "meta": {},
  "error": {
    "name": "TypeError",
    "message": "Cannot read properties of undefined (reading 'timeEngaged')",
    "stack": "TypeError: Cannot read properties of undefined (reading 'timeEngaged')\n    at SystemOrchestrator.identifyEngagementPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:985:25)\n    at SystemOrchestrator.analyzeTherapeuticPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:923:34)\n    at SystemOrchestrator.processTherapeuticData (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:755:35)\n    at testIntegrationFlow (file:///C:/Projetos/protalbetinav3/test-final-integration.js:105:46)"
  }
}
{
  "timestamp": "2025-06-24T22:52:52.533Z",
  "level": "ERROR",
  "message": "Erro ao analisar padrões terapêuticos:",
  "meta": {},
  "error": {
    "name": "TypeError",
    "message": "Cannot read properties of undefined (reading 'timeEngaged')",
    "stack": "TypeError: Cannot read properties of undefined (reading 'timeEngaged')\n    at SystemOrchestrator.identifyEngagementPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:985:25)\n    at SystemOrchestrator.analyzeTherapeuticPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:923:34)\n    at SystemOrchestrator.processTherapeuticData (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:755:35)\n    at testIntegrationFlow (file:///C:/Projetos/protalbetinav3/test-final-integration.js:105:46)"
  }
}
{
  "timestamp": "2025-06-24T22:53:12.443Z",
  "level": "ERROR",
  "message": "Erro ao analisar padrões terapêuticos:",
  "meta": {},
  "error": {
    "name": "TypeError",
    "message": "Cannot read properties of undefined (reading 'timeEngaged')",
    "stack": "TypeError: Cannot read properties of undefined (reading 'timeEngaged')\n    at SystemOrchestrator.identifyEngagementPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:985:25)\n    at SystemOrchestrator.analyzeTherapeuticPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:923:34)\n    at SystemOrchestrator.processTherapeuticData (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:755:35)\n    at testIntegrationFlow (file:///C:/Projetos/protalbetinav3/test-final-integration.js:105:46)"
  }
}
{
  "timestamp": "2025-06-24T22:54:42.328Z",
  "level": "ERROR",
  "message": "Erro ao analisar padrões terapêuticos:",
  "meta": {},
  "error": {
    "name": "TypeError",
    "message": "Cannot read properties of undefined (reading 'timeEngaged')",
    "stack": "TypeError: Cannot read properties of undefined (reading 'timeEngaged')\n    at SystemOrchestrator.identifyEngagementPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:985:25)\n    at SystemOrchestrator.analyzeTherapeuticPatterns (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:923:34)\n    at SystemOrchestrator.processTherapeuticData (file:///C:/Projetos/protalbetinav3/src/api/services/core/SystemOrchestrator.js:755:35)\n    at testIntegrationFlow (file:///C:/Projetos/protalbetinav3/test-final-integration.js:105:46)"
  }
}
{"timestamp":"2025-07-07 23:05:28.480","level":"error","message":"Test error message","service":"betina-v3","version":"3.0.0","environment":"development","context":{"component":"test","requestId":"req_1751940328479_8412udurhgp","userId":"anonymous"},"data":{"stack":"test stack"}}
{"timestamp":"2025-07-08 17:02:01.853","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":30.8103628,"memory":{"rss":62177280,"heapTotal":26480640,"heapUsed":23384736,"external":2407720,"arrayBuffers":33435},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":25712,"timestamp":1752004921848,"memoryUsage":{"total":17098924032,"free":**********,"used":13448519680,"percentage":78.65126282116697}}}}
{"timestamp":"2025-07-08 17:04:33.340","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":30.7564095,"memory":{"rss":62730240,"heapTotal":25956352,"heapUsed":23405168,"external":2407720,"arrayBuffers":33435},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":19012,"timestamp":1752005073337,"memoryUsage":{"total":17098924032,"free":**********,"used":13834158080,"percentage":80.90660005337112}}}}
{"timestamp":"2025-07-08 17:05:17.096","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":30.9095314,"memory":{"rss":77283328,"heapTotal":57151488,"heapUsed":29876712,"external":2603081,"arrayBuffers":228796},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":1888,"timestamp":1752005117093,"memoryUsage":{"total":17098924032,"free":**********,"used":14490464256,"percentage":84.74488937948162}}}}
{"timestamp":"2025-07-08 17:06:11.698","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":30.9043363,"memory":{"rss":62492672,"heapTotal":25956352,"heapUsed":23415576,"external":2407720,"arrayBuffers":33435},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":15460,"timestamp":1752005171691,"memoryUsage":{"total":17098924032,"free":**********,"used":15513927680,"percentage":90.73043222466082}}}}
{"timestamp":"2025-07-08 17:06:40.084","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":60.90797,"memory":{"rss":62734336,"heapTotal":25956352,"heapUsed":23662160,"external":2408000,"arrayBuffers":33675},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":15460,"timestamp":1752005200079,"memoryUsage":{"total":17098924032,"free":**********,"used":15660392448,"percentage":91.58700523314893}}}}
{"timestamp":"2025-07-08 17:07:10.095","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":90.9161086,"memory":{"rss":62910464,"heapTotal":25956352,"heapUsed":23848680,"external":2416392,"arrayBuffers":42067},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":15460,"timestamp":1752005230088,"memoryUsage":{"total":17098924032,"free":**********,"used":15712202752,"percentage":91.89000853267257}}}}
{"timestamp":"2025-07-08 17:07:40.098","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":120.9203167,"memory":{"rss":63098880,"heapTotal":25956352,"heapUsed":24217784,"external":2432976,"arrayBuffers":58651},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":15460,"timestamp":1752005260093,"memoryUsage":{"total":17098924032,"free":**********,"used":14912675840,"percentage":87.21411833920942}}}}
{"timestamp":"2025-07-08 17:08:10.112","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":150.9355764,"memory":{"rss":63557632,"heapTotal":27004928,"heapUsed":23632856,"external":2441368,"arrayBuffers":50059},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":15460,"timestamp":1752005290108,"memoryUsage":{"total":17098924032,"free":**********,"used":15267700736,"percentage":89.29041796680929}}}}
{"timestamp":"2025-07-08 17:08:40.119","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":180.9426205,"memory":{"rss":63893504,"heapTotal":27004928,"heapUsed":23972912,"external":2440968,"arrayBuffers":66643},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":15460,"timestamp":1752005320114,"memoryUsage":{"total":17098924032,"free":**********,"used":16051576832,"percentage":93.87477716118319}}}}
{"timestamp":"2025-07-08 17:09:50.307","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":31.3567183,"memory":{"rss":61665280,"heapTotal":25956352,"heapUsed":23383464,"external":2407720,"arrayBuffers":33435},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":4588,"timestamp":1752005390301,"memoryUsage":{"total":17098924032,"free":**********,"used":15437418496,"percentage":90.28298194149203}}}}
{"timestamp":"2025-07-08 17:10:20.310","level":"error","message":"System Health: overall - unhealthy","service":"betina-v3","version":"3.0.0","environment":"development","context":{"type":"system_health","component":"overall","status":"unhealthy"},"data":{"metrics":{"uptime":61.3614329,"memory":{"rss":62054400,"heapTotal":25956352,"heapUsed":23632008,"external":2408000,"arrayBuffers":33675},"cpu":{"percentage":0,"user":0,"system":0,"total":0},"loadAverage":[0,0,0],"platform":"win32","arch":"x64","nodeVersion":"v22.14.0","pid":4588,"timestamp":1752005420307,"memoryUsage":{"total":17098924032,"free":**********,"used":14720450560,"percentage":86.08992315803745}}}}
